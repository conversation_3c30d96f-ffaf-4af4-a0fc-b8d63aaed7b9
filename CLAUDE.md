# CLAUDE.md

此文件为Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

这是一个基于Java Swing的云南省电网图形应用程序，用于电网管理系统。它是一个桌面应用程序，为管理电网操作、操作票生成以及云南省内不同区域电网的各种实用功能提供图形界面。

## 构建和开发命令

### Maven构建命令
```bash
# 编译项目
mvn compile

# 打包应用程序（创建包含依赖的JAR）
mvn package

# 清理构建产物
mvn clean

# 安装到本地仓库
mvn install

# 跳过测试进行构建（如需要）
mvn package -DskipTests
```

### 运行应用程序
```bash
# 运行主应用程序（打包后）
java -jar target/czp.jar

# 带特定参数运行
java -jar target/czp.jar [参数]

# 从IDE运行：主类是 com.tellhow.czp.app.yndd.Main
```

## 项目架构

### 高层架构
- **桌面应用程序**：基于Java Swing的GUI应用程序
- **多区域支持**：处理不同的电网区域（版纳BS、楚雄CX、德宏DH、大理DL、迪庆DQ、红河HH、昆明KM、临沧LC、丽江LJ、怒江NJ、普洱PE、曲靖QJ、文山WS、西双版纳XSBN、玉溪YX、昭通ZT）
- **Spring框架**：使用Spring 3.1.1进行依赖注入和配置
- **数据库层**：Oracle数据库配合Hibernate 3 ORM
- **SVG图形**：处理基于SVG的电网图表和图形

### 核心包结构
```
com.tellhow.czp.app.yndd/
├── Main.java                    # 应用程序入口点
├── dao/                        # 数据访问对象
├── impl/                       # 服务实现（CZP、EMS、OMS）
├── rule/                       # 不同区域的业务规则引擎
├── tool/                       # 工具类和通用函数
├── view/                       # GUI组件和对话框
└── wordcard/                   # 操作票生成功能
```

### 区域实现模式
应用程序遵循区域模式，每个电网区域都有自己的：
- `CZPImpl[区域].java` - 核心实现
- `EMSImpl[区域].java` - 能量管理系统
- `rule/[区域]/` - 区域特定的业务规则
- `wordcard/[区域]/` - 区域特定的操作票模板

### 关键技术
- **Java 6**（Maven编译目标）
- **Spring 3.1.1** - IoC容器和配置
- **Hibernate 3** - 数据库操作的ORM
- **Apache Commons** - 各种工具库
- **Swing** - 桌面GUI框架
- **SVG/Batik** - 矢量图形处理
- **Oracle JDBC** - 数据库连接

### 配置文件
- `tbp_config/springDataSource.xml` - 带加密属性的数据库配置
- `config/GUIBuilder.xml` - GUI布局配置
- `src/main/resources/db.properties` - 数据库连接属性（加密）
- `src/main/resources/log4j.properties` - 日志配置

### 数据库配置
应用程序使用：
- **Oracle数据库**作为主数据库
- 通过Apache Commons DBCP进行**连接池**
- 通过自定义EncryptablePropertyPlaceholderConfigurer进行**加密数据库凭据**
- **多数据源路由**能力

### 构建配置说明
- **最终JAR名称**：`czp.jar`
- **依赖位置**：`czp_lib/`目录
- **主类**：`com.tellhow.czp.app.yndd.Main`
- **Java版本**：1.6（遗留要求）

### 开发模式
1. **区域抽象**：每个电网区域有独立的实现
2. **基于规则的处理**：业务逻辑组织为可执行规则
3. **基于模板的操作票**：使用区域模板生成文档
4. **GUI构建器模式**：XML驱动的GUI构建
5. **Spring配置**：基于XML的bean配置和属性加密

### 主要依赖
- Spring框架（AOP、Beans、Context、JDBC、ORM、Web）
- Hibernate 3与自定义方言支持
- Apache POI用于Office文档处理
- 各种数据库驱动（Oracle、达梦、人大金仓、SQLite）
- 图形库（JFreeChart、Batik SVG）

## 测试
没有配置明确的测试框架。测试似乎通过以下方式进行：
- 通过GUI进行手动测试
- 区域特定的测试实现（`GetCheckImplTest[区域].java`）

## 区域定制
在处理区域功能时，请注意每个区域（BS、CX、DH等）可能有：
- `rule/[区域]/`中的不同业务规则
- `impl/`中的自定义实现
- `wordcard/[区域]/`中的特定操作票模板
- `src/main/resources/AppConfig[区域].properties`中的区域配置文件