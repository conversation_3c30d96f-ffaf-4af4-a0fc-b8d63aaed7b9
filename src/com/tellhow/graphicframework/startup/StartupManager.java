package com.tellhow.graphicframework.startup;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;

import org.apache.log4j.Logger;

public final class StartupManager {
	private static Logger log = Logger.getLogger(StartupManager.class);
	private static List<StartupLoader> startupLoaders = new ArrayList<StartupLoader>();
	
	static {
		try {
			InputStream inputStream = StartupManager.class.getResourceAsStream("/startupLoader.properties");
			if (inputStream != null) {
				LinkedProperties properties = new LinkedProperties();
				properties.load(inputStream);
				
				 Enumeration enumeration = properties.keys(); 

				 for (int i = 0; i < properties.getKeys().size(); i++) {
					String key = (String)properties.getKeys().get(i);
					String className = properties.getProperty(key);

					Class clazz = Class.forName(className);
					registerStartupLoader(clazz);
					log.info("Register StartupLoader in ordered for class: " + className);
				}
			}
		} catch (Exception ex) {
			log.error("Load startup loader classes fail.", ex);
		}
	}
	
	public static void startup() {
		for (Iterator iterator = startupLoaders.iterator(); iterator.hasNext();) {
			//long start=System.currentTimeMillis();
			StartupLoader loader = (StartupLoader) iterator.next();
			//System.out.println(loader.getClass());
			loader.load();
			//long end=System.currentTimeMillis();
	    	//System.out.println(loader+"���ĵ�ʱ����:"+(end-start)+"ms");
		}
	}
	
	private static void registerStartupLoader(Class clazz) {
		StartupLoader loader;
		try {
			loader = (StartupLoader)clazz.newInstance();
			startupLoaders.add(loader);
		} catch (InstantiationException e) {
			log.error(e.getMessage(), e);
		} catch (IllegalAccessException e) {
			log.error(e.getMessage(), e);
		}
	}
}

class LinkedProperties extends Properties { 
	 
    private final List<Object> keys = new ArrayList<Object>(); 
 
    public List getKeys() {
    	return keys;
    }
 
    @Override
	public Object put(Object key, Object value) { 
        keys.add(key); 
        return super.put(key, value); 
    } 
} 
