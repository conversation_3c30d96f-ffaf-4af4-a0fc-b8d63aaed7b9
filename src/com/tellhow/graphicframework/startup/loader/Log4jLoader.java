package com.tellhow.graphicframework.startup.loader;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import org.apache.log4j.PropertyConfigurator;

import com.tellhow.graphicframework.startup.StartupLoader;

public class Log4jLoader implements StartupLoader {

	private static final String LOG4J_PROPERTIES = "/log4j.properties";

	public void load() {
		try {
    		InputStream inputStream = Log4jLoader.class.getResourceAsStream(LOG4J_PROPERTIES);
    		Properties properties = new Properties();
			properties.load(inputStream);
			PropertyConfigurator.configure(properties);
		} catch (IOException e1) {
			e1.printStackTrace();
		}
	}

}
