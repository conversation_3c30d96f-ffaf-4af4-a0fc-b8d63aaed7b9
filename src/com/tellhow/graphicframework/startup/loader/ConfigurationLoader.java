/**
 * ��Ȩ���� : ̩������ɷ����޹�˾��Ȩ����
 * �� Ŀ �� ������Ʊר��ϵͳ
 * ����˵�� : ���漰ϵͳ���е������ļ����н���
 * ��    �� : ����ƽ
 * �������� : 2008-07-16
 * �޸����� ��
 * �޸�˵�� ��
 * �� �� �� ��
 **/
package com.tellhow.graphicframework.startup.loader;

import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.basic.SVGLayer;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupLoader;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.DOMUtil;

public class ConfigurationLoader implements StartupLoader{
	private static final String SYS_CONFIG_XML_FILE = "config/SYSTEMPARAMSBUILD.xml";
	private static Logger log = Logger.getLogger(ConfigurationLoader.class);
	
	public void load() {
		loadConfiguration(false, "");
	}
	
	/**
	 * ���¼���ָ�����Ƶ�������
	 **/
	public void reload(String paramName)
	{
		loadConfiguration(true, paramName);
	}
	
	public void loadConfiguration(boolean isReload, String paramName) {
		Document doc = null;
        try {

			try {
				doc = DOMUtil.readXMLFile(SYS_CONFIG_XML_FILE);
				
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				return;
			}

            Element rootE = doc.getDocumentElement();

            NodeList childEs = rootE.getChildNodes();
            Element childE = null;
            Element values = null;
            for (int i = 0; i < childEs.getLength(); i++) {
            	if(childEs.item(i).getNodeName().equals("#text"))
            		continue;
                childE = (Element) childEs.item(i);
                if(isReload)
                {
	                if(!childE.getAttribute("name").equals(paramName))
	                	continue;
                }
                
                if (childE.getAttribute("name").equals("menuProviderPackage")) { //��ȡ�˵�����Java Package.
                    values = (Element)childE.getElementsByTagName("value").item(0);
                    MenuRegistryLoader.setPackageName(values.getTextContent().trim());
                }
                else if (childE.getAttribute("name").equals("svgDocumentResolver")) { //��ȡsvg������
                    values = (Element)childE.getElementsByTagName("value").item(0);
                    String className = values.getTextContent().trim();
					try {
						Class clazz = Class.forName(className);
						SVGDocumentResolver.setResolver(clazz);
					} catch (ClassNotFoundException e) {
						log.error(className + " not found.");
					}
                }
                else if (childE.getAttribute("name").toUpperCase().equals("DEVICECOLOR")) {//��ȡ�豸��Ź淶
                   
                    Element codeElem = null;
                    String codeKey = "";
                    String codeName = "";
                    NodeList allVlaues = childE.getElementsByTagName("value");
                    for (int j = 0; j < allVlaues.getLength(); j++) {
                        codeElem = (Element) allVlaues.item(j);
                        codeName = codeElem.getAttribute("code").trim();
                        codeKey = codeElem.getAttribute("key").trim();
                        SystemConstants.putMapColor(codeKey, codeName);
                    }
                }
                else if (childE.getAttribute("name").toUpperCase().equals("SVGLAYER")) {

                    Element codeElem = null;
                    String layerID = "";
                	String layerName = "";
                	boolean isVisiable = true;
                	boolean isHandleEvent = true;
                	boolean isRefresh = false;
                	int refreshInterval = 0;
                    NodeList allVlaues = childE.getElementsByTagName("value");
                    for (int j = 0; j < allVlaues.getLength(); j++) {
                        codeElem = (Element) allVlaues.item(j);
                        layerID = codeElem.getAttribute("layerID").trim();
                        layerName = codeElem.getAttribute("layerName").trim();
                        if(codeElem.getAttribute("isVisiable").trim().equals(""))
                        	isVisiable = true;
                        else
                        	isVisiable = Boolean.valueOf(codeElem.getAttribute("isVisiable").trim());
                        if(codeElem.getAttribute("isHandleEvent").trim().equals(""))
                        	isHandleEvent = true;
                        else
                        	isHandleEvent = Boolean.valueOf(codeElem.getAttribute("isHandleEvent").trim());
                        if(codeElem.getAttribute("isRefresh").trim().equals(""))
                        	isRefresh = false;
                        else
                        	isRefresh = Boolean.valueOf(codeElem.getAttribute("isRefresh").trim());
                        if(codeElem.getAttribute("refreshInterval").trim().equals(""))
                        	refreshInterval = 0;
                        else
                        	refreshInterval = Integer.valueOf(codeElem.getAttribute("refreshInterval").trim());
                        SVGLayer layer = new SVGLayer(layerID,layerName,isVisiable,isHandleEvent,isRefresh,refreshInterval);
                        SystemConstants.putMapSVGLayer(layer);
                    }
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(ConfigurationLoader.class.getName()).log(Level.FATAL, null, ex);
        }
	}
}
