package com.tellhow.graphicframework.startup.loader;

import java.util.Iterator;
import java.util.Set;
import java.util.StringTokenizer;

import org.apache.log4j.Logger;

import com.tellhow.graphicframework.menu.MenuRegistry;
import com.tellhow.graphicframework.menu.SvgCanvasMenuProvider;
import com.tellhow.graphicframework.menu.annotation.MenuProvider;
import com.tellhow.graphicframework.startup.StartupLoader;
import com.tellhow.graphicframework.utils.ResolverUtil;
import com.tellhow.graphicframework.utils.ResolverUtil.IsA;

public class MenuRegistryLoader implements StartupLoader {
	private static Logger log = Logger.getLogger(MenuRegistryLoader.class);
	private static final String PACKAGE_NAME = "com.tellhow.graphicframework.menu.provider";
	private static String packageName;
	public void load() {
		ResolverUtil util = new ResolverUtil();
		
		if (packageName == null) {
			util.findInPackage(new IsA(SvgCanvasMenuProvider.class), PACKAGE_NAME);
		}
		else {
			StringTokenizer tokenizer = new StringTokenizer(packageName, ",");
			while (tokenizer.hasMoreTokens()) {
				String name = tokenizer.nextToken();
				if (name != null) {
					util.findInPackage(new IsA(SvgCanvasMenuProvider.class), name);
				}
			}
		}
		Set set = util.getClasses();
		for (Iterator iterator = set.iterator(); iterator.hasNext();) {
			Class clazz = (Class) iterator.next();
			MenuProvider provider = (MenuProvider)clazz.getAnnotation(MenuProvider.class);
			if (provider != null) {
				MenuRegistry.registerMenuProvider(provider.id(), clazz);
				log.info("Register menu provider for class:" + clazz.getName());
			}
		}
	}
	
	public static void setPackageName(String name) {
		packageName = name;
	}
}
