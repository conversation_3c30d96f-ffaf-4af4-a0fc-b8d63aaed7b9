/**
* ��Ȩ���� : ̩������ɷ����޹�˾��Ȩ����
* �� Ŀ �� ������Ʊר��ϵͳ
* ����˵�� : �����豸����
* ��    �� : �ſ�
* �������� : 2008-05-22
* �޸����� ��
* �޸�˵�� ��
* �� �� �� ��
**/
package com.tellhow.graphicframework.model;
import java.util.HashMap;
import java.util.Map;
/**
 *
 * <AUTHOR>
 */
public class PowerDevice {
	protected String powerDeviceID = "";      //�����豸ID
	private String powerDeviceDefaultSta = "";//�����豸�ĳ�̬ //�����޸�
	private String powerDeviceCode = "";     //�����豸����
    private String powerDeviceName = "";    //�����豸����
    private String powerStationID = "";     //�������վID
    private String powerStationCode = "";     //�������վ����
    private String powerStationName = "";     //�������վ����
    private String cimID = "";
    private String deviceStatus = "";       //�豸��ǰ״̬��־
    private String deviceType = "";         //�豸����
    private double powerVoltGrade = 220;      //�豸��ѹ�ȼ�,��λkV
    private String deviceRunType = "";         //�豸�������,��ĸ�ߵ�բ��ĸ�����ء���·���ص�
    private String deviceSetType = "";         //�豸���а�װ���,��ĸ�ߵ�բ��ĸ������·���ص�
    private String switchnum = "";
    private String isLoseElec = "";   //�Ƿ�ʧ��   0���� 1��ʧ��  ��Ҫ��������������Ĭ��Ϊ��
//    private String dispatch="";			//���״̬
//    private String monitoring="";		//ʵʱ״̬
    
    public PowerDevice() {
    }
    
    public String getPowerDeviceCode() {
		return powerDeviceCode;
	}

	public void setPowerDeviceCode(String powerDeviceCode) {
		this.powerDeviceCode = powerDeviceCode;
	}
    
    public PowerDevice(String devType)
    {
        this.deviceType = devType;
    	
    }
    
    public void setPowerDeviceID(String powerDeviceID)
    {
        this.powerDeviceID = powerDeviceID;
    }
    
    public String getPowerDeviceID()
    {
        return this.powerDeviceID;
    }
    public void setPowerDeviceDefaultSta(String powerDeviceDefaultSta) //�����޸�
    {
        this.powerDeviceDefaultSta = powerDeviceDefaultSta;
    }
    
    public String getPowerDeviceDefaultSta() //�����޸�
    {
        return this.powerDeviceDefaultSta;
    }
    public void setPowerDeviceName(String powerDeviceName)
    {
        this.powerDeviceName = powerDeviceName;
    }
    
    public String getPowerDeviceName()
    {
        return this.powerDeviceName;
    }
    
    public void setDeviceStatus(String deviceStatus)
    {
//    	if(powerDeviceID.equals("114560315521245513")){
//    		System.out.println(1);
//    	}
    	this.deviceStatus = deviceStatus;
    }
    
    public String getDeviceStatus()
    {
        return this.deviceStatus;
    }
    
    public void setDeviceType(String deviceType)
    {
        this.deviceType = deviceType;
    }
    
    public String getDeviceType()
    {
        return this.deviceType;
    }
    public void setPowerStationID(String powerStationID)
    {
        this.powerStationID = powerStationID;
    }
    
    public String getPowerStationID()
    {
        return this.powerStationID;
    }
    
    public void setPowerStationCode(String powerStationCode)
    {
    	this.powerStationCode = powerStationCode;
    }
    
    public String getPowerStationCode()
    {
    	return this.powerStationCode;
    }
    
    public void setPowerStationName(String powerStationName)
    {
    	this.powerStationName = powerStationName;
    }
    
    public String getPowerStationName()
    {
    	return this.powerStationName;
    }
    
    public int getDevicePorts() 
    {
        return 2;
    }
    public void setPowerVoltGrade(double powerVoltGrade)
    {
        this.powerVoltGrade=powerVoltGrade;
    }
    
    public double getPowerVoltGrade()
    {
        return this.powerVoltGrade;
    }
    
    public void setDeviceRunType(String deviceRunType)
    {
    	this.deviceRunType = deviceRunType;
    }
    
    public String getDeviceRunType()
    {
    	return this.deviceRunType;
    }
    public void setDeviceSetType(String deviceSetType)
    {
    	this.deviceSetType = deviceSetType;
    }
    
    public String getDeviceSetType()
    {
    	return this.deviceSetType;
    }
    
   public String getCimID() {
		return cimID;
	}

	public void setCimID(String cimID) {
		this.cimID = cimID;
	}

/**
    * ��  ;:���µ����豸״̬
    * @����1(String)�����վ����
    * @����2(String): �豸����
    * @����3(String)���豸״̬����
    * @����ֵ����
    */
    public void commitPowerDeviceStatusTrans()
    {
//        PowerSystemDBOperator.updatePowerDeviceStatus(this.getPowerStationID(),this.getPowerDeviceID(),this.getDeviceStatus());
    }
    
    
    /**
    * ��  ;:�����豸����ִ�нӿ�
    * @����1(Map)���������
    * @����2(Map): �������
    * @����ֵ����
    */
    public void execute(Map inMapPara,Map outMapPara)
    {}

    @Override
    public String toString() {
        return this.powerDeviceName;
    }

	public void setSwitchnum(String switchnum) {
		this.switchnum = switchnum;
	}

	public String getSwitchnum() {
		return switchnum;
	}

	public String getIsLoseElec() {
		return (isLoseElec == null)?"":isLoseElec;
	}

	public void setIsLoseElec(String isLoseElec) {
		this.isLoseElec = isLoseElec;
	}
}
