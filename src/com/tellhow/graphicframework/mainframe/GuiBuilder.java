package com.tellhow.graphicframework.mainframe;

import java.awt.Component;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.event.MouseEvent;
import java.io.File;
import java.lang.reflect.Constructor;
import java.net.URL;
import java.util.Locale;

import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.PopupFactory;
import javax.swing.ToolTipManager;

import org.apache.batik.bridge.UpdateManager;
import org.apache.log4j.Logger;
import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.GUIUtils;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.InternationalizationManager;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.Menu;
import org.w3c.dom.Document;

import com.tellhow.graphicframework.constants.NonRectanglePopupFactory;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

public abstract class GuiBuilder extends Controller {
	private static Logger log = Logger.getLogger(GuiBuilder.class);
	
	protected Frame frame = null;
	public GuiBuilder() {
	}
	public GuiBuilder(URL url) throws GUIException {
		PopupFactory.setSharedInstance(new  NonRectanglePopupFactory());
		ToolTipManager.sharedInstance().setInitialDelay(0);
		frame = constructFrame(url, "MainFrame");
		/*
		//˫��
		if(SystemConstants.isInitDoubleScreen.equals("1")){
			GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
	        GraphicsDevice[] gs = ge.getScreenDevices();
	        for(GraphicsDevice curGs : gs)
	        {
	            GraphicsConfiguration[] gc = curGs.getConfigurations();
	            for(GraphicsConfiguration curGc : gc)
	            {
	                Rectangle bounds = curGc.getBounds();
	                ColorModel cm = curGc.getColorModel();
	                
	                System.out.println("" + bounds.getX() + "," + bounds.getY() + " " + bounds.getWidth() + "x" + bounds.getHeight() + " " + cm);
	            }
	        }
	        int x=0;
	        int width=0;
	        int height=0;
	        if(gs.length>1){
		        x=(int)gs[1].getDefaultConfiguration().getBounds().getX();
		        width=(int)gs[1].getDefaultConfiguration().getBounds().getWidth();
		        height=(int)gs[1].getDefaultConfiguration().getBounds().getHeight()-100;
	        }else{
	        	x=(int)gs[0].getDefaultConfiguration().getBounds().getX();
	 	        width=(int)gs[0].getDefaultConfiguration().getBounds().getWidth();
	 	        height=(int)gs[0].getDefaultConfiguration().getBounds().getHeight()*8/9;
	        }
	        frame.setProperty("size", new Dimension(x+width,height));
			frame.setProperty("location", new Point(0, 0));
		}
		//
		*/
		Widget widget = frame.getWidget("menuBar");
//		JMenuBar menuBar = (JMenuBar)widget.getRealWidget();
		//menuBar.setBackground(Color.red);
		initMenuState(widget);
//		frame.show();
	}
	
	protected void initMenuState(Widget parentWidget) {
		for(int i = 0; i < parentWidget.getChildCount(); i++) {
			Widget widget = parentWidget.getChild(i);
			
			if (widget instanceof Menu) {
				initMenuState(widget);
			}
			String resourceId = widget.getName();
			if (resourceId == null)
				continue;
			widget.getWidget().setVisible(true);
		}
	}
	
	@Override
	public abstract void eventOccured(GUIEvent event);
	
	public JFrame getJFrame() {
		return (JFrame)frame.getWidget();
	}
	
	
	public Component getComponent(String name) {
		Widget widget = frame.getWidget(name);
		return widget.getRealWidget();
	}
	public Widget getWidget(String name) {
		Widget widget = frame.getWidget(name);
		return widget;
	}
	public SVGCanvasPanel getActivateSVGPanel() {
		return (SVGCanvasPanel)getSVGJTabbedPane().getSelectedComponent();
	}
	
	public UpdateManager getUpdateManagerByDoc(Document doc) {
		JTabbedPane tabbedPane = getSVGJTabbedPane();
		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
			String panelFilePath = panel.getFilePath();
			String docURI = doc.getDocumentURI();
			File file1 = new File(panelFilePath);
			if (file1.toURI().toString().equals(docURI)) {
				return panel.getSvgCanvas().getUpdateManager();
			}
		}
		return null;
	}

	public boolean activateTabbedPageByName(String filePath) {
		JTabbedPane tabbedPane = getSVGJTabbedPane();
		if(tabbedPane!=null){
			for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
				SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
				if (panel.getFilePath().equals(filePath)) {
					tabbedPane.setSelectedComponent(panel);
					return true;
				}
			}
		}
		return false;
	}
	
	public boolean isTabbedPageExist(String filePath) {
		JTabbedPane tabbedPane = getSVGJTabbedPane();
		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
			if (panel.getFilePath().equals(filePath)) {
				return true;
			}
		}
		return false;
	}
	
    public void closeTabbedPage(MouseEvent e) {
        if (getSVGJTabbedPane().getTabCount() == 0) {
            return;
        }
        SVGCanvasPanel selpanel = (SVGCanvasPanel) getSVGJTabbedPane().getSelectedComponent();
        if (e.getButton() == 1 && e.getClickCount() == 2) {
        	String tabName = selpanel.getName();
        	if(SystemConstants.getMapSVGFile().get(selpanel.getFilePath())!=null &&
        			SystemConstants.getMapSVGFile().get(selpanel.getFilePath()).isAlwaysOpen()) {
	   			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "["+tabName+"]���ܹرգ�","��ʾ", JOptionPane.WARNING_MESSAGE);
    			return;
    		}
            String stationID = selpanel.getStationID();
            getSVGJTabbedPane().remove(getSVGJTabbedPane().getSelectedIndex());
            if(!stationID.equals("")) {
            	boolean isNeedRemoveData = true;
            	JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
        		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
        			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
        			if(panel.getStationID().equals(stationID))
        				isNeedRemoveData = false; //��վ���д򿪵�ͼ�β�����ڴ��豸����
        		}
        		if(isNeedRemoveData)
        			SystemConstants.removeLineOrStation(stationID, selpanel.getSvgDocument());
            }
        }
    }
    
  //SVG�Ŵ�
    public void SVGtoBig(GUIEvent e) {
  		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
  		if(splitPane.getDividerLocation()==1)
  			return;
  		
  		SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getSelectedComponent();
  		if(otsp == null)
  			return;
  		SVGCanvas fSvgCanvas=otsp.getSvgCanvas();
  		fSvgCanvas.zoomCanvas(1, 0.2);
  		otsp.setFlush();
  	}
  	//SVG��С
    public void SVGtoSmall(GUIEvent e) {
  		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
  		if(splitPane.getDividerLocation()==1)
  			return;
  		
  		SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getSelectedComponent();
  		if(otsp == null)
  			return;
  		SVGCanvas fSvgCanvas=otsp.getSvgCanvas();
  		fSvgCanvas.zoomCanvas(-1, 0.2);
  		otsp.setFlush();
  	}
  //SVG������ʾ
    public void SVGtoNetural(GUIEvent e) {
  		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
  		if(splitPane.getDividerLocation()==1)
  			return;
  		
  		SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getSelectedComponent();
  		if(otsp == null)
  			return;
  		SVGCanvas fSvgCanvas=otsp.getSvgCanvas();
  		fSvgCanvas.zoomCanvas(0, 0.2);
  		otsp.setFlush();
  	}
	
	public abstract JTabbedPane getSVGJTabbedPane();
	
	public abstract JPopupMenu getSVGCloseMenu();
	
	public static GuiBuilder createGuiBuilder(Class clazz, URL url) {
		
		try {
			Locale locale = Locale.US;
			
			if (Locale.getDefault().getLanguage().equals("de"))
				locale = new Locale("de", "DE");
			GUIUtils.defaultInitialization(locale);
			ImageIconFactory.addSearchPath("tellhow/icons");
			ImageIconFactory.addSearchPath("resources/test/icons");
			ImageIconFactory.addSearchPath("resources/builder/icons");
			InternationalizationManager.addLanguageFile("resources/test/test");
			InternationalizationManager.addLanguageFile("resources/builder/builder");
			
			Constructor constructor = clazz.getConstructor(URL.class);
			GuiBuilder builder = (GuiBuilder)constructor.newInstance(url);
			SystemConstants.setGuiBuilder(builder);
//			GraphicsEnvironment  ge=GraphicsEnvironment.getLocalGraphicsEnvironment();
//			GraphicsDevice gd = ge.getDefaultScreenDevice();
			JFrame jFrame = builder.getJFrame();
			//ȫ��
//            gd.setFullScreenWindow(jFrame);  
			jFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
			SystemConstants.setMainFrame(jFrame);
			return builder;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			new MessageDialog(e);
		}
		return null;
	}
}
