/**
 * ��Ȩ���� : ̩������ɷ����޹�˾��Ȩ����
 * �� Ŀ �� ������Ʊר��ϵͳ
 * ����˵�� : ����������,�����й������������ʧ
 * ��    �� : ����
 * �������� : 2008-10-03
 * �޸����� ��
 * �޸�˵�� �� 
 * �� �� �� ��
 **/
package com.tellhow.graphicframework.mainframe;

import java.awt.Font;

import javax.swing.JFrame;
import javax.swing.JProgressBar;
import javax.swing.JWindow;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.WindowUtils;

public final class WindowSplash{
	private String showMessage = "";
	private static JWindow window = null;
	private static JProgressBar progressBar;
	private JFrame frame;
	private static WindowSplash windowSplash;
	private static boolean isAlive = false;
	
	public WindowSplash(String showMessage,JFrame fr) {
		this.showMessage = showMessage;
		this.frame = fr;
		prepareSplash();
	}

	private void prepareSplash(){
		if(window == null) {
			window = new JWindow(frame);
			progressBar = new JProgressBar();//��ʼ��һ��������
			progressBar.setIndeterminate(true);//���øý�����Ϊ��ȷ��ģʽ
			progressBar.setBorderPainted(true);//�豸���������߿�
			progressBar.setString(showMessage);//��ʾ�ַ���
			progressBar.setStringPainted(true);//��ʾ�ַ���
			progressBar.setFont(new java.awt.Font("΢���ź�", Font.PLAIN, 15));
			window.add(progressBar,"Center");
			
			window.setSize(300, 40);
		}
		else {
			progressBar.setString(showMessage);//��ʾ�ַ���
		}
		
	}
	
	/**
	 * ����startSplash()����ʾ��������
	 *
	 */
	private boolean startSplash(){
		JFrame frame = SystemConstants.getMainFrame();
		if (frame != null && !window.isVisible()) {
			WindowUtils.centerWindow(frame, window);
			window.setVisible(true);
			//����ʱչʾ����ǰ��
			window.setAlwaysOnTop(true);
			window.toFront();
			return true;
		}
		return false;
	}
	
	/**
	 * ����stopSplash()��ֹͣ��ʾ��������
	 *
	 */
	private void stopSplash(){ 
		window.setVisible(false);
		window.dispose();
	}

	public static void showSplashWindow(final String message) {
		if(isAlive)
			return;
		stopSplashWindow();
		Thread thread = new Thread() {
			@Override
			public void run() {
				isAlive = true;
				while(true) {
					if (SystemConstants.getMainFrame() != null) {
						windowSplash = new WindowSplash(message, SystemConstants.getMainFrame());
					}
					
					if (windowSplash != null && windowSplash.startSplash())
							break;
					try {
						Thread.sleep(100);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
				isAlive = false;
			};
		};
		
		thread.start();
	}
	
	public static void stopSplashWindow() {
		if(windowSplash != null)
			windowSplash.stopSplash();
	}
}
