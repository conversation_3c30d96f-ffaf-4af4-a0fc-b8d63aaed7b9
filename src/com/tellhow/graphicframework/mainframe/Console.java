

package com.tellhow.graphicframework.mainframe;

import java.awt.BorderLayout;
import java.awt.Cursor;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;

/**
 *
 * <AUTHOR>
 */
public class Console extends JPanel{
    private static JTextArea output;
    private String title="�������";
    public Console(){
        
        JScrollPane sp_output = new JScrollPane();
        output = new JTextArea();
        output.setEditable(false);
        output.setCursor(new Cursor(Cursor.TEXT_CURSOR));
        output.setColumns(25);
        sp_output.setViewportView(output);
        this.setLayout(new BorderLayout());
        add(sp_output,BorderLayout.CENTER);
    }
    /**
     * ����ַ�������
     * @param message
     */
    public static void outPut(String message){
    	if (output != null) {
	        output.append(message);
	        output.setCaretPosition(output.getText().length());//�L�ӵ��׶�
    	} else {
    		System.out.println(output);
    	}
    	
    }
    /**
     * ����ַ�������
     * @param message
     */
    public static void outPutln(String message){
        output.append(message+"\r\n");
        output.setCaretPosition(output.getText().length());//�L�ӵ��׶�
    }
    /**
     * �����������
     * @param m
     */
    public static void outPut(int m){
        String message=((Integer)m).toString();
        output.append(message);
        output.setCaretPosition(output.getText().length());//�L�ӵ��׶�
    }
    /**
     * �����������
     * @param b
     */
    public static void outPut(boolean b){
        if(b==true){
            output.append("��ȷ");
            output.setCaretPosition(output.getText().length());//�L�ӵ��׶�
        }
        else{
            output.append("����");
            output.setCaretPosition(output.getText().length());//�L�ӵ��׶�
        }
    }
    /**
     * �����������
     * @param o
     */
    public static void outPut(Object o){
        if(o==null)
            output.append("��");
        else
            output.append(o.toString());
    }
    /**
     * �������̨���ڵ���Ϣ
     */
    public static void ClearMessage(){
        output.setText("");
    }
    /**
     * ��ȡ���ڱ���
     */
    public String getTitle(){
        return title;
    }
}
