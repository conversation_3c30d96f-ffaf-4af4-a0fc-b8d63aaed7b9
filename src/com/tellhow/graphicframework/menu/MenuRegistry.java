package com.tellhow.graphicframework.menu;

import java.lang.reflect.Constructor;
import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;

import com.tellhow.graphicframework.menu.provider.DefaultMenuProvider;
import com.tellhow.graphicframework.model.PowerDevice;


public abstract class MenuRegistry {
	private static Logger log = Logger.getLogger(MenuRegistry.class);
	private final static Map<String, Class> providerMap = new HashMap<String, Class>();
	
	//ע��˵��ṩ��
//	static {
//		registerMenuProvider("DeviceSwitch", 				DeviceSwitchMenuProvider.class); 
//		registerMenuProvider("DeviceInOutLine", 			DeviceInOutLineMenuProvider.class);
//		registerMenuProvider("DeviceLoadThreePnaseCenterPortTransformer", DeviceLoadThreePnaseCenterPortTransformerMenuProvider.class);
//		registerMenuProvider("DeviceSwitchFlowGroundLine", 	DeviceSwitchFlowGroundLineMenuProvider.class);
//		registerMenuProvider("DeviceSwitchSeparate", 		DeviceSwitchSeparateMenuProvider.class);
//		registerMenuProvider("DeviceMotherLine", 			DeviceMotherLineMenuProvider.class);
//		registerMenuProvider("DeviceTransLine", 			DeviceTransLineMenuProvider.class);
//	}
	
	public static SvgCanvasMenuProvider getMenuProvider(PowerDevice pd) {
		if (pd == null)
			return null;
		Class providerClazz = providerMap.get(pd.getDeviceType());
		if (providerClazz == null) {
			log.warn("Can not find MenuProvider for device type :" + pd.getDeviceType());
			return null;
		}
			
		try {
			Constructor constructor = providerClazz.getConstructor(PowerDevice.class);
			SvgCanvasMenuProvider menuProvider = (SvgCanvasMenuProvider)constructor.newInstance(pd);
			return menuProvider;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} 
		
		return new DefaultMenuProvider(pd);
	}
	
	public static void registerMenuProvider(String id, Class clazz) {
		if (id == null || clazz == null)
			return;
		providerMap.put(id, clazz);
	}
}
