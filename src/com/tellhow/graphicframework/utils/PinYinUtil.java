package com.tellhow.graphicframework.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

public class PinYinUtil {
	
	private static StringBuffer output;
	private static StringBuilder head;


	public static String getPinYin(String inputString) {
		
		HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
		format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
		format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
		format.setVCharType(HanyuPinyinVCharType.WITH_V);
		

		char[] input = inputString.trim().toCharArray();
		 output = new StringBuffer("");
		 head=new StringBuilder();

		try {
			for (int i = 0; i < input.length; i++) {
				if (Character.toString(input[i]).matches("[\u4E00-\u9FA5]+")) {
					String[] temp = PinyinHelper.toHanyuPinyinStringArray(input[i], format);
					output.append(temp[0]);
					output.append(" ");
					head.append(temp[0].charAt(0));
				} else
					output.append(Character.toString(input[i]));
			}
		} catch (BadHanyuPinyinOutputFormatCombination e) {
			e.printStackTrace();
		}
		return output.toString();
	}
	/**
	 * ֻ�����뺺�ֲ��ܻ��ƴ������ĸ
	 * */
	public static String getHead(String inputString) {
		getPinYin(inputString);
		return head.toString();
	}
	
	public static void main(String[] args) {
		String chs = "�����й��� I'm Chinese!";
		System.out.println(chs);
		System.out.println(getPinYin(chs));
	}
	
}