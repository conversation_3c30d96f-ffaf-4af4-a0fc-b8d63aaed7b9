package com.tellhow.graphicframework.utils;

import java.awt.geom.Point2D;
import java.util.ArrayList;
import java.util.List;

import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

public class UseGroundPoint {

	public static GroundPoint getPoint(Element element) {
		String xlinkNS = org.apache.batik.util.XMLConstants.XLINK_NAMESPACE_URI;
		String id=element.getAttributeNS(xlinkNS, "href").replace("#", "");
		Element symbol = DOMUtil.getSymbolByID(element, id);
		
		GroundPoint p=new GroundPoint();
		
		if(symbol != null){
			NodeList nodes = symbol.getChildNodes();
			Node node;
			Element ele;
			String[] array;
	 		ArrayList<Double> xPos=new ArrayList<Double>();
			ArrayList<Double> yPos=new ArrayList<Double>();
			for (int i = 0; i < nodes.getLength(); i++) {
				node = nodes.item(i);
				if(node instanceof Element){
					ele=(Element) node;
					if("line".equals(ele.getNodeName())){
						xPos.add(Double.parseDouble(ele.getAttribute("x1")));
						xPos.add(Double.parseDouble(ele.getAttribute("x2")));
						yPos.add(Double.parseDouble(ele.getAttribute("y1")));
						yPos.add(Double.parseDouble(ele.getAttribute("y2")));
					}
					if("polyline".equals(ele.getNodeName())){
						array=ele.getAttribute("points").split(" ");
						for (int j = 0; j < array.length; j++) {
							String points = array[j];
							xPos.add(Double.parseDouble(points.split(",")[0]));
						    yPos.add(Double.parseDouble(points.split(",")[1]));
						}
					}
				}
			}
			
			double w = DOMUtil.max(xPos)-DOMUtil.min(xPos);
			double h = DOMUtil.max(yPos)-DOMUtil.min(yPos);
			String flag="0";
			if(w<h){
				flag="1";
			}
			
			Point2D.Double d = new Point2D.Double(w/2, h/2);
			p.setPoint(d);
			p.setFlag(flag);
		}
		
		return p;
	}
	
	
	
}
