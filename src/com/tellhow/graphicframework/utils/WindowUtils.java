package com.tellhow.graphicframework.utils;

import java.awt.Component;
import java.awt.Dimension;
import java.awt.Toolkit;
import java.awt.Window;
import java.awt.geom.AffineTransform;
import java.awt.geom.Point2D;
import java.awt.geom.Rectangle2D;

import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.JTable;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableModel;

import org.apache.batik.swing.JSVGCanvas;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

public final class WindowUtils {

	public static void centerWindow(Window window, Component component) {
		int sx = 0;
		int sy = 0;
		int sw = 0;
		int sh = 0;
		if(window != null) {
			sx = window.getX();
			sy = window.getY();
			sw = window.getWidth();
			sh = window.getHeight();
		}
		else {
			Dimension screensize = Toolkit.getDefaultToolkit().getScreenSize();
			sx = 0;
			sy = 0;
			sw = (int)screensize.getWidth();
			sh = (int)screensize.getHeight();
		}
		int width = component.getWidth(), height = component.getHeight();
		component.setLocation(sw / 2 - (width / 2) + sx, sh
				/ 2 - (height / 2) + sy);
	}
	
	public static void showMessage(String viewValue)
	{
		JFrame mf = SystemConstants.getMainFrame();
		String title=SystemConstants.SYSTEM_TITLE;
		JOptionPane.showMessageDialog(mf, viewValue,title,javax.swing.JOptionPane.INFORMATION_MESSAGE);
	}
	public static void showMessage(Component parenFrame,String viewValue)
	{
		String title=SystemConstants.SYSTEM_TITLE;
		JOptionPane.showMessageDialog(parenFrame, viewValue,title,javax.swing.JOptionPane.INFORMATION_MESSAGE);
	}
	//������
	public static int addTableRow(JTable table) {
		int row;
		DefaultTableModel model = (DefaultTableModel)table.getModel();
		model = (DefaultTableModel) table.getModel();
		if (table.getSelectedRow() != -1) {
			row = table.getSelectedRow() + table.getSelectedRowCount();
			model.insertRow(row, new Object[] {});
		}
		else {
			row = table.getRowCount();
			model.addRow(new Object[] {});
		}
		return row;
	}
	//ɾ����
	public static String[] removeTableRow(JTable table) {
		DefaultTableModel model = (DefaultTableModel)table.getModel();
		int[] selectRows = table.getSelectedRows();
		String[] rowKeys = new String[selectRows.length];
		if (selectRows.length == 0) {
			showMessage("��ѡ����Ҫɾ���ļ�¼");
			return rowKeys;
		}
		table.removeEditor();
		model = (DefaultTableModel) table.getModel();
		
		for (int i = selectRows.length - 1; i >= 0; i--) {
			rowKeys[i] = model.getValueAt(selectRows[i], 0)==null?null:model.getValueAt(selectRows[i], 0).toString();
			model.removeRow(selectRows[i]);
		}
		return rowKeys;
	}
	//����
	public static void moveupTableRow(JTable table) {
		DefaultTableModel model = (DefaultTableModel)table.getModel();
		int rowCount = table.getSelectedRowCount();
		if (table.isEditing()) {
			table.getCellEditor().stopCellEditing();
		}
		int[] selectRows = table.getSelectedRows();//��ȡ����ѡ���е��к����飬��0��ʼ��ʵ���Ǵ�ֵ��һ=ҳ������ʾ���к�--hx
		String[] rowKeys = new String[selectRows.length];//ѡ���˼���--hx
        if (selectRows.length == 0) {
			
			return ;
		}
        for (int i = 0; i <= selectRows.length - 1; i++) {//�����������--hx
        	int selectRow = selectRows[i];//��ȡ����ѡ���е��кţ���0��ʼ��ʵ���Ǵ�ֵ��һ=ҳ������ʾ���к�--hx
        	if (selectRow == 0)//�������ǰһ�У���û�취������--hx
    			return;
        	model.moveRow(selectRow, selectRow, selectRow - 1);
		}	
        
        table.clearSelection();//���ѡ����
        for (int i = 0; i <= selectRows.length - 1; i++) {//����ѡ�������ǰ�ƶ�--hx
        	table.changeSelection(selectRows[i]-1, 0, true, false);//�Ƿ���չ�����Ƿ�˨�Σ��ǣ�--hx
        }
	}
	
	//����
	public static void movedownTableRow(JTable table) {
		DefaultTableModel model = (DefaultTableModel)table.getModel();
		int rowCount = table.getSelectedRowCount();
		//ûѡѡ���У�ֱ�ӷ���
		if (table.isEditing()) {
			table.getCellEditor().stopCellEditing();
		}
		int[] selectRows = table.getSelectedRows();//��ȡ����ѡ���е��к����飬��0��ʼ��ʵ���Ǵ�ֵ��һ=ҳ������ʾ���к�--hx
		String[] rowKeys = new String[selectRows.length];//ѡ���˼���--hx
        if (selectRows.length == 0) {
			
			return ;
		}
        for (int i = selectRows.length - 1; i >=0 ; i--) {//�����������--hx
        	int selectRow = selectRows[i];//��ȡ����ѡ���е��кţ���0��ʼ��ʵ���Ǵ�ֵ��һ=ҳ������ʾ���к�--hx
        	if (selectRow == table.getRowCount() - 1)//��������һ�У���û�취������--hx
    			return;
        	model.moveRow(selectRow, selectRow, selectRow + 1);//��ʽִ�ж�ĳ��ִ�У��к�Ϊ��10,10,11��--hx 
		}	
        table.clearSelection();//���ѡ����
        for (int i = selectRows.length - 1; i >=0 ; i--) {//����ѡ�������ǰ�ƶ�--hx
        	table.changeSelection(selectRows[i]+1, 0, true, false);//�Ƿ���չ�����Ƿ�˨�Σ��ǣ�--hx 	
        }
        
        
	}
	
	
	//������
	public static void paixuTableRow(JTable table , int col) {
		int rowCount = table.getRowCount();
        for (int i = 0; i <= rowCount - 1; i++) {//�����������--hx
			table.setValueAt(i+1, i, col);
		}     
	}
	//����
	public static void mergeTableRow(JTable table, int col) {
		mergeTableRow(table, col, false);
	}
	//����
	public static void mergeTableRow(JTable table, int col, boolean isExistBlank) {
		TableModel model = table.getModel();
		int rowCount = table.getSelectedRowCount();
		//ûѡ���У�ֱ�ӷ���
		if (rowCount == 0)
			return;
		if (table.isEditing()) {
			table.getCellEditor().stopCellEditing();
		}
		
//		int xh = 1;
//		for(int i = 0; i < table.getRowCount(); i++) {
//			if(model.getValueAt(i, col) != null && !model.getValueAt(i, col).toString().equals("")) {
//				model.setValueAt(String.valueOf(xh++), i, col);
//			}
//		}
		
		int selectRow = table.getSelectedRow(); //ѡ�еĵ�һ��
		if(model.getValueAt(selectRow, col) == null)
			return;
		int item0 = getRowNum(table, selectRow-1, col); //ѡ�е�ǰһ�����
		
		int xiangcha=0;
		if(table.getRowCount()>selectRow+rowCount){
			xiangcha = getRowNum(table, selectRow+rowCount, col)-item0-2;
		}
		
		
		
		for(int i = 0; i < table.getRowCount(); i++) {
			if(model.getValueAt(i, col) != null) {
				if(i>selectRow-1&&i<selectRow+rowCount){
					model.setValueAt(item0+1, i, col);
				}else if(i>=selectRow+rowCount){
				
					model.setValueAt(getRowNum(table, i, col)-xiangcha, i, col);
				}
				
			}
		}
//		int item0 = getRowNum(table, selectRow-1, col); //ѡ�е�ǰһ�����
//		int item1 = getRowNum(table, selectRow, col);  //ѡ�еĵ�һ�����
//		if(item1 == item0)
//			item1 = item1+1; //ѡ���к��������
//		if(isExistBlank) {
//			model.setValueAt(item1, selectRow, col);
//			for(int i = selectRow+1; i < selectRow+rowCount; i++) {
//				model.setValueAt(item1, i, col);
//			}
//		}
//		else {
//			for(int i = selectRow; i < selectRow+rowCount; i++) {
//				model.setValueAt(item1, i, col);
//			}
//		}
//		if(selectRow+rowCount < table.getRowCount()) { //ʣ����
//			if(model.getValueAt(selectRow+rowCount, col) != null) {
//				model.setValueAt(item1+1, selectRow+rowCount, col);
//				for(int i = selectRow+rowCount+1; i < table.getRowCount(); i++) {
//					if(model.getValueAt(i, col) == null)
//						continue;
//					if(isExistBlank) {
//						if(model.getValueAt(i, col) != null && !model.getValueAt(i, col).toString().equals("")) {
//							int item3 = getRowNum(table, i-1, col);
//							model.setValueAt(item3+1, i, col);
//						}
//						else
//							model.setValueAt(getRowNum(table, i-1, col), i, col);
//					}
//					else {
//						int item3 = getRowNum(table, i-1, col);
//						model.setValueAt(item3+1, i, col);
//					}
//				}
//			}
//		}
	}
	//����
	public static void splitTableRow(JTable table, int col) {
		splitTableRow(table, col, false);
	}
	//����
	public static void splitTableRow(JTable table, int col, boolean isExistBlank) {
		TableModel model = table.getModel();
		int rowCount = table.getSelectedRowCount();
		//ûѡ���У�ֱ�ӷ���
		if (rowCount == 0)
			return;
		if (table.isEditing()) {
			table.getCellEditor().stopCellEditing();
		}
		
		int selectRow = table.getSelectedRow(); //ѡ�еĵ�һ��
		if(model.getValueAt(selectRow, col) == null)
			return;
		int item0 = getRowNum(table, selectRow-1, col); //ѡ�е�ǰһ�����
		int item1 = getRowNum(table, selectRow, col); //ѡ�������
		
		int countfx =0;
		if(item0==item1){
			countfx++;
			model.setValueAt(item0+countfx, selectRow, col);
		}
		for(int i = selectRow+1; i < table.getRowCount(); i++){
			if(item1==getRowNum(table, i, col)){
				countfx++;
				model.setValueAt(getRowNum(table, i, col)+countfx, i, col);	
			}else {
				break;
			}
		}
		
		int countfx1 =countfx;
		if(item0==item1){
			countfx1--;
		}
		for(int i = selectRow+countfx1+1; i < table.getRowCount(); i++) {

			model.setValueAt(getRowNum(table, i, col)+countfx, i, col);
		}
//		TableModel model = table.getModel();
//		int rowCount = table.getSelectedRowCount();
//		//ûѡѡ���У�ֱ�ӷ���
//		if (rowCount == 0)
//			return;
//		if (table.isEditing()) {
//			table.getCellEditor().stopCellEditing();
//		}
//		int selectRow = table.getSelectedRow();
//		if(model.getValueAt(selectRow, col) == null)
//			return;
//		
//		int item0 = getRowNum(table, selectRow-1, col);
//		int item1 = getRowNum(table, selectRow, col);
//		if(item1 == item0)
//			item1 = item1+1;
//		
//		for(int i = selectRow; i < selectRow+rowCount; i++) {
//			model.setValueAt(item1+i-selectRow, i, col);
//		}
//		if(selectRow+rowCount < table.getRowCount()) {
//			if(model.getValueAt(selectRow+rowCount, col) != null) {
//				
//				if((model.getValueAt(selectRow+rowCount, col) != null && !model.getValueAt(selectRow+rowCount, col).toString().equals("")))
//					model.setValueAt(getRowNum(table, selectRow+rowCount-1, col)+1, selectRow+rowCount, col);
//				
//				for(int i = selectRow+rowCount+1; i < table.getRowCount(); i++) {
//					if(model.getValueAt(i, col) == null)
//						continue;
//					if(isExistBlank) {
//						if(model.getValueAt(i, col) != null && !model.getValueAt(i, col).toString().equals("")) {
//							int item3 = getRowNum(table, i-1, col);
//							model.setValueAt(item3+1, i, col);
//						}
//						else
//							model.setValueAt("", i, col);
//					}
//					else {
//						int item3 = getRowNum(table, i-1, col);
//						model.setValueAt(item3+1, i, col);
//					}
//				}
//			}
//		}
	}
	
	/**
	 * ��ȡ�кţ����Ϊ�������ϲ���
	 * @param table
	 * @param row
	 * @param col
	 * @return
	 */
	private static int getRowNum(JTable table, int row, int col) {
		TableModel model = table.getModel();
		int item0 = 0;
		for(int k = row; k >= 0; k--) {
			if(model.getValueAt(k, col) != null && !model.getValueAt(k, col).toString().equals("")) {
				item0 = Integer.valueOf(model.getValueAt(k, col).toString());
				break;
			}
		}
		return item0;
	}

	/**
     * computes a rectangle given the coordinates of two points
     * @param point1 the first point
     * @param point2 the second point
     * @return the correct rectangle
     */
    public static Rectangle2D getComputedRectangle(Point2D point1, Point2D point2){
        
        if(point1!=null && point2!=null){
            
            double 	width=point2.getX()-point1.getX(), 
            height=point2.getY()-point1.getY(), 
            x=point1.getX(), 
            y=point1.getY();
            
            if(point1.getX()>point2.getX() && point1.getY()>point2.getY()){
                
                x=point2.getX();
                y=point2.getY();
                width=point1.getX()-point2.getX();
                height=point1.getY()-point2.getY();
                
            }else if(point1.getX()>point2.getX() && point1.getY()<point2.getY()){
                
                width=point1.getX()-point2.getX();
                height=point2.getY()-point1.getY();
                x=point2.getX();
                y=point1.getY();
                
            }else if(point1.getX()<point2.getX() && point1.getY()>point2.getY()){
                
                width=point2.getX()-point1.getX();
                height=point1.getY()-point2.getY();
                x=point1.getX();
                y=point2.getY();
            }
            
            return new Rectangle2D.Double(x, y, width, height);	
        }
        
        return new Rectangle2D.Double(0, 0, 0, 0);
    }
    
    
    /**
	 * scales the given rectangle 
	 * @param rectangle the rectangle to scale
	 * @param toBaseScale 	true to scale it to 100%
	 * 										false to scale it at the current canvas scale
	 * @return the scaled rectangle
	 */
	public static Rectangle2D getScaledRectangle(JSVGCanvas canvas, Rectangle2D rectangle, boolean toBaseScale){
		
		Rectangle2D rect=new Rectangle2D.Double(
				rectangle.getX(), rectangle.getY(), rectangle.getWidth(), rectangle.getHeight());
		
		if(toBaseScale){
			
			//applying the inverse of the transforms
			AffineTransform af=new AffineTransform();
			
			try{
				af.preConcatenate(
						canvas.getRenderingTransform().createInverse());
			}catch (Exception ex){}
			
			try{
				af.preConcatenate(
						canvas.getViewingTransform().createInverse());
			}catch (Exception ex){}

			Rectangle2D rect2=af.createTransformedShape(rect).getBounds2D();
			rect=new Rectangle2D.Double(rect2.getX(), rect2.getY(), rect2.getWidth(), rect2.getHeight());
			
		}else{
			
			//applying the transforms
			AffineTransform af=new AffineTransform();
			
			try{
				af.preConcatenate(
						canvas.getViewingTransform());
			}catch (Exception ex){}
			
			try{
				af.preConcatenate(
						canvas.getRenderingTransform());
			}catch (Exception ex){}

			Rectangle2D rect2=af.createTransformedShape(rect).getBounds2D();
			rect=new Rectangle2D.Double(rect2.getX(), rect2.getY(), rect2.getWidth(), rect2.getHeight());
		}
		
		return rect;
	}

	
}
