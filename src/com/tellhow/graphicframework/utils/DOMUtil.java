package com.tellhow.graphicframework.utils;

import java.awt.Dimension;
import java.awt.geom.AffineTransform;
import java.awt.geom.Dimension2D;
import java.awt.geom.Point2D;
import java.awt.geom.Rectangle2D;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.apache.batik.bridge.BridgeContext;
import org.apache.batik.dom.svg.SAXSVGDocumentFactory;
import org.apache.batik.dom.svg.SVGOMMatrix;
import org.apache.batik.gvt.GraphicsNode;
import org.apache.batik.swing.JSVGCanvas;
import org.apache.batik.util.XMLResourceDescriptor;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.svg.SVGDocument;

import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.basic.SVGTransformMatrix;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2012-2-16 ����02:42:59 
 */
public class DOMUtil {
	
	public static void setElementTransform(Element element, double tx, double ty, double sx, double sy) {
		element.setAttribute("transform", "translate(" + String.valueOf(tx) + "," + String.valueOf(ty) + ") "+"scale(" + String.valueOf(sx) + "," + String.valueOf(sy) + ")");
        for(int i = 0; i < element.getChildNodes().getLength(); i++) {
        	Node node= element.getChildNodes().item(i);
        	if(!node.getNodeName().equals("#text") && !node.getNodeName().equals("defs"))
        		((Element)node).setAttribute("transform", "translate(" + String.valueOf(tx) + "," + String.valueOf(ty) + ") "+"scale(" + String.valueOf(sx) + "," + String.valueOf(sy) + ")");
        }
    }
	
	public static Point2D.Double getElementStartPoint(Element element) {
		if(element==null)
			return null;
		Point2D.Double opoint = null;
		String nodeName = element.getNodeName();
		if (nodeName.equals("path")) {
			String d = element.getAttribute("d");
			String[] array = d.replace("M", "").replace("L", "").replace("Z", "").replace(",", " ").trim().split(" ");  //�麣path����������","���������replace(",", " ")
			opoint = new Point2D.Double(Double.valueOf(array[0]), Double.valueOf(array[1]));
		}
		else if (nodeName.equals("polygon")) {
			String points = element.getAttribute("points");
			String[] array = points.split(" ")[0].split(",");
			if(array.length >= 3)
				opoint = new Point2D.Double(Double.valueOf(array[1]), Double.valueOf(array[2]));
			else
				opoint = new Point2D.Double(0, 0);
		}
		else if (nodeName.equals("polyline")) {
			String points = element.getAttribute("points");
			String[] array = points.replace(", ", ",").split(" ")[0].split(",");
			opoint = new Point2D.Double(Double.valueOf(array[0]), Double.valueOf(array[1]));
		}
		else if(nodeName.equals("line")) {
			opoint = new Point2D.Double(Double.valueOf(element.getAttribute("x1")), Double.valueOf(element.getAttribute("y1")));
		}
		else if(nodeName.equals("circle") || nodeName.equals("ellipse")) {
			opoint = new Point2D.Double(Double.valueOf(element.getAttribute("cx")), Double.valueOf(element.getAttribute("cy")));
		}
		else if(element.hasAttribute("x") && element.hasAttribute("y"))
			opoint = new Point2D.Double(Double.valueOf(element.getAttribute("x")), Double.valueOf(element.getAttribute("y")));
		else if(nodeName.equals("use")){
			GroundPoint temp = UseGroundPoint.getPoint(element);
			opoint=temp .getPoint();
		}else
			opoint = new Point2D.Double(0, 0);
		SVGTransformMatrix svgTransformMatrix = getTransformMatrix(element);
		Point2D.Double tpoint = new Point2D.Double();
		
		if(opoint != null){
			tpoint.x = svgTransformMatrix.getNewX(opoint.x, opoint.y);
			tpoint.y = svgTransformMatrix.getNewY(opoint.x, opoint.y);
		}
		
		return tpoint;
	}
	
	public static Point2D.Double getElementWHPoint(Element element) {
		if(element==null)
			return null;
		Point2D.Double opoint = null;
		String nodeName = element.getNodeName();
		if (nodeName.equals("path")) {
			String d = element.getAttribute("d");
			String[] array = d.replace("M", "").replace("L", "").replace("Z", "").trim().split(" ");
			int length=array.length;
			opoint = new Point2D.Double(Double.valueOf(array[length-2]), Double.valueOf(array[length-1]));
//		}else if(nodeName.equals("use")){
//			String width=element.getAttribute("width");
//			String heigth=element.getAttribute("height");
//			opoint = new Point2D.Double(Double.valueOf(width), Double.valueOf(heigth));
		}
		else if (nodeName.equals("polygon")) {
			String points = element.getAttribute("points");
			String[] array = points.split(" ")[0].split(",");
			int length=array.length;
			opoint = new Point2D.Double(Double.valueOf(array[length-2]), Double.valueOf(array[length-1]));
		}
		else if (nodeName.equals("polyline")) {
			String points = element.getAttribute("points");
			String[] array = points.split(" ")[0].split(",");
			int length=array.length;
			opoint = new Point2D.Double(Double.valueOf(array[length-2]), Double.valueOf(array[length-1]));
		}
		else if(nodeName.equals("line")) {
			opoint = new Point2D.Double(Double.valueOf(element.getAttribute("x2")), Double.valueOf(element.getAttribute("y2")));
		}
		else if(nodeName.equals("circle") || nodeName.equals("ellipse")) {
			opoint = new Point2D.Double(Double.valueOf(element.getAttribute("cx")), Double.valueOf(element.getAttribute("cy")));
		}
		else if(element.hasAttribute("x") && element.hasAttribute("y"))
			opoint = new Point2D.Double(Double.valueOf(element.getAttribute("x")), Double.valueOf(element.getAttribute("y")));
		
		else
			opoint = new Point2D.Double(0, 0);
		SVGTransformMatrix svgTransformMatrix = getTransformMatrix(element);
		Point2D.Double tpoint = new Point2D.Double();
		tpoint.x = svgTransformMatrix.getNewX(opoint.x, opoint.y);
		tpoint.y = svgTransformMatrix.getNewY(opoint.x, opoint.y);
		return tpoint;
	}
	
	/**
	 * �ҽӵ��ߵĵ�
	 * */
	public static GroundPoint getGroundPoint(Element element){
		if(element==null)
			return null;
		GroundPoint gpoint=new GroundPoint();
		String flag="1";
		Point2D.Double opoint = null;
		String nodeName = element.getNodeName();
		if (nodeName.equals("path")) {
			String d = element.getAttribute("d");
			String[] array = d.replace("M", "").replace("L", "").replace("Z", "").trim().split(" ");
			opoint = new Point2D.Double(Double.valueOf(array[0]), Double.valueOf(array[1]));
			flag=getFlagByPoint(array);
		}
		else if (nodeName.equals("polygon")) {
			String points = element.getAttribute("points");
			String[] array = points.split(" ")[0].split(",");
			opoint = new Point2D.Double(Double.valueOf(array[1]), Double.valueOf(array[2]));
			flag=getFlagByPoint(points.split(" "));
		}
		else if (nodeName.equals("polyline")) {
			String points = element.getAttribute("points");
			String[] array = points.split(" ")[0].split(",");
			opoint = new Point2D.Double(Double.valueOf(array[0]), Double.valueOf(array[1]));
			flag=getFlagByPoint(points.split(" "));
		}
		else if(nodeName.equals("line")) {
			opoint = new Point2D.Double(Double.valueOf(element.getAttribute("x1")), Double.valueOf(element.getAttribute("y1")));
		}
		else if(nodeName.equals("circle") || nodeName.equals("ellipse")) {
			opoint = new Point2D.Double(Double.valueOf(element.getAttribute("cx")), Double.valueOf(element.getAttribute("cy")));
		}
		else if(element.hasAttribute("x") && element.hasAttribute("y"))
			opoint = new Point2D.Double(Double.valueOf(element.getAttribute("x")), Double.valueOf(element.getAttribute("y")));
		else if(nodeName.equals("use")){
			GroundPoint temp = UseGroundPoint.getPoint(element);
			opoint=temp .getPoint();
			flag=temp.getFlag();
		}else
			opoint = new Point2D.Double(0, 0);
		SVGTransformMatrix svgTransformMatrix = getTransformMatrix(element);
		Point2D.Double tpoint = new Point2D.Double();
		tpoint.x = svgTransformMatrix.getNewX(opoint.x, opoint.y);
		tpoint.y = svgTransformMatrix.getNewY(opoint.x, opoint.y);
		gpoint.setPoint(tpoint);
		gpoint.setFlag(flag);
		return gpoint;
	}
	/**
	 * ����points���Ի�ȡ�ҽӵ��ߵĴ�ֱ��ˮƽ��־
	 * */
	public static String getFlagByPoint(String[] array) {
		String x1 = array[0].split(",")[0];
		String x2=array[1].split(",")[0];
		return x1.trim().equals(x2.trim())?"0":"1";
	}

	public static Element getSymbolByID(Element element,String id) {
		Element defs =(Element) element.getOwnerDocument().getDocumentElement().getElementsByTagName("defs").item(0);
		NodeList syms = defs.getChildNodes();
		for (int i = 0; i < syms.getLength(); i++) {
			Node node = syms.item(i);
			if(node instanceof Element){
				Element sym=(Element) node;
				if(id.equals(sym.getAttribute("id"))){
					return sym;
				}
			}
		}
		return null;
	}
	

	/**
	 * �õ��ڵ�ı任����
	 * @param node
	 * @return
	 */
	public static SVGTransformMatrix getTransformMatrix(Node node) {

		if (node != null) {

			NamedNodeMap attributes = node.getAttributes();

			if (attributes != null) {

				// if the node has the transform atrribute
				Node att = attributes.getNamedItem("transform");

				if (att != null) {

					// gets the value of the transform attribute
					String value = att.getNodeValue();

					// creating the matrix transform
					return getTransformMatrix(value);
				}
			}
		}

		// otherwise returns the identity matrix
		return new SVGTransformMatrix(1, 0, 0, 1, 0, 0);
	}
	
	/**
	 * �õ��ڵ�ı任����
	 * @param value
	 * @return
	 */
	public static SVGTransformMatrix getTransformMatrix(String value) {

		SVGTransformMatrix matrix = new SVGTransformMatrix(1, 0, 0, 1, 0, 0);

		if (value != null && !value.equals("")) {

			int rang = value.indexOf("matrix");
			// computes the double values of the matrix in the transform
			// attribute
			if (rang > -1) {

				String subValue = "";
				subValue = value.substring(rang, value.length());
				subValue = subValue.substring(0, subValue.indexOf(")") + 1);
				value = value.replaceAll("[" + subValue + "]", "");
				subValue = subValue.substring(subValue.indexOf("("), subValue
						.length());

				// cleans the string
				value = StringUtils.cleanTransformString(value);
				subValue = subValue.replaceAll("[(]", "");
				subValue = subValue.replaceAll("[)]", "");
				subValue=subValue.replace(" ", ",");
				subValue = subValue.concat(",");

				int i = subValue.indexOf(','), j = 0;
				double[] matrixDb = new double[6];

				while (i != -1) {

					try {
						matrixDb[j] = new Double(subValue.substring(0, i))
								.doubleValue();
					} catch (Exception ex) {
						return new SVGTransformMatrix(1, 0, 0, 1, 0, 0);
					}

					subValue = subValue.substring(subValue.indexOf(',') + 1,
							subValue.length());
					i = subValue.indexOf(',');

					j++;
				}

				matrix = new SVGTransformMatrix(matrixDb[0], matrixDb[1],
						matrixDb[2], matrixDb[3], matrixDb[4], matrixDb[5]);
			} 
			else {
				AffineTransform at = new AffineTransform();
				String rotateStr = StringUtils.getStringValue(value, "rotate");
				if (!rotateStr.equals("")) {
					String[] rotates = StringUtils.tokenize(rotateStr, ",", true, true);
					if(rotates.length < 3)
						rotates = StringUtils.tokenize(rotateStr, " ", true, true);
					if(rotates.length >= 3)
						at.rotate(Float.valueOf(rotates[0]), Float.valueOf(rotates[1]), Float.valueOf(rotates[2]));
				}
				String scaleStr = StringUtils.getStringValue(value, "scale");
				if (!scaleStr.equals("")) {
					String[] scales = StringUtils.tokenize(scaleStr, ",", true, true);
					at.scale(Float.valueOf(scales[0]), Float.valueOf(scales[1]));

				}
				String translateStr = StringUtils.getStringValue(value, "translate");
				if (!translateStr.equals("")) {
					String[] translates = StringUtils.tokenize(translateStr,",", true, true);
					at.translate(Float.valueOf(translates[0]), Float.valueOf(translates[1]));

				}
				SVGOMMatrix svgMatrix = new SVGOMMatrix(at);
				matrix = new SVGTransformMatrix(svgMatrix.getA(), svgMatrix
						.getB(), svgMatrix.getC(), svgMatrix.getD(), svgMatrix
						.getE(), svgMatrix.getF());

			}
		}
		return matrix;
	}
	public static boolean writeXMLFile(Document doc, String filePath)
	{
		try {
			TransformerFactory tFactory = TransformerFactory.newInstance();
			Transformer transformer = tFactory.newTransformer();
			transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
			transformer.setOutputProperty(OutputKeys.INDENT, "yes");
			transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
			DOMSource source = new DOMSource(doc);
			FileOutputStream stream = new FileOutputStream(filePath);
			StreamResult result = new StreamResult(stream);
			transformer.transform(source, result);
			stream.close();
			return true;
		}
		catch(Exception ex) {
			ex.printStackTrace();
			return false;
		}
	}
	
	public static String getStyleProperty(Element element, String name) {

		String value = "";

		if (element != null && name != null && !name.equals("")) {

			// gets the value of the style attribute
			String styleValue = element.getAttribute("style");
			styleValue = styleValue.replaceAll("\\s*[;]\\s*", ";");
			styleValue = styleValue.replaceAll("\\s*[:]\\s*", ":");

			int rg = styleValue.indexOf(";".concat(name.concat(":")));

			if (rg != -1) {

				rg++;
			}

			if (rg == -1) {

				rg = styleValue.indexOf(name.concat(":"));

				if (rg != 0) {

					rg = -1;
				}
			}

			// if the value of the style attribute contains the property
			if (styleValue != null && !styleValue.equals("") && rg != -1) {

				// computes the value of the property
				value = styleValue.substring(rg + name.length() + 1, styleValue
						.length());
				rg = value.indexOf(";");
				value = value.substring(0, rg == -1 ? value.length() : rg);
			}
			
			if(!name.toLowerCase().equals("style") && element.hasAttribute(name) && value.equals(""))
				return element.getAttribute(name);
		}

		return value;
	}

	public static void setStyleProperty(Element element, String name,
			String value) {

		if (element != null && name != null && !name.equals("")) {

			if (value == null) {

				value = "";
			}

			// the separators
			String valuesSep = ";", nameToValueSep = ":";

			// the map associating the name of a property to its value
			HashMap<String, String> values = new HashMap<String, String>();

			// getting the value of the style attribute
			String styleValue = element.getAttribute("style");
			styleValue = styleValue.replaceAll("\\s*[;]\\s*", ";");
			styleValue = styleValue.replaceAll("\\s*[:]\\s*", ":");

			// filling the map associating a property to its value
			String[] splitValues = styleValue.split(valuesSep);
			int pos = -1;
			String sname = "", svalue = "";

			for (int i = 0; i < splitValues.length; i++) {

				if (splitValues[i] != null && !splitValues[i].equals("")) {

					pos = splitValues[i].indexOf(nameToValueSep);

					sname = splitValues[i].substring(0, pos);
					svalue = splitValues[i].substring(pos
							+ nameToValueSep.length(), splitValues[i].length());

					if (!sname.equals("") && !svalue.equals("")) {

						values.put(sname, svalue);
					}
				}
			}

			// adding the new value
			if (value.equals("")) {

				values.remove(name);

			} else {

				values.put(name, value);
			}

			// computing the new style value
			styleValue = "";

			for (String newName : values.keySet()) {

				styleValue += newName + nameToValueSep + values.get(newName)
						+ valuesSep;
			}

			// sets the value of the style attribute
			if (styleValue != null && !styleValue.equals("")) {

				element.setAttribute("style", styleValue);

			} else {

				element.removeAttribute("style");
			}
		}
	}
	
	
	public static SVGDocument readSVGFile(String filePath) {
		String parser = XMLResourceDescriptor.getXMLParserClassName();
		SAXSVGDocumentFactory factory = new SAXSVGDocumentFactory(parser);
		SVGDocument doc = null;
		try {
			File file = new File(filePath);
			doc = factory.createSVGDocument(file.toURI().toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return doc;
	}
	
	public static Document readXMLFile(String filePath)
	{
		Document doc = null;
		try {
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			File f = new File(filePath);
			if(f.isFile()&&f.length()>0){
				doc = db.parse(f);
				doc.normalize();
			}
		}
		catch(Exception ex) {
			System.out.println("�ļ�" + filePath + "��ȡʧ�ܣ�\r\n����ԭ��"+ex.getMessage());
			ex.printStackTrace();
		}
		return doc;
	}
	  //��ȡ�ڵ���Ϣ
	   public static String   ElementToXMl(Element n){
	    	
	    	 TransformerFactory transFactory = TransformerFactory.newInstance(); 
	    	 Transformer transformer = null; 
	    	 try { 
	    	       transformer = transFactory.newTransformer(); 
	    	 } catch (TransformerConfigurationException e) 
	    	 { 
	    	      e.printStackTrace(); 
	    	 } 
	    	 StringWriter buffer = new StringWriter(); 
	    	 transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes"); 
	    	 try { 
	    	    transformer.transform(new DOMSource(n),new StreamResult(buffer)); 
	    	 } catch (TransformerException e) 
	    	 { 
	    	     e.printStackTrace(); 
	    	 } 
	    	 String s = buffer.toString(); 
	    	 return s;
	    }
	   
	   
	   /**
	     * returns whether the given element is a shape node or not
	     * @param element
	     * @return whether the given element is a shape node or not
	     */
	    public static boolean isElementAShape(Element element){
	        
	    	Set<String> svgShapeElementNames=new HashSet<String>();
	    	svgShapeElementNames.add("g");
			svgShapeElementNames.add("ellipse");
			svgShapeElementNames.add("image");
			svgShapeElementNames.add("path");
			svgShapeElementNames.add("rect");
			svgShapeElementNames.add("text");
	        if(element!=null){
	        	
	        	return svgShapeElementNames.contains(element.getNodeName());
	        }
	        
	        return false;
	    }
	    
	    
	    /**
		 * computes the position and the size of a node on the canvas, 
		 * the only transform applied is the node transform
		 * @param shape the node whose position and size is to be computed
		 * @return a rectangle representing the position and size of the given node
		 */
		public static Rectangle2D getNodeGeometryBounds(JSVGCanvas canvas, Element shape){
			
			Rectangle2D bounds=new Rectangle2D.Double();
			
			if(shape!=null){
				
				//gets the bridge context 
				BridgeContext ctxt=
						canvas.getUpdateManager().getBridgeContext();
				
				if(ctxt!=null){
					
					//gets the graphics node corresponding to the given node
					GraphicsNode gnode=null;
					
					try{gnode=ctxt.getGraphicsNode(shape);}catch (Exception e){}
					
					if(gnode!=null){

						Rectangle2D bounds2D=gnode.getGeometryBounds();
						
						if(bounds2D!=null) {
							
							//getting the transform of this node
							AffineTransform af=gnode.getTransform();
							
							if(af!=null) {
								
								try {
									bounds2D=af.createTransformedShape(bounds2D).getBounds2D();
								}catch (Exception ex) {}
							}
							
							bounds=new Rectangle2D.Double(bounds2D.getX(), bounds2D.getY(), 
									bounds2D.getWidth(), bounds2D.getHeight());
						}
					}
				}
			}
			
			return bounds;
		}

	    public static double max(List<Double> nums){
	    	double max = 0;
	    	int count=0;
	    	for (Double d : nums) {
				if(count==0){
					max=d;
				}
				max=Math.max(max, d);
				count++;
			}
	    	return max;
	    }
	    public static double min(List<Double> nums){
	    	double min = 0;
	    	int count=0;
	    	for (Double d : nums) {
				if(count==0){
					min=d;
				}
				min=Math.min(min, d);
				count++;
			}
	    	return min;
	    }
	    
	    public static Element getMetaDataElement(Element element)
		{
			for (Node child=element.getFirstChild(); child!=null; child=child.getNextSibling()) 
			{
				if(child.getNodeName().equals("metadata"))
					return (Element)child;
			}
			return null;
		}
		
	    
	    public static Element getEquipInfoElement(Element element)
		{
			Element EquipInfoElement = null;
			Element metaDataElement = getMetaDataElement(element);
			if(metaDataElement != null)
				EquipInfoElement = (Element)metaDataElement.getElementsByTagName("cge:TPSR_Ref").item(0);
			return EquipInfoElement;
		}
	    
	    public static String getEquipID(Element element)
		{
			String equipID = "";
			Element equipInfoElement = getEquipInfoElement(element);
			if(equipInfoElement != null)
				equipID = equipInfoElement.getAttribute("TObjectID");
			return equipID;
		}
	    
	    public static String getStationID(Document doc)
		{
			return doc.getDocumentElement().getAttribute("StationID");
		}
}