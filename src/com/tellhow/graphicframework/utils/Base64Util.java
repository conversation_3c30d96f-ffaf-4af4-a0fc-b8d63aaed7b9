package com.tellhow.graphicframework.utils;

import java.io.IOException;
import java.io.InputStream;

import org.apache.commons.io.IOUtils;
import org.apache.log4j.Logger;

import sun.misc.BASE64Encoder;

public final class Base64Util {
	private static Logger log = Logger.getLogger(Base64Util.class);
	public static String encode(InputStream input) {
		byte[] bytes = new byte[0];
		try {
			bytes = IOUtils.toByteArray(input);
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}
		return new BASE64Encoder().encodeBuffer(bytes);
	}
	
	public static void main(String[] args) {
		String s = encode(Base64Util.class.getResourceAsStream("hang.png"));
		System.out.println(s);
	}
}
