/**
* ��Ȩ���� : ̩������ɷ����޹�˾��Ȩ����
* �� Ŀ �� ��
* ����˵�� : ��ʼ���豸����
* ��    �� : ������
* �������� : 2008-08-18
* �޸����� ��
* �޸�˵�� ��
* �� �� �� ��
**/

package com.tellhow.graphicframework.utils;

import java.awt.Color;
import java.text.DecimalFormat;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;

/**
 *
 * <AUTHOR>
 */
public class ColorUtils {
    
	/**
	 * ����ɫ"RGB(0,0,0)"��ʽ���ַ���ת����Color����
	 * @param volCode
	 * @return
	 */
    public static Color getColor(String rgb){
    	String num[] = rgb.replace("rgb", "").replace("(", "").replace(")", "").split(",");
        return new Color(Integer.valueOf(num[0]),Integer.valueOf(num[1]),Integer.valueOf(num[2]));
    }
}
