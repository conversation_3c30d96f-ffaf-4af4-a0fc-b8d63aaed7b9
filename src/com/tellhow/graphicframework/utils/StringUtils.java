/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 字符串操作类
 * 作    者 : 江杰
 * 开发日期 : 2008-08-12
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.graphicframework.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
/**
 *
 * <AUTHOR>
 *
 */
public final class StringUtils {

    public static String getUUID() {
        return java.util.UUID.randomUUID().toString();
    }

    /**
     * 判断两字符串之间的关系
     * @param srcStr 需要比较的字符串
     * @param tagStr 被比较的字符串
     * @return 0:包含 1:不包含
     */
    public static int compareStr(String srcStr, String tagStr) {

        int flag = 1;
        String[] tagArr = tagStr.split(",");
        if (isNotEmpty(tagStr)) {
            for (int i = 0; i < tagArr.length; i++) {
                if (tagArr[i].equals(srcStr)) {
                    flag = 0;//包含
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * 判断字符串srcStr中是否包括tagStr
     * @param srcStr
     * @param tagStr
     * @return
     */
    public static boolean contains(String srcStr, String tagStr) {
        int index = srcStr.indexOf(tagStr);
        if (index < 0) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 判断字符串对象是否为空
     * @param str 原字符串
     * @return
     */
    public static boolean isEmpty(String str) {
        if (str == null || "".equals(str)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断字符串对象是否不为空
     * @param str 原字符串
     * @return
     */
    public static  boolean isNotEmpty(String str) {
        if (str == null || "".equals(str)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 替换指定两个字符之间的字符串 包括制定字符
     * @param srcStr 原字符串 splitOne 制定字符1 splitTwo 指定字符2 updateStr用于替换的字符串
     * @return tagStr
     */
    public static String replaceStr(String srcStr, String splitOne, String splitTwo, String updateStr) {
        String tagStr = "";
        String oldStr = srcStr.substring(srcStr.indexOf(splitOne), srcStr.indexOf(splitTwo) + 1);
        tagStr = srcStr.replace(oldStr, updateStr);
        return tagStr;
    }

    public static String replaceFirstStr(String srcStr, String splitStr, String updateStr) {
        String oldStr = srcStr.substring(0, srcStr.indexOf(splitStr)) + updateStr + srcStr.substring(srcStr.indexOf("$", 2) + 1);
        return oldStr;
    }

    public static String getEquipName(String powerDeviceName) {
        String equipName = powerDeviceName;
        String[] arr = powerDeviceName.split("/");
        if(arr.length >= 2)
            equipName = arr[1];
        arr = equipName.split("\\.");
        if(arr.length >= 2)
            equipName = arr[1];
        if (equipName.toUpperCase().split("KV").length == 2 &&
                equipName.indexOf(getVoltInDevName(equipName)) <=0) {
            equipName = equipName.toUpperCase().split("KV")[1];
        }
        equipName = equipName.trim();
        return equipName;
    }

    public static String getStationName(String powerDeviceName) {
        String stationName = powerDeviceName;
        String volt = getVoltInDevName(stationName);
        stationName = stationName.replace(volt, "");
        if(stationName.indexOf("变电站") > 0)
            stationName = stationName.replace("变电站", "变");
        return stationName;
    }

    public static String getVoltInDevName(String powerDeviceName) {
        String volt = "";
        String equipName = powerDeviceName;
        int pos = equipName.toUpperCase().indexOf("KV");
        if (pos >= 0) {
            volt = "";
            for(int i = pos-1; i >=0; i--) {
                char ch = equipName.charAt(i);
                if (ch >= '0' && ch <= '9')
                    volt = ch + volt;
                else
                    break;
            }
        }
        else
            volt = "";
        return volt;
    }

    /**
     * 作用：获取设备编号
     * @param powerDeviceName  设备名称
     * @return
     */
    public static String getSwitchCode(String powerDeviceName) {
        String knifeName = "";
        String knifeCode = "";
        powerDeviceName= powerDeviceName.replace("PT","");
        List<char[]> numList=new ArrayList<char[]>(); //一个开关名称中存在多个的返回最多的一个
        //带电压等级标签时去掉
        if (powerDeviceName.toUpperCase().lastIndexOf("KV") != -1) {
            knifeName = powerDeviceName.toUpperCase().substring(powerDeviceName.toUpperCase().lastIndexOf("KV")+2);
        } else {
            knifeName = powerDeviceName;
        }

        //将“-”去掉 201310取消
        //knifeName=knifeName.replaceAll("-", "");
        //knifeName=knifeName.replaceAll("_", "");
        //knifeName=knifeName.toUpperCase();

        char[] charArr = knifeName.toCharArray();
        char[] numArr = new char[charArr.length];
        int count = 0;
        for (int j = 0; j < charArr.length; j++) {
            char ch = charArr[j];
            if ((ch >= '0' && ch <= '9')||(ch>='A'&& ch<='Z')||ch=='-'||ch=='甲'||ch=='乙'||ch=='Ⅰ'||ch=='Ⅱ'||ch=='Ⅲ'||ch=='Ⅳ'||ch=='Ⅴ'||ch=='Ⅵ'||ch=='#') {   //在西北网开关数超过10就用A表示
                if (j > 0) {
                    if (numArr.length > 0 && ((charArr[j - 1] < '0' || charArr[j - 1] > '9')&&charArr[j - 1]!='A'&&charArr[j - 1]!='B'&&charArr[j - 1]!='-'
                            &&charArr[j - 1]!='甲'&&charArr[j - 1]!='乙'&&charArr[j - 1]!='Ⅰ'&&charArr[j - 1]!='Ⅱ'&&charArr[j - 1]!='Ⅲ'&&charArr[j - 1]!='Ⅳ'&&charArr[j - 1]!='Ⅴ'&&charArr[j - 1]!='Ⅵ'&&charArr[j - 1]!='#')) {
                        count = 0;
                        char[] tempArr=new char[numArr.length];
                        for (int w = 0; w < numArr.length; w++) {
                            tempArr[w]=numArr[w];
                            numArr[w] = ' ';
                        }
                        numList.add(tempArr);
                    }
                }
                numArr[count] = ch;
                count++;
            }
        }

        String temp="";
        for (int i = 0; i < numList.size(); i++) {
            temp=String.valueOf(numList.get(i)).trim();
            if(i==0)
                knifeCode=temp;
            else{
                knifeCode=temp.length()>knifeCode.length()?temp:knifeCode;
            }
        }

        String numStr=String.valueOf(numArr).trim();
        if(numStr.length()>knifeCode.length())
            knifeCode=numStr;
        return knifeCode;
    }

    /**
     * 作用：获取设备编号
     * @param powerDeviceName  设备名称
     * @return
     */
    public static String getMotherLineCode(String powerDeviceName) {
        String mlName = powerDeviceName;
        String mlCode = "";

        if (mlName.toUpperCase().split("KV").length > 1) //去掉母线前的电压等级
        {
            mlCode = mlName.toUpperCase().split("KV")[1];
        }

        mlCode = mlCode.replaceAll("段母线", "");
        mlCode = mlCode.replaceAll("母线", "");
        mlCode = mlCode.replaceAll("母", "");
        return mlCode.trim();
    }

    public static String killVoltInDevName(String powerDeviceName) {
        String devName = powerDeviceName.toUpperCase();
        if (devName.indexOf("KV") > 0) {
            String[] names = devName.split("KV");
            if (names.length > 1) {  //防止出现 "220kv" 的设备名称  这时返回“”；
                return names[names.length - 1];
            } else {
                return "";
            }
        } else {
            return powerDeviceName;
        }
    }

    public static String getVolt(String voltflag){
        String voltStr=""; //电压等级
        switch(Integer.parseInt(voltflag)){
            case 1 : voltStr="110kV" ;break;
            case 2 : voltStr="220kV"; break;
            case 3 : voltStr="35kV"; break;
            case 5 : voltStr="500kV"; break;
            case 9 : voltStr="10kV"; break;
            default: voltStr="";
        }
        return voltStr;

    }

    public static String getVolt(double voltvalue){
        String volt = String.valueOf(voltvalue);
        String voltStr = volt.substring(0, volt.indexOf(".")) + "kV";
        return voltStr;

    }

    public static String getVoltByWord(String word){
        String voltStr=""; //电压等级

        if(word.contains("110kV")){
            voltStr="110kV";
        }else if(word.contains("220kV")){
            voltStr="220kV";
        }else if(word.contains("35kV")){
            voltStr="35kV";
        }else if(word.contains("500kV")){
            voltStr="500kV";
        }else if(word.contains("10kV")){
            voltStr="10kV";
        }

        return voltStr;
    }

    /**
     * 作用：判断字符是否罗马字母
     * @param num
     * @return
     */
    public static boolean isALB(char c){
        boolean result = false;
        switch(c){
            case 'Ⅰ' : result = true; break;
            case 'Ⅱ' : result = true; break;
            case 'Ⅲ' : result = true; break;
            case 'Ⅴ' : result = true; break;
            default: result = false;
        }
        return result;
    }

    /**
     * 作用：阿拉伯数字否罗马字母
     * @param num
     * @return
     */
    public static String toluoma(String num){
        String result = "";

        if(num.equals("1")){
            result = "Ⅰ";
        }else if(num.equals("2")){
            result = "Ⅱ";
        }else if(num.equals("3")){
            result = "Ⅲ";
        }else if(num.equals("4")){
            result = "Ⅳ";
        }else if(num.equals("5")){
            result = "Ⅴ";
        }else{
            result = num;
        }

        return result;
    }

    /**
     * 作用：数字转化为罗马字母
     * @param num
     * @return
     */
    public static String getALB(String num){
        String voltStr=""; //电压等级
        switch(Integer.parseInt(num)){
            case 1 : voltStr="Ⅰ" ;break;
            case 2 : voltStr="Ⅱ"; break;
            case 3 : voltStr="Ⅲ"; break;
            case 4 : voltStr="Ⅴ"; break;
            default: voltStr="";
        }
        return voltStr;
    }

    /**
     * 作用：罗马字母转化为数字
     * @param voltStr
     * @return
     */
    public static int getNum(String voltStr){
        int num=0; //电压等级
        if(voltStr.equals("I"))
            num=1 ;
        if(voltStr.equals("II"))
            num=2 ;
        if(voltStr.equals("III"))
            num=3 ;
        if(voltStr.equals("IV"))
            num=4 ;
        if(voltStr.equals("V"))
            num=5 ;
        return num;
    }

    /**
     *
     * @param devName 设备名称
     * @return "I母" "II母" "III母"
     */
    public static String getALBinName(String devName){
        if(devName.indexOf("III")+devName.indexOf("Ⅲ")>0)
            return "Ⅲ母";
        if(devName.indexOf("II")+devName.indexOf("Ⅱ")>0)
            return "Ⅱ母";
        if(devName.indexOf("I")+devName.indexOf("Ⅰ")>0)
            return "Ⅰ母";
        return "母线";
    }
    /**
     *
     * @param instrans 对象集合
     * @return true 所有对象是同一个对象 false 不是
     */
    public static boolean isSameInstrans(List instrans){
        if(instrans==null)
            return false;
        if(instrans.size()==0)
            return false;
        Object object=null;
        for (int i = 0; i < instrans.size(); i++) {
            if(instrans.get(i)==null)
                return false;
            if(object==null)
                object=instrans.get(i);
            else
            {
                if(!object.equals(instrans.get(i)))
                    return false;
            }
        }
        return true;
    }
    /**
     * 对象转换为字符串
     * @param obj
     * @return
     */
    public static String ObjToString(Object obj){
        return obj==null?"":obj.toString();
    }

    /**
     * 接地刀闸和所属设备状态是否一致
     * @param knifeStatus 接地刀闸状态
     * @param devStatus 所属设备状态
     * @return
     */
    public static String getGroudKinfeStatus(String devStatus){
        if("012".indexOf(devStatus)>=0)
            return "1";
        else
            return "0";
    }
    /**
     * 刀闸和所属设备状态是否一致
     * @param knifeStatus 刀闸状态
     * @param devStatus 所属设备状态
     * @return
     */
    public static String getKinfeStatus(String devStatus){
        if("01".indexOf(devStatus)>=0)
            return "0";
        else
            return "1";
    }

    public static String getSZ(String zwsz){
        char[] chs = new char[] { '零', '一', '二', '三', '四', '五', '六', '七', '八', '九' , '十' };
        for(int i = 0; i < chs.length; i++) {
            if(String.valueOf(chs[i]).equals(zwsz))
                return String.valueOf(i);
        }
        return "";
    }


    /**
     * 作用：数字转化为中文数字
     * @param num
     * @return
     */
    public static String getZWSZ(int num){
        String zwsz=""; //中文数字
        String shiw="";//十位上的数字
        String gew="";//个位上的数字
        if(num<10){
            switch(num){
                case 1 : zwsz="一" ;break;
                case 2 : zwsz="二"; break;
                case 3 : zwsz="三"; break;
                case 4 : zwsz="四"; break;
                case 5 : zwsz="五" ;break;
                case 6 : zwsz="六"; break;
                case 7 : zwsz="七"; break;
                case 8 : zwsz="八"; break;
                case 9 : zwsz="九" ;break;
            }
        }
        if(num>=10){
            String str = String.valueOf(num);
            if(str.trim().length()==2){
                String a=str.substring(0,1);
                String b=str.substring(1,2);
                switch(Integer.parseInt(a)){
                    case 1 : shiw="十" ;break;
                    case 2 : shiw="二"; break;
                    case 3 : shiw="三"; break;
                    case 4 : shiw="四"; break;
                    case 5 : shiw="五" ;break;
                    case 6 : shiw="六"; break;
                    case 7 : shiw="七"; break;
                    case 8 : shiw="八"; break;
                    case 9 : shiw="九" ;break;
                }
                switch(Integer.parseInt(b)){
                    case 1 : gew="一" ;break;
                    case 2 : gew="二"; break;
                    case 3 : gew="三"; break;
                    case 4 : gew="四"; break;
                    case 5 : gew="五" ;break;
                    case 6 : gew="六"; break;
                    case 7 : gew="七"; break;
                    case 8 : gew="八"; break;
                    case 9 : gew="九" ;break;
                    case 0 : gew="十" ;break;
                }
                zwsz=shiw+gew;
                if(zwsz.equals("十十")){
                    zwsz = zwsz.substring(0,1);
                }
            }
        }
        return zwsz;
    }

    /**
     * 作用：数字转化为中文数字
     * @param num
     * @return
     */
    public static String getTagState(String ch_tagstate){
        String tagstate=""; //数字状态
        if(ch_tagstate.equals("运行"))
            tagstate="1";
        if(ch_tagstate.equals("热备用"))
            tagstate="2";
        if(ch_tagstate.equals("冷备用"))
            tagstate="3";
        if(ch_tagstate.equals("检修"))
            tagstate="4";
        if(ch_tagstate.equals("倒母"))
            tagstate="7";
        if(ch_tagstate.equals("倒至正常运行方式"))
            tagstate="8";
        return tagstate;
    }

    /**
     * modifies the string to removes the extra whitespaces
     *
     * @param value
     *            the string to be modified
     * @return the modified string
     */
    public static String cleanTransformString(String value) {

        String val = new String(value);

        val = val.replaceAll("0\\s", "0,");
        val = val.replaceAll("1\\s", "1,");
        val = val.replaceAll("2\\s", "2,");
        val = val.replaceAll("3\\s", "3,");
        val = val.replaceAll("4\\s", "4,");
        val = val.replaceAll("5\\s", "5,");
        val = val.replaceAll("6\\s", "6,");
        val = val.replaceAll("7\\s", "7,");
        val = val.replaceAll("8\\s", "8,");
        val = val.replaceAll("9\\s", "9,");
        val = val.replaceAll("\\s*[,]\\s*[,]\\s*", ",");
        val = val.replaceAll("\\s+", "");

        return val;
    }

    public static String getStringValue(String srcStr, String paramName) {
        int index = srcStr.indexOf(paramName);
        if (index > -1) {
            srcStr = srcStr.substring(index + paramName.length());
            srcStr = srcStr.substring(srcStr.indexOf("(") + 1, srcStr
                    .indexOf(")"));

        }
        else
            srcStr = "";
        return srcStr;
    }

    /**
     * 此方法将给出的字符串source使用delim划分为字符串数组。
     *
     * @param source
     *            需要进行划分的原字符串
     * @param delim
     *            字符串的分隔字符串
     * @param trimTokens
     *            去掉每个分隔字符串空格
     * @param ignoreEmptyTokens
     *            忽略空的分隔字符串空格
     * @return 划分以后的数组，如果source为null的时候返回以source为唯一元素的数组， 如果delim为null则使用 ,
     *         作为分隔字符串。
     */
    public static String[] tokenize(String source, String delimiters,
                                    boolean trimTokens, boolean ignoreEmptyTokens) {
        List tokens = new ArrayList();

        if (source == null) {
            return new String[0];
        }
        if (delimiters == null) {
            delimiters = ",";
        }

        StringTokenizer st = new StringTokenizer(source, delimiters);

        while (st.hasMoreTokens()) {
            String token = st.nextToken();
            if (trimTokens) {
                token = token.trim();
            }
            if (!(ignoreEmptyTokens && token.length() == 0)) {
                tokens.add(token);
            }
        }
        return (String[]) tokens.toArray(new String[tokens.size()]);
    }

    /**
     * 判断字符串是否是数字
     * @param str
     * @return
     */
    public static boolean isNum(String str){
        if(str.equals("")){
            return false;
        }

        for (int i = str.length();--i>=0;){
            if (!Character.isDigit(str.charAt(i))){
                return false;
            }
        }
        return true;
    }

    public static boolean isDigit(String content) {
        boolean flag = false;
        Pattern p = Pattern.compile(".*\\d+.*");
        Matcher m = p.matcher(content);
        if (m.matches()) {
            flag = true;
        }
        return flag;
    }

    /**

     * 判断是否是字母

     * @param str 传入字符串

     * @return 是字母返回true，否则返回false

     */
    public static boolean isAlpha(String str) {
        if(str==null) return false;

        return str.matches("[a-zA-Z]+");
    }

    public static String replaceBd(String content){
        if(content==null) return "";

        if(content.length() == 0) return "";

        String[] filterStr=new String []{"。",";","；"};
        //是否有标点符号

        for (String string : filterStr) {
            if(content.contains(string)){
                content = content.replace(string, "");
            }
        }

        return content;
    }

    public static double checkSame(String Str_1,String Str_2) {
        Str_1 = Str_1.replace("\n", "");

        int Length1=Str_1.length();
        int Length2=Str_2.length();

        int Distance=0;
        if (Length1==0) {
            Distance=Length2;
        }
        if(Length2==0)
        {
            Distance=Length1;
        }
        if(Length1!=0&&Length2!=0){
            int[][] Distance_Matrix=new int[Length1+1][Length2+1];
            //编号
            int Bianhao=0;
            for (int i = 0; i <= Length1; i++) {
                Distance_Matrix[i][0]=Bianhao;
                Bianhao++;
            }
            Bianhao=0;
            for (int i = 0; i <=Length2; i++) {
                Distance_Matrix[0][i]=Bianhao;
                Bianhao++;
            }


            char[] Str_1_CharArray=Str_1.toCharArray();
            char[] Str_2_CharArray=Str_2.toCharArray();


            for (int i = 1; i <= Length1; i++) {
                for(int j=1;j<=Length2;j++){
                    if(Str_1_CharArray[i-1]==Str_2_CharArray[j-1]){
                        Distance=0;
                    }
                    else{
                        Distance=1;
                    }

                    int Temp1=Distance_Matrix[i-1][j]+1;
                    int Temp2=Distance_Matrix[i][j-1]+1;
                    int Temp3=Distance_Matrix[i-1][j-1]+Distance;

                    Distance_Matrix[i][j]=Temp1>Temp2?Temp2:Temp1;
                    Distance_Matrix[i][j]=Distance_Matrix[i][j]>Temp3?Temp3:Distance_Matrix[i][j];

                }

            }

            Distance=Distance_Matrix[Length1][Length2];
        }

        double Aerfa=1-1.0*Distance/(Length1>Length2?Length1:Length2);
        System.out.println("相似度:"+Aerfa);
        return Aerfa;
    }
}
