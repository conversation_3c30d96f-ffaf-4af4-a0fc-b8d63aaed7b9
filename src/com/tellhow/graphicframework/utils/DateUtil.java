/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tellhow.graphicframework.utils;


import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class DateUtil {

    public String getCurTime(String formats) {
        Calendar curDate = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat(formats);
        String curDateStr = format.format(curDate.getTime());
        return curDateStr;
    }
    public String getHistoryTime(Integer date,String formates){
        Calendar curDate=Calendar.getInstance();
        SimpleDateFormat format=new SimpleDateFormat(formates);
        curDate.add(curDate.DAY_OF_YEAR,-date);
        String curDateStr = format.format(curDate.getTime());
        return curDateStr;
   }  
    private Date today=new Date();
    @SuppressWarnings("deprecation")
	public String getYearTime(){
        Integer year;
        year=today.getYear()+1900;
        return year.toString();
    }
    @SuppressWarnings("deprecation")
	public String getMonthTime(){
        Integer month;
        month=today.getMonth()+1;
        return month.toString();
    }
    @SuppressWarnings("deprecation")
	public String getYesterdayTime(){
        String str_yesterday;
        Integer year,month,day,yesterday;
        year=today.getYear()+1900;
        month=today.getMonth()+1;
        day=today.getDate();
        yesterday=day-1;
        if(yesterday<1){
            if(month-1<1){
                year=year-1;
                str_yesterday=year.toString()+"-"+"12"+"-"+"31";
            }else{
                month=month-1;
                if(month==1||month==3||month==5||month==7||month==8||month==10){
                    str_yesterday=year.toString()+"-"+month.toString()+"-"+"31";
                }else if(month==4||month==6||month==9||month==11){
                    str_yesterday=year.toString()+"-"+month.toString()+"-"+"30";
                }else{
                    if(isRYear(year)){
                        str_yesterday=year.toString()+"-"+month.toString()+"-"+"29";
                    }else{
                        str_yesterday=year.toString()+"-"+month.toString()+"-"+"28";
                    }
                }
            }
        }else{
            str_yesterday=year.toString()+"-"+month.toString()+"-"+yesterday.toString();
        }
        
        return str_yesterday;
    }
    @SuppressWarnings("deprecation")
	public String getPreviousMonth(){
        String preMonth;
		@SuppressWarnings("unused")
		Integer year,month,day,yesterday;
        year=today.getYear()+1900;
        month=today.getMonth()+1;
        day=today.getDate();
        month=month-1;
            if(month-1<1){
                year=year-1;
                preMonth=year.toString()+"-"+"12"+"-"+"1";
            }else{
                preMonth=year.toString()+"-"+month.toString()+"-"+"1";
        }
        preMonth=preMonth+" "+getHourTime()+":"+getMinuteTime()+":"+getSecondTime();
        return preMonth;
    }
    @SuppressWarnings("deprecation")
	public String getTadayTime(){
        String str_taday;
        Integer year,month,day;
        year=today.getYear()+1900;
        month=today.getMonth()+1;
        day=today.getDate();
        str_taday=year.toString()+"-"+month.toString()+"-"+day.toString();
        return str_taday;
    }
    @SuppressWarnings("deprecation")
	public int getHourTime(){
        int hour=1;
        hour=today.getHours();
        return hour;
    }
    @SuppressWarnings("deprecation")
	public int getMinuteTime(){
        int minute=1;
        minute=today.getMinutes();
        return minute;
    }
    @SuppressWarnings("deprecation")
	public int getSecondTime(){
        int second=1;
        second=today.getSeconds();
        return second;
    }
    public static boolean isRYear(int year){
         if((year % 4 == 0)&&(year % 100 != 0)||(year % 400 == 0)){
             return true;
         }
         return false;
    }
}
