package com.tellhow.graphicframework.action;

import org.apache.batik.bridge.UpdateManager;
import org.beryl.gui.GUIException;
import org.beryl.gui.XMLUtils;
import org.w3c.dom.Element;
import org.w3c.dom.svg.SVGDocument;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

public abstract class AbstractSvgAction implements SvgAction {

	protected SVGDocument fSvgDocument; //�豸��Ӧ��SVG�ĵ�
	protected Element fGroupElement;//�������豸���Ԫ��
	protected Element fSvgGraphElement; //ͼ��Ԫ��
	protected PowerDevice fPowerDevice;//�������豸
	protected UpdateManager updateManager; //ͼ�β����̹߳�����
	protected final static String SVG_NAMESPACE_URI = org.apache.batik.dom.svg.SVGDOMImplementation.SVG_NAMESPACE_URI;
	protected final static String XLINK_NS = SystemConstants.XLINK_NS;
	protected SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
 
	
    public AbstractSvgAction(PowerDevice device) {
    	setPowerDevice(device);
    }
    
    public AbstractSvgAction(Element groupElement) {
    	setPowerDevice(groupElement);
    }
    
    public void setPowerDevice(PowerDevice pd) {
    	if (pd == null)
    		return;
    	fPowerDevice = pd;
    	fGroupElement = resolver.getDeviceGroupElement(pd);
    	if(fGroupElement==null&&pd.getDeviceType().equals(SystemConstants.InOutLine)//ȫ��ͼ��·��ȡ���⣨������Ӧ�ã�
    			&&!pd.getSwitchnum().equals("")
    			){
    		pd.setPowerDeviceID(pd.getSwitchnum());
    		pd.setCimID(pd.getSwitchnum());
    		fGroupElement = resolver.getDeviceGroupElement(pd);
    	}
    	if(fGroupElement==null)
    		return;
    	fSvgDocument =  (SVGDocument)fGroupElement.getOwnerDocument();
    	
    	fSvgGraphElement = resolver.getDeviceGraphElement(fGroupElement);
    	
    	updateManager = SystemConstants.getGuiBuilder().getUpdateManagerByDoc(fSvgDocument);
 //   	log.debug("Power device eLement" + dump(fSvgGraphElement));
    }
    
    public void setPowerDevice(Element groupElement) {
    	fSvgDocument =  (SVGDocument)groupElement.getOwnerDocument();
    	fGroupElement = groupElement;
    	fSvgGraphElement = resolver.getDeviceGraphElement(groupElement);
    	if(SystemConstants.getGuiBuilder() != null)
    		updateManager = SystemConstants.getGuiBuilder().getUpdateManagerByDoc(fSvgDocument);
    }
    
    public SVGDocument getSvgDocument() {
    	return fSvgDocument;
    }
    
    public Element getSvgGraphElement() {
    	return fSvgGraphElement;
    	
    }
      
    protected String dump(Element element) {
    	try {
			return XMLUtils.serializeXML(element);
		} catch (GUIException e) {
//			log.error(e.getMessage(), e);
			e.printStackTrace();
		}
		return null;
    }
    
    protected Element getLayer(String layerId) {
    	Element element = fSvgDocument.getElementById(layerId);
    	if (element == null) {
    		element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "g");
    		element.setAttribute("id", layerId);
    		Element root = resolver.resolveSvgElement(fSvgDocument);
    		if(root.hasAttribute("transform"))
    			element.setAttribute("transform", root.getAttribute("transform"));
    		resolver.resolveSvgElement(fSvgDocument).appendChild(element);
    	}
    	return element;
    }
    
	protected String getTransform(String transform, int offsetX, int offsetY) {
		String tempStr = transform.replace("translate(", "");
		tempStr = tempStr.replace(")", "");
		String[] points = tempStr.split(",");
		double x = Double.valueOf(points[0]) + offsetX; 
		double y = Double.valueOf(points[1]) + offsetY;
		return "translate(" + Double.toString(x)+ "," + Double.toString(y) + ")";
	}
	
	
}
