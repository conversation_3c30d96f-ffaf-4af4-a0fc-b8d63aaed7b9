package com.tellhow.graphicframework.action.impl;

import javax.swing.JOptionPane;

import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.action.SvgAction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;

/***
 * ��ӽӵ��߲���
 * <AUTHOR>
 *
 */
public class AddGroundLineAction extends AbstractSvgAction {
	
	private static final int GROUNDLINE_HEIGHT = 20;
	private static final int GROUNDLINE_WIDTH = 20;
	private static final String GROUNDLINE_LAYER = "GroundLine_Layer";
	private boolean isAttached = false;
	private String flagName;
	private int tagX, tagY;
	private int symbolWidth, symbolHeight;
	
	/**
	 * 
	 * @param device
	 * @param flagName flagName��Ӧ��tellhow/flag/icons��·���µ��ļ���
	 * @param isAttached
	 */
	public AddGroundLineAction(PowerDevice device, String flagName, boolean isAttached, int x, int y) {
		super(device);
		this.isAttached = isAttached;
		this.flagName = "0";
		if(this.flagName.equals("0")) {
			symbolWidth = 38;
	    	symbolHeight = 26;
		}
		else {
			symbolWidth = 24;
	    	symbolHeight = 36;
		}
		this.tagX = x;
		this.tagY = y;
	}
	
	public boolean execute() {
		Runnable r = new Runnable() {
	        public void run() {
	        	if (isAttached) {
	    			addSymbol(flagName);
	    			Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "use");
	    			element.setAttribute("x", String.valueOf(tagX));
	    			element.setAttribute("y", String.valueOf(tagY));
	    			element.setAttribute("width", String.valueOf(symbolWidth));
	    			element.setAttribute("height", String.valueOf(symbolHeight));
	    			element.setAttribute("class", "kV"+String.valueOf((int)fPowerDevice.getPowerVoltGrade()));
	    			element.setAttributeNS(XLINK_NS , "href",  "#" + flagName); 
	    			element.setAttribute("id", flagName + fPowerDevice.getPowerDeviceID());
	    			Element layer = getLayer(GROUNDLINE_LAYER);
	    		    layer.appendChild(element);
	    		} else {
	    			Element element  = fSvgDocument.getElementById(flagName + fPowerDevice.getPowerDeviceID());
	    			while (element != null) {
	    				getLayer(GROUNDLINE_LAYER).removeChild(element);
	    				element  = fSvgDocument.getElementById(flagName + fPowerDevice.getPowerDeviceID());
	    			}
	    		}
	        }
		};
		if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}
	
    protected void addSymbol(String symbolId) {
    	Element element = fSvgDocument.getElementById(symbolId);
    	if (element == null) {
    		NodeList list = fSvgDocument.getElementsByTagName("defs");
    		Element defElement = (Element)list.item(0);
    		if(this.flagName.equals("0"))
    			element = createSymbolElement(symbolId, GROUNDLINE_WIDTH, GROUNDLINE_HEIGHT); 
    		else
    			element = createSymbolElement2(symbolId, GROUNDLINE_WIDTH, GROUNDLINE_HEIGHT); 
    		defElement.appendChild(element);
    	}
    }
    
    protected Element createSymbolElement(String symbolId, int width, int height) {
    	Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "symbol");
    	element.setAttribute("id", symbolId);
    	element.setAttribute("viewBox", "0,0,"+symbolWidth+","+symbolHeight);
    	element.appendChild(createLineElement("5","14","10","14","1"));
    	element.appendChild(createLineElement("23","14","28","14","1"));
    	element.appendChild(createLineElement("28","22","28","6","1"));
    	element.appendChild(createLineElement("31","19","31","9","1"));
    	element.appendChild(createLineElement("34","16","34","12","1"));
    	element.appendChild(createLineElement("23","14","10","14","1"));
    	return element;
    }

    protected Element createSymbolElement2(String symbolId, int width, int height) {
    	Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "symbol");
    	element.setAttribute("id", symbolId);
    	element.setAttribute("viewBox", "0,0,"+symbolWidth+","+symbolHeight);
    	element.appendChild(createLineElement("10","4","10","9","1"));
    	element.appendChild(createLineElement("10","22","10","27","1"));
    	element.appendChild(createLineElement("2","27","18","27","1"));
    	element.appendChild(createLineElement("5","30","15","30","1"));
    	element.appendChild(createLineElement("8","33","12","33","1"));
    	element.appendChild(createLineElement("10","22","10","9","1"));
    	return element;
    }
    
    protected Element createLineElement(String x1,String y1,String x2,String y2,String stroke_width)
    {
    	Element line = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "line");
    	line.setAttribute("x1", x1);
    	line.setAttribute("y1", y1);
    	line.setAttribute("x2", x2);
    	line.setAttribute("y2", y2);
    	line.setAttribute("stroke-width", stroke_width);
    	return line;
    }

}