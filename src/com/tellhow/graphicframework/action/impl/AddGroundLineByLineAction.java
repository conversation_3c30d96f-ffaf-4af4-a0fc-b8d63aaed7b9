package com.tellhow.graphicframework.action.impl;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.utils.DOMUtil;
/**
 * ������·���ӵ���
 * */
public class AddGroundLineByLineAction extends AbstractSvgAction {

	private static final int GROUNDLINE_HEIGHT = 20;
	private static final int GROUNDLINE_WIDTH = 20;
	private static final String GROUNDLINE_LAYER = "GroundLine_Layer";
	private boolean isAttached;
	private String flagName;
	private int symbolWidth;
	private int symbolHeight;
	private double tagX;
	private double tagY;
	private String kid;
	private String pid;

	/**
	 * ��ӽӵ���
	 * 
	 * @param line
	 *            �ߵ�svgԪ��
	 * @param flagName
	 *            ���Ƿ�ֱ�ı�־
	 * @param isAttached
	 *            �ǹ��ϻ��ǲ��
	 * @param x
	 *            ���ϵ��x����
	 * @param y
	 *            ���ϵ��y����
	 * */
	public AddGroundLineByLineAction(Element line, String flagName,
			boolean isAttached, double x, double y,String pdID,String knifeId) {
		
		super(line);
		this.pid=pdID;
		this.kid=knifeId;
		//DOMUtil.writeXMLFile(fSvgDocument, "d:/start.svg");
		this.isAttached = isAttached;
		this.flagName = flagName;
		if (flagName.equals("0")) {
			this.symbolWidth = 38;
			this.symbolHeight = 26;
			//����ͼ�����λ��
			x=x-35;
			y=y-symbolHeight/2;
		} else if (flagName.equals("1")) {
			symbolWidth = 24;
			symbolHeight = 36;
			//����ͼ�����λ��
			y=y-5;
			x=x-symbolWidth/2;
		}else if (flagName.equals("2")) {
			this.symbolWidth = 38;
			this.symbolHeight = 26;
			//����ͼ�����λ��
			x=x-5;
			y=y-symbolHeight/2;
		}else if (flagName.equals("3")) {
			symbolWidth = 24;
			symbolHeight = 36;
			//����ͼ�����λ��
			y=y-5;
			x=x-symbolWidth/2;
		}
		this.tagX = x;
		this.tagY = y;
	}
	
	public AddGroundLineByLineAction(Element line, String flagName,
			boolean isAttached, int x, int y,String pid,String kid) {
		this(line, flagName, isAttached, (double)x, (double)y,pid,kid);
		
	}


	
	public boolean execute() {
		
		Runnable r = new Runnable() {
			public void run() {
			   
				drawGroundLine();

				//DOMUtil.writeXMLFile(fSvgDocument, "d:/end.svg");
			}
		};
		if (updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}

	public void drawGroundLine(){
		Element element;
		if (isAttached) {
			addSymbol("groundline_" + flagName);
			element = fSvgDocument.createElementNS(
					SVG_NAMESPACE_URI, "use");
			element.setAttribute("x", String.valueOf(tagX));
			element.setAttribute("y", String.valueOf(tagY));
			element.setAttribute("width", String.valueOf(symbolWidth));
			element
					.setAttribute("height", String
							.valueOf(symbolHeight));
			element.setAttribute("style", fSvgGraphElement.getAttribute("style")==null?"":fSvgGraphElement.getAttribute("style"));
			//element.setAttribute("stroke", fSvgGraphElement.getAttribute("stroke")==null?"":fSvgGraphElement.getAttribute("style"));
			element.setAttribute("class", fSvgGraphElement.getAttribute("class"));
			element.setAttributeNS(XLINK_NS, "href", "#groundline_" + flagName);
			element.setAttribute("id","groundline_"+pid+"-"+kid+"_"+flagName);
			
			Element layer = getLayer(GROUNDLINE_LAYER);
			Element ele = fSvgDocument.getElementById("groundline_"+pid+"-"+kid+"_"+flagName);
			 if(ele==null){
				 layer.appendChild(element);
			 }
		} else {
			 element = fSvgDocument.getElementById("groundline_"+pid+"-"+kid+"_"+flagName);
			 if(element!=null){
			 getLayer(GROUNDLINE_LAYER).removeChild(element);
			 }
			
		}
	}
	
	protected void addSymbol(String symbolId) {
		Element element = fSvgDocument.getElementById(symbolId);
		if (element == null) {
			NodeList list = fSvgDocument.getElementsByTagName("defs");
			Element defElement = (Element) list.item(0);
			if (this.flagName.equals("0"))
				element = createSymbolElement3(symbolId, GROUNDLINE_WIDTH,
						GROUNDLINE_HEIGHT);
			else
				element = createSymbolElement2(symbolId, GROUNDLINE_WIDTH,
						GROUNDLINE_HEIGHT);
			defElement.appendChild(element);
		}
	}
	/**
	 * ��
	 * */
	protected Element createSymbolElement(String symbolId, int width, int height) {
		Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI,
				"symbol");
		element.setAttribute("id", symbolId);
		element.setAttribute("viewBox", "0,0," + symbolWidth + ","
				+ symbolHeight);
		element.appendChild(createLineElement("5", "14", "10", "14", "1"));
		element.appendChild(createLineElement("23", "14", "28", "14", "1"));
		element.appendChild(createLineElement("28", "22", "28", "6", "1"));
		element.appendChild(createLineElement("31", "19", "31", "9", "1"));
		element.appendChild(createLineElement("34", "16", "34", "12", "1"));
		element.appendChild(createLineElement("23", "14", "10", "14", "1"));
		
		return element;
	}

	/**
	 * ��
	 * */
	protected Element createSymbolElement2(String symbolId, int width,
			int height) {
		Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI,
				"symbol");
		element.setAttribute("id", symbolId);
		element.setAttribute("viewBox", "0,0," + symbolWidth + ","
				+ symbolHeight);
		element.appendChild(createLineElement("10", "4", "10", "9", "1"));
		element.appendChild(createLineElement("10", "22", "10", "27", "1"));
		element.appendChild(createLineElement("2", "27", "18", "27", "1"));
		element.appendChild(createLineElement("5", "30", "15", "30", "1"));
		element.appendChild(createLineElement("8", "33", "12", "33", "1"));
		element.appendChild(createLineElement("10", "22", "10", "9", "1"));
		return element;
	}
	/**
	 * ��
	 * */
	protected Element createSymbolElement3(String symbolId, int width,
			int height) {
		Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI,
				"symbol");
		element.setAttribute("id", symbolId);
		element.setAttribute("viewBox", "0,0," + symbolWidth + ","
				+ symbolHeight);
		element.appendChild(createLineElement("10", "14", "14", "14", "1"));
		element.appendChild(createLineElement("14", "14", "24", "14", "1"));
		element.appendChild(createLineElement("24", "14", "34", "14", "1"));
		
		
		
		element.appendChild(createLineElement("10", "22", "10", "6", "1"));
		element.appendChild(createLineElement("7", "19", "7", "9", "1"));
		element.appendChild(createLineElement("4", "16", "4", "12", "1"));
		
		
		return element;
	}
	/**
	 * ��
	 * */
	protected Element createSymbolElement4(String symbolId, int width,
			int height) {
		Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI,
				"symbol");
		element.setAttribute("id", symbolId);
		element.setAttribute("viewBox", "0,0," + symbolWidth + ","
				+ symbolHeight);
		element.appendChild(createLineElement("10", "4", "10", "9", "1"));
		element.appendChild(createLineElement("10", "22", "10", "27", "1"));
		element.appendChild(createLineElement("2", "27", "18", "27", "1"));
		element.appendChild(createLineElement("5", "30", "15", "30", "1"));
		element.appendChild(createLineElement("8", "33", "12", "33", "1"));
		element.appendChild(createLineElement("10", "22", "10", "9", "1"));
		return element;
	}

	protected Element createLineElement(String x1, String y1, String x2,
			String y2, String stroke_width) {
		Element line = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "line");
		line.setAttribute("x1", x1);
		line.setAttribute("y1", y1);
		line.setAttribute("x2", x2);
		line.setAttribute("y2", y2);
		line.setAttribute("stroke-width", stroke_width);

		
		return line;
	}
}
