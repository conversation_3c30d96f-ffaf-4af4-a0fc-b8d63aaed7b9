package com.tellhow.graphicframework.action.impl;

import org.w3c.dom.Element;
import org.w3c.dom.Node;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.utils.DOMUtil;

/**
 * ����ı�ע��
 * <AUTHOR>
 *
 */
public class AttachAnnotationSvgAction extends AbstractSvgAction {

	private static final String TEXT_LAYER = "Text_Layer";
	private String annotation;
	private boolean isAttached = false;
	private int tagX, tagY;
	
	public AttachAnnotationSvgAction(PowerDevice device, String annotation, boolean isAttached, int x, int y) {
		super(device);
		this.isAttached = isAttached;
		this.annotation = annotation;
		this.tagX = x;
		this.tagY = y;
	}
	
	public boolean execute() {
		Runnable r = new Runnable() {
	        public void run() {
	        	if(isAttached) {
	    			Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "text");
	    			Node textNode = fSvgDocument.createTextNode(annotation);
	    			element.setAttribute("id", "ANNOTATION" + fPowerDevice.getPowerDeviceID());
	    			element.setAttribute("x", String.valueOf(tagX));
	    			element.setAttribute("y", String.valueOf(tagY));
	    			element.setAttribute("style", "font-size:20;font-family:Simsun;fill:rgb(0,255,255);stroke:rgb(45,229,229);");
	    			element.appendChild(textNode);
	    			Element layer = getLayer(TEXT_LAYER);
	    		    layer.appendChild(element);
	    		}
	    		else {
	    			Element element  = fSvgDocument.getElementById("ANNOTATION" + fPowerDevice.getPowerDeviceID());
	    			if (element != null) {
	    				getLayer(TEXT_LAYER).removeChild(element);
	    			}
	    		}
	        }
		};
		if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}

}
