package com.tellhow.graphicframework.action.impl;

import java.awt.geom.Point2D;
import java.util.List;
import java.util.UUID;

import javax.security.auth.Refreshable;

import org.apache.batik.dom.svg.SVGContext;
import org.apache.batik.dom.svg.SVGDOMImplementation;
import org.apache.batik.dom.svg.SVGOMTextElement;
import org.w3c.dom.DOMImplementation;
import org.w3c.dom.Document;
import org.w3c.dom.DocumentType;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.svg.SVGAnimationElement;
import org.w3c.dom.svg.SVGDocument;
import org.w3c.dom.svg.SVGLocatable;
import org.w3c.dom.svg.SVGRect;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.utils.DOMUtil;


/**
 * �豸��ѯ������ʾ����
 * <AUTHOR>
 *
 */
public class ChooseDeviceRectFlashingAction extends AbstractSvgAction {
	private Element rectangle;
	public ChooseDeviceRectFlashingAction(PowerDevice device) {
		super(device);
	}
	public boolean backexecute(){
//		if (fGroupElement == null)
//			return false;
		Runnable r = new Runnable() {
	        public void run() {
//	        	NodeList nl=fGroupElement.getElementsByTagName("rect");
//	        	Element el=(Element)nl.item(nl.getLength()-1);
//	        	el.getParentNode().removeChild(el);
	        	if(fSvgDocument!=null && fSvgDocument.getElementById("Flag_Layer") != null) {
	        		NodeList nl=fSvgDocument.getElementById("Flag_Layer").getElementsByTagName("rect");
		        	for(int i = 0; i < nl.getLength();i++){
		        		Element el=(Element)nl.item(i);
		        		if(el.getAttribute("id").equals("rectnewrect"+fPowerDevice.getPowerDeviceID())) {
		        			el.getParentNode().removeChild(el);
		        		}
			        	
		        	}
	        		
    			}

	        }
	    };
        if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}
	
	public boolean backAllExecute(){
//		if (fGroupElement == null)
//			return false;
		Runnable r = new Runnable() {
	        public void run() {
//	        	NodeList nl=fGroupElement.getElementsByTagName("rect");
//	        	Element el=(Element)nl.item(nl.getLength()-1);
//	        	el.getParentNode().removeChild(el);
	        	if(fSvgDocument!=null && fSvgDocument.getElementById("Flag_Layer") != null) {
	        		NodeList nl=fSvgDocument.getElementById("Flag_Layer").getElementsByTagName("rect");
		        	for(int i = 0; i < nl.getLength();i++){
		        		Element el=(Element)nl.item(i);
		        		el.getParentNode().removeChild(el);
		        	}
    			}

	        }
	    };
//        if(updateManager != null)
//			updateManager.getUpdateRunnableQueue().invokeLater(r);
//		else
			r.run();
		return true;
	}
	
	public boolean execute() {
		if (fGroupElement == null)
			return false;
		Runnable r = new Runnable() {
	        public void run() {
    			SVGRect bbox=null;
    			bbox = ((SVGLocatable)fGroupElement).getBBox();
    			float x =bbox.getWidth()==0?bbox.getX()-5:bbox.getX();
    			float y =bbox.getHeight()==0?bbox.getY()-5:bbox.getY();
    			float width=bbox.getWidth()==0?10:bbox.getWidth();
    			float height=bbox.getHeight()==0?10:bbox.getHeight();
    			String xa = String.valueOf(x);
    			String ya = String.valueOf(y);
    			String widtha = String.valueOf(width);
    			String heighta = String.valueOf(height);
    			String xb=String.valueOf(x-width*0.5);
    			String yb=String.valueOf(y-height*0.5);
    			String widthb=String.valueOf(2*width);
    			String heightb=String.valueOf(2*height);
    			
    			
    			rectangle = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "rect");
    			rectangle.setAttribute("id", "rectnewrect"+fPowerDevice.getPowerDeviceID());
    			rectangle.setAttribute("x",xb);
    			rectangle.setAttribute("y",yb);
    			rectangle.setAttribute("width",widthb);
    			rectangle.setAttribute("height",heightb);
    			rectangle.setAttribute("style", "stroke:rgb(85,255,255);stroke-width:2;stroke-dasharray:10,4;fill:none;fill-opacity:0;");
    			
    			//fGroupElement.appendChild(rectangle);
    			if(fSvgDocument.getElementById("Flag_Layer") == null) {
    				Element layer = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "g");
    				layer.setAttribute("id", "Flag_Layer");
    				fSvgDocument.getRootElement().appendChild(layer);
    			}
    			fSvgDocument.getElementById("Flag_Layer").appendChild(rectangle);

	        }
		};
		if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}
	
	//���������excute��ͬ��ֻ����ɫ��һ�����Ҽ������ɫ�Ͷ�ѡ��ɫ��һ��
	public boolean executeRight() {
		if (fGroupElement == null)
			return false;
		Runnable r = new Runnable() {
	        public void run() {
    			SVGRect bbox=null;
    			bbox = ((SVGLocatable)fGroupElement).getBBox();
    			float x =bbox.getWidth()==0?bbox.getX()-5:bbox.getX();
    			float y =bbox.getHeight()==0?bbox.getY()-5:bbox.getY();
    			float width=bbox.getWidth()==0?10:bbox.getWidth();
    			float height=bbox.getHeight()==0?10:bbox.getHeight();
    			String xa = String.valueOf(x);
    			String ya = String.valueOf(y);
    			String widtha = String.valueOf(width);
    			String heighta = String.valueOf(height);
    			String xb=String.valueOf(x-width*0.5);
    			String yb=String.valueOf(y-height*0.5);
    			String widthb=String.valueOf(2*width);
    			String heightb=String.valueOf(2*height);
    			
    			
    			rectangle = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "rect");
    			rectangle.setAttribute("id", "rectnewrect"+fPowerDevice.getPowerDeviceID());
    			rectangle.setAttribute("x",xb);
    			rectangle.setAttribute("y",yb);
    			rectangle.setAttribute("width",widthb);
    			rectangle.setAttribute("height",heightb);
//    			if(CBSystemConstants.isSame==false){
    				rectangle.setAttribute("style", "stroke:rgb(255,0,255);stroke-width:2;stroke-dasharray:10,4;fill:none;fill-opacity:0;");
//    			}else{
//    				rectangle.setAttribute("style", "stroke:red;stroke-width:2;stroke-dasharray:10,4;fill:none;fill-opacity:0;");
//    			}
    			
    			//fGroupElement.appendChild(rectangle);
    			if(fSvgDocument.getElementById("Flag_Layer") == null) {
    				Element layer = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "g");
    				layer.setAttribute("id", "Flag_Layer");
    				fSvgDocument.getRootElement().appendChild(layer);
    			}
    			fSvgDocument.getElementById("Flag_Layer").appendChild(rectangle);

	        }
		};
		if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}

}

