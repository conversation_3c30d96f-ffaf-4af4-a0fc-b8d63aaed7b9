package com.tellhow.graphicframework.action.impl;

import org.apache.batik.apps.svgbrowser.HistoryBrowserInterface.RemoveChildCommand;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.utils.Base64Util;
import com.tellhow.graphicframework.utils.DOMUtil;

/**
 * ���Ʋ���
 * <AUTHOR>
 *
 */
public class AttachFlagSvgAction extends AbstractSvgAction {
	
	private static final int FLAG_HEIGHT = 40;
	private static final int FLAG_WIDTH = 40;
	private static final String FLAG_LAYER = "Flag_Layer";
	private boolean isAttached = false;
	private String flagName;
	private double tagX, tagY;
	private String icon="check.png";
	
	/**
	 * 
	 * @param device
	 * @param flagName flagName��Ӧ��tellhow/flag/icons��·���µ��ļ���
	 * @param isAttached
	 */
	public AttachFlagSvgAction(PowerDevice device, String flagName, boolean isAttached, int x, int y) {
		super(device);
		this.isAttached = isAttached;
		this.flagName = flagName;
		this.tagX = x;
		this.tagY = y;
	}
	
	public AttachFlagSvgAction(Element groupElement, String flagName, boolean isAttached, int x, int y) {
		super(groupElement);
		this.isAttached = isAttached;
		this.flagName = flagName;
		this.tagX = x;
		this.tagY = y;
	}
	
	public AttachFlagSvgAction(Element groupElement, String flagName, boolean isAttached, double x, double y) {
		super(groupElement);
		this.isAttached = isAttached;
		this.flagName = flagName;
		this.tagX = x;
		this.tagY = y;
	}
	public AttachFlagSvgAction(Element pd, String flagName,
			boolean isAttached, double x, double y, String icon) {
		
		this(pd, flagName, isAttached, x, y);
		this.icon=icon;
	}

	public boolean execute() {
		if(fSvgGraphElement==null)
			return false;
		Runnable r = new Runnable() {
	        public void run() {
	        	if (isAttached) {
	    			if(fSvgDocument.getElementById(fGroupElement.getAttribute("id")+"_"+flagName)==null) {
		    			Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "use");
		    			findSymbol(flagName);
		    			element.setAttribute("x", String.valueOf(tagX));
		    			element.setAttribute("y", String.valueOf(tagY));
		    			element.setAttributeNS(XLINK_NS , "href",  "#" + flagName);	
		    			//element.setAttribute("transform", "scale(" + String.valueOf(0.7) + "," + String.valueOf(0.5) + ")");
		    			element.setAttribute("id", fGroupElement.getAttribute("id")+"_"+flagName);
		    			Element layer = getLayer(FLAG_LAYER);
		    		    layer.appendChild(element);
	    			}
	    		} else {
	    			Element element  = fSvgDocument.getElementById(fGroupElement.getAttribute("id")+"_"+flagName);
	    			if (element != null) {
	    				getLayer(FLAG_LAYER).removeChild(element);
	    			}
	    		}
	        }
		};
		if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}

    protected Element findSymbol(String symbolId) {
    	Element element = fSvgDocument.getElementById(symbolId);
    	if (element == null) {
    		NodeList list = fSvgDocument.getElementsByTagName("defs");
    		Element defElement = (Element)list.item(0);
    		element = createSymbolElement(symbolId, FLAG_WIDTH, FLAG_HEIGHT); 
    		defElement.appendChild(element);
    	}
    	return element; 
    }
    

    protected Element createSymbolElement(String symbolId, int width, int height) {
    	Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "symbol");
    	element.setAttribute("id", symbolId);
    	Element imageElement = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "image");
    	imageElement.setAttribute("x", "0");
    	imageElement.setAttribute("y", "0");
    	imageElement.setAttribute("width", String.valueOf(width));
    	imageElement.setAttribute("height", String.valueOf(height));
    	imageElement.setAttribute("transform", "scale(" + String.valueOf(0.7) + "," + String.valueOf(0.5) + ")");
    	imageElement.setAttributeNS(XLINK_NS, "href", "data:image/jpg;base64," + Base64Util.encode(Base64Util.class.getResourceAsStream("/tellhow/flag/icons/"+icon)));
    	element.appendChild(imageElement);
    	return element;
    }

}
