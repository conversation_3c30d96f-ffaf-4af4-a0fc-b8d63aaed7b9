package com.tellhow.graphicframework.action.impl;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.svg.SVGDocument;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.utils.DOMUtil;

public class ShowSvgLayerAction extends AbstractSvgAction {
	private String fLayerId;
	private boolean fShowOrHide;
	
	public ShowSvgLayerAction(Document document, String layerId, boolean showOrHide) {
		super(document.getElementById(layerId));
		fLayerId = layerId;
		fShowOrHide = showOrHide;
	}
	
	public boolean execute() {
		Runnable r = new Runnable() {
	        public void run() {
	        	Element element = fSvgDocument.getElementById(fLayerId);
	    		if (fShowOrHide) {
	    			if(fLayerId.equals(resolver.getHead_Layer()))
	    				fSvgGraphElement.setAttribute("fill", "rgb(0,0,0)");
	    			else if(DOMUtil.getStyleProperty(element, "display").equals("none"))
	    				DOMUtil.setStyleProperty(element, "display", "block");
	    		}
	    		else {
	    			if(fLayerId.equals(resolver.getHead_Layer()))
	    				fSvgGraphElement.setAttribute("fill", "rgb(255,255,255)");
	    			else if(!DOMUtil.getStyleProperty(element, "display").equals("none"))
	    				DOMUtil.setStyleProperty(element, "display", "none");
	    		}
	        }
		};
		if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else {
			r.run();
		}
		return true;
	}

}
