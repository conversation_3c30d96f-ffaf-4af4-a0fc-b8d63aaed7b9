package com.tellhow.graphicframework.action.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.algorithm.ElementSearch;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.utils.DOMUtil;

/**
 * �ı��豸��ɫ����
 * <AUTHOR>
 *
 */
public class ChangeColorAction extends AbstractSvgAction {

	private String fStrokeColor;
	private String fFillColor;
	
	public ChangeColorAction(PowerDevice device, String strokeColor, String fillColor) {
		super(device);
		this.fStrokeColor = strokeColor;
		this.fFillColor = fillColor;
	}
	
	/*���෽��
	public ChangeColorAction(PowerDevice device, String strokeColor, String fillColor,boolean isOff) {
		super(device);
		this.fStrokeColor = strokeColor;
		this.fFillColor = fillColor;
		if(!isOff){
			this.fStrokeColor = "rgb(0,255,0)";
			this.fFillColor = "none";
		}
		
	}
	*/
	
	public ChangeColorAction(Element groupElement, String strokeColor, String fillColor) {
		super(groupElement);
		this.fStrokeColor = strokeColor;
		this.fFillColor = fillColor;
	}
	public ChangeColorAction(Element groupElement, String strokeColor, String fillColor,boolean isOff) {
		super(groupElement);
		this.fStrokeColor = strokeColor;
		this.fFillColor = fillColor;
		if(!isOff){
			this.fStrokeColor = SystemConstants.getMapColor().get(SystemConstants.LOSE_COLOR_CODE);
			this.fFillColor = "none";
		}
	}

	public boolean execute() {
		final Element element = fGroupElement;
		if(element==null)
			return false;

			Runnable r = new Runnable() {
		        public void run() {
		        	setChildElementColor(element);
		        	
		        	fFillColor = "none";
		        	ElementSearch es = new ElementSearch();
		    		Map inMap = new HashMap();
		    		Map outMap = new HashMap();
		    		inMap.put("oprSrcElement", fGroupElement);
		    		inMap.put("searchType", "LinkLine");
		    		es.execute(inMap, outMap);
		    		ArrayList<Element> elementList = (ArrayList<Element>) outMap.get("ElementList");
		    		for(Element ele : elementList) {
	    				setChildElementColor(ele);
	    			}
		    		
		        }
			};
			if(updateManager != null)
				updateManager.getUpdateRunnableQueue().invokeLater(r);
			else 
				r.run();
	
		//dump(element);
		return false;
	}

	private void setChildElementColor(final Element element) {
		if(DOMUtil.getStyleProperty(element, "opacity").equals("0") && //���͸���ȵ���0˵���ýڵ�������Ӧ����¼������ܽ���ɫ��Ϊnone
				!DOMUtil.getStyleProperty(element, "fill").equals("none"))
			fFillColor = "white";
		DOMUtil.setStyleProperty(element, "stroke", fStrokeColor);
//		if(fFillColor.equals("none"))
//			DOMUtil.setStyleProperty(element, "stroke-width", "10");
		DOMUtil.setStyleProperty(element, "fill", fFillColor);
		NodeList nodeList = element.getChildNodes();
		for (int i = 0; i < nodeList.getLength(); i++) {
			Node node = nodeList.item(i);
			if (node instanceof Element) {
				setChildElementColor((Element)node);
			}
		}
	}
}
