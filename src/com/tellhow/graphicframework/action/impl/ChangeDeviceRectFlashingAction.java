package com.tellhow.graphicframework.action.impl;

import java.awt.geom.Point2D;
import java.util.UUID;

import javax.security.auth.Refreshable;

import org.apache.batik.dom.svg.SVGContext;
import org.apache.batik.dom.svg.SVGDOMImplementation;
import org.apache.batik.dom.svg.SVGOMTextElement;
import org.w3c.dom.DOMImplementation;
import org.w3c.dom.Document;
import org.w3c.dom.DocumentType;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.svg.SVGAnimationElement;
import org.w3c.dom.svg.SVGDocument;
import org.w3c.dom.svg.SVGLocatable;
import org.w3c.dom.svg.SVGRect;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.utils.DOMUtil;

/**
 * �豸��ѯ������ʾ����
 * <AUTHOR>
 *
 */
public class ChangeDeviceRectFlashingAction extends AbstractSvgAction {
	private String repeatCount; 
	private Element rectangle;
	public ChangeDeviceRectFlashingAction(PowerDevice device, String repeatCount) {
		super(device);
		this.repeatCount = repeatCount;
	}
	
	public ChangeDeviceRectFlashingAction(Element groupElement, String repeatCount) {
		super(groupElement);
		this.repeatCount = repeatCount;
	}

	public boolean backexecute(){
		if (fGroupElement == null)
			return false;
		Runnable r = new Runnable() {
	        public void run() {
	        	NodeList nl=fSvgDocument.getRootElement().getElementsByTagName("rect");
	        	Element el=(Element)nl.item(nl.getLength()-1);
	        	if(el!=null){
	        		el.getParentNode().removeChild(el);
	        	}else{
	        		return;
	        	}
	        }
	    };
        if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}
	
	public boolean execute() {
		if (fGroupElement == null)
			return false;
		Runnable r = new Runnable() {
	        public void run() {
        		
        		if(!repeatCount.equals("0")) {
        			SVGRect bbox=null;
        			bbox = ((SVGLocatable)fGroupElement).getBBox();
        			
        			if(bbox!=null){
        				float x =bbox.getWidth()==0?bbox.getX()-5:bbox.getX();
    	    			float y =bbox.getHeight()==0?bbox.getY()-5:bbox.getY();
    	    			float width=bbox.getWidth()==0?10:bbox.getWidth();
    	    			float height=bbox.getHeight()==0?10:bbox.getHeight();
    	    			String xa = String.valueOf(x);
    	    			String ya = String.valueOf(y);
    	    			String widtha = String.valueOf(width);
    	    			String heighta = String.valueOf(height);
    	    			String xb=String.valueOf(x-width);
    	    			String yb=String.valueOf(y-height);
    	    			String widthb=String.valueOf(3*width);
    	    			String heightb=String.valueOf(3*height);
    	    			

    	    			rectangle = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "rect");
    	    			rectangle.setAttribute("id", "rectnewrect");
    	    			rectangle.setAttribute("x",xb);
    	    			rectangle.setAttribute("y",yb);
    	    			rectangle.setAttribute("width",widthb);
    	    			rectangle.setAttribute("height",heightb);
    	    			rectangle.setAttribute("style", "stroke:yellow;stroke-width:2;stroke-dasharray:10,4;fill:black;fill-opacity:0;");
    	    			
    	    			fSvgDocument.getRootElement().appendChild(rectangle);
//    	    			((SVGAnimationElement)rectangle).beginElement();
    	    			
//    	    			Element element1=fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "animateTransform");
//    	    			element1.setAttribute("attributeName", "transform");
//    	    			element1.setAttribute("type", "translate");
//    	    			element1.setAttribute("from", "0");
//    	    			element1.setAttribute("to", xa);
//    	    			element1.setAttribute("dur", "2s");
//    	    			fGroupElement.appendChild(element1);
//    	    			((SVGAnimationElement)element1).beginElement();
    	    			
    	    			Element element2=fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "animate");
    	    			element2.setAttribute("attributeName", "display");
    	    			element2.setAttribute("from", "block");
    	    			element2.setAttribute("to", "none");
    	    			element2.setAttribute("dur", "0.5s");
    	    			element2.setAttribute("begin", "0s");
    	    			element2.setAttribute("repeatCount", "8");
    	    			element2.setAttribute("fill", "freeze");
    	    			rectangle.appendChild(element2);
    	    			((SVGAnimationElement)element2).beginElement();
    	    			
    	    			Element element3=fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "animate");
    	    			element3.setAttribute("attributeName", "x");
    	    			element3.setAttribute("from", xb);
    	    			element3.setAttribute("to", xa);
    	    			element3.setAttribute("dur", "4s");
    	    			element3.setAttribute("begin", "0s");
    	    			element3.setAttribute("fill", "freeze");
    	    			rectangle.appendChild(element3);
    	    			((SVGAnimationElement)element3).beginElement();
    	    	
    	    			Element element4=fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "animate");
    	    			element4.setAttribute("attributeName", "y");
    	    			element4.setAttribute("from", yb);
    	    			element4.setAttribute("to", ya);
    	    			element4.setAttribute("dur", "4s");
    	    			element4.setAttribute("begin", "0s");
    	    			element4.setAttribute("fill", "freeze");
    	    			rectangle.appendChild(element4);
    	    			((SVGAnimationElement)element4).beginElement();
    	    			
    	    			Element element5=fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "animate");
    	    			element5.setAttribute("attributeName", "width");
    	    			element5.setAttribute("from", widthb);
    	    			element5.setAttribute("to", widtha);
    	    			element5.setAttribute("dur", "4s");
    	    			element5.setAttribute("begin", "0s");
    	    			element5.setAttribute("fill", "freeze");
    	    			rectangle.appendChild(element5);
    	    			((SVGAnimationElement)element5).beginElement();
    	    			
    	    			Element element6=fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "animate");
    	    			element6.setAttribute("attributeName", "height");
    	    			element6.setAttribute("from", heightb);
    	    			element6.setAttribute("to", heighta);
    	    			element6.setAttribute("dur", "4s");
    	    			element6.setAttribute("begin", "0s");
    	    			element6.setAttribute("fill", "freeze");
    	    			rectangle.appendChild(element6);
    	    			((SVGAnimationElement)element6).beginElement();
        			}
        		}
	        }
		};
		if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}

}

