package com.tellhow.graphicframework.action.impl;

import org.w3c.dom.Attr;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.utils.DOMUtil;

/**
 * �豸�򿪺��ϲ���
 * <AUTHOR>
 *
 */
public class ChangeDeviceOffOnAction extends AbstractSvgAction {

	protected int fSwitchState = -1;
	
	/**
	 * 
	 * @param device
	 * @param state @see SWITCH_OFF | SWITCH_ON | SWITCH_HOT
	 */
	

	public ChangeDeviceOffOnAction(PowerDevice device, int state) {
		super(device);
		this.fSwitchState = state;
	}
	
	public ChangeDeviceOffOnAction(Element groupElement, int state) {
		super(groupElement);
		this.fSwitchState = state;
	}
	
	public boolean execute() {
        if(fSvgGraphElement==null || fSvgGraphElement.getAttributes().getNamedItemNS(XLINK_NS, "href") == null)
        	return false;
		//�õ�Sort����Ԫ���б�
		NodeList childList= fGroupElement.getChildNodes();  
		for(int j=0;j<childList.getLength();j++){
		    Node child = childList.item(j);
		    if (child instanceof Element){
		    	final Element element = (Element)child;
				    
		    	if(element.getAttributes().getNamedItemNS(XLINK_NS, "href") != null){
		    		String str= element.getAttributes().getNamedItemNS(XLINK_NS, "href").getNodeValue();
		    		final String flag = getStateFlag(str);
		    		Runnable r = new Runnable() {
		    	        public void run() {
		    	        	element.getAttributes().getNamedItemNS(XLINK_NS, "href").setNodeValue(flag);
		    	        }
		    		};
		    		if(updateManager != null)
		    			updateManager.getUpdateRunnableQueue().invokeLater(r);
		    		else
		    			r.run();
		    	}
		    }
	    }

		return true;
	}

	private String getStateFlag(String str) {
		
		String srcHref = fSvgGraphElement.getAttributes().getNamedItemNS(XLINK_NS, "href").getNodeValue();
		if(!str.equals("")){
			srcHref=str;
		}
		String deviceHref = srcHref.replace("-UnNor", "_");//����OPEN3000SVGͼ�μ���
		if(deviceHref.length() > 2 && deviceHref.lastIndexOf("_") == deviceHref.length()-2) {
			deviceHref = deviceHref.substring(0, deviceHref.lastIndexOf("_"));
			String on = "_1";
			String off = "_0";
			String symbolID = "";
			switch (fSwitchState) {
				case SWITCH_ON: {
					//DOMUtil.setStyleProperty(fSvgGraphElement, "stroke-width", "2");
					if(deviceHref.contains("DollyBreaker") &&
							DOMUtil.getSymbolByID(fSvgGraphElement, deviceHref.concat("_3").replace("#", "")) != null)
						symbolID = deviceHref.concat("_3");
					else if(deviceHref.contains("#JS_�ֳ�����") &&
							DOMUtil.getSymbolByID(fSvgGraphElement, deviceHref.concat("_3").replace("#", "")) != null)
						symbolID = deviceHref.concat("_3");
					else if(deviceHref.contains("#XJ_С������") &&
							DOMUtil.getSymbolByID(fSvgGraphElement, deviceHref.concat("_3").replace("#", "")) != null)
						symbolID = deviceHref.concat("_3");
					else if(deviceHref.contains("#nc_С������") &&
							DOMUtil.getSymbolByID(fSvgGraphElement, deviceHref.concat("_3").replace("#", "")) != null)
						symbolID = deviceHref.concat("_3");
					else
						symbolID = deviceHref.concat(on);
					break;
				}
				case SWITCH_HOT: {
					//DOMUtil.setStyleProperty(fSvgGraphElement, "stroke-width", "2");
					//symbolID = deviceHref.concat(off);
					if(deviceHref.contains("DollyBreaker") &&
							DOMUtil.getSymbolByID(fSvgGraphElement, deviceHref.concat("_3").replace("#", "")) != null)
						symbolID = deviceHref.concat("_3");
					else if(deviceHref.contains("#JS_�ֳ�����") &&
							DOMUtil.getSymbolByID(fSvgGraphElement, deviceHref.concat("_3").replace("#", "")) != null)
						symbolID = deviceHref.concat("_1");
					else if(deviceHref.contains("#XJ_С������") &&
							DOMUtil.getSymbolByID(fSvgGraphElement, deviceHref.concat("_3").replace("#", "")) != null)
						symbolID = deviceHref.concat("_1");
					else if(deviceHref.contains("#nc_С������") &&
							DOMUtil.getSymbolByID(fSvgGraphElement, deviceHref.concat("_3").replace("#", "")) != null)
						symbolID = deviceHref.concat("_3");
					else
						symbolID = deviceHref.concat(off);
					break;
				}
				case SWITCH_OFF: {
					//DOMUtil.setStyleProperty(fSvgGraphElement, "stroke-width", "2");
					symbolID = deviceHref.concat(off);
					break;
				}
				default: {
					//DOMUtil.setStyleProperty(fSvgGraphElement, "stroke-width", "2");
					symbolID = deviceHref.concat(off);
					break;
				}
			}
			Element ele = DOMUtil.getSymbolByID(fSvgGraphElement, symbolID.replace("#", ""));
			if(ele != null)
				return symbolID;
			else
				return srcHref;
		}else if(deviceHref.contains(".") && 
				(deviceHref.toLowerCase().contains("open")||deviceHref.toLowerCase().contains("close")||deviceHref.contains("UnNor"))
					) {//����E8000ͼ�μ���
			deviceHref = deviceHref.substring(0, deviceHref.lastIndexOf(".")+1);
			String on = "Close";
			String off = "Open";
			String symbolID = "";
			switch (fSwitchState) {
				case SWITCH_ON: {
					symbolID = deviceHref.concat(on);
					break;
				}
				case SWITCH_HOT: {
					symbolID = deviceHref.concat(off);
					break;
				}
				case SWITCH_OFF: {
					symbolID = deviceHref.concat(off);
					break;
				}
				default: {
					symbolID = deviceHref.concat(off);
					break;
				}
			}
			Element ele = DOMUtil.getSymbolByID(fSvgGraphElement, symbolID.replace("#", ""));
			if(ele != null)
				return symbolID;
			else
				return srcHref;
		}
		return deviceHref;
	}

}
