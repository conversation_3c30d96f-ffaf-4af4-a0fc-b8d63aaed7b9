package com.tellhow.graphicframework.action.impl;

import java.awt.geom.Point2D;
import java.util.UUID;

import org.apache.batik.bridge.UpdateManager;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.svg.SVGAnimationElement;

import com.tellhow.graphicframework.action.AbstractSvgAction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.utils.DOMUtil;

/**
 * �豸��˸����
 * <AUTHOR>
 *
 */
public class ChangeDeviceFlashingAction extends AbstractSvgAction {
	private String repeatCount; 
	public ChangeDeviceFlashingAction(PowerDevice device, String repeatCount) {
		super(device);
		this.repeatCount = repeatCount;
	}
	
	public ChangeDeviceFlashingAction(Element groupElement, String repeatCount) {
		super(groupElement);
		this.repeatCount = repeatCount;
	}

	public boolean execute() {
		if (fGroupElement == null)
			return false;
		Runnable r = new Runnable() {
	        public void run() {
        		NodeList nodelist = fGroupElement.getElementsByTagNameNS(SVG_NAMESPACE_URI, "animate");
        		if(nodelist.getLength() > 0) {
	    			for(int i = nodelist.getLength()-1; i >=0; i--) {
	    				fGroupElement.removeChild(nodelist.item(i));
	    			}
        		}
        		if(!repeatCount.equals("0")) {
	    			Element element = fSvgDocument.createElementNS(SVG_NAMESPACE_URI, "animate");
	    			element.setAttribute("id", UUID.randomUUID().toString());
	    			element.setAttribute("attributeName", "display");
	    			element.setAttribute("from", "none");
	    			element.setAttribute("to", "block");
//	    			element.setAttribute("attributeName", "opacity");
//	    			element.setAttribute("from", "0");
//	    			element.setAttribute("to", "1");
	    			element.setAttribute("dur", "1s");
	    			element.setAttribute("begin", "0s");
	    			element.setAttribute("repeatCount", repeatCount);
	    			element.setAttribute("fill", "freeze");
	    			fGroupElement.appendChild(element);
	    			((SVGAnimationElement)element).beginElement();
        		}
	        }
		};
		if(updateManager != null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		return true;
	}

}
