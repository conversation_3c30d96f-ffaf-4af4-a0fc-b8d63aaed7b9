package com.tellhow.graphicframework.basic;
/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2012-2-22 ����11:43:06 
 */
public class LinkEquipVO {
	private String s_id;

	private int s_touchLoaction;

	private int s_linkLSH;

	private String t_id;

	private int t_touchLoaction;
	
	private String t_sub_id;

	private int t_sub_touchLoaction;

	public String getT_id() {
		return t_id;
	}

	public void setT_id(String t_id) {
		this.t_id = t_id;
	}

	public int getS_linkLSH() {
		return s_linkLSH;
	}

	public void setS_linkLSH(int s_linklsh) {
		s_linkLSH = s_linklsh;
	}

	public String getNodeName() {
		return "Pin" + s_touchLoaction + "InfoVect" + s_linkLSH + "LinkObjId";
	}

	public int getS_touchLoaction() {
		return s_touchLoaction;
	}

	public void setS_touchLoaction(int loaction) {
		s_touchLoaction = loaction;
	}

	public int getT_touchLoaction() {
		return t_touchLoaction;
	}

	public void setT_touchLoaction(int loaction) {
		t_touchLoaction = loaction;
	}

	public String getS_id() {
		return s_id;
	}

	public void setS_id(String s_id) {
		this.s_id = s_id;
	}

	public String getT_sub_id() {
		return t_sub_id;
	}

	public void setT_sub_id(String t_sub_id) {
		this.t_sub_id = t_sub_id;
	}

	public int getT_sub_touchLoaction() {
		return t_sub_touchLoaction;
	}

	public void setT_sub_touchLoaction(int loaction) {
		t_sub_touchLoaction = loaction;
	}

}

