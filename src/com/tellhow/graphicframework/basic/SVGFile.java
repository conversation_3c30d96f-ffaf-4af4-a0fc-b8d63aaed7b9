package com.tellhow.graphicframework.basic;


/**
 *
 * <AUTHOR>
 */
public class SVGFile {

    private String fileName = "";
    private String filePath = "";
    private String mapType = "";
    private String stationID = "";
    private String lineID = "";
    private boolean isAlwaysOpen = false;   //�Ƿ�������ʱ����
    private boolean isDefault = false;
    private long lastModified;
    
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public String getFilePath() {
		return filePath;
	}
	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}
	public String getMapType() {
		return mapType;
	}
	public void setMapType(String mapType) {
		this.mapType = mapType;
	}
	public String getStationID() {
		return stationID;
	}
	public void setStationID(String stationID) {
		this.stationID = stationID;
	}
	
	public String getLineID() {
		return lineID;
	}
	public void setLineID(String lineID) {
		this.lineID = lineID;
	}
	public boolean isAlwaysOpen() {
		return isAlwaysOpen;
	}
	public void setAlwaysOpen(boolean isAlwaysOpen) {
		this.isAlwaysOpen = isAlwaysOpen;
	}
	
	public boolean isDefault() {
		return isDefault;
	}
	public void setDefault(boolean isDefault) {
		this.isDefault = isDefault;
	}
	
	public long getLastModified() {
		return lastModified;
	}
	public void setLastModified(long lastModified) {
		this.lastModified = lastModified;
	}
	@Override
    public String toString() {
        return fileName;
    }
}
