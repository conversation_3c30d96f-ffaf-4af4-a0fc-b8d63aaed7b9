package com.tellhow.graphicframework.basic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.tellhow.graphicframework.utils.DOMUtil;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2012-2-24 ����03:44:52 
 */
public class LineLinkCache {
	private Map<String, Map<String, LinkEquipVO>> equipLinkMap = new HashMap<String, Map<String, LinkEquipVO>>();

	public Map<String, Map<String, LinkEquipVO>> getEquipLinkMap() {
		return equipLinkMap;
	}

	public void setEquipLinkMap(
		Map<String, Map<String, LinkEquipVO>> equipLinkMap) {
		this.equipLinkMap = equipLinkMap;
	}

	/**
	 * �õ��豸ͼԪ���ӵ���ͼԪ,��ͼԪIDΪs_id
	 * 
	 * @param equip_id
	 * @return
	 */
	public List<LinkEquipVO> getLinkLineForEquip(String equip_id) {
		List<LinkEquipVO> list = new ArrayList<LinkEquipVO>();
		for (Iterator iter = equipLinkMap.entrySet().iterator(); iter.hasNext();) {
			Map.Entry entry = (Map.Entry) iter.next();
			Map<String, LinkEquipVO> map = (Map<String, LinkEquipVO>) entry
				.getValue();
			if (map.containsKey(equip_id)) {
				list.add(map.get(equip_id));
			}
		}
		return list;
	}

	public List<LinkEquipVO> getLinkLineForSubEquip(String equip_id,
		String sub_equip_id) {
		List<LinkEquipVO> list = new ArrayList<LinkEquipVO>();
		for (Iterator iter = equipLinkMap.entrySet().iterator(); iter.hasNext();) {
			Map.Entry entry = (Map.Entry) iter.next();
			Map<String, LinkEquipVO> map = (Map<String, LinkEquipVO>) entry
				.getValue();
			if (map.containsKey(equip_id)) {
				LinkEquipVO linkEquipVO = map.get(equip_id);
				if (linkEquipVO.getT_sub_id() != null
					&& linkEquipVO.getT_sub_id().equals(sub_equip_id)) {
					list.add(linkEquipVO);
				}

			}
		}
		return list;
	}

	public List<LinkEquipVO> getLinkLineForEquip(String equip_id,
		int touchLocation) {
		List<LinkEquipVO> list = new ArrayList<LinkEquipVO>();
		for (Iterator iter = equipLinkMap.entrySet().iterator(); iter.hasNext();) {
			Map.Entry entry = (Map.Entry) iter.next();
			Map<String, LinkEquipVO> map = (Map<String, LinkEquipVO>) entry
				.getValue();
			if (map.containsKey(equip_id)) {
				LinkEquipVO linkEquipVO = map.get(equip_id);
				if (linkEquipVO.getT_touchLoaction() == touchLocation) {
					list.add(linkEquipVO);
				}
			}
		}
		return list;
	}

	/**
	 * �õ�������ͼԪ���ӵ��豸����ͼԪ
	 * 
	 * @param line_id
	 * @return
	 */
	public List<LinkEquipVO> getLinkEquipForLine(String line_id) {
		List<LinkEquipVO> list = new ArrayList<LinkEquipVO>();
		if (equipLinkMap.containsKey(line_id)) {
			Map<String, LinkEquipVO> map = equipLinkMap.get(line_id);
			for (Iterator iter = map.entrySet().iterator(); iter.hasNext();) {
				Map.Entry entry = (Map.Entry) iter.next();
				list.add((LinkEquipVO) entry.getValue());
			}
		}
		return list;

	}

	public List<LinkEquipVO> getLinkEquipForLine(String line_id,
		int touchLocation) {
		List<LinkEquipVO> list = new ArrayList<LinkEquipVO>();
		if (equipLinkMap.containsKey(line_id)) {
			Map<String, LinkEquipVO> map = equipLinkMap.get(line_id);
			for (Iterator iter = map.entrySet().iterator(); iter.hasNext();) {
				Map.Entry entry = (Map.Entry) iter.next();
				LinkEquipVO linkEquipVO = (LinkEquipVO) entry.getValue();
				if (linkEquipVO.getS_touchLoaction() == touchLocation) {
					list.add(linkEquipVO);
				}
			}
		}
		return list;

	}
	
	public List<LinkEquipVO> getLinkEquipForLine(Document doc, String line_id, List<String> searchedLine) {
		List<LinkEquipVO> LinkEquipVOList = new ArrayList<LinkEquipVO>();
		if(searchedLine == null)
			searchedLine = new ArrayList<String>();
		searchedLine.add(line_id);
		searchLinkEquipForLine(doc, LinkEquipVOList, searchedLine, line_id);
		return LinkEquipVOList;

	}
	
	public void searchLinkEquipForLine(Document doc, List<LinkEquipVO> LinkEquipVOList, List<String> searchedLine, String line_id) {
		if (equipLinkMap.containsKey(line_id)) {
			Map<String, LinkEquipVO> map = equipLinkMap.get(line_id);
			for (Iterator iter = map.entrySet().iterator(); iter.hasNext();) {
				Map.Entry entry = (Map.Entry) iter.next();
				LinkEquipVO linkEquipVO = (LinkEquipVO) entry.getValue();
				String elementID = (String) entry.getKey();
				if(searchedLine.contains(elementID) || doc.getElementById(elementID)==null)
					continue;
				else if(isLinkLineNode(doc.getElementById(elementID))) {
					searchedLine.add(elementID);
					searchLinkEquipForLine(doc, LinkEquipVOList, searchedLine, elementID);
				}
				else if(isPathNode(doc.getElementById(elementID))) {
					if(!isLinkEquipExist(LinkEquipVOList, linkEquipVO.getT_id()))
						LinkEquipVOList.add(linkEquipVO);
					List<LinkEquipVO> linkLineVOList = getLinkLineForEquip(elementID);
					for (int i = 0; i < linkLineVOList.size(); i++) {
						String lineElementID = linkLineVOList.get(i).getS_id();
						if(searchedLine.contains(lineElementID))
							continue;
						searchedLine.add(lineElementID);
						searchLinkEquipForLine(doc, LinkEquipVOList, searchedLine, lineElementID);
					}
				}
				else {
					if(!isLinkEquipExist(LinkEquipVOList, linkEquipVO.getT_id()))
						LinkEquipVOList.add(linkEquipVO);
				}
			}
		}

	}
	
	public static Boolean isPathNode(Element element) {
		if (element.getElementsByTagName("path").getLength() > 0)
			return true;
		else if (DOMUtil.getEquipID(element).equals(""))
			return true;
		else
			return false;
	}

	public static Boolean isLinkLineNode(Element element) {
		if (element.getElementsByTagName("cge:PSR_Link").getLength() > 0)
			return true;
		else
			return false;
	}
	
	public boolean isLinkEquipExist(List<LinkEquipVO> LinkEquipVOList, String equipElementID) {
		boolean isExist = false;
		for (int i = 0; i < LinkEquipVOList.size(); i++) {
			String elementID = LinkEquipVOList.get(i).getT_id();
			if(elementID.equals(equipElementID)) {
				isExist = true;
				break;
			}
		}
		return isExist;
	}

	public LineLinkEquip getLineLinkEquipForLine(String line_id) {
		List<LinkEquipVO> list = getLinkEquipForLine(line_id);
		LineLinkEquip lineLinkEquip = new LineLinkEquip();
		for (int i = 0; i < list.size(); i++) {
			lineLinkEquip.addLinkEquipVO(list.get(i));
		}
		return lineLinkEquip;
	}

	public boolean isLine(String line_id) {
		return equipLinkMap.containsKey(line_id);
	}

	public LineLinkCache clone() {
		LineLinkCache lineLinkCache = new LineLinkCache();
		Map<String, Map<String, LinkEquipVO>> newEquipLinkMap = new HashMap<String, Map<String, LinkEquipVO>>();
		for (Iterator iter = equipLinkMap.entrySet().iterator(); iter.hasNext();) {
			Map.Entry entry = (Map.Entry) iter.next();
			String id = (String) entry.getKey();
			Map<String, LinkEquipVO> map = (Map) entry.getValue();
			Map<String, LinkEquipVO> newMap = new HashMap();
			newMap.putAll(map);
			newEquipLinkMap.put(id, newMap);
		}
		lineLinkCache.setEquipLinkMap(newEquipLinkMap);
		return lineLinkCache;
	}

}
