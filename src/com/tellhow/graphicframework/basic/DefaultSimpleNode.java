/**
 * ��Ȩ���� : ̩������ɷ����޹�˾��Ȩ����
 * �� Ŀ �� ������Ʊϵͳ
 * ����˵�� : �Զ������Ľڵ�
 * ��    �� : ����ƽ
 * �������� : 2008-07-16
 * �޸����� ��
 * �޸�˵�� ��
 * �� �� �� ��
 **/
package com.tellhow.graphicframework.basic;

public class DefaultSimpleNode {

    private String itemCode = "";
    private String itemName = "";
    private String itemparam1= "";
    private String itemparam2= "";

    public String getItemCode() {
        return itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public String getItemparam1() {
        return itemparam1;
    }

    public String getItemparam2() {
        return itemparam2;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public void setItemparam1(String itemparam1) {
        this.itemparam1 = itemparam1;
    }

    public void setItemparam2(String itemparam2) {
        this.itemparam2 = itemparam2;
    }

    
    

    @Override
	public String toString() {
        return itemName;
    }
}
