package com.tellhow.graphicframework.basic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2012-2-22 ����11:45:49 
 */
public class LineLinkEquip {

	private Map<Integer, List<LinkEquipVO>> linkEquipVOMap = new HashMap<Integer, List<LinkEquipVO>>();

	public void addLinkEquipVO(LinkEquipVO linkEquipVO) {
		List<LinkEquipVO> list = null;
		if (linkEquipVOMap.containsKey(linkEquipVO.getS_touchLoaction())) {
			list = linkEquipVOMap.get(linkEquipVO.getS_touchLoaction());
		} else {
			list = new ArrayList<LinkEquipVO>();
			linkEquipVOMap.put(linkEquipVO.getS_touchLoaction(), list);
		}
		list.add(linkEquipVO);
	}

	public boolean isBeginPointLinked() {
		if (linkEquipVOMap.containsKey(0) && linkEquipVOMap.get(0).size() > 0) {
			return true;
		} else {
			return false;
		}
	}

	public List<LinkEquipVO> getBeginLinkEquipVOList() {
		List<LinkEquipVO> list = new ArrayList<LinkEquipVO>();
		if (linkEquipVOMap.containsKey(0)) {
			list.addAll(linkEquipVOMap.get(0));
		}
		return list;
	}

	public List<LinkEquipVO> getEndLinkEquipVOList() {
		List<LinkEquipVO> list = new ArrayList<LinkEquipVO>();
		if (linkEquipVOMap.containsKey(1)) {
			list.addAll(linkEquipVOMap.get(1));
		}
		return list;
	}

	public List<LinkEquipVO> getLinkEquipVOList() {
		List<LinkEquipVO> list = getBeginLinkEquipVOList();
		list.addAll(getEndLinkEquipVOList());
		return list;
	}

	public boolean isEndPointLinked() {
		if (linkEquipVOMap.containsKey(1) && linkEquipVOMap.get(1).size() > 0) {
			return true;
		} else {
			return false;
		}
	}

	public boolean isLinked() {
		if (linkEquipVOMap.size() > 0) {
			return true;
		} else {
			return false;
		}
	}

	public Integer getNextCanUseLinkLSH(Integer linkLoaction) {
		int nextCanUseLinkLSH = 0;
		if (linkEquipVOMap.containsKey(linkLoaction)) {
			List<LinkEquipVO> list = linkEquipVOMap.get(linkLoaction);
			Map<Integer, Integer> map = new HashMap<Integer, Integer>();
			int maxLSH = 0;
			for (int i = 0; i < list.size(); i++) {
				int s_linkLSH = list.get(i).getS_linkLSH();
				map.put(s_linkLSH, null);
				if (maxLSH < s_linkLSH) {
					maxLSH = s_linkLSH;
				}
			}
			for (int i = 0; i < maxLSH + 1; i++) {
				if (map.containsKey(i)) {
					continue;
				} else {
					nextCanUseLinkLSH = i;
					break;
				}
			}
			if (nextCanUseLinkLSH==0){
				nextCanUseLinkLSH++;
			}
		} else {
			nextCanUseLinkLSH = 0;
		}
		return nextCanUseLinkLSH;
	}
	
	public LinkEquipVO getLinkEquipVOByID(String linkEquipID) {

       Set<Entry<Integer, List<LinkEquipVO>>> set = linkEquipVOMap.entrySet();
		
		Iterator<Entry<Integer, List<LinkEquipVO>>> iter = set.iterator();
		
		while (iter.hasNext()) {
			
			Entry<Integer, List<LinkEquipVO>> entry=iter.next();
			
			List<LinkEquipVO> linkEquioList=entry.getValue();
			
			for(int i=0;i<linkEquioList.size();i++) {
				
				LinkEquipVO linkEquipVO=linkEquioList.get(i);
				if(linkEquipVO.getS_id()==linkEquipID) {
					return linkEquipVO;
				}
				
			}		
		}	
		return null;
	}
	
	

	public static void main(String[] args) {
		Pattern p = Pattern.compile("Pin\\d+InfoVect\\d+LinkObjId");
		String temp = "Pin0InfoVect1LinkObjId";
		Matcher m = p.matcher(temp);
		if (m.find()) {
			Pattern p1 = Pattern.compile("\\d+");
			Matcher m1 = p1.matcher(temp);
//			while (m1.find()) {
//				String firstNumber = m1.group();
//				System.out.println(firstNumber);
//			}
			for (int j = 0; j < 2; j++) {
				if (m1.find() && j == 0) {
					String firstNumber = m1.group();
				//	System.out.println(firstNumber);
					continue;
				}
				if (m1.find() && j == 1) {
					String secondNumber = m1.group();
				//	System.out.println(secondNumber);
					continue;
				}
			}
			
		}
	}

}
