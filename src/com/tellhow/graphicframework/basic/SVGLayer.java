package com.tellhow.graphicframework.basic;
/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2012-3-2 ����5:11:37 
 */
public class SVGLayer {

	private String layerID = "";
	private String layerName = "";
	private boolean isVisiable = true;
	private boolean isHandleEvent = true;
	private boolean isRefresh = false;
	private int refreshInterval = 0;
	
	public SVGLayer(String layerID) {
		this.layerID = layerID;
		this.layerName = layerID;
	}
	
	public SVGLayer(String layerID, String layerName, boolean isVisiable,
			boolean isHandleEvent, boolean isRefresh, int refreshInterval) {
		super();
		this.layerID = layerID;
		if(layerName.equals(""))
			this.layerName = layerID;
		else
			this.layerName = layerName;
		this.isVisiable = isVisiable;
		this.isHandleEvent = isHandleEvent;
		this.isRefresh = isRefresh;
		this.refreshInterval = refreshInterval;
	}
	public String getLayerID() {
		return layerID;
	}
	public void setLayerID(String layerID) {
		this.layerID = layerID;
	}
	public String getLayerName() {
		return layerName;
	}
	public void setLayerName(String layerName) {
		this.layerName = layerName;
	}
	public boolean isVisiable() {
		return isVisiable;
	}
	public void setVisiable(boolean isVisiable) {
		this.isVisiable = isVisiable;
	}
	public boolean isHandleEvent() {
		return isHandleEvent;
	}
	public void setHandleEvent(boolean isHandleEvent) {
		this.isHandleEvent = isHandleEvent;
	}
	public boolean isRefresh() {
		return isRefresh;
	}
	public void setRefresh(boolean isRefresh) {
		this.isRefresh = isRefresh;
	}
	public int getRefreshInterval() {
		return refreshInterval;
	}
	public void setRefreshInterval(int refreshInterval) {
		this.refreshInterval = refreshInterval;
	}
	@Override
    public String toString() {
        return layerName;
    }
}
