package com.tellhow.graphicframework.algorithm;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.swing.JOptionPane;

import org.apache.batik.dom.svg.SVGOMGElement;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.basic.LineLinkCache;
import com.tellhow.graphicframework.basic.LineLinkEquip;
import com.tellhow.graphicframework.basic.LinkEquipVO;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2012-2-25 ����10:50:23 
 */
public class ElementSearch {

	private PowerDevice srcDevice = null;   //��ʼ�豸����
	private PowerDevice tagDevice = null;   //��ʼ�豸����
	private Document fSvgDocument = null;
	private Element fGroupElement = null;
	private String elementID = "";
	private String stationID = "";
	private String title = "�ڵ�����";
	private HashMap<String,ArrayList<String>> curPath = new HashMap<String,ArrayList<String>>();
	private ArrayList<ArrayList<String>> allPath = new ArrayList<ArrayList<String>>();
	private ArrayList<String> equipElementIDList = new ArrayList<String>();
	private ArrayList<String> elementIDList = new ArrayList<String>();
	private SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
	private String searchType;
	
	public void execute(Map inParaMap, Map outParaMap) {
		
		if(!initParams(inParaMap))
			return;
		if(srcDevice != null)
			stationID = srcDevice.getPowerStationID();
		if(srcDevice != null)
			fGroupElement = resolver.getDeviceGroupElement(srcDevice);
		fSvgDocument = fGroupElement.getOwnerDocument();
		elementID = fGroupElement.getAttribute("id");
		Map<String, Map<String, LinkEquipVO>> equipLinkMap = SystemConstants.getMapEquipLink().get(fSvgDocument);
		LineLinkCache linkCache = new LineLinkCache();
		linkCache.setEquipLinkMap(equipLinkMap);
		ArrayList<String> list = new ArrayList<String>();
		list.add(elementID);
		curPath.put(elementID, list);
		if(searchType.equals("Path")) {
			String tagElementID = resolver.getDeviceGroupElement(tagDevice).getAttribute("id");
			searchPath(linkCache, elementID, tagElementID);
		}
		else if(searchType.equals("LinkEquipElement"))
			searchLinkEquipElement(linkCache, elementID);
		else if(searchType.equals("LinkElement"))
			searchLinkElement(linkCache, elementID);
		else if(searchType.equals("LinkLine"))
			searchLinkLine(linkCache, elementID);
		int min = 0;
		for(int i = 1; i < allPath.size(); i++) {
			if(allPath.get(i).size() < allPath.get(min).size())
				min = i;
		}
		ArrayList<Element> elementList = new ArrayList<Element>();
		if(searchType.equals("Path")) {
			for(int i = 0; i < allPath.get(min).size(); i++) {
				elementList.add(fSvgDocument.getElementById(allPath.get(min).get(i)));
			}
		}
		else if(searchType.equals("LinkEquipElement")) {
			for(int i = 0; i < equipElementIDList.size(); i++) {
				elementList.add(fSvgDocument.getElementById(equipElementIDList.get(i)));
			}
		}
		else if(searchType.equals("LinkElement")) {
			for(int i = 0; i < equipElementIDList.size(); i++) {
				elementList.add(fSvgDocument.getElementById(equipElementIDList.get(i)));
			}
		}
		else if(searchType.equals("LinkLine")) {
			for(int i = 0; i < equipElementIDList.size(); i++) {
				elementList.add(fSvgDocument.getElementById(equipElementIDList.get(i)));
			}
		}
		outParaMap.put("ElementList", elementList);
	}
	
	public void searchLinkEquipElement(LineLinkCache linkCache, String srcElementID) {
		elementIDList.add(srcElementID);
		List<LinkEquipVO> list = null;
		if(linkCache.isLine(srcElementID))
			list = linkCache.getLinkEquipForLine(srcElementID);
		else if(srcElementID.equals(elementID)) {
			list = linkCache.getLinkLineForEquip(srcElementID);
		}
		else {
			equipElementIDList.add(srcElementID);
			return;
		}
		for(int i = 0; i < list.size(); i++) {
			LinkEquipVO link = list.get(i);
			String linkElementID = "";
			if(!link.getS_id().equals(srcElementID))
				linkElementID = link.getS_id();
			else
				linkElementID = link.getT_id();
			if(elementIDList.contains(linkElementID))
				continue;
			if(linkCache.isLine(linkElementID)) {
				searchLinkEquipElement(linkCache, linkElementID);
			}
			else {
				equipElementIDList.add(linkElementID);
				continue;
			}
		}
	}
	
	public void searchLinkElement(LineLinkCache linkCache, String srcElementID) {
		elementIDList.add(srcElementID);
		List<LinkEquipVO> list = null;
		if(linkCache.isLine(srcElementID)) {
			equipElementIDList.add(srcElementID);
			return;
		}
		else if(srcElementID.equals(elementID)) {
			list = linkCache.getLinkLineForEquip(srcElementID);
		}
		else {
			equipElementIDList.add(srcElementID);
			return;
		}
		for(int i = 0; i < list.size(); i++) {
			LinkEquipVO link = list.get(i);
			String linkElementID = "";
			if(!link.getS_id().equals(srcElementID))
				linkElementID = link.getS_id();
			else
				linkElementID = link.getT_id();
			if(elementIDList.contains(linkElementID))
				continue;
			if(linkCache.isLine(linkElementID)) {
				equipElementIDList.add(linkElementID);
				continue;
			}
		}
	}
	
	public void searchLinkLine(LineLinkCache linkCache, String srcElementID) {
		elementIDList.add(srcElementID);
		List<LinkEquipVO> list = null;
		if(linkCache.isLine(srcElementID))
			list = linkCache.getLinkEquipForLine(srcElementID);
		else if(srcElementID.equals(elementID)) {
			list = linkCache.getLinkLineForEquip(srcElementID);
		}
		else {
			equipElementIDList.add(srcElementID);
			return;
		}
		for(int i = 0; i < list.size(); i++) {
			LinkEquipVO link = list.get(i);
			String linkElementID = "";
			if(!link.getS_id().equals(srcElementID))
				linkElementID = link.getS_id();
			else
				linkElementID = link.getT_id();
			if(elementIDList.contains(linkElementID))
				continue;
			if(linkCache.isLine(linkElementID)) {
				equipElementIDList.add(linkElementID);
				continue;
			}
		}
	}
	
	public void searchPath(LineLinkCache linkCache, String srcElementID, String tagElementID) {
		List<LinkEquipVO> list = null;
		if(linkCache.isLine(srcElementID))
			list = linkCache.getLinkEquipForLine(srcElementID);
		else
			list = linkCache.getLinkLineForEquip(srcElementID);
		for(int i = 0; i < list.size(); i++) {
			LinkEquipVO link = list.get(i);
			String linkElementID = "";
			if(!link.getS_id().equals(srcElementID))
				linkElementID = link.getS_id();
			else
				linkElementID = link.getT_id();
			if(linkElementID.equals(tagElementID)) {
				curPath.get(srcElementID).add(linkElementID);
				allPath.add((ArrayList<String>)curPath.get(srcElementID).clone());
				curPath.remove(srcElementID);
				return;
			}
			else if(!curPath.get(srcElementID).contains(linkElementID)) {
				if(!linkCache.isLine(linkElementID)) {
					String deviceID = resolver.getDeviceID(fSvgDocument.getElementById(linkElementID));
					if(SystemConstants.getMapPowerStationDevice(stationID, deviceID) != null) {
						PowerDevice pd = (PowerDevice)SystemConstants.getMapPowerStationDevice(stationID, deviceID);
						if(pd.getDeviceType().equals(SystemConstants.MotherLine))
							continue;
					}
				}
				ArrayList<String> newlist = (ArrayList<String>)curPath.get(srcElementID).clone();
				newlist.add(linkElementID);
				curPath.put(linkElementID, newlist);
				searchPath(linkCache, linkElementID, tagElementID);
			}
		}
		curPath.remove(srcElementID);
	}
	
	public static Map<String, Map<String, LinkEquipVO>> getEquipLinkMap(
			Document document) {
		Map<String, Map<String, LinkEquipVO>> equipLinkMap = new HashMap<String, Map<String, LinkEquipVO>>();
		String link_layer = null;
		if(document!=null){
			if(document.getElementById("ConnectLine_Layer")!=null){
				link_layer="ConnectLine_Layer";
			}else if(document.getElementById("Link_Layer")!=null){
				link_layer="Link_Layer";
			}
		}
		if (link_layer != null&&link_layer.equals("Link_Layer")) {
			NodeList linkLayerNodeList = document.getElementById(link_layer)
					.getChildNodes();
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Node node = linkLayerNodeList.item(i);
				if (node instanceof SVGOMGElement) {
					String id = ((Element) node).getAttribute("id");
					if (((Element) node).getElementsByTagName("cge:PSR_Link")
							.getLength() != 0) {
						LineLinkEquip lineLinkEquip = getLineLinkEquip((Element) node);
						List<LinkEquipVO> LinkEquipVOList = lineLinkEquip
								.getLinkEquipVOList();
						for (int j = 0; j < LinkEquipVOList.size(); j++) {
							LinkEquipVO linkEquipVO = LinkEquipVOList.get(j);
							Map<String, LinkEquipVO> relationMap = null;
							if (!equipLinkMap.containsKey(id)) {
								relationMap = new HashMap<String, LinkEquipVO>();
								equipLinkMap.put(id, relationMap);
							} else {
								relationMap = equipLinkMap.get(id);
							}
							relationMap.put(linkEquipVO.getT_id(), linkEquipVO);
						}
					}
				}
			}
		}
		return equipLinkMap;
	}
	
	public static LineLinkEquip getLineLinkEquip(Element lineElement) {
		LineLinkEquip lineLinkEquip = new LineLinkEquip();
		if (lineElement.getElementsByTagName("cge:PSR_Link").getLength() != 0) {
			String s_id = getGraphElementID(lineElement);
			Element psrLinkElement = (Element) lineElement
					.getElementsByTagName("cge:PSR_Link").item(0);
			NamedNodeMap attributes = psrLinkElement.getAttributes();
			Node att = null;
			for (int i = 0; i < attributes.getLength(); i++) {
				att = attributes.item(i);
				if (att.getNodeName() != null) {
					String nodePropName = att.getNodeName();
					String nodePropValue = att.getNodeValue();

					Pattern p = Pattern.compile("Pin\\d+InfoVect\\d+LinkObjId");
					Matcher m = p.matcher(nodePropName);
					if (m.find()) {
						Pattern p1 = Pattern.compile("\\d+");
						Matcher m1 = p1.matcher(nodePropName);
						String t_id = null;
						int s_touchLoaction = -1;
						int s_linkLSH = -1;
						int t_touchLoaction = -1;
						// ��s_linkLoactionֵ��s_linkLSHֵ
						int k = 0;
						while (m1.find()) {
							String theNumber = m1.group();
							if (k == 0) {
								s_touchLoaction = Integer.valueOf(theNumber);
							} else if (k == 1) {
								s_linkLSH = Integer.valueOf(theNumber);
							}
							k++;
						}

						// ��t_idֵ��t_linkLoactionֵ
						if (nodePropValue != null && !nodePropValue.equals("")) {
//							p1 = Pattern.compile("\\d+");
//							m1 = p1.matcher(nodePropValue);
//							k = 0;
//							while (m1.find()) {
//								String theNumber = m1.group();
//								if (k == 0) {
//									t_id = theNumber;
//								} else if (k == 1) {
//									t_touchLoaction = Integer.valueOf(theNumber);
//								}
//								k++;
//							}
							if(nodePropValue.lastIndexOf("_") >= 0) {
								t_id = nodePropValue.substring(0, nodePropValue.lastIndexOf("_"));
								t_touchLoaction = Integer.valueOf(nodePropValue.substring(nodePropValue.lastIndexOf("_")+1));
							}
							else
								t_id = nodePropValue;
							LinkEquipVO linkEquipVO = new LinkEquipVO();
							linkEquipVO.setS_id(s_id);
							linkEquipVO.setT_id(t_id);
							linkEquipVO.setT_touchLoaction(t_touchLoaction);
							linkEquipVO.setS_touchLoaction(s_touchLoaction);
							linkEquipVO.setS_linkLSH(s_linkLSH);
							lineLinkEquip.addLinkEquipVO(linkEquipVO);
							String t_sub_prop_name = "P" + s_touchLoaction
									+ "_" + s_linkLSH;
							String t_sub_prop_value = psrLinkElement
									.getAttribute(t_sub_prop_name);
							if (t_sub_prop_value != null
									&& !t_sub_prop_value.equals("")) {
								p1 = Pattern.compile("\\d+");
								m1 = p1.matcher(t_sub_prop_value);
								k = 0;
								String t_sub_id = null;
								int t_sub_touchLoaction = -1;
								while (m1.find()) {
									String theNumber = m1.group();
									if (k == 0) {
										t_sub_id = theNumber;
									} else if (k == 1) {
										t_sub_touchLoaction = Integer
												.valueOf(theNumber);
									}
									k++;
								}
								linkEquipVO.setT_sub_id(t_sub_id);
								linkEquipVO
										.setT_sub_touchLoaction(t_sub_touchLoaction);
							}
						}
					}

				}

			}

		}
		return lineLinkEquip;

	}

	public static String getGraphElementID(Element element) {
		String id = element.getAttribute("id");
		return id;
	}
	
	public boolean initParams(Map inParaMap) {
//		String[] param = {"oprSrcDevice","oprTagDevice","oprSrcElement",""};
//		for(int i = 0; i < param.length; i++) {
//			if(!inParaMap.containsKey(param[i])) {
//		    	JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), title+"ȱ�ٲ���"+param[i]+"��",
//	                    "��ʾ", JOptionPane.ERROR_MESSAGE);
//		    	return false;
//			}
//		}
		srcDevice = (PowerDevice) inParaMap.get("oprSrcDevice");
		tagDevice = (PowerDevice) inParaMap.get("oprTagDevice");
		fGroupElement = (Element) inParaMap.get("oprSrcElement");
		searchType = (String) inParaMap.get("searchType");
		return true;
	}
}
