package com.tellhow.graphicframework.algorithm;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.utils.StringUtils;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2010-7-1 ����03:51:27 
 */
@SuppressWarnings("unchecked")
public class CommonSearch{

	private HashMap<String,PowerDevice> StationDeviceMap;
	private HashMap<String,ArrayList<String>> pointDeviceMap;
	private HashMap<String,ArrayList<String>> devicePointMap;
	private ArrayList<String> busbarSectionPointList;
	
	private HashMap<String,ArrayList<PowerDevice>> pathMap = new HashMap<String,ArrayList<PowerDevice>>();
	private List linkedDeviceList;
	private HashMap<PowerDevice,ArrayList<PowerDevice>> resultPath ;//���ͨ·����
	private HashMap<PowerDevice,ArrayList<ArrayList<PowerDevice>>> resultAllPath  = new HashMap<PowerDevice,ArrayList<ArrayList<PowerDevice>>>(); //����ͨ·����
	
	private PowerDevice srcDevice = null;   //��ʼ�豸����
	private boolean isSearchDirectDevice = false; //�Ƿ�ֻ����ֱ�������豸
	private boolean isSearchOffPath = true; //�Ƿ������Ͽ�·��
	private boolean isStopOnBusbarSection = true; //�Ƿ�����ĸ��ֹͣ����
	private boolean isStopOnTagDevType = true; //����Ŀ���豸����ֹͣ����
	private boolean isPrintPath = false; //�Ƿ��ӡͨ·
	private int validPort = 0; //0:ȫ����Ч  1:1�Ŷ˿���Ч  2:2�Ŷ˿���Ч
	private String tagDevType = "";
	private String excDevType = "";
	private List excDevList = new ArrayList(); //�ų��豸����
	
	public void execute(Map inParaMap, Map outParaMap) {
		// TODO Auto-generated method stub
		this.clearList();
		linkedDeviceList = new ArrayList();
		resultPath  = new HashMap<PowerDevice,ArrayList<PowerDevice>>();
		initParams(inParaMap);
		StationDeviceMap = (HashMap<String, PowerDevice>) SystemConstants.getMapPowerStationDevice(srcDevice.getPowerStationID());
		pointDeviceMap = SystemConstants.getMapToplogyPointDevice().get(srcDevice.getPowerStationID());
		devicePointMap = SystemConstants.getMapToplogyDevicePoint().get(srcDevice.getPowerStationID());
		busbarSectionPointList = SystemConstants.getMapMotherlinePoint().get(srcDevice.getPowerStationID());
		linkedDeviceList = getLinkedDevices(srcDevice.getPowerStationID(),srcDevice.getPowerDeviceID());
		outParaMap.put("linkedDeviceList", linkedDeviceList); //List
		outParaMap.put("pathList", resultPath); //HashMap<PowerDevice,ArrayList<PowerDevice>>
		outParaMap.put("allPathList", resultAllPath); //HashMap<PowerDevice,ArrayList<ArrayList<PowerDevice>>>
		//PowerDevice:linkedDeviceList�е�ĳ��Ŀ���豸  ArrayList<PowerDevice>:��ʼ�豸����Ŀ���豸�����ͨ·
		//ArrayList<ArrayList<PowerDevice>>:��ʼ�豸����Ŀ���豸������ͨ·
		
		if(isPrintPath)
		{
			for(Iterator iter = linkedDeviceList.iterator();iter.hasNext();) {
				PowerDevice pd = (PowerDevice) iter.next();
//				System.out.println(pd.getPowerDeviceName());
//				System.out.println("���ͨ·:");
				for(Iterator iter2 = resultPath.get(pd).iterator();iter2.hasNext();) {
					PowerDevice device = (PowerDevice) iter2.next();
//					System.out.print(device.getPowerDeviceName()+"--");
				}
//				System.out.println();
//				System.out.println("����ͨ·:");
				for(Iterator iter3 = resultAllPath.get(pd).iterator();iter3.hasNext();) {
					ArrayList<PowerDevice> pathList = (ArrayList<PowerDevice>) iter3.next();
					for(Iterator iter4 = pathList.iterator();iter4.hasNext();) {
						PowerDevice device = (PowerDevice) iter4.next();
//						System.out.print(device.getPowerDeviceName()+"--");
					}
//					System.out.println();
				}
//				System.out.println();
			}
		}
	}
	
	public List getLinkedDevices(String powerStationID,String powerEquipID)
	{
		List linkedDeviceList = new ArrayList();
		linkedDeviceList = getDevices(linkedDeviceList, powerStationID, powerEquipID, "");
		return linkedDeviceList;
	}
	
	public List getDevices(List linkedDeviceList, String powerStationID,String powerEquipID, String lastPointID)
	{
		boolean isPointWithBusbarSection = false;
		List pointList = devicePointMap.get(powerEquipID);
		if(pointList==null)
			return linkedDeviceList;
		PowerDevice device = StationDeviceMap.get(powerEquipID);
		for(int i = 0; i < pointList.size(); i++) {
			if(lastPointID.equals("") && validPort != 0 && validPort != (i+1))
				continue;
			String connectPointID = pointList.get(i).toString();
			if(connectPointID.equals(lastPointID))
				continue; //��������ͨ��ͬ�������ӹ�ϵ����
			if(busbarSectionPointList.contains(connectPointID) && !device.getDeviceType().equals(SystemConstants.MotherLine))
				isPointWithBusbarSection = true; //��ǰ�Ƿ�ĸ���豸����ĸ����ص����ӹ�ϵ
			else
				isPointWithBusbarSection = false;
			List deviceList = pointDeviceMap.get(connectPointID);
			for(Iterator iter2 = deviceList.iterator();iter2.hasNext();) {
				String equipID = (String) iter2.next();
				PowerDevice pd = StationDeviceMap.get(equipID);
				if(isPointWithBusbarSection && !pd.getDeviceType().equals(SystemConstants.MotherLine))
					continue; //����ǰ�Ƿ�ĸ���豸����ĸ����ص����ӹ�ϵ,ֻ��ĸ������ЧĿ���豸
				if(equipID.equals(powerEquipID))
					continue;
				else if(pathMap.get(powerEquipID) != null && pathMap.get(powerEquipID).contains(pd))
					continue; //ͨ·�Ѱ�����ǰ�豸ֹͣ����
				else if(excDevList.contains(pd)) //���ų��豸ֹͣ����
					continue;
				else if(excDevType.indexOf(pd.getDeviceType()) >= 0) //���ų��豸����ֹͣ����
					continue;
				
				ArrayList<PowerDevice> pathDeviceList = new ArrayList<PowerDevice>();
				if(pathMap.get(powerEquipID) == null)
					pathDeviceList.add(srcDevice);
				else
				{
					for(Iterator iter3 = pathMap.get(powerEquipID).iterator();iter3.hasNext();) {
						pathDeviceList.add((PowerDevice) iter3.next());
					}
				}
				pathDeviceList.add(pd); //�������������豸����ͨ·
				pathMap.put(pd.getPowerDeviceID(), pathDeviceList); //�������������豸��IDΪͨ·����
				
				if(tagDevType.equals("") || StringUtils.compareStr(pd.getDeviceType(), tagDevType) == 0) //�豸���͹���
				{
					operateDevice(pd);
					if(!linkedDeviceList.contains(pd))
						linkedDeviceList.add(pd);
				}
				if(isSearchDirectDevice)
					continue;
//				else if(excDevType.indexOf(pd.getDeviceType()) >= 0) //���ų��豸����ֹͣ����
//					continue;
				else if(pd.getDeviceType().equals(SystemConstants.InOutLine)) //����·ֹͣ����
					continue;
				else if(pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) //���ӵص�բֹͣ����
					continue;
				else if(isStopOnBusbarSection && pd.getDeviceType().equals(SystemConstants.MotherLine)) //�Ƿ���ĸ��ֹͣ����
					continue;
				else if(!isSearchOffPath && !pd.getDeviceStatus().equals("0"))
					continue;
				else if(isStopOnTagDevType && tagDevType.indexOf(pd.getDeviceType()) >= 0)
					continue;
				else
					getDevices(linkedDeviceList, powerStationID, equipID,connectPointID);
			}
		}
		return linkedDeviceList;
	}
	
	private void operateDevice(PowerDevice pd)
	{
		ArrayList<PowerDevice> path = pathMap.get(pd.getPowerDeviceID());
		if(resultAllPath.get(pd) == null)
		{
			ArrayList<ArrayList<PowerDevice>> pathList = new ArrayList<ArrayList<PowerDevice>>();
			pathList.add(path);
			resultAllPath.put(pd, pathList);
			resultPath.put(pd, path);
		}
		else
		{
			resultAllPath.get(pd).add(path);
			if(resultPath.get(pd).size() > path.size())
				resultPath.put(pd, path);
		}
	}
	
	public void initParams(Map inParaMap) {
	       
		srcDevice = (PowerDevice) inParaMap.get("oprSrcDevice");
        if (inParaMap.get("tagDevType") != null && !inParaMap.get("tagDevType").toString().equals("")) {
            tagDevType = inParaMap.get("tagDevType").toString().trim();
        }
        if (inParaMap.get("excDevType") != null && !inParaMap.get("excDevType").toString().equals("")) {
            excDevType = inParaMap.get("excDevType").toString().trim();
        }
        if (inParaMap.get("isSearchDirectDevice") != null && !inParaMap.get("isSearchDirectDevice").toString().equals("")) {
        	isSearchDirectDevice = java.lang.Boolean.parseBoolean(inParaMap.get("isSearchDirectDevice").toString().trim());
        }
        if (inParaMap.get("isSearchOffPath") != null && !inParaMap.get("isSearchOffPath").toString().equals("")) {
        	isSearchOffPath = java.lang.Boolean.parseBoolean(inParaMap.get("isSearchOffPath").toString().trim());
        }
        if (inParaMap.get("isStopOnBusbarSection") != null && !inParaMap.get("isStopOnBusbarSection").toString().equals("")) {
        	isStopOnBusbarSection = java.lang.Boolean.parseBoolean(inParaMap.get("isStopOnBusbarSection").toString().trim());
        }
        if (inParaMap.get("isStopOnTagDevType") != null && !inParaMap.get("isStopOnTagDevType").toString().equals("")) {
        	isStopOnTagDevType = java.lang.Boolean.parseBoolean(inParaMap.get("isStopOnTagDevType").toString().trim());
        }
        if (inParaMap.get("isPrintPath") != null && !inParaMap.get("isPrintPath").toString().equals("")) {
        	isPrintPath = java.lang.Boolean.parseBoolean(inParaMap.get("isPrintPath").toString().trim());
        }
        if (inParaMap.get("excDevList") != null) {
        	excDevList = (List)inParaMap.get("excDevList");
        }
        if (inParaMap.get("validPort") != null) {
        	validPort = Integer.parseInt(inParaMap.get("validPort").toString());
        }
	}
	public void clearList(){
		this.validPort = 0;
		this.pathMap.clear();
		//this.linkedDeviceList.clear();
		//this.resultPath.clear();
		this.resultAllPath.clear();
		this.srcDevice = null;   //��ʼ�豸����
		this.isSearchDirectDevice = false; //�Ƿ�ֻ����ֱ�������豸
		this.isSearchOffPath = true; //�Ƿ������Ͽ�·��
		this.isStopOnBusbarSection = true; //�Ƿ�����ĸ��ֹͣ����
		this.isStopOnTagDevType = true; //����Ŀ���豸����ֹͣ����
		this.tagDevType = "";
		this.excDevType = "";
		this.excDevList.clear();
		
	}

}
