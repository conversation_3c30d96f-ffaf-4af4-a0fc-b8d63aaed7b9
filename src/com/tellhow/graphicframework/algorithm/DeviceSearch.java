package com.tellhow.graphicframework.algorithm;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: ���豸���ƻ����Ͳ�ѯȫ����վ��·��վ���豸
 * ��    ��: ֣��
 * ��������: 2010-7-20 ����08:56:46 
 */
public class DeviceSearch{

	private String mapType; //��������,��ȫ���ͳ�վ,����
	private String powerStationID; //��վID,mapType�ǳ�վʱ����
	private String searchStr; //��ѯ�ַ���,���豸����ģ��ƥ��,��ѡ
	private String deviceType; //�豸����,��ѡ
	
	public void execute(Map inParaMap, Map outParaMap) {
		// TODO Auto-generated method stub
		initParams(inParaMap);
		ArrayList<PowerDevice> resultList = new ArrayList<PowerDevice>();
		ArrayList<PowerDevice> deviceList = new ArrayList<PowerDevice>();
        if(mapType.equals(SystemConstants.MAP_TYPE_SYS))
        {
        	deviceList.addAll(SystemConstants.getMapPowerStation().values());
        	deviceList.addAll(SystemConstants.getMapPowerLine().values());
        }
        else if(mapType.equals(SystemConstants.MAP_TYPE_FAC))
        {
        	deviceList.addAll(SystemConstants.getMapPowerStationDevice(powerStationID).values());
        }
        for(Iterator iter = deviceList.iterator();iter.hasNext();) 
		{
    		PowerDevice pd = (PowerDevice)iter.next();
    		if(searchStr.equals("") || pd.getPowerDeviceName().indexOf(searchStr) != -1)
    		{
    			if(deviceType.equals("") || deviceType.equals(pd.getDeviceType()))
    				resultList.add(pd);
    		}
		}
        outParaMap.put("resultList", resultList);
	}

	public void initParams(Map inParaMap) {
		mapType = (String) inParaMap.get("MapType");
		searchStr = "";
		deviceType = "";
		if (inParaMap.get("searchStr") != null && !inParaMap.get("searchStr").toString().equals("")) {
			searchStr = inParaMap.get("searchStr").toString().trim();
        }
        if (inParaMap.get("powerStationID") != null && !inParaMap.get("powerStationID").toString().equals("")) {
        	powerStationID = inParaMap.get("powerStationID").toString().trim();
        }
        if (inParaMap.get("deviceType") != null && !inParaMap.get("deviceType").toString().equals("")) {
        	deviceType = inParaMap.get("deviceType").toString().trim();
        }
	}
}
