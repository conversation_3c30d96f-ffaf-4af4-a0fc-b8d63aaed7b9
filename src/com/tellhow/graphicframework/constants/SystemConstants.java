/**
 * ��Ȩ���� : ̩������ɷ����޹�˾��Ȩ����
 * �� Ŀ �� ������Ʊר��ϵͳ
 * ����˵�� : ���ϵͳ������ȫ�ֱ���
 * ��    �� : �ſ�
 * �������� : 2008-05-30
 * �޸����� ��
 * �޸�˵�� ��
 * �� �� �� ��
 **/
package com.tellhow.graphicframework.constants;

import java.io.File;
import java.io.FilenameFilter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.swing.JFrame;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import com.tellhow.graphicframework.basic.LinkEquipVO;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.basic.SVGLayer;
import com.tellhow.graphicframework.mainframe.GuiBuilder;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.startup.loader.ConfigurationLoader;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.DOMUtil;

/**
 *
 * <AUTHOR>
 */
public class SystemConstants {

    private SystemConstants() {

    }
    
    //���վ����{���վID,���վ����}
    static final private HashMap<String,PowerDevice> mapPowerStation = new HashMap<String,PowerDevice>();
    //ȫ����·����{��·ID,��·����}
    static final private HashMap<String,PowerDevice> mapPowerLine = new HashMap<String,PowerDevice>();
    //��ŵ�ǰ�����صı��վ��Ϣ����mapΪ˫�㣬��һ��Ϊ("���վID",���վ�豸����Map),�ڶ���Ϊ("�����豸ID",�����豸����),��Ϊ�������վ�ڲ�����ʱʹ�ø�map��¼�豸����
    static final private HashMap<String,HashMap<String,PowerDevice>> mapPowerStationDevice = new HashMap<String,HashMap<String,PowerDevice>>();
    //{���վID,{���ӵ�ID,���ӵ�����豸ID}}
    static final private HashMap<String,HashMap<String,ArrayList<String>>> mapToplogyPointDevice = new HashMap();
    //{���վID,{�豸ID,�豸�������ӵ�ID}}
    static final private HashMap<String,HashMap<String,ArrayList<String>>> mapToplogyDevicePoint = new HashMap();
    //{���վID,������ĸ�ߵ����ӵ㼯��}
    static final private HashMap<String,ArrayList<String>> mapMotherlinePoint = new HashMap();
    //�豸����{�豸���ͱ�ʶ,�豸��������}
    static final private HashMap<String,String> mapEquipType = new HashMap();
    //��ɫ��Ϣ,��ʽ<��ѹ�ȼ�����,"rgb(0,0,0)">
    static final private HashMap<String,String> mapColor = new HashMap<String,String>();
    //��Ҫ��ʱˢ�µĲ�<��ID,ˢ�¼��(��)>
    static final private HashMap<String,SVGLayer> mapSVGLayer = new HashMap<String,SVGLayer>();
    //�ṹΪ("���վ����"�����վSVG�ĵ�);
    public static Map<String, SVGFile> mapSVGFile = new LinkedHashMap(); //SVG�ļ�
    //
    static final private ArrayList<Element> listSelectedElement = new ArrayList<Element>();
    
    static final private Map<Document, Map<String, Map<String, LinkEquipVO>>> mapEquipLink = new HashMap<Document, Map<String, Map<String, LinkEquipVO>>>();
    //ϵͳ����ʱ��ȡͼ���ļ����߳�
    static public Thread threadLoadFile = null;
    //ͼ���ļ���ģʽ
    static public String MAP_NAME_PATTERN = ""; 
    //ͼ�����ű���
    static public String MAP_SCALE = "1"; 
    //ͼ��ѡ��ģʽ
    static public String MAP_MOUSE_MODE = "0"; //0��קģʽ 1ѡ��ģʽ 
    
    //�򿪵�ͼ��
    static public String OPEN_MAP = ""; //��ǰҪ�򿪵�ͼ��
    
    //��Ϣ�Ի������
    static public String SYSTEM_TITLE = "��ʾ";
    //ʧ����ʽ����
    static public String LOSE_COLOR_CODE = "0"; 
    //��������ʽ����
    static public String NODATA_COLOR_CODE = "-1";
    //����ͼ���λ��  
    //windows 	svg·��
     static final public String FILE_SVGMAP_PATH = "tbp_config"+File.separator+"graph"+File.separator+"map"+File.separator+"svg"+File.separator;
    //linux 	svg·��
   // static final public String FILE_SVGMAP_PATH = "tbp_config/graph/map/svg/";

    //ϵͳ����ͼ
    static final public String MAP_TYPE_SYS = "sys"; 
    //��վ����ͼ
    static final public String MAP_TYPE_FAC = "fac"; 
    //����ͼ
    static final public String MAP_TYPE_LINE = "line"; 
    
    static final public String XLINK_NS = "http://www.w3.org/1999/xlink";
    
    //�û������趨
    public static  String isInitDoubleScreen = "0";//˫����ʾ
    public static  String isInitEMSStatus = "0"; //�Ƿ��Զ�����EMS״̬
    public static  String isInitNewWin = "0";//�Ƿ��´���
    public static  String isInitfigureright="0";//�Ƿ��ͼ�Ҽ�ֱ�����෴����
    public static  String refreshrate="0";//ˢ��Ƶ��
    public static  String isSupervisory ="0";//�Ƿ�������

	//��SVGͼ���ƻ���
	public static List<String> oldSVGName = new ArrayList<String>();
    
    //�ж��Ƿ��·����
    public static List<String> isSControl = new ArrayList<String>();

    //���߳�������
    public static String InOutLine = "ACLineSegment";
    //����
    public static String Switch = "Breaker";
    //ĸ������
    public static String MotherLine = "BusbarSection";
    //��������
    public static String ElecCapacity = "Compensator";
    //��բ����
    public static String SwitchSeparate = "Disconnector";
    //���������
    public static String PowerGenerator = "Generator";
    //�ӵص�բ����
    public static String SwitchFlowGroundLine = "GroundDisconnector";
    //�ɲ�ж�豸����
    public static String RemovableDevice = "RemovableDevice";
    //����Ͷ
    public static String BztDevice = "BztDevice";
    //��Ͷ��ϵ
    public static String BztRelate = "BztRelate";
    //�����豸����
    public static String ProtectDevice = "protectDevice";
    //��������
    public static String PowerTransformer = "PowerTransformer";
    //�翹������
    public static String ElecShock = "Reactor";
    //��ѹ����������
    public static String VolsbTransformer = "PT";
    //������Ȧ
    public static String Ascoil = "Ascoil";
    //�۶���
    public static String Fuse = "Fuse";
    //������
    public static String Arrester = "Arrester";
    //վ�ñ�
    public static String Term = "Term";
    //�ӵر�
    public static String EarthingTransformer = "EarthingTransformer";
    //�����豸
    public static String OtherEquip = "Other";
    //���վ����
    public static String PowerStation = "PowerStation";
    //�糧����
    public static String PowerFactory = "PowerFactory";
    //�������
    public static String DistributionPowerTransform = "DistributionPowerTransform";
    //����ѹ��
    public static String ProtectiveBoard = "Protectiveboard";
    
    private static JFrame mf = null;
    private static GuiBuilder controller;
    
    public static String SwitchStatus = "SwitchStatus";
    public static String EquipStatus = "EquipStatus";

    /**
     * ��  ;: ������·����վ��Ŵ�ϵͳ���Ƴ�����������ݼ�¼
     * @����1(String)����·�����վ����
     * @����ֵ����
     */
    public static void removeLineOrStation(String powerStationID, Document doc) {
        removeMapPowerStation(powerStationID);
        removeMapToplogyPointDevice(powerStationID);
        removeMapToplogyDevicePoint(powerStationID);
        removeMapMotherlinePoint(powerStationID);
        mapEquipLink.remove(doc);
    }
    
    
    
    //mapPowerStation
    public static HashMap<String, PowerDevice> getMapPowerStation() {
		return mapPowerStation;
	}
    
    public static PowerDevice getMapPowerStation(String powerStationID) {
		return mapPowerStation.get(powerStationID);
	}
    
    public static void putMapPowerStation(String powerStationID, PowerDevice station) {
    	mapPowerStation.put(powerStationID, station);
	}
    
    //mapPowerLine
    public static HashMap<String, PowerDevice> getMapPowerLine() {
		return mapPowerLine;
	}
    
    public static PowerDevice getMapPowerLine(String lineCode) {
		return mapPowerLine.get(lineCode);
	}
    
    public static void putMapPowerLine(String lineCode, PowerDevice line) {
    	mapPowerLine.put(lineCode, line);
	}
    
    
    //mapPowerStationDevice
    public static HashMap<String,HashMap<String,PowerDevice>> getMapPowerStationDevice()
    {
    	return mapPowerStationDevice;
    }
    
    public static Map getMapPowerStationDevice(String powerStationID) {
        Object objPowerStation = mapPowerStationDevice.get(powerStationID);
        if (objPowerStation != null) {
            return (Map) objPowerStation;
        } else {
            return null;
        }
    }
    
    public static Object getMapPowerStationDevice(String powerStationID, String powerEquipID) {
        Object objPowerStation = mapPowerStationDevice.get(powerStationID);
        if (objPowerStation != null) {
            return ((Map) objPowerStation).get(powerEquipID);
        } else {
            return null;
        }
    }
    
    public static void putMapPowerStation(String powerStationID, HashMap<String,PowerDevice> powerStationDeviceMap) {
        mapPowerStationDevice.put(powerStationID, powerStationDeviceMap);
    }
    
    public static void removeMapPowerStation(String powerStationID) {
        mapPowerStationDevice.remove(powerStationID);
    }
    
    //mapToplogyPointDevice
    public static HashMap<String, HashMap<String, ArrayList<String>>> getMapToplogyPointDevice() {
		return mapToplogyPointDevice;
	}
    
    public static void putMapToplogyPointDevice(String powerStationID, HashMap<String, ArrayList<String>> toplogyPointDeviceMap) {
    	mapToplogyPointDevice.put(powerStationID, toplogyPointDeviceMap);
    }
    
    public static void removeMapToplogyPointDevice(String powerStationID) {
    	mapToplogyPointDevice.remove(powerStationID);
    }
    
    //mapToplogyDevicePoint
    public static HashMap<String, HashMap<String, ArrayList<String>>> getMapToplogyDevicePoint() {
		return mapToplogyDevicePoint;
	}

    public static void putMapToplogyDevicePoint(String powerStationID, HashMap<String, ArrayList<String>> toplogyDevicePointMap) {
    	mapToplogyDevicePoint.put(powerStationID, toplogyDevicePointMap);
    }
    
    public static void removeMapToplogyDevicePoint(String powerStationID) {
    	mapToplogyDevicePoint.remove(powerStationID);
    }
    
    //mapMotherlinePoint
    public static HashMap<String,ArrayList<String>> getMapMotherlinePoint() {
		return mapMotherlinePoint;
	}
    
    public static void putMapMotherlinePoint(String powerStationID, ArrayList<String> motherlinePointList) {
    	mapMotherlinePoint.put(powerStationID, motherlinePointList);
	}
    
    public static void removeMapMotherlinePoint(String powerStationID) {
    	mapMotherlinePoint.remove(powerStationID);
    }

    /**
     * ��ȡ������
     */
    public static JFrame getMainFrame() {
    	if(mf == null){
    		new JFrame();
    	}
    	
        return mf;
    }

    /**
     * ����������
     */
    public static void setMainFrame(JFrame _mf) {
        mf = _mf;
    }

	public static Map<String, SVGFile> getMapSVGFile() {
		return mapSVGFile;
	}
	
	public static List<SVGFile> getSVGFileByStationID(String stationID) {
		List<SVGFile> listAll = new ArrayList<SVGFile>();
		List<SVGFile> listDefault = new ArrayList<SVGFile>();
		for (Iterator iterator = mapSVGFile.values().iterator(); iterator.hasNext();) {
			SVGFile svgFile = (SVGFile) iterator.next();
			
			if(svgFile.getMapType().equals("line"))
				continue;
			if(svgFile.getStationID().equals(stationID)) {
				listAll.add(svgFile);
				if(svgFile.isDefault())
					listDefault.add(svgFile);
			}else if(svgFile.getFilePath().equals(stationID)) {
				listAll.add(svgFile);
				if(svgFile.isDefault())
					listDefault.add(svgFile);
			}
		}
		while(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive() && listAll.size() == 0) {
			for (Iterator iterator = mapSVGFile.values().iterator(); iterator.hasNext();) {
				SVGFile svgFile = (SVGFile) iterator.next();
				if(svgFile.getMapType().equals("line"))
					continue;
				if(svgFile.getStationID().equals(stationID)) {
					listAll.add(svgFile);
					if(svgFile.isDefault())
						listDefault.add(svgFile);
				}
			}
		}
		
		if(listDefault.size() > 0)
			return listDefault;
		else
			return listAll;
	}
	
	public static List<SVGFile> getSVGFileByLineID(String lineID) {
		List<SVGFile> listAll = new ArrayList<SVGFile>();
		List<SVGFile> listDefault = new ArrayList<SVGFile>();
		for (Iterator iterator = mapSVGFile.values().iterator(); iterator.hasNext();) {
			SVGFile svgFile = (SVGFile) iterator.next();
			if(svgFile.getLineID().equals(lineID)) {
				listAll.add(svgFile);
				if(svgFile.isDefault())
					listDefault.add(svgFile);
			}
		}
		if(listDefault.size() > 0)
			return listDefault;
		else
			return listAll;
	}
	
	public static void initMapSVGFile() {
		File svgPath = new File(SystemConstants.FILE_SVGMAP_PATH);
		
		File[] files = svgPath.listFiles(new FilenameFilter() {
			public boolean accept(File dir, String name) {
				if (name.endsWith(".svg")) {
					return true;
				}
				return false;
			}
		});
		if (files == null)
			return;
		for (int i = 0; i < files.length; i++) {
			String fileName = files[i].getName();
			
			if(fileName.contains("����")||fileName.contains("վ��")||fileName.contains("����")){
				continue;
			}
			
//			if(fileName.contains("35kV")||fileName.contains("220kV")||fileName.contains("110kV")||fileName.contains("500kV")
//					||fileName.contains("��ƺ��˾")||fileName.contains("�ϲ���˾")||fileName.contains("��¤��˾")||fileName.contains("Ӫɽ��˾")){
				String filePath = files[i].getPath();
				long lastModified = files[i].lastModified();
				SVGFile svgFile = new SVGFile();
				svgFile.setFileName(fileName);
				svgFile.setFilePath(filePath);
				svgFile.setLastModified(lastModified);
				svgFile.setAlwaysOpen(false);
				if(!oldSVGName.contains(fileName)){
					mapSVGFile.put(filePath, svgFile);
				}
//			}
		}
	}
	
	public static void openSVGFile(SVGFile svgFile,List fileNameList,List stationIDList) {
		boolean isNeed = true;
		String[] pattern = MAP_NAME_PATTERN.split(";");
	        Document doc = null;
	        
		if(!svgFile.getStationID().equals(""))
				return;
			if(!MAP_NAME_PATTERN.equals("")) {
				for(int i = 0; i < pattern.length; i++) {
					if(svgFile.getFileName().toLowerCase().indexOf(pattern[i].toLowerCase()) >= 0) {
						isNeed = false;
						break;
					}
				}
			}
			else
				isNeed = true;
			if(!isNeed)
				return;
			doc = DOMUtil.readXMLFile(svgFile.getFilePath());
			if(doc == null)
				return;
			String mapType = SVGDocumentResolver.getResolver().getMapType(doc);
			svgFile.setMapType(mapType);
			if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
				String stationID = SVGDocumentResolver.getResolver().getStationID(doc);
				svgFile.setStationID(stationID);
			}
			else if(mapType.equals(SystemConstants.MAP_TYPE_LINE)) {
				String stationID = SVGDocumentResolver.getResolver().getStationID(doc);
				String lineID = SVGDocumentResolver.getResolver().getLineID(doc);
				svgFile.setStationID(stationID);
				svgFile.setLineID(lineID);
			}
			else {
				Element layer = null;
				NodeList layerList = doc.getDocumentElement().getElementsByTagName("g");
				for(int i = 0; i < layerList.getLength(); i++) {
					Element ele = (Element)layerList.item(i);
					if(ele.hasAttribute("id") && ele.getAttribute("id").equals("Substation_Layer")) {
						layer = ele;
						break;
					}
				}
				if(layer != null) {
					NodeList list = layer.getElementsByTagName("g");
					for(int i = 0; i < list.getLength(); i++) {
						Element group = (Element)list.item(i);
						if(group.hasAttribute("href") && !group.getAttribute("href").equals("")) {
							String fileName = group.getAttribute("href");
							fileNameList.add(fileName);
						}
					}
				}
			}
	}
	
	public static void loadMapSVGFile() {
		SystemConstants.threadLoadFile = new Thread(new Runnable() {
	   	 	    public void run() {
	   	 	    	long start=System.currentTimeMillis();
	   	 	    	
	   	 	    	List fileNameList = new ArrayList();
	   	 	    	List stationIDList = new ArrayList();
	   	 	    	
		   	 	    for (Iterator<SVGFile> it = mapSVGFile.values().iterator(); it.hasNext();) {
		   				try {
		   					if(!SystemConstants.OPEN_MAP.equals("")) {
		   						for (Iterator<SVGFile> it2 = mapSVGFile.values().iterator(); it2.hasNext();) {
		   							SVGFile svgFile2 = it2.next();
		   							if(svgFile2.getFileName().contains(SystemConstants.OPEN_MAP)) {
		   								openSVGFile(svgFile2,fileNameList,stationIDList);
		   								SystemConstants.OPEN_MAP = "";
		   								break;
		   							}
		   						}
		   						SystemConstants.OPEN_MAP = "";
		   					}
		   					SVGFile svgFile = (SVGFile)it.next();
		   					openSVGFile(svgFile,fileNameList,stationIDList);
		   				}
		   				catch (Exception ex) {
		   					ex.printStackTrace();
		   		            Logger.getLogger(ConfigurationLoader.class.getName()).log(Level.FATAL, null, ex);
		   		        }
		   			}
		   	 	    
		   	 	    long end=System.currentTimeMillis();
			    	System.out.println("�������������ĵ�ʱ����:"+(end-start)+"ms");
		   	 	    
			   	 	for (Iterator<SVGFile> it = mapSVGFile.values().iterator(); it.hasNext();) {
			   	 		SVGFile svgFile = it.next();
			   	 		if(fileNameList.contains(svgFile.getFileName()) && !svgFile.getStationID().equals(""))
			   	 			stationIDList.add(svgFile.getStationID());
			   	 	}
			   	 	for (Iterator it = mapSVGFile.values().iterator(); it.hasNext();) {
			   	 		SVGFile svgFile = (SVGFile)it.next();
			   	 		if(stationIDList.contains(svgFile.getStationID()) && !fileNameList.contains(svgFile.getFileName()))
			   	 			svgFile.setStationID("");
			   	 	}
	   	 	    }
	 	 	  });
		SystemConstants.threadLoadFile.start();
	}

	public static void setGuiBuilder(GuiBuilder builder) {
		controller = builder;
	}
	
	public static GuiBuilder getGuiBuilder() {
		return controller;
	}
	
	public static HashMap<String, String> getMapEquipType() {
		return mapEquipType;
	}
	
	public static void putMapEquipType(String equipTypeFlag, String equipTypeName) {
		mapEquipType.put(equipTypeFlag, equipTypeName);
	}
	
	public static HashMap<String, String> getMapColor() {
		return mapColor;
	}
	
	public static void putMapColor(String code, String rgb) {
		mapColor.put(code, rgb);
	}

	public static HashMap<String, SVGLayer> getMapSVGLayer() {
		return mapSVGLayer;
	}
	
	public static void putMapSVGLayer(SVGLayer layer) {
		mapSVGLayer.put(layer.getLayerID(), layer);
	}

	public static ArrayList<Element> getSelectedElement() {
		return listSelectedElement;
	}

	public static Map<Document, Map<String, Map<String, LinkEquipVO>>> getMapEquipLink() {
		return mapEquipLink;
	}
	
}
