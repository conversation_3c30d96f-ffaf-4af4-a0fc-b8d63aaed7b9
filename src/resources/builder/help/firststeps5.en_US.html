<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Running it</title>
<link name="Style" href="help.css" type="text/css" rel="stylesheet">
</head>
<body>
<h1>Running it</h1>
		Before compiling the application, you will need to copy over
		some dependencies:
		<table align="left" cellpadding="5" border="0">
<tr valign="top">
<td><img src="images/help_bullet.png"></td><td align="left">XML GUI Library - Obviously you will need this</td>
</tr>
</table>
		
<table align="left" cellpadding="5" border="0">
<tr valign="top">
<td><img src="images/help_bullet.png"></td><td align="left">Log4J, a logging library. This is a must for every serious java project</td>
</tr>
</table>
		
<table align="left" cellpadding="5" border="0">
<tr valign="top">
<td><img src="images/help_bullet.png"></td><td align="left">Xerces, the Apache Software Foundation's XML parser. Feel free to use another XML parser</td>
</tr>
</table>
		
<table align="left" cellpadding="5" border="0">
<tr valign="top">
<td><img src="images/help_bullet.png"></td><td align="left">The HIG Layout manager. It is internally used for the Group container and the exception message dialog</td>
</tr>
</table>
<br>

		Copy the <b>xmlgui-1.0.jar</b>, <b>log4j-1.2.8.jar</b>, <b>xml-apis.jar</b>, <b>xerces-2.4.1.jar</b> and <b>higlayout-1.0a-wenzel.jar</b> files to your project directory.

		<h3>Compiling the project</h3>
		Enter <code>javac -classpath xml-apis.jar:xerces-2.4.1.jar:higlayout-1.0a-wenzel.jar:log4j-1.2.8.jar:xmlgui-1.0.jar Test.java</code> in the project directory to compile it.

		<h3>Running the project</h3>
		Enter <code>java -classpath xml-apis.jar:xerces-2.4.1.jar:higlayout-1.0a-wenzel.jar:log4j-1.2.8.jar:xmlgui-1.0.jar:. Test</code> in the project directory to run the test application:

		<p align="center">
<img src="images/5_result.png"><br>The running test application</p>
<br>

		There will be warning messages on the console which look
		something like this:<br>
<br>
		
<code>18.02.2004 19:13:27,455  WARN [  main] (InternationalizationManager.java:  78) - no internationalization for identifier [Test Application]</code>
<br>
<br>

		They appear in order to inform the developer that he/she forgot to
		internationalize a caption. To find out how to internationalize the
		test application, have a look at the following chapters.
	</body>
</html>
