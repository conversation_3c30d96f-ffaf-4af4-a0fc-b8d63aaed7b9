<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Preface</title>
<link name="Style" href="help.css" type="text/css" rel="stylesheet">
</head>
<body>
<h1>Preface</h1>
		
<table cellspacing="0" cellpadding="1" align="center" class="warning">
<tr>
<td>
<table cellspacing="0" cellpadding="1">
<tr class="label">
<td colspan="4">Warning</td>
</tr>
<tr class="content">
<td width="3"></td><img src="images/help_warning.png"><td width="3"></td><td>
			The documentation is still under construction
		</td><td width="3"></td>
</tr>
</table>
</td>
</tr>
</table>
		
<h3>What is the Beryl XML GUI Builder?</h3>
		This tool (hereafter referred to as "Builder") enables you to create
		graphical user interfaces in a very quick and visual way. After you
		have created an interface, you can save it as an XML file. You can
		then package the created XML file with your application, which in turn
		uses the Beryl XML GUI Library to recreate the user interface.

		<table align="left" cellpadding="5" border="0">
<tr valign="top">
<td><img src="images/help_bullet.png"></td><td align="left">
			This technique has several big advantages:
			<ul>
				
<li>Separation from code and view
					<p>
						Splitting these two essential parts into separate files will
						help you avoid unnecessary clutter in your source. Swing code
						mixed with application logic can become a troublesome and hard to read
						mess as the application size increases.
					</p>
				
</li>
				
<li>Faster development
					<p>
						While developing inside the builder, you will instantly see the
						results, avoiding continuous trial and error.
					</p>
				
</li>
				
<li>Quickly implement changes
					<p>
						Whenever people ask for changes in the user interface,
						you do not need to touch your codebase - you can simply open up
						the GUI description files in the Builder, implement the changes
						and repackage your application.
					</p>
				
</li>
			
</ul>
		
</td>
</tr>
</table>

		
<h3>Who wrote this software</h3>
		The Builder and the XML GUI Library were written by Wenzel Jakob &lt;<EMAIL>&gt;

		<p>
<img src="images/help_see.png"><b>See also :</b>
<blockquote>
			
<a href="firststeps1.en_US.html">First steps</a>
		
</blockquote>
</p>
	
</body>
</html>
