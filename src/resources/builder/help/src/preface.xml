<?xml version="1.0" encoding="iso-8859-1"?>

<helpfile id="preface">
	<page locale="en_US" title="Preface">
		<warning title="Warning">
			The documentation is still under construction
		</warning>
		<h3>What is the Beryl XML GUI Builder?</h3>
		This tool (hereafter referred to as "Builder") enables you to create
		graphical user interfaces in a very quick and visual way. After you
		have created an interface, you can save it as an XML file. You can
		then package the created XML file with your application, which in turn
		uses the Beryl XML GUI Library to recreate the user interface.

		<bullet>
			This technique has several big advantages:
			<ul>
				<li>Separation from code and view
					<p>
						Splitting these two essential parts into separate files will
						help you avoid unnecessary clutter in your source. Swing code
						mixed with application logic can become a troublesome and hard to read
						mess as the application size increases.
					</p>
				</li>
				<li>Faster development
					<p>
						While developing inside the builder, you will instantly see the
						results, avoiding continuous trial and error.
					</p>
				</li>
				<li>Quickly implement changes
					<p>
						Whenever people ask for changes in the user interface,
						you do not need to touch your codebase - you can simply open up
						the GUI description files in the Builder, implement the changes
						and repackage your application.
					</p>
				</li>
			</ul>
		</bullet>

		<h3>Who wrote this software</h3>
		The Builder and the XML GUI Library were written by <PERSON>zel <PERSON> &lt;<EMAIL>&gt;

		<see>
			<link page="firststeps1">First steps</link>
		</see>
	</page>
</helpfile>

