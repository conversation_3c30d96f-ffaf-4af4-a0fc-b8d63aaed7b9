<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Creating a window</title>
<link name="Style" href="help.css" type="text/css" rel="stylesheet">
</head>
<body>
<h1>Creating a window</h1>
		This page will guide you through an example session using the
		Builder. The task is to create a dialog which will ask the user
		for his age.

		<h3>Creating the widget</h3>
		First, please select the tree item called "Widget Tree". When
		you create a widget, you always need to select the widget which
		should be the parent. In this case, the item "Widget Tree" is
		the root item, meaning that the widget should have no parent.
		<p align="center">
<img src="images/2_wtreeselected.png"><br>The widget tree root item</p>
<br>

		Press the "Create Frame" icon. In the upcoming dialog, just press OK.

		<p align="center">
<img src="images/2_frameicon.png"><br>The "Create Frame" icon</p>
<br>
		
<p align="center">
<img src="images/2_addwidget.png"><br>The "Add a widget" dialog</p>
<br>
		
		Now, the widget tree should have changed and display this:

		<p align="center">
<img src="images/2_wtreeframe.png"><br>The widget tree after the frame has been added</p>
<br>
		
<h3>Adding properties to the frame</h3>
		You have created the window, however it is not yet visible.
		To make it visible and to give it a title, you need to add some
		properties. Click on the "Add property" icon

		<p align="center">
<img src="images/2_addproperty.png"><br>The "Add property" icon</p>
<br>
		
		In the dialog, select the <b>size</b>, <b>title</b> and <b> visible</b> properties as shown in the screenshot.

		<p align="center">
<img src="images/2_addpropertydlg.png"><br>The "Add property" dialog</p>
<br>

        
<table cellspacing="0" cellpadding="1" align="center" class="note">
<tr>
<td>
<table cellspacing="0" cellpadding="1">
<tr class="label">
<td colspan="4">Note</td>
</tr>
<tr class="content">
<td width="3"></td><img src="images/help_note.png"><td width="3"></td><td>
			Click on the properties while holding the control<br>key to select multiple items
		</td><td width="3"></td>
</tr>
</table>
</td>
</tr>
</table>

		
<h3>Setting the properties</h3>
		Set the property <b>name</b> to "AgeTest". This is important once
		you want to recreate the dialog in your application. Set the
		<b>size</b> to 400, 100. Set the <b>title</b> to "Test Application".
		Don't be disturbed by the parentheses, they only indicate that the
		contained text will be internationalized. We will look into that later.
		Finally, set the visible flag and an empty window with your title
		will appear.
		<h3>The result</h3>
		
<p align="center">
<img src="images/2_result.png"><br>The created window</p>
<br>
		
<p>
<img src="images/help_see.png"><b>See also :</b>
<blockquote>
			
<a href="firststeps3.en_US.html">Adding content</a>
		
</blockquote>
</p>					 
	
</body>
</html>
