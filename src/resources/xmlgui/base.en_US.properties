xmlgui.wizard.back=< Back
xmlgui.wizard.next=Next >
xmlgui.wizard.cancel=Cancel
xmlgui.wizard.finish=Finish
xmlgui.button.ok=OK
xmlgui.button.ok.mnemonic=O
xmlgui.button.yes=Yes
xmlgui.button.yes.mnemonic=Y
xmlgui.button.no=No
xmlgui.button.no.mnemonic=N
xmlgui.button.cancel=Cancel
xmlgui.button.cancel.mnemonic=C
xmlgui.messagedialog.exception.title=Internal Error
xmlgui.messagedialog.exception=An internal error has occurred. Press 'More' to get additional information
xmlgui.messagedialog.more=More
xmlgui.messagedialog.less=Less
xmlgui.messagedialog.close=Close
xmlgui.messagedialog.close.mnemonic=C

xmlgui.validator.date.invalid=Invalid date
xmlgui.validator.number.invalid=Invalid number
xmlgui.validator.email.invalid=Invalid email address
xmlgui.validator.phone.invalid=Invalid phone number
xmlgui.validator.character.invalid=Invalid character
xmlgui.validator.empty=Cannot be empty
xmlgui.validator.file.notset=No file set
xmlgui.validator.file.existsnot=File does not exist
xmlgui.validator.file.notafile=Filename does not point to a file
xmlgui.validator.file.cantread=Cannot read this file

xmlgui.Widget=Abstract widget
xmlgui.Widget.property.anchor=The anchor inside the parent component's layout
xmlgui.Widget.property.background=The background color
xmlgui.Widget.property.foreground=The foreground color
xmlgui.Widget.property.font=The font
xmlgui.Widget.property.opaque=Determines whether the component's background should be drawn
xmlgui.Widget.property.enabled=Determines whether the component is enabled
xmlgui.Widget.property.helpid=The JavaHelp ID
xmlgui.Button=A clickable button
xmlgui.Button.property.text=The caption of this button
xmlgui.Button.property.mnemonic=The mnemonic of this button
xmlgui.Button.property.icon=The icon of this button
xmlgui.Button.property.rolloverIcon=The mouse roll-over icon of this button
xmlgui.Button.property.default=Determines whether this button is the main button of the dialog
xmlgui.Button.preset.ok=An OK button
xmlgui.Button.preset.cancel=A cancel button
xmlgui.Button.preset.hover=A button with a 'hover'-style border
xmlgui.Button.event.clicked=Invoked when the button is being clicked
xmlgui.ButtonGroup=A panel containing radio buttons
xmlgui.ButtonGroup.property.key=The button group's data model key
xmlgui.CheckBox=A check box
xmlgui.CheckBox.property.key=The data model key
xmlgui.CheckBox.property.text=The caption of this check box
xmlgui.ComboBox=A combo box
xmlgui.ComboBox.property.valuekey=The data model value key
xmlgui.ComboBox.property.indexkey=The data model index key
xmlgui.Console=A command line console
xmlgui.Console.event.command=Invoked when a command has been entered
xmlgui.Dialog=A top-level modal dialog
xmlgui.Dialog.property.border=The dialog border
xmlgui.Dialog.property.defaultCloseOperation=The action to be taken when the dialog is closed
xmlgui.Dialog.property.layout=The dialog layout
xmlgui.Dialog.property.location=The dialog position
xmlgui.Dialog.property.resizable=Determines whether the dialog is resizable
xmlgui.Dialog.property.size=The dialog size
xmlgui.Dialog.property.spacing=The dialog border spacing
xmlgui.Dialog.property.title=The dialog title
xmlgui.Dialog.event.close=Invoked when the dialog is being closed
xmlgui.Dialog.event.open=Invoked from the dialog's event loop once it is created
xmlgui.Frame=A top-level window
xmlgui.Frame.property.border=The frame border
xmlgui.Frame.property.defaultCloseOperation=The action to be taken when the frame is closed
xmlgui.Frame.property.layout=The frame layout
xmlgui.Frame.property.location=The frame position
xmlgui.Frame.property.resizable=Determines whether the frame is resizable
xmlgui.Frame.property.size=The frame size
xmlgui.Frame.property.spacing=The frame border spacing
xmlgui.Frame.property.title=The window title
xmlgui.Frame.property.visible=Determines whether the frame is visible
xmlgui.Frame.property.iconImage=The window manager icon of the frame
xmlgui.Frame.event.close=Invoked when the frame is being closed
xmlgui.Group=Panel which lays out components according to the LnF Design Guidelines
xmlgui.IconView=An icon view component
xmlgui.IconView.property.indexkey=The data model index key
xmlgui.IconView.property.valuekey=The data model value key
xmlgui.Item=A multi-purpose item (List, Combo, IconView)
xmlgui.Item.property.icon=The item icon
xmlgui.Item.property.text=The item text
xmlgui.Label=A label displaying some text, an icon or both
xmlgui.Label.property.text=The caption of this label
xmlgui.Label.property.horizontalAlignment=The horizontal alignment of this label
xmlgui.Label.property.verticalAlignment=The vertical alignment of this label
xmlgui.Label.property.icon=The icon of this label
xmlgui.LabeledWidget=Places a label in front of a widget
xmlgui.LabeledWidget.property.label.text=The label text of the labeled widget
xmlgui.LabeledWidget.property.mnemonic=The label mnemonic
xmlgui.List=A list with selectable entries
xmlgui.List.property.valuekey=The data model value key
xmlgui.List.property.indexkey=The data model index key
xmlgui.List.property.selectionMode=The selection mode (single/single_interval/multiple_interval)
xmlgui.List.property.verticalScrollBar=Defines whether the vertical scroll bar should be visible
xmlgui.List.property.horizontalScrollBar=Defines whether the horizontal scroll bar should be visible
xmlgui.List.event.rightclick=Invoked on a right button click
xmlgui.List.event.doubleclick=Invoked on a double click
xmlgui.Menu=A top level menu
xmlgui.Menu.property.mnemonic=The menu mnemonic
xmlgui.Menu.property.text=The menu caption
xmlgui.MenuBar=A menu bar
xmlgui.MenuItem=A menu item
xmlgui.MenuItem.property.accelerator=Key binding of the menu item
xmlgui.MenuItem.property.mnemonic=The menu item mnemonic
xmlgui.MenuItem.property.text=The menu item caption
xmlgui.MenuItem.event.selected=Invoked when the menu item is selected
xmlgui.OutlookBar=A widget well known from Microsoft Outlook
xmlgui.OutlookPanel=A panel to be embedded inside an Outlook Bar
xmlgui.Panel=An empty panel
xmlgui.Panel.property.border=The panel border
xmlgui.Panel.property.layout=The panel layout
xmlgui.PasswordField=A text field which hides its contents
xmlgui.PopupMenu=A popup menu
xmlgui.ProgressBar=A progress bar
xmlgui.ProgressBar.property.indeterminate=Defines whether the progress bar is in indeterminate mode
xmlgui.ProgressBar.property.stringPainted=Defines whether the percentage string is painted
xmlgui.ProgressBar.property.minimum=The progress bar minimum
xmlgui.ProgressBar.property.maximum=The progress bar maximum
xmlgui.ProgressBar.property.value=The progress bar value
xmlgui.RadioButton=A radio button
xmlgui.RadioButton.property.text=The caption of this radio button
xmlgui.RadioButton.property.text=The value of this radio button (useful with button groups)
xmlgui.Separator=A multi-purpose separator (Menu, ToolBar, PopupMenu, Group)
xmlgui.Spacer=A spacer for box layouts and other uses
xmlgui.Spacer.property.type=The spacer type (strut/glue)
xmlgui.Spacer.property.axis=The spacer axis (h/v)
xmlgui.Spacer.property.size=The spacer size
xmlgui.SplitPane=A component which separates two child components using a divider
xmlgui.SplitPane.property.orientation=The orientation (either 'h' or 'v')
xmlgui.SplitPane.property.resizeWeight=Determines how the available space is distributed (0.0 - 1.0)
xmlgui.TabbedPane=A tabbed pane
xmlgui.TabbedPane.property.closeicon=Lets you add a close icon to all tabs
xmlgui.TabbedPane.event.close=Invoked when the close-icon of a tab is clicked
xmlgui.TabbedPane.event.rightclick=Invoked when a right click on a tab is detected
xmlgui.Table=An advanced table component
xmlgui.Table.property.indexkey=The data model index key
xmlgui.Table.property.valuekey=The data model value key
xmlgui.Table.property.verticalScrollBar=Defines whether the vertical scroll bar should be visible
xmlgui.Table.property.horizontalScrollBar=Defines whether the horizontal scroll bar should be visible
xmlgui.Table.property.selectionMode=The selection mode (single/single_interval/multiple_interval)
xmlgui.Table.event.doubleclick=Invoked when a row receives a double click
xmlgui.Table.event.rightclick=Invoked when a row receives a right click
xmlgui.TextField=A text field
xmlgui.TextField.property.key=The data model key
xmlgui.TextField.event.activated=Dispatched when the user presses the enter key
xmlgui.TextPane=A text pane
xmlgui.TextPane.property.key=The data model key
xmlgui.TextPane.property.verticalScrollBar=Defines whether the vertical scroll bar should be visible
xmlgui.TextPane.property.horizontalScrollBar=Defines whether the horizontal scroll bar should be visible
xmlgui.ToolBar.property.floatable=Defines whether the tool bar should be floatable
xmlgui.ToolBar.property.borderPainted=Defines whether the border around the tool bar should be painted
xmlgui.ToolBar.property.rollover=Defines whether the tool bar buttons should have a 'rollover' effect
xmlgui.Tree=A tree component
xmlgui.Tree.property.key=The data model key
xmlgui.Tree.property.verticalScrollBar=Defines whether the vertical scroll bar should be visible
xmlgui.Tree.property.horizontalScrollBar=Defines whether the horizontal scroll bar should be visible
xmlgui.Tree.event.doubleclick=Invoked when a tree item receives a double click
xmlgui.Tree.event.rightclick=Invoked when a tree item receives a right click
xmlgui.SyntaxEditor=A syntax highlighting editor
xmlgui.SyntaxEditor.property.verticalScrollBar=Defines whether the vertical scroll bar should be visible
xmlgui.SyntaxEditor.property.horizontalScrollBar=Defines whether the horizontal scroll bar should be visible
xmlgui.SyntaxEditor.property.gutter=Defines whether the gutter should be visible
xmlgui.SyntaxEditor.property.lineHighlight=Defines whether line highlights should be visible
xmlgui.SyntaxEditor.property.bracketHighlight=Defines whether bracket highlights should be visible
xmlgui.SyntaxEditor.property.antiAlias=Defines whether anti aliasing should be active
xmlgui.SyntaxEditor.property.mode=The syntax mode of the component
xmlgui.SyntaxEditor.property.theme=The color theme of the component
