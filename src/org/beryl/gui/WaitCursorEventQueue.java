/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.awt.AWTEvent;
import java.awt.Component;
import java.awt.Cursor;
import java.awt.EventQueue;
import java.awt.MenuComponent;
import java.awt.MenuContainer;
import java.util.ArrayList;

import javax.swing.SwingUtilities;

/**
 * Change the cursor to an hour glass when necessary
 * 
 * Usage:
 * EventQueue waitQueue = new WaitCursorEventQueue(500);
 * Toolkit.getDefaultToolkit().getSystemEventQueue().push(waitQueue);
 * 
 * Part of the trick to this tip is choosing the duration after which the hourglass should appear. 
 * The delay should be long enough that the hourglass will not appear for most events processed 
 * within the UI thread, but it should be short enough so that the user perceives near-immediate 
 * feedback when a more intensive task begins processing. In moderate usability testing, 
 * a delay in the range of about 500 milliseconds appears to work quite well.
 * 
 * Taken from JavaWorld
 * Race condition fixed by Wenzel Jakob
 */
public class WaitCursorEventQueue extends EventQueue {
	private int delay = 0;
	private ArrayList waitTimers = null;
	
	public WaitCursorEventQueue(int delay) {
		this.delay = delay;
		waitTimers = new ArrayList();
		addWaitTimer();
	}

	private WaitCursorTimer addWaitTimer() {
		WaitCursorTimer waitTimer = new WaitCursorTimer();
		waitTimer.setDaemon(true);
		waitTimer.start();
		waitTimers.add(waitTimer);
		return waitTimer;
	}
	
	private WaitCursorTimer getWaitTimer() {
		synchronized (waitTimers) {
			for (int i=0; i<waitTimers.size(); i++) {
				WaitCursorTimer timer = (WaitCursorTimer) waitTimers.get(i);
				if (!timer.isUsed())
					return timer;
			}
			return addWaitTimer();
		}
	}

	protected void dispatchEvent(AWTEvent event) {
		WaitCursorTimer timer = getWaitTimer();
		timer.startTimer(event.getSource());
		try {
			super.dispatchEvent(event);
		} finally {
			timer.stopTimer();
		}
	}

	private class WaitCursorTimer extends Thread {
		private Object source = null;
		private Component parent = null;

		synchronized void startTimer(Object source) {
			this.source = source;
			notify();
		}
	
		synchronized boolean isUsed() {
			return source != null;
		}

		synchronized void stopTimer() {
			if (parent == null)
				interrupt();
			else {
				parent.setCursor(null);
				parent = null;
			}
			source = null;
		}

		public synchronized void run() {
			while (true) {
				try {
					/* wait for notification from startTimer() */
					wait();

					/* wait for event processing to reach the threshold,
					 * or interruption from stopTimer() */
					wait(delay);

					if (source instanceof Component)
						parent = SwingUtilities.getRoot((Component) source);
					else if (source instanceof MenuComponent) {
						MenuContainer mParent = ((MenuComponent) source).getParent();
						if (mParent instanceof Component)
							parent = SwingUtilities.getRoot((Component) mParent);
					}

					if (parent != null && parent.isShowing())
						parent.setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
				} catch (InterruptedException ie) {
					/* Ignore */
				}
			}
		}
	}
}