package org.beryl.gui.component;

import java.awt.BorderLayout;
import java.awt.Graphics;
import java.awt.Image;
import java.net.URL;

import javax.swing.ImageIcon;
import javax.swing.JPanel;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2013��10��9�� ����3:46:42 
 */
public class ImagePanel extends JPanel {

    private Image image = null;
    
    public ImagePanel(ImageIcon icon) {
    	if(icon != null)
    		image = icon.getImage();
    }
    
    public ImagePanel(ImageIcon icon, BorderLayout layout) {
    	super(layout);
    	if(icon != null)
    		image = icon.getImage();
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if(image != null)
        	g.drawImage(image, 0, 0, this.getWidth(), this.getHeight(), this);
    }
}