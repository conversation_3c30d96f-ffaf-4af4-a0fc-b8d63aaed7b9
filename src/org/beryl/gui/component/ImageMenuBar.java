package org.beryl.gui.component;

import java.awt.Graphics;
import java.awt.Image;

import javax.swing.ImageIcon;
import javax.swing.JMenuBar;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2013��10��9�� ����3:47:42 
 */
public class ImageMenuBar extends JMenuBar {

    private Image image = null;
    
    public ImageMenuBar(ImageIcon icon) {
    	if(icon != null)
    		image = icon.getImage();
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if(image != null)
        	g.drawImage(image, 0, 0, this.getWidth(), this.getHeight(), this);
    }
}