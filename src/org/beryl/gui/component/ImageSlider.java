package org.beryl.gui.component;

import java.awt.Color;
import java.awt.Graphics;
import java.awt.Image;

import javax.swing.ImageIcon;
import javax.swing.JSlider;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2013��10��10�� ����5:11:56 
 */
public class ImageSlider extends JSlider {

    private Image image = null;
    
    public ImageSlider(ImageIcon icon) {
    	if(icon != null)
    		image = icon.getImage();
    }
    
    public ImageSlider(ImageIcon icon, int min, int max, int value) {
        super(min, max, value);
        if(icon != null)
    		image = icon.getImage();
    }

    @Override
    protected void paintComponent(Graphics g) {
        if(image != null)
        	g.drawImage(image, 0, 0, this.getWidth(), this.getHeight(), this);
        super.paintComponent(g);
    }
}