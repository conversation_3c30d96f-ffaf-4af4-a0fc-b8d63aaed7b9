package org.beryl.gui.component;

import java.awt.Graphics;
import java.awt.Image;

import javax.swing.ImageIcon;
import javax.swing.JTabbedPane;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2014��1��5�� ����3:57:57 
 */
public class ImageTabbedPane extends JTabbedPane {

    private Image image;
    
    public ImageTabbedPane(ImageIcon icon) {
    	if(icon != null)
    		image = icon.getImage();
    }

    @Override
    protected void paintComponent(Graphics g) {
    	super.paintComponent(g);
        if(image != null)
        	g.drawImage(image, 0, 0, this.getWidth(), this.getHeight(), this);
    }
}