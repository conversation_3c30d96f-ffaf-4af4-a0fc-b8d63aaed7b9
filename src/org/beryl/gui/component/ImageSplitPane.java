package org.beryl.gui.component;

import java.awt.Graphics;
import java.awt.Image;
import java.io.File;
import java.io.IOException;
import java.net.URL;

import javax.imageio.ImageIO;
import javax.swing.ImageIcon;
import javax.swing.JPanel;
import javax.swing.JSplitPane;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2013��10��9�� ����7:50:42 
 */
public class ImageSplitPane extends JSplitPane {

    private Image image;
    
    public ImageSplitPane(ImageIcon icon) {
    	if(icon != null)
    		image = icon.getImage();
    }

    @Override
    protected void paintComponent(Graphics g) {
    	super.paintComponent(g);
        if(image != null)
        	g.drawImage(image, 0, 0, this.getWidth(), this.getHeight(), this);
    }
}