package org.beryl.gui.component;

import java.awt.Color;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Image;

import javax.swing.ImageIcon;
import javax.swing.JMenuItem;
import javax.swing.plaf.basic.BasicMenuItemUI;

import com.sun.java.swing.plaf.windows.WindowsMenuItemUI;
import com.sun.java.swing.plaf.windows.WindowsMenuUI;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2013��10��9�� ����4:50:52 
 */
public class ImageMenuItemUI extends WindowsMenuUI
{
    private Image image;  //image to paint 

    public ImageMenuItemUI(Image img)   
    {
    	super.selectionForeground = Color.blue;
        this.image=img;

        
    }
    @Override
    protected void paintBackground(Graphics g, JMenuItem menuItem, Color bgColor)
    {
    	super.paintBackground(g, menuItem, bgColor);
    	g.drawImage(this.image, 0,0,menuItem.getWidth(),menuItem.getHeight(), menuItem);
    	
//    	if(menuItem.isSelected())    // if mouse is entered . 
//        {
//            Graphics2D g2d=(Graphics2D)g;
//            //g2d.setComposite(CTransparentGraphics.makeComposite(0.15f)); 
//            super.paintBackground(g2d, menuItem,Color.BLUE);
//        }
       
    }
}

