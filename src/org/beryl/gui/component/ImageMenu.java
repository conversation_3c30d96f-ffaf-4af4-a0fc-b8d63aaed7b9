package org.beryl.gui.component;

import java.awt.Graphics;
import java.awt.Image;
import java.io.File;
import java.io.IOException;

import javax.imageio.ImageIO;
import javax.swing.ImageIcon;
import javax.swing.JMenu;

/** 
 * ��Ȩ����: ̩������ɷ����޹�˾��Ȩ����
 * ����˵��: 
 * ��    ��: ֣��
 * ��������: 2013��10��9�� ����3:47:42 
 */
public class ImageMenu extends JMenu {

    private Image image = null;
    
    public ImageMenu(ImageIcon icon) {
    	if(icon != null) {
	    	image = icon.getImage();
	        this.setUI(new ImageMenuItemUI(image));
    	}
    }
    
    public void setUI(ImageIcon icon) {
    	if(icon != null) {
	    	image = icon.getImage();
	    	this.setUI(new ImageMenuItemUI(image));
    	}
    }
}