package org.beryl.gui.component;

import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Graphics;
import java.awt.Image;
import java.awt.Insets;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.UIManager;
import javax.swing.border.Border;
import javax.swing.border.EtchedBorder;
import javax.swing.border.SoftBevelBorder;
import javax.swing.SwingConstants;
import javax.swing.border.BevelBorder;

public class BorderButton extends JButton {
	
	private Color bg;
	private Image image = null;
	
	public BorderButton(ImageIcon icon) {
    	if(icon != null)
    		image = icon.getImage();
    	init();
    }
	
	public BorderButton() {
		init();
	}
	
	public void init() {
		
		//setText("\u9010\u4EE4\u7968");
		//setIcon(new ImageIcon(BorderButton.class.getResource("c.png")));
		this.setBorder(new RoundBorder());
		this.addMouseListener(new MouseAdapter() {
			
			private RoundBorder border;
            private boolean isClick;
			@Override
			public void mouseEntered(MouseEvent e) {
				if(isClick){
					return;
				}
				bg = BorderButton.this.getBackground();
//				BorderButton.this.setBackground(new Color(68, 159, 155));
				BorderButton.this.setBackground(bg);
				border = new RoundBorder();
//				border.setBorderColor(new Color(53, 128, 123));
				border.setBorderColor(Color.gray);
				BorderButton.this.setBorder(border);
			}

			@Override
			public void mouseExited(MouseEvent e) {
				if(isClick){
					return;
				}
				if(BorderButton.this.isSelected()) {
					BorderButton.this.setBackground(bg);
				}
				BorderButton.this.setBackground(bg);

				border = new RoundBorder();
				border.setBorderColor(Color.gray);
				BorderButton.this.setBorder(border);
			}

//			@Override
//			public void mouseClicked(MouseEvent e) {
//				if(isClick){
//					isClick=false;
//					mouseExited(e);
//					return;
//				}
//				isClick=true;
//				BorderButton.this.setBorder(new EnterBorder(EnterBorder.LOWERED));
//			}
		});
	}
	
	public void setSelected() {
		this.setSelected(true);
		this.setOpaque(true);
		this.setBorderPainted(true);
		RoundBorder border = new RoundBorder();
		border.setBorderColor(Color.gray);
		this.setBorder(border);
		
		bg = new Color(247, 247, 196);
		this.setBackground(bg);
	}
	
	public void setUnSelected() {
		this.setSelected(false);
		this.setOpaque(false);
		this.setBorderPainted(false);
		RoundBorder border = new RoundBorder();
		border.setBorderColor(new Color(166, 166, 166));
		this.setBorder(border);
		
//		this.setBackground(bg);
	}
	@Override
	public void setEnabled(boolean b) {
		if (!b && model.isRollover()) {
			model.setRollover(false);
		}
		model.setEnabled(b);
		this.disable();
	}
	@Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if(image != null)
        	g.drawImage(image, 0, 0, this.getWidth(), this.getHeight(), this);
    }

	public static void main(String[] args) {
		
		try {
			UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
		}
		catch(Exception ex) {
			ex.printStackTrace();
		}
		
		JFrame frame = new JFrame();
		BorderButton btn = new BorderButton();
		btn.setPreferredSize(new Dimension(100, 100));
		frame.getContentPane().add(btn);
		frame.getContentPane().setLayout(new FlowLayout());
		frame.setVisible(true);
	}
}

class RoundBorder implements Border {
	private Color color = Color.BLUE;

	public void setBorderColor(Color c) {
		color = c;
	}

	public Insets getBorderInsets(Component c) {
		return new Insets(0, 10, 0, 10);
	}

	public boolean isBorderOpaque() {
		return false;
	}

	public void paintBorder(Component c, Graphics g, int x, int y, int width,
			int height) {
		// ʹ�ú�ɫ����������Ե����һ��Բ�Ǿ���
		g.setColor(color);
		g.drawRoundRect(0, 0, c.getWidth() - 1, c.getHeight() - 1, 5, 5);
	}
}

class EnterBorder extends SoftBevelBorder{
    private Color color = Color.gray;
	public EnterBorder(int bevelType) {
		super(bevelType);
	}
	

	public void setBorderColor(Color c) {
		color = c;
	}
	
}
