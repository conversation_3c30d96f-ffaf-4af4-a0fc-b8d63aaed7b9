/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui;

import java.awt.BorderLayout;
import java.util.StringTokenizer;

import org.w3c.dom.Element;

import cz.autel.dmi.HIGConstraints;

public class AnchorFactory {
	private static AnchorFactory factoryInstance = new AnchorFactory();
	public static HIGConstraints constraints = new HIGConstraints();

	public class BoxAnchor {
		private float alignmentX = 0.0f;
		private float alignmentY = 0.0f;
	
		public BoxAnchor() {
		}
	
		public BoxAnchor(float alignmentX, float alignmentY) {
			this.alignmentX = alignmentX;
			this.alignmentY = alignmentY;
		}

		public float getAlignmentX() {
			return alignmentX;
		}

		public float getAlignmentY() {
			return alignmentY;
		}

		public void setAlignmentX(float alignmentX) {
			this.alignmentX = alignmentX;
		}

		public void setAlignmentY(float alignmentY) {
			this.alignmentY = alignmentY;
		}
	}

	public Object constructAnchor(Element anchorNode) throws GUIException {
		String type = anchorNode.getAttribute("type");
		if (type.equals("hig") || type.equals(""))
			return constructHIGAnchor(anchorNode);
		else if (type.equals("box"))
			return constructBoxAnchor(anchorNode);
		else if (type.equals("border"))
			return constructBorderAnchor(anchorNode);
		else
			throw new GUIException("Unknown anchor type ["+type+"]");
	}

	public String constructBorderAnchor(Element anchorNode) throws GUIException {
		String border = anchorNode.getAttribute("border");
		if ("center".equals(border))
			return BorderLayout.CENTER;
		else if ("north".equals(border))
			return BorderLayout.NORTH;
		else if ("east".equals(border))
			return BorderLayout.EAST;
		else if ("south".equals(border))
			return BorderLayout.SOUTH;
		else if ("west".equals(border))
			return BorderLayout.WEST;
		else
			throw new GUIException("Invalid border anchor data");
	}
	
	public BoxAnchor constructBoxAnchor(Element anchorNode) throws GUIException {
		BoxAnchor anchor = new BoxAnchor();
		try {
			if (!anchorNode.getAttribute("alignx").equals(""))
				anchor.setAlignmentX(Float.parseFloat(anchorNode.getAttribute("alignx")));
			if (!anchorNode.getAttribute("aligny").equals(""))
				anchor.setAlignmentY(Float.parseFloat(anchorNode.getAttribute("aligny")));
			return anchor;
		} catch (NumberFormatException e) {
			throw new GUIException("Invalid box anchor data", e);
		}
	}
	
	public HIGConstraints constructHIGAnchor(Element anchorNode) throws GUIException {
		StringTokenizer pos = new StringTokenizer(anchorNode.getAttribute("pos"), ",");
		String align = anchorNode.getAttribute("align");
		int posCount = pos.countTokens();

		try {
			if (align.equals("") && anchorNode.getAttributes().getNamedItem("align") == null)
				align = "rltb";
			if (posCount == 2) {
				return constraints.rc(Integer.parseInt(pos.nextToken()), Integer.parseInt(pos.nextToken()), align);
			} else if (posCount == 3) {
				return constraints.rcwh(
					Integer.parseInt(pos.nextToken()),
					Integer.parseInt(pos.nextToken()),
					Integer.parseInt(pos.nextToken()),
					1,
					align);
			} else if (posCount == 4) {
				return constraints.rcwh(
					Integer.parseInt(pos.nextToken()),
					Integer.parseInt(pos.nextToken()),
					Integer.parseInt(pos.nextToken()),
					Integer.parseInt(pos.nextToken()),
					align);
			} else {
				throw new GUIException("Bad anchor argument count");
			}
		} catch (NumberFormatException e) {
			throw new GUIException("Invalid anchor data", e);
		}
	}

	public static AnchorFactory getInstance() {
		return factoryInstance;
	}
}
