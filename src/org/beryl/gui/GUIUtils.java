/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.awt.Component;
import java.awt.EventQueue;
import java.awt.Toolkit;
import java.io.PrintWriter;
import java.io.Writer;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Locale;
import java.util.Properties;

import javax.swing.LookAndFeel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

import org.apache.log4j.PropertyConfigurator;

/**
 * Miscellaneous functions to get the XML GUI ready for use and to perform common tasks
 */
public class GUIUtils {
	public static final String KUNSTSTOFF_LNF = "com.incors.plaf.kunststoff.KunststoffLookAndFeel";
	private static ArrayList widgets = new ArrayList();

	private static class ExceptionWriter extends Writer {
		public String exception = new String();

		/**
		 * Overwritten IO function (from <tt>java.io.Writer</tt>)
		 */
		public void close() {
		}

		/**
		 * Overwritten IO function (from <tt>java.io.Writer</tt>)
		 */
		public void flush() {
		}

		/**
		 * Overwritten IO function (from <tt>java.io.Writer</tt>)
		 */
		public void write(String string) {
		}

		/**
		 * Overwritten IO function (from <tt>java.io.Writer</tt>)
		 */
		public void write(char[] cbuf, int off, int len) {
			String str = new String(cbuf, off, len);
			exception += str + "\n";
		}

	};

	/**
	 * Convert a <tt>Throwable</tt> into a <tt>String</tt>
	 * @param throwable The <tt>Throwable</tt> to convert
	 * @return A <tt>String</tt> describing the <tt>Throwable</tt>
	 */
	public static String getStringForThrowable(Throwable throwable) {
		ExceptionWriter writer = new ExceptionWriter();
		throwable.printStackTrace(new PrintWriter(writer));
		return writer.exception;
	}

	/**
	 * Add the core internationalization & icons to the search path
	 */
	public static void initializeBase() throws GUIException {
		ImageIconFactory.addSearchPath("resources/xmlgui/icons");
		InternationalizationManager.addLanguageFile("resources/xmlgui/base");
	}

	/**
	 * MacOS X specific initialization
	 */
	public static void initializeOSX() {
		System.setProperty("apple.laf.useScreenMenuBar", "true");
	}

	/**
	 * Initialize the event queue
	 */
	public static void initializeEventQueue() {
		EventQueue waitQueue = new WaitCursorEventQueue(500);
		Toolkit.getDefaultToolkit().getSystemEventQueue().push(waitQueue);
	}

	/**
	 * Initialize log4j with a basic console appender
	 */
	public static void initializeLogging() throws GUIException {
		try {
			
			URL url = GUIUtils.class.getResource("/resources/xmlgui/log4j.properties");
			Properties properties = new Properties();
			properties.load(url.openStream());
			PropertyConfigurator.configure(properties);
		} catch (Exception e) {
			throw new GUIException("Error while initializing logging", e);
		}
	}

	/**
	 * Initialize a custom look and feel
	 */
	public static void initializeLookAndFeel(String className) throws GUIException {
		try {
			Class lnfClass = Class.forName(className);

			UIManager.setLookAndFeel((LookAndFeel) lnfClass.newInstance());
			UIManager.getLookAndFeelDefaults().put("ClassLoader", GUIUtils.class.getClassLoader());
		} catch (Exception e) {
			throw new GUIException("Error while initializing LookAndFeel");
		}
	}

	/**
	 * Initialize everything using the given locale
	 */
	public static void defaultInitialization(Locale locale) throws GUIException {
		if (locale != null)
			Locale.setDefault(locale);
		initializeLogging();
		initializeBase();
		initializeEventQueue();

		if (System.getProperty("os.name").equals("Mac OS X")) {
			initializeOSX();
		} 
//		else {
//			try {
//				initializeLookAndFeel(KUNSTSTOFF_LNF);
//			} catch (GUIException e) {
//				/* Ignore */
//			}
//		}
	}

	/**
	 * Initialize everything while taking the default locale
	 */
	public static void defaultInitialization() throws GUIException {
		defaultInitialization(null);
	}

	/**
	 * Dynamically update the look and feel of all widgets
	 */
	public static void updateAllWidgets() {
		synchronized (widgets) {
			for (int i=0; i<widgets.size(); i++) {
				WeakReference ref = (WeakReference) widgets.get(i);
				Widget referent = (Widget) ref.get();
				
				if (referent != null) {
					Component component = referent.getWidget();
					if (component != null)
						SwingUtilities.updateComponentTreeUI(component);
				}
			}
		}
	}

	/**
	 * Internally used by Frame/Dialogs to register themselves
	 * when they are created. Needed for dynamic Look And Feel
	 * changes
	 */
	public static void register(Widget widget) {
		WeakReference reference = new WeakReference(widget);
		reference.enqueue();
		synchronized (widgets) {
			widgets.add(reference);
		}
		System.gc();
	}
	
	/**
	 * Internally used by Frame/Dialogs to unregister themselves
	 * when they are finalized. Needed for dynamic Look And Feel
	 * changes
	 */
	public static void unregister(Widget widget) {
		synchronized (widgets) {
			for (Iterator i = widgets.iterator(); i.hasNext(); ) {
				WeakReference ref = (WeakReference) i.next();
				Object referent = ref.get();
				
				if (referent == widget || referent == null) {
					i.remove();
				}
			}
		}
	}
}
