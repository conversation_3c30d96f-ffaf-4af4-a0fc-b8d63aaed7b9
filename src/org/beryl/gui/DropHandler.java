/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui;

import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.awt.dnd.DropTargetDragEvent;
import java.awt.dnd.DropTargetDropEvent;
import java.awt.dnd.DropTargetEvent;
import java.awt.dnd.DropTargetListener;

/**
 * Internally used class to facilitate the use of DnD
 */
public class DropHandler implements DropTargetListener {
	private Widget widget;
	private String name;
	private DataFlavor[] flavors = null;
	private GUIEventListener listener = null;

	public DropHandler(Widget widget, GUIEventListener listener, String name, DataFlavor[] flavors) {
		this.widget = widget;
		this.listener = listener;
		this.flavors = flavors;
		this.name = name;
	}

	public void dragEnter(DropTargetDragEvent e) {
	}

	public void dragExit(DropTargetEvent e) {
	}

	public void dragOver(DropTargetDragEvent e) {
	}

	public void drop(DropTargetDropEvent e) {
		Transferable tr = e.getTransferable();
		DataFlavor[] trFlavors = tr.getTransferDataFlavors();

		try {
			for (int i = 0; i < trFlavors.length; i++) {
				for (int o = 0; o < flavors.length; o++) {
					if (trFlavors[i].match(flavors[o])) {
						e.acceptDrop(e.getDropAction());
						Object data = tr.getTransferData(trFlavors[i]);
						GUIEvent event = new GUIEvent(widget, name, e, data);						
						listener.eventOccured(event);
						e.dropComplete(true);
						return;
					}
				}
			}
		} catch (Exception ex) {
			e.dropComplete(false);
			throw new RuntimeException(ex);
		}
		e.rejectDrop();
	}

	public void dropActionChanged(DropTargetDragEvent e) {
	}
};
