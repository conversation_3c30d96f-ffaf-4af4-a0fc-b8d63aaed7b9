/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.table;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;

import javax.swing.ImageIcon;
import javax.swing.JLabel;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.widgets.Label;
import org.beryl.gui.widgets.Table;

public class ImageRenderer implements TableRenderer {
	private int maxImageSize;

	public ImageRenderer() {
		this(10);
	}

	public ImageRenderer(int maxImageSize) {
		this.maxImageSize = maxImageSize;
	}

	public Widget getRenderer(
		Table table,
		Object value,
		boolean isSelected,
		boolean hasFocus,
		TableRow row,
		String key)
		throws GUIException {

		Label label = new Label(null, null);
		ImageIcon icon = (ImageIcon) value;
		Image inImage = icon.getImage();

		/* Resize the image */
		double scale = (double) maxImageSize / (double) inImage.getHeight(null);

		if (inImage.getWidth(null) > inImage.getHeight(null)) {
			scale = (double) maxImageSize / (double) inImage.getWidth(null);
		}
		int scaledW = (int) (scale * inImage.getWidth(null));
		int scaledH = (int) (scale * inImage.getHeight(null));
		BufferedImage outImage = new BufferedImage(scaledW, scaledH, BufferedImage.TYPE_INT_RGB);
		AffineTransform tx = new AffineTransform();

		if (scale < 1.0d) {
			tx.scale(scale, scale);
		}

		Color bgColor = null;
		Graphics2D g2d = outImage.createGraphics();
		g2d.setColor(bgColor);
		g2d.fillRect(0, 0, scaledW, scaledH);
		g2d.drawImage(inImage, tx, null);
		g2d.dispose();

		label.setIcon(new ImageIcon(outImage));
		label.setProperty("horizontalAlignment", new Integer(JLabel.CENTER));
		label.setProperty("opaque", Boolean.TRUE);
		return label;
	}
}
