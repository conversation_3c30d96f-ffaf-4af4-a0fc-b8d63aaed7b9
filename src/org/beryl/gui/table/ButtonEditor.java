/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.table;

import javax.swing.BorderFactory;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.Table;

public class ButtonEditor implements TableEditor {
	private GUIEventListener listener = null;
	private String name = null;

	public ButtonEditor(String name, GUIEventListener listener) {
		this.listener = listener;
		this.name = name;
	}

	public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
		MapDataModel dataModel = new MapDataModel();
		dataModel.setValue("value", value);

		Panel panel = new Panel(null, null);
		panel.setProperty("border", BorderFactory.createEmptyBorder(1, 1, 1, 1));

		Button button = new Button(panel, null);
		button.setProperty("text", "Edit ...");
		button.addListener("clicked",  name, new GUIEventListener() {
			int counter = 0;

			public void eventOccured(GUIEvent event) {
				counter++;
				if (counter > 1) {
					/* Don't handle first click */
					listener.eventOccured(event);
				}
			}
		});
		panel.addChild(button, null);
		panel.recursiveSetDataModel(dataModel);
		return panel;
	}
}
