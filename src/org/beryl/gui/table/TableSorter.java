package org.beryl.gui.table;

/**
 * Enhancements:
 * - reverse lookups
 * - supports the Comparable interface
 * - ascending / descending can be toggled by clicking on a column head twice
 * <AUTHOR>
 */

/*
 * @(#)TableSorter.java	1.8 99/04/23
 *
 * Copyright (c) 1997-1999 by Sun Microsystems, Inc. All Rights Reserved.
 * 
 * Sun grants you ("Licensee") a non-exclusive, royalty free, license to use,
 * modify and redistribute this software in source and binary code form,
 * provided that i) this copyright notice and license appear on all copies of
 * the software; and ii) Licensee does not utilize the software in a manner
 * which is disparaging to <PERSON>.
 * 
 * This software is provided "AS IS," without a warranty of any kind. ALL
 * EXPRESS OR IMPLIED CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY
 * IMPLIED WARRANTY OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NON-INFRINGEMENT, ARE HEREBY EXCLUDED. SUN AND ITS LICENSORS SHALL NOT BE
 * LIABLE FOR ANY DAMAGES SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING
 * OR DISTRIBUTING THE SOFTWARE OR ITS DERIVATIVES. IN NO EVENT WILL SUN OR ITS
 * LICENSORS BE LIABLE FOR ANY LOST REVENUE, PROFIT OR DATA, OR FOR DIRECT,
 * INDIRECT, SPECIAL, CONSEQUENTIAL, INCIDENTAL OR PUNITIVE DAMAGES, HOWEVER
 * CAUSED AND REGARDLESS OF THE THEORY OF LIABILITY, ARISING OUT OF THE USE OF
 * OR INABILITY TO USE SOFTWARE, EVEN IF SUN HAS BEEN ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGES.
 * 
 * This software is not designed or intended for use in on-line control of
 * aircraft, air traffic, aircraft navigation or aircraft communications; or in
 * the design, construction, operation or maintenance of any nuclear
 * facility. Licensee represents and warrants that it will not use or
 * redistribute the Software for such purposes.
 */

/**
 * A sorter for TableModels. The sorter has a model (conforming to TableModel) 
 * and itself implements TableModel. TableSorter does not store or copy 
 * the data in the TableModel, instead it maintains an array of 
 * integers which it keeps the same size as the number of rows in its 
 * model. When the model changes it notifies the sorter that something 
 * has changed eg. "rowsAdded" so that its internal array of integers 
 * can be reallocated. As requests are made of the sorter (like 
 * getValueAt(row, col) it redirects them to its model via the mapping 
 * array. That way the TableSorter appears to hold another copy of the table 
 * with the rows in a different order. The sorting algorthm used is stable 
 * which means that it does not move around rows when its comparison 
 * function returns 0 to denote that they are equivalent. 
 *
 * @version 1.8 04/23/99
 * <AUTHOR> Milne
 */

import java.awt.Component;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.Date;
import java.util.Vector;

import javax.swing.Icon;
import javax.swing.JTable;
import javax.swing.SwingConstants;
import javax.swing.UIManager;
import javax.swing.event.TableModelEvent;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumnModel;
import javax.swing.table.TableModel;

import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;

public class TableSorter extends TableMap {
	private int indexes[];
	private int inverseIndexes[];
	private Vector sortingColumns = new Vector();
	private int singleSortingColumn = -1;
	private boolean ascending = true;
	private int compares;

	private SortedTableHeaderRenderer unSortedRenderer = null;
	private SortedTableHeaderRenderer sortedRenderer = null;
	
	
	public TableSorter() throws GUIException {
		indexes = new int[0]; // For consistency.
		inverseIndexes = new int[0];
		
		sortedRenderer = new SortedTableHeaderRenderer();
		unSortedRenderer = new SortedTableHeaderRenderer();
	}

	public void setModel(TableModel model) {
		singleSortingColumn = -1;
		super.setModel(model);
		reallocateIndexes();
		fireTableChanged(new TableModelEvent(this, TableModelEvent.HEADER_ROW));
	}

	public int compareRowsByColumn(int row1, int row2, int column) {
		Class type = model.getColumnClass(column);
		TableModel data = model;

		// Check for nulls

		Object o1 = data.getValueAt(row1, column);
		Object o2 = data.getValueAt(row2, column);

		// If both values are null return 0
		if (o1 == null && o2 == null) {
			return 0;
		} else if (o1 == null) { // Define null less than everything. 
			return -1;
		} else if (o2 == null) {
			return 1;
		}

		/* We copy all returned values from the getValue call in case
		an optimised model is reusing one object to return many values.
		The Number subclasses in the JDK are immutable and so will not be used in 
		this way but other subclasses of Number might want to do this to save 
		space and avoid unnecessary heap allocation. 
		*/
		if (type.getSuperclass() == java.lang.Number.class) {
			Number n1 = (Number) data.getValueAt(row1, column);
			double d1 = n1.doubleValue();
			Number n2 = (Number) data.getValueAt(row2, column);
			double d2 = n2.doubleValue();

			if (d1 < d2)
				return -1;
			else if (d1 > d2)
				return 1;
			else
				return 0;
		} else if (type == java.util.Date.class) {
			Date d1 = (Date) data.getValueAt(row1, column);
			long n1 = d1.getTime();
			Date d2 = (Date) data.getValueAt(row2, column);
			long n2 = d2.getTime();

			if (n1 < n2)
				return -1;
			else if (n1 > n2)
				return 1;
			else
				return 0;
		} else if (type == String.class) {
			String s1 = (String) data.getValueAt(row1, column);
			String s2 = (String) data.getValueAt(row2, column);
			int result = s1.compareTo(s2);

			if (result < 0)
				return -1;
			else if (result > 0)
				return 1;
			else
				return 0;
		} else if (type == Boolean.class) {
			Boolean bool1 = (Boolean) data.getValueAt(row1, column);
			boolean b1 = bool1.booleanValue();
			Boolean bool2 = (Boolean) data.getValueAt(row2, column);
			boolean b2 = bool2.booleanValue();

			if (b1 == b2)
				return 0;
			else if (b1) // Define false < true
				return 1;
			else
				return -1;
		} else if (type == Comparable.class) {
			Comparable c1 = (Comparable) data.getValueAt(row1, column);
			Comparable c2 = (Comparable) data.getValueAt(row2, column);
			return c1.compareTo(c2);
		} else {
			Object v1 = data.getValueAt(row1, column);
			String s1 = v1.toString();
			Object v2 = data.getValueAt(row2, column);
			String s2 = v2.toString();
			int result = s1.compareTo(s2);

			if (result < 0)
				return -1;
			else if (result > 0)
				return 1;
			else
				return 0;
		}
	}

	public int compare(int row1, int row2) {
		compares++;
		for (int level = 0; level < sortingColumns.size(); level++) {
			Integer column = (Integer) sortingColumns.elementAt(level);
			int result = compareRowsByColumn(row1, row2, column.intValue());
			if (result != 0)
				return ascending ? result : -result;
		}
		return 0;
	}

	public void reallocateIndexes() {
		int rowCount = model.getRowCount();

		// Set up a new array of indexes with the right number of elements
		// for the new data model.
		indexes = new int[rowCount];
		inverseIndexes = new int[rowCount];

		// Initialise with the identity mapping.
		for (int row = 0; row < rowCount; row++) {
			indexes[row] = row;
			inverseIndexes[row] = row;
		}
	}

	public void tableChanged(TableModelEvent e) {
		reallocateIndexes();

		if (singleSortingColumn != -1) {
			/** this is __expensive__ if many single rows are added to an existing table **/
			sortByColumn(singleSortingColumn);
		}
		super.tableChanged(e);
	}

	public void checkModel() {
		if (indexes.length != model.getRowCount()) {
			System.err.println("Sorter not informed of a change in model.");
		}
	}

	public void sort(Object sender) {
		checkModel();

		compares = 0;
		shuttlesort((int[]) indexes.clone(), indexes, 0, indexes.length);
		
		/* Fill inverse index array */
		for (int i=0; i<indexes.length; i++) {
			int inverseRow = indexes[i];
			inverseIndexes[inverseRow] = i;
		}
	}

	// This is a home-grown implementation which we have not had time
	// to research - it may perform poorly in some circumstances. It
	// requires twice the space of an in-place algorithm and makes
	// NlogN assigments shuttling the values between the two
	// arrays. The number of compares appears to vary between N-1 and
	// NlogN depending on the initial order but the main reason for
	// using it here is that, unlike qsort, it is stable.
	public void shuttlesort(int from[], int to[], int low, int high) {
		if (high - low < 2) {
			return;
		}
		int middle = (low + high) / 2;
		shuttlesort(to, from, low, middle);
		shuttlesort(to, from, middle, high);

		int p = low;
		int q = middle;

		/* This is an optional short-cut; at each recursive call,
		check to see if the elements in this subset are already
		ordered.  If so, no further comparisons are needed; the
		sub-array can just be copied.  The array must be copied rather
		than assigned otherwise sister calls in the recursion might
		get out of sinc.  When the number of elements is three they
		are partitioned so that the first set, [low, mid), has one
		element and and the second, [mid, high), has two. We skip the
		optimisation when the number of elements is three or less as
		the first compare in the normal merge will produce the same
		sequence of steps. This optimisation seems to be worthwhile
		for partially ordered lists but some analysis is needed to
		find out how the performance drops to Nlog(N) as the initial
		order diminishes - it may drop very quickly.  */

		if (high - low >= 4 && compare(from[middle - 1], from[middle]) <= 0) {
			for (int i = low; i < high; i++) {
				to[i] = from[i];
			}
			return;
		}

		// A normal merge. 

		for (int i = low; i < high; i++) {
			if (q >= high || (p < middle && compare(from[p], from[q]) <= 0)) {
				to[i] = from[p++];
			} else {
				to[i] = from[q++];
			}
		}
	}

	public void swap(int i, int j) {
		int tmp = indexes[i];
		indexes[i] = indexes[j];
		indexes[j] = tmp;
	}

	// The mapping only affects the contents of the data rows.
	// Pass all requests to these rows through the mapping array: "indexes".

	public Object getValueAt(int aRow, int aColumn) {
		checkModel();
		return model.getValueAt(indexes[aRow], aColumn);
	}

	public void setValueAt(Object aValue, int aRow, int aColumn) {
		checkModel();
		try {
			model.setValueAt(aValue, indexes[aRow], aColumn);
		} catch (IndexOutOfBoundsException e) {
			/* Ignore. This is sometimes thrown when a new table model is set and
			 * editingStopped() gets called  */
		}
	}

	public void sortByColumn(int column) {
		sortingColumns.removeAllElements();
		sortingColumns.addElement(new Integer(column));
		sort(this);
		super.tableChanged(new TableModelEvent(this));
	}

	// There is no-where else to put this. 
	// Add a mouse listener to the Table to trigger a table sort 
	// when a column heading is clicked in the JTable. 
	public void addMouseListenerToHeaderInTable(JTable table) {
		final TableSorter sorter = this;
		final JTable tableView = table;

		tableView.getTableHeader().setDefaultRenderer(unSortedRenderer);
		
		tableView.setColumnSelectionAllowed(false);
		MouseAdapter listMouseListener = new MouseAdapter() {
			public void mouseClicked(MouseEvent e) {
				TableColumnModel columnModel = tableView.getColumnModel();
				int viewColumn = columnModel.getColumnIndexAtX(e.getX());
				int column = tableView.convertColumnIndexToModel(viewColumn);
				if (e.getClickCount() == 1 && column != -1) {
					if (singleSortingColumn == column)
						ascending = !ascending;
					else
						ascending = true;
					sorter.sortByColumn(column);
					singleSortingColumn = column;
					
					// setting renderer 
					for (int i = 0; i < columnModel.getColumnCount(); i++) 
						columnModel.getColumn(i).setHeaderRenderer(unSortedRenderer);
					if (ascending)
						sortedRenderer.setIconType(SortedTableHeaderRenderer.ASCENDING);
					else
						sortedRenderer.setIconType(SortedTableHeaderRenderer.DESCENDING);
					columnModel.getColumn(column).setHeaderRenderer(sortedRenderer);
				}
			}
		};
		JTableHeader th = tableView.getTableHeader();
		th.addMouseListener(listMouseListener);
	}

	public int getSortedRowForRow(int row) {
		return inverseIndexes[row];
	}

	public int getRowForSortedRow(int row) {
		return indexes[row];
	}

	public class SortedTableHeaderRenderer extends DefaultTableCellRenderer {
		private Icon upIcon = null;
		private Icon downIcon = null;
		private Icon icon = null;
		private static final int ASCENDING = 1;
		private static final int DESCENDING = 2;

		public SortedTableHeaderRenderer() throws GUIException {
			upIcon = ImageIconFactory.getIcon("sort_up");
			downIcon = ImageIconFactory.getIcon("sort_down");
		}

		public void setIconType(int type) {
			if (type == ASCENDING) 
				icon = downIcon;
			if (type == DESCENDING) 
				icon = upIcon;
		}
		
		public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
			setFont(UIManager.getFont("TableHeader.font"));
			setForeground( UIManager.getColor("TableHeader.foreground") );
			setBackground( UIManager.getColor("TableHeader.background") );
			setBorder( UIManager.getBorder("TableHeader.cellBorder") );
			setValue(value);
			setIcon(icon);
			setHorizontalTextPosition(SwingConstants.LEADING);
			setHorizontalAlignment(SwingConstants.CENTER);
			return this;	
		}
}
}
