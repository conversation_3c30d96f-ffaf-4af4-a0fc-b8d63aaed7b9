/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.table;

import java.awt.BorderLayout;
import java.awt.Color;

import javax.swing.BorderFactory;
import javax.swing.JLabel;
import javax.swing.border.Border;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.widgets.Label;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.Table;

public class ColorRenderer implements TableRenderer {
	private Border selectedBorder = null;
	private Border unselectedBorder = null;

	private static String toHex(int num) {
		String val = Integer.toString(num, 16);
		if (val.length() == 1)
			val = "0" + val;
		return val;
	}

	public static String toString(Color color) {
		return "#" + toHex(color.getRed()) + toHex(color.getGreen()) + toHex(color.getBlue());
	}

	public Widget getRenderer(
		Table table,
		Object value,
		boolean isSelected,
		boolean hasFocus,
		TableRow row,
		String key)
		throws GUIException {
		Color color = (Color) value;
		Panel panel = new Panel(null, null);
		Label label = new Label(panel, null);

		label.setProperty("opaque", Boolean.TRUE);
		label.setProperty("background", color);

		int lightness = (color.getRed() + color.getBlue() + color.getGreen()) / 3;

		if (lightness > 127)
			label.setProperty("foreground", Color.black);
		else
			label.setProperty("foreground", Color.white);

		label.setProperty(
			"text",
			 toString(color));

		label.setProperty("horizontalAlignment", new Integer(JLabel.CENTER));
		label.setProperty("border", BorderFactory.createEmptyBorder(3, 3, 3, 3));
		panel.setProperty("layout", new BorderLayout());
		panel.addChild(label, BorderLayout.CENTER);
		return panel;
	}
}
