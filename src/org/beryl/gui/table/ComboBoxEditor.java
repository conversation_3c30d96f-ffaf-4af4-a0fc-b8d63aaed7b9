/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.table;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.widgets.ComboBox;
import org.beryl.gui.widgets.Table;

public class ComboBoxEditor implements TableEditor {
	private ListDataModel dataModel = null;

	public ComboBoxEditor(String values[]) {
		dataModel = new ListDataModel();
		try {
			for (int i = 0; i < values.length; i++) {
				dataModel.addValue(values[i]);
			}
		} catch (GUIException e) {
			/* Won't happen */
			throw new RuntimeException(e);
		}
	}

	public ComboBoxEditor(ListDataModel dataModel) {
		this.dataModel = dataModel;
	}

	public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
		ComboBox comboBox = new ComboBox(null, null);
		MapDataModel mapDataModel = new MapDataModel();
		mapDataModel.setValue("value", value);
		comboBox.setProperty("valuekey", "value");
		comboBox.setDataModel(mapDataModel);
		comboBox.setListDataModel(dataModel);
		return comboBox;
	}
}
