/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.awt.datatransfer.UnsupportedFlavorException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;


/**
 * Comfortable transferable subclass for easy drag & drop
 */
public class SimpleTransferable implements Transferable {
	private HashMap data = null;

	public SimpleTransferable() {
		data = new HashMap();
	}

	/**
	 * Add data of a given mime type
	 */
	public void addData(String mimeType, Object transferableData) throws GUIException {
		try {
			data.put(new DataFlavor(mimeType), transferableData);
		} catch (Exception e) {
			throw new GUIException("Error while creating data flavor", e);
		}
	}

	/**
	 * Retrieve data of a given mime type
	 */	
	public Object getData(String mimeType) throws GUIException {
		try {
			return data.get(new DataFlavor(mimeType));
		} catch (Exception e) {
			throw new GUIException("Error while creating data flavor", e);
		}
	}
	
	/* Transferable implementation */
	public Object getTransferData(DataFlavor flavor) throws UnsupportedFlavorException, IOException {
		return data.get(flavor);
	}

	public DataFlavor[] getTransferDataFlavors() {
		DataFlavor flavors[] = new DataFlavor[data.size()];
		int counter = 0;

		for (Iterator i = data.keySet().iterator(); i.hasNext(); ) {
			flavors[counter++] = (DataFlavor) i.next();
		}
		return flavors;
	}

	public boolean isDataFlavorSupported(DataFlavor flavor) {
		return data.containsKey(flavor);
	}
}
