/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.swing;

import java.awt.BorderLayout;
import java.awt.CardLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.ArrayList;
import java.util.HashMap;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JInternalFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;

import org.beryl.gui.GUIException;
import org.beryl.gui.InternationalizationManager;
import org.beryl.gui.WizardListener;
import org.beryl.gui.WizardPageAdapter;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.widgets.Wizard;
import org.beryl.gui.widgets.WizardPage;

import cz.autel.dmi.HIGConstraints;
import cz.autel.dmi.HIGLayout;

/**
 * Java XML WizardItem Framework
 * @version 2.0
 * <AUTHOR> Jakob
 */

public class JWizard extends JFrame {
	private static final Font headingFont = new Font("SansSerif", Font.BOLD, 16);
	private MapDataModel model = null;
	private ModelChangeListener modelListener = null;
	private CardLayout layout = null;
	private JPanel componentPanel = null;
	private ArrayList pages = null;
	private ArrayList listeners = null;
	private HashMap pagesByName = null;
	private JButton backButton = null;
	private JButton nextButton = null;
	private JButton cancelButton = null;
	private JLabel sideImage, titleLabel = null;
	private JBreakingLabel descriptionLabel = null;
	private Wizard widget = null;
	private int totalPages = 0;
	private int currentPage = 0;

	public JWizard(Wizard widget) {
		this.widget = widget;
		pages = new ArrayList();
		listeners = new ArrayList();
		layout = new CardLayout();
		pagesByName = new HashMap();
		componentPanel = new JPanel();
		componentPanel.setLayout(layout);

		JPanel buttonPanel = new JPanel();
		buttonPanel.setLayout(new BoxLayout(buttonPanel, BoxLayout.X_AXIS));
		buttonPanel.add(Box.createGlue());
		backButton = new JButton(InternationalizationManager.getString("xmlgui.wizard.back"));
		backButton.setActionCommand("wizard.back");
		backButton.setEnabled(false);
		backButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				back();
			}
		});
		buttonPanel.add(backButton);
		buttonPanel.add(Box.createRigidArea(new Dimension(5, 0)));

		nextButton = new JButton(InternationalizationManager.getString("xmlgui.wizard.next"));
		nextButton.setActionCommand("wizard.next");
		nextButton.setEnabled(false);
		nextButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				next();
			}
		});
		buttonPanel.add(nextButton);
		buttonPanel.add(Box.createRigidArea(new Dimension(15, 0)));
		cancelButton = new JButton(InternationalizationManager.getString("xmlgui.wizard.cancel"));
		cancelButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				fireCancel();
			}
		});
		buttonPanel.add(cancelButton);
		JPanel lowerPanel = new JPanel();
		lowerPanel.setLayout(new BoxLayout(lowerPanel, BoxLayout.Y_AXIS));
		lowerPanel.add(new JHorizontalSeparator(10));
		lowerPanel.add(Box.createVerticalStrut(5));
		lowerPanel.add(buttonPanel);
		lowerPanel.add(Box.createVerticalStrut(10));

		JPanel mainPanel = new JPanel();
		HIGLayout higLayout = new HIGLayout(new int[] { 0, 30, 0 }, new int[] {10, 30, 0, 0, 20, 50});
		higLayout.setColumnWeight(3, 1);
		higLayout.setRowWeight(3, 1);
		HIGConstraints constraints = new HIGConstraints();
		sideImage = new JLabel();
		titleLabel = new JLabel();
		descriptionLabel = new JBreakingLabel();
		titleLabel.setForeground(Color.black);
		titleLabel.setFont(headingFont);
		mainPanel.setLayout(higLayout);
		mainPanel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
		mainPanel.add(sideImage, constraints.rcwh(1,1,1,6, "rlt"));
		mainPanel.add(titleLabel, constraints.rcwh(2,3,1,1, "lt"));
		mainPanel.add(componentPanel, constraints.rc(4,3, "lrb"));
		mainPanel.add(descriptionLabel, constraints.rc(3,3));
		mainPanel.add(lowerPanel, constraints.rcwh(6,1,3,1));
		getContentPane().add(mainPanel, BorderLayout.CENTER);
		getRootPane().setDefaultButton(nextButton);
		setDefaultCloseOperation(JInternalFrame.DO_NOTHING_ON_CLOSE);

		addWindowListener(new WindowAdapter() {
		    public void windowClosing(WindowEvent e) {
		    	fireCancel();
		    }
		});

		setSize(480, 360);

		Dimension d = Toolkit.getDefaultToolkit().getScreenSize();
		int x = (int) ((d.getWidth() - getWidth()) / 2);
		int y = (int) ((d.getHeight() - getHeight()) / 2);
		setLocation(x, y);
		
		show();
	}

	public void setSideImage(ImageIcon icon) {
		sideImage.setIcon(icon);
	}

	public void setDataModel(MapDataModel newModel) throws GUIException {
		if (model != null && modelListener != null)
			model.removeModelChangeListener(modelListener);
		modelListener = new ModelChangeListener() {
			public void modelChanged(ModelChangeEvent e) throws GUIException {
				updateButtons();
			}
		};
		newModel.addModelChangeListener(modelListener);
	}

	public MapDataModel getDataModel() {
		return model;
	}

	public WizardPage getPage(String name) {
		return (WizardPage) pagesByName.get(name);
	}

	public void addPage(WizardPage page) {
		pages.add(page);
		pagesByName.put(page.getName(), page);
		componentPanel.add(page.getWidget(), page.getName());
		totalPages++;
		updateButtons();
		updatePanel();
	}

	private void back() {
		if (currentPage > 0) {
			WizardPage current = null;
			do {
				current = (WizardPage) pages.get(--currentPage);
			} while (!current.isEnabled());
			if (current.getAdapter() != null)
				current.getAdapter().preparePage(current);
			updateButtons();
			updatePanel();
		}
	}

	private void next() {
		if (currentPage < totalPages - 1) {
			WizardPage current = (WizardPage) pages.get(currentPage);
			WizardPageAdapter wpAdapter = current.getAdapter();
			if (wpAdapter != null)
				wpAdapter.preparePage(current);
			if (wpAdapter == null || wpAdapter.finalizePage(current)) {
				do {
					current = (WizardPage) pages.get(++currentPage);
				} while (!current.isEnabled());
				if (current.getAdapter() != null)
					current.getAdapter().preparePage(current);
				updateButtons();
				updatePanel();
			}
		} else {
			WizardPage current = (WizardPage) pages.get(currentPage);
			WizardPageAdapter wpAdapter = current.getAdapter();
			if (wpAdapter == null || wpAdapter.finalizePage(current)) {
				fireFinish();
			}
		}
	}

	private void fireCancel() {
		for (int i=0; i<listeners.size(); i++) {
			((WizardListener) listeners.get(i)).wizardCanceled(widget);
		}
		dispose();
	}
	
	private void fireFinish() {
		for (int i=0; i<listeners.size(); i++) {
			((WizardListener) listeners.get(i)).wizardFinished(widget);
		}
	}
	
	
	private void updatePanel() {
		WizardPage page = (WizardPage) pages.get(currentPage);
		titleLabel.setText(page.getTitle() != null ? page.getTitle() : "");
		descriptionLabel.setText(page.getDescription() != null ? page.getDescription() : "");
		layout.show(componentPanel, page.getName());
	}

	public void updateButtons() {
		WizardPage page = (WizardPage) pages.get(currentPage);
		WizardPageAdapter wpAdapter = page.getAdapter();

		if (page != null) {
			if (wpAdapter != null)
				nextButton.setEnabled(wpAdapter.isPageReady(page));
			else
				nextButton.setEnabled(true);
			if (currentPage < (totalPages - 1)) {
				nextButton.setText(InternationalizationManager.getString("xmlgui.wizard.next"));
			} else {
				nextButton.setText(InternationalizationManager.getString("xmlgui.wizard.finish"));
			}
			if (currentPage > 0)
				backButton.setEnabled(true);
			else
				backButton.setEnabled(false);
		} else {
			nextButton.setEnabled(false);
			backButton.setEnabled(false);
		}
	}

	public void addWizardListener(WizardListener listener) {
		listeners.add(listener);
	}

	public void removeWizardListener(WizardListener listener) {
		listeners.remove(listener);
	}


	public JButton getBackButton() {
		return backButton;
	}

	public JButton getNextButton() {
		return nextButton;
	}
}
