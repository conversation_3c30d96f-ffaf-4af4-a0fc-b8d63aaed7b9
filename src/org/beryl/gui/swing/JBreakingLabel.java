/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.swing;

import java.awt.Dimension;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.Insets;
import java.util.StringTokenizer;

import javax.swing.JComponent;
import javax.swing.UIManager;

public class JBreakingLabel extends JComponent {
	private String text = null;

	public JBreakingLabel() {
		this("");
	}

	public JBreakingLabel(String text) {
		setForeground(UIManager.getColor("Label.foreground"));
		setBackground(UIManager.getColor("Label.background"));
		setText(text);
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
		repaint();
	}

	public Dimension getMaximumSize() {
		return new Dimension(Short.MAX_VALUE, Short.MAX_VALUE);
	}

	public Dimension getPreferredSize() {
		return super.getPreferredSize();
	}

	public Dimension getMinimumSize() {
		return new Dimension(0, 0);
	}

	public void paintComponent(Graphics g) {
		Insets insets = getInsets();
		
		int width = getWidth() - insets.left - insets.right;
		int height = getHeight() - insets.top - insets.bottom - 1;

		g.setColor(getBackground());
		g.fillRect(insets.left, insets.top, width, height);
		g.setFont(getFont());
		g.setColor(getForeground());

		FontMetrics metrics = g.getFontMetrics(getFont());

		int xoffset = 0;
		int yoffset = metrics.getHeight();

		StringTokenizer tokenizer = new StringTokenizer(text, " ", true);
		while (tokenizer.hasMoreTokens()) {
			String token = tokenizer.nextToken();
			int tokenWidth = metrics.stringWidth(token);
			if (xoffset + tokenWidth > width) {
				yoffset += metrics.getHeight();
				xoffset = 0;
			}
			g.drawString(token, xoffset + insets.left, yoffset + insets.bottom);
			xoffset += tokenWidth;
		}
	}
}
