/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.swing;

import java.awt.Graphics;
import java.awt.Insets;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

/**
 * Manages the console text and attributes
 */
public class ConsoleBuffer {
	private List rows = null;
	private ConsoleAttribute defaultAttribute = null;
	JConsole console = null;

	/**
	 * Create the console buffer
	 */
	public ConsoleBuffer(JConsole console) {
		/* Setup the row buffer */
		clear();

		/* Set the parent console */
		this.console = console;

		/* Create a default attribute */
		defaultAttribute = new ConsoleAttribute();
	}

	/**
	 * Append text to the buffer
	 */
	public void append(String text) {
		append(text, defaultAttribute);
	}

	/**
	 * Append text with an attribute to the buffer
	 */
	public void append(String text, ConsoleAttribute attribute) {
		ConsoleRow row = (ConsoleRow) rows.get(rows.size() - 1);

		if (text.indexOf("\n") != -1 || text.indexOf("\b") != -1) {
			StringTokenizer tokenizer = new StringTokenizer(text, "\n\b", true);
			while (tokenizer.hasMoreTokens()) {
				String token = tokenizer.nextToken();

				if (token.equals("\n")) {
					row = new ConsoleRow(this);
					rows.add(row);
					console.dirty = true;
				} else if (token.equals("\b")) {
					int fragment = row.getFragmentCount() - 1;
					String fragmentText = row.getFragmentText(fragment);
					row.setFragmentText(
						fragment,
						fragmentText.substring(0, fragmentText.length() - 1));
				} else {
					row.appendFragment(token, attribute);
				}
			}
		} else {
			row.appendFragment(text, attribute);
		}
		console.updateScrollbar();
	}

	/**
	 * Paint the buffer
	 */
	void paintBuffer(Graphics g) {
		Insets insets = console.getInsets();
		int visibleRows = (console.height - insets.top - insets.bottom) / console.rowHeight;
		int max = rows.size() - console.offset;

		if (visibleRows > max)
			visibleRows = max;

		for (int i = 0; i < visibleRows; i++) {
			ConsoleRow row = ((ConsoleRow) rows.get(console.offset + i));

			if (console.dirty) {
				row.paintRow(g, i * console.rowHeight + insets.top);
			} else if (row.isDirty()) {
				g.setColor(console.getBackground());
				g.fillRect(
					0,
					i * console.rowHeight + insets.top,
					console.width,
					console.rowHeight);
				row.paintRow(g, i * console.rowHeight + insets.top);
			}
		}
	}

	/**
	 * Return a row
	 */
	public ConsoleRow getRow(int row) {
		return (ConsoleRow) rows.get(row);
	}

	/**
	 * Remove a row
	 */
	public void removeRow(int row) {
		rows.remove(row);
		console.updateScrollbar();
	}

	/**
	 * Get the row count
	 */
	public int getRowCount() {
		return rows.size();
	}

	/**
	 * Clear the buffer
	 */
	public void clear() {
		rows = new ArrayList();
		rows.add(new ConsoleRow(this));
		if (console != null) {
			console.offset = 0;
			console.setCursorPosition(0, 0);
			console.updateScrollbar();
		}
	}
}
