/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.swing;

import java.awt.Dimension;
import java.awt.Font;
import java.awt.Rectangle;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.HashSet;
import java.util.Iterator;

import javax.swing.JComponent;
import javax.swing.ListModel;
import javax.swing.LookAndFeel;
import javax.swing.Scrollable;
import javax.swing.UIManager;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import org.beryl.gui.swing.plaf.IconViewUI;

/**
 * JIconView - A simple Java icon view widget
 * @version 2.0
 * <AUTHOR> Jakob
 */

public class JIconView extends JComponent implements Scrollable {
	private static final String uiClassID = "IconViewUI";
	public static final String MODEL_PROPERTY = "model";
	public static final String XCELLSIZE_PROPERTY = "xCellSize";
	public static final String YCELLSIZE_PROPERTY = "yCellSize";
	public static final String ICONTEXTSPACING_PROPERTY = "iconTextSpacing";
	public static final String HANDCURSORENABLED_PROPERTY = "handCursorEnabled";

	private ListModel model = null;
	private int xCellSize = 0;
	private int yCellSize = 0;
	private int iconTextSpacing = 0;
	private int selectedIndex = -1;
	private boolean handCursorEnabled = false;
	private HashSet listeners = null;

	public JIconView() {
		this(null);
	}


	public JIconView(ListModel model) {
		super();
		setModel(model);
		setFont(new Font("SansSerif", Font.PLAIN, 10));
		xCellSize = yCellSize = 80;
		iconTextSpacing = 3;
		listeners = new HashSet();
		initialize();
		updateUI();
	}

	/* ========== Initialisation ========== */

	public static void initialize() {
		setLookAndFeelProperties(UIManager.getLookAndFeel());
		UIManager.addPropertyChangeListener(new PropertyChangeListener() {
			public void propertyChange(PropertyChangeEvent event) {
				if ("lookAndFeel".equals(event.getPropertyName())) {
					setLookAndFeelProperties((LookAndFeel) event.getNewValue());
				}
			}
		});
	}

	private static void setLookAndFeelProperties(LookAndFeel lookAndFeel) {
		UIManager.put("IconViewUI", "org.beryl.gui.swing.plaf.IconViewUI");
	}

	/* ========== Model ========== */

	public void setModel(ListModel model) {
		ListModel old = this.model;
		this.model = model;
		firePropertyChange(MODEL_PROPERTY, old, model);
	}

	public ListModel getModel() {
		return model;
	}

	/* ========== Spacing ========== */

	public int getXCellSize() {
		return xCellSize;
	}

	public int getYCellSize() {
		return yCellSize;
	}

	public int getIconTextSpacing() {
		return iconTextSpacing;
	}

	public void setXCellSize(int xCellSize) {
		int old = xCellSize;
		this.xCellSize = xCellSize;
		firePropertyChange(XCELLSIZE_PROPERTY, old, xCellSize);
	}

	public void setYCellSize(int yCellSize) {
		int old = yCellSize;
		this.yCellSize = yCellSize;
		firePropertyChange(YCELLSIZE_PROPERTY, old, yCellSize);
	}

	public void setIconTextSpacing(int iconTextSpacing) {
		int old = iconTextSpacing;
		this.iconTextSpacing = iconTextSpacing;
		firePropertyChange(ICONTEXTSPACING_PROPERTY, old, iconTextSpacing);
	}
	/* ========== Cursor ========== */
	
	public boolean getHandCursorEnabled() {
		return handCursorEnabled;
	}

	public void setHandCursorEnabled(boolean enabled) {
		boolean old = handCursorEnabled;
		handCursorEnabled = enabled;
		firePropertyChange(ICONTEXTSPACING_PROPERTY, old, handCursorEnabled);
		
	}
	/* ========== Listeners ========== */

	public void addListSelectionListener(ListSelectionListener listener) {
		listeners.add(listener);
	}

	public void removeListSelectionListener(ListSelectionListener listener) {
		listeners.remove(listener);
	}

	/* ========== Selection ========== */

	public int getSelectedIndex() {
		return selectedIndex;
	}

	public void setSelectedIndex(int selectedIndex) {
		if (model != null) {
			try {
				if (selectedIndex < model.getSize() && selectedIndex >= -1) {
					this.selectedIndex = selectedIndex;
					ListSelectionEvent event = new ListSelectionEvent(this, selectedIndex, selectedIndex, false);
					for (Iterator i = listeners.iterator(); i.hasNext();) {
						((ListSelectionListener) i.next()).valueChanged(event);
					}

				}
			} catch (ArrayIndexOutOfBoundsException e) {
			}
		}
	}

	/* ========== Scrollable ========== */

    public Dimension getPreferredScrollableViewportSize() {
    	return getPreferredSize();
    }


    public int getScrollableUnitIncrement(Rectangle visibleRect, int orientation, int direction) {
    	return 1;
	}

    public int getScrollableBlockIncrement(Rectangle visibleRect, int orientation, int direction) {
    	return 1;
	}

    public boolean getScrollableTracksViewportWidth() {
    	return true;
    }

    public boolean getScrollableTracksViewportHeight() {
    	return false;
    }

	/* ========== UI ========== */

	public IconViewUI getUI() {
		return (IconViewUI) ui;
	}

	public void setUI(IconViewUI ui) {
		super.setUI(ui);
	}

	public void updateUI() {
		setUI((IconViewUI) UIManager.getUI(this));
		invalidate();
	}

	public String getUIClassID() {
		return uiClassID;
	}
}
