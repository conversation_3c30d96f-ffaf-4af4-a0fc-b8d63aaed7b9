/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.swing;

import java.awt.Graphics;
import java.util.ArrayList;

/**
 * This class represents one row in the console
 */
public class ConsoleRow {
	private ArrayList fragments = null;
	private ConsoleBuffer buffer = null;
	private boolean dirty = false;

	private static class Fragment {
		public String text = null;
		public ConsoleAttribute attribute = null;

		public Fragment(String text, ConsoleAttribute attribute) {
			this.text = text;
			this.attribute = attribute;
		}

		public void insert(int offset, String string) {
			text = new StringBuffer(text).insert(offset, string).toString();
		}
	};

	/**
	 * Create a new row
	 */
	public ConsoleRow(ConsoleBuffer buffer) {
		this.buffer = buffer;
		fragments = new ArrayList();
	}

	/**
	 * Append a fragment to the row. If there is already a fragment with
	 * the same attributes, the two fragments are joined
	 */
	public void appendFragment(String text, ConsoleAttribute attribute) {
		if (fragments.size() == 0) {
			fragments.add(new Fragment(text, attribute));
		} else {
			Fragment last = (Fragment) fragments.get(fragments.size() - 1);
			if (last.attribute == attribute)
				last.text += text;
			else
				fragments.add(new Fragment(text, attribute));
		}
		dirty = true;
	}
	
	/**
	 * Get the number of fragments in this row
	 */
	public int getFragmentCount() {
		return fragments.size();
	}
	
	/**
	 * Get the text content of a fragment
	 */
	public String getFragmentText(int index) {
		return ((Fragment) fragments.get(index)).text;
	}

	/**
	 * Set the text content of a fragment
	 */
	public void setFragmentText(int index, String text) {
		((Fragment) fragments.get(index)).text = text;
		dirty = true;
	}

	/**
	 * Get the attribute content of a fragment
	 */
	public ConsoleAttribute getFragmentAttribute(int index) {
		return ((Fragment) fragments.get(index)).attribute;
	}

	/**
	 * Remove a fragment from the row
	 */
	public void removeFragment(int index) {
		fragments.remove(index);
		dirty = true;
	}

	/**
	 * Check whether this row needs repainting
	 */
	public boolean isDirty() {
		return dirty;
	}

	/**
	 * Mark this row to be repainted
	 */
	public void markDirty() {
		dirty = true;
	}

	/**
	 * Paint the row
	 */
	void paintRow(Graphics g, int y) {
		int x = buffer.console.getInsets().left;

		for (int i = 0, size = fragments.size(); i < size; i++) {
			Fragment fragment = (Fragment) fragments.get(i);
			ConsoleAttribute attribute = fragment.attribute;
			int width = buffer.console.columnWidth * fragment.text.length();

			if (attribute.isBold())
				g.setFont(buffer.console.boldFont);
			else
				g.setFont(buffer.console.plainFont);
			g.setColor(attribute.getBackgroundColor());
			g.fillRect(x, y, width, buffer.console.rowHeight);
			g.setColor(attribute.getForegroundColor());
			g.drawString(fragment.text, x, y + buffer.console.rowHeight - buffer.console.descent);

			x += width;
		}
		dirty = false;
	}
};
