/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.swing;

import java.awt.Color;

/**
 * This class encapsulates painting attributes such as
 * background color, foreground color and font style
 */
public class ConsoleAttribute {
	private Color backgroundColor = null;
	private Color foregroundColor = null;
	private boolean bold = false;

	/**
	 * Create a new attribute
	 */
	public ConsoleAttribute() {
		backgroundColor = Color.BLACK;
		foregroundColor = Color.LIGHT_GRAY;
		bold = false;
	}

	public Color getBackgroundColor() {
		return backgroundColor;
	}

	public boolean isBold() {
		return bold;
	}

	public Color getForegroundColor() {
		return foregroundColor;
	}

	public void setBackgroundColor(Color color) {
		backgroundColor = color;
	}

	public void setBold(boolean b) {
		bold = b;
	}

	public void setForegroundColor(Color color) {
		foregroundColor = color;
	}
}
