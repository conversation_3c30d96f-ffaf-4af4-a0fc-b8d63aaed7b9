/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.swing;

import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.util.ArrayList;

/**
 * JCommandConsole - Extended version of JConsole which is targeted
 * towards command line style use
 * @version 1.0
 * <AUTHOR>
 */
public class JCommandConsole extends JConsole {
	private ArrayList history = null;
	private ArrayList listeners = null;
	private String command = null;
	private String prompt = null;
	private int cursorIndex = 0;
	private int historyIndex = -1;

	private class ConsoleKeyListener extends KeyAdapter {
		public void keyPressed(KeyEvent event) {
			if (event.getKeyCode() == KeyEvent.VK_LEFT) {
				if (cursorIndex > 0) {
					cursorIndex--;
					setCursorPosition(getCursorRow(), getCursorColumn() - 1);
				}
			} else if (event.getKeyCode() == KeyEvent.VK_RIGHT) {
				if (cursorIndex < command.length()) {
					setCursorPosition(getCursorRow(), getCursorColumn() + 1);
					cursorIndex++;
				}
			} else if (event.getKeyCode() == KeyEvent.VK_UP) {
				if (historyIndex + 1 < history.size()) {
					historyIndex++;
					command = (String) history.get(historyIndex);
				}
				ConsoleRow row = getBuffer().getRow(getBuffer().getRowCount() - 1);
				row.setFragmentText(row.getFragmentCount() - 1, prompt + command);
				setCursorPosition(getBuffer().getRowCount() - 1, command.length() + prompt.length());
				cursorIndex = command.length();
			} else if (event.getKeyCode() == KeyEvent.VK_DOWN) {
				if (historyIndex > 0) {
					historyIndex--;
					command = (String) history.get(historyIndex);
				} else if (historyIndex == 0) {
					historyIndex--;
					command = "";
				}
				ConsoleRow row = getBuffer().getRow(getBuffer().getRowCount() - 1);
				row.setFragmentText(row.getFragmentCount() - 1, prompt + command);
				setCursorPosition(getBuffer().getRowCount() - 1, command.length() + prompt.length());
				cursorIndex = command.length();
			} else {
				return;
			}
			refresh();
		}

		public void keyTyped(KeyEvent event) {
			if (event.getKeyChar() == '\b') {
				if (cursorIndex > 0) {
					command = command.substring(0, cursorIndex - 1) + command.substring(cursorIndex);
					ConsoleRow row = getBuffer().getRow(getBuffer().getRowCount() - 1);
					row.setFragmentText(row.getFragmentCount() - 1, prompt + command);
					setCursorPosition(getCursorRow(), getCursorColumn() - 1);
					cursorIndex--;
				}
			} else if (event.getKeyChar() == KeyEvent.VK_DELETE) {
					if (cursorIndex < command.length()) {
						command = command.substring(0, cursorIndex) + command.substring(cursorIndex + 1);
						ConsoleRow row = getBuffer().getRow(getBuffer().getRowCount() - 1);
						row.setFragmentText(row.getFragmentCount() - 1, prompt + command);
					}
			} else if (event.getKeyChar() == '\n') {
				appendText("\n");
				fireCommand(command);
				appendText(prompt);
				setCursorPosition(getBuffer().getRowCount() - 1, prompt.length());
				command = "";
				cursorIndex = 0;
				scrollToBottom();
				historyIndex = -1;
			} else {
				String keyString = String.valueOf(event.getKeyChar());
				command = command.substring(0, cursorIndex) + event.getKeyChar() + command.substring(cursorIndex);
				ConsoleRow row = getBuffer().getRow(getBuffer().getRowCount() - 1);
				row.setFragmentText(row.getFragmentCount() - 1, prompt + command);
				setCursorPosition(getCursorRow(), getCursorColumn() + 1);
				cursorIndex++;
			}
			refresh();
		}
	}

	/**
	 * Create a new command console
	 */
	public JCommandConsole() {
		/* Install the key listener */
		addKeyListener(new ConsoleKeyListener());
		
		/* Empty command at startup */
		command = "";
		
		/* Default prompt */
		prompt = "$ ";
		
		/* History */
		history = new ArrayList();
		
		/* Listeners */
		listeners = new ArrayList();

		/* Cursor setup */
		setCursorPosition(0, prompt.length());
		setDrawCursor(true);
		
		/* Add a prompt */
		appendText(prompt);
	}

	/**
	 * Add a command listener to the console
	 */
	public void addCommandListener(CommandListener listener) {
		listeners.add(listener);
	}

	/**
	 * Remove a command listener from the console
	 */
	public void removeCommandListener(CommandListener listener) {
		listeners.remove(listener);
	}

	/**
	 * Append text to the console
	 */
	public void appendText(String text) {
		getBuffer().append(text);
	}

	/**
	 * Append text with an attribute to the console
	 */
	public void appendText(String text, ConsoleAttribute attribute) {
		getBuffer().append(text, attribute);
	}

	/**
	 * Clear the console
	 */
	public void clear() {
		getBuffer().clear();
		setCursorPosition(0, prompt.length());
		appendText(prompt);
	}

	/**
	 * Clear the history
	 */
	public void clearHistory() {
		history.clear();
	}
	
	/**
	 * Get the prompt
	 */
	public String getPrompt() {
		return prompt;
	}

	/**
	 * Set the prompt
	 */
	public void setPrompt(String string) {
		prompt = string;
	}

	private void fireCommand(String command) {
		history.add(0, command);
		
		for (int i=0; i<listeners.size(); i++) {
			((CommandListener) listeners.get(i)).onCommand(new CommandEvent(this, command));
		}
	}
}
