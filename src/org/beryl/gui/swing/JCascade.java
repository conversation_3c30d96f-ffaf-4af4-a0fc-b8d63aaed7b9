/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.swing;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;

import javax.swing.BorderFactory;
import javax.swing.BoxLayout;
import javax.swing.JButton;
import javax.swing.JComponent;
import javax.swing.JPanel;
import javax.swing.JScrollPane;

import org.beryl.gui.LFConstants;

public class JCascade extends JPanel {
	private ArrayList panels = null;

	public JCascade() {
		setLayout(new BoxLayout(this, BoxLayout.Y_AXIS));
		setBorder(BorderFactory.createLoweredBevelBorder());
		panels = new ArrayList();
	}
	
	public void addPanel(JCascadePanel panel) {
		JButton button = panel.getButton();
		JScrollPane scrollPane = new JScrollPane(panel);
		final JPanel container = new JPanel(new BorderLayout());
		container.add(scrollPane, BorderLayout.CENTER);

		button.setMaximumSize(new Dimension(LFConstants.MAX_WIDTH, LFConstants.DEFAULT_HEIGHT));
		button.setAlignmentX(1.0f);
		button.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				for (int i=0; i<panels.size(); i++) {
					JComponent c = (JComponent) panels.get(i);
					if (!c.isVisible())
						continue;
					c.setVisible(false);
				}
				container.setVisible(true);
			}
		});
		panels.add(container);
		add(panel.getButton());
		if (panels.size() != 1)
			container.setVisible(false);
		container.setAlignmentX(1.0f);
		add(container);
		panel.container = container;
	}
	
	public void removePanel(JCascadePanel panel) {
		remove(panel.getButton());
		remove(panel.container);
	}

	public Dimension getMinimumSize() {
		return new Dimension(0, 0);
	}
}