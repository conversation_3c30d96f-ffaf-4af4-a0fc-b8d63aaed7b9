/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.swing;

import java.awt.Dimension;
import java.awt.Graphics;

import javax.swing.JComponent;

public class JHorizontalSeparator extends JComponent {
	private int height = 0;

	public JHorizontalSeparator(int height) {
		this.height = height;
	}

	public void paint(Graphics g) {
		int w = this.getSize().width;
		int h = this.getSize().height;
		g.setColor(this.getBackground().darker());
		g.drawLine(0, h / 2 - 1, w, h / 2 - 1);
		g.setColor(this.getBackground().brighter());
		g.drawLine(0, h / 2, w, h / 2);
	}

	public Dimension getPreferredSize() {
		return new Dimension(100, height);
	}

	public Dimension getMaximumSize() {
		return new Dimension(Short.MAX_VALUE, height);
	}

	public Dimension getMinimumSize() {
		return new Dimension(0, height);
	}
}