/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.awt.Dimension;
import java.net.URL;

import javax.swing.ImageIcon;

import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.TextPane;

/**
 * General purpose message dialog
 */
public class MessageDialog extends Controller {
	private static ImageIcon less = null;
	private static ImageIcon more = null;
	private static ImageIcon error = null;
	private static ImageIcon warning = null;
	private static ImageIcon info = null;
	private MapDataModel dataModel = null;
	private Dialog dialog = null;
	private Panel containerPanel = null;
	private Button switchButton = null;
	private Dimension size = null;
	private Dimension bigSize = null;
	private boolean expanded = false;

	/* Message types */
	public static final int INFORMATION_MESSAGE = 1;
	public static final int WARNING_MESSAGE = 2;
	public static final int ERROR_MESSAGE = 3;

	public MessageDialog(Widget parent, String title, String message) throws GUIException {
		initialize(parent, INFORMATION_MESSAGE, title, message, null);
	}

	public MessageDialog(Widget parent, int type, String title, String message, String details) throws GUIException {
		initialize(parent, type, title, message, details);
	}

	public MessageDialog(Exception exception) {
		this(null, exception);
	}

	public MessageDialog(Widget parent, Exception exception) {
		try {
			log.error("An exception occured", exception);
			String exceptionString = GUIUtils.getStringForThrowable(exception);
			initialize(
				parent,
				ERROR_MESSAGE,
				getString("xmlgui.messagedialog.exception.title"),
				getString("xmlgui.messagedialog.exception"),
				exceptionString);
		} catch (Exception exception2) {
			log.error("However, due to some strange error, the XML GUI could not create an error dialog - the exception keeping the XML GUI from displaying the exception was ", exception2);
			System.exit(-1);
		}
	}

	private void initialize(Widget parent, int type, String title, String message, String details)
		throws GUIException {
		dataModel = new MapDataModel();

		size = new Dimension(500, 100);
		bigSize = new Dimension(500, 200);

		if (less == null) {
			less = ImageIconFactory.getIcon("less");
			more = ImageIconFactory.getIcon("more");
			error = ImageIconFactory.getIcon("error");
			warning = ImageIconFactory.getIcon("warning");
			info = ImageIconFactory.getIcon("info");
		}

		URL url = MessageDialog.class.getResource("MessageDialog.xml");
		dialog = constructDialog(url, "MessageDialog", dataModel);
		TextPane textPane = (TextPane) dialog.getWidget("MessagePane");
		switchButton = (Button) dialog.getWidget("SwitchButton");
		containerPanel = (Panel) dialog.getWidget("ContainerPanel");

		if (details == null) {
			switchButton.setEnabled(false);
		} else {
			TextPane detailPane = (TextPane) dialog.getWidget("DetailPane");
			detailPane.addText(details);
		}

		switch (type) {
			case INFORMATION_MESSAGE :
				textPane.addIcon(info);
				break;
			case WARNING_MESSAGE :
				textPane.addIcon(warning);
				break;
			case ERROR_MESSAGE :
				textPane.addIcon(error);
				break;
			default :
				throw new GUIException("Unknown message type");
		}

		textPane.addText("   " + message);

		dialog.removeChildWidget(containerPanel);

		dialog.initDialog(parent);
		dialog.setProperty("title", title);
		dialog.setProperty("size", size);
		dialog.show();
	}

	public void eventOccured(GUIEvent event) {
		try {
			if (event.getName().equals("switch")) {
				expanded = !expanded;

				switchButton.setProperty("text", getString("xmlgui.messagedialog." + (expanded ? "less" : "more")));
				switchButton.setProperty("icon", expanded ? less : more);

				if (expanded) {
					dialog.addChild(containerPanel, AnchorFactory.constraints.rc(2, 1));
					dialog.setProperty("size", bigSize);
				} else {
					dialog.removeChildWidget(containerPanel);
					dialog.setProperty("size", size);
				}
				dialog.revalidate();
				((javax.swing.JDialog) dialog.getWidget()).pack();
			} else if (event.getName().equals("close")) {
				dialog.dispose();
			}
		} catch (Exception e) {
			log.error("Error while processing a message dialog event", e);
		}
	}
}
