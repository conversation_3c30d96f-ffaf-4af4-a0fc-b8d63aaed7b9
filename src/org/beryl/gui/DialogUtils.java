/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui;

import java.io.File;
import java.io.FilenameFilter;
import java.net.URL;

import javax.swing.JFileChooser;
import javax.swing.filechooser.FileFilter;

import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.Label;

public class DialogUtils {
	public static final int RESULT_YES = 0;
	public static final int RESULT_NO = 1;
	public static final int RESULT_CANCEL = 2;

	/**
	 * Display a file open dialog
	 * @param extension The file extension to look for
	 */
	public static File showOpenFileDialog(Widget parent, String extension) {
		JFileChooser chooser = new JFileChooser();
		chooser.setFileFilter(new ExtensionFilter(extension, true));
		if (chooser.showOpenDialog(parent.getRealWidget()) == JFileChooser.APPROVE_OPTION) {
			return chooser.getSelectedFile();
		} else {
			return null;
		}
	}

	/**
	 * Display a file save dialog
	 * @param extension The file extension of the destination file
	 */
	public static File showSaveFileDialog(Widget parent, String extension) {
		JFileChooser chooser = new JFileChooser();
		chooser.setFileFilter(new ExtensionFilter(extension, true));
		if (chooser.showSaveDialog(parent.getRealWidget()) == JFileChooser.APPROVE_OPTION) {
			String path = chooser.getSelectedFile().getPath();
			if (!path.endsWith("." + extension))
				path = path + "." + extension;
			return new File(path);
		} else {
			return null;
		}
	}

	private static class YesNoCancelEventListener implements GUIEventListener {
		private int result = RESULT_CANCEL;
		
		public void eventOccured(GUIEvent event) {
			String name = event.getName();

			if (name.equals("yes"))
				result = RESULT_YES;
			else if (name.equals("no"))
				result = RESULT_NO;
			else if (name.equals("cancel"))
				result = RESULT_CANCEL;

			((Dialog) event.getSource().getParentWidgetByClass(Dialog.class)).dispose();
		}
		
		public int getResult() {
			return result;
		}
	};

	/**
	 * Show a dialog with a yes/no/cancel button
	 * @param frame The parent frame
	 * @param title The dialog title
	 * @param message The dialog message
	 * @return The result (RESULT_YES/RESULT_NO/RESULT_CANCEL)
	 */
	public static int showYesNoCancelDialog(Frame frame, String title, String message) throws GUIException {
		YesNoCancelEventListener listener = new YesNoCancelEventListener();
		URL url = DialogUtils.class.getResource("DialogUtils.xml");
		Dialog dialog = (Dialog) WidgetFactory.getInstance().constructWidget(url, "YesNoCancelDialog", listener);
		Label label = (Label) dialog.getWidget("DialogCaption");
		label.setProperty("text", message);
		dialog.initDialog(frame);
		dialog.setProperty("title", title);
		dialog.show();
		return listener.getResult();
	}

	/**
	 * A filter class which removes every file except
	 * (optionally) directories and files with a given extension
	 */
	public static class ExtensionFilter extends FileFilter implements FilenameFilter {
		private String extension = null;
		private boolean directories = false;
		
		public ExtensionFilter(String extension, boolean directories) {
			this.extension = "." + extension;
			this.directories = directories;
		}

		public boolean accept(File file) {
			return file.getName().endsWith(extension) || (directories && file.isDirectory());
		}

		public boolean accept(File file, String name) {
			file = new File(name);
			return file.getName().endsWith(extension) || (directories && file.isDirectory());
		}

		public String getDescription() {
			return "*" + extension;
		}
	}
}
