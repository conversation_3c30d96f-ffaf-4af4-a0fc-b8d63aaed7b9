/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 * 					  <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URL;
import java.util.Locale;
import java.util.Properties;

import javax.help.HelpSet;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.beryl.gui.Controller;
import org.beryl.gui.DialogUtils;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.GUIUtils;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.InternationalizationManager;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.XMLUtils;
import org.beryl.gui.widgets.Frame;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

public class Builder extends Controller {
	private static DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();

	/**
	 * Main window
	 */
	private Frame frame = null;

	/**
	 * Widget tree window
	 */
	private WidgetTree widgetTree = null;

	/**
	 * Currently open file
	 */
	private File file = null;

	/**
	 * DOM representation of the XML file
	 */
	private Document document = null;

	/**
	 * Indicates whether there have been changes since the last save
	 */
	private static boolean modified = false;


	/**
	 * Custom internationalization properties
	 */
	private Properties intlProps = null;
	
	/**
	 * Internationalization editor
	 */
	private InternationalizationEditor intlEditor = null;

	/**
	 * Look and feel chooser
	 */
	private LookAndFeelChooser lnfChooser = null;

	/**
	 * Custom internationalization source
	 */
	private class InternationalizationSource implements InternationalizationManager.InternationalizationSource {
		public String getString(String identifier) {
			return intlProps.getProperty(identifier);
		}
	};

	/**
	 * Constructor
	 * @throws GUIException
	 */
	public Builder() throws GUIException {
		URL url = Builder.class.getResource("Builder.xml");
		frame = (Frame) constructFrame(url, "Builder");
		widgetTree = new WidgetTree(this);
		intlProps = new Properties();
		InternationalizationManager.addInternationalizationSource(new InternationalizationSource());
		frame.show();
		doNew();
	}

	/**
	 * Create an empty document
	 * @throws GUIException
	 */
	private void doNew() throws GUIException {
		try {
			document = dbf.newDocumentBuilder().newDocument();
			Element uiElement = document.createElement("UI");
			uiElement.setAttribute("version", "1.0");
			document.appendChild(uiElement);
		} catch (ParserConfigurationException e) {
			throw new GUIException("Error while initializing XML parser", e);
		}
		modified = false;
		doRefresh();
	}

	/**
	 * Save the document to disk
	 * @throws GUIException
	 */
	private void doSave() throws GUIException {
		if (file == null) {
			doSaveAs();
		} else {
			try {
				PrintWriter writer = new PrintWriter(new FileOutputStream(file));
				writer.println(XMLUtils.serializeXML(document));
				writer.close();
			} catch (IOException e) {
				throw new GUIException("Could not create file", e);
			}
			modified = false;
		}
	}

	/**
	 * Popup a save as dialog
	 * @throws GUIException
	 */
	private void doSaveAs() throws GUIException {
		File newFile = DialogUtils.showSaveFileDialog(frame, "xml");
		if (newFile != null) {
			file = newFile;
			doSave();
		}
	}

	/**
	 * Generate a skeleton
	 */
	private void doSkeleton() throws GUIException {
		new SkeletonDialog(frame, document);
	}

	/**
	 * Popup an open dialog
	 * @throws GUIException
	 */
	private void doOpen() throws GUIException {
		File newFile = DialogUtils.showOpenFileDialog(frame, "xml");
		if (newFile != null) {
			try {
				document = dbf.newDocumentBuilder().parse(newFile);
			} catch (Exception e) {
				throw new GUIException("Could not open file", e);
			}
			file = newFile;
			modified = false;
			doRefresh();
		}
	}

	private boolean doAskSaveCancel() throws GUIException {
		if (modified) {
			switch (DialogUtils.showYesNoCancelDialog(frame, getString("builder.save.title"), getString("builder.save.label"))) {
				case DialogUtils.RESULT_YES:
					doSave();
					return true;
				case DialogUtils.RESULT_NO:
					return true;
				case DialogUtils.RESULT_CANCEL:
					return false;
			}
		}
		return true;
	}

	/**
	 * Mark the document as modified
	 */
	public static void markModified() {
		modified = true;
	}

	/**
	 * Refresh the views
	 */
	private void doRefresh() throws GUIException {
		widgetTree.refresh(document);
	}

	public void eventOccured(GUIEvent event) {
		String name = event.getName();
		try {
			if (name.equals("quit")) {
				if (doAskSaveCancel() && (intlEditor == null || intlEditor.doAskSaveCancel(frame)))
					System.exit(0);
			} else if (name.equals("open")) {
				if (doAskSaveCancel())
					doOpen();
			} else if (name.equals("save")) {
				doSave();
			} else if (name.equals("skeleton")) {
				doSkeleton();
			} else if (name.equals("saveAs")) {
				doSaveAs();
			} else if (name.equals("new")) {
				if (doAskSaveCancel())
					doNew();
			} else if (name.equals("lookandfeel")) {
				if (lnfChooser == null || !lnfChooser.isVisible())
					lnfChooser = new LookAndFeelChooser();
			} else if (name.equals("i18n")) {
				if (intlEditor == null || !intlEditor.isVisible())
					intlEditor = new InternationalizationEditor(widgetTree, intlProps);
			} else if (name.equals("help")) {
				showHelp("preface");
			} else if (name.equals("about")) {
				new About(frame);
			} else if (name.startsWith("insert:")) {
				widgetTree.doInsert(name.substring(7));
			}
		} catch (GUIException e) {
			new MessageDialog(e);
		}
	}

	/**
	 * Initialize log4j and start the GUI builder
	 * @param args Command-line parameters are ignored
	 */
	public static void main(String args[]) {
		try {
			Locale locale = Locale.US;
			
			if (Locale.getDefault().getLanguage().equals("de"))
				locale = new Locale("de", "DE");

			ClassLoader cl = Builder.class.getClassLoader();
			GUIUtils.defaultInitialization(locale);

			InternationalizationManager.addLanguageFile("resources/builder/builder");
			ImageIconFactory.addSearchPath("resources/builder/icons");
			Widget.setHelpSet(
				new HelpSet(
					cl,
					HelpSet.findHelpSet(cl, "resources/builder/help/builder." + locale.toString() + ".hs")));
			new Builder();
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}
