<?xml version="1.0" encoding="ISO-8859-1"?>
<UI version="1.0">
    <widget class="Frame" name="InternationalizationEditor">
        <property name="size" type="dimension">
            <width>600</width>
            <height>400</height>
        </property>
        <property name="title" type="istring">builder.i18n.title</property>
        <layout horiz="0,0" hweights="1,1" type="hig" vert="0,5,0" vweights="1,0,0"/>
        <widget class="Table" name="PropertyTable">
            <anchor align="lrtb" pos="1,1,2,1" type="hig"/>
            <property name="column.key">builder.common.name</property>
            <property name="column.value">builder.common.value</property>
            <property name="valuekey" type="string">property.value</property>
            <emit event="rightclick" name="popup"/>
        </widget>
        <widget class="Panel">
            <anchor align="lrtb" pos="3,1" type="hig"/>
            <widget class="Button">
                <property name="text" type="istring">builder.common.import</property>
                <property name="mnemonic" type="istring">builder.common.import.mnemonic</property>
                <emit event="clicked" name="import"/>
                <property name="icon" type="icon">open</property>
            </widget>
            <widget class="Button">
                <property name="text" type="istring">builder.common.save</property>
                <property name="mnemonic" type="istring">builder.common.save.mnemonic</property>
                <emit event="clicked" name="save"/>
                <property name="icon" type="icon">save</property>
            </widget>
            <layout type="lflow"/>
        </widget>
        <widget class="Panel">
            <anchor align="rtb" pos="3,2" type="hig"/>
            <layout type="rflow"/>
            <widget class="Button">
                <property name="text" type="istring">builder.common.add</property>
                <property name="mnemonic" type="istring">builder.common.add.mnemonic</property>
                <property name="icon" type="icon">add</property>
                <emit event="clicked" name="add"/>
            </widget>
            <widget class="Button">
                <property name="text" type="istring">builder.common.refresh</property>
                <property name="mnemonic" type="istring">builder.common.refresh.mnemonic</property>
                <emit event="clicked" name="refresh"/>
                <property name="icon" type="icon">refresh</property>
            </widget>
            <widget class="Button">
                <property name="text" type="istring">builder.common.clear</property>
                <property name="mnemonic" type="istring">builder.common.clear.mnemonic</property>
                <property name="icon" type="icon">clear</property>
                <emit event="clicked" name="clear"/>
            </widget>
        </widget>
    </widget>
    <widget class="Dialog" name="AddInternationalizationDialog">
        <property name="size" type="dimension">
            <width>300</width>
            <height>120</height>
        </property>
        <widget class="Group">
            <widget class="LabeledWidget">
                <widget class="TextField">
                    <property name="key" type="string">key</property>
                </widget>
                <property name="label.text" type="istring">builder.i18n.add.name</property>
            </widget>
            <widget class="LabeledWidget">
                <widget class="TextField">
                    <property name="key" type="string">value</property>
                </widget>
                <property name="label.text" type="istring">builder.i18n.add.value</property>
            </widget>
            <widget class="Button" preset="cancel">
                <emit event="clicked" name="cancel"/>
            </widget>
            <widget class="Button" preset="ok">
                <property name="default" type="bool">true</property>
                <emit event="clicked" name="ok"/>
            </widget>
        </widget>
        <property name="title" type="istring">builder.i18n.add.title</property>
    </widget>
    <widget class="PopupMenu" name="PropertyPopup">
        <widget class="MenuItem">
            <property name="text" type="istring">builder.common.delete</property>
            <property name="icon" type="icon">delete</property>
            <emit event="selected" name="delete"/>
        </widget>
    </widget>
</UI>
