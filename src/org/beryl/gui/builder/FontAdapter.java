/*
 * Beryl - A web platform based on XML, XSLT and Java This file is part of the
 * Beryl XML GUI
 * 
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 * 
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 2.1 of the License, or (at your option)
 * any later version.
 * 
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 * 
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation, Inc.,
 * 59 Temple Place, Suite 330, Boston, MA 02111-3107 USA
 */

package org.beryl.gui.builder;

import java.awt.BorderLayout;
import java.awt.Font;
import javax.swing.JLabel;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.ButtonEditor;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.Label;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.Table;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import say.swing.JFontChooser;


public class FontAdapter implements PropertyAdapter, GUIEventListener {
	private TableEditor fontEditor = null;

	public class FontRenderer implements TableRenderer {
		public Widget getRenderer(Table table, Object value,
				boolean isSelected, boolean hasFocus, TableRow row, String key)
				throws GUIException {
			Font font = (Font) value;
			Panel panel = new Panel(null, null);
			Label label = new Label(panel, null);
			label.setProperty("font", font);
			label.setProperty("text", font.getName());
			label.setProperty("horizontalAlignment", new Integer(
							JLabel.CENTER));
			panel.setProperty("layout", new BorderLayout());
			panel.addChild(label, BorderLayout.CENTER);
			return panel;
		}
	}
	public FontAdapter() {
		fontEditor = new ButtonEditor("edit", this) {
			public Widget getEditor(Table table, Object value, TableRow row,
					String key) throws GUIException {
				Widget widget = super.getEditor(table, value, row, key);
				MapDataModel dataModel = widget.getDataModel();
				dataModel.setValue("frame", table
						.getParentWidgetByClass(Frame.class));
				return widget;
			}
		};
	}
	public TableRenderer getRenderer() {
		return new FontRenderer();
	}
	public TableEditor getEditor() {
		return fontEditor;
	}
	public Object toValue(Object value, Element propertyNode) {
		return value;
	}
	public void toDOM(Object value, Element propertyNode) {
		Font font = (Font) value;
		Document document = propertyNode.getOwnerDocument();
		Element nameNode = document.createElement("name");
		Element styleNode = document.createElement("style");
		Element sizeNode = document.createElement("size");
		int styleID = font.getStyle();
		String style = "";
		if ((styleID & Font.BOLD) != 0 && (styleID & Font.ITALIC) != 0) {
			style = "bolditalic";
		} else if ((styleID & Font.BOLD) != 0) {
			style = "bold";
		} else if ((styleID & Font.ITALIC) != 0) {
			style = "italic";
		}
		nameNode.appendChild(document.createTextNode(font.getName()));
		styleNode.appendChild(document.createTextNode(style));
		sizeNode.appendChild(document.createTextNode(String.valueOf(font
				.getSize())));
		propertyNode.appendChild(nameNode);
		propertyNode.appendChild(styleNode);
		propertyNode.appendChild(sizeNode);
	}

	public void eventOccured(GUIEvent event) {
		try {
			JFontChooser fontChooser = new JFontChooser();
			fontChooser.setSelectedFont((Font) event.getSource().getDataModel().getValue("value"));
			
			int result = fontChooser.showDialog(((Frame) event.getSource()
					.getDataModel().getValue("frame")).getRealWidget());
			if (result == JFontChooser.OK_OPTION) {
				event.getSource().getDataModel().setValue("value",
						fontChooser.getSelectedFont());
			}
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}