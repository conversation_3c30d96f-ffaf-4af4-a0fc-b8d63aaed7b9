<?xml version="1.0" encoding="ISO-8859-1"?>
<UI version="1.0">
    <widget class="Dialog" name="HIGEditor">
        <layout type="vbox"/>
        <property name="title">builder.higeditor.title</property>
        <property name="spacing" type="int">3</property>
        <property name="size" type="dimension">
            <width>500</width>
            <height>400</height>
        </property>
        <widget class="Panel">
            <layout type="hbox"/>
            <widget class="Panel">
                <layout type="vbox"/>
                <property border="titled" name="border" type="border">
                    <title>builder.higeditor.rows</title>
                </property>
                <widget class="Panel">
                    <widget class="Group">
                        <widget class="LabeledWidget">
                            <property name="label.text">builder.higeditor.pixels</property>
                            <widget class="TextField" name="RowPixels">
                                <property name="key">rowpixels</property>
                            </widget>
                        </widget>
                        <widget class="LabeledWidget">
                            <property name="label.text">builder.higeditor.weight</property>
                            <widget class="ComboBox">
                                <property name="indexkey">rowweight</property>
                                <widget class="Item">
                                    <property name="text" type="string">0</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">1</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">2</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">3</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">4</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">5</property>
                                </widget>
                            </widget>
                        </widget>
                        <widget class="Button">
                            <property name="text">builder.higeditor.insbefore</property>
                            <emit event="clicked" name="insert_row_before"/>
                        </widget>
                        <widget class="Button">
                            <property name="text">builder.higeditor.insafter</property>
                            <emit event="clicked" name="insert_row_after"/>
                        </widget>
                    </widget>
                </widget>
                <widget class="Spacer">
                    <property name="type" type="string">strut</property>
                    <property name="axis" type="string">v</property>
                    <property name="size" type="int">5</property>
                </widget>
                <widget class="Separator"/>
                <widget class="Spacer">
                    <property name="type" type="string">strut</property>
                    <property name="axis" type="string">v</property>
                    <property name="size" type="int">5</property>
                </widget>
                <widget class="Table" name="RowsTable">
                    <property name="indexkey">row</property>
                    <property name="selectionMode" type="enum">single</property>
                    <property name="column.index">builder.higeditor.col.index</property>
                    <property name="column.pixels">builder.higeditor.col.pixels</property>
                    <property name="column.weight">builder.higeditor.col.weight</property>
                </widget>
                <widget class="Panel">
                    <layout type="rflow"/>
                    <widget class="Button">
                        <property name="icon" type="icon">arrow_up</property>
                        <property name="toolTipText" type="istring">builder.wtree.moveup</property>
                        <emit event="clicked" name="move_up_row"/>
                    </widget>
                    <widget class="Button">
                        <property name="icon" type="icon">arrow_down</property>
                        <property name="toolTipText" type="istring">builder.wtree.movedown</property>
                        <emit event="clicked" name="move_down_row"/>
                    </widget>
                    <widget class="Button">
                        <property name="icon" type="icon">delete</property>
                        <property name="toolTipText" type="istring">builder.common.delete</property>
                        <emit event="clicked" name="delete_row"/>
                    </widget>
                </widget>
            </widget>
            <widget class="Panel">
                <layout type="vbox"/>
                <property border="titled" name="border" type="border">
                    <title>builder.higeditor.columns</title>
                </property>
                <widget class="Panel">
                    <widget class="Group">
                        <widget class="LabeledWidget">
                            <property name="label.text">builder.higeditor.pixels</property>
                            <widget class="TextField" name="ColumnPixels">
                                <property name="key">columnpixels</property>
                            </widget>
                        </widget>
                        <widget class="LabeledWidget">
                            <property name="label.text">builder.higeditor.weight</property>
                            <widget class="ComboBox">
                                <property name="indexkey">columnweight</property>
                                <widget class="Item">
                                    <property name="text" type="string">0</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">1</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">2</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">3</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">4</property>
                                </widget>
                                <widget class="Item">
                                    <property name="text" type="string">5</property>
                                </widget>
                            </widget>
                        </widget>
                        <widget class="Button">
                            <property name="text">builder.higeditor.insbefore</property>
                            <emit event="clicked" name="insert_column_before"/>
                        </widget>
                        <widget class="Button">
                            <property name="text">builder.higeditor.insafter</property>
                            <emit event="clicked" name="insert_column_after"/>
                        </widget>
                    </widget>
                </widget>
                <widget class="Spacer">
                    <property name="type" type="string">strut</property>
                    <property name="axis" type="string">v</property>
                    <property name="size" type="int">5</property>
                </widget>
                <widget class="Separator"/>
                <widget class="Spacer">
                    <property name="type" type="string">strut</property>
                    <property name="axis" type="string">v</property>
                    <property name="size" type="int">5</property>
                </widget>
                <widget class="Table" name="ColumnsTable">
                    <property name="indexkey">column</property>
                    <property name="selectionMode" type="enum">single</property>
                    <property name="column.index">builder.higeditor.col.index</property>
                    <property name="column.pixels">builder.higeditor.col.pixels</property>
                    <property name="column.weight">builder.higeditor.col.weight</property>
                </widget>
                <widget class="Panel">
                    <layout type="rflow"/>
                    <widget class="Button">
                        <property name="icon" type="icon">arrow_up</property>
                        <property name="toolTipText" type="istring">builder.wtree.moveup</property>
                        <emit event="clicked" name="move_up_column"/>
                    </widget>
                    <widget class="Button">
                        <property name="icon" type="icon">arrow_down</property>
                        <property name="toolTipText" type="istring">builder.wtree.movedown</property>
                        <emit event="clicked" name="move_down_column"/>
                    </widget>
                    <widget class="Button">
                        <property name="icon" type="icon">delete</property>
                        <property name="toolTipText" type="istring">builder.common.delete</property>
                        <emit event="clicked" name="delete_column"/>
                    </widget>
                </widget>
            </widget>
        </widget>
        <widget class="Panel">
            <layout type="rflow"/>
            <widget class="Button" preset="cancel">
                <emit event="clicked" name="cancel"/>
            </widget>
            <widget class="Button" name="OKButton" preset="ok">
                <emit event="clicked" name="ok"/>
            </widget>
            <widget class="Button" name="apply">
                <property name="text">builder.common.apply</property>
                <property name="default" type="bool">true</property>
                <emit event="clicked" name="apply"/>
            </widget>
        </widget>
    </widget>
</UI>
