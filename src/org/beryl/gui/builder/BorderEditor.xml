<?xml version="1.0" encoding="iso-8859-1"?>

<UI version="1.0">
	<widget name="BorderEditor" class="Dialog">
		<property name="title">builder.bdeditor.title</property>

		<property name="size" type="dimension">
			<width>500</width>
			<height>300</height>
		</property>

		<widget name="Group" class="Group">
			<widget class="LabeledWidget">
				<property name="label.text">builder.bdeditor.type</property>

				<widget class="ComboBox">
					<property name="valuekey">type</property>
					
					<widget name="none" class="Item">
						<property name="text" type="string">none</property>
					</widget>
					<widget name="empty" class="Item">
						<property name="text" type="string">empty</property>
					</widget>
					<widget name="titled" class="Item">
						<property name="text" type="string">titled</property>
					</widget>
					<widget name="raised" class="Item">
						<property name="text" type="string">raised</property>
					</widget>
					<widget name="lowered" class="Item">
						<property name="text" type="string">lowered</property>
					</widget>
					<widget name="etched" class="Item">
						<property name="text" type="string">etched</property>
					</widget>
				</widget>
			</widget>
			
			<widget class="Separator"/>
			
			<widget class="Button" preset="cancel">
				<emit event="clicked" name="cancel"/>
			</widget>

			<widget name="OKButton" class="Button" preset="ok">
				<property name="default" type="bool">true</property>
				<emit event="clicked" name="ok"/>
			</widget>
		</widget>
	</widget>


	<!-- Custom components -->

	<widget name="Empty_Top" class="LabeledWidget">
		<property name="label.text">builder.bdeditor.top</property>
		<widget class="TextField">
			<property name="key">empty_top</property>
		</widget>
	</widget>

	<widget name="Empty_Bottom" class="LabeledWidget">
		<property name="label.text">builder.bdeditor.bottom</property>
		<widget class="TextField">
			<property name="key">empty_bottom</property>
		</widget>
	</widget>

	<widget name="Empty_Left" class="LabeledWidget">
		<property name="label.text">builder.bdeditor.left</property>
		<widget class="TextField">
			<property name="key">empty_left</property>
		</widget>
	</widget>

	<widget name="Empty_Right" class="LabeledWidget">
		<property name="label.text">builder.bdeditor.right</property>
		<widget class="TextField">
			<property name="key">empty_right</property>
		</widget>
	</widget>

	<widget name="Titled_Title" class="LabeledWidget">
		<property name="label.text">builder.bdeditor.title</property>
		<widget class="TextField">
			<property name="key">titled_title</property>
		</widget>
	</widget>
</UI>
