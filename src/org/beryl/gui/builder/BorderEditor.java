/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetFactory;
import org.beryl.gui.XMLUtils;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.validators.IntegerValidator;
import org.beryl.gui.validators.ValidationException;
import org.beryl.gui.validators.Validator;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Group;
import org.beryl.gui.widgets.LabeledWidget;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

public class BorderEditor extends Controller implements ModelChangeListener {
	private Widget parent = null;
	private Dialog dialog = null;
	private Group group = null;
	private MapDataModel dataModel = null;
	private Element borderNode = null;
	private ArrayList activeComponents = null;
	private HashMap customComponents = null;
	private Button okButton = null;
	private MapDataModel editorModel = null;

	public BorderEditor(Widget parent, MapDataModel editorModel) throws GUIException {
		this.borderNode = ((PropertyTableRow) editorModel.getValue("row")).getPropertyNode();
		this.parent = parent;
		this.editorModel = editorModel;

		dataModel = new MapDataModel();
		URL url = BorderEditor.class.getResource("BorderEditor.xml");
		dialog = (Dialog) constructDialog(url, "BorderEditor", dataModel);
		group = (Group) dialog.getWidget("Group");
		okButton = (Button) dialog.getWidget("OKButton");

		customComponents = new HashMap();
		activeComponents = new ArrayList();

		loadCustomComponent("empty", "Empty_Top", new IntegerValidator());
		loadCustomComponent("empty", "Empty_Bottom", new IntegerValidator());
		loadCustomComponent("empty", "Empty_Left", new IntegerValidator());
		loadCustomComponent("empty", "Empty_Right", new IntegerValidator());
		loadCustomComponent("titled", "Titled_Title", null);

		doLoad();
		dataModel.addModelChangeListener(this);

	}

	public void show() {
		try {
			dialog.initDialog(parent);
			dialog.show();
		} catch (GUIException e) {
			new MessageDialog(e);
		}
	}

	private void loadCustomComponent(String layoutName, String name, Validator validator) throws GUIException {
		URL url = BorderEditor.class.getResource("BorderEditor.xml");
		Widget widget = WidgetFactory.getInstance().constructWidget(url, name, this, dataModel, group);
		if (validator != null) {
			((LabeledWidget) widget).getDataWidget().addValidator(validator);
		}

		ArrayList components = (ArrayList) customComponents.get(layoutName);

		if (components == null) {
			components = new ArrayList();
			customComponents.put(layoutName, components);
		}

		components.add(widget);
	}

	private void activateType(String type) throws GUIException {
		for (int i = 0; i < activeComponents.size(); i++) {
			group.removeChildWidget((Widget) activeComponents.get(i));
		}

		ArrayList list = (ArrayList) customComponents.get(type);
		if (list != null) {
			for (int i = 0; i < list.size(); i++) {
				group.addChild((Widget) list.get(i), null);
				activeComponents.add(list.get(i));
			}
		}

		group.revalidate();
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (e instanceof MapChangeEvent) {
			MapChangeEvent event = (MapChangeEvent) e;

			if (event.getKey().equals("type")) {
				activateType(event.getNewValue().toString());
			}
		}

		try {
			for (int i = 0; i < activeComponents.size(); i++) {
				Widget widget = (Widget) activeComponents.get(i);

				widget.recursiveValidate();
			}
			okButton.setEnabled(true);
		} catch (ValidationException ex) {
			okButton.setEnabled(false);
		}
	}

	private void doLoad() throws GUIException {
		String type = borderNode.getAttribute("border");

		dataModel.setValue("empty_top", "0");
		dataModel.setValue("empty_bottom", "0");
		dataModel.setValue("empty_left", "0");
		dataModel.setValue("empty_right", "0");
		dataModel.setValue("titled_title", "");

		if (type.equals("none")) {
			dataModel.setValue("type", dialog.getWidget("none"));
		} else if (type.equals("empty")) {
			dataModel.setValue("type", dialog.getWidget("empty"));

			dataModel.setValue("empty_top", XMLUtils.getStringFromChild(borderNode, "top"));
			dataModel.setValue("empty_bottom", XMLUtils.getStringFromChild(borderNode, "bottom"));
			dataModel.setValue("empty_left", XMLUtils.getStringFromChild(borderNode, "left"));
			dataModel.setValue("empty_right", XMLUtils.getStringFromChild(borderNode, "right"));
		} else if (type.equals("titled")) {
			dataModel.setValue("type", dialog.getWidget("titled"));

			dataModel.setValue("titled_title", XMLUtils.getStringFromChild(borderNode, "title"));
		} else if (type.equals("raised")) {
			dataModel.setValue("type", dialog.getWidget("raised"));
		} else if (type.equals("lowered")) {
			dataModel.setValue("type", dialog.getWidget("lowered"));
		} else if (type.equals("etched")) {
			dataModel.setValue("type", dialog.getWidget("etched"));
		} else {
			throw new GUIException("Unknown border type ["+type+"]");
		}

		activateType(dataModel.getValue("type").toString());
	}

	protected void doOK() throws GUIException {
		NodeList children = borderNode.getChildNodes();
		int length = children.getLength();
		for (int i = 0; i < length; i++) {
			borderNode.removeChild(children.item(0));
		}

		String type = dataModel.getValue("type").toString();
		borderNode.setAttribute("border", type);
		Document document = borderNode.getOwnerDocument();

		if (type.equals("empty")) {
			Element top = document.createElement("top");
			top.appendChild(document.createTextNode((String) dataModel.getValue("empty_top")));
			borderNode.appendChild(top);

			Element bottom = document.createElement("bottom");
			bottom.appendChild(document.createTextNode((String) dataModel.getValue("empty_bottom")));
			borderNode.appendChild(bottom);

			Element left = document.createElement("left");
			left.appendChild(document.createTextNode((String) dataModel.getValue("empty_left")));
			borderNode.appendChild(left);

			Element right = document.createElement("right");
			right.appendChild(document.createTextNode((String) dataModel.getValue("empty_right")));
			borderNode.appendChild(right);
		} else if (type.equals("titled")) {
			Element title = document.createElement("title");
			title.appendChild(document.createTextNode((String) dataModel.getValue("titled_title")));
			borderNode.appendChild(title);
		}

		PropertyTableRow row = (PropertyTableRow) editorModel.getValue("row");
		WidgetUserObject object = row.getUserObject();
		editorModel.setValue("value", type);
	}

	public void eventOccured(GUIEvent event) {
		String name = event.getName();
		try {
			if (name.equals("cancel")) {
				dialog.dispose();
			} else if (name.equals("ok")) {
				doOK();
				dialog.dispose();
			}
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}