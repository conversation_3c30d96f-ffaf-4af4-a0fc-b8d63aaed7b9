/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.net.URL;

import javax.swing.LookAndFeel;
import javax.swing.UIManager;
import javax.swing.plaf.metal.MetalLookAndFeel;
import javax.swing.plaf.metal.MetalTheme;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.GUIUtils;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.ComboBox;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.TextField;

public class LookAndFeelChooser extends Controller implements ModelChangeListener {
	private MapDataModel dataModel = null;
	private Frame frame = null;
	private ComboBox themeCombo = null;
	private Button okButton = null;
	private TextField licenseField = null;
	private ListDataModel lnfModel = null;
	private ListDataModel emptyModel = null;

	private class Theme {
		public String clazz = null;

		public Theme(String clazz) {
			this.clazz = clazz;
		}

		public String toString() {
			return getString("builder.lookandfeel." + clazz);
		}
	};

	/**
	 * General Look And Feel
	 */
	private class LnF {
		public String clazz = null;
		public ListDataModel themeModel = null;
		public boolean supported = false;

		public LnF(String clazz) {
			this.clazz = clazz;
			try {
				Class lnf = Class.forName(clazz);
				supported = !(System.getProperty("os.name").equals("Mac OS X") && lnf.getName().equals(GUIUtils.KUNSTSTOFF_LNF));					
			} catch (Exception e) {
				supported = false;
			}
		}

		protected void addTheme(String clazz) throws GUIException {
			if (themeModel == null)
				themeModel = new ListDataModel();
			themeModel.addValue(new Theme(clazz));
		}

		public String toString() {
			if (isSupported())
				return getString("builder.lookandfeel." + clazz);
			else
				return "[" + getString("builder.lookandfeel." + clazz) + "]";
		}

		public boolean isSupported() {
			return supported;
		}

		public boolean needsLicenseCode() {
			return false;
		}

		public LookAndFeel create(String themeClass, String licenseCode) throws GUIException {
			try {
				Class lnfClass = Class.forName(clazz);
				LookAndFeel lnfInstance = (LookAndFeel) lnfClass.newInstance();
				return lnfInstance;
			} catch (Exception e) {
				throw new GUIException("Error while creating Look And Feel", e);
			}
		}
	};

	/**
	 * Metal based Look And Feel
	 */
	private class MetalLnF extends LnF {
		public MetalLnF(String clazz) {
			super(clazz);
		}

		public LookAndFeel create(String themeClass, String licenseCode) throws GUIException {
			MetalLookAndFeel lnf = (MetalLookAndFeel) super.create(themeClass, licenseCode);
			try {
				MetalLookAndFeel.setCurrentTheme((MetalTheme) Class.forName(themeClass).newInstance());
				return lnf;
			} catch (Exception e) {
				throw new GUIException("Error while setting theme", e);
			}
		}
	};

	/**
	 * Proprietary "Alloy" Look And Feel from INCORS
	 */
	private class AlloyLnF extends LnF {
		public AlloyLnF() throws GUIException {
			super("com.incors.plaf.alloy.AlloyLookAndFeel");
			addTheme("com.incors.plaf.alloy.DefaultAlloyTheme");
			addTheme("com.incors.plaf.alloy.themes.acid.AcidTheme");
			addTheme("com.incors.plaf.alloy.themes.bedouin.BedouinTheme");
			addTheme("com.incors.plaf.alloy.themes.glass.GlassTheme");
		}

		public boolean needsLicenseCode() {
			return true;
		}

		public LookAndFeel create(String themeClass, String licenseCode) throws GUIException {
			try {
				Class lnfClass = Class.forName(clazz);

				Method method = lnfClass.getMethod("setProperty", new Class[] { String.class, String.class });
				method.invoke(null, new Object[] { "alloy.licenseCode", licenseCode });

				Constructor constructor =
					lnfClass.getConstructor(new Class[] { Class.forName("com.incors.plaf.alloy.AlloyTheme")});
				LookAndFeel lnfInstance =
					(LookAndFeel) constructor.newInstance(new Object[] { Class.forName(themeClass).newInstance()});

				return lnfInstance;
			} catch (Exception e) {
				throw new GUIException("Error while setting theme", e);
			}
		}
	};

	/**
	 * JGoodies Plastic Look And Feel
	 */
	private class PlasticLnF extends MetalLnF {
		public PlasticLnF(String clazz) throws GUIException {
			super(clazz);
			addTheme("com.jgoodies.looks.plastic.theme.DesertBlue");
			addTheme("com.jgoodies.looks.plastic.theme.BrownSugar");
			addTheme("com.jgoodies.looks.plastic.theme.DarkStar");
			addTheme("com.jgoodies.looks.plastic.theme.DesertBluer");
			addTheme("com.jgoodies.looks.plastic.theme.DesertGreen");
			addTheme("com.jgoodies.looks.plastic.theme.DesertRed");
			addTheme("com.jgoodies.looks.plastic.theme.DesertYellow");
			addTheme("com.jgoodies.looks.plastic.theme.ExperienceBlue");
			addTheme("com.jgoodies.looks.plastic.theme.ExperienceGreen");
			addTheme("com.jgoodies.looks.plastic.theme.Silver");
			addTheme("com.jgoodies.looks.plastic.theme.SkyBlue");
			addTheme("com.jgoodies.looks.plastic.theme.SkyBluer");
			addTheme("com.jgoodies.looks.plastic.theme.SkyBluerTahoma");
			addTheme("com.jgoodies.looks.plastic.theme.SkyGreen");
			addTheme("com.jgoodies.looks.plastic.theme.SkyKrupp");
			addTheme("com.jgoodies.looks.plastic.theme.SkyPink");
			addTheme("com.jgoodies.looks.plastic.theme.SkyRed");
			addTheme("com.jgoodies.looks.plastic.theme.SkyYellow");
		}
	};

	public LookAndFeelChooser() throws GUIException {
		emptyModel = new ListDataModel();
		lnfModel = new ListDataModel();

		LnF lnf = new MetalLnF("javax.swing.plaf.metal.MetalLookAndFeel");
		lnf.addTheme("javax.swing.plaf.metal.DefaultMetalTheme");
		lnfModel.addValue(lnf);
		lnfModel.addValue(new LnF("com.sun.java.swing.plaf.windows.WindowsLookAndFeel") {
			public boolean isSupported() {
				return super.isSupported() && (System.getProperty("os.name").toLowerCase().indexOf("windows") != -1);
			}
		});
		lnfModel.addValue(new LnF("com.sun.java.swing.plaf.motif.MotifLookAndFeel"));
		lnf = new MetalLnF("com.incors.plaf.kunststoff.KunststoffLookAndFeel");
		lnf.addTheme("com.incors.plaf.kunststoff.KunststoffTheme");
		lnfModel.addValue(lnf);
		lnfModel.addValue(new AlloyLnF());

		lnfModel.addValue(new LnF("com.jgoodies.looks.windows.WindowsLookAndFeel") {
			public boolean isSupported() {
				return super.isSupported() && (System.getProperty("os.name").toLowerCase().indexOf("windows") != -1);
			}
		});

		lnfModel.addValue(new PlasticLnF("com.jgoodies.looks.plastic.PlasticLookAndFeel"));
		lnfModel.addValue(new PlasticLnF("com.jgoodies.looks.plastic.Plastic3DLookAndFeel"));
		lnfModel.addValue(new PlasticLnF("com.jgoodies.looks.plastic.PlasticXPLookAndFeel"));

		dataModel = new MapDataModel();
		URL url = this.getClass().getResource("LookAndFeelChooser.xml");
		frame = constructFrame(url, "LookAndFeelChooser", dataModel);
		((ComboBox) frame.getWidget("LnfCombo")).setListDataModel(lnfModel);
		themeCombo = (ComboBox) frame.getWidget("ThemeCombo");
		licenseField = (TextField) frame.getWidget("LicenseField");
		okButton = (Button) frame.getWidget("OKButton");

		dataModel.addModelChangeListener(this);
		dataModel.setValue("lnf.index", new Integer(0));

		frame.show();
	}

	public boolean isVisible() {
		return frame.isVisible();
	}

	public void eventOccured(GUIEvent event) {
		String name = event.getName();
		try {
			if (name.equals("apply")) {
				LnF lnf = (LnF) dataModel.getValue("lnf.value");
				Theme theme = (Theme) dataModel.getValue("theme.value");
				String licenseCode = (String) dataModel.getValue("license");

				LookAndFeel lnfInstance = lnf.create(theme == null ? null : theme.clazz, licenseCode);
				UIManager.setLookAndFeel(lnfInstance);
				GUIUtils.updateAllWidgets();
			} else if (name.equals("close")) {
				frame.dispose();
			}
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (e instanceof MapChangeEvent) {
			MapChangeEvent event = (MapChangeEvent) e;

			if (event.getKey().equals("lnf.value")) {
				LnF lnf = (LnF) dataModel.getValue("lnf.value");

				dataModel.setValue("license", "");
				licenseField.setEnabled(lnf.needsLicenseCode());
				okButton.setEnabled(lnf.isSupported());

				if (lnf.themeModel == null) {
					themeCombo.setListDataModel(emptyModel);
					themeCombo.setEnabled(false);
				} else {
					themeCombo.setListDataModel(lnf.themeModel);
					dataModel.setValue("theme.index", new Integer(0));

					themeCombo.setEnabled(true);
				}
			}
		}
	}
}
