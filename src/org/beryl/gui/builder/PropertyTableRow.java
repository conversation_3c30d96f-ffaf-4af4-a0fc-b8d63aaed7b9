/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.util.HashMap;

import org.beryl.gui.GUIException;
import org.beryl.gui.LayoutFactory;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.PropertyFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.widgets.Table;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.NodeList;

public class PropertyTableRow extends TableRow implements ModelChangeListener {
	private static HashMap propertyAdapters = null;

	static {
		propertyAdapters = new HashMap();
		PropertyAdapter istringAdapter = new IStringAdapter();
		propertyAdapters.put("", istringAdapter);
		propertyAdapters.put("istring", istringAdapter);
		propertyAdapters.put("string", new StringAdapter());

		PropertyAdapter numberAdapter = new NumberAdapter();
		propertyAdapters.put("int", numberAdapter);
		propertyAdapters.put("long", numberAdapter);
		propertyAdapters.put("float", numberAdapter);
		propertyAdapters.put("double", numberAdapter);

		DimensionAdapter dimensionAdapter = new DimensionAdapter();
		propertyAdapters.put("dimension", dimensionAdapter);
		propertyAdapters.put("point", dimensionAdapter);

		propertyAdapters.put("enum", new EnumAdapter());
		propertyAdapters.put("color", new ColorAdapter());
		propertyAdapters.put("bool", new BoolAdapter());
		propertyAdapters.put("icon", new IconAdapter());
		propertyAdapters.put("iicon", new IconAdapter());
		propertyAdapters.put("border", new BorderAdapter());
		propertyAdapters.put("layout", new LayoutAdapter());
		propertyAdapters.put("anchor", new AnchorAdapter());
		propertyAdapters.put("font", new FontAdapter());
	};

	private Element propertyNode = null;
	private WidgetUserObject userObject = null;

	public PropertyTableRow(WidgetUserObject userObject, Object value, Element propertyNode) throws GUIException {
		this.userObject = userObject;
		this.propertyNode = propertyNode;
		String nodeName = propertyNode.getNodeName();

		if (nodeName.equals("property")) {
			setValue("name", propertyNode.getAttribute("name"));
			setValue("value", getPropertyAdapter().toValue(value, propertyNode));
		} else if (nodeName.equals("layout")) {
			setValue("name", "layout");
			setValue("value", getPropertyAdapter().toValue(value, propertyNode));
		} else if (nodeName.equals("anchor")) {
			setValue("name", "anchor");
			setValue("value", getPropertyAdapter().toValue(value, propertyNode));
		}
		addModelChangeListener(this);
	}

	public boolean hasCustomRenderer(String key) {
		if (key.equals("value"))
			return true;
		return false;
	}

	public Widget getRenderer(
		Table table,
		Object value,
		boolean isSelected,
		boolean hasFocus,
		TableRow row,
		String key)
		throws GUIException {
		TableRenderer renderer = getPropertyAdapter().getRenderer();

		if (renderer != null)
			return renderer.getRenderer(table, value, isSelected, hasFocus, row, key);
		return null;
	}

	public WidgetUserObject getUserObject() {
		return userObject;
	}

	public Element getPropertyNode() {
		return propertyNode;
	}

	public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
		TableEditor editor = getPropertyAdapter().getEditor();
		if (editor != null)
			return editor.getEditor(table, value, row, key);
		return null;
	}

	public boolean isEditable(String key) {
		if (key.equals("value")) {
			return true;
		} else {
			return false;
		}
	}

	public PropertyAdapter getPropertyAdapter() throws GUIException {
		if (propertyNode.getNodeName().equals("layout"))
			return (PropertyAdapter) propertyAdapters.get("layout");
		else if (propertyNode.getNodeName().equals("anchor"))
			return (PropertyAdapter) propertyAdapters.get("anchor");

		PropertyAdapter adapter = (PropertyAdapter) propertyAdapters.get(propertyNode.getAttribute("type"));
		if (adapter != null)
			return adapter;
		else
			throw new GUIException("Unknown property type [" + propertyNode.getAttribute("type") + "]");
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		MapChangeEvent event = (MapChangeEvent) e;

		if (event.getKey().equals("value")) {
			if (propertyNode.getNodeName().equals("property")) {
				NamedNodeMap map = propertyNode.getAttributes();
				for (int i = 0; i < map.getLength(); i++) {
					String name = map.item(i).getNodeName();
					if (!name.equals("name") && !name.equals("type")) {
						propertyNode.removeAttribute(map.item(i).getNodeName());
					}
				}

				if (!propertyNode.getAttribute("name").equals("border")) {
					NodeList children = propertyNode.getChildNodes();
					int length = children.getLength();
					for (int i = 0; i < length; i++) {
						propertyNode.removeChild(children.item(0));
					}
				}

				getPropertyAdapter().toDOM(event.getNewValue(), propertyNode);

				try {
					userObject.widget.setProperty(
						(String) getValue("name"),
						PropertyFactory.getInstance().constructProperty(propertyNode));
				} catch (Exception ex) {
					new MessageDialog(ex);
				}

				WidgetTree.revalidate(userObject.widget);
			} else if (propertyNode.getNodeName().equals("layout")) {
				getPropertyAdapter().toDOM(event.getNewValue(), propertyNode);
				
				if (!propertyNode.getAttribute("type").equals("hig") && !propertyNode.getAttribute("type").equals("")) {
					propertyNode.removeAttribute("horiz");
					propertyNode.removeAttribute("vert");
					propertyNode.removeAttribute("hweights");
					propertyNode.removeAttribute("vweights");
				}

				try {
					userObject.widget.setProperty(
						"layout",
						LayoutFactory.getInstance().constructLayout(userObject.widget, propertyNode));
				} catch (Exception ex) {
					new MessageDialog(ex);
				}

				WidgetTree.revalidate(userObject.widget);
			}
		}
	}
};
