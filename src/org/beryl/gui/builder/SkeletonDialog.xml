<?xml version="1.0" encoding="ISO-8859-1"?>
<UI version="1.0">
    <widget class="Dialog" name="SkeletonDialog">
        <property name="size" type="dimension">
            <width>300</width>
            <height>150</height>
        </property>
        <property name="title" type="istring">builder.builder.genskel</property>

		<widget class="Group">
            <widget class="LabeledWidget">
                <widget name="PackageField" class="TextField">
                	<property name="key" type="string">package</property>
				</widget>
                <property name="label.text" type="istring">builder.genskel.fcn</property>
            </widget>
            <widget class="LabeledWidget">
                <property name="label.text" type="istring">builder.genskel.dest</property>
                <widget class="Button">
                    <property name="text" type="istring">builder.common.browse</property>
                	<emit event="clicked" name="browse"/>
                </widget>
            </widget>
            <widget class="Button" preset="cancel">
                <emit event="clicked" name="cancel"/>
            </widget>
            <widget name="OKButton" class="Button" preset="ok">
                <emit event="clicked" name="ok"/>
            </widget>
        </widget>
    </widget>
</UI>
