/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
package org.beryl.gui.builder;

import javax.swing.ImageIcon;

import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.XMLUtils;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.ImageRenderer;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.Table;
import org.beryl.gui.widgets.TextField;
import org.w3c.dom.Element;

public class IconAdapter implements PropertyAdapter {
	private TableRenderer iconRenderer = null;
	private TableEditor iconEditor = null;

	private class IconEditor implements TableEditor {
		public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
			MapDataModel dataModel = new MapDataModel() {
				public Object getValue(String key) {
					if (key.equals("value")) {
						try {
							return ImageIconFactory.getIcon((String) super.getValue("value_str"));
						} catch (Exception e) {
							new MessageDialog(e);
						}
					}
					return super.getValue(key);
				}

			};

			dataModel.setValue("value", value);
			dataModel.setValue("value_str", value.toString());

			Panel panel = new Panel(null, null);
			TextField textField = new TextField(panel, null);
			textField.setProperty("key", "value_str");
			textField.finalizeConstruction();

			panel.addChild(textField, null);
			panel.recursiveSetDataModel(dataModel);

			return panel;
		}
	}

	public IconAdapter() {
		iconRenderer = new ImageRenderer();
		iconEditor = new IconEditor();
	}

	public TableEditor getEditor() {
		return iconEditor;
	}

	public TableRenderer getRenderer() {
		return iconRenderer;
	}

	public Object toValue(Object value, Element propertyNode) {
		((ImageIcon) value).setDescription(XMLUtils.extractTextChildren(propertyNode));
		return value;
	}

	public void toDOM(Object value, Element propertyNode) {
		try {
			if (value instanceof String)
				value = ImageIconFactory.getIcon((String) value);
		} catch (Exception e) {
			/* Should not happen */
			throw new RuntimeException("Error while loading icon", e);
		}
		propertyNode.appendChild(propertyNode.getOwnerDocument().createTextNode(((ImageIcon) value).getDescription()));
	}
}