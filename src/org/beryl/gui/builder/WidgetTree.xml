<?xml version="1.0" encoding="ISO-8859-1"?>
<UI version="1.0">
    <widget class="Frame" name="WidgetTree">
        <layout type="border"/>
        <property name="title">builder.wtree.title</property>
        <property name="defaultCloseOperation" type="enum">nothing</property>
        <property name="size" type="dimension">
            <width>250</width>
            <height>600</height>
        </property>
        <property name="location" type="point">
            <x>0</x>
            <y>180</y>
        </property>
        <property name="visible" type="bool">true</property>
        <widget class="SplitPane">
            <property name="orientation" type="string">v</property>
            <property name="resizeWeight" type="double">0.7</property>
            <widget class="Panel">
                <layout type="border"/>
                <widget class="Tree" name="Tree">
                    <anchor border="center" type="border"/>
                    <emit event="rightclick" name="tree.popup"/>
                    <property name="key" type="string">tree.selected</property>
                    <widget class="TreeItem" name="RootNode">
                        <property name="text" type="istring">builder.wtree.root</property>
                    </widget>
                </widget>
                <widget class="Panel">
                    <anchor border="north" type="border"/>
                    <layout type="rflow"/>
                    <widget class="Button" name="MoveUpButton">
                        <property name="icon" type="icon">arrow_up</property>
                        <property name="toolTipText" type="istring">builder.wtree.moveup</property>
                        <emit event="clicked" name="button_move_up"/>
                        <property name="enabled" type="bool">false</property>
                    </widget>
                    <widget class="Button" name="MoveDownButton">
                        <property name="icon" type="icon">arrow_down</property>
                        <property name="toolTipText" type="istring">builder.wtree.movedown</property>
                        <emit event="clicked" name="button_move_down"/>
                        <property name="enabled" type="bool">false</property>
                    </widget>
                    <widget class="Button" name="DeleteButton">
                        <property name="icon" type="icon">delete</property>
                        <property name="toolTipText" type="istring">builder.common.delete</property>
                        <emit event="clicked" name="button_delete"/>
                        <property name="enabled" type="bool">false</property>
                    </widget>
                </widget>
            </widget>
            <widget class="Panel">
                <layout type="vbox"/>
                <widget class="Panel">
                    <layout type="rflow"/>
                    <widget class="Button">
                        <property name="icon" type="icon">add</property>
                        <property name="toolTipText" type="istring">builder.wtree.addevent</property>
                        <emit event="clicked" name="events"/>
                    </widget>
                    <widget class="Button">
                        <property name="icon" type="icon">add_property</property>
                        <property name="toolTipText" type="istring">builder.wtree.addproperty</property>
                        <emit event="clicked" name="add_property"/>
                    </widget>
                    <widget class="Button">
                        <property name="enabled" type="bool">true</property>
                        <property name="icon" type="icon">delete_property</property>
                        <property name="toolTipText" type="istring">builder.common.delete</property>
                        <emit event="clicked" name="delete_property"/>
                    </widget>
                </widget>
                <widget class="Table" name="Table">
                    <property name="column.name">builder.common.name</property>
                    <property name="column.value">builder.common.value</property>
                    <property name="valuekey" type="string">property.value</property>
                    <emit event="rightclick" name="property.popup"/>
                </widget>
            </widget>
        </widget>
    </widget>
    <widget class="PopupMenu" name="WidgetPopup">
        <widget class="MenuItem" name="MoveUp">
            <property name="text">builder.wtree.moveup</property>
            <property name="icon" type="icon">arrow_up</property>
            <emit event="selected" name="move_up"/>
        </widget>
        <widget class="MenuItem" name="MoveDown">
            <property name="text">builder.wtree.movedown</property>
            <property name="icon" type="icon">arrow_down</property>
            <emit event="selected" name="move_down"/>
        </widget>
        <widget class="Separator"/>
        <widget class="MenuItem" name="Delete">
            <property name="text">builder.common.delete</property>
            <property name="icon" type="icon">delete</property>
            <emit event="selected" name="delete"/>
        </widget>
    </widget>
    <widget class="PopupMenu" name="PropertyPopup">
        <widget class="MenuItem" name="Delete">
            <property name="text">builder.common.delete</property>
            <property name="icon" type="icon">delete</property>
            <emit event="selected" name="delete_property"/>
        </widget>
    </widget>
</UI>
