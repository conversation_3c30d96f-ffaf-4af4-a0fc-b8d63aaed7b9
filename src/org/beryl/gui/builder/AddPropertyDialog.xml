<?xml version="1.0" encoding="ISO-8859-1"?>
<UI version="1.0">
    <widget class="Dialog" name="AddPropertyDialog">
        <property name="title">builder.addproperty.title</property>
        <property name="size" type="dimension">
            <width>500</width>
            <height>300</height>
        </property>
        <widget class="ButtonGroup">
            <property name="key" type="string">mode</property>
            <layout type="hig" horiz="15,0" hweights="0,1" vert="0,0,5,0,5,0,0,5,0" vweights="0,0,0,0,0,0,1,0,0"/>

            <widget class="RadioButton">
            	<anchor pos="1,1,2"/>
                <property name="text" type="istring">builder.addproperty.manual</property>
                <property name="value" type="string">manual</property>
            </widget>

            <widget class="Group">
            	<anchor pos="2,2,1"/>
                <widget class="LabeledWidget">
                    <property name="label.text" type="istring">builder.addproperty.name</property>
                    <widget class="TextField" name="NameField">
                        <property name="key" type="string">name</property>
                    </widget>
                </widget>
                <widget class="LabeledWidget">
                    <property name="label.text" type="istring">builder.addproperty.type</property>
                    <widget class="ComboBox" name="TypeCombo">
                        <property name="indexkey" type="string">type.index</property>
                        <widget class="Item">
                            <property name="text" type="istring">builder.addproperty.istring</property>
                        </widget>
                        <widget class="Item">
                            <property name="text" type="istring">builder.addproperty.string</property>
                        </widget>
                        <widget class="Item">
                            <property name="text" type="istring">builder.addproperty.int</property>
                        </widget>
                        <widget class="Item">
                            <property name="text" type="istring">builder.addproperty.float</property>
                        </widget>
                        <widget class="Item">
                            <property name="text" type="istring">builder.addproperty.double</property>
                        </widget>
                        <widget class="Item">
                            <property name="text" type="istring">builder.addproperty.bool</property>
                        </widget>
                    </widget>
                </widget>
                <widget class="LabeledWidget">
                    <property name="label.text" type="istring">builder.addproperty.value</property>
                    <widget class="TextField" name="ValueField">
                        <property name="key" type="string">value</property>
                    </widget>
                </widget>
            </widget>

            <widget class="Separator">
            	<anchor pos="4,1,2"/>
            </widget>

            <widget class="RadioButton">
            	<anchor pos="6,1,2"/>
                <property name="text" type="istring">builder.addproperty.common</property>
                <property name="value" type="string">common</property>
            </widget>

            <widget class="Table" name="PropertyTable">
            	<anchor pos="7,2,1"/>
                <property name="valuekey">property</property>
                <property name="column.name">builder.common.name</property>
                <property name="columnsize.name" type="int">80</property>
                <property name="column.type">builder.common.type</property>
                <property name="columnsize.type" type="int">50</property>
                <property name="column.description">builder.common.description</property>
                <property name="columnsize.description" type="int">250</property>
            </widget>

            <widget class="Panel">
            	<anchor pos="9,1,2"/>
                <layout type="rflow"/>
                <widget class="Button" preset="cancel">
                    <emit event="clicked" name="cancel"/>
                </widget>
                <widget class="Button" name="OKButton" preset="ok">
                    <property name="default" type="bool">true</property>
                    <emit event="clicked" name="add"/>
                </widget>
            </widget>
        </widget>
    </widget>
</UI>
