/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.Table;
import org.beryl.gui.widgets.TextField;
import org.w3c.dom.Element;

public class StringAdapter implements PropertyAdapter {
	private TableEditor stringEditor = null;
	
	public static class StringEditor implements TableEditor {
		public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
			MapDataModel dataModel = new MapDataModel();
			dataModel.setValue("value", value);

			Panel panel = new Panel(null, null);
			TextField textField = new TextField(panel, null);
			textField.setProperty("key", "value");
			textField.finalizeConstruction();

			panel.addChild(textField, null);
			panel.recursiveSetDataModel(dataModel);

			return panel;
		}
	};


	public StringAdapter() {
		stringEditor = new StringEditor();
	}
	
	public TableEditor getEditor() {
		return stringEditor;
	}

	public TableRenderer getRenderer() {
		return null;
	}

	public Object toValue(Object value, Element propertyNode) {
		return value;
	}

	public void toDOM(Object value, Element propertyNode) {
		propertyNode.appendChild(propertyNode.getOwnerDocument().createTextNode((String) value)) ;
	}
}
