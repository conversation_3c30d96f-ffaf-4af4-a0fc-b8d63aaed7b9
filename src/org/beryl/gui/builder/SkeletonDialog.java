/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.io.File;
import java.net.URL;

import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;

import org.beryl.gui.Controller;
import org.beryl.gui.DialogUtils;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Frame;
import org.w3c.dom.Document;

public class SkeletonDialog extends Controller {
	private static String SKELETON_XSL = "/resources/builder/skeleton.xsl";
	private Dialog dialog = null;
	private Document document = null;
	private MapDataModel dataModel = null;

	public SkeletonDialog(Frame parent, Document document) throws GUIException {
		this.document = document;
		dataModel = new MapDataModel();
		URL url = this.getClass().getResource("SkeletonDialog.xml");
		dialog = constructDialog(url, "SkeletonDialog", dataModel);
		final Button okButton = (Button) dialog.getWidget("OKButton");

		dataModel.addModelChangeListener(new ModelChangeListener() {
			public void modelChanged(ModelChangeEvent e) throws GUIException {
				okButton.setEnabled(
					dataModel.getValue("file") != null
						&& dataModel.getValue("package") != null
						&& !((String) dataModel.getValue("package")).trim().equals(""));
			}
		});

		okButton.setEnabled(false);
		dialog.initDialog(parent);
		dialog.show();
	}

	public void eventOccured(GUIEvent e) {
		String eventName = e.getName();

		try {
			if (eventName.equals("cancel")) {
				dialog.dispose();
			} else if (eventName.equals("ok")) {
				String pkg = (String) dataModel.getValue("package");
				File file = (File) dataModel.getValue("file");

				URL url = this.getClass().getResource(SKELETON_XSL);
				Transformer transformer =
					TransformerFactory.newInstance().newTransformer(new StreamSource(url.openStream()));
				if (pkg.indexOf('.') != -1) {
					transformer.setParameter("package", pkg.substring(0, pkg.lastIndexOf('.')));
					transformer.setParameter("class", pkg.substring(pkg.lastIndexOf(".") + 1, pkg.length()));
				} else {
					transformer.setParameter("package", "");
					transformer.setParameter("class", pkg);
				}
				

				transformer.transform(new DOMSource(document), new StreamResult(file));

				dialog.dispose();
			} else {
				File newFile = DialogUtils.showSaveFileDialog(dialog, "java");
				if (newFile != null) {
					dataModel.setValue("file", newFile);
				}
			}
		} catch (Exception ex) {
			new MessageDialog(dialog, ex);
		}
	}
}
