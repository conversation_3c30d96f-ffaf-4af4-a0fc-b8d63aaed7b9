/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.ButtonEditor;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.Table;
import org.w3c.dom.Element;

public class BorderAdapter implements PropertyAdapter, GUIEventListener {
	private TableEditor borderEditor = null;

	public BorderAdapter() {
		borderEditor = new ButtonEditor("edit", this) {
			public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
				Widget widget = super.getEditor(table, value, row, key);
				MapDataModel dataModel = widget.getDataModel();
				dataModel.setValue("row", row);
				dataModel.setValue("frame", table.getParentWidgetByClass(Frame.class));
				return widget;
			}
		};
	}

	public TableRenderer getRenderer() {
		return null;
	}

	public TableEditor getEditor() {
		return borderEditor;
	}

	public Object toValue(Object value, Element propertyNode) {
		return propertyNode.getAttribute("border");
	}

	public void toDOM(Object value, Element propertyNode) {
		if (value instanceof String)
			propertyNode.setAttribute("border",  (String) value);
		else
			propertyNode.setAttribute("border",  "none");
	}

	public void eventOccured(GUIEvent event) {
		try {
			MapDataModel model = (MapDataModel) event.getSource().getDataModel();

			BorderEditor editor = new BorderEditor((Frame) model.getValue("frame"), model);
			editor.show();
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}
