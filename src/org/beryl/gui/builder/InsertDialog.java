/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.net.URL;
import java.util.Iterator;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.ComboBox;
import org.beryl.gui.widgets.Dialog;
import org.w3c.dom.Element;

public class InsertDialog extends Controller {
	private Dialog dialog = null;
	private WidgetTree tree = null;
	private Element anchorElement = null;
	private MapDataModel dataModel = null;
	private String className = null;
	private String nonePreset = new String("none");

	public InsertDialog(WidgetTree tree, String className, WidgetInfo info, Element anchorElement)
		throws GUIException {
		this.tree = tree;
		this.className = className;
		this.anchorElement = anchorElement;

		dataModel = new MapDataModel();
		dataModel.setValue("preset", nonePreset);
		dataModel.setValue("widget", className);

		ListDataModel presetModel = new ListDataModel();
		presetModel.addValue(nonePreset);

		for (Iterator i = info.getPresetEntries().iterator(); i.hasNext();) {
			presetModel.addValue(i.next());
		}

		URL url = this.getClass().getResource("InsertDialog.xml");
		dialog = constructDialog(url, "InsertDialog", dataModel);
		((ComboBox) dialog.getWidget("PresetBox")).setListDataModel(presetModel);
		((Button) dialog.getWidget("AnchorButton")).setEnabled(info.getSupportsAnchor());
		dialog.initDialog(tree.getFrame());
		dialog.show();
	}

	public void eventOccured(GUIEvent event) {
		String name = event.getName();

		try {
			if (name.equals("cancel")) {
				dialog.dispose();
			} else if (name.equals("add")) {
				dialog.dispose();

				String preset = (dataModel.getValue("preset") instanceof String) ? null : ((WidgetInfo.PresetEntry) dataModel.getValue("preset")).presetName;

				if (anchorElement.getAttribute("type").equals(""))
					anchorElement = null;
				tree.doInsert(className, preset, anchorElement);
			} else if (name.equals("anchor")) {
				AnchorEditor anchorEditor = new AnchorEditor(dialog, anchorElement, null);
				anchorEditor.show();
			}
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}
