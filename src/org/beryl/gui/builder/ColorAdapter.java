/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.awt.Color;

import javax.swing.JColorChooser;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.ButtonEditor;
import org.beryl.gui.table.ColorRenderer;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.Table;
import org.w3c.dom.Element;

public class ColorAdapter implements PropertyAdapter, GUIEventListener {
	private TableEditor colorEditor = null;

	public ColorAdapter() {
		colorEditor = new ButtonEditor("edit", this) {
			public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
				Widget widget = super.getEditor(table, value, row, key);
				MapDataModel dataModel = widget.getDataModel();
				dataModel.setValue("frame", table.getParentWidgetByClass(Frame.class));
				return widget;
			}
			
		};
	}

	public TableRenderer getRenderer() {
		return new ColorRenderer();
	}

	public TableEditor getEditor() {
		return colorEditor;
	}

	public Object toValue(Object value, Element propertyNode) {
		return value;
	}

	public void toDOM(Object value, Element propertyNode) {
		propertyNode.appendChild(propertyNode.getOwnerDocument().createTextNode(ColorRenderer.toString((Color) value)));
	}

	public void eventOccured(GUIEvent event) {
		try {
			Color selectedColor = JColorChooser.showDialog(((Frame) event.getSource().getDataModel().getValue("frame")).getRealWidget(), "", (Color) event.getSource().getDataModel().getValue("value"));
			if (selectedColor != null)
				event.getSource().getDataModel().setValue("value", selectedColor);
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}