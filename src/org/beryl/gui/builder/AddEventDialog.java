/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.net.URL;
import java.util.List;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.WidgetInfo.EventEntry;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.ComboBox;
import org.beryl.gui.widgets.Dialog;
import org.w3c.dom.Element;

public class AddEventDialog extends Controller {
	private WidgetTree tree = null;
	private Dialog dialog = null;
	private MapDataModel dataModel = null;
	private WidgetUserObject object = null;

	public AddEventDialog(WidgetTree tree, Dialog parent, WidgetUserObject object) throws GUIException {
		this.tree = tree;
		this.object = object;

		dataModel = new MapDataModel();
		URL url = AddEventDialog.class.getResource("About.xml");
		dialog = constructDialog(url, "AddEventDialog", dataModel);
		final Button okButton = (Button) dialog.getWidget("OKButton");

		dataModel.addModelChangeListener(new ModelChangeListener() {
			public void modelChanged(ModelChangeEvent e) throws GUIException {
				okButton.setEnabled(
					dataModel.getValue("event") != null
						&& dataModel.getValue("name") != null
						&& !((String) dataModel.getValue("name")).trim().equals(""));
			}
		});

		ListDataModel events = new ListDataModel();
		List list = object.widget.getWidgetInfo().getEventEntries();

		for (int i = 0; i < list.size(); i++) {
			events.addValue(list.get(i));
		}
		
		((ComboBox) dialog.getWidget("EventCombo")).setListDataModel(events);

		if (list.size() > 0) {
			dataModel.setValue("event", list.get(0));
		}

		dialog.initDialog(parent);
		dialog.show();
	}

	public void eventOccured(GUIEvent e) {
		String eventName = e.getName();

		try {
			if (eventName.equals("cancel")) {
				dialog.dispose();
			} else if (eventName.equals("ok")) {
				WidgetInfo.EventEntry event = (EventEntry) dataModel.getValue("event");
				String name = (String) dataModel.getValue("name");

				Element emitNode = object.element.getOwnerDocument().createElement("emit");

				emitNode.setAttribute("event", event.eventName);
				emitNode.setAttribute("name", name);
				
				object.element.appendChild(emitNode);

				TableRow row = new TableRow();
				row.setValue("event", event.eventName);
				row.setValue("name", name);
				row.setValue("description", event.getDescription());
				row.setValue("node", emitNode);

				object.eventModel.addRow(row);
				
				Builder.markModified();

				dialog.dispose();
			}
		} catch (Exception ex) {
			new MessageDialog(ex);
		}
	}
}
