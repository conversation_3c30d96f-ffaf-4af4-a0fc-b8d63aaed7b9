<?xml version="1.0" encoding="iso-8859-1"?>

<UI version="1.0">
	<widget name="AnchorEditor" class="Dialog">
		<property name="title">builder.aneditor.title</property>

		<property name="size" type="dimension">
			<width>500</width>
			<height>300</height>
		</property>

		<widget name="Group" class="Group">
			<widget class="LabeledWidget">
				<property name="label.text">builder.aneditor.type</property>

				<widget class="ComboBox">
					<property name="valuekey">type</property>
					
					<widget name="none" class="Item">
						<property name="text" type="string">none</property>
					</widget>
					<widget name="hig" class="Item">
						<property name="text" type="string">hig</property>
					</widget>
					<widget name="border" class="Item">
						<property name="text" type="string">border</property>
					</widget>
					<widget name="box" class="Item">
						<property name="text" type="string">box</property>
					</widget>
				</widget>
			</widget>
			
			<widget class="Separator"/>
			
			<widget class="Button" preset="cancel">
				<emit event="clicked" name="cancel"/>
			</widget>

			<widget name="OKButton" class="Button" preset="ok">
				<property name="default" type="bool">true</property>
				<emit event="clicked" name="ok"/>
			</widget>
		</widget>
	</widget>


	<!-- Custom components -->

	<widget name="HIG_Row" class="LabeledWidget">
		<property name="label.text">builder.aneditor.row</property>
		<widget class="TextField">
			<property name="key">hig_row</property>
		</widget>
	</widget>

	<widget name="HIG_Column" class="LabeledWidget">
		<property name="label.text">builder.aneditor.column</property>
		<widget class="TextField">
			<property name="key">hig_column</property>
		</widget>
	</widget>

	<widget name="HIG_Width" class="LabeledWidget">
		<property name="label.text">builder.aneditor.width</property>
		<widget class="TextField">
			<property name="key">hig_width</property>
		</widget>
	</widget>

	<widget name="HIG_Height" class="LabeledWidget">
		<property name="label.text">builder.aneditor.height</property>
		<widget class="TextField">
			<property name="key">hig_height</property>
		</widget>
	</widget>

	<widget name="HIG_Alignment" class="LabeledWidget">
		<property name="label.text">builder.aneditor.alignment</property>
		<widget class="Panel">
			<layout type="hbox"/>
			<widget class="CheckBox">
				<property name="text">builder.common.left</property>
				<property name="key">hig_l</property>
			</widget>
			<widget class="CheckBox">
				<property name="text">builder.common.right</property>
				<property name="key">hig_r</property>
			</widget>
			<widget class="CheckBox">
				<property name="text">builder.common.top</property>
				<property name="key">hig_t</property>
			</widget>
			<widget class="CheckBox">
				<property name="text">builder.common.bottom</property>
				<property name="key">hig_b</property>
			</widget>
		</widget>
	</widget>

	<widget name="Border_Alignment" class="LabeledWidget">
		<property name="label.text">builder.aneditor.alignment</property>
		
		<widget class="ComboBox">
			<property name="valuekey">border</property>
			<widget name="center" class="Item">
				<property name="text" type="string">center</property>
			</widget>
			<widget name="north" class="Item">
				<property name="text" type="string">north</property>
			</widget>
			<widget name="south" class="Item">
				<property name="text" type="string">south</property>
			</widget>
			<widget name="east" class="Item">
				<property name="text" type="string">east</property>
			</widget>
			<widget name="west" class="Item">
				<property name="text" type="string">west</property>
			</widget>
		</widget>
	</widget>

	<widget name="Box_AlignX" class="LabeledWidget">
		<property name="label.text">builder.aneditor.xalignment</property>
		<widget class="TextField">
			<property name="key">box_alignx</property>
		</widget>
	</widget>

	<widget name="Box_AlignY" class="LabeledWidget">
		<property name="label.text">builder.aneditor.yalignment</property>
		<widget class="TextField">
			<property name="key">box_aligny</property>
		</widget>
	</widget>
</UI>
