/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.validators.DoubleValidator;
import org.beryl.gui.validators.FloatValidator;
import org.beryl.gui.validators.IntegerValidator;
import org.beryl.gui.validators.LongValidator;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.Table;
import org.beryl.gui.widgets.TextField;
import org.w3c.dom.Element;

public class NumberAdapter implements PropertyAdapter {
	private TableEditor numberEditor = null;

	private class NumberEditor implements TableEditor {
		public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
			final MapDataModel dataModel = new MapDataModel();

			final String type = ((PropertyTableRow) row).getPropertyNode().getAttribute("type");

			dataModel.setValue("value", value);
			dataModel.setValue("value_str", value.toString());

			dataModel.addModelChangeListener(new ModelChangeListener() {
				public void modelChanged(ModelChangeEvent e) throws GUIException {
					if (e instanceof MapChangeEvent) {
						MapChangeEvent event = (MapChangeEvent) e;
						if (event.getKey().equals("value_str")) {
							try {
								if (type.equals("int"))
									dataModel.setValue("value", new Integer((String) dataModel.getValue("value_str")));
								else if (type.equals("long"))
									dataModel.setValue("value", new Long((String) dataModel.getValue("value_str")));
								else if (type.equals("float"))
									dataModel.setValue("value", new Float((String) dataModel.getValue("value_str")));
								else if (type.equals("double"))
									dataModel.setValue("value", new Double((String) dataModel.getValue("value_str")));
							} catch (Exception ex) {
								/* Ignore */
							}
						}
					}
				}
			});

			Panel panel = new Panel(null, null);
			TextField textField = new TextField(panel, null);
			textField.setProperty("key", "value_str");
			textField.finalizeConstruction();

			if (type.equals("int"))
				textField.addValidator(new IntegerValidator());
			else if (type.equals("long"))
				textField.addValidator(new LongValidator());
			else if (type.equals("float"))
				textField.addValidator(new FloatValidator());
			else if (type.equals("double"))
				textField.addValidator(new DoubleValidator());
			else
				throw new GUIException("Unknown numeric type");

			panel.addChild(textField, null);
			panel.recursiveSetDataModel(dataModel);

			return panel;
		}
	};

	public NumberAdapter() {
		numberEditor = new NumberEditor();
	}

	public TableEditor getEditor() {
		return numberEditor;
	}

	public TableRenderer getRenderer() {
		return null;
	}

	public Object toValue(Object value, Element propertyNode) {
		return value;
	}

	public void toDOM(Object value, Element propertyNode) {
		propertyNode.appendChild(propertyNode.getOwnerDocument().createTextNode(value.toString()));
	}
}