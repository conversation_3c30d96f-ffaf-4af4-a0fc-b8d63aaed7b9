/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.net.URL;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.Table;
import org.w3c.dom.Element;

public class EventDialog extends Controller {
	private WidgetTree tree = null;
	private WidgetUserObject object = null;
	private Button deleteButton = null;
	private Dialog dialog = null;
	private MapDataModel dataModel = null;

	public EventDialog(WidgetTree tree, Frame frame, WidgetUserObject object) throws GUIException {
		this.tree = tree;
		this.object = object;

		dataModel = new MapDataModel();
		URL url = EventDialog.class.getResource("EventDialog.xml");
		dialog = (Dialog) constructDialog(url, "EventDialog", dataModel);
		deleteButton = (Button) dialog.getWidget("DeleteButton");

		((Table) dialog.getWidget("EventTable")).setTableDataModel(object.eventModel);

		dataModel.setValue("event", new TableRow[] { });

		dataModel.addModelChangeListener(new ModelChangeListener() {
			public void modelChanged(ModelChangeEvent e) throws GUIException {
				deleteButton.setEnabled(((TableRow[]) dataModel.getValue("event")).length > 0);
			}
		});
		dialog.initDialog(frame);
		dialog.show();
	}

	public void eventOccured(GUIEvent event) {
		try {
			String name = event.getName();
			
			if (name.equals("close")) {
				dialog.dispose();
			} else if (name.equals("add")) {
				new AddEventDialog(tree, dialog, object);
			} else if (name.equals("delete")) {
				TableRow rows[] = (TableRow[]) dataModel.getValue("event");

				for (int i=0; i<rows.length; i++) {
					object.eventModel.removeRow(rows[i]);

					Element element = (Element) rows[i].getValue("node");
					element.getParentNode().removeChild(element);
					
					Builder.markModified();
				}
			}
		} catch (GUIException e) {
			new MessageDialog(e);
		}
	}
}
