<?xml version="1.0" encoding="ISO-8859-1"?>

<UI version="1.0">
    <widget class="Frame" name="LookAndFeelChooser">
        <property name="size" type="dimension">
            <width>400</width>
            <height>160</height>
        </property>

        <property name="title" type="istring">builder.lookandfeel.title</property>
        <widget class="Group">
            <widget class="LabeledWidget">
                <property name="label.text" type="istring">builder.lookandfeel.class</property>
                <widget name="LnfCombo" class="ComboBox">
                	<property name="indexkey" type="string">lnf.index</property>
                	<property name="valuekey" type="string">lnf.value</property>
                </widget>
            </widget>
            <widget class="LabeledWidget">
                <property name="label.text" type="istring">builder.lookandfeel.theme</property>
                <widget name="ThemeCombo" class="ComboBox">
                	<property name="indexkey" type="string">theme.index</property>
                	<property name="valuekey" type="string">theme.value</property>
                </widget>
            </widget>
            <widget class="LabeledWidget">
                <property name="label.text" type="istring">builder.lookandfeel.license</property>
                <widget name="LicenseField" class="TextField">
                	<property name="key" type="string">license</property>
                </widget>
            </widget>
            <widget class="Button" preset="cancel">
                <emit event="clicked" name="close"/>
                <property name="text" type="istring">builder.common.close</property>
            </widget>
            <widget class="Button" name="OKButton" preset="ok">
                <emit event="clicked" name="apply"/>
                <property name="text" type="istring">builder.common.apply</property>
                <property name="default" type="bool">true</property>
            </widget>
        </widget>
    </widget>
</UI>
