/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 * 					  <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.net.URL;
import java.util.StringTokenizer;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.LayoutFactory;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.XMLUtils;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.TableDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.validators.IntegerValidator;
import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.Table;
import org.beryl.gui.widgets.TextField;
import org.beryl.gui.widgets.TreeItem;
import org.w3c.dom.Element;

public class HIGEditor extends Controller {
	private Element layoutNode = null;
	private Dialog dialog = null;
	private Table rowsTable = null;
	private Table columnsTable = null;
	private TableDataModel rowsTableModel = null;
	private TableDataModel columnsTableModel = null;
	private MapDataModel dataModel = null;
	private WidgetUserObject object = null;

	private class TableEntry extends TableRow {
		private TableDataModel model;
		private TableEditor stringEditor = new StringAdapter.StringEditor();
		public TableEntry(TableDataModel model, int pixels, int weight) throws GUIException {
			this.model = model;
			setValue("pixels", String.valueOf(pixels));
			setValue("weight", String.valueOf(weight));
		}

		public Object getValue(String key) {
			if (key.equals("index")) {
				return String.valueOf(model.getRowIndex(this));
			}
			return super.getValue(key);
		}

		public int getPixels() {
			return Integer.parseInt((String)getValue("pixels"));
		}

		public int getWeight() {
			return Integer.parseInt((String)getValue("weight"));
		}
		
		public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
			MapDataModel dataModel = new MapDataModel();
			dataModel.setValue("value", value);

			Panel panel = new Panel(dialog, null);
			TextField textField = new TextField(panel, null);
			textField.setProperty("key", "value");
			textField.addValidator(new IntegerValidator());
			textField.finalizeConstruction();

			panel.addChild(textField, null);
			panel.recursiveSetDataModel(dataModel);

			return panel;
		}
		public boolean isEditable(String key) {
			return !key.equals("index");
		}
	};

	public HIGEditor(Frame frame, Element layoutNode, WidgetUserObject object) throws GUIException {
		this.object = object;
		this.layoutNode = layoutNode;

		dataModel = new MapDataModel();
		dataModel.setValue("rowpixels", "0");
		dataModel.setValue("columnpixels", "0");
		dataModel.setValue("rowweight", new Integer(0));
		dataModel.setValue("columnweight", new Integer(0));
		dataModel.setValue("row", new int[] { });
		dataModel.setValue("column", new int[] { });

		rowsTableModel = new TableDataModel();
		columnsTableModel = new TableDataModel();

		StringTokenizer mainTokenizer = new StringTokenizer(layoutNode.getAttribute("horiz"), ",");
		StringTokenizer weightTokenizer = new StringTokenizer(layoutNode.getAttribute("hweights"), ",");

		while (mainTokenizer.hasMoreTokens()) {
			int pixels = Integer.parseInt(mainTokenizer.nextToken());
			int weight = 0;

			if (weightTokenizer.hasMoreTokens())
				weight = Integer.parseInt(weightTokenizer.nextToken());

			columnsTableModel.addRow(new TableEntry(columnsTableModel, pixels, weight));
		}

		mainTokenizer = new StringTokenizer(layoutNode.getAttribute("vert"), ",");
		weightTokenizer = new StringTokenizer(layoutNode.getAttribute("vweights"), ",");

		while (mainTokenizer.hasMoreTokens()) {
			int pixels = Integer.parseInt(mainTokenizer.nextToken());
			int weight = 0;

			if (weightTokenizer.hasMoreTokens())
				weight = Integer.parseInt(weightTokenizer.nextToken());

			rowsTableModel.addRow(new TableEntry(rowsTableModel, pixels, weight));
		}
		URL url = this.getClass().getResource("HIGEditor");
		dialog = constructDialog(url, "HIGEditor", dataModel);
		((TextField) dialog.getWidget("ColumnPixels")).addValidator(new IntegerValidator());
		((TextField) dialog.getWidget("RowPixels")).addValidator(new IntegerValidator());

		rowsTable = (Table) dialog.getWidget("RowsTable");
		rowsTable.setTableDataModel(rowsTableModel);
	
		columnsTable = (Table) dialog.getWidget("ColumnsTable");
		columnsTable.setTableDataModel(columnsTableModel);
		dialog.initDialog(frame);
		dialog.show();
	}

	private void doOK() throws GUIException {
		try {
			dialog.recursiveValidate();
		} catch (Exception e) {
			return;
		}
		String horiz = "", vert = "", hweights = "", vweights = "";
		
		for (int i = 0; i < rowsTableModel.getRowCount(); i++) {
			TableEntry entry = (TableEntry) rowsTableModel.getTableRow(i);
			vert += String.valueOf(entry.getPixels());
			vweights += String.valueOf(entry.getWeight());

			if (rowsTableModel.getRowCount() - 1 != i) {
				vert += ",";
				vweights += ",";
			}
		}

		for (int i = 0; i < columnsTableModel.getRowCount(); i++) {
			TableEntry entry = (TableEntry) columnsTableModel.getTableRow(i);
			horiz += String.valueOf(entry.getPixels());
			hweights += String.valueOf(entry.getWeight());

			if (columnsTableModel.getRowCount() - 1 != i) {
				horiz += ",";
				hweights += ",";
			}
		}
		
		layoutNode.setAttribute("type", "hig");
		layoutNode.setAttribute("horiz", horiz);
		layoutNode.setAttribute("vert", vert);
		layoutNode.setAttribute("hweights", hweights);
		layoutNode.setAttribute("vweights", vweights);
		object.widget.setProperty("layout", LayoutFactory.getInstance().constructLayout(object.widget, layoutNode));

		for (int i = 0; i < object.treeNode.getChildCount(); i++) {
			TreeItem item = (TreeItem) object.treeNode.getChild(i);
			WidgetUserObject childObject = (WidgetUserObject) item.getUserObject();
			Element anchorNode = XMLUtils.getChild(childObject.element, "anchor");

			Widget childWidget = childObject.widget;
			object.widget.removeChildWidget(childWidget);
			object.widget.addChild(childWidget, WidgetTree.createAnchor(object.widget, anchorNode));
		}

		object.widget.revalidate();
	}

	public void eventOccured(GUIEvent event) {
		String name = event.getName();

		try {
			if (name.equals("cancel")) {
				dialog.dispose();
			} else if (name.equals("ok")) {
				doOK();
				dialog.dispose();
			} else if (name.equals("apply")) {
				doOK();
			} else if (name.startsWith("move")) {
				TableDataModel tableDataModel = null;
				String key = null;
				int pos = -1;
				int[] columns = null;
				if (name.endsWith("column")) {
					columns = ((int[]) dataModel.getValue("column"));
					pos = (columns.length == 0) ? -1 : columns[0];
					tableDataModel = columnsTableModel;
					key = "column";
					columnsTable.stopEditors();
				}
				else if (name.endsWith("row")) {
					columns = ((int[]) dataModel.getValue("row"));
					pos = (columns.length == 0) ? -1 : columns[0];
					tableDataModel = rowsTableModel;
					key = "row";
					rowsTable.stopEditors();
				}
				if (name.indexOf("up") != -1 && pos > 0) {
					TableRow tableRow = tableDataModel.getTableRow(pos);
					tableDataModel.removeRow(pos);
					tableDataModel.insertRow(pos -1, tableRow);
					columns[0] = pos - 1;
				}
				else if (name.indexOf("down") != -1 && pos >= 0 && pos < tableDataModel.getRowCount() -1){
					TableRow tableRow = tableDataModel.getTableRow(pos);
					tableDataModel.removeRow(pos);
					tableDataModel.insertRow(pos +1, tableRow);
					columns[0] = pos + 1;
				}
				dataModel.setValue(key, columns);
			} else if (name.equals("insert_column_before") || name.equals("insert_column_after")) {
				try {
					dialog.recursiveValidate();
				} catch (Exception e) {
					return;
				}

				int index = -1;
				int columns[] = ((int[]) dataModel.getValue("column"));
				int pos = (columns.length == 0) ? -1 : columns[0];

				if (name.equals("insert_column_before")) {
					if (pos != -1)
						index = pos;

					if (index < 0)
						index = 0;
				} else if (name.equals("insert_column_after")) {
					if (pos != -1)
						index = pos + 1;
					else
						index = columnsTableModel.getRowCount();
				}

				TableRow tableRow =
					new TableEntry(
						columnsTableModel,
						Integer.parseInt((String) dataModel.getValue("columnpixels")),
						((Integer) dataModel.getValue("columnweight")).intValue());
				columnsTableModel.insertRow(index, tableRow);
			} else if (name.equals("insert_row_before") || name.equals("insert_row_after")) {
				try {
					dialog.recursiveValidate();
				} catch (Exception e) {
					return;
				}

				int index = -1;
				int rows[] = ((int[]) dataModel.getValue("row"));
				int pos = (rows.length == 0) ? -1 : rows[0];

				if (name.equals("insert_row_before")) {
					if (pos != -1)
						index = pos;

					if (index < 0)
						index = 0;
				} else if (name.equals("insert_row_after")) {
					if (pos != -1)
						index = pos + 1;
					else
						index = rowsTableModel.getRowCount();
				}

				TableRow tableRow =
					new TableEntry(
						rowsTableModel,
						Integer.parseInt((String) dataModel.getValue("rowpixels")),
						((Integer) dataModel.getValue("rowweight")).intValue());
				rowsTableModel.insertRow(index, tableRow);
			} else if (name.equals("delete_row")) {
				int rows[] = ((int[]) dataModel.getValue("row"));
				if (rows.length != 0)
					rowsTableModel.removeRow(rows[0]);
			} else if (name.equals("delete_column")) {
				int columns[] = ((int[]) dataModel.getValue("column"));
				if (columns.length != 0)
					columnsTableModel.removeRow(columns[0]);
			}
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}
