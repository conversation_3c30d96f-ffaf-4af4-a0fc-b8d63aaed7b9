/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.net.URL;
import java.util.ArrayList;
import java.util.Iterator;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.LayoutFactory;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.PropertyFactory;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.ComboBox;
import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.Table;
import org.beryl.gui.widgets.TextField;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

public class AddPropertyDialog extends Controller implements ModelChangeListener {
	private static final int IDX_ISTRING = 0;
	private static final int IDX_STRING = 1;
	private static final int IDX_INT = 2;
	private static final int IDX_FLOAT = 3;
	private static final int IDX_DOUBLE = 4;
	private static final int IDX_BOOL = 5;

	private WidgetUserObject userObject = null;
	private Dialog dialog = null;
	private MapDataModel dataModel = null;
	private ComboBox typeCombo = null;
	private TextField valueField = null;
	private TextField nameField = null;
	private Table propertyTable = null;
	private Button okButton = null;

	private class PropertyEntryTableRow extends TableRow {
		private WidgetInfo.PropertyEntry entry = null;

		public PropertyEntryTableRow(WidgetInfo.PropertyEntry entry) throws GUIException {
			super();
			this.entry = entry;
			setValue("name", entry.propertyName);
			setValue("type", entry.propertyType);
			setValue("description", entry.getDescription());
		}

		public WidgetInfo.PropertyEntry getEntry() {
			return entry;
		}
	};

	public AddPropertyDialog(Frame frame, WidgetUserObject userObject) throws GUIException {
		this.userObject = userObject;

		WidgetInfo info = userObject.widget.getWidgetInfo();

		if (info != null) {
			ArrayList properties = new ArrayList();
			NodeList childNodes = userObject.element.getChildNodes();

			for (int i = 0; i < childNodes.getLength(); i++) {
				Node node = childNodes.item(i);

				if (node.getNodeType() == Node.ELEMENT_NODE) {
					if (node.getNodeName().equals("property")) {
						properties.add(((Element) node).getAttribute("name"));
					} else if (node.getNodeName().equals("anchor")) {
						properties.add("anchor");
					} else if (node.getNodeName().equals("layout")) {
						properties.add("layout");
					}
				}

			}

			TableDataModel tableModel = new TableDataModel();
			for (Iterator i = info.getPropertyEntries().iterator(); i.hasNext();) {
				WidgetInfo.PropertyEntry entry = (WidgetInfo.PropertyEntry) i.next();

				if (!properties.contains(entry.propertyName)) {

					if (entry.propertyName.equals("anchor")) {
						if (info.getSupportsAnchor()) {
							tableModel.addRow(new PropertyEntryTableRow(entry));
						}
					} else {
						tableModel.addRow(new PropertyEntryTableRow(entry));
					}
				}
			}

			dataModel = new MapDataModel();

			URL url = AddPropertyDialog.class.getResource("AddPropertyDialog.xml");
			dialog = constructDialog(url, "AddPropertyDialog", dataModel);

			propertyTable = (Table) dialog.getWidget("PropertyTable");
			okButton = (Button) dialog.getWidget("OKButton");
			nameField = (TextField) dialog.getWidget("NameField");
			valueField = (TextField) dialog.getWidget("ValueField");
			typeCombo = (ComboBox) dialog.getWidget("TypeCombo");

			propertyTable.setTableDataModel(tableModel);

			dataModel.setValue("property", new TableRow[] {
			});
			dataModel.setValue("type.index", new Integer(0));
			dataModel.setValue("name", "");
			dataModel.setValue("value", "");
			dataModel.addModelChangeListener(this);
			dataModel.setValue("mode", "common");

			dialog.initDialog(frame);
			dialog.setInitialFocus(propertyTable);
			dialog.show();
		}
	}

	public void eventOccured(GUIEvent event) {
		try {
			if (event.getName().equals("add")) {
				Document document = userObject.element.getOwnerDocument();

				if (dataModel.getValue("mode").equals("common")) {
					TableRow[] entries = (TableRow[]) dialog.getDataModel().getValue("property");

					for (int i = 0; i < entries.length; i++) {
						WidgetInfo.PropertyEntry entry = ((PropertyEntryTableRow) entries[i]).getEntry();

						Element propertyNode = null;

						if (entry.propertyName.equals("anchor")) {
							propertyNode = document.createElement("anchor");

							AnchorEditor anchorEditor = new AnchorEditor(dialog, propertyNode, userObject);
							anchorEditor.show();
						} else {
							if (entry.propertyName.equals("layout")) {
								propertyNode = document.createElement("layout");
							} else {
								propertyNode = document.createElement("property");
								propertyNode.setAttribute("name", entry.propertyName);
							}

							propertyNode.setAttribute("type", entry.propertyType);

							new PropertyTableRow(userObject, entry.defaultValue, propertyNode)
								.getPropertyAdapter()
								.toDOM(
								entry.defaultValue,
								propertyNode);
							
							Object value = null;
							if (entry.propertyName.equals("layout")) {
								value = LayoutFactory.getInstance().constructLayout(userObject.widget, propertyNode);
							} else {
								value = PropertyFactory.getInstance().constructProperty(propertyNode);
							}

							PropertyTableRow row = new PropertyTableRow(userObject, value, propertyNode);

							userObject.widget.setProperty(entry.propertyName, value);
							WidgetTree.revalidate(userObject.widget);

							userObject.tableModel.addRow(row);
							userObject.element.appendChild(propertyNode);

							Builder.markModified();
						}
					}
				} else {
					String name = (String) dataModel.getValue("name"), type = null;

					Element propertyNode = document.createElement("property");
					propertyNode.setAttribute("name", name);

					switch (((Integer) dataModel.getValue("type.index")).intValue()) {
						case IDX_ISTRING :
							type = "istring";
							break;
						case IDX_STRING :
							type = "string";
							break;
						case IDX_INT :
							type = "int";
							break;
						case IDX_FLOAT :
							type = "float";
							break;
						case IDX_DOUBLE :
							type = "double";
							break;
						case IDX_BOOL :
							type = "bool";
							break;
					}
					propertyNode.setAttribute("type", type);
					propertyNode.appendChild(document.createTextNode((String) dataModel.getValue("value")));
					Object value = PropertyFactory.getInstance().constructProperty(propertyNode);
					PropertyTableRow row = new PropertyTableRow(userObject, value, propertyNode);
					userObject.widget.setProperty(name, value);
					WidgetTree.revalidate(userObject.widget);

					userObject.tableModel.addRow(null, row);
					userObject.element.appendChild(propertyNode);
					Builder.markModified();
				}
				dialog.dispose();
			} else if (event.getName().equals("cancel")) {
				dialog.dispose();
			}
		} catch (Exception e) {
			new MessageDialog(dialog, e);
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (e instanceof MapChangeEvent) {
			MapChangeEvent event = (MapChangeEvent) e;
			if (event.getKey().equals("mode")) {
				boolean common = event.getNewValue().equals("common");

				propertyTable.setEnabled(common);
				nameField.setEnabled(!common);
				typeCombo.setEnabled(!common);
				valueField.setEnabled(!common);
			}
			if (dataModel.getValue("mode").equals("common")) {
				okButton.setEnabled(((TableRow[]) dataModel.getValue("property")).length > 0);
			} else {
				okButton.setEnabled(!dataModel.getValue("name").equals(""));
			}
		}
	}
}
