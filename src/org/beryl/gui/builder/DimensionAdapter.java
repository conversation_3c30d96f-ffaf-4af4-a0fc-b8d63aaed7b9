/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.awt.Dimension;
import java.awt.GridLayout;
import java.awt.Point;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.validators.IntegerValidator;
import org.beryl.gui.widgets.Label;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.Table;
import org.beryl.gui.widgets.TextField;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

public class DimensionAdapter implements PropertyAdapter {
	private TableRenderer dimensionRenderer = null;
	private TableEditor dimensionEditor = null;

	private class DimensionRenderer implements TableRenderer {
		public Widget getRenderer(
			Table table,
			Object value,
			boolean isSelected,
			boolean hasFocus,
			TableRow row,
			String key)
			throws GUIException {
			Label label = new Label(null, null);
			if (value instanceof Dimension) {
				Dimension dimension = (Dimension) value;
				label.setProperty("text", (int) dimension.getWidth() + ", " + (int) dimension.getHeight());
			} else {
				Point point = (Point) value;
				label.setProperty("text", point.x + ", " + point.y);
			}

			return label;
		}
	};

	private class DimensionEditor implements TableEditor {
		public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
			final MapDataModel dataModel = new MapDataModel();

			if (value instanceof Dimension) {
				final Dimension dimension = (Dimension) value;

				dataModel.setValue("width", String.valueOf((int) dimension.getWidth()));
				dataModel.setValue("height", String.valueOf((int) dimension.getHeight()));
				dataModel.setValue("value", value);

				dataModel.addModelChangeListener(new ModelChangeListener() {
					public void modelChanged(ModelChangeEvent e) throws GUIException {
						try {
							int width = Integer.parseInt((String) dataModel.getValue("width"));
							int height = Integer.parseInt((String) dataModel.getValue("height"));

							dimension.setSize(width, height);
						} catch (NumberFormatException ex) {
							/* Ignore */
						}
					}
				});
			} else {
				final Point point = (Point) value;

				dataModel.setValue("width", String.valueOf(point.x));
				dataModel.setValue("height", String.valueOf(point.y));
				dataModel.setValue("value", value);

				dataModel.addModelChangeListener(new ModelChangeListener() {
					public void modelChanged(ModelChangeEvent e) throws GUIException {
						try {
							int x = Integer.parseInt((String) dataModel.getValue("width"));
							int y = Integer.parseInt((String) dataModel.getValue("height"));
							
							point.setLocation(x, y);
						} catch (NumberFormatException ex) {
							/* Ignore */
						}
					}
				});
			}

			Panel panel = new Panel(null, null);
			panel.setProperty("layout", new GridLayout(1, 2));
			TextField widthField = new TextField(panel, null);
			widthField.addValidator(new IntegerValidator());
			widthField.setProperty("key", "width");
			TextField heightField = new TextField(panel, null);
			heightField.addValidator(new IntegerValidator());
			heightField.setProperty("key", "height");
			panel.addChild(widthField, null);
			panel.addChild(heightField, null);
			panel.recursiveSetDataModel(dataModel);

			widthField.finalizeConstruction();
			heightField.finalizeConstruction();

			return panel;
		}
	};

	public DimensionAdapter() {
		dimensionRenderer = new DimensionRenderer();
		dimensionEditor = new DimensionEditor();
	}

	public TableEditor getEditor() {
		return dimensionEditor;
	}

	public TableRenderer getRenderer() {
		return dimensionRenderer;
	}

	public Object toValue(Object value, Element propertyNode) {
		return value;
	}

	public void toDOM(Object value, Element propertyNode) {
		Document document = propertyNode.getOwnerDocument();

		if (value instanceof Dimension) {
			Dimension dimension = (Dimension) value;

			Element widthNode = document.createElement("width");
			Element heightNode = document.createElement("height");
			propertyNode.appendChild(widthNode);
			propertyNode.appendChild(heightNode);
			widthNode.appendChild(document.createTextNode(String.valueOf((int) dimension.getWidth())));
			heightNode.appendChild(document.createTextNode(String.valueOf((int) dimension.getHeight())));
		} else {
			Point point = (Point) value;

			Element xNode = document.createElement("x");
			Element yNode = document.createElement("y");
			propertyNode.appendChild(xNode);
			propertyNode.appendChild(yNode);
			xNode.appendChild(document.createTextNode(String.valueOf(point.x)));
			yNode.appendChild(document.createTextNode(String.valueOf(point.y)));
		}
	}
}
