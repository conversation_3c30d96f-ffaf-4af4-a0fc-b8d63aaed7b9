/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.net.URL;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Frame;

/**
 * About dialog
 */
public class About extends Controller {
	private Dialog dialog = null;

	public About(Frame frame) throws GUIException {
		URL url = About.class.getResource("About.xml");
		dialog = constructDialog(url, "About");
		dialog.initDialog(frame);
		dialog.show();
	}
	
	public void eventOccured(GUIEvent event) {
		if (event.getName().equals("ok")) {
			dialog.dispose();
		}
	}
}
