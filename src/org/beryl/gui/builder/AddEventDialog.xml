<?xml version="1.0" encoding="iso-8859-1"?>

<UI version="1.0">
	<widget name="AddEventDialog" class="Dialog">
		<property name="title">builder.addevent.title</property>

		<property name="size" type="dimension">
			<width>300</width>
			<height>200</height>
		</property>

		<widget class="Group">
			<widget class="LabeledWidget">
				<property name="label.text">builder.addevent.event</property>

				<widget name="EventCombo" class="ComboBox">
					<property name="valuekey">event</property>
				</widget>
			</widget>

			<widget class="LabeledWidget">
				<property name="label.text">builder.addevent.name</property>

				<widget class="TextField">
					<property name="key">name</property>
				</widget>
			</widget>


			<widget class="Button" preset="cancel">
				<emit event="clicked" name="cancel"/>
			</widget>

			<widget class="Button" name="OKButton" preset="ok">
				<emit event="clicked" name="ok"/>
				<property name="default" type="bool">true</property>
				<property name="enabled" type="bool">false</property>
			</widget>
		</widget>
	</widget>
</UI>
