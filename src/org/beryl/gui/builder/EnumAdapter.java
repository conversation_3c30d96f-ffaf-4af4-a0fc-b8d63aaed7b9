/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import org.beryl.gui.XMLUtils;
import org.beryl.gui.table.ComboBoxEditor;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.w3c.dom.Element;

public class EnumAdapter implements PropertyAdapter {
	private TableEditor enumEditor = null;

	public EnumAdapter() {
		enumEditor = new ComboBoxEditor(new String[] { "center", "top", "bottom", "left", "right", "north", "south", "east", "west", "nothing", "dispose", "exit", "single", "single_interval", "multiple_interval"});
	}

	public TableEditor getEditor() {
		return enumEditor;
	}

	public TableRenderer getRenderer() {
		return null;
	}

	public Object toValue(Object value, Element propertyNode) {
		return XMLUtils.extractTextChildren(propertyNode);
	}

	public void toDOM(Object value, Element propertyNode) {
		if (value instanceof Integer)
			value = "dispose";
		propertyNode.appendChild(propertyNode.getOwnerDocument().createTextNode((String) value));
	}
}