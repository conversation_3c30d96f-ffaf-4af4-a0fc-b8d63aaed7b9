<?xml version="1.0" encoding="iso-8859-1"?>

<UI version="1.0">
	<widget name="Builder" class="Frame">
		<layout type="border"/>
		<property name="title">builder.builder.title</property>
		<property name="helpid" type="string">preface</property>
		<property name="defaultCloseOperation" type="enum">nothing</property>
		<emit event="close" name="quit"/>

		<property name="size" type="dimension">
			<width>450</width>
			<height>100</height>
		</property>

		<property name="location" type="point">
			<x>0</x>
			<y>0</y>
		</property>

		<property name="spacing" type="int">5</property>

		<widget class="MenuBar">
			<widget class="Menu">
				<property name="text">builder.common.file</property>
				<widget class="MenuItem">
					<property name="text">builder.common.new</property>
					<property name="icon" type="icon">new</property>
					<property name="accelerator">builder.common.new.accelerator</property>
					<emit event="selected" name="new"/>
				</widget>
				<widget class="Separator"/>
				<widget class="MenuItem">
					<property name="text">builder.common.open</property>
					<property name="icon" type="icon">open</property>
					<property name="accelerator">builder.common.open.accelerator</property>
					<emit event="selected" name="open"/>
				</widget>
				<widget class="MenuItem">
					<property name="text">builder.common.save</property>
					<property name="icon" type="icon">save</property>
					<property name="accelerator">builder.common.save.accelerator</property>
					<emit event="selected" name="save"/>
				</widget>
				<widget class="MenuItem">
					<property name="text">builder.common.saveas</property>
					<property name="icon" type="icon">saveas</property>
					<property name="accelerator">builder.common.saveas.accelerator</property>
					<emit event="selected" name="saveAs"/>
				</widget>
				<widget class="Separator"/>
				<widget class="MenuItem">
					<property name="text">builder.common.quit</property>
					<property name="icon" type="icon">quit</property>
					<property name="accelerator">builder.common.quit.accelerator</property>
					<emit event="selected" name="quit"/>
				</widget>
			</widget>

			<widget class="Menu">
				<property name="text">builder.builder.tools</property>
				<widget class="MenuItem">
					<property name="text">builder.builder.i18n</property>
					<property name="icon" type="icon">language</property>
					<property name="accelerator">builder.builder.i18n.accelerator</property>
					<emit event="selected" name="i18n"/>
				</widget>
				<widget class="MenuItem">
					<property name="text">builder.builder.lookandfeel</property>
					<property name="icon" type="icon">lookandfeel</property>
					<property name="accelerator">builder.builder.lookandfeel.accelerator</property>
					<emit event="selected" name="lookandfeel"/>
				</widget>
				<widget class="Separator"/>
				<widget class="MenuItem">
					<property name="text">builder.builder.genskel</property>
					<property name="icon" type="icon">skeleton</property>
					<property name="accelerator">builder.builder.genskel.accelerator</property>
					<emit event="selected" name="skeleton"/>
				</widget>
			</widget>

			<widget class="Spacer">
				<property name="type" type="string">glue</property>
				<property name="axis" type="string">h</property>
			</widget>

			<widget class="Menu">
				<property name="text">builder.common.help</property>

				<widget class="MenuItem">
					<property name="text">builder.common.helpcontents</property>
					<property name="icon" type="icon">help</property>
					<property name="accelerator">builder.common.helpcontents.accelerator</property>
					<emit event="selected" name="help"/>
				</widget>

				<widget class="Separator"/>

				<widget class="MenuItem">
					<property name="text">builder.common.about</property>
					<property name="icon" type="icon">about</property>
					<property name="accelerator">builder.common.about.accelerator</property>
					<emit event="selected" name="about"/>
				</widget>
			</widget>
		</widget>

		<widget class="TabbedPane">
			<property name="BasicPanel.title">builder.builder.palette.basic</property> 
			<widget name="BasicPanel" class="Panel">
				<layout type="flow"/>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_frame</property>
					<property name="toolTipText">builder.builder.widget.frame.desc</property>
					<emit event="clicked" name="insert:Frame"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_dialog</property>
					<property name="toolTipText">builder.builder.widget.dialog.desc</property>
					<emit event="clicked" name="insert:Dialog"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_panel</property>
					<property name="toolTipText">builder.builder.widget.panel.desc</property>
					<emit event="clicked" name="insert:Panel"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_text</property>
					<property name="toolTipText">builder.builder.widget.label.desc</property>
					<emit event="clicked" name="insert:Label"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_button</property>
					<property name="toolTipText">builder.builder.widget.button.desc</property>
					<emit event="clicked" name="insert:Button"/>
				</widget>
			</widget>

			<property name="MenuPanel.title">builder.builder.palette.menu</property> 
			<widget name="MenuPanel" class="Panel">
				<layout type="flow"/>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_menubar</property>
					<property name="toolTipText">builder.builder.widget.menubar.desc</property>
					<emit event="clicked" name="insert:MenuBar"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_menu</property>
					<property name="toolTipText">builder.builder.widget.menu.desc</property>
					<emit event="clicked" name="insert:Menu"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_menuitem</property>
					<property name="toolTipText">builder.builder.widget.menuitem.desc</property>
					<emit event="clicked" name="insert:MenuItem"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_popupmenu</property>
					<property name="toolTipText">builder.builder.widget.popupmenu.desc</property>
					<emit event="clicked" name="insert:PopupMenu"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_toolbar</property>
					<property name="toolTipText">builder.builder.widget.toolbar.desc</property>
					<emit event="clicked" name="insert:ToolBar"/>
				</widget>
			</widget>

			<property name="InputPanel.title">builder.builder.palette.input</property> 
			<widget name="InputPanel" class="Panel">
				<layout type="flow"/>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_checkbox</property>
					<property name="toolTipText">builder.builder.widget.checkbox.desc</property>
					<emit event="clicked" name="insert:CheckBox"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_radiobutton</property>
					<property name="toolTipText">builder.builder.widget.radiobutton.desc</property>
					<emit event="clicked" name="insert:RadioButton"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_buttongroup</property>
					<property name="toolTipText">builder.builder.widget.buttongroup.desc</property>
					<emit event="clicked" name="insert:ButtonGroup"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_combobox</property>
					<property name="toolTipText">builder.builder.widget.combobox.desc</property>
					<emit event="clicked" name="insert:ComboBox"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_list</property>
					<property name="toolTipText">builder.builder.widget.list.desc</property>
					<emit event="clicked" name="insert:List"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_textfield</property>
					<property name="toolTipText">builder.builder.widget.textfield.desc</property>
					<emit event="clicked" name="insert:TextField"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_passwordfield</property>
					<property name="toolTipText">builder.builder.widget.passwordfield.desc</property>
					<emit event="clicked" name="insert:PasswordField"/>
				</widget>
			</widget>

			<property name="OtherPanel.title">builder.builder.palette.other</property> 
			<widget name="OtherPanel" class="Panel">
				<layout type="flow"/>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_textpane</property>
					<property name="toolTipText">builder.builder.widget.textpane.desc</property>
					<emit event="clicked" name="insert:TextPane"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_table</property>
					<property name="toolTipText">builder.builder.widget.table.desc</property>
					<emit event="clicked" name="insert:Table"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_tree</property>
					<property name="toolTipText">builder.builder.widget.tree.desc</property>
					<emit event="clicked" name="insert:Tree"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_progressbar</property>
					<property name="toolTipText">builder.builder.widget.progressbar.desc</property>
					<emit event="clicked" name="insert:ProgressBar"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_tabbedpane</property>
					<property name="toolTipText">builder.builder.widget.tabbedpane.desc</property>
					<emit event="clicked" name="insert:TabbedPane"/>
				</widget>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_splitpane</property>
					<property name="toolTipText">builder.builder.widget.splitpane.desc</property>
					<emit event="clicked" name="insert:SplitPane"/>
				</widget>
			</widget>

			<property name="CustomPanel.title">builder.builder.palette.custom</property>
			<widget name="CustomPanel" class="Panel">
				<layout type="flow"/>
				
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_spacer</property>
					<property name="toolTipText">builder.builder.widget.spacer.desc</property>
					<emit event="clicked" name="insert:Spacer"/>
				</widget>

				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_separator</property>
					<property name="toolTipText">builder.builder.widget.separator.desc</property>
					<emit event="clicked" name="insert:Separator"/>
				</widget>

				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_item</property>
					<property name="toolTipText">builder.builder.widget.item.desc</property>
					<emit event="clicked" name="insert:Item"/>
				</widget>

				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_treeitem</property>
					<property name="toolTipText">builder.builder.widget.treeitem.desc</property>
					<emit event="clicked" name="insert:TreeItem"/>
				</widget>
				
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_labeledwidget</property>
					<property name="toolTipText">builder.builder.widget.labeledwidget.desc</property>
					<emit event="clicked" name="insert:LabeledWidget"/>
				</widget>
				
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_group</property>
					<property name="toolTipText">builder.builder.widget.group.desc</property>
					<emit event="clicked" name="insert:Group"/>
				</widget>
			</widget>

			<property name="SpecialPanel.title">builder.builder.palette.special</property>
			<widget name="SpecialPanel" class="Panel">
				<layout type="flow"/>
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_outlookbar</property>
					<property name="toolTipText">builder.builder.widget.outlookbar.desc</property>
					<emit event="clicked" name="insert:OutlookBar"/>
				</widget>

				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_outlookpanel</property>
					<property name="toolTipText">builder.builder.widget.outlookpanel.desc</property>
					<emit event="clicked" name="insert:OutlookPanel"/>
				</widget>

				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_iconview</property>
					<property name="toolTipText">builder.builder.widget.iconview.desc</property>
					<emit event="clicked" name="insert:IconView"/>
				</widget>
				
				<widget class="Button" preset="hover">
					<property name="icon" type="icon">widget_syntaxeditor</property>
					<property name="toolTipText">builder.builder.widget.syntaxeditor.desc</property>
					<emit event="clicked" name="insert:SyntaxEditor"/>
				</widget>
			</widget>
		</widget>
	</widget>
</UI>
