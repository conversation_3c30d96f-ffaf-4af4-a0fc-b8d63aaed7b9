/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
package org.beryl.gui.builder;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.widgets.Table;
import org.beryl.gui.widgets.Tree;

public class NameTableRow extends TableRow implements ModelChangeListener {
	private WidgetUserObject object = null;
	private static TableEditor stringEditor = new StringAdapter.StringEditor();

	public NameTableRow(WidgetUserObject object) throws GUIException {
		this.object = object;
		setValue("name", "name");
		setValue("value", object.element.getAttribute("name"));
		addModelChangeListener(this);
	}
	
	public boolean isEditable(String key) {
		if (key.equals("value")) {
			return true;
		} else {
			return false;
		}
	}

	public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
		return stringEditor.getEditor(table, value, row, key);
	}

	
	public void modelChanged(ModelChangeEvent e) throws GUIException {
		String name = (String) getValue("value");

		if (!name.equals("")) {
			object.element.setAttribute("name", name);
			object.widget.setName(name);
		} else {
			object.element.removeAttribute("name");
			object.widget.setName(null);
		}
		object.treeNode.setText(WidgetTree.describeWidget(object.element));
		Tree tree = (Tree) object.treeNode.getParentWidgetByClass(Tree.class);
		tree.nodeChanged(object.treeNode);
	}
}
