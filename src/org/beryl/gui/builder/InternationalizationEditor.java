/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.net.URL;
import java.util.Iterator;
import java.util.Properties;

import org.beryl.gui.Controller;
import org.beryl.gui.DialogUtils;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.View;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetFactory;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.TableDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.PopupMenu;
import org.beryl.gui.widgets.Table;

/**
 * Internationalization editor
 */
public class InternationalizationEditor extends Controller {
	private static boolean modified = false;
	private WidgetTree widgetTree = null;
	private Properties properties = null;
	private MapDataModel dataModel = null;
	private Frame frame = null;
	private Table propertyTable = null;
	private TableDataModel propertyModel = null;

	private class PropertyTableRow extends TableRow {
		private String key = null;

		public PropertyTableRow(String key) {
			this.key = key;
		}

		public boolean isEditable(String key) {
			return key.equals("value");
		}

		public Object getValue(String key) {
			if (key.equals("key"))
				return this.key;
			else if (key.equals("value"))
				return properties.getProperty(this.key);
			else
				throw new RuntimeException("Invalid key selected");
		}

		public void setValue(View source, String key, Object newValue) throws GUIException {
			if (key.equals("value")) {
				properties.setProperty(this.key, (String) newValue);
				modified = true;
			} else {
				throw new RuntimeException("Invalid key selected");
			}
		}
	};

	public InternationalizationEditor(WidgetTree widgetTree, Properties properties) throws GUIException {
		this.properties = properties;
		this.widgetTree = widgetTree;
		dataModel = new MapDataModel();
		URL url = this.getClass().getResource("InternationalizationEditor.xml");
		frame = constructFrame(url, "InternationalizationEditor", dataModel);
		propertyTable = (Table) frame.getWidget("PropertyTable");

		propertyModel = new TableDataModel();
		buildModel();
		propertyTable.setTableDataModel(propertyModel);
		frame.show();
	}

	private void buildModel() throws GUIException {
		dataModel.setValue("property.value", new TableRow[] {
		});
		propertyModel.clear();
		for (Iterator i = properties.keySet().iterator(); i.hasNext();)
			propertyModel.addRow(new PropertyTableRow((String) i.next()));
	}

	public boolean isVisible() throws GUIException {
		return frame.isVisible();
	}

	public boolean doAskSaveCancel(Frame frame) throws GUIException {
		if (modified) {
			switch (DialogUtils
				.showYesNoCancelDialog(
					frame,
					getString("builder.savei18n.title"),
					getString("builder.savei18n.label"))) {
				case DialogUtils.RESULT_YES :
					doSave();
					return true;
				case DialogUtils.RESULT_NO :
					return true;
				case DialogUtils.RESULT_CANCEL :
					return false;
			}
		}
		return true;
	}

	private void doSave() throws GUIException {
		try {
			File file = DialogUtils.showSaveFileDialog(frame, "properties");
			if (file != null) {
				properties.store(new FileOutputStream(file), "Internationalization file created using the Builder");
			}
		} catch (Exception e) {
			throw new GUIException("Error while saving file", e);
		}
	}

	public void eventOccured(GUIEvent event) {
		String name = event.getName();
		try {
			if (name.equals("add")) {
				URL url = this.getClass().getResource("AddInternationalizationDialog.xml");
				final Dialog dialog =
					(Dialog) WidgetFactory
						.getInstance()
						.constructWidget(url, "AddInternationalizationDialog", new GUIEventListener() {
					public void eventOccured(GUIEvent event) {
						try {
							Widget source = event.getSource();
							if (event.getName().equals("ok")) {
								String key = (String) source.getDataModel().getValue("key");
								String value = (String) source.getDataModel().getValue("value");

								boolean isNew = (properties.getProperty(key) == null);
								properties.setProperty(key, value);
								if (isNew)
									propertyModel.addRow(new PropertyTableRow(key));
								modified = true;
							}
							((Dialog) source.getParentWidgetByClass(Dialog.class)).dispose();
						} catch (Exception e) {
							new MessageDialog(e);
						}
					}
				}, new MapDataModel());
				dialog.initDialog(frame);
				dialog.show();
			} else if (name.equals("popup")) {
				URL url = this.getClass().getResource("PropertyPopup.xml");
				PopupMenu popup = (PopupMenu) constructWidget(url, "PropertyPopup");
				popup.popup(event);
			} else if (name.equals("delete")) {
				TableRow rows[] = (TableRow[]) dataModel.getValue("property.value");
				for (int i = 0; i < rows.length; i++) {
					PropertyTableRow row = (PropertyTableRow) rows[i];
					propertyModel.removeRow(row);
					properties.remove(row.getValue("key"));
				}
			} else if (name.equals("clear")) {
				dataModel.setValue("property.value", new TableRow[] {
				});
				propertyModel.clear();
				properties.clear();
			} else if (name.equals("import")) {
				File file = DialogUtils.showOpenFileDialog(frame, "properties");
				if (file != null) {
					properties.load(new FileInputStream(file));
					buildModel();
				}
			} else if (name.equals("save")) {
				doSave();
			} else if (name.equals("refresh")) {
				widgetTree.refreshInternationalProperties();
			}
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}