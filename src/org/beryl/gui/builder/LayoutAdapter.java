/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.builder;

import java.awt.BorderLayout;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.ComboBox;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.Panel;
import org.beryl.gui.widgets.Table;
import org.w3c.dom.Element;

public class LayoutAdapter implements PropertyAdapter, GUIEventListener {
	/**
	 * List data model containing the available layout types
	 */
	private static ListDataModel layoutModel = null;

	private TableEditor layoutEditor = null;

	static {
		try {
			layoutModel = new ListDataModel();
			layoutModel.addValue(null, "border");
			layoutModel.addValue(null, "hig");
			layoutModel.addValue(null, "vbox");
			layoutModel.addValue(null, "hbox");
			layoutModel.addValue(null, "flow");
			layoutModel.addValue(null, "lflow");
			layoutModel.addValue(null, "rflow");
		} catch (GUIException e) {
			/* Won't happen */
			throw new RuntimeException(e);
		}
	};

	private class LayoutEditor implements TableEditor {
		public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
			Panel panel = new Panel(null, null);

			ComboBox comboBox = new ComboBox(panel, null);
			final Button button = new Button(panel, null);
			final MapDataModel dataModel = new MapDataModel();
			dataModel.setValue("userobject", ((PropertyTableRow) row).getUserObject());
			dataModel.setValue("frame", table.getParentWidgetByClass(Frame.class));
			dataModel.setValue("node", ((PropertyTableRow) row).getPropertyNode());

			button.setProperty("text", "...");
			button.addListener("clicked", "modify", LayoutAdapter.this);
			panel.addChild(comboBox, "Center");
			panel.addChild(button, "East");
			comboBox.setProperty("valuekey", "value");
			comboBox.setListDataModel(layoutModel);
			panel.recursiveSetDataModel(dataModel);
			dataModel.addModelChangeListener(new ModelChangeListener() {
				public void modelChanged(ModelChangeEvent e) throws GUIException {
					button.setEnabled("hig".equals(dataModel.getValue("value")));
				}
			});
			dataModel.setValue("value", value);
			return panel;
		}
	}

	public LayoutAdapter() {
		layoutEditor = new LayoutEditor();
	}

	public TableRenderer getRenderer() {
		return null;
	}

	public TableEditor getEditor() {
		return layoutEditor;
	}

	public Object toValue(Object value, Element propertyNode) {
		String type = propertyNode.getAttribute("type");
		if (type.equals(""))
			type="hig";
		return type;
	}

	public void toDOM(Object value, Element propertyNode) {
		if (value instanceof BorderLayout)
			propertyNode.setAttribute("type", "border"); // Default layout 
		else
			propertyNode.setAttribute("type", (String) value);
	}

	public void eventOccured(GUIEvent event) {
		try {
			String layout = (String) event.getSource().getDataModel().getValue("value");
			if (layout.equals("hig")) {
				new HIGEditor(
				(Frame) event.getSource().getDataModel().getValue("frame"),
				(Element) event.getSource().getDataModel().getValue("node"),
					(WidgetUserObject) event.getSource().getDataModel().getValue("userobject"));
			}
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}
