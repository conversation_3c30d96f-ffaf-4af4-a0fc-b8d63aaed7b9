/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import groovy.lang.GroovyClassLoader;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URL;

import org.codehaus.groovy.syntax.SyntaxException;


/**
 * Abstract scripted controller class. All created GUI scripts should subclass
 * from this class
 */
public abstract class ScriptedController extends Controller {
	private GroovyClassLoader loader = null;

	/**
	 * Create a new scripted controller
	 */
	public ScriptedController() {
		loader = (GroovyClassLoader) Thread.currentThread().getContextClassLoader();
	}
	
	/**
	 * Load a groovy script containing another scripted controller
	 * @param scriptFile The script file
	 * @return The instance
	 * @throws GUIExceptions If there were errors while loading/parsing/compiling/instantiating the script
	 */
	protected ScriptedController loadScript(String scriptFile) throws GUIException {
		return loadScript(scriptFile, null);
	}

	/**
	 * Load a groovy script containing another scripted controller
	 * @param scriptFile The script file
	 * @return The instance
	 * @throws GUIExceptions If there were errors while loading/parsing/compiling/instantiating the script
	 */
	protected ScriptedController loadScript(String scriptFile, Object parameters[]) throws GUIException {
		try {
			InputStream stream = null;
			File file = new File(scriptFile);
			
			if (file.exists()) {
				stream = new FileInputStream(file);
			} else {
				URL url = getClass().getResource(scriptFile);
				if (url == null)
					throw new GUIException("Script file [" + scriptFile + "] not found");
				stream = url.openStream();
			}
			
			
			if (file.exists()) {
				stream = new FileInputStream(file);
			} else {
				URL url = getClass().getResource(scriptFile);
				if (url == null)
					throw new GUIException("Script file [" + scriptFile + "] not found");
				stream = url.openStream();
			}

			Class scriptClass = loader.parseClass(new BufferedInputStream(stream), scriptFile);
			Object instance = scriptClass.newInstance();
			
			if (parameters == null || parameters.length == 0)
				return (ScriptedController) instance;

			Method methods[] = scriptClass.getMethods();

			String className = scriptClass.getName();
			if (className.indexOf('.') != -1)
				className = className.substring(className.lastIndexOf('.')+1, className.length());

			for (int i=0; i<methods.length; i++) {
				Method method = methods[i];
				if (method.getName().equals(className) && method.getParameterTypes().length == parameters.length) {
					method.invoke(instance, parameters);
					return (ScriptedController) instance;
				}
			}
			throw new GUIException("No matching constructor found while trying to load a script");
		} catch (IOException e) {
			throw new GUIException("I/O Exception while trying to load a script", e);
		} catch (SyntaxException e) {
			throw new GUIException("Syntax error while trying to load a script", e);
		} catch (IllegalAccessException e) {
			throw new GUIException("Access error while trying to load a script", e);
		} catch (InstantiationException e) {
			throw new GUIException("Instantiation error while trying to load a script", e);
		} catch (InvocationTargetException e) {
			throw new GUIException("Exception while trying to instantiate a script", e);
		} catch (ClassCastException e) {
			throw new GUIException("Error while trying to load a script: Does not extend from ScriptedController", e);
		}
	}
	
	public static ScriptedController launchScript(String scriptFile, Object parameters[]) throws GUIException {
		/* Initialize the script class loader */
		Thread.currentThread().setContextClassLoader(new GroovyClassLoader(ScriptedController.class.getClassLoader()));
		
		ScriptedController tmp = new ScriptedController() {
			public void eventOccured(GUIEvent event) {
			}
		};
		return tmp.loadScript(scriptFile, parameters);
	}
}
