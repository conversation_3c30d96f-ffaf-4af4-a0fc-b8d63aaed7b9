/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.validators;

import java.text.ParseException;
import java.text.SimpleDateFormat;

import org.beryl.gui.InternationalizationManager;
import org.beryl.gui.View;
import org.beryl.gui.widgets.TextField;
;

/**
 * A validator for dates in different formats
 */

public class DateFieldValidator implements Validator {
	public static final SimpleDateFormat EUROPEAN_DATETIME = new SimpleDateFormat("dd.MM.yyyy HH:mm");
	public static final SimpleDateFormat EUROPEAN_DATE = new SimpleDateFormat("dd.MM.yyyy");
	public static final SimpleDateFormat US_DATETIME = new SimpleDateFormat("MM/dd/yyyy hh:mm a");
	public static final SimpleDateFormat US_DATE = new SimpleDateFormat("MM/dd/yyyy");
	private SimpleDateFormat format = null;
	private int length = -1;

	public DateFieldValidator(SimpleDateFormat format) {
		this.format = format;
		length = format.toPattern().length();
	}

	public void validate(View view) throws ValidationException {
		TextField field = (TextField) view;
		String text = field.getText();

		try {
			format.parse(field.getText());
			if (text.length() != length)
				throw new ValidationException(view, InternationalizationManager.getString("xmlgui.validator.date.invalid"));
		} catch (ParseException e) {
			throw new ValidationException(view, InternationalizationManager.getString("xmlgui.validator.date.invalid"));
		}
	}
}
