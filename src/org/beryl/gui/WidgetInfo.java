/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.util.ArrayList;
import java.util.List;

/**
 * The WidgetInfo class reveals a widget's available
 * properties, presets and events
 */

public class WidgetInfo {
	private boolean supportsAnchor = true;
	private ArrayList properties = null;
	private ArrayList presets = null;
	private ArrayList events = null;
	private Class clazz = null;
	private String className = null;

	public class PropertyEntry {
		public String propertyName;
		public String propertyType;
		public Object defaultValue;
		public String className;
		
		public PropertyEntry() {
			className = WidgetInfo.this.className;
		}

		public String getDescription() {
			return InternationalizationManager.getString("xmlgui." + className + ".property."+propertyName);
		}

		public String toString() {
			return propertyName + " (" + getDescription() + ")";
		}
	};

	public class PresetEntry {
		public String presetName;
		public String className;
		
		public PresetEntry() {
			className = WidgetInfo.this.className;
		}

		public String getDescription() {
			return InternationalizationManager.getString("xmlgui." + className + ".preset."+presetName);
		}

		public String toString() {
			return presetName + " (" + getDescription() + ")";
		}
	};

	public class EventEntry {
		public String eventName;
		public String className;
		
		public EventEntry() {
			className = WidgetInfo.this.className;
		}

		public String getDescription() {
			return InternationalizationManager.getString("xmlgui." + className + ".event."+eventName);
		}

		public String toString() {
			return eventName + " (" + getDescription() + ")";
		}
	};

	public WidgetInfo(Class clazz) {
		this.clazz = clazz;
		properties = new ArrayList();
		presets = new ArrayList();
		events = new ArrayList();
		
		String fullName = clazz.getName();
		className = fullName.substring(fullName.lastIndexOf('.') + 1);
	}

	public WidgetInfo(Class clazz, WidgetInfo parentInfo) {
		this(clazz);

		/* Inherit properties */
		properties.addAll(parentInfo.getPropertyEntries());
		/* Inherit events */
		events.addAll(parentInfo.getEventEntries());
		/* Presets are not inherited */
	}

	public List getPropertyEntries() {
		return properties;
	}

	public List getPresetEntries() {
		return presets;
	}

	public PropertyEntry getPropertyEntry(String propertyName) throws GUIException {
		for (int i=0; i<properties.size(); i++) {
			PropertyEntry entry = (PropertyEntry) properties.get(i);
			if (entry.propertyName.equals(propertyName))
				return entry;
		}
		throw new GUIException("No property named ["+propertyName+"] could be found for class ["+clazz.getName()+"]");
	}

	public EventEntry getEventEntry(String eventName) throws GUIException {
		for (int i=0; i<events.size(); i++) {
			EventEntry entry = (EventEntry) events.get(i);
			if (entry.eventName.equals(eventName))
				return entry;
		}
		throw new GUIException("No event named ["+eventName+"] could be found for class ["+clazz.getName()+"]");
	}

	public List getEventEntries() {
		return events;
	}

	public void addPreset(String presetName) {
		PresetEntry entry = new PresetEntry();
		entry.presetName = presetName;
		presets.add(entry);
	}

	public void addEvent(String eventName) {
		EventEntry entry = new EventEntry();
		entry.eventName = eventName;
		events.add(entry);
	}

	public void addProperty(String propertyName, String propertyType) {
		addProperty(propertyName, propertyType, null);
	}

	public void removeProperty(String propertyName) {
		for (int i=0; i<properties.size(); i++) {
			PropertyEntry entry = (PropertyEntry) properties.get(i);
			if (entry.propertyName.equals(propertyName)) {
				properties.remove(i);
				return;
			}
		}
	}

	public void addProperty(String propertyName, String propertyType, Object defaultValue) {
		PropertyEntry info = new PropertyEntry();
		info.propertyName = propertyName;
		info.propertyType = propertyType;
		info.defaultValue = defaultValue;
		properties.add(info);
	}

	public String getDescription() {
		return InternationalizationManager.getString("xmlgui."+className);
	}

	public Class getWidgetClass() {
		return clazz;
	}

	public boolean getSupportsAnchor() {
		return supportsAnchor;
	}

	public void setSupportsAnchor(boolean supportsAnchor) {
		this.supportsAnchor = supportsAnchor;
	}
}
