/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;

import javax.swing.ImageIcon;
import javax.swing.JScrollPane;
import javax.swing.JTextPane;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.text.BadLocationException;
import javax.swing.text.Document;
import javax.swing.text.Style;
import javax.swing.text.StyleConstants;
import javax.swing.text.StyleContext;
import javax.swing.text.StyleConstants.ColorConstants;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;

public class TextPane extends Widget {
	protected static WidgetInfo textPaneInfo = null;
	private JTextPane textPane = null;
	private JScrollPane scrollPane = null;
	private String key = null;
	private Document document = null;
	private boolean sendEvents = true;
	private int counter = 0;

	static {
		textPaneInfo = new WidgetInfo(TextPane.class, widgetInfo);
		textPaneInfo.addProperty("key", "string", "");
		textPaneInfo.addProperty("verticalScrollBar", "bool", Boolean.FALSE);
		textPaneInfo.addProperty("horizontalScrollBar", "bool", Boolean.FALSE);
	};

	public TextPane(Widget parent, String name) throws GUIException {
		super(parent, name);
		textPane = new JTextPane();
		document = textPane.getDocument();
		scrollPane = new JScrollPane(textPane);

		Style def = StyleContext.getDefaultStyleContext().getStyle(StyleContext.DEFAULT_STYLE);

		Style black = textPane.addStyle("black", def);
		ColorConstants.setForeground(black, Color.black);

		Style white = textPane.addStyle("white", def);
		ColorConstants.setForeground(white, Color.white);

		Style gray = textPane.addStyle("gray", def);
		ColorConstants.setForeground(gray, Color.gray);

		Style blue = textPane.addStyle("blue", def);
		ColorConstants.setForeground(blue, Color.blue);

		Style cyan = textPane.addStyle("cyan", def);
		ColorConstants.setForeground(cyan, Color.cyan);

		Style yellow = textPane.addStyle("yellow", def);
		ColorConstants.setForeground(yellow, Color.yellow);

		Style green = textPane.addStyle("green", def);
		ColorConstants.setForeground(green, Color.green);

		Style darkGreen = textPane.addStyle("darkgreen", def);
		ColorConstants.setForeground(darkGreen, new Color(0, 100, 0));

		Style red = textPane.addStyle("red", def);
		ColorConstants.setForeground(red, Color.red);
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("key".equals(name))
			key = (String) value;
		else if ("verticalScrollBar".equals(name))
			scrollPane.setVerticalScrollBarPolicy(
				((Boolean) value).booleanValue()
					? JScrollPane.VERTICAL_SCROLLBAR_ALWAYS
					: JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
		else if ("horizontalScrollBar".equals(name))
			scrollPane.setHorizontalScrollBarPolicy(
				((Boolean) value).booleanValue()
					? JScrollPane.HORIZONTAL_SCROLLBAR_ALWAYS
					: JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		else
			super.setProperty(name, value);
	}

	private void reload() {
		MapDataModel model = getDataModel();
		if (model != null) {
			String value = (String) model.getValue(key);
			if (value != null)
				textPane.setText(value);
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		sendEvents = false;
		try {
			if (e.getSource() == this) {
				/* New data model */
				reload();
			} else if (e instanceof MapChangeEvent) {
				MapChangeEvent event = (MapChangeEvent) e;
				if (event.getKey() == null) {
					reload();
				} else if (event.getKey().equals(key)) {
					textPane.setText((String) event.getNewValue());
				}
			}
		} finally {
			sendEvents = true;
		}
	}

	public void finalizeConstruction() {
		textPane.setPreferredSize(new Dimension(MAX_WIDTH, DEFAULT_HEIGHT));
		document.addDocumentListener(new DocumentListener() {
			public void insertUpdate(DocumentEvent e) {
				changedUpdate(e);
			}

			public void removeUpdate(DocumentEvent e) {
				changedUpdate(e);
			}

			public void changedUpdate(DocumentEvent e) {
				if (sendEvents) {
					try {
						sendEvents = false;
						MapDataModel model = getDataModel();
						if (model != null && key != null) {
							String text = null;
							text = textPane.getText();
							model.setValue(TextPane.this, key, text);
						}
					} catch (GUIException ex) {
						throw new RuntimeException(ex);
					} finally {
						sendEvents = true;
					}
				}
			}
		});
	}

	public void addIcon(ImageIcon icon) throws GUIException {
		String name = "icon" + counter;
		Style style = textPane.addStyle(name, null);
		StyleConstants.setIcon(style, icon);
		addText(name, name);
		counter++;
	}

	public void addText(String text) throws GUIException {
		addText(text, null);
	}

	public final void addText(String text, String style) throws GUIException {
		try {
			document.insertString(document.getLength(), text, style == null ? null : textPane.getStyle(style));
		} catch (BadLocationException e) {
			throw new GUIException("Error while inserting text", e);
		}
	}

	public Component getWidget() {
		return scrollPane;
	}

	public Component getRealWidget() {
		return textPane;
	}

	public void setEnabled(boolean enabled) throws GUIException {
		super.setEnabled(enabled);
		textPane.setEnabled(enabled);
	}

	public WidgetInfo getWidgetInfo() {
		return textPaneInfo;
	}
}