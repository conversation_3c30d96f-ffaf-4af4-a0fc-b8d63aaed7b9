/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Component;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.JMenuItem;
import javax.swing.KeyStroke;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;

public class MenuItem extends Widget {
	protected static WidgetInfo menuItemInfo = null;
	private JMenuItem menuItem = null;

	static {
		menuItemInfo = new WidgetInfo(MenuItem.class, widgetInfo);
		menuItemInfo.addProperty("accelerator", "istring", "");
		menuItemInfo.addProperty("mnemonic", "istring", "");
		menuItemInfo.addProperty("text", "istring", "");
		menuItemInfo.addEvent("selected");
		menuItemInfo.setSupportsAnchor(false);
		try {
			menuItemInfo.addProperty("icon", "icon", ImageIconFactory.getIcon("broken"));
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	};

	public MenuItem(Widget parent, String name) throws GUIException {
		super(parent, name);
		menuItem = new JMenuItem();
	}

	public void addListener(String event, final String name, final GUIEventListener listener) throws GUIException {
		if ("selected".equals(event)) {
			menuItem.addActionListener(new ActionListener() {
				public void actionPerformed(ActionEvent e) {
					listener.eventOccured(new GUIEvent(MenuItem.this, name, e));
				}
			});
		} else {
			super.addListener(event, name, listener);
		}
	}

	/** 
	 * Construct a keystroke from a string
	 */
	private KeyStroke parseKeyStroke(String keyStroke) throws GUIException {
		if (keyStroke == null)
			return null;

		int modifiers = 0;
		int index = keyStroke.indexOf('+');
		if (index != -1) {
			for (int i = 0; i < index; i++) {
				switch (Character.toUpperCase(keyStroke.charAt(i))) {
					case 'A' :
						modifiers |= InputEvent.ALT_MASK;
						break;
					case 'C' :
						modifiers |= InputEvent.CTRL_MASK;
						break;
					case 'M' :
						modifiers |= InputEvent.META_MASK;
						break;
					case 'S' :
						modifiers |= InputEvent.SHIFT_MASK;
						break;
				}
			}
		}
		String key = keyStroke.substring(index + 1);
		if (key.length() == 1) {
			char ch = Character.toUpperCase(key.charAt(0));
			if (modifiers == 0)
				return KeyStroke.getKeyStroke(ch);
			else
				return KeyStroke.getKeyStroke(ch, modifiers);
		} else if (key.length() == 0) {
			throw new GUIException("Invalid key stroke: " + keyStroke);
		} else {
			try {
				int ch = KeyEvent.class.getField("VK_".concat(key)).getInt(null);
				return KeyStroke.getKeyStroke(ch, modifiers);
			} catch (Exception e) {
				throw new GUIException("Invalid key stroke: " + keyStroke);
			}
		}
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("accelerator".equals(name)) {
			if (value != null && !value.equals("")) {
				KeyStroke stroke = parseKeyStroke((String) value);
				menuItem.setAccelerator(stroke);
			}
		} else if (name.equals("mnemonic")) {
			if (((String) value).length() > 0)
				menuItem.setMnemonic(((String) value).charAt(0));
			else
				menuItem.setMnemonic('\0');
		} else {
			super.setProperty(name, value);
		}
	}

	public Component getWidget() {
		return menuItem;
	}

	public WidgetInfo getWidgetInfo() {
		return menuItemInfo;
	}
}
