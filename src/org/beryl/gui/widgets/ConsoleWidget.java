package org.beryl.gui.widgets;

import java.awt.Component;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;

import com.tellhow.graphicframework.mainframe.Console;

public class ConsoleWidget extends Widget {
	private Console console;
	public ConsoleWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		console = new Console();
	}

	public ConsoleWidget(Widget parent, String name, String preset)
			throws GUIException {
		super(parent, name, preset);
		console = new Console();
	}

	@Override
	public Component getWidget() {
		return console;
	}
}
