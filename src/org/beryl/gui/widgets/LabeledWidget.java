/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import javax.swing.ImageIcon;
import javax.swing.JLabel;

import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.validators.ValidationException;

import cz.autel.dmi.HIGLayout;

public class LabeledWidget extends Panel {
	protected static WidgetInfo labeledWidgetInfo = null;
	private static ImageIcon icon = null;
	private static final int[] width = { 0, LABEL_COMPONENT_SPACING_LEFT, DEFAULT_WIDTH };
	private static final int[] heights = { DEFAULT_HEIGHT };
	private Label label = null;
	private Widget dataWidget = null;

	static {
		labeledWidgetInfo = new WidgetInfo(LabeledWidget.class, widgetInfo); /* Not inheriting from panelInfo */
		labeledWidgetInfo.addProperty("label.text", "istring", "");
		labeledWidgetInfo.addProperty("mnemonic", "istring", "");
		icon = ImageIconFactory.getIcon("error_tiny");
	};


	public LabeledWidget(Widget parent, String name) throws GUIException {
		super(parent, name);

		label = new Label(this, null);

		if (!(parent instanceof Group)) {
			/* If the parent is a Group, let it handle the layout */
			HIGLayout layout = new HIGLayout(width, heights);
			layout.setColumnWeight(3, 1);
			setProperty("layout", layout);
			super.addChild(label, constraints.rc(1, 1));
		}
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (constraint != null)
			throw new GUIException("Anchors not supported inside a labeled dataWidget");
		if (dataWidget != null)
			throw new GUIException("A labeled component cannot have more than one child");
		((JLabel) label.getRealWidget()).setLabelFor(widget.getRealWidget());
		dataWidget = widget;
		
		if (!(getParentWidget() instanceof Group)) {
			super.addChild(widget, constraints.rc(1, 3));
		} else {
			addChild(widget);
		}
	}

	public void setProperty(String name, Object value) throws GUIException {
		if (name.startsWith("label.")) {
			label.setProperty(name.substring(6), value);
		} else if (name.equals("mnemonic")) {
			((JLabel) label.getRealWidget()).setDisplayedMnemonic(((String)value).charAt(0));
		} else {
			super.setProperty(name, value);
		}
	}

	public Label getLabel() {
		return label;
	}

	public Widget getDataWidget() throws GUIException {
		if (dataWidget == null)
			return new Panel(this, null);
		return dataWidget;
	}

	public void clearError() throws GUIException {
		label.setIcon(null);
		label.setTooltipText(null);
	}

	public void setError(ValidationException e) throws GUIException {
		label.setIcon(icon);
		label.setTooltipText(e.getMessage());
	}
	
	public void revalidate() throws GUIException {
		if (getParentWidget() instanceof Group)
			((Group) getParentWidget()).revalidate();
		else
			super.revalidate();
	}

	public WidgetInfo getWidgetInfo() {
		return labeledWidgetInfo;
	}
}
