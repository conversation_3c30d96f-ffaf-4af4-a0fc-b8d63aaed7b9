/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Color;
import java.awt.Component;
import java.awt.Font;
import java.io.IOException;
import java.util.Properties;
import java.util.StringTokenizer;

import javax.swing.JScrollPane;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;

import novaworx.syntax.SyntaxFactory;
import novaworx.syntax.Token;
import novaworx.textpane.SyntaxDocument;
import novaworx.textpane.SyntaxGutter;
import novaworx.textpane.SyntaxGutterBase;
import novaworx.textpane.SyntaxStyle;
import novaworx.textpane.SyntaxTextPane;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;

public class SyntaxEditor extends Widget {
	private static final Color CLEAR = new Color(0, 0, 0, 0);
	private static Properties themes = null;
	protected static WidgetInfo syntaxInfo = null;
	private SyntaxDocument document = null;
	private SyntaxTextPane textPane = null;
	private SyntaxGutter gutter = null;
	private SyntaxGutterBase gutterBase = null;
	private JScrollPane scrollPane = null;
	private String key = null;
	private boolean sendEvents = true;
	private boolean processEvents = true;

	static {
		syntaxInfo = new WidgetInfo(SyntaxEditor.class, widgetInfo);
		syntaxInfo.addProperty("key", "string", "");
		syntaxInfo.addProperty("verticalScrollBar", "bool", Boolean.FALSE);
		syntaxInfo.addProperty("horizontalScrollBar", "bool", Boolean.FALSE);
		syntaxInfo.addProperty("gutter", "bool", "true");
		syntaxInfo.addProperty("lineHighlight", "bool", "true");
		syntaxInfo.addProperty("bracketHighlight", "bool", "true");
		syntaxInfo.addProperty("antiAlias", "bool", "true");
		syntaxInfo.addProperty("theme", "string", "default");
		syntaxInfo.addProperty("mode", "string", "xml");

		SyntaxFactory.setSyntaxCatalog(SyntaxEditor.class.getResource("/resources/syntax.catalog.xml"));
		SyntaxFactory.loadSyntaxes();

		try {
			themes = new Properties();
			themes.load(SyntaxEditor.class.getResource("/resources/xmlgui/syntax.properties")
					.openStream());
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	public SyntaxEditor(Widget parent, String name) throws GUIException {
		super(parent, name);

		document = new SyntaxDocument();
		document.setSyntax(SyntaxFactory.getSyntax("xml"));

		textPane = new SyntaxTextPane();
		textPane.setDocument(document);
		textPane.setLineHighlight(true);
		textPane.setBracketHighlight(true);
		textPane.setAntiAlias(true);
		textPane.setOpaque(true);

		scrollPane = new JScrollPane(textPane);

		gutter = new SyntaxGutter(textPane);
		gutterBase = new SyntaxGutterBase(gutter);
		scrollPane.setRowHeaderView(gutter);
		scrollPane.setCorner(JScrollPane.LOWER_LEFT_CORNER, gutterBase);

		setTheme(themes, "default");
	}

	private Color parseColor(String color) throws GUIException {
		if (color == null)
			return CLEAR;
		try {
			if (color.length() == 6) {
				int r = Integer.parseInt(color.substring(0, 2), 16);
				int g = Integer.parseInt(color.substring(2, 4), 16);
				int b = Integer.parseInt(color.substring(4, 6), 16);
				return new Color(r, g, b);
			} else if (color.length() == 8) {
				int a = Integer.parseInt(color.substring(0, 2), 16);
				int r = Integer.parseInt(color.substring(2, 4), 16);
				int g = Integer.parseInt(color.substring(4, 6), 16);
				int b = Integer.parseInt(color.substring(6, 8), 16);
				return new Color(r, g, b, a);
			} else {
				throw new GUIException("Invalid color syntax");
			}
		} catch (NumberFormatException e) {
			throw new GUIException("Invalid color data", e);
		}
	}

	private int parseFont(String font) throws GUIException {
		int flags = Font.PLAIN;
		if (font != null) {

			StringTokenizer tokenizer = new StringTokenizer(font, ",");
			while (tokenizer.hasMoreTokens()) {
				String token = tokenizer.nextToken().toLowerCase();

				if (token.equals("bold"))
					flags |= Font.BOLD;
				else if (token.equals("italic"))
					flags |= Font.ITALIC;
				else if (token.equals("plain"))
					;
				else
					throw new GUIException("Unknown font attribute");
			}
		}
		return flags;
	}

	private void setTheme(Properties props, String prefix) throws GUIException {
		SyntaxStyle[] aStyles = textPane.getSyntaxStyles();

		aStyles[Token.COMMENT1].setForeground(parseColor(props.getProperty(prefix + ".comment1.fg")));
		aStyles[Token.COMMENT1].setBackground(parseColor(props.getProperty(prefix + ".comment1.bg")));
		aStyles[Token.COMMENT1].setFontStyle(parseFont(props.getProperty(prefix + ".comment1.font")));
		aStyles[Token.COMMENT2].setForeground(parseColor(props.getProperty(prefix + ".comment2.fg")));
		aStyles[Token.COMMENT2].setBackground(parseColor(props.getProperty(prefix + ".comment2.bg")));
		aStyles[Token.COMMENT2].setFontStyle(parseFont(props.getProperty(prefix + ".comment2.font")));
		aStyles[Token.COMMENT3].setForeground(parseColor(props.getProperty(prefix + ".comment3.fg")));
		aStyles[Token.COMMENT3].setBackground(parseColor(props.getProperty(prefix + ".comment3.bg")));
		aStyles[Token.COMMENT3].setFontStyle(parseFont(props.getProperty(prefix + ".comment3.font")));
		aStyles[Token.DIGIT].setForeground(parseColor(props.getProperty(prefix + ".digit.fg")));
		aStyles[Token.DIGIT].setBackground(parseColor(props.getProperty(prefix + ".digit.bg")));
		aStyles[Token.DIGIT].setFontStyle(parseFont(props.getProperty(prefix + ".digit.font")));
		aStyles[Token.LITERAL1].setForeground(parseColor(props.getProperty(prefix + ".literal1.fg")));
		aStyles[Token.LITERAL1].setBackground(parseColor(props.getProperty(prefix + ".literal1.bg")));
		aStyles[Token.LITERAL1].setFontStyle(parseFont(props.getProperty(prefix + ".literal1.font")));
		aStyles[Token.LITERAL2].setForeground(parseColor(props.getProperty(prefix + ".literal2.fg")));
		aStyles[Token.LITERAL2].setBackground(parseColor(props.getProperty(prefix + ".literal2.bg")));
		aStyles[Token.LITERAL2].setFontStyle(parseFont(props.getProperty(prefix + ".literal2.font")));
		aStyles[Token.KEYWORD1].setForeground(parseColor(props.getProperty(prefix + ".keyword1.fg")));
		aStyles[Token.KEYWORD1].setBackground(parseColor(props.getProperty(prefix + ".keyword1.bg")));
		aStyles[Token.KEYWORD1].setFontStyle(parseFont(props.getProperty(prefix + ".keyword1.font")));
		aStyles[Token.KEYWORD2].setForeground(parseColor(props.getProperty(prefix + ".keyword2.fg")));
		aStyles[Token.KEYWORD2].setBackground(parseColor(props.getProperty(prefix + ".keyword2.bg")));
		aStyles[Token.KEYWORD2].setFontStyle(parseFont(props.getProperty(prefix + ".keyword2.font")));
		aStyles[Token.KEYWORD3].setForeground(parseColor(props.getProperty(prefix + ".keyword3.fg")));
		aStyles[Token.KEYWORD3].setBackground(parseColor(props.getProperty(prefix + ".keyword3.bg")));
		aStyles[Token.KEYWORD3].setFontStyle(parseFont(props.getProperty(prefix + ".keyword3.font")));
		aStyles[Token.KEYWORD4].setForeground(parseColor(props.getProperty(prefix + ".keyword4.fg")));
		aStyles[Token.KEYWORD4].setBackground(parseColor(props.getProperty(prefix + ".keyword4.bg")));
		aStyles[Token.KEYWORD4].setFontStyle(parseFont(props.getProperty(prefix + ".keyword4.font")));
		aStyles[Token.KEYWORD5].setForeground(parseColor(props.getProperty(prefix + ".keyword5.fg")));
		aStyles[Token.KEYWORD5].setBackground(parseColor(props.getProperty(prefix + ".keyword5.bg")));
		aStyles[Token.KEYWORD5].setFontStyle(parseFont(props.getProperty(prefix + ".keyword5.font")));
		aStyles[Token.FUNCTION].setForeground(parseColor(props.getProperty(prefix + ".function.fg")));
		aStyles[Token.FUNCTION].setBackground(parseColor(props.getProperty(prefix + ".function.bg")));
		aStyles[Token.FUNCTION].setFontStyle(parseFont(props.getProperty(prefix + ".function.font")));
		aStyles[Token.OPERATOR].setForeground(parseColor(props.getProperty(prefix + ".operator.fg")));
		aStyles[Token.OPERATOR].setBackground(parseColor(props.getProperty(prefix + ".operator.bg")));
		aStyles[Token.OPERATOR].setFontStyle(parseFont(props.getProperty(prefix + ".operator.font")));
		aStyles[Token.MARKUP].setForeground(parseColor(props.getProperty(prefix + ".markup.fg")));
		aStyles[Token.MARKUP].setBackground(parseColor(props.getProperty(prefix + ".markup.bg")));
		aStyles[Token.MARKUP].setFontStyle(parseFont(props.getProperty(prefix + ".markup.font")));
		aStyles[Token.LABEL].setForeground(parseColor(props.getProperty(prefix + ".label.fg")));
		aStyles[Token.LABEL].setBackground(parseColor(props.getProperty(prefix + ".label.bg")));
		aStyles[Token.LABEL].setFontStyle(parseFont(props.getProperty(prefix + ".label.font")));
		aStyles[Token.INVALID].setForeground(parseColor(props.getProperty(prefix + ".invalid.fg")));
		aStyles[Token.INVALID].setBackground(parseColor(props.getProperty(prefix + ".invalid.bg")));
		aStyles[Token.INVALID].setFontStyle(parseFont(props.getProperty(prefix + ".invalid.font")));
		aStyles[Token.NULL].setFontStyle(parseFont(props.getProperty(prefix + ".null.font")));
		textPane.setForeground(parseColor(props.getProperty(prefix + ".text.fg")));
		textPane.setBackground(parseColor(props.getProperty(prefix + ".text.bg")));
		textPane.setCaretColor(parseColor(props.getProperty(prefix + ".caret.fg")));
		textPane.setBracketHighlightColor(parseColor(props.getProperty(prefix
				+ ".brackethighlight.fg")));
		textPane.setSelectionColor(parseColor(props.getProperty(prefix + ".selectioncolor.bg")));
		
		textPane.setLineHighlightColor(parseColor(props.getProperty(prefix + ".linehighlight.fg")));
		gutter.setForeground(parseColor(props.getProperty(prefix + ".gutter.fg")));
		gutter.setBackground(parseColor(props.getProperty(prefix + ".gutter.bg")));
		gutter.setDividerForeground(parseColor(props.getProperty(prefix + ".divider.fg")));
		gutter.setDividerBackground(parseColor(props.getProperty(prefix + ".divider.bg")));
		gutter.setCurrentLineForeground(parseColor(props.getProperty(prefix + ".currentline.fg")));
		gutter.setCurrentLineBackground(parseColor(props.getProperty(prefix + ".currentline.bg")));
		gutter.setLineIntervalForeground(parseColor(props.getProperty(prefix + ".lineinterval.fg")));
		gutter.setLineIntervalBackground(parseColor(props.getProperty(prefix + ".lineinterval.bg")));
		gutter.setSelectionForeground(parseColor(props.getProperty(prefix + ".gutterselection.fg")));
		gutter.setSelectionBackground(parseColor(props.getProperty(prefix + ".gutterselection.bg")));
		gutter.setBracketScopeForeground(parseColor(props.getProperty(prefix + ".bracketscope.fg")));
		gutter.setBracketScopeBackground(parseColor(props.getProperty(prefix + ".bracketscope.bg")));
	}

	private void reload() throws GUIException {
		MapDataModel model = getDataModel();
		if (model != null && key != null) {
			try {
				processEvents = false;
				String value = (String) model.getValue(key);
				if (value != null) {
					textPane.setText(value);
				} else {
					model.setValue(SyntaxEditor.this, key, textPane.getText());
				}
			} finally {
				processEvents = true;
			}
		}
	}
	
	
	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (processEvents) {
			sendEvents = false;
			try {
				if (e.getSource() == this) {
					/* New data model */
					reload();
				} else if (e instanceof MapChangeEvent) {
					MapChangeEvent event = (MapChangeEvent) e;
					if (event.getKey() == null) {
						reload();
					} else if (event.getKey().equals(key)) {
						textPane.setText((String) event.getNewValue());
					}
				}
			} finally {
				sendEvents = true;
			}
		}
	}
	
	
	public void finalizeConstruction() {
		textPane.getDocument().addDocumentListener(new DocumentListener() {
			public void insertUpdate(DocumentEvent e) {
				changedUpdate(e);
			}

			public void removeUpdate(DocumentEvent e) {
				changedUpdate(e);
			}

			public void changedUpdate(DocumentEvent e) {
				if (sendEvents) {
					try {
						MapDataModel model = getDataModel();
						if (model != null && key != null) {
							try {
								processEvents = false;
								model.setValue(SyntaxEditor.this, key,  textPane.getText());
							} finally {
								processEvents = true;
							}
						}
					} catch (GUIException ex) {
						throw new RuntimeException(ex);
					}
				}
			}
		});
	}
	
	public void setProperty(String name, Object value) throws GUIException {
		if ("mode".equals(name)) {
			document.setSyntax(SyntaxFactory.getSyntax((String) value));
		} else if ("key".equals(name)) {
			key = (String) value;
		} else if ("theme".equals(name)) {
			setTheme(themes, (String) value);
		} else if ("gutter".equals(name)) {
			if (((Boolean) value).booleanValue()) {
				scrollPane.setRowHeaderView(gutter);
				scrollPane.setCorner(JScrollPane.LOWER_LEFT_CORNER, gutterBase);
			} else {
				scrollPane.setRowHeader(null);
				scrollPane.setCorner(JScrollPane.LOWER_LEFT_CORNER, null);
			}
		} else if ("verticalScrollBar".equals(name)) {
			scrollPane.setVerticalScrollBarPolicy(((Boolean) value).booleanValue() ? JScrollPane.VERTICAL_SCROLLBAR_ALWAYS
					: JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
		} else if ("horizontalScrollBar".equals(name)) {
			scrollPane.setHorizontalScrollBarPolicy(((Boolean) value).booleanValue() ? JScrollPane.HORIZONTAL_SCROLLBAR_ALWAYS
					: JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		} else {
			super.setProperty(name, value);
		}
	}

	public String getText() {
		return textPane.getText();
	}

	public void setText(String text) {
		textPane.setText(text);
	}

	public Component getRealWidget() {
		return textPane;
	}

	public Component getWidget() {
		return scrollPane;
	}

	public WidgetInfo getWidgetInfo() {
		return syntaxInfo;
	}
}