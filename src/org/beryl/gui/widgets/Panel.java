/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.LayoutManager;

import javax.swing.BorderFactory;
import javax.swing.ImageIcon;
import javax.swing.JPanel;

import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.component.ImagePanel;

import cz.autel.dmi.HIGLayout;

public class Panel extends Widget {
	protected static WidgetInfo panelInfo = null;
	protected ImagePanel panel = null;

	static {
		panelInfo = new WidgetInfo(Panel.class, widgetInfo);
		panelInfo.addProperty("border", "border", BorderFactory.createEmptyBorder());
		panelInfo.addProperty("layout", "layout", new BorderLayout());
	};
	
	public Panel(Widget parent, String name) throws GUIException{
		super(parent, name);
		panel = new ImagePanel(null, new BorderLayout());
	}
	
	public Panel(Widget parent, String name, String path) throws GUIException{
		super(parent, name);
		panel = new ImagePanel(ImageIconFactory.getIcon(path), new BorderLayout());
	}
	
	public void addChild(Widget widget, Object constraint) throws GUIException {
		Component component = widget.getWidget();
		if (constraint == null)
			constraint = BorderLayout.CENTER;
		panel.add(component, constraint);
		addChild(widget);
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("spacing".equals(name)) {
			int border = ((Integer) value).intValue();
			panel.setBorder(BorderFactory.createEmptyBorder(border, border, border, border));
		}
		else if (name.equals("backgroundIcon")) {
			if (value != null && !value.equals("")) {
				panel = new ImagePanel((ImageIcon) value);
			}
		}
		else if (name.equals("background")) {
			if (value != null && !value.equals("")) {
				panel.setBackground(new Color(Integer.parseInt(value.toString().replace("#", ""), 16)));
			}
		}
		else {
			super.setProperty(name, value);
		}	
	}

	public void recreateLayout() throws GUIException {
		LayoutManager manager = panel.getLayout();
		if (manager instanceof HIGLayout && !(this instanceof Group)) {
			/* Recreate the hig layout. Why is this needed? */
			HIGLayout layout = (HIGLayout) manager;
			HIGLayout newLayout = new HIGLayout(layout.getWidths(), layout.getHeights());
			for (int i=1; i<layout.getColumnWeights().length; i++)
				newLayout.setColumnWeight(i, layout.getColumnWeights()[i]);
			for (int i=1; i<layout.getRowWeights().length; i++)
				newLayout.setRowWeight(i, layout.getRowWeights()[i]);
			panel.setLayout(newLayout);
		}
		revalidate();
	}


	public void removeChildWidget(Widget widget) throws GUIException {
		panel.remove(widget.getWidget());
		super.removeChildWidget(widget);
	}

	public Component getWidget() {
		return panel;
	}

	public WidgetInfo getWidgetInfo() {
		return panelInfo;
	}
}
