/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.JPasswordField;
import javax.swing.JTextField;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.validators.ValidationException;

public class TextField extends Widget {
	protected static WidgetInfo textFieldInfo = null;
	public static final Color DEFAULT_COLOR = new Color(255, 255, 255);
	public static final Color ERROR_COLOR = new Color(255, 200, 200);

	protected JTextField textField = null;
	private String key = null;
	private boolean sendEvents = true;
	private boolean processEvents = true;

	static {
		textFieldInfo = new WidgetInfo(TextField.class, widgetInfo);
		textFieldInfo.addProperty("key", "string", "");
		textFieldInfo.addEvent("activated");
	};

	public TextField(Widget parent, String name) throws GUIException {
		super(parent, name);
		textField = new JTextField();
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("key".equals(name))
			key = (String) value;
		else
			super.setProperty(name, value);
	}

	private void reload() throws GUIException {
		MapDataModel model = getDataModel();
		if (model != null && key != null) {
			try {
				processEvents = false;
				String value = (String) model.getValue(key);
				if (value != null) {
					textField.setText(value);
				} else {
					String text = null;
					if (textField instanceof JPasswordField)
						text = new String(((JPasswordField) textField).getPassword());
					else
						text = textField.getText();
					model.setValue(TextField.this, key, text);
				}
			} finally {
				processEvents = true;
			}
		}
	}

	public String getText() {
		return textField.getText();
	}

	public void validate() throws ValidationException, GUIException {
		if (hasValidators()) {
			try {
				super.validate();
				if (textField.getBackground() != DEFAULT_COLOR) {
					textField.setBackground(DEFAULT_COLOR);
				}
				if (getParentWidget() instanceof LabeledWidget) {
					((LabeledWidget) getParentWidget()).clearError();
				}
			} catch (ValidationException e) {
				textField.setBackground(ERROR_COLOR);
				if (getParentWidget() instanceof LabeledWidget) {
					((LabeledWidget) getParentWidget()).setError(e);
				}
				throw e;
			}
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (processEvents) {
			sendEvents = false;
			try {
				if (e.getSource() == this) {
					/* New data model */
					reload();
					try {
						validate();
					} catch (ValidationException ex) {
						/* Ignore, error status is displayed already */
					}
				} else if (e instanceof MapChangeEvent) {
					MapChangeEvent event = (MapChangeEvent) e;
					if (event.getKey() == null) {
						reload();
					} else if (event.getKey().equals(key)) {
						textField.setText((String) event.getNewValue());
					}
					try {
						validate();
					} catch (ValidationException ex) {
						/* Ignore, error status is displayed already */
					}
				}
			} finally {
				sendEvents = true;
			}
		}
	}

	public void finalizeConstruction() {
		textField.setPreferredSize(new Dimension(MAX_WIDTH, DEFAULT_HEIGHT));
		textField.getDocument().addDocumentListener(new DocumentListener() {
			public void insertUpdate(DocumentEvent e) {
				changedUpdate(e);
			}

			public void removeUpdate(DocumentEvent e) {
				changedUpdate(e);
			}

			public void changedUpdate(DocumentEvent e) {
				if (sendEvents) {
					try {
						MapDataModel model = getDataModel();
						if (model != null && key != null) {
							String text = null;
							if (textField instanceof JPasswordField)
								text = new String(((JPasswordField) textField).getPassword());
							else
								text = textField.getText();
							try {
								processEvents = false;
								model.setValue(TextField.this, key, text);
							} finally {
								processEvents = true;
							}
							try {
								validate();
							} catch (ValidationException ex) {
								/* Ignore, error status is displayed already */
							}
						}
					} catch (GUIException ex) {
						throw new RuntimeException(ex);
					}
				}
			}
		});
	}

	public void addListener(String event, final String name, final GUIEventListener listener) throws GUIException {
		if ("activated".equals(event)) {
			textField.addActionListener(new ActionListener() {
				public void actionPerformed(ActionEvent e) {
					listener.eventOccured(new GUIEvent(TextField.this, name, e));
				}
			});
		} else {
			super.addListener(event, name, listener);
		}
	}

	public Component getWidget() {
		return textField;
	}

	public WidgetInfo getWidgetInfo() {
		return textFieldInfo;
	}
}
