/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.Component;

import javax.swing.ImageIcon;

import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.swing.IconElement;

/**
 * Dummy widget which can be used to fill the List, Combo
 * and IconView widgets
 */

public class Item extends Widget implements IconElement {
	protected static WidgetInfo itemInfo = null;
	private String text = null;
	private ImageIcon icon = null;
	private ListDataModel owningDataModel = null;
	private Object object = null;
	
	static {
		itemInfo = new WidgetInfo(Item.class);
		itemInfo.addProperty("text", "istring", "");
		itemInfo.setSupportsAnchor(false);
		try {
			itemInfo.addProperty("icon", "icon", ImageIconFactory.getIcon("broken"));
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	};
	
	public Item(Widget parent, String name) throws GUIException {
		super(parent, name);
		text="";
	}
	
	public void setProperty(String name, Object value) throws GUIException {
		if ("icon".equals(name)) {
			setIcon((ImageIcon) value);
		} else if ("text".equals(name)) {
			setText((String) value);
		} else {
			throw new GUIException("Item only supports name and icon properties");
		}
	}
	
	public void setOwningDataModel(ListDataModel owningDataModel) {
		this.owningDataModel = owningDataModel;
	}
	
	public void setText(String text) {
		this.text = text;
	}
	
	public String getText() {
		return text;
	}

	public void setIcon(ImageIcon icon) {
		this.icon = icon;
	}

	public ImageIcon getIcon() {
		return icon;
	}

	public String toString() {
		return text;
	}

	public void setUserObject(Object object) {
		this.object = object;
	}

	public Object getUserObject() {
		return object;
	}

	public void revalidate() throws GUIException {
		if (owningDataModel != null) {
			int index = owningDataModel.indexOf(this);
			if (index != -1) {
				owningDataModel.setValue(this, index, this);
			}
		}
	}

	public Component getWidget() {
		return null;
	}
	
	public WidgetInfo getWidgetInfo() {
		return itemInfo;
	}
}
