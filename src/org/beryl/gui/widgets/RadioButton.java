/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.Component;

import javax.swing.JRadioButton;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;

public class RadioButton extends Widget {
	private static WidgetInfo radioButtonInfo = null;
	private JRadioButton radioButton = null;
	private Object value = "";
	
	static {
		radioButtonInfo = new WidgetInfo(RadioButton.class, widgetInfo);
		radioButtonInfo.addProperty("text", "istring", "");
		radioButtonInfo.addProperty("value", "string", "");
	};
	
	public RadioButton(Widget parent, String name) throws GUIException {
		super(parent, name);
		radioButton = new JRadioButton();
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("value".equals(name)) {
			this.value = value;
		} else {
			super.setProperty(name, value);
		}
	}
	
	public Object getValue() {
		return value;
	}
	
	public Component getWidget() {
		return radioButton;
	}

	public WidgetInfo getWidgetInfo() {
		return radioButtonInfo;
	}
}
