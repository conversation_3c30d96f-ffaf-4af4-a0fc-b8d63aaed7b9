/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Color;
import java.awt.Component;

import javax.swing.JScrollPane;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.swing.JIconView;

public class IconView extends Widget {
	protected static WidgetInfo iconViewInfo = null;
	private JIconView iconView = null;
	private JScrollPane scrollPane = null;
	private ListDataModel listDataModel = null;
	private String indexKey = null, valueKey = null;
	private boolean sendEvents = true;
	private boolean processEvents = true;

	static {
		iconViewInfo = new WidgetInfo(IconView.class, widgetInfo);
		iconViewInfo.addProperty("indexkey", "");
		iconViewInfo.addProperty("valuekey", "");
	};

	public IconView(Widget parent, String name) throws GUIException {
		super(parent, name);
		JIconView.initialize();
		iconView = new JIconView();
		iconView.setBackground(Color.white);
		scrollPane = new JScrollPane(iconView);
		iconView.addListSelectionListener(new ListSelectionListener() {
			public void valueChanged(ListSelectionEvent e) {
				int index = e.getFirstIndex();
				MapDataModel model = getDataModel();
				try {
					if (!e.getValueIsAdjusting() && sendEvents) {
						try {
							sendEvents = false;
							processEvents = false;

							if (indexKey != null)
								model.setValue(indexKey, new Integer(index));
							if (valueKey != null)
								model.setValue(valueKey, index != -1 ? listDataModel.getValue(index) : null);
						} finally {
							sendEvents = true;
							processEvents = true;
						}
					}
				} catch (GUIException ex) {
					throw new RuntimeException(ex);
				}
			}
		});
	}

	public void addListener(String event, final String name, final GUIEventListener listener) throws GUIException {
		if ("selected".equals(event)) {
			iconView.addListSelectionListener(new ListSelectionListener() {
				public void valueChanged(ListSelectionEvent e) {
					int index = e.getFirstIndex();
					listener.eventOccured(new GUIEvent(IconView.this, name, e));
				}
			});
		} else {
			super.addListener(event, name, listener);
		}
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("indexkey".equals(name)) {
			indexKey = (String) value;
		} else if ("valuekey".equals(name)) {
			valueKey = (String) value;
		} else {
			super.setProperty(name, value);
		}
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof Item) {
			try {
				if (listDataModel == null) {
					listDataModel = new ListDataModel();
					sendEvents = false;
					iconView.setModel(new List.ListDataModelAdapter(listDataModel));
				}
				listDataModel.addValue(this, widget);
				addChild(widget);
			} finally {
				sendEvents = true;
			}
		} else {
			throw new GUIException("Only Item children are allowed inside an IconView");
		}
	}

	public void removeChildWidget(Widget widget) throws GUIException {
		if (listDataModel != null) {
			listDataModel.removeValue(this, widget);
			super.removeChildWidget(widget);
		} else {
			throw new GUIException("There are no static items to remove");
		}
	}

	public void setListDataModel(ListDataModel listDataModel) throws GUIException {
		ModelChangeEvent event = new ModelChangeEvent(this, listDataModel);
		this.listDataModel = listDataModel;
		sendEvents = false;
		try {
			iconView.setModel(new List.ListDataModelAdapter(listDataModel));
		} finally {
			sendEvents = true;
		}
		/* Reload data model information */
		modelChanged(event);
	}

	private void setSelectionValue(Object value) {
		if (value == null)
			iconView.setSelectedIndex(-1);
		else
			iconView.setSelectedIndex(listDataModel.indexOf(value));
	}

	private void setSelectionIndex(Integer index) {
		if (index == null)
			iconView.setSelectedIndex(-1);
		else
			iconView.setSelectedIndex(index.intValue());
	}

	private void reload() throws GUIException {
		MapDataModel model = getDataModel();
		if (model != null) {
			try {
				processEvents = false;

				Integer index = indexKey == null ? null : (Integer) model.getValue(indexKey);
				Object value = valueKey == null ? null : model.getValue(valueKey);

				if (index != null) {
					setSelectionIndex(index);
				} else if (value != null) {
					setSelectionValue(value);
				}

				if (((value != null && index == null) || (value == null && index == null)) && indexKey != null) {
					model.setValue(IconView.this, indexKey, new Integer(iconView.getSelectedIndex()));
				}

				if (((index != null && value == null) || (value == null && index == null)) && valueKey != null) {
					value = index.intValue() != -1 ? listDataModel.getValue(index.intValue()) : null;
					model.setValue(
						IconView.this,
						valueKey,
						value);
				}
			} finally {
				processEvents = true;
			}
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (processEvents) {
			try {
				sendEvents = false;
				if (e.getSource() == this) {
					try {
						reload();
					} catch (IllegalArgumentException ex) {
						/* Ignore, list data model is not yet set */
					} catch (ArrayIndexOutOfBoundsException ex) {
						/* Ignore, list data model is not yet set */
					}
				} else if (e instanceof MapChangeEvent) {
					MapChangeEvent event = (MapChangeEvent) e;
					if (event.getKey() == null) {
						reload();
					} else if (event.getKey().equals(indexKey)) {
						setSelectionIndex((Integer) event.getNewValue());
						try {
							processEvents = false;
							int index = iconView.getSelectedIndex();
							if (valueKey != null)
								((MapDataModel) event.getModel()).setValue(
									IconView.this,
									valueKey,
									index != -1 ? listDataModel.getValue(index) : null);
						} finally {
							processEvents = true;
						}
					} else if (event.getKey().equals(valueKey)) {
						setSelectionValue(event.getNewValue());
						try {
							processEvents = false;
							if (indexKey != null)
								((MapDataModel) event.getModel()).setValue(
									IconView.this,
									indexKey,
									new Integer(iconView.getSelectedIndex()));
						} finally {
							processEvents = true;
						}
					}
				}
			} finally {
				sendEvents = true;
			}
		}
	}

	public Component getWidget() {
		return scrollPane;
	}

	public Component getRealWidget() {
		return iconView;
	}

	public WidgetInfo getWidgetInfo() {
		return iconViewInfo;
	}
}
