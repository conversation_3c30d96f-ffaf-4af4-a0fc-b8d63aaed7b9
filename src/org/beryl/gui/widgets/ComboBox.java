/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Component;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.ComboBoxModel;
import javax.swing.JComboBox;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;

public class ComboBox extends Widget {
	protected static WidgetInfo comboBoxInfo = null;
	private JComboBox comboBox = null;
	private ListDataModel listDataModel = null;
	private String indexKey = null, valueKey = null;
	private boolean sendEvents = true;
	private boolean processEvents = true;

	static {
		comboBoxInfo = new WidgetInfo(ComboBox.class, widgetInfo);
		comboBoxInfo.addProperty("valuekey", "string", "");
		comboBoxInfo.addProperty("indexkey", "string", "");
	};

	public static class ComboBoxDataModelAdapter extends List.ListDataModelAdapter implements ComboBoxModel {
		private Object selectedItem = null;

		public ComboBoxDataModelAdapter(ListDataModel model) {
			super(model);
		}

		public void setSelectedItem(Object anItem) {
			selectedItem = anItem;
		}

		public Object getSelectedItem() {
			return selectedItem;
		}
	};

	public ComboBox(Widget parent, String name) throws GUIException {
		super(parent, name);
		comboBox = new JComboBox();
		comboBox.setPreferredSize(new Dimension(MAX_WIDTH, DEFAULT_HEIGHT));
		comboBox.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				try {
					MapDataModel model = getDataModel();
					if (sendEvents && model != null) {
						try {
							sendEvents = false;
							processEvents = false;
							if (indexKey != null)
								model.setValue(ComboBox.this, indexKey, new Integer(comboBox.getSelectedIndex()));
							if (valueKey != null)
								model.setValue(ComboBox.this, valueKey, comboBox.getSelectedItem());
						} finally {
							sendEvents = true;
							processEvents = true;
						}
					}
				} catch (GUIException ex) {
					throw new RuntimeException(ex);
				}
			}
		});
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof Item) {
			try {
				if (listDataModel == null) {
					listDataModel = new ListDataModel();
					sendEvents = false;
					comboBox.setModel(new ComboBoxDataModelAdapter(listDataModel));
				}
				listDataModel.addValue(this, widget);
				addChild(widget);
			} finally {
				sendEvents = true;
			}
		} else {
			throw new GUIException("Only Item children are allowed inside a ComboBox");
		}
	}

	public void removeChildWidget(Widget widget) throws GUIException {
		if (listDataModel != null) {
			listDataModel.removeValue(this, widget);
			super.removeChildWidget(widget);
		} else {
			throw new GUIException("There are no static items to remove");
		}
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("indexkey".equals(name)) {
			indexKey = (String) value;
		} else if ("valuekey".equals(name)) {
			valueKey = (String) value;
		} else {
			super.setProperty(name, value);
		}
	}

	public void setListDataModel(ListDataModel listDataModel) throws GUIException {
		ModelChangeEvent event = new ModelChangeEvent(this, listDataModel);
		this.listDataModel = listDataModel;
		sendEvents = false;
		try {
			comboBox.setModel(new ComboBoxDataModelAdapter(listDataModel));
		} finally {
			sendEvents = true;
		}
		/* Reload data model information */
		modelChanged(event);
	}

	public ListDataModel getListDataModel() {
		return this.listDataModel;
	}

	private void reload() throws GUIException {
		MapDataModel model = getDataModel();
		if (model != null) {
			try {
				processEvents = false;

				Integer index = indexKey == null ? null : (Integer) model.getValue(indexKey);
				Object value = valueKey == null ? null : model.getValue(valueKey);

				if (index != null) {
					comboBox.setSelectedIndex(index.intValue());
					comboBox.repaint();
				} else if (value != null) {
					comboBox.setSelectedItem(value);
					comboBox.repaint();
				}

				if (((value != null && index == null) || (value == null && index == null)) && indexKey != null) {
					model.setValue(ComboBox.this, indexKey, new Integer(comboBox.getSelectedIndex()));
				}

				if (((index != null && value == null) || (value == null && index == null)) && valueKey != null) {
					model.setValue(ComboBox.this, valueKey, comboBox.getSelectedItem());
				}
			} finally {
				processEvents = true;
			}
		} else {
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (processEvents) {
			try {
				sendEvents = false;
				if (e.getSource() == this) {
					try {
						reload();
					} catch (IllegalArgumentException ex) {
						/* Ignore, list data model is not yet set */
					} catch (ArrayIndexOutOfBoundsException ex) {
						/* Ignore, list data model is not yet set */
					}
				} else if (e instanceof MapChangeEvent) {
					MapChangeEvent event = (MapChangeEvent) e;
					if (event.getKey() == null) {
						reload();
					} else if (event.getKey().equals(indexKey)) {
						try {
							comboBox.setSelectedIndex(((Integer) event.getNewValue()).intValue());
							comboBox.repaint();
							try {
								processEvents = false;
								if (valueKey != null)
									((MapDataModel) event.getModel()).setValue(
										ComboBox.this,
										valueKey,
										comboBox.getSelectedItem());
							} finally {
								processEvents = true;
							}
						} catch (IllegalArgumentException ex) {
							throw new GUIException("Changed data model has a bad selection index", ex);
						}
					} else if (event.getKey().equals(valueKey)) {
						try {
							comboBox.setSelectedItem(event.getNewValue());
							comboBox.repaint();
							try {
								processEvents = false;
								if (indexKey != null)
									((MapDataModel) event.getModel()).setValue(
										ComboBox.this,
										indexKey,
										new Integer(comboBox.getSelectedIndex()));
							} finally {
								processEvents = true;
							}
						} catch (IllegalArgumentException ex) {
							throw new GUIException("Changed data model has a bad selection value", ex);
						}
					}
				}
			} finally {
				sendEvents = true;
			}
		}
	}

	public JComboBox getComboBox() {
		return comboBox;
	}

	public Component getWidget() {
		return comboBox;
	}

	public WidgetInfo getWidgetInfo() {
		return comboBoxInfo;
	}
}
