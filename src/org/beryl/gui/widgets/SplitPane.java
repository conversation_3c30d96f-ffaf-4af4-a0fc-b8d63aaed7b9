/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.Color;
import java.awt.Component;

import javax.swing.ImageIcon;
import javax.swing.JSplitPane;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.component.ImageMenuBar;
import org.beryl.gui.component.ImageSplitPane;
import org.beryl.gui.component.SearchSplitPaneUI;

public class SplitPane extends Widget {
	private static WidgetInfo splitPaneInfo = null;
	private ImageSplitPane splitPane = null;
	private Widget first = null;
	private Widget second = null;
	
	static {
		splitPaneInfo = new WidgetInfo(SplitPane.class, widgetInfo);
		splitPaneInfo.addProperty("orientation", "string", "h");
		splitPaneInfo.addProperty("resizeWeight", "double", new Double(0.5));
	};

	public SplitPane(Widget parent, String name) throws GUIException {
		super(parent, name);
		splitPane = new ImageSplitPane(null);
		splitPane.setOneTouchExpandable(true);
		splitPane.setContinuousLayout(true);
		splitPane.setBackground(Color.white);
		splitPane.setDividerSize(8);
	}
	
	public void setProperty(String name, Object value) throws GUIException {
		if ("orientation".equals(name)) {
			String orientation = (String) value;
			
			if (orientation.equals("h")) {
				splitPane.setOrientation(JSplitPane.HORIZONTAL_SPLIT);
			} else if (orientation.equals("v")) {
				splitPane.setOrientation(JSplitPane.VERTICAL_SPLIT);
			} else {
				throw new GUIException("Invalid split pane orientation");
			}
		} else if ("backgroundIcon".equals(name)) {
			if (value != null && !value.equals("")) {
				splitPane = new ImageSplitPane((ImageIcon) value);
			}
		}else if ("background".equals(name)) {
			if (value != null && !value.equals("")) {
				splitPane.setUI(new SearchSplitPaneUI(new Color(Integer.parseInt(value.toString().replace("#", ""), 16))));
			}
		}else {
			super.setProperty(name, value);
		}
	}
	
	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (first == null) {
			if (splitPane.getOrientation() == JSplitPane.HORIZONTAL_SPLIT)
				splitPane.setLeftComponent(widget.getWidget());
			else
				splitPane.setTopComponent(widget.getWidget());
			first = widget;
			addChild(widget);
		} else if (second == null) {
			if (splitPane.getOrientation() == JSplitPane.HORIZONTAL_SPLIT)
				splitPane.setRightComponent(widget.getWidget());
			else
				splitPane.setBottomComponent(widget.getWidget());
			second = widget;
			addChild(widget);
		} else {
			throw new GUIException("A SplitPane cannot have more than two children");
		}
	}

	public void removeChildWidget(Widget widget) throws GUIException {
		if (widget == first)
			first = null;
		else if (widget == second)
			second = null;
		super.removeChildWidget(widget);
	}


	public Component getWidget() {
		return splitPane;
	}
	
	public WidgetInfo getWidgetInfo() {
		return splitPaneInfo;
	}
}
