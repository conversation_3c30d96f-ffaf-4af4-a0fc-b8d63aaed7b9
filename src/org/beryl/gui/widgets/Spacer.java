/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Component;
import java.awt.Dimension;

import javax.swing.JComponent;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;

/**
 * Spacer for BoxLayouts and other uses (MenuBar etc)
 */
public class Spacer extends Widget {
	protected static WidgetInfo spacerInfo = null;
	private static final String TYPE_GLUE = "glue";
	private static final String TYPE_STRUT = "strut";
	private static final String AXIS_HORIZONTAL = "h";
	private static final String AXIS_VERTICAL = "v";

	private Dimension minimumSize = null;
	private Dimension preferredSize = null;
	private Dimension maximumSize = null;
	private JSpacer spacer = null;
	private boolean isConstructed = false;

	private String type = TYPE_GLUE;
	private String axis = AXIS_VERTICAL;
	private int size = 10;

	static {
		spacerInfo = new WidgetInfo(Spacer.class, widgetInfo);
		spacerInfo.addProperty("type", "string", "strut");
		spacerInfo.addProperty("axis", "string", "h");
		spacerInfo.addProperty("size", "int", new Integer(10));
	};

	private class JSpacer extends JComponent {
		public Dimension getMinimumSize() {
			return minimumSize;
		}

		public Dimension getPreferredSize() {
			return preferredSize;
		}

		public Dimension getMaximumSize() {
			return maximumSize;
		}
	};

	public Spacer(Widget parent, String name) throws GUIException {
		super(parent, name);
		
		spacer = new JSpacer();
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("type".equals(name)) {
			type = (String) value;
		} else if ("axis".equals(name)) {
			axis = (String) value;
		} else if ("size".equals(name)) {
			size = ((Integer) value).intValue();
		}
		if (isConstructed)
			finalizeConstruction();
	}

	public void finalizeConstruction() throws GUIException {
		if (type.equals(TYPE_GLUE)) {
			if (axis.equals(AXIS_HORIZONTAL)) {
				minimumSize = new Dimension(0, 0);
				preferredSize = new Dimension(0, 0); 
				maximumSize = new Dimension(Short.MAX_VALUE, 0);
			} else if (axis.equals(AXIS_VERTICAL)) {
				minimumSize = new Dimension(0 ,0);
				preferredSize = new Dimension(0, 0); 
				maximumSize = new Dimension(0, Short.MAX_VALUE);
			} else {
				throw new GUIException("Unknown spacer axis [" + axis + "]");
			}
		} else if (type.equals(TYPE_STRUT)) {
			if (axis.equals(AXIS_HORIZONTAL)) {
				minimumSize = new Dimension(size ,0);
				preferredSize = new Dimension(size, 0); 
				maximumSize = new Dimension(size, Short.MAX_VALUE);
			} else if (axis.equals(AXIS_VERTICAL)) {
				minimumSize = new Dimension(0, size);
				preferredSize = new Dimension(0, size); 
				maximumSize = new Dimension(Short.MAX_VALUE, size);
			} else {
				throw new GUIException("Unknown spacer axis [" + axis + "]");
			}
		} else {
			throw new GUIException("Unknown spacer type [" + type + "]");
		}
		if (isConstructed)
			spacer.invalidate();
		isConstructed = true;
	}

	public Component getWidget() {
		return spacer;
	}

	public WidgetInfo getWidgetInfo() {
		return spacerInfo;
	}
}