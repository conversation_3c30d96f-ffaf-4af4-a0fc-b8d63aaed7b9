/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.Component;

import javax.swing.ImageIcon;
import javax.swing.JMenu;

import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.component.ImageMenuBar;

public class MenuBar extends Widget {
	protected static WidgetInfo menuBarInfo = null;
	private ImageMenuBar menuBar = null;

	static {
		menuBarInfo = new WidgetInfo(MenuBar.class);
		menuBarInfo.setSupportsAnchor(false);
		
		try {
			menuBarInfo.addProperty("backgroundIcon", "icon", ImageIconFactory.getIcon("broken"));
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	};

	public MenuBar(Widget parent, String name) throws GUIException {
		super(parent, name);
		menuBar = new ImageMenuBar(null);
	}
	
	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof Menu) {
			menuBar.add((JMenu)widget.getWidget());
			addChild(widget);
		} else if (widget instanceof Spacer) {
			menuBar.add(widget.getWidget());
			addChild(widget);
		} else {
			throw new GUIException("Only Menu and Spacer children allowed");
		}
	}

	public void removeChildWidget(Widget widget) throws GUIException {
		if (widget instanceof Menu)
			menuBar.remove((JMenu) widget.getWidget());
		else if (widget instanceof Spacer)
			menuBar.remove(widget.getWidget());
		super.removeChildWidget(widget);
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("backgroundIcon".equals(name)) {
			if (value != null && !value.equals("")) {
				menuBar = new ImageMenuBar((ImageIcon) value);
			}
		} else {
			super.setProperty(name, value);
		}
	}

	public Component getWidget() {
		return menuBar;
	}
	
	public WidgetInfo getWidgetInfo() {
		return menuBarInfo;
	}
}