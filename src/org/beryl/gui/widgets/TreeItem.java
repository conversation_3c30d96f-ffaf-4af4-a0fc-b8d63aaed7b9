/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.util.Enumeration;
import java.util.Iterator;

import javax.swing.tree.MutableTreeNode;
import javax.swing.tree.TreeNode;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;

public class TreeItem extends Item implements MutableTreeNode {
	protected static WidgetInfo treeItemInfo = null;
	private Tree tree = null;
	private String name = null;
	protected boolean checkLeaf = true;

	static {
		treeItemInfo = new WidgetInfo(Widget.class, itemInfo);
		treeItemInfo.setSupportsAnchor(false);
	};

	public TreeItem(Widget parent, String name) throws GUIException {
		super(parent, name);
		if (parent != null) {
			if (parent instanceof Tree)
				tree = (Tree) parent;
			else if (parent instanceof TreeItem)
				tree = ((TreeItem) parent).getTree();
		}
		this.name = name;
	}

	/**
	 * Check whether this TreeItem is a leaf tree node?
	 * If set to false, the tree item will always be rendered
	 * with a non-leaf icon
	 */
	public void setCheckLeaf(boolean checkLeaf) {
		this.checkLeaf = checkLeaf;
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof TreeItem) {
			TreeItem treeItem = (TreeItem) widget;
			treeItem.tree = tree;
			addChild(widget);
		} else {
			throw new GUIException("Only TreeItem children are supported");
		}
	}

	public Tree getTree() {
		return tree;
	}

	public void insert(MutableTreeNode child, int index) {
		throw new UnsupportedOperationException("Use addChild(Widget) instead");
	}

	public void remove(int index) {
		try {
			removeChildWidget((Widget) getChildAt(index));
		} catch (GUIException e) {
			/* Will not happen */
			throw new RuntimeException(e);
		}
	}

	public void removeAllChildren() {
		removeAllChildWidgets();
	}

	public void remove(MutableTreeNode node) {
		try {
			removeChildWidget((Widget) node);
		} catch (GUIException e) {
			/* Will not happen */
			throw new RuntimeException(e);
		}
	}

	public void removeFromParent() {
		try {
			getParentWidget().removeChildWidget(this);
		} catch (GUIException e) {
			/* Will not happen */
			throw new RuntimeException(e);
		}
	}

	public void setParent(MutableTreeNode newParent) {
		throw new UnsupportedOperationException("Parent cannot be changed, recreate the node");
	}

	public Enumeration children() {
		final Iterator iterator = getChildren().iterator();
		return new Enumeration() {
			public boolean hasMoreElements() {
				return iterator.hasNext();
			}

			public Object nextElement() {
				return iterator.next();
			}
		};
	}

	public boolean getAllowsChildren() {
		return true;
	}

	public TreeNode getChildAt(int childIndex) {
		return (TreeNode) super.getChild(childIndex);
	}

	public int getIndex(TreeNode node) {
		return super.getChildIndex((Widget) node);
	}

	public TreeNode getParent() {
		Widget parent = getParentWidget();
		try {
			return (TreeNode) parent;
		} catch (ClassCastException e) {
			return null;
		}
	}

	public int getChildCount() {
		return super.getChildCount();
	}

	public boolean isLeaf() {
		return checkLeaf && (getChildCount() == 0);
	}

	public void revalidate() throws GUIException {
		if (tree != null) {
			tree.structureChanged(this);
		}
	}
	
	public WidgetInfo getWidgetInfo() {
		return treeItemInfo;
	}
}
