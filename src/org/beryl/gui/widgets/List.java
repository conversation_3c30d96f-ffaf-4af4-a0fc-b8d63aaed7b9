/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Component;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;

import javax.swing.DefaultListCellRenderer;
import javax.swing.DefaultListModel;
import javax.swing.JList;
import javax.swing.JScrollPane;
import javax.swing.ListModel;
import javax.swing.ListSelectionModel;
import javax.swing.event.ListDataEvent;
import javax.swing.event.ListDataListener;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.ListChangeEvent;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;

public class List extends Widget {
	private static WidgetInfo listInfo = null;
	private JList list = null;
	private JScrollPane scrollPane = null;
	private ListDataModel listDataModel = null;
	private String indexKey, valueKey = null;
	private boolean sendEvents = true;
	private boolean processEvents = true;
	private boolean isConstructed = false;

	static {
		listInfo = new WidgetInfo(List.class, widgetInfo);
		listInfo.addProperty("valuekey", "string", "");
		listInfo.addProperty("indexkey", "string", "");
		listInfo.addProperty("verticalScrollBar", "bool", Boolean.FALSE);
		listInfo.addProperty("horizontalScrollBar", "bool", Boolean.FALSE);
		listInfo.addProperty("selectionMode", "enum", "multiple_interval");
		listInfo.addEvent("rightclick");
		listInfo.addEvent("doubleclick");
	};

	public static class ListDataModelAdapter implements ListModel, ModelChangeListener {
		private ListDataModel model = null;
		private ArrayList listeners = null;

		public ListDataModelAdapter(ListDataModel model) {
			this.model = model;
			model.addModelChangeListener(this);
			listeners = new ArrayList();
		}

		public int getSize() {
			return model.getSize();
		}

		public Object getElementAt(int index) {
			return model.getValue(index);
		}

		public void addListDataListener(ListDataListener l) {
			listeners.add(l);
		}

		public void removeListDataListener(ListDataListener l) {
			listeners.remove(l);
		}

		public void modelChanged(ModelChangeEvent e) {
			ListChangeEvent lce = (ListChangeEvent) e;
			ListDataEvent event =
				new ListDataEvent(e.getSource(), lce.getType(), lce.getFirstIndex(), lce.getLastIndex());

			for (int i = 0; i < listeners.size(); i++) {
				((ListDataListener) listeners.get(i)).contentsChanged(event);
			}
		}
	};

	public class ListCellRenderer extends DefaultListCellRenderer {
		public Component getListCellRendererComponent(
			JList list,
			Object value,
			int index,
			boolean sel,
			boolean hasFocus) {
			super.getListCellRendererComponent(list, value, index, sel, hasFocus);
			try {
				Item node = (Item) value;
				if (node.getIcon() != null) {
					setIcon(node.getIcon());
				}
			} catch (ClassCastException e) {
				/* Ignore */
			}
			return this;
		}
	}

	public List(Widget parent, String name) throws GUIException {
		super(parent, name);
		list = new JList();
		list.setCellRenderer(new ListCellRenderer());
		scrollPane = new JScrollPane(list);
		list.addListSelectionListener(new ListSelectionListener() {
			public void valueChanged(ListSelectionEvent e) {
				try {
					MapDataModel model = getDataModel();
					if (sendEvents && model != null && !e.getValueIsAdjusting()) {
						try {
							sendEvents = false;
							processEvents = false;
							if (indexKey != null)
								model.setValue(List.this, indexKey, list.getSelectedIndices());
							if (valueKey != null)
								model.setValue(List.this, valueKey, list.getSelectedValues());
						} finally {
							sendEvents = true;
							processEvents = true;
						}
					}
				} catch (GUIException ex) {
					throw new RuntimeException(ex);
				}
			}
		});
		list.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof Item) {
			try {
				if (listDataModel == null) {
					listDataModel = new ListDataModel();
					sendEvents = false;
					list.setModel(new ListDataModelAdapter(listDataModel));
				}
				listDataModel.addValue(this, widget);
				addChild(widget);
			} finally {
				sendEvents = true;
			}
		} else {
			throw new GUIException("Only Item children are allowed inside a List");
		}
	}

	public void removeChildWidget(Widget widget) throws GUIException {
		if (listDataModel != null) {
			listDataModel.removeValue(this, widget);
			super.removeChildWidget(widget);
		} else {
			throw new GUIException("There are no static items to remove");
		}
	}

	public void setProperty(String name, Object value) throws GUIException {
		if (name.startsWith("item.")) {
			ListModel model = list.getModel();
			if (!(model instanceof DefaultListModel))
				list.setModel(new DefaultListModel());
			((DefaultListModel) model).addElement(value);
		} else if ("indexkey".equals(name)) {
			indexKey = (String) value;
		} else if ("valuekey".equals(name)) {
			valueKey = (String) value;
		} else if ("verticalScrollBar".equals(name)) {
			scrollPane.setVerticalScrollBarPolicy(
				((Boolean) value).booleanValue()
					? JScrollPane.VERTICAL_SCROLLBAR_ALWAYS
					: JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
		} else if ("horizontalScrollBar".equals(name)) {
			scrollPane.setHorizontalScrollBarPolicy(
				((Boolean) value).booleanValue()
					? JScrollPane.HORIZONTAL_SCROLLBAR_ALWAYS
					: JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		} else {
			super.setProperty(name, value);
		}
	}

	public void setListDataModel(ListDataModel listDataModel) throws GUIException {
		ModelChangeEvent event = new ModelChangeEvent(this, listDataModel);
		this.listDataModel = listDataModel;
		sendEvents = false;
		try {
			list.setModel(new ListDataModelAdapter(listDataModel));
		} finally {
			sendEvents = true;
		}
		/* Reload data model information */
		modelChanged(event);
	}

	public ListDataModel getListDataModel() {
		return this.listDataModel;
	}

	private void setSelectionIndices(int[] indices) {
		if (indices == null) {
			list.clearSelection();
		} else {
			list.clearSelection();
			for (int i = 0; i < indices.length; i++) {
				int index = indices[i];
				list.addSelectionInterval(index, index);
				list.ensureIndexIsVisible(index);
			}
		}
	}

	private void setSelectionValues(Object[] values) {
		if (values == null) {
			list.clearSelection();
		} else {
			list.clearSelection();
			for (int i = 0; i < values.length; i++) {
				int index = listDataModel.indexOf(values[i]);
				list.addSelectionInterval(index, index);
				list.ensureIndexIsVisible(index);
			}
		}
	}

	private void reload() throws GUIException {
		MapDataModel model = getDataModel();
		if (model != null) {
			try {
				processEvents = false;

				int[] indices = indexKey == null ? null : (int[]) model.getValue(indexKey);
				Object values[] = valueKey == null ? null : (Object[]) model.getValue(valueKey);

				if (indices != null) {
					setSelectionIndices(indices);
				} else if (values != null) {
					setSelectionValues(values);
				}

				if (((values != null && indices == null) || (values == null && indices == null)) && indexKey != null) {
					model.setValue(List.this, indexKey, list.getSelectedIndices());
				}

				if (((indices != null && values == null) || (values == null && indices == null)) && valueKey != null) {
					model.setValue(List.this, valueKey, list.getSelectedValues());
				}
			} finally {
				processEvents = true;
			}
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (processEvents) {
			try {
				sendEvents = false;
				if (e.getSource() == this) {
					try {
						reload();
					} catch (IllegalArgumentException ex) {
						/* Ignore, list data model is not yet set */
					} catch (ArrayIndexOutOfBoundsException ex) {
						/* Ignore, list data model is not yet set */
					}
				} else if (e instanceof MapChangeEvent) {
					MapChangeEvent event = (MapChangeEvent) e;
					if (event.getKey() == null) {
						reload();
					} else if (event.getKey().equals(indexKey)) {
						setSelectionIndices((int[]) event.getNewValue());
						try {
							processEvents = false;
							if (valueKey != null)
								((MapDataModel) event.getModel()).setValue(
									List.this,
									valueKey,
									list.getSelectedValues());
						} finally {
							processEvents = true;
						}
					} else if (event.getKey().equals(valueKey)) {
						setSelectionValues((Object[]) event.getNewValue());
						try {
							processEvents = false;
							if (indexKey != null)
								((MapDataModel) event.getModel()).setValue(
									List.this,
									indexKey,
									list.getSelectedIndices());
						} finally {
							processEvents = true;
						}
					}
				}
			} finally {
				sendEvents = true;
			}
		}
	}

	public void addListener(String event, final String name, final GUIEventListener listener) throws GUIException {
		if ("rightclick".equals(event)) {
			list.addMouseListener(new MouseAdapter() {
				public void mousePressed(MouseEvent me) {
				    int row = list.locationToIndex(me.getPoint());
				    boolean multipleSelected = list.getSelectedIndices().length > 1;
				    boolean rowSelected = list.isSelectedIndex(row);
				    
				    if (me.isPopupTrigger() && (multipleSelected || rowSelected)) {
						listener.eventOccured(new GUIEvent(List.this, name, me));
					}
				}

				public void mouseReleased(MouseEvent me) {
					mousePressed(me);
				}
			});
		} else if (event.equals("doubleclick")) {
			list.addMouseListener(new MouseAdapter() {
				public void mousePressed(MouseEvent me) {
				    if (me.getClickCount() == 2) {
						listener.eventOccured(new GUIEvent(List.this, name, me));
					}
				}
			});
		} else {
			super.addListener(event, name, listener);
		}
	}

	public void finalizeConstruction() throws GUIException {
		isConstructed = true;
		super.finalizeConstruction();
	}

	public Component getWidget() {
		return scrollPane;
	}

	public Component getRealWidget() {
		return list;
	}

	public WidgetInfo getWidgetInfo() {
		return listInfo;
	}
}
