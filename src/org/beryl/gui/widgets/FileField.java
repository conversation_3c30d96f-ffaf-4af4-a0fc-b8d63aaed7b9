/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;

import javax.swing.JButton;
import javax.swing.JPanel;
import javax.swing.JTextField;

import org.beryl.gui.DialogUtils;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.validators.ValidationException;

public class FileField extends Widget {
	private File file = null;
	private String key = null;
	private JTextField textField = null;
	private JButton button = null;
	private JPanel panel = null;
	private boolean save = false;
	private boolean sendEvents = true;
	private boolean processEvents = true;
	private String extension = "xml";

	public FileField(Widget parent, String name) throws GUIException {
		super(parent, name);
		textField = new JTextField();
		button = new JButton("..");
		panel = new JPanel();

		textField.setEditable(false);
		panel.setLayout(new BorderLayout());
		panel.add(textField, BorderLayout.CENTER);
		panel.add(button, BorderLayout.EAST);
	}

	public void validate() throws ValidationException, GUIException {
		if (hasValidators()) {
			try {
				super.validate();
				if (textField.getBackground() != TextField.DEFAULT_COLOR) {
					textField.setBackground(TextField.DEFAULT_COLOR);
				}
				if (getParentWidget() instanceof LabeledWidget) {
					((LabeledWidget) getParentWidget()).clearError();
				}
			} catch (ValidationException e) {
				textField.setBackground(TextField.ERROR_COLOR);
				if (getParentWidget() instanceof LabeledWidget) {
					((LabeledWidget) getParentWidget()).setError(e);
				}
				throw e;
			}
		}
	}

	private void reload() throws GUIException {
		MapDataModel model = getDataModel();
		if (model != null && key != null) {
			try {
				processEvents = false;
				File value = (File) model.getValue(key);
				if (value != null) {
					textField.setText(value.getPath());
				} else {
					String text = textField.getText();
					if (text != null && !text.equals("")) {
						File file = new File(text);
						this.file = file;
						model.setValue(FileField.this, key, file);
					}
				}
			} finally {
				processEvents = true;
			}
		}
	}

	public void setProperty(String name, Object value) throws GUIException {
		if (name.equals("enabled")) {
			boolean enabled = ((Boolean) value).booleanValue();
			textField.setEnabled(enabled);
			button.setEnabled(enabled);
		} else if ("key".equals(name)) {
			key = (String) value;
		} else if ("save".equals(name)) {
			save = ((Boolean) value).booleanValue();
		} else if ("extension".equals(name)) {
			extension = (String) value;
		} else {
			super.setProperty(name, value);
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (processEvents) {
			sendEvents = false;
			try {
				if (e.getSource() == this) {
					/* New data model */
					reload();
					try {
						validate();
					} catch (ValidationException ex) {
						/* Ignore, error status is displayed already */
					}
				} else if (e instanceof MapChangeEvent) {
					MapChangeEvent event = (MapChangeEvent) e;
					if (event.getKey() == null) {
						reload();
					} else if (event.getKey().equals(key)) {
						File file = (File) event.getNewValue();
						this.file = file;
						textField.setText(file.getPath());
					}
					try {
						validate();
					} catch (ValidationException ex) {
						/* Ignore, error status is displayed already */
					}
				}
			} finally {
				sendEvents = true;
			}
		}
	}
	
	
	public void finalizeConstruction() {
		textField.setPreferredSize(new Dimension(MAX_WIDTH, DEFAULT_HEIGHT));
		button.setPreferredSize(new Dimension(button.getPreferredSize().width, DEFAULT_HEIGHT));
		button.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				File file = null;
				if (save)
					file = DialogUtils.showOpenFileDialog(FileField.this, extension);
				else
					file = DialogUtils.showOpenFileDialog(FileField.this, extension);
				try {
					if (file != null) {
						MapDataModel model = getDataModel();
						FileField.this.file = file;
						textField.setText(file.getPath());

						if (model != null && key != null) {
							try {
								model.setValue(FileField.this, key, file);
							} finally {
								processEvents = true;
							}
						}
						try {
							validate();
						} catch (ValidationException ex) {
							/* Ignore, error status is displayed already */
						}
					}
				} catch (GUIException ex) {
					throw new RuntimeException(ex);
				}
			}
		});
	}

	public Component getWidget() {
		return panel;
	}

	public Component getRealWidget() {
		return textField;
	}

	public File getFile() {
		return file;
	}
}
