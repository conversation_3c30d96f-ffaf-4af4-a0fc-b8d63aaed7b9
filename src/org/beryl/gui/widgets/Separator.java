/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.Component;

import javax.swing.JPopupMenu;
import javax.swing.JSeparator;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.swing.JHorizontalSeparator;

/**
 * Dummy widget to implement separators
 */

public class Separator extends Widget {
	protected static WidgetInfo separatorInfo = null;
	private JHorizontalSeparator separator = null;
	JSeparator menuSeparator = null;

	static {
		separatorInfo = new WidgetInfo(Separator.class, widgetInfo);
	};

	public Separator(Widget parent, String name) throws GUIException {
		super(parent, name);
		if (!((parent instanceof Menu) || (parent instanceof ToolBar) || (parent instanceof PopupMenu)))
			separator = new JHorizontalSeparator(2);
	}

	public Component getWidget() {
		return separator;
	}
	
	public WidgetInfo getWidgetInfo() {
		return separatorInfo;
	}
}
