/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Component;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.swing.CommandEvent;
import org.beryl.gui.swing.CommandListener;
import org.beryl.gui.swing.ConsoleAttribute;
import org.beryl.gui.swing.JCommandConsole;

public class Console extends Widget {
	protected static WidgetInfo consoleInfo = null;
	private JCommandConsole console = null;

	static {
		consoleInfo = new WidgetInfo(Console.class, widgetInfo);
		consoleInfo.addEvent("command");
	};

	public Console(Widget parent, String name) throws GUIException {
		super(parent, name);
		console = new JCommandConsole();
	}

	public void setProperty(String name, Object value) throws GUIException {
		super.setProperty(name, value);
	}

	public void addListener(String event, final String name, final GUIEventListener listener) throws GUIException {
		if ("command".equals(event)) {
			console.addCommandListener(new CommandListener() {
				public void onCommand(CommandEvent e) {
					listener.eventOccured(new GUIEvent(Console.this, name, e));
				}
			});
		} else {
			super.addListener(event, name, listener);
		}
	}
	
	public void clear() {
		console.clear();
	}
	
	public void appendText(String text) {
		console.appendText(text);
	}

	public void appendText(String text, ConsoleAttribute attribute) {
		console.appendText(text, attribute);
	}

	public Component getWidget() {
		return console;
	}

	public WidgetInfo getWidgetInfo() {
		return consoleInfo;
	}
}
