/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WizardPageAdapter;

public class WizardPage extends Panel {
	private boolean enabled = true;
	private String title = null, description = null;
	private WizardPageAdapter adapter = null;
	
	public WizardPage(Widget parent, String name) throws GUIException {
		super(parent, name);
		if (name == null)
			throw new GUIException("WizardPage widgets need to have a name");
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("enabled".equals(name)) {
			enabled = ((Boolean) value).booleanValue();
		} else if ("title".equals(name)) {
			title = (String) value;
		} else if ("description".equals(name)) {
			description = (String) value;
		} else {
			super.setProperty(name, value);
		}
	}
	
	public boolean isEnabled() {
		return enabled;
	}
	
	public String getTitle() {
		return title;
	}

	public String getDescription() {
		return description;
	}
	
	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}

	public WizardPageAdapter getAdapter() {
		return adapter;
	}

	public void setAdapter(WizardPageAdapter adapter) throws GUIException {
		this.adapter = adapter;
		((Wizard) getParentWidget()).updateButtons();
	}
}
