/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Color;
import java.awt.Component;
import java.awt.Graphics;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.HashMap;

import javax.swing.Icon;
import javax.swing.ImageIcon;
import javax.swing.JPanel;
import javax.swing.JTabbedPane;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.component.CustomTabbedPaneUI;
import org.beryl.gui.component.ImageSplitPane;
import org.beryl.gui.component.ImageTabbedPane;

public class TabbedPane extends Widget {
	protected static WidgetInfo tabbedInfo = null;
	protected ImageTabbedPane  pane = null;
	private HashMap paneInfo = null;
	private boolean isConstructed = false;
	private ImageIcon closeIcon = null;

	static {
		tabbedInfo = new WidgetInfo(TabbedPane.class, widgetInfo);
		tabbedInfo.addEvent("rightclick");
		tabbedInfo.addEvent("close");
		try {
			tabbedInfo.addProperty("closeicon", "icon", ImageIconFactory.getIcon("remove_tab"));
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	private static class CloseIcon implements Icon {
		private Icon icon;
		private int x = 0;
		private int y = 0;
		private int height = 10;
		private int width = 10;

		public CloseIcon(ImageIcon icon) {
			this.icon = icon;

			if (icon != null) {
				height = icon.getIconHeight();
				width = icon.getIconWidth();
			}
		}

		public int getIconHeight() {
			return height;
		}

		public int getIconWidth() {
			return width;
		}

		public void paintIcon(Component c, Graphics g, int x, int y) {
			this.x = x;
			this.y = y;

			if (icon != null) {
				icon.paintIcon(c, g, x, y + 1);
			} else {
				g.drawRect(x, y + 1, width, height);
			}
		}

		public boolean contains(int x2, int y2) {
			if (!(x2 >= x) || !(x2 <= x + width))
				return false;
			if (!(y2 >= y) || !(y2 <= y + height))
				return false;
			return true;
		}
	}

	public TabbedPane(Widget parent, String name) throws GUIException {
		super(parent, name);
		pane = new ImageTabbedPane(null);
		//pane.setUI(new CustomTabbedPaneUI());
		paneInfo = new HashMap();
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		String name = widget.getName();
		if (constraint != null)
			throw new GUIException("Anchors not supported inside a tabbed pane");
		if (!(widget instanceof Panel)) {
			Component component = widget.getWidget();
			if (component instanceof JPanel) {
				
			}
			else
				throw new GUIException("Only panels can be added to a tabbed pane");
		}

		String title = (String) paneInfo.get(name + ".title");
		if (title == null)
			title = "(unnamed)";

		Icon icon = (Icon) paneInfo.get(name + ".icon");
		if (icon == null && closeIcon != null)
			icon = new CloseIcon(closeIcon);
		pane.addTab(title, icon, widget.getWidget(), (String) paneInfo.get(name + ".tooltip"));
		addChild(widget);
	}

	public void removeChildWidget(Widget widget) throws GUIException {
		pane.remove(widget.getWidget());
		super.removeChildWidget(widget);
	}

	public Widget getSelectedWidget() {
		return getChild(pane.getSelectedIndex());
	}

	public void setSelectedWidget(Widget widget) {
		pane.setSelectedIndex(getChildIndex(widget));
	}

	public void setProperty(String name, Object value) throws GUIException {
		if (name.endsWith(".title") || name.endsWith(".icon") || name.endsWith(".tooltip")) {
			String widgetName = name.substring(0, name.lastIndexOf('.'));

			paneInfo.put(name, value);
			if (isConstructed) {
				Widget widget = getWidget(widgetName);
				if (widget != null) {
					int index = pane.indexOfComponent(widget.getWidget());
					if (index == -1)
						throw new GUIException("That widget is not a child of the tabbed pane");
					pane.removeTabAt(index);

					String title = (String) paneInfo.get(widgetName + ".title");
					if (title == null)
						title = "(unnamed)";
					Icon icon = (Icon) paneInfo.get(widgetName + ".icon");
					if (icon == null && closeIcon != null)
						icon = new CloseIcon(closeIcon);
					pane.insertTab(title, icon, widget.getWidget(), (String) paneInfo.get(widgetName + ".tooltip"),
							index);
				} else {
					throw new GUIException("A tab named [" + widgetName + "] was not found");
				}
			}
		} else if ("backgroundIcon".equals(name)) {
			if (value != null && !value.equals("")) {
				pane = new ImageTabbedPane((ImageIcon) value);
				//pane.setUI(new CustomTabbedPaneUI());
			}
		}else if (name.equals("closeicon")) {
			closeIcon = (ImageIcon) value;
		} else {
			super.setProperty(name, value);
		}
	}

	public void addListener(String event, final String name, final GUIEventListener listener) throws GUIException {
		if ("rightclick".equals(event)) {
			pane.addMouseListener(new MouseAdapter() {
				public void mousePressed(MouseEvent e) {
					if (e.isPopupTrigger()) {
						if (pane.getSelectedIndex() >= 0) {
							listener.eventOccured(new GUIEvent(TabbedPane.this, name, e));
						}
					}
				}

				public void mouseReleased(MouseEvent e) {
					mousePressed(e);
				}
			});
		} else if ("close".equals(event)) {
			pane.addMouseListener(new MouseAdapter() {
				public void mouseClicked(MouseEvent e) {
					int i = pane.getSelectedIndex();

					if (i == -1 || e.isPopupTrigger())
						return;

					listener.eventOccured(new GUIEvent(TabbedPane.this, name, e));
					
//					if (pane.getIconAt(i) instanceof CloseIcon) {
//						CloseIcon icon = (CloseIcon) pane.getIconAt(i);
//
//						if (icon != null && icon.contains(e.getX(), e.getY())) {
//							listener.eventOccured(new GUIEvent(TabbedPane.this, name, e));
//						}
//					}
				}
			});
		} else {
			super.addListener(event, name, listener);
		}
	}

	public void finalizeConstruction() throws GUIException {
		isConstructed = true;
	}

	public Component getWidget() {
		return pane;
	}

	public WidgetInfo getWidgetInfo() {
		return tabbedInfo;
	}
}
