/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.Component;
import java.awt.Dimension;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WizardListener;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.swing.JWizard;


public class Wizard extends Widget {
	private JWizard wizard = null;
	
	public Wizard(Widget parent, String name) throws GUIException {
		super(parent, name);
		wizard = new JWizard(this);
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof WizardPage) {
			wizard.addPage((WizardPage) widget);
			addChild(widget);
		} else {
			throw new GUIException("Only WizardPage children are allowed inside a WizardItem");
		}
	}

	public void addWizardListener(WizardListener listener) {
		wizard.addWizardListener(listener);
	}

	public void removeWizardListener(WizardListener listener) {
		wizard.removeWizardListener(listener);
	}
	
	public WizardPage getPage(String pageName) {
		return wizard.getPage(pageName);
	}

	public void hide() {
		wizard.hide();
	}
	
	public void show() {
		/* Workaround for Bug 4102292
		 * http://developer.java.sun.com/developer/bugParade/bugs/4102292.html
		 */
		Dimension ss = wizard.getToolkit().getScreenSize();
		Dimension fs = wizard.getSize();
		wizard.setLocation((ss.width - fs.width) / 2, (ss.height - fs.height) / 2);
		wizard.show();
	}

	public void setDataModel(MapDataModel model) throws GUIException {
		wizard.setDataModel(model);
		super.setDataModel(model);
	}


	public void dispose() {
		wizard.dispose();
	}

	public void updateButtons() {
		wizard.updateButtons();
	}

	public Component getWidget() {
		return wizard;
	}
}
