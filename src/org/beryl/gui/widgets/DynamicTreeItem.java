/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;

/**
 * This class supports lazy creation of a tree as
 * nodes are expanded. When you definitely know
 * that a node is leaf, execute <code>setCheckLeaf(false)</code> 
 */

public abstract class DynamicTreeItem extends TreeItem {
	private boolean isLoaded = false;

	public DynamicTreeItem(Widget parent, String name) throws GUIException {
		super(parent, name);
	}

	/**
	 * Implement this function to load the child nodes. It
	 * will only be called on the first expansion
	 */
	protected abstract void loadChildren() throws GUIException;

	public int getChildCount() {
		if (!isLoaded && checkLeaf) {
			try {
				loadChildren();
			} catch (Exception ex) {
				GUIException ex2 = new GUIException("Error while loading dynamic tree node contents", ex);
				new MessageDialog(ex2);
			}
			isLoaded = true;
		}
		return super.getChildCount();
	}

	/**
	 * Invalidate this dynamic tree node, remove
	 * all child nodes and refresh the tree
	 */
	public void invalidate() throws GUIException {
		isLoaded = false;
		removeAllChildWidgets();
		revalidate();
	}
}
