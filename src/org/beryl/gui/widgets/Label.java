/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Color;
import java.awt.Component;

import javax.swing.ImageIcon;
import javax.swing.JLabel;
import javax.swing.JToolTip;

import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.XMLUtils;

public class Label extends Widget {
	protected static WidgetInfo labelInfo = null;
	private JLabel label = null;

	static {
		labelInfo = new WidgetInfo(Label.class, widgetInfo);
		labelInfo.addProperty("horizontalAlignment", "enum", "center");
		labelInfo.addProperty("verticalAlignment", "enum", "center");
		labelInfo.addProperty("text", "istring", "");
		try {
			labelInfo.addProperty("icon", "icon", ImageIconFactory.getIcon("broken"));
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	};

	public Label(Widget parent, String name) throws GUIException {
		super(parent, name);
		label = new JLabel() {
			public JToolTip createToolTip() {
				/* Makes validation error tooltips look right */
				return new JToolTip();
			}
		};
		label.setForeground(Color.black);
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("text".equals(name)) {
			String text = (String) value;
			if (text.indexOf("\n") != -1) {
				text = "<html>" + XMLUtils.replace(text, "\n", "<br>") + "</html>";
			}
			label.setText(text);
		} else {
			super.setProperty(name, value);
		}
	}

	public Component getWidget() {
		return label;
	}

	public void setIcon(ImageIcon icon) {
		label.setIcon(icon);
	}

	public WidgetInfo getWidgetInfo() {
		return labelInfo;
	}
}
