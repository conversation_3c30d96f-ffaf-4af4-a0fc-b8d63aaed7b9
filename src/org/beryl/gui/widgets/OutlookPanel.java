/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Color;
import java.awt.Component;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.HashMap;
import java.util.Map;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JLabel;
import javax.swing.border.Border;
import javax.swing.plaf.basic.BasicButtonUI;

import org.beryl.gui.GUIException;
import org.beryl.gui.LFConstants;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.swing.JCascadePanel;

import cz.autel.dmi.HIGConstraints;
import cz.autel.dmi.HIGLayout;

public class OutlookPanel extends Widget {
	protected static WidgetInfo outlookPanelInfo = null;
	private static Color backgroundColor = new Color(120, 120, 120);
	private static Border fillerBorder = BorderFactory.createEmptyBorder(2, 2, 2, 2);
	private static Border raisedBorder =
		BorderFactory.createCompoundBorder(BorderFactory.createRaisedBevelBorder(), fillerBorder);
	private static Border loweredBorder =
		BorderFactory.createCompoundBorder(BorderFactory.createLoweredBevelBorder(), fillerBorder);
	private static Border emptyBorder = BorderFactory.createEmptyBorder(4, 4, 4, 4);
	private JCascadePanel panel = null;
	private boolean isConstructed = false;
	private JButton button = null;
	private Map textForButton = null;

	static {
		outlookPanelInfo = new WidgetInfo(OutlookPanel.class);
	};

	public OutlookPanel(Widget parent, String name) throws GUIException {
		super(parent, name);

		textForButton = new HashMap();
		panel = new JCascadePanel();
		button = new JButton("(unnamed)");
		panel.setBackground(backgroundColor);
		panel.setButton(button);
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof Button) {
			final JButton button = (JButton) widget.getRealWidget();
			button.setBorder(emptyBorder);
			button.setForeground(backgroundColor);
			button.setBackground(backgroundColor);
			button.setUI(new BasicButtonUI());
			button.addMouseListener(new MouseListener() {

				public void mouseClicked(MouseEvent e) {
				}

				public void mousePressed(MouseEvent e) {
					button.setBorder(loweredBorder);
				}

				public void mouseReleased(MouseEvent e) {
					if (button.contains(e.getPoint()))
						button.setBorder(raisedBorder);
					else
						button.setBorder(emptyBorder);
				}

				public void mouseEntered(MouseEvent e) {
					button.setBorder(raisedBorder);
				}

				public void mouseExited(MouseEvent e) {
					button.setBorder(emptyBorder);
				}
			});
			addChild(widget);
			if (isConstructed)
				finalizeConstruction();
		} else {
			throw new GUIException("OutlookPanel can only have Button children");
		}
	}

	public void removeChildWidget(Widget widget) throws GUIException {
		super.removeChildWidget(widget);
		textForButton.remove(widget.getRealWidget());
		try {
			if (isConstructed)
				finalizeConstruction();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}


	public void setProperty(String name, Object value) throws GUIException {
		if (name.startsWith("text")) {
			button.setText((String) value);
		} else {
			super.setProperty(name, value);
		}
	}

	public void finalizeConstruction() throws GUIException {
		int childCount = getChildCount();
		int heights[] = new int[childCount * 4 + 2];

		for (int i = 0; i < childCount; i++) {
			heights[i * 4] = LFConstants.CONTENT_BUTTON_SPACING; // inter-button-spacing
			heights[i * 4 + 1] = 0; // button
			heights[i * 4 + 2] = LFConstants.COMPONENT_SPACING; // button-label spacing
			heights[i * 4 + 3] = 0; // label
		}
		heights[childCount * 4 + 1] = heights[0] = 30; // upper + lower spacing

		HIGLayout layout = new HIGLayout(new int[] { 0, 0, 0 }, heights);
		HIGConstraints constraints = new HIGConstraints();
		layout.setColumnWeight(1, 1);
		layout.setColumnWeight(3, 1);
		layout.setRowWeight(0, 1);
		layout.setRowWeight(childCount * 4 + 2, 1);
		panel.removeAll();
		panel.setLayout(layout);

		for (int i = 0; i < childCount; i++) {
			JButton button = (JButton) getChild(i).getRealWidget();
			String text = button.getText();
			if (text == null)
				text = (String) textForButton.get(button);
			if (text != null)
				textForButton.put(button, text);
			button.setText(null);
			panel.add(button, constraints.rc(i * 4 + 2, 2));
			JLabel label = new JLabel(text);
			label.setForeground(Color.white);
			panel.add(label, constraints.rcwh(i * 4 + 4, 1, 3, 1, ""));
		}
		isConstructed = true;
	}

	public Component getWidget() {
		return panel;
	}

	public WidgetInfo getWidgetInfo() {
		return outlookPanelInfo;
	}
}
