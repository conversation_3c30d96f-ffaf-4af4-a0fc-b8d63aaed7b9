/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.JRadioButton;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;

public class ButtonGroup extends Panel {
	protected static WidgetInfo buttonGroupInfo = null;
	private String key = null;
	private javax.swing.ButtonGroup buttonGroup = null;

	static {
		buttonGroupInfo = new WidgetInfo(ButtonGroup.class, panelInfo);
		buttonGroupInfo.addProperty("key", "string", "");
	};

	public ButtonGroup(Widget parentWidget, String name) throws GUIException {
		super(parentWidget, name);
		buttonGroup = new javax.swing.ButtonGroup();
	}

	public void setProperty(String name, Object value) throws GUIException {
		if ("key".equals(name)) {
			this.key = (String) value;
		} else {
			super.setProperty(name, value);
		}
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof RadioButton) {
			final RadioButton radioButton = (RadioButton) widget;
			JRadioButton button = (JRadioButton) radioButton.getRealWidget();
			super.addChild(widget, constraint);
			buttonGroup.add(button);

			button.addActionListener(new ActionListener() {
				public void actionPerformed(ActionEvent e) {
					try {
						MapDataModel model = getDataModel();
						if (model != null && key != null) {
							model.setValue(ButtonGroup.this, key, radioButton.getValue());
						}
					} catch (GUIException ex) {
						throw new RuntimeException(ex);
					}
				}
			});
		} else {
			super.addChild(widget, constraint);
		}
	}

	public void removeChildWidget(Widget widget) throws GUIException {
		if (widget instanceof RadioButton) {
			RadioButton radioButton = (RadioButton) widget;
			buttonGroup.remove((JRadioButton) radioButton.getRealWidget());
		}
		super.removeChildWidget(widget);
	}


	private void reload() throws GUIException {
		Object value = (Object) getDataModel().getValue(key);
		if (value != null) {
			RadioButton button = findButton(value);
			if (button != null)
				 ((JRadioButton) button.getRealWidget()).setSelected(true);
			else
				throw new GUIException("Invalid radio button value [" + value + "]");
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (e.getSource() == this) {
			/* New data model */
			reload();
		} else if (e instanceof MapChangeEvent) {
			MapChangeEvent event = (MapChangeEvent) e;
			if (event.getKey() == null) {
				reload();
			} else if (event.getKey().equals(key)) {
				RadioButton button = findButton(event.getNewValue());
				if (button != null)
					 ((JRadioButton) button.getRealWidget()).setSelected(true);
				else
					throw new GUIException("Invalid radio button value [" + event.getNewValue() + "]");
			}
		}
	}

	private RadioButton findButton(Object object) throws GUIException {
		for (int i=0, count = getChildCount(); i<count; i++) {
			Widget widget = (Widget) getChild(i);
			if (widget instanceof RadioButton) {
				RadioButton button = (RadioButton) widget;
				if (button.getValue() == object || button.getValue().equals(object)) {
					return button;
				}
			}
		}

		throw new GUIException("Value not found in ButtonGroup");
	}

	public WidgetInfo getWidgetInfo() {
		return buttonGroupInfo;
	}
}
