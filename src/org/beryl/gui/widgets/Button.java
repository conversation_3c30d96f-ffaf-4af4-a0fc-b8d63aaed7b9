/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Color;
import java.awt.Component;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import javax.swing.Icon;
import javax.swing.JButton;
import javax.swing.SwingConstants;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.InternationalizationManager;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.component.BorderButton;

public class Button extends Widget {
	protected static WidgetInfo buttonInfo = null;
	private BorderButton button = null;
	private boolean isDefault = false;

	static {
		buttonInfo = new WidgetInfo(Button.class, widgetInfo);
		try {
			buttonInfo.addProperty("rolloverIcon", "icon", ImageIconFactory.getIcon("broken"));
			buttonInfo.addProperty("icon", "icon", ImageIconFactory.getIcon("broken"));
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		buttonInfo.addProperty("mnemonic", "istring", "");
		buttonInfo.addProperty("default", "bool", Boolean.FALSE);
		buttonInfo.addProperty("text", "istring", "");
		buttonInfo.addPreset("ok");
		buttonInfo.addPreset("cancel");
		buttonInfo.addPreset("hover");
		buttonInfo.addEvent("clicked");
	};

	public Button(Widget parent, String name, String preset) throws GUIException {
		this(parent, name);

		if (preset.equals("cancel")) {
			button.setText(InternationalizationManager.getString("xmlgui.button.cancel"));
			button.setMnemonic(InternationalizationManager.getString("xmlgui.button.cancel.mnemonic").charAt(0));
			button.setIcon(ImageIconFactory.getIcon("close"));
		} else if (preset.equals("ok")) {
			button.setText(InternationalizationManager.getString("xmlgui.button.ok"));
			button.setMnemonic(InternationalizationManager.getString("xmlgui.button.ok.mnemonic").charAt(0));
			button.setIcon(ImageIconFactory.getIcon("ok"));
		} else if (preset.equals("hover")) {
			button.setBorderPainted(false);
			button.setOpaque(false);
			button.setContentAreaFilled(false);
			button.addMouseListener(new MouseAdapter() {
				public void mouseEntered(MouseEvent e) {
					if (button.isEnabled()) {
						button.setOpaque(true);
						button.setBorderPainted(true);
					}
				}

				public void mouseExited(MouseEvent e) {
					if(button.isSelected())
						return;
					if (button.isEnabled()) {
						button.setOpaque(false);
						button.setBorderPainted(false);
					}
				}
			});
		} else {
			throw new GUIException("Unknown preset [" + preset + "]");
		}
	}

	public Button(Widget parent, String name) throws GUIException {
		super(parent, name);
		button = new BorderButton();
		// necessary for Alloy L&F
		button.setContentAreaFilled(false);
		button.setHorizontalTextPosition(SwingConstants.CENTER);
		button.setVerticalTextPosition(SwingConstants.BOTTOM);
		
		button.setBorderPainted(false);
		button.setFocusPainted(false);
		button.setFocusable(true);
		button.setMargin(new Insets(0, 10, 0, 10));
	}

	public void addListener(String event, final String name, final GUIEventListener listener) throws GUIException {
		if ("clicked".equals(event)) {
			button.addActionListener(new ActionListener() {
				public void actionPerformed(ActionEvent e) {
					listener.eventOccured(new GUIEvent(Button.this, name, e));
				}
			});
		} else {
			super.addListener(event, name, listener);
		}
	}

	public void setProperty(String name, Object value) throws GUIException {
		if (name.equals("mnemonic")) {
			if (((String) value).length() > 0)
				button.setMnemonic(((String) value).charAt(0));
			else
				button.setMnemonic('\0');
		} else if (name.equals("default")) {
			Widget widget = getParentWidgetByClass(Frame.class);
			if (widget == null) {
				widget = getParentWidgetByClass(Dialog.class);
			}
			if (widget == null) {
				throw new GUIException("Could not find root window");
			}
			if (((Boolean) value).booleanValue()) {
				widget.setProperty("default", this);
			} else {
				if (widget.getProperty("default") == this)
					widget.setProperty("default", null);
			}
		} else if (name.equals("rolloverIcon")) {
			button.setRolloverIcon((Icon) value);
			button.setBorderPainted(false);
		}else if (name.equals("foreground")) {
			button.setForeground(new Color(Integer.parseInt(value.toString().replace("#", ""), 16)));
		} 
		else if (name.equals("enteredbackground")) {
			final String color = value.toString();
			button.addMouseListener(new MouseAdapter() {
				public void mouseEntered(MouseEvent e) {
					if (button.isEnabled()) {
						button.setOpaque(true);
						button.setBorderPainted(true);
						button.setBackground(new Color(Integer.parseInt(color.replace("#", ""), 16)));
					}
				}

				public void mouseExited(MouseEvent e) {
					if(button.isSelected())
						return;
					if (button.isEnabled()) {
						button.setOpaque(false);
						button.setBorderPainted(false);
					}
				}
			});
		} 
		else {
			super.setProperty(name, value);
		}
	}

	public Component getWidget() {
		return button;
	}

	public WidgetInfo getWidgetInfo() {
		return buttonInfo;
	}
}
