/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.Component;

import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JPanel;
import javax.swing.JSeparator;
import javax.swing.JToolBar;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.component.ImageMenuBar;
import org.beryl.gui.component.ImageToolBar;

public class ToolBar extends Widget {
	protected static WidgetInfo toolBarInfo = null;
	private ImageToolBar toolBar = null;

	static {
		toolBarInfo = new WidgetInfo(ToolBar.class, widgetInfo);
		toolBarInfo.addProperty("borderPainted", "bool", Boolean.TRUE);
		toolBarInfo.addProperty("floatable", "bool", Boolean.TRUE);
		toolBarInfo.addProperty("rollover", "bool", Boolean.FALSE);
	}

	public ToolBar(Widget parent, String name) throws GUIException {
		super(parent, name);
		toolBar = new ImageToolBar(null);
	}
	
	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof Button) {
			toolBar.add((JButton)widget.getWidget());
			addChild(widget);
		} else if (widget instanceof Panel) {
			toolBar.add((JPanel)widget.getWidget());
			addChild(widget);
		} else if (widget instanceof Separator) {
			Separator separator = (Separator) widget;
			separator.menuSeparator = new JSeparator(JSeparator.VERTICAL);
			if (toolBar.getOrientation() == JToolBar.VERTICAL)
				separator.menuSeparator.setOrientation(JSeparator.HORIZONTAL);
			else
				separator.menuSeparator.setOrientation(JSeparator.VERTICAL);
//			toolBar.add(separator.menuSeparator);
			toolBar.addSeparator();
			addChild(widget);
		} else {
			throw new GUIException("Only Button and Separator children allowed");
		}
	}	

	public void removeChildWidget(Widget widget) throws GUIException {
		if (widget instanceof Button)
			toolBar.remove((JButton) widget.getWidget());
		else if (widget instanceof Separator)
			toolBar.remove(((Separator) widget).menuSeparator);
		super.removeChildWidget(widget);
	}
	
	public void setProperty(String name, Object value) throws GUIException {
		if ("backgroundIcon".equals(name)) {
			if (value != null && !value.equals("")) {
				toolBar = new ImageToolBar((ImageIcon) value);
			}
		}
		else if ("opaque".equals(name)) {
			if (value != null && !value.equals("")) {
				toolBar.setOpaque(Boolean.valueOf(value.toString()));
			}
		}
		else {
			super.setProperty(name, value);
		}
	}

	public Component getWidget() {
		return toolBar;
	}
	
	public WidgetInfo getWidgetInfo() {
		return super.getWidgetInfo();
	}
}
