/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.widgets;

import java.awt.Component;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.AbstractButton;
import javax.swing.JCheckBox;
import javax.swing.JCheckBoxMenuItem;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;

public class CheckBox extends Widget {
	protected static WidgetInfo checkBoxInfo = null;
	private AbstractButton checkBox = null;
	private String key = null;
	private boolean processEvents = true;

	static {
		checkBoxInfo = new WidgetInfo(CheckBox.class, widgetInfo);
		checkBoxInfo.addProperty("key", "string", "");
		checkBoxInfo.addProperty("text", "istring", "");
	};

	public CheckBox(Widget parent, String name) throws GUIException {
		super(parent, name);
		
		if (parent != null && (parent instanceof Menu || parent instanceof PopupMenu))
			checkBox = new JCheckBoxMenuItem();
		else
			checkBox = new JCheckBox();

		checkBox.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				try {
					MapDataModel model = getDataModel();
					if (model != null && key != null) {
						model.setValue(CheckBox.this, key, new Boolean(checkBox.isSelected()));
					}
				} catch (GUIException ex) {
					throw new RuntimeException(ex);
				}
			}
		});
	}

	public void addListener(String event, final String name, final GUIEventListener listener) throws GUIException {
		if ("selected".equals(event)) {
			checkBox.addActionListener(new ActionListener() {
				public void actionPerformed(ActionEvent e) {
					listener.eventOccured(new GUIEvent(CheckBox.this, name, e));
				}
			});
		} else {
			super.addListener(event, name, listener);
		}
	}
	
	public void setProperty(String name, Object value) throws GUIException {
		if ("key".equals(name))
			key = (String) value;
		else
			super.setProperty(name, value);
	}

	private void reload() throws GUIException {
		MapDataModel model = getDataModel();
		if (model != null && key != null) {
			Boolean value = (Boolean) model.getValue(key);
			if (value != null) {
				checkBox.setSelected(value.booleanValue());
			} else {
				try {
					processEvents = false;
					model.setValue(key, new Boolean(checkBox.isSelected()));
				} finally {
					processEvents = true;
				}
			}
		}
	}

	public void modelChanged(ModelChangeEvent e) throws GUIException {
		if (processEvents) {
			if (e.getSource() == this) {
				reload();
			} else if (e instanceof MapChangeEvent) {
				MapChangeEvent event = (MapChangeEvent) e;
				if (event.getKey() == null) {
					reload();
				} else if (event.getKey().equals(key)) {
					checkBox.setSelected(((Boolean) event.getNewValue()).booleanValue());
				}
			}
		}
	}

	public Component getWidget() {
		return checkBox;
	}

	public WidgetInfo getWidgetInfo() {
		return checkBoxInfo;
	}
}
