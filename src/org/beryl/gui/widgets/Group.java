/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.util.ArrayList;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.JPanel;
import javax.swing.border.Border;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;

import cz.autel.dmi.HIGLayout;

/**
 * A Group is an intelligent panel which lays out LabeledWidgets. It takes
 * care of spacing according  to the Java Look and Feel Design Guidelines.
 * Child widgets which are no labeled widgets are placed on the right side
 * as if they had no label. Buttons are placed at the bottom.
 */

public class Group extends Panel {
	protected static WidgetInfo groupInfo = null;
	private static final Border GROUP_BORDER = BorderFactory.createEmptyBorder(2,2,2,2);
	private static final int[] widths = { 0, LABEL_COMPONENT_SPACING_LEFT, DEFAULT_WIDTH };
	private ArrayList widgets = null;
	private ArrayList buttons = null;

	static {
		groupInfo = new WidgetInfo(Panel.class, panelInfo);
	};

	public Group(Widget parent, String name) throws GUIException {
		super(parent, name);
		buttons = new ArrayList();
		widgets = new ArrayList();
		panel.setBorder(GROUP_BORDER);
	}

	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (constraint != null)
			throw new GUIException("Constraints not supported inside a group");
		addChild(widget);
		if (widget instanceof Button)
			buttons.add(widget);
		else
			widgets.add(widget);
	}

	/* Calculate a layout and add the widgets */
	public void finalizeConstruction() throws GUIException {
		int height = widgets.size() * 2 + (buttons.size() > 0 ? 2 : 0);  // hack to fix annoying cut-off buttons occuring in some dialogs
		int heights[] = new int[height];

		for (int i = 0; i < widgets.size(); i++) {
			heights[i * 2] = 0;
			if (i>1 && !widgets.get(i).getClass().equals(widgets.get(i-1).getClass())) {
				heights[i * 2 + 1] = COMPONENT_GROUP_SPACING;
			} else {
				heights[i * 2 + 1] = COMPONENT_SPACING;
			}
		}
		if (widgets.size() > 0) {
			heights[widgets.size() * 2 - 1] = buttons.size() > 0 ? CONTENT_BUTTON_SPACING : 0;
			if (buttons.size() > 0)
				heights[heights.length-1] = 3; // hack to fix annoying cut-off buttons occuring in some dialogs
		}

		HIGLayout layout = new HIGLayout(widths, heights);
		layout.setRowWeight(widgets.size() * 2, 1);
		layout.setColumnWeight(3, 1);
		panel.setLayout(layout);
	
		for (int i = 0; i < widgets.size(); i++) {
			Widget widget = (Widget) widgets.get(i);
			if (widget instanceof LabeledWidget) {
				LabeledWidget lw = (LabeledWidget) widget;
				if (lw.getDataWidget().getWidget().getPreferredSize().height > DEFAULT_HEIGHT) {
					panel.add(lw.getLabel().getWidget(), constraints.rc(i * 2 + 1, 1, "rlt"));
				} else {
					panel.add(lw.getLabel().getWidget(), constraints.rc(i * 2 + 1, 1));
				}
				panel.add(lw.getDataWidget().getWidget(), constraints.rc(i * 2 + 1, 3));
			} else if (widget instanceof Separator) {
				panel.add(widget.getWidget(), constraints.rcwh(i * 2 + 1, 1, 3, 1));
			} else {
				panel.add(widget.getWidget(), constraints.rc(i * 2 + 1, 3, "rtb"));
			}
		}
		if (buttons.size() > 0) {
			JPanel buttonLayout = new JPanel();
			buttonLayout.setLayout(new BoxLayout(buttonLayout, BoxLayout.X_AXIS));
			buttonLayout.add(Box.createHorizontalGlue());
			for (int i = 0; i < buttons.size(); i++) {
				buttonLayout.add(Box.createHorizontalStrut(COMPONENT_SPACING));
				buttonLayout.add(((Button) buttons.get(i)).getWidget());
			}
			panel.add(buttonLayout, constraints.rcwh(widgets.size() * 2 + 1, 1, 3, 1, "r"));
		}
	}
	
	public void removeChildWidget(Widget widget) throws GUIException {
		if (widget instanceof Button)
			buttons.remove(widget);
		else
			widgets.remove(widget);
		super.removeChildWidget(widget);
	}

	public void revalidate() throws GUIException {
		panel.removeAll();
		finalizeConstruction();
		super.revalidate();
	}

	public WidgetInfo getWidgetInfo() {
		return widgetInfo;
	}
 }
