/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.Component;
import java.awt.event.MouseEvent;

import javax.swing.JMenuItem;
import javax.swing.JPopupMenu;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;

public class PopupMenu extends Widget {
	private static WidgetInfo popupMenuInfo = null;
	private JPopupMenu popupMenu = null;

	static {
		popupMenuInfo = new WidgetInfo(PopupMenu.class, widgetInfo);
		popupMenuInfo.setSupportsAnchor(false);
	};

	public PopupMenu(Widget parent, String name) throws GUIException {
		super(parent, name);
		popupMenu = new JPopupMenu();
	}
	
	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof MenuItem || widget instanceof CheckBox) {
			popupMenu.add((JMenuItem)widget.getWidget());
			addChild(widget);
		} else if (widget instanceof Separator) {
			Separator separator = (Separator) widget;
			separator.menuSeparator = new JPopupMenu.Separator();
			popupMenu.add(separator.menuSeparator);
			addChild(widget);
		} else {
			throw new GUIException("Only MenuItem,CheckBox and Separator children allowed");
		}
	}

	public void removeChildWidget(Widget widget) throws GUIException {
		if (widget instanceof MenuItem)
			popupMenu.remove((JMenuItem) widget.getWidget());
		else if (widget instanceof Separator)
			popupMenu.remove(((Separator) widget).menuSeparator);
		super.removeChildWidget(widget);
	}


	/**
	 * Show the popup menu
	 * @param event The GUIEvent which documents the mouse right-click action
	 */
	public void popup(GUIEvent event) {
		MouseEvent mouseEvent = (MouseEvent) event.getSwingEvent();
		popupMenu.show(((Widget)event.getSource()).getRealWidget(), mouseEvent.getX(), mouseEvent.getY());
	}
	
	public Component getWidget() {
		return popupMenu;
	}
	
	public WidgetInfo getWidgetInfo() {
		return popupMenuInfo;
	}
}