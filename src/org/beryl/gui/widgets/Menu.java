/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.widgets;

import java.awt.Component;

import javax.swing.ImageIcon;
import javax.swing.JMenu;
import javax.swing.JMenuItem;
import javax.swing.JPopupMenu;

import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.WidgetInfo;
import org.beryl.gui.component.ImageMenu;
import org.beryl.gui.component.ImageMenuBar;

public class Menu extends Widget {
	protected static WidgetInfo menuInfo = null;  
	private ImageMenu menu = null;

	static {
		menuInfo = new WidgetInfo(Menu.class, widgetInfo);
		menuInfo.addProperty("mnemonic", "istring", "");
		menuInfo.addProperty("text", "istring", "");
		menuInfo.setSupportsAnchor(false);
	};

	public Menu(Widget parent, String name) throws GUIException {
		super(parent, name);
		menu = new ImageMenu(null);
	}
	
	public void addChild(Widget widget, Object constraint) throws GUIException {
		if (widget instanceof Menu) {
			menu.add((JMenu)widget.getWidget());
			addChild(widget);
		} else if (widget instanceof Menu || widget instanceof MenuItem || widget instanceof CheckBox) {
			menu.add((JMenuItem)widget.getWidget());
			addChild(widget);
		} else if (widget instanceof Separator) {
			Separator separator = (Separator) widget;
			separator.menuSeparator = new JPopupMenu.Separator();
			menu.add(separator.menuSeparator);
			addChild(widget);
		} else {
			throw new GUIException("Only MenuItem,CheckBox and Separator children allowed");
		}
	}
	
	public void setProperty(String name, Object value) throws GUIException {
		if (name.equals("mnemonic")) {
			if (((String) value).length() > 0)
				menu.setMnemonic(((String) value).charAt(0));
			else
				menu.setMnemonic('\0');
		} else if (name.equals("backgroundIcon")) {
			if (value != null && !value.equals("")) {
				menu.setUI((ImageIcon) value);
			}
		} else {
			super.setProperty(name, value);
		}
	}
	
	public void removeChildWidget(Widget widget) throws GUIException {
		if (widget instanceof MenuItem)
			menu.remove((JMenuItem) widget.getWidget());
		else if (widget instanceof Separator)
			menu.remove(((Separator) widget).menuSeparator);
		super.removeChildWidget(widget);
	}

	public Component getWidget() {
		return menu;
	}
	
	public WidgetInfo getWidgetInfo() {
		return menuInfo;
	}
}
