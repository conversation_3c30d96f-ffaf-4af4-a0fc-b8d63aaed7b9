/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Point;

import javax.swing.BorderFactory;
import javax.swing.JFrame;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.border.Border;

import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

/**
 * Known property types:
 * 
 * <tt>int</tt> - Integer
 * <tt>float</tt> - Float
 * <tt>double</tt> - Double
 * <tt>bool</tt> - Boolean
 * <tt>long</tt> - Long
 * <tt>string</tt> - String
 * <tt>istring></tt> - Internationalized string
 * <tt>icon</tt> - Icon
 * <tt>font</tt> - Font
 * <tt>iicon</tt> - Internationalized icon
 * <tt>dimension</tt> - Dimension data (width, height)
 * <tt>point</tt> - Point data (x, y)
 * <tt>color</tt> - Color
 * <tt>border</tt> - Swing border
 * <tt>enum</tt> - Swing constants
 */

public class PropertyFactory {
	private static PropertyFactory factoryInstance = new PropertyFactory();

	private PropertyFactory() {
	}

	public Object constructProperty(Element dataNode) throws GUIException {
		String name = dataNode.getAttribute("name");
		String type = dataNode.getAttribute("type");

		if ("istring".equals(type) || "".equals(type)) {
			if (name.equals("key")
				|| name.equals("valuekey")
				|| name.equals("indexkey"))
				return XMLUtils.extractTextChildren(dataNode);
			else
				return InternationalizationManager.getString(
					XMLUtils.extractTextChildren(dataNode));
		} else if ("string".equals(type)) {
			return XMLUtils.extractTextChildren(dataNode);
		} else if ("iicon".equals(type)) {
			return ImageIconFactory.getIcon(
				InternationalizationManager.getString(
					XMLUtils.extractTextChildren(dataNode)));
		} else if ("icon".equals(type)) {
			return ImageIconFactory.getIcon(
				XMLUtils.extractTextChildren(dataNode));
		} else if ("int".equals(type)) {
			try {
				return new Integer(XMLUtils.extractTextChildren(dataNode));
			} catch (NumberFormatException e) {
				throw new GUIException("Cannot parse integer data", e);
			}
		} else if ("float".equals(type)) {
			try {
				return new Float(XMLUtils.extractTextChildren(dataNode));
			} catch (NumberFormatException e) {
				throw new GUIException("Cannot parse float data", e);
			}
		} else if ("double".equals(type)) {
			try {
				return new Double(XMLUtils.extractTextChildren(dataNode));
			} catch (NumberFormatException e) {
				throw new GUIException("Cannot parse double data", e);
			}
		} else if ("long".equals(type)) {
			try {
				return new Long(XMLUtils.extractTextChildren(dataNode));
			} catch (NumberFormatException e) {
				throw new GUIException("Cannot parse long data", e);
			}
		} else if ("bool".equals(type)) {
			return new Boolean(XMLUtils.extractTextChildren(dataNode));
		} else if ("border".equals(type)) {
			return constructBorder(dataNode);
		} else if ("dimension".equals(type)) {
			return constructDimension(dataNode);
		} else if ("point".equals(type)) {
			return constructPoint(dataNode);
		} else if ("enum".equals(type)) {
			return constructEnum(dataNode);
		} else if ("color".equals(type)) {
			return constructColor(dataNode);
		} else if ("font".equals(type)) {
			return constructFont(dataNode);
		}
		throw new GUIException("Unknown property type [" + type + "]");
	}

	private Dimension constructDimension(Element dataNode)
		throws GUIException {
		NodeList widthNodes = dataNode.getElementsByTagName("width");
		NodeList heightNodes = dataNode.getElementsByTagName("height");

		if (widthNodes.getLength() == 1 && heightNodes.getLength() == 1) {
			try {
				int width =
					Integer.parseInt(
						XMLUtils.getStringFromChild(dataNode, "width"));
				int height =
					Integer.parseInt(
						XMLUtils.getStringFromChild(dataNode, "height"));
				return new Dimension(width, height);
			} catch (NumberFormatException e) {
				throw new GUIException("Cannot parse dimension data", e);
			}
		} else {
			throw new GUIException("Invalid dimension data");
		}
	}

	private Point constructPoint(Element dataNode) throws GUIException {
		NodeList xNodes = dataNode.getElementsByTagName("x");
		NodeList yNodes = dataNode.getElementsByTagName("y");

		if (xNodes.getLength() == 1 && yNodes.getLength() == 1) {
			try {
				int x =
					Integer.parseInt(
						XMLUtils.getStringFromChild(dataNode, "x"));
				int y =
					Integer.parseInt(
						XMLUtils.getStringFromChild(dataNode, "y"));
				return new Point(x, y);
			} catch (NumberFormatException e) {
				throw new GUIException("Cannot parse dimension data", e);
			}
		} else {
			throw new GUIException("Invalid dimension data");
		}
	}

	private Color constructColor(Element dataNode) throws GUIException {
		String color = XMLUtils.extractTextChildren(dataNode);
		if (color.length() == 7 && color.startsWith("#")) {
			try {
				int r = Integer.parseInt(color.substring(1, 3), 16);
				int g = Integer.parseInt(color.substring(3, 5), 16);
				int b = Integer.parseInt(color.substring(5, 7), 16);
				return new Color(r, g, b);
			} catch (NumberFormatException e) {
				throw new GUIException("Invalid color data", e);
			}
		} else {
			throw new GUIException("Invalid color syntax");
		}
	}

	private Font constructFont(Element dataNode) throws GUIException {
		NodeList nameNodes = dataNode.getElementsByTagName("name");
		NodeList styleNodes = dataNode.getElementsByTagName("style");
		NodeList sizeNodes = dataNode.getElementsByTagName("size");

		if (nameNodes.getLength() == 1 && styleNodes.getLength() == 1 && sizeNodes.getLength() == 1) {
			try {
				int size =
					Integer.parseInt(
						XMLUtils.getStringFromChild(dataNode, "size"));
				String name = XMLUtils.getStringFromChild(dataNode, "name");
				String styleName = XMLUtils.getStringFromChild(dataNode, "style");
				int style = Font.PLAIN;
				if (styleName.equals("bold"))
					style = Font.BOLD;
				else if (styleName.equals("italic"))
					style = Font.ITALIC;
				else if (styleName.equals("bolditalic"))
					style = Font.BOLD | Font.ITALIC;
				return new Font(name, style, size);
			} catch (NumberFormatException e) {
				throw new GUIException("Cannot parse font data", e);
			}
		} else {
			throw new GUIException("Invalid font data");
		}
	}
	
	private Border constructBorder(Element dataNode) throws GUIException {
		String borderType = dataNode.getAttribute("border");
		try {
			if (borderType.equals("empty")) {
				int top =
					Integer.parseInt(
						XMLUtils.getStringFromChild(dataNode, "top"));
				int bottom =
					Integer.parseInt(
						XMLUtils.getStringFromChild(dataNode, "bottom"));
				int left =
					Integer.parseInt(
						XMLUtils.getStringFromChild(dataNode, "left"));
				int right =
					Integer.parseInt(
						XMLUtils.getStringFromChild(dataNode, "right"));
				return BorderFactory.createEmptyBorder(
					top,
					left,
					bottom,
					right);
			} else if (borderType.equals("titled")) {
				return BorderFactory.createTitledBorder(
					InternationalizationManager.getString(
						XMLUtils.getStringFromChild(dataNode, "title")));
			} else if (borderType.equals("none")) {
				return BorderFactory.createEmptyBorder();
			} else if (borderType.equals("lowered")) {
				return BorderFactory.createLoweredBevelBorder();
			} else if (borderType.equals("raised")) {
				return BorderFactory.createRaisedBevelBorder();
			} else if (borderType.equals("etched")) {
				return BorderFactory.createEtchedBorder();
			} else {
				throw new GUIException(
					"Unknown border type [" + borderType + "]");
			}
		} catch (NumberFormatException e) {
			throw new GUIException("Invalid border data", e);
		}
	}

	private Integer constructEnum(Element dataNode) throws GUIException {
		String name = XMLUtils.extractTextChildren(dataNode);
		if (name.equals("center"))
			return new Integer(SwingConstants.CENTER);
		else if (name.equals("left") || name.equals("west"))
			return new Integer(SwingConstants.LEFT);
		else if (name.equals("right") || name.equals("east"))
			return new Integer(SwingConstants.RIGHT);
		else if (name.equals("top") || name.equals("north"))
			return new Integer(SwingConstants.TOP);
		else if (name.equals("bottom") || name.equals("south"))
			return new Integer(SwingConstants.BOTTOM);
		else if (name.equals("nothing"))
			return new Integer(JFrame.DO_NOTHING_ON_CLOSE);
		else if (name.equals("dispose"))
			return new Integer(JFrame.DISPOSE_ON_CLOSE);
		else if (name.equals("exit"))
			return new Integer(JFrame.EXIT_ON_CLOSE);
		else if (name.equals("single"))
			return new Integer(ListSelectionModel.SINGLE_SELECTION);
		else if (name.equals("single_interval"))
			return new Integer(ListSelectionModel.SINGLE_INTERVAL_SELECTION);
		else if (name.equals("multiple_interval"))
			return new Integer(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		else
			throw new GUIException("Unknown enum name [" + name + "]");
	}

	public static PropertyFactory getInstance() {
		return factoryInstance;
	}
}
