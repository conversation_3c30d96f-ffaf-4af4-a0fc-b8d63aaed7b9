/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.net.URL;

import org.apache.log4j.Logger;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Frame;

/**
 * Abstract controller class. All created GUI should have their logic
 * in subclasses of this class
 */
public abstract class Controller implements GUIEventListener {
	private static WidgetFactory wf = WidgetFactory.getInstance();
	protected Logger log = null;

	/**
	 * Create the controller
	 */	
	public Controller() {
		log = Logger.getLogger(getClass());
	}
	
	/**
	 * Return an internationalized string
	 */
	protected String getString(String identifier) {
		return InternationalizationManager.getString(identifier);
	}
	
	/**
	 * Manually show a given help id
	 * 
	 * @param id The help id
	 */
	protected void showHelp(String id) {
		Widget.getHelpBroker().showID(id, "javax.help.MainWindow", null);
	}

	/**
	 * Convenience function: constructs a widget with a data model
	 * @param name The widget name inside the XML description
	 * @param dataModel The data model which the widget should use 
	 */
	protected Widget constructWidget(URL url, String name, MapDataModel dataModel) throws GUIException {
		return wf.constructWidget(url, name, this, dataModel);
	}

	/**
	 * Convenience function: constructs a widget
	 * @param name The widget name inside the XML description
	 */
	protected Widget constructWidget(URL url, String name) throws GUIException {
		return constructWidget(url, name, null);
	}

	/**
	 * Convenience function: constructs a frame
	 * @param name The frame name inside the XML description
	 */
	protected Frame constructFrame(URL url, String name) throws GUIException {
		return (Frame) constructWidget(url, name);
	}

	/**
	 * Convenience function: constructs a frame with a data model
	 * @param name The frame name inside the XML description
	 * @param dataModel The data model which the frame should use 
	 */
	protected Frame constructFrame(URL url, String name, MapDataModel dataModel) throws GUIException {
		return (Frame) constructWidget(url, name, dataModel);
	}

	/**
	 * Convenience function: constructs a dialog
	 * @param name The dialog name inside the XML description
	 */
	protected Dialog constructDialog(URL url, String name) throws GUIException {
		return (Dialog) constructWidget(url, name);
	}

	/**
	 * Convenience function: constructs a dialog with a data model
	 * @param name The dialog name inside the XML description
	 * @param dataModel The data model which the dialog should use 
	 */
	protected Dialog constructDialog(URL url, String name, MapDataModel dataModel) throws GUIException {
		return (Dialog) constructWidget(url, name, dataModel);
	}

	/**
	 * Implement event handling in subclasses
	 */
	public abstract void eventOccured(GUIEvent event);
}
