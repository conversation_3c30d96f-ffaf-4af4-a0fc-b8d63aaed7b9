/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.awt.datatransfer.Transferable;
import java.awt.dnd.DragGestureEvent;
import java.awt.dnd.DragGestureListener;

/**
 * Internally used class to facilitate the use of DnD
 */
public class DragHand<PERSON> implements DragGestureListener {
	private Widget widget;
	private String name;
	private GUIEventListener listener = null;

	public DragHandler(Widget widget, GUIEventListener listener, String name) {
		this.widget = widget;
		this.listener = listener;
		this.name = name;
	}

	public void dragGestureRecognized(DragGestureEvent dge) {
		GUIEvent event = new GUIEvent(widget, name, dge);	
		listener.eventOccured(event);
		Transferable transferable = (Transferable) event.getData();
		
		if (transferable != null)
			dge.startDrag(null, transferable);
	}
}
