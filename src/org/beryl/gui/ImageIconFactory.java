/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;

import javax.swing.ImageIcon;

import org.apache.log4j.Logger;

public class ImageIconFactory {
	private static Logger log = Logger.getLogger(ImageIconFactory.class);
	private static ArrayList searchPaths = new ArrayList();
	private static ImageIconFactory iif;
	private static HashMap hmImageIconCache = new HashMap();

	private static final String TYPE_JPG = ".jpg";
	private static final String TYPE_GIF = ".gif";
	private static final String TYPE_PNG = ".png";


	public static void addSearchPath(String path) {
		if (!searchPaths.contains(path)) {
			searchPaths.add(path);
		}
	}

	public static ImageIcon getIcon(String sName) {
		if (sName == null) {
			return null;
		}

		String sImageName = "";

		if (sName.toLowerCase().endsWith(TYPE_GIF)
			|| sName.toLowerCase().endsWith(TYPE_JPG)
			|| sName.toLowerCase().endsWith(TYPE_PNG)) {
			sImageName = sName;
		} else {
			/* PNG should be the preferred image file format because
			 * it supports real alpha transparency */
			sImageName = sName + TYPE_PNG;
		}

		if (hmImageIconCache.containsKey(sImageName)) {
			return (ImageIcon) hmImageIconCache.get(sImageName);
		}

		URL url = null;
		for (int i=searchPaths.size()-1; i>=0; i--) {
			String sPath = "/" + searchPaths.get(i) + "/" + sImageName;
			url = ImageIconFactory.class.getResource(sPath);
			if (url != null) {
				break;
			}
		}

		if (url == null) {
			if (sName.equals("broken")) {
				throw new RuntimeException("Error while trying to load the 'broken' image substitute, maybe the XML GUI has not been initialized?");
			}
			log.warn("ImageIconFactory: Image ["+sName+"] could not be found at any of the search paths");
			return getIcon("broken");
		}

		ImageIcon imageIcon = new ImageIcon(url);
		imageIcon.setDescription(sName);
		if (imageIcon != null) {
			hmImageIconCache.put(sImageName, imageIcon);
		} else {
			return getIcon("broken");
		}
		return imageIcon;
	}
}
