/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.net.URL;
import java.util.ArrayList;
import java.util.Locale;
import java.util.Properties;

import org.apache.log4j.Logger;

/**
 * This class provides internationalization support for the XML GUI
 */
public class InternationalizationManager {
	private static Properties properties = new Properties();
	private static Logger log = Logger.getLogger(InternationalizationManager.class);
	private static ArrayList sources = new ArrayList();

	/**
	 * Implement this interface to provide extensions to the internationalizationManager
	 */
	public interface InternationalizationSource {
		public String getString(String identifier); 
	};

	/**
	 * Add a language file to the internationalization database
	 */
	public static void addLanguageFile(String prefix) throws GUIException {
		try {
			URL url = InternationalizationManager.class.getResource("/" + prefix + "." + Locale.getDefault().toString() + ".properties");
			properties.load(url.openStream());
		} catch (Exception e) {
			throw new GUIException("Error while loading internationalization property file", e);
		}
	}

	/**
	 * Add an external source for internationalizations
	 */
	public static void addInternationalizationSource(InternationalizationSource source) {
		sources.add(source);
	}

	/**
	 * Get the internationalized string for a generic identifier
	 */
	public static String getString(String identifier) {
		try {
			String value = properties.getProperty(identifier);
			if (value == null) {
				for (int i=0, size=sources.size(); i<size; i++) {
					value = ((InternationalizationSource) sources.get(i)).getString(identifier);
					if (value != null)
						return value;
				}
				if (!identifier.equals(""))
					log.warn("no internationalization for identifier ["+identifier+"]");
				return identifier;
			}
			return value;
		} catch (Exception e) {
			if (identifier == null)
				return "(null)";
			else
				throw new RuntimeException("Error while looking up internationalization", e);
		}
	}
}
