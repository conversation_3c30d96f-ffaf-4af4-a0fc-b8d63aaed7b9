/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui;

import java.awt.BorderLayout;
import java.awt.Container;
import java.awt.FlowLayout;
import java.awt.LayoutManager;
import java.util.StringTokenizer;

import javax.swing.BoxLayout;

import org.beryl.gui.widgets.Dialog;
import org.beryl.gui.widgets.Frame;
import org.w3c.dom.Element;

import cz.autel.dmi.HIGLayout;

/**
 * Supported layouts:
 * 
 * HIGLayout (hig)
 * BoxLayout (hbox/vbox)
 * BorderLayout (border)
 * FlowLayout(flow, lflow, rflow)
 */

public class LayoutFactory {
	private static LayoutFactory factoryInstance = new LayoutFactory();

	private LayoutFactory() {
	}

	public LayoutManager constructLayout(Widget widget, Element layoutNode) throws GUIException {
		String type = layoutNode.getAttribute("type");
		
		if (widget instanceof Frame)
			widget = ((Frame)widget).getPanel();
		else if (widget instanceof Dialog)
			widget = ((Dialog)widget).getPanel();
		if (type.equals("") || type.equals("hig"))
			return constructHIGLayout(layoutNode);
		else if (type.equals("vbox") || type.equals("hbox"))
			return constructBoxLayout(widget, layoutNode);
		else if (type.equals("border"))
			return new BorderLayout();
		else if (type.equals("flow"))
			return new FlowLayout();
		else if (type.equals("lflow"))
			return new FlowLayout(FlowLayout.LEFT);
		else if (type.equals("rflow"))
			return new FlowLayout(FlowLayout.RIGHT);
		else
			throw new GUIException("Unknown layout ["+type+"]");
	}

	private LayoutManager constructBoxLayout(Widget widget, Element layoutNode) {
		return new BoxLayout((Container) widget.getWidget(), layoutNode.getAttribute("type").equals("vbox") ? BoxLayout.Y_AXIS : BoxLayout.X_AXIS);
	}


	private LayoutManager constructHIGLayout(Element layoutNode) throws GUIException {
		StringTokenizer horiz = new StringTokenizer(layoutNode.getAttribute("horiz"), ",");
		StringTokenizer vert = new StringTokenizer(layoutNode.getAttribute("vert"), ",");
		int horizCount = horiz.countTokens(), vertCount = vert.countTokens();
		int h[] = new int[horizCount], v[] = new int[vertCount];

		try {
			for (int i = 0; i < horizCount; i++)
				h[i] = Integer.parseInt(horiz.nextToken());
			for (int i = 0; i < vertCount; i++)
				v[i] = Integer.parseInt(vert.nextToken());
			HIGLayout layout = new HIGLayout(h, v);

			if (!layoutNode.getAttribute("hweights").equals("")) {
				StringTokenizer hweights = new StringTokenizer(layoutNode.getAttribute("hweights"), ",");
				int hweightsCount = hweights.countTokens();

				for (int i = 0; i < hweightsCount; i++) {
					layout.setColumnWeight(i + 1, Integer.parseInt(hweights.nextToken()));
				}
			}
			if (!layoutNode.getAttribute("vweights").equals("")) {
				StringTokenizer vweights = new StringTokenizer(layoutNode.getAttribute("vweights"), ",");
				int vweightsCount = vweights.countTokens();

				for (int i = 0; i < vweightsCount; i++) {
					layout.setRowWeight(i + 1, Integer.parseInt(vweights.nextToken()));
				}
			}
			return layout;
		} catch (NumberFormatException e) {
			throw new GUIException("Invalid layout data", e);
		}
	}

	public static LayoutFactory getInstance() {
		return factoryInstance;
	}
}
