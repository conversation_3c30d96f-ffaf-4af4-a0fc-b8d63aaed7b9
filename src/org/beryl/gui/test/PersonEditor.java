/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.test;

import java.net.URL;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.validators.DateFieldValidator;
import org.beryl.gui.validators.StrictTextFieldValidator;
import org.beryl.gui.validators.ValidationException;
import org.beryl.gui.widgets.Button;
import org.beryl.gui.widgets.Frame;

/**
 * Person editor
 */

public class PersonEditor extends Controller implements ModelChangeListener {
	private Frame frame = null;
	private Button okButton = null;

	public PersonEditor(MapDataModel source) throws GUIException {
		/* Create a copy of the table row */
		MapDataModel model = (MapDataModel) source.clone();
		model.setValue("source", source);

		URL url = this.getClass().getResource("PersonEditor.xml");
		frame = constructFrame(url, "PersonEditor", model);
		model.addModelChangeListener(this);

		/* Add date field validators */
		StrictTextFieldValidator validator = new StrictTextFieldValidator();
		frame.getWidget("FirstNameField").addValidator(validator);
		frame.getWidget("LastNameField").addValidator(validator);
		frame.getWidget("EmailField").addValidator(validator);
		frame.getWidget("BirthDateField").addValidator(new DateFieldValidator(DateFieldValidator.EUROPEAN_DATE));

		okButton = (Button) frame.getWidget("OKButton");
		frame.show();
	}

	public void modelChanged(ModelChangeEvent e) {
		MapChangeEvent event = (MapChangeEvent) e;
		log.debug("Data model change : '" + event.getKey() + "' => '" + event.getNewValue() + "'");

		try {
			frame.recursiveValidate();
			okButton.setEnabled(true);
		} catch (ValidationException ex) {
			try {
				/* Disable the OK button if the data does not validate */
				okButton.setEnabled(false);
			} catch (GUIException exx) {
				/* Won't happen since it is a JComponent */
			}
		} catch (GUIException ex) {
			new MessageDialog(ex);
		}
	}

	public void eventOccured(GUIEvent event) {
		try {
			log.debug("Caught event : " + event.toString());
			if (event.getName().equals("cancel")) {
				frame.dispose();
			} else if (event.getName().equals("save")) {
				Frame frame = (Frame) event.getSource().getParentWidgetByClass(Frame.class);
				MapDataModel model = frame.getDataModel();
				model.removeModelChangeListener(this);
				TableRow source = (TableRow) model.removeValueByKey(null, "source");
				source.replace(model);
				frame.dispose();
			}
		} catch (GUIException e) {
			new MessageDialog(e);
		}
	}
}
