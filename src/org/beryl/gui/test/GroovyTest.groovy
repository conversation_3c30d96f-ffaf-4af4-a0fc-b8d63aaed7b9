/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.test;

import org.beryl.gui.ScriptedController;
import org.beryl.gui.GUIEventListener;
import org.beryl.gui.GUIEvent;

class GroovyTest extends ScriptedController implements GUIEventListener {
	property dialog

	public GroovyTest(parent) {
		dialog = constructDialog("GroovyTest")
		dialog.initDialog(parent)
		dialog.show()
	}
	
	public void eventOccured(GUIEvent event) {
		println(event)
		
		if (event.getName() == 'ok') {
			dialog.dispose()
		}
	}
}
