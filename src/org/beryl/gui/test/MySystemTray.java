package org.beryl.gui.test;

import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.net.URL;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.GUIUtils;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.InternationalizationManager;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.ScriptedController;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapChangeEvent;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.model.ModelChangeEvent;
import org.beryl.gui.model.ModelChangeListener;
import org.beryl.gui.model.TableChangeEvent;
import org.beryl.gui.model.TableDataModel;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.swing.CommandEvent;
import org.beryl.gui.swing.ConsoleAttribute;
import org.beryl.gui.widgets.ComboBox;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.List;
import org.beryl.gui.widgets.Table;

import java.awt.AWTException;
import java.awt.Image;
import java.awt.MenuItem;
import java.awt.PopupMenu;
import java.awt.SystemTray;
import java.awt.Toolkit;
import java.awt.TrayIcon;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
 
import javax.swing.JFrame;
 
 
public class MySystemTray extends JFrame{
	
	public MySystemTray() {
		init();
	}
	
	public void init() {
		this.setSize(300, 200);
		this.setLocationRelativeTo(null);
		this.setTray();
		this.setVisible(true);
	}
	
	//���������ʾ��1.���жϵ�ǰƽ̨�Ƿ�֧��������ʾ
	public void setTray() {
		
		if(SystemTray.isSupported()){//�жϵ�ǰƽ̨�Ƿ�֧�����̹���
			//��������ʵ��
			SystemTray tray = SystemTray.getSystemTray();
			//��������ͼ�꣺1.��ʾͼ��Image 2.ͣ����ʾtext 3.�����˵�popupMenu 4.��������ͼ��ʵ��
			//1.����Imageͼ��
			Image image = Toolkit.getDefaultToolkit().getImage("trayIconImage/clientIcon.jpg");
			//2.ͣ����ʾtext
			String text = "MySystemTray";
			//3.�����˵�popupMenu
			PopupMenu popMenu = new PopupMenu();
			MenuItem itmOpen = new MenuItem("��");
			itmOpen.addActionListener(new ActionListener(){
				public void actionPerformed(ActionEvent e) {
					Show();
				}				
			});
			MenuItem itmHide = new MenuItem("����");
			itmHide.addActionListener(new ActionListener(){
				public void actionPerformed(ActionEvent e) {
					UnVisible();
				}
			});
			MenuItem itmExit = new MenuItem("�˳�");
			itmExit.addActionListener(new ActionListener(){
				public void actionPerformed(ActionEvent e) {
					Exit();
				}
			});
			popMenu.add(itmOpen);
			popMenu.add(itmHide);
			popMenu.add(itmExit);
			
			//��������ͼ��
			TrayIcon trayIcon = new TrayIcon(image,text,popMenu);
			//������ͼ��ӵ�������
			try {
				tray.add(trayIcon);
			} catch (AWTException e1) {
				e1.printStackTrace();
			}
		}
	}
	
	//�ڲ����в�����ֱ�ӵ����ⲿ���ʵ����this����ָ��
	public void UnVisible() {
		this.setVisible(false);
	}
	public void Show() {
		this.setVisible(true);
	}
	public void Exit() {
		System.exit(0);
	}
	
	
	public static void main(String[] args) {
		new MySystemTray();
	}
}