/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.test;

import java.text.ParseException;
import java.util.Date;

import org.beryl.gui.GUIException;
import org.beryl.gui.View;
import org.beryl.gui.model.TableRow;
import org.beryl.gui.validators.DateFieldValidator;

/**
 * Simple table row class
 */
public class Person extends TableRow {
	public Person(
		String firstName,
		String lastName,
		String email,
		Date birthDate)
		throws GUIException {
		setValue("firstName", firstName);
		setValue("lastName", lastName);
		setValue("email", email);
		setValue("birthDate", birthDate);
	}

	public void setValue(View source, String key, Object newValue)
		throws GUIException {
		if ("birthDate".equals(key)) {
			super.setValue(
				source,
				"birthDateString",
				DateFieldValidator.EUROPEAN_DATE.format((Date) newValue));
		} else if ("birthDateString".equals(key)) {
			try {
				super.setValue(
					source,
					"birthDate",
					DateFieldValidator.EUROPEAN_DATE.parse((String) newValue));
			} catch (ParseException e) {
				throw new GUIException("Invalid date [" + newValue + "]");
			}
		} else {
			super.setValue(source, key, newValue);
		}
	}

	public boolean isEditable(String key) {
		return true;
	}

	public String toString() {
		return "org.beryl.gui.test.Person[firstName="
			+ getValue("firstName")
			+ ",lastName="
			+ getValue("lastName")
			+ ",email="
			+ getValue("email")
			+ ",birthDate="
			+ getValue("birthDate")
			+ "]";
	}
};
