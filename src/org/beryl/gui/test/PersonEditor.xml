<?xml version="1.0" encoding="iso-8859-1"?>

<UI version="1.0">
	<!-- ================== Person Editor ================== -->
	<widget name="PersonEditor" class="Frame">
		<property name="title">test.editor.title</property>
		<property name="size" type="dimension">
			<width>300</width>
			<height>170</height>
		</property>
		
		<widget class="Group">
			<widget class="LabeledWidget">
				<property name="label.text">test.editor.first</property>
				<property name="mnemonic">test.editor.first.mnemonic</property>
				<widget name="FirstNameField" class="TextField">
					<property name="key" type="string">firstName</property>
				</widget>
			</widget>
		
			<widget class="LabeledWidget">
				<property name="label.text">test.editor.last</property>
				<property name="mnemonic">test.editor.last.mnemonic</property>
				<widget name="LastNameField" class="TextField">
					<property name="key" type="string">lastName</property>
				</widget>
			</widget>

			<widget class="LabeledWidget">
				<property name="label.text">test.editor.email</property>
				<property name="mnemonic">test.editor.email.mnemonic</property>
				<widget name="EmailField" class="TextField">
					<property name="key" type="string">email</property>
				</widget>
			</widget>

			<widget class="LabeledWidget">
				<property name="label.text">test.editor.birthday</property>
				<property name="mnemonic">test.editor.birthday.mnemonic</property>
				<widget name="BirthDateField" class="TextField">
					<property name="key" type="string">birthDateString</property>
				</widget>
			</widget>

			<widget class="Button" preset="cancel">
				<emit event="clicked" name="cancel"/>
			</widget>

			<widget name="OKButton" class="Button" preset="ok">
				<property name="default" type="bool">true</property>
				<emit event="clicked" name="save"/>
			</widget>
		</widget>
	</widget>
</UI>