<?xml version="1.0" encoding="iso-8859-1"?>

<UI version="1.0">
	<!-- ================== Main Window ================== -->
	<widget name="TestFrame" class="Frame">
		<layout type="border"/>
		<property name="title">test.title</property>

		<property name="spacing" type="int">4</property>
		<emit event="close" name="quit"/>

		<property name="size" type="dimension">
			<width>430</width>
			<height>220</height>
		</property>

		<widget class="MenuBar">
			<widget class="Menu">
				<property name="text">test.file</property>
				<widget class="MenuItem">
					<property name="text">test.file.wizard</property>
					<property name="icon" type="icon">new</property>
					<property name="accelerator">test.file.wizard.accelerator</property>
					<emit event="selected" name="wizard"/>
				</widget>
				<widget class="MenuItem">
					<property name="text">test.file.outlook</property>
					<property name="icon" type="icon">desktop</property>
					<property name="accelerator">test.file.outlook.accelerator</property>
					<emit event="selected" name="outlook"/>
				</widget>
				<widget class="MenuItem">
					<property name="text">test.file.dnd</property>
					<property name="icon" type="icon">drag</property>
					<property name="accelerator">test.file.dnd.accelerator</property>
					<emit event="selected" name="dnd"/>
				</widget>
				<widget class="MenuItem">
					<property name="text">test.file.groovy</property>
					<property name="accelerator">test.file.groovy.accelerator</property>
					<emit event="selected" name="groovy"/>
				</widget>
				<widget class="Separator"/>
				<widget class="MenuItem">
					<property name="text">test.file.quit</property>
					<property name="icon" type="icon">quit</property>
					<property name="accelerator">test.file.quit.accelerator</property>
					<emit event="selected" name="quit"/>
				</widget>
			</widget>
			<widget class="Menu">
				<property name="text">test.messages</property>
				<widget class="MenuItem">
					<property name="text">test.messages.info</property>
					<property name="accelerator">test.messages.info.accelerator</property>
					<emit event="selected" name="message.info"/>
				</widget>
				<widget class="MenuItem">
					<property name="text">test.messages.warning</property>
					<property name="accelerator">test.messages.warning.accelerator</property>
					<emit event="selected" name="message.warning"/>
				</widget>
				<widget class="Separator"/>
				<widget class="MenuItem">
					<property name="text">test.messages.error</property>
					<property name="accelerator">test.messages.error.accelerator</property>
					<emit event="selected" name="message.error"/>
				</widget>
				<widget class="CheckBox">
					<property name="text">test.checkbox</property>
					<property name="key" type="string">checkbox</property>
				</widget>
			</widget>
			<widget class="Spacer">
				<property name="type" type="string">glue</property>
				<property name="axis" type="string">h</property>
			</widget>
			<widget class="Menu">
				<property name="text">test.help</property>
				<widget class="MenuItem">
					<property name="text">test.help.about</property>
					<property name="icon" type="icon">help</property>
					<property name="accelerator">test.help.about.accelerator</property>
					<emit event="selected" name="about"/>
				</widget>
			</widget>
		</widget>

		
		<widget class="TabbedPane">
			<anchor type="border" border="center"/>
			<property name="LoginPanel.title">test.login</property> 
			<property name="LoginPanel.tooltip">test.login.tooltip</property> 
			<property name="LoginPanel.icon" type="icon">connect</property>
			<widget name="LoginPanel" include="this"/>
			<property name="MVCPanel.title">test.mvc</property> 
			<widget name="MVCPanel" include="this"/>
			<property name="TablePanel.title">test.table</property> 
			<widget name="TablePanel" include="this"/>
			<property name="RadioPanel.title">test.radio</property> 
			<widget name="RadioPanel" include="this"/>
			<property name="MiscPanel.title">test.misc</property> 
			<widget name="MiscPanel" include="this"/>
			<property name="IconPanel.title">test.icon</property> 
			<widget name="IconPanel" include="this"/>
			<property name="ConsolePanel.title">test.console</property> 
			<widget name="ConsolePanel" include="this"/>
		</widget>
	</widget>

	<!-- ================== Login View ================== -->
	<widget name="LoginPanel" class="Group">
		<property name="spacing" type="int">11</property>
		
		<widget class="LabeledWidget">
			<property name="label.text">test.login.username</property>
			<property name="mnemonic">test.login.username.mnemonic</property>
			<widget name="UsernameField" class="TextField">
				<property name="key" type="string">username</property>
			</widget>
		</widget>
		
		<widget class="LabeledWidget">
			<property name="label.text">test.login.password</property>
			<property name="mnemonic">test.login.password.mnemonic</property>
			<widget name="PasswordField" class="PasswordField">
				<property name="key" type="string">password</property>
			</widget>
		</widget>
		
		<widget class="Button">
			<property name="label">test.login</property>
			<property name="mnemonic">test.login.mnemonic</property>
			<property name="default" type="bool">true</property>
			<property name="icon" type="icon">arrow_right_double</property>
			<emit event="clicked" name="login"/>
		</widget>

		<widget class="Panel">
			<layout type="hbox"/>
			<widget class="CheckBox">
				<property name="text">test.login.ssl</property>
				<property name="key" type="string">ssl</property>
			</widget>
			<widget class="CheckBox">
				<property name="text">test.login.verbose</property>
				<property name="key" type="string">verbose</property>
			</widget>
		</widget>
	</widget>

	<!-- ================== MVC View ================== -->
	<widget name="MVCPanel" class="Group">
		<property name="spacing" type="int">11</property>
		
		<widget class="LabeledWidget">
			<property name="label.text">test.mvc.static</property>
			<property name="mnemonic">test.mvc.static.mnemonic</property>
			<widget class="ComboBox">
				<widget class="Item">
					<property name="text" type="string">Item 1</property>
				</widget>
				<widget class="Item">
					<property name="text" type="string">Item 2</property>
				</widget>
				<widget class="Item">
					<property name="text" type="string">Item 3</property>
				</widget>
			</widget>
		</widget>

		<widget class="LabeledWidget">
			<property name="label.text">test.mvc.mvc1</property>
			<property name="mnemonic">test.mvc.mvc1.mnemonic</property>
			<widget name="Combo1" class="ComboBox">
				<property name="indexkey" type="string">combo.index</property>
				<property name="valuekey" type="string">combo.data</property>
			</widget>
		</widget>

		<widget class="LabeledWidget">
			<property name="label.text">test.mvc.mvc1</property>
			<property name="mnemonic">test.mvc.mvc2.mnemonic</property>
			<widget name="List1" class="List">
				<property name="visibleRowCount" type="int">4</property>
				<property name="indexkey" type="string">list.index</property>
				<property name="valuekey" type="string">list.data</property>
			</widget>
		</widget>
	</widget>

	<!-- ================== Table View ================== -->
	<widget name="TablePanel" class="Panel">
		<property name="spacing" type="int">11</property>
		<layout horiz="0" vert="0,17,0" hweights="1" vweights="1,0,0"/>

		<widget name="Table1" class="Table">
			<anchor pos="1,1"/>
			<property name="column.firstName">test.table.first</property>
			<property name="column.lastName">test.table.last</property>
			<property name="column.email">test.table.email</property>
			<property name="indexkey" type="string">table.index</property>
			<property name="valuekey" type="string">table.value</property>
		</widget>

		<widget class="Button">
			<anchor pos="3,1" align="r"/>
			<property name="text">test.table.edit</property>
			<property name="mnemonic">test.table.edit.mnemonic</property>
			<emit event="clicked" name="edit"/>
		</widget>
	</widget>

	<!-- ================== Radio Button View ================== -->
	<widget name="RadioPanel" class="Group">
		<property name="spacing" type="int">11</property>
		<widget class="LabeledWidget">
			<property name="label.text">test.radio.label</property>
			<widget class="ButtonGroup">
				<layout type="vbox"/>
				<property name="key" type="string">radio</property>

				<widget class="RadioButton">
					<anchor type="box" alignx="0.9"/>
					<property name="text">test.radio.choice1</property>
					<property name="value" type="string">choice1</property>
				</widget>
				<widget class="RadioButton">
					<anchor type="box" alignx="0.9"/>
					<property name="text">test.radio.choice2</property>
					<property name="value" type="string">choice2</property>
				</widget>
				<widget class="RadioButton">
					<anchor type="box" alignx="0.9"/>
					<property name="text">test.radio.choice3</property>
					<property name="value" type="string">choice3</property>
				</widget>
			</widget>
		</widget>
	</widget>

	<!-- ================== Misc View ================== -->
	<widget name="MiscPanel" class="Panel">
		<property name="spacing" type="int">11</property>
		<layout type="vbox"/>
		<widget class="SplitPane">
			<property name="orientation" type="string">h</property>
			<widget class="Label">
				<property name="text">test.misc.left</property>
				<property name="horizontalAlignment" type="enum">center</property>
			</widget>
			<widget class="Label">
				<property name="text">test.misc.right</property>
				<property name="horizontalAlignment" type="enum">center</property>
			</widget>
		</widget>
		<widget class="Spacer">
			<property name="type" type="string">strut</property>
			<property name="axis" type="string">v</property>
			<property name="size" type="int">10</property>
		</widget>
		<widget class="ProgressBar">
			<property name="value" type="int">42</property>
		</widget>
	</widget>

	<!-- ================== Icon View ================== -->
	<widget name="IconPanel" class="Panel">
		<property name="spacing" type="int">11</property>
		<widget class="IconView">
			<property name="indexkey" type="string">icon.index</property>
			<property name="valuekey" type="string">icon.value</property>
			
			<widget class="Item">
				<property name="text">test.icon.widget</property>
				<property name="icon" type="icon">mime-widget</property>
			</widget>
			<widget class="Item">
				<property name="text">test.icon.java</property>
				<property name="icon" type="icon">mime-java</property>
			</widget>
			<widget class="Item">
				<property name="text">test.icon.document</property>
				<property name="icon" type="icon">mime-document</property>
			</widget>
			<widget class="Item">
				<property name="text">test.icon.binary</property>
				<property name="icon" type="icon">mime-binary</property>
			</widget>
			<widget class="Item">
				<property name="text">test.icon.image</property>
				<property name="icon" type="icon">mime-image</property>
			</widget>
			<widget class="Item">
				<property name="text">test.icon.shell</property>
				<property name="icon" type="icon">mime-shell</property>
			</widget>
		</widget>
	</widget>

	<!-- ================== Console View ================== -->
	<widget name="ConsolePanel" class="Panel">
		<property name="spacing" type="int">11</property>
		<widget name="CommandConsole" class="Console">
			<emit event="command" name="consoleCommand"/>
		</widget>
	</widget>
</UI>
