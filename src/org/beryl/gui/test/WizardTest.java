/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.test;

import java.net.URL;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.WizardListener;
import org.beryl.gui.WizardPageAdapter;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.widgets.Wizard;
import org.beryl.gui.widgets.WizardPage;

/**
 * Test of the wizard component
 */
public class WizardTest extends Controller implements WizardListener {
	private MapDataModel dataModel = null;
	private Wizard wizard = null;

	public WizardTest() throws GUIException {
		dataModel = new MapDataModel();
		URL url = this.getClass().getResource("WizardTest.xml");
		wizard = (Wizard) constructWidget(url, "WizardTest", dataModel);
		wizard.addWizardListener(this);

		wizard.getPage("page1").setAdapter(new WizardPageAdapter() {
			public boolean isPageReady(WizardPage page) {
				String value = (String) page.getDataModel().getValue("testValue");
				return (value != null) && !value.equals("");
			}
		});
		wizard.show();
	}

	public void eventOccured(GUIEvent event) {
	}

	public void wizardCanceled(Wizard wizard) {
	}

	public void wizardFinished(Wizard wizard) {
		log.debug("Completed test wizard! Test value is : '" + dataModel.getValue("testValue") + "'");
		wizard.dispose();
	}
}
