/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.test;

import java.net.URL;

import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.SimpleTransferable;
import org.beryl.gui.model.ListDataModel;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.widgets.Frame;
import org.beryl.gui.widgets.List;

/**
 * Drag'n'Drop test
 */
public class DnDTest extends Controller {
	private Frame frame = null;
	private MapDataModel dataModel = null;

	public DnDTest() throws GUIException {
		dataModel = new MapDataModel();
		URL url = this.getClass().getResource("DnDTest.xml");
		frame = constructFrame(url, "DnDTest", dataModel);
		List list1 = (List) frame.getWidget("List1");
		List list2 = (List) frame.getWidget("List2");

		ListDataModel strings = new ListDataModel();
		for (int i = 1; i <= 10; i++)
			strings.addValue("Item " + i);

		list1.setListDataModel(strings);
		list2.setListDataModel(new ListDataModel());

		dataModel.setValue("list1.value", new Object[] {
		});

		frame.show();
	}

	public void eventOccured(GUIEvent event) {
		try {
			log.debug("Caught event : " + event.toString());
			if (event.getName().equals("ok")) {
				frame.dispose();
			} else if (event.getName().equals("drag")) {
				Object values[] = (Object[]) dataModel.getValue("list1.value");
				if (values.length > 0) {
					SimpleTransferable transferable = new SimpleTransferable();
					transferable.addData("text/x-stringarray", values);
					event.setData(transferable);
				}
			} else if (event.getName().equals("drop")) {
				Object values[] = (Object[]) event.getData();
				List list2 = (List) event.getSource();
				ListDataModel model = list2.getListDataModel();

				for (int i = 0; i < values.length; i++)
					model.addValue(list2, values[i]);
			}
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
}