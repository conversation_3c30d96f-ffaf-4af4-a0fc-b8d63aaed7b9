/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import java.io.StringWriter;

import org.apache.xml.serialize.OutputFormat;
import org.apache.xml.serialize.XMLSerializer;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

/**
 * Miscellaneous XML utility functions
 */
public class XMLUtils {
	/**
	 * Extract all text children of an element
	 */
	public static String extractTextChildren(Element parentNode) {
		NodeList childNodes = parentNode.getChildNodes();
		String result = new String();
		for (int i = 0; i < childNodes.getLength(); i++) {
			Node node = childNodes.item(i);
			if (node.getNodeType() == Node.TEXT_NODE) {
				result += node.getNodeValue();
			}
		}
		return result;
	}

	/**
	 * Get a child element
	 */
	public static Element getChild(Element parentNode, String nodeName) {
		NodeList childNodes = parentNode.getChildNodes();

		for (int i = 0; i < childNodes.getLength(); i++) {
			Node node = childNodes.item(i);
			if (node.getNodeType() == Node.ELEMENT_NODE && nodeName.equals(node.getNodeName())) {
				return (Element) node;
			}
		}
		return null;
	}

	/**
	 * Get the text content of a child element
	 */
	public static String getStringFromChild(Element parentNode, String nodeName) {
		Element child = getChild(parentNode, nodeName);
		if (child == null)
			return null;
		else
			return extractTextChildren(child);
	}

	/**
	 * String replacement utility function
	 */
	public static String replace(String string, String from, String to) {
		if (string.indexOf(from) > -1) {
			StringBuffer sb = new StringBuffer();
			int ix = -1;
			while ((ix = string.indexOf((from))) >= 0) {
				sb.append(string.substring(0, ix)).append(to);
				string = string.substring(ix + from.length());
			}
			if (string.length() > 1)
				sb.append(string);
			return sb.toString();
		} else {
			return string;
		}
	}

	/**
	 * Convert an XML document to a latin-1 string
	 */
	public static String serializeXML(Document document) throws GUIException {
		try {
			StringWriter stringWriter = new StringWriter();
			OutputFormat format = new OutputFormat("XML", "utf-8", true);
			format.setLineWidth(0);
			XMLSerializer serializer = new XMLSerializer(stringWriter, format);
			serializer.asDOMSerializer().serialize(document);
			/* We don't want the newline character at the end */
			String string = stringWriter.toString();
			return string.substring(0, string.length() - 1);
		} catch (Exception e) {
			throw new GUIException("Could not serialize XML", e);
		}
	}

	public static String serializeXML(Element element) throws GUIException {
		try {
			StringWriter stringWriter = new StringWriter();
			OutputFormat format = new OutputFormat("XML", "utf-8", true);
			format.setLineWidth(0);
			XMLSerializer serializer = new XMLSerializer(stringWriter, format);
			serializer.asDOMSerializer().serialize(element);
			/* We don't want the newline character at the end */
			String string = stringWriter.toString();
			return string.substring(0, string.length() - 1);
		} catch (Exception e) {
			throw new GUIException("Could not serialize XML", e);
		}
	}
}
