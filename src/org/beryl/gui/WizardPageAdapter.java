/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui;

import org.beryl.gui.widgets.WizardPage;

/**
 * Extend the wizard page adapter class to have more control
 * over the flow of a wizard.
 */
public abstract class WizardPageAdapter {
	/**
	 * Called upon preparation of a wizard page
	 */
	public void preparePage(WizardPage page) {
	}
	
	/**
	 * Called upon finalization of a wizard page. Return false to
	 * avoid going to the next page. You should also display an
	 * error message in this case or the behavior will be considered
	 * strange.
	 */
	public boolean finalizePage(WizardPage page) { return true; }
	
	/**
	 * Called to check whether the current page is ready and
	 * it is possible to move on to the next page. Return false to
	 * disable the "Next" button.
	 */
	public boolean isPageReady(WizardPage page) { return true; }
}
