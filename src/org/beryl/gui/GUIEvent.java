/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui;

/**
 * A GUIEvent indicates an event in the XML GUI, such as a
 * button press or a right mouse click. It does, however, not
 * indicate any data changes. this is done by ModelChangeEvent
 */

public class GUIEvent {
	private Widget source = null;
	private String name = null;
	private Object swingEvent = null;
	private Object data = null;

	public GUIEvent(Widget source, String name, Object swingEvent, Object data) {
		this(source, name, swingEvent);

		this.data = data;
	}
	
	public GUIEvent(Widget source, String name, Object swingEvent) {
		this.name = name;
		this.source = source;
		this.swingEvent = swingEvent;
	}

	public Widget getSource() {
		return source;
	}

	public Object getSwingEvent() {
		return swingEvent;
	}

	public Object getData() {
		return data;
	}

	/**
	 * This may be changed, used to make DnD work
	 */
	public void setData(Object data) {
		this.data = data;
	}

	public String getName() {
		return name;
	}
	
	public String toString() {
		return "org.beryl.gui.GuiEvent[name=\""+name+"\", source="+source+"]";
	}
}
