/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.beryl.gui.GUIException;
import org.beryl.gui.View;
import org.beryl.gui.widgets.Item;

/**
 * List-based data model
 */

public class ListDataModel extends AbstractDataModel implements Serializable {
	private List list = null;

	public ListDataModel() {
		list = new ArrayList();
	}

	public ListDataModel(List list) {
		this.list = list;
	}

	public ListDataModel(Collection collection) {
		list = new ArrayList(collection);
	}

	public void setValue(int index, Object newValue) throws GUIException {
		setValue(null, index, newValue);
	}

	public void setValue(View view, int index, Object newValue) throws GUIException {
		ListChangeEvent event = new ListChangeEvent(view, this, ListChangeEvent.INTERVAL_CHANGED, index, index);
		list.set(index, newValue);
		fireModelEvent(event);
	}

	public void removeIndex(View view, int index) throws GUIException {
		ListChangeEvent event = new ListChangeEvent(view, this, ListChangeEvent.INTERVAL_REMOVED, index, index);
		list.remove(index);
		fireModelEvent(event);
	}

	public void removeValue(View view, Object value) throws GUIException {
		int index = list.indexOf(value);
		if (index == -1)
			throw new GUIException("Value could not be found");
		removeIndex(view, index);
	}

	public void insertValue(View view, int index, Object value) throws GUIException {
		if (value instanceof Item) {
			((Item) value).setOwningDataModel(this);
		}
		
		ListChangeEvent event = new ListChangeEvent(view, this, ListChangeEvent.INTERVAL_ADDED, index, index);
		list.add(index, value);
		fireModelEvent(event);
	}

    public void addValue(View view, Object value) throws GUIException {
    	insertValue(view, list.size(), value);
    }

	public void addValue(Object value) throws GUIException {
		insertValue(null, list.size(), value);
	}

	public void clear(View view) throws GUIException {
		ListChangeEvent event = new ListChangeEvent(view, this, ListChangeEvent.INTERVAL_REMOVED, 0, list.size()-1);
		list.clear();
		fireModelEvent(event);
	}

	public int indexOf(Object value) {
		return list.indexOf(value);
	}

	public Object getValue(int index) {
		return list.get(index);
	}
	
	public int getSize() {
		return list.size();
	}
}
