/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
package org.beryl.gui.model;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import org.beryl.gui.GUIException;

public class ValueObjectTableRow extends TableRow implements Serializable {
	private Object valueObject = null;

	public ValueObjectTableRow(TableDataModel.ReflectionInfo info, Object valueObject) throws GUIException {
		this.valueObject = valueObject;
		
		for (int i=0; i<info.fieldNames.size(); i++) {
			String name = (String) info.fieldNames.get(i);
			Method method = (Method) info.fieldMethods.get(i);
			
			try {
				Object value = method.invoke(valueObject, null);
				setValue(name, value);
			} catch (InvocationTargetException e) {
				throw new GUIException("Invocation target exception while trying to access value object data", e);
			} catch (IllegalAccessException e) {
				throw new GUIException("Illegal access exception while trying to access value object data", e);
			}
		}
	}
	
	public final Object getValueObject() {
		return valueObject;
	}
}
