/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.model;

import java.io.Serializable;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.table.TableEditor;
import org.beryl.gui.table.TableRenderer;
import org.beryl.gui.widgets.Table;

/**
 * A <tt>TableRow</tt> represents a row on a table widget.
 * Columns are accessed by keys rather than their index - this
 * makes it possible to use a <tt>TableRow</tt> both as a
 * row in a table and as data model of a <tt>Frame</tt> at
 * the same time.
 */

public class TableRow extends MapDataModel implements TableRenderer, TableEditor, Serializable {
	public boolean isEditable(String key) {
		return false;
	}
	
	/**
	 * If there are custom renderers, override this and set it to true.
	 * This method exists solely for performance reasons
	 * @param key The row key
	 * @return True if there is a custom renderer
	 */
	
	public boolean hasCustomRenderer(String key) {
		return false;
	}
	
	/**
	 * This function, if overridden provides the possibility
	 * of adding a custom table value editor 
	 * @param key The row key to edit
	 * @return A Widget
	 */
	public Widget getEditor(Table table, Object value, TableRow row, String key) throws GUIException {
		return null;
	}

	/**
	 * This function, if overridden provides the possibility
	 * of adding a custom table value render 
	 * @param key The row key to render
	 * @return A Widget
	 */
	public Widget getRenderer(Table table, Object value, boolean isSelected, boolean hasFocus, TableRow row, String key)
		throws GUIException {
		return null;
	}
}
