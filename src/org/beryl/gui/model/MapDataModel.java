/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.gui.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;

import org.beryl.gui.GUIException;
import org.beryl.gui.View;

/**
 * Key-Value based data model
 */

public class MapDataModel extends AbstractDataModel implements Serializable {
	private HashMap values = new HashMap();

	public void setValue(View source, String key, Object newValue) throws GUIException {
		MapChangeEvent event = new MapChangeEvent(source, this, key, values.get(key), newValue);
		values.put(key, newValue);
		fireModelEvent(event);
	}

	public Object getValue(String key) {
		return values.get(key);
	}

	public Object removeValueByKey(View source, String key) throws GUIException {
		MapChangeEvent event = new MapChangeEvent(source, this, key, values.get(key), null);
		Object value = values.remove(key);
		fireModelEvent(event);
		return value;
	}

	public void clear(View source) throws GUIException {
		MapChangeEvent event = new MapChangeEvent(source, this, null, null, null);
		values.clear();
		fireModelEvent(event);
	}

	public void copy(View source, MapDataModel model) throws GUIException {
		MapChangeEvent event = new MapChangeEvent(source, this, null, null, null);
		if (getClass().equals(MapDataModel.class)) {
			/* Faster */
			values.putAll(model.values);
		} else {
			/* There might be dynamic content or content which requires validation .. */
			for (Iterator i = model.getKeys().iterator(); i.hasNext();) {
				String key = (String) i.next();
				setValue(source, key, model.getValue(key));
			}
		}
		fireModelEvent(event);
	}

	public Set getKeys() {
		return values.keySet();
	}

	public void replace(View view, MapDataModel model) throws GUIException {
		values.clear();
		copy(view, model);
	}

	public Object clone() {
		MapDataModel model = new MapDataModel();
		try {
			model.copy(null, this);
		} catch (GUIException e) {
			/* Will never occur since there are no listeners attached yet */
		}
		return model;
	}

	public String toString() {
		StringBuffer buf = new StringBuffer();
		buf.append("org.beryl.gui.MapDataModel [");
		for (Iterator i=values.keySet().iterator(); i.hasNext(); ) {
			String key = (String) i.next();
			Object value = values.get(key);
			
			buf.append(key).append("=").append(value);
			if (i.hasNext())
				buf.append(", ");
		}
		buf.append("]");
		return buf.toString();
	}

	/* ========== Convenience methods ========== */

	public final void setValue(String key, Object newValue) throws GUIException {
		setValue(null, key, newValue);
	}

	public final Object removeValueByKey(String key) throws GUIException {
		return removeValueByKey(null, key);
	}

	public final void clear() throws GUIException {
		clear(null);
	}

	public final void replace(MapDataModel model) throws GUIException {
		replace(null, model);
	}
}
