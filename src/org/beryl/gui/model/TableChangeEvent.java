/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.model;

import org.beryl.gui.View;

public class TableChangeEvent extends ModelChangeEvent {
    public static final int INTERVAL_INSERTED =  1;
    public static final int INTERVAL_CHANGED =  0;
    public static final int INTERVAL_REMOVED = -1;

	private int type, firstIndex, lastIndex;
	private String key = null;

	public TableChangeEvent(View source, TableDataModel model, int type, int firstIndex, int lastIndex, String key) {
		super(source, model);
		this.type = type;
		this.firstIndex = firstIndex;
		this.lastIndex = lastIndex;
		this.key = key;
	}

	public int getFirstIndex() {
		return firstIndex;
	}

	public int getLastIndex() {
		return lastIndex;
	}

	public int getType() {
		return type;
	}
	
	public String getKey() {
		return key;
	}
}
