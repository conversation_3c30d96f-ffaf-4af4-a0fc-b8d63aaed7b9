/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */
 
package org.beryl.gui.model;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

import org.beryl.gui.GUIException;
import org.beryl.gui.View;

public abstract class AbstractDataModel implements DataModel, Serializable {
	private transient Set modelChangeListeners = null;
	private transient Set views = null;
	
	public void addView(View view) {
		if (views == null)
			views = new HashSet();
		views.add(view);
	}

	public void removeView(View view) {
		if (views != null)
			views.remove(view);
	}

	public void addModelChangeListener(ModelChangeListener mcl) {
		if (modelChangeListeners == null)
			modelChangeListeners = new HashSet();
		modelChangeListeners.add(mcl);
	}

	public void removeModelChangeListener(ModelChangeListener mcl) {
		if (modelChangeListeners != null)
			modelChangeListeners.remove(mcl);
	}
	
	protected void fireModelEvent(ModelChangeEvent event) throws GUIException {
		if (views != null) {
			/* Update all views */
			for (Iterator i=views.iterator(); i.hasNext(); ) {
				View view = (View) i.next();
				/* Dont send events back to their source */
				if (view != event.getSource()) {
					view.modelChanged(event);
				}
			}
		}

		if (modelChangeListeners != null) {
		/* If there are additional ModelChangeListeners attached, notify them too */
			for (Iterator i=modelChangeListeners.iterator(); i.hasNext(); ) {
				((ModelChangeListener)i.next()).modelChanged(event);
			}
		}
	}
}
