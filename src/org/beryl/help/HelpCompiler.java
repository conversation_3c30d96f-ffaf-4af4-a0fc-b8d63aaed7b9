/*
 * Beryl - A web platform based on XML, XSLT and Java
 * This file is part of the Beryl XML GUI
 *
 * Copyright (C) 2004 <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-3107  USA
 */

package org.beryl.help;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.net.URL;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;

import org.apache.log4j.Logger;
import org.beryl.gui.DialogUtils;
import org.beryl.gui.GUIUtils;
import org.w3c.dom.Document;

class HelpException extends Exception {
	public HelpException(String message) {
		super(message);
	}

	public HelpException(String message, Throwable cause) {
		super(message, cause);
	}
}

public class HelpCompiler {
	private static Logger log = null;

	public static void main(String args[]) {
		try {
			GUIUtils.initializeLogging();
			log = Logger.getLogger(HelpCompiler.class);

			if (args.length != 4) {
				throw new HelpException("Syntax: org.beryl.help.HelpCompiler <indir> <outdir> <map file> <language>");
			}

			new HelpCompiler().compile(
				new File(args[0]),
				new File(args[1]),
				new File(args[2]),
				args[3]);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void compile(File inDir, File outDir, File mapFile, String locale) throws HelpException {
		String xmlFiles[] = inDir.list(new DialogUtils.ExtensionFilter("xml", false));
		Transformer transformer = null;
		DocumentBuilder db = null;
		PrintWriter mapPrinter = null;

		log.debug("generating help file with locale [" + locale + "]");
		try {
			log.debug("loading xml parser ..");
			db = DocumentBuilderFactory.newInstance().newDocumentBuilder();
		} catch (Exception e) {
			throw new HelpException("Error while loading an XML parser", e);
		}

		try {
			log.debug("creating map file [" + mapFile.getPath() + "] ..");
			mapPrinter = new PrintWriter(new BufferedOutputStream(new FileOutputStream(mapFile)));
			mapPrinter.println("<?xml version='1.0' encoding='ISO-8859-1' ?>");
			mapPrinter.println(
				"<!DOCTYPE map PUBLIC \"-//Sun Microsystems Inc.//DTD JavaHelp Map Version 1.0//EN\""
					+ " \"http://java.sun.com/products/javahelp/map_1_0.dtd\">\n");
			mapPrinter.println("<!-- Do not change, this file is automatically generated -->");
			mapPrinter.println("<map version=\"1.0\">");
		} catch (Exception e) {
			throw new HelpException("Error while creating the map file", e);
		}

		try {
			log.debug("loading transformer ..");
			URL xslFile = getClass().getResource("/resources/xmlgui/help.xsl");
			transformer = TransformerFactory.newInstance().newTransformer(new StreamSource(xslFile.openStream()));
			transformer.setParameter("locale", locale);
		} catch (Exception e) {
			throw new HelpException("Error while constructing the transformer", e);
		}

		for (int i = 0; i < xmlFiles.length; i++) {
			String xmlFile = xmlFiles[i];
			String idName = xmlFile.substring(0, xmlFile.length() - 4);
			File sourceFile = new File(inDir.getPath() + "/" + xmlFile);
			File outputFile = new File(outDir.getPath() + "/" + idName + "." + locale + ".html");
			Document document = null;

			log.debug("loading help file [" + xmlFile + "]");
			try {
				document = db.parse(sourceFile);
				mapPrinter.println(
					"	<mapID target=\""
						+ document.getDocumentElement().getAttribute("id")
						+ "\" url=\""
						+ outputFile.getName()
						+ "\" />");
			} catch (Exception e) {
				throw new HelpException("Error while loading [" + xmlFile + "]", e);
			}

			if (outputFile.exists() && outputFile.lastModified() >= sourceFile.lastModified())
				continue;

			log.debug("transforming help file to [" + outputFile.getPath() + "]");
			StreamResult result = new StreamResult(outputFile);

			try {
				transformer.transform(new DOMSource(document), result);
			} catch (Exception e) {
				throw new HelpException("Error during the transformation of [" + xmlFile + "]", e);
			}
		}
		mapPrinter.println("</map>");
		mapPrinter.close();
	}
}
