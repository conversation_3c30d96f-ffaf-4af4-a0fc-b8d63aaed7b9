log4j.rootLogger = ERROR
log4j.category.org.springframework = ERROR
log4j.logger.net.sf.hibernate=ERROR   
lof4j.logger.net.sf.hibernate.SQL=DEBUG 

log4j.appender.Console                          = org.apache.log4j.ConsoleAppender
log4j.appender.Console.target                   = System.err
log4j.appender.Console.ImmediateFlush           = true
log4j.appender.Console.layout                   = org.apache.log4j.PatternLayout
log4j.appender.Console.layout.ConversionPattern = %d{yyyy/MM/dd HH:mm:ss,SSS} %-5p [%t] %m%n
log4j.logger.org.beryl.gui=ERROR

log4j.category.org.beryl=File
log4j.category.org.beryl.additivity=false

log4j.appender.File                          = org.apache.log4j.DailyRollingFileAppender
log4j.appender.File.File                     = logs/log.log
log4j.appender.File.DatePattern              = '.'yyyy-MM-dd
log4j.appender.File.layout                   = org.apache.log4j.PatternLayout
log4j.appender.File.layout.ConversionPattern = %d{yyyy/MM/dd HH:mm:ss,SSS} %-5p [%t] %m%n


log4j.appender.DB                            = org.apache.log4j.DailyRollingFileAppender
log4j.appender.DB.File                       = logs/db.log
log4j.appender.DB.DatePattern                = '.'yyyy-MM-dd
log4j.appender.DB.layout                     = org.apache.log4j.PatternLayout
log4j.appender.DB.layout.ConversionPattern   = %d{yyyy/MM/dd HH:mm:ss,SSS} %-5p [%t] %m%n