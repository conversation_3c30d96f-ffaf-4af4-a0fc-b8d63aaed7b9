package czprule.rule.view;

import com.tellhow.czp.svg.document.DefaultSVGDocumentResolver;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import czprule.model.PowerDevice;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.util.List;


public class ShowChekInfo extends JFrame {
	 public static SVGDocumentResolver resovler = DefaultSVGDocumentResolver
				.getResolver();
	private List<Object> list;
	private JTable table;

	public ShowChekInfo(List<Object> list) {
		this.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
		this.setTitle("未关联的设备");
		this.setLocationRelativeTo(null);
		this.list=list;
		JScrollPane scrollPane = new JScrollPane();
		getContentPane().add(scrollPane, BorderLayout.CENTER);
		this.setSize(200,300);
		table = new JTable();
		table.setModel(new DefaultTableModel(
			toModel(),
			new String[] {
				"未关联图形的设备"
			}
		));
		scrollPane.setViewportView(table);
		
	}

	private Object[][] toModel() {
		int size=list.size();
		Object[][] model=new Object[size][];
		int i=0;
		String name = "";
		for(Object obj:list){/*
			if(obj.getClass()==SVGOMGElement.class){
				name=resovler.getDeviceName((Element)obj);
			}*/
			if(obj.getClass()==PowerDevice.class){
				name=((PowerDevice)obj).getPowerDeviceName();
			}
			model[i]=new Object[1];
			model[i][0]=name;
			i++;
		}
		return model;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ShowChekInfo other = (ShowChekInfo) obj;
		if (list == null) {
			if (other.list != null)
				return false;
		} else if (!list.equals(other.list))
			return false;
		if (table == null) {
			if (other.table != null)
				return false;
		} else if (!table.equals(other.table))
			return false;
		return true;
	}
	

}
