package czprule.rule.view;

import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;
import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.dao.CustomCodexDao;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.SVGAddDeviceInf;
import czprule.system.ShowMessage;
import czprule.wordcard.dao.DeviceStateMentManager;
import czprule.wordcard.view.InitDeviceTypeChockBox;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemEvent;
import java.util.List;
import java.util.*;

/**
 * 泰豪软件股份有限公司
 * 
 * <AUTHOR> 修改：张余平
 */
@SuppressWarnings("serial")
public class CustomCodex extends javax.swing.JPanel implements SVGAddDeviceInf {

	private PowerDevice choosePd = null; // 点击选择设备
	private PowerDevice executeDevice = null;// 执行子设备
	private boolean isLocked1 = true; // true:点击设备后切换源设备，false：添加关联设备
	private boolean isLocked2 = true; // true:点击设备后切换源设备，false：添加前置设备
	
	CustomCodexDao ccdd = new CustomCodexDao();
	private DefaultTableModel jtableModel1;
	private DefaultTableModel jtableModel2;
	private DefaultTableModel jtableModel3;

	/** Creates new form CustomCodex */
	public CustomCodex() {
		initComponents();
//		initTable1(this.getLinkDevices());
		initTable1(null);
		initTable2();
		initTable3();
		addAction();
		init(false);

	}

	// 添加设备入口
	public void addPowerDevice(PowerDevice pd) {
		addDevice(pd);
	}

	public void addDevice(PowerDevice pd) {
		if (isLocked1&&isLocked2) { // 切换设备

			this.choosePd = pd;
			jTextField1.setText(this.choosePd.getPowerDeviceName());
			jComboBox1.setModel(InitDeviceTypeChockBox.getDeviceStatusCheckBox(this.choosePd.getDeviceType()));
			jComboBox2.setModel(InitDeviceTypeChockBox.getDeviceStateCheckBox(this.choosePd.getDeviceType(), CBSystemConstants.cardbuildtype));

			// 初始化表格
//			this.initTable1(this.getLinkDevices());
			initTable1(null);
			this.initTable2();
			this.initTable3();
			init(false);
		} else if(!isLocked1){
			// 设备执行动作表添加设备
			this.executeDevice = pd;
			String stationName = executeDevice.getPowerStationName();// 所在变电站
			CodeNameModel cnm = new CodeNameModel();
			cnm.setCode(executeDevice.getDeviceStatus());
			cnm.setName(CBSystemConstants.getDeviceStatusName(executeDevice.getDeviceType(), executeDevice.getDeviceStatus()));
			Object[] rowData = { (this.jtableModel1.getRowCount() + 1), stationName, executeDevice, cnm, "" };
			this.jtableModel1.addRow(rowData);
		} else{
			// 设备执行动作表添加设备
			this.executeDevice = pd;
			String stationName = executeDevice.getPowerStationName();// 所在变电站
			CodeNameModel cnm = new CodeNameModel();
			cnm.setCode(executeDevice.getDeviceStatus());
			cnm.setName(CBSystemConstants.getDeviceStatusName(executeDevice.getDeviceType(), executeDevice.getDeviceStatus()));
			Object[] rowData = { (this.jtableModel3.getRowCount() + 1), stationName, executeDevice, cnm, false };
			this.jtableModel3.addRow(rowData);
		}
	}

	// 初始化隐藏按钮
	public void init(boolean isHide) {
		this.jButton2.setEnabled(isHide);
		this.jButton3.setEnabled(isHide);
		this.jButton4.setEnabled(isHide);
		this.jButton5.setEnabled(isHide);
		this.jButton6.setEnabled(isHide);
	    
		this.jButton8.setEnabled(isHide);
		this.jButton12.setEnabled(isHide);
		this.jButton13.setEnabled(isHide);
		this.jButton14.setEnabled(isHide);
		
		this.jButton11.setEnabled(isHide);
		this.jButton9.setEnabled(isHide);
	}

	// 术语类型按钮事件（状态令、元件令）
	public void actionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		String status = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
		String state = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		if (status.equals("")) {
			return;
		}
		if (state.equals("")) {
			return;
		}
		String equipId = choosePd.getPowerDeviceID();// 设备id
		String stateType = "";
		if (jRadioButton1.isSelected() == true) {
			stateType = "0";
		} else {
			stateType = "1";
		}
		String[] czrwIDs = ccdd.getCzrw(equipId, status, state, stateType);
		jTextArea2.setText(czrwIDs[0]);
		List<String> descs = ccdd.getShuYu(czrwIDs[1]);
		StringBuffer str = new StringBuffer();
		for (int i = 0; i < descs.size(); i++) {
			String tempStr = descs.get(i);
			if (str.length() == 0) {
				str.append(tempStr);
			} else {
				str.append("\r\n" + tempStr);
			}
		}
		this.jTextArea1.setText(str.toString());
	}

	/**
	 * 下拉监听
	 * 
	 * @param e
	 */
	public void itemStateChanged(ItemEvent e) {

		String status = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
		String state = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		if (status.equals("")) {
			return;
		}
		if (state.equals("")) {
			return;
		}
		init(true);
		String equipId = choosePd.getPowerDeviceID();// 设备id
		List<String[]> oneList = ccdd.getUserRuleExecuteEquip(equipId, status, state);
		List<String[]> twoList = ccdd.getUserRuleJudgeClass(equipId, status, state);
        List<String[]> threeList=ccdd.getUserPreCondition(equipId, status, state);
		jtableModel1.setRowCount(0);
		if (oneList.size() > 0) {
			String[] tempStr = new String[4];
			for (int l = 0; l < oneList.size(); l++) {
				tempStr = oneList.get(l);
				PowerDevice pdN = CBSystemConstants.getPowerDevice(tempStr[1], tempStr[0]);
				String stionName = pdN.getPowerStationName();// 设备变电站名称
				CodeNameModel statuscnm = new CodeNameModel();
				statuscnm.setCode(tempStr[2]);
				statuscnm.setName(CBSystemConstants.getDeviceStatusName(pdN.getDeviceType(), tempStr[2]));
				CodeNameModel statecnm = new CodeNameModel();
				statecnm.setCode(tempStr[3]);
				statecnm.setName(CBSystemConstants.getDeviceStateName(tempStr[3]));
				Object[] rowData = { l + 1, stionName, pdN, statuscnm, statecnm };
				jtableModel1.addRow(rowData);
			}
			jTable1.setModel(jtableModel1);
			TableColumnModel tcm = jTable1.getColumnModel();
			tcm.getColumn(0).setWidth(60);
			tcm.getColumn(0).setMaxWidth(60);
			tcm.getColumn(1).setWidth(200);
			tcm.getColumn(2).setWidth(200);
		} else {
//			initTable1(this.getLinkDevices());
			initTable1(null);
		}
		jtableModel2.setRowCount(0);
		if (twoList.size() > 0) {
			String[] infStrs = new String[2];
			for (int t = 0; t < twoList.size(); t++) {
				infStrs = twoList.get(t);
				String it_type = infStrs[0];
				if (it_type.equals("0")) {
					it_type = " 执行前";
				}
				if (it_type.equals("1")) {
					it_type = " 执行后";
				}
				CodeNameModel selectItem = null;
				DeviceStateMentManager dsmm = new DeviceStateMentManager();
				List<CodeNameModel> userRules = dsmm.getUserRules();
				for (int i = 0; i < userRules.size(); i++) {
					CodeNameModel cnm = userRules.get(i);
					if (cnm.getCode().equals(infStrs[1])) {
						selectItem = cnm;
						break;
					}
				}
				Object[] rowData = { it_type, selectItem };
				jtableModel2.addRow(rowData);
			}
			jTable2.setModel(jtableModel2);
			TableColumnModel tcm2 = jTable2.getColumnModel();
			tcm2.getColumn(0).setMaxWidth(180);
		} else {
			initTable2();
		}
		
		jtableModel3.setRowCount(0);
		if(threeList.size()>0){
			
			for (int l = 0; l < threeList.size(); l++) {
				String tempStr[]=threeList.get(l);
				PowerDevice pdN = CBSystemConstants.getPowerDevice(tempStr[0], tempStr[1]);
				String stionName = pdN.getPowerStationName();// 设备变电站名称
				CodeNameModel statuscnm = new CodeNameModel();
				statuscnm.setCode(tempStr[2]);
				statuscnm.setName(CBSystemConstants.getDeviceStatusName(pdN.getDeviceType(), tempStr[2]));
				boolean isnegted=(tempStr[3].equals("0")?false:true);
				Object[] rowData = { l + 1, stionName, pdN, statuscnm, isnegted };
				jtableModel3.addRow(rowData);
			}
		}else{
			initTable3();
		}
		
		String stateType = "";
		if (jRadioButton1.isSelected() == true) {
			stateType = "0";
		} else {
			stateType = "1";
		}
		String[] czrwIDs = ccdd.getCzrw(equipId, status, state, stateType);
		jTextArea2.setText(czrwIDs[0]);
		List<String> descs = ccdd.getShuYu(czrwIDs[1]);
		StringBuffer str = new StringBuffer();
		for (int i = 0; i < descs.size(); i++) {
			String tempStr = StringUtils.ObjToString(descs.get(i));
			if (str.length() == 0) {
				str.append(tempStr);
			} else {
				str.append("\r\n" + tempStr);
			}
		}
		this.jTextArea1.setText(str.toString());
	}

	// 表格换行
	private void changeState(int row) {

		String status = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
		String state = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		if (state.equals("") || status.equals("")) {
			ShowMessage.view("请选择设备起始状态或执行动作！");
			return;
		}

		if (jTable1.getColumnModel().getColumn(3).getCellEditor() != null) {
			jTable1.getColumnModel().getColumn(3).setCellEditor(null);
		}
		if (jTable1.getColumnModel().getColumn(4).getCellEditor() != null) {
			jTable1.getColumnModel().getColumn(4).setCellEditor(null);
		}
		if (jTable1.getCellEditor() != null) {
			jTable1.getCellEditor().stopCellEditing();
		}

		PowerDevice pd = (PowerDevice) jtableModel1.getValueAt(row, 2);
		if (pd == null)
			return;

		// 设备状态下拉框
		JComboBox statusCheck = new JComboBox();
		statusCheck.setModel(InitDeviceTypeChockBox.getDeviceStatusCheckBox(pd.getDeviceType()));
		DefaultCellEditor statusEditor = new DefaultCellEditor(statusCheck);
		statusEditor.setClickCountToStart(2);
		jTable1.getColumnModel().getColumn(3).setCellEditor(statusEditor);
		// 设备动作下拉框
		JComboBox stateCheck = new JComboBox();
		stateCheck.setModel(InitDeviceTypeChockBox.getDeviceStateCheckBox(pd.getDeviceType(), "0"));
		DefaultCellEditor stateEditor = new DefaultCellEditor(stateCheck);
		stateEditor.setClickCountToStart(2);
		jTable1.getColumnModel().getColumn(4).setCellEditor(stateEditor);
		jButton11.setText("锁定");
		this.isLocked1 = false;
	}
	private void changeState2(int row) {

		String status = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
		String state = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		if (state.equals("") || status.equals("")) {
			ShowMessage.view("请选择设备起始状态或执行动作！");
			return;
		}

		if (jTable3.getColumnModel().getColumn(3).getCellEditor() != null) {
			jTable3.getColumnModel().getColumn(3).setCellEditor(null);
		}
		if (jTable3.getCellEditor() != null) {
			jTable3.getCellEditor().stopCellEditing();
		}

		PowerDevice pd = (PowerDevice) jtableModel3.getValueAt(row, 2);
		if (pd == null)
			return;

		// 设备状态下拉框
		JComboBox statusCheck = new JComboBox();
		statusCheck.setModel(InitDeviceTypeChockBox.getDeviceStatusCheckBox(pd.getDeviceType()));
		DefaultCellEditor statusEditor = new DefaultCellEditor(statusCheck);
		statusEditor.setClickCountToStart(2);
		jTable3.getColumnModel().getColumn(3).setCellEditor(statusEditor);
		// 设备取反
		JCheckBox isnegated=new JCheckBox();
		jTable3.getColumnModel().getColumn(4).setCellEditor(new DefaultCellEditor(isnegated));
		
		jButton14.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/lock.gif")));
		jButton14.setToolTipText("锁定");
		this.isLocked2 = false;
	}

	// 自定义规则添加
	public void addAction() {
		// 添加
		// jButton1.addActionListener(new ActionListener() {
		// public void actionPerformed(ActionEvent e) {
		// customAcionBak(e);
		// }
		// });
		jButton1.setVisible(false);
		// 删除
		jButton2.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				delAction(evt);
			}
		});
		// 上移
		jButton3.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				upAction(evt);
			}
		});
		// 下移
		jButton4.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				dowAction(evt);
			}
		});

		// 表二新增
		jButton5.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				customAcion(e);
			}
		});
		// 表二删除
		jButton6.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				delTwoAction(evt);
			}
		});
		// 前置设备添加按钮隐藏
		jButton7.setVisible(false);
		// 表三删除
		jButton8.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				WindowUtils.removeTableRow(jTable3);
			}
		});
		//表三上移
		jButton12.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				WindowUtils.moveupTableRow(jTable3);
			}
		});
		//表三下移
		jButton13.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				WindowUtils.movedownTableRow(jTable3);
			}
		});
		//

		// 表二打开
		/*
		 * jButton7.addActionListener(new java.awt.event.ActionListener() {
		 * public void actionPerformed(java.awt.event.ActionEvent evt) {
		 * RuleManager rm = new RuleManager(SystemConstants.getMainFrame(),
		 * true, "0"); rm.setVisible(true); } });
		 */
		// 保存
		jButton9.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				saveAction();
			}
		});
		// 取消
		jButton10.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				CancelSet(e);
			}

		});
		// 锁定关联设备
		jButton11.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				changeSuo1(e);
			}
		});
		// 锁定前置设备
		jButton14.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				changeSuo2(e);
			}
		});
	}

	// 表一添加
	public void customAcionBak(ActionEvent e) {
		jButton11.setText("锁定");
		this.isLocked1 = false;
		this.jtableModel1.addRow(new Object[] { "", "", "", "", "" });
	}

	/**
	 * 删除
	 * 
	 * @param evt
	 */
	protected void delTwoAction(ActionEvent evt) {
		// 从表删除按钮功能
		WindowUtils.removeTableRow(jTable2);
	}

	/**
	 * 删除行功能（自定义规则）
	 * 
	 * @param evt
	 */
	protected void delAction(ActionEvent evt) {
		// 从表删除按钮功能
		WindowUtils.removeTableRow(jTable1);
	}

	/**
	 * 前置表上移
	 * 
	 * @param evt
	 */
	private void upAction(ActionEvent evt) {
		WindowUtils.moveupTableRow(jTable1);
	}

	/**
	 * 前置表下移
	 * 
	 * @param evt
	 */
	private void dowAction(ActionEvent evt) {
		WindowUtils.movedownTableRow(jTable1);
	}

	// 表二新增
	private void customAcion(ActionEvent e) {
		this.jtableModel2.addRow(new Object[] { "", "" });
	}

	/**
	 * 选择关联设备锁定
	 * 
	 * @param e
	 */
	public void changeSuo1(ActionEvent e) {
		if(!this.isLocked2){
			JOptionPane.showMessageDialog(this, "设备已被前置锁定，请先解除前置锁定");
			return;
		}
		String suoStr = jButton11.getToolTipText();
		if ("未锁定".equals(suoStr.trim())) {
			jButton11.setToolTipText("锁定");
			jButton11.setBorder(null);
			jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/lock.gif")));
			this.isLocked1 = false;
		} else {
			ShowMessage.view("请注意保存相关的记录!");
			jButton11.setToolTipText("未锁定");
			jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/unlock.gif")));
			this.isLocked1 = true;
		}
	}
	/**
	 * 选择前置设备锁定
	 * 
	 * @param e
	 */
	public void changeSuo2(ActionEvent e) {
		if(!this.isLocked1){
			JOptionPane.showMessageDialog(this, "设备已被关联锁定，请先解除关联锁定");
			return;
		}
		String suoStr = jButton14.getToolTipText();
		if ("未锁定".equals(suoStr.trim())) {
			jButton14.setToolTipText("锁定");
			jButton14.setBorder(null);
			jButton14.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/lock.gif")));
			this.isLocked2 = false;
		} else {
			ShowMessage.view("请注意保存相关的记录!");
			jButton14.setToolTipText("未锁定");
			jButton14.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/unlock.gif")));
			this.isLocked2 = true;
		}
	}

	// 取消自定义规则
	public void CancelSet(ActionEvent e) {
		CBSystemConstants.svgAddPd = null;
		this.setVisible(false);
	}

	/**
	 * 保存
	 * 
	 * @param evt
	 */
	public void saveAction() {

		String status = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
		String state = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		if (state.equals("") || status.equals("")) {
			ShowMessage.view("请选择设备起始状态或执行动作！");
			return;
		}

		int countRows = jTable1.getRowCount();
		PowerDevice tempDev = null;
		CodeNameModel statusCnm = null;
		CodeNameModel stateCnm = null;
		List<String[]> cblist = new ArrayList<String[]>();
		for (int j = 0; j < countRows; j++) {
			if (jTable1.getValueAt(j, 2) == null || jTable1.getValueAt(j, 3) == null || jTable1.getValueAt(j, 4) == null) {
				continue;
			}
			if (jTable1.getValueAt(j, 2) == "" || jTable1.getValueAt(j, 3) == "" || jTable1.getValueAt(j, 4) == "") {
				continue;
			}
			tempDev = (PowerDevice) jTable1.getValueAt(j, 2);
			statusCnm = (CodeNameModel) jTable1.getValueAt(j, 3);
			stateCnm = (CodeNameModel) jTable1.getValueAt(j, 4);
			String[] tempObjs = new String[] { tempDev.getPowerDeviceID(), tempDev.getPowerStationID(), statusCnm.getCode(), stateCnm.getCode() };
			cblist.add(tempObjs);
		}
		int countRows2 = jTable2.getRowCount();
		List<String[]> cuslist = new ArrayList<String[]>();
		String popery = "";
		String custom = "";
		for (int r = 0; r < countRows2; r++) {
			popery = (String) jTable2.getValueAt(r, 0);
			custom = ((CodeNameModel) jTable2.getValueAt(r, 1)).getCode();
			if ("".equals(popery.trim()) || "".equals(custom.trim())) {
				continue;
			}
			if ("执行前".equals(popery.trim())) {
				popery = "0";
			} else {
				popery = "1";
			}
			String[] tempCus = new String[] { popery, custom };
			cuslist.add(tempCus);
		}
		int countRows3=jTable3.getRowCount();
		List<String[]> preCondition = new ArrayList<String[]>();
		String isnegated;
		for (int j = 0; j < countRows3; j++) {
			if (jTable3.getValueAt(j, 2) == null || jTable3.getValueAt(j, 3) == null) {
				continue;
			}
			if (jTable3.getValueAt(j, 2) == "" || jTable3.getValueAt(j, 3) == "") {
				continue;
			}
			tempDev = (PowerDevice) jTable3.getValueAt(j, 2);
			statusCnm = (CodeNameModel) jTable3.getValueAt(j, 3);
			isnegated=jTable3.getValueAt(j, 4).equals(false)?"0":"1";
			String[] tempObjs = new String[] { tempDev.getPowerDeviceID(), tempDev.getPowerStationID(), statusCnm.getCode(), isnegated};
			preCondition.add(tempObjs);
		}
		ccdd.insertCustom(this.choosePd.getPowerDeviceID(), status, state, cblist, cuslist,preCondition);

		String stateType = "";
		if (jRadioButton1.isSelected() == true) {
			stateType = "0";
		} else {
			stateType = "1";
		}
		String czrw = jTextArea2.getText();// 操作任务
		String conter = jTextArea1.getText();
		String[] rowStrs = null;
		if (conter.indexOf("\r\n") >= 0) {
			rowStrs = conter.split("\r\n");
		} else if (conter.indexOf("\n") >= 0) {
			rowStrs = conter.split("\n");
		} else {
			rowStrs = new String[] { conter };
		}
		ccdd.insertShuYu(this.choosePd.getPowerDeviceID(), status, state, stateType, czrw, rowStrs);
		
		
		JOptionPane.showMessageDialog(this, "保存成功!", CBSystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
	}

	// 搜索开关和直接接地刀闸
	@SuppressWarnings("unchecked")
	public List<PowerDevice> getLinkDevices() {

		List<PowerDevice> linkDevs = new ArrayList<PowerDevice>(); // 搜索设备集合
		if (this.choosePd == null)
			return linkDevs;

		Map<String, Object> inMap = new HashMap<String, Object>();
		Map<String, Object> outMap = new HashMap<String, Object>();
		CommonSearch cs = new CommonSearch();
		List<PowerDevice> searchDevs = null;
		if (choosePd.getDeviceType().equals(SystemConstants.InOutLine)) {
			Map<PowerDevice, String> stationlines = QueryDeviceDao.getPowersLineByLine(choosePd);
			List<PowerDevice> trans = new ArrayList<PowerDevice>();
			PowerDevice dev = null; // 变电站对象
			for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
				dev = (PowerDevice) iterator.next();
				trans.add(dev);
			}
			// 搜索开关
			inMap.put("oprSrcDevice", choosePd);
			inMap.put("tagDevType", SystemConstants.Switch);
			cs.execute(inMap, outMap);
			searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
			linkDevs.addAll(searchDevs);
			// 搜索电抗器
			inMap.put("oprSrcDevice", choosePd);
			inMap.put("tagDevType", SystemConstants.ElecShock);
			cs.execute(inMap, outMap);
			inMap.clear();
			searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
			linkDevs.addAll(searchDevs);

		} else {
			if (!choosePd.getDeviceType().equals(SystemConstants.Switch)) {
				// 搜索开关
				inMap.put("oprSrcDevice", choosePd);
				inMap.put("tagDevType", SystemConstants.Switch);
				cs.execute(inMap, outMap);
				searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
				linkDevs.addAll(searchDevs);
			} else {
				// 搜索刀闸
				inMap.put("oprSrcDevice", choosePd);
				inMap.put("tagDevType", SystemConstants.SwitchSeparate);
				cs.execute(inMap, outMap);
				inMap.clear();
				searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
				linkDevs.addAll(searchDevs);
			}
		}
		// 搜索直接连接接地刀闸
		inMap.put("oprSrcDevice", choosePd);
		inMap.put("tagDevType", SystemConstants.SwitchFlowGroundLine);// 目标直接接地刀闸
		inMap.put("isSearchDirectDevice", true);
		cs.execute(inMap, outMap);
		searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
		linkDevs.addAll(searchDevs);
		return linkDevs;
	}

	// 初始化执行设备表
	@SuppressWarnings("serial")
	public void initTable1(List<PowerDevice> pdLists) {
		jtableModel1 = new DefaultTableModel(null, new String[] { "序号", "厂站", "设备", "初始状态", "执行状态" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				if (columnIndex == 3 || columnIndex == 4) {
					return true;
				} else {
					return false;
				}
			}
		};
		if (pdLists != null) {
			PowerDevice tempDev = null;
			String stationName = ""; // 变电站名称
			for (int i = 0; i < pdLists.size(); i++) {
				tempDev = pdLists.get(i);
				stationName = tempDev.getPowerStationName();// 所在变电站
				CodeNameModel cnm = new CodeNameModel();
				cnm.setCode(tempDev.getDeviceStatus());
				cnm.setName(CBSystemConstants.getDeviceStatusName(tempDev.getDeviceType(), tempDev.getDeviceStatus()));
				Object[] rowData = { (i + 1), stationName, tempDev, cnm, "" };
				jtableModel1.addRow(rowData);
			}
		}
		jTable1.setModel(jtableModel1);
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);// 列名居中
		sjp.getDefaultRenderer(jTable1.getColumnClass(1), jTable1);

		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMaxWidth(40);
		tcm.getColumn(1).setMaxWidth(100);
		tcm.getColumn(2).setMinWidth(130);
	}

	// 初始化表格接口表
	@SuppressWarnings("serial")
	public void initTable2() {
		jtableModel2 = new DefaultTableModel(null, new String[] { "属性", "自定义规则" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return true;
			}
		};
		jTable2.setModel(jtableModel2);
		TableColumnModel tcm = jTable2.getColumnModel();
		tcm.getColumn(0).setMaxWidth(180);

		// 构造隐藏下拉
		DefaultComboBoxModel statusModel = new DefaultComboBoxModel();
		statusModel.addElement(" 执行前");
		statusModel.addElement(" 执行后");
		JComboBox statusCheck = new JComboBox();
		statusCheck.setModel(statusModel);
		DefaultCellEditor statusEditor = new DefaultCellEditor(statusCheck);
		statusEditor.setClickCountToStart(0);
		jTable2.getColumnModel().getColumn(0).setCellEditor(statusEditor);

		DefaultComboBoxModel ruleModel = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm = new DeviceStateMentManager();
		List<CodeNameModel> userRules = dsmm.getUserRules();
		for (int i = 0; i < userRules.size(); i++) {
			CodeNameModel cnm = userRules.get(i);
			ruleModel.addElement(cnm);
		}
		JComboBox ruleCheck = new JComboBox();
		ruleCheck.setModel(ruleModel);
		DefaultCellEditor ruleEditor = new DefaultCellEditor(ruleCheck);
		ruleEditor.setClickCountToStart(0);
		jTable2.getColumnModel().getColumn(1).setCellEditor(ruleEditor);
	}

	public void initTable3() {
		jtableModel3 = new DefaultTableModel(null, new String[] { "序号", "厂站", "设备", "状态", "取反" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				if (columnIndex == 3 || columnIndex == 4) {
					return true;
				} else {
					return false;
				}
			}
		};
		jTable3.setModel(jtableModel3);
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable3);// 列名居中
		sjp.getDefaultRenderer(jTable1.getColumnClass(1), jTable3);
       
		TableColumnModel tcm = jTable3.getColumnModel();
		tcm.getColumn(0).setMaxWidth(40);
		tcm.getColumn(1).setMaxWidth(100);
		tcm.getColumn(2).setMinWidth(130);
		DefaultTableCellRenderer negatedRender=new DefaultTableCellRenderer(){
			@Override
			public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
				boolean negated=(Boolean) jTable3.getValueAt(row, column);
				JCheckBox check=new JCheckBox();
				if(negated){
					  check.setSelected(true);
					}else{
					  check.setSelected(false);}
				return check;
			}
		};
		jTable3.getColumnModel().getColumn(4).setCellRenderer(negatedRender);
	}

	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		buttonGroup1 = new javax.swing.ButtonGroup();
		jPanel1 = new javax.swing.JPanel();
		jLabel1 = new javax.swing.JLabel();
		jTextField1 = new javax.swing.JTextField();
		jLabel2 = new javax.swing.JLabel();
		jComboBox1 = new JComboBox();
		jLabel3 = new javax.swing.JLabel();
		jComboBox2 = new JComboBox();
		jTabbedPane1 = new javax.swing.JTabbedPane();
		jPanel2 = new javax.swing.JPanel();
		jPanel4 = new javax.swing.JPanel();
		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jButton3 = new javax.swing.JButton();
		jButton4 = new javax.swing.JButton();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new JTable() {
			public void valueChanged(ListSelectionEvent e) {
				super.valueChanged(e);
				int row = getSelectedRow();
				if (row == -1) {
					return;
				}
				if (!this.getValueAt(row, 0).equals("")) {
					changeState(row);
				}
			}
		};
		jButton11 = new javax.swing.JButton();
		jPanel5 = new javax.swing.JPanel();
		jScrollPane2 = new javax.swing.JScrollPane();
		jTable2 = new JTable();
		jButton5 = new javax.swing.JButton();
		jButton6 = new javax.swing.JButton();
		jPanel8 = new javax.swing.JPanel();
		jButton7 = new javax.swing.JButton();
		jButton8 = new javax.swing.JButton();
		jButton12 = new javax.swing.JButton();
		jButton13 = new javax.swing.JButton();
		jButton14 = new javax.swing.JButton();
		jScrollPane4 = new javax.swing.JScrollPane();
		jTable3 = new JTable() {
			public void valueChanged(ListSelectionEvent e) {
				super.valueChanged(e);
				int row = getSelectedRow();
				if (row == -1) {
					return;
				}
				if (!this.getValueAt(row, 0).equals("")) {
					changeState2(row);
				}
			}
		};
		jPanel3 = new javax.swing.JPanel();
		jPanel6 = new javax.swing.JPanel();
		jRadioButton2 = new javax.swing.JRadioButton();
		jRadioButton1 = new javax.swing.JRadioButton();
		jPanel7 = new javax.swing.JPanel();
		jLabel4 = new javax.swing.JLabel();
		jTextArea2 = new javax.swing.JTextArea();
		jLabel5 = new javax.swing.JLabel();
		jScrollPane3 = new javax.swing.JScrollPane();
		jTextArea1 = new javax.swing.JTextArea();
		jButton9 = new javax.swing.JButton();
		jButton10 = new javax.swing.JButton();
		
		/*
		jPanel1.setBackground(new java.awt.Color(244, 243, 243));
		jPanel2.setBackground(new java.awt.Color(244, 243, 243));
		jPanel3.setBackground(new java.awt.Color(244, 243, 243));
		jPanel4.setBackground(new java.awt.Color(244, 243, 243));
		jPanel5.setBackground(new java.awt.Color(244, 243, 243));
		jPanel6.setBackground(new java.awt.Color(244, 243, 243));
		jPanel7.setBackground(new java.awt.Color(244, 243, 243));
		jPanel8.setBackground(new java.awt.Color(244, 243, 243));
		jScrollPane1.getViewport().setBackground(new java.awt.Color(244, 243, 243));
		jScrollPane2.getViewport().setBackground(new java.awt.Color(244, 243, 243));
		jScrollPane3.getViewport().setBackground(new java.awt.Color(244, 243, 243));
		jScrollPane4.getViewport().setBackground(new java.awt.Color(244, 243, 243));
		jTable1.setBackground(new java.awt.Color(244, 243, 243));
		jTable2.setBackground(new java.awt.Color(244, 243, 243));
		jTable3.setBackground(new java.awt.Color(244, 243, 243));
		*/

		jLabel1.setText("  设备：");

		jTextField1.setEditable(false);
		jTextField1.setFont(new java.awt.Font("微软雅黑", 1, 14));

		jLabel2.setText("状态：");

		jComboBox1.addItemListener(new java.awt.event.ItemListener() {
			public void itemStateChanged(ItemEvent evt) {
				CustomCodex.this.itemStateChanged(evt);
			}
		});

		jLabel3.setText(" 操作：");

		jComboBox2.addItemListener(new java.awt.event.ItemListener() {
			public void itemStateChanged(ItemEvent evt) {
				CustomCodex.this.itemStateChanged(evt);
			}
		});

		org.jdesktop.layout.GroupLayout jPanel1Layout = new org.jdesktop.layout.GroupLayout(jPanel1);
		jPanel1.setLayout(jPanel1Layout);
		jPanel1Layout.setHorizontalGroup(jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(
				jPanel1Layout.createSequentialGroup().add(jLabel1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 54, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jTextField1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 121, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jLabel2).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jComboBox1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 61, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jLabel3, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 48, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addPreferredGap(
						org.jdesktop.layout.LayoutStyle.RELATED).add(jComboBox2, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 78, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addContainerGap(23, Short.MAX_VALUE)));
		jPanel1Layout.setVerticalGroup(jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(
				jPanel1Layout.createSequentialGroup().addContainerGap().add(jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE).add(jLabel1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 25, Short.MAX_VALUE).add(jTextField1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).add(jLabel2).add(jComboBox1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).add(jLabel3).add(jComboBox2, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)).addContainerGap()));

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
		jButton1.setToolTipText("新增");
		jButton1.setBorder(null);

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton2.setToolTipText("删除");
		jButton2.setBorder(null);

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
		jButton3.setToolTipText("上移");
		jButton3.setBorder(null);

		jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
		jButton4.setToolTipText("下移");
		jButton4.setBorder(null);

		jScrollPane1.setBorder(javax.swing.BorderFactory.createTitledBorder("关联设备执行"));
		jScrollPane1.setViewportView(jTable1);

		jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/unlock.gif"))); // NOI18N
		jButton11.setToolTipText("未锁定");
		jButton11.setBorder(null);
		jButton11.setBorderPainted(false);
		jButton11.setFocusPainted(false);

		org.jdesktop.layout.GroupLayout jPanel4Layout = new org.jdesktop.layout.GroupLayout(jPanel4);
		jPanel4.setLayout(jPanel4Layout);
		jPanel4Layout.setHorizontalGroup(jPanel4Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(org.jdesktop.layout.GroupLayout.TRAILING, jPanel4Layout.createSequentialGroup().addContainerGap().add(jButton11).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED, 329, Short.MAX_VALUE).add(jButton1).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jButton2).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jButton3).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jButton4).addContainerGap()).add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 446, Short.MAX_VALUE));
		jPanel4Layout.setVerticalGroup(jPanel4Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(org.jdesktop.layout.GroupLayout.TRAILING, jPanel4Layout.createSequentialGroup().addContainerGap().add(jPanel4Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE).add(jButton4).add(jButton3).add(jButton2).add(jButton1).add(jButton11, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 17, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jScrollPane1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 122, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).add(131, 131, 131)));

		jScrollPane2.setBorder(javax.swing.BorderFactory.createTitledBorder("执行接口"));
		jScrollPane2.setViewportView(jTable2);

		jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
		jButton5.setToolTipText("新增");
		jButton5.setBorder(null);

		jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton6.setToolTipText("删除 ");
		jButton6.setBorder(null);

		org.jdesktop.layout.GroupLayout jPanel5Layout = new org.jdesktop.layout.GroupLayout(jPanel5);
		jPanel5.setLayout(jPanel5Layout);
		jPanel5Layout.setHorizontalGroup(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jScrollPane2, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 446, Short.MAX_VALUE).add(org.jdesktop.layout.GroupLayout.TRAILING, jPanel5Layout.createSequentialGroup().addContainerGap(391, Short.MAX_VALUE).add(jButton5).addPreferredGap(org.jdesktop.layout.LayoutStyle.UNRELATED).add(jButton6).add(11, 11, 11)));
		jPanel5Layout.setVerticalGroup(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel5Layout.createSequentialGroup().add(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jButton5).add(jButton6)).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jScrollPane2, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 148, Short.MAX_VALUE)));

		jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
		jButton7.setToolTipText("新增");
		jButton7.setBorder(null);

		jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton8.setToolTipText("删除");
		jButton8.setBorder(null);

		jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
		jButton12.setToolTipText("上移");
		jButton12.setBorder(null);

		jButton13.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
		jButton13.setToolTipText("下移");
		jButton13.setBorder(null);

		jButton14.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/unlock.gif"))); // NOI18N
		jButton14.setToolTipText("未锁定");
		jButton14.setBorder(null);
		jButton14.setBorderPainted(false);
		jButton14.setFocusPainted(false);

		jScrollPane4.setBorder(javax.swing.BorderFactory.createTitledBorder("前置条件"));
		jScrollPane4.setViewportView(jTable3);

		org.jdesktop.layout.GroupLayout jPanel8Layout = new org.jdesktop.layout.GroupLayout(jPanel8);
		jPanel8.setLayout(jPanel8Layout);
		jPanel8Layout.setHorizontalGroup(jPanel8Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(org.jdesktop.layout.GroupLayout.TRAILING, jPanel8Layout.createSequentialGroup().addContainerGap().add(jButton14).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED, 329, Short.MAX_VALUE).add(jButton7).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jButton8).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jButton12).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jButton13).addContainerGap()).add(jScrollPane4, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 446, Short.MAX_VALUE));
		jPanel8Layout.setVerticalGroup(jPanel8Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(org.jdesktop.layout.GroupLayout.TRAILING, jPanel8Layout.createSequentialGroup().addContainerGap().add(jPanel8Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE).add(jButton13).add(jButton12).add(jButton8).add(jButton7).add(jButton14, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 17, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jScrollPane4, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 119, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).add(134, 134, 134)));

		org.jdesktop.layout.GroupLayout jPanel2Layout = new org.jdesktop.layout.GroupLayout(jPanel2);
		jPanel2.setLayout(jPanel2Layout);
		jPanel2Layout.setHorizontalGroup(jPanel2Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel4, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE).add(jPanel5, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE).add(jPanel8, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE));
		jPanel2Layout.setVerticalGroup(jPanel2Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel2Layout.createSequentialGroup().add(jPanel4, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 156, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jPanel8, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 157, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jPanel5, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)));

		jTabbedPane1.addTab("自定义规则", jPanel2);

		buttonGroup1.add(jRadioButton2);
		jRadioButton2.setText("元件令");
		jRadioButton2.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				CustomCodex.this.actionPerformed(evt);
			}
		});

		buttonGroup1.add(jRadioButton1);
		jRadioButton1.setSelected(true);
		jRadioButton1.setText("状态令");
		jRadioButton1.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				CustomCodex.this.actionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout jPanel6Layout = new org.jdesktop.layout.GroupLayout(jPanel6);
		jPanel6.setLayout(jPanel6Layout);
		jPanel6Layout.setHorizontalGroup(jPanel6Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel6Layout.createSequentialGroup().add(81, 81, 81).add(jRadioButton1).add(18, 18, 18).add(jRadioButton2).addContainerGap(225, Short.MAX_VALUE)));
		jPanel6Layout.setVerticalGroup(jPanel6Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel6Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE).add(jRadioButton1).add(jRadioButton2)));

		jLabel4.setText(" 操作任务：");

		jTextArea2.setLineWrap(true);
		jTextArea2.setRows(5);

		jLabel5.setText(" 操作指令：");

		jTextArea1.setLineWrap(true);
		jTextArea1.setRows(5);
		jScrollPane3.setViewportView(jTextArea1);

		org.jdesktop.layout.GroupLayout jPanel7Layout = new org.jdesktop.layout.GroupLayout(jPanel7);
		jPanel7.setLayout(jPanel7Layout);
		jPanel7Layout.setHorizontalGroup(jPanel7Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel7Layout.createSequentialGroup().add(jPanel7Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING, false).add(jLabel4, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE).add(jLabel5, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jPanel7Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jScrollPane3).add(jTextArea2)).addContainerGap()));
		jPanel7Layout.setVerticalGroup(jPanel7Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel7Layout.createSequentialGroup().addContainerGap().add(jPanel7Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jLabel4).add(jTextArea2, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 59, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jPanel7Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel7Layout.createSequentialGroup().add(jLabel5, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 31, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addContainerGap()).add(jScrollPane3, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 382, Short.MAX_VALUE))));

		org.jdesktop.layout.GroupLayout jPanel3Layout = new org.jdesktop.layout.GroupLayout(jPanel3);
		jPanel3.setLayout(jPanel3Layout);
		jPanel3Layout.setHorizontalGroup(jPanel3Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel7, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE).add(jPanel6, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE));
		jPanel3Layout.setVerticalGroup(jPanel3Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(jPanel3Layout.createSequentialGroup().addContainerGap().add(jPanel6, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jPanel7, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)));

		jTabbedPane1.addTab("自定义术语", jPanel3);

		jButton9.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
		jButton9.setText("保存");
		jButton9.setToolTipText("保存");
		jButton9.setMargin(new java.awt.Insets(1,1,1,1));

		jButton10.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
		jButton10.setText("取消");
		jButton10.setToolTipText("取消");
		jButton10.setMargin(new java.awt.Insets(1,1,1,1));

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(this);
		this.setLayout(layout);
		layout.setHorizontalGroup(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(layout.createSequentialGroup().add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(org.jdesktop.layout.GroupLayout.TRAILING, layout.createSequentialGroup().addContainerGap().add(jButton9).add(18, 18, 18).add(jButton10)).add(jPanel1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)).addContainerGap()).add(jTabbedPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 451, Short.MAX_VALUE));
		layout.setVerticalGroup(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING).add(layout.createSequentialGroup().add(jPanel1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(jTabbedPane1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 525, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED).add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.TRAILING).add(jButton10).add(jButton9)).addContainerGap()));
	}// </editor-fold>

	// Variables declaration - do not modify
	private javax.swing.ButtonGroup buttonGroup1;
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton10;
	private javax.swing.JButton jButton11;
	private javax.swing.JButton jButton12;
	private javax.swing.JButton jButton13;
	private javax.swing.JButton jButton14;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JButton jButton4;
	private javax.swing.JButton jButton5;
	private javax.swing.JButton jButton6;
	private javax.swing.JButton jButton7;
	private javax.swing.JButton jButton8;
	private javax.swing.JButton jButton9;
	private JComboBox jComboBox1;
	private JComboBox jComboBox2;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JLabel jLabel3;
	private javax.swing.JLabel jLabel4;
	private javax.swing.JLabel jLabel5;
	private javax.swing.JPanel jPanel1;
	private javax.swing.JPanel jPanel2;
	private javax.swing.JPanel jPanel3;
	private javax.swing.JPanel jPanel4;
	private javax.swing.JPanel jPanel5;
	private javax.swing.JPanel jPanel6;
	private javax.swing.JPanel jPanel7;
	private javax.swing.JPanel jPanel8;
	private javax.swing.JRadioButton jRadioButton1;
	private javax.swing.JRadioButton jRadioButton2;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JScrollPane jScrollPane3;
	private javax.swing.JScrollPane jScrollPane4;
	private javax.swing.JTabbedPane jTabbedPane1;
	private JTable jTable1;
	private JTable jTable2;
	private JTable jTable3;
	private javax.swing.JTextArea jTextArea1;
	private javax.swing.JTextArea jTextArea2;
	private javax.swing.JTextField jTextField1;
	// End of variables declaration
}
