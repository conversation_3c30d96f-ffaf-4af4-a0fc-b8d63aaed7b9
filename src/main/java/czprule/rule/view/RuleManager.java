/*
 * ValiableChooseDialog_.java
 *
 * Created on __DATE__, __TIME__
 */

package czprule.rule.view;

import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.CodeNameModel;
import czprule.model.TreeModel;
import czprule.rule.dao.RuleManagerDao;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

import javax.swing.*;
import javax.swing.GroupLayout.Alignment;
import javax.swing.LayoutStyle.ComponentPlacement;
import javax.swing.event.ListSelectionEvent;
import javax.swing.table.DefaultTableModel;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class RuleManager extends javax.swing.JDialog {
	private DefaultTableModel jtableModel;
	private String ruletype = ""; //0:前提条件  1：执行动作
	private Object[] returnValue = new Object[4];
	private javax.swing.JPopupMenu pop;//hx修改

	/** Creates new form ValiableChooseDialog_ 
	 * @wbp.parser.constructor*/
	public RuleManager(java.awt.Frame parent, boolean modal, String ruletype) {
		super(parent, modal);
		this.ruletype = ruletype;
		initComponents();
		this.initTree();
		this.initTable();
		this.setLocationCenter();
	}

	public RuleManager(javax.swing.JDialog parent, boolean modal,
			String ruletype) {
		super(parent, modal);
		this.ruletype = ruletype;
		initComponents();
		this.initTree();
		this.setLocationCenter();
	}


	public Object[] getSelectRuleClass() {
		this.setVisible(true);
		return returnValue;
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jPanel2 = new javax.swing.JPanel();
		jPanel1 = new javax.swing.JPanel();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTree1 = new javax.swing.JTree();
		jScrollPane2 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable() {
			public void valueChanged(ListSelectionEvent e) {
				super.valueChanged(e);
				int row = getSelectedRow();
				if (row == -1) {
					return;
				}
				if (!this.getValueAt(row, 0).equals("")) {
					changeState(row);
				}
			}
		};
		jButton2 = new javax.swing.JButton();
		jButton3 = new javax.swing.JButton();
		jButton1 = new javax.swing.JButton();
		jButton4 = new javax.swing.JButton();//新增查看备注功能
		org.jdesktop.layout.GroupLayout jPanel2Layout = new org.jdesktop.layout.GroupLayout(
				jPanel2);
		jPanel2.setLayout(jPanel2Layout);
		jPanel2Layout.setHorizontalGroup(jPanel2Layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(0, 100,
				Short.MAX_VALUE));
		jPanel2Layout.setVerticalGroup(jPanel2Layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(0, 100,
				Short.MAX_VALUE));

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jTree1.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				jTree1MouseClicked(evt);
			}
		});
		jScrollPane1.setViewportView(jTree1);

		jTable1.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				jTable1MouseClicked(evt);
			}
		});
		jScrollPane2.setViewportView(jTable1);

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton2.setToolTipText("\u5220\u9664");
		jButton2.setBorder(null);
		jButton2.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/add.png"))); // NOI18N
		jButton3.setToolTipText("\u65b0\u589e");
		jButton3.setBorder(null);
		jButton3.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
		jButton1.setToolTipText("\u786e\u5b9a");
		jButton1.setBorder(null);
		jButton1.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});
		
		jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/much.png"))); // NOI18N
		jButton4.setToolTipText("帮助");
		jButton4.setBorder(null);
		jButton4.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				jButton4ActionPerformed(evt);
			}
		});

		GroupLayout jPanel1Layout = new GroupLayout(jPanel1);
		jPanel1Layout.setHorizontalGroup(
			jPanel1Layout.createParallelGroup(Alignment.TRAILING)
				.addGroup(jPanel1Layout.createSequentialGroup()
					.addComponent(jScrollPane1, GroupLayout.PREFERRED_SIZE, 149, GroupLayout.PREFERRED_SIZE)
					.addPreferredGap(ComponentPlacement.RELATED)
					.addGroup(jPanel1Layout.createParallelGroup(Alignment.TRAILING)
						.addGroup(jPanel1Layout.createSequentialGroup()
							.addComponent(jButton4)
							.addPreferredGap(ComponentPlacement.UNRELATED)
							.addComponent(jButton1)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton3)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton2)
							.addContainerGap())
						.addComponent(jScrollPane2, GroupLayout.DEFAULT_SIZE, 907, Short.MAX_VALUE)))
		);
		jPanel1Layout.setVerticalGroup(
			jPanel1Layout.createParallelGroup(Alignment.LEADING)
				.addGroup(jPanel1Layout.createSequentialGroup()
					.addContainerGap()
					.addGroup(jPanel1Layout.createParallelGroup(Alignment.LEADING)
						.addComponent(jScrollPane1, Alignment.TRAILING, GroupLayout.DEFAULT_SIZE, 493, Short.MAX_VALUE)
						.addGroup(jPanel1Layout.createSequentialGroup()
							.addGap(6)
							.addGroup(jPanel1Layout.createParallelGroup(Alignment.TRAILING)
								.addGroup(jPanel1Layout.createSequentialGroup()
									.addGroup(jPanel1Layout.createParallelGroup(Alignment.BASELINE)
										.addComponent(jButton4)
										.addComponent(jButton2)
										.addComponent(jButton3)
										.addComponent(jButton1)
											)
									.addPreferredGap(ComponentPlacement.RELATED)))
							.addComponent(jScrollPane2, GroupLayout.DEFAULT_SIZE, 470, Short.MAX_VALUE))))
		);
		jPanel1.setLayout(jPanel1Layout);

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jPanel1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE));
		layout.setVerticalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jPanel1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	private void jButton1ActionPerformed(ActionEvent evt) {
		// TODO add your handling code here:

		int[] selectedRows = jTable1.getSelectedRows();
		//没选择任何行，直接返回
		if (selectedRows.length == 0) {
			JOptionPane.showMessageDialog(this, "你没有选择规则项！", "操作票提示框",
					JOptionPane.WARNING_MESSAGE);
			return;
		}
		if (selectedRows.length > 1) {
			JOptionPane.showMessageDialog(this, "不能一次选择多条规则！", "操作票提示框",
					JOptionPane.WARNING_MESSAGE);
			return;
		}
		CodeNameModel cnm = (CodeNameModel) jtableModel.getValueAt(
				jTable1.getSelectedRow(), 1);
		if (cnm == null)
			return;
		String wordid = cnm.getCode();
		RuleManagerDao rmd = new RuleManagerDao();
		RuleBaseMode rbm = rmd.getRuleBaseModel(wordid);
		String state = rbm.getEndState();
		CodeNameModel cnmDevRunType = null;
		CodeNameModel cnmStatus = null;
		CodeNameModel cnmState = null;
		String beginStatus ="";
		String devstate ="";

		Object obj = this.jtableModel.getValueAt(this.jTable1.getSelectedRow(),
				3);
		if (obj != null && !"".equals(obj)) {
			cnmDevRunType = (CodeNameModel) this.jtableModel.getValueAt(
					this.jTable1.getSelectedRow(), 3);
		}
		obj = this.jtableModel.getValueAt(this.jTable1.getSelectedRow(), 4);
		if (obj != null && !"".equals(obj)) {
			cnmStatus = (CodeNameModel) this.jtableModel.getValueAt(
					this.jTable1.getSelectedRow(), 4);
		}
		obj = this.jtableModel.getValueAt(this.jTable1.getSelectedRow(), 5);
		if (obj != null && !"".equals(obj)) {
			cnmState = (CodeNameModel) this.jtableModel.getValueAt(
					this.jTable1.getSelectedRow(), 5);
		}

		if ((state != null && !state.equals("") && cnmState == null)) {
			JOptionPane.showMessageDialog(this, "请选择‘设备执行动作’！", "操作票提示框",
					JOptionPane.WARNING_MESSAGE);
			return;
		}
		
		cnm = (CodeNameModel) this.jtableModel.getValueAt(
				this.jTable1.getSelectedRow(), 1);
		String tranType = StringUtils.ObjToString(this.jtableModel
				.getValueAt(this.jTable1.getSelectedRow(), 6));
		this.returnValue = new Object[] { cnm, cnmDevRunType, cnmStatus,
				cnmState, tranType };
		this.setVisible(false);
		this.dispose();
	}

	/**
	 * 选择规则项目，关闭规则库窗口，返回结果集合
	 */
	private void jTable1MouseClicked(java.awt.event.MouseEvent evt) {
		// TODO add your handling code here:
		if (evt.getClickCount() == 2) {

			CodeNameModel cnm = (CodeNameModel) jtableModel.getValueAt(
					jTable1.getSelectedRow(), 1);
			if (cnm == null)
				return;
			String wordid = cnm.getCode();
			RuleManagerDao rmd = new RuleManagerDao();
			RuleBaseMode rbm = rmd.getRuleBaseModel(wordid);
			String state = rbm.getEndState();
			CodeNameModel cnmDevRunType = null;
			CodeNameModel cnmStatus = null;
			CodeNameModel cnmState = null;

			Object obj = this.jtableModel.getValueAt(
					this.jTable1.getSelectedRow(), 3);
			if (obj != null && !"".equals(obj)) {
				cnmDevRunType = (CodeNameModel) this.jtableModel.getValueAt(
						this.jTable1.getSelectedRow(), 3);
			}
			obj = this.jtableModel.getValueAt(this.jTable1.getSelectedRow(), 4);
			if (obj != null && !"".equals(obj)) {
				cnmStatus = (CodeNameModel) this.jtableModel.getValueAt(
						this.jTable1.getSelectedRow(), 4);
			}
			obj = this.jtableModel.getValueAt(this.jTable1.getSelectedRow(), 5);
			if (obj != null && !"".equals(obj)) {
				cnmState = (CodeNameModel) this.jtableModel.getValueAt(
						this.jTable1.getSelectedRow(), 5);
			}

			if ((state != null && !state.equals("") && cnmState == null)) {
				JOptionPane.showMessageDialog(this, "请选择‘设备执行动作’！", "操作票提示框",
						JOptionPane.WARNING_MESSAGE);
				return;
			}
			cnm = (CodeNameModel) this.jtableModel.getValueAt(
					this.jTable1.getSelectedRow(), 1);
			String tranType = StringUtils.ObjToString(this.jtableModel
					.getValueAt(this.jTable1.getSelectedRow(), 6));
			this.returnValue = new Object[] { cnm, cnmDevRunType, cnmStatus,
					cnmState, tranType };
			this.setVisible(false);
			this.dispose();

		}
	}

	/**
	 * 删除规则库项目
	 * @param evt
	 */
	private void jButton2ActionPerformed(ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectedRows = jTable1.getSelectedRows();
		//没选择任何行，直接返回
		if (selectedRows.length == 0) {
			JOptionPane.showMessageDialog(this, "你没有选择要删除的行！", "操作票提示框",
					JOptionPane.WARNING_MESSAGE);
			return;
		}
		else {
			if(JOptionPane.showConfirmDialog(this, "是否要删除选中的规则？", "操作票提示框", JOptionPane.OK_CANCEL_OPTION) != JOptionPane.OK_OPTION)
				return;
		}
		RuleManagerDao rmd = new RuleManagerDao();
		CodeNameModel cnm = null;
		for (int i = 0; i < selectedRows.length; i++) {
			cnm = (CodeNameModel) this.jtableModel.getValueAt(selectedRows[i],
					1);
			rmd.delWordBean(cnm.getCode());
		}
		this.initTable();
	}

	/**
	 * 新增规则项目
	 * @param evt
	 */
	private void jButton3ActionPerformed(ActionEvent evt) {
		// TODO add your handling code here:
		TreePath path = jTree1.getSelectionPath();
		if (path == null) {
			return;
		}
		//设置当前选中的常用语类别
		DefaultMutableTreeNode node = (DefaultMutableTreeNode) path
				.getLastPathComponent();
		if (!node.isLeaf()){
			JOptionPane.showMessageDialog(this, "左侧目录树当前选择非叶子节点，请选择叶节点添加", "操作票提示框",
					JOptionPane.WARNING_MESSAGE);
			return;
		}
			
		TreeModel cnm = (TreeModel) node.getUserObject();
		RuleAddItem rai = new RuleAddItem(this, true, cnm);
		rai.setVisible(true);
		this.initTable();
	}
	
	/**
	 * 查看规则备注
	 * @param evt
	 */
	private void jButton4ActionPerformed(ActionEvent evt) {
		
		int[] selectedRows = jTable1.getSelectedRows();
		//没选择任何行，直接返回
		if (selectedRows.length == 0) {
			JOptionPane.showMessageDialog(this, "你没有选择要查看的规则！", "操作票提示框",
					JOptionPane.WARNING_MESSAGE);
			return;
		}
		RuleManagerDao rmd = new RuleManagerDao();
		CodeNameModel cnm = null;
		for (int i = 0; i < selectedRows.length; i++) {
			cnm = (CodeNameModel) this.jtableModel.getValueAt(selectedRows[0],
					1);
			String wordId=cnm.getCode();
			String result=rmd.queryWordBean(wordId);
			if (result==null) {
				result="";
			}
			ViewRuleRemark rai = new ViewRuleRemark(this, true, result,wordId);
			rai.setVisible(true);
		}
	}
	

	/**
	 * 点击规则树节点，初始化规则库表
	 * @param evt
	 */
	private void jTree1MouseClicked(java.awt.event.MouseEvent evt) {
		// TODO add your handling code here:
		this.initTable();
//		if(evt.getButton()==evt.BUTTON3){      //黄翔修改
//			if(jTree1.isSelectionEmpty()!=true){
//				DefaultMutableTreeNode path=(DefaultMutableTreeNode) jTree1.getLastSelectedPathComponent();
//					pop.show(jTree1,evt.getX(),evt.getY());
//				}
//			}
		}

	/**
	 * 初始化规则树
	 */
	@SuppressWarnings("unchecked")
	private void initTree() {
		DefaultMutableTreeNode rootNode = new DefaultMutableTreeNode(new TreeModel("1", "规则库"));
		DefaultTreeModel treeModel = new DefaultTreeModel(rootNode);
	    //查询所属系统
		List systemList = DBManager.queryForList("select code,name from "+CBSystemConstants.opcardUser+"T_a_dictionary a where a.unitcode='system' and a.codetype='Ruleareacode'");
		Map system=new HashMap();
		if(systemList!=null||!systemList.isEmpty()){
			for (Iterator iter = systemList.iterator(); iter.hasNext();) {
				system=(Map)iter.next();
				DefaultMutableTreeNode systemNode = new DefaultMutableTreeNode(new TreeModel(system.get("code").toString(), system.get("name").toString()),true);
				//在系统下添加一次设备和其他设备节点
				DefaultMutableTreeNode yiCiSheBeiNode = new DefaultMutableTreeNode(new TreeModel(system.get("code").toString(), "一次设备","0",ruletype));
				DefaultMutableTreeNode qiTaSheBeiNode = new DefaultMutableTreeNode(new TreeModel(system.get("code").toString(), "其它设备","1",ruletype));
				systemNode.add(yiCiSheBeiNode);
				systemNode.add(qiTaSheBeiNode);
				rootNode.add(systemNode);
			}
	 	}
	
		if ("0".equals(this.ruletype)) {
			((TreeModel)rootNode.getUserObject()).setName("规则库(前置条件)");
		} else if ("1".equals(this.ruletype)) {
			((TreeModel)rootNode.getUserObject()).setName("规则库(执行动作)");
		} 
		this.jTree1.setModel(treeModel);
		this.jTree1.expandRow(1);
		this.jTree1.expandRow(2);
		this.jTree1.expandRow(3);
	}
	/**
	 * 初始化表格
	 */
	@SuppressWarnings("serial")
	private void initTable() {

		TreePath path = jTree1.getSelectionPath();
		if (path == null) {
			return;
		}
		DefaultMutableTreeNode node = (DefaultMutableTreeNode) path
				.getLastPathComponent();
		if (!node.isLeaf())
			return;
		//设置当前选中的常用语类别
		TreeModel cnm = (TreeModel) node.getUserObject();
		jtableModel = new DefaultTableModel(null, new String[] { "序号", "规则",
				"class类", "设备类型", "设备状态", "执行动作", "电站类型" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				if (columnIndex > 1)
					return true;
				else
					return false;
			}
		};

		RuleManagerDao rmd = new RuleManagerDao();
		List<Object[]> results = rmd.getWordBeans(cnm);
		for (int i = 0; i < results.size(); i++) {
			jtableModel.addRow(results.get(i));
		}
		jTable1.setModel(jtableModel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(40);
		jTable1.getColumnModel().getColumn(1).setMinWidth(150);
		jTable1.getColumnModel().getColumn(2).setMinWidth(350);
		jTable1.getColumnModel().getColumn(3).setMinWidth(100);
	}

	private void changeState(int row) {

		if (jTable1.getColumnModel().getColumn(3).getCellEditor() != null) {
			jTable1.getColumnModel().getColumn(3).setCellEditor(null);
		}
		if (jTable1.getColumnModel().getColumn(4).getCellEditor() != null) {
			jTable1.getColumnModel().getColumn(4).setCellEditor(null);
		}
		if (jTable1.getCellEditor() != null) {
			jTable1.getCellEditor().stopCellEditing();
		}
		if (jTable1.getColumnModel().getColumn(5).getCellEditor() != null) {
			jTable1.getColumnModel().getColumn(5).setCellEditor(null);
		}
		if (jTable1.getColumnModel().getColumn(6).getCellEditor() != null) {
			jTable1.getColumnModel().getColumn(6).setCellEditor(null);
		}
		CodeNameModel cnm = (CodeNameModel) jtableModel.getValueAt(row, 1);
		if (cnm == null)
			return;
		String wordid = cnm.getCode();
		RuleManagerDao rmd = new RuleManagerDao();
		RuleBaseMode rbm = rmd.getRuleBaseModel(wordid);
		CodeNameModel tempcnm = null;
		//设备类型下拉框
		String devRunType = rbm.getDeviceruntype();
		if (!"".equals(devRunType) && devRunType != null) {
			DefaultComboBoxModel devRunTypeModel = new DefaultComboBoxModel();
			String[] status = devRunType.split(",");
			for (int i = 0; i < status.length; i++) {
				tempcnm = new CodeNameModel(status[i],
						CBSystemConstants.getDictionaryName(status[i]));
				devRunTypeModel.addElement(tempcnm);
			}
			JComboBox devRunTypeCheck = new JComboBox();
			devRunTypeCheck.setModel(devRunTypeModel);
			DefaultCellEditor devRunTypeEditor = new DefaultCellEditor(
					devRunTypeCheck);
			devRunTypeEditor.setClickCountToStart(2);
			jTable1.getColumnModel().getColumn(3)
					.setCellEditor(devRunTypeEditor);
		}
		//设备状态下拉框
		String beginStatus = rbm.getBeginStatus();
		String codetype = rbm.getCodetype();//元件状态还是执行状态
		if (!"".equals(beginStatus) && beginStatus != null) {
			if("EquipStatus".equals(codetype)) {
				DefaultComboBoxModel statusModel = new DefaultComboBoxModel();
				String[] status = beginStatus.split(",");
				for (int i = 0; i < status.length; i++) {
					tempcnm = new CodeNameModel(status[i],
							CBSystemConstants.getDictionaryName(status[i], codetype));
					statusModel.addElement(tempcnm);
				}
				JComboBox statusCheck = new JComboBox();
				statusCheck.setModel(statusModel);
				DefaultCellEditor statusEditor = new DefaultCellEditor(statusCheck);
				statusEditor.setClickCountToStart(2);
				jTable1.getColumnModel().getColumn(4).setCellEditor(statusEditor);
			}else if ("SwitchStatus".equals(codetype)) {
				DefaultComboBoxModel statusModel = new DefaultComboBoxModel();
				String[] status = beginStatus.split(",");
				for (int i = 0; i < status.length; i++) {
					tempcnm = new CodeNameModel(status[i],
							CBSystemConstants.getDictionaryName(status[i], codetype));
					statusModel.addElement(tempcnm);
				}
				JComboBox statusCheck = new JComboBox();
				statusCheck.setModel(statusModel);
				DefaultCellEditor statusEditor = new DefaultCellEditor(statusCheck);
				statusEditor.setClickCountToStart(2);
				jTable1.getColumnModel().getColumn(4).setCellEditor(statusEditor);
			}

		}
		//设备动作下拉框
		String state = rbm.getEndState();
		if (!"".equals(state) && state != null) {
			if("EquipStatus".equals(codetype)) {
				DefaultComboBoxModel stateModel = new DefaultComboBoxModel();
				String[] states = state.split(",");
				for (int i = 0; i < states.length; i++) {
					tempcnm = new CodeNameModel(states[i],
							CBSystemConstants.getDictionaryName(states[i], codetype));
					stateModel.addElement(tempcnm);
				}
				JComboBox stateCheck = new JComboBox();
				stateCheck.setModel(stateModel);
				DefaultCellEditor stateEditor = new DefaultCellEditor(stateCheck);
				stateEditor.setClickCountToStart(2);
				jTable1.getColumnModel().getColumn(5).setCellEditor(stateEditor);
			}else if ("SwitchStatus".equals(codetype)) {
				DefaultComboBoxModel stateModel = new DefaultComboBoxModel();
				String[] states = state.split(",");
				for (int i = 0; i < states.length; i++) {
					tempcnm = new CodeNameModel(states[i],
							CBSystemConstants.getDictionaryName(states[i], codetype));
					stateModel.addElement(tempcnm);
				}
				JComboBox stateCheck = new JComboBox();
				stateCheck.setModel(stateModel);
				DefaultCellEditor stateEditor = new DefaultCellEditor(stateCheck);
				stateEditor.setClickCountToStart(2);
				jTable1.getColumnModel().getColumn(5).setCellEditor(stateEditor);
			}

		}

		//变电站类型下拉框
		String tranType = rbm.getTranType();
		if (!"".equals(tranType) && tranType != null) {
			DefaultComboBoxModel tranTypeModel = new DefaultComboBoxModel();
			String[] states = tranType.split(",");
			for (int i = 0; i < states.length; i++) {
				tranTypeModel.addElement(states[i]);
			}
			JComboBox trantypeCheck = new JComboBox();
			trantypeCheck.setModel(tranTypeModel);
			DefaultCellEditor tranTypeEditor = new DefaultCellEditor(
					trantypeCheck);
			tranTypeEditor.setClickCountToStart(2);
			jTable1.getColumnModel().getColumn(6).setCellEditor(tranTypeEditor);
		}

	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				RuleManager dialog = new RuleManager(new javax.swing.JFrame(),
						true, null);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JButton jButton4;//新增查看备注功能
	private javax.swing.JPanel jPanel1;
	private javax.swing.JPanel jPanel2;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JTable jTable1;
	private javax.swing.JTree jTree1;
}
