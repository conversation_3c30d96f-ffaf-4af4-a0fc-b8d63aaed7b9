package czprule.rule.view;

import czprule.rule.dao.RuleManagerDao;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

public class ViewRuleRemark extends JDialog {

	private final JPanel contentPanel = new JPanel();
	private JTextArea jTextArea;
	private final JButton button1 = new JButton("保存");
	private String wordId="";
	/**
	 * Create the dialog.
	 */
	public ViewRuleRemark(JDialog parent, boolean modal, String content, String wordId) {
		super(parent, modal);
		initViewRuleRemark(content);
		this.wordId=wordId;
		this.setLocationCenter();
	}
	
	private void initViewRuleRemark(String content) {
		addWindowListener(new WindowAdapter() {
			@Override
			public void windowDeiconified(WindowEvent e) {
			}
		});
		setBounds(46, 69, 390, 173);
		getContentPane().setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		getContentPane().add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(null);
		this.setTitle("规则描述编辑框");
		jTextArea = new JTextArea();
		jTextArea.setBounds(2, 0, 351, 92);
		contentPanel.add(jTextArea);
		jTextArea.setColumns(10);
		{
			jTextArea.setText(content);
		}
		button1.setBounds(107, 102, 67, 23);
		contentPanel.add(button1);
		button1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				button1ActionPerformed(evt);
			}
		});
		JButton button2 = new JButton("重置");
		button2.setBounds(205, 102, 67, 23);
		contentPanel.add(button2);
		button2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				button2ActionPerformed(evt);
			}
		});
	}
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	
	/**
	 * 保存文本内容
	 * @param evt
	 */
	private void button1ActionPerformed(java.awt.event.ActionEvent evt) {
		RuleManagerDao rmd = new RuleManagerDao();
		rmd.updateWordBean(jTextArea.getText().trim(),wordId);
		JOptionPane.showMessageDialog(this, "保存成功", "保存规则备注提示框",
				JOptionPane.WARNING_MESSAGE);
		this.dispose();
	}
	
	/**
	 * 重置文本框
	 * @param evt
	 */
	private void button2ActionPerformed(java.awt.event.ActionEvent evt) {
		jTextArea.setText("");
	}
}
