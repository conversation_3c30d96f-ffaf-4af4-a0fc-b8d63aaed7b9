/*
 * RuleAddItem.java
 *
 * Created on __DATE__, __TIME__
 */

package czprule.rule.view;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.CodeNameModel;
import czprule.model.DeviceStateModel;
import czprule.model.DictionarysModel;
import czprule.model.TreeModel;
import czprule.rule.dao.RuleManagerDao;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

import javax.swing.*;
import javax.swing.event.TableModelEvent;
import java.awt.*;
import java.util.List;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class RuleAddItem extends javax.swing.JDialog {

	private TreeModel cnm;

	/** Creates new form RuleAddItem */
	public RuleAddItem(java.awt.Frame parent, boolean modal, TreeModel cnm) {
		super(parent, modal);
		this.cnm = cnm;
		initComponents();
		this.initTable();
		this.setLocationCenter();
	}

	public RuleAddItem(javax.swing.JDialog parent, boolean modal,
			TreeModel cnm) {
		super(parent, modal);
		this.cnm = cnm;
		initComponents();
		this.initTable();
		this.setLocationCenter();
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jLabel1 = new javax.swing.JLabel();
		jLabel2 = new javax.swing.JLabel();
		jLabel3 = new javax.swing.JLabel();
		jTextField1 = new javax.swing.JTextField();
		jTextField2 = new javax.swing.JTextField();
		jComboBox3 = new javax.swing.JComboBox();
		jButton1 = new javax.swing.JButton();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable() {
			public void tableChanged(TableModelEvent e) {
				super.tableChanged(e);
				repaint();
			}
		};
		jScrollPane2 = new javax.swing.JScrollPane();
		jTable2 = new javax.swing.JTable() {
			public void tableChanged(TableModelEvent e) {
				super.tableChanged(e);
				repaint();
			}
		};
		jButton3 = new javax.swing.JButton();
		jScrollPane3 = new javax.swing.JScrollPane();
		jTable3 = new javax.swing.JTable();
		jScrollPane5 = new javax.swing.JScrollPane();
		jTable5 = new javax.swing.JTable();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jLabel1.setText("\u89c4\u5219\u540d\u79f0\uff1a");

		jLabel2.setText("Class\u7c7b\uff1a");
		
		jLabel3.setText("状态类型");
		
		
		DefaultComboBoxModel stateModel = new DefaultComboBoxModel();
		stateModel.addElement(new CodeNameModel("EquipStatus","设备状态"));
		stateModel.addElement(new CodeNameModel("SwitchStatus","元件状态"));
		jComboBox3.setModel(stateModel);
		
		jComboBox3.addItemListener(new java.awt.event.ItemListener() {
			public void itemStateChanged(java.awt.event.ItemEvent evt) {
				initTable();
			}
		});

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/save.png"))); // NOI18N
		jButton1.setToolTipText("\u4fdd\u5b58");
		jButton1.setBorder(null);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jScrollPane1.setBorder(javax.swing.BorderFactory
				.createTitledBorder("\u72b6\u6001"));
		jScrollPane1.setViewportView(jTable1);

		jScrollPane2.setBorder(javax.swing.BorderFactory
				.createTitledBorder("\u6267\u884c\u52a8\u4f5c"));
		jScrollPane2.setViewportView(jTable2);

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/gc.png"))); // NOI18N
		jButton3.setToolTipText("\u6e05\u7a7a");
		jButton3.setBorder(null);
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		jScrollPane3.setBorder(javax.swing.BorderFactory
				.createTitledBorder("\u8bbe\u5907\u7c7b\u578b"));
		jScrollPane3.setViewportView(jTable3);

		jScrollPane5.setBorder(javax.swing.BorderFactory
				.createTitledBorder("\u7535\u7ad9\u7c7b\u578b"));
		jScrollPane5.setViewportView(jTable5);

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.addContainerGap()
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.LEADING)
								.add(layout
										.createSequentialGroup()
										.add(layout
												.createParallelGroup(
														org.jdesktop.layout.GroupLayout.LEADING)
												.add(jLabel1,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
														66,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
												.add(jLabel2,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
														59,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
												.add(jLabel3,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
														59,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
										.addPreferredGap(
												org.jdesktop.layout.LayoutStyle.RELATED)
										.add(layout
												.createParallelGroup(
														org.jdesktop.layout.GroupLayout.LEADING)
												.add(org.jdesktop.layout.GroupLayout.TRAILING,
														layout.createSequentialGroup()
																.add(jScrollPane3,
																		org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																		207,
																		Short.MAX_VALUE)
																.addPreferredGap(
																		org.jdesktop.layout.LayoutStyle.RELATED)
																.add(jScrollPane1,
																		org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
																		133,
																		org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
																.addPreferredGap(
																		org.jdesktop.layout.LayoutStyle.RELATED)
																.add(jScrollPane2,
																		org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
																		130,
																		org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
																.addPreferredGap(
																		org.jdesktop.layout.LayoutStyle.RELATED)
																.add(jScrollPane5,
																		org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
																		125,
																		org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
												.add(jComboBox3,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														616, Short.MAX_VALUE)
												.add(jTextField2,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														616, Short.MAX_VALUE)
												.add(jTextField1,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														616, Short.MAX_VALUE)))
								.add(org.jdesktop.layout.GroupLayout.TRAILING,
										layout.createSequentialGroup()
												.add(jButton3,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
														16,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
												.addPreferredGap(
														org.jdesktop.layout.LayoutStyle.RELATED)
												.add(jButton1,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
														16,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)))
						.addContainerGap()));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(org.jdesktop.layout.GroupLayout.TRAILING,
						layout.createSequentialGroup()
								.addContainerGap()
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jLabel1)
										.add(jTextField1,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.UNRELATED)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jLabel2)
										.add(jTextField2,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
										.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jLabel3)
										.add(jComboBox3,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.LEADING)
										.add(jScrollPane3,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												336, Short.MAX_VALUE)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jScrollPane1,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												336, Short.MAX_VALUE)
										.add(jScrollPane2,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												336, Short.MAX_VALUE)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jScrollPane5,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												336, Short.MAX_VALUE))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.TRAILING)
										.add(jButton1,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												16,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jButton3,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												16,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
								.addContainerGap()));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.jTextField1.setText("");
		this.jTextField2.setText("");
		this.initTable();
	}

	private void initTable() {
		/**/
		jtablemodel = new ColorTableModel();
		jtablemode2 = new ColorTableModel();
		jtablemode3 = new ColorTableModel();
		jtablemode4 = new ColorTableModel();

		DeviceStateModel dsm = null;
		Vector<Object> rowData1 = new Vector<Object>();
		Vector<Object> rowData2 = new Vector<Object>();
		Vector<Object> rowData3 = new Vector<Object>();
		Vector<Object> rowData4 = new Vector<Object>();
		
		Map<String, String> stateModel = new HashMap<String, String>();
		if(jComboBox3.getSelectedIndex() == 0)
			stateModel = CBSystemConstants.DeviceStateModel;
		else {
			List<DictionarysModel> equipStatusDicList = CBSystemConstants.getDictionary("SwitchStatus");
			for(DictionarysModel dm : equipStatusDicList) {
				stateModel.put(dm.getCode(), dm.getName());
			}
		}

		TreeMap<String, DeviceStateModel> menuTreeMap = new TreeMap<String, DeviceStateModel>();
		for (Iterator iterator = stateModel.keySet()
				.iterator(); iterator.hasNext();) {
			String value = iterator.next().toString();
			DeviceStateModel dd = new DeviceStateModel();
			dd.setStateName(stateModel.get(value));
			dd.setStateValue(value);
			dd.setStateCode(value);
			menuTreeMap.put(value,dd);
		}
		for (Iterator iterator = menuTreeMap.values().iterator(); iterator
				.hasNext();) {
			dsm = (DeviceStateModel) iterator.next();
			if (!dsm.getStateValue().equals("-1")) {
				rowData1.add(new Object[] { Boolean.FALSE, dsm });
			}
			rowData2.add(new Object[] { Boolean.FALSE, dsm });
		}
		List<DictionarysModel> SwitchRunTypes = CBSystemConstants
				.getDictionary(SystemConstants.Switch);
		List<DictionarysModel> knifeRunTypes = CBSystemConstants
				.getDictionary(SystemConstants.SwitchSeparate);
		DictionarysModel dm = null;
		for (int i = 0; i < SwitchRunTypes.size(); i++) {
			dm = SwitchRunTypes.get(i);
			rowData3.add(new Object[] { Boolean.FALSE, dm });
		}
		for (int i = 0; i < knifeRunTypes.size(); i++) {
			dm = knifeRunTypes.get(i);
			rowData3.add(new Object[] { Boolean.FALSE, dm });
		}
		rowData4.add(new Object[] { Boolean.FALSE, "S" });
		rowData4.add(new Object[] { Boolean.FALSE, "T" });

		jtablemodel.setRowTitle(new String[] { "选择", "状态" });
		jtablemodel.setRowData(rowData1);
		jtablemode2.setRowTitle(new String[] { "选择", "执行动作" });
		jtablemode2.setRowData(rowData2);
		jtablemode3.setRowTitle(new String[] { "选择", "运行状态" });
		jtablemode3.setRowData(rowData3);
		jtablemode4.setRowTitle(new String[] { "选择", "电站类型" });
		jtablemode4.setRowData(rowData4);
		jTable1.setModel(jtablemodel);
		jTable2.setModel(jtablemode2);
		jTable3.setModel(jtablemode3);
		jTable5.setModel(jtablemode4);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(40);
		jTable2.getColumnModel().getColumn(0).setMaxWidth(40);
		jTable3.getColumnModel().getColumn(0).setMaxWidth(40);
		jTable5.getColumnModel().getColumn(0).setMaxWidth(40);
		
	}

	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if ("".equals(jTextField1.getText().trim())) {
			ShowMessage.view("请填写规则名称！");
			return;
		}
		if ("".equals(jTextField2.getText().trim())) {
			ShowMessage.view("请填写Class类名！");
			return;
		}

		Object[] temp;
		DeviceStateModel dsm = null;
		String beginStatus = "";
		Vector<Object> rowData1 = jtablemodel.getRowData();
		for (int i = 0; i < rowData1.size(); i++) {
			temp = (Object[]) rowData1.get(i);
			dsm = (DeviceStateModel) temp[1];
			if (temp[0].equals(Boolean.TRUE)) {
				if ("".equals(beginStatus))
					beginStatus = dsm.getStateCode();
				else
					beginStatus = beginStatus + "," + dsm.getStateCode();
			}
		}
		Vector<Object> rowData2 = jtablemode2.getRowData();
		String states = "";
		for (int i = 0; i < rowData2.size(); i++) {
			temp = (Object[]) rowData2.get(i);
			dsm = (DeviceStateModel) temp[1];
			if (temp[0].equals(Boolean.TRUE)) {
				if ("".equals(states))
					states = dsm.getStateCode();
				else
					states = states + "," + dsm.getStateCode();
			}
		}
		Vector<Object> rowData3 = jtablemode3.getRowData();
		String devruntype = "";
		DictionarysModel dm = null;
		for (int i = 0; i < rowData3.size(); i++) {
			temp = (Object[]) rowData3.get(i);
			dm = (DictionarysModel) temp[1];
			if (temp[0].equals(Boolean.TRUE)) {
				if ("".equals(devruntype))
					devruntype = dm.getCode();
				else
					devruntype = devruntype + "," + dm.getCode();
			}
		}
		Vector<Object> rowData4 = jtablemode4.getRowData();
		String transType = "";
		for (int i = 0; i < rowData4.size(); i++) {
			temp = (Object[]) rowData4.get(i);
			if (temp[0].equals(Boolean.TRUE)) {
				if ("".equals(transType))
					transType = temp[1].toString();
				else
					transType = transType + "," + temp[1].toString();
			}
		}
		String wordValue = jTextField1.getText().trim();
		String wordRule = jTextField2.getText().trim();
		String ruleArea = cnm.getCode();
		String runType = cnm.getRuleType();
		String equipType = cnm.getEquipType();
		RuleBaseMode rbm = new RuleBaseMode();
		rbm.setRuleValue(wordValue);
		rbm.setRuleBeanClass(wordRule);
		rbm.setBeginStatus(beginStatus);
		rbm.setEndState(states);
		rbm.setDeviceruntype(devruntype);
		rbm.setTranType(transType);
		rbm.setCodetype(((CodeNameModel)jComboBox3.getSelectedItem()).getCode());
		RuleManagerDao rmd = new RuleManagerDao();
		rmd.saveWordBean(rbm, ruleArea,equipType,runType);
		this.setVisible(false);
		this.dispose();
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				RuleAddItem dialog = new RuleAddItem(new javax.swing.JFrame(),
						true, null);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton3;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JLabel jLabel3;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JScrollPane jScrollPane3;
	private javax.swing.JScrollPane jScrollPane5;
	private javax.swing.JTable jTable1;
	private javax.swing.JTable jTable2;
	private javax.swing.JTable jTable3;
	private javax.swing.JTable jTable5;
	private javax.swing.JTextField jTextField1;
	private javax.swing.JTextField jTextField2;
	private javax.swing.JComboBox jComboBox3;
	// End of variables declaration//GEN-END:variables

	private ColorTableModel jtablemodel;
	private ColorTableModel jtablemode2;
	private ColorTableModel jtablemode3;
	private ColorTableModel jtablemode4;
}
