package czprule.rule.view;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import javax.swing.*;
import javax.swing.GroupLayout.Alignment;
import javax.swing.LayoutStyle.ComponentPlacement;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.UUID;

public class WordCardEdit extends JDialog{
	private static final long serialVersionUID = 1L;
	private JComboBox comboBox;
	private String Id;
	private String Area;
	public WordCardEdit(JDialog parent, boolean modal, String id, String typename, String var, String desc, String type, String area) {
		super(parent, modal);
		JPanel panel = new JPanel();
//		//总是显示该窗体在最前面
//		this.setAlwaysOnTop(true);
		this.setBounds(200, 300, 700, 500);
		this.setTitle("术语编辑");
		this.Id = id;
		getContentPane().add(panel, BorderLayout.WEST);		
		textField_1 = new JTextField();
		textField_1.setColumns(10);
		textField_1.setText(typename);
		JLabel lblNewLabel = new JLabel("\u7C7B\u540D\u79F0\uFF1A");		
		JLabel lblNewLabel_1 = new JLabel("\u5B57\u7B26\u53D8\u91CF\uFF1A");		
		textField_2 = new JTextField();
		textField_2.setColumns(10);	
		textField_2.setText(var);
		JLabel label = new JLabel("\u63CF\u8FF0\uFF1A");		
		JLabel label_1 = new JLabel("\u7C7B\u578B\uFF1A");		
		rdbtnNewRadioButton = new JRadioButton("\u66FF\u6362\u7C7B");
		radioButton = new JRadioButton("\u5224\u65AD\u7C7B");
		radioButton_1 = new JRadioButton("\u5176\u4ED6");
		radioButton_1.setEnabled(false);
		
		if(type.equals("替换类")){
			 rdbtnNewRadioButton.setSelected(true);
			 rdbtnNewRadioButton.setActionCommand("0");
		}else if(type.equals("判断类")){
			radioButton.setSelected(true);
			radioButton.setActionCommand("1");
		}else{
			radioButton_1.setSelected(true);
			radioButton.setActionCommand("3");
		}
		
		this.Area = area;
		
		bg = new ButtonGroup();
		bg.add(rdbtnNewRadioButton);
		bg.add(radioButton);
		bg.add(radioButton_1);
		comboBox=new JComboBox();
		JButton button = new JButton("\u4FDD \u5B58");//保存
		JButton button_1 = new JButton("\u5173 \u95ED");

		button.setBorder(null);
		button.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent evt) {
                jButtonActionPerformed(evt);
            }
        });
		
		button_1.setBorder(null);
		button_1.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent evt) {
                closeWindows(evt);
            }
        });
		textArea = new JTextArea();
		textArea.setLineWrap(true);
		textArea.setRows(4);
		textArea.setText(desc);
		
		JLabel label_2 = new JLabel("\u533A\u57DF\uFF1A");
		
		initDictionary();
		GroupLayout gl_panel = new GroupLayout(panel);
		gl_panel.setHorizontalGroup(
			gl_panel.createParallelGroup(Alignment.LEADING)
				.addGroup(gl_panel.createSequentialGroup()
					.addContainerGap()
					.addGroup(gl_panel.createParallelGroup(Alignment.LEADING)
						.addGroup(gl_panel.createSequentialGroup()
							.addGap(227)
							.addComponent(button, GroupLayout.PREFERRED_SIZE, 51, GroupLayout.PREFERRED_SIZE)
							.addGap(28)
							.addComponent(button_1, GroupLayout.PREFERRED_SIZE, 43, GroupLayout.PREFERRED_SIZE))
						.addGroup(gl_panel.createSequentialGroup()
							.addGroup(gl_panel.createParallelGroup(Alignment.LEADING)
								.addComponent(label)
								.addComponent(lblNewLabel)
								.addComponent(lblNewLabel_1)
								.addComponent(label_2, GroupLayout.PREFERRED_SIZE, 36, GroupLayout.PREFERRED_SIZE)
								.addComponent(label_1))
							.addGap(18)
							.addGroup(gl_panel.createParallelGroup(Alignment.LEADING)
								.addComponent(comboBox, GroupLayout.PREFERRED_SIZE, 129, GroupLayout.PREFERRED_SIZE)
								.addGroup(gl_panel.createSequentialGroup()
									.addComponent(rdbtnNewRadioButton)
									.addGap(4)
									.addComponent(radioButton)
									.addPreferredGap(ComponentPlacement.UNRELATED)
									.addComponent(radioButton_1))
								.addComponent(textField_2, GroupLayout.DEFAULT_SIZE, 553, Short.MAX_VALUE)
								.addComponent(textField_1, GroupLayout.DEFAULT_SIZE, 553, Short.MAX_VALUE)
								.addComponent(textArea, GroupLayout.PREFERRED_SIZE, 553, GroupLayout.PREFERRED_SIZE))))
					.addContainerGap())
		);
		gl_panel.setVerticalGroup(
			gl_panel.createParallelGroup(Alignment.LEADING)
				.addGroup(gl_panel.createSequentialGroup()
					.addContainerGap()
					.addGroup(gl_panel.createParallelGroup(Alignment.BASELINE)
						.addComponent(textField_1, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
						.addComponent(lblNewLabel))
					.addGap(18)
					.addGroup(gl_panel.createParallelGroup(Alignment.BASELINE)
						.addComponent(lblNewLabel_1)
						.addComponent(textField_2, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))
					.addGroup(gl_panel.createParallelGroup(Alignment.LEADING)
						.addGroup(gl_panel.createSequentialGroup()
							.addGap(18)
							.addComponent(textArea, GroupLayout.PREFERRED_SIZE, 81, GroupLayout.PREFERRED_SIZE))
						.addGroup(gl_panel.createSequentialGroup()
							.addGap(39)
							.addComponent(label)))
					.addGap(24)
					.addGroup(gl_panel.createParallelGroup(Alignment.BASELINE)
						.addComponent(label_2)
						.addComponent(comboBox, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))
					.addGap(18)
					.addGroup(gl_panel.createParallelGroup(Alignment.LEADING)
						.addGroup(gl_panel.createSequentialGroup()
							.addGap(58)
							.addGroup(gl_panel.createParallelGroup(Alignment.BASELINE)
								.addComponent(button, GroupLayout.PREFERRED_SIZE, 21, GroupLayout.PREFERRED_SIZE)
								.addComponent(button_1, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)))
						.addGroup(gl_panel.createParallelGroup(Alignment.BASELINE)
							.addComponent(label_1)
							.addComponent(rdbtnNewRadioButton)
							.addComponent(radioButton)
							.addComponent(radioButton_1)))
					.addContainerGap(147, Short.MAX_VALUE))
		);
		panel.setLayout(gl_panel);
		this.setLocationCenter();
	}


	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,(h - this.getSize().height) / 2);
	}
	
    /**
     * 初始化业务名称下拉框
     * 显示业务名称 "+CBSystemConstants.opcardUser+"t_A_DICTIONARY中“name”字段
     */
	public void initDictionary() {
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		String sql = "select t.code,t.name from "+CBSystemConstants.opcardUser+"t_A_DICTIONARY t where t.unitcode='system' and t.codetype='Ruleareacode'";
		SqlRowSet set = DBManager.queryForRowSet(sql);
		while (set.next()){
			cnm=new CodeNameModel();
			cnm.setCode(set.getString(1));
			cnm.setName(set.getString(2));
			model.addElement(cnm);
			if(cnm.getName().equals(Area))
				model.setSelectedItem(cnm);
		}
		comboBox.setModel(model);
	}
	 
	public void jButtonActionPerformed(ActionEvent evt) {
		if(rdbtnNewRadioButton.isSelected()) {
			flag = true;
			rdbtnNewRadioButton.setActionCommand("0");
		}
		if(radioButton.isSelected()) {
			flag = true;
			radioButton.setActionCommand("1");
		}
		if(radioButton_1.isSelected()){
			flag = true;
			radioButton_1.setActionCommand("3");
		}
		String uuid = UUID.randomUUID().toString();
		String className = textField_1.getText();//类名称
		String zfbl = textField_2.getText();//字符变量
		String ms = textArea.getText();//描述		
		CodeNameModel cnm=(CodeNameModel) comboBox.getSelectedItem();//区域
		String rulearea=cnm.getCode();
		String type = "";
		String choice = "";

		if ("".equals(className.trim())) {
			JOptionPane.showConfirmDialog(this,"请填写类名称！","提示信息",JOptionPane.PLAIN_MESSAGE);
			return;
		}
		if ("".equals(zfbl.trim())) {
			JOptionPane.showConfirmDialog(this,"请填写字符变量！","提示信息",JOptionPane.PLAIN_MESSAGE);
			return;
		}
		if ("".equals(ms)) {
			JOptionPane.showConfirmDialog(this,"请填写字描述！","提示信息",JOptionPane.PLAIN_MESSAGE);
			return;
		}
		if(flag == true) {
			choice=bg.getSelection().getActionCommand();
		}else {
			JOptionPane.showConfirmDialog(this,"请选择类型！","提示信息",JOptionPane.PLAIN_MESSAGE);
			return;
		}
		
		if("0".equals(choice)){
			type ="0";
		}else if ("1".equals(choice)){
			type ="1";
		}else {
			type ="";
		}
		this.updateCardwordTemp(uuid, className, zfbl, ms, type, rulearea);
		JOptionPane.showConfirmDialog(this,"保存成功！","提示信息",JOptionPane.PLAIN_MESSAGE);
		this.setVisible(false);
		this.dispose();
		
	}
	
	private void closeWindows(ActionEvent evt){
		
		this.setVisible(false);
		this.dispose();
	}
	public void updateCardwordTemp(String id,String classBean,String tempStr,String des,String type,String rulearea){
		String sql = "update "+CBSystemConstants.opcardUser+"t_A_CARDWORDTEMPREPLACE set classbean='"+ classBean + "', tempStr='"+tempStr+"', describe='"+des+"', classtype='"+type+"', rulearea='"+rulearea+"' where id='"+ Id + "'";
		DBManager.execute(sql);
	}
	
	private JTextField textField_1;
	private JTextField textField_2;
	private JTextArea textArea;
    private	ButtonGroup bg ;//单选按钮组颜色渲染  czprule.wordcard.replaceclass.impl.ReplaceStrSBGLMX
    private boolean flag = false;//是否选择radio
    private JRadioButton radioButton;
    private JRadioButton rdbtnNewRadioButton;
    private JRadioButton radioButton_1;
}
