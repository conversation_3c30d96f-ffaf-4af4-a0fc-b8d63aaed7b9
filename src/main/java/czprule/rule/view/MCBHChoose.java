/*
 * EquipCheckChoose.java
 *
 * Created on __DATE__, __TIME__
 */

package czprule.rule.view;

import czprule.system.ShowMessage;

import javax.swing.*;
import java.awt.*;
import java.awt.event.WindowEvent;
import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

/**
 *
 * <AUTHOR>
 */
public class MCBHChoose extends javax.swing.JDialog {
	private List<String> equipList = null;
	private List<String> equipList2 = null;
	private List<String> equipList3 = null;
	private List<String> chooseequipList = new ArrayList<String>();
	private List<String> chooseequipList2 = new ArrayList<String>();
	private List<String> chooseequipList3 = new ArrayList<String>();
	private boolean isCancel = true;
	private boolean isMustSelect = false;
	private String[] title;
	private int[] selType; //0随便选 1单选 2至少选两个  3不能全选
	
	
	public MCBHChoose(java.awt.Frame parent, boolean modal,
			List<String> equipsList1,List<String> equipsList2,List<String> equipsList3,String[] str,int[] selType) {
		super(parent, modal);
		this.title =str;
		this.selType = selType;

		if (equipsList1 != null) {
			this.equipList = equipsList1;
			
		}
		if (equipsList2 != null) {
			this.equipList2 = equipsList2;
			
		}
		if (equipsList3 != null) {
			this.equipList3 = equipsList3;
			
		}
		initComponentsNew();
		
		String titlestr = "  请选择";
		for(String t : title) {
			titlestr = titlestr + t + "、";
		}
		if(titlestr.endsWith("、"))
			titlestr = titlestr.substring(0,titlestr.length()-1);
		this.jLabel1.setText(titlestr);
		jLabel1.setFont(new java.awt.Font("黑体", 0, 16));
		
		
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	
	

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	
	private void initComponentsNew() {
		int gudingSize = 200;
		int tableSize = 200;
		jLabel1 = new JLabel();
		jScrollPane1 = new JScrollPane();
		JScrollPane jScrollPane2 = new JScrollPane();
		JScrollPane jScrollPane3 = new JScrollPane();
		jTable1 = new JTable();
		jTable2 = new JTable();
		jTable3 = new JTable();
		jButton1 = new JButton();
		jButton2 = new JButton();
		jButton3 = new JButton();
		
		this.setLayout(new BorderLayout());
		this.setSize(gudingSize+title.length*tableSize, 350);
		topPanel =new JPanel();
		topPanel.setLayout(new BorderLayout());
		topPanel.setPreferredSize(new Dimension(0,32));
		this.add(topPanel,BorderLayout.NORTH);
		mainPanel =new JPanel();
		mainPanel.setLayout(new BorderLayout(10, 10));
		this.add(mainPanel,BorderLayout.CENTER);
		

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		addWindowListener(new java.awt.event.WindowAdapter() {
			public void windowClosed(WindowEvent evt) {
				windowcloseAction(evt);
			}
		});

		jLabel1.setText("jLabel1");

		jScrollPane1.setViewportView(jTable1);
		jScrollPane2.setViewportView(jTable2);
		jScrollPane3.setViewportView(jTable3);
		
		
		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
		jButton1.setToolTipText("\u786e\u5b9a");
		jButton1.setText("\u786e\u5b9a");
		jButton1.setMargin(new java.awt.Insets(1,1,1,1));
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton11ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/gc.png"))); // NOI18N
		jButton2.setToolTipText("清空");
		jButton2.setText("清空");
		jButton2.setMargin(new java.awt.Insets(1,1,1,1));
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton12ActionPerformed(evt);
			}
		});

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/all.gif"))); // NOI18N
		jButton3.setToolTipText("\u5168\u9009");
		jButton3.setText("\u5168\u9009");
		jButton3.setMargin(new java.awt.Insets(1,1,1,1));
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton13ActionPerformed(evt);
			}
		});
		
		
		
		JButton jButton22 = new JButton();
		jButton22.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/gc.png"))); // NOI18N
		jButton22.setToolTipText("清空");
		jButton22.setText("清空");
		jButton22.setMargin(new java.awt.Insets(1,1,1,1));
		jButton22.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton22ActionPerformed(evt);
			}
		});

		
		JButton jButton23 = new JButton();
		jButton23.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/all.gif"))); // NOI18N
		jButton23.setToolTipText("\u5168\u9009");
		jButton23.setText("\u5168\u9009");
		jButton23.setMargin(new java.awt.Insets(1,1,1,1));
		jButton23.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton23ActionPerformed(evt);
			}
		});
		
		
		JButton jButton32 = new JButton();
		jButton32.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/gc.png"))); // NOI18N
		jButton32.setToolTipText("清空");
		jButton32.setText("清空");
		jButton32.setMargin(new java.awt.Insets(1,1,1,1));
		jButton32.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton32ActionPerformed(evt);
			}
		});

		
		JButton jButton33 = new JButton();
		jButton33.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/all.gif"))); // NOI18N
		jButton33.setToolTipText("\u5168\u9009");
		jButton33.setText("\u5168\u9009");
		jButton33.setMargin(new java.awt.Insets(1,1,1,1));
		jButton33.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton33ActionPerformed(evt);
			}
		});
		
		topPanel.add(jLabel1,BorderLayout.CENTER);
		topPanel.add(jButton1,BorderLayout.EAST);
		
		
		JPanel pn1 =  new JPanel();
		pn1.setLayout(new BorderLayout());
		
		
		
	    JPanel top1 = new JPanel();
	    top1.setPreferredSize(new Dimension(0,30));
	    if(selType[0] != 1)
	    	top1.add(jButton3);
	    top1.add(jButton2);
	    pn1.setPreferredSize(new Dimension((int)(this.getSize().getWidth()/title.length),50));
	    pn1.add(top1,BorderLayout.NORTH);
	    pn1.add(jScrollPane1,BorderLayout.CENTER);
	    mainPanel.add(pn1,BorderLayout.WEST);
	    this.initTable(jTable1,equipList,title[0],Boolean.FALSE,selType[0]);
		
	    if (equipList2 != null) {
	    	JPanel pn2 =  new JPanel();
			pn2.setLayout(new BorderLayout());
	    	JPanel top2 = new JPanel();
		    top2.setPreferredSize(new Dimension(0,30));		
		    if(selType[1] != 1)
		    	top2.add(jButton23);
	 	    top2.add(jButton22);
		    pn2.setPreferredSize(new Dimension(220,50));
		    pn2.add(top2,BorderLayout.NORTH);
		    pn2.add(jScrollPane2,BorderLayout.CENTER);
		    mainPanel.add(pn2,BorderLayout.CENTER);
		    this.initTable(jTable2,equipList2,title[1],Boolean.FALSE,selType[1]);
	    }
	    
	    if (equipList3 != null) {
	    	JPanel pn3 =  new JPanel();
			pn3.setLayout(new BorderLayout());
	    	JPanel top3 = new JPanel();
	 	    top3.setPreferredSize(new Dimension(0,30));
	 	    if(selType[2] != 1)
	 	    	top3.add(jButton33);
	 	    top3.add(jButton32);
		    pn3.setPreferredSize(new Dimension(220,50));
		    pn3.add(top3,BorderLayout.NORTH);
		    pn3.add(jScrollPane3,BorderLayout.CENTER);
		    mainPanel.add(pn3,BorderLayout.EAST);
		    this.initTable(jTable3,equipList3,title[2],Boolean.FALSE,selType[2]);
	    }
		
	}
	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jLabel1 = new JLabel();
		jScrollPane1 = new JScrollPane();
		jTable1 = new JTable();
		jButton1 = new JButton();
		jButton2 = new JButton();
		jButton3 = new JButton();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		addWindowListener(new java.awt.event.WindowAdapter() {
			public void windowClosed(WindowEvent evt) {
				windowcloseAction(evt);
			}
		});

		jLabel1.setText("jLabel1");

		jScrollPane1.setViewportView(jTable1);

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
		jButton1.setToolTipText("\u786e\u5b9a");
		jButton1.setText("\u786e\u5b9a");
		jButton1.setMargin(new java.awt.Insets(1,1,1,1));
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/gc.png"))); // NOI18N
		jButton2.setToolTipText("清空");
		jButton2.setText("清空");
		jButton2.setMargin(new java.awt.Insets(1,1,1,1));
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/all.gif"))); // NOI18N
		jButton3.setToolTipText("\u5168\u9009");
		jButton3.setText("\u5168\u9009");
		jButton3.setMargin(new java.awt.Insets(1,1,1,1));
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(jLabel1,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								295, Short.MAX_VALUE).add(0, 0, 0))
				.add(layout
						.createSequentialGroup()
						.add(jButton3)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jButton2)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 226,
								Short.MAX_VALUE).add(jButton1)
						.addContainerGap())
				.add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1,
						org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 400,
						Short.MAX_VALUE));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(jLabel1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								24,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 22,
								Short.MAX_VALUE)
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.TRAILING)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.LEADING,
												false)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jButton2, 0, 0, Short.MAX_VALUE)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jButton3,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												Short.MAX_VALUE)).add(jButton1))
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jScrollPane1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								320,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//全选
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(Boolean.TRUE);
	}

	//清空
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(Boolean.FALSE);
	}
	
	
	
	//全选
	private void jButton13ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(jTable1,equipList,title[0],Boolean.TRUE,selType[0]);
	}

	//清空
	private void jButton12ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(jTable1,equipList,title[0],Boolean.FALSE,selType[0]);
	}
	
	
	//全选
	private void jButton23ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(jTable2,equipList2,title[1],Boolean.TRUE,selType[1]);
	}

	//清空
	private void jButton22ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(jTable2,equipList2,title[1],Boolean.FALSE,selType[1]);
	}
	
	
	
	//全选
	private void jButton33ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(jTable3,equipList3,title[2],Boolean.TRUE,selType[2]);
	}

	//清空
	private void jButton32ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(jTable3,equipList3,title[2],Boolean.FALSE,selType[2]);
	}
	
	

	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		isCancel = false;
		Object[] temp;
		String choosePd = null;
		Vector<Object> rowData1 = jtablemodel.getRowData();
		for (int i = 0; i < rowData1.size(); i++) {
			temp = (Object[]) rowData1.get(i);
			String  cnm = (String) temp[1];
			for(String equip : equipList) {
				if(equip.equals(cnm)) {
					choosePd = equip;
					break;
				}
			}
			if (temp[0].equals(Boolean.TRUE)) {
				chooseequipList.add(choosePd);
			}
		}
		if(isMustSelect == true && chooseequipList.size() == 0) {
			ShowMessage.view("请至少选择一项！");
			return;
		}
		this.setVisible(false);
		this.dispose();
	}
	
	private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		chooseequipList.clear();
		chooseequipList2.clear();
		chooseequipList3.clear();
		isCancel = false;
		Object[] temp;
		String choosePd = null;
		Vector<Object> rowData1 = jtablemodel.getRowData();
		for (int i = 0; i < rowData1.size(); i++) {
			temp = (Object[]) rowData1.get(i);
			String  cnm = (String) temp[1];
			for(String equip : equipList) {
				if(equip.equals(cnm)) {
					choosePd = equip;
					break;
				}
			}
			if (temp[0].equals(Boolean.TRUE)) {
				chooseequipList.add(choosePd);
			}
		}
		if(selType[0] == 2 && chooseequipList.size() < 2) {
			ShowMessage.view("请至少选择两项"+title[0]+"！");
			return;
		}
		else if(selType[0] == 3 && chooseequipList.size() >=2  && chooseequipList.size() == rowData1.size()) {
			ShowMessage.view(title[0]+"不能全选！");
			return;
		}
		else if(chooseequipList.size() == 0) {
			ShowMessage.view("请至少选择一项"+title[0]+"！");
			return;
		}
		
		if(jtablemodel2 != null) {
			Vector<Object> rowData2 = jtablemodel2.getRowData();
			for (int i = 0; i < rowData2.size(); i++) {
				temp = (Object[]) rowData2.get(i);
				String  cnm = (String) temp[1];
				for(String equip : equipList2) {
					if(equip.equals(cnm)) {
						choosePd = equip;
						break;
					}
				}
				if (temp[0].equals(Boolean.TRUE)) {
					chooseequipList2.add(choosePd);
				}
			}
			if(selType[1] == 2 && chooseequipList2.size() < 2) {
				ShowMessage.view("请至少选择两项"+title[1]+"！");
				return;
			}
			else if(selType[1] == 3 && chooseequipList2.size() >=2 && chooseequipList2.size() == rowData2.size()) {
				ShowMessage.view(title[1]+"不能全选！");
				return;
			}
			else if(chooseequipList2.size() == 0) {
				ShowMessage.view("请至少选择一项"+title[1]+"！");
				return;
			}
		}
		if(jtablemodel3 != null) {
			Vector<Object> rowData3 = jtablemodel3.getRowData();
			for (int i = 0; i < rowData3.size(); i++) {
				temp = (Object[]) rowData3.get(i);
				String  cnm = (String) temp[1];
				for(String equip : equipList3) {
					if(equip.equals(cnm)) {
						choosePd = equip;
						break;
					}
				}
				if (temp[0].equals(Boolean.TRUE)) {
					chooseequipList3.add(choosePd);
				}
			}
			if(selType[2] == 2 && chooseequipList3.size() < 2) {
				ShowMessage.view("请至少选择两项"+title[2]+"！");
				return;
			}
			else if(selType[2] == 3 && chooseequipList3.size() >=2  && chooseequipList3.size() == rowData3.size()) {
				ShowMessage.view(title[2]+"不能全选！");
				return;
			}
			else if(chooseequipList3.size() == 0) {
				ShowMessage.view("请至少选择一项"+title[2]+"！");
				return;
			}
		}
		this.setVisible(false);
		this.dispose();
	}
	
	public void initTable(final JTable table,List<String> equipList ,String str ,Boolean ischoose,int sel) {
		ColorTableModel jtm  = new ColorTableModel();
		if(str.equals(title[0])){  
			jtablemodel = new ColorTableModel();
			jtm= jtablemodel;
		}else if(str.equals(title[1])){
			jtablemodel2 = new ColorTableModel();
			jtm= jtablemodel2;
		}else if(str.equals(title[2])){
			jtablemodel3 = new ColorTableModel();
			jtm= jtablemodel3;
		}

		Vector<Object> rowData = new Vector<Object>();
		String pd = null;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			rowData.add(new Object[] { ischoose, pd });
		}
		jtm.setRowData(rowData);
		jtm.setRowTitle(new String[] { "选择", str});
		table.setRowHeight(30);
		table.setModel(jtm);
		table.getColumnModel().getColumn(0).setMaxWidth(50);
		
		if(sel == 1) {
			table.addMouseListener(new java.awt.event.MouseAdapter() {
				public void mouseClicked(java.awt.event.MouseEvent evt) {
					
					for(int i = 0; i < table.getRowCount();i++) {
						if(i == table.getSelectedRow()) {
							table.setValueAt(true, i, 0);
						}
						else {
							table.setValueAt(false, i, 0);
						}
					}
					table.updateUI();
				}
			});
		}
		
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}
	
	
	public void initTable(Boolean ischoose) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		String pd = null;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			rowData.add(new Object[] { ischoose, pd });
		}
		jtablemodel.setRowData(rowData);
		jtablemodel.setRowTitle(new String[] { "选择", title[0]});
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}

	
	public boolean isCancel() {
		return isCancel;
	}

	public List<String> getChooseEquip() {
		
		return this.chooseequipList;
	}
	
	public List<String> getChooseEquip2() {
			
			return this.chooseequipList2;
		}
	public List<String> getChooseEquip3() {
		
		return this.chooseequipList3;
	}
	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				MCBHChoose dialog = new MCBHChoose(
						new javax.swing.JFrame(), true, null, null, null, null,null);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}
	private void windowcloseAction(WindowEvent evt){
		this.setVisible(false);
	}
	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private JButton jButton1;
	private JButton jButton2;
	private JButton jButton3;
	private JLabel jLabel1;
	private JScrollPane jScrollPane1;
	private JTable jTable1;
	private JTable jTable2;
	private JTable jTable3;
	// End of variables declaration//GEN-END:variables
	private JPanel topPanel;
	private JPanel mainPanel;
	private ColorTableModel jtablemodel = null;
	private ColorTableModel jtablemodel2 = null;
	private ColorTableModel jtablemodel3 = null;
}
