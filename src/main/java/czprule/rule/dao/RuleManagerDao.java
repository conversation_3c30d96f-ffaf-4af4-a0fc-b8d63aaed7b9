package czprule.rule.dao;

import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.model.TreeModel;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RuleManagerDao {

	/**
	 * 返回设备规则主表ID  如果存在满足方式的主表ID返回ID，否则新增一条记录再返回ID。
	 * @param runmode 运行方式
	 * @param devType 设备类型
	 * @param beginstate 起始状态
	 * @param tagState 目标状态
	 * @return 操作任务
	 */
	public  String getZBID(String runmode,String devType,String beginstate,String tagState,String buildType){
		String sql="SELECT T.ZBID FROM "+CBSystemConstants.opcardUser+"t_a_RULEZB T WHERE t.opcode='"+CBSystemConstants.opCode+"' and DEVICERUNMODEL='"+runmode+"' and T.DEVICETYPEID='"+devType+"' AND BEGINSTATUS='"+beginstate+"' AND T.ENDSTATE='"+tagState+"' AND CARDBUILDTYPE='"+buildType+"'";
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		String zbid="";
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			zbid=StringUtils.ObjToString(temp.get("ZBID"));
			return zbid;
		}
		String zbid_seq=java.util.UUID.randomUUID().toString();
		sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_RULEZB(ZBID,DEVICETYPEID,DEVICERUNMODEL,BEGINSTATUS,ENDSTATE,VOLT,OPCODE,CARDBUILDTYPE) VALUES('"+zbid_seq+"','"+devType+"','"+runmode+"','"+beginstate+"','"+tagState+"','','"+CBSystemConstants.opCode+"','"+buildType+"')";
		DBManager.execute(sql);
		return zbid_seq;	
	}
	
	/**
	 * 描述：插入一套从表记录
	 * @param zbid  主表ID
	 * @param wordid 规则ID
	 * @param beginStatus 起始状态
	 * @param state 操作动作
	 * @param orderid  排序
	 */
	public void insertRuleCB(String zbid,String wordid,String orderid,RuleBaseMode rbm){
		String sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_RULECB VALUES('"+zbid+"','"+wordid+"','"+rbm.getBeginStatus()+"','"+rbm.getEndState()+"','"+orderid+"','"+rbm.getTranType()+"','"+rbm.getDeviceruntype()+"')";
		DBManager.execute(sql);
	}
	/**
	 * 描述：删除主表ID对应的规则从表记录
	 * @param zbid 主表ID
	 */
	public void delRuleCB(String zbid){
		String sql="DELETE "+CBSystemConstants.opcardUser+"t_a_RULECB WHERE  F_ZBID='"+zbid+"'";
		DBManager.execute(sql);
	}
	/**
	 * 描述：返回给定条件的规则从表记录集合
	 * @param runmode 接线方式
	 * @param devType  设备类型
	 * @param beginstate 起始状态
	 * @param tagState 操作动作
	 * @param flag 规则类型 0：前提条件 1：执行动作
	 * @return
	 */
	public List<Object[]> getRuleCB(String cardbuildtype,String runmode,String devType,String beginstate,String tagState,String flag,String lineMode){
		String opcode = CBSystemConstants.opRuleCode;
		List<Object[]> ruleCBs=new ArrayList<Object[]>();
		String sql = "SELECT t3.CODETYPE,T3.WORDID,T3.WORDVALUE,T2.BEGINSTATUS,T2.ENDSTATE,T2.TRANTYPE,T2.DEVICERUNTYPE FROM "+CBSystemConstants.opcardUser+"t_a_RULEZB T,"+CBSystemConstants.opcardUser+"t_a_RULECB T2,"+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T3 WHERE\n"
			+ "T.ZBID=T2.F_ZBID AND T2.RULEID=T3.WORDID AND CARDBUILDTYPE='"+cardbuildtype+"' AND T3.RULETYPE='"
			+ flag
			+ "' AND "
			+ "T.DEVICETYPEID='"
			+ devType
			+ "' AND T.DEVICERUNMODEL='"
			+ runmode
			+ "' AND T.BEGINSTATUS='"
			+ beginstate
			+ "' AND T.ENDSTATE='"
			+ tagState
			+ "' AND T.OPCODE='"
			+ opcode + "' ";
		if(!lineMode.equals(""))
			sql = sql + " AND T.LINEMODE='"+lineMode+"'";
		sql = sql + " ORDER BY T2.ORDERID";
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		String wordid="";
		String wordValue = ""; //规则简称
		String beginStatus = ""; //起始状态
		String devstate = ""; //操作动作
		String trantype=""; //变电站类型
		String devRunType=""; //设备运行类型
		String codeType=""; //规则类参数类型
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			codeType=StringUtils.ObjToString(temp.get("CODETYPE"));
			wordid=StringUtils.ObjToString(temp.get("WORDID"));
			wordValue = StringUtils.ObjToString(temp.get("WORDVALUE"));
			CodeNameModel cnmword=new CodeNameModel(wordid, wordValue);
			CodeNameModel cnmb = null;
			CodeNameModel cnmc = null;
			if(!codeType.equals("")) {
				beginStatus = StringUtils.ObjToString(temp.get("BEGINSTATUS"));
				cnmb=new CodeNameModel(beginStatus, CBSystemConstants.getDictionaryName(beginStatus, codeType));
				devstate = StringUtils.ObjToString(temp.get("ENDSTATE"));
				cnmc=new CodeNameModel(devstate, CBSystemConstants.getDictionaryName(devstate, codeType));
			}
//			else {
//				beginStatus = StringUtils.ObjToString(temp.get("BEGINSTATUS"));
//				cnmb=new CodeNameModel(beginStatus, CBSystemConstants.getDeviceStateName(beginStatus));
//				devstate = StringUtils.ObjToString(temp.get("ENDSTATE"));
//				cnmc=new CodeNameModel(devstate, CBSystemConstants.getDeviceStateName(devstate));
//			}
			devRunType = StringUtils.ObjToString(temp.get("DEVICERUNTYPE"));
			CodeNameModel cnmRunType=new CodeNameModel(devRunType, CBSystemConstants.getDictionaryName(devRunType));
			trantype = StringUtils.ObjToString(temp.get("TRANTYPE"));
			
			Object[] rowData = {i+1,trantype,cnmword,cnmRunType,cnmb,cnmc};
			ruleCBs.add(rowData);
		}
		return ruleCBs;
	}
	/**
	 * 描述：返回给定条件的规则从表记录集合
	 * @param runmode 接线方式
	 * @param devType  设备运行类型
	 * @param beginstate 起始状态
	 * @param tagState 操作动作
	 * @param flag 规则类型 0：前提条件 1：执行动作
	 * @return
	 */
	public List<RuleBaseMode> getOldRuleCBClass(String runmode,String devType,String beginstate,String tagState,String flag){
		List<RuleBaseMode> ruleCBs=new ArrayList<RuleBaseMode>();
		String buildType = CBSystemConstants.cardbuildtype;
		String opcode = CBSystemConstants.opRuleCode;
		String sql = "SELECT T3.WORDVALUE,T3.WORDBEANCLASS,T2.BEGINSTATUS,T2.ENDSTATE,T2.TRANTYPE,T2.DEVICERUNTYPE FROM "+CBSystemConstants.opcardUser+"t_a_RULEZB T,"+CBSystemConstants.opcardUser+"t_a_RULECB T2,"+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T3 WHERE\n"
			+ "T.ZBID=T2.F_ZBID AND T2.RULEID=T3.WORDID AND T3.RULETYPE='"
			+ flag
			+ "' AND "
			+ "T.DEVICETYPEID='"
			+ devType
			+ "' AND T.DEVICERUNMODEL='"
			+ runmode
			+ "' AND T.BEGINSTATUS='"
			+ beginstate
//			+ "' AND T.CARDBUILDTYPE='"
//			+ CBSystemConstants.cardbuildtype
			+ "' AND T.ENDSTATE='"
			+ tagState
			+ "' AND T.OPCODE='"
			+ opcode + "' ORDER BY T2.ORDERID";
		List results = DBManager.query(sql);
		
		if(results.size() == 0 && flag.equals("0") && (runmode.equals(CBSystemConstants.RunModelOneLine) || runmode.equals(CBSystemConstants.RunModelDoubleLine) || runmode.equals(CBSystemConstants.RunModelCableLine))) {
			sql = sql.replace(runmode, CBSystemConstants.RunModelOneMotherLine);
			results = DBManager.query(sql);
		}
		
		if(results.size() == 0 && flag.equals("0") && (runmode.equals(CBSystemConstants.RunModelOneLine) || runmode.equals(CBSystemConstants.RunModelDoubleLine) || runmode.equals(CBSystemConstants.RunModelCableLine))) {
			sql = sql.replace(runmode, CBSystemConstants.RunModelOneMotherLine);
			results = DBManager.query(sql);
		}
		if(results.size() == 0 && flag.equals("1")) {
			sql = sql.replace(runmode, CBSystemConstants.RunModelOneMotherLine);
			results = DBManager.query(sql);
		}
		if(results.size()==0){
			sql=sql.replace("T.BEGINSTATUS='"+beginstate+"'", "T.BEGINSTATUS='-1'");
			results=DBManager.query(sql);
		}
		Map temp = new HashMap();
		String wordValue = ""; //规则简称
		String wordbeanclass="";
		String beginStatus = ""; //起始状态
		String devstate = ""; //操作动作
		String trantype=""; //变电站类型
		String deviceRunType=""; //设备运行类型
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			RuleBaseMode rbm=new RuleBaseMode();
			wordValue = StringUtils.ObjToString(temp.get("WORDVALUE"));
			wordbeanclass = StringUtils.ObjToString(temp.get("WORDBEANCLASS"));
			beginStatus = StringUtils.ObjToString(temp.get("BEGINSTATUS"));
			devstate = StringUtils.ObjToString(temp.get("ENDSTATE"));
			trantype = StringUtils.ObjToString(temp.get("TRANTYPE"));
			deviceRunType = StringUtils.ObjToString(temp.get("DEVICERUNTYPE"));
			
			rbm.setRuleValue(wordValue);
			rbm.setRuleBeanClass(wordbeanclass);
			rbm.setBeginStatus(beginStatus);
			rbm.setEndState(devstate);
			rbm.setTranType(trantype);
			rbm.setDeviceruntype(deviceRunType);
			ruleCBs.add(rbm);
		}
		return ruleCBs;
	}
	/**
	 * 描述：返回给定条件的规则从表记录集合
	 * @param runmode 接线方式
	 * @param devType  设备运行类型
	 * @param beginstate 起始状态
	 * @param tagState 操作动作
	 * @param flag 规则类型 0：前提条件 1：执行动作
	 * @return
	 */
	public List<RuleBaseMode> getRuleCBClass(String runmode,String devType,String beginstate,String tagState,String flag){
		if(CBSystemConstants.useOldRole){
			return this.getOldRuleCBClass(runmode, devType, beginstate, tagState, flag);
		}
		List<RuleBaseMode> ruleCBs=new ArrayList<RuleBaseMode>();
		String buildType = CBSystemConstants.cardbuildtype;
		String opcode = CBSystemConstants.opRuleCode;
		String cardbuildtype = "9";
		String sql = "SELECT T3.WORDVALUE,T3.WORDBEANCLASS,T2.BEGINSTATUS,T2.ENDSTATE,T2.TRANTYPE,T2.DEVICERUNTYPE FROM "+CBSystemConstants.opcardUser+"t_a_RULEZB T,"+CBSystemConstants.opcardUser+"t_a_RULECB T2,"+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T3 WHERE\n"
			+ "T.ZBID=T2.F_ZBID AND T2.RULEID=T3.WORDID AND T3.RULETYPE='"
			+ flag
			+ "' AND "
			+ "T.DEVICETYPEID='"
			+ devType
			+ "' AND T.DEVICERUNMODEL='"
			+ runmode
			+ "' AND T.BEGINSTATUS='"
			+ beginstate
			+ "' AND T.CARDBUILDTYPE='"
			+ cardbuildtype
			+ "' AND T.ENDSTATE='"
			+ tagState
			+ "' AND T.OPCODE='"
			+ opcode + "' ORDER BY T2.ORDERID";
		List results = DBManager.query(sql);
		
		if(results.size() == 0) {
			sql = sql.replace("T.CARDBUILDTYPE='9'", "T.CARDBUILDTYPE='"+CBSystemConstants.cardbuildtype+"'");
			results = DBManager.query(sql);
		}
		
		if(results.size() == 0 && flag.equals("0") && (runmode.equals(CBSystemConstants.RunModelOneLine) || runmode.equals(CBSystemConstants.RunModelDoubleLine) || runmode.equals(CBSystemConstants.RunModelCableLine))) {
			sql = sql.replace(runmode, CBSystemConstants.RunModelOneMotherLine);
			results = DBManager.query(sql);
		}
		
		if(results.size() == 0 && flag.equals("0") 
//				&& (runmode.equals(CBSystemConstants.RunModelOneLine) || runmode.equals(CBSystemConstants.RunModelDoubleLine) || runmode.equals(CBSystemConstants.RunModelCableLine))
				) {
			sql = sql.replace(runmode, CBSystemConstants.RunModelOneMotherLine);
			results = DBManager.query(sql);
		}
		if(results.size() == 0 && flag.equals("1")) {
			sql = sql.replace(runmode, CBSystemConstants.RunModelOneMotherLine);
			results = DBManager.query(sql);
		}
		if(results.size()==0){
			sql=sql.replace("T.BEGINSTATUS='"+beginstate+"'", "T.BEGINSTATUS='-1'");
			results=DBManager.query(sql);
		}
		Map temp = new HashMap();
		String wordValue = ""; //规则简称
		String wordbeanclass="";
		String beginStatus = ""; //起始状态
		String devstate = ""; //操作动作
		String trantype=""; //变电站类型
		String deviceRunType=""; //设备运行类型
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			RuleBaseMode rbm=new RuleBaseMode();
			wordValue = StringUtils.ObjToString(temp.get("WORDVALUE"));
			wordbeanclass = StringUtils.ObjToString(temp.get("WORDBEANCLASS"));
			beginStatus = StringUtils.ObjToString(temp.get("BEGINSTATUS"));
			devstate = StringUtils.ObjToString(temp.get("ENDSTATE"));
			trantype = StringUtils.ObjToString(temp.get("TRANTYPE"));
			deviceRunType = StringUtils.ObjToString(temp.get("DEVICERUNTYPE"));
			
			rbm.setRuleValue(wordValue);
			rbm.setRuleBeanClass(wordbeanclass);
			rbm.setBeginStatus(beginStatus);
			rbm.setEndState(devstate);
			rbm.setTranType(trantype);
			rbm.setDeviceruntype(deviceRunType);
			ruleCBs.add(rbm);
		}
		return ruleCBs;
	}
	/**
	 * 获取规则库数据
	 * @param cnm 规则树信息
	 * @return
	 */
	public List<Object[]> getWordBeans(TreeModel cnm){
		List<Object[]> wordbeans=new ArrayList<Object[]>();
		String sql = "SELECT WORDID,T.WORDVALUE,T.WORDBEANCLASS,REMARK FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T where t.equiptype='"+cnm.getEquipType()+"' and t.ruletype='"+cnm.getRuleType()+"' and t.rulearea='"+cnm.getCode()+"' ORDER BY T.WORDVALUE";//黄翔修改 6月  根据节点存的codenamemodel，来查询出该节点下挂的rule类
		String ruleID="";  //规则ID
		String ruleValue = ""; //规则简称
		String ruleClass = ""; //规则操作类
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			ruleID = StringUtils.ObjToString(temp.get("wordid"));
			ruleValue = StringUtils.ObjToString(temp.get("wordvalue"));
			CodeNameModel cnmtemp=new CodeNameModel(ruleID,ruleValue);
			ruleClass = StringUtils.ObjToString(temp.get("wordbeanclass"));
			Object[] rowData = { String.valueOf(i+1),cnmtemp,ruleClass };
			wordbeans.add(rowData);
		}
		return wordbeans;
	}
	/**
	 * 返回规则对象
	 * @param wordid规则ID
	 * @return
	 */
	public RuleBaseMode getRuleBaseModel(String wordid){
		RuleBaseMode rbm=new RuleBaseMode();
		String sql = "SELECT * FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T WHERE T.WORDid='" + wordid + "'";
		List results = DBManager.query(sql);
		Map temp = (Map) results.get(0);
		rbm.setRuleValue(StringUtils.ObjToString(temp.get("WORDVALUE")));
		rbm.setRuleBeanClass(StringUtils.ObjToString(temp.get("WORDBEANCLASS")));
		rbm.setDeviceruntype(StringUtils.ObjToString(temp.get("DEVICERUNTYPES")));
		rbm.setBeginStatus(StringUtils.ObjToString(temp.get("BEGINSTATUS")));
		rbm.setEndState(StringUtils.ObjToString(temp.get("STATE")));
	    rbm.setTranType(StringUtils.ObjToString(temp.get("TRANTYPE")));
	    rbm.setCodetype(StringUtils.ObjToString(temp.get("CODETYPE")));
	    return rbm;
	}
	
	/**
	 * 获取规则库数据
	 * @param cnm 规则树信息
	 * @return
	 */
	public void delWordBean(String wordid){
		String sql = "DELETE "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T WHERE T.WORDID='"+ wordid+ "' ";
		DBManager.execute(sql);
	}
	
	/**
	 * 获取规则库的备注
	 * @param cnm 规则树信息
	 * @return
	 */
	public String queryWordBean(String wordid){
		String result="";
		String sql = "select remark from "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T WHERE T.WORDID='"+ wordid+ "' ";
	    result =DBManager.queryForString(sql);
	    return  result ;
	}
	
	/**
	 * 更新规则库的备注
	 * @param cnm 规则树信息
	 * @return
	 */
	public void updateWordBean(String remark,String wordid){
		String sql = "update "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T set t.remark='"+ remark+ "' WHERE T.WORDID='"+ wordid+ "' ";
	    DBManager.execute(sql);
	}
	
	/**
	 * 获取规则库数据
	 * @param cnm 规则树信息
	 * @return
	 */

	public void saveWordBean(RuleBaseMode rbm,String ruleArea,	String equipType,String runType){
		String sql = "INSERT INTO "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN SELECT '"+
			java.util.UUID.randomUUID().toString()+"','"+ rbm.getRuleValue()+ "','"+ rbm.getRuleBeanClass()+"','"+ equipType+"','"+ runType+ "','" + ruleArea + "','"+rbm.getBeginStatus()+"','"+rbm.getEndState()+"','"+rbm.getDeviceruntype()+"','"+rbm.getTranType()+"',null"+",'"+rbm.getCodetype()+"' from "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN where rownum=1";
	    DBManager.execute(sql);
	}
	
	public List<RuleBaseMode> getProtectAction(String stationID, String equipID, String beginStatus, String endStatus, String isCompleted){
		List<RuleBaseMode> rbmList=new ArrayList<RuleBaseMode>();
		String sql = "select a.protectid,b.actiontype,b.actionword from "+CBSystemConstants.opcardUser+"t_a_protectequip a,"+CBSystemConstants.opcardUser+"t_a_protectword b where a.protecttypeid=b.protecttypeid and a.equipid='"+equipID+"' and b.beginstatus like '%"+beginStatus+"%' and b.endstatus like '%"+endStatus+"%' and ISCOMPLETED='"+isCompleted+"'";	
		List results = DBManager.query(sql);
		for (int i = 0; i < results.size(); i++) {
			Map temp = (Map) results.get(i);
			String protectid = StringUtils.ObjToString(temp.get("protectid"));
			String actiontype = StringUtils.ObjToString(temp.get("actiontype"));
			String actionword= StringUtils.ObjToString(temp.get("actionword"));
			RuleBaseMode rbm = new RuleBaseMode();
			PowerDevice pd = CBSystemConstants.getProtect(stationID, protectid);
			pd.setActionWord(actionword);
			rbm.setPd(pd);
			rbm.setBeginStatus(pd.getDeviceStatus());
			rbm.setEndState(actiontype);
			rbmList.add(rbm);
		}
		return rbmList;
	}
}
