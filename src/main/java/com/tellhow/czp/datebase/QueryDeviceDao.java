package com.tellhow.czp.datebase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.securitycheck.view.CheckWord;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/**
 * 
 * <AUTHOR>
 *
 */
public class QueryDeviceDao {
	
	/**
	 * 输入任何一个线路对象获取线路两端变电站内线路对象
	 * @param inParaMap
	 * @param outParaMap
	 * @return 
	 */
	public static Map<PowerDevice,String> getPowersLineByLine(PowerDevice line){ 
		Map<PowerDevice,String> transMap=new LinkedHashMap<PowerDevice,String>();
		
		if(!OPEService.getService().getPowersLineByLine(line).equals("")){
			List<Map<String,String>> results=DBManager.query(OPEService.getService().getPowersLineByLine(line));
		    Map<String,String> temp=new HashMap<String,String>();
		    String flag="";
		    
		    for (int i = 0; i < results.size(); i++) {
		    	PowerDevice pd=new PowerDevice();
				temp= results.get(i);
				pd.setPowerDeviceID(StringUtils.ObjToString(temp.get("EQUIP_ID")));
				pd.setPowerDeviceName(StringUtils.ObjToString(temp.get("EQUIP_NAME")));
				pd.setPowerVoltGrade(Integer.parseInt(StringUtils.ObjToString(temp.get("VOLTAGE_CODE"))));
				pd.setDeviceType(StringUtils.ObjToString(temp.get("EQUIPTYPE_FLAG")));
				pd.setPowerStationID(StringUtils.ObjToString(temp.get("STATION_ID")));
				pd.setPowerStationName(StringUtils.ObjToString(temp.get("STATION_NAME")));
				if(pd.getPowerStationName().toUpperCase().startsWith("T接")){
					continue;
				}
				
				pd.setDeviceStatus(StringUtils.ObjToString(temp.get("DEVICESTATUS")));
				pd.setDeviceRunType(StringUtils.ObjToString(temp.get("DEVICERUNTYPE")));
				pd.setDeviceRunModel(StringUtils.ObjToString(temp.get("DEVICERUNMODEL")));
				pd.setCimID(StringUtils.ObjToString(temp.get("CIM_ID")));
				flag=StringUtils.ObjToString(temp.get("FLAG"));
				transMap.put(pd,flag);
			}
		}
		return transMap;
	}
	
	/**
	 * 输入任何一个线路对象获取线路两端变电站内线路对象 如果没有关联的用设备名称关联
	 * @param inParaMap
	 * @param outParaMap
	 * @return 
	 */
	public static Map<PowerDevice,String> getPowersLineByLineQY(PowerDevice line){ 
		Map<PowerDevice,String> transMap=new LinkedHashMap<PowerDevice,String>();
		List results=DBManager.query(OPEService.getService().getPowersLineByLine(line));
	    Map temp=new HashMap();
	    String flag="";
	    for (int i = 0; i < results.size(); i++) {
	    	PowerDevice pd=new PowerDevice();
			temp=(Map)results.get(i);
			pd.setPowerDeviceID(StringUtils.ObjToString(temp.get("EQUIP_ID")));
			pd.setPowerDeviceName(StringUtils.ObjToString(temp.get("EQUIP_NAME")));
			pd.setDeviceType(StringUtils.ObjToString(temp.get("EQUIPTYPE_FLAG")));
			pd.setPowerStationID(StringUtils.ObjToString(temp.get("STATION_ID")));
			pd.setPowerStationName(StringUtils.ObjToString(temp.get("STATION_NAME")));
			pd.setDeviceStatus(StringUtils.ObjToString(temp.get("DEVICESTATUS")));
			pd.setDeviceRunType(StringUtils.ObjToString(temp.get("DEVICERUNTYPE")));
			pd.setDeviceRunModel(StringUtils.ObjToString(temp.get("DEVICERUNMODEL")));
			pd.setCimID(StringUtils.ObjToString(temp.get("CIM_ID")));
			flag=StringUtils.ObjToString(temp.get("FLAG"));
			transMap.put(pd,flag);
		}
	    if(transMap.size() == 1 && line.getPowerDeviceName().indexOf("牵") > 0 ){
	    	    transMap.clear();
		    	String sql= "SELECT " + 
				"        T.ID EQUIP_ID," + 
				"        T.NAME EQUIP_NAME," + 
				"        T2.VOLTAGE_CODE," + 
				"        'ACLineSegment' EQUIPTYPE_FLAG," + 
				"        T.ST_ID STATION_ID," + 
				"        T3.STATION_NAME," + 
				"        T.CIM_ID," + 
				"        T4.DEVICESTATUS," + 
				"        T4.DEVICERUNTYPE," + 
				"        T4.DEVICERUNMODEL," + 
				"        t8.flag," + 
				"        T.CIM_ID " + 
				"FROM " + 
				"        "+CBSystemConstants.equipUser+"T_C_ACLINEEND T " + 
				"left join " + 
				"        "+CBSystemConstants.opcardUser+"T_A_LINEWAY t8 " + 
				"on " + 
				"        t.id=t8.lineequipid," + 
				"        "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL T2," + 
				"        "+CBSystemConstants.equipUser+"T_SUBSTATION T3," + 
				"        "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T4 " + 
				" WHERE " + 
				"        T.VOLTAGE_ID=T2.VOLTAGE_ID " + 
				"    AND T.ST_ID     =T3.STATION_ID " + 
				"    AND T.ID        =T4.EQUIPID " + 
				"    AND T.NAME='"+line.getPowerDeviceName()+"'";
				results=DBManager.query(sql);
			    for (int i = 0; i < results.size(); i++) {
			    	PowerDevice pd=new PowerDevice();
					temp=(Map)results.get(i);
					pd.setPowerDeviceID(StringUtils.ObjToString(temp.get("EQUIP_ID")));
					pd.setPowerDeviceName(StringUtils.ObjToString(temp.get("EQUIP_NAME")));
					pd.setDeviceType(StringUtils.ObjToString(temp.get("EQUIPTYPE_FLAG")));
					pd.setPowerStationID(StringUtils.ObjToString(temp.get("STATION_ID")));
					pd.setPowerStationName(StringUtils.ObjToString(temp.get("STATION_NAME")));
					pd.setDeviceStatus(StringUtils.ObjToString(temp.get("DEVICESTATUS")));
					pd.setDeviceRunType(StringUtils.ObjToString(temp.get("DEVICERUNTYPE")));
					pd.setDeviceRunModel(StringUtils.ObjToString(temp.get("DEVICERUNMODEL")));
					pd.setCimID(StringUtils.ObjToString(temp.get("CIM_ID")));
					flag=StringUtils.ObjToString(temp.get("FLAG"));
					transMap.put(pd,flag);
				}
	    }
	    
		return transMap;
	}
	
	public static Map<PowerDevice,String> getPowersLineBySysLine(PowerDevice line){ 
		Map<PowerDevice,String> transMap=new HashMap<PowerDevice,String>();
//		String lineID=line.getPowerDeviceID();
//		String sql="SELECT T.EQUIP_ID,T.EQUIP_NAME,T2.VOLTAGE_CODE,T7.EQUIPTYPE_FLAG,T.STATION_ID,T3.STATION_NAME,T.CIM_ID,T4.DEVICESTATUS,T4.DEVICERUNTYPE,T4.DEVICERUNMODEL,'-1',T.CIM_ID " +
//				   " FROM "+CBSystemConstants.opcardUser+"T_e_EQUIPINFO T,"+CBSystemConstants.opcardUser+"T_e_VOLTAGELEVEL T2,"+CBSystemConstants.opcardUser+"T_e_SUBSTATION T3,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T4,\n" +
//			       " "+CBSystemConstants.opcardUser+"T_e_SUBSTATIONTOPOLOGY T6,"+CBSystemConstants.opcardUser+"T_e_EQUIPTYPE T7 WHERE T.EQUIPTYPE_ID=T7.EQUIPTYPE_ID AND T.VOLTAGE_ID=T2.VOLTAGE_ID AND T.STATION_ID=T3.STATION_ID AND  T.EQUIP_ID=T4.EQUIPID " +
//			       " AND T.EQUIP_ID=T6.EQUIP_ID AND T6.LINE_ID IN (SELECT B.LINE_ID FROM "+CBSystemConstants.opcardUser+"T_e_EQUIPINFO A,"+CBSystemConstants.opcardUser+"T_e_SUBSTATIONTOPOLOGY B WHERE A.EQUIP_ID=B.EQUIP_ID AND B.LINE_ID='"+lineID+"')";
	    //edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getPowersLineBySysLineSql(line));
	    Map temp=new HashMap();
	    String flag="";
	    for (int i = 0; i < results.size(); i++) {
	    	PowerDevice pd=new PowerDevice();
			temp=(Map)results.get(i);
			pd.setPowerDeviceID(StringUtils.ObjToString(temp.get("EQUIP_ID")));
			pd.setPowerDeviceName(StringUtils.ObjToString(temp.get("EQUIP_NAME")));
			pd.setPowerVoltGrade(Integer.parseInt(StringUtils.ObjToString(temp.get("VOLTAGE_CODE"))));
			pd.setDeviceType(StringUtils.ObjToString(temp.get("EQUIPTYPE_FLAG")));
			pd.setPowerStationID(StringUtils.ObjToString(temp.get("STATION_ID")));
			pd.setPowerStationName(StringUtils.ObjToString(temp.get("STATION_NAME")));
			pd.setDeviceStatus(StringUtils.ObjToString(temp.get("DEVICESTATUS")));
			pd.setDeviceRunType(StringUtils.ObjToString(temp.get("DEVICERUNTYPE")));
			pd.setDeviceRunModel(StringUtils.ObjToString(temp.get("DEVICERUNMODEL")));
			pd.setCimID(StringUtils.ObjToString(temp.get("CIM_ID")));
			flag=StringUtils.ObjToString(temp.get("FLAG"));
			transMap.put(pd,flag);
		}
		return transMap;
	}
	
	/**
	 * 获取全网图中线路ID
	 * @param line_cimid
	 * @return
	 */
	public static String getSysLineID(String facLineID){
		String lineID = "";
//		List list = DBManager.queryForList("select t.line_id from "+CBSystemConstants.opcardUser+"T_e_substationtopology t where t.equip_id='"+facLineID+"'");
		//edit 2014.6.26
		List list = DBManager.queryForList(OPEService.getService().QueryDeviceDaoSql2(facLineID));
		if(list.size() > 0)
			lineID = (String)((Map)list.get(0)).get("line_id");
		return lineID;
	}
	
	/**
	 * 获取全网图中线路ID
	 * @param line_cimid
	 * @return
	 */
	public static String getAllMapLine(String line_cimid){
//		return DBManager.queryForString("select t.line_id from  "+CBSystemConstants.opcardUser+"T_e_lineinfo t where t.cim_id=?", line_cimid);
		//edit 2014.6.25
		return DBManager.queryForString(OPEService.getService().QueryDeviceDaoSql(), line_cimid);
	}

	/**
	 * 返回线路对应的潮流信息
	 * @param line_CIM_ID 线路CIMID
	 * @return
	 */
	public static Map<String,String> getLineTidy(String line_CIM_ID){
		Map<String, String> returnMap=new HashMap<String, String>();
//		String sql="SELECT T1.STATION_ID,T.FLAG FROM "+CBSystemConstants.opcardUser+"T_a_LINEWAY T,"+CBSystemConstants.opcardUser+"T_e_EQUIPINFO T1 WHERE\n" +
//			       "T.LINEEQUIPID=T1.EQUIP_ID AND T1.CIM_ID='"+line_CIM_ID+"'";
		//edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getLineTidy(line_CIM_ID));
		Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			returnMap.put(StringUtils.ObjToString(temp.get("STATION_ID")),StringUtils.ObjToString(temp.get("FLAG")));
		}
		return returnMap;
	}
	
	/**
	 * 返回变电站ID
	 * @param StationName 变电站名称
	 * @return
	 */
	public static  String getStationID(String StationName){
		String station_id="";
//		String sql="select * from "+CBSystemConstants.opcardUser+"T_e_substation t Where t.station_name='"+StationName+"'";
		//edit 2014.6.25
		String sql = OPEService.getService().QueryDeviceDaoSql3(StationName);
		List results=DBManager.query(sql);
		Map temp=new HashMap();
		if (results.size()==1) {
			temp=(Map)results.get(0);
			station_id=StringUtils.ObjToString(temp.get("STATION_ID"));
		}
		return station_id;
	}
	
	public static  String getStationIDByKey(String StationName){
		String station_id="";
//		String sql="select * from (select * from "+CBSystemConstants.opcardUser+"T_e_substation t Where t.station_name like '%"+StationName+"%' order by t.station_name) where rownum=1";
		//edit 2014.6.26
		String sql= OPEService.getService().QueryDeviceDaoSql4(StationName);
		List results=DBManager.query(sql);
		Map temp=new HashMap();
		if (results.size()==1) {
			temp=(Map)results.get(0);
			station_id=StringUtils.ObjToString(temp.get("STATION_ID"));
		}
		return station_id;
	}
	
	/**
	 * 返回变电站ID
	 * @param StationName 变电站名称
	 * @return
	 */
	public static  String getStationID_(String EquipID){
		String station_id="";
//		String sql="select t.station_id from "+CBSystemConstants.opcardUser+"T_e_equipinfo t  Where t.equip_id='"+EquipID+"'";
		//edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getStationID_(EquipID));
		Map temp=new HashMap();
		if (results.size()==1) {
			temp=(Map)results.get(0);
			station_id=StringUtils.ObjToString(temp.get("station_id"));
		}
		return station_id;
	}
	
	/**
	 * 返回设备名称
	 * @param EquipID 设备ID
	 * @return
	 */
	public static  String getEquipName(String EquipID){
		String equip_name="";
//		String sql="select t.equip_name from "+CBSystemConstants.opcardUser+"T_e_equipinfo t  Where t.equip_id='"+EquipID+"'";
		//2014.6.24
		List results=DBManager.query(OPEService.getService().getEquipName(EquipID));
		Map temp=new HashMap();
		if (results.size()==1) {
			temp=(Map)results.get(0);
			equip_name=StringUtils.ObjToString(temp.get("equip_name"));
		}
		return equip_name;
	}
	
	/**准确查找  CRAZY
	 * 返回设备ID
	 * @param StationID 厂站ID,EquipName 设备名称
	 * @return
	 */
	public static  String getEquipID(String StationID,String EquipName){
		String equip_id="";
//		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"T_e_equipinfo t Where t.station_id ='"+StationID+"' and t.equip_name like '%"+EquipName+"%'";
		//edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getEquipID(StationID, EquipName));
		Map temp=new HashMap();
		if (results.size()>=1){
			temp=(Map)results.get(0);
			equip_id=StringUtils.ObjToString(temp.get("equip_id"));
		}
		return equip_id;
	}
	/**
	 * 返回设备ID
	 * @param StationID 厂站ID,EquipName 设备名称
	 * @return
	 */
	public static  String getrevdiv(String StationID,String EquipName){
		String equip_id="";
//		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"T_e_equipinfo t Where t.station_id ='"+StationID+"' and replace(t.equip_name,'-','') like '%"+EquipName+"%'";
		//edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getrevdiv(StationID, EquipName));
		Map temp=new HashMap();
		if (results.size()>=1){
			temp=(Map)results.get(0);
			equip_id=StringUtils.ObjToString(temp.get("equip_id"));
		}
		return equip_id;
	}
	/**
	 * 返回设备ID
	 * @param StationID 厂站ID,EquipName 接地刀闸名
	 * @return
	 */
	public static  String getGKdiv(String StationID,String EquipName){
		String equip_id="";
//		String tempname=EquipName.substring(0, 4);
//		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"T_e_equipinfo t,"+CBSystemConstants.opcardUser+"T_e_equiptype a Where t.equiptype_id=a.equiptype_id and t.station_id ='"+StationID+"' and a.equiptype_flag='GroundDisconnector' and t.equip_name like '%"+tempname+"%'";
		//edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getGKdiv(StationID, EquipName));
		Map temp=new HashMap();
		if (results.size()==1){
			temp=(Map)results.get(0);
			equip_id=StringUtils.ObjToString(temp.get("equip_id"));
		}
		return equip_id;
	}
	/**
	 * 返回设备ID
	 * @param StationID 厂站ID,EquipName 设备名称
	 * @return
	 */
	public static  String getdiv(String StationID,String EquipName){
		String equip_id="";
//		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"T_e_equipinfo t Where t.station_id ='"+StationID+"' and t.equip_name='"+EquipName+"'";
		//edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getdiv(StationID, EquipName));
		Map temp=new HashMap();
		if (results.size()==1){
			temp=(Map)results.get(0);
			equip_id=StringUtils.ObjToString(temp.get("equip_id"));
		}
		return equip_id;
	}
	
	/**
	 * 获取线路ID
	 * @param line_cimid
	 * @return
	 */
	public static String getLineByName(String lineName){
		String lineID = "";
//		List list = DBManager.queryForList("select t.line_id from "+CBSystemConstants.opcardUser+"T_e_lineinfo t where t.line_name like '%"+lineName+"%'");
		//edit 2014.6.25
		List list = DBManager.queryForList(OPEService.getService().QueryDeviceDaoSql1()+lineName+"%'");
		if(list.size() > 0)
			lineID = (String)((Map)list.get(0)).get("line_id");
		return lineID;
		
	}
	
	/**
	 * 返回变电站SVG名称
	 * @param StationID 厂站ID,EquipName 设备名称
	 * @return
	 */
	public static  String getTransSVGName(String StationID){
		String transSVGName="";
//		String sql="select t.station_name from "+CBSystemConstants.opcardUser+"T_e_substation t where t.station_id='"+StationID+"'";
		//edit 2014.6.25
		String sql=OPEService.getService().QueryDeviceDaoSql5(StationID);
		List results=DBManager.query(sql);
		Map temp=new HashMap();
		if (results.size()==1){
			temp=(Map)results.get(0);
			transSVGName=StringUtils.ObjToString(temp.get("station_name"));
		}
		return "nx_"+transSVGName+".fac.svg";
	}

	public String getswitchdiv(String StationID, String EquipName) {
		String equip_id="";
		String tempname=EquipName;
		//String tempname=EquipName.substring(0, 4);
//		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"T_e_equipinfo t,"+CBSystemConstants.opcardUser+"T_e_equiptype a Where t.equiptype_id=a.equiptype_id and t.station_id ='"+StationID+"' and a.equiptype_flag='Breaker' and t.equip_name like '%"+tempname+"%'";
		//edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getswitchdiv(StationID, EquipName));
		Map temp=new HashMap();
		if (results.size()==1){
			temp=(Map)results.get(0);
			equip_id=StringUtils.ObjToString(temp.get("equip_id"));
		}
		return equip_id;
	}
	
	public String[] getEquipID(String stationID, String equipNum, String equipType) {
		String[] equip_id = null;
//		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"T_e_equipinfo t,"+CBSystemConstants.opcardUser+"T_e_equiptype a Where t.equiptype_id=a.equiptype_id and t.station_id ='"+stationID+"' and a.equiptype_flag='"+equipType+"' and t.equip_name like '%"+equipNum+"%'";
		//edit 2014.6.25
		List results=DBManager.query(OPEService.getService().getEquipID(stationID, equipNum, equipType));
		Map temp=new HashMap();
		if (results.size() >= 1){
			equip_id = new String[results.size()];
			for(int i = 0; i < results.size(); i++) {
				temp=(Map)results.get(i);
				equip_id[i]=StringUtils.ObjToString(temp.get("equip_id"));
			}
		}
		return equip_id;
	}
	
	public static String getEquipIDByNameType(String stationID, String equipNum, String equipType) {
		equipNum=equipNum.replace("开关", "").replace("地刀", "").replace("刀闸", "");
		String equip_id = "";
		String equip_name="";
//		String sql="select t.equip_id,t.equip_name from "+CBSystemConstants.opcardUser+"T_e_equipinfo t,"+CBSystemConstants.opcardUser+"T_e_equiptype a Where t.equiptype_id=a.equiptype_id and t.station_id ='"+stationID+"' and  t.equip_name like '%"+equipNum+"%'";
		//edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getEquipIDByNameType(stationID, equipNum, equipType));
		if(results.size()==0){
			equipType=SystemConstants.SwitchFlowGroundLine;
			results=results=DBManager.query(OPEService.getService().getEquipIDByNameType(stationID, equipNum, equipType));
		}
		Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp = (Map)results.get(i);
			equip_name=StringUtils.ObjToString(temp.get("equip_name"));
			String en=CheckWord.getEquipNum(equip_name);
			if(en.equals(equipNum)&&equip_name.contains(equipType)){
				return StringUtils.ObjToString(temp.get("equip_id"));
			}
		}
		for (int i = 0; i < results.size(); i++) {
			temp = (Map)results.get(i);
			equip_name=StringUtils.ObjToString(temp.get("equip_name"));
			String en=CheckWord.getEquipNum(equip_name);
			if(en.equals(equipNum)){
				return StringUtils.ObjToString(temp.get("equip_id"));
			}
		}
		return "";
	}
	
	
	
	//返回电压等级集合
	public List<CodeNameModel> getDeviceVolts() {
        List<CodeNameModel>  deviceVolts=new ArrayList<CodeNameModel>();
//		String sql="select t.voltage_code,t.voltage_name from "+CBSystemConstants.opcardUser+"T_e_voltagelevel t order by t.voltage_code";
        //edit 2014.6.26
        String sql=OPEService.getService().QueryDeviceDaoSql2();
        List results=DBManager.query(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			cnm.setCode(StringUtils.ObjToString(temp.get("voltage_code")));
			cnm.setName(StringUtils.ObjToString(temp.get("voltage_name")));
			deviceVolts.add(cnm);
		}
		return deviceVolts;	
	}
	
	/**
	 * 根据保护类型获取所有的保护
	 * */
	public List<CodeNameModel> getProtectByType(PowerDevice pd,String protectType) {
		if(pd==null){
			return null;
		}
        List<CodeNameModel>  protects=new ArrayList<CodeNameModel>();
//        String sql="select p.protecttypeid,p.protecttypename,p.runtype from "+CBSystemConstants.opcardUser+"T_a_protectinfo p ,"+CBSystemConstants.opcardUser+"T_e_equiptype t  where protectkind='"+protectType+"' and t.equiptype_id=p.equiptypeid and t.cim_id ='"+pd.getDeviceType()+"'";
        //edit 2014.6.24
        List results=DBManager.query(OPEService.getService().getProtectByTypeSql(pd, protectType));
        Map temp=new HashMap();
        String runtype;
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			runtype=StringUtils.ObjToString(temp.get("runtype"));
			if("".equals(runtype)||runtype.equals(pd.getDeviceRunType())){
				cnm.setCode(StringUtils.ObjToString(temp.get("protecttypeid")));
			    cnm.setName(StringUtils.ObjToString(temp.get("protecttypename")));
			    protects.add(cnm);
			}
			
		}
		return protects;	
	}
	
	/**
	 * 线路潮流数据库设置
	 * @param lineid 线路ID
	 * @param flag  潮流标志
	 */
	public static void UpdateLineTidy(String lineid,String flag){
		String sql =  "";
		sql="select count(*) from  "+CBSystemConstants.opcardUser+"T_a_LINEWAY WHERE LINEEQUIPID='"+lineid+"'";
		int count = DBManager.queryForInt(sql);
		if(count == 1)
			sql="UPDATE  "+CBSystemConstants.opcardUser+"T_a_LINEWAY SET FLAG='"+flag+"' WHERE LINEEQUIPID='"+lineid+"'";
		else
			sql="insert into "+CBSystemConstants.opcardUser+"T_a_LINEWAY(LINEEQUIPID,FLAG) VALUES('"+lineid+"',"+flag+")";
		DBManager.execute(sql);
	}
	
	public static  String getStationIDByName(String StationName){
		if("".equals(StationName)){
			return "";
		}
		char stationChar[] = StationName.toCharArray();
		if(StationName.indexOf("厂")>0){
			StationName = StationName.replace("电厂", "").replace("厂", "");
			StationName = StationName + "%厂";
		}
		else if(StationName.indexOf("站")>0){
			StationName = StationName.replace("变电站", "").replace("站", "");
			StationName = StationName + "%站";
		}
		else if(StationName.indexOf("变")>0){
			StationName = StationName.replace("变", "");
			StationName = StationName + "%变";
		}
		else {
			StationName=StationName.replace("变电站", "").replace("电厂", "");
			StationName=StationName.replace("站", "").replace("厂", "");
		}
		String stationID = "";
		CodeNameModel cnm=null;
		
//		String sql="select STATION_ID from "+CBSystemConstants.opcardUser+"T_e_substation t Where replace(replace(t.station_name,'厂',''),'站','') = '"+StationName+"'";
		//edit 2014.6.26
		String sql= OPEService.getService().QueryDeviceDaoSql6(StationName);
		List<Map> results=DBManager.query(sql);
		for(Iterator it = results.iterator(); it.hasNext();){
			Map temp=(Map)it.next();
			String stationname=StringUtils.ObjToString(temp.get("station_name"));
			if(stationname.indexOf("告警") >= 0){
				it.remove();
			}
		}
		if(results.size()>0) {
			Map temp=(Map)results.get(0);
			stationID=StringUtils.ObjToString(temp.get("STATION_ID"));
		}
		else {
			sql= OPEService.getService().QueryDeviceDaoSql7(StationName);
			results=DBManager.query(sql);
			if(results.size()>0) {
				for(Map temp : results) {
					Map deviceMap = CBSystemConstants.getMapPowerStationDevice().get(temp.get("STATION_ID"));
					if(deviceMap != null && deviceMap.size() > 0)
						stationID = StringUtils.ObjToString(temp.get("STATION_ID"));
				}
				/* (Gny)
				 * 寻找最接近用户预期的电厂 。
				 * find the most possible station. */
				if (stationID.equals("")) {
					double max = 0;
					Map relStation = new HashMap();
					double[] sb = new double[results.size()];
					for (int i = 0; i < results.size(); i++) { 	//对比所搜索出的可能的厂站
						Map temp = results.get(i);
						String sn = StringUtils.ObjToString(temp
								.get("STATION_NAME"));
						sn = sn.replaceAll("^([\u4e00-\u9fa5]*.)", "");
						for (char sc : stationChar) {
							if (sn.contains(String.valueOf(sc))) {
								sb[i]++;
							}
						}
						sb[i] = sb[i] / sn.length();	// 比率越高越接近实际想要的厂站
						if (sb[i] == max) {		// 如果厂站名称一样优先获取包含‘站’的厂站
							if (sn.indexOf("站") > -1) {
								relStation = (Map) results.get(i);
							}
						} else {
							relStation = sb[i] - max > 0 ? (Map) results.get(i)
									: relStation;
							max = sb[i] - max > 0 ? sb[i] : max;
						}
					}
					stationID = StringUtils.ObjToString(relStation
							.get("STATION_ID"));
				}
			}
		}
		return stationID;
	}
	
	/**
	  * 创建时间 2013年12月9日 下午5:21:54
	  * 根据明细id获取厂站id
	  * <AUTHOR>
	  * @Title getStatinByMxid
	  * @param mxId
	  * @return
	  */
	public static String getStatinByMxid(String mxId){
		String sql="select t.czdw from "+CBSystemConstants.opcardUser+"T_a_czpmx t where t.mxid=? ";
		List<Map> results=DBManager.queryForList(sql,mxId);
		if(results.size()==0)
			return "";
		String czdw=StringUtils.ObjToString(results.get(0).get("CZDW"));
		czdw=czdw.replace("厂", "").replace("站", "");
//	    sql="select s.station_id from "+CBSystemConstants.opcardUser+"T_e_substation s where s.station_name like ?";
		//edit 2014.6.26
		//监控特殊处理
		
		if(czdw.indexOf("监控")>-1){
			String sqll="select t.cznr from "+CBSystemConstants.opcardUser+"T_a_czpmx t where t.mxid=? ";
			List<Map> resultsl=DBManager.queryForList(sqll,mxId);
			String cznr=StringUtils.ObjToString(resultsl.get(0).get("CZNR"));
			int start=cznr.indexOf("将");
			int end=cznr.indexOf("站");
			if(start>-1&&end>-1){
				czdw=cznr.substring(start+1, end+1);
				int fi=czdw.indexOf("kV");
				if(fi>-1){
					czdw=czdw.substring(fi+2,czdw.length());
				}
			}
		}
		sql=OPEService.getService().QueryDeviceDaoSql8();
		results=DBManager.queryForList(sql,"%"+czdw+"%");
		String stationID = "";
		for(Iterator it = results.iterator();it.hasNext();){
			Map temp=(Map)it.next();
			String stationname=StringUtils.ObjToString(temp.get("station_name"));
			if(stationname.indexOf("告警") >= 0){
				it.remove();
			}
		}
		if(results.size()>0) {
			Map temp=(Map)results.get(0);
			stationID=StringUtils.ObjToString(temp.get("station_id"));
		}
		return stationID;
	}
	
	/**
	 * 返回变电站ID
	 * @param StationName 变电站名称
	 * @return
	 */
	public static  List<CodeNameModel> getStationNames(String StationName){
		List<CodeNameModel> stationNames=new ArrayList<CodeNameModel>();
		CodeNameModel cnm=null;
//		String sql="select STATION_ID,STATION_NAME from "+CBSystemConstants.opcardUser+"T_e_substation t Where t.station_name like '%"+StationName+"%'";
		//edit 2014.6.26
		String sql=OPEService.getService().QueryDeviceDaoSql9(StationName);
		List results=DBManager.query(sql);
		Map temp=new HashMap();
		for (int i=0;i<results.size();i++) {
			temp=(Map)results.get(i);
			String code=StringUtils.ObjToString(temp.get("STATION_ID"));
			String name=StringUtils.ObjToString(temp.get("STATION_NAME"));
			cnm=new CodeNameModel(code,name);
			stationNames.add(cnm);
		}
		return stationNames;
	}
	
	public static PowerDevice getDevice(String devCode, String StationName,String devType) {
		String equip_id="";
		String station_id="";
		PowerDevice pd=null;
		String tj="";
		if(devType.equals(SystemConstants.InOutLine)){
			tj="t.equip_name like '%"+devCode.substring(0,1)+"%' and t.equip_name like '%"+devCode.substring(1,2)+"%' ";
			if(devCode.substring(2,3).equals("Ⅰ"))
				tj=tj+" and (t.equip_name like '%Ⅰ%' or t.equip_name like '%I%') and  t.equip_name not like '%II%'" ;
			if(devCode.substring(2,3).equals("Ⅱ"))
				tj=tj+" and (t.equip_name like '%Ⅱ%' or t.equip_name like '%II%')" ;
			
		}else{
			if(devCode.indexOf("、")>0)
			    tj="t.equip_name like '%"+devCode.split("、")[1]+"%'";
			else 
				tj="t.equip_name like '%"+devCode+"%'";
		}
		
		
		
//		String sql="select t.equip_id,t.station_id from "+CBSystemConstants.opcardUser+"T_e_equipinfo t,"+CBSystemConstants.opcardUser+"T_e_substation s,"+CBSystemConstants.opcardUser+"T_e_equiptype t2\n" + 
//                   " Where t.station_id=s.station_id and t.equiptype_id=t2.equiptype_id and " +
//                   tj+" and s.station_name like '%"+StationName+"%' and t2.equiptype_flag='"+devType+"'";
		//edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getDevice(devCode, StationName, devType));
		Map temp=new HashMap();
		if (results.size()==1){
			temp=(Map)results.get(0);
			equip_id=StringUtils.ObjToString(temp.get("equip_id"));
			station_id=StringUtils.ObjToString(temp.get("station_id"));
		}
		if(SystemConstants.getMapPowerStationDevice(station_id, equip_id)!=null)
		     pd=(PowerDevice)SystemConstants.getMapPowerStationDevice(station_id, equip_id);
		return pd;
	}

	
	/**
	  * 创建时间 2013年12月9日 下午7:52:39
	  * 根据mxid获取主表id然后获取线路电源侧信息 并且加入缓存
	  * <AUTHOR>
	  * @Title loadLineSourceInfo
	  * @param mxid
	  */
	public static void loadLineSourceInfo(String mxid) {
		
		String sql="select * from "+CBSystemConstants.opcardUser+"T_a_czpmx t,"+CBSystemConstants.opcardUser+"T_a_line_src_load l WHERE t.mxid=? and t.f_zbid=l.zbid";
		List results=DBManager.queryForList(sql, mxid);
		Map temp;
		for (int i=0;i<results.size();i++) {
			temp=(Map)results.get(i);
			String id=StringUtils.ObjToString(temp.get("XLID"));
			String st=StringUtils.ObjToString(temp.get("SRC"));
			CBSystemConstants.LineSource.put(id, CBSystemConstants.getPowerStation(st));
		}
	}

	/**
	  * 创建时间 2013年12月10日 上午8:43:02
	  * 根据明细id获取rbm集合
	  * <AUTHOR>
	  * @Title getRbmByMxid
	  * @param mxid
	  * @param stationID
	  * @return
	  */
	public static ArrayList<RuleBaseMode> getRbmByMxid(String mxid,String stationID) {
		
		String sql="select * from "+CBSystemConstants.opcardUser+"T_a_czpactionstate t where t.cardmxid=? order by t.stateorder";
		List results = DBManager.queryForList(sql, mxid);
		Map temp;
		ArrayList<RuleBaseMode> rbms=new ArrayList<RuleBaseMode>();
		for (int i=0;i<results.size();i++) {
			RuleBaseMode rbm=new RuleBaseMode();
			temp=(Map)results.get(i);
			String id=StringUtils.ObjToString(temp.get("equipid"));
			String begin=StringUtils.ObjToString(temp.get("beginstatus"));
			String end=StringUtils.ObjToString(temp.get("endstate"));
			PowerDevice pd=CBSystemConstants.getPowerDevice(stationID, id);
			rbm.setBeginStatus(begin);
			rbm.setPd(pd);
			rbm.setEndState(end);
			rbms.add(rbm);
		}
		return rbms;
	}

	/**
	  * 创建时间 2013年12月10日 上午9:38:48
	  * 根据明细id获取最大停电范围信息
	  * <AUTHOR>
	  * @Title getMaxLoadOffByMxid
	  * @param mxid
	  * @return
	  */
	public static Boolean getMaxLoadOffByMxid(String mxid) {
		String sql="select lf.ismaxloadoff from "+CBSystemConstants.opcardUser+"T_a_czpmx mx,"+CBSystemConstants.opcardUser+"T_a_is_max_load_off lf where mx.mxid=? and lf.zbid=mx.f_zbid";
		List results = DBManager.queryForList(sql, mxid);
		Map temp;
		for (int i=0;i<results.size();i++) {
			RuleBaseMode rbm=new RuleBaseMode();
			temp=(Map)results.get(i);
			String loadoff=StringUtils.ObjToString(temp.get("ISMAXLOADOFF"));
			if(loadoff.equals("1")){
				return false;
			}else{
				return true;
			}
		}
		return null;
	}
	/**
	 * 
	 * @param unitCode 区域编码
	 * @param roleCode 主网配网权限
	 * @return opcode 操作码
	 */
	public static String getOpcode(String unitCode,String roleCode){
		String returnValue = "";
		if(unitCode == "" || roleCode == "")
			return returnValue;
		String sql="SELECT T.OPCODE FROM  "+CBSystemConstants.opcardUser+"T_A_OPCODEINFO T WHERE T.AREANO='"+unitCode+"' AND T.ROLECODE='"+roleCode+"'";
		List results = DBManager.queryForList(sql);
		Map temp;
		if(results.size()>0) {
			temp=(Map)results.get(0);
			returnValue = StringUtils.ObjToString(temp.get("OPCODE"));
		}
		return returnValue;
	}
	/**
	 * 
	 * @param unitCode 区域编码
	 * @param roleCode 主网配网权限
	 * @return 全区域 opcode 操作码
	 */
	public static List getAllAreaOpcode(String unitCode,String roleCode){
		if(unitCode == "" || roleCode == "")
			return new ArrayList();
		String sql="SELECT T.OPCODE FROM  "+CBSystemConstants.opcardUser+"T_A_OPCODEINFO T WHERE T.AREANO like '"+unitCode.substring(0,5)+"%' AND T.ROLECODE='"+roleCode+"'";
		List results = DBManager.queryForList(sql);
		return results;
	}
	/**
	 * 
	 * @param unitCode 区域编码
	 * @param roleCode 主网配网权限
	 * @return 全区域 opcode 操作码
	 */
	public static String getParentDeviceStateName(String statecode){
		String returnValue = "";
		if(statecode == "" || statecode == "")
			return returnValue;
		String sql="select t.statename from "+CBSystemConstants.opcardUser+"T_a_devicestateinfo t where t.statecode='"+statecode+"'";
		List results = DBManager.queryForList(sql);
		Map temp;
		if(results.size()>0) {
			temp=(Map)results.get(0);
			returnValue = StringUtils.ObjToString(temp.get("statename"));
		}
		return returnValue;
	}
	
	
	
}
