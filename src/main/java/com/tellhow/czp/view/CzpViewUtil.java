package com.tellhow.czp.view;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import com.tellhow.czp.user.User;
import com.tellhow.czp.user.UserDao;

import czprule.system.CBSystemConstants;

public class CzpViewUtil {
	private static Properties pro=new Properties();
	static {
		try {
			InputStream inputStream = CzpViewUtil.class.getResourceAsStream("/czpview.properties");
		    pro.load(inputStream);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	public static void initview(String view){
		CzpViewInterface cvi=getInstance(view);
		cvi.init(CBSystemConstants.SYSTEM_TITLE);
		cvi.begin();
		//用户爱好设置
		User user=CBSystemConstants.getUser();
		UserDao userdao=new UserDao();
		userdao.LoadUserLike(user);
	}
	
	/**
	 * 根据不同的字符串获取不同的子类
	 * */
	private static CzpViewInterface getInstance(String view){
		CzpViewInterface cvi;
		try {
			cvi=(CzpViewInterface) Class.forName(pro.getProperty(view)).newInstance();
		} catch (Exception e) {
			return null;
		}
		return cvi;
		
	}
}
