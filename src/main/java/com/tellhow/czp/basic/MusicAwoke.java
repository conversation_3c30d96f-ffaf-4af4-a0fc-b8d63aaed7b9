package com.tellhow.czp.basic;

import java.io.FileInputStream;
import java.util.Date;
import java.util.Timer;

import javax.swing.JFrame;
import javax.swing.JOptionPane;

import javazoom.jl.player.Player;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.system.CBSystemConstants;

/**
 * 定时提醒功能
 * 
 * <AUTHOR>
 * 
 */
public class MusicAwoke {
	public Timer NewTime;

	public Timer getNewTime() {
		return NewTime;
	}

	public void setNewTime(Timer newTime) {
		NewTime = newTime;
	}

	public void execute(Timer timer, Date date) {
		setNewTime(timer);
		MyTimerTask m1 = new MyTimerTask();
		timer.schedule(m1, date, 100 * 100);
	}

	public void stopMusic(Timer newTime) {
		newTime.cancel();
		JFrame mf = SystemConstants.getMainFrame();
		JOptionPane.showMessageDialog(mf, "消息提示：距离“下发时间”已经一个小时了，请尽快下发“回令时间”！",
				CBSystemConstants.SYSTEM_TITLE,
				javax.swing.JOptionPane.INFORMATION_MESSAGE);
		return;
	}

	class MyTimerTask extends java.util.TimerTask {
		int i = 0;

		@Override
		public void run() {
			i++;
			if (i == 2) {
				try {
					FileInputStream mp3_file = new FileInputStream(
							"music/awoke.mp3");
					Player mp3 = new Player(mp3_file);
					mp3.play();
					MusicAwoke.this.stopMusic(MusicAwoke.this.getNewTime());
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}
}
