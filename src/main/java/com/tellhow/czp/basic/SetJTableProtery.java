package com.tellhow.czp.basic;

import java.awt.Color;
import java.awt.Component;
import java.util.List;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableCellRenderer;

public class SetJTableProtery {
	public void getTableHeader(JTable table) {
		JTableHeader tableHeader = table.getTableHeader();
		tableHeader.setReorderingAllowed(false); // 设置表格列不可重排
		DefaultTableCellRenderer hr = (DefaultTableCellRenderer) tableHeader.getDefaultRenderer(); // 获得表格头的单元格对象
		hr.setHorizontalAlignment(DefaultTableCellRenderer.CENTER); // 列名居中
	}

	public void getDefaultRenderer(Class<?> columnClass, JTable table) {
		DefaultTableCellRenderer cr = (DefaultTableCellRenderer) table.getDefaultRenderer(columnClass);
		cr.setHorizontalAlignment(DefaultTableCellRenderer.CENTER); // 单元格内容居中
	}

	/**
	 * 单元格内容居左
	 * 
	 * @param columnClass
	 * @param table
	 */
	public void getDefaultLeft(Class<?> columnClass, JTable table) {
		DefaultTableCellRenderer cr = (DefaultTableCellRenderer) table.getDefaultRenderer(columnClass);
		cr.setHorizontalAlignment(DefaultTableCellRenderer.LEFT); // 单元格内容居左
	}

	/**
	 * 功能：JTable行背景换色
	 */
	@SuppressWarnings("serial")
	public void makeFace_bak(JTable table) {
		try {
			DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
				public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected,
															   boolean hasFocus, int row, int column) {
					if (row % 2 == 0)
						setBackground(Color.white); // 设置奇数行底色
					else if (row % 2 == 1)
						setBackground(new Color(206, 231, 255)); // 设置偶数行底色
					if (column == 0 || column == 1||table.getColumnName(column).endsWith("序")) {
						setHorizontalAlignment(SwingConstants.CENTER);
					} else {
						setHorizontalAlignment(SwingConstants.LEFT);
					}
					return super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
				}
			};
			for (int i = 0; i < table.getColumnCount(); i++) {
				// tcr.setHorizontalAlignment(javax.swing.SwingConstants.CENTER);
				table.getColumn(table.getColumnName(i)).setCellRenderer(tcr);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}

	public void makeFace(JTable table) {
		try {
			DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
				private final JTextArea textArea = new JTextArea(); // 将 JTextArea 作为类的成员变量

				{
					// 初始化 JTextArea 的通用属性
					textArea.setLineWrap(true);
					textArea.setWrapStyleWord(true);
					textArea.setEditable(false); // 设置为不可编辑
					textArea.setFocusable(false); // 禁用焦点
				}

				public Component getTableCellRendererComponent(JTable table, Object value,
															   boolean isSelected, boolean hasFocus, int row, int column) {
					// 获取行是否被选中
					boolean isRowSelected = table.isRowSelected(row);
					// 处理"操作内容"列
					if (table.getColumnName(column).equals("操作内容")) {
						// 设置背景色和前景色
						if (isSelected) {
							textArea.setBackground(table.getSelectionBackground());
							textArea.setForeground(table.getSelectionForeground());
						} else {
							textArea.setForeground(table.getForeground());
							textArea.setBackground(row % 2 == 0 ? Color.white : new Color(206, 231, 255));
						}

						// 设置其他属性
						textArea.setFont(table.getFont());
						textArea.setText(value == null ? "" : value.toString());

						// 计算首选高度
						textArea.setSize(table.getColumnModel().getColumn(column).getWidth(), 10);
						int maxPreferredHeight = Math.max(25, textArea.getPreferredSize().height);

						// 设置行高
						if (table.getRowHeight(row) != maxPreferredHeight) {
							table.setRowHeight(row, maxPreferredHeight);
						}

						// 设置边框
						if (hasFocus) {
							textArea.setBorder(UIManager.getBorder("Table.focusCellHighlightBorder"));
						} else {
							textArea.setBorder(new EmptyBorder(1, 1, 1, 1));
						}

						return textArea;
					}
					// 处理其他列
					else {
						Component comp = super.getTableCellRendererComponent(table, value, isSelected,
								hasFocus, row, column);

						// 设置背景色
						if (!isSelected) {
							comp.setBackground(row % 2 == 0 ? Color.white : new Color(206, 231, 255));
						}

						// 设置对齐方式
						if (column == 0 || column == 1 || table.getColumnName(column).endsWith("序")) {
							setHorizontalAlignment(SwingConstants.CENTER);
						} else {
							setHorizontalAlignment(SwingConstants.LEFT);
						}

						return comp;
					}
				}
			};

			// 应用渲染器到所有列
			for (int i = 0; i < table.getColumnCount(); i++) {
				table.getColumn(table.getColumnName(i)).setCellRenderer(tcr);
			}

//			// 设置选择模式
//			table.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
//			table.setRowSelectionAllowed(true);  // 启用行选择
//			table.setColumnSelectionAllowed(true);  // 启用列选择
//			table.setCellSelectionEnabled(true);  // 启用单元格选择
//
//			// 设置选择行为
//			table.getSelectionModel().setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
//			table.getColumnModel().getSelectionModel().setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);

		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	/**
	 * 功能：JTable行背景换色          ---殷旺旺 修改
	 * @param table
	 * @param colnumList
	 * <AUTHOR>
	 */
	public void makeFaceCenter(JTable table,final List<Integer> colnumList) {
		try {
			DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
				public Component getTableCellRendererComponent(JTable table,
						Object value, boolean isSelected, boolean hasFocus,
						int row, int column) {
					if (row % 2 == 0)
						setBackground(Color.white); // 设置奇数行底色
					else if (row % 2 == 1)
						setBackground(new Color(206, 231, 255)); // 设置偶数行底色
					if(colnumList.contains(column)){
					    setHorizontalAlignment(SwingConstants.CENTER);
					}else{
						setHorizontalAlignment(SwingConstants.LEFT);
					}
					return super.getTableCellRendererComponent(table, value,
							isSelected, hasFocus, row, column);
				}
			};
			for (int i = 0; i < table.getColumnCount(); i++) {
				table.getColumn(table.getColumnName(i)).setCellRenderer(tcr);
			}
		   
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
}

