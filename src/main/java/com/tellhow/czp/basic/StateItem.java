/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.tellhow.czp.basic;

/**
 *
 * <AUTHOR>
 */
public class StateItem {
    private String id="";//标识id
    private String name="";//显示字符
    private String type="";//状态类型
    public StateItem(){
        
    }
    public StateItem(String id,String name,String type){
        this.id=id;
        if(name==null){
            this.name= "";
        }else{
            this.name = name;
        }
        if(type==null){
            this.type= "";
        }else{
            this.type = type;
        }
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        if(name==null){
            this.name= "";
        }else{
            this.name = name;
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final StateItem other = (StateItem) obj;
        if (this.name != other.name && (this.name == null || !this.name.equals(other.name))) {
            return false;
        }
        other.setId(id);
        other.setType(type);
        return true;
    }


    @Override
    public String toString() {
        return name;
    }
}
