package com.tellhow.czp.basic;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintStream;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.swing.JOptionPane;
import javax.swing.UIManager;

public class ReaderConfiguration {

	private File file;
	private FileInputStream inStream;
	BufferedWriter bWriter;
	private Properties properties;

	public static char[] hexDigits = { '0', '1', '2', '3',

	'4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };

	/**
	 * 将字节数组转换为String
	 * 
	 * @param bytes
	 * @param split
	 *            分隔符
	 * @return
	 */
	public static String toHexString(byte[] bytes, char

	split) {
		char[] chars = new char[bytes.length * 3];

		for (int i = 0; i < bytes.length; i++) {
			int b = bytes[i];
			chars[i * 3] = hexDigits[(b & 0xF0) >>

			4];
			chars[i * 3 + 1] = hexDigits[b & 0x0F];
			chars[i * 3 + 2] = i * 3 + 2 <

			bytes.length * 3 - 1 ? split : '\t';
		}

		return new String(chars).trim();
	}

	public ReaderConfiguration(String name) {
		this.file = new File(name);
		try {
			inStream = new FileInputStream(file);
		} catch (FileNotFoundException e) {

			e.printStackTrace();
		}
		this.init();
	}

	public ReaderConfiguration() {
		this.init();
	}

	public void init() {

	}

	public Properties getProperties(String name) {
		this.file = new File(name);
		try {
			inStream = new FileInputStream(file);
			properties = new Properties();
			properties.load(inStream);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return properties;
	}

	public void writeProperties(String filename, Map map) {
		this.getProperties(filename);

		try {
			Set set = new HashSet();
			properties.setProperty("MAC_Address_" +

			properties.size(), "xx");
			properties.list(new PrintStream

			(filename));
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * 
	 * @param name
	 *            properties的文件地址
	 * @return value 返回的properties所有值
	 */
	public List<String> getPropValue(String name) {
		this.getProperties(name);
		Set<Object> set = properties.keySet();

		Object[] key = set.toArray();
		List<String> value = new ArrayList<String>();

		for (int i = 0; i < key.length; i++) {
			value.add(properties.getProperty((String)

			key[i]).trim());
		}
		return value;
	}

	/**
	 * 通过Runtime获得电脑的物理地址 先判断操作系统类型
	 */
	public static List<String> getMacByDos() {
		List<String> mac = new ArrayList<String>();
		try {
			Process process = null;
			String osName = System.getProperty

			("os.name");

			Pattern p1 = Pattern
					.compile("\\w{2}-\\w{2}-\\w{2}-\\w{2}-\\w{2}-\\w{2}");
			Pattern p2 = Pattern
					.compile("\\w{2}:\\w{2}:\\w{2}:\\w{2}:\\w{2}:\\w{2}");
			Matcher matcher;

			BufferedReader reader;

			if (osName.toLowerCase().startsWith

			("windows")) {

				process = Runtime.getRuntime

				().exec("ipconfig /all");

			}
			if (osName.toLowerCase().startsWith

			("linux")) {
				process = Runtime.getRuntime

				().exec("ifconfig eth0");
			}
			if (osName.toLowerCase().startsWith

			("unix")) {
				process = Runtime.getRuntime

				().exec("ifconfig eth0");
			}

			reader = new BufferedReader(new

			InputStreamReader(process.getInputStream

			()));
			String line = null;
			while ((line = reader.readLine()) !=

			null) {
				line = line.trim();
				if (line.length() >= 17) {
					// 匹配---
					matcher = p1.matcher

					(line.substring(line.length() - 17,

					line.length()));

					if (matcher.matches()) {
						mac.add

						(line.substring(line.length() - 17,

						line.length()));
					} else {
						// 匹配：：：

						matcher = p2.matcher(line.substring(line.length() - 17,
								line.length

								()));
						if

						(matcher.matches()) {
							mac.add

							(line.substring(line.length() - 17,

							line.length()));
						}
					}
				}

			}
			reader.close();

		} catch (IOException e) {

			e.printStackTrace();
		}
		return mac;

	}

	/**
	 * 通过NetworkInterface（jdk1.6）获得电脑的物理地址
	 * 
	 * @param split
	 *            mac地址的分隔符
	 * @return 返回mac地址
	 */

	public static String getMacByIP(String split) {
		StringBuffer mac = new StringBuffer();
		try {
			InetAddress address =

			InetAddress.getLocalHost();
			NetworkInterface network =

			NetworkInterface.getByInetAddress

			(address);
			byte macArray[] =

			network.getHardwareAddress();
			for (int i = 0; i < macArray.length; i++)

			{
				mac.append(String.format("%02X%s", macArray[i], (i <

				macArray.length - 1) ? split : ""));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return mac.toString();

	}

	/**
	 * @function 比较参数是否有一个相同
	 * @param sys
	 *            系统参数
	 * @param your
	 *            客户机参数
	 * @return result 返回true则比较成功
	 */
	public boolean compareProp(List<String> sys, List<String>

	your) {
		boolean result = false;
		loop: for (int i = 0; i < sys.size(); i++) {
			for (int j = 0; j < your.size(); j++) {
				if (sys.get(i).equals(your.get

				(j))) {
					result = true;
					break loop;
				}
			}
		}
		return result;
	}

	/*
	 * 判断电脑物理地址
	 */
	public boolean judgeMacAddress() {
		List<String> customer = new ArrayList<String>();
		customer.add(this.getMacByIP("-").trim());
		this.getProperties("src/system.properties");
		boolean result = this.compareProp(this.getPropValue

		("src/system.properties"), customer);

		if (!result) {
			try {
				UIManager.setLookAndFeel

				(UIManager

				.getSystemLookAndFeelClassName());
			} catch (Exception e) {
				e.printStackTrace();
			}
			JOptionPane.showMessageDialog(null, "对不起，你无权访问本系统！", "警告",

			JOptionPane.WARNING_MESSAGE);
			System.exit(0);
		}

		return result;
	}

	public static void main(String args[]) {
		ReaderConfiguration t = new ReaderConfiguration();

	}

}
