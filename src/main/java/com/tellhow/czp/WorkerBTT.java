package com.tellhow.czp;

import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Scanner;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

/**
 * Worker for listening message, expecting "BringToTop"
 */

public class WorkerBTT extends Thread {
	
	private ServerSocket server;
    private static int BIND_PORT = 2012;
    public static String MSG_BTT="no_useful_message";

    private WorkerBTT(ServerSocket server) {
        this.server = server;
        this.setDaemon(true);
    }
    
    /**
     * Bind and listening port BIND_PORT, for avoid duplicated startup.
     * @return Success or not.
     */
    public static boolean bindAndListen() {
        try {
            final ServerSocket server = new ServerSocket();
            server.bind(new InetSocketAddress("127.0.0.1", BIND_PORT));
            new WorkerBTT(server).start(); // Start a worker thread to listening "BringToTop" message.
            return true;
        } catch (IOException e) {
            //System.err.println(e.getMessage());
            return false;
        }

    }
    
    /**
     * Send "BringToTop" message to application which holding port BIND_PORT.
     */
    public static void sendBringToTop() {
        Socket client = new Socket();
        try {
            try {
                client.connect(new InetSocketAddress("127.0.0.1", BIND_PORT));
                OutputStream os = client.getOutputStream();
                os.write(MSG_BTT.getBytes());
                os.flush();
            } finally {
                client.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    

    public void run() {
        while (true) {
            try {
                Socket socket = server.accept();
                try {
                    Scanner sc = new Scanner(socket.getInputStream());
                    String str = sc.next();
                    CZPService.getService().setArg(str);
                    
                    if(SystemConstants.getMainFrame()==null){
                    	return;
                    } 
                    if (SystemConstants.getMainFrame().getState() == java.awt.Frame.ICONIFIED ) {
                    	SystemConstants.getMainFrame().setState(java.awt.Frame.NORMAL); // Make normal if it's iconified.
                    }
                    SystemConstants.getMainFrame().setAlwaysOnTop(true); // Force to top level
                    SystemConstants.getMainFrame().toFront(); // Flashing the TaskBar
                    SystemConstants.getMainFrame().setAlwaysOnTop(false); // Cancel
                    
                } finally {
                    socket.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
