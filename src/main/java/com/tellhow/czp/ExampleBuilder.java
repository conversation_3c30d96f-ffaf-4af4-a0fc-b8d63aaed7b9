package com.tellhow.czp;

import java.awt.GraphicsEnvironment;
import java.awt.Point;
import java.awt.event.MouseEvent;
import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.JPopupMenu;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.event.ChangeEvent;
import javax.swing.event.ChangeListener;
import javax.swing.text.JTextComponent;

import org.apache.log4j.Logger;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.MessageDialog;
import org.beryl.gui.Widget;
import org.beryl.gui.builder.LookAndFeelChooser;
import org.beryl.gui.component.BorderButton;
import org.w3c.dom.Element;

import com.tellhow.czp.Robot.InversionTicket;
import com.tellhow.czp.app.ConvertCIMDialog;
import com.tellhow.czp.app.ConvertSVGDialog;
import com.tellhow.czp.glossary.GlossarySortJDialog;
import com.tellhow.czp.mainframe.DriverPorpery;
import com.tellhow.czp.mainframe.FootPanelAbstract;
import com.tellhow.czp.mainframe.menu.provider.SearchEquipForm;
import com.tellhow.czp.operationcard.MonitorTicketTypePanel;
import com.tellhow.czp.operationcard.OperateTicketConvertPanel;
import com.tellhow.czp.operationcard.OperateTicketDXP;
import com.tellhow.czp.operationcard.OperateTicketSGP;
import com.tellhow.czp.operationcard.OperateTicketSGPDefault;
import com.tellhow.czp.operationcard.OperateTicketTypePanel;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.sysconfig.AuthManagerDialog;
import com.tellhow.czp.sysconfig.CodeAuthManagerDialog;
import com.tellhow.czp.sysconfig.ConfigTicketDialog;
import com.tellhow.czp.sysconfig.CzpCountDialog;
import com.tellhow.czp.sysconfig.CzpQuerryDialog;
import com.tellhow.czp.sysconfig.DictionaryDialog;
import com.tellhow.czp.sysconfig.EquipOperationManageDialog;
import com.tellhow.czp.sysconfig.EquipStateDialog;
import com.tellhow.czp.sysconfig.SetFlowDialog;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.czp.sysconfig.SystableDialog;
import com.tellhow.czp.sysconfig.SystemParamSetDialog;
import com.tellhow.czp.sysconfig.UnitManageSplitPDialog;
import com.tellhow.czp.user.UpdateUserPassword;
import com.tellhow.czp.user.UserDao;
import com.tellhow.czp.user.UserLoginInter;
import com.tellhow.czp.user.UserManage;
import com.tellhow.czp.util.GUIUtil;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.czp.widget.LineTreeWidget;
import com.tellhow.czp.widget.TransTreeWidget;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.mainframe.GuiBuilder;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationmodel.DrawRemovableDevice;
import czprule.rule.view.CheckDataAndSvgFrame;
import czprule.rule.view.CopyRuleDialg;
import czprule.rule.view.CustomCodex;
import czprule.rule.view.EquipOrganCheckChoose;
import czprule.rule.view.EquipPermissionCheckChoose;
import czprule.rule.view.RuleCustomDialog;
import czprule.stationstartup.InitDeviceRunType;
import czprule.stationstartup.InitDeviceStatus;
import czprule.stationstartup.StationStartupManager;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.ShowMessage;
import czprule.wordcard.view.CardDefDialog;
import czprule.wordcard.view.CardDescDialog;
import czprule.wordcard.view.MenuConfig;

public class ExampleBuilder extends GuiBuilder {
	private SystableDialog systabledialog=new SystableDialog();
	
	
	public ExampleBuilder(URL url) throws GUIException {
		super(url);
		
		
		loadAuth();
		
		
    	frame.show();
    	
    	
	}

	private static Logger log = Logger.getLogger(ExampleBuilder.class);

	@Override
	public JTabbedPane getSVGJTabbedPane() {
		return (JTabbedPane) frame.getWidget("svgTabbedPane").getRealWidget();
	}
	
	@Override
	public JPopupMenu getSVGCloseMenu() {
		return (JPopupMenu) frame.getWidget("svgCloseMenu").getRealWidget();
	}
	
	public FootPanelAbstract getFootPane() {
		return (FootPanelAbstract) frame.getWidget("footPane").getRealWidget();
	}
	
	protected void loadAuth() {
		
		
			GUIUtil g=new GUIUtil();
			ArrayList<String> disList= new ArrayList<String>();
			disList = g.disposeguiMenu(CBSystemConstants.getUser().getUserID());
			Widget menuBar = frame.getWidget("menuBar");
			for(int i = 0; i < menuBar.getChildCount(); i++) {
				Widget menu = menuBar.getChild(i);
				int count = 0;
				for(int j = 0; j < menu.getChildCount(); j++) {
					Widget widget = menu.getChild(j);
					if(widget.getRealWidget() != null) {
						if(disList.contains(widget.getName()))
							widget.getRealWidget().setVisible(false);
						else {
							widget.getRealWidget().setVisible(true);
							count++;
						}
					}				
				}
	
				if(count == 0) //如果菜单下没有要显示的菜单项，则菜单不显示
				{
					menu.getRealWidget().setVisible(false);
				}else{
					menu.getRealWidget().setVisible(true);
				}
			}
			disList = g.disposeguiTool(CBSystemConstants.getUser().getUserID());
			Widget toolBar = frame.getWidget("toolBar");
			for(int i = 0; i < toolBar.getChildCount(); i++) {
				Widget widget = toolBar.getChild(i);
				if(widget.getRealWidget() != null) {
					if(disList.contains(widget.getName()))
						widget.getRealWidget().setVisible(false);
					else
						widget.getRealWidget().setVisible(true);
				}
			}
		
		
		       //业务类型
				for(int i = 0; i < 4; i++) {
					String widgetName = "roleSelect" + i;
					if(frame.getWidget(widgetName) != null) {
						BorderButton button = (BorderButton)frame.getWidget(widgetName).getRealWidget();
						if(button.isVisible()) {
				    		button.setSelected();
				    		for(int j = i+1; j < 4; j++) {
								widgetName = "roleSelect" + j;
								if(frame.getWidget(widgetName) != null) {
									button = (BorderButton)frame.getWidget(widgetName).getRealWidget();
									button.setUnSelected();
								}
				    		}
				    		break;
						}
			    	}
				}
				
				//智能出票
				if(frame.getWidget("smartTicket") != null) {
					BorderButton button = (BorderButton)frame.getWidget("smartTicket").getRealWidget();
		    		button.setSelected();
		    	}
				//智能逐项票
				if(frame.getWidget("smartTicketzx") != null) {
					BorderButton button = (BorderButton)frame.getWidget("smartTicketzx").getRealWidget();
					button.setSelected();
				}
				//智能综令票
				if(frame.getWidget("smartTicketzl") != null) {
					BorderButton button = (BorderButton)frame.getWidget("smartTicketzl").getRealWidget();
					button.setUnSelected();
				}
				//点图开票
		    	if(frame.getWidget("stepTicket") != null) {
		    		BorderButton button = (BorderButton)frame.getWidget("stepTicket").getRealWidget();
		    		button.setUnSelected();
		    	}
		    	if(frame.getWidget("samestepTicket") != null){
		    		BorderButton button = (BorderButton)frame.getWidget("samestepTicket").getRealWidget();
		    		if(CBSystemConstants.isSame==false)
		    			button.setUnSelected();
		    		else
		    			button.setSelected();
		    	}
		    	//多选开票
		    	if(frame.getWidget("samesmartTicket") != null){
		    		BorderButton button = (BorderButton)frame.getWidget("samesmartTicket").getRealWidget();
		    		if(CBSystemConstants.isSame==false)
		    			button.setUnSelected();
		    		else
		    			button.setSelected();
		    	}
		    	//设备对位
		    	if(frame.getWidget("equipStatusSet") != null) {
		    		BorderButton button = (BorderButton)frame.getWidget("equipStatusSet").getRealWidget();
		    		button.setUnSelected();
		    		//20160328 czp项目实时态下不禁用设备对位按钮，其他项目可以覆盖修改
//		    		if(CBSystemConstants.cardstatus.equals("1")){//实时态
//		    			button.setEnabled(false);
//		    		} 
		    	}
		    	//通过状态成票
		    	if(frame.getWidget("stateOfTheDrawer") != null) {
		    		BorderButton button = (BorderButton)frame.getWidget("stateOfTheDrawer").getRealWidget();
		    		button.setUnSelected();
		    	}
		    	
		    	//状态同步
		    	if(frame.getWidget("statusCheck") != null) {
		    		BorderButton button = (BorderButton)frame.getWidget("statusCheck").getRealWidget();
		    		button.setUnSelected();
//		    		if(CBSystemConstants.cardstatus.equals("1")){//实时态
//		    			button.setEnabled(false);
//		    		} 
		    	}
		    	if(frame.getWidget("lookZDFWTicket") != null && CBSystemConstants.isMaxRangeOffTicket) {
		    		BorderButton button = (BorderButton)frame.getWidget("lookZDFWTicket").getRealWidget();
		    		button.setSelected();
		    	}
		    	if(frame.getWidget("ZHLTicket")!=null &&CBSystemConstants.cardtype.equals("1")){
		    		BorderButton button = (BorderButton)frame.getWidget("ZHLTicket").getRealWidget();
		    		button.setSelected();
		    	}
		    	if(frame.getWidget("FBLTicket")!=null &&CBSystemConstants.cardtype.equals("0")){
		    		BorderButton button = (BorderButton)frame.getWidget("FBLTicket").getRealWidget();
		    		button.setSelected();
		    	}		
	}

	@Override
	public void eventOccured(GUIEvent e) {
		String name = e.getName();
		try {
			if ("closeSVGPanel".equals(name)) { //关闭图形
				closeSVGPanel(e);
			}
			else if ("closeMenu".equals(name)) { //打开关闭图形菜单
				openCloseMenu(e);
			}
			else if ("closeOthers".equals(name)) { //关闭其他图形
				closeOthers(e);
			}
			else if ("closeAll".equals(name)) { //关闭所有图形
				closeAll(e);
			}
			else if("userManager".equals(name)){ //用户管理
				openUserManage(e);
			}
			else if("roleManager".equals(name)){ //角色管理
				openRoleManage(e);
			}
			else if("authManager".equals(name)){ //权限管理
				authManager(e);
			}
			else if("codeAuthManager".equals(name)){//区域权限管理
				codeAuthManager(e);
			}
			else if("loginSystem".equals(name)){ //重新登录
				reLogin(e);
			}
			else if("alterUser".equals(name)){ //修改密码
				updatePassword(e);
			}
			else if ("exitSystem".equals(name)) { //退出系统
				exitSystem(e);
			}
			else if ("deviceRule".equals(name)) { //设备规则维护
				devRuleManager(e);
			}
			else if ("deviceDesc".equals(name)) { //设备操作术语维护
				wordManager(e);
			}
			else if ("deviceDef".equals(name)) { //设备操作模板定义
				wordDef(e);
			}
			else if("lookMLTicket".equals(name)){ //命令票
				openFXTicket(e);
			}
			else if("lookJKTicket".equals(name)){ //监控票
				openJKTicket(e);
			}
			else if("lookDDJZTicket".equals(name)){ //监控票
				openDDJZTicket(e);
			}
			else if("lookDXTicket".equals(name)){ //典型票
				openDXTicket(e);
			}
			else if("lookSGTicket".equals(name)){ //开票
				openSGTicket(e);
			}
			else if("lookZDFWTicket".equals(name)){ //最大范围停电开票
				lookZDFWTicket(e);
			}
			else if("ZHLTicket".equals(name)){  //综合令即状态令开票
				ZHLTicket(e);
			}
			else if("FBLTicket".equals(name)){ //分步令即逐项令开票
				FBLTicket(e);
			}
			
			else if ("flowNumSet".equals(name)) {//票号设置
				setFlowNum(e);
			}
			else if ("newequipoperationmanager".equals(name)){//新设备规则类解析规则类管理
				systabledialog.NewEquipOperationManager(e);
			}
			else if("queryTicket".equals(name)){
				openTicketQueryDialog(e);
			}
			else if("countTicket".equals(name)){
				openTicketCountDialog(e);
			}
			else if("roleSelect0".equals(name)){ //主网
				setRoleKind("roleSelect0");
				CBSystemConstants.roleCode = "0";
			}
			else if("roleSelect1".equals(name)){ //配网
				setRoleKind("roleSelect1");
				CBSystemConstants.roleCode = "1";
			}
			else if("smartTicket".equals(name)){ //智能出票
				setSmartTicket(e);
			}
			if ("smartTicketzx".equals(name)) { //逐项票
				setSmartTicketZX(e);
			}
			else if ("smartTicketzl".equals(name)) { //打开关闭图形菜单
				setSmartTicketZL(e);
			}
			else if("stepTicket".equals(name)){ //点图开票
				setStepTicket(e);
			}
			else if("samestepTicket".equals(name)){ //多选点图开票
				setSameStepTicket(e);
			}
			else if("samesmartTicket".equals(name)){ //多选智能开票
				setSameSmartTicket(e);
			}
			else if("lookTransMap".equals(name)){ //显示桌面
				openTransMap(e);
			}
			else if ("glossaryMgr".equals(name)) { //常用语管理
				openGlossaryManager(e);
			} 
			else if("termInversion".equals(name)){ //模拟演示
				termInversion(e);
			}
			else if("securityCheck".equals(name)){ //潮流校核
				securityCheck(e);
			}
			else if("beatEquip".equals(name)){ //搜素设备
				searchEquipAction(e);
			}
			else if("attribute".equals(name)){ //设备属性
				equipAttributeAction(e);
			}
			
			else if("customCodex".equals(name)){ //自定义规则设置
				customCodexAction(e);
			}
			else if("protectType".equals(name)){ //保护类型维护
				systabledialog.protectTypeAction(e);
			}
			else if("protectOperate".equals(name)){ //保护操作维护
				systabledialog.protectOperateAction(e);
			}
			
			else if ("equipoperationmanage".equals(name)) {//设备操作管理
				equipoperationmanageAction(e);
			}
			else if ("ruleclassmanageraction".equals(name)){//票号生成规则类管理
				systabledialog.ruleClassManagerAction(e);
			}
			else if("rulemanageraction".equals(name)){//票号规则管理
				systabledialog.ruleManagerAction(e);
			}
			else if("systemConfig".equals(name)) //系统设置
			{
				SystemParamSetDialog dialog = new SystemParamSetDialog(SystemConstants.getMainFrame(),false);
				dialog.setVisible(true);
			}
			else if("areaConfig".equals(name)) //区域设置
			{
				systabledialog.aeraManagerAction(e);
			}
			else if(name.equals("configticket")){ //设置票号
				ConfigTicketDialog bhDialog = new ConfigTicketDialog();
				bhDialog.setVisible(true);
			}
			else if("opMenuConfig".equals(name)) //操作菜单配置
			{
				opMenuConfigAction(e);
			}
			else if("codeManager".equals(name)){ //代码管理
				DictionaryDialog dialog = new DictionaryDialog();
				dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
				dialog.setSize(500, 300);
				Point p = GraphicsEnvironment.getLocalGraphicsEnvironment().getCenterPoint();    
				dialog.setBounds(p.x - 500 / 2, p.y - 400 / 2, 500, 300); //将对话框设置为居中位置 
				dialog.setVisible(true);
				
				dialog.pack();
			}
			else if("statusManager".equals(name)){ //设备状态管理
				EquipStateDialog dialog = new EquipStateDialog();
				dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
				dialog.setVisible(true);
			}
			else if("operationManager".equals(name)){ //设备操作管理
				final EquipOperationManageDialog dialog = new EquipOperationManageDialog(new javax.swing.JFrame(), true);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						dialog.dispose();
					}
				});
				dialog.setVisible(true);
			}
			else if("unitManager".equals(name)){ //机构管理
				UnitManageSplitPDialog dialog = new UnitManageSplitPDialog();
				dialog.setVisible(true);
			}
			else if("userManager".equals(name)){ //人员管理
				//protectOperateAction(e);
			}
			else if("zoom-in".equals(name)){ //放大
				SVGtoBig(e);
			}
			else if("zoom-out".equals(name)){ //缩小
				SVGtoSmall(e);
			}
			else if("zoom-100".equals(name)){ //正常
				SVGtoNetural(e);
			}
			else if ("DeviceTypeInit".equals(name)) { //接线方式初始化
				DeviceTypeInit(e);
			}
			else if ("DeviceStatusInit".equals(name)) { //状态初始化
				InitEquipStatus(e);
			}
			else if ("statusCheck".equals(name)) { //状态校核
				LoadEMSStatus(e);
			}
			else if ("statusCheckAll".equals(name)) { //所有厂站状态校核
				LoadEMSStatusAll(e);
			}
			else if ("checkSvgAndData".equals(name)) { //图模校核
				checkSvgAndData(e);
			}
			else if ("multiSelect".equals(name)) { //设置图形选择模式
				setMouseMode(e);
			}
//			else if ("mouseMode".equals(name)) { //设置图形选择模式
//				setMouseMode(e);
//			}
			else if ("organInit".equals(name)) { //调管机构初始化
//				loadOrgan(e);
			}
			else if ("organSet".equals(name)) { //调管机构设置
				setOrgan(e);
			}
			else if ("permissionSet".equals(name)) { //调管机构设置
				setPermission(e);
			}
			else if("equipStatusSet".equals(name)){ //设备对位
				equipStatusSet(e);
			}
			else if("stateOfTheDrawer".equals(name)){ //通过状态成票
				stateOfTheDrawer(e);
			}
			else if (name.equals("copyRuleWord")) { //规则术语复制
//				if("0".equals(CBSystemConstants.getUser().getUserDuty())){
					CopyRuleDialg dialog = new CopyRuleDialg();
					dialog.setVisible(true);
//				}
			}
			else if (name.equals("lookandfeel")) { //外观样式
				LookAndFeelChooser lnfChooser = new LookAndFeelChooser();
			}
			else if (name.equals("about")) { //版本信息
				ShowMessage.view(CBSystemConstants.SYSTEM_TITLE+" "+CBSystemConstants.SYSTEM_VERSION);
			}
			else if (name.equals("help")) { //使用手册
//				String file = "";
//				if(CBSystemConstants.roleCode.equals("0"))
//					file = "ddczp.doc";
//				else
//					file = "jkczp.doc";
//				String cmd = "rundll32 url.dll, FileProtocolHandler "+file;
//				Runtime.getRuntime().exec(cmd);
				ShowMessage.view("请到网络发令系统Web版本“附件下载”模块下载使用手册，谢谢！");
			}
			else if("CIM".equals(name)){
				cimLoad(e);
			}
			else if("SVG".equals(name)){
				svgLoad(e);
			}
			else if (name.equals("quit")) {
					System.exit(0);
				}
			} catch (Exception ex) {
				new MessageDialog(ex);
			}
	}
	
	
	//设备操作管理
	protected void equipoperationmanageAction(GUIEvent e) {
		EquipOperationManageDialog drm=new EquipOperationManageDialog(SystemConstants.getMainFrame(),true);
		drm.setVisible(true);
	}

	//设备搜索
	protected void searchEquipAction(GUIEvent e){
		SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getSelectedComponent();
		SVGCanvas fSvgCanvas=otsp.getSvgCanvas();
		SearchEquipForm sef = new SearchEquipForm(fSvgCanvas);
	}
	//设备属性
	protected void equipAttributeAction(GUIEvent e){
		if(TempTicket.getTempTicket()!=null){
			ShowMessage.view("请先关闭当前所打开的操作票界面！");
			return;
		}
		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.75);
		DriverPorpery ylp=new DriverPorpery();//设备属性Jpanel
		splitPane.setRightComponent(ylp);
		CBSystemConstants.svgAddPd=ylp;
	}
	
	//自定义规则开票
	protected void customCodexAction(GUIEvent e){
		if(TempTicket.getTempTicket()!=null){
			ShowMessage.view("请先关闭当前所打开的操作票界面！");
			return;
		}
		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.57);
//		CustomCodex cc=new CustomCodex();
//		splitPane.setRightComponent(cc);
//		CBSystemConstants.svgAddPd=cc;
	}
	
	protected void opMenuConfigAction(GUIEvent e){
		if(TempTicket.getTempTicket()!=null){
			if(CBSystemConstants.roleCode.equals("0")){
				ShowMessage.view("请保存并关闭操作票！");
				return;
			}else{
				ShowMessage.view("请保存并关闭监控票！");
				return;
			}
		}
		
		MenuConfig mc=new MenuConfig(SystemConstants.getMainFrame());
		mc.setLocationRelativeTo(null);
		mc.setVisible(true);
	}
	
	// 常用语管理
	protected void openGlossaryManager(GUIEvent e) {
		
		JTextComponent yong=null;
		GlossarySortJDialog gsd = GlossarySortJDialog.getDialog();
		JTextComponent jc=OperateTicketSGPDefault.jc;
		JTextComponent tjc=TempTicket.getTjc();
		if(jc!=null){
			yong=jc;
		}
		if(tjc!=null){
			yong=tjc;
		}
		if(gsd == null) {
			gsd = new GlossarySortJDialog(this.getJFrame(), yong);
			gsd.setVisible(true);
		}else{
			gsd.setVisible(true);
		}
		jc=null;
		tjc=null;
		int n=0;
		
	}
	// 用户管理
	protected void openUserManage(GUIEvent e) {
		UserManage otum = new UserManage(this.getJFrame(),true);
		otum.setVisible(true);
	}
	// 角色管理
	protected void openRoleManage(GUIEvent e) {
		UserManage otum = new UserManage(this.getJFrame(),true);
		otum.setVisible(true);
	}
	// 权限管理
	protected void authManager(GUIEvent e){
		AuthManagerDialog amd = new AuthManagerDialog(this.getJFrame(),true);
		amd.setVisible(true);
	}
	
	// 角色权限管理
	protected void codeAuthManager(GUIEvent e){
		CodeAuthManagerDialog amd = new CodeAuthManagerDialog(this.getJFrame(),true);
		amd.setVisible(true);
	}
	// 修改密码
	protected void updatePassword(GUIEvent e) {
		UpdateUserPassword otum = new UpdateUserPassword(this.getJFrame(),true);
		otum.setVisible(true);
	}
	// 重新登录
	protected void reLogin(GUIEvent e) {
		//SystemConstants.getMainFrame().setVisible(false);
//		UserLogin loginDialog = new UserLogin("图形化智能操作票系统登录");
//		loginDialog.setVisible(true);
		UserLoginInter.getInstance().setVisible(true);
		//Main.init();
		getFootPane().getjLabel2().setText(CBSystemConstants.getUser().getUserName());
		
		
		
		LineTreeWidget tree2 = (LineTreeWidget)frame.getWidget("Tree2");
		tree2.refresh();
		TransTreeWidget tree3 = (TransTreeWidget)frame.getWidget("Tree3");
		if(tree3 != null)
		tree3.refresh();		
		loadAuth();
		if(TempTicket.getTempTicket()!=null){
			TempTicket.setTempTicket(null);
		}
		CBSystemConstants.roleCode = "";//重置角色编码
		UserDao userdao=new UserDao();
		userdao.LoadUserLike(CBSystemConstants.getUser());
		
		if(!CBSystemConstants.cardstatus.equals("1")) {
			if(CBSystemConstants.roleCode.equals("0"))
	    		CBSystemConstants.cardstatus = "0";
	    	else if(CBSystemConstants.roleCode.equals("2"))
	    		CBSystemConstants.cardstatus = "2";
		}
	}
	// 重新登录
		public void reInitUser(GUIEvent e) {
				getFootPane().getjLabel2().setText(CBSystemConstants.getUser().getUserName());
			
			
			
				TransTreeWidget tree2 = (TransTreeWidget)frame.getWidget("Tree2");
				tree2.refresh();
				LineTreeWidget tree3 = (LineTreeWidget)frame.getWidget("Tree3");
				if(tree3 != null)
				tree3.refresh();		
			loadAuth();
			if(TempTicket.getTempTicket()!=null){
				TempTicket.setTempTicket(null);
			}
			UserDao userdao=new UserDao();
			userdao.LoadUserLike(CBSystemConstants.getUser());
			CBSystemConstants.cardstatus = "1";
		}
	//打开设备规则管理器
	protected void devRuleManager(GUIEvent e) {
		RuleCustomDialog drm=new RuleCustomDialog(SystemConstants.getMainFrame(),true);
		drm.setVisible(true);
	}
	//打开设备术语管理器
	protected void wordManager(GUIEvent e) {
		CardDescDialog drm=new CardDescDialog(SystemConstants.getMainFrame(),false);
		drm.setVisible(true);
	}
	//打开设备模板管理器
	protected void wordDef(GUIEvent e) {
		CardDefDialog drm=new CardDefDialog(SystemConstants.getMainFrame(),true);
		drm.setVisible(true);
	}
	//打开电网联络图
	protected void openTransMap(GUIEvent e) {
		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setEnabled(false);      // 禁止拖动分割条
		splitPane.setDividerLocation(0.9999);      
		splitPane.setOneTouchExpandable(true);
		splitPane.setDividerSize(12);
		CBSystemConstants.svgAddPd = null;
		setTicketKind("lookTransMap");
	}
	//打开命令票管理
	protected void openFXTicket(GUIEvent e) {
		if(TempTicket.getTempTicket()!=null){
			if(CBSystemConstants.roleCode.equals("0")){
				ShowMessage.view("请保存并关闭操作票！");
				return;
			}else{
				ShowMessage.view("请保存并关闭监控票！");
				return;
			}
		}
		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.0);
		 splitPane.setOneTouchExpandable(false);
		OperateTicketTypePanel otp = OperateTicketTypePanel.getInstance();
		otp.initTable();
		otp.setVisible(true);
		splitPane.setRightComponent(otp);
//		otp.jTabbedPane1.setSelectedIndex(0);
		
		//典型票页面刷新
		OperateTicketDXP otp2 = OperateTicketDXP.getInstance();
		otp2.initDXP();
		setTicketKind("lookMLTicket");
	}
	//打开监控票管理
	protected void openJKTicket(GUIEvent e) {
		if(TempTicket.getTempTicket()!=null){
			if(CBSystemConstants.roleCode.equals("0")){
				ShowMessage.view("请保存并关闭操作票！");
				return;
			}else{
				ShowMessage.view("请保存并关闭监控票！");
				return;
			}
		}
		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.0);
		MonitorTicketTypePanel otp =  MonitorTicketTypePanel.getInstance();
		splitPane.setRightComponent(otp);
//		otp.jTabbedPane1.setSelectedIndex(0);
		otp.setVisible(true);
		splitPane.setRightComponent(otp);
		otp.initTable();
	}
	//打开调度票接转管理
	protected void openDDJZTicket(GUIEvent e) {
		if(TempTicket.getTempTicket()!=null){
			if(CBSystemConstants.roleCode.equals("0")){
				ShowMessage.view("请保存并关闭操作票！");
				return;
			}else{
				ShowMessage.view("请保存并关闭监控票！");
				return;
			}
		}
		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.0);
		OperateTicketConvertPanel otp = new OperateTicketConvertPanel();
		splitPane.setRightComponent(otp);
		otp.jTabbedPane1.setSelectedIndex(0);
		otp.setVisible(true);
		splitPane.setRightComponent(otp);
		//otp.initTable();
	}
    //打开典型票管理  
	protected void openDXTicket(GUIEvent e) {
		if(TempTicket.getTempTicket()!=null){
			if(CBSystemConstants.roleCode.equals("0")){
				ShowMessage.view("请保存并关闭操作票！");
				return;
			}else{
				ShowMessage.view("请保存并关闭监控票！");
				return;
			}
		}
		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.0);		
		OperateTicketTypePanel otp = OperateTicketTypePanel.getInstance();
		// = new OperateTicketTypePanelDefault();
		splitPane.setRightComponent(otp);
//		otp.jTabbedPane1.setSelectedIndex(1);
	}
	
    //打开手工票管理
	protected void openSGTicket(GUIEvent e) {
		if(TempTicket.getTempTicket()!=null){
			if(CBSystemConstants.roleCode.equals("0")){
				ShowMessage.view("请保存并关闭操作票！");
				return;
			}else{
				ShowMessage.view("请保存并关闭监控票！");
				return;
			}
		}
		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.0);
		OperateTicketSGP ots=OperateTicketSGP.getInstance();
		ots.setVisible(true);
		splitPane.setRightComponent(ots);
		setTicketKind("lookSGTicket");
	}
	//最大范围停电开票
		protected void lookZDFWTicket(GUIEvent e) {
			if(CBSystemConstants.isMaxRangeOffTicket == false) {
				BorderButton button = (BorderButton)frame.getWidget("lookZDFWTicket").getRealWidget();
	    		button.setSelected();
				CBSystemConstants.isMaxRangeOffTicket = true;
			}
			else {
				BorderButton button = (BorderButton)frame.getWidget("lookZDFWTicket").getRealWidget();
	    		button.setUnSelected();
				CBSystemConstants.isMaxRangeOffTicket = false;
			}
			
//			JudgePowerSource jps = new JudgePowerSource();
//			List<PowerDevice> results = jps.execute(devList);
//			if(results.size() == 0)
//				return;
//			PowerDevice pd = results.get(0);
//			int isOk=JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "选择最大停电范围开票的电源侧设备是["+pd.getPowerDeviceName()+"]，是否继续？",SystemConstants.SYSTEM_TITLE, JOptionPane.YES_NO_OPTION);
//			if(isOk==JOptionPane.NO_OPTION){
//				return;
//			}
//			CBSystemConstants.isMaxRangeOffTicket = true;
//			DeviceOperate dre=new DeviceOperate();
//			boolean issuss=dre.execute(pd, "3");
//			if(!issuss){
//		    	ShowMessage.view("操作失败！");
//		    }
//			CBSystemConstants.isMaxRangeOffTicket = false;
			
		}
		
		//综合令即状态令出票方式
		protected void ZHLTicket(GUIEvent e)
		{
			if(frame.getWidget("ZHLTicket")!=null) {
				BorderButton button = (BorderButton)frame.getWidget("ZHLTicket").getRealWidget();
	    		button.setSelected();
				CBSystemConstants.cardtype="1";
			}
			 if(frame.getWidget("FBLTicket") != null){
				BorderButton button = (BorderButton)frame.getWidget("FBLTicket").getRealWidget();
	    		button.setUnSelected();
			}
			
		}
		//分步令即逐项令出票方式
		protected void FBLTicket(GUIEvent e)
		{
			if(frame.getWidget("FBLTicket")!=null) {
				BorderButton button = (BorderButton)frame.getWidget("FBLTicket").getRealWidget();
	    		button.setSelected();
				CBSystemConstants.cardtype="0";
			}
			if(frame.getWidget("ZHLTicket") != null){
				BorderButton button = (BorderButton)frame.getWidget("ZHLTicket").getRealWidget();
	    		button.setUnSelected();
				
			}
			
		}
	//初始化设备状态
	protected void InitEquipStatus(GUIEvent e){
		
		Object[] options = {"已打开厂站", "所有厂站"}; 
    	int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要初始化设备状态的范围", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
    	if(sel == 0) {
    		InitDeviceStatus ie=new InitDeviceStatus();
    		JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
    		String names = "";
    		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
    			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
    			String stationID = panel.getStationID();
    			String[] staarr = stationID.split(",");
    			String stationName = panel.getName();
    			for(String sta : staarr) {
        			if(!sta.equals("")) {
        				if(!ie.initStatus_EMS(sta))
        					ie.initStatus_CZP(sta);
        				Map deviceMap=(Map)CBSystemConstants.getMapPowerStationDevice().get(sta);
        				PowerDevice pd=null;
        		    	for (Iterator iter = deviceMap.values().iterator(); iter.hasNext();) {
        		    		pd=(PowerDevice) iter.next();
        		    		if(pd==null)
        		    			continue;
        		    		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
        				}
        		    	
        			}
        			if(CBSystemConstants.roleCode.equals("1")) { //配网要由馈线ID找厂站ID，加载厂站设备
    					List<Map> list = DBManager.queryForList("select st_id from "+CBSystemConstants.equipUser+"t_c_aclineend where id='"+sta+"'");
    		            if(list.size() > 0) {
    		            	if(!ie.initStatus_EMS(list.get(0).get("st_id").toString()))
            					ie.initStatus_CZP(list.get(0).get("st_id").toString());
    		            	Map deviceMap=(Map)CBSystemConstants.getMapPowerStationDevice().get(list.get(0).get("st_id").toString());
    						PowerDevice pd=null;
    				    	for (Iterator iter = deviceMap.values().iterator(); iter.hasNext();) {
    				    		pd=(PowerDevice) iter.next();
    				    		if(pd==null)
    				    			continue;
    				    		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
    						}
    		            }
    				}
    			}
    			names = names + stationName + ",";
    		}
    		if(names.equals(""))
    			ShowMessage.view("没有打开厂站图，初始化失败！");
    		else
    			ShowMessage.view("["+names.substring(0,names.length()-1)+"]设备状态初始化完成！");
    	}
    	else if(sel == 1) {
    		for(Iterator it=CBSystemConstants.getMapPowerStation().keySet().iterator();it.hasNext();){
				String stationID = (String)it.next();
				if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
					CreatePowerStationToplogy.loadFacEquip(stationID);
				}
				InitDeviceStatus ie=new InitDeviceStatus();
				if(!ie.initStatus_EMS(stationID))
					ie.initStatus_CZP(stationID);
				
				if(CBSystemConstants.roleCode.equals("1")) { //配网要由馈线ID找厂站ID，加载厂站设备
					List<Map> list = DBManager.queryForList("select st_id from "+CBSystemConstants.equipUser+"t_c_aclineend where id='"+stationID+"'");
		            if(list.size() > 0) {
		            	if(!ie.initStatus_EMS(list.get(0).get("st_id").toString()))
        					ie.initStatus_CZP(list.get(0).get("st_id").toString());
		            }
				}
			}
			JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
			for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
				SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
				String stationID = panel.getStationID();
				String stationName = panel.getName();
				if(!stationID.equals("")) {
					Map<String, PowerDevice> deviceMap=(Map<String, PowerDevice>)CBSystemConstants.getMapPowerStationDevice().get(stationID);
					PowerDevice pd=null;
					if(deviceMap!=null){
						for (Iterator<PowerDevice> iter = deviceMap.values().iterator(); iter.hasNext();) {
				    		pd= iter.next();
				    		if(pd==null)
				    			continue;
				    		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
						}
				    	if(CBSystemConstants.roleCode.equals("1")) { //配网要由馈线ID找厂站ID，加载厂站设备
							List<Map> list = DBManager.queryForList("select st_id from "+CBSystemConstants.equipUser+"t_c_aclineend where id='"+stationID+"'");
				            if(list.size() > 0) {
				            	deviceMap=(Map)CBSystemConstants.getMapPowerStationDevice().get(list.get(0).get("st_id").toString());
								pd=null;
						    	for (Iterator iter = deviceMap.values().iterator(); iter.hasNext();) {
						    		pd=(PowerDevice) iter.next();
						    		if(pd==null)
						    			continue;
						    		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
								}
				            }
						}
					}
				}
			}
			ShowMessage.view("设备状态更新完成！");
			return;
    	}
	}
	//同步EMS状态
	protected void LoadEMSStatusAll(GUIEvent e){

		InitDeviceStatus ie=new InitDeviceStatus();
		
		int isOk=JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "是否要初始化所有厂站的设备状态？",SystemConstants.SYSTEM_TITLE, JOptionPane.YES_NO_OPTION);
		if(isOk==JOptionPane.YES_OPTION){
			for(Iterator it=CBSystemConstants.getMapPowerStation().keySet().iterator();it.hasNext();){
				String stationID = (String)it.next();
				
//				if(CBSystemConstants.getPowerStation(stationID).getPowerStationName().indexOf("敦煌")==-1 )
//					continue;
				
				if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
					CreatePowerStationToplogy.loadFacEquip(stationID);
				}
				
				if(CBSystemConstants.getPowerStation(stationID).getPowerStationName().indexOf("柴达木")>=0 ||
						CBSystemConstants.getPowerStation(stationID).getPowerStationName().indexOf("白银220")>=0 ||
						CBSystemConstants.getPowerStation(stationID).getPowerStationName().indexOf("中节能风电")>=0)
					continue;
				
				ie.initStatus_EMS(stationID);
			}
			ShowMessage.view("设备状态更新完成！");
			return;
		}
		
		
	}
	
	//同步EMS状态
	protected void LoadEMSStatus(GUIEvent e){
//			if(!DevicePropertyDB.isEMSStatusExist()) {
//		ShowMessage.view("不能连接到EMS，更新失败！");
//		return;
//	}
	InitDeviceStatus ie=new InitDeviceStatus();
	
	int isOk=JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "是否要以实时数据重置当前厂站的设备状态？",SystemConstants.SYSTEM_TITLE, JOptionPane.YES_NO_OPTION);
	if(isOk==JOptionPane.NO_OPTION || isOk==JOptionPane.CLOSED_OPTION){
		return;
	}
	
	JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
	//挂的牌
	List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
	String names = "";
	HashMap<String, ArrayList<PowerDevice>> allrmd=CBSystemConstants.getRMDevice();
	
	int n=getSVGJTabbedPane().getSelectedIndex();
	SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(n);
	
	for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
		SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
		String stationID = panel.getStationID();
		String stationName = panel.getName();
		
		if(!panel.getFilePath().equals(sel.getFilePath()))
			continue;
		
		if(!stationID.equals("")) {
			
			String[] staarr = stationID.split(",");
			for(String sta : staarr) {
				ArrayList<PowerDevice> rmdlist=allrmd.get(sta);
				PowerDevice pd=null;
				
				if(rmdlist!=null){
					for (int j=0;j<rmdlist.size();j++) {
						pd=rmdlist.get(j);
			    		if(pd==null){
			    			continue;
			    		}
//			    		String powerEquipID=pd.getDevice();
//			    		String powerStationID=pd.getPowerStationID();
//			    		PowerDevice powerpd=CBSystemConstants.getPowerDevice(powerStationID, powerEquipID);
			    		pdlist.add(pd);
					}
				}
			}
		}
	}
	
	for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
		SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
		
		if(!panel.getFilePath().equals(sel.getFilePath()))
			continue;
		
		String stationID = panel.getStationID();
		
		if(!stationID.equals("")) {
			
			String[] staarr = stationID.split(",");
			for(String sta : staarr) {
				String stationName = "";
				if(CBSystemConstants.getPowerStation(sta)!=null)
					stationName = CBSystemConstants.getPowerStation(sta).getPowerStationName();
				if(CBSystemConstants.roleCode.equals("1") && CBSystemConstants.getMapPowerFeeder().get(sta)!=null) {
					stationName = CBSystemConstants.getMapPowerFeeder().get(sta).getPowerDeviceName();
				}
				ie.initStatus_EMS(sta);
				Map deviceMap=(Map)CBSystemConstants.getMapPowerStationDevice().get(sta);
				PowerDevice pd=null;
				if(deviceMap!=null) {
			    	for (Iterator iter = deviceMap.values().iterator(); iter.hasNext();) {
			    		pd=(PowerDevice) iter.next();
			    		if(pd==null)
			    			continue;
			    		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
					}
				}
		    	if(CBSystemConstants.roleCode.equals("1")) { //配网要由馈线ID找厂站ID，加载厂站设备
					List<Map> list = DBManager.queryForList("select st_id from "+CBSystemConstants.equipUser+"t_c_aclineend where id='"+sta+"'");
		            if(list.size() > 0&&ie!=null) {
		            	ie.initStatus_EMS(list.get(0).get("st_id").toString());
		            	deviceMap=(Map)CBSystemConstants.getMapPowerStationDevice().get(list.get(0).get("st_id").toString());
		            	if(deviceMap!=null) {
			            	pd=null;
					    	for (Iterator iter = deviceMap.values().iterator(); iter.hasNext();) {
					    		pd=(PowerDevice) iter.next();
					    		if(pd==null)
					    			continue;
					    		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
							}
		            	}
		            }
				}
		    	if(!stationName.equals(""))
		    		names = names + stationName + ",";
			}
		}
	}
	for(int i=0;i<pdlist.size();i++){
		PowerDevice pd=pdlist.get(i);
		new DrawRemovableDevice().execute(pd);
	}
	if(names.equals(""))
		ShowMessage.view("没有打开厂站图，更新失败！");
	else
		ShowMessage.view("["+names.substring(0,names.length()-1)+"]设备状态更新完成！");
		}
	
	protected void setOrgan(GUIEvent e){
		
		if(SystemConstants.getSelectedElement().size()==0) {
			ShowMessage.view("请先在框选设备模式下选择设备！");
			return;
		}
		SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		ArrayList<Element> list = SystemConstants.getSelectedElement();
		List<PowerDevice> devList = new ArrayList<PowerDevice>();
		for(Element ele : list){
			String stationID = resolver.getStationID(ele.getOwnerDocument());
			String equipID = resolver.getDeviceID(ele);
			PowerDevice pd = CBSystemConstants.getPowerDevice(stationID, equipID);
			if(pd != null)
				devList.add(pd);
		}
//		EquipOrganCheckChoose ecc=new EquipOrganCheckChoose(SystemConstants.getMainFrame(), true, devList, "请选择并确认所选设备");
//		List<PowerDevice> choose=ecc.getChooseEquip();
		EquipOrganCheckChoose bjecc=new EquipOrganCheckChoose(SystemConstants.getMainFrame(), true, devList, "请为选中的设备选择调控单位","调控单位");
		List<PowerDevice> bjchoose=bjecc.getChooseEquip();
		String bjchooseorgan=bjecc.getChooseOrgan();
		bjecc.disposeBjChoose(bjchoose,bjchooseorgan);
		//ShowMessage.view("保存成功！");
	}
	
	//许可机构设置
	protected void setPermission(GUIEvent e){
		if(SystemConstants.getSelectedElement().size()==0) {
			ShowMessage.view("请先在框选设备模式下选择设备！");
			return;
		}
		SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		ArrayList<Element> list = SystemConstants.getSelectedElement();
		List<PowerDevice> devList = new ArrayList<PowerDevice>();
		for(Element ele : list){
			String stationID = resolver.getStationID(ele.getOwnerDocument());
			String equipID = resolver.getDeviceID(ele);
			PowerDevice pd = CBSystemConstants.getPowerDevice(stationID, equipID);
			if(pd != null)
				devList.add(pd);
		}
//		EquipOrganCheckChoose ecc=new EquipOrganCheckChoose(SystemConstants.getMainFrame(), true, devList, "请选择并确认所选设备");
//		List<PowerDevice> choose=ecc.getChooseEquip();
		EquipPermissionCheckChoose bjecc=new EquipPermissionCheckChoose(SystemConstants.getMainFrame(), true, devList, "请为选中的设备选择许可单位","许可单位");
		List<PowerDevice> bjchoose=bjecc.getChooseEquip();
		String bjchooseorgan=bjecc.getChooseOrgan();
		bjecc.disposeBjChoose(bjchoose,bjchooseorgan);
		//ShowMessage.view("保存成功！");
	}
	//设置图形选择模式
	protected void setMouseMode(GUIEvent e){
		if(frame.getWidget("multiSelect") != null) {
    		BorderButton button = (BorderButton)frame.getWidget("multiSelect").getRealWidget();
    		if(button.isSelected()) {
    			button.setUnSelected();
    			SystemConstants.MAP_MOUSE_MODE = "0";
    		}
    		else {
    			button.setSelected();
    			SystemConstants.MAP_MOUSE_MODE = "1";
    		}
    	}
	}
	
	private CheckDataAndSvgFrame cd;
	/**
	 * 图模校核
	 * */
	protected void checkSvgAndData(GUIEvent e){
		if(this.cd==null){
		cd=new CheckDataAndSvgFrame();
		}
		cd.show();
	}
	
	protected void setRoleKind(String selectWidget){
		String[] widgetNameArray = new String[]{"roleSelect0","roleSelect1","roleSelect2","roleSelect3"};
		for(String widgetName : widgetNameArray) {
			if(frame.getWidget(widgetName) != null) {
	    		BorderButton button = (BorderButton)frame.getWidget(widgetName).getRealWidget();
	    		if(widgetName.equals(selectWidget))
	    			button.setSelected();
	    		else
	    			button.setUnSelected();
	    	}
		}
	}
	
	protected void setTicketKind(String selectWidget){
		String[] widgetNameArray = new String[]{"smartTicketzl","smartTicketzx","smartTicket","stepTicket","samestepTicket","samesmartTicket","lookSGTicket","equipStatusSet","lookTransMap","lookMLTicket"};
		for(String widgetName : widgetNameArray) {
			if(frame.getWidget(widgetName) != null) {
	    		BorderButton button = (BorderButton)frame.getWidget(widgetName).getRealWidget();
	    		if(widgetName.equals(selectWidget))
	    			button.setSelected();
	    		else
	    			button.setUnSelected();
	    	}
		}
	}
	
	
	//智能出票
		protected void setSmartTicket(GUIEvent e){
    		CBSystemConstants.stateOfTheDrawer =false;
			CBSystemConstants.cardbuildtype = "0";
			CBSystemConstants.isLock=true;
	    	CBSystemConstants.isOutCard=true;
	    	CBSystemConstants.isSame=false;
	    	CBSystemConstants.cardflag = "1";
	    	setTicketKind("smartTicket");
	    	SvgUtil.clear(); 
	    	
			JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			if(splitPane.getDividerLocation() == 1){
		  	    splitPane.setDividerLocation(0.99999);
			}
			splitPane.setOneTouchExpandable(true);
			splitPane.setDividerSize(12);
			CBSystemConstants.svgAddPd = null;
		}
		//智能逐项票
		protected void setSmartTicketZX(GUIEvent e){
    		CBSystemConstants.stateOfTheDrawer =false;
			CBSystemConstants.cardbuildtype = "0";
			CBSystemConstants.isLock=true;
	    	CBSystemConstants.isOutCard=true;
	    	CBSystemConstants.isSame=false;
	    	CBSystemConstants.cardflag = "1";
	    	setTicketKind("smartTicketzx");
	    	SvgUtil.clear(); 
		}
		//智能综令票
		protected void setSmartTicketZL(GUIEvent e){
    		CBSystemConstants.stateOfTheDrawer =false;
			CBSystemConstants.cardbuildtype = "0";
			CBSystemConstants.isLock=true;
	    	CBSystemConstants.isOutCard=true;
	    	CBSystemConstants.isSame=false;
	    	setTicketKind("smartTicketzl");
	    	CBSystemConstants.cardflag = "0";
	    	SvgUtil.clear(); 
		}
		//点图开票
		protected void setStepTicket(GUIEvent e){
    		CBSystemConstants.stateOfTheDrawer =false;
		    	CBSystemConstants.cardbuildtype = "1";
		    	CBSystemConstants.isLock=true;
		    	CBSystemConstants.isOutCard=true;
		    	CBSystemConstants.isSame=false;
		    	CBSystemConstants.cardflag = "1";
		    	setTicketKind("stepTicket");
		    	SvgUtil.clear(); 
	    	CBSystemConstants.cardbuildtype = "1";
	    	CBSystemConstants.isLock=true;
	    	CBSystemConstants.isOutCard=true;
	    	CBSystemConstants.isSame=false;
	    	CBSystemConstants.cardflag = "1";
	    	setTicketKind("stepTicket");
	    	SvgUtil.clear(); 
			JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			System.out.println(splitPane.getDividerLocation());
			if(splitPane.getDividerLocation() == 1){
		  	    splitPane.setDividerLocation(0.99999);
			}
			splitPane.setOneTouchExpandable(true);
			splitPane.setDividerSize(12);
			CBSystemConstants.svgAddPd = null;
		}
		//多选点图开票
		protected void setSameStepTicket(GUIEvent e){
    		CBSystemConstants.stateOfTheDrawer =false;
				if(CBSystemConstants.roleCode.equals("0")){
					CBSystemConstants.cardbuildtype = "1";
				}
				else if(CBSystemConstants.roleCode.equals("2")){
					CBSystemConstants.cardbuildtype = "0";
				}
		    	CBSystemConstants.isLock=true;
		    	CBSystemConstants.isOutCard=true;
		    	CBSystemConstants.isSame=true;
		    	setTicketKind("samestepTicket");
		    	SvgUtil.clear(); 
		}
		//多选智能开票
		protected void setSameSmartTicket(GUIEvent e){
    		CBSystemConstants.stateOfTheDrawer =false;
				CBSystemConstants.cardbuildtype = "0";
		    	CBSystemConstants.isLock=true;
		    	CBSystemConstants.isOutCard=true;
		    	CBSystemConstants.isSame=true;
		    	setTicketKind("samesmartTicket");
		    	SvgUtil.clear(); 
		}
		//设备置位/取消
	    protected void equipStatusSet(GUIEvent e){
    		CBSystemConstants.stateOfTheDrawer =false;
	    	if(CBSystemConstants.isLock==true && CBSystemConstants.isOutCard==true) {
		    	CBSystemConstants.cardbuildtype = "2";
		    	CBSystemConstants.isLock=false;
		    	CBSystemConstants.isOutCard=false;
		    	CBSystemConstants.isSame=false;
		    	setTicketKind("equipStatusSet");
		    	if(CBSystemConstants.isInversion) {
					CBSystemConstants.isInversion = false;
					SystemConstants.getMainFrame().setTitle(CBSystemConstants.SYSTEM_TITLE);
					BorderButton button = (BorderButton)frame.getWidget("termInversion").getRealWidget();
					button.setUnSelected();
					InversionTicket.getInstance().close();
					closeAll(e);
				}
	    	}
	    	SvgUtil.clear(); 
		}

		//通过状态成票
	    protected void stateOfTheDrawer(GUIEvent e){

				CBSystemConstants.cardbuildtype = "0";
		    	CBSystemConstants.isLock=true;
		    	CBSystemConstants.isOutCard=true;
		    	CBSystemConstants.isSame=false;
		    	setTicketKind("stateOfTheDrawer");
		    	BorderButton button = (BorderButton)frame.getWidget(e.getName()).getRealWidget();
		    	if(button.isSelected()){
		    		CBSystemConstants.stateOfTheDrawer =true;
		    	}else{
		    		CBSystemConstants.stateOfTheDrawer =false;
		    	}
		    	SvgUtil.clear(); 
		}
	    
	    
		//术语反演
	protected void termInversion(GUIEvent e){
		String addTitle = "---模拟演示";
		if(CBSystemConstants.isLock==false && CBSystemConstants.isOutCard==false) {
    		ShowMessage.view("请先退出设备对位！");
			return;
		}
		
		BorderButton button = (BorderButton)frame.getWidget("termInversion").getRealWidget();
		if(CBSystemConstants.isInversion) {
			CBSystemConstants.isInversion = false;
			SystemConstants.getMainFrame().setTitle(SystemConstants.getMainFrame().getTitle().replace(addTitle, ""));
			button.setUnSelected();
			InversionTicket.getInstance().close();
		}
		else {
			CBSystemConstants.isInversion = true;
			SystemConstants.getMainFrame().setTitle(SystemConstants.getMainFrame().getTitle()+addTitle);
			button.setSelected();
		}
	}
	protected void DeviceTypeInit(GUIEvent e) {
		
		Object[] options = {"已打开厂站", "所有厂站"}; 
    	int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要重置设备接线方式的范围", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
    	if(sel == 0) {
    		JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
    		String names = "";
    		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
    			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
    			String stationID = panel.getStationID();
    			String stationName = panel.getName();
    			if(!stationID.equals("")) {
    				InitDeviceRunType idrt = new InitDeviceRunType();
    				
    				String[] staarr = stationID.split(",");
					for(String sta : staarr) {
						idrt.execute(sta);
					}
					names = names + stationName + ",";
    			}
    		}
    		if(names.equals(""))
    			ShowMessage.view("没有打开厂站图，更新失败！");
    		else
    			ShowMessage.view("["+names.substring(0,names.length()-1)+"]设备接线方式更新完成！");
    	}
    	else if(sel == 1) {
    		for(Iterator it=CBSystemConstants.getMapPowerStation().keySet().iterator();it.hasNext();){
				String stationID = (String)it.next();
				
				if(CBSystemConstants.getPowerStation(stationID).getPowerStationName().indexOf("柴达木")>=0 ||
						CBSystemConstants.getPowerStation(stationID).getPowerStationName().indexOf("白银220")>=0 ||
						CBSystemConstants.getPowerStation(stationID).getPowerStationName().indexOf("中节能风电")>=0)
					continue;
				
				if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
					CreatePowerStationToplogy.loadFacEquip(stationID);
				}
				InitDeviceRunType idrt = new InitDeviceRunType();
				//System.out.println(stationID);
				idrt.execute(stationID);
			}
			ShowMessage.view("设备安装类型更新完成！");
			return;
    	}
	}
	
	//安全校核
	protected void securityCheck(GUIEvent e){
		ShowMessage.view("不能连接到EMS, 无法进行潮流校核！");	
	}
	//CIM接口数据更新
    protected void cimLoad(GUIEvent e){
    	ConvertCIMDialog dialog = new ConvertCIMDialog(SystemConstants.getMainFrame());
    	WindowUtils.centerWindow(SystemConstants.getMainFrame(), dialog);
    	dialog.setVisible(true);
    }
    //SVG接口数据更新
    protected void svgLoad(GUIEvent e){
    	ConvertSVGDialog dialog = new ConvertSVGDialog(SystemConstants.getMainFrame());
    	WindowUtils.centerWindow(SystemConstants.getMainFrame(), dialog);
    	dialog.setVisible(true);
    }
	//关闭选项卡
	public boolean closeTabbedPage(int index) {
        SVGCanvasPanel selpanel = (SVGCanvasPanel) getSVGJTabbedPane().getComponentAt(index);
    	if(SystemConstants.getMapSVGFile().get(selpanel.getFilePath())!=null &&
    			SystemConstants.getMapSVGFile().get(selpanel.getFilePath()).isAlwaysOpen()) {
			return false;
		}
    	//解决超过8个卡的问题
        String stationID = selpanel.getStationID();
        SvgP svgp=CBSystemConstants.svgps.get(stationID);
        CBSystemConstants.svgps.remove(svgp);
        //
        getSVGJTabbedPane().remove(index);
        final SVGCanvas canvas = selpanel.getSvgCanvas();
        new Thread(new Runnable() {
        	public void run() {
        		canvas.destroyCanvas();
            	System.gc();
        	}
	 	  }).start();
    	
        if(!stationID.equals("")) {
        	boolean isNeedRemoveData = true;
        	JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
    		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
    			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
    			if(panel.getStationID().equals(stationID))
    				isNeedRemoveData = false; //该站还有打开的图形不清除内存设备数据
    		}
    		if(isNeedRemoveData) {
    			if(stationID.contains(",")) {
	    			String[] staarr = stationID.split(",");
					for(String sta : staarr) {
						SystemConstants.removeLineOrStation(sta, selpanel.getSvgDocument());
		    			CBSystemConstants.removeMapPowerStationDevices(sta);
					}
    			}
    			else {
	    			SystemConstants.removeLineOrStation(stationID, selpanel.getSvgDocument());
	    			CBSystemConstants.removeMapPowerStationDevices(stationID);
    			}
    		}
        }
        CBSystemConstants.clearSamepdlist();
        return true;
    }
	
	protected void closeSVG(){
		this.closeTabbedPage(getSVGJTabbedPane().getSelectedIndex());
		
	}
	
	//关闭窗口
	protected void closeSVGPanel(GUIEvent e) {
		boolean result = true;
		if(e.getSwingEvent() instanceof MouseEvent) {
			MouseEvent event = (MouseEvent) e.getSwingEvent();
			//解决超过8个卡的问题
			if(event.getClickCount() == 1){
				JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
				//当前
				int n=getSVGJTabbedPane().getSelectedIndex();
				SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(n);
				String station=sel.getStationID();
				boolean isdestry=sel.isIsdestry();
				if(isdestry==true && !station.equals("")){
					SvgP svgp=CBSystemConstants.svgps.get(station);
					if(svgp != null) {
						File svgMapFile = new File(svgp.getFilePath());
						sel.loadSvgFile(svgMapFile);
						sel.setIsdestry(false);
						//所有
						int m=tabbedPane.getComponentCount();
						for(int i=0;i<m;i++){
							SVGCanvasPanel selpanel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
			    			String stationID = selpanel.getStationID();
			    			boolean allisdestry=selpanel.isIsdestry();
			    			if(allisdestry==false){
				    			final SVGCanvas canvas = selpanel.getSvgCanvas();
				    		    new Thread(new Runnable() {
						        	public void run() {
						        		canvas.destroyCanvas();
						            	System.gc();
						        	}
						 	    }).start();
				    		    selpanel.setIsdestry(true);
				    		    break;
			    			}
						}
					}
				}
			}
			
			//
			if (event.getClickCount() == 2){
				result = this.closeTabbedPage(getSVGJTabbedPane().getSelectedIndex());
				//缓存解决
				JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
				int m=tabbedPane.getComponentCount();
				int n=getSVGJTabbedPane().getSelectedIndex();
				if(n>=0){
					SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(m-1);
					String station=sel.getStationID();
					boolean isdestry=sel.isIsdestry();
					if(isdestry==true){
						SvgP svgp=CBSystemConstants.svgps.get(station);
						if(svgp != null) {
							File svgMapFile = new File(svgp.getFilePath());
							sel.loadSvgFile(svgMapFile);
							sel.setIsdestry(false);
						}
					}
				}
				//
			}
		}
		else{
			result = this.closeTabbedPage(getSVGJTabbedPane().getSelectedIndex());
			//缓存解决
			JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
			int m=tabbedPane.getComponentCount();
			int n=getSVGJTabbedPane().getSelectedIndex();
			if(n>=0){
				SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(m-1);
				String station=sel.getStationID();
				boolean isdestry=sel.isIsdestry();
				if(isdestry==true){
					SvgP svgp=CBSystemConstants.svgps.get(station);
					if(svgp != null) {
						File svgMapFile = new File(svgp.getFilePath());
						sel.loadSvgFile(svgMapFile);
						sel.setIsdestry(false);
					}
				}
			}
//			result = this.closeTabbedPage(getSVGJTabbedPane().getSelectedIndex());
//			//缓存问题
//			JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
//			int n=getSVGJTabbedPane().getSelectedIndex();
//			int m=tabbedPane.getComponentCount();
//			if(n>=0){
//				SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(m-1);
//				String station=sel.getStationID();
//				SvgP svgp=CBSystemConstants.svgps.get(station);
//				if(svgp != null) {
//					File svgMapFile = new File(svgp.getFilePath());
//					sel.loadSvgFile(svgMapFile);
//					sel.setIsdestry(false);
//				}
//			}
			//
		}
		if(!result) {
			String tabName = ((SVGCanvasPanel) getSVGJTabbedPane().getSelectedComponent()).getName();
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "["+tabName+"]不能关闭！","提示", JOptionPane.WARNING_MESSAGE);
		}
	}
	//关闭其他窗口
	protected void closeOthers(GUIEvent e) {
		for(int i = getSVGJTabbedPane().getTabCount()-1; i >=0; i--) {
			if(i != getSVGJTabbedPane().getSelectedIndex()){
				this.closeTabbedPage(i);
			}
		}
		JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
		int n=getSVGJTabbedPane().getSelectedIndex();
		SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(n);
		String station=sel.getStationID();
		boolean isdestry=sel.isIsdestry();
		if(isdestry==true){
			SvgP svgp=CBSystemConstants.svgps.get(station);
			File svgMapFile = new File(svgp.getFilePath());
			sel.loadSvgFile(svgMapFile);
			sel.setIsdestry(false);
		}
	}
	//关闭所有窗口
	protected void closeAll(GUIEvent e) {
		for(int i = getSVGJTabbedPane().getTabCount()-1; i >=0; i--) {
			this.closeTabbedPage(i);
		}
	}
	//打开关闭窗口菜单
	protected void openCloseMenu(GUIEvent e) {
		MouseEvent event = (MouseEvent) e.getSwingEvent();
		//this.closeTabbedPage(event);
		getSVGCloseMenu().show(getSVGJTabbedPane(), event.getX(), event.getY());
	}
	// 退出系统
	protected void exitSystem(GUIEvent e) {
		JFrame f = new JFrame();
		int retval = JOptionPane.showConfirmDialog(f, "您确定退出系统吗？", "退出程序",
				JOptionPane.YES_NO_OPTION);
		if (retval == JOptionPane.YES_OPTION) {
			System.exit(0);
		}else{
			return;
		}
	}	
	//操作票票号设置
	protected void setFlowNum(GUIEvent e) {
		new SetFlowDialog().setVisible(true);
		
	}
	
	//操作票查询
	protected void openTicketQueryDialog(GUIEvent e){
		CzpQuerryDialog tcd = new CzpQuerryDialog(SystemConstants.getMainFrame(),true);
		tcd.setVisible(true);
	}
	
	//操作票统计
		protected void openTicketCountDialog(GUIEvent e){
			CzpCountDialog tcd = new CzpCountDialog(SystemConstants.getMainFrame(),true);
			tcd.setVisible(true);
		}
}
