/*
 * AddUserDialog.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.user;

import java.awt.Toolkit;

import javax.swing.DefaultComboBoxModel;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;
import javax.swing.GroupLayout;
import javax.swing.GroupLayout.Alignment;
import javax.swing.LayoutStyle.ComponentPlacement;

import org.apache.bsf.util.event.adapters.java_awt_event_ActionAdapter;

/**
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class AddUserDialog extends javax.swing.JDialog {

	/** Creates new form AddUserDialog */
	public AddUserDialog(javax.swing.JDialog parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("用户管理-新增用户");
		this.init();
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	public void init() {
		DefaultComboBoxModel comboxModel = new DefaultComboBoxModel();
		comboxModel.addElement(new CodeNameModel("0", "管理员"));
		comboxModel.addElement(new CodeNameModel("1", "调度员"));
		comboxModel.addElement(new CodeNameModel("2", "副职调度长"));
		comboxModel.addElement(new CodeNameModel("3", "正职调度长"));
		comboxModel.addElement(new CodeNameModel("4", "普通用户"));
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jLabel1 = new javax.swing.JLabel();
		jLabel6 = new javax.swing.JLabel();
		jLabel2 = new javax.swing.JLabel();
		jTextField1 = new javax.swing.JTextField();
		jTextField2 = new javax.swing.JTextField();
		jPasswordField1 = new javax.swing.JPasswordField();
		jLabel4 = new javax.swing.JLabel();
		jPasswordField2 = new javax.swing.JPasswordField();
		jLabel5 = new javax.swing.JLabel();
		jSeparator1 = new javax.swing.JSeparator();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/save.png"))); // NOI18N
		jButton1.setToolTipText("\u4fdd\u5b58");
		jButton1.setBorder(null);
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png"))); // NOI18N
		jButton2.setToolTipText("\u53d6\u6d88");
		jButton2.setBorder(null);
		jButton2.setFocusPainted(false);
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jLabel1.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel1.setText("\u7528 \u6237 \u540d\uff1a");
		
		jLabel6.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel6.setText("\u767b\u0020\u9646\u0020\u540d\uff1a");

		jLabel2.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel2.setText("\u5bc6    \u7801\uff1a");

		jTextField1.setFont(new java.awt.Font("宋体", 0, 14));
		jTextField2.setFont(new java.awt.Font("宋体", 0, 14));

		jPasswordField1.setFont(new java.awt.Font("宋体", 0, 14));

		jLabel4.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel4.setText("\u786e\u5b9a\u5bc6\u7801\uff1a");

		jPasswordField2.setFont(new java.awt.Font("宋体", 0, 14));

		jLabel5.setFont(new java.awt.Font("微软雅黑", 1, 14));
		jLabel5.setText("\u65b0\u589e\u7528\u6237\u4fe1\u606f\uff1a");

		GroupLayout layout = new GroupLayout(
						getContentPane());
		layout.setHorizontalGroup(
			layout.createParallelGroup(Alignment.TRAILING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap()
					.addComponent(jLabel5, GroupLayout.PREFERRED_SIZE, 138, GroupLayout.PREFERRED_SIZE)
					.addPreferredGap(ComponentPlacement.RELATED, 204, Short.MAX_VALUE)
					.addComponent(jButton1)
					.addGap(18)
					.addComponent(jButton2)
					.addContainerGap())
				.addGroup(layout.createSequentialGroup()
					.addGap(48)
					.addGroup(layout.createParallelGroup(Alignment.TRAILING, false)
						.addComponent(jLabel1, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
						.addComponent(jLabel6, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
						.addComponent(jLabel2, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
						.addComponent(jLabel4, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
					.addPreferredGap(ComponentPlacement.RELATED)
					.addGroup(layout.createParallelGroup(Alignment.LEADING)
						.addComponent(jPasswordField2, Alignment.TRAILING, GroupLayout.DEFAULT_SIZE, 242, Short.MAX_VALUE)
						.addComponent(jPasswordField1, Alignment.TRAILING, GroupLayout.DEFAULT_SIZE, 242, Short.MAX_VALUE)
						.addComponent(jTextField1, GroupLayout.DEFAULT_SIZE, 242, Short.MAX_VALUE)
						.addComponent(jTextField2, GroupLayout.DEFAULT_SIZE, 242, Short.MAX_VALUE))
					.addGap(48))
				.addComponent(jSeparator1, GroupLayout.DEFAULT_SIZE, 412, Short.MAX_VALUE)
		);
		layout.setVerticalGroup(
			layout.createParallelGroup(Alignment.TRAILING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap()
					.addGroup(layout.createParallelGroup(Alignment.TRAILING)
						.addComponent(jLabel5, GroupLayout.PREFERRED_SIZE, 26, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton1)
						.addComponent(jButton2))
					.addPreferredGap(ComponentPlacement.RELATED)
					.addComponent(jSeparator1, GroupLayout.PREFERRED_SIZE, 10, GroupLayout.PREFERRED_SIZE)
					.addPreferredGap(ComponentPlacement.RELATED, 33, Short.MAX_VALUE)
					.addGroup(layout.createParallelGroup(Alignment.BASELINE)
						.addComponent(jTextField1, GroupLayout.PREFERRED_SIZE, 33, GroupLayout.PREFERRED_SIZE)
						.addComponent(jLabel1))
					.addGap(18)
					.addGroup(layout.createParallelGroup(Alignment.BASELINE)
						.addComponent(jTextField2, GroupLayout.PREFERRED_SIZE, 33, GroupLayout.PREFERRED_SIZE)
						.addComponent(jLabel6))
					.addGap(18)
					.addGroup(layout.createParallelGroup(Alignment.BASELINE)
						.addComponent(jPasswordField1, GroupLayout.PREFERRED_SIZE, 29, GroupLayout.PREFERRED_SIZE)
						.addComponent(jLabel2))
					.addGap(18)
					.addGroup(layout.createParallelGroup(Alignment.BASELINE)
						.addComponent(jPasswordField2, GroupLayout.PREFERRED_SIZE, 29, GroupLayout.PREFERRED_SIZE)
						.addComponent(jLabel4))
					.addGap(109))
		);
		getContentPane().setLayout(layout);

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//保存
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if ("".equals(this.jTextField1.getText().trim())) {
			ShowMessage.view(this, "用户名不能为空!");
			return;
		}
		if ("".equals(this.jTextField2.getText().trim())) {
			ShowMessage.view(this, "登录名不能为空!");
			return;
		}
		if ("".equals(this.jPasswordField1.getText().trim())) {
			ShowMessage.view(this, "密码不能为空!");
			return;
		}
		if (!this.jPasswordField1.getText().trim()
				.equals(this.jPasswordField2.getText().trim())) {
			ShowMessage.view(this, "密码不一致，请重新输入!");
			return;
		}
//		if (this.jComboBox1.getSelectedItem() == null) {
//			ShowMessage.view(this, "请选择用户类型!");
//			return;
//		}
		User user = new User();
		user.setUserID(java.util.UUID.randomUUID().toString());
		user.setUserName(this.jTextField1.getText().trim());
		user.setLoginname(this.jTextField2.getText().trim());
		user.setPassword(tbp.common.util.StringUtil.getMD5(jPasswordField1.getText().trim()));
//		CodeNameModel cnm = (CodeNameModel) this.jComboBox1.getSelectedItem();
//		user.setUserDuty(cnm.getCode());
		user.setUserDuty("4");
		UserDao ud = new UserDao();
		boolean isok=ud.addUserAndRole(user, CBSystemConstants.roleCode);
		
		if(isok==true){
			this.setVisible(false);
			this.dispose();
		}
	}

	//取消
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.setVisible(false);
		this.dispose();
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JLabel jLabel4;
	private javax.swing.JLabel jLabel5;
	private javax.swing.JLabel jLabel6;//登录名
	private javax.swing.JPasswordField jPasswordField1;
	private javax.swing.JPasswordField jPasswordField2;
	private javax.swing.JSeparator jSeparator1;
	private javax.swing.JTextField jTextField1;
	private javax.swing.JTextField jTextField2;
	// End of variables declaration//GEN-END:variables

}
