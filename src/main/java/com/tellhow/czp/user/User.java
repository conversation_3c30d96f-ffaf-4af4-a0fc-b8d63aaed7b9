/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：
* 功能说明 : 用来保存用户基本信息程序
* 作    者 : 邹力兴
* 开发日期 : 2008-08-4
* 修改日期 ：2012-02-29
* 修改说明 ：
* 修 改 人 ：张余平
**/

package com.tellhow.czp.user;

/**
 * 用户信息类
 * <AUTHOR>
 */
public class User {
    private String userID;  //用户ID
    private String userName;  //用户名
    private String password="";    //用户登入密码
    private String userDuty; //用户类型(1：调度员 2：副职调度长 3：正职调度长 4：普遍用户 0：管理员)
    private String userDutyName;//用户类型名称(1：调度员 2：副职调度长 3：正职调度长 4：普遍用户 0：管理员)
    private String unitCode;//区域编码
    private String organID="";//机构ID
    private String loginname;//用户登录名
	private boolean isZW=false;
    private boolean isPW=false;
    private String organGrade = "";  //调度级别 2：地调 3：县调
    
    
    public boolean isZW() {
		return isZW;
	}

	public void setZW(boolean isZW) {
		this.isZW = isZW;
	}

	public boolean isPW() {
		return isPW;
	}

	public void setPW(boolean isPW) {
		this.isPW = isPW;
	}
	



	/**
     * 
     * @param userID 用户ID
     * @param userCNName 用户中文名
     * @param password 用户登入密码
     * @param userDuty 职务(3-值长2-正值1-副值0-实习)
     * @param unitCode 机构编码
     * @param userloginname 用户登录名
     */
    public User(String userID,String userName,String password,String userDuty,String unitCode,String loginname){
        this.userID = userID;
        this.userName = userName.trim();
        this.password=password.trim();
        this.userDuty=userDuty;
        this.unitCode=unitCode.trim(); 
        this.loginname=loginname.trim();
    }
    
	/**
     * 
     * @param userID 用户ID
     * @param userCNName 用户中文名
     * @param password 用户登入密码
     * @param userDuty 职务(3-值长2-正值1-副值0-实习)
     * @param unitCode 机构编码
     */
    public User(String userID,String userName,String password,String userDuty,String unitCode){
        this.userID = userID;
        this.userName = userName.trim();
        this.password=password.trim();
        this.userDuty=userDuty;
        this.unitCode=unitCode.trim(); 
    }
    
    public User(){}


    
    public String getUserID() {
		return userID;
	}



	public void setUserID(String userID) {
		this.userID = userID;
	}



	public String getUserName() {
		return userName;
	}



	public void setUserName(String userName) {
		this.userName = userName;
	}



	public String getPassword() {
		return password;
	}



	public void setPassword(String password) {
		this.password = password;
	}



	public String getUserDuty() {
		return userDuty;
	}



	public void setUserDuty(String userDuty) {
		this.userDuty = userDuty;
	}



	public String getUnitCode() {
		return unitCode;
	}



	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}

	public String getOrganID() {
		return organID;
	}

	public void setOrganID(String organID) {
		this.organID = organID;
	}

	@Override
	public String toString(){
        return this.userName;
    }
    /**
     * 获取职务名称
     */
    public String getUserDutyName(){
        String[] dutyName = {"管理员","调度员","副值","正值","用户"};
        return dutyName[Integer.parseInt(userDuty)];
    }

	public void setLoginname(String loginname) {
		this.loginname = loginname;
	}

	public String getLoginname() {
		return loginname;
	}

	public String getOrganGrade() {
		return organGrade;
	}

	public void setOrganGrade(String organGrade) {
		this.organGrade = organGrade;
	}

	public void setUserDutyName(String userDutyName) {
		this.userDutyName = userDutyName;
	}
	
	public String getUserDutyName(String userDutyName) {
		return userDutyName;
	}
}
