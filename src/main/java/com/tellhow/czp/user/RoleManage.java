/*
 * OperateTicketUserManage.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.user;

import java.awt.Toolkit;
import java.util.List;

import javax.swing.JOptionPane;
import javax.swing.table.DefaultTableModel;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.system.ShowMessage;

/**
 *
 * <AUTHOR>
 */
public class RoleManage extends javax.swing.JDialog {
	private DefaultTableModel dTableModel;

	/** Creates new form OperateTicketUserManage */
	public RoleManage(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("角色管理");
		setLocationCenter();
		initTable();
	}

	/**
	 * 初始化表格
	 */
	public void initTable() {
		dTableModel = new DefaultTableModel(null, new String[] { "序号", "角色名",
				"用户类型" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		UserDao ud = new UserDao();
		List<User> userLists = ud.getAllUser();
		User user = null;
		for (int i = 0; i < userLists.size(); i++) {
			user = userLists.get(i);
			dTableModel
					.addRow(new Object[] {
							new CodeNameModel(user.getUserID(), String.valueOf(i + 1)),
							user.getUserName(), user.getUserDuty() });
		}
		jTable1.setModel(dTableModel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jButton3 = new javax.swing.JButton();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();
		jButton2 = new javax.swing.JButton();
		jButton1 = new javax.swing.JButton();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/add.png"))); // NOI18N
		jButton3.setToolTipText("\u65b0\u589e");
		jButton3.setBorder(null);
		jButton3.setFocusPainted(false);
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));

		jTable1.setFont(new java.awt.Font("宋体", 0, 13));
		jTable1.setModel(new javax.swing.table.DefaultTableModel(
				new Object[][] { { null, null, null, null },
						{ null, null, null, null }, { null, null, null, null },
						{ null, null, null, null } }, new String[] { "Title 1",
						"Title 2", "Title 3", "Title 4" }));
		jTable1.setRowHeight(26);
		jScrollPane1.setViewportView(jTable1);

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton2.setToolTipText("\u5220\u9664");
		jButton2.setBorder(null);
		jButton2.setFocusPainted(false);
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/order.gif"))); // NOI18N
		jButton1.setToolTipText("\u6392\u5e8f");
		jButton1.setBorder(null);
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.addContainerGap()
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.LEADING)
								.add(jScrollPane1,
										org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
										491, Short.MAX_VALUE)
								.add(layout
										.createSequentialGroup()
										.add(jButton3)
										.addPreferredGap(
												org.jdesktop.layout.LayoutStyle.RELATED)
										.add(jButton2)
										.addPreferredGap(
												org.jdesktop.layout.LayoutStyle.RELATED)
										.add(jButton1))).addContainerGap()));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.addContainerGap()
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.BASELINE)
								.add(jButton3).add(jButton2).add(jButton1))
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jScrollPane1,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								394, Short.MAX_VALUE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//排序
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		UserOrderManager uom = new UserOrderManager(this, true);
		uom.setVisible(true);
		this.initTable();
	}

	//新增
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		AddRoleDialog aud = new AddRoleDialog(this, true);
		aud.setVisible(true);
		this.initTable();

	}

	//删除
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要删除的记录");
			return;
		}
		int isOk=JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "是否要删除已选择的用户？",SystemConstants.SYSTEM_TITLE, JOptionPane.YES_NO_OPTION);
		if(isOk==JOptionPane.NO_OPTION){
			return;
		}
		jTable1.removeEditor();
		dTableModel = (DefaultTableModel) jTable1.getModel();
		UserDao ud = new UserDao();
		CodeNameModel cnm = null;
		for (int i = selectRows.length - 1; i >= 0; i--) {
			cnm = (CodeNameModel) this.jTable1.getValueAt(selectRows[i], 0);
			ud.delUser(cnm.getCode());
		}
		initTable();
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				RoleManage dialog = new RoleManage(new javax.swing.JFrame(),
						true);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTable1;
	// End of variables declaration//GEN-END:variables

}
