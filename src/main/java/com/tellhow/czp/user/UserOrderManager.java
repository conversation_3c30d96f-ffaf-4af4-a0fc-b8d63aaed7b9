/*
 * UserOrderManager.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.user;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import javax.swing.table.DefaultTableModel;

import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;

/**
 *
 * <AUTHOR>
 */
public class UserOrderManager extends javax.swing.JDialog {
	private DefaultTableModel jTable1Model = null;

	/** Creates new form UserOrderManager */
	public UserOrderManager(javax.swing.JDialog parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("用户管理 —用户排序");
		initTable();
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/**
	 * 初始化表格
	 */
	public void initTable() {
		jTable1Model = new DefaultTableModel(null,
				new String[] { "用户名", "用户类型" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		UserDao ud = new UserDao();
		List<User> userLists = ud.getAllUser(CBSystemConstants.getUser().getUnitCode(),CBSystemConstants.roleCode);
		User user = null;
		for (int i = 0; i < userLists.size(); i++) {
			user = userLists.get(i);
			jTable1Model.addRow(new Object[] {
					new CodeNameModel(user.getUserID(), user.getUserName()),
					user.getUserDutyName() });
		}
		jTable1.setModel(jTable1Model);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jButton3 = new javax.swing.JButton();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/btn_up.png"))); // NOI18N
		jButton1.setToolTipText("\u4e0a\u79fb");
		jButton1.setBorder(null);
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/btn_down.png"))); // NOI18N
		jButton2.setToolTipText("\u4e0b\u79fb");
		jButton2.setBorder(null);
		jButton2.setFocusPainted(false);
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/save.png"))); // NOI18N
		jButton3.setToolTipText("\u4fdd\u5b58");
		jButton3.setBorder(null);
		jButton3.setFocusPainted(false);
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		jTable1.setFont(new java.awt.Font("宋体", 0, 13));
		jTable1.setModel(new javax.swing.table.DefaultTableModel(
				new Object[][] { { null, null, null, null },
						{ null, null, null, null }, { null, null, null, null },
						{ null, null, null, null } }, new String[] { "Title 1",
						"Title 2", "Title 3", "Title 4" }));
		jTable1.setRowHeight(26);
		jScrollPane1.setViewportView(jTable1);

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.addContainerGap()
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.LEADING)
								.add(jScrollPane1,
										org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
										404, Short.MAX_VALUE)
								.add(layout
										.createSequentialGroup()
										.add(jButton1)
										.addPreferredGap(
												org.jdesktop.layout.LayoutStyle.RELATED)
										.add(jButton2)
										.addPreferredGap(
												org.jdesktop.layout.LayoutStyle.RELATED)
										.add(jButton3))).addContainerGap()));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.addContainerGap()
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.BASELINE)
								.add(jButton1).add(jButton2).add(jButton3))
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jScrollPane1,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								415, Short.MAX_VALUE).addContainerGap()));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//保存
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		List<String> userOrder = new ArrayList<String>();
		CodeNameModel cnm = null;
		for (int i = 0; i < this.jTable1.getRowCount(); i++) {
			cnm = (CodeNameModel) this.jTable1.getValueAt(i, 0);
			userOrder.add(cnm.getCode());
		}
		UserDao ud = new UserDao();
		ud.updateUserOrder(userOrder);
		this.setVisible(false);
		this.dispose();
	}

	//下移
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.movedownTableRow(jTable1);
	}

	//上移
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.moveupTableRow(jTable1);
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTable1;
	// End of variables declaration//GEN-END:variables

}
