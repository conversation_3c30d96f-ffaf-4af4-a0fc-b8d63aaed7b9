/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：
* 功能说明 : 登入窗口程序
* 作    者 : 邹力兴
* 开发日期 : 2008-08-4
* 修改日期 ：2012-02-29
* 修改说明 ：
* 修 改 人 ：张余平
**/

package com.tellhow.czp.user;

import java.awt.Graphics;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.List;
import java.util.Map;

import javax.swing.BorderFactory;
import javax.swing.DefaultComboBoxModel;
import javax.swing.GroupLayout;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.SwingConstants;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.DOMUtil;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/**
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class UserLoginDefault extends UserLoginInter{
	private JPanel content;             //组建面板
    private JLabel l_UserName;          //用户名标签
    private JLabel l_PassWord;          //密码标签
    private JLabel l_state;             //用户密码验证信息提示标签
    private DefaultComboBoxModel dcbUser;
    private JComboBox cb_UserName;     //用户名输入框 下拉框
    private JCheckBox  r_UserName;           //记住密码
    private JLabel  j_UserName;  //记住用户  
    private JPasswordField pf_PassWord; //密码输入框
    private JButton b_Login;            //登录按钮
    private JButton b_cancel;           //取消按钮
    private JCheckBox  r_Password;           //记住密码
    private JLabel  l_Password;    
    private boolean isEnterSystem=false;
    private String title;

	// 添加成员变量
	private int loginFailCount = 0;
	private static final int MAX_LOGIN_FAILS = 5;
	private static final int LOCK_MINUTES = 15;

    public UserLoginDefault(){
        init(title);
    }
    /**
     * 初始化用户下拉列表
     */
    private void initUserList(){
        dcbUser=new DefaultComboBoxModel();
        UserDao userdao=new UserDao();
        List<User> allusers=userdao.getAllUserLogin();
        for (int i = 0; i < allusers.size(); i++) {
        	 User user = allusers.get(i);
        	 CodeNameModel cnm = new CodeNameModel(user.getUserID(),user.getUserName());
             dcbUser.addElement(cnm);
             if(user.getUserID().equals(CBSystemConstants.defaultUser))
            	 dcbUser.setSelectedItem(cnm);
		}
        cb_UserName.setModel(dcbUser);
    }
    /**
     * 判断密码是否被记住
     */
    private void initPassword(){
        if(CBSystemConstants.rememberPassword.equals("1")){//密码被记住
        	r_Password.setSelected(true);
        	pf_PassWord.setText(CBSystemConstants.password);
        }
    }
    
    /**
     * 验证用户登入信息
     */
    private boolean validateLogin(){
        char[] a_password = pf_PassWord.getPassword();
        String password = new String(a_password);
        if(cb_UserName.getSelectedItem() instanceof String) {
        	setPrompt("用户名不存在！");
            return false;
        }
        CodeNameModel cnm = (CodeNameModel)cb_UserName.getSelectedItem();
        UserDao userdao=new UserDao();
        User user = userdao.getUserByID(cnm.getCode());

		// 先检查用户是否被锁定
		java.util.Date unlockTime = userdao.checkUserLocked(cnm.getCode());
		if (unlockTime != null) {
			// 用户当前被锁定
			long remainingTime = unlockTime.getTime() - System.currentTimeMillis();
			if (remainingTime > 0) {
				int remainingMinutes = (int) (remainingTime / (60 * 1000));
				int remainingSeconds = (int) ((remainingTime / 1000) % 60);
				javax.swing.JOptionPane.showMessageDialog(this,
						"账号已锁定，将在" + remainingMinutes + "分钟" + remainingSeconds + "秒后解锁。",
						"账号锁定", javax.swing.JOptionPane.WARNING_MESSAGE);
				/*if(SystemConstants.getMainFrame() == null)
					System.exit(0);
				else
					CloseDialog();*/
				return false;
			}
		}
        
        if(user==null){
            setPrompt("用户名不能为空！");
            return false;
        }else if(password == null || password.equals("")){
            setPrompt("密码不能为空！");
            return false;
        }else{
        	if(SystemConstants.getMainFrame()!=null&&user.getUserID()==CBSystemConstants.getUser().getUserID()){
        		setPrompt("该用户正在线！");
                return false;
        	}
        	
            if(user.getPassword().equals(tbp.common.util.StringUtil.getMD5(password))){
				// 密码正确，重置失败计数
				loginFailCount = 0;
            	 //密码输入正确后更新到数据库
            	if(CBSystemConstants.rememberPassword.equals("1")){
            		
            	     CBSystemConstants.rememberPassword = "1";
            	     CBSystemConstants.password = password;
            	      
            	}else{
            		
            		 CBSystemConstants.rememberPassword = "0";
              	     CBSystemConstants.password = "";
            		
            	}
            	CBSystemConstants.unitCode=user.getUnitCode();
            	CBSystemConstants.setUser(user);
            	CBSystemConstants.defaultUser = user.getUserID();
            	Document doc = DOMUtil.readXMLFile(CBSystemConstants.SYS_CONFIG_XML_FILE);
        		Element rootE = doc.getDocumentElement();
                NodeList childEs = rootE.getChildNodes();
                Element childE = null;
                for (int i = 0; i < childEs.getLength(); i++) {
                	if(childEs.item(i).getNodeName().equals("#text"))
                		continue;
                	childE = (Element) childEs.item(i);
                	if (childE.getAttribute("name").toUpperCase().equals("PROJECTPARAM")) { 
                		Element codeElem = null;
                        String codeKey = "";
                        String codeValue="";
                		NodeList allVlaues = childE.getElementsByTagName("value");
                        for (int j = 0; j < allVlaues.getLength(); j++) {
                            codeElem = (Element) allVlaues.item(j);
                            codeKey = codeElem.getAttribute("key").trim();
                            codeValue=codeElem.getTextContent().trim();
                            if("defaultUser".equals(codeKey)){
                            	String curUser = codeElem.getTextContent();
                            	if(!curUser.equals(CBSystemConstants.defaultUser)) {
                            		codeElem.setTextContent(CBSystemConstants.defaultUser);
                            		DOMUtil.writeXMLFile(doc, CBSystemConstants.SYS_CONFIG_XML_FILE);
                            	}
                            }else if("rememberPassword".equals(codeKey)){
                            	String curUser = codeElem.getTextContent();
                            	if(!curUser.equals(CBSystemConstants.rememberPassword)) {
                            		codeElem.setTextContent(CBSystemConstants.rememberPassword);
                            		DOMUtil.writeXMLFile(doc, CBSystemConstants.SYS_CONFIG_XML_FILE);
                            	}
                            }else if("password".equals(codeKey)){
                            	String curUser = codeElem.getTextContent();
                            	if(!curUser.equals(CBSystemConstants.password)) {
                            		codeElem.setTextContent(CBSystemConstants.password);
                            		DOMUtil.writeXMLFile(doc, CBSystemConstants.SYS_CONFIG_XML_FILE);
                            	}
                            }
                        }
                        break;
                	}
                }
                
            	return true;
            }else{
				// 密码错误，增加失败计数
				loginFailCount++;

				if (loginFailCount >= MAX_LOGIN_FAILS) {
					// 锁定账号
					userdao.lockUser(user.getUserID(), LOCK_MINUTES);

					// 显示锁定信息
					java.util.Calendar cal = java.util.Calendar.getInstance();
					cal.add(java.util.Calendar.MINUTE, LOCK_MINUTES);
					java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String unlockTimeStr = sdf.format(cal.getTime());

					javax.swing.JOptionPane.showMessageDialog(this,
							"由于密码输入错误次数过多，账号已被锁定！\n解锁时间：" + unlockTimeStr,
							"账号锁定", javax.swing.JOptionPane.WARNING_MESSAGE);

					// 重置失败计数
					loginFailCount = 0;

					if(SystemConstants.getMainFrame() == null)
						System.exit(0);
					else
						CloseDialog();
					return false;
				}

				setPrompt("密码不正确！第" + loginFailCount + "次错误，连续5次错误将锁定账号15分钟！");
                r_Password.setSelected(false);
                pf_PassWord.setText("");
            }
        }
        return false;
    }
    /**
     * 把验证信息提示出来
     */
    private void setPrompt(String error){
    	l_state.requestFocus();
        l_state.setText(error);
    }
    public void enterSystem(){
        this.dispose();
    }

    public void CloseDialog(){
    	this.setVisible(false);
        this.dispose();
    }
	@Override
	public void init(String title) {
		// TODO Auto-generated method stub
		 	this.setModal(true);
	        this.setTitle(title);
	        //放置登录组件的面板
	        content = new JPanel();
	        content.setLayout(null);
			content.setOpaque(false);
			//全局布局
			GroupLayout thisLayout = new GroupLayout((JComponent)getContentPane());
			getContentPane().setLayout(thisLayout);
			thisLayout.setVerticalGroup(thisLayout.createSequentialGroup()
					.addComponent(content, 0, 224, Short.MAX_VALUE));
			thisLayout.setHorizontalGroup(thisLayout.createSequentialGroup()
					.addComponent(content, 0, 340, Short.MAX_VALUE));
	        //初始化组件
	        l_UserName = new JLabel("用户名:",new ImageIcon(getClass().getResource("/tellhow/icons/user.gif")),SwingConstants.CENTER);
	        l_PassWord = new JLabel("密  码:",new ImageIcon(getClass().getResource("/tellhow/icons/password.gif")),SwingConstants.CENTER);
	        cb_UserName = new JAutoCompleteComboBox();
	        pf_PassWord = new JPasswordField();
	        l_Password = new JLabel("记住密码");
	        r_Password = new JCheckBox();
	        j_UserName = new JLabel("记住用户");
	        r_UserName = new JCheckBox();
	        pf_PassWord.enableInputMethods(true);
	    
	        //登录按钮 
	        b_Login = new JButton(){
	        	public void paint(Graphics g){
	        		super.paint(g);
	        		g.drawImage(new ImageIcon(getClass().getResource("/tellhow/icons/ConfigBtn.gif")).getImage(), 0, 0, this);
	        		g.drawString("登 录",20,15);
	        	}
	        };
	        b_Login.setBorder(BorderFactory.createCompoundBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0),null));
	        //取消按钮
	        b_cancel = new JButton(){
	        	public void paint(Graphics g){
	        		super.paint(g);
	        		g.drawImage(new ImageIcon(getClass().getResource("/tellhow/icons/ConfigBtn.gif")).getImage(), 0, 0, this);
	        		g.drawString("取 消",20,15);
	        	}
	        };
	        b_cancel.setBorder(BorderFactory.createCompoundBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0),null));
	        
	        l_state = new JLabel("");
	        l_state.setFont(new java.awt.Font("宋体",1,12));
	        l_state.setForeground(new java.awt.Color(255,0,0));
	        //初始化组件位置
	        l_UserName.setBounds(305, 133, 70, 20);
	        l_PassWord.setBounds(305, 176, 70, 20);
	        cb_UserName.setBounds(400, 133, 110, 20);
	        pf_PassWord.setBounds(400, 176, 110, 20);
	        r_Password.setBounds(510, 175, 18, 18);
	        l_Password.setBounds(530, 175, 50, 18);
	        r_UserName.setBounds(510, 132, 18, 18);
	        j_UserName.setBounds(530, 132, 50, 18);
	        b_Login.setBounds(332,234, 69, 21);
	        b_cancel.setBounds(441,234, 69, 21);
	        l_state.setBounds(20, 276, 560, 20);
	        r_UserName.setSelected(true);
	        r_UserName.setEnabled(false);
	        ItemListener itemListener = new ItemListener() {
	       
	        	 public void itemStateChanged(ItemEvent e) { 
	        		 r_Password = (JCheckBox) e.getSource(); 
	        			if(r_Password.isSelected()){
	        				   char[] a_password = pf_PassWord.getPassword();
	        			        String password = new String(a_password);
	                	if(password.equals("")){
	                		if(CBSystemConstants.rememberPassword.equals("1")){
	                			pf_PassWord.setText(CBSystemConstants.password);
	                			r_Password.setSelected(true);
	                		}else{
	                		 setPrompt("请输入需要保存的密码信息！");
	                		 r_Password.setSelected(false);
	                		}
	                
	                	}else{
	               	     CBSystemConstants.rememberPassword = "1";
	               	     CBSystemConstants.password = password;
	                	}
	               	      
	               	}else{
	               		
	               		 CBSystemConstants.rememberPassword = "0";
	                 	     CBSystemConstants.password = "";
	               		
	               	}
	        	 } 
	        	 }; 
	        	   r_Password.addItemListener(itemListener);  

	        //初始化数据
	        initUserList();
	        //获取用户是否记住密码
	        initPassword();
	        //初始化组件监听事件
	        pf_PassWord.addKeyListener(new KeyAdapter(){
	            public void keyPressed(KeyEvent e){
	            	if(e.getKeyCode()==KeyEvent.VK_ENTER){
	            		if(isEnterSystem=validateLogin()){
	            			enterSystem();
	            		}
	            	}
	            }
	        });
	        l_state.addFocusListener(new FocusAdapter(){
				public void focusLost(FocusEvent arg0) {
					l_state.setText("");
				}
	        });
	        b_Login.addActionListener(new ActionListener(){
	            public void actionPerformed(ActionEvent e){
	                if(isEnterSystem=validateLogin()){
	                    enterSystem();
	                }
	            } 
	        });
	        addWindowListener(new WindowAdapter() {
				public void windowClosing(WindowEvent e) {
	            	if(SystemConstants.getMainFrame()==null)
	            		System.exit(0);
	            	else
	            		CloseDialog();
	            }
	        });
	        b_cancel.addActionListener(new ActionListener(){
	            public void actionPerformed(ActionEvent e){
	            	if(SystemConstants.getMainFrame()==null)
	            		System.exit(0);
	            	else
	            		CloseDialog();
	            }
	        });
		// 初始化错误计数
		loginFailCount = 0;
	        //添加到登入面板
	        content.add(l_UserName);
	        content.add(l_PassWord);
	        content.add(cb_UserName);
	        content.add(r_UserName);
	        content.add(j_UserName);
	        content.add(pf_PassWord);
	        content.add(r_Password);
	        content.add(l_Password);
	        content.add(b_Login);
	        content.add(b_cancel);
	        content.add(l_state);
	        ((JPanel)this.getContentPane()).setOpaque(false);
	        ImageIcon img = new ImageIcon(getClass().getResource("/tellhow/icons/newload.png"));
			JLabel bglabel = new JLabel(img);
			
			this.getLayeredPane().add(bglabel,new Integer(Integer.MIN_VALUE));
			bglabel.setBounds(0, 0, img.getIconWidth(), img.getIconHeight());
			this.setResizable(false);
			this.setSize(600, 400);
	        int w=(int)Toolkit.getDefaultToolkit().getScreenSize().getWidth();
	        int h=(int)Toolkit.getDefaultToolkit().getScreenSize().getHeight();
	        this.setLocation((w-this.getSize().width)/2, (h-this.getSize().height)/2);
	}
}
