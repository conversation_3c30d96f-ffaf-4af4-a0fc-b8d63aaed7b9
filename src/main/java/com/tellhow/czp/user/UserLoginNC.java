/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：
* 功能说明 : 登入窗口程序
* 作    者 : 邹力兴
* 开发日期 : 2008-08-4
* 修改日期 ：2014-08-1
* 修改说明 ：
* 修 改 人 ：
**/

package com.tellhow.czp.user;

import java.awt.Graphics;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

import javax.swing.BorderFactory;
import javax.swing.GroupLayout;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JTextField;
import javax.swing.SwingConstants;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.DOMUtil;

import czprule.system.CBSystemConstants;
import java.awt.Font;
import java.awt.Color;

/**
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class UserLoginNC extends UserLoginInter{
	private JPanel content;             //组建面板
    private JLabel l_UserName;          //用户名标签
    private JLabel l_PassWord;          //密码标签
    private JLabel l_state1;             //用户密码验证信息提示标签
    private JLabel l_state2;
    private JTextField cb_UserName;     //用户名输入框 
    private JPasswordField pf_PassWord; //密码输入框
    private JButton b_Login;            //登录按钮
    private JButton b_cancel;           //取消按钮
    private String title;
    
    
    public UserLoginNC(){
        init(title);
    }
    
	@Override
	public void init(String title) {
		// TODO Auto-generated method stub
		 	this.setModal(true);
	        this.setTitle(title);
	        //放置登录组件的面板
	        content = new JPanel();
	        content.setLayout(null);
			content.setOpaque(false);
			//全局布局
			GroupLayout thisLayout = new GroupLayout((JComponent)getContentPane());
			getContentPane().setLayout(thisLayout);
			thisLayout.setVerticalGroup(thisLayout.createSequentialGroup().addComponent(content, 0, 224, Short.MAX_VALUE));
			thisLayout.setHorizontalGroup(thisLayout.createSequentialGroup().addComponent(content, 0, 340, Short.MAX_VALUE));
	        //初始化组件
	        l_UserName = new JLabel("用户名:",new ImageIcon(getClass().getResource("/tellhow/icons/user.gif")),SwingConstants.CENTER);
	        l_PassWord = new JLabel("密  码:",new ImageIcon(getClass().getResource("/tellhow/icons/password.gif")),SwingConstants.CENTER);
	        cb_UserName = new JTextField() ;
	        pf_PassWord = new JPasswordField();
	        //登录按钮 
	        b_Login = new JButton(){
	        	public void paint(Graphics g){
	        		super.paint(g);
	        		g.drawImage(new ImageIcon(getClass().getResource("/tellhow/icons/ConfigBtn.gif")).getImage(), 0, 0, this);
	        		g.drawString("登 录",20,15);
	        	}
	        };
	        b_Login.setBorder(BorderFactory.createCompoundBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0),null));
	        //取消按钮
	        b_cancel = new JButton(){
	        	public void paint(Graphics g){
	        		super.paint(g);
	        		g.drawImage(new ImageIcon(getClass().getResource("/tellhow/icons/ConfigBtn.gif")).getImage(), 0, 0, this);
	        		g.drawString("取 消",20,15);
	        	}
	        };
	        b_cancel.setBorder(BorderFactory.createCompoundBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0),null));
	        
	        
	        l_state1 = new JLabel("");
	        l_state1.setFont(new java.awt.Font("宋体",1,12));
	        l_state1.setForeground(new java.awt.Color(255,0,0));
	        
	        l_state2 = new JLabel("");
	        l_state2.setFont(new Font("宋体", Font.BOLD, 12));
	        l_state2.setForeground(new java.awt.Color(255,0,0));	        
	        
	        //初始化组件位置
	        l_UserName.setBounds(305, 133, 70, 20);
	        l_PassWord.setBounds(305, 176, 70, 20);
	        cb_UserName.setBounds(400, 133, 110, 20);
	        pf_PassWord.setBounds(400, 176, 110, 20);
	        b_Login.setBounds(332,234, 69, 21);
	        b_cancel.setBounds(441,234, 69, 21);
	        l_state1.setBounds(400, 155, 160, 20);
	        l_state2.setBounds(400, 198, 160, 20);
	        //初始化数据
	        /*initUserList();*/
	        //初始化组件监听事件
	        
	        pf_PassWord.addKeyListener(new KeyAdapter(){
	            public void keyPressed(KeyEvent e){
	            	if(e.getKeyCode()==KeyEvent.VK_ENTER){
	            		if(validateLogin()){
	            			enterSystem();
	            		}
	            	}
	            }
	        });
	        l_state1.addFocusListener(new FocusAdapter(){
				public void focusLost(FocusEvent arg0) {
					l_state1.setText("");
				}
	        });
	        l_state2.addFocusListener(new FocusAdapter(){
				public void focusLost(FocusEvent arg0) {
					l_state2.setText("");
				}
	        });
	        b_Login.addActionListener(new ActionListener(){
	            public void actionPerformed(ActionEvent e){
	                if(validateLogin()){
	                    enterSystem();
	                }
	            } 
	        });
	        addWindowListener(new WindowAdapter() {
				public void windowClosing(WindowEvent e) {
	            	if(SystemConstants.getMainFrame()==null)
	            		System.exit(0);
	            	else
	            		CloseDialog();
	            }
	        });
	        b_cancel.addActionListener(new ActionListener(){
	            public void actionPerformed(ActionEvent e){
	            	if(SystemConstants.getMainFrame()==null)
	            		System.exit(0);
	            	else
	            		CloseDialog();
	            }
	        });
	        //添加到登入面板
	        content.add(l_UserName);
	        content.add(l_PassWord);
	        content.add(cb_UserName);
	        content.add(pf_PassWord);
	        content.add(b_Login);
	        content.add(b_cancel);
	        content.add(l_state1);
	        content.add(l_state2);
	        
	        ((JPanel)this.getContentPane()).setOpaque(false);
	        ImageIcon img = new ImageIcon(getClass().getResource("/tellhow/icons/newload.png"));
			JLabel bglabel = new JLabel(img);
			
			this.getLayeredPane().add(bglabel,new Integer(Integer.MIN_VALUE));
			bglabel.setBounds(0, 0, img.getIconWidth(), img.getIconHeight());
			this.setResizable(false);
			this.setSize(600, 400);
	        int w=(int)Toolkit.getDefaultToolkit().getScreenSize().getWidth();
	        int h=(int)Toolkit.getDefaultToolkit().getScreenSize().getHeight();
	        this.setLocation((w-this.getSize().width)/2, (h-this.getSize().height)/2);
	}
    
    /**
     * 验证用户登入信息
     */
    private boolean validateLogin(){
        char[] a_password = pf_PassWord.getPassword();
        String password = new String(a_password);
        String username = cb_UserName.getText().trim();       
        UserDao userdao=new UserDao();
        User user = userdao.getUserByLoginName(username);
//        String omsname = null;
		User omsuser = userdao.getOMSUserByName(username);

        if(username==null||username.equals("")){
            setPrompt("用户名不能为空！");
            return false;
//        else if(user.getLoginname()==null){
//          setPrompt("用户名不存在！");
//          return false;
//        }
        }else if(user.getLoginname()==null && omsuser.getLoginname()!=null){
        	userdao.addUser(omsuser);
        	return true;
        }else if(user.getLoginname()==null && omsuser.getLoginname()==null){
        	setPrompt("用户名不存在！");
        	return false;
        }
        else if(password == null || password.equals("")){
            setPrompt2("密码不能为空！");
            return false;
        }else{
        	if(SystemConstants.getMainFrame()!=null&&user.getUserID()==CBSystemConstants.getUser().getUserID()){
        		setPrompt("该用户正在线！");
                return false;
        	}
            if(user.getPassword().equals(tbp.common.util.StringUtil.getMD5(password))){
            	//判断密码
            	CBSystemConstants.unitCode=user.getUnitCode();
            	CBSystemConstants.setUser(user);
            	CBSystemConstants.defaultUser = user.getUserID();
            	Document doc = DOMUtil.readXMLFile(CBSystemConstants.SYS_CONFIG_XML_FILE);
        		Element rootE = doc.getDocumentElement();
                NodeList childEs = rootE.getChildNodes();
                Element childE = null;
                for (int i = 0; i < childEs.getLength(); i++) {
                	if(childEs.item(i).getNodeName().equals("#text"))
                		continue;
                	childE = (Element) childEs.item(i);
                	if (childE.getAttribute("name").toUpperCase().equals("PROJECTPARAM")) { 
                		Element codeElem = null;
                        String codeKey = "";
                        NodeList allVlaues = childE.getElementsByTagName("value");
                        for (int j = 0; j < allVlaues.getLength(); j++) {
                            codeElem = (Element) allVlaues.item(j);
                            codeKey = codeElem.getAttribute("key").trim();
                            if("defaultUser".equals(codeKey)){
                            	String curUser = codeElem.getTextContent();
                            	if(!curUser.equals(CBSystemConstants.defaultUser)) {
                            		codeElem.setTextContent(CBSystemConstants.defaultUser);
                            		DOMUtil.writeXMLFile(doc, CBSystemConstants.SYS_CONFIG_XML_FILE);
                            	}
                            	break;
                            }
                        }
                        break;
                	}
                }
            	return true;
            }else{
                setPrompt2("密码不正确！");
                pf_PassWord.setText("");
            }
        }
        return false;
    }
    /**
     * 把验证信息提示出来
     */
    private void setPrompt(String error){
    	l_state1.requestFocus();
        l_state1.setText(error);
    }
    
    /**
     * 把验证信息提示出来
     */
    private void setPrompt2(String error){
    	l_state2.requestFocus();
        l_state2.setText(error);
    }
    
    public void enterSystem(){
        this.dispose();
    }

    public void CloseDialog(){
    	this.setVisible(false);
        this.dispose();
    }
}
