/*
 * AddUserDialog.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.user;

import java.awt.Toolkit;

import javax.swing.DefaultComboBoxModel;

import czprule.model.CodeNameModel;
import czprule.system.ShowMessage;

/**
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class AddRoleDialog extends javax.swing.JDialog {

	/** Creates new form AddUserDialog */
	public AddRoleDialog(javax.swing.JDialog parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("角色管理-新增角色");
		this.init();
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	public void init() {
		DefaultComboBoxModel comboxModel = new DefaultComboBoxModel();
		comboxModel.addElement(new CodeNameModel("0", "管理员"));
		comboxModel.addElement(new CodeNameModel("1", "调度员"));
		comboxModel.addElement(new CodeNameModel("2", "副职调度长"));
		comboxModel.addElement(new CodeNameModel("3", "正职调度长"));
		comboxModel.addElement(new CodeNameModel("4", "普通用户"));
		this.jComboBox1.setModel(comboxModel);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jLabel1 = new javax.swing.JLabel();
		jLabel2 = new javax.swing.JLabel();
		jTextField1 = new javax.swing.JTextField();
		jPasswordField1 = new javax.swing.JPasswordField();
		jLabel4 = new javax.swing.JLabel();
		jPasswordField2 = new javax.swing.JPasswordField();
		jComboBox1 = new javax.swing.JComboBox();
		jLabel3 = new javax.swing.JLabel();
		jLabel5 = new javax.swing.JLabel();
		jSeparator1 = new javax.swing.JSeparator();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/save.png"))); // NOI18N
		jButton1.setToolTipText("\u4fdd\u5b58");
		jButton1.setBorder(null);
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png"))); // NOI18N
		jButton2.setToolTipText("\u53d6\u6d88");
		jButton2.setBorder(null);
		jButton2.setFocusPainted(false);
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jLabel1.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel1.setText("\u7528 \u6237 \u540d\uff1a");

		jLabel2.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel2.setText("\u5bc6    \u7801\uff1a");

		jTextField1.setFont(new java.awt.Font("宋体", 0, 14));

		jPasswordField1.setFont(new java.awt.Font("宋体", 0, 14));

		jLabel4.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel4.setText("\u786e\u5b9a\u5bc6\u7801\uff1a");

		jPasswordField2.setFont(new java.awt.Font("宋体", 0, 14));

		jComboBox1.setFont(new java.awt.Font("宋体", 0, 13));
		jComboBox1.setModel(new javax.swing.DefaultComboBoxModel(new String[] {
				"Item 1", "Item 2", "Item 3", "Item 4" }));

		jLabel3.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel3.setText("\u7528\u6237\u7c7b\u578b\uff1a");

		jLabel5.setFont(new java.awt.Font("微软雅黑", 1, 14));
		jLabel5.setText("\u65b0\u589e\u7528\u6237\u4fe1\u606f\uff1a");

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.addContainerGap()
						.add(jLabel5,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								138,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 200,
								Short.MAX_VALUE).add(jButton1).add(18, 18, 18)
						.add(jButton2).addContainerGap())
				.add(org.jdesktop.layout.GroupLayout.TRAILING,
						layout.createSequentialGroup()
								.add(48, 48, 48)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.TRAILING,
												false)
										.add(jLabel3,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												Short.MAX_VALUE)
										.add(jLabel1,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												Short.MAX_VALUE)
										.add(jLabel2,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												Short.MAX_VALUE)
										.add(jLabel4,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												Short.MAX_VALUE))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.LEADING)
										.add(jComboBox1, 0, 241,
												Short.MAX_VALUE)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jPasswordField2,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												241, Short.MAX_VALUE)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jPasswordField1,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												241, Short.MAX_VALUE)
										.add(jTextField1,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												241, Short.MAX_VALUE))
								.add(48, 48, 48))
				.add(jSeparator1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
						412, Short.MAX_VALUE));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(org.jdesktop.layout.GroupLayout.TRAILING,
						layout.createSequentialGroup()
								.addContainerGap()
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.TRAILING)
										.add(jLabel5,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												26,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jButton1).add(jButton2))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(jSeparator1,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
										10,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED,
										33, Short.MAX_VALUE)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jTextField1,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												33,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jLabel1))
								.add(18, 18, 18)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jPasswordField1,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												29,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jLabel2))
								.add(18, 18, 18)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jPasswordField2,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												29,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jLabel4))
								.add(20, 20, 20)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jLabel3)
										.add(jComboBox1,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												28,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
								.add(73, 73, 73)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//保存
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if ("".equals(this.jTextField1.getText().trim())) {
			ShowMessage.view(this, "用户名不能为空!");
			return;
		}
		if ("".equals(this.jPasswordField1.getText().trim())) {
			ShowMessage.view(this, "密码不能为空!");
			return;
		}
		if (!this.jPasswordField1.getText().trim()
				.equals(this.jPasswordField2.getText().trim())) {
			ShowMessage.view(this, "密码不一致，请重新输入!");
			return;
		}
		if (this.jComboBox1.getSelectedItem() == null) {
			ShowMessage.view(this, "请选择用户类型!");
			return;
		}
		User user = new User();
		user.setUserID(java.util.UUID.randomUUID().toString());
		user.setUserName(this.jTextField1.getText().trim());
		user.setPassword(tbp.common.util.StringUtil.getMD5(jPasswordField1.getText().trim()));
		CodeNameModel cnm = (CodeNameModel) this.jComboBox1.getSelectedItem();
		user.setUserDuty(cnm.getCode());
		UserDao ud = new UserDao();
		ud.addUser(user);

		this.setVisible(false);
		this.dispose();
	}

	//取消
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.setVisible(false);
		this.dispose();
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JComboBox jComboBox1;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JLabel jLabel3;
	private javax.swing.JLabel jLabel4;
	private javax.swing.JLabel jLabel5;
	private javax.swing.JPasswordField jPasswordField1;
	private javax.swing.JPasswordField jPasswordField2;
	private javax.swing.JSeparator jSeparator1;
	private javax.swing.JTextField jTextField1;
	// End of variables declaration//GEN-END:variables

}
