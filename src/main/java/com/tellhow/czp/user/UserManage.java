/*
 * OperateTicketUserManage.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.user;

import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

import javax.swing.GroupLayout;
import javax.swing.GroupLayout.Alignment;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JOptionPane;
import javax.swing.LayoutStyle.ComponentPlacement;
import javax.swing.table.DefaultTableModel;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

/**
 *
 * <AUTHOR>
 */
public class UserManage extends javax.swing.JDialog {
	private DefaultTableModel dTableModel;

	/** Creates new form OperateTicketUserManage */
	public UserManage(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("用户管理");
		setLocationCenter();
		initTable();
	}

	/**
	 * 初始化表格
	 */
	public void initTable() {
		dTableModel = new DefaultTableModel(null, new String[] { "序号", "用户名","登录名",
				"用户类型" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		UserDao ud = new UserDao();
		List<User> userLists = new ArrayList<User>();
		//fus 0：管理员
		if(CBSystemConstants.getUser().getUserDutyName().equals("管理员")){
			userLists = ud.getAllUser(CBSystemConstants.getUser().getUnitCode());
		}else {
			User uu = ud.getUserByID(CBSystemConstants.getUser().getUserID());
			userLists.add(uu);
		}
		User user = null;
		for (int i = 0; i < userLists.size(); i++) {
			user = userLists.get(i);
			dTableModel
					.addRow(new Object[] {
							new CodeNameModel(user.getUserID(), String.valueOf(i + 1)),
							user.getUserName(),user.getLoginname(), user.getUserDutyName() });
		}
		jTable1.setModel(dTableModel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jButton3 = new javax.swing.JButton();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();
		jButton2 = new javax.swing.JButton();
		jButton1 = new javax.swing.JButton();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

//		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
//				"/tellhow/btnIcon/add.png"))); // NOI18N

		jButton3.setText("新增");
		jButton3.setToolTipText("\u65b0\u589e");
		jButton3.setBorder(null);
		jButton3.setFocusPainted(false);
		//fus 0：管理员
		if(!"管理员".equals(CBSystemConstants.getUser().getUserDutyName())){
			jButton3.setEnabled(false);
		}
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));

		jTable1.setFont(new java.awt.Font("宋体", 0, 13));
		jTable1.setModel(new javax.swing.table.DefaultTableModel(
				new Object[][] { { null, null, null, null },
						{ null, null, null, null }, { null, null, null, null },
						{ null, null, null, null } }, new String[] { "Title 1",
						"Title 2", "Title 3", "Title 4" }));
		jTable1.setRowHeight(26);
		jTable1.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				jTable1MouseClicked(evt);
			}
		});
		jScrollPane1.setViewportView(jTable1);

//		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
//				"/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton2.setText("删除");
		jButton2.setToolTipText("\u5220\u9664");
		jButton2.setBorder(null);
		jButton2.setFocusPainted(false);
		//fus 0：管理员
		if(!"管理员".equals(CBSystemConstants.getUser().getUserDutyName())){
			jButton2.setEnabled(false);
		}
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

//		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
//				"/tellhow/btnIcon/order.gif"))); // NOI18N
		jButton1.setText("修改");
		jButton1.setToolTipText("\u6392\u5e8f");
		jButton1.setBorder(null);
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});
		jButton4 = new JButton();
//		jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource(
//				"/tellhow/btnIcon/add.png"))); // NOI18N
		jButton4.setText("角色配置");
		jButton4.setToolTipText("角色配置");
		jButton4.setBorder(null);
		jButton4.setFocusPainted(false);
		//fus 0：管理员
		if (!CBSystemConstants.getUser().getUserName().contains("管理员")){
			jButton4.setEnabled(false);
		}
		jButton4.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton4ActionPerformed(evt);
			}
		});
		
		JButton jButton5 = new JButton("用户排序");
		jButton5.setToolTipText("用户排序");
		jButton5.setBorder(null);
		jButton5.setFocusPainted(false);
		//fus 0：管理员
		if(!"管理员".equals(CBSystemConstants.getUser().getUserDutyName())){
			jButton5.setEnabled(false);
		}
		jButton5.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton5ActionPerformed(evt);
			}
		});
		
		JButton btnNewButton = new JButton("导入用户");
		btnNewButton.setBorder(null);
		
		btnNewButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				loadOMSUserInfo(evt);
			}
		});
		//fus 0：管理员
		if(!CBSystemConstants.getUser().getUserDutyName().equals("管理员")){
			btnNewButton.setEnabled(false);
		}
		
		GroupLayout layout = new GroupLayout(
						getContentPane());
		layout.setHorizontalGroup(
			layout.createParallelGroup(Alignment.LEADING)
				.addGroup(layout.createSequentialGroup()
					.addGroup(layout.createParallelGroup(Alignment.LEADING)
						.addGroup(layout.createSequentialGroup()
							.addContainerGap()
							.addComponent(jScrollPane1, GroupLayout.DEFAULT_SIZE, 491, Short.MAX_VALUE))
						.addGroup(layout.createSequentialGroup()
							.addGap(59)
							.addComponent(btnNewButton, GroupLayout.PREFERRED_SIZE, 100, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton5, GroupLayout.PREFERRED_SIZE, 57, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.UNRELATED)
							.addComponent(jButton3, GroupLayout.PREFERRED_SIZE, 43, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton1, GroupLayout.PREFERRED_SIZE, 43, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton2, GroupLayout.PREFERRED_SIZE, 47, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.UNRELATED)
							.addComponent(jButton4, GroupLayout.PREFERRED_SIZE, 58, GroupLayout.PREFERRED_SIZE)))
					.addContainerGap())
		);
		layout.setVerticalGroup(
			layout.createParallelGroup(Alignment.LEADING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap()
					.addGroup(layout.createParallelGroup(Alignment.BASELINE)
						.addComponent(jButton4, GroupLayout.PREFERRED_SIZE, 27, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton2, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton1, GroupLayout.PREFERRED_SIZE, 23, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton3, GroupLayout.PREFERRED_SIZE, 24, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton5, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)
						.addComponent(btnNewButton, GroupLayout.PREFERRED_SIZE, 26, GroupLayout.PREFERRED_SIZE))
					.addGap(18)
					.addComponent(jScrollPane1, GroupLayout.DEFAULT_SIZE, 386, Short.MAX_VALUE))
		);
		getContentPane().setLayout(layout);

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//修改
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要修改的记录");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view(this, "只能选择一条记录");
			return;
		}
		CodeNameModel cnm = (CodeNameModel) this.jTable1.getValueAt(selectRows[0], 0);
		String userId = cnm.getCode();
		UpdateUserDialog uom = new UpdateUserDialog(this, true,userId);
		uom.setVisible(true);
		this.initTable();
	}

	//新增
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		AddUserDialog aud = new AddUserDialog(this, true);
		aud.setVisible(true);
		this.initTable();

	}

	//删除
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要删除的记录");
			return;
		}
		int isOk=JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "是否要删除已选择的用户？",SystemConstants.SYSTEM_TITLE, JOptionPane.YES_NO_OPTION);
		if(isOk==JOptionPane.NO_OPTION){
			return;
		}
		jTable1.removeEditor();
		dTableModel = (DefaultTableModel) jTable1.getModel();
		UserDao ud = new UserDao();
		CodeNameModel cnm = null;
		for (int i = selectRows.length - 1; i >= 0; i--) {
			cnm = (CodeNameModel) this.jTable1.getValueAt(selectRows[i], 0);
			ud.delUser(cnm.getCode());
		}
		initTable();
	}
	
	//角色权限赋予
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择用户");
			return;
		}
		if (selectRows.length >= 2) {
			ShowMessage.view(this, "不能同时选择多个用户");
			return;
		}
		dTableModel = (DefaultTableModel) jTable1.getModel();
		UserDao ud = new UserDao();
		CodeNameModel cnm = null;
		String userid="";
		for (int i = selectRows.length - 1; i >= 0; i--) {
			cnm = (CodeNameModel) this.jTable1.getValueAt(selectRows[i], 0);
		    userid = cnm.getCode();
		}
		
		UserRoleDialog aurd = new UserRoleDialog(new JDialog(),true,userid);
		aurd.setVisible(true);
		initTable();
	}
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		UserOrderManager aud = new UserOrderManager(this, true);
		aud.setVisible(true);
		this.initTable();
	}
	//导入OMS用户信息
	private void loadOMSUserInfo(java.awt.event.ActionEvent evt) {
		User user = CBSystemConstants.getUser();
		String sql = "";
		if("2".equals(user.getOrganGrade())){
		   //地调
		   sql = " insert into "+CBSystemConstants.opcardUser+"t_a_userrole\n" +
			     " select distinct t.employeeid,decode(lb,'1','0','2','1')\n" + 
			     " from <EMAIL> t\n" + 
			     " where t.areano='"+CBSystemConstants.getUser().getUnitCode()+"'\n" + 
			     " and not exists (select * from "+CBSystemConstants.opcardUser+"t_a_poweruserinfo t1 where t1.userid=t.employeeid and t1.unitcode='"+CBSystemConstants.getUser().getUnitCode()+"')";
           DBManager.execute(sql);
	       sql = " insert into "+CBSystemConstants.opcardUser+"t_a_poweruserinfo\n" +
				 " select distinct t.employeeid,t.employeename,'c4ca4238a0b923820dcc509a6f75849b','4','"+CBSystemConstants.getUser().getUnitCode()+"','0','1','"+CBSystemConstants.getUser().getUnitCode()+"',t.username\n" + 
				 " from <EMAIL> t\n" + 
				 " where t.areano='"+CBSystemConstants.getUser().getUnitCode()+"'\n" + 
				 " and not exists (select * from "+CBSystemConstants.opcardUser+"t_a_poweruserinfo t1 where t1.userid=t.employeeid and t1.unitcode='"+CBSystemConstants.getUser().getUnitCode()+"')";
	       DBManager.execute(sql);
	       sql = "UPDATE "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO T1 SET T1.LOGINNAME=\n" +
	    				   "(SELECT T.<NAME_EMAIL> T\n" + 
	    				   " WHERE T.AREANO='"+CBSystemConstants.getUser().getUnitCode()+"' AND T1.USERID=T.EMPLOYEEID and rownum=1) WHERE\n" + 
	    				   " EXISTS (SELECT * FROM <EMAIL> T\n" + 
	    				   " WHERE  T1.USERID=T.EMPLOYEEID) AND  T1.UNITCODE='"+CBSystemConstants.getUser().getUnitCode()+"'";
	       DBManager.execute(sql);
		}else{
			   //县调
			   sql = " insert into "+CBSystemConstants.opcardUser+"t_a_userrole\n" +
				     " select distinct t.employeeid,'0'\n" + 
				     " from <EMAIL> t\n" + 
				     " where t.areano='"+CBSystemConstants.getUser().getUnitCode()+"'\n" + 
				     " and not exists (select * from "+CBSystemConstants.opcardUser+"t_a_poweruserinfo t1 where t1.userid=t.employeeid and t1.unitcode='"+CBSystemConstants.getUser().getUnitCode()+"')";
	           DBManager.execute(sql);
		       sql = " insert into "+CBSystemConstants.opcardUser+"t_a_poweruserinfo\n" +
					 " select distinct t.employeeid,t.employeename,'c4ca4238a0b923820dcc509a6f75849b','4','"+CBSystemConstants.getUser().getUnitCode()+"','0','1','"+CBSystemConstants.getUser().getUnitCode()+"',t.username\n" + 
					 " from <EMAIL> t\n" + 
					 " where  t.areano='"+CBSystemConstants.getUser().getUnitCode()+"'\n" + 
					 " and not exists (select * from "+CBSystemConstants.opcardUser+"t_a_poweruserinfo t1 where t1.userid=t.employeeid and t1.unitcode='"+CBSystemConstants.getUser().getUnitCode()+"')";
		       DBManager.execute(sql);
		       sql = "UPDATE "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO T1 SET T1.LOGINNAME=\n" +
    				   "(SELECT T.<NAME_EMAIL> T\n" + 
    				   " WHERE T.AREANO='"+CBSystemConstants.getUser().getUnitCode()+"' AND T1.USERID=T.EMPLOYEEID  and rownum=1) WHERE\n" + 
    				   " EXISTS (SELECT * FROM <EMAIL> T\n" + 
    				   " WHERE  T1.USERID=T.EMPLOYEEID) AND  T1.UNITCODE='"+CBSystemConstants.getUser().getUnitCode()+"'";
               DBManager.execute(sql);
		}
		this.initTable();
		
	}
	
	/**
	 * 双击table中一行数据，弹出修改页面
	 * @param e
	 */
	private void jTable1MouseClicked(MouseEvent e){
		if(e.getClickCount() ==2){
			int[] selectRows = jTable1.getSelectedRows();
			if (selectRows.length == 0) {
				ShowMessage.view(this, "请选择用户");
				return;
			}
			
			CodeNameModel cnm = (CodeNameModel) this.jTable1.getValueAt(selectRows[0], 0);
			String userId = cnm.getCode();
			UpdateUserDialog uom = new UpdateUserDialog(this, true,userId);
			
			//UserOrderManager aud = new UserOrderManager(this, true);
			uom.setVisible(true);
			this.initTable();
		}

	}
	
	
//	public String getuserId(String id) {
//		String sql = "select userId,name from T_A_USERROLE t,T_A_DICTIONARY s where t.rolecode = s.code " +
//				"and s.codetype = 'Role' and t.userid = '"+userId+"'";
//		List results=DBManager.queryForList(sql);
////        Map temp=new HashMap();
////		for (int i = 0; i < results.size(); i++) {
////			temp=(Map)results.get(i);
////			User user=new User();
////			user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
////			user.setUserName(StringUtils.ObjToString(temp.get("Name")));
////		}
//		return results;
//	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				UserManage dialog = new UserManage(new javax.swing.JFrame(),
						true);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JButton jButton4;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTable1;
}
