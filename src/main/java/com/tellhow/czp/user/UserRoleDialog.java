/**
 * 用户角色管理
 */
package com.tellhow.czp.user;

import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.GroupLayout;
import javax.swing.GroupLayout.Alignment;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JDialog;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.LayoutStyle.ComponentPlacement;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumn;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

/**
 * <AUTHOR>
 *
 */
public class UserRoleDialog extends JDialog{
	
	private JButton jButton1 ;
	private JTable jTable1 ;
	private DefaultTableModel dTableModel;
	private JCheckBox chckbxNewCheckBox;
	
	String isUserId = "";

	
	/** Creates new form AddUserDialog */
	public UserRoleDialog(javax.swing.JDialog parent, boolean modal,String userId) {
		super(parent, modal);
		isUserId = userId;
		initComponents();
		this.setTitle("\u89D2\u8272\u7BA1\u7406");
		this.initTable(userId);
		//this.init();
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/**
	 * 初始化页面
	 * 
	 */
	private void initComponents() {
		
		JScrollPane scrollPane = new JScrollPane();
		
		jButton1 = new JButton("\u8BBE\u7F6E");
		jButton1.setToolTipText("\u8BBE\u7F6E");
		jButton1.setBorder(null);
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jTable1 = new javax.swing.JTable();
		jTable1.setFont(new java.awt.Font("宋体", 0, 13));
		jTable1.setModel(new javax.swing.table.DefaultTableModel(
				new Object[][] { { null, null, null, null },
						{ null, null, null, null }, { null, null, null, null },
						{ null, null, null, null } }, new String[] { "Title 1",
						"Title 2", "Title 3", "Title 4" }));
		jTable1.setRowHeight(26);
		scrollPane.setViewportView(jTable1);
		GroupLayout layout = new GroupLayout(getContentPane());
		layout.setHorizontalGroup(
			layout.createParallelGroup(Alignment.TRAILING)
				.addGroup(layout.createSequentialGroup()
					.addGroup(layout.createParallelGroup(Alignment.LEADING)
						.addGroup(layout.createSequentialGroup()
							.addContainerGap()
							.addComponent(scrollPane, GroupLayout.DEFAULT_SIZE, 392, Short.MAX_VALUE))
						.addGroup(layout.createSequentialGroup()
							.addGap(336)
							.addComponent(jButton1, GroupLayout.PREFERRED_SIZE, 52, GroupLayout.PREFERRED_SIZE)))
					.addContainerGap())
		);
		layout.setVerticalGroup(
			layout.createParallelGroup(Alignment.TRAILING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap()
					.addComponent(jButton1, GroupLayout.DEFAULT_SIZE, 27, Short.MAX_VALUE)
					.addPreferredGap(ComponentPlacement.UNRELATED)
					.addComponent(scrollPane, GroupLayout.PREFERRED_SIZE, 229, GroupLayout.PREFERRED_SIZE)
					.addContainerGap())
		);
		getContentPane().setLayout(layout);
		pack();
	}// </editor-fold>
	/**
	 * 设置操作
	 * @param evt
	 */
	private void jButton1ActionPerformed(ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		dTableModel = (DefaultTableModel) jTable1.getModel();
		int length = dTableModel.getRowCount();
		CodeNameModel cnm = null;
		deleteUserRole(isUserId);
		for (int i = length-1; i >= 0; i--) {
			String id = (String)dTableModel.getValueAt(i, 0);
			String code = (String) this.dTableModel.getValueAt(i, 4);
			String flag = this.dTableModel.getValueAt(i, 3).toString();
			if("true".equals(flag)) {
				insertUserRoles(id,code);
			}
			//String id = (String) this.jTable1.getValueAt(selectRows[i], 0);
			//dTableModel.getColumnName(column)
			//String code = (String) this.jTable1.getValueAt(selectRows[i], 4);
			//insertUserRoles(id,code);
		}
		ShowMessage.view("设置成功！");

	}
	
	/**
	 * 初始化表格
	 */
	public void initTable(String userId) {
		DefaultTableModel dtm1 = new DefaultTableModel(null, new String[] { "用户id","序号","角色名称","选择","code"}) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return true;
			}
		};
		Object[] tableHeads1=new String[]{"用户id","序号", "名称","选择","code"};
		dtm1.setColumnIdentifiers(tableHeads1);
		jTable1.setModel(dtm1);
		List list = this.getAllRoleName();
		Map temp=new HashMap();
		Map temp2=new HashMap();
		
		for (int i = 0; i < list.size(); i++) {
			boolean flag = false;
			temp = (Map)list.get(i);
			String name = StringUtils.ObjToString(temp.get("Name"));
			String code = StringUtils.ObjToString(temp.get("code"));
			List list2 = getAllRole(userId);
			for (int j = 0; j < list2.size(); j++) {
				temp2 =  (Map)list2.get(j);
				String checkname = StringUtils.ObjToString(temp2.get("Name"));
				if(name.equals(checkname)) {
					flag = true; 
					break;
				}
			}
			String roleCode = "";
			Vector v = new Vector();
			v.add(userId);
			v.add(i+1);
			v.add(name);
			v.add(flag);
			v.add(code);
			dtm1.addRow(v);
			
			jTable1.setModel(dtm1);
	 		TableColumn tc = jTable1.getColumnModel().getColumn(0);  
			tc.setMaxWidth(0);
			tc.setPreferredWidth(0);
			tc.setWidth(0);
			tc.setMinWidth(0);
			
	 		TableColumn tc2 = jTable1.getColumnModel().getColumn(4);  
	 		tc2.setMaxWidth(0);
	 		tc2.setPreferredWidth(0);
	 		tc2.setWidth(0);
	 		tc2.setMinWidth(0);
	 		TableColumn aColumn = jTable1.getColumnModel().getColumn(3);  
			//jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
			aColumn.setCellEditor(jTable1.getDefaultEditor(Boolean.class));   
			aColumn.setCellRenderer(jTable1.getDefaultRenderer(Boolean.class));

		}

	}
	
	/**
	 * 查询所有角色
	 * @param userId
	 * @return
	 */
	public List getAllRole(String userId) {
		String sql = "select userId,name,code from "+CBSystemConstants.opcardUser+"T_A_USERROLE t,"+CBSystemConstants.opcardUser+"T_A_DICTIONARY s where t.rolecode = s.code " +
				"and s.codetype = 'Role' and t.userid = '"+userId+"'";
		List results=DBManager.queryForList(sql);
		return results;
	}
	
	/**
	 * 查询用户已在管理表中的权限
	 * @return
	 */
	public List getRoleName() {
		String sql = "select distinct name from "+CBSystemConstants.opcardUser+"T_A_USERROLE t,"+CBSystemConstants.opcardUser+"T_A_DICTIONARY s where t.rolecode = s.code and s.codetype = 'Role'";
		List results=DBManager.queryForList(sql);
		return results;
	}
	
	/**
	 * 查询所有角色的对应的业务名称
	 * @return
	 */
	public List getAllRoleName() {
		String sql = "select name,code from "+CBSystemConstants.opcardUser+"T_A_DICTIONARY t where t.codetype = 'Role' and t.unitcode = 'system'";
		List results=DBManager.queryForList(sql);
		return results;
	}
	
	/**
	 * 删除所有用户的角色
	 * @param userId
	 */
	public void deleteUserRole(String userId) {
		String sql = "delete from "+CBSystemConstants.opcardUser+"T_A_USERROLE  t where t.userId = '"+userId+"'";
		DBManager.execute(sql);
	}
	/**
	 * 插入用户的角色
	 * @param userId
	 * @param code
	 */
	public void insertUserRoles(String userId, String code){
		String sql = "insert into "+CBSystemConstants.opcardUser+"T_A_USERROLE(USERID,ROLECODE) values ('"+userId+"',"+"'"+code+"')";
		DBManager.execute(sql);
	}
	
	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		UserRoleDialog jDialog = new UserRoleDialog(new javax.swing.JDialog(), true, "cfd51331-3a71-48e0-b86f-0cb3fd2edda4");
		jDialog.addWindowListener(new java.awt.event.WindowAdapter() {
			public void windowClosing(java.awt.event.WindowEvent e) {
				System.exit(0);
			}
		});
		jDialog.setVisible(true);
	}
}
