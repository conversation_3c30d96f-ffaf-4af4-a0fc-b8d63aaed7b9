package com.tellhow.czp.user;


import javax.swing.JDialog;

import com.tellhow.czp.app.CZPImpl;


@SuppressWarnings("serial")
public abstract class UserLoginInter extends JDialog{
	
	protected static UserLoginInter userlogininter;
	
	public static UserLoginInter getuserlogininter(){
		return userlogininter;
	}
	public static UserLoginInter getInstance() {
		if(userlogininter==null){
			
			userlogininter=(UserLoginInter)CZPImpl.getInstance("UserLogin");
			if(userlogininter == null)
				userlogininter = new UserLoginDefault();
		}
			
		return userlogininter;
	}
	
	public static void setuserlogininter(Class clazz) {
		if (UserLoginInter.class.isAssignableFrom(clazz)) {
			try {
				userlogininter = (UserLoginInter)clazz.newInstance();
			} catch (InstantiationException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} catch (IllegalAccessException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		} else {
			String errMsg = clazz.getName()
					+ " is not a subclass of UserLoginInter.";
			throw new RuntimeException(errMsg);
		}
	}
	
	public abstract void init(String title);
}
