/*
 * AddUserDialog.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.user;

import java.awt.Toolkit;

import javax.swing.DefaultComboBoxModel;

import czprule.model.CodeNameModel;
import czprule.system.ShowMessage;
import javax.swing.GroupLayout;
import javax.swing.GroupLayout.Alignment;
import javax.swing.LayoutStyle.ComponentPlacement;

import org.apache.bsf.util.event.adapters.java_awt_event_ActionAdapter;

/**
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class UpdateUserDialog extends javax.swing.JDialog {

	/** Creates new form AddUserDialog */
	public UpdateUserDialog(javax.swing.JDialog parent, boolean modal,String userId) {
		super(parent, modal);
		initComponents(userId);
		userIdALL = userId;
		this.setTitle("用户管理-修改用户");
		this.init();
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	public void init() {
		DefaultComboBoxModel comboxModel = new DefaultComboBoxModel();
		comboxModel.addElement(new CodeNameModel("0", "管理员"));
		comboxModel.addElement(new CodeNameModel("1", "调度员"));
		comboxModel.addElement(new CodeNameModel("2", "副职调度长"));
		comboxModel.addElement(new CodeNameModel("3", "正职调度长"));
		comboxModel.addElement(new CodeNameModel("4", "普通用户"));
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents(String userId) {
		
		UserDao ud = new UserDao();
		String userName = ud.getUserByID(userId).getUserName();
		String loginName = ud.getUserByID(userId).getLoginname();
		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jLabel1 = new javax.swing.JLabel();
		jLabel6 = new javax.swing.JLabel();
		jTextField1 = new javax.swing.JTextField();
		jTextField2 = new javax.swing.JTextField();
		jLabel5 = new javax.swing.JLabel();
		jSeparator1 = new javax.swing.JSeparator();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/save.png"))); // NOI18N
		jButton1.setToolTipText("\u4fdd\u5b58");
		jButton1.setBorder(null);
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png"))); // NOI18N
		jButton2.setToolTipText("\u53d6\u6d88");
		jButton2.setBorder(null);
		jButton2.setFocusPainted(false);
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jLabel1.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel1.setText("\u7528 \u6237 \u540d\uff1a");
		
		jLabel6.setFont(new java.awt.Font("宋体", 0, 14));
		jLabel6.setText("\u767b\u0020\u9646\u0020\u540d\uff1a");

		jTextField1.setFont(new java.awt.Font("宋体", 0, 14));
		jTextField1.setText(userName);
		
		jTextField2.setFont(new java.awt.Font("宋体", 0, 14));
		jTextField2.setText(loginName);
		jLabel5.setFont(new java.awt.Font("微软雅黑", 1, 14));
		jLabel5.setText("\u4FEE\u6539\u7528\u6237\u4FE1\u606F\uFF1A");

		GroupLayout layout = new GroupLayout(
						getContentPane());
		layout.setHorizontalGroup(
			layout.createParallelGroup(Alignment.TRAILING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap()
					.addComponent(jLabel5, GroupLayout.PREFERRED_SIZE, 138, GroupLayout.PREFERRED_SIZE)
					.addPreferredGap(ComponentPlacement.RELATED, 204, Short.MAX_VALUE)
					.addComponent(jButton1)
					.addGap(18)
					.addComponent(jButton2)
					.addContainerGap())
				.addGroup(layout.createSequentialGroup()
					.addGap(48)
					.addGroup(layout.createParallelGroup(Alignment.TRAILING, false)
						.addComponent(jLabel1, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
						.addComponent(jLabel6, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
					.addPreferredGap(ComponentPlacement.RELATED)
					.addGroup(layout.createParallelGroup(Alignment.LEADING)
						.addComponent(jTextField1, GroupLayout.DEFAULT_SIZE, 242, Short.MAX_VALUE)
						.addComponent(jTextField2, GroupLayout.DEFAULT_SIZE, 242, Short.MAX_VALUE))
					.addGap(48))
				.addComponent(jSeparator1, GroupLayout.DEFAULT_SIZE, 412, Short.MAX_VALUE)
		);
		layout.setVerticalGroup(
			layout.createParallelGroup(Alignment.TRAILING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap()
					.addGroup(layout.createParallelGroup(Alignment.TRAILING)
						.addComponent(jLabel5, GroupLayout.PREFERRED_SIZE, 26, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton1)
						.addComponent(jButton2))
					.addPreferredGap(ComponentPlacement.RELATED)
					.addComponent(jSeparator1, GroupLayout.PREFERRED_SIZE, 10, GroupLayout.PREFERRED_SIZE)
					.addPreferredGap(ComponentPlacement.RELATED, 33, Short.MAX_VALUE)
					.addGroup(layout.createParallelGroup(Alignment.BASELINE)
						.addComponent(jTextField1, GroupLayout.PREFERRED_SIZE, 33, GroupLayout.PREFERRED_SIZE)
						.addComponent(jLabel1))
					.addGap(18)
					.addGroup(layout.createParallelGroup(Alignment.BASELINE)
						.addComponent(jTextField2, GroupLayout.PREFERRED_SIZE, 33, GroupLayout.PREFERRED_SIZE)
						.addComponent(jLabel6))
					.addGap(190))
		);
		getContentPane().setLayout(layout);

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//保存
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if ("".equals(this.jTextField1.getText().trim())) {
			ShowMessage.view(this, "用户名不能为空!");
			return;
		}
		if ("".equals(this.jTextField2.getText().trim())) {
			ShowMessage.view(this, "登录名不能为空!");
			return;
		}
		User user = new User();
		user.setUserID(userIdALL);
		user.setUserName(this.jTextField1.getText().trim());
		user.setLoginname(this.jTextField2.getText().trim());
		UserDao ud = new UserDao();
		ud.updateUserName(user);
		
		this.setVisible(false);
		this.dispose();
	}

	//取消
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.setVisible(false);
		this.dispose();
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel5;
	private javax.swing.JLabel jLabel6;//登录名
	private javax.swing.JSeparator jSeparator1;
	private javax.swing.JTextField jTextField1;
	private javax.swing.JTextField jTextField2;
	private String userIdALL;
	// End of variables declaration//GEN-END:variables

}
