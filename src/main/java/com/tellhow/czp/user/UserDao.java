package com.tellhow.czp.user;

import java.text.SimpleDateFormat;
import java.util.*;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.sysconfig.AuthManagerDialog;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.DOMUtil;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;


public class UserDao {
	private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	//获取所有用户信息
	public List<User> getAllUserLogin(){
        List<User> allUsers=new ArrayList<User>();
        String sql="SELECT a.USERID,a.USERNAME,a.USERPASSWORD,DECODE(a.USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTYNAME,USERDUTY,a.ORGANID,a.unitcode FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO a WHERE ISDEL=0 ";
        String organ=getSYS_CONFIG("organ");
        if(!organ.equals("")){
        	sql += " and  organid = '"+organ+"' ";
        }
        
        sql+= " ORDER BY ORDERNUM";
		List results=DBManager.queryForList(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			User user=new User();
			user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
			user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
			user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
			user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
			user.setUnitCode(StringUtils.ObjToString(temp.get("UNITCODE")));
			user.setOrganID(StringUtils.ObjToString(temp.get("ORGANID")));
			allUsers.add(user);
		}
		return allUsers;		
	}
	
	public String getSYS_CONFIG(String para){
		String ret = "";
		Document doc=null;
		try {
			 doc = DOMUtil.readXMLFile(CBSystemConstants.SYS_CONFIG_XML_FILE);
		} catch (Exception e) {
			//log.error(e.getMessage(), e);
			return "";
		}

        Element rootE = doc.getDocumentElement();

        NodeList childEs = rootE.getChildNodes();
        Element childE = null;
        Element values = null;
        for (int i = 0; i < childEs.getLength(); i++) {
        	if(childEs.item(i).getNodeName().equals("#text"))
        		continue;
            childE = (Element) childEs.item(i);
            if (childE.getAttribute("name").toUpperCase().equals("PROJECTPARAM")) { 
            	 Element codeElem = null;
                 String codeKey = "";
                 String codeValue="";
                 NodeList allVlaues = childE.getElementsByTagName("value");
                 for (int j = 0; j < allVlaues.getLength(); j++) {
                     codeElem = (Element) allVlaues.item(j);
                     codeKey = codeElem.getAttribute("key").trim();
                     codeValue=codeElem.getTextContent().trim();
                     if(para.equals(codeKey)){
                    	 ret=codeValue;
                     }
                 }
            }
        }
        return ret;
	}
	
	/**
	 * @return 所有用户信息
	 */
	public List<User> getAllUser(){
        List<User> allUsers=new ArrayList<User>();
        String sql="SELECT USERID,USERNAME,USERPASSWORD,DECODE(USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTYNAME,USERDUTY,loginname,ORGANID FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO WHERE ISDEL=0 and unitcode='"+CBSystemConstants.getUser().getUnitCode()+"' ORDER BY ORDERNUM";
		List results=DBManager.queryForList(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			User user=new User();
			user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
			user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
			user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
			user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
			user.setLoginname(StringUtils.ObjToString(temp.get("LOGINNAME")));
			user.setUnitCode(CBSystemConstants.unitCode);
			user.setOrganID(StringUtils.ObjToString(temp.get("ORGANID")));
			allUsers.add(user);
		}
		return allUsers;		
	}
	
	/**
	 * 根据区域过滤用户
	 * @param 用户区域编码
	 */
	public List<User> getAllUser(String unitcode){
		 List<User> allUsers=new ArrayList<User>();
		 String sql="SELECT USERID,USERNAME,USERPASSWORD,DECODE(USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTYNAME,USERDUTY,ORGANID,LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO WHERE ISDEL=0 and UNITCODE ='"+unitcode+"' ORDER BY ORDERNUM";
			List results=DBManager.queryForList(sql);
	        Map temp=new HashMap();
			for (int i = 0; i < results.size(); i++) {
				temp=(Map)results.get(i);
				User user=new User();
				user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
				user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
				user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
				user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
				user.setUnitCode(CBSystemConstants.unitCode);
				user.setOrganID(StringUtils.ObjToString(temp.get("ORGANID")));
				user.setLoginname(StringUtils.ObjToString(temp.get("LOGINNAME")));
				allUsers.add(user);
			}
			return allUsers;
	}
	
	/**
	 * 根据区域过滤用户
	 * @param 用户区域编码
	 * @param 用户角色
	 */
	public List<User> getAllUser(String unitcode,String role){
		  List<User> allUsers=new ArrayList<User>();
		 String sql="SELECT a.USERID,a.USERNAME,a.USERPASSWORD,DECODE(a.USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTYNAME,USERDUTY,a.ORGANID ,b.rolecode FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO a ,"+CBSystemConstants.opcardUser+"T_a_userrole b WHERE a.ISDEL=0 and a.UNITCODE ='"+unitcode+"' and b.rolecode='"+role+"' and a.userid=b.userid ORDER BY ORDERNUM";
			List results=DBManager.queryForList(sql);
	        Map temp=new HashMap();
			for (int i = 0; i < results.size(); i++) {
				temp=(Map)results.get(i);
				User user=new User();
				user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
				user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
				user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
				user.setUserDutyName(StringUtils.ObjToString(temp.get("USERDUTYNAME")));
				user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
				user.setUnitCode(StringUtils.ObjToString(temp.get("UNITCODE")).equals("")?unitcode:StringUtils.ObjToString(temp.get("UNITCODE")));
				user.setOrganID(StringUtils.ObjToString(temp.get("ORGANID")));
				allUsers.add(user);
			}
			return allUsers;
	}
	/**
	 * @param userID 用户ID
	 * @return 用户ID对应的用户信息
	 */
	public User getUserByID(String userID){
        User user=new User();
        String sql="SELECT USERID,USERNAME,USERPASSWORD,DECODE(USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTYNAME,USERDUTY,LOGINNAME,UNITCODE FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO WHERE USERID='"+userID+"'";
		List results=DBManager.queryForList(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
			user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
			user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
			user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
			user.setLoginname(StringUtils.ObjToString(temp.get("LOGINNAME")));
			user.setUnitCode(StringUtils.ObjToString(temp.get("UNITCODE")));
		}
		return user;		
	}
	/**
	 * @param userID 用户ID
	 * @return 用户ID对应的用户信息和所属机构
	 */
	public User getUserInfoByID(String userID){
        User user=new User();
        String sql="SELECT USERID,USERNAME,USERPASSWORD,DECODE(USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTYNAME,USERDUTY,LOGINNAME,a.unitcode,a.organid FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO a left join "+CBSystemConstants.platformUser+"T_tbp_organ b on a.organid=b.organid WHERE a.USERID='"+userID+"'";
		List results=DBManager.queryForList(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
			user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
			user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
			user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
			user.setLoginname(StringUtils.ObjToString(temp.get("LOGINNAME")));
			user.setUnitCode(StringUtils.ObjToString(temp.get("unitcode")));
			user.setOrganID(StringUtils.ObjToString(temp.get("organid")));
		}
		return user;		
	}
	/**
	 * 描述：添加用户
	 * @param user 用户信息
	 */
	public boolean addUser(User user){
		String selectuser="select userid from "+CBSystemConstants.opcardUser+"T_a_poweruserinfo where loginname='"+user.getLoginname()+"'";
		List users=DBManager.queryForList(selectuser);
		if(users.size()>0){
			ShowMessage.view("已经存在该登录名！");
			return true;
		}else{
			//OMS默认更新账户至操作票unitcode字段暂时为空，其他项目代码不影响。
			String sql="INSERT INTO  "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO(USERID,USERNAME,USERPASSWORD,USERDUTY,UNITCODE,organid,ORDERNUM,LOGINNAME) values('"+user.getUserID()+"','"+user.getUserName()+"','"+user.getPassword()+"','"+user.getUserDuty()+"','"+CBSystemConstants.unitCode+"','"+CBSystemConstants.getUser().getOrganID()+"','','"+user.getLoginname()+"')";
		    DBManager.execute(sql);
		    sql="INSERT INTO  "+CBSystemConstants.opcardUser+"T_A_USERROLE(USERID,ROLECODE) values('"+user.getUserID()+"','"+CBSystemConstants.roleCode+"')";//（原ROLECODE 存入为固定值4）rolecode为当前rolecode
		    DBManager.execute(sql);
		    return true;
		}	    
	}
	
	/**
	 * 描述：添加用户角色
	 * @param user 用户信息
	 */
	public boolean addRole(User user,String role){
		String sql="select count(*) from "+CBSystemConstants.opcardUser+"T_A_USERROLE where USERID='"+user.getUserID()+"' and ROLECODE='"+role+"'";
		int count=DBManager.queryForInt(sql);
		if(count == 0){
		    sql="INSERT INTO  "+CBSystemConstants.opcardUser+"T_A_USERROLE(USERID,ROLECODE) values('"+user.getUserID()+"','"+role+"')";
		    DBManager.execute(sql);
		}
		return true;
	}
	
	/**
	 * 描述：添加用户与角色
	 * @param user 用户信息
	 */
	public boolean addUserAndRole(User user,String role){
		String selectuser="select userid from "+CBSystemConstants.opcardUser+"T_a_poweruserinfo where loginname='"+user.getLoginname()+"'";
		List users=DBManager.queryForList(selectuser);
		if(users.size()>0){
			ShowMessage.view("已经存在该登录名!");
			return false;
		}else{
			String sql="INSERT INTO  "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO(USERID,USERNAME,USERPASSWORD,USERDUTY,UNITCODE,ORDERNUM,LOGINNAME,ISDEL) values('"+user.getUserID()+"','"+user.getUserName()+"','"+user.getPassword()+"','"+user.getUserDuty()+"','"+CBSystemConstants.unitCode+"','','"+user.getLoginname()+"','0')";
		    DBManager.execute(sql);
		    sql="INSERT INTO  "+CBSystemConstants.opcardUser+"T_A_USERROLE(USERID,ROLECODE) values('"+user.getUserID()+"','"+role+"')";
		    DBManager.execute(sql);
		    return true;
		}
	    
	}
	
	/**
	 * 描述：添加用户与角色
	 * @param user 用户信息
	 */
	public boolean addUserAndRoleAndOrgan(User user,String role){
		String selectuser="select userid from "+CBSystemConstants.opcardUser+"T_a_poweruserinfo where unitcode='"+CBSystemConstants.unitCode+"' and loginname='"+user.getLoginname()+"'";
		List users=DBManager.queryForList(selectuser);
		if(users.size()>0){
			ShowMessage.view("已经存在该登录名!");
			return false;
		}else{
			String sql="INSERT INTO  "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO(USERID,USERNAME,USERPASSWORD,USERDUTY,UNITCODE,organid,ORDERNUM,LOGINNAME) values('"+user.getUserID()+"','"+user.getUserName()+"','"+user.getPassword()+"','"+user.getUserDuty()+"','"+CBSystemConstants.unitCode+"','"+user.getOrganID()+"','','"+user.getLoginname()+"')";
		    DBManager.execute(sql);
		    sql="INSERT INTO  "+CBSystemConstants.opcardUser+"T_A_USERROLE(USERID,ROLECODE) values('"+user.getUserID()+"','"+role+"')";
		    DBManager.execute(sql);
		    return true;
		}
	    
	}
	
	/**
	 * 描述：添加用户
	 * @param user 用户信息
	 */
	public void delUser(String userID){
//		String sql="UPDATE "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO SET ISDEL = 1 WHERE USERID='"+userID+"'";  tanfei 2015-01-19 表中删除用户数据
		String sql="delete from  "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO WHERE USERID='"+userID+"'";

	    DBManager.execute(sql);
	}
	/**
	 * 描述：用户排序
	 * @param userids 用户ID集合
	 */
	public void updateUserOrder(List<String> userids){
		String sql="";
		for (int i = 0; i < userids.size(); i++) {
			sql="UPDATE "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO SET ORDERNUM="+(i+1)+" WHERE USERID='"+userids.get(i)+"'";
		    DBManager.execute(sql);
		}
	}
	/**
	 * 描述：修改用户密码
	 * @param user用户对象
	 */
	public void updatePassword(User user){
		String sql="UPDATE "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO set userpassword='"+user.getPassword()+"' WHERE userId='"+user.getUserID()+"'";
		    DBManager.execute(sql);
	}	
	
	/**
	 * 描述：修改用户名称和登陆名称
	 * @param user用户对象
	 */
	public void updateUserName(User user){
		String sql="UPDATE "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO set USERNAME='"+user.getUserName()+"', LOGINNAME='"+user.getLoginname()+"' WHERE userId='"+user.getUserID()+"'";
		DBManager.execute(sql);
	}	
	//根据登录名查询
	public User getUserByLoginName(String name){
		User user=new User();
		String sql="SELECT t.USERID,t.LOGINNAME,t.USERNAME,t.USERPASSWORD,t.ORGANID,t.USERDUTY, t.UNITCODE,r.ROLECODE as Roleid,t2.organgrade FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO t,"+CBSystemConstants.opcardUser+"T_A_USERROLE r,"+CBSystemConstants.platformUser+"T_tbp_zone t2 WHERE  (t.LOGINNAME='"+name+"' or t.username='"+name+"') and t.userid=r.userid and t.UNITCODE=t2.zone_no and rownum=1";
		List results=DBManager.queryForList(sql);
		
		if(results.size()>0){
			user = new User();
			for(int i=0;i<results.size();i++){
			Map temp=(Map)results.get(i);
			user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
			user.setLoginname(StringUtils.ObjToString(temp.get("LOGINNAME")));
			user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
			user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
			user.setOrganID(StringUtils.ObjToString(temp.get("ORGANID")));
			user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
			user.setUnitCode(StringUtils.ObjToString(temp.get("UNITCODE")));
			user.setOrganGrade(StringUtils.ObjToString(temp.get("organgrade")));
			if(StringUtils.ObjToString(temp.get("Roleid")).equals(0)){
				user.setZW(true);
			}else if(StringUtils.ObjToString(temp.get("Roleid")).equals(1)){
				user.setPW(true);
			}
			}
			
			
		}
		return user;	
	}
	
	//根据用户名查询
	public User getUserByName(String name){
		User user=new User();
		String sql="SELECT t.USERID,t.USERNAME,t.USERPASSWORD,t.ORGANID,t.USERDUTY, t.UNITCODE,r.ROLECODE as Roleid FROM "+CBSystemConstants.opcardUser+"t_a_POWERUSERINFO t,"+CBSystemConstants.opcardUser+"T_A_USERROLE r WHERE  t.USERNAME='"+name+"' and t.userid=r.userid and rownum=1";
		List results=DBManager.queryForList(sql);
		
		if(results.size()>0){
			user = new User();
			for(int i=0;i<results.size();i++){
			Map temp=(Map)results.get(i);
			user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
			user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
			user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
			user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
			user.setUnitCode(StringUtils.ObjToString(temp.get("UNITCODE")));
			user.setOrganID(StringUtils.ObjToString(temp.get("ORGANID")));
			if(StringUtils.ObjToString(temp.get("Roleid")).equals(0)){
				user.setZW(true);
			}else if(StringUtils.ObjToString(temp.get("Roleid")).equals(1)){
				user.setPW(true);
			}
			}
			
			
		}
		return user;	
	}
	
	//根据用户名和区域编码查询
		public User getUserByNameAndUnitCode(String name,String unitcode){
			User user=new User();
			String sql="SELECT t.USERID,t.USERNAME,t.USERPASSWORD,t.ORGANID,t.USERDUTY, t.UNITCODE,r.ROLECODE as Roleid FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO t left join "+CBSystemConstants.opcardUser+"T_A_USERROLE r on t.userid=r.userid WHERE t.USERNAME='"+name+"' and t.unitcode='" + unitcode + "' and rownum=1";
			List results=DBManager.queryForList(sql);
			
			if(results.size()>0){
				user = new User();
				for(int i=0;i<results.size();i++){
				Map temp=(Map)results.get(i);
				user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
				user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
				user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
				user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
				user.setUnitCode(StringUtils.ObjToString(temp.get("UNITCODE")));
				user.setOrganID(StringUtils.ObjToString(temp.get("ORGANID")));
				if(StringUtils.ObjToString(temp.get("Roleid")).equals(0)){
					user.setZW(true);
				}else if(StringUtils.ObjToString(temp.get("Roleid")).equals(1)){
					user.setPW(true);
				}
				}
				
				
			}
			return user;	
		}
	
	/**
	 * 根据用户ID查询但不考虑 unitCode
	 * @param userID
	 * @return
	 */
	public User getUserById(String userID){
        User user=new User();
        String sql="SELECT USERID,USERNAME,USERPASSWORD,DECODE(USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTYNAME,USERDUTY,LOGINNAME,UNITCODE FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO WHERE USERID='"+userID+"'";
		List results=DBManager.queryForList(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
			user.setUserName(StringUtils.ObjToString(temp.get("USERNAME")));
			user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
			user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
			user.setLoginname(StringUtils.ObjToString(temp.get("LOGINNAME")));
			user.setUnitCode(StringUtils.ObjToString(temp.get("UNITCODE")));
		}
		return user;		
	}
	
	public User getOMSUserByName(String name) { 
		User user=new User();
		String sql="SELECT USERID,USERNAME,USERALIAS,PASSWORD as USERPASSWORD,'4' as USERDUTY FROM "+CBSystemConstants.platformUser+"T_TBP_USER WHERE USERNAME='"+name+"' and rownum=1";		
		List results=DBManager.queryForList(sql);
		if(results.size()>0){
			Map temp=(Map)results.get(0);
			user = new User();
			user.setUserID(StringUtils.ObjToString(temp.get("USERID")));
			user.setLoginname(StringUtils.ObjToString(temp.get("USERNAME")));
			user.setUserName(StringUtils.ObjToString(temp.get("USERALIAS")));
			user.setPassword(StringUtils.ObjToString(temp.get("USERPASSWORD")));
			user.setUserDuty(StringUtils.ObjToString(temp.get("USERDUTY")));
		}
		return user;	
	}
	
	public User addUserByName(String name){
		User user = new User();
		user.setUserName(name);
		user.setUserID(StringUtils.getUUID());
		user.setPassword(tbp.common.util.StringUtil.getMD5("1"));
		user.setUserDuty("1");
		user.setUnitCode(CBSystemConstants.unitCode);
		addUser(user);
		String code = AuthManagerDialog.backusercode(user);
		return user;
	}
	public User addUserByNameRole(String name,String role){
		User user = new User();
		user.setUserName(name);
		String userid=StringUtils.getUUID();
		if(CBSystemConstants.userid.equals("")){
			user.setUserID(userid);
		}else{
			user.setUserID(CBSystemConstants.userid);
		}
		
		user.setPassword(tbp.common.util.StringUtil.getMD5("1"));
		user.setUserDuty("1");
		user.setUnitCode(CBSystemConstants.unitCode);
		user.setOrganID("");
		user.setLoginname(name);
		addUserAndRole(user,role);
		String code = AuthManagerDialog.backusercode(user);
		CBSystemConstants.roleCode=role;
		return user;
	}
	
	public User addUserByNameRole(User tempUser,String role){
		String userid=StringUtils.getUUID();
		tempUser.setUserID(userid);
		tempUser.setPassword(tbp.common.util.StringUtil.getMD5("1"));
		addUserAndRoleAndOrgan(tempUser,role);
		String code = AuthManagerDialog.backusercode(tempUser);
		CBSystemConstants.roleCode=role;
		return tempUser;
	}
	
	
	//加载用户爱好设置
	public void LoadUserLike(User user){
		//初始版修改配置
	    String userid=user.getUserID();
	    String sql = "";
	    //用户数据
	    sql="select * from "+CBSystemConstants.opcardUser+"T_a_userlike where userid='"+userid+"'";
	    List result=DBManager.queryForList(sql);
	    //所有爱好数据
	    String updatesql="select * from "+CBSystemConstants.opcardUser+"T_a_userlikekey";
    	List updateresult=DBManager.queryForList(updatesql);
    	if(result.size()!=updateresult.size()){
	    	for(int i=0;i<updateresult.size();i++){
	    		Map updatemap=(Map)updateresult.get(i);
	    		String likeid=StringUtils.ObjToString(updatemap.get("likeid"));
	    		String likevalue=StringUtils.ObjToString(updatemap.get("likevalue"));
	    		boolean nothere=false;
	    		int num=0;
	    		for(int j=0;j<result.size();j++){
	    			Map map=(Map)result.get(j);
	    			String likekey=StringUtils.ObjToString(map.get("likekey"));
	    			if(likekey.equals(likeid)){
	    				nothere=true;
	    			}
	    		}
	    		if(nothere==false&&(userid!=null)){
	    			String lastsql="insert into "+CBSystemConstants.opcardUser+"T_a_userlike (id,userid,likekey,userlike) values ('"+StringUtils.getUUID()+"','"+userid+"','"+likeid+"',"+likevalue+")";
		    		DBManager.execute(lastsql);
	    		}
	    	}
	    }
    	/**
    	 * 先还原为默认喜好再加载
    	 */
    	SystemConstants.isInitDoubleScreen="0";
    	SystemConstants.isInitEMSStatus="0";
    	SystemConstants.isInitfigureright="0";
    	SystemConstants.isInitNewWin="0";
    	SystemConstants.isSupervisory="0";
    	SystemConstants.refreshrate="0";
    	CBSystemConstants.isHrefOnLine=false;
    	CBSystemConstants.isAutoLoadSVGFile="1";
	    for(int i=0;i<result.size();i++){
    		Map map=(Map)result.get(i);
    		String likekey=StringUtils.ObjToString(map.get("likekey"));
    		String like=StringUtils.ObjToString(map.get("USERLIKE"));
    	
    		if(likekey.equals("isInitDoubleScreen")){
    			SystemConstants.isInitDoubleScreen=like;
    		}else if(likekey.equals("isInitEMSStatus")){
    			SystemConstants.isInitEMSStatus=like;
    			if(SystemConstants.isInitEMSStatus.equals("0"))
    				CBSystemConstants.cardstatus = "0";
    		}else if(likekey.equals("isInitNewWin")){
    			SystemConstants.isInitNewWin=like;
    		}else if(likekey.equals("isAutoLoadSVGFile")){
    			CBSystemConstants.isAutoLoadSVGFile=like;
    		}else if(likekey.equals("isInitfigureright")){
    			SystemConstants.isInitfigureright=like;
    		}else if(likekey.equals("refreshrate")){
    			SystemConstants.refreshrate=like;
    		}else if(likekey.equals("isHrefOnLine")){
    			CBSystemConstants.isHrefOnLine=like.equals("1");
    		}else if(likekey.equals("isSupervisory")){
    			SystemConstants.isSupervisory = like;
    		}
    	}
	    //
	    
	    sql="select a.unitcode,b.rolecode,c.opcode from "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO a,"+CBSystemConstants.opcardUser+"T_A_USERROLE b,"+CBSystemConstants.opcardUser+"T_A_OPCODEINFO c where a.userid=b.userid and a.unitcode=c.areano and b.rolecode=c.rolecode and a.userid='"+userid+"' order by b.rolecode";
	    try{
			result=DBManager.queryForList(sql);
		    if(result.size() > 0) {
		    	if(CBSystemConstants.unitCode.equals(""))
		    		CBSystemConstants.unitCode = ((Map)result.get(0)).get("unitcode").toString();
		    	if(CBSystemConstants.roleCode.equals(""))
		    		CBSystemConstants.roleCode = ((Map)result.get(0)).get("rolecode").toString();
		    	
//		    	//设置设备状态类型(0调度拟票态 2监控拟票态)
//			    if(CBSystemConstants.roleCode.equals("0"))
//		    		CBSystemConstants.cardstatus = "0";
//		    	else if(CBSystemConstants.roleCode.equals("2"))
//		    		CBSystemConstants.cardstatus = "2";
//			    if(SystemConstants.isInitEMSStatus.equals("1"))
//			    	CBSystemConstants.cardstatus = "1";
		    }
	   }
	   catch(Exception ex) {
		   
	   }
	    
	    CBSystemConstants.opCode = QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode);
	    if(CBSystemConstants.opCode.equals(""))
	    	CBSystemConstants.opCode = QueryDeviceDao.getOpcode("0", CBSystemConstants.roleCode);
		int count = DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"T_a_devicestateinfo where islock=0 and opcode='"+CBSystemConstants.opCode+"'");
		if(count == 0) {
			List<Map> list = DBManager.queryForList("select areano from  "+CBSystemConstants.opcardUser+"T_A_OPCODEINFO where opcode='0' and rolecode='0'");
			if(list.size() > 0)
				CBSystemConstants.opRuleCode = QueryDeviceDao.getOpcode(list.get(0).get("areano").toString(), CBSystemConstants.roleCode);
		}
		else
			 CBSystemConstants.opRuleCode = CBSystemConstants.opCode;
	}

	/**
	 * 检查用户是否被锁定
	 * @param userId 用户ID
	 * @return 如果被锁定返回解锁时间，否则返回null
	 */
	public java.util.Date checkUserLocked(String userId) {
	    String sql = "SELECT time FROM " + CBSystemConstants.opcardUser + "t_a_parameter WHERE type='login' AND value=? AND time > SYSDATE";
	    List<Map> result = DBManager.queryForList(sql, userId);
	    if (result != null && !result.isEmpty()) {
	        return (java.util.Date) result.get(0).get("time");
	    }
	    return null;
	}

	/**
	 * 锁定用户账号
	 * @param userId 用户ID
	 * @param minutes 锁定时间(分钟)
	 */
	public void lockUser(String userId, int minutes) {
	    // 先删除该用户之前的锁定记录
	    String deleteSql = "DELETE FROM " + CBSystemConstants.opcardUser + "t_a_parameter WHERE type='login' AND value=?";
	    DBManager.execute(deleteSql, userId);

	    // 计算解锁时间
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.MINUTE, minutes);
		// 格式化日期为字符串
		String unlockTime = SDF.format(cal.getTime());

	    // 插入新的锁定记录
	    String insertSql = "INSERT INTO " + CBSystemConstants.opcardUser + "t_a_parameter (type, value, time) " +
				"VALUES ('login', '"+userId+"', TO_DATE('"+unlockTime+"', 'YYYY-MM-DD HH24:MI:SS'))";
	    DBManager.execute(insertSql);
	}
	
}
