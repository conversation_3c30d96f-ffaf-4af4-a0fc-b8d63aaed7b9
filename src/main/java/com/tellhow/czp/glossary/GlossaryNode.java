/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：
* 功能说明 : 常用语树节点
* 作    者 : 邹力兴
* 开发日期 : 2008-09-11
* 修改日期 ：
* 修改说明 ：
* 修 改 人 ：
**/

package com.tellhow.czp.glossary;

import javax.swing.tree.DefaultMutableTreeNode;

/**
 *
 * <AUTHOR>
 */
public class GlossaryNode extends DefaultMutableTreeNode{
    private Glossary glossary;//常用语
    private String nodeType;
    public GlossaryNode(Glossary glossary){
        super(glossary.getGlossary());
        this.glossary=glossary;
        nodeType="Glossary";
    }
    
    public Glossary getGlossary(){
        return glossary;
    }
}
