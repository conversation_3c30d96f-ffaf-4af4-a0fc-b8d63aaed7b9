/*
 * RRRR.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.glossary;

import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

import javax.swing.DefaultCellEditor;
import javax.swing.Icon;
import javax.swing.ImageIcon;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.table.DefaultTableModel;
import javax.swing.text.JTextComponent;
import javax.swing.tree.DefaultTreeCellRenderer;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;

import org.apache.log4j.Logger;
import org.beryl.gui.GUIException;
import org.beryl.gui.table.TableSorter;

import com.tellhow.czp.mainframe.JPopupTextField;
import com.tellhow.czp.operationcard.OperateTicketSGPDefault;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.user.UserLogin;

/**
 * 
 * <AUTHOR>
 */
public class GlossarySortJDialog extends javax.swing.JDialog {

	public GlossarySortJDialog(JDialog parent) {
		super(parent, "常用语管理", false);
		initComponents();
		this.initComponentss();
		initTree();
		initTable(currentGlossarySort, UPDATE);
		setLocationCenter();
		dialog = this;
	}

	public GlossarySortJDialog(JFrame parent) {
		super(parent, "常用语管理", false);
		initComponents();
		this.initComponentss();
		initTree();
		initTable(currentGlossarySort, UPDATE);
		setLocationCenter();
		dialog = this;
	}

	public GlossarySortJDialog(JFrame parent, JTextComponent taTask) {
		super(parent, "常用语管理", false);
		this.taTask = taTask;
		initComponents();
		this.initComponentss();
		tableGlossary.addMouseListener(new MouseAdapter() {
			public void mouseClicked(MouseEvent e) {
				if (e.getButton() == e.BUTTON1 && e.getClickCount() == 2) {
					bSelectActionPerformed(new ActionEvent(bSelect, 0, ""));
				}
			}
		});
		bSelect.setEnabled(true);
		initTree();
		initTable(currentGlossarySort, UPDATE);
		setLocationCenter();
//		TempTicket.setTjc(null);
		//OperateTicketSGPDefault.jc=null;
		dialog = this;
	}

	private static Connection conn = null;
	private static String ADD = "add";
	private static String UPDATE = "update";

	private GlossaryDao gDBOperator = null;

	private DefaultTableModel dTableModel = null;// 列表模型
	private TableSorter sorter;
	private JTextComponent taTask;// 操作任务框

	private DefaultTreeModel dtm = null;

	private javax.swing.JMenuItem flash;
	private javax.swing.JPopupMenu treePopuMenu;
	private javax.swing.JSeparator jSeparator;
	private javax.swing.JMenuItem miAddChild;
	private javax.swing.JMenuItem miAddGlossary;
	private javax.swing.JMenuItem miDeleteNode;
	private javax.swing.JMenuItem miUpdate;

	private GlossarySort currentGlossarySort;// 当前选中的常用语类别
	private static Logger log = Logger.getLogger(UserLogin.class);

	private void initComponentss() {

		treePopuMenu = new javax.swing.JPopupMenu();
		miAddChild = new javax.swing.JMenuItem();
		miUpdate = new javax.swing.JMenuItem();
		miDeleteNode = new javax.swing.JMenuItem();
		jSeparator = new javax.swing.JSeparator();
		miAddGlossary = new javax.swing.JMenuItem();
		flash = new javax.swing.JMenuItem();
		tableGlossary = new javax.swing.JTable() {
			public void changeSelection(int rowIndex, int columnIndex,
					boolean toggle, boolean extend) {
				super.changeSelection(rowIndex, columnIndex, toggle, extend);
				super.editCellAt(rowIndex, columnIndex, null);
				bSelect.setEnabled(true);
			}

			@Override
			public void clearSelection() {
				super.clearSelection();
				bSelect.setEnabled(false);
			}

			public void setValueAt(Object aValue, int row, int column) {
				Glossary g;
				g = (Glossary) tableGlossary.getModel().getValueAt(row, column);
				g.setGlossary((String) aValue);
				dTableModel.setValueAt(g, row, column);
			}
		};
		bSelect.setEnabled(false);

		miAddChild.setText("添加子类别");
		miAddChild.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				miAddChildActionPerformed(evt);
			}
		});
		treePopuMenu.add(miAddChild);

		miUpdate.setText("修改类别名称");
		miUpdate.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				miUpdateActionPerformed(evt);
			}
		});
		treePopuMenu.add(miUpdate);

		miDeleteNode.setText("删除此类别");
		miDeleteNode.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				miDeleteNodeActionPerformed(evt);
			}
		});
		treePopuMenu.add(miDeleteNode);
		treePopuMenu.add(jSeparator);

		miAddGlossary.setText("添加常用语");
		miAddGlossary.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				miAddGlossaryActionPerformed(evt);
			}
		});
		treePopuMenu.add(miAddGlossary);

		flash.setText("刷新");
		flash.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				flashActionPerformed(evt);
			}
		});
		treePopuMenu.add(flash);

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		treeGlossary.setToggleClickCount(2);
		treeGlossary
				.addTreeSelectionListener(new javax.swing.event.TreeSelectionListener() {
					public void valueChanged(
							javax.swing.event.TreeSelectionEvent evt) {
						treeGlossaryValueChanged(evt);
					}
				});
		treeGlossary.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				treeGlossaryMouseClicked(evt);
			}
		});
		DefaultTreeCellRenderer cellRenderer = (DefaultTreeCellRenderer) treeGlossary
				.getCellRenderer();
		Icon icon = new ImageIcon(getClass().getResource(
				"/tellhow/icons/page.gif"));
		cellRenderer.setClosedIcon(icon);
		cellRenderer.setOpenIcon(icon);
		cellRenderer.setLeafIcon(icon);
		jScrollPane1.setViewportView(treeGlossary);

		tableGlossary.setRowHeight(24);
		DefaultCellEditor editor = new DefaultCellEditor(new JPopupTextField());
		tableGlossary.setDefaultEditor(Object.class, editor);
		jScrollPane2.setViewportView(tableGlossary);
	}

	/**
	 * 鼠标点击右键菜单增加常用语类别
	 * 
	 * @param evt
	 */
	private void miAddChildActionPerformed(java.awt.event.ActionEvent evt) {
		TreePath path = treeGlossary.getSelectionPath();
		if (path == null)
			return;
		GlossarySortNode lastNode = (GlossarySortNode) path
				.getLastPathComponent();
		GlossarySort glossarySort = lastNode.getGlossary();// 选中的常用语类别
		String preglossarySort = glossarySort.getGlossarySortID();// 添加常用语类别的上级节点
		String sortName = JOptionPane.showInputDialog(this, "请输入要添加的常用语类别：");
		if (sortName == null || sortName.equals(""))
			return;
		List<GlossarySort> lsGlossarySort = new ArrayList<GlossarySort>();
		lsGlossarySort.add(new GlossarySort("1", sortName, preglossarySort, 1));
		gDBOperator = new GlossaryDao();
		gDBOperator.glossarySortAdd(lsGlossarySort);
		initTree();
	}

	/**
	 * 保存修改或增加的常用语
	 * 
	 * @param evt
	 */
	private void bSaveActionPerformed(java.awt.event.ActionEvent evt) {
		tableGlossary.changeSelection(0, 0, false, false);
		int rowCount = tableGlossary.getRowCount();
		Glossary value;
		List<Glossary> lsGlossary = new ArrayList<Glossary>();
		for (int i = 0; i < rowCount; i++) {

			value = (Glossary) dTableModel.getValueAt(i, 1);
			if (value != null) {
				if (!value.getGlossary().equals(""))
					lsGlossary.add(value);
			}
		}
		gDBOperator = new GlossaryDao();
		// 新增
		if (evt.getActionCommand().equals(ADD)) {
			gDBOperator.glossaryAdd(lsGlossary);
		}
		// 修改
		if (evt.getActionCommand().equals(UPDATE)) {
			gDBOperator.glossaryUpdate(lsGlossary);
		}
		initTable(currentGlossarySort, UPDATE);
	}

	/**
	 * 修改常用语类别
	 * 
	 * @param evt
	 */
	private void miUpdateActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_miUpdateActionPerformed
		TreePath path = treeGlossary.getSelectionPath();
		if (path == null)
			return;
		GlossarySortNode lastNode = (GlossarySortNode) path
				.getLastPathComponent();
		GlossarySort glossarySort = lastNode.getGlossary();// 选中的常用语类别
		String sortName = JOptionPane.showInputDialog(this, "请输入要修改的常用语类别：",
				glossarySort.getSortName());
		if (sortName == null || sortName.equals(""))
			return;
		List<GlossarySort> lsGlossarySort = new ArrayList<GlossarySort>();
		glossarySort.setSortName(sortName);
		lsGlossarySort.add(glossarySort);
		gDBOperator = new GlossaryDao();
		gDBOperator.glossarySortUpdate(lsGlossarySort);
		initTree();
	}

	/**
	 * 选中按钮功能
	 * 
	 * @param evt
	 */
	private void bSelectActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_bSelectActionPerformed
		int[] rowcount = tableGlossary.getSelectedRows();// 选中的行数
		if (rowcount.length == 0)
			return;
		for (int i = 0; i < rowcount.length; i++) {
			String task = ((Glossary) dTableModel.getValueAt(rowcount[i], 1))
					.getGlossary();
			if(taTask != null) {
				int fk=taTask.getCaretPosition();
				String fkstring=taTask.getText().trim();
				String first=fkstring.substring(0, fk);
				String last=fkstring.substring(fk, fkstring.length());
				if (fkstring.equals("")) {
					taTask.setText(task);
				} else {
					taTask.setText(first+task+last);
				}
			}
		}
		this.setVisible(false);
		this.dispose();
	}

	/**
	 * 增加常用语按钮
	 * 
	 * @param evt
	 */
	private void bAddActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_bAddActionPerformed
		if (currentGlossarySort == null) {
			JOptionPane.showMessageDialog(this, "请选常用语类别！", "操作票提示框",
					JOptionPane.INFORMATION_MESSAGE);
			return;
		}
		initTable(currentGlossarySort, ADD);
		bSave.setActionCommand(ADD);
	}

	/**
	 * 删除常用语按钮
	 * 
	 * @param evt
	 */
	private void bDelActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_bDelActionPerformed
		int[] selectedRows;
		List<Glossary> lsGlossary = new ArrayList<Glossary>();
		selectedRows = tableGlossary.getSelectedRows();
		// 没选择任何行，直接返回
		if (selectedRows.length == 0) {
			JOptionPane.showMessageDialog(this, "你没有选择要删除是行！", "操作票提示框",
					JOptionPane.WARNING_MESSAGE);
			return;
		}
		int ok = JOptionPane.showConfirmDialog(this, "删除后不能恢复，你确定要删除吗？",
				"操作票提示框", JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.NO_OPTION) {
			return;
		}
		for (int i = 0; i < selectedRows.length; i++) {
			lsGlossary.add((Glossary) tableGlossary.getModel().getValueAt(
					selectedRows[i], 1));
		}
		// 删除选中的常用语
		gDBOperator = new GlossaryDao();
		gDBOperator.glossaryDelete(lsGlossary);
		initTable(currentGlossarySort, UPDATE);
	}

	/**
	 * 鼠标右键点击常用语类别树
	 * 
	 * @param evt
	 */
	private void treeGlossaryMouseClicked(java.awt.event.MouseEvent evt) {

		if (evt.getButton() == evt.BUTTON3) {
			TreePath path = treeGlossary.getPathForLocation(evt.getX(),
					evt.getY());
			if (path == null) {
				return;
			}
			treeGlossary.setSelectionPath(path);
			// 设置当前选中的常用语类别
			GlossarySortNode lastNode = (GlossarySortNode) path
					.getLastPathComponent();
			currentGlossarySort = lastNode.getGlossary();
			initTable(currentGlossarySort, UPDATE);
			bSave.setActionCommand(UPDATE);
			// 弹出菜单
			treePopuMenu.show(treeGlossary, evt.getX(), evt.getY());
		}
	}

	/**
	 * 鼠标点击右键菜单删除常用语类别
	 * 
	 * @param evt
	 */
	private void miDeleteNodeActionPerformed(java.awt.event.ActionEvent evt) {
		TreePath path = treeGlossary.getSelectionPath();
		if (path == null)
			return;
		GlossarySortNode lastNode = (GlossarySortNode) path
				.getLastPathComponent();
		GlossarySort glossarySort = lastNode.getGlossary();// 选中的常用语类别
		int ok = JOptionPane.showConfirmDialog(this, "删除后不能恢复，你确定要删除吗？",
				"操作票提示框", JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.NO_OPTION) {
			return;
		}
		List<GlossarySort> lsGlossarySort = new ArrayList<GlossarySort>();
		lsGlossarySort.add(glossarySort);
		gDBOperator = new GlossaryDao();
		gDBOperator.glossarySortDelete(lsGlossarySort);
		initTree();
		initTable(currentGlossarySort, UPDATE);
	}

	/**
	 * 鼠标点击右键菜单刷新常用语
	 * 
	 * @param evt
	 */
	private void flashActionPerformed(java.awt.event.ActionEvent evt) {
		initTree();
		initTable(currentGlossarySort, UPDATE);
	}

	/**
	 * 鼠标点击右键菜单增加常用语
	 * 
	 * @param evt
	 */
	private void miAddGlossaryActionPerformed(java.awt.event.ActionEvent evt) {
		TreePath path = treeGlossary.getSelectionPath();
		if (path == null)
			return;
		// 设置当前选中的常用语类别
		GlossarySortNode lastNode = (GlossarySortNode) path
				.getLastPathComponent();
		currentGlossarySort = lastNode.getGlossary();
		initTable(currentGlossarySort, ADD);
		bSave.setActionCommand(ADD);
	}

	/**
	 * 初始化树
	 */
	private void initTree() {
		GlossaryDao gd = new GlossaryDao();
		currentGlossarySort = new GlossarySort("0", "常用语类别", "-1", 0);
		GlossarySortNode rootNode = new GlossarySortNode(currentGlossarySort);
		rootNode.addGlossarySortNode(gd.CreateGlossaryNode("0"));
		dtm = new DefaultTreeModel(rootNode);
		DefaultTreeCellRenderer dtc=new DefaultTreeCellRenderer();
		dtc.setLeafIcon(new ImageIcon(getClass().getResource("/tellhow/btnIcon/copy.gif")));
		treeGlossary.setCellRenderer(dtc);
		treeGlossary.setModel(dtm);
	}

	/**
	 * 常用语类别选择变更事件
	 * 
	 * @param evt
	 */
	private void treeGlossaryValueChanged(
			javax.swing.event.TreeSelectionEvent evt) {// GEN-FIRST:event_treeGlossaryValueChanged
		TreePath path = treeGlossary.getSelectionPath();
		if (path == null) {
			return;
		}
		// 设置当前选中的常用语类别
		GlossarySortNode lastNode = (GlossarySortNode) path
				.getLastPathComponent();
		currentGlossarySort = lastNode.getGlossary();
		initTable(currentGlossarySort, UPDATE);
		bSave.setActionCommand(UPDATE);
	}

	// state表示是查看还是修改
	private void initTable(GlossarySort glossarySort, String state) {
		Object[][] body = null;
		String[] head = { "序号", "常用语" };
		dTableModel = new DefaultTableModel(body, head) {
			boolean[] canEdit = new boolean[] { false, true };

			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return canEdit[columnIndex];
			}
		};
		if (state.equals(UPDATE)) {
			GlossaryDao gd = new GlossaryDao();
			List<Object[]> gloss = gd.getGlossaryByID(glossarySort
					.getGlossarySortID());
			for (int i = 0; i < gloss.size(); i++) {
				Object[] rowData = gloss.get(i);
				dTableModel.addRow(rowData);
			}
			bSave.setActionCommand(UPDATE);
		} else {
			for (int i = 0; i < 10; i++) {
				Object[] rowData = {
						(i + 1),
						new Glossary("",
								currentGlossarySort.getGlossarySortID()) };
				dTableModel.addRow(rowData);
			}
		}
		try {
			sorter = new TableSorter();
		} catch (GUIException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		sorter.setModel(dTableModel);
		tableGlossary.setModel(sorter);
		//		sorter.setTableHeader(tableGlossary.getTableHeader());
		tableGlossary.getColumnModel().getColumn(0).setMaxWidth(50);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** Creates new form RRRR */
	public GlossarySortJDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.initComponentss();
		this.initTree();
		initTable(currentGlossarySort, UPDATE);
		setLocationCenter();
	}

	/**
	 * This method is called from within the constructor to initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is always
	 * regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jPanel1 = new javax.swing.JPanel();
		jScrollPane1 = new javax.swing.JScrollPane();
		treeGlossary = new javax.swing.JTree();
		bSave = new javax.swing.JButton();
		bDel = new javax.swing.JButton();
		bAdd = new javax.swing.JButton();
		bSelect = new javax.swing.JButton();
		jScrollPane2 = new javax.swing.JScrollPane();
		tableGlossary = new javax.swing.JTable();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jScrollPane1.setViewportView(treeGlossary);
		jPanel1.setBackground(new java.awt.Color(244, 243, 243));
		jScrollPane1.getViewport().setBackground(new java.awt.Color(244, 243, 243));
		jScrollPane2.getViewport().setBackground(new java.awt.Color(244, 243, 243));

		bSave.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/save.png"))); // NOI18N
		bSave.setToolTipText("\u4fdd\u5b58");
		bSave.setBorder(null);
		bSave.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				bSaveActionPerformed(evt);
			}
		});

		bDel.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/delete.png"))); // NOI18N
		bDel.setToolTipText("\u5220\u9664\u5e38\u7528\u8bed");
		bDel.setBorder(null);
		bDel.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				bDelActionPerformed(evt);
			}
		});

		bAdd.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/add.png"))); // NOI18N
		bAdd.setToolTipText("\u589e\u52a0\u5e38\u7528\u8bed");
		bAdd.setBorder(null);
		bAdd.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				bAddActionPerformed(evt);
			}
		});

		bSelect.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
		bSelect.setToolTipText("\u9009\u4e2d");
		bSelect.setBorder(null);
		bSelect.setEnabled(false);
		bSelect.setFocusPainted(false);
		bSelect.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				bSelectActionPerformed(evt);
			}
		});

		tableGlossary.setModel(new javax.swing.table.DefaultTableModel(
				new Object[][] { { null, null, null, null },
						{ null, null, null, null }, { null, null, null, null },
						{ null, null, null, null } }, new String[] { "Title 1",
						"Title 2", "Title 3", "Title 4" }));
		jScrollPane2.setViewportView(tableGlossary);

		org.jdesktop.layout.GroupLayout jPanel1Layout = new org.jdesktop.layout.GroupLayout(
				jPanel1);
		jPanel1.setLayout(jPanel1Layout);
		jPanel1Layout
				.setHorizontalGroup(jPanel1Layout
						.createParallelGroup(
								org.jdesktop.layout.GroupLayout.LEADING)
						.add(jPanel1Layout
								.createSequentialGroup()
								.add(jScrollPane1,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
										172,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(jPanel1Layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.LEADING)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jPanel1Layout
														.createSequentialGroup()
														.add(bSelect)
														.addPreferredGap(
																org.jdesktop.layout.LayoutStyle.RELATED)
														.add(bAdd)
														.addPreferredGap(
																org.jdesktop.layout.LayoutStyle.RELATED)
														.add(bDel)
														.addPreferredGap(
																org.jdesktop.layout.LayoutStyle.RELATED)
														.add(bSave))
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jScrollPane2,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												481, Short.MAX_VALUE))
								.addContainerGap()));
		jPanel1Layout
				.setVerticalGroup(jPanel1Layout
						.createParallelGroup(
								org.jdesktop.layout.GroupLayout.LEADING)
						.add(jScrollPane1,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								491, Short.MAX_VALUE)
						.add(jPanel1Layout
								.createSequentialGroup()
								.addContainerGap()
								.add(jPanel1Layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.TRAILING)
										.add(jPanel1Layout
												.createParallelGroup(
														org.jdesktop.layout.GroupLayout.LEADING)
												.add(bSave).add(bDel).add(bAdd))
										.add(bSelect))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(jScrollPane2,
										org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
										438, Short.MAX_VALUE).addContainerGap()));

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jPanel1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE));
		layout.setVerticalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jPanel1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	public static void main(String args[]) {
		new GlossarySortJDialog(new JFrame()).setVisible(true);
	}

	public static GlossarySortJDialog getDialog() {
		return dialog;
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton bAdd;
	private javax.swing.JButton bDel;
	private javax.swing.JButton bSave;
	private javax.swing.JButton bSelect;
	private javax.swing.JPanel jPanel1;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JTable tableGlossary;
	private javax.swing.JTree treeGlossary;
	private static GlossarySortJDialog dialog = null;
	// End of variables declaration//GEN-END:variables

}
