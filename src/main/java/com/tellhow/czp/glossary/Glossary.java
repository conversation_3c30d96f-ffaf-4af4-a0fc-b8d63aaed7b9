/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：
* 功能说明 : 常用语信息
* 作    者 : 邹力兴
* 开发日期 : 2008-09-11
* 修改日期 ：
* 修改说明 ：
* 修 改 人 ：
**/

package com.tellhow.czp.glossary;

/**
 *t.glossaryid,t.glossary,t.glossarysortid,t.delflag
 * <AUTHOR>
 */
public class Glossary {
    private String glossaryid;//常用语ID
    private String glossary="";//常用语
    private String glossarysortid;//类别ID
    private int delflag;//删除标志(0-未删除1-已删除)
    
    public Glossary(){
        
    }
    public Glossary(String glossary,String glossarysortid){
        this.glossary=glossary;
        this.glossarysortid=glossarysortid;
        this.delflag=0;
    }
    public Glossary(String glossaryid,String glossary,String glossarysortid){
        this.glossaryid=glossaryid;
        this.glossary=glossary;
        this.glossarysortid=glossarysortid;
        this.delflag=0;
    }
    public Glossary(String glossaryid,String glossary,String glossarysortid,int delflag){
        this.glossaryid=glossaryid;
        this.glossary=glossary;
        this.glossarysortid=glossarysortid;
        this.delflag=delflag;
    }
    //设置常用语ID
    public void setGlossaryID(String glossaryid){
        this.glossaryid=glossaryid;
    }
    //设置常用语
    public void setGlossary(String glossary){
        this.glossary=glossary;
    }
    //设置类别ID
    public void setGlossarySortID(String glossarysortid){
        this.glossarysortid=glossarysortid;
    }
    //设置删除标志
    public void setDelFlag(int delflag){
        this.delflag=delflag;
    }
    //获取常用语ID
    public String getGlossaryID(){
        return glossaryid;
    }
    //获取常用语
    public String getGlossary(){
        return glossary;
    }
    //获取类别ID
    public String getGlossarySortID(){
        return glossarysortid;
    }
    //获取删除标志
    public int getDelFlag(){
        return delflag;
    }
    @Override
	public String toString(){
        return glossary;
    }
}
