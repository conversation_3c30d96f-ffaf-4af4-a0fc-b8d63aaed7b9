package com.tellhow.czp.glossary;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class GlossaryDao {

//private String unitcode=CBSystemConstants.unitCode;//机构编码
private String unitcode=CBSystemConstants.opCode;
    
    public GlossaryDao(){
        
    }
    /**
     * 添加常用语类别（上级类别id为0，则说明此类别为最顶层）
     * @param lsGlossarySort 常用语类别数组列表
     */
    public int glossarySortAdd(List lsGlossarySort){
        if(lsGlossarySort.size()==0)
            return 0;
        int countInsert=0;//插入的记录条数
        String glossarysortid;//类别ID
        String sortname;//类别名称
        String preglossarysortid;//上级类别ID
        String strSql="";

        for (int i = 0; i < lsGlossarySort.size(); i++) {
            GlossarySort glossarySort= (GlossarySort)lsGlossarySort.get(i);
            //生成唯一的ID
            glossarysortid = java.util.UUID.randomUUID().toString();
            //insert的SQL
            sortname=glossarySort.getSortName();
            preglossarysortid=glossarySort.getPreGlossarySortID();
            strSql ="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_GLOSSARYSORT(GLOSSARYSORTID,SORTNAME,PREGLOSSARYSORTID,OPCODE) VALUES('"+glossarysortid+"','"+sortname+"','"+preglossarysortid+"','"+unitcode+"')";
            DBManager.execute(strSql);
            countInsert++;
        }
            //关闭
        return countInsert;
    }
    
    /**
     * 修改常用语类别（上级类别id为0，则说明此类别为最顶层）
     * @param lsGlossarySort 常用语类别数组列表
     * 
     */
    public int glossarySortUpdate(List lsGlossarySort){
        if(lsGlossarySort.size()==0)
            return 0;
        int countUpdate=0;//更新的记录条数
        String glossarysortid;//类别ID
        String sortname;//类别名称
        String preglossarysortid;//上级类别ID
        for (int i = 0; i < lsGlossarySort.size(); i++) {
                GlossarySort glossarySort= (GlossarySort)lsGlossarySort.get(i);
                glossarysortid = glossarySort.getGlossarySortID();
                sortname = glossarySort.getSortName();
                preglossarysortid = glossarySort.getPreGlossarySortID();
                String strSql ="UPDATE "+CBSystemConstants.opcardUser+"t_a_GLOSSARYSORT SET SORTNAME='" + sortname + "',PREGLOSSARYSORTID='" + preglossarysortid + "' WHERE GLOSSARYSORTID='" + glossarysortid + "'";;
                DBManager.execute(strSql);
                countUpdate++;
            }
        return countUpdate;
    }
    
    /**
     * 删除常用语类别（上级类别id为0，则说明此类别为最顶层）（逻辑删除）
     * 只是修改记录的删除标志
     * @param lsGlossarySort 列表
     */
    public void glossarySortDelete(List lsGlossarySort){
        if(lsGlossarySort.size()==0)
            return;
        String glossarysortid;
        //循环删除
        for (int i = 0; i < lsGlossarySort.size(); i++) {
            GlossarySort glossarySort = (GlossarySort) lsGlossarySort.get(i);
            glossarysortid = glossarySort.getGlossarySortID();
            deleteGlossarySort(glossarysortid);
        }
    }
    /**
     * 递归删除指定的常用语类别ID
     * @param glossarysortid
     */
    private void deleteGlossarySort(String glossarysortid){
            String strSql="";
            
            //删除常用语类别
            strSql = "UPDATE "+CBSystemConstants.opcardUser+"t_a_GLOSSARYSORT SET DELFLAG=1 WHERE GLOSSARYSORTID='" + glossarysortid + "'";;
            DBManager.execute(strSql);
            //删除此类别的常用语
            strSql = "UPDATE "+CBSystemConstants.opcardUser+"t_a_GLOSSARY SET DELFLAG=1 WHERE GLOSSARYSORTID='" + glossarysortid + "'";;
            DBManager.execute(strSql);
            
            //查询常用语上级类别为此类别的常用语类别
            strSql = "SELECT GLOSSARYSORTID FROM "+CBSystemConstants.opcardUser+"t_a_GLOSSARYSORT WHERE DELFLAG=0 AND PREGLOSSARYSORTID='" + glossarysortid + "'";;
            List results=DBManager.queryForList(strSql);
    		Map temp=null;
    		String gsID="0";//常用语类别ID
    		for (int i = 0; i < results.size(); i++) {
    			temp=(Map)results.get(i);
    			gsID = StringUtils.ObjToString(temp.get("GLOSSARYSORTID"));
    			deleteGlossarySort(gsID);//递归
    		}           
    }
    //上面是对“常用语类别”进行数据库操作
    //下面是对“常用语”进行数据库操作
    
    /**
     * 添加常用语
     * @param lsGlossary 常用语数组列表
     */
    public int glossaryAdd(List lsGlossary){
        if(lsGlossary.size()==0)
            return 0;
        Glossary objGlossary;//常用语对象
        int countInsert=0;//插入的记录条数
        String glossaryid;//常用语ID
        String glossary;//常用语
        String glossarysortid;//类别ID
        String strSql="";
        for (int i = 0; i < lsGlossary.size(); i++) {
                objGlossary= (Glossary)lsGlossary.get(i);
                //生成唯一的ID
                glossaryid = java.util.UUID.randomUUID().toString();
                glossary=objGlossary.getGlossary();
                glossarysortid=objGlossary.getGlossarySortID();
                strSql ="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_GLOSSARY(GLOSSARYID,GLOSSARY,GLOSSARYSORTID,OPCODE) VALUES('"+glossaryid+"','"+glossary+"','"+glossarysortid+"','"+unitcode+"')";
                DBManager.execute(strSql);
                countInsert++;
            }   
        return countInsert;
    }
    /**
     * 修改常用语
     * @param lsGlossary 常用语数组列表
     * 
     */
    public int glossaryUpdate(List lsGlossary){
	        if(lsGlossary.size()==0)
	            return 0;
	        Glossary objGlossary;//常用语对象
	        int countUpdate=0;//更新的记录条数
	        String glossaryid;//常用语ID
	        String glossary;//常用语
	        String glossarysortid;//类别ID
	        String strSql="";
            for (int i = 0; i < lsGlossary.size(); i++) {
                strSql="";
                objGlossary= (Glossary)lsGlossary.get(i);
                glossaryid = objGlossary.getGlossaryID();
                glossary = objGlossary.getGlossary();
                glossarysortid = objGlossary.getGlossarySortID();
                //构造SQL
                strSql = "UPDATE "+CBSystemConstants.opcardUser+"t_a_GLOSSARY SET GLOSSARY='" + glossary + "',GLOSSARYSORTID='" + glossarysortid+ "' WHERE GLOSSARYID='" + glossaryid + "'";;
                DBManager.execute(strSql);
                countUpdate++;
            }

        return countUpdate;
    }
    /**
     * 删除常用语
     * @param lsGlossary 常用语数组列表
     * 
     */
    public int glossaryDelete(List lsGlossary){
	        if(lsGlossary.size()==0)
	            return 0;
	        Glossary objGlossary;//常用语对象
	        int countDelete=0;//删除的记录条数
	        String glossaryid;//常用语ID
	        String strSql="";
            for (int i = 0; i < lsGlossary.size(); i++) {
                strSql="";
                objGlossary= (Glossary)lsGlossary.get(i);
                glossaryid = objGlossary.getGlossaryID();
                //构造SQL
                strSql = "UPDATE "+CBSystemConstants.opcardUser+"t_a_GLOSSARY SET DELFLAG=1 WHERE GLOSSARYID='" + glossaryid + "'";
                DBManager.execute(strSql);
                countDelete++;
            }          
        return countDelete;
    }
    // 递归构造树节点
	public List<GlossarySortNode> CreateGlossaryNode(String preglossarysortid) {
			List<GlossarySortNode> nodes = new ArrayList<GlossarySortNode>();
			List<GlossarySortNode> childNodes = null;
			String glossarysortid;// 类别ID
			String sortname;// 类别名称
			String preid = preglossarysortid;// 上级类别ID
			String strSql = "SELECT * FROM "+CBSystemConstants.opcardUser+"t_a_GLOSSARYSORT WHERE OPCODE='"+unitcode+"' and DELFLAG=0 AND  PREGLOSSARYSORTID='" + preid + "'";
			List results=DBManager.queryForList(strSql);
			Map temp=null;
			for (int i = 0; i < results.size(); i++) {
				temp=(Map)results.get(i);
				glossarysortid = StringUtils.ObjToString(temp.get("GLOSSARYSORTID"));
				sortname = StringUtils.ObjToString(temp.get("SORTNAME"));
				preid = StringUtils.ObjToString(temp.get("PREGLOSSARYSORTID"));
				GlossarySortNode node = new GlossarySortNode(new GlossarySort(
						glossarysortid, sortname, preid));
				nodes.add(node);
				childNodes = CreateGlossaryNode(glossarysortid);// 用当前查到类别ID当上级类别ID
																// ，继续递归创建节点,返回节点数组
				node.addGlossarySortNode(childNodes);
			}		
			return nodes;
	}
    // 递归构造树节点
	public List<Object[]> getGlossaryByID(String glossID) {
		List<Object[]> glosss=new ArrayList<Object[]>();
		String glossaryid;// 常用语ID
		String glossary;// 常用语
		String glossarysortid;// 类别ID
		String strSql = "SELECT * FROM "+CBSystemConstants.opcardUser+"t_a_GLOSSARY WHERE OPCODE='"+unitcode+"' and DELFLAG=0 AND GLOSSARYSORTID='"+ glossID + "'";
		List results=DBManager.queryForList(strSql);
		Map temp=null;
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			glossaryid = StringUtils.ObjToString(temp.get("GLOSSARYID"));
			glossary = StringUtils.ObjToString(temp.get("GLOSSARY"));
			glossarysortid = StringUtils.ObjToString(temp.get("GLOSSARYSORTID"));
			Object[] rowData = { (i+1),
					new Glossary(glossaryid, glossary, glossarysortid) };
			glosss.add(rowData);
		}
		return glosss;
	}
}
