/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：
* 功能说明 : 常用语类别树节点
* 作    者 : 邹力兴
* 开发日期 : 2008-09-11
* 修改日期 ：
* 修改说明 ：
* 修 改 人 ：
**/
package com.tellhow.czp.glossary;

import java.util.List;

import javax.swing.tree.DefaultMutableTreeNode;

/**
 *
 * <AUTHOR>
 */
public class GlossarySortNode extends DefaultMutableTreeNode {
    private GlossarySort glossarySort;//常用语类别
    private String nodeType;
    public GlossarySortNode(GlossarySort glossarySort){
        super(glossarySort.getSortName());
        this.glossarySort=glossarySort;
        nodeType="GlossarySort";
    }
    //添加孩子节点
    public void addGlossarySortNode(List<GlossarySortNode> childNodes){
        for(int i=0;i<childNodes.size();i++){
            this.add(childNodes.get(i));
        }
    }
    
    public GlossarySort getGlossary(){
        return glossarySort;
    }
    
}
