/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：
* 功能说明 : 常用语类别信息
* 作    者 : 邹力兴
* 开发日期 : 2008-09-11
* 修改日期 ：
* 修改说明 ：
* 修 改 人 ：
**/

package com.tellhow.czp.glossary;

/**
 *
 * <AUTHOR>
 */
public class GlossarySort {
    private String glossarysortid;//类别ID
    private String sortname;//类别名称
    private String preglossarysortid;//上级类别ID
    private int delflag;//删除标志(0-未删除1-已删除)
    public GlossarySort(){
        
    }
    public GlossarySort(String glossarysortid,String sortname,String preglossarysortid){
        this.glossarysortid=glossarysortid;
        this.sortname=sortname;
        this.preglossarysortid=preglossarysortid;
        this.delflag=0;
    }
    public GlossarySort(String glossarysortid,String sortname,String preglossarysortid,int delflag){
        this.glossarysortid=glossarysortid;
        this.sortname=sortname;
        this.preglossarysortid=preglossarysortid;
        this.delflag=delflag;
    }
    //设置类别ID
    public void setGlossarySortID(String glossarysortid){
        this.glossarysortid=glossarysortid;
    }
    //设置类别名称
    public void setSortName(String sortname){
        this.sortname=sortname;
    }
    //设置上级类别ID
    public void setPreGlossarySortID(String preglossarysortid){
        this.preglossarysortid=preglossarysortid;
    }
    //设置删除标志
    public void setDelFlag(int delflag){
        this.delflag=delflag;
    }
    //获取类别ID
    public String getGlossarySortID(){
        return glossarysortid;
    }
    //获取类别名称
    public String getSortName(){
        return sortname;
    }
    //获取上级类别ID
    public String getPreGlossarySortID(){
        return preglossarysortid;
    }
    //获取删除标志
    public int getDelFlag(){
        return delflag;
    }
}
