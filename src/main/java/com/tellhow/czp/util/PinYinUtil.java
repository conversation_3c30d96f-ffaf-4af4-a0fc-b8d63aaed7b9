package com.tellhow.czp.util;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

public class PinYinUtil {

	static List<String> output = new ArrayList<String>();
	static List<String> head = new ArrayList<String>();

	public static List<String> getPinYin(String inputString) {

		HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
		format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
		format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
		format.setVCharType(HanyuPinyinVCharType.WITH_V);
		output = new ArrayList<String>();
		head = new ArrayList<String>();
		char[] input = inputString.trim().toCharArray();
		try {
			for (int i = 0; i < input.length; i++) {
				if (Character.toString(input[i]).matches("[\u4E00-\u9FA5]+")) {
					String[] temp = PinyinHelper.toHanyuPinyinStringArray(
							input[i], format);
					if (output.size() == 0) {
						for (int j = 0; j < temp.length; j++) {
							output.add(temp[j]);
							head.add(Character.toString(temp[j].charAt(0)));
						}
					} else {
						List<String> ot=new ArrayList<String>();
						List<String> ht=new ArrayList<String>();
						for (Iterator<String> itr = output.iterator(); itr.hasNext();) {
							String s = (String) itr.next();
							for (int k = 0; k < temp.length; k++) {
								ot.add(s+temp[k]);
							}
						}
						output.clear();
						output.addAll(ot);
						for (Iterator<String> itr = head.iterator(); itr.hasNext();) {
							String s = (String) itr.next();
							for (int k = 0; k < temp.length; k++) {
								ht.add(s+temp[k].charAt(0));
							}
						}
						head.clear();
						head.addAll(ht);
					}

				} else {
					List<String> ot=new ArrayList<String>();
					List<String> ht=new ArrayList<String>();
					String s;
					for (Iterator<String> itr = output.iterator(); itr.hasNext();) {
						s = (String) itr.next();
						ot.add(s + input[i]);
					}
					output.clear();
					output.addAll(ot);
					for (Iterator<String> itr = head.iterator(); itr.hasNext();) {
						s = (String) itr.next();
						ht.add(s + input[i]);
					}
					head.clear();
					head.addAll(ht);
				}

			}
		} catch (BadHanyuPinyinOutputFormatCombination e) {
			e.printStackTrace();
		}

		return output;
	}

	/**
	 * 只有输入汉字才能获得拼音首字母
	 * */
	public static List<String> getHead(String inputString) {
		getPinYin(inputString);
		return head;
	}

	public static void main(String[] args) {
		String chs = "通州";
//		System.out.println(chs);
//		System.out.println(getPinYin(chs));
	}

}
