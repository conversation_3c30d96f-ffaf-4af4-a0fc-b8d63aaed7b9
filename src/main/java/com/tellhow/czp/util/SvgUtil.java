package com.tellhow.czp.util;

import java.util.List;

import com.tellhow.graphicframework.action.impl.ChooseDeviceRectFlashingAction;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;

public class SvgUtil {
	//取消多选选中状态
	public static void clear(){
		List<PowerDevice> pdlist=CBSystemConstants.getSamepdlist();
		if(pdlist.size()>0){
			for(int i=0;i<pdlist.size();i++){
				ChooseDeviceRectFlashingAction cdrfa = new ChooseDeviceRectFlashingAction(pdlist.get(i));
				cdrfa.backexecute();
			}
			CBSystemConstants.clearSamepdlist();
		}
	}
}
