package com.tellhow.czp.util;

import java.io.StringWriter;

import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.w3c.dom.Element;

public class Dom4jUtil {
     public static String   ToXMl(Element n){
    	
    	 TransformerFactory transFactory = TransformerFactory.newInstance(); 
    	 Transformer transformer = null; 
    	 try { 
    	       transformer = transFactory.newTransformer(); 
    	 } catch (TransformerConfigurationException e) 
    	 { 
    	      e.printStackTrace(); 
    	 } 
    	 StringWriter buffer = new StringWriter(); 
    	 transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes"); 
    	 try { 
    	    transformer.transform(new DOMSource(n),new StreamResult(buffer)); 
    	 } catch (TransformerException e) 
    	 { 
    	     e.printStackTrace(); 
    	 } 
    	 String s = buffer.toString(); 
    	 return s;
        }
}
