package com.tellhow.czp.util;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import sun.util.logging.resources.logging;

public class ThreadPoolUtil {
	 private ExecutorService _threadPool;
	    private volatile boolean _initialized;
	    
	    public static final int DEFAULT_THREAD_NUMBER = 1;
	    
	    
	    public ThreadPoolUtil() {
	        this(DEFAULT_THREAD_NUMBER);
	    }
	    
	    public ThreadPoolUtil(int threadNumber) {
	        chechThreadNumber(threadNumber);
	        doInitThreadPool(threadNumber);
	    }
	    
	    public boolean isInitialized() {
	        return _initialized;
	    }
	    
	    public boolean isTerminated() {
	        return _threadPool.isTerminated();
	    }
	    
	    public Future<?> submit(Runnable task) {
	        checkThreadPoolStatus();
	        return _threadPool.submit(task);
	    }
	    
	    public <T> Future<T> submit(Callable<T> task) {
	        checkThreadPoolStatus();
	        return _threadPool.submit(task);
	    }

	    public void resize(int threadNumber) {
	        chechThreadNumber(threadNumber);
	        destroy();
	        doInitThreadPool(threadNumber);
	    }
	    
	    public void destroy() {
	        if (!isInitialized()) {
	            System.err.println("ThreadPoolManager has not initialized.");
	            return;
	        }
	        
	        _threadPool.shutdown();
	        try {
	            if (!_threadPool.awaitTermination(2, TimeUnit.SECONDS)) {
	                _threadPool.shutdownNow();
	                
	                if (!_threadPool.awaitTermination(60, TimeUnit.SECONDS)) {
	                	System.err.println("Could not terminate thread pool manager!");
	                }
	            }
	        } catch (InterruptedException e) {
	            _threadPool.shutdownNow();
	            Thread.currentThread().interrupt();
	        }
	        _initialized = false;
	        
	        System.err.println("ThreadPoolManager has destroyed.");
	    }
	    
	    private void checkThreadPoolStatus() {
	        if (!isInitialized()) {
	            throw new IllegalStateException("ThreadPoolManager is not initialized!");
	        }
	    }

	    private void doInitThreadPool(int threadNumber) {
	        _threadPool = Executors.newFixedThreadPool(threadNumber);
	        _initialized = true;
	        
		  System.err.println("ThreadPoolManager has initialized with [{"
				+ threadNumber + "}] thread number");
	    }
	    
	    private void chechThreadNumber(int threadNumber) {
	        if (threadNumber <= 0) {
	            throw new IllegalArgumentException("Thread number must be a positive number: " + threadNumber);
	        }
	    }
	    
}
