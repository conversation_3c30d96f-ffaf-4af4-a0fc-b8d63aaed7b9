package com.tellhow.czp.util;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;

import org.apache.commons.collections.CollectionUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tellhow.czp.sysconfig.Auth;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class GUIUtil {
	private  List<CodeNameModel> menuitem; //菜单权限集
	private  List<CodeNameModel> toolbar; //工具栏权限集
	private  List<String> menuitemid; //菜单id权限集
	private  List<String> toolbarid; //工具栏id权限集
	
	//分析xml文件
	public void anaGUI(){
	   menuitem=new ArrayList<CodeNameModel>();
	   menuitemid=new ArrayList<String>();
	   toolbar=new ArrayList<CodeNameModel>();
	   toolbarid=new ArrayList<String>();
	   SAXReader sr = new SAXReader();
	   Document document = null;
	   try {
			document = sr.read(new File("config/GUIBuilder.xml"));
		} catch (DocumentException e) {
			e.printStackTrace();
		}
	    Element root = document.getRootElement(); 
	    Element wEle = root.element("widget");
	    List<Element> eles = wEle.elements("widget");
	    for (int i=0;i<eles.size();i++) {
			Element ie=eles.get(i);
			String widget=ie.attribute("class").getText();
			if(widget.equals("MenuBar")){
				List<Element> ieles=ie.elements("widget");
				for(int j=0;j<ieles.size();j++){
					Element je=ieles.get(j);
					String jwidget=je.attribute("class").getText();
					if(jwidget.equals("Menu")){
						List<Element> jeles=je.elements("widget");
						for(int k=0;k<jeles.size();k++){
							Element ke=jeles.get(k);
							String kwidget=ke.attribute("class").getText();
							if(kwidget.equals("MenuItem")){
								Element emit=ke.element("emit");
								Element property=ke.element("property");
								String id=emit.attribute("name").getText();
								String name=property.getText();
								CodeNameModel cnm=new CodeNameModel(id,name);
								if(menuitemid.contains(id)){
								}else{
									menuitem.add(cnm);
									menuitemid.add(id);
								}
							}
						}
					}
				}
			}
			int n=0;
			int u=0;
			if(widget.equals("ToolBarPanel")){
				List<Element> ieles=ie.elements("widget");
				for(int j=0;j<ieles.size();j++){
					Element je=ieles.get(j);
					String jwidget=je.attribute("class").getText();
					if(jwidget.equals("ToolBar")){
						List<Element> jeles=je.elements("widget");
						for(int k=0;k<jeles.size();k++){
							Element ke=jeles.get(k);
							String kwidget=ke.attribute("class").getText();
							if(kwidget.equals("Button")){
								List<Element> propertys=ke.elements("property");
								for(int z=0;z<propertys.size();z++){
									Element property=propertys.get(z);
									String cname=property.attribute("name").getText();
									if(cname.equals("text")){
										String id=ke.attribute("name").getText();
										String name=property.getText();
										CodeNameModel cnm=new CodeNameModel(id,name);
										toolbar.add(cnm);
										toolbarid.add(id);
									}
								}
							}
						}
					}
				}
			}
		}
	}
	
	public List<CodeNameModel> getmenuitem(){
		return menuitem;
	}
	
	public List<CodeNameModel> gettoolbar(){
		return toolbar;
	}
	
	public List<String> getmenuitemid(){
		return menuitemid;
	}
	
	public List<String> gettoolbarid(){
		return toolbarid;
	}
	
	//处理xml文件：根据权限修改原xml文件生成临时用的New...xml文件
	public ArrayList<String> disposeguiMenu(String usercode){
		anaGUI();
		List results = DBManager.query("select a.userid as userid, a.authid as authid,a.authname as authname,SUM(a.ischeck) as ischeck from " +
				"(select distinct r.userid ,rm.authid,rm.authname,rm.ischeck from "+CBSystemConstants.opcardUser+"T_A_USERROLE r,"+CBSystemConstants.opcardUser+"T_A_POWERROLEAUTHINFO rm " +
				"where r.userid='"+usercode+"' and rm.roleid = r.rolecode and rm.classname='MenuItem' union " +
				"select userid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO u where u.userid='"+usercode+"' and u.classname='MenuItem' union " +
						"select distinct '"+usercode+"' as userid ,rm.authid,rm.authname,rm.ischeck from "+CBSystemConstants.opcardUser+"T_A_POWERROLEAUTHINFO rm where  rm.roleid = '4' and rm.classname='MenuItem' ) a" +
				" group by a.userid,a.authid,a.authname");
		Map tabletemp=new HashMap();
		List<Auth> resultslist=new ArrayList<Auth>();
		List<String> resultsstring=new ArrayList<String>();
		for(int j=0;j<results.size();j++){
			tabletemp = (Map) results.get(j);
			
			String userid = StringUtils.ObjToString(tabletemp.get("userid"));
			String authid=StringUtils.ObjToString(tabletemp.get("authid"));
			String authname=StringUtils.ObjToString(tabletemp.get("authname"));
			String isckeck=StringUtils.ObjToString(tabletemp.get("ischeck"));
			int ischeck=Integer.parseInt(isckeck);
			Auth auth=new Auth(userid, authid, authname, ischeck);
			if(ischeck==1){
				resultslist.add(auth);
				resultsstring.add(authid);
			}
		}
		
	
		ArrayList<String> disList = new ArrayList<String>();
		Collection<String> disjunction =CollectionUtils.disjunction(menuitemid,resultsstring);
		disList.addAll(disjunction);
		return disList;
	}
	
	public ArrayList<String> disposeguiTool(String usercode){
		
		List<String> roleSelectList=new ArrayList<String>();
		String sql="select a.unitcode,b.rolecode,c.opcode from "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO a,"+CBSystemConstants.opcardUser+"T_A_USERROLE b,"+CBSystemConstants.opcardUser+"T_A_OPCODEINFO c where a.userid=b.userid and a.unitcode=c.areano and b.rolecode=c.rolecode and a.userid='"+usercode+"' order by b.rolecode";
	   try{
			List result=DBManager.queryForList(sql);
		    if(result.size() > 0) {
		    	for(int i = 0; i < result.size(); i++) {
		    		String rolecode = ((Map)result.get(i)).get("rolecode").toString();
		    		roleSelectList.add("roleSelect"+rolecode);
		    	}
		    	if(roleSelectList.size() == 1)
		    		roleSelectList.clear();
		    }
	   }
	   catch(Exception ex) {
		   
	   }
	   
		anaGUI();
		
		List resultsq = DBManager.query("select a.userid as userid, a.authid as authid,a.authname as authname,SUM(a.ischeck) as ischeck from " +
				"(select distinct r.userid ,rm.authid,rm.authname,rm.ischeck from "+CBSystemConstants.opcardUser+"T_A_USERROLE r,"+CBSystemConstants.opcardUser+"T_A_POWERROLEAUTHINFO rm " +
				"where r.userid='"+usercode+"' and rm.roleid = r.rolecode and rm.classname='Button' union " +
				"select userid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO u where u.userid='"+usercode+"' and u.classname='Button' union " +
						"select distinct '"+usercode+"' as userid ,rm.authid,rm.authname,rm.ischeck from "+CBSystemConstants.opcardUser+"T_A_POWERROLEAUTHINFO rm where  rm.roleid = '4' and rm.classname='Button' ) a" +
				" group by a.userid,a.authid,a.authname");
		Map tabletempq=new HashMap();
		List<Auth> resultslistq=new ArrayList<Auth>();
		List<String> resultsstringq=new ArrayList<String>();
		for(int j=0;j<resultsq.size();j++){
			tabletempq = (Map) resultsq.get(j);
			
			String userid = StringUtils.ObjToString(tabletempq.get("userid"));
			String authid=StringUtils.ObjToString(tabletempq.get("authid"));
			if(authid.indexOf("roleSelect") >= 0 && !roleSelectList.contains(authid))
				continue;
			String authname=StringUtils.ObjToString(tabletempq.get("authname"));
			String isckeck=StringUtils.ObjToString(tabletempq.get("ischeck"));
			int ischeck=Integer.parseInt(isckeck);
			Auth auth=new Auth(userid, authid, authname, ischeck);
			if(ischeck==1){
				resultslistq.add(auth);
				resultsstringq.add(authid);
			}
		}
		
		ArrayList<String> disList = new ArrayList<String>();
		Collection<String> disjunctiontoolbar=CollectionUtils.disjunction(toolbarid,resultsstringq);
		disList.addAll(disjunctiontoolbar);
		return disList;
	}
	
	public static void fillComboBox(JComboBox comboBox, String sql) {
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		List result = DBManager.queryForList(sql);
		for(int i = 0; i < result.size(); i++) {
    	
			if(((Map)result.get(i)).get("station_id") == null || ((Map)result.get(i)).get("station_name") ==null)
				continue;
			cnm=new CodeNameModel();
			cnm.setCode(((Map)result.get(i)).get("station_id").toString());
			cnm.setName(((Map)result.get(i)).get("station_name").toString());
			model.addElement(cnm);
		}
		comboBox.setModel(model);
	}
	
	public static void main(String[] args) {
		GUIUtil g=new GUIUtil();
		g.anaGUI();
		g.disposeguiMenu("1700071");
	}
}
