package com.tellhow.czp.util;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.rpc.ParameterMode;
import javax.xml.rpc.ServiceException;

import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.encoding.XMLType;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.app.service.OPEService;

import czprule.stationstartup.StationStartupManager;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

public class WebServiceUtil {
	public static Properties pro = new Properties();
	static {
		InputStream inputStream = null;
		try {
			inputStream = StationStartupManager.class
					.getResourceAsStream("/webservice.properties");
			pro.load(inputStream);
		} catch (IOException e) {
			e.printStackTrace();
		}
		finally {
			try {
				if(inputStream != null)
					inputStream.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}

	}
	/*
	public static List webServiceExecuteList(String url, String xml,
			String method, String param) {
		String endpoint = url;
		Service service = new Service();
		String result = "";
		Call call;
		Object[] object = new Object[1];

		object[0] = xml;// Object是用来存储方法的参数

		try {
			call = (Call) service.createCall();
			call.setTargetEndpointAddress(endpoint);// 远程调用路径
			call.setOperationName(method);// 调用的方法名

			// 设置参数名:
			call.addParameter(param, // 参数名
					XMLType.XSD_STRING,// 参数类型:String
					ParameterMode.IN);// 参数模式：'IN' or 'OUT'

			// 设置返回值类型：
			call.setReturnType(XMLType.XSD_STRING);// 返回值类型：String

			result = (String) call.invoke(object);// 远程调用
		} catch (ServiceException e) {
			e.printStackTrace();
		} catch (RemoteException e) {
			e.printStackTrace();
			
		}finally{
			if(result==null||result.equals("")){
				if(CBSystemConstants.isCurrentSys)
					ShowMessage.view("请保持OMS的连接");
				return null;
			}
		}
		
		List list = xmlToStr(result);
		return list;
	}
	*/
	public static List<Map<String, String>> xmlToStr(String arg) {
		/**
		 * 构造返回的xml。
		 * */
		Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding("GBK");
		Element datas=doc.addElement("Datas");
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		/**
		 * 解析传入的xml。
		 * */
		
		try {
			InputStream is = new ByteArrayInputStream(arg.getBytes("gbk"));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("data");
			//System.out.println(itemLists);
			for (int i = 0; i <itemLists.size(); i++) {
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				Map<String, String> mapInfo =new HashMap<String,String>();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), URLDecoder.decode(el.getTextTrim()));				
				}
				list.add(mapInfo);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	

	public static String webServiceExecute(String url, String xml,
			String method, String param) {
		String endpoint = url;
		Service service = new Service();
		String result = "";
		Call call;
		Object[] object = new Object[1];

		object[0] = xml;// Object是用来存储方法的参数

		try {
			call = (Call) service.createCall();
			call.setTargetEndpointAddress(endpoint);// 远程调用路径
			call.setOperationName(method);// 调用的方法名
			
			// 设置参数名:
			call.addParameter(param, // 参数名
					XMLType.XSD_STRING,// 参数类型:String
					ParameterMode.IN);// 参数模式：'IN' or 'OUT'

			// 设置返回值类型：
			call.setReturnType(XMLType.XSD_STRING);// 返回值类型：String

			result = (String) call.invoke(object);// 远程调用
		} catch (ServiceException e) {
			e.printStackTrace();
		} catch (RemoteException e) {
			e.printStackTrace();
			
		}finally{
			if(result==null||result.equals("")){
				if(CBSystemConstants.isCurrentSys)
					ShowMessage.view("请保持OMS的连接");
				return null;
			}
		}
		return result;
	}

	public static String getJKState(String zbid) {
		StringBuffer xmlData = new StringBuffer("");
		xmlData.append("<?xml version=\"1.0\" encoding=\"GBK\" ?>");
		xmlData.append("<Datas>");
		xmlData.append("   <ITEM>");
		xmlData.append("             <id>").append(zbid).append("</id>");
		xmlData.append("         </ITEM>");
		xmlData.append("      </Datas>");
		String xml = webServiceExecute(pro.getProperty("JK_STATE"), xmlData.toString(),
				"queryResource", "arg0");
		return getReqInfo(xml,"ITEM").get(0).get("state");
	}
/*	public static List<Map<String,String>> getYC(String statinID){
		List<Map> list = DBManager.queryForList("select m.aliasName from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m where t.station_id='"+statinID+"' and m.MemberOf_PSR=t.cim_id ");
		//System.out.println(list.size());
		StringBuffer xmlData = new StringBuffer("");
		xmlData.append("<?xml version=\"1.0\" encoding=\"GBK\" ?>");
		xmlData.append("<Datas>");
		for (Map map : list) {
			String pid=StringUtils.ObjToString(map.get("aliasName"));
			String dataType="0";

			xmlData.append("   <ITEM>");
			xmlData.append("             <datatype>").append(dataType).append("</datatype>");
			xmlData.append("             <pid>").append(pid).append("</pid>");
			
			xmlData.append("         </ITEM>");
		}
		xmlData.append("      </Datas>");
		String rs = webServiceExecute(pro.getProperty("SOCKET_URL"), xmlData.toString(), "getData", "xml");
	    List<Map<String, String>> ls = getReqInfo(rs, "ITEM");
	    //System.out.println(ls.size());
	    return ls;
	}
	
	public static List<Map<String,String>> getYX(String statinID){
		List<Map> list = DBManager.queryForList("select m.aliasName from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m where t.station_id='"+statinID+"' and m.MemberOf_PSR=t.cim_id ");
		//System.out.println(list.size());
		StringBuffer xmlData = new StringBuffer("");
		xmlData.append("<?xml version=\"1.0\" encoding=\"GBK\" ?>");
		xmlData.append("<Datas>");
		for (Map map : list) {
			String pid=StringUtils.ObjToString(map.get("aliasName"));
			String dataType="1";

			xmlData.append("   <ITEM>");
			xmlData.append("             <datatype>").append(dataType).append("</datatype>");
			xmlData.append("             <pid>").append(pid).append("</pid>");
			
			xmlData.append("         </ITEM>");
		}
		xmlData.append("      </Datas>");
		String rs = webServiceExecute(pro.getProperty("SOCKET_URL"), xmlData.toString(), "getData", "xml");
	    List<Map<String, String>> ls = getReqInfo(rs, "ITEM");
	    //System.out.println(ls.size());
	    return ls;
	}*/
	
	public static List<Map<String,String>> getYXFromDB(String stationID){
//		String sql = "select t.equip_id,decode(n.value,1,1,2,0) status from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YXVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=to_char(n.pid) and n.type!=3 union all select t.equip_id,decode(n.value,1,1,2,0) status from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YXVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR||'1'=t.cim_id and m.aliasName=to_char(n.pid) and n.type!=3 and t.cim_id like '%XC1'";
		//edit 2014.6.23
		List<Map<String,String>> list = DBManager.queryForList(OPEService.getService().getYXFromDB(stationID));
		return list;
	}
	
	public static List<Map<String,String>> getYCFromDB(String stationID){
//		String sql = "select to_char(n.pid) pid,round(n.value,1) value from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YCVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=to_char(n.pid)";
		//edit 2014.6.23
		List<Map<String,String>> list = DBManager.queryForList(OPEService.getService().getYCFromDB(stationID));
		return list;
	}
	
	public static Double getXLYGFromDB(String equipID){
		Double yg = null;
//		List<Map> list = DBManager.queryForList("select value from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YCVALUE n where m.measurementtype='MeasType-12' and t.equip_id='"+equipID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=to_char(n.pid)");
		//edit 2014.6.23
		List<Map> list = DBManager.queryForList(OPEService.getService().getXLYGFromDB(equipID));
		if(list.size() > 0) {
			yg = (Double)list.get(0).get("value");
		}
		return yg;
	}

	public static List<Map<String, String>> getReqInfo(String arg,String item) {
		List<Map<String, String>> voLists = new ArrayList<Map<String, String>>();

		try {
			InputStream is = new ByteArrayInputStream(arg.getBytes("gbk"));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			// 获取ITEM节点DOM
			List<Element> itemLists = root.elements(item);
			for (int i = 0; i < itemLists.size(); i++) {
				Map<String, String> mapInfo = new HashMap<String, String>();
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					// 将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());
				}
				voLists.add(mapInfo);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return voLists;
	}

	public static void main(String[] args) {
		String xml="<?xml version=\"1.0\" encoding=\"GBK\" ?><Datas>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>f5a3f4e4-2091-423b-957c-4a1b80b528b1</mxid>             <czsn>宝山站</czsn>             <czdw>宝山站</czdw>             <cznr>拉开门宝二2211开关</cznr>             <cardorder>1</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>399d667a-2020-4e0f-9da9-89d63ef0c85a</mxid>             <czsn>（张仪运维队）</czsn>             <czdw></czdw>             <cznr></cznr>             <cardorder>2</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>1fbf9538-40f6-466c-b570-841433c65e03</mxid>             <czsn></czsn>             <czdw></czdw>             <cznr></cznr>             <cardorder>3</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>4c9a6c11-f244-4db7-ae2b-90526712864f</mxid>             <czsn>门头沟站</czsn>             <czdw>门头沟站</czdw>             <cznr>拉开2262开关</cznr>             <cardorder>4</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>e1c1c261-9b52-4219-9955-58ec4fc97293</mxid>             <czsn>（冀北监控）</czsn>             <czdw>门头沟站</czdw>             <cznr>拉开2263-2刀闸</cznr>             <cardorder>5</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>8da5a584-434c-43d0-9347-a106bece321d</mxid>             <czsn></czsn>             <czdw>门头沟站</czdw>             <cznr>合上门宝二2263开关</cznr>             <cardorder>6</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>8da5a584-434c-43d0-9347-a106bece321d</mxid>             <czsn></czsn>             <czdw>门头沟站</czdw>             <cznr>（投退短引线保护操作许可）</cznr>             <cardorder>7</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>d5029d1d-8ebd-4f2f-9837-401c045fa8ac</mxid>             <czsn></czsn>             <czdw></czdw>             <cznr></cznr>             <cardorder>8</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>65497e82-ccf5-4b55-ab65-99fbd098376c</mxid>             <czsn>宝山站</czsn>             <czdw>宝山站</czdw>             <cznr>拉开门宝二2211-2刀闸</cznr>             <cardorder>9</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>4de79f13-a53c-4b79-97c7-ce38e608c170</mxid>             <czsn>（张仪运维队）</czsn>             <czdw>宝山站</czdw>             <cznr>合上门宝二2211-17接地刀闸</cznr>             <cardorder>10</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>06b83324-d227-4a40-9a30-2bea97b54bdc</mxid>             <czsn></czsn>             <czdw></czdw>             <cznr></cznr>             <cardorder>11</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>70398149-4897-4e70-ab6b-b0eaa199471a</mxid>             <czsn>门头沟站</czsn>             <czdw>门头沟站</czdw>             <cznr>合上2263-27接地刀闸</cznr>             <cardorder>12</cardorder>         </ITEM>   <ITEM>             <zbid>74de2a89-37e6-4ef6-b2a7-66740337baaf</zbid>             <czrw>停220kV门宝二线</czrw>             <username>管理员</username>             <mxid>20ccbfc9-aaa8-4d85-8540-57ba88241573</mxid>             <czsn>（冀北监控）</czsn>             <czdw></czdw>             <cznr></cznr>             <cardorder>13</cardorder>         </ITEM>      </Datas>";
		webServiceExecute(pro.getProperty("DD_SERVICE"), xml, "updateDaoZhaResource", "arg0");
	}
}
