package com.tellhow.czp.util;

import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;

import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.system.DBManager;


public class DesUtil {


    /**
     * 加密
     * @param data
     * @param sKey
     * @return
     */
    public static byte[] encrypt(byte[] data, String sKey) {
        try {
            byte[] key = sKey.getBytes();
            // 初始化向量
            IvParameterSpec iv = new IvParameterSpec(key);
            DESKeySpec desKey = new DESKeySpec(key);
            // 创建一个密匙工厂，然后用它把DESKeySpec转换成securekey
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            SecretKey securekey = keyFactory.generateSecret(desKey);
            // Cipher对象实际完成加密操作
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            // 用密匙初始化Cipher对象
            cipher.init(Cipher.ENCRYPT_MODE, securekey, iv);
            // 现在，获取数据并加密
            // 正式执行加密操作
            return cipher.doFinal(data);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解密
     * @param src
     * @param sKey
     * @return
     * @throws Exception
     */
    public static byte[] decrypt(byte[] src, String sKey) throws Exception {
        byte[] key = sKey.getBytes();
        // 初始化向量
        IvParameterSpec iv = new IvParameterSpec(key);
        // 创建一个DESKeySpec对象
        DESKeySpec desKey = new DESKeySpec(key);
        // 创建一个密匙工厂
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        // 将DESKeySpec对象转换成SecretKey对象
        SecretKey securekey = keyFactory.generateSecret(desKey);
        // Cipher对象实际完成解密操作
        Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        // 用密匙初始化Cipher对象
        cipher.init(Cipher.DECRYPT_MODE, securekey, iv);
        // 真正开始解密操作
        return cipher.doFinal(src);
    }

    /**
     * 将二进制转换成16进制
     *
     * @param buf
     * @return
     */
    public static String parseByte2HexStr(byte buf[]) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 将16进制转换为二进制
     *
     * @param hexStr
     * @return
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) return null;
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }
    
    
    /**
     * 加密
     * @param srcStr
     * @param charset
     * @param sKey
     * @return
     */
    public static String encrypt(String srcStr) {
    	String SKEY    = "ths@1998";
        byte[] src = srcStr.getBytes(CHARSET);
        byte[] buf = DesUtil.encrypt(src, SKEY);
        return DesUtil.parseByte2HexStr(buf);
    }

    /**
     * 解密
     *
     * @param hexStr
     * @param sKey
     * @return
     * @throws Exception
     */
    public static String decrypt(String hexStr) throws Exception {
    	String SKEY    = "ths@1998";
        byte[] src = DesUtil.parseHexStr2Byte(hexStr);
        byte[] buf = DesUtil.decrypt(src, SKEY);
        return new String(buf, CHARSET);
    }
    
    public static boolean isAuthCodeValid(String authcode)  {
    	boolean isValid = true;
    	try {
            String decryResult = DesUtil.decrypt(authcode);
   
            String[] arr = decryResult.split("@");
            String date2 = arr[1];
            
            String hard = DesUtil.decrypt(arr[0]);
            String[] hardarr = hard.split("@");
            String macAddress2=hardarr[0]; 
            String cpuSerial2=hardarr[1]; 
            
            Date date = new Date(); //获取当前的系统时间。
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            
            String date1 = "";
            List<Map> list = DBManager.queryForList("select to_char(sysdate,'yyyymmdd') dd from dual");
            if(list.size() > 0)
            	date1 = list.get(0).get("dd").toString();
            else
            	date1 = dateFormat.format(date);
            
            String macAddress1=HardwareUtil.getMACAddress(); 
            String cpuSerial1=HardwareUtil.getCPUSerial(); 
           
            
            if(!macAddress1.equals(macAddress2))
            	isValid = false;
            else if(!cpuSerial1.equals(cpuSerial2))
            	isValid = false;
            else if(date1.compareTo(date2) > 0)
            	isValid = false;
            
            if(!isValid) {
            	 
            	isValid = false;
            }
            
        } catch (Exception e1) {
            e1.printStackTrace();
            return false;
        }
    	return isValid;
    }

    private static final Charset CHARSET = Charset.forName("gb2312");

    public static void main(String[] args) {
        // 待加密内容
        String str = "dda@1313";
        String encryptResult = encrypt(str);
        System.out.println(encryptResult);
        // 直接将如上内容解密
        String decryResult = "";
        try {
            decryResult = decrypt("091B18E2B734280F0A7DB61CB400596541D16F17AD15005F49A66A1E517DCC3F58887747790FFC315D492F53700E2809BB1D91E61F3BA09E61109CF43A46AB45");
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        System.out.println(decryResult);
    }
}
