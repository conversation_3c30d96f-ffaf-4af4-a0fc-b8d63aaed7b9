package com.tellhow.czp.util;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @BelongProject : powernet-graphic-app-czpyndd
 * @BelongPackage : com.tellhow.czp.util
 * @Description : 应用上下文管理器
 * 用于在应用不同组件间共享数据的线程安全单例类
 * <AUTHOR> WangJQ
 * @Date : 2025/3/11 16:51
 */

public class ApplicationContext {
    // 使用volatile确保instance在所有线程中可见
    private static volatile ApplicationContext instance;

    // 使用ConcurrentHashMap保证线程安全
    private final ConcurrentHashMap<String, Object> contextData = new ConcurrentHashMap<String, Object>();

    // 私有构造函数防止外部实例化
    private ApplicationContext() {
        // 防止通过反射创建多个实例
        if (instance != null) {
            throw new RuntimeException("请使用getInstance()方法获取ApplicationContext实例");
        }
    }

    /**
     * 获取ApplicationContext单例实例
     * 使用双重检查锁定确保线程安全
     */
    public static ApplicationContext getInstance() {
        // 第一次检查（不加锁）
        if (instance == null) {
            // 同步锁
            synchronized (ApplicationContext.class) {
                // 第二次检查（加锁）
                if (instance == null) {
                    instance = new ApplicationContext();
                }
            }
        }
        return instance;
    }

    /**
     * 存储任意类型的数据
     * @param key 唯一标识符
     * @param value 要存储的值
     */
    public void set(String key, Object value) {
        if (key == null) {
            throw new IllegalArgumentException("Key不能为null");
        }
        if (value != null) {
            contextData.put(key, value);
        } else {
            // 如果值为null，则移除该键
            contextData.remove(key);
        }
    }

    /**
     * 获取存储的数据
     * @param key 唯一标识符
     * @param <T> 返回数据的类型
     * @return 存储的值，如果不存在则返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        return (T) contextData.get(key);
    }

    /**
     * 获取存储的数据，如果不存在则返回默认值
     * @param key 唯一标识符
     * @param defaultValue 默认值
     * @param <T> 返回数据的类型
     * @return 存储的值，如果不存在则返回默认值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, T defaultValue) {
        Object value = contextData.get(key);
        return value != null ? (T) value : defaultValue;
    }

    /**
     * 检查是否包含指定的键
     * @param key 要检查的键
     * @return 如果包含该键则返回true
     */
    public boolean containsKey(String key) {
        return contextData.containsKey(key);
    }

    /**
     * 移除指定的键值对
     * @param key 要移除的键
     * @return 被移除的值，如果键不存在则返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T remove(String key) {
        return (T) contextData.remove(key);
    }

    /**
     * 清空所有数据
     */
    public void clear() {
        contextData.clear();
    }

    /**
     * 获取所有键的集合（不可修改）
     * @return 键集合
     */
    public Set<String> keySet() {
        return Collections.unmodifiableSet(contextData.keySet());
    }

    /**
     * 获取键值对数量
     * @return 键值对数量
     */
    public int size() {
        return contextData.size();
    }

    // 针对常见数据类型的便捷方法

    /**
     * 获取字符串值
     */
    public String getString(String key) {
        return getString(key, null);
    }

    /**
     * 获取字符串值，如果不存在则返回默认值
     */
    public String getString(String key, String defaultValue) {
        Object value = contextData.get(key);
        return (value instanceof String) ? (String) value : defaultValue;
    }

    /**
     * 获取整数值
     */
    public Integer getInteger(String key) {
        return getInteger(key, null);
    }

    /**
     * 获取整数值，如果不存在则返回默认值
     */
    public Integer getInteger(String key, Integer defaultValue) {
        Object value = contextData.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof Number) {
            return ((Number) value).intValue();
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    /**
     * 获取布尔值
     */
    public Boolean getBoolean(String key) {
        return getBoolean(key, null);
    }

    /**
     * 获取布尔值，如果不存在则返回默认值
     */
    public Boolean getBoolean(String key, Boolean defaultValue) {
        Object value = contextData.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return defaultValue;
    }

    /**
     * 获取列表
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getList(String key) {
        Object value = contextData.get(key);
        return (value instanceof List) ? (List<T>) value : null;
    }

    /**
     * 获取Map
     */
    @SuppressWarnings("unchecked")
    public <K, V> Map<K, V> getMap(String key) {
        Object value = contextData.get(key);
        return (value instanceof Map) ? (Map<K, V>) value : null;
    }

    /**
     * 向List中添加元素
     * @param key 列表的键
     * @param item 要添加的元素
     * @param <T> 元素类型
     * @return 如果添加成功返回true
     */
    public <T> boolean addToList(String key, T item) {
        if (key == null || item == null) {
            return false;
        }

        synchronized (this) {  // 同步以确保线程安全
            List<T> list = getList(key);
            if (list == null) {
                list = new ArrayList<T>();
            } else {
                // 创建副本以避免潜在的并发修改问题
                list = new ArrayList<T>(list);
            }
            list.add(item);
            set(key, list);
            return true;
        }
    }

    /**
     * 向List中批量添加元素
     * @param key 列表的键
     * @param items 要添加的元素集合
     * @param <T> 元素类型
     * @return 如果添加成功返回true
     */
    public <T> boolean addAllToList(String key, Collection<? extends T> items) {
        if (key == null || items == null || items.isEmpty()) {
            return false;
        }

        synchronized (this) {
            List<T> list = getList(key);
            if (list == null) {
                list = new ArrayList<T>();
            } else {
                // 创建副本以避免潜在的并发修改问题
                list = new ArrayList<T>(list);
            }
            boolean added = list.addAll(items);
            if (added) {
                set(key, list);
            }
            return added;
        }
    }

    /**
     * 从List中移除元素
     * @param key 列表的键
     * @param item 要移除的元素
     * @param <T> 元素类型
     * @return 如果移除成功返回true
     */
    public <T> boolean removeFromList(String key, T item) {
        if (key == null || item == null) {
            return false;
        }

        synchronized (this) {
            List<T> list = getList(key);
            if (list == null || list.isEmpty()) {
                return false;
            }

            List<T> newList = new ArrayList<T>(list);
            boolean removed = newList.remove(item);
            if (removed) {
                set(key, newList);
            }
            return removed;
        }
    }

    /**
     * 从List中批量移除元素
     * @param key 列表的键
     * @param items 要移除的元素集合
     * @param <T> 元素类型
     * @return 如果至少有一个元素被移除则返回true
     */
    public <T> boolean removeAllFromList(String key, Collection<?> items) {
        if (key == null || items == null || items.isEmpty()) {
            return false;
        }

        synchronized (this) {
            List<T> list = getList(key);
            if (list == null || list.isEmpty()) {
                return false;
            }

            List<T> newList = new ArrayList<T>(list);
            boolean removed = newList.removeAll(items);
            if (removed) {
                set(key, newList);
            }
            return removed;
        }
    }

    /**
     * 按条件从List中移除元素
     * @param key 列表的键
     * @param predicate 判断条件
     * @param <T> 元素类型
     * @return 移除的元素数量
     */
    public <T> int removeFromListIf(String key, java.util.function.Predicate<T> predicate) {
        if (key == null || predicate == null) {
            return 0;
        }

        synchronized (this) {
            List<T> list = getList(key);
            if (list == null || list.isEmpty()) {
                return 0;
            }

            List<T> newList = new ArrayList<T>(list);
            int initialSize = newList.size();
            newList.removeIf(predicate);
            int removed = initialSize - newList.size();

            if (removed > 0) {
                set(key, newList);
            }
            return removed;
        }
    }

    /**
     * 保留List中指定的元素，移除其他元素
     * @param key 列表的键
     * @param items 要保留的元素集合
     * @param <T> 元素类型
     * @return 如果列表被修改则返回true
     */
    public <T> boolean retainAllInList(String key, Collection<?> items) {
        if (key == null || items == null) {
            return false;
        }

        synchronized (this) {
            List<T> list = getList(key);
            if (list == null || list.isEmpty()) {
                return false;
            }

            List<T> newList = new ArrayList<T>(list);
            boolean modified = newList.retainAll(items);
            if (modified) {
                set(key, newList);
            }
            return modified;
        }
    }

    /**
     * 向Map中添加键值对
     * @param mapKey Map的键
     * @param key 要添加的键
     * @param value 要添加的值
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 添加前该键关联的旧值，如果之前没有值则返回null
     */
    public <K, V> V putInMap(String mapKey, K key, V value) {
        if (mapKey == null || key == null) {
            return null;
        }

        synchronized (this) {
            Map<K, V> map = getMap(mapKey);
            if (map == null) {
                map = new HashMap<K, V>();
            } else {
                // 创建副本以避免潜在的并发修改问题
                map = new HashMap<K, V>(map);
            }

            V oldValue = map.put(key, value);
            set(mapKey, map);
            return oldValue;
        }
    }

    /**
     * 向Map中批量添加键值对
     * @param mapKey Map的键
     * @param entries 要添加的键值对Map
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 如果至少有一个键值对被添加则返回true
     */
    public <K, V> boolean putAllInMap(String mapKey, Map<? extends K, ? extends V> entries) {
        if (mapKey == null || entries == null || entries.isEmpty()) {
            return false;
        }

        synchronized (this) {
            Map<K, V> map = getMap(mapKey);
            if (map == null) {
                map = new HashMap<K, V>();
            } else {
                // 创建副本以避免潜在的并发修改问题
                map = new HashMap<K, V>(map);
            }

            int sizeBefore = map.size();
            map.putAll(entries);
            boolean modified = map.size() > sizeBefore;

            set(mapKey, map);
            return modified;
        }
    }

    /**
     * 从Map中移除键值对
     * @param mapKey Map的键
     * @param key 要移除的键
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 移除的值，如果键不存在则返回null
     */
    public <K, V> V removeFromMap(String mapKey, K key) {
        if (mapKey == null || key == null) {
            return null;
        }

        synchronized (this) {
            Map<K, V> map = getMap(mapKey);
            if (map == null || !map.containsKey(key)) {
                return null;
            }

            Map<K, V> newMap = new HashMap<K, V>(map);
            V removed = newMap.remove(key);
            set(mapKey, newMap);
            return removed;
        }
    }

    /**
     * 从Map中批量移除键
     * @param mapKey Map的键
     * @param keys 要移除的键集合
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 实际移除的键值对数量
     */
    public <K, V> int removeAllFromMap(String mapKey, Collection<?> keys) {
        if (mapKey == null || keys == null || keys.isEmpty()) {
            return 0;
        }

        synchronized (this) {
            Map<K, V> map = getMap(mapKey);
            if (map == null || map.isEmpty()) {
                return 0;
            }

            Map<K, V> newMap = new HashMap<K, V>(map);
            int removedCount = 0;

            for (Object key : keys) {
                if (newMap.remove(key) != null) {
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                set(mapKey, newMap);
            }
            return removedCount;
        }
    }

    /**
     * 获取列表的子列表（从指定位置开始到结束）
     * @param key 列表的键
     * @param fromIndex 起始索引（包含）
     * @param <T> 元素类型
     * @return 子列表，如果原列表不存在或索引无效则返回空列表
     */
    public <T> List<T> getSubList(String key, int fromIndex) {
        List<T> list = getList(key);
        if (list == null || fromIndex >= list.size()) {
            return Collections.emptyList();
        }
        return Collections.unmodifiableList(list.subList(fromIndex, list.size()));
    }

    /**
     * 获取列表的子列表（指定的索引范围）
     * @param key 列表的键
     * @param fromIndex 起始索引（包含）
     * @param toIndex 结束索引（不包含）
     * @param <T> 元素类型
     * @return 子列表，如果原列表不存在或索引无效则返回空列表
     */
    public <T> List<T> getSubList(String key, int fromIndex, int toIndex) {
        List<T> list = getList(key);
        if (list == null || fromIndex >= list.size() || fromIndex >= toIndex) {
            return Collections.emptyList();
        }
        // 确保toIndex不超出列表范围
        toIndex = Math.min(toIndex, list.size());
        return Collections.unmodifiableList(list.subList(fromIndex, toIndex));
    }

    /**
     * 使用自定义比较器对列表进行排序
     * @param key 列表的键
     * @param comparator 比较器
     * @param <T> 元素类型
     * @return 如果排序成功返回true
     */
    public <T> boolean sortList(String key, Comparator<? super T> comparator) {
        if (key == null || comparator == null) {
            return false;
        }

        synchronized (this) {
            List<T> list = getList(key);
            if (list == null || list.size() <= 1) {
                return false;
            }

            List<T> newList = new ArrayList<T>(list);
            newList.sort(comparator);
            set(key, newList);
            return true;
        }
    }

    /**
     * 过滤列表中的元素
     * @param key 列表的键
     * @param predicate 过滤条件
     * @param <T> 元素类型
     * @return 过滤后的新列表（不会修改原列表）
     */
    public <T> List<T> filterList(String key, java.util.function.Predicate<T> predicate) {
        if (key == null || predicate == null) {
            return Collections.emptyList();
        }

        List<T> list = getList(key);
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        /*return list.stream()
                .filter(predicate)
                .collect(java.util.stream.Collectors.toList());*/
        List<T> result = new ArrayList<T>();
        for (T item : list) {
            if (predicate.test(item)) {
                result.add(item);
            }
        }
        return result;
    }
}
