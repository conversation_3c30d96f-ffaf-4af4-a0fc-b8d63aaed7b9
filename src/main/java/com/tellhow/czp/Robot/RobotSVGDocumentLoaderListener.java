/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：
 * 功能说明 : 
 * 作    者 : 姚星乐
 * 开发日期 : 2012-12-24
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.Robot;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import org.apache.batik.swing.svg.SVGDocumentLoaderAdapter;
import org.apache.batik.swing.svg.SVGDocumentLoaderEvent;
import org.w3c.dom.CDATASection;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.events.EventTarget;
import org.w3c.dom.svg.SVGDocument;

import com.tellhow.graphicframework.basic.SVGLayer;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;

public class RobotSVGDocumentLoaderListener extends SVGDocumentLoaderAdapter{
	private SVGCanvasPanel panel;
	
	
	
	public RobotSVGDocumentLoaderListener(SVGCanvasPanel svgPanel) {
		this.panel = svgPanel;
	}

	@Override
	public void documentLoadingStarted(SVGDocumentLoaderEvent e) {
    }

    @Override
	public void documentLoadingCompleted(SVGDocumentLoaderEvent e) {
		SVGDocument doc = e.getSVGDocument();
		//设置电压等级对应颜色
		
		Element styleElement = (Element)doc.getElementsByTagName("style").item(0);
    	while(styleElement.hasChildNodes())
    		styleElement.removeChild(styleElement.getFirstChild());
    	String cData = "\r\n";
    	for(Map.Entry<String, String> entry : SystemConstants.getMapColor().entrySet()) {
    		String volCode= entry.getKey();
    		String volValue= entry.getValue();
    		String volStyle = ".kV"+volCode+"{stroke:"+volValue+";fill:none}\r\n";
    		cData += volStyle;
    	}
    	CDATASection cdataSection = doc.createCDATASection(cData);
    	styleElement.appendChild(cdataSection);
		
		Element svgElement = SVGDocumentResolver.getResolver().resolveSvgElement(doc);
		
		Element headElement = doc.getElementById("Head_Layer");
		if(headElement != null) {
			NodeList rectList = headElement.getElementsByTagName("rect");
			for(int i = 0; i < rectList.getLength(); i++) {
				Element rectElement = (Element)rectList.item(i);
				if(rectElement.hasAttribute("fill") && 
						!rectElement.getAttribute("rect").toLowerCase().equals("rgb(0,0,0)"))
					rectElement.setAttribute("fill", "rgb(0,0,0)");
			}
		}
		
		final String mapType = SVGDocumentResolver.getResolver().getMapType(doc);
		if (mapType.equals(SystemConstants.MAP_TYPE_SYS)) {
			processAllMap(svgElement);
		} else if (mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
			processStationMap(svgElement);
		}
		initDeviceAction(mapType);
		
		 List list = DBManager
					.queryForList("select a.station_id,b.* from "+CBSystemConstants.opcardUser+"t_e_equipinfo a,"+CBSystemConstants.opcardUser+"t_a_czpactionstate b  where cardid='13320' and b.equipid=a.equip_id");

			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map =(Map<String, Object>) list.get(i);
				PowerDevice pd = (PowerDevice)

				CBSystemConstants.getPowerDevice(String.valueOf(map.get

				("STATION_ID")), String.valueOf(map.get("EQUIPID")));
				if (pd != null) {
					pd.setDeviceStatus

					(String.valueOf(map.get("BEGINSTATUS")));

					DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
				}

			}
			
    }
    
  
    private void processAllMap(Element svgElement) {
		runRealTimeDataTask();
	}

	private void processStationMap(Element svgElement) {
		//处理开关
		NodeList symbolList = svgElement.getOwnerDocument().getElementsByTagName("symbol");
    	for(int i = 0; i < symbolList.getLength(); i++) {
	    	Element symbol = (Element)symbolList.item(i);
	    	if(symbol.getAttribute("id").toLowerCase().indexOf("breaker:") == 0 ||
	    			symbol.getAttribute("id").toLowerCase().indexOf("dollybreaker:") == 0) {
		    	for(int j = 0; j < symbol.getChildNodes().getLength(); j++) {
					Node node = (Node)symbol.getChildNodes().item(j);
					if(node.getNodeName().equals("rect")) {
						if(node.getAttributes().getNamedItem("fill") != null)
							node.getAttributes().removeNamedItem("fill");
					}
		    	}
	    	}
    	}
    	
		String stationID = SVGDocumentResolver.getResolver().getStationID(svgElement.getOwnerDocument());
		runRealTimeDataTask();
//		DeviceSVGPanelUtil.ChangeDeviceSVGSize(panel.getSvgCanvas(), -1, 0.2);
    	Map mapDevice=CBSystemConstants.getStationPowerDevices(stationID);
    	PowerDevice pd=null;
    	for (Iterator iter = mapDevice.values().iterator(); iter.hasNext();) {
    		pd=(PowerDevice) iter.next();
    		if(pd==null)
    			continue;
    		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);  //执行图形外观变更
		}
	}

	/**
	 * 用 途: 初始化SVG画板事件处理接口
	 * 
	 * @返回值：无
	 */
	private void initDeviceAction(String mapType) {
		SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		Element svgElement = resolver.resolveSvgElement(panel.getSvgDocument());
		for(int i = 0; i < svgElement.getChildNodes().getLength(); i++) {
			Node node1 = (Node)svgElement.getChildNodes().item(i);
			if(node1.getNodeName().equals("#text"))
				continue;
			else if(node1.getNodeName().equals("defs"))
				continue;
			else {
				Element layerElement = (Element)node1;
				String layerID = layerElement.getAttribute("id");
				if(layerID.equals(""))
					continue;
				else if(!SystemConstants.getMapSVGLayer().containsKey(layerID)) {
					SVGLayer layer = new SVGLayer(layerID);
					SystemConstants.putMapSVGLayer(layer);
				}
				SVGLayer layer = SystemConstants.getMapSVGLayer().get(layerID);
				if(!layer.isVisiable())
					layerElement.setAttribute("style", "display:none");
				if(layer.isHandleEvent()) {
					for(int j = 0; j < layerElement.getChildNodes().getLength(); j++) {
						Node node2 = layerElement.getChildNodes().item(j);
						if(node2.getNodeName().equals("#text"))
							continue;
						Element element = (Element)node2;
						if(layerID.equals(resolver.getHead_Layer())) { //背景响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent.addEventListener("click", panel, false);
						}
						else if(layerID.equals(resolver.getSubstation_Layer())) { //厂站层的节点都响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent.addEventListener("click", panel, false);
							elementEvent.addEventListener("mouseover", panel, false);
							elementEvent.addEventListener("mouseout", panel, false);
						}
						else if(!element.getAttribute("href").equals("")) { //有超链接的节点都响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent.addEventListener("click", panel, false);
							elementEvent.addEventListener("mouseover", panel, false);
							elementEvent.addEventListener("mouseout", panel, false);
						}
						else if(!resolver.getDeviceID(element).equals("")) { //有ID的节点都响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent.addEventListener("click", panel, false);
							elementEvent.addEventListener("mouseover", panel, false);
							elementEvent.addEventListener("mouseout", panel, false);
						}
						else
							element.setAttribute("pointer-events", "none");
					}
				}
				else
					layerElement.setAttribute("pointer-events", "none");
			}
		}
	}
	
	public void runRealTimeDataTask()
    {
    	for (Iterator iter = SystemConstants.getMapSVGLayer().values().iterator();iter.hasNext();) 
    	{ 
    		final SVGLayer layer = (SVGLayer) iter.next();
    		if(layer.isVisiable() && layer.isRefresh()) {
		    	TimerTask task = new TimerTask() { 
		    		public void run() { 
		    			executeTask(layer.getLayerID());
		    			} 
		    	}; 
			    Timer timer = new Timer(); 
			    timer.schedule(task, 0, layer.getRefreshInterval()*1000);
    		}
		 }
    }
	
	public void executeTask(final String layerID)
	{
		final SVGCanvas fSvgCanvas = panel.getSvgCanvas();
		Runnable r = new Runnable() {
	        public void run() {
	        	if(fSvgCanvas.getSVGDocument() != null) {
	        		SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
	        		String stationID = resolver.getStationID(fSvgCanvas.getSVGDocument());
					showMeasurementValue(fSvgCanvas.getSVGDocument(), stationID, layerID);
	        	}
	        }
		};
	    if(fSvgCanvas.getUpdateManager() != null)
	    	fSvgCanvas.getUpdateManager().getUpdateRunnableQueue().invokeLater(r);
	    else
	    	r.run();
	}
	
	public void showMeasurementValue(SVGDocument svgDoc, String stationID, String layerID) {
		Map<String, Map> telemeteringMap = CreatePowerStationToplogy.buildTelemetering(stationID,"1");
		SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		Element measurementLayer = svgDoc.getElementById(layerID);
		NodeList svgDevNodeList = measurementLayer.getElementsByTagName("g");
		for (int i = 0; i < svgDevNodeList.getLength(); i++) {
			Element element = (Element) svgDevNodeList.item(i);
			String deviceID = resolver.getDeviceCIMID(element);
			if (deviceID.equals(""))
				continue;
			if(telemeteringMap.containsKey(deviceID)) {
				Map map = telemeteringMap.get(deviceID);
				if (map.get("MEASVALUE") != null) {
					String telemeteringValue = map.get("MEASVALUE").toString();
					resolver.setTelemeteringValue(element, telemeteringValue);
				}
			}
		}
	} 
}
