/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：
 * 功能说明 : 
 * 作    者 : 姚星乐
 * 开发日期 : 2012-12-19
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.Robot;

import java.awt.AWTException;
import java.awt.Robot;
import java.awt.event.InputEvent;
import java.awt.event.MouseEvent;
import java.awt.geom.Point2D;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;
import javax.swing.JSplitPane;

import org.w3c.dom.Element;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.mainframe.WindowSplash;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.DOMUtil;

import czprule.model.OperationInfo;
import czprule.model.PowerDevice;
import czprule.securitycheck.view.CheckWord;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;

public class CzpRobot {
	private  SVGCanvasPanel panel;
	public  void doInversionIng(SVGCanvasPanel panel)
	{
		
	}
	
	public void doInversion(String cardid)
	{
		try {
			
		List list = DBManager.queryForList("select b.station_id,b.equip_id from "+CBSystemConstants.opcardUser+"t_a_czpzb a,"+CBSystemConstants.opcardUser+"t_e_equipinfo b where a.equipid=b.equip_id and a.zbid='"+cardid+"'" );
		
		if(list.size() == 0) {
			List list2 = DBManager.queryForList("select b.czdw,a.czrw from "+CBSystemConstants.opcardUser+"t_a_czpzb a,"+CBSystemConstants.opcardUser+"t_a_czpmx b where a.zbid=b.f_zbid and b.cardorder=1 and a.zbid='"+cardid+"'");
			if(list2.size() > 0) {
				Map map = (Map)list2.get(0);
				String czdw = map.get("czdw").toString();
				String czrw = map.get("czrw").toString();
				String stationID = QueryDeviceDao.getStationIDByName(czdw);
				if(!stationID.equals("")) {
					Map map2 = new HashMap();
					map2.put("station_id", stationID);
					map2.put("equip_id", "");
					list.add(map2);
				}
			}
		}
		
		if(list.size() > 0) {
			Map map = (Map)list.get(0);
			String stationID = map.get("station_id").toString();
			String equipID = map.get("equip_id").toString();
			CreatePowerStationToplogy.loadFacData(stationID);
			PowerDevice pd=CBSystemConstants.getPowerDevice(stationID, equipID);
			if(pd != null && pd.getDeviceType().equals(SystemConstants.InOutLine)) {
				Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineByLine(pd);
				for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
					PowerDevice dev=(PowerDevice)iterator.next();
					CreatePowerStationToplogy.loadFacData(dev.getPowerStationID());
				}
			}
			
			String sql = "select t6.equipid,t6.beginstatus,t6.endstate,t7.station_id,t8.station_name from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t6 ,"+CBSystemConstants.opcardUser+"t_e_equipinfo t7 ,"+CBSystemConstants.opcardUser+"t_e_substation t8"
				+ " where t6.equipid=t7.equip_id and t7.station_id=t8.station_id and t6.cardid='"+cardid+"'  order by t6.stateorder asc";
			List devMap = new ArrayList();
			list = DBManager.query(sql);
	
			// 打开或激活电站
			for (int i = 0; i < list.size(); i++) {
				Map li = (Map) list.get(i);
				PowerDevice dev=CBSystemConstants.getPowerDevice((String) li.get("station_id"), (String) li.get("equipid"));
				if(devMap.contains(dev.getPowerDeviceID()))
					continue;
				String status = String.valueOf(li.get("BEGINSTATUS"));
				dev.setDeviceStatus(status);
				devMap.add(dev.getPowerDeviceID());
			}
			
			//RobotSvgUtil.openSVGPanel(stationID,equipID);
			DeviceSVGPanelUtil.openSVGPanel(stationID);
			if(pd != null && pd.getDeviceType().equals(SystemConstants.InOutLine)) {
				Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineByLine(pd);
				for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
					PowerDevice dev=(PowerDevice)iterator.next();
					DeviceSVGPanelUtil.openSVGPanel(dev.getPowerStationID(), dev.getPowerDeviceID());
				}
			}
			
			TicketDBManager tdb = new TicketDBManager();
			String[] zb =tdb.queryTicketZB(cardid);
			List<BaseCardModel> mx = tdb.queryTicketMX(cardid);
			InversionTicket ttk=InversionTicket.getInstance();
	    	
	    	ttk.init(zb,mx);
	    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(0.57);
			splitPane.setRightComponent(ttk);
		}
		

		
//			PowerDevice dev=(PowerDevice)CBSystemConstants.getPowerDevice(ci.getPowerStationId(),ci.getPowerDeviceid());
//			Robot robot;
//			Thread.currentThread().sleep(2000);
//			robot = new Robot();
//			robot.setAutoDelay(2000);
//			robot.mouseMove(931, 182);
//			robot.keyPress(InputEvent.BUTTON2_MASK);
			
			/**
			Point2D.Double point = DOMUtil.getElementStartPoint(SVGDocumentResolver.getResolver().getDeviceGraphElement(dev));
			SVGCanvas svgCanvas=SystemConstants.getGuiBuilder().getActivateSVGPanel().getSvgCanvas();
			double svgWidth = 0,svgHeight = 0,scrollXScale = 0,scrollYScale = 0;
	    	double devPosX = 0,devPosY = 0;
	    	int scrollVertValue = 0,scrollHorizValue = 0;
	    	String posStr = "";
	    	
	    	Element svgElement = SVGDocumentResolver.getResolver().getDeviceGraphElement(dev);
	    	
	    	//-----------------获取当前设备的坐标位置------------------------------------
	    	if (svgElement.hasAttribute("x"))
	    	{
	    		devPosX = Double.valueOf(svgElement.getAttribute("x"));
	    		devPosY = Double.valueOf(svgElement.getAttribute("y"));
	    	}
	    	else if(svgElement.getNodeName().equals("path"))   //当前设备是线路则不具有transform属性，此时需取出path的前两位作为坐标
	    	{
				posStr = svgElement.getAttributes().getNamedItem("d").getNodeValue().replace("M", "").trim().replace(" ", ",");
				devPosX = Double.valueOf(posStr.split(",")[0]).intValue();
				devPosY = Double.valueOf(posStr.split(",")[1]).intValue();
	    	}
	    	
			//----------------获取当前接线图的原始大小及与当前设备的------------------------------------
			svgWidth = svgCanvas.getInitWidth();
			svgHeight = svgCanvas.getInitHeight();
			
			scrollXScale = devPosX / svgWidth;
			scrollYScale = devPosY / svgHeight;
			//-------------实现自动滚动条定位-----------------------
			scrollVertValue =  (new Double(scrollYScale * svgCanvas.getSVGScrollPane().getVertScrollBar().getMaximum()).intValue()) - 100;
			scrollHorizValue = (new Double(scrollXScale * svgCanvas.getSVGScrollPane().getHorizScrollBar().getMaximum()).intValue()) - 100;
			svgCanvas.getSVGScrollPane().getVertScrollBar().setValue(scrollVertValue);
			svgCanvas.getSVGScrollPane().getHorizScrollBar().setValue(scrollHorizValue);

			if(point==null){
	    		return;
	    	}
	    	
	    	robot.mouseMove((int)point.getX(), (int)point.getY());
	    	robot.mousePress(InputEvent.BUTTON2_MASK);
	    	**/
			
		} catch (Exception  e) {
			// TODO Auto-generated catch block
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), e.getMessage(), "错误异常信息!", JOptionPane.ERROR_MESSAGE);
			e.printStackTrace();
		}
		
		
		
	}
	
	
	//加载修改分析类
	public void doCardAlert(String cardid)
	{
		try {
			
		List list = DBManager.queryForList("select b.station_id,b.equip_id from "+CBSystemConstants.opcardUser+"t_a_czpzb a,"+CBSystemConstants.opcardUser+"t_e_equipinfo b where a.equipid=b.equip_id and a.zbid='"+cardid+"'" );
		
		if(list.size() == 0) {
			List list2 = DBManager.queryForList("select b.czdw,a.czrw from "+CBSystemConstants.opcardUser+"t_a_czpzb a,"+CBSystemConstants.opcardUser+"t_a_czpmx b where a.zbid=b.f_zbid and b.cardorder=1 and a.zbid='"+cardid+"'");
			if(list2.size() > 0) {
				Map map = (Map)list2.get(0);
				String czdw = map.get("czdw").toString();
				String czrw = map.get("czrw").toString();
				String stationID = QueryDeviceDao.getStationIDByName(czdw);
				if(!stationID.equals("")) {
					Map map2 = new HashMap();
					map2.put("station_id", stationID);
					map2.put("equip_id", "");
					list.add(map2);
				}
			}
		}
		
		if(list.size() > 0) {
			Map map = (Map)list.get(0);
			String stationID = map.get("station_id").toString();
			String equipID = map.get("equip_id").toString();
			CreatePowerStationToplogy.loadFacData(stationID);
			PowerDevice pd=CBSystemConstants.getPowerDevice(stationID, equipID);
			
			
//			new DeviceOperate().execute(pd, pd.getDeviceStatus());
			
			
			if(pd != null && pd.getDeviceType().equals(SystemConstants.InOutLine)) {
				Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineByLine(pd);
				for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
					PowerDevice dev=(PowerDevice)iterator.next();
					CreatePowerStationToplogy.loadFacData(dev.getPowerStationID());
				}
			}
			 
			String sql = "select t6.equipid,t6.beginstatus,t6.endstate,t7.station_id,t8.station_name from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t6 ,"+CBSystemConstants.opcardUser+"t_e_equipinfo t7 ,"+CBSystemConstants.opcardUser+"t_e_substation t8"
				+ " where t6.equipid=t7.equip_id and t7.station_id=t8.station_id and t6.cardid='"+cardid+"'  order by t6.stateorder asc";
			List devMap = new ArrayList();
			list = DBManager.query(sql);
	
			// 打开或激活电站
			for (int i = 0; i < list.size(); i++) {
				Map li = (Map) list.get(i);
				PowerDevice dev=CBSystemConstants.getPowerDevice((String) li.get("station_id"), (String) li.get("equipid"));
				if(devMap.contains(dev.getPowerDeviceID()))
					continue;
				String status = String.valueOf(li.get("BEGINSTATUS"));
				dev.setDeviceStatus(status);
				devMap.add(dev.getPowerDeviceID());
			}
			
			//RobotSvgUtil.openSVGPanel(stationID,equipID);
			DeviceSVGPanelUtil.openSVGPanel(stationID);
			if(pd != null && pd.getDeviceType().equals(SystemConstants.InOutLine)) {
				Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineByLine(pd);
				for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
					PowerDevice dev=(PowerDevice)iterator.next();
					DeviceSVGPanelUtil.openSVGPanel(dev.getPowerStationID(), dev.getPowerDeviceID());
				}
			}
			
			TicketDBManager tdb = new TicketDBManager();
			String[] zb =tdb.queryTicketZB(cardid);
			List<BaseCardModel> mx = tdb.queryTicketMX(cardid);
			TempTicket ttk=TempTicket.getInstance();
	    	//ttk.init(zb,mx);
	    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(0.57);
			splitPane.setRightComponent(ttk);
		
		}
		} catch (Exception  e) {
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), e.getMessage(), "错误异常信息!", JOptionPane.ERROR_MESSAGE);
			e.printStackTrace();
		}
	}
	
	public void disposeCZP(String cardid){
		List list = DBManager.queryForList("select b.station_id,b.equip_id from "+CBSystemConstants.opcardUser+"t_a_czpzb a,"+CBSystemConstants.opcardUser+"t_e_equipinfo b where a.equipid=b.equip_id and a.zbid='"+cardid+"'" );
		if(list.size()==0){
			
		}else{
			Map map = (Map)list.get(0);
			String stationID = map.get("station_id").toString();
			String equipID = map.get("equip_id").toString();
			CreatePowerStationToplogy.loadFacData(stationID);
			PowerDevice pd=CBSystemConstants.getPowerDevice(stationID, equipID);
			if(pd != null && pd.getDeviceType().equals(SystemConstants.InOutLine)) {
				Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineByLine(pd);
				for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
					PowerDevice dev=(PowerDevice)iterator.next();
					CreatePowerStationToplogy.loadFacData(dev.getPowerStationID());
				}
			}
			DeviceSVGPanelUtil.openSVGPanel(stationID);
			TicketDBManager tdb = new TicketDBManager();
			String[] zb =tdb.queryTicketZB(cardid);
			List<BaseCardModel> mx = tdb.queryTicketMX(cardid);
			TempTicket ttk=TempTicket.getInstance();
	    	//ttk.init(zb,mx);
	    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(0.57);
			splitPane.setRightComponent(ttk);
		}
	}
}
