/*
 * TempTicket.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.Robot;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultCellEditor;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.service.OperationCheckDefault;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;
import javax.swing.JButton;
import javax.swing.GroupLayout;
import javax.swing.GroupLayout.Alignment;
import javax.swing.LayoutStyle.ComponentPlacement;
import javax.swing.ImageIcon;

/**
 *
 * <AUTHOR>
 */
public class InversionTicket extends javax.swing.JPanel {

	public static InversionTicket tempTicket = null;

	public static synchronized InversionTicket getInstance() {
		if (tempTicket == null)
			tempTicket = new InversionTicket();
		return tempTicket;
	}

	/** Creates new form TempTicket */
	private InversionTicket() {
		initComponents(); 
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jLabel1 = new javax.swing.JLabel();
		jLabel2 = new javax.swing.JLabel();
		jLabel3 = new javax.swing.JLabel();
		jLabel4 = new javax.swing.JLabel();
		jLabel5 = new javax.swing.JLabel();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTextArea1 = new javax.swing.JTextArea();
		jScrollPane2 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();
		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jButton3 = new javax.swing.JButton();
		jButton4 = new javax.swing.JButton();
		jButton5 = new javax.swing.JButton();
		jButton6 = new javax.swing.JButton();
		jButton7 = new javax.swing.JButton();
		jButton8 = new javax.swing.JButton();
		jButton9 = new javax.swing.JButton();
		jButton3.setVisible(false);
		jButton4.setVisible(false);
		jButton1.setVisible(false);
		jButton2.setVisible(false);
		jButton7.setVisible(false);
		jButton8.setVisible(false);

		jLabel1.setText("\u62df \u7968 \u4eba \uff1a");

		jLabel2.setText("\u62df\u7968\u65f6\u95f4\uff1a");

		jLabel3.setFont(new java.awt.Font("微软雅黑", 1, 13));
		jLabel3.setText("jLabel3");

		jLabel4.setFont(new java.awt.Font("微软雅黑", 1, 13));
		jLabel4.setText("jLabel4");

		jLabel5.setText("\u64cd\u4f5c\u4efb\u52a1\uff1a");

		jTextArea1.setColumns(20);
		jTextArea1.setFont(new java.awt.Font("宋体", 1, 14));
		jTextArea1.setLineWrap(true);
		jTextArea1.setEditable(false);
		jScrollPane1.setViewportView(jTextArea1);

		jTable1.setFont(new java.awt.Font("宋体", 0, 13));
		jTable1.setRowHeight(26);
		jTable1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
            	String czmx = jTable1.getValueAt(jTable1.getSelectedRow(), 2).toString();
            	String station = jTable1.getValueAt(jTable1.getSelectedRow(), 1).toString();
            	//List<RuleBaseMode> rbmList=OperationCheckDefault.execute(station, czmx);
        		//OperationCheckDefault.inverse(rbmList);
            	//JudgeDoWhat jdw = new JudgeDoWhat();
            	//jdw.excute(czmx, jTextArea1.getText(), String.valueOf(jTable1.getSelectedRow()+1), station, InversionTicket.this);
            }
        });
		
		jScrollPane2.setViewportView(jTable1);

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/add.png"))); // NOI18N
		jButton1.setToolTipText("\u589e\u52a0");
		jButton1.setBorder(null);
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton2.setToolTipText("\u5220\u9664");
		jButton2.setBorder(null);
		jButton2.setFocusPainted(false);
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/btn_up.png"))); // NOI18N
		jButton3.setToolTipText("\u4e0a\u79fb");
		jButton3.setBorder(null);
		jButton3.setFocusPainted(false);
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/btn_down.png"))); // NOI18N
		jButton4.setToolTipText("\u4e0b\u79fb");
		jButton4.setBorder(null);
		jButton4.setFocusPainted(false);
		jButton4.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton4ActionPerformed(evt);
			}
		});
		
		jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/save.png"))); // NOI18N
		jButton5.setToolTipText("\u4fdd\u5b58");
		jButton5.setBorder(null);
		jButton5.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton5ActionPerformed(evt);
			}
		});

		jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png"))); // NOI18N
		jButton6.setToolTipText("\u53d6\u6d88");
		jButton6.setBorder(null);
		jButton6.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton6ActionPerformed(evt);
			}
		});
		
		jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource(
			"/tellhow/btnIcon/merge.png"))); // NOI18N
		jButton7.setToolTipText("合项");
		jButton7.setBorder(null);
		jButton7.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton7ActionPerformed(evt);
			}
		});

		jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource(
			"/tellhow/btnIcon/split.png"))); // NOI18N
		jButton8.setToolTipText("分项");
		jButton8.setBorder(null);
		jButton8.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton8ActionPerformed(evt);
			}
		});
		
		jButton9.setIcon(new javax.swing.ImageIcon(getClass().getResource(
			"/tellhow/btnIcon/play.png"))); // NOI18N
		jButton9.setText("自动演示");
		jButton9.setToolTipText("自动演示");
		jButton9.setMargin(new java.awt.Insets(1,1,1,1));
		jButton9.setFocusPainted(false);
		jButton9.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton9ActionPerformed(evt);
			}
		});
		
		jButton10 = new JButton("下一步");
		jButton10.setToolTipText("下一步");
		jButton10.setMargin(new java.awt.Insets(1,1,1,1));
		jButton10.setFocusPainted(false);
		jButton10.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton10ActionPerformed(evt);
			}
		});
		
		jButton11 = new JButton("\u4E0A\u4E00\u6B65");
		jButton11 = new JButton("上一步");
		jButton11.setToolTipText("上一步");
		jButton11.setMargin(new java.awt.Insets(1,1,1,1));
		jButton11.setFocusPainted(false);
		jButton11.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton11ActionPerformed(evt);
			}
		});


		GroupLayout layout = new GroupLayout(
						this);
		layout.setHorizontalGroup(
			layout.createParallelGroup(Alignment.TRAILING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap(382, Short.MAX_VALUE)
					.addComponent(jButton5)
					.addPreferredGap(ComponentPlacement.UNRELATED)
					.addComponent(jButton6)
					.addGap(24))
				.addGroup(layout.createSequentialGroup()
					.addGroup(layout.createParallelGroup(Alignment.TRAILING)
						.addComponent(jScrollPane2, Alignment.LEADING, GroupLayout.DEFAULT_SIZE, 440, Short.MAX_VALUE)
						.addGroup(layout.createSequentialGroup()
							.addContainerGap()
							.addGroup(layout.createParallelGroup(Alignment.TRAILING)
								.addGroup(layout.createSequentialGroup()
									.addGroup(layout.createParallelGroup(Alignment.LEADING, false)
										.addComponent(jLabel1, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
										.addComponent(jLabel5, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
									.addGroup(layout.createParallelGroup(Alignment.TRAILING)
										.addGroup(layout.createSequentialGroup()
											.addGap(7)
											.addComponent(jLabel4, GroupLayout.DEFAULT_SIZE, 106, Short.MAX_VALUE)
											.addPreferredGap(ComponentPlacement.RELATED)
											.addComponent(jLabel2)
											.addPreferredGap(ComponentPlacement.RELATED)
											.addComponent(jLabel3, GroupLayout.PREFERRED_SIZE, 179, GroupLayout.PREFERRED_SIZE))
										.addGroup(layout.createSequentialGroup()
											.addPreferredGap(ComponentPlacement.RELATED)
											.addComponent(jScrollPane1, GroupLayout.DEFAULT_SIZE, 360, Short.MAX_VALUE))))
								.addGroup(layout.createSequentialGroup()
									.addComponent(jButton1)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jButton2)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jButton3)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jButton4)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jButton7)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jButton8)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jButton11)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jButton10)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jButton9)
									.addGap(8)))))
					.addContainerGap())
		);
		layout.setVerticalGroup(
			layout.createParallelGroup(Alignment.LEADING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap()
					.addGroup(layout.createParallelGroup(Alignment.BASELINE)
						.addComponent(jLabel1, GroupLayout.PREFERRED_SIZE, 27, GroupLayout.PREFERRED_SIZE)
						.addComponent(jLabel4)
						.addComponent(jLabel2)
						.addComponent(jLabel3))
					.addPreferredGap(ComponentPlacement.RELATED)
					.addGroup(layout.createParallelGroup(Alignment.LEADING)
						.addGroup(layout.createSequentialGroup()
							.addComponent(jScrollPane1, GroupLayout.PREFERRED_SIZE, 58, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addGroup(layout.createParallelGroup(Alignment.LEADING)
								.addComponent(jButton8)
								.addGroup(layout.createParallelGroup(Alignment.BASELINE)
									.addComponent(jButton4)
									.addComponent(jButton7)
									.addComponent(jButton9)
									.addComponent(jButton3)
									.addComponent(jButton2)
									.addComponent(jButton1)
									.addComponent(jButton10)
									.addComponent(jButton11))))
						.addComponent(jLabel5, GroupLayout.PREFERRED_SIZE, 27, GroupLayout.PREFERRED_SIZE))
					.addPreferredGap(ComponentPlacement.RELATED)
					.addComponent(jScrollPane2, GroupLayout.DEFAULT_SIZE, 132, Short.MAX_VALUE)
					.addPreferredGap(ComponentPlacement.RELATED)
					.addGroup(layout.createParallelGroup(Alignment.BASELINE)
						.addComponent(jButton6)
						.addComponent(jButton5))
					.addContainerGap())
		);
		this.setLayout(layout);

		jButton1.getAccessibleContext().setAccessibleDescription("");
	}// </editor-fold>
		//GEN-END:initComponents


	//保存
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		close ();
	}
	
	//取消
	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		//回滚

		DeviceOperate.RollbackDeviceStatus();
		//清空
		DeviceOperate.ClearDevMap();
		Rollback();
		close ();
		flag=true;
	}
	private void Rollback(){
		for (int i = Reversecard.Rollbackdiv.values().size(); i >=1; i--) { 
			String key = String.valueOf(i);
			if(!((HashMap)Reversecard.Rollbackdiv).containsKey(key))
				continue;
			HashMap map = (HashMap)((HashMap)Reversecard.Rollbackdiv).get(key);
			Iterator iter = map.entrySet().iterator(); 
			while (iter.hasNext()) { 
			    Map.Entry entry = (Map.Entry) iter.next(); 
			    PowerDevice pd = (PowerDevice)entry.getKey(); 
			    String status = (String)entry.getValue(); 
			    pd.setDeviceStatus(status);
			    DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
			} 
		}
	}
	public void close () {
		Reversecard.Rollbackdiv.clear();
		this.setVisible(false);
		this.tempTicket = null;
	}
	
	//演示
	private void jButton9ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		//Rollback();
		
//		DeviceOperate.RollbackDeviceStatus();
//		DeviceOperate.ClearDevMap();
//		CBSystemConstants.getDtdMap().clear();
		if(jButton9.getText().equals("自动演示")) {
			jButton9.setText("停止演示");
			Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
			for(int i = allMap.size(); i >= 1; i--) {
				DispatchTransDevice dtd = allMap.get(i);
				dtd.getTransDevice().setDeviceStatus(dtd.getBeginstatus());
				DeviceSVGPanelUtil.changeDeviceSVGColor(dtd.getTransDevice());
			}
			CBSystemConstants.getDtdMap().clear();
			
	//		for(int i = jTable1.getSelectedRow(); i < jTable1.getSelectedRow()+1; i++) {
			
		    	new Thread(new Runnable() {
					
					public void run() {
						// TODO Auto-generated method stub
						try {
							Thread.sleep(500);
						} catch (InterruptedException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
						CBSystemConstants.cardbuildtype = "1";
						if(isZoom==false){
			          		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			          		if(splitPane.getDividerLocation()==1)
			          			return ;
			          		int s = SystemConstants.getGuiBuilder().getSVGJTabbedPane().getSelectedIndex();
			          		for(int i=1; i<=s; i++){
				          		SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getComponentAt(i);
				          		if(otsp == null)
				          			return ;
				          		SVGCanvas fSvgCanvas=otsp.getSvgCanvas();
				          		fSvgCanvas.zoomCanvas(1,0.5);
				          		otsp.setFlush();
			          		}
			          		isZoom = true;
						}
		          		
						for(int i = 0; i < jTable1.getRowCount(); i++) {
							if(flag==true){
								Thread.interrupted();
								Rollback();
								break;
							}
							if(jButton9.getText().equals("自动演示")) {
								break;
							}
							jTable1.setRowSelectionInterval(i, i);
		
							String czmx = jTable1.getValueAt(jTable1.getSelectedRow(), 2).toString();
			            	String station = jTable1.getValueAt(jTable1.getSelectedRow(), 1).toString();
			            	List<RuleBaseMode> rbmList=OperationCheckDefault.execute(station, czmx);

			            	
			        		OperationCheckDefault.inverse(rbmList);
			        		try {
								Thread.sleep(4500);
							} catch (InterruptedException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
						}
						CBSystemConstants.cardbuildtype = "0";
					}
				}).start();
		}
		else {
			jButton9.setText("自动演示");
		}
	}
	//下一步 模拟功能演示
	private void jButton10ActionPerformed(java.awt.event.ActionEvent evt) {
		if(jButton9.getText().equals("自动演示")) {
			jButton9.setText("停止演示");
			Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
			for(int i = allMap.size(); i >= 1; i--) {
				DispatchTransDevice dtd = allMap.get(i);
				dtd.getTransDevice().setDeviceStatus(dtd.getBeginstatus());
				DeviceSVGPanelUtil.changeDeviceSVGColor(dtd.getTransDevice());
			}
			CBSystemConstants.getDtdMap().clear();
	    	new Thread(new Runnable() {
				public void run() {
					// TODO Auto-generated method stub
					try {
						Thread.sleep(500);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					CBSystemConstants.cardbuildtype = "1";
					if(isZoom==false){
		          		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		          		if(splitPane.getDividerLocation()==1)
		          			return ;
		          		int s = SystemConstants.getGuiBuilder().getSVGJTabbedPane().getSelectedIndex();
		          		for(int i=1; i<=s; i++){
			          		SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getComponentAt(i);
			          		if(otsp == null)
			          			return ;
			          		SVGCanvas fSvgCanvas=otsp.getSvgCanvas();
			          		fSvgCanvas.zoomCanvas(1,0.5);
			          		otsp.setFlush();
		          		}
		          		isZoom = true;
					}
					int y = Integer.valueOf(jTable1.getValueAt(jTable1.getSelectedRow(), 0).toString());
					if(jTable1.getRowCount()==y){
						ShowMessage.view("模拟演示已完成");
						return;
					}
					for(int i = y; i < jTable1.getRowCount(); i++) {
						if(flag==true){
							Thread.interrupted();
							Rollback();
							break;
						}
						if(jButton9.getText().equals("自动演示")) {
							break;
						}
						jTable1.setRowSelectionInterval(i, i);
	
						String czmx = jTable1.getValueAt(jTable1.getSelectedRow(), 2).toString();
		            	String station = jTable1.getValueAt(jTable1.getSelectedRow(), 1).toString();
		            	List<RuleBaseMode> rbmList=OperationCheckDefault.execute(station, czmx);
		        		OperationCheckDefault.inverse(rbmList);
		        		try {
							Thread.sleep(4500);
						} catch (InterruptedException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					CBSystemConstants.cardbuildtype = "0";
				}
			}).start();
		}
		else {
			jButton9.setText("自动演示");
		}
	}
	
	
	//上一步 模拟功能演示
	private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {
		if(jButton9.getText().equals("自动演示")) {
			jButton9.setText("停止演示");
			Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
			for(int i = allMap.size(); i >= 1; i--) {
				DispatchTransDevice dtd = allMap.get(i);
				dtd.getTransDevice().setDeviceStatus(dtd.getBeginstatus());
				DeviceSVGPanelUtil.changeDeviceSVGColor(dtd.getTransDevice());
			}
			CBSystemConstants.getDtdMap().clear();
	    	new Thread(new Runnable() {
				public void run() {
					// TODO Auto-generated method stub
					try {
						Thread.sleep(500);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					CBSystemConstants.cardbuildtype = "1";
					if(isZoom==false){
		          		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		          		if(splitPane.getDividerLocation()==1)
		          			return ;
		          		int s = SystemConstants.getGuiBuilder().getSVGJTabbedPane().getSelectedIndex();
		          		for(int i=1; i<=s; i++){
			          		SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getComponentAt(i);
			          		if(otsp == null)
			          			return ;
			          		SVGCanvas fSvgCanvas=otsp.getSvgCanvas();
			          		fSvgCanvas.zoomCanvas(1,0.5);
			          		otsp.setFlush();
		          		}
		          		isZoom = true;
					}
					int y = Integer.valueOf(jTable1.getValueAt(jTable1.getSelectedRow(), 0).toString());
					if(jTable1.getRowCount()==1){
						ShowMessage.view("已经到达第一步");
						return;
					}
					for(int i = y-2; i < jTable1.getRowCount(); i++) {
						if(flag==true){
							Thread.interrupted();
							Rollback();
							break;
						}
						if(jButton9.getText().equals("自动演示")) {
							break;
						}
						jTable1.setRowSelectionInterval(i, i);
						String czmx = jTable1.getValueAt(jTable1.getSelectedRow()-1, 2).toString();
		            	String station = jTable1.getValueAt(jTable1.getSelectedRow()-1, 1).toString();
		            	List<RuleBaseMode> rbmList=OperationCheckDefault.execute(station, czmx);
		        		OperationCheckDefault.inverse(rbmList);
		        		try {
							Thread.sleep(4500);
						} catch (InterruptedException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					CBSystemConstants.cardbuildtype = "0";
				}
			}).start();
		}
		else {
			jButton9.setText("自动演示");
		}
	}
	
	//合项
	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.mergeTableRow(jTable1, 0);
	}
	
	//分项
	private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.splitTableRow(jTable1, 0);
	}

	//下移
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.movedownTableRow(jTable1);
	}

	//上移
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.moveupTableRow(jTable1);
	}

	//删除
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.removeTableRow(jTable1);
	}

	//新增
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.addTableRow(jTable1);
	}
	private RuleBaseMode Srcrbm;
	
	public void init(CardModel cm,RuleBaseMode Srcrbm) {
		
		if (cm == null)
			return;
		int startItem = 0;
		this.Srcrbm=Srcrbm;
		
		Object[][] tableDate = null;
		if (jTable1Model == null)
			jTable1Model = new DefaultTableModel(tableDate, new String[] {
					"顺序", "操作单位", "操作内容" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
                return false;
            }
		};
		else if(jTable1Model.getRowCount() > 0) {
			startItem = Integer.valueOf(((CodeNameModel)jTable1Model.getValueAt(jTable1Model.getRowCount()-1, 0)).getName());
		}
		List<CardItemModel> DescList = cm.getCardItems();
		if (DescList != null) {
			for (int i = 0; i < DescList.size(); i++) {
				CardItemModel bcm = DescList.get(i);
				CodeNameModel cnm=new CodeNameModel(bcm.getUuIds(),bcm.getCardNum());
				Object[] rowData = { cnm, bcm.getStationName(), bcm.getCardDesc() };
				jTable1Model.addRow(rowData);
			}
		}
		if (jTable1 == null)
			jTable1 = new JTable();
		jTable1.setModel(jTable1Model);
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);

		DefaultCellEditor cellEdit = new DefaultCellEditor(new JTextField());
		cellEdit.setClickCountToStart(2);//双击后使选择的格子可编辑
		jTable1.getColumnModel().getColumn(0).setMinWidth(50);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(60);
		jTable1.getColumnModel().getColumn(1).setMinWidth(100);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
		jScrollPane2.setViewportView(jTable1);

		//拟票时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date dt = new Date();
		jLabel3.setText(sdf.format(dt));
		//拟票人
		jLabel4.setText(CBSystemConstants.getUser().getUserName());
		//操作任务
		if (jTextArea1.getText().trim().equals(""))
			jTextArea1.setText(cm.getCzrw());

		this.setVisible(true);

	}

	public void init(String[] zb,List<BaseCardModel> mx) {
		
		int startItem = 0;
		this.Srcrbm=Srcrbm;
		
		Object[][] tableDate = null;
		if (jTable1Model == null)
			jTable1Model = new DefaultTableModel(tableDate, new String[] {
					"顺序", "操作单位", "操作内容" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
                return false;
            }
		};
		else if(jTable1Model.getRowCount() > 0) {
			startItem = Integer.valueOf(((CodeNameModel)jTable1Model.getValueAt(jTable1Model.getRowCount()-1, 0)).getName());
		}
	
		for (int i = 0; i < mx.size(); i++) {
			BaseCardModel bcm = mx.get(i);
			CodeNameModel cnm=new CodeNameModel(bcm.getMxid(),bcm.getCardSub());
			Object[] rowData = { cnm, bcm.getStationName(), bcm.getCardDesc() };
			jTable1Model.addRow(rowData);
		}
	
		if (jTable1 == null)
			jTable1 = new JTable();
		jTable1.setModel(jTable1Model);
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);

		DefaultCellEditor cellEdit = new DefaultCellEditor(new JTextField());
		cellEdit.setClickCountToStart(2);//双击后使选择的格子可编辑
		jTable1.getColumnModel().getColumn(0).setMinWidth(50);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(60);
		jTable1.getColumnModel().getColumn(1).setMinWidth(100);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
		jScrollPane2.setViewportView(jTable1);

		//拟票时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date dt = new Date();
		jLabel3.setText(sdf.format(dt));
		//拟票人
		jLabel4.setText(CBSystemConstants.getUser().getUserName());
		//操作任务
		if (jTextArea1.getText().trim().equals(""))
			jTextArea1.setText(zb[2]);

		this.setVisible(true);

	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JButton jButton4;
	private javax.swing.JButton jButton5;
	private javax.swing.JButton jButton6;
	private javax.swing.JButton jButton7;
	private javax.swing.JButton jButton8;
	private javax.swing.JButton jButton9;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JLabel jLabel3;
	private javax.swing.JLabel jLabel4;
	private javax.swing.JLabel jLabel5;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JTable jTable1;
	private javax.swing.JTextArea jTextArea1;
	// End of variables declaration//GEN-END:variables
	private boolean flag =false;//是否停止
	private boolean isZoom = false;
	private DefaultTableModel jTable1Model = null;
	private JButton jButton10;
	private JButton jButton11;
}
