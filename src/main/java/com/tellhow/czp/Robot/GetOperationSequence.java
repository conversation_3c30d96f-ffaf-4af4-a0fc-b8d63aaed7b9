package com.tellhow.czp.Robot;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.WordExecute;

public class GetOperationSequence {
	
	public static String execute(String arg) {
		
		
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.cardbuildtype = "0";
		CBSystemConstants.roleCode = "0";
		CBSystemConstants.opCode = "0";
		CBSystemConstants.opRuleCode = "0";
		CBSystemConstants.jh_tai = 0;
		
		CZPService.getService().setArg("");
		StartupManager.startup();
		
		System.out.println("**********输入参数**********");
		System.out.println(arg);
		System.out.println("***************************");
		String xmlCode = "";
		if(arg.toUpperCase().contains("UTF-8")){
			xmlCode = "UTF-8";
		}else{
			xmlCode = "GBK";
		}
		
		/**
		 * 构造返回的xml。
		 * */
		Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding(xmlCode);
		Element datas=doc.addElement("Datas");
		
		/**
		 * 解析传入的xml。
		 * */
		//传入参数数据
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		InputStream is =null;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("ITEM");
			//System.out.println(itemLists);
			for (int i = 0; i <itemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				list.add(mapInfo);
			}
			
			//根据厂站和操作指令获取的报文内容
			List<Map<String, String>> msglist = new ArrayList<Map<String, String>>();
			for(Map<String, String> map : list){
				List<Map<String, String>> data = getCheckDataList(map.get("stationname"),map.get("caozuozhiling"));
				msglist.addAll(data);
			}
			//msglist转换成xml格式输出
			for(int i = 0; i < msglist.size();i++) {
				Element item=datas.addElement("ITEM");
				item.addElement("stationname").setText(msglist.get(i).get("stationname"));
				item.addElement("stationid").setText(msglist.get(i).get("stationid"));
				item.addElement("devname").setText(msglist.get(i).get("devname"));
				item.addElement("devid").setText(msglist.get(i).get("devid"));
				item.addElement("beginstatus").setText(msglist.get(i).get("beginstatus"));
				item.addElement("endstatus").setText(msglist.get(i).get("endstatus"));
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally{
			if(is!=null){
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		
		/**
		 * 返回校核结果。
		 * */
		System.out.println("**********输出结果**********");
		System.out.println(doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>"));
		System.out.println("***************************");
		return doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
	}
	
	/*
	 * 根据厂站名和操作指令，获取南瑞电网所需要的文件内容
	 */
	private static List<Map<String, String>> getCheckDataList(String stationname,String caozuozhiling){
		List<Map<String, String>> msglist = new ArrayList<Map<String, String>>();
		
		CZPService cZPService = CZPService.getService();
		List<RuleBaseMode> rbmList = cZPService.getRBMList(stationname, caozuozhiling);
		if(rbmList==null){
			return msglist;
		}
		
		if(caozuozhiling.contains("充电状态")){
			List<RuleBaseMode> newList = new ArrayList<RuleBaseMode>();
			
			for(RuleBaseMode rbm:rbmList){
				CBSystemConstants.setCurRBM(rbm);
				List<PowerDevice> sw = RuleExeUtil.getLinkedSwitch(rbm.getPd());
				List<PowerDevice> templist =  RuleExeUtil.getLineOtherSideList(rbm.getPd());
				List<PowerDevice> dcsw = RuleExeUtil.getLinkedSwitch(templist.get(0));
				templist.addAll(sw);
				
				if(caozuozhiling.contains("（")&&caozuozhiling.contains("）")){
					String state = caozuozhiling.substring(caozuozhiling.indexOf("（")+1, caozuozhiling.indexOf("）"));
					
					if(state.contains("，")){
						String[] arr = state.split("，");
						
						for(int i = 0; i < arr.length ; i++){
							RuleBaseMode rbm2 = new RuleBaseMode();
							String str = arr[i];
							if(str.contains(CZPService.getService().getDevName(CBSystemConstants.getPowerStation(sw.get(0).getPowerStationID())))){
								rbm2.setPd(sw.get(0));
								rbm2.setEndState(RuleExeUtil.getNumStatusJS(str.substring(str.indexOf("侧")+1, str.length())));
								newList.add(rbm2);
							}else if(str.contains(CZPService.getService().getDevName(CBSystemConstants.getPowerStation(dcsw.get(0).getPowerStationID())))){
								rbm2.setPd(dcsw.get(0));
								rbm2.setEndState(RuleExeUtil.getNumStatusJS(str.substring(str.indexOf("侧")+1, str.length())));
								newList.add(rbm2);
							}
						}
					}
				}
				
				if(newList.size()>0){
					for (int i = 0; i < newList.size() ; i++) {
						PowerDevice dev = newList.get(i).getPd();
						RuleBaseMode dtd = newList.get(i);
						if(dev.getDeviceType().equals(SystemConstants.Switch)
								||dev.getDeviceType().equals(SystemConstants.SwitchSeparate)
								||dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
							Map<String, String> msgvo =new HashMap<String, String>();
							msgvo.put("stationname", dev.getPowerStationName());
							msgvo.put("stationid", dev.getPowerStationID());
							msgvo.put("devname", dev.getPowerDeviceName());
							msgvo.put("devid", dev.getPowerDeviceID());
							msgvo.put("beginstatus", dtd.getBeginStatus());
							msgvo.put("endstatus", dtd.getEndState());
							if(dev.getDeviceType().equals(SystemConstants.Switch)){
								msgvo.put("devtype", "开关");
							} else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
								msgvo.put("devtype", "刀闸类型");
							} else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
								msgvo.put("devtype", "接地刀闸类型");
							}
							msglist.add(msgvo);
						}
					}
				}
			}
			return msglist;
		}else if(caozuozhiling.endsWith("状态")&&!caozuozhiling.contains("改为")&&!caozuozhiling.contains("均为")){
			for(RuleBaseMode rbm:rbmList){
				CBSystemConstants.setCurRBM(rbm);
				PowerDevice dev = rbm.getPd();
				Map<String, String> msgvo =new HashMap<String, String>();
				msgvo.put("stationname", dev.getPowerStationName());
				msgvo.put("stationid", dev.getPowerStationID());
				msgvo.put("devname", dev.getPowerDeviceName());
				msgvo.put("devid", dev.getPowerDeviceID());
				msgvo.put("beginstatus", rbm.getBeginStatus());
				msgvo.put("endstatus", rbm.getPd().getDeviceStatus());
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					msgvo.put("devtype", "开关");
				} else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
					msgvo.put("devtype", "刀闸类型");
				} else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
					msgvo.put("devtype", "接地刀闸类型");
				}
				msglist.add(msgvo);
			}
		}else if(caozuozhiling.contains("所有设备均为")){
			List<RuleBaseMode> newList = new ArrayList<RuleBaseMode>();
			String volt  = caozuozhiling.substring(caozuozhiling.indexOf("变")+1, caozuozhiling.indexOf("kV"));
			
			for(RuleBaseMode rbm:rbmList){
				CBSystemConstants.setCurRBM(rbm);
				
				for(Iterator<PowerDevice> itor = CBSystemConstants.getMapPowerStationDevice().get(rbm.getPd().getPowerStationID()).values().iterator();itor.hasNext();){
					PowerDevice pd = itor.next();
					RuleBaseMode rbm2 = new RuleBaseMode();
					
					if((int)pd.getPowerVoltGrade() == Integer.valueOf(volt)){
						if(pd.getDeviceType().equals(SystemConstants.MotherLine)||
								pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||
								pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)||
								pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
							rbm2.setPd(pd);
							rbm2.setEndState(RuleExeUtil.getNumStatusJS(caozuozhiling.substring(caozuozhiling.indexOf("为")+1, caozuozhiling.indexOf("状态"))));
							newList.add(rbm2);
						}
					}
				}
			}
			
			for (int i = 0; i < newList.size() ; i++) {
				PowerDevice dev = newList.get(i).getPd();
				RuleBaseMode dtd = newList.get(i);
				Map<String, String> msgvo =new HashMap<String, String>();
				msgvo.put("stationname", dev.getPowerStationName());
				msgvo.put("stationid", dev.getPowerStationID());
				msgvo.put("devname", dev.getPowerDeviceName());
				msgvo.put("devid", dev.getPowerDeviceID());
				msgvo.put("beginstatus", dtd.getBeginStatus());
				msgvo.put("endstatus", dtd.getEndState());
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					msgvo.put("devtype", "开关");
				} else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
					msgvo.put("devtype", "刀闸类型");
				} else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
					msgvo.put("devtype", "接地刀闸类型");
				}
				msglist.add(msgvo);
			}
		}
		
		for(RuleBaseMode rbm:rbmList){
			rbm.getPd().setDeviceStatus(rbm.getBeginStatus());
			CBSystemConstants.setCurRBM(rbm);
			RuleExecute ruleExc=new RuleExecute();
			ruleExc.execute(rbm);
			if(CBSystemConstants.getDtdMap()==null){
				CBSystemConstants.getDtdMap().clear();
				DeviceOperate.getAlltransDevMap().clear();
				continue;
			}
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				PowerDevice dev = dtd.getTransDevice();
				if((dev.getDeviceType().equals(SystemConstants.Switch)&&(dtd.getBeginstatus().equals("0")||dtd.getEndstate().equals("0")))
						||dev.getDeviceType().equals(SystemConstants.SwitchSeparate)
						||dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
					Map<String, String> msgvo =new HashMap<String, String>();
					msgvo.put("stationname", dev.getPowerStationName());
					msgvo.put("stationid", dev.getPowerStationID());
					msgvo.put("devname", dev.getPowerDeviceName());
					msgvo.put("devid", dev.getPowerDeviceID());
					
//					if(caozuozhiling.contains("（充电）")){
//						dtd.setEndstate("4");
//					}
					msgvo.put("endstatus", dtd.getEndstate());
					msgvo.put("beginstatus", dtd.getBeginstatus());
					if(dev.getDeviceType().equals(SystemConstants.Switch)){
						msgvo.put("devtype", "开关");
					} else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						msgvo.put("devtype", "刀闸类型");
					} else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
						msgvo.put("devtype", "接地刀闸类型");
					}
					msglist.add(msgvo);
				}
			}
			for (int i = CBSystemConstants.getDtdMap().size(); i>0; i--) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				PowerDevice dev = dtd.getTransDevice();
				dev.setDeviceStatus(dtd.getBeginstatus());
			}
			CBSystemConstants.getDtdMap().clear();
			DeviceOperate.getAlltransDevMap().clear();
		}
		return msglist;
	}

}
