package com.tellhow.czp.Robot;

import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import org.apache.batik.dom.svg.SVGOMGElement;
import org.apache.batik.gvt.GraphicsNode;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.action.SvgAction;
import com.tellhow.graphicframework.action.impl.AttachFlagSvgAction;
import com.tellhow.graphicframework.action.impl.ChangeColorAction;
import com.tellhow.graphicframework.action.impl.ChangeDeviceFlashingAction;
import com.tellhow.graphicframework.action.impl.ChangeDeviceOffOnAction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.CreatePowerStationToplogy;

/**
 * 执行反编译在图形上的动作
 * <AUTHOR>
 *
 */
public class ReverseAction {
private	QueryDeviceDao  dao=new QueryDeviceDao();	
/**
 * 打开对应厂站SVG图
 * @param stationname
 */
public void openSvgStation(String stationname){
	//String stationid=dao.getStationID(stationname); 
	CreatePowerStationToplogy.createSVGPanel(stationname);
	//StationSVGPanelManage sspm=new StationSVGPanelManage();
	//sspm.openPanel(stationid);	
}
/**
 * 执行SVG状态
 * @param divlist
 * @param tagetstatus
 * @param isLineCard
 * @param station
 * @param reversecardform 
 */
public void doSvgAction(List divlist){
	PowerDevice pd=new PowerDevice();
	///如果是线路还需要执行刀闸操作-- 但目前无法解析线路接地刀闸  2011.2.16记 其实可以查询到地刀，然后可通过地刀，线路联合完成相应操作，但目前沿用现有逻辑
	Map temp=new HashMap();
	for(int i=0;i<divlist.size();i++){
//		ReversePdbean rpd=new ReversePdbean();//构建回滚对象
		pd=(PowerDevice) divlist.get(i);
//		if(!Reversecard.Rollbackdiv.contains(pd)){
//		Reversecard.Rollbackdiv.add(pd);
//		temp.put(pd,pd.getDeviceStatus());//回滚备份				
//		rpd.setPd(pd);
//		rpd.setTargetStatus(pd.getDeviceStatus());
//		Reversecard.Rollback.add(rpd);
//		}
		//pd.setDeviceStatus(tagetstatus);//设置转换状态		
		
		
		new ChangeDeviceFlashingAction(pd, "3").execute();
	}	
	if(temp.size()>0)//可能MAP中无新设备
	Reversecard.Rollback.add(temp);
	}



/**
 * 搜索接地刀闸所连线路
 * @param pd 接地刀闸
 * @return
 */
public PowerDevice getLine(PowerDevice pd) {
	//执行完还需要执行相应地刀操作
	CommonSearch cs = new CommonSearch();
	PowerDevice linedev=null;
	PowerDevice pd2=null;
	Map inPara = new HashMap();
	Map outPara = new HashMap();
	inPara.put("oprSrcDevice",pd);
	inPara.put("tagDevType", SystemConstants.InOutLine);
	inPara.put("isSearchDirectDevice", true);
	cs.execute(inPara, outPara);
	List allDevList = (ArrayList) outPara.get("linkedDeviceList");
	if(allDevList.size()>0){	
			 pd2= (PowerDevice) allDevList.get(0); 			    
	}
	if(pd2!=null)
	 linedev = (PowerDevice) CBSystemConstants.getPowerDevice(pd2.getPowerStationID(), pd2.getPowerDeviceID());// 线路对象
	return linedev;
}
/**
 * 回滚设备状态
 * @param rollbackdiv
 * @param rollback
 */
public void rollback() {
	//doSvgAction(Reversecard.Rollback,Reversecard.Rollbackdiv);
	Reversecard.Rollback.clear();//清空
	Reversecard.Rollbackdiv.clear();//清空
}
/**
 * 执行设备回滚
 * @param Rollbackdiv
 * @param Rollback
 */
public void doSvgAction(List Rollbackdiv,List Rollback ){
	PowerDevice pd=new PowerDevice();
//	ReversePdbean rpd=new ReversePdbean();
	///如果是线路还需要执行刀闸操作-- 但目前无法解析线路和接地刀闸
	String tagetstatus="";
	for(int i=0;i<Rollback.size();i++){
		pd=(PowerDevice) Rollback.get(i);
		for(int j=0;j<Rollbackdiv.size();j++){//获得每个设备MAP
			Map temp=(Map) Rollbackdiv.get(j);
//			rpd=(ReversePdbean) Rollbackdiv.get(i);
			 for(int n=0;n<temp.size();n++){//循环回滚的状态
				 if(!temp.containsKey(pd))
					 continue;
				 tagetstatus=temp.get(pd).toString();
			 }
//			tagetstatus=rpd.getTargetStatus();
	   if(!temp.containsKey(pd))//确认是当前设备
				 continue;
		pd.setDeviceStatus(tagetstatus);//设置转换状态		
		if(SystemConstants.Switch.equals(pd.getDeviceType())){
			//开关类型
			
			if(pd.getDeviceStatus().equals("0")){
				//运行：填充颜色
				SvgAction action =  new ChangeColorAction(pd, PowerDeviceColorUtils.getDeviceColor(pd),PowerDeviceColorUtils.getDeviceColor(pd));
				action.execute();
			}else{
				SvgAction action =  new ChangeColorAction(pd, PowerDeviceColorUtils.getDeviceColor(pd),"rgb(0,0,0)");
				action.execute();
			}	
			continue;
		}
		if(SystemConstants.SwitchSeparate.equals(pd.getDeviceType())||SystemConstants.SwitchFlowGroundLine.equals(pd.getDeviceType())){
			//刀闸类型
			if(pd.getDeviceStatus().equals("0")){
				//合上
				SvgAction action =  new ChangeDeviceOffOnAction(pd, SvgAction.SWITCH_ON);
				action.execute();
			}else{
				SvgAction action =  new ChangeDeviceOffOnAction(pd, SvgAction.SWITCH_OFF);
				action.execute();
			}	
			continue;
		}

		if(SystemConstants.InOutLine.equals(pd.getDeviceType()))
		{
				if("3".equals(pd.getDeviceStatus())){
					//线路转检修加上检修牌
					SvgAction action =new AttachFlagSvgAction(pd,"check",true,-20,-10); 
					action.execute();			
				}else{
					//SvgAction action =new AttachFlagSvgAction(pd,"check",false); 
					//action.execute();			
				}
		}
		
		if(!pd.getDeviceStatus().equals("0")&&!SystemConstants.SwitchFlowGroundLine.equals(pd.getDeviceType())){
			SvgAction action =  new ChangeColorAction(pd, PowerDeviceColorUtils.getLoseColor(),"");
			action.execute();
		}else{
			SvgAction action =  new ChangeColorAction(pd, PowerDeviceColorUtils.getDeviceColor(pd),"");
			action.execute();
		}		
	}	
	}
	}	
public void autoToScrollToEquip(List divs)
{
	PowerDevice pd = (PowerDevice)divs.get(0);
	SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
	SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getActivateSVGPanel();
	SVGCanvas svgCanvas=otsp.getSvgCanvas();
	
	Element groupElement = resolver.getDeviceGroupElement(pd);
	final  Document document = svgCanvas.getSVGDocument();
	Element svgg=resolver.resolveSvgElement(document);
	
	
	if(groupElement == null)
	{
		JOptionPane.showMessageDialog(svgCanvas, "接线图上不存在该厂站/设备！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
		return;
	}
	
	SVGOMGElement gElement = (SVGOMGElement) groupElement;

	// Convert Screen coordinates to Document Coordinates.
//	if(!svgCanvas.isShowing())
//		svgCanvas.setVisible(true);
	int tagScreenX = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getX() + svgCanvas.getWidth()/2;
	int tagScreenY = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getY() + svgCanvas.getHeight()/2;
	GraphicsNode node = svgCanvas.getUpdateManager().getBridgeContext().getGraphicsNode(gElement);
	  // Rectangle2D ddd = node.getTransformedBounds(node.getTransform());
	System.out.println(svgCanvas.getViewBoxTransform());
	System.out.println(gElement);
	System.out.println(node);
	Rectangle2D bounds = svgCanvas.getViewBoxTransform().createTransformedShape(node.getBounds()).getBounds();  
	int srcScreenX= (int)(bounds.getX()+bounds.getWidth()/2)+(int)svgCanvas.getLocationOnScreen().getX();
	int srcScreenY = (int)(bounds.getY()+bounds.getHeight()/2)+(int)svgCanvas.getLocationOnScreen().getY();
	int offX = (int)(tagScreenX-srcScreenX);
	int offY = (int)(tagScreenY-srcScreenY);
	AffineTransform at = svgCanvas.getRenderingTransform();
	at.translate(offX/svgCanvas.getRenderingTransform().getScaleX(),  offY/svgCanvas.getRenderingTransform().getScaleY());
	svgCanvas.setRenderingTransform(at, true);
}
}
