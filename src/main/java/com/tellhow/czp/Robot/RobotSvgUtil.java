/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：
 * 功能说明 : 
 * 作    者 : 姚星乐
 * 开发日期 : 2012-12-21
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.Robot;

import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;

import javax.swing.JOptionPane;
import javax.swing.JTabbedPane;

import org.apache.batik.dom.svg.SAXSVGDocumentFactory;
import org.apache.batik.util.XMLResourceDescriptor;
import org.w3c.dom.Element;
import org.w3c.dom.svg.SVGDocument;

import com.tellhow.czp.svg.listener.AllMapMouseEventListener;
import com.tellhow.czp.svg.listener.StationMouseEventListener;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.stationstartup.StationStartupManager;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;

public class RobotSvgUtil {
	public static void openSVGPanel(String stationID, String equipID) {
		if (stationID == null || "".equals(stationID))
			return;
		String stationName = "";
		String fileName = "";
		String filePath = "";
		
		List<SVGFile> SVGFiles = SystemConstants
				.getSVGFileByStationID(stationID);

		if (SVGFiles.size() > 1) {
			
			for (Iterator<SVGFile> it = SVGFiles.iterator(); it.hasNext();) {
				filePath = it.next().getFilePath();
				if (SystemConstants.getGuiBuilder().isTabbedPageExist(filePath)) {
					JTabbedPane tabbedPane = SystemConstants.getGuiBuilder()
							.getSVGJTabbedPane();

					for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
						SVGCanvasPanel panel = (SVGCanvasPanel) tabbedPane
								.getComponentAt(i);
						if (panel.getStationID().equals(stationID)) {
							tabbedPane.remove(i);
							break;
						}
					}
				}
			}
			
			for (Iterator<SVGFile> it = SVGFiles.iterator(); it.hasNext();) {
				filePath = it.next().getFilePath();
				String parser = XMLResourceDescriptor.getXMLParserClassName();
				SAXSVGDocumentFactory factory = new SAXSVGDocumentFactory(
						parser);
				SVGDocument doc = null;
				
				try {
					File file = new File(filePath);
					doc = factory.createSVGDocument(file.toURI().toString());
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				
				if (doc != null) {
					Element element = SVGDocumentResolver.getResolver()
							.getDeviceGroupElement(doc, equipID);
					if (element == null) {
						it.remove();
						continue;
					}
				}
			}
			if (SVGFiles.size() > 1) {
				Object[] options = SVGFiles.toArray();
				int i = JOptionPane.showOptionDialog(
						SystemConstants.getMainFrame(), "选择要打开的图形",
						SystemConstants.SYSTEM_TITLE,
						JOptionPane.DEFAULT_OPTION,
						JOptionPane.WARNING_MESSAGE, null, options, options[0]);
				if (i == -1)
					fileName = SVGFiles.get(0).getFileName();
				else
					fileName = SVGFiles.get(i).getFileName();
			} else
				fileName = SVGFiles.get(0).toString();
		} else if (SVGFiles.size() == 1)
			fileName = SVGFiles.get(0).toString();
		else {
			CreatePowerStationToplogy.loadFacData(stationID);
			return;
		}

		filePath = SystemConstants.FILE_SVGMAP_PATH + fileName;

		PowerDevice pd = CBSystemConstants.getPowerStation(stationID);
		stationName = pd.getPowerDeviceName();
		
		createSVGPanel(stationID, stationName, fileName, filePath,equipID);
		
	}

	public static SVGCanvasPanel createSVGPanel(String stationID,
			String tabName, String fileName, String filePath,String equipId) {
		if (SystemConstants.getGuiBuilder() != null
				&& SystemConstants.getGuiBuilder().activateTabbedPageByName(
						filePath))
			return null;
		SVGCanvasPanel svgCanvasPanel = null;
		File svgMapFile = new File(filePath);
		if (!filePath.equals("") && !svgMapFile.exists()) {
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(),
					"不存在[" + fileName + "]一次接线图文件！",
					SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
		} else {
			if(!filePath.equals(""))
				loadFacData(stationID);//先加载缓存设备数据
			
			svgCanvasPanel = new SVGCanvasPanel(tabName);
			svgCanvasPanel
					.addSVGDocumentLoaderListener(new RobotSVGDocumentLoaderListener(
							svgCanvasPanel));
			svgCanvasPanel
					.addGVTTreeRendererListener(new RobotGVTTreeRendererListener(
							svgCanvasPanel, (PowerDevice) CBSystemConstants.getPowerDevice(stationID, equipId)));
			svgCanvasPanel
					.addGVTTreeBuilderListener(new RobotGVTTreeBuilderListener(
							svgCanvasPanel));
			svgCanvasPanel.setStationID(stationID);
			if (!filePath.equals("")) {
				if (stationID.equals(""))
					svgCanvasPanel
							.addSvgMouseEventListener(new AllMapMouseEventListener());
				else {
					svgCanvasPanel
							.addSvgMouseEventListener(new StationMouseEventListener());
					
				}
				svgCanvasPanel.loadSvgFile(svgMapFile, true);
			} else
				svgCanvasPanel
						.addSvgMouseEventListener(new AllMapMouseEventListener());
		}
		return svgCanvasPanel;
	}

	public static void loadFacData(String stationID) {
		if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
			StationStartupManager.startup(stationID);
		}
	}
}
