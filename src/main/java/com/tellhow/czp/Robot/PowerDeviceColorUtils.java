package com.tellhow.czp.Robot;

import java.awt.Color;
import java.text.DecimalFormat;

import com.tellhow.graphicframework.model.PowerDevice;

public class PowerDeviceColorUtils {
	 private static String[] powerGrade = { "0", "6", "10", "15", "18", "20", "35", "66", "110", "220", "330", "500", "750", "1000", "-1" };

	  private static String[] powerGradeColor = { "rgb(255,170,255)", "rgb(204,255,51)", "rgb(0,0,0)", 
	    "rgb(181,204,167)", "rgb(85,170,127)", "rgb(164,246,255)", 
	    "rgb(0,85,0)", "rgb(170,0,255)", "rgb(255,255,0)", 
	    "rgb(255,255,255)", "rgb(220,0,0)", "rgb(255,0,255)", 
	    "rgb(255,170,0)", "rgb(30,144,255)", "rgb(93,92,88)" };

	  private static Color[] GradeColor = { new Color(255, 170, 255), new Color(204, 255, 51), new Color(0, 0, 0), 
	    new Color(181, 204, 167), new Color(85, 170, 127), new Color(164, 246, 255), 
	    new Color(0, 85, 0), new Color(170, 0, 255), new Color(255, 255, 0), 
	    new Color(255, 255, 255), new Color(220, 0, 0), new Color(255, 0, 255), 
	    new Color(255, 170, 0), new Color(30, 144, 255), new Color(93, 92, 88) };

	  public static String getFillColor(String _powerGrade)
	  {
	    for (int i = 0; i < powerGrade.length; i++) {
	      if (powerGrade[i].equals(_powerGrade))
	        return powerGradeColor[i];
	    }
	    return "rgb(255,255,255)";
	  }
	  public static String getStrokeColor(String _powerGrade) {
	    for (int i = 0; i < powerGrade.length; i++) {
	      if (powerGrade[i].equals(_powerGrade))
	        return powerGradeColor[i];
	    }
	    return "rgb(0,0,0)";
	  }
	  public static String getStrokeColorStyle(String _powerGrade) {
	    return "kv" + _powerGrade;
	  }

	  public static String getLoseColor()
	  {
	    return "rgb(128,128,128)";
	  }

	  public static String getLoseColorStyle() {
	    return "kv0";
	  }

	  public static String getMaintenanceColor() {
	    String maintenanceColor = "";
	    maintenanceColor = getLoseColor();
	    return maintenanceColor;
	  }
	  public static Color getColor(String _powerGrade) {
	    for (int i = 0; i < powerGrade.length; i++) {
	      if (powerGrade[i].equals(_powerGrade))
	        return GradeColor[i];
	    }
	    return new Color(0, 0, 0);
	  }

	  public static String getDeviceColor(PowerDevice powerDevice) {
	    String deviceColor = null;
	    String powerVolt = new DecimalFormat("0").format(powerDevice.getPowerVoltGrade());

	    if ((!powerDevice.getDeviceStatus().equals("0")) && (!powerDevice.getDeviceStatus().equals("9")) && (!powerDevice.getDeviceStatus().equals("")))
	      deviceColor = getLoseColor();
	    else {
	      deviceColor = getStrokeColor(powerVolt);
	    }
	    return deviceColor;
	  }
	  public static String[] getPowerGrade() {
	    return powerGrade;
	  }
	  public static String[] getPowerGradeColor() {
	    return powerGradeColor;
	  }
	  public static Color[] getGradeColor() {
	    return GradeColor;
	  }
}
