package com.tellhow.czp.Robot;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.SwingUtilities;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.OperationInfo;
import czprule.model.PowerDevice;
import czprule.securitycheck.view.CheckWord;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.DeviceSVGPanelUtil;

/**
 * 根据单条术语以及操作任务判断要干什么
 * 
 * <AUTHOR>
 * 
 */
public class JudgeDoWhat {
	private String czrw = "";
	private String stepcontent = "";
	private List cardlist = new ArrayList();
	private List divlist = new ArrayList();
	private String SrcStatus = "";
	private String tagStatus = "";
	private String step = "";
	private GetReversediv grd = new GetReversediv();
	private ReverseAction ra = new ReverseAction();
	private boolean isLineCard = false;
	private String station = "";
	private QueryDeviceDao qdd = new QueryDeviceDao();
	private boolean isGroundnife = false;// 此句为地刀操作
	
	public void excute(String stepcontent, String czrw, String step,
			String station, InversionTicket reversecard) {
		
		this.stepcontent = stepcontent;
		this.step = step;
		this.station = station;
		this.czrw = czrw;
		final List divs = new ArrayList();
		
		String stationID = qdd.getStationIDByKey(station);// 当前术语变站ID
		ra.openSvgStation(stationID);// 打开对应厂站
		
		if(Reversecard.Rollbackdiv.containsKey(step)) {
			HashMap map = (HashMap)Reversecard.Rollbackdiv.get(step);
			Iterator iter = map.entrySet().iterator(); 
			while (iter.hasNext()) { 
			    Map.Entry entry = (Map.Entry) iter.next(); 
			    PowerDevice pd = (PowerDevice)entry.getKey(); 
			    String status = (String)entry.getValue(); 
			    pd.setDeviceStatus(status);
			    DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
			} 
			((HashMap)Reversecard.Rollbackdiv.get(step)).clear();
		}
		
		ArrayList<OperationInfo> oiList = CheckWord.getOperation(stepcontent);
		for(OperationInfo oi : oiList) {
			if(oi.getEquipNum().equals("") || oi.getBeginStatus().equals("") || oi.getEndStatus().equals(""))
				continue;
			String[] equipIDArr = qdd.getEquipID(stationID, oi.getEquipNum(), oi.getEquipType());
			if(equipIDArr == null)
				continue;
			for(String equipID : equipIDArr) {
				PowerDevice pd = (PowerDevice) CBSystemConstants.getPowerDevice(stationID, equipID);
				addEquipToBack(pd);
				pd.setDeviceStatus(oi.getEndStatus());
				divs.add(pd);
				
				if (SystemConstants.Switch.equals(pd.getDeviceType())) { // 开关类型
					List knifes = null;
					knifes = DoLinkknife(pd);
					
					if (knifes.size() > 0) {// 刀闸动作并记录
						for (int j = 0; j < knifes.size(); j++) {
							PowerDevice dev = (PowerDevice) knifes.get(j);
							String knifeStatus = "";
							if (Integer.valueOf(oi.getBeginStatus()) >= 2 && Integer.valueOf(oi.getEndStatus()) <= 1)
								knifeStatus = "0";
							else if (Integer.valueOf(oi.getBeginStatus()) <= 1 && Integer.valueOf(oi.getEndStatus()) >= 2)
								knifeStatus = "1";
							if(!knifeStatus.equals("")) {
								addEquipToBack(dev);
								dev.setDeviceStatus(knifeStatus);
								divs.add(dev);
							}
						}
					}
				} 
				else if (SystemConstants.InOutLine.equals(pd.getDeviceType())) {
					List grounds = getLineGround(pd);
					if (grounds.size() > 0) {// 刀闸动作并记录
						for (int j = 0; j < grounds.size(); j++) {
							PowerDevice dev = (PowerDevice) grounds.get(j);
							String knifeStatus = "";
							if (Integer.valueOf(oi.getBeginStatus()) <= 2 && Integer.valueOf(oi.getEndStatus()) == 3)
								knifeStatus = "0";
							else if (Integer.valueOf(oi.getBeginStatus()) == 3 && Integer.valueOf(oi.getEndStatus()) <= 2)
								knifeStatus = "1";
							if(!knifeStatus.equals("")) {
								addEquipToBack(dev);
								dev.setDeviceStatus(knifeStatus);
								divs.add(dev);
							}
						}
					}
				}
				else if (SystemConstants.SwitchFlowGroundLine.equals(pd.getDeviceType())) {
					PowerDevice dev = ra.getLine(pd);
					if (dev != null) {
						String lineStatus = "";
						if (Integer.valueOf(oi.getEndStatus()) == 1)
							lineStatus = "0";
						else
							lineStatus = "3";
						addEquipToBack(dev);
						dev.setDeviceStatus(lineStatus);
						divs.add(dev);
					}
				}
			}
		}
 		if(divs.size() > 0) {
			SwingUtilities.invokeLater(new Runnable() {
				@Override
				public void run() {
					ra.autoToScrollToEquip(divs);
				} 
			  });
			new Thread(new Runnable() {
				
				public void run() {
					// TODO Auto-generated method stub
					try {
						//Thread.sleep(2000);
						ra.doSvgAction(divs);
						Thread.sleep(3000);
						DeviceSVGPanelUtil.changeDeviceSVGColor((PowerDevice)divs.get(0));
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}).start();
		}
	}
	
	public void excute1(String stepcontent, String czrw, String step,
			String station, InversionTicket reversecard) {
		
		this.stepcontent = stepcontent;
		this.step = step;
		this.station = station;
		this.czrw = czrw;
		String lineName;
		List divname = new ArrayList();
		final List divs = new ArrayList();
		final String tagetstatus = JudgeStatusChange(stepcontent);
		String stationid = qdd.getStationIDByKey(station);// 当前术语变站ID
		ra.openSvgStation(station);// 打开对应厂站

		divname = grd.GetdivIdFromCard(stepcontent);// 获得相关设备名 
		
		if (divname.size() > 0) {
			if (stepcontent.indexOf("接地刀闸") >= 0) {
				List gds = getGKdiv(divname, stationid, isLineCard);
				if (gds.size() == 0) {
					// reversecard.changeValue(3, reversecard.stepNum,
					// "执行完毕，无法解析获得设备");
					return;
				}
				PowerDevice linedev = ra.getLine((PowerDevice) gds.get(0));// 通过地刀查线路
				if (linedev != null) {
					divs.add(linedev);
				} else {
					// reversecard.changeValue(3, reversecard.stepNum,
					// "执行完毕，无法解析获得设备");
					return;
				}
			} else {
				divs.addAll(getdiv(divname, stationid, isLineCard));// 获的设备对象
			}
		}
		
		if (divs.size() <= 0) {
			//reversecard.changeValue(3, reversecard.stepNum, "执行完毕，无法解析获得设备");
			
			return;
		}
		else {
			List kgs = new ArrayList();
			if(Reversecard.Rollbackdiv.containsKey(step)) {
				HashMap map = (HashMap)Reversecard.Rollbackdiv.get(step);
				Iterator iter = map.entrySet().iterator(); 
				while (iter.hasNext()) { 
				    Map.Entry entry = (Map.Entry) iter.next(); 
				    PowerDevice pd = (PowerDevice)entry.getKey(); 
				    String status = (String)entry.getValue(); 
				    pd.setDeviceStatus(status);
				    DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
				} 
				((HashMap)Reversecard.Rollbackdiv.get(step)).clear();
			}
			for (int i = 0; i < divs.size(); i++) {
				PowerDevice pd = (PowerDevice) divs.get(i);
				addEquipToBack(pd);
				if(!tagetstatus.equals("倒母"))
					pd.setDeviceStatus(tagetstatus);
				
					
					
				
				if (SystemConstants.Switch.equals(pd.getDeviceType())) {
					// 开关类型
					List knifes = null;
					if(tagetstatus.equals("倒母"))
						knifes = DoLinkknife(pd);
					else
						knifes = DoLinkknife(pd);
					
					if (knifes.size() > 0) {// 刀闸动作并记录
						for (int j = 0; j < knifes.size(); j++) {
							PowerDevice dev = (PowerDevice) knifes.get(j);
							String knifeStatus;
							if(tagetstatus.equals("倒母")) {
								if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))
									continue;
								else
									knifeStatus = dev.getDeviceStatus().equals("0")?"1":"0";
							}
							else if (tagetstatus.equals("0") || tagetstatus.equals("1"))// 开关运行热备时直连刀闸合上
								knifeStatus = "0";
							else
								knifeStatus = "1";
							if(!dev.getDeviceStatus().equals(knifeStatus)) {
								addEquipToBack(dev);
								dev.setDeviceStatus(knifeStatus);
								kgs.add(dev);
							}
						}
						
					}
					List lines=doLine(pd);
					//如果是合上线路开关，则把冷备用开关转运行
					if(stepcontent.contains("合上")&&lines.size() > 0) {
						for (int j = 0; j < lines.size(); j++) {
							PowerDevice dev = (PowerDevice) lines.get(j);
							dev.setDeviceStatus("0");
							kgs.add(dev);
						}
						
					}
					
				} 
				else if (SystemConstants.InOutLine.equals(pd.getDeviceType())) {
					List grounds = getLineGround(pd);
					if (grounds.size() > 0) {// 刀闸动作并记录
						for (int j = 0; j < grounds.size(); j++) {
							PowerDevice dev = (PowerDevice) grounds.get(j);
							String knifeStatus;
							if (tagetstatus.equals("3"))
								knifeStatus = "0";
							 else
								 knifeStatus = "1";
							if(!dev.getDeviceStatus().equals(knifeStatus)) {
								addEquipToBack(dev);
								dev.setDeviceStatus(knifeStatus);
								kgs.add(dev);
							}
						}
					}
				}
			}
			divs.addAll(kgs);
			//Reversecard.Rollbackdiv.addAll(kgs);
		}
		if(tagetstatus.equals("")){
			return;
		}
		
		ra.autoToScrollToEquip(divs);
		
		new Thread(new Runnable() {
			
			public void run() {
				// TODO Auto-generated method stub
				try {
					Thread.sleep(3000);
					ra.doSvgAction(divs);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}).start();


		
		//reversecard.changeValue(3, reversecard.stepNum, "执行成功");
	}
	
	private List doLine(PowerDevice pd) {
		//周围获得线路
		CommonSearch cs = new CommonSearch();
		Map inPara = new HashMap();
		Map outPara = new HashMap();
		inPara.put("oprSrcDevice",pd);
		inPara.put("tagDevType", SystemConstants.InOutLine);
		cs.execute(inPara, outPara);
		List allDevList = (ArrayList) outPara.get("linkedDeviceList");
		return allDevList;
	}

	private void addEquipToBack(PowerDevice pd) {
		if(!Reversecard.Rollbackdiv.containsKey(step)) {
			HashMap map = new HashMap();
			Reversecard.Rollbackdiv.put(step, map);
		}
		((HashMap)Reversecard.Rollbackdiv.get(step)).put(pd, pd.getDeviceStatus());
	}
	
	private List DoLinkknife(PowerDevice pd) {
		//执行完还需要执行相应地刀操作
		CommonSearch cs = new CommonSearch();
		Map inPara = new HashMap();
		Map outPara = new HashMap();
		inPara.put("oprSrcDevice",pd);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate);
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		List allDevList = (ArrayList) outPara.get("linkedDeviceList");
		return allDevList;
	}
	
	/**
	 * 搜索刀闸
	 * @param pd
	 * @return
	 */
	private List getLineGround(PowerDevice pd) {
		//执行完还需要执行相应地刀操作
		CommonSearch cs = new CommonSearch();
		Map inPara = new HashMap();
		Map outPara = new HashMap();
		inPara.put("oprSrcDevice",pd);
		inPara.put("tagDevType", SystemConstants.SwitchFlowGroundLine);
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		List allDevList = (ArrayList) outPara.get("linkedDeviceList");
		return allDevList;
	}

	private List getGKdiv(List divname, String stationid, boolean isLineCard) {
		String temp = "";
		List divs = new ArrayList();
		for (int i = 0; i < divname.size(); i++) {
			String devid = qdd.getGKdiv(stationid, divname.get(i).toString()
					.trim()
					+temp);
			if (devid.equals(""))
				continue;
			PowerDevice dev = (PowerDevice) CBSystemConstants.getPowerDevice(stationid, devid);
			if (dev.equals(null))
				continue;

			divs.add(dev);
		}
		return divs;
	}

	/**
	 * 获得设备对象
	 * 
	 * @param divname
	 * @param stationid
	 * @return
	 */
	private List getdiv(List divname, String stationid, boolean isLineCard) {
		
		String temp = "";
		String devid ="";
		List divs = new ArrayList();
//		if (this.stepcontent.indexOf("开关") >= 0) {
//			temp = "开关";
//		}
		
		for (int i = 0; i < divname.size(); i++) {
			
			if (this.stepcontent.indexOf("开关") >= 0) {
				devid=qdd.getswitchdiv(stationid, divname.get(i).toString().trim())	;//开关
			}else{
			    
				devid = qdd.getrevdiv(stationid, divname.get(i).toString().trim()+temp);
			}
					
			if (devid.equals(""))
				continue;
			PowerDevice dev = (PowerDevice) CBSystemConstants
					.getPowerDevice(stationid, devid);
			
			if (dev==null)
				continue;
           
			divs.add(dev);
		}
		
		return divs;
	}

	/**
	 * 单条术语解析出设备目标状态
	 * 
	 * @param card
	 * @return
	 */
	public String JudgeStatusChange(String card) {
		String tagetstatus = "";
		if (this.czrw.indexOf("母") >= 0) {// 母线
			if(this.czrw.indexOf("倒") >= 0)
				tagetstatus = "倒母";
			if (card.indexOf("转检修") >= 0) {
				tagetstatus = "3";
			}
			if (card.indexOf("转热备用") >= 0) {
				tagetstatus = "1";
			}
			if (card.indexOf("转冷备用") >= 0) {
				tagetstatus = "2";
			}
			if (card.indexOf("转运行") >= 0) {
				tagetstatus = "0";
			}

		}
		else if (this.czrw.indexOf("线") >= 0) {// 线路 全部为开关和接地刀闸操作
			isLineCard = true;
			if (card.indexOf("断开") >= 0 && card.indexOf("开关") >= 0) {// 开关运行转热备
				tagetstatus = "1";// 目标状态为1，2，3，4 2为热备
			}
			if (card.indexOf("合上") >= 0 && card.indexOf("开关") >= 0) {// 热备运行
				tagetstatus = "0";// 目标状态为1，2，3，4 2为热备
			}
			if (card.indexOf("转检修") >= 0) {
				tagetstatus = "3";
			}
			if (card.indexOf("转热备用") >= 0) {
				tagetstatus = "1";
			}
			if (card.indexOf("转冷备用") >= 0) {
				tagetstatus = "2";
			}
			if (card.indexOf("转运行") >= 0) {
				tagetstatus = "0";
			}
			if (card.indexOf("合上") >= 0 && card.indexOf("接地刀闸") >= 0) {
				tagetstatus = "3"; // 接地刀闸合上 由线路动作 所以合上刀闸，线路转检修
				isGroundnife = true;
			}
			if (card.indexOf("拉开") >= 0 && card.indexOf("接地刀闸") >= 0) {
				isGroundnife = true;
				tagetstatus = "2";
			}
			if (card.indexOf("合上") >= 0 && card.indexOf("刀闸") >= 0 && card.indexOf("接地刀闸") == -1) {
				tagetstatus = "0"; 
//				isGroundnife = true;
			}
			if (card.indexOf("推上") >= 0 && card.indexOf("刀闸") >= 0 && card.indexOf("接地刀闸") == -1) {
				tagetstatus = "0"; 
//				isGroundnife = true;
			}
			if (card.indexOf("拉开") >= 0 && card.indexOf("刀闸") >= 0 && card.indexOf("接地刀闸") == -1) {
//				isGroundnife = true;
				tagetstatus = "1";//需测试
			}
		} else if (this.czrw.indexOf("开关") >= 0) {// 开关票
			if (card.indexOf("断开") >= 0 && card.indexOf("开关") >= 0) {// 开关运行转热备
				tagetstatus = "1";// 目标状态为1，2，3，4 2为热备
			}
			if (card.indexOf("转检修") >= 0) {
				tagetstatus = "3";
			}
			if (card.indexOf("转热备用") >= 0) {
				tagetstatus = "1";
			}
			if (card.indexOf("转冷备用") >= 0) {
				tagetstatus = "2";
			}
			if (card.indexOf("转运行") >= 0) {
				tagetstatus = "0";
			}
		} else {
			if (card.indexOf("断开") >= 0 && card.indexOf("开关") >= 0) {// 开关运行转热备
				tagetstatus = "1";// 目标状态为1，2，3，4 2为热备
			}
			if (card.indexOf("转检修") >= 0) {
				tagetstatus = "3";
			}
			if (card.indexOf("转热备用") >= 0) {
				tagetstatus = "1";
			}
			if (card.indexOf("转冷备用") >= 0) {
				tagetstatus = "2";
			}
			if (card.indexOf("转运行") >= 0) {
				tagetstatus = "0";
			}
		}

		return tagetstatus;
	}

	@Override
	public String toString() {
		return "JudgeDoWhat [SrcStatus=" + SrcStatus + ", cardlist=" + cardlist
				+ ", czrw=" + czrw + ", divlist=" + divlist + ", grd=" + grd
				+ ", isGroundnife=" + isGroundnife + ", isLineCard="
				+ isLineCard + ", qdd=" + qdd + ", ra=" + ra + ", station="
				+ station + ", step=" + step + ", stepcontent=" + stepcontent
				+ ", tagStatus=" + tagStatus + "]";
	}
	

}
