package com.tellhow.czp.Robot;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.utils.StringUtils;

/**
 * 获得术语设计的厂站，以及相关的设备
 *
 * <AUTHOR>
 *
 */
public class GetReversediv {

	/**
	 * 从术语中解析连续数字获得相关设备ID
	 *
	 * @param str
	 * @return
	 */
	public List GetdivIdFromCard(String str) {
		List<String> tlist = new ArrayList<String>();
		boolean flag=false;
		String temp="";
		str=str.replaceAll("将|由", "");
		if (str.contains("PT")) {
			// 处理pt
			temp = str.substring(0,str.indexOf("PT"));
			tlist.add(temp);
		} else if (str.indexOf("刀闸") >= 0 || str.indexOf("开关") >= 0) {
			// for循环遍历字符串
			for (int i = 0; i < str.length(); i++) {

				// 判断是都为数字
				if (Character.isDigit(str.charAt(i)) || str.charAt(i) == '#') {
					temp = temp + str.charAt(i);
					flag = true;

				} else {
					// 走过数字之后才能做
					if (flag) {
						tlist.add(temp);
						flag = false;
					}
					temp = "";
				}

			}
		} else if (!str.contains("倒") && str.contains("母")) {
			// 处理母线
			temp = str.substring(0, str.lastIndexOf("母") + 1);
			tlist.add(temp);
		} else if (str.indexOf("线") >= 0||str.indexOf("段")>=0) {

			int position = str.indexOf("线");
			if(position==-1){
				position = str.indexOf("段");
			}
			if (position >= 3) {
				temp = str.substring(0, position+1);
			} else {
				temp = str.substring(0, position+1);
			}
			tlist.add(temp);
		}

		return tlist;
	}

	/**
	 * 截取线路名
	 *
	 * @param czrw
	 * @return
	 */
	public String GetLinename(String czrw) {
		String linename = "";
		linename = czrw
				.substring(czrw.indexOf("kV") + 2, czrw.indexOf("线") + 1);
		linename = linename.replaceAll("\\d*", "");
		return linename;
	}

	/**
	 * test
	 *
	 * @param args
	 */
	public static void main(String[] args) {
		GetReversediv x = new GetReversediv();

	}
	public static String getEquip(String word) {
		String equipName = "";
		String[] key = new String[]{"接地刀闸", "刀闸", "开关", "母线", "母", "线", "主变", "电抗器", "电容器"};
		for(int i = 0; i < key.length; i++) {
			if(word.lastIndexOf(key[i]) >= 0) {
				String str = word.substring(0, word.lastIndexOf(key[i]));
				if(key[i].equals("线"))
					equipName = str;
				else {
					for (int j = str.length()-1; j >= 0; j--) {
						char c = str.charAt(j);
						if (Character.isDigit(c) || c == '#' || c == '、' || StringUtils.isALB(c) || key[i].indexOf(c)>=0)
							equipName = str.charAt(j) + equipName;
						else
							break;
					}
				}
				equipName = equipName + key[i];
				break;
			}
		}
		return equipName;

	}
}
