/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：
 * 功能说明 : 
 * 作    者 : 姚星乐
 * 开发日期 : 2012-12-24
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.Robot;

import org.apache.batik.swing.svg.GVTTreeBuilderAdapter;
import org.apache.batik.swing.svg.GVTTreeBuilderEvent;
import org.apache.log4j.Logger;

import com.tellhow.czp.svg.listener.DefaultGVTTreeBuilderListener;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

public class RobotGVTTreeBuilderListener extends GVTTreeBuilderAdapter {
	private static Logger log = Logger
			.getLogger(DefaultGVTTreeBuilderListener.class);

	protected SVGCanvasPanel panel;

	public RobotGVTTreeBuilderListener(SVGCanvasPanel svgPanel) {
		this.panel = svgPanel;
	}

	@Override
	public void gvtBuildStarted(GVTTreeBuilderEvent e) {
		// Console.outPut("开始构建图形...\r\n");
	}

	@Override
	public void gvtBuildCompleted(GVTTreeBuilderEvent e) {
		

	}
}
