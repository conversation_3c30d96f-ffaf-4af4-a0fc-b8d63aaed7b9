/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项   目   组 ：
 * 功能说明 : 
 * 作          者 : 姚星乐
 * 开发日期 : 2012-12-24
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.Robot;

import java.awt.Color;

import javax.swing.JOptionPane;

import org.apache.batik.swing.gvt.GVTTreeRendererAdapter;
import org.apache.batik.swing.gvt.GVTTreeRendererEvent;
import org.beryl.gui.MessageDialog;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.mainframe.WindowSplash;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

import czprule.model.PowerDevice;

public class RobotGVTTreeRendererListener extends GVTTreeRendererAdapter{
	private  SVGCanvasPanel panel;
	private PowerDevice pd;
	public RobotGVTTreeRendererListener(SVGCanvasPanel svgPanel,PowerDevice pd) {
		this.panel = svgPanel;
		this.pd=pd;
	}

	@Override
	public void gvtRenderingPrepare(GVTTreeRendererEvent e) {
		try {
			/**
			 * 依据操作票、图元各设备状态位回复至之前状态
			 */
			new Thread(){
				long sleepTimes=2000;
				@SuppressWarnings("static-access")
				public void run() {
					try
					{
					/*
					Robot robot = new Robot();
					Thread currentThread = Thread.currentThread();
					currentThread.sleep(sleepTimes);
					
					
					SVGCanvas svgCanvas=panel.getSvgCanvas();
					SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
			    	Element svgElement = resolver.getDeviceGraphElement(pd);
			    	if(svgElement == null)
			    	{
			    		JOptionPane.showMessageDialog(svgCanvas, "接线图上不存在该厂站/设备！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
			    		return;
			    	}
			    	double svgWidth = 0,svgHeight = 0,scrollXScale = 0,scrollYScale = 0;
			    	int scrollVertValue = 0,scrollHorizValue = 0;
			    	String posStr = "";
			    	
			    	Point2D.Double point= DOMUtil.getElementStartPoint(svgElement);
			   
					//----------------获取当前接线图的原始大小及与当前设备的------------------------------------
					svgWidth = svgCanvas.getInitWidth();
					svgHeight = svgCanvas.getInitHeight();
					
					if(point.getX()>svgWidth)
					{
						scrollHorizValue=(int)(svgWidth/2+point.getX());
						if(scrollHorizValue>svgCanvas.getSVGScrollPane().getVertScrollBar().getMaximum())
							scrollHorizValue=(int)svgCanvas.getSVGScrollPane().getVertScrollBar().getMaximum();
					}
					if(point.getY()>svgHeight)
					{
						scrollVertValue=(int)(svgHeight/2+point.getY());
						if(scrollVertValue>svgCanvas.getSVGScrollPane().getVertScrollBar().getMaximum())
							scrollVertValue=(int)svgCanvas.getSVGScrollPane().getVertScrollBar().getMaximum();
					}
					
					System.out.println("目前垂直滚动条位置:"+svgCanvas.getSVGScrollPane().getVertScrollBar().getValue()+"\t水平滚动条位置:"+svgCanvas.getSVGScrollPane().getHorizScrollBar().getAlignmentX());
					System.out.println("需要滚动的值:"+scrollVertValue+"\t"+scrollHorizValue);
					svgCanvas.getSVGScrollPane().getVertScrollBar().setValue(scrollVertValue);
					svgCanvas.getSVGScrollPane().getHorizScrollBar().setValue(scrollHorizValue);
//					System.out.println("之后垂直滚动条位置:"+svgCanvas.getSVGScrollPane().getVertScrollBar().getValue()+"\t之后滚动条位置:"+svgCanvas.getSVGScrollPane().getHorizScrollBar().getAlignmentX());
					
					
					int beginX=(int)(188+point.getX()-scrollHorizValue);
					int beginY=(int)(120+point.getY()-scrollVertValue);
					
					robot.mouseMove(beginX,beginY );
				
					
					System.out.println("x:"+beginX+"\ty:"+beginY);
					
					currentThread.sleep(sleepTimes);
					
					// 点击右键 弹出菜单
					robot.mousePress(InputEvent.BUTTON3_MASK);
					robot.mouseRelease(InputEvent.BUTTON3_MASK);
					currentThread.sleep(sleepTimes);
					
					// 鼠标移至弹出菜单 操作选项上
					// 菜单选项半个间距为10
					robot.mouseMove(beginX+30, beginY+70);
					currentThread.sleep(sleepTimes);
					
					// 点击弹出菜单 操作选项 
					robot.mousePress(InputEvent.BUTTON1_MASK);
					robot.mouseRelease(InputEvent.BUTTON1_MASK);
					currentThread.sleep(sleepTimes);
					
					// 操作场景 线路 解环侧 鼠标移至单选 
					robot.mouseMove(600, 351);
					currentThread.sleep(sleepTimes);
					
					// 点击单选选项
					robot.mousePress(InputEvent.BUTTON1_MASK);
					robot.mouseRelease(InputEvent.BUTTON1_MASK);
					currentThread.sleep(sleepTimes);
					
					// 鼠标移至按钮 
					robot.mouseMove(620, 436);
					currentThread.sleep(sleepTimes);
					
					// 点击确认按钮 
					robot.mousePress(InputEvent.BUTTON1_MASK);
					robot.mouseRelease(InputEvent.BUTTON1_MASK);
					currentThread.sleep(sleepTimes);
					
					// 鼠标移至 Table显示的开关选择项 
					robot.mouseMove(507, 338);
					currentThread.sleep(sleepTimes);
					
					// 点击选中 开关选择项 
					robot.mousePress(InputEvent.BUTTON1_MASK);
					robot.mouseRelease(InputEvent.BUTTON1_MASK);
					currentThread.sleep(sleepTimes);
					
					// 鼠标移至确认按钮 
					robot.mouseMove(865, 297);
					currentThread.sleep(sleepTimes);
					
					// 点击确认按钮 
					robot.mousePress(InputEvent.BUTTON1_MASK);
					robot.mouseRelease(InputEvent.BUTTON1_MASK);
					currentThread.sleep(sleepTimes);
					
					// 鼠标移至 Table显示的开关选择项 
					robot.mouseMove(508, 357);
					currentThread.sleep(sleepTimes);
					
					// 点击选中 开关选择项 
					robot.mousePress(InputEvent.BUTTON1_MASK);
					robot.mouseRelease(InputEvent.BUTTON1_MASK);
					currentThread.sleep(sleepTimes);
					
					// 鼠标移至确认按钮 
					robot.mouseMove(865, 297);
					currentThread.sleep(sleepTimes);
					
					// 点击确认按钮 完成回演 
					robot.mousePress(InputEvent.BUTTON1_MASK);
					robot.mouseRelease(InputEvent.BUTTON1_MASK);
					
					robot = null;
					*/
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "操作票反演完成!","提示信息",MessageDialog.INFORMATION_MESSAGE);
					
					}catch(Exception ex)
					{
						ex.printStackTrace();
					}
				};
			}.start();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
    }

    @Override
	public void gvtRenderingCompleted(GVTTreeRendererEvent e) {
    	WindowSplash.stopSplashWindow();
        panel.getSvgCanvas().setBackground(Color.BLACK);
    }
}
