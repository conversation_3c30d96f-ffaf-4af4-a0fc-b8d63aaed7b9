/*
 * Reversecard.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.Robot;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;
import javax.swing.table.DefaultTableModel;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/**
 * 逐步解析操作术语窗口
 * <AUTHOR>
 */
public class Reversecard extends javax.swing.JDialog {
	private DefaultTableModel jTable1Model = null;
	private int row_xh = 1;//操作序号	
	private String czrw = "";//存放操作任务

	public int stepNum = 0;//执行步骤(计数用)
	public static List Rollback = new ArrayList();//记录已执行的步骤，Map (key-pd value-设备状态)
	public static Map Rollbackdiv = new HashMap();//记录已执行的步骤，设备集合

	private List cardList = new ArrayList();//存放操作术语集合
	private ReverseAction ra = new ReverseAction();
	private boolean RollCheck = false;//回滚状态，默认为失败

	/** Creates new form Reversecard */
	public Reversecard(java.awt.Frame parent, CodeNameModel cnm) {
		super(parent, "操作票演示", false);
		initComponents();
		jBRollback.setVisible(false);
		initdate(cnm);
		setLocationCenter();
	}

	/**
	 * 初始化数据
	 * @param cnm
	 */
	private void initdate(CodeNameModel cnm) {
		jTnpr.setText("");
		jTczrw.setText("");
		jTnpsj.setText("");
		czrw = "";
		Object[][] tableData = null;
		jTable1Model = new DefaultTableModel(tableData, new String[] { "序号",
				"操作单位", "操作内容", "演示状态" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		String sql = "select czdw,cznr from "+CBSystemConstants.opcardUser+"t_a_czpmx where f_zbid ='"
				+ cnm.getCode() + "' order by to_number(mxid)";
		List results = DBManager.query(sql);
		cardList.addAll(results);
		Map temp = new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			Object[] rowData = { "   " + StringUtils.ObjToString(row_xh++),
					StringUtils.ObjToString(temp.get("czdw")),
					StringUtils.ObjToString(temp.get("cznr")) };
			jTable1Model.addRow(rowData);
		}
		jTcard.setModel(jTable1Model);
		jTcard.getColumnModel().getColumn(0).setMaxWidth(40);
		jTcard.getColumnModel().getColumn(1).setMaxWidth(70);
		jTcard.getColumnModel().getColumn(2).setMinWidth(180);
	}

	/**
	 * 单步执行
	 */
	private void jBsteprunActionPerformed(java.awt.event.ActionEvent evt) {
		if (stepNum >= jTcard.getRowCount()) {
			javax.swing.JOptionPane
					.showMessageDialog(this, "没有下一步了!",
							CBSystemConstants.SYSTEM_TITLE,
							JOptionPane.WARNING_MESSAGE);
			return;
		}
		String step = jTcard.getValueAt(stepNum, 2).toString();
		String stationname = jTcard.getValueAt(stepNum, 1).toString();
		JudgeDoWhat jdw = new JudgeDoWhat(); //判断并执行 将当前form传递便于 
		//jdw.excute(step, czrw, stepNum, stationname, this);
		stepNum++;
	}

	private void formWindowClosing(java.awt.event.WindowEvent evt) {
		if (Rollbackdiv.size() > 0 && Rollback.size() > 0) {
			ra.rollback();
			stepNum = 0;//重置
		}
	}

	/**
	 * 更新执行状态
	 * @param cnm
	 */
	public void changeValue(int column, int row, String result) {
		jTcard.setValueAt(result, row, column);
	}

	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jLabel1 = new javax.swing.JLabel();
		jTnpr = new javax.swing.JTextField();
		jLabel2 = new javax.swing.JLabel();
		jLabel3 = new javax.swing.JLabel();
		jTczrw = new javax.swing.JTextField();
		jTnpsj = new javax.swing.JTextField();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTcard = new javax.swing.JTable();
		jBsteprun = new javax.swing.JButton();
		jBRollback = new javax.swing.JButton();
		jLabel4 = new javax.swing.JLabel();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		addWindowListener(new java.awt.event.WindowAdapter() {
			public void windowClosing(java.awt.event.WindowEvent evt) {
				formWindowClosing(evt);
			}
		});

		jLabel1.setText("\u62df\u7968\u4eba\uff1a");

		jTnpr.setEditable(false);

		jLabel2.setText("\u62df\u7968\u65f6\u95f4\uff1a");

		jLabel3.setText("\u64cd\u4f5c\u4efb\u52a1\uff1a");

		jTczrw.setEditable(false);

		jTnpsj.setEditable(false);

		jTcard.setModel(new javax.swing.table.DefaultTableModel(new Object[][] {

		}, new String[] { "序号", "操作单位", "操作任务", "演示状态" }));
		jScrollPane1.setViewportView(jTcard);

		jBsteprun.setText("\u5355\u6b65\u6267\u884c");
		jBsteprun.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jBsteprunActionPerformed(evt);
			}
		});

		jBRollback.setText("\u56de\u6eda\u5355\u6b65");

		jLabel4.setForeground(new java.awt.Color(255, 51, 102));
		jLabel4.setText("\u6ce8\uff1a\u6240\u6709\u6f14\u793a\u5728\u5173\u95ed\u540e\u5c06\u8fd4\u56de\u6f14\u793a\u524d\u72b6\u6001\uff0c\u5982\u56de\u6eda\u5931\u8d25\uff0c\u8bf7\u540c\u6b65EMS\u72b6\u6001");

		javax.swing.GroupLayout layout = new javax.swing.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
				.addGroup(
						layout.createSequentialGroup()
								.addGap(25, 25, 25)
								.addGroup(
										layout.createParallelGroup(
												javax.swing.GroupLayout.Alignment.LEADING)
												.addComponent(jLabel1)
												.addGroup(
														layout.createSequentialGroup()
																.addPreferredGap(
																		javax.swing.LayoutStyle.ComponentPlacement.RELATED)
																.addComponent(
																		jLabel3)))
								.addPreferredGap(
										javax.swing.LayoutStyle.ComponentPlacement.RELATED)
								.addGroup(
										layout.createParallelGroup(
												javax.swing.GroupLayout.Alignment.LEADING,
												false)
												.addGroup(
														layout.createSequentialGroup()
																.addComponent(
																		jTnpr,
																		javax.swing.GroupLayout.PREFERRED_SIZE,
																		92,
																		javax.swing.GroupLayout.PREFERRED_SIZE)
																.addGap(18, 18,
																		18)
																.addComponent(
																		jLabel2)
																.addPreferredGap(
																		javax.swing.LayoutStyle.ComponentPlacement.RELATED)
																.addComponent(
																		jTnpsj,
																		javax.swing.GroupLayout.PREFERRED_SIZE,
																		196,
																		javax.swing.GroupLayout.PREFERRED_SIZE))
												.addComponent(jTczrw))
								.addContainerGap(140, Short.MAX_VALUE))
				.addGroup(
						javax.swing.GroupLayout.Alignment.TRAILING,
						layout.createSequentialGroup()
								.addGap(191, 191, 191)
								.addComponent(jBsteprun)
								.addPreferredGap(
										javax.swing.LayoutStyle.ComponentPlacement.RELATED,
										103, Short.MAX_VALUE)
								.addComponent(jBRollback).addGap(145, 145, 145))
				.addGroup(
						layout.createSequentialGroup().addContainerGap()
								.addComponent(jLabel4)
								.addContainerGap(203, Short.MAX_VALUE))
				.addGroup(
						layout.createSequentialGroup()
								.addContainerGap()
								.addComponent(jScrollPane1,
										javax.swing.GroupLayout.DEFAULT_SIZE,
										577, Short.MAX_VALUE).addContainerGap()));
		layout.setVerticalGroup(layout
				.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
				.addGroup(
						layout.createSequentialGroup()
								.addContainerGap()
								.addGroup(
										layout.createParallelGroup(
												javax.swing.GroupLayout.Alignment.BASELINE)
												.addComponent(jLabel1)
												.addComponent(jLabel2)
												.addComponent(
														jTnpr,
														javax.swing.GroupLayout.PREFERRED_SIZE,
														javax.swing.GroupLayout.DEFAULT_SIZE,
														javax.swing.GroupLayout.PREFERRED_SIZE)
												.addComponent(
														jTnpsj,
														javax.swing.GroupLayout.PREFERRED_SIZE,
														javax.swing.GroupLayout.DEFAULT_SIZE,
														javax.swing.GroupLayout.PREFERRED_SIZE))
								.addPreferredGap(
										javax.swing.LayoutStyle.ComponentPlacement.RELATED)
								.addGroup(
										layout.createParallelGroup(
												javax.swing.GroupLayout.Alignment.BASELINE)
												.addComponent(jLabel3)
												.addComponent(
														jTczrw,
														javax.swing.GroupLayout.PREFERRED_SIZE,
														javax.swing.GroupLayout.DEFAULT_SIZE,
														javax.swing.GroupLayout.PREFERRED_SIZE))
								.addPreferredGap(
										javax.swing.LayoutStyle.ComponentPlacement.RELATED)
								.addComponent(jScrollPane1,
										javax.swing.GroupLayout.PREFERRED_SIZE,
										312,
										javax.swing.GroupLayout.PREFERRED_SIZE)
								.addPreferredGap(
										javax.swing.LayoutStyle.ComponentPlacement.RELATED)
								.addComponent(jLabel4)
								.addPreferredGap(
										javax.swing.LayoutStyle.ComponentPlacement.RELATED)
								.addGroup(
										layout.createParallelGroup(
												javax.swing.GroupLayout.Alignment.BASELINE)
												.addComponent(jBRollback)
												.addComponent(jBsteprun))
								.addContainerGap(16, Short.MAX_VALUE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//GEN-END:initComponen

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width),
				(h - this.getSize().height) / 2);
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jBRollback;
	private javax.swing.JButton jBsteprun;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JLabel jLabel3;
	private javax.swing.JLabel jLabel4;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTcard;
	private javax.swing.JTextField jTczrw;
	private javax.swing.JTextField jTnpr;
	private javax.swing.JTextField jTnpsj;
	// End of variables declaration//GEN-END:variables

}
