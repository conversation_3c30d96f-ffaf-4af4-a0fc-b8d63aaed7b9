package com.tellhow.czp.widget;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTree;
import javax.swing.plaf.basic.BasicArrowButton;
import javax.swing.plaf.basic.BasicComboBoxUI;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeCellRenderer;
import javax.swing.tree.TreeModel;
import javax.swing.tree.TreePath;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.svg.listener.AllMapMouseEventListener;
import com.tellhow.czp.svg.listener.DefaultGVTTreeBuilderListener;
import com.tellhow.czp.svg.listener.DefaultGVTTreeRendererListener;
import com.tellhow.czp.svg.listener.DefaultSVGDocumentLoaderListener;
import com.tellhow.czp.svg.listener.StationMouseEventListener;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.czp.util.GUIUtil;
import com.tellhow.czp.widget.OperateTicketPubTree;
import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

/**
 * 变电站树列表面板
 * <AUTHOR>
 */
public class SVGFileWidget extends Widget {
	private JPanel panel=new JPanel();
	private JScrollPane sp_Trans;
	private JPopupMenu treePopupMenu;
	private JMenuItem searchMenuItem;
	private String lastSearchText = null;
	private JTree transTree;
	private List list = null;	
	private JLabel label;
	private JPanel searchPanel;
	final JComboBox jComboBox = new JAutoCompleteComboBox();
	public SVGFileWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		OperateTicketLineTree ottl = new OperateTicketLineTree();
		
        transTree = ottl.buildTreeAll("接线图");//由sql动态构建树
        
       
        transTree.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    clickTreeNode(simplePath);
                    //缓存解决方法（暂定）
                    
                    JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
                    int n=tabbedPane.getComponentCount();
                    if(n>8){
                    	for(int i=0;i<n;i++){
	            			SVGCanvasPanel selpanel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
	            			boolean isdestry = selpanel.isIsdestry();
	            			if(isdestry==false){
		            			final SVGCanvas canvas = selpanel.getSvgCanvas();
		            		    new Thread(new Runnable() {
		        		        	public void run() {
		        		        		canvas.destroyCanvas();
		        		        		System.gc();
		        		        	}
		    			 	    }).start();
		            		    selpanel.setIsdestry(true);
		            		    break;
	            			}
                    	}
                    }
                }
            }
            
            public void mouseReleased(MouseEvent e) {
            	TreePath path = transTree.getPathForLocation(e.getX(), e.getY());
				if (path == null) {
					if (e.getButton() == 3)
						treePopupMenu.show(transTree, e.getX(), e.getY());
					return;
				}
            }
        });
        transTree.setRootVisible(false);
        transTree.setToggleClickCount(1);
        sp_Trans = new JScrollPane();
        sp_Trans.setViewportView(transTree);
        searchMenuItem = new JMenuItem("查找");
        searchMenuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				String searchText = JOptionPane.showInputDialog(SystemConstants.getMainFrame(), "输入要查找的厂站名称", lastSearchText);
				if (searchText == null)
					return;
				else if (searchText.equals("")) {
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "查询条件不能为空！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
					return;
				}
				lastSearchText = searchText;
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
				}
			}
		});
        treePopupMenu = new JPopupMenu();
        treePopupMenu.add(searchMenuItem);
        
        panel.setLayout(new BorderLayout());
        panel.add(sp_Trans,BorderLayout.CENTER);
        addSearchItem();
        setleafIcon();
	}
	
	public void refresh() {
	
	}
	
	private void clickTreeNode(TreePath simplePath) {
		if(TempTicket.getTempTicket()==null){
			JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(1.0);
		}
		if(simplePath==null){
			return;
		}
		DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
  
		File openFile = null;
		File file = new File(SystemConstants.FILE_SVGMAP_PATH+"");
		if(file.isDirectory()){
			File[] files = file.listFiles();
			for(File filePara :files){
				if(filePara.getName().indexOf(lastNode.toString())>=0){
					openFile=filePara;
					break;
				}
			}
		}
		if(openFile!=null){
	    	CreatePowerStationToplogy.createSVGPanel("", lastNode.toString(), lastNode.toString(), openFile.getPath());  
		}
           
		

	
	}
	
	private void setleafIcon() {
		Thread thread =new Thread(){
       	 public void run(){
       		 
       		 if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive()){
	        		    try {
	        		    	SystemConstants.threadLoadFile.join();
						} catch (InterruptedException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
	        		    DifferentIconTreeCellRender render = new DifferentIconTreeCellRender();
	        		    transTree.setCellRenderer(render); 
       		 }
       	 }
       };
       thread.start();
	   
	}
	//搜索栏的添加
	private void addSearchItem() {
		label = new JLabel("搜索厂站：");
		searchPanel = new JPanel();
		searchPanel.setBackground(Color.white);
		searchPanel.setLayout(new BorderLayout());
		searchPanel.add(label,BorderLayout.WEST);
//		String sql = CZPImpl.getPropertyValue("StationSearchSql");
//		if(sql == null)
//			sql = OPEService.getService().TransTreeWidgetSql1();
//			
//		GUIUtil.fillComboBox(jComboBox, sql);
		
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		File file = new File(SystemConstants.FILE_SVGMAP_PATH+"");
		int i=0;
		if(file.isDirectory()){
			File[] files = file.listFiles();
			for(File filePara :files){
				String filename = filePara.getName().split("\\.")[0];
//				String filename = filePara.getName();
				filename = filename.replaceAll(".svg", "");
				cnm=new CodeNameModel();
				cnm.setCode(filename);
				cnm.setName(filename);
				model.addElement(cnm);

			}
		}
		jComboBox.setModel(model);
		
		jComboBox.setSelectedIndex(-1);
		searchPanel.add(jComboBox,BorderLayout.CENTER);
		panel.add(searchPanel,BorderLayout.NORTH);
		
		jComboBox.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				if(jComboBox.getSelectedItem()==null || jComboBox.getSelectedItem() instanceof java.lang.String){
					return;
				}
				String searchText = ((CodeNameModel) jComboBox.getSelectedItem()).getCode();
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
					if(!jComboBox.getName().equals("selecting"))
						clickTreeNode(treePath);
				}
			}
		});
		
	}

	private TreePath findInPath(JTree tree, TreePath treePath, String str) {
		Object object = treePath.getLastPathComponent();

		DefaultMutableTreeNode node = (DefaultMutableTreeNode)object;
		if(node.getUserObject() == null)
			return null;
		String value = "";
		if(node.getUserObject() instanceof DefaultSimpleNode) {
			value = ((DefaultSimpleNode)node.getUserObject()).getItemCode();
		}
		
		if (value.toLowerCase().equals(str.toLowerCase()) ) {
			return treePath;
		}else {
			TreeModel model = tree.getModel();
			int n = model.getChildCount(object);
			int curRow = tree.getRowForPath(tree.getSelectionPath());
			for (int i = 0; i < n; i++) {
				Object child = model.getChild(object, i);
				TreePath path = treePath.pathByAddingChild(child);
				if(child.toString().equals(str)){
					return path;
				}
//				TreePath path = treePath.pathByAddingChild(child);
//				path = findInPath(tree, path, str);
//				if (path != null && tree.getRowForPath(path) > curRow) {
//					return path;
//				}
			}
			return null;
		}
	}   		   

	public SVGFileWidget(Widget parent, String name, String preset)
			throws GUIException {
		super(parent, name, preset);
	}

	@Override
	public Component getWidget() {
		return panel;
	}
	
	public void getywd(){
		String opteam;
		String stationid;
		String sql="select opteam,station_id from opcard.t_e_substationpicinfo";
		List result=DBManager.queryForList(sql);
		for(int i=0;i<result.size();i++){
			Map map=(Map)result.get(i);
			stationid=StringUtils.ObjToString(map.get("station_id"));
			opteam=StringUtils.ObjToString(map.get("opteam"));
			//运维队缓存
        	CBSystemConstants.yunweidui.put(stationid, opteam);
		}
	}

}




