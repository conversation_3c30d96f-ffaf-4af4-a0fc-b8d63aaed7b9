package com.tellhow.czp.widget;

import java.awt.Component;
import java.io.File;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

import czprule.system.CreatePowerStationToplogy;


public class SVGCanvasWidget extends Widget{
	private SVGCanvasPanel svgPanel = null;
	private String svgFile = null;
	private String name = null;
	
	public SVGCanvasWidget(Widget parent, String name) throws GUIException{
		super(parent, name);
	}
	
	public Component getWidget() {
		return svgPanel;
	}
	
	public void finalizeConstruction() throws GUIException {
		File file = new File(svgFile);
		if(file.exists()) {
			svgPanel = CreatePowerStationToplogy.createSVGPanel("", name, name, svgFile);
			if(SystemConstants.getMapSVGFile().get(svgPanel.getFilePath())!=null)
				SystemConstants.getMapSVGFile().get(svgPanel.getFilePath()).setAlwaysOpen(true);
		}
		else
			svgPanel = CreatePowerStationToplogy.createSVGPanel("","","","");
	}
	
	public void setProperty(String name, Object value) throws GUIException {
		if ("svgFile".equals(name)) {
			this.svgFile = (String)value;
		}
		else if ("name".equals(name)){
			this.name = (String)value;
		}	
	}
}

