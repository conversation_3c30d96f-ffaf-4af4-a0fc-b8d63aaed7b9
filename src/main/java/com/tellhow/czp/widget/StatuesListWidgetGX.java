package com.tellhow.czp.widget;

import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.Iterator;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.component.BorderButton;
import org.beryl.gui.component.ImagePanel;

import com.tellhow.czp.sysconfig.UnitManager;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.stationstartup.InitDeviceStatus;
import czprule.stationstartup.StationStartupManager;
import czprule.system.CBSystemConstants;
import czprule.system.DeviceSVGPanelUtil;

/**
 * 状态显示
 * */
public class StatuesListWidgetGX extends Widget implements ItemListener{
	private ImagePanel attributepanel=new ImagePanel(new javax.swing.ImageIcon(ImagePanel.class.getResource("/tellhow/icons/bg2.png")));
	private JComboBox nodeattributecomboBox=new JComboBox();
	public static JComboBox jComboBox = new JComboBox();
	public static String gxcardtype = "0"; //0逐项令;1保护令;2综合令;4单项令;5许可令
	public static String gxcardkind = "逐项令";//0逐项令;1保护令;2综合令;4单项令;5许可令
//	public static String lineName = "";
	
//	private TransTreeWidget ttw=null;
	
	public JComboBox getNodeattributecomboBox() {
		return nodeattributecomboBox;
	}
	public void setNodeattributecomboBox(JComboBox nodeattributecomboBox) {
		this.nodeattributecomboBox = nodeattributecomboBox;
	}
	public JComboBox getjComboBox() {
		return jComboBox;
	}
	public void setjComboBox(JComboBox jComboBox) {
		this.jComboBox = jComboBox;
	}
	//private ImageSlider slider;
	public StatuesListWidgetGX(Widget parent, String name) throws GUIException {
		super(parent, name);
		Vector<UnitManager> organkindlist=new Vector<UnitManager>();//机构属性
		//机构属性
		 UnitManager 	unitManager10= new UnitManager();
	  	 UnitManager	unitManager11= new UnitManager();

	  	 if(CBSystemConstants.roleCode.equals("0")) {
			 unitManager10.setCode("0");
			 unitManager10.setName("拟票态"); //调度拟票态
			 organkindlist.add(unitManager10);
			 unitManager11.setCode("1");
			 unitManager11.setName("实时态");
			 organkindlist.add(unitManager11);
			
	  	 }
		 nodeattributecomboBox=new JComboBox(organkindlist);//机构属性
		 nodeattributecomboBox.setPreferredSize(new Dimension(100,30));
		 nodeattributecomboBox.setFont(new Font("宋体", Font.PLAIN, 14));
		 nodeattributecomboBox.setSelectedItem(unitManager11);
		 attributepanel.add(nodeattributecomboBox,BorderLayout.CENTER);
		 nodeattributecomboBox.addItemListener(this);
		 
//		 ttw=new TransTreeWidget(parent, name);
		 
		 //操作类型下拉框	 
		 DefaultComboBoxModel model = new DefaultComboBoxModel();
		 model.addElement(new CodeNameModel("0", "逐项令"));
		 model.addElement(new CodeNameModel("1", "二次令"));
		 model.addElement(new CodeNameModel("2", "综合令"));
		 model.addElement(new CodeNameModel("4", "单项令"));//3位监控令,不可取
		 model.addElement(new CodeNameModel("5", "许可令"));
		 jComboBox.setModel(model);		
		 jComboBox.setPreferredSize(new Dimension(100,30));
		 jComboBox.setFont(new Font("宋体", Font.PLAIN, 14));
		 attributepanel.add(jComboBox,BorderLayout.CENTER);	 
		 jComboBox.addItemListener(new ItemListener(){
			    public void itemStateChanged(ItemEvent e) {
			    	if(e.getStateChange() == ItemEvent.SELECTED){
			    		if(jComboBox.getSelectedIndex()==0){
			    			gxcardtype = "0";
			    			gxcardkind = "逐项令";
			    		}else if(jComboBox.getSelectedIndex()==1){
			    			gxcardtype = "1";
			    			gxcardkind = "二次令";
			    		}else if(jComboBox.getSelectedIndex()==2){
			    			gxcardtype = "2";
			    			gxcardkind = "综合令";
			    		}else if(jComboBox.getSelectedIndex()==3){
			    			gxcardtype = "4";
			    			gxcardkind = "单项令";
			    		}else if(jComboBox.getSelectedIndex()==4){
			    			gxcardtype = "5";
			    			gxcardkind = "许可令";
			    		}
			    	}
			    }
		 });
	}
	@Override
	public void itemStateChanged(ItemEvent e) {	
		if(e.getStateChange() == ItemEvent.SELECTED){
			UnitManager getuse=(UnitManager)nodeattributecomboBox.getSelectedItem();
			String getcode=getuse.getCode();
			if(getcode.equals("0")){
				CBSystemConstants.cardstatus="0";
				BorderButton button1 = (BorderButton)SystemConstants.getGuiBuilder().getWidget("equipStatusSet").getRealWidget();
				button1.setEnabled(true);
				BorderButton button2 = (BorderButton)SystemConstants.getGuiBuilder().getWidget("statusCheck").getRealWidget();
				button2.setEnabled(true);
			}else if(getcode.equals("1")){
				CBSystemConstants.cardstatus="1";
				BorderButton button1 = (BorderButton)SystemConstants.getGuiBuilder().getWidget("equipStatusSet").getRealWidget();
				button1.setEnabled(false);
				BorderButton button2 = (BorderButton)SystemConstants.getGuiBuilder().getWidget("statusCheck").getRealWidget();
				button2.setEnabled(false);
			}else{
				CBSystemConstants.cardstatus="2";
			}
			refushing();

		}
	}
	@Override
	public Component getWidget() {
		return attributepanel;
	}

	public static void refushing(){
		SVGCanvasPanel panelsel= SystemConstants.getGuiBuilder().getActivateSVGPanel();
		String stationIDsel = "";
		if(panelsel != null) {
			stationIDsel = panelsel.getStationID();
			if(!stationIDsel.equals("")) {
				refushingStation(stationIDsel);
			}
		}
		SvgUtil.clear(); 
	}
	
	public static void refushingStation(String stationID){
		InitDeviceStatus ie = new InitDeviceStatus();
		if(CBSystemConstants.cardstatus.equals("0"))
			StationStartupManager.startup(stationID);
		else if(CBSystemConstants.cardstatus.equals("1"))
			ie.initStatus_EMSToCache(stationID);
		else if(CBSystemConstants.cardstatus.equals("2"))
			StationStartupManager.startup(stationID);
		Map deviceMap=(Map)CBSystemConstants.getMapPowerStationDevice().get(stationID);
		PowerDevice pd=null;
	
    	for (Iterator iter = deviceMap.values().iterator(); iter.hasNext();) {
    		pd=(PowerDevice) iter.next();
    		if(pd==null)
    			continue;
    		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
		}
	}
}
