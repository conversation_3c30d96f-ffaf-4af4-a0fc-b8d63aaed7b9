package com.tellhow.czp.widget;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;

import javax.swing.BorderFactory;
import javax.swing.JLabel;
import javax.swing.JPanel;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.ColorUtils;

public class VoltSignPanel extends JPanel {
	
	public VoltSignPanel() {
		super();
		init();

	}
	
	private void init() {
		this.setLayout(new FlowLayout());
		this.add(new JLabel("颜色说明:"));
//		addLabel("6KV", getColor("6"));
//		addLabel("10KV", getColor("10"));
		addLabel("35KV", getColor("35"));
		addLabel("110KV", getColor("110"));
		addLabel("220KV", getColor("220"));
		addLabel("330KV", getColor("330"));
		addLabel("500KV", getColor("500"));
//		addLabel("750KV", getColor("750"));
//		addLabel("1000KV", getColor("1000"));
	}
	
	private void addLabel(String text, Color color) {
		JPanel panel = new javax.swing.JPanel();
		panel.setBackground(color);
		panel.setPreferredSize(new Dimension(16,16)); 
		panel.setBorder(BorderFactory.createLoweredBevelBorder());
		
		JLabel label = new javax.swing.JLabel();
		label.setText(text);
		
		this.add(panel);
		this.add(label);
	}
	
	private Color getColor(String volCode) {
		String rgb = SystemConstants.getMapColor().get(volCode);
		return ColorUtils.getColor(rgb);
	}
	
	
    
}
