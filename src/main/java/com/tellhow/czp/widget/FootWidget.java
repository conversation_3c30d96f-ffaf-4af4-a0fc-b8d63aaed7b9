package com.tellhow.czp.widget;

import java.awt.Component;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;

import com.tellhow.czp.mainframe.FootPanel;



public class FootWidget extends Widget {
	private FootPanel footPanel;
	public FootWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		footPanel=new FootPanel();
		
	}

	@Override
	public Component getWidget() {
		
		return footPanel;
	}


}
