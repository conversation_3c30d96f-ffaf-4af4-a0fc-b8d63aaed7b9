/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票系统
 * 功能说明 : 构建一个线路树
 * 作    者 : 张余平
 * 开发日期 : 2008-07-16
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.widget;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Pattern;

import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeNode;
import javax.swing.tree.TreePath;

import org.apache.log4j.Logger;

import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.DBManager;


public class OperateTicketPubTree {
	private static Logger log = Logger.getLogger(OperateTicketPubTree.class);
    private List<DefaultSimpleNode> codeMap = new ArrayList<DefaultSimpleNode>();   //id 和 value 的映射
    private Integer colNum;      //树的层次 
    private Integer cols;   //数据库语句列数
    
    public OperateTicketPubTree(Integer colNum) {
    	this.colNum = colNum;
    }

    /**
     *作用：构建JTree
     *@param rootName 树名
     *@param connType 连接类型1（青海）2（江西）3（其他）
     *@param sql 树的数据源
     *@param colnum 树的级数
     *@param isSort 是否需要内部排序
     ***/
    public JTree buildLineTree(String rootName, List list, boolean isSort) {
        DefaultMutableTreeNode rootNode = new DefaultMutableTreeNode();   //创建根节点     
        DefaultSimpleNode dsn1 = new DefaultSimpleNode();
		dsn1.setItemCode("厂站");
		dsn1.setItemName("厂站");
		DefaultMutableTreeNode rNode = new DefaultMutableTreeNode(dsn1);
		rootNode.add(rNode);
			
        if(list != null) {
	        Collections.sort(codeMap, new Comparator<DefaultSimpleNode>() {
				public int compare(DefaultSimpleNode pd1, DefaultSimpleNode pd2) {
			        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");  
					if (!pd1.getItemparam1().equals("") && !pd2.getItemparam1().equals("") &&
							pattern.matcher(pd1.getItemparam1().replace("kV", "")).matches() && 
							Double.valueOf(pd1.getItemparam1().replace("kV", "")) >= Double.valueOf(pd2.getItemparam1().replace("kV", "")))
						return 0;
					else
						return 1;
				}
			});
	        
	        HashMap<String, DefaultMutableTreeNode> volNodeMap = new HashMap<String, DefaultMutableTreeNode>();
	        for (Iterator it = codeMap.iterator(); it.hasNext();) {
	        	DefaultSimpleNode newKey = (DefaultSimpleNode)it.next();     //treeMap的序列码
	            
	            DefaultMutableTreeNode newNode;   //为每个序号新建一个树节点
	            
	            newNode = new DefaultMutableTreeNode(newKey);
	            
	            DefaultMutableTreeNode volNode = null;
	            if(volNodeMap.containsKey(newKey.getItemparam1())) {
	            	volNode = volNodeMap.get(newKey.getItemparam1());
	            }
	            else {
	            	 DefaultSimpleNode volDsn = new DefaultSimpleNode();
	            	 volDsn.setItemCode(newKey.getItemparam1());
	            	 volDsn.setItemName(newKey.getItemparam1());
	         		 volNode = new DefaultMutableTreeNode(volDsn);
	         		 rNode.add(volNode);
	         		 volNodeMap.put(newKey.getItemparam1(), volNode);
	            }
	            volNode.add(newNode);  
	            
	        }
        }
        else {
        	for (Iterator it = SystemConstants.getMapSVGFile().values().iterator(); it.hasNext();) {
   				SVGFile svgFile = (SVGFile)it.next();
   				DefaultSimpleNode dsn = new DefaultSimpleNode();
   				dsn.setItemCode(svgFile.getFilePath());
				dsn.setItemName(svgFile.getFileName());
				DefaultMutableTreeNode newNode = new DefaultMutableTreeNode(dsn);
  				rootNode.add(newNode);
        	}
        }
        JTree lineTree = new JTree(rootNode);
        expandAll(lineTree, new TreePath(rootNode), true);
        return lineTree;
    }
    
    /**
     *作用：构建JTree  按照条件过滤不需要的svg文件
     *@param rootName 树名
     *@param connType 连接类型1（青海）2（江西）3（其他）
     *@param sql 树的数据源
     *@param colnum 树的级数
     *@param isSort 是否需要内部排序
     ***/
    public JTree buildLineTree2(String rootName, List list, boolean isSort) {
        DefaultMutableTreeNode rootNode = new DefaultMutableTreeNode(rootName);   //创建根节点     
        for (Iterator it = SystemConstants.getMapSVGFile().values().iterator(); it.hasNext();) {
				SVGFile svgFile = (SVGFile)it.next();
				DefaultSimpleNode dsn = new DefaultSimpleNode();
				if(svgFile.getFileName().indexOf("母线") >0){
					continue;
				}else if(svgFile.getFileName().indexOf("线") >0){
					dsn.setItemCode(svgFile.getFilePath());
					dsn.setItemName(svgFile.getFileName());
					DefaultMutableTreeNode newNode = new DefaultMutableTreeNode(dsn);
  				rootNode.add(newNode);
				}

    	}
        JTree lineTree = new JTree(rootNode);
        expandAll(lineTree, new TreePath(rootNode), true);
        return lineTree;
    }
   
    /**
     * 作用：获取结果集
     * @param:connType 连接类型
     * 扩展：sql可作参数输入动态改变树
     **/
    public List getList(String sql, Integer connType) {
    
    	List  list = getListByRes(DBManager.queryForList(sql));
            
        return list;
    }

    /**
     * 作用：对数据库查询返回的结果集ResultSet 进行处理,拆分有逗号隔开的字段，空字段设置为“其他”
     *       将各个字段组合为"XXXX-XXXX-XXXX"存入list返回
     **/
    public List getListByRes(List rs) {
    	codeMap.clear();
        List list = new ArrayList();
        DefaultSimpleNode dsn = null;
        try {
//            String[] shortArray = new String[colNum];                //一条记录的各列处理后的临时数据集合
            StringBuffer shortStrBuf;
            
            for(int i = 0; i < rs.size(); i++) {
            	Map map = (Map)rs.get(i);
                // 加载对象
                if (cols != colNum) {
                    dsn = new DefaultSimpleNode();
                    
                    dsn.setItemCode(StringUtils.ObjToString(map.get("NODECODE")));
                    dsn.setItemName(StringUtils.ObjToString(map.get("NODENAME")));
                    dsn.setItemparam1(StringUtils.ObjToString(map.get("VOLTAGE_CODE")));
                    codeMap.add(dsn);
                }
            }
                
        } catch (Exception e) {
        	log.error(e.getMessage(), e);
        }
        return list;
    }

    /**
     * 作用：根据输入的list 排序后生成根"01 根节点 0101 次节点...." 的一个TreeMap 
     * @param: isSort 是否需要对list排序
     * @param：nodeList字段 XXXX-XXXX-XXX 形式
     **/
    public TreeMap getTreeMap(List nodeList, boolean isSort) {
        // TreeMap treeMap =new  TreeMap(Collections.reverseOrder());     //数据倒序
        if (isSort) {
            Comparator comp = Collections.reverseOrder();  //降序
            Collections.sort(nodeList, comp);
        //  Collections.sort(nodeList);     //将list排序
        }
        String[] nodeStr = new String[colNum];  //当前节点数组
        String[] nodeSeq = new String[colNum];   //当前节点序号
        Integer[] intSeq = new Integer[colNum + 1];   //当前节点在其父节点中的排序
        TreeMap treeMap = new TreeMap();
        String[] newNodeStr = null;
        for (int cn = 0; cn < nodeList.size(); cn++) {
            //遍历List
            newNodeStr = nodeList.get(cn).toString().split("_");    // 新增一组节点
            for (int i = 0; i < newNodeStr.length; i++) {
                if (nodeStr[i] == null) {           //初始化节点
                    nodeStr[i] = "@tellhow#";           //设置个无意义字符串 方便比较 出现相同概率较少
                }
                if (intSeq[i] == null) {             //初始化节点在父节点中的排序
                    intSeq[i] = 0;
                }
                if (i == 0) {         //根节点
                    if (!nodeStr[i].equals(newNodeStr[i])) {
                        nodeStr[i] = newNodeStr[i];
                        if (intSeq[i] < 9) {
                            nodeSeq[i] = "00" + (++intSeq[i]);
                            intSeq[i + 1] = 0;
                        } else {
                            if (intSeq[i] < 99) {
                                nodeSeq[i] = "0" + (++intSeq[i]);
                                intSeq[i + 1] = 0;
                            } else {
                                nodeSeq[i] = String.valueOf(++intSeq[i]);
                                intSeq[i + 1] = 0;          //将下一级级节点序号清零
                            }
                        }
                        treeMap.put(nodeSeq[i], nodeStr[i]);
                    }
                } else {
                    if (!nodeStr[i].equals(newNodeStr[i]) || intSeq[i] == 0) {
                        nodeStr[i] = newNodeStr[i];
                        if (intSeq[i] < 9) {
                            nodeSeq[i] = nodeSeq[i - 1] + "00" + (++intSeq[i]);
                            intSeq[i + 1] = 0;
                        } else {
                            if (intSeq[i] < 99) {
                                nodeSeq[i] = nodeSeq[i - 1] + "0" + (++intSeq[i]);
                                intSeq[i + 1] = 0;
                            } else {
                                nodeSeq[i] = nodeSeq[i - 1] + String.valueOf(++intSeq[i]);
                                intSeq[i + 1] = 0;          //将下一级级节点序号清零
                            }
                        }
                        treeMap.put(nodeSeq[i], nodeStr[i]);
                    }
                }
            }
        }
        return treeMap;
    }
    
    void expandAll(JTree tree, TreePath parent, boolean expand) {
		TreeNode node = (TreeNode) parent.getLastPathComponent();
		if (node.getChildCount() >= 0) {
			for (Enumeration e = node.children(); e.hasMoreElements();) {
				TreeNode n = (TreeNode) e.nextElement();
				TreePath path = parent.pathByAddingChild(n);
				expandAll(tree, path, expand);
			}
		}
		if (expand) {
			tree.expandPath(parent);
		} else {
			tree.collapsePath(parent);
		}
	}
}
   
