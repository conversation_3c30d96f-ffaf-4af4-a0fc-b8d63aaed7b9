package com.tellhow.czp.widget;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.ImageIcon;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeCellRenderer;
import javax.swing.tree.TreeModel;
import javax.swing.tree.TreePath;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

/**
 * 读取路径下svg文件，如果不存在状态则显示为文件，否则显示为有状态的svg文件
 * <AUTHOR>
 * @since 2014-10-09 13:40
 *
 */
public class TheTreeWidget extends Widget {
	private JPanel panel=new JPanel();
	private JScrollPane sp_Trans;
	private JPopupMenu treePopupMenu;
	private JTree transTree;
	private List list = null;
	private String transsql="";
	public TheTreeWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		OperateTicketPubTree ottl = new OperateTicketPubTree(3);
        transTree = ottl.buildLineTree2("厂站", null, false);//由sql动态构建树
        transTree.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    clickTreeNode(simplePath);
                    //缓存解决方法（暂定）
                    
                    JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
                    int n=tabbedPane.getComponentCount();
                    if(n>8){
                    	for(int i=0;i<n;i++){
	            			SVGCanvasPanel selpanel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
	            			boolean isdestry = selpanel.isIsdestry();
	            			if(isdestry==false){
		            			final SVGCanvas canvas = selpanel.getSvgCanvas();
		            		    new Thread(new Runnable() {
		        		        	public void run() {
		        		        		canvas.destroyCanvas();
		        		        		System.gc();
		        		        	}
		    			 	    }).start();
		            		    selpanel.setIsdestry(true);
		            		    break;
	            			}
                    	}
                    }
                }
            }
            
            public void mouseReleased(MouseEvent e) {
            	TreePath path = transTree.getPathForLocation(e.getX(), e.getY());
				if (path == null) {
					if (e.getButton() == 3)
						treePopupMenu.show(transTree, e.getX(), e.getY());
					return;
				}
            }
        });
        transTree.setRootVisible(false);
        transTree.setToggleClickCount(1);
        sp_Trans = new JScrollPane();
        sp_Trans.setViewportView(transTree);
        panel.setLayout(new BorderLayout());
        panel.add(sp_Trans,BorderLayout.CENTER);
        addSearchItem();
        panel.setMaximumSize(new Dimension(100,500));
        panel.setMinimumSize(new Dimension(100,500));
        panel.setPreferredSize(new Dimension(100,500));
        setleafIcon(transTree);
	}
	
	public void refresh() {
		OperateTicketPubTree ottl = new OperateTicketPubTree(3);
		transsql = CZPImpl.getPropertyValue("StationTreeSql").replace("[organ]", CBSystemConstants.getUser().getOrganID());
		if(transsql == null)
			transsql = OPEService.getService().TransTreeWidgetSql();
		transsql=transsql.replaceAll("equip\\.", CBSystemConstants.equipUser+"\\.");
        list = ottl.getList(transsql, 1);
        transTree = ottl.buildLineTree("厂站", list, false);//由sql动态构建树
        sp_Trans.setViewportView(transTree);
        
        transTree.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    clickTreeNode(simplePath);
                    //缓存解决方法（暂定）
                    JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
                    int n=tabbedPane.getComponentCount();
                    if(n>8){
                    	for(int i=0;i<n;i++){
	            			SVGCanvasPanel selpanel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
	            			boolean isdestry = selpanel.isIsdestry();
	            			if(isdestry==false){
		            			final SVGCanvas canvas = selpanel.getSvgCanvas();
		            		    new Thread(new Runnable() {
		        		        	public void run() {
		        		        		canvas.destroyCanvas();
		        		        		System.gc();
		        		        	}
		    			 	    }).start();
		            		    selpanel.setIsdestry(true);
		            		    break;
	            			}
                    	}
                    }
                }
            }
            
            public void mouseReleased(MouseEvent e) {
            	TreePath path = transTree.getPathForLocation(e.getX(), e.getY());
				if (path == null) {
					if (e.getButton() == 3)
						treePopupMenu.show(transTree, e.getX(), e.getY());
					return;
				}
            }
        });
	}
	
	private void clickTreeNode(TreePath simplePath) {
		if(TempTicket.getTempTicket()==null){
			JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(1.0);
		}
		if(simplePath==null){
			return;
		}
		DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
		String stationId = "";
		List<SVGFile> fileList = null;
        if (lastNode.isLeaf()) {
            DefaultSimpleNode dsn = (DefaultSimpleNode) lastNode.getUserObject();
            String filePath = "";
            String fileName = "";
            String stationID = "";
            String stationName = "";

        	filePath = dsn.getItemCode();
            fileName = dsn.getItemName();
            stationName = dsn.getItemName().substring(0,dsn.getItemName().indexOf("."));
    		stationId = SystemConstants.getMapSVGFile().get(filePath).getStationID();
    		if("".equals(stationId) || stationId ==null) {
    			String tabName = fileName.replace(".svg", "");
    			CreatePowerStationToplogy.createSVGPanel("", tabName, fileName, filePath);

    		}else{
    			fileList = SystemConstants.getSVGFileByStationID(stationId);
    			if(fileList.size() == 0) {
                	if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive())
                		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
                	else
                		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在" + dsn.getItemName() + "一次接线图！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
                    return;
                }
                else if(fileList.size() == 1) {
                	filePath = fileList.get(0).getFilePath();
                    fileName = fileList.get(0).getFileName();
                }
                else {
                	Object[] options = fileList.toArray(); 
                	int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
                	if(i == -1)
                		return;
                	filePath = fileList.get(i).getFilePath();
                    fileName = fileList.get(i).getFileName();
                }
                stationID = SystemConstants.getMapSVGFile().get(filePath).getStationID();
            
                //8缓存
                SvgP svgp=new SvgP(stationID, stationName, fileName, filePath);
                CBSystemConstants.svgps.put(stationID, svgp);
                JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
        		int n=tabbedPane.getComponentCount();
        		for(int i=0;i<n;i++){
        			SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
    				String station=sel.getStationID();
    				boolean isdestry=sel.isIsdestry();
    				if(station.equals(stationID)&&isdestry==true){
    					File svgMapFile = new File(filePath);
    					sel.loadSvgFile(svgMapFile);
    					sel.setIsdestry(false);
    				}
        		}
    			CreatePowerStationToplogy.createSVGPanel(stationID, stationName, fileName, filePath);
    		
    		}
            
		}
	}
	
	private void setleafIcon(JTree tree) {
	   DifferentIconTreeCellRender1 render = new DifferentIconTreeCellRender1();
	   transTree.setCellRenderer(render); 
	}
	//搜索栏的添加
	private void addSearchItem() {
		JLabel label = new JLabel("搜索：");
		JPanel searchPanel = new JPanel();
		searchPanel.setBackground(Color.white);
		searchPanel.setLayout(new BorderLayout());
		searchPanel.add(label,BorderLayout.WEST);
		final JComboBox combo = new JAutoCompleteComboBox();
		String sql = CZPImpl.getPropertyValue("StationSearchSql");
		sql=sql.replaceAll("equip\\.", CBSystemConstants.equipUser+"\\.");
		fillComboBox(combo, sql);
		combo.setSelectedIndex(-1);
		searchPanel.add(combo);
		panel.add(searchPanel,BorderLayout.NORTH);		
		combo.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				if(combo.getSelectedItem() instanceof java.lang.String){
					return;
				}
				String searchText = ((CodeNameModel) combo.getSelectedItem()).getName();
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
						clickTreeNode(treePath);
				}
			}
		});
	}

	public void fillComboBox(JComboBox comboBox, String sql) {
		
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
    	for (Iterator it = SystemConstants.getMapSVGFile().values().iterator(); it.hasNext();) {
			SVGFile svgFile = (SVGFile)it.next();
			if(svgFile.getFileName().indexOf("母线") >0){
				continue;
			}else if(svgFile.getFileName().indexOf("线") >0){
  				cnm=new CodeNameModel();
				cnm.setCode(svgFile.getFilePath());
				cnm.setName(svgFile.getFileName());
				model.addElement(cnm);
			}
	            
    	}
		comboBox.setModel(model);
	}
	private TreePath findInPath(JTree tree, TreePath treePath, String str) {
		Object object = treePath.getLastPathComponent();
		if (object == null) {
			return null;
		}

		String value = object.toString();
		/*if (value.toLowerCase().indexOf(str.toLowerCase()) >= 0) {
			return treePath;
		}*/
		if (value.toLowerCase().equals(str.toLowerCase()) ) {
			return treePath;
		}else {
			TreeModel model = tree.getModel();
			int n = model.getChildCount(object);
			int curRow = tree.getRowForPath(tree.getSelectionPath());
			for (int i = 0; i < n; i++) {
				Object child = model.getChild(object, i);
				TreePath path = treePath.pathByAddingChild(child);

				path = findInPath(tree, path, str);
				if (path != null && tree.getRowForPath(path) > curRow) {
					return path;
				}
			}
			return null;
		}
	}

	public TheTreeWidget(Widget parent, String name, String preset)
			throws GUIException {
		super(parent, name, preset);
	}

	@Override
	public Component getWidget() {
		return panel;
	}
	

}

class DifferentIconTreeCellRender1 extends JLabel implements TreeCellRenderer{
	@Override
	public Component getTreeCellRendererComponent(JTree tree, Object value,
			boolean sel, boolean expanded, boolean leaf, int row,
			boolean hasFocus) {
		DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
		int level=node.getLevel();
//		if(level==2){
//			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree2.png")));
//		}else if(level==1){
//			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree1.png")));
//		}else {
//			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree3.png")));
//		}
//		if(level==2){
//		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree2.png")));
//	}else if(level==1){
//		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree1.png")));
//	}else {
//		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree3.png")));
//	}
	if(level==1){
	}
	else if(level==2){
		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree1.png")));
	}else if(level==3){
		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree2.png")));
	}else {
		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree3.png")));
	}
		if(sel){
			this.setOpaque(true);
			this.setForeground(Color.white);
			this.setBackground(Color.blue);
		}else{
			this.setOpaque(false);
			this.setForeground(Color.black);
			this.setBackground(Color.white);
		}
		this.setText(value.toString());
		return this;
	}
}
