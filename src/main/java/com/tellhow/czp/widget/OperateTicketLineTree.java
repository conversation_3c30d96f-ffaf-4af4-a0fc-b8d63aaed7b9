package com.tellhow.czp.widget;


import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.Iterator;

import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;

import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.system.CreatePowerStationToplogy;

/**
 *
 * <AUTHOR>
 */
public class OperateTicketLineTree {

    /**
     * 构建树（Tree）
     * @param rootName 根结点名称
     * @return lineTree
     */
    public JTree buildTree(String rootName) {
        DefaultTreeModel dtm = null;
        SVGFile svgfile = null;
        DefaultMutableTreeNode root = new DefaultMutableTreeNode(rootName);   //根节点
        DefaultSimpleNode dsn = null;   //自定义节点对象
        DefaultMutableTreeNode childs = null;

        for (Iterator iterator = SystemConstants.getMapSVGFile().values().iterator(); iterator.hasNext();) {
        	svgfile = (SVGFile) iterator.next();
        	if(svgfile.getMapType().equals(SystemConstants.MAP_TYPE_SYS)) {
	        	dsn = new DefaultSimpleNode();
	            dsn.setItemCode(svgfile.getFilePath());
	            dsn.setItemName(svgfile.getFileName());
	            childs = new DefaultMutableTreeNode(dsn);
	            root.add(childs);
        	}
        }
        dtm = new DefaultTreeModel(root);
        JTree lineTree = new JTree(dtm);
        lineTree.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
                    if (lastNode.isLeaf()) {
                        DefaultSimpleNode dsn = (DefaultSimpleNode) lastNode.getUserObject();
                        //如果系统接线图已经打开，则只切换视图
                        String fileName = dsn.getItemName();
                        String filePath = dsn.getItemCode();
                    	CreatePowerStationToplogy.createSVGPanel("", fileName, fileName, filePath);
                    }
                }
            }
        });
        lineTree.setRootVisible(false);
        return lineTree;
    }
    
    public JTree buildTreeAll(String rootName) {
        DefaultTreeModel dtm = null;
        SVGFile svgfile = null;
        DefaultMutableTreeNode root = new DefaultMutableTreeNode(rootName);   //根节点
        DefaultSimpleNode dsn = null;   //自定义节点对象
        DefaultMutableTreeNode childs = null;

        for (Iterator iterator = SystemConstants.getMapSVGFile().values().iterator(); iterator.hasNext();) {
        	svgfile = (SVGFile) iterator.next();
        	dsn = new DefaultSimpleNode();
            dsn.setItemCode(svgfile.getFilePath());
            dsn.setItemName(svgfile.getFileName().replace(".svg", ""));
            childs = new DefaultMutableTreeNode(dsn);
            root.add(childs);
        }
        dtm = new DefaultTreeModel(root);
        JTree lineTree = new JTree(dtm);
        lineTree.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
                    if (lastNode.isLeaf()) {
                        DefaultSimpleNode dsn = (DefaultSimpleNode) lastNode.getUserObject();
                        //如果系统接线图已经打开，则只切换视图
                        String fileName = dsn.getItemName();
                        String filePath = dsn.getItemCode();
                    	CreatePowerStationToplogy.createSVGPanel("", fileName, fileName, filePath);
                    }
                }
            }
        });
        lineTree.setRootVisible(false);
        return lineTree;
    }
}

