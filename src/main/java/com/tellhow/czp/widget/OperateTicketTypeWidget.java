package com.tellhow.czp.widget;

import java.awt.Component;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;

import com.tellhow.czp.operationcard.OperateTicketTypePanel;
import com.tellhow.czp.operationcard.OperateTicketTypePanelDefault;



public class OperateTicketTypeWidget extends Widget {

//	private OperateTicketTypePanelDefault operateTicketTypePanel;
	private OperateTicketTypePanel otp;

	public OperateTicketTypeWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
//		operateTicketTypePanel = new OperateTicketTypePanelDefault();
		otp = OperateTicketTypePanel.getInstance();
	}

	@Override
	public Component getWidget() {
		return otp;
	}
	

}
