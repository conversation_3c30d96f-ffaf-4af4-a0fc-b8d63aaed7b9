package com.tellhow.czp.widget;

import java.awt.Component;

import javax.swing.ImageIcon;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;

import com.tellhow.czp.mainframe.FootPanelTH;

/**
 * 带技术支持信息的底部面板
 * <AUTHOR>
 * 2014-12-24
 */

public class FootWidgetTH extends Widget {
	private FootPanelTH footPanel;
	public FootWidgetTH(Widget parent, String name) throws GUIException {
		super(parent, name);
		footPanel=new FootPanelTH();
		
	}
	
	public void setProperty(String name, Object value) throws GUIException {
		 if (name.equals("backgroundIcon")) {
			if (value != null && !value.equals("")) {
				footPanel=new FootPanelTH((ImageIcon) value);
			}
		}
		
	}

	@Override
	public Component getWidget() {
		
		return footPanel;
	}


}
