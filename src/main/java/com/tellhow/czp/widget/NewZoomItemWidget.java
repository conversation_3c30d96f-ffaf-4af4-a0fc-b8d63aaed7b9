package com.tellhow.czp.widget;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import javax.swing.JPanel;
import javax.swing.JSlider;
import javax.swing.JSplitPane;
import javax.swing.event.ChangeEvent;
import javax.swing.event.ChangeListener;

import org.beryl.gui.GUIException;
import org.beryl.gui.ImageIconFactory;
import org.beryl.gui.Widget;
import org.beryl.gui.component.BorderButton;
import org.beryl.gui.component.ImagePanel;
import org.beryl.gui.component.ImageSlider;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;


/**
 * Gny
 * 工具栏的放大缩小组件
 * */
public class NewZoomItemWidget extends Widget{

	private JPanel zoomPane = new JPanel();
	private ImageSlider slider;
	public NewZoomItemWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		slider =new ImageSlider(null, 1, 160, 80);
		slider.setOpaque(false);
		slider.setPreferredSize(new Dimension(100, 20));
		BorderButton bt1 = new BorderButton(ImageIconFactory.getIcon("jian.png"));
		BorderButton bt2 = new BorderButton(ImageIconFactory.getIcon("jia.png"));
		bt1.setBounds(0, 0, 10, 10);
		bt2.setBounds(0, 0, 10, 10);
		bt1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
            	SystemConstants.getGuiBuilder().SVGtoSmall(null);
            }
        });
		
		bt2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
            	SystemConstants.getGuiBuilder().SVGtoBig(null);
            }
        });
		bt1.setBorderPainted(false);
		bt1.setContentAreaFilled(false);
		bt2.setBorderPainted(false);
		bt1.setOpaque(false);
		bt2.setOpaque(false);
		bt2.setContentAreaFilled(false);
		zoomPane.setOpaque(false);
		zoomPane.setLayout(new BorderLayout());
		zoomPane.add(bt1,BorderLayout.WEST);
		zoomPane.add(slider,BorderLayout.CENTER);
		zoomPane.add(bt2,BorderLayout.EAST);
		
		slider.addChangeListener(new ChangeListener() {
			// 滚动条监听器
			public void stateChanged(ChangeEvent evt) {
				JSlider slider = (JSlider) evt.getSource();
				int value = slider.getValue();
				ZoomSvgBySliderValue(value);
			}
		});
	}
	
/*	private BorderButton getButton(String text) {
		final BorderButton button = new BorderButton();
		button.setText(text);
		button.setFont(new Font("宋体",Font.PLAIN, 20));
		button.setBorderPainted(false);
		button.setOpaque(false);
		button.setContentAreaFilled(false);
		button.addMouseListener(new MouseAdapter() {
			public void mouseEntered(MouseEvent e) {
				if (button.isEnabled()) {
					button.setOpaque(true);
					button.setBorderPainted(true);
				}
			}

			public void mouseExited(MouseEvent e) {
				if (button.isEnabled()) {
					button.setOpaque(false);
					button.setBorderPainted(false);
				}
			}
		});
		return button;
	}*/


	@Override
	public Component getWidget() {
		return zoomPane;
	}
	
	/**
	 * 根据slide的数值控制缩放比例
	 * */
	public void ZoomSvgBySliderValue(int value) {
		JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		if(splitPane.getDividerLocation()==1)
			return;
		SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getSelectedComponent();
		if(otsp==null){
			return;
		}
		SVGCanvas svgCanvas=otsp.getSvgCanvas();
		
		double scale;
		if (value > 80) {
			scale = 1 + (value - 80) * (9 - 1) / 80.0;
		} else {
			scale = 1 - (80 - value) * (1 - 0.2) / 80.0;
		}
		double orgScale = svgCanvas.getRenderingTransform().getScaleX();
		int flag = orgScale > scale ? -1 : 1;
		scale = Math.abs(orgScale - scale);
		svgCanvas.zoomCanvas(flag, scale);
		otsp.setFlush();
	}
	
	/**
	 *缩小 
	 * */
	public void zoomIn(){
		int value = slider.getValue();
		if (value < 11) {
			value = 1;
		} else {
			value -= 10;
		}
		slider.setValue(value);
		ZoomSvgBySliderValue(value);
	}
	/**
	 * 放大
	 * */
	public void zoomOut(){
		int value = slider.getValue();
		if (value > 150) {
			value = 160;
		} else {
			value += 10;
		}
		slider.setValue(value);
		ZoomSvgBySliderValue(value);
	}
     
}
