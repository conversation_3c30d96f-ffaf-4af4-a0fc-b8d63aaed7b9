package com.tellhow.czp.widget;

import java.awt.Component;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;


/**
 * 电压等级信息提示Panel
 * <AUTHOR>
 *
 */
public class VoltSignWidget extends org.beryl.gui.widgets.Panel {
	private VoltSignPanel voltSignPanel;
	
	public VoltSignWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		voltSignPanel = new VoltSignPanel();
	}


	@Override
	public Component getWidget() {
		return voltSignPanel;
	}
}
