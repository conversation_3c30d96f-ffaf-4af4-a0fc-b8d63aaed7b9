package com.tellhow.czp.widget;

import java.awt.Component;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;

import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreePath;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;

import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.system.CreatePowerStationToplogy;

public class LinesTreeWidget extends Widget {
	private JScrollPane sp_Line = new JScrollPane();
	private JTree lineTree = null;
	private DefaultMutableTreeNode root = null;
	public LinesTreeWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		root = new DefaultMutableTreeNode("系统图");
		lineTree = new JTree(root);
		lineTree.expandRow(0);
		lineTree.expandRow(1);
	    lineTree.setToggleClickCount(1);
	    lineTree.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
            	if(TempTicket.getTempTicket()==null){
        			JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
        			splitPane.setDividerLocation(1.0);
        		}
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    if(simplePath!=null){
	                    DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
	                    if (lastNode.isLeaf()) {
	                        DefaultSimpleNode dsn = (DefaultSimpleNode) lastNode.getUserObject();
	                        //如果系统接线图已经打开，则只切换视图
	                        String fileName = dsn.getItemName();
	                        String filePath = dsn.getItemCode();
	                    	CreatePowerStationToplogy.createSVGPanel("", fileName, fileName, filePath);
	                    }
                    }
                }
            }
        });
	    sp_Line.setViewportView(lineTree);
	}

	@Override
	public void finalizeConstruction() throws GUIException {
		lineTree.expandPath(new TreePath(root));
		lineTree.setRootVisible(false);
	}

	@Override
	public Component getWidget() {
		return sp_Line;
	}
	
	public void setProperty(String name, Object value) throws GUIException {
		File file = new File((String)value);
		if(file.exists()) {
			DefaultSimpleNode dsn = new DefaultSimpleNode();
            dsn.setItemCode(file.getPath());
            dsn.setItemName(name);
            DefaultMutableTreeNode childs = new DefaultMutableTreeNode(dsn);
			root.add(childs);
		}
	}
}
