package com.tellhow.czp.widget;

import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.Font;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Vector;

import javax.swing.JComboBox;
import javax.swing.JTabbedPane;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.component.ImagePanel;

import com.tellhow.czp.sysconfig.UnitManager;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

import czprule.model.PowerDevice;
import czprule.stationstartup.InitDeviceStatus;
import czprule.stationstartup.StationStartupManager;
import czprule.system.CBSystemConstants;
import czprule.system.DeviceSVGPanelUtil;

/**
 * 状态显示下拉框，默认实时态
 * */
public class StatusListRTWidget extends Widget implements ItemListener{
	private ImagePanel attributepanel=new ImagePanel(new javax.swing.ImageIcon(ImagePanel.class.getResource("/tellhow/icons/bg2.png")));
	private JComboBox nodeattributecomboBox=new JComboBox();
//	private TransTreeWidget ttw=null;
	
	public JComboBox getNodeattributecomboBox() {
		return nodeattributecomboBox;
	}

	public void setNodeattributecomboBox(JComboBox nodeattributecomboBox) {
		this.nodeattributecomboBox = nodeattributecomboBox;
	}

	//private ImageSlider slider;
	public StatusListRTWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		//slider =new ImageSlider(new javax.swing.ImageIcon(ImagePanel.class.getResource("/tellhow/icons/bg2.png")), 1, 160, 80);
		// TODO Auto-generated constructor stub
		//slider.setOpaque(false);
		//slider.setPreferredSize(new Dimension(100, 20));
		Vector<UnitManager> organkindlist=new Vector<UnitManager>();//机构属性
		//机构属性
		 UnitManager 	unitManager10= new UnitManager();
	  	 UnitManager	unitManager11= new UnitManager();
	  	 UnitManager 	unitManager12= new UnitManager();

	  	 if(CBSystemConstants.cardstatus.equals("1")) { //当前用户设置为默认实时态
	  		if(CBSystemConstants.roleCode.equals("0")) {
				 
				 unitManager11.setCode("1");
				 unitManager11.setName("实时态");
				 organkindlist.add(unitManager11);
				 unitManager10.setCode("0");
				 unitManager10.setName("模拟态"); //调度拟票态
				 organkindlist.add(unitManager10);
		  	 }
		  	 else if(CBSystemConstants.roleCode.equals("2")) {
				 
				 unitManager11.setCode("1");
				 unitManager11.setName("实时态");
				 organkindlist.add(unitManager11);
				 unitManager12.setCode("2");
				 unitManager12.setName("模拟态"); //监控拟票态
				 organkindlist.add(unitManager12);
		  	 }
		  	 else if(CBSystemConstants.roleCode.equals("3")) {
				 
				 unitManager11.setCode("1");
				 unitManager11.setName("实时态");
				 organkindlist.add(unitManager11);
				 unitManager10.setCode("0");
				 unitManager10.setName("模拟态"); //调度拟票态
				 organkindlist.add(unitManager10);
		  	 }
		  	 else {
				 
				 unitManager11.setCode("1");
				 unitManager11.setName("实时态");
				 organkindlist.add(unitManager11);
				 unitManager10.setCode("0");
				 unitManager10.setName("模拟态"); //调度拟票态
				 organkindlist.add(unitManager10);
		  	 }
	  	 }
	  	 else {
		  	 if(CBSystemConstants.roleCode.equals("0")) {
				 unitManager10.setCode("0");
				 unitManager10.setName("模拟态"); //调度拟票态
				 organkindlist.add(unitManager10);
				 unitManager11.setCode("1");
				 unitManager11.setName("实时态");
				 organkindlist.add(unitManager11);
		  	 }
		  	 else if(CBSystemConstants.roleCode.equals("2")) {
				 unitManager12.setCode("2");
				 unitManager12.setName("模拟态"); //监控拟票态
				 organkindlist.add(unitManager12);
				 unitManager11.setCode("1");
				 unitManager11.setName("实时态");
				 organkindlist.add(unitManager11);
		  	 }
		  	 else  if(CBSystemConstants.roleCode.equals("3")) {
				 unitManager10.setCode("0");
				 unitManager10.setName("模拟态"); //调度拟票态
				 organkindlist.add(unitManager10);
				 unitManager11.setCode("1");
				 unitManager11.setName("实时态");
				 organkindlist.add(unitManager11);
		  	 }
		  	 else {
				 unitManager10.setCode("0");
				 unitManager10.setName("模拟态"); //调度拟票态
				 organkindlist.add(unitManager10);
				 unitManager11.setCode("1");
				 unitManager11.setName("实时态");
				 organkindlist.add(unitManager11);
		  	 }
	  	 }
		 
		 nodeattributecomboBox=new JComboBox(organkindlist);//机构属性
		 nodeattributecomboBox.setFont(new Font("宋体", Font.PLAIN, 12));
		 //attributepanel.add(nodeattributecomboBox);
		 attributepanel.add(nodeattributecomboBox,BorderLayout.CENTER);
		 nodeattributecomboBox.addItemListener(this);
		 
//		 ttw=new TransTreeWidget(parent, name);
		 
	}
	@Override
	public void itemStateChanged(ItemEvent e) {
		// TODO Auto-generated method stub
		/*if (e.getStateChange() == ItemEvent.SELECTED) {
		    //JComboBox jcb = (JComboBox) e.getSource();
		    //System.out.println((String) (jcb.getSelectedItem()));
		    //System.out.println(jcb.getSelectedIndex());
			 System.out.println("test1...s...");
		}else{
			System.out.println("www......");
		}*/
		
		if(e.getStateChange() == ItemEvent.SELECTED){
			UnitManager getuse=(UnitManager)nodeattributecomboBox.getSelectedItem();
			String getcode=getuse.getCode();
			String getname=getuse.getName();
			if(getcode.equals("0")){
				CBSystemConstants.cardstatus="0";
			}else if(getcode.equals("1")){
				CBSystemConstants.cardstatus="1";
			}else{
				CBSystemConstants.cardstatus="2";
			}
//			InitDeviceStatus ie=new InitDeviceStatus();
			
//			if(names.equals("")){
//				ShowMessage.view("没有打开厂站图，获取状态失败！");
//			}
			refushing();
			//System.out.println(getcode+"+++++++++"+getname+"++++++++++++"+CBSystemConstants.cardstatus);
		}
	}
	@Override
	public Component getWidget() {
		return attributepanel;
	}

	public static void refushing(){
		JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
		SVGCanvasPanel panelsel= SystemConstants.getGuiBuilder().getActivateSVGPanel();
		String stationIDsel = "";
		if(panelsel != null) {
			stationIDsel = panelsel.getStationID();
			if(!stationIDsel.equals("")) {
				refushingStation(stationIDsel);
			}
		}
		
		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
			String stationID = panel.getStationID();
			if(!stationID.equals("") && !stationID.equals(stationIDsel)) {
				refushingStation(stationID);
			}
		}
		SvgUtil.clear(); 
	}
	
	public static void refushingStation(String stationID){
		PowerDevice pd=null;
		Map statusMap= new HashMap();
		Map deviceMap=(Map)CBSystemConstants.getMapPowerStationDevice().get(stationID);
		for (Iterator iter = deviceMap.values().iterator(); iter.hasNext();) {
    		pd=(PowerDevice) iter.next();
    		statusMap.put(pd.getPowerDeviceID(), pd.getDeviceStatus());
		}
		InitDeviceStatus ie = new InitDeviceStatus();
		ie.initStatus_ToCache(stationID);
		
		
		long start=System.currentTimeMillis();
    	for (Iterator iter = deviceMap.values().iterator(); iter.hasNext();) {
    		pd=(PowerDevice) iter.next();
    		if(!pd.getDeviceStatus().equals(statusMap.get(pd.getPowerDeviceID())))
    			DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
		}
    	long end=System.currentTimeMillis();
		System.out.println("所消耗的时间是:"+(end-start)+"ms");
	}
}
