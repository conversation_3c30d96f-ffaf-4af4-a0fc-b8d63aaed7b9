package com.tellhow.czp.widget;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTree;
import javax.swing.plaf.basic.BasicArrowButton;
import javax.swing.plaf.basic.BasicComboBoxUI;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeCellRenderer;
import javax.swing.tree.TreeModel;
import javax.swing.tree.TreePath;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.mainframe.svgFileChoose;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

/**
 * 变电站-线路树列表面板
 * <AUTHOR>
 */
public class LineTreeWidget extends Widget {
	private JPanel panel=new JPanel();
	private JScrollPane sp_Trans;
	private JPopupMenu treePopupMenu;
	private JMenuItem searchMenuItem;
	private String lastSearchText = null;
	private JTree transTree;
	private List list = null;
	private String transsql="";
	public LineTreeWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		sp_Trans = new JScrollPane();
		 refresh();

        searchMenuItem = new JMenuItem("查找");
        searchMenuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				String searchText = JOptionPane.showInputDialog(SystemConstants.getMainFrame(), "输入要查找的线路名称", lastSearchText);
				if (searchText == null)
					return;
				else if (searchText.equals("")) {
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "查询条件不能为空！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
					return;
				}
				lastSearchText = searchText;
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
				}
			}
		});
        treePopupMenu = new JPopupMenu();
        treePopupMenu.add(searchMenuItem);
        
        
        panel.setLayout(new BorderLayout());
        panel.add(sp_Trans,BorderLayout.CENTER);
        panel.setMaximumSize(new Dimension(250,500));
        panel.setMinimumSize(new Dimension(250,500));
        panel.setPreferredSize(new Dimension(250,500));
        addSearchItem();
        setleafIcon(transTree);
	}
	private void clickTreeNode(TreePath simplePath) {
		if(TempTicket.getTempTicket()==null){
			JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(1.0);
		}
		DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
        if (lastNode.isLeaf()) {
            DefaultSimpleNode dsn = (DefaultSimpleNode) lastNode.getUserObject();
            String filePath = "";
            String fileName = "";
            String stationID = "";
            String stationName = "";
            if(list == null) {
            	filePath = dsn.getItemCode();
                fileName = dsn.getItemName();
                stationName = dsn.getItemName().substring(0,dsn.getItemName().indexOf("."));
            }
            else {
            	SystemConstants.OPEN_MAP = dsn.getItemName();
                List<SVGFile> fileList = SystemConstants.getSVGFileByLineID(dsn.getItemCode());
                if(fileList.size() == 0) {
                	while(SystemConstants.threadLoadFile.isAlive() && !SystemConstants.OPEN_MAP.equals("")) {
                		try {
							Thread.sleep(100);
						} catch (InterruptedException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
                		fileList = SystemConstants.getSVGFileByLineID(dsn.getItemCode());
                	}
                }
                if(fileList.size() == 0) {
                	if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive())
                		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
                	else{
                		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在[" + dsn.getItemName() + "]单线图！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
                		return;
//                		svgFileChoose sfc = new svgFileChoose(SystemConstants.getMainFrame(),true);
//                		String chooseFileName = sfc.bulidsvgFileTree();
//                		this.linkSvgFile(fileName, chooseFileName);
//            			SVGFile svgFile = new SVGFile();
//            			svgFile.setFileName(chooseFileName);
//            			svgFile.setFilePath(SystemConstants.FILE_SVGMAP_PATH+chooseFileName);
//            			svgFile.setAlwaysOpen(false);
//                		fileList.add(svgFile);
                	}
                }
                else if(fileList.size() == 1) {
                	filePath = fileList.get(0).getFilePath();
                    fileName = fileList.get(0).getFileName();
                    stationID = fileList.get(0).getLineID();
                }
                else {
                	Object[] options = fileList.toArray(); 
                	int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
                	if(i == -1)
                		return;
                	filePath = fileList.get(i).getFilePath();
                    fileName = fileList.get(i).getFileName();
                    stationID = fileList.get(i).getLineID();
                }
               
//                stationName = fileName.substring(0, fileName.lastIndexOf(".")).replace(".fac", "").replace(".pic", "");
//                if(stationName.indexOf(".") > 0)
//                	stationName = stationName.substring(stationName.indexOf(".")+1);
                stationName = dsn.getItemName();
            }
            
            //8缓存
            SvgP svgp=new SvgP(stationID, stationName, fileName, filePath);
            CBSystemConstants.svgps.put(stationID, svgp);
            JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
    		int n=tabbedPane.getComponentCount();
    		for(int i=0;i<n;i++){
    			SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
				String station=sel.getStationID();
				boolean isdestry=sel.isIsdestry();
				if(station.equals(stationID)&&isdestry==true){
					File svgMapFile = new File(filePath);
					sel.loadSvgFile(svgMapFile);
					sel.setIsdestry(false);
				}
    		}
    		//
			CreatePowerStationToplogy.createSVGPanel(stationID, stationName, fileName, filePath);
		}
	}
	
	public void refresh() {
		OperateTicketPubTree ottl = new OperateTicketPubTree(3);
		String orgaID = CBSystemConstants.getUser().getOrganID()==null?"":CBSystemConstants.getUser().getOrganID();
		String lineTreeSql = CZPImpl.getPropertyValue("LineTreeSql");
		if(lineTreeSql != null)
			transsql = lineTreeSql.replace("[organ]", orgaID);
		if(transsql == null || transsql.equals(""))
			//edit 2014.7.2
			transsql = "select decode(b.station_type, '0', '', '1', '', '2', '', '3','', '4', '', '5', '','6', '') area, translate(b.station_name, '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', '-') as station,  a.id nodecode,  replace(translate(a.name, '10kV', ' '),' ','') as nodename  from "+CBSystemConstants.equipUser+"t_C_ACLINEEND a, "+CBSystemConstants.equipUser+"t_SUBSTATION b, "+CBSystemConstants.equipUser+"t_VOLTAGELEVEL c  where a.st_id = b.station_id  and a.voltage_id = c.voltage_id and c.voltage_code = '10' order by area,station desc, nodename asc";
		transsql=transsql.toLowerCase().replaceAll("equip\\.", CBSystemConstants.equipUser).replaceAll("opcard\\.", CBSystemConstants.opcardUser);
		list = ottl.getList(transsql, 1);
        transTree = ottl.buildLineTree("厂站", list, false);//由sql动态构建树
        transTree.setRootVisible(false);
        transTree.setToggleClickCount(1);
        
        
        sp_Trans.setViewportView(transTree);
//        getywd();
        transTree.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    clickTreeNode(simplePath);
                    //缓存解决方法（暂定）
                    
                    JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
                    int n=tabbedPane.getComponentCount();
                    if(n>8){
                    	for(int i=0;i<n;i++){
	            			SVGCanvasPanel selpanel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
	            			String stationID = selpanel.getStationID();
	            			boolean isdestry = selpanel.isIsdestry();
	            			if(isdestry==false){
		            			final SVGCanvas canvas = selpanel.getSvgCanvas();
		            		    new Thread(new Runnable() {
		        		        	public void run() {
		        		        		canvas.destroyCanvas();
		        		        		System.gc();
		        		        	}
		    			 	    }).start();
		            		    selpanel.setIsdestry(true);
		            		    break;
	            			}
                    	}
                    }
                    int jj=0;
                    //
                }
            }
            
            public void mouseReleased(MouseEvent e) {
            	TreePath path = transTree.getPathForLocation(e.getX(), e.getY());
				if (path == null) {
					if (e.getButton() == 3)
						treePopupMenu.show(transTree, e.getX(), e.getY());
					return;
				}
            }
        });
	}
	
	private void setleafIcon(JTree tree) {
	   TreeCellRender render = new TreeCellRender();
	   transTree.setCellRenderer(render); 
	}
	//搜索栏的添加
	private void addSearchItem() {
	    /*JPanel	searchPanel=new JPanel(new BorderLayout());
	    final JTextField text = new JTextField();
	    JButton btn=new JButton("搜索");
	    searchPanel.add(text ,BorderLayout.CENTER);
	    searchPanel.add(btn,BorderLayout.EAST);
		panel.add(searchPanel,BorderLayout.NORTH);
		btn.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent arg0) {
				String searchText=text.getText();
				if(searchText.equals("")){
					return;
				}
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
				}
			}
		});*/
		JLabel label = new JLabel("搜索线路：");
		JPanel searchPanel = new JPanel();
		searchPanel.setBackground(Color.white);
		searchPanel.setLayout(new BorderLayout());
		searchPanel.add(label,BorderLayout.WEST);
		final JComboBox combo = new JAutoCompleteComboBox();
		
		String sql = CZPImpl.getPropertyValue("LineSearchSql");
		
		if(sql == null)
			//edit 2014.7.2
			sql = "select t.id,t.name from "+CBSystemConstants.equipUser+"t_c_aclineend t,"+CBSystemConstants.equipUser+"t_voltagelevel a where t.voltage_id=a.voltage_id order by a.voltage_code desc,t.name";
		else{
			sql=sql.toLowerCase().replaceAll("equip\\.", CBSystemConstants.equipUser);
		}
		fillComboBox(combo, sql);
		combo.setSelectedIndex(-1);
		searchPanel.add(combo,BorderLayout.CENTER);
		panel.add(searchPanel,BorderLayout.NORTH);
		
		combo.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				if(combo.getSelectedItem() instanceof java.lang.String){
					return;
				}
				String searchText = ((CodeNameModel) combo.getSelectedItem()).getName();
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
					if(!combo.getName().equals("selecting"))
						clickTreeNode(treePath);
				}
			}
		});
		
//		combo.addItemListener(new ItemListener() {
//			@Override
//			public void itemStateChanged(ItemEvent e) {
//			    if(e.getItem() instanceof java.lang.String){
//					return;
//				}
//				String searchText = ((CodeNameModel) e.getItem()).getName();
//				Object root = transTree.getModel().getRoot();
//				TreePath treePath = new TreePath(root);
//				
//				treePath = findInPath(transTree, treePath, searchText);
//				if (treePath == null) {
//					transTree.setSelectionRow(0);
//					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
//				}
//				if (treePath != null) {
//					transTree.setSelectionPath(treePath);
//					transTree.scrollPathToVisible(treePath);
//				}
//			}
//		});
	}
	
	public void linkSvgFile(String lineName,String svgFile){
		
		
		
	}
	

	public void fillComboBox(JComboBox comboBox, String sql) {
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		List result = DBManager.queryForList(sql);
		for(int i = 0; i < result.size(); i++) {
    	
			if(((Map)result.get(i)).get("nodecode") == null || ((Map)result.get(i)).get("nodename") ==null)
				continue;
			cnm=new CodeNameModel();
			cnm.setCode(((Map)result.get(i)).get("nodecode").toString());
			cnm.setName(((Map)result.get(i)).get("nodename").toString());
			model.addElement(cnm);
		}
		comboBox.setModel(model);
	}
	private TreePath findInPath(JTree tree, TreePath treePath, String str) {
		Object object = treePath.getLastPathComponent();
		if (object == null) {
			return null;
		}
		

		String value = object.toString();
		if (value == null) {
			return null;
		}
		/*if (value.toLowerCase().indexOf(str.toLowerCase()) >= 0) {
			return treePath;
		}*/
		if (value.toLowerCase().equals(str.toLowerCase()) ) {
			return treePath;
		}else {
			TreeModel model = tree.getModel();
			int n = model.getChildCount(object);
			int curRow = tree.getRowForPath(tree.getSelectionPath());
			for (int i = 0; i < n; i++) {
				Object child = model.getChild(object, i);
				TreePath path = treePath.pathByAddingChild(child);
				path = findInPath(tree, path, str);
				if (path != null && tree.getRowForPath(path) > curRow) {
					return path;
				}
			}
			return null;
		}
	}

	public LineTreeWidget(Widget parent, String name, String preset)
			throws GUIException {
		super(parent, name, preset);
	}

	@Override
	public Component getWidget() {
		return panel;
	}
	

}

class TreeCellRender extends JLabel implements TreeCellRenderer{
	@Override
	public Component getTreeCellRendererComponent(JTree tree, Object value,
			boolean sel, boolean expanded, boolean leaf, int row,
			boolean hasFocus) {
		DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
		int level=node.getLevel();
//		if(level==2){
//			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree2.png")));
//		}else if(level==1){
//			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree1.png")));
//		}else {
//			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree3.png")));
//		}
//		if(level==2){
//		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree2.png")));
//	}else if(level==1){
//		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree1.png")));
//	}else {
//		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree3.png")));
//	}
	if(level==1){
	}
	else if(level==2){
		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree1.png")));
	}else if(level==3){
		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree2.png")));
	}else {
		this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree3.png")));
	}
		if(sel){
			this.setOpaque(true);
			this.setForeground(Color.white);
			this.setBackground(Color.blue);
		}else{
			this.setOpaque(false);
			this.setForeground(Color.black);
			this.setBackground(Color.white);
		}
		this.setText(value.toString());
		return this;
	}
	
	

}



class ColorsArrowUI extends BasicComboBoxUI {

    @Override 
    protected JButton createArrowButton() {
    	JButton bt = new BasicArrowButton(
                BasicArrowButton.SOUTH,
                Color.cyan, Color.magenta,
                Color.yellow, Color.blue);
    	bt.setVisible(false);
        return bt;
    }
}
