package com.tellhow.czp.widget;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeModel;
import javax.swing.tree.TreePath;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

public class InOutLineTreeWidget extends Widget {
	private JPanel panel = new JPanel();
	private JScrollPane sp_Trans;
	private JPopupMenu treePopupMenu;
	private JMenuItem searchMenuItem;
	private String lastSearchText = null;
	private JTree transTree;
	private List list = null;

	public InOutLineTreeWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		OperateTicketPubTree ottl = new OperateTicketPubTree(3);
//		String inOutLinesql = "select '线路' as line, d.voltage_code||'kV',"
//				+ "c.line_id nodecode,c.line_name nodename from "+CBSystemConstants.opcardUser+"t_e_lineinfo c,"+CBSystemConstants.opcardUser+"t_e_voltagelevel d where c.voltage_id = d.voltage_id and d.voltage_value>=35 and c.line_id in (select a.line_id from "+CBSystemConstants.opcardUser+"t_e_substationtopology a group by a.line_id) order by line, d.voltage_value desc, nodename asc";
		//edit 2014.6.25
		String inOutLinesql = OPEService.getService().InOutLineTreeWidgetSql();
		list = ottl.getList(inOutLinesql, 1);
		transTree = ottl.buildLineTree("线路", list, false);

		transTree.addMouseListener(new MouseAdapter() {

			public void mouseClicked(MouseEvent e) {
				if (e.getClickCount() == 2 && e.getButton() == 1) {
					JTree tree = (JTree) e.getSource();
					TreePath simplePath = tree.getSelectionPath();
					clickTreeNode(simplePath);
				}
			}

			public void mouseReleased(MouseEvent e) {
				TreePath path = transTree.getPathForLocation(e.getX(), e.getY());
				if (path == null) {
					if (e.getButton() == 3)
						treePopupMenu.show(transTree, e.getX(), e.getY());
					return;
				}
			}
		});
		transTree.setRootVisible(false);
		transTree.setToggleClickCount(1);
		sp_Trans = new JScrollPane();
		sp_Trans.setViewportView(transTree);

		searchMenuItem = new JMenuItem("查找");
		searchMenuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				String searchText = JOptionPane.showInputDialog(
						SystemConstants.getMainFrame(), "输入要查找的线路名称",
						lastSearchText);
				if (searchText == null)
					return;
				else if (searchText.equals("")) {
					JOptionPane.showMessageDialog(
							SystemConstants.getMainFrame(), "查询条件不能为空！",
							SystemConstants.SYSTEM_TITLE,
							JOptionPane.WARNING_MESSAGE);
					return;
				}
				lastSearchText = searchText;
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree,
							transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
				}
			}
		});
		treePopupMenu = new JPopupMenu();
		treePopupMenu.add(searchMenuItem);

		panel.setLayout(new BorderLayout());
		panel.add(sp_Trans, BorderLayout.CENTER);
		addSearchItem();
		setleafIcon(transTree);
	}

	private void clickTreeNode(TreePath simplePath) {
		if(TempTicket.getTempTicket()==null){
			JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(1.0);
		}
		DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath
				.getLastPathComponent();
		if (lastNode.isLeaf()) {
			DefaultSimpleNode dsn = (DefaultSimpleNode) lastNode
					.getUserObject();// 获取线路信息
			List<String> filePathList = new ArrayList<String>();
			List<String> fileNameList = new ArrayList<String>();
			String lineId = dsn.getItemCode();

			Map<String,String> stationMap = new HashMap<String,String>();
			Statement stam = null;
			ResultSet rs = null;
//			String sql = "select a.station_name,a.station_id from "+CBSystemConstants.opcardUser+"t_e_substation a where a.station_id in(select t.station_id from "+CBSystemConstants.opcardUser+"t_e_substationtopology t where t.line_id = '"
//					+ lineId + "')";
			//edit 2014.6.26
			String sql = OPEService.getService().InOutLineTreeWidgetSql2()+ lineId + "')";
			Connection conn = DBManager.getConnection();
			try {
				stam = conn.createStatement();
				rs = stam.executeQuery(sql);
				while(rs.next()){
					stationMap.put(rs.getString("station_id"), rs.getString("station_name"));
				}
				stam.close();
				conn.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}

			String filePath = "";
			String fileName = "";
			String stationID = "";
			String stationName = "";
			
			if (list == null) {
				filePath = dsn.getItemCode();
				fileName = dsn.getItemName();
				stationName = dsn.getItemName().substring(0,
						dsn.getItemName().indexOf("."));
			} else {
				if(stationMap.size() <= 0){
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(),"不存在" + dsn.getItemName() + "连接的厂站！",SystemConstants.SYSTEM_TITLE,JOptionPane.WARNING_MESSAGE);
					return;
				}
				for(Map.Entry<String, String> stationEntry : stationMap.entrySet()){
					List<SVGFile> fileList = SystemConstants.getSVGFileByStationID(stationEntry.getKey());
					if (fileList.size() == 0) {
						if (SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive())
							JOptionPane.showMessageDialog(SystemConstants.getMainFrame(),"接线图正在加载中，请稍后打开！",SystemConstants.SYSTEM_TITLE,JOptionPane.WARNING_MESSAGE);
						else
							JOptionPane.showMessageDialog(SystemConstants.getMainFrame(),"不存在" + stationEntry.getValue() + "一次接线图！",SystemConstants.SYSTEM_TITLE,JOptionPane.WARNING_MESSAGE);
						continue;
					} else if (fileList.size() == 1) {
						filePath = fileList.get(0).getFilePath();
						fileName = fileList.get(0).getFileName();
					} else {
						Object[] options = fileList.toArray();
						int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形",SystemConstants.SYSTEM_TITLE,JOptionPane.DEFAULT_OPTION,JOptionPane.WARNING_MESSAGE, null, options,options[0]);
						if (i == -1)
							continue;
						filePath = fileList.get(i).getFilePath();
						fileName = fileList.get(i).getFileName();
					}
					stationID = stationEntry.getKey();
					stationName = CBSystemConstants.getPowerStation(stationID).getPowerDeviceName();
					CreatePowerStationToplogy.createSVGPanel(stationID, stationName,
							fileName, filePath);
				}
			}
		}
	}

	private void setleafIcon(JTree tree) {
		TreeCellRender render = new TreeCellRender();
		transTree.setCellRenderer(render);
	}

	// 搜索栏的添加
	private void addSearchItem() {
		JLabel label = new JLabel("搜索线路：");
		JPanel searchPanel = new JPanel();
		searchPanel.setBackground(Color.white);
		searchPanel.setLayout(new BorderLayout());
		searchPanel.add(label, BorderLayout.WEST);
		final JComboBox combo = new JAutoCompleteComboBox();
		// combo.setUI(new ColorArrowUI());
//		fillComboBox(
//				combo,
//				"select t.LINE_ID,t.LINE_NAME from "+CBSystemConstants.opcardUser+"t_e_lineinfo t,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a where t.voltage_id=a.voltage_id and a.voltage_code in ('500','220','110') and t.LINE_NAME not like '%T接%' and t.LINE_NAME not like '%系统%' order by a.voltage_code desc,t.LINE_NAME");
		//edit 2014.6.25
		fillComboBox(combo,OPEService.getService().InOutLineTreeWidgetSql1());
		combo.setSelectedIndex(-1);
		searchPanel.add(combo, BorderLayout.CENTER);
		panel.add(searchPanel, BorderLayout.NORTH);

		combo.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				if (combo.getSelectedItem() instanceof java.lang.String) {
					return;
				}
				String searchText = ((CodeNameModel) combo.getSelectedItem())
						.getName();
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);

				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree,
							transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
					clickTreeNode(treePath);
				}
			}
		});

		// combo.addItemListener(new ItemListener() {
		// @Override
		// public void itemStateChanged(ItemEvent e) {
		// if(e.getItem() instanceof java.lang.String){
		// return;
		// }
		// String searchText = ((CodeNameModel) e.getItem()).getName();
		// Object root = transTree.getModel().getRoot();
		// TreePath treePath = new TreePath(root);
		//
		// treePath = findInPath(transTree, treePath, searchText);
		// if (treePath == null) {
		// transTree.setSelectionRow(0);
		// treePath = findInPath(transTree, transTree.getSelectionPath(),
		// searchText);
		// }
		// if (treePath != null) {
		// transTree.setSelectionPath(treePath);
		// transTree.scrollPathToVisible(treePath);
		// }
		// }
		// });
	}

	public void fillComboBox(JComboBox comboBox, String sql) {
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm = null;
		SqlRowSet set = DBManager.queryForRowSet(sql);
		while (set.next()) {
			cnm = new CodeNameModel();
			cnm.setCode(set.getString(1));
			cnm.setName(set.getString(2));
			model.addElement(cnm);
		}
		comboBox.setModel(model);
	}

	private TreePath findInPath(JTree tree, TreePath treePath, String str) {
		Object object = treePath.getLastPathComponent();
		if (object == null || object.toString()==null) {
			return null;
		}

		String value = object.toString();
		if (value.toLowerCase().indexOf(str.toLowerCase()) >= 0) {
			return treePath;
		} else {
			TreeModel model = tree.getModel();
			int n = model.getChildCount(object);
			int curRow = tree.getRowForPath(tree.getSelectionPath());
			for (int i = 0; i < n; i++) {
				Object child = model.getChild(object, i);
				TreePath path = treePath.pathByAddingChild(child);

				path = findInPath(tree, path, str);
				if (path != null && tree.getRowForPath(path) > curRow) {
					return path;
				}
			}
			return null;
		}
	}

	public InOutLineTreeWidget(Widget parent, String name, String preset)
			throws GUIException {
		super(parent, name, preset);
	}

	@Override
	public Component getWidget() {
		return panel;
	}
}
