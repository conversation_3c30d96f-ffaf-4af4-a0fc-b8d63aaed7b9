package com.tellhow.czp.svg.document;

import java.util.ArrayList;
import java.util.List;

import javax.swing.JTabbedPane;

import org.apache.batik.bridge.UpdateManager;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.svg.SVGDocument;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.system.CBSystemConstants;

public class DefaultSVGDocumentResolver extends SVGDocumentResolver {

	protected static String svgNS = "http://www.w3.org/2000/xmlns/";
	protected static String xlinkNS = "http://www.w3.org/1999/xlink";
	
	private static String MeasurementValue_Layer = "MeasurementValue_Layer";
	private static String Substation_Layer = "Substation_Layer";
	private static String Head_Layer = "Head_Layer";
	private static String Other_Layer = "Other_Layer";
	
	@Override
	public Element resolveSvgElement(Document document) {
		return document.getDocumentElement();
	}
	/**
	 * 查找设备所在的SVG文档根节点
	 */
	public Element resolveSvgElement(PowerDevice device) {
		//先在当前文档查找
		if(SystemConstants.getGuiBuilder() == null)
			return null;
		SVGCanvasPanel curpanel = SystemConstants.getGuiBuilder().getActivateSVGPanel();
		if(curpanel != null && curpanel.getSvgDocument() != null) {
			Element rootElement = curpanel.getSvgDocument().getDocumentElement();
			String cgeNS =  rootElement.getAttributeNS(svgNS, "cge");
			NodeList nodeList = rootElement.getElementsByTagNameNS(cgeNS, "TPSR_Ref");
	    	for (int j = 0; j < nodeList.getLength(); j++) {
	    		Element psrElement = (Element)nodeList.item(j);
	    		if(psrElement.getAttribute("TObjectID").equals(device.getPowerDeviceID())) {
	    			return rootElement;
	    		}
			}
		}
		//如果当前文档没有，遍历所有打开的文档查找
		JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
			if(panel.getSvgDocument() == null || panel.getFilePath().equals(curpanel.getFilePath()))
				continue;
			Element rootElement = panel.getSvgDocument().getDocumentElement();
			String cgeNS =  rootElement.getAttributeNS(svgNS, "cge");
			NodeList nodeList = rootElement.getElementsByTagNameNS(cgeNS, "TPSR_Ref");
			if(nodeList == null || nodeList.getLength()==0) 
				continue;
	    	for (int j = 0; j < nodeList.getLength(); j++) {
	    		Element psrElement = (Element)nodeList.item(j);
	    		if(psrElement.getAttribute("TObjectID").equals(device.getPowerDeviceID())) {
	    			if(CBSystemConstants.isInversion)
	    				SystemConstants.getGuiBuilder().getSVGJTabbedPane().setSelectedComponent(panel);
	    			return rootElement;
	    		}
			}
		}
		return null;
	}
	
	@Override
	public String getMapType(Document document) {
		Element root = resolveSvgElement(document);
		return root.getAttribute("MapType");
	}
	
	@Override
	public String getStationID(Document document) {
		Element root = resolveSvgElement(document);
		if(getMapType(document).equals("line"))
			return getLineID(document);
		else
			return root.getAttribute("StationID");
	}
	@Override
	public String getLineID(Document document) {
		Element root = resolveSvgElement(document);
		return root.getAttribute("LineID");
	}
	public String getViewbox(Document document){
		Element root = resolveSvgElement(document);
		return root.getAttribute("viewBox");
	}
	
	@Override
	public Element getDeviceGroupElement(PowerDevice device) {
		if(device == null) 
			return null;
		String powerDeviceID = device.getPowerDeviceID();
    	Element rootElement = resolveSvgElement(device);
    	if(rootElement == null)
    		return null;
    	String cgeNS =  rootElement.getAttributeNS(svgNS, "cge");
    	NodeList nodeList = rootElement.getElementsByTagNameNS(cgeNS, "TPSR_Ref");
    	for (int i = 0; i < nodeList.getLength(); i++)
		{
    		Element psrElement = (Element)nodeList.item(i);
    		Element graphElement = getDeviceGraphElement((Element)psrElement.getParentNode().getParentNode());
    		if(!graphElement.getNodeName().equals("text") && psrElement.getAttribute("TObjectID").equals(powerDeviceID))
    			return (Element)psrElement.getParentNode().getParentNode();
		}
    	for (int i = 0; i < nodeList.getLength(); i++)
		{
    		Element psrElement = (Element)nodeList.item(i);
    		if(psrElement.getAttribute("TObjectID").equals(powerDeviceID))
    			return (Element)psrElement.getParentNode().getParentNode();
		}
    	return null;
	}
	
	public List<Element> getDeviceGroupElements(PowerDevice device) {
		List<Element> list = new ArrayList<Element>();
		if(device == null) 
			return null;
		String powerDeviceID = device.getPowerDeviceID();
    	Element rootElement = resolveSvgElement(device);
    	if(rootElement == null)
    		return null;
    	String cgeNS =  rootElement.getAttributeNS(svgNS, "cge");
    	NodeList nodeList = rootElement.getElementsByTagNameNS(cgeNS, "TPSR_Ref");
    	for (int i = 0; i < nodeList.getLength(); i++)
		{
    		Element psrElement = (Element)nodeList.item(i);
    		Element graphElement = getDeviceGraphElement((Element)psrElement.getParentNode().getParentNode());
    		if(!graphElement.getNodeName().equals("text") && psrElement.getAttribute("TObjectID").equals(powerDeviceID))
    			list.add((Element)psrElement.getParentNode().getParentNode());
		}
    	for (int i = 0; i < nodeList.getLength(); i++)
		{
    		Element psrElement = (Element)nodeList.item(i);
    		if(psrElement.getAttribute("TObjectID").equals(powerDeviceID))
    			list.add((Element)psrElement.getParentNode().getParentNode());
		}
    	return list;
	}
	
	@Override
	public Element getDeviceGroupElement(Document doc, String equipID) {
    	Element rootElement = doc.getDocumentElement();
    	String cgeNS =  rootElement.getAttributeNS(svgNS, "cge");
    	NodeList nodeList = rootElement.getElementsByTagNameNS(cgeNS, "TPSR_Ref");
    	for (int i = 0; i < nodeList.getLength(); i++)
		{
    		Element psrElement = (Element)nodeList.item(i);
    		if(psrElement.getAttribute("TObjectID").equals(equipID))
    			return (Element)psrElement.getParentNode().getParentNode();
		}
    	return null;
	}
	
	@Override
	public Element getDeviceGraphElement(Element element) {
		Element deviceGroupElement = element;
		Element deviceGraph = null;
		if(deviceGroupElement.getElementsByTagName("use").getLength() != 0)
			deviceGraph = (Element)deviceGroupElement.getElementsByTagName("use").item(0);
		else if(deviceGroupElement.getElementsByTagName("path").getLength() != 0)
			deviceGraph = (Element)deviceGroupElement.getElementsByTagName("path").item(0);
		else if(deviceGroupElement.getElementsByTagName("text").getLength() != 0)
			deviceGraph = (Element)deviceGroupElement.getElementsByTagName("text").item(0);
		else if(deviceGroupElement.getChildNodes().item(1) instanceof Element)
			deviceGraph = (Element)deviceGroupElement.getChildNodes().item(1);
		return deviceGraph;
	}

	@Override
	public Element getDeviceGraphElement(PowerDevice device) {
		Element deviceGroupElement = getDeviceGroupElement(device);
		if(deviceGroupElement == null)
			return null;
		return getDeviceGraphElement(deviceGroupElement);
	}
	
	@Override
	public NodeList getDeviceGroupElementList(Element element) {
		Element deviceGroupElement = element;
		NodeList deviceGroupList = deviceGroupElement.getElementsByTagName("g");
		return deviceGroupList;
	}
	
	@Override
	public NodeList getDeviceGroupElementList(PowerDevice device) {
		Element deviceGroupElement = getDeviceGroupElement(device);
		if(deviceGroupElement == null)
			return null;
		return getDeviceGroupElementList(deviceGroupElement);
	}
	
	@Override
	public String getDeviceCIMID(Element element) {
		String deviceID = "";
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:PSR_Ref").getLength() != 0)
			{
				Element psr = (Element)metadata.getElementsByTagName("cge:PSR_Ref").item(0);
				if(psr.hasAttribute("ObjectID"))
					deviceID =psr.getAttribute("ObjectID");
			}
			
		}
		return deviceID;
	}
	
	@Override
	public String getDeviceName(Element element) {
		String deviceName = "";
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:PSR_Ref").getLength() != 0)
			{
				if(metadata.getElementsByTagName("cge:PSR_Ref").item(0).getAttributes().getNamedItem("ObjectName")!=null){
					deviceName = metadata.getElementsByTagName("cge:PSR_Ref").item(0).getAttributes().getNamedItem("ObjectName").getNodeValue();
			
				}
			}
		}
		return deviceName;
	}
    /**
     * 获取当前节点的某一个属性
     * @param element 节点对象
     * @param attributeName 属性名
     * */
	public String getElementAttribute(Element element,
			String attributeName) {
		String attributeValue = "";
		Element metadata;
		if (element.getElementsByTagName("metadata").getLength() > 0) {
			metadata = (Element) element.getElementsByTagName("metadata").item(
					0);
			if (metadata.getElementsByTagName("cge:TPSR_Ref").getLength() > 0) {
				Element psr = (Element) metadata.getElementsByTagName(
						"cge:TPSR_Ref").item(0);
				if (psr.hasAttribute(attributeName))
					attributeValue = psr.getAttribute(attributeName);
			}
			if(attributeValue.equals("")) {
				if (metadata.getElementsByTagName("cge:PSR_Ref").getLength() > 0) {
					Element psr = (Element) metadata.getElementsByTagName(
							"cge:PSR_Ref").item(0);
					if (psr.hasAttribute(attributeName))
						attributeValue = psr.getAttribute(attributeName);
				}
			}

		}
		return attributeValue;
	}
	/**
     * 设置当前节点的某一个属性
     * */
	public void setElementAttribute(Element element, String namespace, String attributeName, String attributeValue) {
		Element metadata;
		if (element.getElementsByTagName("metadata").getLength() > 0) {
			metadata = (Element) element.getElementsByTagName("metadata").item(0);
			if (metadata.getElementsByTagName(namespace).getLength() > 0) {
				Element psr = (Element) metadata.getElementsByTagName(namespace).item(0);
				psr.setAttribute(attributeName, attributeValue);
			}

		}
	}
	/**
	 * 根据图层名获取图层节点
	 * @param doc svg文档对象
	 * @param layerID  图层名
	 * */
	@Override
	public Element getLayerElement(Document doc, String layerID) {
		Element layer = doc.getElementById(layerID);
		if (layer == null) {
			Element group = doc.createElementNS(svgNS, "g");
			group.setAttribute("id", layerID);
			doc.getDocumentElement().appendChild(group);
			return group;
		} else
			return layer;
	}
	
	@Override
	public String getDeviceID(Element element) {
		String deviceID = "";
		Element metadata;
		
		if(element!=null && (metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:TPSR_Ref").getLength() != 0)
			{
				Element psr = (Element)metadata.getElementsByTagName("cge:TPSR_Ref").item(0);
				if(psr.hasAttribute("TObjectID"))
					deviceID =psr.getAttribute("TObjectID");
			}
			
		}
		return deviceID;
	}
	
	public boolean isEquip(Element element) {
		boolean isEquip = false;
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:TPSR_Ref").getLength() != 0)
			{
				Element psr = (Element)metadata.getElementsByTagName("cge:TPSR_Ref").item(0);
				if(psr.hasAttribute("TObjectID"))
					isEquip = true;
			}
			
		}
		return isEquip;
	}
	
	@Override
	public String getMeasRelatedID(Element element) {
		String deviceID = "";
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:TPSR_Ref").getLength() != 0)
			{
				Element psr = (Element)metadata.getElementsByTagName("cge:TPSR_Ref").item(0);
				if(psr.hasAttribute("RelatedID"))
					deviceID =psr.getAttribute("RelatedID");
			}
			
		}
		return deviceID;
	}
	
	@Override
	public String getMeasRelatedField(Element element) {
		String deviceID = "";
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:TPSR_Ref").getLength() != 0)
			{
				Element psr = (Element)metadata.getElementsByTagName("cge:TPSR_Ref").item(0);
				if(psr.hasAttribute("RelatedField"))
					deviceID =psr.getAttribute("RelatedField");
			}
			
		}
		return deviceID;
	}
	
	@Override
	public String getTelemeteringID(Element element) {
		String telemeteringID = "";
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:Meas_Ref").getLength() != 0)
			{
				Element psr = (Element)metadata.getElementsByTagName("cge:Meas_Ref").item(0);
				if(psr.hasAttribute("ObjectID"))
					telemeteringID =psr.getAttribute("ObjectID");
				else if(psr.hasAttribute("ObjectId"))
					telemeteringID =psr.getAttribute("ObjectId");
			}
			
		}
		return telemeteringID;
	}
	
	@Override
	public String getTelemeteringName(Element element) {
		String telemeteringName = "";
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:Meas_Ref").getLength() != 0)
			{
				Element psr = (Element)metadata.getElementsByTagName("cge:Meas_Ref").item(0);
				if(psr.hasAttribute("ObjectName"))
					telemeteringName =psr.getAttribute("ObjectName");
			}
			
		}
		return telemeteringName;
	}
	
	public String setTelemeteringName(Element element, String telemeteringName) {
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:Meas_Ref").getLength() != 0)
			{
				Element psr = (Element)metadata.getElementsByTagName("cge:Meas_Ref").item(0);
				psr.setAttribute("ObjectName", telemeteringName);
			}
			
		}
		return telemeteringName;
	}
	
	@Override
	public void setTelemeteringValue(Element element, String telemeteringValue) {
		Element graphElement = getDeviceGraphElement(element);
		if (graphElement != null&&graphElement.getFirstChild()!=null){//黄翔修改
			graphElement.getFirstChild().setNodeValue(telemeteringValue);}
		
	}
	
	@Override
	public String getTelemeteringValue(Element element) {
		String nodeValue = "";
		Element graphElement = getDeviceGraphElement(element);
		if (graphElement != null&&graphElement.getFirstChild()!=null){
			nodeValue = graphElement.getFirstChild().getNodeValue();}
		return nodeValue;
	}

	@Override
	public String getHref(Element element) {
		String href = "";
		if(!element.getAttribute("href").equals(""))
			href = element.getAttribute("href");
		return href;
	}
	
	@Override
	public String[] getLayers(PowerDevice device) {
		List result = new ArrayList();
    	Element element = resolveSvgElement(device);
    	return getLayers(element);
	}

	@Override
	public String[] getLayers(Element element) {
		List result = new ArrayList();
		NodeList nodeList = element.getChildNodes();
    	for (int index = 0; index < nodeList.getLength(); index++) {
    		if (nodeList.item(index) instanceof Element) {
    			Element node =  (Element)nodeList.item(index);
    			if(node.hasAttribute("id"))
    				result.add(node.getAttribute("id"));
    		}
		}
    	return (String[])result.toArray(new String[0]);
	}
	
	@Override
	protected Element getChildByTagName(Element e, String name) {
		Element element = null;
		NodeList nodeList = e.getChildNodes();
		for (int i = 0; i < nodeList.getLength(); i++)
		{
			if (nodeList.item(i) instanceof Element && nodeList.item(i).getNodeName().equals(name)) 
			{
				element =  (Element)nodeList.item(i);
				break;
			}
		}
		return element;
	}

	@Override
	public String getLink0(Element element) {
		String deviceName = "";
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:PSR_Link").getLength() != 0)
			{
				Element link = (Element)metadata.getElementsByTagName("cge:PSR_Link").item(0);
				if(link.hasAttribute("Pin0InfoVect0LinkObjId"))
					deviceName = link.getAttribute("Pin0InfoVect0LinkObjId");
			}
			
		}
		return deviceName;
	}

	@Override
	public String getLink1(Element element) {
		String deviceName = "";
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:PSR_Link").getLength() != 0)
			{
				Element link = (Element)metadata.getElementsByTagName("cge:PSR_Link").item(0);
				if(link.hasAttribute("Pin1InfoVect0LinkObjId"))
					deviceName = link.getAttribute("Pin1InfoVect0LinkObjId");
			}
			
		}
		return deviceName;
	}

	@Override
	public void addElementStyle(Element element,String styleName, String styleValue) {
		// TODO Auto-generated method stub
		if(element.hasAttribute("style"))
		{
			String style = element.getAttribute("style");
			if(!style.endsWith(";"))
				style += ";";
			element.setAttribute("style", style+styleName+":"+styleValue+";");
		}
		else
			element.setAttribute("style", styleName+":"+styleValue+";");
	}

	public String getMeasurementValue_Layer() {
		return MeasurementValue_Layer;
	}

	public String getSubstation_Layer() {
		return Substation_Layer;
	}

	public String getHead_Layer() {
		return Head_Layer;
	}
	
	public String getOther_Layer() {
		return Other_Layer;
	}
	
	public Element getSymbolByID(Document document, String symbolID) {
		// TODO Auto-generated method stub
		NodeList list = document.getElementsByTagName("symbol");
		for (int i = 0; i < list.getLength(); i++) {
			Element symbol = (Element) list.item(i);
			if(symbol.getAttribute("id").equals(symbolID))
				return symbol;
		}
		return null;
	}
	
	/**
	 * 在开关上新增单带和重合闸运行标签
	 * @param pd
	 * @param string
	 */
	public void addChildCircle(final PowerDevice pd, final String string) {
		Element fGroupElement = SVGDocumentResolver.getResolver().getDeviceGroupElement(pd);
    	if(fGroupElement==null)
    		return;
    	final SVGDocument fSvgDocument =  (SVGDocument)fGroupElement.getOwnerDocument();
    	final UpdateManager updateManager = SystemConstants.getGuiBuilder().getUpdateManagerByDoc(fSvgDocument);

		
		Runnable r = new Runnable() {
	        public void run() {
	        	try {
	        		Element element = getDeviceGroupElement(pd);
		        	if(element != null) {
			    		NodeList nodeList = element.getChildNodes();
			    		String x="",y="",w="",h="",height="";
			    		for(int i = 0; i < nodeList.getLength(); i++){
			    			Node rectNode = nodeList.item(i);
			    			String s = rectNode.getLocalName();
			    			if("rect".equals(s)){
			    				NamedNodeMap attrMap = rectNode.getAttributes();
			    				x = attrMap.getNamedItem("x").getNodeValue();
			    				y = attrMap.getNamedItem("y").getNodeValue();
			    				height = attrMap.getNamedItem("height").getNodeValue();
			    				if(pd.getDeviceType().equals(SystemConstants.VolsbTransformer)){
			    		    		String transform =attrMap.getNamedItem("transform").getNodeValue();// rotate(0,48,223) scale(1.2,1.2) translate(-21,-41.1667)
			    		    		String[] rotate =transform.substring(transform.indexOf("rotate")+7,transform.indexOf(")")).split(",");
			    		    		String[] translate = transform.substring(transform.indexOf("translate")+10,transform.length()-1).split(",");
			    		    		if(Double.parseDouble(rotate[2])+Double.parseDouble(translate[1])>0){
			    		    			x=(Double.parseDouble(x)+10)+"";
			    		    			y=(Double.parseDouble(y)+30)+"";
			    		    		}else{
			    		    			x=(Double.parseDouble(x)-7)+"";
			    		    			y=(Double.parseDouble(y)-20)+"";
			    		    		}
			    				}
			    				break;
			    			}
			    		}
//			    		SVGDocument svgDocument = curpanel.getSvgDocument();
			    		Element shape = fSvgDocument.createElementNS("http://www.w3.org/2000/svg", "rect");
			    		Element text = fSvgDocument.createElementNS("http://www.w3.org/2000/svg", "text");
			    		if(x.equals("")){
			    			x="0";
			    		}
			    		if(y.equals("")){
			    			y="0";
			    		}
//						x = (Double.parseDouble(x)+10)+"";
//			    		y = (Double.parseDouble(y)+15)+"";
			    		
			    		w = String.valueOf(string.length()*12);
			    		h = "12";
			    		shape.setAttribute("x", "" + String.valueOf(Double.parseDouble(x)-(string.length()/2.0)*12) + "");
			            shape.setAttribute("y", "" + String.valueOf(Double.parseDouble(y)-10) + "");
			            shape.setAttribute("width", "" + w + "");
			            shape.setAttribute("height", "" + h + "");
			            shape.setAttribute("rx", "2");
			            shape.setAttribute("ry", "2");
			            //shape.setAttribute("r", "10");
			            shape.setAttribute("fill", "red");
			            shape.setAttribute("stroke", "red");
			            shape.setAttribute("stroke-width", "0.75");
			            shape.setAttribute("style", "wmode:transparent");
			            shape.setTextContent(string);
			            
			            text.setAttribute("x", x);
			            text.setAttribute("y", y);
			            text.setAttribute("style", "stroke:rgb(255,255,255);fill:rgb(255,255,255); font-size:12; text-anchor:middle; stroke-width:0.55; transform:matrix(1.000000 0.000000 0.000000 1.000000 1497.000000 -450.000000) translate(0,10)");
			            text.setAttribute("font-family", "SimSun");
			            text.setTextContent(string);
			            element.appendChild(shape);
			            element.appendChild(text);
			            //curpanel.getSvgCanvas().updateUI();
		        	}
	        	}catch(Exception e){
	        		
	        	}
	        	
	        }
		};
		if(updateManager!= null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		
		
	}
	/**
	 * 在线路上新增单带和重合闸运行标签
	 * @param pd
	 * @param string
	 */
	public void addChildXL(final PowerDevice pd, final String string) {
//		SVGCanvasPanel pdPanel =null;
		
		//遍历所有打开的文档查找
//		JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
//		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
//			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
//			if(panel.getSvgDocument() == null )
//				continue;
//			Element rootElement = panel.getSvgDocument().getDocumentElement();
//			String cgeNS =  rootElement.getAttributeNS(svgNS, "cge");
//			NodeList nodeListxl = rootElement.getElementsByTagNameNS(cgeNS, "TPSR_Ref");
//			if(nodeListxl == null || nodeListxl.getLength()==0) 
//				continue;
//	    	for (int j = 0; j < nodeListxl.getLength(); j++) {
//	    		Element psrElement = (Element)nodeListxl.item(j);
//	    		if(psrElement.getAttribute("TObjectID").equals(pd.getPowerDeviceID())) {
//	    			pdPanel = panel;
//	    			break;
//	    		}
//			}
//	    	if(pdPanel!=null){
//	    		break;
//	    	}
//		}
//		final SVGCanvasPanel curpanel =pdPanel;
		Element fGroupElement = SVGDocumentResolver.getResolver().getDeviceGroupElement(pd);
    	if(fGroupElement==null)
    		return;
    	final SVGDocument fSvgDocument =  (SVGDocument)fGroupElement.getOwnerDocument();
    	final UpdateManager updateManager = SystemConstants.getGuiBuilder().getUpdateManagerByDoc(fSvgDocument);

		Runnable r = new Runnable() {
	        public void run() {
	        	try {
	        		Element acLineElement = resolveSvgElementByName("ACLineSegment_Layer");
	        		Element element = getDeviceGroupElement(pd);
		        	if(element != null) {
			    		NodeList nodeList = element.getChildNodes();
			    		String x="",y="",w="",h="",height="";
			    		for(int i = 0; i < nodeList.getLength(); i++){
			    			Node rectNode = nodeList.item(i);
			    			String s = rectNode.getLocalName();
			    			if("path".equals(s)){
			    				NamedNodeMap attrMap = rectNode.getAttributes();
			    				String[] d = attrMap.getNamedItem("d").getNodeValue().split(" ");
			    				if(d.length<6){
			    					return;
			    				}
			    				if(pd.getDeviceType().equals(SystemConstants.MotherLine)){
			    					x = (Integer.parseInt(d[1])+(Integer.parseInt(d[4])-Integer.parseInt(d[1]))/2)+"";
				    				y=(Integer.parseInt(d[2])-10)+"";
			    				}else{
			    					x = d[4];
				    				y=d[5];
			    				}
			    				
			    				height = "10";
			    				break;
			    			}
			    		}
			    		
//			    		SVGDocument svgDocument = curpanel.getSvgDocument();
			    		
			    		Element shape = fSvgDocument.createElementNS("http://www.w3.org/2000/svg", "rect");
			    		Element text = fSvgDocument.createElementNS("http://www.w3.org/2000/svg", "text");
			    		if(x.equals("")){
			    			x="0";
			    		}
			    		if(y.equals("")){
			    			y="0";
			    		}
			    		x = (Double.parseDouble(x)+10)+"";
			    		y = (Double.parseDouble(y)+15)+"";
			    		w = String.valueOf(string.length()*12);
			    		h = "12";
			    		shape.setAttribute("x", "" + String.valueOf(Double.parseDouble(x)-(string.length()/2.0)*12) + "");
			            shape.setAttribute("y", "" + String.valueOf(Double.parseDouble(y)-10) + "");
			            shape.setAttribute("width", "" + w + "");
			            shape.setAttribute("height", "" + h + "");
			            shape.setAttribute("rx", "2");
			            shape.setAttribute("ry", "2");
			            //shape.setAttribute("r", "10");
			            shape.setAttribute("fill", "red");
			            shape.setAttribute("stroke", "red");
			            shape.setAttribute("stroke-width", "0.75");
			            shape.setAttribute("style", "wmode:transparent");
			            shape.setTextContent(string);
			            
			            text.setAttribute("x", x);
			            text.setAttribute("y", y);
			            text.setAttribute("style", "stroke:rgb(255,255,255);fill:rgb(255,255,255); font-size:12; text-anchor:middle; stroke-width:0.55; transform:matrix(1.000000 0.000000 0.000000 1.000000 1497.000000 -450.000000) translate(0,10)");
			            text.setAttribute("font-family", "SimSun");
			            text.setTextContent(string);
//			            if(acLineElement==null){
			            	   element.appendChild(shape);
					           element.appendChild(text);
//			            }else{
//			            	acLineElement.appendChild(shape);
//			            	acLineElement.appendChild(text);
//			            }
			         
//			            curpanel.getSvgCanvas().updateUI();
		        	}
	        	} catch (Exception e) {
	    			// TODO: handle exception
	    		}
	        }
		};	
	
		if(updateManager!= null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
//		if(curpanel.getSvgCanvas().getUpdateManager() != null)
//			curpanel.getSvgCanvas().getUpdateManager().getUpdateRunnableQueue().invokeLater(r);
//		else
//			r.run();
		
			
		
		
	
		
		
	}
	/**
	 * 查找当前SVG文档节点ID的根节点
	 */
	public Element resolveSvgElementByName(String eleName) {
		//先在当前文档查找
		if(SystemConstants.getGuiBuilder() == null)
			return null;
		SVGCanvasPanel curpanel = SystemConstants.getGuiBuilder().getActivateSVGPanel();
		if(curpanel != null && curpanel.getSvgDocument() != null) {
			Element rootElement = curpanel.getSvgDocument().getElementById(eleName);
			return rootElement;
		}
		return null;
	}
	/**
	 * 去除掉单带和重合闸运行的标签
	 * @param pd
	 * @param str
	 */
	public void removeChildNode(final PowerDevice pd, final String str) {
//		final SVGCanvasPanel curpanel = SystemConstants.getGuiBuilder().getActivateSVGPanel();
		Element fGroupElement = SVGDocumentResolver.getResolver().getDeviceGroupElement(pd);
    	if(fGroupElement==null)
    		return;
    	final SVGDocument fSvgDocument =  (SVGDocument)fGroupElement.getOwnerDocument();
    	final UpdateManager updateManager = SystemConstants.getGuiBuilder().getUpdateManagerByDoc(fSvgDocument);

		Runnable r = new Runnable() {
	        public void run() {
	        	Element element = getDeviceGroupElement(pd);
	        	if(element != null){
		    		NodeList nodeList1 = element.getChildNodes();
		    		for(int i = 0; i < nodeList1.getLength(); i++){
		    			Node circleNode = nodeList1.item(i);
		    			String s = circleNode.getLocalName();
		    			String sValue = circleNode.getTextContent();
		    			if((("rect".equals(s)) || "text".equals(s)) && str.equals(sValue)){
		    				element.removeChild(circleNode);
		    				i=0;
		    			}
		    		}
	        	}
	        }
		};
//		if(curpanel.getSvgCanvas().getUpdateManager() != null)
//			curpanel.getSvgCanvas().getUpdateManager().getUpdateRunnableQueue().invokeLater(r);
//		else
//			r.run();
		if(updateManager!= null)
			updateManager.getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
		
	}
	
	public String getDeviceHref(Element element) {
		String href = "";
		Element metadata;
		if((metadata = getChildByTagName(element, "g")) != null)
		{
			if(!metadata.getAttribute("href").equals(""))
			{
				href = metadata.getAttribute("href");
			}
		}
			
		return href;
	}
}
