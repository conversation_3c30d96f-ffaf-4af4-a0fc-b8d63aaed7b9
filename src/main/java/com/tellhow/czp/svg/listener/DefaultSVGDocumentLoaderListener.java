package com.tellhow.czp.svg.listener;

import java.awt.Dimension;
import java.awt.geom.Point2D;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import org.apache.batik.swing.svg.SVGDocumentLoaderAdapter;
import org.apache.batik.swing.svg.SVGDocumentLoaderEvent;
import org.w3c.dom.Attr;
import org.w3c.dom.CDATASection;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.Text;
import org.w3c.dom.events.EventTarget;
import org.w3c.dom.svg.SVGDocument;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.EMSService;
import com.tellhow.czp.svg.document.DefaultSVGDocumentResolver;
import com.tellhow.graphicframework.action.SvgAction;
import com.tellhow.graphicframework.action.impl.ChangeColorAction;
import com.tellhow.graphicframework.action.impl.ChangeDeviceOffOnAction;
import com.tellhow.graphicframework.algorithm.ElementSearch;
import com.tellhow.graphicframework.basic.LinkEquipVO;
import com.tellhow.graphicframework.basic.SVGLayer;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.DOMUtil;

import czprule.model.PowerDevice;
import czprule.rule.operationmodel.DrawRemovableDevice;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;

public class DefaultSVGDocumentLoaderListener extends SVGDocumentLoaderAdapter {
	private SVGCanvasPanel panel;
	
	private static SVGDocumentResolver resovler = DefaultSVGDocumentResolver
			.getResolver();
	public static final String svgNS = org.apache.batik.dom.svg.SVGDOMImplementation.SVG_NAMESPACE_URI;

	public DefaultSVGDocumentLoaderListener(SVGCanvasPanel svgPanel) {
		this.panel = svgPanel;
	}

	@Override
	public void documentLoadingStarted(SVGDocumentLoaderEvent e) {
	}

	@Override
	public void documentLoadingCompleted(SVGDocumentLoaderEvent e) {
		SVGDocument doc = e.getSVGDocument();
		
		Map<String, Map<String, LinkEquipVO>> equipLinkMap = ElementSearch.getEquipLinkMap(doc);
		SystemConstants.getMapEquipLink().put(doc, equipLinkMap);
		
		//setStyleVolt(doc);
		setBackground(doc);

		Element svgElement = SVGDocumentResolver.getResolver().resolveSvgElement(doc);
		
		if(!svgElement.hasAttribute("viewBox") && svgElement.hasAttribute("width") && svgElement.hasAttribute("height")) {
			svgElement.setAttribute("viewBox", "0 0 "+svgElement.getAttribute("width")+" "+svgElement.getAttribute("height"));
		}
		String mapType = SVGDocumentResolver.getResolver().getMapType(doc);
		if (mapType.equals(SystemConstants.MAP_TYPE_SYS)) {
			if(CBSystemConstants.roleCode.equals("1"))
				processAllMap(svgElement);
			else
				processAllMap(svgElement);
		} else if (mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
			processStationMap(svgElement);
		} else if (mapType.equals(SystemConstants.MAP_TYPE_LINE)) {
			processStationMap(svgElement);
		}
		initDeviceAction(svgElement);
		
		CZPService.getService().editMap(doc, panel.getName()); //处理图形
		
		//scaleMap(doc);
		
		addRemovable(panel.getStationID());
	}
	
	
	//加载电压等级样式
	public  void setStyleVolt(SVGDocument doc) {
		if("true".equals(CBSystemConstants.isUseSysColor))
		{
			Element styleElement = (Element) doc.getElementsByTagName("style").item(0);
			while (styleElement.hasChildNodes()) {
				Node node = styleElement.getFirstChild();
				if(node instanceof CDATASection) {
					if(((CDATASection)node).getNodeValue().indexOf("BV-")>=0)
						break;
				}
				styleElement.removeChild(node);
			}
			String cData = "\r\n";
			for (Map.Entry<String, String> entry : SystemConstants.getMapColor()
					.entrySet()) {
				String volCode = entry.getKey();
				String volValue = entry.getValue();
				String volStyle = ".kV" + volCode + "{stroke:" + volValue
						+ ";fill:none}\r\n";
				cData += volStyle;
			}
			CDATASection cdataSection = doc.createCDATASection(cData);
			styleElement.appendChild(cdataSection);
		}
	}
	
	//设置背景
	public  void setBackground(SVGDocument doc) {
		Element headElement = doc.getElementById("Head_Layer");
		if (headElement != null) {
			NodeList rectList = headElement.getElementsByTagName("rect");
			for (int i = 0; i < rectList.getLength(); i++) {
				Element rectElement = (Element) rectList.item(i);
				if (rectElement.hasAttribute("fill")
						&& !rectElement.getAttribute("rect").toLowerCase()
								.equals("rgb(0,0,0)"))
					rectElement.setAttribute("fill", "rgb(0,0,0)");
			}
		}
	}
	
	//缩放图形
	public  void scaleMap(SVGDocument doc) {
		double scale = Double.valueOf(SystemConstants.MAP_SCALE);
		if (scale == 0) {
			// 屏幕大小
			Dimension screen = panel.getSvgCanvas().getParent().getSize();
			// svg大小
			double scale1 = screen.getWidth()
					/ Double.valueOf(doc.getRootElement().getAttribute("width"));
			double scale2 = screen.getHeight()
					/ Double.valueOf(doc.getRootElement()
							.getAttribute("height"));
			scale = scale1 < scale2 ? scale1 : scale2;
		}
		
		DOMUtil.setElementTransform(doc.getRootElement(), 0, 0, scale, scale);
	}	
	
	
	//加载拆卸设备缓存
	public  static void  addRemovable(String id) {
		ArrayList<PowerDevice> list = CBSystemConstants.getRMDevice().get(id);
		if(list==null){
			return;
		}
		for (PowerDevice rm : list) {
//			new DrawRemovableDevice().execute(id, rm.getDevice(), rm.getKnife(), rm.getDeviceStatus(),rm.getRmType());
			new DrawRemovableDevice().execute(rm);
		}
	}	
		
	
	private void processAllMap(Element svgElement) {
		String flow = "flow";
		addSymbol(svgElement.getOwnerDocument(), flow);
		runRealTimeDataTask();
		
		String stationID = "";
		
		if (svgElement.getOwnerDocument().getElementById("PowerLine_Layer") != null) {
			NodeList linkLayerNodeList = svgElement.getOwnerDocument()
					.getElementById("PowerLine_Layer").getChildNodes();
			InitDeviceStatus initDeviceStatus = new InitDeviceStatus();
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Node no = linkLayerNodeList.item(i);
				if(!no.getNodeName().equals("g"))
					continue;
				Element node = (Element) linkLayerNodeList.item(i);
				String lineID = SVGDocumentResolver.getResolver().getDeviceID(
						node);
				PowerDevice pd = null;

				if (!lineID.equals("")) {
					PowerDevice line = CBSystemConstants.getPowerDevice(lineID);
					if(CBSystemConstants.getStationPowerDevices(line.getPowerStationID())==null) {
						CreatePowerStationToplogy.loadFacData(line.getPowerStationID());
					}
					pd = CBSystemConstants.getPowerDevice(line.getPowerStationID(), lineID);
					stationID = stationID + line.getPowerStationID() + "," ;
				}
				if (pd != null) {
					DeviceSVGPanelUtil.setDeviceSVGColor(pd);
				} 
			}
			if(stationID.endsWith(","))
				stationID = stationID.substring(0, stationID.length()-1);
			if(!stationID.equals(""))
				this.panel.setStationID(stationID);
			
			if(CBSystemConstants.roleCode.equals("1")) {
	    		
				String[] staarr = stationID.split(",");
				for(String sta : staarr) {
		    		CZPService.getService().initDeviceRunType(sta); //特殊初始化接线方式、安装类型
    		    	
				}
	    	}
		}
		
		String allEquipID = "";
		if (svgElement.getOwnerDocument().getElementById("Bus_Layer") != null) {
			NodeList linkLayerNodeList = svgElement.getOwnerDocument()
					.getElementById("Bus_Layer").getChildNodes();
			InitDeviceStatus initDeviceStatus = new InitDeviceStatus();
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Node no = linkLayerNodeList.item(i);
				if(!no.getNodeName().equals("g"))
					continue;
				Element node = (Element) linkLayerNodeList.item(i);
				String equipID = SVGDocumentResolver.getResolver().getDeviceID(
						node);
				if (!equipID.equals(""))
					allEquipID = allEquipID + "'"+equipID+"',";
			}
		}
			
		if (svgElement.getOwnerDocument().getElementById("Breaker_Layer") != null) {
			NodeList linkLayerNodeList = svgElement.getOwnerDocument()
					.getElementById("Breaker_Layer").getChildNodes();
			InitDeviceStatus initDeviceStatus = new InitDeviceStatus();
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Node no = linkLayerNodeList.item(i);
				if(!no.getNodeName().equals("g"))
					continue;
				Element node = (Element) linkLayerNodeList.item(i);
				String equipID = SVGDocumentResolver.getResolver().getDeviceID(
						node);
				if (!equipID.equals(""))
					allEquipID = allEquipID + "'"+equipID+"',";
			}
		}
		
		if(!allEquipID.equals("")) {
			allEquipID = allEquipID.substring(0, allEquipID.length()-1);
			List stationIDList = DBManager.queryForList("select distinct line_id stationid from "+CBSystemConstants.equipUser+"T_PD_EQUIPINFO where equip_id in ("+allEquipID+") union all select distinct station_id from "+CBSystemConstants.equipUser+"T_EQUIPINFO where equip_id in ("+allEquipID+")");
			for(int i = 0;i<stationIDList.size();i++){
				String sid = ((Map)stationIDList.get(i)).get("stationid").toString();
				
				if(!stationID.contains(sid))
					stationID = stationID + sid + "," ;
			}
			if(stationID.endsWith(","))
				stationID = stationID.substring(0, stationID.length()-1);
			
			//long start2=System.currentTimeMillis();
			CreatePowerStationToplogy.loadFacData(stationID);
//			long end2=System.currentTimeMillis();
//	    	System.out.println("********************所消耗的时间是:"+(end2-start2)+"ms");
	    	
			if(!stationID.equals(""))
				this.panel.setStationID(stationID);
			
//			if(CBSystemConstants.roleCode.equals("1")) {
//	    		
//				String[] staarr = stationID.split(",");
//				for(String sta : staarr) {
//		    		CZPService.getService().initDeviceRunType(sta); //特殊初始化接线方式、安装类型
//    		    	
//				}
//	    	}
		}
		
		
		
		
		if (svgElement.getOwnerDocument().getElementById("Breaker_Layer") != null) {
			NodeList linkLayerNodeList = svgElement.getOwnerDocument()
					.getElementById("Breaker_Layer").getElementsByTagName("g");
			InitDeviceStatus initDeviceStatus = new InitDeviceStatus();
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Element node = (Element) linkLayerNodeList.item(i);
				String deviceid = SVGDocumentResolver.getResolver().getDeviceID(
						node);
				
				PowerDevice pd = null;
				
				if (!deviceid.equals("")) {
					pd = CBSystemConstants.getPowerDevice(deviceid);
						
				}
				
				if (pd != null) {
					String powerVolt = String.valueOf((int) pd.getPowerVoltGrade());
					if (pd.getDeviceStatus().equals("0")) {
						SvgAction action = new ChangeDeviceOffOnAction(pd,
								SvgAction.SWITCH_ON);
						action.execute();
						action = new ChangeColorAction(pd, SystemConstants
								.getMapColor().get(powerVolt), SystemConstants
								.getMapColor().get(powerVolt));
						action.execute();
					} else {
						SvgAction action = new ChangeDeviceOffOnAction(pd,
								SvgAction.SWITCH_OFF);
						action.execute();
						action = new ChangeColorAction(pd, SystemConstants
								.getMapColor().get(SystemConstants.LOSE_COLOR_CODE),
								"none");
						action.execute();
					}
				} else {
					SvgAction action = new ChangeColorAction(node,
							SystemConstants.getMapColor().get(
									SystemConstants.NODATA_COLOR_CODE), "none");
					action.execute();
				}
			}
		}
		
		
		
		// 修改全网图线路显示样式
		if (svgElement.getOwnerDocument().getElementById("ACLine_Layer") != null) {
			NodeList linkLayerNodeList = svgElement.getOwnerDocument()
					.getElementById("ACLine_Layer").getElementsByTagName("g");
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Element node = (Element) linkLayerNodeList.item(i);
				String lineID = SVGDocumentResolver.getResolver().getDeviceID(
						node);
				PowerDevice pd = null;

				if (!lineID.equals(""))
					pd = CBSystemConstants.getMapPowerLine().get(lineID);
				if (pd != null) {
					DeviceSVGPanelUtil.setDeviceSVGColor(pd);
				} else {
					SvgAction action = new ChangeColorAction(node,
							SystemConstants.getMapColor().get(
									SystemConstants.NODATA_COLOR_CODE), "none");
					action.execute();
				}
			}
			//DOMUtil.writeXMLFile(svgElement.getOwnerDocument(), "D:\\aa.svg");
		}
		
		
		
		if (svgElement.getOwnerDocument().getElementById("Switch_Layer") != null) {
			NodeList linkLayerNodeList = svgElement.getOwnerDocument()
					.getElementById("Switch_Layer").getElementsByTagName("g");
			InitDeviceStatus initDeviceStatus = new InitDeviceStatus();
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Element node = (Element) linkLayerNodeList.item(i);
				String deviceid = SVGDocumentResolver.getResolver().getDeviceID(
						node);
				PowerDevice pd = null;
				if (!deviceid.equals("")) {
					PowerDevice line = CBSystemConstants.getPowerDevice(deviceid);
					if(CBSystemConstants.getStationPowerDevices(line.getPowerStationID())==null) {
						CreatePowerStationToplogy.loadFacData(line.getPowerStationID());
					}
					
				}

				if (!deviceid.equals("")) {
//					CreatePowerStationToplogy.loadFacData(CBSystemConstants.getPowerDevice(deviceid).getPowerStationID());
					pd = CBSystemConstants.getPowerDeviceCache(deviceid);
				}
				
				if (pd != null) {
					if(pd.getPowerDeviceName().contains("10240")){
						int asda =1;
					}
					String powerVolt = String.valueOf((int) pd.getPowerVoltGrade());
					if (pd.getDeviceStatus().equals("0")) {
						SvgAction action = new ChangeDeviceOffOnAction(pd,
								SvgAction.SWITCH_ON);
						action.execute();
						action = new ChangeColorAction(pd, SystemConstants
								.getMapColor().get(powerVolt), SystemConstants
								.getMapColor().get(powerVolt));
						action.execute();
					} else {
						SvgAction action = new ChangeDeviceOffOnAction(pd,
								SvgAction.SWITCH_OFF);
						action.execute();
						action = new ChangeColorAction(pd, SystemConstants
								.getMapColor().get(powerVolt), SystemConstants
								.getMapColor().get(powerVolt));
						action.execute();
						// 开关的断开svg显示效果
						/*List<PowerDevice> pdl=null;
						try{
							pdl = RuleExeUtil.getDeviceDirectList(pd,
								SystemConstants.SwitchSeparate);
							pdl.add(pd);
						}catch(Exception e){
							pdl=new ArrayList<PowerDevice>();
						}
						
						List<PowerDevice> epdl = new ArrayList<PowerDevice>();
						List<PowerDevice> nepdl = new ArrayList<PowerDevice>();
						for (int j = 0; j < pdl.size(); j++) {
							PowerDevice pde = pdl.get(j);
							if (!pde.getDeviceStatus().equals("0")) {
								epdl.add(pde);
							} else {
								nepdl.add(pde);
							}
						}

						for (int j = 0; j < epdl.size(); j++) {
							SvgAction action = new ChangeDeviceOffOnAction(epdl.get(j),
									SvgAction.SWITCH_OFF);
							action.execute();
							action = new ChangeColorAction(epdl.get(j),
									SystemConstants.getMapColor().get(
											SystemConstants.LOSE_COLOR_CODE), "none");
							action.execute();
						}
						for (int j = 0; j < nepdl.size(); j++) {
							SvgAction action = new ChangeDeviceOffOnAction(
									nepdl.get(j), SvgAction.SWITCH_ON);
							action.execute();
							action = new ChangeColorAction(nepdl.get(j),
									SystemConstants.getMapColor().get(powerVolt),
									SystemConstants.getMapColor().get(powerVolt));
							action.execute();
						}*/
					}
				} else {
					SvgAction action = new ChangeColorAction(node,
							SystemConstants.getMapColor().get(
									SystemConstants.NODATA_COLOR_CODE), "none");
					action.execute();
				}
			}
			//writeXMLFile(svgElement.getOwnerDocument(), "D:\\a.svg");
		}
		
		
	}
	
	private void processAllMapPW(Element svgElement) {
		
		
		String stationID = "";
		
		if (svgElement.getOwnerDocument().getElementById("PowerLine_Layer") != null) {
			NodeList linkLayerNodeList = svgElement.getOwnerDocument()
					.getElementById("PowerLine_Layer").getChildNodes();
			InitDeviceStatus initDeviceStatus = new InitDeviceStatus();
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Node no = linkLayerNodeList.item(i);
				if(!no.getNodeName().equals("g"))
					continue;
				Element node = (Element) linkLayerNodeList.item(i);
				String lineID = SVGDocumentResolver.getResolver().getDeviceID(
						node);
				PowerDevice pd = null;

				if (!lineID.equals("")) {
					PowerDevice line = CBSystemConstants.getPowerDevice(lineID);
					if(CBSystemConstants.getStationPowerDevices(line.getPowerStationID())==null) {
						CreatePowerStationToplogy.loadFacData(line.getPowerStationID());
					}
					pd = CBSystemConstants.getPowerDevice(line.getPowerStationID(), lineID);
					stationID = stationID + line.getPowerStationID() + "," ;
				}
				if (pd != null) {
					DeviceSVGPanelUtil.setDeviceSVGColor(pd);
				} 
			}
			if(stationID.endsWith(","))
				stationID = stationID.substring(0, stationID.length()-1);
			if(!stationID.equals(""))
				this.panel.setStationID(stationID);
			
			if(CBSystemConstants.roleCode.equals("1")) {
	    		
				String[] staarr = stationID.split(",");
				for(String sta : staarr) {
		    		CZPService.getService().initDeviceRunType(sta); //特殊初始化接线方式、安装类型
    		    	
				}
	    	}
		}
		
		
		
		
		if (svgElement.getOwnerDocument().getElementById("Breaker_Layer") != null) {
			NodeList linkLayerNodeList = svgElement.getOwnerDocument()
					.getElementById("Breaker_Layer").getElementsByTagName("g");
			InitDeviceStatus initDeviceStatus = new InitDeviceStatus();
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Element node = (Element) linkLayerNodeList.item(i);
				String deviceid = SVGDocumentResolver.getResolver().getDeviceID(
						node);
				
				PowerDevice pd = null;
				
				if (!deviceid.equals("")) {
					
					boolean isLoad = false;
					for(Iterator it =CBSystemConstants.getMapPowerStationDevice().values().iterator();it.hasNext();) {
						HashMap<String, PowerDevice> devMap = (HashMap<String, PowerDevice>)it.next();
						if(devMap.containsKey(deviceid)) {
							pd = devMap.get(deviceid);
							isLoad = true;
							break;
						}
					}
					if(!isLoad) {
						PowerDevice line = CBSystemConstants.getPowerDevice(deviceid);
						
						if(line!=null && CBSystemConstants.getStationPowerDevices(line.getPowerStationID())==null) {
							CreatePowerStationToplogy.loadFacData(line.getPowerStationID());
						}
						pd = line;
					}
				}
				
				if (pd != null) {
					String powerVolt = String.valueOf((int) pd.getPowerVoltGrade());
					if (pd.getDeviceStatus().equals("0")) {
						SvgAction action = new ChangeDeviceOffOnAction(node,
								SvgAction.SWITCH_ON);
						action.execute();
						action = new ChangeColorAction(node, SystemConstants
								.getMapColor().get(powerVolt), SystemConstants
								.getMapColor().get(powerVolt));
						action.execute();
					} else {
						SvgAction action = new ChangeDeviceOffOnAction(node,
								SvgAction.SWITCH_OFF);
						action.execute();
						action = new ChangeColorAction(node, SystemConstants
								.getMapColor().get(SystemConstants.LOSE_COLOR_CODE),
								"none");
						action.execute();
					}
				} else {
					SvgAction action = new ChangeColorAction(node,
							SystemConstants.getMapColor().get(
									SystemConstants.NODATA_COLOR_CODE), "none");
					action.execute();
				}
			}
			
		}
		
		if (svgElement.getOwnerDocument().getElementById("Switch_Layer") != null) {
			NodeList linkLayerNodeList = svgElement.getOwnerDocument()
					.getElementById("Switch_Layer").getElementsByTagName("g");
			InitDeviceStatus initDeviceStatus = new InitDeviceStatus();
			for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
				Element node = (Element) linkLayerNodeList.item(i);
				String deviceid = SVGDocumentResolver.getResolver().getDeviceID(
						node);
				PowerDevice pd = null;
				if (!deviceid.equals("")) {
					PowerDevice line = CBSystemConstants.getPowerDevice(deviceid);
					if(CBSystemConstants.getStationPowerDevices(line.getPowerStationID())==null) {
						CreatePowerStationToplogy.loadFacData(line.getPowerStationID());
					}
					
				}

				if (!deviceid.equals("")) {
					pd = CBSystemConstants.getPowerDeviceCache(deviceid);
				}
				
				if (pd != null) {
					if(pd.getPowerDeviceName().contains("10240")){
						int asda =1;
					}
					String powerVolt = String.valueOf((int) pd.getPowerVoltGrade());
					if (pd.getDeviceStatus().equals("0")) {
						SvgAction action = new ChangeDeviceOffOnAction(node,
								SvgAction.SWITCH_ON);
						action.execute();
						action = new ChangeColorAction(node, SystemConstants
								.getMapColor().get(powerVolt), SystemConstants
								.getMapColor().get(powerVolt));
						action.execute();
					} else {
						SvgAction action = new ChangeDeviceOffOnAction(node,
								SvgAction.SWITCH_OFF);
						action.execute();
						action = new ChangeColorAction(node, SystemConstants
								.getMapColor().get(powerVolt), SystemConstants
								.getMapColor().get(powerVolt));
						action.execute();
					}
				} else {
					SvgAction action = new ChangeColorAction(node,
							SystemConstants.getMapColor().get(
									SystemConstants.NODATA_COLOR_CODE), "none");
					action.execute();
				}
			}
			
		}
		
		
	}

	private void processStationMap(Element svgElement) {
		// 处理开关
		if(CBSystemConstants.isProcessSwitch){
			NodeList symbolList = svgElement.getOwnerDocument()
					.getElementsByTagName("symbol");
			for (int i = 0; i < symbolList.getLength(); i++) {
				Element symbol = (Element) symbolList.item(i);
				if (symbol.getAttribute("id").toLowerCase().indexOf("breaker:") >= 0
						|| symbol.getAttribute("id").toLowerCase()
								.indexOf("dollybreaker:") >= 0) {
					for (int j = 0; j < symbol.getChildNodes().getLength(); j++) {
						Node node = (Node) symbol.getChildNodes().item(j);
						if (node.getNodeName().equals("rect")) {
							if (node.getAttributes().getNamedItem("fill") != null)
								node.getAttributes().removeNamedItem("fill");
						}
					}
				}
			}
		}
		
		runRealTimeDataTask();
		
		String stationID = "";
		if(CBSystemConstants.roleCode.equals("0") || CBSystemConstants.roleCode.equals("1")) {
			String[] layerArr = {"Breaker_Layer", "DollyBreaker_Layer", "BreakerClass"};
			for(String layerName : layerArr) {
				if (svgElement.getOwnerDocument().getElementById(layerName) != null) {
					NodeList linkLayerNodeList = svgElement.getOwnerDocument()
							.getElementById(layerName).getChildNodes();
					InitDeviceStatus initDeviceStatus = new InitDeviceStatus();
					for (int i = 0; i < linkLayerNodeList.getLength(); i++) {
						Node no = linkLayerNodeList.item(i);
						if(!no.getNodeName().equals("g"))
							continue;
						Element node = (Element) linkLayerNodeList.item(i);
						String devID = SVGDocumentResolver.getResolver().getDeviceID(
								node);
						PowerDevice pd = null;
						
						if (!devID.equals("")) {
							
							PowerDevice dev = CBSystemConstants.getPowerDevice(devID);
							if(dev!=null) {
								if(CBSystemConstants.getStationPowerDevices(dev.getPowerStationID())==null) {
									CreatePowerStationToplogy.loadFacData(dev.getPowerStationID());
								}
								pd = CBSystemConstants.getPowerDevice(dev.getPowerStationID(), devID);
								
								if(pd!=null && !stationID.contains(pd.getPowerStationID()))
									stationID = stationID + pd.getPowerStationID() + "," ;
							}
						}
						if (pd != null) {
							DeviceSVGPanelUtil.setDeviceSVGColor(pd);
						} 
					}
				}
			}
			if(stationID.endsWith(","))
				stationID = stationID.substring(0, stationID.length()-1);
		}
		
		if(CBSystemConstants.roleCode.equals("1") && !stationID.equals(""))
			this.panel.setStationID(stationID);
		
		final String[] staarr = stationID.split(",");
		for(String sta : staarr) {
			if(!sta.equals(""))
				CZPService.getService().initDeviceRunType(sta); //特殊初始化接线方式、安装类型
		}
		
		// DeviceSVGPanelUtil.ChangeDeviceSVGSize(panel.getSvgCanvas(), -1,
		// 0.2);
		stationID = SVGDocumentResolver.getResolver().getStationID(
				svgElement.getOwnerDocument());
		String sid = SVGDocumentResolver.getResolver().resolveSvgElement(svgElement.getOwnerDocument()).getAttribute("StationID");
		if (CBSystemConstants.roleCode.equals("0") && !stationID.equals("")) {
			NodeList layerNodeList = svgElement.getChildNodes();
			for (int i = 0; i < layerNodeList.getLength(); i++) {
				if (!layerNodeList.item(i).getNodeName().equals("g"))
					continue;
				Element layer = (Element) layerNodeList.item(i);
				if (layer.getAttribute("id").equals("Link_Layer")) { // 设置连线颜色
					
				} 
				else if (layer.getAttribute("id").equals("ConnLine_Layer")) { // 设置连线颜色
					
				}
				else {
					if (CBSystemConstants.getStationPowerDevices(stationID) == null)
						continue;
					NodeList groupNodeList = layer.getChildNodes();
					for (int j = 0; j < groupNodeList.getLength(); j++) {
						if (!groupNodeList.item(j).getNodeName().equals("g"))
							continue;
						Element group = (Element) groupNodeList.item(j);
						if (SVGDocumentResolver.getResolver().isEquip(group)) {
							String equipID = SVGDocumentResolver.getResolver()
									.getDeviceID(group);
							PowerDevice pd = null;
							if (!equipID.equals(""))
								pd = CBSystemConstants.getPowerDevice(stationID, equipID);
							if(pd == null)
								pd = CBSystemConstants.getPowerDevice(sid, equipID);
							if(pd == null)
								pd = CBSystemConstants.getPowerDevice(equipID);
							
							if (pd != null) {
							DeviceSVGPanelUtil.changeDeviceSVGColor(pd); // 执行图形外观变更

								if(pd.getIsLoseElec().equals("1")){
									//配网失电
										SvgAction pwloseElecaction = null; 
										if(SystemConstants.Switch.equals(pd.getDeviceType())){
											pwloseElecaction = new ChangeColorAction(pd,
													SystemConstants.getMapColor().get(
															SystemConstants.LOSE_COLOR_CODE), SystemConstants.getMapColor().get(
																	SystemConstants.LOSE_COLOR_CODE));
										}else{
											pwloseElecaction = new ChangeColorAction(pd,
													SystemConstants.getMapColor().get(
															SystemConstants.LOSE_COLOR_CODE), null);
										}
										pwloseElecaction.execute();
								}
							} 
							else if (SystemConstants.getMapSVGLayer().containsKey(layer.getAttribute("id")) && !SystemConstants.getMapSVGLayer().get(layer.getAttribute("id")).isHandleEvent()) { //不响应事件的节点，例如连接线，不渲染为白色
								
							}
							else if (layer.getAttribute("id").indexOf("GroundDisconnector") < 0) {
							}
						}
					}
				}
			  }
			}
	 }
	

	/**
	 * 用 途: 初始化SVG画板事件处理接口
	 * 
	 * @返回值：无
	 */
	private void initDeviceAction(Element svgElement) {
		SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		for (int i = 0; i < svgElement.getChildNodes().getLength(); i++) {
			Node node1 = (Node) svgElement.getChildNodes().item(i);
			if (node1.getNodeName().equals("#text"))
				continue;
			else if (node1.getNodeName().equals("defs"))
				continue;
			else {
				Element layerElement = (Element) node1;
				String layerID = layerElement.getAttribute("id");
				if (layerElement.hasAttributeNS("http://schemas.microsoft.com/visio/2003/SVGExtensions/","mID")) {
//					for (int j = 0; j < layerElement.getChildNodes()
//							.getLength(); j++) {
//						Node node2 = layerElement.getChildNodes().item(j);
//						if (node2.getNodeName().equals("#text"))
//							continue;
//						if (node2.getNodeName().equals("g")) {
//							EventTarget elementEvent = (EventTarget) layerElement;
//							elementEvent
//									.addEventListener("click", panel, false);
//							elementEvent.addEventListener("mouseover", panel,
//									false);
//							elementEvent.addEventListener("mouseout", panel,
//									false);
//						}
//					}
//					continue;
					EventTarget elementEvent = (EventTarget) layerElement;
					elementEvent
							.addEventListener("click", panel, false);
					elementEvent.addEventListener("mouseover", panel,
							false);
					elementEvent.addEventListener("mouseout", panel,
							false);
				}
				if (layerID.equals(""))
					continue;
				else if (!SystemConstants.getMapSVGLayer().containsKey(layerID)) {
					SVGLayer layer = new SVGLayer(layerID);
					SystemConstants.putMapSVGLayer(layer);
				}
				SVGLayer layer = SystemConstants.getMapSVGLayer().get(layerID);
				if (!layer.isVisiable())
					DOMUtil.setStyleProperty(layerElement, "display", "none");
				if (layer.isHandleEvent()) {
					for (int j = 0; j < layerElement.getChildNodes()
							.getLength(); j++) {
						Node node2 = layerElement.getChildNodes().item(j);
						if (node2.getNodeName().equals("#text"))
							continue;
						Element element = (Element) node2;
						
						if (layerID.equals(resolver.getHead_Layer()) || layerID.equals("HeadClass")|| layerID.equals("GraphHead")|| layerID.equals("BackGround_Layer")) { // 背景响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent
									.addEventListener("click", panel, false);
						} else if (layerID.equals(resolver
								.getSubstation_Layer())) { // 厂站层的节点都响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent
									.addEventListener("click", panel, false);
							elementEvent.addEventListener("mouseover", panel,
									false);
							elementEvent.addEventListener("mouseout", panel,
									false);
						} else if (layerID.equals(resolver.getMeasurementValue_Layer())||layerID.equals("TermMeasure_Layer")||layerID.equals("ScadaRealValue_Layer")||layerID.equals("DynamicPoint_Layer")||layerID.equals("Other_Layer")) { // 潮流数据层的节点都响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent
									.addEventListener("click", panel, false);
							elementEvent.addEventListener("mouseover", panel,
									false);
							elementEvent.addEventListener("mouseout", panel,
									false);
						} else if (!element.getAttribute("href").equals("")) { // 有超链接的节点都响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent
									.addEventListener("click", panel, false);
							elementEvent.addEventListener("mouseover", panel,
									false);
							elementEvent.addEventListener("mouseout", panel,
									false);
						} else if (!resolver.getDeviceID(element).equals("")) { // 有ID的节点都响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent
									.addEventListener("click", panel, false);
							elementEvent.addEventListener("mouseover", panel,
									false);
							elementEvent.addEventListener("mouseout", panel,
									false);
						}
						else if (!resolver.getDeviceCIMID(element).equals("")) { // 有ID的节点都响应事件
							EventTarget elementEvent = (EventTarget) layerElement;
							elementEvent
									.addEventListener("click", panel, false);
							elementEvent.addEventListener("mouseover", panel,
									false);
							elementEvent.addEventListener("mouseout", panel,
									false);
						} 
						else
							element.setAttribute("pointer-events", "none");
					}
				} else
					layerElement.setAttribute("pointer-events", "none");
			}
		}
	}

	public void runRealTimeDataTask() {
		
		SVGCanvas fSvgCanvas = panel.getSvgCanvas();
		boolean result = false;
		if(Integer.valueOf(SystemConstants.refreshrate) > 0)
			result = showMeasurementValue(fSvgCanvas.getSVGDocument());
		
		if (result && Integer.valueOf(SystemConstants.refreshrate) > 0) {
			
			TimerTask task = new TimerTask() {
				public void run() {
					executeTask();
				}
			};
			Timer timer = new Timer();
			timer.schedule(task, Integer.valueOf(SystemConstants.refreshrate) * 1000,
					Integer.valueOf(SystemConstants.refreshrate) * 1000);
		}
	}

	public void executeTask() {
		final SVGCanvas fSvgCanvas = panel.getSvgCanvas();
		Runnable r = new Runnable() {
			public void run() {
				if (fSvgCanvas.getSVGDocument() != null) {
					showMeasurementValue(fSvgCanvas.getSVGDocument());
				}
			}
		};
		if (fSvgCanvas.getUpdateManager() != null)
			fSvgCanvas.getUpdateManager().getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
	}

	public boolean showMeasurementValue(SVGDocument svgDoc) {
		
		return EMSService.getService().showMeasAnalog(svgDoc);
		
   
	}
	
	public static Element copyElement(Document doc, Node tagElement,
			Node srcElement) {
		String xlinkPrefix = org.apache.batik.util.XMLConstants.XLINK_PREFIX;
		String xlinkNS = org.apache.batik.util.XMLConstants.XLINK_NAMESPACE_URI;
		if (srcElement.getNodeName().equals("#text")) {
			Text element = doc.createTextNode(srcElement.getNodeValue());
			tagElement.appendChild(element);
			return null;
		} else {
			String uri = "";
			if (srcElement.getNamespaceURI() != null)
				uri = srcElement.getNamespaceURI();
			else
				uri = doc.getDocumentElement().getNamespaceURI();
			Element element = doc
					.createElementNS(uri, srcElement.getNodeName());
			NamedNodeMap attrs = srcElement.getAttributes();
			if (attrs != null) {
				for (int i = 0; i < attrs.getLength(); i++) {
					Attr attr = (Attr) attrs.item(i);
					String attrNS = attr.getName().indexOf(xlinkPrefix) == -1 ? null
							: xlinkNS;
					element.setAttributeNS(attrNS, attr.getName(),
							attr.getValue());
				}
			}
			for (int i = 0; i < srcElement.getChildNodes().getLength(); i++) {
				Node node = srcElement.getChildNodes().item(i);
				if (node.getNodeName().equals("#text")
						&& node.getNodeValue().trim().equals(""))
					continue;
				copyElement(doc, element, node);
			}
			tagElement.appendChild(element);
			return element;
		}
	}
	/**
	 * 根据线路信息做潮流箭头
	 * */
	public void createFlow( Document doc,  Element element,  double flowvalue) {
		
		String svgNS = org.apache.batik.dom.svg.SVGDOMImplementation.SVG_NAMESPACE_URI;
		String xlinkNS = org.apache.batik.util.XMLConstants.XLINK_NAMESPACE_URI;
		String[] arr = element.getAttribute("d").replace("M", "")
				.replace("L", "").trim().split("( )+");
		double distance = Point2D.Double.distance(Double.valueOf(arr[0]),
				Double.valueOf(arr[1]), Double.valueOf(arr[2]),
				Double.valueOf(arr[3]));
		
		int dur = (int)(distance*Math.abs(flowvalue/100)/40);
//		if(flowvalue == 0)
//			flowvalue = 50;
//		else if(flowvalue > 0)
//			flowvalue += 30;
//		else if(flowvalue < 0)
//			flowvalue -= 30;
		String lineID = ((Element)element.getParentNode()).getAttribute("id")+"_flow";
		element.setAttribute("id", lineID);
		String flow = "flow";
		
		String linePathID = lineID;
		if (flowvalue < 0) {
			linePathID = (lineID + "_display");
			// 创建一条隐藏线用于反向潮流
			if (doc.getElementById(linePathID) == null) {
				Element displayLine = copyElement(doc,
						element.getParentNode(), element);
				displayLine.setAttribute("id", linePathID);
				displayLine.setAttribute("d", "M" + arr[2] + " " + arr[3] + " "
						+ "L" + arr[0] + " " + arr[1]);
				displayLine.setAttribute("display", "none");
			}
		}
		
		Element flowsGroup = doc.getElementById("Other_Layer");
		for(int i = 0; i < dur; i++) {
			Element mpath = doc.createElementNS(svgNS, "mpath");
		    mpath.setAttributeNS(xlinkNS , "xlink:href",  "#" + linePathID);
		    Element animateMotion = doc.createElementNS(svgNS, "animateMotion");
		    animateMotion.setAttribute("repeatCount", "indefinite");//indefinite
		    animateMotion.setAttribute("rotate", "auto");
		  
		    animateMotion.setAttribute("dur", dur+"s");
		    animateMotion.setAttribute("begin", (i-dur)+"s");
		    
		    animateMotion.appendChild(mpath);
		    Element use = doc.createElementNS(svgNS, "use");
		    use.setAttribute("id", lineID);
		    use.setAttribute("x", "-4");
		    use.setAttribute("y", "-4");
		    use.setAttribute("width", "8");
		    use.setAttribute("height", "8");
		    use.setAttribute("class", element.getAttribute("class"));
		    addSymbol(doc, flow);
		    //addSymbol(doc, SVGDataCache.getSvgSymbolMap().get(flow));
		    use.setAttributeNS(xlinkNS , "xlink:href",  "#" + flow);
		    use.appendChild(animateMotion);
		    flowsGroup.appendChild(use);
		
		    
		}
        
	}

	/**
	 * 根据flowid添加相应的symbol
	 * */
	public static void addSymbol(Document doc, String flowid) {
		Element defs =(Element) doc.getElementsByTagName("defs").item(0);
		if(resovler.getSymbolByID(doc, flowid)!=null){
			return;
		}
		Element symbol = doc.createElementNS(svgNS,"symbol");
		createSymbol(doc, symbol);
		defs.appendChild(symbol);
       
	}

	/**
	 * 创建并添加symbol到defs flow
	 * */
	private static void createSymbol(Document doc, Element symbol) {
		symbol.setAttribute("id", "flow");
		symbol.setAttribute("viewbox", "0 0 8 8");
		Element l1 = doc.createElementNS(svgNS,"line");
		Element l2 = doc.createElementNS(svgNS,"line");
		l1.setAttribute("x1", "0");
		l1.setAttribute("y1", "0");
		l1.setAttribute("x2", "4");
		l1.setAttribute("y2", "4");
		l1.setAttribute("stroke", "#FFFF00");
		l1.setAttribute("stroke-width", "2");
		//l1.setAttribute("fill", "none");

		l2.setAttribute("x1", "4");
		l2.setAttribute("y1", "4");
		l2.setAttribute("x2", "0");
		l2.setAttribute("y2", "8");
		l2.setAttribute("stroke", "#FFFF00");
		l2.setAttribute("stroke-width", "2");
		//l2.setAttribute("fill", "none");

		symbol.appendChild(l1);
		symbol.appendChild(l2);
		

	}


}
