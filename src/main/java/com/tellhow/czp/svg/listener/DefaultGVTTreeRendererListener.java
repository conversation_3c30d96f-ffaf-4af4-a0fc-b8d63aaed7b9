package com.tellhow.czp.svg.listener;

import java.awt.Color;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;

import javax.swing.JOptionPane;

import org.apache.batik.dom.svg.SVGOMGElement;
import org.apache.batik.gvt.GraphicsNode;
import org.apache.batik.swing.gvt.GVTTreeRendererAdapter;
import org.apache.batik.swing.gvt.GVTTreeRendererEvent;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.svg.SVGDocument;
import org.w3c.dom.svg.SVGLocatable;
import org.w3c.dom.svg.SVGRect;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.action.impl.ChangeDeviceRectFlashingAction;
import com.tellhow.graphicframework.action.impl.ChooseDeviceRectFlashingAction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.mainframe.WindowSplash;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2010-9-25 上午11:55:09 
 */
public class DefaultGVTTreeRendererListener extends GVTTreeRendererAdapter{

	private SVGCanvasPanel panel;
	public DefaultGVTTreeRendererListener(SVGCanvasPanel svgPanel) {
		this.panel = svgPanel;
	}

	@Override
	public void gvtRenderingPrepare(GVTTreeRendererEvent e) {
    }

    @Override
	public void gvtRenderingCompleted(GVTTreeRendererEvent e) {
    	
    	Runnable r = new Runnable() {
	        public void run() {
	        	setUseBounds(panel.getSvgCanvas().getSVGDocument());
	        }
		};
		if(panel.getSvgCanvas().getUpdateManager() != null)
			panel.getSvgCanvas().getUpdateManager().getUpdateRunnableQueue().invokeLater(r);
		else
			r.run();
    	
    	WindowSplash.stopSplashWindow();
        panel.getSvgCanvas().setBackground(Color.BLACK);
        CZPService.getService().editBackGround(panel);
        if(CBSystemConstants.rbmList.size() > 0) {
        	for(RuleBaseMode rbm :CBSystemConstants.rbmList){
        		PowerDevice pd = rbm.getPd();
    	        if(panel.getStationID()!=null && pd!=null && panel.getStationID().equals(pd.getPowerStationID())) {
    	        	//边框效果
    	        	ChooseDeviceRectFlashingAction action = new ChooseDeviceRectFlashingAction(pd);
    	        	action.backexecute();
    	    		action.execute();
    	        }
        	}
        }
    }
    
    public void setUseBounds(SVGDocument svgDoc) {
    	String SVG_NAMESPACE_URI = org.apache.batik.dom.svg.SVGDOMImplementation.SVG_NAMESPACE_URI;
    	org.w3c.dom.Element svgElement = svgDoc.getRootElement();
   		 for (int i = 0; i < svgElement.getChildNodes().getLength(); i++) {
   				Node node1 = (Node) svgElement.getChildNodes().item(i);
   				if (node1.getNodeName().equals("#text"))
   					continue;
   				else if (node1.getNodeName().equals("defs"))
   					continue;
   				else {
   					org.w3c.dom.Element layerElement = (org.w3c.dom.Element) node1;
   					for (int j = 0; j < layerElement.getChildNodes().getLength(); j++) {
   						Node node2 = layerElement.getChildNodes().item(j);
   						if (node2.getNodeName().equals("#text"))
   							continue;
   						if (node2.getNodeName().equals("#cdata-section"))
   							continue;
   						org.w3c.dom.Element gEle = (org.w3c.dom.Element) node2;
   						if(gEle.getNodeName().equals("g")) {
   							org.w3c.dom.Element useEle = null;
   							if(gEle.getElementsByTagName("use").getLength() == 1) {
   								useEle = (org.w3c.dom.Element)gEle.getElementsByTagName("use").item(0);
   							}
   							else if(gEle.getElementsByTagName("g").getLength() >= 1) {
   								org.w3c.dom.Element child = (org.w3c.dom.Element)gEle.getElementsByTagName("g").item(0);
   								if(child.getElementsByTagName("use").getLength() == 1) {
       								useEle = (org.w3c.dom.Element)gEle.getElementsByTagName("use").item(0);
       							}
   							}
   							
   							if(useEle!=null && (!useEle.hasAttribute("width") || !useEle.hasAttribute("height"))) {
   								
									SVGRect bbox = ((SVGLocatable)gEle).getBBox();
									if(bbox!= null) {
   									org.w3c.dom.Element actionElement = svgDoc.createElementNS(SVG_NAMESPACE_URI, "rect");
   									actionElement.setAttribute("x", String.valueOf(bbox.getX()));
   									actionElement.setAttribute("y", String.valueOf(bbox.getY()));
   									actionElement.setAttribute("width", String.valueOf(bbox.getWidth()));
   									actionElement.setAttribute("height", String.valueOf(bbox.getHeight()));
   									actionElement.setAttribute("fill", "yellow");
   									actionElement.setAttribute("stroke", "yellow");
   									//actionElement.setAttribute("opacity", "1");
   									actionElement.setAttribute("opacity", "0");//解决江西鹰潭地调35kv主变开关地调黄色背景异常的问题  2017/08/23 黄彩凤
   									actionElement.setAttribute("fill", "white");
   									actionElement.setAttribute("stroke", "white");
   									actionElement.setAttribute("opacity", "0");
   									gEle.appendChild(actionElement);
									}
								}
   						}
   					}
   				}
   		 }
    }
}
