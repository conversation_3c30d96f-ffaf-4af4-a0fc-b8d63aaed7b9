/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：
 * 功能说明 : 设备右键操作入口
 * 作    者 : 张余平
 * 开发日期 : 2010-09-6
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.svg.listener;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.mainframe.menu.AbstractJMenuItem;
import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.widget.StatuesListWidgetGX;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;

public class PopupmenuActionListenter implements ActionListener {

	public void actionPerformed(ActionEvent e) {
		// TODO 自动生成方法存根

		AbstractJMenuItem mi = (AbstractJMenuItem) e.getSource();
		DeviceMenuModel dmm = mi.getDeviceMenuModel(); // 右键菜单项内容
		
		//状态成票对成票标识进行置位
		if(!dmm.getStatecode().equals("86a9b7699d764d8a8b32cd02bd61114")){
			CBSystemConstants.stateOfTheDrawer = false;
		}
		PowerDevice pd = dmm.getPd(); // 右键操作设备
		
		//"投入、退出"自动生成保护令
		String parentName = QueryDeviceDao.getParentDeviceStateName(dmm.getParentcode());
//		if("投入".equals(parentName)||"退出".equals(parentName)){
//			
//			StatuesListWidgetGX.gxcardtype = "1";
//			StatuesListWidgetGX.gxcardkind = "二次令";
//			StatuesListWidgetGX.jComboBox.setSelectedIndex(1);
//		}
		
		if(!pd.getDeviceType().equals(SystemConstants.PowerStation)){
			List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(pd, "");
			if(devList.size() == 0) {
//				ShowMessage.view("D5000系统中没有["+CZPService.getService().getDevName(pd)+"]的连接信息，建议手工拟票");
			}
		}

		if (CBSystemConstants.currentInitConfig
				.equals(CBSystemConstants.INITDEVICESTATUS)) {
			return;
		}
		//普通开票
		if (CBSystemConstants.isSame == false) {
			DeviceOperate dre = new DeviceOperate();
			//设备对位时如果选择线路，则同时对其他侧对位
			if(CBSystemConstants.cardbuildtype.equals("2") && pd.getDeviceType().equals(SystemConstants.InOutLine)) {
				List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(pd);
				for(PowerDevice line : lineList) {
					if(!dre.execute(line, dmm)){
						break;
					}	
				}
			}
			else
				dre.execute(pd, dmm);
		} else if (CBSystemConstants.stateOfTheDrawer){//状态成票
			return;
			
		} else {
			//多选设备开票
			if (CBSystemConstants.getSamepdlist().size() == 0) {
				DeviceOperate dre = new DeviceOperate();
				dre.execute(pd, dmm);
			} 
			DeviceOperate dre = new DeviceOperate();
			dre.executesame(CBSystemConstants.getSamepdlist(),
					dmm);
		}
	}
	
	public static void openAnotherSideStation(PowerDevice pd){
		List<PowerDevice> anothers = RuleExeUtil.getLineOtherSideList(pd);
		for (PowerDevice another : anothers) {
			List<SVGFile> files=SystemConstants.getSVGFileByStationID(another.getPowerStationID());
		    for (SVGFile file : files) {
		    	String fileName =file.getFileName();
			    String deviceCode=another.getPowerStationID();
			    String deviceName;
			    if(fileName.equals(""))
			    	return;
			    if(!deviceCode.equals("") && CBSystemConstants.getPowerStation(deviceCode)!= null)
			    	deviceName =another.getPowerStationName();
				else
					return;
			    if (SystemConstants.getGuiBuilder().activateTabbedPageByName(fileName))
			    	return;
				String filePath =SystemConstants.FILE_SVGMAP_PATH+fileName;
				CreatePowerStationToplogy.createSVGPanel(deviceCode, deviceName, fileName, filePath);
			}
		}
	}
}
