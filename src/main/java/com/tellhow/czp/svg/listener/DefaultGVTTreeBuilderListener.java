package com.tellhow.czp.svg.listener;

import org.apache.batik.swing.svg.GVTTreeBuilderAdapter;
import org.apache.batik.swing.svg.GVTTreeBuilderEvent;
import org.apache.log4j.Logger;

import com.tellhow.graphicframework.svg.SVGCanvasPanel;

public class DefaultGVTTreeBuilderListener extends
	GVTTreeBuilderAdapter {
	private static Logger log = Logger.getLogger(DefaultGVTTreeBuilderListener.class);

	protected SVGCanvasPanel panel;

	public DefaultGVTTreeBuilderListener(SVGCanvasPanel svgPanel) {
		this.panel = svgPanel;
	}

	@Override
	public void gvtBuildStarted(GVTTreeBuilderEvent e) {
		//Console.outPut("开始构建图形...\r\n");	
	}

	@Override
	public void gvtBuildCompleted(GVTTreeBuilderEvent e) {
		//Console.outPut("完成.\r\n");
	}
}
