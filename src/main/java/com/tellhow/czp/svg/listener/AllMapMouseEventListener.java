package com.tellhow.czp.svg.listener;


import java.awt.Cursor;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.swing.JPopupMenu;
import javax.swing.SwingUtilities;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.events.Event;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.mainframe.menu.DeviceInfoMenuProvider;
import com.tellhow.czp.mainframe.menu.DeviceMenuBuild;
import com.tellhow.czp.mainframe.menu.MeasurementInfoMenuProvider;
import com.tellhow.czp.mainframe.menu.provider.ScaleMenuProvider;
import com.tellhow.czp.mainframe.menu.provider.ScaleMenuProviderDefault;
import com.tellhow.graphicframework.action.impl.ChooseDeviceRectFlashingAction;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.svg.listener.SvgMouseEventAdapter;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.SVGAddDeviceInf;

/**
 * 全网图鼠标事件处理器
 * <AUTHOR>
 *
 */
public class AllMapMouseEventListener extends SvgMouseEventAdapter {
	
	protected static AllMapMouseEventListener allMapMouseEventListener;
	
	JPopupMenu popupMenu = null;
	public static AllMapMouseEventListener getInstance() {

		if (allMapMouseEventListener == null) {
			allMapMouseEventListener=(AllMapMouseEventListener)CZPImpl.getInstance("AllMapMouseEventListener");
			if(allMapMouseEventListener == null)
				allMapMouseEventListener = new AllMapMouseEventListener();

			return allMapMouseEventListener;
		}
		else
			return allMapMouseEventListener;
	}
	
	public void mouseClick(Event evt) {
		final org.apache.batik.dom.events.DOMMouseEvent me = (org.apache.batik.dom.events.DOMMouseEvent) evt;
		Element evtElement = ((Element) me.getTarget());
        final Node deviceNode = evtElement.getParentNode();
        final SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
        //测量值处理
        Element layerElement=(Element)deviceNode.getParentNode();
        if(layerElement.getAttribute("id").equals(resolver.getMeasurementValue_Layer())){
        	final String relatedID = resolver.getMeasRelatedID((Element) deviceNode);
        	final String relatedField = resolver.getMeasRelatedField((Element) deviceNode);
        	Runnable r = new Runnable() {
    	        public void run() {
    	        	MeasurementInfoMenuProvider provider = null;
    	        	if(!relatedID.equals(""))
    	        		provider = new MeasurementInfoMenuProvider(relatedID,relatedField);
    	        	else{
    	        		String measID = resolver.getTelemeteringID((Element) deviceNode);
    	        		if(!measID.equals(""))
    	        			provider = new MeasurementInfoMenuProvider(measID,"0");
    	        	}
    	        	JPopupMenu popupMenu = provider.createMenu();
    	        	popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
    	        }
    		};
    		SwingUtilities.invokeLater(r);
    		return;
        }
        
        PowerDevice dev = null;
        String deviceCode = resolver.getDeviceID((Element)deviceNode);
        if(!deviceCode.equals("")) {
	        dev = CBSystemConstants.getPowerDevice(deviceCode);
	        if(dev != null)
	        	CreatePowerStationToplogy.loadFacData(dev.getPowerStationID());
        }
		
        String fileName = resolver.getHref((Element)deviceNode);
        if(dev != null) {
        	final PowerDevice pd = CBSystemConstants.getPowerDevice(dev.getPowerStationID(), deviceCode);
        	if(pd==null)
        		return;
        	if(CBSystemConstants.isSame==true){
        		ChooseDeviceRectFlashingAction cdrfa = new ChooseDeviceRectFlashingAction(pd);
        		boolean inhere=false;
        		for(int i=0;i<CBSystemConstants.getSamepdlist().size();i++){
        			if(pd.equals(CBSystemConstants.getSamepdlist().get(i))){
        				inhere=true;
        				break;
        			}
        		}
        		if(inhere==false){
        			cdrfa.execute();
        			CBSystemConstants.setSampdlist(pd);
        		}else{
        			cdrfa.backexecute();
        			CBSystemConstants.getSamepdlist().remove(pd);
        		}
        	}
        	else {
	        	Runnable r = new Runnable() {
	    	        public void run() {
	    	        	DeviceInfoMenuProvider provider = new DeviceInfoMenuProvider(pd);
	    	        	popupMenu = provider.createMenu();
	    	        	popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
	    	        }
	    		};
	    		SwingUtilities.invokeLater(r);
        	}
        }
        else if((!fileName.equals("") || (!deviceCode.equals("") && CBSystemConstants.getPowerStation(deviceCode)!=null)) 
        		&& fileName.indexOf("厂站目录")<0 
        		&& !CBSystemConstants.isHrefDoubleClick) {
        	if(popupMenu != null)
				popupMenu.setVisible(false);
        	CreatePowerStationToplogy.openHref(evtElement);
        }
        /*
        else if((fileName.toLowerCase().equals(".svg") || fileName.indexOf("厂站目录")>=0) 
        		&& ((Element)deviceNode).hasAttribute("ChangePicPlane")) {
        	String changePicPlane = ((Element)deviceNode).getAttribute("ChangePicPlane");
        	String array[] = changePicPlane.split(",");
        	if(array.length == 2) {
        		String plane = array[1];
        		Element svgElement = resolver.resolveSvgElement(evtElement.getOwnerDocument());
        		NodeList layerNodeList = svgElement.getElementsByTagName("g");
        		for (int i = 0; i < layerNodeList.getLength(); i++) {
        			if (((Element) layerNodeList.item(i)).getAttribute("id").equals("Other_Layer")) {
        				 Element layer = (Element) layerNodeList.item(i);
        			     NodeList childNodes =layer.getChildNodes();
        			     for (int j = 0; j < childNodes.getLength(); j++) {
        			    	 Node childNode=childNodes.item(j);
        			    	 if(childNode.getNodeName().equals("image"))
        			    	      childNode.getParentNode().removeChild(childNode);
        			     }
        			}
        			if (((Element) layerNodeList.item(i)).getAttribute("id").equals("Other_Layer")) {
        				     Element layer = (Element) layerNodeList.item(i);
        				     NodeList childNodes =layer.getChildNodes();
        				     Element childEle=null;
        				     for (int j = 0; j < childNodes.getLength(); j++) {
        				    	 Node childNode=childNodes.item(j);
        				    	 if(childNode.getNodeName().equals("#text"))
        				    		 continue;
        				    	 childEle=(Element)childNode;
        				    	 if (childEle.getAttribute("Plane").equals(plane)) {
        								childEle.setAttribute("visibility", "visible");
        						 }else{
        								childEle.setAttribute("visibility", "hidden");
        							}
        				     }  
        			}
        			if (((Element) layerNodeList.item(i)).getAttribute("id").equals("Text_Layer")) {
        			     Element layer = (Element) layerNodeList.item(i);
        			     NodeList childNodes =layer.getChildNodes();
        			     Element childEle=null;
        			     for (int j = 0; j < childNodes.getLength(); j++) {
        			    	 Node childNode=childNodes.item(j);
        			    	 if(childNode.getNodeName().equals("#text"))
        			    		 continue;
        			    	 childEle=(Element)childNode;
        			    	 if (childEle.getAttribute("Plane").equals(plane)) {
        							childEle.setAttribute("visibility", "visible");
        					 }else{
        							childEle.setAttribute("visibility", "hidden");
        						}
        			     }  
        		   }
        		}
        		return;
        	}
        }
        */
        else if((fileName.toLowerCase().equals(".svg") || fileName.indexOf("厂站目录")>=0) 
        		&& ((Element)deviceNode).hasAttribute("ChangePicPlane")) { //西北图形特殊处理
        	String changePicPlane = ((Element)deviceNode).getAttribute("ChangePicPlane");
        	String array[] = changePicPlane.split(",");
        	Map map = new HashMap();
        	map.put("1", "陕西");
        	map.put("2", "甘肃");
        	map.put("3", "宁夏");
        	map.put("4", "青海");
        	map.put("5", "新疆");
        	map.put("6", "华中联网");
        	map.put("7", "华北联网");
        	if(array.length == 2) {
        		String plane = map.get(array[1]).toString();
        		Element svgElement = resolver.resolveSvgElement(evtElement.getOwnerDocument());
        		NodeList layerNodeList = svgElement.getElementsByTagName("g");
        		for (int i = 0; i < layerNodeList.getLength(); i++) {
        			if (((Element) layerNodeList.item(i)).getAttribute("id").equals("Other_Layer")) {
        				 Element layer = (Element) layerNodeList.item(i);
        			     NodeList childNodes =layer.getChildNodes();
        			     for (int j = 0; j < childNodes.getLength(); j++) {
        			    	 Node childNode=childNodes.item(j);
        			    	 if(childNode.getNodeName().equals("image"))
        			    	      childNode.getParentNode().removeChild(childNode);
        			     }
        			}
        			if (((Element) layerNodeList.item(i)).getAttribute("id").equals("Other_Layer")) {
        				     Element layer = (Element) layerNodeList.item(i);
        				     NodeList childNodes =layer.getChildNodes();
        				     Element childEle=null;
        				     for (int j = 0; j < childNodes.getLength(); j++) {
        				    	 Node childNode=childNodes.item(j);
        				    	 if(childNode.getNodeName().equals("#text"))
        				    		 continue;
        				    	 childEle=(Element)childNode;
        				    	 if (childEle.getAttribute("Plane").equals(plane)) {
        								childEle.setAttribute("visibility", "visible");
        						 }else{
        								childEle.setAttribute("visibility", "hidden");
        							}
        				     }  
        			}
        			if (((Element) layerNodeList.item(i)).getAttribute("id").equals("Text_Layer")) {
        			     Element layer = (Element) layerNodeList.item(i);
        			     NodeList childNodes =layer.getChildNodes();
        			     Element childEle=null;
        			     for (int j = 0; j < childNodes.getLength(); j++) {
        			    	 Node childNode=childNodes.item(j);
        			    	 if(childNode.getNodeName().equals("#text"))
        			    		 continue;
        			    	 childEle=(Element)childNode;
        			    	 if (childEle.getAttribute("Plane").equals(plane)) {
        							childEle.setAttribute("visibility", "visible");
        					 }else{
        							childEle.setAttribute("visibility", "hidden");
        						}
        			     }  
        		   }
        		}
        		return;
        	}
        }
        else {
        	final PowerDevice pd = CBSystemConstants.getPowerDevice(deviceCode);
        	if(pd != null) {
            	Runnable r = new Runnable() {
        	        public void run() {
        	        	DeviceInfoMenuProvider provider = new DeviceInfoMenuProvider(pd);
        	        	popupMenu = provider.createMenu();
        	        	popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
        	        }
        		};
        		SwingUtilities.invokeLater(r);
            }
        }
        
        if(CBSystemConstants.svgAddPd!=null){ //点击SVG图形选择设备
			SVGAddDeviceInf svgAddPowerDev=CBSystemConstants.svgAddPd;
			svgAddPowerDev.addPowerDevice(dev);
		}
	}

	public void mouseDoubleClick(Event evt) {
		if(CBSystemConstants.isHrefDoubleClick) {
			if(popupMenu != null)
				popupMenu.setVisible(false);
			org.apache.batik.dom.events.DOMMouseEvent me = (org.apache.batik.dom.events.DOMMouseEvent) evt;
	        Element evtElement = ((Element) me.getTarget());
	        CreatePowerStationToplogy.openHref(evtElement);
		}
	}

	public void mouseRightButtonClick(Event evt) {
		final org.apache.batik.dom.events.DOMMouseEvent me = (org.apache.batik.dom.events.DOMMouseEvent) evt;
		final Element evtElement = ((Element) me.getTarget());
        Node deviceNode = evtElement.getParentNode();
        SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
        String deviceID = resolver.getDeviceID((Element)deviceNode);
        Document document = evtElement.getOwnerDocument();
        
        PowerDevice pd = null;
        if(!deviceID.equals("")) {
	        PowerDevice dev = CBSystemConstants.getPowerDevice(deviceID);
	        if(dev != null) {
	        	CreatePowerStationToplogy.loadFacData(dev.getPowerStationID());
	        	pd = CBSystemConstants.getPowerDevice(dev.getPowerStationID(), deviceID);
	        }
        }
		
        final PowerDevice line = pd;
        
    	Runnable r = new Runnable() {
	        public void run() {
	        	if (line == null) {
	            	ScaleMenuProvider provider = ScaleMenuProvider.getInstance(fCanvas, line);
	            	JPopupMenu popupMenu = provider.createMenu();
	            	popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
	            }
	            else {
	            	Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(line);
	            	DeviceMenuBuild dmb=new DeviceMenuBuild(line);
	    			JPopupMenu popupMenu = dmb.createMenu();
	    			popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
	    		}
	        }
		};
		SwingUtilities.invokeLater(r);
	}
	
	public void mouseOut(Event evt) {
		Runnable r = new Runnable() {
	        public void run() {
	        	fCanvas.setCursor(Cursor.getDefaultCursor());
	        }
		};
		SwingUtilities.invokeLater(r);
	}

	public void mouseOver(Event evt) {
		Runnable r = new Runnable() {
	        public void run() {
	        	fCanvas.setCursor(new Cursor(Cursor.HAND_CURSOR));
	        }
		};
		SwingUtilities.invokeLater(r);
	}
	
	// 更新线路两端变电站实时数据
	public void updateStationData(Map<PowerDevice, String> stationlines,
			PowerDevice line) {
		// 重新初始化涉及相关线路的变电站
		String sysLineStatus = "";
		InitDeviceStatus ie = new InitDeviceStatus();
		for (int i = 0; i < stationlines.size(); i++) {
			PowerDevice sl = stationlines.values().size() > 0 ? (PowerDevice) stationlines
					.keySet().toArray()[i]
					: null;
			if (CBSystemConstants
					.getStationPowerDevices(sl.getPowerStationID()) == null) {
				CreatePowerStationToplogy.loadFacEquip(sl.getPowerStationID());
			}
			PowerDevice pd = CBSystemConstants.getPowerDevice(sl.getPowerStationID(), sl.getPowerDeviceID());
			sl.setDeviceStatus(pd.getDeviceStatus());
			if(pd.getDeviceStatus().equals("3"))
				sysLineStatus = pd.getDeviceStatus();
			else if(sysLineStatus.equals(""))
				sysLineStatus = pd.getDeviceStatus();
			else if(!sysLineStatus.equals("3") && Integer.valueOf(pd.getDeviceStatus()) < Integer.valueOf(sysLineStatus))
				sysLineStatus = pd.getDeviceStatus();
		}
		line.setDeviceStatus(sysLineStatus);
		// 修改线路样式
		DeviceSVGPanelUtil.changeDeviceSVGColor(line);

	}
	
}
