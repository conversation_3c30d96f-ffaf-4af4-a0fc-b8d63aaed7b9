package com.tellhow.czp.svg.listener;

import java.awt.Cursor;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;
import javax.swing.JPopupMenu;
import javax.swing.JTabbedPane;
import javax.swing.SwingUtilities;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.events.Event;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.mainframe.menu.DeviceInfoMenuProvider;
import com.tellhow.czp.mainframe.menu.DeviceMenuBuild;
import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.czp.mainframe.menu.MeasurementInfoMenuProvider;
import com.tellhow.czp.mainframe.menu.provider.ModelMenuProvider;
import com.tellhow.czp.mainframe.menu.provider.ScaleMenuProvider;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.util.Dom4jUtil;
import com.tellhow.graphicframework.action.impl.ChooseDeviceRectFlashingAction;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.svg.listener.SvgMouseEventAdapter;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.SVGAddDeviceInf;

/**
 * 厂站图鼠标事件处理器
 * <AUTHOR>
 *
 */
public class StationMouseEventListener extends SvgMouseEventAdapter {
	
	JPopupMenu popupMenu = null;
	
	@Override
	public void mouseClick(Event evt) {
		final org.apache.batik.dom.events.DOMMouseEvent me = (org.apache.batik.dom.events.DOMMouseEvent) evt;
		Element evtElement = ((Element) me.getTarget());
        Node deviceNode = evtElement.getParentNode();
//        System.out.println(Dom4jUtil.ToXMl((Element)deviceNode));
        SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
        //处理测量数据层
        if(deviceNode.getParentNode() instanceof Element) {
	        Element layer=(Element)deviceNode.getParentNode();
	        if(layer.getAttribute("id").equals(resolver.getMeasurementValue_Layer()) || layer.getAttribute("id").equals("TermMeasure_Layer") || layer.getAttribute("id").equals("ScadaRealValue_Layer")){
	        	final String measID = resolver.getTelemeteringID((Element) deviceNode);
	        	Runnable r = new Runnable() {
	    	        public void run() {
	    	        	MeasurementInfoMenuProvider provider = new MeasurementInfoMenuProvider(measID,"0");
	    	        	JPopupMenu popupMenu = provider.createMenu();
	    	        	popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
	    	        }
	    		};
	    		SwingUtilities.invokeLater(r);
	    		return;
	        }
	        else if(layer.getAttribute("id").equals("Other_Layer")){
	        	final String measID = resolver.getDeviceCIMID((Element) deviceNode);
	        	Element ele = resolver.getDeviceGraphElement((Element) deviceNode);
	        	if(ele!=null && ele.getAttributeNS("http://www.w3.org/1999/xlink" , "href")!=null) {
					String href = ele.getAttributeNS("http://www.w3.org/1999/xlink" , "href").replace("#", "");
					if(href.contains("自投") || href.contains("rectangleGzp")) {
						Runnable r = new Runnable() {
			    	        public void run() {
			    	        	MeasurementInfoMenuProvider provider = new MeasurementInfoMenuProvider(measID,"1");
			    	        	JPopupMenu popupMenu = provider.createMenu();
			    	        	popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
			    	        }
			    		};
			    		SwingUtilities.invokeLater(r);
			    		return;
					}
	        	}
	        }
        }
        
        
        PowerDevice dev2 = null;
        String deviceID = resolver.getDeviceID((Element)deviceNode);
        if(!deviceID.equals("")) {
	        Document document = evtElement.getOwnerDocument();
	        String stationId = resolver.getStationID(document);
	        dev2 = (PowerDevice) CBSystemConstants.getPowerDevice(stationId, deviceID);
	        if(dev2 == null) {
	       	 String sid = resolver.resolveSvgElement(document).getAttribute("StationID");
	       	 dev2 = (PowerDevice) CBSystemConstants.getPowerDevice(sid, deviceID);
	       }
	        if(dev2 == null) {
	        	dev2 = (PowerDevice) CBSystemConstants.getPowerDevice(deviceID);
	       }
        }
        
        
        final PowerDevice pd = dev2;
        String deviceCode = resolver.getDeviceID((Element)deviceNode);
        String fileName = resolver.getHref((Element)deviceNode);
        if (pd == null) {
        	if((!fileName.equals("") || !deviceCode.equals("")) 
            		&& !CBSystemConstants.isHrefDoubleClick) {
            	if(popupMenu != null)
    				popupMenu.setVisible(false);
            	CreatePowerStationToplogy.openHref(evtElement);
            	
            }
        	return;
        }
        else if(CBSystemConstants.svgAddPd!=null){ //点击SVG图形选择设备
			SVGAddDeviceInf svgAddPowerDev=CBSystemConstants.svgAddPd;
			svgAddPowerDev.addPowerDevice(pd);
		}
    	else if(CBSystemConstants.isSame) { //多选设备开票方式
    		if(CBSystemConstants.isSame==true){
        		ChooseDeviceRectFlashingAction cdrfa = new ChooseDeviceRectFlashingAction(pd);
        		boolean inhere=false;
        		for(int i=0;i<CBSystemConstants.getSamepdlist().size();i++){
        			if(pd.equals(CBSystemConstants.getSamepdlist().get(i))){
        				inhere=true;
        				break;
        			}
        		}
        		if(inhere==false){
        			cdrfa.execute();
        			CBSystemConstants.setSampdlist(pd);
        		}else{
        			cdrfa.backexecute();
        			CBSystemConstants.getSamepdlist().remove(pd);
        		}
        	}
    	}
    	else {
	    	Runnable r = new Runnable() {
		        public void run() {
	            	if(SystemConstants.isInitfigureright.equals("0")){
	            		DeviceInfoMenuProvider provider = new DeviceInfoMenuProvider(pd);
			        	JPopupMenu popupMenu = provider.createMenu();
			        	try{
			        		popupMenu.show(fCanvas, me.getClientX()+10, me.getClientY());
			        	}catch(Exception ex) {
			        	}
	            	}else{
	            		if(CBSystemConstants.roleCode.equals("0")&&CBSystemConstants.cardbuildtype.equals("1")){
		            		String devicetype=pd.getDeviceType();
		            		if(devicetype.equals(SystemConstants.Switch)||devicetype.equals(SystemConstants.SwitchSeparate)||devicetype.equals(SystemConstants.SwitchFlowGroundLine)){
		            			DeviceMenuModel dmm=new DeviceMenuModel();
		            			String statevalue="";
		            			if(pd.getDeviceStatus().equals("0")||pd.getDeviceStatus().equals("1")){
			            			if(pd.getDeviceStatus().equals("0")){
			            				statevalue="1";
			            			}else{
			            				statevalue="0";
			            			}
			            			String sql="select * from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and  statevalue='"+statevalue+"' and opcode='"+CBSystemConstants.opCode+"' and devicetypeid='"+pd.getDeviceType()+"' and cardbuildtype='1' and parentcode='0'";
			            			List result=DBManager.queryForList(sql);
			            			Map map=(Map)result.get(0);
			            			dmm.setStatecode(StringUtils.ObjToString(map.get("statecode")));
			            			dmm.setParentcode(StringUtils.ObjToString(map.get("parentcode")));
			            			dmm.setRuntype(StringUtils.ObjToString(map.get("devicetypeid")));
			            			dmm.setStatevalue(StringUtils.ObjToString(map.get("statevalue")));
			            			dmm.setPd(pd);
			            			dmm.setStatename(StringUtils.ObjToString(map.get("statename")));
			            			dmm.setStatetype(StringUtils.ObjToString(map.get("cardbuildtype")));
			            			DeviceOperate dre = new DeviceOperate();
			            			boolean issuss = dre.execute(pd, dmm);
		            			}else{
		            				
		            			}
		            			
		            		}else{
		            			DeviceInfoMenuProvider provider = new DeviceInfoMenuProvider(pd);
		    		        	JPopupMenu popupMenu = provider.createMenu();
		    		        	popupMenu.show(fCanvas, me.getClientX()+10, me.getClientY());
		            		}
	            		}else{
	            			DeviceInfoMenuProvider provider = new DeviceInfoMenuProvider(pd);
	    		        	JPopupMenu popupMenu = provider.createMenu();
	    		        	popupMenu.show(fCanvas, me.getClientX()+10, me.getClientY());
	            		}
	            	}
		        }
			};
			SwingUtilities.invokeLater(r);
    	}
	}
	
	public void mouseRightButtonClick(Event evt) {
		final org.apache.batik.dom.events.DOMMouseEvent me = (org.apache.batik.dom.events.DOMMouseEvent) evt;
		Element evtElement = ((Element) me.getTarget());
        final Element deviceNode = (Element)evtElement.getParentNode();
        SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
        String deviceID = resolver.getDeviceID(deviceNode);
        Document document = evtElement.getOwnerDocument();
        String stationId = resolver.getStationID(document);
        
    
       
        PowerDevice dev2 = (PowerDevice) CBSystemConstants.getPowerDevice(stationId, deviceID);
        if(dev2 == null) {
        	dev2 = (PowerDevice) CBSystemConstants.getPowerDevice(deviceID);
       }
        if(dev2 == null) {
        	 String sid = resolver.resolveSvgElement(document).getAttribute("StationID");
        	 dev2 = (PowerDevice) CBSystemConstants.getPowerDevice(sid, deviceID);
        }
        
        final PowerDevice pd = dev2;
        
        //List<PowerDevice> devList = RuleExeUtil.getKnifeRelateSwitch(pd);
    	//boolean devList1 = RuleExeUtil.isKnifeLinkSwitch(pd);
    	//PowerDevice devList2 = RuleExeUtil.getDeviceSwitch(pd);
        Runnable r = new Runnable() {
	        public void run() {
	        	if (pd == null) {
	        		if(deviceNode.getNodeName().equals("g")) {
	        			ModelMenuProvider provider = new ModelMenuProvider(fCanvas, deviceNode);
		            	JPopupMenu popupMenu = provider.createMenu();
		            	popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
	        		}
	        		else {
	        			ScaleMenuProvider provider = ScaleMenuProvider.getInstance(fCanvas, pd);
		            	JPopupMenu popupMenu = provider.createMenu();
		            	popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
	        		}
	        		ScaleMenuProvider provider = ScaleMenuProvider.getInstance(fCanvas, pd);
	            	JPopupMenu popupMenu = provider.createMenu();
	            	popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
	            }
	            else {
	            		//监控开票与调度令闭锁
	            		if(CBSystemConstants.cardbuildtype.equals("0") && CBSystemConstants.roleCode.equals("2")) { //监控智能开票
		            		if(!CBSystemConstants.ddzlID.equals("") && CBSystemConstants.rbmList.size() > 0) { //传入了调度令
		            			RuleBaseMode rbm = CBSystemConstants.rbmList.get(0);
			            		if(rbm.getPd() != null && !rbm.getBeginStatus().equals("") && !rbm.getEndState().equals("")) {
				            		if(!rbm.getPd().equals(pd)) {
				            			
				            			String message = "<html>调度令是[<font color=red>"+CBSystemConstants.ddzl+"</font>]，是否继续操作["+CZPService.getService().getDevName(pd)+"？]</html>";
				            			int option = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), message,"提示",JOptionPane.YES_NO_OPTION);
				            			if(option != JOptionPane.YES_OPTION)
				            				return;
				            		}
				            		else if(rbm.getEndState().equals(pd.getDeviceStatus())) {
				            			String message = "<html>调度令是[<font color=red>"+CBSystemConstants.ddzl+"</font>]，["+CZPService.getService().getDevName(pd)+"]已经在"+CBSystemConstants.getDeviceStatusName(pd.getDeviceType(), pd.getDeviceStatus())+"状态</html>";
				            			int option = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), message,"提示",JOptionPane.YES_NO_OPTION);
				            			if(option != JOptionPane.YES_OPTION)
				            				return;
				            		}
				            		//由于拟票时实时状态可能和操作初始状态不一致，取消该校验
//				            		else if(!rbm.getBeginStatus().equals(pd.getDeviceStatus())) {
//				            			String message = "<html>调度令是[<font color=red>"+CBSystemConstants.ddzl+"</font>]，["+CZPService.getService().getDevName(pd)+"]不在"+CBSystemConstants.getDeviceStatusName(pd.getDeviceType(), rbm.getBeginStatus())+"状态</html>";
//				            			int option = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), message,"提示",JOptionPane.YES_NO_OPTION);
//				            			if(option != JOptionPane.YES_OPTION)
//				            				return;
//				            		}
			            		}
		            		}
		            		else if(!CBSystemConstants.ddpID.equals("")) { //传入了调度票
		            			RuleBaseMode rbm = null;
		            			for(RuleBaseMode rb :CBSystemConstants.rbmList) {
		            				if(rb.getPd() != null && rb.getPd().equals(pd)) {
		            					rbm = rb;
		            					break;
		            				}
		            			}
			            		if(rbm == null) {
			            			if(!CBSystemConstants.ddzl.equals("")) {
				            			String message = "<html>调度令是[<font color=red>"+CBSystemConstants.ddzl+"</font>]，<br>是否继续操作[<font color=blue>"+CZPService.getService().getDevName(pd)+"</font>]？</html>";
				            			int option = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), message,"提示",JOptionPane.YES_NO_OPTION);
				            			if(option != JOptionPane.YES_OPTION)
				            				return;
			            			}
			            		}
			            		else if(rbm.getEndState().equals(pd.getDeviceStatus())) {
			            			String message = "<html>调度令是[<font color=red>"+CBSystemConstants.ddzl+"</font>]，["+CZPService.getService().getDevName(pd)+"]已经在"+CBSystemConstants.getDeviceStatusName(pd.getDeviceType(), pd.getDeviceStatus())+"状态，<br>是否继续操作[<font color=blue>"+CZPService.getService().getDevName(pd)+"</font>]？</html>";
			            			int option = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), message,"提示",JOptionPane.YES_NO_OPTION);
			            			if(option != JOptionPane.YES_OPTION)
			            				return;
			            		}
			            		else if(!rbm.getBeginStatus().equals(pd.getDeviceStatus())) {
			            			String message = "<html>调度令是[<font color=red>"+CBSystemConstants.ddzl+"</font>]，["+CZPService.getService().getDevName(pd)+"]不在"+CBSystemConstants.getDeviceStatusName(pd.getDeviceType(), rbm.getBeginStatus())+"状态，<br>是否继续操作[<font color=blue>"+CZPService.getService().getDevName(pd)+"</font>]？</html>";
			            			int option = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), message,"提示",JOptionPane.YES_NO_OPTION);
			            			if(option != JOptionPane.YES_OPTION)
			            				return;
			            		}
		            		}
	            		}
	            		boolean isCanOp = false;
	            		if(CBSystemConstants.getUser().getOrganID().equals("0") ||
	            				CBSystemConstants.getUser().getOrganID().equals("")|| 
	            				CBSystemConstants.getUser().getOrganID().equals("system") ||
	            				pd.getOrgaId().equals("") ||
	            				CBSystemConstants.getUser().getOrganID().equals(pd.getOrgaId())) {
	            			
	            			if(CBSystemConstants.roleCode.equals("0")) {
	            				isCanOp = true;
	            			}
	            			else if(CBSystemConstants.roleCode.equals("1")) {
	            				if(pd.isPW()) {
	            					isCanOp = true;
	            				}
	            				
	            				else
	            					isCanOp = true;
	            			}
	            			else if(CBSystemConstants.roleCode.equals("2")) {
	            				isCanOp = true;
	            			}
	            			else
		            			isCanOp = true;
	            		}
	            		else
	            			isCanOp = true;
	            		if(isCanOp) {
            				DeviceMenuBuild dmb=new DeviceMenuBuild(pd);
			    			JPopupMenu popupMenu = dmb.createMenu();
			    			popupMenu.show(fCanvas, me.getClientX(), me.getClientY());
            			}
	    		}
	        }
		};
		SwingUtilities.invokeLater(r); 
	}
	public void mouseOut(Event evt) {
		Runnable r = new Runnable() {
	        public void run() {
	        	fCanvas.setCursor(Cursor.getDefaultCursor());
	        }
		};
		SwingUtilities.invokeLater(r);
	}

	public void mouseOver(Event evt) {
		Runnable r = new Runnable() {
	        public void run() {
	        	fCanvas.setCursor(new Cursor(Cursor.HAND_CURSOR));
	        }
		};
		SwingUtilities.invokeLater(r);
	}

	@Override
	public void mouseDoubleClick(Event evt) {
		if(CBSystemConstants.isHrefOnLine) {
			org.apache.batik.dom.events.DOMMouseEvent me = (org.apache.batik.dom.events.DOMMouseEvent) evt;
			Element evtElement = ((Element) me.getTarget());
	        Element deviceElement = (Element)evtElement.getParentNode();
	        SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
	        Node deviceNode = evtElement.getParentNode();
	        String deviceID = resolver.getDeviceID((Element)deviceNode);
	        Document document = evtElement.getOwnerDocument();
	        String stationId = resolver.getStationID(document);
	       
			final PowerDevice pd = (PowerDevice) CBSystemConstants.getPowerDevice(stationId, deviceID);
			if(pd != null) {
				List<PowerDevice> anothers = RuleExeUtil.getLineOtherSideList(pd);
				for (PowerDevice another : anothers) {
					List<SVGFile> files=SystemConstants.getSVGFileByStationID(another.getPowerStationID());
					String filePath = "";
					String fileName = "";
					if(files.size()!=1){
						CZPService.getService().filterMap(files, another.getPowerStationID());
					}
					if(files.size()==1){
						filePath = files.get(0).getFilePath();
						fileName = files.get(0).getFileName();
					}else if (files.size()>0){
						Object[] options = files.toArray();
						int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
					    if(i==-1)
						    return;
					    filePath = files.get(i).getFilePath();
					    fileName = files.get(i).getFileName();
					}			    
				    String deviceCode = another.getPowerStationID();
				    String deviceName = "";  
					if(fileName.equals(""))
					    return;
					if(!deviceCode.equals("") && CBSystemConstants.getPowerStation(deviceCode)!= null) {
						PowerDevice st = CBSystemConstants.getPowerStation(deviceCode);
						if(st.getPowerStationName().indexOf(".") > 0)
							deviceName =st.getPowerDeviceName();
						else
							deviceName =CZPService.getService().getDevName(st);
					}
					else
						return;
					if (SystemConstants.getGuiBuilder().activateTabbedPageByName(fileName))
					    return;
					filePath =SystemConstants.FILE_SVGMAP_PATH+fileName;
					CreatePowerStationToplogy.createSVGPanel(deviceCode, deviceName, fileName, filePath);				
				}
			}
		}
	}
}
