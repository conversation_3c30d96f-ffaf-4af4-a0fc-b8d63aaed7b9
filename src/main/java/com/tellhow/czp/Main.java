package com.tellhow.czp;


import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;
import java.io.File;
import java.net.URL;

import javax.swing.UIManager;

import org.apache.log4j.Logger;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.basic.ReaderConfiguration;
import com.tellhow.czp.mainframe.LoadWindow;
import com.tellhow.czp.user.UserDao;
import com.tellhow.czp.user.UserLoginInter;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.mainframe.GuiBuilder;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.resource.file.frame.CallbackHander;
import com.tellhow.resource.file.logic.GraphResourceEntityLogic;
import com.tellhow.resource.file.logic.GraphResourceLogic;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;



/**
 * 
 * <AUTHOR>
 *
 */
public class Main {

	
    
	
	private static Logger log = Logger.getLogger(Main.class);
	private static LoadWindow loadWindow = null;
	
//	public static void main(String[] args) {
//		Main main = new Main();
//		main.init(ExampleBuilder.class);
//	}
	
	protected static void init() {
		init(ExampleBuilder.class);
	}
	
	protected static void init(final Class clazz) {
//		if (!WorkerBTT.bindAndListen()) {
//            System.out.println("系统已经启动!");
//            WorkerBTT.sendBringToTop(); //激活已经启动的实例
//            System.exit(-1);
//        }
		
		//设置外观
		try {
			UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
		}
		catch(Exception ex) {
			ex.printStackTrace();
		}
		
		//显示启动界面
		loadWindow = new LoadWindow();
		loadWindow.loadwin();
		
		if(DBManager.isConnected()) {
			
			//启动加载项
			StartupManager.startup();
			
			//判断用户MAC地址
			if(CBSystemConstants.isvalidateMAC){
				new  ReaderConfiguration().judgeMacAddress();
			}
		
			//显示登录窗口
			UserLoginInter.getInstance().setVisible(true);
			
			//读取用户配置
			UserDao userdao=new UserDao();
			userdao.LoadUserLike(CBSystemConstants.getUser());
			
			//创建图形界面
			try {
				if(CBSystemConstants.isAutoLoadSVGFile.equals("1")){
					CallbackHander callbackHander = new CallbackHander() {
						public void execute() {
							initGUI(clazz);
						}
					};
					GraphResourceLogic graphResourceLogic = new GraphResourceEntityLogic("tbp.sys.DataSource1");
					graphResourceLogic.downloadResourceFromServer(CBSystemConstants.projectID, "资源下载中....", callbackHander);
				}
				else
					initGUI(clazz);
			}
			catch(Exception ex) {
				ex.printStackTrace();
				log.error(ex.getMessage(), ex);
			}
		}
		else { //离线模式启动
			System.exit(-1);
		}
	}
	
	private static void initGUI(Class clazz) {
		
		try {
			//初始化图形
			SystemConstants.initMapSVGFile();
			//设置默认图形
			CZPService.getService().setMapSVGFileDefault();
			//设置图形文件类型
			SystemConstants.loadMapSVGFile();
			//关闭启动界面
			loadWindow.closeWindow();
			//创建主界面
			URL url = (new File("config/GUIBuilder.xml")).toURI().toURL();
			GuiBuilder.createGuiBuilder(clazz, url);
		}
		catch(Exception ex) {
			ex.printStackTrace();
			log.error(ex.getMessage(), ex);
		}

	}
	
   
}
