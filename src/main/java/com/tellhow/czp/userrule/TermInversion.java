package com.tellhow.czp.userrule;

import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.graphicframework.action.impl.ChangeDeviceFlashingAction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;

public class TermInversion {
	PowerDevice preDevice = null;
	PowerDevice dev;
	List list;

	public void termInversion(String cardId) {

		String powerstationName = SystemConstants.getGuiBuilder()
				.getActivateSVGPanel().getName();

//		String sql = "select t6.equipid,t6.beginstatus,t6.endstate,t7.station_id,t8.station_name from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t6 ,"+CBSystemConstants.opcardUser+"t_e_equipinfo t7 ,"+CBSystemConstants.opcardUser+"t_e_substation t8"
//				+ " where t6.equipid=t7.equip_id and t7.station_id=t8.station_id  order by t6.stateid asc";

		list = DBManager.query(OPEService.getService().TermInversion());

		// 打开或激活电站
		for (int i = 0; i < list.size(); i++) {
			Map li = (Map) list.get(i);
			DeviceSVGPanelUtil.openSVGPanel((String) li.get("station_id"));
		}
		if(JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "您确认要反演该命令票的操作过程!","操作提示",JOptionPane.YES_NO_OPTION)==JOptionPane.YES_OPTION)
			{
								JOptionPane.showMessageDialog(null, "反演结束!");
			}
			else
			{
								
			}
				
				
	

	}

	DeviceOperate operate = new DeviceOperate();


}
class testFlash extends Thread {
	private volatile PowerDevice pd;
    public testFlash(PowerDevice srcDev,boolean isflash){
		this.pd=srcDev;
		this.start();
	}
    public void run(){  
		ChangeDeviceFlashingAction cfa = new ChangeDeviceFlashingAction(pd, "3");
		cfa.execute();
    }
}
class testDevStatusAction extends Thread {
	private volatile PowerDevice pd;
    public testDevStatusAction(PowerDevice srcDev){
		this.pd=srcDev;
		this.start();
	}
    public void run(){  
    	DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
    }
}


