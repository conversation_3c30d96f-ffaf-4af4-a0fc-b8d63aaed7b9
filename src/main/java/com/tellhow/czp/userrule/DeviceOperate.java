/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 设备右键操作类调用规则框架，生成操作票等
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.userrule;

import java.awt.Dimension;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.Rectangle;
import java.awt.Toolkit;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.swing.JOptionPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;

import com.sun.org.apache.xml.internal.security.keys.content.RetrievalMethod;
import com.tellhow.czp.Robot.InversionTicket;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.mainframe.DriverPorpery;
import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.czp.operationcard.ReverseTempTicketDefault;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.operationcard.dao.DeviceStatusManager;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.action.SvgAction;
import com.tellhow.graphicframework.action.impl.ChangeColorAction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.UserRuleExecute;
import czprule.rule.conditionmodel.judgeLineOperateOrder;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.runmode.ExecuteDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.ShowMessage;
import czprule.wordcard.WordExecute;
import czprule.wordcard.dao.DeviceStateMentManager;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;
import czprulepw.PWSystemConstants;

public class DeviceOperate implements RulebaseInf {

	/**
	 * 全部操作对应的操作设备集合，在保存到数据库后清空
	 */
	private static  Map<Integer, DispatchTransDevice> alltransDevMap  = new HashMap<Integer, DispatchTransDevice>();
	//初始化全部操作对应的操作设备集合，在保存到数据库后清空
	private static  Map<Integer, DispatchTransDevice> chushialltransDevMap  = new HashMap<Integer, DispatchTransDevice>(); 

	//反向成票所有保存操作设备集合
	private static  Map<Integer, DispatchTransDevice> reversetransDevMap  = new HashMap<Integer, DispatchTransDevice>();
	
	
	private static int bzIndex = 0;
	public  static void ClearDevMap(){
		bzIndex = 0;
		alltransDevMap.clear();
		PWSystemConstants.loseelecDeviceMap.clear();
		CBSystemConstants.LineTransform.clear();
		CBSystemConstants.LineTagStatus.clear();
		CBSystemConstants.tagStatusBeginMap.clear();
		CBSystemConstants.getCurRBMList().clear();
		CBSystemConstants.tagStatusMap.clear();
		
	}
	public static int getBzIndex() {
		return bzIndex;
	}
	public static void setBzIndex(int bzIndex) {
		DeviceOperate.bzIndex = bzIndex;
	}
	public static Map<Integer, DispatchTransDevice> getAlltransDevMap(){
		return alltransDevMap;
	}
	public static void setAlltransDevMap(Map<Integer, DispatchTransDevice> alltransDevMap) {
		DeviceOperate.alltransDevMap = alltransDevMap;
	}
	
	public static Map<Integer, DispatchTransDevice> getReversetransDevMap() {
		return reversetransDevMap;
	}
	public static void setReversetransDevMap(
			Map<Integer, DispatchTransDevice> reversetransDevMap) {
		DeviceOperate.reversetransDevMap = reversetransDevMap;
	}
	public boolean execute(PowerDevice pd, DeviceMenuModel dmm) {
		RuleBaseMode rbm=new RuleBaseMode();
		String statecode=dmm.getStatecode();
		CBSystemConstants.notRollBack =false;
		if(statecode.contains(",")) {
			CBSystemConstants.isMultiTicket = true;
			String[] arr = statecode.split(",");
			for(String code : arr) {
				RuleBaseMode currbm = getRBMbyCode(pd, code);
				CBSystemConstants.setCurRBM(currbm);
				currbm.setTranType(statecode);
				bzIndex++;
				boolean rs = execute(currbm);
				if(!rs)
					return true;
				if(CBSystemConstants.isSame && CBSystemConstants.samepdlist.size() == 0)
					return true;
			}
			CBSystemConstants.isMultiTicket = false;
			return true;
		}
		
		int num=statecode.indexOf(":");
		String [] newsplitstr=new String [10];
		if(num>0){
			newsplitstr = statecode.split(":");
			//保护设备的code
			String czcode=newsplitstr[0];
			//保护设备选择的操作的code
			String bhcode=newsplitstr[1];
			rbm=getbh(pd,bhcode,czcode);
		}else{
			rbm = getRBM(pd, dmm);
		}
		if("".equals(pd.getOrgaId())){
			String orgaID=CBSystemConstants.getOrgaID(pd.getPowerDeviceID());
			pd.setOrgaId(orgaID);
		}
		CBSystemConstants.setCurRBM(rbm);
		bzIndex++;
		if(CBSystemConstants.reverse==true){
			return executeReverse(rbm);
		}else if(CBSystemConstants.oneClickOpenerTicket ==true){
			return excuteOneClick(rbm);
		}else{
			return execute(rbm);
		}
		
	}
	
	public boolean executeZt(PowerDevice pd, RuleBaseMode rbm) {
		if("".equals(pd.getOrgaId())){
			String orgaID=CBSystemConstants.getOrgaID(pd.getPowerDeviceID());
			pd.setOrgaId(orgaID);
		}
		CBSystemConstants.setCurRBM(rbm);
		bzIndex++;
		if(CBSystemConstants.reverse==true){
			return executeReverse(rbm);
		}else if(CBSystemConstants.oneClickOpenerTicket ==true){
			return excuteOneClick(rbm);
		}else{
			return execute(rbm);
		}
		
	}
	

	/**
	 * 一建成票功能
	 * <AUTHOR>
	 * @param Srcrbm
	 * @return
	 * @since 2014-10-24 9:16
	 */
	public boolean excuteOneClick(RuleBaseMode Srcrbm) {
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.getCurOperateDevs().clear();
		
		///设备对位时操作线路设置为电源侧
		if(!CBSystemConstants.isLock && Srcrbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)) {
			CBSystemConstants.LineSource.put(Srcrbm.getPd().getPowerDeviceID(), Srcrbm.getPd());
			CBSystemConstants.LineLoad.put(Srcrbm.getPd().getPowerDeviceID(), new ArrayList<PowerDevice>());
		}
		//一、执行规则
//		CBSystemConstants.isLock = false;
		UserRuleExecute userRuleExc=new UserRuleExecute();
		if(userRuleExc.execute(Srcrbm)){
			RuleExecute ruleExc=new RuleExecute();
			RuleExecute.setShowMessage(true);
			if(!ruleExc.execute(Srcrbm)){
				if(CBSystemConstants.cardbuildtype.equals("0")) {
					if(TempTicket.getTempTicket()==null) {
			    		CBSystemConstants.oneClickOpenerTicket =false;
						putDeviceStatus(Srcrbm.getPd());
						RollbackDeviceStatus();
						ClearDevMap();
						CBSystemConstants.getDtdMap().clear();
					}
					else {
			    		CBSystemConstants.oneClickOpenerTicket =false;
						RollbackDeviceStatusStep();
						if(TempTicket.getTempTicket()==null)
							CBSystemConstants.clearLineSourceAndLoad();
					}
				}
				else if(CBSystemConstants.cardbuildtype.equals("1")) {
		    		CBSystemConstants.oneClickOpenerTicket =false;
					RollbackDeviceStatusStep();
					if(TempTicket.getTempTicket()==null)
						CBSystemConstants.clearLineSourceAndLoad();
				}
				
				return false;
			}
		}
		else
			return false;
		
		//二、将本次操作设备记录保存到缓存中
		putDeviceStatus(Srcrbm.getPd());
    	//模拟演示
		if(!CBSystemConstants.cardbuildtype.equals("2"))
			CBSystemConstants.getCurRBMList().add(Srcrbm);
    	if(CBSystemConstants.isOutCard){    	
    		//三、构造术语
    		CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
	    	CardModel cm = new CardModel();
	    	cm.setCardItems(new ArrayList<CardItemModel>());
	    	
	    	if(CBSystemConstants.roleCode.equals("0")) {
	    		cm=WordExecute.getInstance().execute(Srcrbm);
	    	}
	    	else {
	    		setTask(cm);
				CardModel cardModel=WordExecute.getInstance().execute(Srcrbm);
				cm.getCardItems().addAll(cardModel.getCardItems());
	    	}

	    	for(int i=0;i<cm.getCardItems().size();i++){
	    		cm.getCardItems().get(i).setBzbj(String.valueOf(bzIndex));
	    	}
	    	//四、显示操作票窗口
	    	RuleBaseMode r = CBSystemConstants.getCurRBM();
		    TempTicket ttk=TempTicket.getInstance();
		    ttk.init(cm,Srcrbm);
//		    if(SystemConstants.isInitNewWin.equals("0")){}else{}
    	}else{
    		CBSystemConstants.oneClickOpenerTicket =false;
    		DeviceStatusManager dsm = new DeviceStatusManager();
    		if(DBManager.isInterfaceOpened){
    			dsm.insertDevStatu();
    		}else{
    			try {
    				Connection conn = DBManager.getConnection();
    				dsm.insertDevStatu(conn);
    				conn.close();
    			} catch (SQLException e) {
    				// TODO Auto-generated catch block
    				e.printStackTrace();
    			} // 保存操作票对应设备预令状态
    		}
    	}
    	//五、改变设备图形操作
    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
		if(!CBSystemConstants.isOutCard) {
			CBSystemConstants.getDtdMap().clear();
			alltransDevMap.clear();
		}
		CBSystemConstants.oneClickReturn = "true";
	    return true;
	}
	
	public boolean execute(RuleBaseMode Srcrbm) {
//		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.getCurOperateDevs().clear();
		
		///设备对位时操作线路设置为电源侧
		if(!CBSystemConstants.isLock && Srcrbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)) {
			CBSystemConstants.LineSource.put(Srcrbm.getPd().getPowerDeviceID(), Srcrbm.getPd());
			CBSystemConstants.LineLoad.put(Srcrbm.getPd().getPowerDeviceID(), new ArrayList<PowerDevice>());
		}
		
    	if(CBSystemConstants.isCXHCZ.equals("1")||CBSystemConstants.isCXHCZ.equals("2")){
			if(!Srcrbm.getPd().getDeviceType().equals(SystemConstants.Switch)&&!Srcrbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)){
				ShowMessage.view("程序化操作及EMS程序化操作模式只能生成线路及开关停送电操作票！");
				return false;
			}
		}
    	
    	if(CBSystemConstants.cardbuildtype.equals("2") && Srcrbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)) { //线路设备对位，直接设置电源负荷侧
			new judgeLineOperateOrder().execute(Srcrbm);
		}
		
		
		//一、执行规则
		UserRuleExecute userRuleExc=new UserRuleExecute();
		if(userRuleExc.execute(Srcrbm)){
			RuleExecute ruleExc=new RuleExecute();
			RuleExecute.setShowMessage(true);
			if(!ruleExc.execute(Srcrbm)){
				
				if(CBSystemConstants.cardbuildtype.equals("0")) {
					if(TempTicket.getTempTicket()==null) {
						putDeviceStatus(Srcrbm.getPd());
						RollbackDeviceStatus();
						ClearDevMap();
						CBSystemConstants.getDtdMap().clear();
					}
					else {
						RollbackDeviceStatusStep();
						if(TempTicket.getTempTicket()==null)
							CBSystemConstants.clearLineSourceAndLoad();
					}
				}
				else if(CBSystemConstants.cardbuildtype.equals("1")) {
					RollbackDeviceStatusStep();
					if(TempTicket.getTempTicket()==null)
						CBSystemConstants.clearLineSourceAndLoad();
				}
				
				return false;
			}
		}
		else
			return false;
		
		//二、将本次操作设备记录保存到缓存中
		putDeviceStatus(Srcrbm.getPd());
    	
    	//模拟演示
		if(CBSystemConstants.isInversion){ 
			
	    	CardModel cm=WordExecute.getInstance().execute(Srcrbm);
	    	InversionTicket ttk=InversionTicket.getInstance();
	    	ttk.init(cm,Srcrbm);
	    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(0.52);
			splitPane.setRightComponent(ttk);
			
			//五、改变设备图形操作
	    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
			if(!CBSystemConstants.isOutCard) {
				CBSystemConstants.getDtdMap().clear();
				alltransDevMap.clear();
			}
			return true;
    	}

		if(!CBSystemConstants.cardbuildtype.equals("2"))
			CBSystemConstants.getCurRBMList().add(Srcrbm);
		
		//设置接线图做过设备对位
		JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
			SVGCanvasPanel panel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
			panel.setSetStatus(true);
		}
    	
    	if(CBSystemConstants.isOutCard){    	
    		//三、构造术语
    		CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
	    	CardModel cm = new CardModel();
	    	cm.setCardItems(new ArrayList<CardItemModel>());
	    	
	    	
	    	
	    	if(CBSystemConstants.roleCode.equals("0")) {
	    		cm=WordExecute.getInstance().execute(Srcrbm);
	    		if(cm.getCardItems()==null){
	    			//回滚
					DeviceOperate.RollbackDeviceStatus();
					//清空
					DeviceOperate.ClearDevMap();
					CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
					CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
			    	CBSystemConstants.bztRelationRecord.clear();
					CBSystemConstants.bztStateRecord.clear();
					CBSystemConstants.bztOrganRecord.clear();
	    			return false;
	    		}
	    	}
	    	else if(CBSystemConstants.roleCode.equals("1")) {
	    		cm=WordExecute.getInstance().execute(Srcrbm);
	    		if(cm.getCardItems()==null){
	    			//回滚
					DeviceOperate.RollbackDeviceStatus();
					//清空
					DeviceOperate.ClearDevMap();
					CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
					CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
			    	CBSystemConstants.bztRelationRecord.clear();
					CBSystemConstants.bztStateRecord.clear();
					CBSystemConstants.bztOrganRecord.clear();
	    			return false;
	    		}
	    	}
	    	else {
	    		setTask(cm);
				CardModel cardModel=WordExecute.getInstance().execute(Srcrbm);
				cm.getCardItems().addAll(cardModel.getCardItems());
				cm.setBzsx(cardModel.getBzsx());
	    	}
	    	
	    	
	    	for(int i=0;i<cm.getCardItems().size();i++){
	    		cm.getCardItems().get(i).setBzbj(String.valueOf(bzIndex));
	    	}
	    	//四、显示操作票窗口
	    	TempTicket ttk = null;
	    	if(CBSystemConstants.isMultiTicket) {
	    		ttk=TempTicket.getNewInstance();
	    	}
	    	else {
	    		ttk=TempTicket.getInstance();
	    	}
	    	ttk.init(cm,Srcrbm);

	    	if(SystemConstants.isInitNewWin.equals("0")){
		    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		    	Dimension scrsiz = Toolkit.getDefaultToolkit().getScreenSize();
	    		double bl = 0;

		    	if(CBSystemConstants.isFixedSize){
		    		bl = 0.75;
		    	}else{
			    	if(scrsiz.width>1280){
			    		bl = 0.45;
			    	}else if(scrsiz.width<=1280&&scrsiz.width>1024){
			    		bl = 0.30;
			    	}else if(scrsiz.width<=1024&&scrsiz.width>800){
			    		bl = 0.20;
			    	}else{
			    		bl =0.15;
			    	}
			    	double minbl = 1-Double.valueOf(ttk.getPanelLength())/scrsiz.width;
			    	if(minbl < bl && minbl > 0 && minbl < 1)
			    		bl = minbl;
			    	
			    	if(CBSystemConstants.isTicketFullScreen){
			    		bl=0;
			    	}
			    	if(CBSystemConstants.tempticketSpit>0){
			    		bl=CBSystemConstants.tempticketSpit;
			    	}
		    	}
		    	
		    	splitPane.setDividerLocation(bl);
		    	
		    	splitPane.setEnabled(true);  
		    	if(CBSystemConstants.isMultiTicket) {
		    		if(!(splitPane.getRightComponent() instanceof JTabbedPane)) {
				    	JTabbedPane jtp = new JTabbedPane();
				    	jtp.addTab("操作票1", ttk);
				    	splitPane.setRightComponent(jtp);
		    		}
		    		else {
		    			JTabbedPane jtp = (JTabbedPane)splitPane.getRightComponent();
		    			jtp.addTab("操作票"+String.valueOf(((JTabbedPane)splitPane.getRightComponent()).getTabCount()+1), ttk);
		    			if(!jtp.isVisible())
		    				jtp.setVisible(true);
		    		}
			    	
		    	}
		    	else if(CBSystemConstants.dcchzMap.size() > 0 && !CBSystemConstants.isSame) {
			    	JTabbedPane jtp = new JTabbedPane();
			    	//jtp.addTab("操作票1", ttk);
			    	
			    	for(Iterator<RuleBaseMode> it = CBSystemConstants.dcchzMap.keySet().iterator();it.hasNext();) {
//			    		PowerDevice pdnew = it.next();
//			    		RuleBaseMode rbnew = new RuleBaseMode();
//			    		rbnew.setPd(pdnew);
//			    		rbnew.setBeginStatus(Srcrbm.getBeginStatus());
//			    		rbnew.setEndState(Srcrbm.getEndState());
//			    		rbnew.setStateCode(Srcrbm.getStateCode());
			    		
			    		RuleBaseMode rbnew = it.next();
			    		PowerDevice pdnew = rbnew.getPd();
			    		CardModel cmnew=WordExecute.getInstance().execute(rbnew);
			    		TempTicket ttknew=TempTicket.getNewInstance();
			    		ttknew.init(cmnew,rbnew);
			    		jtp.addTab(CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pdnew.getPowerStationID())), ttknew);
			    	}
			    	splitPane.setRightComponent(jtp);
			    	CBSystemConstants.dcchzMap.clear();
		    	}
		    	else {
		    		splitPane.setRightComponent(ttk);
		    	}
	    	}else{
	    		if(SystemConstants.isInitDoubleScreen.equals("1")){
	    			GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
	    	        GraphicsDevice[] gs = ge.getScreenDevices();
	    	        int x=0;
	    	        int y=0;
	    	        int width=0;
	    	        int height=0;
	    	        if(gs.length>1){
	    	        	Rectangle fram=SystemConstants.getGuiBuilder().getJFrame().getBounds();
	    	        	int x0=(int)gs[0].getDefaultConfiguration().getBounds().getX();
	    	        	int y0=(int)gs[0].getDefaultConfiguration().getBounds().getY();
	    	        	int width0=(int)gs[0].getDefaultConfiguration().getBounds().getWidth();
	    	        	int height0=(int)gs[0].getDefaultConfiguration().getBounds().getHeight()-100;
	    	        	int x1=(int)gs[1].getDefaultConfiguration().getBounds().getX();
	    	        	int y1=(int)gs[1].getDefaultConfiguration().getBounds().getY();
	    	        	int width1=(int)gs[1].getDefaultConfiguration().getBounds().getWidth();
	    	        	int height1=(int)gs[1].getDefaultConfiguration().getBounds().getHeight()-100;
	    	        	int xg=(int)fram.getX();
	    	        	if(xg-x0>100){
	    	        		x=x0;
	    	        		y=y0;
	    	        		width=width0;
	    	        		height=height0;
	    	        	}else{
	    	        		x=x1;
	    	        		y=y1;
	    	        		width=width1;
	    	        		height=height1;
	    	        	}
	    	        	
	    				ttk.setBounds(x, y, width, height);
	    	        }else{
	    	        	int x0=(int)gs[0].getDefaultConfiguration().getBounds().getX();
	    	        	int y0=(int)gs[0].getDefaultConfiguration().getBounds().getY();
	    	        	int width0=(int)gs[0].getDefaultConfiguration().getBounds().getWidth();
	    	        	int height0=(int)gs[0].getDefaultConfiguration().getBounds().getHeight()-100;
	    	        	
	    	        	x=x0;
    	        		y=y0;
    	        		width=width0;
    	        		height=height0;
	    	        	
	    				ttk.setBounds(x, y, width, height);
	    	        }
	    	        
	    	        int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
	    	        int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
	    	        
	    	        TempTicket.doubleScreenJFame.setContentPane(ttk);
	    	        TempTicket.doubleScreenJFame.setBounds(w*3/5, h/7, width*2/5, height*5/6);
	    	        TempTicket.doubleScreenJFame.setVisible(true);
	    		}
	    	}
    	}else{
    		
    		DeviceStatusManager dsm = new DeviceStatusManager();
    		if(DBManager.isInterfaceOpened){
    			dsm.insertDevStatu();
    		}else{
    			try {
    				Connection conn = DBManager.getConnection();
    				dsm.insertDevStatu(conn);
    				conn.close();
    			} catch (SQLException e) {
    				e.printStackTrace();
    			} // 保存操作票对应设备预令状态
    		}
		
    	}
    	//五、改变设备图形操作 
        //20160328 默认实时态下可以开票，改变设备状态
//    	if(!CBSystemConstants.cardstatus.equals("1")){
//	    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
//	    	this.ExecutePWDeviceLoseElec(); //执行配网失电
//    	}
    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
    	this.ExecutePWDeviceLoseElec(); //执行配网失电
		if(!CBSystemConstants.isOutCard) {
			DeviceOperate.ClearDevMap();
			CBSystemConstants.getDtdMap().clear();
			PWSystemConstants.loseelecDeviceMap.clear();
			alltransDevMap.clear();
		}
	    return true;
	}
	public void setChangeSVG(){
    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
    	this.ExecutePWDeviceLoseElec(); //执行配网失电
		if(!CBSystemConstants.isOutCard) {
			CBSystemConstants.getDtdMap().clear();
			PWSystemConstants.loseelecDeviceMap.clear();
			alltransDevMap.clear();
		}
	}
	//多选开票
	public void executesame(List<PowerDevice> pdlist,DeviceMenuModel dmm){
		if(pdlist.size()==0){
			return;
		}
		List<RuleBaseMode> rbmlist=new ArrayList<RuleBaseMode>();
		
		String state = dmm.getStatevalue();
		if(!tbp.common.util.StringUtil.isNum(state) || 
				pdlist.get(0).getDeviceType().equals(SystemConstants.SwitchSeparate) || 
				pdlist.get(0).getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) {
			for(int i=0;i<pdlist.size();i++){
				RuleBaseMode rbm = getRBM (pdlist.get(i),dmm);
				///设备对位时操作线路设置为电源侧
				rbmlist.add(rbm);
			}
			if(sameright(pdlist)==true){//双线路开票
				if(rbmlist.size()==2&&rbmlist.get(0).getPd().getDeviceType().equals("ACLineSegment")
						&&rbmlist.get(1).getPd().getDeviceType().equals("ACLineSegment")){
					executeTwoLine(rbmlist);
				}else{
					executeSame(rbmlist);//多选开票
				}
			}
		}
		else {

			for(int i=0;i<pdlist.size();i++){
				RuleBaseMode rbm = getRBM (pdlist.get(i),dmm);
				rbmlist.add(rbm);
			}
			if(dmm.getStatename().equals("退出")||dmm.getStatename().equals("投入")||dmm.getStatename().contains("重合闸")){//多选重合闸只判断类型不判断状态
				
				for(RuleBaseMode rbm:rbmlist){
					execute(rbm);
				}
			}
			else if(sameright(pdlist)==true){
//				if(rbmlist.size()==2&&rbmlist.get(0).getPd().getDeviceType().equals("ACLineSegment")
//						&&rbmlist.get(1).getPd().getDeviceType().equals("ACLineSegment")){
//					executeTwoLine(rbmlist);
//				}else{
					executeSame(rbmlist);
//				}
			}
		}
		//清除设备选中状态
		SvgUtil.clear();
		
	}
	/**
	 * 反向成票页面
	 * @param Srcrbm
	 * @return
	 * <AUTHOR>
	 * @since 2014-10-17 9:16
	 */
	public boolean executeReverse(RuleBaseMode Srcrbm) {
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.getCurOperateDevs().clear();
		reversetransDevMap.clear();
		///设备对位时操作线路设置为电源侧
		if(!CBSystemConstants.isLock && Srcrbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)) {
			CBSystemConstants.LineSource.put(Srcrbm.getPd().getPowerDeviceID(), Srcrbm.getPd());
			CBSystemConstants.LineLoad.put(Srcrbm.getPd().getPowerDeviceID(), new ArrayList<PowerDevice>());
		}
		
		//〇、点图防误校核
		if(CBSystemConstants.cardbuildtype.equals("1")){
			
		}
		//一、执行规则
		UserRuleExecute userRuleExc=new UserRuleExecute();
		if(userRuleExc.execute(Srcrbm)){
			RuleExecute ruleExc=new RuleExecute();
			RuleExecute.setShowMessage(true);
			if(!ruleExc.execute(Srcrbm)){
				
				if(CBSystemConstants.cardbuildtype.equals("0")) {
					if(TempTicket.getTempTicket()==null) {
						putDeviceStatus(Srcrbm.getPd());
						RollbackDeviceStatus();
						ClearDevMap();
						CBSystemConstants.getDtdMap().clear();
					}
					else {
						RollbackDeviceStatusStep();
						if(TempTicket.getTempTicket()==null)
							CBSystemConstants.clearLineSourceAndLoad();
					}
				}
				else if(CBSystemConstants.cardbuildtype.equals("1")) {
					RollbackDeviceStatusStep();
					if(TempTicket.getTempTicket()==null)
						CBSystemConstants.clearLineSourceAndLoad();
				}
				
				return false;
			}
		}
		else
			return false;
		
		//二、将本次操作设备记录保存到缓存中
//		CBSystemConstants.getDtdMap();
//		putDeviceStatus(Srcrbm.getPd());
		putRerverseDeviceStatus(Srcrbm.getPd());
    	//模拟演示
		if(CBSystemConstants.isInversion){ 
	    	CardModel cm=WordExecute.getInstance().execute(Srcrbm);
	    	InversionTicket ttk=InversionTicket.getInstance();
	    	ttk.init(cm,Srcrbm);
	    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(0.52);
			splitPane.setRightComponent(ttk);
			
			//五、改变设备图形操作
	    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
			if(!CBSystemConstants.isOutCard) {
				CBSystemConstants.getDtdMap().clear();
				alltransDevMap.clear();
			}
			return true;
    	}
		if(!CBSystemConstants.cardbuildtype.equals("2"))
			CBSystemConstants.getCurRBMList().add(Srcrbm);
    	
    	if(CBSystemConstants.isOutCard){    	
    		//三、构造术语
    		CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
	    	CardModel cm = new CardModel();
	    	cm.setCardItems(new ArrayList<CardItemModel>());
	    	
	    	if(CBSystemConstants.roleCode.equals("0")) {
	    		cm=WordExecute.getInstance().execute(Srcrbm);
	    	}
	    	else {
	    		setTask(cm);
				CardModel cardModel=WordExecute.getInstance().execute(Srcrbm);
				cm.getCardItems().addAll(cardModel.getCardItems());
	    	}

	    	for(int i=0;i<cm.getCardItems().size();i++){
	    		cm.getCardItems().get(i).setBzbj(String.valueOf(bzIndex));
	    	}
	    	//四、显示操作票窗口
	    	RuleBaseMode r = CBSystemConstants.getCurRBM();
	    	//if(CBSystemConstants.reverse ==false){
	    	ReverseTempTicketDefault ttk=new ReverseTempTicketDefault(SystemConstants.getMainFrame(),false);
	    	ttk.init(cm, Srcrbm);
//	    	putDeviceStatus(Srcrbm.getPd());
//			RollbackDeviceStatus();
//			ClearDevMap();
//			CBSystemConstants.getDtdMap().clear();
//	    	}else{
//	    		ReverseTempTicketDefault2 ttk=new ReverseTempTicketDefault2();
//		    	ttk.init(cm, Srcrbm);
//	    	}

//	    	if(SystemConstants.isInitNewWin.equals("0")){
//		    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
//		    	Dimension scrsiz = Toolkit.getDefaultToolkit().getScreenSize();
////		    	System.out.println("屏幕大小"+scrsiz);
//		    	if(scrsiz.width>1280){
//		    		splitPane.setDividerLocation(0.52);
//		    	}else if(scrsiz.width<=1280&&scrsiz.width>1024){
//		    		splitPane.setDividerLocation(0.42);
//		    	}else if(scrsiz.width<=1024&&scrsiz.width>800){
//		    		splitPane.setDividerLocation(0.32);
//		    	}else{
//		    		splitPane.setDividerLocation(0.15);
//		    	}
//				splitPane.setRightComponent(ttk);
//	    	}else{
//				//修改版窗体可移动
//	    		//双屏
//	    		if(SystemConstants.isInitDoubleScreen.equals("1")){
//	    			GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
//	    	        GraphicsDevice[] gs = ge.getScreenDevices();
//	    	        int x=0;
//	    	        int y=0;
//	    	        int width=0;
//	    	        int height=0;
//	    	        if(gs.length>1){
//	    	        	Rectangle fram=SystemConstants.getGuiBuilder().getJFrame().getBounds();
//	    	        	int x0=(int)gs[0].getDefaultConfiguration().getBounds().getX();
//	    	        	int y0=(int)gs[0].getDefaultConfiguration().getBounds().getY();
//	    	        	int width0=(int)gs[0].getDefaultConfiguration().getBounds().getWidth();
//	    	        	int height0=(int)gs[0].getDefaultConfiguration().getBounds().getHeight()-100;
//	    	        	int x1=(int)gs[1].getDefaultConfiguration().getBounds().getX();
//	    	        	int y1=(int)gs[1].getDefaultConfiguration().getBounds().getY();
//	    	        	int width1=(int)gs[1].getDefaultConfiguration().getBounds().getWidth();
//	    	        	int height1=(int)gs[1].getDefaultConfiguration().getBounds().getHeight()-100;
//	    	        	int xg=(int)fram.getX();
//	    	        	if(xg-x0>100){
//	    	        		x=x0;
//	    	        		y=y0;
//	    	        		width=width0;
//	    	        		height=height0;
//	    	        	}else{
//	    	        		x=x1;
//	    	        		y=y1;
//	    	        		width=width1;
//	    	        		height=height1;
//	    	        	}
//	    	        	
//	    				ttk.setBounds(x, y, width, height);
//	    	        }
//	    		}
//	    	}
    	}else{
    		DeviceStatusManager dsm = new DeviceStatusManager();
    		if(DBManager.isInterfaceOpened){
    			dsm.insertDevStatu();
    		}else{
    			try {
    				Connection conn = DBManager.getConnection();
    				dsm.insertDevStatu(conn);
    				conn.close();
    			} catch (SQLException e) {
    				// TODO Auto-generated catch block
    				e.printStackTrace();
    			} // 保存操作票对应设备预令状态
    		}
    	}
    	//五、改变设备图形操作
    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
		if(!CBSystemConstants.isOutCard) {
			CBSystemConstants.getDtdMap().clear();
			alltransDevMap.clear();
		}
		CBSystemConstants.reverse =false;
	    return true;
	}
	public boolean executeSame(List<RuleBaseMode> SrcrbmList) {
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.getCurOperateDevs().clear();
		
		List<RuleBaseMode> executeList = new ArrayList<RuleBaseMode>();
		List<RuleBaseMode> warningList = new ArrayList<RuleBaseMode>();
		List<RuleBaseMode> errorList = new ArrayList<RuleBaseMode>();
		List<RuleBaseMode> okList = new ArrayList<RuleBaseMode>();
		
		RuleExecute ruleExc=new RuleExecute();
		RuleExecute.setShowMessage(false);
		for(int i = 0; i < SrcrbmList.size(); i++) {
			RuleBaseMode Srcrbm = SrcrbmList.get(i);
			CBSystemConstants.setCurRBM(Srcrbm);	
			boolean result = ruleExc.execute(Srcrbm);
			if(!result){
				if(TempTicket.getTempTicket()==null && !CBSystemConstants.isSame)
					CBSystemConstants.clearLineSourceAndLoad();
				if(Srcrbm.getMessageList().size() > 0) {
					errorList.add(Srcrbm);
					
				}
				else if(Srcrbm.getInfoList().size() > 0) {
					warningList.add(Srcrbm);
					Srcrbm.getInfoList().clear();
				}
			}
			else {
				okList.add(Srcrbm);
				putDeviceStatus(Srcrbm.getPd());
				executeList.add(Srcrbm);
			}
		}
		RuleExecute.setShowMessage(true);
		
		for(int i = 0; i < warningList.size(); i++) {
			RuleBaseMode Srcrbm = warningList.get(i);
			CBSystemConstants.setCurRBM(Srcrbm);
			ruleExc.execute(Srcrbm);
			putDeviceStatus(Srcrbm.getPd());
			executeList.add(Srcrbm);
		}

		if(errorList.size() >0) {
			if(okList.size() != 0 || warningList.size() != 0) {
				for(int i = 0; i < errorList.size(); i++) {
					RuleBaseMode Srcrbm = errorList.get(i);
					Srcrbm.getMessageList().clear();
					CBSystemConstants.setCurRBM(Srcrbm);
					boolean result = ruleExc.execute(Srcrbm);
					if(!result){
						RollbackDeviceStatusStep();
						if(TempTicket.getTempTicket()==null)
							CBSystemConstants.clearLineSourceAndLoad();
						
						return false;
					}
					else {
						putDeviceStatus(Srcrbm.getPd());
						executeList.add(Srcrbm);
					}
				}
			}
			else {
				for(int i = 0; i < errorList.size(); i++) {
					RuleBaseMode Srcrbm = errorList.get(i);
					String message = "";
					for(String str : Srcrbm.getMessageList()) {
						message = message + str + "\r\n";
					}
					ShowMessage.viewWarning(SystemConstants.getMainFrame(), message);
					Srcrbm.getMessageList().clear();
				}
				return false;
			}
		}
		
		if(!CBSystemConstants.cardbuildtype.equals("2"))
			CBSystemConstants.getCurRBMList().addAll(executeList);
		
		
		//三、构造术语
		CardModel cm = new CardModel();
		cm.setCardItems(new ArrayList<CardItemModel>());
		if(CBSystemConstants.cardbuildtype.equals("0")) {
			setTask(cm);
			if(executeList.size() == 1)
				cm=WordExecute.getInstance().execute(executeList.get(0));
			else if(executeList.size() > 1)
					cm=WordExecute.getInstance().execute(executeList);
				
		}
		else if(CBSystemConstants.cardbuildtype.equals("1")&&CBSystemConstants.isSame) {
			setTask(cm);
			if(executeList.size() == 1)
				cm=WordExecute.getInstance().execute(executeList.get(0));
			else if(executeList.size() > 1)
					cm=WordExecute.getInstance().execute(executeList);
				
		}
		else if(CBSystemConstants.roleCode.equals("0")) {
	    	
			if(executeList.size() == 1)
				cm=WordExecute.getInstance().execute(executeList.get(0));
			else if(executeList.size() > 1)
				cm=WordExecute.getInstance().execute(executeList);
	    	
		}
		else {
			setTask(cm);
			for(RuleBaseMode rbm : executeList) {
				CardModel cardModel = WordExecute.getInstance().execute(rbm);
				cm.getCardItems().addAll(cardModel.getCardItems());
			}
		}
		
		for(int i=0;i<cm.getCardItems().size();i++){
    		cm.getCardItems().get(i).setBzbj(String.valueOf(bzIndex));
    	}
    	
    	//四、生成临时操作票窗口
    	if(executeList.size() > 0) {
	    	//四、生成临时操作票窗口
	    	TempTicket ttk=TempTicket.getInstance();
	    	ttk.init(cm,executeList.get(0));
	    	if(SystemConstants.isInitNewWin.equals("0")){
		    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		    	Dimension scrsiz = Toolkit.getDefaultToolkit().getScreenSize();
		    	if(scrsiz.width>1280){
		    		splitPane.setDividerLocation(0.52);
		    	}else if(scrsiz.width<=1280&&scrsiz.width>1024){
		    		splitPane.setDividerLocation(0.42);
		    	}else if(scrsiz.width<=1024&&scrsiz.width>800){
		    		splitPane.setDividerLocation(0.32);
		    	}else{
		    		splitPane.setDividerLocation(0.15);
		    	}
				splitPane.setRightComponent(ttk);
	    	}else{
				//修改版窗体可移动
	    		
	    		//双屏
	    		if(SystemConstants.isInitDoubleScreen.equals("1")){
	    			GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
	    	        GraphicsDevice[] gs = ge.getScreenDevices();
	    	        int x=0;
	    	        int y=0;
	    	        int width=0;
	    	        int height=0;
	    	        if(gs.length>1){
	    	        	Rectangle fram=SystemConstants.getGuiBuilder().getJFrame().getBounds();
	    	        	int x0=(int)gs[0].getDefaultConfiguration().getBounds().getX();
	    	        	int y0=(int)gs[0].getDefaultConfiguration().getBounds().getY();
	    	        	int width0=(int)gs[0].getDefaultConfiguration().getBounds().getWidth();
	    	        	int height0=(int)gs[0].getDefaultConfiguration().getBounds().getHeight()-100;
	    	        	int x1=(int)gs[1].getDefaultConfiguration().getBounds().getX();
	    	        	int y1=(int)gs[1].getDefaultConfiguration().getBounds().getY();
	    	        	int width1=(int)gs[1].getDefaultConfiguration().getBounds().getWidth();
	    	        	int height1=(int)gs[1].getDefaultConfiguration().getBounds().getHeight()-100;
	    	        	int xg=(int)fram.getX();
	    	        	if(xg-x0>100){
	    	        		x=x0;
	    	        		y=y0;
	    	        		width=width0;
	    	        		height=height0;
	    	        	}else{
	    	        		x=x1;
	    	        		y=y1;
	    	        		width=width1;
	    	        		height=height1;
	    	        	}
	    	        	
	    				ttk.setBounds(x, y, width, height);
	    	        }else{
	    	        }
	    	        
	    		}
	    	}
    	}
    

    	//五、改变设备图形操作
    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
		if(!CBSystemConstants.isOutCard) {
			CBSystemConstants.getDtdMap().clear();
			alltransDevMap.clear();
		}
			
	    return true;
	}
	/**
	 * <AUTHOR>
	 * @since 2014-09-17 9:24
	 * @param SrcrbmList
	 * @return
	 */
	public boolean executeTwoLine(List<RuleBaseMode> SrcrbmList) {
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.getCurOperateDevs().clear();
		
		RuleBaseMode Srcrbm = SrcrbmList.get(0);
		//一、执行规则
		UserRuleExecute userRuleExc=new UserRuleExecute();
		if(userRuleExc.execute(Srcrbm)){
			RuleExecute ruleExc=new RuleExecute();
			CBSystemConstants.setCurRBM(Srcrbm);
			RuleExecute.setShowMessage(true);
			if(!ruleExc.execute(Srcrbm)){
				
				if(CBSystemConstants.cardbuildtype.equals("0")) {
					if(TempTicket.getTempTicket()==null) {
						putDeviceStatus(Srcrbm.getPd());
						RollbackDeviceStatus();
						ClearDevMap();
						CBSystemConstants.getDtdMap().clear();
					}
					else {
						RollbackDeviceStatusStep();
						if(TempTicket.getTempTicket()==null)
							CBSystemConstants.clearLineSourceAndLoad();
					}
				}
				else if(CBSystemConstants.cardbuildtype.equals("1")) {
					RollbackDeviceStatusStep();
					if(TempTicket.getTempTicket()==null)
						CBSystemConstants.clearLineSourceAndLoad();
				}
				
				return false;
			}
		}
		else
			return false;
		
		//二、将本次操作设备记录保存到缓存中
		putDeviceStatus(Srcrbm.getPd());
    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
		//三、构造术语
		CardModel cm = new CardModel();
		cm.setCardItems(new ArrayList<CardItemModel>());
		setTask(cm);
		cm=WordExecute.getInstance().execute(Srcrbm);

		for(int i=0;i<cm.getCardItems().size();i++){
    		cm.getCardItems().get(i).setBzbj(String.valueOf(bzIndex));
    	}
		
		//改变设备图形操作
    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
		CBSystemConstants.getDtdMap().clear();
		
		
		
		//执行第二条线路
		RuleBaseMode Srcrbm2 = SrcrbmList.get(1);
		//一、执行规则
		UserRuleExecute userRuleExc2=new UserRuleExecute();
		if(userRuleExc2.execute(Srcrbm2)){
			RuleExecute ruleExc=new RuleExecute();
			CBSystemConstants.setCurRBM(Srcrbm2);

			RuleExecute.setShowMessage(true);
			if(!ruleExc.execute(Srcrbm2)){
				
				if(CBSystemConstants.cardbuildtype.equals("0")) {
					if(TempTicket.getTempTicket()==null) {
						putDeviceStatus(Srcrbm2.getPd());
						RollbackDeviceStatus();
						ClearDevMap();
						CBSystemConstants.getDtdMap().clear();
					}
					else {
						RollbackDeviceStatusStep();
						if(TempTicket.getTempTicket()==null)
							CBSystemConstants.clearLineSourceAndLoad();
					}
				}
				else if(CBSystemConstants.cardbuildtype.equals("1")) {
					RollbackDeviceStatusStep();
					if(TempTicket.getTempTicket()==null)
						CBSystemConstants.clearLineSourceAndLoad();
				}
				
				return false;
			}
		}
		else
			return false;
		
		//二、将本次操作设备记录保存到缓存中
		putDeviceStatus(Srcrbm2.getPd());
		//三、构造术语
		CardModel cm2 = new CardModel();
		cm2.setCardItems(new ArrayList<CardItemModel>());
		setTask(cm2);
		cm=WordExecute.getInstance().executeTwo(Srcrbm,Srcrbm2,cm,cm2);

		for(int i=0;i<cm.getCardItems().size();i++){
    		cm.getCardItems().get(i).setBzbj(String.valueOf(bzIndex));
    	}
    	
    	//四、生成临时操作票窗口
    	//if(executeList.size() > 0) {
	    	//四、生成临时操作票窗口
	    	TempTicket ttk=TempTicket.getInstance();
	    	ttk.init(cm,Srcrbm);
	    	if(SystemConstants.isInitNewWin.equals("0")){
		    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		    	Dimension scrsiz = Toolkit.getDefaultToolkit().getScreenSize();
		    	double bl = 0;
		    	if(scrsiz.width>1280){
		    		bl = 0.52;
		    	}else if(scrsiz.width<=1280&&scrsiz.width>1024){
		    		bl = 0.42;
		    	}else if(scrsiz.width<=1024&&scrsiz.width>800){
		    		bl = 0.32;
		    	}else{
		    		bl =0.15;
		    	}
		    	double minbl = 1-Double.valueOf(ttk.getPanelLength())/scrsiz.width;
		    	if(minbl < bl)
		    		bl = minbl;
		    	splitPane.setDividerLocation(bl);
				splitPane.setRightComponent(ttk);
	    	}else{
				//修改版窗体可移动
	    		
	    		//双屏
	    		if(SystemConstants.isInitDoubleScreen.equals("1")){
	    			GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
	    	        GraphicsDevice[] gs = ge.getScreenDevices();
	    	        int x=0;
	    	        int y=0;
	    	        int width=0;
	    	        int height=0;
	    	        if(gs.length>1){
	    	        	Rectangle fram=SystemConstants.getGuiBuilder().getJFrame().getBounds();
	    	        	int x0=(int)gs[0].getDefaultConfiguration().getBounds().getX();
	    	        	int y0=(int)gs[0].getDefaultConfiguration().getBounds().getY();
	    	        	int width0=(int)gs[0].getDefaultConfiguration().getBounds().getWidth();
	    	        	int height0=(int)gs[0].getDefaultConfiguration().getBounds().getHeight()-100;
	    	        	int x1=(int)gs[1].getDefaultConfiguration().getBounds().getX();
	    	        	int y1=(int)gs[1].getDefaultConfiguration().getBounds().getY();
	    	        	int width1=(int)gs[1].getDefaultConfiguration().getBounds().getWidth();
	    	        	int height1=(int)gs[1].getDefaultConfiguration().getBounds().getHeight()-100;
	    	        	int xg=(int)fram.getX();
	    	        	if(xg-x0>100){
	    	        		x=x0;
	    	        		y=y0;
	    	        		width=width0;
	    	        		height=height0;
	    	        	}else{
	    	        		x=x1;
	    	        		y=y1;
	    	        		width=width1;
	    	        		height=height1;
	    	        	}
	    	        	
	    				ttk.setBounds(x, y, width, height);
	    	        }else{
	    	        }
	    	        
	    		}
	    	}
    	//}
    

    	//五、改变设备图形操作
    	this.ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
		if(!CBSystemConstants.isOutCard) {
			CBSystemConstants.getDtdMap().clear();
			alltransDevMap.clear();
		}
			
	    return true;
	}
	
	public boolean sameright(List<PowerDevice> pdlist){
		
		List<String> devicetypelist=new ArrayList<String>();
		List<String> devicestatuslist=new ArrayList<String>();
		List<String> devicekindlist=new ArrayList<String>();
		List<String> deviceRunTypelist=new ArrayList<String>();
		for(int i=0;i<pdlist.size();i++){
			String type=pdlist.get(i).getDeviceType();
			String status=pdlist.get(i).getDeviceStatus();
			String kind=pdlist.get(i).getDeviceKind();
			String runtype=pdlist.get(i).getDeviceRunType();
			devicetypelist.add(type);
			devicestatuslist.add(status);
			devicekindlist.add(kind);
			deviceRunTypelist.add(runtype);
		}
		int m=0;
		for(int i=0;i<devicetypelist.size();i++){
			if(devicetypelist.get(0).equals(devicetypelist.get(i))){
				m=m+1;
			}
		}
		int n=0;
		for(int i=0;i<devicestatuslist.size();i++){
			if(devicestatuslist.get(0).equals(devicestatuslist.get(i))){
				n=n+1;
			}
		}
		int k=0;
		for(int i=0;i<devicekindlist.size();i++){
			if(devicekindlist.get(0).equals(devicekindlist.get(i))){
				k=k+1;
			}
		}
		int l=0;
		for(int i=0;i<deviceRunTypelist.size();i++){
			if(deviceRunTypelist.get(0).equals(deviceRunTypelist.get(i))){
				l=l+1;
			}
		}
		if(m==devicetypelist.size()&&n==devicestatuslist.size()&&k==devicekindlist.size()){
			return true;
		}else{
			ShowMessage.view("您选择的不是同类设备或设备状态不同");
			SvgUtil.clear();
			return false;
		}
	}
	
	public boolean isRBMSame(RuleBaseMode rbm1, RuleBaseMode rbm2){
		if(!rbm1.getPd().getPowerStationID().equals(rbm2.getPd().getPowerStationID()))
			return false;
		if(!rbm1.getPd().getDeviceType().equals(rbm2.getPd().getDeviceType()))
			return false;
		if(!rbm1.getPd().getDeviceKind().equals(rbm2.getPd().getDeviceKind()))
			return false;
		if(!rbm1.getBeginStatus().equals(rbm2.getBeginStatus()))
			return false;
		if(!rbm1.getEndState().equals(rbm2.getEndState()))
			return false;
		if(!rbm1.getStateCode().equals(rbm2.getStateCode()))
			return false;
		return true;
	}
	//保护设备返回RBM
	private RuleBaseMode getbh(PowerDevice pd,String bhcode,String czcode){
		RuleBaseMode rbm=new RuleBaseMode();
	    HashMap<String, PowerDevice> all=CBSystemConstants.getProtect(pd.getPowerStationID());
	    PowerDevice bhpd=all.get(bhcode);
	    String statrstatus=bhpd.getDeviceStatus();
	    List r2=DBManager.query("select statename,statevalue from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and statecode='"+czcode+"'");
		Map r2m=(Map) r2.get(0);
		String endstate=StringUtils.ObjToString(r2m.get("statevalue"));
	    rbm.setPd(bhpd);
	    rbm.setBeginStatus(statrstatus);
	    rbm.setEndState(endstate);
	    rbm.setStateCode(czcode);
		return rbm;
	}
	
	//根据statecode获取RBM
	private RuleBaseMode getRBMbyCode(PowerDevice pd,String czcode){
		RuleBaseMode rbm=new RuleBaseMode();
	    List r2=DBManager.query("select statename,statevalue from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where statecode='"+czcode+"'");
		Map r2m=(Map) r2.get(0);
		String endstate=StringUtils.ObjToString(r2m.get("statevalue"));
	    rbm.setPd(pd);
	    rbm.setBeginStatus(pd.getDeviceStatus());
	    rbm.setEndState(endstate);
	    rbm.setStateCode(czcode);
		return rbm;
	}
	
	public RuleBaseMode getRBM(PowerDevice pd, String state) {
	    RuleBaseMode rbm=new RuleBaseMode();
	    rbm.setPd(pd);
	    String beginStatus=pd.getDeviceStatus();
	    rbm.setBeginStatus(beginStatus);
	    rbm.setEndState(state);
	    if(state.equals("0")||state.equals("1")||state.equals("2")||state.equals("3")) {
		    DeviceStateMentManager dsmm = new DeviceStateMentManager();
		    String stateCode = dsmm.getStateCodeByStatus(pd.getDeviceType(), state);
		    rbm.setStateCode(stateCode);
	    }
	    else
	    	rbm.setStateCode(state);
	    return rbm;
	}		
	
	public RuleBaseMode getRBM(PowerDevice pd, DeviceMenuModel dmm) {
	    RuleBaseMode rbm=new RuleBaseMode();
	    rbm.setPd(pd);
	    String beginStatus=pd.getDeviceStatus();
	    rbm.setBeginStatus(beginStatus);
	    rbm.setEndState(dmm.getStatevalue());
	    rbm.setStateCode(dmm.getStatecode());
	    return rbm;
	}		
	
	/**
	 * 构造监控任务
	 * @param cm
	 */
	public void setTask(CardModel cm) {
		List<List<RuleBaseMode>> rbmListList = new ArrayList<List<RuleBaseMode>>();
		List<RuleBaseMode> rbmList = CBSystemConstants.getCurRBMList();
		for(RuleBaseMode rbm : rbmList) {
			if(rbm.getPd() == null)
				continue;
			boolean isExist = false;
			for(List<RuleBaseMode> list : rbmListList) {
				if(list.size() > 0 && isRBMSame(list.get(0), rbm)) {
					list.add(rbm);
					isExist = true;
					break;
				}
			}
			if(!isExist) {
				List<RuleBaseMode> newList = new ArrayList<RuleBaseMode>();
				newList.add(rbm);
				rbmListList.add(newList);
			}
		}
		String czrw = "";
		String prevSt="";
		List<String> stations=new ArrayList<String>();
		int i=0;
		for(List<RuleBaseMode> list : rbmListList) {
			CardModel cml = WordExecute.getInstance().execute(list);
			String task = "";
			if(cml != null)
				task = cml.getCzrw();
			if(i!=0){
				prevSt=stations.get(i-1);
			}
			if(!prevSt.equals("")&&task.contains(prevSt)){
				task=task.replace(prevSt, "").replace("站", "");
				if(czrw.length()>0){
					czrw = czrw.substring(0, czrw.length()-1)+"，";
				}
			}
			if(!task.equals("")){
				czrw = czrw + task + "；";
			}
			
			
			stations.add(list.get(0).getPd().getPowerStationName());
			i++;
		}
		if(czrw.endsWith("；"))
			czrw = czrw.substring(0, czrw.length()-1);
		
		cm.setCzrw(czrw);
		
	}

	public boolean executeCondition(RuleBaseMode Srcrbm) {
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.getCurOperateDevs().clear();
		
		///设备对位时操作线路设置为电源侧
		if(!CBSystemConstants.isLock && Srcrbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)) {
			CBSystemConstants.LineSource.put(Srcrbm.getPd().getPowerDeviceID(), Srcrbm.getPd());
			CBSystemConstants.LineLoad.put(Srcrbm.getPd().getPowerDeviceID(), new ArrayList<PowerDevice>());
		}
		
		//一、执行规则
		UserRuleExecute userRuleExc=new UserRuleExecute();
		if(userRuleExc.execute(Srcrbm)){
			RuleExecute ruleExc=new RuleExecute();
			RuleExecute.setShowMessage(false);
			if(!ruleExc.execute(Srcrbm)){
				
				if(CBSystemConstants.cardbuildtype.equals("0")) {
					putDeviceStatus(Srcrbm.getPd());
					if(CBSystemConstants.isLock==true){
					}
					RollbackDeviceStatus();
				}
				else if(CBSystemConstants.cardbuildtype.equals("1")){
					if(CBSystemConstants.isLock==true){
					}
					RollbackDeviceStatusStep();
				}
				CBSystemConstants.getDtdMap().clear();
				return false;
			}
		}
		else
			return false;
		
		//二、将本次操作设备记录保存到缓存中
		putDeviceStatus(Srcrbm.getPd());

		if(!CBSystemConstants.isOutCard) {
			CBSystemConstants.getDtdMap().clear();
			alltransDevMap.clear();
		}
	    return true;
	}
	
	public boolean executeCheck(RuleBaseMode Srcrbm) {
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.getCurOperateDevs().clear();
		
		///设备对位时操作线路设置为电源侧
		if(!CBSystemConstants.isLock && Srcrbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)) {
			CBSystemConstants.LineSource.put(Srcrbm.getPd().getPowerDeviceID(), Srcrbm.getPd());
			CBSystemConstants.LineLoad.put(Srcrbm.getPd().getPowerDeviceID(), new ArrayList<PowerDevice>());
		}
		
		//一、执行规则
		UserRuleExecute userRuleExc=new UserRuleExecute();
		if(userRuleExc.execute(Srcrbm)){
			RuleExecute ruleExc=new RuleExecute();
			if(!ruleExc.execute(Srcrbm)){
				
				if(CBSystemConstants.cardbuildtype.equals("0")) {
					putDeviceStatus(Srcrbm.getPd());
					if(CBSystemConstants.isLock==true){
					}
					RollbackDeviceStatus();
				}
				else if(CBSystemConstants.cardbuildtype.equals("1")){
					if(CBSystemConstants.isLock==true){
					}
					RollbackDeviceStatusStep();
				}
				CBSystemConstants.getDtdMap().clear();
				return false;
			}
		}
		else
			return false;
		
		//二、将本次操作设备记录保存到缓存中
		putDeviceStatus(Srcrbm.getPd());

		if(!CBSystemConstants.isOutCard) {
			CBSystemConstants.getDtdMap().clear();
			alltransDevMap.clear();
		}
	    return true;
	}
	
    private void ExecuteDeviceSVGAction(Map<Integer, DispatchTransDevice> curtransDevMap){
		
		DispatchTransDevice dtd=null;
		PowerDevice dev=null;
		for (int j = 1; j < curtransDevMap.size() + 1; j++) {    //遍历元件状态 排除开关
        	dtd = curtransDevMap.get(j);
        	dev = dtd.getTransDevice();
        	//20131119
        	//if(SystemConstants.Switch.equals(dev.getDeviceType())&&dtd.getFlag().equals("1"))
        	//	continue;
        	if((dtd.getBeginstatus().equals("3")&&dtd.getEndstate().equals("2"))
        			||(dtd.getBeginstatus().equals("2")&&dtd.getEndstate().equals("3"))){
        		CBSystemConstants.jxDtd=true;
        	}else {
        		CBSystemConstants.jxDtd=false;
			}
        	DeviceSVGPanelUtil.changeDeviceSVGColor(dev);
        	CBSystemConstants.jxDtd=false;
        }
	}
    private void ExecutePWDeviceLoseElec(){
		
    	
    	
    	DispatchTransDevice dtd=null;
    	
//    	for (int j = 1; j < CBSystemConstants.getDtdMap().size() + 1; j++) { 
//    		dtd = CBSystemConstants.getDtdMap().get(j);
//    		String stationID = "";
//    		if(!dtd.getTransDevice().isPW()) {
//				List<PowerDevice> linkdevList = RuleExeUtil.getDeviceDirectList(dtd.getTransDevice(), "");
//				for(PowerDevice linkdev  : linkdevList) {
//					if(linkdev.isPW()) {
//						stationID = linkdev.getPowerStationID();
//						break;
//					}
//				}
//				
//			}
//    		else
//    			stationID = dtd.getTransDevice().getPowerStationID();
//    		
//    		
//    	}
    	if(SystemConstants.getGuiBuilder()!=null && 
    			SystemConstants.getGuiBuilder().getActivateSVGPanel()!=null&&CBSystemConstants.isSetToplogySVGColor) {
	    	String stationID = SystemConstants.getGuiBuilder().getActivateSVGPanel().getStationID();
			String[] staarr = stationID.split(",");
			for(String sta : staarr) {
				 DeviceSVGPanelUtil.setToplogySVGColor(sta);	
			}
    	}
    	
		
//		PowerDevice tempPd=null;
//		SvgAction action = null;
//		for (int j = 1; j < PWSystemConstants.loseelecDeviceMap.size() + 1; j++) { 
//        	dtd = PWSystemConstants.loseelecDeviceMap.get(j);
//        	tempPd = dtd.getTransDevice();
//			tempPd.setIsLoseElec(dtd.getEndstate());
//			String powerVolt = String.valueOf((int) tempPd.getPowerVoltGrade());
//			
//			if("0".equals(dtd.getEndstate())){   			
//    			if(SystemConstants.Switch.equals(tempPd.getDeviceType())){
//    				action = new ChangeColorAction(tempPd, SystemConstants
//    						.getMapColor().get(powerVolt), SystemConstants
//    						.getMapColor().get(powerVolt));
//    			}else{
//    				action = new ChangeColorAction(tempPd, SystemConstants
//    						.getMapColor().get(powerVolt), null);
//    			}
//			}else{
//				if(SystemConstants.Switch.equals(tempPd.getDeviceType())){
//				    action = new ChangeColorAction(tempPd,
//						SystemConstants.getMapColor().get(
//								SystemConstants.LOSE_COLOR_CODE), SystemConstants.getMapColor().get(
//										("01".indexOf(tempPd.getDeviceStatus())>=0)?SystemConstants.LOSE_COLOR_CODE:null));
//    			}else{
//    				action = new ChangeColorAction(tempPd,
//    						SystemConstants.getMapColor().get(
//    								SystemConstants.LOSE_COLOR_CODE), null);
//    			}
//			}
//			action.execute();
//        }
	}
    
    public static void putDeviceStatus(PowerDevice pd){
    	//chushialltransDevMap.clear();
    	DispatchTransDevice dtd=null;
    	int sysNum=alltransDevMap.size();
    	for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {    //遍历设备状态
    		dtd = CBSystemConstants.getDtdMap().get(i);
    		dtd.setRemark(String.valueOf(bzIndex));
//    		System.out.println(CBSystemConstants.getDeviceStateName(dtd.getTransDevice().getDeviceType(), dtd.getEndstate())+":"+dtd.getTransDevice().getPowerDeviceName());
    		alltransDevMap.put(++sysNum, dtd);
    		if(!CBSystemConstants.cardbuildtype.equals("1") && CBSystemConstants.getSamepdlist().size()==0){
        		chushialltransDevMap.put(sysNum, dtd);
    		}
    	}
    }
    /**
     * 反向成票时，
     * @param pd
     */
    public static void putRerverseDeviceStatus(PowerDevice pd){
    	reversetransDevMap.clear();
    	if(!CBSystemConstants.cardbuildtype.equals("1") && CBSystemConstants.getSamepdlist().size()==0){
        	reversetransDevMap.clear();
        	DispatchTransDevice dtd=null;
        	DispatchTransDevice dtdkey = new DispatchTransDevice();
        	dtdkey.setComment("是反向成票");
//        	Map<Integer, DispatchTransDevice> DevMap  = new HashMap<Integer, DispatchTransDevice>(); 
        	int sysNum=chushialltransDevMap.size();
        	for(int i = 1; i < chushialltransDevMap.size() + 1; i++){
        		dtd = chushialltransDevMap.get(i);
        		reversetransDevMap.put(i, dtd);
        	}
        	int keyNum = sysNum+1;
        	reversetransDevMap.put(keyNum, dtdkey);
        	for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {    //遍历设备状态
        		dtd = CBSystemConstants.getDtdMap().get(i);
        		dtd.setRemark(String.valueOf(keyNum+1));
        		System.out.println(CBSystemConstants.getDeviceStateName(dtd.getTransDevice().getDeviceType(), dtd.getEndstate())+":"+dtd.getTransDevice().getPowerDeviceName());
        		reversetransDevMap.put(++keyNum, dtd);
        	}
    	}


    }
   //回滚上一步
    public static void RollbackLastStatusStep(String bzbj){
    	Map<Integer, DispatchTransDevice> returnback =  new HashMap<Integer, DispatchTransDevice>();
    	Set<Integer> keys = alltransDevMap.keySet();
    	DispatchTransDevice dtdn=null;
    	for (Integer key : keys) {
			dtdn=alltransDevMap.get(key);
			if(dtdn.getRemark().equals(bzbj)){
				returnback.put(key, dtdn);
			}
		}
//    	List<Map<Integer, DispatchTransDevice>> onetoall=getOnetoAlltransDevMap();
//    	Map<Integer, DispatchTransDevice> lastone=onetoall.get(onetoall.size()-1);
    	DispatchTransDevice dtd = new DispatchTransDevice();
        PowerDevice pdDev = null;
        String srcStatus = "";
        //取出源操作对像

        Set<Integer> rbkeys = returnback.keySet();
        
        //回滚设备状态信息
        for(int j=alltransDevMap.size();j>alltransDevMap.size()-returnback.size();j--)
        {
        	    dtd = returnback.get(j);
        	    pdDev = (PowerDevice) dtd.getTransDevice();  
                if (CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals("-1")) //为-1说明不需要对状态进行变更，否则必须将设备原状态设置回去，不论执行成功与否
                {
                    continue;
                } 
                if(pdDev.getDeviceType().equals(SystemConstants.RemovableDevice)){
                	pdDev.setDeviceStatus(dtd.getBeginstatus());
                }
                if(dtd.getFlag().equals("0")){
	                srcStatus = dtd.getBeginstatus();
	                pdDev.setDeviceStatus(srcStatus);	
	                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
                }else{
                    srcStatus = CBSystemConstants.getDeviceStateValue(dtd.getBeginstatus()) ;
                    pdDev.setDeviceStatus(srcStatus);
                    DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
                }
        } 
        CBSystemConstants.clearLineSourceAndLoad();
        if(alltransDevMap.size()-1>0){
        	for(Integer key:rbkeys){
        		if(returnback.get(key).equals(alltransDevMap.get(key))){
        			alltransDevMap.remove(key);
        		}
        	}
        }else{
        	return;
        }
       
    }
    
    //回滚点图开票单步状态
    public static void RollbackDeviceStatusStep(){
		if(CBSystemConstants.notRollBack){
			return;
		}
    	Map<Integer, DispatchTransDevice> steptransDevMap  = new HashMap<Integer, DispatchTransDevice>(); 
    	
    	DispatchTransDevice dtd = new DispatchTransDevice();
    	int sysNum=steptransDevMap.size();
    	for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {    //遍历设备状态
    		dtd = CBSystemConstants.getDtdMap().get(i);
    		steptransDevMap.put(++sysNum, dtd);
    	}
    	
    	
        PowerDevice pdDev = null;
        String srcStatus = "";
        //取出源操作对像

        //回滚设备状态信息
        for(int j=steptransDevMap.size();j>0;j--)
        {
        	    dtd = steptransDevMap.get(j);
        	    pdDev = (PowerDevice) dtd.getTransDevice();  
                if (CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals("-1")) //为-1说明不需要对状态进行变更，否则必须将设备原状态设置回去，不论执行成功与否
                {
                    continue;
                } 
                if(pdDev.getDeviceType().equals(SystemConstants.RemovableDevice)){
                	pdDev.setDeviceStatus(dtd.getBeginstatus());
                }
                if(dtd.getFlag().equals("0")){
	                srcStatus = dtd.getBeginstatus();
	                pdDev.setDeviceStatus(srcStatus);	
	                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
                }else{
                    srcStatus = CBSystemConstants.getDeviceStateValue(dtd.getBeginstatus()) ;
                    pdDev.setDeviceStatus(srcStatus);
                    DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
                }

        } 
        //CBSystemConstants.clearLineSourceAndLoad();
       // rollbackGroundLine();
    }
    //回滚状态
    public static void RollbackDeviceStatus(){
		if(CBSystemConstants.notRollBack){
			return;
		}
    	DispatchTransDevice dtd = new DispatchTransDevice();
        PowerDevice pdDev = null;
        String srcStatus = "";
        //取出源操作对像
        
        if(CBSystemConstants.roleCode.equals("2")){
        	for(int j=alltransDevMap.size();j>0;j--)
	        {
    			dtd = alltransDevMap.get(j);
    			pdDev = (PowerDevice) dtd.getTransDevice();
    			
    			if (CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals("-1")) //为-1说明不需要对状态进行变更，否则必须将设备原状态设置回去，不论执行成功与否
                {
                    continue;
                } 
                if(pdDev.getDeviceType().equals(SystemConstants.RemovableDevice)){
                	pdDev.setDeviceStatus(dtd.getBeginstatus());
                }
                if(dtd.getFlag().equals("0")){
	                srcStatus = dtd.getBeginstatus();
	                pdDev.setDeviceStatus(srcStatus);	
	                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
                }else{
                    srcStatus = CBSystemConstants.getDeviceStateValue(dtd.getBeginstatus()) ;
                    pdDev.setDeviceStatus(srcStatus);
                    DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
                }
    			String deviceId = "";
    			
	        }
	        CBSystemConstants.clearLineSourceAndLoad();
	       
        }else{
        	
        	//回滚设备状态信息
        	if(reversetransDevMap.size()>0){
        		if(CBSystemConstants.reverseCancel){//反向成票取消
        			for(int j=reversetransDevMap.size();j>(reversetransDevMap.size()-chushialltransDevMap.size());j--)
        	        {
        	        	    dtd = reversetransDevMap.get(j);
        	        	    pdDev = (PowerDevice) dtd.getTransDevice();
        	        	    if(pdDev==null){
        	        	    	 continue;
        	        	    }
        	                if (CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals("-1")) //为-1说明不需要对状态进行变更，否则必须将设备原状态设置回去，不论执行成功与否
        	                {
        	                    continue;
        	                } 
        	                if(pdDev.getDeviceType().equals(SystemConstants.RemovableDevice)){
        	                	pdDev.setDeviceStatus(dtd.getBeginstatus());
        	                	
        	                }
        	                if(dtd.getFlag().equals("0")){
        	                	srcStatus = dtd.getBeginstatus();
        		                pdDev.setDeviceStatus(srcStatus);	
        		                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
        	                }else{
        	                	srcStatus = CBSystemConstants.getDeviceStateValue(dtd.getBeginstatus()) ;
        		             	pdDev.setDeviceStatus(srcStatus);
        		                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
        	                }
        	        }
        			//reversetransDevMap.clear();
        		}else{//反向成票取消后，开票页面取消
        			for(int j=chushialltransDevMap.size();j>0;j--)
        	        {
        	        	    dtd = chushialltransDevMap.get(j);
        	        	    pdDev = (PowerDevice) dtd.getTransDevice();  
        	                if (CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals("-1")) //为-1说明不需要对状态进行变更，否则必须将设备原状态设置回去，不论执行成功与否
        	                {
        	                    continue;
        	                } 
        	                if(pdDev.getDeviceType().equals(SystemConstants.RemovableDevice)){
        	                	pdDev.setDeviceStatus(dtd.getBeginstatus());
        	                	
        	                }
        	                if(dtd.getFlag().equals("0")){
        	                	srcStatus = dtd.getBeginstatus();
        		                pdDev.setDeviceStatus(srcStatus);	
        		                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
        	                }else{
        	                	srcStatus = CBSystemConstants.getDeviceStateValue(dtd.getBeginstatus()) ;
        		             	pdDev.setDeviceStatus(srcStatus);
        		                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
        	                }
        	        }
        			chushialltransDevMap.clear();
        			reversetransDevMap.clear();
        		}
        	}else{
                
    	        //回滚设备状态信息
    	        for(int j=alltransDevMap.size();j>0;j--)
    	        {
    	        	    dtd = alltransDevMap.get(j);
    	        	    pdDev = (PowerDevice) dtd.getTransDevice();  
    	        	    if(CBSystemConstants.powerDeviceDevMap.size()>0&&CBSystemConstants.stateOfTheDrawer){
    	        	    	if(CBSystemConstants.powerDeviceDevMap.containsKey(pdDev)){
    	        	    		//if(pdDev.getDeviceType().equals(SystemConstants.InOutLine)||pdDev.getDeviceType().equals(SystemConstants.Switch)){
    	        	    			dtd.setBeginstatus(CBSystemConstants.pdStart);
    	        	    		//}else{
    	        	    		//	dtd.setBeginstatus(CBSystemConstants.powerDeviceDevMap.get(pdDev).getDeviceStatus());
    	        	    		//}
    	        	    	}else{
    	        	    		//if(pdDev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
    	        	    			dtd.setBeginstatus(CBSystemConstants.pdStart);
    	        	    		//}
    	        	    	}
    	        	    	
    	        	    }
    	    
    	                if (CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals("-1")) //为-1说明不需要对状态进行变更，否则必须将设备原状态设置回去，不论执行成功与否
    	                {
    	                    continue;
    	                } 
    	                if(pdDev.getDeviceType().equals(SystemConstants.RemovableDevice)){
    	                	pdDev.setDeviceStatus(dtd.getBeginstatus());
    	                }
    	                if(dtd.getFlag().equals("0")){
    		                srcStatus = dtd.getBeginstatus();
    		                if(CBSystemConstants.roleCode.equals("3")){
//    		                	if(pdDev.getDeviceType().equals(SystemConstants.BztDevice)){
//    		                		ArrayList<Jktriprelate> listRelate = CBSystemConstants.mapTriprelate.get(pdDev.getPowerStationID());
//    		                		ArrayList<Jktriprelate> list = new ArrayList<Jktriprelate>();
//    		                		for(Jktriprelate bzt : listRelate) {
//    		                			if(bzt.getSCS_ID().equals(pdDev.getPowerDeviceID())){
//    		                				RuleUtil.deviceStatusSet(bzt, bzt.getDeviceStatus(), srcStatus);
//    		                			}
//    		                		}
//    		                	}else 
    		                	if(pdDev.getDeviceType().equals(SystemConstants.BztRelate)){
    		                		RuleUtil.deviceStatusSet(pdDev, pdDev.getDeviceStatus(), srcStatus);
    		                	}
    		                }
    		                pdDev.setDeviceStatus(srcStatus);
    		                //20160328
//    		                if(!CBSystemConstants.cardstatus.equals("1")){
//    		                    DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
//    		                }
    		                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
    	                }else{
    	                    srcStatus = CBSystemConstants.getDeviceStateValue(dtd.getBeginstatus()) ;
    	                    pdDev.setDeviceStatus(srcStatus);
    	                    //20160328
//    		                if(!CBSystemConstants.cardstatus.equals("1")){
//    		                    DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
//    		                }
    		                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
    	                }
    	        }
    	        CBSystemConstants.powerDeviceDevMap = new HashMap<PowerDevice, PowerDevice>();
        	}
        	
        	//配网失电回滚
        	DeviceOperate dop = new DeviceOperate();
        	dop.ExecutePWDeviceLoseElec(); //执行配网失电
//    		PowerDevice tempPd=null;
//    		SvgAction action = null;
//    		for (int j = PWSystemConstants.loseelecDeviceMap.size(); j > 0; j--) {  
//            	dtd = PWSystemConstants.loseelecDeviceMap.get(j);
//            	tempPd = dtd.getTransDevice();
//            	tempPd.setIsLoseElec(dtd.getBeginstatus());
//    			String powerVolt = String.valueOf((int) tempPd.getPowerVoltGrade());
//    			if("0".equals(dtd.getBeginstatus())||"".equals(dtd.getBeginstatus().trim())){   			
//	    			if(SystemConstants.Switch.equals(tempPd.getDeviceType())){
//	    				action = new ChangeColorAction(tempPd, SystemConstants
//	    						.getMapColor().get(powerVolt), SystemConstants
//	    						.getMapColor().get(powerVolt));
//	    			}else{
//	    				action = new ChangeColorAction(tempPd, SystemConstants
//	    						.getMapColor().get(powerVolt), null);
//	    			}
//    			}else{
//    				if(SystemConstants.Switch.equals(tempPd.getDeviceType())){
//    				    action = new ChangeColorAction(tempPd,
//    						SystemConstants.getMapColor().get(
//    								SystemConstants.LOSE_COLOR_CODE), SystemConstants.getMapColor().get(
//    										("01".indexOf(tempPd.getDeviceStatus())>=0)?SystemConstants.LOSE_COLOR_CODE:null));
//	    			}else{
//	    				action = new ChangeColorAction(tempPd,
//	    						SystemConstants.getMapColor().get(
//	    								SystemConstants.LOSE_COLOR_CODE), null);
//	    			}
//    			}
//    			action.execute();
//            }
        	
	        CBSystemConstants.clearLineSourceAndLoad();
	       
	       // rollbackGroundLine();
        }
        
    }
    /**
     * 创建时间 2013年12月5日 下午3:13:29
     * 校验时的回滚
     * <AUTHOR>
     * @Title CheckRollback
     */
   public static void CheckRollback(){
   	DispatchTransDevice dtd = new DispatchTransDevice();
       PowerDevice pdDev = null;
       String srcStatus = "";
       //取出源操作对像

       //回滚设备状态信息
       for(int j=alltransDevMap.size();j>0;j--)
       {
       	    dtd = alltransDevMap.get(j);
       	    pdDev = (PowerDevice) dtd.getTransDevice();  
               if (CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals("-1")) //为-1说明不需要对状态进行变更，否则必须将设备原状态设置回去，不论执行成功与否
               {
                   continue;
               } 
               if(dtd.getFlag().equals("0")){
	                srcStatus = dtd.getBeginstatus();
	                pdDev.setDeviceStatus(srcStatus);	
	                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
               }else{
                   srcStatus = CBSystemConstants.getDeviceStateValue(dtd.getBeginstatus()) ;
                   pdDev.setDeviceStatus(srcStatus);
                   DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
               }
       }
      // rollbackGroundLine();
   }
  //回滚状态
    public static void RollbackDeviceStatus(String desc){
    	DispatchTransDevice dtd = new DispatchTransDevice();
        PowerDevice pdDev = null;
        String srcStatus = "";
        //取出源操作对像

        //回滚设备状态信息
        for(int j=alltransDevMap.size();j>0;j--){
    	    dtd = alltransDevMap.get(j);
        	    if(!dtd.getRemark().equals(desc)){
        	    	continue;
        	    }else{
        	    	alltransDevMap.remove(j);
        	    }
        	    pdDev = (PowerDevice) dtd.getTransDevice();  
                if (CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals("-1")){ //为-1说明不需要对状态进行变更，否则必须将设备原状态设置回去，不论执行成功与否
                    continue;
                } 
                if(pdDev.getDeviceType().equals(SystemConstants.RemovableDevice)){
                	pdDev.setDeviceStatus(dtd.getBeginstatus());
                }
                if(dtd.getFlag().equals("0")){
	                srcStatus = dtd.getBeginstatus();
	                pdDev.setDeviceStatus(srcStatus);
	                DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
                }else{
                	if(SystemConstants.Switch.equals(pdDev.getDeviceType())){
                    	continue;
                	}
                    srcStatus = CBSystemConstants.getDeviceStateValue(dtd.getBeginstatus()) ;
                    pdDev.setDeviceStatus(srcStatus);
                    DeviceSVGPanelUtil.changeDeviceSVGColor(pdDev);
                }
        } 
        CBSystemConstants.clearLineSourceAndLoad();
    }
    /**
     * 传人设备对象和目标状态，构建相关设备的状态
     * @param pd
     * @param endStatus
     */
    public static void buildDeviceStatus(PowerDevice pd, String endStatus) {
		HashMap<PowerDevice,String> deviceStatusMap = new HashMap<PowerDevice,String>();
		deviceStatusMap.put(pd, endStatus);
		ExecuteDeviceStatus.execute(deviceStatusMap, true);
	}
    
    /**
     * 监控接转令
     * 
     */
	public CardModel executeJKJZL(RuleBaseMode Srcrbm) {
		CardModel cm = new CardModel();
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.getCurOperateDevs().clear();
		CBSystemConstants.setCurRBM(Srcrbm);
		///设备对位时操作线路设置为电源侧
		if(!CBSystemConstants.isLock && Srcrbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)) {
			CBSystemConstants.LineSource.put(Srcrbm.getPd().getPowerDeviceID(), Srcrbm.getPd());
			CBSystemConstants.LineLoad.put(Srcrbm.getPd().getPowerDeviceID(), new ArrayList<PowerDevice>());
		}
		
		//一、执行规则
		UserRuleExecute userRuleExc=new UserRuleExecute();
		if(userRuleExc.execute(Srcrbm)){
			RuleExecute ruleExc=new RuleExecute();
			RuleExecute.setShowMessage(true);
			if(!ruleExc.execute(Srcrbm)){
				
				if(CBSystemConstants.cardbuildtype.equals("0")) {
					if(TempTicket.getTempTicket()==null) {
						putDeviceStatus(Srcrbm.getPd());
						RollbackDeviceStatus();
						ClearDevMap();
						CBSystemConstants.getDtdMap().clear();
					}
					else {
						RollbackDeviceStatusStep();
						if(TempTicket.getTempTicket()==null){
							CBSystemConstants.clearLineSourceAndLoad();
						}
					}
				}
				else if(CBSystemConstants.cardbuildtype.equals("1")) {
					RollbackDeviceStatusStep();
					if(TempTicket.getTempTicket()==null){
						CBSystemConstants.clearLineSourceAndLoad();
					}
				}
				return null;
			}
		}
		else{
			return null;
		}
		
		//二、将本次操作设备记录保存到缓存中
		putDeviceStatus(Srcrbm.getPd());
		CBSystemConstants.setCurRBM(Srcrbm);
		
    	if(CBSystemConstants.isOutCard){    	
    		//三、构造术语
    		CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
	    	
	    	cm.setCardItems(new ArrayList<CardItemModel>());
	    	
	    	if(CBSystemConstants.roleCode.equals("0")) {
	    		cm=WordExecute.getInstance().execute(Srcrbm);
	    	}
	    	else {
	    		setTask(cm);
				CardModel cardModel=WordExecute.getInstance().execute(Srcrbm);
				cm.getCardItems().addAll(cardModel.getCardItems());
	    	}
	    	for(int i=0;i<cm.getCardItems().size();i++){
	    		cm.getCardItems().get(i).setBzbj(String.valueOf(bzIndex));
	    	}
    	}else{
    		DeviceStatusManager dsm = new DeviceStatusManager();
    		if(DBManager.isInterfaceOpened){
    			dsm.insertDevStatu();
    		}else{
    			try {
    				Connection conn = DBManager.getConnection();
    				dsm.insertDevStatu(conn);
    				conn.close();
    			} catch (SQLException e) {
    				// TODO Auto-generated catch block
    				e.printStackTrace();
    			} // 保存操作票对应设备预令状态
    		}
    	}
    	
    	
	    return cm;
	}
}
