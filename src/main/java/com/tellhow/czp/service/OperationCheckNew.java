package com.tellhow.czp.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.czp.mainframe.menu.GetDeviceMenuModel;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.OperationInfo;
import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.securitycheck.view.CheckWord;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

public class OperationCheckNew {

	public static Map<String,Boolean> loadStation=new HashMap<String, Boolean>();
	
	/**
	  * 创建时间 2014年3月19日 下午3:49:24
	  * 
	  * <AUTHOR>
	  * @Title main
	  * @param args
	  */
	public static void main(String args[]) {
		
		RuleBaseMode b;
		/*b=jiaoyanjiazhixing("草桥", "134、145自投停用");
		System.out.println(b);
		*/
		/*String zbid="ceb9430c-63a4-4872-8e0c-2ac7831e9eb9";
		List<Map> list=DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"t_a_czpmx t where t.f_zbid=?",zbid);
		for (Map map : list) {
			String mxid=StringUtils.ObjToString(map.get("MXID"));
			b=checkAndDoByMxid(mxid);
			System.out.println(b);
		}*/
		CBSystemConstants.unitCode="0";
		CBSystemConstants.isCurrentSys=false;
		
		b = OperationCheck.jiaoyanjiazhixing("仁和站", "拉开遂仁一2212开关");
		
		int aa=1;
		//isEqual("9c1f550f-81b8-4514-9ca0-6abb7c1ef76", "asdfasdf");
		/*b=checkAndDoByMxid("9c1f550f-81b8-4514-9ca0-6abb7c1ef76");
		System.out.println(b);
		b=checkAndDoByMxid("fdfd6891-af96-4821-becb-8ea001fc8c8e");
		System.out.println(b);
		DeviceOperate.RollbackDeviceStatus();
		DeviceOperate.ClearDevMap();*/
		/*b=jiaoyanjiazhixing("仁和", "合上母联2245-5隔离刀闸");
		System.out.println(b);
		b=jiaoyanjiazhixing("仁和站", "合上母联2245-4隔离刀闸");
		System.out.println(b);
		b=jiaoyanjiazhixing("仁和站", "合上母联2245断路器");
		System.out.println(b);
		b=jiaoyanjiazhixing("仁和站", "拉开2211断路器");
		System.out.println(b);
		*/
		
	/*	b=jiaoyanjiazhixing("草桥", "拉开1#变压器101、201开关");
		System.out.println(b);
		b=jiaoyanjiazhixing("草桥", "合上母联134、145开关");
		System.out.println(b);*/
		/*
		b=jiaoyanjiazhixing("草桥", "拉开母联2245开关");
		System.out.println(b);*/
		
		
		
		
		
		
		//b=jiaoyan("白坊站", "拉开#1主变111开关、444开关");
		
	/*	b=jiaoyan("回龙观", "拉开115开关");
		System.out.println(b);
		b=jiaoyan("回龙观", "拉开115-2刀闸");
		System.out.println(b);*/
		/*Object[] obj=rbmToObjcet(b);
		String[] message=(String[]) obj[1];
		//System.out.println(b);
		if(obj[0].equals("2")){
			b=jiaoyan("八里庄", "拉开2215开关", message[2]);
			System.out.println(b);
		}*/
		/*b=execute("八里庄", "拉开2216开关",false);
		System.out.println(b);
		b=execute("玉泉营", "拉开2217开关",false);
		System.out.println(b);*/
		//execute("莲花池", "拉开115-2刀闸");
//		getdate("天通苑", "拉开115-2刀闸");
	}
	
	/**
	  * 创建时间 2013年11月27日 下午2:31:22
	  * 提取rbm里的信息转化成Oms系统要识别的Object数组
	  * <AUTHOR>
	  * @Title rbmToObjcet
	  * @param rbm
	  * @return
	  */
	public static Object[] rbmToObjcet(RuleBaseMode rbm){
		Object[] obj=new Object[2];
		String code = null;
		String[] message=new String[10];
		if(rbm.isNeedChoose()){
			code="2";
			if(rbm.getMessageList().size()!=0){
				message[0]=rbm.getMessageList().get(0);
			}
			
			for (int i = 0; i <rbm.getDeviceConnectStations().size(); i++) {
				message[i+1]=rbm.getDeviceConnectStations().get(i);
			}
		}else if(rbm.getCheckout()){
			code="0";
		}else if(!rbm.getCheckout()){
			code="1";
			if(rbm.getMessageList().size()!=0){
				message[0]=rbm.getMessageList().get(0);
			}
			
		}
		obj[0]=code;
		obj[1]=message;
		return obj;
	}
	
	public static String getdate(String station,String operation){
		StartupManager.startup();
		StringBuffer messageList = new StringBuffer();
		QueryDeviceDao qdd = new QueryDeviceDao();

    		String word = operation;
    		String stationID = qdd.getStationIDByName(station);
    		if(stationID.equals("")) {
    			List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
    			PowerDevice pd = new PowerDevice();
    			pd.setPowerStationName(station);
    			pdlist.add(pd);
    			CheckMessage cm = new CheckMessage();
    			cm.setPd(pdlist);
    			cm.setBottom("300");
    			if(CBSystemConstants.lcm==null){
    				CBSystemConstants.lcm = new ArrayList<CheckMessage>();
    			}
    			CBSystemConstants.lcm.add(cm);
				
			}
    		ArrayList<OperationInfo> list = CheckWord.getOperation(word);
    		CreatePowerStationToplogy.loadFacData(stationID);
    		
    		for(int j = 0; j < list.size(); j++) {
    			OperationInfo oi = list.get(j);
    			String equipID = qdd.getEquipIDByNameType(stationID, oi.getEquipName(), oi.getEquipType());
    		
    			PowerDevice pd = CBSystemConstants.getPowerDevice(stationID, equipID);
    			if(pd == null) {
    				List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
    				pd.setPowerStationName(station);
        			pdlist.add(pd);
        			CheckMessage cm = new CheckMessage();
        			cm.setPd(pdlist);
        			cm.setBottom("300");
        			if(CBSystemConstants.lcm==null){
        				CBSystemConstants.lcm = new ArrayList<CheckMessage>();
        			}
        			CBSystemConstants.lcm.add(cm);
    			}
    			RuleBaseMode rbm = new RuleBaseMode();
    			rbm.setPd(pd);
    			rbm.setBeginStatus(oi.getBeginStatus());
    			rbm.setEndState(oi.getEndStatus());
    			DeviceOperate operate = new DeviceOperate();
    			if(!operate.executeCondition(rbm)) {
    				messageList.append(rbm.getMessageList());
    			}
    		}
    		return "123";
	}
	
	
	public static RuleBaseMode execute(String station, String operation,String side) {
		System.out.println("文字解析开始");
		
		//CBSystemConstants.cardstatus="1";
		CBSystemConstants.cardbuildtype="1";
		RuleBaseMode rbm=new RuleBaseMode();
		try{
		boolean result=true;
		//自投就自动通过
		if(operation.contains("自投")){
			rbm.setCheckout(true);
			return rbm;
		}
		if(station.equals("")){
			rbm.setCheckout(true);
			return rbm;
		}
		//确认系统画面
		if(operation.contains("确认")&&operation.contains("系统画面")){
			rbm.setCheckout(true);
			return rbm;
		}
		if(operation.contains("检查")&&operation.contains("应")){
			rbm.setCheckout(true);
			return rbm;
		}
		//遇到主变就通过
		if(operation.contains("#")&&!operation.contains("开关")&&!operation.contains("刀闸")){
			rbm.setCheckout(true);
			return rbm;
		}
		
		QueryDeviceDao qdd = new QueryDeviceDao();
			//监控特殊处理
    		String word = operation;
    		if(station.indexOf("监控")>-1){
    			int start=operation.indexOf("将");
    			int end=operation.indexOf("站");
    			if(start>-1&&end>-1){
    				station=operation.substring(start+1, end+1);
    				int fi=station.indexOf("kV");
    				if(fi>-1){
    					station=station.substring(fi+2,station.length());
    				}
    			}
    		}
    		String stationID = qdd.getStationIDByName(station);
    		if(stationID.equals("")) {
    			List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
    			PowerDevice pd = new PowerDevice();
    			pd.setPowerStationName(station);
    			pdlist.add(pd);
    			CheckMessage cm = new CheckMessage();
    			cm.setPd(pdlist);
    			cm.setBottom("300");
    			if(CBSystemConstants.lcm==null){
    				CBSystemConstants.lcm = new ArrayList<CheckMessage>();
    			}
    			CBSystemConstants.lcm.add(cm);
    			rbm.getMessageList().add("找不到["+station+"]，厂站名称可能拼写错误!");
				rbm.setCheckout(false);
				return rbm;
			}

    		ArrayList<OperationInfo> list = CheckWord.getOperation(word);
    		
    		if(list.size()==0){
    			result=true;
    		}
    		else if(list.get(0).getEquipNum().equals("") || list.get(0).getBeginStatus().equals("") || list.get(0).getEndStatus().equals("")) {
    			result=true;
    		}
    		else {
	    		//加载实时缓存
	    		if(loadStation.get(stationID)==null||loadStation.get(stationID)==false){
	    			
	    			CreatePowerStationToplogy.loadSysData();
	    			//StationStartupManager.startup(stationID);
	    			if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
						CreatePowerStationToplogy.loadFacEquip(stationID);
					}
	    			InitDeviceStatus ie = new InitDeviceStatus();
	    			ie.initStatus_EMSToCache(stationID);
	    			loadStation.put(stationID, true);
	    		}
	    		result=executeByOI(rbm, list, side, stationID);
    		}
    		
    		rbm.setCheckout(result);
    		System.out.println("文字解析结束");
		}catch(Exception e){
			e.printStackTrace();
		}
		return rbm;
	}
	
	/**
	  * 创建时间 2013年12月10日 上午8:51:55
	  * 根据明细id进行校验
	  * <AUTHOR>
	  * @Title executeByCBid
	  * @param station
	  * @param mxid
	  * @return
	  */
	public static RuleBaseMode executeByCBid(String mxid){
		if(mxid.equals("null")){
			return null;
		}
		boolean result=true;
		RuleBaseMode rbm=new RuleBaseMode();
		//加载最大范围停电信息
		Boolean b1=QueryDeviceDao.getMaxLoadOffByMxid(mxid);
		Boolean b2 = CBSystemConstants.isMaxRangeOffTicket;
		if(b1!=null){
			CBSystemConstants.isMaxRangeOffTicket=b1;
		}
		
		//加载线路电源侧负荷侧缓存
		QueryDeviceDao.loadLineSourceInfo(mxid);
		//获取厂站id
		String stationID= QueryDeviceDao.getStatinByMxid(mxid);
		
		//加载实时态缓存
		//CBSystemConstants.cardstatus="1";
		if(loadStation.get(stationID)==null||loadStation.get(stationID)==false){
			
			CreatePowerStationToplogy.loadSysData();
			//StationStartupManager.startup(stationID);
			if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
				CreatePowerStationToplogy.loadFacEquip(stationID);
			}
			InitDeviceStatus ie = new InitDeviceStatus();
			ie.initStatus_EMSToCache(stationID);
			loadStation.put(stationID, true);
		}
		ArrayList<RuleBaseMode> list = QueryDeviceDao.getRbmByMxid(mxid,stationID);
		if(list.size()==0){
			return null;
		}
		for (int i = 0; i < list.size(); i++) {
			rbm=list.get(i);
			DeviceOperate operate = new DeviceOperate();
			if(!operate.executeCondition(rbm)) {
				result=false;
				break;
			}
			
		}
		rbm.setCheckout(result);
		//清除线路电源侧负荷侧缓存
		CBSystemConstants.clearLineSourceAndLoad();
		//还原最大范围停电信息
		CBSystemConstants.isMaxRangeOffTicket=b2;
		return rbm;
		
	}
	
	
	/**
	  * 创建时间 2013年12月9日 下午5:08:25
	  * 根据OI进行校验
	  * <AUTHOR>
	  * @Title executeByOI
	  * @param rbm
	  * @param list
	  * @param side
	  * @param stationID
	  * @return
	  */
	public static boolean executeByOI(RuleBaseMode rbm,ArrayList<OperationInfo> list,String side,String stationID){
		boolean result = true;
		if(list.size()==0){
			return false;
		}
		for(int j = 0; j < list.size(); j++) {
			OperationInfo oi = list.get(j);
			if(oi.getBeginStatus().equals("")||oi.getEndStatus().equals("")){
				result = false;
				continue;
			}
			String equipID =QueryDeviceDao.getEquipIDByNameType(stationID, oi.getEquipNum(), oi.getTypeName());
		
			PowerDevice pd = CBSystemConstants.getPowerDevice(stationID, equipID);
	    	
			if(pd == null) {
//	    		rbm.getMessageList().add("找不到["+oi.getEquipName()+"]，设备名称可能拼写错误!");
				List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
				pdlist.add(pd);
				CheckMessage cm = new CheckMessage();
				cm.setPd(pdlist);
				cm.setBottom("302");
				if(CBSystemConstants.lcm==null){
					CBSystemConstants.lcm = new ArrayList<CheckMessage>();
				}
				CBSystemConstants.lcm.add(cm);
	    		result=false;
				break;
			}
			rbm.setPd(pd);
			rbm.setSrcSide(side);
			rbm.setBeginStatus(oi.getBeginStatus());
			rbm.setEndState(oi.getEndStatus());
			DeviceOperate operate = new DeviceOperate();
			System.out.println("校验"+pd.getPowerDeviceName()+oi.getBeginStatus()+"-"+oi.getEndStatus());
			if(!operate.executeCondition(rbm)) {
				result=false;
				break;
			}
			
		}
		return result;
	}
	
	/**
	 * 内部调用这个方法
	 * */
	public static RuleBaseMode execute(String station, String operation){
		return execute(station,operation,"");
	}
	
	/**
	 * 外部调用用这个方法 只校验
	 * */
	/*public static RuleBaseMode jiaoyan(String station, String operation) {
	
		CBSystemConstants.isCurrentSys=false;
		return execute(station, operation,"");
	}*/
	/**
	 * 外部调用用这个方法 校验加执行
	 * */
	public static RuleBaseMode jiaoyanjiazhixing(String station, String operation) {
		CBSystemConstants.isCurrentSys=false;
		return execute(station, operation,"");
	}
	/**
	 * 外部调用带电源侧厂站名称  只校验
	 * */
	/*public static RuleBaseMode  jiaoyan(String station, String operation,String srcStation) {

		Boolean b = CBSystemConstants.isMaxRangeOffTicket;
		CBSystemConstants.isMaxRangeOffTicket=true;
		CBSystemConstants.isCurrentSys=false;
		RuleBaseMode rbm = execute(station, operation,srcStation);
		CBSystemConstants.isMaxRangeOffTicket=b;
		return rbm;
	}*/
	/**
	 * 外部调用带电源侧厂站名称  校验加执行
	 * */
	/*public static  RuleBaseMode  jiaoyanjiazhixing(String station, String operation,String srcStation){
		CBSystemConstants.isCurrentSys=false;
		return execute(station, operation,srcStation);
	}*/
	
	public static RuleBaseMode checkAndDoByMxid(String cbid){
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.cardbuildtype="1";
		return executeByCBid(cbid);
	}
	
	public static boolean isEqual(String cbid,String sy){
		List list= DBManager.query("select t.cznr from "+CBSystemConstants.opcardUser+"T_A_CZPMX t where t.mxid='"+cbid+"'");
		if(list.size()==0)
			return false;
		String cznr=StringUtils.ObjToString(((Map)list.get(0)).get("cznr"));
		if(cznr==null)
			return false;
		if(cznr.equals(sy)){
			return true;
		}else 
			return false;
	}
	/*public static RuleBaseMode checkOnlyByMxid(String cbid){
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.cardbuildtype="1";
		return executeByCBid(cbid);
	}*/
	/**
	 * 功能：传入参数,一键成票
	 * <AUTHOR>
	 * @since 2014-09-10  10:00
	 * @param stationID  传入产站id
	 * @param deviceID	 传入设备ID
	 * @param statename	 czdw 操作单位
	 * @param ticketType 开票类型  1、调度票 2、监控票 3、配网调度票  4、配网监控票
	 * @return
	 */
	public static Boolean oneClickCP(String stateName,String deviceID,String stationID){
		
		CBSystemConstants.oneClickOpenerTicket = true;
//		stationID = "5bff24c7-fb0b-46c1-99b8-2a48ba22d320";//5bff24c7-fb0b-46c1-99b8-2a48ba22d320
//		deviceID = "2a16a556-4c5d-4222-959e-864bd55bf11b";//2a16a556-4c5d-4222-959e-864bd55bf11b
//		stateName = "检修";
		CreatePowerStationToplogy.loadSysData();
		CreatePowerStationToplogy.loadFacData(stationID);
		CreatePowerStationToplogy.loadFacEquip(stationID);
		PowerDevice pd = new PowerDevice();
		//获得产站下的集合和设备集合
		HashMap<String, PowerDevice> stationDevs = CBSystemConstants.getMapPowerStationDevice().get(stationID);
		pd = stationDevs.get(deviceID);//获得产站下的设备
    	
		String startState = pd.getDeviceStatus();//设备状态
		String endState = "";
//		String stateName = "";
		HashMap<String, String> statusMap = new HashMap<String, String>();
		if(startState.equals("0")){
			endState = "运行";
		}else if(startState.equals("1")){
			endState = "热备用";
		}else if(startState.equals("2")){
			endState = "冷备用";
		}else if(startState.equals("3")){
			endState = "检修";
		}
		if(endState.equals(stateName)){
			//ShowMessage.view("设备状态无法解析");
//			CBSystemConstants.oneClickReturn = "设备状态无法解析";
        	return false;
		}
		String filePath = "";
		String fileName = "";
		String stationName = pd.getPowerStationName();
		//等待一次接线图目录加载完成
		try { 
			Thread.sleep (1000) ; 
		} catch (InterruptedException e)
		{
			e.printStackTrace();
		}
		//得到svg文档
		List<SVGFile> fileList = SystemConstants.getSVGFileByStationID(stationID);
        if(fileList.size() == 0) {
//        	if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive())
//        		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
//        	else
//        		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在" + stationName + "一次接线图！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
            return false;
        }
        else if(fileList.size() == 1) {
        	filePath = fileList.get(0).getFilePath();
            fileName = fileList.get(0).getFileName();
        }
        else {
        	Object[] options = fileList.toArray(); 
        	int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
        	if(i == -1)
        		return false;
        	filePath = fileList.get(i).getFilePath();
            fileName = fileList.get(i).getFileName();
        }
        //点击目录中的svg文件
		CreatePowerStationToplogy.createSVGPanel(stationID, stationName, fileName, filePath);

		String stateCode = getStateCode(pd.getDeviceType(),stateName,CBSystemConstants.cardbuildtype);//得到操作码
		Map<String,DeviceMenuModel> devMenusMap = null;
		GetDeviceMenuModel gdmm=new GetDeviceMenuModel();
		devMenusMap=gdmm.execute(pd);//得到右击操作数据
		DeviceMenuModel dmm = new DeviceMenuModel();
		if(devMenusMap.get(stateCode).getStatename().equals(stateName))
			dmm = devMenusMap.get(stateCode);//检修
		if (CBSystemConstants.getSamepdlist().size() == 0) {
			DeviceOperate dre = new DeviceOperate();
			dre.execute(pd, dmm);//右击后执行操作
		}
		return true;

	}
	
	
	/**
	 * 得到数据库中唯一的操作码statecode
	 * <AUTHOR>
	 * @since 2014-09-10 11:15
	 * @param deviceType
	 * @param stateName
	 * @param powerState
	 * @return
	 */
	public static String getStateCode(String deviceType,String stateName,String powerState){
		String sql = "select statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t where t.islock = '0' and t.DEVICETYPEID = '"
				+deviceType+"'"+" and t.STATENAME = '"+stateName+"'"+"and t.CARDBUILDTYPE = '"+powerState+"'"+" and t.opcode = '"+CBSystemConstants.opCode+"'";
		List list = DBManager.query(sql);
		Map temp = new HashMap();
		temp = (Map)list.get(0);
		
		String statecode = temp.get("statecode").toString();
		return statecode;
	}
	
	/**
	 * 根据传入的产站名称解析出产站id stationID
	 * <AUTHOR>
	 * @since 2014-09-10  14:39
	 * @param stationName
	 * @return
	 */
	public static String getStationId(String stationName){
		String sql = "select STATION_ID from "+CBSystemConstants.equipUser+"T_SUBSTATION t where t.station_name = '"
				+stationName+"'";
		List list = DBManager.query(sql);
		Map temp = new HashMap();
		temp = (Map)list.get(0);
		String stationID = temp.get("STATION_ID").toString();
		return stationID;
	}
	
	/**
	 * 根据传入的产站名称解析出设备id equipId
	 * <AUTHOR>
	 * @since 2014-09-10  15:01
	 * @param deviceName
	 * @return
	 */
	public static String getDeviceID(String deviceName){
		String sql ="";
		String deviceId = "";
		if(deviceName.indexOf("线") >0){
			sql = "select ID from "+CBSystemConstants.equipUser+"T_C_ACLINEEND t where t.NAME='"+deviceName+"'";
			List list = DBManager.query(sql);
			Map temp = new HashMap();
			temp = (Map)list.get(0);
			deviceId = temp.get("ID").toString();
		}else{
			sql = "select EQUIP_ID from "+CBSystemConstants.equipUser+"T_EQUIPINFO t where t.EQUIP_NAME='"+deviceName+"'";
			List list = DBManager.query(sql);
			Map temp = new HashMap();
			temp = (Map)list.get(0);
			deviceId = temp.get("EQUIP_ID").toString();
		}
		return deviceId;
	}
	
	/**
	 * 根据传入的产站名称解析出设备id equipId
	 * <AUTHOR>
	 * @since 2014-09-10  15:01
	 * @param deviceName
	 * @return
	 */
	public static String getDeviceID(String deviceName,String czdw){
		String sql ="";
		String deviceId = "";
		if(deviceName.indexOf("线") >0){
			sql = "select ID from "+CBSystemConstants.equipUser+"T_C_ACLINEEND t where t.NAME like'%"+czdw+"%"+deviceName+"%'";
			List list = DBManager.query(sql);
			Map temp = new HashMap();
			temp = (Map)list.get(0);
			deviceId = temp.get("ID").toString();
		}else{
			sql = "select EQUIP_ID from "+CBSystemConstants.equipUser+"T_EQUIPINFO t where t.EQUIP_NAME like '%"+czdw+"%"+deviceName+"%'";
			List list = DBManager.query(sql);
			Map temp = new HashMap();
			temp = (Map)list.get(0);
			deviceId = temp.get("EQUIP_ID").toString();
		}
		return deviceId;
	}
	

}
