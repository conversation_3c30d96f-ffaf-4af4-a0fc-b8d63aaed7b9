package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.operationcard.EchoReplace;
import com.tellhow.czp.service.OperationCheck;
import com.tellhow.czp.service.OperationCheckDefault;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;

public class CheckCzpImpl {
	public static String getXMLcode(String str){
		if(str.toUpperCase().contains("UTF-8")){
			return "UTF-8";
		}else{
			return "GBK";
		}
	}
	
	public static Element getItemByCbid(Element datas, String cbid) {
		Element target=null;
		for (Element element : (List<Element>)datas.elements()) {
			Element cbElement=element.element("cbid");
			if(cbElement!=null){
				String checkid=cbElement.getTextTrim();
				if(checkid.equals(cbid)){
					target=element;
				}
			}
		}
		return target;
	}
	
	public static final String SELECT="1";	//是否需要校核：   1 选中 表示需要校核
	
	public String execute(String arg){
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		
		System.out.println("**********当前时间**********");
		System.out.println(sdf.format(date));
		System.out.println("**********输入参数**********");
		System.out.println(arg);
		System.out.println("***************************");
		/**
		 * 设置系统运行方式为校核类型。
		 * */
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.opCode = "0";
		CBSystemConstants.opRuleCode = "0";
		
		String xmlCode = getXMLcode(arg);
		
		/**
		 * 构造返回的xml。
		 * */
		Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding(xmlCode);
		Element datas=doc.addElement("Datas");
		
		/**
		 * 解析传入的xml。
		 * */
		//传入参数数据
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		InputStream is =null;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("ITEM");
			//System.out.println(itemLists);
			for (int i = 0; i <itemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				list.add(mapInfo);
			}
			
			List<Element> czrwLists =root.elements("CZRW");
			//System.out.println(itemLists);
			if(czrwLists.size() > 0) {
				//主表操作任务文字替换，并赋值给调度令变量
				CBSystemConstants.ddzl = czrwLists.get(0).getTextTrim().replace("千伏", "kV");
			}
			
			List<Element> qymbLists =root.elements("AREANO");
			if(qymbLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				CBSystemConstants.qybm = qymbLists.get(0).getTextTrim();
			}
			
			checkOperate(list, datas);
			
			date = new Date();
			System.out.println("**********结束时间**********");
			System.out.println(sdf.format(date));
			System.out.println("**********输出结果**********");
			System.out.println(doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>"));
			System.out.println("***************************");
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			if(is!=null){
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return doc.asXML();
	}
	
	/**
	  * 创建时间 2014年12月23日 下午11:24:15
	  * 重庆进行校核
	  * <AUTHOR>
	  * @Title checkOperateCQ
	  * @param checkType   RUNTIMECHECK 实时校核   LOGICCHECK 逻辑校核
	  * 修改时间：2014-12-23
	  * 修改内容：重庆校核
	  */
	private void checkOperate(List<Map<String, String>> list, Element datas){
		try {
			//设备防误信息
			if(CBSystemConstants.lcm!=null)
				CBSystemConstants.lcm.clear();
			ArrayList<int[]> errorList = new ArrayList<int[]>();
			errorList.clear();
			//防误错误信息编码转文字提示
			EchoReplace ec = new EchoReplace();
			// 开票方式 0：正常票 1：点图成票
			CBSystemConstants.cardbuildtype = "1";
			//判断是否处于校核状态 0 非校核 1校核
			CBSystemConstants.jh_tai = 1;
			//加载系统数据，厂站，设备状态，组织等数据
			CreatePowerStationToplogy.loadSysData();
			//加载厂站
			List<String> loadStationList = new ArrayList<String>();
			
			//重置指令单位id为0
			CBSystemConstants.czdwId = "";
			
			for (Map<String, String> map : list) {
				String station=map.get("changzhan");
				String opr=map.get("caozuozhiling");
				String cbid=map.get("cbid");
				String roleCode=map.get("roleCode");
				//将指令关联的操作单位id加入缓存，用于后续筛选变电站
				CBSystemConstants.czdwId = StringUtils.ObjToString(map.get("czdwids"));
				// 业务类型 0:主网调度1：配网调度
				CBSystemConstants.roleCode=roleCode;
				if(CBSystemConstants.roleCode==null || CBSystemConstants.roleCode.equals("")) {
					CBSystemConstants.roleCode = "0";
				}
				
				//是否实时校核，1为实时校核，0为拟票校核
				String isrealtime=map.get("isrealtime");
				CBSystemConstants.isRealTime=isrealtime.equals("1")?true:false;
				//清空设备防误信息
				if(CBSystemConstants.lcm != null) {
					CBSystemConstants.lcm.clear();
				}
				//校核开始
				List<RuleBaseMode> rbmStep=OperationCheckDefault.execute(station,opr);
				
				for(Iterator<RuleBaseMode> itor = rbmStep.iterator();itor.hasNext();){
					RuleBaseMode rbm = itor.next();
					
					if(rbm.getPd() == null){
						itor.remove();
					}
				}
				
				CBSystemConstants.getDtdMap().clear();
				
				//实时状态加载
				for(RuleBaseMode rbm : rbmStep) {
					if(rbm.getPd() != null) {
						//获取厂站id
						String stationID = rbm.getPd().getPowerStationID();
						if(!stationID.equals("")) {
							//若缓存中无该厂站中对应的设备集合，重新加载
							if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
								CreatePowerStationToplogy.loadFacEquip(stationID);
							}
							
							if(!loadStationList.contains(stationID)) {
								InitDeviceStatus ie = new InitDeviceStatus();
								ie.initStatus_EMSToCache(stationID);
								loadStationList.add(stationID);
							}
						}
						List<PowerDevice> otherXL = new ArrayList<PowerDevice>();//存放设备相关其他侧线路
						if(rbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)){
							otherXL =RuleExeUtil.getLineOtherSideList(rbm.getPd());
						}else if(!rbm.getPd().getPowerDeviceID().equals("")){//非线路设备，先搜关联的线路，找到了线路，再搜其他侧线路
							List<PowerDevice> xlList  = RuleExeUtil.getDeviceList(rbm.getPd(), SystemConstants.InOutLine,
									SystemConstants.PowerTransformer, true, true, true);
							if(xlList != null){
								if(xlList.size()>0){
									otherXL =RuleExeUtil.getLineOtherSideList(xlList.get(0));
								}
							}
						}

						for(PowerDevice xl:otherXL){
							if(CBSystemConstants.getStationPowerDevices(xl.getPowerStationID())==null) {
								CreatePowerStationToplogy.loadFacEquip(xl.getPowerStationID());
							}
							
							if(!loadStationList.contains(xl.getPowerStationID())) {
								InitDeviceStatus ie = new InitDeviceStatus();
								ie.initStatus_EMSToCache(xl.getPowerStationID());
								loadStationList.add(xl.getPowerStationID());
							}
						}
					}
				}
				
				/**
				 * 实时校核或第一个操作刷新场站缓存。
				 * */
				if(CBSystemConstants.isRealTime){
					OperationCheck.loadStation.clear();
				}
				
				//返回的值
				List<Map<String,String>> messageList = new ArrayList<Map<String,String>>();
				
				for(RuleBaseMode rbm : rbmStep){
					ArrayList<String> oprList = new ArrayList<String>();
					oprList.add(opr);
					rbm.setInfoList(oprList);
					
					if(CBSystemConstants.isRealTime){
						if(rbm.getOperaTion().equals("倒母")){
							
						}else if(rbm.getOperaTion().equals("装设地线")||rbm.getOperaTion().equals("拆除地线")){
							
						}else{
							OperationCheckDefault.check(rbm);
							
							if(CBSystemConstants.lcm != null && CBSystemConstants.lcm.size() > 0) {
								for (CheckMessage msg : CBSystemConstants.lcm) {
									if(!msg.getBottom().equals("305")&&!msg.getBottom().equals("306")
											&&!msg.getBottom().equals("307")&&!msg.getBottom().equals("308")){
										RuleExeUtil.deviceStatusReset(rbm.getPd(), rbm.getPd().getDeviceStatus(), rbm.getBeginStatus());
										break;
									}
								}
							}
						}
					}else{
						if(rbm.getOperaTion().equals("倒母")){
							
						}else if(rbm.getOperaTion().equals("装设地线")||rbm.getOperaTion().equals("拆除地线")){
							
						}else if(rbm.getBeginStatus().equals(rbm.getPd().getDeviceStatus())){
							OperationCheckDefault.check(rbm);
							
							if(CBSystemConstants.lcm != null && CBSystemConstants.lcm.size() > 0) {
								for (CheckMessage msg : CBSystemConstants.lcm) {
									if(!msg.getBottom().equals("305")&&!msg.getBottom().equals("306")
											&&!msg.getBottom().equals("307")&&!msg.getBottom().equals("308")){

										RuleExeUtil.deviceStatusReset(rbm.getPd(), rbm.getPd().getDeviceStatus(), rbm.getBeginStatus());
										break;
									}
								}
							}
						}else{
							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
							pdlist.add(rbm.getPd());
							CheckMessage cm = new CheckMessage();
							cm.setPd(pdlist);
							cm.setBottom("301");
							cm.setStatus(rbm.getPd().getDeviceStatus());
							if(CBSystemConstants.lcm==null){
								CBSystemConstants.lcm = new ArrayList<CheckMessage>();
							}
							CBSystemConstants.lcm.add(cm);
						}
					}
						
					List<Map<String,String>> msgList = new ArrayList<Map<String,String>>();
					
					if(CBSystemConstants.lcm != null && CBSystemConstants.lcm.size() > 0) {
						List<String> msgStrList = new ArrayList<String>();//同类提示只显示第一个
						
						for (CheckMessage msg : CBSystemConstants.lcm) {
							if(msgStrList.contains(msg.getBottom())){
								continue;
							}
							
							msgStrList.add(msg.getBottom());
							Map<String,String> resultMap = ec.getEchoMap(msg, rbm.getPd());

							String message = resultMap.get("msg");

							//拼接设备状态不符的详细信息
							if(message.contains("初始状态与操作指令不一致")){
								String deviceStatus = msg.getStatus();
								message += "，当前设备状态："+RuleExeUtil.getStatusNew(msg.getPd().get(0).getDeviceType(), deviceStatus)+"，请确认！";
							}else if(message.contains("当前指令设备名称不符合命名规范")){
								message += "当前设备模型名称："+msg.getPd().get(0).getPowerDeviceName()+"，校核名称："+msg.getDeviceName();
							}else if(message.contains("操作指令与设备状态不符")){
								String deviceStatus = msg.getStatus();
								message += "，当前设备状态："+RuleExeUtil.getStatusNew(msg.getPd().get(0).getDeviceType(), deviceStatus)+"，请确认！";
							}else if(message.contains("当前指令状态转换术语不规范")){
								String deviceStatus = msg.getStatus();
								message += "，当前设备状态："+RuleExeUtil.getStatusNew(msg.getPd().get(0).getDeviceType(), deviceStatus)+"，请确认！";
							}

							resultMap.put("msg", message);
							
							msgList.add(resultMap);
						}
						
						messageList.addAll(msgList);
					}else{
						rbm.setCheckResult(0);
						Map<String,String> tempMap = new HashMap<String,String>();
						tempMap.put("msg", "成功");
						messageList.add(tempMap);
					}
					
					if(CBSystemConstants.lcm != null){
						CBSystemConstants.lcm.clear();
					}
				}
				
				Map<String,String> remessageMap = new HashMap<String, String>();
				
				StringBuffer cardwordcheck = new StringBuffer();
				StringBuffer statuscheck = new StringBuffer();
				StringBuffer topologycheck = new StringBuffer();
				StringBuffer addsigncheck = new StringBuffer();
				StringBuffer losspowercheck = new StringBuffer();
				StringBuffer closedloopcheck = new StringBuffer();

				remessageMap.put("cardwordcheckresult", "成功");
				remessageMap.put("cardwordcheckinfo", "成功");
				remessageMap.put("cardwordcheckcode", "0");

				remessageMap.put("statuscheckresult", "成功");
				remessageMap.put("statuscheckinfo", "成功");
				remessageMap.put("statuscheckcode", "0");

				remessageMap.put("topologycheckresult", "成功");
				remessageMap.put("topologycheckinfo", "成功");
				remessageMap.put("topologycheckcode", "0");

				remessageMap.put("losspowercheckresult", "成功");
				remessageMap.put("losspowercheckinfo", "成功");
				remessageMap.put("losspowercheckcode", "0");
				
				//挂牌
				remessageMap.put("addsigncheckresult", "成功");
				remessageMap.put("addsigncheckinfo", "成功");
				remessageMap.put("addsigncheckcode", "0");
				
				//跨电压等级合环
				remessageMap.put("closedloopcheckresult", "成功");
				remessageMap.put("closedloopcheckinfo", "成功");
				remessageMap.put("closedloopcheckcode", "0");
				
				for(int j=0 ; j < messageList.size() ; j++){
					Map<String,String> tempMap = messageList.get(j);
					
					String message = tempMap.get("msg");
					String code = tempMap.get("code");
					String kind = tempMap.get("kind");
					
					if(message.equals("成功")){
						continue;
					}
					
					if(kind.equals("0")){
						cardwordcheck.append(message+"；");
						remessageMap.put("cardwordcheckresult", "失败");
						remessageMap.put("cardwordcheckcode", code);
					}else if(kind.equals("1")){
						if(!CBSystemConstants.isSimulation){
							statuscheck.append(message+"；");
							remessageMap.put("statuscheckresult", "失败");
							
							if(remessageMap.get("statuscheckcode").equals("2")&&code.equals("1")){
								
							}else{
								remessageMap.put("statuscheckcode", code);
							}
						}
					}else if(kind.equals("2")){
						topologycheck.append(message+"；");
						remessageMap.put("topologycheckresult", "失败");
						
						if(remessageMap.get("topologycheckcode").equals("2")&&code.equals("1")){
							
						}else{
							remessageMap.put("topologycheckcode", code);
						}
					}else if(kind.equals("3")){
						losspowercheck.append(message+"；");
						remessageMap.put("losspowercheckresult", "失败");
						
						if(remessageMap.get("losspowercheckcode").equals("2")&&code.equals("1")){
							
						}else{
							remessageMap.put("losspowercheckcode", code);
						}
					}else if(kind.equals("4")){
						addsigncheck.append(message+"；");
						remessageMap.put("addsigncheckresult", "失败");
						
						if(remessageMap.get("addsigncheckinfo").equals("2")&&code.equals("1")){
							
						}else{
							remessageMap.put("addsigncheckcode", code);
						}
					}else{
						closedloopcheck.append(message+"；");
						remessageMap.put("closedloopcheckresult", "失败");
						
						if(remessageMap.get("closedloopcheckinfo").equals("2")&&code.equals("1")){
							
						}else{
							remessageMap.put("closedloopcheckcode", code);
						}
					}
				}
				
				if(cardwordcheck.length() == 0){
					cardwordcheck.append("成功");
				}
				
				if(statuscheck.length() == 0){
					statuscheck.append("成功");
				}
				
				if(topologycheck.length() == 0){
					topologycheck.append("成功");
				}
				
				if(losspowercheck.length() == 0){
					losspowercheck.append("成功");
				}
				
				if(addsigncheck.length() == 0){
					addsigncheck.append("成功");
				}
				
				if(closedloopcheck.length() == 0){
					closedloopcheck.append("成功");
				}
				
				
				remessageMap.put("cardwordcheckinfo", cardwordcheck.toString());
				
				if(!CBSystemConstants.isSimulation){
					remessageMap.put("statuscheckinfo", statuscheck.toString());
				}else{
					remessageMap.put("statuscheckresult", "-");
					remessageMap.put("statuscheckinfo", "-");
				}
				
				remessageMap.put("topologycheckinfo", topologycheck.toString());
				
				remessageMap.put("losspowercheckinfo", losspowercheck.toString());
				remessageMap.put("addsigncheckinfo", addsigncheck.toString());
				remessageMap.put("closedloopcheckinfo", closedloopcheck.toString());

				if(CBSystemConstants.lcm != null){
					CBSystemConstants.lcm.clear();
				}
				
				/**
				 * 拼接校核信息。
				 * */
				Element item = getItemByCbid(datas,cbid);
				if(item==null){
					item=datas.addElement("ITEM");
					item.addElement("changzhan").setText(station);
					item.addElement("caozuozhiling1").setText(opr);
					item.addElement("cbid").setText(cbid);
					
					if(!StringUtils.ObjToString(map.get("zbid")).equals("")){
						item.addElement("zbid").setText(StringUtils.ObjToString(map.get("zbid")));
					}
					
	            	String cardwordcheckresult = remessageMap.get("cardwordcheckresult");
	            	String cardwordcheckinfo = remessageMap.get("cardwordcheckinfo");
	            	String cardwordcheckcode = remessageMap.get("cardwordcheckcode");

	            	String statuscheckresult = remessageMap.get("statuscheckresult");
	            	String statuscheckinfo = remessageMap.get("statuscheckinfo");
	            	String statuscheckcode = remessageMap.get("statuscheckcode");

	            	String topologycheckresult = remessageMap.get("topologycheckresult");
	            	String topologycheckinfo = remessageMap.get("topologycheckinfo");
	            	String topologycheckcode = remessageMap.get("topologycheckcode");
	            	
	            	String losspowercheckresult = remessageMap.get("losspowercheckresult");
	            	String losspowercheckinfo = remessageMap.get("losspowercheckinfo");
	            	String losspowercheckcode = remessageMap.get("losspowercheckcode");

	            	String addsigncheckresult = remessageMap.get("addsigncheckresult");
	            	String addsigncheckinfo = remessageMap.get("addsigncheckinfo");
	            	String addsigncheckcode = remessageMap.get("addsigncheckcode");
	            	
	            	String closedloopcheckresult = remessageMap.get("closedloopcheckresult");
	            	String closedloopcheckinfo = remessageMap.get("closedloopcheckinfo");
	            	String closedloopcheckcode = remessageMap.get("closedloopcheckcode");
	            	
	            	item.addElement("cardwordcheckresult").setText(cardwordcheckresult);
	            	item.addElement("cardwordcheckinfo").setText(cardwordcheckinfo);
	            	item.addElement("cardwordcheckcode").setText(cardwordcheckcode);

	            	item.addElement("statuscheckresult").setText(statuscheckresult);
	            	item.addElement("statuscheckinfo").setText(statuscheckinfo);
	            	item.addElement("statuscheckcode").setText(statuscheckcode);

	            	item.addElement("topologycheckresult").setText(topologycheckresult);
	            	item.addElement("topologycheckinfo").setText(topologycheckinfo);
	            	item.addElement("topologycheckcode").setText(topologycheckcode);

	            	item.addElement("losspowercheckresult").setText(losspowercheckresult);
	            	item.addElement("losspowercheckinfo").setText(losspowercheckinfo);
	            	item.addElement("losspowercheckcode").setText(losspowercheckcode);

	            	item.addElement("addsigncheckresult").setText(addsigncheckresult);
	            	item.addElement("addsigncheckinfo").setText(addsigncheckinfo);
	            	item.addElement("addsigncheckcode").setText(addsigncheckcode);

	            	item.addElement("closedloopcheckresult").setText(closedloopcheckresult);
	            	item.addElement("closedloopcheckinfo").setText(closedloopcheckinfo);
	            	item.addElement("closedloopcheckcode").setText(closedloopcheckcode);
	            	
	            	/*
	            	 * 海南字段
	            	 */
	            	/*if(cardwordcheckresult.equals("失败")){
		            	item.addElement("pmresult").setText(cardwordcheckinfo);
	            	}else{
	            		item.addElement("pmresult").setText("成功");
	            	}
	            	
	            	if(statuscheckresult.equals("失败")){
		            	item.addElement("ztresult").setText(statuscheckinfo);
	            	}else{
	            		item.addElement("ztresult").setText("成功");
	            	}
	            	
	            	if(topologycheckresult.equals("失败")){
		            	item.addElement("tpresult").setText(topologycheckinfo);
	            	}else{
	            		item.addElement("tpresult").setText("成功");
	            	}*/
	            	
	            	if(cardwordcheckresult.equals("成功")&&statuscheckresult.equals("成功")&&topologycheckresult.equals("成功")){
	    	            item.addElement("code").setText("0");
	                	item.addElement("remessage").setText("成功");
	            	}else if(cardwordcheckresult.equals("成功")&&statuscheckresult.equals("-")&&topologycheckresult.equals("成功")){
	    	            item.addElement("code").setText("0");
	                	item.addElement("remessage").setText("成功");
	            	}else{
	            		String remessage = "";
	            		
	            		if(cardwordcheckresult.equals("失败")){
	            			remessage += cardwordcheckinfo;
	            		}
	            		
	            		if(!CBSystemConstants.isSimulation){
	            			if(statuscheckresult.equals("失败")){
	                			remessage += statuscheckinfo;
	                		}
	            		}
	            		
	            		if(topologycheckresult.equals("失败")){
	            			remessage += topologycheckinfo;
	            		}
	            		
	            		item.addElement("code").setText("2");
	                	item.addElement("remessage").setText(remessage);
	            	}
				}
				datas.addText("\r\n");
			}
			//201508
			DeviceOperate.RollbackDeviceStatus();
			DeviceOperate.getAlltransDevMap().clear();
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
}
