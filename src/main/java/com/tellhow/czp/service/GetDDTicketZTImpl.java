package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;

public class GetDDTicketZTImpl {
public static String pwRoomName = "";//配网设备关联配电箱 大连检修单成票
public static String pwOperationEquipment = "";//投运设备 大连检修单成票
public static List<Map<String, String>> globalSlaveDevList = new ArrayList<Map<String, String>>();

public String execute(String arg){
		
	String result = "";
	
	/**
	 * 设置系统运行方式为智能开票类型。
	 * */
	CBSystemConstants.isCurrentSys=false;
	CBSystemConstants.cardbuildtype = "0";
	CBSystemConstants.roleCode = "0";
	CBSystemConstants.opCode = "0";
	CBSystemConstants.opRuleCode = "0";
	CBSystemConstants.jh_tai = 0;
	
	System.out.println("输入参数：{"+arg+"}");
	
	String xmlCode = getXMLcode(arg);
	/**
	 * 构造返回的xml。
	 * */
	Document doc=DocumentHelper.createDocument();
	doc.setXMLEncoding(xmlCode);
	Element datas=doc.addElement("Datas");
	
	/**
	 * 解析传入的xml。
	 * */
	//传入参数数据
	List<Map<String, String>> mainDevlist = new ArrayList<Map<String, String>>();
	List<Map<String, String>> slaveDevlist = new ArrayList<Map<String, String>>();
	
	InputStream is=null;
	try {
		is = new ByteArrayInputStream(arg.getBytes(xmlCode));
		DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
		DocumentBuilder db = dbf.newDocumentBuilder();
		org.w3c.dom.Document document = db.parse(is);
		DOMReader domReader = new DOMReader();
		Document ret = domReader.read(document);
		Element root = ret.getRootElement();
		
		/*
		 * 清除全局变量
		 */
		globalSlaveDevList.clear();
		
		List<Element> qymbLists =root.elements("areano");
		if(qymbLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			CBSystemConstants.qybm = qymbLists.get(0).getTextTrim();
			CBSystemConstants.unitCode = qymbLists.get(0).getTextTrim();
		}
		
		List<Element> roomnameLists =root.elements("roomname");
		if(roomnameLists.size() > 0) {
			
		}
		
		StartupManager.startup();
		CZPService.getService().setArg("");
		
		
		List<Element> roleLists =root.elements("role");
		if(roleLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			CBSystemConstants.roleCode = roleLists.get(0).getTextTrim();
		}
		
		
		if(qymbLists.size() > 0) {
			CBSystemConstants.opCode = QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode);
		    if(CBSystemConstants.opCode.equals(""))
		    	CBSystemConstants.opCode = QueryDeviceDao.getOpcode("0", CBSystemConstants.roleCode);
		    CBSystemConstants.opRuleCode = CBSystemConstants.opCode;
		}
		
	
		
		String operation = "";
		String endstatus = "";
		List<Element> operationLists =root.elements("operation");
		if(operationLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			operation = operationLists.get(0).getTextTrim();
			if(operation.equals("运行")){
				endstatus = "0";
			}else if(operation.equals("热备用")){
				endstatus = "1";
			}else if(operation.equals("冷备用")){
				endstatus = "2";
			}else if(operation.equals("检修")){
				endstatus = "3";
			}
		}
		
		List<Element> firstsstationLists = root.elements("firststation");
		if(firstsstationLists.size() > 0) {
			CBSystemConstants.oneClickString = firstsstationLists.get(0).getTextTrim();
		}
		
		String beginstatus = "";
		List<Element> beginstatusLists =root.elements("beginstatus");
		if(beginstatusLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			beginstatus = beginstatusLists.get(0).getTextTrim();
		}
		
		//获取ITEM节点DOM
		List<Element> mainItemLists =root.elements("mainequip");
		//System.out.println(itemLists);
		for (int i = 0; i <mainItemLists.size(); i++) {
			Map<String, String> mapInfo =new HashMap<String,String>();
			Element element = mainItemLists.get(i);
			List<Element> elist = element.elements();
			for (int j = 0; j < elist.size(); j++) {
				Element el = elist.get(j);
				//将节点名称与值放入集合
				mapInfo.put(el.getName(), el.getTextTrim());				
			}
			mainDevlist.add(mapInfo);
		}
		
		//获取ITEM节点DOM
		List<Element> slaveItemLists =root.elements("slaveequip");
		//System.out.println(itemLists);
		for (int i = 0; i <slaveItemLists.size(); i++) {
			Map<String, String> mapInfo =new HashMap<String,String>();
			Element element = slaveItemLists.get(i);
			List<Element> elist = element.elements();
			for (int j = 0; j < elist.size(); j++) {
				Element el = elist.get(j);
				
				if(el.getName().equals("equipname")){
					if(!CBSystemConstants.oneClickString.equals("")&&!el.getTextTrim().equals("")){
						List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(CBSystemConstants.oneClickString,el.getTextTrim());
						
						if(rbmList.size()>0){
							if(rbmList.get(0).getPd()!=null){
								mapInfo.put("equipid", rbmList.get(0).getPd().getPowerDeviceID());
							}
						}
					}
				}
				
				//将节点名称与值放入集合
				mapInfo.put(el.getName(), el.getTextTrim());
				
			}
			slaveDevlist.add(mapInfo);
		}
		
		globalSlaveDevList.addAll(slaveDevlist);
		
		PowerDevice pd = null;
		for(Map<String, String> map : mainDevlist) {
			pwRoomName = "";
			pwOperationEquipment = "";
			
			String equipID =  StringUtils.ObjToString(map.get("equipid"));
			String equipname = StringUtils.ObjToString(map.get("equipname"));
			String stationid = StringUtils.ObjToString(map.get("stationid"));
			String stationname = StringUtils.ObjToString(map.get("stationname"));
			String roomname = "";
			
			if(map.containsKey("roomname")){
				roomname = StringUtils.ObjToString(map.get("roomname"));
			}
			
			String pwoperationequipment = StringUtils.ObjToString(map.get("operationequipment"));

			if(!roomname.equals("")){
				pwRoomName = roomname;
			}
			
			if(!pwoperationequipment.equals("")){
				pwOperationEquipment = pwoperationequipment;
			}
			
			if(stationname.equals("") && equipID.equals("") && !equipname.equals("")) {
				List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(equipname);
				equipID = rbmList.get(0).getPd().getPowerDeviceID();
			}else if(equipID.equals("") && !equipname.equals("")) {
				List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(stationname,equipname);
				if(rbmList.size()>0){
					equipID = rbmList.get(0).getPd().getPowerDeviceID();
				}
			}
			
			PowerDevice eq = new PowerDevice();
			
			if(!equipID.equals("")){
				eq = CBSystemConstants.getPowerDevice(equipID);
			}
			
			if(eq.getDeviceType().equals(SystemConstants.InOutLine) && eq.getPowerStationID().equals("")) {
				Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(eq);
				for(PowerDevice ln:stationlines.keySet()){
					eq=ln;
					break;
				}
			}
			CreatePowerStationToplogy.loadFacData(eq.getPowerStationID());
			
			if(!eq.getPowerStationID().equals("")){
				pd=CBSystemConstants.getStationPowerDevices(eq.getPowerStationID()).get(eq.getPowerDeviceID());
				
				System.out.println("------------------加载本侧厂站设备实时状态---------------------");
				InitDeviceStatus ie=new InitDeviceStatus();
				ie.initStatus_EMS(pd.getPowerStationID());
				
				List<PowerDevice> otherXL = new ArrayList<PowerDevice>();
				if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
					System.out.println("------------------获取对侧线路---------------------");
					otherXL =RuleExeUtil.getLineOtherSideList(pd);
				}

				for(PowerDevice xl:otherXL){
					System.out.println("------------------加载对侧厂站设备实时状态---------------------");
					ie.initStatus_EMSToCache(xl.getPowerStationID());
				}
				
				RuleBaseMode rbm = new RuleBaseMode();
				rbm.setPd(pd);
				rbm.setBeginStatus(beginstatus);
				rbm.setEndState(endstatus);
				
				String statecode = "";
				String sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where cardbuildtype='0' and t.opcode='"+CBSystemConstants.opCode+"' and t.devicetypeid='"+pd.getDeviceType()+"' and t.statename = '"+operation+"' and isLock = '0'";
				List<Map<String, Object>> results = DBManager.queryForList(sql);
				Map<String, Object> temp;
				if(results.size()>0) {
					temp=(Map<String, Object>)results.get(0);
					statecode = StringUtils.ObjToString(temp.get("statecode"));
				}
				
				if(statecode.contains(",")){
					String[]  statecodeArr = statecode.split(",");
					
					for(String str : statecodeArr){
						rbm.setStateCode(str);
						
						CardModel cm = new CardModel();
						cm.setCardItems(new ArrayList<CardItemModel>());
						
						CBSystemConstants.setCurRBM(rbm);
						RuleExecute ruleExc=new RuleExecute();
						ruleExc.execute(rbm);
						DeviceOperate doe = new DeviceOperate();
						doe.setTask(cm);

						CardModel cardModel=WordExecute.getInstance().execute(rbm);
						for(int i=0;i<cardModel.getCardItems().size();i++){//清除空指令
							if(cardModel.getCardItems().get(i).getCardDesc().equals("")){
								cardModel.getCardItems().remove(i);
								i--;
							}
						}
						
						cm.getCardItems().addAll(cardModel.getCardItems());
						
						if(!cardModel.getCzrw().equals("") || cardModel.getCardItems().size() > 0) {
							Element rw=datas.addElement("ITEM");
							rw.addElement("czrw").setText(cardModel.getCzrw());
							rw.addElement("mainequipid").setText(CBSystemConstants.getCurRBM().getPd().getPowerDeviceID());
							rw.addElement("mainequipname").setText(CBSystemConstants.getCurRBM().getPd().getPowerDeviceName());
						}
						
						for(int i = 0; i < cm.getCardItems().size();i++) {
							Element item=datas.addElement("ITEM");
							item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
							item.addElement("cardorder").setText(String.valueOf(i+1));
							item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
							String cardDesc=cm.getCardItems().get(i).getCardDesc();
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
							item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
							item.addElement("czdwid").setText(cm.getCardItems().get(i).getCzdwID());
						}
						result = doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
					
					}
				}else{
					rbm.setStateCode(statecode);
					
					CardModel cm = new CardModel();
					cm.setCardItems(new ArrayList<CardItemModel>());
					
					CBSystemConstants.setCurRBM(rbm);
					RuleExecute ruleExc=new RuleExecute();
					ruleExc.execute(rbm);
					DeviceOperate doe = new DeviceOperate();
					doe.setTask(cm);

					CardModel cardModel=WordExecute.getInstance().execute(rbm);
					for(int i=0;i<cardModel.getCardItems().size();i++){//清除空指令
						if(cardModel.getCardItems().get(i).getCardDesc().equals("")){
							cardModel.getCardItems().remove(i);
							i--;
						}
					}
					
					cm.getCardItems().addAll(cardModel.getCardItems());
					
					if(!cardModel.getCzrw().equals("") || cardModel.getCardItems().size() > 0) {
						Element rw=datas.addElement("ITEM");
						rw.addElement("czrw").setText(cardModel.getCzrw());
						rw.addElement("mainequipid").setText(CBSystemConstants.getCurRBM().getPd().getPowerDeviceID());
						rw.addElement("mainequipname").setText(CBSystemConstants.getCurRBM().getPd().getPowerDeviceName());
					}
					
					for(int i = 0; i < cm.getCardItems().size();i++) {
						Element item=datas.addElement("ITEM");
						item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
						String cardDesc=cm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
						item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
						item.addElement("czdwid").setText(cm.getCardItems().get(i).getCzdwID());
					}
					result = doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
				}
			}else{
				result = "";
			}
		}
	} catch (Exception e) {
		e.printStackTrace();
		
	}
	finally{
		if(is!=null){
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
		System.out.println(result);
		return result;
	}

	public static String getXMLcode(String str){
		if(str.toUpperCase().contains("UTF-8")){
			return "UTF-8";
		}else{
			return "GBK";
		}
	}
}
