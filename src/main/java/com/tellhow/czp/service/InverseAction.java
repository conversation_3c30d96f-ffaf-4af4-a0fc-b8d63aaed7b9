package com.tellhow.czp.service;

import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;
import javax.swing.JTabbedPane;

import org.apache.batik.dom.svg.SVGOMGElement;
import org.apache.batik.gvt.GraphicsNode;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.graphicframework.action.impl.ChangeDeviceFlashingAction;
import com.tellhow.graphicframework.action.impl.ChangeDeviceRectFlashingAction;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.CreatePowerStationToplogy;

/**
 * 执行反编译在图形上的动作
 * <AUTHOR>
 *
 */
public class InverseAction {
private	QueryDeviceDao  dao=new QueryDeviceDao();	
/**
 * 打开对应厂站SVG图
 * @param stationname
 */
public void openSvgStation(String stationid){
	//String stationid=dao.getStationID(stationname); 
	CreatePowerStationToplogy.createSVGPanel(stationid);
	//StationSVGPanelManage sspm=new StationSVGPanelManage();
	//sspm.openPanel(stationid);	
}
/**
 * 执行SVG状态
 * @param divlist
 * @param tagetstatus
 * @param isLineCard
 * @param station
 * @param reversecardform 
 */
public void doSvgAction(List<PowerDevice> devList){
	PowerDevice pd=new PowerDevice();
	///如果是线路还需要执行刀闸操作-- 但目前无法解析线路接地刀闸  2011.2.16记 其实可以查询到地刀，然后可通过地刀，线路联合完成相应操作，但目前沿用现有逻辑
	Map temp=new HashMap();
	for(int i=0;i<devList.size();i++){

		pd=(PowerDevice) devList.get(i);
	
		
		
		new ChangeDeviceFlashingAction(pd, "3").execute();
	}	

	}



/**
 * 搜索接地刀闸所连线路
 * @param pd 接地刀闸
 * @return
 */
public PowerDevice getLine(List<PowerDevice> devList) {
	//执行完还需要执行相应地刀操作
	PowerDevice pd = devList.get(0);
	CommonSearch cs = new CommonSearch();
	PowerDevice linedev=null;
	PowerDevice pd2=null;
	Map inPara = new HashMap();
	Map outPara = new HashMap();
	inPara.put("oprSrcDevice",pd);
	inPara.put("tagDevType", SystemConstants.InOutLine);
	inPara.put("isSearchDirectDevice", true);
	cs.execute(inPara, outPara);
	List allDevList = (ArrayList) outPara.get("linkedDeviceList");
	if(allDevList.size()>0){	
			 pd2= (PowerDevice) allDevList.get(0); 			    
	}
	if(pd2!=null)
	 linedev = (PowerDevice) CBSystemConstants.getPowerDevice(pd2.getPowerStationID(), pd2.getPowerDeviceID());// 线路对象
	return linedev;
}

//public void autoToScrollToEquip(PowerDevice pd)
//{
//	
//	SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
//	SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getActivateSVGPanel();
//	SVGCanvas svgCanvas=otsp.getSvgCanvas();
//	
//	Element rootElement = resolver.resolveSvgElement(pd);
//	int count = 0;
//	while(rootElement == null && count < 10) {
//		try {
//			Thread.sleep(1000);
//			rootElement = resolver.resolveSvgElement(pd);
//			count++;
//		} catch (InterruptedException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//	}
//	if(rootElement == null)
//		return;
//	Element groupElement = resolver.getDeviceGroupElement(pd);
//	if(groupElement == null)
//	{
//		//JOptionPane.showMessageDialog(svgCanvas, "接线图上不存在该厂站/设备！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
//		return;
//	}
//	
//	SVGOMGElement gElement = (SVGOMGElement) groupElement;
//
//	// Convert Screen coordinates to Document Coordinates.
////	if(!svgCanvas.isShowing())
////		svgCanvas.setVisible(true);
//	int tagScreenX = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getX() + svgCanvas.getWidth()/2;
//	int tagScreenY = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getY() + svgCanvas.getHeight()/2;
//	GraphicsNode node = svgCanvas.getUpdateManager().getBridgeContext().getGraphicsNode(gElement);
//	  // Rectangle2D ddd = node.getTransformedBounds(node.getTransform());
////	System.out.println(svgCanvas.getViewBoxTransform());
////	System.out.println(gElement);
////	System.out.println(node);
//	Rectangle2D bounds = svgCanvas.getViewBoxTransform().createTransformedShape(node.getBounds()).getBounds();  
//	int srcScreenX= (int)(bounds.getX()+bounds.getWidth()/2)+(int)svgCanvas.getLocationOnScreen().getX();
//	int srcScreenY = (int)(bounds.getY()+bounds.getHeight()/2)+(int)svgCanvas.getLocationOnScreen().getY();
//	int offX = (int)(tagScreenX-srcScreenX);
//	int offY = (int)(tagScreenY-srcScreenY);
//	AffineTransform at = svgCanvas.getRenderingTransform();
//	at.translate(offX/svgCanvas.getRenderingTransform().getScaleX(),  offY/svgCanvas.getRenderingTransform().getScaleY());
//	svgCanvas.setRenderingTransform(at, true);
//}

	public void autoToScrollToEquip(PowerDevice pd){
	
		SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
//		SVGCanvasPanel otsp=(SVGCanvasPanel)SystemConstants.getGuiBuilder().getActivateSVGPanel();
		
		
		JTabbedPane tabbedPane = SystemConstants.getGuiBuilder()
			    .getSVGJTabbedPane();
			  SVGCanvasPanel devpanel =null;
		for (int i = 0; i < tabbedPane.getComponentCount(); i++) {
			SVGCanvasPanel panel = (SVGCanvasPanel) tabbedPane.getComponentAt(i);
			if(panel.getStationID().equals(pd.getPowerStationID())){
				devpanel = panel;
				break;
			}
		}
			  
		SVGCanvasPanel otsp = devpanel==null ? SystemConstants.getGuiBuilder().getActivateSVGPanel() : devpanel;
		
		SVGCanvas svgCanvas=otsp.getSvgCanvas();
	//	SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		Element groupElement = resolver.getDeviceGroupElement(pd);
		final  Document document = svgCanvas.getSVGDocument();
		if(document!=null){
			Element svgg=resolver.resolveSvgElement(document);
			String viewbox=svgg.getAttribute("viewBox");
		}
		
	//	if(groupElement == null)
	//	{
	//		JOptionPane.showMessageDialog(svgCanvas, "接线图上不存在该厂站/设备！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
	//		return;
	//	}
	
		
		
		SVGOMGElement gElement = (SVGOMGElement) groupElement;
	
	
		
		// Convert Screen coordinates to Document Coordinates.
		int tagScreenX = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getX() + svgCanvas.getWidth()/2;
		int tagScreenY = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getY() + svgCanvas.getHeight()/2;
		
		if(gElement!=null){
			if(svgCanvas.getUpdateManager().getBridgeContext().hasGraphicsNodeBridge(gElement)){
				if(svgCanvas.getUpdateManager().getBridgeContext().getGraphicsNode(gElement)!=null){
					GraphicsNode node = svgCanvas.getUpdateManager().getBridgeContext().getGraphicsNode(gElement);
					  // Rectangle2D ddd = node.getTransformedBounds(node.getTransform());
					if(node == null)
						return;
					Rectangle2D bounds = svgCanvas.getViewBoxTransform().createTransformedShape(node.getBounds()).getBounds();  
					int srcScreenX= (int)(bounds.getX()+bounds.getWidth()/2)+(int)svgCanvas.getLocationOnScreen().getX();
					int srcScreenY = (int)(bounds.getY()+bounds.getHeight()/2)+(int)svgCanvas.getLocationOnScreen().getY();
					int offX = (int)(tagScreenX-srcScreenX);
					int offY = (int)(tagScreenY-srcScreenY);
					AffineTransform at = svgCanvas.getRenderingTransform();
					at.translate(offX/svgCanvas.getRenderingTransform().getScaleX(),  offY/svgCanvas.getRenderingTransform().getScaleY());
					svgCanvas.setRenderingTransform(at, true);
					
				    //闪烁效果
					ChangeDeviceRectFlashingAction action = new ChangeDeviceRectFlashingAction(pd, "3");
					action.backexecute();
					action.execute();
				}
			}
		}
	}
}
