package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.StationStartupManager;
import czprule.system.CBSystemConstants;
import tbp.common.config.constants.SysConstants;

public class GetSearchSequence {
	
	/*
	 * 拓扑检索执行方法
	 */
	public static String execute(String arg) {
		
		//初始化参数
		init();
		
		//解析xml输入参数为map类型
		Map<String, String> mapInfo = analysisXml(arg);
		
		//拓扑检索出所需的目标设备类型所有设备参数
		List<Map<String, String>> msglist = getDeviceList(mapInfo);
		
		//检索出的报文内容转换为xml输出
		String result= transferXml(msglist,mapInfo);
		return result;
	}
	
	/**
	 * 初始化参数
	 */
	private static void init(){
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.cardbuildtype = "0";
		CBSystemConstants.roleCode = "0";
		CBSystemConstants.opCode = "0";
		CBSystemConstants.opRuleCode = "0";
		CBSystemConstants.jh_tai = 0;
	}
	
	/*
	 * 解析xml输入参数为map类型
	 */
	private static Map<String, String> analysisXml(String arg){
		System.out.println("**********输入参数**********");
		System.out.println(arg);
		System.out.println("***************************");
		String xmlCode = "";
		if(arg.toUpperCase().contains("UTF-8")){
			xmlCode = "UTF-8";
		}else{
			xmlCode = "GBK";
		}
		/**
		 * 解析传入的xml。
		 * */
		//传入参数数据
		Map<String, String> mapInfo =new HashMap<String,String>();
		mapInfo.put("xmlCode", xmlCode);
		InputStream is =null;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取传入参数数据
			if(root.elements("TYPE")!=null && !root.elements("TYPE").isEmpty()){
				Element TYPE =(Element) root.elements("TYPE").get(0);
				mapInfo.put(TYPE.getName(), TYPE.getTextTrim());
			}
			if(root.elements("stationid")!=null && !root.elements("stationid").isEmpty()){
				Element stationid =(Element) root.elements("stationid").get(0);
				mapInfo.put(stationid.getName(), stationid.getTextTrim());
			}
			if(root.elements("equipid")!=null && !root.elements("equipid").isEmpty()){
				Element equipid =(Element) root.elements("equipid").get(0);
				mapInfo.put(equipid.getName(), equipid.getTextTrim());
			}
			if(root.elements("tagtype")!=null && !root.elements("tagtype").isEmpty()){
				Element tagtype =(Element) root.elements("tagtype").get(0);
				mapInfo.put(tagtype.getName(), tagtype.getTextTrim());
			}
			if(root.elements("isSearchDirectOnly")!=null && !root.elements("isSearchDirectOnly").isEmpty()){
				Element isSearchDirectOnly =(Element) root.elements("isSearchDirectOnly").get(0);
				mapInfo.put(isSearchDirectOnly.getName(), isSearchDirectOnly.getTextTrim());
			}
			if(root.elements("isSearchOff")!=null && !root.elements("isSearchOff").isEmpty()){
				Element isSearchOff =(Element) root.elements("isSearchOff").get(0);
				mapInfo.put(isSearchOff.getName(), isSearchOff.getTextTrim());
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally{
			if(is!=null){
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return mapInfo;
	}
	
	/**
	 * 检索出的报文内容转换为xml输出
	 */
	private static String transferXml(List<Map<String, String>> msglist,Map<String, String> mapInfo){
		String xmlCode = mapInfo.get("xmlCode");
		String type = mapInfo.get("TYPE");
		
		/**
		 * 构造返回的xml。
		 * */
		Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding(xmlCode);
		Element datas=doc.addElement("Datas");
		
		Element typeElement=datas.addElement("TYPE");
		typeElement.setText(type);
		//msglist转换成xml格式输出
		for(int i = 0; i < msglist.size();i++) {
			Element item=datas.addElement("ITEM");
			item.addElement("stationname").setText(msglist.get(i).get("stationname"));
			item.addElement("stationid").setText(msglist.get(i).get("stationid"));
			item.addElement("equipname").setText(msglist.get(i).get("equipname"));
			item.addElement("equipid").setText(msglist.get(i).get("equipid"));
			item.addElement("equiptype").setText(msglist.get(i).get("equiptype"));
			item.addElement("equiptypename").setText(msglist.get(i).get("equiptypename"));
		}
			
		/**
		 * 返回校核结果。
		 * */
		String result= doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
		System.out.println("**********输出结果**********");
		System.out.println(result);
		System.out.println("***************************");
		return result;
	}
	
	/*
	 * 拓扑检索出所需的目标设备类型所有设备参数
	 */
	private static List<Map<String, String>> getDeviceList(Map<String, String> mapInfo){
		String stationid = mapInfo.get("stationid")==null?"":mapInfo.get("stationid");
		String equipid= mapInfo.get("equipid")==null?"":mapInfo.get("equipid");
		String tagType= mapInfo.get("tagtype")==null?"":mapInfo.get("tagtype");
		String isSearchDirectOnly = mapInfo.get("isSearchDirectOnly")==null?"":mapInfo.get("isSearchDirectOnly");
		String isSearchOff = mapInfo.get("isSearchOff")==null?"":mapInfo.get("isSearchOff");
		
		String[] tagTypeArray = tagType.split(",");  //获取目标设备类型，可以有多个，用逗号隔开
		String[] isSearchDirectOnlyArray= isSearchDirectOnly.split(",");  //是否只搜索直接连接设备，可以有多个，用逗号隔开
		String[] isSearchOffArray = isSearchOff.split(",");  //是否搜索断开路径，可以有多个，用逗号隔开
		Boolean isSearchDirectOnlyParam;
		Boolean isSearchOffParam;
		
		StationStartupManager.startup(stationid);
		PowerDevice pd = CBSystemConstants.getPowerDevice(stationid,equipid); //搜索起始设备
		
		//获取设备list
		List<PowerDevice> pds = new ArrayList<PowerDevice>();
		for(int i=0;i<tagTypeArray.length;i++){
			if(pd==null){
				continue;
			}
			if(isSearchDirectOnlyArray.length > i && !isSearchDirectOnlyArray[i].isEmpty()){ 
				isSearchDirectOnlyParam = Boolean.parseBoolean(isSearchDirectOnlyArray[i]);
			} else{
				isSearchDirectOnlyParam = true; //如果没有设置是否只搜索直接连接设备,默认是false
			}
			if(isSearchOffArray.length > i && !isSearchOffArray[i].isEmpty()){ 
				isSearchOffParam = Boolean.parseBoolean(isSearchOffArray[i]);
			} else{
				isSearchOffParam = true; //如果没有设置是否搜索断开路径,默认是false
			}
			//查询符合条件的设备
			pds.addAll(RuleExeUtil.getDeviceList(pd, tagTypeArray[i],SystemConstants.PowerTransformer,
					isSearchOffParam, true, true));
		}
		
		//设备list转为maplist
		List<Map<String, String>> msglist = new ArrayList<Map<String, String>>();
		for(int i = 0; i < pds.size();i++){
			Map<String, String> msgvo =new HashMap<String, String>();
			msgvo.put("stationname", pds.get(i).getPowerStationName());
			msgvo.put("stationid", pds.get(i).getPowerStationID());
			msgvo.put("equipname", pds.get(i).getPowerDeviceName());
			msgvo.put("equipid", pds.get(i).getPowerDeviceID());
			msgvo.put("equiptype", pds.get(i).getDeviceType());
			msgvo.put("equiptypename", SystemConstants.getMapEquipType().get(pds.get(i).getDeviceType()));
			msglist.add(msgvo);
		}
		return msglist;
	}

}
