package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.EMSService;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.runmode.ExecuteDeviceStatus;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.WordExecute;

public class GetRelateDeviceStatus {
	
	public static String execute(String arg) {
		
		
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.cardbuildtype = "0";
		CBSystemConstants.roleCode = "0";
		CBSystemConstants.opCode = "0";
		CBSystemConstants.opRuleCode = "0";
		CBSystemConstants.jh_tai = 0;
		
		CZPService.getService().setArg("");
		StartupManager.startup();
		
		System.out.println("**********输入参数**********");
		System.out.println(arg);
		System.out.println("***************************");
		String xmlCode = "";
		if(arg.toUpperCase().contains("UTF-8")){
			xmlCode = "UTF-8";
		}else{
			xmlCode = "GBK";
		}
		
		/**
		 * 构造返回的xml。
		 * */
		Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding(xmlCode);
		Element datas=doc.addElement("Datas");
		
		/**
		 * 解析传入的xml。
		 * */
		//传入参数数据
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		InputStream is =null;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("ITEM");
			//System.out.println(itemLists);
			for (int i = 0; i <itemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				list.add(mapInfo);
			}
			
			String username = "";
			String area = "";

			List<Element> usernameLists =root.elements("USERNAME");

			if(usernameLists.size()>0){
				username = usernameLists.get(0).getText();
			}
			
			List<Element> areaLists =root.elements("AREA");

			if(areaLists.size()>0){
				area = areaLists.get(0).getText();
			}
			
			for(Map<String, String> map : list){
				String stationid = map.get("stationid");
				String equipid = map.get("equipid");
				String beginstatus = map.get("beginstatus");
				String endstatus = map.get("endstatus");
				
				
				if(CBSystemConstants.getStationPowerDevices(stationid)==null) {
					CreatePowerStationToplogy.loadFacEquip(stationid);
				}
				
				PowerDevice pd = CBSystemConstants.getPowerDevice(stationid,equipid);
				if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
					List<PowerDevice> otherXlList =RuleExeUtil.getLineOtherSideList(pd);
					for(PowerDevice xl:otherXlList){
						if(CBSystemConstants.getStationPowerDevices(xl.getPowerStationID())==null) {
							CreatePowerStationToplogy.loadFacEquip(xl.getPowerStationID());
						}
					}
				}
				
				if(!pd.getDeviceStatus().equals(beginstatus)){
					RuleExeUtil.deviceStatusReset(pd, pd.getDeviceStatus(), beginstatus);
				}
				CBSystemConstants.getDtdMap().clear();
				
				
				RuleExeUtil.deviceStatusChange(pd,beginstatus, endstatus);
				
				Map<PowerDevice, String> devMap = new HashMap<PowerDevice, String>();
				
				Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
				for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();){
					 DispatchTransDevice dtd=(DispatchTransDevice)iterator.next();
					 PowerDevice dev =dtd.getTransDevice();
					 if(devMap.get(dev)==null){
						 devMap.put(dev, dev.getDeviceStatus()); 
					 }
				}
				
				for (Map.Entry<PowerDevice, String> entry : devMap.entrySet()) {
					Element item=datas.addElement("ITEM");
					item.addElement("stationid").setText(entry.getKey().getPowerStationID());
					item.addElement("equipname").setText(entry.getKey().getPowerDeviceName());
					item.addElement("equiptype").setText(entry.getKey().getDeviceType());
					item.addElement("equipid").setText(entry.getKey().getCimID());
					
					String equipstatus = entry.getKey().getDeviceStatus();
					
					if(area.equals("安徽")){
						if(entry.getKey().getDeviceType().equals(SystemConstants.SwitchSeparate)){
							if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
								if((beginstatus.equals("2")||beginstatus.equals("3"))
										&&endstatus.equals("0")){
									String sql = "SELECT RUNMODE FROM EQUIPSTATE.T_S_DISCONNECTOR_YS WHERE EQUIP_ID = '"+entry.getKey().getCimID()+"' AND USERNAME = '"+username+"'";
									
									List<Map<String,String>> runmodelist = DBManager.queryForList(sql);
									
									if(runmodelist.size()>0){
										if(StringUtils.ObjToString(runmodelist.get(0).get("RUNMODE")).equals("1")){
											equipstatus = "0";
										}else if(StringUtils.ObjToString(runmodelist.get(0).get("RUNMODE")).equals("0")){
											equipstatus = "1";
										}
									}
								}
							}
						}
					}
					
					item.addElement("equipstatus").setText(equipstatus);
					String switchstatus = "";
					if((SystemConstants.Switch+","+SystemConstants.SwitchSeparate+","+
							SystemConstants.SwitchFlowGroundLine).contains(entry.getKey().getDeviceType())){
						if(entry.getKey().getDeviceStatus().equals("0")){
							switchstatus="1";
						}else{
							switchstatus="0";
						}
					}
					item.addElement("switchstatus").setText(switchstatus);
				}
					
			}
			
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally{
			if(is!=null){
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		
		/**
		 * 返回校核结果。
		 * */
		System.out.println("**********输出结果**********");
		System.out.println(doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>"));
		System.out.println("***************************");
		return doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
	}
	
	

}
