package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.EMSService;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.runmode.ExecuteDeviceStatus;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.wordcard.WordExecute;

public class GetEMSDeviceStatusByStationid {
	
	public static String execute(String arg) {
		
		
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.cardbuildtype = "0";
		CBSystemConstants.roleCode = "0";
		CBSystemConstants.opCode = "0";
		CBSystemConstants.opRuleCode = "0";
		CBSystemConstants.jh_tai = 0;
		
		CZPService.getService().setArg("");
		StartupManager.startup();
		
		System.out.println("**********输入参数**********");
		System.out.println(arg);
		System.out.println("***************************");
		String xmlCode = "";
		if(arg.toUpperCase().contains("UTF-8")){
			xmlCode = "UTF-8";
		}else{
			xmlCode = "GBK";
		}
		
		/**
		 * 构造返回的xml。
		 * */
		Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding(xmlCode);
		Element datas=doc.addElement("Datas");
		
		/**
		 * 解析传入的xml。
		 * */
		//传入参数数据
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		InputStream is =null;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("ITEM");
			//System.out.println(itemLists);
			for (int i = 0; i <itemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				list.add(mapInfo);
			}
			
			List<Element> stationidLists =root.elements("stationid");
			
			if(stationidLists.size()>0){
				Element stationidEle = stationidLists.get(0);
				if(CBSystemConstants.getStationPowerDevices(stationidEle.getTextTrim())==null) {
					CreatePowerStationToplogy.loadFacEquip(stationidEle.getTextTrim());
				}
				
				Map statusMap = EMSService.getService().getMeasPoint(stationidEle.getTextTrim());
				
				for(Map<String, String> map : list){
					String status = map.get("devstatus");
					if(status.equals("0")){
						status="1";
					}else{
						status="0";
					}
					statusMap.put(map.get("equipid"),status );
				}
				
				
				String equipid="";
				String status="";
				String curstatus="";
				HashMap<PowerDevice,String> deviceStatusMap = new HashMap<PowerDevice,String>();
				for (Iterator iter = statusMap.entrySet().iterator(); iter.hasNext();) {
					Entry enry = (Entry)iter.next();
					if(enry.getValue() == null)
						continue;
					equipid = (String)enry.getKey();
					status = String.valueOf(enry.getValue());
//					if(equipid.equals("114560316091663677")){
//						int asd =1;
//						asd =2;
//					}
					PowerDevice pd = CBSystemConstants.getPowerDevice(stationidEle.getTextTrim(),equipid);
					if(pd == null)
						continue;
					curstatus = pd.getDeviceStatus();
					if(!pd.getDeviceStatus().equals("-1"))
						curstatus = pd.getDeviceStatus().equals("0")?"0":"1";
					pd.setDeviceStatus(status);
					deviceStatusMap.put(pd, status);
				}
				//设备状态构建
				if(deviceStatusMap.size() > 0) {
					ExecuteDeviceStatus.execute(deviceStatusMap, false);
				}
				
				Map<String, PowerDevice> stationDevMap =  CBSystemConstants.getMapPowerStationDevice().get(stationidEle.getTextTrim());
				Element stationidItem=datas.addElement("stationid");
				stationidItem.setText(stationidEle.getTextTrim());
				
				for (Map.Entry<String, PowerDevice> entry : stationDevMap.entrySet()) {
					if(entry.getValue().getDeviceStatus().equals("-1")){
						continue;
					}
					
					Element item=datas.addElement("ITEM");
					item.addElement("equip_id").setText(entry.getValue().getPowerDeviceID());
					item.addElement("equip_name").setText(entry.getValue().getPowerDeviceName());
					item.addElement("cim_id").setText(entry.getValue().getCimID());
					item.addElement("voltage_code").setText(String.valueOf((int) entry.getValue().getPowerVoltGrade()));
					item.addElement("device_status").setText(entry.getValue().getDeviceStatus());
					item.addElement("equip_type").setText(entry.getValue().getDeviceType());
					item.addElement("station_id").setText(entry.getValue().getPowerStationID());
					item.addElement("station_name").setText(entry.getValue().getPowerStationName());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally{
			if(is!=null){
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		
		/**
		 * 返回校核结果。
		 * */
		System.out.println("**********输出结果**********");
		System.out.println(doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>"));
		System.out.println("***************************");
		return doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
	}
	
	

}
