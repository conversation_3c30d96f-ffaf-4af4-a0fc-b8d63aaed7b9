package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;
@SuppressWarnings("unchecked")
public class GetDDTicketYLSXTDImpl {
	public String execute(String arg){
	String result = "";
	/*
	 * 清除全局变量
	 */
	CBSystemConstants.globalMainDevList.clear();
	CBSystemConstants.globalSlaveDevList.clear();
	
	/**
	 * 设置系统运行方式为智能开票类型。
	 * */
	CBSystemConstants.isCurrentSys=false;
	CBSystemConstants.cardbuildtype = "0";
	CBSystemConstants.roleCode = "0";
	CBSystemConstants.opCode = "0";
	CBSystemConstants.opRuleCode = "0";
	CBSystemConstants.jh_tai = 0;
	CBSystemConstants.cardstatus = "0";
	CBSystemConstants.lcm.clear();
	System.out.println("输入参数：{"+arg+"}");
	
	String ylczrw = "";
	String operation = "";
	String endstatus = "";
	String beginstatus = "";
	List<Map<String, String>> ylzlList = new ArrayList<Map<String, String>>();
	String xmlCode = getXMLcode(arg);
	/**
	 * 构造返回的xml。
	 * */
	Document doc=DocumentHelper.createDocument();
	doc.setXMLEncoding(xmlCode);
	Element datas=doc.addElement("Datas");
	
	/**
	 * 解析传入的xml。
	 * */
	//传入参数数据
	List<Map<String, String>> mainDevlist = new ArrayList<Map<String, String>>();
	List<Map<String, String>> slaveDevlist = new ArrayList<Map<String, String>>();
	Map<String, String> operateMap = new HashMap<String, String>();
	InputStream is=null;
	try {
		is = new ByteArrayInputStream(arg.getBytes(xmlCode));
		DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
		DocumentBuilder db = dbf.newDocumentBuilder();
		org.w3c.dom.Document document = db.parse(is);
		DOMReader domReader = new DOMReader();
		Document ret = domReader.read(document);
		Element root = ret.getRootElement();
		
		List<Element> ylContentLists = root.elements("PreparatoryCommandContent");
		List<Element> deviceStatusLists = root.elements("DeviceStatus");

		if(ylContentLists.size() > 0) {
			List<Element> typeLists =  ylContentLists.get(0).elements("TYPE");
			if(typeLists.size()>0){
				String type = typeLists.get(0).getTextTrim();
				
				if(type.equals("预令成票")){
					CBSystemConstants.isztcp = false;
				}
			}
			
			List<Element> czrwLists =  ylContentLists.get(0).elements("CZRW");
			
			if(czrwLists.size()>0){
				ylczrw = czrwLists.get(0).getTextTrim();
			}
			
			List<Element> itemLists =  ylContentLists.get(0).elements("ITEM");
			
			if(itemLists.size()>0){
				for(Element element : itemLists){
					List<Element> elist = element.elements();
					Map<String, String> mapInfo =new HashMap<String,String>();
					for (int j = 0; j < elist.size(); j++) {
						Element el = elist.get(j);
						//将节点名称与值放入集合
						if(el.getName().equals("changzhan")){
							if((!el.getTextTrim().equals("向家坝右岸电厂")
									&&!el.getTextTrim().equals("向家坝左岸电厂")
										&&!el.getTextTrim().equals("溪洛渡左岸电厂")
											&&!el.getTextTrim().equals("溪洛渡右岸电厂"))){
								break;
							}else{
								mapInfo.put(el.getName(), el.getTextTrim());
							}
						}else if(el.getName().equals("caozuozhiling")){
							if(el.getTextTrim().contains("B升压变")){
								mapInfo.put(el.getName(), el.getTextTrim().replace("B升压变", "主变"));
							}else{
								mapInfo.put(el.getName(), el.getTextTrim());
							}
						}else{
							mapInfo.put(el.getName(), el.getTextTrim());
						}
					}
					
					if(!mapInfo.isEmpty()){
						ylzlList.add(mapInfo);
					}
				}
			}
		}
		
		if(checkEcDevName(ylczrw)){
			String curCardItem = "";
			int size = 0;
			int zlxhNum = 1;
			int cardItemNum = 1;
			int cardOrderNum = 1;
			String curDev = "";
			
			for(Iterator<Map<String,String>> itor = ylzlList.iterator();itor.hasNext();){
				Map<String,String> map = itor.next();
				String stationName = StringUtils.ObjToString(map.get("changzhan"));
				String carditem = StringUtils.ObjToString(map.get("carditem"));
				String caozuozhiling = StringUtils.ObjToString(map.get("caozuozhiling")).replace("许可：", "").replace("许可:", "").replace("通知：", "").replace("通知:", "");

				if(stationName.equals("溪洛渡左岸电厂")){
					if(cardOrderNum == 1){
						curDev = getEcDevName(caozuozhiling);
						curCardItem = carditem;
						cardOrderNum++;
						size++;
					}else{
						if(!curCardItem.equals(carditem)&&curDev.equals(getEcDevName(caozuozhiling))){//不同大项的同一设备
							curDev = getEcDevName(caozuozhiling);
							curCardItem = carditem;
							size++;
						}
					}
				}
			}
			
			for(int i=0;i<size;i++){
				/*
				 * 重置下
				 */
				curDev = "";
				zlxhNum = 1;
				cardItemNum = 1;
				cardOrderNum = 1;
				
				List<Map<String,String>> resultList = new ArrayList<Map<String,String>>();
				for(Iterator<Map<String,String>> itor = ylzlList.iterator();itor.hasNext();){//先生成溪洛渡左岸电厂指令
					Map<String,String> map = itor.next();
					String stationName = StringUtils.ObjToString(map.get("changzhan"));
					String cbid = StringUtils.ObjToString(map.get("cbid"));
					String carditem = StringUtils.ObjToString(map.get("carditem"));
					String caozuozhiling = StringUtils.ObjToString(map.get("caozuozhiling")).replace("许可：", "").replace("许可:", "").replace("通知：", "").replace("通知:", "");

					if(stationName.equals("溪洛渡左岸电厂")){
						if(cardOrderNum == 1){
							curDev = getEcDevName(caozuozhiling);
							Map<String,String> tempCzrwMap = new HashMap<String, String>();
							Map<String,String> tempMap = new HashMap<String, String>();
							curCardItem = carditem;
							
							String czrwTemp = ylczrw;
							
							if(czrwTemp.contains("方式调整")){
								czrwTemp = czrwTemp.replace("方式调整", curDev+"方式调整");
							}else if(czrwTemp.contains("投入")&&czrwTemp.contains("解列装置")){
								czrwTemp = "投入"+curDev;
							}else if(czrwTemp.contains("投入")&&czrwTemp.contains("和")){
								czrwTemp = czrwTemp.replace("投入", "投入"+stationName+"500kV");
							}else if(czrwTemp.contains("退出")&&czrwTemp.contains("解列装置")){
								czrwTemp = "退出"+curDev;
							}else if(czrwTemp.contains("退出")&&czrwTemp.contains("和")){
								czrwTemp = czrwTemp.replace("退出", "退出"+stationName+"500kV");
							}
							
							tempCzrwMap.put("czrw", czrwTemp);
							tempMap.put("cardorder", String.valueOf(cardOrderNum));
							tempMap.put("carditem", String.valueOf(cardItemNum));
							tempMap.put("zlxh", String.valueOf(zlxhNum));
							tempMap.put("czdw", String.valueOf(stationName));
							tempMap.put("cznr", String.valueOf(caozuozhiling));
							tempMap.put("cbid", String.valueOf(cbid));
							resultList.add(tempCzrwMap);
							resultList.add(tempMap);
							cardOrderNum++;
							itor.remove();
						}else{
							if(curCardItem.equals(carditem)){//同一大项
								zlxhNum++;
								Map<String,String> tempMap = new HashMap<String, String>();
								tempMap.put("cardorder", String.valueOf(cardOrderNum));
								tempMap.put("carditem", String.valueOf(cardItemNum));
								tempMap.put("zlxh", String.valueOf(zlxhNum));
								tempMap.put("czdw", String.valueOf(stationName));
								tempMap.put("cznr", String.valueOf(caozuozhiling));
								tempMap.put("cbid", String.valueOf(cbid));
								resultList.add(tempMap);
								cardOrderNum++;
								itor.remove();
							}else if(curDev.equals(getEcDevName(caozuozhiling))){//不同大项同一设备
								cardItemNum++;
								Map<String,String> tempMap = new HashMap<String, String>();
								tempMap.put("cardorder", String.valueOf(cardOrderNum));
								tempMap.put("carditem", String.valueOf(cardItemNum));
								tempMap.put("zlxh", String.valueOf(zlxhNum));
								tempMap.put("czdw", String.valueOf(stationName));
								tempMap.put("cznr", String.valueOf(caozuozhiling));
								tempMap.put("cbid", String.valueOf(cbid));
								resultList.add(tempMap);
								cardOrderNum++;
								itor.remove();
							}
						}
					}
				}
				
				if(resultList.size()>0){
					int cardorder = 1;
					Element data = datas.addElement("Data");
					
					for(int k=0;k<resultList.size();k++){
						Map<String,String> dataMap  = resultList.get(k);
						
						if(dataMap.containsKey("czrw")){
							Element rw  = data.addElement("ITEM");
							rw.addElement("czrw").setText(StringUtils.ObjToString(dataMap.get("czrw")));
						}else{
							Element item= data.addElement("ITEM");
							item.addElement("carditem").setText(StringUtils.ObjToString(dataMap.get("carditem")));
							item.addElement("cardorder").setText(String.valueOf(cardorder));
							item.addElement("czdw").setText(StringUtils.ObjToString(dataMap.get("czdw")));
							item.addElement("cznr").setText(StringUtils.ObjToString(dataMap.get("cznr")));
							item.addElement("czsn").setText(StringUtils.ObjToString(dataMap.get("czdw")));
							item.addElement("zlxh").setText(StringUtils.ObjToString(dataMap.get("zlxh")));
							item.addElement("cbid").setText(StringUtils.ObjToString(dataMap.get("cbid")));
							cardorder++;
						}
					}
				}
			}
			
			/*
			 * 重置下
			 */
			curDev = "";
			zlxhNum = 1;
			size = 0;
			cardItemNum = 1;
			cardOrderNum = 1;
			
			if(ylzlList.size()>0){
				for(Iterator<Map<String,String>> itor = ylzlList.iterator();itor.hasNext();){
					Map<String,String> map = itor.next();
					String stationName = StringUtils.ObjToString(map.get("changzhan"));
					String carditem = StringUtils.ObjToString(map.get("carditem"));
					String caozuozhiling = StringUtils.ObjToString(map.get("caozuozhiling")).replace("许可：", "").replace("许可:", "").replace("通知：", "").replace("通知:", "");

					if(!stationName.equals("溪洛渡左岸电厂")){
						if(cardOrderNum == 1){
							curDev = getEcDevName(caozuozhiling);
							curCardItem = carditem;
							cardOrderNum++;
							size++;
						}else{
							if(!curCardItem.equals(carditem)&&curDev.replace("向家坝左岸电厂", "").replace("向家坝右岸电厂", "").equals(getEcDevName(caozuozhiling).replace("向家坝左岸电厂", "").replace("向家坝右岸电厂", ""))){//不同大项同一设备
								curDev = getEcDevName(caozuozhiling);
								curCardItem = carditem;
								size++;
							}
						}
					}
				}
				
				for(int i=0;i<size;i++){
					/*
					 * 重置下
					 */
					curDev = "";
					zlxhNum = 1;
					cardItemNum = 1;
					cardOrderNum = 1;
					
					List<Map<String,String>> resultList = new ArrayList<Map<String,String>>();
					for(Iterator<Map<String,String>> itor = ylzlList.iterator();itor.hasNext();){//先生成溪洛渡左岸电厂指令
						Map<String,String> map = itor.next();
						String stationName = StringUtils.ObjToString(map.get("changzhan"));
						String cbid = StringUtils.ObjToString(map.get("cbid"));
						String carditem = StringUtils.ObjToString(map.get("carditem"));
						String caozuozhiling = StringUtils.ObjToString(map.get("caozuozhiling")).replace("许可：", "").replace("许可:", "").replace("通知：", "").replace("通知:", "");

						if(cardOrderNum == 1){
							curDev = getEcDevName(caozuozhiling);
							caozuozhiling = getProtectNameXl(caozuozhiling);
							Map<String,String> tempCzrwMap = new HashMap<String, String>();
							Map<String,String> tempMap = new HashMap<String, String>();
							curCardItem = carditem;
							
							String czrwTemp = ylczrw;
							
							if(czrwTemp.contains("方式调整")){
								czrwTemp = czrwTemp.replace("方式调整", curDev+"方式调整");
							}else if(czrwTemp.contains("投入")&&czrwTemp.contains("解列装置")){
								czrwTemp = "投入"+curDev;
							}else if(czrwTemp.contains("投入")&&czrwTemp.contains("和")){
								czrwTemp = czrwTemp.replace("投入", "投入"+stationName+"500kV");
							}else if(czrwTemp.contains("退出")&&czrwTemp.contains("解列装置")){
								czrwTemp = "退出"+curDev;
							}else if(czrwTemp.contains("退出")&&czrwTemp.contains("和")){
								czrwTemp = czrwTemp.replace("退出", "退出"+stationName+"500kV");
							}
							
							String stationName2 = "";
							
							for(Map<String,String> map2 : ylzlList){
								if(stationName2.equals("")){
									stationName2 = StringUtils.ObjToString(map2.get("changzhan"));
								}else if(!stationName2.equals(StringUtils.ObjToString(map2.get("changzhan")))){
									czrwTemp = czrwTemp.replace("向家坝右岸电厂", "向家坝电厂").replace("向家坝左岸电厂", "向家坝电厂");
								}
							}
							
							tempCzrwMap.put("czrw", czrwTemp);
							tempMap.put("cardorder", String.valueOf(cardOrderNum));
							tempMap.put("carditem", String.valueOf(cardItemNum));
							tempMap.put("zlxh", String.valueOf(zlxhNum));
							tempMap.put("czdw", String.valueOf(stationName));
							tempMap.put("cznr", String.valueOf(caozuozhiling));
							tempMap.put("cbid", String.valueOf(cbid));
							resultList.add(tempCzrwMap);
							resultList.add(tempMap);
							cardOrderNum++;
							itor.remove();
						}else{
							caozuozhiling = getProtectNameXl(caozuozhiling);
							if(curCardItem.equals(carditem)){//同一大项
								zlxhNum++;
								Map<String,String> tempMap = new HashMap<String, String>();
								tempMap.put("cardorder", String.valueOf(cardOrderNum));
								tempMap.put("carditem", String.valueOf(cardItemNum));
								tempMap.put("zlxh", String.valueOf(zlxhNum));
								tempMap.put("czdw", String.valueOf(stationName));
								tempMap.put("cznr", String.valueOf(caozuozhiling));
								tempMap.put("cbid", String.valueOf(cbid));
								resultList.add(tempMap);
								cardOrderNum++;
								itor.remove();
							}else if(curDev.replace("向家坝左岸电厂", "").replace("向家坝右岸电厂", "").equals(getEcDevName(caozuozhiling).replace("向家坝左岸电厂", "").replace("向家坝右岸电厂", ""))){//不同大项同一设备
								cardItemNum++;
								Map<String,String> tempMap = new HashMap<String, String>();
								tempMap.put("cardorder", String.valueOf(cardOrderNum));
								tempMap.put("carditem", String.valueOf(cardItemNum));
								tempMap.put("zlxh", String.valueOf(zlxhNum));
								tempMap.put("czdw", String.valueOf(stationName));
								tempMap.put("cznr", String.valueOf(caozuozhiling));
								tempMap.put("cbid", String.valueOf(cbid));
								resultList.add(tempMap);
								cardOrderNum++;
								itor.remove();
							}
						}
					}
					
					if(resultList.size()>0){
						Element data = datas.addElement("Data");
						int cardorder = 1;
						for(int k=0;k<resultList.size();k++){
							Map<String,String> dataMap  = resultList.get(k);
							
							if(dataMap.containsKey("czrw")){
								Element rw  = data.addElement("ITEM");
								rw.addElement("czrw").setText(StringUtils.ObjToString(dataMap.get("czrw")));
							}else{
								Element item= data.addElement("ITEM");
								item.addElement("carditem").setText(StringUtils.ObjToString(dataMap.get("carditem")));
								item.addElement("cardorder").setText(StringUtils.ObjToString(cardorder));
								item.addElement("czdw").setText(StringUtils.ObjToString(dataMap.get("czdw")));
								item.addElement("cznr").setText(StringUtils.ObjToString(dataMap.get("cznr")));
								item.addElement("czsn").setText(StringUtils.ObjToString(dataMap.get("czdw")));
								item.addElement("zlxh").setText(StringUtils.ObjToString(dataMap.get("zlxh")));
								item.addElement("cbid").setText(StringUtils.ObjToString(dataMap.get("cbid")));
								cardorder++;
							}
						}
					}
				}
			}
			
			result = doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>").replace("</Data><Data>", "</Data>\n<Data>");
			System.out.println("二次设备成票结果："+result);
			return result;
		}
		
		if(deviceStatusLists.size() > 0){
			List<Element> qymbLists =deviceStatusLists.get(0).elements("areano");
			if(qymbLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				CBSystemConstants.qybm = qymbLists.get(0).getTextTrim();
				CBSystemConstants.unitCode = qymbLists.get(0).getTextTrim();
			}
			
			CZPService.getService().setArg("");
			StartupManager.startup();
			
			if(qymbLists.size() > 0) {
				CBSystemConstants.opCode = QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode);
			    if(CBSystemConstants.opCode.equals(""))
			    	CBSystemConstants.opCode = QueryDeviceDao.getOpcode("0", CBSystemConstants.roleCode);
			    CBSystemConstants.opRuleCode = CBSystemConstants.opCode;
			}
			
			
			
			
			List<Element> roleLists =deviceStatusLists.get(0).elements("role");
			if(roleLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				CBSystemConstants.roleCode = roleLists.get(0).getTextTrim();
			}
			
			List<Element> operationLists =deviceStatusLists.get(0).elements("operation");
			if(operationLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				operation = operationLists.get(0).getTextTrim();
				if(operation.equals("运行")){
					endstatus = "0";
				}else if(operation.equals("检修")){
					endstatus = "3";
				}else if(operation.equals("冷备用")){
					endstatus = "2";
				}else if(operation.equals("热备用")){
					endstatus = "1";
				}
				
				operateMap.put("endstatus", endstatus);
			}
			
			List<Element> firstsstationLists = deviceStatusLists.get(0).elements("firststation");
			if(firstsstationLists.size() > 0) {
				CBSystemConstants.oneClickString = firstsstationLists.get(0).getTextTrim();
			}
			
			List<Element> beginstatusLists =deviceStatusLists.get(0).elements("beginstatus");
			if(beginstatusLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				beginstatus = beginstatusLists.get(0).getTextTrim();
			}
			
			//获取ITEM节点DOM
			List<Element> mainItemLists =deviceStatusLists.get(0).elements("mainequip");
			//System.out.println(itemLists);
			for (int i = 0; i <mainItemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = mainItemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());	
				}
				mapInfo.put("endstatus", getNotNullString(operateMap.get("endstatus")));
				mainDevlist.add(mapInfo);
			}
			
			CBSystemConstants.globalMainDevList.addAll(mainDevlist);
			
			//获取ITEM节点DOM
			List<Element> slaveItemLists =deviceStatusLists.get(0).elements("slaveequip");
			//System.out.println(itemLists);
			for (int i = 0; i <slaveItemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = slaveItemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				slaveDevlist.add(mapInfo);
			}
			
			CBSystemConstants.globalSlaveDevList.addAll(slaveDevlist);
			
		}
		
		
		PowerDevice pd = null;
		if(mainDevlist.size()>0){
			for(Map<String, String> map : mainDevlist) {
				String equipID = getNotNullString(map.get("equipid"));
				String equipname = getNotNullString(map.get("equipname"));
				String stationid = getNotNullString(map.get("stationid"));
				String stationname = getNotNullString(map.get("stationname"));
				
				String sql = "select station_id as stationid from "+CBSystemConstants.equipUser+"T_SUBSTATION where station_name = '溪洛渡右岸电厂'";
				List<Map<String, Object>> results = DBManager.queryForList(sql);
				if(results.size()>0) {
					String stid = results.get(0).get("STATIONID")==null?"":results.get(0).get("STATIONID").toString();
					if(!stationid.equals(stid)){
						CBSystemConstants.cardtype = "1";
					}else{
						CBSystemConstants.cardtype = "0";
					}
				}
				
				if(stationname.equals("") && equipID.equals("") && !equipname.equals("")) {
					List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(equipname);
					equipID = rbmList.get(0).getPd().getPowerDeviceID();
					CBSystemConstants.setCurRBM(rbmList.get(0));
				}
				else if(equipID.equals("") && !equipname.equals("")) {
					List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(stationname,equipname);
					equipID = rbmList.get(0).getPd().getPowerDeviceID();
					CBSystemConstants.setCurRBM(rbmList.get(0));
				}
				
				PowerDevice eq = new PowerDevice();
				
				if(!equipID.equals("")){
					eq = CBSystemConstants.getPowerDevice(equipID);
				}
				
				if(eq.getDeviceType().equals(SystemConstants.InOutLine) && eq.getPowerStationID().equals("")) {
					Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(eq);
					for(PowerDevice ln:stationlines.keySet()){
						eq=ln;
						break;
					}
				}
				
				CreatePowerStationToplogy.loadFacData(eq.getPowerStationID());
				pd=CBSystemConstants.getStationPowerDevices(eq.getPowerStationID()).get(eq.getPowerDeviceID());
				
				RuleBaseMode curRbm =  CBSystemConstants.getCurRBM();
				PowerDevice curMainDev = curRbm.getPd();
				if(curMainDev==null){
					List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();
					RuleBaseMode rbm = new RuleBaseMode();
					rbm.setPd(pd);
					rbmList.add(rbm);
					CBSystemConstants.setCurRBM(rbmList.get(0));
				}
				
				curRbm.setBeginStatus(beginstatus);
				curRbm.setEndState(endstatus);
			}
		}
		
		List<Map<String, String>> ylzlListAdd = new ArrayList<Map<String, String>>();
		
		for(Iterator<Map<String,String>> itor = ylzlList.iterator();itor.hasNext();){
			Map<String,String> map = itor.next();
			String czzl = StringUtils.ObjToString(map.get("caozuozhiling"));
			String changzhan  = StringUtils.ObjToString(map.get("changzhan"));
			
			if(czzl.contains("开关运行")){
				String requirement = "开关运行";
				List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(changzhan, czzl);
				
				if(rbmList.get(0).getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
					
					String param = getZtcpParamsInPowerTransformer(rbmList,requirement);
					
					GetDDTicketZTImpl ztimpl = new GetDDTicketZTImpl();
					String results = ztimpl.execute(param);
					List<Map<String,String>> list = getReqInfo(results);
					
					if(list.size()>0){
						for(Map<String,String> msg:list){
							Map<String,String> msgNew = new HashMap<String, String>();
							if(!msg.containsKey("czrw")){
								msgNew.put("cbid", StringUtils.ObjToString(map.get("cbid")));
								msgNew.put("carditem", StringUtils.ObjToString(map.get("carditem")));
								msgNew.put("zlxh", StringUtils.ObjToString(map.get("zlxh")));
								msgNew.put("caozuozhiling", StringUtils.ObjToString(msg.get("cznr")));
								msgNew.put("changzhan", StringUtils.ObjToString(msg.get("czdw")));
								ylzlListAdd.add(msgNew);
							}
						}
					}
					itor.remove();
				}
			}
		}
		
		ylzlList.addAll(ylzlListAdd);
		
		/*
		 * 在这里加载厂站的各种数据
		 */
		InitDeviceStatus ie = new InitDeviceStatus();
		ie.initStatus_EMSToCache(pd.getPowerStationID());
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			List<PowerDevice> list = RuleExeUtil.getLineOtherSideList(pd);
			
			if(list.size()>0){
				for(PowerDevice dev:list){
					ie.initStatus_EMSToCache(dev.getPowerStationID());
				}
			}
		}
		
		for(Map<String, String> slavemap : slaveDevlist) {
			String equipID = getNotNullString(slavemap.get("equipid"));
			String equipname = getNotNullString(slavemap.get("equipname"));
			
			if(equipID.equals("") && !equipname.equals("")) {
				List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(CBSystemConstants.oneClickString,equipname);
				equipID = rbmList.get(0).getPd().getPowerDeviceID();
				slavemap.put("equipid", equipID);
			}
		}
		
		System.out.println(pd);
		
		if(pd.getDeviceType().equals(SystemConstants.MotherLine)){
			if(beginstatus.equals("0")&&endstatus.equals("3")){
				beginstatus = "2";
				operation = "检修";
				
				if(CBSystemConstants.globalSlaveDevList.size()>0){
					for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
						Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
						map.put("beginstatus","2");	
						map.put("endstatus","3");
					}
				}
			}else if(beginstatus.equals("3")&&endstatus.equals("0")){
				endstatus = "2";
				operation = "冷备用";
				
				if(CBSystemConstants.globalSlaveDevList.size()>0){
					for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
						Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
						map.put("beginstatus","3");	
						map.put("endstatus","2");	
					}
				}
			}
		}else if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){
			if(kgjx()){
				if(beginstatus.equals("0")&&endstatus.equals("3")){
					beginstatus = "2";
					operation = "检修";
					
					if(CBSystemConstants.globalSlaveDevList.size()>0){
						for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
							Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
							map.put("beginstatus","2");	
							map.put("endstatus","3");
						}
					}
				}else if(endstatus.equals("0")&&beginstatus.equals("3")){
					endstatus = "2";
					operation = "冷备用";
					if(CBSystemConstants.globalSlaveDevList.size()>0){
						for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
							Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
							map.put("beginstatus","3");	
							map.put("endstatus","2");	
						}
					}
				}
			}
		}
		
		RuleBaseMode rbm = new RuleBaseMode();
		rbm.setPd(pd);
		rbm.setBeginStatus(beginstatus);
		rbm.setEndState(endstatus);
		String statecode = "";
		
		String sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where cardbuildtype='"+CBSystemConstants.cardbuildtype+"' and t.opcode='"+CBSystemConstants.opCode+"' and t.devicetypeid='"+pd.getDeviceType()+"' and t.statename = '"+operation+"' and isLock = '0'";
		List<Map<String, Object>> results = DBManager.queryForList(sql);
		if(results.size()>0) {
			Map<String, Object> temp=(Map<String, Object>)results.get(0);
			statecode = StringUtils.ObjToString(temp.get("statecode"));
		}
		rbm.setStateCode(statecode);
		
		CardModel cm = new CardModel();
		cm.setCardItems(new ArrayList<CardItemModel>());
		
		if(!rbm.getPd().getDeviceStatus().equals(rbm.getBeginStatus())) {
			System.out.println("------------------开始执行状态重置规则---------------------");
			System.out.println("设备现状态为：{"+rbm.getPd().getDeviceStatus()+"}");
			System.out.println("设备重置后的状态为：{"+rbm.getBeginStatus()+"}");
			RuleExeUtil.deviceStatusChange(rbm.getPd(),rbm.getPd().getDeviceStatus(),rbm.getBeginStatus());
		}
		
		if(CBSystemConstants.globalSlaveDevList.size()>0){
			for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
				Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
				PowerDevice dev = CBSystemConstants.getPowerDevice(StringUtils.ObjToString(map.get("equipid")));
				String begin = StringUtils.ObjToString(map.get("beginstatus"));
				
				if(!begin.equals(dev.getDeviceStatus())){
					RuleExeUtil.deviceStatusChange(dev,dev.getDeviceStatus(),begin);
				}
			}
		}
		
		DeviceOperate.getAlltransDevMap().clear();
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.LineTagStatus.clear();
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			List<PowerDevice> allline = RuleExeUtil.getLineAllSideList(pd);
			
			for(PowerDevice line:allline){
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(line.getPowerStationID());
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev =  it2.next();
					if ((dev.getDeviceType().equals(SystemConstants.Switch)||dev.getDeviceType().equals(SystemConstants.InOutLine)||dev.getDeviceType().equals(SystemConstants.MotherLine)||dev.getDeviceType().equals(SystemConstants.PowerTransformer)||dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) 
							&& dev.getPowerVoltGrade()==pd.getPowerVoltGrade()) {
						System.out.print("厂站:"+dev.getPowerStationName()+"|设备:"+dev+"|状态:"+dev.getDeviceStatus()+"\n");
					}
				}
			}
		}else{
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev =  it2.next();
				if ((dev.getDeviceType().equals(SystemConstants.Switch)||dev.getDeviceType().equals(SystemConstants.InOutLine)||dev.getDeviceType().equals(SystemConstants.MotherLine)||dev.getDeviceType().equals(SystemConstants.PowerTransformer)||dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) 
						&& dev.getPowerVoltGrade()==pd.getPowerVoltGrade()) {
					System.out.print("厂站:"+dev.getPowerStationName()+"|设备:"+dev+"|状态:"+dev.getDeviceStatus()+"\n");
				}
			}
		}
		
		CBSystemConstants.setCurRBM(rbm);
		RuleExecute ruleExc=new RuleExecute();
		ruleExc.execute(rbm);
		
		DeviceOperate doe = new DeviceOperate();
		doe.setTask(cm);

		CardModel cardModel=WordExecute.getInstance().execute(rbm);
		for(int i=0;i<cardModel.getCardItems().size();i++){//清除空指令
			if(cardModel.getCardItems().get(i).getCardDesc().equals("")){
				cardModel.getCardItems().remove(i);
				i--;
			}
		}
		cm.getCardItems().addAll(cardModel.getCardItems());
		
		Element data = datas.addElement("Data");
		
		if(!cardModel.getCzrw().equals("") || cardModel.getCardItems().size() > 0) {
			Element rw=data.addElement("ITEM");
			rw.addElement("czrw").setText(cardModel.getCzrw());
		}
		
		CardModel ylcm = new CardModel();
		ylcm.setCardItems(new ArrayList<CardItemModel>());
		
		ylcm = setRelate(ylzlList);
		
		if(CBSystemConstants.cardtype.equals("1")){
			if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
				if(Integer.valueOf(CBSystemConstants.getCurRBM().getBeginStatus())>Integer.valueOf(CBSystemConstants.getCurRBM().getEndState())){//复电
					setCarditem(ylcm,cm.getCardItems().get(cm.getCardItems().size()-1).getCardItem());
					setZlxh(cm);

					for(int i = 0; i < cm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
						String cardDesc=cm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
					}
					
					int cardordernew = cm.getCardItems().size()+1;
					for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(cardordernew));
						item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
						String cardDesc=ylcm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
						item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
						cardordernew++;
					}
				}else{
					setCarditem(ylcm,"0");
					setZlxh(ylcm);

					for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
						String cardDesc=ylcm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
						item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
					}
					
					int cardordernew = ylcm.getCardItems().size()+1;
					setCarditem(cm,ylcm.getCardItems().get(ylcm.getCardItems().size()-1).getCardItem());
					setZlxh(cm);
					for(int i = 0; i < cm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(cardordernew));
						item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
						String cardDesc=cm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
						cardordernew++;
					}
				}
			}else if(pd.getDeviceType().equals(SystemConstants.MotherLine)){
				if(beginstatus.equals("2")&&endstatus.equals("3")&&kgjx()){//运行到检修
					setZlxh(cm);
					for(int i = 0; i < cm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
						String cardDesc=cm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
					}
					
					data = datas.addElement("Data");
					Element itemczrw=data.addElement("ITEM");
					itemczrw.addElement("czrw").setText(ylczrw);
					
					for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
						String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
						item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
					}
				}else if(beginstatus.equals("3")&&endstatus.equals("2")&&kgjx()){//检修到运行
					setZlxh(cm);
					for(int i = 0; i < cm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
						String cardDesc=cm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
					}
					
					data = datas.addElement("Data");
					Element itemczrw=data.addElement("ITEM");
					itemczrw.addElement("czrw").setText(ylczrw);
					
					for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
						String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
						item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
					}
				}else{
					if(beginstatus.equals("2")&&endstatus.equals("0")){
						setCarditem(ylcm,"0");
						setZlxh(ylcm);
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(i+1));
							item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
							String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
							item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
						}
					}else if(beginstatus.equals("0")&&endstatus.equals("2")){
						setCarditem(ylcm,"0");
						setZlxh(ylcm);
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(i+1));
							item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
							String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
							item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
						}
					}else if(beginstatus.equals("2")&&endstatus.equals("3")){
						setCarditem(ylcm,"0");
						setZlxh(ylcm);
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(i+1));
							item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
							String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
							item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
						}
						
						int cardordernew = ylcm.getCardItems().size()+1;
						setCarditem(cm,ylcm.getCardItems().get(ylcm.getCardItems().size()-1).getCardItem());
						setZlxh(cm);
						for(int i = 0; i < cm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(cardordernew));
							item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
							String cardDesc=cm.getCardItems().get(i).getCardDesc();
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
							cardordernew++;
						}
					}else{
						setCarditem(ylcm,cm.getCardItems().get(cm.getCardItems().size()-1).getCardItem());
						setZlxh(cm);
						for(int i = 0; i < cm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(i+1));
							item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
							String cardDesc=cm.getCardItems().get(i).getCardDesc();
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
						}
						
						int cardordernew = cm.getCardItems().size()+1;
						
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(cardordernew));
							item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
							String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
							item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
							cardordernew++;
						}
					}
				}
			}else if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){
				if(beginstatus.equals("2")&&endstatus.equals("0")){
					setCarditem(ylcm,"0");
					setZlxh(ylcm);
					for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
						String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
						item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
					}
				}else if(beginstatus.equals("0")&&endstatus.equals("2")){
					setCarditem(ylcm,"0");
					setZlxh(ylcm);
					for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
						String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
						item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
					}
				}else if(beginstatus.equals("2")&&endstatus.equals("3")&&kgjx()){//运行到检修
					setZlxh(cm);
					for(int i = 0; i < cm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
						String cardDesc=cm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
					}
					
					data = datas.addElement("Data");
					Element itemczrw=data.addElement("ITEM");
					itemczrw.addElement("czrw").setText(ylczrw);
					
					for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
						String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
						item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
					}
				}else if(beginstatus.equals("3")&&endstatus.equals("2")&&kgjx()){//检修到运行
					setZlxh(cm);
					for(int i = 0; i < cm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
						String cardDesc=cm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
					}
					
					data = datas.addElement("Data");
					Element itemczrw=data.addElement("ITEM");
					itemczrw.addElement("czrw").setText(ylczrw);
					
					for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
						item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
						item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
						String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
						item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
					}
				}else{
					if(beginstatus.equals("0")&&endstatus.equals("3")){
						setCarditem(ylcm,"0");
						setZlxh(ylcm);
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(i+1));
							item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
							String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
							item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
						}
						
						int cardordernew = ylcm.getCardItems().size()+1;
						setCarditem(cm,ylcm.getCardItems().get(ylcm.getCardItems().size()-1).getCardItem());
						setZlxh(cm);
						for(int i = 0; i < cm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(cardordernew));
							item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
							String cardDesc=cm.getCardItems().get(i).getCardDesc();
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
							cardordernew++;
						}
					}else{
						setCarditem(ylcm,cm.getCardItems().get(cm.getCardItems().size()-1).getCardItem());
						setZlxh(cm);
						for(int i = 0; i < cm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(i+1));
							item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
							String cardDesc=cm.getCardItems().get(i).getCardDesc();
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
						}
						
						int cardordernew = cm.getCardItems().size()+1;
						
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(cardordernew));
							item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
							String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace("B升压", "主").replace("500kV侧", "");
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
							item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
							cardordernew++;
						}
					}
				}
			}else if(pd.getDeviceType().equals(SystemConstants.Switch)){
				int num = 0;
				int sizeAdd = 0;
				CardItemModel ylcmAdd = new CardItemModel();
				List<CardItemModel> cardItems = new ArrayList<CardItemModel>();
				
				for(Iterator<CardItemModel> itor = cm.getCardItems().iterator();itor.hasNext();){
					CardItemModel cim = itor.next();
					
					if(cim.getCardDesc().contains("核：")){
						num = 1;
						sizeAdd = 1;
						ylcmAdd = cim;
						cardItems.add(ylcmAdd);
						Element item=data.addElement("ITEM");
						item.addElement("cardorder").setText("1");
						item.addElement("carditem").setText("1");
						item.addElement("zlxh").setText("1");
						item.addElement("czdw").setText(cim.getStationName());
						String cardDesc=cim.getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(cim.getShowName());
						itor.remove();
					}
				}
				
				if(Integer.valueOf(CBSystemConstants.getCurRBM().getBeginStatus())>Integer.valueOf(CBSystemConstants.getCurRBM().getEndState())){//复电
					if(endstatus.equals("0")&&beginstatus.equals("2")){
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(i+1));
							item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
							String cardDesc=ylcm.getCardItems().get(i).getCardDesc();
							item.addElement("cznr").setText(cardDesc.replace(ylcm.getCardItems().get(i).getStationName(), ylcm.getCardItems().get(i).getStationName()+"500kV "));
							item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
							item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
						}
					}else{
						setCarditem(ylcm,cm.getCardItems().get(cm.getCardItems().size()-1).getCardItem());
						setZlxh(cm);
						for(int i = 0; i < cm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(num+1));
							item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
							String cardDesc=cm.getCardItems().get(i).getCardDesc();
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
							num++;
						}
						
						int cardordernew = cm.getCardItems().size()+1+sizeAdd;
						
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(cardordernew));
							item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
							String cardDesc=ylcm.getCardItems().get(i).getCardDesc();
							item.addElement("cznr").setText(cardDesc.replace(ylcm.getCardItems().get(i).getStationName(), ylcm.getCardItems().get(i).getStationName()+"500kV "));
							item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
							item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
							cardordernew++;
						}
					}
				}else{
					if(endstatus.equals("2")&&beginstatus.equals("0")){
						cardItems.addAll(ylcm.getCardItems());
						ylcm.setCardItems(cardItems);
						
						setCarditem(ylcm,"0");
						setZlxh(ylcm);
						
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							if(!ylcm.getCardItems().get(i).getCardDesc().contains("核：")){
								Element item=data.addElement("ITEM");
								item.addElement("cardorder").setText(String.valueOf(i+1));
								item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
								item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
								item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
								String cardDesc=ylcm.getCardItems().get(i).getCardDesc();
								item.addElement("cznr").setText(cardDesc.replace(ylcm.getCardItems().get(i).getStationName(), ylcm.getCardItems().get(i).getStationName()+"500kV "));
								item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
								item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
							}
						}
					}else{
						setCarditem(ylcm,"0");
						setZlxh(ylcm);
						
						for(int i = 0 ; i < ylcm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(num+1));
							item.addElement("carditem").setText(ylcm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(ylcm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(ylcm.getCardItems().get(i).getStationName());
							String cardDesc=ylcm.getCardItems().get(i).getCardDesc().replace(ylcm.getCardItems().get(i).getStationName(), ylcm.getCardItems().get(i).getStationName()+"500kV ");
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(ylcm.getCardItems().get(i).getShowName());
							item.addElement("cbid").setText(ylcm.getCardItems().get(i).getUuIds());
							num++;
						}
						
						int cardordernew = ylcm.getCardItems().size()+1+sizeAdd;
						setCarditem(cm,ylcm.getCardItems().get(ylcm.getCardItems().size()-1).getCardItem());
						setZlxh(cm);
						
						for(int i = 0; i < cm.getCardItems().size();i++) {
							Element item=data.addElement("ITEM");
							item.addElement("cardorder").setText(String.valueOf(cardordernew));
							item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
							item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
							item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
							String cardDesc=cm.getCardItems().get(i).getCardDesc();
							item.addElement("cznr").setText(cardDesc);
							item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
							cardordernew++;
						}
					}
				}
			}
		}else{
			if(pd.getDeviceType().equals(SystemConstants.InOutLine)
					||pd.getDeviceType().equals(SystemConstants.PowerTransformer)
						||pd.getDeviceType().equals(SystemConstants.MotherLine)){
				setZlxh(cm);

				for(int i = 0; i < cm.getCardItems().size();i++) {
					Element item=data.addElement("ITEM");
					String cardDesc=cm.getCardItems().get(i).getCardDesc();
					List<RuleBaseMode> rbmList = CZPService.getService().getRBMList("溪洛渡右岸电厂", cardDesc);

					if(rbmList.get(0).getPd()!=null){
						for(int j = 0 ; j < ylcm.getCardItems().size();j++) {
							String ylCardDesc=ylcm.getCardItems().get(j).getCardDesc();
							String uuid=ylcm.getCardItems().get(j).getUuIds();

							List<RuleBaseMode> ylRbmList = CZPService.getService().getRBMList("溪洛渡右岸电厂", ylCardDesc);

							if(ylRbmList.get(0).getPd()!=null){
								for(RuleBaseMode rulebasemode : rbmList){
									if(rulebasemode.getPd().equals(ylRbmList.get(0).getPd())){
										if(rulebasemode.getBeginStatus().equals(rulebasemode.getEndState())){
											if(rulebasemode.getEndState().equals(ylRbmList.get(0).getEndState())){
												item.addElement("cbid").setText(uuid);
												break;
											}
										}else{
											if(rulebasemode.getBeginStatus().equals(ylRbmList.get(0).getBeginStatus())
													&&rulebasemode.getEndState().equals(ylRbmList.get(0).getEndState())){
												item.addElement("cbid").setText(uuid);
												break;
											}
										}
									}
								}
							}
						}
					}
					
					item.addElement("cardorder").setText(String.valueOf(i+1));
					item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
					item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
					item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
					item.addElement("cznr").setText(cardDesc);
					item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
				}
			}else if(pd.getDeviceType().equals(SystemConstants.Switch)){
				setZlxh(cm);

				for(int i = 0; i < cm.getCardItems().size();i++) {
					Element item=data.addElement("ITEM");
					String cardDesc=cm.getCardItems().get(i).getCardDesc();

					for(int j = 0 ; j < ylcm.getCardItems().size();j++) {
						String uuid=ylcm.getCardItems().get(j).getUuIds();
						item.addElement("cbid").setText(uuid);
					}
					
					item.addElement("cardorder").setText(String.valueOf(i+1));
					item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
					item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
					item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
					item.addElement("cznr").setText(cardDesc);
					item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
				}
			}
		}
		
		result = doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>").replace("</Data><Data>", "</Data>\n<Data>");
		System.out.println(result);
		
		
	} catch (Exception e) {
		e.printStackTrace();
		return "<?xml version=\"1.0\" encoding=\""+xmlCode+"\"?><Datas><Data><ITEM><result>预令成票失败</result></ITEM></Data></Datas>";
	}
	finally{
		if(is!=null){
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
		return result;
	}

	public String getZtcpParamsInPowerTransformer(List<RuleBaseMode> rbmlist,String requirement){
		String returnxml = "";
		if(rbmlist.size()>0){
			PowerDevice curDev = rbmlist.get(0).getPd();
			List<PowerDevice> pdList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			
			for(Iterator<PowerDevice> it = pdList.iterator();it.hasNext();){
				PowerDevice pd = it.next();
				
				if(pd.getPowerVoltGrade() == 20){
					it.remove();
				}
			}
			
			returnxml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Datas><type></type><areano></areano><role>0</role>"
					+ "<beginstatus>"+rbmlist.get(0).getBeginStatus()+"</beginstatus><operation>"+RuleExeUtil.getStatus(rbmlist.get(0).getEndState())+"</operation>"
					+ "<firststation>"+curDev.getPowerStationName()+"</firststation><mainequip><stationid>"+curDev.getPowerStationID()+"</stationid>"
					+ "<stationname>"+curDev.getPowerStationName()+"</stationname><equipid>"+curDev.getPowerDeviceID()+"</equipid><equipname>"+curDev.getPowerDeviceName()+"</equipname></mainequip>";
			
			for(int k=0;k<pdList.size();k++){
				PowerDevice dev = pdList.get(k);
				String devStartStatus = "";
				String devEndStatus = "";
				
				if(requirement.equals("开关检修")){
					devStartStatus = "0";
					devEndStatus = "3";
				}else if(requirement.equals("开关运行")){
					devStartStatus = "0";
					devEndStatus = "0";
				}else{
					devStartStatus = rbmlist.get(0).getBeginStatus();
					devEndStatus = rbmlist.get(0).getEndState();
				}
				
				String item = "<slaveequip><equipid>"+dev.getPowerDeviceID()+"</equipid><equipname>"+dev.getPowerDeviceName()+"</equipname><beginstatus>"+devStartStatus+"</beginstatus><endstatus>"+devEndStatus+"</endstatus></slaveequip>";
				returnxml = returnxml + item;
			}
			returnxml = returnxml + "</Datas>";
		}
		return returnxml;
	}

	private void setCarditem(CardModel ylcm, String lastCarditem) {
		int newCarditem = Integer.valueOf(lastCarditem);
		int num = 1;
		String oldCarditem = "";
		for(int i = 0; i < ylcm.getCardItems().size();i++) {
			if(oldCarditem.equals("")){
				oldCarditem = ylcm.getCardItems().get(i).getCardItem();
				ylcm.getCardItems().get(i).setCardItem(StringUtils.ObjToString(newCarditem+num));
			}else if(oldCarditem.equals(ylcm.getCardItems().get(i).getCardItem())){
				ylcm.getCardItems().get(i).setCardItem(StringUtils.ObjToString(newCarditem+num));
			}else{
				num++;
				oldCarditem = StringUtils.ObjToString(ylcm.getCardItems().get(i).getCardItem());
				ylcm.getCardItems().get(i).setCardItem(StringUtils.ObjToString(newCarditem+num));
			}
		}		
	}

	private CardModel setRelate(List<Map<String, String>> ylzlList) {
		CardModel ylcm = new CardModel();
		ylcm.setCardItems(new ArrayList<CardItemModel>());
		List<CardItemModel> cardItems = new ArrayList<CardItemModel>();
		String sql="SELECT NAME,VOLTAGELVL FROM SX_EQUIP.T_C_ACLINEEND";
		List<Map<String, String>> results = DBManager.queryForList(sql);
		
		for(Map<String, String> map:ylzlList){
			CardItemModel cim  = new CardItemModel();
			String cbid = StringUtils.ObjToString(map.get("cbid"));
			String caozuozhiling = StringUtils.ObjToString(map.get("caozuozhiling"));
			String changzhan = StringUtils.ObjToString(map.get("changzhan"));
			String cardItem = StringUtils.ObjToString(map.get("carditem"));
			String zlxh = StringUtils.ObjToString(map.get("zlxh"));
			
			if(results.size()>0){
				for(Map<String, String> linemap: results){
					String line = linemap.get("NAME");
					String voltagelvl = linemap.get("VOLTAGELVL");

					if(caozuozhiling.contains(line)){
						caozuozhiling = caozuozhiling.replace(line, voltagelvl+line);
					}
				}
			}
			
			cim.setStationName(changzhan);
			cim.setCardItem(cardItem);
			cim.setZlxh(zlxh);
			cim.setCardDesc(caozuozhiling.replace("许可：", "").replace("通知：", "").replace("许可:", "").replace("通知:", ""));
			cim.setUuIds(cbid);
			cardItems.add(cim);
		}
		ylcm.setCardItems(cardItems);
		return ylcm;
	}

	private void setZlxh(CardModel cm) {
		int num = 1;
		Set<String> set = new HashSet<String>();
		
		for(int i = 0; i < cm.getCardItems().size();i++) {
			String carditem = cm.getCardItems().get(i).getCardItem();
			
			String zlxh = "";
			if(!set.contains(carditem)&&!"".equals(carditem)){
				set.add(carditem);
				num = 1;
			}else if(set.contains(carditem)){
				num += 1;
			}
			
			zlxh = String.valueOf(num);
			
			cm.getCardItems().get(i).setZlxh(String.valueOf(zlxh));
		}
	}

	
	
	public static String getXMLcode(String str){
		if(str.toUpperCase().contains("UTF-8")){
			return "UTF-8";
		}else{
			return "GBK";
		}
	}

	public static String getNotNullString(Object obj){
		return obj==null?"":obj.toString().trim();	
	}
	
	/**
	 * Xml转换为List<Map<String,String>> 对象
	 * */
	public static  List<Map<String, String>> getReqInfo(String arg) {
		List<Map<String, String>> voLists = new ArrayList<Map<String, String>>();
		String xmlCode = getXMLcode(arg);
		try {
			InputStream is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("Data");
			Element item =itemLists.get(0);
			List<Element> itemListsNew =item.elements("ITEM");
			//System.out.println(itemLists);
			for (int i = 0; i <itemListsNew.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = itemListsNew.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				voLists.add(mapInfo);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return voLists;
	}
	
	public boolean kgjx() {
		if(CBSystemConstants.globalSlaveDevList.size()>0){
			boolean flag = false;
			for(Map<String, String> map:CBSystemConstants.globalSlaveDevList){
				if(StringUtils.ObjToString(map.get("beginstatus")).equals("3")||StringUtils.ObjToString(map.get("endstatus")).equals("3")){
					flag = true;
					break;
				}
			}
			
			if(flag){
				return true;
			}else{
				return false;
			}
		}else{
			return false;
		}
	}
	
	public String getEcDevName(String zl){
		String name = "";
		String sql = "SELECT NAME FROM SX_EQUIP.T_C_ECDEVICE";
		List<Map<String,String>>  list = DBManager.queryForList(sql);
		
		for(Map<String,String> map:list){
			String equiptypeName = StringUtils.ObjToString(map.get("NAME")).replace("\r\n", "");
			if(zl.contains(equiptypeName)){
				return equiptypeName;
			}
		}
		return name;
	}
	
	private String getProtectNameXl(String caozuozhiling) {
		String sql = "SELECT NAME FROM SX_EQUIP.T_C_ECDEVICE WHERE CODETYPE = 'protect_name_xl'";
		List<Map<String,String>>  list = DBManager.queryForList(sql);
		
		for(Map<String,String> map:list){
			String equiptypeName = StringUtils.ObjToString(map.get("NAME")).replace("\r\n", "");
			if(caozuozhiling.contains(equiptypeName)){
				return caozuozhiling.replace(equiptypeName, "500kV"+equiptypeName);
			}
		}
		return caozuozhiling;
	}
	
	public boolean checkEcDevName(String zl){
		String sql = "SELECT EQUIPTYPE_NAME FROM SX_EQUIP.T_EQUIPTYPE WHERE EQUIPTYPE_CODE = '2'";
		List<Map<String,String>>  list = DBManager.queryForList(sql);
		
		for(Map<String,String> map:list){
			String name = StringUtils.ObjToString(map.get("EQUIPTYPE_NAME"));
			if(zl.contains(name)){
				return true;
			}
		}
		return false;
	}
}
