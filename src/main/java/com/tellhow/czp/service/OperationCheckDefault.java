package com.tellhow.czp.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.axis.i18n.RB;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.operationcard.VOSViewPanel;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RuleExecuteDefault;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.MotherLineLoad;
import czprule.system.CBSystemConstants;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013年11月13日 下午2:17:45 
 */
public class OperationCheckDefault {
	/**
	 * 根据操作任务，单条指令、厂站返回rbm可校验参数
	 * @param czrw
	 * @param station
	 * @param operation
	 * @return
	 */
	public static List<RuleBaseMode> execute(String czrw, String station,
			String operation) {
		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();
		RuleBaseMode mode = new RuleBaseMode();
		rbmList = CZPService.getService().getRBMList(station, czrw);
		mode = rbmList.get(0);
		if (rbmList.size() > 0 && !rbmList.get(0).getCheckout()) {
			return rbmList;
		}
		rbmList = CZPService.getService().getRBMList(station, operation);
		String message = "";
		for (String str : rbmList.get(0).getMessageList()) {
			message = message + str + "\r\n";
		}
		if (!message.equals("")) {
			List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
			PowerDevice pd = new PowerDevice();
			pd.setPowerStationName(station);
			pdlist.add(pd);
			CheckMessage cm = new CheckMessage();
			cm.setPd(pdlist);
			cm.setBottom("300");
			if (CBSystemConstants.lcm == null) {
				CBSystemConstants.lcm = new ArrayList<CheckMessage>();
			}
			CBSystemConstants.lcm.add(cm);
			if(CBSystemConstants.isCurrentSys) {
				VOSViewPanel vos = VOSViewPanel.getInstance();
				vos.initData(rbmList.get(0));
			}
			// ShowMessage.viewWarning(SystemConstants.getMainFrame(), message);
		}

		return rbmList;
	}

	
	public static List<RuleBaseMode> execute(String station, String operation) {
		return execute(station,operation,false);
	}
	/**
	 * 根据厂站、单条指令返回rbm可校验参数
	 * @param station
	 * @param operation
	 * @return
	 */
	public static List<RuleBaseMode> execute(String station, String operation, boolean isRW) {
		List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(
				station, operation);
		//返回数据中的设备类型为线路时，根据相应的设备状态转换，获取需操作的设备
		if(rbmList.size() > 0 && !isRW && rbmList.get(0).getPd()!=null && rbmList.get(0).getPd().getDeviceType().equals(SystemConstants.InOutLine)) {
			//设备状态为热备用改检修，或者为检修该冷备用时，操作接地刀闸
			if((rbmList.get(0).getBeginStatus().equals("3") && rbmList.get(0).getEndState().equals("2")) ||
					(rbmList.get(0).getBeginStatus().equals("2") && rbmList.get(0).getEndState().equals("3")) ) {
				List<PowerDevice> ddList = RuleExeUtil.getDeviceDirectList(rbmList.get(0).getPd(), SystemConstants.SwitchFlowGroundLine);
				if(ddList.size() == 1) {
					rbmList.get(0).setPd(ddList.get(0));
					//检修转冷备用时，接地刀闸拉开,0为合上状态，1为拉开状态
					if(rbmList.get(0).getBeginStatus().equals("3")) {
						rbmList.get(0).setBeginStatus("0");
						rbmList.get(0).setEndState("1");
					}
					//冷备用转检修时，接地刀闸合上,0为合上状态，1为拉开状态
					else if(rbmList.get(0).getEndState().equals("3")) {
						rbmList.get(0).setBeginStatus("1");
						rbmList.get(0).setEndState("0");
					}
				}
			}else if((rbmList.get(0).getBeginStatus().equals("0") && rbmList.get(0).getEndState().equals("1")) || 
					(rbmList.get(0).getBeginStatus().equals("1") && rbmList.get(0).getEndState().equals("0")) ) {
				PowerDevice sw = RuleExeUtil.getDeviceSwitch(rbmList.get(0).getPd());
				if(sw != null) {
					rbmList.get(0).setPd(sw);
				}
			}else if((rbmList.get(0).getBeginStatus().equals("1") && rbmList.get(0).getEndState().equals("2")) || 
					(rbmList.get(0).getBeginStatus().equals("2") && rbmList.get(0).getEndState().equals("1")) ) {
				PowerDevice sw = RuleExeUtil.getDeviceSwitch(rbmList.get(0).getPd());
				if(sw != null) {
					rbmList.get(0).setPd(sw);
				}
			}
		}else if(rbmList.size() > 0 && !isRW && rbmList.get(0).getPd()!=null && rbmList.get(0).getPd().getDeviceType().equals(SystemConstants.MotherLine)) {
			if(rbmList.get(0).getOperaTion().equals("母线倒母")){
				PowerDevice curDev = rbmList.get(0).getPd();
			 	List<PowerDevice> kgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			 	
			 	rbmList.clear();
			 	
			 	for(PowerDevice switchDev : kgList){
			 		if(switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) || switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL))
						continue;
					if(!switchDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
						continue;
					if(!switchDev.getDeviceStatus().equals("0") && !switchDev.getDeviceStatus().equals("1"))
						continue;
					
					boolean isDm = false;
					
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(switchDev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
							if(dz.getDeviceStatus().equals("0")){
								List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
								
								if(mxList.contains(curDev)){
									isDm = true;
									break;
								}
							}
						}
					}
							
					if(isDm){
						for(PowerDevice dz : dzList){
							if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
								if(dz.getDeviceStatus().equals("1")){
									RuleBaseMode rbm = new RuleBaseMode();
									rbm.setPd(dz);
									rbm.setBeginStatus("1");
									rbm.setEndState("0");
									rbmList.add(rbm);
								}
							}
						}
						
						for(PowerDevice dz : dzList){
							if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
								if(dz.getDeviceStatus().equals("0")){
									RuleBaseMode rbm = new RuleBaseMode();
									rbm.setPd(dz);
									rbm.setBeginStatus("0");
									rbm.setEndState("1");
									rbmList.add(rbm);
								}
							}
						}
					}
			 	}
			}
		}
		
		return rbmList;
	}

	/**
	 * 校验
	 * @param rbm
	 * @return
	 */
	public static boolean check(RuleBaseMode rbm) {

		DeviceOperate operate = new DeviceOperate();
		boolean result = operate.executeCheck(rbm);

		return result;

	}

	/**
	 * 执行动作的校验闪烁
	 * @param rbmList
	 * @return
	 */
	public static boolean inverse(final List<RuleBaseMode> rbmList) {
		// CBSystemConstants.cardbuildtype = "1";
		final InverseAction ia = new InverseAction();
		final List<PowerDevice> devList = new ArrayList<PowerDevice>();

		PowerDevice pd = rbmList.get(0).getPd();
		if(pd==null) return false;
		ia.openSvgStation(pd.getPowerStationID());// 打开对应厂站
		ia.autoToScrollToEquip(pd);

		boolean result = true;
		RuleExecute ruleExc = new RuleExecute();
		for (RuleBaseMode rbm : rbmList) {
			if (rbm.getBeginStatus().equals("") || rbm.getEndState().equals(""))
				continue;
			result = ruleExc.execute(rbm);
			if (!result)
				return false;
		}

		for (int i = 1; i <= CBSystemConstants.getDtdMap().size(); i++) {
			DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
			PowerDevice dev = dtd.getTransDevice();
			devList.add(dev);
		}

		new Thread(new Runnable() {

			public void run() {
				// TODO Auto-generated method stub
				try {
					Thread.sleep(500);
					ia.doSvgAction(devList);
					Thread.sleep(3500);

					for (int i = 1; i <= CBSystemConstants.getDtdMap().size(); i++) {
						DispatchTransDevice dtd = CBSystemConstants.getDtdMap()
								.get(i);
						PowerDevice dev = dtd.getTransDevice();
						dev.setDeviceStatus(dtd.getEndstate());
						DeviceSVGPanelUtil.changeDeviceSVGColor(dev);
					}
					CBSystemConstants.getDtdMap().clear();
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				// ia.doSvgAction(devList);
			}
		}).start();
		// return;
		// CBSystemConstants.cardbuildtype = "0";
		return result;
	}
/**
 * <AUTHOR>
 * 检查操作任务与操作指令是否相符
 * @param rmbList 
 * @param rmbListRW
 * @return 为2时通过
 */
	public static int CheckOperation(List<RuleBaseMode> rmbListRW,
		List<RuleBaseMode> rmbList) {
		for(RuleBaseMode rbm:rmbListRW){
			int result =  OperationCheckDefault.CompareOperation(rbm,rmbList);
			if(result != 2){
				return result;
			}
		}
		return 2;
		
	}
	/**
	 * <AUTHOR>
	 * @param czMode
	 * @param rmbList
	 * @return 
	 */
	private static int CompareOperation(RuleBaseMode czMode,
			List<RuleBaseMode> rmbList){
		int result = 0;
		int flag = 0;
		PowerDevice device = null;
		if (czMode.getPd() == null || czMode.getBeginStatus().equals("") || czMode.getEndState().equals("")) {
			return 2;
		}
		//当当前的设备状态与设备的初始状态不符时，重置设备状态
		if(!czMode.getPd().getDeviceStatus().equals(czMode.getBeginStatus()))
			RuleExeUtil.deviceStatusReset(czMode.getPd(), czMode.getPd().getDeviceStatus(), czMode.getBeginStatus());
		for (int i = 0; i < rmbList.size(); i++) {
			RuleBaseMode rbm = rmbList.get(i);
			if(rbm.getPd() == null || rbm.getBeginStatus().equals(""))
				continue;
			if(rbm.getPd().getDeviceStatus().equals(rbm.getBeginStatus()))
				RuleExeUtil.deviceStatusReset(rbm.getPd(), rbm.getPd().getDeviceStatus(), rbm.getEndState());
		}
		String czEndStatus = czMode.getPd().getDeviceStatus();
		if(!czEndStatus.equals(czMode.getEndState()))
			return 0;
		
		
		return 2;
		
	}
}
