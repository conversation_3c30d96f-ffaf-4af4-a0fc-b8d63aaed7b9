package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;
import org.xml.sax.SAXException;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;

public class GetDDTicketZTJHImpl {
public static List<Map<String, String>> globalMainDevList = new ArrayList<Map<String, String>>();
public static List<Map<String, String>> globalSlaveDevList = new ArrayList<Map<String, String>>();

public String execute(String arg){
		
	String result = "";
	String mainDeviceType = "";
	
	/**
	 * 设置系统运行方式为智能开票类型。
	 * */
	CBSystemConstants.isCurrentSys=false;
	CBSystemConstants.cardbuildtype = "0";
	CBSystemConstants.roleCode = "0";
	CBSystemConstants.opCode = "0";
	CBSystemConstants.opRuleCode = "0";
	CBSystemConstants.jh_tai = 0;
	
	System.out.println("输入参数：{"+arg+"}");
	
	String xmlCode = getXMLcode(arg);
	/**
	 * 构造返回的xml。
	 * */
	Document doc=DocumentHelper.createDocument();
	doc.setXMLEncoding(xmlCode);
	Element datas=doc.addElement("Datas");
	
	/**
	 * 解析传入的xml。
	 * */
	//传入参数数据
	List<Map<String, String>> mainDevlist = new ArrayList<Map<String, String>>();
	List<Map<String, String>> slaveDevlist = new ArrayList<Map<String, String>>();
	
	InputStream is=null;
	try {
		is = new ByteArrayInputStream(arg.getBytes(xmlCode));
		DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
		DocumentBuilder db = dbf.newDocumentBuilder();
		org.w3c.dom.Document document = db.parse(is);
		DOMReader domReader = new DOMReader();
		Document ret = domReader.read(document);
		Element root = ret.getRootElement();
		
		/*
		 * 清除全局变量
		 */
		globalMainDevList.clear();
		globalSlaveDevList.clear();
		
		List<Element> qymbLists =root.elements("areano");
		if(qymbLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			CBSystemConstants.qybm = qymbLists.get(0).getTextTrim();
			CBSystemConstants.unitCode = qymbLists.get(0).getTextTrim();
		}
		
		CZPService.getService().setArg("");
		StartupManager.startup();
		
		if(qymbLists.size() > 0) {
			CBSystemConstants.opCode = QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode);
		    if(CBSystemConstants.opCode.equals(""))
		    	CBSystemConstants.opCode = QueryDeviceDao.getOpcode("0", CBSystemConstants.roleCode);
		    CBSystemConstants.opRuleCode = CBSystemConstants.opCode;
		}
		
		List<Element> roleLists =root.elements("role");
		if(roleLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			CBSystemConstants.roleCode = roleLists.get(0).getTextTrim();
		}
		
		String operation = "";
		String endstatus = "";
		
		List<Element> firstsstationLists = root.elements("firststation");
		if(firstsstationLists.size() > 0) {
			CBSystemConstants.oneClickString = firstsstationLists.get(0).getTextTrim();
		}
		
		String beginstatus = "";
		
		//获取ITEM节点DOM
		List<Element> mainItemLists =root.elements("mainequip");
		//System.out.println(itemLists);
		for (int i = 0; i <mainItemLists.size(); i++) {
			Map<String, String> mapInfo =new HashMap<String,String>();
			Element element = mainItemLists.get(i);
			List<Element> elist = element.elements();
			for (int j = 0; j < elist.size(); j++) {
				Element el = elist.get(j);
				//将节点名称与值放入集合
				mapInfo.put(el.getName(), el.getTextTrim());				
			}
			mainDevlist.add(mapInfo);
		}
		
		globalMainDevList.addAll(mainDevlist);
		
		//获取ITEM节点DOM
		List<Element> slaveItemLists =root.elements("slaveequip");
		//System.out.println(itemLists);
		for (int i = 0; i <slaveItemLists.size(); i++) {
			Map<String, String> mapInfo =new HashMap<String,String>();
			Element element = slaveItemLists.get(i);
			List<Element> elist = element.elements();
			for (int j = 0; j < elist.size(); j++) {
				Element el = elist.get(j);
				//将节点名称与值放入集合
				mapInfo.put(el.getName(), el.getTextTrim());				
			}
			slaveDevlist.add(mapInfo);
		}
		
		globalSlaveDevList.addAll(slaveDevlist);
		
		String devicekind = "";
		
		List<Element> devicekindLists =root.elements("devicekind");
		
		if(devicekindLists.size() > 0) {
			devicekind  = devicekindLists.get(0).getTextTrim();
		}
		
		PowerDevice pd = null;
		String curMainDevId = "";
		
		if(devicekind.equals("一次设备")){
			for(Map<String, String> map : mainDevlist) {
				String equipID = StringUtils.ObjToString(map.get("equipid"));
				String equipname = StringUtils.ObjToString(map.get("equipname"));
				String stationid = "";
				String stationname = StringUtils.ObjToString(map.get("stationname"));
				beginstatus = StringUtils.ObjToString(map.get("beginstatus"));
				operation = StringUtils.ObjToString(map.get("operation"));
				CBSystemConstants.approval = StringUtils.ObjToString(map.get("approval")).replace("继保运规", "继保规程");
				CBSystemConstants.restoration = StringUtils.ObjToString(map.get("restoration"));
				
				if(operation.equals("运行")){
					endstatus = "0";
				}else if(operation.equals("检修")){
					endstatus = "3";
				}else if(operation.equals("冷备用")){
					endstatus = "2";
				}else if(operation.equals("热备用")){
					endstatus = "1";
				}
				
				if(equipname.contains("支线")){
					if(mainDevlist.size()>1){
						continue;
					}else{
						equipname = equipname.substring(0, lastAlphaNumeric(equipname)+1)+"线";
					}
				}
				
				if(stationname.equals("") && equipID.equals("") && !equipname.equals("")) {
					List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(equipname);
					equipID = rbmList.get(0).getPd().getPowerDeviceID();
					stationid = rbmList.get(0).getPd().getPowerStationID();
				}
				else if(equipID.equals("") && !equipname.equals("")) {
					List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(stationname,equipname);
					if(rbmList.get(0).getPd()!=null){
						equipID = rbmList.get(0).getPd().getPowerDeviceID();
						stationid = rbmList.get(0).getPd().getPowerStationID();
					}
				}
				
				PowerDevice eq = new PowerDevice();
				
				if(!equipID.equals("")){
					if(curMainDevId.equals("")){
						curMainDevId = equipID;
					}else if(curMainDevId.equals(equipID)){
						continue;
					}
					
					eq = CBSystemConstants.getPowerDevice(equipID);
				}
				
				if(eq.getPowerStationID().equals("")){
					eq.setPowerStationID(stationid);
				}
				
				/*if(eq.getDeviceType().equals(SystemConstants.InOutLine) && eq.getPowerStationID().equals("")) {
					Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(eq);
					for(PowerDevice ln:stationlines.keySet()){
						eq=ln;
						break;
					}
				}*/
				
				CreatePowerStationToplogy.loadFacData(eq.getPowerStationID());
				if(!eq.getPowerDeviceID().equals("")){
					pd=CBSystemConstants.getStationPowerDevices(eq.getPowerStationID()).get(eq.getPowerDeviceID());
					
					System.out.println("------------------加载本侧厂站设备实时状态---------------------");
					InitDeviceStatus ie=new InitDeviceStatus();
					ie.initStatus_EMS(pd.getPowerStationID());
					
					List<PowerDevice> otherXL = new ArrayList<PowerDevice>();
					if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
						System.out.println("------------------获取对侧线路---------------------");
						otherXL =RuleExeUtil.getLineOtherSideList(pd);
					}

					for(PowerDevice xl:otherXL){
						System.out.println("------------------加载对侧厂站设备实时状态---------------------");
						ie.initStatus_EMSToCache(xl.getPowerStationID());
					}
					
					if((eq.getDeviceType().equals(SystemConstants.InOutLine))&&CBSystemConstants.qybm.equals("330700")){
						List<PowerDevice> linelist =  RuleExeUtil.getLineAllSideList(eq);
						
						for(PowerDevice line : linelist){
							
							if(RuleExeUtil.getDeviceSwitch(line)!=null){
								String sql = "SELECT (SELECT TOP 1 AREA_NAME FROM TH_ROBOT.EMS_RESP_AREA_DEF D WHERE BITAND(E.RESP_AREA,D.AREA_VALUE)>0 AND BUSI_AREANO!='' ) AREANO FROM TH_ROBOT.MV_EQUIP_INFO E WHERE EQUIP_ID = '"+RuleExeUtil.getDeviceSwitch(line).getPowerDeviceID()+"'";
								
								List<Map<String, String>> list = DBManager.queryForList(sql);
								
								if(list.size()>0){
									String areaName = list.get(0).get("AREANO");
									
									if(areaName!=null){
										if(!areaName.equals("地调监控")){
											CBSystemConstants.cardtype = "1";
											break;
										}else{
											CBSystemConstants.cardtype = "0";
										}
									}
								}
							}
						}
					}else if((eq.getDeviceType().equals(SystemConstants.PowerTransformer))&&CBSystemConstants.qybm.equals("330700")){
						
						List<PowerDevice> swlist = RuleExeUtil.getTransformerSwitchSource(eq);
						
						for(PowerDevice sw : swlist){
							String sql = "SELECT (SELECT TOP 1 AREA_NAME FROM TH_ROBOT.EMS_RESP_AREA_DEF D WHERE BITAND(E.RESP_AREA,D.AREA_VALUE)>0 AND BUSI_AREANO!='' ) AREANO FROM TH_ROBOT.MV_EQUIP_INFO E WHERE EQUIP_ID = '"+sw.getPowerDeviceID()+"'";
							
							List<Map<String, String>> list = DBManager.queryForList(sql);
							
							if(list.size()>0){
								String areaName = list.get(0).get("AREANO");
								
								if(areaName!=null){
									if(!areaName.equals("地调监控")&&!areaName.equals("磐安公司")){
										CBSystemConstants.cardtype = "1";
										break;
									}else{
										CBSystemConstants.cardtype = "0";
									}
								}
							}
						}
					}else if((eq.getDeviceType().equals(SystemConstants.MotherLine))&&CBSystemConstants.qybm.equals("330700")){
						
						List<PowerDevice> swlist = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						
						for(PowerDevice sw : swlist){
							String sql = "SELECT (SELECT TOP 1 AREA_NAME FROM TH_ROBOT.EMS_RESP_AREA_DEF D WHERE BITAND(E.RESP_AREA,D.AREA_VALUE)>0 AND BUSI_AREANO!='' ) AREANO FROM TH_ROBOT.MV_EQUIP_INFO E WHERE EQUIP_ID = '"+sw.getPowerDeviceID()+"'";
							
							List<Map<String, String>> list = DBManager.queryForList(sql);
							
							if(list.size()>0){
								String areaName = list.get(0).get("AREANO");
								
								if(areaName!=null){
									if(!areaName.equals("地调监控")&&!areaName.equals("磐安公司")){
										CBSystemConstants.cardtype = "1";
										break;
									}else{
										CBSystemConstants.cardtype = "0";
									}
								}
							}
						}
					}else{
						CBSystemConstants.cardtype = "0";
					}
					
					mainDeviceType = pd.getDeviceType();
					
					RuleBaseMode rbm = new RuleBaseMode();
					rbm.setPd(pd);
					rbm.setBeginStatus(beginstatus);
					rbm.setEndState(endstatus);
					
					/*
					 * 本侧线路及母分开关状态重置
					 */
					if(!rbm.getPd().getDeviceStatus().equals(rbm.getBeginStatus())) {
						if(rbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)){
							if(!rbm.getPd().getDeviceStatus().equals("1")){
								System.out.println("------------------开始执行状态重置规则---------------------");
								System.out.println("本侧线路现状态为：{"+rbm.getPd().getDeviceStatus()+"}");
								RuleExeUtil.deviceStatusChange(rbm.getPd(),rbm.getPd().getDeviceStatus(),rbm.getBeginStatus());
								System.out.println("本侧线路重置后的状态为：{"+rbm.getPd().getDeviceStatus()+"}");
							}
						}else{
							System.out.println("------------------开始执行状态重置规则---------------------");
							System.out.println("设备现状态为：{"+rbm.getPd().getDeviceStatus()+"}");
							RuleExeUtil.deviceStatusChange(rbm.getPd(),rbm.getPd().getDeviceStatus(),rbm.getBeginStatus());
							System.out.println("设备重置后的状态为：{"+rbm.getPd().getDeviceStatus()+"}");
						}
					}
					
					/*
					 * 对侧线路及母分开关状态重置
					 */
					for(PowerDevice xl:otherXL){
						if(xl.getPowerVoltGrade() == 110){
							/*List<PowerDevice> list = RuleExeUtil.getDeviceList(xl, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
							
							List<PowerDevice> devices = RuleExeUtil.getDeviceList(xl, SystemConstants.InOutLine, SystemConstants.PowerTransformer,
									true, false, true);*/

							/*
							 * 线路先置到初始状态，开关默认转运行
							 */
							if(!rbm.getPd().getDeviceStatus().equals(xl.getDeviceStatus())&&!rbm.getPd().getDeviceStatus().equals("1")){
								System.out.println("对侧线路现状态为：{"+xl.getDeviceStatus()+"}");
								RuleExeUtil.deviceStatusChange(xl,xl.getDeviceStatus(),rbm.getBeginStatus());
								System.out.println("对侧线路重置后的状态为：{"+xl.getDeviceStatus()+"}");
								
								/*
								 * 用同一侧的线路状态来决定母分开关的状态
								 */
								
								/*if(rbm.getBeginStatus().equals("0")){//停电
									if(devices.size()>0&&list.size()>0){
										PowerDevice line = devices.get(0);
										List<PowerDevice> switchList = RuleExeUtil.getLinkedSwitch(xl);
										
										if(switchList.size()>0){
											if(switchList.get(0).getDeviceStatus().equals("1")){//如果线路开关已经是热备的，相当于非主送线路成票，母联开关置为运行
												RuleExeUtil.deviceStatusChange(list.get(0),list.get(0).getDeviceStatus(),rbm.getBeginStatus());
											}else if(switchList.get(0).getDeviceStatus().equals("0")){//如果线路开关是运行状态，默认母联置为热备
												RuleExeUtil.deviceStatusChange(list.get(0),list.get(0).getDeviceStatus(),"1");
											}else{//如果线路是冷备或者检修，先把线路置为运行，母联开关默认热备
												RuleExeUtil.deviceStatusChange(line,line.getDeviceStatus(),rbm.getBeginStatus());
												RuleExeUtil.deviceStatusChange(list.get(0),list.get(0).getDeviceStatus(),"1");
											}
										}
									}
								}else{
									if(devices.size()>0&&list.size()>0){
										PowerDevice line = devices.get(0);
										RuleExeUtil.deviceStatusChange(line,line.getDeviceStatus(),"0");
										RuleExeUtil.deviceStatusChange(list.get(0),list.get(0).getDeviceStatus(),"0");
									}
								}*/
							}
						}
					}
					
					DeviceOperate.getAlltransDevMap().clear();
					CBSystemConstants.getDtdMap().clear();
					CBSystemConstants.LineTagStatus.clear();
					
					if(eq.getDeviceType().equals(SystemConstants.InOutLine)){
						List<PowerDevice> allline = RuleExeUtil.getLineAllSideList(pd);
						
						for(PowerDevice line:allline){
							HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(line.getPowerStationID());
							
							for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
								PowerDevice dev = (PowerDevice) it2.next();
								if ((dev.getDeviceType().equals(SystemConstants.Switch)||dev.getDeviceType().equals(SystemConstants.InOutLine)||dev.getDeviceType().equals(SystemConstants.MotherLine)||dev.getDeviceType().equals(SystemConstants.PowerTransformer)) 
										&& dev.getPowerVoltGrade()==eq.getPowerVoltGrade()) {
									System.out.print("厂站:"+dev.getPowerStationName()+"|设备:"+dev+"|状态:"+dev.getDeviceStatus()+"\n");
									
									if(!rbm.getBeginStatus().equals("")&&!rbm.getEndState().equals("")){
										if(dev.getDeviceType().equals(SystemConstants.Switch)&&Integer.valueOf(rbm.getBeginStatus())<Integer.valueOf(rbm.getEndState())){
											String sql="UPDATE "+CBSystemConstants.opcardUser+"T_A_DEVICEEQUIPINFO SET LOADELECSTATUS = '"+dev.getDeviceStatus()+"' WHERE EQUIPID = '"+dev.getPowerDeviceID()+"'";
											DBManager.execute(sql);
										}
									}
								}
							}
						}
					}else{
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
						
						for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = (PowerDevice) it2.next();
							if ((dev.getDeviceType().equals(SystemConstants.Switch)||dev.getDeviceType().equals(SystemConstants.InOutLine)||dev.getDeviceType().equals(SystemConstants.MotherLine)||dev.getDeviceType().equals(SystemConstants.PowerTransformer)) 
									&& dev.getPowerVoltGrade()==eq.getPowerVoltGrade()) {
								System.out.print("厂站:"+dev.getPowerStationName()+"|设备:"+dev+"|状态:"+dev.getDeviceStatus()+"\n");
								
								if(dev.getDeviceType().equals(SystemConstants.Switch)&&Integer.valueOf(rbm.getBeginStatus())<Integer.valueOf(rbm.getEndState())){
									String sql="UPDATE "+CBSystemConstants.opcardUser+"T_A_DEVICEEQUIPINFO SET LOADELECSTATUS = '"+dev.getDeviceStatus()+"' WHERE EQUIPID = '"+dev.getPowerDeviceID()+"'";
									DBManager.execute(sql);
								}
							}
						}
					}
					
					String statecode = "";
					String sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where cardbuildtype='0' and t.opcode='"+CBSystemConstants.opCode+"' and t.devicetypeid='"+pd.getDeviceType()+"' and t.statename = '"+operation+"' and isLock = '0'";
					List<Map<String, Object>> results = DBManager.queryForList(sql);
					
					Map<String, Object> temp;
					if(results.size()>0) {
						temp=(Map<String, Object>)results.get(0);
						statecode = StringUtils.ObjToString(temp.get("statecode"));
					}
					rbm.setStateCode(statecode);
					
					CardModel cm = new CardModel();
					cm.setCardItems(new ArrayList<CardItemModel>());
					
					CBSystemConstants.setCurRBM(rbm);
					
					RuleExecute ruleExc=new RuleExecute();
					ruleExc.execute(rbm);
					DeviceOperate doe = new DeviceOperate();
					doe.setTask(cm);
					
					CardModel cardModel=WordExecute.getInstance().execute(rbm);
					for(int i=0;i<cardModel.getCardItems().size();i++){//清除空指令
						if(cardModel.getCardItems().get(i).getCardDesc().equals("")){
							cardModel.getCardItems().remove(i);
							i--;
						}
					}
					
					cm.getCardItems().addAll(cardModel.getCardItems());
					
					if(!cardModel.getCzrw().equals("") || cardModel.getCardItems().size() > 0) {
						Element rw=datas.addElement("ITEM");
						rw.addElement("czrw").setText(cardModel.getCzrw());
					}
					
					for(int i = 0; i < cm.getCardItems().size();i++) {
						Element item=datas.addElement("ITEM");
						item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
						item.addElement("cardorder").setText(String.valueOf(i+1));
						item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
						String cardDesc=cm.getCardItems().get(i).getCardDesc();
						item.addElement("cznr").setText(cardDesc);
						item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
						item.addElement("zlxh").setText(cm.getCardItems().get(i).getOrderNumber());
						item.addElement("czdwid").setText(cm.getCardItems().get(i).getBzbj());
					}
					result = doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
				}
			}
		}else{
			for(Map<String, String> map : mainDevlist) {
				String equipname = StringUtils.ObjToString(map.get("equipname"));
				String stationname = StringUtils.ObjToString(map.get("stationname"));
				beginstatus = StringUtils.ObjToString(map.get("beginstatus"));
				operation = StringUtils.ObjToString(map.get("operation"));
				CBSystemConstants.restoration = StringUtils.ObjToString(map.get("restoration"));
				String operate = "";
				
				if(operation.equals("跳闸")){
					endstatus = "4";
					operate = "复役";
				}else if(operation.equals("信号")){
					endstatus = "5";
					operate = "停役";
				}
				
				mainDeviceType = "二次设备";
				
				CardModel cm = new CardModel();
				cm.setCardItems(new ArrayList<CardItemModel>());
				List<CardItemModel> cardItems = new ArrayList<CardItemModel>();
				
				if(operate.equals("停役")){
					if(equipname.contains("线")&&!equipname.contains("母线")){
						String lineName = equipname.substring(0, equipname.indexOf("线")+1);
						List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(stationname,lineName);
						PowerDevice line = rbmList.get(0).getPd();
						
						if(line.getDeviceType().equals(SystemConstants.InOutLine) && line.getPowerStationID().equals("")) {
							Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(line);
							for(PowerDevice ln:stationlines.keySet()){
								line=ln;
								break;
							}
						}
						
						List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(line);
						
						for(int i=0;i<lineList.size();i++){
							PowerDevice lines = lineList.get(i);
							CardItemModel cim = new CardItemModel();
							cim.setZlxh("1");
							cim.setCardItem(String.valueOf(i+1));
							cim.setShowName(lines.getPowerStationName());
							cim.setStationName(lines.getPowerStationName());
							cim.setCardDesc(equipname+"由跳闸改为信号");
							cardItems.add(cim);
						}
						
						CardItemModel cim = new CardItemModel();
						cim.setZlxh("1");
						cardItems.add(cim);
						cim.setCardItem("3");
						cim.setShowName(stationname);
						cim.setStationName(stationname);
						cim.setCardDesc("告他："+equipname+"处信号，具备状态移交条件，现移交现场，改为工作状态后回告");
					}else if(equipname.contains("故障录波器")){
						for(int i=0;i<2;i++){
							CardItemModel cim = new CardItemModel();
							switch (i) {
							case 0:
								cim.setZlxh("1");
								cim.setCardItem("1");
								cim.setShowName(stationname);
								cim.setStationName(stationname);
								cim.setCardDesc("确证："+equipname+"处信号");
								cardItems.add(cim);
								break;
							case 1:
								cim.setZlxh("1");
								cardItems.add(cim);
								cim.setCardItem("2");
								cim.setShowName(stationname);
								cim.setStationName(stationname);
								cim.setCardDesc("告他："+equipname+"处信号，具备状态移交条件，现移交现场，改为工作状态后回告");
								break;
							}
						}
					}else{
						for(int i=0;i<2;i++){
							CardItemModel cim = new CardItemModel();
							switch (i) {
							case 0:
								cim.setZlxh("1");
								cim.setCardItem("1");
								cim.setShowName(stationname);
								cim.setStationName(stationname);
								cim.setCardDesc(equipname+"由跳闸改为信号");
								cardItems.add(cim);
								break;
							case 1:
								cim.setZlxh("1");
								cardItems.add(cim);
								cim.setCardItem("2");
								cim.setShowName(stationname);
								cim.setStationName(stationname);
								cim.setCardDesc("告他："+equipname+"处信号，具备状态移交条件，现移交现场，改为工作状态后回告");
								break;
							}
						}
					}
				}else if(operate.equals("复役")){
					if(equipname.contains("线")&&!equipname.contains("母线")){
						String lineName = equipname.substring(0, equipname.indexOf("线")+1);
						List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(stationname,lineName);
						PowerDevice line = rbmList.get(0).getPd();
						
						if(line.getDeviceType().equals(SystemConstants.InOutLine) && line.getPowerStationID().equals("")) {
							Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(line);
							for(PowerDevice ln:stationlines.keySet()){
								line=ln;
								break;
							}
						}
						
						List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(line);
						
						CardItemModel cim = new CardItemModel();
						cim.setZlxh("1");
						cardItems.add(cim);
						cim.setCardItem("1");
						cim.setShowName(stationname);
						cim.setStationName(stationname);
						cim.setCardDesc("汇报："+equipname+"第一套纵联保护处信号，具备状态移交条件，现交回调度");
						
						for(int i=0;i<lineList.size();i++){
							PowerDevice lines = lineList.get(i);
							cim = new CardItemModel();
							cim.setZlxh("1");
							cim.setCardItem(String.valueOf(i+2));
							cim.setShowName(lines.getPowerStationName());
							cim.setStationName(lines.getPowerStationName());
							cim.setCardDesc(equipname+"由信号改为跳闸");
							cardItems.add(cim);
						}
					}else{
						for(int i=0;i<2;i++){
							CardItemModel cim = new CardItemModel();
							switch (i) {
							case 0:
								cim.setZlxh("1");
								cim.setCardItem("1");
								cim.setShowName(stationname);
								cim.setStationName(stationname);
								cim.setCardDesc("汇报："+equipname+"处信号，具备状态移交条件，现交回调度");
								cardItems.add(cim);
								break;
							case 1:
								cim.setZlxh("1");
								cardItems.add(cim);
								cim.setCardItem("2");
								cim.setShowName(stationname);
								cim.setStationName(stationname);
								cim.setCardDesc(equipname+"由信号改为跳闸");
								break;
							}
						}
					}
				}
				
				cm.setCardItems(cardItems);
				Element rw=datas.addElement("ITEM");
				rw.addElement("czrw").setText(stationname+"："+equipname+operate);
				
				for(int i = 0; i < cm.getCardItems().size();i++) {
					Element item=datas.addElement("ITEM");
					item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
					item.addElement("cardorder").setText(String.valueOf(i+1));
					item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
					String cardDesc=cm.getCardItems().get(i).getCardDesc();
					item.addElement("cznr").setText(cardDesc);
					item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
					item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
					item.addElement("czdwid").setText(cm.getCardItems().get(i).getBzbj());
				}
				result = doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
			}
		}
		
	} catch (Exception e) {
		e.printStackTrace();
	}
	finally{
		if(is!=null){
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
		if(mainDevlist.size()>1){//针对于浙江金华，存在多主设备的情况
			for(Map<String, String> map : globalMainDevList){
				String devname = StringUtils.ObjToString(map.get("equipname"));
				
				if(devname.contains("支线")){
					System.out.println(result);
					return result;
				}
			}
			
			List<Map<String, String>> list = new ArrayList<Map<String, String>>();
			try {
				is = new ByteArrayInputStream(result.getBytes(getXMLcode(result)));
				DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
				DocumentBuilder db;
				db = dbf.newDocumentBuilder();
				org.w3c.dom.Document document;
				document = db.parse(is);
				DOMReader domReader = new DOMReader();
				Document ret = domReader.read(document);
				Element root = ret.getRootElement();
				
				//获取ITEM节点DOM
				List<Element> itemLists =root.elements("ITEM");
				//System.out.println(itemLists);
				for (int i = 0; i <itemLists.size(); i++) {
					Map<String, String> mapInfo =new HashMap<String,String>();
					Element element = itemLists.get(i);
					List<Element> elist = element.elements();
					for (int j = 0; j < elist.size(); j++) {
						Element el = elist.get(j);
						//将节点名称与值放入集合
						mapInfo.put(el.getName(), el.getTextTrim());				
					}
					list.add(mapInfo);
				}
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			} catch (ParserConfigurationException e) {
				e.printStackTrace();
			} catch (SAXException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			} finally{
				if(is!=null){
					try {
						is.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
			
			String returnxml = "<?xml version=\"1.0\" encoding=\""+xmlCode+"\"?><Datas>";
			/*
			 * 1、生成操作任务
			 */
			String czrw = "";
			
			if(mainDeviceType.equals(SystemConstants.InOutLine)||mainDeviceType.equals(SystemConstants.ElecCapacity)||mainDeviceType.equals(SystemConstants.ElecShock)||mainDeviceType.equals("二次设备")){
				int i= 1;
				for(Iterator<Map<String,String>> itor = list.iterator();itor.hasNext();){
					Map<String,String> map = itor.next();
					if(map.containsKey("czrw")){
						String czrwmx = StringUtils.ObjToString(map.get("czrw"));
						
						if(!"".equals(czrw)){
							if(mainDeviceType.equals(SystemConstants.ElecCapacity)||mainDeviceType.equals(SystemConstants.ElecShock)||mainDeviceType.equals("二次设备")){
								String station = czrw.substring(0, czrw.indexOf("："));
								String stationNew = czrwmx.substring(0, czrwmx.indexOf("："));
								
								if(station.equals(stationNew)){
									czrwmx = czrwmx.substring(czrwmx.indexOf("：")+1,czrwmx.length());
								}
							}
						}
						
						czrw = czrw + czrwmx + "、";
						itor.remove();
					}else{
						map.put("cardorder", String.valueOf(i));
						i++;
					}
				}
			}
			
			if(czrw.endsWith("、")){
				czrw = czrw.substring(0, czrw.length()-1);
				czrw = czrw.replace("停役、", "、").replace("复役、", "、");
			}
			returnxml = returnxml+"<ITEM><czrw>"+czrw+"</czrw></ITEM>";
			Collections.sort(list, new Comparator<Map<String,String>>() {
				@Override
				public int compare(Map<String, String> map1,
						Map<String, String> map2) {
					if(!map1.containsKey("cardorder")){
						return -1;
					}else if(!map2.containsKey("cardorder")){
						return 0;
					}
					
					if(!StringUtils.ObjToString(map1.get("cardorder")).equals("")&&!StringUtils.ObjToString(map2.get("cardorder")).equals("")){
						int cardorder1 = Integer.valueOf(StringUtils.ObjToString(map1.get("cardorder")));
						int cardorder2 = Integer.valueOf(StringUtils.ObjToString(map2.get("cardorder")));
						
						if(cardorder1<cardorder2){
							return -1;
						}else if(cardorder1==cardorder2){
							return 0;
						}
					}
					
					return 1;
				}
	        });
			
			Collections.sort(list, new Comparator<Map<String,String>>() {
				@Override
				public int compare(Map<String, String> map1,
						Map<String, String> map2) {
					
					if(!map1.containsKey("carditem")){
						return -1;
					}else if(!map2.containsKey("carditem")){
						return 0;
					}
					
					if(!StringUtils.ObjToString(map1.get("carditem")).equals("")&&!StringUtils.ObjToString(map2.get("carditem")).equals("")){
						int carditem1 = Integer.valueOf(StringUtils.ObjToString(map1.get("carditem")));
						int carditem2 = Integer.valueOf(StringUtils.ObjToString(map2.get("carditem")));

						if(carditem1<carditem2){
							return -1;
						}else if(carditem1==carditem2){
							return 0;
						}
					}
					
					return 1;
				}
	        });
			
			/*
			 * 生成指令
			 */
			int num = 1;
			if(mainDeviceType.equals(SystemConstants.InOutLine)){
				for(int j=0;j<list.size();j++){
					Map<String,String> map = list.get(j);
					String carditem = StringUtils.ObjToString(map.get("carditem"));
					String czdw = StringUtils.ObjToString(map.get("czdw"));
					String cznr = StringUtils.ObjToString(map.get("cznr"));
					
					if(cznr.contains("告他：")&&cznr.contains("对侧已改为冷备用")){
						String cznrNext = StringUtils.ObjToString(list.get(j+1).get("cznr"));
						if(cznrNext.contains("告他：")&&cznrNext.contains("对侧已改为冷备用")){
							String cznrNew = "告他："+cznr.substring(cznr.indexOf("：")+1, cznr.indexOf("线")+1)+"、"+cznrNext.substring(cznrNext.indexOf("：")+1, cznrNext.indexOf("线")+1)+"对侧处冷备用";
							cznr = cznrNew;
						}else{
							continue;
						}
					}else if(cznr.equals("告他上述")&&j==list.size()-1){
						continue;
					}
					
					String item = "<ITEM><carditem>"+carditem+"</carditem>"
								+ "<cardorder>"+String.valueOf(num)+"</cardorder>"
								+ "<czdw>"+czdw+"</czdw>"
								+ "<cznr>"+cznr+"</cznr></ITEM>";
					returnxml = returnxml + item;
					num++;
				}
				returnxml = returnxml +"</Datas>";
				result = returnxml.replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>").replace("</czrw><ITEM>", "</czrw>\n<ITEM>");
			}else if(mainDeviceType.equals(SystemConstants.ElecCapacity)||mainDeviceType.equals(SystemConstants.ElecShock)){
				for(int j=0;j<list.size();j++){
					Map<String,String> map = list.get(j);
					String carditem = StringUtils.ObjToString(map.get("carditem"));
					String czdw = StringUtils.ObjToString(map.get("czdw"));
					String cznr = StringUtils.ObjToString(map.get("cznr"));
					
					if(cznr.contains("告他：")){
						if(j+1<list.size()){
							String cznrNext = StringUtils.ObjToString(list.get(j+1).get("cznr"));
							String cznrLast = cznr.substring(cznr.indexOf("处"), cznr.length());
							
							if(cznrNext.contains("告他：")){
								String devname1 = "";
								String devname2 = "";
								
								if(cznr.contains("电容器")){
									devname1 = cznr.substring(cznr.indexOf("：")+1, cznr.indexOf("电容器")+3);
								}else if(cznr.contains("电抗器")){
									devname1 = cznr.substring(cznr.indexOf("：")+1, cznr.indexOf("电抗器")+3);
								}

								if(cznrNext.contains("电容器")){
									devname2 = cznrNext.substring(cznrNext.indexOf("：")+1, cznrNext.indexOf("电容器")+3);
								}else if(cznrNext.contains("电抗器")){
									devname2 = cznrNext.substring(cznrNext.indexOf("：")+1, cznrNext.indexOf("电抗器")+3);
								}
								
								String cznrNew = "告他："+devname1+"、"+devname2+cznrLast;
								cznr = cznrNew;
							}
						}else{
							continue;
						}
					}
					
					String item = "<ITEM><carditem>"+carditem+"</carditem>"
								+ "<cardorder>"+String.valueOf(num)+"</cardorder>"
								+ "<czdw>"+czdw+"</czdw>"
								+ "<cznr>"+cznr+"</cznr></ITEM>";
					returnxml = returnxml + item;
					num++;
				}
				returnxml = returnxml +"</Datas>";
				result = returnxml.replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>").replace("</czrw><ITEM>", "</czrw>\n<ITEM>");
			}else if(mainDeviceType.equals("二次设备")){
				for(int j=0;j<list.size();j++){
					Map<String,String> map = list.get(j);
					String carditem = StringUtils.ObjToString(map.get("carditem"));
					String czdw = StringUtils.ObjToString(map.get("czdw"));
					String cznr = StringUtils.ObjToString(map.get("cznr"));
					
					if(cznr.contains("告他：")){
						if(j+1<list.size()){
							String cznrNext = StringUtils.ObjToString(list.get(j+1).get("cznr"));
							if(cznrNext.contains("告他：")){
								String devname1 = "";
								String devname2 = "";
								
								if(cznr.contains("保护")){
									devname1 = cznr.substring(cznr.indexOf("：")+1, cznr.indexOf("保护")+2);
								}else if(cznr.contains("装置")){
									devname1 = cznr.substring(cznr.indexOf("：")+1, cznr.indexOf("装置")+2);
								}

								if(cznrNext.contains("保护")){
									devname2 = cznrNext.substring(cznrNext.indexOf("：")+1, cznrNext.indexOf("保护")+2);
								}else if(cznrNext.contains("装置")){
									devname2 = cznrNext.substring(cznrNext.indexOf("：")+1, cznrNext.indexOf("装置")+2);
								}
								
								String cznrNew = "告他："+devname1+"、"+devname2+"处信号，具备状态移交条件，现移交现场，改为工作状态后回告";
								cznr = cznrNew;
							}
						}else{
							continue;
						}
					}
					
					String item = "<ITEM><carditem>"+carditem+"</carditem>"
								+ "<cardorder>"+String.valueOf(num)+"</cardorder>"
								+ "<czdw>"+czdw+"</czdw>"
								+ "<cznr>"+cznr+"</cznr></ITEM>";
					returnxml = returnxml + item;
					num++;
				}
				returnxml = returnxml +"</Datas>";
				result = returnxml.replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>").replace("</czrw><ITEM>", "</czrw>\n<ITEM>");
			}
		}
		System.out.println(result);
		return result;
	}

	public int lastAlphaNumeric(String s) {
	    for (int i = s.length() - 1; i >= 0; i--) {
	        char c = s.charAt(i);
	        if (Character.isDigit(c))
	            return i;
	    }
	    return -1; // no alphanumeric character at all
	}

	public String getXMLcode(String str){
		if(str.toUpperCase().contains("UTF-8")){
			return "UTF-8";
		}else{
			return "GBK";
		}
	}
}
