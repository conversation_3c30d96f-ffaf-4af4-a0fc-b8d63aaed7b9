package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;

@SuppressWarnings("unchecked")
public class GetDDTicketZTSXTDImpl {
	public String execute(String arg){
		
	String result = "";
	
	/**
	 * 设置系统运行方式为智能开票类型。
	 * */
	CBSystemConstants.isCurrentSys=false;
	CBSystemConstants.cardbuildtype = "0";
	CBSystemConstants.roleCode = "0";
	CBSystemConstants.opCode = "0";
	CBSystemConstants.opRuleCode = "0";
	CBSystemConstants.jh_tai = 0;
	CBSystemConstants.cardstatus = "0";
	CBSystemConstants.lcm.clear();
	System.out.println("输入参数：{"+arg+"}");
	
	String xmlCode = getXMLcode(arg);
	/**
	 * 构造返回的xml。
	 * */
	Document doc=DocumentHelper.createDocument();
	doc.setXMLEncoding(xmlCode);
	Element datas=doc.addElement("Datas");
	
	/**
	 * 解析传入的xml。
	 * */
	//传入参数数据
	List<Map<String, String>> mainDevlist = new ArrayList<Map<String, String>>();
	List<Map<String, String>> slaveDevlist = new ArrayList<Map<String, String>>();
	Map<String, String> operateMap = new HashMap<String, String>();
	InputStream is=null;
	try {
		is = new ByteArrayInputStream(arg.getBytes(xmlCode));
		DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
		DocumentBuilder db = dbf.newDocumentBuilder();
		org.w3c.dom.Document document = db.parse(is);
		DOMReader domReader = new DOMReader();
		Document ret = domReader.read(document);
		Element root = ret.getRootElement();
		
		/*
		 * 清除全局变量
		 */
		CBSystemConstants.globalMainDevList.clear();
		CBSystemConstants.globalSlaveDevList.clear();
		
		List<Element> typeLists =root.elements("type");
		if(typeLists.size()>0){
			String type = typeLists.get(0).getTextTrim();
			
			if(type.equals("状态成票")){
				CBSystemConstants.isztcp = true;
			}
		}
		
		List<Element> qymbLists =root.elements("areano");
		if(qymbLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			CBSystemConstants.qybm = qymbLists.get(0).getTextTrim();
			CBSystemConstants.unitCode = qymbLists.get(0).getTextTrim();
		}
		
		CZPService.getService().setArg("");
		StartupManager.startup();
		
		if(qymbLists.size() > 0) {
			CBSystemConstants.opCode = QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode);
		    if(CBSystemConstants.opCode.equals(""))
		    	CBSystemConstants.opCode = QueryDeviceDao.getOpcode("0", CBSystemConstants.roleCode);
		    CBSystemConstants.opRuleCode = CBSystemConstants.opCode;
		}
		
		
		
		
		List<Element> roleLists =root.elements("role");
		if(roleLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			CBSystemConstants.roleCode = roleLists.get(0).getTextTrim();
		}
		
		String operation = "";
		String endstatus = "";
		List<Element> operationLists =root.elements("operation");
		if(operationLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			operation = operationLists.get(0).getTextTrim();
			if(operation.equals("运行")){
				endstatus = "0";
			}else if(operation.equals("检修")){
				endstatus = "3";
			}else if(operation.equals("冷备用")){
				endstatus = "2";
			}else if(operation.equals("热备用")){
				endstatus = "1";
			}
			
			operateMap.put("endstatus", endstatus);
		}
		
		List<Element> firstsstationLists = root.elements("firststation");
		if(firstsstationLists.size() > 0) {
			CBSystemConstants.oneClickString = firstsstationLists.get(0).getTextTrim();
		}
		
		String beginstatus = "";
		List<Element> beginstatusLists =root.elements("beginstatus");
		if(beginstatusLists.size() > 0) {
			//如果传了区域编码参数，给区域编码全局变量赋值
			beginstatus = beginstatusLists.get(0).getTextTrim();
		}
		
		//获取ITEM节点DOM
		List<Element> mainItemLists =root.elements("mainequip");
		//System.out.println(itemLists);
		for (int i = 0; i <mainItemLists.size(); i++) {
			Map<String, String> mapInfo =new HashMap<String,String>();
			Element element = mainItemLists.get(i);
			List<Element> elist = element.elements();
			for (int j = 0; j < elist.size(); j++) {
				Element el = elist.get(j);
				//将节点名称与值放入集合
				mapInfo.put(el.getName(), el.getTextTrim());	
			}
			mapInfo.put("endstatus", getNotNullString(operateMap.get("endstatus")));
			mainDevlist.add(mapInfo);
		}
		
		CBSystemConstants.globalMainDevList.addAll(mainDevlist);
		
		//获取ITEM节点DOM
		List<Element> slaveItemLists =root.elements("slaveequip");
		//System.out.println(itemLists);
		for (int i = 0; i <slaveItemLists.size(); i++) {
			Map<String, String> mapInfo =new HashMap<String,String>();
			Element element = slaveItemLists.get(i);
			List<Element> elist = element.elements();
			for (int j = 0; j < elist.size(); j++) {
				Element el = elist.get(j);
				//将节点名称与值放入集合
				mapInfo.put(el.getName(), el.getTextTrim());				
			}
			slaveDevlist.add(mapInfo);
		}
		
		CBSystemConstants.globalSlaveDevList.addAll(slaveDevlist);
		
		PowerDevice pd = null;
		for(Map<String, String> map : mainDevlist) {
			String equipID = getNotNullString(map.get("equipid"));
			String equipname = getNotNullString(map.get("equipname"));
			String stationid = getNotNullString(map.get("stationid"));
			String stationname = getNotNullString(map.get("stationname"));
			
			String sql = "select station_id as stationid from "+CBSystemConstants.equipUser+"T_SUBSTATION where station_name = '溪洛渡右岸电厂'";
			List<Map<String, Object>> results = DBManager.queryForList(sql);
			if(results.size()>0) {
				String stid = results.get(0).get("STATIONID")==null?"":results.get(0).get("STATIONID").toString();
				if(!stationid.equals(stid)){
					CBSystemConstants.cardtype = "1";
				}else{
					CBSystemConstants.cardtype = "0";
				}
			}
			
			if(stationname.equals("") && equipID.equals("") && !equipname.equals("")) {
				List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(equipname);
				equipID = rbmList.get(0).getPd().getPowerDeviceID();
				CBSystemConstants.setCurRBM(rbmList.get(0));
			}
			else if(equipID.equals("") && !equipname.equals("")) {
				List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(stationname,equipname);
				equipID = rbmList.get(0).getPd().getPowerDeviceID();
				CBSystemConstants.setCurRBM(rbmList.get(0));
			}
			
			PowerDevice eq = new PowerDevice();
			
			if(!equipID.equals("")){
				eq = CBSystemConstants.getPowerDevice(equipID);
			}
			
			if(eq.getDeviceType().equals(SystemConstants.InOutLine) && eq.getPowerStationID().equals("")) {
				Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(eq);
				for(PowerDevice ln:stationlines.keySet()){
					eq=ln;
					break;
				}
			}
			
			CreatePowerStationToplogy.loadFacData(eq.getPowerStationID());
			pd=CBSystemConstants.getStationPowerDevices(eq.getPowerStationID()).get(eq.getPowerDeviceID());
			
			RuleBaseMode curRbm =  CBSystemConstants.getCurRBM();
			PowerDevice curMainDev = curRbm.getPd();
			if(curMainDev==null){
				List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();
				RuleBaseMode rbm = new RuleBaseMode();
				rbm.setPd(pd);
				rbmList.add(rbm);
				CBSystemConstants.setCurRBM(rbmList.get(0));
			}
			
			curRbm.setBeginStatus(beginstatus);
			curRbm.setEndState(endstatus);
		}
		
		/*
		 * 在这里加载厂站的各种数据
		 */
		InitDeviceStatus ie = new InitDeviceStatus();
		ie.initStatus_EMSToCache(pd.getPowerStationID());
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			List<PowerDevice> list = RuleExeUtil.getLineOtherSideList(pd);
			
			if(list.size()>0){
				for(PowerDevice dev:list){
					ie.initStatus_EMSToCache(dev.getPowerStationID());
				}
			}
		}
		
		for(Map<String, String> slavemap : slaveDevlist) {
			String equipID = getNotNullString(slavemap.get("equipid"));
			String equipname = getNotNullString(slavemap.get("equipname"));
			
			if(equipID.equals("") && !equipname.equals("")) {
				List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(CBSystemConstants.oneClickString,equipname);
				equipID = rbmList.get(0).getPd().getPowerDeviceID();
				slavemap.put("equipid", equipID);
			}
		}
		
		System.out.println(pd);
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			RuleBaseMode rbm = new RuleBaseMode();
			rbm.setPd(pd);
			rbm.setBeginStatus(beginstatus);
			rbm.setEndState(endstatus);
			getTicket(pd,operation,rbm,datas);
		}else if(pd.getDeviceType().equals(SystemConstants.Switch)){
			RuleBaseMode rbm = new RuleBaseMode();
			rbm.setPd(pd);
			rbm.setBeginStatus(beginstatus);
			rbm.setEndState(endstatus);
			getTicket(pd,operation,rbm,datas);
		}else{
			if(beginstatus.equals("0")&&endstatus.equals("3")&&kgjx()){//运行到检修
				for(int k=0;k<2;k++){
					RuleBaseMode rbm = new RuleBaseMode();
					
					if(k==0){
						rbm.setPd(pd);
						rbm.setBeginStatus(beginstatus);
						rbm.setEndState("2");
						operation = "冷备用";
						
						if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){
							if(CBSystemConstants.globalSlaveDevList.size()>0){
								for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
									Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
									map.put("beginstatus", "0");
									map.put("endstatus", "2");
								}
							}
						}
					}else{
						rbm.setPd(pd);
						rbm.setBeginStatus("2");
						rbm.setEndState(endstatus);
						operation = "检修";
						
						if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){
							if(CBSystemConstants.globalSlaveDevList.size()>0){
								for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
									Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
									map.put("beginstatus", "2");
									map.put("endstatus", "3");
								}
							}
						}
					}
					
					getTicket(pd,operation,rbm,datas);
				}
			}else if(beginstatus.equals("3")&&endstatus.equals("0")&&kgjx()){//检修到运行
				for(int k=0;k<2;k++){
					RuleBaseMode rbm = new RuleBaseMode();
					
					if(k==0){
						rbm.setPd(pd);
						rbm.setBeginStatus(beginstatus);
						rbm.setEndState("2");
						operation = "冷备用";
						
						if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){
							if(CBSystemConstants.globalSlaveDevList.size()>0){
								for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
									Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
									if(map.get("beginstatus").equals("3")&&map.get("endstatus").equals("3")){
										
									}else{
										map.put("beginstatus", "3");
										map.put("endstatus", "2");
									}
								}
							}
						}
					}else{
						rbm.setPd(pd);
						rbm.setBeginStatus("2");
						rbm.setEndState(endstatus);
						operation = "运行";
						
						for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
							Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
							if(map.get("beginstatus").equals("3")&&map.get("endstatus").equals("3")){
								
							}else{
								map.put("beginstatus", "2");
								map.put("endstatus", "0");
							}
						}
					}
					
					getTicket(pd,operation,rbm,datas);
				}
			}else{
				RuleBaseMode rbm = new RuleBaseMode();
				rbm.setPd(pd);
				rbm.setBeginStatus(beginstatus);
				rbm.setEndState(endstatus);
				getTicket(pd,operation,rbm,datas);
			}
		}
		result = doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>").replace("</Data><Data>", "</Data>\n<Data>");
		System.out.println(result);
	} catch (Exception e) {
		e.printStackTrace();
		return "<?xml version=\"1.0\" encoding=\""+xmlCode+"\"?><Datas><Data><ITEM><result>状态成票失败</result></ITEM></Data></Datas>";
	}
	finally{
		if(is!=null){
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
		return result;
	}

	private void setZlxh(CardModel cm) {
		int num = 1;
		Set<String> set = new HashSet<String>();
		
		for(int i = 0; i < cm.getCardItems().size();i++) {
			String carditem = cm.getCardItems().get(i).getCardItem();
			
			String zlxh = "";
			if(!set.contains(carditem)&&!"".equals(carditem)){
				set.add(carditem);
				num = 1;
			}else if(set.contains(carditem)){
				num += 1;
			}
			
			zlxh = String.valueOf(num);
			
			cm.getCardItems().get(i).setZlxh(String.valueOf(zlxh));
		}
	}

	public static String getXMLcode(String str){
		if(str.toUpperCase().contains("UTF-8")){
			return "UTF-8";
		}else{
			return "GBK";
		}
	}

	public static String getNotNullString(Object obj){
		return obj==null?"":obj.toString().trim();	
	}
	
	
	private void getTicket(PowerDevice pd,String operation,RuleBaseMode rbm,Element datas){
		String statecode = "";
		String sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where cardbuildtype='"+CBSystemConstants.cardbuildtype+"' and t.opcode='"+CBSystemConstants.opCode+"' and t.devicetypeid='"+pd.getDeviceType()+"' and t.statename = '"+operation+"' and isLock = '0'";
		List<Map<String, Object>> results = DBManager.queryForList(sql);
		if(results.size()>0) {
			Map<String, Object> temp=(Map<String, Object>)results.get(0);
			statecode = StringUtils.ObjToString(temp.get("statecode"));
		}
		rbm.setStateCode(statecode);
		
		CardModel cm = new CardModel();
		cm.setCardItems(new ArrayList<CardItemModel>());
		
		if(!rbm.getPd().getDeviceStatus().equals(rbm.getBeginStatus())) {
			System.out.println("------------------开始执行状态重置规则---------------------");
			System.out.println("设备现状态为：{"+rbm.getPd().getDeviceStatus()+"}");
			System.out.println("设备重置后的状态为：{"+rbm.getBeginStatus()+"}");
			RuleExeUtil.deviceStatusChange(rbm.getPd(),rbm.getPd().getDeviceStatus(),rbm.getBeginStatus());
		}
		
		if(CBSystemConstants.globalSlaveDevList.size()>0){
			for(int i=0;i<CBSystemConstants.globalSlaveDevList.size();i++){
				Map<String,String> map = CBSystemConstants.globalSlaveDevList.get(i);
				PowerDevice dev = CBSystemConstants.getPowerDevice(StringUtils.ObjToString(map.get("equipid")));
				String begin = StringUtils.ObjToString(map.get("beginstatus"));
				
				if(!begin.equals(dev.getDeviceStatus())){
					RuleExeUtil.deviceStatusChange(dev,dev.getDeviceStatus(),begin);
				}
			}
		}
		
		DeviceOperate.getAlltransDevMap().clear();
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.LineTagStatus.clear();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		
		for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = it2.next();
			if ((dev.getDeviceType().equals(SystemConstants.Switch)||dev.getDeviceType().equals(SystemConstants.MotherLine)||dev.getDeviceType().equals(SystemConstants.PowerTransformer)||dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) 
					&& dev.getPowerVoltGrade()==pd.getPowerVoltGrade()) {
				System.out.print("厂站:"+dev.getPowerStationName()+"|设备:"+dev+"|状态:"+dev.getDeviceStatus()+"\n");
			}
		}
		
		CBSystemConstants.setCurRBM(rbm);
		RuleExecute ruleExc=new RuleExecute();
		ruleExc.execute(rbm);
		
		DeviceOperate doe = new DeviceOperate();
		doe.setTask(cm);

		CardModel cardModel=WordExecute.getInstance().execute(rbm);
		for(int i=0;i<cardModel.getCardItems().size();i++){//清除空指令
			if(cardModel.getCardItems().get(i).getCardDesc().equals("")){
				cardModel.getCardItems().remove(i);
				i--;
			}
		}
		cm.getCardItems().addAll(cardModel.getCardItems());
		Element data = datas.addElement("Data");
		
		if(!cardModel.getCzrw().equals("") || cardModel.getCardItems().size() > 0) {
			Element rw  = data.addElement("ITEM");
			rw.addElement("czrw").setText(cardModel.getCzrw());
		}else{
			if(rbm.getBeginStatus().equals(rbm.getEndState())){
				Element rw  = data.addElement("ITEM");
				rw.addElement("result").setText("成票失败！原因：设备初始状态和目标状态一致");
			}else{
				Element rw  = data.addElement("ITEM");
				rw.addElement("result").setText("成票失败！请检查页面参数");
			}
		}
		
		setZlxh(cm);
		
		for(int i = 0; i < cm.getCardItems().size();i++) {
			Element item= data.addElement("ITEM");
			item.addElement("carditem").setText(cm.getCardItems().get(i).getCardItem());
			item.addElement("cardorder").setText(String.valueOf(i+1));
			item.addElement("czdw").setText(cm.getCardItems().get(i).getStationName());
			String cardDesc=cm.getCardItems().get(i).getCardDesc();
			item.addElement("cznr").setText(cardDesc);
			item.addElement("czsn").setText(cm.getCardItems().get(i).getShowName());
			item.addElement("zlxh").setText(cm.getCardItems().get(i).getZlxh());
			item.addElement("czdwid").setText(cm.getCardItems().get(i).getBzbj());
		}
	}
	
	
	public boolean kgjx() {
		if(CBSystemConstants.globalSlaveDevList.size()>0){
			boolean flag = false;
			for(Map<String, String> map:CBSystemConstants.globalSlaveDevList){
				if(StringUtils.ObjToString(map.get("beginstatus")).equals("3")||StringUtils.ObjToString(map.get("endstatus")).equals("3")){
					flag = true;
					break;
				}
			}
			
			if(flag){
				return true;
			}else{
				return false;
			}
		}else{
			return false;
		}
	}
}
