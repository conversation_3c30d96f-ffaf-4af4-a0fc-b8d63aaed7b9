package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.WordExecute;

public class GetOperationSequence {
	
	public static String execute(String arg) {
		
		
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.cardbuildtype = "0";
		CBSystemConstants.roleCode = "0";
		CBSystemConstants.opCode = "0";
		CBSystemConstants.opRuleCode = "0";
		CBSystemConstants.jh_tai = 0;
		
		CZPService.getService().setArg("");
		StartupManager.startup();
		
		System.out.println("**********输入参数**********");
		System.out.println(arg);
		System.out.println("***************************");
		String xmlCode = "";
		if(arg.toUpperCase().contains("UTF-8")){
			xmlCode = "UTF-8";
		}else{
			xmlCode = "GBK";
		}
		
		/**
		 * 构造返回的xml。
		 * */
		Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding(xmlCode);
		Element datas=doc.addElement("Datas");
		
		/**
		 * 解析传入的xml。
		 * */
		//传入参数数据
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		InputStream is =null;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			
			
			//如果传了规则ID，给全局变量赋值（role参数0为主网，1为配网）
			List<Element> roleLists =root.elements("role");
			if(roleLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				CBSystemConstants.roleCode = roleLists.get(0).getTextTrim();
			}
			
			
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("ITEM");
			//System.out.println(itemLists);
			for (int i = 0; i <itemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				list.add(mapInfo);
			}
			
			//根据厂站和操作指令获取的报文内容
			List<Map<String, String>> msglist = new ArrayList<Map<String, String>>();
			for(Map<String, String> map : list){
				List<Map<String, String>> data = getCheckDataList(StringUtils.ObjToString(map.get("stationname")),StringUtils.ObjToString(map.get("caozuozhiling")),StringUtils.ObjToString(map.get("cbid")));
				msglist.addAll(data);
			}
			//msglist转换成xml格式输出
			for(int i = 0; i < msglist.size();i++) {
				Element item=datas.addElement("ITEM");
				item.addElement("stationname").setText(msglist.get(i).get("stationname"));
				item.addElement("stationid").setText(msglist.get(i).get("stationid"));
				item.addElement("devname").setText(msglist.get(i).get("devname"));
				item.addElement("devid").setText(msglist.get(i).get("devid"));
				item.addElement("beginstatus").setText(msglist.get(i).get("beginstatus"));
				item.addElement("endstatus").setText(msglist.get(i).get("endstatus"));
				item.addElement("cbid").setText(StringUtils.ObjToString(msglist.get(i).get("cbid")));
				item.addElement("devtype").setText(StringUtils.ObjToString(msglist.get(i).get("devtype")));
				item.addElement("devvol").setText(StringUtils.ObjToString(msglist.get(i).get("devvol")));
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally{
			CBSystemConstants.getDtdMap().clear();
			DeviceOperate.getAlltransDevMap().clear();
			if(is!=null){
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		
		/**
		 * 返回校核结果。
		 * */
		System.out.println("**********输出结果**********");
		System.out.println(doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>"));
		System.out.println("***************************");
		return doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
	}
	
	/*
	 * 根据厂站名和操作指令，获取南瑞电网所需要的文件内容
	 */
	private static List<Map<String, String>> getCheckDataList(String stationname,String caozuozhiling,String cbid){
		List<Map<String, String>> msglist = new ArrayList<Map<String, String>>();
		
		CZPService cZPService = CZPService.getService();
		List<RuleBaseMode> rbmList = cZPService.getRBMList(stationname, caozuozhiling);
		if(rbmList==null){
			return msglist;
		}
		
		
		
		for(RuleBaseMode rbm:rbmList){
			if(rbm.getPd() == null)
				continue;
			
			if(rbm.getBeginStatus().equals("")){//临沂检查遥信合位、分位判断
				Map<String, String> msgvo =new HashMap<String, String>();
				msgvo.put("stationname", rbm.getPd().getPowerStationName());
				msgvo.put("stationid", rbm.getPd().getPowerStationID());
				msgvo.put("devname", rbm.getPd().getPowerDeviceName());
				msgvo.put("devid", rbm.getPd().getPowerDeviceID());
				msgvo.put("cbid", cbid);
				msgvo.put("endstatus", rbm.getEndState().equals("0")?"1":"0");
				msgvo.put("beginstatus", "");
				if(rbm.getPd().getDeviceType().equals(SystemConstants.Switch)){
					msgvo.put("devtype", "开关");
				} else if(rbm.getPd().getDeviceType().equals(SystemConstants.SwitchSeparate)){
					msgvo.put("devtype", "刀闸");
				} else if(rbm.getPd().getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
					msgvo.put("devtype", "接地刀闸");
				}
				msgvo.put("devvol", (int)rbm.getPd().getPowerVoltGrade()+"");
				msglist.add(msgvo);
				return msglist;
			}
			
			if(!rbm.getPd().getDeviceStatus().equals(rbm.getBeginStatus()))
				RuleExeUtil.deviceStatusReset(rbm.getPd(), rbm.getPd().getDeviceStatus(), rbm.getBeginStatus());
			CBSystemConstants.getDtdMap().clear();
//			DeviceOperate.getAlltransDevMap().clear();
			
			//rbm.getPd().setDeviceStatus(rbm.getBeginStatus());
			CBSystemConstants.setCurRBM(rbm);
			RuleExecute ruleExc=new RuleExecute();
			ruleExc.execute(rbm);
//			if(CBSystemConstants.getDtdMap()==null){
//				CBSystemConstants.getDtdMap().clear();
//				DeviceOperate.getAlltransDevMap().clear();
//				continue;
//			}
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				PowerDevice dev = dtd.getTransDevice();
				if(dtd.getBeginstatus().equals("2")||dtd.getBeginstatus().equals("3")){
					continue;
				}
				if(dtd.getEndstate().equals("2")||dtd.getEndstate().equals("3")){
					continue;
				}
				if((dev.getDeviceType().equals(SystemConstants.Switch)&&(dtd.getBeginstatus().equals("0")||dtd.getEndstate().equals("0")))
						||dev.getDeviceType().equals(SystemConstants.SwitchSeparate)
						||dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
					Map<String, String> msgvo =new HashMap<String, String>();
					msgvo.put("stationname", dev.getPowerStationName());
					msgvo.put("stationid", dev.getPowerStationID());
					msgvo.put("devname", dev.getPowerDeviceName());
					msgvo.put("devid", dev.getPowerDeviceID());
					msgvo.put("cbid", cbid);
//					if(caozuozhiling.contains("（充电）")){
//						dtd.setEndstate("4");
//					}
					msgvo.put("endstatus", dtd.getEndstate().equals("0")?"1":"0");
					msgvo.put("beginstatus", dtd.getBeginstatus().equals("0")?"1":"0");
					if(dev.getDeviceType().equals(SystemConstants.Switch)){
						msgvo.put("devtype", "开关");
					} else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						msgvo.put("devtype", "刀闸");
					} else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
						msgvo.put("devtype", "接地刀闸");
					}
					msgvo.put("devvol", (int)dev.getPowerVoltGrade()+"");
					msglist.add(msgvo);
				}
			}
			for (int i = CBSystemConstants.getDtdMap().size(); i>0; i--) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				PowerDevice dev = dtd.getTransDevice();
				dev.setDeviceStatus(dtd.getBeginstatus());
			}
			CBSystemConstants.getDtdMap().clear();
			DeviceOperate.getAlltransDevMap().clear();
		}
		return msglist;
	}

}
