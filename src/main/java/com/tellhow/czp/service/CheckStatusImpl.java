package com.tellhow.czp.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.lowagie.text.pdf.PdfAcroForm;
import com.tellhow.czp.operationcard.EchoReplace;
import com.tellhow.czp.service.OperationCheckDefault;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

public class CheckStatusImpl {
	public static String getXMLcode(String str){
		if(str.toUpperCase().contains("UTF-8")){
			return "UTF-8";
		}else{
			return "GBK";
		}
	}
	
	public static Element getItemByCbid(Element datas, String cbid) {
		Element target=null;
		for (Element element : (List<Element>)datas.elements()) {
			Element cbElement=element.element("cbid");
			if(cbElement!=null){
				String checkid=cbElement.getTextTrim();
				if(checkid.equals(cbid)){
					target=element;
				}
			}
		}
		return target;
	}
	
	public String execute(String arg){
		System.out.println("**********输入参数**********");
		System.out.println(arg);
		System.out.println("***************************");
		
		/**
		 * 设置系统运行方式为校核类型。
		 * */
		CBSystemConstants.isCurrentSys=false;
		
		String xmlCode = getXMLcode(arg);
		
		/**
		 * 构造返回的xml。
		 * */
		Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding(xmlCode);
		Element datas=doc.addElement("Datas");
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		/**
		 * 解析传入的xml。
		 * */
		InputStream is =null;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("ITEM");
			//System.out.println(itemLists);
			for (int i = 0; i <itemLists.size(); i++) {
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				Map<String, String> mapInfo =new HashMap<String,String>();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				list.add(mapInfo);
			}
			
			List<Element> qymbLists =root.elements("AREANO");
			if(qymbLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				CBSystemConstants.qybm = qymbLists.get(0).getTextTrim();
			}
			
			Element resultItem=datas.addElement("RESULT");
			resultItem.addElement("message").setText("成功");
			
			/**
			 * 解析
			 **/
			for(int i=0;i<list.size();i++){
				Map<String, String> map=list.get(i);
				String station=map.get("changzhan");
				String opr=map.get("caozuozhiling");
				String cbid=map.get("cbid");
				
				if(map.containsKey("ischeck")){
					String ischeck=map.get("ischeck");

					if(ischeck.equals("0")){
						continue;
					}
				}
				
				List<RuleBaseMode> rbmlist=OperationCheckDefault.execute(station,opr);
				//加载厂站
				List<String> loadStationList = new ArrayList<String>();
				//实时状态加载
				for(RuleBaseMode rbm : rbmlist) {
					if(rbm.getPd() != null) {
						String stationID = rbm.getPd().getPowerStationID();
						if(!stationID.equals("")) {
							if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
								CreatePowerStationToplogy.loadFacEquip(stationID);
							}
							if(!loadStationList.contains(stationID)) {
								InitDeviceStatus ie = new InitDeviceStatus();
								ie.initStatus_EMSToCache(stationID);
								loadStationList.add(stationID);
							}
						}
					}
				}
				
				List<Map<String,String>> messageList = new ArrayList<Map<String,String>>();
				
				for(RuleBaseMode rbm:rbmlist){
					EchoReplace ec = new EchoReplace();

					if(rbm.getPd()!=null){
						//解析出的结束状态为空，返回指令不需要返校
						if(rbm.getEndState().equals("")||rbm.getEndState()==null){
							CheckMessage cm=new CheckMessage();
							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
							pdlist.add(rbm.getPd());
							cm.setBottom("305");
							cm.setPd(pdlist);
							
							Map<String,String> resultMap = ec.getEchoMap(cm, rbm.getPd());
							messageList.add(resultMap);
						}else{
							if(CBSystemConstants.isCheckTelemetry){
								PowerDevice dev = rbm.getPd();
								
								if(dev.getDeviceType().equals(SystemConstants.Switch)){
									List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
									List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
									
									String deviceID = "";
									
									if(lineList.size() == 1){
										deviceID = lineList.get(0).getCimID();
									}else if(zbList.size() == 1){
										String sql = "SELECT A.ID From "+CBSystemConstants.equipUser+"T_M_TRANSFORMERWINDING A,"+CBSystemConstants.equipUser+"T_VOLTAGELEVEL B "
												+ "WHERE A.MEMBEROF_POWERTRANSFORMER = '"+zbList.get(0).getPowerDeviceID()+"' AND A.BASEVOLTAGE = B.VOLTAGE_ID AND B.VOLTAGE_NAME = '"+(int)dev.getPowerVoltGrade()+"'";
										
										List<Map<String,String>> idList = DBManager.queryForList(sql);
										
										if(idList.size() > 0){
											deviceID = idList.get(0).get("ID");
										}
									}else{
										deviceID = dev.getPowerDeviceID();
									}
									
									String sql = "SELECT B.VALUE FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT A,"+CBSystemConstants.equipUser+"T_S_ANALOGVALUE B "
											+ "WHERE A.ID = B.ID AND A.MEMBEROF_PSR = '"+deviceID+"'  AND A.MEASUREMENTTYPE = '41550' "
											+ "AND A.NAME NOT LIKE '%T接站%'";
									
									List<Map<String,String>> valueList = DBManager.queryForList(sql);
									
									//合上后的电流值大于0.5A算成功，断开后的电流值小于0.5A算成功。
									if(rbm.getBeginStatus().equals("0")){//断开开关校核
										for(Map<String,String> valueMap : valueList){
											String value = valueMap.get("VALUE");
											
											if(!value.equals("")){
												if(Double.valueOf(value) >= 0.5){
													CheckMessage cm=new CheckMessage();
													List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
													pdlist.add(rbm.getPd());
													cm.setBottom("494");
													cm.setPd(pdlist);
													Map<String,String> resultMap = ec.getEchoMap(cm, rbm.getPd());
													messageList.add(resultMap);
												}
											}
										}
									}else if(rbm.getEndState().equals("0")){//合上开关校核
										for(Map<String,String> valueMap : valueList){
											String value = valueMap.get("VALUE");
											
											if(!value.equals("")){
												if(Double.valueOf(value) <= 0.5){
													CheckMessage cm=new CheckMessage();
													List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
													pdlist.add(rbm.getPd());
													cm.setBottom("493");
													cm.setPd(pdlist);
													Map<String,String> resultMap = ec.getEchoMap(cm, rbm.getPd());
													messageList.add(resultMap);
												}
											}
										}
									}
								}
							}
							
							
							if(rbm.getEndState().equals(rbm.getPd().getDeviceStatus()) ||
									(Integer.valueOf(rbm.getBeginStatus())>Integer.valueOf(rbm.getEndState()) && Integer.valueOf(rbm.getEndState())>Integer.valueOf(rbm.getPd().getDeviceStatus())) ||
									(Integer.valueOf(rbm.getBeginStatus())<Integer.valueOf(rbm.getEndState()) && Integer.valueOf(rbm.getEndState())<Integer.valueOf(rbm.getPd().getDeviceStatus())) ){
								
								
							}else{
								CheckMessage cm=new CheckMessage();
								List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
								pdlist.add(rbm.getPd());
								cm.setBottom("492");
								cm.setPd(pdlist);
								
								Map<String,String> resultMap = ec.getEchoMap(cm, rbm.getPd());
								String message = resultMap.get("msg");

								//拼接设备状态不符的详细信息
								if(message.contains("目标状态与操作指令不一致")){
									String deviceStatus = rbm.getPd().getDeviceStatus();
									message += "，当前设备状态："+RuleExeUtil.getStatusNew(rbm.getPd().getDeviceType(), deviceStatus)+"，请确认！";
								}
								
								resultMap.put("msg", message);
								messageList.add(resultMap);
							}
						}
					}else {
						CheckMessage cm=new CheckMessage();
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(rbm.getPd());
						cm.setBottom("302");
						cm.setPd(pdlist);
						Map<String,String> resultMap = ec.getEchoMap(cm, rbm.getPd());
						messageList.add(resultMap);
					}
				}
				
				StringBuffer cardwordcheck = new StringBuffer();
				StringBuffer statuscheck = new StringBuffer();

				 Map<String,String> remessageMap = new HashMap<String, String>();
				
				remessageMap.put("cardwordcheckresult", "成功");
				remessageMap.put("cardwordcheckinfo", "成功");
				remessageMap.put("cardwordcheckcode", "0");

				remessageMap.put("statuscheckresult", "成功");
				remessageMap.put("statuscheckinfo", "成功");
				remessageMap.put("statuscheckcode", "0");

				remessageMap.put("topologycheckresult", "-");
				remessageMap.put("topologycheckinfo", "-");
				remessageMap.put("topologycheckcode", "-");

				for(int j=0 ; j < messageList.size() ; j++){
					Map<String,String> tempMap = messageList.get(j);
					
					String message = tempMap.get("msg");
					String code = tempMap.get("code");
					String kind = tempMap.get("kind");
					
					if(kind.equals("0")){
						cardwordcheck.append(message+"；");
						remessageMap.put("cardwordcheckresult", "失败");
						remessageMap.put("cardwordcheckcode", code);
					}else if(kind.equals("1")){
						statuscheck.append(message+"；");
						remessageMap.put("statuscheckresult", "失败");
						
						if(remessageMap.get("statuscheckcode").equals("2")&&code.equals("1")){
							
						}else{
							remessageMap.put("statuscheckcode", code);
						}
					}
				}
				
				if(cardwordcheck.length() == 0){
					cardwordcheck.append("成功");
				}
				
				if(statuscheck.length() == 0){
					statuscheck.append("成功");
				}
				
				remessageMap.put("cardwordcheckinfo", cardwordcheck.toString());
				remessageMap.put("statuscheckinfo", statuscheck.toString());

				if(CBSystemConstants.lcm != null){
					CBSystemConstants.lcm.clear();
				}
				
				/**
				 * 拼接校核信息。
				 * */
				Element item = getItemByCbid(datas,cbid);
				if(item==null){
					item=datas.addElement("ITEM");
					item.addElement("changzhan").setText(station);
					item.addElement("caozuozhiling1").setText(opr);
					item.addElement("cbid").setText(cbid);
					
					if(!StringUtils.ObjToString(map.get("zbid")).equals("")){
						item.addElement("zbid").setText(StringUtils.ObjToString(map.get("zbid")));
					}
		            
	            	String cardwordcheckresult = remessageMap.get("cardwordcheckresult");
	            	String cardwordcheckinfo = remessageMap.get("cardwordcheckinfo");
	            	String cardwordcheckcode = remessageMap.get("cardwordcheckcode");

	            	String statuscheckresult = remessageMap.get("statuscheckresult");
	            	String statuscheckinfo = remessageMap.get("statuscheckinfo");
	            	String statuscheckcode = remessageMap.get("statuscheckcode");

	            	String topologycheckresult = remessageMap.get("topologycheckresult");
	            	String topologycheckinfo = remessageMap.get("topologycheckinfo");
	            	String topologycheckcode = remessageMap.get("topologycheckcode");

	            	item.addElement("cardwordcheckresult").setText(cardwordcheckresult);
	            	item.addElement("cardwordcheckinfo").setText(cardwordcheckinfo);
	            	item.addElement("cardwordcheckcode").setText(cardwordcheckcode);

	            	item.addElement("statuscheckresult").setText(statuscheckresult);
	            	item.addElement("statuscheckinfo").setText(statuscheckinfo);
	            	item.addElement("statuscheckcode").setText(statuscheckcode);

	            	item.addElement("topologycheckresult").setText(topologycheckresult);
	            	item.addElement("topologycheckinfo").setText(topologycheckinfo);
	            	item.addElement("topologycheckcode").setText(topologycheckcode);

	            	/*
	            	 * 海南字段
	            	 */
	            	/*if(cardwordcheckresult.equals("失败")){
		            	item.addElement("pmresult").setText(cardwordcheckinfo);
	            	}else{
	            		item.addElement("pmresult").setText("成功");
	            	}
	            	
	            	if(statuscheckresult.equals("失败")){
		            	item.addElement("ztresult").setText(statuscheckinfo);
	            	}else{
	            		item.addElement("ztresult").setText("成功");
	            	}
	            	
	            	if(topologycheckresult.equals("失败")){
		            	item.addElement("tpresult").setText(topologycheckinfo);
	            	}else{
	            		item.addElement("tpresult").setText("成功");
	            	}*/
	            	
	            	if(cardwordcheckresult.equals("成功")&&statuscheckresult.equals("成功")){
	    	            item.addElement("code").setText("0");
	                	item.addElement("remessage").setText("成功");
	            	}else{
	            		String remessage = "";
	            		
	            		if(cardwordcheckresult.equals("失败")){
	            			remessage += cardwordcheckinfo;
	            		}
	            		
	            		if(statuscheckresult.equals("失败")){
	            			remessage += statuscheckinfo;
	            		}
	            		
	            		item.addElement("code").setText("2");
	                	item.addElement("remessage").setText(remessage);
	            	}
				}
				datas.addText("\r\n");
			}
			/**
			 * 返回校核结果。
			 * */
			System.out.println("**********输出结果**********");
			System.out.println(doc.asXML());
			System.out.println("***************************");
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally{
			if(is!=null){
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		
		return doc.asXML();
	}
}
