package com.tellhow.czp.app;

import java.awt.BorderLayout;
import java.awt.Container;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Frame;
import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFileChooser;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.border.Border;

import com.tellhow.common.spring.BeanFactory;
import com.tellhow.resource.cim.convert.CIMDataExchange;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2010-5-13 下午02:41:12 
 */
public class ConvertCIMDialog extends JDialog {

	private static final long serialVersionUID = 1L;

	private JPanel jContentPane = null;

	private JPanel workPanel = null;

	private JButton chooseButton = null;

	private JTextField xmlTextField = null;

	private JButton importButton = null;
	
	private JButton  updateButton = null;
	
	private Container parent;
	

	/**
	 * @param owner
	 */
	public ConvertCIMDialog(Frame owner) {
		super(owner, true);
		initialize();
	}
	
	/**
	 * 选取并修改index path
	 * 
	 * 2008-8-11
	 */
	private void ChooseXML() {
		String xmlPath = "";
		
		JFileChooser chooser = new JFileChooser(xmlPath);
		chooser.setDialogTitle("选择CIM数据文件");
        int returnVal = chooser.showOpenDialog(parent);
        if (returnVal == JFileChooser.APPROVE_OPTION) {
        	xmlPath = chooser.getSelectedFile().getAbsolutePath();
    		this.xmlTextField.setText(xmlPath);
        }
	}	
	
	private void handleXML(String xmlPath) {
    	if(xmlPath.equals("")) {
        	JOptionPane.showMessageDialog(this, "请选择CIM文件！", "提示",JOptionPane.ERROR_MESSAGE);
        	return;
    	}
    	else {
    		Map map = new HashMap();
    		map.put("inputPath", xmlPath);
    		map.put("dataSource", (DataSource)BeanFactory.getBean("tbp.sys.DataSource1"));
    		map.put("projectID", "czp");
    		try {
    			String result = CIMDataExchange.execute(map);
    			if(!result.equals(""))
    				JOptionPane.showMessageDialog(this, result, "提示",JOptionPane.ERROR_MESSAGE);
    			else
    				JOptionPane.showMessageDialog(this, "转换完成！", "提示",JOptionPane.INFORMATION_MESSAGE);
    		} catch (Exception e) {
    			e.printStackTrace();
    			JOptionPane.showMessageDialog(this, e.getMessage(), "提示",JOptionPane.ERROR_MESSAGE);
    		}
    	}
	}

	/**
	 * This method initializes this
	 * 
	 * @return void
	 */
	private void initialize() {
		this.setTitle("CIM更新");
		this.setSize(480, 200);
		this.add(getJContentPane());
	}

	/**
	 * This method initializes jContentPane
	 * 
	 * @return javax.swing.JPanel
	 */
	private JPanel getJContentPane() {
		if (jContentPane == null) {
			jContentPane = new JPanel();
			
			Border titleBorder1 = BorderFactory.createTitledBorder("导入CIM文件");
			jContentPane.setBorder(titleBorder1);
			
			jContentPane.setLayout(new BorderLayout());
			jContentPane.add(getWorkPanel(), BorderLayout.CENTER);
		}
		return jContentPane;
	}

	/**
	 * This method initializes workPanel	
	 * 	
	 * @return javax.swing.JPanel	
	 */
	private JPanel getWorkPanel() {
		if (workPanel == null) {
			workPanel = new JPanel();
			workPanel.setLayout(new FlowLayout());
			workPanel.add(getXmlTextField(), null);
			workPanel.add(getChooseButton(), null);
			workPanel.add(getImportButton(), null);
		}
		return workPanel;
	}

	/**
	 * This method initializes chooseButton	
	 * 	
	 * @return javax.swing.JButton	
	 */
	private JButton getChooseButton() {
		if (chooseButton == null) {
			chooseButton = new JButton();
			chooseButton.setText("选择文件");
			chooseButton.setName("chooseButton");
			chooseButton.setPreferredSize(new Dimension(90, 30)); 
			chooseButton.addActionListener(new java.awt.event.ActionListener() {
				public void actionPerformed(java.awt.event.ActionEvent e) {
					ChooseXML();
				}
			});
		}
		return chooseButton;
	}

	/**
	 * This method initializes xmlTextField	
	 * 	
	 * @return javax.swing.JTextField	
	 */
	private JTextField getXmlTextField() {
		if (xmlTextField == null) {
			xmlTextField = new JTextField();
			xmlTextField.setPreferredSize(new Dimension(380, 30));
			xmlTextField.setName("xmlTextField");
		}
		return xmlTextField;
	}

	/**
	 * This method initializes importButton	
	 * 	
	 * @return javax.swing.JButton	
	 */
	private JButton getImportButton() {
		if (importButton == null) {
			importButton = new JButton();
			importButton.setText("导入模型");
			importButton.setName("importButton");
			importButton.setPreferredSize(new Dimension(90, 30));
			importButton.addActionListener(new java.awt.event.ActionListener() {
				public void actionPerformed(java.awt.event.ActionEvent e) {
					handleXML(ConvertCIMDialog.this.xmlTextField.getText());
				}
			});
		}
		return importButton;
	}
	
	public static void main(String[] args)
	{
		new ConvertCIMDialog(null).setVisible(true);
	}


	
}  //  @jve:decl-index=0:visual-constraint="10,10"
