package com.tellhow.czp.app;

import java.awt.Toolkit;
import java.io.File;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.swing.JDialog;
import javax.swing.JFileChooser;
import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableModel;

import org.jfree.report.Element;
import org.jfree.report.JFreeReport;
import org.jfree.report.JFreeReportBoot;
import org.jfree.report.ReportProcessingException;
import org.jfree.report.filter.StaticDataSource;
import org.jfree.report.modules.gui.base.PreviewDialog;
import org.jfree.report.modules.gui.pdf.PDFExportTask;
import org.jfree.report.modules.parser.base.ReportGenerator;

import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-9-28 下午04:13:46 
 */
public class PrintOperationCard {

	JFrame frame = null;
	JDialog dialog = null;
    JFreeReport report;
    
    public PrintOperationCard(JFrame parent, String[] zb, List<BaseCardModel> mx) {
        this.frame = parent;
        init(zb, mx);
    }
    
    public PrintOperationCard(JDialog parent, String[] zb, List<BaseCardModel> mx) {
        this.dialog = parent;
        init(zb, mx);
    }
    
    public void init(String[] zb, List<BaseCardModel> mx) {
    	this.id = zb[0];
        this.cardKind = Integer.valueOf(zb[8]);
        this.unit = "";
        this.time = "时间";
        this.no = zb[1];
        this.task = zb[2];
        this.xgsx = "备注测试";
        if(zb.length>12){
        	this.xgsx = zb[12];
        }
        this.remark = "";
        this.npr = "";

        TableModel data = null;
        if (cardKind == 0) {
        	unit = mx.get(0).getStationName();
            data = getColligateData(mx);
            report = getColligateDef(this.unit, this.no, this.task, this.remark, this.npr,this.xgsx);
        } else if (cardKind == 3){
            data = getSubentryData(mx);
            report = getMonitorDef(this.no, this.task, this.remark, this.npr);
        }else {//新投票
            data = getSubentryDataNewVote(mx);
            report = getSubentryDef(this.no, this.task, this.remark, this.npr);
        }

        // 将报表定义和数据结合
        report.setData(data);
    }

    /**
     * 打印预览
     */
    public void PrintPreview() {
        try {
            // 将生成的报表放到预览窗口中
        	if(frame != null)
        		preview = new PreviewDialog(report, frame);
        	else if(dialog != null)
        		preview = new PreviewDialog(report, dialog);
            preview.pack();
            // 显示报表预览窗口
            preview.setVisible(true);
            preview.setModal(true);
            int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
            int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
            preview.setBounds((w - preview.getSize().width) / 2, 0, preview.getSize().width, h - 50);
        } catch (ReportProcessingException e) {
            System.out.println(e);
        }
    }

    /**
     * 导出PDF
     */
    public void ExportPDF() {
        JFileChooser fc = new JFileChooser(".");
        fc.setDialogTitle("保存文件");
        fc.setFileFilter(new javax.swing.filechooser.FileFilter() {

            public boolean accept(File f) {
                if (f.getName().toLowerCase().endsWith(".pdf") || f.isDirectory()) {
                    return true;
                }
                return false;
            }

            public String getDescription() {
                return "PDF文件(*.pdf)";
            }
        });
        String defaultFileName = "操作票";
        if (this.no != null && this.no != "") {
            defaultFileName = this.no;
        }
        fc.setSelectedFile(new File(defaultFileName));
        int choice = fc.showSaveDialog(frame);
        if (choice == JFileChooser.APPROVE_OPTION) {
            File file = fc.getSelectedFile();
            if (!file.getName().toLowerCase().endsWith(".pdf")) {
                file = new File(file.toString() + ".pdf");
            }
            PDFExportTask export = new PDFExportTask(file.getPath(), null, report);
            export.run();
            JOptionPane.showMessageDialog(null, "导出成功！", "提示", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    public static void startInstance() {
    	JFreeReportBoot.getInstance().start();
    }
    
    /**
     * 导出PDF
     */
    public void ExportPDF(String path) {
    	
    	
       
       
        PDFExportTask export = new PDFExportTask(path, null, report);
        export.run();
    }

    /**
     * 创建生成报表需要用到的数据
     * 
     * @返回一个TableModel实例
     */
    private TableModel  getSubentryDataNewVote(List<BaseCardModel> tableModel) {

        int rowOfPage = 23;
        if(tableModel.size()>23){
        	rowOfPage = tableModel.size();
        }
        final int charOfLine = 46;
        final Object[] columnNames = new String[]{"page", "num", "unit",
            "step", "operation", "flsj", "slr", "hbsj", "hbr"
        };
        int rowCount = tableModel.size();

        final DefaultTableModel result = new DefaultTableModel(columnNames,
                rowCount);
        int pagenum = 1;
        int row = 0;
        int num = 0;
        int step = 0;
        String rowContent = "";
        String content = "";
        String oldUnit = "";
        String newUnit = "";
        int oldPage = 1;
        int oldJd = 0;
        int newJd = 0;
        for (int i = 0; i < rowCount; i++, row++) {
            oldPage = pagenum;
           // pagenum = (int) row / rowOfPage + 1;
            newUnit = tableModel.get(i).getStationName();
            newJd = Integer.parseInt(tableModel.get(i).getCardSub());
            //操作顺序，场站过滤
            if (newJd==oldJd &&newUnit.equals(oldUnit) && oldPage == pagenum) {
                step++;
                newUnit = "";
            } else {
                step = 1;
                oldUnit = newUnit;
            }
            num =  Integer.parseInt(tableModel.get(i).getCardSub());
          //步骤过滤
            if(oldJd==num){
            	
            	num = 0;
            }
            else
            	 oldJd=num;
            
            result.setValueAt(pagenum, row, 0);
            result.setValueAt((num==0 ? "" :StringUtils.getZWSZ(num)), row, 1);
            result.setValueAt(newUnit, row, 2);
            result.setValueAt(step, row, 3);
            content = tableModel.get(i).getCardDesc();
            if (getStringLength(content) > charOfLine) {
                int strOfLine = getStringPos(content, charOfLine);
                rowContent = content.substring(0, strOfLine);
                content = content.substring(strOfLine);
            } else {
                rowContent = content;
                content = "";
            }
            result.setValueAt(rowContent, row, 4);
            result.setValueAt("", row, 5);
            result.setValueAt("", row, 6);
            result.setValueAt("", row, 7);
            result.setValueAt("", row, 8);
            while (!content.equals("")) {
                row++;
               // pagenum = (int) row / rowOfPage + 1;
                if (getStringLength(content) > charOfLine) {
                    int strOfLine = getStringPos(content, charOfLine);
                    rowContent = content.substring(0, strOfLine);
                    content = content.substring(strOfLine);
                } else {
                    rowContent = content;
                    content = "";
                }
                result.insertRow(row, new Object[]{pagenum, "", "", "",
                    rowContent, "", "", "", ""
                });
            }
        }
        int pageTotal = pagenum * rowOfPage-1;
        for (; row < pageTotal; row++) {
            result.addRow(new Object[]{pagenum, "", "", "", "", "", "", "",
                ""
            });
        }
        return result;


    } 
   

    /**
     * 创建生成报表需要用到的数据
     * 
     * @返回一个TableModel实例
     */
    
    private TableModel getSubentryData(List<BaseCardModel> tableModel) {

        int rowOfPage = 23;
        if(tableModel.size()>23){
        	rowOfPage = 23+((tableModel.size()%23));
        }
        final int charOfLine = 46;
        final Object[] columnNames = new String[]{"page", "num", "unit",
            "step", "operation", "flsj", "slr", "hbsj", "hbr"
        };
        int rowCount = tableModel.size();

        final DefaultTableModel result = new DefaultTableModel(columnNames,
                rowCount);
        int pagenum = 1;
        int row = 0;
        int num = 0;
        int step = 0;
        String rowContent = "";
        String content = "";
        String oldUnit = "";
        String newUnit = "";
        int oldPage = 1;
        int oldJd = 0;
        int newJd = 0;
        for (int i = 0; i < rowCount; i++, row++) {
            oldPage = pagenum;
            pagenum = (int) row / rowOfPage + 1;
            newUnit = tableModel.get(i).getStationName();
            newJd = Integer.parseInt(tableModel.get(i).getCardSub());
            //操作顺序，场站过滤
            if (newJd==oldJd &&newUnit.equals(oldUnit) && oldPage == pagenum) {
                step++;
                newUnit = "";
            } else {
                step = 1;
                oldUnit = newUnit;
            }
            num =  Integer.parseInt(tableModel.get(i).getCardSub());
          //步骤过滤
            if(oldJd==num){
            	
            	num = 0;
            }
            else
            	 oldJd=num;
            
            result.setValueAt(pagenum, row, 0);
            result.setValueAt((num==0 ? "" :StringUtils.getZWSZ(num)), row, 1);
            result.setValueAt(newUnit, row, 2);
            result.setValueAt(step, row, 3);
            content = tableModel.get(i).getCardDesc();
            if (getStringLength(content) > charOfLine) {
                int strOfLine = getStringPos(content, charOfLine);
                rowContent = content.substring(0, strOfLine);
                content = content.substring(strOfLine);
            } else {
                rowContent = content;
                content = "";
            }
            result.setValueAt(rowContent, row, 4);
            result.setValueAt("", row, 5);
            result.setValueAt("", row, 6);
            result.setValueAt("", row, 7);
            result.setValueAt("", row, 8);
            while (!content.equals("")) {
                row++;
                pagenum = (int) row / rowOfPage + 1;
                if (getStringLength(content) > charOfLine) {
                    int strOfLine = getStringPos(content, charOfLine);
                    rowContent = content.substring(0, strOfLine);
                    content = content.substring(strOfLine);
                } else {
                    rowContent = content;
                    content = "";
                }
                result.insertRow(row, new Object[]{pagenum, "", "", "",
                    rowContent, "", "", "", ""
                });
            }
        }
        int pageTotal = pagenum * rowOfPage-1;
        for (; row < pageTotal; row++) {
            result.addRow(new Object[]{pagenum, "", "", "", "", "", "", "",
                ""
            });
        }
        return result;

    }

    private TableModel getColligateData(List<BaseCardModel> tableModel) {
        final int rowOfPage = 18;
        final int charOfLine = 90;
        final Object[] columnNames = new String[]{"page", "note", "operation"};
        int rowCount = tableModel.size();
        final DefaultTableModel result = new DefaultTableModel(columnNames,
                rowCount);
        int pagenum = 1;
        int row = 0;
        String rowContent = "";
        String content = "";
        for (int i = 0; i < rowCount; i++, row++) {
            pagenum = (int) row / rowOfPage + 1;
            result.setValueAt(pagenum, row, 0);
            result.setValueAt("", row, 1);
            content = (i + 1) + "、" + tableModel.get(i).getCardDesc();
            if (getStringLength(content) > charOfLine) {
                int strOfLine = getStringPos(content, charOfLine);
                rowContent = content.substring(0, strOfLine);
                content = content.substring(strOfLine);
            } else {
                rowContent = content;
                content = "";
            }
            result.setValueAt(rowContent, row, 2);
            while (!content.equals("")) {
                row++;
                pagenum = (int) row / rowOfPage + 1;
                if (getStringLength(content) > charOfLine) {
                    int strOfLine = getStringPos(content, charOfLine);
                    rowContent = content.substring(0, strOfLine);
                    content = content.substring(strOfLine);
                } else {
                    rowContent = content;
                    content = "";
                }
                result.insertRow(row, new Object[]{pagenum, "", rowContent});
            }
        }
        int pageTotal = pagenum * rowOfPage-1;
        for (; row < pageTotal; row++) {
            result.addRow(new Object[]{pagenum, "", ""});
        }
        result.setValueAt("注意事项", 0, 1);
        return result;

    }

    /**
     * 创建一个报表定义
     * 
     * @返回一个报表定义实例
     */
    private JFreeReport getSubentryDef(String no, String task, String remark, String npr) {
        String configFiile = "config/PrintNewInvestment.xml";
        if(no.contains("启")||!CBSystemConstants.unitCode.equals("36000000000")){//针对江西新投票打印需求变更黄彩凤-20160331
        	configFiile = "config/PrintSubentry.xml";
        }
        JFreeReport report = null;

        try {
            report = ReportGenerator.getInstance().parseReport(configFiile);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Element eNo = report.getPageHeader().getElement("no");
        eNo.setDataSource(new StaticDataSource("编号：" + no));
        Element eTask = report.getPageHeader().getElement("task");
        eTask.setDataSource(new StaticDataSource(task));
        Element eRemark = report.getGroupByName("Group1").getFooter().getElement("remark");
        eRemark.setDataSource(new StaticDataSource(remark));
        Element eNpr = report.getGroupByName("Group1").getFooter().getElement("npr");
        eNpr.setDataSource(new StaticDataSource(npr));

        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;

        if (!CZPSJ.equals("")) {
            String[] weekDays = {"日", "一", "二", "三", "四", "五", "六"};
            try {
                date = format.parse(CZPSJ);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar ca = Calendar.getInstance();
            ca.setTime(date);
            String day = String.valueOf(ca.get(Calendar.DAY_OF_MONTH));
            String month = String.valueOf(ca.get(Calendar.MONTH) + 1);
            String year = String.valueOf(ca.get(Calendar.YEAR));
            int week = ca.get(Calendar.DAY_OF_WEEK) - 1;

            String czpsj = year + " 年 " + month + " 月 " + day + " 日     星期" + weekDays[week];
            Element eCZPSJ = report.getPageHeader().getElement("czpsj");
            eCZPSJ.setDataSource(new StaticDataSource(czpsj));
        }


        if (!NLSJ.equals("")) {
            try {
                date = format.parse(NLSJ);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar ca = Calendar.getInstance();
            ca.setTime(date);
            String day = String.valueOf(ca.get(Calendar.DAY_OF_MONTH));
            String month = String.valueOf(ca.get(Calendar.MONTH) + 1);
            String year = String.valueOf(ca.get(Calendar.YEAR));
            String nlsj = "拟令日期：  " + year + " 年 " + month + " 月 " + day + " 日";
            Element eNLSJ = report.getPageHeader().getElement("nlsj");
            eNLSJ.setDataSource(new StaticDataSource(nlsj));
        }

        if (!FLSJ.equals("")) {
            try {
                date = format.parse(FLSJ);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar ca = Calendar.getInstance();
            ca.setTime(date);
            String day = String.valueOf(ca.get(Calendar.DAY_OF_MONTH));
            String month = String.valueOf(ca.get(Calendar.MONTH) + 1);
            String year = String.valueOf(ca.get(Calendar.YEAR));
            String flsj = "发令日期：  " + year + " 年 " + month + " 月 " + day + " 日";
            Element eFLSJ = report.getPageHeader().getElement("flsj");
            eFLSJ.setDataSource(new StaticDataSource(flsj));
        }
        /*
        if (psi.FLR != null && !psi.FLR.equals("")) {
            Element eFLR = report.getGroupByName("Group1").getFooter().getElement("flr");
            eFLR.setDataSource(new StaticDataSource(psi.FLR));
        }
        if (psi.JHR != null && !psi.JHR.equals("")) {
            Element eJHR = report.getGroupByName("Group1").getFooter().getElement("jhr");
            eJHR.setDataSource(new StaticDataSource(psi.JHR));
        }
        if (psi.SCR != null && !psi.SCR.equals("")) {
            Element eSCR = report.getGroupByName("Group1").getFooter().getElement("scr");
            eSCR.setDataSource(new StaticDataSource(psi.SCR));
        }
		*/
        return report;

    }
    
    /**
     * 创建一个报表定义
     * 
     * @返回一个报表定义实例
     */
    private JFreeReport getMonitorDef(String no, String task, String remark, String npr) {

        String configFiile = "config/PrintMonitor.xml";
        JFreeReport report = null;

        try {
            report = ReportGenerator.getInstance().parseReport(configFiile);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Element eNo = report.getPageHeader().getElement("no");
        eNo.setDataSource(new StaticDataSource("编号：" + no));
        Element eTask = report.getPageHeader().getElement("task");
        eTask.setDataSource(new StaticDataSource(task));
        Element eRemark = report.getGroupByName("Group1").getFooter().getElement("remark");
        eRemark.setDataSource(new StaticDataSource(remark));
        Element eNpr = report.getGroupByName("Group1").getFooter().getElement("npr");
        eNpr.setDataSource(new StaticDataSource(npr));

        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;

        if (!CZPSJ.equals("")) {
            String[] weekDays = {"日", "一", "二", "三", "四", "五", "六"};
            try {
                date = format.parse(CZPSJ);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar ca = Calendar.getInstance();
            ca.setTime(date);
            String day = String.valueOf(ca.get(Calendar.DAY_OF_MONTH));
            String month = String.valueOf(ca.get(Calendar.MONTH) + 1);
            String year = String.valueOf(ca.get(Calendar.YEAR));
            int week = ca.get(Calendar.DAY_OF_WEEK) - 1;

            String czpsj = year + " 年 " + month + " 月 " + day + " 日     星期" + weekDays[week];
            Element eCZPSJ = report.getPageHeader().getElement("czpsj");
            eCZPSJ.setDataSource(new StaticDataSource(czpsj));
        }


        if (!NLSJ.equals("")) {
            try {
                date = format.parse(NLSJ);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar ca = Calendar.getInstance();
            ca.setTime(date);
            String day = String.valueOf(ca.get(Calendar.DAY_OF_MONTH));
            String month = String.valueOf(ca.get(Calendar.MONTH) + 1);
            String year = String.valueOf(ca.get(Calendar.YEAR));
            String nlsj = "拟令日期：  " + year + " 年 " + month + " 月 " + day + " 日";
            Element eNLSJ = report.getPageHeader().getElement("nlsj");
            eNLSJ.setDataSource(new StaticDataSource(nlsj));
        }

        if (!FLSJ.equals("")) {
            try {
                date = format.parse(FLSJ);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar ca = Calendar.getInstance();
            ca.setTime(date);
            String day = String.valueOf(ca.get(Calendar.DAY_OF_MONTH));
            String month = String.valueOf(ca.get(Calendar.MONTH) + 1);
            String year = String.valueOf(ca.get(Calendar.YEAR));
            String flsj = "发令日期：  " + year + " 年 " + month + " 月 " + day + " 日";
            Element eFLSJ = report.getPageHeader().getElement("flsj");
            eFLSJ.setDataSource(new StaticDataSource(flsj));
        }
        /*
        if (psi.FLR != null && !psi.FLR.equals("")) {
            Element eFLR = report.getGroupByName("Group1").getFooter().getElement("flr");
            eFLR.setDataSource(new StaticDataSource(psi.FLR));
        }
        if (psi.JHR != null && !psi.JHR.equals("")) {
            Element eJHR = report.getGroupByName("Group1").getFooter().getElement("jhr");
            eJHR.setDataSource(new StaticDataSource(psi.JHR));
        }
        if (psi.SCR != null && !psi.SCR.equals("")) {
            Element eSCR = report.getGroupByName("Group1").getFooter().getElement("scr");
            eSCR.setDataSource(new StaticDataSource(psi.SCR));
        }
		*/
        return report;

    }

    private JFreeReport getColligateDef(String unit, String no, String task, String remark, String npr,String xgsx) {

        String configFiile = "config/PrintColligate.xml";
        JFreeReport report = null;

        try {
            report = ReportGenerator.getInstance().parseReport(configFiile);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Element eUnit = report.getPageHeader().getElement("unit");
        eUnit.setDataSource(new StaticDataSource("操作单位：" + unit));
        Element eNo = report.getPageHeader().getElement("no");
        eNo.setDataSource(new StaticDataSource("编号：" + no));
        Element eTask = report.getPageHeader().getElement("task");
        Element lxgsx = report.getPageHeader().getElement("xgsx");
        Element txgsx = report.getPageHeader().getElement("txgsx");
        eTask.setDataSource(new StaticDataSource(task));
        if(lxgsx!=null){
        	lxgsx.setDataSource(new StaticDataSource(xgsx));
        }
        if(xgsx.equals("")){
        	//昌东变220kV南昌I线由旁路241开关倒回214开关运行，220kV旁母及旁路241开关由运行转冷备用
	        if((task.indexOf("旁路")>-1&&task.indexOf("倒回")>-1)||task.contains("旁母及旁路")){
	         	txgsx.setDataSource(new StaticDataSource("调度备注："));
	        }
        }else{
            if(xgsx.contains("或光纤差动主保护")){
            	txgsx.setDataSource(new StaticDataSource("调度备注："));
            }  
            //出票不规则增加判断《黄彩凤》
            if(xgsx.indexOf("光纤")>-1&&xgsx.indexOf("主保护")>-1){
            	txgsx.setDataSource(new StaticDataSource("调度备注："));
            }
        }
//        Element lxgsx = report.getPageHeader().getElement("xgsx");
//        Element txgsx = report.getPageHeader().getElement("txgsx");
//        eTask.setDataSource(new StaticDataSource(task));
//        lxgsx.setDataSource(new StaticDataSource(xgsx));
//        if(xgsx.contains("或光纤差动主保护")){
//        	txgsx.setDataSource(new StaticDataSource("调度备注："));
//        }
        Element eRemark = report.getGroupByName("Group1").getFooter().getElement("remark");
        eRemark.setDataSource(new StaticDataSource(remark));
        Element eNpr = report.getGroupByName("Group1").getFooter().getElement("npr");
        eNpr.setDataSource(new StaticDataSource(npr));

        if (!CZPSJ.equals("")) {
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date();
            try {
                date = format.parse(CZPSJ);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            String day = String.valueOf(cal.get(Calendar.DAY_OF_MONTH));
            String month = String.valueOf(cal.get(Calendar.MONTH) + 1);
            String year = String.valueOf(cal.get(Calendar.YEAR));
            int week = cal.get(Calendar.DAY_OF_WEEK) - 1;
            String[] weekDays = {"日", "一", "二", "三", "四", "五", "六"};
            String czpsj = year + " 年 " + month + " 月 " + day + " 日     星期" + weekDays[week];
            Element eCZPSJ = report.getPageHeader().getElement("czpsj");
            eCZPSJ.setDataSource(new StaticDataSource(czpsj));
        }
        /*
        if (pci.FLR != null && !pci.FLR.equals("")) {
            Element eFLR = report.getGroupByName("Group1").getFooter().getElement("flr");
            eFLR.setDataSource(new StaticDataSource(pci.FLR));
        }
        if (pci.JHR != null && !pci.JHR.equals("")) {
            Element eJHR = report.getGroupByName("Group1").getFooter().getElement("jhr");
            eJHR.setDataSource(new StaticDataSource(pci.JHR));
        }
        if (pci.SCR != null && !pci.SCR.equals("")) {
            Element eSCR = report.getGroupByName("Group1").getFooter().getElement("scr");
            eSCR.setDataSource(new StaticDataSource(pci.SCR));
        }
		*/

        return report;

    }

    //计算字符串长度，对于0-255之间的字符按1计算，大于255的字符按2计算 *  
    public int getStringLength(String str) {
        int length = 0;
        for (char c : str.toCharArray()) {
            if (c > 255) {
                length += 2;
            } else {
                length++;
            }
        }
        return length;
    }

    //获取使字符串占用长度大于指定值的字符索引
    public int getStringPos(String str, int charOfLine) {
        int length = 0;
        int i = 0;
        for (; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c > 255) {
                length += 2;
            } else {
                length++;
            }
            if (length == charOfLine) {
                i++;
                break;
            } else if (length == charOfLine + 1) {
                break;
            }
        }
        return i;
    }
    private String id;
    private int cardKind;
    private String unit;
    private String time;
    private String no;
    private String task;
    private String remark;
    private String npr;
    private String xgsx;
    private PreviewDialog preview;
    
    private String CZPSJ="";
    private String NLSJ="";
    private String FLSJ="";
}
