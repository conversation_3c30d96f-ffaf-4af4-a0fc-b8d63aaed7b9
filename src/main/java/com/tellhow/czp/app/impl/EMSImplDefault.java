package com.tellhow.czp.app.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.BadSqlGrammarException;
import org.w3c.dom.Document;

import com.tellhow.czp.app.service.EMSService;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class EMSImplDefault extends EMSService {

	@Override
	public boolean showMeasAnalog(Document svgDoc) {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public Map getMeasPoint(String stationID) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getLineFlow(String stationID, String equipID) {
		// TODO Auto-generated method stub
		String flow = "-1";
	/*	PowerDevice station = CBSystemConstants.getPowerStation(stationID);
		PowerDevice line = CBSystemConstants.getPowerDevice(stationID, equipID);
		String sql = "select t.有功 from emsetl.t_s_ACLINESEGMENT t where replace(t.厂站名,'SS.','')='"+station.getCimID().replace("SS-", "")+"' and t.线路段名='"+line.getCimID()+"'";
		List list = new ArrayList();
		try{
			list = DBManager.queryForList(sql);
		}catch(BadSqlGrammarException e){
			e.printStackTrace();
		}
		if(list.size() > 0) {
			double yg = Double.valueOf(((Map)list.get(0)).get("有功").toString());
			if(yg == 0)
				flow = "0";
			else if(yg < 0)
				flow = "1";
			else if(yg > 0)
				flow = "2";
		}*/
		return flow;
	
	}

	@Override
	public Map getMeasAnalogData(String measID) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map getMeasPointData(String measID) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean getMeasPointYQ(String stationID) {
		// TODO Auto-generated method stub
		return false;
	}

}
