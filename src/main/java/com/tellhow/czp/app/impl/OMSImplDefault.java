package com.tellhow.czp.app.impl;

import java.util.List;

import com.tellhow.czp.app.service.OMSService;

public class OMSImplDefault extends OMSService {

	@Override
	public boolean importOMS(String czpID) {
		// TODO Auto-generated method stub
		return false;
	}
	@Override
	public boolean importOMS(String czpID,boolean iszsh) {
		// TODO Auto-generated method stub
		return false;
	}
	@Override
	public boolean importOMS(String czpID,boolean iszsh,boolean ishistory) {
		// TODO Auto-generated method stub
		return false;
	}
	@Override
	public String getJXPNO() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getCZPNO() {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public boolean deleteOMS(List<String> czpIDs) {
		// TODO Auto-generated method stub
		return false;
	}

}
