package com.tellhow.czp.app.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;
import org.w3c.dom.Document;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;

public class CZPImplDefault extends CZPService {

	@Override
	public void setArg(String str) {

	}

	@Override
	public String getDevNameInit(PowerDevice pd) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getDevNum(PowerDevice pd) {
		return getDevNum(pd.getPowerDeviceName());
	}

	@Override
	public String getDevNum(String word) {
		String powerDeviceName = word.trim();

		if(powerDeviceName.lastIndexOf(".") >= 0)
			powerDeviceName = powerDeviceName.substring(powerDeviceName.lastIndexOf(".")+1);

		String knifeName = "";
		String knifeCode = "";

		String equipTypeFlag = "";
		String equipTypeName = "";
		String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
				SystemConstants.SwitchFlowGroundLine,
				SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate,
				SystemConstants.SwitchSeparate, SystemConstants.Switch,
				SystemConstants.Switch, SystemConstants.VolsbTransformer,
				SystemConstants.VolsbTransformer,
				SystemConstants.VolsbTransformer, SystemConstants.MotherLine,
				SystemConstants.MotherLine, SystemConstants.InOutLine,
				SystemConstants.PowerTransformer, SystemConstants.ElecShock,
				SystemConstants.ElecCapacity, SystemConstants.PowerTransformer};
		String[] key = new String[] { "接地刀闸", "地刀", "隔离刀闸", "小车", "刀闸", "断路器",
				"开关", "PT", "pt", "电压互感器", "母线", "母", "线", "主变", "电抗器", "电容器",
				"#变"};
		for(int i = 0; i < key.length; i++) {
			if(word.lastIndexOf(key[i]) >= 0) {
				equipTypeFlag = type[i];
				equipTypeName = key[i];
				break;
			}
		}
		//罗马数字切换大小写
		powerDeviceName=powerDeviceName.replaceAll("IV", "Ⅳ");
		powerDeviceName=powerDeviceName.replaceAll("III", "Ⅲ");
		powerDeviceName=powerDeviceName.replaceAll("II", "Ⅱ");
		powerDeviceName=powerDeviceName.replaceAll("I", "Ⅰ");

		powerDeviceName=powerDeviceName.replaceAll("＃", "#");
		powerDeviceName=powerDeviceName.replaceAll("－", "-");

		if(equipTypeFlag.equals(SystemConstants.InOutLine)) {
			if(powerDeviceName.lastIndexOf("站") >= 0)
				powerDeviceName = powerDeviceName.substring(powerDeviceName.lastIndexOf("站")+1);
			if(powerDeviceName.toLowerCase().lastIndexOf("kv") >= 0)
				powerDeviceName = powerDeviceName.substring(powerDeviceName.toLowerCase().lastIndexOf("kv")+2);
			knifeCode = powerDeviceName.substring(0, powerDeviceName.indexOf("线")+1).replace("将", "").replace("退出", "").replace("启用", "").replace("投入", "").replace("检查","").replace("kV", "");
			knifeCode = knifeCode.replaceAll("^\\d{3,5}","").replaceAll("/","").replaceAll("\\d{3,5}","");
			return knifeCode;
		}

		powerDeviceName = StringUtils.killVoltInDevName(powerDeviceName);

		List<char[]> numList=new ArrayList<char[]>(); //一个开关名称中存在多个的返回最多的一个
		//带电压等级标签时去掉
		if (powerDeviceName.toUpperCase().lastIndexOf("KV") != -1) {
			knifeName = powerDeviceName.toUpperCase().substring(powerDeviceName.toUpperCase().lastIndexOf("KV")+2);
		} else {
			knifeName = powerDeviceName;
		}
		//将设备操作PT设备名称改变为数字
		if(equipTypeFlag.equals(SystemConstants.VolsbTransformer)){
			String[] num  = new String[]{"Ⅰ","Ⅱ","Ⅲ","Ⅳ"};
			for(int i = 0;i<num.length;i++){
				if(knifeName.indexOf(num[i])>-1){
					knifeName = knifeName.replace(num[i], String.valueOf(i+1));
					break;
				}
			}
		}
		char[] charArr = knifeName.toCharArray();

		char[] keyArr = null;

		if(StringUtils.compareStr(equipTypeFlag, SystemConstants.Switch) == 0) {
			keyArr = new char[]{'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F','G','H','L','X','Y','P','旁'};
		}else if(StringUtils.compareStr(equipTypeFlag, SystemConstants.SwitchSeparate) == 0) {
			keyArr = new char[]{'0','1','2','3','4','5','6','7','8','9','X','Y','旁'};
		}else if(StringUtils.compareStr(equipTypeFlag, SystemConstants.PowerTransformer) == 0) {
			keyArr = new char[]{'0','1','2','3','4','5','6','7','8','9'};
		}else if(StringUtils.compareStr(equipTypeFlag, SystemConstants.MotherLine) == 0) {
			keyArr = new char[]{'正','副','旁','Ⅰ','Ⅱ','Ⅲ','Ⅳ'};
		}else {
			keyArr = new char[]{'0','1','2','3','4','5','6','7','8','9','-','A','a','B','b','D','K','M','Y','正','副','#','I','旁','一','二','三','四','五','六'};
		}

		char[] numArr = new char[charArr.length];

		for (int i = charArr.length-1; i >= 0; i--) {
			char ch = charArr[i];

			boolean isChInKey = false;
			for (int j = keyArr.length-1; j >= 0; j--) {
				if(ch == keyArr[j]) {
					isChInKey = true;
					break;
				}
			}
			if(isChInKey)
				knifeCode = ch + knifeCode;
			if(!isChInKey && !knifeCode.equals(""))
				break;
		}

		if(equipTypeFlag.equals(SystemConstants.PowerTransformer)) {
			if(knifeCode.indexOf("#") == -1)
				knifeCode = "#" + knifeCode ;
		}

		return knifeCode;
	}

	@Override
	public List<RuleBaseMode> getRBMList(String word) {
		String station = "";

		for(Iterator it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();){
			PowerDevice st = (PowerDevice)it.next();
			String stName = CZPService.getService().getDevName(st);
			if(stName.length() <= 1)
				continue;
			if(word.contains(stName))
				station = stName;
		}
		if(station.equals("") && word.indexOf("站") >= 0)
			station = word.substring(0, word.indexOf("站")+1);
		return getRBMList(station, word);
	}

	@Override
	public List<RuleBaseMode> getRBMList(String station, String word) {

		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();
		RuleBaseMode rbmInfo = new RuleBaseMode();
		String stationID ="";


		if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
			CreatePowerStationToplogy.loadFacEquip(stationID);
		}
		HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationID);
		if(devMap==null){
			return rbmList;
		}
		String beginStatus = "";
		String endStatus = "";
		if(word.indexOf("转") >= 0) {
			String w1 = word.substring(0, word.indexOf("转"));
			String w2 = word.substring(word.indexOf("转")+1);
			if (w1.indexOf("运行") >= 0) {
				beginStatus = "0";
			}
			else if (w1.indexOf("热备用") >= 0) {
				beginStatus = "1";
			}
			else if (w1.indexOf("冷备用") >= 0) {
				beginStatus = "2";
			}
			else if (w1.indexOf("检修") >= 0) {
				beginStatus = "3";
			}
			if (w2.indexOf("运行") >= 0) {
				endStatus = "0";
			}
			else if (w2.indexOf("热备用") >= 0) {
				endStatus = "1";
			}
			else if (w2.indexOf("冷备用") >= 0) {
				endStatus = "2";
			}
			else if (w2.indexOf("检修") >= 0) {
				endStatus = "3";
			}
		}
		else if(word.indexOf("拉开") >= 0) {
			beginStatus = "0";
			endStatus = "1";
		}
		else if(word.indexOf("拉出") >= 0) {
			beginStatus = "0";
			endStatus = "1";
		}
		else if(word.indexOf("推上") >= 0) {
			beginStatus = "1";
			endStatus = "0";
		}
		else if(word.indexOf("推入") >= 0) {
			beginStatus = "1";
			endStatus = "0";
		}
		else if(word.indexOf("断开") >= 0) {
			beginStatus = "0";
			endStatus = "1";
		}
		else if(word.indexOf("合上") >= 0) {
			beginStatus = "1";
			endStatus = "0";
		}

		String equipTypeFlag = "";
		String equipTypeName = "";
		String[] type = new String[]{SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine, SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate, SystemConstants.Switch, SystemConstants.Switch,SystemConstants.MotherLine, SystemConstants.MotherLine, SystemConstants.InOutLine, SystemConstants.PowerTransformer, SystemConstants.ElecShock, SystemConstants.ElecCapacity};
		String[] key = new String[]{"接地刀闸","地刀","隔离刀闸","小车", "刀闸","断路器" ,"开关", "母线", "母", "线", "主变", "电抗器", "电容器"};
		for(int i = 0; i < key.length; i++) {
			if(word.lastIndexOf(key[i]) >= 0) {
				equipTypeFlag = type[i];
				equipTypeName = key[i];
				break;
			}
		}

		String volt = StringUtils.getVoltInDevName(word);



		String[] wordArr = null;
		if(word.indexOf("、") > 0) {
			wordArr = word.split("、");
			for(int i = 0; i < wordArr.length;i++) {
				wordArr[i] = wordArr[i] + equipTypeName;
			}
		}
		else {
			wordArr = new String[1];
			wordArr[0] = word;
		}
		for(int i = 0; i < wordArr.length; i++) {
			//devNumList.add(getDevNum(wordArr[i]));
			String devNum = getDevNum(wordArr[i]);
			PowerDevice pd = null;
			for(PowerDevice dev : devMap.values()) {
				if(!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
					continue;
				if(!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
					continue;
				if(getDevNum(dev.getPowerDeviceName()).equals(devNum)) {
					pd = dev;
					break;
				}
			}
			RuleBaseMode rbm = new RuleBaseMode();
			rbm.setPd(pd);
			rbm.setBeginStatus(beginStatus);
			rbm.setEndState(endStatus);
			if(pd == null && !beginStatus.equals("") && !endStatus.equals("")) {
				rbmInfo.getMessageList().add("找不到["+devNum+equipTypeName+"]，设备名称可能拼写错误!");
				rbmInfo.setCheckout(false);
				rbmList.clear();
				rbmList.add(rbmInfo);
				return rbmList;
			}
			rbmList.add(rbm);
		}
		return rbmList;
	}

	@Override
	public String getDevName(PowerDevice pd) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getDevName(List<PowerDevice> pdList) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void editMap(Document svgDoc, String fileName) {
		// TODO Auto-generated method stub

	}

	@Override
	public void editBackGround(SVGCanvasPanel panel) {
		// TODO Auto-generated method stub
	}

	@Override
	public void setMapSVGFileDefault() {
		// TODO Auto-generated method stub

	}
	/**
	 * 过滤图形
	 */
	@Override
	public void filterMap(List<SVGFile> svgFiles, String stationid){
		PowerDevice st = CBSystemConstants.getPowerStation(stationid);
		if(st != null && !st.getGraphFileName().equals("")) {
			for (Iterator it = svgFiles.iterator(); it.hasNext();) {
				SVGFile svgFile = (SVGFile)it.next();
				if(svgFile.getFileName().equals(st.getGraphFileName()) || svgFile.getFileName().equals(st.getGraphFileName().replace(".g", ".svg"))) {
					svgFiles.clear();
					svgFiles.add(svgFile);
					break;
				}
			}
		}

	}

	public List<RuleBaseMode> checkOperate(String sbName, String operation,String station) {
		return null;
		// TODO Auto-generated method stub
	}

	@Override
	public RuleBaseMode getCZSB(String czrw, String station) {
		// TODO Auto-generated method stub
		return null;
	}

	public String getKnifeNormalStatus(PowerDevice pd) {
		return "-1";
	}

	@Override
	public Object getResult(Object para) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setOldSVGName() {
		// TODO Auto-generated method stub

	}

	@Override
	public void initDeviceRunType(String stationID) {
		// TODO Auto-generated method stub
	}

	public void setDataBaseUser() {
		// TODO Auto-generated method stub

	}
	public void setOmsStationMapDefault() {
		// TODO Auto-generated method stub

	}

	@Override
	public String getJxdTicket(String arg) {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public String getZJCZTicket(String arg) {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public String getWTCZTicket(String arg) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<RuleBaseMode> getRBMList(String word, String opr,
										 String deviceid, String op) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getJXDDeviceInfo(String arg) {
		String result = "";

		/**
		 * 设置系统运行方式为智能开票类型。
		 * */
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.cardbuildtype = "0";
		CBSystemConstants.roleCode = "0";
		CBSystemConstants.opCode = "0";
		CBSystemConstants.opRuleCode = "0";
		CBSystemConstants.jh_tai = 0;

		System.out.println("输入参数：{"+arg+"}");

		String xmlCode = getXMLcode(arg);
		/**
		 * 构造返回的xml。
		 * */
		org.dom4j.Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding(xmlCode);
		Element datas=doc.addElement("Datas");

		InputStream is=null;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			org.dom4j.Document ret = domReader.read(document);
			Element root = ret.getRootElement();


		}catch(Exception e){
			e.printStackTrace();
		}

		return result;
	}

	public String getXMLcode(String str){
		if(str.toUpperCase().contains("UTF-8")){
			return "UTF-8";
		}else{
			return "GBK";
		}
	}

	@Override
	public String getRwTicket(String arg) {
		List<Map<String, String>> globalMainDevList = new ArrayList<Map<String, String>>();
		List<Map<String, String>> globalSlaveDevList = new ArrayList<Map<String, String>>();

		String result = "";

		/**
		 * 设置系统运行方式为智能开票类型。
		 * */
		CBSystemConstants.isCurrentSys=false;
		CBSystemConstants.cardbuildtype = "0";
		CBSystemConstants.roleCode = "0";
		CBSystemConstants.opCode = "0";
		CBSystemConstants.opRuleCode = "0";
		CBSystemConstants.jh_tai = 0;

		System.out.println("输入参数：{"+arg+"}");

		String xmlCode = getXMLcode(arg);
		/**
		 * 构造返回的xml。
		 * */
		org.dom4j.Document doc=DocumentHelper.createDocument();
		doc.setXMLEncoding(xmlCode);
		Element datas=doc.addElement("Datas");

		List<Map<String, String>> mainDevlist = new ArrayList<Map<String, String>>();
		List<Map<String, String>> slaveDevlist = new ArrayList<Map<String, String>>();

		InputStream is=null;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			org.dom4j.Document ret = domReader.read(document);
			Element root = ret.getRootElement();

			/*
			 * 清除全局变量
			 */
			globalMainDevList.clear();
			globalSlaveDevList.clear();

			List<Element> qymbLists =root.elements("areano");
			if(qymbLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				CBSystemConstants.qybm = qymbLists.get(0).getTextTrim();
				CBSystemConstants.unitCode = qymbLists.get(0).getTextTrim();
			}

			String czrwbak = "";

			List<Element> czrwLists =root.elements("czrw");
			if(czrwLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				czrwbak = czrwLists.get(0).getTextTrim();
			}


			List<Element> ticketkindLists =root.elements("ticketkind");

			List<Element> roomnameLists =root.elements("roomname");
			if(roomnameLists.size() > 0) {

			}

			StartupManager.startup();
//			CZPService.getService().setArg("");


			List<Element> roleLists =root.elements("role");
			if(roleLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				CBSystemConstants.roleCode = roleLists.get(0).getTextTrim();
			}


			if(qymbLists.size() > 0) {
				CBSystemConstants.opCode = QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode);
				if(CBSystemConstants.opCode.equals(""))
					CBSystemConstants.opCode = QueryDeviceDao.getOpcode("0", CBSystemConstants.roleCode);
				CBSystemConstants.opRuleCode = CBSystemConstants.opCode;
			}



			String operation = "";
//			String endstatus = "";
			List<Element> operationLists =root.elements("operation");
			if(operationLists.size() > 0) {
				//如果传了区域编码参数，给区域编码全局变量赋值
				operation = operationLists.get(0).getTextTrim();
			}

			List<Element> firstsstationLists = root.elements("firststation");
			if(firstsstationLists.size() > 0) {
				CBSystemConstants.oneClickString = firstsstationLists.get(0).getTextTrim();
			}

			//获取ITEM节点DOM
			List<Element> mainItemLists =root.elements("mainequip");
			//System.out.println(itemLists);
			for (int i = 0; i <mainItemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = mainItemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());
				}
				mainDevlist.add(mapInfo);
			}

			//获取ITEM节点DOM
			List<Element> slaveItemLists =root.elements("slaveequip");
			//System.out.println(itemLists);
			for (int i = 0; i <slaveItemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = slaveItemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);

					if(el.getName().equals("equipname")){
						if(!CBSystemConstants.oneClickString.equals("")&&!el.getTextTrim().equals("")){
							List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(CBSystemConstants.oneClickString,el.getTextTrim());

							if(rbmList.size()>0){
								if(rbmList.get(0).getPd()!=null){
									mapInfo.put("equipid", rbmList.get(0).getPd().getPowerDeviceID());
								}
							}
						}
					}

					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());

				}
				slaveDevlist.add(mapInfo);
			}

			globalSlaveDevList.addAll(slaveDevlist);

			PowerDevice pd = null;

			Map<String,String> czrwMap = new HashMap<String, String>();//主表内容
			List<Map<String,String>> czzlList = new ArrayList<Map<String,String>>();//从表内容
			List<RuleBaseMode> cardWordRbmList = new ArrayList<RuleBaseMode>();//术语List
			List<RuleBaseMode> tagDevRbmList = new ArrayList<RuleBaseMode>();

			for(Map<String, String> map : mainDevlist) {
				String equipID =  StringUtils.ObjToString(map.get("equipid"));
				String equipname = StringUtils.ObjToString(map.get("equipname"));
				String stationid = StringUtils.ObjToString(map.get("stationid"));
				String stationname = StringUtils.ObjToString(map.get("stationname"));
				String beginstatus =  StringUtils.ObjToString(map.get("beginstatus"));
				String endstatus =  StringUtils.ObjToString(map.get("endstatus"));

				if("".equals(endstatus)){
					if(operation.equals("运行")){
						endstatus = "0";
					}else if(operation.equals("热备用")){
						endstatus = "1";
					}else if(operation.equals("冷备用")){
						endstatus = "2";
					}else if(operation.equals("检修")){
						endstatus = "3";
					}else{
						endstatus = "-1";
					}
				}

				if(stationname.equals("") && equipID.equals("") && !equipname.equals("")) {
					List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(equipname);
					equipID = rbmList.get(0).getPd().getPowerDeviceID();
				}else if(equipID.equals("") && !equipname.equals("")) {
					List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(stationname,equipname);
					if(rbmList.size()>0){
						equipID = rbmList.get(0).getPd().getPowerDeviceID();
					}
				}

				PowerDevice eq = new PowerDevice();

				if(!equipID.equals("")){
					eq = CBSystemConstants.getPowerDevice(equipID);
				}

				if(eq != null){
					if(eq.getDeviceType().equals(SystemConstants.InOutLine)) {
						Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(eq);
						for(PowerDevice ln:stationlines.keySet()){
							eq=ln;
							break;
						}
					}

					CreatePowerStationToplogy.loadFacData(eq.getPowerStationID());

					if(!eq.getPowerStationID().equals("")){
						pd=CBSystemConstants.getStationPowerDevices(eq.getPowerStationID()).get(eq.getPowerDeviceID());

						if(pd == null){
							pd = eq;
						}

						InitDeviceStatus ie=new InitDeviceStatus();
						ie.initStatus_EMSToCache(pd.getPowerStationID());

						List<PowerDevice> otherXL = new ArrayList<PowerDevice>();
						if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
							otherXL =RuleExeUtil.getLineOtherSideList(pd);
						}

						for(PowerDevice xl:otherXL){
							ie.initStatus_EMSToCache(xl.getPowerStationID());
						}

						if(!pd.getDeviceStatus().equals(beginstatus)) {
							RuleExeUtil.deviceStatusReset(pd,pd.getDeviceStatus(),beginstatus);
							DeviceOperate.getAlltransDevMap().clear();
							CBSystemConstants.getDtdMap().clear();
							CBSystemConstants.LineTagStatus.clear();
						}

						RuleBaseMode rbm = new RuleBaseMode();
						rbm.setPd(pd);
						rbm.setBeginStatus(beginstatus);
						rbm.setEndState(endstatus);

						tagDevRbmList.add(rbm);
					}else{
						result = "";
					}
				}
			}

			for(RuleBaseMode rbm : tagDevRbmList){
				PowerDevice dev = rbm.getPd();
				String beginstatus = rbm.getBeginStatus();
				String endstatus = rbm.getEndState();

				rbm.setPd(dev);
				rbm.setBeginStatus(beginstatus);
				rbm.setEndState(endstatus);

				String statecode = "";
				String sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where cardbuildtype='0' and t.opcode='"+CBSystemConstants.opCode+"' and t.devicetypeid='"+dev.getDeviceType()+"' and t.statename = '"+operation+"' and isLock = '0'";
				List<Map<String, Object>> results = DBManager.queryForList(sql);
				Map<String, Object> temp;
				if(results.size()>0) {
					temp=(Map<String, Object>)results.get(0);
					statecode = StringUtils.ObjToString(temp.get("statecode"));
				}

				if(statecode.contains(",")){
					String[]  statecodeArr = statecode.split(",");

					for(String str : statecodeArr){
						rbm.setStateCode(str);
						cardWordRbmList.add(rbm);
						CardModel cm = new CardModel();
						cm.setCardItems(new ArrayList<CardItemModel>());

						CBSystemConstants.setCurRBM(rbm);
						RuleExecute ruleExc=new RuleExecute();
						ruleExc.execute(rbm);
						DeviceOperate doe = new DeviceOperate();
						doe.setTask(cm);

					}
				}else{
					rbm.setStateCode(statecode);
					cardWordRbmList.add(rbm);
					CardModel cm = new CardModel();
					cm.setCardItems(new ArrayList<CardItemModel>());

					CBSystemConstants.setCurRBM(rbm);
					RuleExecute ruleExc=new RuleExecute();
					ruleExc.execute(rbm);
					DeviceOperate doe = new DeviceOperate();
					doe.setTask(cm);
				}
			}

			boolean isdxcp = true;
			boolean isdzcp = false;

			if(ticketkindLists.size() > 0) {
				String kind = ticketkindLists.get(0).getTextTrim();

				if(kind.equals("multipleticket")){
					isdxcp = true;
				}
			}

			if(isdxcp){//是多选成票
				CardModel cardModel=WordExecute.getInstance().execute(cardWordRbmList);

				for(int i=0;i<cardModel.getCardItems().size();i++){//清除空指令
					if(cardModel.getCardItems().get(i).getCardDesc().equals("")){
						cardModel.getCardItems().remove(i);
						i--;
					}
				}

				if(!cardModel.getCzrw().equals("") || cardModel.getCardItems().size() > 0) {
					Element rw=datas.addElement("ITEM");

					if(!cardModel.getCzrw().equals("")){
						rw.addElement("czrw").setText(cardModel.getCzrw());
					}else{
						rw.addElement("czrw").setText(czrwbak);
					}
				}

				for(int i = 0; i < cardModel.getCardItems().size();i++) {
					Element item=datas.addElement("ITEM");
					item.addElement("carditem").setText(cardModel.getCardItems().get(i).getCardItem());
					item.addElement("cardorder").setText(String.valueOf(i+1));
					item.addElement("czdw").setText(cardModel.getCardItems().get(i).getStationName());
					String cardDesc=cardModel.getCardItems().get(i).getCardDesc();
					item.addElement("cznr").setText(cardDesc);
					item.addElement("czsn").setText(cardModel.getCardItems().get(i).getShowName());
					item.addElement("zlxh").setText(cardModel.getCardItems().get(i).getZlxh());

					String sql1 = "select ORGANID from "+CBSystemConstants.opcardUser+"TH_DC_DCMS_ORGAN "
							+ "where dwmc='"+ cardModel.getCardItems().get(i).getStationName()+"'";
					List<Map> list1 = DBManager.queryForList(sql1);
					item.addElement("czdwid").setText(list1.size()>0 && list1.get(0).get("ORGANID")!=null?list1.get(0).get("ORGANID").toString():"");
				}
				result = doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
			}else if(isdzcp){//是成多张操作票

			}else{//叠加类型的成票
				String lastcarditem = "";

				for(RuleBaseMode rbm : cardWordRbmList){
					CardModel cardModel=WordExecute.getInstance().execute(rbm);

					for(int i=0;i<cardModel.getCardItems().size();i++){//清除空指令
						if(cardModel.getCardItems().get(i).getCardDesc().equals("")){
							cardModel.getCardItems().remove(i);
							i--;
						}
					}

					if(!cardModel.getCzrw().equals("") || cardModel.getCardItems().size() > 0) {
						String beforeczrw  = StringUtils.ObjToString(czrwMap.get("czrw"));

						if(!beforeczrw.equals("")){
							czrwMap.put("czrw", beforeczrw+"；"+cardModel.getCzrw());
						}else{
							czrwMap.put("czrw", cardModel.getCzrw());
						}
					}

					int num = 0;

					for(int i = 0; i < cardModel.getCardItems().size();i++) {
						Map<String,String> map = new HashMap<String, String>();

						if(i == 0){
							if(!lastcarditem.equals("")){
								num = Integer.valueOf(lastcarditem);
							}
						}

						String carditem ="";

						if(cardModel.getCardItems().get(i).getCardItem().equals("")){
							carditem = "";
						}else{
							carditem = Integer.valueOf(cardModel.getCardItems().get(i).getCardItem())+num+"";
						}

						map.put("carditem", carditem+"");
						map.put("cardorder", String.valueOf(i+1));
						map.put("czdw", cardModel.getCardItems().get(i).getStationName());
						String cardDesc=cardModel.getCardItems().get(i).getCardDesc();
						map.put("cznr", cardDesc);
						map.put("czsn", cardModel.getCardItems().get(i).getShowName());
						map.put("zlxh", cardModel.getCardItems().get(i).getZlxh());


						String sql1 = "select ORGANID from "+CBSystemConstants.opcardUser+"TH_DC_DCMS_ORGAN "
								+ "where dwmc='"+ cardModel.getCardItems().get(i).getStationName()+"'";
						List<Map> list1 = DBManager.queryForList(sql1);
						map.put("czdwid", list1.size()>0 && list1.get(0).get("ORGANID")!=null?list1.get(0).get("ORGANID").toString():"");
						czzlList.add(map);

						if(i == cardModel.getCardItems().size()-1){
							lastcarditem = map.get("carditem");
						}
					}
				}

				Element rw=datas.addElement("ITEM");


				if(!StringUtils.ObjToString(czrwMap.get("czrw")).equals("")){
					rw.addElement("czrw").setText(StringUtils.ObjToString(czrwMap.get("czrw")));
				}else{
					rw.addElement("czrw").setText(czrwbak);
				}


				for(int i = 0; i < czzlList.size();i++) {
					Map<String,String> cm = czzlList.get(i);

					Element item=datas.addElement("ITEM");
					item.addElement("carditem").setText(StringUtils.ObjToString(cm.get("carditem")));
					item.addElement("cardorder").setText(String.valueOf(i+1));
					item.addElement("czdw").setText(StringUtils.ObjToString(cm.get("czdw")));
					item.addElement("cznr").setText(StringUtils.ObjToString(cm.get("cznr")));
					item.addElement("czsn").setText(StringUtils.ObjToString(cm.get("czsn")));
					item.addElement("zlxh").setText(StringUtils.ObjToString(cm.get("zlxh")));
					item.addElement("czdwid").setText(StringUtils.ObjToString(cm.get("czdwid")));
				}
				result = doc.asXML().replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>");
			}

			CBSystemConstants.getDtdMap().clear();

		} catch (Exception e) {
			e.printStackTrace();

		}
		finally{
			if(is!=null){
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}

		System.out.println(result);
		return result;
	}
}
