package com.tellhow.czp.app;

import java.awt.BorderLayout;
import java.awt.Container;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Frame;
import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFileChooser;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.border.Border;

import com.tellhow.common.spring.BeanFactory;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.resource.svg.convert.SVGDataExchange;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2010-6-21 上午09:22:25 
 */
public class ConvertSVGDialog extends JDialog {

	private static final long serialVersionUID = 1L;

	private JPanel jContentPane = null;

	private JPanel workPanel = null;

	private JButton chooseButton = null;

	private JTextField xmlTextField = null;

	private JButton importButton = null;
	
	private JButton  updateButton = null;
	
	private Container parent;
	

	/**
	 * @param owner
	 */
	public ConvertSVGDialog(Frame owner) {
		super(owner, true);
		initialize();
	}
	
	/**
	 * 选取并修改index path
	 * 
	 * 2008-8-11
	 */
	private void ChooseXML() {
		String svgPath = "";
		JFileChooser chooser = new JFileChooser("");
		chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
		chooser.setDialogTitle("选择要转换的SVG文件夹");
        int returnVal = chooser.showOpenDialog(parent);
        if (returnVal == JFileChooser.APPROVE_OPTION) {
        	svgPath = chooser.getSelectedFile().getAbsolutePath();
        		this.xmlTextField.setText(svgPath);
        }
	}	
	
	private void handleXML(String xmlPath) {
    	if(xmlPath.equals("")) {
        	JOptionPane.showMessageDialog(this, "请选择SVG文件夹！", "提示",JOptionPane.ERROR_MESSAGE);
        	return;
    	}
    	else {
    		Map map = new HashMap();
    		map.put("inputPath", xmlPath);
    		map.put("outputPath", SystemConstants.FILE_SVGMAP_PATH);
    		map.put("dataSource", (DataSource)BeanFactory.getBean("tbp.sys.DataSource1"));
    		map.put("projectID", "czp");
    		try {
    			String result = SVGDataExchange.execute(map);
    			if(!result.equals(""))
    				JOptionPane.showMessageDialog(this, result, "提示",JOptionPane.ERROR_MESSAGE);
    			else
    				JOptionPane.showMessageDialog(this, "转换完成！", "提示",JOptionPane.INFORMATION_MESSAGE);
    		} catch (Exception e) {
    			e.printStackTrace();
    			JOptionPane.showMessageDialog(this, e.getMessage(), "提示",JOptionPane.ERROR_MESSAGE);
    		}
    	}
	}

	/**
	 * This method initializes this
	 * 
	 * @return void
	 */
	private void initialize() {
		this.setTitle("SVG更新");
		this.setSize(480, 200);
		this.add(getJContentPane());
	}

	/**
	 * This method initializes jContentPane
	 * 
	 * @return javax.swing.JPanel
	 */
	private JPanel getJContentPane() {
		if (jContentPane == null) {
			jContentPane = new JPanel();
			
			Border titleBorder1 = BorderFactory.createTitledBorder("转换SVG图形");
			jContentPane.setBorder(titleBorder1);
			
			jContentPane.setLayout(new BorderLayout());
			jContentPane.add(getWorkPanel(), BorderLayout.CENTER);
		}
		return jContentPane;
	}

	/**
	 * This method initializes workPanel	
	 * 	
	 * @return javax.swing.JPanel	
	 */
	private JPanel getWorkPanel() {
		if (workPanel == null) {
			workPanel = new JPanel();
			workPanel.setLayout(new FlowLayout());
			workPanel.add(getXmlTextField(), null);
			workPanel.add(getChooseButton(), null);
			workPanel.add(getImportButton(), null);
		}
		return workPanel;
	}

	/**
	 * This method initializes chooseButton	
	 * 	
	 * @return javax.swing.JButton	
	 */
	private JButton getChooseButton() {
		if (chooseButton == null) {
			chooseButton = new JButton();
			chooseButton.setText("选择目录");
			chooseButton.setName("chooseButton");
			chooseButton.setPreferredSize(new Dimension(90, 30)); 
			chooseButton.addActionListener(new java.awt.event.ActionListener() {
				public void actionPerformed(java.awt.event.ActionEvent e) {
					ChooseXML();
				}
			});
		}
		return chooseButton;
	}

	/**
	 * This method initializes xmlTextField	
	 * 	
	 * @return javax.swing.JTextField	
	 */
	private JTextField getXmlTextField() {
		if (xmlTextField == null) {
			xmlTextField = new JTextField();
			xmlTextField.setPreferredSize(new Dimension(380, 30));
			xmlTextField.setName("xmlTextField");
		}
		return xmlTextField;
	}

	/**
	 * This method initializes importButton	
	 * 	
	 * @return javax.swing.JButton	
	 */
	private JButton getImportButton() {
		if (importButton == null) {
			importButton = new JButton();
			importButton.setText("转换图形");
			importButton.setName("importButton");
			importButton.setPreferredSize(new Dimension(90, 30));
			importButton.addActionListener(new java.awt.event.ActionListener() {
				public void actionPerformed(java.awt.event.ActionEvent e) {
					handleXML(ConvertSVGDialog.this.xmlTextField.getText());
				}
			});
		}
		return importButton;
	}
	
	public static void main(String[] args)
	{
		new ConvertSVGDialog(null).setVisible(true);
	}

}
