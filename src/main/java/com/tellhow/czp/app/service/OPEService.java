package com.tellhow.czp.app.service;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.staticsql.OpeInfo;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;

public abstract class OPEService {
	private static OPEService service=null;
	protected static String name = CBSystemConstants.equipUser;
	public static OPEService getService(){
		if(service==null){
			Object impl=CZPImpl.getInstance("ALLSQL");
			if(impl!=null){
				service=(OPEService)impl;
			}else{
				service=new OpeInfo();
			}
		}
		return service;
	}
	public abstract String getExeEqu(String equip, String starOne,String endTwo);
	public abstract String getPreEqu(String pid, String srcsta, String status);
	public abstract String getLs();
	public abstract String getQueCompement(String code);
	public abstract String getBasParam(String code);
	public abstract String getCs();
	public abstract String getTriParam(String code) ;
	public abstract String getEOCCParam(String organid,PowerDevice pd);
	public abstract String getEPCCParam(String organid,PowerDevice pd);
	public abstract String getDsd();
	public abstract String getdelDevicedql();
	public abstract String getDsd2();
	public abstract String getIdrt(String stationID);
	public abstract String getIdrs(String stationID) ;
	public abstract String getIdrs0(String stationID);
	public abstract String getIdrs1(String stationID);
	public abstract String getIdrs2(String stationID);
	public abstract String getPowDevice(String powerEquipID);
	public abstract String getOrga(String powerDeviceID) ;
	public abstract String getStationDeviceList(String powerStationID);
	public abstract String getEquipType();
	public abstract String getStationProtectList(String powerStationID);
	public abstract String getStationToplogy(String powerStationID);
	public abstract String getStationToplogyLine(String powerStationID);
	public abstract String getSysTelemetering();
	public abstract String getFacTelemetering();
	public abstract String getDeviceState() ;
	public abstract String getActionNote();
	public abstract String getActionGroundLine();
	public abstract String getActionCard();
	public abstract String getStationRMDevice();
	public abstract String getStationBZTDevice(String stationID);
	public abstract String getStationRelateDevice();
	public abstract String getYXFromDB(String stationID);
	public abstract String getYCFromDB(String stationID);
	public abstract String getXLYGFromDB(String equipID);
	public abstract String getPowersLineByLine(PowerDevice line);
	public abstract String getLineTidy(String line_CIM_ID);
	public abstract String getStationID_(String EquipID);
	public abstract String getEquipName(String EquipID);
	public abstract String getEquipID(String StationID,String EquipName);
	public abstract String getrevdiv(String StationID,String EquipName);
	public abstract String getGKdiv(String StationID,String EquipName);
	public abstract String getdiv(String StationID,String EquipName);
	public abstract String getswitchdiv(String StationID, String EquipName);
	public abstract String getEquipID(String stationID, String equipNum, String equipType);
	public abstract String getEquipIDByNameType(String stationID, String equipNum, String equipType);
	public abstract String getDevice(String devCode, String StationName,String devType);
	public abstract String updateDevVoltage(PowerDevice pd, CodeNameModel codeNameModel);
	public abstract String updateDevtypemethods(PowerDevice pd,CodeNameModel codeNameModel);
	public abstract String updateAdjustablePipeOrgan(PowerDevice pd,CodeNameModel codeNameModel);
	public abstract String updatePermitOrgan(PowerDevice pd, CodeNameModel codeNameModel);
	public abstract String updateDevice1(String powerStationID);
	public abstract String updateDevice2(String powerStationID);
	public abstract String checkValue();
	public abstract String DriverProperty2(PowerDevice pd);
	public abstract String DriverProperty3(PowerDevice pd);
	public abstract String PermitOrgan2();
	public abstract String TwodeviceDialog1();
	public abstract String TwodeviceDialog2();
	public abstract String TwodeviceDialog3();
	public abstract String TwodeviceDialog4();
	public abstract String DeviceStatusManager(String zbid);
	public abstract String TicketDBManager1();
	public abstract String TicketDBManager2();
	public abstract String TicketDBManagerjk();
	public abstract String MonitorTicketTypePanel(String zbid);
	public abstract String OperateTicketConvertPanel(String zbid);
	public abstract String TermInversion();
	public abstract String RuleCustomDialog();
	public abstract String DictionarysModelInit();
	public abstract String getDeviceTypeSql();
	public abstract String getDeviceStatusSql(String deviceType);
	public abstract String getcombobox1Sql();
	public abstract String importOMSSql(String zbid);
	public abstract String importOMSSql1(String zbid);
	public abstract String getPowersLineBySysLineSql(PowerDevice line);
	public abstract String getProtectByTypeSql(PowerDevice pd, String protectType);
	public abstract String updateDevtypemethods(CodeNameModel codeNameModel);
	public abstract String executeSql();
	public abstract String DevicetypemethodsSql();
	public abstract String DriverPorpetySql();
	public abstract String DriverPorpetySql1();
	public abstract String DriverPorpetySql2();
	public abstract String DriverPorpetySql3();
	public abstract String DriverPorpetySql4();
	public abstract String EquipManagerDialogSql();
	public abstract String EquipManagerDialogSql1();
	public abstract String EquipManagerDialogSql2();
	public abstract String EquipManagerDialogSql3();
	public abstract String EquipManagerDialogSql4();
	public abstract String EquipManagerDialogSql5();
	public abstract String EquipManagerDialogSql6();
	public abstract String EquipManagerDialogSql7();
	public abstract String EquipManagerDialogSql8();
	public abstract String EquipManagerDialogSql9();
	public abstract String EquipManagerDialogSql10();
	public abstract String EquipOperationManagerDialogSql();
	public abstract String EquipOperationManagerDialogSqltype1();
	public abstract String EquipOperationManagerDialogSqltype2();
	public abstract String EquipStateDialogSql();
	public abstract String EquipStateDialogSql1();
	public abstract String EquipTypeDialogSql();
	public abstract String SystableDialogSql();
	public abstract String SystableDialogSql1();
	public abstract String SystableDialogSql2();
	public abstract String SystableDialogSql3();
	public abstract String SystableDialogSql4();
	public abstract String SystableDialogSql5();
	public abstract String PowerSystemDBOperatorSql();
	public abstract String PowerFeederOperatorSql();
	public abstract String QueryDeviceDaoSql();
	public abstract String QueryDeviceDaoSql1();
	public abstract String InOutLineTreeWidgetSql();
	public abstract String InOutLineTreeWidgetSql1();
	public abstract String ElecIslandAlgorithmSql();
	public abstract String SautodeviceDialogSql();
	public abstract String TriprelateDialogSql();
	public abstract String GetDifferStatusDevicesSql();
	public abstract String StationDeviceToplogySql();
	public abstract String OrganListSql();
	public abstract String PowerSystemDBOperatorSql1();
	public abstract String PowerSystemDBOperatorSql2();
	public abstract String PowerSystemDBOperatorSql3();
	public abstract String DataToDemoSql();
	public abstract String CZPOperatorSql(String cardid);
	public abstract String CZPOperatorsdSql(String cardid);
	public abstract String CZPOperatorsdSql1();
	public abstract String CZPOperatorsdSql2();
	public abstract String QueryDeviceDaoSql2(String facLineID);
	public abstract String QueryDeviceDaoSql3(String StationName);
	public abstract String QueryDeviceDaoSql4(String StationName);
	public abstract String QueryDeviceDaoSql5(String StationID);
	public abstract String QueryDeviceDaoSql6(String StationName);
	public abstract String QueryDeviceDaoSql7(String StationName);
	public abstract String QueryDeviceDaoSql8();
	public abstract String QueryDeviceDaoSql9(String StationName);
	public abstract String DriverPorperySql();
	public abstract String DriverPorperySql1();
	public abstract String DriverPorperySql2();
	public abstract String InOutLineTreeWidgetSql2();
	public abstract String DictionaryModelInitSql();
	public abstract String DeviceStateMentManagerSql();
	public abstract String QueryDeviceDaoSql2();
	public abstract String TransTreeWidgetSql();
	public abstract String TransTreeWidgetSql1();
	public abstract String GetDIfferStatusDevicesSql1();
	public abstract String CZPOperatorJCSql();
	public abstract String CZPOperatorJCSql1();
	public abstract String CZPOperatorJCSql2();
	public abstract String CZPOperatorJCSql3();
	public abstract String CZPOperatorXBSql();
	public abstract String CZPOperatorXBSql1();
	public abstract String CZPOperatorXBSql2();
	public abstract String CZPOperatorXBSql3();
	public abstract String CZPOperatorXBD5000Sql();
	public abstract String getEquipCheck(String EquipName);
	
}
