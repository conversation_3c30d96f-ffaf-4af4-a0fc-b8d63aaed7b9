package com.tellhow.czp.app.service;

import java.util.Map;

import org.w3c.dom.Document;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.impl.EMSImplDefault;

public abstract class EMSService {

	private static EMSService service = null;
	
	public static EMSService getService() {
		if(service == null) {
			Object impl = CZPImpl.getInstance("EMSImpl");
			if(impl != null)
				service = (EMSService)impl;
			else
				service = new EMSImplDefault();
		}
		return service;
	}
	
	/**
	 * 在图形上展示遥测数据
	 * @param svgDoc
	 * @param stationID
	 * @param layerID
	 */
	public abstract boolean showMeasAnalog(Document svgDoc);
	
	/**
	 * 获取遥信数据
	 * @param stationID
	 * @return
	 */
	public abstract Map getMeasPoint(String stationID);
	
	public abstract boolean getMeasPointYQ(String stationID);
	
	/**
	 * 获取线路潮流 -1无数据 0有功为零 1进线 2出线
	 * @param equipID
	 * @return
	 */
	public abstract String getLineFlow(String stationID, String equipID);
	
	/**
	 * 获取遥测
	 * @param equipID
	 * @return
	 */
	public abstract Map getMeasAnalogData(String measID);
	
	/**
	 * 获取遥测
	 * @param equipID
	 * @return
	 */
	public abstract Map getMeasPointData(String measID);
}
