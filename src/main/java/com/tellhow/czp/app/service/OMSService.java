package com.tellhow.czp.app.service;

import java.util.List;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.impl.OMSImplDefault;

public abstract class OMSService {
	
	private static OMSService service = null;
	
	public static OMSService getService() {
		if(service == null) {
			Object impl = CZPImpl.getInstance("OMSImpl");
			if(impl != null)
				service = (OMSService)impl;
			else
				service = new OMSImplDefault();
		}
		return service;
	}
	public abstract boolean importOMS(String czpID);
	public abstract boolean importOMS(String czpID,boolean iszsh);
	public abstract boolean importOMS(String czpID,boolean iszsh,boolean ishistory);
	public abstract String getJXPNO();
	
	public abstract String getCZPNO();
	public abstract boolean deleteOMS(List<String> czpIDs);
	
}
