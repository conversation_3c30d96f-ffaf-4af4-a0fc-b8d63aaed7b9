package com.tellhow.czp.app.service;

import java.util.List;

import org.w3c.dom.Document;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.impl.CZPImplDefault;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;

public abstract class CZPService {

	private static CZPService service = null;
	
	public static CZPService getService() {
		if(service == null) {
			Object impl = CZPImpl.getInstance("CZPImpl");
			if(impl != null)
				service = (CZPService)impl;
			else
				service = new CZPImplDefault();
		}
		return service;
	}
	
	public static void setService(CZPService ser) {
		service = ser;
	}
	
	/**
	 * 鍦ㄧ郴缁熷惎鍔ㄦ椂锛岃缃郴缁熷惎鍔ㄥ弬鏁帮紝姣斿鐢ㄦ埛ID銆佹搷浣滅エ绫诲瀷
	 * @param str
	 */
	public abstract void setArg(String str);
	
	
	/**
	 * 鑾峰彇璁惧鍒濆鍖栫殑鍚嶇О
	 * @param pd
	 * @return
	 */
	public abstract String getDevNameInit(PowerDevice pd);
	/**
	 * 鑾峰彇鏁版嵁搴撳瓨鍌ㄧ殑鏃VG鍥惧悕绉�	 * 
	 */
	public abstract void setOldSVGName();
	/**
	 * 鑾峰彇璁惧缂栧彿
	 * @param pd
	 * @return
	 */
	public abstract String getDevNum(String word);
	/**
	 * 鑾峰彇璁惧缂栧彿
	 * @param pd
	 * @return
	 */
	public abstract String getDevNum(PowerDevice word);
	
	/**
	 * 鑾峰彇鎿嶄綔瀵硅薄鍙婂姩浣�	 * @param pd
	 * @return
	 */
	public abstract List<RuleBaseMode> getRBMList(String word);
	
	/**
	 * 鑾峰彇鎿嶄綔瀵硅薄鍙婂姩浣�	 * @param pd
	 * @return
	 */
	public abstract List<RuleBaseMode> getRBMList(String station, String word);

	/**
	 * 鑾峰彇鎿嶄綔绁ㄤ腑璁惧鐨勫悕绉�	 * @param pd
	 * @return
	 */
	public abstract String getDevName(PowerDevice pd);
	/**
	 * 鑾峰彇鎿嶄綔绁ㄤ腑璁惧鐨勫悕绉�澶氫釜璁惧)
	 * @param pdList
	 * @return
	 */
	public abstract String getDevName(List<PowerDevice> pdList);
	
	
	/**
	 * 澶勭悊鍥惧舰
	 * @param svgDoc
	 * @param stationID
	 * @param layerID
	 */
	public abstract void editMap(Document svgDoc, String fileName);
	/**
	 * 杩囨护鍥惧舰
	 */
	public  abstract void filterMap(List<SVGFile> svgFiles, String stationid);
	/**
	 * 鑾峰彇鏁版嵁搴撶敤鎴�	 */
	public  abstract void setDataBaseUser();
	/**
	 * 澶勭悊鍥惧舰
	 * @param svgDoc
	 * @param stationID
	 * @param layerID
	 */
	public abstract void editBackGround(SVGCanvasPanel panel);
	/**
	 * 璁剧疆榛樿鍥惧舰
	 */
	public abstract void setMapSVGFileDefault();
	/**
	 * 鍔犺浇OMS鍘傜珯鍚嶇О缂撳瓨
	 */
	public abstract void setOmsStationMapDefault();
	/**
	 * 妫�慨鍗曟垚绁�	 */
	public abstract String getJxdTicket(String arg);
	public abstract RuleBaseMode getCZSB(String czrw,String station) ;

	public abstract List<RuleBaseMode> checkOperate(String sbName, String operation, String station);
	
	/**
	 * 鑾峰緱鍙屾瘝鎺ョ嚎姣嶇嚎鍒�椄鐨勬甯歌繍琛屾柟寮忎笅鐘舵�锛堝悇鍦拌鍒欎笉鍚岋級
	 * @param pd
	 * @return
	 */
	public abstract String getKnifeNormalStatus(PowerDevice pd);
	
	public abstract Object getResult(Object para);
	
	/**
	 * 鍒濆鍖栬澶囧畨瑁呯被鍨嬶紙鐗规畩瑙勫垯锛�	 * @param pd
	 * @return
	 */
	public abstract void initDeviceRunType(String stationID);
	/**
	 * 涓皟鍏宠仈鎴愮エ--鐩存帴鎿嶄綔
	 */
	public abstract String getZJCZTicket(String arg);
	/**
	 * 涓皟鍏宠仈鎴愮エ--濮旀墭鎿嶄綔
	 */
	public abstract String getWTCZTicket(String arg);

	public List<RuleBaseMode> getRBMList(String word, String opr,
			String deviceid, String op) {
		return null;
	}

	public abstract String getJXDDeviceInfo(String word) ;

	public abstract String getRwTicket(String word) ;
}
