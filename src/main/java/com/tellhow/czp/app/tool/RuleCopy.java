package com.tellhow.czp.app.tool;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class RuleCopy {

	public static void main(String[] args) {
		//copyRuleByCardType("Compensator","2","1");
		//copyRuleByEquipType("Compensator","Reactor");
		//copyWordByEquipType("Compensator","Reactor");
		copyAllByOpcode("0", "2");
	}
	
	/**
	 * 按opcode拷贝菜单、规则、术语
	 * @param srcOpcode
	 * @param tagOpcode
	 */
	public static void copyAllByOpcode(String srcOpcode, String tagOpcode) {
		//DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_RULECB a where a.f_zbid in (select zbid from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.endstate in (select statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO where opcode='"+tagOpcode+"'))");
		//DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.endstate in (select statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO where opcode='"+tagOpcode+"')");
		//DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB a where a.f_zbid in (select zbid from "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.endstate in (select statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO where opcode='"+tagOpcode+"'))");
		//DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.endstate in (select statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO where opcode='"+tagOpcode+"')");
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t where opcode='"+tagOpcode+"'");
		DBManager.update("insert into "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO SELECT guid,STATENAME,STATEVALUE,'"+tagOpcode+"',DEVICETYPEID,PARENTCODE,STATETYPE,RUNMODEL,HASSIDE,RUNTYPE,STATEORDER,SECONDTYPEID,SECONDSTATE,OPERATECODE,CARDBUILDTYPE,STATEKIND,ISLOCK FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO where opcode='"+srcOpcode+"'");
		copyDeviceParentCode(srcOpcode,tagOpcode);
		copyRuleByStateCode(srcOpcode,tagOpcode);
		copyWordByStateCode(srcOpcode,tagOpcode);
	}
	
	public static void copyRuleByStateCode(String srcOpcode, String tagOpcode) {
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_RULECB a where a.f_zbid in (select zbid from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.OPCODE='"+tagOpcode+"')");
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.OPCODE='"+tagOpcode+"'");
		
		String zbSql = "insert into "+CBSystemConstants.opcardUser+"T_A_RULEZB (ZBID,DEVICETYPEID,DEVICERUNMODEL,BEGINSTATUS,ENDSTATE,VOLT,OPCODE,CARDBUILDTYPE) values(?,?,?,?,?,?,?,?)";
		List zbParaList = new ArrayList(); 
		List<String> cbSqlList = new ArrayList();
		List<Map> listMap = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.OPCODE='"+srcOpcode+"' and endstate not in (select statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO where islock = '1')");
		for(Map map : listMap) {
			if(map.get("ENDSTATE") == null)
				continue;
			String oldZbid = map.get("ZBID").toString();
			String newZbid = java.util.UUID.randomUUID().toString();
			String oldStateCode = map.get("ENDSTATE").toString();
			if(oldStateCode.equals("e4dafa30-a7fc-4578-a4bf-b7e11bf12c37")){
				int asd =1;
			}
			List<Map> list = DBManager.queryForList("SELECT a.statecode FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a,"+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO b where a.statename=b.statename and a.cardbuildtype=b.cardbuildtype and a.devicetypeid=b.devicetypeid and a.opcode='"+tagOpcode+"' and b.opcode='"+srcOpcode+"' and b.statecode='"+oldStateCode+"' and a.islock = '0'");
			if(list.size() == 0)
				continue;
			if(list.size()>1){
				int asd =1;
			}
			String newStateCode = list.get(0).get("statecode").toString();
			if(newStateCode.equals("E3B67E06BD2B06DBA8A89CB758383559")){
				int asd =1;
			}
			Object[] zbPara = new Object[]{newZbid,StringUtils.ObjToString(map.get("DEVICETYPEID")),StringUtils.ObjToString(map.get("DEVICERUNMODEL")),StringUtils.ObjToString(map.get("BEGINSTATUS")),newStateCode,StringUtils.ObjToString(map.get("VOLT")),tagOpcode,map.get("CARDBUILDTYPE")};
			zbParaList.add(zbPara);
			cbSqlList.add("insert into "+CBSystemConstants.opcardUser+"T_A_RULECB(f_zbid,RULEID,BEGINSTATUS,ENDSTATE,ORDERID,TRANTYPE,DEVICERUNTYPE) select '"+newZbid+"',RULEID,BEGINSTATUS,ENDSTATE,ORDERID,TRANTYPE,DEVICERUNTYPE from "+CBSystemConstants.opcardUser+"T_A_RULECB a where a.f_zbid='"+oldZbid+"'");
		}
		
		DBManager.update(zbSql, zbParaList);
		DBManager.batchUpdate(cbSqlList.toArray(new String[]{}));
		System.out.println("转换完成");
	}
	
	public static void copyWordByStateCode(String srcOpcode, String tagOpcode) {
		
		
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB a where a.f_zbid in (select zbid from "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.OPCODE='"+tagOpcode+"')");
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.OPCODE='"+tagOpcode+"'");
		
		String zbSql = "insert into "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB (ZBID,DEVICETYPEID,BEGINSTATUS,ENDSTATE,STATETYPE,OPCODE,CZRW,VOLT,CARDTYPE,EQUIPID,BZSX) values(?,?,?,?,?,?,?,?,?,?,?)";
		List zbParaList = new ArrayList(); 
		List<String> cbSqlList = new ArrayList();
		List<Map> listMap = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.OPCODE='"+srcOpcode+"'");
		for(Map map : listMap) {
			String oldZbid = map.get("ZBID").toString();
			String newZbid = java.util.UUID.randomUUID().toString();
			String oldStateCode = map.get("ENDSTATE").toString();
			List<Map> list = DBManager.queryForList("SELECT a.statecode FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a,"+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO b where a.statename=b.statename and a.cardbuildtype=b.cardbuildtype and a.devicetypeid=b.devicetypeid and a.opcode='"+tagOpcode+"' and b.opcode='"+srcOpcode+"' and b.statecode='"+oldStateCode+"'");
			if(list.size() == 0)
				continue;
			String newStateCode = list.get(0).get("statecode").toString();
			Object[] zbPara = new Object[]{newZbid,StringUtils.ObjToString(map.get("DEVICETYPEID")),StringUtils.ObjToString(map.get("BEGINSTATUS")),newStateCode,StringUtils.ObjToString(map.get("STATETYPE")),tagOpcode,StringUtils.ObjToString(map.get("CZRW")),StringUtils.ObjToString(map.get("VOLT")),map.get("CARDTYPE"),StringUtils.ObjToString(map.get("EQUIPID")),StringUtils.ObjToString(map.get("BZSX"))};
			zbParaList.add(zbPara);
			cbSqlList.add("insert into "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB(F_ZBID,DEVICESTATEMENTCB,ORDERID) select '"+newZbid+"',DEVICESTATEMENTCB,ORDERID from "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB a where a.f_zbid='"+oldZbid+"'");
		}
		
		DBManager.update(zbSql, zbParaList);
		DBManager.batchUpdate(cbSqlList.toArray(new String[]{}));
		System.out.println("转换完成");
	}

	/**
	 * 复制T_A_DEVICESTATEINFO表数据后 新复制的数据parentCode与stateCode关联上
	 * 更新复制的parentCode不为0的数据为新的stateCode字段数据
	 * 
	 */
	public static void copyDeviceParentCode(String sourceCode,String tagertCode){
		//DM
//		String sql = " update "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t set t.parentcode=a2.statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t,"+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a1,"+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a2 where a1.statename=a2.statename and a1.devicetypeid   =a2.devicetypeid and a1.statetype   =a2.statetype and a1.cardbuildtype   =a2.cardbuildtype and a1.statecode    =t.parentcode and a1.opcode ='"+sourceCode+"' and a2.opcode ='"+tagertCode+"' and t.opcode='"+tagertCode+"' and t.parentcode!='"+sourceCode+"'";
		
		//oracle
		String sql = " update "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t set t.parentcode =  (select a2.statecode from  "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t, "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a1 , "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a2 where a1.statename = a2.statename and a1.devicetypeid = a2.devicetypeid and a1.statetype = a2.statetype and a1.cardbuildtype = a2.cardbuildtype  and a1.statecode = t.parentcode  and a1.opcode = '"+sourceCode+"'  and a2.opcode = '"+tagertCode+"') where   t.opcode = '"+tagertCode+"' and t.parentcode != '"+sourceCode+"' ";
		   
		DBManager.execute(sql);
		
		sql = " update "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t  set t.parentcode = '0' where t.parentcode is null and opcode = '"+tagertCode+"'";
		   
		DBManager.execute(sql);
	}
	
	public static void copyRuleByCardType(String equipType, String srcBuildType, String tagBuildType) {
		
		
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_RULECB a where a.f_zbid in (select zbid from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.devicetypeid='"+equipType+"' and cardbuildtype='"+tagBuildType+"')");
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.devicetypeid='"+equipType+"' and cardbuildtype='"+tagBuildType+"'");
		
		String zbSql = "insert into "+CBSystemConstants.opcardUser+"T_A_RULEZB (ZBID,DEVICETYPEID,DEVICERUNMODEL,BEGINSTATUS,ENDSTATE,VOLT,OPCODE,CARDBUILDTYPE) values(?,?,?,?,?,?,?,?)";
		List zbParaList = new ArrayList(); 
		List<String> cbSqlList = new ArrayList();
		List<Map> listMap = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.devicetypeid='"+equipType+"' and cardbuildtype='"+srcBuildType+"'");
		for(Map map : listMap) {
			String oldZbid = map.get("ZBID").toString();
			String newZbid = java.util.UUID.randomUUID().toString();
			String oldStateCode = map.get("ENDSTATE").toString();
			List<Map> list = DBManager.queryForList("SELECT a.statecode FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a,"+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO b where a.islock = '0' and  a.statename=b.statename and a.opcode=b.opcode and a.devicetypeid=b.devicetypeid and a.cardbuildtype='"+tagBuildType+"' and b.statecode='"+oldStateCode+"'");
			if(list.size() == 0)
				continue;
			String newStateCode = list.get(0).get("statecode").toString();
			Object[] zbPara = new Object[]{newZbid,equipType,StringUtils.ObjToString(map.get("DEVICERUNMODEL")),StringUtils.ObjToString(map.get("BEGINSTATUS")),newStateCode,StringUtils.ObjToString(map.get("VOLT")),map.get("OPCODE"),tagBuildType};
			zbParaList.add(zbPara);
			cbSqlList.add("insert into "+CBSystemConstants.opcardUser+"T_A_RULECB(f_zbid,RULEID,BEGINSTATUS,ENDSTATE,ORDERID,TRANTYPE,DEVICERUNTYPE) select '"+newZbid+"',RULEID,BEGINSTATUS,ENDSTATE,ORDERID,TRANTYPE,DEVICERUNTYPE from "+CBSystemConstants.opcardUser+"T_A_RULECB a where a.f_zbid='"+oldZbid+"'");
		}
		
		DBManager.update(zbSql, zbParaList);
		DBManager.batchUpdate(cbSqlList.toArray(new String[]{}));
		System.out.println("转换完成");
	}
	
	public static void copyRuleByEquipType(String srcEquipType, String tagEquipType) {
		
		
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_RULECB a where a.f_zbid in (select zbid from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.devicetypeid='"+tagEquipType+"')");
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.devicetypeid='"+tagEquipType+"'");
		
		String zbSql = "insert into "+CBSystemConstants.opcardUser+"T_A_RULEZB (ZBID,DEVICETYPEID,DEVICERUNMODEL,BEGINSTATUS,ENDSTATE,VOLT,OPCODE,CARDBUILDTYPE) values(?,?,?,?,?,?,?,?)";
		List zbParaList = new ArrayList(); 
		List<String> cbSqlList = new ArrayList();
		List<Map> listMap = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_RULEZB t where t.devicetypeid='"+srcEquipType+"'");
		for(Map map : listMap) {
			String oldZbid = map.get("ZBID").toString();
			String newZbid = java.util.UUID.randomUUID().toString();
			String oldStateCode = map.get("ENDSTATE").toString();
			List<Map> list = DBManager.queryForList("SELECT a.statecode FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a,"+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO b where a.islock = '0' and  a.statename=b.statename and a.opcode=b.opcode and a.cardbuildtype=b.cardbuildtype and a.devicetypeid='"+tagEquipType+"' and b.statecode='"+oldStateCode+"'");
			if(list.size() == 0)
				continue;
			String newStateCode = list.get(0).get("statecode").toString();
			Object[] zbPara = new Object[]{newZbid,tagEquipType,StringUtils.ObjToString(map.get("DEVICERUNMODEL")),StringUtils.ObjToString(map.get("BEGINSTATUS")),newStateCode,StringUtils.ObjToString(map.get("VOLT")),map.get("OPCODE"),map.get("CARDBUILDTYPE")};
			zbParaList.add(zbPara);
			cbSqlList.add("insert into "+CBSystemConstants.opcardUser+"T_A_RULECB(f_zbid,RULEID,BEGINSTATUS,ENDSTATE,ORDERID,TRANTYPE,DEVICERUNTYPE) select '"+newZbid+"',RULEID,BEGINSTATUS,ENDSTATE,ORDERID,TRANTYPE,DEVICERUNTYPE from "+CBSystemConstants.opcardUser+"T_A_RULECB a where a.f_zbid='"+oldZbid+"'");
		}
		
		DBManager.update(zbSql, zbParaList);
		DBManager.batchUpdate(cbSqlList.toArray(new String[]{}));
		System.out.println("转换完成");
	}
	
	public static void copyWordByEquipType(String srcEquipType, String tagEquipType) {
		
		
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB a where a.f_zbid in (select zbid from "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.devicetypeid='"+tagEquipType+"')");
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.devicetypeid='"+tagEquipType+"'");
		
		String zbSql = "insert into "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB (ZBID,DEVICETYPEID,BEGINSTATUS,ENDSTATE,STATETYPE,OPCODE,CZRW,VOLT,CARDTYPE,EQUIPID,BZSX) values(?,?,?,?,?,?,?,?,?,?,?)";
		List zbParaList = new ArrayList(); 
		List<String> cbSqlList = new ArrayList();
		List<Map> listMap = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.devicetypeid='"+srcEquipType+"'");
		for(Map map : listMap) {
			String oldZbid = map.get("ZBID").toString();
			String newZbid = java.util.UUID.randomUUID().toString();
			String oldStateCode = map.get("ENDSTATE").toString();
			List<Map> list = DBManager.queryForList("SELECT a.statecode FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a,"+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO b where a.islock = '0' and a.statename=b.statename and a.opcode=b.opcode and a.cardbuildtype=b.cardbuildtype and a.devicetypeid='"+tagEquipType+"' and b.statecode='"+oldStateCode+"'");
			if(list.size() == 0)
				continue;
			String newStateCode = list.get(0).get("statecode").toString();
			Object[] zbPara = new Object[]{newZbid,tagEquipType,StringUtils.ObjToString(map.get("BEGINSTATUS")),newStateCode,StringUtils.ObjToString(map.get("STATETYPE")),map.get("OPCODE"),StringUtils.ObjToString(map.get("CZRW")),StringUtils.ObjToString(map.get("VOLT")),map.get("CARDTYPE"),StringUtils.ObjToString(map.get("EQUIPID")),StringUtils.ObjToString(map.get("BZSX"))};
			zbParaList.add(zbPara);
			cbSqlList.add("insert into "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB(F_ZBID,DEVICESTATEMENTCB,ORDERID) select '"+newZbid+"',DEVICESTATEMENTCB,ORDERID from "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB a where a.f_zbid='"+oldZbid+"'");
		}
		
		DBManager.update(zbSql, zbParaList);
		DBManager.batchUpdate(cbSqlList.toArray(new String[]{}));
		System.out.println("转换完成");
	}
}
