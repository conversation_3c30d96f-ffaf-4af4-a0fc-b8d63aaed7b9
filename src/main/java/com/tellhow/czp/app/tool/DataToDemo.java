package com.tellhow.czp.app.tool;

/**
 * 混淆数据，用于系统演示
 */
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.HashMap;
import java.util.List;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.staticsql.OpeInfo;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

public class DataToDemo {
	public static String encoding="gbk";
	public static void main(String[] args) {
		CreatePowerStationToplogy.buildStation();
		//changeStationToDemo();
		changeSvgToDemo();
	}
	
	public static void changeSvgToDemo() {
		
			File file=new File("tbp_config/graph/map/svg/");
			File[] list = file.listFiles();
			for (File f : list) {
				if(f.isDirectory())
					continue;
				try {
					Document doc=new SAXReader().read(f);
					Element svg = doc.getRootElement();
					List<Element> layers = svg.elements();
					Element textLayer = null;
					for (Element layer :layers ) {
						if("Text_Layer".equals(layer.attributeValue("id"))){
							textLayer=layer;
							break;
						}
					}
					if(textLayer==null){
						doc=null;
						System.gc();
						continue;		
					}
						
					List<Element> txts = textLayer.elements();
					for (Element text : txts) {
						if(text.getName().equals("g")){
							text=text.element("text");						
						}
						if(!text.getText().matches(".*\\d.*")||text.getText().toLowerCase().contains("kv")){
							
							text.setText("Demo");
						}
					}
					writeToFile(doc, f.getPath(), encoding);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		
	}

	public static void changeStationToDemo(){
		HashMap<String, PowerDevice> stations = CBSystemConstants.getMapPowerStation();	
		Object[] stationArry = stations.keySet().toArray();
		for (int i = 0; i < stationArry.length; i++) {
			Object stationName = stationArry[i];
			PowerDevice station = stations.get(stationName);
			String stationID=station.getPowerDeviceID();
//			String sql="update "+CBSystemConstants.opcardUser+"t_e_substation set station_name='演示厂站"+i+"'  where station_id='"+stationID+"'";
			//2014.6.26
			String sql=OPEService.getService().DataToDemoSql()+i+"'  where station_id='"+stationID+"'";
			DBManager.execute(sql);
		}
	}
	public static void writeToFile(Document doc, String filePath,
            String encoding) {
        try {
            OutputFormat fmt = OutputFormat.createPrettyPrint();
            fmt.setEncoding(encoding);

            XMLWriter xmlWriter = new XMLWriter(new OutputStreamWriter(
                    new FileOutputStream(filePath), encoding), fmt);
            xmlWriter.write(doc);
            xmlWriter.close();
            {
            	xmlWriter=null;
            	fmt=null;
            	System.gc();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
