package com.tellhow.czp.app;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.Properties;

import czprule.system.CBSystemConstants;

public class CZPImpl {
	
	private static Properties pro=new Properties();

	static {
		
		try {
			if(CBSystemConstants.appConfig.equals(""))
				pro = getProperties("/AppConfig.properties");
			else
				pro = getProperties(CBSystemConstants.appConfig);
		} catch (Exception e) {
			e.printStackTrace();
		} 
	}
	
	
	public static void setPro(Properties pro) {
		CZPImpl.pro = pro;
	}


	public static Properties getProperties(String name) {
		Properties prop=new Properties();         
		try {
			prop.load(new InputStreamReader(CZPImpl.class.getResourceAsStream(name), "UTF-8"));
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			pro = null;
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			pro = null;
			e.printStackTrace();
		} 
		return prop;
	}

	
	public static String getPropertyValue(String key){
		return pro.getProperty(key);
	}
	
	public static Object getInstance(String key){
		String className = pro.getProperty(key);
		if(className == null)
			return null;
		Object impl = null;
		try {
			impl = Class.forName(className).newInstance();
		} catch (InstantiationException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (ClassNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return impl;
	}

}
