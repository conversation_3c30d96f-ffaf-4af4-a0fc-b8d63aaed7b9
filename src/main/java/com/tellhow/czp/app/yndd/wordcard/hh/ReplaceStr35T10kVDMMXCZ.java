package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr35T10kVDMMXCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("35T10kV单母母线操作".equals(tempStr)){
			RuleBaseMode rbm = CBSystemConstants.getCurRBM();
			String begin = rbm.getBeginStatus();
			String end = rbm.getEndState();
			
			String beginstatus = CBSystemConstants.getCurRBM().getBeginStatus();
			String endstatus = CBSystemConstants.getCurRBM().getEndState();
			CommonFunctionHH cf = new CommonFunctionHH();

			if(Integer.valueOf(beginstatus)>Integer.valueOf(endstatus)){//复电
				
			}else{
				PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
				
				String stationName = CZPService.getService().getDevName(station); 
				String sbName = CZPService.getService().getDevName(curDev); 
				
				List<PowerDevice> hsdevList = new ArrayList<PowerDevice>();
				
				List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				List<PowerDevice> fhczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
				List<PowerDevice> drqkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR, "", false, true, true, true);
				List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Term, SystemConstants.PowerTransformer, true, true, true);
				
				CommonSearch cs=new CommonSearch();
		        Map<String,Object> inPara = new HashMap<String,Object>();
		        Map<String,Object> outPara = new HashMap<String,Object>();
			    inPara.put("oprSrcDevice", curDev);
		        inPara.put("tagDevType", SystemConstants.PowerTransformer); //目标设备主变
		        cs.execute(inPara, outPara);
		        List searchDevs = (ArrayList) outPara.get("linkedDeviceList");
			    if(searchDevs.size()==0&&curDev.getDeviceType().equals(SystemConstants.MotherLine)){
			    	List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "", true, false, true);
			    	for(PowerDevice zb:zbList){
			    		if(RuleExeUtil.isDeviceInDtd(zb)){
			    			searchDevs.add(zb);
				    	}
			    	}
			    }
				
			    List<PowerDevice> zblists = new ArrayList<PowerDevice>();
				
			    String ce = "";
			    
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						zblists.add(dev);
						
						if(RuleExeUtil.getTransformerSwitchSource(dev).size()==2){
							ce = "三";
						}else{
							ce = "两";
						}
					}
				}
				
				List<PowerDevice> hotdevList = new ArrayList<PowerDevice>();
			    List<PowerDevice> colddevList = new ArrayList<PowerDevice>();
				
			    if(mlkgList.size()==0){
			    	hsdevList.addAll(mlkgList);
					hsdevList.addAll(drqkgList);
					hsdevList.addAll(zybList);
			    	
				    for(PowerDevice hsdev :hsdevList){
				    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("1")){
				    		hotdevList.add(hsdev);
				    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("2")){
				    		colddevList.add(hsdev);
				    	}
				    }
			    	
			    	List<PowerDevice> allkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					
					if(allkgList.size()>0){
					    List<PowerDevice> hotList = new ArrayList<PowerDevice>();
					    List<PowerDevice> jdzybList = new ArrayList<PowerDevice>();
					    List<PowerDevice> zbdyckgList = new ArrayList<PowerDevice>();

						for(PowerDevice hsdev :allkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("1")){
								hotList.add(hsdev);
					    	}
							
							if(hsdev.getPowerDeviceName().contains("接地站用变")&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("0")){
								jdzybList.add(hsdev);
							}
							
							if(hsdev.getDeviceRunType().contains(CBSystemConstants.RunTypeSwitchFHC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("0")){
								zbdyckgList.add(hsdev);
							}
						}
						
						if(hotList.size()>0){
					    	replaceStr +="核实"+CZPService.getService().getDevName(hotList)+"热备用/r/n";
						}
						
						if(jdzybList.size()>0){
					    	replaceStr +="红河地调@遥控断开"+CZPService.getService().getDevName(jdzybList)+"/r/n";
					    	replaceStr +="核实"+CZPService.getService().getDevName(jdzybList)+"热备用/r/n";
						}
						
				    	replaceStr += "红河地调配网调控组@核实"+stationName+CZPService.getService().getDevName(curDev)+"上其所管辖的所有10kV出线断路器均己转冷备用/r/n";
				    	
				    	if(zblists.size()>1){
					    	replaceStr += "退出10kV备自投装置/r/n";
				    	}
				    	
				    	if(zbdyckgList.size()>0){
					    	replaceStr +="红河地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckgList)+"/r/n";
					    	
					    	boolean bbzjx = false;
					    	
					    	for(PowerDevice zbdyckg : zbdyckgList){
					    		List<PowerDevice> dzList =  RuleExeUtil.getDeviceDirectList(zbdyckg, SystemConstants.SwitchSeparate);
					    		
					    		if(dzList.size()==1){
					    			bbzjx = true;
					    		}
					    	}
					    	
					    	if(bbzjx){
					    		//for(PowerDevice zbdyckg : zbdyckgList){
						    		replaceStr +="核实"+CZPService.getService().getDevName(zbdyckgList)+"在分闸位置/r/n";
						    	//}
					    		
						    	replaceStr +="核实"+CZPService.getService().getDevName(curDev)+"及母线设备热备用/r/n";
					    		
						    	String drjdbkg = "";
						    	
						    	if(drqkgList.size()>0){
						    		drjdbkg += CZPService.getService().getDevName(drqkgList)+"、";
						    	}

						    	if(jdzybList.size()>0){
						    		drjdbkg += CZPService.getService().getDevName(jdzybList)+"及断路器、";
						    	}
						    	
						    	List<PowerDevice> glkgList = new ArrayList<PowerDevice>();
						    	
						    	for(PowerDevice zbdyckg : zbdyckgList){
						    		List<PowerDevice> dzList =  RuleExeUtil.getDeviceDirectList(zbdyckg, SystemConstants.SwitchSeparate);
						    		
						    		for(PowerDevice dz : dzList){
						    			if(RuleExeUtil.getDeviceEndStatus(dz).equals("1")){
								    		glkgList.add(dz);
						    			}
						    		}
						    	}
						    	
						    	if(glkgList.size()>0){
						    		replaceStr +="拉开"+CZPService.getService().getDevName(glkgList)+"/r/n";
						    	}
						    	
						    	if(drjdbkg.equals("")){
							    	replaceStr +="将"+CZPService.getService().getDevName(curDev)+"及母线设备由热备用转冷备用/r/n";
						    	}else{
						    		if(drjdbkg.endsWith("、")){
						    			drjdbkg = drjdbkg.substring(0, drjdbkg.length()-1);
						    		}
						    		
							    	replaceStr +="将"+CZPService.getService().getDevName(curDev)+"及母线设备、"+drjdbkg+"由热备用转冷备用/r/n";
						    	}
					    	}else{
					    		replaceStr +="核实"+CZPService.getService().getDevName(zbdyckgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备热备用/r/n";
						    	
						    	String drjdbkg = "";
						    	
						    	if(drqkgList.size()>0){
						    		drjdbkg += CZPService.getService().getDevName(drqkgList)+"、";
						    	}

						    	if(jdzybList.size()>0){
						    		drjdbkg += CZPService.getService().getDevName(jdzybList)+"及断路器、";
						    	}
						    	
						    	if(drjdbkg.equals("")){
							    	replaceStr +="将"+CZPService.getService().getDevName(zbdyckgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由热备用转冷备用/r/n";
						    	}else{
						    		if(drjdbkg.endsWith("、")){
						    			drjdbkg.substring(0, drjdbkg.length()-1);
						    		}
						    		
							    	replaceStr +="将"+CZPService.getService().getDevName(zbdyckgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备、"+drjdbkg+"由热备用转冷备用/r/n";
						    	}
					    	}
				    	}
				    	
				    	replaceStr += "退出"+CZPService.getService().getDevName(zblists)+"低后备保护动作跳主变"+ce+"侧断路器";
					}
			    }else{
					List<PowerDevice> allkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> bzkgList = new ArrayList<PowerDevice>();//标准接线开关
					List<PowerDevice> bbzkgList = new ArrayList<PowerDevice>();//非标准接线开关
					List<PowerDevice> bbzdzList = new ArrayList<PowerDevice>();
				    List<PowerDevice> mxkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
				    List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
				    List<PowerDevice> yxmlkgList = new ArrayList<PowerDevice>();
				    List<PowerDevice> bbzmlkgdzList = new ArrayList<PowerDevice>();
				    
				    if(mlkgList.size()>0){
				    	for(PowerDevice mlkg : mlkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(mlkg).equals("0")){
								yxmlkgList.add(mlkg);
							}
						}
				    	
				    	List<PowerDevice> mlkgscList =  RuleExeUtil.getDeviceDirectList(mlkgList.get(0), SystemConstants.SwitchSeparate);
					    List<PowerDevice> mlkgdzList = RuleExeUtil.getDeviceList(mlkgList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
					    List<PowerDevice> mlkgkgList = RuleExeUtil.getDeviceList(mlkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, false);

					    if(mlkgdzList.size()>2){
					    	for(Iterator<PowerDevice> itor = mlkgdzList.iterator();itor.hasNext();){
								PowerDevice dz = itor.next();
								
								if(mlkgscList.contains(dz)){
									itor.remove();
								}
							}
					    	
					    	bbzmlkgdzList.addAll(mlkgdzList);
					    }else{
					    	bbzmlkgdzList.addAll(mlkgkgList);
					    }
				    }
				    
			    	if(mxkgList.size()>0){
			    		for(PowerDevice dev : mxkgList){
			    			if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
									jdzybkgList.add(dev);
								}
								
								if(dev.getPowerDeviceName().contains("站用变")&&!dev.getPowerDeviceName().contains("接地站用变")){
									zybkgList.add(dev);
								}
							}
			    		}
			    	}
				    	
					
			    	allkgList.addAll(fhczbkgList);
			    	allkgList.addAll(drqkgList);
			    	allkgList.addAll(jdzybkgList);
					allkgList.addAll(zybkgList);

					for(PowerDevice allkg : allkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceList(allkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
						
						if(dzList.size()>2){
							for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
								PowerDevice dz = itor.next();
								
								if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
									itor.remove();
								}
							}
							
							if(dzList.size()==1){
								bbzdzList.addAll(dzList);
							}
						}else if(dzList.size()==1){
							bbzkgList.add(allkg);
							bbzdzList.addAll(dzList);
						}
					}
					
					allkgList.addAll(mlkgList);
					
				    for(PowerDevice allkg : allkgList){
				    	if(!bbzkgList.contains(allkg)){
				    		bzkgList.add(allkg);
				    	}
				    }
				    
				    List<PowerDevice> yxbbzkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> rbybbzkgList = new ArrayList<PowerDevice>();

					List<PowerDevice> dkbbzdzList = new ArrayList<PowerDevice>();
					List<PowerDevice> hsbbzdzList = new ArrayList<PowerDevice>();
				    
				    for(PowerDevice dev : bbzkgList){
				    	if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
				    		rbybbzkgList.add(dev);
				    	}else{
				    		yxbbzkgList.add(dev);
				    	}
				    }
				    
				    for(PowerDevice dev : bbzdzList){
				    	if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
				    		dkbbzdzList.add(dev);
				    	}else{
				    		hsbbzdzList.add(dev);
				    	}
				    }
				    
				    if(rbybbzkgList.size()>0){
				    	replaceStr +="核实"+CZPService.getService().getDevName(rbybbzkgList).replace("及", "")+"在分闸位置/r/n";
				    }
				    
				    if(dkbbzdzList.size()>0){
				    	replaceStr +="核实"+CZPService.getService().getDevName(dkbbzdzList)+"在拉开位置/r/n";
				    }
				    
				    if(hsbbzdzList.size()>0){
						List<PowerDevice> tempdzList = new ArrayList<PowerDevice>();
				    	
				    	for(PowerDevice hsbbzdz : hsbbzdzList){
				    		List<PowerDevice> swList =  RuleExeUtil.getDeviceDirectList(hsbbzdz, SystemConstants.Switch);
							List<PowerDevice> mlkglist =  RuleExeUtil.getDeviceList(hsbbzdz, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

				    		if(swList.size()>0){
				    			if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(swList.get(0)).equals("0")&&mlkglist.size()==0){
				    				tempdzList.add(hsbbzdz);
				    			}
				    		}else{
				    			if(mlkglist.size()==0){
				    				tempdzList.add(hsbbzdz);
				    			}
				    		}
				    	}
				    	
				    	if(tempdzList.size()>0){
					    	replaceStr +="核实"+CZPService.getService().getDevName(tempdzList)+"在合闸位置/r/n";
				    	}
				    }
				    
				    List<PowerDevice> lbybzkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> rbybzkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> yxbzkgList = new ArrayList<PowerDevice>();

				    for(PowerDevice dev : bzkgList){
				    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
				    		yxbzkgList.add(dev);
				    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
				    		rbybzkgList.add(dev);
				    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
				    		lbybzkgList.add(dev);
				    	}
				    }
				    
				    if(rbybzkgList.size()>0){
				    	replaceStr +="核实"+CZPService.getService().getDevName(rbybzkgList)+"热备用/r/n";
				    }
				    
				    if(lbybzkgList.size()>0){
				    	replaceStr +="核实"+CZPService.getService().getDevName(lbybzkgList)+"冷备用/r/n";
				    }
				    
				    replaceStr += "红河配调@核实"+stationName+sbName+"上其所管辖的所有10kV出线断路器均己转冷备用/r/n";
				    
				    if(yxmlkgList.size() == 0){
					    replaceStr += "退出10kV备自投装置/r/n";
				    }
				    
				    
			    	
				    List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();

				    yxkgList.addAll(jdzybkgList);
				    yxkgList.addAll(yxmlkgList);
				    yxkgList.addAll(fhczbkgList);
			    	
			    	for(Iterator<PowerDevice> itor = yxkgList.iterator();itor.hasNext();){
						PowerDevice yxkg = itor.next();
						
						if(!RuleExeUtil.getDeviceBeginStatus(yxkg).equals("0")){
							itor.remove();
						}
					}
			    	
			    	replaceStr += cf.getYcDkStrReplace(yxkgList, stationName);
		    	    
		    	    if(yxbbzkgList.size()>0){
			    	    replaceStr += "核实"+CZPService.getService().getDevName(yxbbzkgList)+"在分闸位置/r/n";
		    	    }
		    	    
		    	    if(yxbzkgList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(yxbzkgList)+"、"+sbName+"及母线设备热备用/r/n";
		    	    }else{
						replaceStr += "核实"+sbName+"及母线设备热备用/r/n";
		    	    }
		    	    
		    	    if(hsbbzdzList.size()>0){
			    	    replaceStr += "拉开"+CZPService.getService().getDevName(hsbbzdzList)+"/r/n";
		    	    }

		    	    
		    	    yxbzkgList.addAll(rbybzkgList);
		    	    
		    	    if(end.equals("2")){
		    	    	if(yxbzkgList.size()>0){
								replaceStr += "将"+CZPService.getService().getDevName(yxbzkgList)+"、"+sbName+"及母线设备由热备用转冷备用/r/n";
			    	    }else{
							replaceStr += "核实"+sbName+"及母线设备由热备用转冷备用/r/n";
			    	    }
		    	    	
		    	    	 if(bbzmlkgdzList.size()>0){
				    		 replaceStr += "核实10kV分段"+CZPService.getService().getDevNum(bbzmlkgdzList.get(0))+"隔离手车冷备用/r/n";
			    		}
			    		
			    	    if(drqkgList.size()>0){
			    	    	boolean sc = false;
			    	    	
			    	    	for(PowerDevice drqkg : drqkgList){
			    	    		List<PowerDevice> dzzlList = RuleExeUtil.getDeviceDirectList(drqkg, SystemConstants.SwitchSeparate);
								List<PowerDevice> dzList = RuleExeUtil.getDeviceList(drqkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
								
								if(dzList.size()>2){
									for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
										PowerDevice dz = itor.next();
										
										if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
											sc = true;
										}
										
										if(dzzlList.contains(dz)){
											itor.remove();
										}
									}
									
									if(dzList.size()==1&!sc){
							    		 replaceStr += "核实"+CZPService.getService().getDevName(dzList)+"在拉开位置/r/n";
									}
								}
			    	    	}
			    	    }
			    	    
			    		List<PowerDevice> zbList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "", true, true, true);
			    		
					    if(zbList.size()>0){
						    replaceStr += "退出"+CZPService.getService().getDevName(zbList.get(0))+"10kV侧后备保护动作跳主变"+ce+"侧断路器/r/n";
					    }
		    	    }
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
