package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.MotherLineLoad;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.dao.DeviceStateMentManager;


public class ZTSMJXMXExecute implements RulebaseInf {
	public static Map<String, PowerDevice> tagMap = new HashMap<String, PowerDevice>();

	@Override
	public boolean execute(RuleBaseMode rbm) {
		PowerDevice pd=rbm.getPd();
		
		if(pd == null){
			return false;
		}
	
		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
		MotherLineLoad mll = new MotherLineLoad();//导母
		String begin = rbm.getBeginStatus();
		String end = rbm.getEndState();
		
	 	List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
	 	PowerDevice mlkg = new PowerDevice();
	 	if(mlkgList.size()>0){
	 		mlkg = mlkgList.get(0);
	 	}
	 	
		tagMap.clear();
	 	
		if(station.getPowerVoltGrade() == 220){//220kV厂站的110、220kV母线规则一致
			if(Integer.valueOf(begin) < Integer.valueOf(end)){//母线停电
		 		if(mlkg.getDeviceStatus().equals("0")){//如果是运行
		 			mll.execute(rbm);//导母线
		 		}else if(mlkg.getDeviceStatus().equals("1")){//如果是热备用
		 			if(pd.getPowerVoltGrade() == 110||pd.getPowerVoltGrade() == 35){
						PowerDevice gycmlkg = new PowerDevice();
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		 				
		 				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
							
							if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									gycmlkg = dev;
									break;
								}
							}
						}
		 				
		 				if(gycmlkg.getDeviceStatus().equals("1")){
		 					RuleExeUtil.deviceStatusExecute(gycmlkg, gycmlkg.getDeviceStatus(), "0");
		 				}
		 			}
		 			
	 				RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
		 			mll.execute(rbm);//导母线
		 		}else{
		 			mll.execute(rbm);//导母线
		 		}
			}else{
	 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "1");
	 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
			}
			
			for(PowerDevice mlkgs : mlkgList){
				RuleExecute ruleExecute=new RuleExecute();
				RuleBaseMode rbmode=new RuleBaseMode();
				DeviceStateMentManager dsmm = new DeviceStateMentManager();
				rbmode.setPd(mlkgs);
				rbmode.setBeginStatus(begin);
				rbmode.setEndState(rbm.getEndState());
				
				rbmode.setStateCode(dsmm.getStateCodeByStatus(mlkgs.getDeviceType(), rbm.getEndState(), "9")); //默认取基本操作,操作类型代码是9
				if(!ruleExecute.execute(rbmode)){
					return false;
				}
			}
		}else if(station.getPowerVoltGrade() == 110){
			if(pd.getPowerVoltGrade() == 110){
				if(Integer.valueOf(begin) < Integer.valueOf(end)){//母线停电
			 		if(mlkg.getDeviceStatus().equals("0")){//如果是运行
			 			mll.execute(rbm);//导母线
			 		}else if(mlkg.getDeviceStatus().equals("1")){//如果是热备用
			 			
			 			
						String flag =  "能合环";
						
						if(flag.equals("能合环")){
				 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
						}
						
						List<PowerDevice> kgList = new ArrayList<PowerDevice>();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
						
						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
							
							if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getDeviceStatus().equals("0")){
									kgList.add(dev);
								}
							}
						}
						
						if(flag.equals("不能合环")){
				 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
						}
						
			 			mll.execute(rbm);//导母线
			 		}
				}else{
		 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "1");
		 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
				}
				
				for(PowerDevice mlkgs : mlkgList){
					RuleExecute ruleExecute=new RuleExecute();
					RuleBaseMode rbmode=new RuleBaseMode();
					DeviceStateMentManager dsmm = new DeviceStateMentManager();
					rbmode.setPd(mlkgs);
					rbmode.setBeginStatus(begin);
					rbmode.setEndState(rbm.getEndState());
					
					rbmode.setStateCode(dsmm.getStateCodeByStatus(mlkgs.getDeviceType(), rbm.getEndState(), "9")); //默认取基本操作,操作类型代码是9
					if(!ruleExecute.execute(rbmode)){
						return false;
					}
				}
			}else if(pd.getPowerVoltGrade() == 35){
				if(Integer.valueOf(begin) < Integer.valueOf(end)){//停电
					PowerDevice gycmlkg = new PowerDevice();
					List<PowerDevice> kgList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg = dev;
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getDeviceStatus().equals("0")){
								kgList.add(dev);
							}
						}
					}
					
					if(mlkg.getDeviceStatus().equals("0")){
			 			mll.execute(rbm);//导母线
					}else{
						if(gycmlkg.getDeviceStatus().equals("1")){//如果是热备用
			 				String flag  =  "能合环";
							
							if(flag.equals("能合环")){
					 			RuleExeUtil.deviceStatusExecute(gycmlkg, gycmlkg.getDeviceStatus(), "0");
							}
							
							if(flag.equals("能合环")){
					 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
							}
							
							if(flag.equals("不能合环")){
					 			RuleExeUtil.deviceStatusExecute(gycmlkg, gycmlkg.getDeviceStatus(), "0");
					 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
							}
				 		}
						mll.execute(rbm);//导母线
					}
					
					for(PowerDevice mlkgs : mlkgList){
						RuleExecute ruleExecute=new RuleExecute();
						RuleBaseMode rbmode=new RuleBaseMode();
						DeviceStateMentManager dsmm = new DeviceStateMentManager();
						rbmode.setPd(mlkgs);
						rbmode.setBeginStatus(begin);
						rbmode.setEndState(rbm.getEndState());
						
						rbmode.setStateCode(dsmm.getStateCodeByStatus(mlkgs.getDeviceType(), rbm.getEndState(), "9")); //默认取基本操作,操作类型代码是9
						if(!ruleExecute.execute(rbmode)){
							return false;
						}
					}
				}else{
					PowerDevice gycmlkg = new PowerDevice();
					List<PowerDevice> kgList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg = dev;
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getDeviceStatus().equals("0")){
								kgList.add(dev);
							}
						}
					}
					
					if(gycmlkg.getDeviceStatus().equals("0")){//如果高压侧母联在运行
			 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "1");
			 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
			 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "1");
					}else if(gycmlkg.getDeviceStatus().equals("1")){//如果高压侧母联在热备用
			 			String[] arr = {"能合环","不能合环"};
			 			
			 			int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择当前厂站能否合环", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
			 			
						String flag = "";
						
						if(sel==0){
			 				flag  =  "能合环";
			 			}else if(sel==1){
			 				flag =  "不能合环";
			 			}else if(sel==-1){
			 				return false;
			 			}
						
						String showMessage="请选择合环后需要断开的开关";
						
						EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, kgList , showMessage);
						List<PowerDevice> chooseEquips=ecc.getChooseEquip();
						if(chooseEquips.size()==0)
							return false;
						
						if(flag.equals("能合环")){
				 			RuleExeUtil.deviceStatusExecute(gycmlkg, gycmlkg.getDeviceStatus(), "0");
						}
						
						PowerDevice dev = chooseEquips.get(0);
						RuleExeUtil.deviceStatusExecuteJB(dev, dev.getDeviceStatus(), "1");
						
						if(flag.equals("能合环")){
				 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "1");
						}
						
						tagMap.put(flag, dev);
						
						if(flag.equals("不能合环")){
				 			RuleExeUtil.deviceStatusExecute(gycmlkg, gycmlkg.getDeviceStatus(), "0");
				 			RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
						}
						
			 			mll.execute(rbm);//导母线
					}
					
		 			mll.execute(rbm);//导母线
				}
			}
		}
		
		return true;
	}

}
