package com.tellhow.czp.app.yndd.wordcard.dl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDLSMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("大理双母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			PowerDevice othermx = new PowerDevice();

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.isSwitchDoubleML(dev)){
					
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					for(PowerDevice mx : mxList){
						if(!mx.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
							othermx = mx ;
							break;
						}
					}
				}
			}
			
			String othermxName = CZPService.getService().getDevName(othermx);
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
			}
			
			for(PowerDevice dev : mlkgList){
				replaceStr += "大理地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
			}
			
			List<PowerDevice> yxList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbyList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();

			tempList.addAll(zbkgList);
			tempList.addAll(xlkgList);

			for(PowerDevice dev : tempList){
				List<PowerDevice> dzList =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbyList.add(dev);
							break;
						}
					}
				}
			}
			
			String yxdevName = "";
			
			for(PowerDevice dev : yxList){
				yxdevName += CZPService.getService().getDevName(dev) + "、";
			}
			
			if(yxdevName.endsWith("、")){
				yxdevName = yxdevName.substring(0, yxdevName.length() - 1);
				replaceStr += stationName+"@将"+yxdevName+"由"+othermxName+"运行倒至"+deviceName+"运行/r/n";
			}
			
			String rbydevName = "";
			
			for(PowerDevice dev : rbyList){
				rbydevName += CZPService.getService().getDevName(dev) + "、";
			}
			
			if(rbydevName.endsWith("、")){
				rbydevName = rbydevName.substring(0, rbydevName.length() - 1);
				replaceStr += stationName+"@将"+rbydevName+"由"+othermxName+"运行倒至"+deviceName+"热备用/r/n";
			}
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
