package com.tellhow.czp.app.yndd.wordcard.zt;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;

import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrZTKGDMCZRW implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("昭通开关倒母操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			String curMxName = "";
			String otherMxName = "";
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

			for(PowerDevice dev : dzList){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
					
					if(dev.getDeviceStatus().equals("0")){
						for(PowerDevice mx : mxList){
							otherMxName = CZPService.getService().getDevName(mx);
							break;
						}
					}else if(dev.getDeviceStatus().equals("1")){
						for(PowerDevice mx : mxList){
							curMxName = CZPService.getService().getDevName(mx);
							break;
						}
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("1")){
				replaceStr += "将"+stationName+deviceName+"由"+curMxName+"热备用倒至"+otherMxName+"热备用/r/n";
			}else if(curDev.getDeviceStatus().equals("0")){
				replaceStr += "将"+stationName+deviceName+"由"+curMxName+"运行倒至"+otherMxName+"运行/r/n";
			}
		}
		
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
