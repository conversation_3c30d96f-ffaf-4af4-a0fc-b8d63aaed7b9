package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.GridLayout;
import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JPanel;

import com.sun.org.apache.bcel.internal.generic.NEW;
import com.tellhow.czp.app.yndd.tool.JCheckBoxHideToolTip;

import czprule.datemodule.TopBottomLineBorder;
import czprule.system.ShowMessage;

public class ProtectChooseWindow  extends javax.swing.JDialog {
	//是否勾选选项之外的参数组合:第0个元素为名称，后面的为勾选选项；
	//是否勾选选项的参数组合：第0个元素为""（空），第1个元素为名称，后面的为勾选选项；
	private ArrayList<String[]> equipsList;
	private ArrayList<Map> returnList = new  ArrayList<Map>();
	private static  Map<String, String> selectMap = new LinkedHashMap<String, String>();
	private JCheckBoxHideToolTip[] jcbDUO; 
	private JCheckBoxHideToolTip[] jcbDAN;
	public Map<String, String> getSelectMap() {
		return selectMap;
	}

	public void setSelectMap(Map<String, String> retMap) {
		this.selectMap = retMap;
	}
	int duoxuanCount =0;
	int danxuancount =0;
	public ProtectChooseWindow(java.awt.Frame parent, boolean modal,
			ArrayList<String[]> equipsList, String showMessage,int width) {
		super(parent, modal);
		this.setTitle(showMessage);
		this.equipsList =equipsList;
		initComponents(width);
	}
	private JButton jButton1 = new JButton();
	private JButton jButton2 = new JButton();
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	
	private void initComponents(int width) {
		
		ArrayList<String[]> danxuanList = new ArrayList<String[]>();
		ArrayList<String[]> duoxuanList = new ArrayList<String[]>();
		
		for(int i=0;i<equipsList.size();i++){
			if(equipsList.get(i)[0].equals("")){
				danxuancount++;
				danxuanList.add(equipsList.get(i));
			}else{
				duoxuanCount++;
				duoxuanList.add(equipsList.get(i));
			}
		}
		
		
		this.setLayout(new BorderLayout());
		int bottomSize =0;
		if(danxuancount>0){
			bottomSize=120;
		}else{
			bottomSize=60;
		}
		this.setSize(width, 240);//38是窗口上下边框高度之和
		this.setLocationCenter();
		//选择面板
		JPanel mainPanel = new JPanel();
		mainPanel.setLayout(new BorderLayout());
		this.add(mainPanel,BorderLayout.CENTER);
		//按钮面板
		JPanel bottomPanel =new JPanel();
		bottomPanel.setBorder(new TopBottomLineBorder(Color.GRAY));
		bottomPanel.setPreferredSize(new Dimension(width,60));
		bottomPanel.setLayout(new FlowLayout(FlowLayout.CENTER,25,18));
		this.add(bottomPanel,BorderLayout.SOUTH);
		
		
		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
		jButton1.setToolTipText("确定");
		jButton1.setText("确定");
		jButton1.setMargin(new java.awt.Insets(1,1,1,1));
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png"))); // NOI18N
		jButton2.setToolTipText("取消");
		jButton2.setText("取消");
		jButton2.setMargin(new java.awt.Insets(1,1,1,1));
		jButton2.setFocusPainted(false);
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});
		
		bottomPanel.add(jButton1);
		bottomPanel.add(jButton2);
		
		//选择面板内多选面板
		JPanel mainCenterPanel =new JPanel();
//		mainCenterPanel.setBorder(new TopBottomLineBorder(Color.GRAY));
		mainCenterPanel.setLayout(new GridLayout(duoxuanCount, 1));
		mainPanel.add(mainCenterPanel,BorderLayout.CENTER);
		
		
		//选择面板内单选面板
		JPanel mainFootPanel =new JPanel();
		if(bottomSize==60){
			mainFootPanel.setPreferredSize(new Dimension(width,0));
		}else{
			mainFootPanel.setPreferredSize(new Dimension(width,60));
		}
		mainFootPanel.setLayout(new FlowLayout(FlowLayout.LEFT,10,5));
		mainPanel.add(mainFootPanel,BorderLayout.SOUTH);

		
		
		
		jcbDAN = new JCheckBoxHideToolTip[danxuancount];
		
		for(int i=0;i<danxuancount;i++){
			jcbDAN[i] = new JCheckBoxHideToolTip() ;
			jcbDAN[i].setText(danxuanList.get(i)[1]);
			jcbDAN[i].setToolTipText(danxuanList.get(i)[1]);
			mainFootPanel.add(jcbDAN[i]);
		}
		
		int checkboxduo =0;
		for(int i=0;i<duoxuanList.size();i++){
			checkboxduo+=duoxuanList.get(i).length-1;
		}
		jcbDUO =new JCheckBoxHideToolTip[checkboxduo];
		
		
		JPanel[] jpDUO = new JPanel[duoxuanCount];
		int num =checkboxduo;
		for(int i=0;i<duoxuanCount;i++){
			jpDUO[i]= new JPanel();
			jpDUO[i].setLayout(new FlowLayout(FlowLayout.LEFT,20,20));
			jpDUO[i].setBorder(BorderFactory.createTitledBorder(duoxuanList.get(i)[0]));
			for(int j =0;j<duoxuanList.get(i).length-1;j++){
				jcbDUO[checkboxduo-num]=new JCheckBoxHideToolTip();
				jcbDUO[checkboxduo-num].setText(duoxuanList.get(i)[j+1]);
				jcbDUO[checkboxduo-num].setToolTipText(duoxuanList.get(i)[0]);
				jpDUO[i].add(jcbDUO[checkboxduo-num]);
				num--;
			}
			mainCenterPanel.add(jpDUO[i]);
		}
		
		

//		JCheckBox[] jcbDuo = new JCheckBox[danxuancount];
		
		
		
		
		
		
		this.setVisible(true);
	}
	//确定
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		selectMap.clear();
		for(int i=0;i<jcbDUO.length;i++){
			if(jcbDUO[i].isSelected()){
				selectMap.put(jcbDUO[i].getText(), jcbDUO[i].getToolTipText());
			}
		}
		for(int i=0;i<jcbDAN.length;i++){
			if(jcbDAN[i].isSelected()){
				selectMap.put(jcbDAN[i].getText(), jcbDAN[i].getToolTipText());
			}
		}
		
		if(selectMap.size()==0){
			ShowMessage.view("请至少选择一个厂站");
			this.setVisible(true);
		}else{
			this.setVisible(false);
		}
	}
	//取消
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		this.setVisible(false);
		this.dispose();
	}
	
	
	
	
	
	
	
	
	
	
	
	
	
}
