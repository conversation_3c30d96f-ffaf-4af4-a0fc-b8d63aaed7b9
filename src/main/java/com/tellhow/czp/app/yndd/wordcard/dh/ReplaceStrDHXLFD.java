package com.tellhow.czp.app.yndd.wordcard.dh;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dh.DHJDKGXZ;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDH;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDHXLFD implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("德宏线路复电".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			String devName = CZPService.getService().getDevName(sourceLineTrans);

			replaceStr += CommonFunctionDH.getPowerOnLineCheckContent();
			replaceStr += CommonFunctionDH.getPowerOnCheckContent();
			
			List<Map<String, String>> stationLineList = CommonFunctionDH.getStationLineList(curDev);
			
			boolean isControl = true;

			for(PowerDevice dev : loadLineTrans){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionDH.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(sourceLineTrans!=null){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionDH.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			String[] lineNameArr = {"德傣Ⅰ回线","德傣Ⅱ回线","卡盈线"};
 			
			for(String lineName : lineNameArr){
				if(curDev.getPowerDeviceName().contains(lineName)){
					isControl = false;
				}
			}
			
			if(stationLineList.size() > 0){
				isControl = false;
			}
			
			for(PowerDevice loadLineTran : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				
				List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							hignVoltMlkgList.add(dev);
						}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							hignVoltXlkgList.add(dev);
						}
					}
				}
				
				List<PowerDevice> tempList = new ArrayList<PowerDevice>();
				
				tempList.addAll(hignVoltXlkgList);
				tempList.addAll(hignVoltMlkgList);
				
				for(PowerDevice dev : tempList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("3")){
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					  
					if(DHJDKGXZ.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
						 replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(curDev)+"线路侧三相接地线/r/n";
					}else{
						 replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
					}
				}
				
				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
						
					if(jddzList != null){
						 if(DHJDKGXZ.chooseEquips.contains(dev)||jddzList.size()==0){
							  replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(curDev)+"线路侧三相接地线/r/n";
						 }else{
							  replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
						 }
					}
				}
				
				for(Map<String,String> map : stationLineList){
					String unit = StringUtils.ObjToString(map.get("UNIT"));
					String linename = StringUtils.ObjToString(map.get("LINE_NAME"));
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
					String endpointtype = StringUtils.ObjToString(map.get("ENDPOINT_TYPE"));
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));
					String grounddisconnectorname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));

					boolean isccdx =  false;
					
					for(PowerDevice dev : DHJDKGXZ.chooseEquips){
						if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
							isccdx = true;
							replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧三相接地线/r/n";
							break;
						 }
					}
					
					if(!isccdx){
						if(operationkind.equals("下令")){
							if(grounddisconnectorname.equals("")){
								replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧三相接地线/r/n";
							}else{
								replaceStr += unit+"@拉开"+lowerunit+grounddisconnectorname+"/r/n";
							}
						}else if(operationkind.equals("许可")){
							if(grounddisconnectorname.equals("")){
								replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧三相接地线/r/n";
							}else{
								replaceStr += unit+"@拉开"+lowerunit+grounddisconnectorname+"/r/n";
							}
						}else if(operationkind.equals("配合")){
							replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧三相接地线/r/n";
						}
					}
				}
			}
			
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			
			kgList.addAll(xlkgList);
			kgList.addAll(zbkgList);

			boolean isRunModelThreeTwo = false;
			
			for(PowerDevice dev : kgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					isRunModelThreeTwo = true;
					break;
				}
			}
			
			if(isRunModelThreeTwo){
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制前的要求进行设置/r/n";
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							Collections.reverse(dzList);

							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "德宏地调@遥控合上"+stationName+devname+"/r/n";
								}
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							Collections.reverse(dzList);

							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "德宏地调@遥控合上"+stationName+devname+"/r/n";
								}
							}
						}
					}
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制后的要求进行设置/r/n";
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "德宏地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								
								if(curDev.getPowerVoltGrade() > 110){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									dzList = RuleExeUtil.sortByMXC(dzList);
									
									for(PowerDevice dz : dzList){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"在合上位置/r/n";
										}
									}
								}
							}else{
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						replaceStr += stationName+"@将"+lowerunit+switchName+"由冷备用转热备用/r/n";
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								
								replaceStr += "德宏地调@遥控退出"+stationName+deviceName+"检同期软压板/r/n";
								replaceStr += "德宏地调@遥控投入"+stationName+deviceName+"检无压软压板/r/n";
								replaceStr += "德宏地调@遥控合上"+stationName+deviceName+"对线路充电/r/n";
								replaceStr += "德宏地调@遥控退出"+stationName+deviceName+"检无压软压板/r/n";
								replaceStr += "德宏地调@遥控投入"+stationName+deviceName+"检同期软压板/r/n";
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "德宏地调@遥控用"+stationName+deviceName+"同期合环/r/n";
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> xlkgLoadList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					for(PowerDevice dev : xlkgLoadList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							String deviceName = CZPService.getService().getDevName(dev);
							replaceStr += "德宏地调@遥控用"+stationName+deviceName+"同期合环/r/n";
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						replaceStr += stationName+"@用"+lowerunit+switchName+"同期合环/r/n";
					}
				}
			}else if(isControl){
				if(RuleExeUtil.isDeviceHadStatus(sourceLineTrans, "2", "1")){
					replaceStr += "德宏地调@执行"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用程序操作/r/n";
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).endsWith("0")){
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "德宏地调@遥控合上"+stationName+deviceName+"对线路充电/r/n";
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
						for(PowerDevice dev : xlswList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionDH.getHhContent(dev, "德宏地调", stationName);
							}
						}
					}
				}
			}else{
				for(Map<String,String> map : stationLineList){
					String stationName = StringUtils.ObjToString(map.get("UNIT"));
					String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
					String endpointtype = StringUtils.ObjToString(map.get("ENDPOINT_TYPE"));
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));
					String ptdisconnectorName = StringUtils.ObjToString(map.get("PTDISCONNECTOR_NAME"));
					String grounddisconnectorname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));

					if(operationkind.equals("下令")){
						if(!disconnectorName.equals("")){
							replaceStr += stationName+"@合上"+lowerunit+disconnectorName+"/r/n";
						}else if(!switchName.equals("")){
							replaceStr += stationName+"@将"+lowerunit+lineName+"由冷备用转热备用/r/n";
						}
						
						if(!ptdisconnectorName.equals("")){
							if(ptdisconnectorName.contains("、")){
								String[] ptdisconnectorNameArr = ptdisconnectorName.split("、");
								
								for(String dzName : ptdisconnectorNameArr){
									replaceStr += stationName+"@将"+lowerunit+dzName+"由冷备用转运行/r/n";
								}
							}else{
								if(ptdisconnectorName.contains("站用变")){
									replaceStr += stationName+"@确认已将"+lowerunit+ptdisconnectorName+"由冷备用转运行/r/n";
								}else{
									replaceStr += stationName+"@将"+lowerunit+ptdisconnectorName+"由冷备用转运行/r/n";
								}
							}
						}
					}else if(operationkind.equals("许可")){
						if(!ptdisconnectorName.equals("")){
							if(ptdisconnectorName.contains("、")){
								String[] ptdisconnectorNameArr = ptdisconnectorName.split("、");
								
								for(String dzName : ptdisconnectorNameArr){
									if(dzName.contains("隔离开关")){
										replaceStr += stationName+"@确认已合上"+lowerunit+dzName+"/r/n";
									}else{
										replaceStr += stationName+"@确认已将"+lowerunit+dzName+"由冷备用转运行/r/n";
									}
								}
							}else{
								if(ptdisconnectorName.contains("隔离开关")){
									replaceStr += stationName+"@确认已合上"+lowerunit+ptdisconnectorName+"/r/n";
								}else{
									replaceStr += stationName+"@确认已将"+lowerunit+ptdisconnectorName+"由冷备用转运行/r/n";
								}
							}
						}
						
						if(!disconnectorName.equals("")){
							if(disconnectorName.contains("、")){
								String[] disconnectorNameArr = disconnectorName.split("、");
								
								for(String dzName : disconnectorNameArr){
									replaceStr += stationName+"@确认已合上"+lowerunit+dzName+"/r/n";
								}
							}else{
								replaceStr += stationName+"@确认已合上"+lowerunit+disconnectorName+"/r/n";
							}
						}else{
							if(!switchName.equals("")){
								replaceStr += stationName+"@确认已将"+lowerunit+lineName+"由冷备用转热备用/r/n";
							}
						}
					}else if(operationkind.equals("配合")){
						if(!switchName.equals("")){
							if(switchName.contains("、")){
								String[] switchNameArr = switchName.split("、");
								
								for(String kgName : switchNameArr){
									replaceStr += stationName+"@确认已将"+lowerunit+kgName+"由冷备用转热备用/r/n";
								}
							}else{
								replaceStr += stationName+"@确认已将"+lowerunit+switchName+"由冷备用转热备用/r/n";
							}
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "德宏地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
							}else{
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
									String deviceName = CZPService.getService().getDevName(sourceLineTrans);
									replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
								}
							}
						}
					}
					
					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+sourceLineTrans.getPowerDeviceID()+"'";
					List<Map<String,String>> zybNameList=DBManager.queryForList(sql);
					    
				    for(Map<String,String> zybNameMap : zybNameList){
			    		String zybName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
			    		replaceStr += stationName+"@确认已将"+zybName+"由冷备用转运行/r/n";
				    }
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

					if(stationName.equals("220kV傣龙变") || stationName.equals("220kV卡场变")){
						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();

						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
							
							if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev);
								}
							}
						}
						
						for(PowerDevice dev : hignVoltMlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += "德宏地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								}else{
									String deviceName = CZPService.getService().getDevName(loadLineTran);
									replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
								}
							}
						}
						
						for(PowerDevice dz : xldzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
								replaceStr += CommonFunctionDH.getKnifeOnContent(xldzList, stationName);
							}
						}
					}else if(xldzList.size() == 1 && xlswList.size() == 0){
						for(PowerDevice dz : xldzList){
							if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
								replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dz)+"/r/n";
							}
						}
					}else{
						for(PowerDevice dev : xlswList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += "德宏地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								}else{
									if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
										String deviceName = CZPService.getService().getDevName(loadLineTran);
										replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
									}
								}
							}
						}
					}
					
					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+loadLineTran.getPowerDeviceID()+"'";
					List<Map<String,String>> zybNameList=DBManager.queryForList(sql);
					    
				    for(Map<String,String> zybNameMap : zybNameList){
			    		String zybName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
			    		replaceStr += stationName+"@确认已将"+zybName+"由冷备用转运行/r/n";
				    }
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).endsWith("0")){
					for(Map<String, String> map : stationLineList) {
						String unit = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
						replaceStr += unit+"@确认"+lineName+"具备送电条件/r/n";
					}
					
					for(Map<String, String> map : stationLineList) {
						String unit = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();

						if(operationkind.equals("配合")){
							if(!switchName.equals("")){
								if(switchName.contains("、")){
									String[] switchNameArr = switchName.split("、");
									
									for(String kgName : switchNameArr){
										replaceStr += unit+"@确认已合上"+lowerunit+kgName+"/r/n";
									}
								}else{
									replaceStr += unit+"@确认已合上"+lowerunit+switchName+"/r/n";
								}
							}
						}
					}
					
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						for(PowerDevice dev : xlswList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "德宏地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对线路充电/r/n";
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
							
							if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev);
								}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev);
								}
							}
						}
						
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionDH.getHhContent(dev, "德宏地调", stationName);
							}
						}
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								replaceStr += CommonFunctionDH.getSwitchOffContent(dev, stationName, station);
							}
						}
					}
					
					for(Map<String, String> map : stationLineList) {
						String unit = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
						
						if(operationkind.equals("许可")){
							replaceStr += unit+"@通知"+lineName+"已送电/r/n";
						}else if(operationkind.equals("下令")){
							if(!switchName.equals("")){
								replaceStr += unit+"@合上"+lowerunit+switchName+"/r/n";
							}
						}
					}
				}
			}
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
