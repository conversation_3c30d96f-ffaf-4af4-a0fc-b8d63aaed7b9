package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrJDZYBZTQ implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("接地站用变自投切".equals(tempStr)){
			List<PowerDevice> jdbList = new ArrayList<PowerDevice>();

			List<PowerDevice> dyczbkgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> allkglist = new ArrayList<PowerDevice>();
			for(PowerDevice dyczbkg : dyczbkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dyczbkg).equals("0")){
					List<PowerDevice> mxlist =  RuleExeUtil.getDeviceList(dyczbkg, SystemConstants.MotherLine,  SystemConstants.Switch, true, true, true);

					allkglist =  RuleExeUtil.getDeviceList(mxlist.get(0), SystemConstants.Switch,  SystemConstants.PowerTransformer, true, true, true);
				}
			}
			
			for(PowerDevice dev : allkglist){
				if(dev.getPowerVoltGrade() == 10&&dev.getDeviceType().equals(SystemConstants.Switch)&&(dev.getPowerDeviceName().contains("接地站用变")||dev.getPowerDeviceName().contains("接地变"))){
					jdbList.add(dev);
				}
			}
			
			String num = "";
			
			RuleExeUtil.swapDeviceList(jdbList);
			
			for(PowerDevice jdb : jdbList){
				String jdbName = "";
				
				if(jdb.getPowerDeviceName().indexOf("接地站用变")>0){
					jdbName = jdb.getPowerDeviceName().substring(0, jdb.getPowerDeviceName().indexOf("接地站用变"));
				}else if(jdb.getPowerDeviceName().indexOf("接地变")>0){
					jdbName = jdb.getPowerDeviceName().substring(0, jdb.getPowerDeviceName().indexOf("接地变"));
				}
				
				
				num += CZPService.getService().getDevNum(jdbName)+"、";
			}
			
			if(num.endsWith("、")){
				num =num.substring(0, num.length()-1);
			}
			
			if(stationDev.getPowerStationName().contains("文澜变")){
				replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(CBSystemConstants.getPowerStation(curDev.getPowerStationID()))+CZPService.getService().getDevName(jdbList)+"/r/n";
				replaceStr += "核实"+CZPService.getService().getDevName(jdbList)+"热备用/r/n";
			}else{
				replaceStr = "退出10kV"+num+"接地站用变消弧线圈并联小电阻自投切功能";
			}
			
			if(num.equals("")){
				replaceStr = null;
			}
			
		}
		return replaceStr;
	}

}
