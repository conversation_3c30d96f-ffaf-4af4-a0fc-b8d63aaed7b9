package com.tellhow.czp.app.yndd.view;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.table.DefaultTableModel;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;
import org.xml.sax.SAXException;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.yndd.tool.XmlUtil;
import com.tellhow.czp.operationcard.EchoReplace;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;

public class VOSWritCheck {
	private JTable jTable = null;// 操作指令表
	private JTextArea jTextArea = null;// 操作任务
	int dwI;//操作单位所在列
	int zlI;//操作指令所在列
	int jhI;//校核所在列
	boolean isSuccess = true;//操作票校验结果
	EchoReplace ec = new EchoReplace();
	
	public VOSWritCheck(JTable jTable, JTextArea jTextArea) {
		this.jTable = jTable;
		this.jTextArea = jTextArea;
		excute();
	}
	/**
	 * 执行类
	 */
	private void excute() {
			CBSystemConstants.lcm = null;
			final ArrayList<int[]> errorList = new ArrayList<int[]>();
			errorList.clear();
			new Thread(new Runnable() {
				public void run() {
					DefaultTableModel jTableModel = (DefaultTableModel) jTable.getModel();
				try{
					CreatePowerStationToplogy.loadSysData();
					String czrw = jTextArea.getText();
					StringBuffer param = new StringBuffer();
							
					param.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>"+czrw+"</CZRW>");
			
					for(int i = 0; i < jTable.getRowCount(); i++) {
						String stationName = StringUtils.ObjToString(jTableModel.getValueAt(i, 3)).trim();
						String oprdesc = StringUtils.ObjToString(jTableModel.getValueAt(i, 4)).trim();
						
						param.append("<ITEM>"
								+ "<changzhan>"+stationName+"</changzhan>"
								+ "<caozuozhiling>"+oprdesc+"</caozuozhiling>"
								+ "<cbid>"+(i+1)+"</cbid>"
								+ "<refresh>true</refresh>"
								+ "<zhixing>false</zhixing>"
								+ "<roleCode>0</roleCode>"
								+ "<ischeck>1</ischeck>"
								+ "<isrealtime>0</isrealtime>"
								+ "</ITEM>");
					}
					param.append("</Datas>");
					
					String endpoint = CZPImpl.getPropertyValue("GetCheckOneUrl");
					
					Service service = new Service();
					Call call = (Call) service.createCall();
					call.setTargetEndpointAddress(new java.net.URL(endpoint));
					
					call.addParameter("key",
							org.apache.axis.encoding.XMLType.XSD_STRING,
							javax.xml.rpc.ParameterMode.IN);// 接口的参数
					call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);// 设置返回类型
					String operation = "getCheckOne";
					call.setOperationName(operation);// WSDL里面描述的接口名称
					String ret=(String) call.invoke(new Object[] { param.toString() });
					List<Map<String, Object>> list = analysisXml(ret);
				}catch (Exception e) {
					e.printStackTrace();
				}finally{
					if(CBSystemConstants.lcm != null){
						CBSystemConstants.lcm.clear();
					}
				}
				}
			}).start();
	}
	
    public static List<Map<String, Object>> analysisXml(String arg){
		String xmlCode = XmlUtil.getXMLcode(arg);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        InputStream is;
		try {
			is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
	        DocumentBuilder db = dbf.newDocumentBuilder();
	        org.w3c.dom.Document document = db.parse(is);
	        DOMReader domReader = new DOMReader();
	        Document ret = domReader.read(document);
	        Element root = ret.getRootElement();
	        //获取ITEM节点DOM
	        List<Element> itemLists =root.elements("ITEM");
	        for (int i = 0; i <itemLists.size(); i++) {
	            Map<String, Object> mapInfo =new HashMap<String, Object>();
	            Element element = itemLists.get(i);
	            List<Element> elist = element.elements();
	            for (int j = 0; j < elist.size(); j++) {
	                Element el = elist.get(j);
	                //将节点名称与值放入集合
	                mapInfo.put(el.getName(), el.getTextTrim());
	            }
	            list.add(mapInfo);
	        }
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (SAXException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (ParserConfigurationException e) {
			e.printStackTrace();
		}
        return list;
    }
	
	public void setCellCoin(RuleBaseMode rbm,JTable jTable1,int i,EchoReplace ec){
		int WarFlag = -1;
		List<CheckMessage> msgList = new ArrayList<CheckMessage>();
		if(CBSystemConstants.lcm != null && CBSystemConstants.lcm.size() > 0) {
			WarFlag = 0;	
			boolean check =true;
			String czrw = jTable1.getModel().getValueAt(i, zlI).toString();
			for (CheckMessage msg : CBSystemConstants.lcm) {
				if(msg.getPd().size()>0){
					check=false;
					if (WarFlag != 1 && msg.getBottom().substring(0, 1).equals("1")) {
						WarFlag = 1;
					}else if(WarFlag != 2 && msg.getBottom().substring(0, 1).equals("3")) {
						WarFlag = 2;
					}
					//String str = ec.getEcho(msg, rbm.getPd());
					msgList.add(msg);
				}
			}
			
			if(check){
				rbm.setCheckResult(0);
				RuleBaseMode rbmPre = null;
				Object obj = jTable1.getValueAt(i, jhI);
			   if(obj != null && obj instanceof RuleBaseMode)
				   rbmPre = (RuleBaseMode)obj;
			   if(rbmPre == null || rbmPre.getCheckResult() == 0)
				   jTable1.setValueAt(rbm, i, jhI);
			   else if(rbm.getCheckResult() != 0){
				   rbmPre.getMessage().get("sbxx").addAll(rbm.getMessage().get("sbxx"));
			   }
				return;
			}
			
			int result = 0;
			if(WarFlag == -1)
				result = 0;
			else if(WarFlag == 0)
				result = 1;
			else if(WarFlag == 1)
				result = 2;
			else if(WarFlag == 2)
				result = 2;
			
			rbm.setCheckResult(result);
			Map<String, List<CheckMessage>> map = new HashMap<String, List<CheckMessage>>();
			CBSystemConstants.lcm.clear();
			map.put("sbxx", msgList);
			rbm.setMessage(map);
		}
		else
			rbm.setCheckResult(0);
		
		RuleBaseMode rbmPre = null;
		Object obj = jTable1.getValueAt(i, jhI);
	   if(obj != null && obj instanceof RuleBaseMode)
		   rbmPre = (RuleBaseMode)obj;
	   if(rbmPre == null || rbmPre.getCheckResult() == 0)
		   jTable1.setValueAt(rbm, i, jhI);
	   else if(rbm.getCheckResult() != 0){
		   rbmPre.getMessage().get("sbxx").addAll(rbm.getMessage().get("sbxx"));
	   }
//		if(CBSystemConstants.lcm != null)
//			CBSystemConstants.lcm.clear();
   }
	
}
