package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHH23JXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("红河二分之三接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			/*List<PowerDevice> kgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

			RuleExeUtil.swapDeviceListNum(kgList);
			Collections.reverse(kgList);
			
			for(PowerDevice dev : kgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}*/
			replaceStr += "红河地调@执行"+stationName+deviceName+"由运行转热备用程序操作/r/n";
			
			if(curDev.getDeviceStatus().equals("2")){
				replaceStr += "红河地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
			}
		}
		
		return replaceStr;
	}

}
