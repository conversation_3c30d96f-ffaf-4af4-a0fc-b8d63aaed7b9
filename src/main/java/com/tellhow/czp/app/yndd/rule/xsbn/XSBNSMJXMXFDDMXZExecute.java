package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.SwitchChangeMotherLine;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;


public class XSBNSMJXMXFDDMXZExecute implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		PowerDevice pd=rbm.getPd();

		if(pd == null){
			return false;
		}
		
		String showMessage="请选择需要倒在"+CZPService.getService().getDevName(pd)+"上的开关";
		
		List<PowerDevice> kgList = new ArrayList<PowerDevice>();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		
		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
				if((dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL))||(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
						&&(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")||RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1"))){
					kgList.add(dev);
				}
			}
		}
		
		EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, kgList , showMessage);
		List<PowerDevice> chooseEquips=ecc.getChooseEquip();
		
		if(chooseEquips.size()==0)
			return true;
		
		SwitchChangeMotherLine scml = new SwitchChangeMotherLine();
		
		for(PowerDevice chooseEquip:chooseEquips){
			RuleBaseMode chooserbm = new RuleBaseMode();
			chooserbm.setPd(chooseEquip);
			chooserbm.setBeginStatus(rbm.getBeginStatus());
			chooserbm.setEndState(rbm.getEndState());
			scml.execute(chooserbm);
		}
		
		return true;
	}

}
