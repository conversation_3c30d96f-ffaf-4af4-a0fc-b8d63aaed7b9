package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr220T35kVDMMXCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("220T35kV单母母线操作".equals(tempStr)){
			CommonFunctionHH cf = new CommonFunctionHH();

			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();//临时数组,使用完要clear
			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> fhczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> yxxlkgList = new ArrayList<PowerDevice>();
			
			if(xlkgList.size()>0){
				for(PowerDevice xlkg : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(xlkg).equals("0")){
						yxxlkgList.add(xlkg);
					}
				}
			}
			
			replaceStr += cf.getHsdcnrStrReplace(curDev);
			
			if(mlkgList.size()==0){
				List<PowerDevice> kgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,  true, true, true);
				List<PowerDevice> zbList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,  true, true, true);

				List<PowerDevice> yxkgList =  new ArrayList<PowerDevice>();
				List<PowerDevice> hotdevList =  new ArrayList<PowerDevice>();
				List<PowerDevice> colddevList =  new ArrayList<PowerDevice>();

			    List<PowerDevice> yxdevList = new ArrayList<PowerDevice>();

				
				for(PowerDevice kg : kgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(kg).equals("0")&&kg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
			    		yxdevList.add(kg);
			    	}
					
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(kg).equals("0")){
						yxkgList.add(kg);
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(kg).equals("1")){
						hotdevList.add(kg);
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(kg).equals("2")){
						colddevList.add(kg);
					}
				}
				
			    for(PowerDevice mlkg : mlkgList){
			    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(mlkg).equals("0")){
					    yxdevList.add(mlkg);
			    	}
			    }
				    
			    for(PowerDevice fhczbkg : fhczbkgList){
			    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(fhczbkg).equals("0")){
					    yxdevList.add(fhczbkg);
			    	}
			    }
				
				if(hotdevList.size()>0){
					replaceStr += "核实"+CZPService.getService().getDevName(hotdevList)+"热备用/r/n";
				}
				
				if(colddevList.size()>0){
					replaceStr += "核实"+CZPService.getService().getDevName(colddevList)+"冷备用/r/n";
				}
				
				if(yxdevList.size()>0){
					replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(yxdevList)+"/r/n";
				}
				
				tempList.addAll(yxdevList);
				tempList.addAll(hotdevList);
				
				replaceStr += cf.getCzMotherLineDevStrReplace(tempList, curDev,null, stationName, "由热备用转冷备用");
				replaceStr += "退出"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套35kV侧后备保护动作跳主变三侧断路器/r/n";
				replaceStr += "退出"+CZPService.getService().getDevName(curDev)+"保护装置/r/n";
			}else{
				double vlot = 0;
				List<PowerDevice> zblist = new ArrayList<PowerDevice>();
				List<PowerDevice> dycmlkglist = new ArrayList<PowerDevice>();
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						zblist.add(dev);
						vlot = RuleExeUtil.getTransformerVolByType(dev, "low");
					}
				}
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&vlot == dev.getPowerVoltGrade()){
						dycmlkglist.add(dev);
					}
				}
				
			    List<PowerDevice> yxdevList = new ArrayList<PowerDevice>();
			    List<PowerDevice> hotdevList = new ArrayList<PowerDevice>();
			    List<PowerDevice> colddevList = new ArrayList<PowerDevice>();

			    tempList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			    
			    for(PowerDevice dev :tempList){
			    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
			    		yxdevList.add(dev);
			    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
			    		hotdevList.add(dev);
			    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
			    		colddevList.add(dev);
			    	}
			    }
			    
			    tempList.clear();
			    
			    for(PowerDevice mlkg : mlkgList){
			    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(mlkg).equals("0")){
					    yxdevList.add(mlkg);
			    	}
			    }
			    
			    for(PowerDevice fhczbkg : fhczbkgList){
			    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(fhczbkg).equals("0")){
					    yxdevList.add(fhczbkg);
			    	}
			    }
			    
			    if(hotdevList.size()>0){
			    	replaceStr += "核实"+CZPService.getService().getDevName(hotdevList)+"热备用/r/n";
			    }
			    
			    if(colddevList.size()>0){
			    	replaceStr += "核实"+CZPService.getService().getDevName(colddevList)+"冷备用/r/n";
			    }
			    
			    List<PowerDevice> curzbList = new ArrayList<PowerDevice>();
			    
			    if(fhczbkgList.size()>0){
					curzbList = RuleExeUtil.getDeviceList(fhczbkgList.get(0), SystemConstants.PowerTransformer, "", true, true, true);
			    }
				replaceStr += "退出35kV备自投装置/r/n";
			
				replaceStr += cf.getYcDkStrReplace(yxdevList, stationName);
				
				tempList.addAll(yxdevList);
				tempList.addAll(hotdevList);
				
				replaceStr += cf.getCzMotherLineDevStrReplace(tempList, curDev,null, stationName, "由热备用转冷备用");
				
				if(curzbList.size()>0){
					replaceStr += "退出"+CZPService.getService().getDevName(curzbList)+"第Ⅰ、Ⅱ套35kV侧后备保护动作跳主变三侧断路器";
				}
			}
		}
		
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
