package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Component;
import java.util.List;

import javax.swing.ImageIcon;
import javax.swing.JLabel;
import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeCellRenderer;

import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

class DifferentIconTreeCellRender extends JLabel implements TreeCellRenderer{
	@Override
	public Component getTreeCellRendererComponent(JTree tree, Object value,
			boolean sel, boolean expanded, boolean leaf, int row,
			boolean hasFocus) {
		DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
		int level=node.getLevel();
//		if(level==2){
//			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree2.png")));
//		}else if(level==1){
//			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree1.png")));
//		}else {
//			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree3.png")));
//		}
		if(level==1){
			
		}else if(level==2){
			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree1.png")));
		}else if(level==3){
			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree2.png")));
		}else {
			this.setIcon(new ImageIcon(getClass().getResource("/tellhow/icons/tree3.png")));
		}
		if(sel){
			this.setOpaque(true);
			this.setForeground(Color.white);
			this.setBackground(Color.blue);
		}else{
			this.setOpaque(false);
			this.setForeground(Color.black);
			this.setBackground(Color.white);
		}
		
		if(leaf == true &&DefaultSimpleNode.class.isInstance(node.getUserObject())){
			DefaultSimpleNode dsn = (DefaultSimpleNode) node.getUserObject();
	        List<SVGFile> fileList = SystemConstants.getSVGFileByStationID(dsn.getItemCode());
	        if(fileList.size() == 0) {
	        	 this.setForeground(Color.RED);
	        }
		}
		
		this.setText(value.toString());
		return this;
	}
}
