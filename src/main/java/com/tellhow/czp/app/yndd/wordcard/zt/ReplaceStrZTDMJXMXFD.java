package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTDMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通单母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);

			// 判断厂站名称是否以数字结尾
			if (stationName.matches(".*\\d$")) {
				stationName += "=N=";
			}

			List<PowerDevice> mlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zbList =  new ArrayList<PowerDevice>();
			List<PowerDevice> allmlkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycmxList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC+","+ CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
						gycmlkgList.add(dev);
					}
					mlkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : allmlkgList){
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					mlkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.MotherLine,true, true, true);
				zbList.addAll(tempList);
			}
			
			List<PowerDevice> zbdyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbgyckgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbList){
				zbdyckgList.addAll(RuleExeUtil.getTransformerSwitchLow(dev));
				zbzyckgList.addAll(RuleExeUtil.getTransformerSwitchMiddle(dev));
				zbgyckgList.addAll(RuleExeUtil.getTransformerSwitchHigh(dev));
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			}
			
			for(PowerDevice dev : zbzyckgList){
				zycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			}
			
			for(PowerDevice dev : zbgyckgList){
				gycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			}
			
			for(PowerDevice dev : gycmxList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML ,"", false, true, true, true);
			}
			
			for(PowerDevice dev : zycmxList){
				zycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML ,"", false, true, true, true);
			}
			
			for(PowerDevice dev : dycmxList){
				dycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML ,"", false, true, true, true);
			}
			
			if(mlkgList.size()==0){//单母不分段
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
					
				}else{
					
				}
			}else{//单母分段
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
						replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
					}
					
					for(PowerDevice dev  : xlkgList){
						if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()&&RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
							replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev  : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
					
					for(PowerDevice dev  : zycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
					
					for(PowerDevice dev  : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}else{
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
						replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
					}
					
					for(PowerDevice dev  : mlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
						}
					}
					
					for(PowerDevice dev  : zbkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
						}
					}
					
					for(PowerDevice dev  : xlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
						}
					}
					
					/*
					 * 站用变开关
					 */
					List<PowerDevice> zybkgList =  new ArrayList<PowerDevice>();
					
					String sql = "SELECT ZYB_DEVID FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+curDev.getPowerDeviceID()+"'";
					List<Map<String,String>> zybList =  DBManager.queryForList(sql);

					for(Map<String,String> map : zybList){
						String devid = StringUtils.ObjToString(map.get("ZYB_DEVID"));
						PowerDevice dev = CBSystemConstants.getPowerDevice(devid);
						zybkgList.add(dev);
					}
					
					for(PowerDevice dev : zybkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					
					replaceStr += "投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
