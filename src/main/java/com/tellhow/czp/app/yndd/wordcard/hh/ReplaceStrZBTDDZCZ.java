package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBTDDZCZ implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,PowerDevice stationDev,String desc) {
		String replaceStr = "";

		if ("主变停电刀闸操作".equals(tempStr)) {
			List<PowerDevice> dztagList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycList = RuleExeUtil.getTransformerSwitchSource(curDev);
			List<PowerDevice> dycList = RuleExeUtil.getTransformerSwitchLoad(curDev);
			
			if (gycList.size() > 0) {
				for(PowerDevice dycKG : dycList) {
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dycKG, SystemConstants.SwitchSeparate);
					for(PowerDevice dz : dzList) {
						if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
							dztagList.add(dz);
						}
					}
				}
			}
			
			if (gycList.size() > 0) {
				for(PowerDevice gycKG : gycList) {
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(gycKG, SystemConstants.SwitchSeparate);
					for(PowerDevice dz : dzList) {
						if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
							dztagList.add(dz);
						}
					}
				}
			}
			
			if(dztagList.size()>0){
				replaceStr = CZPService.getService().getDevName(dztagList);
			}
		}
		
		replaceStr = replaceStr.replace("隔离开关、", "、");
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
  }
}