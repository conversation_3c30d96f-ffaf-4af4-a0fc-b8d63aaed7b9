package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXZBTDDD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪主变停电倒电".equals(tempStr)){
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			
			String curStationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(curDev.getPowerStationID()));
			
			replaceStr += "退出"+(int)RuleExeUtil.getTransformerVolByType(curDev, "low")+"kV备自投装置/r/n";
			
			if((int)RuleExeUtil.getTransformerVolByType(curDev, "middle")>0){
				replaceStr += "退出"+(int)RuleExeUtil.getTransformerVolByType(curDev, "middle")+"kV备自投装置/r/n";
			}
			
			List<PowerDevice> middlemxList = RuleExeUtil.getMotherLineAllByVol(curDev, RuleExeUtil.getTransformerVolByType(curDev, "middle"));
			
			if(middlemxList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(middlemxList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer,  true, true, true);

				for(PowerDevice dev : lineList){
					List<Map<String, String>> stationLineList = CommonFunction.getStationLineList(dev);
					
					if(stationLineList.size()==0){
						List<PowerDevice> otherlineList =  RuleExeUtil.getLineOtherSideList(dev);
						
						for(PowerDevice otherline : otherlineList){
							List<PowerDevice> lineSwList = RuleExeUtil.getLinkedSwitch(otherline);

							for(PowerDevice lineSw : lineSwList){
								if(lineSw.getDeviceStatus().equals("0")){
									PowerDevice zb = new PowerDevice();
									
									PowerDevice station2 = CBSystemConstants.getPowerStation(lineSw.getPowerStationID());

									HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station2.getPowerDeviceID());

									for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
										PowerDevice dev2 = it2.next();
										
										if(dev2.getDeviceType().equals(SystemConstants.PowerTransformer)){
											zb= dev2;
											break;
										}
									}
									
									replaceStr += CZPService.getService().getDevName(station2)+"@退出"+(int)RuleExeUtil.getTransformerVolByType(zb, "low")+"kV备自投装置/r/n";
									
									if((int)RuleExeUtil.getTransformerVolByType(zb, "middle")>0){
										replaceStr += CZPService.getService().getDevName(station2)+"@退出"+(int)RuleExeUtil.getTransformerVolByType(zb, "middle")+"kV备自投装置/r/n";
									}
									replaceStr += CZPService.getService().getDevName(station2)+"@退出"+(int)RuleExeUtil.getTransformerVolByType(zb, "high")+"kV备自投装置/r/n";
									
								}
							}
						}
					}
				}
			}
			
			if(middlemxList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(middlemxList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer,  true, true, true);

				for(PowerDevice dev : lineList){
					List<PowerDevice> lineOtherSideList = RuleExeUtil.getLineOtherSideList(dev);
					if(lineOtherSideList.size()>0){
						for(PowerDevice dev2 : lineOtherSideList){
							List<PowerDevice> swList = RuleExeUtil.getLinkedSwitch(dev2);
							
							if(swList.size()>0){
								if(swList.get(0).getDeviceStatus().equals("1")){
									replaceStr += CZPService.getService().getDevName(CBSystemConstants.getPowerStation(swList.get(0).getPowerStationID()))+"@核实"+CZPService.getService().getDevName(swList.get(0))+"已处热备用/r/n";
								}
							}
						}
					}
				}
			}
			
			List<PowerDevice> lowmxList =RuleExeUtil.getMotherLineAllByVol(curDev, RuleExeUtil.getTransformerVolByType(curDev, "low"));

			if(middlemxList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(middlemxList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer,  true, true, true);

				for(PowerDevice dev : lineList){
					List<Map<String, String>> stationLineList = CommonFunction.getStationLineList(dev);

					if(stationLineList.size()>0){
						for(Map<String,String> map : stationLineList){
							String userStationName = map.get("UNIT");
							

							if(userStationName.contains("光伏电站")){
								replaceStr += userStationName+"@核实站内光伏方阵已全部解列/r/n";
								replaceStr += userStationName+"@核实"+CZPService.getService().getDevName(dev)+"可以短时停电倒电/r/n";
							}else{
								replaceStr += userStationName+"@核实"+CZPService.getService().getDevName(dev)+"可以短时停电倒电/r/n";
							}
						}
					}else{
						List<PowerDevice> otherlineList =  RuleExeUtil.getLineOtherSideList(dev);
						
						for(PowerDevice otherline : otherlineList){
							List<PowerDevice> lineSwList = RuleExeUtil.getLinkedSwitch(otherline);

							for(PowerDevice lineSw : lineSwList){
								PowerDevice station = CBSystemConstants.getPowerStation(lineSw.getPowerStationID());
								
								if(lineSw.getDeviceStatus().equals("0")){
									replaceStr += "玉溪配调@核实"+CZPService.getService().getDevName(station)+"可以短时停电倒电/r/n";
								}
							}
						}
					}
				}
			}
			
			List<PowerDevice> zbzdyckgList = new ArrayList<PowerDevice>();
			
			zbzdyckgList.addAll(zbzyckgList);
			zbzdyckgList.addAll(zbdyckgList);
			
			replaceStr += CommonFunction.getZybDrCheckContent(zbzdyckgList);
			
			if(lowmxList.size()>0){
				replaceStr += "玉溪配调@核实"+curStationName+CZPService.getService().getDevName(lowmxList.get(0))+"可以短时停电倒电/r/n";
			}
			
			List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						hignVoltXlkgList.add(dev2);
					}
				}
			}
			
			String temp = curStationName+CZPService.getService().getDevName(curDev);
			
			for(PowerDevice dev : hignVoltXlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
					temp += "由"+CZPService.getService().getDevName(lineList.get(0))+"供电停电倒由";
				}
			}
			
			for(PowerDevice dev : hignVoltXlkgList){
				if(!RuleExeUtil.isDeviceChanged(dev)){
					if(dev.getPowerStationID().equals("SS-61")){//110kV青龙变特殊判断
						if(dev.getPowerDeviceID().equals("880")
								||dev.getPowerDeviceID().equals("892")){
							continue;
						}
					}
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
					temp += CZPService.getService().getDevName(lineList.get(0))+"供电程序操作";
				}
			}
			
			replaceStr += "玉溪地调@执行"+temp+"/r/n";
			
			if((int)RuleExeUtil.getTransformerVolByType(curDev, "middle")>0){
				replaceStr +="按当前运行方式投入"+(int)RuleExeUtil.getTransformerVolByType(curDev, "middle")+"kV备自投装置/r/n";
			}
			replaceStr += "按当前运行方式投入"+(int)RuleExeUtil.getTransformerVolByType(curDev, "low")+"kV备自投装置/r/n";;

			if(middlemxList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(middlemxList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer,  true, true, true);

				for(PowerDevice dev : lineList){
					List<Map<String, String>> stationLineList = CommonFunction.getStationLineList(dev);
					
					if(stationLineList.size()==0){
						List<PowerDevice> otherlineList =  RuleExeUtil.getLineOtherSideList(dev);
						
						for(PowerDevice otherline : otherlineList){
							List<PowerDevice> lineSwList = RuleExeUtil.getLinkedSwitch(otherline);

							for(PowerDevice lineSw : lineSwList){
								if(lineSw.getDeviceStatus().equals("0")){
									PowerDevice zb = new PowerDevice();
									
									PowerDevice station2 = CBSystemConstants.getPowerStation(lineSw.getPowerStationID());

									HashMap<String, PowerDevice> mapStationDevice2 = CBSystemConstants.getStationPowerDevices(station2.getPowerDeviceID());
									
									for (Iterator<PowerDevice> it2 = mapStationDevice2.values().iterator(); it2.hasNext();) {
										PowerDevice dev2 = it2.next();
										
										if(dev2.getDeviceType().equals(SystemConstants.PowerTransformer)){
											zb= dev2;
											break;
										}
									}
									
									replaceStr += CZPService.getService().getDevName(station2)+"@按当前运行方式投入"+(int)RuleExeUtil.getTransformerVolByType(zb, "high")+"kV备自投装置/r/n";
									
									if((int)RuleExeUtil.getTransformerVolByType(zb, "middle")>0){
										replaceStr += CZPService.getService().getDevName(station2)+"@按当前运行方式投入"+(int)RuleExeUtil.getTransformerVolByType(zb, "middle")+"kV备自投装置/r/n";
									}
									
									replaceStr += CZPService.getService().getDevName(station2)+"@按当前运行方式投入"+(int)RuleExeUtil.getTransformerVolByType(zb, "low")+"kV备自投装置/r/n";
								}
							}
						}
					}
				}
			}
			
			if(lowmxList.size()>0){
				replaceStr += "玉溪配调@通知"+curStationName+CZPService.getService().getDevName(lowmxList.get(0))+"短时停电倒电完毕/r/n";
			}
			
			if(middlemxList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(middlemxList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer,  true, true, true);

				for(PowerDevice dev : lineList){
					List<Map<String, String>> stationLineList = CommonFunction.getStationLineList(dev);
					
					if(stationLineList.size()==0){
						List<PowerDevice> lineOtherSideList = RuleExeUtil.getLineOtherSideList(dev);
						for(PowerDevice dev2 : lineOtherSideList){
							List<PowerDevice> lineSwList = RuleExeUtil.getLinkedSwitch(dev2);
							
							for(PowerDevice lineSw : lineSwList){
								PowerDevice station2 = CBSystemConstants.getPowerStation(lineSw.getPowerStationID());
								
								if(lineSw.getDeviceStatus().equals("0")){
									replaceStr += "玉溪配调@通知"+CZPService.getService().getDevName(station2)+"短时停电倒电完毕/r/n";
								}
							}
						}
					}
				}
				
				for(PowerDevice dev : lineList){
					List<Map<String, String>> stationLineList = CommonFunction.getStationLineList(dev);
					
					if(stationLineList.size()>0){
						for(Map<String,String> map : stationLineList){
							String userStationName = map.get("UNIT");
							
							replaceStr += userStationName+"@通知"+CZPService.getService().getDevName(dev)+"短时停电倒电完毕/r/n";
						}
					}
				}
			}
		}
		
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
