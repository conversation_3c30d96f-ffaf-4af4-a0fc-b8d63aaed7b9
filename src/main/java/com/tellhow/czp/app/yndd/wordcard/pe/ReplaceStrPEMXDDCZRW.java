package com.tellhow.czp.app.yndd.wordcard.pe;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEMXDDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱母线倒电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			
			String sourceName = "";
			String loadName = "";

			boolean issamestation = true;
			
			for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
				if(!dev.getPowerStationID().equals(station.getPowerDeviceID())){
					issamestation = false;
					break;
				}
			}
			
			for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					if(issamestation){
						sourceName = CZPService.getService().getDevName(dev); 
					}else{
						PowerDevice devstation = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(devstation); 
						sourceName = stationName+CZPService.getService().getDevName(dev); 
					}
				}else if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					if(issamestation){
						loadName = CZPService.getService().getDevName(dev); 
					}else{
						PowerDevice devstation = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(devstation); 
						loadName = stationName+CZPService.getService().getDevName(dev); 
					}
				}
			}
			
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			replaceStr = stationName+deviceName+"负荷由"+sourceName+"供电倒由"+loadName+"供电";
		}
		
		return replaceStr;
	}

}
