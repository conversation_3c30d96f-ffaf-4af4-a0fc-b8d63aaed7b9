package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMXTDCZGLZB implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("母线停电操作关联主变".equals(tempStr)){
			List<PowerDevice> zbfhckgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			
			if(zbfhckgList.size()>0){
				List<PowerDevice>  zbList = RuleExeUtil.getDeviceList(zbfhckgList.get(0),SystemConstants.PowerTransformer,SystemConstants.PowerTransformer, true, true, true);
				
				if(zbList.size()>0){
					replaceStr = CZPService.getService().getDevName(zbList.get(0));
				}else{
					replaceStr = null;
				}
			}
			
		}
		return replaceStr;
	}

}
