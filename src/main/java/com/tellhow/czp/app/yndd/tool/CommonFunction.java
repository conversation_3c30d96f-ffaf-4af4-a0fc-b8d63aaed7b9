package com.tellhow.czp.app.yndd.tool;

import java.awt.Dimension;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.Rectangle;
import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.view.StringCheckChoose;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardModel;

public class CommonFunction {
	public static String getSuffixDeviceName(String deviceType,String deviceName){
		String replaceStr = "";
		
		if(deviceType.equals(SystemConstants.Switch)){
			deviceName = deviceName.replace("断路器", "");
		}else if(deviceType.equals(SystemConstants.SwitchSeparate)){
			
			
		}else if(deviceType.equals(SystemConstants.SwitchFlowGroundLine)){
			
			
		}
		
		return replaceStr;
	}
	
	public static String getHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(dev.getPowerVoltGrade() > 35){
				if(stationName.contains("电站")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}
			}else{
				if(stationName.contains("电站")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getBztResult(List<String> voltList,String kind){
		String result = "";
		
		List<Map<String, String>> chooseEquips = new ArrayList<Map<String, String>>();
		StringCheckChoose ecc = new StringCheckChoose(SystemConstants.getMainFrame(), true, voltList, "请选择需要"+kind+"的备自投：");
		chooseEquips = ecc.getChooseEquip();
		
		if(chooseEquips.size()>0){
			for(Map<String, String> chooseEquip : chooseEquips){
				String bzt = chooseEquip.get("备自投");
				
				result += kind+bzt+"/r/n";
			}
		}
		
		return result;
	}
	
	public static String getBztBhResult(List<String>  voltList,List<PowerDevice> allmlkgList, String kind){
		String result = "";
		
		List<Map<String, String>> chooseEquips = new ArrayList<Map<String, String>>();
		StringCheckChoose ecc=new StringCheckChoose(SystemConstants.getMainFrame(), true, voltList,true, "请选择需要"+kind+"的备自投：");
		chooseEquips=ecc.getChooseEquip();
		
		if(chooseEquips.size()>0){
			for(Map<String, String> chooseEquip : chooseEquips){
				String bzt = chooseEquip.get("备自投");
				String bh = chooseEquip.get("保护");

				result += kind+bzt+"/r/n";
				
				if(bh.equals("存在")){
					for(PowerDevice dev : allmlkgList){
						if(bzt.contains(String.valueOf((int)dev.getPowerVoltGrade()))){
							result += kind+CZPService.getService().getDevName(dev)+"保护/r/n";
						}
					}
				}
			}
		}
		
		return result;
	}
	
	public static String getLineBztResult(List<PowerDevice> stationList,String volt,String kind){
		String result = "";
		
		List<Map<String, String>> chooseEquips = new ArrayList<Map<String, String>>();
		StringCheckChoose ecc=new StringCheckChoose(SystemConstants.getMainFrame(), true,stationList, true);
		chooseEquips=ecc.getChooseEquip();
		
		if(chooseEquips.size()>0){
			for(Map<String, String> chooseEquip : chooseEquips){
				for(PowerDevice dev : stationList){
					String stationName = CZPService.getService().getDevName(dev);
					String chooseStationName = StringUtils.ObjToString(chooseEquip.get("厂站"));
					
					if(stationName.equals(chooseStationName)){
						result += stationName+"@"+kind+volt+"备自投装置/r/n";
					}
				}
			}
		}
		
		return result;
	}
	
	public static List<PowerDevice> getMlkgGlZb(PowerDevice mlkg){
		List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmxList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

		for(PowerDevice dycmx : dycmxList){
			List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dycmx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
			zbList.addAll(tempList);
		}
		
		return zbList;
	}

	/*
	 * 玉溪
	 */
	public static List<Map<String, String>> getStationLineList(PowerDevice curDev){
		List<Map<String, String>> stationLineList = new ArrayList<Map<String,String>>();
		
		String sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND "
				+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND "
						+ "WHERE ID = '"+curDev.getPowerDeviceID()+"')";
		
		stationLineList = DBManager.queryForList(sql);
		
		if(stationLineList.size() == 0){
			sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND "
					+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_NAME = '"+CZPService.getService().getDevName(curDev)+"'";
			
			stationLineList = DBManager.queryForList(sql);
		}
		
		return stationLineList; 
	}
	
	public static String get3KnifeContent(List<PowerDevice> mlkgList,String stationName,String operation){
		String replaceStr = "";
		
		for(PowerDevice dev : mlkgList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			List<PowerDevice> alldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.MotherLine, true, true, false);
			
			for(PowerDevice dz : alldzList){
				if(!dzList.contains(dz)&&!dz.getPowerDeviceName().endsWith("1")){
					if(operation.equals("拉开")){
						List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
						dzTempList.add(dz);
						replaceStr = getKnifeOffContent(dzTempList,stationName);
					}else{
						List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
						dzTempList.add(dz);
						replaceStr = getKnifeOnContent(dzTempList,stationName);
					}
					break;
				}
			}
		}
		
		return replaceStr; 
	}
	
	
	public static String getZybCheckContentByLine(PowerDevice line) {
		String replaceStr = ""; 
		PowerDevice station = CBSystemConstants.getPowerStation(line.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station);
		
		boolean iszyb = false;
		
		String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+line.getPowerDeviceID()+"'";
		List<Map<String,String>> zybNameList = DBManager.queryForList(sql);
		
		if(zybNameList.size() > 0){
			iszyb = true;
		}
		
		if(iszyb){
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();
			List<PowerDevice> curMxList = new ArrayList<PowerDevice>();
			List<PowerDevice> tempMxList = RuleExeUtil.getDeviceList(line,SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			curMxList.addAll(tempMxList);
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(line.getPowerStationID());

			for(Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&!curMxList.contains(dev)&&
						!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					if(dev.getPowerVoltGrade() == 35 || dev.getPowerVoltGrade() == 10){
						mxList.add(dev);
					}
				}
			}
			
			for(PowerDevice othermx : mxList){
				List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(othermx, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchQT, SystemConstants.PowerTransformer, false, true, true, true);
				List<PowerDevice> zybdzList = RuleExeUtil.getDeviceList(othermx, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT, SystemConstants.PowerTransformer, false, true, true, true);
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(othermx, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
				List<PowerDevice> zybdevList = new ArrayList<PowerDevice>();
						
				zybdevList.addAll(zybdzList);
				zybdevList.addAll(zybkgList);
				

				if(othermx != null){
					sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+othermx.getPowerDeviceID()+"'";
					List<Map<String,String>> zybList = DBManager.queryForList(sql);
					
					for(Map<String,String> zybMap : zybList){
						replaceStr = stationName+"@核实站用电已倒由"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"供电正常/r/n";
						break;
					}
				}
				
				if(replaceStr.equals("")){
					for(PowerDevice dev : lineList){
						sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+dev.getPowerDeviceID()+"'";
						List<Map<String,String>> zybList = DBManager.queryForList(sql);
						
						for(Map<String,String> zybMap : zybList){
							replaceStr = stationName+"@核实站用电已倒由"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"供电正常/r/n";
							break;
						}
					}
				}
				
				if(replaceStr.equals("")){
					for(PowerDevice dev : zybdevList){
						if(dev.getPowerDeviceName().contains("站用变")){
							String temp = CZPService.getService().getDevName(dev);
							
							if(temp.contains("站用变")){
								String zybName = temp.substring(0, temp.indexOf("站用变")+3);
								
								if(!zybName.contains("kV")){
									zybName = (int)dev.getPowerVoltGrade()+"kV"+zybName;
								}
								
								replaceStr = stationName+"@核实站用电已倒由"+zybName+"供电正常/r/n";
							}else{
								replaceStr = stationName+"@核实站用电已倒由XX站用变供电正常/r/n";
							}
							break;
						}else if(dev.getPowerDeviceName().contains("所用变")){
							String temp = CZPService.getService().getDevName(dev);
							
							if(temp.contains("所用变")){
								String zybName = temp.substring(0, temp.indexOf("所用变")+3);
								
								if(!zybName.contains("kV")){
									zybName = (int)dev.getPowerVoltGrade()+"kV"+zybName;
								}
								
								replaceStr = stationName+"@核实站用电已倒由"+zybName+"供电正常/r/n";
							}else{
								replaceStr = stationName+"@核实站用电已倒由XX所用变供电正常/r/n";
							}
							break;
						}else if(dev.getPowerDeviceName().contains("站用接地变")){
							String temp = CZPService.getService().getDevName(dev);
							
							if(temp.contains("站用接地变")){
								String zybName = temp.substring(0, temp.indexOf("站用接地变")+5);
								
								if(!zybName.contains("kV")){
									zybName = (int)dev.getPowerVoltGrade()+"kV"+zybName;
								}
								
								replaceStr = stationName+"@核实站用电已倒由"+zybName+"供电正常/r/n";
							}else{
								replaceStr = stationName+"@核实站用电已倒由XX站用接地变供电正常/r/n";
							}
							break;
						}
					}
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getZybDrCheckContent(List<PowerDevice> zbzdyckgList){
		String replaceStr = "";
		String stationid = "";
		
		List<PowerDevice> drList = new ArrayList<PowerDevice>();
		List<PowerDevice> curMxList = new ArrayList<PowerDevice>();
		List<PowerDevice> otherMxList = new ArrayList<PowerDevice>();

		for(PowerDevice dev : zbzdyckgList){
			stationid = dev.getPowerStationID();
			List<PowerDevice> tempMxList = RuleExeUtil.getDeviceList(dev,SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			curMxList.addAll(tempMxList);
		}
		
		boolean iszyb = false;
		
		for(PowerDevice dev : curMxList){
			List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchQT, SystemConstants.PowerTransformer, false, true, true, true);
			List<PowerDevice> zybdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT, SystemConstants.PowerTransformer, false, true, true, true);
			List<PowerDevice> tempDrList = RuleExeUtil.getDeviceList(dev,SystemConstants.ElecCapacity, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
			
			List<PowerDevice> zybdevList = new ArrayList<PowerDevice>();
			
			drList.addAll(tempDrList);
			zybdevList.addAll(zybdzList);
			zybdevList.addAll(zybkgList);
			
			for(PowerDevice zybdev : zybdevList){
				if(zybdev.getPowerDeviceName().contains("站用变")
						||zybdev.getPowerDeviceName().contains("所用变")
						||zybdev.getPowerDeviceName().contains("站用接地变")){
					iszyb = true;
				}
			}
			
			if(!iszyb){
				String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+dev.getPowerDeviceID()+"'";
				List<Map<String,String>> zybList = DBManager.queryForList(sql);
				
				if(zybList.size() > 0){
					iszyb = true;
				}
			}
			
			if(!iszyb){
				for(PowerDevice line : lineList){
					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+line.getPowerDeviceID()+"'";
					List<Map<String,String>> zybList = DBManager.queryForList(sql);
					
					if(zybList.size() > 0){
						iszyb = true;
					}
				}
			}
		}
		
		if(iszyb){
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationid);

			for(Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					if(!curMxList.contains(dev)&&110>dev.getPowerVoltGrade()){
						otherMxList.add(dev);
					}
				}
			}
			
			for(PowerDevice othermx : otherMxList){
				List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(othermx, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchQT, SystemConstants.PowerTransformer, false, true, true, true);
				List<PowerDevice> zybdzList = RuleExeUtil.getDeviceList(othermx, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT, SystemConstants.PowerTransformer, false, true, true, true);
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(othermx, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
				List<PowerDevice> zybdevList = new ArrayList<PowerDevice>();
						
				zybdevList.addAll(zybdzList);
				zybdevList.addAll(zybkgList);
				

				if(othermx != null){
					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+othermx.getPowerDeviceID()+"'";
					List<Map<String,String>> zybList = DBManager.queryForList(sql);
					
					for(Map<String,String> zybMap : zybList){
						replaceStr = "核实站用电已倒由"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"供电正常/r/n";
						break;
					}
				}
				
				if(replaceStr.equals("")){
					for(PowerDevice line : lineList){
						String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+line.getPowerDeviceID()+"'";
						List<Map<String,String>> zybList = DBManager.queryForList(sql);
						
						for(Map<String,String> zybMap : zybList){
							replaceStr = "核实站用电已倒由"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"供电正常/r/n";
							break;
						}
					}
				}
				
				if(replaceStr.equals("")){
					for(PowerDevice dev : zybdevList){
						if(dev.getPowerDeviceName().contains("站用变")){
							String temp = CZPService.getService().getDevName(dev);
							
							if(temp.contains("站用变")){
								String zybName = temp.substring(0, temp.indexOf("站用变")+3);
								
								if(!zybName.contains("kV")){
									zybName = (int)dev.getPowerVoltGrade()+"kV"+zybName;
								}
								
								replaceStr = "核实站用电已倒由"+zybName+"供电正常/r/n";
							}else{
								replaceStr = "核实站用电已倒由XX站用变供电正常/r/n";
							}
							break;
						}else if(dev.getPowerDeviceName().contains("所用变")){
							String temp = CZPService.getService().getDevName(dev);
							
							if(temp.contains("所用变")){
								String zybName = temp.substring(0, temp.indexOf("所用变")+3);
								
								if(!zybName.contains("kV")){
									zybName = (int)dev.getPowerVoltGrade()+"kV"+zybName;
								}
								
								replaceStr = "核实站用电已倒由"+zybName+"供电正常/r/n";
							}else{
								replaceStr = "核实站用电已倒由XX所用变供电正常/r/n";
							}
							break;
						}else if(dev.getPowerDeviceName().contains("站用接地变")){
							String temp = CZPService.getService().getDevName(dev);
							
							if(temp.contains("站用接地变")){
								String zybName = temp.substring(0, temp.indexOf("站用接地变")+5);
								
								if(!zybName.contains("kV")){
									zybName = (int)dev.getPowerVoltGrade()+"kV"+zybName;
								}
								
								replaceStr = "核实站用电已倒由"+zybName+"供电正常/r/n";
							}else{
								replaceStr = "核实站用电已倒由XX站用接地变供电正常/r/n";
							}
							break;
						}
					}
				}
			}
		}
		
		for(PowerDevice dev  : drList){
			if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
				String status = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				status = RuleExeUtil.getStatusNew(dev.getDeviceType(), status);
				replaceStr += "核实"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
			}else{
				replaceStr += "核实"+CZPService.getService().getDevName(dev)+"已处热备用/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static List<PowerDevice> getTransformerKnife(PowerDevice zb,PowerDevice zbkg){
		List<PowerDevice> dztagList = new ArrayList<PowerDevice>();

		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
		List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(zb, zbkg, SystemConstants.PowerTransformer, "", true, true);
		
		for(PowerDevice path : pathList){
			if(path.getDeviceType().equals(SystemConstants.SwitchSeparate)){
				if(!dzList.contains(path)){
					dztagList.add(path);
				}
			}
		}
		
		return dztagList;
	}
	
	public static String getKnifeOnContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "玉溪地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOffCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
				if(dev.getPowerDeviceName().endsWith("1")){
					it.remove();
				}
			}
		}
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")&&ifKnifeCheck(dz)){
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(swList)+"已处冷备用/r/n";
				}else{
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
				if(dev.getPowerDeviceName().endsWith("1")){
					it.remove();
				}
			}
		}
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")&&ifKnifeCheck(dz)){
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(swList)+"已处热备用/r/n";
				}else{
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getZxdJddzOffCheckContent(List<PowerDevice> zxdjddzList,String stationName,PowerDevice station){
		String replaceStr = "";

		boolean isZxdOff = false;
		
		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOff = true;
			}
		}
		
		if(isZxdOff){
			tagzxdjddzList = RuleExeUtil.sortByVoltHigh(tagzxdjddzList);
			
			if(station.getPowerVoltGrade() == 220){
				replaceStr += "玉溪地调@执行拉开"+stationName+CZPService.getService().getDevName(tagzxdjddzList)+"程序操作/r/n";
			}else{
				replaceStr += "玉溪地调@遥控拉开"+stationName+CZPService.getService().getDevName(tagzxdjddzList)+"/r/n";
			}
			
			for(PowerDevice dev : tagzxdjddzList) {
				if(ifKnifeCheck(dev)){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"在拉开位置/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getZxdJddzOnCheckContent(List<PowerDevice> zxdjddzList,String stationName,PowerDevice station){
		String replaceStr = "";
		
		boolean isZxdOn = false;
		
		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOn = true;
			}
		}
		
		if(isZxdOn){
			tagzxdjddzList = RuleExeUtil.sortByVoltLow(tagzxdjddzList);

			if(station.getPowerVoltGrade() == 220){
				replaceStr += "玉溪地调@执行合上"+stationName+CZPService.getService().getDevName(tagzxdjddzList)+"程序操作/r/n";
			}else{
				replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(tagzxdjddzList)+"/r/n";
			}
			
			for(PowerDevice dev : tagzxdjddzList) {
				if(ifKnifeCheck(dev)){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"在合闸位置/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static boolean ifKnifeCheck(PowerDevice dev){
		/*
		 * 尖山变、寒武变特殊判断
		 */
		if(dev.getPowerStationID().equals("SS-418") || 
				dev.getPowerStationID().equals("SS-361")){
			return false;
		}
		
		
		String sql = "SELECT ISABLE FROM "+CBSystemConstants.equipUser+"T_A_KNIFECHECK WHERE KNIFEID = '"+dev.getPowerDeviceID()+"'";
		List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
		
		for(Map<String,String> ifcontrolMap : ifcontrolList){
			String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISABLE"));
			
			if(ifcontrol.equals("是")){
				return false;
			}else{
				return true;
			}
		}
		
		return true;
	}
	
	public static boolean ifSwitchControl(PowerDevice dev){//开关可控
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54' AND DESCRIPTION NOT LIKE '%相%'";
			
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
				return Boolean.parseBoolean(ifcontrol);
			}
		}
		
		return false;
	}
	
	public static boolean ifSwitchSeparateControl(PowerDevice dev){//刀闸是否可控
		boolean result = true;
		
		if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
				
				if(!Boolean.parseBoolean(ifcontrol)){
					result = false;
				}
			}
			
			if(ifcontrolList.size() == 0){
				return false;
			}
			
			return result;
		}else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
			String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
				
				if(!Boolean.parseBoolean(ifcontrol)){
					result = false;
				}
			}
			
			if(ifcontrolList.size() == 0){
				return false;
			}
			
			return result;
		}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(dev.getPowerDeviceID().equals("2661")||dev.getPowerDeviceID().equals("2657")){
				return false;
			}
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dz : dzList){
				String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dz.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(!Boolean.parseBoolean(ifcontrol)){
						result = false;
					}
				}
				
				if(ifcontrolList.size() == 0){
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						continue;
					}else{
						return false;
					}
				}
			}
			
			if(dzList.size() == 0){
				return false;
			}
			
			return result;
		}
		
		return false;
	}
	
	public static void openTicketWindow(CardModel cm,RuleBaseMode Srcrbm){
    	TempTicket ttk = null;
    	if(CBSystemConstants.isMultiTicket) {
    		ttk=TempTicket.getNewInstance();
    	}
    	else {
    		ttk=TempTicket.getInstance();
    	}
    	ttk.init(cm,Srcrbm);

    	if(SystemConstants.isInitNewWin.equals("0")){
	    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
	    	Dimension scrsiz = Toolkit.getDefaultToolkit().getScreenSize();
//	    	System.out.println("屏幕大小"+scrsiz);
    		double bl = 0;

	    	if(CBSystemConstants.isFixedSize){
	    		bl = 0.75;
	    	}else{
		    	if(scrsiz.width>1280){
		    		bl = 0.40;
		    	}else if(scrsiz.width<=1280&&scrsiz.width>1024){
		    		bl = 0.30;
		    	}else if(scrsiz.width<=1024&&scrsiz.width>800){
		    		bl = 0.20;
		    	}else{
		    		bl =0.15;
		    	}
		    	double minbl = 1-Double.valueOf(ttk.getPanelLength())/scrsiz.width;
		    	if(minbl < bl && minbl > 0 && minbl < 1)
		    		bl = minbl;
		    	
		    	if(CBSystemConstants.isTicketFullScreen){
		    		bl=0;
		    	}
		    	if(CBSystemConstants.tempticketSpit>0){
		    		bl=CBSystemConstants.tempticketSpit;
		    	}
	    	}
	    	
	    	splitPane.setDividerLocation(bl);
	    	
	    	splitPane.setEnabled(true);  
	    	if(CBSystemConstants.isMultiTicket) {
	    		if(!(splitPane.getRightComponent() instanceof JTabbedPane)) {
			    	JTabbedPane jtp = new JTabbedPane();
			    	jtp.addTab("操作票1", ttk);
			    	splitPane.setRightComponent(jtp);
	    		}
	    		else {
	    			JTabbedPane jtp = (JTabbedPane)splitPane.getRightComponent();
	    			jtp.addTab("操作票"+String.valueOf(((JTabbedPane)splitPane.getRightComponent()).getTabCount()+1), ttk);
	    			if(!jtp.isVisible())
	    				jtp.setVisible(true);
	    		}
		    	
	    	}
	    	else if(CBSystemConstants.dcchzMap.size() > 0 && !CBSystemConstants.isSame) {
		    	JTabbedPane jtp = new JTabbedPane();
		    	//jtp.addTab("操作票1", ttk);
		    	
		    	for(Iterator<RuleBaseMode> it = CBSystemConstants.dcchzMap.keySet().iterator();it.hasNext();) {
//		    		PowerDevice pdnew = it.next();
//		    		RuleBaseMode rbnew = new RuleBaseMode();
//		    		rbnew.setPd(pdnew);
//		    		rbnew.setBeginStatus(Srcrbm.getBeginStatus());
//		    		rbnew.setEndState(Srcrbm.getEndState());
//		    		rbnew.setStateCode(Srcrbm.getStateCode());
		    		
		    		RuleBaseMode rbnew = it.next();
		    		PowerDevice pdnew = rbnew.getPd();
		    		CardModel cmnew=WordExecute.getInstance().execute(rbnew);
		    		TempTicket ttknew=TempTicket.getNewInstance();
		    		ttknew.init(cmnew,rbnew);
		    		jtp.addTab(CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pdnew.getPowerStationID())), ttknew);
		    	}
		    	splitPane.setRightComponent(jtp);
		    	CBSystemConstants.dcchzMap.clear();
	    	}
	    	else {
	    		splitPane.setRightComponent(ttk);
	    	}
    	}else{
			//修改版窗体可移动
    		//双屏
    		if(SystemConstants.isInitDoubleScreen.equals("1")){
    			GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
    	        GraphicsDevice[] gs = ge.getScreenDevices();
    	        int x=0;
    	        int y=0;
    	        int width=0;
    	        int height=0;
    	        if(gs.length>1){
    	        	Rectangle fram=SystemConstants.getGuiBuilder().getJFrame().getBounds();
    	        	int x0=(int)gs[0].getDefaultConfiguration().getBounds().getX();
    	        	int y0=(int)gs[0].getDefaultConfiguration().getBounds().getY();
    	        	int width0=(int)gs[0].getDefaultConfiguration().getBounds().getWidth();
    	        	int height0=(int)gs[0].getDefaultConfiguration().getBounds().getHeight()-100;
    	        	int x1=(int)gs[1].getDefaultConfiguration().getBounds().getX();
    	        	int y1=(int)gs[1].getDefaultConfiguration().getBounds().getY();
    	        	int width1=(int)gs[1].getDefaultConfiguration().getBounds().getWidth();
    	        	int height1=(int)gs[1].getDefaultConfiguration().getBounds().getHeight()-100;
    	        	int xg=(int)fram.getX();
    	        	if(xg-x0>100){
    	        		x=x0;
    	        		y=y0;
    	        		width=width0;
    	        		height=height0;
    	        	}else{
    	        		x=x1;
    	        		y=y1;
    	        		width=width1;
    	        		height=height1;
    	        	}
    	        	
    				ttk.setBounds(x, y, width, height);
    	        }
    		}
    	}
	}
	
	/*
	 * 昭通
	 */
	
	public static String getControlContentZT(String stationName,String operate,String deviceName,String endwords,String ddName) {
		String result = "";
		
		if(stationName.endsWith("电站")){
			result += stationName+"@"+operate+deviceName+endwords+"/r/n";
		}else{
			result += ddName+"@遥控"+operate+stationName+deviceName+endwords+"/r/n";
		}
		
		return result;
	}
	
	public static String getTransformerHHTdContent(PowerDevice zb,String ddname,String stationName){
		String replaceStr = "";
		
		List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		
		boolean ishh = false;
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(zb.getPowerStationID());

		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
				zbList.add(dev);
			}
			
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
					ishh = true;
				}
			}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
				if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
					ishh = true;
				}
			}
		}
		
		if(ishh){
			if(zbList.size() == 2){
				RuleExeUtil.sortListByDevName(zbList);
				replaceStr += ddname+"@核实"+stationName+CZPService.getService().getDevName(zbList)+"具备合环条件/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getTransformerHHFdContent(PowerDevice zb,String ddname,String stationName){
		String replaceStr = "";
		
		List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		
		boolean ishh = false;
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(zb.getPowerStationID());

		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
				zbList.add(dev);
			}
			
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
				if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
					ishh = true;
				}
			}
		}
		
		if(ishh){
			if(zbList.size() == 2){
				RuleExeUtil.sortListByDevName(zbList);
				replaceStr += ddname+"@核实"+stationName+CZPService.getService().getDevName(zbList)+"具备合环条件/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static boolean isSwitchDiffMotherLine(PowerDevice curDev) {
		List<PowerDevice> plkgMXList = new ArrayList<PowerDevice>();
		List<PowerDevice> curMXList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, null, true, true, true);
		
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(curDev,null ,SystemConstants.Switch, SystemConstants.Switch, CBSystemConstants.RunTypeSwitchPL,null, false, true, false, true,true);
		if(mlList != null && mlList.size()>0) {
			for(PowerDevice pdDev : mlList) {
				List<PowerDevice> tempList = RuleExeUtil.getDeviceList(pdDev, SystemConstants.MotherLine, null, true, true, true);
				plkgMXList.addAll(tempList);
			}
		}
		
		if(curMXList == null) {
			return true;
		}
		
		for(int i=0;i<curMXList.size();i++) {
			if(CBSystemConstants.RunTypeSideMother.equals(curMXList.get(i).getDeviceRunType())) {
				curMXList.remove(i);
			}
		}
		
		for(int i=0;i<plkgMXList.size();i++) {
			if(CBSystemConstants.RunTypeSideMother.equals(plkgMXList.get(i).getDeviceRunType())) {
				plkgMXList.remove(i);
			}
		}
		
		if(plkgMXList.size()>0 && curMXList.size()>0) {
			for(int i=0;i<plkgMXList.size();i++) {
				for(int j=0;j<curMXList.size();j++) {
					if(plkgMXList.get(i).getPowerDeviceID().equals(curMXList.get(j).getPowerDeviceID())) {
						return false;
					}
				}
			}
		}
		return true;
	}
	
	
	/*
	 * 接线方式判断
	 */
	
	//单母不分段接线
	public static boolean isOneMotherLineNotSection(PowerDevice curDev) {
		List<PowerDevice> mlkgList  = new ArrayList<PowerDevice>();

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
		
		for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = it2.next();
			
			if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					mlkgList.add(dev);
				}
			}
			
			if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
				List<PowerDevice> zbList  = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.PowerTransformer);
				
				if(zbList.size()>0){
					return false;
				}
			}
		}
		
		
		if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
			PowerDevice equip = RuleExeUtil.getDeviceSwitch(curDev);
			if(equip!=null&&equip.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)&&mlkgList.size()==0){
				return true;
			}
		}else if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)&&mlkgList.size()==0){
			return true;
		}else if(curDev.getDeviceType().equals(SystemConstants.PowerTransformer)&&mlkgList.size()==0&&!RuleExeUtil.isTransformerXBZ(curDev)){
			return true;
		}
		
		return false;
	}
	
	public static boolean isLineTransformerUnit(PowerDevice curDev) {
		if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			if(curDev.getPowerVoltGrade() == 500 || station.getPowerVoltGrade() == 500){
				return false;
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(RuleExeUtil.isTransformerXBZ(dev)){
						return true;
					}
				}
			}
		}
		
		
		return false;
	}

	/**
	 * 传入一个母联开关集合，判断相关母线是否是双母双分段
	 * @param mlkgList 母联开关集合
	 * @return  相关母线是否是双母双分段
	 */
	public static boolean isDoubleBusDoubleBranch(List<PowerDevice> mlkgList) {
		boolean isDoubleBusDoubleBranch = false;

		for (PowerDevice mlkg : mlkgList) {
			// 分段开关搜到四条母线则认为是双母双分段
			if (!RuleExeUtil.isSwitchDoubleML(mlkg)) {
                /*List<PowerDevice> mxList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine,
                        SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother,
                        false, true, false, true);
                if (mxList.size() == 4) isDoubleBusDoubleBranch = true;*/
				return RuleExeUtil.isFDSwitchSMSF(mlkg);
			}
		}

		return isDoubleBusDoubleBranch;
	}

	/**
	 * @param bus 母线
	 * @return 当前母线是否是三母零分段
	 */
	public static List<PowerDevice> isTripleBusZeroBranch(PowerDevice bus) {
		// 以当前所选母线为视角，判断是否有与其不通过分段开关直接相连的母线
		// 逻辑：母线绕过开关能搜到母线
		if (bus.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(bus, SystemConstants.MotherLine,
					SystemConstants.Switch, true, true, true);
			if (!mxList.isEmpty()) return mxList;
		}

		return null;
	}

	/**
	 * @param bus 当前母线
	 * @return 获取三母零分段母线类型中间的刀闸
	 */
	public static List<PowerDevice> getTripleBusZeroBranchKnife(PowerDevice bus) {
		List<PowerDevice> dzList = new ArrayList<PowerDevice>();
		List<PowerDevice> mxList = isTripleBusZeroBranch(bus);

		if (mxList != null && !mxList.isEmpty()) {
			List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(mxList.get(0), bus,
					SystemConstants.MotherLine, SystemConstants.Switch, true, true);
			// 如果路径上有不在刀闸集合中的刀闸，则加进去
			for (PowerDevice device : pathList) {
				if (!dzList.contains(device) && device.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
					dzList.add(device);
				}
			}
		}

		return dzList;
	}

	/**
	 * @param kgList 开关
	 * @param yxkgList 运行开关
	 * @param rbykgList 热备用开关
	 */
	public static void categorizeSwitch(List<PowerDevice> kgList, List<PowerDevice> yxkgList,
								 List<PowerDevice> rbykgList, Map<PowerDevice, String> devNameMap) {
		for (PowerDevice dev : kgList) {
			getDeviceNameAndStoreInMap(dev, devNameMap);
			List<PowerDevice> dzList = com.tellhow.czp.app.yndd.rule.RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

			for (PowerDevice dz : dzList) {
				if (com.tellhow.czp.app.yndd.rule.RuleExeUtil.isDeviceChanged(dz)) {
					if (dev.getDeviceStatus().equals("0")) {
						yxkgList.add(dev);
						break;
					} else if (dev.getDeviceStatus().equals("1")) {
						rbykgList.add(dev);
						break;
					}
				}
			}
		}
	}

	/**
	 * 获取设备名称并存入map
	 * @param dev 设备
	 * @param devNameMap 设备名称map
	 */
	public static void getDeviceNameAndStoreInMap(PowerDevice dev, Map<PowerDevice, String> devNameMap) {
		String devName = CZPService.getService().getDevName(dev);
		if (!devNameMap.containsKey(dev)) {
			devNameMap.put(dev, devName);
		}
	}
}
