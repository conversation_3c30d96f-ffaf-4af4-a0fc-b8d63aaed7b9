package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXZBTDDDCZRW implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪主变停电倒电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						hignVoltXlkgList.add(dev);
					}
				}
			}
			
			replaceStr = stationName+deviceName;
			
			for(PowerDevice dev : hignVoltXlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
					replaceStr += "由"+CZPService.getService().getDevName(lineList.get(0))+"供电停电倒由";
				}
			}
			
			for(PowerDevice dev : hignVoltXlkgList){
				if(!RuleExeUtil.isDeviceChanged(dev)){
					if(dev.getPowerStationID().equals("SS-61")){//110kV青龙变特殊判断
						if(dev.getPowerDeviceID().equals("880")
								||dev.getPowerDeviceID().equals("892")){
							continue;
						}
					}
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
					replaceStr += CZPService.getService().getDevName(lineList.get(0))+"供电";
				}
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
