package com.tellhow.czp.app.yndd.wordcard.km;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooHSKG implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("合上开关".equals(tempStr)){
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
                DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
                if (dtd.getTransDevice().getDeviceType().equals(SystemConstants.Switch)) {
                	if(dtd.getEndstate().equals("0")){
                		return true;
                	}
                }
            }
		}
        return false;
	}
}
