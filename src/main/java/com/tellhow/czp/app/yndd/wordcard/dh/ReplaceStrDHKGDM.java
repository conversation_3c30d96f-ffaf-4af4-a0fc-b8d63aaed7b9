package com.tellhow.czp.app.yndd.wordcard.dh;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDHKGDM  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("德宏开关倒母".equals(tempStr)){
			List<PowerDevice> zbdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			
			String thismxName = "";
			String othermxName = "";
			
			for(PowerDevice zbdz : zbdzList){
				if(RuleExeUtil.getDeviceEndStatus(zbdz).equals("0")){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(zbdz, SystemConstants.MotherLine);
					othermxName = CZPService.getService().getDevName(mxList);
				}else if(RuleExeUtil.getDeviceBeginStatus(zbdz).equals("0")){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(zbdz, SystemConstants.MotherLine);
					thismxName = CZPService.getService().getDevName(mxList);
				}
			}
			
			if(!othermxName.equals("")&&!thismxName.equals("")){
				replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由"+thismxName+RuleExeUtil.getStatus(curDev.getDeviceStatus())+
						"倒至"+othermxName+RuleExeUtil.getStatus(curDev.getDeviceStatus())+"/r/n";
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
