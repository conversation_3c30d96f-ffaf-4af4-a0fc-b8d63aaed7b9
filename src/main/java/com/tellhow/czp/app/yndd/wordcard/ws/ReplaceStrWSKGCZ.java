package com.tellhow.czp.app.yndd.wordcard.ws;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionWS;

import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrWSKGCZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("文山开关操作".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			String begin = CBSystemConstants.getCurRBM().getBeginStatus();
			String end = CBSystemConstants.getCurRBM().getEndState();

			if(curDev.getPowerVoltGrade() == 500){
				boolean ifSwitchControl = false;
				boolean ifSwitchSeparateControl = false;

				if(CommonFunctionWS.ifSwitchControl(curDev)){
					ifSwitchControl = true;
				}
				
				if(CommonFunctionWS.ifSwitchSeparateControl(curDev)){
					ifSwitchSeparateControl = true;
				}
				
				if(begin.equals("0")){
					if(end.equals("1")){
						replaceStr += "文山地调@遥控断开"+stationName+deviceName+"/r/n";
					}else if(end.equals("2")){
						if(ifSwitchControl && ifSwitchSeparateControl){
							replaceStr += "文山地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
						}else{
							replaceStr += "文山地调@遥控断开"+stationName+deviceName+"/r/n";
							
							if(ifSwitchSeparateControl){
								replaceStr += "文山地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
							}else{
								replaceStr += stationName+"@将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
							}
						}
					}
				}else if(begin.equals("1")){
					String deviceNum = CZPService.getService().getDevNum(curDev);
					boolean isCenterSwitch = false;
					
					if(deviceNum.endsWith("2")){
						isCenterSwitch = true;
					}
					
					if(end.equals("0")){
						if(ifSwitchControl){
							replaceStr += "文山地调@遥控断开"+stationName+deviceName+"/r/n";
						}else{
							if(isCenterSwitch){
								replaceStr += "文山地调@确认"+stationName+deviceName+"检同期功能压板已投入/r/n";
								replaceStr += CommonFunctionWS.getHhContent(curDev, "文山地调", stationName);
							}else{
								replaceStr += "文山地调@遥控退出"+stationName+deviceName+"检同期功能压板/r/n";
								replaceStr += "文山地调@遥控投入"+stationName+deviceName+"检无压功能压板/r/n";
								
								if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									replaceStr += "文山地调@遥控合上"+stationName+deviceName+"对线路充电/r/n";
								}else{
									replaceStr += "文山地调@遥控合上"+stationName+deviceName+"对主变充电/r/n";
								}
							}
						}
					}else if(end.equals("2")){
						if(ifSwitchSeparateControl){
							replaceStr += "文山地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
						}
					}
				}else if(begin.equals("2")){
					if(end.equals("1")){
						if(ifSwitchSeparateControl){
							replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
						}
					}else if(end.equals("0")){
						if(ifSwitchControl && ifSwitchSeparateControl){
							replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
						}else{
							if(ifSwitchSeparateControl){
								replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
							}else{
								replaceStr += stationName+"@将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
							}
							
							replaceStr += "文山地调@遥控合上"+stationName+deviceName+"/r/n";
						}
					}
				}
			}else{
				boolean ifSwitchControl = false;
				boolean ifSwitchSeparateControl = false;

				if(CommonFunctionWS.ifSwitchControl(curDev)){
					ifSwitchControl = true;
				}
				
				if(CommonFunctionWS.ifSwitchSeparateControl(curDev)){
					ifSwitchSeparateControl = true;
				}
				
				if(begin.equals("0")){
					if(end.equals("1")){
						replaceStr += CommonFunctionWS.getSwitchOffContent(curDev, stationName, station);
					}else if(end.equals("2")){
						if(ifSwitchControl && ifSwitchSeparateControl){
							replaceStr += "文山地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
						}else{
							replaceStr += CommonFunctionWS.getSwitchOffContent(curDev, stationName, station);
							
							if(ifSwitchSeparateControl){
								replaceStr += "文山地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
							}else{
								replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
							}
						}
					}
				}else if(begin.equals("1")){
					if(end.equals("0")){
						replaceStr += CommonFunctionWS.getSwitchOnContent(curDev, stationName, station);
					}else if(end.equals("2")){
						if(ifSwitchSeparateControl){
							replaceStr += "文山地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
						}else{
							replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
						}
					}
				}else if(begin.equals("2")){
					if(end.equals("1")){
						if(ifSwitchSeparateControl){
							replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
						}
					}else if(end.equals("0")){
						if(ifSwitchControl && ifSwitchSeparateControl){
							replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
						}else{
							if(ifSwitchSeparateControl){
								replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
							}else{
								replaceStr += stationName+"@将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
							}
							
							replaceStr += CommonFunctionWS.getSwitchOnContent(curDev, stationName, station);
						}
					}
				}
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
