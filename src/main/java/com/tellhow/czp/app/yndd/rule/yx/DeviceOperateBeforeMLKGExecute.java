package com.tellhow.czp.app.yndd.rule.yx;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.EMSService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.LineExecuteLoad;
import czprule.rule.view.EquipChoose;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2014年3月27日 下午8:23:38 
 */
public class DeviceOperateBeforeMLKGExecute implements RulebaseInf {
	
	public static List<PowerDevice> tagDevList = new ArrayList<PowerDevice>();
	
	@Override
	public boolean execute(RuleBaseMode rbm) {
		PowerDevice pd=rbm.getPd();
		
		if(pd == null){
			return false;
		}
		tagDevList.clear();
		int gycvolt = 0;
		int zycvolt = 0;
		int dycvolt = 0;

		List<PowerDevice>  dycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice>  zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice>  gycmlkgList = new ArrayList<PowerDevice>();

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		
		for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = it2.next();
			if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
				gycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "high");
				zycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "middle");
				dycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "low");
				break;
			}
		}
		
		for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = it2.next();
			if(dev.getDeviceType().equals(SystemConstants.Switch)
					&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				if(dycvolt==dev.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")){
						dycmlkgList.add(dev);
					}
				}else if(zycvolt==dev.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")){
						zycmlkgList.add(dev);
					}
				}else if(gycvolt==dev.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")){
						gycmlkgList.add(dev);
					}
				}
			}
		}
		
		if(dycmlkgList.size()>0){
			for(PowerDevice dev : dycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
		}
		
		if(zycmlkgList.size()>0){
			for(PowerDevice dev : zycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
		}
		
		if(gycmlkgList.size()>0){
			for(PowerDevice dev : gycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			if(gycvolt<220){
				List<PowerDevice> devList = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(dev.getPowerVoltGrade() == gycvolt&&dev.getDeviceStatus().equals("0")){
							devList.add(dev);
						}
					}
				}
				
				EquipChoose dialog = new EquipChoose(SystemConstants.getMainFrame(), true, devList , "请选择需要断开的线路开关");
				
				tagDevList = dialog.getChooseEquip();

				if(tagDevList.size()>0){
					for(PowerDevice dev : tagDevList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
			}
		}
	
		return true;
	}

}
