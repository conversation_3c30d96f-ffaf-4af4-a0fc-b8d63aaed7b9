package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.zt.LineWorkSelectionDialog;
import com.tellhow.czp.app.yndd.rule.zt.StationWorkSelectionDialog;
import com.tellhow.czp.app.yndd.rule.zt.ZTJDKGXZ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTXLJXTOLBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通线路由检修转冷备用".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());

			String sql = "SELECT LINE_NAME,UNIT,GROUNDDISCONNECTOR_NAME,LOWERUNIT FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (  SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+curDev.getPowerDeviceID()+"')";
			List<Map<String, String>> stationLineList = DBManager.queryForList(sql);
			
			for(Map<String,String> map : stationLineList){
				String unit = StringUtils.ObjToString(map.get("UNIT"));
				String linename = StringUtils.ObjToString(map.get("LINE_NAME"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				String grounddisconnectorname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));
				String endpointtype = StringUtils.ObjToString(map.get("ENDPOINT_TYPE"));

				boolean zsccdx =  false;
				
				if(grounddisconnectorname.equals("")){
					replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧三相接地线/r/n";
				}else{
					for(PowerDevice dev : ZTJDKGXZ.chooseEquips){
						if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
							zsccdx = true;
							replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧三相接地线/r/n";
							break;
						 }
					}
				}
			}
			
			if(loadLineTrans.size()>0){
				for(PowerDevice dev : loadLineTrans){
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
						
					if(ZTJDKGXZ.chooseEquips.contains(dev)||jddzList.size()==0){
						 replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(curDev)+"线路侧三相接地线/r/n";
					}else{
					}
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
				  
				if(ZTJDKGXZ.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
					  replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(curDev)+"线路侧三相接地线/r/n";
				}else{
				}
			}
			
			for(Map<String,String> map : stationLineList){
				String unit = StringUtils.ObjToString(map.get("UNIT"));
				String linename = StringUtils.ObjToString(map.get("LINE_NAME"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				String grounddisconnectorname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));
				String endpointtype = StringUtils.ObjToString(map.get("ENDPOINT_TYPE"));

				boolean zsccdx =  false;
				
				for(PowerDevice dev : ZTJDKGXZ.chooseEquips){
					if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
						zsccdx = true;
						break;
					 }
				}
				
				if(!zsccdx){
					if(!grounddisconnectorname.equals("")){
						replaceStr += unit+"@拉开"+lowerunit+grounddisconnectorname+"/r/n";
					}
				}
			}
			
			if(loadLineTrans.size()>0){
				for(PowerDevice dev : loadLineTrans){
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
						
					if(ZTJDKGXZ.chooseEquips.contains(dev)||jddzList.size()==0){
					}else{
						 replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
					}
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
				  
				if(ZTJDKGXZ.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
				}else{
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
