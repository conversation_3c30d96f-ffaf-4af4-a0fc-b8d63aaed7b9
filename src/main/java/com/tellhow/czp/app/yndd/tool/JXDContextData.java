package com.tellhow.czp.app.yndd.tool;

import czprule.wordcard.model.CardModel;

import java.util.*;

/**
 * 检修单相关的上下文数据（及一些常量值）
 */
public class JXDContextData {

    /**停役或复役*/
    private String operation = "";

    /**检修单关联的停役票，可能多张*/
    private List<CardModel> cardModels = new ArrayList<CardModel>();

    /**记录检修单识别出的设备(及其他相关信息)，list中的一个map对应一个设备(及其他相关信息)，map的key说明查看 {@link JXDDeviceInfo}*/
    private List<Map<JXDDeviceInfo, Object>> jxdDevices = new ArrayList<Map<JXDDeviceInfo, Object>>();

    /**记录检修单信息，数据结构：【检修单编号 - 【检修单信息字段名 - 字段值】】*/
    private Map<String, Map<JXDInfo, String>> jxdInfos = new HashMap<String, Map<JXDInfo, String>>();

    /**记录已加载过厂站数据（含实时状态）的厂站id，避免反复加载*/
    Set<String> loadedStationIds = new HashSet<String>();


    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getOperation() {
        return operation;
    }

    public void setCardModels(List<CardModel> cardModels) {
        this.cardModels = cardModels;
    }

    public List<CardModel> getCardModels() {
        return cardModels;
    }

    public void setJxdDevices(List<Map<JXDDeviceInfo, Object>> jxdDevices) {
        this.jxdDevices = jxdDevices;
    }

    public List<Map<JXDDeviceInfo, Object>> getJxdDevices() {
        return jxdDevices;
    }

    public void setJxdInfos(Map<String, Map<JXDInfo, String>> jxdInfos) {
        this.jxdInfos = jxdInfos;
    }

    public Map<String, Map<JXDInfo, String>> getJxdInfos() {
        return jxdInfos;
    }

    public Set<String> getLoadedStationIds() {
        return loadedStationIds;
    }

    public void setLoadedStationIds(Set<String> loadedStationIds) {
        this.loadedStationIds = loadedStationIds;
    }

    /** 检修单设备信息字段常量 */
    public enum JXDDeviceInfo {
        note("备注,如：解环、倒方式或合环;多个备注用英文逗号分割"),
        jxdbh("检修单编号"), beginStatus("开始状态"), endStatus("结束状态"), device("设备");

        private final String desc;

        JXDDeviceInfo(String desc) {
            this.desc = desc;
        }
    }

    /** 检修单信息字段常量 */
    public enum JXDInfo {
        TDFW("停电范围"), TDCS("停电场所"), YFYJ("运方意见(批复)"), JBYJ("继保意见(批复)"), DDZXBZ("调度执行备注");

        private String desc;

        private JXDInfo(String desc) {
            this.desc = desc;
        }
    }
}
