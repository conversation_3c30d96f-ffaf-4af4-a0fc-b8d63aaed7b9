package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.yndd.view.ProtectChooseWindow;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/** 
 * 开断线路厂站选择 复选框规则类
 * <AUTHOR>
 * @dare 2021年9月7日下午3:56:05
 */
public class TransformKDXLChoose implements RulebaseInf {
	
	public static Map retMap = new HashMap();
		
	public static Map<String, Object> userStations = new HashMap<String, Object>();
	
	@Override
	public boolean execute(RuleBaseMode rbm) {
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
		
		retMap.clear();
		userStations.clear();
		
		String curLineId = pd.getPowerDeviceID();
		String sql = "SELECT UNIT FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE  LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+curLineId+"')";
		List<Map<String, Object>> stations = DBManager.queryForList(sql);
		
		// 根据线路查对侧厂站
		List<Map<String, Object>> stationsTemp = new ArrayList<Map<String, Object>>();
		List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(pd);
		
		RuleExeUtil.swapDeviceByHighVoltList(lineList);
		
		PowerDevice lineSource = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());

		for(Iterator<PowerDevice> itor = lineList.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			if(lineSource.getPowerStationName().equals(dev.getPowerStationName())){
				itor.remove();
			}
		}
		
		if(lineList != null && lineList.size()>0) {
			for(PowerDevice powerDEvice : lineList) {
				Map<String, Object> temp = new HashMap<String, Object>();
				temp.put("UNIT", powerDEvice.getPowerStationName());
				stationsTemp.add(temp);
			}
		}
		stationsTemp.addAll(stations);
		
		for(Map<String, Object> station: stations) {
			userStations.put(String.valueOf(station.get("UNIT")), "");
		}
		
		if(stationsTemp != null && stationsTemp.size()>0) {
			String[] temp = new String[stationsTemp.size()+1];
			temp[0] = "需要操作的厂站";
			int loop = 1;
			for(Map<String, Object> station:stationsTemp) {
				temp[loop] = String.valueOf(station.get("UNIT"));
				loop++;
			}
			ArrayList<String[]> equipsList = new ArrayList<String[]>();
			equipsList.add(temp);
			
			ProtectChooseWindow pcw =new ProtectChooseWindow(SystemConstants.getMainFrame(), true, equipsList, "选择需要操作的厂站",600);
			
			retMap = pcw.getSelectMap();
			
			if(retMap.size()==0){
				return false;
			}
		}
		return true;
	}
}
