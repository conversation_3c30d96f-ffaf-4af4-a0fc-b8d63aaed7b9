package com.tellhow.czp.app.yndd;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.service.CheckCzpImpl;
import com.tellhow.czp.service.CheckStatusImpl;
import com.tellhow.graphicframework.startup.StartupManager;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class GetCheckImplTestQJ {
    public static void main(String[] params) {
	    CheckCzpImpl check = new CheckCzpImpl();
	    CheckStatusImpl checkback = new CheckStatusImpl();
	
	    String param = "";
	
		StartupManager.startup();
		CZPService.getService().setArg(param);
		
		check.execute(param);
    }
}
