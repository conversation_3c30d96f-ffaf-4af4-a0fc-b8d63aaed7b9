package com.tellhow.czp.app.yndd.wordcard.lc;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLC;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLCSMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("临沧双母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> plkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL +","+ CBSystemConstants.RunTypeSwitchMLPL, "", false, true, true, true);
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade() && !dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
						mxList.add(dev);
					}
				}
			}
			
			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			}
			
			replaceStr += "核实检修申请临沧供电局-XX现场工作任务已结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，"+(int)curDev.getPowerVoltGrade()+"kVXX二次装置已正常投入，确认"+(int)curDev.getPowerVoltGrade()+"kVXX具备复电条件/r/n";
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				replaceStr += "临沧地调@执行"+stationName+deviceName+"由热备用转运行程序操作/r/n";
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += "临沧地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
				replaceStr += CommonFunctionLC.getSequenceConfirmFdContent(mlkgList, stationName);
				
				List<PowerDevice> tvdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, "", true, true, true, true);
				
				for(PowerDevice tvdz : tvdzList){
					replaceStr += CommonFunctionLC.getSequenceConfirmFdContent(tvdz, stationName);
				}
			}
			
			replaceStr += stationName+"@核实已合上"+deviceName+"TV二次空气开关/r/n";
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += stationName+"@投入"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
					replaceStr += CommonFunctionLC.getSwitchOnContent(dev, stationName, station);
					replaceStr += stationName+"@退出"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
				}
			}
			
			replaceStr += stationName+"@将"+deviceName+"运行方式恢复为正常运行方式/r/n";
		}
		
		return replaceStr;
	}
}
