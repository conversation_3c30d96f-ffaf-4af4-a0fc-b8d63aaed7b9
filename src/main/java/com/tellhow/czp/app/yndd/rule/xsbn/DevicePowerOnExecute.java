package com.tellhow.czp.app.yndd.rule.xsbn;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.EquipRadioChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.czp.app.yndd.view.EquipCheckChoose;
import com.tellhow.czp.app.yndd.view.EquipStatusAndMxChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import java.util.*;

public class DevicePowerOnExecute implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd = rbm.getPd();
		if(pd==null)
			return false;

		CommonFunctionBN.groundWireList.clear();
		CommonFunctionBN.closedLoopList.clear();
		CommonFunctionBN.chargeDeviceList.clear();
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(pd);
			List<Map<String, String>> stationLineList = CommonFunctionBN.getStationLineList(pd);
			
			List<String> userstations = new ArrayList<String>();
			
			for(Map<String, String> map : stationLineList){
				String stationName = String.valueOf(map.get("UNIT")).trim();
				String lowerUnit = String.valueOf(map.get("LOWERUNIT")).trim();

				userstations.add(stationName);
				userstations.add(lowerUnit);
			}

			List<PowerDevice> removedDevices = new ArrayList<PowerDevice>();	//用于存放被移除的设备
			List<PowerDevice> linedzList = new ArrayList<PowerDevice>();	//特殊判断，单刀闸直接合上
			
			for(Iterator<PowerDevice> itor = lineList.iterator(); itor.hasNext();){
				PowerDevice dev = itor.next();
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				List<PowerDevice> dzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"",false,true, true, true);

				if(dzList.size() > 0){
					linedzList.addAll(dzList);
				}

				for(String userstation : userstations){
                    if (stationName.contains(userstation) || userstation.contains(stationName)) {
                        removedDevices.add(dev);
                        itor.remove();
                    }
                }
			}
			
			for(Map<String,String> map : stationLineList){
				PowerDevice newLine = new PowerDevice();
				newLine.setPowerDeviceID(StringUtils.ObjToString(map.get("ID")));
				newLine.setPowerDeviceName(StringUtils.ObjToString(map.get("LINE_NAME")));
				
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				
				if(!lowerunit.equals("")){
					newLine.setPowerStationName(lowerunit);
				}else{
					newLine.setPowerStationName(StringUtils.ObjToString(map.get("UNIT")));
				}
				
				lineList.add(newLine);
			}
			
			List<PowerDevice> switchList = new ArrayList<PowerDevice>();

			if(CBSystemConstants.isCurrentSys){
				List<String> defaultStatusList = new ArrayList<String>();
				List<String> expStatusList = new ArrayList<String>();
				
				String showMessage = "请选择需要操作的线路（至少选择一项）";

				EquipCheckChoose ecc = new EquipCheckChoose(SystemConstants.getMainFrame(),true,lineList,showMessage,true,true,true);
				List<PowerDevice> chooseEquips = ecc.getChooseEquip();
				
				if(chooseEquips.size()==0)
					return false;
				
				if(rbm.getBeginStatus().equals("3")){
					showMessage = "请选择拆除三相接地线的线路";
					
					EquipCheckChoose ecc2 = new EquipCheckChoose(SystemConstants.getMainFrame(), true, lineList , showMessage);
					List<PowerDevice> chooseEquips2 = ecc2.getChooseEquip();
					
					for(PowerDevice chooseEquip : chooseEquips2){
						CommonFunctionBN.groundWireList.add(chooseEquip);
					}
				}
				
				for(PowerDevice line : chooseEquips) {
					if(!line.getPowerDeviceID().equals("")){
						List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC,"", false, true, true, true);
						
						List<PowerDevice> otherxlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> kgList = new ArrayList<PowerDevice>();

						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(line.getPowerStationID());

						if(mapStationDevice != null){
							for(Iterator<PowerDevice> itor = mapStationDevice.values().iterator(); itor.hasNext();) {
								PowerDevice dev = itor.next();
								
								if(line.getPowerVoltGrade() == dev.getPowerVoltGrade()){
									if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										mlkgList.add(dev);
									}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) && !xlkgList.contains(dev)){
										otherxlkgList.add(dev);
									}
								}
							}
							
							kgList.addAll(xlkgList);
							kgList.addAll(otherxlkgList);
							kgList.addAll(mlkgList);
							
							if(kgList.size() == 3){
								switchList.addAll(mlkgList);
							}else if(mlkgList.size() == 0 && kgList.size() == 2){
								switchList.addAll(otherxlkgList);
							}
							
	                        if(xlkgList != null && !xlkgList.isEmpty()){
	                            switchList.addAll(xlkgList);
	                        }
						}
                    }
				}

				for(PowerDevice dev : switchList) {
					defaultStatusList.add(rbm.getEndState());
				}
				
				EquipStatusAndMxChoose dialog = new EquipStatusAndMxChoose(SystemConstants.getMainFrame(), true, switchList, defaultStatusList,expStatusList, "请选择断路器状态：",SystemConstants.InOutLine);
				List<Map<String,String>> tagStatusList = dialog.getTagStatusList();
				 
				if(tagStatusList.size() > 0){
			        List<PowerDevice> yxList = new ArrayList<PowerDevice>();
			        List<PowerDevice> rbyList = new ArrayList<PowerDevice>();
					List<PowerDevice> mxList = new ArrayList<PowerDevice>();

					for(Map<String,String> tagStatusMap : tagStatusList){
						 String equipid = tagStatusMap.get("设备ID");
						 String status = tagStatusMap.get("状态");
						 String mxid = tagStatusMap.get("母线ID");
						 String opkind = tagStatusMap.get("操作类型");

						 PowerDevice curDev = CBSystemConstants.getPowerDevice(equipid);
						 mxList.add(CBSystemConstants.getPowerDevice(mxid));
						 
						 if(opkind.equals("合环")){
							 CommonFunctionBN.closedLoopList.add(curDev);
						 }else if(opkind.equals("充电")){
							 CommonFunctionBN.chargeDeviceList.add(curDev);
						 }
						 
						 for(PowerDevice dev : switchList){
							 if(dev.getPowerDeviceID().equals(equipid)){
								if(status.equals("1")){
			    					rbyList.add(dev);
			    				}else if(status.equals("0")){
			    					yxList.add(dev);
			    				}
							}
						}
					}


                    removedDevices.addAll(chooseEquips);
                    
					for(PowerDevice dev : removedDevices){
						if(!dev.getPowerDeviceID().isEmpty()){
							List<PowerDevice> jddzList = RuleExeUtil.getDeviceList(dev,SystemConstants.SwitchFlowGroundLine, SystemConstants.PowerTransformer,true, true, true);

							if (jddzList != null) for(PowerDevice jddz : jddzList){
								RuleExeUtil.deviceStatusChange(jddz,jddz.getDeviceStatus(),"1");
							}
						}
					}
					
					for(PowerDevice dev : rbyList){
						if(dev.getDeviceStatus().equals("0")){
							RuleExeUtil.deviceStatusExecute(dev,dev.getDeviceStatus(),"1");
						}else{
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							for(PowerDevice dz : dzList){
								if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
									if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
										List<PowerDevice> tempList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
										
										if(tempList.size() > 0){
											if(mxList.contains(tempList.get(0))){
												RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
											}
										}
									}else{
										RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
									}
								}
							}
						}
					}
					
					for(PowerDevice dev : rbyList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(!dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
								RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
							}
						}
					}
					
					for(PowerDevice dev : yxList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
								if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
								List<PowerDevice> tempList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
								
								if(tempList.size() > 0){
									if(mxList.contains(tempList.get(0))){
										RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
									}
								}
								}else{
									RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
								}
							}
						}
					}
					
					if(rbm.getEndState().equals("1") || rbm.getEndState().equals("0")){
						for(PowerDevice dev : linedzList){
							if(dev.getDeviceStatus().equals("1")){
								RuleExeUtil.deviceStatusExecute(dev,dev.getDeviceStatus(),"0");
							}
						}
					}
					
					for(PowerDevice dev : yxList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(!dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
								RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
							}
						}
					}
					
					for(PowerDevice dev : yxList){
						RuleExeUtil.deviceStatusExecute(dev,dev.getDeviceStatus(),"0");
					}
				 }else{
					 return false;
				 }
			}
			
			return true;
		}
		else if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){
			if(rbm.getEndState().equals("0")){
				setZXDOn(pd);
			}
			
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(pd);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(pd);
			List<PowerDevice> zbdyckgList =	RuleExeUtil.getTransformerSwitchLow(pd);
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(pd);
			List<PowerDevice> zbdzList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate, CBSystemConstants.RunTypeKnifeZB);

			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> switchList = new ArrayList<PowerDevice>();

			switchList.addAll(zbgyckgList);
			switchList.addAll(zbzyckgList);
			switchList.addAll(zbdyckgList);
			
			if(CBSystemConstants.isCurrentSys){
				//内桥接线
				if(zbgycdzList.size() == 1){
					List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();

					if(!zbgyckgList.isEmpty()){
						List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
						gycmlkgList.addAll(kgList);
					}else{
						List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
						List<PowerDevice> kgList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
						gycmlkgList.addAll(kgList);
					}
					
					switchList.addAll(0, gycmlkgList);
					
					for(PowerDevice dev : gycmlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					for(PowerDevice dev : zbgyckgList){
						List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
						
						for(PowerDevice line : lineList){
							if(line.getDeviceStatus().equals("0")){
								RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
							}
						}
					}
					
					// 合上主变刀闸
					List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
					
					for (PowerDevice kf : kfList) {
						RuleExeUtil.deviceStatusExecute(kf, kf.getDeviceStatus(), "0");
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(gycmlkgList);
					tempList.addAll(zbgyckgList);

					if(!tempList.isEmpty()){
						CommonFunctionBN.chargeDeviceByNqZbFdList.clear();
						EquipRadioChoose erc = new EquipRadioChoose(SystemConstants.getMainFrame(), true, tempList,
								"请选择对"+CZPService.getService().getDevName(pd)+"充电的断路器：",true);
						PowerDevice kgChangeOn = erc.getChooseEquip();
						
						if(erc.isCancel()||kgChangeOn== null)
							return false;
						
						CommonFunctionBN.chargeDeviceByNqZbFdList.add(kgChangeOn);
						RuleExeUtil.deviceStatusExecute(kgChangeOn, kgChangeOn.getDeviceStatus(), "0");
					}
				}
				
				if(!zbzyckgList.isEmpty()){
					List<PowerDevice> kgList = getMlkgList(zbzyckgList.get(0));
					zycmlkgList.addAll(kgList);
				}
				
				if(!zbdyckgList.isEmpty()){
					List<PowerDevice> kgList = getMlkgList(zbdyckgList.get(0));
					dycmlkgList.addAll(kgList);
				}
				
				List<String> defaultStatusList = new ArrayList<String>();
				List<String> expStatusList = new ArrayList<String>();

				switchList.addAll(zycmlkgList);
				switchList.addAll(dycmlkgList);
				
				for(PowerDevice line : switchList) {
					defaultStatusList.add(rbm.getEndState());
				}
				
				EquipStatusAndMxChoose dialog = new EquipStatusAndMxChoose(SystemConstants.getMainFrame(), true, switchList,
						defaultStatusList,expStatusList, "请选择断路器状态：",SystemConstants.PowerTransformer);
				List<Map<String,String>> tagStatusList = dialog.getTagStatusList();
				 
				if(!tagStatusList.isEmpty()){
					List<PowerDevice> allList = new ArrayList<PowerDevice>();
			        List<PowerDevice> yxList = new ArrayList<PowerDevice>();
			        List<PowerDevice> rbyList = new ArrayList<PowerDevice>();
					List<PowerDevice> mxList = new ArrayList<PowerDevice>();

					for(Map<String,String> tagStatusMap : tagStatusList){
						 String equipid = tagStatusMap.get("设备ID");
						 String status = tagStatusMap.get("状态");
						 String mxid = tagStatusMap.get("母线ID");
						 String opkind = tagStatusMap.get("操作类型");

						 PowerDevice curDev = CBSystemConstants.getPowerDevice(equipid);
						 mxList.add(CBSystemConstants.getPowerDevice(mxid));
						 
						 if(opkind.equals("合环")){
							 CommonFunctionBN.closedLoopList.add(curDev);
						 }else if(opkind.equals("充电")){
							 CommonFunctionBN.chargeDeviceList.add(curDev);
						 }
						 
						 for(PowerDevice dev : switchList){
							 if(dev.getPowerDeviceID().equals(equipid)){
								 if(mxid.isEmpty()){//单母接线
									 RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(),status);
								 }else{
									if(status.equals("1")){
			    						rbyList.add(dev);
				    				}else if(status.equals("0")){
				    					yxList.add(dev);
				    				}
								}
							}
						}
					}
					
					allList.addAll(rbyList);
			        allList.addAll(yxList);
					 
					 for(PowerDevice dev : allList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
								List<PowerDevice> tempList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
								
								if(!tempList.isEmpty()){
									if(mxList.contains(tempList.get(0))){
										RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
									}
								}
							}
						}
					 }
					
					 for(PowerDevice dev : allList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(!dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
								RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
							}
						}
					 }
					 
					 for(PowerDevice dev : zbdzList){
			        	RuleExeUtil.deviceStatusExecute(dev,dev.getDeviceStatus(),"0");
					 }
					 
					 for(PowerDevice dev : yxList){
			        	RuleExeUtil.deviceStatusExecute(dev,dev.getDeviceStatus(),"0");
					 }
				 }else{
					 return false;
				 }
			}
			
			if(rbm.getEndState().equals("0")){
				setZXDOff(pd);
			}
			
			return true;
		}
		else if(pd.getDeviceType().equals(SystemConstants.Switch)){
			List<PowerDevice> switchList = new ArrayList<PowerDevice>();
			switchList.add(pd);
			List<String> defaultStatusList = new ArrayList<String>();
			List<String> expStatusList = new ArrayList<String>();

			for(PowerDevice line : switchList) {
				defaultStatusList.add(rbm.getEndState());
			}
			
			EquipStatusAndMxChoose dialog = new EquipStatusAndMxChoose(SystemConstants.getMainFrame(), true, switchList, defaultStatusList,expStatusList, "请选择断路器状态：",SystemConstants.PowerTransformer);
			List<Map<String,String>> tagStatusList = dialog.getTagStatusList();
			
			if(tagStatusList.size() > 0){
				List<PowerDevice> allList = new ArrayList<PowerDevice>();
		        List<PowerDevice> yxList = new ArrayList<PowerDevice>();
		        List<PowerDevice> rbyList = new ArrayList<PowerDevice>();
				List<PowerDevice> mxList = new ArrayList<PowerDevice>();

				for(Map<String,String> tagStatusMap : tagStatusList){
					 String equipid = tagStatusMap.get("设备ID");
					 String status = tagStatusMap.get("状态");
					 String mxid = tagStatusMap.get("母线ID");
					 String opkind = tagStatusMap.get("操作类型");

					 PowerDevice curDev = CBSystemConstants.getPowerDevice(equipid);
					 mxList.add(CBSystemConstants.getPowerDevice(mxid));
					 
					 if(opkind.equals("合环")){
						 CommonFunctionBN.closedLoopList.add(curDev);
					 }else if(opkind.equals("充电")){
						 CommonFunctionBN.chargeDeviceList.add(curDev);
					 }
					 
					 for(PowerDevice dev : switchList){
						 if(dev.getPowerDeviceID().equals(equipid)){
							 if(mxid.equals("")){//单母接线
								 RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(),status);
							 }else{
								if(status.equals("1")){
		    						rbyList.add(dev);
			    				}else if(status.equals("0")){
			    					yxList.add(dev);
			    				}
							}
						}
					}
				}
				
				allList.addAll(rbyList);
		        allList.addAll(yxList);
				 
				 for(PowerDevice dev : allList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
							List<PowerDevice> tempList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
							
							if(tempList.size() > 0){
								if(mxList.contains(tempList.get(0))){
									RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
								}
							}
						}
					}
				 }
				
				 for(PowerDevice dev : allList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(!dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
							RuleExeUtil.deviceStatusExecute(dz,dz.getDeviceStatus(),"0");
						}
					}
				 }
				 
				 for(PowerDevice dev : yxList){
		        	RuleExeUtil.deviceStatusExecute(dev,dev.getDeviceStatus(),"0");
				 }
			 }else{
				 return false;
			 }
		}
		
		return true;
	}
	
	public List<PowerDevice>  getMlkgList(PowerDevice pd){
		List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> kgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
		
		for(Iterator<PowerDevice> itor = kgList.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				
				if(tempmxList.size()<2){
					itor.remove();
				}else{
					for(PowerDevice tempmx : tempmxList){
						List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(tempmx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeKnifeQT, false, true, true, true);
						
						if(tempzbList.size() == 0){
							itor.remove();
							break;
						}
					}
				}
			}
		}
		
		if(kgList.size()==0){
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							kgList.add(dev);
						}
					}
				}
			}
		}
		
		mlkgList.addAll(kgList);
	
		return mlkgList;
	}

	public boolean setZXDOn(PowerDevice pd) {
		List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		RuleExeUtil.swapLowDeviceList(gdList);
		for(PowerDevice gd : gdList) {
			RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
		}
		return true;
	}
	
	
	/**
	 * 分列运行主变高压侧中性点地刀拉开
	 * @param pd
	 * @return
	 */
	public boolean setZXDOff(PowerDevice pd) {
		List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		RuleExeUtil.swapLowDeviceList(gdList);
		for(PowerDevice gd : gdList) {
			RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "1");
		}
		return true;
	}
}

