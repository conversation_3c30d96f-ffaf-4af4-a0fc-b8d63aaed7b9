package com.tellhow.czp.app.yndd.wordcard.nj;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionNJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrNJKGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("怒江开关复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			if(RuleExeUtil.isDeviceHadStatus(curDev, "2", "1")){
				String mxName = "";
				
				if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : mxList){
						mxName = CZPService.getService().getDevName(mx);
						break;
					}
				}
				
				if(CommonFunctionNJ.ifSwitchSeparateControl(curDev)){
					if(!mxName.equals("")){
						replaceStr += "怒江地调@执行"+stationName+deviceName+"由冷备用转热备用于"+mxName+"程序操作/r/n";
					}else{
						replaceStr += "怒江地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					}

					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionNJ.getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}
			}
			
			if(curDev.getDeviceStatus().equals("0")){
				replaceStr += CommonFunctionNJ.getSwitchOnContent(curDev, stationName,station);
			}
		}
		
		return replaceStr;
	}

}
