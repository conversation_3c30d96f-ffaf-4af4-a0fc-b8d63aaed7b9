package com.tellhow.czp.app.yndd.tool;

import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @BelongProject : powernet-graphic-app-czpyndd
 * @BelongPackage : com.tellhow.czp.app.yndd.tool
 * @Description : TODO
 * <AUTHOR> WangJQ
 * @Date : 2025/4/11 16:21
 */
public class GetDtdData {

    protected static final Logger logger = Logger.getLogger(GetDtdData.class);
    protected static List<Map<String, String>> modelList = new ArrayList<Map<String, String>>(); // 术语模板

}
