package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.yx.TransformExecuteYX;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYXXBZZBFD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪线变组接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zxdjddzList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			for(Iterator<PowerDevice> it = dycmxList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);

				if(mlkgList.size() == 0){
					it.remove();
				}
			}
			
			for(PowerDevice dev : dycmxList){
				dycmlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				
				if(dycmlkgList.size() > 0){
					break;
				}
			}
			 
			List<PowerDevice> gdList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			RuleExeUtil.swapLowDeviceList(gdList);
			
			for(PowerDevice gd : gdList) {
				if(RuleExeUtil.getDeviceBeginStatus(gd).equals("1")){
					zxdjddzList.add(gd);
				}
			}
			
			if(zxdjddzList.size() > 0){
				if(curDev.getPowerVoltGrade() == 220){
					replaceStr += "玉溪地调@执行合上"+stationName+CZPService.getService().getDevName(zxdjddzList)+"程序操作/r/n";
				}else{
					replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(zxdjddzList)+"/r/n";
				}
			}
			
			for(PowerDevice gd : gdList) {
				String zxdjddzName = CZPService.getService().getDevName(gd);
				
				if(RuleExeUtil.getDeviceBeginStatus(gd).equals("1")&&gd.getPowerVoltGrade() == 220){
					replaceStr += stationName+"@核实"+zxdjddzName+"在合闸位置/r/n";
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(CommonFunction.ifSwitchSeparateControl(dev)){
					replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
					
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")&&dz.getPowerVoltGrade() > 110){
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
					if(CommonFunction.ifSwitchSeparateControl(dev)){
						replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")&&dz.getPowerVoltGrade() > 110){
								replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
							}
						}
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
				}
			}
			
			PowerDevice dycmx = new PowerDevice();
			
			if(dycmxList.size() > 0){
				dycmx = dycmxList.get(0);
				
				List<PowerDevice> drList = RuleExeUtil.getDeviceList(dycmx,SystemConstants.ElecCapacity, SystemConstants.PowerTransformer, true, true, true);

				List<PowerDevice> othermxList = new ArrayList<PowerDevice>();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(dycmx.getPowerStationID());

				for(Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getPowerVoltGrade() == dycmx.getPowerVoltGrade()){
						if(dev.getDeviceType().equals(SystemConstants.MotherLine) 
								&& !dycmx.getPowerDeviceID().equals(dev.getPowerDeviceID())){
							
							List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);

							if(mlkgList.size() > 0){
								othermxList.add(dev);
							}
						}
					}
				}
				
				for(PowerDevice othermx : othermxList){
					List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(othermx, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchQT, SystemConstants.PowerTransformer, false, true, true, true);
					List<PowerDevice> zybdzList = RuleExeUtil.getDeviceList(othermx, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT, SystemConstants.PowerTransformer, false, true, true, true);
					List<PowerDevice> zybdevList = new ArrayList<PowerDevice>();
					
					zybdevList.addAll(zybkgList);
					zybdevList.addAll(zybdzList);

					for(PowerDevice dev : zybdevList){
						if(dev.getPowerDeviceName().contains("站用变")){
							String temp = CZPService.getService().getDevName(dev);
							
							if(temp.contains("站用变")){
								replaceStr += stationName+"@核实站用电已倒由"+temp.substring(0, temp.indexOf("站用变")+3)+"供电正常/r/n";
							}else{
								replaceStr += stationName+"@核实站用电已倒由XX站用变供电正常/r/n";
							}
							break;
						}
					}
				}
				
				for(PowerDevice dev  : drList){
					if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), dev.getDeviceStatus());
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
					}
				}
				
				if(dycmx.getPowerVoltGrade() == 35 || dycmx.getPowerVoltGrade() == 10){
					replaceStr += "玉溪配调@核实"+stationName+CZPService.getService().getDevName(dycmx)+"可以短时停电倒电/r/n";
				}
				
				
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dycmx, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC,"", false, true, true, true);
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dycmx, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML,"", false, true, true, true);
				
				String sourcezbname = "";
				
				for(PowerDevice dev : zbkgList){
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev,SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);

					for(PowerDevice zb : zbList){
						sourcezbname = CZPService.getService().getDevName(zb);
						break;
					}
				}
				
				String loadzbname = "";
				
				for(PowerDevice dev : othermxList){
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev,SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);

					for(PowerDevice zb : zbList){
						loadzbname = CZPService.getService().getDevName(zb);
						break;
					}
				}
				
				replaceStr += "玉溪地调@执行"+stationName+dycmx+"由"+loadzbname+"供电停电倒由"+sourcezbname+"供电程序操作/r/n";
				
				if(dycmx.getPowerVoltGrade() == 35 || dycmx.getPowerVoltGrade() == 10){
					replaceStr += "玉溪配调@通知"+stationName+CZPService.getService().getDevName(dycmx)+"短时停电倒电完毕/r/n";
				}
				
				replaceStr += stationName+"@按当前运行方式投入"+(int)dycmx.getPowerVoltGrade()+"kV备自投装置/r/n";
			}
			
			if(zxdjddzList.size() > 0){
				if(curDev.getPowerVoltGrade() == 220){
					replaceStr += "玉溪地调@执行拉开"+stationName+CZPService.getService().getDevName(zxdjddzList)+"程序操作/r/n";
				}else{
					replaceStr += "玉溪地调@遥控拉开"+stationName+CZPService.getService().getDevName(zxdjddzList)+"/r/n";
				}
			}
			
			for(PowerDevice gd : gdList) {
				String zxdjddzName = CZPService.getService().getDevName(gd);
				
				if(RuleExeUtil.getDeviceBeginStatus(gd).equals("0")&&gd.getPowerVoltGrade() == 220){
					replaceStr += stationName+"@核实"+zxdjddzName+"在拉开位置/r/n";
				}
			}
		}
		return replaceStr;
	}

}
