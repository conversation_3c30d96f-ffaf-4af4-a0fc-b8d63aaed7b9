/**

 **/
package com.tellhow.czp.app.yndd.rule;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

public class KGBZTDDDExecute implements RulebaseInf {//开关备自投调电低
	/**
	 * 选择执行满足输入条件的开关  输入条件（变电站类型，设备运行类型，初始状态，执行动作）
	 */
	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;

		RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
		
		List<PowerDevice> mxList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false,true, true);

		if(mxList.size()>0){
			for(PowerDevice mx : mxList){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false,true, true);

				if(zbList.size()>0){
					for(PowerDevice zb : zbList){
						if(RuleExeUtil.isTransformerXBDY(zb)){//线变组接线
							List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(zb);
								
							if(zbzyckgList.size()>0){
								for(PowerDevice zbzyckg : zbzyckgList){
									RuleExeUtil.deviceStatusExecute(zbzyckg, zbzyckg.getDeviceStatus(), "1");
									
									List<PowerDevice>  fdswList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);

									if(fdswList.size()>0){
										RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
									}
								}
							}
								
							List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zb);

							if(zbdyckgList.size()>0){
								for(PowerDevice zbdyckg : zbdyckgList){
									RuleExeUtil.deviceStatusExecute(zbdyckg, zbdyckg.getDeviceStatus(), "1");
								}

								for(PowerDevice zbdyckg : zbdyckgList){
									List<PowerDevice>  fdswList = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,  CBSystemConstants.RunTypeSwitchFHC, false, true, false, true);

									if(fdswList.size()>0){
										if(fdswList.get(0).getDeviceStatus().equals("1")){
											RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
										}else{
											fdswList = RuleExeUtil.getDeviceList(fdswList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);
											
											if(fdswList.size()>0){
												if(fdswList.get(0).getDeviceStatus().equals("1")){
													RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
												}else{
													fdswList = RuleExeUtil.getDeviceList(fdswList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, true, true);

													if(fdswList.size()>0){
														if(fdswList.get(0).getDeviceStatus().equals("1")){
															RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
														}
													}
												}
											}
										}
									}
								}
							}
						}else{
							List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(zb);
							
							if(zbzyckgList.size()>0){
								for(PowerDevice zbzyckg : zbzyckgList){
									RuleExeUtil.deviceStatusExecute(zbzyckg, zbzyckg.getDeviceStatus(), "1");
									
									List<PowerDevice>  fdswList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);

									if(fdswList.size()>0){
										for(PowerDevice fdsw : fdswList){
											RuleExeUtil.deviceStatusExecute(fdsw, fdsw.getDeviceStatus(), "0");
										}
									}
								}
							}
							
							List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zb);

							if(zbdyckgList.size()>0){
								for(PowerDevice zbdyckg : zbdyckgList){
									RuleExeUtil.deviceStatusExecute(zbdyckg, zbdyckg.getDeviceStatus(), "1");
									
									List<PowerDevice>  fdswList = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);

									if(fdswList.size()>0){
										for(PowerDevice fdsw : fdswList){
											RuleExeUtil.deviceStatusExecute(fdsw, fdsw.getDeviceStatus(), "0");
										}
									}
								}
							}
						}
					}
				}
			}
		}else{//线变组接线
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false,true, true);

			if(zbList.size()>0){
				for(PowerDevice zb : zbList){
					List<PowerDevice> gdList = RuleExeUtil.getDeviceList(zb, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
					for(PowerDevice gd : gdList) {
						RuleExeUtil.deviceStatusSet(gd, gd.getDeviceStatus(), "0");
					}
					
					List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(zb);
					
					if(zbzyckgList.size()>0){
						for(PowerDevice zbzyckg : zbzyckgList){
							RuleExeUtil.deviceStatusExecute(zbzyckg, zbzyckg.getDeviceStatus(), "1");
							
							List<PowerDevice>  fdswList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);

							if(fdswList.size()>0){
								RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
							}
						}
					}
					
					List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zb);

					if(zbdyckgList.size()>0){
						for(PowerDevice zbdyckg : zbdyckgList){
							RuleExeUtil.deviceStatusExecute(zbdyckg, zbdyckg.getDeviceStatus(), "1");
						}

						for(PowerDevice zbdyckg : zbdyckgList){
							List<PowerDevice>  fdswList = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,  CBSystemConstants.RunTypeSwitchFHC, false, true, false, true);

							if(fdswList.size()>0){
								if(fdswList.get(0).getDeviceStatus().equals("1")){
									RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
								}else{
									fdswList = RuleExeUtil.getDeviceList(fdswList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);
									
									if(fdswList.size()>0){
										if(fdswList.get(0).getDeviceStatus().equals("1")){
											RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
										}else{
											fdswList = RuleExeUtil.getDeviceList(fdswList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, true, true);

											if(fdswList.size()>0){
												if(fdswList.get(0).getDeviceStatus().equals("1")){
													RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
		
		RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "0");
		
		return true;
	}

}
