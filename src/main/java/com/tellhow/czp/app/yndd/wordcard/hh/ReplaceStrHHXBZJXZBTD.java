package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHXBZJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河线变组接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev);

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> otherzbdyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbgyckgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();

			zbkgList.addAll(zbdyckgList);
			zbkgList.addAll(zbzyckgList);
			zbkgList.addAll(zbgyckgList);

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbList){
				if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
					otherzbList.add(dev);
				}
			}
			
			for(PowerDevice dev : otherzbList){
				otherzbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
				otherzbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
				otherzbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
			}
			
			List<PowerDevice> gycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> zycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbzyckgList){
				gycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			for(PowerDevice dev : zbzyckgList){
				zycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice dycmx : dycmxList){
					dycmlkgList  = RuleExeUtil.getDeviceList(dycmx,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true);
				}
			}
			
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbzyckgList){
				zycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice zycmx : zycmxList){
					zycmlkgList  = RuleExeUtil.getDeviceList(zycmx,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true);
				}
			}
			
			List<PowerDevice> hhkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					hhkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : otherzbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					hhkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					hhkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : otherzbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					hhkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				String devName = CZPService.getService().getDevName(dev);

				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+CZPService.getService().getDevName(zbList)+(int)dev.getPowerVoltGrade()+"kV侧后备保护动作跳"+devName+"/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				String devName = CZPService.getService().getDevName(dev);

				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+CZPService.getService().getDevName(zbList)+(int)dev.getPowerVoltGrade()+"kV侧后备保护动作跳"+devName+"/r/n";
				}
			}
			
			if(zbList.size() == 2){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(zbList)+"具备并列条件/r/n";
			}
			
			replaceStr += CommonFunctionHH.getZbZxdStrReplace(zbList);
			
			if(hhkgList.size() > 0){
				replaceStr += CommonFunctionHH.getYcHsStrReplace(hhkgList,stationName);
			}
			
			if(zbkgList.size() > 0){
				replaceStr += CommonFunctionHH.getYcDkStrReplace(zbkgList,stationName);
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				replaceStr += CommonFunctionHH.getTransformerRbyToLbyStrReplace(curDev, stationName);
			}
			
			for(PowerDevice dev : dycmlkgList){
				String devName = CZPService.getService().getDevName(dev);

				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+deviceName+(int)dev.getPowerVoltGrade()+"kV侧后备保护动作跳"+devName+"/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				String devName = CZPService.getService().getDevName(dev);

				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+deviceName+(int)dev.getPowerVoltGrade()+"kV侧后备保护动作跳"+devName+"/r/n";
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}