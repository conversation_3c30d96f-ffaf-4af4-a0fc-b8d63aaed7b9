package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.zt.SelectTicketKind;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTDMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通单母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);

			// 判断厂站名称是否以数字结尾
			if (stationName.matches(".*\\d$")) {
				stationName += "=N=";
			}
			
			if(SelectTicketKind.ticketKind.equals("综合票")){
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += stationName+"@将"+deviceName+"由运行转冷备用/r/n";
					}else{
						replaceStr += stationName+"@将"+deviceName+"由运行转热备用/r/n";
					}
				}else{
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
					}
				}
			}else{
				String otherdeviceName = "";
				
				List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
				List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
				List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
				
				List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
				List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
				List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
				
				List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
				
				for(PowerDevice dev : zbdyckgList){
					dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice mx : dycmxList){
						dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice mx : mxList){
						zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice mx : mxList){
						gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
				}
				
				List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
				List<PowerDevice> otherzxdjddzList = new ArrayList<PowerDevice>();
						
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade() && !dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							otherzbList.add(dev);
						}
					}
				}
				
				for(PowerDevice dev : otherzbList){
					otherdeviceName = CZPService.getService().getDevName(dev);
					otherzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
					RuleExeUtil.swapLowDeviceList(otherzxdjddzList);
					break;
				}
				
				List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(zxdjddzList);
				
				boolean isZxdJddzAllOff = true;

				for(PowerDevice dev : zxdjddzList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						isZxdJddzAllOff = false;
					}
				}
				
				for(PowerDevice dev : otherzxdjddzList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						isZxdJddzAllOff = false;
					}
				}
				
				boolean isZxdAble = false;
				
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")
						&&RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					isZxdAble = true;
				}
				
				List<String> voltList = new ArrayList<String>();
				
				for(PowerDevice dev  : dycmlkgList){
					voltList.add((int)dev.getPowerVoltGrade()+"kV备自投装置");
				}
				
				for(PowerDevice dev  : zycmlkgList){
					voltList.add((int)dev.getPowerVoltGrade()+"kV备自投装置");
				}
				
				if(voltList.size() > 0){
					replaceStr += CommonFunction.getBztResult(voltList , "退出");
				}
				
				if(isZxdAble){
					if(isZxdJddzAllOff&&zxdjddzList.size()>0){
						replaceStr += stationName+"@将"+deviceName+(int)curDev.getPowerVoltGrade()+"kV侧中性点接地方式由间隙接地改为直接接地，相关保护配合/r/n";
					}else{
						for(PowerDevice dev : zxdjddzList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
								replaceStr += stationName+"@核实"+deviceName+(int)dev.getPowerVoltGrade()+"kV侧中性点接地方式为直接接地，相关保护配合/r/n";
							}
						}
						
						for(PowerDevice dev : otherzxdjddzList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
								replaceStr += stationName+"@核实"+otherdeviceName+(int)dev.getPowerVoltGrade()+"kV侧中性点接地方式为直接接地，相关保护配合/r/n";
							}
						}
						

						for(PowerDevice dev : zxdjddzList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
								
							}else{
								replaceStr += stationName+"@将"+deviceName+(int)dev.getPowerVoltGrade()+"kV侧中性点接地方式由间隙接地改为直接接地，相关保护配合/r/n";
							}
						}
						
						for(PowerDevice dev : otherzxdjddzList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
								
							}else{
								replaceStr += stationName+"@将"+deviceName+(int)dev.getPowerVoltGrade()+"kV侧中性点接地方式由间隙接地改为直接接地，相关保护配合/r/n";
							}
						}
					}
				}
				
				replaceStr += CommonFunction.getTransformerHHTdContent(curDev,"昭通地调",stationName);
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					replaceStr += "将"+deviceName+"由热备用转冷备用/r/n";
				}
				
				if(isZxdAble){
					if(curDev.getPowerVoltGrade() > 35){
						replaceStr += stationName+"@将"+deviceName+(int)curDev.getPowerVoltGrade()+"kV侧中性点接地方式由直接接地改为间隙接地，相关保护配合/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
}
