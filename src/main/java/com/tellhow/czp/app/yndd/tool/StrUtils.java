package com.tellhow.czp.app.yndd.tool;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.security.Security;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;


import com.sun.crypto.provider.SunJCE;

/**
 * 
 * <AUTHOR>
 * 
 */
public class StrUtils {

	/**
	 * 字符串是否非空
	 * 
	 * @param str
	 * @return
	 */
	public static final boolean isNotBlank(Object str) {
		return !isBlank(str);
	}

	/**
	 * 字符串是否为空
	 * 
	 * @param str
	 * @return
	 */
	public static final boolean isBlank(Object str) {
		return (str == null || "".equals(str)||"null".equals(str) ||"".equals(str.toString().trim()));
	}

	/**
	 * 得到非空值
	 * 
	 * @param obj
	 * @return
	 */
	public static final String getNotNullString(Object obj) {
		if (obj == null) {
			return "";
		}
		return obj.toString();
	}

	public static final String getNullStringDefault(Object obj,String defaultString) {
		if (obj == null) {
			return defaultString;
		}
		return obj.toString();
	}
	// 字符串加密
	public static String getEncrypt(String temp) {
		String result = "";
		byte result_byte[] = null;
		byte data[] = null;
		Security.addProvider(new SunJCE());
		data = temp.getBytes();
		result = (new BASE64Encoder()).encode(data);
		return result;
	}

	// 字符串解密
	public static String getDecrypt(String temp) {
		String result = "";
		try {
			byte data[] = (new BASE64Decoder()).decodeBuffer(temp);
			result = new String(data);
		} catch (IOException e) {
			String s = result;
			return s;
		}
		return result;
	}

	public static final int PATCH_BEFORE = 1;// 前补

	public static final int PATCH_AFTER = 2;// 前补


	// 替换标识类似c#
	public static final String format(String str, Object[] pars) {
		for (int i = 0; i < pars.length; i++) {
			String string = getNotNullString(pars[i]);
			str = replaceAll(str, "{" + i + "}", string);
		}
		return str;
	}

	/**
	 * jdk里的replaceAll等替换子串的方法中必须是正则表达式 该方法用于替换任意子串
	 * 
	 * @param content
	 *            执行子串替换的字符串
	 * @param expression
	 *            被替换掉的子串
	 * @param replacement
	 *            用来替换的子串
	 * @return
	 */
	public static String replaceAll(String content, String expression,
			String replacement) {
		if (isBlank(content)) {
			return "";
		}
		StringBuffer sb = new StringBuffer();
		int begin = 0;
		int end = content.indexOf(expression);
		int len = expression.length();
		while (end >= 0) {
			sb.append(content.substring(begin, end));
			sb.append(replacement);
			begin = end + len;
			end = content.indexOf(expression, begin);
		}
		sb.append(content.substring(begin));
		return sb.toString();
	}

	public static String replaceAll(String content, Map par, String startS,
			String endS) {
		StringBuffer result = new StringBuffer();
		StringBuffer strBuffer = new StringBuffer(content);
		int slen = startS.length();
		int elen = endS.length();
		int startindex = 0;
		int endindex = 0;

		// 取出变量
		do {
			startindex = strBuffer.indexOf(startS, endindex);
			if (startindex >= 0) {
				String preStr = strBuffer.substring(endindex, startindex);
				result.append(preStr);
				endindex = strBuffer.indexOf(endS, startindex) + endS.length();
				String str = strBuffer.substring(startindex + slen, endindex
						- elen);
				if (par.containsKey(str)) {
					result.append(par.get(str));
				}
			}
		} while (startindex >= 0);

		result.append(strBuffer.substring(endindex));
		return result.toString();
	}

	/**
	 * 删除指定的字符串
	 * 
	 * @param content
	 * @param strs
	 * @return
	 */
	public static String deleteStr(String content, String[] strs) {
		String result = "";
		for (int i = 0; i < strs.length; i++) {
			result = replaceAll(content, strs[i], " ");
		}

		return result;
	}

	// 用于HTML标签的常量
	private static final char[] QUOTE_ENCODE = "&quot;".toCharArray();

	private static final char[] AMP_ENCODE = "&amp;".toCharArray();

	private static final char[] LT_ENCODE = "&lt;".toCharArray();

	private static final char[] GT_ENCODE = "&gt;".toCharArray();

	/**
	 * 将字符串中的html相关的字符替换成可显示的值
	 * 
	 * @param in
	 * @return
	 */
	public static final String escapeHTMLTags(String in) {
		if (in == null) {
			return null;
		}
		char ch;
		int i = 0;
		int last = 0;
		char[] input = in.toCharArray();
		int len = input.length;
		StringBuffer out = new StringBuffer((int) (len * 1.3));
		for (; i < len; i++) {
			ch = input[i];
			if (ch > '>') {
				continue;
			} else if (ch == '<') {
				if (i > last) {
					out.append(input, last, i - last);
				}
				last = i + 1;
				out.append(LT_ENCODE);
			} else if (ch == '>') {
				if (i > last) {
					out.append(input, last, i - last);
				}
				last = i + 1;
				out.append(GT_ENCODE);
			}
		}
		if (last == 0) {
			return in;
		}
		if (i > last) {
			out.append(input, last, i - last);
		}
		return out.toString();
	}

	/**
	 * 
	 * 替换一个字符串中的不能在XML中使用的字符
	 * 
	 * @param string
	 *            the string to escape.
	 * @return the string with appropriate characters escaped.
	 */
	public static final String escapeForXML(String string) {
		if (string == null) {
			return null;
		}
		char ch;
		int i = 0;
		int last = 0;
		char[] input = string.toCharArray();
		int len = input.length;
		StringBuffer out = new StringBuffer((int) (len * 1.3));
		for (; i < len; i++) {
			ch = input[i];
			if (ch > '>') {
				continue;
			} else if (ch == '<') {
				if (i > last) {
					out.append(input, last, i - last);
				}
				last = i + 1;
				out.append(LT_ENCODE);
			} else if (ch == '&') {
				if (i > last) {
					out.append(input, last, i - last);
				}
				last = i + 1;
				out.append(AMP_ENCODE);
			} else if (ch == '"') {
				if (i > last) {
					out.append(input, last, i - last);
				}
				last = i + 1;
				out.append(QUOTE_ENCODE);
			}
		}
		if (last == 0) {
			return string;
		}
		if (i > last) {
			out.append(input, last, i - last);
		}
		return out.toString();
	}

	public static final Map getParamentersMap(String pars) {
		String[] cons = pars.split("&");
		Map result = new HashMap();
		for (int i = 0; i < cons.length; i++) {
			String string = cons[i];
			int index = string.indexOf("=");
			if (index < 0) {
				continue;
			}

			String splitStr[] = new String[2];
			splitStr[0] = string.substring(0, index);
			splitStr[1] = string.substring(index + 1);
			if (splitStr.length == 2) {
				String tempkey = splitStr[0];
				result.put(tempkey, tempkey);
			}
		}
		return result;
	}

	/**
	 * 还原一个XML避免序列到正常的字符
	 * 
	 * @param string
	 *            the string to unescape.
	 * @return the string with appropriate characters unescaped.
	 */
	public static final String unescapeFromXMLOrHTML(String string) {
		string = replaceAll(string, "&lt;", "<");
		string = replaceAll(string, "&gt;", ">");
		string = replaceAll(string, "&quot;", "\"");
		return replaceAll(string, "&amp;", "&");
	}

	/**
	 * 读取一个文本文件的所有内容
	 * 
	 * @param filename
	 * @return
	 */
	public static final StringBuffer readTextFile(String filename,
			String charcode) {
		StringBuffer sb = new StringBuffer();
		List list = readTextFileToList(filename, charcode);
		for (Iterator iter = list.iterator(); iter.hasNext();) {
			sb.append("\n");
			sb.append(iter.next());

		}
		return sb;
	}

	/**
	 * 读取一个文本文件的所有内容到一个List中去
	 * 
	 * @param filename
	 * @return
	 */
	public static final List readTextFileToList(String filename, String charcode) {
		List result = new ArrayList();
		InputStreamReader read = null;
		try {
			FileInputStream inputStream = new FileInputStream(filename);
			read = new InputStreamReader(inputStream, charcode);
			BufferedReader reader = new BufferedReader(read);

			String line;

			while ((line = reader.readLine()) != null) {
				result.add(line);
			}
			reader.close();
			read.close();
			inputStream.close();

		} catch (UnsupportedEncodingException e) {
			// TODO 自动生成 catch 块
			e.printStackTrace();
		} catch (FileNotFoundException e) {
			// TODO 自动生成 catch 块
			e.printStackTrace();
		} catch (IOException e) {
			// TODO 自动生成 catch 块
			e.printStackTrace();
		} finally {
			try {
				read.close();
			} catch (IOException e) {
				// TODO 自动生成 catch 块
				e.printStackTrace();
			}
		}

		return result;
	}

	/**
	 * 将一段字符串存入到指定路径中去
	 * 
	 * @param script
	 *            字符串
	 * @param filePath
	 *            文本文件路径
	 */
	public static final void saveTextFile(StringBuffer script, String filePath) {
		FileWriter writer = null;
		PrintWriter pw = null;
		try {
			writer = new FileWriter(filePath);
			pw = new PrintWriter(writer);
			pw.print(script);
		} catch (IOException iox) {
			System.err.println(iox);
		} finally {
			try {
				pw.flush();
				pw.close();
				writer.close();
			} catch (IOException iox) {
				System.err.println(iox);
			}
		}

	}

	/**
	 * 编码并压缩方法
	 * @param str
	 * @return
	 * @throws IOException
	 */
	public static String compress(String str) throws IOException {
		if (str == null || str.length() == 0) {
			return str;
		}
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		GZIPOutputStream gzip = new GZIPOutputStream(out);
		gzip.write(str.getBytes());
		gzip.close();
		return new sun.misc.BASE64Encoder().encode(out.toByteArray());
	}
	
	/**
	 * 解压缩和解码方法
	 * @param str
	 * @return
	 * @throws IOException
	 */
	public static String decompress(String str) throws IOException {
		if (str == null || str.length() == 0) {
			return str;
		}
		byte[] bt = new sun.misc.BASE64Decoder().decodeBuffer(str);
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		ByteArrayInputStream in = new ByteArrayInputStream(bt);
		GZIPInputStream gunzip = new GZIPInputStream(in);
		byte[] buffer = new byte[256];
		int n;
		while ((n = gunzip.read(buffer)) >= 0) {
			out.write(buffer, 0, n);
		}
		return out.toString();
	}
	
	// 当前月第一天
    public static String getCurrentMonthFirstDay(Date date) {
        TimeZone tz = TimeZone.getTimeZone("Etc/GMT-8");
        TimeZone.setDefault(tz);
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM");
        String startDate = sd.format(date) + "-01";
        return startDate;
    }
	
	 // by liuxl
    public static String getPreDay(Date date, String flag) throws ParseException {
        TimeZone tz = TimeZone.getTimeZone("Etc/GMT-8");
        TimeZone.setDefault(tz);
        String startDate = getCurrentMonthFirstDay(date);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = format.format(date);
        Calendar c = Calendar.getInstance(tz);
        c.setTime(format.parse(currentDate));
        int day = c.get(Calendar.DATE);
        if ("before".equalsIgnoreCase(flag))
            c.set(Calendar.DATE, day - 1);
        else if ("after".equalsIgnoreCase(flag))
            c.set(Calendar.DATE, day + 1);
        Date endDate = c.getTime();
        String resultDate = format.format(endDate);
        return resultDate;
    }
    
	public static String getString(Object obj) {
		return ((isNotBlank(obj)) ? obj.toString() : "");
	}

	public static void main(String[] args) {
		Map h = new java.util.HashMap();
		h.put("0", "1111");
		h.put("1", "3333");
		String str = StrUtils.replaceAll("#{0}#ffff+#{0}#+#{1}+adsfasdfa", h,
				"#{", "}#");
		System.out.println(str);
	}
}
