package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTXLRBYTOYX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通线路由热备用转运行".equals(tempStr)){
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());

			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				
				if(xlkgList.size() == 2){
					for(PowerDevice dev : xlkgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对线路充电/r/n";
							}
						}
					}
					
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
							}
						}
					}
				}else{
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对线路充电/r/n";
						}
					}
				}
			}	
			
			List<String> stationNameList = new ArrayList<String>();
			
			String sql = "SELECT B.ID,B.UNIT,B.LOWERUNIT,B.SWITCH_NAME,B.DISCONNECTOR_NAME FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER B WHERE B.LINE_ID = (SELECT A.ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND A WHERE A.ID = '"+curDev.getPowerDeviceID()+"')";
			List<Map<String, String>> list = DBManager.queryForList(sql);
		    
			for(Map<String,String> map : list){
				String unit = StringUtils.ObjToString(map.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				String switchname = StringUtils.ObjToString(map.get("SWITCH_NAME"));
				String disconnectorname = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));

				if(!lowerunit.equals("")){
					stationNameList.add(lowerunit);
					
					if(!switchname.equals("")){
						replaceStr += unit+"@合上"+lowerunit+switchname+"/r/n";
					}
				}else{
					stationNameList.add(StringUtils.ObjToString(map.get("UNIT")));
					replaceStr += unit+"@合上"+switchname+"/r/n";
				}
			}
			
			String volt = (int)curDev.getPowerVoltGrade()+"kV";
			PowerDevice sourceStation = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());

			List<PowerDevice> stationList = new ArrayList<PowerDevice>();
			
			stationList.add(sourceStation);
			
			for(PowerDevice dev : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				stationList.add(station);
			}
			
			for(PowerDevice loadLineTran : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				
				if(!stationNameList.contains(stationName)){
					List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

					if(xlkgList.size() == 2){
						for(PowerDevice dev : xlkgList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
								}
							}
						}
						
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
								}
							}
						}
					}else{
						xlkgList.clear();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
							
							if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									mlkgList.add(dev);
								}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									xlkgList.add(dev);
								}
							}
						}
						
						boolean ishh = false;
						
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(xlkgList);
						tempList.addAll(mlkgList);
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								ishh = true;
							}
						}
						
						if(ishh){
							boolean isMxFd = false;
							
							for(PowerDevice dev : mxList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									isMxFd = true;
								}
							}
							
							for(PowerDevice dev : xlkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									if(isMxFd){
										String deviceName = CZPService.getService().getDevName(dev);
										replaceStr += CommonFunction.getControlContentZT(stationName, "用", deviceName , "对母线充电", "昭通地调");
									}else{
										replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
									}
								}
							}
							
							for(PowerDevice dev : tempList){
								if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
									replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
								}
							}
						}else{
							for(PowerDevice dev : xlkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += CommonFunction.getControlContentZT(stationName, "合上", deviceName , "", "昭通地调");
								}
							}
						}
					}
				}
			}
			
			replaceStr += CommonFunction.getLineBztResult(stationList,volt,"投入");
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
