package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEZYBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱站用变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			String sql = "SELECT ZYB_DEVID,ZYB_NAME,ZYB_DZNAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+stationDev.getPowerDeviceID()+"'";
			
			List<Map<String,String>> zybidList = DBManager.queryForList(sql);
			
			for(Map<String,String> zybidMap : zybidList){
				String zybid = StringUtils.ObjToString(zybidMap.get("ZYB_DEVID"));
				String zybName = StringUtils.ObjToString(zybidMap.get("ZYB_NAME"));
				String zybdzName = StringUtils.ObjToString(zybidMap.get("ZYB_DZNAME"));

				if(!zybdzName.equals("")){
					replaceStr += stationName+"@核实"+zybName+"具备停电条件/r/n";
					
					if(zybdzName.contains("隔离开关")){
						replaceStr += "普洱地调@遥控拉开"+stationName+zybdzName+"/r/n";
						
						if(station.getPowerVoltGrade() >= 220){
							replaceStr += "普洱地调@核实"+stationName+zybdzName+"处于拉开位置/r/n";
						}else{
							replaceStr += stationName+"@核实"+zybdzName+"处于拉开位置/r/n";
						}
					}else{
						replaceStr += "普洱地调@遥控断开"+stationName+zybdzName+"/r/n";
						
					}
				}else{
					
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
