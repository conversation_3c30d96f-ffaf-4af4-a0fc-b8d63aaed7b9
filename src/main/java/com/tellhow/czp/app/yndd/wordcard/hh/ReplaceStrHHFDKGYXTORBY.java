package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.SwitchJoinExecute;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHFDKGYXTORBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河分段开关由运行转热备用".equals(tempStr)){
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			CommonFunctionHH cf = new CommonFunctionHH();
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			String stationName = CZPService.getService().getDevName(station);
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					mlkgList.add(dev);
				}
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
						jdzybkgList.add(dev);
					}
				}
			}
			
			List<PowerDevice> otherMlkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice mlkg : mlkgList){
				if(station.getPowerVoltGrade() > mlkg.getPowerVoltGrade() && mlkg.getPowerDeviceID()!=curDev.getPowerDeviceID()){
					otherMlkgList.add(mlkg);
				}
			}
			
			List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
			
			tempList.addAll(otherMlkgList);
			tempList.add(curDev);

			RuleExeUtil.swapDeviceList(tempList);
			
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",false, true, true, false);
			List<PowerDevice> kgdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			
			for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
				PowerDevice dev = (PowerDevice)it2.next();
				if(kgdzList.contains(dev))
					it2.remove();
			}
			
			if(SwitchJoinExecute.result.equals("仅操作当前断路器")){
				replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
				replaceStr += "核实"+CZPService.getService().getDevName(curDev)+"热备用/r/n";
			}else{
				if(station.getPowerVoltGrade() == 220){
					replaceStr += "核实主变中性点切换装置功能已按现场规程调整好/r/n";
					
					for(PowerDevice dev : zbList){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

						for(PowerDevice gd : gdList){
							if(gd.getDeviceStatus().equals("1")){
								replaceStr += "投入"+CZPService.getService().getDevName(dev)+"高、中压侧中性点及其零序保护/r/n";
								break;
							}
						}
					}
				}
				
				replaceStr += cf.getYcDkStrReplace(tempList, stationName);
				replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"热备用/r/n";
				
				for(PowerDevice otherMlkg : otherMlkgList){
					if(RuleExeUtil.getDeviceEndStatus(otherMlkg).equals("1")){
						replaceStr += "投入"+(int)otherMlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
						replaceStr += "投入"+CZPService.getService().getDevName(zbList)+(int)otherMlkg.getPowerVoltGrade()+"kV侧后备保护动作闭锁"+(int)otherMlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}

				if(jdzybkgList.size()>0){
					for(PowerDevice dev : mlkgList){
						if(dev.getPowerVoltGrade()==10){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += "投入10kV#X接地变小电阻自投切功能/r/n";
							}
						}
					}
				}
			}
		}
		return replaceStr;
	}

}