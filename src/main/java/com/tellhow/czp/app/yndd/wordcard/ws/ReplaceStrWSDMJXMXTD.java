package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionWS;
import com.tellhow.graphicframework.constants.SystemConstants;

import com.tellhow.uitl.StringUtils;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSDMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山单母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

			List<PowerDevice> kgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, false, true);

			boolean isSwitchControl = true;
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}	
			
			if(mlkgList.size()>0){//分段
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, false, true);
					
					if(isSwitchControl&&isSwitchSeparateControl){
						replaceStr += stationName+"@确认"+deviceName+"具备停电条件/r/n";
						String dzName = "";
						List<PowerDevice> ptdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, "", true, true, true, true);
						if (!ptdzList.isEmpty()) {
							for (PowerDevice dev : ptdzList) {
								String dzNum=CZPService.getService().getDevNum(dev);
								String dzNameTemp = CZPService.getService().getDevName(dev);
								if (dzNameTemp.contains("PT")) {
									dzNameTemp=dzNameTemp.replace("PT"+dzNum, "电压互感器" +"PT"+ dzNum);
								}else{
									dzNameTemp=dzNameTemp.replace(dzNum, "电压互感器" + dzNum);
								}
								if (CommonFunctionWS.ifSwitchSeparateControl(dev)) {
									replaceStr+="文山地调@遥控拉开"+stationName+dzNameTemp+"/r/n";
									replaceStr+=stationName+"@确认"+dzNameTemp+"在分闸位置/r/n";
								}else{
									dzName+=dzNameTemp+"、";
								}
							}
							if (StringUtils.isNotEmpty(dzName)){
								dzName = dzName.substring(0, dzName.length()-1);
								replaceStr += stationName+"@将"+dzName+"由运行转冷备用/r/n";
							}
						}else{
							replaceStr += stationName+"@将"+deviceName+"电压互感器由运行转冷备用/r/n";
						}
						replaceStr += "文山地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
						
						for(PowerDevice dev : kgList){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList , stationName);
							}
						}
						
						if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
							for(PowerDevice dev : kgList){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList , stationName);
								}
							}
						}else{
							for(PowerDevice dev : kgList){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList , stationName);
								}
							}
						}
						
						for(PowerDevice dev : kgList){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList , stationName);
							}
						}
					}else{
						for(PowerDevice dev : zbList){
							List<PowerDevice> gycswList = RuleExeUtil.getTransformerSwitchHigh(dev);
							List<PowerDevice> zycswList = RuleExeUtil.getTransformerSwitchMiddle(dev);
							List<PowerDevice> dycswList = RuleExeUtil.getTransformerSwitchLow(dev);
							
							for(PowerDevice dycsw : dycswList){
								List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();

								if(!dycsw.getPowerDeviceID().equals("")){
									dycmlkgList =  RuleExeUtil.getDeviceList(dycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
								
									for(PowerDevice dycmlkg : dycmlkgList){
										if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("0")){
											replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
										}
									}
								}
								
								if(RuleExeUtil.getDeviceBeginStatus(dycsw).equals("0")){
									replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dycsw)+"/r/n";
								}
							}
							
							for(PowerDevice zycsw : zycswList){
								List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();

								if(!zycsw.getPowerDeviceID().equals("")){
									zycmlkgList =  RuleExeUtil.getDeviceList(zycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
								
									for(PowerDevice zycmlkg : zycmlkgList){
										if(RuleExeUtil.getDeviceEndStatus(zycmlkg).equals("0")){
											replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(zycmlkg)+"/r/n";
										}
									}
								}
								
								if(RuleExeUtil.getDeviceBeginStatus(zycsw).equals("0")){
									replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(zycsw)+"/r/n";
								}
							}
							
							for(PowerDevice gycsw : gycswList){
								if(RuleExeUtil.getDeviceBeginStatus(gycsw).equals("0")){
									replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(gycsw)+"/r/n";
								}
							}
						}
						
						if(curDev.getPowerVoltGrade() == 10){
							replaceStr += "文山配调@确认"+stationName+deviceName+"具备停电条件/r/n";
						}else{
							replaceStr += stationName+"@确认"+deviceName+"具备停电条件/r/n";
						}
					
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
						
						for(PowerDevice dev : mlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
						
						if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
						}
					}
				}else{
					List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
					
					replaceStr += stationName+"@确认"+deviceName+"具备停电条件/r/n";
					
					for(PowerDevice dev : zbkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
					}
				}
			}else{
				List<PowerDevice> zbList = new ArrayList<PowerDevice>();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						if(!dev.getPowerDeviceName().contains("接地变")){
							zbList.add(dev);
						}
					}
				}
				
				RuleExeUtil.swapDeviceList(zbList);
				
				replaceStr += stationName+"@确认"+deviceName+"具备停电条件/r/n";
				String dzName = "";
				List<PowerDevice> ptdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, "", true, true, true, true);
				if (!ptdzList.isEmpty()) {
					for (PowerDevice dev : ptdzList) {
						String dzNum=CZPService.getService().getDevNum(dev);
						String dzNameTemp = CZPService.getService().getDevName(dev);
						if (dzNameTemp.contains("PT")) {
							dzNameTemp=dzNameTemp.replace("PT"+dzNum, "电压互感器" +"PT"+ dzNum);
						}else{
							dzNameTemp=dzNameTemp.replace(dzNum, "电压互感器" + dzNum);
						}
						if (CommonFunctionWS.ifSwitchSeparateControl(dev)) {
							replaceStr+="文山地调@遥控拉开"+stationName+dzNameTemp+"/r/n";
							replaceStr+=stationName+"@确认"+dzNameTemp+"在分闸位置/r/n";
						}else{
							dzName+=dzNameTemp+"、";
						}
					}
					if (StringUtils.isNotEmpty(dzName)){
						dzName = dzName.substring(0, dzName.length()-1);
						replaceStr += stationName+"@将"+dzName+"由运行转冷备用/r/n";
					}
				}else{
					replaceStr += stationName+"@将"+deviceName+"电压互感器由运行转冷备用/r/n";
				}
				
				if(isSwitchControl&&isSwitchSeparateControl){
					replaceStr += "文山地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
					
					for(PowerDevice dev : kgList){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList , stationName);
						}
					}
					
					if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
						for(PowerDevice dev : kgList){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList , stationName);
							}
						}
					}else{
						for(PowerDevice dev : kgList){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList , stationName);
							}
						}
					}
				}else{
					for(PowerDevice dev : zbList){
						List<PowerDevice> gycswList = RuleExeUtil.getTransformerSwitchHigh(dev);
						List<PowerDevice> zycswList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						List<PowerDevice> dycswList = RuleExeUtil.getTransformerSwitchLow(dev);
						
						for(PowerDevice dycsw : dycswList){
							if(RuleExeUtil.getDeviceBeginStatus(dycsw).equals("0")){
								replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dycsw)+"/r/n";
							}
						}
						
						for(PowerDevice zycsw : zycswList){
							if(RuleExeUtil.getDeviceBeginStatus(zycsw).equals("0")){
								replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(zycsw)+"/r/n";
							}
						}
						
						for(PowerDevice gycsw : gycswList){
							if(RuleExeUtil.getDeviceBeginStatus(gycsw).equals("0")){
								replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(gycsw)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
