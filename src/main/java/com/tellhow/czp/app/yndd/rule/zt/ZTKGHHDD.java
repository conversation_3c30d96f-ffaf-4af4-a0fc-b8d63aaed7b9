package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;

public class ZTKGHHDD implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		
		List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		
		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()&&dev.getDeviceStatus().equals("1")){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					rbykgList.add(dev);
				}
			}
		}
		
		if(rbykgList.size() > 1){
			EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, rbykgList , "请选择需要合上的断路器：");
			List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

			if(ecc.isCancel()){
				return false;
			}
			
			for(PowerDevice dev : choosedyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
		}else if(rbykgList.size() == 1){
			RuleExeUtil.deviceStatusExecute(rbykgList.get(0), rbykgList.get(0).getDeviceStatus(), "0");
		}else{
			return false;
		}
		
		RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");

		return true;
	}
}
