package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHLS  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("合环落实".equals(tempStr)){//只用于复电
			//先做110的，其他等级没有典型票
			if(stationDev.getDeviceType().equals(SystemConstants.InOutLine)&&stationDev.getPowerVoltGrade()==110){
				List<PowerDevice> xllist = RuleExeUtil.getLineAllSideList(stationDev);
				PowerDevice offswDev = null;
				for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
					DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
					PowerDevice dev = dtd.getTransDevice();
					if(dev.getPowerVoltGrade()==stationDev.getPowerVoltGrade()&&dtd.getEndstate().equals("1")&&dtd.getBeginstatus().equals("0")
							&&(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
									||(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&!RuleExeUtil.isSwitchDoubleML(dev)))){
						offswDev =dev;
						break;
					}
				}
				if(offswDev!=null){
					if(offswDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){//分段开关
						PowerDevice xlPD = null;
						for(PowerDevice xl:xllist){
							if(xl.getPowerStationID().equals(offswDev.getPowerStationID())){
								xlPD=xl;
								break;
							}
						}
						if(xlPD!=null){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(xlPD, SystemConstants.MotherLine, SystemConstants.PowerTransformer,
									true, true, true);
							if(mxList.size()>0){
								List<PowerDevice> xlList = RuleExeUtil.getDeviceList(offswDev,mxList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer, 
										"", "", false, false, false, true);
								if(xlList.size()>0){
									List<PowerDevice> allxlList = RuleExeUtil.getLineAllSideList(xlList.get(0));
									List<PowerDevice> allxlList2 = RuleExeUtil.getLineAllSideList(xlPD);
									RuleExeUtil.swapLowDeviceList(allxlList);
									RuleExeUtil.swapLowDeviceList(allxlList2);
									if(allxlList.size()>0&&allxlList2.size()>0&&!allxlList.get(0).equals(allxlList2.get(0))){
										replaceStr="云南省调@落实"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(allxlList2.get(0).getPowerStationID()))
												+"220kV系统与"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(allxlList.get(0).getPowerStationID()))
												+"220kV系统联络运行/r/n"+replaceStr;
									}
								}
							}
						}
						
					}else{//线路开关
						if(offswDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
								&&offswDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母线路开关
							List<PowerDevice> xlDevices = RuleExeUtil.getDeviceList(offswDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,
									true, true, true);
							if(xlDevices.size()>0){
								PowerDevice dev2 = null;
								for(PowerDevice xl:xllist){
									PowerDevice xlswDevice =RuleExeUtil.getDeviceSwitch(xl);
									if(xlswDevice!=null&&xlswDevice.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)&&!xl.equals(xlDevices.get(0))){
										dev2=xl;
										break;
									}
								}
								if(dev2!=null){
									replaceStr="云南省调@落实"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(xlDevices.get(0).getPowerStationID()))
											+"220kV系统与"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(dev2.getPowerStationID()))
											+"220kV系统联络运行/r/n"+replaceStr;
								}
							}
						}
					}
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
