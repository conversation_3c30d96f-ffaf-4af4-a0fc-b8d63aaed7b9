package com.tellhow.czp.app.yndd.wordcard.km;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooOTHER implements TempBooleanReplace {
	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		
		ReplaceBooNQ boonq = new ReplaceBooNQ();
		ReplaceBooKDNQ bookdnq = new ReplaceBooKDNQ();
		ReplaceBooXBZ booxbz = new ReplaceBooXBZ();
		
		if(!boonq.strReplace(tempStr, curDev, stationDev, desc)
				&&!bookdnq.strReplace(tempStr, curDev, stationDev, desc)
				&&!booxbz.strReplace(tempStr, curDev, stationDev, desc)){
			return true;
		}
		
		return false;
	}
		
}
