package com.tellhow.czp.app.yndd.wordcard.bs;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSPTTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山电压互感器停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev)+"电压互感器";

			String maintenanceName = CommonFunctionBS.getMaintenance(stationName);
			
			if(stationName.equals(maintenanceName)){
				replaceStr += maintenanceName+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
			}else{
				replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
			}
			
			replaceStr += "保山地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
			
			if(stationName.equals(maintenanceName)){
				replaceStr += maintenanceName+"@核实"+deviceName+"一、二次设备无异常/r/n";
			}else{
				replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备无异常/r/n";
			}
		}
		
		return replaceStr;
	}

}
