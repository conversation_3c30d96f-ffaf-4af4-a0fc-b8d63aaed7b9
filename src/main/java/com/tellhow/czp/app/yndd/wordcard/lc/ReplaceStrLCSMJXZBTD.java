package com.tellhow.czp.app.yndd.wordcard.lc;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.lc.LCTicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLC;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLCSMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("临沧双母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getTransformerKnifeLoad(curDev);

			double midvolt = RuleExeUtil.getTransformerVolByType(curDev, "middle");
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdzList){
				if(dev.getPowerVoltGrade() == lowvolt){
					zbdycdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}		
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}
			}
			
//			replaceStr += CommonFunctionLC.getMotherLineTdContent(dycmxList, stationName);
			String isControl = CommonFunctionLC.ifDeviceControlLC(curDev);

			if(isControl.equals("全部可控")){
				replaceStr += "临沧地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
				
				replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(zbdyckgList,stationName);
				
				for(PowerDevice dev : zbdycdzList){
					replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(dev,stationName);
				}
				
				replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(zbzyckgList,stationName);
				replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(zbgyckgList,stationName);
			}else{
				for(PowerDevice dev : zxdjddzList){
					String dzName = CZPService.getService().getDevName(dev);
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += stationName+"@核实"+dzName+"电机电源、操作电源已投入/r/n";
						replaceStr += "临沧地调@遥控合上"+stationName+dzName+"/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += stationName+"@核实"+dzName+"在合闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : otherzxdjddzList){
					String dzName = CZPService.getService().getDevName(dev);
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "临沧地调@遥控合上"+stationName+dzName+"/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += stationName+"@核实"+dzName+"在合闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += "临沧地调@核实"+stationName+CZPService.getService().getDevName(dev)+"在合闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionLC.getHhContent(dev, "临沧地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "临沧地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += "临沧地调@核实"+stationName+CZPService.getService().getDevName(dev)+"在合闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "临沧地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "临沧地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
//				replaceStr += stationName+"@将"+deviceName+"保护定值区由XX区调整至XX区/r/n";
				
				if(curDev.getDeviceStatus().equals("2")){
					if(isControl.equals("部分可控")){
						for(PowerDevice dev : zbzyckgList){
							if(CommonFunctionLC.ifSwitchSeparateControlLC(dev)){
								replaceStr += "临沧地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(zbzyckgList,stationName);
							}
						}
						
						for(PowerDevice dev : zbgyckgList){
							if(CommonFunctionLC.ifSwitchSeparateControlLC(dev)){
								replaceStr += "临沧地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(zbgyckgList,stationName);
							}
						}
						
						replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
					}else{
//						replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
						
						List<PowerDevice> dycglkg = new ArrayList<PowerDevice>();
						List<PowerDevice> zycglkg = new ArrayList<PowerDevice>();
						List<PowerDevice> gycglkg = new ArrayList<PowerDevice>();

						
						for(PowerDevice dev : zbdyckgList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "1", "2")){
								replaceStr += "临沧地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								dycglkg = RuleUtil.getDirectDevice(dev, SystemConstants.SwitchSeparate);
							}
						}
						for(PowerDevice dev : dycglkg){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += stationName+"@核实"+stationName+CZPService.getService().getDevName(dev)+"在拉开位置/r/n";
							}
						}
			
						for(PowerDevice dev : zbzyckgList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "1", "2")){
								replaceStr += "临沧地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								zycglkg = RuleUtil.getDirectDevice(dev, SystemConstants.SwitchSeparate);
							}
						}
						for(PowerDevice dev : zycglkg){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += stationName+"@核实"+stationName+CZPService.getService().getDevName(dev)+"在拉开位置/r/n";
							}
						}
						for(PowerDevice dev : zbgyckgList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "1", "2")){
								replaceStr += "临沧地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								gycglkg = RuleUtil.getDirectDevice(dev, SystemConstants.SwitchSeparate);
							}
						}
						for(PowerDevice dev : gycglkg){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += stationName+"@核实"+stationName+CZPService.getService().getDevName(dev)+"在拉开位置/r/n";
							}
						}
					}
				}
				
				for(PowerDevice dev : zxdjddzList){
					String dzName = CZPService.getService().getDevName(dev);
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += stationName+"@核实"+dzName+"电机电源、操作电源已投入/r/n";
						replaceStr += "临沧地调@遥控拉开"+stationName+dzName+"/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
}
