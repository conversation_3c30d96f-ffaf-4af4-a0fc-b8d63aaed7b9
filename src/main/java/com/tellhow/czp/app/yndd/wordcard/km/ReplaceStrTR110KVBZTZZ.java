package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTR110KVBZTZZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("投入110kV备自投装置".equals(tempStr)){
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&RuleExeUtil.getDeviceEndStatus(dev).equals("1")
						&&!dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					if(dev.getPowerVoltGrade() == 110){
						dycmlkgList.add(dev);
					}
				}
			}
			
			if(dycmlkgList.size()>1){//多段母线
				for(PowerDevice dycmlkg : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
						replaceStr += CZPService.getService().getDevName(station)+"@投入"+CZPService.getService().getDevName(dycmlkg)+"备自投装置/r/n";
					}
				}
			}else if(dycmlkgList.size()==1){
				replaceStr += CZPService.getService().getDevName(station)+"@投入110kV备自投装置/r/n";
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
