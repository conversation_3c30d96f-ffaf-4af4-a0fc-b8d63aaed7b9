package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXMXTDDD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪母线停电倒电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC,"", false, true, true, true);
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML,"", false, true, true, true);
			
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());

			for(Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
			
			replaceStr += CommonFunction.getZybDrCheckContent(zbkgList);
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			if(curDev.getPowerVoltGrade() == 35 || curDev.getPowerVoltGrade() == 10){
				replaceStr += "玉溪配调@核实"+stationName+deviceName+"可以短时停电倒电/r/n";
			}
			
			String sourcezbname = "";
			String loadzbname = "";
			
			for(PowerDevice dev : zbkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					List<PowerDevice> curzbList = RuleExeUtil.getDeviceList(dev,SystemConstants.PowerTransformer, SystemConstants.MotherLine, false, true, true);
					loadzbname = CZPService.getService().getDevName(curzbList);
					
					for(PowerDevice zb : zbList){
						if(!curzbList.contains(zb)){
							sourcezbname = CZPService.getService().getDevName(zb);
							break;
						}
					}
				}else{
					List<PowerDevice> otherzbList = RuleExeUtil.getDeviceList(dev,SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);
					sourcezbname = CZPService.getService().getDevName(otherzbList);
					
					for(PowerDevice zb : zbList){
						if(!otherzbList.contains(zb)){
							loadzbname = CZPService.getService().getDevName(zb);
							break;
						}
					}
				}
			}
			
			replaceStr += "玉溪地调@执行"+stationName+deviceName+"由"+sourcezbname+"供电停电倒由"+loadzbname+"供电程序操作/r/n";
			
			if(curDev.getPowerVoltGrade() == 35 || curDev.getPowerVoltGrade() == 10){
				replaceStr += "玉溪配调@通知"+stationName+deviceName+"短时停电倒电完毕/r/n";
			}
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += stationName+"@按当前运行方式投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
		}
		return replaceStr;
	}
	
}
