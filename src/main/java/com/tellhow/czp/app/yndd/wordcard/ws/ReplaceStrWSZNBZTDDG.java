package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSZNBZTDDG  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山站内备自投调电高".equals(tempStr)){
			List<PowerDevice> otherlineList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherxlkglist = new ArrayList<PowerDevice>();
			
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.Switch, true, true, true);

			for(PowerDevice dev : lineList){
				otherlineList = RuleExeUtil.getLineOtherSideList(dev);
			}
			
			for(PowerDevice dev : otherlineList){
				otherxlkglist = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			}
			
			for(PowerDevice xlkg : otherxlkglist){
				PowerDevice station = CBSystemConstants.getPowerStation(xlkg.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 
				
				if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
					replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkg)+"/r/n";
				}
			}
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV备自投动作成功/r/n";
			replaceStr += "文山地调@确认"+stationName+CZPService.getService().getDevName(curDev)+"在热备用/r/n";

			for(PowerDevice xlkg : otherxlkglist){
				PowerDevice station1 = CBSystemConstants.getPowerStation(xlkg.getPowerStationID());
				String stationName1 = CZPService.getService().getDevName(station1); 
				
				if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
					replaceStr += "文山地调@遥控合上"+stationName1+CZPService.getService().getDevName(xlkg)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
