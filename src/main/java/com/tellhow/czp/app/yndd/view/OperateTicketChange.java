/*
 * OperateTicketMLPLoad.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.swing.DefaultComboBoxModel;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;


import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.user.User;
import com.tellhow.czp.user.UserDao;
import com.tellhow.graphicframework.utils.DateUtil;

import czprule.datemodule.JCalendarPanel;
import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

import javax.swing.JLabel;
import javax.swing.GroupLayout;
import javax.swing.GroupLayout.Alignment;
import javax.swing.LayoutStyle.ComponentPlacement;
import javax.swing.JComboBox;
import javax.swing.JTextField;

/**
 * 
 * <AUTHOR>
 *
 */
public class OperateTicketChange extends javax.swing.JDialog {
	private CodeNameModel cnm = null; //返回结果
	private List<String> mxList = new ArrayList<String>(); //返回操作内容结果
	private List<String> mxsList = new ArrayList<String>(); //返回操作内容结果
	private String cardType = ""; //0：典型票 1：正常票
	DefaultTableModel tableModel;
	private SetJTableProtery sjp = new SetJTableProtery();
	
	public static void main(String args[]) {
		OperateTicketChange aa = new OperateTicketChange(null,false);
		aa.setVisible(true);
	}

	/** Creates new form OperateTicketMLPLoad */
	public OperateTicketChange(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		this.setTitle("调度票转监控票");
		initComponents();
		setLocationCenter();
		//初始化起始时间
		Date today = new Date();
		DateUtil dateUtil = new DateUtil();
		jTextField1.setText(dateUtil.getHistoryTime(today.getDate() - 1,"yyyy-MM-dd"));
		jTextField2.setText(dateUtil.getCurTime("yyyy-MM-dd"));
		initMLPLoad();
		initMXLoad();
	}

	//返回选择操作票
	public CodeNameModel getMLP() {
		return this.cnm;
	}
	
	//返回选择操作票内容
	public List<String> getMX() {
		return this.mxList;
	}
	public List<String> getMXS() {
		return this.mxsList;
	}

	/**
	 * @屏幕中央显示
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	//实现查询按钮
	protected void jButton1ActionPerformed(ActionEvent e) {
		initMLPLoad();
	}

	//结束时间
	private void endTimeAction(java.awt.event.MouseEvent evt) {
		// TODO add your handling code here:
		String selectTime = "";
		if (evt.getButton() == 1 && evt.getClickCount() == 1) {
			JCalendarPanel calendarPanel = new JCalendarPanel(
					this.getLocation().x + this.jTextField2.getX() + 12,
					this.getLocation().y - 47);
			selectTime = calendarPanel.getDateStr();
		}
		if (!selectTime.equals("")) {
			String[] beginTime = selectTime.split(" ");
			jTextField2.setText(beginTime[0]);
		}
	}

	//起始时间
	private void beginTimeAction(java.awt.event.MouseEvent evt) {
		// TODO add your handling code here:
		String selectTime = "";
		if (evt.getButton() == 1 && evt.getClickCount() == 1) {
			JCalendarPanel calendarPanel = new JCalendarPanel(
					this.getLocation().x + this.jTextField1.getX() + 12,
					this.getLocation().y - 47);
			selectTime = calendarPanel.getDateStr();
		}
		if (!selectTime.equals("")) {
			String[] beginTime = selectTime.split(" ");
			jTextField1.setText(beginTime[0]);
		}
	}

	/**
	 * 初始化表格
	 */
	public void initMLPLoad() {
		
		String querynpr = ""; //拟票人
		if (this.jComboBox1.getSelectedItem() != null) {
			querynpr = ((User) this.jComboBox1.getSelectedItem()).getUserID();
		}

		if (tableModel == null) {
			tableModel = new DefaultTableModel(null, new String[] { "序号",
					 "拟票时间" , "操作任务" }) {
				public boolean isCellEditable(int rowIndex, int columnIndex) {
					return false;
				}
			};
		}
		tableModel.setRowCount(0);
		TicketDBManager tdb = new TicketDBManager();
		List<String[]> results = null;
		results = tdb.queryTicketZB(this.jTextField1.getText().trim(),
											this.jTextField2.getText().trim(), 
											this.jTextField3.getText().trim().replace(" ", "%").replace("　", "%"), 
											querynpr, 
											"",
											"0");
		String[] tempStr = null;
		CodeNameModel cnn = null;
		String npsj = "";
		for (int i = 0; i < results.size(); i++) {
			tempStr = results.get(i);

			if ("0".equals(cardType)) {
				cnn = new CodeNameModel(tempStr[0], tempStr[1]);
				npsj = tempStr[3];
			} else {
				cnn = new CodeNameModel(tempStr[0], tempStr[2]);
				npsj = tempStr[4];
			}
			Object[] rowData = { String.valueOf(i + 1),npsj, cnn  };
			tableModel.addRow(rowData);
		}
		jTable1.setModel(tableModel);
		sjp.makeFace(jTable1);
		sjp.getTableHeader(jTable1);//列名居中
		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMaxWidth(50);
		tcm.getColumn(0).setMinWidth(50);
		tcm.getColumn(1).setMaxWidth(140);
		tcm.getColumn(1).setMinWidth(140);
		//tcm.getColumn(2).setMinWidth(145);
	}
	
	public void initMXLoad() {
		DefaultTableModel jtableModel2 = new DefaultTableModel(null,
				new String[] { "序号", "操作单位", "操作内容" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		
		jTable2.setRowHeight(26);
		jTable2.setModel(jtableModel2);
		sjp.makeFace(jTable2);
		sjp.getTableHeader(jTable2);//列名居中
		TableColumnModel tcm = jTable2.getColumnModel();
		tcm.getColumn(0).setMaxWidth(50);
		tcm.getColumn(0).setMinWidth(50);
		tcm.getColumn(1).setMaxWidth(140);
		tcm.getColumn(1).setMinWidth(140);
		
	
		TicketDBManager tdb = new TicketDBManager();
		if(jTable1.getSelectedRows().length > 0) {
			CodeNameModel cn = (CodeNameModel) jTable1.getValueAt(jTable1.getSelectedRows()[0], 2);
			List<BaseCardModel> results = tdb.queryTicketMX(cn.getCode());
			BaseCardModel bcm = null;
			for (int i = 0; i < results.size(); i++) {
				bcm = results.get(i);
				CodeNameModel cnn = new CodeNameModel(bcm.getMxid(), String.valueOf(i+1));
				Object[] rowData = { cnn, bcm.getCzsn().equals("")?bcm.getStationName():bcm.getCzsn(),
						bcm.getCardDesc() };
				jtableModel2.addRow(rowData);
			}
			
		}
	}

	/**
	 * @表格鼠标事件
	 * @param me
	 */
	protected void jTable1MouseClicked(MouseEvent me) {

//		if ((SwingUtilities.isLeftMouseButton(me) && me.getClickCount() == 1)) {
//			int[] selectRows = jTable1.getSelectedRows();
//			if (selectRows.length == 0) {
//				return;
//			}
//			cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 2);
//			//OperateTicketMLPLoadMX omx = new OperateTicketMLPLoadMX(this, true);
//			//omx.init(cnm);
//			initMXLoad();
//		}
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			return;
		}
		
		initMXLoad();
	}
	

	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {

		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view("请选择需要导入的记录");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view("只能选择一条导入记录");
			return;
		}
		cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 2);
		this.setVisible(false);
	
		
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {                             //初始化********************************************************************

		jLabel1 = new javax.swing.JLabel();
		jTextField1 = new javax.swing.JTextField();
		jLabel2 = new javax.swing.JLabel();
		jTextField2 = new javax.swing.JTextField();
		jTextField3 = new javax.swing.JTextField();
		jLabel3 = new javax.swing.JLabel();
		jButton1 = new javax.swing.JButton();
		jScrollPane1 = new javax.swing.JScrollPane();
		jScrollPane2 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();
		jTable2 = new javax.swing.JTable();
		jButton4 = new javax.swing.JButton();
		
		this.getContentPane().setBackground(new java.awt.Color(244, 243, 243));
		jScrollPane1.getViewport().setBackground(new java.awt.Color(244, 243, 243));
		jScrollPane2.getViewport().setBackground(new java.awt.Color(244, 243, 243));
		jScrollPane1.setBorder(new TitledBorder(UIManager.getBorder("TitledBorder.border"), "操作票", TitledBorder.LEADING, TitledBorder.TOP, null, new Color(0, 0, 0)));
		jScrollPane2.setBorder(new TitledBorder(UIManager.getBorder("TitledBorder.border"), "操作票明细", TitledBorder.LEADING, TitledBorder.TOP, null, new Color(0, 0, 0)));


		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jLabel1.setText("拟票时间");
		

		jTextField1.setPreferredSize(new java.awt.Dimension(6, 26));
		jTextField1.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				beginTimeAction(evt);
			}
		});

		jLabel2.setText("-");

		jTextField2.setPreferredSize(new java.awt.Dimension(6, 26));
		jTextField2.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				endTimeAction(evt);
			}
		});

		jTextField3.setPreferredSize(new java.awt.Dimension(6, 26));

		jLabel3.setText("\u64cd\u4f5c\u4efb\u52a1\uff1a");

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/find4.gif"))); // NOI18N
		jButton1.setText("搜索");
		jButton1.setToolTipText("搜索");
		jButton1.setMargin(new java.awt.Insets(1,1,1,1));
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});


		jTable1.getSelectionModel().setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
		jTable1.setFont(new java.awt.Font("宋体", 0, 13));
		jTable1.setModel(new javax.swing.table.DefaultTableModel(
				new Object[][] { { null, null, null, null },
						{ null, null, null, null }, { null, null, null, null },
						{ null, null, null, null } }, new String[] { "Title 1",
						"Title 2", "Title 3", "Title 4" }));
		jTable1.setRowHeight(26);
		jTable1.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				jTable1MouseClicked(evt);
			}
		});
		jScrollPane1.setViewportView(jTable1);
		
		
		jTable2.setFont(new java.awt.Font("宋体", 0, 13));
		jTable2.setModel(new javax.swing.table.DefaultTableModel(
				new Object[][] { { null, null, null, null },
						{ null, null, null, null }, { null, null, null, null },
						{ null, null, null, null } }, new String[] { "Title 1",
						"Title 2", "Title 3", "Title 4" }));
		jTable2.setRowHeight(26);
		jScrollPane2.setViewportView(jTable2);

		jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/add.png"))); // NOI18N
		jButton4.setText("生成监控票");
		jButton4.setToolTipText("一键成票");
		jButton4.setMargin(new java.awt.Insets(1,1,1,1));
		jButton4.setFocusPainted(false);
		jButton4.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton4ActionPerformed(evt);
			}
		});
		
		lblNewLabel = new JLabel("拟票人");             //手工开票拟票人#############################################################
		jComboBox1=new JComboBox();
		
		DefaultComboBoxModel dcbUser = new DefaultComboBoxModel();
		UserDao userdao = new UserDao();
		List<User> allusers = userdao.getAllUser("0");
		User user = new User();
		user.setUserID("");
		user.setUserName("请选择");
		dcbUser.addElement(user);
		for (int i = 0; i < allusers.size(); i++) {
			user = allusers.get(i);
			dcbUser.addElement(user);
		}
		this.jComboBox1.setModel(dcbUser);                                         //####################################################################
		


		GroupLayout layout = new GroupLayout(
						getContentPane());
		layout.setHorizontalGroup(
			layout.createParallelGroup(Alignment.LEADING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap()
					.addGroup(layout.createParallelGroup(Alignment.TRAILING)
						.addGroup(layout.createSequentialGroup()
							.addGroup(layout.createParallelGroup(Alignment.LEADING)
								.addComponent(jLabel1)
								.addComponent(jLabel3))
							.addPreferredGap(ComponentPlacement.RELATED)
							.addGroup(layout.createParallelGroup(Alignment.LEADING, false)
								.addGroup(layout.createSequentialGroup()
									.addComponent(jTextField1, GroupLayout.PREFERRED_SIZE, 149, GroupLayout.PREFERRED_SIZE)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jLabel2, GroupLayout.PREFERRED_SIZE, 18, GroupLayout.PREFERRED_SIZE)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jTextField2, GroupLayout.PREFERRED_SIZE, 146, GroupLayout.PREFERRED_SIZE))
								.addComponent(jTextField3, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
							.addGroup(layout.createParallelGroup(Alignment.LEADING)
								.addGroup(layout.createSequentialGroup()
									.addPreferredGap(ComponentPlacement.RELATED, 127, Short.MAX_VALUE)
									.addComponent(jButton1)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jButton4)
									.addGap(11))
								.addGroup(layout.createSequentialGroup()
									.addGap(18)
									.addComponent(lblNewLabel)
									.addPreferredGap(ComponentPlacement.RELATED)
									.addComponent(jComboBox1, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))))
						.addComponent(jScrollPane1, GroupLayout.DEFAULT_SIZE, 817, Short.MAX_VALUE)
						.addComponent(jScrollPane2, GroupLayout.DEFAULT_SIZE, 817, Short.MAX_VALUE))
					.addContainerGap())
		);
		layout.setVerticalGroup(
			layout.createParallelGroup(Alignment.LEADING)
				.addGroup(layout.createSequentialGroup()
					.addContainerGap()
					.addGroup(layout.createParallelGroup(Alignment.TRAILING)
						.addGroup(layout.createSequentialGroup()
							.addGroup(layout.createParallelGroup(Alignment.BASELINE)
								.addComponent(jLabel1)
								.addComponent(jTextField1, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
								.addComponent(jLabel2)
								.addComponent(jTextField2, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
								.addComponent(lblNewLabel)
								.addComponent(jComboBox1, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))
							.addPreferredGap(ComponentPlacement.RELATED)
							.addGroup(layout.createParallelGroup(Alignment.BASELINE)
								.addComponent(jLabel3)
								.addComponent(jTextField3, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)))
						.addComponent(jButton1)
						.addComponent(jButton4))
					.addPreferredGap(ComponentPlacement.RELATED)
					.addComponent(jScrollPane1, GroupLayout.DEFAULT_SIZE, 255, Short.MAX_VALUE)
					.addComponent(jScrollPane2, GroupLayout.DEFAULT_SIZE, 255, Short.MAX_VALUE))
		);
		getContentPane().setLayout(layout);

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton4;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JLabel jLabel3;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JTable jTable1;
	private javax.swing.JTable jTable2;
	private javax.swing.JTextField jTextField1;
	private javax.swing.JTextField jTextField2;
	private javax.swing.JTextField jTextField3;
	private JLabel lblNewLabel;
	private JComboBox jComboBox1;
}