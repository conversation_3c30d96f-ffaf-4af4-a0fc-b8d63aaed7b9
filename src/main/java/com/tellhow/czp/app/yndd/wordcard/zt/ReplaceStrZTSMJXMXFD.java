package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTSMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通双母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			List<PowerDevice> mlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> mxList =  new ArrayList<PowerDevice>();
			mxList.add(curDev);
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					mlkgList.add(dev);
				}
				
				if (dev.getDeviceType().equals(SystemConstants.MotherLine)&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())&&dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						mxList.add(dev);
					}
				}
			}
				
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
			}
			
			for(PowerDevice dev  : mlkgList){
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					replaceStr +="投入"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
					replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr +="退出"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
				}
			}
				
			RuleExeUtil.swapDeviceList(mxList);
			
			replaceStr += "将"+CZPService.getService().getDevName(mxList)+"恢复正常运行方式/r/n";
		}
		
		return replaceStr;
	}

}
