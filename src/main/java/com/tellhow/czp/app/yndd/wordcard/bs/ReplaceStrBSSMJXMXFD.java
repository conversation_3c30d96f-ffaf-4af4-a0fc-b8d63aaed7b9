package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSSMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山双母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			String maintenance =  CommonFunctionBS.getMaintenance(stationName);
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> plkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL +","+ CBSystemConstants.RunTypeSwitchMLPL, "", false, true, true, true);
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			for (Iterator<PowerDevice> it = othermxList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(dev, curDev, "", "", true, true);
				
				for(PowerDevice path : pathList){
					if(path.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if (!RuleExeUtil.isSwitchDoubleML(path)) {
							it.remove();
							break;
		                }
					}
				}
			}
			
			mxList.add(curDev);
			mxList.addAll(othermxList);
			
			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			}
			
			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : xlkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			if(maintenance.equals(stationName)){
				replaceStr += stationName+"@核实"+deviceName+"相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
				replaceStr += stationName+"@核实"+deviceName+"电压互感器已恢复正常运行/r/n";

				for(PowerDevice dev : mlkgList){
					replaceStr += stationName+"@投入"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
				}
			}else{
				replaceStr += maintenance+"@核实"+stationName+deviceName+"相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
				replaceStr += maintenance+"@核实"+stationName+deviceName+"电压互感器已恢复正常运行/r/n";
				
				for(PowerDevice dev : mlkgList){
					replaceStr += maintenance+"@投入"+stationName+CZPService.getService().getDevName(dev)+"充电保护/r/n";
				}
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"复电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"复电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
				}
				
				replaceStr += "保山地调@执行"+stationName+deviceName+"由热备用转空载运行程序操作/r/n";
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"复电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"复电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
				}
				
				replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转空载运行程序操作/r/n";
			}
			
			if(maintenance.equals(stationName)){
				for(PowerDevice dev : mlkgList){
					replaceStr += stationName+"@退出"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
				}
				
				replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
				
				for(PowerDevice dev : mlkgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
				}
			}else{
				for(PowerDevice dev : mlkgList){
					replaceStr += maintenance+"@退出"+stationName+CZPService.getService().getDevName(dev)+"充电保护/r/n";
				}
				
				replaceStr += maintenance+"@核实"+stationName+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
				
				for(PowerDevice dev : mlkgList){
					replaceStr += maintenance+"@核实"+stationName+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
				}
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice yxkg : yxkgList){
						replaceStr += "保山地调@执行"+stationName+CZPService.getService().getDevName(yxkg)+"由"+deviceName+"运行倒至"+CZPService.getService().getDevName(dev)+"运行程序操作/r/n";
					}
				}
			}
			
			for(PowerDevice dev : mlkgList){
				replaceStr += maintenance+"@核实"+stationName+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
			}
			
			if(stationName.equals(maintenance)){
				replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			}else{
				replaceStr += maintenance+"@核实"+stationName+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice rbykg : rbykgList){
						replaceStr += "保山地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由热备用转冷备用程序操作/r/n";
						replaceStr += "保山地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由冷备用转热备用于"+CZPService.getService().getDevName(curDev)+"程序操作/r/n";
					}
				}
			}
			
			if(mxList.size() > 0){
				RuleExeUtil.swapDeviceList(mxList);
				
				if(stationName.equals(maintenance)){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(mxList)+"一、二次设备运行正常/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+CZPService.getService().getDevName(mxList)+"一、二次设备运行正常/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
