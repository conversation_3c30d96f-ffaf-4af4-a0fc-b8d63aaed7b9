package com.tellhow.czp.app.yndd.wordcard.yx;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYX23JXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("玉溪二分之三接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					Collections.reverse(zbgyckgList);
				}
				break;
			}
			
			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(jddzList);
				
				otherzxdjddzList.addAll(jddzList);
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunction.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				for(PowerDevice dev : zbgyckgList){
					if(CommonFunction.ifSwitchSeparateControl(dev)){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
						Collections.reverse(dzList);
						
						for(PowerDevice dz : dzList){
							List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
							dzTempList.add(dz);
							
							replaceStr += CommonFunction.getKnifeOnContent(dzTempList, stationName);
						}
					}else{
						String mxName = "";
						
						if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
							
							for(PowerDevice mx : mxList){
								mxName = "联"+CZPService.getService().getDevName(mx);
								break;
							}
						}
						
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转"+mxName+"热备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> zycdzList = CommonFunction.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunction.getKnifeOnContent(zycdzList,stationName);
					
					if(CommonFunction.ifSwitchSeparateControl(dev)){
						String mxName = "";
						
						if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
							
							for(PowerDevice mx : mxList){
								mxName = "联"+CZPService.getService().getDevName(mx);
								break;
							}
						}
						
						replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转"+mxName+"热备用程序操作/r/n";
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
					}else{
						String mxName = "";
						
						if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
							
							for(PowerDevice mx : mxList){
								mxName = "联"+CZPService.getService().getDevName(mx);
								break;
							}
						}
						
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转"+mxName+"热备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					List<PowerDevice> dycdzList = CommonFunction.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunction.getKnifeOnContent(dycdzList,stationName);
					
					if(CommonFunction.ifSwitchSeparateControl(dev)){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						replaceStr += CommonFunction.getKnifeOnContent(dzList,stationName);
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : dycmxList){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处热备用/r/n";
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"电压互感器已处运行/r/n";
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					break;
				}
			}
			
			Collections.reverse(zbgyckgList);
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
					break;
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					String dycmxName = "";
					
					for(PowerDevice dycmx : dycmxList){
						dycmxName = CZPService.getService().getDevName(dycmx);
					}
					
					replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+dycmxName+"充电/r/n";
				}
			}
			
			if(zxdjddzList.size() > 0){					
				replaceStr += CommonFunction.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
			}
			
			if(otherzxdjddzList.size() > 0){
				replaceStr += CommonFunction.getZxdJddzOffCheckContent(otherzxdjddzList, stationName, station);
			}
		}
		
		return replaceStr;
	}

}
