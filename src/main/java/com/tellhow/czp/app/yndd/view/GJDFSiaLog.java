package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.DataFormatException;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.table.DefaultTableModel;
import javax.swing.text.JTextComponent;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.mainframe.JPopupTextField;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;


public class GJDFSiaLog extends javax.swing.JDialog{
	
	private JPanel topPanel;
	private JPanel mainPanel;
	private JTable jTable1;
	private JButton saveButton;//查询按钮
	private JComboBox czComboBox;
	private JComboBox equipComboBox;
	private JTextArea remindMemo;
	private JScrollPane jScrollPane;
	private DefaultTableModel jTable1Model = null;
	public static JTextComponent tjc;
	private String curLineId;
	private String curKind;
	
	public GJDFSiaLog(javax.swing.JDialog parent, boolean modal) {
		super(parent, modal);
		initComponents();
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}



	
	private void initComponents()  {
		
		this.setLayout(new BorderLayout());
		this.setSize(1100, 400);
		topPanel =new JPanel();
		this.add(topPanel,BorderLayout.NORTH);
		mainPanel =new JPanel();
		this.add(mainPanel,BorderLayout.CENTER);
		jScrollPane = new JScrollPane();
		jScrollPane.setBounds(40, 90, 1000, 200);
		topPanel.setLayout(new FlowLayout(FlowLayout.RIGHT,15,5));
		
		mainPanel.setLayout(null);
//		JLabel jLabel1= new JLabel("线路名称：");
		JLabel jLabel1= new JLabel("");
//		jLabel1.setBounds(40, 40, 80, 26);
		
		//设置不可编辑单元格
		jTable1 = new JTable();
		JPopupTextField jtf = new JPopupTextField();
		jtf.setFont(new Font("宋体",Font.PLAIN,14));
		DefaultCellEditor editor = new DefaultCellEditor(jtf);
		jTable1.setDefaultEditor(Object.class, editor);
		jTable1.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		jTable1.setPreferredScrollableViewportSize(new Dimension(450, 200));
		jTable1.setFont(new Font("宋体",Font.PLAIN,14)); // NOI18N	
        jTable1.setRowHeight(30);
		Object[][] tableDate = null;
		
		if (jTable1Model == null)
			jTable1Model = new DefaultTableModel(tableDate, new String[] {
					"时间","厂站名","设备名","设备类型","告警信号类型" });
		
		jTable1.setModel(jTable1Model);
		
		String sql = "SELECT t.时间, t.厂站名,t.设备名,t.设备类型,t.告警信号类型  FROM  opcardhh.t_s_alarm t ORDER BY t.时间 DESC";
		querySql(sql);
		jTable1.getColumnModel().getColumn(0).setMinWidth(150);
     	jTable1.getColumnModel().getColumn(0).setMaxWidth(160);
		jTable1.getColumnModel().getColumn(1).setMinWidth(100);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(100);
		jTable1.getColumnModel().getColumn(2).setMinWidth(100);
		jTable1.getColumnModel().getColumn(2).setMaxWidth(100);
		jTable1.getColumnModel().getColumn(3).setMinWidth(100);
		jTable1.getColumnModel().getColumn(3).setMaxWidth(100);
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		// sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);
		
        jScrollPane.setViewportView(jTable1);
		mainPanel.add(jLabel1);
		searchButton();	
		mainPanel.add(jScrollPane);
	}
		
		
	public void paint(Graphics g) {
		super.paint(g);
		Graphics2D g_2d = (Graphics2D) g;
		g_2d.setColor(Color.GRAY);
		g_2d.drawLine(20, 75, this.getSize().width - 20, 75);

	}
	
	
	/**
	 * 创建搜索框
	 */
	public void searchButton() {
		
		remindMemo = new JTextArea();
		remindMemo.setBounds(40, 40, 240, 26);
		remindMemo.setFont(new java.awt.Font("宋体", 0, 13));
		remindMemo.setSelectionEnd(-1);
		mainPanel.add(remindMemo);
		
		JButton searchButton = new JButton("搜索");
		searchButton.setBounds(290, 40, 80, 26);
		searchButton.setFocusPainted(false);
		searchButton.setMargin(new java.awt.Insets(1,1,1,1));
		searchButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                search(remindMemo.getText());
            }
        });
		mainPanel.add(searchButton);
	}
	
	
	/**
	 * 根据输入框的内容进行查询
	 * @param typeName
	 */
	public void search(String typeName) {
		
		((DefaultTableModel) jTable1.getModel()).getDataVector().clear();   //清除表格数据
		((DefaultTableModel) jTable1.getModel()).fireTableDataChanged();//通知模型更新
		jTable1.updateUI();//刷新表格
		
 		if(typeName.length() > 0 && typeName != null) {
		    String sql = "SELECT t.时间,t.厂站名,t.设备名,t.设备类型,t.告警信号类型 FROM opcardhh.t_s_alarm t WHERE  t.厂站名 LIKE '%"+typeName+"%' OR t.设备名  LIKE '%"+typeName+"%' OR t.设备类型  LIKE '%"+typeName+"%'  OR t.告警信号类型  LIKE '%"+typeName+"%' order by t.时间   DESC";
		    querySql(sql);
		    
		}else {
			String sql = "SELECT t.时间, t.厂站名,t.设备名,t.设备类型,t.告警信号类型  FROM  opcardhh.t_s_alarm t ORDER BY t.时间 DESC";
			querySql(sql);
		}
	   
	}
		
	/**
	 * 执行sql语句并把结果放入表格中
	 * @param sql
	 */
   public void querySql(String sql) {
	
		List<Map<String,String>> results= DBManager.queryForList(sql);
		
		if(results.size()>0){
			for(int i = 0; i < results.size(); i++){
				Map map=(Map)results.get(i);
				 
				Object[] rowData;
				SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
				
				SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				
			    String str = null ;
				try {
					Date date = simpleDateFormat.parse((String) map.get("时间"));
					str =simpleDateFormat2.format(date);
				} catch (ParseException e) {
					// TODO 自动生成的 catch 块
					e.printStackTrace();
				}
				
				rowData = new Object[]{
						 str
						,map.get("厂站名")
						,map.get("设备名")
						, map.get("设备类型")
						, map.get("告警信号类型")};
				
				jTable1Model.addRow(rowData);
			}
		}
	}
}