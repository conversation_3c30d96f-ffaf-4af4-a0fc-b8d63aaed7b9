package com.tellhow.czp.app.yndd;

import java.awt.BorderLayout;
import java.awt.FlowLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.border.EmptyBorder;

import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tellhow.czp.app.tool.RuleCopy;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.rule.CopyCardWord;
import czprule.rule.CopyRule;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class CopyRuleDialg extends JDialog {

	private JComboBox comboBox1;//源数据：单位输入框 下拉框
	private JComboBox comboBox2;//目标数据：单位输入框 下拉框
	private JComboBox comboBox3;//源数据：业务名称输入框 下拉框
	private JComboBox comboBox4;//目标数据：单位输入框 下拉框

	private final JPanel contentPanel = new JPanel();

	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			CopyRuleDialg dialog = new CopyRuleDialg();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * Create the dialog.
	 */
	public CopyRuleDialg() {
		setTitle("\u590D\u5236\u6570\u636E");
		setBounds(100, 100, 420, 243);
		getContentPane().setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		getContentPane().add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(null);
		{
			JLabel lblNewLabel = new JLabel("\u6E90\u6570\u636E");
			lblNewLabel.setBounds(22, 34, 118, 32);
			contentPanel.add(lblNewLabel);
		}
		{
			comboBox1 = new JComboBox();//源数据：单位输入框 下拉框
			comboBox1.setBounds(64, 76, 146, 21);
			initZoneName(comboBox1,"0");
			contentPanel.add(comboBox1);
		}
		{
			comboBox3 = new JComboBox();//源数据：业务名称输入框 下拉框
			comboBox3.setBounds(64, 107, 146, 21);
			initName(comboBox3);
			contentPanel.add(comboBox3);
		}
		{
			JLabel label = new JLabel("\u76EE\u6807\u6570\u636E");
			label.setBounds(248, 34, 103, 32);
			contentPanel.add(label);
		}
		{
			comboBox2 = new JComboBox();////目标数据：单位输入框 下拉框
			comboBox2.setBounds(248, 76, 146, 21);
			initZoneName(comboBox2,"");
			contentPanel.add(comboBox2);
		}
		{
			comboBox4 = new JComboBox();//目标数据：业务名称输入框 下拉框
			comboBox4.setBounds(248, 107, 146, 21);
			initName(comboBox4);
			contentPanel.add(comboBox4);
		}
		
		JLabel lblNewLabel_1 = new JLabel("\u5355\u4F4D:");
		lblNewLabel_1.setBounds(22, 79, 32, 15);
		contentPanel.add(lblNewLabel_1);
		
		JLabel lblNewLabel_2 = new JLabel("\u4E1A\u52A1:");
		lblNewLabel_2.setBounds(22, 108, 32, 18);
		contentPanel.add(lblNewLabel_2);
		{
			JPanel buttonPane = new JPanel();
			buttonPane.setLayout(new FlowLayout(FlowLayout.RIGHT));
			getContentPane().add(buttonPane, BorderLayout.SOUTH);
			
//			JButton button = new JButton("\u5168\u5C40\u590D\u5236");
//			button.addActionListener(new ActionListener() {
//				public void actionPerformed(ActionEvent arg0) {
//					copyAllData();
//				}
//			});
//			buttonPane.add(button);
			{
				JButton okButton = new JButton("\u590D\u5236");
				okButton.setActionCommand("OK");
				buttonPane.add(okButton);
				okButton.addActionListener(new ActionListener(){
					public void actionPerformed(ActionEvent e){
						copy();
					} 
				});
				getRootPane().setDefaultButton(okButton);
				
			}
			{
				JButton cancelButton = new JButton("\u53D6\u6D88");
				cancelButton.setActionCommand("Cancel");
				buttonPane.add(cancelButton);
				
				cancelButton.addActionListener(new ActionListener(){
		            public void actionPerformed(ActionEvent e){
		            	CloseDialog();
		            }
		        });
			}
		}
	}
	
    /**
     * 初始化单位下拉框
     * 显示单位 "+CBSystemConstants.platformUser+"T_TBP_ZONE中“zone_name”字段
     */
	public void initZoneName(JComboBox comboBox,String unitcode) {
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		
		cnm=new CodeNameModel();
		cnm.setCode("0");
		cnm.setName("系统");
		model.addElement(cnm);
		
		String sql = "";
		sql = "select organid code, dwmc name from "+CBSystemConstants.opcardUser+"TH_DC_DCMS_ORGAN where organtype='0' ";
			
		SqlRowSet set = DBManager.queryForRowSet(sql);
		while (set.next()){
			cnm=new CodeNameModel();
			cnm.setCode(set.getString(1));
			cnm.setName(set.getString(2));
			model.addElement(cnm);
		}
		comboBox.setModel(model);
	}
	
    /**
     * 初始化业务名称下拉框
     * 显示业务名称 "+CBSystemConstants.opcardUser+"T_A_DICTIONARY中“name”字段
     */
	public void initName(JComboBox comboBox) {
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		String sql = "select t.code,t.name from "+CBSystemConstants.opcardUser+"T_A_DICTIONARY t where t.unitcode='system' and t.codetype='Role'";
		SqlRowSet set = DBManager.queryForRowSet(sql);
		while (set.next()){
			cnm=new CodeNameModel();
			cnm.setCode(set.getString(1));
			cnm.setName(set.getString(2));
			model.addElement(cnm);
		}
		comboBox.setModel(model);
	}
	/**
	 * 取消功能，窗口关闭
	 * 
	 */
    public void CloseDialog(){
    	this.setVisible(false);
        this.dispose();
    }
    
    public void copyAllData(){
    	
    	//得到源数据区域编码
		CodeNameModel sourceAreaNO=(CodeNameModel) comboBox1.getSelectedItem();
		String sAreaNo = sourceAreaNO.getCode();//源数据的areano
		CodeNameModel sourceRoleCode=(CodeNameModel) comboBox3.getSelectedItem();
		String sourceRole = sourceRoleCode.getCode();//源数据的areano
		String sourceUnitCode = QueryDeviceDao.getOpcode(sAreaNo, sourceRole);
		
		//得到目标区域编码
		CodeNameModel targetModel=(CodeNameModel) comboBox2.getSelectedItem();
		String tNo = targetModel.getCode();//目标数据的areano
		CodeNameModel targetModel2=(CodeNameModel) comboBox4.getSelectedItem();
		String tRole = targetModel2.getCode();//目标数据的areano
		
		/*
		List allOpcodes = QueryDeviceDao.getAllAreaOpcode(tNo, tRole);
        String opcode = "";
        Map temp = null;
		for (Iterator iterator = allOpcodes.iterator(); iterator.hasNext();) {
			temp = (Map) iterator.next();
			opcode = StringUtils.ObjToString(temp.get("OPCODE"));
			
			if("".equals(sourceUnitCode) || sourceUnitCode ==null){
				JOptionPane.showMessageDialog(null,"源数据的区域编码不存在","信息提示",JOptionPane.WARNING_MESSAGE);
			}else if(sourceUnitCode.equals(opcode)){
				JOptionPane.showMessageDialog(null,"不能选择相同的数据","信息提示",JOptionPane.WARNING_MESSAGE);
			}else if(checkZB(opcode) == true) {//判断是否已经在目标区域中存在源数据
				deleteZBCB(opcode);//删除已存在区域数据
				copyData(sourceUnitCode, opcode);
			}else{
				copyData(sourceUnitCode, opcode);
			}
		}
		*/
		JOptionPane.showMessageDialog(null,"复制成功","信息提示",JOptionPane.WARNING_MESSAGE);
    }
    
    /**
     * 通过源数据和目标数据，取得两个数据的区域编码，把源数据规则和语义复制给目标数据
     * "+CBSystemConstants.platformUser+"T_TBP_ZONE  zone_no 对应条件的  areano
     * "+CBSystemConstants.opcardUser+"T_A_OPCODEINFO  code 对应条件的  rolecode
     */
    public void copy(){
    	//得到源数据区域编码
		CodeNameModel sourceAreaNO=(CodeNameModel) comboBox1.getSelectedItem();
		String sAreaNo = sourceAreaNO.getCode();//源数据的areano
		CodeNameModel sourceRoleCode=(CodeNameModel) comboBox3.getSelectedItem();
		String sourceRole = sourceRoleCode.getCode();//源数据的areano
		String sourceUnitCode = QueryDeviceDao.getOpcode(sAreaNo, sourceRole);
		
		//得到目标区域编码
		CodeNameModel targetModel=(CodeNameModel) comboBox2.getSelectedItem();
		String tNo = targetModel.getCode();//目标数据的areano
		CodeNameModel targetModel2=(CodeNameModel) comboBox4.getSelectedItem();
		String tRole = targetModel2.getCode();//目标数据的areano
		String targetUnitCode = QueryDeviceDao.getOpcode(tNo, tRole);
		
		
		if("".equals(sourceUnitCode) || sourceUnitCode ==null){
			JOptionPane.showMessageDialog(null,"源数据的区域编码不存在","信息提示",JOptionPane.WARNING_MESSAGE);
		}else if(sourceUnitCode.equals(targetUnitCode)){
			JOptionPane.showMessageDialog(null,"不能选择相同的数据","信息提示",JOptionPane.WARNING_MESSAGE);
		}else if(checkZB(targetUnitCode) == true) {//判断是否已经在目标区域中存在源数据
			
			int option = JOptionPane.showConfirmDialog(this, "目标区域已存在源数据,你是否还要继续复制","提示信息",JOptionPane.YES_NO_OPTION);
			if(option == 0){
				RuleCopy.copyAllByOpcode(sourceUnitCode, targetUnitCode);
				JOptionPane.showMessageDialog(null,"复制成功","信息提示",JOptionPane.WARNING_MESSAGE);
			}
		}else{
			RuleCopy.copyAllByOpcode(sourceUnitCode, targetUnitCode);
			JOptionPane.showMessageDialog(null,"复制成功","信息提示",JOptionPane.WARNING_MESSAGE);
			//CloseDialog();
		}
		
	}
    
    public void copyAllData(String sourceUnitCode, String targetUnitCode){
    	
    	
    }
    
    public void copyData(String sourceUnitCode, String targetUnitCode) {
    	
    	//一、复制右键操作
    	String sql = " INSERT INTO "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO T(STATECODE,STATENAME,STATEVALUE,OPCODE,DEVICETYPEID,PARENTCODE,STATETYPE,RUNMODEL,HASSIDE,RUNTYPE,STATEORDER,SECONDTYPEID,SECONDSTATE,OPERATECODE,CARDBUILDTYPE,STATEKIND,ISLOCK)\n" +
    				 " SELECT '"+targetUnitCode+"'||t.STATECODE,T.STATENAME,T.STATEVALUE, '"+targetUnitCode+"',T.DEVICETYPEID,decode(T.PARENTCODE,'0','0','"+targetUnitCode+"'||T.PARENTCODE), T.STATETYPE, T.RUNMODEL,T.HASSIDE,T.RUNTYPE,T.STATEORDER,T.SECONDTYPEID,T.SECONDSTATE,T.OPERATECODE,T.CARDBUILDTYPE,T.STATEKIND, T.ISLOCK\n" + 
    				 " FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO T WHERE T.OPCODE='"+sourceUnitCode+"'";
    	DBManager.execute(sql);
    	
    	
    	//二、 复制规则
    	//复制主表
    	sql = " INSERT INTO "+CBSystemConstants.opcardUser+"T_A_RULEZB(ZBID,DEVICETYPEID,DEVICERUNMODEL,BEGINSTATUS,ENDSTATE,VOLT,OPCODE,CARDBUILDTYPE)\n" +
    		  " SELECT '"+targetUnitCode+"'||ZBID,DEVICETYPEID,DEVICERUNMODEL,BEGINSTATUS,'"+targetUnitCode+"'||ENDSTATE,VOLT,'"+targetUnitCode+"',CARDBUILDTYPE FROM "+CBSystemConstants.opcardUser+"T_A_RULEZB T WHERE T.OPCODE='"+sourceUnitCode+"'";
    	DBManager.execute(sql);  
    	//复制从表
    	sql = " INSERT INTO "+CBSystemConstants.opcardUser+"T_A_RULECB(F_ZBID,RULEID,BEGINSTATUS,ENDSTATE,ORDERID,TRANTYPE,DEVICERUNTYPE)\n" +
    		  " SELECT '"+targetUnitCode+"'||T1.ZBID,T.RULEID,T.BEGINSTATUS,T.ENDSTATE,T.ORDERID,T.TRANTYPE,T.DEVICERUNTYPE FROM "+CBSystemConstants.opcardUser+"T_A_RULECB T,"+CBSystemConstants.opcardUser+"T_A_RULEZB T1\n" +
    		  " WHERE T.F_ZBID=T1.ZBID AND T1.OPCODE='"+sourceUnitCode+"'";
    	DBManager.execute(sql);  
    	
//    	//更新ENDSTATE
//    	sql = " UPDATE "+CBSystemConstants.opcardUser+"t_a_Rulezb T SET T.ENDSTATE=\n" +
//    		  " (SELECT T1.STATECODE FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO T1 WHERE T.ENDSTATE=T1.TEMPCODE AND T1.OPCODE='"+targetUnitCode+"')\n" +
//    		  " WHERE  T.OPCODE='"+targetUnitCode+"'";
//    	DBManager.execute(sql); 
    	
    	//三、复制术语
    	//复制主表
    	sql = " INSERT INTO  "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB(ZBID,DEVICETYPEID,BEGINSTATUS,ENDSTATE,STATETYPE,OPCODE,CZRW,VOLT,CARDTYPE,EQUIPID)\n" +
    		  " SELECT  '"+targetUnitCode+"'||ZBID,DEVICETYPEID,BEGINSTATUS,'"+targetUnitCode+"'||ENDSTATE,STATETYPE,'"+targetUnitCode+"',CZRW,VOLT,CARDTYPE,EQUIPID FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB  WHERE OPCODE='"+sourceUnitCode+"'";
    	DBManager.execute(sql); 
    	//复制从表
    	sql = " INSERT INTO "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB(DEVICESTATEMENTCB,F_ZBID,ORDERID)\n" +
    		  " SELECT T.DEVICESTATEMENTCB,'"+targetUnitCode+"'||T1.ZBID,T.ORDERID FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB T,"+CBSystemConstants.opcardUser+"T_A_CARDWORDZB T1 WHERE T.F_ZBID=T1.ZBID AND T1.OPCODE='"+sourceUnitCode+"'";
    	DBManager.execute(sql); 
    	//更新ENDSTATE
//    	sql = " UPDATE "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB T SET T.ENDSTATE=\n" +
//    		  " (SELECT T1.STATECODE FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO T1 WHERE t1.islock = '0' and T.ENDSTATE=T1.TEMPCODE AND T1.OPCODE='"+targetUnitCode+"')\n" +
//    		  " WHERE  T.OPCODE='"+targetUnitCode+"'";
//    	DBManager.execute(sql); 
    	
//    	String sql3 = "select t.statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t where t.parentcode='0' and t.opcode='"+sourceUnitCode+"'";
//		List list3 = DBManager.queryForList(sql3);
//		Map tempCB = new HashMap();
//		for (int i = 0; i < list3.size(); i++) {
//			Map map3 = (Map) list3.get(i);
//			String sourceStateCode = StringUtils.ObjToString(map3.get("statecode"));
//			String targetStateCode = CopyCardWord.copyDevicesStateInfo(sourceUnitCode, targetUnitCode, sourceStateCode, "0");
//			CopyRule.copyDecvState(sourceUnitCode, targetUnitCode, sourceStateCode, targetStateCode);//copy 规则数据
//			CopyCardWord.copyDecvCard(sourceUnitCode, targetUnitCode, sourceStateCode, targetStateCode);//copy 语义数据
//			
//			String sql4 = "select t.statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t where t.parentcode='"+sourceStateCode+"' and t.opcode='"+sourceUnitCode+"'";
//			List list4 = DBManager.queryForList(sql4);
//			for (int j = 0; j < list4.size(); j++) {
//				Map map4 = (Map) list4.get(j);
//				String sourceStateCodeChild = StringUtils.ObjToString(map4.get("statecode"));
//				String targetStateCodeChild = CopyCardWord.copyDevicesStateInfo(sourceUnitCode, targetUnitCode, sourceStateCodeChild, targetStateCode);
//				CopyRule.copyDecvState(sourceUnitCode, targetUnitCode, sourceStateCodeChild, targetStateCodeChild);//copy 规则数据
//				CopyCardWord.copyDecvCard(sourceUnitCode, targetUnitCode, sourceStateCodeChild, targetStateCodeChild);//copy 语义数据
//			}
//		}
    }
    
	/**
	 * 判断是否源数据是否已保存到目标数据中。
	 * 
	 * @param endState
	 * @return
	 */
	public static boolean checkZB(String code) {
		String sql = "SELECT count(*) ct FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t where t.islock = '0' and t.opcode='"
				+ code + "'";
		List list = DBManager.queryForList(sql);
		if(((Map)(list.get(0))).get("ct").toString().equals("0"))
			return false;
		else
			return true;
	}
	
	/**
	 * 根据区域编码删除T_A_DEVICESTATEINFO表中已存在区域的数据
	 * @param code
	 */
	public static void deleteDeviceInfo(String code){
		String sql = " delete FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t where t.islock = '0' and t.opcode='"
				+ code + "'";
		DBManager.execute(sql);
	}
	/**
	 * 删除主从表数据
	 * @param code
	 */
	public static void deleteZBCB(String code){
		CopyRule.delAreaRule(code);//删除规则主从表中已存在区域数据源
		CopyCardWord.delAreaWord(code);//删除语义主表中已存在区域数据源
		deleteDeviceInfo(code);//删除T_A_DEVICESTATEINFO设备操作信息
	}
}
