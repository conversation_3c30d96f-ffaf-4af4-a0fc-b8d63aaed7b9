package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.cx.CXGCBHDialog;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXPLDGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄旁路代供复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			String plkgName = "";
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) || dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
						plkgName = CZPService.getService().getDevName(dev);
						break;
					}
				}
			}
			
			replaceStr += "楚雄地调@"+stationName+deviceName+"停电设备摘牌。/r/n";

			String mxName = "";
			
			if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
				
				for(PowerDevice mx : mxList){
					mxName = CZPService.getService().getDevName(mx);
					break;
				}
			}
			
			replaceStr += voltStationName+"@"+deviceName+"由冷备用转运行于"+mxName+"，"+plkgName+"由运行转热备用。/r/n";
			
			if(CXGCBHDialog.isgcbh){
				List<PowerDevice> lineList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice line : lineList){
					List<PowerDevice> linesList = RuleExeUtil.getLineAllSideList(line);
					
					for(PowerDevice dev : linesList){
						PowerDevice station2 = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName2 = CZPService.getService().getDevName(station2); 
						String deviceName2 = CZPService.getService().getDevName(dev); 
						
						replaceStr += stationName2+"@投入"+deviceName2+"光差保护。/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
