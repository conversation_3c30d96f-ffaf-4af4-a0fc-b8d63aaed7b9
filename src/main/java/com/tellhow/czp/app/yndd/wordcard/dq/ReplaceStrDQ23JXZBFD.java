package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQ23JXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		
		if("迪庆二分之三接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					Collections.reverse(zbgyckgList);
				}
				break;
			}
			
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			replaceStr += stationName+"@落实迪庆供电局-XXXXXX检修申请工作任务已结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），该设备的二次装置已正常投入，确认设备具备送电条件/r/n";
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				for(PowerDevice dev : zbgyckgList){
					List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
					zbkgList.add(dev);
					
					if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
						replaceStr += "迪庆地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
					
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(zbkgList, stationName);
				}
				
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> zycdzList = CommonFunctionDQ.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunctionDQ.getKnifeOnContent(zycdzList,stationName);
					
					if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
						replaceStr += "迪庆地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						replaceStr += CommonFunctionDQ.getKnifeOnCheckContent(dzList, stationName);
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					List<PowerDevice> dycdzList = CommonFunctionDQ.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunctionDQ.getKnifeOnContent(dycdzList,stationName);
					
					if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						replaceStr += CommonFunctionDQ.getKnifeOnContent(dzList,stationName);
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : dycmxList){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已处热备用/r/n";
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"电压互感器已处运行/r/n";
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "迪庆地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					break;
				}
			}
			
			Collections.reverse(zbgyckgList);
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionDQ.getHhContent(dev, "迪庆地调", stationName);
					break;
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionDQ.getHhContent(dev, "迪庆地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					String dycmxName = "";
					
					for(PowerDevice dycmx : dycmxList){
						dycmxName = CZPService.getService().getDevName(dycmx);
					}
					
					replaceStr += "迪庆地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+dycmxName+"充电/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
