package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.PowerDevice;
import czprule.rule.model.CardWordMode;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.model.CardItemModel;

public class GetDtdDataDL extends GetDtdData{
	public static ArrayList<String[]> getData(List<CardItemModel> itemModelsShow,String ddname){
		ArrayList<String[]> data = new ArrayList<String[]>();
		modelList = DBManager.queryForList(CommonUtils.WORD_RBM_SQL);

		for(CardItemModel cim : itemModelsShow){
			String cznr = cim.getCardDesc();
			String stationid = cim.getCzdwID();
			String stationname = cim.getStationName();
			String bdzname = cim.getBdzName();
			String uuids = cim.getUuIds();

			String isyk = "1";
			String devname = "";
			String startZT = "";
			String endZT= "";
			String deviceType= "";
			
			List<RuleBaseMode> rbmList = getRBMList(stationname,cznr);
			
			String czname = "";
			
			if(rbmList.size()>0){
				PowerDevice dev = rbmList.get(0).getPd();
				
				if(dev!=null){
					String begin = rbmList.get(0).getBeginStatus();
					String end = rbmList.get(0).getEndState();
					
					if(!dev.getPowerStationID().equals("")){
						PowerDevice stationDev = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						czname = CZPService.getService().getDevName(stationDev);
						devname = dev.getPowerDeviceName();
					}else{
						czname = stationname;
						
						if(czname.equals("大理地调")){
							czname = bdzname;
						}
					}
					
					if(cznr.contains("程序操作") || cznr.contains("遥控")){
						if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
							List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(dev.getPowerDeviceID());
							PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(dev.getPowerDeviceID());
							
							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								if(begin.equals("0")){
									if(loadLineTrans != null){
										for(PowerDevice loadLineTran : loadLineTrans){
											List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
											PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
											czname = CZPService.getService().getDevName(station);
											
											if(!ishh(loadLineTran)){
												for(PowerDevice kg : kgList){
													if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
														String devid = kg.getPowerDeviceID();
														cznr = "遥控断开"+czname+CZPService.getService().getDevName(kg);
														data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
													}
												}
											}
										}
									}
									
									List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
									PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
									czname = CZPService.getService().getDevName(station);
									
									for(PowerDevice kg : kgList){
										if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
											String devid = kg.getPowerDeviceID();
											cznr = "遥控断开"+czname+CZPService.getService().getDevName(kg);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
										}
									}
								}
								
								
								if(end.equals("2")){
									if(loadLineTrans != null){
										for(PowerDevice loadLineTran : loadLineTrans){
											List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
											PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
											czname = CZPService.getService().getDevName(station);
											getSwitchSequenceTdMerge(kgList,data,uuids,czname,ddname);
										}
									}
									
									List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
									PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
									czname = CZPService.getService().getDevName(station);
									
									if(kgList.size() == 2){
										List<PowerDevice> newkgList = new ArrayList<PowerDevice>();
										
										for(PowerDevice kg : kgList){
											if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
												newkgList.add(kg);
											}
										}
										
										for(PowerDevice kg : kgList){
											if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
												newkgList.add(kg);
											}
										}
										
										kgList.clear();
										kgList.addAll(newkgList);
									}
									
									getSwitchSequenceTdMerge(kgList, data, uuids, czname, ddname);
								}
							}else{
								if(begin.equals("2")){
									if(sourceLineTrans != null){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
										czname = CZPService.getService().getDevName(station);
										
										if(kgList.size() == 2){
											List<PowerDevice> newkgList = new ArrayList<PowerDevice>();
											
											for(PowerDevice kg : kgList){
												if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
													newkgList.add(kg);
												}
											}
											
											for(PowerDevice kg : kgList){
												if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
													newkgList.add(kg);
												}
											}
											
											kgList.clear();
											kgList.addAll(newkgList);
										}
										
										getSwitchSequenceFdMerge(kgList, data, uuids, czname, ddname);
									}
									
									if(loadLineTrans != null){
										for(PowerDevice loadLineTran : loadLineTrans){
											List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
											PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
											czname = CZPService.getService().getDevName(station);
											getSwitchSequenceFdMerge(kgList,data,uuids,czname,ddname);
										}
									}
								}
								
								if(end.equals("0")){
									if(sourceLineTrans != null){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
										czname = CZPService.getService().getDevName(station);

										for(PowerDevice kg : kgList){
											String devid = kg.getPowerDeviceID();
											cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(kg);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
										}
									}

									if(loadLineTrans != null){
										for(PowerDevice loadLineTran : loadLineTrans){
											List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
											PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
											czname = CZPService.getService().getDevName(station);
											
											for(PowerDevice kg : kgList){
												String devid = kg.getPowerDeviceID();
												cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(kg);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
											}
										}
									}
								}
							}
						}else if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
							List<PowerDevice> swList = new ArrayList<PowerDevice>();
							//母线为高压侧，且为单电源单刀闸情况
							List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXLS,"",true, true, true, true);

							if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
								swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchFHC,false, true, true, true);
							}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
								swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
							}
							
							List<PowerDevice> drswList = new ArrayList<PowerDevice>();
							List<PowerDevice> dkswList = new ArrayList<PowerDevice>();
							List<PowerDevice> xlswList = new ArrayList<PowerDevice>();
							List<PowerDevice> jdbswList = new ArrayList<PowerDevice>();
							List<PowerDevice> zybswList = new ArrayList<PowerDevice>();
							List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
							List<PowerDevice> mlswList = new ArrayList<PowerDevice>();

							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
									drswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(drswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
									dkswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(dkswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									xlswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(xlswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getPowerDeviceName().contains("接地变")
										||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)){
									jdbswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(jdbswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getPowerDeviceName().contains("站用变")
										||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchZYB)){
									zybswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(zybswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
									zbswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(zbswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									mlswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(mlswList);
							
							swList.clear();
							
							if(!end.equals("")&&!begin.equals("")){
								if(rbmList.get(0).getOperaTion().equals("停电倒电")){
									List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC,"", false, true, true, true);
									List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML,"", false, true, true, true);
									List<PowerDevice> kgList = new ArrayList<PowerDevice>();
									
									kgList.addAll(zbkgList);
									kgList.addAll(mlkgList);
									
									for(PowerDevice kg : kgList){
										if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
											String devid = kg.getPowerDeviceID();
											String devicename = kg.getPowerDeviceName();
											cznr  =  "遥控断开"+czname+CZPService.getService().getDevName(kg);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devicename,startZT,"断开",deviceType,uuids});
										}
									}
									
									for(PowerDevice kg : kgList){
										if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
											String devid = kg.getPowerDeviceID();
											String devicename = kg.getPowerDeviceName();
											cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(kg);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devicename,startZT,"合上",deviceType,uuids});
										}
									}
								}else if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
									if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
										swList.addAll(drswList);
										swList.addAll(dkswList);
										swList.addAll(xlswList);
										swList.addAll(jdbswList);
										swList.addAll(zybswList);
										swList.addAll(zbswList);
										swList.addAll(mlswList);

										getSwitchSequenceTdStep(swList,data,uuids,czname,ddname);
										
										for(PowerDevice xldz : xldzList){
											String devid = xldz.getPowerDeviceID();

											if(RuleExeUtil.isDeviceChanged(xldz)){
												cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(xldz);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
											}
										}
									}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
										for(PowerDevice kg : mlswList){
											if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
												String devid = kg.getPowerDeviceID();
												cznr = "遥控断开"+czname+CZPService.getService().getDevName(kg);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
											}
										}
										
										swList.addAll(mlswList);
										getSwitchSequenceTdStep(swList,data,uuids,czname, ddname);
									}
								}else{//复电
									if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
										if(dev.getPowerVoltGrade() == 10){
											swList.addAll(zbswList);
										}else{
											swList.addAll(zbswList);
											swList.addAll(zybswList);
											swList.addAll(jdbswList);
											swList.addAll(xlswList);
											swList.addAll(mlswList);
											swList.addAll(dkswList);
											swList.addAll(drswList);
											
											getSwitchSequenceFdStep(swList,data,uuids,czname,ddname);
										}
									}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
										swList.addAll(mlswList);
										getSwitchSequenceFdStep(swList,data,uuids,czname, ddname);

										for(PowerDevice kg : mlswList){
											if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
												String devid = kg.getPowerDeviceID();
												cznr = "遥控合上"+czname+CZPService.getService().getDevName(kg);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
											}
										}
									}
								}
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}else if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							List<PowerDevice> gyczbkgList = RuleExeUtil.getTransformerSwitchHigh(dev);
							List<PowerDevice> zyczbkgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
							List<PowerDevice> dyczbkgList = RuleExeUtil.getTransformerSwitchLow(dev);
							List<PowerDevice> zbgycdzkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeZBS, "", true, true, true, true);
							List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
							RuleExeUtil.swapLowDeviceList(gdList);
							
							if(RuleUtil.isTransformerNQ(dev)
									||RuleUtil.isTransformerKDNQ(dev)){
								gyczbkgList.clear();
							}
							
							if(rbmList.get(0).getOperaTion().equals("停电倒电")){
								List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
								List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
								List<PowerDevice> kgList = new ArrayList<PowerDevice>();

								PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
								HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(dev.getPowerStationID());
								
								for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
									PowerDevice dev2 = it.next();
									
									if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
										if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
											hignVoltXlkgList.add(dev2);
										}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
											hignVoltMlkgList.add(dev2);
										}
									}
								}
								
								kgList.addAll(hignVoltXlkgList);
								kgList.addAll(hignVoltMlkgList);
								
								for(PowerDevice kg : kgList){
									if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
										String devid = kg.getPowerDeviceID();
										String zbkgName = CZPService.getService().getDevName(kg);

										cznr  =  "遥控断开"+czname+zbkgName;
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"断开",deviceType,uuids});
									}
								}
								
								for(PowerDevice kg : kgList){
									if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
										String devid = kg.getPowerDeviceID();
										String zbkgName = CZPService.getService().getDevName(kg);

										cznr  =  "遥控合上"+czname+zbkgName;
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"合上",deviceType,uuids});
									}
								}
							}else{
								if(!begin.equals("")&&!end.equals("")){
									if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
										if(begin.equals("0")){
											for(PowerDevice gd : gdList){
												String devid = gd.getPowerDeviceID();
												String gdName = CZPService.getService().getDevName(gd);

												cznr = "遥控合上"+czname+gdName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gdName,startZT,"合上",deviceType,uuids});
											}
											
											for(PowerDevice dyczbkg : dyczbkgList){
												String devid = dyczbkg.getPowerDeviceID();
												String zbkgName = CZPService.getService().getDevName(dyczbkg);
												
												if(RuleExeUtil.getDeviceBeginStatus(dyczbkg).equals("0")){
													cznr = "遥控断开"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"断开",deviceType,uuids});
												}
											}
											
											for(PowerDevice zyczbkg : zyczbkgList){
												String devid = zyczbkg.getPowerDeviceID();
												String zbkgName = CZPService.getService().getDevName(zyczbkg);
												
												if(RuleExeUtil.getDeviceBeginStatus(zyczbkg).equals("0")){
													cznr = "遥控断开"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"断开",deviceType,uuids});
												}
											}
											
											for(PowerDevice gyczbkg : gyczbkgList){
												String devid = gyczbkg.getPowerDeviceID();
												String zbkgName = CZPService.getService().getDevName(gyczbkg);

												if(RuleExeUtil.getDeviceBeginStatus(gyczbkg).equals("0")){
													cznr = "遥控断开"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"断开",deviceType,uuids});
												}
											}
										}
										
										getSwitchSequenceTdMerge(dyczbkgList, data, uuids, czname, ddname);
										
										getSwitchSequenceTdMerge(zyczbkgList, data, uuids, czname, ddname);

										getSwitchSequenceTdMerge(gyczbkgList, data, uuids, czname, ddname);
										
										getSwitchSeparateSequenceTd(zbgycdzkgList,data,uuids,czname,ddname);
										
										if(begin.equals("0")){
											for(PowerDevice gd : gdList){
												String devid = gd.getPowerDeviceID();
												String gdName = CZPService.getService().getDevName(gd);

												cznr  =  "遥控拉开"+czname+gdName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gd.getPowerDeviceName(),startZT,"拉开",deviceType,uuids});
											}
										}
									}else{//复电
										if(end.equals("0")){
											for(PowerDevice gd : gdList){
												String devid = gd.getPowerDeviceID();
												String gdName = CZPService.getService().getDevName(gd);

												cznr  =  "遥控合上"+czname+gdName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gd.getPowerDeviceName(),startZT,"合上",deviceType,uuids});
											}
										}
										
										getSwitchSeparateSequenceFd(zbgycdzkgList,data,uuids,czname,ddname);

										getSwitchSequenceFdMerge(gyczbkgList, data, uuids, czname, ddname);
										
										getSwitchSequenceFdMerge(zyczbkgList, data, uuids, czname, ddname);
										
										getSwitchSequenceFdMerge(dyczbkgList, data, uuids, czname, ddname);
										
										
										if(end.equals("0")){
											for(PowerDevice gyczbkg : gyczbkgList){
												String devid = gyczbkg.getPowerDeviceID();
												String zbkgName = CZPService.getService().getDevName(gyczbkg);

												cznr  =  "遥控合上"+czname+zbkgName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gyczbkg.getPowerDeviceName(),startZT,"合上",deviceType,uuids});
											}
											
											for(PowerDevice zyczbkg : zyczbkgList){
												if(RuleExeUtil.getDeviceEndStatus(zyczbkg).equals("0")){
													String devid = zyczbkg.getPowerDeviceID();
													String zbkgName = CZPService.getService().getDevName(zyczbkg);

													cznr  =  "遥控合上"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zyczbkg.getPowerDeviceName(),startZT,"合上",deviceType,uuids});
												}
											}
											
											for(PowerDevice dyczbkg : dyczbkgList){
												if(RuleExeUtil.getDeviceEndStatus(dyczbkg).equals("0")){
													String devid = dyczbkg.getPowerDeviceID();
													String zbkgName = CZPService.getService().getDevName(dyczbkg);

													cznr  =  "遥控合上"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,dyczbkg.getPowerDeviceName(),startZT,"合上",deviceType,uuids});
												}
											}
											
											for(PowerDevice gd : gdList){
												String devid = gd.getPowerDeviceID();
												String gdName = CZPService.getService().getDevName(gd);

												cznr  =  "遥控拉开"+czname+gdName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gd.getPowerDeviceName(),startZT,"拉开",deviceType,uuids});
											}
										}
									}
								}else{
									data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
								}
							}
						}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
							if((begin.equals("1")&&end.equals("2"))){
								List<PowerDevice> swList = new ArrayList<PowerDevice>();
								swList.add(dev);
								getSwitchSequenceTdStep(swList , data, uuids, czname ,ddname);
							}else if(begin.equals("2")&&end.equals("1")){
								List<PowerDevice> swList = new ArrayList<PowerDevice>();
								swList.add(dev);
								getSwitchSequenceFdStep(swList , data, uuids, czname ,ddname);
							}else if(begin.equals("1")&&end.equals("0")){
								String devid = dev.getPowerDeviceID();
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								String devid = dev.getPowerDeviceID();
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("2")){
								String devid = dev.getPowerDeviceID();
								cznr  =  "遥控断开"+czname+devname;
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
								
								List<PowerDevice> swList = new ArrayList<PowerDevice>();
								swList.add(dev);
								getSwitchSequenceTdStep(swList , data, uuids, czname ,ddname);
							}else if(begin.equals("2")&&end.equals("0")){
								List<PowerDevice> swList = new ArrayList<PowerDevice>();
								swList.add(dev);
								getSwitchSequenceFdStep(swList , data, uuids, czname ,ddname);
								
								String devid = dev.getPowerDeviceID();
								cznr  =  "遥控合上"+czname+devname;
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}else if(cznr.contains("运行倒至")){
								for(RuleBaseMode rm : rbmList){
									if(rm.getPd().getDeviceType().equals(SystemConstants.Switch)){
										List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(rm.getPd(), SystemConstants.SwitchSeparate);

										for(PowerDevice dz : dzList){
											if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
												String devid = dz.getPowerDeviceID();
												devname= dz.getPowerDeviceName();

												cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
											}
										}
										
										for(PowerDevice dz : dzList){
											String devid = dz.getPowerDeviceID();
											devname= dz.getPowerDeviceName();
											
											if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
												cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
											}
										}
									}
								}
							}else{
								data.add(new String[]{"","",czname,czname,cznr,isyk,"",devname,startZT,endZT,deviceType,uuids});
							}
						}else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)){
								for(RuleBaseMode rbm : rbmList){
									if(begin.equals("1")&&end.equals("0")){
										String devid = rbm.getPd().getPowerDeviceID();
										devname = rbm.getPd().getPowerDeviceName();
										cznr = "遥控合上"+czname+CZPService.getService().getDevName(rbm.getPd());
										
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									}else if(begin.equals("0")&&end.equals("1")){
										String devid = rbm.getPd().getPowerDeviceID();
										devname = rbm.getPd().getPowerDeviceName();
										cznr = "遥控拉开"+czname+CZPService.getService().getDevName(rbm.getPd());
										
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
									}
								}
							}else{
								if(begin.equals("1")&&end.equals("0")){
									String devid = dev.getPowerDeviceID();
									
									cznr =  "合上"+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								}else if(begin.equals("0")&&end.equals("1")){
									String devid = dev.getPowerDeviceID();
									
									cznr =  "拉开"+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								}else{
									data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
								}
							}
						}else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
							String devid = dev.getPowerDeviceID();
							
							if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
								if(begin.equals("1")&&end.equals("0")){
									cznr  =  "将"+czname+CZPService.getService().getDevName(dev)+"由试验位置摇至工作位置";
									data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上",deviceType,uuids});
								}else if(begin.equals("0")&&end.equals("1")){
									cznr  =  "将"+czname+CZPService.getService().getDevName(dev)+"由工作位置摇至试验位置";
									data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
								}else{
									data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
								}
							}else{
								if(begin.equals("1")&&end.equals("0")){
									cznr =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								}else if(begin.equals("0")&&end.equals("1")){
									cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								}else{
									data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
								}
							}
						}else if(cznr.contains("所有断路器倒至")){
							List<PowerDevice> swList = new ArrayList<PowerDevice>();
							
							PowerDevice	device = CBSystemConstants.getCurRBM().getPd();
							
							if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
								swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchFHC,false, true, true, true);
							}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
								swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
							}
							
							List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
							List<PowerDevice> xlswList = new ArrayList<PowerDevice>();

							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
									zbswList.add(switchs);
								}
							}
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
									zbswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(zbswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
									xlswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(xlswList);
							
							List<PowerDevice> yxswList = new ArrayList<PowerDevice>();
							
							yxswList.addAll(xlswList);
							yxswList.addAll(zbswList);
							
							for(PowerDevice sw : yxswList){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

								for(PowerDevice dz : dzList){
									String devid = dz.getPowerDeviceID();
									
									if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
										cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									}
								}
								
								for(PowerDevice dz : dzList){
									String devid = dz.getPowerDeviceID();

									if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
										cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
									}
								}
							}
						}else if(cznr.contains("退出") || cznr.contains("投入")){
							if(cznr.contains("遥控")){
								data.add(new String[]{"","",ddname,czname,cznr,"0",dev.getPowerDeviceID(),devname,startZT,endZT,deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0",dev.getPowerDeviceID(),devname,startZT,endZT,deviceType,uuids});
							}
						}else{
							if(cim.getStationName().equals("云南省调")){
								czname = "云南省调";
								cznr = cznr.replace("落实", "落实省调");
							}
							
							if(cznr.contains("遥控")){
								data.add(new String[]{"","",ddname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}
					}else{
						String devid = dev.getPowerDeviceID();
						devname = dev.getPowerDeviceName();
						
						if(!begin.equals("")){
							startZT = RuleExeUtil.getStatusNew(dev.getDeviceType(), begin);
						}
						
						if(!end.equals("")){
							endZT = RuleExeUtil.getStatusNew(dev.getDeviceType(), end);
						}
						
						data.add(new String[]{"","",stationname,czname,cznr,"0",devid,devname,startZT,endZT,deviceType,uuids});
					}
				}else{
					data.add(new String[]{"","",stationname,stationname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
				}
			}else{
				data.add(new String[]{"","",stationname,stationname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
			}
		}
		
		return data;
	}
	
	private static List<RuleBaseMode> getRBMList(String station, String word) {
		String kuohao = "";
		word = word.replace("(", "（").replace(")", "）");
		
		if(word.contains("（")&&word.contains("）")){
			if(word.indexOf("（")<word.indexOf("）")){
				kuohao = word.substring(word.indexOf("（"),word.indexOf("）")+1);
			}
		}
		
		word = word.replace(kuohao, "");
		String wholeWord = word;
		
		/*
		 * 上级单位
		 */
		String superiorStationName = "";
		/*
		 * 下级单位
		 */
		String subordinateStationName = "";
		/*
		 * 指令单位
		 */
		String instructionStationName = "";
		
		/*
		 * 生成设备对象、初始状态、目标状态、操作、所在母线解析
		 */
		List<CardWordMode> wordRbmList = new ArrayList<CardWordMode>();
		
//		String sql="SELECT MODELDESC,BEGINSTATUS,ENDSTATUS,OPERATION " +
//				"FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL ORDER BY TO_NUMBER(ORDERID) ASC";
//	    List<Map<String,String>> modelList=DBManager.queryForList(sql);
	    
	    for(Map<String,String> model : modelList){
    		String modeldesc = StringUtils.ObjToString(model.get("MODELDESC"));
    		String beginStatus = StringUtils.ObjToString(model.get("BEGINSTATUS"));
    		String endStatus = StringUtils.ObjToString(model.get("ENDSTATUS"));
    		String operation = StringUtils.ObjToString(model.get("OPERATION"));
    		
			Object[] splitParams = SafeCheckUtilDL.init(modeldesc,'[',']');
			List<String> paramsKey=(ArrayList<String>)splitParams[1]; //标签集合
			
			if(paramsKey.size()>0){
				List<String> firstStr=(ArrayList)splitParams[0];
				String lastStr=splitParams[2].toString();

				StringBuffer descBuff=new StringBuffer();
				
				for(String first : firstStr){
					descBuff.append(first+"(.*)");
				}
				
				descBuff.append(lastStr);
				
				String regx = "^"+descBuff.toString()+"$";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
            		System.out.println("操作内容："+word);

            		String[] splitregxs = regx.replace("^", "").replace("$", "").replace("(.*)", "|").split("\\|");
            		
            		for(String string : splitregxs){
            			if(!string.equals("")){
            				if(string.equals("倒由")){
            					word = word.replace("倒", "");
            				}
            				word = word.replace(string, "");
            			}
            		}
            		
            		List<Map<String,String>> returnList = SafeCheckUtilDL.getDeviceInfoByWord(paramsKey,word);
            		
            		CardWordMode cwm = new CardWordMode();
        			
        			if(!beginStatus.equals("")){
        				cwm.setBeginStatus(beginStatus);
        			}
        				
        			if(!endStatus.equals("")){
        				cwm.setEndStatus(endStatus);
        			}
        				
        			if(!operation.equals("")){
        				cwm.setOperaTion(operation);
        			}
        			
        			List<PowerDevice> devList = new ArrayList<PowerDevice>();
        			
        			for(Map<String,String> returnMap : returnList){
        				PowerDevice dev = new PowerDevice();

        				if(returnMap.containsKey("厂站名称")){
        					dev.setPowerStationName(returnMap.get("厂站名称"));
        					instructionStationName = returnMap.get("厂站名称");
        				}
        					
        				if(returnMap.containsKey("设备名称")){
        					dev.setPowerDeviceName(returnMap.get("设备名称"));
        				}
        					
        				if(returnMap.containsKey("设备状态")){
        					if(cwm.getBeginStatus().equals("")){
        						cwm.setBeginStatus(returnMap.get("设备状态"));
        					}else if(cwm.getEndStatus().equals("")){
        						cwm.setEndStatus(returnMap.get("设备状态"));
        					}
        				}
        				
        				if(returnMap.containsKey("所在母线")){
        					cwm.setBusBar(returnMap.get("所在母线"));
        				}
        				
        				if(returnMap.containsKey("设备类型")){
        					cwm.setDeviceKind(returnMap.get("设备类型"));
        				}
        				
        				if(dev.getPowerStationName().equals("")){
        					dev.setPowerStationName(station);
        				}
        				
        				if(!dev.getPowerDeviceName().equals("")){
        					devList.add(dev);
        				}
        			}
        			
        			cwm.setPdList(devList);
        			wordRbmList.add(cwm);
        			
        			for(PowerDevice device : devList){
        				System.out.println("厂站名称："+device.getPowerStationName());
        				System.out.println("设备名称："+device.getPowerDeviceName());
        			}
        			
        			System.out.println("初始状态："+cwm.getBeginStatus());
        			System.out.println("目标状态："+cwm.getEndStatus());
        			System.out.println("操作："+cwm.getOperaTion());
        			System.out.println("所在母线："+cwm.getBusBar());
        			System.out.println("****************************************");
            		
            		break;
                }
			}
	    }
	    
		String stationID = "";
		
		if(instructionStationName.equals("")){//指令单位为空，那么下级单位取station
			subordinateStationName = station;
		}else{//指令单位不为空，那么上级单位为station，下级单位为instructionStationName
			superiorStationName = station;
			subordinateStationName = instructionStationName;
		}
		
		//厂站名称校验
		for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();){
			PowerDevice st = it.next();
			
			String modelDevName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(st));
			subordinateStationName = StringUtils.killVoltInDevName(subordinateStationName);
			
			if(modelDevName.equals(subordinateStationName)) {
				stationID = st.getPowerDeviceID();
				
				if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
					CreatePowerStationToplogy.loadFacEquip(stationID);
				}
				
				HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationID);
				
				if(devMap != null){
					break;
				}else{
					continue;
				}
			}
		}
		
		/*
		 * 只写线路的情况下特殊判断
		 */
		
		if(stationID.equals("")){
			if(!wholeWord.contains("断路器")&&!wholeWord.contains("隔离开关")&&!wholeWord.contains("接地开关")){
				for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerLine().values().iterator();it.hasNext();){
					PowerDevice line = it.next();
					
					if(line.getPowerVoltGrade() > 10){
						String lineName = CZPService.getService().getDevName(line);
						
						if(!lineName.equals("备用")&&wholeWord.contains(lineName)){
							Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(line);
							
							for(PowerDevice ln:stationlines.keySet()){
								if(ln.getPowerStationName().contains("tase")){
									continue;
								}
								
								stationID = ln.getPowerStationID();
								break;
							}
						}
					}
				}
			}
		}
		
		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();
		
		//设备名称校验
		if(!stationID.equals("")){
			if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
				CreatePowerStationToplogy.loadFacEquip(stationID);
			}
			
			HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationID);
			
			if(devMap != null){
				String devNum = "";

				for(CardWordMode cwm : wordRbmList){
					List<PowerDevice> devList = cwm.getPdList();
					
					if(cwm.getDeviceKind().equals("二次设备")){
						
					}else if(cwm.getOperaTion().contains("地线")){
						
					}else{
						for(PowerDevice device : devList){
							String equipTypeFlag = "";
							String equipTypeName = "";
							String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
									SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine,
									SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate,SystemConstants.Switch, SystemConstants.SwitchSeparate,
									SystemConstants.SwitchSeparate, SystemConstants.Switch,
									SystemConstants.Switch,SystemConstants.VolsbTransformer, SystemConstants.MotherLine,
									SystemConstants.MotherLine, SystemConstants.InOutLine,SystemConstants.InOutLine,
									SystemConstants.PowerTransformer, SystemConstants.ElecShock,
									SystemConstants.ElecCapacity ,SystemConstants.PowerTransformer};
							String[] key = new String[] { "接地刀闸","接地开关", "地刀", "隔离刀闸","隔离开关","小车开关", "小车", "刀闸", "断路器",
									"开关","PT", "母线", "母", "线","回", "主变", "电抗器", "电容器","#变"};
							for (int i = 0; i < key.length; i++) {
								if (device.getPowerDeviceName().lastIndexOf(key[i]) >= 0) {
									equipTypeFlag = type[i];
									equipTypeName = key[i];
									break;
								}
							}
							
							devNum = CZPService.getService().getDevNum(device.getPowerDeviceName());
							
							String volStr = "";
							
							if(device.getPowerDeviceName().toLowerCase().split("kv").length >= 3){
								volStr = device.getPowerDeviceName().toLowerCase().substring(device.getPowerDeviceName().toLowerCase().indexOf("kv")+2);
							}else if(device.getPowerDeviceName().contains("中性点")&&device.getPowerDeviceName().contains("接地开关")){
								volStr = "";
							}else{
								volStr = device.getPowerDeviceName();
							}
							
							//获取电压等级
							String volt = StringUtils.getVoltInDevName(volStr);
							
							PowerDevice pd = new PowerDevice();
							
							for (PowerDevice dev : devMap.values()) {
								if (!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
									continue;
								if (!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
									continue;
								if(dev.getPowerDeviceName().contains("A相")||dev.getPowerDeviceName().contains("B相")||dev.getPowerDeviceName().contains("C相"))
									continue;
								if(dev.getPowerDeviceName().contains("虚"))
									continue;
								
								if(equipTypeFlag.equals(SystemConstants.InOutLine)){
									if (CZPService.getService().getDevName(dev).equals(device.getPowerDeviceName())){
										pd = dev;
										break;
									}
								}else if (CZPService.getService().getDevNum(dev.getPowerDeviceName()).equals(devNum)&&!devNum.equals("")) {
									if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
										pd = dev;
										break;
									}
									else
										pd = dev;
								}
							}
							
							if(!pd.getPowerDeviceID().equals("")){
								String modelName = pd.getPowerDeviceName();
								String deviceName = device.getPowerDeviceName();
								String beginStatus = cwm.getBeginStatus();
								String endStatus = cwm.getEndStatus();
								
								RuleBaseMode rbm = new RuleBaseMode();
								rbm.setPd(pd);
								rbm.setBeginStatus(RuleExeUtil.getNumStatusNew(beginStatus));
								rbm.setEndState(RuleExeUtil.getNumStatusNew(endStatus));
								rbm.setBusBar(cwm.getBusBar());
								rbm.setOperaTion(cwm.getOperaTion());
								rbmList.add(rbm);
							}
						}
					}
				}
			}
		}
		
		if(rbmList.size()==0){
			RuleBaseMode rbm = new RuleBaseMode();
			PowerDevice dev = new PowerDevice();
			rbm.setPd(dev);
			rbmList.add(rbm);
		}
		
	    return rbmList;
	}
	
	public static String getVoltInDevName(String powerDeviceName) {
    	String volt = "";
    	String equipName = powerDeviceName;
    	int pos = equipName.toUpperCase().indexOf("KV");
		if (pos >= 0) {
			volt = "";
			for(int i = pos-1; i >=0; i--) {
				char ch = equipName.charAt(i);
				if (ch >= '0' && ch <= '9')
					volt = ch + volt;
				else
					break;
			}
        }
		else
			volt = "";
    	return volt;
    }
	
	public static  String getStationNameByCznr(String cznr) {
		String stationName = "";
		if(cznr.lastIndexOf("站") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("站")+1);
		else if(cznr.lastIndexOf("变") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("变")+1);
		else if(cznr.lastIndexOf("厂") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("厂")+1);
		
		if(stationName.indexOf("千伏") >= 0)
			stationName = stationName.substring(stationName.lastIndexOf("千伏")+2);
		else if(stationName.toLowerCase().indexOf("kv") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().lastIndexOf("kv")+2);
		else if(stationName.toLowerCase().indexOf("切换至") >= 0 || stationName.toLowerCase().indexOf("切至") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("至")+1);
		else if(stationName.toLowerCase().indexOf("切换到") >= 0 || stationName.toLowerCase().indexOf("切到") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("到")+1);
		else if(stationName.toLowerCase().indexOf("断开") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("断开")+2);
		else if(stationName.toLowerCase().indexOf("合上") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("合上")+2);
		return stationName;
	}
	
	/*
	 * 合并序列
	 */
	public static void getSwitchSequenceTdMerge(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
					Collections.reverse(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				if(RuleExeUtil.isDeviceChanged(zbdz)&&!zbdz.getPowerDeviceName().contains("PT")){
					String devid =  zbdz.getPowerDeviceID();
					String devname = CZPService.getService().getDevName(zbdz);
					String deviceType = zbdz.getDeviceType();
					
					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String cznr = "遥控将"+czname+CZPService.getService().getDevName(sw)+"由热备用转冷备用";
						data.add(new String[]{"","",ddname,czname,cznr,"",sw.getPowerDeviceID(),CZPService.getService().getDevName(sw),"","冷备用",deviceType,uuids});
						break;
					}else{
						String cznr = "遥控拉开"+czname+CZPService.getService().getDevName(zbdz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
					}
				}
			}
		}
	}
	
	/*
	 * 刀闸停电（分布格式）
	 */
	public static void getSwitchSequenceTdStep(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
					Collections.reverse(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				if(RuleExeUtil.isDeviceHadStatus(zbdz, "0", "1")&&!zbdz.getPowerDeviceName().contains("PT")){
					String devid =  zbdz.getPowerDeviceID();
					String devname = zbdz.getPowerDeviceName();
					String deviceType = zbdz.getDeviceType();
					
					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						if(zbdz.getPowerDeviceName().contains("隔离手车")){
							String cznr = "将"+czname+CZPService.getService().getDevName(zbdz)+"由工作位置摇至试验位置";
							data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
						}else{
							String cznr = "将"+czname+CZPService.getService().getDevName(sw)+"手车由工作位置摇至试验位置";
							data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
						}
					}else{
						String cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(zbdz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
					}
				}
			}
		}
	}
	
	/*
	 * 刀闸复电（合并格式）
	 */
	public static void getSwitchSequenceFdMerge(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
					Collections.reverse(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				String devid = zbdz.getPowerDeviceID();

				if(RuleExeUtil.isDeviceChanged(zbdz)){
					devid =  zbdz.getPowerDeviceID();
					String devname = CZPService.getService().getDevName(zbdz);
					String deviceType = zbdz.getDeviceType();
					
					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String cznr = "遥控将"+czname+CZPService.getService().getDevName(sw)+"由冷备用转热备用";
						data.add(new String[]{"","",ddname,czname,cznr,"",sw.getPowerDeviceID(),CZPService.getService().getDevName(sw),"","热备用",deviceType,uuids});
						break;
					}else{
						String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(zbdz);
						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});
					}
				}
			}
		}
	}
	
	/*
	 * 开关复电获取刀闸序列（分布格式）
	 */
	public static void getSwitchSequenceFdStep(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
					Collections.reverse(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				String devid = zbdz.getPowerDeviceID();

				if(RuleExeUtil.isDeviceHadStatus(zbdz, "1", "0")&&!zbdz.getPowerDeviceName().contains("PT")){
					devid =  zbdz.getPowerDeviceID();
					String devname = CZPService.getService().getDevName(zbdz);
					String deviceType = zbdz.getDeviceType();
					
					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String cznr  =  "将"+czname+CZPService.getService().getDevName(sw)+"手车由试验位置摇至工作位置";
						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});
					}else{
						String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(zbdz);
						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});
					}
				}
			}
		}
	}
	
	/*
	 * 传入刀闸获取序列
	 */
	public static void getSwitchSeparateSequenceTd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dz : dzList){
			if(dz.getPowerDeviceName().contains("站用变")){
				if(RuleExeUtil.isDeviceChanged(dz)){
					String devid =  dz.getPowerDeviceID();
					String devname = dz.getPowerDeviceName();

					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String dzName = CZPService.getService().getDevName(dz);
						String zybname = dzName.substring(0, dzName.indexOf("站用变")+3);
						
						String cznr = "将"+czname+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车由工作位置摇至试验位置";
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
					}else{
						String cznr = "遥控拉开"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
					}
				}
			}else{
				String devid = dz.getPowerDeviceID();
				String devname = dz.getPowerDeviceName();

				if(RuleExeUtil.isDeviceChanged(dz)){
					String cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
				}
			}
		}
	}
	
	/*
	 * 传入刀闸获取序列
	 */
	public static void getSwitchSeparateSequenceFd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dz : dzList){
			if(dz.getPowerDeviceName().contains("站用变")){
				if(RuleExeUtil.isDeviceChanged(dz)){
					String devid =  dz.getPowerDeviceID();
					String devname = dz.getPowerDeviceName();

					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String dzName = CZPService.getService().getDevName(dz);
						String zybname = dzName.substring(0, dzName.indexOf("站用变")+3);
						
						String cznr  =  "将"+czname+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车由试验位置摇至工作位置";
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
					}else{
						String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
					}
				}
			}else{
				String devid = dz.getPowerDeviceID();
				String devname = dz.getPowerDeviceName();

				if(RuleExeUtil.isDeviceChanged(dz)){
					String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
				}
			}
		}
	}
	
	public static boolean ishh(PowerDevice device){
		boolean ishh = false;

		List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(device.getPowerStationID());
		
		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getPowerVoltGrade() == device.getPowerVoltGrade()){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					hignVoltMlkgList.add(dev);
				}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
					hignVoltXlkgList.add(dev);
				}
			}
		}
		
		List<PowerDevice> tempList = new ArrayList<PowerDevice>();
		
		tempList.addAll(hignVoltXlkgList);
		tempList.addAll(hignVoltMlkgList);

		for(PowerDevice dev : tempList){
			if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
				ishh = true;
			}
		}
		return ishh;
	}
}
