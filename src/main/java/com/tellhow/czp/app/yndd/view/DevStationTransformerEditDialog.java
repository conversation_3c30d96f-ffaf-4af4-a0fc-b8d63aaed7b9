package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JTextField;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;



public class DevStationTransformerEditDialog extends javax.swing.JDialog{
	private String dfsId = "";
	private String stid = "";
	private String devid = "";
	private JComboBox zybComboBox = new JComboBox();
 	private JAutoCompleteComboBox equipComboBox = new JAutoCompleteComboBox();
 	private JAutoCompleteComboBox equipRelComboBox = new JAutoCompleteComboBox();
	private JAutoCompleteComboBox stationComboBox = new JAutoCompleteComboBox();
	private JTextField textField;

	public DevStationTransformerEditDialog(JDialog parent, boolean modal,String dfsid,String stid,String devid) {
		super(parent, modal);
		this.dfsId= dfsid;
		this.stid = stid;
		this.devid = devid;
		initComponents();
		this.setTitle("厂站关联站用变维护");
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	private void initComponents() {
		getContentPane().setLayout(null);
		
		JLabel label_3 = new JLabel("\u5382 \u7AD9 \u540D \u79F0");
		label_3.setBounds(24, 23, 99, 18);
		getContentPane().add(label_3);
		
		JLabel label_1 = new JLabel("\u8BBE \u5907 \u540D \u79F0");
		label_1.setBounds(24, 78, 113, 19);
		getContentPane().add(label_1);
		
		JLabel label = new JLabel("\u7AD9\u7528\u53D8\u540D\u79F0");
		label.setBounds(24, 134, 113, 18);
		getContentPane().add(label);
		
		JLabel lblNewLabel = new JLabel("\u7AD9\u7528\u53D8\u5173\u8054\u8BBE\u5907\u540D\u79F0");
		lblNewLabel.setBounds(27, 194, 176, 21);
		getContentPane().add(lblNewLabel);
		
		JLabel lblNewLabel_1 = new JLabel("\u8BF7\u9009\u62E9\u5173\u8054\u8BBE\u5907");
		lblNewLabel_1.setBounds(24, 261, 157, 21);
		getContentPane().add(lblNewLabel_1);
		
		JButton button = new JButton("\u786E\u5B9A");
		button.setBounds(53, 330, 57, 23);
		getContentPane().add(button);
		
		JButton button_1 = new JButton("\u53D6\u6D88");
		button_1.setBounds(142, 330, 57, 23);
		getContentPane().add(button_1);
		this.setSize(280, 435);

		button.setToolTipText("\u786e\u5b9a");
		button.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png")));
		button.setFont(new Font("宋体", Font.PLAIN, 14));
		button_1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png")));
		button_1.setToolTipText("\u53d6\u6d88");
		button_1.setFont(new Font("宋体", Font.PLAIN, 14));

		button.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button.setFocusPainted(false);
		button.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveButtonActionPerformed(evt);
			}
		});

		button_1.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button_1.setFocusPainted(false);
		
		String sql = "SELECT STATION_ID,STATION_NAME FROM "+CBSystemConstants.opcardUser+"T_SUBSTATION";
		List<Map<String,String>> stationlist = DBManager.queryForList(sql);
		
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		for(int i=0;i<stationlist.size();i++){
			Map<String,String> temp=stationlist.get(i);
			String code = StringUtils.ObjToString(temp.get("STATION_ID"));
			String name = StringUtils.ObjToString(temp.get("STATION_NAME"));
			CodeNameModel cnm=new CodeNameModel(code,name);
			model.addElement(cnm);
		}
		
		stationComboBox.addActionListener(new java.awt.event.ActionListener() {
             public void actionPerformed(java.awt.event.ActionEvent evt) {
               jComboBox1ActionPerformed(evt);
             }
        });
		
		stationComboBox.setModel(model);
		stationComboBox.setBounds(24, 41, 210, 26);
		stationComboBox.setSelectedIndex(-1);
		stationComboBox.setEditable(true);
		getContentPane().add(stationComboBox);
		
		equipComboBox.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
              jComboBox2ActionPerformed(evt);
            }
       });
		
		equipComboBox.setBounds(24, 97, 210, 26);
		equipComboBox.setSelectedIndex(-1);
		equipComboBox.setEditable(true);
		getContentPane().add(equipComboBox);
		zybComboBox.setBounds(24, 155, 210, 24);
		getContentPane().add(zybComboBox);
		
		textField = new JTextField();
		textField.setBounds(24, 219, 210, 27);
		getContentPane().add(textField);
		textField.setColumns(10);
		
		equipRelComboBox.setBounds(24, 288, 210, 27);
		getContentPane().add(equipRelComboBox);
		
		String[] zybArr = {"35kV站用变","35kV1号站用变","35kV2号站用变","35kV#1站用变","35kV#2站用变","10kV站用变","10kV1号站用变","10kV2号站用变","10kV#1站用变","10kV#2站用变","35kV1号厂用变","35kV2号厂用变","35kV1号所用变"};
		
		for(int i = 0 ;i<zybArr.length;i++){
			CodeNameModel cnm=new CodeNameModel(String.valueOf(i),zybArr[i]);
			zybComboBox.addItem(cnm);
		}
		
		button_1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				cancelButtonActionPerformed(evt);
			}
		});
		
		if(!dfsId.equals("")){
			getSelectDfsInfo(dfsId);
		}
	}
	
	private void jComboBox1ActionPerformed(ActionEvent evt) {
		CodeNameModel stationcnm = (CodeNameModel) stationComboBox.getSelectedItem();
		
		if(stationcnm!=null){
			String sql = "SELECT VOLTAGE_ID FROM "+CBSystemConstants.opcardUser+"T_VOLTAGELEVEL WHERE VOLTAGE_CODE IN ('10','35')";
			List<Map<String,String>> voltidlist=DBManager.queryForList(sql);
			
			String voltageids = "";
			
			for(int i=0;i<voltidlist.size();i++){
				Map<String,String> temp=voltidlist.get(i);
				String voltageid = StringUtils.ObjToString(temp.get("VOLTAGE_ID"));
				
				voltageids += "'"+voltageid+"',";
			}
			
			if(voltageids.endsWith(",")){
				voltageids = voltageids.substring(0, voltageids.length()-1);
			}
			
			sql = "SELECT ID AS LINEID ,NAME AS LINENAME FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND WHERE ST_ID = '"+stationcnm.getCode()+"' AND VOLTAGE_ID IN ("+voltageids+")";
			List<Map<String,String>> linelist=DBManager.queryForList(sql);
			
			String mxsql = "SELECT EQUIP_ID,EQUIP_NAME FROM "+CBSystemConstants.opcardUser+"T_EQUIPINFO WHERE STATION_ID = '"+stationcnm.getCode()+"' AND EQUIPTYPE_ID = '7' AND VOLTAGE_ID IN ("+voltageids+")";
			List<Map<String,String>> mxlist=DBManager.queryForList(mxsql);
			
			String kgdzsql = "SELECT EQUIP_ID,EQUIP_NAME FROM "+CBSystemConstants.opcardUser+"T_EQUIPINFO WHERE STATION_ID = '"+stationcnm.getCode()+"' AND EQUIPTYPE_ID IN ('26','8') AND VOLTAGE_ID IN ("+voltageids+")";
			List<Map<String,String>> kgdzList = DBManager.queryForList(kgdzsql);
			
			DefaultComboBoxModel model2 = new DefaultComboBoxModel();
			DefaultComboBoxModel model3 = new DefaultComboBoxModel();

			for(int i=0;i<linelist.size();i++){
				Map<String,String> temp=linelist.get(i);
				String code = StringUtils.ObjToString(temp.get("LINEID"));
				String name = StringUtils.ObjToString(temp.get("LINENAME"));
				CodeNameModel cnm=new CodeNameModel(code,name);
				model2.addElement(cnm);
			}
			
			for(int i=0;i<mxlist.size();i++){
				Map<String,String> temp=mxlist.get(i);
				String code = StringUtils.ObjToString(temp.get("EQUIP_ID"));
				String name = StringUtils.ObjToString(temp.get("EQUIP_NAME"));

				CodeNameModel cnm=new CodeNameModel(code,name);
				model2.addElement(cnm);
			}

			CodeNameModel cnm1 = new CodeNameModel("","");
			model3.addElement(cnm1);
			
			for(int i=0;i<kgdzList.size();i++){
				Map<String,String> temp=kgdzList.get(i);
				String code = StringUtils.ObjToString(temp.get("EQUIP_ID"));
				String name = StringUtils.ObjToString(temp.get("EQUIP_NAME"));

				CodeNameModel cnm=new CodeNameModel(code,name);
				model3.addElement(cnm);
			}
			
			equipComboBox.setModel(model2);
			equipRelComboBox.setModel(model3);
		}
	}
	
	private void jComboBox2ActionPerformed(ActionEvent evt) {
		CodeNameModel stationcnm = (CodeNameModel) stationComboBox.getSelectedItem();
		
		if(stationcnm!=null){
			String sql = "SELECT EQUIP_ID,EQUIP_NAME FROM "+CBSystemConstants.opcardUser+"T_EQUIPINFO WHERE STATION_ID = '"+stationcnm.getCode()+"'";
			List<Map<String,String>> linelist=DBManager.queryForList(sql);
			
			DefaultComboBoxModel model2 = new DefaultComboBoxModel();
			for(int i=0;i<linelist.size();i++){
				Map<String,String> temp=linelist.get(i);
				String code = StringUtils.ObjToString(temp.get("EQUIP_ID"));
				String name = StringUtils.ObjToString(temp.get("EQUIP_NAME"));
				String type = StringUtils.ObjToString(temp.get("EQUIPTYPE_ID"));

				CodeNameModel cnm=new CodeNameModel(code,name);
				model2.addElement(cnm);
			}
		}
	}
	
	//获取修改界面信息WW
	public void getSelectDfsInfo(String dfsId) {
		String sql = "SELECT DEV_ID, STATION_NAME, DEV_NAME, ZYB_NAME, ZYB_DZNAME, ZYB_DEVID,STATION_ID FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE ID ='"+dfsId+"'";
		List<Map<String,String>> zyblist=DBManager.queryForList(sql);
		
		for(Map<String,String> temp : zyblist){
			String zyb_id = "";
			String dev_id=StringUtils.ObjToString(temp.get("DEV_ID"));
			String dev_name=StringUtils.ObjToString(temp.get("DEV_NAME"));
			String station_id=StringUtils.ObjToString(temp.get("STATION_ID"));
			String station_name=StringUtils.ObjToString(temp.get("STATION_NAME"));
			String zyb_name=StringUtils.ObjToString(temp.get("ZYB_NAME"));
			String zyb_dzname=StringUtils.ObjToString(temp.get("ZYB_DZNAME"));
			
			CodeNameModel cnm=new CodeNameModel(station_id,station_name);
			stationComboBox.setSelectedItem(cnm);
			
			CodeNameModel cnm2=new CodeNameModel(dev_id,dev_name);
			equipComboBox.setSelectedItem(cnm2);
			
			if(zyb_name.equals("35kV1号站用变")){
				zyb_id = "1";
			}else{
				zyb_id = "2";
			}
			
			CodeNameModel cnm3=new CodeNameModel(zyb_id,zyb_name);
			equipComboBox.setSelectedItem(cnm3);
		}
	}
	
	//确定
	private void saveButtonActionPerformed(java.awt.event.ActionEvent evt) {
		//先删除原来的数据
		String sql = "DELETE "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE ID = '"+dfsId+"'";
		DBManager.execute(sql);
		
		//再插入新数据
		String id = StringUtils.ObjToString(UUID.randomUUID());
		String devid = ((CodeNameModel)equipComboBox.getSelectedItem()).getCode();
		String devname = ((CodeNameModel)equipComboBox.getSelectedItem()).getName();
		String devrelname = textField.getText();
		String devrelid = "";
		if(!equipRelComboBox.getSelectedItem().equals("")){
			devrelid = ((CodeNameModel)equipRelComboBox.getSelectedItem()).getCode();
		}
		String stationid = ((CodeNameModel)stationComboBox.getSelectedItem()).getCode();
		String stationname = ((CodeNameModel)stationComboBox.getSelectedItem()).getName();
		String zybname = ((CodeNameModel)zybComboBox.getSelectedItem()).getName();

		sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_STATIONZYB (ID,DEV_ID, STATION_NAME, DEV_NAME, ZYB_NAME, STATION_ID,ZYB_DZNAME,ZYB_DEVID)"
				+ "VALUES ('"+id+"','"+devid+"', '"+stationname+"', '"+devname+"', '"+zybname+"', '"+stationid+"','"+devrelname+"','"+devrelid+"')";
		DBManager.execute(sql);

		this.setVisible(false);
		this.dispose();
	}
	
	public void paint(Graphics g) {
		super.paint(g);
		Graphics2D g_2d = (Graphics2D) g;
		g_2d.setColor(Color.GRAY);
		g_2d.drawLine(20, 40, this.getSize().width - 20, 40);

	}
	//取消
	private void cancelButtonActionPerformed(java.awt.event.ActionEvent evt) {
		this.setVisible(false);
		this.dispose();
	}
}