package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.List;

import com.sun.java.help.search.Rule;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPESMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱双母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC +","+ CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			List<PowerDevice> plkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL +","+ CBSystemConstants.RunTypeSwitchMLPL, "", false, true, true, true);

			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
					mxList.add(curDev);
					RuleExeUtil.swapDeviceList(mxList);
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(mxList)+"并列运行正常/r/n";
					
					replaceStr += "普洱地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
					replaceStr += "普洱地调@检查"+stationName+CZPService.getService().getDevName(dev)+"三相潮流指示正常/r/n";
				}
			}
			
			if(TicketKindChoose.flag.equals("全部手动")){
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
						replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(curDev.getDeviceStatus().equals("2")){
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
			}else if(TicketKindChoose.flag.equals("全部程序化")){
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(curDev.getDeviceStatus().equals("2")){
					replaceStr += "普洱地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";

					for(PowerDevice dev : zbkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dz, stationName);
							}
						}
					}
					
					for(PowerDevice dev : xlkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dz, stationName);
							}
						}
					}
					
					for(PowerDevice dev : mlkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dz, stationName);
							}
						}
					}
					
					List<PowerDevice> ptdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

					for(PowerDevice dev : ptdzList){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
							if(dev.getPowerDeviceName().contains("901") || dev.getPowerDeviceName().contains("902")){
								replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev, stationName);
							}
						}
					}
				}
			}else if(TicketKindChoose.flag.equals("部分程序化")){
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				List<PowerDevice> ptdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : ptdzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
						if(dev.getPowerDeviceName().contains("901") || dev.getPowerDeviceName().contains("902")){
							replaceStr += "普洱地调@遥控拉开"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev, stationName);
						}
					}
				}
				
				for(PowerDevice dev : mlkgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					dzList = RuleExeUtil.sortByCZMXC(dzList);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
							replaceStr += "普洱地调@遥控拉开"+stationName+CommonFunctionPE.getSequentialDeviceName(dz)+"/r/n";
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dz, stationName);
						}
					}
				}
				
				for(PowerDevice dev : zbkgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dz, stationName);
						}
					}
				}
				
				for(PowerDevice dev : xlkgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dz, stationName);
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
