package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJDMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江单母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);
			
			List<PowerDevice> curmlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> curxlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true);
			List<PowerDevice> curzbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, false, true);

			if(curmlkgList.size()>0){//分段
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
					List<PowerDevice> zbList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice dev : zbList){
						List<PowerDevice> zbzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

						List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
						List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);

						List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
						List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
						List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();

						for(PowerDevice zbdyckg : zbdyckgList){
							dycmxList = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

							for(PowerDevice mx : dycmxList){
								dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
							}
						}
						
						for(PowerDevice zbzyckg : zbzyckgList){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
							
							for(PowerDevice mx : mxList){
								zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
							}
						}
						
						for(PowerDevice gycmlkg : curmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("1")){
								replaceStr += "退出"+(int)gycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
							
						for(PowerDevice zbzxdjddz : zbzxdjddzList){
							replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(zbzxdjddz)+"/r/n";
							replaceStr += "丽江地调@检查"+stationName+CZPService.getService().getDevName(zbzxdjddz)+"在合上位置/r/n";
						}
						
						for(PowerDevice gycmlkg : curmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("1")){
								replaceStr += CommonFunctionLJ.getHhContent(gycmlkg,"丽江地调",stationName);
							}
						}
						
						for(PowerDevice zycmlkg : zycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(zycmlkg).equals("1")){
								replaceStr += CommonFunctionLJ.getHhContent(zycmlkg,"丽江地调",stationName);
							}
						}
						
						for(PowerDevice zbzyckg : zbzyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(zbzyckg).equals("0")){
								replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbzyckg)+"/r/n";
							}
						}
						
						for(PowerDevice dycmlkg : dycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
								replaceStr += CommonFunctionLJ.getHhContent(dycmlkg,"丽江地调",stationName);
							}
						}
						
						for(PowerDevice zbdyckg : zbdyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
								replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
							}
						}
						
						for(PowerDevice zbgyckg : zbgyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(zbgyckg).equals("0")){
								replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbgyckg)+"/r/n";
							}
						}
						
						for(PowerDevice curmlkg : curmlkgList){
							if(RuleExeUtil.isDeviceHadStatus(curmlkg, "0", "1")){
								replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(curmlkg)+"/r/n";
							}
						}
						
						for(PowerDevice curxlkg : curxlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(curxlkg).equals("0")){
								replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(curxlkg)+"/r/n";
							}
						}
					}
				}else{//负荷侧
					for(PowerDevice zbdyckg : curzbkgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
							replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
					
					for(PowerDevice zbdyckg : curmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
							replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
					
					for(PowerDevice zbdyckg : curxlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
							replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
				}
			}else{
				List<PowerDevice> zbList = new ArrayList<PowerDevice>();
				List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();

				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						
						if(!dev.getPowerDeviceName().contains("接地变")){
							zbList.add(dev);
						}
					}else if (dev.getDeviceType().equals(SystemConstants.MotherLine) && dev.getPowerVoltGrade() == 10){
						dycmxList.add(dev);
					}
				}
				
				if(station.getPowerVoltGrade() == 35){
					replaceStr += "丽江配调@落实"+stationName+"10kV负荷已转供，"+CZPService.getService().getDevName(dycmxList)+"具备停电条件/r/n";
				}
				
				for(PowerDevice dev : zbList){
					List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
					List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
					List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
					
					for(PowerDevice zbdyckg : zbdyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
							replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
					
					for(PowerDevice zbzyckg : zbzyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbzyckg).equals("0")){
							replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbzyckg)+"/r/n";
						}
					}
					
					for(PowerDevice zbgyckg : zbgyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbgyckg).equals("0")){
							replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbgyckg)+"/r/n";
						}
					}
				}
				
				for(PowerDevice dev : curxlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
				replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
