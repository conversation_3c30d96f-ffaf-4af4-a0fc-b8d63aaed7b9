package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJSMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江双母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			replaceStr += stationName+"@核实"+deviceName+"相关工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，设备的二次装置已正常投入，确认"+deviceName+"具备复电条件/r/n";
			
			PowerDevice othermx = new PowerDevice();
			PowerDevice mlkg = new PowerDevice();

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.isSwitchDoubleML(dev)){
					mlkg = dev;
					
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					for(PowerDevice mx : mxList){
						if(!mx.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
							othermx = mx ;
							break;
						}
					}
				}
			}
			
			String othermxName = CZPService.getService().getDevName(othermx);
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			
			String mlkgName = CZPService.getService().getDevName(mlkg);
			
			replaceStr += stationName+"@将"+mlkgName+"由冷备用转热备用/r/n";
			replaceStr += stationName+"@投入"+mlkgName+"充电保护/r/n";
			replaceStr += "丽江地调@遥控合上"+stationName+mlkgName+"/r/n";
			replaceStr += stationName+"@退出"+mlkgName+"充电保护/r/n";
			
			List<PowerDevice> yxList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbyList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbkgList){
				if(dev.getDeviceStatus().equals("0")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							yxList.add(dev);
							break;
						}
					}
				}else if(dev.getDeviceStatus().equals("1")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							rbyList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : xlkgList){
				if(dev.getDeviceStatus().equals("0")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							yxList.add(dev);
							break;
						}
					}
				}else if(dev.getDeviceStatus().equals("1")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							rbyList.add(dev);
							break;
						}
					}
				}
			}
			
			String devName = "";

			for(PowerDevice dev : yxList){
				devName += CZPService.getService().getDevName(dev)+"、";
			}
			
			if(devName.endsWith("、")){
				devName = devName.substring(0, devName.length()-1);
			}
			
			if(!devName.equals("")){
				replaceStr += stationName+"@将"+devName+"由"+deviceName+"运行倒至"+othermxName+"运行/r/n";
			}

			devName = "";
			
			for(PowerDevice dev : rbyList){
				devName = CZPService.getService().getDevName(dev);
			}
			
			if(devName.endsWith("、")){
				devName = devName.substring(0, devName.length()-1);
			}
			
			if(!devName.equals("")){
				replaceStr += stationName+"@将"+devName+"由"+deviceName+"热备用倒至"+othermxName+"热备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
