package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrCloseThreeSwitchSeparate implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		
		if("合上三刀闸".equals(tempStr)){
			List<PowerDevice> list =  RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, "", true, true, false);
			List<PowerDevice> list2 =  RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
		
			if(list.size() > 2){
				for(PowerDevice dev : list){
					if(!list2.contains(dev)){
						replaceStr = ",合上"+CZPService.getService().getDevName(dev);
					}
				}
			}
		}
		
		return replaceStr;
	}
	
}
