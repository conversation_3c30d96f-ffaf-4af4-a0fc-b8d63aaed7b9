package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.TransformKDXLChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKMXLRBYTOLBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昆明线路由热备用转冷备用".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			if(loadLineTrans.size()>0){
				RuleExeUtil.swapDeviceByLowVoltList(loadLineTrans);
				
				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					
					List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
					List<PowerDevice> zbList =  RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, "", true, false, true);
					List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					List<PowerDevice> zbkglist = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDYC + "," + CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);

					if(xlkgList.size()>0){
						if(dev.getPowerVoltGrade() == 220&&station.getPowerVoltGrade() == 220){
							if(!xlkgList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
								ReplaceStrTC220KVBZTZZ bzt220kv = new ReplaceStrTC220KVBZTZZ();
								String temp = bzt220kv.strReplace("退出220kV备自投装置", dev, dev, desc);
								
								if(temp!=null){
									replaceStr +=  temp;
								}
							}
							
						}else if(dev.getPowerVoltGrade() == 110&&station.getPowerVoltGrade() == 110){
							if(zbList.size()>0){
								if(!RuleExeUtil.isTransformerXBZ(zbList.get(0))&&!RuleExeUtil.isTransformerXBDY(zbList.get(0))){
									replaceStr +=  CZPService.getService().getDevName(station)+"@退出110kV备自投装置/r/n";
								}
							}else{
								replaceStr +=  CZPService.getService().getDevName(station)+"@退出110kV备自投装置/r/n";
							}
						}else if(dev.getPowerVoltGrade() == 35&&station.getPowerVoltGrade() == 35){
							ReplaceStrTC35KVBZTZZ bzt35kv = new ReplaceStrTC35KVBZTZZ();
							String temp = bzt35kv.strReplace("退出35kV备自投装置", dev, dev, desc);
							
							if(temp!=null){
								replaceStr +=  temp;
							}else{
								List<PowerDevice> mlkg35kVList = new ArrayList<PowerDevice>();
								List<PowerDevice> lineList = new ArrayList<PowerDevice>();

								HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(dev.getPowerStationID());

								for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
									PowerDevice dev2 = it2.next();
									if (dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										if(dev2.getPowerVoltGrade() == 35){
											mlkg35kVList.add(dev2);
										}
									}
									
									if (dev2.getDeviceType().equals(SystemConstants.InOutLine)
											&&dev2.getPowerVoltGrade() == station.getPowerVoltGrade()
											&&!dev2.getPowerDeviceName().contains("备用")){
										lineList.add(dev2);
									}
								}
								
								if(lineList.size()>1){
									replaceStr +=  CZPService.getService().getDevName(station)+"@退出35kV备自投装置/r/n";
								}
							}
						}
					}
					
					if(zbList.size()>0 && dev.getPowerVoltGrade() < 500 && station.getPowerVoltGrade() < 500){
						if(RuleExeUtil.isTransformerXBZ(zbList.get(0))||RuleExeUtil.isTransformerXBDY(zbList.get(0))){
							ReplaceStrTC10KVBZTZZ bzt10kv = new ReplaceStrTC10KVBZTZZ();
							String temp = bzt10kv.strReplace("退出10kV备自投装置", dev, dev, desc);
							
							if(temp!=null){
								replaceStr +=  temp;
							}
							replaceStr +=  CZPService.getService().getDevName(station)+"@将"+CZPService.getService().getDevName(zbList)+"由热备用转冷备用/r/n";
							continue;
						}
					}
					
					
					if(mxlist.size()>0){
						if(mxlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
							xlkgList.addAll(zbkglist);
							
							for(PowerDevice xlkg : xlkgList){
								if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
									if(RuleExeUtil.isDeviceHadStatus(xlkg, "1", "2")){
										replaceStr += CZPService.getService().getDevName(station)+"@将"+CZPService.getService().getDevName(xlkg)+"由热备用转冷备用/r/n";
									}
								}
							}
							
							for(PowerDevice xlkg : xlkgList){
								if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
									if(RuleExeUtil.isDeviceHadStatus(xlkg, "1", "2")){
										replaceStr += CZPService.getService().getDevName(station)+"@将"+CZPService.getService().getDevName(xlkg)+"由热备用转冷备用/r/n";
									}
								}
							}
						}else{
							for(PowerDevice xlkg : xlkgList){
								if(RuleExeUtil.isDeviceHadStatus(xlkg, "1", "2")){
									replaceStr += CZPService.getService().getDevName(station)+"@将"+CZPService.getService().getDevName(xlkg)+"由热备用转冷备用/r/n";
								}
							}
						}
					}
				}
			}
			
			String curLineId = curDev.getPowerDeviceID();
			String devName = CZPService.getService().getDevName(curDev);
			
			TransformKDXLChoose kdxl = new TransformKDXLChoose();
			
			String sql = "SELECT DEVICE_NUM,UNIT,OPERATION_KIND,LOWERUNIT,ENDPOINT_KIND FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+curLineId+"')";
			List<Map<String, Object>> stations = DBManager.queryForList(sql);
			
			if(stations != null && stations.size()>0) {
				
				if(kdxl.retMap.size()>0){
					for(Iterator<Map<String, Object>> itor = stations.iterator();itor.hasNext();){
						Map<String, Object> map = itor.next();
						String stationName = StringUtils.ObjToString(map.get("UNIT"));

						if(!kdxl.retMap.containsKey(stationName)){
							itor.remove();
						}
					}
				}
				
				for(Map<String, Object> station:stations) {
					String stationName = StringUtils.ObjToString(station.get("UNIT")).trim();
					String stationKind = StringUtils.ObjToString(station.get("OPERATION_KIND")).trim();
					String deviceNum = StringUtils.ObjToString(station.get("DEVICE_NUM")).trim();
					String lowerunit = StringUtils.ObjToString(station.get("LOWERUNIT")).trim();
					String endpointkind = StringUtils.ObjToString(station.get("ENDPOINT_KIND")).trim();

					if(stationKind.equals("下令")){
						if(endpointkind.equals("外接站用变")){
							replaceStr += stationName+"@将"+devName+ deviceNum +"断路器由热备用转冷备用/r/n";
						}else{
							if(lowerunit.equals("")){
								replaceStr += stationName + "@将"+devName+ deviceNum +"断路器由热备用转冷备用/r/n";
							}else{
								replaceStr += stationName + "@将"+lowerunit+devName+ deviceNum +"断路器由热备用转冷备用/r/n";
							}
						}
					}else if(stationKind.equals("落实")){
						if(endpointkind.equals("其它供电局")){
							replaceStr += stationName + "@落实"+ devName +"已由热备用转冷备用/r/n";
						}
					}else if(stationKind.equals("许可")){
						replaceStr += stationName + "@许可将"+ devName +"由热备用转冷备用/r/n";
					}
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				
				List<PowerDevice> loadLineTransSwitchList = RuleExeUtil.getDeviceList(sourceLineTrans,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
				List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);

				if(mxlist.size()>0){
					if(mxlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						for(PowerDevice loadLineTransSwitch : loadLineTransSwitchList){
							if(RuleExeUtil.isDeviceHadStatus(loadLineTransSwitch, "1", "2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(loadLineTransSwitch)+"由热备用转冷备用/r/n";
							}
						}
					}else if(mxlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
						for(PowerDevice loadLineTransSwitch : loadLineTransSwitchList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(loadLineTransSwitch)){
								if(RuleExeUtil.getDeviceBeginStatus(loadLineTransSwitch).equals("0")){
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(loadLineTransSwitch)+"由热备用转冷备用/r/n";
								}
							}
						}
						
						for(PowerDevice loadLineTransSwitch : loadLineTransSwitchList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(loadLineTransSwitch)){
								if(RuleExeUtil.getDeviceBeginStatus(loadLineTransSwitch).equals("0")){
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(loadLineTransSwitch)+"由热备用转冷备用/r/n";
								}
							}
						}
					}else{
						for(PowerDevice loadLineTransSwitch : loadLineTransSwitchList){
							if(RuleExeUtil.isDeviceHadStatus(loadLineTransSwitch, "1", "2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(loadLineTransSwitch)+"由热备用转冷备用/r/n";
							}
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
