package com.tellhow.czp.app.yndd.rule.xsbn;

import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

public class XSBNTicket<PERSON>indChoose implements RulebaseInf {
	public static String flag = "";
	@Override
	public boolean execute(RuleBaseMode rbm) {
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
		
		flag = "";
		
		if(pd.getPowerVoltGrade() == 500){
			return true;
		}
		
		String[] arr = {"全部程序化","部分程序化","全部手动"};
		
		int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择操作票类型", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
		if(sel==0){
			flag =  "全部程序化";
		}else if(sel==1){
			flag =  "部分程序化";
		}else if(sel==2){
			flag =  "全部手动";
		}else if(sel==-1){
			return false;
		}
		
		return true;
	}
}
