package com.tellhow.czp.app.yndd.wordcard.nj;


import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionNJ;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrNJLKDZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("怒江拉开刀闸".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			if(CommonFunctionNJ.ifSwitchSeparateControl(curDev)){
				List<PowerDevice> dzList = new ArrayList<PowerDevice>();
				dzList.add(curDev);
				
				replaceStr += "怒江地调@遥控拉开"+stationName+deviceName+"/r/n";
				replaceStr += CommonFunctionNJ.getKnifeOffCheckContent(dzList , stationName);
			}else{
				replaceStr += stationName+"@拉开"+deviceName+"/r/n";
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
