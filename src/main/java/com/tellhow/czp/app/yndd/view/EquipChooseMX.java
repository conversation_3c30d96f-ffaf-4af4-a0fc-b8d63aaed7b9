package com.tellhow.czp.app.yndd.view;

import java.awt.Toolkit;
import java.text.Collator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Vector;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JOptionPane;
import javax.swing.JTable;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumn;

import com.sun.org.apache.bcel.internal.generic.NEW;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.dao.GZCBSystemConstants;
import com.tellhow.czp.mainframe.EachRowEditor;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.view.ColorTableModel;
import czprule.rule.view.EquipListView;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;
import czprule.wordcard.dao.DeviceStateMentManager;
import czprule.wordcard.view.InitDeviceTypeChockBox;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013-7-11 上午11:38:02 
 */
public class EquipChooseMX extends javax.swing.JDialog {
	private List<PowerDevice> equipList = new ArrayList<PowerDevice>();
	private Vector<Object> rowData = new Vector<Object>();
	private Vector<Object> rowDataDefault = new Vector<Object>();
	private Map<PowerDevice ,String>tagStatusMap = new HashMap<PowerDevice, String>();
//	private List<String> expStatusList;

	/** Creates new form EquipCheckChoose */
	public EquipChooseMX(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, String showMessage) {
		super(parent, modal);

		if (equipsList != null)
			this.equipList = equipsList;
		initComponents();
		this.jLabel1.setText(showMessage);
		
		this.initTable();
		FitTableColumns(jTable1);
		this.setLocationCenter();
		this.setVisible(true);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">
    private void initComponents() {

        jLabel1 = new javax.swing.JLabel();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        jButton1 = new javax.swing.JButton();
        jButton2 = new javax.swing.JButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

        jLabel1.setText("jLabel1");

        jScrollPane1.setViewportView(jTable1);

        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/ok.png"))); // NOI18N
        jButton1.setToolTipText("确定");
        jButton1.setText("确定");
        jButton1.setMargin(new java.awt.Insets(1,1,1,1));
        jButton1.setFocusPainted(false);
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
        jButton2.setToolTipText("取消");
        jButton2.setText("取消");
        jButton2.setMargin(new java.awt.Insets(1,1,1,1));
        jButton2.setFocusPainted(false);
        jButton2.setVisible(false);
        jButton2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jLabel1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 450, Short.MAX_VALUE)
            .add(layout.createSequentialGroup()
                .addContainerGap(org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .add(jButton1)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jButton2)
                .add(5, 5, 5))
            .add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 295, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .add(jLabel1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 24, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED, 22, Short.MAX_VALUE)
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.TRAILING)
                    .add(jButton2)
                    .add(jButton1))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jScrollPane1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 120+equipList.size()*15, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
        );

        pack();
    }// </editor-fold>

    private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
        // TODO add your handling code here:
		this.setVisible(false);
		this.dispose();
    }


	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
//		CBSystemConstants.tagStatusMap.clear();
		

//		 for(int i=0;i<rowData.size();i++){
//		    	
//		    	PowerDevice paraPD =  (PowerDevice)para[3];
//		 }
//				
		
		Map<PowerDevice, String> endStatusMap = new LinkedHashMap<PowerDevice, String>();
		
		for (int i = 0; i < jtablemodel.getRowCount(); i++) {
			String  endstatue = (String)jtablemodel.getValueAt(i, 2);
			Object[] para = (Object[])rowDataDefault.get(i);
			if( (!endstatue.equals("")) &&  (!para[1].equals("")) && (!endstatue.equals(para[1])) ){
				tagStatusMap.put((PowerDevice)para[0], endstatue);
			}
			
			//放到全局变量里，用来出术语
			if(!endstatue.equals("")){
				endStatusMap.put((PowerDevice)para[0], endstatue);	
			}
	
		}
		
	    //这里将endStatusMap.entrySet()转换成list
        List<Map.Entry<PowerDevice,String>> list = new ArrayList<Map.Entry<PowerDevice,String>>(endStatusMap.entrySet());
        //然后通过比较器来实现排序
        Collections.sort(list,new Comparator<Map.Entry<PowerDevice,String>>() {
            //升序排序
            public int compare(Entry<PowerDevice, String> o1,
                    Entry<PowerDevice, String> o2) {
                return o1.getValue().compareTo(o2.getValue());
            }
            
        });
        endStatusMap.clear();
		for(Map.Entry<PowerDevice,String> mapping:list){
			endStatusMap.put(mapping.getKey(), mapping.getValue());
		}
		
		GZCBSystemConstants.setMXYXFSmap(endStatusMap);
		
		
		this.setVisible(false);
		this.dispose();
	}

	public void initTable() {
		jtablemodel = new ColorTableModel();

		PowerDevice pd = null;
		jtablemodel.setRowTitle(new String[] { "厂站名称", "设备名称", "挂接母线（可选）"});
		jTable1.setModel(jtablemodel);
		jTable1.setRowHeight(20);

		CommonSearch cs=new CommonSearch();
	    Map<String,Object> inPara = new HashMap<String,Object>();
        Map<String,Object> outPara = new HashMap<String,Object>();
        List<PowerDevice> mxList=null;
        List<PowerDevice> xlkgList=null;
		
	    EachRowEditor rowEditor = new EachRowEditor(jTable1);
	    for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			PowerDevice xlkgDev = null;
			
			if(pd.getDeviceType().equals(SystemConstants.Switch)){
				xlkgDev = pd;
			}
			else{
				inPara.clear();
				inPara.put("oprSrcDevice", pd);
		        inPara.put("tagDevType", SystemConstants.Switch); 
		        cs.execute(inPara, outPara);
		        xlkgList = (ArrayList) outPara.get("linkedDeviceList");
				for(int j=0;j<xlkgList.size();j++){
					if(xlkgList.get(j).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						xlkgDev = xlkgList.get(j);
						break;
					}
				}
			}
	
	
			if(xlkgDev==null){
				JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "未找到"+pd+"连接的开关！", "提示", JOptionPane.WARNING_MESSAGE);
				return;
			}
			
			
			
			
			inPara.clear();
			inPara.put("oprSrcDevice", xlkgDev);
	        inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
	        inPara.put("isSearchOffPath", false); 
	    	inPara.put("isStopOnDiffVolt", true);
	        cs.execute(inPara, outPara);
	        mxList = (ArrayList) outPara.get("linkedDeviceList");
	       
			
	        
	        String defaultStatus ="";
			if(mxList.size()>0){
				defaultStatus =  mxList.get(0).getPowerDeviceName();
			}else{
//				ShowMessage.view(pd.getPowerDeviceName()+"未找到当前运行母线！");
//				return;
				defaultStatus ="";
				continue;//没有找到当前运行
			}
			

			String devName =CZPService.getService().getDevName(pd);
			if(pd.getDeviceStatus().equals("1")){
				devName+="（热备用）";
			}
			
			rowData.add(new Object[] { CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pd.getPowerStationID())), new CodeNameModel(pd.getPowerDeviceID(),devName), defaultStatus ,pd});
	    }
	    
	    
	    //按母线编号从小到大排序
	    if(rowData.size()>1){
	    	 Collator collator = Collator.getInstance(Locale.CHINA);
	    	 for(int j=1;j<rowData.size();j++){
	    		 for(int i=j;i<rowData.size();i++){
	    		    	
		    		 Object[] para1 = (Object[])rowData.get(j-1);
		    		 String  pa1 = (String)para1[2];
		    		 Object[] para2 = (Object[])rowData.get(i);
		    		 String  pa2 = (String)para2[2];
		    		 int result = collator.compare(pa1, pa2);
		    		 if(result>0){
		    			 Collections.swap(rowData, j-1, i);
		    		 }
		 	    } 
	    	 }
	    }
	    for(int i=0;i<rowData.size();i++){
	    	 Object[] para1 = (Object[])rowData.get(i);
	    	 rowDataDefault.add(new Object[]{para1[3],para1[2]});
	    }
	    
	   
	    
	    
	    for(int i=0;i<rowData.size();i++){
	    	Object[] para = (Object[])rowData.get(i);
	    		
	    	PowerDevice paraPD =  (PowerDevice)para[3];
	    	inPara.put("oprSrcDevice", paraPD);
	 	    inPara.put("isSearchOffPath", true); 
	 	    cs.execute(inPara, outPara);
	 	    mxList = (ArrayList) outPara.get("linkedDeviceList");
	 	    
	 	    List<String> orderList = new ArrayList<String>();
	 	    JComboBox cb = new JComboBox();
	 	    cb.addItem("");
	 	    for(int j=0;j<mxList.size();j++){
	 	    	if(!mxList.get(j).getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)&&mxList.get(j).getPowerVoltGrade()==paraPD.getPowerVoltGrade()){
	 	    		orderList.add(mxList.get(j).getPowerDeviceName());
	 	    	}
	 	    }
	 	    Collections.sort(orderList);
	 	    for(int j=0;j<orderList.size();j++){
	 	    	cb.addItem(orderList.get(j));
	 	    }
	 	    
	 		cb.setSelectedItem(para[2]);
	 		rowEditor.setEditorAt(i, new DefaultCellEditor(cb));
	 		
	    }
	    
	    
	    
	    
	    
	    
	    
	    jTable1.getColumnModel().getColumn(2).setCellEditor(rowEditor);
	    jtablemodel.setRowData(rowData);
	}

	public Map getTagStatusMap() {
		return tagStatusMap;
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				EquipListView dialog = new EquipListView(
						new javax.swing.JFrame(), true, null, null);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}
	
	
	
	
	
	
	
	
	
	
	
	

    // Variables declaration - do not modify
    private javax.swing.JButton jButton1;
    private javax.swing.JButton jButton2;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTable jTable1;
    // End of variables declaration

	private ColorTableModel jtablemodel;
	
	//Jtable列宽自适应
	public void FitTableColumns(JTable myTable){  
	    JTableHeader header = myTable.getTableHeader();  
	     int rowCount = myTable.getRowCount();  
	  
	     Enumeration columns = myTable.getColumnModel().getColumns();  
	     while(columns.hasMoreElements()){  
	         TableColumn column = (TableColumn)columns.nextElement();  
	         int col = header.getColumnModel().getColumnIndex(column.getIdentifier());  
	         int width = (int)myTable.getTableHeader().getDefaultRenderer()  
	                 .getTableCellRendererComponent(myTable, column.getIdentifier()  
	                         , false, false, -1, col).getPreferredSize().getWidth();  
	         for(int row = 0; row<rowCount; row++){  
	             int preferedWidth = (int)myTable.getCellRenderer(row, col).getTableCellRendererComponent(myTable,  
	               myTable.getValueAt(row, col), false, false, row, col).getPreferredSize().getWidth();  
	             width = Math.max(width, preferedWidth)+2;  
	         }  
	         header.setResizingColumn(column); // 此行很重要  
	         column.setWidth(width+myTable.getIntercellSpacing().width);  
	     }  
	}  
}