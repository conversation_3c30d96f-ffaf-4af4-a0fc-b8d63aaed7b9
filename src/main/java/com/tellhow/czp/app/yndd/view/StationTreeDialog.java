package com.tellhow.czp.app.yndd.view;


import java.awt.BorderLayout;
import java.awt.Button;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

import org.w3c.dom.Element;

import com.tellhow.graphicframework.algorithm.ElementSearch;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.stationstartup.StationDeviceToplogy;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;



public class StationTreeDialog extends javax.swing.JDialog {
	private DefaultTableModel dTableModel;
	private JPanel topPanel;//查询条件及按钮面板
	private JPanel mainPanel;//信息面板
	private JTextField searchText;//查询框
	private JButton searchButton;//查询按钮
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTableInfo;//信息列表

	public StationTreeDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("厂站树维护");
		setLocationCenter();
		initTable("");
	}

	/**
	 * 初始化表格  传入参数关键字
	 */
	public void initTable(String gjz) {
		dTableModel.setRowCount(0);
		
		String sql = "SELECT A.STATION_ID,A.STATION_NAME,A.VOLTAGE_ID,DECODE(B.ISREMOVE,'','未启用','1','未启用','已启用') AS STATUS FROM "+CBSystemConstants.equipUser+"T_SUBSTATION A"
				+ " LEFT JOIN "+CBSystemConstants.equipUser+"T_SUBSTATION_TREE B ON A.STATION_ID = B.STATIONID";
		
		 if(!gjz.equals("")){
			 sql+=" WHERE A.STATION_NAME LIKE '%"+gjz+"%' ";
		 }	
		 
		 sql += " ORDER BY TO_NUMBER(REPLACE(B.VOLT,'kV','')) DESC";
		 
		 List<Map<String,String>> results=DBManager.queryForList(sql);

		 for (int i = 0; i < results.size(); i++) {
			 Map<String,String> temp = results.get(i);
			 String stationId = StringUtils.ObjToString(temp.get("STATION_ID"));
			 String stationName = StringUtils.ObjToString(temp.get("STATION_NAME"));
			 String voltageId = StringUtils.ObjToString(temp.get("VOLTAGE_ID"));
			 String status = StringUtils.ObjToString(temp.get("STATUS"));

			 Object[] rowData = {stationId,i+1,stationName,voltageId,status};
			 dTableModel.addRow(rowData);
		 }

		jTableInfo.setModel(dTableModel);
		DefaultTableCellRenderer  r  =  new  DefaultTableCellRenderer();   
		r.setHorizontalAlignment(JTextField.CENTER);   
		jTableInfo.getColumn("序号").setCellRenderer(r);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}


	private void initComponents() {
		getContentPane().setLayout(new BorderLayout());
		this.setSize(700, 480);
		topPanel =new JPanel();
		topPanel.setPreferredSize(new Dimension(0,45));
		getContentPane().add(topPanel,BorderLayout.NORTH);
		mainPanel =new JPanel();
		getContentPane().add(mainPanel,BorderLayout.CENTER);
		
		JLabel label1 = new JLabel("厂站关键字:");
		JLabel label2 = new JLabel("");//增加空位用
		label2.setPreferredSize(new Dimension(60,0));
		topPanel.setLayout(new FlowLayout(FlowLayout.CENTER,10,10));
		searchText =new JTextField();
		searchText.setPreferredSize(new Dimension(200,20));
		searchButton =new JButton("查询");
		searchButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				searchButtonActionPerformed(evt);
			}
		});
		
		topPanel.add(label1);
		topPanel.add(searchText);
		topPanel.add(searchButton);
		topPanel.add(label2);
		
		dTableModel = new DefaultTableModel(null,new String[] { "ID","序号", "厂站名称","电压等级ID","是否启用"}){
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		jTableInfo = new JTable();
		jTableInfo.setModel(dTableModel);
		
		jTableInfo.getColumnModel().getColumn(0).setMinWidth(0);
		jTableInfo.getColumnModel().getColumn(0).setMaxWidth(0);
		jTableInfo.getColumnModel().getColumn(1).setPreferredWidth(50);
		jTableInfo.getColumnModel().getColumn(1).setMaxWidth(50);
		jTableInfo.getColumnModel().getColumn(2).setPreferredWidth(200);
		
		jTableInfo.setRowHeight(26);
		jScrollPane1 = new JScrollPane(jTableInfo);
		jScrollPane1.setPreferredSize(new Dimension(600, 370));
		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));
		mainPanel.add(jScrollPane1,BorderLayout.CENTER);
		

		jTableInfo.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				jTableInfoMouseClicked(evt);
			}
		});
	}
	
	//查询
	private void searchButtonActionPerformed(java.awt.event.ActionEvent evt) {
		this.initTable(searchText.getText().trim());
	}

	/**
	 * 双击table中一行数据，弹出修改页面
	 * @param e
	 */
	private void jTableInfoMouseClicked(MouseEvent e){
		if(e.getClickCount() ==2){
			int[] selectRows = jTableInfo.getSelectedRows();
			if (selectRows.length == 0) {
				ShowMessage.view(this, "请选择一条信息！");
				return;
			}
			String stationId = StringUtils.ObjToString(this.jTableInfo.getValueAt(selectRows[0], 0));
			String stationName = StringUtils.ObjToString(this.jTableInfo.getValueAt(selectRows[0], 2));

			StationTreeEditDialog aud = new StationTreeEditDialog(new JFrame(),true,stationId,stationName);
			aud.setVisible(true);
			this.initTable(searchText.getText().trim());
		}
	}
}
