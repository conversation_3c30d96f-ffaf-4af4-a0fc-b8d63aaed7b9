package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JTextField;

import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;



public class StationBoardEditDialog extends javax.swing.JDialog{
	private String id = "";
	private String stationId = "";
	private String stationName = "";
	private String switchId = "";
	private String switchName = "";
	private String remark = "";
	private String kind = "";

	private JAutoCompleteComboBox switchComboBox = new JAutoCompleteComboBox();
	private JAutoCompleteComboBox stationComboBox = new JAutoCompleteComboBox();
	private JTextField textField = new JTextField();

	public StationBoardEditDialog(javax.swing.JDialog parent, boolean modal,String id,String stationId,String stationName,String switchId,String switchName,String remark,String kind) {
		super(parent, modal);
		this.id = id;
		this.kind = kind;
		this.stationId = stationId;
		this.stationName = stationName;
		this.switchId = switchId;
		this.switchName = switchName;
		this.remark = remark;
		
		initComponents();
		this.setTitle("厂站设备关联压板关系修改");
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	private void initComponents() {
		getContentPane().setLayout(null);
		
		JLabel stationLabel = new JLabel("\u5382\u7AD9\u540D\u79F0");
		stationLabel.setBounds(14, 13, 113, 18);
		getContentPane().add(stationLabel);
		
		JLabel switchLabel = new JLabel("\u65AD\u8DEF\u5668\u540D\u79F0");
		switchLabel.setBounds(14, 81, 136, 18);
		getContentPane().add(switchLabel);
		
		
		JButton button1 = new JButton("\u786E\u5B9A");
		button1.setBounds(48, 199, 63, 23);
		getContentPane().add(button1);
		
		JButton button2 = new JButton("\u53D6\u6D88");
		button2.setBounds(125, 199, 57, 23);
		getContentPane().add(button2);
		this.setSize(260, 310);

		button1.setToolTipText("\u786e\u5b9a");
		button1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png")));
		button1.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button1.setFocusPainted(false);
		
		button1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveButtonActionPerformed(evt);
			}
		});
		
		button2.setFont(new Font("宋体", Font.PLAIN, 14));
		button2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png")));
		button2.setToolTipText("\u53d6\u6d88");
		button2.setFont(new Font("宋体", Font.PLAIN, 14));

		button2.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button2.setFocusPainted(false);
		
		button2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				cancelButtonActionPerformed(evt);
			}
		});
		
		DefaultComboBoxModel model = new DefaultComboBoxModel();

		for(Iterator<PowerDevice>  itor = CBSystemConstants.getMapPowerStation().values().iterator();itor.hasNext();){
			PowerDevice line = itor.next();
			
			CodeNameModel cnm = new CodeNameModel(line.getPowerDeviceID(), line.getPowerDeviceName());
			
			model.addElement(cnm);
		}
		
		stationComboBox.setModel(model);
		
		stationComboBox.setBounds(14, 44, 206, 26);
		stationComboBox.setSelectedIndex(-1);
		stationComboBox.setEditable(true);
		getContentPane().add(stationComboBox);
		
		
		stationComboBox.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				stationComboBoxActionPerformed(evt);
			}
		});

		switchComboBox.setBounds(14, 152, 206, 24);
		getContentPane().add(switchComboBox);
		
		textField = new JTextField();
		textField.setBounds(14, 112, 206, 24);
		getContentPane().add(textField);
		textField.setColumns(10);
		
		getSelectDfsInfo();
	}
	
	private void stationComboBoxActionPerformed(ActionEvent evt) {
		DefaultComboBoxModel model = new DefaultComboBoxModel();

		CodeNameModel stationcnm = (CodeNameModel) stationComboBox.getSelectedItem();

		String stationId = stationcnm.getCode();
		
		if(!stationId.equals("")){
			String sql = "SELECT EQUIP_ID,EQUIP_NAME FROM "+CBSystemConstants.opcardUser+"T_EQUIPINFO  WHERE STATION_ID = '"+stationId+"' AND EQUIPTYPE_ID = '26' AND VOLTAGE_ID = '112871465660973060'";
			
			List<Map<String, String>> list = DBManager.queryForList(sql);
			
			for(Map<String,String> map : list){
				String equipid = StringUtils.ObjToString(map.get("EQUIP_ID"));
				String equipname = StringUtils.ObjToString(map.get("EQUIP_NAME"));
				
				CodeNameModel cnm = new CodeNameModel(equipid, equipname);
				model.addElement(cnm);
			}
			
			switchComboBox.setModel(model);
		}
	}
	
	//获取修改界面信息WW
	public void getSelectDfsInfo() {
		CodeNameModel cnm1 = new CodeNameModel(this.stationId, this.stationName);
		stationComboBox.setSelectedItem(cnm1);
		
		CodeNameModel cnm2 = new CodeNameModel(this.switchId, this.switchName);
		switchComboBox.setSelectedItem(cnm2);
	}
	
	//确定
	private void saveButtonActionPerformed(java.awt.event.ActionEvent evt) {
		String id = String.valueOf(UUID.randomUUID());
		
		CodeNameModel stationcnm = (CodeNameModel) stationComboBox.getSelectedItem();
		CodeNameModel switchcnm = (CodeNameModel) switchComboBox.getSelectedItem();

		String stationId = stationcnm.getCode();
		String stationName = stationcnm.getName();
		
		String switchId = switchcnm.getCode();
		String switchName = textField.getText();
		
		if(kind.equals("新增")){
			String sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_STATIONBOARD "
					+ "(ID,STATIONID,STATIONNAME,SWITCHID,SWITCHNAME,REMARK) VALUES ('"+id+"','"+stationId+"','"+stationName+"','"+switchId+"','"+switchName+"','"+remark+"')";
			DBManager.execute(sql);
		}else{
			String sql = "UPDATE "+CBSystemConstants.opcardUser+"T_A_STATIONBOARD "
					+ "SET STATIONID = '"+stationId+"', STATIONNAME = '"+stationName+"', SWITCHID = '"+switchId+"', SWITCHNAME = '"+switchName+"', REMARK = '"+remark+"' WHERE ID = '"+this.id+"'";
			DBManager.execute(sql);
		}
		
		this.setVisible(false);
		this.dispose();
	}
	
	public void paint(Graphics g) {
		super.paint(g);
		Graphics2D g_2d = (Graphics2D) g;
		g_2d.setColor(Color.GRAY);
		g_2d.drawLine(20, 40, this.getSize().width - 20, 40);
	}
	//取消
	private void cancelButtonActionPerformed(java.awt.event.ActionEvent evt) {
		this.setVisible(false);
		this.dispose();
	}
}