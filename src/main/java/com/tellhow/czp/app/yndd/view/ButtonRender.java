package com.tellhow.czp.app.yndd.view;

import java.awt.Component;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Vector;

import javax.swing.AbstractCellEditor;
import javax.swing.JButton;
import javax.swing.JOptionPane;
import javax.swing.JTable;
import javax.swing.table.TableCellEditor;
import javax.swing.table.TableCellRenderer;

import org.apache.batik.dom.svg.SVGOMGElement;
import org.apache.batik.gvt.GraphicsNode;
import org.w3c.dom.Element;

import com.tellhow.graphicframework.action.impl.ChangeDeviceRectFlashingAction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.ColorTableModel;
import czprule.system.CBSystemConstants;
import czprule.system.DeviceSearch;
import czprule.system.ShowMessage;

class ButtonRender extends AbstractCellEditor implements TableCellRenderer,ActionListener, TableCellEditor{
	private static final long serialVersionUID = 1L;
	private JButton button =null;
	public ButtonRender(String text){
		button = new JButton(text);
		button.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/dingwei.png")));
		button.setSize(10, 10);
		button.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
		button.addActionListener(this);
	}
	@Override
	public Object getCellEditorValue() {
		return null;
	}
	@Override
	public Component getTableCellRendererComponent(JTable table, Object value,
			boolean isSelected, boolean hasFocus, int row, int column) {
		return button;
	}
	
	@Override
	public void actionPerformed(ActionEvent e) {
		try {
			JTable table = (JTable)button.getParent();
			int rownum = table.getSelectedRow();
			
			String devId = StringUtils.ObjToString(table.getValueAt(rownum, 3));
			String devtype = StringUtils.ObjToString(table.getValueAt(rownum, 1));

			if(devtype.equals("输电线路")){
				PowerDevice dev = CBSystemConstants.getPowerFeeder(devId);
				searchEquipByCondition(dev);
			}else{
				PowerDevice dev = CBSystemConstants.getPowerDevice(devId);
				searchEquipByCondition(dev);
			}
		} catch (Exception e2) {
			e2.printStackTrace();
			ShowMessage.view("无法获取设备列表信息，请联系系统管理员！");
		}
	}
	
	private void searchEquipByCondition(PowerDevice pd){
		SVGCanvas svgCanvas = SystemConstants.getGuiBuilder().getActivateSVGPanel().getSvgCanvas();
		 
		SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
    	Element groupElement = resolver.getDeviceGroupElement(pd);
		
		SVGOMGElement gElement = (SVGOMGElement) groupElement;
	    	
    	int tagScreenX = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getX() + svgCanvas.getWidth()/2;
    	int tagScreenY = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getY() + svgCanvas.getHeight()/2;
    	GraphicsNode node = svgCanvas.getUpdateManager().getBridgeContext().getGraphicsNode(gElement);
    	  // Rectangle2D ddd = node.getTransformedBounds(node.getTransform());

    	if(node!=null){
    		Rectangle2D bounds = svgCanvas.getViewBoxTransform().createTransformedShape(node.getBounds()).getBounds();  
        	int srcScreenX= (int)(bounds.getX()+bounds.getWidth()/2)+(int)svgCanvas.getLocationOnScreen().getX();
        	int srcScreenY = (int)(bounds.getY()+bounds.getHeight()/2)+(int)svgCanvas.getLocationOnScreen().getY();
        	int offX = (int)(tagScreenX-srcScreenX);
        	int offY = (int)(tagScreenY-srcScreenY);
        	AffineTransform at = svgCanvas.getRenderingTransform();
        	at.translate(offX/svgCanvas.getRenderingTransform().getScaleX(),  offY/svgCanvas.getRenderingTransform().getScaleY());
        	svgCanvas.setRenderingTransform(at, true);
        	
            //闪烁效果
        	ChangeDeviceRectFlashingAction action = new ChangeDeviceRectFlashingAction(pd, "3");
        	action.backexecute();
    		action.execute();
    	}else{
    		ShowMessage.view("当前厂站图上不存在该设备！");
    	}
	}
	
	@Override
	public Component getTableCellEditorComponent(JTable table, Object value,
			boolean isSelected, int row, int column) {
		// TODO Auto-generated method stub
		return button;
	}
}
