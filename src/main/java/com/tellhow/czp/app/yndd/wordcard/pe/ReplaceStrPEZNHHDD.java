package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEZNHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱站内合环调电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			List<PowerDevice> otherlineList = new ArrayList<PowerDevice>();
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.Switch, true, true, true);
			
			List<PowerDevice> loopkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> unloopkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if (dev.getDeviceType().equals(SystemConstants.InOutLine)){
						if(!lineList.contains(dev)){
							otherlineList.add(dev);
						}
					}
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							loopkgList.add(dev);
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							unloopkgList.add(dev);
						}
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							loopkgList.add(dev);
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							unloopkgList.add(dev);
						}
					}
				}
			}
			
			for(PowerDevice dev : loopkgList){
				String kgName = CZPService.getService().getDevName(dev); 
				replaceStr += stationName+"@核实"+kgName+"处于热备用/r/n";
				replaceStr += "普洱地调@遥控用"+stationName+kgName+"同期合环/r/n";
			}
			
			
			for(PowerDevice dev : unloopkgList){
				String kgName = CZPService.getService().getDevName(dev); 

				replaceStr += "普洱地调@遥控断开"+stationName+kgName+"/r/n";
//				replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
			}
		}
		
		return replaceStr;
	}

}
