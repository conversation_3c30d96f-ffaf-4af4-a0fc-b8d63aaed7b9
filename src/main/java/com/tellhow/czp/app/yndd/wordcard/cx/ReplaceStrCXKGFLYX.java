package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.sun.java.help.search.Rule;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXKGFLYX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄开关分列运行".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName =  StringUtils.killVoltInDevName(voltStationName);
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> deviceList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
					RuleExeUtil.swapLowDeviceList(gdList);
					for(PowerDevice gd : gdList) {
						if(RuleExeUtil.getDeviceEndStatus(gd).equals("0")){
							deviceList.add(gd);
						}
					}
				}
			}
			
			for(PowerDevice dev : deviceList){
				String devName = CZPService.getService().getDevName(dev); 
				
				replaceStr += "楚雄地调@遥控合上"+stationName+devName+"。/r/n";
			}
			
			replaceStr += "楚雄地调@遥控断开"+stationName+deviceName+"。/r/n";
		}
		
		return replaceStr;
	}

}
