package com.tellhow.czp.app.yndd.view;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;

import czprule.model.CodeNameModel;

public class ReverseCzp {
	public  String  content = "";
    public List<BaseCardModel> parsing(CodeNameModel cnm,String czrw) {

        List<BaseCardModel> newResults  = new ArrayList<BaseCardModel>();
        TicketDBManager tdb = new TicketDBManager();
        List<BaseCardModel> results = tdb.gxqueryTicketMX(cnm.getCode());
        if(czrw.indexOf("线")>=0){
            newResults = parsingLine(results,czrw);
        }

        return newResults;
    }
    //线路令  反向成票
    public List<BaseCardModel> parsingLine(List<BaseCardModel> list,String czrw){
        List<BaseCardModel> results  = new ArrayList<BaseCardModel>();
        String cardSub = "0";//合序
        String endSub = list.get(list.size()-1).getCardSub();
        String station = "";
        String cardOrder = "1";
        if(czrw.indexOf("运行状态转为检修状态") >=0){//线路令，运行转检修反向成票
            int num = 1;
            int addNum = 0;//新增指令条数
            for(int i=list.size()-1;i>=0;i--){
            	cardOrder = String.valueOf(list.size()-i+addNum);
                BaseCardModel model = new BaseCardModel();
                model = list.get(i);
                String stationMx = list.get(i).getStationName();
                if(!model.getCardSub().equals(endSub)){//不同序号  序号加1
                    endSub = String.valueOf(Integer.valueOf(endSub)-1);
                    cardSub = String.valueOf(Integer.valueOf(cardSub) +1);
                }else{
                    if(i==list.size()-1){//相同序号  第一项加1
                    	cardSub = String.valueOf(Integer.valueOf(cardSub) +1);
                    }
                }
                model.setCardSub(cardSub);
                String cardDesc = list.get(i).getCardDesc();
                if(i==0&&cardDesc.indexOf("断开")>=0&&cardDesc.indexOf("开关")>=0){//判断是否最后一条术语   同期合上220kV百德Ⅱ线2055开关。
                	//断开220kV百德Ⅱ线2055开关。 判断为断开和开关关键字  反向成票为同期
                	content = "同期";
                }else if(i==1&&cardDesc.indexOf("断开")>=0 &&cardDesc.indexOf("开关")>=0){//线路令操作票  倒数第二条术语  合上220kV崇桃Ⅱ线2053开关对线路充电。
                	//断开百色站220kV百德Ⅱ线S2063开关。    判断为断开和开关关键字  反向成票为对线路充电
                	content = "对线路充电";
                }
                String desc =compareString(list.get(i).getCardDesc());
                model.setCardDesc(desc);
                
                if(list.get(i).getCardDesc().indexOf("拉开")>=0 &&  list.get(i).getCardDesc().indexOf("地刀")>=0){//改术语下面为  检查百色站220kV百沙Ⅱ线S2059开关两侧及线路无接地。
                	addNum = addNum +1;
                	model.setCardOrder(cardOrder);
                	results.add(model);
                	BaseCardModel modelnew = new BaseCardModel();
                	if(i-2>0){
                		if(list.get(i+1).getCardDesc().indexOf("将")>=0 && list.get(i+1).getCardDesc().indexOf("开关") >=0){
                    		String jcDesc = list.get(i+1).getCardDesc().replace("将", "");
                    		jcDesc = "检查"+jcDesc.split("开关")[0].toString()+"开关两侧及线路无接地。";
                        	modelnew.setCardDesc(jcDesc);
                		}
                	}
                	cardOrder = String.valueOf(Integer.valueOf(cardOrder)+1);
                	modelnew.setCardOrder(cardOrder);
                	modelnew.setCardSub(cardSub);
                	modelnew.setStationName(list.get(i).getStationName());
                	results.add(modelnew);
                	
                }else{
                	model.setCardOrder(cardOrder);
                    results.add(model);
                    
                }
            }

        }else if(czrw.indexOf("检修状态转为运行状态") >=0){////线路令，检修状态转运行状态反向成票
            int num = 1;
            int deleteNum = 0;//删除指令条数
            for(int i=list.size()-1;i>=0;i--){
            	cardOrder = String.valueOf(list.size()-i-deleteNum);
                BaseCardModel model = new BaseCardModel();
                model = list.get(i);
                String stationMx = list.get(i).getStationName();
                if(!model.getCardSub().equals(endSub)){//不同序号  序号加1
                    endSub = String.valueOf(Integer.valueOf(endSub)-1);
                    cardSub = String.valueOf(Integer.valueOf(cardSub) +1);
                }else{
                    if(i==list.size()-1){//相同序号  第一项加1
                    	cardSub = String.valueOf(Integer.valueOf(cardSub) +1);
                    }
                }
                model.setCardSub(cardSub);
                String cardDesc = list.get(i).getCardDesc();
                String desc =compareString(list.get(i).getCardDesc());
                model.setCardDesc(desc);
                
                if(list.get(i).getCardDesc().indexOf("检查")>=0 &&  list.get(i).getCardDesc().indexOf("开关两侧及线路无接地")>=0){
                	deleteNum = deleteNum +1;
                }else{
                	model.setCardOrder(cardOrder);
                    results.add(model);
                }
            }
        }else {//其他  开关  刀闸   母线等设的反向成票
        	for(int i=list.size()-1;i>=0;i--){
            	cardOrder = String.valueOf(list.size()-i);
                BaseCardModel model = new BaseCardModel();
                model = list.get(i);
                if(!model.getCardSub().equals(endSub)){//不同序号  序号加1
                    endSub = String.valueOf(Integer.valueOf(endSub)-1);
                    cardSub = String.valueOf(Integer.valueOf(cardSub) +1);
                }else{
                    if(i==list.size()-1){//相同序号  第一项加1
                    	cardSub = String.valueOf(Integer.valueOf(cardSub) +1);
                    }
                }
                model.setCardOrder(cardOrder);
                model.setCardSub(cardSub);
                String cardDesc = list.get(i).getCardDesc();
                String desc =compareString(list.get(i).getCardDesc());
                model.setCardDesc(desc);
                results.add(model);
        	}
        }
        return results;
    }


    //解析操作任务
    public String parsingCzrw(String czrw){
        List<BaseCardModel> results  = new ArrayList<BaseCardModel>();
        if(czrw.indexOf("运行状态转为检修状态") >=0){
            czrw = czrw.replace("运行状态转为检修状态", "检修状态转为运行状态");
        }else if(czrw.indexOf("检修状态转为运行状态") >=0){
            czrw = czrw.replace("检修状态转为运行状态", "运行状态转为检修状态");
        }
        return czrw;
    }
    //术语替换
    public  String compareString(String cardDesc){

        if(cardDesc.indexOf("同期")>=0){
            cardDesc = cardDesc.replace("同期", "");
            if(cardDesc.indexOf("合上") >=0){
            	cardDesc = cardDesc.replace("合上", "断开");
            }else if(cardDesc.indexOf("断开") >=0){
            	cardDesc = cardDesc.replace("断开", "合上");
            }
        }else if(cardDesc.indexOf("对线路充电")>=0){//对线路充电
            cardDesc = cardDesc.replace("对线路充电", "");
            if(cardDesc.indexOf("合上") >=0){
            	cardDesc = cardDesc.replace("合上", "断开");
            }else if(cardDesc.indexOf("拉开") >=0){
            	cardDesc = cardDesc.replace("拉开", "合上");
            }
        }else if(cardDesc.indexOf("退出")>=0){
            cardDesc = cardDesc.replace("退出", "投入");
        }else if(cardDesc.indexOf("投入")>=0){
            cardDesc = cardDesc.replace("投入", "退出");
        }else if(cardDesc.indexOf("拉开")>=0){
            cardDesc = cardDesc.replace("拉开", "合上");
        }else if(cardDesc.indexOf("合上")>=0){
            cardDesc = cardDesc.replace("合上", "拉开");
        }else if(cardDesc.indexOf("热备用状态转为冷备用状态")>=0){
            cardDesc = cardDesc.replace("热备用状态转为冷备用状态", "冷备用状态转为热备用状态");
        }else if(cardDesc.indexOf("冷备用状态转为热备用状态")>=0){
            cardDesc = cardDesc.replace("冷备用状态转为热备用状态", "热备用状态转为冷备用状态");
        }else if(cardDesc.indexOf("运行状态转为热备用状态")>=0){
            cardDesc = cardDesc.replace("运行状态转为热备用状态", "热备用状态转为运行状态");
        }else if(cardDesc.indexOf("热备用状态转为运行状态")>=0){
            cardDesc = cardDesc.replace("热备用状态转为运行状态", "运行状态转为热备用状态");
        }else if(cardDesc.indexOf("冷备用状态转为检修状态")>=0){
            cardDesc = cardDesc.replace("冷备用状态转为检修状态", "检修状态转为冷备用状态");
        }else if(cardDesc.indexOf("检修状态转为冷备用状态")>=0){
            cardDesc = cardDesc.replace("检修状态转为冷备用状态", "冷备用状态转为检修状态");
        }else if(cardDesc.indexOf("运行状态转为检修状态")>=0){
            cardDesc = cardDesc.replace("运行状态转为检修状态", "检修状态转为运行状态");
        }else if(cardDesc.indexOf("检修状态转为运行状态")>=0){
            cardDesc = cardDesc.replace("检修状态转为运行状态", "运行状态转为检修状态");
        }else if(cardDesc.indexOf("由运行转检修") >=0){
        	cardDesc = cardDesc.replace("由运行转检修", "由检修转运行");
        }else if(cardDesc.indexOf("由检修转运行") >=0){
        	cardDesc = cardDesc.replace("由检修转运行", "由运行转检修");
        }else if(cardDesc.indexOf("断开")>=0){
            cardDesc = cardDesc.replace("断开", "合上");
            if("同期".equals(content)){
            	cardDesc = content + cardDesc;
            }else if("对线路充电".equals(content)){
            	cardDesc =cardDesc.replace("。","");
            	cardDesc = cardDesc + content + "。";
            }
        }
        return cardDesc;
    }

}
