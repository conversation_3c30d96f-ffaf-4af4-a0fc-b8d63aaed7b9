package com.tellhow.czp.app.yndd.wordcard.yx;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYX23JXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("玉溪二分之三接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			for(PowerDevice dev : zbgyckgList){
				if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					Collections.reverse(zbgyckgList);
				}
				break;
			}
			
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzxdjddzList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(jddzList);
				
				otherzxdjddzList.addAll(jddzList);
			}
			
			for(PowerDevice dev : zbdyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
			
			if(curDev.getPowerStationID().equals("SS-302")){//500kV宁州变特殊处理
				replaceStr += stationName+"@核实站用电已倒由35kV备用站用变供电正常/r/n";
			}else{
				replaceStr += CommonFunction.getZybDrCheckContent(zbdyckgList);
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunction.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			if(otherzxdjddzList.size() > 0){
				replaceStr += CommonFunction.getZxdJddzOnCheckContent(otherzxdjddzList, stationName, station);
			}
		
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						replaceStr += CommonFunction.getKnifeOffContent(dzList,stationName);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						if(CommonFunction.ifSwitchSeparateControl(dev)){
							if(dev.getPowerStationID().equals("SS-302")){//500kV宁州变
								replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
							}else{
								if(CommonFunction.ifSwitchSeparateControl(dev)){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
									
									for(PowerDevice dz : dzList){
										List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
										dzTempList.add(dz);
										
										replaceStr += CommonFunction.getKnifeOffContent(dzTempList, stationName);
									}
								}else{
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
								}
							}
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}
						
						List<PowerDevice> dzList = CommonFunction.getTransformerKnife(curDev, dev);
						replaceStr += CommonFunction.getKnifeOffContent(dzList,stationName);
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						if(CommonFunction.ifSwitchSeparateControl(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							
							for(PowerDevice dz : dzList){
								List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
								dzTempList.add(dz);
								
								replaceStr += CommonFunction.getKnifeOffContent(dzTempList, stationName);
							}
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}
					}
				}
				
				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunction.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
				}
			}
		
		}
		
		return replaceStr;
	}

}
