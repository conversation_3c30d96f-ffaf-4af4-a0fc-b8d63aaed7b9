package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNKGRBYTOLBY implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳开关由热备用转冷备用".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String devName = CZPService.getService().getDevName(curDev);

			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
				if(CommonFunctionBN.ifSwitchSeparateControlBN(curDev)){
					replaceStr += "版纳地调@执行"+stationName+devName+"由热备用转冷备用程序操作/r/n";
					List<PowerDevice> kgList = new ArrayList<PowerDevice>();
					kgList.add(curDev);
					replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(kgList ,stationName);
				}else{
					replaceStr += stationName+"@将"+devName+"由热备用转冷备用/r/n";
				}
			}
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
