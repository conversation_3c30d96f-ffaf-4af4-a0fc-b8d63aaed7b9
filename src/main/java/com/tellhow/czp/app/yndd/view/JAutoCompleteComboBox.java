package com.tellhow.czp.app.yndd.view;

import java.awt.Component;
import java.awt.event.ActionListener;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Vector;

import javax.swing.ComboBoxEditor;
import javax.swing.ComboBoxModel;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JTextField;

import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.util.PinYinUtil;

import czprule.model.CodeNameModel;
import czprule.system.DBManager;

/**
 * 版权声明: 泰豪软件股份有限公司版权所有 功能说明: 作 者: 郑柯 开发日期: 2011-5-18 上午09:11:47
 */
public class JAutoCompleteComboBox extends JComboBox {
	public JAutoCompleteComboBox() {
		super();
		this.setEditor(new PopupComboEditor());
		this.setEditable(true);
		final JTextField textfield = (JTextField) getComboBox().getEditor()
				.getEditorComponent();
		textfield.addKeyListener(new KeyAdapter() {
			
			public void keyPressed(KeyEvent ke) {
				getComboBox().setName("");
				char ch = ke.getKeyChar();
				if ( ch == KeyEvent.CHAR_UNDEFINED||ch == KeyEvent.VK_UP || ch == KeyEvent.VK_DOWN) {
					getComboBox().setName("selecting");
					return;
				} 
			}

			public void keyReleased(KeyEvent ke) {
				getComboBox().setName("");
				char ch = ke.getKeyChar();
				if (ch == KeyEvent.CHAR_UNDEFINED || ch == KeyEvent.VK_UP
						|| ch == KeyEvent.VK_DOWN) {
					return;
				} 
				else if (ch == KeyEvent.VK_ENTER) {
					if (getComboBox().getSelectedIndex() == -1) {
						getComboBox().setSelectedIndex(0);
						return;
					}
				}
				// if(ch==KeyEvent.VK_ENTER||ch==KeyEvent.VK_SPACE){
				findMatchingStrings(textfield.getText());
				if (!JAutoCompleteComboBox.this.isPopupVisible()) {
					// JAutoCompleteComboBox.this.setSelectedIndex(0);
				}
				if (getComboBox().getSelectedIndex() == -1) {
//					getComboBox().setSelectedIndex(0);
					return;
				}
				
				// }
			}
		});
		
	}

	public JComboBox getComboBox() {
		return this;
	}

	public void findMatchingStrings(String entered) {
		if (entered.equals(""))
			return;
		List<CodeNameModel> v1 = new Vector<CodeNameModel>();
		List<CodeNameModel> v2 = new Vector<CodeNameModel>();
		List<CodeNameModel> v3 = new Vector<CodeNameModel>();
		List<CodeNameModel> v4 = new Vector<CodeNameModel>();
		ComboBoxModel model = this.getModel();
	
		String stationName;
		List<String> stationPYs;// 厂站拼音
		List<String> stationPYHeads;// 厂站拼音首字母缩写

		for (int i = 0; i < model.getSize(); i++) {
			stationName = model.getElementAt(i).toString();
			stationPYs = PinYinUtil.getPinYin(stationName);
			stationPYHeads = PinYinUtil.getHead(stationName);
			String stationPYHead;
			boolean hasAdd=false;
			for (int j = 0; j < stationPYHeads.size(); j++) {
				String stationPY = stationPYs.get(j);
				stationPYHead = stationPYHeads.get(j);
				if (stationName.toLowerCase().equals(entered.toLowerCase())
						|| stationPY.equals(entered.toLowerCase())
						|| stationPYHead.equals(entered.toLowerCase())) {
					v1.add((CodeNameModel) model.getElementAt(i));
					hasAdd=true;
					break;
				} else if (stationName.toLowerCase().startsWith(entered.toLowerCase())
						|| stationPY.startsWith(entered.toLowerCase())
						|| stationPYHead.startsWith(entered.toLowerCase())) {
					v2.add((CodeNameModel) model.getElementAt(i));
					hasAdd=true;
					break;
				} 
				else if (stationName.toLowerCase().indexOf(entered.toLowerCase()) >= 0
						|| stationPY.indexOf(entered.toLowerCase()) >= 0
						|| stationPYHead.indexOf(entered.toLowerCase()) >= 0) {
					v3.add((CodeNameModel) model.getElementAt(i));
					hasAdd=true;
					break;
				}
				else if (stationName.equals("")){
					v4.add((CodeNameModel) model.getElementAt(i));
					hasAdd=true;
				}
			}
			
			if(!hasAdd)
				v4.add((CodeNameModel) model.getElementAt(i));
		}
		v1.addAll(v2);
		v1.addAll(v3);
		v1.addAll(v4);
		JPopupTextField textField = (JPopupTextField) this.getEditor()
				.getEditorComponent();
		int caretPosition = textField.getCaretPosition();
		this.setModel(new DefaultComboBoxModel(v1.toArray()));
		textField.setText(entered);
		textField.setCaretPosition(caretPosition);
		if(this.getSelectedItem()==null){
			this.showPopup();
		}
		else if (!this.getSelectedItem().toString().equals(entered))
			this.showPopup();
		// this.setSelectedIndex(-1);//重置选择
	}
	
	public void fillComboBox(String sql) {
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		SqlRowSet set = DBManager.queryForRowSet(sql);
		while (set.next()){
			cnm=new CodeNameModel();
			cnm.setCode(set.getString(1));
			cnm.setName(set.getString(2));
			model.addElement(cnm);
		}
		this.setModel(model);
	}
	
	public void setSelected(String code) {
		for(int i = 0; i < this.getModel().getSize(); i++) {
			CodeNameModel cnm = (CodeNameModel)this.getModel().getElementAt(i);
			if(cnm.getCode().equals(code)) {
				this.setSelectedIndex(i);
				break;
			}
		}
	}
}

/**
 * 带右键菜单编辑器
 * <AUTHOR>
 *
 */
class PopupComboEditor  implements ComboBoxEditor,FocusListener {
    protected JPopupTextField editor;
    private Object oldValue;

    public PopupComboEditor() {
        editor = createEditorComponent();
    }

    public Component getEditorComponent() {
        return editor;
    }

    /**
     * Creates the internal editor component. Override this to provide
     * a custom implementation.
     *
     * @return a new editor component
     * @since 1.6
     */
    protected JPopupTextField createEditorComponent() {
    	JPopupTextField editor = new JPopupTextField();
        editor.setBorder(null);
        return editor;
    }

    /**
     * Sets the item that should be edited.
     *
     * @param anObject the displayed value of the editor
     */
    public void setItem(Object anObject) {
        if ( anObject != null )  {
            editor.setText(anObject.toString());

            oldValue = anObject;
        } else {
            editor.setText("");
        }
    }

    public Object getItem() {
        Object newValue = editor.getText();

        if (oldValue != null && !(oldValue instanceof String))  {
            // The original value is not a string. Should return the value in it's
            // original type.
            if (newValue.equals(oldValue.toString()))  {
                return oldValue;
            } else {
                // Must take the value from the editor and get the value and cast it to the new type.
                Class cls = oldValue.getClass();
                try {
                    Method method = cls.getMethod("valueOf", new Class[]{String.class});
                    newValue = method.invoke(oldValue, new Object[] { editor.getText()});
                } catch (Exception ex) {
                    // Fail silently and return the newValue (a String object)
                }
            }
        }
        return newValue;
    }

    public void selectAll() {
        editor.selectAll();
        editor.requestFocus();
    }

    // This used to do something but now it doesn't.  It couldn't be
    // removed because it would be an API change to do so.
    public void focusGained(FocusEvent e) {}

    // This used to do something but now it doesn't.  It couldn't be
    // removed because it would be an API change to do so.
    public void focusLost(FocusEvent e) {}

    public void addActionListener(ActionListener l) {
        editor.addActionListener(l);
    }

    public void removeActionListener(ActionListener l) {
        editor.removeActionListener(l);
    }
}
