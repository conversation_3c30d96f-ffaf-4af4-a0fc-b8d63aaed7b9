package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooMLKGRBY implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("母联开关热备用".equals(tempStr)){
			List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
			
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						hignVoltMlkgList.add(dev2);
					}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						hignVoltXlkgList.add(dev2);
					}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)&&curDev.getPowerStationName().contains("威远街")){
						hignVoltMlkgList.add(dev2);
					}
				}
			}
			
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			
			if((hignVoltMlkgList.size() == 1||hignVoltMlkgList.size() == 2) && hignVoltXlkgList.size() == 2  && mxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				for(PowerDevice hignVoltMlkg :hignVoltMlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(hignVoltMlkg).equals("1")||(RuleExeUtil.getDeviceBeginStatus(hignVoltMlkg).equals("")&&hignVoltMlkg.getDeviceStatus().equals("1"))){
						 return true;
					}
				}
			}
		}
        return false;
	}
}
