package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJDMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖单母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			replaceStr += CommonFunctionQJ.getPowerOnCheckContent();
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgyckgList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				gycxlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true);
			}

			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			List<PowerDevice> otherzbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbdyckgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0") && RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						if(dev.getPowerVoltGrade() == RuleExeUtil.getTransformerVolByType(curDev, "middle")){
							otherzbzyckgList.add(dev);
						}else if(dev.getPowerVoltGrade() == RuleExeUtil.getTransformerVolByType(curDev, "low")){
							otherzbdyckgList.add(dev);
						}
					}
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				for(PowerDevice dev : zbgyckgList){
					if (RuleExeUtil.isDeviceChanged(dev)) {
						replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if (RuleExeUtil.isDeviceChanged(dev)) {
						replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
					}
			}

				for(PowerDevice dev : zbdyckgList){
                    if (RuleExeUtil.isDeviceChanged(dev)) {
                    	replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
                    }
                }
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionQJ.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "曲靖地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}
			}
			
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "曲靖地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}
			}
			
			for(PowerDevice dev : otherzbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "曲靖地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}
			}
			
			for(PowerDevice dev : otherzbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : gycxlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "曲靖地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionQJ.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
			}
		}
		
		return replaceStr;
	}

}
