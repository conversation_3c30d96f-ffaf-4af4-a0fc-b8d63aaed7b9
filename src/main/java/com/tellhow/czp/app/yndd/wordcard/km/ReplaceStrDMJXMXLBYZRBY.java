package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrDMJXMXLBYZRBY implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("单母接线母线冷备用转热备用".equals(tempStr)){
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "", true, true, true);
			List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

			if(zbList.size()>0){
				List<PowerDevice> dycswList = new ArrayList<PowerDevice>();
				
				if(zbList.size()>1){
					for(PowerDevice zb : zbList){
						dycswList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
					}
				}else{
					dycswList = RuleExeUtil.getTransformerSwitchLow(zbList.get(0));
				}
				
				for(PowerDevice dycsw : dycswList){
					if(RuleExeUtil.getDeviceBeginStatus(dycsw).equals("2")){
						List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dycsw, SystemConstants.PowerTransformer, "", true, true, true);

						replaceStr += "将"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用/r/n";
					}
				}
				
				if(xlkgList.size()>0){
					replaceStr += "将"+CZPService.getService().getDevName(curDev)+"及各分路由冷备用转热备用/r/n";
				}else{
					replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
				}
			}
		}
		return replaceStr;

	}
}
