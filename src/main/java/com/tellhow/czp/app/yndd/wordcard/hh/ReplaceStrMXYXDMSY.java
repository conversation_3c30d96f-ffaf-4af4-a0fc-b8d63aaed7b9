package com.tellhow.czp.app.yndd.wordcard.hh;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrMXYXDMSY implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("运行开关倒母术语".equals(tempStr)){
			List<PowerDevice> dmswList = new ArrayList<PowerDevice>();
			Map<PowerDevice, PowerDevice>  beginmxMap = new HashMap<PowerDevice, PowerDevice>();
			Map<PowerDevice, PowerDevice>  endmxMap = new HashMap<PowerDevice, PowerDevice>();
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				PowerDevice dev = dtd.getTransDevice();
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) {
					List<PowerDevice> swList =RuleExeUtil.getDeviceDirectList(dev, SystemConstants.Switch);
					if(swList.size()>0&&
							swList.get(0).getDeviceStatus().equals("0")&&
							(swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)||swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)||swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL))
							){
						if(!dmswList.contains(swList.get(0))){
							dmswList.add(swList.get(0));
						}
						List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
						if(mxList.size()>0){
							if(dtd.getBeginstatus().equals("0")){
								beginmxMap.put(swList.get(0), mxList.get(0));
							}else{
								endmxMap.put(swList.get(0), mxList.get(0));
							}
						}
						continue;
					}

				}
			}
			//将110kV下念湖I线104开关由110kV 1号母线热备用倒至110kV 2号母线热备用
			String str = "";
			for(PowerDevice sw:dmswList){
				if(beginmxMap.get(sw)!=null&&endmxMap.get(sw)!=null){
					str+=CZPService.getService().getDevName(sw)+"、";
				}
			}
			if(str.endsWith("、")){
				str=str.substring(0,str.length()-1);
				replaceStr+="将"+str+"由"+CZPService.getService().getDevName(beginmxMap.get(dmswList.get(0)))+"运行倒至"+CZPService.getService().getDevName(endmxMap.get(dmswList.get(0)))+"运行";
				
			}
		
	    }
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}
	
}
