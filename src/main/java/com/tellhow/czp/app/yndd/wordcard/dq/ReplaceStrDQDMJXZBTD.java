package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQDMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆单母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getTransformerKnifeLoad(curDev);
			List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbdyckgList = new ArrayList<PowerDevice>();
			
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> gycdzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdzList){
				if(dev.getPowerVoltGrade() == lowvolt){
					zbdycdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
				
				gycdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, true);
			}
			
			boolean isAlongTransformer = true;
			
			List<PowerDevice> mx10kVList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						isAlongTransformer = false;
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						otherzbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
						otherzbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
					if(dev.getPowerVoltGrade() == 10){
						mx10kVList.add(dev);
					}
				}
			}

			if(isAlongTransformer){
				replaceStr += stationName+"@落实"+CZPService.getService().getDevName(mx10kVList)+"具备停电条件/r/n";
			}
			
			for(PowerDevice dev : zxdjddzList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "迪庆地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"处合位/r/n";
				}
			}
			
			for(PowerDevice dev : otherzxdjddzList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "迪庆地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"处合位/r/n";
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionDQ.getHhContent(dev,"迪庆地调", stationName);
				}
			}
			
			for(PowerDevice dev : otherzbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionDQ.getHhContent(dev,"迪庆地调", stationName);
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionDQ.getHhContent(dev,"迪庆地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			replaceStr += stationName+"@落实"+deviceName+"具备停电条件/r/n";

			for(PowerDevice dev : otherzbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionDQ.getHhContent(dev,"迪庆地调", stationName);
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionDQ.getHhContent(dev,"迪庆地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(station.getPowerVoltGrade() > 35){
				replaceStr += stationName+"@将"+(int)curDev.getPowerVoltGrade()+"kVX号主变保护定值区由XX区调整至XX区/r/n";
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
							String devName = CZPService.getService().getDevName(dev);
							replaceStr += "迪庆地调@执行"+stationName+devName+"由热备用转冷备用程序操作/r/n";
							replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbzyckgList,stationName);
						}
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
							String devName = CZPService.getService().getDevName(dev);
							replaceStr += "迪庆地调@执行"+stationName+devName+"由热备用转冷备用程序操作/r/n";
							replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbgyckgList,stationName);
						}
					}
				}
				//低压侧开关热备转冷备
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.isDeviceHadStatus(dev, "1", "2")){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					}
				}
				//高压侧开关热备转冷备(拉开隔离开关)
				for(PowerDevice dev : gycdzList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
							replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"具备遥控操作条件/r/n";
							replaceStr += "迪庆地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"在拉开位置/r/n";
						}else{
							replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zxdjddzList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "迪庆地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@落实"+(int)dev.getPowerVoltGrade()+"kV备自投装置处退出状态/r/n";
				}else{
					for(PowerDevice dycmx : dycmxList){
						if(RuleExeUtil.getDeviceBeginStatus(dycmx).equals("0")){
							replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@落实"+(int)dev.getPowerVoltGrade()+"kV备自投装置处退出状态/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
