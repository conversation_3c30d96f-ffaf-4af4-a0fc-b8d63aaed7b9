package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrQTZBZXDJJDZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("其它主变中性点接地刀闸".equals(tempStr)){
			
			List<PowerDevice> gycmlkgList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true,true);
			
			if(gycmlkgList.size() == 2){
				return null;
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					
					gycmlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true,true);
					
					if(gycmlkgList.size() == 2){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", false, true, true, true);
						if(gdList.size()>0){
							replaceStr += "落实"+CZPService.getService().getDevName(gdList)+"处合位/r/n";
						}
					}
				}
			}
	    }
		return replaceStr;
	}

}
