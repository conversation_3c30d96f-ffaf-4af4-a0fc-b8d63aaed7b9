package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTDMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通单母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);

			// 判断厂站名称是否以数字结尾
			if (stationName.matches(".*\\d$")) {
				stationName += "=N=";
			}

			List<PowerDevice> zbList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycmxList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> mlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+ CBSystemConstants.RunTypeSwitchFHC ,"", false, true, true, true);
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL ,"", false, true, true, true);
			
			/*
			 * 站用变开关
			 */
			List<PowerDevice> zybkgList =  new ArrayList<PowerDevice>();
			
			String sql = "SELECT ZYB_DEVID FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+curDev.getPowerDeviceID()+"'";
			List<Map<String,String>> zybList =  DBManager.queryForList(sql);

			for(Map<String,String> map : zybList){
				String devid = StringUtils.ObjToString(map.get("ZYB_DEVID"));
				PowerDevice dev = CBSystemConstants.getPowerDevice(devid);
				zybkgList.add(dev);
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.MotherLine,true, true, true);
				zbList.addAll(tempList);
			}
			
			List<PowerDevice> zbdyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbgyckgList = new ArrayList<PowerDevice>();

			if(zbList.size()>0){
				for(PowerDevice dev : zbList){
					zbdyckgList.addAll(RuleExeUtil.getTransformerSwitchLow(dev));
					zbzyckgList.addAll(RuleExeUtil.getTransformerSwitchMiddle(dev));
					zbgyckgList.addAll(RuleExeUtil.getTransformerSwitchHigh(dev));
				}
			}else{
				gycmlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML ,"", false, true, true, true);
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			}
			
			for(PowerDevice dev : zbzyckgList){
				zycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			}
			
			for(PowerDevice dev : zbgyckgList){
				gycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			}
			
			for(PowerDevice dev : gycmxList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML ,"", false, true, true, true);
			}
			
			for(PowerDevice dev : zycmxList){
				zycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML ,"", false, true, true, true);
			}
			
			for(PowerDevice dev : dycmxList){
				dycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML ,"", false, true, true, true);
			}
			
			List<PowerDevice> allmlkgList = new ArrayList<PowerDevice>();
			allmlkgList.addAll(gycmlkgList);
			allmlkgList.addAll(zycmlkgList);
			allmlkgList.addAll(dycmlkgList);
			
			for(PowerDevice dev : allmlkgList){
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					mlkgList.add(dev);
				}
			}
			
			if(mlkgList.size()==0){//单母不分段
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
					for(PowerDevice dev : zbList){
						List<PowerDevice> dyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
						List<PowerDevice> zyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						List<PowerDevice> gyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
						
						for(PowerDevice dyckg : dyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(dyckg).equals("0")){
								replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dyckg)+"/r/n";
							}
						}
						
						for(PowerDevice dyckg : zyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(dyckg).equals("0")){
								replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dyckg)+"/r/n";
							}
						}
						
						for(PowerDevice dyckg : gyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(dyckg).equals("0")){
								replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dyckg)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += "将"+deviceName+"由热备用转冷备用/r/n";
					}
				}else{
					//负荷侧
					for(PowerDevice dev : zbList){
						List<PowerDevice> kgList = RuleExeUtil.getTransformerSwitchByVol(dev, curDev.getPowerVoltGrade());
						
						for(PowerDevice dyckg : kgList){
							if(RuleExeUtil.getDeviceBeginStatus(dyckg).equals("0")){
								replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dyckg)+"/r/n";
							}
						}
					}
					
//					for(PowerDevice dev : xlkgList){
//						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
//							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
//						}
//					}
					
					for(PowerDevice dev : zybkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += "将"+deviceName+"由热备用转冷备用/r/n";
					}
				}
			}else{//单母分段
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
					for(PowerDevice dev  : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
					
					for(PowerDevice dev  : zycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
					
					for(PowerDevice dev  : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
					
					for(PowerDevice dev  : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
						}
					}
					
					for(PowerDevice dev  : zycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
						}
					}
					
					for(PowerDevice dev  : zbgyckgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						String ymxName = "";
						String mbmxName = "";

						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								List<PowerDevice> ymxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
								ymxName = CZPService.getService().getDevName(ymxList);
							}else if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
								List<PowerDevice> mbmxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
								mbmxName = CZPService.getService().getDevName(mbmxList);
							}
						}
						
						if(!ymxName.equals("") && !mbmxName.equals("")){
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由"+ymxName+"运行倒至"+mbmxName+"运行/r/n";
						}
					}
					
					for(PowerDevice dev  : zbzyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev  : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
						}
					}
					
					for(PowerDevice dev  : zbdyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev  : gycmlkgList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev  : zbgyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += "将"+deviceName+"由热备用转冷备用/r/n";
					}
				}else{
					zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+ CBSystemConstants.RunTypeSwitchFHC ,"", false, true, true, true);
					
					List<String> voltList = new ArrayList<String>();
					
					for(PowerDevice dev  : mlkgList){
						voltList.add((int)dev.getPowerVoltGrade()+"kV备自投装置");
					}					
					
					replaceStr += CommonFunction.getBztResult(voltList , "退出");
					
					if(curDev.getPowerVoltGrade() != 10){
//						for(PowerDevice dev : xlkgList){
//							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
//								replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
//							}
//						}
						
						for(PowerDevice dev : zybkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev  : mlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}

					for(PowerDevice dev : zbkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += "将"+deviceName+"由热备用转冷备用/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
