package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMXTDHSNR implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		StringBuffer reBuffer = new StringBuffer();
		StringBuffer reBuffer2 = new StringBuffer();

		if("母线停电核实内容".equals(tempStr)){
			Map<Integer, DispatchTransDevice> dtds = CBSystemConstants.getDtdMap();
			Set<PowerDevice>  kgList = new HashSet<PowerDevice>();
			
			for (Iterator<DispatchTransDevice> iterator = dtds.values().iterator(); iterator.hasNext();) {
				DispatchTransDevice dtd = iterator.next();
				PowerDevice dev = dtd.getTransDevice();
				if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
					List<PowerDevice> temp = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.Switch);
					
					if(temp.size()>0){
						for(PowerDevice pd : temp){
							if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								kgList.add(pd);
							}
						}
					}
				}
			}
			
			List<PowerDevice>  lbykgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch,  SystemConstants.PowerTransformer, true, true, true);

			List<PowerDevice>  mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine,  SystemConstants.PowerTransformer, true, true, true);
			
			for (Iterator<PowerDevice> iterator = mxList.iterator(); iterator.hasNext();) {
				PowerDevice mx = iterator.next();
				
				if(mx.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					iterator.remove();
				}
			}
			
			Map<String,PowerDevice> map  = new HashMap<String, PowerDevice>();
			
			for(PowerDevice dev : kgList){
				String state = RuleExeUtil.getStatusNew(dev.getDeviceType(), RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev)) ;
				map.put(state, dev);
			}
			
			List<PowerDevice> yxkfList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykfList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : kgList){
				String state = RuleExeUtil.getStatusNew(dev.getDeviceType(), RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev));
				
				if(state.equals("运行")){
					yxkfList.add(dev);
				}
				
				if(state.equals("热备用")){
					rbykfList.add(dev);
				}
			}
			
			if(lbykgList.size()>0){
				for(PowerDevice pd : lbykgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(pd).equals("2")){
						replaceStr += "核实"+CZPService.getService().getDevName(pd)+"冷备用/r/n";
					}
				}
			}
			
			if(yxkfList.size()>0){
				for(PowerDevice yxkf: yxkfList){
					reBuffer.append(CZPService.getService().getDevName(yxkf)+"、");
				}
				
				reBuffer.delete(reBuffer.length()-1, reBuffer.length());
				
				replaceStr += "核实"+reBuffer+"运行于"+CZPService.getService().getDevName(curDev).replace("组", "")+"/r/n";
			}
			
			
			if(rbykfList.size()>0){
				for(PowerDevice rbykf: rbykfList){
					reBuffer2.append(CZPService.getService().getDevName(rbykf)+"、");
				}
				
				reBuffer2.delete(reBuffer2.length()-1, reBuffer2.length());
				
				replaceStr += "核实"+reBuffer2+"热备用于"+CZPService.getService().getDevName(curDev).replace("组", "")+"/r/n";
			}
		}
		return replaceStr;
	}

}
