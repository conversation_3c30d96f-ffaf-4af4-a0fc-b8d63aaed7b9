package com.tellhow.czp.app.yndd.wordcard.yx;


import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXLKDZCZRW implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪拉开刀闸操作任务".equals(tempStr)){
			
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
