package com.tellhow.czp.app.yndd;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.service.CheckCzpImpl;
import com.tellhow.czp.service.CheckStatusImpl;
import com.tellhow.graphicframework.startup.StartupManager;

public class GetCheckImplTestWS {
    public static void main(String[] params) {
	    CheckCzpImpl check = new CheckCzpImpl();
	    CheckStatusImpl checkback = new CheckStatusImpl();
	
	    String param = "";
	
		StartupManager.startup();
		CZPService.getService().setArg(param);
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>文山地调</changzhan><caozuozhiling>遥控断开220kv开化变220kv#1主变35kV侧301断路器</caozuozhiling><cbid>1456</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>文山地调</changzhan><caozuozhiling>遥控断开220kV路德变110kV路东江线139断路器</caozuozhiling><cbid>1456</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas>	<TYPE>操作校核</TYPE>"
				+ "<CZRW>110kV马德线由运行转检修</CZRW>	<ITEM>		<changzhan>文山地调</changzhan>		"
				+ "<caozuozhiling>遥控拉开110kV马塘变110kV马德线1422隔离开关</caozuozhiling>		"
				+ "<cbid>b68f8838-28bc-4a23-8e02-ef3403650ed5</cbid>		<zlxh>8</zlxh>	"
				+ "	<refresh>true</refresh>		<zhixing>false</zhixing>		<roleCode>0</roleCode>	"
				+ "	<ischeck>1</ischeck>		<isrealtime>1</isrealtime>	</ITEM></Datas>";
		
		check.execute(param);
    }
}
