package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.km.LoneMotherLineExecute;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;
import czprule.wordcard.replaceclass.impl.ReplaceBooDMJX;

public class ReplaceStrKMDMJXMXFD implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";

		if("昆明单母接线母线复电".equals(tempStr)) {
			String result = "";
			PowerDevice hskg = new PowerDevice();
			PowerDevice dkkg = new PowerDevice();

			if(LoneMotherLineExecute.tagMap != null){
				if(LoneMotherLineExecute.tagMap.size()>0){
					result = LoneMotherLineExecute.tagMap.get("是否合环");
					hskg = CBSystemConstants.getPowerDevice(LoneMotherLineExecute.tagMap.get("合环开关"));
					dkkg = CBSystemConstants.getPowerDevice(LoneMotherLineExecute.tagMap.get("断开开关"));
				}
			}
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);

			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "", true, true, true);
			
			if(zbList.size()>1){
				for(Iterator<PowerDevice> itor = zbList.iterator();itor.hasNext();){
					PowerDevice zb  = itor.next();
					
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(zb).equals("0")){
						itor.remove();
					}
				}
			}
			
			if(zbList.size()>0){
				PowerDevice powertransformer = zbList.get(0);
				PowerDevice gycmlkg = new PowerDevice();
				PowerDevice zycmlkg = new PowerDevice();
				PowerDevice dycmlkg = new PowerDevice();

				List<PowerDevice> gycswList = new ArrayList<PowerDevice>();
				List<PowerDevice> zycswList = new ArrayList<PowerDevice>();
				List<PowerDevice> dycswList = new ArrayList<PowerDevice>();
				
				
				if(zbList.size()>1){
					for(PowerDevice zb : zbList){
						gycswList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
						zycswList.addAll(RuleExeUtil.getTransformerSwitchMiddle(zb));
						dycswList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
					}
				}else{
					gycswList = RuleExeUtil.getTransformerSwitchHigh(zbList.get(0));
					zycswList = RuleExeUtil.getTransformerSwitchMiddle(zbList.get(0));
					dycswList = RuleExeUtil.getTransformerSwitchLow(zbList.get(0));
				}
				
				List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();			 	
				
				for(PowerDevice gycsw : gycswList){
					List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(gycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					gycmlkgList.addAll(tempList);
				}
				
				for(PowerDevice zycsw : zycswList){
					List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(zycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					zycmlkgList.addAll(tempList);
				}
				
				for(PowerDevice dycsw : dycswList){
					List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(dycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					dycmlkgList.addAll(tempList);
				}
				
				
				List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true);

				if(gycmlkgList.size()>0){
					gycmlkg = gycmlkgList.get(0);
				}
				
				if(zycmlkgList.size()>0){
					zycmlkg = zycmlkgList.get(0);
				}
				
				if(dycmlkgList.size()>0){
					dycmlkg = dycmlkgList.get(0);
				}
				
				if(curDev.getPowerVoltGrade() == dycmlkg.getPowerVoltGrade()){
					dycmlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
					dycmlkg = dycmlkgList.get(0);
				}
				
			 	List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSwitchZYB+","+CBSystemConstants.RunTypeSwitchJDB, "", false, true, false, true);
			 	List<PowerDevice> drdkList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, false, true);

				if(curDev.getPowerVoltGrade() < station.getPowerVoltGrade() ){//母线是负荷侧
					ReplaceBooDMJX dmjx = new ReplaceBooDMJX();

					if(curDev.getPowerVoltGrade() == 10||curDev.getPowerVoltGrade() == 6){
						//有出线
						if(xlkgList.size()>0){
							for(PowerDevice zyb : zybList){
						 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
							 		if(RuleExeUtil.getDeviceBeginStatus(zyb).equals("2")){
							 			replaceStr += "将"+CZPService.getService().getDevName(zyb)+"由冷备用转热备用/r/n";
							 		}
						 		}
						 	}
							
							for(PowerDevice drdk : drdkList){
						 		if(RuleExeUtil.getDeviceBeginStatus(drdk).equals("2")){
						 			replaceStr += "将"+CZPService.getService().getDevName(drdk)+"由冷备用转热备用/r/n";
						 		}
						 	}
						}
						
						
						if(com.tellhow.czp.app.yndd.rule.RuleExeUtil.isTransformerXBDY(powertransformer)||RuleExeUtil.isTransformerXBZ(powertransformer)){//线变组或者是线变单元接线
							List<PowerDevice> zbLists = new ArrayList<PowerDevice>();
							
							for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
								PowerDevice dev = it2.next();
								
								if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
									zbLists.add(dev);
								}
							}
							
							if(zbLists.size() == 3){//3主变
								replaceStr += "将"+CZPService.getService().getDevName(dycmlkg)+"由冷备用转热备用/r/n";
								
								if(xlkgList.size()>0){
						 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
								}else{
						 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"及各分路由冷备用转热备用/r/n";
								}
								
								if(!dycmlkg.getPowerDeviceID().equals("")){
									if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycmlkgList.get(0)).equals("1")&&RuleExeUtil.getDeviceEndStatus(dycmlkgList.get(0)).equals("1")){
							 			replaceStr +=  "投入"+CZPService.getService().getDevName(dycmlkg)+"备自投装置/r/n";
									}
								}
								
					 			for(PowerDevice pd : dycswList){
					 				if(RuleExeUtil.getDeviceEndStatus(pd).equals("0")){
							 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(pd)+"/r/n";
					 				}
					 			}

					 			for(PowerDevice zyb : zybList){
							 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
								 		if(RuleExeUtil.getDeviceEndStatus(zyb).equals("0")){
								 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
								 		}
							 		}
							 	}
							}else if(zbLists.size() == 2){//2主变
								if(curDev.getPowerStationName().contains("月牙变")){
									replaceStr += "将"+CZPService.getService().getDevName(dycmlkg)+"由冷备用转热备用/r/n";
									
									if(xlkgList.size()>0){
							 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
									}else{
							 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"及各分路由冷备用转热备用/r/n";
									}
									
									if(dycmlkgList.size()>0){
										if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycmlkgList.get(0)).equals("1")&&RuleExeUtil.getDeviceEndStatus(dycmlkgList.get(0)).equals("1")){
								 			replaceStr +=  "投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
										}
									}
									
									List<PowerDevice> zbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
									List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
									
									for(PowerDevice zbkg : zbkgList){
										if(RuleExeUtil.getDeviceEndStatus(zbkg).equals("0")){
								 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zbkg)+"/r/n";
								 		}
									}
									
									for(PowerDevice mlkg : mlkgList){
										if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
								 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
								 		}
									}
									
									for(PowerDevice zyb : zybList){
								 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
									 		if(RuleExeUtil.getDeviceEndStatus(zyb).equals("0")){
									 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
									 		}
								 		}
								 	}
								}else{
									replaceStr += "将"+CZPService.getService().getDevName(dycmlkg)+"由冷备用转热备用/r/n";
									
									if(xlkgList.size()>0){
							 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
									}else{
							 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"及各分路由冷备用转热备用/r/n";
									}
									
									if(!dycmlkg.getPowerDeviceID().equals("")){
										if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycmlkgList.get(0)).equals("1")&&RuleExeUtil.getDeviceEndStatus(dycmlkgList.get(0)).equals("1")){
								 			replaceStr +=  "投入"+(int)dycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
										}
									}
									
									if(result.equals("能合环")){
										replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
							 			
							 			for(PowerDevice pd : dycswList){
								 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(pd)+"/r/n";
							 			}
							 			
										replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(hskg)+"/r/n";
							 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
							 			
							 			for(PowerDevice zyb : zybList){
									 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
										 		if(RuleExeUtil.getDeviceEndStatus(zyb).equals("0")){
										 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
										 		}
									 		}
									 	}
									}else if(result.equals("不能合环")){
										for(PowerDevice pd : dycswList){
								 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(pd)+"/r/n";
							 			}
										
							 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
										replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(hskg)+"/r/n";
							 			
							 			for(PowerDevice zyb : zybList){
									 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
										 		if(RuleExeUtil.getDeviceEndStatus(zyb).equals("0")){
										 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
										 		}
									 		}
									 	}
									}
								}
							}else{
								if(xlkgList.size()>0){
						 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
								}else{
						 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"及各分路由冷备用转热备用/r/n";
								}
								
								for(PowerDevice pd : dycswList){
					 				if(RuleExeUtil.getDeviceEndStatus(pd).equals("0")){
							 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(pd)+"/r/n";
					 				}
					 			}
							}
						}else if(dmjx.strReplace("单母接线", curDev, stationDev, desc)){
							for(PowerDevice gycsws : gycswList){
								if(RuleExeUtil.getDeviceBeginStatus(gycsws).equals("2")){
									List<PowerDevice> tempList = RuleExeUtil.getDeviceList(gycsws, SystemConstants.PowerTransformer, "", true, true, true);
									
						 			replaceStr += "将"+stationName+CZPService.getService().getDevName(tempList)+"由冷备用转热备用/r/n";
								}
							}
							
							if(!dycmlkg.getPowerDeviceID().equals("")){
					 			replaceStr += "将"+CZPService.getService().getDevName(dycmlkg)+"由冷备用转热备用/r/n";
							}
							
							if(xlkgList.size()>0){
					 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
							}else{
					 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"及各分路由冷备用转热备用/r/n";
							}
							
							for(PowerDevice gycsws : gycswList){
								if(RuleExeUtil.getDeviceEndStatus(gycsws).equals("0")){
						 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycsws)+"/r/n";
								}
							}
							
							for(PowerDevice dycsws : dycswList){
								if(RuleExeUtil.getDeviceEndStatus(dycsws).equals("0")){
						 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dycsws)+"/r/n";
								}
							}
							
							for(PowerDevice zyb : zybList){
						 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
							 		if(RuleExeUtil.getDeviceEndStatus(zyb).equals("0")){
							 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
							 		}
						 		}
						 	}
						}else{//单母分段
							List<PowerDevice> mxLists = new ArrayList<PowerDevice>();
							
							for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
								PowerDevice dev = it2.next();
								
								if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&dev.getPowerVoltGrade() == 10){
									mxLists.add(dev);
								}
							}
							
							for(PowerDevice gycsws : gycswList){
								if(RuleExeUtil.getDeviceBeginStatus(gycsws).equals("2")){
									List<PowerDevice> tempList = RuleExeUtil.getDeviceList(gycsws, SystemConstants.PowerTransformer, "", true, true, true);
									
						 			replaceStr += "将"+stationName+CZPService.getService().getDevName(tempList)+"由冷备用转热备用/r/n";
								}
							}
							
				 			replaceStr += "将"+CZPService.getService().getDevName(dycmlkg)+"由冷备用转热备用/r/n";
							
							if(xlkgList.size()>0){
					 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
							}else{
					 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"及各分路由冷备用转热备用/r/n";
							}
							
							if(dycmlkgList.size()>0){
								if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycmlkgList.get(0)).equals("1")
										&&RuleExeUtil.getDeviceEndStatus(dycmlkgList.get(0)).equals("1")){
									if(mxLists.size()>3){
							 			replaceStr +=  "投入"+CZPService.getService().getDevName(dycmlkgList.get(0))+"备自投装置/r/n";
									}else{
							 			replaceStr +=  "投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
									}
								}
							}
							
							List<PowerDevice> zbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
							List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
							
							for(PowerDevice zbkg : zbkgList){
								if(RuleExeUtil.getDeviceEndStatus(zbkg).equals("0")){
						 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zbkg)+"/r/n";
						 		}
							}
							
							for(PowerDevice mlkg : mlkgList){
								if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
						 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
						 		}
							}
							
							for(PowerDevice zyb : zybList){
						 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
							 		if(RuleExeUtil.getDeviceEndStatus(zyb).equals("0")){
							 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
							 		}
						 		}
						 	}
						}
					}
				}else{
					ReplaceStrDMJXMXLBYZRBY lbytorby = new ReplaceStrDMJXMXLBYZRBY();
					replaceStr += lbytorby.strReplace("单母接线母线冷备用转热备用", curDev, stationDev, desc);
					
					for(PowerDevice zb : zbList){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(zb, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						
						if(gdList.size() > 0){
							replaceStr += "落实"+CZPService.getService().getDevName(gdList)+"处合位/r/n";
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : gycswList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zycswList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : dycswList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					List<PowerDevice>  ymxList = new ArrayList<PowerDevice>(); 
					List<PowerDevice>  tagmxList = new ArrayList<PowerDevice>(); 
					
					for(PowerDevice gyczbkg : gycswList){
						List<PowerDevice>  dzList =  RuleExeUtil.getDeviceDirectList(gyczbkg, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								ymxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
							}else if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
								tagmxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
							}
						}
					}
					
					if(ymxList.size()>0&&tagmxList.size()>0){
 			 			replaceStr += "将"+CZPService.getService().getDevName(gycswList)+"由"+CZPService.getService().getDevName(ymxList)+"运行倒至"+CZPService.getService().getDevName(tagmxList)+"运行/r/n";
					}
					
					ReplaceStrTR10KVBZTZZ tr10kvbzt = new ReplaceStrTR10KVBZTZZ();
					
					if(tr10kvbzt.strReplace("投入10kV备自投装置", curDev, stationDev, desc)!=null){
						replaceStr += tr10kvbzt.strReplace("投入10kV备自投装置", curDev, stationDev, desc);
					}
					
					ReplaceStrTR35KVBZTZZ tr35kvbzt = new ReplaceStrTR35KVBZTZZ();
					
					if(tr35kvbzt.strReplace("投入35kV备自投装置", curDev, stationDev, desc)!=null){
						replaceStr += tr35kvbzt.strReplace("投入35kV备自投装置", curDev, stationDev, desc);
					}
					
					replaceStr += "投入110kV备自投装置";
				}
			}else{
				List<PowerDevice> dycmlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				List<PowerDevice> tempmlkgList = new ArrayList<PowerDevice>();
				
				for(PowerDevice dycmlkg : dycmlkgList){
					tempmlkgList = RuleExeUtil.getDeviceList(dycmlkg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
					if(tempmlkgList.size()>0){
						break;
					}
				}
				
				dycmlkgList.addAll(tempmlkgList);
				
				for(PowerDevice dycmlkg : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("2")){
						replaceStr += "将"+CZPService.getService().getDevName(dycmlkg)+"由冷备用转热备用/r/n";
					}
				}
				
				List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true);
				
				if(xlkgList.size()>0){
		 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
				}else{
		 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"及各分路由冷备用转热备用/r/n";
				}
				
				
				for (Iterator<PowerDevice> it2 = dycmlkgList.iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getPowerDeviceName().contains("002")||dev.getPowerDeviceName().contains("004")){
						it2.remove();
					}
				}
				
				if(dycmlkgList.size()>0){
					for(PowerDevice dycmlkg : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("0")){
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
						}
					}
				}
				
				List<PowerDevice> zjmlkgList = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

						if(tempList.size()>0&&RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							zjmlkgList.add(dev);
						}
					}
				}
				
				if(zjmlkgList.size()>0){
					for(PowerDevice zjmlkg : zjmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(zjmlkg).equals("0")&&tempmlkgList.contains(zjmlkg)){
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zjmlkg)+"/r/n";
						}
					}
					
					for(PowerDevice zjmlkg : zjmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(zjmlkg).equals("0")&&!tempmlkgList.contains(zjmlkg)){
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zjmlkg)+"/r/n";
						}
					}
				}
				
			 	List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, false, true);
				
				for(PowerDevice zyb : zybList){
			 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
				 		if(RuleExeUtil.getDeviceEndStatus(zyb).equals("0")){
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
				 		}
			 		}
			 	}
				
				if(dycmlkgList.size()>0){
					if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycmlkgList.get(0)).equals("1")&&RuleExeUtil.getDeviceEndStatus(dycmlkgList.get(0)).equals("0")){
			 			replaceStr +=  "投入"+(int)dycmlkgList.get(0).getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
