package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.view.ShowLoopViewDialog;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

public class LoopPathDDExecute implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(CBSystemConstants.jh_tai == 1)
			return true;
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;

		String stationid = pd.getPowerStationID();
		String stationname = pd.getPowerStationName();
		double voltageid = CBSystemConstants.getPowerStation(stationid).getPowerVoltGrade();
		
		String sql  = "select distinct t.stationname,(select c.voltage_name from "+CBSystemConstants.opcardUser+"t_voltagelevel c where c.voltage_id =  t.voltage_id ) as voltageid,t.stationid  from  "+CBSystemConstants.opcardUser+"t_c_station_terminal t where t.connectivitynode_id in (select t.connectivitynode_id from  "+CBSystemConstants.opcardUser+"t_c_station_terminal t  where t.stationid = '"+stationid +"') and t.stationid != '"+stationid+"'";
		List<Map<String,String>> list = DBManager.queryForList(sql);
		String stids = stationid+"|";
		String path = stationname+"-";

		if(list.size()>0){
			for(Iterator<Map<String, String>> itor = list.iterator();itor.hasNext();){
				Map<String, String> devMap = itor.next();
				String volt =  devMap.get("voltageid");
				String stname =  devMap.get("stationname");

				if(Integer.valueOf(volt)<voltageid){
					itor.remove();
				}else if(stname.contains("虚拟站")){
					itor.remove();
				}
			}
		}
		
		List<List<String>> alldataList = new ArrayList<List<String>>();
		
		for(Map<String,String> map : list){
			List<String> retList = new ArrayList<String>();
			
			List<String>  resultList =  getStationPath(retList,map,path,stids,stationid);
			
			alldataList.add(resultList);
		}
		
		List<List<PowerDevice>> allLoopPathList = new ArrayList<List<PowerDevice>>();
		
		for(int i= 0 ; i < alldataList.size()-1; i++){

			List<String> dataList = alldataList.get(i);
			
			for(String string1 :dataList){
				String[] arr1 = string1.split("\\|");
			
				for(int k = i+1 ; k < alldataList.size() ; k++){
					List<String> dataNextList = alldataList.get(k);

					for(String string2 :dataNextList){
						String[] arr2 = string2.split("\\|");
						
						if(getLoopPath(arr1, arr2).size()>3){
							allLoopPathList.add(getLoopPath(arr1, arr2));
						}
					}
				}
			}
		}
		
		for(int i = 0 ; i  <  allLoopPathList.size() - 1 ; i++ )  {       
			for( int j = allLoopPathList.size() - 1 ; j  >  i; j--)  {       
				if(allLoopPathList.get(j).equals(allLoopPathList.get(i)))  {       
					allLoopPathList.remove(j);       
				}        
			}        
		}  
		
		ShowLoopViewDialog dialog =  new ShowLoopViewDialog(SystemConstants.getMainFrame(), true, allLoopPathList);
		
		if(dialog.isCancel()){
			return false;
		}
		
		return true;
	}
	

//  String sql = "select distinct t.acline_id from "+CBSystemConstants.opcardUser+"t_c_aclineend t where t.voltage_id in ('BV-1','BV-2','BV-3','BV-4','BV-5')";
//
//  List<Map<String,String>> list = DBManager.queryForList(sql);
//  
//  if(list.size()>0){
//  	for(Map<String,String> map : list){
//  		String acline_id = map.get("ACLINE_ID");
//  		
//  		sql = "select a.station_id,a.station_name,a.voltage_id from "+CBSystemConstants.opcardUser+"t_substation a where  a.station_id in (select t.st_id from "+CBSystemConstants.opcardUser+"t_c_aclineend t where t.acline_id = '"+acline_id+"')";
//  		
//          List<Map<String,String>> newlist = DBManager.queryForList(sql);
//
//  		
//          for(Map<String,String> map1 : newlist){
//      		String station_id = map1.get("STATION_ID");
//      		String station_name = map1.get("STATION_NAME");
//      		String voltage_id = map1.get("VOLTAGE_ID");
//
//      		sql = "insert into "+CBSystemConstants.opcardUser+"t_c_station_terminal (id, stationid, stationname, connectivitynode_id,voltage_id) values ('"+UUID.randomUUID()+"','"+station_id+"','"+station_name+"','"+acline_id+"','"+voltage_id+"')";
//      		
//      		DBManager.execute(sql);
//      		
//          }
//  	}
//  }
  
	
	public List<String> getStationPath(List<String> resultList , Map<String, String> map,String path,String stationids,String excstationid){
		String stationname = map.get("stationname");
		String stationid = map.get("stationid");
		String voltageid = map.get("voltageid");
		
		if(!path.contains(stationname)){
			path += stationname+"-";
			stationids += stationid+"|";
		}
	
		String sql  = "select distinct t.stationname,(select c.voltage_name from "+CBSystemConstants.opcardUser+"t_voltagelevel c where c.voltage_id =  t.voltage_id ) as voltageid,t.stationid  from  "+CBSystemConstants.opcardUser+"t_c_station_terminal t where t.connectivitynode_id in (select t.connectivitynode_id from  "+CBSystemConstants.opcardUser+"t_c_station_terminal t  where t.stationid = '"+stationid +"') and t.stationid not in ('"+stationid+"','"+excstationid+"')";
		
		List<Map<String,String>> datalist = DBManager.queryForList(sql);
		
		if(datalist.size()>0){
			for(Iterator<Map<String, String>> itor = datalist.iterator();itor.hasNext();){
				Map<String, String> devMap = itor.next();
				String volt =  devMap.get("voltageid");
				String stid =  devMap.get("stationid");
				String stname =  devMap.get("stationname");
	
				if(Integer.valueOf(volt)<Integer.valueOf(voltageid)){
					itor.remove();
					continue;
				}else if(stname.contains("虚拟站")){
					continue;
				}
				
				if(Integer.valueOf(volt)<500&&!path.contains(stname)){
					getStationPath(resultList,devMap,path,stationids,stationid);
				}else{
					System.out.println(path+stname);
					resultList.add(stationids+stid);
				}
			}
		}
		return resultList;
	}

	public List<PowerDevice> getLoopPath(String[] arr1,String[] arr2){
		List<PowerDevice> loopPath = new ArrayList<PowerDevice>();
		List<PowerDevice> beginPath = new ArrayList<PowerDevice>();
		
		for(int i=0;i< arr1.length;i++){
			String string1 = arr1[i];
			
			PowerDevice station1 = CBSystemConstants.getPowerStation(string1);
			
			beginPath.add(station1);
			
			if(!string1.equals("")&&i>0){
				List<PowerDevice> endPath = new ArrayList<PowerDevice>();
				for(int j=0;j< arr2.length;j++){
					String string2 = arr2[j];
					PowerDevice station2 = CBSystemConstants.getPowerStation(string2);
	
					if(string1.equals(string2)&&j>0){
						Collections.reverse(endPath);
						
						loopPath.addAll(beginPath);
						loopPath.addAll(endPath);
						
						return loopPath;
					}else if(station2.getPowerVoltGrade() == 500&&station1.getPowerVoltGrade() == 500){
						String sql = "select count(1) from "+CBSystemConstants.opcardUser+"t_c_station_terminal t where t.connectivitynode_id in (select t.connectivitynode_id from "+CBSystemConstants.opcardUser+"t_c_station_terminal t where t.stationid = '"+station2.getPowerDeviceID()+"') and t.stationid = '"+station1.getPowerDeviceID()+"'";
						
						if(DBManager.queryForInt(sql)>0){
							endPath.add(station2);
							Collections.reverse(endPath);
							
							loopPath.addAll(beginPath);
							loopPath.addAll(endPath);
							
							return loopPath;
						}
					}else{
						endPath.add(station2);
					}
				}
			}
		}
		return loopPath;
	}
}
