package com.tellhow.czp.app.yndd.view;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JOptionPane;
import javax.swing.JTable;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumn;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.mainframe.EachRowEditor;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.ColorTableModel;
import czprule.rule.view.EquipListView;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.view.InitDeviceTypeChockBox;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013-7-11 上午11:38:02 
 */
public class EquipStatusChooseYHZYX extends javax.swing.JDialog {
	private List<PowerDevice> equipList = new ArrayList<PowerDevice>();
	private Map<PowerDevice,Map<String,String>> tagStatusMap = new HashMap<PowerDevice, Map<String,String>>();
//	private List<String> expStatusList;
	private List<String> defaultStatusList;

	/** Creates new form EquipCheckChoose */
	public EquipStatusChooseYHZYX(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, List<String> defaultStatusList, String showMessage) {
		super(parent, modal);
		this.defaultStatusList = defaultStatusList;
		if (equipsList != null)
			this.equipList = equipsList;
		initComponents();
		this.jLabel1.setText(showMessage);
		
		this.initTable(new ArrayList<String>());
		FitTableColumns(jTable1);
		this.setLocationCenter();
		this.setVisible(true);
	}
	public EquipStatusChooseYHZYX(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, List<String> defaultStatusList,List<String> expStatusList, String showMessage) {
		super(parent, modal);
		this.defaultStatusList = defaultStatusList;
		if (equipsList != null)
			this.equipList = equipsList;
		initComponents();
		this.jLabel1.setText(showMessage);
		
		this.initTable(expStatusList);
		FitTableColumns(jTable1);
		this.setLocationCenter();
		this.setVisible(true);
	}
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">
    private void initComponents() {

        jLabel1 = new javax.swing.JLabel();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        jButton1 = new javax.swing.JButton();
        jButton2 = new javax.swing.JButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

        jLabel1.setText("jLabel1");

        jScrollPane1.setViewportView(jTable1);

        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/ok.png"))); // NOI18N
        jButton1.setToolTipText("确定");
        jButton1.setText("确定");
        jButton1.setMargin(new java.awt.Insets(1,1,1,1));
        jButton1.setFocusPainted(false);
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
        jButton2.setToolTipText("取消");
        jButton2.setText("取消");
        jButton2.setMargin(new java.awt.Insets(1,1,1,1));
        jButton2.setFocusPainted(false);
        jButton2.setVisible(false);
        jButton2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jLabel1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 350, Short.MAX_VALUE)
            .add(layout.createSequentialGroup()
                .addContainerGap(org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .add(jButton1)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jButton2)
                .add(5, 5, 5))
            .add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .add(jLabel1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 24, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED, 22, Short.MAX_VALUE)
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.TRAILING)
                    .add(jButton2)
                    .add(jButton1))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jScrollPane1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 120+equipList.size()*15, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
        );

        pack();
    }// </editor-fold>

    private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
        // TODO add your handling code here:
		this.setVisible(false);
		this.dispose();
    }


	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		for (int i = 0; i < jtablemodel.getRowCount(); i++) {
			CodeNameModel cnm = (CodeNameModel)jtablemodel.getValueAt(i, 1);
			PowerDevice pd = null;
			 for (int j = 0; j < equipList.size(); j++) {
				 if(equipList.get(j).getPowerDeviceID().equals(cnm.getCode())) {
					 pd = equipList.get(j);
					 break;
				 }
			 }
			String beginstatus = ((CodeNameModel)jtablemodel.getValueAt(i, 2)).getCode();
			String endstatus = ((CodeNameModel)jtablemodel.getValueAt(i, 3)).getCode();

			if(endstatus.equals("")) {
				JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "没有选择目标状态！", "提示", JOptionPane.WARNING_MESSAGE);
				return;
			}
			
			Map<String,String> map = new HashMap<String, String>();
			
			map.put("beginstatus", beginstatus);
			map.put("endstatus", endstatus);

			tagStatusMap.put(pd, map);
		}
		this.setVisible(false);
		this.dispose();
	}

	public void initTable(List<String> expStatusList) {//expStatusList:下拉框排除的状态列表
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		PowerDevice pd = null;
		jtablemodel.setRowTitle(new String[] { "厂站名称", "设备名称","初始状态", "目标状态（可选）"});
		jTable1.setModel(jtablemodel);
		jTable1.setRowHeight(20);

	    EachRowEditor rowEditor = new EachRowEditor(jTable1);
	    EachRowEditor rowEditor2 = new EachRowEditor(jTable1);

	    for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			String defaultStatus =defaultStatusList.get(i);
			
			JComboBox cb = new JComboBox(InitDeviceTypeChockBox.getDeviceStatusCheckBox(pd.getDeviceType(),expStatusList,defaultStatus));
			rowEditor.setEditorAt(i, new DefaultCellEditor(cb));
			
			JComboBox cb2 = new JComboBox();
			
			DefaultComboBoxModel model = new DefaultComboBoxModel();
			
			for(int j=0;j<4;j++){
				CodeNameModel cnm= new CodeNameModel();
				
				switch(j){
					case 0:cnm.setCode(j+"");cnm.setName("运行");break;
					case 1:cnm.setCode(j+"");cnm.setName("热备用");break;
					case 2:cnm.setCode(j+"");cnm.setName("冷备用");break;
					case 3:cnm.setCode(j+"");cnm.setName("检修");break;
				}
				
				if(cnm.getCode().equals(pd.getDeviceStatus())){
					model.setSelectedItem(cnm);
				}
				
				model.addElement(cnm);
			}
			
			cb2.setModel(model);
			
			rowEditor2.setEditorAt(i, new DefaultCellEditor(cb2));

			rowData.add(new Object[] { pd.getPowerStationName(), new CodeNameModel(pd.getPowerDeviceID(),pd.getPowerDeviceName()),cb2.getSelectedItem(), cb.getSelectedItem() });
	    }
	    jTable1.getColumnModel().getColumn(3).setCellEditor(rowEditor);
	    jTable1.getColumnModel().getColumn(2).setCellEditor(rowEditor2);
	    jtablemodel.setRowData(rowData);
	    
	}

	public Map<PowerDevice,Map<String,String>> getTagStatusMap() {
		return tagStatusMap;
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				EquipListView dialog = new EquipListView(
						new javax.swing.JFrame(), true, null, null);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

    // Variables declaration - do not modify
    private javax.swing.JButton jButton1;
    private javax.swing.JButton jButton2;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTable jTable1;
    // End of variables declaration

	private ColorTableModel jtablemodel;
	
	//Jtable列宽自适应
	public void FitTableColumns(JTable myTable){  
	    JTableHeader header = myTable.getTableHeader();  
	     int rowCount = myTable.getRowCount();  
	  
	     Enumeration columns = myTable.getColumnModel().getColumns();  
	     while(columns.hasMoreElements()){  
	         TableColumn column = (TableColumn)columns.nextElement();  
	         int col = header.getColumnModel().getColumnIndex(column.getIdentifier());  
	         int width = (int)myTable.getTableHeader().getDefaultRenderer()  
	                 .getTableCellRendererComponent(myTable, column.getIdentifier()  
	                         , false, false, -1, col).getPreferredSize().getWidth();  
	         for(int row = 0; row<rowCount; row++){  
	             int preferedWidth = (int)myTable.getCellRenderer(row, col).getTableCellRendererComponent(myTable,  
	             myTable.getValueAt(row, col), false, false, row, col).getPreferredSize().getWidth();  
	             width = Math.max(width, preferedWidth)+2;  
	         }  
	         header.setResizingColumn(column); // 此行很重要  
	         column.setWidth(width+myTable.getIntercellSpacing().width);  
	     }  
	}  
}
