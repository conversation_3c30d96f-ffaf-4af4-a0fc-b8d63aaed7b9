package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.qj.JDKGXZQJ;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJXLTD  implements TempStringReplace {
	private static final Pattern NUMBER_PATTERN = Pattern.compile("[^0-9.]");

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖线路停电".equals(tempStr)){
			List<Map<String, String>> stationLineList = CommonFunctionQJ.getStationLineList(curDev);
			
			for(Map<String, String> stationLine : stationLineList) {
                String stationName = StringUtils.ObjToString(stationLine.get("UNIT")).trim();
                String switchName = StringUtils.ObjToString(stationLine.get("SWITCH_NAME")).trim();
                String disconnectorName = StringUtils.ObjToString(stationLine.get("DISCONNECTOR_NAME")).trim();
                String lowerunit = StringUtils.ObjToString(stationLine.get("LOWERUNIT")).trim();
                String operationkind = StringUtils.ObjToString(stationLine.get("OPERATION_KIND")).trim();
                String endpointkind = StringUtils.ObjToString(stationLine.get("ENDPOINT_KIND")).trim();
                String lineName = StringUtils.ObjToString(stationLine.get("LINE_NAME")).trim();
				String voltage = StringUtils.ObjToString(stationLine.get("VOLTAGE")).trim();
				double volt;
                try {
                    String numberOnly = NUMBER_PATTERN.matcher(voltage).replaceAll("");
                    volt = Double.parseDouble(numberOnly);
                } catch (NumberFormatException e) {
					volt = 0;
					e.printStackTrace();
                }

                if (operationkind.equals("许可")) {
                    if (!disconnectorName.isEmpty()) {
                        replaceStr += stationName + "@核实" + lowerunit + disconnectorName + "确处分闸位置/r/n";
                    }
                } else if (operationkind.equals("下令")) {
					if (volt <= 35) {
						replaceStr += stationName + "@核实" + lowerunit + switchName + "确处热备用/r/n";
					} else if (volt >= 110) {
						if (endpointkind.equals("风电场")) {
							if (disconnectorName.isEmpty()) {
								replaceStr += stationName + "@断开" + lowerunit + switchName + "/r/n";

							} else {
								replaceStr += stationName + "@核实" + lowerunit + switchName + "确已断开/r/n";
								replaceStr += stationName + "@核实" + lowerunit + disconnectorName + "确已拉开/r/n";
							}
						} else {
							replaceStr += stationName + "@断开" + lowerunit + switchName + "/r/n";
						}
					}
                }
            }
			
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			for(PowerDevice loadLineTran : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 
				
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				List<PowerDevice> otherxlkgList = new ArrayList<PowerDevice>();

				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();

					if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) && !xlkgList.contains(dev)){
							otherxlkgList.add(dev);
						}
					}
				}

				if(mxList.size()>0){
					if(mxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
						for(PowerDevice xlkg : xlkgList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
								if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
									replaceStr += CommonFunctionQJ.getSwitchOffContent(xlkg, stationName, station);
								}
							}
						}
						
						for(PowerDevice xlkg : xlkgList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
								if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
									replaceStr += CommonFunctionQJ.getSwitchOffContent(xlkg, stationName, station);
								}
							}
						}
					}else{
						for(PowerDevice mlkg : mlkgList){
							if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
								replaceStr += CommonFunctionQJ.getHhContent(mlkg, "曲靖地调", stationName);
							}
						}
						
						for(PowerDevice otherxlkg : otherxlkgList){
							if(RuleExeUtil.getDeviceEndStatus(otherxlkg).equals("0")){
								replaceStr += CommonFunctionQJ.getHhContent(otherxlkg, "曲靖地调", stationName);
							}
						}

						for(PowerDevice xlkg : xlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
								replaceStr += CommonFunctionQJ.getSwitchOffContent(xlkg, stationName, station);
							}
						}
					}
				}else{//内桥接线
					for(PowerDevice mlkg : mlkgList){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
							replaceStr += CommonFunctionQJ.getHhContent(mlkg, "曲靖地调", stationName);
						}
					}
					
					for(PowerDevice otherxlkg : otherxlkgList){
						if(RuleExeUtil.getDeviceEndStatus(otherxlkg).equals("0")){
							replaceStr += CommonFunctionQJ.getHhContent(otherxlkg, "曲靖地调", stationName);
						}
					}

					for(PowerDevice xlkg : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
							replaceStr += CommonFunctionQJ.getSwitchOffContent(xlkg, stationName, station);
						}
					}
				}
			}
			
			if(sourceLineTrans != null && !sourceLineTrans.getPowerDeviceID().equals("")){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 
				
				List<PowerDevice> xlkglist = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				
				if(xlkglist.size() == 2){
					for(PowerDevice xlkg : xlkglist){
						if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
								replaceStr += CommonFunctionQJ.getSwitchOffContent(xlkg, stationName, station);
							}
						}
					}
					
					for(PowerDevice xlkg : xlkglist){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
								replaceStr += CommonFunctionQJ.getSwitchOffContent(xlkg, stationName, station);
							}
						}
						
					}
				}else{
					for(PowerDevice xlkg : xlkglist){
						if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
							replaceStr += CommonFunctionQJ.getSwitchOffContent(xlkg, stationName, station);
						}
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("2") || curDev.getDeviceStatus().equals("3")){//用户站热备用转冷备用
				for(Map<String, String> stationLine : stationLineList) {
					String stationName = StringUtils.ObjToString(stationLine.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(stationLine.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(stationLine.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(stationLine.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(stationLine.get("OPERATION_KIND")).trim();
					String endpointkind = StringUtils.ObjToString(stationLine.get("ENDPOINT_KIND")).trim();
					String lineName = StringUtils.ObjToString(stationLine.get("LINE_NAME")).trim();

					String voltage = StringUtils.ObjToString(stationLine.get("VOLTAGE")).trim();
					double volt;
					try {
						String numberOnly = NUMBER_PATTERN.matcher(voltage).replaceAll("");
						volt = Double.parseDouble(numberOnly);
					} catch (NumberFormatException e) {
						volt = 0;
						e.printStackTrace();
					}

					if(operationkind.equals("下令")){
						if (volt <= 35) {
							replaceStr += stationName + "@核实" + lowerunit + switchName + "确处冷备用/r/n";
						} else if (volt >= 110) {
							if(endpointkind.equals("风电场")){
								if(disconnectorName.equals("")){
//									replaceStr += stationName+"@将"+lowerunit+switchName+"由热备用转冷备用/r/n";
									replaceStr += stationName + "@断开" + lowerunit + switchName + "/r/n";
								}
							}else{
//								replaceStr += stationName+"@将"+lowerunit+switchName+"由热备用转冷备用/r/n";
								replaceStr += stationName + "@断开" + lowerunit + switchName + "/r/n";
							}
						}
					}
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 

				List<Map<String, String>> stationZybList = CommonFunctionQJ.getStationZybList(sourceLineTrans);

				for(Map<String, String> stationZybMap : stationZybList){
					String zybDzName = StringUtils.ObjToString(stationZybMap.get("ZYB_DZNAME"));
					
					if(!zybDzName.equals("")){
						replaceStr += stationName+"@拉开"+zybDzName+"/r/n";
					}
				}
				
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				List<PowerDevice> linedzList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXLS+","+CBSystemConstants.RunTypeKnifeXL,"",false,true, true, true);

				if(mxlist.size()>0){
					if(mxlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")||RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
									if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
										replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
									}
								}
							}
						}
						
						for(PowerDevice dev : xlkgList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")||RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
									if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
										replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
									}
								}
							}
						}
					}else{
						if(linedzList.size() == 1 && xlkgList.size() == 0){
							for(PowerDevice dev : linedzList){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
									replaceStr += CommonFunctionQJ.getKnifeOffContent(linedzList, stationName);
								}
							}
						}else{
							for(PowerDevice dev : linedzList){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
									replaceStr += CommonFunctionQJ.getKnifeOffContent(linedzList, stationName);
								}
							}
							
							for(PowerDevice dev : xlkgList){
								if(RuleExeUtil.isDeviceHadStatus(dev, "1", "2")){
									replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
								}
							}
						}
					}
				}
			}
			
			for(PowerDevice loadLineTran : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 
				
				List<Map<String, String>> stationZybList = CommonFunctionQJ.getStationZybList(loadLineTran);

				for(Map<String, String> stationZybMap : stationZybList){
					String zybDzName = StringUtils.ObjToString(stationZybMap.get("ZYB_DZNAME"));
					
					if(!zybDzName.equals("")){
						replaceStr += stationName+"@拉开"+zybDzName+"/r/n";
					}
				}
				
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(loadLineTran,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> linedzList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXLS+","+CBSystemConstants.RunTypeKnifeXL,"",false,true, true, true);

				if(linedzList.size() == 1 && xlkgList.size() == 0){
					for(PowerDevice dev : linedzList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionQJ.getKnifeOffContent(linedzList, stationName);
						}
					}
				}else{
					for(PowerDevice dev : linedzList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionQJ.getKnifeOffContent(linedzList, stationName);
						}
					}
					
					if(xlkgList.size() == 2){
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")||RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
									if(RuleExeUtil.getDeviceEndStatus(dev).equals("2") || RuleExeUtil.getDeviceEndStatus(dev).equals("3")){
										replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
									}
								}
							}
						}

						for(PowerDevice dev : xlkgList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")||RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
									if(RuleExeUtil.getDeviceEndStatus(dev).equals("2") || RuleExeUtil.getDeviceEndStatus(dev).equals("3")){
										replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
									}
								}
							}
						}
					}else{
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")||RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("2") || RuleExeUtil.getDeviceEndStatus(dev).equals("3")){
									replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
								}
							} else if (RuleExeUtil.getDeviceBeginStatus(dev).isEmpty()) {
                                // 特殊接线，光伏电站以及风电场，存在断路器只有一侧隔离开关的情况，此时需要生成操作隔离开关的指令
                                List<PowerDevice> dzList = czprule.rule.operationclass.RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
                                if (dzList.size() == 1) {
                                    replaceStr += stationName + "@核实" + CZPService.getService().getDevName(dev) + "处断开位置/r/n";
									if (RuleExeUtil.getDeviceBeginStatus(dzList.get(0)).isEmpty()) {
										replaceStr += stationName + "@核实" + CZPService.getService().getDevName(dzList.get(0)) + "处断开位置/r/n";
									}
                                }
                            }
						}
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("3")){
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					
					if(JDKGXZQJ.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
						replaceStr += stationName+"@在"+CZPService.getService().getDevName(sourceLineTrans)+"线路侧装设一组代替线路接地开关功能的三相接地线/r/n";
					}else{
						for(PowerDevice dev : jddzList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
				}
				
				for(Map<String,String> stationLineMap : stationLineList){
					String unit = StringUtils.ObjToString(stationLineMap.get("UNIT"));
					String linename = StringUtils.ObjToString(stationLineMap.get("LINE_NAME"));
					String lowerunit = StringUtils.ObjToString(stationLineMap.get("LOWERUNIT"));
					String jddzName = StringUtils.ObjToString(stationLineMap.get("GROUNDDISCONNECTOR_NAME"));
					String endpointtype = StringUtils.ObjToString(stationLineMap.get("ENDPOINT_TYPE"));
					String switchName = StringUtils.ObjToString(stationLineMap.get("SWITCH_NAME")).trim();

					if(unit.equals("输电管理所")){
						continue;
					}

					boolean zsccdx =  false;
					
					for(PowerDevice dev : JDKGXZQJ.chooseEquips){
						if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
							zsccdx = true;
							replaceStr += unit+"@在"+lowerunit+linename+"线路侧装设一组代替线路接地开关功能的三相接地线/r/n";
							break;
						 }
					}
					
					if(!zsccdx){
						if(!jddzName.equals("")){
							replaceStr += unit+"@合上"+lowerunit+jddzName+"/r/n";
						}else{
							replaceStr += unit+"@在"+lowerunit+linename+"线路侧装设一组代替线路接地开关功能的三相接地线/r/n";
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(loadLineTran, SystemConstants.SwitchFlowGroundLine);
					
					if(JDKGXZQJ.chooseEquips.contains(loadLineTran)||jddzList.size()==0){
						replaceStr += stationName+"@在"+CZPService.getService().getDevName(loadLineTran)+"线路侧装设一组代替线路接地开关功能的三相接地线/r/n";
					}else{
						for(PowerDevice dev : jddzList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
