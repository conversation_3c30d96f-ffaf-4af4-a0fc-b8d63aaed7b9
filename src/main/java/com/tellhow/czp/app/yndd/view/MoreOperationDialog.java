package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Frame;
import java.awt.GridLayout;
import java.awt.event.ActionEvent;

import javax.swing.BorderFactory;
import javax.swing.BoxLayout;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JPanel;
import javax.swing.border.Border;

import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;


public class MoreOperationDialog extends JDialog {
	public static JComboBox comboBox1 = null;
	
	public boolean isCancel = true;
	
    public MoreOperationDialog(Frame owner, String type) {
        super(owner, true);
        this.isCancel = true;
        
        initializeUI(type);
        setLocationRelativeTo(null); // 窗口居中显示  
    }

    private void initializeUI(String type) {
        JPanel comboBoxPanel = new JPanel(new GridLayout(1, 2, 10, 0)); // 1行2列，列间距为5 
        
        if(type.equals("停电")){
        	comboBox1 = new JComboBox(new String[]{"常规操作", "配合中调操作"});  
        }else{
        	comboBox1 = new JComboBox(new String[]{"有检修申请线路的复电", "有检修申请站内设备的复电" ,"无检修申请线路的复电" ,"无检修申请站内设备的复电"
            		,"无工作陪停线路的复电","无工作陪停站内设备的复电","配合中调复电操作"});  
        }
        
        comboBoxPanel.add(comboBox1);  
        
        add(comboBoxPanel, BorderLayout.NORTH);  
        // 创建按钮面板  
        JPanel buttonPanel = new JPanel(); // 默认FlowLayout  
        JButton saveButton = new JButton("保存");  
        JButton cancelButton = new JButton("取消");  
        buttonPanel.add(saveButton);  
        buttonPanel.add(cancelButton);  
  
        // 将按钮面板添加到JFrame的SOUTH位置  
     // 创建一个EmptyBorder，上、右、下、左内边距都是10像素  
        Border emptyBorder = BorderFactory.createEmptyBorder(10, 10, 10, 10);  
        // 创建其他边框  
        Border etchedBorder = BorderFactory.createEtchedBorder();  
        Border titledBorder = BorderFactory.createTitledBorder(type+"核实");  
        Border innerBorder = BorderFactory.createCompoundBorder(emptyBorder, titledBorder);  
        Border outerBorder = BorderFactory.createCompoundBorder(etchedBorder, innerBorder);  
        
        comboBoxPanel.setBorder(outerBorder);
        
        JPanel comboBoxContainer = new JPanel();  
        comboBoxContainer.setLayout(new BoxLayout(comboBoxContainer, BoxLayout.Y_AXIS));  
        comboBoxContainer.add(comboBoxPanel);  
//            comboBoxContainer.add(Box.createVerticalStrut(20)); // 可选：添加垂直间距  
        
        add(comboBoxContainer, BorderLayout.NORTH);
        add(buttonPanel, BorderLayout.SOUTH);
        
        setSize(350, 200);  
        
        saveButton.addActionListener(new java.awt.event.ActionListener() {
		    public void actionPerformed(java.awt.event.ActionEvent evt) {
		        savejButtonActionPerformed(evt);
		    }
		});
        
        cancelButton.addActionListener(new java.awt.event.ActionListener() {
		    public void actionPerformed(java.awt.event.ActionEvent evt) {
		    	canceljButtonActionPerformed(evt);
		    }
		});
    }
    
    private void savejButtonActionPerformed(ActionEvent evt){
    	isCancel = false;
    	CommonFunctionQJ.orderContent = ""+comboBox1.getSelectedItem();
    	this.setVisible(false);
    }

	private void canceljButtonActionPerformed(ActionEvent evt){
		this.setVisible(false);
	}
}


