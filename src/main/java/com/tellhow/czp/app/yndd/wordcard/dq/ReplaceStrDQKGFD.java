package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQKGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆开关复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			String deviceNum = CZPService.getService().getDevNum(curDev);
			
			replaceStr += stationName+"@落实迪庆供电局-XXXXXX检修申请工作任务已结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），该设备的二次装置已正常投入，确认设备具备送电条件/r/n";
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				String mxName = "";
				
				if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : mxList){
						mxName = CZPService.getService().getDevName(mx);
						break;
					}
				}
				
				if(CommonFunctionDQ.ifSwitchSeparateControl(curDev)){
					replaceStr += stationName+"@落实"+deviceName+"间隔具备程序化控制条件/r/n";
					
					if(!mxName.equals("")){
						replaceStr += "迪庆地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作，"+deviceNum+"断路器在"+mxName+"上热备用/r/n";
					}else{
						replaceStr += "迪庆地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					}

					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionDQ.getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}
			}
			
			if(curDev.getDeviceStatus().equals("0")){
				replaceStr += CommonFunctionDQ.getSwitchOnContent(curDev, stationName,station);
			}
		}
		
		return replaceStr;
	}

}
