package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQDMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆单母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchZYB, "", false, true, true, true);
			List<PowerDevice> jdbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, true);
			List<PowerDevice> drkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR, "", false, true, true, true);
			List<PowerDevice> dkkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDK, "", false, true, true, true);

			if(zybkgList.size() == 0){
				List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);

				for(PowerDevice dev : qtkgList){
					if(dev.getPowerDeviceName().contains("站用变")){
						zybkgList.add(dev);
					}
				}
			}
			
			if(jdbkgList.size() == 0){
				List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);

				for(PowerDevice dev : qtkgList){
					if(dev.getPowerDeviceName().contains("接地变")){
						zybkgList.add(dev);
					}
				}
			}
			
			if(curDev.getPowerVoltGrade() < station.getPowerVoltGrade()){//负荷侧
				if(curDev.getPowerVoltGrade() == 10 && xlkgList.size() > 0){
					replaceStr += "迪庆配调@确认"+stationName+deviceName+"配调管辖10kV出线运行方式已调整完毕，具备停电条件/r/n";
				}
				
				if(station.getPowerVoltGrade() == 500){//顺控操作
					List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
					
					for(PowerDevice dev  : zybkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
						}
					}
					
					for(PowerDevice dev  : zbkgList){
						if(!dev.getPowerDeviceName().contains("备用")){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
							}
						}
					}
					
					replaceStr += stationName+"@将"+deviceName+"电压互感器由运行转冷备用/r/n";
					replaceStr += "迪庆地调@执行将"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(drkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(dkkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zybkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbkgList, stationName);
				}else if(station.getPowerVoltGrade() == 220 && stationName.equals("220kV茶城变")){//220kV茶城变特殊判断
					List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);

					for(PowerDevice dev  : zybkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
						}
					}
					
					PowerDevice zbdz = CBSystemConstants.getPowerDevice("114841790497959799");
					
					for(PowerDevice dev  : jdbkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
						}
					}
					
					for(PowerDevice dev  : zbkgList){
						if(!dev.getPowerDeviceName().contains("备用")){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
							}
						}
					}
					
					replaceStr += stationName+"@将"+deviceName+"电压互感器由运行转冷备用/r/n";
					replaceStr += "迪庆地调@执行将"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(drkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(dkkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(jdbkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zybkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbdz, stationName);

					replaceStr += stationName+"@退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}else{
					List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
					
					if(curDev.getPowerVoltGrade() == 35){
						for(PowerDevice dev  : xlkgList){
							if(!dev.getPowerDeviceName().contains("备用")){
								if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
									String status = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
									status = RuleExeUtil.getStatusNew(dev.getDeviceType(), status);
									replaceStr += "确认"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
								}
							}
						}
					}
					
					for(PowerDevice dev  : drkgList){
						replaceStr += CommonFunctionDQ.getSwitchConfirmContent(dev, stationName);
					}
					
					for(PowerDevice dev  : dkkgList){
						replaceStr += CommonFunctionDQ.getSwitchConfirmContent(dev, stationName);
					}
					
					for(PowerDevice dev  : jdbkgList){
						if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
							String status = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
							status = RuleExeUtil.getStatusNew(dev.getDeviceType(), status);
							replaceStr += "确认"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
						}
					}
					
					for(PowerDevice dev  : zybkgList){
						if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
							String status = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
							status = RuleExeUtil.getStatusNew(dev.getDeviceType(), status);
							replaceStr += "确认"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
						}
					}
					
					if(curDev.getDeviceStatus().equals("1")){
						replaceStr += stationName+"@将"+deviceName+"由运行转热备用/r/n";
					}else if(curDev.getDeviceStatus().equals("2")){
						if(curDev.getPowerVoltGrade() >= 35){
							for(PowerDevice dev  : xlkgList){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
									replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
								}
							}
						}
						
						for(PowerDevice dev  : jdbkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
							}
						}

						for(PowerDevice dev  : zybkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
							}
						}
						
						for(PowerDevice dev  : zbkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
							}
						}
						
						for(PowerDevice dev  : mlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
							}
						}
						
						replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
					}
					
					if(curDev.getPowerVoltGrade() == 10){
						replaceStr += "迪庆配调@通知"+stationName+deviceName+"已处冷备用/r/n";
					}
					
					for(PowerDevice dev : mlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += "退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
			}else if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> mldzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeML, "", true, true, true, true);

				for(Iterator<PowerDevice> itor = zbkgList.iterator();itor.hasNext();){
					PowerDevice zbkg = itor.next();
					
					if(!RuleExeUtil.isDeviceChanged(zbkg)){
						itor.remove();
					}
				}
				
				for(PowerDevice dev  : xlkgList){
					if(!dev.getPowerDeviceName().contains("备用")){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionDQ.getSwitchOffContent(dev, stationName, station);
						}
					}
				}
				
				replaceStr += stationName+"@将"+deviceName+"电压互感器由运行转冷备用/r/n";

				if(curDev.getPowerVoltGrade() >= 110){//顺控操作
					replaceStr += "迪庆地调@执行将"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(xlkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbkgList, stationName);

					if(mldzList.size() > 0){
						for(PowerDevice dev : mldzList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(dev, stationName);
							}
						}
					}else{
						replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(mlkgList, stationName);
					}
				}else{
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
				
				replaceStr += stationName+"@退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
			}
		}
		
		return replaceStr;
	}

}
