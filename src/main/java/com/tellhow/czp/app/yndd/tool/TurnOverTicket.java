package com.tellhow.czp.app.yndd.tool;



public class TurnOverTicket {
	public static String xhTurn(String xh,String max1,String max2) {//反转序号
		try {
			if(max2.equals("")){//不带点的序号，如1
				xh = (Integer.parseInt(max1)-Integer.parseInt(xh)+1)+"";
			}else{//带点的序号，如1.1
				String[] para =xh.split("\\.");
				String xh1 = para[0];
				String xh2 = para[1];
				xh1= (Integer.parseInt(max1)-Integer.parseInt(xh1)+1)+"";
				xh2= (Integer.parseInt(max2)-Integer.parseInt(xh2)+1)+"";
			}
			return xh;
		} catch (Exception e) {
			return xh;
		}
		
	}
	public static String stateTurn(String state) {//反转状态
		if(state.equals("0")){
			return "1";
		}else{
			return "0";
		}
	}
	public static String cznrTurn(String cznr) {//反转操作内容
		if(cznr.contains("断开")){
			cznr.replaceAll("断开", "合上");
		}else if(cznr.contains("拉开")){
			cznr.replaceAll("拉开", "合上");
		}else if(cznr.contains("合上")){
			if(cznr.contains("刀闸")){
				cznr.replaceAll("合上", "拉开");
			}else if(cznr.contains("开关")){
				cznr.replaceAll("合上", "断开");
			}
		}else if(cznr.contains("运行转热备用")){
			cznr.replaceAll("运行转热备用", "热备用转运行");
		}else if(cznr.contains("热备用转冷备用")){
			cznr.replaceAll("热备用转冷备用", "冷备用转热备用");
		}else if(cznr.contains("冷备用转检修")){
			cznr.replaceAll("冷备用转检修", "检修转冷备用");
		}else if(cznr.contains("检修转冷备用")){
			cznr.replaceAll("检修转冷备用", "冷备用转检修");
		}else if(cznr.contains("冷备用转热备用")){
			cznr.replaceAll("冷备用转热备用", "热备用转冷备用");
		}else if(cznr.contains("热备用转运行")){
			cznr.replaceAll("热备用转运行", "运行转热备用");
		}
		
		else if(cznr.contains("由")&&cznr.contains("转")){
			String[] str =cznr.split("由");
			String[] str1 = str[1].split("转");
			cznr = str[0]+"由"+str1[1]+"转"+str1[0];
		}
		
//		else if(cznr.contains("运行状态转热备用状态")){
//			cznr.replaceAll("运行状态转热备用状态", "热备用状态转运行状态");
//		}else if(cznr.contains("热备用状态转冷备用状态")){
//			cznr.replaceAll("热备用状态转冷备用状态", "冷备用状态转热备用状态");
//		}else if(cznr.contains("冷备用状态转检修状态")){
//			cznr.replaceAll("冷备用状态转检修状态", "检修状态转冷备用状态");
//		}else if(cznr.contains("检修状态转冷备用状态")){
//			cznr.replaceAll("检修状态转冷备用状态", "冷备用状态转检修状态");
//		}else if(cznr.contains("冷备用状态转热备用状态")){
//			cznr.replaceAll("冷备用状态转热备用状态", "热备用状态转冷备用状态");
//		}else if(cznr.contains("热备用状态转运行状态")){
//			cznr.replaceAll("热备用状态转运行状态", "运行状态转热备用状态");
//		}
		
		return cznr;
	}
}
