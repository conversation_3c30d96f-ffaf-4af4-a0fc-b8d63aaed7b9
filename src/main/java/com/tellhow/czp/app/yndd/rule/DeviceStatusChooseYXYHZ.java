package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.view.EquipStatusChooseYHZYX;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 关联设备目标状态选择器
 * 作    者: 郑柯
 * 开发日期: 2013年8月17日 上午10:13:38 
 */
public class DeviceStatusChooseYXYHZ implements RulebaseInf {
	public static Map<PowerDevice,Map<String,String>> tagStatusMap= new HashMap<PowerDevice, Map<String,String>>();
	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(!CBSystemConstants.isCurrentSys)
			return true;
		if(CBSystemConstants.jh_tai == 1)
			return true;
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return true;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return true;
		if(!rbm.getPd().equals(pd))
			return true;
		
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();  //执行开关集合
		
		String sql1 = "SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND WHERE ID = '"+pd.getPowerDeviceID()+"'";
		List<Map<String,String>> list1 = DBManager.queryForList(sql1);
		
		if(list1.size()>0){
			String sql2 = "SELECT ID,BREAKER_NUMBER,VOLTAGE_LEVEL,ORDERED_UNIT,IS_CIRCUIT_BREAKER,OPERATE_ISOKNIFE,ISO_KNIFE_NUM FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID = '"+StringUtils.ObjToString(list1.get(0).get("ACLINE_ID"))+"'";
			List<Map<String,String>> list2 = DBManager.queryForList(sql2);

			if(list2.size()>0){
				for(Map<String,String> map : list2){
					String orderedunit = StringUtils.ObjToString(map.get("ORDERED_UNIT"));
					String breakernumber = StringUtils.ObjToString(map.get("BREAKER_NUMBER"));
					String id = StringUtils.ObjToString(map.get("ID"));

					PowerDevice dev = new PowerDevice();
					dev.setPowerDeviceID(id);
					dev.setDeviceType(SystemConstants.Switch);
					dev.setPowerDeviceName(CZPService.getService().getDevName(pd)+breakernumber+"断路器");
					dev.setDeviceStatus(curRBM.getBeginStatus());
					dev.setPowerStationName(orderedunit);
					switchs.add(dev);
					
				}
			}
		}
		
		if(switchs.size()==0){
			return true;
		}
		
		tagStatusMap.clear();
		
		String showMessage="请选择设备的目标状态";
	
		String defaultStatus = CBSystemConstants.getCurRBM().getEndState();
		List<String> defaultStatusList = new ArrayList<String>();
		
		for(int i = 0 ;i<switchs.size();i++){
			defaultStatusList.add(defaultStatus);
		}

		EquipStatusChooseYHZYX dialog = new EquipStatusChooseYHZYX(SystemConstants.getMainFrame(), true, switchs, defaultStatusList, showMessage);
		tagStatusMap=dialog.getTagStatusMap();
		
		if(tagStatusMap.size() == 0)
			return false;

		return true;
	}
	

}
