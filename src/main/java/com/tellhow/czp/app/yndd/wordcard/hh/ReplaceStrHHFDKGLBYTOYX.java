package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHFDKGLBYTOYX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河分段开关由冷备用转运行".equals(tempStr)){
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",false, true, true, false);
			List<PowerDevice> kgdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());

			String stationName = CZPService.getService().getDevName(station);
			
			for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
				PowerDevice dev = (PowerDevice)it2.next();
				if(kgdzList.contains(dev))
					it2.remove();
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
						jdzybkgList.add(dev);
					}
				}
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					mlkgList.add(dev);
				}
			}
			
			tempList.add(curDev);
			
			for(PowerDevice qtdz : qtdzList){
				tempList.add(qtdz);
				break;
			}
			
			replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"冷备用/r/n";
			
			tempList.clear();
			
			for(PowerDevice dev : mlkgList){
				if(station.getPowerVoltGrade() == dev.getPowerVoltGrade()||dev.getPowerDeviceID()!=curDev.getPowerDeviceID()){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						tempList.add(dev);
					}
				}
			}
			
			if(tempList.size()>0){
				replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"热备用/r/n";
			}
			
			replaceStr += "投入"+CZPService.getService().getDevName(zbList)+(int)curDev.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(curDev)+"/r/n";
			
			if(jdzybkgList.size()>0&&curDev.getPowerVoltGrade() == 10){
				String num = "";
				
				for(PowerDevice jdzybkg : jdzybkgList){
					String jdbName = jdzybkg.getPowerDeviceName().substring(0, jdzybkg.getPowerDeviceName().indexOf("接地"));
					
					num += CZPService.getService().getDevNum(jdbName)+"、";
				}
				
				if(num.endsWith("、")){
					num = num.substring(0, num.length()-1);
				}
				
				replaceStr += "投入10kV"+num+"接地变保护动作跳"+CZPService.getService().getDevName(curDev)+"/r/n";
			}
			
			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
			
			for(PowerDevice qtdz : qtdzList){
				replaceStr += "核实"+CZPService.getService().getDevName(qtdz)+"在合闸位置/r/n";
				break;
			}

			if(station.getPowerVoltGrade() < 220){
				List<PowerDevice> otherMlkgList = new ArrayList<PowerDevice>();
				
				for(PowerDevice mlkg : mlkgList){
					if(station.getPowerVoltGrade() > mlkg.getPowerVoltGrade() && mlkg.getPowerDeviceID()!=curDev.getPowerDeviceID()){
						otherMlkgList.add(mlkg);
					}
				}
				tempList.clear();
				
				tempList.addAll(otherMlkgList);
				tempList.add(curDev);
				
				for(PowerDevice otherMlkg : otherMlkgList){
					if(RuleExeUtil.getDeviceEndStatus(otherMlkg).equals("0")){
						replaceStr += "退出"+(int)otherMlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
						replaceStr += "投入"+CZPService.getService().getDevName(zbList)+(int)otherMlkg.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(otherMlkg)+"/r/n";
					}
				}
			}
			
			if(station.getPowerVoltGrade() == 220){
				replaceStr += "核实主变中性点切换装置功能已按现场规程调整好/r/n";
			}
			
			replaceStr += CommonFunctionHH.getZbBLTQStrReplace(zbList.get(0));
			
			if(station.getPowerVoltGrade() < 220){
				tempList.clear();
				
				for(PowerDevice dev : mlkgList){
					if(dev.getPowerVoltGrade()<station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							tempList.add(dev);
						}
					}
				}
				
				RuleExeUtil.swapDeviceList(tempList);
				
				if(tempList.size()>0){
					replaceStr += CommonFunctionHH.getYcHsStrReplace(tempList, stationName);
					replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"运行正常/r/n";
				}
			}
			
			if(station.getPowerVoltGrade() == 220){
				if(curDev.getPowerVoltGrade() == 220){
					tempList.clear();
					tempList.add(curDev);
					
					for(PowerDevice dev : mlkgList){
						if(dev.getPowerVoltGrade() == 110){
							replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"110kV侧后备保护动作跳"+CZPService.getService().getDevName(dev)+"/r/n";
							tempList.add(dev);
						}
					}
					
					if(tempList.size()>0){
						replaceStr += CommonFunctionHH.getYcHsStrReplace(tempList, stationName);
						replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"运行正常/r/n";
					}
				}else{
					tempList.clear();
					tempList.add(curDev);
					replaceStr += CommonFunctionHH.getYcHsStrReplace(tempList, stationName);
					replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"运行正常/r/n";
				}
				
				for(PowerDevice dev : zbList){
					List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

					for(PowerDevice gd : gdList){
						if(gd.getDeviceStatus().equals("1")){
							replaceStr += "退出"+CZPService.getService().getDevName(dev)+"高、中压侧中性点及其零序保护/r/n";
							break;
						}
					}
				}
			}
			
			if(jdzybkgList.size()>0){
				replaceStr += "退出10kV#X接地变小电阻自投切功能/r/n";
			}
		}
		return replaceStr;
	}

}