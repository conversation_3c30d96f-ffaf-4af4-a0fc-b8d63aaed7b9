package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrYXZBTDDDZBMC implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪主变停电倒电主变名称".equals(tempStr)){
			List<PowerDevice> list = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "", true, false, false);
			
			if(list.size()>0){
				replaceStr = CZPService.getService().getDevName(list.get(0));
			}
	    }
		return replaceStr;
	}

}
