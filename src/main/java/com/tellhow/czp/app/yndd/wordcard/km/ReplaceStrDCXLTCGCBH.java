package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrDCXLTCGCBH implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("对侧线路退出光差保护".equals(tempStr)){
			List<PowerDevice> lineList =RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, "", true, true, true);
			
			if(lineList.size()>0){
				List<PowerDevice> list = RuleExeUtil.getLineOtherSideList(lineList.get(0));
				
				if(list.size()>0){
					for(PowerDevice dev : list){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName=CZPService.getService().getDevName(station);
						replaceStr += stationName+"@退出"+CZPService.getService().getDevName(lineList)+"线路光差保护/r/n";
					}
				}
				
				String sql = "SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND WHERE ID = '"+lineList.get(0).getPowerDeviceID()+"'";
				List<Map<String,String>> lineidlist = DBManager.queryForList(sql);
				
				if(lineidlist.size()>0){
					String sql1 = "SELECT UNIT,LOWERUNIT FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER T WHERE LINE_ID = '"+lineidlist.get(0).get("ACLINE_ID")+"'";
					List<Map<String,String>> unitlist = DBManager.queryForList(sql1);
					
					if(unitlist.size()>0){
						for(Map<String,String> map : unitlist){
							String unit = StringUtils.ObjToString(map.get("UNIT"));
							String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));

							if(!lowerunit.equals("")&&!unit.equals("")){
								replaceStr += unit+"@许可退出"+lowerunit+CZPService.getService().getDevName(lineList)+"线路光差保护/r/n";
							}else if(lowerunit.equals("")&&!unit.equals("")){
								replaceStr += unit+"@退出"+CZPService.getService().getDevName(lineList)+"线路光差保护/r/n";
							}else if(!lowerunit.equals("")&&unit.equals("")){
								replaceStr += lowerunit+"@退出"+CZPService.getService().getDevName(lineList)+"线路光差保护/r/n";
							}
						}
					}
				}else{
					ShowMessage.view("没有找到对侧厂站！");
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		
		return replaceStr;
	}
	
}
