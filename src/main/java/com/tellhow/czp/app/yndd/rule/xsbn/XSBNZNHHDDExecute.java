package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;

public class XSBNZNHHDDExecute implements RulebaseInf {
	public static List<PowerDevice> powerDeviceList = new ArrayList<PowerDevice>();//供电线路
	public static List<PowerDevice> powercutDeviceList = new ArrayList<PowerDevice>();//需要断开的线路
	
	public boolean execute(RuleBaseMode rbm) {
		PowerDevice pd=rbm.getPd();

		if(pd == null){
			return false;
		}
		
		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
		
		if(pd.getPowerVoltGrade() == station.getPowerVoltGrade()){
			powerDeviceList.clear();
			powercutDeviceList.clear();
			
			List<PowerDevice> powerxlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> yxxlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
					if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
						xlkgList.add(dev);
					}
				}
			}
			
			for(Iterator<PowerDevice> itor = xlkgList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(dev.getDeviceStatus().equals("0")){
					yxxlkgList.add(dev);
					powerxlkgList.add(dev);
				}else if(dev.getDeviceStatus().equals("1")){
					powerxlkgList.add(dev);
				}
			}
			
			List<PowerDevice>  selectDevList = new ArrayList<PowerDevice>();
			
			for(Iterator<PowerDevice> itor = yxxlkgList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				List<ArrayList<PowerDevice>>  pathList =  RuleExeUtil.getPathAllByDevice(pd, dev, "", "", false, false);
				
				if(pathList == null){
					itor.remove();
				}
			}
			
			if(yxxlkgList.size()>1){
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, yxxlkgList , "请选择操作前供电的断路器：");
				List<PowerDevice> chooseList = ecc.getChooseEquip();

				if(ecc.isCancel()){
					return false;
				}
				
				for(PowerDevice dev : chooseList){
					selectDevList.add(dev);
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,false, true, true);
					
					for(PowerDevice line : lineList){
						powercutDeviceList.add(line);
					}
					
					if(mlkgList.size() > 0){
						for(PowerDevice mlkg : mlkgList){
							if(mlkg.getDeviceStatus().equals("0")){
								RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "1");
							}else{
								RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
							}
						}
					}else{
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
			}else if(yxxlkgList.size()==1){
				for(PowerDevice dev : yxxlkgList){
					selectDevList.add(dev);
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,false, true, true);
					
					for(PowerDevice line : lineList){
						powercutDeviceList.add(line);
					}
				}
			}
			
			for(Iterator<PowerDevice> itor = powerxlkgList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(selectDevList.contains(dev)){
					itor.remove();
				}
			}
			
			
			if(powerxlkgList.size()>1){
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, powerxlkgList , "请选择转由供电的断路器：");
				List<PowerDevice> chooseList = ecc.getChooseEquip();

				if(ecc.isCancel()){
					return false;
				}
				
				for(PowerDevice dev : chooseList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,false, true, true);
					
					for(PowerDevice line : lineList){
						powerDeviceList.add(line);
					}
					
					List<ArrayList<PowerDevice>> pathList =  RuleExeUtil.getPathAllByDevice(pd, dev, SystemConstants.PowerTransformer, "", true, false);
					
					if(pathList!=null){
						for(List<PowerDevice> paths : pathList){
							for(PowerDevice path : paths){
								if(path.getDeviceStatus().equals("1")&&path.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									RuleExeUtil.deviceStatusExecute(path, path.getDeviceStatus(), "0");
								}
							}
						}
					}
				}
			}else if(powerxlkgList.size()==1){
				for(PowerDevice dev : powerxlkgList){
					List<ArrayList<PowerDevice>> pathList =  RuleExeUtil.getPathAllByDevice(pd, dev, SystemConstants.PowerTransformer, "", true, false);
					
					if(pathList!=null){
						for(List<PowerDevice> paths : pathList){
							for(PowerDevice path : paths){
								if(path.getDeviceStatus().equals("1")&&path.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									RuleExeUtil.deviceStatusExecute(path, path.getDeviceStatus(), "0");
								}
							}
						}
					}
					
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,false, true, true);
					
					for(PowerDevice line : lineList){
						powerDeviceList.add(line);
					}
				}
			}
		}else{
			List<PowerDevice> yxzbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbyzbkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, false, true);

			for(PowerDevice dev : zbkgList){
				if(dev.getDeviceStatus().equals("0")){
					yxzbkgList.add(dev);
				}else if(dev.getDeviceStatus().equals("1")){
					rbyzbkgList.add(dev);
				}
			}
			
			if(rbyzbkgList.size()>1){
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, rbyzbkgList , "请选择转由供电的断路器：");
				List<PowerDevice> chooseList = ecc.getChooseEquip();

				if(ecc.isCancel()){
					return false;
				}
				
				for(PowerDevice dev : chooseList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,false, true, true);
					
					for(PowerDevice zb : zbList){
						powerDeviceList.add(zb);
					}
				}
			}else if(yxzbkgList.size()==1){
				for(PowerDevice dev : rbyzbkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,false, true, true);
					
					for(PowerDevice zb : zbList){
						powerDeviceList.add(zb);
					}
				}
			}
			
			if(yxzbkgList.size()>1){
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, yxzbkgList , "请选择操作前供电的断路器：");
				List<PowerDevice> chooseList = ecc.getChooseEquip();

				if(ecc.isCancel()){
					return false;
				}
				
				for(PowerDevice dev : chooseList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,false, true, true);
					
					for(PowerDevice zb : zbList){
						powercutDeviceList.add(zb);
					}
				}
			}else if(yxzbkgList.size()==1){
				for(PowerDevice dev : yxzbkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,false, true, true);
					
					for(PowerDevice zb : zbList){
						powercutDeviceList.add(zb);
					}
				}
			}
			
		}
		
		return true;
	}
}
