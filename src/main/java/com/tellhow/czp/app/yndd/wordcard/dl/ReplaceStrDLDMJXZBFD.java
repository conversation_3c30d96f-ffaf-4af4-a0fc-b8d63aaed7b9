package com.tellhow.czp.app.yndd.wordcard.dl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDL;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDLDMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("大理单母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();

			boolean isSwitchControl = true;
			
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionDL.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionDL.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			
			for(PowerDevice dev : zbgyckgList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			}

			
			
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList .add(dev);
					}
				}
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						gycxlkgList.add(dev);
					}
				}
			}
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				otherzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				break;
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				if(dycmlkgList.size() == 0){
					for(PowerDevice dev : dycmxList){
						if(dev.getPowerVoltGrade() == 10){
							for(PowerDevice zbdyckg : zbdyckgList){
								if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("0")){
									replaceStr += "大理配调@核实"+stationName+CZPService.getService().getDevName(dev)+"具备送电条件/r/n";
								}
							}
						}
					}
				}
				
				replaceStr += "大理地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						replaceStr += "按当前运行方式投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				if(dycmlkgList.size() == 0){
					for(PowerDevice dev : dycmxList){
						if(dev.getPowerVoltGrade() == 10){
							for(PowerDevice zbdyckg : zbdyckgList){
								if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("0")){
									replaceStr += "大理配调@通知"+stationName+deviceName+"已处运行/r/n";
								}
							}
						}
					}
				}
			}else{
				if(station.getPowerVoltGrade() == 35){
					boolean isSeparateControl = false;
					
					for(PowerDevice dev : kgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(!dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
								if(CommonFunctionDL.ifSwitchSeparateControl(dz)){
									isSeparateControl = true;
								}
							}
						}
					}
					
					if(isSeparateControl){
						boolean isKnifeXC = false;
						boolean isKnifeFB = false;

						for(PowerDevice dev : zbgyckgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(dzList.size() == 1){
								isKnifeFB = true;
								break;
							}else{
								for(PowerDevice dz : dzList){
									if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
										isKnifeXC = true;
										break;
									}
								}
							}
						}
						
						if(isKnifeFB){
							for(PowerDevice dev : zbgyckgList){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionDL.getKnifeOnContent(dzList, stationName);
							}
						}else if(isKnifeXC){
							for(PowerDevice dev : zbgyckgList){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
							}
						}else{
							for(PowerDevice dev : zbgyckgList){
								replaceStr += "大理地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
								
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionDL.getKnifeOffCheckContent(dzList, stationName);
							}
						}
						
						isKnifeXC = false;
						isKnifeFB = false;

						for(PowerDevice dev : zbdyckgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(dzList.size() == 1){
								isKnifeFB = true;
								break;
							}else{
								for(PowerDevice dz : dzList){
									if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
										isKnifeXC = true;
										break;
									}
								}
							}
						}
						
						if(isKnifeFB){
							for(PowerDevice dev : zbdyckgList){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionDL.getKnifeOnContent(dzList, stationName);
							}
						}else if(isKnifeXC){
							for(PowerDevice dev : zbdyckgList){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
							}
						}else{
							for(PowerDevice dev : zbdyckgList){
								replaceStr += "大理地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
								
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionDL.getKnifeOffCheckContent(dzList, stationName);
							}
						}
					}else{
						if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
							replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
						}
					}
					
					if(curDev.getPowerStationID().equals("SS-185")&&curDev.getPowerDeviceID().equals("19488")){
						for(PowerDevice dev : gycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
								replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
							}
						}
						
						for(PowerDevice dev : gycxlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							String devName = CZPService.getService().getDevName(dev);
							replaceStr += "大理地调@遥控合上"+stationName+devName+"对"+deviceName+"充电/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(curDev.getPowerStationID().equals("SS-185")&&curDev.getPowerDeviceID().equals("19488")){
						for(PowerDevice dev : gycxlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
							}
						}
						
						for(PowerDevice dev : gycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
					

					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "按当前运行方式投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}else{
					if(zxdjddzList.size() > 0){
						replaceStr += CommonFunctionDL.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
						}
					}
					
					for(PowerDevice dev : gycxlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						String devName = CZPService.getService().getDevName(dev);

						if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
							replaceStr += "大理地调@执行"+stationName+devName+"由冷备用转热备用程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+devName+"由冷备用转热备用/r/n";
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						List<PowerDevice> zycdzList = CommonFunctionDL.getTransformerKnife(curDev, dev);
						replaceStr += CommonFunctionDL.getKnifeOnContent(zycdzList,stationName);
						
						String devName = CZPService.getService().getDevName(dev);

						if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
							replaceStr += "大理地调@执行"+stationName+devName+"由冷备用转热备用程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+devName+"由冷备用转热备用/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						List<PowerDevice> dycdzList = CommonFunctionDL.getTransformerKnife(curDev, dev);
						replaceStr += CommonFunctionDL.getKnifeOnContent(dycdzList,stationName);
						
						String devName = CZPService.getService().getDevName(dev);

						if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
							replaceStr += "大理地调@执行"+stationName+devName+"由冷备用转热备用程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							for (Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
								PowerDevice dz = it.next();
								
								if(dz.getPowerDeviceName().endsWith("1")){
									it.remove();
								}
							}
							
							replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+devName+"由冷备用转热备用/r/n";
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							String devName = CZPService.getService().getDevName(dev);
							replaceStr += "大理地调@遥控合上"+stationName+devName+"对"+deviceName+"充电/r/n";
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
						}
					}

					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
						}
					}

					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : gycxlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(zxdjddzList.size() > 0){					
						replaceStr += CommonFunctionDL.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
					}
					
					if(otherzxdjddzList.size() > 0){
						replaceStr += CommonFunctionDL.getZxdJddzOffCheckContent(otherzxdjddzList, stationName, station);
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
