package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionCX;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXDMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄单母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();

			boolean isSwitchControl = true;
			
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionCX.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionCX.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			
			for(PowerDevice dev : zbgyckgList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			}
			
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						gycxlkgList.add(dev);
					}
				}
			}
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				otherzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(otherzxdjddzList);
				break;
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			if(dycmlkgList.size() == 0){
				for(PowerDevice dev : dycmxList){
					if(dev.getPowerVoltGrade() == 10){
						replaceStr += "楚雄配调@落实"+stationName+CZPService.getService().getDevName(dev)+"已转供/r/n";
					}
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				replaceStr += CommonFunctionCX.getCheckSwitchPosition(dev, stationName);
			}
			
			for(PowerDevice dev : zbzyckgList){
				replaceStr += CommonFunctionCX.getCheckSwitchPosition(dev, stationName);
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				
			}else{
				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunctionCX.getZxdJddzOnCheckContent(zxdjddzList, stationName);
				}
				
				if(otherzxdjddzList.size() > 0){
					replaceStr += CommonFunctionCX.getZxdJddzOnCheckContent(otherzxdjddzList, stationName);
				}
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置。/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionCX.getHhContent(dev, "楚雄地调", stationName);
					}
				}
				
				replaceStr += CommonFunctionCX.getCheckBeforeContent(deviceName, stationName, "");
				replaceStr += "楚雄地调@执行"+stationName+deviceName+"由运行转冷备用程序操作。/r/n";
				
				for(PowerDevice dev : zbdyckgList){
					replaceStr += stationName+"@检查"+CZPService.getService().getDevName(dev)+"在分闸位置。/r/n";
				}
				
				for(PowerDevice dev : zbgyckgList){
					replaceStr += stationName+"@检查"+CZPService.getService().getDevName(dev)+"在分闸位置。/r/n";
				}
				
				for(PowerDevice dev : zbdyckgList){
					replaceStr += CommonFunctionCX.getKnifeOffCheckContent(dev);
				}
				
				for(PowerDevice dev : zbgyckgList){
					replaceStr += CommonFunctionCX.getKnifeOffCheckContent(dev);
				}
			}else{
				if(station.getPowerVoltGrade() == 35){
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置。/r/n";
						}
					}
					
					boolean isSeparateControl = false;
					
					for(PowerDevice dev : kgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(!dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
								if(CommonFunctionCX.ifSwitchSeparateControl(dz)){
									isSeparateControl = true;
								}
							}
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionCX.getHhContent(dev, "楚雄地调", stationName);
						}
					}

					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
						}
					}
					
					if(isSeparateControl){
						boolean isKnifeXC = false;
						boolean isKnifeFB = false;

						for(PowerDevice dev : zbdyckgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(dzList.size() == 1){
								isKnifeFB = true;
								break;
							}else{
								for(PowerDevice dz : dzList){
									if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
										isKnifeXC = true;
										break;
									}
								}
							}
						}
						
						if(isKnifeFB){
							for(PowerDevice dev : zbdyckgList){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionCX.getKnifeOffContent(dzList, stationName);
							}
						}else if(isKnifeXC){
							for(PowerDevice dev : zbdyckgList){
								replaceStr += stationName+"@"+CZPService.getService().getDevName(dev)+"由热备用转冷备用。/r/n";
							}
						}else{
							for(PowerDevice dev : zbdyckgList){
								replaceStr += "楚雄地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作。/r/n";
								replaceStr += CommonFunctionCX.getKnifeOffCheckContent(dev);
							}
						}
						

						isKnifeXC = false;
						isKnifeFB = false;

						for(PowerDevice dev : zbgyckgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(dzList.size() == 1){
								isKnifeFB = true;
								break;
							}else{
								for(PowerDevice dz : dzList){
									if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
										isKnifeXC = true;
										break;
									}
								}
							}
						}
						
						if(isKnifeFB){
							for(PowerDevice dev : zbgyckgList){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionCX.getKnifeOffContent(dzList, stationName);
							}
						}else if(isKnifeXC){
							for(PowerDevice dev : zbgyckgList){
								replaceStr += stationName+"@"+CZPService.getService().getDevName(dev)+"由热备用转冷备用。/r/n";
							}
						}else{
							for(PowerDevice dev : zbgyckgList){
								replaceStr += "楚雄地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作。/r/n";
								replaceStr += CommonFunctionCX.getKnifeOffCheckContent(dev);
							}
						}
					}else{
						if(curDev.getDeviceStatus().equals("2")){
							replaceStr += stationName+"@"+deviceName+"由热备用转冷备用。/r/n";
						}
					}
				}else{
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionCX.getHhContent(dev, "楚雄地调", stationName);
						}
					}
					
					for(PowerDevice gycxlkg : gycxlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("0")){
							replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(gycxlkg)+"。/r/n";
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionCX.getHhContent(dev, "楚雄地调", stationName);
						}
					}

					for(PowerDevice zbdyckg : zbdyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
							replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"。/r/n";
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionCX.getHhContent(dev, "楚雄地调", stationName);
						}
					}

					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
						}
					}
					
					for(PowerDevice dev : gycxlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionCX.getHhContent(dev, "楚雄地调", stationName);
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
						}
					}
					
					if(curDev.getDeviceStatus().equals("2")){
						for(PowerDevice dev : zbdyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunctionCX.ifSwitchSeparateControl(dev)){
									replaceStr += "楚雄地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作。/r/n";
									replaceStr += CommonFunctionCX.getKnifeOffCheckContent(dev);
								}else{
									replaceStr += stationName+"@"+CZPService.getService().getDevName(dev)+"由热备用转冷备用。/r/n";
								}
								
								List<PowerDevice> dzList = CommonFunctionCX.getTransformerKnife(curDev, dev);
								
								for (Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
									PowerDevice dz = it.next();
									
									if(dz.getPowerDeviceName().endsWith("1")){
										it.remove();
									}
								}
								
								replaceStr += CommonFunctionCX.getKnifeOffContent(dzList,stationName);
							}
						}
						
						for(PowerDevice dev : zbzyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunctionCX.ifSwitchSeparateControl(dev)){
									replaceStr += "楚雄地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作。/r/n";
									replaceStr += CommonFunctionCX.getKnifeOffCheckContent(dev);
								}else{
									replaceStr += stationName+"@"+CZPService.getService().getDevName(dev)+"由热备用转冷备用。/r/n";
								}
								
								List<PowerDevice> dzList = CommonFunctionCX.getTransformerKnife(curDev, dev);
								replaceStr += CommonFunctionCX.getKnifeOffContent(dzList,stationName);
							}
						}
						
						for(PowerDevice dev : zbgyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunctionCX.ifSwitchSeparateControl(dev)){
									replaceStr += "楚雄地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作。/r/n";
									replaceStr += CommonFunctionCX.getKnifeOffCheckContent(dev);
								}else{
									replaceStr += stationName+"@"+CZPService.getService().getDevName(dev)+"由热备用转冷备用。/r/n";
								}
							}
						}
					}
				}
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				
			}else{
				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunctionCX.getZxdJddzOffCheckContent(zxdjddzList, stationName);
				}
			}

			if(curDev.getPowerVoltGrade() > 35){
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置。/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置。/r/n";
					}
				}
			}
			
			 replaceStr += "楚雄地调@"+stationName+deviceName+"停电设备挂牌。/r/n";
		}
		
		return replaceStr;
	}

}
