package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSZBFHCMXHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山主变负荷侧母线合环倒电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						if(!curDev.getPowerDeviceID().equals(dev.getPowerDeviceID())){
							otherzbList.add(dev);
							break;
						}
					}
				}
			}
			
			
			replaceStr += "文山地调@落实相关断面潮流满足合环倒电要求/r/n";
			
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> zycmxList = new ArrayList<PowerDevice>();
			
			List<String> voltList = new ArrayList<String>();
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
				List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
				
				for(PowerDevice zbzyckg : zbzyckgList){
					zycmxList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					
					String zbzyckgName = CZPService.getService().getDevName(zbzyckg); 
					
					if(RuleExeUtil.getDeviceBeginStatus(zbzyckg).equals("1")){
						replaceStr += "文山地调@遥控用"+stationName+zbzyckgName+"合环/r/n";
						replaceStr += "文山地调@检查"+stationName+zbzyckgName+"三相潮流指示正常/r/n";
						voltList.add((int)zbzyckg.getPowerVoltGrade()+"kV");
					}
				}
				
				for(PowerDevice zbdyckg : zbdyckgList){
					dycmxList = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					
					String zbdyckgName = CZPService.getService().getDevName(zbdyckg); 
					
					if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("1")){
						replaceStr += "文山地调@遥控用"+stationName+zbdyckgName+"合环/r/n";
						replaceStr += "文山地调@检查"+stationName+zbdyckgName+"三相潮流指示正常/r/n";
						voltList.add((int)zbdyckg.getPowerVoltGrade()+"kV");
					}
				}
			}
			
			for(PowerDevice dev : dycmxList){
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true);
				
				if(mlkgList.size() > 0){
					for(PowerDevice mlkg : mlkgList){
						String mlkgName = CZPService.getService().getDevName(mlkg); 

						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
							replaceStr += "文山地调@遥控断开"+stationName+mlkgName+"/r/n";
						}
					}
				}else{
					List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC,"", false, true, true, true);
					
					for(PowerDevice zbkg : zbkgList){
						String zbkgName = CZPService.getService().getDevName(zbkg); 

						if(RuleExeUtil.getDeviceEndStatus(zbkg).equals("1")){
							replaceStr += "文山地调@遥控断开"+stationName+zbkgName+"/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : zycmxList){
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true);
				
				if(mlkgList.size() > 0){
					for(PowerDevice mlkg : mlkgList){
						String mlkgName = CZPService.getService().getDevName(mlkg); 

						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
							replaceStr += "文山地调@遥控断开"+stationName+mlkgName+"/r/n";
						}
					}
				}else{
					List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC,"", false, true, true, true);
					
					for(PowerDevice zbkg : zbkgList){
						String zbkgName = CZPService.getService().getDevName(zbkg); 

						if(RuleExeUtil.getDeviceEndStatus(zbkg).equals("1")){
							replaceStr += "文山地调@遥控断开"+stationName+zbkgName+"/r/n";
						}
					}
				}
			}
			
			for(String volt : voltList){
				replaceStr += stationName+"@确认"+volt+"备自投装置可靠投入/r/n";
			}
			
		}
		
		return replaceStr;
	}

}
