package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

public class SwitchExecuteGZ implements RulebaseInf {
	
	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		String devRunType = rbm.getDeviceruntype();
		
		
		//一、搜索设备连接的开关
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();  //执行开关集合
		List<PowerDevice> tempswitchs=new ArrayList<PowerDevice>();  //开关集合
		PowerDevice tempDev=null;
		if(!"".equals(rbm.getTranType())){
			 PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
			 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
			 if(sourceLineTrans==null||loadLineTrans==null){
				 ShowMessage.view("请先设置线路两端变电站属性！");
				 return false;
			 }
			 if("S".equals(rbm.getTranType())){
				 if(CBSystemConstants.LineTagStatus.containsKey(sourceLineTrans)) {
				 		String tagStatus = CBSystemConstants.LineTagStatus.get(sourceLineTrans);
				 		if(Integer.valueOf(tagStatus) < Integer.valueOf(CBSystemConstants.getDeviceStateValue(rbm.getEndState())))
					 		return true;
				 	}
				 inPara.put("oprSrcDevice", sourceLineTrans);
	             inPara.put("tagDevType", SystemConstants.Switch+","+SystemConstants.ElecShock);
	             inPara.put("excDevType", SystemConstants.PowerTransformer);
	             cs.execute(inPara, outPara);
	    	 	 inPara.clear();
	    	 	 tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
	    	 	//20131103
	    		if(tempswitchs.size() == 0) {
	    			inPara.put("oprSrcDevice", sourceLineTrans);
					inPara.put("tagDevType", SystemConstants.Switch+","+SystemConstants.ElecShock);
		            inPara.put("excDevType", SystemConstants.PowerTransformer);
	    			inPara.put("isStopOnBusbarSection", false);
	    			inPara.put("isStopOnTagDevType", true);
	    			cs.execute(inPara, outPara);
	    			tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
	    			if(rbm.getEndState().equals("0")) {
		    			for (Iterator<PowerDevice> itr = tempswitchs.iterator(); itr.hasNext();) {
		    				PowerDevice dev = itr.next();
		    				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) &&
		    					!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
		    					itr.remove();
		    			}
	    			}
	    			inPara.clear();
	    		}
	    	  	 for (int i = 0; i < tempswitchs.size(); i++) {
	    	 		tempDev=(PowerDevice)tempswitchs.get(i);
	    	 		if (devRunType.equals("") || !tempDev.getDeviceType().equals(SystemConstants.Switch)) {
						switchs.add(tempDev);
					} else {
						if (tempDev.getDeviceRunType().equals(devRunType))
							switchs.add(tempDev);
					}
				 }
			 }else{
				 for (int i = 0; i < loadLineTrans.size(); i++) {
					
					 if(CBSystemConstants.LineTagStatus.containsKey(loadLineTrans.get(i))) {
					 		String tagStatus = CBSystemConstants.LineTagStatus.get(loadLineTrans.get(i));
					 		if(Integer.valueOf(tagStatus) < Integer.valueOf(CBSystemConstants.getDeviceStateValue(rbm.getEndState())))
						 		return true;
					 	}
					inPara.put("oprSrcDevice", loadLineTrans.get(i));
					inPara.put("tagDevType", SystemConstants.Switch+","+SystemConstants.ElecShock);
		            inPara.put("excDevType", SystemConstants.PowerTransformer);
		            cs.execute(inPara, outPara);
		    		inPara.clear();
		    		tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
		    		//20131103
		    		if(tempswitchs.size() == 0) {
		    			inPara.put("oprSrcDevice", loadLineTrans.get(i));
						inPara.put("tagDevType", SystemConstants.Switch+","+SystemConstants.ElecShock);
			            inPara.put("excDevType", SystemConstants.PowerTransformer);
		    			inPara.put("isStopOnBusbarSection", false);
		    			inPara.put("isStopOnTagDevType", true);
		    			cs.execute(inPara, outPara);
		    			tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
		    			if(rbm.getEndState().equals("0")) {
			    			for (Iterator<PowerDevice> itr = tempswitchs.iterator(); itr.hasNext();) {
			    				PowerDevice dev = itr.next();
			    				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) &&
			    					!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
			    					itr.remove();
			    			}
		    			}
		    			inPara.clear();
		    		}
		    		for (int j = 0; j < tempswitchs.size(); j++) {
		    			tempDev=(PowerDevice)tempswitchs.get(j);
		    	 		if (devRunType.equals("") || !tempDev.getDeviceType().equals(SystemConstants.Switch)) {
							switchs.add(tempDev);
						} else {
							if (tempDev.getDeviceRunType().equals(devRunType))
								switchs.add(tempDev);
						}
					}
				}
			 }
		}else{
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.Switch);
            inPara.put("excDevType", SystemConstants.PowerTransformer);
            
            if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
            	devRunType = CBSystemConstants.RunTypeSwitchPL;
            
            cs.execute(inPara, outPara);
            tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
   	  	    for (int i = 0; i < tempswitchs.size(); i++) {
	   	 		tempDev=(PowerDevice)tempswitchs.get(i);
	   	 		if (devRunType.equals("") || !tempDev.getDeviceType().equals(SystemConstants.Switch)) {
					switchs.add(tempDev);
				} else {
				    if (tempDev.getDeviceRunType().equals(devRunType))
					    switchs.add(tempDev);
				}
			}
   	  	    
		}	
		
		if(Integer.valueOf(rbm.getBeginStatus()) < Integer.valueOf(rbm.getEndState())) {
			Collections.sort(switchs, new Comparator<PowerDevice>() {
				public int compare(PowerDevice pd1, PowerDevice pd2) {
					if(pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
						return 1;
					else if(pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
						return 0;
					else
						return pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName());
				}
			});
		}
		else {
			Collections.sort(switchs, new Comparator<PowerDevice>() {
				public int compare(PowerDevice pd1, PowerDevice pd2) {
					if(pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
						return 0;
					else if(pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
						return 1;
					else
						return pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName());
				}
			});
			
		}
	    //TODO 如果是3/2接线方式就把中间的开关放前面
		if(switchs.size()==2&&switchs.get(1).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
			PowerDevice switch1 = switchs.get(0);
			PowerDevice switch2 = switchs.get(1);
			boolean b1=RuleExeUtil.isSwMiddleInThreeSecond(switch1);
			boolean b2=Integer.valueOf(rbm.getBeginStatus()) < Integer.valueOf(rbm.getEndState());
		    if(b1&&b2){
		    	switchs.clear();
		    	switchs.add(switch1);
		    	switchs.add(switch2);	
		    }else{
		    	switchs.clear();
		    	switchs.add(switch2);
		    	switchs.add(switch1);
		    }
		}
		
		///750主变特殊操作
	  	    if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)) {
	  	    	if(rbm.getEndState().equals("3")) {
	  	    		RuleExeUtil.swapLowDeviceList(switchs);
	  	    	}
	  	    	else if(rbm.getBeginStatus().equals("3")) {
	  	    		Collections.sort(switchs, new Comparator<PowerDevice>() {
	  	  			public int compare(PowerDevice pd1, PowerDevice pd2) {
	  	  				if (pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
	  	  					return 1;
	  	  				else if (pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
	  	  					return 0;
	  	  				else
	  	  					return Math.abs(pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName())-1);
	  	  			}
	  	  		});
	  	    	}
	  	    	
	  	    	List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.Switch+","+SystemConstants.PowerTransformer, true, true, true);
	   	  	    for(Iterator it = mlList.iterator(); it.hasNext();) {
	   	  	    	PowerDevice dev = (PowerDevice)it.next();
	   	  	    	if(dev.getPowerVoltGrade() != 35)
	   	  	    		it.remove();
	   	  	    }
	   	  	    if(mlList.size() > 0) {
	   	  	    	for(PowerDevice ml : mlList) {
	   	  	    		List<PowerDevice> swList = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
	   	  	    		switchs.addAll(swList); //加入35kV主开关
	   	  	    	}
	   	  	    }
	   	  	    
	   	  	    //
	   	  	    List<PowerDevice> compList = new ArrayList<PowerDevice>(); 
		   	  	List<PowerDevice> sw66List = new ArrayList<PowerDevice>(); 
		   	  	for(PowerDevice sw : switchs) {
			   	  	if(sw.getPowerVoltGrade() == 35) {
			   	  		sw66List.add(sw);
			   	  	}
		   	  	}
		   	  	
		     	for(PowerDevice sw : sw66List) {
		     		List<PowerDevice> ml66List = RuleExeUtil.getDeviceList(sw, SystemConstants.MotherLine, SystemConstants.Switch+","+SystemConstants.PowerTransformer, true, true, true);
		   	  	    for(Iterator it = ml66List.iterator(); it.hasNext();) {
		   	  	    	PowerDevice ml = (PowerDevice)it.next();
			   	  	    if(mlList.contains(ml))
		     				continue;
		   	  	    	List<PowerDevice> swList2 = RuleExeUtil.getDeviceList(ml, SystemConstants.ElecShock+","+SystemConstants.ElecCapacity+","+SystemConstants.Term, SystemConstants.PowerTransformer, true, true, true);
		   	  	    	mlList.add(ml);
		   	  	    	compList.addAll(swList2);
		   	  	    }
		   	  	}
		     	
		     	RuleExeUtil.swapDeviceListNum(compList);
		     	switchs.addAll(mlList);
		     	switchs.addAll(compList);
	  	    }
	  	    else if(pd.getDeviceType().equals(SystemConstants.MotherLine)) {
		   	  	inPara.put("oprSrcDevice", pd);
				inPara.put("tagDevType", SystemConstants.ElecShock);
		        cs.execute(inPara, outPara);
		        tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
			  	    for (int i = 0; i < tempswitchs.size(); i++) {
		   	 		tempDev=(PowerDevice)tempswitchs.get(i);
		   	 		if (tempDev.getPowerVoltGrade() == 35)
						switchs.add(tempDev);
				}
	  	}
		
		
		for (int i = 0; i < switchs.size(); i++) {
			
			tempDev = (PowerDevice) switchs.get(i);
			if(!rbm.getBeginStatus().equals("")&&!rbm.getBeginStatus().equals(tempDev.getDeviceStatus()))
				continue;
			
			//根据用户设置的目标状态开关可能不执行状态转换
			if(CBSystemConstants.LineTagStatus.containsKey(tempDev)) {
		 		String tagStatus = CBSystemConstants.LineTagStatus.get(tempDev);
		 		if(rbm.getBeginStatus().equals("0") && tempDev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
		 			;/////3/2接线
		 		else if(Integer.valueOf(CBSystemConstants.getDeviceStateValue(rbm.getBeginStatus())) < Integer.valueOf(CBSystemConstants.getDeviceStateValue(rbm.getEndState()))) {
			 		if(Integer.valueOf(tagStatus) < Integer.valueOf(rbm.getEndState()))
			 			continue;
		 		}
		 		else {
		 			if(Integer.valueOf(tagStatus) > Integer.valueOf(rbm.getEndState()))
		 				continue;
		 		}
		 	}
			
			String beginStatus="";
			if(rbm.getBeginStatus().equals(""))
				beginStatus=tempDev.getDeviceStatus();
			else
				beginStatus=rbm.getBeginStatus();
			
			if(tempDev.getDeviceType().equals(SystemConstants.MotherLine)) {
				RuleExeUtil.deviceStatusSet(tempDev, beginStatus, rbm.getEndState());
				continue;
			}
			
			
			RuleExecute ruleExecute=new RuleExecute();
			RuleBaseMode rbmode=new RuleBaseMode();
			rbmode.setPd(tempDev);
			rbmode.setBeginStatus(beginStatus);
			rbmode.setEndState(rbm.getEndState());
			if(!ruleExecute.execute(rbmode)){
				//return false;
				continue;
			}
        }
		
		return true;
	}
}
