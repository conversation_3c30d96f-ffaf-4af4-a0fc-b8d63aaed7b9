package com.tellhow.czp.app.yndd;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.service.CheckCzpImpl;
import com.tellhow.czp.service.CheckStatusImpl;
import com.tellhow.graphicframework.startup.StartupManager;

public class GetCheckImplTestLJ {
    public static void main(String[] params) {
	    CheckCzpImpl check = new CheckCzpImpl();
	    CheckStatusImpl checkback = new CheckStatusImpl();
	
	    String param = "";
	
		StartupManager.startup();
		CZPService.getService().setArg(param);
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>丽江地调</changzhan><caozuozhiling>执行110kV荣将变110kV华荣Ⅱ回路153短路器由热备用转冷备用程序操作</caozuozhiling><cbid>1456</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>220kV华坪变(新)</changzhan><caozuozhiling>合上110kv华荣Ⅰ回18267线路接地开关</caozuozhiling><cbid>1456</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>丽江地调</changzhan><caozuozhiling>执行220kV岩乐变220kV凤岩II回线284断路器由冷备用转热备用上220kVII母程序操作</caozuozhiling><cbid>1456</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>丽江地调</changzhan><caozuozhiling>执行110kV金山变110kV北金线181断路器由运行转热备用程序操作</caozuozhiling><cbid>1456</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>丽江地调</changzhan><caozuozhiling>执行110kV北门坡变110kV北金线175断路器由运行转热备用程序操作</caozuozhiling><cbid>3</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>110kV金山变</changzhan><caozuozhiling>将110kV北金线181断路器由热备用转冷备用</caozuozhiling><cbid>4</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>110kV北门坡变</changzhan><caozuozhiling>将110kV北金线175断路器由热备用转冷备用</caozuozhiling><cbid>6</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>110kV北门坡变</changzhan><caozuozhiling>合上110kV北金线17567线路接地开关</caozuozhiling><cbid>8</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>110kV金山变</changzhan><caozuozhiling>合上110kV北金线18167线路接地开关</caozuozhiling><cbid>9</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		check.execute(param);
    }
}
