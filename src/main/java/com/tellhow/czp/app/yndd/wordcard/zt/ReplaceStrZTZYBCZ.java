package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.zt.ZTZYBZTXZ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTZYBCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通站用变操作".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			
			String beginstatus =  ZTZYBZTXZ.retMap.get("BEGINSTATUS");
			String endstatus =  ZTZYBZTXZ.retMap.get("ENDSTATUS");

			List<Map<String,String>> zybList = new ArrayList<Map<String,String>>();
			
			if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
				String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_LINEZYB WHERE LINE_ID = '"+curDev.getPowerDeviceID()+"'";
				zybList =  DBManager.queryForList(sql);
			}else{
				String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+curDev.getPowerDeviceID()+"'";
				zybList =  DBManager.queryForList(sql);
			}

			for(Map<String,String> map : zybList){
				 String zybName = StringUtils.ObjToString(map.get("ZYB_NAME"));
				 replaceStr = stationName+"@将"+zybName+"由"+beginstatus+"转"+endstatus;
			}
		}
		
		return replaceStr;
	}

}
