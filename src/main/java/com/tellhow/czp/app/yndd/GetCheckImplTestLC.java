package com.tellhow.czp.app.yndd;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.service.CheckCzpImpl;
import com.tellhow.czp.service.CheckStatusImpl;
import com.tellhow.graphicframework.startup.StartupManager;

public class GetCheckImplTestLC {
    public static void main(String[] params) {
	    CheckCzpImpl check = new CheckCzpImpl();
	    CheckStatusImpl checkback = new CheckStatusImpl();
	
	    String param = "";
	
		StartupManager.startup();
		CZPService.getService().setArg(param);
		
			
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>110kV大石桥变</changzhan><caozuozhiling>拉开110kV南大Ⅱ回线17567接地开关</caozuozhiling><cbid>1456</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV南伞变</changzhan><caozuozhiling>拉开110kV南大Ⅱ回线18267接地开关</caozuozhiling><cbid>3</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>执行220kV南伞变110kV南大Ⅱ回线182断路器由冷备用转热备用程序操作</caozuozhiling><cbid>4</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>执行110kV大石桥变110kV南大Ⅱ回线175断路器由冷备用转热备用程序操作</caozuozhiling><cbid>6</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>遥控合上220kV南伞变110kV南大Ⅱ回线182断路器</caozuozhiling><cbid>8</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>遥控合上110kV大石桥变110kV南大Ⅱ回线175断路器</caozuozhiling><cbid>9</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>遥控断开110kV大石桥变110kV南大Ⅱ回线175断路器</caozuozhiling><cbid>9</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>遥控断开220kV南伞变110kV南大Ⅱ回线182断路器</caozuozhiling><cbid>8</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>执行110kV大石桥变110kV南大Ⅱ回线175断路器由热备用转冷备用程序操作</caozuozhiling><cbid>4</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>执行220kV南伞变110kV南大Ⅱ回线182断路器由热备用转冷备用程序操作</caozuozhiling><cbid>6</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV南伞变</changzhan><caozuozhiling>合上110kV南大Ⅱ回线18267接地开关</caozuozhiling><cbid>3</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>110kV大石桥变</changzhan><caozuozhiling>合上110kV南大Ⅱ回线17567接地开关</caozuozhiling><cbid>1456</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>遥控断开220kV昔本变220kV1号主变35kV侧301断路器</caozuozhiling><cbid>1</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>遥控断开220kV昔本变220kV1号主变110kV侧101断路器</caozuozhiling><cbid>2</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>执行220kV昔本变220kV1号主变35kV侧301断路器由热备用转冷备用程序操作</caozuozhiling><cbid>7</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>遥控断开220kV昔本变220kV1号主变220kV侧201断路器</caozuozhiling><cbid>3</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>执行220kV昔本变220kV1号主变110kV侧101断路器由热备用转冷备用程序操作</caozuozhiling><cbid>5</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>执行220kV昔本变220kV1号主变220kV侧201断路器由热备用转冷备用程序操作</caozuozhiling><cbid>6</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";

		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>220kV昔本变</changzhan><caozuozhiling>将110kVⅠ母上运行的所有断路器倒至110kVⅡ母上运行</caozuozhiling><cbid>1</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>遥控断开220kV昔本变110kV母联112断路器</caozuozhiling><cbid>2</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>临沧地调</changzhan><caozuozhiling>执行220kV昔本变110kVⅠ母由热备用转冷备用程序操作</caozuozhiling><cbid>3</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		
		check.execute(param);
    }
}
