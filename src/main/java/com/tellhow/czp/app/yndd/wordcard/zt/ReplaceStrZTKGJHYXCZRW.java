package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTKGJHYXCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通开关解环运行操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			//断点搜索线路时，遇到母线停止
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, false, true);
			
			PowerDevice beginStation = new PowerDevice();
			PowerDevice endStation = new PowerDevice();

			for(PowerDevice dev : lineList){
				List<PowerDevice> otherLineList =  RuleExeUtil.getLineOtherSideList(dev);
				
				for(PowerDevice other : otherLineList){
					PowerDevice otherstation = CBSystemConstants.getPowerStation(other.getPowerStationID());
					
					if(otherstation.getPowerVoltGrade() == 220){
						beginStation = otherstation;
					}
				}
				
				if(!beginStation.getPowerDeviceID().equals("")){
					break;
				}
			}

			for(PowerDevice dev : lineList){
				if(dev.getPowerDeviceName().contains("北水Ⅰ回")||dev.getPowerDeviceName().contains("北水Ⅱ回")||dev.getPowerDeviceName().contains("北总云")||dev.getPowerDeviceName().contains("水总云")){
					replaceStr += "220kV北门变-110kV水富变-110kV云天化煤化工变110kV环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
					return replaceStr;
				}
			}

			for(PowerDevice dev : lineList){
				if(dev.getPowerDeviceName().contains("油翠")||dev.getPowerDeviceName().contains("高大翠")||dev.getPowerDeviceName().contains("油大")){
					replaceStr += "220kV大关变-110kV油坊沟电站-110kV翠华变110kV环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
					return replaceStr;
				}
			}
			
			String beginStationName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(beginStation));
			String endStationName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(endStation));

			if(beginStationName.startsWith("西")&&endStationName.startsWith("昭")){
				replaceStr = "220kV/110kV昭西电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}else if(beginStationName.startsWith("昭")&&endStationName.startsWith("西")){
				replaceStr = "220kV/110kV昭西电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}
			
			if(beginStationName.startsWith("西")&&endStationName.startsWith("发")){
				replaceStr = "220kV/110kV西发电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}else if(beginStationName.startsWith("发")&&endStationName.startsWith("西")){
				replaceStr = "220kV/110kV西发电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}
			
			if(beginStationName.startsWith("大")&&endStationName.startsWith("发")){
				replaceStr = "220kV/110kV发大电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}else if(beginStationName.startsWith("发")&&endStationName.startsWith("大")){
				replaceStr = "220kV/110kV发大电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}
			
			if(beginStationName.startsWith("北")||endStationName.startsWith("北")){
				replaceStr = "220kV/110kV北盐电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}
			
			if(beginStationName.startsWith("镇")&&endStationName.startsWith("徐")){
				replaceStr = "220kV/110kV镇徐电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}else if(beginStationName.startsWith("徐")&&endStationName.startsWith("镇")){
				replaceStr = "220kV/110kV镇徐电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}
			
			if(beginStationName.startsWith("镇")&&endStationName.startsWith("安")){
				replaceStr = "220kV/110kV安镇电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}else if(beginStationName.startsWith("安")&&endStationName.startsWith("镇")){
				replaceStr = "220kV/110kV安镇电磁环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}
			
			if(beginStationName.equals(endStationName)){
				String firstword1 = StringUtils.killVoltInDevName(beginStationName).substring(0,1);
				String firstword2 = StringUtils.killVoltInDevName(stationName).substring(0,1);

				replaceStr = "110kV"+firstword1+firstword2+"环网断点在"+stationName+CZPService.getService().getDevName(curDev)+"处解环运行";
				return replaceStr;
			}
		}
		
		return replaceStr;
	}

}
