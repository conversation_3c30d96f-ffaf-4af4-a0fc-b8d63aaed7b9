package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;

public class ZTHaveWorkDialog implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(pd);
	    	String lineName = CZPService.getService().getDevName(pd);
			
			LineWorkSelectionDialog wsd = new LineWorkSelectionDialog(SystemConstants.getMainFrame(), true, lineList,lineName);
			StationWorkSelectionDialog swsd = new StationWorkSelectionDialog(SystemConstants.getMainFrame(), true, lineList);

			if(wsd.isCancel()){
				return false;
			}
			
			if(swsd.isCancel()){
				return false;
			}
		}else{
			List<PowerDevice> deviceList = new ArrayList<PowerDevice>();
			deviceList.add(pd);
			
			StationWorkSelectionDialog swsd = new StationWorkSelectionDialog(SystemConstants.getMainFrame(), true, deviceList);

			if(swsd.isCancel()){
				return false;
			}
		}
		
		return true;
	}
}

