package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPETVFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱TV复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
				replaceStr += stationName+"@核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
				replaceStr += stationName+"@将"+deviceName+"线路TV由冷备用转运行/r/n";
			}else{
				List<PowerDevice> dzList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeQT, "", true, true, true, true);
				
				for(PowerDevice dev : dzList){
					String dzNum = CZPService.getService().getDevNum(dev);
					
					if(dzNum.contains("901") || dzNum.contains("902")){
						if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
							replaceStr += stationName+"@核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
							replaceStr += "普洱地调@遥控合上"+stationName+deviceName+"TV"+dzNum+"隔离开关/r/n";
							replaceStr += stationName+"@核实"+deviceName+"TV"+dzNum+"隔离开关处于合上位置/r/n";
						}else{
							replaceStr += stationName+"@核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
							replaceStr += stationName+"@将"+deviceName+"TV由冷备用转运行/r/n";
						}
					}
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
