package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNDMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳单母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);

			replaceStr += CommonFunctionQJ.getPowerOnCheckContent();

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getTransformerKnifeLoad(curDev);
			List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbdyckgList = new ArrayList<PowerDevice>();
			
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdzList){
				if(dev.getPowerVoltGrade() == lowvolt){
					zbdycdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						otherzbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
						otherzbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					replaceStr += CommonFunctionBN.getTrSwitchProtect(dev, stationName);
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@确认"+(int)dev.getPowerVoltGrade()+"kV备自投装置处投入状态/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					replaceStr += CommonFunctionBN.getTrSwitchProtect(dev, stationName);
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@确认"+(int)dev.getPowerVoltGrade()+"kV备自投装置处投入状态/r/n";
				}else{
					for(PowerDevice dycmx : dycmxList){
						if(RuleExeUtil.getDeviceEndStatus(dycmx).equals("0")){
							replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : zxdjddzList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合位/r/n";
				}
			}

			String isControl = CommonFunctionBN.ifDeviceControlBN(curDev);
			
			if(isControl.equals("全部可控")){
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					replaceStr += "版纳地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbgyckgList,stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbzyckgList,stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbdyckgList,stationName);

					for(PowerDevice dev : zbdycdzList){
//						replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(dev,stationName);
						replaceStr += CommonFunctionBN.getCdOrHhContent(dev, "版纳地调", stationName);
					}
					

					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
//							replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							replaceStr += CommonFunctionBN.getCdOrHhContent(dev, "版纳地调", stationName);
						}
					}
					

					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
//							replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							replaceStr += CommonFunctionBN.getCdOrHhContent(dev, "版纳地调", stationName);
						}
					}

					if(CommonFunctionBN.isMotherLineFd(dycmxList)){
						for(PowerDevice dev : dycmxList){
							replaceStr += "版纳配调@将对"+stationName+CZPService.getService().getDevName(dev)+"送电，配调管辖设备无影响"+CZPService.getService().getDevName(dev)+"送电情况/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
			}else{
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					if(isControl.equals("部分可控")){
						boolean isControlGyc = false;
						boolean isControlZyc = false;

						for(PowerDevice dev : zbgyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								if(CommonFunctionBN.ifSwitchSeparateControlBN(dev)){
									isControlGyc = true;
								}
							}
						}
						
						for(PowerDevice dev : zbzyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								if(CommonFunctionBN.ifSwitchSeparateControlBN(dev)){
									isControlZyc = true;
								}
							}
						}
						
						String addConTent = "";
						
						/*if(isControlGyc){
							for(PowerDevice dev : zbgyckgList){
								addConTent += CZPService.getService().getDevNum(dev)+"、";
							}
							
							if(isControlZyc){
								for(PowerDevice dev : zbzyckgList){
									addConTent += CZPService.getService().getDevNum(dev)+"、";
								}
							}
							
							if(addConTent.endsWith("、")){
								addConTent = "，"+addConTent.substring(0, addConTent.length()-1)+"断路器冷备用";
							}
						}*/
						
						replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用"+addConTent+"/r/n";
						
						if(isControlGyc){
							for(PowerDevice dev : zbgyckgList){
								String devName = CZPService.getService().getDevName(dev);
								replaceStr += "版纳地调@执行"+stationName+devName+"由冷备用转热备用程序操作/r/n";
								replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbgyckgList,stationName);
							}
						}
						
						if(isControlZyc){
							for(PowerDevice dev : zbzyckgList){
								String devName = CZPService.getService().getDevName(dev);
								replaceStr += "版纳地调@执行"+stationName+devName+"由冷备用转热备用程序操作/r/n";
								replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbzyckgList,stationName);
							}
						}
					}else{
						replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
//							replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							replaceStr += CommonFunctionBN.getCdOrHhContent(dev, "版纳地调", stationName);
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
							replaceStr += "版纳地调@确认"+stationName+CZPService.getService().getDevName(dev)+"处合闸位置/r/n";
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
//							replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
							replaceStr += CommonFunctionBN.getCdOrHhContent(dev, "版纳地调", stationName);
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(CommonFunctionBN.isMotherLineFd(dycmxList)){
						for(PowerDevice dev : dycmxList){
							replaceStr += "版纳配调@将对"+stationName+CZPService.getService().getDevName(dev)+"送电，配调管辖设备无影响"+CZPService.getService().getDevName(dev)+"送电情况/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionBN.getCdOrHhContent(dev, "版纳地调", stationName);
							/*if(dycmlkgList.size() == 0){
								replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}else{
								boolean ishh = false;
								
								for(PowerDevice dycmlkg : dycmlkgList){
									if(dycmlkg.getDeviceStatus().equals("0")){
										ishh = true;
									}
								}
								
								if(ishh){
									replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
								}else{
									replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
								}
							}*/
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : zxdjddzList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "版纳地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(station.getPowerVoltGrade() > 35){
				replaceStr += stationName+"@将"+(int)curDev.getPowerVoltGrade()+"kVX号主变保护定值区由XX区调整至XX区/r/n";
			}
			
			if(CommonFunctionBN.isMotherLineFd(dycmxList)){
				for(PowerDevice dev : dycmxList){
					replaceStr += "版纳配调@通知"+stationName+CZPService.getService().getDevName(dev)+"已送电/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
