package com.tellhow.czp.app.yndd.wordcard.hh;


import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 */
public class ReplaceStrHHXLKGBHJH implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("红河线路开关编号集合".equals(tempStr)){
			if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
				List<PowerDevice> xlList = RuleExeUtil.getLineAllSideList(curDev);
				
				sortByLineName(xlList);
				
				List<String> numList = new ArrayList<String>();
				
				for(PowerDevice xl:xlList){
					List<PowerDevice> swList = RuleExeUtil.getDeviceList(xl, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,CBSystemConstants.RunTypeSideMother, false,  true, true, true);
					List<PowerDevice> hignVoltXldzList = RuleExeUtil.getDeviceList(xl, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXLS, "", false, true, true, true);
					
					swList.addAll(hignVoltXldzList);
					
					for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(!RuleExeUtil.isDeviceChanged(dev)){
							it.remove();
						}
					}
					
					for(PowerDevice sw : swList){
						numList.add(CZPService.getService().getDevNum(sw));
					}
				}
				
				String sql = "SELECT LOWERUNIT,LINE_NAME,SWITCH_NAME,DISCONNECTOR_NAME,UNIT,OPERATION_KIND,LOWERUNIT,ENDPOINT_KIND "
						+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE  ISREMOVE = '0' AND  LINE_ID IN "
								+ "(  SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+curDev.getPowerDeviceID()+"')";
				List<Map<String, Object>> stations = DBManager.queryForList(sql);
				
				for(Map<String, Object> station:stations) {
					String switchName = StringUtils.ObjToString(station.get("SWITCH_NAME")).trim();
					String dzName = StringUtils.ObjToString(station.get("DISCONNECTOR_NAME")).trim();
					
					String switchNum = CZPService.getService().getDevNum(switchName);
					String dzNum = CZPService.getService().getDevNum(dzName);

					if(!switchNum.equals("")){
						numList.add(switchNum);
					}else if(!dzNum.equals("")){
						numList.add(dzNum);
					}
				}
				
				for(String num : numList){
					if(!num.equals("")){
						replaceStr += num+"～";
					}
				}
				
				if(replaceStr.endsWith("～")){
					replaceStr="（"+replaceStr.substring(0,replaceStr.length()-1)+"）";
				}
			}else if(curDev.getDeviceType().equals(SystemConstants.MotherLine)){
				List<PowerDevice> xlList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				if(xlList.size()>1){
					for(int i=0;i<xlList.size();i++){
						PowerDevice xl = xlList.get(i);
						String xlName = xl.getPowerDeviceName();
						String stationName = xl.getPowerStationName();
						if(xlName.contains(".")){
							xlName=xlName.substring(xlName.lastIndexOf(".")+1);
						}
						if(stationName.contains(".")){
							stationName=stationName.substring(stationName.lastIndexOf(".")+1);
						}
						if(xlName.startsWith(stationName.substring(0,1))){
							xlList.remove(i);
							i--;
						}
					}
				}
				if(xlList.size()>0){
					xlList = RuleExeUtil.getLineAllSideList(xlList.get(0));
					RuleExeUtil.sortByLineName(xlList);
					for(PowerDevice xl:xlList){
						List<PowerDevice> swList = RuleExeUtil.getDeviceList(xl, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						if(swList.size()>0){
							for(PowerDevice sw : swList){
								String stationName=sw.getPowerStationName();
								if(stationName.contains(".")){
									stationName=stationName.substring(stationName.indexOf(".")+1);
								}
								if(stationName.length()>0){
									stationName =stationName.substring(0,1);
								}
								replaceStr+=stationName+CZPService.getService().getDevNum(sw)+"～";
							}
						}
					}
					if(replaceStr.endsWith("～")){
						replaceStr="（"+replaceStr.substring(0,replaceStr.length()-1)+"）";
					}
				}
			}
	    }
		return replaceStr;
	}
	
	public  void sortByLineName(List<PowerDevice> lineDevices){
		if(lineDevices.size()>0){
			String lineName = "";
			for(PowerDevice dev:lineDevices){
				if(dev.getPowerDeviceName().contains("T线")){
					lineName=CZPService.getService().getDevName(dev);
					break;
				}
			}
			if(lineName.equals("")){
				RuleExeUtil.sortByLineName(lineDevices);
			}else{
				String devname = lineName.substring(0,lineName.indexOf("T线"));
				
				PowerDevice beginDevice = null;
				PowerDevice endDevice = null;
				for(PowerDevice dev:lineDevices){
					if(StringUtils.killVoltInDevName(dev.getPowerStationName()).substring(0,1).equals(devname.substring(devname.length()-1,devname.length()))){
						endDevice =dev;
					}else if(StringUtils.killVoltInDevName(dev.getPowerStationName()).substring(0,1).equals(devname.substring(devname.length()-2,devname.length()-1))){
						beginDevice=dev;
					}
					
				}
				if(beginDevice!=null){
					lineDevices.remove(beginDevice);
					lineDevices.add(0,beginDevice);
				}
				if(endDevice!=null){
					lineDevices.remove(endDevice);
					lineDevices.add(endDevice);
				}
			}
					
					
			
			
		}
	}
}
