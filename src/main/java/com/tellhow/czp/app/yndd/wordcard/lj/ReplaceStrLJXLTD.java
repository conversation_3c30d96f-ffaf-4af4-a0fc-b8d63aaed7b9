package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.lj.JDKGXZLJ;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJXLTD  implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江线路停电".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());

			List<Map<String, String>> stationLineList = CommonFunctionLJ.getStationLineList(curDev);
			
			boolean isControl = true;

			for(PowerDevice dev : loadLineTrans){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionLJ.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(sourceLineTrans!=null){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionLJ.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(stationLineList.size() > 0){
				isControl = false;
			}
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			
			kgList.addAll(xlkgList);
			kgList.addAll(zbkgList);

			boolean isRunModelThreeTwo = false;
			
			for(PowerDevice dev : kgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					isRunModelThreeTwo = true;
					break;
				}
			}
			
			List<String> bztList = new ArrayList<String>();
			
			if(isRunModelThreeTwo){
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						replaceStr += stationName+"@断开"+lowerunit+switchName+"/r/n";
					}else if(operationkind.equals("许可")){
						if(!switchName.equals("")){
							replaceStr += stationName+"@确认"+lowerunit+switchName+"处于冷备用/r/n";
						}else{
							replaceStr += stationName+"@确认"+lowerunit+disconnectorName+"处拉开位置/r/n";
						}
					}else if(operationkind.equals("配合")){
						replaceStr += stationName+"@确认"+lowerunit+switchName+"处于热备用/r/n";
					}
				}
				
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> xlkgLoadList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					for(PowerDevice dev : xlkgLoadList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							String deviceName = CZPService.getService().getDevName(dev);
							replaceStr += "丽江地调@遥控断开"+stationName+deviceName+"/r/n";
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "丽江地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "丽江地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							if(CommonFunctionLJ.ifSwitchSeparateControl(dev)){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "丽江地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
								
								if(curDev.getPowerVoltGrade() > 110){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									dzList = RuleExeUtil.sortByXLC(dzList);
									
									for(PowerDevice dz : dzList){
										if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
											replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
										}
									}
								}
							}else{
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						replaceStr += stationName+"@将"+lowerunit+switchName+"由热备用转冷备用/r/n";
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制前的要求进行设置/r/n";
					
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							
							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "丽江地调@遥控拉开"+stationName+devname+"/r/n";
									replaceStr += stationName+"@确认"+devname+"处拉开位置/r/n";
								}
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							
							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "丽江地调@遥控拉开"+stationName+devname+"/r/n";
									replaceStr += stationName+"@确认"+devname+"处拉开位置/r/n";
								}
							}
						}
					}
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制后的要求进行设置/r/n";
				}
			}else if(isControl){
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
							replaceStr += "丽江地调@确认"+stationName+CZPService.getService().getDevName(dev)+"处分闸位置/r/n";
						}
					}
				}
				
				replaceStr += "丽江地调@执行"+CZPService.getService().getDevName(curDev)+"由运行转冷备用程序操作/r/n";

				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
					
					for(PowerDevice xlsw : xlswList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
						
						if(xlsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							dzList = RuleExeUtil.sortByCZMXC(dzList);
						}else{
							if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
								dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							}else{
								dzList = RuleExeUtil.sortByMXC(dzList);
								Collections.reverse(dzList);
							}
						}
						
						for(PowerDevice dz : dzList){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处分闸位置/r/n";
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice xlsw : xlkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
						
						if(xlsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							dzList = RuleExeUtil.sortByCZMXC(dzList);
						}else{
							if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
								dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							}else{
								dzList = RuleExeUtil.sortByMXC(dzList);
								Collections.reverse(dzList);
							}
						}
						
						for(PowerDevice dz : dzList){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处分闸位置/r/n";
						}
					}
				}
			}else{
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")||RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
							replaceStr += "丽江地调@确认"+stationName+CZPService.getService().getDevName(dev)+"处分闸位置/r/n";
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")||RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
							replaceStr += "丽江地调@确认"+stationName+CZPService.getService().getDevName(dev)+"处分闸位置/r/n";
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						if(!switchName.equals("")){
							replaceStr += stationName+"@断开"+lowerunit+switchName+"/r/n";
						}
					}else if(operationkind.equals("许可")){
						if(!switchName.equals("")){
							if(switchName.contains("、")){
								String[] switchNameArr = switchName.split("、");
								
								for(String kgName : switchNameArr){
									replaceStr += stationName+"@确认"+lowerunit+kgName+"处分闸位置/r/n";
								}
							}else{
								replaceStr += stationName+"@确认"+lowerunit+switchName+"处分闸位置/r/n";
							}
						}else{
							if(disconnectorName.contains("、")){
								String[] disconnectorNameArr = disconnectorName.split("、");
								
								for(String dzName : disconnectorNameArr){
									replaceStr += stationName+"@确认已拉开"+lowerunit+dzName+"/r/n";
								}
							}else if(!disconnectorName.equals("")){
								replaceStr += stationName+"@确认已拉开"+lowerunit+disconnectorName+"/r/n";
							}
						}
						
						replaceStr += stationName+"@确认"+lowerunit+lineName+"具备停电条件/r/n";
					}else if(operationkind.equals("配合")){
						if(!switchName.equals("")){
							if(switchName.contains("、")){
								String[] switchNameArr = switchName.split("、");
								
								for(String kgName : switchNameArr){
									replaceStr += stationName+"@确认已断开"+lowerunit+kgName+"/r/n";
								}
							}else{
								replaceStr += stationName+"@确认已断开"+lowerunit+switchName+"/r/n";
							}
						}
						replaceStr += stationName+"@确认"+lowerunit+lineName+"具备停电条件/r/n";
					}
				}
				
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								hignVoltMlkgList.add(dev);
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								hignVoltXlkgList.add(dev);
							}
						}
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(hignVoltXlkgList);
					tempList.addAll(hignVoltMlkgList);

					for(PowerDevice dev : tempList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
							String bztContent = stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
							bztList.add(bztContent);
							replaceStr += CommonFunctionLJ.getHhContent(dev,"丽江地调", stationName);
						}
					}
					
					for(PowerDevice dev : tempList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionLJ.getSwitchOffContent(dev, stationName, station);
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionLJ.getSwitchOffContent(dev, stationName, station);
						}
					}
				}
				
				/*
				 * 热备用转冷备用
				 */
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

					if(xldzList.size() > 0){
						for(PowerDevice dev : xlkgList){
							String switchStatus = "，"+CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatusNew(dev.getDeviceType(), dev.getDeviceStatus());
							String deviceName = CZPService.getService().getDevName(dev);
							replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用"+switchStatus+"/r/n";
						}
					}else{
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")||RuleExeUtil.getDeviceEndStatus(dev).equals("3")){
								if(CommonFunctionLJ.ifSwitchSeparateControl(dev)){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += "丽江地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
									
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									replaceStr += CommonFunctionLJ.getKnifeOffCheckContent(dzList, stationName);
								}else{
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
								}
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					List<PowerDevice> zbswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
					List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

					if(xldzList.size() == 1 && xlswList.size() == 0){
						for(PowerDevice dz : xldzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dz)+"/r/n";
							}
						}
					}else if(xldzList.size() == 1 && xlswList.size() == 1){
						for(PowerDevice dev : xlswList){
							String switchStatus = "，"+CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatusNew(dev.getDeviceType(), dev.getDeviceStatus());
							String deviceName = CZPService.getService().getDevName(dev);
							replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用"+switchStatus+"/r/n";
						}
					}else if(zbswList.size() == 1 && xlswList.size() == 0){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbswList.get(0), SystemConstants.SwitchSeparate);

						if(dzList.size() == 1){
							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
									replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dz)+"/r/n";
								}
							}
						}
					}else{
						for(PowerDevice dev : xlswList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")||RuleExeUtil.getDeviceEndStatus(dev).equals("3")){
								if(CommonFunctionLJ.ifSwitchSeparateControl(dev)){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += "丽江地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
									
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									replaceStr += CommonFunctionLJ.getKnifeOffCheckContent(dzList, stationName);
								}else{
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String ptdisconnectorName = StringUtils.ObjToString(map.get("PTDISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						if(!disconnectorName.equals("")){
							replaceStr += stationName+"@拉开"+lowerunit+disconnectorName+"/r/n";
						}else if(!switchName.equals("")){
							replaceStr += stationName+"@将"+lowerunit+switchName+"由热备用转冷备用/r/n";
						}
						
						if(!ptdisconnectorName.equals("")){
							if(ptdisconnectorName.contains("、")){
								String[] ptdisconnectorNameArr = ptdisconnectorName.split("、");
								
								for(String dzName : ptdisconnectorNameArr){
									replaceStr += stationName+"@将"+lowerunit+dzName+"由运行转冷备用/r/n";
								}
							}else{
								if(ptdisconnectorName.contains("站用变")){
									replaceStr += stationName+"@确认已将"+lowerunit+ptdisconnectorName+"由运行转冷备用/r/n";
								}else{
									replaceStr += stationName+"@将"+lowerunit+ptdisconnectorName+"由运行转冷备用/r/n";
								}
							}
						}
					}else if(operationkind.equals("许可")){
						if(!disconnectorName.equals("")){
							if(disconnectorName.contains("、")){
								String[] disconnectorNameArr = disconnectorName.split("、");
								
								for(String dzName : disconnectorNameArr){
									replaceStr += stationName+"@确认已拉开"+lowerunit+dzName+"/r/n";
								}
							}else{
								replaceStr += stationName+"@确认已拉开"+lowerunit+disconnectorName+"/r/n";
							}
						}else{
							if(!switchName.equals("")){
								replaceStr += stationName+"@确认已将"+lowerunit+switchName+"由热备用转冷备用/r/n";
							}
						}

						if(!ptdisconnectorName.equals("")){
							if(ptdisconnectorName.contains("、")){
								String[] ptdisconnectorNameArr = ptdisconnectorName.split("、");
								
								for(String dzName : ptdisconnectorNameArr){
									if(dzName.contains("隔离开关")){
										replaceStr += stationName+"@确认已拉开"+lowerunit+dzName+"/r/n";
									}else{
										replaceStr += stationName+"@确认已将"+lowerunit+dzName+"由运行转冷备用/r/n";
									}
								}
							}else{
								if(ptdisconnectorName.contains("隔离开关")){
									replaceStr += stationName+"@确认已拉开"+lowerunit+ptdisconnectorName+"/r/n";
								}else{
									replaceStr += stationName+"@确认已将"+lowerunit+ptdisconnectorName+"由运行转冷备用/r/n";
								}
							}
						}
					}else if(operationkind.equals("配合")){
						if(!switchName.equals("")){
							if(switchName.contains("、")){
								String[] switchNameArr = switchName.split("、");
								
								for(String kgName : switchNameArr){
									replaceStr += stationName+"@确认已将"+lowerunit+kgName+"由热备用转冷备用/r/n";
								}
							}else{
								replaceStr += stationName+"@确认已将"+lowerunit+switchName+"由热备用转冷备用/r/n";
							}
						}
					}
				}
			}
			
			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("3")){
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String voltStationName = CZPService.getService().getDevName(station); 
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					
					if(jddzList.size() == 0){
						List<PowerDevice> dzLoad = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzLoad){
							if(!dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)){
								jddzList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchFlowGroundLine);
							}
						}
					}
					
					if(jddzList != null){
						if(JDKGXZLJ.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
						    replaceStr += voltStationName+"@在"+CZPService.getService().getDevName(curDev)+"线路侧装设三相接地线/r/n";
						}else{
						    replaceStr += voltStationName+"@合上"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
					 	}
					}
				}
				
				for(Map<String, String> stationLine : stationLineList) {
					String linename = StringUtils.ObjToString(stationLine.get("LINE_NAME")).trim();
					String stationName = StringUtils.ObjToString(stationLine.get("UNIT")).trim();
					String ddname = StringUtils.ObjToString(stationLine.get("GROUNDDISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(stationLine.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(stationLine.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")||operationkind.equals("落实")){
						boolean zsccdx =  false;
						
						for(PowerDevice dev : JDKGXZLJ.chooseEquips){
							if(dev.getPowerStationName().equals(stationName)||dev.getPowerStationName().equals(lowerunit)){
								zsccdx = true;
								replaceStr += stationName+"@在"+lowerunit+linename+"线路侧装设三相接地线/r/n";
								break;
							 }
						}
						
						if(!zsccdx){
							if(ddname.equals("")){
								replaceStr += stationName+"@在"+lowerunit+linename+"线路侧装设三相接地线/r/n";
							}else{
								replaceStr += stationName+"@合上"+lowerunit+ddname+"/r/n";
							}
						}
					}
				}
				
				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String voltStationName = CZPService.getService().getDevName(station); 
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
					
					if(jddzList != null){
						if(jddzList.size() == 0){
							List<PowerDevice> dzLoad = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							for(PowerDevice dz : dzLoad){
								if(!dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)){
									jddzList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchFlowGroundLine);
								}
							}
						}
						
						if(JDKGXZLJ.chooseEquips.contains(dev)||jddzList.size()==0){
						    replaceStr += voltStationName+"@在"+CZPService.getService().getDevName(curDev)+"线路侧装设三相接地线/r/n";
						}else{
						    replaceStr += voltStationName+"@合上"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
					 	}
					}
				}
			}
			
			for(String str : bztList){
				replaceStr += str;
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
