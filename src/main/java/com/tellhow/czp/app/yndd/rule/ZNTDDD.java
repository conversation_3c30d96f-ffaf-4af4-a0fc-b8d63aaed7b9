package com.tellhow.czp.app.yndd.rule;



import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.view.EquipRadioChoose;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

public class ZNTDDD implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
	
		
		List<String> lineMap = CBSystemConstants.getStationlinemap().get(pd.getPowerDeviceID());
		
		List<PowerDevice> xlList = new ArrayList<PowerDevice>();
		List<PowerDevice> motherLineList = new ArrayList<PowerDevice>();
		for(String lineID:lineMap){
			PowerDevice xl = CBSystemConstants.getPowerDevice(pd.getPowerDeviceID(),lineID+"_"+pd.getPowerDeviceID());
			if(xl==null){
				continue;
			}
			if(xl.getPowerVoltGrade()==pd.getPowerVoltGrade()){
				xlList.add(xl);
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(xl, SystemConstants.MotherLine, SystemConstants.PowerTransformer,
						true, true, true);
				if(mxList.size()>0&&mxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)
						&&!motherLineList.contains(mxList.get(0))){
					motherLineList.add(mxList.get(0));
				}
			}
		}
		
		if(motherLineList.size()==1){
			List<PowerDevice> kgyxList = new ArrayList<PowerDevice>();
			List<PowerDevice> kgrbyList = new ArrayList<PowerDevice>();
			for(PowerDevice xl:xlList){
				List<PowerDevice> swDeviceList = RuleExeUtil.getDeviceList(xl, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				if(swDeviceList.size()>0){
					if(swDeviceList.get(0).getDeviceStatus().equals("0")){
						kgyxList.add(swDeviceList.get(0));
					}else{
						kgrbyList.add(swDeviceList.get(0));
					}
				}
			}
			
			if(kgyxList.size()==0){
				ShowMessage.view("高压侧没有找到运行的线路开关!");
				return false;
			}
			
			if(kgrbyList.size()==0){
				ShowMessage.view("高压侧没有找到非运行的线路开关!");
				return false;
			}
			
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
			
			List<PowerDevice> zbkgChangeList = new ArrayList<PowerDevice>();
			for(PowerDevice zb:zbList){
				List<PowerDevice> zbswList = RuleExeUtil.getDeviceList(pd,SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				RuleExeUtil.swapDeviceList(zbswList);
				for(PowerDevice zbsw:zbswList){
					if(zbsw.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusExecute(zbsw, "0", "1");
					}
					zbkgChangeList.add(zbsw);
				}
			}
			
			for(PowerDevice kgyx: kgyxList){
				RuleExeUtil.deviceStatusExecute(kgyx, "0", "1");
			}
			
			
			PowerDevice kgChangeOn = null;
			if(kgrbyList.size()==1){
				kgChangeOn=kgrbyList.get(0);
			}else{
				EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, kgrbyList, "请选择需要合上的线路开关.");
				kgChangeOn = dcd.getChooseEquip();
				if(kgChangeOn==null){
					return false;
				}
			}
		}else{
			ShowMessage.view("该接线方式厂站停电调电未开发，请联系系统厂家！");
			return false;
		}
		
		return true;
	}
}
