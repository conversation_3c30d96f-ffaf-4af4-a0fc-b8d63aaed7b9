package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSZYBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山站用变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = "";

			String maintenanceName = CommonFunctionBS.getMaintenance(stationName);
			
			List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,
					CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);
			
			List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : qtkgList){
				if(dev.getPowerDeviceName().contains("站用变")){
					String devName = CZPService.getService().getDevName(dev);
					deviceName = devName.substring(0, devName.indexOf("站用变")+3);
					zybkgList.add(dev);
				}
			}
			
			if(stationName.equals(maintenanceName)){
				replaceStr += maintenanceName+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
			}else{
				replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
			}
			
			for(PowerDevice dev : zybkgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatus(dev);
				beginstatus = RuleExeUtil.getStatusNew(SystemConstants.Switch, beginstatus);
				
				if(dev.getDeviceStatus().equals("1")){
					replaceStr += "保山地调@执行"+stationName+deviceName+"由"+beginstatus+"转热备用程序操作/r/n";
				}else if(dev.getDeviceStatus().equals("2")){
					replaceStr += "保山地调@执行"+stationName+deviceName+"由"+beginstatus+"转冷备用程序操作/r/n";
				}
			}
			
			if(stationName.equals(maintenanceName)){
				replaceStr += maintenanceName+"@核实"+deviceName+"一、二次设备无异常/r/n";
			}else{
				replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备无异常/r/n";
			}
		}
		
		return replaceStr;
	}

}
