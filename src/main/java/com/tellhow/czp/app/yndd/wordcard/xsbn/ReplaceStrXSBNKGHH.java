package com.tellhow.czp.app.yndd.wordcard.xsbn;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNKGHH  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳开关合环".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			
//			220kV/110kV景洪变-嘎栋变-傣乡变电磁环网由开环运行转合环运行
			
			if(stationName.equals("110kV嘎栋变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "版纳地调@确认XXkVXX线（XX→XX）+ XXkVXX线（XX→XX）负荷 ≤ XXMW/r/n";
					replaceStr += "云南中调@核实220kV景洪变-220kV傣乡变220kV系统确为同期系统/r/n";
				}
			}else if(stationName.equals("110kV曼弄枫变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "版纳地调@确认XXkVXX线（XX→XX）+ XXkVXX线（XX→XX）负荷 ≤ XXMW/r/n";
					replaceStr += "云南中调@核实220kV景洪变-220kV黎明变220kV系统确为同期系统/r/n";
				}
			}else if(stationName.equals("110kV勐罕变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "版纳地调@确认XXkVXX线（XX→XX）+ XXkVXX线（XX→XX）负荷 ≤ XXMW/r/n";
					replaceStr += "云南中调@核实220kV景洪变-220kV黎明变220kV系统确为同期系统/r/n";
				}
			}else if(stationName.equals("110kV辉凰变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "版纳地调@确认XXkVXX线（XX→XX）+ XXkVXX线（XX→XX）负荷 ≤ XXMW/r/n";
					replaceStr += "云南中调@核实220kV景洪变-220kV傣乡变220kV系统确为同期系统/r/n";
				}
			}else if(stationName.equals("110kV东盟变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "版纳地调@确认XXkVXX线（XX→XX）+ XXkVXX线（XX→XX）负荷 ≤ XXMW/r/n";
					replaceStr += "云南中调@核实220kV茶城变-220kV黎明变220kV系统确为同期系统/r/n";
				}
			}else if(stationName.equals("110kV勐宽变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "版纳地调@确认XXkVXX线（XX→XX）+ XXkVXX线（XX→XX）负荷 ≤ XXMW/r/n";
					replaceStr += "云南中调@核实220kV傣乡变-220kV黎明变220kV系统确为同期系统/r/n";
				}
			}
			
			replaceStr += CommonFunctionBN.getHhContent(curDev, "版纳地调", stationName);
			
			if(stationName.equals("110kV嘎栋变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV景洪-嘎栋-傣乡电磁环网已合环运行/r/n";
				}
			}else if(stationName.equals("110kV曼弄枫变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV景洪-曼弄枫-黎明电磁环网已合环运行/r/n";
				}
			}else if(stationName.equals("110kV勐罕变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV景洪-勐罕-黎明电磁环网已合环运行/r/n";
				}
			}else if(stationName.equals("110kV辉凰变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV景洪-江北-辉凰-傣乡电磁环网已合环运行/r/n";
				}
			}else if(stationName.equals("110kV东盟变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV茶城-东盟-勐腊-黎明电磁环网已合环运行/r/n";
				}
			}else if(stationName.equals("110kV勐宽变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV傣乡-巴奇-勐宽-黎明电磁环网已合环运行/r/n";
				}
			}
			
			replaceStr += stationName+"@退出110kV备自投装置/r/n";
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
