package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.km.OtherTransformFDChangeMotherLine;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBFDDM implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("主变复电倒母".equals(tempStr)) {
			
			if(OtherTransformFDChangeMotherLine.map!=null&&!OtherTransformFDChangeMotherLine.map.isEmpty()){
				for(Iterator<PowerDevice> itor = OtherTransformFDChangeMotherLine.map.keySet().iterator();itor.hasNext();){
					PowerDevice pd = itor.next();
					
					if(pd == null){
						return null;
					}
					
					List<PowerDevice> gyckgList = RuleExeUtil.getTransformerSwitchHigh(pd);
					
					for (Iterator<PowerDevice> it2 = gyckgList.iterator(); it2.hasNext();) {
						PowerDevice dev2 = it2.next();
						
						if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							it2.remove();
						}
					}
					
					if(gyckgList.size()>0){
						List<PowerDevice> gyckgdzList = RuleExeUtil.getDeviceDirectList(gyckgList.get(0), SystemConstants.SwitchSeparate);
						
						if(gyckgdzList.size()>0){
							String sormxName = "";
							String tagmxName = "";
							
							for(PowerDevice dev : gyckgdzList){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);

									if(mxList.size()>0){
										tagmxName = CZPService.getService().getDevName(mxList.get(0));
									}
								}if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);

									if(mxList.size()>0){
										sormxName = CZPService.getService().getDevName(mxList.get(0));
									}
								}
							}
							
							if(!sormxName.equals("")&&!tagmxName.equals("")){
								replaceStr += "将"+CZPService.getService().getDevName(gyckgList.get(0))+"由"+sormxName+"运行倒至"+tagmxName+"运行/r/n";
							}
							
						}
					}
					
					List<PowerDevice> zyckgList = RuleExeUtil.getTransformerSwitchMiddle(pd);

					if(zyckgList.size()>0){
						List<PowerDevice> zyckgdzList = RuleExeUtil.getDeviceDirectList(zyckgList.get(0), SystemConstants.SwitchSeparate);

						if(zyckgdzList.size()>0){
							String sormxName = "";
							String tagmxName = "";
							
							for(PowerDevice dev : zyckgdzList){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);

									if(mxList.size()>0){
										tagmxName = CZPService.getService().getDevName(mxList.get(0));
									}
								}if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);

									if(mxList.size()>0){
										sormxName = CZPService.getService().getDevName(mxList.get(0));
									}
								}
							}
							
							if(!sormxName.equals("")&&!tagmxName.equals("")){
								replaceStr += "将"+CZPService.getService().getDevName(zyckgList.get(0))+"由"+sormxName+"运行倒至"+tagmxName+"运行/r/n";
							}
						}
					}
				
					List<PowerDevice> dyckgList = RuleExeUtil.getTransformerSwitchLow(pd);
					List<PowerDevice> dyckgdzList = RuleExeUtil.getDeviceDirectList(dyckgList.get(0), SystemConstants.SwitchSeparate);
					
					
					if(dyckgdzList.size()>0){
						String sormxName = "";
						String tagmxName = "";
						
						for(PowerDevice dev : dyckgdzList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
								List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);

								if(mxList.size()>0){
									tagmxName = CZPService.getService().getDevName(mxList.get(0));
								}
							}if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);

								if(mxList.size()>0){
									sormxName = CZPService.getService().getDevName(mxList.get(0));
								}
							}
						}
						
						if(!sormxName.equals("")&&!tagmxName.equals("")){
							replaceStr += "将"+CZPService.getService().getDevName(dyckgList.get(0))+"由"+sormxName+"运行倒至"+tagmxName+"运行/r/n";
						}
					}
					
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		return replaceStr;
	}


}