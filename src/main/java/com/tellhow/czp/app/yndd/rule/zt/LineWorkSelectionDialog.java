package com.tellhow.czp.app.yndd.rule.zt;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprulepw.PWSystemConstants;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class LineWorkSelectionDialog extends JDialog {
    private final JTable deviceTable;
	private boolean isCancel = true;
    public static List<Map<String,String>> deviceList = new ArrayList<Map<String, String>>();

    public LineWorkSelectionDialog(Frame parent, boolean modal,List<PowerDevice> lineList,String lineName) {
        super(parent, "请选择线路是否开展过工作", modal);
        deviceList.clear();
        getContentPane().setLayout(new BorderLayout());
        deviceTable = new JTable();
        
        JTableHeader header = deviceTable.getTableHeader();
        header.setFont(new Font("微软雅黑",Font.PLAIN,24));             //字体
        header.setPreferredSize(new Dimension(header.getWidth(),30));
        String[] columnNames = {"选择结果","核实单位","线路名称"};
        
        final DefaultTableModel tableModel = new DefaultTableModel(null,columnNames) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                return super.getColumnClass(columnIndex);
            }
        };
        
        CodeNameModel codeNameModel1 = new CodeNameModel("0","");
        tableModel.addRow(new Object[]{codeNameModel1,"输电管理所",lineName}); // 默认全选
        
        // 创建初始状态和目标状态的下拉框选项
        for (int i = 0 ; i < lineList.size() ; i++) {
        	PowerDevice device = lineList.get(i);
        	
        	PowerDevice station = CBSystemConstants.getPowerStation(device.getPowerStationID());
        	String stationName = CZPService.getService().getDevName(station);
        	String deviceName = CZPService.getService().getDevName(device);

            CodeNameModel codeNameModel = new CodeNameModel("0","");
            tableModel.addRow(new Object[]{codeNameModel,stationName,deviceName}); // 默认全选
        }
        
        CodeNameModel codeNameModel2 = new CodeNameModel("0","");
        tableModel.addRow(new Object[]{codeNameModel2,"直流融冰工作组",lineName}); // 默认全选
        
        // 创建表格
        deviceTable.setModel(tableModel);
        deviceTable.getColumnModel().getColumn(0).setMinWidth(100); // 设置选择列的宽度
        deviceTable.getColumnModel().getColumn(0).setMaxWidth(120); // 设置选择列的宽度
        deviceTable.getColumnModel().getColumn(0).setPreferredWidth(100);

        deviceTable.setRowHeight(26);
        // 创建初始状态和目标状态的单元格渲染器
        DefaultTableCellRenderer renderer = new DefaultTableCellRenderer();
        renderer.setHorizontalAlignment(SwingConstants.CENTER); // 居中显示
        
        DefaultComboBoxModel comboBoxModel = createDefaultComboBoxModel();
        // 创建初始状态下拉框编辑器
        JComboBox statusComboBox = new JComboBox(comboBoxModel);
        DefaultCellEditor editor = new DefaultCellEditor(statusComboBox);

        deviceTable.getColumnModel().getColumn(0).setCellEditor(editor);
        
        JScrollPane scrollPane = new JScrollPane(deviceTable);
        getContentPane().add(scrollPane, BorderLayout.CENTER);

        JPanel topPanel = new JPanel();
        getContentPane().add(topPanel, BorderLayout.NORTH);
        topPanel.setLayout(new BorderLayout(0, 0));

        // 创建确定按钮
        JButton okButton = new JButton("确定");
        okButton.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
        
        topPanel.add(okButton,BorderLayout.EAST);
        okButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
            	if (deviceTable.isEditing())
            		deviceTable.getCellEditor().stopCellEditing();
            	
            	isCancel = false;
                // 获取选中的设备
                DefaultTableModel model = (DefaultTableModel) deviceTable.getModel();
                for (int i = 0; i < model.getRowCount(); i++) {
                     String result = ((CodeNameModel) model.getValueAt(i,0)).getName();
                     String stationName = (String) model.getValueAt(i,1);
                     
                     if(!result.equals("")){
                    	Map<String, String> deviceMap = new HashMap<String, String>();
                     	deviceMap.put("result",result);
                     	deviceMap.put("stationName",stationName);
                     	deviceList.add(deviceMap);
                     }
                }
                dispose();
            }
        });
        int maxVisibleButtons = 10; // 最多显示的按钮数量
        int actualHeight = Math.min(lineList.size()+2, maxVisibleButtons) * 24 + 50; // 根据按钮数量计算实际的界面高度,+5避免出现滚动条影响美观
        scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setPreferredSize(new Dimension(400, actualHeight)); // 应用实际的界面高度
        pack();
        setLocationRelativeTo(null);    //居中显示
        setVisible(true);
    }
    
    private DefaultComboBoxModel createDefaultComboBoxModel(){
        DefaultComboBoxModel comboBoxModel = new DefaultComboBoxModel();
        CodeNameModel codeNameModel = new CodeNameModel("0","");
        CodeNameModel codeNameModel1 = new CodeNameModel("1","是");
        CodeNameModel codeNameModel2 = new CodeNameModel("2","否");

        comboBoxModel.addElement(codeNameModel);
        comboBoxModel.addElement(codeNameModel1);
        comboBoxModel.addElement(codeNameModel2);
        return comboBoxModel;
    }

    public static List<Map<String,String>> getDeviceList() {
        return deviceList;
    }
    
    public boolean isCancel() {
		return isCancel;
	}
}

