package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrXLHHDD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("线路合环调电".equals(tempStr)){
			
			PowerDevice onswDevice = null;
			PowerDevice offswDevice = null;
			
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				if(dtd.getTransDevice().getDeviceType().equals(SystemConstants.Switch)){
					if(dtd.getBeginstatus().equals("1")&&dtd.getEndstate().equals("0")){
						onswDevice=dtd.getTransDevice();
					}else if(dtd.getBeginstatus().equals("0")&&dtd.getEndstate().equals("1")){
						offswDevice=dtd.getTransDevice();
					}
				}
			}
			
			
			if(onswDevice!=null&&offswDevice!=null){
				if(RuleExeUtil.getDeviceBeginStatus(onswDevice).equals("2")){
					String mbmx = "";
					if(onswDevice.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(onswDevice, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother, false,false, true, true);
						if(mxList.size()>0){
							mbmx = "上"+CZPService.getService().getDevName(mxList.get(0));
						}
					}
					replaceStr+=CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(onswDevice.getPowerStationID()))
							+"@将"+CZPService.getService().getDevName(onswDevice)+"由冷备用转热备用"+mbmx+"\r\n";
				}
				
				replaceStr+=CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(onswDevice.getPowerStationID()))
						+"@落实"+CZPService.getService().getDevName(curDev)+"线路保护及重合闸已正确投入\r\n";
				
				replaceStr+="云南省调@落实"
						+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(onswDevice.getPowerStationID()))
						+"220kV母线与"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(offswDevice.getPowerStationID()))+"220kV母线为同期系统\r\n";
				replaceStr+="昆明地调@遥控合上"
						+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(onswDevice.getPowerStationID()))
						+CZPService.getService().getDevName(onswDevice)+"\r\n";
				replaceStr+="昆明地调@遥控断开"
						+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(offswDevice.getPowerStationID()))
						+CZPService.getService().getDevName(offswDevice)+"\r\n";
				if(offswDevice.getDeviceStatus().equals("2")){
					replaceStr+=CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(offswDevice.getPowerStationID()))
							+"@将"+CZPService.getService().getDevName(offswDevice)+"由热备用转冷备用\r\n";
				}
			}
	    }
		return replaceStr;
	}

}
