package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.*;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHSMJXMXFD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station);
		String deviceName = CZPService.getService().getDevName(curDev);
		if("红河双母接线母线复电".equals(tempStr)){

			List<PowerDevice> adjacentBuses = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine,
					SystemConstants.Switch, true, true, true);
			adjacentBuses.add(stationDev);
			RuleExeUtil.swapDeviceList(adjacentBuses);
			List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
			Map<PowerDevice, String> deviceToBusMap = new HashMap<PowerDevice, String>();	// 设备及其所属母线
			for (PowerDevice bus : adjacentBuses) {
				String busName = CZPService.getService().getDevName(bus);
				List<PowerDevice> tempList = RuleExeUtil.getDeviceList(bus, SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "",
						false, true, true, true);
				for (PowerDevice dev : tempList) {
					deviceToBusMap.put(dev, busName);
				}
				xlkgList.addAll(tempList);
			}

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "",
					false, true, false, true);
			List<PowerDevice> plkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL +","+ CBSystemConstants.RunTypeSwitchMLPL,
					"", false, true, true, true);
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate,
					SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifePT, CBSystemConstants.RunTypeSideMother,
					true, true, true, true);

			List<PowerDevice> othermxList = new ArrayList<PowerDevice>();
			for (PowerDevice mlkg : mlkgList) {
				// 只取母联开关
				if (RuleExeUtil.isSwitchDoubleML(mlkg)) {
					List<PowerDevice> tempOtherMxList = czprule.rule.operationclass.RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine,
							SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother,
							false, true, true, true);
					tempOtherMxList.remove(stationDev);
					for (PowerDevice otherMx : tempOtherMxList) {
						List<PowerDevice> tempAdjBusList = CommonFunctionHH.isTripleBusZeroBranch(otherMx);
						if (tempAdjBusList != null && !new HashSet<PowerDevice>(tempOtherMxList).containsAll(tempAdjBusList)) {
							othermxList.addAll(tempAdjBusList);
						}
					}othermxList.addAll(tempOtherMxList);
				}
			}
			// 如果即有母联开关又有分段开关，则认为是双母双分段类型
			boolean isSmSfd = CommonFunctionHH.isDoubleBusDoubleBranch(mlkgList); // 双母双分段

			List<PowerDevice> zbkgList;
            List<PowerDevice> mxList = new ArrayList<PowerDevice>(othermxList);
			mxList.addAll(adjacentBuses);
			RuleExeUtil.swapDeviceList(mxList);

			//电源侧
			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "",
						false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "",
						false, true, true, true);
			}
			// 统一获取一遍设备的名称，存入map中，避免后续反复获取
			HashMap<PowerDevice, String> devNameMap = new HashMap<PowerDevice, String>();

			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			CommonFunctionHH.categorizeSwitch(xlkgList, yxkgList, rbykgList, devNameMap);
			CommonFunctionHH.categorizeSwitch(zbkgList, yxkgList, rbykgList, devNameMap);
			CommonFunctionHH.categorizeSwitch(plkgList, yxkgList, rbykgList, devNameMap);

			RuleExeUtil.swapDeviceList(yxkgList);
			RuleExeUtil.swapDeviceList(rbykgList);

			for (PowerDevice mlkg : mlkgList) {
				CommonFunctionHH.getDeviceNameAndStoreInMap(mlkg, devNameMap);
			}
			Map<PowerDevice, List<PowerDevice>> busToSwitchMap = new HashMap<PowerDevice, List<PowerDevice>>();
			for (PowerDevice othermx : othermxList) {
				CommonFunctionHH.getDeviceNameAndStoreInMap(othermx, devNameMap);
				List<PowerDevice> swList = RuleExeUtil.getDeviceList(othermx, SystemConstants.Switch,
						SystemConstants.PowerTransformer, true, true, true);
				busToSwitchMap.put(othermx, swList);}
			
			List<PowerDevice> ptdzList = new ArrayList<PowerDevice>();
			
			for(PowerDevice qtdz : qtdzList){
				if(RuleExeUtil.getDeviceEndStatus(qtdz).equals("0")){
					ptdzList.add(qtdz);
				}
			}

			ReplaceStrHHMXMC replaceStrHHMXMC = new ReplaceStrHHMXMC();
			String currentMxName = replaceStrHHMXMC.strReplace("红河母线名称", curDev, stationDev, desc);
			if (currentMxName.isEmpty()) currentMxName = deviceName;
			if (CommonFunctionHH.isTripleBusZeroBranch(stationDev) != null) {
				replaceStr += stationName+"@确认"+currentMxName+"相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，" +
						"现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），"
						+currentMxName+"的二次装置已正常投入，现"+currentMxName+"冷备用，具备送电条件/r/n";
			} else {
				replaceStr += stationName+"@确认"+currentMxName+"相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，" +
						"现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），"
						+(int)curDev.getPowerVoltGrade()+"kV母线的二次装置已正常投入，现"+currentMxName+"冷备用，具备送电条件/r/n";
			}

			if(!ptdzList.isEmpty()){
				replaceStr += CommonFunctionHH.getKnifeOnContent(ptdzList, stationName);
				replaceStr += stationName+"@确认"+deviceName+"TV二次侧空气开关处合上位置/r/n";
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				replaceStr += "红河地调@执行"+stationName+currentMxName+"由热备用转运行程序操作/r/n";
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += "红河地调@执行"+stationName+currentMxName+"由冷备用转运行程序操作/r/n";
			}
			
			for(PowerDevice dev : mlkgList){
				if (isSmSfd && !RuleExeUtil.isSwitchDoubleML(dev)) continue;
				replaceStr += stationName+"@确认"+devNameMap.get(dev)+"操作电源已断开，"
						+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程要求执行，互联压板已投入，具备倒母线操作条件/r/n";
			}

			String originalMxName = deviceName;	// 初始母线名称
			for(PowerDevice dev : othermxList){
				for(PowerDevice yxkg : yxkgList){
					if (!busToSwitchMap.get(dev).contains(yxkg)) continue;
                    if (CommonFunctionHH.ifSwitchSeparateControl(yxkg) && CommonFunctionHH.isTransSwitch(yxkg)) {
						if (deviceToBusMap.containsKey(yxkg)) {
							originalMxName = deviceToBusMap.get(yxkg);
						} else {
							originalMxName = deviceName;
						}
                        replaceStr += "红河地调@执行" + stationName + devNameMap.get(yxkg) + "由" + devNameMap.get(dev) + "运行倒至" + originalMxName + "运行程序操作/r/n";
                    }
				}
			}
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice yxkg : yxkgList){
					if (!busToSwitchMap.get(dev).contains(yxkg)) continue;
                    if (CommonFunctionHH.ifSwitchSeparateControl(yxkg) && !CommonFunctionHH.isTransSwitch(yxkg)) {
						if (deviceToBusMap.containsKey(yxkg)) {
							originalMxName = deviceToBusMap.get(yxkg);
						} else {
							originalMxName = deviceName;
						}
                        replaceStr += "红河地调@执行" + stationName + devNameMap.get(yxkg) + "由" + devNameMap.get(dev) + "运行倒至" + originalMxName + "运行程序操作/r/n";
                    }
				}
			}
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice yxkg : yxkgList){
					if (!busToSwitchMap.get(dev).contains(yxkg)) continue;
                    if (!CommonFunctionHH.ifSwitchSeparateControl(yxkg) && CommonFunctionHH.isTransSwitch(yxkg)) {
						if (deviceToBusMap.containsKey(yxkg)) {
							originalMxName = deviceToBusMap.get(yxkg);
						} else {
							originalMxName = deviceName;
						}
                        replaceStr += stationName + "@将" + devNameMap.get(yxkg) + "由" + devNameMap.get(dev) + "运行倒至" + originalMxName + "运行/r/n";
                    }
				}
			}
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice yxkg : yxkgList){
					if (!busToSwitchMap.get(dev).contains(yxkg)) continue;
                    if (!CommonFunctionHH.ifSwitchSeparateControl(yxkg) && !CommonFunctionHH.isTransSwitch(yxkg)) {
						if (deviceToBusMap.containsKey(yxkg)) {
							originalMxName = deviceToBusMap.get(yxkg);
						} else {
							originalMxName = deviceName;
						}
                        replaceStr += stationName + "@将" + devNameMap.get(yxkg) + "由" + devNameMap.get(dev) + "运行倒至" + originalMxName + "运行/r/n";
                    }
				}
			}
			
			for(PowerDevice dev : mlkgList){
                if (isSmSfd && !RuleExeUtil.isSwitchDoubleML(dev)) continue;
				replaceStr += stationName+"@确认"+devNameMap.get(dev)+"操作电源已投入，"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程要求执行，"
						+ "互联压板已退出，"+CZPService.getService().getDevName(mxList)+"相关一、二次设备运行正常/r/n";
			}
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice rbykg : rbykgList){
					if (!busToSwitchMap.get(dev).contains(rbykg)) continue;
					if (CommonFunctionHH.ifSwitchControl(rbykg) && CommonFunctionHH.ifSwitchSeparateControl(rbykg)) {
						if (deviceToBusMap.containsKey(rbykg)) {
							originalMxName = deviceToBusMap.get(rbykg);
						} else {
							originalMxName = deviceName;
						}
						replaceStr += "红河地调@执行" + stationName + devNameMap.get(rbykg) + "由" + devNameMap.get(dev) + "热备用倒至" + originalMxName + "热备用程序操作/r/n";
					}
				}
			}
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice rbykg : rbykgList){
					if (!busToSwitchMap.get(dev).contains(rbykg)) continue;
					if (deviceToBusMap.containsKey(rbykg)) {
						originalMxName = deviceToBusMap.get(rbykg);
					} else {
						originalMxName = deviceName;
					}
					if(CommonFunctionHH.ifSwitchControl(rbykg)&&CommonFunctionHH.ifSwitchSeparateControl(rbykg)){

					}else{
						replaceStr += stationName+"@将"+devNameMap.get(rbykg)+"由"+devNameMap.get(dev)+"热备用倒至"+originalMxName+"热备用/r/n";
					}
				}
			}
		} else if ("红河双母接线母线复电备注".equals(tempStr)) {
			PowerDevice other = RuleExeUtil.getMLDoubleOther(stationDev);
			ReplaceStrHHMXMC replaceStrHHMXMC = new ReplaceStrHHMXMC();
			String currentMxName = replaceStrHHMXMC.strReplace("红河母线名称", curDev, stationDev, desc);
			String otherMxName = replaceStrHHMXMC.strReplace("红河母线名称", other, stationDev, desc);
			replaceStr += "双母接线备注：1、操作前，核实" + stationName + currentMxName + "处冷备用状态；2、执行第13～24条指令过程中，" +
					"在合上" + currentMxName + "侧隔离开关形成设备双跨母线状态时，调控员需检查母联断路器三相电流情况，" +
					"确认母联断路器不存在明显三相电流不平衡情况后，方可拉开" + otherMxName + "侧隔离开关。";
		}
		return replaceStr;
	}
}
