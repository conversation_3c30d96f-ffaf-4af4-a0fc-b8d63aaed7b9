package com.tellhow.czp.app.yndd;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.service.CheckCzpImpl;
import com.tellhow.czp.service.CheckStatusImpl;
import com.tellhow.graphicframework.startup.StartupManager;

public class GetCheckImplTestCX {
    public static void main(String[] params) {
	    CheckCzpImpl check = new CheckCzpImpl();
	    CheckStatusImpl checkback = new CheckStatusImpl();
	
	    String param = "";
	
		StartupManager.startup();
		CZPService.getService().setArg(param);
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>楚雄地调</changzhan><caozuozhiling>遥控断开苍岭变220kV苍广牵Ⅱ回线255断路器。</caozuozhiling><cbid>81</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>楚雄地调</changzhan><caozuozhiling>执行苍岭变220kV苍广牵Ⅱ回线255断路器由热备用转冷备用程序操作。</caozuozhiling><cbid>8</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>楚雄地调</changzhan><caozuozhiling>执行苍岭变220kV苍广牵Ⅱ回线255断路器由冷备用转热备用于220kV Ⅲ母程序操作。</caozuozhiling><cbid>118</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		check.execute(param);
    }
}
