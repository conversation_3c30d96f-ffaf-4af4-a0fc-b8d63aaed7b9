package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBSMTCBZTKM implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("主变双母接线退出备自投昆明".equals(tempStr)){
			List<PowerDevice> middlekgList = new ArrayList<PowerDevice>();
			List<PowerDevice> lowkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> highkgList = new ArrayList<PowerDevice>();


			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&!CZPService.getService().getDevName(dev).contains("相")){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						if(dev.getPowerVoltGrade() == RuleExeUtil.getTransformerVolByType(curDev, "high")){
							highkgList.add(dev);
						}else if(dev.getPowerVoltGrade() == RuleExeUtil.getTransformerVolByType(curDev, "middle")){
							middlekgList.add(dev);
						}else if(dev.getPowerVoltGrade() == RuleExeUtil.getTransformerVolByType(curDev, "low")){
							lowkgList.add(dev);
						}
						
					}
				}
			}
			
			if(lowkgList.size()>1){
				for(PowerDevice pd : lowkgList){
					replaceStr += "退出"+CZPService.getService().getDevName(pd)+"备自投装置/r/n";
				}
			}else if(lowkgList.size() == 1){
				replaceStr += "退出"+(int)lowkgList.get(0).getPowerVoltGrade()+"kV备自投装置/r/n";
			}
			
			if(middlekgList.size()>1){
				for(PowerDevice pd : middlekgList){
					replaceStr += "退出"+CZPService.getService().getDevName(pd)+"备自投装置/r/n";
				}
			}else if(middlekgList.size() == 1){
				replaceStr += "退出"+(int)middlekgList.get(0).getPowerVoltGrade()+"kV备自投装置/r/n";
			}
			
			
//			if(highkgList.size()>1){
//				for(PowerDevice pd : highkgList){
//					replaceStr += "退出"+CZPService.getService().getDevName(pd)+"备自投装置/r/n";
//				}
//			}else if(highkgList.size() == 1){
//				replaceStr += "退出"+(int)highkgList.get(0).getPowerVoltGrade()+"kV备自投装置/r/n";
//			}
			
			if(replaceStr.length() == 0) {
				return null;
			}
	    }
		return replaceStr;
	}

}
