package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTKGHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通开关合环调电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			
			String filter = CZPImpl.getPropertyValue("ProtectiveDevice");
			String[] filterArr = filter.split(";");
			List<String> filterList =  Arrays.asList(filterArr);
			
			String filter2 = CZPImpl.getPropertyValue("TripExport");
			String[] filterArr2 = filter2.split(";");
			List<String> filterList2 =  Arrays.asList(filterArr2);
			
			List<PowerDevice> xlswList = new ArrayList<PowerDevice>();

			PowerDevice powerTransformer = new PowerDevice();
			PowerDevice powerdev = new PowerDevice();
			PowerDevice powercutdev = new PowerDevice();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				 if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					 powerTransformer = dev;
				 }
				
				 if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						xlswList.add(dev);
					}
				 }
				 
				 if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							powercutdev = dev;
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							powerdev = dev;
						}
					}
				 }
			}
			
			if(RuleUtil.isTransformerNQ(powerTransformer)){
				for(PowerDevice dev : xlswList){
					if(!filterList.contains(dev.getPowerDeviceID())){
						replaceStr += "投入"+CZPService.getService().getDevName(dev)+"总跳闸出口功能/r/n";
					}
				}
			}
			
			for(PowerDevice dev : xlswList){
				if(filterList2.contains(dev.getPowerDeviceID())){
					replaceStr += "投入"+CZPService.getService().getDevName(dev)+"总跳闸出口功能/r/n";
				}
			}
			
			replaceStr += CommonFunction.getHhContent(powerdev, "昭通地调", stationName);
			replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(powercutdev)+"/r/n";
			
			if(RuleUtil.isTransformerNQ(powerTransformer)){
				for(PowerDevice dev : xlswList){
					if(dev.getDeviceStatus().equals("0")){
						if(!filterList.contains(dev.getPowerDeviceID())){
							replaceStr += "退出"+CZPService.getService().getDevName(dev)+"总跳闸出口功能/r/n";
						}
					}
				}
			}
			
			
			for(PowerDevice dev : xlswList){
				if(filterList2.contains(dev.getPowerDeviceID())){
					replaceStr += "退出"+CZPService.getService().getDevName(dev)+"总跳闸出口功能/r/n";
				}
			}
			
			String filter3 = CZPImpl.getPropertyValue("HHProtectedLocation");
			
			String[] filterArr3 = filter3.split(";");
			
			List<String> filterList3 =  Arrays.asList(filterArr3);
			

			List<PowerDevice> tagkgList = new ArrayList<PowerDevice>();
			
			tagkgList.add(powerdev);
			tagkgList.add(powercutdev);

			for(Iterator<PowerDevice> itor = tagkgList .iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(!filterList3.contains(dev.getPowerDeviceID())){
					itor.remove();
				}
			}
			
			if(tagkgList.size()>0){
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, tagkgList , "请选择需要调整保护定值区的断路器：");
				List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

				for(PowerDevice dev : choosedyckgList){
					PowerDevice stationNew = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					
					replaceStr += CZPService.getService().getDevName(stationNew)+"@将"+CZPService.getService().getDevName(dev)+"保护定值区由0X区切换至0X区/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
