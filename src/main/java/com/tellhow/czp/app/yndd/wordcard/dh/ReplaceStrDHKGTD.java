package com.tellhow.czp.app.yndd.wordcard.dh;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDH;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDHKGTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("德宏开关停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			String begin = CBSystemConstants.getCurRBM().getBeginStatus();
			String end = CBSystemConstants.getCurRBM().getEndState();
			
			boolean ifSwitchControl = false;
			boolean ifSwitchSeparateControl = false;

			if(CommonFunctionDH.ifSwitchControl(curDev)){
				ifSwitchControl = true;
			}
			
			if(CommonFunctionDH.ifSwitchSeparateControl(curDev)){
				ifSwitchSeparateControl = true;
			}
			
			if(begin.equals("0")){
				if(end.equals("1")){
					replaceStr += CommonFunctionDH.getSwitchOffContent(curDev, stationName, station);
				}else if(end.equals("2")){
					if(ifSwitchControl && ifSwitchSeparateControl){
						replaceStr += "德宏地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
					}else{
						replaceStr += CommonFunctionDH.getSwitchOffContent(curDev, stationName, station);
						
						if(ifSwitchSeparateControl){
							replaceStr += "德宏地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
						}else{
							replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用";
						}
					}
				}
			}else if(begin.equals("1")){
				if(end.equals("0")){
					replaceStr += CommonFunctionDH.getSwitchOnContent(curDev, stationName, station);
				}else if(end.equals("2")){
					if(ifSwitchSeparateControl){
						replaceStr += "德宏地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					}else{
						replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用";
					}
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
