package com.tellhow.czp.app.yndd.view;

import java.awt.Dimension;
import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JOptionPane;
import javax.swing.JTable;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumn;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.mainframe.EachRowEditor;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.ColorTableModel;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.view.InitDeviceTypeChockBox;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013-7-11 上午11:38:02 
 */
public class EquipStatusAndMxChoose extends javax.swing.JDialog {
	private List<PowerDevice> equipList = new ArrayList<PowerDevice>();
	private List<Map<String,String>> tagStatusList = new ArrayList<Map<String,String>>();
//	private List<String> expStatusList;
	private boolean isCancel = true;
	private String devicetype = "";
	private List<String> defaultStatusList;

	public EquipStatusAndMxChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, List<String> defaultStatusList, String showMessage,String equiptype) {
		super(parent, modal);
		this.defaultStatusList = defaultStatusList;
		this.devicetype = equiptype;
		if (equipsList != null)
			this.equipList = equipsList;
		initComponents();
		this.jLabel1.setText(showMessage);
		
		this.initTable(new ArrayList<String>());
		FitTableColumns(jTable1);
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	public EquipStatusAndMxChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, List<String> defaultStatusList,List<String> expStatusList, String showMessage,String equiptype) {
		super(parent, modal);
		this.defaultStatusList = defaultStatusList;
		this.devicetype = equiptype;

		if (equipsList != null)
			this.equipList = equipsList;
		initComponents();
		this.jLabel1.setText(showMessage);
		
		this.initTable(expStatusList);
		FitTableColumns(jTable1);
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

    private void initComponents() {
        jLabel1 = new javax.swing.JLabel();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        jButton1 = new javax.swing.JButton();
        jButton2 = new javax.swing.JButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

        jLabel1.setText("jLabel1");

        jScrollPane1.setViewportView(jTable1);

        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/ok.png"))); // NOI18N
        jButton1.setToolTipText("确定");
        jButton1.setText("确定");
        jButton1.setMargin(new java.awt.Insets(1,1,1,1));
        jButton1.setFocusPainted(false);
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
        jButton2.setToolTipText("取消");
        jButton2.setText("取消");
        jButton2.setMargin(new java.awt.Insets(1,1,1,1));
        jButton2.setFocusPainted(false);
        jButton2.setVisible(false);
        jButton2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jLabel1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 800, Short.MAX_VALUE)
            .add(layout.createSequentialGroup()
                .addContainerGap(org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .add(jButton1)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jButton2)
                .add(5, 5, 5))
            .add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 650, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .add(jLabel1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 24, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED, 22, Short.MAX_VALUE)
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.TRAILING)
                    .add(jButton2)
                    .add(jButton1))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jScrollPane1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 120+equipList.size()*15, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
        );

        pack();
    }// </editor-fold>

    private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		this.setVisible(false);
		this.dispose();
    }


	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		isCancel = false;
		
		for(int i = 0; i < jtablemodel.getRowCount(); i++) {
			CodeNameModel cnm = (CodeNameModel)jtablemodel.getValueAt(i, 1);
			PowerDevice pd = null;
			for (int j = 0; j < equipList.size(); j++) {
				 if(equipList.get(j).getPowerDeviceID().equals(cnm.getCode())) {
					 pd = equipList.get(j);
					 break;
				 }
			}
			
			Map<String, String> tagStatusMap = new HashMap<String, String>();
			
			if(devicetype.equals(SystemConstants.PowerTransformer)){
				String status = ((CodeNameModel)jtablemodel.getValueAt(i, 3)).getCode();
				String opkind = ((CodeNameModel)jtablemodel.getValueAt(i, 4)).getCode();
				String mxid = ((CodeNameModel)jtablemodel.getValueAt(i, 5)).getCode();

				if(status.equals("")) {
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "没有选择目标状态！", "提示", JOptionPane.WARNING_MESSAGE);
					return;
				}
				
				tagStatusMap.put("设备ID", pd.getPowerDeviceID());
				tagStatusMap.put("状态", status);
				tagStatusMap.put("操作类型", opkind);
				tagStatusMap.put("母线ID", mxid);
			}else if(devicetype.equals(SystemConstants.InOutLine)){
				String status = ((CodeNameModel)jtablemodel.getValueAt(i, 3)).getCode();
				String opkind = ((CodeNameModel)jtablemodel.getValueAt(i, 4)).getCode();
				String mxid = ((CodeNameModel)jtablemodel.getValueAt(i, 5)).getCode();

				if(status.equals("")) {
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "没有选择目标状态！", "提示", JOptionPane.WARNING_MESSAGE);
					return;
				}
				
				tagStatusMap.put("设备ID", pd.getPowerDeviceID());
				tagStatusMap.put("状态", status);
				tagStatusMap.put("操作类型", opkind);
				tagStatusMap.put("母线ID", mxid);
			}

			tagStatusList.add(tagStatusMap);
		}
		this.setVisible(false);
		this.dispose();
	}

	public void initTable(List<String> expStatusList){//expStatusList:下拉框排除的状态列表
		jtablemodel = new ColorTableModel(){
            @Override
            public boolean isCellEditable(int row, int column) {
            	if(column == 0||column == 1||column == 2){
            		return false; // 单元格都不可编辑
            	}else{
            		return true; 
            	}
            }
        };
        
		Vector<Object> rowData = new Vector<Object>();
		PowerDevice pd = null;
		
		if(devicetype.equals(SystemConstants.PowerTransformer)){
			jtablemodel.setRowTitle(new String[] { "厂站名称","设备名称","初始状态","目标状态","操作类型","目标母线名称"});
			jTable1.setModel(jtablemodel);
			jTable1.setRowHeight(30);
			
			EachRowEditor rowEditor = new EachRowEditor(jTable1);
		    EachRowEditor rowEditor2 = new EachRowEditor(jTable1);
		    EachRowEditor rowEditor3 = new EachRowEditor(jTable1);

		    for(int i = 0; i < equipList.size(); i++) {
				pd = equipList.get(i);
				
				String defaultStatus = defaultStatusList.get(i);
				Map tagStatusMap = null;
				CommonSearch cs=new CommonSearch();
				Map inPara = new HashMap();
				Map outPara = new HashMap();
				inPara.clear();
				outPara.clear();;
				inPara.put("oprSrcDevice", pd);
				inPara.put("tagDevType", SystemConstants.SwitchSeparate);
				inPara.put("excDevType", SystemConstants.PowerTransformer);
				inPara.put("isSearchOffPath", true);
				inPara.put("isStopOnBusbarSection", true);
				inPara.put("isStopOnTagDevType", false);
				cs.execute(inPara, outPara);
				List<PowerDevice> devs =  (List<PowerDevice>) outPara.get("linkedDeviceList");
				
				if(expStatusList.size()==0){//没给默认排除状态的，按代码逻辑排除状态
					if(CBSystemConstants.getCurRBM().getBeginStatus().equals("3")) {
						if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) { //线路初始状态在检修，线路开关一般不能转检修
							expStatusList.add("3");
						}
					}else{
						if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {//复电母联开关默认热备用
							defaultStatus="1";
						}
					}
				}
				
				List<PowerDevice> motherLineList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				
				JComboBox cb = new JComboBox(InitDeviceTypeChockBox.getDeviceStatusCheckBox(pd.getDeviceType(),expStatusList,defaultStatus));
				
				boolean isDoubleMotherLine = false;
				
				for(PowerDevice motherLine : motherLineList){
					if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(motherLine.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						isDoubleMotherLine = true;
					}
				}
				}
				
				if(isDoubleMotherLine){
					JComboBox mxcb = new JComboBox();
					
					DefaultComboBoxModel model = new DefaultComboBoxModel();
					
					for(PowerDevice mx : motherLineList){
						CodeNameModel cnm = new CodeNameModel(mx.getPowerDeviceID(),CZPService.getService().getDevName(mx));
						model.addElement(cnm);
					}
					
					CodeNameModel cnm = new CodeNameModel("","");
					model.addElement(cnm);
					
					mxcb.setModel(model);
					
					JComboBox opcb = new JComboBox();
					
					DefaultComboBoxModel model2 = new DefaultComboBoxModel();
					
					CodeNameModel cnm1 = new CodeNameModel("","");
					model2.addElement(cnm1);
					
					CodeNameModel cnm2 = new CodeNameModel("充电","充电");
					model2.addElement(cnm2);
					
					CodeNameModel cnm3 = new CodeNameModel("合环","合环");
					model2.addElement(cnm3);
					
					opcb.setModel(model2);
					
					rowEditor.setEditorAt(i, new DefaultCellEditor(cb));
					rowEditor2.setEditorAt(i, new DefaultCellEditor(mxcb));
					rowEditor3.setEditorAt(i, new DefaultCellEditor(opcb));

					rowData.add(new Object[] { CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pd.getPowerStationID())), new CodeNameModel(pd.getPowerDeviceID(),CZPService.getService().getDevName(pd)),RuleExeUtil.getStatus(RuleExeUtil.getDeviceBeginStatusContainNotOperate(pd)), cb.getSelectedItem(),opcb.getSelectedItem() ,mxcb.getSelectedItem()});
				}else{
					JComboBox mxcb = new JComboBox();
					
					DefaultComboBoxModel model = new DefaultComboBoxModel();
					
					CodeNameModel cnm = new CodeNameModel("","");
					model.addElement(cnm);
					
					mxcb.setModel(model);
					
					JComboBox opcb = new JComboBox();
					
					DefaultComboBoxModel model2 = new DefaultComboBoxModel();
					
					CodeNameModel cnm1 = new CodeNameModel("","");
					model2.addElement(cnm1);
					
					CodeNameModel cnm2 = new CodeNameModel("充电","充电");
					model2.addElement(cnm2);
					
					CodeNameModel cnm3 = new CodeNameModel("合环","合环");
					model2.addElement(cnm3);
					
					opcb.setModel(model2);
					
					rowEditor.setEditorAt(i, new DefaultCellEditor(cb));
					rowEditor2.setEditorAt(i, new DefaultCellEditor(mxcb));
					rowEditor3.setEditorAt(i, new DefaultCellEditor(opcb));

					rowData.add(new Object[] { CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pd.getPowerStationID())), new CodeNameModel(pd.getPowerDeviceID(),CZPService.getService().getDevName(pd)),RuleExeUtil.getStatus(RuleExeUtil.getDeviceBeginStatusContainNotOperate(pd)), cb.getSelectedItem(),opcb.getSelectedItem(),mxcb.getSelectedItem()});
				}
			}
		    
		    jTable1.getColumnModel().getColumn(3).setCellEditor(rowEditor);
		    jTable1.getColumnModel().getColumn(4).setCellEditor(rowEditor3);
		    jTable1.getColumnModel().getColumn(5).setCellEditor(rowEditor2);

		}else if(devicetype.equals(SystemConstants.InOutLine)){
			jtablemodel.setRowTitle(new String[] { "厂站名称", "设备名称","初始状态", "目标状态","操作类型","目标母线名称"});
			jTable1.setModel(jtablemodel);
			jTable1.setRowHeight(30);

		    EachRowEditor rowEditor = new EachRowEditor(jTable1);
		    EachRowEditor rowEditor2 = new EachRowEditor(jTable1);
		    EachRowEditor rowEditor3 = new EachRowEditor(jTable1);

		    for (int i = 0; i < equipList.size(); i++) {
				pd = equipList.get(i);
				
				String defaultStatus = defaultStatusList.get(i);
				Map tagStatusMap = null;
				CommonSearch cs=new CommonSearch();
				Map inPara = new HashMap();
				Map outPara = new HashMap();
				inPara.clear();
				outPara.clear();;
				inPara.put("oprSrcDevice", pd);
				inPara.put("tagDevType", SystemConstants.SwitchSeparate);
				inPara.put("excDevType", SystemConstants.PowerTransformer);
				inPara.put("isSearchOffPath", true);
				inPara.put("isStopOnBusbarSection", true);
				inPara.put("isStopOnTagDevType", false);
				cs.execute(inPara, outPara);
				List<PowerDevice> devs =  (List<PowerDevice>) outPara.get("linkedDeviceList");
				
				if(expStatusList.size()==0){//没给默认排除状态的，按代码逻辑排除状态
					if(CBSystemConstants.getCurRBM().getBeginStatus().equals("3")) {
						if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) { //线路初始状态在检修，线路开关一般不能转检修
							expStatusList.add("3");
						}
					}else{
						if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {//复电母联开关默认热备用
							defaultStatus="1";
						}
					}
				}
				
				List<PowerDevice> motherLineList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				
				JComboBox cb = new JComboBox(InitDeviceTypeChockBox.getDeviceStatusCheckBox(pd.getDeviceType(),expStatusList,defaultStatus));
				
				boolean isDoubleMotherLine = false;
				
				for(PowerDevice motherLine : motherLineList){
					if(motherLine.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						isDoubleMotherLine = true;
					}
				}
				
				if(isDoubleMotherLine){
					JComboBox mxcb = new JComboBox();
					
					DefaultComboBoxModel model = new DefaultComboBoxModel();
					
					for(PowerDevice mx : motherLineList){
						CodeNameModel cnm = new CodeNameModel(mx.getPowerDeviceID(),CZPService.getService().getDevName(mx));
						model.addElement(cnm);
					}
					
					CodeNameModel cnm = new CodeNameModel("","");
					model.addElement(cnm);
					model.setSelectedItem(cnm);
					mxcb.setModel(model);
					
					DefaultComboBoxModel model2 = new DefaultComboBoxModel();
					JComboBox opcb = new JComboBox();

					CodeNameModel cnm1 = new CodeNameModel("","");
					model2.addElement(cnm1);
					
					CodeNameModel cnm2 = new CodeNameModel("充电","充电");
					model2.addElement(cnm2);
					
					CodeNameModel cnm3 = new CodeNameModel("合环","合环");
					model2.addElement(cnm3);
					
					opcb.setModel(model2);
					
					rowEditor.setEditorAt(i, new DefaultCellEditor(cb));
					rowEditor2.setEditorAt(i, new DefaultCellEditor(mxcb));
					rowEditor3.setEditorAt(i, new DefaultCellEditor(opcb));

					rowData.add(new Object[] { CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pd.getPowerStationID())), new CodeNameModel(pd.getPowerDeviceID(),CZPService.getService().getDevName(pd)),RuleExeUtil.getStatus(RuleExeUtil.getDeviceBeginStatusContainNotOperate(pd)), cb.getSelectedItem() ,opcb.getSelectedItem(),mxcb.getSelectedItem()});
				}else{
					JComboBox mxcb = new JComboBox();
					
					DefaultComboBoxModel model = new DefaultComboBoxModel();
					
					CodeNameModel cnm = new CodeNameModel("","");
					model.addElement(cnm);
					
					mxcb.setModel(model);
					
					DefaultComboBoxModel model2 = new DefaultComboBoxModel();

					JComboBox opcb = new JComboBox();

					CodeNameModel cnm0 = new CodeNameModel("","");
					model2.addElement(cnm0);
					
					CodeNameModel cnm1 = new CodeNameModel("充电","充电");
					model2.addElement(cnm1);
					
					CodeNameModel cnm2 = new CodeNameModel("合环","合环");
					model2.addElement(cnm2);
					
					opcb.setModel(model2);
					
					rowEditor.setEditorAt(i, new DefaultCellEditor(cb));
					rowEditor2.setEditorAt(i, new DefaultCellEditor(mxcb));
					rowEditor3.setEditorAt(i, new DefaultCellEditor(opcb));

					rowData.add(new Object[] { CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pd.getPowerStationID())), new CodeNameModel(pd.getPowerDeviceID(),CZPService.getService().getDevName(pd)),RuleExeUtil.getStatus(RuleExeUtil.getDeviceBeginStatusContainNotOperate(pd)), cb.getSelectedItem() ,opcb.getSelectedItem(),mxcb.getSelectedItem()});
				}
			}
		    
		    jTable1.getColumnModel().getColumn(3).setCellEditor(rowEditor);
		    jTable1.getColumnModel().getColumn(4).setCellEditor(rowEditor3);
		    jTable1.getColumnModel().getColumn(5).setCellEditor(rowEditor2);
		}
		
	    jtablemodel.setRowData(rowData);
	}

	public List<Map<String,String>> getTagStatusList() {
		return tagStatusList;
	}

	public boolean isCancel() {
		return isCancel;
	}
	
    // Variables declaration - do not modify
    private javax.swing.JButton jButton1;
    private javax.swing.JButton jButton2;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTable jTable1;
    // End of variables declaration

	private ColorTableModel jtablemodel;
	
	//Jtable列宽自适应
	public void FitTableColumns(JTable myTable){  
	    JTableHeader header = myTable.getTableHeader();  
	     int rowCount = myTable.getRowCount();  
	  
	     Enumeration columns = myTable.getColumnModel().getColumns();  
	     while(columns.hasMoreElements()){  
	         TableColumn column = (TableColumn)columns.nextElement();  
	         int col = header.getColumnModel().getColumnIndex(column.getIdentifier());  
	         int width = (int)myTable.getTableHeader().getDefaultRenderer()  
	                 .getTableCellRendererComponent(myTable, column.getIdentifier()  
	                         , false, false, -1, col).getPreferredSize().getWidth();  
	         for(int row = 0; row<rowCount; row++){  
	             int preferedWidth = (int)myTable.getCellRenderer(row, col).getTableCellRendererComponent(myTable,  
	               myTable.getValueAt(row, col), false, false, row, col).getPreferredSize().getWidth();  
	             width = Math.max(width, preferedWidth)+10;  
	         }  
	         header.setResizingColumn(column); // 此行很重要  
	         column.setWidth(width+myTable.getIntercellSpacing().width);  
	     }  
	}  
}
