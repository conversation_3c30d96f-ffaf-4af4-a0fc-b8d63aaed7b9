package com.tellhow.czp.app.yndd.rule;

import java.util.List;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

public class SwitchThreeSwitchSeparateExecute implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dev : dzList){
				List<PowerDevice> threedzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

				for(PowerDevice threedz : threedzList){
					RuleExeUtil.deviceStatusExecute(threedz, threedz.getDeviceStatus(), rbm.getEndState());
				}
			}
		}
		
		return true;
	}
}
