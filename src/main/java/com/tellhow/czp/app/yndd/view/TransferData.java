package com.tellhow.czp.app.yndd.view;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class TransferData{
	static Connection conn = DBManager.getConnection();
//	static final String[] strNums = {"一","二","三","四","五","六","七","八","九"};
	static final String strNums = "一二三四五六七八九";
	Statement statement;
	ResultSet crs;
	static Statement state;
	private ResultSet rs;
	/**
	 * 逐项票导入
	 */
	public void transZX(){
		String zbid;
		try{
			statement = conn.createStatement();
			String zbidsql = "select t.FXZXPZBID as zbid from poweroperationcard.fxzxpzb t";
			rs = statement.executeQuery(zbidsql);
			while(rs.next()){
				zbid = String.valueOf(rs.getObject("zbid"));
				if(CheckZxMx(zbid)){
					//插入主表
					String zbsql = "insert into "+CBSystemConstants.opcardUser+"t_a_czpzb b " +
					"select 'z"+zbid+"',czrw,0,pjyj as DMISFLAG,0,1,kplx as BUILDKIND,lockflag as ISLOCK,isdxp as ISMODEL,'0',NPR,NPSJ,SHR,BH,SHSJ,0,'','',bz as bzsx" +
					" from poweroperationcard.fxzxpzb t where t.FXZXPZBID = '"+zbid+"'";
					DBManager.execute(zbsql);
				}
			}
		}catch (Exception e) {
		// TODO: handle exception
			e.printStackTrace();
		}
	}
	/**
	 * 检查逐项明细表
	 * @param zbid
	 * @return
	 * @throws SQLException
	 */
	public boolean CheckZxMx(String zbid) throws SQLException{
		boolean flag = false;
		boolean xflag = false;
		String sql = "select t.xh,t.czbh from poweroperationcard.fxzxpmx t where t.zbid ='"+zbid+"' order by t.xh";
		List list = DBManager.query(sql);
		Map<String,String> map = new HashMap();
		Map Umap = new HashMap();
		String czbh ="";
		for(int i=0,j=list.size();i<j;i++){
			map =(Map)list.get(i);
			if(map.get("CZBH")!=null){
				czbh=StringUtils.ObjToString(map.get("CZBH"));
			}
			//如果为中文数字则替换
			if(strNums.indexOf(czbh)>-1){
				czbh =String.valueOf(strNums.indexOf(czbh)+1);
//				System.out.println("问题序号"+zbid);
			}
/*			if(xflag){
				for(int s=0;0<strNums.length;s++){
					if(xh.equals(strNums[i])){
						xh = String.valueOf(s+1);
					}
				}
			}*/
			Umap.put(StringUtils.ObjToString(map.get("XH")),czbh);
			if(map.get("CZBH")!=null&&flag==false){
				flag = true;
			}
		}
		if(flag ==true){
			px(Umap,zbid);
		}
			
		return flag;
	}
	/**
	 * 插入从表
	 * @param map
	 * @param id
	 * @throws SQLException
	 */
	public void px(Map map,String id) throws SQLException{
		System.out.println("ID"+id);
		for(int i=0;i<=map.size();i++){
			if(i>0&&map.get(String.valueOf(i))==""){
				if( map.get(String.valueOf(i-1))!=null){
					map.put(String.valueOf(i), map.get(String.valueOf(i-1)));
				}else{
					int j = 1;
					while(map.get(String.valueOf(i-j))==null){
						j++;
						System.out.println("注意"+id);
					}
					map.put(String.valueOf(i), map.get(String.valueOf(i-j)));
				}
			}
//			System.out.println(map.get(String.valueOf(i)));
		}
		for(int i =1,j= map.size();i<=j;i++){
			String z = (String) map.get(String.valueOf(i));
			String sql = "insert into t_a_czpmx m " +
					"select guid,'','z"+id+"',czdw,czbz,xlr,xlsj,hbr,hbsj,'','',slr,xh,'"+z+"',czdw" +
					" from poweroperationcard.fxzxpmx t where t.zbid='"+id+"' and t.xh='"+i+"'";
			if(i==1){
//				delRepeat(id);
			}
			DBManager.execute(sql);
			
		}
	}
	/**
	 * 综合令
	 * @throws SQLException
	 */
	public void transZH() throws SQLException{
		state = conn.createStatement();
		String  sql;
		String insql;
		String zbid;
		int j;
		String zbidsql = "select t.ZHZXPZBID as zbid from poweroperationcard.ZHZXPZB t";
		rs = state.executeQuery(zbidsql);
		while(rs.next()){
			zbid = String.valueOf(rs.getObject("zbid"));
			sql = "select t.zbid,t.mxid from poweroperationcard.ZHZXPMX t where t.zbid ='"+zbid+"'";
			List list = DBManager.query(sql);
			System.out.println(zbid);
			Map<String,String> map = new HashMap();
			for(int i=0;i<list.size();i++){
				j=i+1;
				map = (Map<String, String>) list.get(i);
				String mxid =StringUtils.ObjToString(map.get("MXID"));
				insql = "insert into t_a_czpmx " +
						"select guid,'',zbid,'',czbz,'','','','','','','','"+j+
						"','"+j+"','' from poweroperationcard.ZHZXPMX t where zbid ='"+zbid+"'and mxid ='"+mxid+"'";
				if(i==0){
//					delRepeat(zbid);
				}
				DBManager.execute(insql);
			}
		}
		insql ="insert into "+CBSystemConstants.opcardUser+"t_a_czpzb " +
		"select  ZHZXPZBID as zbid," +
		"czrw,0,pjyj as DMISFLAG,0,0,KPFS as BUILDKIND," +
		"lockflag as ISLOCK,isdxp as ISMODEL," +
		"'0',NPR,NPSJ,SHR,BH,SHSJ,0,'','',bz as bzsx" +
		" from poweroperationcard.ZHZXPZB t";
		DBManager.execute(insql);
	}
	/**
	 * 删除重复（避免主键冲突,保留老系统）
	 * @param bid
	 */
	public void delRepeat(String bid){
	
				List list = DBManager.query("select t.mxid from "+CBSystemConstants.opcardUser+"t_a_czpmx t where t.F_ZBID ='"+bid+"'");
				if(list.size()>0){
					DBManager.execute("delete from t_a_czpmx t where t.F_ZBID ='"+bid+"'");
					
				}
				List list1 = DBManager.query("select t.ZBID from "+CBSystemConstants.opcardUser+"t_a_czpzb t where t.ZBID ='"+bid+"'");
				if(list1.size()>0){
					DBManager.execute("delete from t_a_czpzb t where t.ZBID ='"+bid+"'");
				}
	}
	/**
	 *防误
	 * @param args
	 * @throws SQLException
	 */
	public void clear(){
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"t_a_czpzb t");
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"t_a_czpmx t");
	}
	public static void main(String[] args) throws SQLException{
//		TransferData data = new TransferData();
//		data.clear();
//		data.transZH();
//		data.transZX();
		String insql = "insert into t_a_czpmx " +
		"select guid,'',zbid,'',czbz,'','','','','','','','1221','"+1221+"','' from poweroperationcard.ZHZXPMX t where zbid ='1221'and mxid ='1221'";
		System.out.println(insql);
		
	}
}
