/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票系统
 * 功能说明 : 构建一个线路树
 * 作    者 : 张余平
 * 开发日期 : 2008-07-16
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.app.yndd.tool;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeNode;
import javax.swing.tree.TreePath;

import org.apache.log4j.Logger;

import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.DBManager;


public class OperateTicketPubTreeGZ {
	private static Logger log = Logger.getLogger(OperateTicketPubTreeGZ.class);
    private HashMap codeMap = new HashMap();   //id 和 value 的映射
    private Integer colNum;      //树的层次 
    private Integer cols;   //数据库语句列数
    
    public OperateTicketPubTreeGZ(Integer colNum) {
    	this.colNum = colNum;
    }

    /**
     *作用：构建JTree
     *@param rootName 树名
     *@param connType 连接类型1（青海）2（江西）3（其他）
     *@param sql 树的数据源
     *@param colnum 树的级数
     *@param isSort 是否需要内部排序
     ***/
    public JTree buildLineTree(String rootName, List list, boolean isSort) {
        DefaultMutableTreeNode rootNode = new DefaultMutableTreeNode();   //创建根节点     
        Map<DefaultMutableTreeNode,DefaultMutableTreeNode> removeNodeMap = new HashMap<DefaultMutableTreeNode, DefaultMutableTreeNode>();//要移除的节点
        if(list != null) {
	        TreeMap treeMap = this.getTreeMap(list, isSort);
	        Object[] keyarray = treeMap.keySet().toArray();
	        HashMap nodeMap = new HashMap();       //存放树的节点 及对应序号 方便查询是否有父节点
	
	        for (int i = 0; i < keyarray.length; i++) {
	            String newKey = keyarray[i].toString();     //treeMap的序列码
	            String newID = treeMap.get(newKey).toString();  //如果叶节点是对象则是id   否则是Str

	            DefaultMutableTreeNode newNode;   //为每个序号新建一个树节点
	            if (colNum != cols) {
	                if (codeMap.get(newID) == null) {
	                    //非叶节点
	                    newNode = new DefaultMutableTreeNode(newID);
	                } else {
	                    //叶节点存放对象
	                    newNode = new DefaultMutableTreeNode(codeMap.get(newID));
	                }
	            } else {
					
	                newNode = new DefaultMutableTreeNode(newID);
	            }
	            String parentkey = newKey.substring(0, newKey.length() - 3);   //待查询父节点key   
	            if (nodeMap.get(parentkey) == null) {    //查询是否有父节点
	                rootNode.add(newNode);             //加入根节点
	            } else {
	                DefaultMutableTreeNode paramNode = (DefaultMutableTreeNode) nodeMap.get(parentkey);
	                if(paramNode.getUserObject().toString().equals("parentname")){
	                	DefaultMutableTreeNode paramNodeParent =  (DefaultMutableTreeNode)paramNode.getParent();
	                	paramNodeParent.add(newNode);
	                	removeNodeMap.put(paramNode, paramNodeParent);//记录要移除的节点
	                }else{
	                	paramNode.add(newNode);    //加入父节点
	                }
	            }
	            nodeMap.put(keyarray[i].toString(), newNode);
	        }
        }
        else {
        	for (Iterator it = SystemConstants.getMapSVGFile().values().iterator(); it.hasNext();) {
   				SVGFile svgFile = (SVGFile)it.next();
   				DefaultSimpleNode dsn = new DefaultSimpleNode();
   				dsn.setItemCode(svgFile.getFilePath());
				dsn.setItemName(svgFile.getFileName());
				DefaultMutableTreeNode newNode = new DefaultMutableTreeNode(dsn);
  				rootNode.add(newNode);
        	}
        }
        //移除parentname节点
        for(Map.Entry<DefaultMutableTreeNode, DefaultMutableTreeNode> map: removeNodeMap.entrySet()){    
        	map.getValue().remove(map.getKey());
        }

        JTree lineTree = new JTree(rootNode);
        expandAll(lineTree, new TreePath(rootNode), true);
        return lineTree;
    }
    
    
    /**
     *作用：构建JTree  按照条件过滤不需要的svg文件
     *@param rootName 树名
     *@param connType 连接类型1（青海）2（江西）3（其他）
     *@param sql 树的数据源
     *@param colnum 树的级数
     *@param isSort 是否需要内部排序
     ***/
    public JTree buildLineTree2(String rootName, List list, boolean isSort) {
        DefaultMutableTreeNode rootNode = new DefaultMutableTreeNode(rootName);   //创建根节点     
        if(list != null) {
	        TreeMap treeMap = this.getTreeMap(list, isSort);
	        Object[] keyarray = treeMap.keySet().toArray();
	        HashMap nodeMap = new HashMap();       //存放树的节点 及对应序号 方便查询是否有父节点
	
	        for (int i = 0; i < keyarray.length; i++) {
	            String newKey = keyarray[i].toString();     //treeMap的序列码
	            String newID = treeMap.get(newKey).toString();  //如果叶节点是对象则是id   否则是Str
	
	            DefaultMutableTreeNode newNode;   //为每个序号新建一个树节点
	            if (colNum != cols) {
	                if (codeMap.get(newID) == null) {
	                    //非叶节点
	                    newNode = new DefaultMutableTreeNode(newID);
	                } else {
	                    //叶节点存放对象
	                    newNode = new DefaultMutableTreeNode(codeMap.get(newID));
	                }
	            } else {
	                newNode = new DefaultMutableTreeNode(newID);
	            }
	            String parentkey = newKey.substring(0, newKey.length() - 3);   //待查询父节点key   
	            if (nodeMap.get(parentkey) == null) {    //查询是否有父节点
	                rootNode.add(newNode);             //加入根节点
	            } else {
	                DefaultMutableTreeNode paramNode = (DefaultMutableTreeNode) nodeMap.get(parentkey);
	                paramNode.add(newNode);    //加入父节点
	            }
	            nodeMap.put(keyarray[i].toString(), newNode);
	        }
        }
        else {
        	for (Iterator it = SystemConstants.getMapSVGFile().values().iterator(); it.hasNext();) {
   				SVGFile svgFile = (SVGFile)it.next();
   				DefaultSimpleNode dsn = new DefaultSimpleNode();
   				if(svgFile.getFileName().indexOf("母线") >0){
   					continue;
   				}else if(svgFile.getFileName().indexOf("线") >0){
   					dsn.setItemCode(svgFile.getFilePath());
   					dsn.setItemName(svgFile.getFileName());
   					DefaultMutableTreeNode newNode = new DefaultMutableTreeNode(dsn);
      				rootNode.add(newNode);
   				}
 
        	}
        }
        JTree lineTree = new JTree(rootNode);
        expandAll(lineTree, new TreePath(rootNode), true);
        return lineTree;
    }

    /**
     * 作用：获取结果集
     * @param:connType 连接类型
     * 扩展：sql可作参数输入动态改变树
     **/
    public List getList(String sql, Integer connType) {
        List list = null;
        Connection conn = null;
        Statement stam = null;
        ResultSet rs = null;
        try {
	        if (connType == 1) {   //青海连接
	            conn = DBManager.getConnection();
	        } else {
	        }
	        if(conn == null)
	        	return null;
            stam = conn.createStatement();
            rs = stam.executeQuery(sql);
            list = getListByRes(rs);
            stam.close();
            conn.close();
        } catch (Exception e) {
        	log.error(e.getMessage(), e);
        } finally {
        }
        return list;
    }

    /**
     * 作用：对数据库查询返回的结果集ResultSet 进行处理,拆分有逗号隔开的字段，空字段设置为“其他”
     *       将各个字段组合为"XXXX-XXXX-XXXX"存入list返回
     **/
    public List getListByRes(ResultSet rs) {
    	codeMap.clear();
        List list = new ArrayList();
        DefaultSimpleNode dsn = null;
        try {
//            String[] shortArray = new String[colNum];                //一条记录的各列处理后的临时数据集合
            StringBuffer shortStrBuf;
            cols = rs.getMetaData().getColumnCount();   //列数
            while (rs.next()) {
                // 加载对象
                if (cols != colNum) {
                    dsn = new DefaultSimpleNode();
                    dsn.setItemCode(rs.getString("NODECODE"));
                    dsn.setItemName(rs.getString("NODENAME"));
                    codeMap.put(rs.getString("NODECODE"), dsn);
                }
                // 加载节点
                shortStrBuf = new StringBuffer("");
                String itemStr = "";
                for (int cf = 0; cf < colNum; cf++) {        //将Rs中的记录各个字段按相应格式处理，存入数组shortArray，其中标明有逗号隔开的字段                                   
                    if (rs.getString(cf + 1) == null) {   //若节点为空,则用“其他”代替
                        itemStr = "厂站";
                    } else {
                        itemStr = rs.getString(cf + 1).trim();
                    }

                    if (shortStrBuf.toString().equals("")) {
                        shortStrBuf.append(itemStr);
                    } else {
                        shortStrBuf.append("_" + itemStr);  //将各个字段用"-"连接 UUID中包括了‘-’
                    }
                    list.add(shortStrBuf.toString());
                }
            }
//                for (int cf = 0; cf < colNum; cf++) {        //将Rs中的记录各个字段按相应格式处理，存入数组shortArray，其中标明有逗号隔开的字段
//                    if (rs.getString(cf + 1) == null) {   //若节点为空,则用“其他”代替
//                        shortArray[cf] = "其他";
//                    } else {
//                        if (rs.getString(cf + 1).indexOf(",") > 0) {
//                            cus = cf;
//                            shortArray[cf] = rs.getString(cf + 1);
//                        } else {
//                            shortArray[cf] = rs.getString(cf + 1);
//                        }
//                    }
//                }
//                if (cus == -1) {        // 字段不包含逗号
//                    shortStrBuf = new StringBuffer("");
//                    for (int ch = 0; ch < colNum; ch++) {
//                        if (shortStrBuf.toString().equals("")) {
//                            shortStrBuf.append(shortArray[ch]);
//                        } else {
//                            shortStrBuf.append("-" + shortArray[ch]);  //将各个字段用"-"连接
//                        }
//                    }
//                    list.add(shortStrBuf.toString());
//                } else {
//                    //记录中字段中包含","的拆分为两条记录  不是很常用
//                    String[] commaArray = shortArray[cus].split(",");
//                    for (int cc = 0; cc < commaArray.length; cc++) {
//                        shortStrBuf = new StringBuffer("");
//                        shortArray[cus] = commaArray[cc];
//                        for (int cl = 0; cl < colNum; cl++) {
//                            if (shortStrBuf.toString().equals("")) {
//                                shortStrBuf.append(shortArray[cl]);
//                            } else {
//                                shortStrBuf.append("-" + shortArray[cl]);
//                            }
//                        }
//                        list.add(shortStrBuf.toString());
//                    }
//                }
//            }
        } catch (SQLException e) {
        	log.error(e.getMessage(), e);
        }
        return list;
    }

    /**
     * 作用：根据输入的list 排序后生成根"01 根节点 0101 次节点...." 的一个TreeMap 
     * @param: isSort 是否需要对list排序
     * @param：nodeList字段 XXXX-XXXX-XXX 形式
     **/
    public TreeMap getTreeMap(List nodeList, boolean isSort) {
        // 用于统计每个电压等级的记录数（按照第二层节点类型分类统计）
        Map<String, Map<String, Integer>> voltageCountMap = new HashMap<String, Map<String, Integer>>();
        voltageCountMap.put("变电站", new HashMap<String, Integer>());
        voltageCountMap.put("电厂", new HashMap<String, Integer>());
        voltageCountMap.put("用户站", new HashMap<String, Integer>());

        // 预处理,统计每个电压等级的记录数
        for (Object node : nodeList) {
            String nodeStr = node.toString();
            String[] parts = nodeStr.split("_");
            // 只处理具有完整4层结构的节点（厂站_变电站_电压等级_ID）
            if (parts.length == 4) {
                String secondLevel = parts[1];  // 第二层节点类型（变电站/电厂/用户站）
                String voltageLevel = parts[2]; // 获取电压等级部分
                if (voltageLevel.contains("kV")) {
                    // 根据第二层节点类型选择对应的统计Map
                    if (voltageCountMap.containsKey(secondLevel)) {
                        Map<String, Integer> countMap = voltageCountMap.get(secondLevel);
                        if (countMap.containsKey(voltageLevel)) {
                            countMap.put(voltageLevel, countMap.get(voltageLevel) + 1);
                        } else {
                            countMap.put(voltageLevel, 1);
                        }
                    }
                }
            }
        }
        // TreeMap treeMap =new  TreeMap(Collections.reverseOrder());     //数据倒序
        if (isSort) {
            Comparator comp = Collections.reverseOrder();  //降序
            Collections.sort(nodeList, comp);
        //  Collections.sort(nodeList);     //将list排序
        }
        String[] nodeStr = new String[colNum];  //当前节点数组
        String[] nodeSeq = new String[colNum];   //当前节点序号
        Integer[] intSeq = new Integer[colNum + 1];   //当前节点在其父节点中的排序
        TreeMap treeMap = new TreeMap();
        String[] newNodeStr = null;
        for (int cn = 0; cn < nodeList.size(); cn++) {
            //遍历List
            newNodeStr = nodeList.get(cn).toString().split("_");    // 新增一组节点
            for (int i = 0; i < newNodeStr.length; i++) {
            	
                if (nodeStr[i] == null) {           //初始化节点
                    nodeStr[i] = "@tellhow#";           //设置个无意义字符串 方便比较 出现相同概率较少
                }
                if (intSeq[i] == null) {             //初始化节点在父节点中的排序
                    intSeq[i] = 0;
                }
                // 处理节点值
                String currentNodeValue = newNodeStr[i];
                // 如果当前节点包含kV，说明是电压等级节点
                // 如果是电压等级节点且存在统计数据
                if (currentNodeValue.contains("kV") && i == 2) {
                    String secondLevel = newNodeStr[1]; // 获取第二层节点类型
                    if (voltageCountMap.containsKey(secondLevel)) {
                        Map<String, Integer> countMap = voltageCountMap.get(secondLevel);
                        if (countMap.containsKey(currentNodeValue)) {
                            currentNodeValue = currentNodeValue + " (" + countMap.get(currentNodeValue) + ")";
                        }
                    }
                }
                if (i == 0) {         //根节点
                    if (!nodeStr[i].equals(currentNodeValue)) {
                        nodeStr[i] = currentNodeValue;
                        if (intSeq[i] < 9) {
                            nodeSeq[i] = "00" + (++intSeq[i]);
                            intSeq[i + 1] = 0;
                        } else {
                            if (intSeq[i] < 99) {
                                nodeSeq[i] = "0" + (++intSeq[i]);
                                intSeq[i + 1] = 0;
                            } else {
                                nodeSeq[i] = String.valueOf(++intSeq[i]);
                                intSeq[i + 1] = 0;          //将下一级级节点序号清零
                            }
                        }
                        treeMap.put(nodeSeq[i], nodeStr[i]);
                    }
                } else {
                    if (!nodeStr[i].equals(currentNodeValue) || intSeq[i] == 0) {
                        nodeStr[i] = currentNodeValue;
                        if (intSeq[i] < 9) {
                            nodeSeq[i] = nodeSeq[i - 1] + "00" + (++intSeq[i]);
                            intSeq[i + 1] = 0;
                        } else {
                            if (intSeq[i] < 99) {
                                nodeSeq[i] = nodeSeq[i - 1] + "0" + (++intSeq[i]);
                                intSeq[i + 1] = 0;
                            } else {
                                nodeSeq[i] = nodeSeq[i - 1] + String.valueOf(++intSeq[i]);
                                intSeq[i + 1] = 0;          //将下一级级节点序号清零
                            }
                        }
                        treeMap.put(nodeSeq[i], nodeStr[i]);
                    }
                }
            }
        }
        return treeMap;
    }
    
    void expandAll(JTree tree, TreePath parent, boolean expand) {
		TreeNode node = (TreeNode) parent.getLastPathComponent();
		if (node.getChildCount() >= 0) {
			for (Enumeration e = node.children(); e.hasMoreElements();) {
				TreeNode n = (TreeNode) e.nextElement();
				TreePath path = parent.pathByAddingChild(n);
				expandAll(tree, path, expand);
			}
		}
		if (expand) {
			tree.expandPath(parent);
		} else {
			tree.collapsePath(parent);
		}
	}
}
   
