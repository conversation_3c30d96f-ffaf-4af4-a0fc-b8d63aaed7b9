package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEZYBFDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱站用变复电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			String sql = "SELECT ZYB_DEVID,ZYB_NAME,ZYB_DZNAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+stationDev.getPowerDeviceID()+"'";
			
			List<Map<String,String>> zybidList = DBManager.queryForList(sql);
			
			for(Map<String,String> zybidMap : zybidList){
				String zybName = StringUtils.ObjToString(zybidMap.get("ZYB_NAME"));

				replaceStr = stationName+zybName+"由冷备用转运行/r/n";
			}
		}
		
		return replaceStr;
	}

}
