package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZNTDDDTRBZT implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("站内停电调电投入备自投".equals(tempStr)) {
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			if(station.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
				List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
				List<PowerDevice> zbLists = new ArrayList<PowerDevice>();
				PowerDevice zycmlkg = new PowerDevice();
				PowerDevice dycmlkg = new PowerDevice();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
		
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
							gycmlkgList.add(dev);
						}else if(dev.getPowerVoltGrade() == 35&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
							zycmlkg = dev;
						}else if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
							dycmlkg = dev;
						}
					}

					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						zbLists.add(dev);
					}
					
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&(dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
						dycmxList.add(dev);
					}
				}		
				
				if(gycmlkgList.size()>0){
					if(zbLists.size() == 2){
						replaceStr += CZPService.getService().getDevName(station)+"@投入"+(int)zycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
						replaceStr += CZPService.getService().getDevName(station)+"@投入"+(int)dycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
					}else{
						for(PowerDevice gycmlkg : gycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gycmlkg).equals("1")||
									(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gycmlkg).equals("0")&&gycmlkg.getPowerDeviceID().equals(curDev.getPowerDeviceID()))){
								
								String num = CZPService.getService().getDevNum(gycmlkg);
								
								if(!zycmlkg.getPowerDeviceID().equals("")){
									replaceStr += "投入"+(int)zycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
								}
								
								if(!dycmlkg.getPowerDeviceID().equals("")){
									if(dycmxList.size()>2){
										replaceStr += "投入"+(int)dycmlkg.getPowerVoltGrade()+"kV分段0"+num.substring(num.length()-2, num.length())+"断路器备自投装置/r/n";
									}else{
										replaceStr += "投入"+(int)dycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
									}
								}
								break;
							}
						}
					}
				}
			}
			if(replaceStr.equals("")){
				replaceStr = null;
			}
			
		}
		return replaceStr;
	}


}