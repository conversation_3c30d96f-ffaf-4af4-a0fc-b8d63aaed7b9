package com.tellhow.czp.app.yndd.wordcard.dl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.dl.JDKGXZDL;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDL;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDLXLFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("大理线路复电".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			List<Map<String, String>> stationLineList = CommonFunctionDL.getStationLineList(curDev);
			
			boolean isControl = true;

			for(PowerDevice dev : loadLineTrans){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionDL.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(sourceLineTrans!=null){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionDL.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(loadLineTrans.size() == 0){
				isControl = false;
			}
			
			if(stationLineList.size() > 0){
				isControl = false;
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("3")){
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					  
					if(JDKGXZDL.chooseEquips.contains(sourceLineTrans) && jddzList.size() == 1){
						 replaceStr += stationName+"@拆除在"+CZPService.getService().getDevName(curDev)+"线路侧装设的一组代替线路接地开关功能的三相短路接地线/r/n";
					}else if(JDKGXZDL.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
						 replaceStr += stationName+"@拆除在"+CZPService.getService().getDevName(curDev)+"线路侧装设的一组三相短路接地线/r/n";
					}else{
						 replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
					}
				}
				
				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
						
					if(jddzList != null){
						if(JDKGXZDL.chooseEquips.contains(dev) && jddzList.size() == 1){
							 replaceStr += stationName+"@拆除在"+CZPService.getService().getDevName(curDev)+"线路侧装设的一组代替线路接地开关功能的三相短路接地线/r/n";
						}else if(JDKGXZDL.chooseEquips.contains(dev)||jddzList.size()==0){
							 replaceStr += stationName+"@拆除在"+CZPService.getService().getDevName(curDev)+"线路侧装设的一组三相短路接地线/r/n";
						}else{
							 replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
						}
					}
				}
				
				String preStationName = "";
				
				for(Map<String,String> stationLineMap : stationLineList){
					String unit = StringUtils.ObjToString(stationLineMap.get("UNIT"));
					String linename = StringUtils.ObjToString(stationLineMap.get("LINE_NAME"));
					String switchName = StringUtils.ObjToString(stationLineMap.get("SWITCH_NAME"));
					String lowerunit = StringUtils.ObjToString(stationLineMap.get("LOWERUNIT"));
					String grounddisconnectorname = StringUtils.ObjToString(stationLineMap.get("GROUNDDISCONNECTOR_NAME"));
					
					if(preStationName.equals("")){
						preStationName = unit;
					}else if(preStationName.equals(unit)){
						continue;
					}
					
					boolean isccdx =  false;
					
					for(PowerDevice dev : JDKGXZDL.chooseEquips){
						if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
							isccdx = true;
							replaceStr += unit+"@拆除在"+lowerunit+linename+"线路侧装设的一组三相短路接地线/r/n";
							break;
						 }
					}
					
					if(!isccdx){
						if(!grounddisconnectorname.equals("")){
							replaceStr += unit+"@拉开"+lowerunit+grounddisconnectorname+"/r/n";
						}else{
							replaceStr += unit+"@拆除在"+lowerunit+linename+"线路侧装设的一组三相短路接地线/r/n";
						}
					}
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 
				replaceStr += stationName+"@落实"+CZPService.getService().getDevName(sourceLineTrans)+"线路保护及重合闸已按正常方式投入/r/n";
			}
			
			for(PowerDevice loadLineTran : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				replaceStr += stationName+"@落实"+CZPService.getService().getDevName(loadLineTran)+"线路保护及重合闸已按正常方式投入/r/n";
			}
			
			for(Map<String,String> stationLineMap : stationLineList){
				String unit = StringUtils.ObjToString(stationLineMap.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(stationLineMap.get("LOWERUNIT"));
				String linename = StringUtils.ObjToString(stationLineMap.get("LINE_NAME"));
				replaceStr += unit+"@落实"+lowerunit+linename+"线路保护及重合闸已按正常方式投入/r/n";
			}
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			
			kgList.addAll(xlkgList);
			kgList.addAll(zbkgList);

			boolean isRunModelThreeTwo = false;
			
			for(PowerDevice dev : kgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					isRunModelThreeTwo = true;
					break;
				}
			}
			
			if(isRunModelThreeTwo){
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制前的要求进行设置/r/n";
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							Collections.reverse(dzList);

							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "大理地调@遥控合上"+stationName+devname+"/r/n";
									replaceStr += stationName+"@核实"+devname+"在合闸位置/r/n";
								}
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							Collections.reverse(dzList);

							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "大理地调@遥控合上"+stationName+devname+"/r/n";
									replaceStr += stationName+"@核实"+devname+"在合闸位置/r/n";
								}
							}
						}
					}
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制后的要求进行设置/r/n";
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								
								if(curDev.getPowerVoltGrade() > 110){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									dzList = RuleExeUtil.sortByMXC(dzList);
									
									for(PowerDevice dz : dzList){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
										}
									}
								}
							}else{
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						replaceStr += stationName+"@将"+lowerunit+switchName+"由冷备用转热备用/r/n";
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								
								replaceStr += "大理地调@遥控退出"+stationName+deviceName+"检同期软压板/r/n";
								replaceStr += "大理地调@遥控投入"+stationName+deviceName+"检无压软压板/r/n";
								replaceStr += "大理地调@遥控合上"+stationName+deviceName+"对线路充电/r/n";
								replaceStr += "大理地调@遥控退出"+stationName+deviceName+"检无压软压板/r/n";
								replaceStr += "大理地调@遥控投入"+stationName+deviceName+"检同期软压板/r/n";
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> xlkgLoadList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					for(PowerDevice dev : xlkgLoadList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							String deviceName = CZPService.getService().getDevName(dev);
							
							if(stationName.contains("钢铁变")){
								replaceStr += stationName+"@用"+deviceName+"同期合环/r/n";
							}else{
								replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						replaceStr += stationName+"@用"+lowerunit+switchName+"同期合环/r/n";
					}
				}
			}else if(isControl){
				if(RuleExeUtil.isDeviceHadStatus(sourceLineTrans, "2", "1")){
					StringBuffer sbf = new StringBuffer();
					
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						stationName = StringUtils.killVoltInDevName(stationName);
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
						
						for(PowerDevice xlsw : xlswList){
							if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
								List<PowerDevice> mxList = new ArrayList<PowerDevice>();
								
								for(PowerDevice dz : dzList){
									if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
										}
									}
								}

								String mxNum = "";
								
								for(PowerDevice mx : mxList){
									mxNum = CZPService.getService().getDevNum(mx);
								}
								
								sbf.append(stationName+CZPService.getService().getDevNum(xlsw)+"断路器上"+mxNum+"母，");
							}
						}
					}
					
					for(PowerDevice dev : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						stationName = StringUtils.killVoltInDevName(stationName);
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						for(PowerDevice xlsw : xlswList){
							if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
								List<PowerDevice> mxList = new ArrayList<PowerDevice>();
								
								for(PowerDevice dz : dzList){
									if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
										}
									}
								}

								String mxNum = "";
								
								for(PowerDevice mx : mxList){
									mxNum = CZPService.getService().getDevNum(mx);
								}
								
								sbf.append(stationName+CZPService.getService().getDevNum(xlsw)+"断路器上"+mxNum+"母，");
							}
						}
					}
					
					String remark = "";
					
					if(sbf.length() > 0){
						if(sbf.toString().endsWith("，")){
							sbf = sbf.delete(sbf.length()-1, sbf.length());
						}
						
						remark = "（备注："+sbf.toString()+"）";
					}
					
					replaceStr += "大理地调@执行"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用程序操作"+remark+"/r/n";
					
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
					
						for(PowerDevice xlsw : xlkgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
							
							if(xlsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dzList = RuleExeUtil.sortByCZMXC(dzList);
								Collections.reverse(dzList);
							}else{
								if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
									dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
									Collections.reverse(dzList);
								}else{
									dzList = RuleExeUtil.sortByMXC(dzList);
								}
							}
							replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
						}
					}
					
					for(PowerDevice dev : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						for(PowerDevice xlsw : xlswList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
							
							if(xlsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dzList = RuleExeUtil.sortByCZMXC(dzList);
								Collections.reverse(dzList);
							}else{
								if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
									dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
									Collections.reverse(dzList);
								}else{
									dzList = RuleExeUtil.sortByMXC(dzList);
								}
							}
							replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
						}
					}
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).endsWith("0")){
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@遥控合上"+stationName+deviceName+"对线路充电/r/n";
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev2 = it2.next();
							
							if(dev2.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev2);
								}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev2);
								}
							}
						}
						
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
							}
						}
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
							
							if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev);
								}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev);
								}
							}
						}
						
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
					}
				}
			}else{
				if(RuleExeUtil.isDeviceHadStatus(sourceLineTrans, "2", "1")){
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						if(curDev.getPowerVoltGrade() == 35){
							String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+sourceLineTrans.getPowerDeviceID()+"'";
							List<Map<String,String>> zybList = DBManager.queryForList(sql);
							
							for(Map<String,String> zybMap : zybList){
								replaceStr += stationName+"@将"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"由冷备用转运行/r/n";
								break;
							}
						}
						
						for(PowerDevice dev : xlswList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(curDev.getPowerVoltGrade() == 35){
								List<PowerDevice> linedzList = new ArrayList<PowerDevice>();
								
								for(PowerDevice dz : dzList){
									List<PowerDevice> dz3List = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);
									
									for(PowerDevice dz3 : dz3List){
										if(dz3.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
											linedzList.add(dz3);
											break;
										}
									}
								}
								
								replaceStr += CommonFunctionDL.getKnifeOnContent(linedzList, stationName);
							}
							
							String deviceName = CZPService.getService().getDevName(dev);
							
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
									replaceStr += "大理地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
									replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
								}else{
									if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
										if(curDev.getPowerVoltGrade() == 35){
											if(dzList.size() == 1){
												replaceStr += CommonFunctionDL.getKnifeOnContent(dzList, stationName);
											}else{
												replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
											}
										}else{
											replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
										}
									}
								}
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						if(curDev.getPowerVoltGrade() == 35){
							String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+loadLineTran.getPowerDeviceID()+"'";
							List<Map<String,String>> zybList = DBManager.queryForList(sql);
							
							for(Map<String,String> zybMap : zybList){
								replaceStr += stationName+"@将"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"由冷备用转运行/r/n";
								break;
							}
						}
						
						for(PowerDevice dev : xlswList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(curDev.getPowerVoltGrade() == 35){
								List<PowerDevice> linedzList = new ArrayList<PowerDevice>();
								
								for(PowerDevice dz : dzList){
									List<PowerDevice> dz3List = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);
									
									for(PowerDevice dz3 : dz3List){
										if(dz3.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
											linedzList.add(dz3);
											break;
										}
									}
								}
								
								replaceStr += CommonFunctionDL.getKnifeOnContent(linedzList, stationName);
							}
							
							String deviceName = CZPService.getService().getDevName(dev);
							
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
									replaceStr += "大理地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
									replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
								}else{
									if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
										if(curDev.getPowerVoltGrade() == 35){
											if(dzList.size() == 1){
												replaceStr += CommonFunctionDL.getKnifeOnContent(dzList, stationName);
											}else{
												replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
											}
										}else{
											replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
										}
									}
								}
							}
						}
					}
					
					for(Map<String, String> map : stationLineList) {
						String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
						String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();
						String endpointkind = StringUtils.ObjToString(map.get("ENDPOINT_KIND")).trim();
						
						if(operationkind.equals("下令")){
							if(!disconnectorName.equals("")){
								replaceStr += stationName+"@合上"+lowerunit+disconnectorName+"/r/n";
							}
							
							if(!switchName.equals("")){
								replaceStr += stationName+"@将"+lowerunit+switchName+"由冷备用转热备用/r/n";
							}
						}else if(operationkind.equals("许可")){
							
						}else if(operationkind.equals("配合")){
							
						}
					}
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).endsWith("0")){
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						for(PowerDevice dev : xlswList){
							if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += "大理地调@遥控合上"+stationName+deviceName+"对线路充电/r/n";
								}
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev2 = it2.next();
							
							if(dev2.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev2);
								}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev2);
								}
							}
						}
						
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);
						
						for(PowerDevice dev : tempList){
							if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
								}
							}
						}
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev2 = it2.next();
							
							if(dev2.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev2);
								}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev2);
								}
							}
						}
						
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
					}
					
					if(curDev.getPowerVoltGrade() == 35){
						for(PowerDevice loadLineTran : loadLineTrans){
							PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
							String stationName = CZPService.getService().getDevName(station);
							
							if(station.getPowerVoltGrade() == 35){
								String bztContent = stationName+"@投入"+(int)loadLineTran.getPowerVoltGrade()+"kV备自投装置/r/n";

								if(!replaceStr.contains(bztContent)){
									replaceStr += bztContent;
								}
							}
						}
					}
					
					for(Map<String, String> map : stationLineList) {
						String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
						String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();
						String endpointkind = StringUtils.ObjToString(map.get("ENDPOINT_KIND")).trim();
						
						if(operationkind.equals("下令")){
							if(!switchName.equals("")){
								replaceStr += stationName+"@合上"+lowerunit+switchName+"/r/n";
							}
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
