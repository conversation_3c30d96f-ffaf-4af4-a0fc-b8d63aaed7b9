package com.tellhow.czp.app.yndd.view;


import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.MouseEvent;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;



public class UserStationDialog extends javax.swing.JDialog {
	private DefaultTableModel dTableModel;
	private JPanel topPanel;//查询条件及按钮面板
	private JPanel mainPanel;//信息面板
	private JTextField searchText;//查询框
	private JButton searchButton;//查询按钮
	private JButton addButton;//新增按钮
	private JButton updateButton;//修改按钮
	private JButton delButton;//删除按钮
	private JButton readButton;//新增按钮
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTableInfo;//信息列表



	public UserStationDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("设置线路对应用户站术语");
		setLocationCenter();
		initTable("");
	}

	/**
	 * 初始化表格  传入参数关键字
	 */
	
	public void initTable(String gjz) {
		dTableModel.setRowCount(0);
		String sql = "SELECT ID,LINE_ID,LINE_NAME,SWITCH_NAME,ENDPOINT_KIND,OPERATION_KIND,UNIT,VOLTAGE,LOWERUNIT,SOURCE,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE"
				+ " LINE_NAME LIKE '%"+gjz+"%' OR SWITCH_NAME LIKE '%"+gjz+"%' OR ENDPOINT_KIND LIKE '%"+gjz+"%' OR OPERATION_KIND LIKE '%"+gjz+"%' OR UNIT LIKE '%"+gjz+"%' OR LOWERUNIT LIKE '%"+gjz+"%' OR VOLTAGE LIKE '%"+gjz+"%' ";
		List results= DBManager.queryForList(sql);
		Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			 temp=(Map)results.get(i);
			 
			 String id=StringUtils.ObjToString(temp.get("ID"));
			 String unit=StringUtils.ObjToString(temp.get("UNIT"));
			 String voltage=StringUtils.ObjToString(temp.get("VOLTAGE"));
			 String lineid=StringUtils.ObjToString(temp.get("LINE_ID"));
			 String linename=StringUtils.ObjToString(temp.get("LINE_NAME"));
			 String switchname=StringUtils.ObjToString(temp.get("SWITCH_NAME"));
			 String disconnectorname=StringUtils.ObjToString(temp.get("DISCONNECTOR_NAME"));
			 String grounddisconnectorname=StringUtils.ObjToString(temp.get("GROUNDDISCONNECTOR_NAME"));
			 String endpointkind=StringUtils.ObjToString(temp.get("ENDPOINT_KIND"));
			 String operationkind=StringUtils.ObjToString(temp.get("OPERATION_KIND"));
			 String lowerunit=StringUtils.ObjToString(temp.get("LOWERUNIT"));
			 String source=StringUtils.ObjToString(temp.get("SOURCE"));

			 if(source.equals("1")){
				 source = "是";
			 }else{
				 source = "否";
			 }
			 
			 Object[] rowData = {id,lineid,i+1,voltage,linename,switchname,disconnectorname,grounddisconnectorname,endpointkind, operationkind,unit,lowerunit,source};
			 dTableModel.addRow(rowData);
		 }

		jTableInfo.setModel(dTableModel);
		DefaultTableCellRenderer r  =  new  DefaultTableCellRenderer();   
		r.setHorizontalAlignment(JTextField.CENTER);   
		jTableInfo.getColumn("序号").setCellRenderer(r);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}


	private void initComponents() {
		this.setLayout(new BorderLayout());
		this.setSize(1000, 480);
		topPanel =new JPanel();
		topPanel.setPreferredSize(new Dimension(0,45));
		this.add(topPanel,BorderLayout.NORTH);
		mainPanel =new JPanel();
		this.add(mainPanel,BorderLayout.CENTER);
		
		JLabel label1 = new JLabel("线路关键字：");
		JLabel label2 = new JLabel("");//增加空位用
		label2.setPreferredSize(new Dimension(60,0));
		topPanel.setLayout(new FlowLayout(FlowLayout.CENTER,10,10));
		searchText =new JTextField();
		searchText.setPreferredSize(new Dimension(200,20));
		searchButton =new JButton("查询");
		searchButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				searchButtonActionPerformed(evt);
			}
		});
		
		readButton =new JButton("查看");
		readButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				readButtonActionPerformed(evt);
			}
		});
		
		addButton =new JButton("新增");
		addButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				addButtonActionPerformed(evt);
			}
		});
		updateButton =new JButton("修改");
		updateButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				updateButtonActionPerformed(evt);
			}
		});
		delButton =new JButton("删除");
		delButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				delButtonActionPerformed(evt);
			}
		});
		
		topPanel.add(label1);
		topPanel.add(searchText);
		topPanel.add(searchButton);
		topPanel.add(label2);

		topPanel.add(readButton);
		topPanel.add(addButton);
		topPanel.add(updateButton);
		topPanel.add(delButton);
		

		
		dTableModel = new DefaultTableModel(null,new String[] {"ID","线路ID","序号","电压等级","线路名称","开关名称","刀闸名称","接地刀闸名称","端点类型","操作项目类别","受令单位","下级单位","电源侧线路"}){
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		jTableInfo = new JTable();
		jTableInfo.setModel(dTableModel);
		
		jTableInfo.getColumnModel().getColumn(0).setMinWidth(0);
		jTableInfo.getColumnModel().getColumn(0).setMaxWidth(0);
		jTableInfo.getColumnModel().getColumn(1).setMinWidth(0);
		jTableInfo.getColumnModel().getColumn(1).setMaxWidth(0);
		jTableInfo.getColumnModel().getColumn(2).setMinWidth(50);
		jTableInfo.getColumnModel().getColumn(2).setMaxWidth(50);
		jTableInfo.getColumnModel().getColumn(2).setPreferredWidth(50);
		jTableInfo.getColumnModel().getColumn(3).setMinWidth(50);
		jTableInfo.getColumnModel().getColumn(3).setMaxWidth(50);
		jTableInfo.getColumnModel().getColumn(3).setPreferredWidth(50);
		jTableInfo.getColumnModel().getColumn(10).setMinWidth(80);
		jTableInfo.getColumnModel().getColumn(10).setMaxWidth(80);
		jTableInfo.getColumnModel().getColumn(10).setPreferredWidth(80);
		jTableInfo.setRowHeight(26);
		jScrollPane1 = new JScrollPane(jTableInfo);
		jScrollPane1.setPreferredSize(new Dimension(950,370));
		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));
		mainPanel.add(jScrollPane1,BorderLayout.CENTER);
		

		jTableInfo.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				jTableInfoMouseClicked(evt);
			}
		});

		
	}

//	//修改
	private void updateButtonActionPerformed(java.awt.event.ActionEvent evt) {
	
		int[] selectRows = jTableInfo.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要修改的记录！");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view(this, "只能选择一条记录！");
			return;
		}
		String lineid = this.jTableInfo.getValueAt(selectRows[0], 1).toString();
		UserStationAddDialog aud = new UserStationAddDialog(this, true,lineid,"修改");
		aud.setVisible(true);
		this.initTable(searchText.getText().trim());
	}
	
	private void readButtonActionPerformed(ActionEvent evt) {
		int[] selectRows = jTableInfo.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要查看的记录！");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view(this, "只能选择一条记录！");
			return;
		}
		String lineid = this.jTableInfo.getValueAt(selectRows[0], 1).toString();
		UserStationAddDialog aud = new UserStationAddDialog(this, true,lineid,"查看");
		aud.setVisible(true);
	}
	
	//新增
	private void addButtonActionPerformed(java.awt.event.ActionEvent evt) {
		UserStationAddDialog aud = new UserStationAddDialog(this, true,"","新增");
		aud.setVisible(true);
		this.initTable(searchText.getText().trim());
	}
	//查询
	private void searchButtonActionPerformed(java.awt.event.ActionEvent evt) {
		this.initTable(searchText.getText().trim());
	}
	
	//删除
	private void delButtonActionPerformed(java.awt.event.ActionEvent evt) {

		int[] selectRows = jTableInfo.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要删除的记录！");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view(this, "只能选择一条记录！");
			return;
		}
		String id = this.jTableInfo.getValueAt(selectRows[0], 0).toString();
		
		int isOk=JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "是否确定删除该记录？",SystemConstants.SYSTEM_TITLE, JOptionPane.YES_NO_OPTION);
		if(isOk==JOptionPane.NO_OPTION){
			return;
		}
		
		String sql = "DELETE FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE ID = '"+id+"'";
		DBManager.update(sql);
		
		this.initTable(searchText.getText().trim());
	}
	
	private void jTableInfoMouseClicked(MouseEvent e){
		if(e.getClickCount() ==2){
			int[] selectRows = jTableInfo.getSelectedRows();
			if (selectRows.length == 0) {
				ShowMessage.view(this, "请选择一条信息！");
				return;
			}
			String lineid = this.jTableInfo.getValueAt(selectRows[0], 1).toString();
			UserStationAddDialog aud = new UserStationAddDialog(this, true,lineid,"查看");

			aud.setVisible(true);
			this.initTable(searchText.getText().trim());
		}
	}
}
