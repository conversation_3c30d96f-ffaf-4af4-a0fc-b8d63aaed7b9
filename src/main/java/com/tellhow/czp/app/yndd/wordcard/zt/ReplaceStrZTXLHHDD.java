package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTXLHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通线路合环调电".equals(tempStr)){
			List<PowerDevice> tagkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> allLineList =  RuleExeUtil.getLineAllSideList(curDev);
			
			String filter = CZPImpl.getPropertyValue("HHProtectedLocation");
			
			String[] filterArr = filter.split(";");
			
			List<String> filterList =  Arrays.asList(filterArr);	
			
			for(PowerDevice dev : allLineList){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlkg : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("1")){
						tagkgList.add(xlkg);
						replaceStr += CommonFunction.getHhContent(xlkg, "昭通地调", stationName);
					}
				}
			}
			
			for(PowerDevice dev : allLineList){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);

				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlkg : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
						tagkgList.add(xlkg);
						replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkg)+"/r/n";
					}
				}
			}
			
			for(Iterator<PowerDevice> itor = tagkgList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(!filterList.contains(dev.getPowerDeviceID())){
					itor.remove();
				}
			}
			
			if(tagkgList.size()>0){
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, tagkgList , "请选择需要调整保护定值区的断路器：");
				List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

				for(PowerDevice dev : choosedyckgList){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);

					replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"保护定值区由0X区切换至0X区/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
