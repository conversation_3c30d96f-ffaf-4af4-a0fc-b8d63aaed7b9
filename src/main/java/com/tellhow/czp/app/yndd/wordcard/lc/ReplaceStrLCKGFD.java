package com.tellhow.czp.app.yndd.wordcard.lc;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLC;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLCKGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("临沧开关复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			String beginStatus = CBSystemConstants.getCurRBM().getBeginStatus();
			String endStatus = CBSystemConstants.getCurRBM().getEndState();
			
			if(beginStatus.equals("2") && endStatus.equals("1")){
				String mxName = "";
				
				if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : mxList){
						mxName = "上"+CZPService.getService().getDevName(mx);
						break;
					}
				}
				
				if(CommonFunctionLC.ifSwitchSeparateControlLC(curDev)){
					if(!mxName.equals("")){
						replaceStr += "临沧地调@执行"+stationName+deviceName+"由冷备用转"+mxName+"热备用程序操作/r/n";
					}else{
						replaceStr += "临沧地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					}

					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionLC.getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}
			}else if(beginStatus.equals("1") && endStatus.equals("0")){
				replaceStr += CommonFunctionLC.getSwitchOnContent(curDev, stationName,station);
			}else if(beginStatus.equals("2") && endStatus.equals("0")){
				String mxName = "";
				
				if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : mxList){
						mxName = "上"+CZPService.getService().getDevName(mx);
						break;
					}
				}
				
				if(CommonFunctionLC.ifSwitchSeparateControlLC(curDev)){
					if(!mxName.equals("")){
						replaceStr += "临沧地调@执行"+stationName+deviceName+"由冷备用转"+mxName+"热备用程序操作/r/n";
					}else{
						replaceStr += "临沧地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					}

					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionLC.getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}
				
				replaceStr += CommonFunctionLC.getSwitchOnContent(curDev, stationName,station);
			}
		}
		
		return replaceStr;
	}

}
