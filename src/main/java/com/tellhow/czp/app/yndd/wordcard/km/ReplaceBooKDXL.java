package com.tellhow.czp.app.yndd.wordcard.km;

import com.tellhow.czp.app.yndd.rule.TransformKDXLChoose;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempBooleanReplace;

/** 
 * 开断线路厂站判断
 * <AUTHOR>
 * @dare 2021年9月7日下午4:26:11
 */
public class ReplaceBooKDXL implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,PowerDevice stationDev, String desc) {
		if("开断线路厂站判断".equals(tempStr)) {
			if(TransformKDXLChoose.retMap != null) {
				// String devName = curDev.getPowerDeviceName();
				String stationName = stationDev.getPowerStationName();
				boolean result = false;
				for(Object key : TransformKDXLChoose.retMap.keySet()) {
					if(String.valueOf(key).equals(stationName)) {
						result = true;
						break;
					}
				}
				return result;
			}
		}
        return false;
	}
}
