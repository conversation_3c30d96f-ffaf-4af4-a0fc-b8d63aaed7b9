package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNZNTDDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳站内停电调电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			List<PowerDevice> zbList =  new ArrayList<PowerDevice>();
			List<PowerDevice> curzbList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycmxList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+ CBSystemConstants.RunTypeSwitchFHC ,"", false, true, true, true);
			
			for(PowerDevice dev : zbkgList){
				curzbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.MotherLine,true, true, true);
			}
			
			List<PowerDevice> zbdyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbgyckgList = new ArrayList<PowerDevice>();

			double highvolt = 0;
			double midvolt = 0;
			double lowvolt = 0;
			
			if(curzbList.size()>0){
				for(PowerDevice dev : curzbList){
					zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
					zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
					zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
					
					highvolt = RuleExeUtil.getTransformerVolByType(dev, "high");
					midvolt = RuleExeUtil.getTransformerVolByType(dev, "middle");
					lowvolt = RuleExeUtil.getTransformerVolByType(dev, "low");
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			}
			
			for(PowerDevice dev : zbzyckgList){
				zycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			}
			
			for(PowerDevice dev : zbgyckgList){
				gycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			}
			
			List<PowerDevice> allzycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> alldycmlkgList =  new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getPowerVoltGrade() == midvolt){
						allzycmlkgList.add(dev);
					}else if(dev.getPowerVoltGrade() == lowvolt){
						alldycmlkgList.add(dev);
					}
				}
				
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceName().contains("接地变")){
						zbList.add(dev);
					}
				}
			}
			
			for(PowerDevice dev : dycmxList){
				replaceStr += "版纳配调@确认"+stationName+CZPService.getService().getDevName(dev)+"具备停电条件/r/n";
			}
			
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();
			List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> highVoltXlkgList = new ArrayList<PowerDevice>();
			
			for (Iterator<PowerDevice> itor = mapStationDevice.values().iterator(); itor.hasNext();) {
				PowerDevice dev = itor.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						hignVoltMlkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						highVoltXlkgList.add(dev);
					}
				}
			}
			
			tempList.addAll(hignVoltMlkgList);
			tempList.addAll(highVoltXlkgList);
			
			for(PowerDevice dev : tempList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : tempList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : dycmxList){
				replaceStr += "版纳配调@通知"+stationName+CZPService.getService().getDevName(dev)+"已带电/r/n";
			}
		}
		
		return replaceStr;
	}

}
