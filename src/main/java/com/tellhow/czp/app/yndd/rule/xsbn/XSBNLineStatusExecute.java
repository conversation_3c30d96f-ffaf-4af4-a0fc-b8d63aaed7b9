package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class XSBNLineStatusExecute implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		String beginStatus =rbm.getBeginStatus();
		String endStatus = rbm.getEndState();
		PowerDevice pd = rbm.getPd();
		//获取各侧站内线路
		
		PowerDevice lineSource = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
		List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
		
		if(lineLoad==null){
			return true;
		}
		
		lineLoad = orderByLoadline(lineLoad);

		boolean result =false;
		
		if(beginStatus.equals("0")){
			result = xl0to1(lineSource, lineLoad);
			if(!result)
				return result;
		}else if(beginStatus.equals("1") && endStatus.equals("2")) { //热备用转冷备用
			result = xl1to2(lineSource, lineLoad);
			if(!result)
				return result;
		}else if(beginStatus.equals("2") && endStatus.equals("3")) { //冷备用转检修
			xl2to3(lineSource, lineLoad);
		}else if(beginStatus.equals("2") && endStatus.equals("1")) { //冷备用转热备用
			result = xl2to1(lineSource, lineLoad);
			if(!result)
				return result;
		}else if(beginStatus.equals("3") && endStatus.equals("2")) { //检修转冷备用
			result = xl3to2(lineSource, lineLoad);
			if(!result)
				return result;
		}else if(endStatus.equals("0")) { //热备用转运行
			result = xl1to0(lineSource, lineLoad);
		}
		
		return true;
	}

	/**
	 * 线路运行转热备用
	 */
	public boolean xl0to1(PowerDevice lineSource,List<PowerDevice> lineLoad){
		//线路倒负荷及断开线路对侧开关
		for(PowerDevice line : lineLoad) {
			if(line!=null){
				boolean res = loadLine(line);
				if(!res){
					return res;
				}
				List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				this.sortListBySwMiddle(swList);
				for(PowerDevice sw : swList){
					if(sw.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "1");
					}
				}
			}
		}
		
		if(lineSource!=null){
			//断开线路开关
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
			this.sortListBySwMiddle(swList);
			for(PowerDevice sw:swList){
				if(sw.getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "1");
				}
			}
		}
		
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
		
		if(lineSource != null){
			lineList.add(lineSource);
		}
		lineList.addAll(lineLoad);
		
		for(PowerDevice line:lineList){
			if(line!=null){
				if(line.getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusSet(line, "0", "1");
				}
			}
		}
		
		return true;
	}
	/**
	 * 线路热备用转运行
	 */
	public boolean xl1to0(PowerDevice lineSource,List<PowerDevice> lineLoad){
		if(lineSource!=null){
			List<PowerDevice> swList1 = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);
			this.sortListBySwMiddle(swList1);
			Collections.reverse(swList1);
			for(PowerDevice sw:swList1){
				if(!sw.getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "0");
				}
			}
		}
		
		for(PowerDevice line : lineLoad) {
			ConvertOtherZxdOn(line);
			
			PowerDevice station = CBSystemConstants.getPowerStation(line.getPowerStationID());
			
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);
			
			this.sortListBySwMiddle(swList);
			Collections.reverse(swList);
			for(PowerDevice sw:swList){
				if(!sw.getDeviceStatus().equals("0")&&(CBSystemConstants.getLineTagStatus(sw).equals("-1")||CBSystemConstants.getLineTagStatus(sw).equals("0"))){
					RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "0");
				}
			}
			if(swList.size()>0&&line.getPowerVoltGrade()==station.getPowerVoltGrade()){
				if(swList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
					XSBNLineExecuteLoad loadExe = new XSBNLineExecuteLoad();
					loadExe.executeLoadBack(line);
				}
			}
		}	
		
		List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(lineSource);
		
		for(PowerDevice line : lineList){
			if(line.getDeviceStatus().equals("1")){
				RuleExeUtil.deviceStatusSet(line, "1", "0");
			}
		}
		
		return true;
	}
	
	
	
	
	/**
	 * 线路热备用转冷备用
	 */
	public boolean xl1to2(PowerDevice lineSource,List<PowerDevice> lineLoad){
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
		List<PowerDevice> deviceList = new ArrayList<PowerDevice>();

		lineList.add(lineSource);
		lineList.addAll(lineLoad);
		
		for(PowerDevice dev : lineList){
			List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸
			List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);

			if(xldzList.size() > 0 && xlswList.size() > 0){
				if(xldzList.size() > 0){
					deviceList.addAll(xldzList);
					deviceList.addAll(xlswList);
				}else{
					for(PowerDevice xlsw : xlswList){
						if(xlsw.getDeviceStatus().equals("1")){
							RuleExeUtil.deviceStatusExecute(xlsw, xlsw.getDeviceStatus(),"2");
						}
					}
				}
			}else if(xldzList.size() == 1 && xlswList.size() == 0){
				for(PowerDevice line:lineList){
					if(line!=null){
						if(line.getDeviceStatus().equals("0")){
							RuleExeUtil.deviceStatusSet(line, "0", "1");
						}
					}
				}
				
				for(PowerDevice xldz : xldzList){
					if(xldz.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusExecute(xldz, xldz.getDeviceStatus(),"1");
					}
				}
			}else{
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);

				for(PowerDevice zbkg : zbkgList){
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(zbkg, SystemConstants.PowerTransformer, SystemConstants.MotherLine , true, true, true);
					
					for(PowerDevice zb : zbList){
						List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(zb);

						for(PowerDevice zbdyckg : zbdyckgList){
							if(zbdyckg.getDeviceStatus().equals("1")){
								RuleExeUtil.deviceStatusExecute(zbdyckg, zbdyckg.getDeviceStatus(),"2");
							}
						}
					}
					
					if(zbkg.getDeviceStatus().equals("1")){
						RuleExeUtil.deviceStatusExecute(zbkg, zbkg.getDeviceStatus(),"2");
					}
				}
				
				for(PowerDevice xlkg : xlkgList){
					if(xlkg.getDeviceStatus().equals("1")){
						RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(),"2");
					}
				}
			}
		}
		
		if(deviceList.size() > 0){
			XSBNDeviceSelectionDialog dsd = new XSBNDeviceSelectionDialog(SystemConstants.getMainFrame(), true, deviceList, "停电");
			
			if(dsd.isCancel()){
				return false;
			}
		}
		
		return true;
	}
	
	
	/**
	 * 线路冷备用转热备用
	 */
	public boolean xl2to1(PowerDevice lineSource,List<PowerDevice> lineLoad){
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
		
		lineList.addAll(lineLoad);
		
		if(lineSource != null){
			lineList.add(lineSource);
		}
		
		for(PowerDevice line : lineList) {
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);//搜索线路开关
			
			Collections.reverse(swList);
			for(PowerDevice sw:swList){
				if(sw.getDeviceStatus().equals("3")){
					RuleExeUtil.deviceStatusChange(sw, "3", "2");
				}
				
				if(CBSystemConstants.LineTagStatus.get(sw)!=null){
					if(!CBSystemConstants.LineTagStatus.get(sw).equals("2")){
						RuleExeUtil.deviceStatusChange(sw, "2", "1");//开关转热备用
					}
				}else{
					RuleExeUtil.deviceStatusChange(sw, "2", "1");//开关转热备用
				}
			}

			PowerDevice xldz = null;
			List<PowerDevice> dzList = RuleExeUtil.getDeviceList(line, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸
			if(dzList.size()==1){
				xldz = dzList.get(0);
				
				if(xldz.getDeviceStatus().equals("1")){//合上线路侧刀闸
					RuleExeUtil.deviceStatusChange(xldz, "1", "0");
				}
			}
		}
		
		for(PowerDevice line:lineList){
			if(line.getDeviceStatus().equals("2")){
				RuleExeUtil.deviceStatusSet(line, "2", "1");
			}
		}

		return true;
	}
	
	/**
	 * 线路冷备用转检修
	 */
	public boolean xl2to3(PowerDevice lineSource,List<PowerDevice> lineLoad){
		if(lineSource!=null){
			List<PowerDevice> didao = RuleExeUtil.getDeviceDirectList(lineSource,SystemConstants.SwitchFlowGroundLine);
			if(didao.size()>0){
				for(PowerDevice dd:didao){
					RuleExeUtil.deviceStatusChange(dd, "1", "0");
				}
			}else{
				RuleExeUtil.deviceStatusSet(lineSource, "2", "3");
			}
		}
		
		//加入电源侧放最后面
		lineLoad = orderByLoadlineDesc(lineLoad);
		//对线路冷备用转检修
		for(int i=0;i<lineLoad.size();i++){
			List<PowerDevice> didaoLoad = RuleExeUtil.getDeviceDirectList(lineLoad.get(i), SystemConstants.SwitchFlowGroundLine);
			if(didaoLoad.size()>0){
				for(PowerDevice dd:didaoLoad){
					RuleExeUtil.deviceStatusChange(dd, "1", "0");
				}
			}else{
				RuleExeUtil.deviceStatusSet(lineLoad.get(i), "2", "3");
			}
		
			//线路开关根据选择器状态执行
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineLoad.get(i), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			for(PowerDevice sw:swList){
				if("3".equals(CBSystemConstants.LineTagStatus.get(sw))){
					RuleExeUtil.deviceStatusExecute(sw, "2", "3");
				}
			}
		}

		if(lineSource!=null){
			//线路开关根据选择器状态执行
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			for(PowerDevice sw:swList){
				if("3".equals(CBSystemConstants.LineTagStatus.get(sw))){
					RuleExeUtil.deviceStatusExecute(sw, "2", "3");
				}
			}
		}
		
		return true;
	}
	/**
	 * 线路检修转冷备用
	 */
	public boolean xl3to2(PowerDevice lineSource,List<PowerDevice> lineLoad){
		if(lineSource != null){
			//如果开关在检修，先转冷备用
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer,
					true, true, true);
			for(PowerDevice sw:swList){
				if(sw.getDeviceStatus().equals("3")){
					RuleExeUtil.deviceStatusChange(sw, "3", "2");
				}
			}
			
			//电源侧线路检修转冷备用
			List<PowerDevice> didaoDY = RuleExeUtil.getDeviceList(lineSource,
					SystemConstants.SwitchFlowGroundLine, SystemConstants.PowerTransformer,true, true, true);
			for(PowerDevice dd:didaoDY){
				RuleExeUtil.deviceStatusChange(dd, "0", "1");
				//关联设备状态设置
			}
		}
		
		lineLoad =orderByLoadlineDesc(lineLoad);
		//对线路检修转冷备用
		for(int i=0;i<lineLoad.size();i++){
			//如果开关在检修，先转冷备用
			List<PowerDevice> swLoadList = RuleExeUtil.getDeviceList(lineLoad.get(i), SystemConstants.Switch, SystemConstants.PowerTransformer,
					true, true, true);
			for(PowerDevice sw:swLoadList){
				if(sw.getDeviceStatus().equals("3")){
					RuleExeUtil.deviceStatusChange(sw, "3", "2");
				}
			}
			List<PowerDevice> didao = RuleExeUtil.getDeviceList(lineLoad.get(i),
					SystemConstants.SwitchFlowGroundLine, SystemConstants.PowerTransformer,true, true, true);
			if(didao.size()>0){
				for(PowerDevice dd:didao){
					RuleExeUtil.deviceStatusChange(dd, "0", "1");
				}
			}else{
				if(CommonFunction.isLineTransformerUnit(lineLoad.get(i))){
					List<PowerDevice> dzLoad = RuleExeUtil.getDeviceDirectList(lineLoad.get(i), SystemConstants.SwitchSeparate);
					if(dzLoad.size()>0){
						for(PowerDevice dz:dzLoad){
							dzLoad = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchFlowGroundLine);
							if(dzLoad.size()>0){
								for(PowerDevice dd:dzLoad){
									RuleExeUtil.deviceStatusChange(dd, "0", "1");
								}
							}
						}
					}
				}
			
				RuleExeUtil.deviceStatusSet(lineLoad.get(i), "3", "2");
			}
		}
		
		List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(lineSource);
		
		for(PowerDevice line:lineList){
			if(line.getDeviceStatus().equals("3")){
				RuleExeUtil.deviceStatusSet(line, "3", "2");
			}
		}
		
		return true;
	}
	/**
	 * 对侧线路按选择排序，无选择则按习惯排序
	 */
	public List<PowerDevice> orderByLoadline(List<PowerDevice> line){
		//线变组放前面
		for(int i=0;i<line.size();i++){
			if(line.get(i)!=null){
				PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(line.get(i));
				//识别开关是否线变组接线的主变开关
				if(lineSwitch!=null &&  lineSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
					line.add(0,line.remove(i));
				}
			}

		}
		
		return line;
	}
	
	/**
	 * （主变放后面）
	 */
	public List<PowerDevice> orderByLoadlineDesc(List<PowerDevice> line){
		//线变组放前面
		for(int i=0;i<line.size();i++){
			PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(line.get(i));
			//识别开关是否线变组接线的主变开关
			if(lineSwitch!= null && lineSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
				line.add(line.size()-1,line.remove(i));
			}
			
		}
		
		return line;
	}
	
	
	
	/**
	 * 线路倒负荷
	 * @param line
	 * @param rbm
	 * @return
	 */
	public boolean loadLine(PowerDevice line) {
		boolean result = true;
		if(!line.getDeviceStatus().equals("0"))
			return true;
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(line, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, true, true);
		
		if(tfList.size() > 0) { //如果线路直接连接主变，执行主变的主变状态转换
			for (PowerDevice tf : tfList) {
				result = loadTransformer(line, tf);
				if(!result)
					return false;
			}
		}
		else { //如果线路连接了母线，对母线倒负荷
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			for (PowerDevice ml : mlList) {
				PowerDevice station = CBSystemConstants.getPowerStation(ml.getPowerStationID());
				
				if(station.getPowerVoltGrade()<=220){
					result = loadMotherLine(line, ml);
					if(!result)
						return false;
				}
				
				List<PowerDevice> zbList = RuleExeUtil.getDeviceDirectList(ml, SystemConstants.PowerTransformer);
				
				if(zbList.size()>0){
					for (PowerDevice tf : zbList) {
						result = loadTransformer(line, tf);
						if(!result)
							return false;
					}
				}
			}
		}
		return true;
	}
	
	/**
	 * 主变倒负荷
	 * @param srcLine
	 * @param tf
	 * @return
	 */
	public boolean loadTransformer(PowerDevice srcLine, PowerDevice tf) {
		boolean result = true;
		PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(srcLine);
		//查找主变其他可能的电源点
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(tf, lineSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT, "", false, false, false, true, true);
		for(Iterator it = swList.iterator();it.hasNext();) {
			PowerDevice mlsw = (PowerDevice)it.next();
			List<PowerDevice> srcList = RuleExeUtil.getDeviceList(mlsw, lineSwitch, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, false, false, true, true);
			if(srcList.size() == 0)
				it.remove();
		}
		
		PowerDevice dev = null; 
		if(swList.size() == 1)
			dev = swList.get(0);
		else if(swList.size() > 1) {
			EquipCheckChoose ecc = new EquipCheckChoose(SystemConstants.getMainFrame(), true, swList, "请选择["+CBSystemConstants.getPowerStation(srcLine.getPowerStationID()).getPowerStationName()+"]["+srcLine.getPowerDeviceName()+"]停电前合上的开关");
			if(ecc.getChooseEquip()!=null && ecc.getChooseEquip().size() > 0) {
				dev = ecc.getChooseEquip().get(0);
			}
		}
		if(dev != null)
			RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");
		else{
			String endstate =CBSystemConstants.getCurRBM().getEndState();
			if(endstate.equals("3")){
				endstate="2";
			}
			result = RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), "1");
		}
		return result;
	}
	
	/**
	 * 母线倒负荷
	 * @param srcLine
	 * @param ml
	 * @return
	 */
	public boolean loadMotherLine(PowerDevice srcLine, PowerDevice mx) {
		PowerDevice station = CBSystemConstants.getPowerStation(mx.getPowerStationID());
		
		if(mx.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧找线路开关和分段开关
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
			
			if(mlkgList.size() > 0){//单母分段
				List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();

				for(PowerDevice dev : mlkgList){
					if(!dev.getDeviceStatus().equals("0")){
						List<PowerDevice> list = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
						xlkgList.addAll(list);
					}
				}
				
				List<PowerDevice> lineList = new ArrayList<PowerDevice>();
				
				for(PowerDevice dev : xlkgList){
					if(dev.getDeviceStatus().equals("0")){
						List<PowerDevice> list = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
						lineList.addAll(list);
					}
				}
				
				boolean issource = false;
				
				for(PowerDevice dev : lineList){
					if(!dev.getPowerDeviceID().equals(srcLine.getPowerDeviceID())){
						List<PowerDevice> otherlineList = RuleExeUtil.getLineOtherSideList(dev);
						
						for(PowerDevice otherline : otherlineList){
							PowerDevice otherstation = CBSystemConstants.getPowerStation(otherline.getPowerStationID());
							
							if(otherstation.getPowerVoltGrade() > station.getPowerVoltGrade()){
								issource = true;
								break;
							}
						}
					}
				}
				
				if(!issource){
					for(PowerDevice dev : mlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
				}
			}else{//单母接线
				List<PowerDevice> xlkgList= new ArrayList<PowerDevice>();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(mx.getPowerStationID());

				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getDeviceStatus().equals("1")&&!dev.getPowerDeviceName().contains("备用")){
						if(dev.getPowerVoltGrade() == mx.getPowerVoltGrade()){
							xlkgList.add(dev);
						}
					}
				}
				
				if(xlkgList.size()>1){
					EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, xlkgList , "请选择用来合环的断路器：");
					List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

					if(ecc.isCancel()){
						return false;
					}
					
					RuleExeUtil.deviceStatusExecute(choosedyckgList.get(0), choosedyckgList.get(0).getDeviceStatus(), "0");
				}else if(xlkgList.size()==1){
					RuleExeUtil.deviceStatusExecute(xlkgList.get(0), xlkgList.get(0).getDeviceStatus(), "0");
				}
			}
		}else if(mx.getPowerVoltGrade() < station.getPowerVoltGrade()){//负荷侧找主变开关和分段开关
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
			
			if(mlkgList.size() > 0){//单母分段
				List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();

				for(PowerDevice dev : mlkgList){
					if(!dev.getDeviceStatus().equals("0")){
						List<PowerDevice> list = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
						xlkgList.addAll(list);
					}
				}
				
				List<PowerDevice> lineList = new ArrayList<PowerDevice>();
				
				for(PowerDevice dev : xlkgList){
					if(dev.getDeviceStatus().equals("0")){
						List<PowerDevice> list = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
						lineList.addAll(list);
					}
				}
				
				boolean issource = false;
				
				for(PowerDevice dev : lineList){
					if(!dev.getPowerDeviceID().equals(srcLine.getPowerDeviceID())){
						List<PowerDevice> otherlineList = RuleExeUtil.getLineOtherSideList(dev);
						
						for(PowerDevice otherline : otherlineList){
							PowerDevice otherstation = CBSystemConstants.getPowerStation(otherline.getPowerStationID());
							
							if(otherstation.getPowerVoltGrade() > station.getPowerVoltGrade()){
								issource = true;
								break;
							}
						}
					}
				}
				
				if(!issource){
					for(PowerDevice dev : mlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
				}
			}else{
				List<PowerDevice> zbkgList= new ArrayList<PowerDevice>();
				
				for(PowerDevice dev : zbkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
		}
		
		return true;
	}
	/**
	 * 按中间开关排序，中间开关放最前面
	 */
	public static void sortListBySwMiddle(List<PowerDevice> swlist){
		if(swlist==null){
			return;
		}else{
		    Collections.sort(swlist, new Comparator<PowerDevice>() {
		        @Override
		        public int compare(PowerDevice p1, PowerDevice p2) {
		          if(RuleExeUtil.isSwMiddleInThreeSecond(p1)) {
		            return -1;
		          }
		          else {
		            return 1;
		          }
		        }
		    });
		}
	}
	/**
	 * 按刀闸排序，母线刀闸放后面
	 */
	public static void sortDzListByMXDZ(List<PowerDevice> dzlist){
		if(dzlist.size() ==2){
			PowerDevice pd1 = dzlist.get(0);
			PowerDevice pd2 = dzlist.get(1);
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd1, pd2, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "",
				false, true, true, true, true);
			if(lineList.size()==0){
				Collections.reverse(dzlist);
			}
		}
	}
	
	//合上并列运行主变的中性点地刀
	public boolean ConvertOtherZxdOn(PowerDevice pd) {
		List<PowerDevice> list = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, false, true);
			
		if(list.size()>1){
			for(PowerDevice zb : list){
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(zb, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				for(PowerDevice gd : gdList) {
					RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
				}
			}
		}
		
		List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		for(PowerDevice gd : gdList) {
			RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
		}
		
		return true;
	}
}
