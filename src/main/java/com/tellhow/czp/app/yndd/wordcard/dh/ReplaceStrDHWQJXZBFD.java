package com.tellhow.czp.app.yndd.wordcard.dh;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dh.DHHaveWorkDialog;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDHWQJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("德宏外桥接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			replaceStr += CommonFunctionDH.getPowerOnCheckContent();
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();

			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				for(PowerDevice dev : zbgyckgList){
					if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
						replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						replaceStr += CommonFunctionDH.getKnifeOnCheckContent(dzList, stationName);
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
			
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> zycdzList = CommonFunctionDH.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunctionDH.getKnifeOnContent(zycdzList,stationName);
					
					if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
						replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						replaceStr += CommonFunctionDH.getKnifeOnCheckContent(dzList, stationName);
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					List<PowerDevice> dycdzList = CommonFunctionDH.getTransformerKnife(curDev, dev);
					
					for (Iterator<PowerDevice> it = dycdzList.iterator(); it.hasNext();) {
						PowerDevice dz = it.next();
						
						if(dz.getPowerDeviceName().endsWith("1")){
							it.remove();
						}
					}
					
					replaceStr += CommonFunctionDH.getKnifeOnContent(dycdzList,stationName);
					
					if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
						replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						replaceStr += CommonFunctionDH.getKnifeOnCheckContent(dzList, stationName);
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("0")){
				for(PowerDevice dev : zxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控合上"+stationName+devName+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "德宏地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对主变充电/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += CommonFunctionDH.getHhContent(dev, "德宏地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						String dycmxName = "";
						
						for(PowerDevice dycmx : dycmxList){
							dycmxName = CZPService.getService().getDevName(dycmx);
						}
						
						replaceStr += "德宏地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+dycmxName+"充电/r/n";
					}
				}
				
				for(PowerDevice dev : zxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控拉开"+stationName+devName+"/r/n";
					}
				}
				
				for(PowerDevice dev : otherzxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控拉开"+stationName+devName+"/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
}
