package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXKDXLFDCZRW implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪开断线路复电操作任务".equals(tempStr)){
			List<PowerDevice> list = RuleExeUtil.getLineAllSideList(curDev);
			
			RuleExeUtil.swapDeviceByHighVoltList(list);
			
			if(list.size()>0){
				for(PowerDevice pd : list){
					List<PowerDevice> swlist =  RuleExeUtil.getLinkedSwitch(pd);
					
					replaceStr += CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pd.getPowerStationID()))+CZPService.getService().getDevName(swlist.get(0))+"、";
				}
			}
			
			if(replaceStr.endsWith("、")){
				replaceStr = replaceStr.substring(0, replaceStr.length()-1);
			}
			
			replaceStr += "至"+CZPService.getService().getDevName(curDev)+"#XX塔段线路由检修转运行";
		}
		return replaceStr;
	}
	
}
