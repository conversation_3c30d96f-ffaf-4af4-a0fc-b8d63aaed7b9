/*
 * BHHistory.java
 *
 * Created on 2010年5月7日, 下午4:29
 */

package com.tellhow.czp.app.yndd.view;

import java.awt.Toolkit;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.swing.table.DefaultTableModel;

import czprule.system.DBManager;

/**
 *
 * <AUTHOR>
 */
public class BHHistory extends javax.swing.JDialog {
    private DefaultTableModel dTableModelZX;  //逐项票
    private DefaultTableModel dTableModelZL;  //综令票
    
    /** Creates new form BHHistory */
    public BHHistory() {
        initComponents();
        this.setTitle("操作票票号历史记录");
        this.setLocationCenter();
        this.initTable();
    }
    
    /** This method is called from within the constructor to
     * initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is
     * always regenerated by the Form Editor.
     */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jTabbedPane3 = new javax.swing.JTabbedPane();
        jTabbedPane1 = new javax.swing.JTabbedPane();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        jScrollPane2 = new javax.swing.JScrollPane();
        jTable2 = new javax.swing.JTable();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

        jTabbedPane1.setToolTipText("");
        jTabbedPane1.addChangeListener(new javax.swing.event.ChangeListener() {
            public void stateChanged(javax.swing.event.ChangeEvent evt) {
                changAction(evt);
            }
        });

        jScrollPane1.setViewportView(jTable1);

        jTabbedPane1.addTab("逐项票", jScrollPane1);

        jScrollPane2.setViewportView(jTable2);

        jTabbedPane1.addTab("综令票", jScrollPane2);

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .addContainerGap()
                .add(jTabbedPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 311, Short.MAX_VALUE)
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .addContainerGap()
                .add(jTabbedPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 265, Short.MAX_VALUE)
                .addContainerGap())
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void changAction(javax.swing.event.ChangeEvent evt) {//GEN-FIRST:event_changAction
        // TODO add your handling code here:
    //    this.initTable();
    }//GEN-LAST:event_changAction
    
    /**
     * @param args the command line arguments
     */
/*    public static void main(String args[]) {
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                BHHistory dialog = new BHHistory();
                dialog.addWindowListener(new java.awt.event.WindowAdapter() {
                    public void windowClosing(java.awt.event.WindowEvent e) {
                        System.exit(0);
                    }
                });
                dialog.setVisible(true);
            }
        });
    }*/
        /**
     * 屏幕中央位置
     */
    public void setLocationCenter() {
        int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
        int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
        this.setLocation((w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
    }
    private void initTable(){
        Object[][] body = null;
        String[] head = new String[]{"票号", "设置人", "设置时间"};
        dTableModelZX = new DefaultTableModel(body, head) {
            public boolean isCellEditable(int rowIndex, int columnIndex) {
                return false;
            }
        };
        dTableModelZL = new DefaultTableModel(body, head) {
            public boolean isCellEditable(int rowIndex, int columnIndex) {
                return false;
            }
        };
        try {
            Connection conn = DBManager.getConnection();
            Statement stem = conn.createStatement();
            ResultSet rs=null;
            String bh="";   
            String userName="";
            String upTime="";
             //逐项票
            String sql="select t.cardno,to_char(t.carddate,'yyyy-MM-dd hh24:mi:ss') uptime from t_a_czpno t where t.unitcode=1 and t.cardkind=1 order by t.carddate";
            rs=stem.executeQuery(sql);
            while(rs.next()){
               Object[] rowData = null;
               bh=rs.getString("cardno");
//               userName=rs.getString("username");
               upTime=rs.getString("uptime");
               rowData=new Object[]{bh,userName,upTime};
               dTableModelZX.addRow(rowData);
            }
            if(dTableModelZX.getRowCount()==0)
                 dTableModelZX.addRow(new Object[]{null,null,null});
            jTable1.setModel(dTableModelZX);
            jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
            jTable1.getColumnModel().getColumn(1).setMaxWidth(70);
            
            //综令票
            sql="select t.cardno,to_char(t.carddate,'yyyy-MM-dd hh24:mi:ss') uptime from t_a_czpno t where t.unitcode=1 and t.cardkind=0 order by t.carddate";
            rs=stem.executeQuery(sql);
            while(rs.next()){
               Object[] rowData = null;
               bh=rs.getString("cardno");
//               userName=rs.getString("username");
               upTime=rs.getString("uptime");
               rowData=new Object[]{bh,userName,upTime};
               dTableModelZL.addRow(rowData);
            }
            if(dTableModelZL.getRowCount()==0) 
                 dTableModelZL.addRow(new Object[]{null,null,null});
            jTable2.setModel(dTableModelZL);
            jTable2.getColumnModel().getColumn(0).setMaxWidth(50);
            jTable2.getColumnModel().getColumn(1).setMaxWidth(70); 
            
        } catch (SQLException ex) {
            Logger.getLogger(BHHistory.class.getName()).log(Level.SEVERE, null, ex);
        }
    
    
    
    }
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JScrollPane jScrollPane2;
    private javax.swing.JTabbedPane jTabbedPane1;
    private javax.swing.JTabbedPane jTabbedPane3;
    private javax.swing.JTable jTable1;
    private javax.swing.JTable jTable2;
    // End of variables declaration//GEN-END:variables
    
}
