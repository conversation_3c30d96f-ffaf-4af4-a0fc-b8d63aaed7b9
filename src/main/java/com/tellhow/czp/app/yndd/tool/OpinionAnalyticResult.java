package com.tellhow.czp.app.yndd.tool;

import java.util.List;

import czprule.model.PowerDevice;

/**
 * @description: 运方意见解析结果实体类
 * @author: hxy
 * @create: 2024-07-10 17:26
 **/

public class OpinionAnalyticResult {
    // 指令序号
    int sort;

    //所属场站
    PowerDevice station;

    // 操作设备
    List<PowerDevice> powerDeviceList;

	// 具体指令
    String directive;
	
    // 备注
    String remark;

    // 仅生成备注
    boolean remarksOnly;

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public PowerDevice getStation() {
        return station;
    }

    public void setStation(PowerDevice station) {
        this.station = station;
    }

    public String getDirective() {
        return directive;
    }

    public void setDirective(String directive) {
        this.directive = directive;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public boolean isRemarksOnly() {
        return remarksOnly;
    }

    public void setRemarksOnly(boolean remarksOnly) {
        this.remarksOnly = remarksOnly;
    }
    
    public List<PowerDevice> getPowerDeviceList() {
		return powerDeviceList;
	}

	public void setPowerDeviceList(List<PowerDevice> powerDeviceList) {
		this.powerDeviceList = powerDeviceList;
	}
}
