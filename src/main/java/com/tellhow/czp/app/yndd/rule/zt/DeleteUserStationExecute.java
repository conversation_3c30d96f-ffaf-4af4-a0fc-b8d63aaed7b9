package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.view.EquipCheckChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class DeleteUserStationExecute implements RulebaseInf {
	
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		
		List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
		
		String sql = "SELECT UNIT,LOWERUNIT FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE  LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+pd.getPowerDeviceID()+"')";
		List<Map<String, Object>> stations = DBManager.queryForList(sql);
		
		List<String> userstations = new ArrayList<String>();
		
		if(stations.size()>0){
			for(Map<String, Object> map : stations){
				String stationName = String.valueOf(map.get("UNIT")).trim();
				String lowerUnit = String.valueOf(map.get("LOWERUNIT")).trim();

				userstations.add(stationName);
				userstations.add(lowerUnit);
			}
		}
		
		for(Iterator<PowerDevice> itor = lineLoad.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			if(dev.getPowerDeviceName().equals("五坪线")){//五坪线特殊判断
				itor.remove();
				continue;
			}
			
			for(String userstation : userstations){
				if(dev != null){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					if(stationName.equals("110kV得云水泥厂")){
						stationName = "110kV得云水泥厂变";
					}else if(stationName.equals("35kV昊龙水泥磨粉厂")){
						stationName = "35kV昊龙水泥磨粉厂变";
					}
					
					if(stationName.contains(userstation)
							||userstation.contains(dev.getPowerStationName())){
						itor.remove();
						break;
					}
				}
			}
		}
	    
		return true;
	}

}
