package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Toolkit;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.table.DefaultTableModel;
import javax.swing.text.JTextComponent;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.mainframe.JPopupTextField;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;



public class UserStationAddDialog extends javax.swing.JDialog{
	private JPanel topPanel;
	private JPanel mainPanel;
	private JTable jTable1;
	private JButton saveButton;//查询按钮
	private JComboBox czComboBox;
	private JComboBox equipComboBox;
	private JTextArea remindMemo;
	private JScrollPane jScrollPane;
	private DefaultTableModel jTable1Model = null;
	public static JTextComponent tjc;
	private String curLineId;
	private String curKind;
	
	public UserStationAddDialog(javax.swing.JDialog parent, boolean modal,String lineid,String kind) {
		super(parent, modal);
		this.setTitle(kind);
		curLineId = lineid;
		curKind = kind;
		initComponents(lineid,kind);
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}



	
	private void initComponents(String lineid,String kind) {
		this.setLayout(new BorderLayout());
		this.setSize(1100, 400);
		topPanel =new JPanel();
		this.add(topPanel,BorderLayout.NORTH);
		mainPanel =new JPanel();
		this.add(mainPanel,BorderLayout.CENTER);
		jScrollPane = new JScrollPane();
		jScrollPane.setBounds(40, 90, 1000, 200);
		topPanel.setLayout(new FlowLayout(FlowLayout.RIGHT,15,5));
		
		saveButton =new JButton();
		saveButton.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
		saveButton.setText("保存");
		saveButton.setToolTipText("保存");
		saveButton.setMargin(new java.awt.Insets(1,1,1,1));
		saveButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveButtonActionPerformed(evt);
			}
		});
		
		JButton addjButton = new JButton();
		addjButton.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
		addjButton.setText("增加行");
		addjButton.setToolTipText("增加行");
		addjButton.setMargin(new java.awt.Insets(1,1,1,1));
        addjButton.setFocusPainted(false);
        addjButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });
		
        JButton deletejButton = new JButton();
        deletejButton.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
        deletejButton.setText("删除行");
        deletejButton.setToolTipText("删除行");
        deletejButton.setMargin(new java.awt.Insets(1,1,1,1));
        deletejButton.setFocusPainted(false);
        deletejButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });
        
        if(!kind.equals("查看")){
        	topPanel.add(addjButton);
        	topPanel.add(deletejButton);
    		topPanel.add(saveButton);
        }

		mainPanel.setLayout(null);
		JLabel jLabel1= new JLabel("线路名称：");
		jLabel1.setBounds(40, 40, 80, 26);
		
		//设置不可编辑单元格
		jTable1 = new JTable();
		JPopupTextField jtf = new JPopupTextField();
		jtf.setFont(new Font("宋体",Font.PLAIN,14));
		DefaultCellEditor editor = new DefaultCellEditor(jtf);
		jTable1.setDefaultEditor(Object.class, editor);
		jTable1.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		jTable1.setPreferredScrollableViewportSize(new Dimension(450, 200));
		jTable1.setFont(new Font("宋体",Font.PLAIN,14)); // NOI18N	
        jTable1.setRowHeight(30);
		Object[][] tableDate = null;
		
		if (jTable1Model == null)
			jTable1Model = new DefaultTableModel(tableDate, new String[] {"ID","电压等级","断路器名称","刀闸名称","接地刀闸名称","端点类型","操作项目类别","受令单位","下级单位","电源侧线路"});
		
		jTable1.setModel(jTable1Model);
		
		String sql = "SELECT ID,LINE_NAME,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,OPERATION_KIND,UNIT,VOLTAGE,LOWERUNIT,SOURCE FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER T WHERE LINE_ID = '"+lineid+"'";
		List<Map<String,String>> results= DBManager.queryForList(sql);
		
		if(results.size()>0){
			for(int i = 0; i < results.size(); i++){
				Map map=(Map)results.get(i);
				 
				String id = StringUtils.ObjToString(map.get("ID"));
				String voltage = StringUtils.ObjToString(map.get("VOLTAGE"));
				String kgname = StringUtils.ObjToString(map.get("SWITCH_NAME"));
				String dzname = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));
				String ddname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));
				String endpoint = StringUtils.ObjToString(map.get("ENDPOINT_KIND"));
				String operation = StringUtils.ObjToString(map.get("OPERATION_KIND")); 
				String unit = StringUtils.ObjToString(map.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));  
				String source = StringUtils.ObjToString(map.get("SOURCE"));  

				if(source.equals("1")){
					source = "是";
				}else{
					source = "否";
				}
				
				Object[] rowData;
				rowData = new Object[]{id,voltage,kgname,dzname,ddname,endpoint,operation,unit,lowerunit,source};
				jTable1Model.addRow(rowData);
			}
		} 
		
		jTable1.getColumnModel().getColumn(0).setMinWidth(0);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(1).setMinWidth(100);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(110);
		jTable1.getColumnModel().getColumn(2).setMinWidth(100);
		jTable1.getColumnModel().getColumn(2).setMaxWidth(110);
		jTable1.getColumnModel().getColumn(3).setMinWidth(100);
		jTable1.getColumnModel().getColumn(3).setMaxWidth(110);
		jTable1.getColumnModel().getColumn(4).setMinWidth(100);
		jTable1.getColumnModel().getColumn(4).setMaxWidth(110);
		jTable1.getColumnModel().getColumn(7).setMinWidth(80);
		jTable1.getColumnModel().getColumn(7).setMaxWidth(90);
		
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		// sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);
		
        jScrollPane.setViewportView(jTable1);
		mainPanel.add(jLabel1);

        if(kind.equals("查看")){
        	JTextField text = new JTextField();
        	text.setBounds(130, 40, 240, 26);
        	String sql1 ="SELECT ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND WHERE ACLINE_ID = '"+lineid+"'";
        	
        	List<Map<String,String>> list = DBManager.query(sql1);
        	
        	if(list.size()>0){
            	text.setText(CZPService.getService().getDevName(CBSystemConstants.getPowerDevice(list.get(0).get("ID"))));
        	}
        	         	text.setEditable(false);
        	mainPanel.add(text);
        	jTable1.setEnabled(false);
		}else if(kind.equals("修改")){
			JTextField text = new JTextField();
        	text.setBounds(130, 40, 240, 26);
        	
        	String sql1 ="SELECT ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND WHERE ACLINE_ID = '"+lineid+"'";
        	
        	List<Map<String,String>> list = DBManager.query(sql1);
        	
        	if(list.size()>0){
            	text.setText(CZPService.getService().getDevName(CBSystemConstants.getPowerDevice(list.get(0).get("ID"))));
        	}
        	 
        	text.setEditable(false);
        	mainPanel.add(text);
        	jTable1.setEnabled(true);
		}else{
			equipComboBox = new JAutoCompleteComboBox();
			
			String sql2 = "SELECT ID AS LINEID ,NAME AS LINENAME FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND";
			List<Map<String,String>> sqllist=DBManager.queryForList(sql2);
			
			DefaultComboBoxModel model = new DefaultComboBoxModel();
			for(int i=0;i<sqllist.size();i++){
				Map<String,String> temp=sqllist.get(i);
				String code = StringUtils.ObjToString(temp.get("LINEID"));
				String name = StringUtils.ObjToString(temp.get("LINENAME"));
				CodeNameModel cnm=new CodeNameModel(code,name);
				model.addElement(cnm);
			}
			equipComboBox.setModel(model);
			
			equipComboBox.setBounds(130, 40, 240, 26);
			equipComboBox.setFont(new java.awt.Font("宋体", 0, 13));
			equipComboBox.setSelectedIndex(-1);
        	jTable1.setEnabled(true);
			mainPanel.add(equipComboBox);
		}
        
		mainPanel.add(jScrollPane);
	}
	//获取修改界面信息
	public void getSelectDfsInfo(String dfsId) {
		String sql = "select czcz,czequip,remindmemo from opcard.t_a_dfsremind where zbid ='"+dfsId+"'";
		List sqllist=DBManager.queryForList(sql);
		Map temp=(Map) sqllist.get(0);
		String czid=StringUtils.ObjToString(temp.get("czcz"));
		String equipid=StringUtils.ObjToString(temp.get("czequip"));
		for(int i=0;i<czComboBox.getItemCount();i++){
			CodeNameModel codeNameModel=(CodeNameModel) czComboBox.getItemAt(i);
			if(czid.equals(codeNameModel.getCode())){
				czComboBox.setSelectedItem(new CodeNameModel(czid,codeNameModel.getName()));
				break;
			}
		}
		for(int i=0;i<equipComboBox.getItemCount();i++){
			CodeNameModel codeNameModel=(CodeNameModel) equipComboBox.getItemAt(i);
			if(equipid.equals(codeNameModel.getCode())){
				equipComboBox.setSelectedItem(new CodeNameModel(equipid,codeNameModel.getName()));
				break;
			}
		}
		
		remindMemo.setText(StringUtils.ObjToString(temp.get("remindmemo")));
	}
	
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要删除的记录！");
			return;
		}
		
		int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"是否确认删除选中内容？",
				CBSystemConstants.SYSTEM_TITLE,
				JOptionPane.YES_NO_OPTION);
		if (isok != JOptionPane.YES_OPTION)
			return;
		WindowUtils.removeTableRow(jTable1);
	}
	
	//保存
	private void saveButtonActionPerformed(java.awt.event.ActionEvent evt) {
		//单元格编辑状态下点击保存
		if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
		if(curLineId.equals("")){
			int num = equipComboBox.getSelectedIndex();
			CodeNameModel codeNameModel=(CodeNameModel) equipComboBox.getItemAt(num);
			
			if(codeNameModel == null){
				ShowMessage.view("请填写线路名称");
				return;
			}
			
			curLineId = codeNameModel.getCode();

			String sql1 = "SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND WHERE ID ='"+curLineId+"'";
			List<Map<String,String>> list = DBManager.queryForList(sql1);
			
			
			if(list.size()>0){
				curLineId = StringUtils.ObjToString(list.get(0).get("ACLINE_ID"));
			}
		}
		
		String lineName = CZPService.getService().getDevName(CBSystemConstants.getPowerDevice(curLineId));
		
		if(curKind.equals("新增")){
			if(jTable1Model.getRowCount() == 0){
				ShowMessage.view("请填写用户站相关信息");
				return;
			}
			
			for(int i=0;i<jTable1Model.getRowCount();i++){
				String dydj = StringUtils.ObjToString(jTable1Model.getValueAt(i, 1));
				String kgname = StringUtils.ObjToString(jTable1Model.getValueAt(i, 2));//可以为空
				String dzname = StringUtils.ObjToString(jTable1Model.getValueAt(i, 3));//可以为空
				String ddname = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4));//可以为空
				String ddkind = StringUtils.ObjToString(jTable1Model.getValueAt(i, 5));
				String lb = StringUtils.ObjToString(jTable1Model.getValueAt(i, 6));
				String sldw = StringUtils.ObjToString(jTable1Model.getValueAt(i, 7));
				String xjsldw = StringUtils.ObjToString(jTable1Model.getValueAt(i, 8));
				String source = StringUtils.ObjToString(jTable1Model.getValueAt(i, 9));

				if(source.equals("是")){
					source = "1";
				}else{
					source = "0";
				}
				
				int rownum = i+1;
				
				if(dydj.equals("")){
					ShowMessage.view("第"+rownum+"行电压等级不可为空");
					return;
				}
				
				if(dydj.length()>5){
					ShowMessage.view("第"+rownum+"行电压等级不可超过5个字符");
					return;
				}
				
				if(kgname.length()>30){
					ShowMessage.view("第"+rownum+"行设备编号不可超过30个字符");
					return;
				}
				
				
				if(ddkind.equals("")){
					ShowMessage.view("第"+rownum+"行端点类型不可为空");
					return;
				}
				
				if(ddkind.length()>30){
					ShowMessage.view("第"+rownum+"行端点类型不可超过30个字符");
					return;
				}
				
				if(lb.equals("")){
					ShowMessage.view("第"+rownum+"行操作项目类别不可为空");
					return;
				}
				
				if(lb.length()>5){
					ShowMessage.view("第"+rownum+"行操作项目类别不可超过5个字符");
					return;
				}
				
				if(sldw.equals("")){
					ShowMessage.view("第"+rownum+"行受令单位不可为空");
					return;
				}
				
				if(sldw.length()>30){
					ShowMessage.view("第"+rownum+"行受令单位不可超过30个字符");
					return;
				}
				
				if(xjsldw.length()>30){
					ShowMessage.view("第"+rownum+"行下级单位不可超过30个字符");
					return;
				}
				
				String sql3 = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER (ID, LINE_ID,LINE_NAME,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,OPERATION_KIND,UNIT,VOLTAGE,LOWERUNIT,SOURCE)"
						+ " VALUES  ('"+UUID.randomUUID()+"', '"+curLineId+"','"+lineName+"','"+kgname+ "','"+dzname+ "','"+ddname+ "','"+ddkind+ "','"+lb+ "','"
						+sldw+ "','"+dydj+ "','"+xjsldw+ "','"+source+"')";
				DBManager.execute(sql3);
				
				ShowMessage.view("保存成功！");
			}
		}else if(curKind.equals("修改")){
			for(int i=0;i<jTable1Model.getRowCount();i++){
				String id = "";
				String dydj = StringUtils.ObjToString(jTable1Model.getValueAt(i, 1));
				String num = StringUtils.ObjToString(jTable1Model.getValueAt(i, 2));
				String dzname = StringUtils.ObjToString(jTable1Model.getValueAt(i, 3));//可以为空
				String ddname = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4));//可以为空
				String ddkind = StringUtils.ObjToString(jTable1Model.getValueAt(i, 5));
				String lb = StringUtils.ObjToString(jTable1Model.getValueAt(i, 6));
				String sldw = StringUtils.ObjToString(jTable1Model.getValueAt(i, 7));
				String xjsldw = StringUtils.ObjToString(jTable1Model.getValueAt(i, 8));
				String source = StringUtils.ObjToString(jTable1Model.getValueAt(i, 9));

				if(source.equals("是")){
					source = "1";
				}else{
					source = "0";
				}
				
				int rownum = i+1;
				
				if(dydj.equals("")){
					ShowMessage.view("第"+rownum+"行电压等级不可为空");
					return;
				}
				
				if(dydj.length()>5){
					ShowMessage.view("第"+rownum+"行电压等级不可超过5个字符");
					return;
				}
				
				if(num.length()>30){
					ShowMessage.view("第"+rownum+"行设备编号不可超过30个字符");
					return;
				}
				
				
				if(ddkind.equals("")){
					ShowMessage.view("第"+rownum+"行端点类型不可为空");
					return;
				}
				
				if(ddkind.length()>30){
					ShowMessage.view("第"+rownum+"行端点类型不可超过30个字符");
					return;
				}
				
				if(lb.equals("")){
					ShowMessage.view("第"+rownum+"行操作项目类别不可为空");
					return;
				}
				
				if(lb.length()>5){
					ShowMessage.view("第"+rownum+"行操作项目类别不可超过5个字符");
					return;
				}
				
				if(sldw.equals("")){
					ShowMessage.view("第"+rownum+"行受令单位不可为空");
					return;
				}
				
				if(sldw.length()>30){
					ShowMessage.view("第"+rownum+"行受令单位不可超过30个字符");
					return;
				}
				
				if(xjsldw.length()>30){
					ShowMessage.view("第"+rownum+"行下级单位不可超过30个字符");
					return;
				}
				
				if(jTable1Model.getValueAt(i, 0) == null){
					String sql3 = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER (ID, LINE_ID,LINE_NAME,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,OPERATION_KIND,UNIT,VOLTAGE,LOWERUNIT,SOURCE)"
							+ " VALUES  ('"+UUID.randomUUID()+"', '"+curLineId+"','"+lineName+"','"+num+ "','"+ddkind+ "','"+lb+ "','"
							+sldw+ "','"+dydj+ "','"+xjsldw+ "','"+source+"')";
					DBManager.execute(sql3);
				}else{
					id = jTable1Model.getValueAt(i, 0).toString();
				}

				String sql3 = " UPDATE "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER SET SWITCH_NAME = '"+num+"',DISCONNECTOR_NAME = '"+dzname+"',GROUNDDISCONNECTOR_NAME = '"+ddname+"', ENDPOINT_KIND = '"+ddkind+"', OPERATION_KIND = '"+lb+"',UNIT = '"+sldw+ "',VOLTAGE = '"+dydj+ "',LOWERUNIT= '"+xjsldw+ "',SOURCE = '"+source+"' WHERE ID = '"+id+ "'";
				DBManager.execute(sql3);
				
				ShowMessage.view("修改成功！");
			}
		}
		
		this.setVisible(false);
		this.dispose();
		
	}
	
	//新增
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		WindowUtils.addTableRow(jTable1);
	}
	
	public void paint(Graphics g) {
		super.paint(g);
		Graphics2D g_2d = (Graphics2D) g;
		g_2d.setColor(Color.GRAY);
		g_2d.drawLine(20, 75, this.getSize().width - 20, 75);

	}

}