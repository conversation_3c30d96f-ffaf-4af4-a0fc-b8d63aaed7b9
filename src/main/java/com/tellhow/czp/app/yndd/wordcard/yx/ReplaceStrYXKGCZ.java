package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXKGCZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪开关操作".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			String begin = CBSystemConstants.getCurRBM().getBeginStatus();
			String end = CBSystemConstants.getCurRBM().getEndState();

			if(curDev.getPowerVoltGrade() == 500){
				boolean ifSwitchControl = false;
				boolean ifSwitchSeparateControl = false;

				if(CommonFunction.ifSwitchControl(curDev)){
					ifSwitchControl = true;
				}
				
				if(CommonFunction.ifSwitchSeparateControl(curDev)){
					ifSwitchSeparateControl = true;
				}
				
				if(begin.equals("0")){
					if(end.equals("1")){
						replaceStr += "玉溪地调@遥控断开"+stationName+deviceName+"/r/n";
					}else if(end.equals("2")){
						if(ifSwitchControl && ifSwitchSeparateControl){
							replaceStr += "玉溪地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
						}else{
							replaceStr += "玉溪地调@遥控断开"+stationName+deviceName+"/r/n";
							
							if(ifSwitchSeparateControl){
								replaceStr += "玉溪地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
							}else{
								replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用";
							}
						}
					}
				}else if(begin.equals("1")){
					String deviceNum = CZPService.getService().getDevNum(curDev);
					boolean isCenterSwitch = false;
					
					if(deviceNum.endsWith("2")){
						isCenterSwitch = true;
					}
					
					if(end.equals("0")){
						if(ifSwitchControl){
							replaceStr += "玉溪地调@遥控断开"+stationName+deviceName+"/r/n";
						}else{
							if(isCenterSwitch){
								replaceStr += "玉溪地调@核实"+stationName+deviceName+"检同期功能压板已投入/r/n";
								replaceStr += CommonFunction.getHhContent(curDev, "玉溪地调", stationName);
							}else{
								replaceStr += "玉溪地调@遥控退出"+stationName+deviceName+"检同期功能压板/r/n";
								replaceStr += "玉溪地调@遥控投入"+stationName+deviceName+"检无压功能压板/r/n";
								
								if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									replaceStr += "玉溪地调@遥控合上"+stationName+deviceName+"对线路充电/r/n";
								}else{
									replaceStr += "玉溪地调@遥控合上"+stationName+deviceName+"对主变充电/r/n";
								}
							}
						}
					}else if(end.equals("2")){
						if(ifSwitchSeparateControl){
							replaceStr += "玉溪地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
						}else{
							replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用";
						}
					}
				}else if(begin.equals("2")){
					if(end.equals("1")){
						if(ifSwitchSeparateControl){
							replaceStr += "玉溪地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用";
						}
					}else if(end.equals("0")){
						if(ifSwitchControl && ifSwitchSeparateControl){
							replaceStr += "玉溪地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
						}else{
							if(ifSwitchSeparateControl){
								replaceStr += "玉溪地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
							}else{
								replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用";
							}
							
							replaceStr += "玉溪地调@遥控合上"+stationName+deviceName+"/r/n";
						}
					}
				}
			}else{
				boolean ifSwitchControl = false;
				boolean ifSwitchSeparateControl = false;

				if(CommonFunction.ifSwitchControl(curDev)){
					ifSwitchControl = true;
				}
				
				if(CommonFunction.ifSwitchSeparateControl(curDev)){
					ifSwitchSeparateControl = true;
				}
				
				if(begin.equals("0")){
					if(end.equals("1")){
						replaceStr += "玉溪地调@遥控断开"+stationName+deviceName+"/r/n";
					}else if(end.equals("2")){
						if(ifSwitchControl && ifSwitchSeparateControl){
							replaceStr += "玉溪地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
						}else{
							replaceStr += "玉溪地调@遥控断开"+stationName+deviceName+"/r/n";
							
							if(ifSwitchSeparateControl){
								replaceStr += "玉溪地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
							}else{
								replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用";
							}
						}
					}
				}else if(begin.equals("1")){
					if(end.equals("0")){
						replaceStr += "玉溪地调@遥控合上"+stationName+deviceName+"/r/n";
					}else if(end.equals("2")){
						if(ifSwitchSeparateControl){
							replaceStr += "玉溪地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
						}else{
							replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由热备用转冷备用";
						}
					}
				}else if(begin.equals("2")){
					if(end.equals("1")){
						if(ifSwitchSeparateControl){
							replaceStr += "玉溪地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用";
						}
					}else if(end.equals("0")){
						if(ifSwitchControl && ifSwitchSeparateControl){
							replaceStr += "玉溪地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
						}else{
							if(ifSwitchSeparateControl){
								replaceStr += "玉溪地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
							}else{
								replaceStr += "将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用";
							}
							
							replaceStr += "玉溪地调@遥控合上"+stationName+deviceName+"/r/n";
						}
					}
				}
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
