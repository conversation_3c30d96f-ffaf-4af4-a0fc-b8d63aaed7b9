package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTKGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通开关复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);

			// 判断厂站名称是否以数字结尾
			if (stationName.matches(".*\\d$")) {
				stationName += "=N=";
			}

			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			dzList = RuleExeUtil.sortByXLC(dzList);
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("0")){
					if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
						List<PowerDevice> lineAllSideList = new ArrayList<PowerDevice>();
						
						for(PowerDevice dev : lineList){
							lineAllSideList = RuleExeUtil.getLineOtherSideList(dev);
							break;
						}
						
						boolean ishh = true;
						
						for(PowerDevice dev : lineAllSideList){
							List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
							
							for(PowerDevice xlkg : xlkgList){
								if(!xlkg.getDeviceStatus().equals("0")){
									ishh = false;
								}
							}
						}
						
						if(ishh){
							replaceStr += CommonFunction.getHhContent(curDev, "昭通地调", stationName);
						}else{
							replaceStr += "昭通地调@遥控合上"+stationName+deviceName+"/r/n";
						}
					}else{
						replaceStr += "昭通地调@遥控合上"+stationName+deviceName+"/r/n";
					}
				}
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				replaceStr += "昭通地调@遥控合上"+stationName+deviceName+"/r/n";
			}
			
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
						||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							rbykgList.add(dev);
						}
					}
				}
			}
			
			List<PowerDevice> stationList = new ArrayList<PowerDevice>();
			stationList.add(station);
			String volt = (int)curDev.getPowerVoltGrade()+"kV";

			for(PowerDevice dev : rbykgList){
				replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				replaceStr += CommonFunction.getLineBztResult(stationList,volt , "投入");
			}
		}
		
		return replaceStr;
	}

}
