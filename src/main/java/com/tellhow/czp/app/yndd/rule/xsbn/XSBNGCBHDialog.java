package com.tellhow.czp.app.yndd.rule.xsbn;

import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

public class XSBNGCBHDialog implements RulebaseInf {
	public static boolean isgcbh = false ;
	
	@Override
	public boolean execute(RuleBaseMode rbm) {
		isgcbh = false;
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
//			int ok = JOptionPane.showConfirmDialog(null,
//					"当前线路是否存在光差保护", "操作票提示框",
//					JOptionPane.YES_NO_OPTION);
//			if(ok == JOptionPane.YES_OPTION) {
//				isgcbh = true;
//			}
			
			String[] arr = {"否","是"};
			
			int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "当前线路是否存在光差保护", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
			if(sel==0){
				isgcbh = false;
			}else if(sel==1){
				isgcbh = true;
			}else if(sel==-1){
				isgcbh = false;
			}
		}
		return true;
	}
}

