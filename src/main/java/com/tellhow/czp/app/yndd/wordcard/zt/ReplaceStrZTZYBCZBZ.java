package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.List;
import java.util.Map;
import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTZYBCZBZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通站用变操作备注".equals(tempStr)){
			String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_LINEZYB WHERE LINE_ID = '"+curDev.getPowerDeviceID()+"'";
			List<Map<String,String>> zybList =  DBManager.queryForList(sql);

			for(Map<String,String> map : zybList){
				 String zybName = (int)curDev.getPowerVoltGrade() + "kV" + StringUtils.ObjToString(map.get("ZYB_NAME"));
				 
				 replaceStr = "操作前核实站用负荷已倒供，"+zybName+"具备停电条件";
			}
		}
		
		return replaceStr;
	}

}
