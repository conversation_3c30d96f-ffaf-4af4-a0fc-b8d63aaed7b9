package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHNQJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河内桥接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev);

			PowerDevice dycmx = new PowerDevice();
			PowerDevice zycmx = new PowerDevice();
			PowerDevice gycmx = new PowerDevice();
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();

			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> otherxlkgList = new ArrayList<PowerDevice>();
			
			if(zbdyckgList.size()>0){
				List<PowerDevice> dycmxList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				if(dycmxList.size()>0){
					dycmx = dycmxList.get(0);
				}
			}
			
			if(!dycmx.getPowerDeviceID().equals("")){
				dycmlkgList = RuleExeUtil.getDeviceList(dycmx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false,true, true, true);
			}
			
			if(zbzyckgList.size()>0){
				List<PowerDevice> zycmxList = RuleExeUtil.getDeviceList(zbzyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				if(zycmxList.size()>0){
					zycmx = zycmxList.get(0);
				}
				
				if(!zycmx.getPowerDeviceID().equals("")){
					zycmlkgList = RuleExeUtil.getDeviceList(zycmx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false,true, true, true);
				}
			}
			
			if(zbgyckgList.size()>0){
				List<PowerDevice> gycmxList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				if(gycmxList.size()>0){
					gycmx = gycmxList.get(0);
				}
				
				if(!gycmx.getPowerDeviceID().equals("")){
					gycmlkgList = RuleExeUtil.getDeviceList(gycmx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false,true, true, true);
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			List<PowerDevice> allzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> alldyckgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					alldyckgList.addAll(RuleExeUtil.getTransformerSwitchLow(dev));
					allzyckgList.addAll(RuleExeUtil.getTransformerSwitchMiddle(dev));
					
					zbList.add(dev);
				}
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(!zbgyckgList.contains(dev)){
						otherxlkgList.add(dev);
					}
				}
			}
			
			List<PowerDevice> hhkgList = new ArrayList<PowerDevice>();
			
			if(zbgyckgList.size()>0){
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gycmlkgList.get(0)).equals("1")){
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					tempList.addAll(dycmlkgList);
					tempList.addAll(zycmlkgList);
					tempList.addAll(gycmlkgList);
					tempList.addAll(allzyckgList);
					tempList.addAll(alldyckgList);
					
					for(Iterator<PowerDevice> itor = tempList.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
							itor.remove();
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						hhkgList.add(dev);
						replaceStr += CommonFunctionHH.getHhContent(dev, "红河地调", stationName);
					}
					
					replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(zbgyckgList)+"/r/n";
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(otherxlkgList.get(0)).equals("1")){
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					tempList.addAll(dycmlkgList);
					tempList.addAll(zycmlkgList);
					tempList.addAll(otherxlkgList);
					tempList.addAll(allzyckgList);
					tempList.addAll(alldyckgList);
					
					for(Iterator<PowerDevice> itor = tempList.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
							itor.remove();
						}
					}
					
					for(PowerDevice dev : otherxlkgList){
						hhkgList.add(dev);
						replaceStr += CommonFunctionHH.getHhContent(dev, "红河地调", stationName);
					}
					
					replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(zbgyckgList)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zxdjddzList){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已按现场规程调整/r/n";
			}
			
			RuleExeUtil.swapDeviceList(zbList);
			
			if(zbList.size() == 2){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(zbList)+"具备并列运行条件/r/n";
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
    		for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
    		
    		for(PowerDevice zbdyckg : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
					replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
				}
			}
    		
    		for(PowerDevice zbdyckg : zbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
					replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
				}
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				for(PowerDevice hhkg : hhkgList){
					replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(hhkg)+"/r/n";
				}
				
				List<PowerDevice> zbdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate , "", CBSystemConstants.RunTypeKnifeZBS, "", true, true, true, true);
				
				for(PowerDevice dev : zbdzList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "红河地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}

				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
					if(CommonFunctionHH.ifSwitchSeparateControl(dev)){
						replaceStr += "红河地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					}
					
					List<PowerDevice> dzList = CommonFunctionHH.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunctionHH.getKnifeOffContent(dzList,stationName);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
					if(CommonFunctionHH.ifSwitchSeparateControl(dev)){
						replaceStr += "红河地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					}
					
					List<PowerDevice> dzList = CommonFunctionHH.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunctionHH.getKnifeOffContent(dzList,stationName);
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}
	
	
}