package com.tellhow.czp.app.yndd.wordcard.dh;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDHWQJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("德宏外桥接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(!RuleExeUtil.isDeviceChanged(dev)){
					String devName = CZPService.getService().getDevName(dev);
					replaceStr += stationName+"@落实"+devName+"处"+RuleExeUtil.getStatusNew(dev.getDeviceType(), dev.getDeviceStatus())+"状态/r/n";
				}
			}
			
			for(PowerDevice dev : zxdjddzList){
				String devName = CZPService.getService().getDevName(dev);
				
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "德宏地调@遥控合上"+stationName+devName+"/r/n";
				}
			}
			
			for(PowerDevice dev : otherzxdjddzList){
				String devName = CZPService.getService().getDevName(dev);
				
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "德宏地调@遥控合上"+stationName+devName+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			//220kV卡场变特殊判断
			if(curDev.getPowerStationID().equals("5066549682765825")&&curDev.getPowerDeviceID().equals("6755399530315778")){
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")&&dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							break;
						}
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				for(PowerDevice dev : zxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控拉开"+stationName+devName+"/r/n";
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				for(PowerDevice dev : zbdyckgList){
					if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
						replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
						replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
						replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
					}
				}
				
				//220kV卡场变特殊判断
				if(curDev.getPowerStationID().equals("5066549682765825")&&curDev.getPowerDeviceID().equals("6755399530315778")){
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")&&dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								break;
							}
						}
					}
				}
			}
		}
		
		return replaceStr;
	}
}
