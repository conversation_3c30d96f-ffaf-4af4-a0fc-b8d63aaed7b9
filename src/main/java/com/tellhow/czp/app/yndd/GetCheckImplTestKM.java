package com.tellhow.czp.app.yndd;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.service.CheckCzpImpl;
import com.tellhow.czp.service.CheckStatusImpl;
import com.tellhow.czp.service.GetOperationSequence;
import com.tellhow.graphicframework.startup.StartupManager;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class GetCheckImplTestKM {
    public static void main(String[] params) {
	    CheckCzpImpl check = new CheckCzpImpl();
	    CheckStatusImpl checkback = new CheckStatusImpl();
		GetOperationSequence czxl = new GetOperationSequence();

	    String param = "";
	
		StartupManager.startup();
		CZPService.getService().setArg(param);
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开110kV倪家营变110kV果松倪吴Ⅰ回181断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>220kV青山变</changzhan><caozuozhiling>将110kVⅠ母上运行的所有断路器倒至110kVⅡ母上运行</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV青山变110kVⅠ-Ⅱ母母联112断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV青山变</changzhan><caozuozhiling>将110kVⅠ母由热备用转冷备用</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F13123183592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>500kV草铺变</changzhan><caozuozhiling>将35kVⅣ母由热备用转冷备用</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开110kV潘家湾变110kV永文威潘线171断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控合上110kV潘家湾变110kV#1主变1010中性点接地开关</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><TYPE>操作指令分解</TYPE><ITEM>"
				+ "<stationname>昆明地调</stationname><stationid></stationid><caozuozhiling>遥控合上110kV潘家湾变110kV#1主变1010中性点接地开关</caozuozhiling></ITEM>"
				+ "<ITEM><stationname>110kV潘家湾变</stationname><stationid></stationid><caozuozhiling>确认110kV#1主变中性点1010接地开关处合上位置</caozuozhiling></ITEM>"
				+ "<ITEM><stationname>昆明地调</stationname><stationid></stationid><caozuozhiling>遥控断开110kV潘家湾变110kV永文威潘线171断路器</caozuozhiling></ITEM>"
				+ "<ITEM><stationname>昆明地调</stationname><stationid></stationid><caozuozhiling>遥控合上110kV潘家湾变110kV永文潘线171断路器</caozuozhiling></ITEM>"
				+ "<ITEM><stationname>昆明地调</stationname><stationid></stationid><caozuozhiling>遥控拉开110kV潘家湾变110kV#1主变中性点1010接地开关</caozuozhiling></ITEM>"
				+ "<ITEM><stationname>110kV潘家湾变</stationname><stationid></stationid><caozuozhiling>确认110kV#1主变中性点1010接地开关处拉开位置</caozuozhiling></ITEM>"
				+ "</Datas>";
		
//		czxl.execute(param);
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控合上220kV上峰变110kV上海三西线135断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 276
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开500kV七甸变500kV#2主变220kV侧、220kV七铝Ⅱ回2762断路器</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开500kV七甸变220kV七铝Ⅱ回2763断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控合上500kV七甸变500kV#2主变220kV侧、220kV七铝Ⅱ回2762断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F13123183592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 255
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开500kV七甸变220kV七铝Ⅱ回2763断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 108
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV果林变110kV果呈线148断路器</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开110kV呈贡变110kV果呈线151断路器</caozuozhiling><cbid>4C4BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV果林变</changzhan><caozuozhiling>将110kV果呈线148断路器由热备用转冷备用</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV果林变</changzhan><caozuozhiling>合上110kV果呈线14867线路接地开关</caozuozhiling><cbid>95159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 101
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV果林变110kV果松倪吴Ⅰ回149断路器</caozuozhiling><cbid>95-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV果林变</changzhan><caozuozhiling>合上110kV果松倪吴Ⅰ回14960接地开关</caozuozhiling><cbid>95159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 101
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>220kV果林变</changzhan><caozuozhiling>合上110kVⅡ段母线PT19027接地开关</caozuozhiling><cbid>95159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 253
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV果林变220kV#1主变220kV侧201断路器</caozuozhiling><cbid>95159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 253
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV果林变220kV#1主变110kV侧101断路器</caozuozhiling><cbid>95159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 301
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控合上220kV果林变110kV果琅Ⅰ回139断路器</caozuozhiling><cbid>95159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 272
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV果林变220kV#1主变10kV侧001断路器</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV果林变220kV#1主变110kV侧101断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV果林变220kV#1主变220kV侧201断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F13123183592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控合上220kV果林变220kV#1主变10kV侧001断路器</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4A54B998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 173
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV松茂变220kV#2主变35kV侧302断路器</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV松茂变220kV#2主变110kV侧102断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV松茂变220kV#2主变220kV侧202断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F13123183592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控拉开220kV松茂变220kV#2主变2020中性点接地开关</caozuozhiling><cbid>4C791115159-94BF-41F8-804A-4AB998F13123183592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控合上220kV松茂变220kV#2主变220kV侧202断路器</caozuozhiling><cbid>4C795151119-94BF-41F8-804A-4AB998F13123183592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 刀闸所在线路各侧的开关都在分位才能操作，待完成
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开110kV威远街变110kV金威潘线142断路器</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>110kV威远街变</changzhan><caozuozhiling>拉开110kV金威潘线1427隔离开关</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 111
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控拉开110kV威远街变110kVⅠ-Ⅱ母分段1121隔离开关</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开110kV呈贡变110kV果呈线151断路器</caozuozhiling><cbid>4C795159111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控拉开110kV呈贡变110kV果呈线1511隔离开关</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 152
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV松茂变220kV#1主变35kV侧301断路器</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV松茂变220kV#1主变110kV侧101断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV松茂变220kV#1主变220kV侧201断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F13123183592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 111
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控拉开220kV松茂变220kV#1主变35kV侧3016隔离开关</caozuozhiling><cbid>4C795159111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 111
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控拉开220kV果林变220kV#1主变10kV侧001开关手车隔离开关</caozuozhiling><cbid>4C795159111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 119
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>220kV果林变</changzhan><caozuozhiling>遥控合上220kV果林变110kV果小Ⅱ回154断路器</caozuozhiling><cbid>4C7951591111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV果林变</changzhan><caozuozhiling>拉开110kV果小Ⅱ回1545隔离开关</caozuozhiling><cbid>4C795159111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 120
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>220kV果林变</changzhan><caozuozhiling>遥控合上220kV果林变110kV果小Ⅱ回154断路器</caozuozhiling><cbid>4C7951591464111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV果林变</changzhan><caozuozhiling>合上110kV果小Ⅰ回1555隔离开关</caozuozhiling><cbid>4C795159111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		
		/*
		 * 135
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控合上110kV郑家营变110kV#3主变1030中性点接地开关</caozuozhiling><cbid>4C79515132391464111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开110kV郑家营变110kV#3主变10kV侧003断路器</caozuozhiling><cbid>4C7951591464111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开110kV郑家营变110kV松郑Ⅲ回173断路器</caozuozhiling><cbid>4C79515119111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV松茂变110kV松郑Ⅲ回136断路器</caozuozhiling><cbid>4C795159146411sadasd1-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>110kV郑家营变</changzhan><caozuozhiling>将110kV#3主变由热备用转冷备用</caozuozhiling><cbid>4C795151191s11-5464694BasdadF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV松茂变</changzhan><caozuozhiling>将110kV松郑Ⅲ回136断路器由热备用转冷备用</caozuozhiling><cbid>4C795151asdsd19111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>110kV郑家营变</changzhan><caozuozhiling>合上110kV松郑Ⅲ回17367接地开关</caozuozhiling><cbid>4C79515914asdsad64111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV松茂变</changzhan><caozuozhiling>合上110kV松郑Ⅲ回13667接地开关</caozuozhiling><cbid>4C79dasd51das5119111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV松茂变</changzhan><caozuozhiling>将110kV松郑Ⅲ回136断路器由冷备用转热备用</caozuozhiling><cbid>4C7951465651asdsd19111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		/*
		 * 251
		 */
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>220kV普吉变</changzhan><caozuozhiling>将110kV旁路115断路器由冷备用转热备用</caozuozhiling><cbid>4C795159111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控合上220kV普吉变110kV旁路115断路器</caozuozhiling><cbid>4C795159111-5111464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV普吉变110kV旁路115断路器</caozuozhiling><cbid>4C795159111-5111464694BF-41F8-804A-4AB475998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>110kV圆山变</changzhan><caozuozhiling>断开110kV嵩寻圆线181断路器</caozuozhiling><cbid>4C79515954654111-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		check.execute(param);
		
		/*
		 * 通过ZBID校核
		 */
		/*String zbid = "12963515-2aa3-44a8-b3d1-d2571acf9ac4";
		
		if(!zbid.equals("")){
			String sql = "SELECT CZRW FROM "+CBSystemConstants.opcardUser+"T_A_CZPZB WHERE ZBID = '"+zbid+"'";
			List<Map<String,String>> zbList = DBManager.queryForList(sql);
			
			for(Map<String,String> zbMap : zbList){
				String czrw = zbMap.get("CZRW");
				
				param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>"+czrw+"</CZRW>";
				
				sql = "SELECT MXID,CZDW,CZNR FROM "+CBSystemConstants.opcardUser+"T_A_CZPMX WHERE F_ZBID = '"+zbid+"'";
				List<Map<String,String>> mxList = DBManager.queryForList(sql);
				
				for(Map<String,String> mxMap : mxList){
					String mxid = mxMap.get("MXID");
					String czdw = mxMap.get("CZDW");
					String cznr = mxMap.get("CZNR");
					param += "<ITEM><changzhan>"+czdw+"</changzhan><caozuozhiling>"+cznr+"</caozuozhiling><cbid>"+mxid+"</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>";
				}
				
				param += "</Datas>";
				
				String result = check.execute(param);
			}
		}*/
		
//		/*
//		 * 通过MXID校核
//		 */
//		String mxid = "e115239c-d0d0-4936-92ed-a6163170808d";
//		
//		if(!mxid.equals("")){
//			String sql = "SELECT MXID,CZDW,CZNR FROM "+CBSystemConstants.opcardUser+"T_A_CZPMX WHERE MXID = '"+mxid+"'";
//			List<Map<String,String>> mxList = DBManager.queryForList(sql);
//			
//			for(Map<String,String> mxMap : mxList){
//				String czdw = mxMap.get("CZDW");
//				String cznr = mxMap.get("CZNR");
//				
//				param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>测试</CZRW>";
//				param += "<ITEM><changzhan>"+czdw+"</changzhan><caozuozhiling>"+cznr+"</caozuozhiling><cbid>"+mxid+"</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>";
//				param += "</Datas>";
//			}
//		}
//	
//		
//		/*
//		 * 批量校核
//		 */
//		String sql = "SELECT CZRW,ZBID FROM "+CBSystemConstants.opcardUser+"T_A_CZPZB ORDER BY NPSJ";
//		List<Map<String,String>> zbList = DBManager.queryForList(sql);
//		
//		List<String> successList = new ArrayList<String>();
//		List<String> failList = new ArrayList<String>();
//
//		for(int i =0 ;i< zbList.size() ; i++){
//			Map<String,String> zbMap = zbList.get(i);
//			String id = zbMap.get("ZBID");
//			String czrw = zbMap.get("CZRW");
//
//			param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>"+czrw+"</CZRW>";
//			
//			sql = "SELECT MXID,CZDW,CZNR FROM "+CBSystemConstants.opcardUser+"T_A_CZPMX WHERE F_ZBID = '"+id+"'";
//			List<Map<String,String>> mxList = DBManager.queryForList(sql);
//			
//			for(Map<String,String> mxMap : mxList){
//				String cbid = mxMap.get("MXID");
//				String czdw = mxMap.get("CZDW");
//				String cznr = mxMap.get("CZNR");
//				param += "<ITEM><changzhan>"+czdw+"</changzhan><caozuozhiling>"+cznr+"</caozuozhiling><cbid>"+cbid+"</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>";
//			}
//			
//			param += "</Datas>";
//			
//			String result = check.execute(param);
//			
//			if(result.contains("失败")){
//				failList.add(id);
//			}else{
//				successList.add(id);
//			}
//		}
//		
//		System.out.println("当前校核操作票"+(successList.size()+failList.size())+"条，成功"+successList.size()+"条,失败"+failList.size()+"条");
//		
//		for(String fail : failList){
//			System.out.println("失败zbid:"+fail);
//		}
    }
}
