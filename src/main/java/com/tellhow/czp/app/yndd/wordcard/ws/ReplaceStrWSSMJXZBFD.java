package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionWS;
import com.tellhow.graphicframework.constants.SystemConstants;

import com.tellhow.uitl.StringUtils;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSSMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山双母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();

			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zycmxList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			for(PowerDevice dev : dycmxList){
				dycmlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
			}
			
			for(PowerDevice dev : zbzyckgList){
				zycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			for (Iterator<PowerDevice> it = zycmxList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					it.remove();
				}
			}	
			
			for(PowerDevice dev : zycmxList){
				zycmlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			boolean isSwitchControl = true;
			
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(jddzList);
				
				otherzxdjddzList.addAll(jddzList);
			}
			
			replaceStr += stationName+"@核实"+deviceName+"相关现场工作任务已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），该设备的二次装置已正常投入，"+deviceName+"具备送电条件/r/n";
			
			if(isSwitchControl && isSwitchSeparateControl){
				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunctionWS.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
				}
				
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					
					if(curDev.getDeviceStatus().equals("0")){
						replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
					}else{
						replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					}
				}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
					replaceStr += "文山地调@执行"+stationName+deviceName+"由热备用转运行程序操作/r/n";
				}
				
				if(zxdjddzList.size() > 0){					
					replaceStr += CommonFunctionWS.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
				}
				
				if(otherzxdjddzList.size() > 0){
					replaceStr += CommonFunctionWS.getZxdJddzOffCheckContent(otherzxdjddzList, stationName, station);
				}
			}else{
				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunctionWS.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
				}
				
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					for(PowerDevice dev : zbgyckgList){
						if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
							String mxName = "";
							
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
							
							for(PowerDevice mx : mxList){
								mxName = "于"+CZPService.getService().getDevName(mx);
								break;
							}
							
							replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用"+mxName+"程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
						}else{
							String mxName = "";
							
							if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
								List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
								
								for(PowerDevice mx : mxList){
									mxName = "于"+CZPService.getService().getDevName(mx);
									break;
								}
							}
							
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用"+mxName+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						List<PowerDevice> zycdzList = CommonFunctionWS.getTransformerKnife(curDev, dev);
						replaceStr += CommonFunctionWS.getKnifeOnContent(zycdzList,stationName);
						
						if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
							String mxName = "";
							
							if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
								List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
								
								for(PowerDevice mx : mxList){
									mxName = "于"+CZPService.getService().getDevName(mx);
									break;
								}
							}
							
							replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用"+mxName+"程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
						}else{
							String mxName = "";
							
							if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
								List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
								
								for(PowerDevice mx : mxList){
									mxName = "于"+CZPService.getService().getDevName(mx);
									break;
								}
							}
							
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用"+mxName+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						List<PowerDevice> dycdzList = CommonFunctionWS.getTransformerKnife(curDev, dev);
						
						for (Iterator<PowerDevice> it = dycdzList.iterator(); it.hasNext();) {
							PowerDevice dz = it.next();
							
							if(dz.getPowerDeviceName().endsWith("1")){
								it.remove();
							}
						}
						
						replaceStr += CommonFunctionWS.getKnifeOnContent(dycdzList,stationName);
						
						if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
							replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}

					if(dycmlkgList.size() == 0){
						for(PowerDevice dev : dycmxList){
							String dzName =getptdzName(dev);
							if (StringUtils.isNotEmpty(dzName)){
								replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+dzName+"已处运行/r/n";
							}else{
								replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"电压互感器已处运行/r/n";
							}
						}
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += CommonFunctionWS.getCdOrHhContent(dev, "文山地调", stationName,"对"+deviceName+"充电");
						//"文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					}
				}
				
				if(zycmlkgList.size() == 0){
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							String zycmxName = "";
							
							for(PowerDevice zycmx : zycmxList){
								zycmxName = CZPService.getService().getDevName(zycmx);
							}
							
							replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+zycmxName+"充电/r/n";
						}
					}
				}else{
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionWS.getCdOrHhContent(dev, "文山地调", stationName,"1");
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
				
				if(dycmlkgList.size() == 0){
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							String dycmxName = "";
							
							for(PowerDevice dycmx : dycmxList){
								dycmxName = CZPService.getService().getDevName(dycmx);
							}
							
							replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+dycmxName+"充电/r/n";
						}
					}
				}else{
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionWS.getCdOrHhContent(dev, "文山地调", stationName,"1");
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
				
				if(zxdjddzList.size() > 0){					
					replaceStr += CommonFunctionWS.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
				}
				
				if(otherzxdjddzList.size() > 0){
					replaceStr += CommonFunctionWS.getZxdJddzOffCheckContent(otherzxdjddzList, stationName, station);
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
		}
		
		return replaceStr;
	}

	private String getptdzName(PowerDevice curDev){
		String dzName = "";
		List<PowerDevice> ptdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, "", true, true, true, true);
		if (ptdzList.isEmpty())
			return dzName;
		for(PowerDevice dev : ptdzList){
			String devNum = CZPService.getService().getDevNum(dev);
			String devName = CZPService.getService().getDevName(dev);
			if(devName.contains("PT")){
				dzName+=devName.replace("PT"+devNum, "电压互感器"+"PT"+devNum)+"、";
			}else{
				dzName+=devName.replace(devNum, "电压互感器"+devNum)+"、";
			}
		}
		if(dzName.length()>0){
			dzName = dzName.substring(0, dzName.length()-1);
		}
		return dzName;
	}
}
