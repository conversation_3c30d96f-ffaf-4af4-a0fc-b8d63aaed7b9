package com.tellhow.czp.app.yndd.wordcard.km;

import com.tellhow.czp.app.yndd.rule.km.JudgeSwitchBDGDLQZT;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooBDGDLQLBY implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("被代供断路器冷备用".equals(tempStr)){
			return "冷备用".equals(JudgeSwitchBDGDLQZT.flag);
		}
        return false;
	}
}
