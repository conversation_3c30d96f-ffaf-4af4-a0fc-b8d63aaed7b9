package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.HashMap;
import java.util.Iterator;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooXBZ implements TempBooleanReplace {
	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {

		if (tempStr.equals("线变组")) {
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			if(curDev.getPowerVoltGrade() == 500 || station.getPowerVoltGrade() == 500){
				return false;
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(RuleExeUtil.isTransformerXBZ(dev)){
						return true;
					}
				}
			}
		}
		return false;
	}
		
}
