package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNSMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳双母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> plkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL +","+ CBSystemConstants.RunTypeSwitchMLPL, "", false, true, true, true);
			List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
			
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade() && !dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
						mxList.add(dev);
					}
				}
			}
			
			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : xlkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice rbykg : rbykgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(rbykg, SystemConstants.SwitchSeparate);

						if(CommonFunctionBN.ifSwitchControlBN(rbykg)&&CommonFunctionBN.ifSwitchSeparateControlBN(rbykg)){
							replaceStr += "版纳地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由热备用转冷备用程序操作/r/n";
							replaceStr += getKnifeOffCheckContent(dzList, stationName);
							
							replaceStr += "版纳地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由冷备用转热备用程序操作/r/n";
							replaceStr += getKnifeOnCheckContent(dzList, stationName);
						}
					}
				}
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice rbykg : rbykgList){
						if(CommonFunctionBN.ifSwitchControlBN(rbykg)&&CommonFunctionBN.ifSwitchSeparateControlBN(rbykg)){
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(rbykg)+"由"+deviceName+"热备用倒至"+CZPService.getService().getDevName(dev)+"热备用/r/n";
						}
					}
				}
			}
			
			replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice yxkg : yxkgList){
						if(CommonFunctionBN.ifSwitchControlBN(yxkg)&&CommonFunctionBN.ifSwitchSeparateControlBN(yxkg)){
							replaceStr += "版纳地调@执行"+stationName+CZPService.getService().getDevName(yxkg)+"由"+deviceName+"运行倒至"+CZPService.getService().getDevName(dev)+"运行程序操作/r/n";
						
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(yxkg, SystemConstants.SwitchSeparate);
							
							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
									replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处合上位置/r/n";
								}
							}
							
							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
									replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
								}
							}
						}
					}
				}
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice yxkg : yxkgList){
						if(CommonFunctionBN.ifSwitchControlBN(yxkg)&&CommonFunctionBN.ifSwitchSeparateControlBN(yxkg)){
							
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(yxkg)+"由"+deviceName+"运行倒至"+CZPService.getService().getDevName(dev)+"运行/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
			}
			
			replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			
			for(PowerDevice dev : othermxList){
				replaceStr += stationName+"@确认"+deviceName+"上运行的所有断路器已倒至"+CZPService.getService().getDevName(dev)+"运行，"+deviceName+"已处空载运行状态，具备停电条件/r/n";
			}
			
			replaceStr += stationName+"@将"+deviceName+"电压互感器由运行转冷备用/r/n";
			
			if(curDev.getDeviceStatus().equals("1")){
				replaceStr += "版纳地调@执行"+stationName+deviceName+"由空载运行转热备用程序操作/r/n";
			}else if(curDev.getDeviceStatus().equals("2")){
				replaceStr += "版纳地调@执行"+stationName+deviceName+"由空载运行转冷备用程序操作/r/n";
				replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(mlkgList, stationName);
			}
		}
		
		return replaceStr;
	}
	
	public String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处合上位置/r/n";
			}
		}
		
		return replaceStr;
	}
}
