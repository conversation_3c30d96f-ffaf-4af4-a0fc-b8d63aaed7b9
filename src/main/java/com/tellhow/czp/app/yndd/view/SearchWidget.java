package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTabbedPane;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;


/**
 * 搜索框的创建
 * <AUTHOR>
 */
public class SearchWidget extends Widget {
	private JPanel panel=new JPanel();

	
	private JMenuItem searchMenuItem;
	private String lastSearchText = null;
	private JLabel label;
	private JPanel searchPanel;
	private int nameMaxLength = 30;
	private String filePath = "";
	private String fileName = "";
	private String stationID = "";
	private String stationName = "";
	final JComboBox jComboBox = new JAutoCompleteComboBox();
	
	public SearchWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		
       
        searchMenuItem = new JMenuItem("查找");
        searchMenuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				String searchText = JOptionPane.showInputDialog(SystemConstants.getMainFrame(), "输入要查找的厂站名称", lastSearchText);
				if (searchText == null)
					return;
				else if (searchText.equals("")) {
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "查询条件不能为空！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
					return;
				}
				lastSearchText = searchText;
			}
		});
        panel.setLayout(new BorderLayout());
        addSearchItem();
//        setleafIcon();
	}
	
	
	private void setleafIcon() {
		Thread thread =new Thread(){
       	 public void run(){
       		 
       		 if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive()){
	        		    try {
	        		    	SystemConstants.threadLoadFile.join();
						} catch (InterruptedException e) {
							e.printStackTrace();
						}
       		 }
       	 }
       };
       thread.start();
	   
	}
	//搜索栏的添加
	private void addSearchItem() {
		
		label = new JLabel("搜索厂站：");
		searchPanel = new JPanel();
		searchPanel.setPreferredSize(new Dimension(450, 35));
//		searchPanel.setBackground(Color.lightGray );
		searchPanel.setLayout(new BorderLayout());
		searchPanel.add(label,BorderLayout.WEST);
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		File file = new File(SystemConstants.FILE_SVGMAP_PATH+"");
	
		
		// 加入厂站图
		String sql2 = CZPImpl.getPropertyValue("StationSearchSql");
		if(sql2 == null)
			sql2 = OPEService.getService().TransTreeWidgetSql1();
		sql2=sql2.toLowerCase().replaceAll("equip\\.", CBSystemConstants.equipUser);
		List result2 = DBManager.queryForList(sql2);
		for(int i = 0; i < result2.size(); i++) {
    	
			if(((Map)result2.get(i)).get("station_id") == null || ((Map)result2.get(i)).get("station_name") ==null)
				continue;
			cnm=new CodeNameModel();
			
			if(((Map)result2.get(i)).get("station_name").toString().contains("中调")&&!((Map)result2.get(i)).get("station_name").toString().contains("厂")){
				continue;
			}
			
			cnm.setCode(((Map)result2.get(i)).get("station_id").toString());
			cnm.setName(((Map)result2.get(i)).get("station_name").toString());
			model.addElement(cnm);
		
		}
		
		
		jComboBox.setModel(model);
		jComboBox.setSelectedIndex(-1);
		searchPanel.add(jComboBox,BorderLayout.CENTER);
		panel.add(searchPanel,BorderLayout.NORTH);

	
		jComboBox.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {	
				if(jComboBox.getSelectedItem()==null || jComboBox.getSelectedItem() instanceof java.lang.String){
					return;
				}
				String searchText = ((CodeNameModel) jComboBox.getSelectedItem()).getCode();
				String searchName = ((CodeNameModel) jComboBox.getSelectedItem()).getName();
				
				if(searchText.contains("\\")){
					CreatePowerStationToplogy.createSVGPanel("", searchName, searchName , searchText);
				}else {
					List<SVGFile> fileList = SystemConstants.getSVGFileByLineID(searchText);  // 获取线路的
					List<SVGFile> fileList2 = SystemConstants.getSVGFileByStationID(searchText);  // 获取厂站
					
					if(fileList.size() > 0 && fileList2.size() == 0){
						selectline(fileList, searchName, searchText);
					}
					
					if(fileList.size() == 0 && fileList2.size() >  0){
						selectStation(fileList2, searchName, searchText);
					}
					
					if(fileList.size() == 0 && fileList2.size() == 0){
						JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在" + searchName + "接线图！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
	            		return;
					}
				}
			}
			
		});
		
	}

	

	public SearchWidget(Widget parent, String name, String preset)
			throws GUIException {
		super(parent, name, preset);
	}

	@Override
	public Component getWidget() {
		return panel;
	}
	
	/**
	 * 获取选择的线路图图形
	 * @param fileList   线路图集合
	 * @param searchName    选择的名字
	 * @param searchText   线路或厂站的名称
	 */
	public void selectline(List<SVGFile> fileList , String searchName , String searchText ){
		
		
		if(fileList != null){
			 if(fileList.size() == 0) {
	            	while(SystemConstants.threadLoadFile.isAlive() && !SystemConstants.OPEN_MAP.equals("")) {
	            		try {
							Thread.sleep(100);
						} catch (InterruptedException e1) {
							// TODO Auto-generated catch block
							e1.printStackTrace();
						}
	            		fileList = SystemConstants.getSVGFileByLineID(searchText);
	            	}
	            }
	            if(fileList.size() == 0) {
	            	if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive())
	            		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
	            	else{
	            		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在[" + searchName + "]单线图！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
	            		return;
	            	}
	            }
	            else if(fileList.size() == 1) {
	            	filePath = fileList.get(0).getFilePath();
	                fileName = fileList.get(0).getFileName();
	                stationID = fileList.get(0).getLineID();
	            }
	            else {
	            	Object[] options = fileList.toArray(); 
	            	int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
	            	if(i == -1)
	            		return;
	            	filePath = fileList.get(i).getFilePath();
	                fileName = fileList.get(i).getFileName();
	                stationID = fileList.get(i).getLineID();
	            }
	           
//						                stationName = fileName.substring(0, fileName.lastIndexOf(".")).replace(".fac", "").replace(".pic", "");
//						                if(stationName.indexOf(".") > 0)
//							                	stationName = stationName.substring(stationName.indexOf(".")+1);
	            stationName = fileName.substring(fileName.indexOf("_")+1, fileName.length()).replaceAll(".sln.pic.svg", "");	
		}
		

	    //8缓存
	    SvgP svgp=new SvgP(stationID, stationName, fileName, filePath);
	    CBSystemConstants.svgps.put(stationID, svgp);
	    JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
		int n=tabbedPane.getComponentCount();
		for(int i=0;i<n;i++){
			SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
			String station=sel.getStationID();
			boolean isdestry=sel.isIsdestry();
			if(station.equals(stationID)&&isdestry==true){
				File svgMapFile = new File(filePath);
				sel.loadSvgFile(svgMapFile);
				sel.setIsdestry(false);
			}
		}
		//
		CreatePowerStationToplogy.createSVGPanel(stationID, stationName, fileName, filePath);
	}
	
	
	/**
	 * 打开厂站图
	 * @param fileList
	 * @param searchName
	 * @param searchText
	 */
	public void selectStation(List<SVGFile> fileList , String searchName , String searchText){
		
			if(fileList.size()!=1){
	   		 CZPService.getService().filterMap(fileList, searchText);
			}
	       if(fileList.size() == 0) {
	       	if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive()){
	       		jComboBox.setEnabled(false);
	       		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
	       		jComboBox.setEnabled(true);
	       		jComboBox.setSelectedIndex(-1);
	       	}else{
	       		jComboBox.setEnabled(false);
	       		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在" + searchName + "一次接线图！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
	       		jComboBox.setEnabled(true);
	       		jComboBox.setSelectedIndex(-1);
	       	}
	       	return;
	       }
	       else if(fileList.size() == 1) {
	       	filePath = fileList.get(0).getFilePath();
	           fileName = fileList.get(0).getFileName();
	           jComboBox.setSelectedIndex(-1);
	       }
	       else {
	       	Object[] options = fileList.toArray(); 
	       	jComboBox.setEnabled(false);
	       	int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);         	
	       	jComboBox.setEnabled(true);     
	       	jComboBox.setSelectedIndex(-1);
	       	if(i == -1)
	       		return;
	       	filePath = fileList.get(i).getFilePath();
	           fileName = fileList.get(i).getFileName();
	       }
	       stationID = searchText;
	//       stationName = fileName.substring(0, fileName.lastIndexOf(".")).replace(".fac", "").replace(".pic", "");
	//       if(stationName.indexOf(".") > 0)
	//       	stationName = stationName.substring(stationName.indexOf(".")+1);
	       stationName = searchName;
	
	   //8缓存
	   SvgP svgp=new SvgP(stationID, stationName, fileName, filePath);
	   CBSystemConstants.svgps.put(stationID, svgp);
	   JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
		int n=tabbedPane.getComponentCount();
		for(int i=0;i<n;i++){
			SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
			String station=sel.getStationID();
			boolean isdestry=true;//sel.isIsdestry();
			if(station.equals(stationID)&&isdestry==true){
				File svgMapFile = new File(filePath);
				sel.loadSvgFile(svgMapFile);
				sel.setIsdestry(false);
			}
		}
		//
		CreatePowerStationToplogy.createSVGPanel(stationID, stationName, fileName, filePath);
	}
	
}




