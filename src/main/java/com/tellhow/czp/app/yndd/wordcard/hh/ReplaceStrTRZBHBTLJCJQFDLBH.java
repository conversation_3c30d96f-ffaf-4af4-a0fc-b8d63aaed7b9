package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.List;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTRZBHBTLJCJQFDLBH implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		
		if("投入主变差动、后备跳两侧及桥、非电量保护闭锁".equals(tempStr)){
			
			List<PowerDevice> powerTransformers = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "","","", false, true, true, true);
			if(powerTransformers.size() > 0){
				for(PowerDevice powerDevice : powerTransformers){
					int voltage = (int) powerDevice.getPowerVoltGrade();
				  String powerTransformer = powerDevice.getPowerDeviceName().replace(powerDevice.getPowerStationName(), "");
				  replaceStr =voltage+"kV"+ powerTransformer+"差动、后备（跳两侧及桥）、非电量保护动作闭锁"+voltage+"kV";
				}
			}
		}
		return replaceStr;
	}

}
