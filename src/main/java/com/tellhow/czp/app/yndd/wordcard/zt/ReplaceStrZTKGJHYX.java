package com.tellhow.czp.app.yndd.wordcard.zt;

import com.tellhow.czp.app.service.CZPService;


import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTKGJHYX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通开关解环运行".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			replaceStr += stationName+"@将110kV电源快速切换装置保护定值区由01区切换至02区/r/n";
			replaceStr += "昭通地调@遥控断开"+stationName+deviceName+"/r/n";
		}
		
		return replaceStr;
	}

}
