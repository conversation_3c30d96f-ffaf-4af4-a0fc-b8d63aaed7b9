package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class CommonFunctionPE {
	public static String getLineTVDzOffContent(PowerDevice dev,String stationName){
		String replaceStr = "";
		
		if(stationName.equals("35kV思茅变") && dev.getPowerDeviceName().equals("35kV南思线")){
			replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"线路TV二次空开处于分闸位置/r/n";
			replaceStr += "普洱地调@遥控拉开"+stationName+"35kV南思线线路TV3529隔离开关/r/n";
			replaceStr += stationName+"@核实35kV南思线线路TV3529隔离开关处于拉开位置/r/n";
		}else{
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

			for(PowerDevice dz : dzList){
				if(dz.getPowerDeviceName().contains("TV")){
					String dzName = CZPService.getService().getDevName(dz);
					
					if(ifSwitchControl(dev)){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"线路TV二次空开处于分闸位置/r/n";
						replaceStr += "普洱地调@遥控拉开"+stationName+dzName+"/r/n";
						replaceStr += stationName+"@核实"+dzName+"处于拉开位置/r/n";
					}else{
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"线路TV二次空开处于分闸位置/r/n";
						replaceStr += stationName+"@拉开"+dzName+"/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getLineTVDzOnContent(PowerDevice dev,String stationName){
		String replaceStr = "";
		
		if(stationName.equals("35kV思茅变") && dev.getPowerDeviceName().equals("35kV南思线")){
			replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"线路TV二次空开处于分闸位置/r/n";
			replaceStr += "普洱地调@遥控合上"+stationName+"35kV南思线线路TV3529隔离开关/r/n";
			replaceStr += stationName+"@核实35kV南思线线路TV3529隔离开关处于合上位置/r/n";
		}else{
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dz : dzList){
				if(dz.getPowerDeviceName().contains("TV")){
					String dzName = CZPService.getService().getDevName(dz);
					
					if(ifSwitchControl(dev)){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"线路TV二次空开处于分闸位置/r/n";
						replaceStr += "普洱地调@遥控合上"+stationName+dzName+"/r/n";
						replaceStr += stationName+"@核实"+dzName+"处于合上位置/r/n";
					}else{
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"线路TV二次空开处于分闸位置/r/n";
						replaceStr += stationName+"@合上"+dzName+"/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getSwitchOffContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(stationName.endsWith("电站") || stationName.endsWith("电厂")){
				replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getSwitchOnContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(stationName.endsWith("电站") || stationName.endsWith("电厂")){
				replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(dev.getPowerVoltGrade() > 35){
				if(stationName.contains("电站") || stationName.endsWith("电厂")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}
			}else{
				if(stationName.contains("电站") || stationName.endsWith("电厂")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static boolean ifKnifeCheck(PowerDevice dev){
		/*String sql = "SELECT ISABLE FROM "+CBSystemConstants.equipUser+"T_A_KNIFECHECK WHERE KNIFEID = '"+dev.getPowerDeviceID()+"'";
		List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
		
		for(Map<String,String> ifcontrolMap : ifcontrolList){
			String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISABLE"));
			
			if(ifcontrol.equals("是")){
				return true;
			}else{
				return false;
			}
		}*/
		
		PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
		
		if(station.getPowerVoltGrade() <= 220 && 
				!station.getPowerDeviceName().equals("宁洱110kV普洱变")){
			return true;
		}else{
			return false;
		}
	}
	
	public static List<Map<String, String>> getStationLineList(PowerDevice curDev){
		List<Map<String, String>> stationLineList = new ArrayList<Map<String,String>>();
		
		String sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,PTDISCONNECTOR_NAME "
				+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND "
						+ "WHERE ID = '"+curDev.getPowerDeviceID()+"')";
		
		stationLineList = DBManager.queryForList(sql);
		
		if(stationLineList.size() == 0){
			String lineName = CZPService.getService().getDevName(curDev);
			
			sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,PTDISCONNECTOR_NAME "
					+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_NAME = '"+lineName+"'";
			
			stationLineList = DBManager.queryForList(sql);
		}
		
		return stationLineList; 
	}
	
	public static String getKnifeOnCheckContent(PowerDevice dev){
		//dev是开关
		// 更改为一遥一核实
		String replaceStr = "";
		
		PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station); 
		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
		
		boolean ismldz = false;
		
		for(PowerDevice dz : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dz, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				if(ifKnifeCheck(dz)){
					String dzName = getSequentialDeviceName(dz);
					replaceStr += stationName+"@核实"+dzName+"处于合上位置/r/n";
				}else{
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String deviceName = CZPService.getService().getDevName(dev); 
						replaceStr += stationName+"@核实"+deviceName+"处于热备用/r/n";
						break;
					}else{
						String dzName = getSequentialDeviceName(dz);
						replaceStr += stationName+"@核实"+dzName+"处于合上位置/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffCheckContent(PowerDevice dev){
		//dev是开关
		// 更改为一遥一核实
		String replaceStr = "";
		
		PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station); 
		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
		
		boolean ismldz = false;
		
		for(PowerDevice dz : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dz, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				if(ifKnifeCheck(dz)){
					String dzName = getSequentialDeviceName(dz);
					replaceStr += stationName+"@核实"+dzName+"处于拉开位置/r/n";
				}else{
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String deviceName = CZPService.getService().getDevName(dev); 
						replaceStr += stationName+"@核实"+deviceName+"处于冷备用/r/n";
						break;
					}else{
						String dzName = getSequentialDeviceName(dz);
						replaceStr += stationName+"@核实"+dzName+"处于拉开位置/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeHalfControlOffContent(PowerDevice dev){//dev是开关
		String replaceStr = "";
		
		PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station); 
		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
		
		boolean ismldz = false;
		
		for(PowerDevice dz : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dz, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
		
		for(PowerDevice dz : dzList){
			if(CommonFunctionPE.ifSwitchSeparateControl(dz)){
				replaceStr += "普洱地调@遥控拉开"+stationName+CZPService.getService().getDevName(dz)+"/r/n";
				
				if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
					if(ifKnifeCheck(dz)){
						String dzName = getSequentialDeviceName(dz);
						replaceStr += "普洱地调@核实"+stationName+dzName+"处于拉开位置/r/n";
					}else{
						if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							String deviceName = CZPService.getService().getDevName(dev); 
							replaceStr += stationName+"@核实"+deviceName+"处于冷备用/r/n";
							break;
						}else{
							String dzName = getSequentialDeviceName(dz);
							replaceStr += stationName+"@核实"+dzName+"处于拉开位置/r/n";
						}
					}
				}
			}else{
				replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dz)+"/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeHalfControlOnContent(PowerDevice dev){//dev是开关
		String replaceStr = "";
		
		PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station); 
		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
		
		boolean ismldz = false;
		
		for(PowerDevice dz : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dz, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				if(CommonFunctionPE.ifSwitchSeparateControl(dz)){
					replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dz)+"/r/n";

					if(ifKnifeCheck(dz)){
						String dzName = getSequentialDeviceName(dz);
						replaceStr += "普洱地调@核实"+stationName+dzName+"处于合上位置/r/n";
					}else{
						if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							String deviceName = CZPService.getService().getDevName(dev); 
							replaceStr += stationName+"@核实"+deviceName+"处于热备用/r/n";
							break;
						}else{
							String dzName = getSequentialDeviceName(dz);
							replaceStr += stationName+"@核实"+dzName+"处于合上位置/r/n";
						}
					}
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dz)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				if(ifKnifeCheck(dz)){
					String dzName = getSequentialDeviceName(dz);
					replaceStr += "普洱地调@核实"+stationName+dzName+"处于合上位置/r/n";
				}else{
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

						String deviceName = ""; 
						
						for(PowerDevice dev : swList){
							deviceName = CZPService.getService().getDevName(dev); 
						}
						
						replaceStr += stationName+"@核实"+deviceName+"处于热备用/r/n";
						break;
					}else{
						String dzName = getSequentialDeviceName(dz);
						replaceStr += stationName+"@核实"+dzName+"处于合上位置/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				if(ifKnifeCheck(dz)){
					String dzName = getSequentialDeviceName(dz);
					replaceStr += "普洱地调@核实"+stationName+dzName+"处于拉开位置/r/n";
				}else{
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

						String deviceName = ""; 
						
						for(PowerDevice dev : swList){
							deviceName = CZPService.getService().getDevName(dev); 
						}
						
						replaceStr += stationName+"@核实"+deviceName+"处于冷备用/r/n";
						break;
					}else{
						String dzName = getSequentialDeviceName(dz);
						replaceStr += stationName+"@核实"+dzName+"处于拉开位置/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnCheckContent(PowerDevice dz,String stationName){
		String replaceStr = "";
		
		if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
			if(ifKnifeCheck(dz)){
				String dzName = getSequentialDeviceName(dz);
				replaceStr += "普洱地调@核实"+stationName+dzName+"处于合上位置/r/n";
			}else{
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

					String deviceName = ""; 
					
					for(PowerDevice dev : swList){
						deviceName = CZPService.getService().getDevName(dev); 
					}
					
					replaceStr += stationName+"@核实"+deviceName+"处于热备用/r/n";
				}else{
					String dzName = getSequentialDeviceName(dz);
					replaceStr += stationName+"@核实"+dzName+"处于合上位置/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffCheckContent(PowerDevice dz,String stationName){
		String replaceStr = "";
		
		if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
			if(ifKnifeCheck(dz)){
				String dzName = getSequentialDeviceName(dz);
				replaceStr += "普洱地调@核实"+stationName+dzName+"处于拉开位置/r/n";
			}else{
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

					String deviceName = ""; 
					
					for(PowerDevice dev : swList){
						deviceName = CZPService.getService().getDevName(dev); 
					}
					
					replaceStr += stationName+"@核实"+deviceName+"处于冷备用/r/n";
				}else{
					String dzName = getSequentialDeviceName(dz);
					replaceStr += stationName+"@核实"+dzName+"处于拉开位置/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getSequentialDeviceName(PowerDevice dz){
		return dz.getPowerDeviceName();
	}
	
	public static boolean ifSwitchControl(PowerDevice dev){
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
					+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dev.getPowerDeviceID()+"'";
			
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
				return Boolean.parseBoolean(ifcontrol);
			}
		}
		
		return true;
	}
	
	public static boolean ifSwitchSeparateControl(PowerDevice dev){//刀闸是否可控
		if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
					+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dev.getPowerDeviceID()+"'";
			
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
				return Boolean.parseBoolean(ifcontrol);
			}
		}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dz : dzList){
				String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
						+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dz.getPowerDeviceID()+"'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}
		}
		
		return true;
	}
	
	public static boolean ifSwitchSeparateHalfControl(PowerDevice dev){//刀闸是否一个可控一个不可控
		boolean result = false;
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			boolean ifControl = false;
			boolean ifNotControl = false;
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dz : dzList){
				String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
						+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dz.getPowerDeviceID()+"'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						ifNotControl = true;
					}else{
						ifControl = true;
					}
				}
			}
			
			if(ifNotControl && ifControl){
				result = true;
			}else{
				result = false;
			}
		}
		
		return result;
	}
	
	public static boolean ifLineContainTV(){
		boolean result = false;
		
		
		
		return result;
	}
	
	public static String get3KnifeContent(List<PowerDevice> kgList,String stationName,String operation){
		String replaceStr = "";
		
		for(PowerDevice dev : kgList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			List<PowerDevice> alldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.MotherLine, true, true, false);
			
			for(PowerDevice dz : alldzList){
				if(!dzList.contains(dz)&&!dz.getPowerDeviceName().endsWith("1")){
					if(operation.equals("拉开")){
						replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dz)+"/r/n";
					}else{
						replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dz)+"/r/n";
					}
					break;
				}
			}
		}
		
		return replaceStr; 
	}
	
	public static String getKnifeOffContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "普洱地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOffCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeListOffContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
				if(dev.getPowerDeviceName().endsWith("1")){
					it.remove();
				}
			}
		}
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				replaceStr += "普洱地调@遥控拉开"+stationName+CZPService.getService().getDevName(dz)+"/r/n";
				replaceStr += "普洱地调@核实"+stationName+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeListOnContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
				if(dev.getPowerDeviceName().endsWith("1")){
					it.remove();
				}
			}
		}
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dz)+"/r/n";
				replaceStr += "普洱地调@核实"+stationName+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
			}
		}
		
		return replaceStr;
	}
}
