package com.tellhow.czp.app.yndd.wordcard.km;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKMLKJDDZ implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";

		if("昆明拉开接地刀闸".equals(tempStr)) {
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);

			if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)){
				String deviceName = CZPService.getService().getDevName(curDev);
				deviceName=deviceName.replace("主变中性点", "主变"+(int)curDev.getPowerVoltGrade()+"kV侧中性点");
				replaceStr += "昆明地调@遥控拉开"+stationName+deviceName;
			}else{
				replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(curDev);
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
