package com.tellhow.czp.app.yndd.wordcard.hh;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

import java.util.*;

public class ReplaceStrHHDQMXMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("红河当前母线名称".equals(tempStr)) {

			Map<Integer, DispatchTransDevice> dtds = CBSystemConstants.getDtdMap();
			DispatchTransDevice dtd = null;
			PowerDevice dev = null;
			CommonSearch cs = new CommonSearch();
			Map<String, Object> inPara = new HashMap<String, Object>();
			Map<String, Object> outPara = new HashMap<String, Object>();
			List<PowerDevice> motherList = new ArrayList<PowerDevice>();
			List searchDevs = null;
            for (DispatchTransDevice dispatchTransDevice : dtds.values()) {
                dtd = dispatchTransDevice;
                dev = dtd.getTransDevice();
                if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
					// 只取当前设备的刀闸
					List<PowerDevice> swList= RuleExeUtil.getKnifeRelateSwitch(dev);
					if (curDev.getDeviceType().equals(SystemConstants.Switch) && !swList.contains(curDev)) continue;
                    inPara.put("oprSrcDevice", dev);
                    inPara.put("tagDevType", SystemConstants.MotherLine); // 目标设备母线
                    inPara.put("isSearchDirectDevice", "true");
                    cs.execute(inPara, outPara);
                    searchDevs = (ArrayList) outPara.get("linkedDeviceList");
                    if (searchDevs.isEmpty())
                        continue;
                    dev = (PowerDevice) searchDevs.get(0);
                    if (dtd.getBeginstatus().equals("0")
                            && dev.getPowerStationID().equals(stationDev.getPowerStationID())
                            && !motherList.contains(dev))
                        motherList.add(dev);
                }
            }
			if (motherList.isEmpty()) {
				return null;
			}
            for (PowerDevice powerDevice : motherList) {
                dev = powerDevice;
                if ("".equals(replaceStr))
                    replaceStr = CZPService.getService().getDevName(dev).replace(
                            dev.getPowerStationName(), "");
                else
                    replaceStr = replaceStr
                            + "、"
                            + CZPService.getService().getDevName(dev).replace(
                            dev.getPowerStationName(), "");
            }
		}
		return replaceStr;
	}

}
