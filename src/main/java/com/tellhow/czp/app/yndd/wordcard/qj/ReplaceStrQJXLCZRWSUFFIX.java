package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.qj.JDKGXZQJ;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJXLCZRWSUFFIX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖线路操作任务后缀".equals(tempStr)){
			if(curDev.getPowerVoltGrade() >= 220){
				String begin = CBSystemConstants.getCurRBM().getBeginStatus();
				String end = CBSystemConstants.getCurRBM().getEndState();
				
				if(Integer.valueOf(begin) < Integer.valueOf(end)){//停电
					List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());

					for(PowerDevice line : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(line.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						stationName = StringUtils.killVoltInDevName(stationName);
						replaceStr += "（从"+stationName+"侧解环）";
						break;
					}
				}else{
					PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					stationName = StringUtils.killVoltInDevName(stationName);
					replaceStr += "（从"+stationName+"侧充电）";
				}
			}
		}
		
		return replaceStr;
	}

}
