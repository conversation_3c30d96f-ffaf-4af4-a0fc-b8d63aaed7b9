package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHJLTVFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河计量TV复电术语".equals(tempStr)){
			CommonFunctionHH cf = new CommonFunctionHH();
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();
			List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",true, true, true, true);
			
			PowerDevice tvdz = new PowerDevice();
			
			for(PowerDevice dev : qtdzList){
				if(dev.getPowerDeviceName().contains("计量TV")){
					tvdz = dev;
				}
			}
			
			replaceStr += "核实"+CZPService.getService().getDevName(tvdz)+"冷备用/r/n";
			
			String mxName = CZPService.getService().getDevName(curDev);
			
			if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				mxName = mxName.replace("母", "组母线");
			}else if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				mxName = mxName.replace("母", "段母线");
			}
			
			replaceStr += "核实"+mxName+"电压互感器"+CZPService.getService().getDevNum(tvdz)+"隔离开关在拉开位置/r/n";
			
			replaceStr += "将"+CZPService.getService().getDevName(tvdz)+"由冷备用转运行/r/n";
			
			mxList.addAll(othermxList);
			mxList.add(curDev);

			for (Iterator<PowerDevice> it = mxList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					it.remove();
				}
			}
			
			RuleExeUtil.swapDeviceList(mxList);
			
			String allmxName = "";
			
			if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				allmxName = CZPService.getService().getDevName(mxList).replace("母", "组母线");
			}else if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				allmxName = CZPService.getService().getDevName(mxList).replace("母", "段母线");
			}
			
			if(mxList.size()==2){
				replaceStr += "将"+allmxName+"电压互感器所供二次负荷分开供电/r/n";
			}
			
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			
			ReplaceStrHHZNHHDD hhcz = new ReplaceStrHHZNHHDD();
			
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							tempList.add(dev);
						}
					}
				}
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
			
			if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//设备在电源侧
				for(PowerDevice dev : tempList){
					replaceStr += StringUtils.ObjToString(hhcz.strReplace("红河站内合环调电", dev, dev, desc));
				}
			}else{//设备在负荷侧
				tempList.clear();
				
				List<PowerDevice> allmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> fhcmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
				List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> zbdyckgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC,"",false, true, true, true);
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML,"",false, true, true, true);
				
				tempList.addAll(zbdyckgList);
				
				for(PowerDevice dev : othermxList){
					List<PowerDevice> otherzbdyckgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC,"",false, true, true, true);
					tempList.addAll(otherzbdyckgList);
				}
				
				tempList.addAll(mlkgList);
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						allmlkgList.add(dev);
						
						if (dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
							gycmlkgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						zbkgList.add(dev);
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
						gycxlkgList.add(dev);
					}
					
					if(dev.getDeviceType().equals(SystemConstants.Switch)){
						if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
							jdzybkgList.add(dev);
						}
					}
					
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							rbykgList.add(dev);
						}
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							rbykgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							rbykgList.add(dev);
						}
					}
				}
				
				if(rbykgList.size()>0){
					replaceStr += "核实"+CZPService.getService().getDevName(rbykgList)+"热备用/r/n";
				}
				
				List<String> hsnrList = new ArrayList<String>();
				
				for(PowerDevice dev : tempList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += cf.getZbBLTQStrReplace(zbList.get(0));

						replaceStr += "红河地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
						hsnrList.add("核实"+CZPService.getService().getDevName(dev)+"运行正常/r/n");
					}
				}
				
				for (Iterator<PowerDevice> it = allmlkgList.iterator(); it.hasNext();) {
					PowerDevice dev = it.next();

					List<PowerDevice> mxtempList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					
					if(mxtempList.size()<2){
						it.remove();
					}
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() < station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
							fhcmlkgList.add(mlkg);
						}
					}
				}
				
				replaceStr += cf.getYcDkStrReplace(fhcmlkgList, stationName);
				
				for(String hsnr : hsnrList){
					replaceStr += hsnr;
				}
				
				if(fhcmlkgList.size()>0){
					replaceStr += "核实"+CZPService.getService().getDevName(fhcmlkgList)+"热备用/r/n";
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() < station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
							replaceStr += "投入"+(int)mlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() < station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
							replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+(int)mlkg.getPowerVoltGrade()+"kV侧后备保护动作闭锁"+(int)mlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
							
							if(mlkg.getPowerVoltGrade() == 10){
								replaceStr += "投入10kV#X接地变保护动作闭锁"+(int)mlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
					}
				}
				
				if(station.getPowerVoltGrade() == 220){
					replaceStr += "投入"+(int)curDev.getPowerVoltGrade()+"kV母差保护动作闭锁"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() == 10){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
							replaceStr += "投入10kV#X接地变小电阻自投切功能/r/n";
						}
					}
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() == 10&&curDev.getPowerVoltGrade() == 10){
						replaceStr += "投入10kV小电流接地选线装置/r/n";
						break;
					}
				}
				
				tempList.clear();
				tempList.addAll(gycmlkgList);
				tempList.addAll(gycxlkgList);
				
				hsnrList.clear();
				
				for(PowerDevice dev : tempList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "红河地调@遥控用"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"同期合环/r/n";
						hsnrList.add("核实"+CZPService.getService().getDevName(dev)+"运行正常/r/n");
					}
				}
				
				for(PowerDevice dev : tempList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
						hsnrList.add("核实"+CZPService.getService().getDevName(dev)+"热备用/r/n");
					}
				}
				
				for(String hsnr : hsnrList){
					replaceStr += hsnr;
				}
				
				for(PowerDevice dev : tempList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "核实"+(int)dev.getPowerVoltGrade()+"kV备自投装置充电且运行正常/r/n";
					}
				}
				
				if(RuleUtil.isTransformerNQ(zbList.get(0))){
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += cf.getZbNqjxBhStrReplace(zbList,gycmlkgList,gycxlkgList);
						}
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr +=  cf.getXzZbListLxbhltxdStrReplace(zbList, "退出");
						replaceStr += cf.getXzZbZxdTcStrReplace(zbList);
					}
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}