package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Iterator;


public class DataBlock {
	private String className;
	private String entityName;

	private ArrayList<String> indexes = new ArrayList<String>();
	private ArrayList<String> headers = new ArrayList<String>();
	public ArrayList<String> datas = new ArrayList<String>();

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
	
	public void addData(String data) {
		datas.add(data);
	}
	
	public void addHeader(String header) {
		headers.add(header);
	}
	
	public void addIndex(String index) {
		indexes.add(index);
	}
	
	public int getSize() {
		return indexes.size();
	}
	
	public Iterator<String> headerIterator() {
		return headers.iterator();
	}
	
	public Iterator<String> indexIterator() {
		return indexes.iterator();
	}
	
	public Iterator<String> dataIterator() {
		return datas.iterator();
	}

}
