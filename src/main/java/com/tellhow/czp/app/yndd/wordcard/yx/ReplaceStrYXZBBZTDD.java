package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXZBBZTDD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪主变备自投倒电".equals(tempStr)){
			List<PowerDevice> highVoltXlkgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			String curStationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(curDev.getPowerStationID()));
			List<PowerDevice> hlowVoltZbkgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,true, false, false);
			
			if(hlowVoltZbkgList.size() > 0 && zbList.size() > 0){
				replaceStr += "核实站用电已倒由"+(int)hlowVoltZbkgList.get(0).getPowerVoltGrade()+"kV"+CZPService.getService().getDevNum(zbList.get(0))+"站用变供电正常/r/n";
				replaceStr += "核实"+(int)curDev.getPowerVoltGrade()+"kV备自投装置运行正常/r/n";
			}
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
				if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						highVoltXlkgList.add(dev2);
					}
				}
			}
			
			for (Iterator<PowerDevice> it2 = highVoltXlkgList.iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev2, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice line : lineList){
					if(RuleExeUtil.judgeLineFlow(line).equals("0")){
						it2.remove();
					}
				}
			}
			
			for(PowerDevice dev : highVoltXlkgList){
				if(!RuleExeUtil.isDeviceChanged(dev)){
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
					replaceStr += "核实"+CZPService.getService().getDevName(lineList.get(0))+"带电运行正常/r/n";
				}
			}
		
			List<PowerDevice> middlemxList = RuleExeUtil.getMotherLineAllByVol(curDev, RuleExeUtil.getTransformerVolByType(curDev, "middle"));
			
			if(middlemxList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(middlemxList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer,  true, true, true);

				for(PowerDevice dev : lineList){
					List<PowerDevice> lineOtherSideList = RuleExeUtil.getLineOtherSideList(dev);
					if(lineOtherSideList.size()>0){
						for(PowerDevice dev2 : lineOtherSideList){
							List<PowerDevice> swList = RuleExeUtil.getLinkedSwitch(dev2);
							
							if(swList.size()>0){
								if(swList.get(0).getDeviceStatus().equals("1")){
									replaceStr += CZPService.getService().getDevName(CBSystemConstants.getPowerStation(swList.get(0).getPowerStationID()))+"@核实"+CZPService.getService().getDevName(swList.get(0))+"已处热备用/r/n";
								}
							}
						}
					}
				}
			}
			
			List<PowerDevice> lowmxList =RuleExeUtil.getMotherLineAllByVol(curDev, RuleExeUtil.getTransformerVolByType(curDev, "low"));

			if(lowmxList.size()>0){
				List<PowerDevice> drqList = RuleExeUtil.getDeviceList(lowmxList.get(0), SystemConstants.ElecCapacity, SystemConstants.PowerTransformer,  true, true, true);

				for(PowerDevice dev : drqList){
					replaceStr += "玉溪地调@核实"+CZPService.getService().getDevName(CBSystemConstants.getPowerStation(dev.getPowerStationID()))+CZPService.getService().getDevName(dev)+"已处热备用/r/n";
				}
			}
			
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

			List<PowerDevice> xlList = new ArrayList<PowerDevice>();
			
			for(PowerDevice mx : mxList){
				if(mx.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					xlList = RuleExeUtil.getDeviceList(mx, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				}
			}
			
			for(PowerDevice dev : xlList){
				if(RuleExeUtil.judgeLineFlow(dev).equals("0")){
					List<PowerDevice> otherxlList = RuleExeUtil.getLineOtherSideList(dev);
					
					if(otherxlList.size()>0){
						PowerDevice station = CBSystemConstants.getPowerStation(otherxlList.get(0).getPowerStationID());
						
						replaceStr += CZPService.getService().getDevName(station)+"@核实"+CZPService.getService().getDevName(dev)+"可以短时停电倒电/r/n";
						replaceStr += "玉溪地调@核实已对"+CZPService.getService().getDevName(station)+"挂全站停电检修牌/r/n";
					}
				}
			}
			
			if(middlemxList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(middlemxList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer,  true, true, true);

				for(PowerDevice dev : lineList){
					List<Map<String, String>> stationLineList = CommonFunction.getStationLineList(dev);
					
					if(stationLineList.size()>0){
						for(Map<String,String> map : stationLineList){
							String userStationName = map.get("UNIT");
							

							if(userStationName.contains("光伏电站")){
								replaceStr += userStationName+"@核实站内光伏方阵已全部解列/r/n";
								replaceStr += userStationName+"@核实"+CZPService.getService().getDevName(dev)+"可以短时停电倒电/r/n";
							}else{
								replaceStr += userStationName+"@核实"+CZPService.getService().getDevName(dev)+"可以短时停电倒电/r/n";
							}
						}
					}else{
						List<PowerDevice> otherlineList =  RuleExeUtil.getLineOtherSideList(dev);
						
						for(PowerDevice otherline : otherlineList){
							List<PowerDevice> lineSwList = RuleExeUtil.getLinkedSwitch(otherline);

							for(PowerDevice lineSw : lineSwList){
								PowerDevice station = CBSystemConstants.getPowerStation(lineSw.getPowerStationID());
								
								if(lineSw.getDeviceStatus().equals("0")){
									List<PowerDevice> drkgList = new ArrayList<PowerDevice>();
									
									HashMap<String, PowerDevice> mapStationDevice2 = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
									
									for (Iterator<PowerDevice> it2 = mapStationDevice2.values().iterator(); it2.hasNext();) {
										PowerDevice dev2 = it2.next();
										
										if(dev2.getPowerVoltGrade() == 10){
											if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
												drkgList.add(dev2);
											}
										}
									}
									
									for(PowerDevice drkg : drkgList){
										List<PowerDevice> pathList =  RuleExeUtil.getPathByDevice(drkg, null, lineSw, "", "", false, false);
										
										if(pathList!= null && pathList.size()>0){
											List<PowerDevice> drList = RuleExeUtil.getDeviceList(drkg, SystemConstants.ElecCapacity, SystemConstants.PowerTransformer,  true, true, true);
											
											replaceStr += "玉溪地调@核实"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(drList)+"已处热备用/r/n";
										}
									}
									
									replaceStr += "玉溪配调@核实"+CZPService.getService().getDevName(station)+"可以短时停电倒电/r/n";
								}
							}
						}
					}
				}
			}
			
			if(lowmxList.size()>0){
				replaceStr += "玉溪配调@核实"+curStationName+CZPService.getService().getDevName(lowmxList.get(0))+"可以短时停电倒电/r/n";
			}
			
			List<PowerDevice> lineSwList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true,true);;

			if(xlswList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(xlswList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				List<PowerDevice> lineOtherList = RuleExeUtil.getLineOtherSideList(lineList.get(0));
				
				if(lineOtherList.size()>0){
					lineSwList = RuleExeUtil.getLinkedSwitch(lineOtherList.get(0));
					
					if(lineSwList.size()>0){
						PowerDevice station = CBSystemConstants.getPowerStation(lineSwList.get(0).getPowerStationID());
						replaceStr += "玉溪地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(lineSwList.get(0))+"/r/n";
					}
				}
			}
			
			List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						hignVoltMlkgList.add(dev2);
					}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						hignVoltXlkgList.add(dev2);
					}
				}
			}
			
			hignVoltMlkgList.addAll(hignVoltXlkgList);
			
			String temp = "";
			
			for(PowerDevice dev : hignVoltMlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					temp += "断开"+CZPService.getService().getDevName(dev)+"，";
				}
			}
			
			for(PowerDevice dev : hignVoltMlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					temp += "合上"+CZPService.getService().getDevName(dev)+"";
				}
			}
			
			replaceStr += "核实"+(int)curDev.getPowerVoltGrade()+"kV备自投装置动作正确，"+temp+"/r/n";
			
			
			if(lowmxList.size()>0){
				replaceStr += "玉溪配调@通知"+curStationName+CZPService.getService().getDevName(lowmxList.get(0))+"短时停电倒电完毕/r/n";
			}
			
			if(middlemxList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(middlemxList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer,  true, true, true);

				for(PowerDevice dev : lineList){
					List<Map<String, String>> stationLineList = CommonFunction.getStationLineList(dev);
					
					if(stationLineList.size()>0){
						for(Map<String,String> map : stationLineList){
							String userStationName = map.get("UNIT");
							
							replaceStr += userStationName+"@通知"+CZPService.getService().getDevName(dev)+"短时停电倒电完毕/r/n";
						}
					}else{
						List<PowerDevice> lineOtherSideList = RuleExeUtil.getLineOtherSideList(dev);
						for(PowerDevice dev2 : lineOtherSideList){
							lineSwList = RuleExeUtil.getLinkedSwitch(dev2);
							
							for(PowerDevice lineSw : lineSwList){
								PowerDevice station2 = CBSystemConstants.getPowerStation(lineSw.getPowerStationID());
								
								if(lineSw.getDeviceStatus().equals("0")){
									replaceStr += "玉溪配调@通知"+CZPService.getService().getDevName(station2)+"短时停电倒电完毕/r/n";
								}
							}
						}
					}
				}
			}
			
			if(xlswList.size()>0){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(xlswList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				List<PowerDevice> lineOtherList = RuleExeUtil.getLineOtherSideList(lineList.get(0));
				
				if(lineOtherList.size()>0){
					lineSwList = RuleExeUtil.getLinkedSwitch(lineOtherList.get(0));
					
					if(lineSwList.size()>0){
						PowerDevice otherstation = CBSystemConstants.getPowerStation(lineSwList.get(0).getPowerStationID());
						replaceStr += "玉溪地调@遥控合上"+CZPService.getService().getDevName(otherstation)+CZPService.getService().getDevName(lineSwList.get(0))+"对线路充电/r/n";
					}
				}
			}
			
			for(PowerDevice dev : xlList){
				if(RuleExeUtil.judgeLineFlow(dev).equals("0")){
					List<PowerDevice> otherxlList = RuleExeUtil.getLineOtherSideList(dev);
					
					if(otherxlList.size()>0){
						PowerDevice station2 = CBSystemConstants.getPowerStation(otherxlList.get(0).getPowerStationID());
						
						replaceStr += "玉溪地调@核实已摘除"+CZPService.getService().getDevName(station2)+"全站停电检修牌/r/n";
					}
				}
			}
		}
			
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		
		return replaceStr;
	}
	
}
