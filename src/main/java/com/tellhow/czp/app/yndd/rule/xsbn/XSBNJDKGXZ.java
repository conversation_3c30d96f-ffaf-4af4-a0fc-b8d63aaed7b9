package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.czp.app.yndd.view.EquipCheckChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class XSBNJDKGXZ implements RulebaseInf {
	public static List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
	
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		
		chooseEquips.clear();
		
		String begin = CBSystemConstants.getCurRBM().getBeginStatus();
		String end = CBSystemConstants.getCurRBM().getEndState();
		
		String showMessage = "";
		
		if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
			showMessage = "请选择装设三相接地线的线路";
		}else{
			showMessage = "请选择拆除三相接地线的线路";
		}
		
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
	    List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
		PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());

		lineList.add(sourceLineTrans);
		lineList.addAll(loadLineTrans);
		
		String[] stationArr = {"版纳_220kV_普文牵引变","普洱_220kV_木乃河电站","版纳_35kV_流沙河六级水电站","版纳_35kV_流沙河七级水电站"};
		
	    for (Iterator iterator = lineList.iterator(); iterator.hasNext();) {
			PowerDevice dev = (PowerDevice)iterator.next();
			
			if(dev.getPowerStationName().contains("虚拟站")){
				iterator.remove();
			}else{
				for(String station : stationArr){
					if(dev.getPowerStationName().contains(station)){
						iterator.remove();
					}
				}
			}
		}
	    
		List<Map<String, String>> stationLineList = CommonFunctionBN.getStationLineList(pd);
	    
	    if(stationLineList.size()>0){
			for(Map<String,String> map : stationLineList){
				PowerDevice newLine = new PowerDevice();
				newLine.setPowerDeviceID(StringUtils.ObjToString(map.get("ID")));
				newLine.setPowerDeviceName(StringUtils.ObjToString(map.get("LINE_NAME")));
				
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				
				if(!lowerunit.equals("")){
					newLine.setPowerStationName(lowerunit);
				}else{
					newLine.setPowerStationName(StringUtils.ObjToString(map.get("UNIT")));
				}
				
				lineList.add(newLine);
			}
		}
	    
	    EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, lineList , showMessage);
	    chooseEquips=ecc.getChooseEquip();
		
	    if(ecc.isCancel()){
	    	return false;
	    }
	    
		return true;
	}

}
