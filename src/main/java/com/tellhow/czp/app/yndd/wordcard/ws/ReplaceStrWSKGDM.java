package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.List;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionWS;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSKGDM implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("文山开关倒母".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			String curMxName = "";
			String otherMxName = "";
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

			for(PowerDevice dev : dzList){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
					
					if(dev.getDeviceStatus().equals("0")){
						for(PowerDevice mx : mxList){
							otherMxName = CZPService.getService().getDevName(mx);
							break;
						}
					}else if(dev.getDeviceStatus().equals("1")){
						for(PowerDevice mx : mxList){
							curMxName = CZPService.getService().getDevName(mx);
							break;
						}
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("1")){
				if(CommonFunctionWS.ifSwitchControl(curDev)&&CommonFunctionWS.ifSwitchSeparateControl(curDev)){
					replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
					
					for(PowerDevice dev : mlkgList){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
					}
					
					replaceStr += "文山地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
				}else{
					replaceStr += stationName+"@将"+deviceName+"由"+curMxName+"热备用倒至"+otherMxName+"热备用/r/n";
				}
			}else if(curDev.getDeviceStatus().equals("0")){
				if(CommonFunctionWS.ifSwitchControl(curDev)&&CommonFunctionWS.ifSwitchSeparateControl(curDev)){
					replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
					
					for(PowerDevice dev : mlkgList){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
					}
					
					replaceStr += "文山地调@执行"+stationName+deviceName+"由"+curMxName+"运行倒至"+otherMxName+"运行程序操作/r/n";
					
					for(PowerDevice dev : mlkgList){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
					}
					
					replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
					
				}else{
					replaceStr += stationName+"@将"+deviceName+"由"+curMxName+"运行倒至"+otherMxName+"运行/r/n";
				}
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
}
