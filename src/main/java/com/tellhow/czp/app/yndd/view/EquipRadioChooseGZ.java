/*
 * EquipRadioChoose.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.app.yndd.view;

import java.awt.Component;
import java.awt.Font;
import java.awt.Toolkit;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

import javax.swing.ButtonGroup;
import javax.swing.DefaultCellEditor;
import javax.swing.JCheckBox;
import javax.swing.JOptionPane;
import javax.swing.JRadioButton;
import javax.swing.JTable;
import javax.swing.event.TableModelEvent;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableCellRenderer;
import javax.swing.table.TableColumn;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 *
 * <AUTHOR>
 */
public class EquipRadioChooseGZ extends javax.swing.JDialog {

	private List<PowerDevice> equipList = new ArrayList<PowerDevice>();
	private PowerDevice chooseDev = null;

	/** Creates new form EquipRadioChoose */
	public EquipRadioChooseGZ(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, String showMessage) {
		super(parent, modal);
		initComponents();
		jLabel1.setText(showMessage);
		jLabel1.setFont(new Font("宋体",Font.BOLD,14));
		if (equipsList != null)
			this.equipList = equipsList;
		this.initTable(Boolean.FALSE);
		FitTableColumns(jTable1);
		this.setLocationCenter();
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jLabel1 = new javax.swing.JLabel();
		jButton1 = new javax.swing.JButton();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable() {
			public void tableChanged(TableModelEvent e) {
				super.tableChanged(e);
				repaint();
			}
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jLabel1.setText("jLabel1");

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
		jButton1.setToolTipText("\u9009\u62e9");
		jButton1.setText("\u9009\u62e9");
		jButton1.setMargin(new java.awt.Insets(1,1,1,1));
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jScrollPane1.setViewportView(jTable1);

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(jLabel1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
						265, Short.MAX_VALUE)
				.add(jScrollPane1,
						org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 300,
						Short.MAX_VALUE)
				.add(org.jdesktop.layout.GroupLayout.TRAILING,
						layout.createSequentialGroup().addContainerGap()
								.add(jButton1)));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(jLabel1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								33,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jButton1)
						.add(6, 6, 6)
						.add(jScrollPane1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								175,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//选择
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		DefaultTableModel jtableModel = (DefaultTableModel) this.jTable1
				.getModel();
		int rows = jtableModel.getRowCount();
		JRadioButton jb = null;
		for (int i = 0; i < rows; i++) {
			jb = (JRadioButton) jtableModel.getValueAt(i, 0);
			if (jb.isSelected()) {
				CodeNameModel cnm = (CodeNameModel)jtableModel.getValueAt(i, 2);
				for(PowerDevice equip : equipList) {
					if(equip.getPowerDeviceID().equals(cnm.getCode())) {
						this.chooseDev = equip;
						break;
					}
				}
			}
		}
		if (chooseDev == null) {
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "没有选择设备！", "提示", JOptionPane.WARNING_MESSAGE);
			return;
		}
		this.setVisible(false);
		this.dispose();
	}
	public void setChooseEquip(PowerDevice equip) {//黄翔修改，设置选择参数中的设备
		DefaultTableModel jtableModel = (DefaultTableModel) this.jTable1
		.getModel();
		int rows = jtableModel.getRowCount();
		JRadioButton jb = null;
		for (int i = 0; i < rows; i++) {
			jb = (JRadioButton) jtableModel.getValueAt(i, 0);
			
			CodeNameModel cnm = (CodeNameModel)jtableModel.getValueAt(i, 2);
			if(cnm.getCode().equals(equip.getPowerDeviceID())) {
				this.chooseDev = equip;
				jb.setSelected(true);
				break;
			}
		}
		this.setVisible(false);
		this.dispose();
		}
	public void initTable(Boolean ischoose) {
		DefaultTableModel jtableModel = new DefaultTableModel(null,
				new String[] { "选择", "厂站名称", "设备名称" }) {
		};
		PowerDevice pd = null;
		
		equipList=RuleExeUtil.sortListByDevName(equipList);
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			//如果设备是母线，处理设备名称 2018.1.25
			if(pd.getDeviceType().equals(SystemConstants.MotherLine)){
				String mxName = CZPService.getService().getDevName(pd);
				if(mxName.indexOf("M")<0&&mxName.indexOf("母")<0){
					jtableModel.addRow(new Object[] { new JRadioButton(),
							CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pd.getPowerStationID())), new CodeNameModel(pd.getPowerDeviceID(), mxName+"M") });
				}
				else {
					jtableModel.addRow(new Object[] { new JRadioButton(),
							CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pd.getPowerStationID())), new CodeNameModel(pd.getPowerDeviceID(), CZPService.getService().getDevName(pd)) });
				}
			}
			else{
				jtableModel.addRow(new Object[] { new JRadioButton(),
						CZPService.getService().getDevName(CBSystemConstants.getPowerStation(pd.getPowerStationID())), new CodeNameModel(pd.getPowerDeviceID(), CZPService.getService().getDevName(pd)) });
			
			}
		}
		JRadioButton jb = null;
		int rows = jtableModel.getRowCount();
		ButtonGroup group1 = new ButtonGroup();
		for (int i = 0; i < rows; i++) {
			jb = (JRadioButton) jtableModel.getValueAt(i, 0);
			group1.add(jb);
		}

		jTable1.setModel(jtableModel);
		jTable1.setRowHeight(30);
		jTable1.getColumn("选择").setCellRenderer(new ercRadioButtonRenderer());
		jTable1.getColumn("选择").setCellEditor(
				new ercRadioButtonEditor2(new JCheckBox()));
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		jTable1.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				if(jTable1.getSelectedRowCount() == 1) {
					JRadioButton jb = (JRadioButton)jTable1.getValueAt(jTable1.getSelectedRow(), 0);
					jb.setSelected(true);
					jTable1.updateUI();
				}
			}
			public void mousePressed(java.awt.event.MouseEvent evt) {
				int rowIndex = jTable1.rowAtPoint(evt.getPoint());
				if(jTable1.getSelectedRowCount() == 1) {
					jTable1.clearSelection();
					jTable1.setRowSelectionInterval(rowIndex,rowIndex);
					JRadioButton jb = (JRadioButton)jTable1.getValueAt(rowIndex, 0);
					jb.setSelected(true);
					jTable1.updateUI();
				}
			}
		});
	}

	public PowerDevice getChooseEquip() {
		this.setVisible(true);
		return this.chooseDev;
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				EquipRadioChooseGZ dialog = new EquipRadioChooseGZ(
						new javax.swing.JFrame(), true, null, null);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTable1;
	// End of variables declaration//GEN-END:variables
	//Jtable列宽自适应
			public void FitTableColumns(JTable myTable){  
			    JTableHeader header = myTable.getTableHeader();  
			     int rowCount = myTable.getRowCount();  
			  
			     Enumeration columns = myTable.getColumnModel().getColumns();  
			     while(columns.hasMoreElements()){  
			         TableColumn column = (TableColumn)columns.nextElement();  
			         int col = header.getColumnModel().getColumnIndex(column.getIdentifier());  
			         int width = (int)myTable.getTableHeader().getDefaultRenderer()  
			                 .getTableCellRendererComponent(myTable, column.getIdentifier()  
			                         , false, false, -1, col).getPreferredSize().getWidth();  
			         for(int row = 0; row<rowCount; row++){  
			             int preferedWidth = (int)myTable.getCellRenderer(row, col).getTableCellRendererComponent(myTable,  
			               myTable.getValueAt(row, col), false, false, row, col).getPreferredSize().getWidth();  
			             width = Math.max(width, preferedWidth)+2;  
			         }  
			         header.setResizingColumn(column); // 此行很重要  
			         column.setWidth(width+myTable.getIntercellSpacing().width);  
			     }  
			}  
}

class ercRadioButtonRenderer implements TableCellRenderer {
	public Component getTableCellRendererComponent(JTable table, Object value,
			boolean isSelected, boolean hasFocus, int row, int column) {
		if (value == null)
			return null;
		return (Component) value;
	}
}

class ercRadioButtonEditor2 extends DefaultCellEditor implements ItemListener {
	private JRadioButton button;

	public ercRadioButtonEditor2(JCheckBox checkBox) {
		super(checkBox);
	}

	public Component getTableCellEditorComponent(JTable table, Object value,
			boolean isSelected, int row, int column) {
		if (value == null)
			return null;
		button = (JRadioButton) value;
		button.addItemListener(this);
		return (Component) value;
	}

	public Object getCellEditorValue() {
		button.removeItemListener(this);
		return button;
	}

	public void itemStateChanged(ItemEvent e) {
		super.fireEditingStopped();
	}
	
}


