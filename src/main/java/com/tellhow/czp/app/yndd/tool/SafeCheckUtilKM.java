package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.log4j.Logger;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.model.CardWordMode;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

public class SafeCheckUtilKM {
	public Logger log=Logger.getLogger(this.getClass());
	
	public static Object[] init(String dynStr,char beginSpilt,char endSplit) {
		Object[] results = new Object[3];
		List<String> keyFirst=new ArrayList<String>();
		List<String> keyStr=new ArrayList<String>();
		String lastStr="";
		StringBuffer buff = new StringBuffer(dynStr);
		int len = buff.length();
		int start = 0, ptr = 1;
		boolean noMatching=true;
		for (ptr = start; ptr < len; ptr++) {
			if (buff.charAt(ptr) == beginSpilt) {
				if (ptr == 0 || buff.charAt(ptr - 1) != '\\') {
					if (ptr > 2 && buff.charAt(ptr - 2) == '\\') {
						buff.deleteCharAt(ptr - 2);
						--ptr;
					}
					int end = ptr;
					noMatching=false;
					keyFirst.add(buff.substring(start, end));
					ptr += 2;
					for (; ptr < len; ptr++) {
						if (buff.charAt(ptr) == endSplit
								&& buff.charAt(ptr) != '\\') {
							if (buff.charAt(ptr - 2) == '\\') {
								buff.deleteCharAt(ptr - 2);
								--ptr;
							}
							keyStr.add(buff.substring(end+1,ptr));
							start = ptr+1;
							noMatching=true;
							break;
						}
					}
				}
			}
		}
		if(noMatching && ptr<=len){
			lastStr=buff.substring(start,ptr);
		}
		results[0]=keyFirst;
		results[1]=keyStr;
		results[2]=lastStr;
		return results;
	}
	
	public static List<Map<String, String>> getDeviceInfoByWord(List<String> paramsKey,String word){
		List<String> busList = new ArrayList<String>();
		List<String> stationList = new ArrayList<String>();
		List<String> statusList = new ArrayList<String>();
		List<String> deviceTypeList = new ArrayList<String>();
		List<String> deviceNameList = new ArrayList<String>();

		List<Map<String,String>> resultList = new ArrayList<Map<String,String>>(); 
		
		for (int j = 0; j < paramsKey.size(); j++) {
			String tempStr=paramsKey.get(j).toString(); //标签
			
			if(tempStr.equals("厂站名称")){
				String[] stationArr = {"变","站","厂","电场"};
				String station = word;
				
				for(String stationKey : stationArr){
					if(station.contains(stationKey)){
						if(station.contains("主变")){
							if(station.indexOf(stationKey)<station.indexOf("主变")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("站用变")){
							if(station.indexOf(stationKey)<station.indexOf("站用变")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("移动变")){
							if(station.indexOf(stationKey)<station.indexOf("移动变")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("接地变")){
							if(station.indexOf(stationKey)<station.indexOf("接地变")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("硅厂")){
							if(station.indexOf(stationKey)<station.indexOf("硅厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("铝厂")){
							if(station.indexOf(stationKey)<station.indexOf("铝厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("钢厂")){
							if(station.indexOf(stationKey)<station.indexOf("钢厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("铜厂")){
							if(station.indexOf(stationKey)<station.indexOf("铜厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("肥厂")){
							if(station.indexOf(stationKey)<station.indexOf("肥厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("盐厂")){
							if(station.indexOf(stationKey)<station.indexOf("盐厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("石厂")){
							if(station.indexOf(stationKey)<station.indexOf("石厂")){
								if(station.contains("水电厂")){
									String stationName = word.substring(0, station.indexOf("水电厂")+3);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}else{
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}
						}else if(station.contains("粉厂")){
							if(station.indexOf(stationKey)<station.indexOf("粉厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("车厂")){
							if(station.indexOf(stationKey)<station.indexOf("车厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("仁厂")){
							if(station.indexOf(stationKey)<station.indexOf("仁厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("垃圾电厂")){
							if(station.indexOf(stationKey)<station.indexOf("垃圾电厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("垃圾厂")){
							if(station.indexOf(stationKey)<station.indexOf("垃圾厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("龙厂")){
							if(station.indexOf(stationKey)<station.indexOf("龙厂")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("（至")){
							if(station.indexOf(stationKey)<station.indexOf("（至")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("线") || station.contains("回")){
							if(station.indexOf("水泥厂")>0&&(station.indexOf("水泥厂")<station.indexOf("线")
									||station.indexOf(stationKey)<station.indexOf("回"))){
								if(station.indexOf(stationKey)<station.indexOf("水泥厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf("宣厂")>0&&station.indexOf("宣厂")<station.indexOf("线")){
								if(station.indexOf(stationKey)<station.indexOf("宣厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf("烟厂")>0&&station.indexOf("烟厂")<station.indexOf("线")){
								if(station.indexOf(stationKey)<station.indexOf("烟厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf("邑厂")>0&&station.indexOf("邑厂")<station.indexOf("线")){
								if(station.indexOf(stationKey)<station.indexOf("邑厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf("黄磷厂")>0&&station.indexOf("黄磷厂")<station.indexOf("线")){
								if(station.indexOf(stationKey)<station.indexOf("黄磷厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf("和平厂")>0&&station.indexOf("和平厂")<station.indexOf("回")){
								if(station.indexOf(stationKey)<station.indexOf("和平厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf(stationKey)<station.indexOf("线") || station.indexOf(stationKey)<station.indexOf("回")){
								if(station.indexOf("站用变")>0){
									if(station.indexOf(stationKey)<station.indexOf("站用变")){
										String stationName = word.substring(0, station.indexOf(stationKey)+1);
										stationList.add(stationName);
										word = word.substring(stationName.length());
									}
								}else{
									String stationName = word.substring(0, station.indexOf(stationKey)+stationKey.length());
									
									if(stationName.length()>6){
										stationList.add(stationName);
										word = word.substring(stationName.length());
									}
								}
							}
						}else{
							String stationName = word.substring(0, station.indexOf(stationKey)+stationKey.length());
							stationList.add(stationName);
							word = word.substring(stationName.length());
						}
						break;
			        }
				}
			}else if(tempStr.equals("设备名称")){
				word = word.replace("及母线设备", "");
				
				if(word.contains("断路器")&&!word.contains("隔离开关")){
					if(word.contains("、")&&!word.contains("母联")&&!word.contains("分段")&&!word.contains("主变")&&!word.contains("线、")&&!word.contains("回、")){
						String[] arr = word.split("、");
						
						word = word.replace("、", "");
						
						for(String words : arr){
							if(words.contains("断路器")){
								String devName = words.substring(0, words.lastIndexOf("断路器")+3);
								deviceNameList.add(devName);
			                    word = word.replace(devName, "");
							}else{
								String regx = ".*?(母|装置|发变组)";
								Pattern compile = Pattern.compile(regx);
				                Matcher matcher = compile.matcher(word);

				                if(matcher.find()){//解析
				                	String group = matcher.group();
				                	
									deviceNameList.add(group);
				                    word = word.replace(group, "");
				                }
							}
						}
					}else{
						String devName = word.substring(0, word.lastIndexOf("断路器")+3);
						deviceNameList.add(devName);
	                    word = word.replace(devName, "");
					}
				}else if(word.contains("接地开关")){
					String devName = word.substring(0, word.lastIndexOf("接地开关")+4);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("刀闸")){
					String devName = word.substring(0, word.lastIndexOf("刀闸")+2);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("隔离开关手车")){
					String devName = word.substring(0, word.lastIndexOf("隔离开关手车")+6);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("隔离开关")){
					String devName = word.substring(0, word.lastIndexOf("隔离开关")+4);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("隔离手车")){
					String devName = word.substring(0, word.lastIndexOf("隔离手车")+4);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("手车")){
					String devName = word.substring(0, word.lastIndexOf("手车")+2);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("电容器")){
					String devName = word.substring(0, word.lastIndexOf("电容器")+3);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("电抗器")){
					String devName = word.substring(0, word.lastIndexOf("电抗器")+3);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("站用变")){
					String devName = word.substring(0, word.lastIndexOf("站用变")+3);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else{
					String regx = ".*?(主变|母|母线|回|线|装置|发变组)";
					Pattern compile = Pattern.compile(regx);
	                Matcher matcher = compile.matcher(word);

	                if(matcher.find()){//解析
						deviceNameList.add(matcher.group());
	                    word = word.replace(matcher.group(), "");
	                }
				}
				deviceTypeList.add("一次设备");
			}else if(tempStr.equals("设备状态")){
				String regx = ".*?(运行|热备用|冷备用|检修)";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
                	String group = matcher.group();
                	statusList.add(group);
                    word = word.substring(word.indexOf(group)+group.length(), word.length());
                    
                }
			}else if(tempStr.equals("二次设备")){
				String regx = ".*?(备自投装置)";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
                	String group = matcher.group();
                	deviceNameList.add(matcher.group());
                	deviceTypeList.add("二次设备");
                    word = word.substring(word.indexOf(group)+group.length(), word.length());
                }
			}else if(tempStr.equals("所在母线")){
				String regx = ".*?(母)";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
                	String group = matcher.group();
                	busList.add(group);
                    word = word.substring(word.indexOf(group)+group.length(), word.length());
                }
			}else if(tempStr.equals("所在线路")){
				String regx = ".*?(线)";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
                	String group = matcher.group();
                    word = word.substring(word.indexOf(group)+group.length(), word.length());
                }
			}
		}
		
		
		for(String deviceName : deviceNameList){
			Map<String,String> resultMap = new HashMap<String, String>();

			resultMap.put("设备名称", deviceName);
			
			for(String stationName : stationList){
				resultMap.put("厂站名称", stationName);
			}
			
			for(String deviceType : deviceTypeList){
				resultMap.put("设备类型", deviceType);
			}
			
			resultList.add(resultMap);
		}
		
		for(String status : statusList){
			Map<String,String> resultMap = new HashMap<String, String>();

			resultMap.put("设备状态", status);
			
			resultList.add(resultMap);
		}
		
		if(word.length() > 0){
			resultList.clear();
		}
		
		return resultList;
	}	
	
	public static List<RuleBaseMode> setLcmInfo(String stationName,String code){
		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();

		RuleBaseMode rbmInfo = new RuleBaseMode();
		rbmInfo.setCheckout(false);
		rbmList.add(rbmInfo);
		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
		PowerDevice pd = new PowerDevice();
		pd.setPowerStationName(stationName);
		pdlist.add(pd);
		rbmInfo.setPd(pd);
		CheckMessage cm = new CheckMessage();
		cm.setPd(pdlist);
		cm.setBottom(code);
		if(CBSystemConstants.lcm==null){
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		CBSystemConstants.lcm.add(cm);
		return rbmList;
	}
	
	public static double checkSame(String Str_1,String Str_2) {
		Str_1 = Str_1.replace("\n", "");
		
		int Length1=Str_1.length();
		int Length2=Str_2.length();
		
		int Distance=0;
		if (Length1==0) {
			Distance=Length2;
		}
		if(Length2==0)
		{
			Distance=Length1;
		}
		if(Length1!=0&&Length2!=0){
			int[][] Distance_Matrix=new int[Length1+1][Length2+1];
			//编号
			int Bianhao=0;
			for (int i = 0; i <= Length1; i++) {
					Distance_Matrix[i][0]=Bianhao;
					Bianhao++;
			}
			Bianhao=0;
			for (int i = 0; i <=Length2; i++) {
				Distance_Matrix[0][i]=Bianhao;
				Bianhao++;
			}
			
			
			char[] Str_1_CharArray=Str_1.toCharArray();
			char[] Str_2_CharArray=Str_2.toCharArray();
			
			
			for (int i = 1; i <= Length1; i++) {
				for(int j=1;j<=Length2;j++){
					if(Str_1_CharArray[i-1]==Str_2_CharArray[j-1]){
						Distance=0;
					}	
					else{
						Distance=1;
					}
						
						int Temp1=Distance_Matrix[i-1][j]+1;
						int Temp2=Distance_Matrix[i][j-1]+1;
						int Temp3=Distance_Matrix[i-1][j-1]+Distance;
						
						Distance_Matrix[i][j]=Temp1>Temp2?Temp2:Temp1;
						Distance_Matrix[i][j]=Distance_Matrix[i][j]>Temp3?Temp3:Distance_Matrix[i][j];
					
				}
				
			}
			
			Distance=Distance_Matrix[Length1][Length2];
		}
		
		double Aerfa=1-1.0*Distance/(Length1>Length2?Length1:Length2);
		
		return Aerfa;
	}
	
	public static List<CardWordMode> getCardWordModeList(String station,String word){
		List<CardWordMode> resultList = new ArrayList<CardWordMode>();
		
		String sql="SELECT MODELDESC,BEGINSTATUS,ENDSTATUS,OPERATION FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL ORDER BY TO_NUMBER(ORDERID) ASC";
	    List<Map<String,String>> modelList=DBManager.queryForList(sql);
		
	    for(Map<String,String> model : modelList){
    		String modeldesc = StringUtils.ObjToString(model.get("MODELDESC"));
    		String beginStatus = StringUtils.ObjToString(model.get("BEGINSTATUS"));
    		String endStatus = StringUtils.ObjToString(model.get("ENDSTATUS"));
    		String operation = StringUtils.ObjToString(model.get("OPERATION"));
    		
			Object[] splitParams = init(modeldesc,'[',']');
			List<String> paramsKey=(ArrayList<String>)splitParams[1]; //标签集合
			
			if(paramsKey.size()>0){
				List<String> firstStr=(ArrayList)splitParams[0];
				String lastStr=splitParams[2].toString();

				StringBuffer descBuff=new StringBuffer();
				
				for(String first : firstStr){
					descBuff.append(first+"(.*)");
				}
				
				descBuff.append(lastStr);
				
				String regx = "^"+descBuff.toString()+"$";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
                	System.out.println("模板内容："+regx);
                	System.out.println("操作内容："+word);

            		String[] splitregxs = regx.replace("^", "").replace("$", "").replace("(.*)", "|").split("\\|");
            		
            		for(String string : splitregxs){
            			if(string.contains("转") && word.contains("转线")){
        					word = word.substring(0, word.lastIndexOf("转"))+word.substring(word.lastIndexOf("转")+1, word.length());
        					continue;
        				}
        				
        				if(string.equals("倒由")){
        					word = word.replace("倒", "");
        				}
        				
        				word = word.replace(string, "");
            		}
            		
            		List<Map<String,String>> returnList = getDeviceInfoByWord(paramsKey,word);
            		
            		if(returnList.size() == 0){
            			continue;
            		}
            		
            		CardWordMode cwm = new CardWordMode();
        			
        			if(!beginStatus.equals("")){
        				cwm.setBeginStatus(beginStatus);
        			}
        				
        			if(!endStatus.equals("")){
        				cwm.setEndStatus(endStatus);
        			}
        				
        			if(!operation.equals("")){
        				cwm.setOperaTion(operation);
        			}
        			
        			List<PowerDevice> devList = new ArrayList<PowerDevice>();
        			
        			for(Map<String,String> returnMap : returnList){
        				PowerDevice dev = new PowerDevice();

        				if(returnMap.containsKey("厂站名称")){
        					dev.setPowerStationName(returnMap.get("厂站名称"));
        				}
        					
        				if(returnMap.containsKey("设备名称")){
        					dev.setPowerDeviceName(returnMap.get("设备名称"));
        				}
        					
        				if(returnMap.containsKey("设备状态")){
        					if(cwm.getBeginStatus().equals("")){
        						cwm.setBeginStatus(returnMap.get("设备状态"));
        					}else if(cwm.getEndStatus().equals("")){
        						cwm.setEndStatus(returnMap.get("设备状态"));
        					}
        				}
        				
        				if(returnMap.containsKey("所在母线")){
        					cwm.setBusBar(returnMap.get("所在母线"));
        				}
        				
        				if(returnMap.containsKey("设备类型")){
        					cwm.setDeviceKind(returnMap.get("设备类型"));
        				}
        				
        				if(dev.getPowerStationName().equals("")){
        					dev.setPowerStationName(station);
        				}
        				
        				if(!dev.getPowerDeviceName().equals("")){
        					devList.add(dev);
        				}
        			}
        			
        			cwm.setPdList(devList);
        			resultList.add(cwm);
        			
        			for(PowerDevice device : devList){
        				System.out.println("厂站名称："+device.getPowerStationName());
        				System.out.println("设备名称："+device.getPowerDeviceName());
        			}
        			
        			System.out.println("初始状态："+cwm.getBeginStatus());
        			System.out.println("目标状态："+cwm.getEndStatus());
        			System.out.println("操作："+cwm.getOperaTion());
        			System.out.println("所在母线："+cwm.getBusBar());
        			System.out.println("****************************************");
            		
            		break;
                }
			}
	    }
	    
		return resultList;
	}
	
	/*
	 * 校核厂站名称
	 */
	public static List<String> checkStationName(String subordinateStationName){
		List<String> stationIDList = new ArrayList<String>();
		//厂站名称校验
		for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();){
			PowerDevice st = it.next();
			
			String modelDevName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(st));
			subordinateStationName = StringUtils.killVoltInDevName(subordinateStationName);
			
			if(modelDevName.equals(subordinateStationName)) {
				String stationID = st.getPowerDeviceID();
				stationIDList.add(stationID);
				
				if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
					CreatePowerStationToplogy.loadFacEquip(stationID);
				}
			}
		}
		
		if(stationIDList.size() > 1){
			for(Iterator<String> it = stationIDList.iterator();it.hasNext();){
				String stationID = it.next();
				
				String sql = "SELECT STATIONID FROM " + CBSystemConstants.equipUser + "T_SUBSTATION_TREE WHERE STATIONID = '"+stationID+"' AND ISREMOVE = '1'";
				List<Map<String,String>> idList = DBManager.queryForList(sql);
				
				if(idList.size()==1){
					it.remove();
				}
			}
		}
		
		return stationIDList;
	}
	
	/*
	 * 校核电压等级
	 */
	public static boolean checkVoltName(String word){
	    boolean isRightVolt = true;

		String sql = "SELECT NAME FROM " + CBSystemConstants.opcardUser + "T_A_CARDWORD WHERE CODE = '电压等级'";
		List<Map<String,String>> voltList = DBManager.query(sql);
		
		if(word.contains("kV")){
			String tempword = word;
			
			for(Map<String,String> map : voltList){
				String name = StringUtils.ObjToString(map.get("NAME"));
				
				tempword = tempword.replace(name, "");
			}
			
			if(tempword.contains("kV")){
				isRightVolt = false;
			}else if(tempword.contains("k")&&!tempword.contains("V")){
				isRightVolt = false;
			}else if(tempword.contains("V")&&!tempword.contains("k")){
				isRightVolt = false;
			}
		}else{
			isRightVolt = false;
		}
		
		return isRightVolt;
	}
	
	/*
	 * 校核设备名称
	 */
	public static List<RuleBaseMode> checkDeviceName(List<CardWordMode> wordRbmList,String stationID){
		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();
		
		if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
			CreatePowerStationToplogy.loadFacEquip(stationID);
		}
		
		HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationID);
		
		if(devMap != null){
			for(CardWordMode cwm : wordRbmList){
				List<PowerDevice> devList = cwm.getPdList();
				
				if(cwm.getDeviceKind().equals("二次设备")){
					
				}else{
					for(PowerDevice device : devList){
						String equipTypeFlag = "";
						String equipTypeName = "";
						String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
								SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine,
								SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate,
								SystemConstants.Switch, SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate,
								SystemConstants.SwitchSeparate, SystemConstants.Switch,
								SystemConstants.Switch,SystemConstants.VolsbTransformer, SystemConstants.MotherLine,
								SystemConstants.MotherLine, SystemConstants.InOutLine,SystemConstants.InOutLine,
								SystemConstants.PowerTransformer, SystemConstants.ElecShock,
								SystemConstants.ElecCapacity ,SystemConstants.PowerTransformer};
						String[] key = new String[] { "接地刀闸","接地开关", "地刀", "隔离刀闸","隔离开关","隔离手车","小车开关","手车", "小车", "刀闸", "断路器",
								"开关","PT", "母线", "母", "线","回", "主变", "电抗器", "电容器","#变"};
						for (int i = 0; i < key.length; i++) {
							if (device.getPowerDeviceName().lastIndexOf(key[i]) >= 0) {
								equipTypeFlag = type[i];
								equipTypeName = key[i];
								break;
							}
						}
						
						String devName = CZPService.getService().getDevName(device);
						
						String volStr = "";
						
						if(device.getPowerDeviceName().toLowerCase().split("kv").length >= 3){
							volStr = device.getPowerDeviceName().toLowerCase().substring(device.getPowerDeviceName().toLowerCase().indexOf("kv")+2);
						}else if(device.getPowerDeviceName().contains("中性点接地开关")){
							volStr = "";
						}else{
							volStr = device.getPowerDeviceName();
						}
						
						//获取电压等级
						String volt = StringUtils.getVoltInDevName(volStr);
						List<PowerDevice> pdList = new ArrayList<PowerDevice>();
						
						for (PowerDevice dev : devMap.values()) {
							if (!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
								continue;
							if (!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
								continue;
							if(dev.getPowerDeviceName().contains("A相")||dev.getPowerDeviceName().contains("B相")||dev.getPowerDeviceName().contains("C相"))
								continue;
							if(dev.getPowerDeviceName().contains("压板"))
								continue;
							
							if(equipTypeFlag.equals(SystemConstants.InOutLine)){
								if (CZPService.getService().getDevName(dev).equals(device.getPowerDeviceName())){
									pdList.add(dev);
									if(pdList.size()>1){
										break;
									}
								}
							}else if(CZPService.getService().getDevName(dev).equals(devName)&&!devName.equals("")) {
								if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
									pdList.add(dev);
									if(pdList.size()>1){
										break;
									}
								}else{
									pdList.add(dev);
									if(pdList.size()>1){
										break;
									}
								}
							}
						}
						
						PowerDevice pd = new PowerDevice();
						
						if(pdList.size()>1){
							pd = new PowerDevice();
							
							String deviceName = device.getPowerDeviceName();
							String station = device.getPowerStationName();

							pd.setPowerStationName(station);
							pd.setPowerDeviceName(deviceName);
							RuleBaseMode rbmInfo = new RuleBaseMode();
							rbmInfo.setCheckout(false);
							rbmList.clear();
							rbmList.add(rbmInfo);
							rbmInfo.setPd(pd);
							CheckMessage cm = new CheckMessage();
							cm.setPd(pdList);
							cm.setBottom("310");
							if(CBSystemConstants.lcm==null)
							CBSystemConstants.lcm=new ArrayList<CheckMessage>();
							CBSystemConstants.lcm.add(cm);
							return rbmList;
						}else if(pdList.size()==1){
							pd = pdList.get(0);
							
							if(!pd.getPowerDeviceID().equals("")){
								System.out.println("设备ID："+pd.getPowerDeviceID());
								
								String modelName = pd.getPowerDeviceName();
								String deviceName = device.getPowerDeviceName();
								String beginStatus = cwm.getBeginStatus();
								String endStatus = cwm.getEndStatus();
								
								RuleBaseMode rbm = new RuleBaseMode();
								rbm.setPd(pd);
								rbm.setBeginStatus(RuleExeUtil.getNumStatusNew(beginStatus));
								rbm.setEndState(RuleExeUtil.getNumStatusNew(endStatus));
								rbm.setOperaTion(cwm.getOperaTion());
								rbmList.add(rbm);
								
								/*if(!modelName.equals(deviceName)){
						        	CheckMessage cm = new CheckMessage();
						     		cm.setBottom("307");
						     		cm.setDeviceName(deviceName);
						     		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						     		pdlist.add(pd);
									cm.setPd(pdlist);
						     		if(CBSystemConstants.lcm==null){
						     			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
						     		}
						     		CBSystemConstants.lcm.add(cm);
								}*/
							}
						}else{
							if(CBSystemConstants.jh_tai == 1){
								List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
								
								String deviceName = device.getPowerDeviceName();
								pd.setPowerDeviceName(deviceName);
								
								RuleBaseMode rbm = new RuleBaseMode();
								rbm.setPd(pd);
								rbmList.add(rbm);
								
								CheckMessage cm = new CheckMessage();
								pdlist.add(pd);
								cm.setPd(pdlist);
								cm.setBottom("302");
								List<CheckMessage> msgList = new ArrayList<CheckMessage>();
								msgList.add(cm);
								Map<String, List<CheckMessage>> map = new HashMap<String, List<CheckMessage>>();
								map.put("sbxx", msgList);
								
								if(CBSystemConstants.lcm==null){
									CBSystemConstants.lcm = new ArrayList<CheckMessage>();
								}
								CBSystemConstants.lcm.add(cm);
							}
						}
					}
				}
			}
		}
		
		return rbmList;
	}
	
	/*
	 * 过滤一些关键字，不需要校核
	 */
	public static boolean filterList(String word){
		boolean result = false;
		
		String[] filterList = {"通知","核","委托","许可","备注","停用","启用","压变",
				"保护","确认","作业","潮流","送电","控制","出力","核实","落实","代供"};
		
		for(String filter : filterList){
			if(word.contains(filter)){
				result = true;
				return result;
			}
		}
		
		return result;
	}
}
