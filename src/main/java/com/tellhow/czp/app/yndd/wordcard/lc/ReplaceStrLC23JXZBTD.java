package com.tellhow.czp.app.yndd.wordcard.lc;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLC;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLC23JXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("临沧二分之三接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			
			for(PowerDevice dev : zbgyckgList){
				if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					Collections.reverse(zbgyckgList);
				}
				break;
			}
			
			for(PowerDevice dev : zbdyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
		
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "临沧地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "临沧地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "临沧地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						replaceStr += CommonFunctionLC.getKnifeOffContent(dzList,stationName);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						if(CommonFunctionLC.ifSwitchSeparateControlLC(dev)){
							replaceStr += "临沧地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}
						
						replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(zbzyckgList, stationName);
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
						zbkgList.add(dev);
						
						if(CommonFunctionLC.ifSwitchSeparateControlLC(dev)){
							replaceStr += "临沧地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}
						
						replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(zbkgList, stationName);
					}
				}
			}
		}
		
		return replaceStr;
	}

}
