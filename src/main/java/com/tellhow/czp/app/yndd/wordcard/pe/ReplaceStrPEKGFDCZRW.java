package com.tellhow.czp.app.yndd.wordcard.pe;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEKGFDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱开关复电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String kgName = CZPService.getService().getDevName(curDev);
			
			String beginstatus = RuleExeUtil.getDeviceBeginStatus(curDev);
			String endstatus = RuleExeUtil.getDeviceEndStatus(curDev);

			beginstatus = RuleExeUtil.getStatus(beginstatus);
			endstatus = RuleExeUtil.getStatus(endstatus);

			replaceStr = voltStationName+kgName+"由"+beginstatus+"转"+endstatus;
		}
		
		return replaceStr;
	}

}
