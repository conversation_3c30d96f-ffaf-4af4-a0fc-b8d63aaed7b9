package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPESMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱双母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC + "," + CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			List<PowerDevice> plkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL +","+ CBSystemConstants.RunTypeSwitchMLPL, "", false, true, true, true);

			PowerDevice othermx = new PowerDevice();
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.isSwitchDoubleML(dev)){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					for(PowerDevice mx : mxList){
						if(!mx.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
							othermx = mx ;
							break;
						}
					}
				}
			}
			
			String othermxName = CZPService.getService().getDevName(othermx);
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2") && curDev.getPowerVoltGrade() == 220){
				replaceStr += stationName+"@核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
			}
			
			if(TicketKindChoose.flag.equals("全部手动")){
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						replaceStr += stationName+"@将"+deviceName+"经"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
						replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					}
				}
			}else if(TicketKindChoose.flag.equals("全部程序化")){
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						replaceStr += "普洱地调@执行"+stationName+deviceName+"经"+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
					}
				}
				
				List<PowerDevice> ptdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : ptdzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT) && RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						if(dev.getPowerDeviceName().contains("901") || dev.getPowerDeviceName().contains("902")){
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev, stationName);
						}
					}
				}
				
				for(PowerDevice dev : mlkgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dz, stationName);
						}
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}else if(TicketKindChoose.flag.equals("部分程序化")){
				List<PowerDevice> ptdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : ptdzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT) && RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						if(dev.getPowerDeviceName().contains("901") || dev.getPowerDeviceName().contains("902")){
							replaceStr += "普洱地调@遥控合上"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev, stationName);
						}
					}
				}
				
				for(PowerDevice dev : mlkgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					dzList = RuleExeUtil.sortByCZMXC(dzList);
					Collections.reverse(dzList);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
							replaceStr += "普洱地调@遥控合上"+stationName+CommonFunctionPE.getSequentialDeviceName(dz)+"/r/n";
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dz, stationName);
						}
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			
			List<PowerDevice> yxList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbyList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : plkgList){
				if(dev.getDeviceStatus().equals("0")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							yxList.add(dev);
							break;
						}
					}
				}else if(dev.getDeviceStatus().equals("1")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							rbyList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				if(dev.getDeviceStatus().equals("0")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							yxList.add(dev);
							break;
						}
					}
				}else if(dev.getDeviceStatus().equals("1")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							rbyList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : xlkgList){
				if(dev.getDeviceStatus().equals("0")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							yxList.add(dev);
							break;
						}
					}
				}else if(dev.getDeviceStatus().equals("1")){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.isDeviceChanged(dz)){
							rbyList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
