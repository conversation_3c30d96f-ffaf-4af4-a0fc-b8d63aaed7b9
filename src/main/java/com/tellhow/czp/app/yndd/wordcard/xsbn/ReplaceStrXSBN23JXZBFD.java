package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBN23JXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳二分之三接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);

			replaceStr += CommonFunctionQJ.getPowerOnCheckContent();

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					Collections.reverse(zbgyckgList);
				}
				break;
			}
			
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				for(PowerDevice dev : zbgyckgList){
					List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
					zbkgList.add(dev);
					
					if(CommonFunctionBN.ifSwitchSeparateControlBN(dev)){
						replaceStr += "版纳地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
					
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbkgList, stationName);
				}
				
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> zycdzList = CommonFunctionBN.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunctionBN.getKnifeOnContent(zycdzList,stationName);
					
					if(CommonFunctionBN.ifSwitchSeparateControlBN(dev)){
						replaceStr += "版纳地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						replaceStr += CommonFunctionBN.getKnifeOnCheckContent(dzList, stationName);
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					List<PowerDevice> dycdzList = CommonFunctionBN.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunctionBN.getKnifeOnContent(dycdzList,stationName);
					
					if(CommonFunctionBN.ifSwitchSeparateControlBN(dev)){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						replaceStr += CommonFunctionBN.getKnifeOnContent(dzList,stationName);
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : dycmxList){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已处热备用/r/n";
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"电压互感器已处运行/r/n";
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					break;
				}
			}
			
			Collections.reverse(zbgyckgList);
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
					break;
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					String dycmxName = "";
					
					for(PowerDevice dycmx : dycmxList){
						dycmxName = CZPService.getService().getDevName(dycmx);
					}
					
					replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+dycmxName+"充电/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
