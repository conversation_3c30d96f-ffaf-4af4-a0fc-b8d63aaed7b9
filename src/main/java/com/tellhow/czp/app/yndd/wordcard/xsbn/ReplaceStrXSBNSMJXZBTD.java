package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNSMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳双母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);
			String beginStatus = RuleExeUtil.getDeviceBeginStatus(curDev);
			String endStatus = RuleExeUtil.getDeviceEndStatus(curDev);
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getTransformerKnifeLoad(curDev);

			double midvolt = RuleExeUtil.getTransformerVolByType(curDev, "middle");
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();

			List<PowerDevice> otherZbList = new ArrayList<PowerDevice>();
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdzList){
				if(dev.getPowerVoltGrade() == lowvolt){
					zbdycdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}		
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
						if (dev.getDeviceStatus().equals("0")) {
							otherZbList.add(dev);
						}
					}
				}
			}
			
			replaceStr += CommonFunctionBN.getMotherLineTdContent(dycmxList, stationName);

			String isControl = CommonFunctionBN.ifDeviceControlBN(curDev);
			
			if(isControl.equals("全部可控")){
                if (beginStatus.equals("0") && endStatus.equals("2")) {
                    replaceStr += "版纳地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";

                    replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(zbdyckgList,stationName);

                    for(PowerDevice dev : zbdycdzList){
                        replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(dev,stationName);
                    }

                    replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(zbzyckgList,stationName);
                    replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(zbgyckgList,stationName);
                } else if (endStatus.equals("1")) {
					replaceStr += "版纳地调@执行"+stationName+deviceName+"由运行转热备用程序操作/r/n";
                } else if (endStatus.equals("2")) {
					replaceStr += "版纳地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";

					replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(zbdyckgList,stationName);

					for(PowerDevice dev : zbdycdzList){
						replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(dev,stationName);
					}

					replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(zbzyckgList,stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(zbgyckgList,stationName);
				}
			}else{
				for(PowerDevice dev : zxdjddzList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合位/r/n";
					}
				}
				
				for(PowerDevice dev : otherzxdjddzList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合位/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += "版纳地调@确认"+stationName+CZPService.getService().getDevName(dev)+"处合闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += CommonFunctionBN.getSwitchOffContent(dev, stationName, station);
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += "版纳地调@确认"+stationName+CZPService.getService().getDevName(dev)+"处合闸位置/r/n";
					} else if (RuleExeUtil.getDeviceBeginStatus(dev).equals("1")) {
						replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += CommonFunctionBN.getSwitchOffContent(dev, stationName, station);
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += CommonFunctionBN.getSwitchOffContent(dev, stationName, station);
					}
				}

                for (PowerDevice other : otherZbList) {
                    replaceStr += stationName+"@将"+CZPService.getService().getDevName(other)+"保护定值区由XX区调整至XX区/r/n";
					break;
                }

                if(curDev.getDeviceStatus().equals("2")){
					if(isControl.equals("部分可控")){
						for(PowerDevice dev : zbzyckgList){
							if(CommonFunctionBN.ifSwitchSeparateControlBN(dev)){
								replaceStr += "版纳地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(zbzyckgList,stationName);
							}
						}
						
						for(PowerDevice dev : zbgyckgList){
							if(CommonFunctionBN.ifSwitchSeparateControlBN(dev)){
								replaceStr += "版纳地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								replaceStr += CommonFunctionBN.getSequenceConfirmTdContent(zbgyckgList,stationName);
							}
						}
						
						replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
					}else{
						replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
					}
				}
				
				for(PowerDevice dev : zxdjddzList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "版纳地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					replaceStr += CommonFunctionBN.getTcSwitchProtect(dev, stationName);
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@确认"+(int)dev.getPowerVoltGrade()+"kV备自投装置处退出状态/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					replaceStr += CommonFunctionBN.getTcSwitchProtect(dev, stationName);
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@确认"+(int)dev.getPowerVoltGrade()+"kV备自投装置处退出状态/r/n";
				}
			}
		}
		
		return replaceStr;
	}
}
