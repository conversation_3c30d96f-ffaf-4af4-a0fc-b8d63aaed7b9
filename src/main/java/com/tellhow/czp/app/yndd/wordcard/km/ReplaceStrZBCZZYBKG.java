package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.HashMap;
import java.util.Iterator;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBCZZYBKG implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("主变操作站用变开关".equals(tempStr)){
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchQT)&&(dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变"))){
						if((RuleExeUtil.getDeviceEndStatus(dev).equals("1")||RuleExeUtil.getDeviceEndStatus(dev).equals("2"))&&RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr = CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev);
							break;
						}else if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")&&(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")||RuleExeUtil.getDeviceBeginStatus(dev).equals("2"))){
							replaceStr = CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev);
							break;
						}else if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")&&RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							replaceStr = CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev);
							break;
						}
					}
				}
			}
	    }
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
