package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.km.TransformTDKindChoose;
import com.tellhow.czp.app.yndd.rule.km.JudgeLoopClosing;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKDNQZZBFDSY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("扩大内桥中主变复电术语".equals(tempStr)){
			String devName =  CZPService.getService().getDevName(curDev);
			String stationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(curDev.getPowerStationID()));
			
			List<PowerDevice> gyckgList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true,true);
			List<PowerDevice> gycmlkgList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true,true);

			List<PowerDevice> othergyckgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> dyckgList = RuleExeUtil.getDeviceList(curDev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true,true);
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			if(dyckgList.size()>0){
				dycmlkgList = RuleExeUtil.getDeviceList(dyckgList.get(0),SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true);
			}			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&!gyckgList.contains(dev)){
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						othergyckgList.add(dev);
					}
				}
			}
			
			if(TransformTDKindChoose.tdflag.equals("母线一起停电")){
				for(PowerDevice gycmlkg : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("2")){
						replaceStr += CZPService.getService().getDevName(gycmlkg)+"由冷备用转为热备用/r/n";
					}
				}
				
				replaceStr += "将"+devName+"由冷备用转热备用/r/n";
				replaceStr += "落实"+CZPService.getService().getDevName(curDev)+"中性点1010接地开关已处合位/r/n";

				for(PowerDevice gycmlkg : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(gycmlkg).equals("0")){
						replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
					}
				}
				
				replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dyckgList)+"/r/n";

				for(PowerDevice dycmlkg : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("0")){
						replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
						replaceStr += "投入"+CZPService.getService().getDevName(dycmlkg)+"备自投装置/r/n";
					}
				}
				
				replaceStr += "投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
			}else{//短时停电
				boolean ismlkgrby = false;
				
				for(PowerDevice gycmlkg : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gycmlkg).equals("1")){
						ismlkgrby = true;
						break;
					}
				}
				
				if(ismlkgrby){
					for(PowerDevice gycmlkg : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("0")){
							replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
						}
					}
					
					replaceStr += "将"+devName+"由冷备用转热备用/r/n";
					replaceStr += "落实"+CZPService.getService().getDevName(curDev)+"中性点1010接地开关已处合位/r/n";

					for(PowerDevice gycmlkg : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(gycmlkg).equals("0")){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
						}
					}
					
					replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dyckgList)+"/r/n";

					for(PowerDevice dycmlkg : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("0")){
							replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
							replaceStr += "投入"+CZPService.getService().getDevName(dycmlkg)+"备自投装置";
						}
					}
				}else{
					if(JudgeLoopClosing.flag.equals("能合环")){//能合环
						replaceStr += "云南省调@落实220kVXXX变220kV母线与220kVXXX变220kV母线为同期系统/r/n";
						
						for(PowerDevice gyckg : othergyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(gyckg).equals("1")){
								replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gyckg)+"/r/n";
							}
						}
						
						for(PowerDevice gycmlkg : gycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("0")){
								replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
							}
						}
						
						replaceStr += "将"+devName+"由冷备用转热备用/r/n";
						replaceStr += "落实"+CZPService.getService().getDevName(curDev)+"中性点1010接地开关已处合位/r/n";

						for(PowerDevice gycmlkg : gycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(gycmlkg).equals("0")){
								replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
							}
						}
						
						replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dyckgList)+"/r/n";

						for(PowerDevice dycmlkg : dycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("0")){
								replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
								replaceStr += "投入"+CZPService.getService().getDevName(dycmlkg)+"备自投装置";
							}
						}
					}else{//不能合环
						List<PowerDevice> alldycmlkgList = RuleExeUtil.getDeviceList(dyckgList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true,true);
						
						for(PowerDevice alldycmlkg : alldycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(alldycmlkg).equals("1")){
								replaceStr += "退出"+CZPService.getService().getDevName(alldycmlkg)+"备自投装置/r/n";
								break;
							}
						}
						
						PowerDevice hsdycmlkg = new PowerDevice();
						
						for(PowerDevice dev : dycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
								hsdycmlkg = dev;
								break;
							}
						}
						
						for(PowerDevice dev : gycmlkgList){
							String num = CZPService.getService().getDevNum(hsdycmlkg).replace("0", "1");
							
							if(!dev.getPowerDeviceName().contains(num)){
								replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
						
						for(PowerDevice gyckg : othergyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(gyckg).equals("1")){
								replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gyckg)+"/r/n";
							}
						}
						
						for(PowerDevice dev : gycmlkgList){
							String num = CZPService.getService().getDevNum(hsdycmlkg).replace("0", "1");
							
							if(dev.getPowerDeviceName().contains(num)){
								replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
						
						replaceStr += "将"+devName+"由冷备用转热备用/r/n";
						replaceStr += "落实"+CZPService.getService().getDevName(curDev)+"中性点1010接地开关已处合位/r/n";

						for(PowerDevice gycmlkg : gycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(gycmlkg).equals("0")){
								replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
							}
						}
						
						replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dyckgList)+"/r/n";

						for(PowerDevice dycmlkg : dycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("0")){
								replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
								replaceStr += "投入"+CZPService.getService().getDevName(dycmlkg)+"备自投装置/r/n";
							}
						}
						
						for(PowerDevice alldycmlkg : alldycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(alldycmlkg).equals("1")){
								replaceStr += "投入"+CZPService.getService().getDevName(alldycmlkg)+"备自投装置/r/n";
								break;
							}
						}
					}
				}
			}
		}
		return replaceStr;
	}

}
