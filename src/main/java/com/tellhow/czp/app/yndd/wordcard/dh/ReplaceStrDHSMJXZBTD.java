package com.tellhow.czp.app.yndd.wordcard.dh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.xsbn.XSBNTicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDHSMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("德宏双母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getTransformerKnifeLoad(curDev);

			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			double midvolt = RuleExeUtil.getTransformerVolByType(curDev, "middle");
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdzList){
				if(dev.getPowerVoltGrade() == lowvolt){
					zbdycdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}		
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}
			}
			
			boolean isSwitchControl = true;
			
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionDH.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionDH.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				for(PowerDevice dev : zxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控合上"+stationName+devName+"/r/n";
					}
				}
				
				for(PowerDevice dev : otherzxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控合上"+stationName+devName+"/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += "德宏地调@确认"+stationName+CZPService.getService().getDevName(dev)+"在合闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionDH.getHhContent(dev, "德宏地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += "德宏地调@确认"+stationName+CZPService.getService().getDevName(dev)+"在合闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(curDev.getDeviceStatus().equals("2")){
					replaceStr += "德宏地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
				}
				
				for(PowerDevice dev : zxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控拉开"+stationName+devName+"/r/n";
					}
				}
			}else{
				for(PowerDevice dev : zxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控合上"+stationName+devName+"/r/n";
					}
				}
				
				for(PowerDevice dev : otherzxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控合上"+stationName+devName+"/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += "德宏地调@确认"+stationName+CZPService.getService().getDevName(dev)+"在合闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionDH.getHhContent(dev, "德宏地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += "德宏地调@确认"+stationName+CZPService.getService().getDevName(dev)+"在合闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(curDev.getDeviceStatus().equals("2")){
					for(PowerDevice dev : zbzyckgList){
						if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
							replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(CommonFunctionDH.ifSwitchSeparateControl(dev)){
							replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
						}
					}
					
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
				
				for(PowerDevice dev : zxdjddzList){
					String devName = CZPService.getService().getDevName(dev);
					
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "德宏地调@遥控拉开"+stationName+devName+"/r/n";
					}
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
