package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSZBFDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山主变复电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(curDev);
			List<PowerDevice> zbgycmxList = new ArrayList<PowerDevice>();

			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgycdzList){
				zbgycmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				gycxlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true);
			}

			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				if(zbgycmxList.size() > 0){
					for(PowerDevice dev : zbgycmxList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							List<PowerDevice> yxList = new ArrayList<PowerDevice>();
							List<PowerDevice> rbyList = new ArrayList<PowerDevice>();
							
							for(PowerDevice gycxlkg : gycxlkgList){
								if(RuleExeUtil.getDeviceEndStatus(gycxlkg).equals("0")){
									yxList.add(gycxlkg);
								}else if(RuleExeUtil.getDeviceEndStatus(gycxlkg).equals("1")){
									rbyList.add(gycxlkg);
								}
							}
							
							for(PowerDevice gycmlkg : gycmlkgList){
								if(RuleExeUtil.getDeviceEndStatus(gycmlkg).equals("0")){
									yxList.add(gycmlkg);
								}else if(RuleExeUtil.getDeviceEndStatus(gycmlkg).equals("1")){
									rbyList.add(gycmlkg);
								}
							}
							
							for(PowerDevice yxkg : yxList){
								if(RuleExeUtil.getDeviceBeginStatus(yxkg).equals("2")){
									replaceStr += voltStationName+CZPService.getService().getDevName(yxkg)+"及"+deviceName+"由冷备用转运行";
								}
							}
							
							for(PowerDevice rbykg : rbyList){
								if(RuleExeUtil.getDeviceBeginStatus(rbykg).equals("2")){
									replaceStr += "，"+CZPService.getService().getDevName(rbykg)+"由冷备用转热备用";
								}
							}
							
							replaceStr += "";
						}else{
							replaceStr += voltStationName+deviceName+"由冷备用转运行";
						}
					}
				}else{
					replaceStr += voltStationName+deviceName+"由冷备用转运行";
				}
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				replaceStr += voltStationName+deviceName+"由热备用转运行";
			}
		}
		
		return replaceStr;
	}

}
