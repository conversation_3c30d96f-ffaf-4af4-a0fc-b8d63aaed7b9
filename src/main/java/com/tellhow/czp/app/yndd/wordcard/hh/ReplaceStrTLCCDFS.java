package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTLCCDFS  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("投两侧差动方式".equals(tempStr)){
			String sql = "SELECT ID FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE  ISREMOVE = '0' AND  LINE_ID IN "
							+ "(  SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+stationDev.getPowerDeviceID()+"')";
			List<Map<String, Object>> stations = DBManager.queryForList(sql);
			
			List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(stationDev);
			
			if(lineList.size()+stations.size()>2){
				List<PowerDevice> xlkgList =  RuleExeUtil.getLinkedSwitch(stationDev);
				
				String num = "";
				
				if(xlkgList.size()>0){
					num = CZPService.getService().getDevNum(xlkgList.get(0));
				}
				
				replaceStr += "退出"+CZPService.getService().getDevName(stationDev)+num+"线路“投两侧差动方式”";
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
