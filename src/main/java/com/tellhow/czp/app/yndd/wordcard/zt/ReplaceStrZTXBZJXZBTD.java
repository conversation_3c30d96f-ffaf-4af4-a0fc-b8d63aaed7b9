package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.zt.SelectTicketKind;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTXBZJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通线变组接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			if(SelectTicketKind.ticketKind.equals("综合票")){
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += stationName+"@将"+deviceName+"由运行转冷备用/r/n";
					}else{
						replaceStr += stationName+"@将"+deviceName+"由运行转热备用/r/n";
					}
				}else{
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
					}
				}
			}else{
				List<PowerDevice> zbzxdjddzList =  new ArrayList<PowerDevice>();
				List<PowerDevice> otherzbzxdjddzList =  new ArrayList<PowerDevice>();

				List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
				List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
				List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
				
				List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
				List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
				List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
				
				List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
				List<PowerDevice> wqxlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> zbList =  new ArrayList<PowerDevice>();
				
				for(PowerDevice dev : zbdyckgList){
					dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice mx : dycmxList){
						dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice mx : mxList){
						zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice mx : mxList){
						gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
				}
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						
						if(!dev.getPowerDeviceName().contains("接地变")){
							zbList.add(dev);
						}
						
						if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
							List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
							RuleExeUtil.swapLowDeviceList(gdList);
							for(PowerDevice gd : gdList) {
								otherzbzxdjddzList.add(gd);
							}
						}
					}
				}
				
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				
				for(PowerDevice gd : gdList) {
					zbzxdjddzList.add(gd);
				}
				
				List<PowerDevice> tempList = new ArrayList<PowerDevice>();
				
				tempList.addAll(zbzxdjddzList);
				tempList.addAll(otherzbzxdjddzList);
				
				RuleExeUtil.swapDeviceList(tempList);
				
				Collections.reverse(tempList);
				
				
				for(PowerDevice dev : dycmxList){
					if(dev.getPowerVoltGrade() == 10){
						replaceStr += "昭通配调@落实所辖电网运行方式已安排好，"+deviceName+"具备停电条件/r/n";
					}
				}

				double highvolt = RuleExeUtil.getTransformerVolByType(curDev, "high");
				double midvolt = RuleExeUtil.getTransformerVolByType(curDev, "middle");
				double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");

				List<String> voltList = new ArrayList<String>();
				
				if(station.getPowerVoltGrade() == 220){
					voltList.add(String.valueOf((int)lowvolt)+"kV备自投装置");
					voltList.add(String.valueOf((int)midvolt)+"kV备自投装置");
				}else{
					voltList.add(String.valueOf((int)lowvolt)+"kV备自投装置");
					
					if(midvolt > lowvolt){
						voltList.add(String.valueOf((int)midvolt)+"kV备自投装置");
					}
					
					voltList.add(String.valueOf((int)highvolt)+"kV备自投装置");
				}

				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
					}
				}
				
				if(station.getPowerVoltGrade() > 35){
					List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
					EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList, "请选择需要投入中性点的主变：");
					chooseEquips=ecc.getChooseEquip();
					
					if(chooseEquips.size()>0){
						for(PowerDevice dev : chooseEquips){
							gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						}
						
						RuleExeUtil.swapLowDeviceList(gdList);
						
						for(PowerDevice dev : gdList){
							replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
					
					wqxlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice xlkg : wqxlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkg)+"/r/n";
						}
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				
				RuleExeUtil.swapDeviceList(zbzxdjddzList);
				
				for(PowerDevice dev : zbzxdjddzList){
					replaceStr += "昭通地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					replaceStr += "将"+deviceName+"由热备用转冷备用/r/n";
				}
				
				for(PowerDevice dev : wqxlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						replaceStr += "将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					}
				}
				
				replaceStr += CommonFunction.getBztResult(voltList , "退出");
			}
		}
			
		return replaceStr;
	}

}
