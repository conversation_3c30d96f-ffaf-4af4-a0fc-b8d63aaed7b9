package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.List;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHJDBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河接地变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String devName = curDev.getPowerDeviceName();
			List<PowerDevice> motherLineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbdyckgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : motherLineList){
				dycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				zbdyckgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			}
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX, "", true, true, true, true);
			
			String devkind = "";
			String jdbnum = "";
			
			if(devName.contains("")){
				if(devName.contains("接地变")){
					String jdbName = devName.substring(0, devName.indexOf("接地变"));
					jdbnum = CZPService.getService().getDevNum(jdbName);
					devkind = "接地变";
				}else if(devName.contains("接地站用变")){
					String jdbName = devName.substring(0, devName.indexOf("接地站用变"));
					jdbnum = CZPService.getService().getDevNum(jdbName);
					devkind = "接地站用变";
				}else if(devName.contains("小电阻")){
					String jdbName = devName.substring(0, devName.indexOf("小电阻"));
					jdbnum = CZPService.getService().getDevNum(jdbName);
					devkind = "小电阻";
				}else{
					devkind = "接地变";
				}
			}
			
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();
			tempList.addAll(zbdyckgList);
			tempList.addAll(dycmlkgList);
			
			String[] arr = {"标准","非标准"};
			
			int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择当前接地变断路器是否标准接线", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
			if(sel==0||sel==-1){
				replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(curDev).replace("及", "")+"/r/n";
				replaceStr += "核实"+CZPService.getService().getDevName(curDev)+"热备用/r/n";
				replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
				replaceStr += "退出10kV"+jdbnum+"接地变保护动作跳"+CZPService.getService().getDevName(tempList)+"/r/n";
			}else if(sel==1){
				replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(curDev).replace("及", "")+"/r/n";
				replaceStr += "核实"+CZPService.getService().getDevName(curDev).replace("及", "")+"在分闸位置/r/n";
				replaceStr += "核实"+CZPService.getService().getDevName(dzList)+"在合闸位置/r/n";
				replaceStr += "核实10kV"+jdbnum+devkind+"热备用/r/n";
				replaceStr += "拉开"+CZPService.getService().getDevName(dzList)+"/r/n";
				replaceStr += "将10kV"+jdbnum+devkind+"由热备用转冷备用/r/n";
				replaceStr += "退出10kV"+jdbnum+devkind+"保护动作跳"+CZPService.getService().getDevName(tempList)+"/r/n";
			}
		}
		return replaceStr;
	}

}