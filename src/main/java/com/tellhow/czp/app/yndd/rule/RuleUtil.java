package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.LoadOff;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.CreatePowerStationToplogy;

public class RuleUtil {
    private static CommonSearch cs=new CommonSearch();
    private static Map inPara=new HashMap();
    private static Map outPara=new HashMap();
	/**
	 * 设备状态转换
	 * */
	public static boolean deviceStatusSet(PowerDevice dev, String startStatus,String endStatus) {
		if(dev.getDeviceStatus().equals(endStatus)){
			return true;
		}else{
			DispatchTransDevice dtd = new DispatchTransDevice();
			dev.setDeviceStatus(endStatus);
			dtd.setTransDevice(dev);
			dtd.setParentDevice(CBSystemConstants.getParentDev());
			dtd.setBeginstatus(startStatus);
			dtd.setEndstate(endStatus);
			dtd.setFlag("0");
			CBSystemConstants.putDtdMap(dtd);
			
			return true;
		}
	}

	/**
	 * 设备状态转换
	 * */
	public static boolean deviceStatusChange(PowerDevice dev, String startStatus, String endStatus) {
		RuleExecute ruleExecute = new RuleExecute();
		RuleBaseMode rbmode = new RuleBaseMode();
		rbmode.setPd(dev);
		rbmode.setBeginStatus(startStatus);
		rbmode.setEndState(endStatus);
		return ruleExecute.execute(rbmode);
	}

	/**
	 * 设备状态转换
	 * */
	public static boolean deviceStatusExecute(PowerDevice dev,
			String startStatus, String endStatus) {
		CBSystemConstants.isLock = false;
		RuleExecute ruleExecute = new RuleExecute();
		RuleBaseMode rbmode = new RuleBaseMode();
		rbmode.setPd(dev);
		rbmode.setBeginStatus(startStatus);
		rbmode.setEndState(endStatus);
		boolean result = ruleExecute.execute(rbmode);
		CBSystemConstants.isLock = true;
		return result;
	}

	/**
	 * 根据母线获得连接在母线上的线路
	 * 
	 * */
	public static List<PowerDevice> getLoad(PowerDevice motherLine) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", motherLine);
		inPara.put("tagDevType", SystemConstants.InOutLine);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		cs.execute(inPara, outPara);
		return (ArrayList<PowerDevice>) outPara.get("linkedDeviceList");
	}

	/**
	 * 根据母线获得连接在母线上的运行所有负荷，包括电容器、站用变等等
	 * 
	 * */
	public static List<PowerDevice> getAllLoad(PowerDevice motherLine) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", motherLine);
		inPara.put("isSearchOffPath", "false");
		inPara.put("excDevType", SystemConstants.PowerTransformer + ","
				+ SystemConstants.MotherLine + "," + SystemConstants.Ascoil
				+ "," + SystemConstants.Arrester);
		cs.execute(inPara, outPara);
		List<PowerDevice> rs = (ArrayList<PowerDevice>) outPara
				.get("linkedDeviceList");
		String type;
		for (Iterator itr = rs.iterator(); itr.hasNext();) {
			PowerDevice pd = (PowerDevice) itr.next();
			type = pd.getDeviceType();
			if (type.equals(SystemConstants.Switch)
					|| type.equals(SystemConstants.SwitchSeparate)
					|| type.equals(SystemConstants.SwitchFlowGroundLine)) {
				itr.remove();
				continue;
			}
			if(motherLine.getPowerVoltGrade() < pd.getPowerVoltGrade()) {
				itr.remove();
				continue;
			}
			if(!pd.getDeviceStatus().equals("0")) {
				itr.remove();
				continue;
			}
		}
		return rs;
	}

	/**
	 * 不需要转供的时候，处理主变负荷反供的情况
	 * 
	 * @param dev
	 *            停电主变
	 * @param lines
	 *            停电母线集合
	 * */
	public static boolean fangong(PowerDevice dev, List<Double> vols) {
		PowerDevice device = CBSystemConstants.getParentDev();
		List<PowerDevice> loadMap = new ArrayList<PowerDevice>();
		if (judgeTransform(dev, vols)) {
			int rs = JOptionPane.showConfirmDialog(null,
					dev.getPowerDeviceName() + "所带负荷是否由中压侧反供？");
			if (rs == JOptionPane.OK_OPTION) {
				// 获取反供线路并显示给用户
				ArrayList<PowerDevice> ls = getMotherLine(dev);
				//20131023
				//Double vol1 = (Double) ls.keySet().toArray()[0];
				//Double vol2 = (Double) ls.keySet().toArray()[1];
				//Double max = Math.max(vol1, vol2);
				EquipCheckChoose ecc = new EquipCheckChoose(
						SystemConstants.getMainFrame(), true,
						getLinkedDeviceByType(ls.get(0),
								SystemConstants.InOutLine), "请选择反供的线路");
				PowerDevice pd;

				for (int i = 0; i < ecc.getChooseEquip().size(); i++) {
					pd = ecc.getChooseEquip().get(i);
					if (!pd.getDeviceStatus().equals("0")) {
						if (!RuleUtil.deviceStatusChange(pd,
								pd.getDeviceStatus(), "0")) {
							return false;
						}

					}
				}
				// 根据停电序位表按优先级列出要倒的负荷 根据主变和负荷的容量判断是否会过载?
				for(PowerDevice ml : ls) {
					loadMap.addAll(getLoad(ml));
				}
				ecc = new EquipCheckChoose(SystemConstants.getMainFrame(),
						true, loadMap, "请选择转冷备用的负荷");
				return new LoadOff().execute(ecc.getChooseEquip());
			}
		}

		List<PowerDevice> lines = RuleUtil.getMotherLineList(dev);
		// 获取停电侧所有负荷并调用负荷停电算法 ps:是否要考虑母线停负荷的操作？
		for (PowerDevice pd : lines) {
			loadMap.addAll(getAllLoad(pd));
		}
		boolean c1 = new LoadOff().execute(loadMap);
		for (PowerDevice pd : lines) {
			RuleUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "2");
		}

		if (!device.getPowerDeviceID().equals(dev.getPowerDeviceID())) {
			if (device.getDeviceType().equals(SystemConstants.InOutLine)) {
				if (RuleUtil.getPowerDeviceByType(device.getPowerStationID(),
						SystemConstants.InOutLine, device.getPowerVoltGrade())
						.size() == 1)
					RuleUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "2");
			} else
				RuleUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
		}
		return c1;

	}

	/**
	 * 判断主变是否符合转供的条件: 1是三卷变 2中低压侧都没有停电
	 * 
	 * @param lines
	 *            停电电压列表
	 * */
	public static boolean judgeTransform(PowerDevice transform,
			List<Double> lines) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", transform);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate);
		inPara.put("isSearchDirectDevice", "true");
		cs.execute(inPara, outPara);
		List<PowerDevice> ls = (ArrayList<PowerDevice>) outPara
				.get("linkedDeviceList");
		if (ls.size() != 3) {
			return false;
		}
		double vol1 = ls.get(0).getPowerVoltGrade();
		double vol2 = ls.get(1).getPowerVoltGrade();
		double vol3 = ls.get(2).getPowerVoltGrade();
		if (vol1 != vol2 && vol1 != vol3 && vol2 != vol3) {
			List<Double> vols = new ArrayList<Double>();
			vols.add(vol1);
			vols.add(vol2);
			vols.add(vol3);
			Collections.sort(vols);
			// 中低压侧不在停电电压列表
			if (!lines.contains(vols.get(0)) && !lines.contains(vols.get(1))) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 根据主变查找与主变电压等级不同的母线
	 * 
	 * @return 返回 电压等级与母线相对应的HashMap
	 * */
	public static ArrayList<PowerDevice> getMotherLine(PowerDevice dev) {
		ArrayList<PowerDevice> lines = new ArrayList<PowerDevice>();
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", dev);
		inPara.put("tagDevType", SystemConstants.MotherLine); // 目标设备母线
		inPara.put("isSearchOffPath", false);
		cs.execute(inPara, outPara);
		List<PowerDevice> ls = (ArrayList<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (int i = 0; i < ls.size(); i++) {
			if (dev.getPowerVoltGrade() != ls.get(i).getPowerVoltGrade()) {
				lines.add(ls.get(i));
			}
		}
		return lines;
	}

	/**
	 * 根据主变查找主变负荷侧连通的母线
	 * 
	 * @return 返回 母线集合的List
	 * */
	public static List<PowerDevice> getMotherLineList(PowerDevice dev) {
		HashMap<Double, PowerDevice> lines = new HashMap<Double, PowerDevice>();
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", dev);
		inPara.put("tagDevType", SystemConstants.MotherLine); // 目标设备母线
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", false);
		cs.execute(inPara, outPara);
		List<PowerDevice> ls = (ArrayList<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator<PowerDevice> itr = ls.iterator(); itr.hasNext();) {
			PowerDevice line = itr.next();
			if (line.getPowerVoltGrade() == dev.getPowerVoltGrade()) {
				itr.remove();
			}
		}
		return ls;

	}
	
	/**
	 * 根据主变查找主变负荷侧连通的母线
	 * 
	 * @return 返回 母线集合的List
	 * */
	public static List<PowerDevice> getMotherLineListAllByVol(PowerDevice dev, List<Double> vols) {
		List<PowerDevice> lines = new ArrayList<PowerDevice>();
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", dev);
		inPara.put("tagDevType", SystemConstants.MotherLine); // 目标设备母线
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> ls = (ArrayList<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (int i = 0; i < ls.size(); i++) {
			if (vols.contains(ls.get(i).getPowerVoltGrade())) { //&& dev.getPowerVoltGrade() != ls.get(i).getPowerVoltGrade()
				lines.add(ls.get(i));
			}
		}
		return lines;

	}
	
	public static List<PowerDevice> getMotherLineListAllVolOff(PowerDevice dev) {
		HashMap<Double, PowerDevice> lines = new HashMap<Double, PowerDevice>();
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", dev);
		inPara.put("tagDevType", SystemConstants.MotherLine); // 目标设备母线
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> ls = (ArrayList<PowerDevice>) outPara
				.get("linkedDeviceList");
		return ls;

	}
	
	/**
	 * 根据主变查找主变负荷侧连通的母线
	 * 
	 * @return 返回 母线集合的List
	 * */
	public static List<PowerDevice> getMotherLineListAll(PowerDevice dev) {
		HashMap<Double, PowerDevice> lines = new HashMap<Double, PowerDevice>();
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", dev);
		inPara.put("tagDevType", SystemConstants.MotherLine); // 目标设备母线
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> ls = (ArrayList<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator<PowerDevice> itr = ls.iterator(); itr.hasNext();) {
			PowerDevice line = itr.next();
			if (line.getPowerVoltGrade() == dev.getPowerVoltGrade()) {
				itr.remove();
			}
		}
		return ls;

	}

	/**
	 * 根据主变以及停电电压类表 ，查找停电母线
	 * 
	 * @return 返回 电压等级与母线相对应的HashMap
	 * */
	public static ArrayList<PowerDevice> getMotherLine(PowerDevice dev,
			List<Double> vols) {
		ArrayList<PowerDevice> lines = new ArrayList<PowerDevice>();
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", dev);
		inPara.put("tagDevType", SystemConstants.MotherLine); // 目标设备母线
		cs.execute(inPara, outPara);
		List<PowerDevice> ls = (ArrayList<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (int i = 0; i < ls.size(); i++) {
			if (vols.contains(ls.get(i).getPowerVoltGrade())
					&& dev.getPowerVoltGrade() != ls.get(i).getPowerVoltGrade()) {
				lines.add(ls.get(i));
			}
		}
		return lines;
	}

	/**
	 * 搜索厂站内固定电压等级的某一设备类型的设备
	 * 
	 * @param stationID
	 *            厂站id
	 * @param type
	 *            设备类型
	 * @param voltGrade
	 *            电压等级
	 * @return 返回设备集合
	 * */
	public static List<PowerDevice> getPowerDevice(String stationID,
			String type, double voltGrade) {
		List<PowerDevice> tfs = new ArrayList<PowerDevice>();
		HashMap<String, PowerDevice> devices = CBSystemConstants
				.getStationPowerDevices(stationID);
		if(devices==null){
			return tfs;
		}
		for (String str : devices.keySet()) {
			if (devices.get(str).getDeviceType().equals(type)
					&& devices.get(str).getPowerVoltGrade() == voltGrade) {
				tfs.add(devices.get(str));
			}
		}
		return tfs;
	}

	/**
	 * 搜索厂站内固定电压等级的某一设备类型并处于非检修状态的设备
	 * 
	 * @param stationID
	 *            厂站id
	 * @param type
	 *            设备类型
	 * @param voltGrade
	 *            电压等级
	 * @return 返回设备集合
	 * */
	public static List<PowerDevice> getPowerDeviceByType(String stationID,
			String type, double voltGrade) {
		List<PowerDevice> tfs = new ArrayList<PowerDevice>();
		HashMap<String, PowerDevice> devices = CBSystemConstants
				.getStationPowerDevices(stationID);
		for (String str : devices.keySet()) {
			if (devices.get(str).getDeviceType().equals(type)
					&& devices.get(str).getPowerVoltGrade() == voltGrade
					&& !devices.get(str).getDeviceStatus().equals("3")) {
				tfs.add(devices.get(str));
			}
		}
		return tfs;
	}

	/**
	 * 搜索厂站内固定电压等级的某一设备类型并处于运行状态的设备
	 * 
	 * @param stationID
	 *            厂站id
	 * @param type
	 *            设备类型
	 * @param voltGrade
	 *            电压等级
	 * @return 返回设备集合
	 * */
	public static List<PowerDevice> getPowerDeviceByTypeRun(String stationID,
			String type, double voltGrade) {
		List<PowerDevice> tfs = new ArrayList<PowerDevice>();
		HashMap<String, PowerDevice> devices = CBSystemConstants
				.getStationPowerDevices(stationID);
		for (String str : devices.keySet()) {
			if (devices.get(str).getDeviceType().equals(type)
					&& devices.get(str).getPowerVoltGrade() == voltGrade
					&& devices.get(str).getDeviceStatus().equals("0")) {
				tfs.add(devices.get(str));
			}
		}
		return tfs;
	}

	/**
	 * 获取两母线之间的母联开关或者母联刀闸
	 * 
	 * @param pdline
	 *            母线一
	 * @param devline
	 *            母线二
	 * @return 母联开关或者母联刀闸
	 * */
	public static List<PowerDevice> getBtwMthline(PowerDevice pdline,
			PowerDevice devline) {
		List<PowerDevice> list = new ArrayList<PowerDevice>();
		ArrayList<ArrayList<PowerDevice>> paths = RuleUtil.getConnectPathByDeviceAll2(pdline,
				devline); // 获取母线之间的通路
		for (ArrayList<PowerDevice> path : paths) {
			for (PowerDevice p : path) {
				if (p.getPowerVoltGrade()==pdline.getPowerVoltGrade() && (p.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)
						|| p.getDeviceRunType().equals(
								CBSystemConstants.RunTypeKnifeML))) {
					list.add(p);
				}
			}
		}
		return list;
	}
	
	/**
	 * 查找与设备相连的某类型设备
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getLinkedDeviceByType1(PowerDevice pd,
			String type) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", false);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara.get("linkedDeviceList");

	}
	
	/**
	 * 查找与设备相连的某类型设备
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getLinkedDeviceByType2(PowerDevice pd,
			String type) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara.get("linkedDeviceList");

	}

	/**
	 * 查找与设备连通的某类型设备
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getConnectDeviceByType(PowerDevice pd,
			String type) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", false);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara.get("linkedDeviceList");

	}
	
	public static List<PowerDevice> getLinkedDeviceByType5(PowerDevice pd,
			String type) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara.get("linkedDeviceList");

	}

	/**
	 * 查找与设备相连的某类型设备
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getLinkedDeviceByType(PowerDevice pd,
			String type) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", false);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara.get("linkedDeviceList");

	}

	/**
	 * 查找与设备相连的某类型设备
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getBayDeviceByType(PowerDevice pd,
			String type) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara.get("linkedDeviceList");

	}

	/**
	 * 查找与设备相连的某个设备
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getConnectPathByDevice(PowerDevice pd,
			PowerDevice tag) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevice", tag);
		// inPara.put("tagDevType", tag.getDeviceType());
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) ((HashMap<PowerDevice, ArrayList<PowerDevice>>) outPara
				.get("pathList")).get(tag);

	}
	
	public static List<PowerDevice> getConnectPathByDevice2(PowerDevice pd,
			PowerDevice tag) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevice", tag);
		// inPara.put("tagDevType", tag.getDeviceType());
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) ((HashMap<PowerDevice, ArrayList<PowerDevice>>) outPara
				.get("pathList")).get(tag);

	}
	
	/**
	 * 查找与设备相连的某个设备
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static ArrayList<ArrayList<PowerDevice>> getConnectPathByDeviceAll(PowerDevice pd,
			PowerDevice tag) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevice", tag);
		// inPara.put("tagDevType", tag.getDeviceType());
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		cs.execute(inPara, outPara);
		return (ArrayList<ArrayList<PowerDevice>>) ((HashMap<PowerDevice,ArrayList<ArrayList<PowerDevice>>>) outPara
				.get("allPathList")).get(tag);

	}
	
	public static ArrayList<ArrayList<PowerDevice>> getConnectPathByDeviceAll2(PowerDevice pd,
			PowerDevice tag) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevice", tag);
		// inPara.put("tagDevType", tag.getDeviceType());
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", false);
		cs.execute(inPara, outPara);
		return (ArrayList<ArrayList<PowerDevice>>) ((HashMap<PowerDevice,ArrayList<ArrayList<PowerDevice>>>) outPara
				.get("allPathList")).get(tag);

	}

	/**
	 * 获取线路上所供的所有负荷线路
	 * 
	 * @param pd
	 *            线路
	 * */
	public static List<PowerDevice> getLineLoad(PowerDevice pd) {
		List<PowerDevice> motherlines = RuleUtil.getConnectDeviceByType(pd,
				SystemConstants.MotherLine);
		// if (motherlines.size() > 1) {
		// JOptionPane.showMessageDialog(null, "倒母中勿操作线路");
		// return null;
		// }
		// List<PowerDevice> tfs =
		// RuleUtil.getConnectDeviceByType(motherlines.get(0),
		// SystemConstants.PowerTransformer);

		List<PowerDevice> tfs = new ArrayList<PowerDevice>();
		for (PowerDevice ml : motherlines) {
			List<PowerDevice> list = RuleUtil.getConnectDeviceByType(ml,
					SystemConstants.PowerTransformer);
			for (PowerDevice tf : list) {
				if (!tfs.contains(tf))
					tfs.add(tf);
			}
		}

		motherlines.clear();
		for (PowerDevice tf : tfs) {
			List<PowerDevice> mlList = RuleUtil.getMotherLineList(tf);
			for (PowerDevice ml : mlList) {
				if (!motherlines.contains(ml))
					motherlines.add(ml);
			}
		}
		List<PowerDevice> lines = new ArrayList<PowerDevice>();
		for (PowerDevice mline : motherlines) {
			List<PowerDevice> loads = getLoad(mline);
			for (PowerDevice load : loads) {
				if (!lines.contains(load))
					lines.add(load);
			}
		}
		return lines;
	}
	
	/**
	 * 获取主变上所供的所有负荷线路
	 * 
	 * @param pd
	 *            线路
	 * */
	public static List<PowerDevice> getTransformerLoad(PowerDevice pd) {
		List<PowerDevice> motherlines = new ArrayList<PowerDevice>();
		
		List<PowerDevice> mlList = RuleUtil.getMotherLineList(pd);
		for (PowerDevice ml : mlList) {
			if (!motherlines.contains(ml))
				motherlines.add(ml);
		}
		List<PowerDevice> lines = new ArrayList<PowerDevice>();
		for (PowerDevice mline : motherlines) {
			List<PowerDevice> loads = getLoad(mline);
			for (PowerDevice load : loads) {
				if (!lines.contains(load))
					lines.add(load);
			}
		}
		return lines;
	}

	/**
	 * 获取直接相连的设备
	 * 
	 * @param pd
	 *        源设备
	 * @return
	 * */
	public static List<PowerDevice> getDirectDevice(PowerDevice pd) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");
	}

	/**
	 * 获取直接相连的设备
	 * 
	 * @param pd
	 *            源设备
	 * @param targetType
	 *            目标设备类型
	 * @return
	 * */
	public static List<PowerDevice> getDirectDevice(PowerDevice pd, String type) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("isSearchDirectDevice", true);
		inPara.put("isSearchOffPath", true);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");

	}
	
	/**
	 * 获取直接相连的设备
	 * 
	 * @param pd
	 *            源设备
	 * @param targetType
	 *            目标设备类型
	 * @return
	 * */
	public static List<PowerDevice> getDirectDevice(CommonSearch cs,
			Map<String, Object> inPara, Map<String, Object> outPara,
			PowerDevice pd, String targetType) {
		inPara.clear();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", targetType);
		inPara.put("isSearchDirectDevice", true);
		inPara.put("isSearchOffPath", true);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");

	}

	/**
	 * 判断两设备间是否有目标类型的设备
	 * 
	 * @param cs
	 *            inPara outPara 搜索算法所需参数
	 * 
	 * @param pd
	 *            knife 两个源设备
	 * 
	 * @param targetType
	 *            目标设备类型
	 * */
	public static PowerDevice hasItBetweenUs(CommonSearch cs,
			Map<String, Object> inPara, Map<String, Object> outPara,
			PowerDevice pd, PowerDevice knife, String targetType) {
		List<PowerDevice> pdtemp = getDirectDevice(cs, inPara, outPara, pd,
				targetType);
		List<PowerDevice> knifetemp = getDirectDevice(cs, inPara, outPara,
				knife, targetType);
		if (pdtemp.size() == 0 || knifetemp.size() == 0) {
			return null;
		}
		for (PowerDevice dev : pdtemp) {
			if (knifetemp.contains(dev)) {
				return dev;
			}
		}
		return null;
	}

	/**
	 * 判断两设备是否直接相连
	 * */
	public static boolean isConnected(PowerDevice pd1, PowerDevice pd2,
			CommonSearch cs, Map<String, Object> inPara,
			Map<String, Object> outPara) {
		List<PowerDevice> pds = getDirectDevice(cs, inPara, outPara, pd1,
				pd2.getDeviceType());
		if (pds.contains(pd2)) {
			return true;
		}
		return false;

	}

	// 搜索设备某侧的刀闸
	public static PowerDevice getSideKnife0(CommonSearch cs,
			Map<String, Object> inPara, Map<String, Object> outPara,
			PowerDevice pd) {
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate);
		inPara.put("validPort", "1");
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		List list = ((ArrayList) outPara.get("linkedDeviceList"));
		if (list == null || list.size() == 0) {
			return null;
		}
		return (PowerDevice) list.get(0);
	}

	// 搜索设备另一侧的刀闸
	public static PowerDevice getSideKnife1(CommonSearch cs,
			Map<String, Object> inPara, Map<String, Object> outPara,
			PowerDevice pd) {
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate);
		inPara.put("validPort", "2");
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		List list = ((ArrayList) outPara.get("linkedDeviceList"));
		if (list == null || list.size() == 0) {
			return null;
		}
		return (PowerDevice) list.get(0);
	}
	
	/**
	 * 获取设备直接连接的母线
	 * @param pd
	 * @return
	 */
	public static PowerDevice getDirectMotherLine(PowerDevice pd) {
		List list = getDirectDevice(pd, SystemConstants.MotherLine);
		if (list == null || list.size() == 0) {
			return null;
		}
		return (PowerDevice) list.get(0);
	}
	
	/**
	 * 获取母线的母联开关或刀闸
	 * @param pd
	 * @return
	 */
	public static PowerDevice getMLSwitchByML(PowerDevice pd) {
		List list = getLinkedDeviceByType1(pd, SystemConstants.Switch);
		if (list == null || list.size() == 0) {
			return null;
		}
		for (Iterator<PowerDevice> itr = list.iterator(); itr.hasNext();) {
			PowerDevice dev = itr.next();
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) || 
					dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL))
				return dev;
		}
		list = getLinkedDeviceByType1(pd, SystemConstants.SwitchSeparate);
		if (list == null || list.size() == 0) {
			return null;
		}
		for (Iterator<PowerDevice> itr = list.iterator(); itr.hasNext();) {
			PowerDevice dev = itr.next();
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeML))
				return dev;
		}
		return null;
	}
    /**
     * 判断两个设备是否直接相连
     * */
	public static boolean isConnected(PowerDevice pd1, PowerDevice pd2) {
		inPara.clear();
		outPara.clear();
		return isConnected(pd1, pd2, cs, inPara, outPara);
	}
    /**
     * 查找旁路开关
     * */
	public static PowerDevice getSideSwitch(PowerDevice pd) {
		// TODO Auto-generated method stub
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.Switch);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> list = ((ArrayList<PowerDevice>) outPara.get("linkedDeviceList"));
		for (PowerDevice dev : list) {
		    if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)){
		    	return dev;
		    }	
		}
		return null;
	}
    
	/**
	 * 根据主变获取停电侧母线
	 * */
	public static List<PowerDevice> getOffLoadLineByTransform(PowerDevice pd) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.MotherLine);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		cs.execute(inPara, outPara);
		List<PowerDevice> onLines = (List<PowerDevice>) outPara.get("linkedDeviceList");
		List<PowerDevice> allLines = getLinkedDeviceByType1(pd, SystemConstants.MotherLine);
	    allLines.removeAll(onLines);
	    return allLines;
	}
    
	/**
	 * 获取主变的中性点地刀
	 * @return 
	 * */
	public static List<PowerDevice> getZxdddByTransform(PowerDevice transform) {
		List<PowerDevice> glines = getDirectDevice(transform);
		List<PowerDevice> zxddd=new ArrayList<PowerDevice>();
		for (PowerDevice line : glines) {
			if(getDirectDevice(line).size()==1){
				zxddd.add(line);
			}
		}
		return zxddd;
	}

	public static List<PowerDevice> getLinkedDeviceByType3(PowerDevice pd,
			String type) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara.get("linkedDeviceList");
	}
	
	public static List<PowerDevice> getLinkedDeviceByType4(PowerDevice pd,
			String type) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara.get("linkedDeviceList");
	}
	
	public static  List<PowerDevice> getDirectDevice(PowerDevice pd1, PowerDevice pd2,
			String type) {
		List<PowerDevice> list=new ArrayList<PowerDevice>(); 
		List<PowerDevice> list1 = getDirectDevice(pd1,type);
		List<PowerDevice> list2 = getDirectDevice(pd2,type);
		for (PowerDevice l1 : list1) {
			for (PowerDevice l2 : list2) {
				if(l1.getPowerDeviceID().equals(l2.getPowerDeviceID())){
					list.add(l1);
				}
			}
		}
		return  list;
	}
    
	//判断设备是否在当前操作票中
	public static boolean isDeviceInDtdMap(PowerDevice rm) {
		Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
		for (DispatchTransDevice dispatch : dtds.values()) {
			if(dispatch.getTransDevice().equals(rm)){
				return true;
			}
		}
		return false;
	}
	
	//判断主变是否外桥接线
	public static boolean isTransformerWQ(PowerDevice pd) {
		if(!pd.getDeviceType().equals(SystemConstants.PowerTransformer))
			return false;
		List<PowerDevice> kgs = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, null, false, true, true, true);
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
				
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice device = it.next();
			if (device.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
				if(pd.getPowerVoltGrade() == device.getPowerVoltGrade()){
					xlkgList.add(device);
				}
			}
		}
		
		//还要判断是否有主变变高侧刀闸
		List<PowerDevice> zbdzs = RuleExeUtil.getDeviceList(pd, null, SystemConstants.SwitchSeparate,
				SystemConstants.PowerTransformer, "", "", true, true, true, true, true);
		
		return (kgs.size()==1&&zbdzs.size()>0&&xlkgList.size()==0);
	}
	
	//判断主变是否内桥接线
	public static boolean isTransformerNQ(PowerDevice pd) {
		if(CBSystemConstants.opcardUser.toLowerCase().contains("qj")||CBSystemConstants.opcardUser.toLowerCase().contains("hh")
				||CBSystemConstants.opcardUser.toLowerCase().contains("xsbn")||CBSystemConstants.opcardUser.toLowerCase().contains("zt")
				||CBSystemConstants.opcardUser.toLowerCase().contains("lj")||CBSystemConstants.opcardUser.toLowerCase().contains("ws")){
			if(!pd.getDeviceType().equals(SystemConstants.PowerTransformer))
				return false;
			List<PowerDevice> kgs = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, null, false, true, true, true);
			
			//还要判断是否有主变变高侧刀闸
			List<PowerDevice> zbdzs = RuleExeUtil.getDeviceList(pd, null, SystemConstants.SwitchSeparate,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeZB+","+CBSystemConstants.RunTypeKnifeZBS, "", true, true, true, true, true);
			return (kgs.size()==0&&zbdzs.size()>0);
		}
		
		
		
		if(!pd.getDeviceType().equals(SystemConstants.PowerTransformer))
			return false;
		
//		//主变变高开关双母接线为非内桥(本来要判断非单母，因为地调没有3/2接线，反而有的母线判断错误为3/2，就先这样)暂时注释，T接线有问题
//		List<PowerDevice> bgkgList  = RuleExeUtil.getTransformerSwitchHigh(pd);
//		if(bgkgList.size()>0&&bgkgList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
//			return false;
//		}
		
		List<PowerDevice> kgs = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, null, false, true, true, true);
		
		List<PowerDevice> mx = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.Switch, true, true, true);

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		
		List<PowerDevice> allMxList = new ArrayList<PowerDevice>(); 
		
		for (Iterator<PowerDevice> itor = mapStationDevice.values().iterator(); itor.hasNext();) {
			PowerDevice dev = itor.next();
			if (dev.getDeviceType().equals(SystemConstants.MotherLine)&&pd.getPowerVoltGrade() == dev.getPowerVoltGrade()){
				List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(dev, "");
				
				if(devList.size() > 0){
					allMxList.add(dev);
				}
			}
		}
		
		for (Iterator<PowerDevice> it = mx.iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
				it.remove();
			}
		}
		
		if(mx.size()>0){
			List<PowerDevice> mlkg = RuleExeUtil.getDeviceList(mx.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, true, true);
			
			if(mlkg.size() > 0 && kgs.size()==0 && allMxList.size()<3){
				return true;
			}else{
				return false;
			}
		}else{
			if(pd.getPowerStationName().contains("金刀营")){
				return true;
			}
		}
		return false;
	}
	
	//判断主变是否内桥接线
	public static boolean isTransformerKDNQ(PowerDevice pd) {
		if(!pd.getDeviceType().equals(SystemConstants.PowerTransformer))
			return false;
		
		List<PowerDevice> kgs = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, null, false, true, true, true);
		
		List<PowerDevice> mx = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.Switch, true, true, true);

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		
		List<PowerDevice> allMxList = new ArrayList<PowerDevice>(); 
		
		for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = (PowerDevice) it2.next();
			if (dev.getDeviceType().equals(SystemConstants.MotherLine)&&pd.getPowerVoltGrade() == dev.getPowerVoltGrade()){
				List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(dev, "");
				
				if(devList.size() > 0){
					allMxList.add(dev);
				}
			}
		}
		
		for (Iterator it2 = mx.iterator(); it2.hasNext();) {
			PowerDevice dev = (PowerDevice) it2.next();
			if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
				it2.remove();
			}
		}
		
		if(mx.size()>0){
			List<PowerDevice> mlkg = RuleExeUtil.getDeviceList(mx.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, true, true);
			
			if(mlkg.size() > 0 && kgs.size()==0 && allMxList.size()==3){
				return true;
			}else{
				return false;
			}
		}
		
		return false;
	}
	
	//判断主变是否并列运行
	public static boolean isTransformerBL(PowerDevice pd) {
		boolean isBL = false;
		List<Double> vols = RuleUtil.getTransformerVol(pd);
		double highVol = vols.get(vols.size()-1);
		List<PowerDevice> mls = RuleUtil.getLinkedDeviceByType2(pd, SystemConstants.MotherLine);
		 for(PowerDevice dev : mls) {
  		   if(highVol != dev.getPowerVoltGrade())
  			   continue;
  		   if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
  			 isBL = true;
  		   else
  			 isBL = false;
  	   }
		return isBL;
	}
	/**
	 * 判断设备是否为中性点地刀
	 * */
    public static boolean isZXDDD(PowerDevice pd){
    	if(!SystemConstants.SwitchFlowGroundLine.equals(pd.getDeviceType())){
    		return false;
    	}
    	List<PowerDevice> con = getDirectDevice(pd);
    	if(con.size()==1 && con.get(0).getDeviceType().equals(SystemConstants.PowerTransformer)){
    		return true;
    	}
    	return false;
    }
    
    /**
	 * 查找并列运行的主变
	 * */
    public static List<PowerDevice> getTransformerBL(PowerDevice pd) {
    	List<PowerDevice> tfs = RuleUtil.getLinkedDeviceByType5(pd, SystemConstants.PowerTransformer);
    	for (Iterator it=tfs.iterator();it.hasNext();) {
    		PowerDevice tf = (PowerDevice)it.next();
    		ArrayList<ArrayList<PowerDevice>> paths = RuleExeUtil.getPathAllByDevice(pd, tf, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSideMother, false, false);
    		boolean isBL = false;
    		for(ArrayList<PowerDevice> path : paths) {
    			int count = 0;
    			for(PowerDevice dev : path) {
        			if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade())
        				count++;
        		}
    			if(count == path.size()) {
    				isBL = true;
    				break;
    			}
    		}
    		if(!isBL)
    			it.remove();
		}
    	return tfs;
    }
    
    /**
   	 * 获得主变电压等级
   	 * */
       public static List<Double> getTransformerVol(PowerDevice pd) {
    	   List<Double> vols = new ArrayList<Double>();
    	   List<PowerDevice> switchs = RuleUtil.getDirectDevice(pd, SystemConstants.OtherEquip+","+SystemConstants.Switch+","+SystemConstants.SwitchSeparate+","+SystemConstants.ElecShock);
    	   for(PowerDevice dev : switchs) {
    		   if(!vols.contains(dev.getPowerVoltGrade()))
    			   vols.add(dev.getPowerVoltGrade());
    	   }
    	   Collections.sort(vols);
    	   return vols;
       }
       
       public static Double getTransformerVolByType(PowerDevice pd, String type) {
    	   List<Double> vols = RuleUtil.getTransformerVol(pd);
    	   if(vols.size() <= 1) {
      			return null;
      		}
      		if(vols.size() == 2 && type.equals("middle")) {
      			return null;
      		}
    	   double vol = 0;
      		if(type.equals("high"))
      			vol = vols.get(vols.size()-1);
      		else if(type.equals("middle"))
      			vol = vols.get(1);
      		else if(type.equals("low"))
      			vol = vols.get(0);
      		return vol;
       }
       
       /**
      	 * 获得主变指定侧的操作开关
      	 * */
       public static List<PowerDevice> getTransformerSwitch(PowerDevice pd, String type) {
   		List<PowerDevice> tagSwitch = new ArrayList<PowerDevice>();
   		
   		List<Double> vols = RuleUtil.getTransformerVol(pd);
   		if(vols.size() <= 1) {
   			return tagSwitch;
   		}
   		if(vols.size() == 2 && type.equals("middle")) {
   			return tagSwitch;
   		}
   		double vol = 0;
   		if(type.equals("high"))
   			vol = vols.get(vols.size()-1);
   		else if(type.equals("middle"))
   			vol = vols.get(1);
   		else if(type.equals("low"))
   			vol = vols.get(0);
   			
   		
   		List<PowerDevice> switchs = RuleUtil.getLinkedDeviceByType2(pd, SystemConstants.Switch);
   		for(PowerDevice dev : switchs) {
   			if(dev.getPowerVoltGrade() == vol) {
   				tagSwitch.add(dev);
   			}
   		}
   		if(tagSwitch.size() == 0) {
	   		if(type.equals("high")) {
   				switchs = RuleUtil.getLinkedDeviceByType4(pd, SystemConstants.Switch);
   				for(PowerDevice dev : switchs) {
   					if(dev.getPowerVoltGrade() == vol && dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
   						tagSwitch.add(dev);
   					}
   				}
	   		}
			
   		}
   		if(tagSwitch.size() == 0 || !CBSystemConstants.isMaxRangeOffTicket) {
	   		switchs = RuleUtil.getLinkedDeviceByType2(pd, SystemConstants.SwitchSeparate);
			for(PowerDevice dev : switchs) {
				if(dev.getPowerVoltGrade() == vol && 
						(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZB) ||
								dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZBS))) {
					tagSwitch.add(dev);
					break;
				}
			}
   		}
   		return tagSwitch;
   	}
       
       /**
        * 获取母联刀闸
        * */
   	public static PowerDevice getLinkedMLSwitch(PowerDevice dev) {
   		List<PowerDevice> sws = getLinkedDeviceByType(dev,SystemConstants.Switch);
   		for (PowerDevice sw : sws) {
   			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
   				return sw;
   			}
   		}
   		return null;
   	}
       /**
        * 根据一条母线获取与之相连的另一条在运行的母线
        * @return 
        * */
   	public static  PowerDevice getDoubelMLineAnotherBYMotherline(PowerDevice dev) {
   		List<PowerDevice> lines = getLinkedDeviceByType(dev, SystemConstants.MotherLine);
   		lines.remove(dev);
   		for (PowerDevice l : lines) {
   			if(l.getDeviceStatus().equals("0")){
   				return l;
   			}
   		}
   		return null;
   	}
   	
   	/**
	 * 根据设备获取电源线路
	 * */
	public static List<PowerDevice> getSource(PowerDevice pd) {
		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
		inPara.clear();
		outPara.clear();
		
		//判断是不是刀闸
//		boolean isMLKnife = false;
//		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeML))
//			isMLKnife = true;
//		else {
//			List<PowerDevice> kgs = RuleUtil.getDirectDevice(pd, SystemConstants.Switch);
//			for (PowerDevice kg : kgs) {
//	   			if(kg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
//	   				isMLKnife = true;
//	   			}
//	   		}
//		}
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.InOutLine);
			inPara.put("isSearchOffPath", false);
			inPara.put("isStopOnBusbarSection", false);
			inPara.put("isStopOnTagDevType", true);
			cs.execute(inPara, outPara);
		}
		else if(pd.getPowerVoltGrade() == station.getPowerVoltGrade() && !pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.InOutLine);
			inPara.put("isSearchOffPath", true);
			inPara.put("isStopOnBusbarSection", true);
			inPara.put("isStopOnTagDevType", true);
			cs.execute(inPara, outPara);
		}
		else {
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.InOutLine);
			inPara.put("isSearchOffPath", true);
			inPara.put("isStopOnBusbarSection", false);
			inPara.put("isStopOnTagDevType", true);
			cs.execute(inPara, outPara);
		}
		
		List<PowerDevice> lines = (List<PowerDevice>) outPara.get("linkedDeviceList");
		
		for (Iterator<PowerDevice> itr = lines.iterator(); itr.hasNext();) {
			PowerDevice line = itr.next();
			if(line.getPowerVoltGrade() != station.getPowerVoltGrade()){
				itr.remove();
   			}
		}
	    return lines;
	}
	
	/**
	 * 判断线路是否双侧电源线路
	 * @param pd
	 * @return
	 */
	public static boolean isDoubleSource(PowerDevice pd) {
		
		ArrayList<String> lineList = CBSystemConstants.getEquiplinemap().get(pd.getPowerDeviceID());
		if(lineList == null || lineList.size() == 0)
			return false;
		String lineID = lineList.get(0);
		ArrayList<String> stationList = CBSystemConstants.getLinestationmap().get(lineID);
		if(stationList.size() != 2)
			return false;
		PowerDevice s1 = CBSystemConstants.getPowerStation(stationList.get(0));
		PowerDevice s2 = CBSystemConstants.getPowerStation(stationList.get(1));
		CreatePowerStationToplogy.loadFacEquip(stationList.get(0));
		CreatePowerStationToplogy.loadFacEquip(stationList.get(1));
		if(s1.getPowerVoltGrade() != s2.getPowerVoltGrade())
			return false;
		
		ArrayList<String> equipLineList = CBSystemConstants.getLineequipmap().get(lineID);
		for (String equipLineID : equipLineList) {
			String stationID = "";
			PowerDevice equipLine = null;
			if(CBSystemConstants.getPowerDevice(s1.getPowerDeviceID(), equipLineID) != null) {
				stationID = s1.getPowerDeviceID();
				equipLine = CBSystemConstants.getPowerDevice(s1.getPowerDeviceID(), equipLineID);
			}
			else {
				stationID = s2.getPowerDeviceID();
				equipLine = CBSystemConstants.getPowerDevice(s2.getPowerDeviceID(), equipLineID);
			}
			if(equipLine == null)
				equipLine = CBSystemConstants.getPowerDevice(s2.getPowerDeviceID(), equipLineID);
			 List<PowerDevice> otherLineList = RuleUtil.getPowerDevice(stationID, SystemConstants.InOutLine, equipLine.getPowerVoltGrade());
			 boolean isSourceExist = false;
			 for (PowerDevice otherLine : otherLineList) {
				 if(otherLine.getPowerDeviceID().equals(equipLine.getPowerDeviceID()))
					 continue;
				 else if(isDoubleCircuit(equipLine, otherLine))
					 continue;
				 else
					 isSourceExist = true;
			 }
			 if(!isSourceExist)
				 return false;
		}
		
		return true;
		
	}
	
	/**
	 * 判断两条线路是否双回线
	 * @param line1
	 * @param line2
	 * @return
	 */
	public static boolean isDoubleCircuit(PowerDevice line1, PowerDevice line2) {
		ArrayList<String> lineSysList1 = CBSystemConstants.getEquiplinemap().get(line1.getPowerDeviceID());
		ArrayList<String> lineSysList2 = CBSystemConstants.getEquiplinemap().get(line2.getPowerDeviceID());
		if(lineSysList1==null || lineSysList2==null)
			return false;
		if(lineSysList1.size() == 0 || lineSysList2.size() == 0)
			return false;
		ArrayList<String> list1 = CBSystemConstants.getLinestationmap().get(lineSysList1.get(0));
		ArrayList<String> list2 = CBSystemConstants.getLinestationmap().get(lineSysList2.get(0));
		if(list1.size() != 2 || list2.size() != 2)
			return false;
		if(list1.get(0).equals(list2.get(0)) && list1.get(1).equals(list2.get(1)))
			return true;
		if(list1.get(0).equals(list2.get(1)) && list1.get(1).equals(list2.get(0)))
			return true;
		else
			return false;
	}
	
	/**
	 * 得到刀闸最大停电范围需要操作的刀闸
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getOprKnife(PowerDevice pd) {
		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
		List<PowerDevice> knifes = RuleUtil.getDirectDevice(pd, SystemConstants.SwitchSeparate);
		if(knifes.size() <= 1)
			return null;
		String kinfeRunType = "";
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {
			kinfeRunType = CBSystemConstants.RunTypeKnifeMX;
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
			kinfeRunType = CBSystemConstants.RunTypeKnifeMX;
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
			boolean isDouble = false;
			List<PowerDevice> mls = RuleUtil.getLinkedDeviceByType2(pd, SystemConstants.MotherLine);
			for (PowerDevice ml : mls) {
				 if(ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
					 isDouble = true;
					 break;
				 }
			 }
			if(!isDouble) {
				inPara.clear();
				outPara.clear();
				inPara.put("oprSrcDevice", pd);
				if(pd.getPowerVoltGrade() == station.getPowerVoltGrade()) {
					inPara.put("tagDevType", SystemConstants.InOutLine);
					inPara.put("excDevType", SystemConstants.PowerTransformer);
				}
				else
					inPara.put("tagDevType", SystemConstants.PowerTransformer);
				
				inPara.put("isSearchOffPath", false);
				inPara.put("isStopOnBusbarSection", false);
				inPara.put("isStopOnTagDevType", true);
				cs.execute(inPara, outPara);
				List<PowerDevice> lines = (List<PowerDevice>) outPara.get("linkedDeviceList");
				for (Iterator<PowerDevice> itr = lines.iterator(); itr.hasNext();) {
					PowerDevice line = itr.next();
					if(line.getPowerVoltGrade() != station.getPowerVoltGrade()){
						itr.remove();
		   			}
				}
				
				if(lines.size() > 0) {
					List<PowerDevice> path = RuleUtil.getConnectPathByDevice(pd, lines.get(lines.size()-1));
					if(pd.getPowerVoltGrade() == station.getPowerVoltGrade()) {
						for(Iterator it=knifes.iterator();it.hasNext();){
							PowerDevice knife = (PowerDevice)it.next();
							if(knife.getDeviceStatus().equals("0") && !path.contains(knife))
								it.remove();
						}
					}
					else {
						for(Iterator it=knifes.iterator();it.hasNext();){
							PowerDevice knife = (PowerDevice)it.next();
							if(knife.getDeviceStatus().equals("0") && path.contains(knife))
								it.remove();
						}
					}
				}
			}
		}
		else if(pd.getPowerVoltGrade() == station.getPowerVoltGrade()) {
			kinfeRunType = CBSystemConstants.RunTypeKnifeDY;
		}
		else if(pd.getPowerVoltGrade() != station.getPowerVoltGrade()) {
			kinfeRunType = CBSystemConstants.RunTypeKnifeDY;
		}
		for(Iterator it=knifes.iterator();it.hasNext();){
			PowerDevice knife = (PowerDevice)it.next();
			if(!kinfeRunType.equals("") && !knife.getDeviceRunType().equals(kinfeRunType)) {
				it.remove();
			}
		}
		return knifes;
	}
	
	public static List<PowerDevice> getTransformerSwitchs(PowerDevice pd1, PowerDevice pd2) {
		List<PowerDevice> path = new ArrayList<PowerDevice>();
		PowerDevice ml1 = null;
		PowerDevice ml2 = null;
		List<PowerDevice> mls = RuleUtil.getLinkedDeviceByType2(pd1, SystemConstants.MotherLine);
		 for (PowerDevice ml : mls) {
			 if(ml.getPowerVoltGrade() == pd1.getPowerVoltGrade())
				 ml1 = ml;
		 }
		mls = RuleUtil.getLinkedDeviceByType2(pd2, SystemConstants.MotherLine);
		 for (PowerDevice ml : mls) {
			 if(ml.getPowerVoltGrade() == pd2.getPowerVoltGrade())
				 ml2 = ml;
		 }
		 if(!ml1.getPowerDeviceID().equals(ml2.getPowerDeviceID())) {
			 List<PowerDevice> devs = RuleUtil.getConnectPathByDevice(ml1, ml2);
			 for (PowerDevice dev : devs) {
				 if(dev.getDeviceType().equals(SystemConstants.Switch))
					 path.add(dev);
			 }
		 }
		return path;
	}
	
	public static PowerDevice getLineSwitch(PowerDevice pd) {
		List<PowerDevice> kgs = RuleUtil.getLinkedDeviceByType2(pd, SystemConstants.Switch);
		PowerDevice lineSwitch = null;
		for (PowerDevice kg : kgs) {
		 if(kg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL))
			 lineSwitch = kg;
		}
		return lineSwitch;
	}
	
	/**
	 * 创建时间 2013年11月19日 上午9:36:50 查找该母线上的电源点
	 * 
	 * <AUTHOR>
	 * @Title getRunningSrcDeviceByType
	 * @param motherline
	 *            传入母线
	 * @param runType
	 *            目标电源设备类型
	 * @return 目标电源设备集合
	 */
	public static List<PowerDevice> getRunningSrcDeviceByType(
			PowerDevice motherline, String runType) {
		if (motherline == null) {
			return new ArrayList<PowerDevice>();
		}
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", motherline);
		inPara.put("tagDevType", SystemConstants.Switch+","+SystemConstants.SwitchSeparate);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", false);
		cs.execute(inPara, outPara);
		List<PowerDevice> results = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator itr = results.iterator(); itr.hasNext();) {
			PowerDevice device = (PowerDevice) itr.next();
			if (!device.getDeviceStatus().equals("0")
					|| !runType.contains(device.getDeviceRunType())) {
				itr.remove();
			}
		}
		return results;
	}

	/**
	 * 创建时间 2013年11月19日 上午9:37:56 查找线路开关所属母线(排除旁母)
	 * 
	 * <AUTHOR>
	 * @Title getSwitchOrderMotherline
	 * @param lineSwitch
	 *            传入的线路开关
	 * @return 找到的母线（排除了旁母）
	 */
	public static PowerDevice getSwitchOrderMotherline(PowerDevice lineSwitch) {
		if (lineSwitch == null
				|| !lineSwitch.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchXL)) {
			return null;
		}
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", lineSwitch);
		inPara.put("tagDevType", SystemConstants.MotherLine);
		inPara.put("excDevType",SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		/*List<PowerDevice> results = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator itr = results.iterator(); itr.hasNext();) {
			PowerDevice l = (PowerDevice) itr.next();
			if (l.getDeviceRunType()
					.equals(CBSystemConstants.RunTypeSideMother)) {
				itr.remove();
			}
		}*/
		HashMap<PowerDevice,ArrayList<PowerDevice>> path =(HashMap<PowerDevice, ArrayList<PowerDevice>>) outPara.get(CommonSearch.PATHLIST);
		PowerDevice pd = null;
		for (Iterator itr = path.keySet().iterator(); itr.hasNext();) {
			PowerDevice l = (PowerDevice) itr.next();
			if (l.getDeviceRunType()
					.equals(CBSystemConstants.RunTypeSideMother)) {
				itr.remove();
				continue;
			}
			if(pd==null){
				pd=l;
			}else{
				if(path.get(l).size()<path.get(pd).size()){
					pd=l;
				}
			}
		}
		return pd;
	}

	/**
	 * 创建时间 2013年11月19日 上午9:38:55 查找线路开关连接的旁母
	 * 
	 * <AUTHOR>
	 * @Title getSwitchSiderMotherline
	 * @param lineSwitch
	 *            线路开关
	 * @return 找到的旁母
	 */
	public static PowerDevice getSwitchSiderMotherline(PowerDevice lineSwitch) {
		if (lineSwitch == null
				|| !lineSwitch.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchXL)) {
			return null;
		}
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", lineSwitch);
		inPara.put("tagDevType", SystemConstants.MotherLine);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> results = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator itr = results.iterator(); itr.hasNext();) {
			PowerDevice l = (PowerDevice) itr.next();
			if (l.getDeviceRunType()
					.equals(CBSystemConstants.RunTypeSideMother)) {
				return l;
			}
		}
		return null;
	}

	/**
	 * 创建时间 2013年11月19日 上午9:40:15 查找线路开关所属线路
	 * 
	 * <AUTHOR>
	 * @Title getSwitchOrderLine
	 * @param lineSwitch
	 *            线路开关
	 * @return 找到的线路
	 */
	public static List<PowerDevice> getSwitchOrderLines(PowerDevice lineSwitch) {
		if (lineSwitch == null
				|| !lineSwitch.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchXL)) {
			return null;
		}
		/*List<PowerDevice> kns = RuleUtil.getDirectDevice(lineSwitch,
				SystemConstants.SwitchSeparate);
		PowerDevice result = null;
		for (PowerDevice kn : kns) {
			List<PowerDevice> ls = RuleUtil.getDirectDevice(kn,
					SystemConstants.InOutLine);
			if (ls.size() != 0) {
				result = ls.get(0);
				break;
			}
		}*/
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", lineSwitch);
		inPara.put("tagDevType", SystemConstants.InOutLine);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara
				.get("linkedDeviceList");
	}

	/**
	 * 查找母线相连的母联开关或母联刀闸
	 * */
	public static List<PowerDevice> getMLSwitchOrKnife(PowerDevice motherLine) {
		if (motherLine == null
				|| !motherLine.getDeviceType().equals(
						SystemConstants.MotherLine)) {
			return null;
		}
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", motherLine);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> devices = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", motherLine);
		inPara.put("tagDevType", SystemConstants.Switch);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		devices.addAll((List<PowerDevice>) outPara.get("linkedDeviceList"));
		for (Iterator itr = devices.iterator(); itr.hasNext();) {
			PowerDevice dev = (PowerDevice) itr.next();
			if (!dev.getDeviceStatus().equals("0")) {
				itr.remove();
				continue;
			}
			if (!dev.getDeviceRunType()
					.equals(CBSystemConstants.RunTypeKnifeML)
					&& !dev.getDeviceRunType().equals(
							CBSystemConstants.RunTypeSwitchML)) {
				itr.remove();
				continue;
			}
		}
		return devices;
	}

	/**
	 * 根据母线和母联开关查找另一根母线
	 * */
	public static PowerDevice getAnotherMotherLine(PowerDevice ml,
			PowerDevice motherLine) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", ml);
		inPara.put("tagDevType", SystemConstants.MotherLine);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> ls = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		ls.remove(motherLine);
		if(ls.size() == 0)
			return null;
		return ls.get(0);
	}

	/**
	 * 母线到主变的所有通路
	 * */
	public static HashMap<PowerDevice, ArrayList<ArrayList<PowerDevice>>> getTransformsLoadByMotherLine(
			PowerDevice motherLine) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", motherLine);
		inPara.put("tagDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		// inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		return (HashMap<PowerDevice, ArrayList<ArrayList<PowerDevice>>>) outPara
				.get("allPathList");
	}

	/**
	 * 创建时间 2013年11月18日 下午9:12:32
	 * 
	 * 根据母线搜连接的主变
	 * 
	 * <AUTHOR>
	 * @Title getTransformsByMotherLine
	 * @param motherLine
	 *            母线
	 * @return 母线连接的主变集合
	 */
	public static List<PowerDevice> getTransformsByMotherLine(
			PowerDevice motherLine) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", motherLine);
		inPara.put("tagDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", true);
		inPara.put("excDevRunType", CBSystemConstants.RunTypeSwitchXL);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) outPara.get("linkedDeviceList");
	}

	/**
	 * 判断开关电压等级是否为最低
	 * 
	 * @param swt
	 *            开关
	 * @return true 最低 false 中高压
	 * */
	public static boolean isLowestLevel(PowerDevice swt) {
		List<PowerDevice> tfs = RuleUtil.getLinkedDeviceByType(swt, SystemConstants.PowerTransformer);
		PowerDevice tf=tfs.get(0);
		List<PowerDevice> mls = RuleUtil.getLinkedDeviceByType(tf, SystemConstants.MotherLine);
		List<Double> vols=new ArrayList<Double>();
		for (PowerDevice ml : mls) {
			double vol=ml.getPowerVoltGrade();
			if(!vols.contains(vol)){
				vols.add(vol);
			}
		}
		Collections.sort(vols);
		
		double vol=swt.getPowerVoltGrade();
		if(vols.get(0)==vol){
			return true;
		}
		return false;
	}
	
	/**
	  * 创建时间 2013年12月13日 下午3:19:21
	  * 设备电压等级是否与厂站一致
	  * <AUTHOR>
	  * @Title isSameLevelWithStation
	  * @param swt
	  * @return
	  */
	public static boolean isSameLevelWithStation(PowerDevice swt){
		String stationid = swt.getPowerStationID();
		double stvolt = CBSystemConstants.getPowerStation(stationid)
				.getPowerVoltGrade();
		if (swt.getPowerVoltGrade() == stvolt) {
			return true;
		}
		return false;
	}
	
	

	/**
	 * 创建时间 2013年11月19日 上午9:43:15 查找线路开关所属线路连接的另一侧厂站
	 * 
	 * <AUTHOR>
	 * @Title getAnotherSideStation
	 * @param swt
	 *            传入开关
	 * @return 另一侧厂站
	 */
	public static List<PowerDevice> getAnotherSideStation(PowerDevice swt) {
		List<PowerDevice> lines = getSwitchOrderLines(swt);
		List<PowerDevice> pds=new ArrayList<PowerDevice>();
		for (PowerDevice line : lines) {
			ArrayList<String> equipids = CBSystemConstants.getEquiplinemap().get(
					line.getPowerDeviceID());
			if (equipids == null || equipids.size() == 0) {
				continue;
			}
			ArrayList<String> stations = CBSystemConstants.getLinestationmap().get(
					equipids.get(0));
			List<PowerDevice> sts = new ArrayList<PowerDevice>();
			for (Iterator itr = stations.iterator(); itr.hasNext();) {
				String st = (String) itr.next();
				if (!st.equals(swt.getPowerStationID())) {
					pds.add(CBSystemConstants.getPowerStation(st));
				}
			}
		}
		
		return pds;
	}

	/**
	 * 创建时间 2013年11月19日 上午10:25:11 根据线路开关获取主变高压侧开关（或刀闸）
	 * 
	 * <AUTHOR>
	 * @Title getHighTransSwitch
	 * @param swt
	 *            传入的线路开关
	 * @return 主变高压侧开关（或刀闸）
	 */
	public static List<PowerDevice> getHighTransSwitch(PowerDevice swt) {
		PowerDevice motherline = getSwitchOrderMotherline(swt);
		List<PowerDevice> trans = getTransformsByMotherLine(motherline);
		List<PowerDevice> sws = new ArrayList<PowerDevice>();
		for (Iterator itr = trans.iterator(); itr.hasNext();) {
			PowerDevice tr = (PowerDevice) itr.next();
			PowerDevice pd = getTranHighSideSwitch(tr);
			if (pd == null) {
				pd = getTranHighSideKnife(tr);
			}
			sws.add(pd);
		}
		return sws;
	}

	/**
	 * 创建时间 2013年11月19日 上午10:37:15 根据主变获取主变高压侧开关
	 * 
	 * <AUTHOR>
	 * @Title getTranHighSide
	 * @param tran
	 *            主变
	 * @return 主变高压侧开关
	 */
	public static PowerDevice getTranHighSideSwitch(PowerDevice tran) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", tran);
		inPara.put("tagDevType", SystemConstants.Switch);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", false);
		cs.execute(inPara, outPara);
		List<PowerDevice> sws = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator itr = sws.iterator(); itr.hasNext();) {
			PowerDevice sw = (PowerDevice) itr.next();
			if (sw.getDeviceRunType()
					.equals(CBSystemConstants.RunTypeSwitchDYC)) {
				return sw;
			}
		}
		return null;
	}

	/**
	 * 创建时间 2013年11月19日 上午10:39:15 根据主变获取主变高压侧刀闸（代替开关）
	 * 
	 * <AUTHOR>
	 * @Title getTranHighSide
	 * @param tran
	 *            主变
	 * @return 主变高压侧刀闸（代替开关）
	 */
	public static PowerDevice getTranHighSideKnife(PowerDevice tran) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", tran);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> sws = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator itr = sws.iterator(); itr.hasNext();) {
			PowerDevice sw = (PowerDevice) itr.next();
			if (sw.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZBS)) {
				return sw;
			}
		}
		return null;
	}

	/**
	 * 创建时间 2013年11月19日 下午4:15:21 获取线路开关另一侧厂站的线路集合
	 * 
	 * <AUTHOR>
	 * @Title getAnotherSideSwitch
	 * @param pd
	 */
	public static List<PowerDevice> getAnotherSideLines(PowerDevice pd) {
		List<PowerDevice> lines = getSwitchOrderLines(pd);
		List<PowerDevice> rs=new ArrayList<PowerDevice>();
		for (PowerDevice line : lines) {
			/*String stid = pd.getPowerStationID();
			ArrayList<String> equipids = CBSystemConstants.getEquiplinemap()
					.get(line.getPowerDeviceID());
			if(equipids==null){
				continue;
			}
			String equipid =equipids.get(0);
			ArrayList<String> elines = new ArrayList<String>(CBSystemConstants.getLineequipmap().get(
					equipid));
			ArrayList<String> stations =new ArrayList<String>(CBSystemConstants.getLinestationmap().get(
					equipid));
			stations.remove(stid);
			elines.remove(line.getPowerDeviceID());
			if(stations.size()!=0){
				new CreatePowerStationToplogy().loadFacData(stations.get(0));
				rs.add(CBSystemConstants.getPowerDevice(stations.get(0), elines.get(0)));
			}*/
			PowerDevice another = getAnotherSideLineByLine(line);
			if(another!=null){
				rs.add(another);
			}
			
		}
		return rs;
		
	}
	/**
	 * 创建时间 2013年11月19日 下午4:15:21 获取线路另一侧厂站的线路
	 * 
	 * <AUTHOR>
	 * @Title getAnotherSideSwitch
	 * @param pd
	 */
	public static PowerDevice getAnotherSideLineByLine(PowerDevice l) {
		String stid = l.getPowerStationID();
		ArrayList<String> equipids = CBSystemConstants.getEquiplinemap()
				.get(l.getPowerDeviceID());
		if(equipids==null){
			return null;
		}
		String equipid =equipids.get(0);
		ArrayList<String> elines = new ArrayList<String>(CBSystemConstants.getLineequipmap().get(
				equipid));
		ArrayList<String> stations =new ArrayList<String>(CBSystemConstants.getLinestationmap().get(
				equipid));
		stations.remove(stid);
		elines.remove(l.getPowerDeviceID());
		if(stations.size()!=0){
			new CreatePowerStationToplogy().loadFacData(stations.get(0));
			return CBSystemConstants.getPowerDevice(stations.get(0), elines.get(0));
		}
		return null;
	}

	/**
	 * 创建时间 2013年11月19日 下午4:41:43 获取线路开关或线路刀闸（代替开关的刀闸）
	 * 
	 * <AUTHOR>
	 * @param line
	 *            线路
	 * @Title getlineSwitchOrKnife
	 * @return
	 */
	public static PowerDevice getlineSwitchOrKnife(PowerDevice line) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", line);
		inPara.put("tagDevType", SystemConstants.Switch);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> sws = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		List<PowerDevice> result=new ArrayList<PowerDevice>();
		if (sws == null || sws.size() == 0) {
			inPara.clear();
			outPara.clear();
			inPara.put("oprSrcDevice", line);
			inPara.put("tagDevType", SystemConstants.SwitchSeparate);
			inPara.put("isSearchOffPath", false);
			inPara.put("isStopOnBusbarSection", true);
			inPara.put("isStopOnTagDevType", true);
			cs.execute(inPara, outPara);
			sws = (List<PowerDevice>) outPara.get("linkedDeviceList");
			if(sws == null)
				return null;
			for (Iterator itr = sws.iterator(); itr.hasNext();) {
				PowerDevice sw = (PowerDevice) itr.next();
				if (sw.getDeviceRunType().equals(
						CBSystemConstants.RunTypeKnifeXLS)) {
					result.add(sw);
				}
			}
			if(result.size() == 0)
				return null;
			else
				return result.get(0);
		}
		return sws.get(0);
	}
	
	/**
	 * 创建时间 2015年10月08日 上午11:34:43 获取线路对侧刀闸编号
	 * 
	 * <AUTHOR>
	 * @param line
	 *            线路
	 * @Title getlineSwitchOrknife
	 * @return
	 */
	public static PowerDevice getlineSwitchOrknife(PowerDevice line) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", line);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> sws = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		List<PowerDevice> result=new ArrayList<PowerDevice>();
		if (sws == null || sws.size() == 0) {
			inPara.clear();
			outPara.clear();
			inPara.put("oprSrcDevice", line);
			inPara.put("tagDevType", SystemConstants.SwitchSeparate);
			inPara.put("isSearchOffPath", false);
			inPara.put("isStopOnBusbarSection", true);
			inPara.put("isStopOnTagDevType", true);
			cs.execute(inPara, outPara);
			sws = (List<PowerDevice>) outPara.get("linkedDeviceList");
			if(sws == null)
				return null;
			for (Iterator itr = sws.iterator(); itr.hasNext();) {
				PowerDevice sw = (PowerDevice) itr.next();
				if (sw.getDeviceRunType().equals(
						CBSystemConstants.RunTypeKnifeXLS)) {
					result.add(sw);
				}
			}
			if(result.size() == 0)
				return null;
			else
				return result.get(0);
		}
		return sws.get(0);
	}	
	/**
	 * 创建时间 2013年11月19日 下午7:59:34 获取母线连接的主变开关
	 * 
	 * <AUTHOR>
	 * @Title getTransSwitch
	 * @param ml
	 * @return
	 */
	public static List<PowerDevice> getTransSwitch(PowerDevice ml) {
		inPara.clear();
		outPara.clear();
		inPara.put("oprSrcDevice", ml);
		inPara.put("tagDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", false);
		cs.execute(inPara, outPara);
		HashMap<PowerDevice, List<PowerDevice>> loads = (HashMap<PowerDevice, List<PowerDevice>>) outPara
				.get("pathList");
		ArrayList<PowerDevice> rs = new ArrayList<PowerDevice>();
		for (PowerDevice tr : loads.keySet()) {
			List<PowerDevice> load = loads.get(tr);
			for (PowerDevice dev : load) {
				if (dev.getDeviceRunType()
						.equals(CBSystemConstants.RunTypeSwitchDYC)
						|| dev.getDeviceRunType().equals(
								CBSystemConstants.RunTypeSwitchFHC)
						|| dev.getDeviceRunType().equals(
								CBSystemConstants.RunTypeKnifeZBS)) {
					rs.add(dev);
				}
			}
		}
		return rs;
	}
	
	/**
	  * 创建时间 2013年11月23日 下午3:05:41
	  * 判断一个设备是否有关联的接地线 存在于接地线缓存中  并且是在挂上状态
	  * <AUTHOR>
	  * @Title IsDeviceHasGroundLineCache
	  * @param pd
	  * @return
	  */
	public static boolean IsDeviceHasGroundLineCache(PowerDevice pd){
		List<PowerDevice> gls = CBSystemConstants.getGroundLineByStationAndDevice(pd.getPowerStationID(), pd.getPowerDeviceID());
		for (PowerDevice gl : gls) {
			if(gl.getDeviceStatus().equals("0")){
				return true;
			}
		}
		return false;
	}
	
	/**
	  * 创建时间 2013年12月11日 上午9:04:13
	  * 获取线路开关连通的主变
	  * <AUTHOR>
	  * @Title getTransformsBySwitchXL
	  * @param sw
	  */
	public static List<PowerDevice> getTransformsBySwitchXL(PowerDevice sw){
		outPara.clear();
		inPara.clear();
		inPara.put("oprSrcDevice", sw);
		inPara.put("tagDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", true);
		inPara.put("excDevRunType", CBSystemConstants.RunTypeSwitchXL);
		cs.execute(inPara, outPara);
		List<PowerDevice> sws = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		return sws;
	}
	/**
	  * 创建时间 2013年12月12日 下午6:39:45
	  * 获取线路开关连通任意一个的主变
	  * <AUTHOR>
	  * @Title getTransformBySwitchXL
	  * @param sw
	  * @return
	  */
	public static PowerDevice getTransformBySwitchXL(PowerDevice sw){
		return getTransformsBySwitchXL(sw).get(0);
	}

	public static boolean isVolLowestInStation(PowerDevice station,
			double pVol) {
		CreatePowerStationToplogy.loadFacData(station.getPowerDeviceID());
		HashMap<String, PowerDevice> devs = CBSystemConstants.getMapPowerStationDevice().get(station.getPowerDeviceID());
		List<Double> vols=new ArrayList<Double>();
		for (String str : devs.keySet()) {
			PowerDevice dev = devs.get(str);
			double vol=dev.getPowerVoltGrade();
			if(!vols.contains(vol)){
				vols.add(vol);
			}
		}
		Collections.sort(vols);
		if(pVol==vols.get(0)){
			return true;
		}
		return false;
	}

	public static String getlineFlowInfo(String powerDeviceID) {
		return CBSystemConstants.getLineFlowInfo().get(powerDeviceID);
		/*String sql="select * from "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO t where t.EQUIP_ID =?";
		List list = DBManager.queryForList(sql, powerDeviceID);
		if(list.size()==0)
			return "";
		Map map=(Map) list.get(0);
		return StringUtils.ObjToString(map.get("FLOWINFO"));*/
	}

	
  
    
    public  static int getLineTurnModel(PowerDevice d){
    	int level =0;
    	PowerDevice sw = RuleExeUtil.getDeviceSwitch(d);
    	
    	if(sw == null)
    		return 0;
    	if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) //3/2接线
    		return 1;
    	
    	if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) //3/2接线
    		return 1;
    	
    	
	    return 0;
    }

}
