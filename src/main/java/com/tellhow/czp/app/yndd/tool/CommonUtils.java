package com.tellhow.czp.app.yndd.tool;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @BelongProject : powernet-graphic-app-czpyndd
 * @BelongPackage : com.tellhow.czp.app.yndd.tool
 * @Description : 工具类
 * <AUTHOR> WangJQ
 * @Date : 2025/3/19 16:49
 */
public class CommonUtils {
    private static final Map<String, String[]> areaMap = new HashMap<String, String[]>();

    public static final String WORD_RBM_SQL = "SELECT MODELDESC,BEGINSTATUS,ENDSTATUS,OPERATION " +
            "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL ORDER BY TO_NUMBER(ORDERID) ASC";

    static {
        areaMap.put("opcardbs", new String[]{"保山地调"});
        areaMap.put("opcardcx", new String[]{"楚雄地调"});
        areaMap.put("opcarddh", new String[]{"德宏地调"});
        areaMap.put("opcarddl", new String[]{"大理地调"});
        areaMap.put("opcarddq", new String[]{"迪庆地调"});
        areaMap.put("opcardhh", new String[]{"红河地调"});
        areaMap.put("opcardkm", new String[]{"昆明地调"});
        areaMap.put("opcardlc", new String[]{"临沧地调"});
        areaMap.put("opcardlj", new String[]{"丽江地调"});
        areaMap.put("opcardnj", new String[]{"怒江地调"});
        areaMap.put("opcardpe", new String[]{"普洱地调"});
        areaMap.put("opcardqj", new String[]{"曲靖地调"});
        areaMap.put("opcardws", new String[]{"文山地调"});
        areaMap.put("opcardyx", new String[]{"玉溪地调"});
        areaMap.put("opcardzt", new String[]{"昭通地调"});
        areaMap.put("opcardxsbn", new String[]{"西双版纳地调"});
    }
    private CommonUtils() {
    }

    /**
     * 通过设备id来获取设备名称和厂站名称
     * @param deviceId 设备id
     * @return 设备名称和厂站名称
     */
    public static String[] getDevNameAndStationNameByDevId(String deviceId) {
        String[] result = new String[2];
        String sql = "select EQUIP_NAME, STATION_NAME from " + CBSystemConstants.equipUser + "T_SUBSTATION a " +
                "join " + CBSystemConstants.equipUser + " T_EQUIPINFO b " +
                "on a.STATION_ID=b.STATION_ID where b.EQUIP_ID = ? ";
        List<Map<String,String>> nameList = DBManager.queryForList(sql, deviceId);
        for (Map<String,String> map : nameList) {
            result[0] = map.get("EQUIP_NAME");
            result[1] = map.get("STATION_NAME");
            return result;
        }
        return result;
    }

    /**
     * 获取当前的地调
     * @param isNum 是否返回地调编号
     */
    public static String getCurrentArea(boolean isNum) {
        String areaName = "云南地调";
        String areaNo = "0401";
        for (Map.Entry<String, String[]> entry : areaMap.entrySet()) {
            if (CBSystemConstants.equipUser.toLowerCase().contains(entry.getKey())) {
                areaName = entry.getValue()[0];
                break;
            }
        }
        return isNum ? areaNo : areaName;
    }

    public static String join(String[] array, String delimiter) {
        if (array == null || array.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < array.length - 1; i++) {
            sb.append(array[i]).append(delimiter);
        }
        sb.append(array[array.length - 1]);
        return sb.toString();
    }

    public static String join(Set<String> set, String delimiter) {
        if (set == null || set.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        int size = set.size();
        int counter = 0;
        for (String item : set) {
            sb.append(item);
            if (++counter < size) {
                sb.append(delimiter);
            }
        }
        return sb.toString();
    }

}
