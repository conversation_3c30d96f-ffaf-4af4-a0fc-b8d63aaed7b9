package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXZBGLMX implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪主变关联母线".equals(tempStr)){
			List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.MotherLine);
			
			if(mxList.size()>0){
				if(mxList.get(0).getPowerVoltGrade() == 10){
					replaceStr = "（10.5kV"+CZPService.getService().getDevNum(mxList.get(0))+"母）";
				}
			}
		}
		return replaceStr;
	}
	
}
