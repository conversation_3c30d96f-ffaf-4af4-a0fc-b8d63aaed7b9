package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHXLMC  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河线路名称".equals(tempStr)){
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			
			replaceStr = CZPService.getService().getDevName(lineList);
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}