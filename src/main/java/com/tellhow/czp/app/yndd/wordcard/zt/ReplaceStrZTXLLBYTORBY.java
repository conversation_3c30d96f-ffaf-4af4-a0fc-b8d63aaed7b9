package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTXLLBYTORBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通线路由冷备用转热备用".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			for(PowerDevice dev : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				List<PowerDevice> zbList =  RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, "", true, true, true);
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

				if(xlkgList.size() == 2){
					for(PowerDevice xlkg : xlkgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.isDeviceHadStatus(xlkg, "2", "1")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(xlkg)+"由冷备用转热备用/r/n";
							}
						}
					}
					
					for(PowerDevice xlkg : xlkgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.isDeviceHadStatus(xlkg, "2", "1")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(xlkg)+"由冷备用转热备用/r/n";
							}
						}
					}
				}else{
					if(zbList.size()>0){
						if(RuleExeUtil.isTransformerXBZ(zbList.get(0))||RuleExeUtil.isTransformerXBDY(zbList.get(0))){
							if(RuleExeUtil.isDeviceHadStatus(zbList.get(0), "2", "1")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(zbList)+"由冷备用转热备用/r/n";
							}
							continue;
						}
					}
					
					if(xlkgList.size() == 0){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
							PowerDevice dz = itor.next();
							
							if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
								itor.remove();
							}
						}
						
						if(dzList.size()==1){
							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
									replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dz)+"/r/n";
								}
							}
						}
					}else{
						for(PowerDevice loadLineLoadSwitch : xlkgList){
							if(RuleExeUtil.isDeviceHadStatus(loadLineLoadSwitch, "2", "1")){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(loadLineLoadSwitch, SystemConstants.SwitchSeparate);
								
								for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
									PowerDevice dz = itor.next();
									
									if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
										itor.remove();
									}
								}
								
								if(dzList.size()==1){
									for(PowerDevice dz : dzList){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											replaceStr +=  stationName+"@合上"+CZPService.getService().getDevName(dz)+"/r/n";
										}
									}
								}else{
									for(PowerDevice dz : xldzList){//三刀闸
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											replaceStr +=  stationName+"@合上"+CZPService.getService().getDevName(dz)+"/r/n";
										}
									}
									
									String mxName = "";

									if(loadLineLoadSwitch.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
										List<PowerDevice> mxList = RuleExeUtil.getDeviceList(loadLineLoadSwitch, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
										 
										for(PowerDevice mx : mxList){
											mxName = "，热备用于"+CZPService.getService().getDevName(mx);
											break;
										} 
									}
									
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(loadLineLoadSwitch)+"由冷备用转热备用"+mxName+"/r/n";
								}
							}
						}
					}
					
					if(dev.getPowerStationID().equals("113997365567815890")){//特殊判断
						replaceStr += stationName+"@将35kV母线由冷备用转热备用/r/n";
					}
					
					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+dev.getPowerDeviceID()+"'";
					List<Map<String,String>> zybList =  DBManager.queryForList(sql);

					for(Map<String,String> map : zybList){
						String zybName = StringUtils.ObjToString(map.get("ZYB_NAME"));
						replaceStr += stationName+"@将"+zybName+"由冷备用转运行/r/n";
					}
				}
			}
			
			String sql = "SELECT B.ID,B.UNIT,B.LOWERUNIT,B.SWITCH_NAME,B.DISCONNECTOR_NAME FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER B WHERE B.LINE_ID = (SELECT A.ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND A WHERE A.ID = '"+curDev.getPowerDeviceID()+"')";
			List<Map<String, String>> list = DBManager.queryForList(sql);
		    
			for(Map<String,String> map : list){
				String unit = StringUtils.ObjToString(map.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				String switchname = StringUtils.ObjToString(map.get("SWITCH_NAME"));
				String disconnectorname = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));

				if(!disconnectorname.equals("")){
					replaceStr += unit+"@合上"+lowerunit+disconnectorname+"/r/n";
				}else{
					replaceStr += unit+"@将"+lowerunit+switchname+"由冷备用转热备用/r/n";
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);

				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

				if(xlkgList.size() == 2){
					for(PowerDevice xlkg : xlkgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.isDeviceHadStatus(xlkg, "2", "1")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(xlkg)+"由冷备用转热备用/r/n";
							}
						}
					}
					
					for(PowerDevice xlkg : xlkgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.isDeviceHadStatus(xlkg, "2", "1")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(xlkg)+"由冷备用转热备用/r/n";
							}
						}
					}
				}else{
					for(PowerDevice dev : xlkgList){
						String mxName = "";

						if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
							 
							for(PowerDevice mx : mxList){
								mxName = "，热备用于"+CZPService.getService().getDevName(mx);
								break;
							} 
						}
						
						for(PowerDevice dz : xldzList){
							if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
								replaceStr +=  stationName+"@合上"+CZPService.getService().getDevName(dz)+"/r/n";
							}
						}
						
						if(RuleExeUtil.isDeviceHadStatus(dev, "2", "1")){
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用"+mxName+"/r/n";
						}
						
						sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_LINEZYB WHERE LINE_ID = '"+sourceLineTrans.getPowerDeviceID()+"'";
						List<Map<String,String>> zybList =  DBManager.queryForList(sql);

						for(Map<String,String> map : zybList){
							String zybName = StringUtils.ObjToString(map.get("ZYB_NAME"));
							replaceStr += stationName+"@将"+(int)dev.getPowerVoltGrade()+"kV"+zybName+"由冷备用转运行/r/n";
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
