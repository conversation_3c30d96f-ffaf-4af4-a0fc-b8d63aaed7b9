package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Font;
import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Vector;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.view.ColorTableModel;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

/**
 *
 * <AUTHOR>
 */
public class EquipCheckChoose extends javax.swing.JDialog {
	private List<PowerDevice> equipList = new ArrayList<PowerDevice>();
	private List<PowerDevice> chooseequipList = new ArrayList<PowerDevice>();
	private boolean isCancel = true;
	private boolean isMustSelect = false;

	/** Creates new form EquipCheckChoose */
	public EquipCheckChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, String showMessage) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		if (equipsList != null) {
			this.equipList = equipsList;
			Collections.sort(this.equipList, new Comparator<PowerDevice>() {
				public int compare(PowerDevice pd1, PowerDevice pd2) {
					// TODO Auto-generated method stub
					if(pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
						return 0;
					else if(pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
						return 1;
					else
						return pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName());
				}
			});
		}
		this.initTable(Boolean.FALSE);
		this.setLocationCenter();
		this.setVisible(true);
	}

	public EquipCheckChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, String showMessage,PowerDevice defaultPD) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		if (equipsList != null) {
			this.equipList = equipsList;
			Collections.sort(this.equipList, new Comparator<PowerDevice>() {
				public int compare(PowerDevice pd1, PowerDevice pd2) {
					// TODO Auto-generated method stub
					if(pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
						return 0;
					else if(pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
						return 1;
					else
						return pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName());
				}
			});
		}
		this.initTable(defaultPD);
		this.setLocationCenter();
		this.setVisible(true);
	}
	public EquipCheckChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, String showMessage, boolean isSort,List<PowerDevice> defaultPD) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		if(isSort) {
			if (equipsList != null) {
				this.equipList = equipsList;
				Collections.sort(this.equipList, new Comparator<PowerDevice>() {
					public int compare(PowerDevice pd1, PowerDevice pd2) {
						// TODO Auto-generated method stub
						if(pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
							return 0;
						else if(pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
							return 1;
						else
							return pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName());
					}
				});
			}
		}
		else {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		this.initTable(defaultPD);
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	public EquipCheckChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, String showMessage, boolean isSort) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		if(isSort) {
			if (equipsList != null) {
				this.equipList = equipsList;
				Collections.sort(this.equipList, new Comparator<PowerDevice>() {
					public int compare(PowerDevice pd1, PowerDevice pd2) {
						// TODO Auto-generated method stub
						if(pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
							return 0;
						else if(pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
							return 1;
						else
							return pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName());
					}
				});
			}
		}
		else {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		this.initTable(Boolean.FALSE);
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	public EquipCheckChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, String showMessage, boolean isSort, boolean isMustSelect) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		this.isMustSelect = isMustSelect;
		if(isSort) {
			if (equipsList != null) {
				this.equipList = equipsList;
				Collections.sort(this.equipList, new Comparator<PowerDevice>() {
					public int compare(PowerDevice pd1, PowerDevice pd2) {
						if(pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
							return 0;
						else if(pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
							return 1;
						else
							return pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName());
					}
				});
			}
		}
		else {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		if(showMessage.contains("重合闸")){
			this.initTable(Boolean.TRUE);
		}else{
			this.initTable(Boolean.FALSE);	
		}
		this.setLocationCenter();
		this.setVisible(true);
	}

	public EquipCheckChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, String showMessage, boolean isSort, boolean isMustSelect, boolean isSelectAll) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		this.isMustSelect = isMustSelect;
		
		if(isSort) {
			if (equipsList != null) {
				this.equipList = equipsList;
				Collections.sort(this.equipList, new Comparator<PowerDevice>() {
					public int compare(PowerDevice pd1, PowerDevice pd2) {
						if(pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
							return 0;
						else if(pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
							return 1;
						else
							return pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName());
					}
				});
			}
		}
		else {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		if(isSelectAll){
			this.initTable(Boolean.TRUE);
		}else{
			this.initTable(Boolean.FALSE);	
		}
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jLabel1 = new javax.swing.JLabel();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();
		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jButton3 = new javax.swing.JButton();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		addWindowListener(new java.awt.event.WindowAdapter() {
			public void windowClosed(java.awt.event.WindowEvent evt) {
				windowcloseAction(evt);
			}
		});
		
		Font font = new Font("黑体", Font.PLAIN, 15);
		jLabel1.setFont(font);
		jLabel1.setForeground(new Color(255,0,0));
		
		jLabel1.setText("jLabel1");
		jScrollPane1.setViewportView(jTable1);

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
		jButton1.setToolTipText("\u786e\u5b9a");
		jButton1.setText("\u786e\u5b9a");
		jButton1.setMargin(new java.awt.Insets(1,1,1,1));
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/gc.png"))); // NOI18N
		jButton2.setToolTipText("清空");
		jButton2.setText("清空");
		jButton2.setMargin(new java.awt.Insets(1,1,1,1));
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/all.gif"))); // NOI18N
		jButton3.setToolTipText("\u5168\u9009");
		jButton3.setText("\u5168\u9009");
		jButton3.setMargin(new java.awt.Insets(1,1,1,1));
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(jLabel1,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								295, Short.MAX_VALUE).add(0, 0, 0))
				.add(layout
						.createSequentialGroup()
						.add(jButton3)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jButton2)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 226,
								Short.MAX_VALUE).add(jButton1)
						.addContainerGap())
				.add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1,
						org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 400,
						Short.MAX_VALUE));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(jLabel1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								24,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 22,
								Short.MAX_VALUE)
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.TRAILING)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.LEADING,
												false)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jButton2, 0, 0, Short.MAX_VALUE)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jButton3,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												Short.MAX_VALUE)).add(jButton1))
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jScrollPane1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								320,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//全选
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(Boolean.TRUE);
	}

	//清空
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(Boolean.FALSE);
	}

	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		isCancel = false;
		chooseequipList.clear();
		Object[] temp;
		PowerDevice choosePd = null;
		Vector<Object> rowData1 = jtablemodel.getRowData();
		for (int i = 0; i < rowData1.size(); i++) {
			temp = (Object[]) rowData1.get(i);
			CodeNameModel cnm = (CodeNameModel) temp[2];
			for(PowerDevice equip : equipList) {
				if(equip.getPowerDeviceID().equals(cnm.getCode())) {
					choosePd = equip;
					break;
				}
			}
			if (temp[0].equals(Boolean.TRUE)) {
				chooseequipList.add(choosePd);
			}
		}
		if(isMustSelect == true && chooseequipList.size() == 0) {
			ShowMessage.view("请至少选择一项！");
			return;
		}
		this.setVisible(false);
		this.dispose();
	}

	public void initTable(Boolean ischoose) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		PowerDevice pd = null;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			String stationName = "";
			
			if(!pd.getPowerStationID().equals("")){
				PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
				stationName = CZPService.getService().getDevName(station);
			}else{
				stationName = pd.getPowerStationName();
			}
			
			rowData.add(new Object[] { ischoose, stationName , new CodeNameModel(pd.getPowerDeviceID(), CZPService.getService().getDevName(pd)) });		
		}
		jtablemodel.setRowTitle(new String[] { "选择", "厂站名称", "设备名称" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}

	public void initTable(PowerDevice defaultPD) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		PowerDevice pd = null;
		boolean ischoose =false;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			if(pd.equals(defaultPD)){
				ischoose =true;
			}
			String stationName = "";
			
			if(!pd.getPowerStationID().equals("")){
				PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
				stationName = CZPService.getService().getDevName(station);
			}else{
				stationName = pd.getPowerStationName();
			}
			
			rowData.add(new Object[] { ischoose, stationName , new CodeNameModel(pd.getPowerDeviceID(), CZPService.getService().getDevName(pd)) });			ischoose=false;
		}
		jtablemodel.setRowTitle(new String[] { "选择", "厂站名称", "设备名称" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}
	public void initTable(List<PowerDevice> defaultPD) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		PowerDevice pd = null;
		boolean ischoose =false;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			if(defaultPD.contains(pd)){
				ischoose =true;
			}
			
			String stationName = "";
			
			if(!pd.getPowerStationID().equals("")){
				PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
				stationName = CZPService.getService().getDevName(station);
			}else{
				stationName = pd.getPowerStationName();
			}
			
			rowData.add(new Object[] { ischoose, stationName , new CodeNameModel(pd.getPowerDeviceID(), CZPService.getService().getDevName(pd)) });
			ischoose=false;
		}
		jtablemodel.setRowTitle(new String[] { "选择", "厂站名称", "设备名称" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}
	
	public boolean isCancel() {
		return isCancel;
	}

	public List<PowerDevice> getChooseEquip() {
		
		return this.chooseequipList;
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				EquipCheckChoose dialog = new EquipCheckChoose(
						new javax.swing.JFrame(), true, null, null);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}
	private void windowcloseAction(java.awt.event.WindowEvent evt){
		this.setVisible(false);
	}
	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTable1;
	// End of variables declaration//GEN-END:variables

	private ColorTableModel jtablemodel;

}
