package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

public class SafeCheckUtilQJ {
	public static Object[] init(String dynStr,char beginSpilt,char endSplit) {
		Object[] results = new Object[3];
		List<String> keyFirst=new ArrayList<String>();
		List<String> keyStr=new ArrayList<String>();
		String lastStr="";
		StringBuffer buff = new StringBuffer(dynStr);
		int len = buff.length();
		int start = 0, ptr = 1;
		boolean noMatching=true;
		for (ptr = start; ptr < len; ptr++) {
			if (buff.charAt(ptr) == beginSpilt) {
				if (ptr == 0 || buff.charAt(ptr - 1) != '\\') {
					if (ptr > 2 && buff.charAt(ptr - 2) == '\\') {
						buff.deleteCharAt(ptr - 2);
						--ptr;
					}
					int end = ptr;
					noMatching=false;
					keyFirst.add(buff.substring(start, end));
					ptr += 2;
					for (; ptr < len; ptr++) {
						if (buff.charAt(ptr) == endSplit
								&& buff.charAt(ptr) != '\\') {
							if (buff.charAt(ptr - 2) == '\\') {
								buff.deleteCharAt(ptr - 2);
								--ptr;
							}
							keyStr.add(buff.substring(end+1,ptr));
							start = ptr+1;
							noMatching=true;
							break;
						}
					}
				}
			}
		}
		if(noMatching && ptr<=len){
			lastStr=buff.substring(start,ptr);
		}
		results[0]=keyFirst;
		results[1]=keyStr;
		results[2]=lastStr;
		return results;
	}
	
	public static List<Map<String, String>> getDeviceInfoByWord(List<String> paramsKey,String word){
		List<String> busList = new ArrayList<String>();
		List<String> stationList = new ArrayList<String>();
		List<String> statusList = new ArrayList<String>();
		List<String> deviceTypeList = new ArrayList<String>();
		List<String> deviceNameList = new ArrayList<String>();

		List<Map<String,String>> resultList = new ArrayList<Map<String,String>>(); 
		
		for (int j = 0; j < paramsKey.size(); j++) {
			String tempStr=paramsKey.get(j).toString(); //标签
			
			if(tempStr.equals("厂站名称")){
				String[] stationArr = {"变","站","厂","电场"};
				String station = word;
				
				for(String stationKey : stationArr){
					if(station.contains(stationKey)){
						if(station.contains("主变")){
							if(station.indexOf(stationKey)<station.indexOf("主变")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("站用变")){
							if(station.indexOf(stationKey)<station.indexOf("站用变")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("移动变")){
							if(station.indexOf(stationKey)<station.indexOf("移动变")){
								String stationName = word.substring(0, station.indexOf(stationKey)+1);
								stationList.add(stationName);
								word = word.substring(stationName.length());
							}
						}else if(station.contains("线") || station.contains("回")){
							if(station.indexOf("水泥厂")>0&&station.indexOf("水泥厂")<station.indexOf("线")){
								if(station.indexOf(stationKey)<station.indexOf("水泥厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf("宣厂")>0&&station.indexOf("宣厂")<station.indexOf("线")){
								if(station.indexOf(stationKey)<station.indexOf("宣厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf("烟厂")>0&&station.indexOf("烟厂")<station.indexOf("线")){
								if(station.indexOf(stationKey)<station.indexOf("烟厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf("黄磷厂")>0&&station.indexOf("黄磷厂")<station.indexOf("线")){
								if(station.indexOf(stationKey)<station.indexOf("黄磷厂")){
									String stationName = word.substring(0, station.indexOf(stationKey)+1);
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}else if(station.indexOf(stationKey)<station.indexOf("线") || station.indexOf(stationKey)<station.indexOf("回")){
								if(station.indexOf("站用变")>0){
									if(station.indexOf(stationKey)<station.indexOf("站用变")){
										String stationName = word.substring(0, station.indexOf(stationKey)+1);
										stationList.add(stationName);
										word = word.substring(stationName.length());
									}
								}else{
									String stationName = word.substring(0, station.indexOf(stationKey)+stationKey.length());
									stationList.add(stationName);
									word = word.substring(stationName.length());
								}
							}
						}else{
							String stationName = word.substring(0, station.indexOf(stationKey)+stationKey.length());
							stationList.add(stationName);
							word = word.substring(stationName.length());
						}
						break;
			        }
				}
			}else if(tempStr.equals("设备名称")){
				word = word.replace("及母线设备", "");
				
				if(word.contains("断路器")&&!word.contains("刀闸")){
					if(word.contains("、")){
						String[] arr = word.split("、");
						
						word = word.replace("、", "");
						
						for(String words : arr){
							if(words.contains("断路器")){
								String devName = words.substring(0, words.lastIndexOf("断路器")+3);
								deviceNameList.add(devName);
			                    word = word.replace(devName, "");
							}else{
								String regx = ".*?(主变|线|母|装置|发变组)";
								Pattern compile = Pattern.compile(regx);
				                Matcher matcher = compile.matcher(word);

				                if(matcher.find()){//解析
				                	String group = matcher.group();
				                	
									deviceNameList.add(group);
				                    word = word.replace(group, "");
				                }
							}
						}
					}else{
						String devName = word.substring(0, word.lastIndexOf("断路器")+3);
						deviceNameList.add(devName);
	                    word = word.replace(devName, "");
					}
				}else if(word.contains("接地开关")){
					String devName = word.substring(0, word.lastIndexOf("接地开关")+4);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("刀闸")){
					String devName = word.substring(0, word.lastIndexOf("刀闸")+2);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("隔离开关")){
					String devName = word.substring(0, word.lastIndexOf("隔离开关")+4);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("PT")){
					String devName = word.substring(0, word.lastIndexOf("PT")+2);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("电容器")){
					String devName = word.substring(0, word.lastIndexOf("电容器")+3);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("电抗器")){
					String devName = word.substring(0, word.lastIndexOf("电抗器")+3);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else if(word.contains("站用变")){
					String devName = word.substring(0, word.lastIndexOf("站用变")+3);
					deviceNameList.add(devName);
                    word = word.replace(devName, "");
                    
				}else{
					String regx = ".*?(主变|母|母线|回|线|装置|发变组)";
					Pattern compile = Pattern.compile(regx);
	                Matcher matcher = compile.matcher(word);

	                if(matcher.find()){//解析
						deviceNameList.add(matcher.group());
	                    word = word.replace(matcher.group(), "");
	                }
				}
				deviceTypeList.add("一次设备");
			}else if(tempStr.equals("设备状态")){
				String regx = ".*?(运行|热备用|冷备用|检修)";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
                	String group = matcher.group();
                	statusList.add(group);
                    word = word.substring(word.indexOf(group)+group.length(), word.length());
                    
                }
			}else if(tempStr.equals("二次设备")){
				String regx = ".*?(备自投|重合闸|压板|断路器)";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
                	String group = matcher.group();
                	deviceNameList.add(matcher.group());
                	deviceTypeList.add("二次设备");
                    word = word.substring(word.indexOf(group)+group.length(), word.length());
                }
			}else if(tempStr.equals("所在母线")){
				String regx = ".*?(母)";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
                	String group = matcher.group();
                	busList.add(group);
                    word = word.substring(word.indexOf(group)+group.length(), word.length());
                    
                }
			}
		}
		
		
		for(String deviceName : deviceNameList){
			Map<String,String> resultMap = new HashMap<String, String>();

			resultMap.put("设备名称", deviceName);
			
			for(String stationName : stationList){
				resultMap.put("厂站名称", stationName);
			}
			
			for(String deviceType : deviceTypeList){
				resultMap.put("设备类型", deviceType);
			}
			
			resultList.add(resultMap);
		}
		
		for(String status : statusList){
			Map<String,String> resultMap = new HashMap<String, String>();

			resultMap.put("设备状态", status);
			
			resultList.add(resultMap);
		}
		
		return resultList;
	}	
	
	public static List<RuleBaseMode> setLcmInfo(String stationName,String code){
		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();

		RuleBaseMode rbmInfo = new RuleBaseMode();
		rbmInfo.setCheckout(false);
		rbmList.add(rbmInfo);
		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
		PowerDevice pd = new PowerDevice();
		pd.setPowerStationName(stationName);
		pdlist.add(pd);
		rbmInfo.setPd(pd);
		CheckMessage cm = new CheckMessage();
		cm.setPd(pdlist);
		cm.setBottom(code);
		if(CBSystemConstants.lcm==null){
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		CBSystemConstants.lcm.add(cm);
		return rbmList;
	}
	
	public static double checkSame(String Str_1,String Str_2) {
		Str_1 = Str_1.replace("\n", "");
		
		int Length1=Str_1.length();
		int Length2=Str_2.length();
		
		int Distance=0;
		if (Length1==0) {
			Distance=Length2;
		}
		if(Length2==0)
		{
			Distance=Length1;
		}
		if(Length1!=0&&Length2!=0){
			int[][] Distance_Matrix=new int[Length1+1][Length2+1];
			//编号
			int Bianhao=0;
			for (int i = 0; i <= Length1; i++) {
					Distance_Matrix[i][0]=Bianhao;
					Bianhao++;
			}
			Bianhao=0;
			for (int i = 0; i <=Length2; i++) {
				Distance_Matrix[0][i]=Bianhao;
				Bianhao++;
			}
			
			
			char[] Str_1_CharArray=Str_1.toCharArray();
			char[] Str_2_CharArray=Str_2.toCharArray();
			
			
			for (int i = 1; i <= Length1; i++) {
				for(int j=1;j<=Length2;j++){
					if(Str_1_CharArray[i-1]==Str_2_CharArray[j-1]){
						Distance=0;
					}	
					else{
						Distance=1;
					}
						
						int Temp1=Distance_Matrix[i-1][j]+1;
						int Temp2=Distance_Matrix[i][j-1]+1;
						int Temp3=Distance_Matrix[i-1][j-1]+Distance;
						
						Distance_Matrix[i][j]=Temp1>Temp2?Temp2:Temp1;
						Distance_Matrix[i][j]=Distance_Matrix[i][j]>Temp3?Temp3:Distance_Matrix[i][j];
					
				}
				
			}
			
			Distance=Distance_Matrix[Length1][Length2];
		}
		
		double Aerfa=1-1.0*Distance/(Length1>Length2?Length1:Length2);
		
		return Aerfa;
	}
}
