package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.JDKGXZPE;
import com.tellhow.czp.app.yndd.rule.pe.StationWorkSelectionDialog;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEXLFD implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱线路复电".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			List<Map<String, String>> stationLineList = CommonFunctionPE.getStationLineList(curDev);
			
			
			for(Map<String,String> mapMap :  StationWorkSelectionDialog.deviceList){
				String result = mapMap.get("result");
				String stationName = mapMap.get("stationName");

				if(result.equals("是")){
					replaceStr += stationName+"@核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
				}else{
					if(sourceLineTrans!=null){
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						for(PowerDevice xlsw : xlswList){
							PowerDevice station = CBSystemConstants.getPowerStation(xlsw.getPowerStationID());
							String stationModelName = CZPService.getService().getDevName(station);
							
							if(stationModelName.equals(stationName)){
								replaceStr += stationName+"@核实"+CZPService.getService().getDevName(xlsw)+"间隔未开展任何工作，二次装置已正常投入，设备具备带电条件/r/n";
							}
						}
					}
					
					for(PowerDevice dev : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationModelName = CZPService.getService().getDevName(station);
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						if(stationModelName.equals(stationName)){
							for(PowerDevice xlsw : xlswList){
								replaceStr += stationName+"@核实"+CZPService.getService().getDevName(xlsw)+"间隔未开展任何工作，二次装置已正常投入，设备具备带电条件/r/n";
							}
						}
					}
				}
			}
			
			
			boolean isControl = true;

			for(PowerDevice dev : loadLineTrans){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionPE.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(sourceLineTrans!=null){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionPE.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(stationLineList.size() > 0){
				isControl = false;
			}
			
			for(PowerDevice loadLineTran : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				
				List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							hignVoltMlkgList.add(dev);
						}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							hignVoltXlkgList.add(dev);
						}
					}
				}
				
				List<PowerDevice> tempList = new ArrayList<PowerDevice>();
				
				tempList.addAll(hignVoltXlkgList);
				tempList.addAll(hignVoltMlkgList);
				
				for(PowerDevice dev : tempList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("3")){
				for(Map<String,String> map : stationLineList){
					String unit = StringUtils.ObjToString(map.get("UNIT"));
					String linename = StringUtils.ObjToString(map.get("LINE_NAME"));
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
					String endpointtype = StringUtils.ObjToString(map.get("ENDPOINT_TYPE"));
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
					String switchname = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorname = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));
					String grounddisconnectorname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));

					boolean isccdx =  false;
					
					for(PowerDevice dev : JDKGXZPE.chooseEquips){
						if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
							isccdx = true;
							replaceStr += unit+"@拆除"+lowerunit+linename+"靠线路侧三相短路接地线/r/n";
							break;
						 }
					}
					
					if(!isccdx){
						if(operationkind.equals("直接")){
							if(!grounddisconnectorname.equals("")){
								replaceStr += unit+"@拉开"+lowerunit+grounddisconnectorname+"/r/n";
							}else if(!disconnectorname.equals("")){
								replaceStr += unit+"@拆除"+lowerunit+disconnectorname+"靠线路侧三相短路接地线/r/n";
							}
						}else if(operationkind.equals("许可")){
							if(grounddisconnectorname.equals("")){
								replaceStr += unit+"@拆除"+lowerunit+linename+"靠线路侧三相短路接地线/r/n";
							}else{
								replaceStr += unit+"@拉开"+lowerunit+grounddisconnectorname+"/r/n";
							}
						}else if(operationkind.equals("配合")){
							replaceStr += unit+"@拆除"+lowerunit+linename+"靠线路侧三相短路接地线/r/n";
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchSeparate);

					String dzName = "";
					
					for(PowerDevice dev : dzList){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY)
								||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
							dzName = "线路"+CZPService.getService().getDevNum(dev)+"隔离开关";
						}
					}  
					
					if(JDKGXZPE.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
						replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(sourceLineTrans)+dzName+"靠线路侧三相短路接地线/r/n";
					}else{
						if(RuleExeUtil.getDeviceBeginStatus(jddzList.get(0)).equals("0")){
							replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
						}else{
							replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(sourceLineTrans)+dzName+"靠线路侧三相短路接地线/r/n";
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String voltStationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(loadLineTran, SystemConstants.SwitchFlowGroundLine);
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(loadLineTran, SystemConstants.SwitchSeparate);

					String dzName = "";
					
					for(PowerDevice dev : dzList){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY)
								||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
							dzName = "线路"+CZPService.getService().getDevNum(dev)+"隔离开关";
						}
					}
					
					 if(JDKGXZPE.chooseEquips.contains(loadLineTran)||jddzList.size()==0){
						  replaceStr += voltStationName+"@拆除"+CZPService.getService().getDevName(loadLineTran)+dzName+"靠线路侧三相短路接地线/r/n";
					 }else{
						 if(RuleExeUtil.getDeviceBeginStatus(jddzList.get(0)).equals("0")){
							 replaceStr += voltStationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
						 }else{
							 replaceStr += voltStationName+"@拆除"+CZPService.getService().getDevName(loadLineTran)+dzName+"靠线路侧三相短路接地线/r/n";
						 }
					 }
				}
			}
			
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			
			kgList.addAll(xlkgList);
			kgList.addAll(zbkgList);

			boolean isRunModelThreeTwo = false;
			
			for(PowerDevice dev : kgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					isRunModelThreeTwo = true;
					break;
				}
			}
			
			if(isRunModelThreeTwo){
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制前的要求进行设置/r/n";
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							Collections.reverse(dzList);

							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "普洱地调@遥控合上"+stationName+devname+"/r/n";
									replaceStr += stationName+"@核实"+devname+"处于合上位置/r/n";
								}
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							Collections.reverse(dzList);

							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "普洱地调@遥控合上"+stationName+devname+"/r/n";
									replaceStr += stationName+"@核实"+devname+"处于合上位置/r/n";
								}
							}
						}
					}
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制后的要求进行设置/r/n";
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "普洱地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								
								if(curDev.getPowerVoltGrade() > 110){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									dzList = RuleExeUtil.sortByMXC(dzList);
									
									for(PowerDevice dz : dzList){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"处于合上位置/r/n";
										}
									}
								}
							}else{
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("直接")){
						replaceStr += stationName+"@将"+lowerunit+switchName+"由冷备用转热备用/r/n";
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "普洱地调@遥控合上"+stationName+deviceName+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "普洱地调@遥控用"+stationName+deviceName+"同期合环/r/n";
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> xlkgLoadList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					for(PowerDevice dev : xlkgLoadList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							String deviceName = CZPService.getService().getDevName(dev);
							replaceStr += "普洱地调@遥控用"+stationName+deviceName+"同期合环/r/n";
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("直接")){
						replaceStr += stationName+"@用"+lowerunit+switchName+"同期合环/r/n";
					}
				}
			}else if(isControl){
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					replaceStr += CommonFunctionPE.getLineTVDzOnContent(loadLineTran, stationName);
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					replaceStr += CommonFunctionPE.getLineTVDzOnContent(sourceLineTrans, stationName);
				}
				
				if(RuleExeUtil.isDeviceHadStatus(sourceLineTrans, "2", "1")){
					replaceStr += "普洱地调@执行"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用程序操作/r/n";
					
					/*if(sourceLineTrans!=null){
						for(PowerDevice xlsw : xlkgList){
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(xlsw);
						}
					}
					
					for(PowerDevice dev : loadLineTrans){
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						for(PowerDevice xlsw : xlswList){
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(xlsw);
						}
					}*/
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).endsWith("0")){
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "普洱地调@遥控合上"+stationName+deviceName+"/r/n";
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
						for(PowerDevice dev : xlswList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "普洱地调@遥控合上"+stationName+deviceName+"/r/n";
							}
						}
					}
				}
			}else{
				/*for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					replaceStr += CommonFunctionPE.getLineTVDzOnContent(loadLineTran, stationName);
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					replaceStr += CommonFunctionPE.getLineTVDzOnContent(sourceLineTrans, stationName);
				}*/
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "普洱地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								
//								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
//								replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dzList, stationName);
							}else if(CommonFunctionPE.ifSwitchSeparateHalfControl(dev)){
								replaceStr += CommonFunctionPE.getKnifeHalfControlOnContent(dev);
							}else{
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
								}
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					replaceStr += CommonFunctionPE.getLineTVDzOnContent(loadLineTran, stationName);
				}

				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);

					replaceStr += CommonFunctionPE.getLineTVDzOnContent(sourceLineTrans, stationName);
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

					if(xldzList.size() == 1 && xlswList.size() == 0){
						for(PowerDevice dz : xldzList){
							if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
								replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dz)+"/r/n";
							}
						}
					}else{
						for(PowerDevice dev : xlswList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += "普洱地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								
//									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
//									replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dzList, stationName);
								}else if(CommonFunctionPE.ifSwitchSeparateHalfControl(dev)){
									replaceStr += CommonFunctionPE.getKnifeHalfControlOnContent(dev);
								}else{
									if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
										String deviceName = CZPService.getService().getDevName(dev);
										replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
									}
								}
							}
						}
					}
				}
				
				for(Map<String,String> map : stationLineList){
					String stationName = StringUtils.ObjToString(map.get("UNIT"));
					String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
					String endpointtype = StringUtils.ObjToString(map.get("ENDPOINT_TYPE"));
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));
					String ptdisconnectorName = StringUtils.ObjToString(map.get("PTDISCONNECTOR_NAME"));
					String grounddisconnectorname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));

					if(ptdisconnectorName.contains("10kV")){
						replaceStr += "普洱配调@核实"+stationName+ptdisconnectorName+"处于热备用/r/n";
					}
					
					if(operationkind.equals("直接")){
						if(ptdisconnectorName.contains("站用变")){
							continue;
						}
						
						if(!disconnectorName.equals("")){
							replaceStr += stationName+"@合上"+lowerunit+disconnectorName+"/r/n";
						}else if(!switchName.equals("")){
							replaceStr += stationName+"@将"+lowerunit+switchName+"由冷备用转热备用/r/n";
						}
						
						if(!ptdisconnectorName.equals("") && !ptdisconnectorName.contains("10kV")){
							if(ptdisconnectorName.contains("、")){
								String[] ptdisconnectorNameArr = ptdisconnectorName.split("、");
								
								for(String dzName : ptdisconnectorNameArr){
									replaceStr += stationName+"@将"+lowerunit+dzName+"由冷备用转运行/r/n";
								}
							}else{
								replaceStr += stationName+"@将"+lowerunit+ptdisconnectorName+"由冷备用转运行/r/n";
							}
						}
					}else if(operationkind.equals("许可")){
						if(!ptdisconnectorName.equals("")){
							if(ptdisconnectorName.contains("、")){
								String[] ptdisconnectorNameArr = ptdisconnectorName.split("、");
								
								for(String dzName : ptdisconnectorNameArr){
									if(dzName.contains("隔离开关")){
										replaceStr += stationName+"@核实已合上"+lowerunit+dzName+"/r/n";
									}else{
										replaceStr += stationName+"@核实已将"+lowerunit+dzName+"由冷备用转运行/r/n";
									}
								}
							}else{
								if(ptdisconnectorName.contains("隔离开关")){
									replaceStr += stationName+"@核实已合上"+lowerunit+ptdisconnectorName+"/r/n";
								}else{
									replaceStr += stationName+"@核实已将"+lowerunit+ptdisconnectorName+"由冷备用转运行/r/n";
								}
							}
						}
						
						if(!disconnectorName.equals("")){
							if(disconnectorName.contains("、")){
								String[] disconnectorNameArr = disconnectorName.split("、");
								
								for(String dzName : disconnectorNameArr){
									replaceStr += stationName+"@核实已合上"+lowerunit+dzName+"/r/n";
								}
							}else{
								replaceStr += stationName+"@核实已合上"+lowerunit+disconnectorName+"/r/n";
							}
						}else{
							if(!switchName.equals("")){
								replaceStr += stationName+"@核实已将"+lowerunit+switchName+"由冷备用转热备用/r/n";
							}
						}
					}else if(operationkind.equals("配合")){
						if(!switchName.equals("")){
							if(switchName.contains("、")){
								String[] switchNameArr = switchName.split("、");
								
								for(String kgName : switchNameArr){
									replaceStr += stationName+"@核实已将"+lowerunit+kgName+"由冷备用转热备用/r/n";
								}
							}else{
								replaceStr += stationName+"@核实已将"+lowerunit+switchName+"由冷备用转热备用/r/n";
							}
						}
					}
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).endsWith("0")){
					for(Map<String, String> map : stationLineList) {
						String unit = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
						replaceStr += unit+"@核实"+lowerunit+lineName+"具备送电条件/r/n";
					}
					
					for(Map<String, String> map : stationLineList) {
						String unit = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();

						if(operationkind.equals("配合")){
							if(!switchName.equals("")){
								if(switchName.contains("、")){
									String[] switchNameArr = switchName.split("、");
									
									for(String kgName : switchNameArr){
										replaceStr += unit+"@核实已合上"+lowerunit+kgName+"/r/n";
									}
								}else{
									replaceStr += unit+"@核实已合上"+lowerunit+switchName+"/r/n";
								}
							}
						}
					}
					
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						for(PowerDevice dev : xlswList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionPE.getSwitchOnContent(dev, stationName, station);
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
							
							if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev);
								}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev);
								}
							}
						}
						
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionPE.getHhContent(dev, "普洱地调", stationName);
							}
						}
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								replaceStr += CommonFunctionPE.getSwitchOffContent(dev, stationName, station);
							}
						}
					}
					
					for(Map<String, String> map : stationLineList) {
						String unit = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
						
						if(operationkind.equals("许可")){
							replaceStr += unit+"@通知"+lineName+"已送电/r/n";
						}else if(operationkind.equals("直接")){
							if(!switchName.equals("")){
								replaceStr += unit+"@合上"+lowerunit+switchName+"/r/n";
							}
						}
					}
				}
				
				/*
				 * 站用变操作
				 */
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 

					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+sourceLineTrans.getPowerDeviceID()+"'";
					List<Map<String,String>> zybNameList=DBManager.queryForList(sql);
					    
				    for(Map<String,String> zybNameMap : zybNameList){
			    		String zybName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
			    		replaceStr += stationName+"@将"+zybName+"由冷备用转运行/r/n";
				    }
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+loadLineTran.getPowerDeviceID()+"'";
					List<Map<String,String>> zybNameList=DBManager.queryForList(sql);
					    
				    for(Map<String,String> zybNameMap : zybNameList){
			    		String zybName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
			    		replaceStr += stationName+"@将"+zybName+"由冷备用转运行/r/n";
				    }
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String ptdisconnectorName = StringUtils.ObjToString(map.get("PTDISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("直接")){
						if(ptdisconnectorName.contains("站用变")){
							replaceStr += stationName+"@将"+lowerunit+ptdisconnectorName+"由冷备用转运行/r/n";
						}
					}
				}
			}
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
