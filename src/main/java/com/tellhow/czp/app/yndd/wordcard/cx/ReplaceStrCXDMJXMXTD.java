package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXDMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄单母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName);
			String deviceName = CZPService.getService().getDevName(curDev);
			
			if(curDev.getPowerVoltGrade() == 10){
				replaceStr += "楚雄配调@做好"+stationName+deviceName+"停电准备。/r/n";
				replaceStr += voltStationName+"@做好10kV X号站用变停电准备。/r/n";
			}else{
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true);
				
				for(PowerDevice dev : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
					}
				}
			}
			
			replaceStr += voltStationName+"@退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置。/r/n";
			replaceStr += voltStationName+"@检查"+deviceName+"空载运行。/r/n";
			replaceStr += voltStationName+"@"+deviceName+"由运行转冷备用。/r/n";
		}
		
		return replaceStr;
	}

}
