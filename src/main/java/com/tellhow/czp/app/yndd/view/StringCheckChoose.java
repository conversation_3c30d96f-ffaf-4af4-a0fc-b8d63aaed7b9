package com.tellhow.czp.app.yndd.view;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.mainframe.EachRowEditor;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.view.ColorTableModel;
import czprule.system.ShowMessage;

/**
 *
 * <AUTHOR>
 */
public class StringCheckChoose extends javax.swing.JDialog {
	private List<String> equipList = new ArrayList<String>();
	private List<PowerDevice> stationList = new ArrayList<PowerDevice>();
	private List<Map<String, String>> chooseequipList = new ArrayList<Map<String,String>>();
	private boolean isCancel = true;
	private boolean isMustSelect = false;
	private boolean iskgbh = false;
	private boolean iscz = false;
	/** Creates new form EquipCheckChoose */
	public StringCheckChoose(java.awt.Frame parent, boolean modal,
			List<String> equipsList, String showMessage) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		if (equipsList != null) {
			this.equipList = equipsList;
		}
		this.initTable(Boolean.FALSE);
		this.setLocationCenter();
		this.setVisible(true);
	}

	/** Creates new form EquipCheckChoose */
	public StringCheckChoose(java.awt.Frame parent, boolean modal,
			List<String> equipsList,boolean iskgbh, String showMessage) {
		super(parent, modal);
		this.iskgbh = iskgbh;
		initComponents();
		this.jLabel1.setText(showMessage);
		if (equipsList != null) {
			this.equipList = equipsList;
		}
		this.initTableBh(Boolean.FALSE);
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	
	/** Creates new form EquipCheckChoose */
	public StringCheckChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList,boolean iscz) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText("请选择需要操作备自投的厂站：");
		this.iscz = iscz;
		
		if (stationList != null) {
			this.stationList = equipsList;
		}
		
		this.initTableSt(Boolean.FALSE);
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	private void initTableSt(Boolean false1) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		
		for (int i = 0; i < stationList.size(); i++) {
			PowerDevice pd  = stationList.get(i);
			rowData.add(new Object[] { false1, pd.getPowerStationName() });
		}
		
		jtablemodel.setRowTitle(new String[] { "选择", "厂站名称"});
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
	}

	public StringCheckChoose(java.awt.Frame parent, boolean modal,
			List<String> equipsList, String showMessage,PowerDevice defaultPD) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		if (equipsList != null) {
			this.equipList = equipsList;
		}
		this.initTable(defaultPD);
		this.setLocationCenter();
		this.setVisible(true);
	}
	public StringCheckChoose(java.awt.Frame parent, boolean modal,
			List<String> equipsList, String showMessage, boolean isSort,List<PowerDevice> defaultPD) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		if(isSort) {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		else {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		this.initTable(defaultPD);
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	public StringCheckChoose(java.awt.Frame parent, boolean modal,
			List<String> equipsList, String showMessage, boolean isSort) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		if(isSort) {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		else {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		this.initTable(Boolean.FALSE);
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	public StringCheckChoose(java.awt.Frame parent, boolean modal,
			List<String> equipsList, String showMessage, boolean isSort, boolean isMustSelect) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		this.isMustSelect = isMustSelect;
		if(isSort) {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		else {
			if (equipsList != null) {
				this.equipList = equipsList;
			}
		}
		if(showMessage.contains("重合闸")){
			this.initTable(Boolean.TRUE);
		}else{
			this.initTable(Boolean.FALSE);	
		}
		this.setLocationCenter();
		this.setVisible(true);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jLabel1 = new javax.swing.JLabel();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();
		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jButton3 = new javax.swing.JButton();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		addWindowListener(new java.awt.event.WindowAdapter() {
			public void windowClosed(java.awt.event.WindowEvent evt) {
				windowcloseAction(evt);
			}
		});

		jLabel1.setText("jLabel1");

		jScrollPane1.setViewportView(jTable1);

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
		jButton1.setToolTipText("\u786e\u5b9a");
		jButton1.setText("\u786e\u5b9a");
		jButton1.setMargin(new java.awt.Insets(1,1,1,1));
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/gc.png"))); // NOI18N
		jButton2.setToolTipText("清空");
		jButton2.setText("清空");
		jButton2.setMargin(new java.awt.Insets(1,1,1,1));
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/all.gif"))); // NOI18N
		jButton3.setToolTipText("\u5168\u9009");
		jButton3.setText("\u5168\u9009");
		jButton3.setMargin(new java.awt.Insets(1,1,1,1));
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(jLabel1,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								295, Short.MAX_VALUE).add(0, 0, 0))
				.add(layout
						.createSequentialGroup()
						.add(jButton3)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jButton2)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 226,
								Short.MAX_VALUE).add(jButton1)
						.addContainerGap())
				.add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1,
						org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 400,
						Short.MAX_VALUE));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(jLabel1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								24,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 22,
								Short.MAX_VALUE)
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.TRAILING)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.LEADING,
												false)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jButton2, 0, 0, Short.MAX_VALUE)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jButton3,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												Short.MAX_VALUE)).add(jButton1))
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jScrollPane1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								320,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//全选
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		if(iskgbh){
			this.initTableBh(Boolean.TRUE);
		}else if(iscz){
			this.initTableSt(Boolean.TRUE);
		}else{
			this.initTable(Boolean.TRUE);
		}
	}

	//清空
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(Boolean.FALSE);
	}

	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		isCancel = false;
		chooseequipList.clear();
		Object[] temp;
		
		
		Vector<Object> rowData1 = jtablemodel.getRowData();
		for (int i = 0; i < rowData1.size(); i++) {
			Map<String,String> map = new HashMap<String, String>();
			
			temp = (Object[]) rowData1.get(i);
			
			for(String equip : equipList) {
				if(equip.equals(temp[1])) {
					map.put("备自投", equip);
					
					if(temp.length>2){
						map.put("保护", ((CodeNameModel)temp[2]).getName());
					}
					
					break;
				}
			}
			
			for(PowerDevice equip : stationList) {
				if(equip.getPowerStationName().equals(temp[1])) {
					map.put("厂站", CZPService.getService().getDevName(equip));
					break;
				}
			}
			
			if (temp[0].equals(Boolean.TRUE)) {
				chooseequipList.add(map);
			}
		}
		if(isMustSelect == true && chooseequipList.size() == 0) {
			ShowMessage.view("请至少选择一项！");
			return;
		}
		this.setVisible(false);
		this.dispose();
	}

	public void initTable(Boolean ischoose) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		String pd = null;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			rowData.add(new Object[] { ischoose, pd });
		}
		jtablemodel.setRowTitle(new String[] { "选择", "备自投电压等级" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}

	public void initTableBh(Boolean ischoose) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		
		JComboBox cb = new JComboBox();
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm1 = new CodeNameModel();
		
		cnm1.setCode("0");
		cnm1.setName("存在");
		
		CodeNameModel cnm2 = new CodeNameModel();
		
		cnm2.setCode("1");
		cnm2.setName("不存在");
		
		model.addElement(cnm2);
		model.addElement(cnm1);

		cb.setModel(model);
		
		String pd = null;
		
		EachRowEditor rowEditor = new EachRowEditor(jTable1);
		
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			rowData.add(new Object[] { ischoose, pd , cb.getSelectedItem()});
			rowEditor.setEditorAt(i, new DefaultCellEditor(cb));
		}
		
		
		jtablemodel.setRowTitle(new String[] { "选择", "备自投电压等级","断路器保护"});
		
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		jTable1.getColumnModel().getColumn(2).setCellEditor(rowEditor);

		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}
	
	public void initTable(PowerDevice defaultPD) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		String pd = null;
		boolean ischoose =false;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			if(pd.equals(defaultPD)){
				ischoose =true;
			}
			rowData.add(new Object[] { ischoose, pd });
			ischoose=false;
		}
		jtablemodel.setRowTitle(new String[] { "选择", "备自投电压等级" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}
	public void initTable(List<PowerDevice> defaultPD) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		String pd = null;
		boolean ischoose =false;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			if(defaultPD.contains(pd)){
				ischoose =true;
			}
			rowData.add(new Object[] { ischoose, pd });
			ischoose=false;
		}
		jtablemodel.setRowTitle(new String[] { "选择",  "备自投电压等级" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}
	
	public void initTable(List<PowerDevice> defaultPD,List<PowerDevice> lineList) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		String pd = null;
		boolean ischoose =false;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			if(defaultPD.contains(pd)){
				ischoose =true;
			}
			rowData.add(new Object[] { ischoose, pd });
			ischoose=false;
		}
		jtablemodel.setRowTitle(new String[] { "选择",  "备自投电压等级" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}
	
	public boolean isCancel() {
		return isCancel;
	}

	public List<Map<String,String>> getChooseEquip() {
		return this.chooseequipList;
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				StringCheckChoose dialog = new StringCheckChoose(
						new javax.swing.JFrame(), true, null, null);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}
	private void windowcloseAction(java.awt.event.WindowEvent evt){
		this.setVisible(false);
	}
	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTable1;
	// End of variables declaration//GEN-END:variables

	private ColorTableModel jtablemodel;

}
