/*
 * EquipCheckChoose.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.app.yndd.rule.xsbn;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Vector;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.util.SvgUtil;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.view.ColorTableModel;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

/**
 *
 * <AUTHOR>
 */
public class EquipCheckChoose extends javax.swing.JDialog {
	private List<PowerDevice> equipList = new ArrayList<PowerDevice>();
	private List<PowerDevice> chooseequipList = new ArrayList<PowerDevice>();
	private boolean isCancel = true;
	private boolean isMustSelect = false;
	private RuleBaseMode rbm = null;
	private boolean confirmed = false; // 用于标记用户是否点击了确定按钮

	private OperationCallback callback;	// 回调成员变量

	/** Creates new form EquipCheckChoose */
	public EquipCheckChoose(java.awt.Frame parent, boolean modal,
			List<PowerDevice> equipsList, String showMessage,RuleBaseMode rbm) {
		super(parent, modal);
		initComponents();
		this.jLabel1.setText(showMessage);
		this.rbm = rbm;
		if (equipsList != null) {
			this.equipList = equipsList;
			Collections.sort(this.equipList, new Comparator<PowerDevice>() {
				public int compare(PowerDevice pd1, PowerDevice pd2) {
					// TODO Auto-generated method stub
					if(pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
						return 0;
					else if(pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
						return 1;
					else
						return pd1.getPowerDeviceName().compareTo(pd2.getPowerDeviceName());
				}
			});
		}
		this.initTable(Boolean.FALSE);
		this.setLocationCenter();
		this.setVisible(true);
	}
	
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width),
				(h - this.getSize().height));
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {
		jLabel1 = new javax.swing.JLabel();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();
		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jButton3 = new javax.swing.JButton();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		addWindowListener(new java.awt.event.WindowAdapter() {
			public void windowClosed(java.awt.event.WindowEvent evt) {
				windowcloseAction(evt);
			}
		});

		jLabel1.setText("jLabel1");

		jScrollPane1.setViewportView(jTable1);

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
		jButton1.setToolTipText("确定");
		jButton1.setText("确定");
		jButton1.setMargin(new java.awt.Insets(1,1,1,1));
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/gc.png"))); // NOI18N
		jButton2.setToolTipText("清空");
		jButton2.setText("清空");
		jButton2.setMargin(new java.awt.Insets(1,1,1,1));
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/all.gif"))); // NOI18N
		jButton3.setToolTipText("全选");
		jButton3.setText("全选");
		jButton3.setMargin(new java.awt.Insets(1,1,1,1));
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(jLabel1,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								295, Short.MAX_VALUE).add(0, 0, 0))
				.add(layout
						.createSequentialGroup()
						.add(jButton3)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jButton2)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 226,
								Short.MAX_VALUE).add(jButton1)
						.addContainerGap())
				.add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1,
						org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 400,
						Short.MAX_VALUE));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(jLabel1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								24,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 22,
								Short.MAX_VALUE)
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.TRAILING)
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.LEADING,
												false)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jButton2, 0, 0, Short.MAX_VALUE)
										.add(org.jdesktop.layout.GroupLayout.TRAILING,
												jButton3,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												Short.MAX_VALUE)).add(jButton1))
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jScrollPane1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								320,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	//全选
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable(Boolean.TRUE);
	}

	//清空
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		SvgUtil.clear();
		CBSystemConstants.getSamepdlist().clear();
		
	}

	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		isCancel = false;
		chooseequipList.clear();
		Object[] temp;
		PowerDevice choosePd = null;
		Vector<Object> rowData1 = jtablemodel.getRowData();
		for (int i = 0; i < rowData1.size(); i++) {
			temp = (Object[]) rowData1.get(i);
			CodeNameModel cnm = (CodeNameModel) temp[2];
			for(PowerDevice equip : equipList) {
				if(equip.getPowerDeviceID().equals(cnm.getCode())) {
					choosePd = equip;
					break;
				}
			}
			if (temp[0].equals(Boolean.TRUE)) {
				chooseequipList.add(choosePd);
			}
		}
		if(isMustSelect == true && chooseequipList.size() == 0) {
			ShowMessage.view("请至少选择一项！");
			return;
		}
		this.setVisible(false);
		this.dispose();
		
		DeviceOperate dop = new DeviceOperate();
		DeviceMenuModel dmm = new DeviceMenuModel();
		dmm.setStatecode(CBSystemConstants.getCurRBM().getStateCode());
		dop.execute(rbm.getPd(), dmm);
		CBSystemConstants.isSame=false;
		confirmed = true;
		// 调用回调
		if (callback != null) {
			callback.onOperationComplete(true);
		}
	}

	public void initTable(Boolean ischoose) {
		jtablemodel = new ColorTableModel();
		final Vector<Object> rowData = new Vector<Object>();
		PowerDevice pd = null;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			rowData.add(new Object[] { ischoose, pd.getPowerStationName(), new CodeNameModel(pd.getPowerDeviceID(), CZPService.getService().getDevName(pd)) });
		}
		jtablemodel.setRowTitle(new String[] { "选择", "地点", "设备名称" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(0).setMinWidth(0);
		jTable1.getColumnModel().getColumn(0).setPreferredWidth(0);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
		
		
		new Thread(new Runnable() {
   	 	    public void run() {
   	 	    	while(isCancel) {
   	 	    		jtablemodel.getRowData().clear();
		   	 	    for(int i=0;i<CBSystemConstants.getSamepdlist().size();i++){
		   	 	    	PowerDevice pd = CBSystemConstants.getSamepdlist().get(i);
		   	 	    	
		   				rowData.add(new Object[] { true, pd.getPowerStationName(), new CodeNameModel(pd.getPowerDeviceID(), CZPService.getService().getDevName(pd)) });
		   				jtablemodel.setRowData(rowData);
		   				
		   			}
		   	 	    jTable1.updateUI();
		   	 	    try {
						Thread.sleep(500);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
   	 	    	}
   	 	 }
		}).start();
		
		
	}

	public void initTable(PowerDevice defaultPD) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		PowerDevice pd = null;
		boolean ischoose =false;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			if(pd.equals(defaultPD)){
				ischoose =true;
			}
			rowData.add(new Object[] { ischoose, pd.getPowerStationName(), new CodeNameModel(pd.getPowerDeviceID(), CZPService.getService().getDevName(pd)) });
			ischoose=false;
		}
		jtablemodel.setRowTitle(new String[] { "选择", "地点", "设备名称" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}
	public void initTable(List<PowerDevice> defaultPD) {
		jtablemodel = new ColorTableModel();
		Vector<Object> rowData = new Vector<Object>();
		PowerDevice pd = null;
		boolean ischoose =false;
		for (int i = 0; i < equipList.size(); i++) {
			pd = equipList.get(i);
			if(defaultPD.contains(pd)){
				ischoose =true;
			}
			rowData.add(new Object[] { ischoose, pd.getPowerStationName(), new CodeNameModel(pd.getPowerDeviceID(), CZPService.getService().getDevName(pd)) });
			ischoose=false;
		}
		jtablemodel.setRowTitle(new String[] { "选择", "地点", "设备名称" });
		jtablemodel.setRowData(rowData);
		jTable1.setRowHeight(30);
		jTable1.setModel(jtablemodel);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(50);
		//jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
	}
	
	public boolean isCancel() {
		return isCancel;
	}

	public List<PowerDevice> getChooseEquip() {
		return this.chooseequipList;
	}

	
	private void windowcloseAction(java.awt.event.WindowEvent evt){
		CBSystemConstants.isSame = false;
		// 如果未确认，调用取消回调
		if (callback != null) {
			callback.onOperationComplete(confirmed);
		}
		this.setVisible(false);
	}

	// 设置回调的方法
	public void setCallback(OperationCallback callback) {
		this.callback = callback;
	}

	// 添加方法获取用户是否确认
	public boolean isConfirmed() {
		return confirmed;
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTable1;
	// End of variables declaration//GEN-END:variables

	private ColorTableModel jtablemodel;

	// 回调接口
	public interface OperationCallback {
		void onOperationComplete(boolean confirmed);
	}

}
