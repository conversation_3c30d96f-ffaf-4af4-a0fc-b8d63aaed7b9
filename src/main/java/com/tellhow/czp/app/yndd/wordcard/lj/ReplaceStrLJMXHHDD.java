package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJMXHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江母线合环倒电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC,"", false, true, true, true);
			
			List<PowerDevice> zbzxddzList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);
				
				for(PowerDevice zb : zbList){
					zbzxddzList = RuleExeUtil.getDeviceList(zb, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				}
			}
			
			replaceStr += stationName+"@退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
			
			for(PowerDevice dev : zbzxddzList){
				String zbzxddzName = CZPService.getService().getDevName(dev); 
				
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "丽江地调@遥控合上"+stationName+zbzxddzName+"/r/n";
				}
				replaceStr += stationName+"@确认"+zbzxddzName+"处合上位置/r/n";
			}

			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionLJ.getHhContent(dev,"丽江地调",stationName);
				}
			}
			
			for(PowerDevice dev : zbkgList){
				String zbkgName = CZPService.getService().getDevName(dev); 
				
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionLJ.getHhContent(dev,"丽江地调",stationName);
				}
			}
			
			for(PowerDevice dev : mlkgList){
				String mlkgName = CZPService.getService().getDevName(dev); 

				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "丽江地调@遥控断开"+stationName+mlkgName+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbkgList){
				String zbkgName = CZPService.getService().getDevName(dev); 

				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "丽江地调@遥控断开"+stationName+zbkgName+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzxddzList){
				String zbzxddzName = CZPService.getService().getDevName(dev); 
				
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "丽江地调@遥控拉开"+stationName+zbzxddzName+"/r/n";
					replaceStr += stationName+"@确认"+zbzxddzName+"处拉开位置/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
