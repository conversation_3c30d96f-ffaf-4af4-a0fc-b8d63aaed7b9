package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.lowagie.tools.concat_pdf;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.hh.JDKGXZHH;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHXLFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("红河线路复电".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			List<Map<String, String>> stationLineList = CommonFunctionHH.getStationLineList(curDev);
			
			boolean isControl = true;

			for(PowerDevice dev : loadLineTrans){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionHH.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(sourceLineTrans!=null){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionHH.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(loadLineTrans.size() == 0){
				isControl = false;
			}
			
			if(stationLineList.size() > 0){
				isControl = false;
			}
			
			for(Map<String,String> map : stationLineList){
				String stationName = StringUtils.ObjToString(map.get("UNIT"));
				String linename = StringUtils.ObjToString(map.get("LINE_NAME"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				String endpointkind = StringUtils.ObjToString(map.get("ENDPOINT_KIND"));
				String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
				String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
				String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));
				
				if(curDev.getPowerVoltGrade() == 35){
					if(operationkind.equals("许可")){
						if(stationName.equals("输电管理所")){
							replaceStr += stationName+"@核实"+switchName+"引流线已解断/r/n";
							continue;
						}
					}
				}
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("3")){
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					  
					if(JDKGXZHH.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
						 replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(curDev)+"线路侧三相接地线/r/n";
					}else{
						 replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
					}
				}
				
				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
						
					if(jddzList != null){
						 if(JDKGXZHH.chooseEquips.contains(dev)||jddzList.size()==0){
							  replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(curDev)+"线路侧三相接地线/r/n";
						 }else{
							  replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
						 }
					}
				}
				
				String preStationName = "";
				
				for(Map<String,String> stationLineMap : stationLineList){
					String unit = StringUtils.ObjToString(stationLineMap.get("UNIT"));
					String linename = StringUtils.ObjToString(stationLineMap.get("LINE_NAME"));
					String switchName = StringUtils.ObjToString(stationLineMap.get("SWITCH_NAME"));
					String lowerunit = StringUtils.ObjToString(stationLineMap.get("LOWERUNIT"));
					String grounddisconnectorname = StringUtils.ObjToString(stationLineMap.get("GROUNDDISCONNECTOR_NAME"));
					
					if(unit.equals("输电管理所")){
						continue;
					}

					if(preStationName.equals("")){
						preStationName = unit;
					}else if(preStationName.equals(unit)){
						continue;
					}
					
					boolean isccdx =  false;
					
					for(PowerDevice dev : JDKGXZHH.chooseEquips){
						if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
							isccdx = true;
							replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧三相接地线/r/n";
							break;
						 }
					}
					
					if(!isccdx){
						if(!grounddisconnectorname.equals("")){
							replaceStr += unit+"@拉开"+lowerunit+grounddisconnectorname+"/r/n";
						}else{
							replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧三相接地线/r/n";
						}
					}
				}
			}
			
			for(Map<String,String> map : stationLineList){
				String stationName = StringUtils.ObjToString(map.get("UNIT"));
				String linename = StringUtils.ObjToString(map.get("LINE_NAME"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				String endpointkind = StringUtils.ObjToString(map.get("ENDPOINT_KIND"));
				String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
				String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
				String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));

				if(stationName.equals("110kV挖窖河二级电站")){
					replaceStr += stationName+"@核实"+lowerunit+switchName+"在分闸位置/r/n";
					replaceStr += stationName+"@核实"+lowerunit+disconnectorName+"在拉开位置/r/n";
					replaceStr += stationName+"@核实"+linename+"具备送电条件/r/n";
					continue;
				}
				
				if(stationName.equals("输电管理所")){
					continue;
				}
				
				if(curDev.getPowerVoltGrade() == 110){
					if(endpointkind.equals("牵引变")){
						if(disconnectorName.contains("、")){
							String[] disconnectorNameArr = disconnectorName.split("、");
							
							for(String str : disconnectorNameArr){
								replaceStr += stationName+"@核实"+lowerunit+str+"在拉开位置/r/n";
							}
						}else{
							replaceStr += stationName+"@核实"+lowerunit+disconnectorName+"在拉开位置/r/n";
						}
					}else{
						if(operationkind.equals("许可")){
							if(!switchName.equals("")){
								replaceStr += stationName+"@核实"+lowerunit+switchName+"已处冷备用/r/n";
							}else{
								replaceStr += stationName+"@核实"+lowerunit+disconnectorName+"在拉开位置/r/n";
							}
						}
					}
				}else{
					if(endpointkind.equals("带站用变用户")){
						replaceStr += stationName+"@核实"+lowerunit+"35kV#X站用变已处冷备用/r/n";
					}
					
					if(operationkind.equals("许可")){
						if(disconnectorName.equals("")){
							if(switchName.contains("熔断器")){
								replaceStr += stationName+"@核实"+lowerunit+switchName+"在拉开位置/r/n";
							}else{
								replaceStr += stationName+"@核实"+lowerunit+switchName+"已处冷备用/r/n";
							}
						}else{
							if(switchName.equals("")){
								replaceStr += stationName+"@核实"+lowerunit+disconnectorName+"在拉开位置/r/n";
							}else{
								if(switchName.contains("三刀闸")){
									switchName = switchName.replace("（三刀闸）", "");
									replaceStr += stationName+"@核实"+lowerunit+switchName+"已处冷备用/r/n";
								}else{
									replaceStr += stationName+"@核实"+lowerunit+switchName+"在分闸位置/r/n";
								}
								
								if(disconnectorName.contains("、")){
									String[] dzNameArr = disconnectorName.split("、");
									
									for(String dzName : dzNameArr){
										replaceStr += stationName+"@核实"+lowerunit+dzName+"在拉开位置/r/n";
									}
								}else{
									replaceStr += stationName+"@核实"+lowerunit+disconnectorName+"在拉开位置/r/n";
								}
							}
						}
					}
				}
				
				String checkLine = stationName+"@核实"+linename+"具备送电条件/r/n";
				
				if(!replaceStr.contains(checkLine)){
					replaceStr += checkLine;
				}
			}
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			
			kgList.addAll(xlkgList);
			kgList.addAll(zbkgList);

			boolean isRunModelThreeTwo = false;
			
			for(PowerDevice dev : kgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					isRunModelThreeTwo = true;
					break;
				}
			}
			
			if(isRunModelThreeTwo){
				String lineName = CZPService.getService().getDevName(sourceLineTrans);

				if(RuleExeUtil.getDeviceBeginStatus(sourceLineTrans).equals("2") || RuleExeUtil.getDeviceBeginStatus(sourceLineTrans).equals("3")){
					replaceStr += "红河地调@执行"+lineName+"由冷备用转热备用程序操作/r/n";
				}

				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);

					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "红河地调@遥控合上"+stationName+deviceName+"/r/n";
							}
						}
					}

					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionHH.getHhContent(dev, "红河地调", stationName);
							}
						}
					}
				}

				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> xlkgLoadList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);

					if(xlkgLoadList.size() == 2){
						for(PowerDevice dev : xlkgLoadList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += "红河地调@遥控合上"+stationName+deviceName+"/r/n";
								}
							}
						}

						for(PowerDevice dev : xlkgLoadList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionHH.getHhContent(dev, "红河地调", stationName);
								}
							}
						}
					}else{
						for(PowerDevice dev : xlkgLoadList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "红河地调@遥控合上"+stationName+deviceName+"/r/n";
							}
						}
					}
				}
				if(loadLineTrans.size() > 0){
				}
				else{
					
					
					
					
				}
			}else if(isControl){
				if(RuleExeUtil.isDeviceHadStatus(sourceLineTrans, "2", "1")){
					StringBuffer sbf = new StringBuffer();

					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						stationName = StringUtils.killVoltInDevName(stationName);
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);

						for(PowerDevice xlsw : xlswList){
							if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
								List<PowerDevice> mxList = new ArrayList<PowerDevice>();

								for(PowerDevice dz : dzList){
									if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
										}
									}
								}

								String mxNum = "";

								for(PowerDevice mx : mxList){
									mxNum = CZPService.getService().getDevNum(mx);
								}

								sbf.append(stationName+CZPService.getService().getDevNum(xlsw)+"断路器上"+mxNum+"母，");
							}
						}
					}

					for(PowerDevice dev : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						stationName = StringUtils.killVoltInDevName(stationName);
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

						for(PowerDevice xlsw : xlswList){
							if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
								List<PowerDevice> mxList = new ArrayList<PowerDevice>();

								for(PowerDevice dz : dzList){
									if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
										}
									}
								}

								String mxNum = "";

								for(PowerDevice mx : mxList){
									mxNum = CZPService.getService().getDevNum(mx);
								}

								sbf.append(stationName+CZPService.getService().getDevNum(xlsw)+"断路器上"+mxNum+"母，");
							}
						}
					}

					String remark = "";

					if(sbf.length() > 0){
						if(sbf.toString().endsWith("，")){
							sbf = sbf.delete(sbf.length()-1, sbf.length());
						}

						remark = "（备注："+sbf.toString()+"）";
					}

					replaceStr += "红河地调@执行"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用程序操作"+remark+"/r/n";
				}

				if(RuleExeUtil.getDeviceEndStatus(curDev).endsWith("0")){
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);

						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "红河地调@遥控合上"+stationName+deviceName+"对线路充电/r/n";
							}
						}
					}

					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);

						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();

						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());

						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev2 = it2.next();

							if(dev2.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev2);
								}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev2);
								}
							}
						}

						List<PowerDevice> tempList = new ArrayList<PowerDevice>();

						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);

						for(PowerDevice dev : tempList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionHH.getHhContent(dev, "红河地调", stationName);
							}
						}

						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "红河地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}

					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);

						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();

						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());

						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();

							if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev);
								}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev);
								}
							}
						}

						List<PowerDevice> tempList = new ArrayList<PowerDevice>();

						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);

						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								replaceStr += stationName+"@按当前运行方式投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
					}
				}
			}else{
				if(RuleExeUtil.isDeviceHadStatus(sourceLineTrans, "2", "1")){
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						if(curDev.getPowerVoltGrade() == 35){
							String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+sourceLineTrans.getPowerDeviceID()+"'";
							List<Map<String,String>> zybList = DBManager.queryForList(sql);
							
							for(Map<String,String> zybMap : zybList){
								replaceStr += stationName+"@将"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"由冷备用转运行/r/n";
								break;
							}
						}
						
						for(PowerDevice dev : xlswList){
							String mxName = "";
							
							if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
								List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
								
								for(PowerDevice mx : mxList){
									mxName = "联"+CZPService.getService().getDevName(mx);
									break;
								}
							}
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(curDev.getPowerVoltGrade() == 35){
								List<PowerDevice> linedzList = new ArrayList<PowerDevice>();
								
								for(PowerDevice dz : dzList){
									List<PowerDevice> dz3List = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);
									
									for(PowerDevice dz3 : dz3List){
										if(dz3.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
											linedzList.add(dz3);
											break;
										}
									}
								}
								
								replaceStr += CommonFunctionHH.getKnifeOnContent(linedzList, stationName);
							}
							
							String deviceName = CZPService.getService().getDevName(dev);
							
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								if(CommonFunctionHH.ifSwitchSeparateControl(dev)){
									replaceStr += "红河地调@执行"+stationName+deviceName+"由冷备用转"+mxName+"热备用程序操作/r/n";
									replaceStr += CommonFunctionHH.getKnifeOnCheckContent(dzList, stationName);
								}else{
									if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
										if(curDev.getPowerVoltGrade() == 35){
											if(dzList.size() == 1){
												replaceStr += CommonFunctionHH.getKnifeOnContent(dzList, stationName);
											}else{
												replaceStr += stationName+"@将"+deviceName+"由冷备用转"+mxName+"热备用/r/n";
											}
										}else{
											replaceStr += stationName+"@将"+deviceName+"由冷备用转"+mxName+"热备用/r/n";
										}
									}
								}
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						if(curDev.getPowerVoltGrade() == 35){
							String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+loadLineTran.getPowerDeviceID()+"'";
							List<Map<String,String>> zybList = DBManager.queryForList(sql);
							
							for(Map<String,String> zybMap : zybList){
								replaceStr += stationName+"@将"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"由冷备用转运行/r/n";
								break;
							}
						}
						
						for(PowerDevice dev : xlswList){
							String mxName = "";
							
							if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
								List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
								
								for(PowerDevice mx : mxList){
									mxName = "联"+CZPService.getService().getDevName(mx);
									break;
								}
							}

							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(curDev.getPowerVoltGrade() == 35){
								List<PowerDevice> linedzList = new ArrayList<PowerDevice>();
								
								for(PowerDevice dz : dzList){
									List<PowerDevice> dz3List = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);
									
									for(PowerDevice dz3 : dz3List){
										if(dz3.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
											linedzList.add(dz3);
											break;
										}
									}
								}
								
								replaceStr += CommonFunctionHH.getKnifeOnContent(linedzList, stationName);
							}
							
							String deviceName = CZPService.getService().getDevName(dev);
							
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								if(CommonFunctionHH.ifSwitchSeparateControl(dev)){
									replaceStr += "红河地调@执行"+stationName+deviceName+"由冷备用转"+mxName+"热备用程序操作/r/n";
									replaceStr += CommonFunctionHH.getKnifeOnCheckContent(dzList, stationName);
								}else{
									if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
										if(curDev.getPowerVoltGrade() == 35){
											if(dzList.size() == 1){
												replaceStr += CommonFunctionHH.getKnifeOnContent(dzList, stationName);
											}else{
												replaceStr += stationName+"@将"+deviceName+"由冷备用转"+mxName+"热备用/r/n";
											}
										}else{
											replaceStr += stationName+"@将"+deviceName+"由冷备用转"+mxName+"热备用/r/n";
										}
									}
								}
							}
						}
					}
					
					for(Map<String, String> map : stationLineList) {
						String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
						String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();
						String endpointkind = StringUtils.ObjToString(map.get("ENDPOINT_KIND")).trim();
						
						if(operationkind.equals("直接")){
							if(!disconnectorName.equals("")){
								replaceStr += stationName+"@合上"+lowerunit+disconnectorName+"/r/n";
							}
							
							if(!stationName.contains("羊岔街风电场")){
								replaceStr += stationName+"@将"+lowerunit+switchName+"由冷备用转热备用/r/n";
							}
						}else if(operationkind.equals("许可")){
							
						}else if(operationkind.equals("配合")){
							
						}
					}
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).endsWith("0")){
					if(sourceLineTrans!=null){
						PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station); 
						List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						for(PowerDevice dev : xlswList){
							if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += "红河地调@遥控合上"+stationName+deviceName+"对线路充电/r/n";
								}
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev2 = it2.next();
							
							if(dev2.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev2);
								}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev2);
								}
							}
						}
						
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);
						
						for(PowerDevice dev : tempList){
							if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionHH.getHhContent(dev, "红河地调", stationName);
								}
							}
						}
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "红河地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
					
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev2 = it2.next();
							
							if(dev2.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev2);
								}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev2);
								}
							}
						}
						
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								replaceStr += stationName+"@按当前运行方式投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
					}
					
					if(curDev.getPowerVoltGrade() == 35){
						for(PowerDevice loadLineTran : loadLineTrans){
							PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
							String stationName = CZPService.getService().getDevName(station);
							
							if(station.getPowerVoltGrade() == 35){
								String bztContent = stationName+"@按当前运行方式投入"+(int)loadLineTran.getPowerVoltGrade()+"kV备自投装置/r/n";

								if(!replaceStr.contains(bztContent)){
									replaceStr += bztContent;
								}
							}
						}
					}
					
					for(Map<String, String> map : stationLineList) {
						String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
						String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
						String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
						String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
						String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
						String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();
						String endpointkind = StringUtils.ObjToString(map.get("ENDPOINT_KIND")).trim();
						
						if(stationName.equals("110kV挖窖河二级电站")){
							continue;
						}
						
						if(operationkind.equals("直接")){
							replaceStr += stationName+"@合上"+lowerunit+switchName+"/r/n";
						}
						
						if(curDev.getPowerVoltGrade() == 110){
							if(endpointkind.equals("水电厂")||endpointkind.equals("风电厂")||endpointkind.equals("光伏电站")||endpointkind.equals("单线路用户")){
								if(!lowerunit.equals("")){
									replaceStr += "红河地调@核实已摘除"+lowerunit+"全站停电检修牌/r/n";
								}else{
									replaceStr += "红河地调@核实已摘除"+stationName+"全站停电检修牌/r/n";
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String unit = StringUtils.ObjToString(map.get("UNIT")).trim();
					String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
					String checkLine = "";
					
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
						checkLine = unit+"@通知"+lineName+"已处冷备用/r/n";
					}else if(RuleExeUtil.getDeviceEndStatus(curDev).equals("1")){
						checkLine = unit+"@通知"+lineName+"已处热备用/r/n";
					}else if(RuleExeUtil.getDeviceEndStatus(curDev).equals("0")){
						checkLine = unit+"@通知"+lineName+"已处运行/r/n";
					}
					
					if(!replaceStr.contains(checkLine)){
						replaceStr += checkLine;
					}
				}
			}
		}
		
		return replaceStr;
	}

}
