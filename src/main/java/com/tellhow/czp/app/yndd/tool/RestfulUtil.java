package com.tellhow.czp.app.yndd.tool;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * restful接口调用工具
 */
public class RestfulUtil {

   /**
    * 连接hlfront
    * @param url：
    * @param param：请求方式
    * @param message：报文
    * @return
    */
   public static String dealCon(String targetUrl, String message) throws IOException {
	   
	   URL url = new URL(targetUrl);
	   HttpURLConnection conn = null;
	   
		conn = (HttpURLConnection) url.openConnection();
		conn.setRequestMethod("POST");
		conn.setDoOutput(true);
		conn.setDoInput(true);
		conn.setUseCaches(false);
		conn.setConnectTimeout(3000);
		conn.setRequestProperty("Connection", "Keep-Alive");
		conn.setRequestProperty("Charset", "UTF-8");
		conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
		conn.setRequestProperty("accept", "application/json;charset=UTF-8");

    	  byte[] writebytes = message.getBytes();
  			conn.setRequestProperty("Content-Length", String.valueOf(writebytes.length));
  			OutputStream os = conn.getOutputStream();
  			os.write(message.getBytes("UTF-8"));
  			os.flush();
  			os.close();

      if (conn.getResponseCode() != 200) {
         throw new RuntimeException(
               "HTTP Request Failed with Error code : "
                     + conn.getResponseCode());
      }
      BufferedReader responseBuffer = new BufferedReader(
            new InputStreamReader((conn.getInputStream())));

      String output = "";
      StringBuffer result = new StringBuffer();
      while ((output = responseBuffer.readLine()) != null) {
         result.append(output);
      }
      conn.disconnect();
      responseBuffer.close();
      return result.toString();
   }
}
