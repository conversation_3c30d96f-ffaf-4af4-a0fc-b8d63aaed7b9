package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEDMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱单母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();

			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			}
			
			if(TicketKindChoose.flag.equals("全部手动")){
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(curDev).equals("2")){
					replaceStr += stationName+"@核实普洱供电局-×××号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
						tempList.addAll(xlkgList);
						tempList.addAll(mlkgList);
					}else{
						tempList.addAll(zbkgList);
						tempList.addAll(mlkgList);
					}
					
					String loaddeviceName = "";
					
					for(PowerDevice dev : tempList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							loaddeviceName = CZPService.getService().getDevName(dev);
							break;
						}
					}
					
					replaceStr += stationName+"@将"+deviceName+"经"+loaddeviceName+"由冷备用转热备用/r/n";
				}else{
					if(stationDev.getPowerVoltGrade() > 35){
						for(PowerDevice dev : zbkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")&&!RuleExeUtil.isDeviceChanged(dev)){
								replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
							}
						}
						
						for(PowerDevice dev : mlkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")&&!RuleExeUtil.isDeviceChanged(dev)){
								replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
							}
						}
						
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")&&!RuleExeUtil.isDeviceChanged(dev)){
								replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
							}
						}
					}else{
						replaceStr += stationName+"@核实"+deviceName+"具备带电条件/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}else{
					for(PowerDevice dev : zbkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
			}else if(TicketKindChoose.flag.equals("全部程序化")){
				List<PowerDevice> tempList = new ArrayList<PowerDevice>();
				
				if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
					tempList.addAll(xlkgList);
					tempList.addAll(mlkgList);
				}else{
					tempList.addAll(zbkgList);
					tempList.addAll(mlkgList);
				}
				
				String loaddeviceName = "";
				
				for(PowerDevice dev : tempList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						loaddeviceName = CZPService.getService().getDevName(dev);
						break;
					}
				}
				
				replaceStr += "普洱地调@执行"+stationName+deviceName+"经"+loaddeviceName+"由冷备用转热备用程序操作/r/n";
				
				for(PowerDevice xlkg : xlkgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlkg, SystemConstants.SwitchSeparate);
					dzList = RuleExeUtil.sortByMXC(dzList);
					
					for(PowerDevice dev : dzList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev,stationName);
						}
					}
				}
				
				for(PowerDevice mlkg : mlkgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(mlkg, SystemConstants.SwitchSeparate);
					dzList = RuleExeUtil.sortByCZMXC(dzList);
					Collections.reverse(dzList);
					
					for(PowerDevice dev : dzList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev,stationName);
						}
					}
				}
				
				List<PowerDevice> ptdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : ptdzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
						if(dev.getPowerDeviceName().contains("901") || dev.getPowerDeviceName().contains("902")){
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev,stationName);
						}
					}
				}
				
				replaceStr += "普洱地调@执行"+stationName+deviceName+"由热备用转运行程序操作/r/n";
			}else{
				List<PowerDevice> ptdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : ptdzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
						if(dev.getPowerDeviceName().contains("901") || dev.getPowerDeviceName().contains("902")){
							replaceStr += "普洱地调@遥控合上"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev,stationName);
						}
					}
				}
				
				for(PowerDevice xlkg : xlkgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlkg, SystemConstants.SwitchSeparate);
					dzList = RuleExeUtil.sortByMXC(dzList);
					
					for(PowerDevice dev : dzList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "普洱地调@遥控合上"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev,stationName);
						}
					}
				}
				
				for(PowerDevice mlkg : mlkgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(mlkg, SystemConstants.SwitchSeparate);
					dzList = RuleExeUtil.sortByCZMXC(dzList);
					Collections.reverse(dzList);
					
					for(PowerDevice dev : dzList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "普洱地调@遥控合上"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev,stationName);						
						}
					}
				}
				
				replaceStr += "普洱地调@执行"+stationName+deviceName+"由热备用转运行程序操作/r/n";
			}
		}
		
		return replaceStr;
	}

}
