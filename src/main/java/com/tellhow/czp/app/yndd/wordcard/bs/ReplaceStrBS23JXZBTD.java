package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBS23JXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山二分之三接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			String maintenance = CommonFunctionBS.getMaintenance(stationName);

			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "保山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						if(stationName.equals(maintenance)){
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}else{
							replaceStr += maintenance+"@将"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}
					}
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
					if(stationName.equals(maintenance)){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处冷备用/r/n";
					}else{
						replaceStr += maintenance+"@核实"+stationName+CZPService.getService().getDevName(dev)+"处冷备用/r/n";
					}
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade() && !dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
					if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						otherzbList.add(dev);
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("1")){
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
				}
				
				replaceStr += "保山地调@执行"+stationName+deviceName+"由运行转热备用程序操作/r/n";
				
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备无异常/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备无异常/r/n";
				}
			}else if(curDev.getDeviceStatus().equals("2")){
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
				}
				
				replaceStr += "保山地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
				
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备无异常/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备无异常/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
