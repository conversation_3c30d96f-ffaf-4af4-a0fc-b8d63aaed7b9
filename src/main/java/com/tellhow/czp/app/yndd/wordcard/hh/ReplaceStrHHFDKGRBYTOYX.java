package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.SwitchJoinExecute;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHFDKGRBYTOYX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河分段开关由热备用转运行".equals(tempStr)){
			CommonFunctionHH cf = new CommonFunctionHH();
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",false, true, true, false);
			List<PowerDevice> kgdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());

			String stationName = CZPService.getService().getDevName(station);
			
			for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
				PowerDevice dev = (PowerDevice)it2.next();
				if(kgdzList.contains(dev))
					it2.remove();
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
						jdzybkgList.add(dev);
					}
				}
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					mlkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : mlkgList){
				if(station.getPowerVoltGrade() == dev.getPowerVoltGrade()||dev.getPowerDeviceID()!=curDev.getPowerDeviceID()){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						tempList.add(dev);
					}
				}
			}
			
			if(SwitchJoinExecute.result.equals("仅操作当前断路器")){
				replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
				replaceStr += "核实"+CZPService.getService().getDevName(curDev)+"运行正常/r/n";
			}else{
				if(tempList.size()>0){
					replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"热备用/r/n";
				}
				
				replaceStr += "投入"+CZPService.getService().getDevName(zbList)+(int)curDev.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(curDev)+"/r/n";
				
				if(jdzybkgList.size()>0&&curDev.getPowerVoltGrade() == 10){
					String num = "";
					
					for(PowerDevice jdzybkg : jdzybkgList){
						String jdbName = jdzybkg.getPowerDeviceName().substring(0, jdzybkg.getPowerDeviceName().indexOf("接地"));
						
						num += CZPService.getService().getDevNum(jdbName)+"、";
					}
					
					if(num.endsWith("、")){
						num = num.substring(0, num.length()-1);
					}
					
					replaceStr += "投入10kV"+num+"接地变保护动作跳"+CZPService.getService().getDevName(curDev)+"/r/n";
				}
				
				for(PowerDevice qtdz : qtdzList){
					replaceStr += "核实"+CZPService.getService().getDevName(qtdz)+"在合闸位置/r/n";
					break;
				}
				
				List<PowerDevice> otherMlkgList = new ArrayList<PowerDevice>();
				
				for(PowerDevice mlkg : mlkgList){
					if(station.getPowerVoltGrade() > mlkg.getPowerVoltGrade() && mlkg.getPowerDeviceID()!=curDev.getPowerDeviceID()){
						otherMlkgList.add(mlkg);
					}
				}
				tempList.clear();
				
				tempList.addAll(otherMlkgList);
				tempList.add(curDev);
				
				for(PowerDevice otherMlkg : otherMlkgList){
					if(RuleExeUtil.getDeviceEndStatus(otherMlkg).equals("0")){
						replaceStr += "退出"+(int)otherMlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
						replaceStr += "投入"+CZPService.getService().getDevName(zbList)+(int)otherMlkg.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(otherMlkg)+"/r/n";
					}
				}
				
				replaceStr += cf.getZbBLTQStrReplace(zbList.get(0));
				
				tempList.clear();
				
				for(PowerDevice dev : mlkgList){
					if(dev.getPowerVoltGrade()<station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							tempList.add(dev);
						}
					}
				}
				
				tempList.add(curDev);
				
				replaceStr += cf.getYcHsStrReplace(tempList, stationName);
				replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"运行正常/r/n";
				
				if(jdzybkgList.size()>0){
					for(PowerDevice dev : mlkgList){
						if(dev.getPowerVoltGrade()==10){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "退出10kV#X接地变小电阻自投切功能/r/n";
							}
						}
					}
				}
			}
		}
		return replaceStr;
	}

}