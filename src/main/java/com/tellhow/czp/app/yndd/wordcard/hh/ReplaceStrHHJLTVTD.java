package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHJLTVTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河计量TV停电术语".equals(tempStr)){
			CommonFunctionHH cf = new CommonFunctionHH();
			List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",true, true, true, true);

			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			PowerDevice tvdz = new PowerDevice();
			
			for(PowerDevice dev : qtdzList){
				if(dev.getPowerDeviceName().contains("计量TV")){
					tvdz = dev;
				}
			}
			
			PowerDevice othertvdz = new PowerDevice();
			
			for(PowerDevice dev : othermxList){
				List<PowerDevice> otherqtdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",true, true, true, true);
				
				for(PowerDevice otherqtdz : otherqtdzList){
					if(otherqtdz.getPowerDeviceName().contains("计量TV")){
						othertvdz = otherqtdz;
					}
				}
			}
			
			ReplaceStrHHZNHHDD hhcz = new ReplaceStrHHZNHHDD();
			
			List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							xlkgList.add(dev);
							break;
						}
					}
				}
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
			
			if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//设备在电源侧
				for(PowerDevice xlkg : xlkgList){
					replaceStr += StringUtils.ObjToString(hhcz.strReplace("红河站内合环调电", xlkg, xlkg, desc));
				}
				
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML,"",false, true, true, true);

				if(mlkgList.size()>0){
					for(PowerDevice mlkg : mlkgList){
						if(mlkg.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							replaceStr += "核实"+CZPService.getService().getDevName(mlkg)+"运行正常/r/n";
						}else if(mlkg.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
							if(!RuleExeUtil.isDeviceChanged(mlkg)){
								replaceStr += "核实"+CZPService.getService().getDevName(mlkg)+"运行正常/r/n";
							}
						}
					}
				}
			}else{
				List<PowerDevice> allmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> drkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDR,"", false,true, true, true);

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						allmlkgList.add(dev);
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						zbkgList.add(dev);
					}
					
					if(dev.getDeviceType().equals(SystemConstants.Switch)){
						if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
							jdzybkgList.add(dev);
						}
					}
				}
				
				RuleExeUtil.swapLowDeviceList(allmlkgList);
				
				List<PowerDevice> tempList = new ArrayList<PowerDevice>(); 
				
				tempList.addAll(allmlkgList);
				tempList.addAll(zbkgList);
				
				for(Iterator<PowerDevice> it2 = tempList.iterator();it2.hasNext();) {
					PowerDevice src = (PowerDevice)it2.next();
					if(!RuleExeUtil.getDeviceBeginStatus(src).equals("1"))
						it2.remove();
				}
				
				tempList.addAll(drkgList);
				
				if(tempList.size()>0){
					replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"热备用/r/n";
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() < station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
							replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+(int)mlkg.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(mlkg)+"/r/n";
						}
					}
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() == 10){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")&&jdzybkgList.size()>0){
							String jdbnum = "";
							
							for(PowerDevice jdzybkg : jdzybkgList){
								String jdbName = jdzybkg.getPowerDeviceName().substring(0, jdzybkg.getPowerDeviceName().indexOf("接地"));
								
								jdbnum += CZPService.getService().getDevNum(jdbName)+"、";
							}
							
							if(jdbnum.endsWith("、")){
								jdbnum = jdbnum.substring(0, jdbnum.length()-1);
							}
							
							replaceStr += "投入10kV"+jdbnum+"接地变保护动作跳"+CZPService.getService().getDevName(mlkg)+"/r/n";
						}
					}
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() == station.getPowerVoltGrade()&&station.getPowerVoltGrade() > 35){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
							replaceStr += cf.getXzZbListLxbhltxdStrReplace(zbList, "投入");
						}
					}
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() == 10&&curDev.getPowerVoltGrade() == 10){
						replaceStr += "退出10kV小电流接地选线装置/r/n";
						break;
					}
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() < station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
							replaceStr += "退出"+(int)mlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							gycxlkgList.add(dev);
						}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							gycmlkgList.add(dev);
						}
					}
				}
				
				tempList.clear();
				
				tempList.addAll(gycxlkgList);
				tempList.addAll(gycmlkgList);

				List<String>  hsnrList = new ArrayList<String>();
				
				for(PowerDevice dev : tempList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "红河地调@遥控用"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"同期合环/r/n";
						hsnrList.add("核实"+CZPService.getService().getDevName(dev)+"运行正常/r/n");
					}
				}
				
				for(PowerDevice dev : tempList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
						hsnrList.add("核实"+CZPService.getService().getDevName(dev)+"热备用/r/n");
					}
				}
				
				for(String hsnr : hsnrList){
					replaceStr += hsnr;
				}
				
				tempList.clear();
				hsnrList.clear();
				
				for(PowerDevice dev : allmlkgList){
					if(dev.getPowerVoltGrade() < station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							tempList.add(dev);
						}
					}
				}
				
				String stationName = CZPService.getService().getDevName(station);
				
				for(PowerDevice dev : allmlkgList){
					if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "核实"+(int)dev.getPowerVoltGrade()+"kV备自投装置充电且运行正常/r/n";
						}
					}
				}
				
				if(RuleUtil.isTransformerNQ(zbList.get(0))){
					for(PowerDevice mlkg : allmlkgList){
						if(mlkg.getPowerVoltGrade() == station.getPowerVoltGrade()){
							if(RuleExeUtil.isDeviceChanged(mlkg)){
								replaceStr += cf.getZbNqjxBhStrReplace(zbList,gycmlkgList,gycxlkgList);
							}
						}
					}
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() == station.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
							replaceStr += cf.getXzZbZxdTcStrReplace(zbList);
						}
					}
				}
				
				replaceStr += cf.getZbBLTQStrReplace(zbList.get(0));

				if(tempList.size()==1){
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(tempList)+"/r/n";
					hsnrList.add("核实"+CZPService.getService().getDevName(tempList)+"运行正常/r/n");
				}else if(tempList.size()>1){
					replaceStr += "红河地调@遥控依次合上"+stationName+CZPService.getService().getDevName(tempList)+"/r/n";
					hsnrList.add("核实"+CZPService.getService().getDevName(tempList)+"运行正常/r/n");
				}
				
				for(PowerDevice dev : zbkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						hsnrList.add("核实"+CZPService.getService().getDevName(dev)+"热备用/r/n");
					}
				}
				
				for(String hsnr : hsnrList){
					replaceStr += hsnr;
				}
				
				for(PowerDevice mlkg : allmlkgList){
					if(mlkg.getPowerVoltGrade() == 10){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")&&jdzybkgList.size()>0){
							replaceStr += "退出10kV#X接地变小电阻自投切功能/r/n";
						}
					}
				}
			}
			
			if(!othertvdz.getPowerDeviceID().equals("")){
				replaceStr += "将"+CZPService.getService().getDevName(tvdz)+"所供二次负荷转由"+CZPService.getService().getDevName(othertvdz)+"供电/r/n";
			}
			
			replaceStr += "将"+CZPService.getService().getDevName(tvdz)+"由运行转冷备用/r/n";
			
			String mxName = CZPService.getService().getDevName(curDev);
			
			if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				mxName = mxName.replace("母", "组母线");
			}else if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				mxName = mxName.replace("母", "段母线");
			}
			
			replaceStr += "核实"+mxName+"电压互感器"+CZPService.getService().getDevNum(tvdz)+"隔离开关在拉开位置/r/n";
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}