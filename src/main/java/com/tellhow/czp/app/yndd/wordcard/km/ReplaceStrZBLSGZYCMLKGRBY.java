package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.km.OtherTransformChangeMotherLine;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**  

* <p>Description: </p>  
* <AUTHOR>
* @date 2021年9月13日    
*/
public class ReplaceStrZBLSGZYCMLKGRBY implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String result = "";

		if("主变落实高中压侧母联开关热备用".equals(tempStr)) {
			int curvolt = (int)curDev.getPowerVoltGrade();
			int gycvolt = 0;
			int zycvolt = 0;
			int dycvolt = 0;

			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			List<PowerDevice>  dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice>  zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice>  gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice>  xlList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					gycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "high");
					zycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "middle");
					dycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "low");
				}
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(dev.getPowerVoltGrade() == stationDev.getPowerVoltGrade()&&RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							xlList.add(dev);
						}
					}
				}
			}
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if(dev.getDeviceType().equals(SystemConstants.Switch)
						&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dycvolt==dev.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							dycmlkgList.add(dev);
						}
					}else if(zycvolt==dev.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							zycmlkgList.add(dev);
						}
					}else if(gycvolt==dev.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							gycmlkgList.add(dev);
						}
					}
				}
			}
			
			if(gycmlkgList.size()>0&&curvolt!=dycvolt){
				if(OtherTransformChangeMotherLine.map.isEmpty()){
					//result += "云南省调@落实"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(gycmlkgList)+"具备运行条件/r/n";
					result += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(gycmlkgList)+"/r/n";
				}else{
					result += "云南省调@落实220kVXXX变220kV母线与220kVXXX变220kV母线为同期系统/r/n";
					result += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(gycmlkgList)+"/r/n";
					
					if(xlList.size()>0){
						result += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(xlList)+"/r/n";
					}
				}
			}
			
			if(zycmlkgList.size()>0){
				if(OtherTransformChangeMotherLine.map.isEmpty()){
					if(RuleExeUtil.getDeviceBeginStatus(zycmlkgList.get(0)).equals("1")){
						result += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(zycmlkgList.get(0))+"/r/n";
					}
				}
			}
			
			
		}
		if(result.equals("")){
			return null;
		}
		return result;
	}

}
