package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMXFDSMJXDM implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		StringBuffer reBuffer = new StringBuffer();
		StringBuffer reBuffer2 = new StringBuffer();

		if("母线复电双母接线倒母".equals(tempStr)){
			Map<Integer, DispatchTransDevice> dtds = CBSystemConstants.getDtdMap();
			Set<PowerDevice>  kgList = new HashSet<PowerDevice>();
			
			for (Iterator<DispatchTransDevice> iterator = dtds.values().iterator(); iterator.hasNext();) {
				DispatchTransDevice dtd = iterator.next();
				PowerDevice dev = dtd.getTransDevice();
				if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
					List<PowerDevice> temp = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.Switch);
					
					if(temp.size()>0){
						for(PowerDevice pd : temp){
							if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								kgList.add(pd);
							}
						}
					}
				}
			}
			
			List<PowerDevice>  mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine,  SystemConstants.PowerTransformer, true, true, true);
			
			for (Iterator<PowerDevice> iterator = mxList.iterator(); iterator.hasNext();) {
				PowerDevice mx = iterator.next();
				
				if(mx.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					iterator.remove();
				}
			}
			
			List<PowerDevice> yxkfList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykfList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : kgList){
				String state = RuleExeUtil.getStatusNew(dev.getDeviceType(), RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev));
				
				if(state.equals("运行")){
					yxkfList.add(dev);
				}
				
				if(state.equals("热备用")){
					rbykfList.add(dev);
				}
			}
			
			if(yxkfList.size()>0){
				for(PowerDevice yxkf: yxkfList){
					reBuffer.append(CZPService.getService().getDevName(yxkf)+"、");
				}
				
				reBuffer.delete(reBuffer.length()-1, reBuffer.length());
				
				replaceStr += "将"+reBuffer+"由"+CZPService.getService().getDevName(curDev).replace("组", "")+"运行倒至"+CZPService.getService().getDevName(mxList.get(0)).replace("组", "")+"运行/r/n";
			}
			
			
			if(rbykfList.size()>0){
				for(PowerDevice rbykf: rbykfList){
					reBuffer2.append(CZPService.getService().getDevName(rbykf)+"、");
				}
				
				reBuffer2.delete(reBuffer2.length()-1, reBuffer2.length());
				
				replaceStr += "将"+reBuffer2+"由"+CZPService.getService().getDevName(curDev).replace("组", "")+"热备用倒至"+CZPService.getService().getDevName(mxList.get(0)).replace("组", "")+"热备用/r/n";
			}
		}
		return replaceStr;
	}

}
