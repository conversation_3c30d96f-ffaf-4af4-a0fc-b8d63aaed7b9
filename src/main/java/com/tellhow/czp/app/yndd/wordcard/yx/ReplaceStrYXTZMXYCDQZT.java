package com.tellhow.czp.app.yndd.wordcard.yx;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunction;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYXTZMXYCDQZT  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("玉溪通知母线已处当前状态".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.MotherLine, true, true, true);   
			
			for(Iterator<PowerDevice> itor = lineList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(dev.getPowerDeviceName().contains("备用")){
					itor.remove();
				}
			}
			
			for(PowerDevice dev : lineList){
				List<Map<String, String>> stationLineList = CommonFunction.getStationLineList(curDev);
				
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true);

				for(PowerDevice xlkg : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(xlkg).equals("0")){
						for(Map<String,String> map : stationLineList){
							String userStationName = map.get("UNIT");
							
							replaceStr += userStationName+"@通知"+stationName+CZPService.getService().getDevName(dev)+"已处冷备用/r/n";
						}
					}
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
