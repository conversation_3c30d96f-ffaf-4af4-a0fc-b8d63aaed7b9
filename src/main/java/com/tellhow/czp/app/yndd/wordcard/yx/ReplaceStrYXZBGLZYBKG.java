package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXZBGLZYBKG implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪主变关联站用变开关".equals(tempStr)){
			
			List<PowerDevice> lowList =  RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> middleList = RuleExeUtil.getTransformerSwitchMiddle(curDev);

			if(lowList.size()>0){
				List<PowerDevice> mxLowList = RuleExeUtil.getDeviceList(lowList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				if(mxLowList.size()==1){
					List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(mxLowList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchQT, SystemConstants.PowerTransformer, false, true, true, true);
					
					if(zybkgList.size()>0){
						for(PowerDevice dev : zybkgList){
							if(dev.getPowerDeviceName().contains("站用变")&&RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr = CZPService.getService().getDevName(dev);
								return replaceStr;
							}
						}
					}
				}
			}
			
			if(lowList.size()>0){
				List<PowerDevice> mxMidList = RuleExeUtil.getDeviceList(middleList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				if(mxMidList.size()==1){
					List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(mxMidList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchQT, SystemConstants.PowerTransformer, false, true, true, true);

					if(zybkgList.size()>0){
						for(PowerDevice dev : zybkgList){
							if(dev.getPowerDeviceName().contains("站用变")&&RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr = CZPService.getService().getDevName(dev);
								return replaceStr;
							}
						}
					}
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}
	
}
