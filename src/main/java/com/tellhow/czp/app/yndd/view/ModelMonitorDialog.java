package com.tellhow.czp.app.yndd.view;


import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.MouseEvent;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;



public class ModelMonitorDialog extends javax.swing.JDialog {
	private DefaultTableModel dTableModel;
	private JPanel mainPanel;//信息面板
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTableInfo;//信息列表



	public ModelMonitorDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("模型监测");
		setLocationCenter();
		initTable();
	}

	/**
	 * 初始化表格  传入参数关键字
	 */
	
	public void initTable() {
		dTableModel.setRowCount(0);
		String sql = "SELECT TIME, NAME, CONTENT FROM "+CBSystemConstants.opcardUser+"T_A_JHRZ WHERE TYPE = '数据监测'";
		List<Map<String,String>> results = DBManager.queryForList(sql);
		Map<String,String> temp=new HashMap<String, String>();
		
		for (int i = 0; i < results.size(); i++) {
			 temp = results.get(i);
			 String time = StringUtils.ObjToString(temp.get("TIME"));
			 String name = StringUtils.ObjToString(temp.get("NAME"));
			 String content = StringUtils.ObjToString(temp.get("CONTENT"));

			 Object[] rowData = {name,time,content};
			 dTableModel.addRow(rowData);
		 }

		jTableInfo.setModel(dTableModel);
		DefaultTableCellRenderer r  =  new  DefaultTableCellRenderer();   
		r.setHorizontalAlignment(JTextField.CENTER);   
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}


	private void initComponents() {
		getContentPane().setLayout(new BorderLayout());
		this.setSize(800, 250);
		mainPanel =new JPanel();
		getContentPane().add(mainPanel,BorderLayout.CENTER);
		
		dTableModel = new DefaultTableModel(null,new String[] {"模型类型","更新时间","是否异常"}){
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		jTableInfo = new JTable();
		jTableInfo.setModel(dTableModel);
		
		jTableInfo.getColumnModel().getColumn(0).setMinWidth(50);
		jTableInfo.getColumnModel().getColumn(0).setMaxWidth(150);
		jTableInfo.getColumnModel().getColumn(1).setMinWidth(150);
		jTableInfo.getColumnModel().getColumn(1).setMaxWidth(200);
		jTableInfo.setRowHeight(50);
		jScrollPane1 = new JScrollPane(jTableInfo);
		jScrollPane1.setPreferredSize(new Dimension(750,171));
		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));
		mainPanel.add(jScrollPane1,BorderLayout.CENTER);
	}
}
