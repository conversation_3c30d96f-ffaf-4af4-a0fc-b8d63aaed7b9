package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEKGRBYTOLBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱开关热备用转冷备用".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String kgName = CZPService.getService().getDevName(curDev);
			
			if(CommonFunctionPE.ifSwitchSeparateControl(curDev)){
				boolean is35kVMlkg = false;
				
				if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(curDev.getPowerVoltGrade() == 35){
						is35kVMlkg = true;
					}
				}
				
				if(is35kVMlkg){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionPE.getKnifeListOffContent(dzList,stationName);
				}else{
					replaceStr += "普洱地调@执行"+stationName+kgName+"由热备用转冷备用程序操作/r/n";
//					replaceStr += CommonFunctionPE.getKnifeOffCheckContent(curDev);
				}
			}else{
				replaceStr += stationName+"@将"+kgName+"由热备用转冷备用/r/n";
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
