package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNNQJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳内桥接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(curDev);
			
			List<PowerDevice> zbdzList = RuleExeUtil.getTransformerKnifeLoad(curDev);
			List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbdyckgList = new ArrayList<PowerDevice>();
			
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdzList){
				if(dev.getPowerVoltGrade() == lowvolt){
					zbdycdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				if(mxList.size() == 0){
					gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}else{
					for(PowerDevice mx : mxList){
						gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						otherzbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
						otherzbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					replaceStr += CommonFunctionBN.getTrSwitchProtect(dev, stationName);
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@确认"+(int)dev.getPowerVoltGrade()+"kV备自投装置处投入状态/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					replaceStr += CommonFunctionBN.getTrSwitchProtect(dev, stationName);
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@确认"+(int)dev.getPowerVoltGrade()+"kV备自投装置处投入状态/r/n";
				}
			}
			
			for(PowerDevice dev : zxdjddzList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合位/r/n";
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : kfList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
				}
			}

			for(PowerDevice dev : CommonFunctionBN.chargeDeviceByNqZbFdList){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
					replaceStr += CommonFunctionQJ.getCdContent(dev, "曲靖地调", stationName,deviceName);

					for(PowerDevice gycmlkg : gycmlkgList){
						replaceStr += CommonFunctionQJ.getHhContent(gycmlkg, "曲靖地调", stationName);
					}

					for(PowerDevice gycxlkg : zbgyckgList){
						replaceStr += CommonFunctionQJ.getSwitchOffContent(gycxlkg, stationName, station);
					}
				}else{
					replaceStr += CommonFunctionQJ.getCdContent(dev, "曲靖地调", stationName,deviceName);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
//					replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += CommonFunctionBN.getCdOrHhContent(dev, "版纳地调", stationName);
				}
			}
			
			for(PowerDevice dev : zxdjddzList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "版纳地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "版纳地调@遥控用220kVXX变110kV母联112断路器同期合环/r/n";
					replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
//					replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
					replaceStr += CommonFunctionBN.getCdOrHhContent(dev, "版纳地调", stationName);
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
//					replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
					replaceStr += CommonFunctionBN.getCdOrHhContent(dev, "版纳地调", stationName);
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += "版纳地调@遥控断开220kVXX变110kV母联112断路器/r/n";
				}
			}
			
			if(station.getPowerVoltGrade() > 35){
				replaceStr += stationName+"@将"+(int)curDev.getPowerVoltGrade()+"kVX号主变保护定值区由XX区调整至XX区/r/n";
			}
		}
		
		return replaceStr;
	}

}
