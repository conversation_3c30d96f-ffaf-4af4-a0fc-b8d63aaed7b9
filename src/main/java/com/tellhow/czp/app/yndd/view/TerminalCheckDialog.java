package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.BorderFactory;
import javax.swing.DefaultComboBoxModel;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JToolBar;
import javax.swing.JTree;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeNode;
import javax.swing.tree.TreePath;

import org.apache.batik.dom.svg.SVGOMGElement;
import org.apache.batik.gvt.GraphicsNode;
import org.w3c.dom.Element;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.graphicframework.action.impl.ChangeDeviceRectFlashingAction;
import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

public class TerminalCheckDialog extends JDialog {
	private JSplitPane splitPane = new JSplitPane();
	private DefaultTableModel jTable1Model = new DefaultTableModel();
	private JAutoCompleteComboBox jComboBox = new JAutoCompleteComboBox();
	private JTable table = new JTable();
	public TerminalCheckDialog(JFrame mainFrame, boolean model) {
		super(mainFrame,model);
		
		initComponents();
		this.setTitle("厂站连接点检测");
		setLocationCenter();
	}
	
	private void initComponents() {
		this.setSize(800, 600);
		getContentPane().setLayout(new BorderLayout(0, 0));
		
		DefaultMutableTreeNode rootNode = new DefaultMutableTreeNode();   //创建根节点     
        DefaultSimpleNode dsn1 = new DefaultSimpleNode();
		dsn1.setItemCode("厂站");
		dsn1.setItemName("厂站");
		DefaultMutableTreeNode rNode = new DefaultMutableTreeNode(dsn1);
		rootNode.add(rNode);
		
		List<PowerDevice> stationDevList = new ArrayList<PowerDevice>();
		
		String sql = "SELECT DISTINCT D.STATION_ID FROM "+CBSystemConstants.opcardUser+"T_EQUIPINFO A,"+CBSystemConstants.opcardUser+"T_SUBSTATION D,"
				+ ""+CBSystemConstants.opcardUser+"T_VOLTAGELEVEL E WHERE A.EQUIP_ID NOT IN (SELECT C.EQUIP_ID FROM "+CBSystemConstants.opcardUser+"T_C_TERMINAL C) "
						+ "AND A.STATION_ID = D.STATION_ID AND E.VOLTAGE_ID = D.VOLTAGE_ID AND E.VOLTAGE_CODE > 10";
		List<Map<String,String>> stationList = DBManager.queryForList(sql);
		
		for(Map<String,String> map : stationList){
			String stationid = StringUtils.ObjToString(map.get("STATION_ID"));
			PowerDevice station = CBSystemConstants.getPowerStation(stationid);
			stationDevList.add(station);
		}
		
		RuleExeUtil.swapDeviceList(stationDevList);
		Collections.reverse(stationDevList);
		
		HashMap<String, DefaultMutableTreeNode> volNodeMap = new HashMap<String, DefaultMutableTreeNode>();
        for (Iterator<PowerDevice> it = stationDevList.iterator(); it.hasNext();) {
        	PowerDevice station = it.next();
        	
        	DefaultSimpleNode newKey = new DefaultSimpleNode();     //treeMap的序列码
            
        	newKey.setItemCode(station.getPowerDeviceID());
        	newKey.setItemName(station.getPowerDeviceName());
        	newKey.setItemparam1(StringUtils.ObjToString((int)station.getPowerVoltGrade()));

            DefaultMutableTreeNode newNode;   //为每个序号新建一个树节点
            
            newNode = new DefaultMutableTreeNode(newKey);
            
            DefaultMutableTreeNode volNode = null;
            if(volNodeMap.containsKey(newKey.getItemparam1())) {
            	volNode = volNodeMap.get(newKey.getItemparam1());
            }else {
            	 DefaultSimpleNode volDsn = new DefaultSimpleNode();
            	 volDsn.setItemCode(newKey.getItemparam1());
            	 volDsn.setItemName(newKey.getItemparam1());
         		 volNode = new DefaultMutableTreeNode(volDsn);
         		 rNode.add(volNode);
         		 volNodeMap.put(newKey.getItemparam1(), volNode);
            }
            volNode.add(newNode);  
            
        }
		
        JTree tree = new JTree(rootNode);
        
        tree.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    clickTreeNode(simplePath);
                    //缓存解决方法（暂定）
                    
                    JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
                    int n=tabbedPane.getComponentCount();
                    if(n>8){
                    	for(int i=0;i<n;i++){
	            			SVGCanvasPanel selpanel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
	            			boolean isdestry = selpanel.isIsdestry();
	            			if(isdestry==false){
		            			final SVGCanvas canvas = selpanel.getSvgCanvas();
		            		    new Thread(new Runnable() {
		        		        	public void run() {
		        		        		canvas.destroyCanvas();
		        		        		System.gc();
		        		        	}
		    			 	    }).start();
		            		    selpanel.setIsdestry(true);
		            		    break;
	            			}
                    	}
                    }
                }
            }
        });
        
        JToolBar toolBar = new JToolBar();
        toolBar.setLayout(new FlowLayout(FlowLayout.LEFT, 10, 10));
        
        DefaultComboBoxModel model = new DefaultComboBoxModel();
		
		for(PowerDevice dev : stationDevList) {
			CodeNameModel cnm = new CodeNameModel();
			cnm.setCode(dev.getPowerDeviceID());
			cnm.setName(dev.getPowerDeviceName());
			model.addElement(cnm);
		}
		
		jComboBox.setPreferredSize(new Dimension(200,35));
		jComboBox.setBorder(BorderFactory.createLineBorder(new Color(0,173,232)));
		jComboBox.setModel(model);
		jComboBox.setSelectedIndex(-1);
        
        toolBar.add(jComboBox);
        
        JButton findbutton = makeNavigationButton("find","find","查询厂站","查询");
        toolBar.add(findbutton);
        
        JButton clearbutton = makeNavigationButton("clear","clear","清空查询内容","清空");
        toolBar.add(clearbutton);
        
        JButton minimizebutton = makeNavigationButton("minimize","minimize","最小化","最小化");
        toolBar.add(minimizebutton);
        
        JButton maximizebutton = makeNavigationButton("maximize","maximize","最大化","最大化");
        toolBar.add(maximizebutton);
        
        JButton locationbutton = makeNavigationButton("location","location","定位设备位置","定位");
        toolBar.add(locationbutton);
        
        getContentPane().add(toolBar, BorderLayout.NORTH);
        
        tree.setRootVisible(false);
        tree.setToggleClickCount(1);
        tree.setSelectionRow(0);
        
		JScrollPane leftScrollPane = new JScrollPane();

		expandAll(tree, new TreePath(rootNode), true);
		leftScrollPane.setViewportView(tree);
        splitPane.setLeftComponent(leftScrollPane);
        
		JScrollPane rightScrollPane = new JScrollPane();
		
		splitPane.setRightComponent(rightScrollPane);
		
		getContentPane().add(splitPane);
		
		table.setRowHeight(30);
		
		Object[][] tableDate = null;

		jTable1Model =  new DefaultTableModel(tableDate, new String[] {"厂站名称","设备类型","电压等级","设备ID","设备名称"});
		
		table.setModel(jTable1Model);
		
		rightScrollPane.setViewportView(table);
		
		table.getColumnModel().getColumn(0).setMinWidth(130);
		table.getColumnModel().getColumn(0).setMaxWidth(140);
		table.getColumnModel().getColumn(0).setPreferredWidth(130);
		
		table.getColumnModel().getColumn(1).setMinWidth(60);
		table.getColumnModel().getColumn(1).setMaxWidth(70);
		table.getColumnModel().getColumn(1).setPreferredWidth(60);
		
		table.getColumnModel().getColumn(2).setMinWidth(50);
		table.getColumnModel().getColumn(2).setMaxWidth(60);
		table.getColumnModel().getColumn(2).setPreferredWidth(50);
		
		table.getColumnModel().getColumn(3).setMinWidth(0);
		table.getColumnModel().getColumn(3).setMaxWidth(0);
		table.getColumnModel().getColumn(3).setPreferredWidth(0);
	}

	private JButton makeNavigationButton(String imageName,String actionCommand,String toolTipText,String altText){
        //搜索图片
        ImageIcon icon = new ImageIcon(getClass().getResource("/tellhow/icons/"+imageName+".png"));
        //初始化工具按钮
        JButton button=new JButton();
        button.setHorizontalAlignment(SwingConstants.LEFT);
        //设置按钮的命令
        button.setActionCommand(actionCommand);
        //设置提示信息
        button.setToolTipText(toolTipText);
        button.addActionListener(new java.awt.event.ActionListener() {
		    public void actionPerformed(java.awt.event.ActionEvent evt) {
		        jButtonActionPerformed(evt);
		    }
		});
        button.setIcon(icon);
        return button;
    }
	
	public void jButtonActionPerformed(ActionEvent e) {
		if(e.getActionCommand().contentEquals("find")) {
			if(jComboBox.getSelectedItem()==null || jComboBox.getSelectedItem() instanceof java.lang.String){
				return;
			}
			String searchText = ((CodeNameModel) jComboBox.getSelectedItem()).getCode();
			String searchName = ((CodeNameModel) jComboBox.getSelectedItem()).getName();
			
			selectStation(searchText,searchName);
			
			updateTable(searchText,searchName);
		}else if(e.getActionCommand().contentEquals("location")) {
			int rownum = table.getSelectedRow();
			
			String devId = StringUtils.ObjToString(table.getValueAt(rownum, 3));
			String devtype = StringUtils.ObjToString(table.getValueAt(rownum, 1));

			PowerDevice dev = new PowerDevice();
			
			if(devtype.equals("输电线路")){
				dev = CBSystemConstants.getPowerFeeder(devId);
			}else{
				dev = CBSystemConstants.getPowerDevice(devId);
			}
			
			SVGCanvas svgCanvas = SystemConstants.getGuiBuilder().getActivateSVGPanel().getSvgCanvas();
			 
			SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
	    	Element groupElement = resolver.getDeviceGroupElement(dev);
			
			SVGOMGElement gElement = (SVGOMGElement) groupElement;
		    	
	    	int tagScreenX = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getX() + svgCanvas.getWidth()/2;
	    	int tagScreenY = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getY() + svgCanvas.getHeight()/2;
	    	GraphicsNode node = svgCanvas.getUpdateManager().getBridgeContext().getGraphicsNode(gElement);
	    	  // Rectangle2D ddd = node.getTransformedBounds(node.getTransform());

	    	if(node!=null){
	    		Rectangle2D bounds = svgCanvas.getViewBoxTransform().createTransformedShape(node.getBounds()).getBounds();  
	        	int srcScreenX= (int)(bounds.getX()+bounds.getWidth()/2)+(int)svgCanvas.getLocationOnScreen().getX();
	        	int srcScreenY = (int)(bounds.getY()+bounds.getHeight()/2)+(int)svgCanvas.getLocationOnScreen().getY();
	        	int offX = (int)(tagScreenX-srcScreenX);
	        	int offY = (int)(tagScreenY-srcScreenY);
	        	AffineTransform at = svgCanvas.getRenderingTransform();
	        	at.translate(offX/svgCanvas.getRenderingTransform().getScaleX(),  offY/svgCanvas.getRenderingTransform().getScaleY());
	        	svgCanvas.setRenderingTransform(at, true);
	        	
	            //闪烁效果
	        	ChangeDeviceRectFlashingAction action = new ChangeDeviceRectFlashingAction(dev, "3");
	        	action.backexecute();
	    		action.execute();
	    	}else{
	    		ShowMessage.view("当前厂站图上不存在该设备！");
	    	}
		}else if(e.getActionCommand().contentEquals("clear")) {
			jComboBox.setSelectedItem(null);
		}else if(e.getActionCommand().contentEquals("minimize")) {
			this.setSize(800, 100);
		}else if(e.getActionCommand().contentEquals("maximize")) {
			this.setSize(800, 600);
		}
	}
	
	/**
	 * 打开厂站图
	 * @param fileList
	 * @param searchName
	 * @param searchText
	 */
	public void selectStation(String stationID , String stationName){
		String filePath = "";
        String fileName = "";
         
        List<SVGFile> fileList = SystemConstants.getSVGFileByStationID(stationID);
    	if(fileList.size()!=1){
    		 CZPService.getService().filterMap(fileList, stationID);
		}
        if(fileList.size() == 0) {
        	if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive()){
        		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
        	}else{
        		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在" + stationName + "一次接线图！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
        	}
        	return;
        }
        else if(fileList.size() == 1) {
        	filePath = fileList.get(0).getFilePath();
            fileName = fileList.get(0).getFileName();
        }
        else {
        	Object[] options = fileList.toArray(); 
        	int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);         	
        	if(i == -1)
        		return;
        	filePath = fileList.get(i).getFilePath();
            fileName = fileList.get(i).getFileName();
        }

        SvgP svgp=new SvgP(stationID, stationName, fileName, filePath);
        CBSystemConstants.svgps.put(stationID, svgp);
        JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
		int n=tabbedPane.getComponentCount();
		for(int i=0;i<n;i++){
			SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
			String station=sel.getStationID();
			boolean isdestry=true;//sel.isIsdestry();
			if(station.equals(stationID)&&isdestry==true){
				File svgMapFile = new File(filePath);
				sel.loadSvgFile(svgMapFile);
				sel.setIsdestry(false);
			}
		}
		CreatePowerStationToplogy.createSVGPanel(stationID, stationName, fileName, filePath);
	}
	
	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	
	public void expandAll(JTree tree, TreePath parent, boolean expand) {
		TreeNode node = (TreeNode) parent.getLastPathComponent();
		if (node.getChildCount() >= 0) {
			for (Enumeration e = node.children(); e.hasMoreElements();) {
				TreeNode n = (TreeNode) e.nextElement();
				TreePath path = parent.pathByAddingChild(n);
				expandAll(tree, path, expand);
			}
		}
		if (expand) {
			tree.expandPath(parent);
		} else {
			tree.collapsePath(parent);
		}
	}
	
	private void clickTreeNode(TreePath simplePath) {
		if(simplePath==null){
			return;
		}
		
		String stationID = "";
		String stationName = "";
		
		DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
		
		if (lastNode.isLeaf()) {
			DefaultSimpleNode dsn = (DefaultSimpleNode) lastNode.getUserObject();
	           
			stationID = dsn.getItemCode();
			stationName = dsn.getItemName();
			
			selectStation(stationID,stationName);
		}
		
		updateTable(stationID,stationName);
	}
	
	private void updateTable(String stationID,String stationName){
		jTable1Model.getDataVector().clear();
		jTable1Model.fireTableDataChanged();//通知模型更新
		table.updateUI();//刷新表格
		
		List<PowerDevice> tagDevList = new ArrayList<PowerDevice>();
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationID);

		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(dev, "");
				
			if(devList.size() == 0 && dev.getPowerVoltGrade() >= 6){
				tagDevList.add(dev);
			}
		}
		
		RuleExeUtil.swapDeviceList(tagDevList);
		
		for(PowerDevice dev : tagDevList){
			Object[] rowData;
			
			rowData = new Object[]{stationName,SystemConstants.getMapEquipType().get(dev.getDeviceType()),(int)dev.getPowerVoltGrade(),dev.getPowerDeviceID(),dev.getPowerDeviceName(),};
			jTable1Model.addRow(rowData);
		}
	}
}
