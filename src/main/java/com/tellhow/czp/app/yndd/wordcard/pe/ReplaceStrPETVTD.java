package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPETVTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱TV停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> dzList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeQT, "", true, true, true, true);
			
			for(PowerDevice dev : dzList){
				String dzNum = CZPService.getService().getDevNum(dev);
				
				if(dzNum.contains("901") || dzNum.contains("902")){
					if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
						replaceStr += stationName+"@核实"+deviceName+"TV具备停电条件/r/n";
						replaceStr += "普洱地调@遥控拉开"+stationName+deviceName+"TV"+dzNum+"隔离开关/r/n";
						
						if(station.getPowerVoltGrade() >= 220){
							replaceStr += "普洱地调@核实"+stationName+deviceName+"TV"+dzNum+"隔离开关处于拉开位置/r/n";
						}else{
							replaceStr += stationName+"@核实"+deviceName+"TV"+dzNum+"隔离开关处于拉开位置/r/n";
						}
					}
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
