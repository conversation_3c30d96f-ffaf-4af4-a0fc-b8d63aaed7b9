package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.km.JDKGXZKM;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXLLBYTOJXKM  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昆明线路由冷备用转检修".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());

			if(loadLineTrans.size()>0){
				for(PowerDevice dev : loadLineTrans){
					 List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
					
					 if(dev.getPowerStationName().equals("220kV昆明南牵引站")){
						 continue;
					 }
					  
					 if(JDKGXZKM.chooseEquips.contains(dev)||jddzList.size()==0){
						  replaceStr += CZPService.getService().getDevName(CBSystemConstants.getPowerStation(dev.getPowerStationID()))+"@在"+CZPService.getService().getDevName(curDev)+"线路侧装设三相短路接地线"+"/r/n";
					 }else{
						  replaceStr += CZPService.getService().getDevName(CBSystemConstants.getPowerStation(dev.getPowerStationID()))+"@合上"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
					 }
				}
			}
			
			if(sourceLineTrans!=null){
				  List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
				  
				  if(JDKGXZKM.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
					  replaceStr += CZPService.getService().getDevName(CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID()))+"@在"+CZPService.getService().getDevName(curDev)+"线路侧装设三相短路接地线"+"/r/n";
				 }else{
					  replaceStr += CZPService.getService().getDevName(CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID()))+"@合上"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
				 }
			}
			
		}
		
		return replaceStr;
	}

}
