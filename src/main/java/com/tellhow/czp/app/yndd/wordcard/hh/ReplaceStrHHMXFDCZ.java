package com.tellhow.czp.app.yndd.wordcard.hh;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrHHMXFDCZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("红河母线复电操作替换类".equals(tempStr)){
			CommonFunctionHH cf = new CommonFunctionHH();
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String sbName = CZPService.getService().getDevName(curDev); 
			String stationName =  CZPService.getService().getDevName(station);
			
			List<PowerDevice> zbList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();

			PowerDevice curzb = new PowerDevice();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			String ce = "";
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
					
					if(RuleExeUtil.getTransformerSwitchSource(dev).size()==2){
						ce  = "三";
					}else{
						ce = "两";
					}
				}
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					gycmlkgList.add(dev);
				}
			}
			
			for(Iterator<PowerDevice> itor = zbList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(RuleExeUtil.isDeviceChanged(dev)){
					curzb = dev;
				}
			}
			
			if(curDev.getPowerVoltGrade() == 110){
				List<PowerDevice> fdkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

				if(fdkglist.size()==0){//单母不分段
					List<PowerDevice> curzbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
					
					if(curzbkgList.size()>0){
						List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
						List<PowerDevice> zycmxList =  new ArrayList<PowerDevice>();
						List<PowerDevice> zbzyckgList  = new ArrayList<PowerDevice>();
						List<PowerDevice> zbdyckgList  = new ArrayList<PowerDevice>();
						List<PowerDevice> zycmlkgList  = new ArrayList<PowerDevice>();
						List<PowerDevice> dycmlkgList  = new ArrayList<PowerDevice>();
						
						String zbnr = "";
						
						for(PowerDevice zb : zbList){
							zbzyckgList.addAll(RuleExeUtil.getTransformerSwitchMiddle(zb));
							zbdyckgList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
						}
						
						if(zbzyckgList.size()>0){
							zycmlkgList = RuleExeUtil.getDeviceList(zbzyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						
							if(zycmlkgList.size()>0){
								zycmxList = RuleExeUtil.getDeviceList(zycmlkgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
							}
						}
						
						if(zbdyckgList.size()>0){
							dycmlkgList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
							
							if(dycmlkgList.size()>0){
								dycmxList = RuleExeUtil.getDeviceList(dycmlkgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
							}
						}
						
						for(PowerDevice zb : zbList){
							List<PowerDevice> tempList = new ArrayList<PowerDevice>();
							
							tempList.add(zb);
							tempList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
							tempList.addAll(RuleExeUtil.getTransformerSwitchMiddle(zb));
							tempList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
							
							zbnr += CZPService.getService().getDevName(tempList)+"、";
						}
						
						List<PowerDevice> tempList  = new ArrayList<PowerDevice>();

						tempList.addAll(zycmlkgList);
						tempList.addAll(dycmlkgList);
						
						RuleExeUtil.swapDeviceList(zycmxList);
						RuleExeUtil.swapDeviceList(dycmxList);

						replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"、"+CZPService.getService().getDevName(zycmxList)+"及母线设备、"+CZPService.getService().getDevName(dycmxList)+"及母线设备热备用/r/n";
					    replaceStr += "红河配调@核实"+stationName+CZPService.getService().getDevName(dycmxList)+"上其所管辖的所有10kV出线断路器均己转冷备用/r/n";
					    
						replaceStr += cf.getXhxqStrReplace(zbList, "全部拉开");
					    
						List<PowerDevice> allkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSwitchDYC, false,  true, true, true);
						
						
						if(allkgList.size()>0){
							replaceStr += "核实"+zbnr+CZPService.getService().getDevName(allkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备冷备用/r/n";
						}
						
						if(dycmlkgList.size() > 0){
							PowerDevice mlkg = dycmlkgList.get(0);
							replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+(int)mlkg.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(mlkg)+"/r/n";
						}
					
						if(zycmlkgList.size() > 0){
							PowerDevice mlkg = zycmlkgList.get(0);
							replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+(int)mlkg.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(mlkg)+"/r/n";
						}
						
						if(allkgList.size()>0){
							replaceStr += "将"+zbnr+CZPService.getService().getDevName(allkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由冷备用转热备用/r/n";
						}
						
						replaceStr += "核实"+CZPService.getService().getDevName(zbList)+"中性点及其零序保护己投入/r/n";
						
						if(allkgList.size()>0){
							allkgList.addAll(zbzyckgList);
							allkgList.addAll(zbdyckgList);
							
							replaceStr += cf.getYcHsStrReplace(allkgList, stationName);
							
						}
						
						allkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSwitchDYC, false,  true, true, true);
						
						replaceStr += "核实"+zbnr+CZPService.getService().getDevName(curDev)+"母线及母线设备、"+CZPService.getService().getDevName(zycmxList)+"母线及母线设备、"+CZPService.getService().getDevName(dycmxList)+"母线及母线设备、"+CZPService.getService().getDevName(allkgList)+"运行正常/r/n";
						
						for(PowerDevice zb : zbList){
							if(cf.getZbIsJdzybStrReplace(zb)){
								replaceStr +="投入10kV#X接地变小电阻自投切功能/r/n";
								
								for(PowerDevice zbdyckg : zbdyckgList){
									replaceStr +="投入10kV#X接地变保护动作跳"+CZPService.getService().getDevName(dycmlkgList)+"及"+CZPService.getService().getDevName(zbdyckg)+"/r/n";
								}
							}
						}
						
						replaceStr += "投入110kV备自投装置/r/n";
						replaceStr += "投入35kV备自投装置/r/n";
						replaceStr += "投入10kV备自投装置/r/n";
						replaceStr += "投入110kV母差保护动作闭锁110kV备自投装置/r/n";
					}else{
						List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, "", false,  true, true, true);
						List<PowerDevice> mldzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeML, "", false,  true, true, false);
						List<PowerDevice> zlmldzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeML, "", true,  true, true, false);

						List<PowerDevice> tempList = new ArrayList<PowerDevice>();

						tempList.addAll(xlkgList);
						tempList.add(curDev);
						
						RuleExeUtil.swapDeviceListNum(mldzList);
						
						replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"冷备用/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(mldzList)+"在拉开位置/r/n";
						replaceStr += "将"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用/r/n";
						replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(xlkgList)+"/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"运行正常/r/n";
						replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkgList)+"/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"热备用/r/n";
						
						tempList.clear();
						
						for(PowerDevice dev : mldzList){
							if(!zlmldzList.contains(dev)){
								tempList.add(dev);
							}
						}
						
						tempList.addAll(zlmldzList);
						
						replaceStr += "依次合上"+CZPService.getService().getDevName(tempList)+"/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(curDev)+"运行正常/r/n";
						replaceStr += "投入110kV备自投装置/r/n";
						replaceStr += "投入110kV母差保护动作闭锁110kV备自投装置/r/n";
					}
				}else{
					List<PowerDevice> jdzybkgList =  new ArrayList<PowerDevice>();

					List<PowerDevice> xlkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					List<PowerDevice> zbgyckglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
					List<PowerDevice> zbgycmlkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

					for(Iterator<PowerDevice> itor = zbgyckglist.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							itor.remove();
						}
					}					
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					List<PowerDevice> zbjzdyckglist = new ArrayList<PowerDevice>();
					List<PowerDevice> zbzdyckglist = new ArrayList<PowerDevice>();
				    List<PowerDevice> zbzyckglist = new ArrayList<PowerDevice>();
				    List<PowerDevice> zbdyckglist = new ArrayList<PowerDevice>();

			    	zbzyckglist = RuleExeUtil.getTransformerSwitchMiddle(curzb);
					zbdyckglist = RuleExeUtil.getTransformerSwitchLow(curzb);
				    
					zbzdyckglist.addAll(zbzyckglist);
					zbzdyckglist.addAll(zbdyckglist);
					
					for(Iterator<PowerDevice> itor = zbzdyckglist.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
							itor.remove();
						}
					}
					
					if(zbzdyckglist.size()>0){
						tempList.addAll(0,zbzdyckglist);
					}
					
					tempList.addAll(0,zbgyckglist);
					
					if(zbzdyckglist.size()>0){
						tempList.add(0,curzb);
					}
					
					tempList.addAll(zbgycmlkglist);
					
					if(RuleUtil.isTransformerNQ(curzb)){
						replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"、"+CZPService.getService().getDevName(tempList)+"冷备用/r/n";
					}else{
						replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备、"+CZPService.getService().getDevName(tempList)+"冷备用/r/n";
					}
				    
				    if(zbdyckglist.size()>0){
				    	List<PowerDevice> mxList = RuleExeUtil.getDeviceList(zbdyckglist.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

				    	if(mxList.size()>0){
					    	List<PowerDevice> mxkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

					    	if(mxkgList.size()>0){
					    		for(PowerDevice dev : mxkgList){
					    			if(dev.getDeviceType().equals(SystemConstants.Switch)){
										if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
											jdzybkgList.add(dev);
										}
									}
					    		}
					    	}
				    	}
				    }
				    
				    zbjzdyckglist.addAll(zbzyckglist);
				    zbjzdyckglist.addAll(zbdyckglist);
				    
				    for(Iterator<PowerDevice> itor = zbjzdyckglist.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
							itor.remove();
						}
					}
				    
				    if(zbjzdyckglist.size()>0){
				    	zbjzdyckglist.add(0,curzb);
						replaceStr += "核实"+CZPService.getService().getDevName(zbjzdyckglist)+"热备用/r/n";
				    }
					
					replaceStr += cf.getDqZbHbBhTMlkgStrReplace(curzb, "midlow", "投入","停电");
					
					if(RuleUtil.isTransformerNQ(curzb)){
						replaceStr += "将"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"、"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用/r/n";
					}else{
						if(zbList.size() == 3){
							String zbnr = "";
							
							for(PowerDevice zb : zbList){
								List<PowerDevice> tempkgList =  new ArrayList<PowerDevice>();
								
								if(RuleExeUtil.isDeviceChanged(zb)){
									List<PowerDevice> dyckglist =  RuleExeUtil.getTransformerSwitchLow(zb);
									List<PowerDevice> zyckglist =  RuleExeUtil.getTransformerSwitchMiddle(zb);
									List<PowerDevice> gyckglist =  RuleExeUtil.getTransformerSwitchHigh(zb);
									
									tempkgList.add(zb);
									tempkgList.addAll(gyckglist);
									tempkgList.addAll(zyckglist);
									tempkgList.addAll(dyckglist);
									
									zbnr += CZPService.getService().getDevName(tempkgList)+"、";
								}
							}
							
							replaceStr += "将"+zbnr+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由冷备用转热备用/r/n";
						}else{
							replaceStr += "将"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备、"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用/r/n";
						}
					}
					
					List<PowerDevice> zbdzList = RuleExeUtil.getDeviceList(curzb, SystemConstants.SwitchSeparate , "", CBSystemConstants.RunTypeKnifeZBS, "", true, true, true, true);
					
					if(zbdzList.size()>0){
						replaceStr += "合上"+CZPService.getService().getDevName(zbdzList)+"/r/n";
					}
					
					if(zbgyckglist.size()>0){
						List<PowerDevice> mxdzList = RuleExeUtil.getDeviceList(zbgyckglist.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeMX, "", false, true, true, true);
						
						if(mxdzList.size()>0){
							for(PowerDevice mxdz : mxdzList){
								if(!RuleExeUtil.isDeviceChanged(mxdz)){
									replaceStr += "核实"+CZPService.getService().getDevName(mxdz)+"在拉开位置/r/n";
								}
							}
						}
					}
					
					if(xlkglist.size()>0){
						replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(xlkglist)+"/r/n";
						
						if(RuleUtil.isTransformerNQ(curzb)){
							replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"、"+CZPService.getService().getDevName(curzb)+"运行正常/r/n";
						}else{
							replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备运行正常/r/n";
						}
						
						if(zbgycmlkglist.size()>0){
							replaceStr += "红河地调@遥控用"+stationName+CZPService.getService().getDevName(zbgycmlkglist)+"同期合环/r/n";
						}
						
						replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkglist)+"/r/n";
						
						if(zbgycmlkglist.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(zbgycmlkglist)+"运行正常/r/n";
						}
						
						replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"热备用/r/n";
						
					}
					
					
					replaceStr += "投入110kV备自投装置/r/n";
					
					if(!RuleUtil.isTransformerNQ(curzb)){
						replaceStr += "投入110kV母差保护动作闭锁110kV备自投装置/r/n";
					}
					
					replaceStr += cf.getZbBLTQStrReplace(curzb);
					replaceStr += cf.get110kVZbZxdStrReplace(curzb, zbList);
					
				    List<PowerDevice> zbgzdyckglist = new ArrayList<PowerDevice>();
				    List<PowerDevice> zdycmlkglist = new ArrayList<PowerDevice>();
				    List<PowerDevice> dycmlkglist = new ArrayList<PowerDevice>();
				    zbgzdyckglist.addAll(zbgyckglist);
				    zbgzdyckglist.addAll(zbzyckglist);
				    zbgzdyckglist.addAll(zbdyckglist);
				    
					replaceStr += cf.getYcHsStrReplace(zbgzdyckglist, stationName);
					
					if(zbdyckglist.size()>0){
						dycmlkglist = RuleExeUtil.getDeviceList(zbdyckglist.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						zdycmlkglist.addAll(dycmlkglist);
					}
					
					if(zbzyckglist.size()>0){
						List<PowerDevice> zycmlkglist = RuleExeUtil.getDeviceList(zbzyckglist.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						zdycmlkglist.addAll(zycmlkglist);
					}
					

					for(Iterator<PowerDevice> itor = zdycmlkglist.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							itor.remove();
						}
					}
					
					if(zdycmlkglist.size()>0){
						replaceStr += cf.getYcDkStrReplace(zdycmlkglist, stationName);
					}
					
					if(!RuleUtil.isTransformerNQ(curzb)){
						zbgzdyckglist.add(0,curzb);
					}
					
					
					replaceStr += "核实"+CZPService.getService().getDevName(zbgzdyckglist)+"运行正常/r/n";

					if(zdycmlkglist.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(zdycmlkglist)+"热备用/r/n";
					}

					replaceStr += cf.getDqZbHbBhTMlkgDzStrReplace(curzb, "midlow", "复电");
					
					if(jdzybkgList.size()>0){
						replaceStr += "投入10kV#X接地站用变小电阻自投切功能/r/n";
						
						if(dycmlkglist.size()>0&&zbdyckglist.size()>0){
							dycmlkglist.addAll(zbdyckglist);
							replaceStr += "投入10kV#X接地站用变保护动作跳"+CZPService.getService().getDevName(dycmlkglist)+"/r/n";
						}
						replaceStr += "投入10kV#X接地站用变保护动作闭锁10kV备自投装置/r/n";
					}
					
					replaceStr += cf.getXzZbLxbhltxdStrReplace(curzb, "投入");
					
					replaceStr += cf.getXzZbZxdStrReplace(zbList);
					
					if(xlkglist.size()>0){
						if(RuleExeUtil.getDeviceEndStatus(xlkglist.get(0)).equals("0")){
							replaceStr += "红河地调@遥控用"+stationName+CZPService.getService().getDevName(xlkglist)+"同期合环/r/n";
							replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"运行正常/r/n";
							
							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(zbgycmlkglist)+"/r/n";
							replaceStr += "核实"+CZPService.getService().getDevName(zbgycmlkglist)+"热备用/r/n";
							
							replaceStr += "核实110kV备自投装置充电且运行正常/r/n";
						}
					}
					
					replaceStr += cf.getXhxqStrReplace(zbList, "复电");
				}
			}else if(curDev.getPowerVoltGrade() == 35){
				List<PowerDevice> zbkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
				List<PowerDevice> fdkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				replaceStr += cf.getHsdcnrStrReplace(curDev);
				
			    if(fdkglist.size()==0){
			    	List<PowerDevice> allkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSwitchFHC, false, true, true, true);
					
			    	allkgList.addAll(0,zbkglist);
			    	
					if(allkgList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(allkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备冷备用/r/n";
					}
					
					List<PowerDevice> lbykgList = new ArrayList<PowerDevice>();
					
					for(PowerDevice kg : allkgList){
						if(RuleExeUtil.getDeviceBeginStatus(kg).equals("2")){
							lbykgList.add(kg);
						}
					}
					
					if(zbkglist.size()>0){
						zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					}
					
					replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+(int)curDev.getPowerVoltGrade()+"kV侧后备保护动作跳主变三侧断路器/r/n";
					replaceStr += "投入"+CZPService.getService().getDevName(curDev)+"保护装置/r/n";
					replaceStr += "将"+CZPService.getService().getDevName(lbykgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由冷备用转热备用/r/n";
					
					List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
					
					for(PowerDevice kg : allkgList){
						if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
							yxkgList.add(kg);
						}
					}
					
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(yxkgList)+"/r/n";

					replaceStr += "核实"+CZPService.getService().getDevName(yxkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备运行正常/r/n";
					
					replaceStr += cf.getXzZbLxbhltxdStrReplace(curzb, "投入");
			    }else{
					List<PowerDevice> drdkkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, true, true);
					List<PowerDevice> allmxkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					List<PowerDevice> xlkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(Iterator<PowerDevice> itor = xlkglist.iterator();itor.hasNext();){
						PowerDevice xlkg = itor.next();
						
						if(xlkg.getPowerDeviceName().contains("站用变")){
							itor.remove();
						}
					}
					
					List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
					
					for(PowerDevice allmxkg : allmxkgList){
						if(allmxkg.getDeviceType().equals(SystemConstants.Switch)){
							if(allmxkg.getPowerDeviceName().contains("站用变")){
								zybkgList.add(allmxkg);
							}
						}
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(zbkglist);
					tempList.addAll(fdkglist);
					tempList.addAll(xlkglist);
					tempList.addAll(drdkkglist);
					tempList.addAll(zybkgList);
				    
					replaceStr += "核实"+CZPService.getService().getDevName(tempList)+CZPService.getService().getDevName(curDev)+"及母线设备冷备用/r/n";
					
					List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true);
					
					List<PowerDevice> xldzList =  new ArrayList<PowerDevice>();
					
					for(PowerDevice xlkg : xlkgList){
						List<PowerDevice> tempxldzList = RuleExeUtil.getDeviceList(xlkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL, "", false, true, true, true);
						xldzList.addAll(tempxldzList);
					}
					
					if(xldzList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(xldzList)+"在拉开位置/r/n";
					}
					
				    replaceStr += "投入"+CZPService.getService().getDevName(zbList)+(int)curDev.getPowerVoltGrade()+"kV侧后备保护动作跳主变三侧断路器/r/n";
				    
				    replaceStr += "将"+CZPService.getService().getDevName(tempList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由冷备用转热备用/r/n";
				    
				    if(xldzList.size()>0){
						replaceStr += "合上"+CZPService.getService().getDevName(xldzList)+"/r/n";
					}
				    
					if(xlkglist.size()>0){
						if(station.getPowerVoltGrade()==110&&fdkglist.get(0).getDeviceStatus().equals("0")){
							
						}else{
							xlkglist.addAll(0,fdkglist);
						}
						xlkglist.addAll(0,zbkglist);
						
						for(Iterator<PowerDevice> itor = xlkglist.iterator();itor.hasNext();){
							PowerDevice xlkg = itor.next();
							
							if(!RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
								itor.remove();
							}
						}
						
						replaceStr += cf.getYcHsStrReplace(xlkglist, stationName);
					    
						
						replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备运行正常/r/n";
					}
					
					if(station.getPowerVoltGrade()==110&&fdkglist.get(0).getDeviceStatus().equals("0")){
						replaceStr += "核实"+stationName+CZPService.getService().getDevName(gycmlkgList)+"运行正常/r/n";
						
						if(!curzb.getPowerDeviceID().equals("")){
							replaceStr += cf.getZbBLTQStrReplace(curzb);
						}
						replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(fdkglist)+"/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(fdkglist)+"运行正常/r/n";
					}
					
					if(fdkglist.size()>0){
						if(RuleExeUtil.getDeviceEndStatus(fdkglist.get(0)).equals("1")){
							replaceStr += "投入35kV备自投装置/r/n";
							if(station.getPowerVoltGrade() >110){
								replaceStr += "投入110kV#1、#2主变35kV侧后备保护、35kV母差保护动作闭锁35kV备自投装置/r/n";
							}else{
								replaceStr += "投入35kV#1、#2弧光保护动作闭锁35kV备自投装置/r/n";
								replaceStr += "投入110kV#1、#2主变35kV侧后备保护动作闭锁35kV备自投装置/r/n";
							}
						}
					}
					
					replaceStr += cf.getXhxqStrReplace(zbList, "复电");
					
					if(!curzb.getPowerDeviceID().equals("")){
						replaceStr += cf.getXzZbLxbhltxdStrReplace(curzb, "投入");
					}
			    }
			}else if(curDev.getPowerVoltGrade() == 10){
				
				if(station.getPowerVoltGrade() == 110){
					List<PowerDevice> zbkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
					List<PowerDevice> fdkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
					List<PowerDevice> drkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDR, "", false, true, true, true);
					List<PowerDevice> yxdycmlkglist = new ArrayList<PowerDevice>();
							
					if(zbkglist.size()>0){
				    	zbList = RuleExeUtil.getDeviceList(zbkglist.get(0), SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);
				    }
					
					for(PowerDevice fdkg : fdkglist){
						if(RuleExeUtil.getDeviceEndStatus(fdkg).equals("0")){
							yxdycmlkglist.add(fdkg);
						}
					}
					
				    List<PowerDevice> mxkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				    List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
				    List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
				    List<PowerDevice> allczkgList = new ArrayList<PowerDevice>();

					if(mxkgList.size()>0){
			    		for(PowerDevice dev : mxkgList){
			    			if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
									jdzybkgList.add(dev);
								}
								
								if(dev.getPowerDeviceName().contains("站用变")&&!dev.getPowerDeviceName().contains("接地站用变")){
									zybkgList.add(dev);
								}
							}
			    		}
			    	}
					
					replaceStr += "红河地调配网调控组@核实"+stationName+CZPService.getService().getDevName(curDev)+"上其所管辖的所有10kV断路器均在冷备用/r/n";
					
					replaceStr += "核实";
					
					List<PowerDevice> bbzdzList = new ArrayList<PowerDevice>();//可能存在非标准接线刀闸

					List<PowerDevice> bbzmlkgdzList = new ArrayList<PowerDevice>();
					    
				    if(fdkglist.size()>0){
				    	List<PowerDevice> mlkgscList =  RuleExeUtil.getDeviceDirectList(fdkglist.get(0), SystemConstants.SwitchSeparate);
					    List<PowerDevice> mlkgdzList = RuleExeUtil.getDeviceList(fdkglist.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
					    List<PowerDevice> mlkgkgList = RuleExeUtil.getDeviceList(fdkglist.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, false);

					    if(mlkgdzList.size()>2){
					    	for(Iterator<PowerDevice> itor = mlkgdzList.iterator();itor.hasNext();){
								PowerDevice dz = itor.next();
								
								if(mlkgscList.contains(dz)){
									itor.remove();
								}
							}
					    	
					    	bbzmlkgdzList.addAll(mlkgdzList);
					    }else{
					    	bbzmlkgdzList.addAll(mlkgkgList);
					    }
				    }
				    
					allczkgList.addAll(zbkglist);
					allczkgList.addAll(fdkglist);
					allczkgList.addAll(bbzmlkgdzList);
					allczkgList.addAll(zybkgList);
					allczkgList.addAll(jdzybkgList);
					allczkgList.addAll(drkglist);
					
					if(allczkgList.size()>0){
						replaceStr += CZPService.getService().getDevName(allczkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备冷备用/r/n";
						
						List<PowerDevice> bbzdzkgList = new ArrayList<PowerDevice>();//可能存在非标准接线刀闸

						bbzdzkgList.addAll(zbkglist);
						bbzdzkgList.addAll(drkglist);
						bbzdzkgList.addAll(jdzybkgList);
						bbzdzkgList.addAll(zybkgList);

						for(PowerDevice bbzdzkg : bbzdzkgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceList(bbzdzkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
							
							if(dzList.size()>2){
								for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
									PowerDevice dz = itor.next();
									
									if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){//复电
										itor.remove();
									}
								}
								bbzdzList.addAll(dzList);
							}
						}
						
						if(bbzdzList.size()>0){
							replaceStr +="核实"+stationName+CZPService.getService().getDevName(bbzdzList)+"在拉开位置/r/n";
						}
					}
					
					if(zbkglist.size()>0){
						List<PowerDevice> zblist =  RuleExeUtil.getDeviceList(zbkglist.get(0), SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);
						
						List<PowerDevice> loadList = RuleExeUtil.getTransformerSwitchLoad(curzb);
						
						if(loadList.size()==2){
							replaceStr +="投入"+CZPService.getService().getDevName(zblist)+"10kV侧后备保护动作跳主变三侧断路器/r/n";
						}else{
							replaceStr +="投入"+CZPService.getService().getDevName(zblist)+"10kV侧后备保护动作跳主变两侧断路器/r/n";
						}
						
					}
					
					for (Iterator<PowerDevice> it = zbkglist.iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if (!RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							it.remove();
						}
					}
					
					if(allczkgList.size()>0){
						for (Iterator<PowerDevice> it = allczkgList.iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
							
							if (!RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								it.remove();
							}
						}
						
						if(jdzybkgList.size()>0){
							replaceStr +="投入10kV#X接地变小电阻自投切功能/r/n";
							replaceStr +="投入10kV#X接地变保护动作跳"+CZPService.getService().getDevName(fdkglist)+"及"+CZPService.getService().getDevName(zbkglist)+"/r/n";
						}
						
						replaceStr +="投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套10kV侧后备保护动作跳"+CZPService.getService().getDevName(fdkglist)+"/r/n";
						
						replaceStr +="将"+CZPService.getService().getDevName(allczkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由冷备用转热备用/r/n";
						
						if(bbzdzList.size()>0){
							replaceStr +="合上"+CZPService.getService().getDevName(bbzdzList)+"/r/n";
						}
						
						if(bbzmlkgdzList.size()>0){
							replaceStr +="核实"+CZPService.getService().getDevName(bbzmlkgdzList)+"在合闸位置/r/n";
						}
					}
					
					if(zbkglist.size()>0){
						zbkglist.addAll(yxdycmlkglist);
						
						if(yxdycmlkglist.size()>0){
							replaceStr +="核实"+stationName+CZPService.getService().getDevName(gycmlkgList)+"运行正常/r/n";
							replaceStr +=cf.getZbBLTQStrReplace(curzb);
						}
						
						replaceStr +="红河地调	@遥控合上"+stationName+CZPService.getService().getDevName(zbkglist)+"/r/n";
						replaceStr +="核实"+CZPService.getService().getDevName(zbkglist)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备运行正常/r/n";
					}
					
					if(zbList.size()>1){
						if(jdzybkgList.size()>0){
							replaceStr +="红河地调@遥控合上"+stationName+CZPService.getService().getDevName(jdzybkgList)+"/r/n";
							replaceStr +="核实"+CZPService.getService().getDevName(jdzybkgList)+"运行正常/r/n";
						}
						
						if(yxdycmlkglist.size()==0){
							replaceStr += "投入10kV备自投装置/r/n";
							
							StringBuffer dycsbf = new StringBuffer();
							
							dycsbf.append("投入");
							
							RuleExeUtil.swapDeviceList(zbList);
							
							if(fdkglist.size()>0){
								for(PowerDevice zb : zbList){
									List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zb);
									
									if(zbdyckgList.size()>1){
										dycsbf.append(CZPService.getService().getDevName(zb)+"第Ⅰ、Ⅱ套"+(int)zbdyckgList.get(0).getPowerVoltGrade()+"kV分支X后备保护及");
									}else if(zbdyckgList.size()==1){
										dycsbf.append(CZPService.getService().getDevName(zb)+"第Ⅰ、Ⅱ套"+(int)zbdyckgList.get(0).getPowerVoltGrade()+"kV侧后备保护及");
									}
								}
								
								if(dycsbf.toString().endsWith("及")){
									dycsbf = dycsbf.delete(dycsbf.length()-1, dycsbf.length());
								}
								
								dycsbf.append("动作闭锁10kV备自投装置/r/n");
								replaceStr += dycsbf.toString();
							}else{
								dycsbf.append(CZPService.getService().getDevName(zbList)+"10kV侧后备保护动作闭锁10kV备自投装置/r/n");
								replaceStr += dycsbf.toString();
							}
							
							if(fdkglist.size()>0){
								replaceStr += "投入10kV#1、#2弧光保护动作闭锁10kV备自投装置/r/n";
							}
						}
					}
					
					if(jdzybkgList.size()>0){
						replaceStr +="投入10kV#X接地变保护动作闭锁10kV备自投装置/r/n";
					}
					replaceStr += cf.getXzZbLxbhltxdStrReplace(curzb, "投入");
				}else if(station.getPowerVoltGrade() == 35){
				    replaceStr += "红河配网调控组@核实"+stationName+sbName+"上其所管辖的所有10kV出线断路器均在冷备用/r/n";
				    replaceStr += "核实"+CZPService.getService().getDevName(zbList)+"空载运行正常/r/n";
					List<PowerDevice> allkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> bzkgList = new ArrayList<PowerDevice>();//标准接线开关
					List<PowerDevice> bbzkgList = new ArrayList<PowerDevice>();//非标准接线开关
					List<PowerDevice> bbzdzList = new ArrayList<PowerDevice>();
				    List<PowerDevice> mxkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
				    List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
				    List<PowerDevice> yxmlkgList = new ArrayList<PowerDevice>();
				    List<PowerDevice> bbzmlkgdzList = new ArrayList<PowerDevice>();
					List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
					List<PowerDevice> fhczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
					List<PowerDevice> drqkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR, "", false, true, true, true);
					
				    if(mlkgList.size()>0){
				    	for(PowerDevice mlkg : mlkgList){
							if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
								yxmlkgList.add(mlkg);
							}
						}
				    	
				    	List<PowerDevice> mlkgscList =  RuleExeUtil.getDeviceDirectList(mlkgList.get(0), SystemConstants.SwitchSeparate);
					    List<PowerDevice> mlkgdzList = RuleExeUtil.getDeviceList(mlkgList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
					    List<PowerDevice> mlkgkgList = RuleExeUtil.getDeviceList(mlkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, false);

					    if(mlkgdzList.size()>2){
					    	for(Iterator<PowerDevice> itor = mlkgdzList.iterator();itor.hasNext();){
								PowerDevice dz = itor.next();
								
								if(mlkgscList.contains(dz)){
									itor.remove();
								}
							}
					    	
					    	bbzmlkgdzList.addAll(mlkgdzList);
					    }else{
					    	bbzmlkgdzList.addAll(mlkgkgList);
					    }
				    }
				    
			    	if(mxkgList.size()>0){
			    		for(PowerDevice dev : mxkgList){
			    			if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
									jdzybkgList.add(dev);
								}
								
								if(dev.getPowerDeviceName().contains("站用变")&&!dev.getPowerDeviceName().contains("接地站用变")){
									zybkgList.add(dev);
								}
							}
			    		}
			    	}
				    	
					
			    	allkgList.addAll(fhczbkgList);
			    	allkgList.addAll(drqkgList);
			    	allkgList.addAll(jdzybkgList);
					allkgList.addAll(zybkgList);

					for(PowerDevice allkg : allkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceList(allkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
						
						if(dzList.size()>2){
							for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
								PowerDevice dz = itor.next();
								
								if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
									itor.remove();
								}
							}
							
							if(dzList.size()==1){
								bbzdzList.addAll(dzList);
							}
						}else if(dzList.size()==1){
							bbzkgList.add(allkg);
							bbzdzList.addAll(dzList);
						}
					}
					
					allkgList.addAll(mlkgList);
					
				    for(PowerDevice allkg : allkgList){
				    	if(!bbzkgList.contains(allkg)){
				    		bzkgList.add(allkg);
				    	}
				    }
				    
				    List<PowerDevice> yxbbzkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> rbybbzkgList = new ArrayList<PowerDevice>();

					List<PowerDevice> dkbbzdzList = new ArrayList<PowerDevice>();
					List<PowerDevice> hsbbzdzList = new ArrayList<PowerDevice>();
				    
				    for(PowerDevice dev : bbzkgList){
				    	if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
				    		rbybbzkgList.add(dev);
				    	}else{
				    		yxbbzkgList.add(dev);
				    	}
				    }
				    
				    for(PowerDevice dev : bbzdzList){
				    	if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
				    		dkbbzdzList.add(dev);
				    	}else{
				    		hsbbzdzList.add(dev);
				    	}
				    }
				    
				    if(rbybbzkgList.size()>0){
				    	replaceStr +="核实"+CZPService.getService().getDevName(rbybbzkgList).replace("及", "")+"在分闸位置/r/n";
				    }
				    
				    if(dkbbzdzList.size()>0){
				    	replaceStr +="核实"+CZPService.getService().getDevName(dkbbzdzList)+"在拉开位置/r/n";
				    }
				    
				    if(drqkgList.size()>0){
				    	boolean sc = false;
				    	
		    	    	for(PowerDevice drqkg : drqkgList){
		    	    		List<PowerDevice> dzzlList = RuleExeUtil.getDeviceDirectList(drqkg, SystemConstants.SwitchSeparate);
							List<PowerDevice> dzList = RuleExeUtil.getDeviceList(drqkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
							
							if(dzList.size()>2){
								for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
									PowerDevice dz = itor.next();
									
									if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
										sc = true;
									}
									
									if(dzzlList.contains(dz)){
										itor.remove();
									}
								}
								
								if(dzList.size()==1&&!sc){
						    		 replaceStr += "核实"+CZPService.getService().getDevName(dzList)+"在拉开位置/r/n";
								}
							}
		    	    	}
		    	    }
				    
				    if(hsbbzdzList.size()>0){
						List<PowerDevice> tempdzList = new ArrayList<PowerDevice>();
				    	
				    	for(PowerDevice hsbbzdz : hsbbzdzList){
				    		List<PowerDevice> swList =  RuleExeUtil.getDeviceDirectList(hsbbzdz, SystemConstants.Switch);
				    		
				    		if(swList.size()>0){
				    			if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(swList.get(0)).equals("0")){
				    				tempdzList.add(hsbbzdz);
				    			}
				    		}else{
				    			tempdzList.add(hsbbzdz);
				    		}
				    	}
				    	
				    	if(tempdzList.size()>0){
					    	replaceStr +="核实"+CZPService.getService().getDevName(tempdzList)+"在合闸位置/r/n";
				    	}
				    }
				    
				    List<PowerDevice> lbybzkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> rbybzkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> yxbzkgList = new ArrayList<PowerDevice>();

					for(PowerDevice fhczbkg : fhczbkgList){
						if(!bzkgList.contains(fhczbkg)&&!bbzkgList.contains(fhczbkg)){
							bzkgList.add(0,fhczbkg);
						}
					}
					
				    for(PowerDevice dev : bzkgList){
				    	if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				    		yxbzkgList.add(dev);
				    	}
				    	
				    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
				    		rbybzkgList.add(dev);
				    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
				    		lbybzkgList.add(dev);
				    	}
				    }
				    
				    if(lbybzkgList.size()>0){
				    	lbybzkgList.addAll(bbzmlkgdzList);
				    	replaceStr +="核实"+CZPService.getService().getDevName(lbybzkgList)+"、"+sbName+"及母线设备冷备用/r/n";
				    	
				    	for(PowerDevice bbzmlkgdz : bbzmlkgdzList){
				    		lbybzkgList.remove(bbzmlkgdz);
				    	}
				    }
				    
				    if(rbybzkgList.size()>0){
				    	replaceStr +="核实"+CZPService.getService().getDevName(rbybzkgList)+"热备用/r/n";
				    }
				    
				    if(zbList.size()>0){
					    replaceStr += "投入"+CZPService.getService().getDevName(curzb)+"10kV侧后备保护动作跳主变"+ce+"侧断路器/r/n";
					    replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"10kV侧后备保护动作跳"+CZPService.getService().getDevName(mlkgList)+"/r/n";
				    }
				    
				    lbybzkgList.addAll(rbybzkgList);
		    	    
				    if(jdzybkgList.size()>0){
				    	replaceStr += "投入10kV#X接地变小电阻自投切功能/r/n";
				    	replaceStr += "投入10kV#X接地变保护动作跳"+CZPService.getService().getDevName(mlkgList)+"及"+CZPService.getService().getDevName(fhczbkgList)+"/r/n";
					}
				    
		    	    if(lbybzkgList.size()>0){
						replaceStr += "将"+CZPService.getService().getDevName(lbybzkgList)+"、"+sbName+"及母线设备由冷备用转热备用/r/n";
		    	    }else{
						replaceStr += "核实"+sbName+"及母线设备由冷备用转热备用/r/n";
		    	    }
				    
		    	    if(dkbbzdzList.size()>0){
			    	    replaceStr += "合上"+CZPService.getService().getDevName(dkbbzdzList)+"/r/n";
		    	    }
		    	    
		    	    if(bbzmlkgdzList.size()>0){
			    	    replaceStr += "核实"+CZPService.getService().getDevName(bbzmlkgdzList)+"在合闸位置/r/n";
		    	    }
		    	    
		    	    if(yxbzkgList.size()>0){
		    	    	if(yxbzkgList.size()==1){
		    	    		replaceStr += "遥控合上"+stationName+CZPService.getService().getDevName(yxbzkgList)+"/r/n";
		    	    	}else if(yxbzkgList.size()>1){
		    	    		if(yxmlkgList.size()>0){
			    	    		replaceStr += cf.getZbBLTQStrReplace(curzb);
		    	    		}
		    	    		
		    	    		replaceStr += cf.getYcHsStrReplace(yxbzkgList, stationName);
		    	    	}
		    	    	
						replaceStr += "核实"+CZPService.getService().getDevName(yxbzkgList)+"、"+sbName+"及母线设备运行正常/r/n";
		    	    }
		    	    
					if(yxmlkgList.size()==0&&zbList.size()>1){
						replaceStr += "投入10kV备自投装置/r/n";
						
						StringBuffer dycsbf = new StringBuffer();
						
						dycsbf.append("投入");
						
						RuleExeUtil.swapDeviceList(zbList);
						
						if(mlkgList.size()>0){
							boolean zbdyc2kg = false;
							
							for(PowerDevice zb : zbList){
								List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zb);
								
								if(zbdyckgList.size()>1){
									zbdyc2kg = true;
								}
							}
							
							if(zbdyc2kg){
								for(PowerDevice zb : zbList){
									List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zb);
									
									if(zbdyckgList.size()>1){
										dycsbf.append(CZPService.getService().getDevName(zb)+(int)zbdyckgList.get(0).getPowerVoltGrade()+"kV分支X后备保护及");
									}
								}
								
								if(dycsbf.toString().endsWith("及")){
									dycsbf = dycsbf.delete(dycsbf.length()-1, dycsbf.length());
								}
								
								dycsbf.append("动作闭锁10kV备自投装置/r/n");
								replaceStr += dycsbf.toString();
							}else{
								dycsbf.append(CZPService.getService().getDevName(zbList)+"10kV侧后备保护动作闭锁10kV备自投装置/r/n");
								replaceStr += dycsbf.toString();
							}
						}else{
							dycsbf.append(CZPService.getService().getDevName(zbList)+"10kV侧后备保护动作闭锁10kV备自投装置/r/n");
							replaceStr += dycsbf.toString();
						}
						
						if(jdzybkgList.size()>0){
					    	replaceStr += "投入10kV#X接地变保护动作闭锁10kV备自投装置/r/n";
						}
					}
				}
			}
		}
		return replaceStr;
	}
	
}
