package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.EMSService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.util.WebServiceUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.conditionmodel.judgeLineOperateOrder;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

/**
 * 版权声明: 泰豪软件股份有限公司版权所有 功能说明: 作 者: 郑柯 开发日期: 2012年5月31日 上午10:14:29
 */
public class RuleExeUtil {
	static Logger log=Logger.getLogger(RuleExeUtil.class);
	/***
	 * 基本方法
	 */

	public static boolean isDeviceHadStatus(PowerDevice dev,String begin,String end) {
		boolean result = false;
		
		boolean isbegin = false;
		boolean isend = false;

		Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
		for (DispatchTransDevice dtd : dtds.values()) {
			if(dtd.getTransDevice().equals(dev)){
				if(dtd.getBeginstatus().equals(begin)){
					isbegin = true;
				}
				
				if(dtd.getEndstate().equals(end)){
					isend = true;
				}
			}
		}
		
		if(isend&&isbegin){
			result = true;
		}
		
		return result;
	}
	
	/**
	 * 按设备智能操作规则执行状态转换
	 * */
	public static boolean deviceStatusChange(PowerDevice dev,
			String startStatus, String endStatus) {
		String preBuildType = CBSystemConstants.cardbuildtype;
		CBSystemConstants.cardbuildtype = "0";
		RuleExecute ruleExecute = new RuleExecute();
		RuleBaseMode rbmode = new RuleBaseMode();
		rbmode.setPd(dev);
		rbmode.setBeginStatus(startStatus);
		rbmode.setEndState(endStatus);
		boolean result = ruleExecute.execute(rbmode);
		CBSystemConstants.cardbuildtype = preBuildType;
		return result;
	}
	
	/**
	 * 按设备点图操作规则执行状态转换
	 * */
	public static boolean deviceStatusExecute(PowerDevice dev,
			String startStatus, String endStatus) {
		String preBuildType = CBSystemConstants.cardbuildtype;
		CBSystemConstants.cardbuildtype = "1";
		RuleExecute ruleExecute = new RuleExecute();
		RuleBaseMode rbmode = new RuleBaseMode();
		rbmode.setPd(dev);
		rbmode.setBeginStatus(startStatus);
		rbmode.setEndState(endStatus);
		boolean result = ruleExecute.execute(rbmode);
		CBSystemConstants.cardbuildtype = preBuildType;
		return result;
	}
	
	/**
	 * 按设备对位操作规则执行状态转换
	 * */
	public static boolean deviceStatusReset(PowerDevice dev,
			String startStatus, String endStatus) {
		String preBuildType = CBSystemConstants.cardbuildtype;
		CBSystemConstants.cardbuildtype = "2";
		RuleExecute ruleExecute = new RuleExecute();
		RuleBaseMode rbmode = new RuleBaseMode();
		rbmode.setPd(dev);
		rbmode.setBeginStatus(startStatus);
		rbmode.setEndState(endStatus);
		if(dev.getDeviceType().equals(SystemConstants.InOutLine)) { //线路设备对位，直接设置电源负荷侧
			new judgeLineOperateOrder().execute(rbmode);
		}
		boolean result = ruleExecute.execute(rbmode);
		CBSystemConstants.cardbuildtype = preBuildType;
		return result;
	}

	/**
	 * 只执行设备自身的状态转换
	 * */
	public static boolean deviceStatusSet(PowerDevice dev, String startStatus,
			String endStatus) {
		DispatchTransDevice dtd = new DispatchTransDevice();
		dtd.setTransDevice(dev);
		dtd.setParentDevice(CBSystemConstants.getParentDev());
		dtd.setBeginstatus(startStatus);
		dtd.setEndstate(endStatus);
		dtd.setFlag("0");
		CBSystemConstants.putDtdMap(dtd);
		dev.setDeviceStatus(endStatus);
		return true;
	}
	
	/**
	 * 只执行设备自身的状态转换
	 * */
	public static boolean deviceStatusSet(PowerDevice dev, String startStatus,
			String endStatus, boolean isSaveDtd) {
		if(isSaveDtd) {
			DispatchTransDevice dtd = new DispatchTransDevice();
			dtd.setTransDevice(dev);
			dtd.setParentDevice(CBSystemConstants.getParentDev());
			dtd.setBeginstatus(startStatus);
			dtd.setEndstate(endStatus);
			dtd.setFlag("0");
			CBSystemConstants.putDtdMap(dtd);
			dev.setDeviceStatus(endStatus);
		}
		else
			dev.setDeviceStatus(endStatus);
		return true;
	}

	/**
	 * 判断设备是否已经操作过
	 * 
	 * @param dev
	 * @return
	 */
	public static boolean isDeviceOperate(PowerDevice dev) {
		for (int i = 1; i <= CBSystemConstants.getCurOperateDevs().size(); i++) {
			if (CBSystemConstants.getCurOperateDevs().get(i)!=null&&CBSystemConstants.getCurOperateDevs().get(i).equals(dev))
				return true;
		}
		return false;
	}
	/**
	 * 判断设备是否在dtd里
	 * 
	 * @param dev
	 * @return
	 */
	public static boolean isDeviceInDtd(PowerDevice dev) {
		for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
			DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
			if(dev.equals(dtd.getTransDevice()))
				return true;
		}
		return false;
	}
	/**
	 * 判断设备是否已经状态改变过
	 * 
	 * @param dev
	 * @return
	 */
	public static boolean isDeviceChanged(PowerDevice dev) {
		Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
		for (DispatchTransDevice dtd : dtds.values()) {
			if(dtd.getTransDevice().equals(dev)){
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 判断设备是否在操作过程中是否处于过检修状态
	 * 
	 * @param dev
	 * @return
	 */
	public static boolean isDeviceRepaired(PowerDevice dev) {
		Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
		for (DispatchTransDevice dtd : dtds.values()) {
			if(dtd.getTransDevice().equals(dev)){
				if(dtd.getBeginstatus().equals("3") || dtd.getEndstate().equals("3"))
					return true;
			}
		}
		return false;
	}
	
	/**
	 * 判断设备是否在操作过程中是否处于过运行状态
	 * 
	 * @param dev
	 * @return
	 */
	public static boolean isDeviceRun(PowerDevice dev) {
		Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
		for (DispatchTransDevice dtd : dtds.values()) {
			if(dtd.getTransDevice().equals(dev)){
				if(dtd.getBeginstatus().equals("0") || dtd.getEndstate().equals("0"))
					return true;
			}
		}
		return false;
	}
	
	/**
	 * 获得设备在操作前的状态
	 * 
	 * @param dev
	 * @return
	 */
	public static String getDeviceBeginStatus(PowerDevice dev) {
		Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
		for (DispatchTransDevice dtd : dtds.values()) {
			if(dtd.getTransDevice().equals(dev)){
				return dtd.getBeginstatus();
			}
		}
		return "";
	}
	
	/**
	 * 获得设备在操作前的状态,包括不操作的状态
	 * 
	 * @param dev
	 * @return
	 */
	public static String getDeviceBeginStatusContainNotOperate(PowerDevice dev) {
		Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
		for (DispatchTransDevice dtd : dtds.values()) {
			if(dtd.getTransDevice().equals(dev)){
				return dtd.getBeginstatus();
			}
		}
		return dev.getDeviceStatus();
	}
	
	/**
	 * 判断是否存在接地变操作
	 * 
	 * @param dev
	 * @return
	 */
	public static boolean isEarthTransformerOperate() {
		boolean result = false;
		for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
			DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
			PowerDevice dev = dtd.getTransDevice();
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)) {
				result = true;
				break;
			}
		}
		return result;
	}
	
	/**
	 * 判断是否存在小车操作
	 * 
	 * @param dev
	 * @return
	 */
	public static boolean isKnifeXCOperate() {
		boolean result = false;
		for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
			DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
			PowerDevice dev = dtd.getTransDevice();
			if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)) {
				result = true;
				break;
			}
		}
		return result;
	}
	
	/**
	 * 判断是否存在高压侧母联操作
	 * 
	 * @param pd 主变
	 * @return
	 */
	public static boolean isMLSwitchHighOperate(PowerDevice pd) {
		boolean result = false;
		for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
			DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
			PowerDevice dev = dtd.getTransDevice();
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) &&
					dev.getPowerStationID().equals(pd.getPowerStationID()) &&
					dev.getPowerVoltGrade() == pd.getPowerVoltGrade()) {
				result = true;
				break;
			}
		}
		return result;
	}

	public static void swapDtdList(List<DispatchTransDevice> dtdList) {
		Collections.sort(dtdList, new Comparator<DispatchTransDevice>() {
			public int compare(DispatchTransDevice dtd1, DispatchTransDevice dtd2) {
				PowerDevice pd1 = dtd1.getTransDevice();
				PowerDevice pd2 = dtd2.getTransDevice();
				if (pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
					return 1;
				else if (pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
					return 0;
				else
					return pd1.getPowerDeviceName().compareTo(
							pd2.getPowerDeviceName());
			}
		});
	}
	
	public static void swapLowDtdList(List<DispatchTransDevice> dtdList) {
		Collections.sort(dtdList, new Comparator<DispatchTransDevice>() {
			public int compare(DispatchTransDevice dtd1, DispatchTransDevice dtd2) {
				PowerDevice pd1 = dtd1.getTransDevice();
				PowerDevice pd2 = dtd2.getTransDevice();
				if (pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
					return 1;
				else if (pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
					return 0;
				else
					return pd1.getPowerDeviceName().compareTo(
							pd2.getPowerDeviceName());
			}
		});
	}
	
	/**
	 * 设备排序，先比较电压等级（低的在前），再比较设备名称,
	 * 
	 * @param deviceList
	 */
	public static void swapDeviceList(List<PowerDevice> deviceList) {
		Collections.sort(deviceList, new Comparator<PowerDevice>() {
			public int compare(PowerDevice pd1, PowerDevice pd2) {
				if (pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
					return 1;
				else if (pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
					return 0;
				else
					return pd1.getPowerDeviceName().compareTo(
							pd2.getPowerDeviceName());
			}
		});
	}
	
	public static void swapDeviceByHighVoltList(List<PowerDevice> deviceList) {
		Collections.sort(deviceList, new Comparator<PowerDevice>() {
			public int compare(PowerDevice pd1, PowerDevice pd2) {
				PowerDevice station1 = CBSystemConstants.getPowerStation(pd1.getPowerStationID());
				PowerDevice station2 = CBSystemConstants.getPowerStation(pd2.getPowerStationID());

				if (station1.getPowerVoltGrade() < station2.getPowerVoltGrade())
					return 1;
				else 
					return 0;
			}
		});
	}
	
	public static void swapDeviceByLowVoltList(List<PowerDevice> deviceList) {
		Collections.sort(deviceList, new Comparator<PowerDevice>() {
			public int compare(PowerDevice pd1, PowerDevice pd2) {
				PowerDevice station1 = CBSystemConstants.getPowerStation(pd1.getPowerStationID());
				PowerDevice station2 = CBSystemConstants.getPowerStation(pd2.getPowerStationID());

				if (station1.getPowerVoltGrade() > station2.getPowerVoltGrade())
					return 1;
				else 
					return 0;
			}
		});
	}
	
	/**
	 * 设备排序，按设备名称倒序,
	 * 
	 * @param deviceList
	 */
	public static void swapLowNameDeviceList(List<PowerDevice> deviceList) {
		  Collections.sort(deviceList, new Comparator<PowerDevice>() {
		    public int compare(PowerDevice pd1, PowerDevice pd2) {
		      return pd1.getPowerDeviceName().compareTo(
		        pd2.getPowerDeviceName());
		    }
		  });
	 }
	
	public static void swapDeviceListDefaultSta(List<PowerDevice> deviceList) {//常状态为0的优先排前面，然后其他的按它以前的排序//黄翔修改
       
		Collections.sort(deviceList, new Comparator<PowerDevice>() {
			public int compare(PowerDevice pd1, PowerDevice pd2) {
				List<PowerDevice> zxddd = RuleUtil.getZxdddByTransform(pd1);//黄翔修改 通过主变找到它的中性点刀闸
				List<PowerDevice> zxdddd = RuleUtil.getZxdddByTransform(pd2);//黄翔修改通过主变找到它的中性点刀闸
				if(zxddd.size()==0||zxdddd.size()==0){
					return 0;
				}else{
				PowerDevice zxddd2 = zxdddd.get(0);
				PowerDevice zxddd1 = zxddd.get(0);
		        if (zxddd1.getPowerDeviceDefaultSta().equals("0")){
		        	 return -1;
				}else if(zxddd2.getPowerDeviceDefaultSta().equals("0")){
		        	 return 1;
		        	 }else{
		        		 return 0;
		        	 }}
		        }});
	}

	/**
	 * 设备排序，先比较电压等级（高的在前），再比较设备名称，
	 * 
	 * @param deviceList
	 */
	public static void swapLowDeviceList(List<PowerDevice> deviceList) {
		Collections.sort(deviceList, new Comparator<PowerDevice>() {
			public int compare(PowerDevice pd1, PowerDevice pd2) {
				if (pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
					return 1;
				else if (pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
					return -1;
				else
					return pd1.getPowerDeviceName().compareTo(
							pd2.getPowerDeviceName());
			}
		});
	}
	
	/**
	 * 设备排序，先比较电压等级（低的在前），再比较设备类型，再比较设备编号
	 * 
	 * @param deviceList
	 */
	public static void swapDeviceListNum(List<PowerDevice> deviceList) {
		Collections.sort(deviceList, new Comparator<PowerDevice>() {
			public int compare(PowerDevice pd1, PowerDevice pd2) {
				if (pd1.getPowerVoltGrade() > pd2.getPowerVoltGrade())
					return 1;
				else if (pd1.getPowerVoltGrade() < pd2.getPowerVoltGrade())
					return 0;
				int type = pd1.getDeviceType().compareTo(pd2.getDeviceType());
				if(type != 0)
					return type;
				else {
					return CZPService.getService().getDevNum(pd1).compareTo(CZPService.getService().getDevNum(pd2));
				}
			}
		});
	}
	
	/**
	 * 设备排序，先比较电压等级（低的在前），再比较设备类型，再比较设备编号
	 * 
	 * @param deviceList
	 */
	public static void swapDispatchTransDeviceListNum(List<DispatchTransDevice> deviceList) {
		Collections.sort(deviceList, new Comparator<DispatchTransDevice>() {
			public int compare(DispatchTransDevice pd1, DispatchTransDevice pd2) {
				if (pd1.getTransDevice().getPowerVoltGrade() > pd2.getTransDevice().getPowerVoltGrade())
					return 1;
				else if (pd1.getTransDevice().getPowerVoltGrade() < pd2.getTransDevice().getPowerVoltGrade())
					return 0;
				int type = pd1.getTransDevice().getDeviceType().compareTo(pd2.getTransDevice().getDeviceType());
				if(type != 0)
					return type;
				else {
					return CZPService.getService().getDevNum(pd1.getTransDevice()).compareTo(CZPService.getService().getDevNum(pd2.getTransDevice()));
				}
			}
		});
	}
	
	/**
	 * 合并设备名称
	 * 
	 * @param deviceList
	 * @return
	 */
	public static String getDeviceName(List<PowerDevice> deviceList) {
		swapDeviceList(deviceList);
		String deviceName = "";
		for (PowerDevice device : deviceList) {
			deviceName = deviceName + device.getPowerDeviceName() + ",";
		}
		if (deviceName.endsWith(","))
			deviceName = deviceName.substring(0, deviceName.length() - 1);
		return deviceName;
	}

	/**
	 * 判断是否电源侧的设备
	 * 
	 * @param deviceList
	 * @return
	 */
	public static boolean isSourceSide(PowerDevice pd) {
		PowerDevice station = CBSystemConstants.getPowerStation(pd
				.getPowerStationID());
		if(station == null)
			return true;
		return pd.getPowerVoltGrade() == station.getPowerVoltGrade();
	}

	/**
	 * 查找设备，传入常用参数
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param tagType
	 *            目标设备类型
	 * @param excType
	 *            排除设备类型
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索
	 * @param isStopOnTagType
	 *            是否遇目标设备类型停止搜索
	 * @return 目标设备集合
	 */
	public static List<PowerDevice> getDeviceList(PowerDevice pd,
			String tagType, String excType, boolean isSearchOff,
			boolean isStopOnBus, boolean isStopOnTagType) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", tagType);
		inPara.put("excDevType", excType);
		inPara.put("isSearchOffPath", isSearchOff);
		inPara.put("isStopOnBusbarSection", isStopOnBus);
		inPara.put("isStopOnTagDevType", isStopOnTagType);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");
	}

	/**
	 * 查找设备，传入除排除设备外所有参数
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param tagType
	 *            目标设备类型
	 * @param excType
	 *            排除设备类型
	 * @param tagRunType
	 *            目标安装类型
	 * @param excRunType
	 *            排除安装类型
	 * @param isSearchDirectOnly
	 *            是否只搜索直接连接设备
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索
	 * @param isStopOnTagType
	 *            是否遇目标设备类型停止搜索
	 * @return 目标设备集合
	 */
	public static List<PowerDevice> getDeviceList(PowerDevice pd,
			String tagType, String excType, String tagRunType,
			String excRunType, boolean isSearchDirectOnly, boolean isSearchOff,
			boolean isStopOnBus, boolean isStopOnTagType) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", tagType);
		inPara.put("excDevType", excType);
		inPara.put("tagDevRunType", tagRunType);
		inPara.put("excDevRunType", excRunType);
		inPara.put("isSearchDirectDevice", isSearchDirectOnly);
		inPara.put("isSearchOffPath", isSearchOff);
		inPara.put("isStopOnBusbarSection", isStopOnBus);
		inPara.put("isStopOnTagDevType", isStopOnTagType);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");
	}

	/**
	 * 查找设备，传入所有参数
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param excDev
	 *            排除设备对象
	 * @param tagType
	 *            目标设备类型
	 * @param excType
	 *            排除设备类型
	 * @param tagRunType
	 *            目标安装类型
	 * @param excRunType
	 *            排除安装类型
	 * @param isSearchDirectOnly
	 *            是否只搜索直接连接设备
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索
	 * @param isStopOnTagType
	 *            是否遇目标设备类型停止搜索
	 * @return 目标设备集合
	 */
	public static List<PowerDevice> getDeviceList(PowerDevice pd,
			PowerDevice excDev, String tagType, String excType,
			String tagRunType, String excRunType, boolean isSearchDirectOnly,
			boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("excDevice", excDev);
		inPara.put("tagDevType", tagType);
		inPara.put("excDevType", excType);
		inPara.put("tagDevRunType", tagRunType);
		inPara.put("excDevRunType", excRunType);
		inPara.put("isSearchDirectDevice", isSearchDirectOnly);
		inPara.put("isSearchOffPath", isSearchOff);
		inPara.put("isStopOnBusbarSection", isStopOnBus);
		inPara.put("isStopOnTagDevType", isStopOnTagType);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");
	}
	
	/**
	 * 查找设备，传入所有参数
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param excDev
	 *            排除设备对象
	 * @param tagType
	 *            目标设备类型
	 * @param excType
	 *            排除设备类型
	 * @param tagRunType
	 *            目标安装类型
	 * @param excRunType
	 *            排除安装类型
	 * @param isSearchDirectOnly
	 *            是否只搜索直接连接设备
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索
	 * @param isStopOnTagType
	 *            是否遇目标设备类型停止搜索
	 * @param isStopOnDiffVolt
	 *            是否遇不同电压等级设备停止搜索
	 * @return 目标设备集合
	 */
	public static List<PowerDevice> getDeviceList(PowerDevice pd,
			PowerDevice excDev, String tagType, String excType,
			String tagRunType, String excRunType, boolean isSearchDirectOnly,
			boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType, boolean isStopOnDiffVolt) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("excDevice", excDev);
		inPara.put("tagDevType", tagType);
		inPara.put("excDevType", excType);
		inPara.put("tagDevRunType", tagRunType);
		inPara.put("excDevRunType", excRunType);
		inPara.put("isSearchDirectDevice", isSearchDirectOnly);
		inPara.put("isSearchOffPath", isSearchOff);
		inPara.put("isStopOnBusbarSection", isStopOnBus);
		inPara.put("isStopOnTagDevType", isStopOnTagType);
		inPara.put("isStopOnDiffVolt", isStopOnDiffVolt);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");
	}

	/**
	 * 查找设备，传入所有参数
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param tagType
	 *            目标设备类型
	 * @param excType
	 *            排除设备类型
	 * @param tagRunType
	 *            目标安装类型
	 * @param excRunType
	 *            排除安装类型
	 * @param isSearchDirectOnly
	 *            是否只搜索直接连接设备
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索
	 * @param isStopOnTagType
	 *            是否遇目标设备类型停止搜索
	 * @param validPort
	 *            0:全部端口有效 1:1号端口有效 2:2号端口有效
	 * @return 目标设备集合
	 */
	public static List<PowerDevice> getDeviceList(PowerDevice pd,
			String tagType, String excType, String tagRunType,
			String excRunType, boolean isSearchDirectOnly, boolean isSearchOff,
			boolean isStopOnBus, boolean isStopOnTagType, String port) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", tagType);
		inPara.put("excDevType", excType);
		inPara.put("tagDevRunType", tagRunType);
		inPara.put("excDevRunType", excRunType);
		inPara.put("isSearchDirectDevice", isSearchDirectOnly);
		inPara.put("isSearchOffPath", isSearchOff);
		inPara.put("isStopOnBusbarSection", isStopOnBus);
		inPara.put("isStopOnTagDevType", isStopOnTagType);
		inPara.put("validPort", port);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");
	}

	/**
	 * 查找设备，只查找直接相连的设备
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param tagDevtype
	 *            目标设备类型
	 * @return
	 */
	public static List<PowerDevice> getDeviceDirectList(PowerDevice pd,
			String tagDevtype) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", tagDevtype);
		inPara.put("isSearchDirectDevice", true);
		inPara.put("isSearchOffPath", true);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");
	}
	/**
	 * 查找设备，只查找直接相连的设备
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param tagDevtype
	 *            目标设备类型
     * @param devRunType
	 *            设备运行状态
	 * @return
	 */
	public static List<PowerDevice> getDeviceDirectList(PowerDevice pd,
			String tagDevtype,String devRunType) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", tagDevtype);
		inPara.put("tagDevRunType", devRunType);
		inPara.put("isSearchDirectDevice", true);
		inPara.put("isSearchOffPath", true);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");
	}
	/**
	 * 查找设备，根据端口查找直接相连的设备
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param type
	 *            目标设备类型
	 * @param validPort
	 *            0:全部端口有效 1:1号端口有效 2:2号端口有效
	 * @return
	 */
	public static List<PowerDevice> getDeviceDirectByPortList(PowerDevice pd,
			String type, String port) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", type);
		inPara.put("isSearchDirectDevice", true);
		inPara.put("isSearchOffPath", true);
		inPara.put("validPort", port);
		cs.execute(inPara, outPara);
		return (ArrayList) outPara.get("linkedDeviceList");
	}

	/**
	 * 查找两个设备间的最短路径
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param tag
	 *            搜索目标设备
	 * @param excType
	 *            排除设备类型
	 * @param excRunType
	 *            排除安装类型
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索
	 * @return
	 */
	public static List<PowerDevice> getPathByDevice(PowerDevice pd,
			PowerDevice tag, String excType, String excRunType,
			boolean isSearchOff, boolean isStopOnBus) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevice", tag);
		inPara.put("excDevType", excType);
		inPara.put("excDevRunType", excRunType);
		inPara.put("isSearchOffPath", isSearchOff);
		inPara.put("isStopOnBusbarSection", isStopOnBus);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) ((HashMap<PowerDevice, ArrayList<PowerDevice>>) outPara
				.get("pathList")).get(tag);

	}

	/**
	 * 查找两个设备间的最短路径
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param excDev
	 *            排除设备对象
	 * @param tag
	 *            搜索目标设备
	 * @param excType
	 *            排除设备类型
	 * @param excRunType
	 *            排除安装类型
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索
	 * @return
	 */
	public static List<PowerDevice> getPathByDevice(PowerDevice pd,
			PowerDevice excDev, PowerDevice tag, String excType,
			String excRunType, boolean isSearchOff, boolean isStopOnBus) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("excDevice", excDev);
		inPara.put("tagDevice", tag);
		inPara.put("excDevType", excType);
		inPara.put("excDevRunType", excRunType);
		inPara.put("isSearchOffPath", isSearchOff);
		inPara.put("isStopOnBusbarSection", isStopOnBus);
		cs.execute(inPara, outPara);
		return (List<PowerDevice>) ((HashMap<PowerDevice, ArrayList<PowerDevice>>) outPara
				.get("pathList")).get(tag);

	}

	/**
	 * 查找两个设备间的所有路径
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param tag
	 *            搜索目标设备
	 * @param excType
	 *            排除设备类型
	 * @param excRunType
	 *            排除安装类型
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索
	 * @return
	 */
	public static ArrayList<ArrayList<PowerDevice>> getPathAllByDevice(
			PowerDevice pd, PowerDevice tag, String excType, String excRunType,
			boolean isSearchOff, boolean isStopOnBus) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevice", tag);
		inPara.put("excDevType", excType);
		inPara.put("excDevRunType", excRunType);
		inPara.put("isSearchOffPath", isSearchOff);
		inPara.put("isStopOnBusbarSection", isStopOnBus);
		cs.execute(inPara, outPara);
		return (ArrayList<ArrayList<PowerDevice>>) ((HashMap<PowerDevice, ArrayList<ArrayList<PowerDevice>>>) outPara
				.get("allPathList")).get(tag);

	}

	/**
	 * 查找路径，传入所有参数
	 * 
	 * @param pd
	 *            搜索起始设备
	 * @param excDev
	 *            排除设备对象
	 * @param tagType
	 *            目标设备类型
	 * @param excType
	 *            排除设备类型
	 * @param tagRunType
	 *            目标安装类型
	 * @param excRunType
	 *            排除安装类型
	 * @param isSearchDirectOnly
	 *            是否只搜索直接连接设备
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索
	 * @param isStopOnTagType
	 *            是否遇目标设备类型停止搜索
	 * @return 目标设备集合
	 */
	public static HashMap<PowerDevice, ArrayList<PowerDevice>> getPathByDevice(
			PowerDevice pd, PowerDevice excDev, String tagType, String excType,
			String tagRunType, String excRunType, boolean isSearchDirectOnly,
			boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("excDevice", excDev);
		inPara.put("tagDevType", tagType);
		inPara.put("excDevType", excType);
		inPara.put("tagDevRunType", tagRunType);
		inPara.put("excDevRunType", excRunType);
		inPara.put("isSearchDirectDevice", isSearchDirectOnly);
		inPara.put("isSearchOffPath", isSearchOff);
		inPara.put("isStopOnBusbarSection", isStopOnBus);
		inPara.put("isStopOnTagDevType", isStopOnTagType);
		cs.execute(inPara, outPara);
		return (HashMap<PowerDevice, ArrayList<PowerDevice>>) ((HashMap<PowerDevice, ArrayList<PowerDevice>>) outPara
				.get("pathList"));
	}

	/***
	 * 设备通用方法
	 */

	/**
	 * 获得设备关联的开关
	 * 
	 * @param pd
	 * @return
	 */
	public static PowerDevice getDeviceSwitch(PowerDevice pd) {
		if(pd==null){
			return null;
		}
		
		List<PowerDevice> switchs = null;
		if(pd.getDeviceType().equals(SystemConstants.InOutLine))
			switchs = getDeviceList(pd, SystemConstants.Switch,
					SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeKnifeQT,false, true, true, true);
		else
			switchs = getDeviceList(pd, SystemConstants.Switch,
				SystemConstants.PowerTransformer, true, true, true);
		if (switchs.size() > 0){
			RuleExeUtil.sortListByDevName(switchs);
			return switchs.get(0);
		}
		
		else {
			switchs = getDeviceList(pd, SystemConstants.Switch, null, null, null, false, true, false, true);
			for(int i = 0; i < switchs.size(); i++) {
				PowerDevice sw = switchs.get(i);
				if(pd.getDeviceType().equals(SystemConstants.EarthingTransformer)&& (!sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB))){
					switchs.remove(i);
					i--;
					continue;
				}

				if(sw.getDeviceStatus().equals("0") || sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
					switchs.add(0, sw);
					switchs.remove(i);
				}
			}
			if (switchs.size() > 0)
				return switchs.get(0);
			else
				return null;
		}

	}
	
	/***
	 * 刀闸方法
	 */
	
	/**
	 * 获取双母接线的另一侧刀闸
	 * @param pd
	 * @return
	 */
	public static PowerDevice getKnifeOtherML(PowerDevice pd) {
		List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
		if(swList.size() > 0) {
			if(CBSystemConstants.pjzMap.containsKey(pd.getPowerStationID())){
				List<PowerDevice> kfList = RuleExeUtil.getDeviceDirectByPortList(swList.get(0), SystemConstants.SwitchSeparate, "0");
				
				for(Iterator<PowerDevice> itor = kfList.iterator();itor.hasNext();){
					PowerDevice pe = itor.next();
					List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(pe,SystemConstants.SwitchSeparate);
						
					if(list.size()>0){
						kfList.remove(list.get(0));
						kfList.remove(pe);
						
						if(kfList.size()>0){
							return kfList.get(0);
						}
					}
				}
				
			}
			
			List<PowerDevice> kfList = RuleExeUtil.getDeviceDirectByPortList(swList.get(0), SystemConstants.SwitchSeparate, "1");
			if(kfList.contains(pd)) {
				kfList.remove(pd);
				if(kfList.size() > 0)
					return kfList.get(0);
			}
			else {
				kfList = RuleExeUtil.getDeviceDirectByPortList(swList.get(0), SystemConstants.SwitchSeparate, "2");
				if(kfList.contains(pd)) {
					kfList.remove(pd);
					if(kfList.size() > 0)
						return kfList.get(0);
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取刀闸的所属开关对侧的刀闸
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getKnifeOtherSide(PowerDevice pd) {
		List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
		if(swList.size() > 0) {
			List<PowerDevice> kfList = RuleExeUtil.getDeviceDirectByPortList(swList.get(0), SystemConstants.SwitchSeparate, "1");
			if(!kfList.contains(pd)) {
				return kfList;
			}
			else {
				kfList = RuleExeUtil.getDeviceDirectByPortList(swList.get(0), SystemConstants.SwitchSeparate, "2");
				if(!kfList.contains(pd)) {
					return kfList;
				}
			}
		}
		return null;
	}
	
	/**
	 * 查找接地刀闸所属的设备
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getGroundKnifeRelateLine(PowerDevice pd) {
		List<PowerDevice> knifeList = null;
		List<PowerDevice> lineList = null;
		knifeList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
		lineList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine);
		boolean isExistMxdcKnife = false;
		for (Iterator<PowerDevice> itr = knifeList.iterator(); itr.hasNext();) {
			PowerDevice dev = itr.next();
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY))
				isExistMxdcKnife = true;;
		}
		if(isExistMxdcKnife) {
			lineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
		}
		return lineList;
	}
	
	
	/**
	 * 判断刀闸是否直接连接开关
	 * @param pd
	 * @return
	 */
	public static boolean isKnifeLinkSwitch(PowerDevice pd) {
		List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
		return swList.size()>0;
	}
	
	/**
	 * 查找刀闸所属的开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getKnifeRelateSwitch(PowerDevice pd) {
		List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);// 获取连接的开关
		if (list!=null && list.size() == 0) {
			list = RuleExeUtil.getDeviceList(pd,  SystemConstants.Switch,  SystemConstants.PowerTransformer, true, true, true);
			if(list.size() >= 2) {
				for(Iterator it = list.iterator(); it.hasNext();) {
					PowerDevice dev = (PowerDevice)it.next();
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
						List<PowerDevice> srcList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, false, true);
						for(Iterator it2=srcList.iterator();it2.hasNext();) {
							PowerDevice src = (PowerDevice)it2.next();
							if(!src.getDeviceStatus().equals("0"))
								it2.remove();
						}
						if(srcList.size() == 1) {
							PowerDevice source = srcList.get(0); //电源点
							List<PowerDevice> path = RuleExeUtil.getPathByDevice(dev, source, "", "", false, false);
							if(path != null && !path.contains(pd)) {
								it.remove();
							}
						}
					}
				}
			}
		}else if(list.size() >= 2){//刀闸只会关联一个开关
			PowerDevice glkg =null;
			for(PowerDevice sw:list){
				if(CZPService.getService().getDevNum(sw.getPowerDeviceName()).equals(CZPService.getService().getDevNum(pd.getPowerDeviceName()))){
					glkg=sw;
					break;
				}
			}
			if(glkg!=null){
				list.clear();
				list.add(glkg);
			}
		}
		return list;
	}
	
	/***
	 * 开关方法
	 */
	
	/**
	 * 查找开关关联的刀闸
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getSwitchRelateKnife(PowerDevice pd) {
		List<PowerDevice> kfList = new ArrayList<PowerDevice>();
		List<PowerDevice> kfList1 = RuleExeUtil.getSwitchRelateKnifeByPort(pd, "1");
		List<PowerDevice> kfList2 = RuleExeUtil.getSwitchRelateKnifeByPort(pd, "2");
		kfList.addAll(kfList1);
		kfList.addAll(kfList2);
		return kfList;
	}
	
	public static List<PowerDevice> getSwitchRelateKnifeByPort(PowerDevice pd, String port) {
		List<PowerDevice> kfList = new ArrayList<PowerDevice>();
		List<PowerDevice> kfList1 = RuleExeUtil.getDeviceDirectByPortList(pd, SystemConstants.SwitchSeparate, port);
		kfList.addAll(kfList1);
		for(PowerDevice kf : kfList1) {
			if(kf.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)) {
				//List<PowerDevice> kfList2 = RuleExeUtil.getDeviceDirectList(kf, SystemConstants.SwitchSeparate);
				//kfList.addAll(kfList2);
				List<PowerDevice> kfList2 = RuleExeUtil.getDeviceList(kf, pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, "", "", false, true, true, false);
				if(kfList2.size() > 0) {
					for(PowerDevice kf2 : kfList2) {
						List<PowerDevice>  swList = RuleExeUtil.getKnifeRelateSwitch(kf2);
						if(swList.size() == 1 && swList.get(0).equals(pd))
							kfList.add(kf2);
					}
					
				}
			}
		}
		return kfList;
	}
	
	/**
	 * 查找开关关联的设备,包括开关自身、旁边刀闸、旁边母联开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getSwitchRelateDevice(PowerDevice pd) {
		List<PowerDevice> pdList = new ArrayList<PowerDevice>();
		pdList.add(pd);
		List<PowerDevice> kfList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
		pdList.addAll(kfList);
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
		for(PowerDevice ml : mlList) {
			if(ml.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
				continue;
			if(ml.getPowerDeviceName().indexOf("虚拟") >= 0) {
				kfList = RuleExeUtil.getDeviceDirectList(ml, SystemConstants.SwitchSeparate);
				pdList.addAll(kfList);
			}
			pdList.addAll(RuleExeUtil.getMLSwitchList(ml));
		}
		return pdList;
	}
	
	/**
	 * 查找开关带的负荷
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getSwitchLoadLine(PowerDevice pd) {
		List<PowerDevice> lnList = null;
		lnList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
		if(lnList.size() == 0) {
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			if(mlList.size() > 0) {
				lnList = RuleExeUtil.getDeviceList(mlList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			}
		}
		return lnList;
	}
	
	/**
	 * 
	 * @param pd 线路开关或主变开关
	 * @return 母联开关
	 */
	public static PowerDevice getSwitchBackupSwitch(PowerDevice pd) {
		PowerDevice backupSwitch = null;
		List<PowerDevice> backupSwitchlist=new ArrayList<PowerDevice>();
		String type=pd.getDeviceRunType();
		if(type.equals(CBSystemConstants.RunTypeSwitchDYC)||type.equals(CBSystemConstants.RunTypeSwitchFHC)||type.equals(CBSystemConstants.RunTypeSwitchXL)){
			List<PowerDevice> ml=getDeviceList(pd,SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchFHC,null,false,true,false,true);
			
		}
		if(backupSwitchlist.size()==1){
			backupSwitch=backupSwitchlist.get(0);
		}
		return backupSwitch;
	}

	/**
	 * 判断母联开关是否双母联络开关，否则是分段开关
	 * @param pd
	 * @return
	 */
	public static boolean isSwitchDoubleML(PowerDevice pd) {
		boolean result = false;
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
		for(Iterator it = mlList.iterator();it.hasNext();) {
			PowerDevice ml = (PowerDevice)it.next();
			if(ml.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
				it.remove();
		}
		if(mlList.size() >= 2) {
			if(mlList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
				List<PowerDevice> otherMLs = getMLDoubleOtherList(mlList.get(0)); //解决三母分段判断不准确问题
				if(otherMLs.contains(mlList.get(1)))
					result = true;
			}
		}
		return result;
	}
	/**
	 * 判断开关是否是分段开关  且是接线方式是双母双分段
	 * @param pd
	 * @return
	 */
	public static boolean isFDSwitchSMSF(PowerDevice pd) {
		
		if(!pd.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchML) 
		 || RuleExeUtil.isSwitchDoubleML(pd) ){
			return false;
		}
		
		List<PowerDevice> mlList = new ArrayList<PowerDevice>();
		Map<String,PowerDevice> devices  = (HashMap<String, PowerDevice>) CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		PowerDevice tempDev = null;
		for (Iterator iterator = devices.values().iterator(); iterator.hasNext();) {
			tempDev = (PowerDevice) iterator.next();
			if(tempDev.getDeviceType().equals(SystemConstants.MotherLine)&&tempDev.getPowerVoltGrade() == pd.getPowerVoltGrade())
				mlList.add(tempDev);
		}
		
		List<PowerDevice> fdswitchs = new ArrayList<PowerDevice>();
		for(Iterator it = mlList.iterator();it.hasNext();) {
			PowerDevice ml = (PowerDevice)it.next();
			List<PowerDevice> switchs = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		    for (PowerDevice swDev : switchs) {
		    	 if(swDev.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchML) 
		    			 && !RuleExeUtil.isSwitchDoubleML(swDev) 
		    			 && !fdswitchs.contains(swDev))
		    		 fdswitchs.add(swDev);
			}
		
		}
		if(fdswitchs.size() >= 2) {
			return true;
		}
		return false;
	}

	/***
	 * 线路方法
	 */

	/**
	 * 判断线路类型 0普通 1电缆
	 * 
	 * @param pd
	 * @return
	 */
	public static String judgeLineType(PowerDevice pd) {
		List<PowerDevice> allSideList = RuleExeUtil.getLineAllSideList(pd);
		for (PowerDevice line : allSideList) {
			
			List<PowerDevice> lnList1 = RuleExeUtil.getLineCable(line);
			if(lnList1.size() > 0)
				return "1";
			
			List<PowerDevice> lnList2 = RuleExeUtil.getLineCableOther(line);
			if(lnList2.size() > 0)
				return "1";
		}

		return "0";
	}
	
	/**
	 * 获得站内线路的对应的系统线路
	 * 
	 * @param pd
	 * @return
	 */
	public static PowerDevice getLineSystem(PowerDevice pd) {
		PowerDevice line = null;
		List<String> list = CBSystemConstants.getEquiplinemap().get(pd.getPowerDeviceID());
		if(list!= null && list.size() > 0) {
			String lineID = list.get(0);
			line = CBSystemConstants.getPowerLine(lineID);
		}
		return line;
	}

	/**
	 * 获得站内线路的另外几侧站内线路
	 * 
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getLineOtherSideList(PowerDevice pd) {
		List<PowerDevice> list = new ArrayList<PowerDevice>();
		
		
		if (!CBSystemConstants.getEquiplinemap().containsKey(
				pd.getPowerDeviceID()))
			return list;
		String lineID = CBSystemConstants.getEquiplinemap().get(pd.getPowerDeviceID()).get(0);
		ArrayList<String> stationList = CBSystemConstants.getLinestationmap().get(lineID);
		ArrayList<String> equipLineList = CBSystemConstants.getLineequipmap().get(lineID);
		if(stationList.size() == 1){
			//广西牵引站线路没有拓扑
			stationList = new ArrayList<String>();
			equipLineList = new ArrayList<String>();
			Map stationMap = QueryDeviceDao.getPowersLineByLineQY(pd);
			for (Iterator iterator = stationMap.keySet().iterator(); iterator.hasNext();) {
				PowerDevice tempdev=(PowerDevice)iterator.next();
				stationList.add(tempdev.getPowerStationID());
				equipLineList.add(tempdev.getPowerDeviceID());
			}
		}
		
		for (String stationID : stationList) {
			if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
				CreatePowerStationToplogy.loadFacEquip(stationID);
			}
		}

		for (String equipLineID : equipLineList) {
			String stationID = "";
			PowerDevice equipLine = null;
			for (String staID : stationList) {
				if (CBSystemConstants.getPowerDevice(staID, equipLineID) != null) {
					stationID = staID;
					equipLine = CBSystemConstants.getPowerDevice(stationID,
							equipLineID);
					break;
				}
			}
			if (equipLine!=null && !equipLine.equals(pd))
				list.add(equipLine);
		}
		return list;
	}
	
	/**
	 * 得到线路、开关所有线路段
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getLineAllSideList(PowerDevice pd) {
		List<PowerDevice> list = getLineOtherSideList(pd);
		list.add(pd);
		return list;
	}
	
	/**
	 * 获得共用主变开关的 线路
	 * 
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getLineCableOther(PowerDevice pd) {
		List<PowerDevice> cableList = new ArrayList<PowerDevice>();
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		if(swList.size() > 0) //如果不通过母线连接了开关，不存在共用主变开关情况
			return cableList;
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer+","+SystemConstants.Switch, true, true, true);
		if(mlList.size() > 0) {
			PowerDevice ml = mlList.get(0);
			if(!ml.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)) {
				List<PowerDevice> lnList = RuleExeUtil.getDeviceList(ml, SystemConstants.InOutLine , SystemConstants.PowerTransformer, true, true, true);
				for(PowerDevice ln : lnList) {
					if(!ln.equals(pd))
						cableList.add(ln);
				}
			}
		}
		return cableList;
	}
	
	/**
	 * 获得共用线路开关的线路
	 * 
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getLineCable(PowerDevice pd) {
		List<PowerDevice> cableList = new ArrayList<PowerDevice>();
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch , SystemConstants.PowerTransformer, true, true, true);
		if(swList.size() == 1) {
			PowerDevice sw = swList.get(0);
			List<PowerDevice> lnList = RuleExeUtil.getDeviceList(sw, SystemConstants.InOutLine , SystemConstants.PowerTransformer+","+SystemConstants.Switch, true, true, true);
			for(PowerDevice ln : lnList) {
				if((!ln.equals(pd)))
					cableList.add(ln);
			}
		}
		return cableList;
	}
	
	/**
	 * 判断线路是进线还是出线  -1无数据 0有功为零 1进线 2出线
	 * @param pd
	 * @return
	 */
	public static String getLineFlow(PowerDevice pd) {
		String flow = "-1";
		String lineflow = EMSService.getService().getLineFlow(pd.getPowerStationID(), pd.getPowerDeviceID());
		if(lineflow != null)
			flow = lineflow;
		return flow;
	}
	
	/**
	 * 判断线路 0双母 1单母 2线变组 3共用线路开关 4共用主变开关
	 * @param pd
	 * @return
	 */
	public static String getRunMode(PowerDevice pd) {
		List<PowerDevice> lnList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine , SystemConstants.PowerTransformer+","+ SystemConstants.Switch, true, true, true);
		if(lnList.size() > 0)
			return "3";
		
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer , SystemConstants.PowerTransformer, true, true, true);
		if(tfList.size() > 0)
			return "2";
		
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch , SystemConstants.PowerTransformer, true, true, true);
		if(swList.size() > 0) {
			PowerDevice sw = swList.get(0);
			if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine) || 
					sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
				return "0";
			else
				return "1";
		}
		else
			return "4";
	}
	
	/**
	 * 判断线路是进线还是出线 1进线 2出线
	 * @param pd
	 * @return
	 */
	
	public static String judgeLineFlow(PowerDevice pd) {
		String flag = "";
		
		String sql = "SELECT FLAG FROM "+CBSystemConstants.opcardUser+"T_A_LINEWAY WHERE LINEEQUIPID = '"+pd.getPowerDeviceID()+"'";
		
		List<Map<String,String>> list = DBManager.queryForList(sql);
		
		for(Map<String,String> map : list){
			flag = StringUtils.ObjToString(map.get("FLAG"));
		}
		
		return flag;
	}
	
	public static String judgeLineFlow(PowerDevice pd, PowerDevice src, List<PowerDevice> searchedList) {
		String lineFlow = EMSService.getService().getLineFlow(pd.getPowerStationID(), pd.getPowerDeviceID());
		String runMode = getRunMode(pd);
		if(!src.equals(pd))
			searchedList.add(src);
		if(!src.equals(pd) && !pd.getDeviceStatus().equals("0") && src.getDeviceStatus().equals("0"))
			return "1";
		else if(runMode.equals("2"))
			return "1";
		else if(runMode.equals("4"))
			return "2";
		else if(runMode.equals("3")) {
			List<PowerDevice> lnList1 = RuleExeUtil.getLineOtherSideList(pd);
			List<PowerDevice> lnList2 = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine , SystemConstants.PowerTransformer+","+ SystemConstants.Switch, true, true, true);
			
			for(PowerDevice ln : lnList1) {
				if(searchedList.contains(ln))
					continue;
				searchedList.add(ln);
				String flow = judgeLineFlow(ln, src, searchedList);
				if(src.equals(pd)) {
					if(flow.equals("2"))
						return "1";
				}
				else
					return flow;
			}
			for(PowerDevice ln : lnList2) {
				if(searchedList.contains(ln))
					continue;
				searchedList.add(ln);
				String flow = judgeLineFlow(ln, src, searchedList);
				if(src.equals(pd)) {
					if(flow.equals("2"))
						return "2";
				}
				else
					return flow;
				
			}
		}
		else {
			List<PowerDevice> lnList = RuleExeUtil.getLineOtherSideList(pd);
			if(lnList.size() == 0)
				return "2";
			PowerDevice lnOther = null;
			boolean isExistOn = false;
			for(PowerDevice ln : lnList){
				if(!RuleExeUtil.isSourceSide(ln))
					lnOther = ln;
				if(ln.getDeviceStatus().equals("0")){
					isExistOn = true;
				}
			}
			if(lnOther == null)
				lnOther = lnList.get(0);
			if(pd.getDeviceStatus().equals("0") && !isExistOn)
				return "2";
			else if(!pd.getDeviceStatus().equals("0") && isExistOn)
				return "1";
			
			
			String mode = getRunMode(lnList.get(0));
			if(RuleExeUtil.isSourceSide(pd)) { //电源侧
				if(!RuleExeUtil.isSourceSide(lnOther))
					return "1";
				else if(runMode.equals("0") && mode.equals("1"))
					return "2";
				else if(runMode.equals("1") && mode.equals("0"))
					return "1";
				List<PowerDevice> lnList1 = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine , SystemConstants.PowerTransformer, false, false, true);
				List<PowerDevice> lnList2 = RuleExeUtil.getDeviceList(lnOther, SystemConstants.InOutLine , SystemConstants.PowerTransformer, false, false, true);
				if(lnList1.size() > 0 && lnList2.size() == 0)
					return "2";
				else if(lnList1.size() == 0 && lnList2.size() > 0)
					return "1";
				else
					return lineFlow;
			}
			else {
				if(!mode.equals("0"))
					return "2";
				else if(lineFlow.equals("1") || lineFlow.equals("2"))
					return lineFlow;
				else
					return "2";
			}
		}
		return lineFlow;
		/*
		List<PowerDevice> lineList = getLineAllSideList(pd);
		
		boolean isExistOn = false;
		
		for(int i=0;i<lineList.size();i++){
			PowerDevice lpd=lineList.get(i);
			String status=lpd.getDeviceStatus();
			if(status.equals("0")){
				isExistOn = true;
				break;
			}
		}
		
		if(isExistOn) {
			for(int i=0;i<lineList.size();i++){
				PowerDevice lpd=lineList.get(i);
				String status=lpd.getDeviceStatus();
				if(!status.equals("0")){
					lineList.remove(i);
					i--;
				}
			}
		}
		
		//获取可能是电源侧的线路
		HashMap<String, List<PowerDevice>> maxLevelMap = new HashMap<String, List<PowerDevice>>();
		double maxLevel = 0;
		for(PowerDevice line : lineList) {
			double level = RuleUtil.getLineTurnLevel(line);
//			if(line.getDeviceStatus().equals("0")){
				if(maxLevel <= level) {
					maxLevel = level;
					if(!maxLevelMap.containsKey(String.valueOf(maxLevel)))
						maxLevelMap.put(String.valueOf(maxLevel), new ArrayList<PowerDevice>());
					maxLevelMap.get(String.valueOf(maxLevel)).add(line);
				}
//			}
		}
		List<PowerDevice> sourcrList = maxLevelMap.get(String.valueOf(maxLevel));
		
		//确定电源侧线路
		PowerDevice source = null;
		if(sourcrList.size() == 1) {
			source = sourcrList.get(0);
			return source.equals(pd)?"2":"1";
		}
		if(RuleExeUtilYNDD.isSourceSide(pd) && RuleExeUtilYNDD.getLineCable(pd).size() == 0) {
			String flow = EMSService.getService().getLineFlow(pd.getPowerStationID(), pd.getPowerDeviceID());
			if(flow != null && !flow.equals("-1")) {
				//System.out.println(pd.getPowerStationName()+pd.getPowerDeviceName()+(flow.equals("1")?"进线":"出线"));
				return flow;
			}
		}
		
		List<PowerDevice> lnList = RuleExeUtilYNDD.getLineCable(pd);
		if(lnList.size() > 0) {
			List<PowerDevice> lnList2 = RuleExeUtilYNDD.getLineOtherSideList(lnList.get(0));
			if(lnList2.size() > 0 && !lnList2.get(0).getDeviceStatus().equals("0")) {
				List<PowerDevice> lnList3 = RuleExeUtilYNDD.getLineOtherSideList(pd);
				if(lnList3.size() > 0) {
					PowerDevice source2 = lnList3.get(0);
					CBSystemConstants.putLineSource(source2.getPowerDeviceID(), source2);
					List<PowerDevice> lnList4 = new ArrayList<PowerDevice>();
					lnList4.add(pd);
					CBSystemConstants.putLineLoad(pd.getPowerDeviceID(), lnList4);
				}
				return "1";
			}
		}
		
		Map<PowerDevice,String> stationlines=new LinkedHashMap<PowerDevice,String>();
		String preStationID = "";
		for(PowerDevice dev : sourcrList) {
			if(!dev.getPowerStationID().equals(preStationID)) {
				stationlines.put(dev, "-1");
				preStationID = dev.getPowerStationID();
			}
		}
		if(CBSystemConstants.LineSource.containsKey(pd.getPowerDeviceID()))
			source = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
		else {
			LineTransChooseDialog linetransChoose=new LineTransChooseDialog(SystemConstants.getMainFrame(), true, stationlines, "选择电源侧");
			source=linetransChoose.getReslut();
			if(source == null)
				return "-1";
			List<PowerDevice> loadList = new ArrayList<PowerDevice>();
			for(PowerDevice line : lineList) {
				if(!line.equals(source))
					loadList.add(line);
			}
			
			for(PowerDevice line : lineList) {
				CBSystemConstants.putLineSource(line.getPowerDeviceID(), source);
				CBSystemConstants.putLineLoad(line.getPowerDeviceID(), loadList);
			}
		}
				
		return source.equals(pd)?"2":"1";
		*/
		
	}
	
	/**
	 * 判断线路是否双端电源线路
	 * @param pd
	 * @return
	 */
	public static boolean isDoubleSource(PowerDevice pd) {
		List<PowerDevice> lineList = getLineAllSideList(pd);
		if(lineList.size() == 1)
			return false;
		

		for(PowerDevice line : lineList) {
			double level = RuleUtil.getLineTurnModel(line);
			if(level == 0) {
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 判断线路开关是进线开关还是出线开关 1进线开关 2出线开关-1待用线路开关
	 * @param pd
	 * @return
	 */
	public static String judgeSwitchFlow(PowerDevice pd) {
		List<PowerDevice> lnList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
		if(lnList.size() == 0)
			return "1";
		else if((lnList.get(0).getPowerDeviceName().contains("待用")||lnList.get(0).getPowerDeviceName().contains("未运行")||lnList.get(0).getPowerDeviceName().contains("备用"))){
			return "-1";
		}
		else
			return judgeLineFlow(lnList.get(0));
	}
	
	/**
	 * 判断线路刀闸是进线刀闸还是出线刀闸 1进线刀闸 2出线刀闸
	 * @param pd
	 * @return
	 */
	public static String judgeKnifeFlow(PowerDevice pd) {
		List<PowerDevice> lnList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine);
		if(lnList.size() == 0)
			return "1";
		else
			return judgeLineFlow(lnList.get(0));
	}

	/**
	 * 判断两条线路段是否属于同一条线路
	 * @param pd1
	 * @param pd2
	 * @return
	 */
	public static boolean isSameLine(PowerDevice pd1, PowerDevice pd2) {
		if (pd1.equals(pd2))
			return true;
		if(!CBSystemConstants.getEquiplinemap().containsKey(pd1.getPowerDeviceID()))
			return false;
		if(!CBSystemConstants.getEquiplinemap().containsKey(pd2.getPowerDeviceID()))
			return false;
		String lineID1 = CBSystemConstants.getEquiplinemap().get(pd1.getPowerDeviceID()).get(0);
		String lineID2 = CBSystemConstants.getEquiplinemap().get(pd2.getPowerDeviceID()).get(0);
		if (lineID1.equals(lineID2))
			return true;
		else
			return false;
	}

	/**
	 * 判断主变上是否有负载
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isTFOnLoad(PowerDevice pd) {
		List<PowerDevice> mlList = getDeviceList(pd,
				SystemConstants.MotherLine, SystemConstants.PowerTransformer,
				false, true, true);
		for (PowerDevice ml : mlList) {
			if (ml.getPowerVoltGrade() != pd.getPowerVoltGrade())
				return true;
		}
		return false;
	}
	
	/**
	 * 判断设备上是否有负载
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isDeviceOnLoad(PowerDevice pd) {
		if(pd.getDeviceType().equals(SystemConstants.PowerTransformer))
			return isTFOnLoad(pd);
		else if(pd.getDeviceType().equals(SystemConstants.MotherLine))
			return isMLOnLoad(pd);
		else if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			if(!pd.getDeviceStatus().equals("0"))
				return false;
			else {
				List<PowerDevice> lineList = RuleExeUtil.getLineOtherSideList(pd);
				boolean isExistOn = false;
				for (PowerDevice line : lineList) {
					if (line.getDeviceStatus().equals("0")) {
						isExistOn = true;
						break;
					}
				}
				return isExistOn;
			}
		}
		return false;
	}

	/***
	 * 母线方法
	 */

	/**
	 * 判断母线上是否有负载
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isMLOnLoad(PowerDevice pd) {
		List<PowerDevice> lineList = null;
		if (RuleExeUtil.isSourceSide(pd)) {
			lineList = getDeviceList(pd, SystemConstants.PowerTransformer+","+ SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
			for(Iterator it = lineList.iterator(); it.hasNext();) { //只保留出线
				PowerDevice src = (PowerDevice)it.next();
				if(src.getDeviceType().equals(SystemConstants.InOutLine) && !RuleExeUtil.getLineFlow(src).equals("2"))
					it.remove();
				else if(src.getDeviceType().equals(SystemConstants.PowerTransformer) && !isTFOnLoad(src)) //主变是否有负载
					it.remove();
			}
		}
		else {
			lineList = getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
			for(Iterator it = lineList.iterator(); it.hasNext();) { //去掉进线
				PowerDevice src = (PowerDevice)it.next();
				if(src.getDeviceType().equals(SystemConstants.InOutLine) && 
						RuleExeUtil.getLineFlow(src).equals("1"))
					it.remove();
			}
			
			for (Iterator<PowerDevice> itr = lineList.iterator(); itr.hasNext();) {
				PowerDevice line = itr.next();
				List<PowerDevice> loadList = RuleExeUtil.getLineOtherSideList(line);
				boolean isOnLoad = true;
				for (PowerDevice load : loadList) {
					if (!load.getDeviceStatus().equals("0"))
						isOnLoad = false;
				}
				if (!isOnLoad)
					itr.remove();
			}

		}
		return lineList.size() > 0;
	}
	
	/**
	 * 判断母线上是否有负载或空载线路
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isMLOnLoadOrNone(PowerDevice pd) {
		List<PowerDevice> lineList = null;
		if (RuleExeUtil.isSourceSide(pd)) {
			lineList = getDeviceList(pd, SystemConstants.PowerTransformer,
					SystemConstants.PowerTransformer, false, true, true);
			if(lineList.size() == 0) {
				lineList = getDeviceList(pd, SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "",false, false, true, true);
				for(int i=0;i<lineList.size();i++){//如果母联开关还直接连接其他电源线路，则去除
					List<PowerDevice> sourceLineList = getDeviceList(lineList.get(i), pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "",
							"", false, false, false, true);
					boolean isSource =false;
					for(PowerDevice line:sourceLineList){
						if(isSourceSide(line)){
							isSource=true;
							break;
						}
					}
					if(isSource){
						lineList.remove(i);
						i--;
					}
				}
			}
		}
		
		else {
			lineList = getDeviceList(pd, SystemConstants.InOutLine,
					SystemConstants.PowerTransformer, false, true, true);
		}
		return lineList.size() > 0;
	}

	/**
	 * 判断母线上是否连接到电源
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isMLOnSource(PowerDevice pd) {
		List<PowerDevice> lineList = null;
		if (RuleExeUtil.isSourceSide(pd)) {
			lineList = getDeviceList(pd, SystemConstants.InOutLine+","+SystemConstants.MotherLine,
					SystemConstants.PowerTransformer+","+SystemConstants.DistributionPowerTransform, false, true, false);
			for (Iterator<PowerDevice> itr = lineList.iterator(); itr.hasNext();) {
				PowerDevice dev = itr.next();
				if(!dev.getDeviceStatus().equals("0"))
					itr.remove();
			}
		}
		else {
			lineList = getDeviceList(pd, SystemConstants.PowerTransformer,
					SystemConstants.PowerTransformer, false, false, true);
			for (Iterator<PowerDevice> itr = lineList.iterator(); itr.hasNext();) {
				PowerDevice dev = itr.next();
				if(!dev.getDeviceStatus().equals("0"))
					itr.remove();
			}
		}
		return lineList.size() > 0;
	}
	
	/**
	 * 判断母线上是否直接连接到电源
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isMLOnSourceDirect(PowerDevice pd) {
		List<PowerDevice> lineList = null;
		if (RuleExeUtil.isSourceSide(pd))
			lineList = getDeviceList(pd, SystemConstants.InOutLine,
					SystemConstants.PowerTransformer, false, true, false);
		else
			lineList = getDeviceList(pd, SystemConstants.PowerTransformer,
					SystemConstants.PowerTransformer, false, true, true);
		return lineList.size() > 0;
	}

	/**
	 * 获取双母接线方式母线的另一段母线
	 * 
	 * @param pd
	 * @return
	 */
	public static PowerDevice getMLDoubleOther(PowerDevice pd) {
		PowerDevice ml = null;
		List<PowerDevice> swList = getDeviceList(pd, SystemConstants.Switch,
				SystemConstants.PowerTransformer,
				CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
		if (swList.size() > 0) {
			PowerDevice sw = swList.get(0);
			List<PowerDevice> mlList = getDeviceList(sw,
					SystemConstants.MotherLine,
					SystemConstants.PowerTransformer, "",
					CBSystemConstants.RunTypeSideMother, false, true, true,
					true);
			mlList.remove(pd);
			if (mlList.size() > 0)
				ml = mlList.get(0);
		}
		return ml;
	}
	
	/**
	 * 获取母线分段连接的其他母线
	 * 
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getMLLinkOther(PowerDevice pd) {
		List<PowerDevice> mlList = new ArrayList<PowerDevice>();
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) {
			return mlList;
		}
		
		
		mlList = getDeviceList(pd, SystemConstants.MotherLine,
				SystemConstants.PowerTransformer,
				"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
			PowerDevice sm = getMLDoubleOther(pd);
			mlList.remove(sm);
		}
	
		
		
		return mlList;
	}
	
	/**
	 * 获取双母接线方式母线的另一段或多段母线 （3母）
	 * 
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getMLDoubleOtherList(PowerDevice pd) {
		List<PowerDevice> mlList = new ArrayList<PowerDevice>();
		List<PowerDevice> swList = getDeviceList(pd, SystemConstants.Switch,
				SystemConstants.PowerTransformer,
				CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
		
		for (PowerDevice sw : swList) {
			List<PowerDevice>tempmlList = getDeviceList(sw,
					SystemConstants.MotherLine,
					SystemConstants.PowerTransformer, "",
					CBSystemConstants.RunTypeSideMother, false, true, true,
					true);
			for (PowerDevice dev : tempmlList) {
				if(!mlList.contains(dev))
					mlList.add(dev);
			}
			
		}
		mlList.remove(pd);
		return mlList;
	}
	
	
	/**
	 * 获取母线的母联开关
	 * 
	 * @param pd
	 * @return
	 */
	public static PowerDevice getMLSwitch(PowerDevice pd) {
		PowerDevice mlDev = null;
		PowerDevice mlOther = RuleExeUtil.getMLDoubleOther(pd);
		if(mlOther != null)
			mlDev = RuleExeUtil.getMLSwitch(pd, mlOther);
		return mlDev;
	}

	/**
	 * 获取母线的母联开关或刀闸
	 * 
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getMLSwitchList(PowerDevice pd) {
		List<PowerDevice> devList = new ArrayList<PowerDevice>();
		List list = getDeviceList(pd, SystemConstants.Switch,
				SystemConstants.PowerTransformer, true, true, true);
		if (list != null || list.size() > 0) {
			for (Iterator<PowerDevice> itr = list.iterator(); itr.hasNext();) {
				PowerDevice dev = itr.next();
				if (dev.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchML)
						|| dev.getDeviceRunType().equals(
								CBSystemConstants.RunTypeSwitchMLPL))
					devList.add(dev);
			}
		}

		list = getDeviceList(pd, SystemConstants.SwitchSeparate,
				SystemConstants.PowerTransformer, true, true, true);
		if (list != null || list.size() > 0) {
			for (Iterator<PowerDevice> itr = list.iterator(); itr.hasNext();) {
				PowerDevice dev = itr.next();
				if (dev.getDeviceRunType().equals(
						CBSystemConstants.RunTypeKnifeML))
					devList.add(dev);
			}
		}
		return devList;
	}

	/**
	 * 获取两母线之间的母联开关或者母联刀闸
	 * 
	 * @param pdline
	 *            母线一
	 * @param devline
	 *            母线二
	 * @return 母联开关或者母联刀闸
	 * */
	public static PowerDevice getMLSwitch(PowerDevice pd1, PowerDevice pd2) {
		List<PowerDevice> list1 = getMLSwitchList(pd1);
		List<PowerDevice> list2 = getMLSwitchList(pd2);
		for (PowerDevice dev : list1) {
			if (list2.contains(dev))
				return dev;
		}
		return null;
	}

	/**
	 * 判断母联开关是否可以直接断开
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isMLSwitchCanOff(PowerDevice pd) {
		if (!pd.getDeviceStatus().equals("0"))
			return true;
		if (pd.getPowerVoltGrade() != CBSystemConstants.getPowerStation(
				pd.getPowerStationID()).getPowerVoltGrade())
			return true;
		List<PowerDevice> mllist = RuleExeUtil.getDeviceList(pd,
				SystemConstants.MotherLine, SystemConstants.PowerTransformer,
				false, true, false);
		for (PowerDevice ml : mllist) {
			List<PowerDevice> linelist = RuleExeUtil.getDeviceList(ml,
					SystemConstants.InOutLine,
					SystemConstants.PowerTransformer, false, true, false);
			if (linelist.size() == 0) {
				boolean isOtherMLExist = false;
				List<PowerDevice> swlist = RuleExeUtil.getDeviceList(ml,
						SystemConstants.Switch,
						SystemConstants.PowerTransformer, false, true, false);
				for (PowerDevice sw : swlist) {
					if (!sw.equals(pd)
							&& sw.getDeviceRunType().equals(
									CBSystemConstants.RunTypeSwitchML)
							&& sw.getDeviceStatus().equals("0")) {
						isOtherMLExist = true;
						break;
					}
				}
				if (!isOtherMLExist)
					return false;
			}
		}
		return true;
	}

	/**
	 * 根据接地变对母线排序
	 * 
	 * @param mlList
	 * @param isEarthFirst
	 */
	public static void swapMotherLineByEarth(List<PowerDevice> mlList,
			boolean isEarthFirst) {
		for (PowerDevice ml : mlList) {
			List<PowerDevice> etlist = RuleExeUtil.getDeviceList(ml, SystemConstants.EarthingTransformer, SystemConstants.PowerTransformer, true, true, false);
			if (etlist.size() > 0) {
				if (isEarthFirst) {
					mlList.remove(ml);
					mlList.add(0, ml);
					return;
				} else {
					mlList.remove(ml);
					mlList.add(ml);
					return;
				}
			}
		}
	}

	/**
	 * 根据接地变对主变排序
	 * 
	 * @param tfList
	 * @param tfMap
	 * @param isEarthFirst
	 *            是否连接接地变的在前
	 */
	public static void swapTransformerByEarth(List<PowerDevice> tfList,
			HashMap<PowerDevice, List<PowerDevice>> tfMap, boolean isEarthFirst) {
		for (PowerDevice tf : tfList) {
			for (PowerDevice ml : tfMap.get(tf)) {
				if (ml == null)
					continue;
				List<PowerDevice> etlist = RuleExeUtil.getDeviceList(ml,
						SystemConstants.EarthingTransformer,
						SystemConstants.PowerTransformer, true, true, false);
				if (etlist.size() > 0) {
					if (isEarthFirst) {
						tfList.remove(tf);
						tfList.add(0, tf);
						return;
					} else {
						tfList.remove(tf);
						tfList.add(tf);
						return;
					}
				}
			}
		}
	}

	/**
	 * 按电压等级查找与主变相连的非旁路母线
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getMotherLineByVol(PowerDevice pd,
			double vol) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.MotherLine);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> list = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator<PowerDevice> itr = list.iterator(); itr.hasNext();) {
			PowerDevice dev = itr.next();
			if (dev.getPowerVoltGrade() != vol
					|| dev.getDeviceRunType().equals(
							CBSystemConstants.RunTypeSideMother))
				itr.remove();
		}
		return list;
	}

	/**
	 * 按电压等级查找与主变相连的所有非旁路母线
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getMotherLineAllByVol(PowerDevice pd,
			double vol) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.MotherLine);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", false);
		cs.execute(inPara, outPara);
		List<PowerDevice> list = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator<PowerDevice> itr = list.iterator(); itr.hasNext();) {
			PowerDevice dev = itr.next();
			if (dev.getPowerVoltGrade() != vol
					|| dev.getDeviceRunType().equals(
							CBSystemConstants.RunTypeSideMother))
				itr.remove();
		}
		return list;
	}

	/**
	 * 查找主变负荷侧的母线
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getMotherLineLoad(PowerDevice pd) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.MotherLine);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnBusbarSection", false);
		inPara.put("isStopOnTagDevType", false);
		cs.execute(inPara, outPara);
		List<PowerDevice> list = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator<PowerDevice> itr = list.iterator(); itr.hasNext();) {
			PowerDevice dev = itr.next();
			if (dev.getPowerVoltGrade() == pd.getPowerVoltGrade())
				itr.remove();
		}
		return list;
	}

	/**
	 * 查找主变负荷侧的母线
	 * 
	 * @param 源设备
	 * @param 目标设备类型
	 * */
	public static List<PowerDevice> getMotherLineLoadAll(PowerDevice pd) {
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.MotherLine);
		inPara.put("excDevType", SystemConstants.PowerTransformer);
		inPara.put("isSearchOffPath", true);
		inPara.put("isStopOnBusbarSection", true);
		inPara.put("isStopOnTagDevType", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> list = (List<PowerDevice>) outPara
				.get("linkedDeviceList");
		for (Iterator<PowerDevice> itr = list.iterator(); itr.hasNext();) {
			PowerDevice dev = itr.next();
			if (dev.getPowerVoltGrade() == pd.getPowerVoltGrade())
				itr.remove();
		}
		return list;
	}

	/**
	 * 获得主变电压等级
	 * */
	public static List<Double> getTransformerVol(PowerDevice pd) {
		List<Double> vols = new ArrayList<Double>();
		List<PowerDevice> switchs = RuleUtil.getDirectDevice(pd,
				SystemConstants.Switch + "," + SystemConstants.SwitchSeparate);
		for (PowerDevice dev : switchs) {
			if (!vols.contains(dev.getPowerVoltGrade()))
				vols.add(dev.getPowerVoltGrade());
		}
		Collections.sort(vols);
		return vols;
	}

	/**
	 * 获取主变指定电压等级侧的电压
	 * 
	 * @param pd
	 * @param type
	 * @return
	 */
	public static double getTransformerVolByType(PowerDevice pd, String type) {
		List<Double> vols = RuleUtil.getTransformerVol(pd);
		if (vols.size() <= 1) {
			return 0;
		}
		if (vols.size() == 2 && type.equals("middle")) {
			return 0;
		}
		double vol = 0;
		if (type.equals("high"))
			vol = vols.get(vols.size() - 1);
		else if (type.equals("middle"))
			vol = vols.get(1);
		else if (type.equals("low"))
			vol = vols.get(0);
		return vol;
	}
	
	/**
	 * 获得主变高压侧开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getTransformerSwitchHigh(PowerDevice pd) {
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, "", "", CBSystemConstants.RunTypeSideMother+","+CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeKnifeQT, false, true, false, true);
		List<Double> vols = RuleUtil.getTransformerVol(pd);
		if(vols.size() > 0) {
			for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
				PowerDevice sw = it.next();
				if(sw.getPowerVoltGrade() != vols.get(vols.size()-1))
					it.remove();
				else if(pd.getPowerVoltGrade() < vols.get(vols.size()-1))
					it.remove();
			}
		}
		return swList;
	}
	
	/**
	 * 获得主变中压侧开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getTransformerSwitchMiddle(PowerDevice pd) {
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, "", "", CBSystemConstants.RunTypeSideMother+","+CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeKnifeQT, false, true, false, true);
		List<Double> vols = RuleUtil.getTransformerVol(pd);
		if(vols.size() == 3) {
			for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
				PowerDevice sw = it.next();
				if(sw.getPowerVoltGrade() != vols.get(1))
					it.remove();
			}
		}
		else
			swList.clear();
		return swList;
	}
	
	/**
	 * 获得主变低压侧开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getTransformerSwitchLow(PowerDevice pd) {
		List<PowerDevice> swList = new ArrayList<PowerDevice>();
				
		if(pd.getPowerStationID().equals("SS-154")||pd.getPowerStationID().equals("SS-102")||pd.getPowerStationID().equals("SS-7")){//玉溪220kV红塔山变、110kV纳溪变、220kV高古楼变低压侧手车拓扑问题
			swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, "", "", CBSystemConstants.RunTypeSideMother+","+CBSystemConstants.RunTypeSwitchML, false, true, true, true);
			List<Double> vols = RuleUtil.getTransformerVol(pd);
			if(vols.size() > 0) {
				for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
					PowerDevice sw = it.next();
					if(sw.getPowerVoltGrade() != vols.get(0))
						it.remove();
				}
			}
		}else{
			swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, "", "", CBSystemConstants.RunTypeSideMother+","+CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeKnifeQT, false, true, true, true);
			List<Double> vols = RuleUtil.getTransformerVol(pd);
			if(vols.size() > 0) {
				for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
					PowerDevice sw = it.next();
					if(sw.getPowerVoltGrade() != vols.get(0))
						it.remove();
				}
			}
		}
		
		return swList;
	}

	/**
	 * 获得两台主变高压侧连接路径
	 * */
	public static List<PowerDevice> getTransformersLinkDevice(PowerDevice pd1,
			PowerDevice pd2) {
		List<PowerDevice> path = new ArrayList<PowerDevice>();
		List<PowerDevice> mlList1 = getMotherLineByVol(pd1,
				pd1.getPowerVoltGrade());
		List<PowerDevice> mlList2 = getMotherLineByVol(pd2,
				pd2.getPowerVoltGrade());
		if(mlList1.size() == 0 || mlList2.size() == 0)
			return path;
		if (mlList1.get(0).getDeviceRunModel()
				.equals(CBSystemConstants.RunModelDoubleMotherLine)) {
			PowerDevice sw = RuleExeUtil.getMLSwitch(mlList1.get(0),
					mlList2.get(0));
			if(sw!=null){
				path.add(sw);	
			}
		} else {
			path = getPathByDevice(mlList1.get(0), mlList2.get(0),
					SystemConstants.PowerTransformer,
					CBSystemConstants.RunTypeSwitchXL, true, false);
		}
		return path;
	}
	
	/**
	 * 获得两台主变高压侧之间断开的母联开关
	 * */
	public static List<PowerDevice> getTransformersLinkMLSwitchOff(PowerDevice pd1,
			PowerDevice pd2) {
		List<PowerDevice> devList = new ArrayList<PowerDevice>();
		List<PowerDevice> path = getTransformersLinkDevice(pd1, pd2);
		if(path != null) {
			for (PowerDevice dev : path) {
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) && !dev.getDeviceStatus().equals("0")) {
					devList.add(dev);
				}
			}
		}
		return devList;
	}

	/**
	 * 查找并列运行的主变
	 * 
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getTransformerBL(PowerDevice pd) {
		List<PowerDevice> tfList = new ArrayList<PowerDevice>();
		List<PowerDevice> mlList = getMotherLineByVol(pd, pd.getPowerVoltGrade());
//		if (mlList.size() == 0) {
//			
//			List<PowerDevice> lineList = RuleExeUtilYNDD.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
//			if(lineList.size() > 0) {
//				PowerDevice line = lineList.get(0);
//				
//				for(Iterator it = CBSystemConstants.getMapPowerStationDevice().get(pd.getPowerStationID()).values().iterator(); it.hasNext();) {
//					PowerDevice dev = (PowerDevice)it.next();
//					if(ml.getPowerVoltGrade() != pd.getPowerVoltGrade())
//						it.remove();
//				}
//				
//				List<PowerDevice> otherList = RuleExeUtilYNDD.getLineOtherSideList(line);
//				for (PowerDevice other : otherList) {
//					List<PowerDevice> line1List = RuleExeUtilYNDD.getDeviceList(other, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
//				}
//			}
//			return tfList;
//		}
		if (mlList.size() == 2) {// 如果有两条母线则剔除与主变不连通的母线
			List<PowerDevice> needDeleteMLList = new ArrayList<PowerDevice>();
			for (PowerDevice mlTemp : mlList) {
				List<PowerDevice> pathList = getPathByDevice(pd, mlTemp, SystemConstants.PowerTransformer, null, true, true);
				if (!(pathList != null && pathList.size() > 0))
					needDeleteMLList.add(mlTemp);
			}
			mlList.removeAll(needDeleteMLList);
		}
		if(mlList.size() > 0){
			tfList = getDeviceList(mlList.get(0), SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, false, true);
			tfList.remove(pd);
		}
		swapDeviceList(tfList);
		return tfList;
	}
	
	public static List<PowerDevice> getTransformerSource(PowerDevice pd) {
		List<PowerDevice> lnList = getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
		if(lnList.size() > 0)
			return lnList;
		List<PowerDevice> mlList = getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
		for(Iterator it = mlList.iterator(); it.hasNext();) {
			PowerDevice ml = (PowerDevice)it.next();
			if(ml.getPowerVoltGrade() != pd.getPowerVoltGrade())
				it.remove();
		}
		if(mlList.size() > 0) {
			lnList = getDeviceList(mlList.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, false, true);
		}
		return lnList;
	}

	/**
	 * 判断主变是否并列运行
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isTransformerBL(PowerDevice pd) {
		boolean isBL = getTransformerBL(pd).size() == 0 ? false : true;
		return isBL;
	}
	
	/**
	 * 判断主变是否带线路负荷
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isTransformerHasLoad(PowerDevice pd) {
		boolean result = false;
		double lowVol = RuleExeUtil.getTransformerVolByType(pd, "low");
		PowerDevice lowSwitch = RuleExeUtil.getTransformerSwitch(pd, lowVol);
		if(lowSwitch==null)
			return true;
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(lowSwitch, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, false, true);
		for(PowerDevice ml : mlList) {
			List<PowerDevice> lnList = RuleExeUtil.getDeviceList(ml, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
			if(lnList.size() > 0) {
				result = true;
				break;
			}
		}
		return result;
	}

	/**
	 * 按电压等级查找主变开关、线路开关（代主变开关）、主变刀闸
	 * 
	 * @param pd
	 * @param type
	 * @return
	 */
	public static List<PowerDevice> getTransformerSwitchKnife(PowerDevice pd,
			double vol) {
		List<PowerDevice> tagSwitch = new ArrayList<PowerDevice>();

		List<PowerDevice> switchs = getDeviceList(pd, SystemConstants.Switch,
				"", true, true, true);
		for (PowerDevice dev : switchs) {
			if (dev.getPowerVoltGrade() == vol) {
				tagSwitch.add(dev);
			}
		}
		List<Double> vols = getTransformerVol(pd);
		if(vols.size() == 0)
			return tagSwitch;
		if (tagSwitch.size() == 0) {
			if (vol == vols.get(vols.size() - 1)) { // 高压侧
				List<PowerDevice> mlList = getMotherLineByVol(pd, vol);
				if (mlList.size() > 0) {
					switchs = getDeviceList(mlList.get(0),
							SystemConstants.Switch, "", true, true, true);
					for (PowerDevice dev : switchs) {
						if (dev.getDeviceRunType().equals(
								CBSystemConstants.RunTypeSwitchXL))
							tagSwitch.add(dev);
					}
				}
			}
		}
		if (tagSwitch.size() == 0 || !CBSystemConstants.isMaxRangeOffTicket || vol == vols.get(vols.size() - 1)) {
			switchs = getDeviceList(pd, SystemConstants.SwitchSeparate, "",
					true, true, true);
			for (PowerDevice dev : switchs) {
				if (dev.getPowerVoltGrade() == vol
						&& (dev.getDeviceRunType().equals(
								CBSystemConstants.RunTypeKnifeZB) || dev
								.getDeviceRunType().equals(
										CBSystemConstants.RunTypeKnifeZBS))) {
					tagSwitch.add(dev);
					// break;
				}
			}
		}
		return tagSwitch;
	}
	
	/**
	 * 获取主变开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getTransformerSwitchByVol(PowerDevice pd, double vol) {
		List<PowerDevice> sourceSwitchList = new ArrayList<PowerDevice>();
		List<PowerDevice> allSwitchList = getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		for (PowerDevice sw : allSwitchList) {
			if (sw.getPowerVoltGrade() == vol)
				sourceSwitchList.add(sw);
		}
		if(sourceSwitchList.size() == 0) {
			List<PowerDevice> allMLList = getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			for (PowerDevice ml : allMLList) {
				if (ml.getPowerVoltGrade() == vol) {
					allSwitchList = getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					//线路开关放前面
					for (PowerDevice sw : allSwitchList) {
						if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL))
							sourceSwitchList.add(sw);
					}
					for (PowerDevice sw : allSwitchList) {
						if(!sourceSwitchList.contains(sw))
							sourceSwitchList.add(sw);
					}
					break;
				}
			}
		}
		else {
			for (PowerDevice sw : sourceSwitchList) {
				if(RuleExeUtil.isSwMiddleInThreeSecond(sw)) {
					sourceSwitchList.remove(sw);
					sourceSwitchList.add(0, sw);
					break;
				}
			}
		}
		return sourceSwitchList;
	}
	
	
	/**
	 * 获取主变开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getAllTransformerSwitchByVol(PowerDevice pd, double vol) {
		List<PowerDevice> sourceSwitchList = new ArrayList<PowerDevice>();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		
		for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = (PowerDevice) it2.next();
			if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
				if(dev.getPowerVoltGrade() == vol){
					sourceSwitchList.add(dev);
				}
			}
		}
		
		return sourceSwitchList;
	}
	
	
	/**
	 * 获取主变电源侧开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getTransformerSwitchSource(PowerDevice pd) {
		
		return getTransformerSwitchByVol(pd, pd.getPowerVoltGrade());
	}
	
	/**
	 * 获取主变电源侧刀闸
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getTransformerKnifeSource(PowerDevice pd) {
		List<PowerDevice> sourceKnifeList = new ArrayList<PowerDevice>();
		List<PowerDevice> allKnifeList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeZBS+","+CBSystemConstants.RunTypeKnifeZB, "", true, true, true, true);
		for (PowerDevice dev : allKnifeList) {
			if (dev.getPowerVoltGrade() == pd.getPowerVoltGrade())
				sourceKnifeList.add(dev);
		}
		return sourceKnifeList;
	}
	
	/**
	 * 获取主变负荷侧刀闸
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getTransformerKnifeLoad(PowerDevice pd) {
		List<PowerDevice> sourceKnifeList = new ArrayList<PowerDevice>();
		List<PowerDevice> allKnifeList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeZBS+","+CBSystemConstants.RunTypeKnifeZB, "", true, true, true, true);
		for (PowerDevice dev : allKnifeList) {
			if (dev.getPowerVoltGrade() != pd.getPowerVoltGrade())
				sourceKnifeList.add(dev);
		}
		return sourceKnifeList;
	}

	/**
	 * 获取主变负荷侧开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getTransformerSwitchLoad(PowerDevice pd) {
		List<PowerDevice> loadSwitchList = new ArrayList<PowerDevice>();
		List<PowerDevice> allSwitchList = getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		for (PowerDevice dev : allSwitchList) {
			if (dev.getPowerVoltGrade() != pd.getPowerVoltGrade())
				loadSwitchList.add(dev);
		}
		return loadSwitchList;
	}
	
	/**
	 * 判断开关是否主变中压侧开关
	 * @param pd
	 * @return
	 */
	public static boolean isSwitchMiddleVolSide(PowerDevice pd) {
		boolean result = false;
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
		if(tfList.size() > 0) {
			double vol = RuleExeUtil.getTransformerVolByType(tfList.get(0), "middle");
			List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchKnife(tfList.get(0), vol);
			result = swList.contains(pd);
		}
		return result;
	}
	
	/**
	 * 判断开关是否主变低压侧开关
	 * @param pd
	 * @return
	 */
	public static boolean isSwitchLowVolSide(PowerDevice pd) {
		boolean result = false;
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
		if(tfList.size() > 0) {
			double vol = RuleExeUtil.getTransformerVolByType(tfList.get(0), "low");
			List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchKnife(tfList.get(0), vol);
			result = swList.contains(pd);
		}
		return result;
	}
	
	/**
	 * 判断刀闸是为电源侧负荷侧，1:电源侧;2:负荷侧;9:不区分电源侧负荷侧
	 * 
	 * @param pd
	 * @return
	 */
	public static String JudgeknifeIsPowerSide(PowerDevice pd) {
		List<PowerDevice> list = null;
		list = RuleUtil.getDirectDevice(pd, SystemConstants.Switch);// 获取连接的开关
		if (list == null || list.size() == 0) {
			list = RuleExeUtil.getDeviceList(pd,  SystemConstants.Switch,  SystemConstants.PowerTransformer, true, true, true);
			if (list == null || list.size() == 0)
				return "9";
		}
		PowerDevice directSwitch = (PowerDevice) list.get(0);// 获取刀闸连接的开关
		// 判断是否为其他开关
		if(directSwitch.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) //3/2接线不区分刀闸拉开顺序
			return "9";
		else if (directSwitch.getDeviceRunType().equals(
				CBSystemConstants.RunTypeSwitchXL)||directSwitch.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchQT)){
			return lineSwitch(pd);
		}else if (directSwitch.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchDYC))// 判断是否为主变电源侧开关
			return powerStationSwitch(pd);
		else if (directSwitch.getDeviceRunType().equals(
				CBSystemConstants.RunTypeSwitchFHC))// 判断是否为主变负荷侧开关
			return transformerLoadSwitch(pd);
		else if (directSwitch.getDeviceRunType().equals(
				CBSystemConstants.RunTypeSwitchPL))// 判断是否为旁路开关
			return sideLineSwitch(pd);
		else if (directSwitch.getDeviceRunType().equals(
				CBSystemConstants.RunTypeSwitchML))// 判断是否为母联开关
			return motherLineSwitch(pd);
		else if (directSwitch.getDeviceRunType().equals(
				CBSystemConstants.RunTypeSwitchMLPL)) {// 判断是否为母联兼旁路开关
			List<PowerDevice> searchDeviceList = null;
			searchDeviceList = getDeviceList(pd, "", "", "", "", false, false,
					true, false);
			for (PowerDevice switchTemp : searchDeviceList) {
				if (switchTemp.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSideMother))
					return sideLineSwitch(pd);
			}
			return motherLineSwitch(pd);
		}
		else  {// 判断是否为接地变开关
			if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))
				return "1";
			else
				return "2";
		}
	}
	
	/**
	 * 主变负荷侧开关判断刀闸为电源侧负荷侧算法
	 * 
	 * @param pd
	 * @return
	 */
	private static String transformerLoadSwitch(PowerDevice pd) {
		List<PowerDevice> swList = RuleExeUtil.getKnifeRelateSwitch(pd); //刀闸所属开关
		if(swList.size() == 0)
			return "9";
		PowerDevice sw = swList.get(0); //刀闸所属开关
		
		List<PowerDevice> srcList = RuleExeUtil.getDeviceList(sw, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, false, true);
		for(Iterator it=srcList.iterator();it.hasNext();) {
			PowerDevice src = (PowerDevice)it.next();
			if(!src.getDeviceStatus().equals("0"))
				it.remove();
		}
		if(srcList.size() == 0)
			return "9";
		
		PowerDevice source = srcList.get(0); //电源点
		
		List<PowerDevice> kfList = RuleExeUtil.getSwitchRelateKnife(sw); //开关附属刀闸
		List<PowerDevice> path = RuleExeUtil.getPathByDevice(sw, source, "", "", false, false);
		if(path != null) {
			for(int i = path.size()-1; i >=0; i--) {
				PowerDevice dev = path.get(i);
				if(kfList.contains(dev)) { //找到电源侧刀闸
					if(pd.equals(dev))
						return "1";
					else
						return "2";
				}
			}
		}
		return "9";
	}

	/**
	 * 母联开关判断刀闸为电源侧负荷侧算法
	 * 
	 * @param pd
	 * @return
	 */
	private static String motherLineSwitch(PowerDevice pd) {
		
		List<PowerDevice> swList = RuleExeUtil.getKnifeRelateSwitch(pd); //刀闸所属开关
		if(swList.size() == 0)
			return "9";
		PowerDevice sw = swList.get(0); //刀闸所属开关
		
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(sw, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
		for(Iterator it=mlList.iterator();it.hasNext();) {
			PowerDevice ml = (PowerDevice)it.next();
			if(!ml.getDeviceStatus().equals("0"))
				it.remove();
		}
		if(mlList.size() != 1)
			return "9";
		
		PowerDevice source = mlList.get(0); //电源点母线
		
		List<PowerDevice> kfList = RuleExeUtil.getSwitchRelateKnife(sw); //开关附属刀闸
		List<PowerDevice> path = RuleExeUtil.getPathByDevice(sw, source, "", "", false, true);
		for(int i = path.size()-1; i >=0; i--) {
			PowerDevice dev = path.get(i);
			if(kfList.contains(dev)) { //找到电源侧刀闸
				if(pd.equals(dev))
					return "1";
				else
					return "2";
			}
		}
		return "9";
		
		/*
		List<PowerDevice> searchDeviceList1 = null;
		List<PowerDevice> searchDeviceList2 = null;
		List<PowerDevice> route = null;// 路径集合
		PowerDevice searchDevice = null;

		List<PowerDevice> list = null;
		list = RuleUtil.getDirectDevice(pd, SystemConstants.Switch);// 获取连接的开关
		if (list == null || list.size() == 0) {
			list = RuleExeUtilYNDD.getDeviceList(pd,  SystemConstants.Switch,  SystemConstants.PowerTransformer, true, true, true);
			if (list == null || list.size() == 0)
				return "9";
		}
		PowerDevice directSwitch = (PowerDevice) list.get(0);

		List<PowerDevice> knifeList = RuleExeUtilYNDD.getDeviceDirectList(
				directSwitch, SystemConstants.SwitchSeparate);// 获取开关两侧的刀闸
		PowerDevice otherKnife = null;
		for (PowerDevice pdTemp : knifeList) {
			if (pdTemp.equals(pd)) {
				knifeList.remove(pdTemp);// 在搜索到的刀闸结果中排除当前操作刀闸
				break;
			}
		}
		if (knifeList.size() == 0)
			return "9";
		otherKnife = knifeList.get(0);// 获取开关连接的另一个刀闸

		String powerStationID = pd.getPowerStationID();// 获取变电站ID
		PowerDevice powerStation = CBSystemConstants.getMapPowerStation().get(
				powerStationID);// 获取变电站
		double powerStationPowerVoltGrade = powerStation.getPowerVoltGrade();// 获取变电站电压等级
		double switchPowerVoltGrade = directSwitch.getPowerVoltGrade();// 获取开关电压等级
		if (switchPowerVoltGrade == powerStationPowerVoltGrade) {// 如果开关处在高电压等级则搜索线路，如果在低电压等级则搜索变压器
			searchDeviceList1 = RuleExeUtilYNDD.getDeviceList(pd,
					SystemConstants.InOutLine,
					SystemConstants.PowerTransformer, "", "", false, false,
					false, true);// 由当前刀闸开始寻找连通的在运行的线路
			searchDeviceList2 = RuleExeUtilYNDD.getDeviceList(otherKnife,
					SystemConstants.InOutLine,
					SystemConstants.PowerTransformer, "", "", false, false,
					false, true);// 由另一侧刀闸开始寻找连通的在运行的线路
			if ((searchDeviceList1.size() > 0 && searchDeviceList2.size() > 0)
					|| (searchDeviceList1.size() == 0 && searchDeviceList2
							.size() == 0)) {// 如果向两侧都搜到线路则不分
				return "9";//
			} else {
				if (searchDeviceList1.size() > 0)
					searchDevice = (PowerDevice) searchDeviceList1.get(0);
				else
					searchDevice = (PowerDevice) searchDeviceList2.get(0);
				route = RuleExeUtilYNDD.getPathByDevice(directSwitch, searchDevice,
						SystemConstants.PowerTransformer,
						CBSystemConstants.RunTypeSideMother, true, false);// 搜索开关到电源点的路径
			}
		} else {
			searchDeviceList1 = RuleExeUtilYNDD.getDeviceList(pd,
					SystemConstants.PowerTransformer,
					SystemConstants.InOutLine, "", "", false, false, false,
					true);// 由当前刀闸开始寻找连通的在运行的主变
			searchDeviceList2 = RuleExeUtilYNDD.getDeviceList(otherKnife,
					SystemConstants.PowerTransformer,
					SystemConstants.InOutLine, "", "", false, false, false,
					true);// 由另一侧刀闸开始寻找连通的在运行的主变
			if ((searchDeviceList1.size() > 0 && searchDeviceList2.size() > 0)
					|| (searchDeviceList1.size() == 0 && searchDeviceList2
							.size() == 0)) {
				return "9";// 如果向两侧都没有搜到变压器
			} else {
				if (searchDeviceList1.size() > 0)
					searchDevice = (PowerDevice) searchDeviceList1.get(0);
				else
					searchDevice = (PowerDevice) searchDeviceList2.get(0);
				route = RuleExeUtilYNDD.getPathByDevice(directSwitch, searchDevice,
						SystemConstants.InOutLine+","+SystemConstants.PowerTransformer, "", true, false);
			}
		}
		if (route.contains(pd)) // 如果路径包含了当前操作刀闸则说明是电源侧
			return "1";
		else
			return "2";// 如果路径不包含操作刀闸则说明是负荷侧
		*/
	}

	/**
	 * 旁路开关判断刀闸为电源侧负荷侧算法
	 * 
	 * @param pd
	 * @return
	 */
	private static String sideLineSwitch(PowerDevice pd) {
		List<PowerDevice> devs = null;
		String powerStationID = pd.getPowerStationID();// 获取变电站ID
		PowerDevice powerStation = CBSystemConstants.getMapPowerStation().get(
				powerStationID);// 获取变电站
		double powerStationPowerVoltGrade = powerStation.getPowerVoltGrade();// 获取变电站电压等级

		devs = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, "",
				true, true, true);
		PowerDevice sideMotherLine = null;
		if (devs.size() == 0)
			return "9";
		else {
			for (PowerDevice motherLineTemp : devs) {
				if (motherLineTemp.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSideMother))
					sideMotherLine = motherLineTemp;
			}
		}
		if(sideMotherLine == null)
			return "9";
		double sideMotherPowerVoltGrade = sideMotherLine.getPowerVoltGrade();
		List<PowerDevice> knifeDirectDevice = RuleUtil.getDirectDevice(pd);// 搜索刀闸直接连接的设备，判断是否有旁母
		if (sideMotherPowerVoltGrade == powerStationPowerVoltGrade) {// 判断旁母是属于高压侧母线还是低压侧母线
			if (knifeDirectDevice.contains(sideMotherLine))// 如果是旁母是高压侧母线，那么与旁母连接的刀闸则是属于电源侧刀闸
				return "1";// 电源侧刀闸，返回1
			else
				return "2";// 负荷侧刀闸，返回2
		} else {// 如果旁母属于低压侧母线，那么与旁母直接连接的刀闸属于负荷侧返回2
			if (knifeDirectDevice.contains(sideMotherLine))
				return "2";
			else
				return "1";
		}
	}

	/**
	 * 主变开关判断刀闸为电源侧负荷侧算法
	 * 
	 * @param pd
	 * @return
	 */
	private static String powerStationSwitch(PowerDevice pd) {
		List<PowerDevice> list = null;
		list = RuleUtil.getDirectDevice(pd, SystemConstants.Switch);// 获取连接的开关
		if(list.size() == 0)
			return "9";
		PowerDevice directSwitch = (PowerDevice) list.get(0);// 获取刀闸连接的开关
		List<PowerDevice> zbList = null;
		List<PowerDevice> mxList = null;
		zbList = RuleExeUtil.getDeviceList(directSwitch, SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);
		mxList = RuleExeUtil.getDeviceList(directSwitch, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
		if(zbList.size() <= 0)
			return "9";
		if(mxList.size() <= 0){
			mxList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXL, null, true, true, true, true);
			if(mxList.size() <= 0) {
				//线变组接线
				if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY))
					return "1";
				else
					return "2";
			}
			else
				return "1";
		}
		PowerDevice zbTemp = zbList.get(0);
		PowerDevice mxTemp = null;
		if(mxList.size() == 1)
			mxTemp = mxList.get(0);
		else{
			if(mxList.size() > 1){
				for(int im = 0; im < mxList.size(); im++){
					List<PowerDevice> routeDeviceTempList = RuleExeUtil.getPathByDevice(zbTemp, null, mxList.get(im), null, null, true, true);
					if(routeDeviceTempList.contains(pd)){
						mxTemp = mxList.get(im);
						break;
					}
				}
			}
		}
		
		if((zbTemp!=null&&!"0".equals(zbTemp.getDeviceStatus())) || (mxTemp!=null&&!"0".equals(mxTemp.getDeviceStatus()))){
			PowerDevice tempDevice = null;
			if(zbTemp!=null && "0".equals(zbTemp.getDeviceStatus()))
				tempDevice = zbTemp;
			else if(mxTemp!=null && "0".equals(mxTemp.getDeviceStatus()))
				tempDevice = mxTemp;
			if(tempDevice == null) {
				//return "9";
				if(RuleExeUtil.isSourceSide(pd)) {
					if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))
						return "1";
					else
						return "2";
				}
				else{
					if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))
						return "2";
					else
						return "1";
				}
			}
			List<PowerDevice> routeDeviceList = RuleExeUtil.getPathByDevice(pd, tempDevice, null, null, true, true);
			if(routeDeviceList.contains(directSwitch))
				return "2";
			else
				return "1";
		}else {
			// 判断是否处在高电压等级，相等则为高电压等级
			double knifePowerVoltGrade = pd.getPowerVoltGrade();// 取刀闸的电压等级
			String powerStationID = pd.getPowerStationID();// 获取变电站ID
			PowerDevice powerStation = CBSystemConstants.getMapPowerStation().get(
					powerStationID);// 获取变电站
			double powerStationPowerVoltGrade = powerStation.getPowerVoltGrade();// 获取变电站电压等级
			if (knifePowerVoltGrade == powerStationPowerVoltGrade) {// 高电压等级
				if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) // 判断是否为母线刀闸，如果是则为电源侧
					return "1";// 电源侧：返回1
				else
					return "2";
			} else {// 不相等则为低电压等级
				if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)
						|| pd.getDeviceRunType().equals(
								CBSystemConstants.RunTypeKnifeQT)) // 判断是否为母线刀闸或其它刀闸，如果是则为负荷侧
					return "2";// 负荷侧：返回2
				else
					return "1";
			}
		}
	}

	/**
	 * 线路开关和其他开关判断刀闸为电源侧负荷侧算法
	 * 
	 * @param pd
	 * @return
	 */
	private static String lineSwitch(PowerDevice pd) {
		// 判断是否处在高电压等级，相等则为高电压等级
		double knifePowerVoltGrade = pd.getPowerVoltGrade();// 取刀闸的电压等级
		String powerStationID = pd.getPowerStationID();// 获取变电站ID
		PowerDevice powerStation = CBSystemConstants.getMapPowerStation().get(
				powerStationID);// 获取变电站
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) // 判断是否为母线刀闸，如果是则为电源侧
			return "1";
		else
			return "2";
//		double powerStationPowerVoltGrade = powerStation.getPowerVoltGrade();// 获取变电站电压等级
//		if (knifePowerVoltGrade == powerStationPowerVoltGrade) {
//			List<PowerDevice> lines=RuleExeUtilYNDD.getDeviceList(pd, SystemConstants.InOutLine,"", true, true, true);
//			String flow = "";
//			for (PowerDevice line : lines) {
//				if(!line.getDeviceStatus().equals("0"))
//					continue;
//				if(RuleExeUtilYNDD.judgeLineFlow(line).equals("1"))
//					flow = "1";
//			}
//			if(flow.equals("1")){
//				if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) // 判断是否为母线刀闸，如果是则为负荷侧返回2
//				    return "2";
//			    else
//				    return "1";// 如果不是则为电源侧 返回1
//			}else{
//				if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) // 判断是否为母线刀闸，如果是则为电源侧
//					return "1";
//				else
//					return "2";
//			}
//			
//		} else {// 不相等则为低电压等级
//			if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) // 判断是否为母线刀闸，如果是则为电源侧
//				return "1";
//			else
//				return "2";
//		}
	}

	
	/**
	 * 查找主变的主变开关、线路开关（带主变开关）
	 * 
	 * @param pd
	 * @return
	 */
	public static PowerDevice getTransformerSwitch(PowerDevice pd, double vol) {
		PowerDevice tagSwitch = null;
		List<PowerDevice> switchs = getDeviceList(pd, SystemConstants.Switch,
				"", true, true, true);
		for (PowerDevice dev : switchs) {
			if (dev.getPowerVoltGrade() == vol) {
				tagSwitch = dev;
				break;
			}
		}
		if (tagSwitch == null) {
			List<Double> vols = getTransformerVol(pd);
			if(vols.size() == 0)
				return tagSwitch;
			if (vol == vols.get(vols.size() - 1)) { // 高压侧
				List<PowerDevice> mlList = getMotherLineByVol(pd, vol);
				if (mlList.size() > 0) {
					switchs = getDeviceList(mlList.get(0),
							SystemConstants.Switch, "", true, true, true);
					for (PowerDevice dev : switchs) {
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
							tagSwitch = dev;
							break;
						}
					}
				}
			}
		}
		return tagSwitch;
	}

	/**
	 * 判断母联开关连接的母线中是否存在没有负载的在运行的母线，如果有，那么母联开关应在合位
	 * 
	 * @param pd
	 * @return
	 */
	public static boolean isAllMLHasLoad(PowerDevice pd) {
		if (pd.getPowerVoltGrade() != CBSystemConstants.getPowerStation(
				pd.getPowerStationID()).getPowerVoltGrade())
			return true;
		List<PowerDevice> mllist = RuleExeUtil.getDeviceList(pd,
				SystemConstants.MotherLine, SystemConstants.PowerTransformer,
				false, true, false);
		for (PowerDevice mlTemp : mllist) {
			if (!isMLOnLoad(mlTemp) && mlTemp.getDeviceStatus().equals("0"))
				return false;
		}
		return true;
	}

	/**
	 * 断开线路开关或者主变开关等开关时，如果开关连接的母线是双母接线则合上母线间的母联开关以及母联开关到目标主变路径中的其他开关
	 * 
	 * @param srcMotherLine
	 * @param descTransformer
	 */
	public static boolean doubleMotherLineMLSwitchToOn(PowerDevice srcMotherLine, PowerDevice descTransformer) {
		List<PowerDevice> mlSwitchListTemp = getMLSwitchList(srcMotherLine);
		PowerDevice mlSwitch = null;
		if (mlSwitchListTemp.size() > 0) {
			for(PowerDevice switchTemp : mlSwitchListTemp){
				if(switchTemp.getDeviceType().equals(SystemConstants.Switch) && switchTemp.getDeviceStatus().equals("0")){
					mlSwitch = switchTemp;//判断是否存在合上的母联开关
					break;
				}
			}
			if(mlSwitch == null){//如果不存在合上的母联开关则需要合上一个母联开关
				mlSwitch = mlSwitchListTemp.get(0);
				deviceStatusChange(mlSwitch, mlSwitch.getDeviceStatus(), "0");// 合上双母间的母联开关
			}
			
			List<PowerDevice> srcToDescRoute = getPathByDevice(mlSwitch, descTransformer, SystemConstants.PowerTransformer, null, true, false);
			for (PowerDevice deviceTemp : srcToDescRoute) {
				if (deviceTemp.getDeviceType().equals(SystemConstants.Switch) && !deviceTemp.getDeviceStatus().equals("0"))// 路径上的开关都转运行
					deviceStatusChange(deviceTemp, deviceTemp.getDeviceStatus(), "0");
			}
		}
		return true;
	}

	/**
	 * 打印整个厂站中所有设备直接连接的设备
	 * 
	 * @param pd
	 * @return
	 */
	public static void printAllStationDevice(PowerDevice pd) {
		Map<String, PowerDevice> allDeviceMap = new HashMap<String, PowerDevice>();
		String stationId = pd.getPowerStationID();
		System.out.println("------"
				+ CBSystemConstants.getMapPowerStation().get(stationId)
				+ "------");
		allDeviceMap = CBSystemConstants.getMapPowerStationDevice().get(
				stationId);
		Collection<PowerDevice> allDeviceList = allDeviceMap.values();
		PowerDevice[] pdArray = new PowerDevice[allDeviceList.size()];
		pdArray = allDeviceList.toArray(pdArray);
		for (int a = 0; a < pdArray.length; a++) {
			for (int b = 0; b < pdArray.length - a - 1; b++) {
				if (pdArray[b].getPowerDeviceName().compareTo(
						pdArray[b + 1].getPowerDeviceName()) > 0) {
					PowerDevice pdTemp = pdArray[b];
					pdArray[b] = pdArray[b + 1];
					pdArray[b + 1] = pdTemp;
				}
			}
		}
		int i = 1;
		for (PowerDevice deviceTemp : pdArray) {
			i++;
			List<PowerDevice> linkedDeviceList = RuleExeUtil
					.getDeviceDirectList(deviceTemp, null);
			System.out.println(i + ")" + deviceTemp + ":" + linkedDeviceList);
		}
	}

	/**
	 * 判断某开关在二分之三接线方式中是否在中间位置
	 * */
	public static boolean isSwMiddleInThreeSecond(PowerDevice switch1) {
		if(!switch1.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
			   return false;
		}
		String switchCode = StringUtils.getSwitchCode(switch1.getPowerDeviceName());
		String className = CZPService.getService().getClass().getName();
		if(className.indexOf("app.xb") >= 0 && switchCode.endsWith("0")) //西北尾号为0的是中间开关
			return true;
		else if(className.indexOf("app.jx") >= 0 && switchCode.endsWith("2")) //江西尾号为2的是中间开关
			return true;
		else if(className.indexOf("app.cq") >= 0 && switchCode.endsWith("2")) //重庆尾号为2的是中间开关
			return true;
		else if(className.indexOf("app.gx") >= 0 && switchCode.endsWith("2")) //广西尾号为2的是中间开关
			return true;
		else if(className.indexOf("app.hn") >= 0 && switchCode.endsWith("2")) //河南尾号为2的是中间开关
			return true;
		else if(className.indexOf("app.zd") >= 0 && switchCode.endsWith("2")) //总调尾号为2的是中间开关
			return true;
		else if(className.indexOf("app.sh") >= 0 && switchCode.endsWith("2")) //总调尾号为2的是中间开关
			return true;
		else if(className.indexOf("app.yn") >= 0 && switchCode.endsWith("2")) //云南尾号为2的是中间开关
			return true;
		else if(className.indexOf("app.yndd") >= 0 && switchCode.endsWith("2")) //云南尾号为2的是中间开关
			return true;
		else if(className.indexOf("app.ah") >= 0 && switchCode.endsWith("2")) //安徽尾号为2的是中间开关
			return true;
		else if(className.indexOf("app.sc") >= 0 && switchCode.endsWith("2")) //四川尾号为2的是中间开关
			return true;
		List<PowerDevice> ls1 = RuleExeUtil.getDeviceList(switch1, SystemConstants.Switch, "", "", "", false, true, true, true, "1");
		List<PowerDevice> ls2 = RuleExeUtil.getDeviceList(switch1, SystemConstants.Switch, "", "", "", false, true, true, true, "2");
		/*针对总调部分不是中间开关系统判定中间开关特殊处理*/
		List<PowerDevice> ls3 = RuleExeUtil.getDeviceList(switch1, SystemConstants.ElecCapacity, "", "", "", false, true, true, true, "1");
		List<PowerDevice> ls4 = RuleExeUtil.getDeviceList(switch1, SystemConstants.ElecCapacity, "", "", "", false, true, true, true, "2");
		if(ls1.size()==0||ls2.size()==0){
			return false;
		}
		if(ls3.size()>0||ls4.size()>0){
			return false;
		}
		return true;
	}
	
	/**
	 * 获取与设备的电源开关
	 * @param pd
	 * @return
	 */
	public static PowerDevice getDeviceSourceSwitch(PowerDevice pd){
		PowerDevice sourceSwitch = null;
		List<PowerDevice> swList = null;
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			swList = getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			if(swList.size() > 0)
				sourceSwitch = swList.get(0);
			else {
				List<PowerDevice> mlList = getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				if(mlList.size() > 0)
					sourceSwitch = getDeviceSourceSwitch(mlList.get(0));
			}
		}
		else if(pd.getDeviceType().equals(SystemConstants.MotherLine)) {
			if(RuleExeUtil.isSourceSide(pd)) {
				
			}
			else {
				swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				
				//swList = RuleExeUtilYNDD.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC+","+CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				if(swList.size() > 0) {
					for(PowerDevice sw : swList) {
						if(!sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC) && !sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
							continue;
						if(sw.getDeviceStatus().equals("0")) {
							sourceSwitch = sw;
							break;
						}
					}
					if(sourceSwitch == null)
						sourceSwitch = swList.get(0);
				}
			}
		}
		return sourceSwitch;
	}
	
	/**
	 * 获取与设备的电源刀闸
	 * @param pd
	 * @return
	 */
	public static PowerDevice getDeviceSourceKnife(PowerDevice pd){
		PowerDevice sourceKnife = null;
		List<PowerDevice> kfList = null;
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			PowerDevice sw = getDeviceSourceSwitch(pd);
			if(sw == null || !sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
				kfList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
				if(kfList.size() > 0)
					sourceKnife = kfList.get(0);
			}
			
		}
		return sourceKnife;
	}
	
	/**
	 * 获取与线路、母线、主变直接连接的开关
	 * @param pd
	 * @return
	 */
	public static List<PowerDevice> getLinkedSwitch(PowerDevice pd){
		List<PowerDevice> list = new ArrayList<PowerDevice>();
		if(pd==null)
			return list;
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			list = getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			if(list.size() == 0) {
				list = getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, null, null, false, true, false, true);
				for(int i = 0; i < list.size(); i++) {
					PowerDevice sw = list.get(i);
					if(sw.getDeviceStatus().equals("0") || !sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
						list.remove(i);
						list.add(0, sw);
					}
						
				}
			}
			else {
				if(list.size() >= 2) {
					List<PowerDevice> dycList = new ArrayList<PowerDevice>();
					for(PowerDevice sw : list) {
						if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {
							dycList.add(sw);
						}
					}
					if(dycList.size() >= 2) {
						int pathCount = 999;
						PowerDevice dyc = null;
						for(PowerDevice sw : dycList) {
							List pathList = RuleExeUtil.getPathByDevice(pd, sw, SystemConstants.PowerTransformer, null, true, false);
							if(pathList.size() < pathCount) {
								pathCount = pathList.size();
								dyc = sw;
							}
						}
						for (Iterator<PowerDevice> itr = list.iterator(); itr.hasNext();) {
							PowerDevice sw = itr.next();
							if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) && !sw.equals(dyc))
								itr.remove();;
						}
					}
				}
			}
		}
		else if(pd.getDeviceType().equals(SystemConstants.MotherLine)){
			if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
				list = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL, "", false, true, true, true);
			}
			else if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				//list = getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
				list.addAll(RuleExeUtil.getMLSwitchList(pd));
			}
			else{
				list = getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			}
		}else if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){
			list = getDeviceList(pd, SystemConstants.Switch, null, true, true, true);
			boolean flag = false;
			for(PowerDevice switchTemp : list){//判断主变高压侧是否搜到开关
				if(switchTemp.getPowerVoltGrade() == pd.getPowerVoltGrade()){
					flag = true;
					break;
				}
			}
			if(!flag){
				List<PowerDevice> mlList = new ArrayList<PowerDevice>();
				List<PowerDevice> otherSwitchList = new ArrayList<PowerDevice>();
				PowerDevice motherLine = null;
				mlList = getDeviceList(pd, SystemConstants.MotherLine, null, true, true, true);
				for(PowerDevice motherLineTemp : mlList){
					if(motherLineTemp.getPowerVoltGrade() == pd.getPowerVoltGrade()){
						motherLine = motherLineTemp;
						break;
					}
				}
				if(motherLine != null) {
					otherSwitchList = getDeviceList(motherLine, SystemConstants.Switch, SystemConstants.MotherLine, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeKnifeML, false, true, true, true);
					list.addAll(otherSwitchList);
				}
			}
		}
		return list;
	}
	//根据母线上的设备查找关联主变开关
	public static PowerDevice getTransformSwitch(PowerDevice pd){
		List<PowerDevice> mls=getDeviceList(pd, SystemConstants.MotherLine, "", true, true, true);
		if(mls==null||mls.size()==0){
			return null;
		}
		PowerDevice ml = mls.get(0);
		List<PowerDevice> list=getDeviceList(ml, null, SystemConstants.Switch, "", CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
		if(list.size()!=0){
			return list.get(0);
		}
		return null;
	}
	
	//判断是否为双母倒母开关
	public static boolean isdoubledaomu(PowerDevice beforepd,PowerDevice afterpd){
			List<PowerDevice> kglist=getDeviceList(beforepd, SystemConstants.Switch, null, null, null, false, true, true, true);
			if(kglist.size() == 0)
				return false;
			PowerDevice kg=kglist.get(0);
			List<PowerDevice> dzright=getDeviceList(kg, SystemConstants.SwitchSeparate, null, null, null, true, true, true, true,"1");
			List<PowerDevice> dzleft=getDeviceList(kg, SystemConstants.SwitchSeparate, null, null, null, true, true, true, true,"2");
			if(dzright.contains(beforepd)==true){
				if(dzright.contains(afterpd)==true){
					return true;
				}
			}else{
				if(dzleft.contains(beforepd)==true){
					if(dzleft.contains(afterpd)==true){
						return true;
					}
				}
			}
			return false;
		}
		
	/*
	 * 基于潮流线路电源侧负荷侧判定算法(非电缆线路) 
	 * 2016-02-03 赵于权
	 */
	public static String getLineFlowNew(PowerDevice dev){
	   String flowNew = "-1";
	   Double ygz = null;
	   List<PowerDevice> dcSwitchs = new ArrayList<PowerDevice>();
	   /*线路接线方式为电缆线路*/
	   if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelCableLine)){
			return flowNew;
	   }
	   /*获取当前线路段开关*/
	   List<PowerDevice> swList = getLinkedSwitch(dev);
	   if(swList.size()==0){
		    List<PowerDevice> otherLine = getLineOtherSideList(dev);
			if(otherLine.size()==0)
			    return flowNew;
			for(PowerDevice pdLine:otherLine){
			    dcSwitchs = getLinkedSwitch(pdLine);
			}
			if(dcSwitchs.size()==0)
			    return flowNew;
			for(PowerDevice dcSwitch:dcSwitchs){
				/*获取对侧开关实测有功*/
			    ygz = WebServiceUtil.getXLYGFromDB(dcSwitch.getPowerDeviceID());
			    if(ygz == null){
			        flowNew = "-1";//开关实时数据异常
			    }else if(ygz > 0){
			        flowNew = "1";//线路为进线,所在厂站为负荷侧
			    }else if(ygz < 0){
			        flowNew = "2";//线路为出线,所在厂站为电源侧
			    }else if(ygz == 0){
			        if(dcSwitch.getDeviceStatus().equals("0"))
			    	     flowNew = "3";//线路为空载运行,所在厂站为负荷侧
			        else
			    	     flowNew = "0";//线路为空载运行,所在厂站为电源侧
			    }
			}
		  return flowNew;
		}		    
		/*获取开关实测有功*/
		for(PowerDevice sw:swList){
			ygz = WebServiceUtil.getXLYGFromDB(sw.getPowerDeviceID());
			if(ygz == null){
				flowNew = "-1";//开关实时数据异常
			}else if(ygz > 0){
				flowNew = "2";//线路为出线,所在厂站为电源侧
			}else if(ygz < 0){
				flowNew = "1";//线路为进线,所在厂站为负荷侧
			}else if(ygz == 0){
				if(sw.getDeviceStatus().equals("0"))
					flowNew = "0";//线路为空载运行,所在厂站为电源侧
				else
					flowNew = "3";//线路为空载运行,所在厂站为负荷侧		
			}
		}
		return flowNew;
	}
	
	
	//判断是否为牵引线
	public static boolean isQYline(PowerDevice pd,String desc){
		if(!pd.getDeviceType().equals(SystemConstants.InOutLine)){
        	return false;
        }    
		
		PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
		List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
		
		PowerDevice operateDev = null;
		if(desc.indexOf("(源)") >= 0) {
			if(sourceLineTrans != null)
				operateDev = sourceLineTrans;
		}else if(desc.indexOf("(负)") >= 0) {
			if(loadLineTrans != null && loadLineTrans.size() > 0)
				operateDev = loadLineTrans.get(0);
		}

		if( operateDev != null && operateDev.getPowerStationName().indexOf("牵引") > 0){
			return true;
		}else{
			return false;
		}
	}
	/**
	 * 根据数字状态获取文字状态（运行0，热备1，冷备2，检修3）
	 */
	public static String getStatus(String statue){
		if(statue.equals("0")){
			return "运行";
		}else if(statue.equals("1")){
			return "热备用";
		}else if(statue.equals("2")){
			return "冷备用";
		}else{
			return "检修";
		}
	}
	
	/**
	 * 根据文字状态获取数字状态（运行0，热备1，冷备2，检修3）
	 */
	public static String getNumStatue(String statue){
		if(statue.equals("运行")){
			return "0";
		}else if(statue.equals("热备用")){
			return "1";
		}else if(statue.equals("冷备用")){
			return "2";
		}else{
			return "3";
		}
	}
	
	//判断是否是牵引刀闸
	public static boolean isQYKnife(PowerDevice pd,String desc){
		Boolean isTrue = false;
		 //一、搜索牵引线操作的母线对策设备开关
		 CommonSearch cs=new CommonSearch();
		 Map<String, Object> inPara = new HashMap<String, Object>();
	  	 Map<String, Object> outPara = new HashMap<String, Object>();
	   	 PowerDevice operateDev = null;
	   	 PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
		 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
		 if(desc.indexOf("(源)") >= 0) {
			if(sourceLineTrans != null)
				operateDev = sourceLineTrans;
		 }else if(desc.indexOf("(负)") >= 0) {
			if(loadLineTrans != null && loadLineTrans.size() > 0)
				operateDev = loadLineTrans.get(0);
		 }
		 if(operateDev == null)
			 return isTrue;
		 inPara.put("oprSrcDevice", operateDev);
         inPara.put("tagDevType", SystemConstants.SwitchSeparate);
         inPara.put("isStopOnBusbarSection", "false");
         cs.execute(inPara, outPara);
	 	 inPara.clear();
	 	 List tempDevices = (ArrayList) outPara.get("linkedDeviceList");
		
	 	 PowerDevice tempDev=null;
	  	 for (int i = 0; i < tempDevices.size(); i++) {
	 		tempDev=(PowerDevice)tempDevices.get(i);
	 		if(tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)
	 				||tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS)
	 				 ||tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZBS)){
	 			isTrue = true;
	 			break;
	 		}
		 }
	  	 return isTrue;
	}
	/**
	 * 获取线路相邻母联开关的相邻的另一条线路（多线路公用开关情况）
	 */
	public static PowerDevice otherXlsWithML(List<PowerDevice> xl){
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(xl.get(0),xl.get(0), SystemConstants.Switch, "", CBSystemConstants.RunTypeSwitchML
				, "", false, true, false, true,true);
		PowerDevice mlkg =null;
		for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
    		DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
    		PowerDevice pd = dtd.getTransDevice();
    		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
    			for(PowerDevice ml:mlList){
    				if(ml.equals(pd)){
    					mlkg=pd;
    					break;
    				}
    			}
    		}
    		if(mlkg!=null){
    			break;
    		}
		}
		if(mlkg==null&&mlList.size()>0){
			mlList =RuleExeUtil.getDeviceList(mlList.get(0),SystemConstants.Switch, SystemConstants.PowerTransformer,
					CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
	    		DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
	    		PowerDevice pd = dtd.getTransDevice();
	    		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
	    			for(PowerDevice ml:mlList){
	    				if(ml.equals(pd)){
	    					mlkg=pd;
	    					break;
	    				}
	    			}
	    		}
	    		if(mlkg!=null){
	    			break;
	    		}
			}
		}
		if(mlkg!=null){
			List<PowerDevice> xlList =RuleExeUtil.getDeviceList(mlkg, SystemConstants.InOutLine, SystemConstants.PowerTransformer, 
					"", CBSystemConstants.RunTypeSwitchML, false, true, false, true);
			for(PowerDevice xldev:xl){
				xlList.remove(xldev);
			}
			if(xlList.size()>0){
				return xlList.get(0);
			}else {
				xlList =RuleExeUtil.getDeviceList(mlkg, SystemConstants.InOutLine, SystemConstants.PowerTransformer, 
						"", "", false, true, false, true);
				for(PowerDevice xldev:xl){
					xlList.remove(xldev);
				}
				if(xlList.size()>0){
					return xlList.get(0);
				}else{
					return null;
				}
			}
		}else {
			return null;
		}
	
	}
	/**
	 * 获取线路相邻母联开关的相邻的另一条线路
	 */
	public static PowerDevice otherXlWithML(PowerDevice xl){
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(xl,xl, SystemConstants.Switch, "", CBSystemConstants.RunTypeSwitchML
				, "", false, true, false, true,true);
		if(mlList.size()>0){
			List<PowerDevice> xlList =RuleExeUtil.getDeviceList(mlList.get(0),xl, SystemConstants.InOutLine, "", ""
					, "", false, true, false, true,true);
			if(xlList.size()>0){
				return xlList.get(0);
			}else {
				return null;
			}
		}else {
			return null;
		}
	
	}
	/**
	 * 根据母联开关及线路查与这个母联开关相邻的另一条线路
	 */
	public static PowerDevice otherXlWithMLAndXL(PowerDevice ml,PowerDevice xl){
		List<PowerDevice> gyxl =getLineCable(xl);
		List<PowerDevice> xlList =RuleExeUtil.getDeviceList(ml,xl, SystemConstants.InOutLine, "", ""
				, CBSystemConstants.RunTypeSwitchML, false, true, false, true,true);
		for(PowerDevice xlDev:gyxl){
			xlList.remove(xlDev);
		}
		if(xlList.size()>0){
			return xlList.get(0);
		}else {
			return null;
		}
	
	
	}
	/**
	 * 设备按名称排序
	 */
	public static List<PowerDevice> sortListByDevName(List<PowerDevice> list){
		if(list==null){
			return null;
		}else{
		    Collections.sort(list, new Comparator<PowerDevice>() {
		        @Override
		        public int compare(PowerDevice p1, PowerDevice p2) {
		          if(p1.getPowerDeviceName().compareTo(p2.getPowerDeviceName())>0) {
		            return 1;
		          }
		          else {
		            return -1;
		          }
		        }
		      });
		    return list;
		}
	}
	
	

	
	/**
	  * 判断开关刀闸是否可控
	  * 
	  * <AUTHOR>
	  */
	public static boolean isBreakerCtrl_GZ(PowerDevice pd ){
		boolean ret =false;
		String  cimid =pd.getCimID();
		if(!cimid.equals("")&& !pd.getPowerStationName().contains("花都")&&!pd.getPowerStationName().contains("番禺")){
			String sql = "select 是否可遥控 as isyk from "+CBSystemConstants.emsetlUser+"t_s_breakerctrl t where t.开关ID = '"+cimid+"' ";
			List ykList = DBManager.query(sql);
			Map temp = new HashMap();
			if(ykList.size()>0){
				temp = (Map) ykList.get(0);
				if(StringUtils.ObjToString(temp.get("isyk")).equals("1")){
					ret =true;
				}
			}
		}
		return ret;
	}
	
	/**
	  * 创建时间 2018年5月31日 下午3:05:41
	  * 判断停电主变是否涉及负荷均分（若线路停电所T接主变的站有三台主变，且该T接主变变低不是双变低开关的，转电时均会涉及上述负荷是否均分的问题）
	  * <AUTHOR>
	  * @Title 
	  * @param pd主变
	  * @return
	  */
	public static boolean IsTransformFHJF(PowerDevice pd){
		if(getTransformerSwitchLow(pd).size()>1){//排除变低是双变低开关
			return false;
		}
		//获取其他主变
		List<PowerDevice> zbList = RuleExeUtil.getDeviceList(pd, pd, SystemConstants.PowerTransformer, null, "", "", false, true, false, true, false);
		if(zbList.size()<2){//排除其他主变个数小于2
			return false;
		}
		boolean isDoubleBD =false;
		for(PowerDevice zb:zbList){
			if(getTransformerSwitchLow(zb).size()>1){
				isDoubleBD = true;
				break;
			}
		}
		if(!isDoubleBD){//其他主变只要有一个是双变低开关
			return false;
		}
		return true;
	}
	
	public static String getJKZX(String czdw) {

		List jkzxList = DBManager
				.queryForList("select n.name from "+CBSystemConstants.emsetlUser+"t_m_substation t , "+CBSystemConstants.emsetlUser+"t_m_subcontrolarea n where t.name like '%"
						+ czdw + "%' and t.memberof_subcontrolarea = n.id");
		String jkzx="";
		if (jkzxList!=null&&jkzxList.size()>0) {
			jkzx=((Map<String, String>)jkzxList.get(0)).get("name");
			
		}
		return jkzx;
	}
	//获取数据库设置的equip、emsetl、platform用户对应的用户名
	public static void getDataBaseUser(){
		try {
			String sql = "select code,name  from "+CBSystemConstants.opcardUser+"t_a_dictionary where codetype = 'database'";
			List list = DBManager.queryForList(sql);
			
			for (int i = 0; i < list.size(); i++) {
					Map temp = (Map) list.get(i);
					if("equip".equals(temp.get("code").toString())){
						CBSystemConstants.equipUser = temp.get("name").toString();
					}else if("emsetl".equals(temp.get("code").toString())){
						CBSystemConstants.emsetlUser = temp.get("name").toString();
					}else if("platform".equals(temp.get("code").toString())){
						CBSystemConstants.platformUser = temp.get("name").toString();
					}else if("opcard".equals(temp.get("code").toString())){
						CBSystemConstants.opcardUser = temp.get("name").toString();
					}
					

			}
		} catch (Exception e) {
			String sql = "select code,name  from TH_DCS.t_a_dictionary where codetype = 'database'";
			List list = DBManager.queryForList(sql);
			
			for (int i = 0; i < list.size(); i++) {
					Map temp = (Map) list.get(i);
					if("equip".equals(temp.get("code").toString())){
						CBSystemConstants.equipUser = temp.get("name").toString();
					}else if("emsetl".equals(temp.get("code").toString())){
						CBSystemConstants.emsetlUser = temp.get("name").toString();
					}else if("platform".equals(temp.get("code").toString())){
						CBSystemConstants.platformUser = temp.get("name").toString();
					}else if("opcard".equals(temp.get("code").toString())){
						CBSystemConstants.opcardUser = temp.get("name").toString();
					}

			}
		}
	
	}
	
	//根据线路名称排序，比如ABC线，A厂站端线路排第一个
	public static void sortByLineName(List<PowerDevice> lineDevices){
	    Collections.sort(lineDevices, new Comparator<PowerDevice>() {
        @Override
        public int compare(PowerDevice p1, PowerDevice p2) {
          String lineName = CZPService.getService().getDevName(p1);
          if(lineName.contains("_")){
        	  lineName = lineName.substring(lineName.indexOf("_")+1);
          }
          String stName1=p1.getPowerStationName();
          String stName2 = p2.getPowerStationName();
          if(stName1.contains(".")){
        	  stName1=stName1.substring(stName1.indexOf(".")+1);
          }
          if(stName2.contains(".")){
        	  stName2=stName2.substring(stName2.indexOf(".")+1);
          }
          if(lineName.indexOf(stName1.substring(0,1))>lineName.indexOf(stName2.substring(0,1))) {
            return 1;
          }
          else {
            return -1;
          }
        }
      });
//	    lineDevices = orderByLoadlineDesc(lineDevices);
	}
	/**
	 * （主变放后面）
	 */
	public static List<PowerDevice> orderByLoadlineDesc(List<PowerDevice> line){
		//线变组放前面
		for(int i=0;i<line.size();i++){
			PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(line.get(i));
			//识别开关是否线变组接线的主变开关
			if(lineSwitch!=null&&lineSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
				line.add(line.size()-1,line.remove(i));
			}
			
		}
		
		return line;
	}
	
	//判断主变是否线变组接线
	public static boolean isTransformerXBZ(PowerDevice pd) {
		List<PowerDevice> list = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.MotherLine, true, true, true);
		if(list.size() > 0){
			List<PowerDevice> mlkglist = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false,  true, true, true);
			List<PowerDevice> kglist = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer,true, true, true);

			for(PowerDevice dev : kglist){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					return false;
				}
			}
			
			if(mlkglist.size()==0){
				return true;
			}else{
				return false;
			}
		}else
			return false;
	}
	
	//判断主变是否线变单元接线
	public static boolean isTransformerXBDY(PowerDevice pd) {
		List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.MotherLine);
		if(list.size() > 0){
			if(list.get(0).getPowerVoltGrade() < pd.getPowerVoltGrade()){
				return false;
			}
			return true;
		}else{
			List<PowerDevice> gyckgList = RuleExeUtil.getTransformerSwitchHigh(pd);
			
			for (Iterator<PowerDevice> it2 = gyckgList.iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					it2.remove();
				}
			}
			
			if(gyckgList.size()>0){
				list = RuleExeUtil.getDeviceList(gyckgList.get(0), SystemConstants.MotherLine , SystemConstants.PowerTransformer,true,true,true);
				
				if(list.size() > 0){
					List<PowerDevice> lineList = RuleExeUtil.getDeviceDirectList(list.get(0), SystemConstants.InOutLine);
					
					List<PowerDevice> dyczbkglist = RuleExeUtil.getDeviceList(list.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDYC, "", false,  true, true, true);
					
					if(dyczbkglist.size() > 1){
						return false;
					}
					
					if(lineList.size()>0){
						return true;
					}
				}
			}

			return false;
		}
	}

	public static boolean isTransformerZBDY(PowerDevice pd) {
		if(CBSystemConstants.opcardUser.toLowerCase().contains("yx")){
			return false;
		}
		
		List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.MotherLine);
		
		if(list.size() > 0){
			if(list.get(0).getPowerVoltGrade() < pd.getPowerVoltGrade()){
				return true;
			}else{
				return false;
			}
		}else{
			return false;
		}
	}

	public static boolean isTransformer23(PowerDevice pd) {
		List<PowerDevice> list = getTransformerSwitchHigh(pd);
		  
		if(list.size() == 2){
			return true;
		}else{
			return false;
		}
	}
	
	public static boolean isTransformerJX(PowerDevice pd) {
		List<PowerDevice> list = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.MotherLine, true, true, true);
		if(list.size() > 0){
			for(PowerDevice sw :list){
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelFourCornerLine)){
					return true;
				}
			}
			return false;
		}else
			return false;
	}
	
	/**
	 * 判断某开关在三分之四或二分之三接线方式中是否在母线侧位置
	 * */
	public static boolean isSwBusSideInThreeSecondAndFourThree(PowerDevice switch1) {
		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(switch1, SystemConstants.SwitchSeparate);
		
		for(PowerDevice dz : dzList){
			List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);

			if(mxList.size()>0){
				return true;
			}
		}
		
		return false;
	}

	public static boolean isTransformerZBDM(PowerDevice pd){
		if(CBSystemConstants.opcardUser.toLowerCase().contains("hh")){
		  	List<PowerDevice> swlist = getTransformerSwitchHigh(pd);
		  	
		  	if(pd.getPowerStationID().equals("SS-19")){
		  		return true;
		  	}
		  	
		    if ((swlist.size() > 0) && ((swlist.get(0)).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine))) {
		    	return true;
		    }
		    return false;
		}else if(CBSystemConstants.opcardUser.toLowerCase().contains("dh")){
			if(pd.getPowerStationID().equals("5066549675229185") || pd.getPowerStationID().equals("5066549682765825")){
		  		return false;
		  	}
		}
		
	    List swlist = getTransformerSwitchHigh(pd);
	    if ((swlist.size() > 0) && (((PowerDevice)swlist.get(0)).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine))) {
	      return true;
	    }
	    return false;
	 }
	  
	  public static boolean isTransformerZBSM(PowerDevice pd){
	    List<PowerDevice> swlist = getTransformerSwitchHigh(pd);
	    
	    if ((swlist.size() > 0) && (((PowerDevice)swlist.get(0)).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))) {
	      return true;
	    }else{
	    	if(pd.getPowerStationName().equals("220kV凤鸣变")){
	    		 return true;
	    	}
	    }
	    
	    return false;
	  }

	public static String getDeviceEndStatus(PowerDevice dev) {
		String endStatus = "";
		Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
		for (DispatchTransDevice dtd : dtds.values()) {
			if(dtd.getTransDevice().equals(dev)){
				endStatus = dtd.getEndstate();
			}
		}
		return endStatus;
	}

	/**
	 * 根据数字状态获取文字状态新（运行、合上0，热备、拉开1，冷备2，检修3）
	 */
	public static String getStatusNew(String devicetype , String statue){
		if(devicetype.equals(SystemConstants.SwitchSeparate)||devicetype.equals(SystemConstants.SwitchFlowGroundLine)){
			if(statue.equals("0")){
				return "合上";
			}else{
				return "拉开";
			}
		}else{
			if(statue.equals("0")){
				return "运行";
			}else if(statue.equals("1")){
				return "热备用";
			}else if(statue.equals("2")){
				return "冷备用";
			}else{
				return "检修";
			}
		}
	}
	
	/**
	 * 设备设备操作顺序排序，母线侧在前
	 */
	public static List<PowerDevice> sortByMXC(List<PowerDevice> list){
		List<PowerDevice> newList = new ArrayList<PowerDevice>();
		
		if(list==null){
			return null;
		}else{
			for(PowerDevice dev : list){
				List<PowerDevice> mxList1 = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);

		        if(mxList1.size()>0) {
		        	newList.add(0,dev);
		        }else{
	        		newList.add(dev);
		        }
			}
        }
		return newList;
	}
	
	/**
	 * 按刀闸排序，母线刀闸放后面
	 */
	public static List<PowerDevice> sortDzListByMXDZ(List<PowerDevice> dzList){
		if(dzList.size() ==2){
			PowerDevice pd1 = dzList.get(0);
			PowerDevice pd2 = dzList.get(1);
			
			if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(pd1, pd2, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "", "",
						false, true, true, true, true);
				
				if(zbList.size()==0){
					Collections.reverse(dzList);
				}
			}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.InOutLine)){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd1, pd2, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "",
						false, true, true, true, true);
				
				if(CBSystemConstants.getCurRBM().getPd() != null && !CBSystemConstants.getCurRBM().getPd().getPowerDeviceID().equals("")){
					for(Iterator<PowerDevice> itor = lineList.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!dev.getPowerDeviceID().equals(CBSystemConstants.getCurRBM().getPd().getPowerDeviceID())){
							itor.remove();
						}
					}
				}
				
				if(lineList.size()==0){
					Collections.reverse(dzList);
				}
			}
		}if(dzList.size() == 3){
			List<PowerDevice> newdzList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : dzList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
					newdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : dzList){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
					newdzList.add(dev);
				}
			}
			
			dzList.clear();
			dzList.addAll(newdzList);
		}
		
		return dzList;
	}
	
	/**
	 * 设备设备操作顺序排序，线路侧在前
	 */
	public static List<PowerDevice> sortByXLC(List<PowerDevice> list){
		List<PowerDevice> newList = new ArrayList<PowerDevice>();
		
		if(list==null){
			return null;
		}else{
			for(PowerDevice dev : list){
		        List<PowerDevice> xlList1 = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.InOutLine);

		        if(xlList1.size()>0) {
		        	newList.add(0,dev);
		        }else{
	        		newList.add(dev);
		        }
			}
        }
		return newList;
	}
	
	/**
	 * 设备设备操作顺序排序，操作母线侧的在前
	 */
	public static List<PowerDevice> sortByCZMXC(List<PowerDevice> list){
		List<PowerDevice> newList = new ArrayList<PowerDevice>();
		
		if(list==null){
			return null;
		}else{
			for(PowerDevice dev : list){
				List<PowerDevice> mxList1 = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);

		        if(mxList1.size()>0) {
		        	if(RuleExeUtil.isDeviceChanged(mxList1.get(0))){
		        		newList.add(0,dev);
		        	}else{
		        		newList.add(dev);
		        	}
		        }else{
	        		newList.add(dev);
		        }
			}
        }
		return newList;
	}
}
