package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrJLTVNAME  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("计量电压互感器名称".equals(tempStr)){
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",true, true, true, true);

			PowerDevice tvdz = new PowerDevice();
			
			for(PowerDevice dev : qtdzList){
				if(dev.getPowerDeviceName().contains("计量TV")){
					tvdz = dev;
				}
			}
			
			replaceStr += CZPService.getService().getDevName(tvdz);
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}