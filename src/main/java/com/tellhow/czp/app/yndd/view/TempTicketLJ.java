package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Insets;
import java.awt.Point;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableModel;
import javax.swing.text.JTextComponent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OMSService;
import com.tellhow.czp.app.yndd.impl.WebServiceUtil;
import com.tellhow.czp.app.yndd.tool.Client;
import com.tellhow.czp.app.yndd.tool.CommonUtils;
import com.tellhow.czp.app.yndd.tool.GetDtdDataBN;
import com.tellhow.czp.app.yndd.tool.GetDtdDataLJ;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.mainframe.JPopupTextArea;
import com.tellhow.czp.mainframe.JPopupTextField;
import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.czp.mainframe.menu.GetDeviceMenuModel;
import com.tellhow.czp.operationcard.ReplaceWord;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.operationcard.TempTicketDefault;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.dao.TicketManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.service.OperationCheck;
import com.tellhow.czp.service.OperationCheckDefault;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;
import czprule.wordcard.model.SequencesModel;
import czprule.wordcard.replaceclass.impl.ReplaceStrDYDJ;


public class TempTicketLJ extends TempTicket  {

	protected final JPanel contentPanel = new JPanel();
	private JTable jTable1  = new JTable();
	private JTable jTable2 = new JTable();
	private javax.swing.JScrollPane jScrollPanefj;
	private JTabbedPane jTabbedpane = new JTabbedPane();// 存放选项卡的组件
	protected String bccardid;//保存主表id
	protected boolean isInversing = false; //是否在演示
	boolean vflag = true;//判断综合与逐步
	boolean isSuccess = true;//操作票校验结果
	String cardid =""; //票号
	public static JTextComponent tjc;
	private SetJTableProtery sjp = new SetJTableProtery();
	public static JTextComponent getJc(){
		return getTjc();
	}
	public static void setJc(JTextComponent tjc){
		TempTicketDefault.setTjc(tjc);
	}
	/**
	 * Create the dialog.
	 */
	public TempTicketLJ() {
		initComponents(); 
	}
	protected void initComponents() {
		setBounds(100, 100, 534, 451);
		this.setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		this.add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(new BorderLayout(0, 0));
		{
			JPanel northpanel = new JPanel();
			contentPanel.add(northpanel, BorderLayout.NORTH);
			northpanel.setLayout(new BorderLayout(0, 0));
			{
				JPanel panel = new JPanel();
				northpanel.add(panel, BorderLayout.CENTER);
				panel.setLayout(new BorderLayout(0, 2));
				{
					JPanel panel_t = new JPanel();
					panel.add(panel_t,BorderLayout.NORTH);
					panel_t.setLayout(new BorderLayout(0,0));
					{
						jLabel5 = new JLabel("\u64CD\u4F5C\u4EFB\u52A1\uFF1A");
						panel_t.add(jLabel5, BorderLayout.WEST);
					}
					{
						jScrollPane1 = new JScrollPane();
						panel_t.add(jScrollPane1, BorderLayout.CENTER);
						{
							jTextArea1 = new JPopupTextArea();
							jTextArea1.setPreferredSize(new Dimension(4, 80));
							jTextArea1.setColumns(20);
							jTextArea1.setFont(new java.awt.Font("宋体", 1, 14)); // NOI18N
							jTextArea1.setLineWrap(true);
							jTextArea1.addMouseListener(new MouseAdapter() {
								@Override
								public void mouseClicked(MouseEvent e) {
									Point mousepoint; 
									mousepoint =e.getPoint();
									Component texteditor=jTextArea1.findComponentAt(mousepoint);
									texteditor.requestFocusInWindow();
									Component c = texteditor.getComponentAt(0, 0);
								    if (c != null && c instanceof JTextComponent) {
								        tjc=(JTextComponent)c;
								        setTjc(tjc);
								    }
								}
							});
							jScrollPane1.setViewportView(jTextArea1);
						}
					}
				}
				{
					JPanel  panel_l = new JPanel();
					panel.add(panel_l,BorderLayout.SOUTH);
					panel_l.setLayout(new BorderLayout(0,0));
					{
						jLabel6 = new JLabel("备注事项：");
						panel_l.add(jLabel6,BorderLayout.WEST);
					}
					{
						jScrollPane3 = new JScrollPane();
						panel_l.add(jScrollPane3, BorderLayout.CENTER);	
						{
							jTextArea2 = new JPopupTextArea();
							jTextArea2.setPreferredSize(new Dimension(4,80));
							jTextArea2.setColumns(20);
							jTextArea2.setFont(new java.awt.Font("宋体", 1, 14));
							jTextArea2.setLineWrap(true);
							jTextArea2.addMouseListener(new MouseAdapter() {
								@Override
								public void mouseClicked(MouseEvent e) {
									Point mousepoint; 
									mousepoint =e.getPoint();
									Component texteditor=jTextArea2.findComponentAt(mousepoint);
									texteditor.requestFocusInWindow();
									Component c = texteditor.getComponentAt(0, 0);
								    if (c != null && c instanceof JTextComponent) {
								        tjc=(JTextComponent)c;
								        setTjc(tjc);
								    }
								}
							});
							jScrollPane3.setViewportView(jTextArea2);
						}
					}
				}
			}

			{
				JPanel panel = new JPanel();
				northpanel.add(panel, BorderLayout.SOUTH);
				panel.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_1 = new JPanel();
					panel_1.setPreferredSize(new Dimension(60, 40));
					panel.add(panel_1, BorderLayout.WEST);
					{
						jButton1 = new JButton();
				        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
				        jButton1.setText("增加");
				        jButton1.setToolTipText("增加");
				        jButton1.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton1.setFocusPainted(false);
				        jButton1.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				                jButton1ActionPerformed(evt);
				            }
				        });
						panel_1.add(jButton1);
					}
				}
				{
					JPanel panel_1 = new JPanel();
					panel.add(panel_1, BorderLayout.CENTER);
					panel_1.setLayout(new BorderLayout(0, 0));
					{
						JPanel panel_2 = new JPanel();
						panel_1.add(panel_2, BorderLayout.WEST);
						{
							jButton2 = new JButton();
							jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
					        jButton2.setText("删除");
					        jButton2.setToolTipText("删除");
					        jButton2.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton2.setFocusPainted(false);
					        jButton2.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton2ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton2);
						}
						{
							jButton11 = new JButton();
							jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/import.gif"))); // NOI18N
							jButton11.setText("撤销");
							jButton11.setToolTipText("撤销");
							jButton11.setMargin(new java.awt.Insets(1,1,1,1));
							jButton11.setFocusPainted(false);
							jButton11.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton11ActionPerformed(evt);
					            }
					        });
							//panel_2.add(jButton11);
//							if(CBSystemConstants.cardbuildtype.equals("0"))
//								jButton11.setVisible(false);
						}
						{
							jButton10 = new JButton();
					        jButton10.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/replace.png"))); // NOI18N
					        jButton10.setText("替换");
					        jButton10.setToolTipText("替换");
					        jButton10.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton10.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton10ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton10);
						}
						{
							jButton3 = new JButton();
							jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
					        jButton3.setText("上移");
					        jButton3.setToolTipText("上移");
					        jButton3.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton3.setFocusPainted(false);//是否指示了输入焦点--黄翔
					        jButton3.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton3ActionPerformed(evt);     //给按钮赋予方法，次方法定义在后面--hx
					            }
					        });
							panel_2.add(jButton3);
						}
						{
							jButton4 = new JButton();
					        jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
					        jButton4.setText("下移");
					        jButton4.setToolTipText("下移");
					        jButton4.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton4.setFocusPainted(false);
					        jButton4.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton4ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton4);
						}
						{
							jButton7 = new JButton();
					        jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/merge.png"))); // NOI18N
					        jButton7.setText("合项");
					        jButton7.setToolTipText("合项");
					        jButton7.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton7.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton7ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton7);
							if(CBSystemConstants.roleCode.equals("2"))
								jButton7.setVisible(false);
						}
						{
							jButton8 = new JButton("\u5206\u9879");
							jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/split.png"))); // NOI18N
							jButton8.setText("分项");
							jButton8.setToolTipText("分项");
							jButton8.setMargin(new java.awt.Insets(1,1,1,1));
							jButton8.addActionListener(new java.awt.event.ActionListener() {
								public void actionPerformed(java.awt.event.ActionEvent evt) {
									jButton8ActionPerformed(evt);
								}
							});
							//panel_2.add(jButton8);
							if(CBSystemConstants.roleCode.equals("2"))
								jButton8.setVisible(false);
						}
						{
							jButton14 = new JButton();
							jButton14.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
							jButton14.setText("序列上移");
							jButton14.setToolTipText("序列上移");
							jButton14.setMargin(new java.awt.Insets(1,1,1,1));
							jButton14.setFocusPainted(false);//是否指示了输入焦点--黄翔
							jButton14.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton14ActionPerformed(evt);     //给按钮赋予方法，次方法定义在后面--hx
					            }
					        });
							panel_2.add(jButton14);
						}
						{
							jButton15 = new JButton();
							jButton15.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
							jButton15.setText("序列下移");
							jButton15.setToolTipText("序列下移");
							jButton15.setMargin(new java.awt.Insets(1,1,1,1));
							jButton15.setFocusPainted(false);//是否指示了输入焦点--黄翔
							jButton15.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton15ActionPerformed(evt);     //给按钮赋予方法，次方法定义在后面--hx
					            }
					        });
							panel_2.add(jButton15);
						}
						{
							jButton16 = new JButton();
							jButton16.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/synchronization.png"))); // NOI18N
							jButton16.setText("同步序列");
							jButton16.setToolTipText("同步序列");
							jButton16.setMargin(new java.awt.Insets(1,1,1,1));
							jButton16.setFocusPainted(false);//是否指示了输入焦点--黄翔
							jButton16.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton16ActionPerformed(evt);     //给按钮赋予方法，次方法定义在后面--hx
					            }
					        });
							panel_2.add(jButton16);
						}
						{
							jButton9 = new JButton("校验");
					        jButton9.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/check.png"))); // NOI18N
					        jButton9.setText("校验");
					        jButton9.setToolTipText("校验");
					        jButton9.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton9.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton9ActionPerformed(evt);
					            }
					        });
							//panel_2.add(jButton9);
						}
						{
							cmb2 = new JComboBox();
							cmb2.addItem("请选择备注事项类型");
							cmb2.addItem("停电");
							cmb2.addItem("复电");
							
							//panel_2.add(cmb2);
							
							cmb2.addItemListener(new ItemListener() {
					            @Override
					            public void itemStateChanged(ItemEvent e) {
					                if (e.getStateChange() == ItemEvent.SELECTED) {
					                	if(e.getItem().equals("停电")){
					                		jTextArea2.setText("1.按检修申请：XXXXXX执行。\n2.核实现场人员已到达现场，已进行过现场操作勘察，具备操作条件。");
					                	}else if(e.getItem().equals("复电")){
					                		jTextArea2.setText("1.核实检修申请：XXXXXXX现场工作任务已结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，该设备的保护装置已正常投入，具备送电条件。\n2.核实现场人员已到达现场，已进行过现场操作勘察，具备操作条件。\n3.已对设备状态进行核对确认。");
					                	}
					                }
					            }
					        });
						}
						{
							jButton12 = new JButton("演示");
							jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/play.png"))); // NOI18N
							jButton12.setText("演示");
							jButton12.setToolTipText("演示");
					        jButton12.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton12.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton12ActionPerformed(evt);
					            }
					        });
							//panel_2.add(jButton12);//江西不加演示功能
						}
						{
							jButton13 = new JButton("反向成票");
//							 if(CBSystemConstants.cardbuildtype.equals("1") || CBSystemConstants.getSamepdlist().size()>0){
//							        jButton13.setVisible(false);
//							 }
							jButton13.setVisible(false); //重庆暂时不用反向成票
							jButton13.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/play.png"))); // NOI18N
							jButton13.setText("反向成票");
							jButton13.setToolTipText("反向成票");
							jButton13.setMargin(new java.awt.Insets(1,1,1,1));
							jButton13.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton13ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton13);
						}
						{
							cmb = new JComboBox();
							cmb.addItem("请选择操作类型");
							cmb.addItem("主变停电");
							cmb.addItem("主变复电");
							cmb.addItem("母线停电");
							cmb.addItem("母线复电");
							cmb.addItem("线路停电");
							cmb.addItem("线路复电");
							cmb.addItem("方式变更");
							cmb.addItem("安稳状态变更");
							cmb.addItem("串补停电");
							cmb.addItem("串补复电");
							cmb.addItem("继电保护");
							cmb.addItem("断路器");
							cmb.addItem("旁路代供");
							cmb.addItem("恢复本断路器");
							cmb.addItem("其他");
							
							panel_2.add(cmb);
						}
					}
				}
			}
			{
				JPanel panel = new JPanel();
				northpanel.add(panel, BorderLayout.NORTH);
				panel.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_1 = new JPanel();
					panel.add(panel_1, BorderLayout.CENTER);
					panel_1.setLayout(new BorderLayout(0, 0));
					{
						//JPanel panel_2 = new JPanel();
						JPanel panel_3 = new JPanel();
						JPanel panel_4 = new JPanel();
//						panel_1.add(panel_2, BorderLayout.WEST);
//						{
//							lblNewLabel_1 = new JLabel("单位：");
//							panel_2.add(lblNewLabel_1);
//						}
//						{
//							lblNewLabel = new JLabel("");
//							lblNewLabel.setVerticalAlignment(SwingConstants.TOP);
//							lblNewLabel.setFont(new java.awt.Font("微软雅黑", 1, 13)); // NOI18N
//							panel_2.add(lblNewLabel);
//						}
						panel_1.add(panel_3, BorderLayout.WEST);
						{
							jLabel1 = new JLabel("拟票人：");
							//jLabel1.setPreferredSize(new Dimension(48, 50));
							panel_3.add(jLabel1);
						}
						{
							jLabel4 = new JLabel("");
							jLabel4.setFont(new java.awt.Font("微软雅黑", 1, 13)); // NOI18N
					        jLabel4.setText("jLabel4");
							panel_3.add(jLabel4);
						}
						panel_1.add(panel_4, BorderLayout.EAST);
						{
							jLabel2 = new JLabel("拟票时间：");
							panel_4.add(jLabel2);
						}
						{
							jLabel3 = new JLabel("");
							jLabel3.setFont(new java.awt.Font("微软雅黑", 1, 13)); // NOI18N
					        jLabel3.setText("jLabel3");
							panel_4.add(jLabel3);
						}
					}
				}
			}
		}
		{
			jScrollPane2 = new JScrollPane();
			jTabbedpane.addTab("操作票", null, jScrollPane2);// 加入第一个页面
			contentPanel.add(jTabbedpane, BorderLayout.CENTER);
			{
				//设置不可编辑单元格
				jTable1 = new JTable() {
					public boolean isCellEditable(int rowindex,int colindex){
				        if (colindex==5) return false;
				        return true;  
					}//其他列可以修改
				};
				JPopupTextField jtf = new JPopupTextField();
				jtf.setFont(new Font("宋体",Font.PLAIN,14));
				DefaultCellEditor editor = new DefaultCellEditor(jtf);
				jTable1.setDefaultEditor(Object.class, editor);
				//支持多选 单选 悬着竖排
				//jTable1.setColumnSelectionAllowed(true);
				jTable1.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
				jTable1.setPreferredScrollableViewportSize(new Dimension(450, 200));
				jTable1.setFont(new Font("宋体",Font.PLAIN,14)); // NOI18N	
		        jTable1.setRowHeight(26);
				jScrollPane2.setViewportView(jTable1);
			}
			{
				jScrollPanefj = new JScrollPane();
				jTabbedpane.addTab("操作序列", null, jScrollPanefj);// 加入第二个页面
				JPopupTextField jtf2 = new JPopupTextField();
				jtf2.setFont(new Font("宋体",Font.PLAIN,14));
				jTable2.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
				jTable2.setPreferredScrollableViewportSize(new Dimension(450, 200));
				jTable2.setFont(new Font("宋体",Font.PLAIN,14)); // NOI18N	
		        jTable2.setRowHeight(26);
				jScrollPanefj.setViewportView(jTable2);
			}
			
		}
		{
			JPanel buttonPane = new JPanel();
			buttonPane.setLayout(new FlowLayout(FlowLayout.RIGHT));
			{
				
				if(CBSystemConstants.roleCode.equals("0")) {
					smmlCheckBox = new JCheckBox();
					smmlCheckBox.setForeground(new java.awt.Color(204, 0, 0));
					smmlCheckBox.setText("书面命令");
					buttonPane.add(smmlCheckBox);
					smmlCheckBox.addItemListener(itemListenerSMML);
					
					jkCheckBox = new JCheckBox();
					jkCheckBox.setForeground(new java.awt.Color(204, 0, 0));
					jkCheckBox.setText("监控票");
					buttonPane.add(jkCheckBox);
					jkCheckBox.addItemListener(itemListenerJK);
					
					tczlCheckBox = new JCheckBox();
					tczlCheckBox.setForeground(new java.awt.Color(204, 0, 0));
					tczlCheckBox.setText("投产指令");
					buttonPane.add(tczlCheckBox);
					tczlCheckBox.addItemListener(itemListenerTCZL);
					
					skCheckBox = new JCheckBox();
					skCheckBox.setForeground(new java.awt.Color(204, 0, 0));
					skCheckBox.setText("顺控票");
					buttonPane.add(skCheckBox);
					skCheckBox.addItemListener(itemListenerSK);
					
				}
				
//				jButton5 = new JButton();
//				jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
//				jButton5.setText("正式保存");
//				jButton5.setToolTipText("正式保存");
//				jButton5.setMargin(new java.awt.Insets(1,1,1,1));
//				jButton5.addActionListener(new java.awt.event.ActionListener() {
//				    public void actionPerformed(java.awt.event.ActionEvent evt) {
//				        jButton5ActionPerformed(evt,true);
//				    }
//				});
//				//cancelButton.setActionCommand("Cancel");
//				buttonPane.add(jButton5);
				savejButtonBS = new JButton();
				savejButtonBS.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
				savejButtonBS.setText("正式保存(BS)");
				savejButtonBS.setToolTipText("正式保存(BS)");
				savejButtonBS.setMargin(new java.awt.Insets(1,1,1,1));
				savejButtonBS.addActionListener(new java.awt.event.ActionListener() {
					public void actionPerformed(java.awt.event.ActionEvent evt) {
						jButton5ActionPerformed(evt,true,"1");
					}
				});
				buttonPane.add(savejButtonBS);

				savejButtonCS = new JButton();
				savejButtonCS.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
				savejButtonCS.setText("正式保存(CS)");
				savejButtonCS.setToolTipText("正式保存(CS)");
				savejButtonCS.setMargin(new java.awt.Insets(1,1,1,1));
				savejButtonCS.addActionListener(new java.awt.event.ActionListener() {
					public void actionPerformed(java.awt.event.ActionEvent evt) {
						jButton5ActionPerformed(evt,true,"0");
					}
				});
				buttonPane.add(savejButtonCS);
				{
					button = new JButton();
					button.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
					button.setToolTipText("本地保存");
					button.setText("本地保存");
					button.setMargin(new Insets(1, 1, 1, 1));
					button.addActionListener(new java.awt.event.ActionListener() {
					    public void actionPerformed(java.awt.event.ActionEvent evt) {
					        jButton5ActionPerformed(evt,false,"2");
					    }
					});
					buttonPane.add(button);
				}
			}
			{
				btnoms = new JButton();
				btnoms.addActionListener(new ActionListener() {
					public void actionPerformed(ActionEvent evt) {
						 jButton5ActionPerformed(evt,false,"3");
					}
					
				});
				if(CBSystemConstants.roleCode.equals("0"))
					btnoms.setVisible(false);
				btnoms.setText("导入OMS");
				btnoms.setToolTipText("导入OMS");
				btnoms.setVisible(false);
				btnoms.setVisible(false);
				btnoms.setMargin(new java.awt.Insets(1,1,1,1));
				//buttonPane.add(btnoms);
			}
			
			jButton6 = new JButton();
			//jButton6.setPreferredSize(new Dimension(33, 20));
	        jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
	        jButton6.setText("关闭");
	        jButton6.setToolTipText("关闭");
	        jButton6.setMargin(new java.awt.Insets(1,1,1,1));
	        jButton6.setFocusPainted(false);
	        jButton6.addActionListener(new java.awt.event.ActionListener() {
	            public void actionPerformed(java.awt.event.ActionEvent evt) {
	            	TempTicket.isRoll=true;
	                jButton6ActionPerformed(evt);
	                TempTicket.isRoll=false;
	            }
	        });
	        buttonPane.add(jButton6);
			
			contentPanel.add(buttonPane, BorderLayout.SOUTH);
			{
				jCheckBox1 = new JCheckBox();
				jCheckBox1.setForeground(new java.awt.Color(204, 0, 0));
		        jCheckBox1.setText("另存为典型票");
				buttonPane.add(jCheckBox1);
			}
		}
		{
			JPanel panel = new JPanel();
			this.add(panel, BorderLayout.NORTH);
			panel.setLayout(new BorderLayout(0, 0));
//			{
//				jButton6 = new JButton();
//				//jButton6.setPreferredSize(new Dimension(33, 20));
//		        jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
//		        jButton6.setText("取消");
//		        jButton6.setToolTipText("取消");
//		        jButton6.setMargin(new java.awt.Insets(1,1,1,1));
//		        jButton6.setFocusPainted(false);
//		        jButton6.addActionListener(new java.awt.event.ActionListener() {
//		            public void actionPerformed(java.awt.event.ActionEvent evt) {
//		            	TempTicket.isRoll=true;
//		                jButton6ActionPerformed(evt);
//		                TempTicket.isRoll=false;
//		            }
//		        });
//				panel.add(jButton6, BorderLayout.EAST);
//			}
			{
				panel_5 = new JPanel();
//				if(CZPOperator.getOperator().getClass().getName().equals("com.tellhow.czp.app.CZPOperatorJC"))
//					panel.add(panel_5, BorderLayout.WEST);
				{
					lblNewLabel_2 = new JLabel("关联检修票");
					panel_5.add(lblNewLabel_2);
				}
				{
					textField = new JTextField();
					textField.setEditable(false);
					panel_5.add(textField);
					textField.setColumns(10);
				}
				{
					btnNewButton = new JButton("选择");
					btnNewButton.addActionListener(new ActionListener() {
						public void actionPerformed(ActionEvent e) {
							String jxpno = OMSService.getService().getJXPNO();
							if (!jxpno.equals("")){
								textField.setText(jxpno);
							}
						}
					});
					panel_5.add(btnNewButton);
				}
				{
					lblNewLabel_3 = new JLabel("关联操作票");
					panel_5.add(lblNewLabel_3);
				}
				{
					textField_1 = new JTextField();
					textField_1.setEditable(false);
					panel_5.add(textField_1);
					textField_1.setColumns(10);
				}
				{
					btnNewButton_1 = new JButton("选择");
					btnNewButton_1.addActionListener(new ActionListener() {
						public void actionPerformed(ActionEvent e) {
							String czpno = OMSService.getService().getCZPNO();
							if (!czpno.equals("")){
								textField_1.setText(czpno);
							}
						}
					});
					panel_5.add(btnNewButton_1);
				}
			}
		}
	}
	
	private List<CardItemModel> getItemModel() {
		String preStationName = "";
		List<CardItemModel> DescList = new ArrayList<CardItemModel>();
		if(jTable1Model.getRowCount() > 0) {
			for (int i = 0; i < jTable1Model.getRowCount(); i++) {
				CardItemModel cim = new CardItemModel();
				if(jTable1Model.getValueAt(i, 0) instanceof CardItemModel) {
					CardItemModel cnm=(CardItemModel)jTable1Model.getValueAt(i, 0);
				    cim.setUuIds(cnm.getUuIds());
				    cim.setBzbj(cnm.getBzbj());
				}
				else {
					cim.setUuIds(StringUtils.getUUID());
				}
				
			    cim.setCardNum(StringUtils.ObjToString(jTable1Model.getValueAt(i, 2)));
			    cim.setCardItem(getStageInt(StringUtils.ObjToString(jTable1Model.getValueAt(i, 1))));
			    
			    String showName = StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
			    cim.setShowName(showName);
			    
			    String cardDesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
				cim.setCardDesc(cardDesc);
				
			    String stationName= StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
			    if(cardDesc.equals(""))
			    	stationName = "";
			    else if((stationName.equals("") || stationName.indexOf("（")>=0))
			    	stationName = preStationName;
				cim.setStationName(stationName);
				
				DescList.add(cim);
				if(!stationName.equals("") && stationName.indexOf("（")==-1)
					preStationName = stationName;
			}
		}
		return DescList;
	}

	//保存
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt,boolean flag,String systemType) {
		if(!isSuccess) {
			int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"操作票校验不通过，是否确认要保存操作票？",
					CBSystemConstants.SYSTEM_TITLE,
					JOptionPane.YES_NO_OPTION);
			if (isok == JOptionPane.NO_OPTION)
				return;
		}
		if(isInversing) {
			ShowMessage.view(this, "请先停止操作票演示再保存！");
			return;
		}
		int row = jTable1.getRowCount();
		if (row == 0) {
			ShowMessage.view(this, "数据为空,保存失败！");
			return;
		}
		if (jTextArea1.getText().trim().equals("")) {
			ShowMessage.view(this, "操作任务不能为空,保存失败！");
			return;
		}
		
		if (cmb.getSelectedItem().equals("请选择操作类型")){
			ShowMessage.view(this, "请选择操作类型！");
			return;
		}
		
		if(!checkContent())
			return;
		
		//单元格编辑状态下点击保存
		if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
		String czrw = jTextArea1.getText().trim();//编辑修改后的操作任务名称
		String bzsx = jTextArea2.getText().trim();
		String jxpdh = "";//检修票单号
		String cardKind = "0";
		String isPW = "0";
		String isyf = "0";
		List<CardItemModel> descList = saveList(getItemModel());
		if(checkList(descList)==false){
			ShowMessage.viewWarning(this, "操作阶段数据存在异常,请检查！");
			return;
		}
		jTable1Model =  (DefaultTableModel) jTable1.getModel();
		
//		List<String> stationList = new ArrayList<String>();
//		for (int i = 0; i < jTable1.getRowCount(); i++) {
//			
//		    String cardDesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
//		    String stationName= StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
//		    if(!stationName.equals("") && !cardDesc.equals("") && !stationList.contains(stationName))
//		    	stationList.add(stationName);
//		}
//		if(CBSystemConstants.roleCode.equals("2"))
//			cardKind = "3";
//		else if(stationList.size() >= 2)
//			cardKind = "1";
		if(CBSystemConstants.roleCode.equals("0")) {
			if(jkCheckBox.isSelected()) {
				cardKind = "3";
			}else if(skCheckBox.isSelected()) {
				cardKind = "4";
			}if(smmlCheckBox.isSelected()){ 
				cardKind = "5";
			}else if(tczlCheckBox.isSelected()) {
				cardKind = "6";
			}
		}
		
		if(cardid.equals("")) {
			if(CBSystemConstants.getImportInfo()!=null&&!CBSystemConstants.getImportInfo().equals("")){
				cardid=CBSystemConstants.getImportInfo();
			}else{
				cardid=StringUtils.getUUID();
			}
		}
		
		List<SequencesModel> descList2 = getItemModel2();

		TicketManager ticket = new TicketManager();
		TicketDBManager ticketDB = new TicketDBManager();
		
		try {
			ticket.delTicket(cardid);//
			InsertTicketDB(cardid,descList,descList2, czrw, bzsx, "0", cardKind, CBSystemConstants.cardbuildtype, Srcrbm.getPd().getPowerDeviceID(),jxpdh);
			
			if(flag){
				importOMS(cardid,descList,descList2, czrw, bzsx, "0", cardKind, CBSystemConstants.cardbuildtype, Srcrbm.getPd().getPowerDeviceID(),jxpdh,isyf,isPW,systemType);
			}
			
			ticket.insertDevState(cardid); // 保存操作票对应设备元件状态变更
			ticket.insertDevStatus(); // 保存操作票对应设备预令状态
			
			if (this.jCheckBox1.isSelected()) {
				ticketDB.InsertDXTicketDB(cardid);
			}

			CBSystemConstants.clearLineSourceAndLoad();
			if(!flag){
				ShowMessage.view(this, "保存成功！");
			}
		} catch (SQLException e) {
			
			e.printStackTrace();
			//回滚
			DeviceOperate.RollbackDeviceStatus();
			ShowMessage.view(this, "保存失败！");  
		} finally {
			if(CBSystemConstants.roleCode.equals("2")){
				Map<Integer, DispatchTransDevice> all=DeviceOperate.getAlltransDevMap();
				for(int i=1;i<all.size()+1;i++){
					DispatchTransDevice dtd=all.get(i);
					dtd.setIssave("1");
				}
			}
			//清空
			DeviceOperate.ClearDevMap();
			CBSystemConstants.getDtdMap().clear();
			CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
			CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
	    	//CBSystemConstants.bztRelationRecord.clear();
			CBSystemConstants.bztStateRecord.clear();
			//CBSystemConstants.bztOrganRecord.clear();
			SvgUtil.clear();
//			setVisible(false);
			if(!CBSystemConstants.roleCode.equals("0"))
				setdel();
			tempTicket = null;
		}
		
	}
	
	/**
	 * 描述：保存新建操作票
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czxlList
	 *            操作票序列集合
	 * @param czrw
	 *            操作任务
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public String InsertTicketDB(String zbid, List<CardItemModel> list,List<SequencesModel>czxlList, String czrw, String bzsx, String isModel, String cardKind,
			String buildKind, String equipID, String jxpNo) {
		String date = "sysdate";
		
		if (CBSystemConstants.isOffline) {
			date = "date('now')";
		}
		
		String ticketType =  (String) cmb.getSelectedItem();

		// 插入操作票主表
		String zbsql = "INSERT INTO " + CBSystemConstants.opcardUser + "T_A_CZPZB(ZBID,CZRW,BZSX,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,CZLX) VALUES('" + zbid + "','"
				+ czrw + "','" + bzsx + "'," + buildKind + ",'" + CBSystemConstants.getUser().getUserName() + "'," + date + ",'" + isModel + "'," + cardKind
				+ ",'" + CBSystemConstants.opCode + "','" + equipID + "','" + jxpNo + "','" + ticketType + "')";
		DBManager.execute(zbsql);
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			// String operateNum = String.valueOf(i+1);
			String operateItem = bcm.getCardItem();
			//解决操作票不一致和czdw为空的bug
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();;
			
			String operateContent = bcm.getCardDesc();
			String mxid = bcm.getUuIds();
			if (operateItem.equals("")) {
				operateItem = operateNum;
				// operateItem= bcm.getCardNum();
			}
			String mxsql = "INSERT INTO  " + CBSystemConstants.opcardUser + "T_A_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ operateShow + "','" + operateContent + "'," + operateNum + "," + operateItem + ",'" + operateShow + "')";
			DBManager.execute(mxsql);
		}
		
		String deleteczxlsql = "DELETE FROM " + CBSystemConstants.opcardUser + "T_SK_OPERATION WHERE CZP_ID = '"+zbid+"'";
		DBManager.execute(deleteczxlsql);
		
		for (int i = 0; i < czxlList.size(); i++) {//还是不偷懒用序列对象,不然不好看
			SequencesModel czxl = czxlList.get(i);
			
			String devId = czxl.getDevId();
			String targetstatus = czxl.getTargetStatus();
			String mxid = czxl.getMxid();
			String devName = czxl.getDevName();
			String czzl = czxl.getCzzl();
			String sfkk = czxl.getSfkk();
			String sscz = czxl.getSscz();
			String czcz = czxl.getCzcz();
			
			if(sfkk.equals("否")){
				sfkk = "0";
			}else if(sfkk.equals("是")){
				sfkk = "1";
			}
			
			String czxlsql = "INSERT INTO  " + CBSystemConstants.opcardUser + "T_SK_OPERATION(FID,DEV_ID,CZP_ID,CZ_NUMBER,"
					+ "CREATE_TIME,MX_ID,DEV_NAME,CZZL,SFKK,SSCZ,CZCZ,TARGET_STATUS) VALUES "
					+ "('"+StringUtils.getUUID()+"','"+devId+"','"+zbid+"',"+(i+1)+","+date+",'"+mxid+"','"+devName+"','"+czzl+"','"+sfkk+"','"+sscz+"','"+czcz+"','"+targetstatus+"')";
			
			DBManager.execute(czxlsql);
		}
		return zbid;
	}
	
	public void setdel(){
		this.setVisible(false);
	}
	/**
	 * 获取操作序列
	 * @return 设备ID(dev_id),起始状态D5000(original_status),目标状态D5000(target_status)
	 * 			操作票明细ID(mx_id),设备名称(dev_name)
	 * 			操作指令(czzl),操作厂站(czcz)
	 */
	private List<SequencesModel> getItemModel2() {
		List<SequencesModel> czxlList = new ArrayList<SequencesModel>();
		for (int i = 0; i < jTable2Model.getRowCount(); i++) {
			String czdw = StringUtils.ObjToString(jTable2Model.getValueAt(i, 1));
			String cznr = StringUtils.ObjToString(jTable2Model.getValueAt(i, 2));
			String equipid = StringUtils.ObjToString(jTable2Model.getValueAt(i, 3));
			String equipName = StringUtils.ObjToString(jTable2Model.getValueAt(i, 4));
			String stationName = StringUtils.ObjToString(jTable2Model.getValueAt(i, 5));
			String op = StringUtils.ObjToString(jTable2Model.getValueAt(i, 6));
			String isyk = StringUtils.ObjToString(jTable2Model.getValueAt(i, 7));
			String id = StringUtils.ObjToString(jTable2Model.getValueAt(i, 8));
			String czpsx = StringUtils.ObjToString(jTable2Model.getValueAt(i, 9));

			String[] nameArr = CommonUtils.getDevNameAndStationNameByDevId(equipid);
			if (nameArr[0] != null) equipName = nameArr[0];
			if (nameArr[1] != null) stationName = nameArr[1];

			SequencesModel sqm = new SequencesModel();
			
			sqm.setCzcz(czdw);
			sqm.setCzzl(cznr);
			sqm.setDevId(equipid);
			sqm.setDevName(equipName);
			sqm.setSscz(stationName);
			sqm.setTargetStatus(op);
			sqm.setMxid(id);
			sqm.setSfkk(isyk);
			
			czxlList.add(sqm);
		}
		return czxlList;
	}
	public boolean checkContent() {
		int countRowsI = jTable1.getRowCount();
		for (int i = 0; i < countRowsI; i++) {
			String czsx = jTable1.getValueAt(i, 2) == null?"":jTable1.getValueAt(i, 2).toString().trim();
			String czdw = jTable1.getValueAt(i, 3) == null?"":jTable1.getValueAt(i, 3).toString().trim();
			String cznr = jTable1.getValueAt(i, 4) == null?"":jTable1.getValueAt(i, 4).toString().trim();
			if(cznr.equals("")) {
			}
			if(!cznr.equals("")) {
				if(czdw.equals("")) {
				}
				if(czsx.equals("")) {
					if(i == 0)
						jTable1.setValueAt("1", i, 1);
					else if(jTable1.getValueAt(i, 1).toString().equals(jTable1.getValueAt(i-1, 1).toString()))
						jTable1.setValueAt(jTable1.getValueAt(i-1, 1).toString(), i, 1);
					else
						jTable1.setValueAt(String.valueOf(Integer.valueOf(jTable1.getValueAt(i-1, 1).toString())+1), i, 1);
				}
				else if(!StringUtils.isNum(czsx)) {
					String sz = StringUtils.getSZ(czsx);
					if(sz.equals("")) {
						ShowMessage.viewWarning(this, "第"+String.valueOf(i+1)+"行操作顺序填写的不是数字!");
						return false;
					}
					else
						jTable1.setValueAt(sz, i, 1);
				}
			}
		}
		return true;
	}
	/**
	  * 创建时间 2013年11月26日 上午9:51:43
	  * 操作票校核
	  * <AUTHOR>
	  * @Title checkOps
	  * @return
	  */
	public boolean checkOps(){
		//复制原来操作的缓存
		//Map<Integer, DispatchTransDevice> dtdmap =new HashMap<Integer, DispatchTransDevice>(CBSystemConstants.getDtdMap());
		//Map<Integer, DispatchTransDevice> allMap =new HashMap<Integer, DispatchTransDevice>(DeviceOperate.getAlltransDevMap());
		//Map<Integer, PowerDevice> curOprDev = CBSystemConstants.getCurOperateDevs();
		
		//DeviceOperate.getAlltransDevMap().clear();
		
		//DeviceOperate.CheckRollback();
		/*Map<String, HashMap<String, PowerDevice>> map = CBSystemConstants.getMapPowerStationDevice();
		for (Iterator<String> iterator = map.keySet().iterator(); iterator.hasNext();) {
			String stid = (String) iterator.next();
			//首先初始化所有关联厂站的缓存
			new StationDeviceToplogy().StationLoad(stid);
		}*/
		
		
		String sysBuild = CBSystemConstants.cardbuildtype;
		
		//DeviceOperate.ClearDevMap();
		int preSize = DeviceOperate.getAlltransDevMap().size();
		
		final ArrayList<int[]> errorList = new ArrayList<int[]>();
		errorList.clear();
	
		String stationname;
		String oprdesc;
		RuleBaseMode b;	
		boolean result=true;
		
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			stationname = StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
			oprdesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
			b=OperationCheck.execute(stationname, oprdesc);
			if(!b.getCheckout()){
				if(b.getMessageList().size()!=0){
					ShowMessage.view(b.getMessageList().get(0));
				}
				
				
				errorList.add(new int[]{i,2});
				ShowMessage.view("校核["+stationname+"]站["+oprdesc+"]操作发现异常");
				result=false;
				break;
			}
			
		}
		
		DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component cell = super.getTableCellRendererComponent  
                        (table, value, isSelected, hasFocus, row, column);
                boolean isError = false;
                for(int[] c : errorList) {
                	if(c[0] == row && c[1] == column && cell.isBackgroundSet()){ {
                		cell.setForeground(Color.red);
                		isError = true;
                		break;
                	}
                }
                if(!isError)
                	cell.setForeground(Color.black);
                   
              }
                return cell;
            }
        };
		jTable1.getColumnModel().getColumn(2).setCellRenderer(tcr);
		jTable1.updateUI();
		
//		if(result){
//			Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
//			for (int i = allMap.size(); i >0; i--) {
//				DispatchTransDevice dtd = allMap.get(i);
//				DeviceSVGPanelUtil.changeDeviceSVGColor(dtd.getTransDevice());
//			}
//			
//		}
		
		if(result) {
			Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
			for (int i = allMap.size(); i >preSize; i--) {
				allMap.remove(i);
			}
		}
		
		CBSystemConstants.cardstatus="0";
		OperationCheck.loadStation.clear();
		CBSystemConstants.cardbuildtype=sysBuild;
		return result;
	}
	
	//排序行
	public void paixuTableRow(JTable table , int col) {
		DefaultTableModel model = (DefaultTableModel)table.getModel();
		int rowCount = table.getRowCount();
        for (int i = 0; i <= rowCount - 1; i++) {//对行数组遍历--hx
    		table.setValueAt(i+1, i, col);
    	}     
        
        if(table.getSelectedRow()!=-1){
        	 String curStage = getStageInt(""+table.getValueAt(table.getSelectedRow(), 1));
             int nextStage = Integer.valueOf(curStage)+1;
             
             table.setValueAt(getStage(String.valueOf(nextStage)), table.getSelectedRow()+1, 1);
             
//             String oldstage = "";
//             
//             for (int i = 0; i <= rowCount - 1; i++) {//对行数组遍历--hx
//            	 if(i>=nextStage){
//            		 int newstageint = nextStage+1;
//            		 
//            		 table.getValueAt(newstageint, 1);
//            		 
//                     table.setValueAt(getStage(String.valueOf(newstageint)), i, 1);
//            	 }
//         	}
        }else{
        	String curStage = getStageInt(""+table.getValueAt(table.getRowCount()-2, 1));
        	
        	int nextStage = Integer.valueOf(curStage)+1;
            
            table.setValueAt(getStage(String.valueOf(nextStage)), table.getRowCount()-1, 1);
        }
        
        table.setValueAt(UUID.randomUUID().toString(), table.getRowCount()-1, 5);
    }
	
	//小写转大写
	public static String getStageInt(String str) {
		 str = str.replace("阶段", "");
		 
		 int stageint = RuleExeUtil.chineseNumber2Int(str);
		 
	    return String.valueOf(stageint);
    }
	
	//新增
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		int row;
		DefaultTableModel model = (DefaultTableModel)jTable1.getModel();
		model = (DefaultTableModel) jTable1.getModel();
		if (jTable1.getSelectedRow() != -1) {
			row = jTable1.getSelectedRow() + jTable1.getSelectedRowCount();
			model.insertRow(row, new Object[] {});
		}
		else {
			row = jTable1.getRowCount();
			model.addRow(new Object[] {});
		}
		
		paixuTableRow(jTable1,2);
	}
	//删除
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"是否确认删除选中的操作内容？",
				CBSystemConstants.SYSTEM_TITLE,
				JOptionPane.YES_NO_OPTION);
		if (isok != JOptionPane.YES_OPTION)
			return;
		
		int[] selectRows = jTable1.getSelectedRows();
		
		jTable1.removeEditor();
		DefaultTableModel model = (DefaultTableModel) jTable1.getModel();
		
		for (int i = selectRows.length - 1; i >= 0; i--) {
			String zlglid = StringUtils.ObjToString(jTable1.getModel().getValueAt(selectRows[i], 5));

			model.removeRow(selectRows[i]);
			
			for (int j = jTable2.getRowCount() - 1; j >= 0 ; j--) {
				String xlglid = StringUtils.ObjToString(jTable2.getModel().getValueAt(j, 8));//序列的关联id
				
				if(zlglid.equals(xlglid)){
					DefaultTableModel model2 = (DefaultTableModel) jTable2.getModel();
					model2.removeRow(j);
				}
			}
		}
		
		for(int i = 0; i < jTable1.getRowCount(); i++){
			String stage = getStage(String.valueOf(i+1));
			
			jTable1.getModel().setValueAt(stage, i, 1);
			jTable1.getModel().setValueAt(String.valueOf(i+1), i, 2);
		}
		
		for (int i = 0; i < jTable2.getRowCount(); i++) {
			jTable2.getModel().setValueAt(String.valueOf(i+1), i, 0);
			String xlglid = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 8));//序列的关联id
			
			for(int j = 0; j < jTable1.getRowCount(); j++){
				String zlglid = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 5));
				String sxglid = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 2));

				if(zlglid.equals(xlglid)){
					jTable2.getModel().setValueAt(sxglid, i, 9);
					break;
				}
			}
		}
	}
		
	//撤销
	private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {
		int bzbj = DeviceOperate.getBzIndex();
		if(bzbj > 0) {
			DeviceOperate.RollbackLastStatusStep(String.valueOf(bzbj));
			for(int i=itemModels.size()-1;i>=0;i--){
				if(itemModels.get(i).getBzbj().equals(String.valueOf(bzbj)))
					itemModels.remove(i);
			}
			DeviceOperate.setBzIndex(bzbj-1);
		}
		
		initTable ();
	}
	//上移
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {  //上移方法
		if(jTable1.getSelectedRow() == 0){
			return;
		}else if(jTable1.getSelectedRow() == -1){
			ShowMessage.viewWarning(this, "未选择操作票指令！");
			return;
		}
		WindowUtils.moveupTableRow(jTable1);
		WindowUtils.paixuTableRow(jTable1,2);
		
		String bfStage = getStageInt(""+jTable1.getValueAt(jTable1.getSelectedRow(), 1));
		String curStage = getStageInt(""+jTable1.getValueAt(jTable1.getSelectedRow()+1, 1));
        
		jTable1.setValueAt(getStage(bfStage), jTable1.getSelectedRow()+1, 1);
		jTable1.setValueAt(getStage(curStage), jTable1.getSelectedRow(), 1);

		this.changeTable2Czpsx();
	}
	
	//上移
	private void jButton14ActionPerformed(java.awt.event.ActionEvent evt) {  //上移方法
		if(jTable2.getSelectedRow() == 0){
			return;
		}else if(jTable2.getSelectedRow() == -1){
			ShowMessage.viewWarning(this, "未选择操作票指令！");
			return;
		}
		WindowUtils.moveupTableRow(jTable2);
		WindowUtils.paixuTableRow(jTable2,0);
	}
	
	//上移
	private void jButton15ActionPerformed(java.awt.event.ActionEvent evt) {  //上移方法
		if(jTable2.getSelectedRow() == 0){
			return;
		}else if(jTable2.getSelectedRow() == -1){
			ShowMessage.viewWarning(this, "未选择操作票指令！");
			return;
		}
		WindowUtils.movedownTableRow(jTable2);
		WindowUtils.paixuTableRow(jTable2,0);
	}
	
	private void jButton16ActionPerformed(java.awt.event.ActionEvent evt) {
		//单元格编辑状态下点击保存
		if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
		
		List<CardItemModel> itemModelsShow = new ArrayList<CardItemModel>();
		
		for(int j = 0; j < jTable1.getRowCount(); j++){
			CardItemModel cim = new CardItemModel();
			
			String stationname = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 3));
			String cznr = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 4));
			String zlglid = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 5));
			String issk = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 6));
			String bzdname = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 7));

			cim.setCardDesc(cznr);
			cim.setBdzName(bzdname);
			cim.setStationName(stationname);
			cim.setUuIds(zlglid);
			
			if(issk.equals("true")){
				cim.setIssk(true);
			}else{
				cim.setIssk(false);
			}
			
			
			itemModelsShow.add(cim);
		}
		
		jTable2Model.setRowCount(0);
		
		ArrayList<String[]> djzl = GetDtdDataLJ.getData(itemModelsShow,"丽江地调");
		
		String glidbf = "";
		int czpsx= 0;
		
		int jTable2RowCount = jTable2Model.getRowCount();
		
		for(int i=0;i<djzl.size();i++){
			String czdw = djzl.get(i)[2];
			String cznr = djzl.get(i)[4];
			
			if(cznr.contains("中性点")&&cznr.contains("落实")){
				cznr = cznr.replace("落实", "确认");
			}
			
			if(!djzl.get(i)[11].equals(glidbf)){
				czpsx ++;
				glidbf = djzl.get(i)[11];
			}
			
			PowerDevice dev = CBSystemConstants.getPowerDevice(djzl.get(i)[6]);
			String devName = "";
			if(dev!=null){
				devName = dev.getPowerDeviceName();
			}
			
			Object[] rowData = new Object[]{ i+1+jTable2RowCount ,czdw, cznr, djzl.get(i)[6],devName,djzl.get(i)[3],djzl.get(i)[9],""
					,djzl.get(i)[11],String.valueOf(czpsx)};	
			
			jTable2Model.addRow(rowData);
		}
		
		//设置可控
		for(int j = 0;j<jTable2Model.getRowCount();j++){
			String cznr = StringUtils.ObjToString(jTable2Model.getValueAt(j, 2)).trim();
			String deviceid = StringUtils.ObjToString(jTable2Model.getValueAt(j, 3)).trim();

			String isskxl = "";
			
			if(cznr.contains("确认")||deviceid.equals("")){
				isskxl = "否";
			}else if(cznr.contains("接地开关")){
				if(cznr.contains("中性点")){
					isskxl = "是";
				}else{
					isskxl = "否";
				}
			}else{
				isskxl = "是";
			}
			
			jTable2Model.setValueAt(isskxl ,j, 7);
		}
		
		ShowMessage.view("同步成功！");
	}
	
	private void changeTable2Czpsx() {
		for (int i = 0; i < jTable2.getRowCount(); i++) {
			String xlglid = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 8));//序列的关联id
			
			for(int j = 0; j < jTable1.getRowCount(); j++){
				String zlglid = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 5));
				String sxglid = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 2));

				if(zlglid.equals(xlglid)){
					jTable2.getModel().setValueAt(sxglid, i, 9);
					break;
				}
			}
		}
		
		ArrayList<String[]> djzl = new ArrayList<String[]>();
		
		for(int i = 0; i < jTable2.getRowCount(); i++){
			String xlsx  =  StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 0));
			String czdw =  StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 1));
			String cznr = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 2));
			String devid = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 3));
			String devname  = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 4));
			String sscz = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 5));
			String endstate = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 6));
			String issk = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 7));
			String glid = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 8));
			String czpsx = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 9));

			String[] arr = {xlsx,czdw, cznr,devid,devname,sscz,endstate,issk,glid,czpsx};
			
			djzl.add(arr);
		}
		
		Collections.sort(djzl, new Comparator<String[]>() {
			@Override
			public int compare(String[] p1, String[] p2) {
				if(Integer.valueOf(p1[9]) <= Integer.valueOf(p2[9])){
					return -1;
				}else{
					return 1;
				}
			}
		});
		
		jTable2Model.getDataVector().clear();
		jTable2Model.fireTableDataChanged();
		
		for(int i=0;i<djzl.size();i++){
			String czdw = djzl.get(i)[1];
			String cznr = djzl.get(i)[2];
			String devid = djzl.get(i)[3];
			String devname = djzl.get(i)[4];
			String sscz = djzl.get(i)[5];
			String endstate = djzl.get(i)[6];
			String issk = djzl.get(i)[7];
			String glid = djzl.get(i)[8];
			String czpsx = djzl.get(i)[9];
			
			Object[] rowData = new Object[]{ String.valueOf(i+1),czdw, cznr,devid,devname,sscz,endstate,issk,glid,czpsx};	
			jTable2Model.addRow(rowData);
		}
		
		jTable2.updateUI();
	}
	
	//下移
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		if(jTable1.getSelectedRow() == jTable1.getRowCount()-1){
			return;
		}else if(jTable1.getSelectedRow() == -1){
			ShowMessage.viewWarning(this, "未选择操作票指令！");
			return;
		}
		
		WindowUtils.movedownTableRow(jTable1);
		WindowUtils.paixuTableRow(jTable1,2);
		
		String bfStage = getStageInt(""+jTable1.getValueAt(jTable1.getSelectedRow(), 1));
		String curStage = getStageInt(""+jTable1.getValueAt(jTable1.getSelectedRow()-1, 1));
        
		jTable1.setValueAt(getStage(bfStage), jTable1.getSelectedRow()-1, 1);
		jTable1.setValueAt(getStage(curStage), jTable1.getSelectedRow(), 1);
		
		 this.changeTable2Czpsx();
	}
	//取消
	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
		if(isInversing) {
			ShowMessage.view(this, "请先停止操作票演示再关闭！");
			return;
		}
		//回滚
		DeviceOperate.RollbackDeviceStatus();
		CBSystemConstants.getDtdMap().clear();		
		DeviceOperate.ClearDevMap();
		CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
		CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
    	CBSystemConstants.bztRelationRecord.clear();
		CBSystemConstants.bztStateRecord.clear();
		CBSystemConstants.bztOrganRecord.clear();
		this.setVisible(false);
		this.tempTicket = null;
		SvgUtil.clear();
		bccardid=null;
	}
	//合项
	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {
		int col = 1;
		TableModel model = jTable1.getModel();
		int rowCount = jTable1.getSelectedRowCount();
		//没选择行，直接返回
		if (rowCount == 0)
			return;
		if (jTable1.isEditing()) {
			jTable1.getCellEditor().stopCellEditing();
		}
		
		int selectRow = jTable1.getSelectedRow(); //选中的第一行
		if(model.getValueAt(selectRow, col) == null)
			return;
		int item0 = getRowNum(jTable1, selectRow-1, col); //选中的前一行项号
		
		int xiangcha=0;
		if(jTable1.getRowCount()>selectRow+rowCount){
			xiangcha = getRowNum(jTable1, selectRow+rowCount, col)-item0-2;
		}
		
		for(int i = 0; i < jTable1.getRowCount(); i++) {
			if(model.getValueAt(i, col) != null) {
				if(i>selectRow-1&&i<selectRow+rowCount){
					model.setValueAt(getStage(String.valueOf(item0+1)), i, col);
				}else if(i>=selectRow+rowCount){
				
					model.setValueAt(getStage(String.valueOf(getRowNum(jTable1, i, col)-xiangcha)), i, col);
				}
				
			}
		}
	}
	
	private int getRowNum(JTable table, int row, int col) {
		TableModel model = table.getModel();
		int item0 = 0;
		for(int k = row; k >= 0; k--) {
			if(model.getValueAt(k, col) != null && !model.getValueAt(k, col).toString().equals("")) {
				item0 = Integer.valueOf(getStageInt(model.getValueAt(k, col).toString()));
				break;
			}
		}
		return item0;
	}
	
	//分项
	private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {
		WindowUtils.splitTableRow(jTable1, 1, true);
	}
	//校核
	private void jButton9ActionPerformed(java.awt.event.ActionEvent evt) {
		checkOperation();
	}
	//演示
	private void jButton12ActionPerformed(java.awt.event.ActionEvent evt) {
		if(isInversing) {
			isInversing = false;
			jButton12.setText("演示");
		}
		else {
			isInversing = true;
			jButton12.setText("停止演示");
			inverseOperation();
		}
	}
	
	/**
	 * 
	 * 反向成票 弹出反向成票页面
	 * @param evt
	 * <AUTHOR>
	 * @since 2014-10-16 11:04
	 * 
	 */
	private void jButton13ActionPerformed(java.awt.event.ActionEvent evt) {
		CBSystemConstants.reverse = true;
		RuleBaseMode rbm = CBSystemConstants.getCurRBM();
		PowerDevice pd= CBSystemConstants.getCurRBM().getPd();
		String stateValue = rbm.getBeginStatus();
		String stateCode = "";
		if(CBSystemConstants.reverseCancel==false){
			CBSystemConstants.startCode = stateValue;
			//修改  查询时加入opcode条件，得到唯一的值
			stateCode = getStateCode(pd.getDeviceType(),stateValue,pd.getDeviceStatus());//得到操作码
		}else{
			stateValue = CBSystemConstants.startCode;
			stateCode = getStateCode(pd.getDeviceType(),stateValue,pd.getDeviceStatus());//得到操作码
		}
		Map<String,DeviceMenuModel> devMenusMap = null;
		GetDeviceMenuModel gdmm=new GetDeviceMenuModel();
		devMenusMap=gdmm.execute(pd);//得到右击操作数据
		DeviceMenuModel dmm = new DeviceMenuModel();
		if(devMenusMap.get(stateCode).getStatevalue().equals(stateValue))

			dmm = devMenusMap.get(stateCode);//检修
		if (CBSystemConstants.getSamepdlist().size() == 0) {
			DeviceOperate dre = new DeviceOperate();
			dre.execute(pd, dmm);//右击后执行操作
		}
	}
	
	ItemListener itemListenerSMML = new ItemListener() {
        JCheckBox jCheckBox;
        public void itemStateChanged(ItemEvent e) {
            jCheckBox = (JCheckBox) e.getSource();
            if (jCheckBox.isSelected()) {
                skCheckBox.setSelected(false);
                jkCheckBox.setSelected(false);
                tczlCheckBox.setSelected(false);
             } 
        }
    };
	
    ItemListener itemListenerTCZL = new ItemListener() {
        JCheckBox jCheckBox;
        public void itemStateChanged(ItemEvent e) {
            jCheckBox = (JCheckBox) e.getSource();
            if (jCheckBox.isSelected()) {
                skCheckBox.setSelected(false);
                jkCheckBox.setSelected(false);
                smmlCheckBox.setSelected(false);
             } 
        }
    };
    
    ItemListener itemListenerSK = new ItemListener() {
        JCheckBox jCheckBox;
        public void itemStateChanged(ItemEvent e) {
            jCheckBox = (JCheckBox) e.getSource();
            if (jCheckBox.isSelected()) {
                tczlCheckBox.setSelected(false);
                jkCheckBox.setSelected(false);
                smmlCheckBox.setSelected(false);
             } 
        }
    };
    
	ItemListener itemListenerJK = new ItemListener() {
        JCheckBox jCheckBox;
        public void itemStateChanged(ItemEvent e) {
            jCheckBox = (JCheckBox) e.getSource();
            if (jCheckBox.isSelected()) {
               skCheckBox.setSelected(false);
               tczlCheckBox.setSelected(false);
               smmlCheckBox.setSelected(false);
            } 
        }
    };
	
	/**
	 * 得到数据库中唯一的操作码statecode
	 * <AUTHOR>
	 * @since 2014-09-10 11:15
	 * @param deviceType
	 * @param stateName
	 * @param powerState
	 * @return
	 */
	public static String getStateCode(String deviceType,String stateName,String powerState){
		String sql = "select statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t where t.DEVICETYPEID = '"
				+deviceType+"'"+" and t.STATEVALUE = '"+stateName+"'"+"and t.CARDBUILDTYPE = '"+CBSystemConstants.cardbuildtype+"'"
				+" and t.opcode='"+QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode)+"'";
		List list = DBManager.query(sql);
		Map temp = new HashMap();
		temp = (Map)list.get(0);
		String statecode = temp.get("statecode").toString();
		return statecode;
	}
	
	public void inverseOperation(){
		
		final ArrayList<int[]> errorList = new ArrayList<int[]>();
		errorList.clear();
		DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component cell = super.getTableCellRendererComponent  
                        (table, value, isSelected, hasFocus, row, column);
                boolean isError = false;
                for(int[] c : errorList) {
                	if(c[0] == row && c[1] == column && cell.isBackgroundSet()) { 
                		cell.setForeground(Color.red);
                		isError = true;
                		break;
                	}
                if(!isError)
                	cell.setForeground(Color.black);
                }
                return cell;
            }
        };
		jTable1.getColumnModel().getColumn(4).setCellRenderer(tcr);

	
			
			final Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
			for(int i = allMap.size(); i >= 1; i--) {
				DispatchTransDevice dtd = allMap.get(i);
				dtd.getTransDevice().setDeviceStatus(dtd.getBeginstatus());
				DeviceSVGPanelUtil.changeDeviceSVGColor(dtd.getTransDevice());
			}
			CBSystemConstants.getDtdMap().clear();
			
	    	new Thread(new Runnable() {
				
				public void run() {
					try {
						Thread.sleep(500);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
					String sysBuild = CBSystemConstants.cardbuildtype;
					CBSystemConstants.cardbuildtype = "1";
					for(int i = 0; i < jTable1.getRowCount(); i++) {
						
						if(!isInversing) {
							break;
						}
						jTable1.setRowSelectionInterval(i, i);
	
						String czmx = jTable1.getValueAt(jTable1.getSelectedRow(), 4).toString();
		            	String station = jTable1.getValueAt(jTable1.getSelectedRow(), 3).toString();
		            	List<RuleBaseMode> rbmList=OperationCheckDefault.execute(station, czmx);
		            	if(rbmList.size() == 0)
							break;
						else if(!rbmList.get(0).getCheckout()) {
							errorList.add(new int[]{i,4});
							jTable1.clearSelection();
							break;
						}
		        		boolean result = OperationCheckDefault.inverse(rbmList);
		        		if(!result)
		        			break;
		        		try {
							Thread.sleep(4500);
						} catch (InterruptedException e) {
								e.printStackTrace();
							}
						}
						for(int i = 1; i <= allMap.size(); i++) {
							DispatchTransDevice dtd = allMap.get(i);
							dtd.getTransDevice().setDeviceStatus(dtd.getEndstate());
							DeviceSVGPanelUtil.changeDeviceSVGColor(dtd.getTransDevice());
						}
						CBSystemConstants.cardbuildtype = sysBuild;
						isInversing = false;
						jButton12.setText("演示");
					}
				}).start();

	}
	
	public void checkOperation(){
		new VOSWritCheck(jTable1, jTextArea1);
	}
	
	//替换
	private void jButton10ActionPerformed(java.awt.event.ActionEvent evt) {
		ReplaceWord rw = new ReplaceWord(SystemConstants.getMainFrame(), false, jTable1);
    	rw.setVisible(true);
	}
	
	private RuleBaseMode Srcrbm;
	private List<CardItemModel> cimlist=new ArrayList<CardItemModel>();
	
	public void init(CardModel cm,RuleBaseMode Srcrbm) {
		if (cm == null)
			return;

		this.Srcrbm=Srcrbm;
		for(int i=0;i<cm.getCardItems().size();i++){
			cimlist.add(cm.getCardItems().get(i));
		}
		if(cimlist.size()>0){
			String fName = cimlist.get(0).getStationName();
			for(int i=1;i<cimlist.size();i++){
				if(!cimlist.get(i).getStationName().equals(fName)){
					vflag = false;
					break;
				}
			}
		}

		Object[][] tableDate = null;
		if (jTable1Model == null)
		{
			if(CBSystemConstants.roleCode.equals("0"))
			{
				jTable1Model = new DefaultTableModel(tableDate, new String[] 
				  {"","阶段","序号", "单位", "操作内容","关联ID","是否顺控","厂站名称"});
			}else
			{
				jTable1Model = new DefaultTableModel(tableDate, new String[] 
				{"","","顺序", "操作单位", "操作项目"});
			}
		}

		jTable1.setModel(jTable1Model);
		
		if (jTable2Model == null)
		{
			jTable2Model = new DefaultTableModel(tableDate, new String[] 
			  {"序号", "操作单位", "操作内容","设备ID","设备名称","所属厂站","目标状态","是否顺控","关联ID","操作票顺序"});
		}

		jTable2.setModel(jTable2Model);
		
		//单位下拉框
		JAutoCompleteComboBox comboBox = new JAutoCompleteComboBox();
		comboBox.setFont(new Font("宋体",Font.PLAIN,14));
        DefaultComboBoxModel model = new DefaultComboBoxModel();
        
        CodeNameModel codeNameModel1=new CodeNameModel("0","云南中调");
  		model.addElement(codeNameModel1);
          
  		CodeNameModel codeNameModel2=new CodeNameModel("1","丽江地调");
  		model.addElement(codeNameModel2);
        
        for(Iterator it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();) {
			PowerDevice organ = (PowerDevice)it.next();
			
			if(organ.getPowerVoltGrade() > 10 && !organ.getPowerDeviceName().contains("厂站") && !organ.getPowerDeviceName().contains("T接")){
				String organName = CZPService.getService().getDevName(organ);
				
				CodeNameModel codeNameModel=new CodeNameModel(organ.getPowerDeviceID(),organName);
				model.addElement(codeNameModel);
			}
		}
        
		comboBox.setModel(model);
		DefaultCellEditor tranTypeEditor = new DefaultCellEditor(comboBox);
		tranTypeEditor.setClickCountToStart(2);
		jTable1.getColumnModel().getColumn(3).setCellEditor(tranTypeEditor);
		
		
		JAutoCompleteComboBox comboBox2 = new JAutoCompleteComboBox();
		comboBox2.setFont(new Font("宋体",Font.PLAIN,14));
        DefaultComboBoxModel model2 = new DefaultComboBoxModel();
        
        for(int i=1;i<21;i++) {
        	CodeNameModel codeNameModel=new CodeNameModel(String.valueOf(i),getStage(String.valueOf(i)));
    		model2.addElement(codeNameModel);
        }
	
		comboBox2.setModel(model2);
		DefaultCellEditor tranTypeEditor2 = new DefaultCellEditor(comboBox2);
		tranTypeEditor2.setClickCountToStart(2);
		jTable1.getColumnModel().getColumn(1).setCellEditor(tranTypeEditor2);
		
		sjp.makeFace(jTable1);
		sjp.getTableHeader(jTable1);//列名居中
		
		sjp.makeFace(jTable2);
		sjp.getTableHeader(jTable2);//列名居中
		
		itemModels.addAll(cm.getCardItems());
		
		itemModels.clear();
		List<CardItemModel> DescList = getItemModel();
		for(CardItemModel cim : DescList) {
			if(!cim.getCardDesc().equals(""))
				itemModels.add(cim);
		}
		
		
		for(CardItemModel cim: cm.getCardItems()) {
			itemModels.add(cim);
		}
		
		initTable ();
		
		List<CardItemModel> itemModelsShow = new ArrayList<CardItemModel>();
		
		for(int i=0;i<cm.getCardItems().size();i++){
			itemModelsShow.add(cm.getCardItems().get(i));
		}
		ArrayList<String[]> djzl = GetDtdDataLJ.getData(itemModelsShow,"丽江地调");
		
		String glidbf = "";
		int czpsx= 0;
		int count = 1;
		
		for(int i=0;i<djzl.size();i++){
			String czdw = djzl.get(i)[2];
			String cznr = djzl.get(i)[4];
			
			if(cznr.contains("中性点")&&cznr.contains("落实")){
				cznr = cznr.replace("落实", "确认");
			}
			
			if(!djzl.get(i)[11].equals(glidbf)){
				czpsx ++;
				glidbf = djzl.get(i)[11];
			}
			
			PowerDevice dev = CBSystemConstants.getPowerDevice(djzl.get(i)[6]);
			String devName = "";
			if(dev!=null){
				devName = dev.getPowerDeviceName();
			}
			
			Object[] rowData = new Object[]{count,czdw, cznr, djzl.get(i)[6],devName,djzl.get(i)[3],djzl.get(i)[9],""
					,djzl.get(i)[11],String.valueOf(czpsx)};	
			
			jTable2Model.addRow(rowData);
			
			count++;
		}
	
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);

		//DefaultCellEditor cellEdit = new DefaultCellEditor(new JTextField());
		//cellEdit.setClickCountToStart(2);//双击后使选择的格子可编辑
		jTable1.getColumnModel().getColumn(0).setMinWidth(0);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(1).setMinWidth(50);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(80);
		jTable1.getColumnModel().getColumn(2).setMinWidth(50);
		jTable1.getColumnModel().getColumn(2).setMaxWidth(80);
		jTable1.getColumnModel().getColumn(3).setMinWidth(100);
		jTable1.getColumnModel().getColumn(3).setMaxWidth(250);
		jTable1.getColumnModel().getColumn(5).setMinWidth(0);
		jTable1.getColumnModel().getColumn(5).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(6).setMinWidth(50);
		jTable1.getColumnModel().getColumn(6).setMaxWidth(80);
		jTable1.getColumnModel().getColumn(6).setCellEditor(jTable1.getDefaultEditor(Boolean.class));
	    jTable1.getColumnModel().getColumn(6).setCellRenderer(jTable1.getDefaultRenderer(Boolean.class));
	    jTable1.getColumnModel().getColumn(7).setMinWidth(0);
		jTable1.getColumnModel().getColumn(7).setMaxWidth(0);
		
		jTable2.getColumnModel().getColumn(0).setMinWidth(60);
		jTable2.getColumnModel().getColumn(0).setMaxWidth(60);
		jTable2.getColumnModel().getColumn(1).setMinWidth(120);
		jTable2.getColumnModel().getColumn(1).setMaxWidth(150);
		jTable2.getColumnModel().getColumn(3).setMinWidth(150);
		jTable2.getColumnModel().getColumn(3).setMaxWidth(170);
		jTable2.getColumnModel().getColumn(4).setMinWidth(0);
		jTable2.getColumnModel().getColumn(4).setMaxWidth(0);
		jTable2.getColumnModel().getColumn(5).setMinWidth(100);
		jTable2.getColumnModel().getColumn(5).setMaxWidth(120);
		jTable2.getColumnModel().getColumn(6).setMinWidth(50);
		jTable2.getColumnModel().getColumn(6).setMaxWidth(60);
		jTable2.getColumnModel().getColumn(7).setMinWidth(50);
		jTable2.getColumnModel().getColumn(7).setMaxWidth(60);
		jTable2.getColumnModel().getColumn(8).setMinWidth(0);
		jTable2.getColumnModel().getColumn(8).setMaxWidth(0);
		jTable2.getColumnModel().getColumn(9).setMinWidth(0);
		jTable2.getColumnModel().getColumn(9).setMaxWidth(0);
		jScrollPane2.setViewportView(jTable1);
		//拟票时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date dt = new Date();
		jLabel3.setText(sdf.format(dt));
		//拟票人
		jLabel4.setText(CBSystemConstants.getUser().getUserName());
		//操作任务
		if(CBSystemConstants.roleCode.equals("0")) {
			if (!cm.getCzrw().equals("")&&!jTextArea1.getText().equals(""))
				jTextArea1.setText(jTextArea1.getText()+"；"+cm.getCzrw());
			else if(!cm.getCzrw().equals("")&&jTextArea1.getText().equals(""))
				jTextArea1.setText(cm.getCzrw());
		}else if(CBSystemConstants.roleCode.equals("2")) {
			if (!cm.getCzrw().equals(""))
				jTextArea1.setText(cm.getCzrw());
		}
		
		//备注
		if (!cm.getBzsx().equals("")&&!jTextArea2.getText().equals(""))
			jTextArea2.setText(jTextArea2.getText());
		else if(!cm.getBzsx().equals("")&&jTextArea2.getText().equals(""))
			jTextArea2.setText(cm.getBzsx());
		
		//设置可控
		for(int i = 0;i<jTable1Model.getRowCount();i++){
			String cznr =StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
			String czdw =StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();

			boolean isskxl = false;
			
			if(czdw.equals("丽江地调")){
				if(cznr.contains("检查")||cznr.contains("校验")||cznr.contains("确认")){
					isskxl = false;
				}else{
					isskxl = true;
				}
			}else{
				isskxl = false;
			}
			
			jTable1Model.setValueAt(isskxl ,i, 6);
		}
		
		//设置可控
		for(int j = 0;j<jTable2Model.getRowCount();j++){
			String czdw = StringUtils.ObjToString(jTable2Model.getValueAt(j, 1)).trim();
			String cznr = StringUtils.ObjToString(jTable2Model.getValueAt(j, 2)).trim();

			String isskxl = "";
			
			if(czdw.equals("丽江地调")){
				if(cznr.contains("检查")||cznr.contains("校验")||cznr.contains("确认")){
					isskxl = "否";
				}else{
					isskxl = "是";
				}
			}else{
				isskxl = "否";
			}
			
			jTable2Model.setValueAt(isskxl ,j, 7);
		}
		
		this.setVisible(true);
	}
	
	public void initTable () {
		List<CardItemModel> itemModelsShow = new ArrayList<CardItemModel>();
		
		for(CardItemModel cim : itemModels) {
			itemModelsShow.add(cim);
		}
		//itemModels.addAll(itemModelsAll);
		
		//设置顺序
		String usedNum =null;
		for (int i = 0; i < itemModelsShow.size(); i++) {
			if(itemModelsShow.get(i).getCardItem().equals("")) {
//				if(i == 0)
//					itemModelsShow.get(i).setCardItem("1");
//				else
//					itemModelsShow.get(i).setCardItem(itemModelsShow.get(i-1).getCardItem());
////					itemModelsShow.get(i).setCardItem(String.valueOf(i+1));
			}else{
				if(i>0&&itemModelsShow.get(i).getCardItem().equals(usedNum)){
					itemModelsShow.get(i).setCardItem("");
				}else{
					usedNum = itemModelsShow.get(i).getCardItem();
				}
			}
			itemModelsShow.get(i).setCardNum(String.valueOf(i+1));
		}
		
		//DescList.addAll(cm.getCardItems());
		//List<CardItemModel> DescList = cm.getCardItems();
		while(jTable1Model.getRowCount() != 0) {
			jTable1Model.removeRow(jTable1Model.getRowCount()-1);
		}
		int maxSize = 0;
		if (itemModelsShow != null) {
			for (int i = 0; i < itemModelsShow.size(); i++) {
				CardItemModel cim = itemModelsShow.get(i);
				//CodeNameModel cnm=new CodeNameModel(bcm.getUuIds(),bcm.getCardNum());
				Object[] rowData;
				
				String jieduan = getStage(String.valueOf(i+1));
				
				if(cim.getStationName().contains("金刀营")&& cim.getCardDesc().contains("备自投")){
					continue;
				}
				
				rowData = new Object[]{ cim,jieduan,cim.getCardNum(),cim.getStationName(), cim.getCardDesc() , cim.getUuIds() ,"",cim.getBdzName()};
				if(cim.getCardDesc().length() > maxSize)
					maxSize = cim.getCardDesc().length();
				jTable1Model.addRow(rowData);
			}
		}
		panelLength = 320 + maxSize*14;
		
		if(CBSystemConstants.roleCode.equals("2"))
			WindowUtils.paixuTableRow(jTable1,2);
		
		if(CBSystemConstants.roleCode.equals("0")) {
//			String czpType =getDefaultType();
//			if(czpType.equals("0")){
//				smmlCheckBox.setSelected(false);
				jkCheckBox.setSelected(true);
//			}else{
//				smmlCheckBox.setSelected(true);
//				zhlCheckBox.setSelected(false);
//			}
		}
	}
	
	//小写转大写
	public static String getStage(String str) {
		String stage = "阶段"+RuleExeUtil.int2chineseNum(Integer.valueOf(str));
		return stage;
    }
	
	public void init (String[] zb,List<BaseCardModel> mx) {
		Object[][] tableDate = null;
		if (jTable1Model == null)
			jTable1Model = new DefaultTableModel(tableDate, new String[] {
					"顺序", "操作单位", "操作内容" });
		
		for (int i = 0; i < mx.size(); i++) {
			BaseCardModel bcm = mx.get(i);
			CodeNameModel cnm=new CodeNameModel(bcm.getMxid(),bcm.getCardSub());
			Object[] rowData = { cnm, bcm.getStationName(), bcm.getCardDesc() };
			jTable1Model.addRow(rowData);
		}
		
		if (jTable1 == null)
			jTable1 = new JTable();
		jTable1.setModel(jTable1Model);
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);

		//DefaultCellEditor cellEdit = new DefaultCellEditor(new JTextField());
		//cellEdit.setClickCountToStart(2);//双击后使选择的格子可编辑
		jTable1.getColumnModel().getColumn(0).setMinWidth(0);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(1).setMinWidth(100);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
		jTable1.getColumnModel().getColumn(5).setMinWidth(0);
		jTable1.getColumnModel().getColumn(5).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(5).setPreferredWidth(0);
		jScrollPane2.setViewportView(jTable1);
		//拟票时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date dt = new Date();
		jLabel3.setText(sdf.format(dt));
		//拟票人
		jLabel4.setText(CBSystemConstants.getUser().getUserName());
		//操作任务
		if (jTextArea1.getText().trim().equals(""))
			jTextArea1.setText(zb[2]);
		
		boolean isMultiUnit = false;
		String preStationName = "";
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			String stationName = jTable1.getValueAt(i, 1).toString();
			if(!stationName.equals("")) {
				if(!preStationName.equals("") && !preStationName.equals(stationName)) {
					isMultiUnit = true;
					break;
				}
				preStationName = stationName;
			}
		}
		if(isMultiUnit)
			lblNewLabel.setText("调控中心");
		else{
			if("0".equals(CBSystemConstants.unitCode)){
				lblNewLabel.setText(preStationName);
			}else{
				lblNewLabel.setText("调控中心("+preStationName+")");
			}
		}
		if("1".equals(CBSystemConstants.unitCode)){
			jTable1.getColumnModel().getColumn(1).setMaxWidth(0);
			jTable1.getColumnModel().getColumn(1).setMinWidth(0);
			jTable1.getColumnModel().getColumn(1).setPreferredWidth(0);
			jTable1.getColumnModel().getColumn(1).setResizable(false);
		}
		this.setVisible(true);
	}
	private  List<CardItemModel> saveList(List<CardItemModel> cimlist){
		String itemId;
		for(int i=1,l = cimlist.size();i<l;i++){
			 itemId = cimlist.get(i).getCardItem();
			 if(itemId.equals("")){
				 cimlist.get(i).setCardItem(cimlist.get(i-1).getCardItem());
			 }
		}
		return cimlist;
	}
	private  boolean checkList(List<CardItemModel> cimlist){
		String itemId;
		boolean flag = true;
		for(int i=0,l = cimlist.size();i<l;i++){
			 itemId = cimlist.get(i).getCardItem();
			 if(i==0&&!itemId.equals("1")){
				 flag = false;
				 break;
			 }
			 if(i>0){
				 int d = Integer.parseInt(itemId)-Integer.parseInt(cimlist.get(i-1).getCardItem());
				 if(d>=2||d<0){
					 flag = false;
					 break;
				 }
			 }
		}
		return flag;
	}
	public List<CardItemModel> getCimlist() {
		return cimlist;
	}

	public void setCimlist(List<CardItemModel> cimlist) {
		this.cimlist = cimlist;
	}
	
	private JLabel jLabel1;
	private JLabel jLabel2;
	private JLabel jLabel3;
	private JLabel jLabel4;
	private JPopupTextArea jTextArea1;
	private JCheckBox jCheckBox1;
	private JButton jButton1;
	private JButton jButton2;
	private JButton jButton3;
	private JButton jButton4;
	private JButton jButton5;
	private JButton savejButtonBS;
	private JButton savejButtonCS;
	private JButton jButton6;
	private JButton jButton7;
	private JButton jButton8;
	private JButton jButton9;
	private JButton jButton10;
	private JButton jButton11;
	private JButton jButton12;
	private JButton jButton13;
	private JButton jButton14;
	private JButton jButton15;
	private JButton jButton16;
	private javax.swing.JLabel jLabel5;
	private JLabel jLabel6;
	//private javax.swing.JTextField jTextField2;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JScrollPane jScrollPane3;
	private DefaultTableModel jTable1Model = null;
	private DefaultTableModel jTable2Model = null;
	private JLabel lblNewLabel;
	private JButton btnoms;
	private JPanel panel_5;
	private JLabel lblNewLabel_2;
	private JTextField textField;
	private JButton btnNewButton;
	private JLabel lblNewLabel_3;
	private JTextField textField_1;
	private JButton btnNewButton_1;
	private JPopupTextArea jTextArea2;
	private JButton button;
	private JCheckBox smmlCheckBox;
	private JCheckBox jkCheckBox;
	private JCheckBox tczlCheckBox;
	private JCheckBox skCheckBox;
	
	
	private JComboBox cmb;
	private JComboBox cmb2;

	public boolean importOMS(String zbid, List<CardItemModel> list,List<SequencesModel> list2, String czrw, String bzsx, String isModel, String cardKind, String buildKind, String equipID, String jxpNo, String hasyFlag, String isPW, String systemType){
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNLJWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNLJWEB_PORT"));
		PowerDevice dev = CBSystemConstants.getPowerDevice(equipID);
		
		JSONObject operationTicket = new JSONObject();
		JSONObject operationTicketNr = new JSONObject();
		JSONObject user = new JSONObject();
		JSONArray ticketList = new JSONArray();
		
		ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
		String ticketType =  (String) cmb.getSelectedItem();
		String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
		
		operationTicketNr.put("commandType", "正令");
		operationTicketNr.put("systemType", systemType);
		operationTicketNr.put("voltageLevel", voltageLevel);
		operationTicketNr.put("operationTask", czrw);
		operationTicketNr.put("ticketType", ticketType);
		operationTicketNr.put("type", cardKind);
		operationTicketNr.put("company", "丽江地调");
		operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));

		operationTicket.put("operationTicket", operationTicketNr);
		
		if(skCheckBox.isSelected()){
			for(int i = 0;i<list2.size();i++){
				SequencesModel sqm =  list2.get(i);
				JSONObject jsonObj = new JSONObject();
				
				String number =	String.valueOf(i+1);
				String operationCompany = sqm.getCzcz();
				String stage = "";
				String operationContent = sqm.getCzzl();
				String devId = sqm.getDevId();
				String devName = sqm.getDevName();
				String facName = sqm.getSscz();
				String op = sqm.getTargetStatus();
				String isyk = sqm.getSfkk();

				if(op.equals("断开")||op.equals("拉开")||op.equals("冷备用")){
					op = "0";
				}else if(op.equals("合上")||op.equals("热备用")){
					op = "1";
				}
				
				if(isyk.equals("否")){
					isyk = "0";
				}else if(isyk.equals("是")){
					isyk = "1";
				}
				
				jsonObj.put("number", number);
				jsonObj.put("operationCompany", operationCompany);
				jsonObj.put("stage", stage);
				jsonObj.put("operationContent", operationContent);
				jsonObj.put("devId", devId);
				jsonObj.put("devName", devName);
				jsonObj.put("facName", facName);
				jsonObj.put("op", op);
				jsonObj.put("isyk",isyk);
				
				ticketList.add(jsonObj);
			}
		}else{
			for(CardItemModel cim : list){
				JSONObject jsonObj = new JSONObject();
				
				String number =	cim.getCardNum();
				String operationCompany = cim.getStationName();
				String stage = cim.getCardItem();
				String operationContent = cim.getCardDesc();
				
				jsonObj.put("number", number);
				jsonObj.put("operationCompany", operationCompany);
				jsonObj.put("stage", stage);
				jsonObj.put("operationContent", operationContent);
				
				ticketList.add(jsonObj);
			}
		}
		
		operationTicketNr.put("ticketList", ticketList);
		
		user.put("employeeName", CBSystemConstants.getUser().getUserName());
		user.put("employeeCompany", "50700");
		
		String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
		List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
		
		if(loginnamelist.size()>0){
			user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
		}else{
			user.put("employeeCode", CBSystemConstants.getUser().getUserName());
		}
		
		operationTicketNr.put("user", user);
		System.out.println("传入网络发令参数："+operationTicket);
		
		try {
			
			new Client(host, port, operationTicket.toString()).push();
			
		} catch (IOException e) {
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
			e.printStackTrace();
		}
		
		return true;
	}
}