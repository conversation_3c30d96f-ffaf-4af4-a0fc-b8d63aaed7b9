package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr220T10kVDMMXCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("220T10kV单母母线操作".equals(tempStr)){
			CommonFunctionHH cf = new CommonFunctionHH();
			
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			
			String stationName = CZPService.getService().getDevName(station); 
			String sbName = CZPService.getService().getDevName(curDev); 
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();//临时数组,使用完要clear
			
			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> fhczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> drqkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR, "", false, true, true, true);
			List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Term, SystemConstants.PowerTransformer, true, true, true);
			
			tempList.addAll(mlkgList);
			tempList.addAll(drqkgList);
			tempList.addAll(zybList);
			
		    List<PowerDevice> hotdevList = new ArrayList<PowerDevice>();
		    List<PowerDevice> colddevList = new ArrayList<PowerDevice>();

		    for(PowerDevice dev :tempList){
		    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
		    		hotdevList.add(dev);
		    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
		    		colddevList.add(dev);
		    	}
		    }
		    
		    tempList.clear();
		    
		    if(hotdevList.size()>0){
				replaceStr += "核实"+CZPService.getService().getDevName(hotdevList)+"热备用/r/n";
			}
			
			if(colddevList.size()>0){
				replaceStr += "核实"+CZPService.getService().getDevName(colddevList)+"冷备用/r/n";
			}
		    
		    replaceStr += "红河配调@核实"+stationName+sbName+"上其所管辖的所有10kV出线断路器均己转冷备用/r/n";
		    
		    replaceStr += "退出10kV备自投装置/r/n";
		    
		    if(fhczbkgList.size()>0){
		    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(fhczbkgList.get(0)).equals("0")){
		    		 replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(fhczbkgList)+"/r/n";
		    	}
		    }
		    
		    tempList.addAll(fhczbkgList);
		    tempList.addAll(mlkgList);
		    tempList.addAll(drqkgList);
		    
		    List<PowerDevice> curzbList = new ArrayList<PowerDevice>();
		    
		    if(fhczbkgList.size()>0){
				curzbList = RuleExeUtil.getDeviceList(fhczbkgList.get(0), SystemConstants.PowerTransformer, "", true, true, true);
		    }
		    
		    replaceStr += cf.getCzMotherLineDevStrReplace(tempList, curDev, null, stationName, "由热备用转冷备用");
		    replaceStr += "退出"+CZPService.getService().getDevName(curzbList)+"低后备保护动作跳主变三侧断路器/r/n";
		}
		
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
