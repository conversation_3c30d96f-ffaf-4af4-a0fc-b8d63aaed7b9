package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXZBBZTDDCZRW implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪主变备自投倒电操作任务".equals(tempStr)){
			List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						hignVoltXlkgList.add(dev2);
					}
				}
			}
			replaceStr =CZPService.getService().getDevName(CBSystemConstants.getPowerStation(curDev.getPowerStationID()))+CZPService.getService().getDevName(curDev);
			
			for(PowerDevice dev : hignVoltXlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
					replaceStr += "由"+CZPService.getService().getDevName(lineList.get(0))+"供电用"+(int)curDev.getPowerVoltGrade()+"kV备自投倒由";
				}
			}
			
			for(PowerDevice dev : hignVoltXlkgList){
				if(!RuleExeUtil.isDeviceChanged(dev)){
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,true, true, true);
					replaceStr += CZPService.getService().getDevName(lineList.get(0))+"供电";
				}
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
