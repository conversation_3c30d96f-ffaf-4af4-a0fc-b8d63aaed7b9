package com.tellhow.czp.app.yndd.tool;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import org.apache.log4j.Logger;
import org.dom4j.Element;

import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 检修单解析（通用）
 */
public class JXDResolving {
    private Logger log = Logger.getLogger(this.getClass());
    
}
