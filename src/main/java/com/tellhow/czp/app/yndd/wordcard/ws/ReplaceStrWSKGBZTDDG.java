package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSKGBZTDDG  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山开关备自投调电高".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							yxkgList.add(dev);
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							rbykgList.add(dev);
						}
					}
				}
			}
			
			String loadlineName = "";
			String sourcelineName = "";

			for(PowerDevice dev : rbykgList){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处热备用/r/n";
				
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSwitchML, false, true, true, true);
				loadlineName = CZPService.getService().getDevName(lineList);
			}
			
			for(PowerDevice dev : yxkgList){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSwitchML, false, true, true, true);
				sourcelineName = CZPService.getService().getDevName(lineList);
			}
			
			replaceStr += stationName+"@确认按适应“"+stationName+"由"+sourcelineName+"主供，"+loadlineName+"备供”方式投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
			replaceStr += "文山配调@确认"+stationName+"具备备自投调电操作条件/r/n";
			
			List<PowerDevice> otherxlkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : yxkgList){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSwitchML, false, true, true, true);

				for(PowerDevice line : lineList){
					List<PowerDevice> otherlineList = RuleExeUtil.getLineOtherSideList(line);
					
					for(PowerDevice otherline : otherlineList){
						List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(otherline, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true);
						
						for(PowerDevice xlkg : xlkgList){
							otherxlkgList.add(xlkg);
						}
					}
				}
			}
			
			for(PowerDevice otherxlkg : otherxlkgList){
				PowerDevice otherstation = CBSystemConstants.getPowerStation(otherxlkg.getPowerStationID());
				String otherstationName = CZPService.getService().getDevName(otherstation); 
				
				replaceStr += "文山地调@遥控断开"+otherstationName+CZPService.getService().getDevName(otherxlkg)+"/r/n";
			}
			
			replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV备自投装置正确动作/r/n";
			replaceStr += stationName+"@确认"+deviceName+"处热备用/r/n";
			
			for(PowerDevice otherxlkg : otherxlkgList){
				PowerDevice otherstation = CBSystemConstants.getPowerStation(otherxlkg.getPowerStationID());
				String otherstationName = CZPService.getService().getDevName(otherstation);
				
				replaceStr += "文山地调@遥控合上"+otherstationName+CZPService.getService().getDevName(otherxlkg)+"对线路充电/r/n";
			}
			
			replaceStr += stationName+"@按适应“"+stationName+loadlineName+"主供，"+sourcelineName+"备供”方式调整"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
		}
		
		return replaceStr;
	}

}
