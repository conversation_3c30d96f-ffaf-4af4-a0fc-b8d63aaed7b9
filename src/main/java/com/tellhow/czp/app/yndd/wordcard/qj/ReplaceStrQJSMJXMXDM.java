package com.tellhow.czp.app.yndd.wordcard.qj;


import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrQJSMJXMXDM implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("曲靖双母接线母线倒母".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> plkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
						mxList.add(dev);
					}
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						mlkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						xlkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)){
						plkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
						zbkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
						zbkgList.add(dev);
					}
				}
			}
			
			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : xlkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
				}else{
					replaceStr += "曲靖地调@核实"+stationName+CZPService.getService().getDevName(dev)+"确处合闸位置/r/n";
				}
			}
			
			replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线具备倒母线操作条件/r/n";
			
			
			for(PowerDevice dev : yxkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

				for(PowerDevice dz : dzList){
					if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
						String deviceName = CZPService.getService().getDevName(dz);
						replaceStr += "曲靖地调@遥控合上"+stationName+deviceName+"/r/n";
					}
				}
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
						String deviceName = CZPService.getService().getDevName(dz);
						replaceStr += "曲靖地调@遥控拉开"+stationName+deviceName+"/r/n";
					}
				}
			}
			for(PowerDevice dev : rbykgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				List<PowerDevice> reDzList = RuleExeUtil.sortByMXC(dzList);
				Collections.reverse(reDzList);

				for(PowerDevice dz : reDzList){
					if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
						String deviceName = CZPService.getService().getDevName(dz);
						replaceStr += "曲靖地调@遥控拉开"+stationName+deviceName+"/r/n";
					}
				}

				Collections.reverse(reDzList);
				for(PowerDevice dz : reDzList){
					if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
						String deviceName = CZPService.getService().getDevName(dz);
						replaceStr += "曲靖地调@遥控合上"+stationName+deviceName+"/r/n";
					}
				}

			}
			
			replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线一、二次设备无异常/r/n";
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
}
