package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.lj.LJGCBHDialog;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSPLDGTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山旁路代供停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> plkgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						plkgList.add(dev);
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev); 

				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
					replaceStr += stationName+"@将"+plkgName+"按代供"+deviceName+"方式由冷备用转热备用/r/n";
				}
			}
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev); 

				if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
					replaceStr += "文山地调@遥控合上"+stationName+plkgName+"/r/n";
				}
			}
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev); 

				if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
					replaceStr += "文山地调@遥控断开"+stationName+plkgName+"/r/n";
				}
			}
			
			List<PowerDevice> pldzList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePL, "", false, true, true, true);
			
			for(PowerDevice dev : pldzList){
				String pldzName = CZPService.getService().getDevName(dev); 

				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "文山地调@遥控合上"+stationName+pldzName+"/r/n";
				}
			}
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev); 

				if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
					replaceStr += "文山地调@遥控用"+stationName+plkgName+"同期合环/r/n";
				}
			}
			
			if(RuleExeUtil.isDeviceHadStatus(curDev, "0", "1")){
				replaceStr += "文山地调@遥控断开"+stationName+deviceName+"/r/n";
				replaceStr += stationName+"@退出"+deviceName+"重合闸/r/n";
			}
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev); 

				replaceStr += stationName+"@投入"+plkgName+"重合闸/r/n";
			}
			
			if(RuleExeUtil.isDeviceHadStatus(curDev, "1", "2")){
				replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
