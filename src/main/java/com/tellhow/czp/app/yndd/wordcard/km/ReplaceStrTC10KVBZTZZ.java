package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTC10KVBZTZZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("退出10kV备自投装置".equals(tempStr)){
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbLists = new ArrayList<PowerDevice>();
			List<PowerDevice> mxLists = new ArrayList<PowerDevice>();
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							dycmlkgList.add(dev);
						}
					}
				}
				
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbLists.add(dev);
				}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)&&(dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
					if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
						mxLists.add(dev);
					}
				}
			}
			
			//if(dycmlkgList.size()>1){//多段母线
			if(dycmlkgList.size()>0){
				if(zbLists.size() == 2){
					replaceStr += CZPService.getService().getDevName(station)+"@退出"+(int)dycmlkgList.get(0).getPowerVoltGrade()+"kV备自投装置/r/n";
				}else if(zbLists.size() == 3){
					if(mxLists.size() == 2){
						replaceStr += CZPService.getService().getDevName(station)+"@退出"+(int)dycmlkgList.get(0).getPowerVoltGrade()+"kV备自投装置/r/n";
					}else {
						boolean flag = false;
						
						List<PowerDevice> tagdycmlkgList = new ArrayList<PowerDevice>();
						
						for(PowerDevice dycmlkg : dycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
								List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dycmlkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
								
								for(PowerDevice mx : mxList){
									List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);

									if(zbList.size()==0){
										flag = true;
										break;
									}else{
										if(!tagdycmlkgList.contains(dycmlkg)){
											tagdycmlkgList.add(dycmlkg);
										}
									}
								}
							}
						}
						
						if(flag){
							replaceStr += CZPService.getService().getDevName(station)+"@退出"+(int)dycmlkgList.get(0).getPowerVoltGrade()+"kV备自投装置/r/n";
						}else{
							for(PowerDevice tagdycmlkg : tagdycmlkgList){
								replaceStr += CZPService.getService().getDevName(station)+"@退出"+CZPService.getService().getDevName(tagdycmlkg)+"备自投装置/r/n";
							}
						}
					}
				}
			}
			
			//}else if(dycmlkgList.size()==1){
				//replaceStr += CZPService.getService().getDevName(station)+"@退出"+(int)dycmlkgList.get(0).getPowerVoltGrade()+"kV备自投装置/r/n";
			//}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
