package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQXBZJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆线变组接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			List<PowerDevice> zbzxdjddzList =  new ArrayList<PowerDevice>();

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}

			if(station.getPowerVoltGrade() == 500){
				replaceStr += stationName+"@落实35kV母线具备停电条件/r/n";
				replaceStr += stationName+"@落实"+deviceName+"500kV侧中性点直接接地/r/n";
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				replaceStr += stationName+"@落实"+deviceName+"具备停电条件/r/n";

				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
						replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"间隔具备程序化控制条件/r/n";
						replaceStr += "迪庆地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
						replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbgyckgList,stationName);
						break;
					}
				}
			}else{
//				replaceStr += CommonFunctionDQ.getMotherLineTdContent(dycmxList, stationName);

				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				
				RuleExeUtil.swapDeviceList(zbzxdjddzList);
				
				for(PowerDevice dev : zbzxdjddzList){
					replaceStr += "迪庆地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					replaceStr += "将"+deviceName+"由热备用转冷备用/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
