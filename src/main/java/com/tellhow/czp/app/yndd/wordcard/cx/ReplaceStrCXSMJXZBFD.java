package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXSMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄双母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			List<PowerDevice> otherzbzxdjddzList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zbList =  new ArrayList<PowerDevice>();

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			List<PowerDevice> zbzxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
					
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						for(PowerDevice gd : gdList) {
							otherzbzxdjddzList.add(gd);
						}
					}
				}
			}
			
			List<PowerDevice> gdList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			RuleExeUtil.swapLowDeviceList(gdList);
			
			String zxdNum = "";
			
			for(PowerDevice dev : gdList){
				zxdNum += CZPService.getService().getDevNum(dev)+"、";
			}
			
			if(zxdNum.endsWith("、")){
				zxdNum = zxdNum.substring(0, zxdNum.length()-1);
			}
			
			replaceStr += "楚雄地调@"+stationName+deviceName+"停电设备摘牌。/r/n";
			
			for(PowerDevice dev : gycmlkgList){
				replaceStr += voltStationName+"@投入"+deviceName+"高后备保护联跳"+CZPService.getService().getDevName(dev)+"功能。/r/n";
			}
			
			for(PowerDevice dev : zycmlkgList){
				replaceStr += voltStationName+"@投入"+deviceName+"中后备保护联跳"+CZPService.getService().getDevName(dev)+"功能。/r/n";
			}
			
			for(PowerDevice dev : dycmlkgList){
				replaceStr += voltStationName+"@投入"+deviceName+"低后备保护联跳"+CZPService.getService().getDevName(dev)+"功能。/r/n";
			}
			
			for(PowerDevice dev : zbgyckgList){
				replaceStr += voltStationName+"@检查"+CZPService.getService().getDevName(dev)+"启动失灵保护投入。/r/n";
			}
			
			replaceStr += voltStationName+"@"+"检查"+deviceName+"保护正确投入。/r/n";

			
			if(dycmlkgList.size()==0){
				for(PowerDevice dev : dycmxList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						replaceStr += ""+CZPService.getService().getDevName(dev)+"由冷备用转热备用。/r/n";
					}
				}
			}
			
			String gycmxName = "";
			
			for(PowerDevice dev : zbgyckgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : mxList){
						gycmxName = CZPService.getService().getDevName(mx);
						break;
					}
				}
			}
			
			String kuohaonr = "";
			
			for(PowerDevice dev : zbzyckgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : mxList){
						kuohaonr += "其中"+CZPService.getService().getDevNum(dev)+"断路器转热备用于"+CZPService.getService().getDevName(mx)+"，";
						break;
					}
				}else{
					kuohaonr += CZPService.getService().getDevNum(dev)+"断路器操作至热备用，";
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : mxList){
						kuohaonr += "其中"+CZPService.getService().getDevNum(dev)+"断路器转热备用于"+CZPService.getService().getDevName(mx);
						break;
					}
				}else{
					kuohaonr += CZPService.getService().getDevNum(dev)+"断路器操作至热备用";
				}
			}
			
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2") && RuleExeUtil.getDeviceEndStatus(curDev).equals("0")){
					replaceStr += voltStationName+"@"+"检查"+stationName+deviceName+"间隔已按远方操作前的要求进行设置。/r/n";
					replaceStr += "楚雄地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作。/r/n";
					
					for(PowerDevice dev : zbdyckgList){
						List<PowerDevice> zbdycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice zbdycdz : zbdycdzList){
							if(RuleExeUtil.getDeviceBeginStatus(zbdycdz).equals("1")){
								replaceStr += voltStationName+"@"+"检查"+stationName+CZPService.getService().getDevName(zbdycdz)+"在合上位置。/r/n";
							}
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						List<PowerDevice> zbzycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice zbzycdz : zbzycdzList){
							if(RuleExeUtil.getDeviceBeginStatus(zbzycdz).equals("1")){
								replaceStr += voltStationName+"@"+"检查"+stationName+CZPService.getService().getDevName(zbzycdz)+"在合上位置。/r/n";
							}
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						List<PowerDevice> zbgycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice zbgycdz : zbgycdzList){
							if(RuleExeUtil.getDeviceBeginStatus(zbgycdz).equals("1")){
								replaceStr += voltStationName+"@"+"检查"+stationName+CZPService.getService().getDevName(zbgycdz)+"在合上位置。/r/n";
							}
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
						}
					}

				}
			
			for(PowerDevice dev : zbzxdjddzList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					String devName = CZPService.getService().getDevName(dev); 
					replaceStr += "楚雄地调@遥控拉开"+stationName+devName+"。/r/n";
				}else{
					String devName = CZPService.getService().getDevName(dev); 
					replaceStr += voltStationName+"@检查"+devName+"在合上位置。/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}
			
			for(PowerDevice dev : otherzbzxdjddzList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					String devName = CZPService.getService().getDevName(dev); 
					replaceStr += "楚雄地调@遥控拉开"+stationName+devName+"。/r/n";
				}else{
					String devName = CZPService.getService().getDevName(dev); 
					replaceStr += voltStationName+"@检查"+devName+"在合上位置。/r/n";
				}
			}
			
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}

			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}

			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置。/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置。/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
