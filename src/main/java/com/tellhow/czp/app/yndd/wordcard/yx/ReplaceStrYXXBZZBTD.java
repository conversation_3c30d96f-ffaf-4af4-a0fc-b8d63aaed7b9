package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYXXBZZBTD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪线变组接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			for(PowerDevice dev : dycmxList){
				dycmlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				
				if(dycmlkgList.size() > 0){
					break;
				}
			}
			
			List<PowerDevice> otherzxdjddzList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			for(PowerDevice dev : otherzbList){
				otherzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(otherzxdjddzList);
				break;
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

			for(PowerDevice dev : zbdyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
			
			 
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunction.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			if(otherzxdjddzList.size() > 0){
				replaceStr += CommonFunction.getZxdJddzOnCheckContent(otherzxdjddzList, stationName, station);
			}
			
			boolean isSwitchControl = true;
			
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunction.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(CommonFunction.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				replaceStr += "玉溪地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
				
				for(PowerDevice dev : zbgyckgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					boolean isxcknife = false;
					
					for(PowerDevice dz : dzList){
						if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							isxcknife = true;
							break;
						}
					}
					
					if(isxcknife){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处冷备用/r/n";
						}
					}else{
						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
							}
						}
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					boolean isxcknife = false;
					
					for(PowerDevice dz : dzList){
						if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							isxcknife = true;
							break;
						}
					}
					
					if(isxcknife){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处冷备用/r/n";
						}
					}else{
						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
							}
						}
					}
				}
			}else{
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				
				for(PowerDevice dev : zbgyckgList){
					if(CommonFunction.ifSwitchSeparateControl(dev)){
						replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")&&dz.getPowerVoltGrade() > 110){
								replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
							}
						}
					}
				}
				
				replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				
				if(isSwitchControl && isSwitchSeparateControl){
					
				}else{
					if(zxdjddzList.size() > 0){
						replaceStr += CommonFunction.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
					}
				}
			}
		}
		return replaceStr;
	}

}
