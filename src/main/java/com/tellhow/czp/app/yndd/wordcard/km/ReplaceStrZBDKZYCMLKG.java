package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**  

* <p>Description: </p>  
* <AUTHOR>
* @date 2021年9月13日    
*/
public class ReplaceStrZBDKZYCMLKG implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String result = "";

		if("主变断开中压侧母联开关".equals(tempStr)) {
			int zycvolt = 0;

			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			List<PowerDevice>  zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice>  xlList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "middle");
				}
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(dev.getPowerVoltGrade() == stationDev.getPowerVoltGrade()&&dev.getDeviceStatus().equals("1")){
							xlList.add(dev);
						}
					}
				}
			}
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if(dev.getDeviceType().equals(SystemConstants.Switch)
						&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(zycvolt==dev.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							zycmlkgList.add(dev);
						}
					}
				}
			}
			
			if(zycmlkgList.size()>0){
				if(RuleExeUtil.getDeviceBeginStatus(zycmlkgList.get(0)).equals("1")){
					result += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(zycmlkgList)+"/r/n";
				}
			}
			
			if(zycmlkgList.size()>1){
				for(PowerDevice pd : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(pd).equals("1")&&station.getPowerVoltGrade()!=220){
						result += "投入"+CZPService.getService().getDevName(pd)+"备自投装置/r/n";
					}
					
				}
			}else if(zycmlkgList.size()==1){
				if(RuleExeUtil.getDeviceBeginStatus(zycmlkgList.get(0)).equals("1")&&station.getPowerVoltGrade()!=220){
					result += "投入"+zycvolt+"kV备自投装置/r/n";
				}
			}
		}
		if(result.equals("")){
			return null;
		}
		return result;
	}

}
