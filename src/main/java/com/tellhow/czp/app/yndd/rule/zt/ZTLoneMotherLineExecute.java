package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.view.EquipStatusChooseView;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2014年3月27日 下午8:23:38 
 */
public class ZTLoneMotherLineExecute implements RulebaseInf {
	public static Map<String, String> tagMap = new HashMap<String, String>();

	@Override
	public boolean execute(RuleBaseMode rbm) {
		PowerDevice pd=rbm.getPd();
		
		if(pd == null){
			return false;
		}
		
		String begin = rbm.getBeginStatus();
		String end = rbm.getEndState();
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

		tagMap.clear();
		
		List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

		for(Iterator<PowerDevice> itor = xlkgList.iterator();itor.hasNext();){
			PowerDevice xlkg = itor.next();
			
			if(xlkg.getPowerDeviceName().contains("备用")){
				itor.remove();
			}
		}
		
		if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", false, true, true);

			if(zbList.size()>0){
				List<PowerDevice> fhcswList = new ArrayList<PowerDevice>();
				List<PowerDevice> gycswList = new ArrayList<PowerDevice>();
				List<PowerDevice> zycswList = new ArrayList<PowerDevice>();
				List<PowerDevice> dycswList = new ArrayList<PowerDevice>();
				
				
				if(zbList.size()>1){
					for(PowerDevice zb : zbList){
						gycswList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
						zycswList.addAll(RuleExeUtil.getTransformerSwitchMiddle(zb));
						dycswList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
					}
				}else{
					gycswList = RuleExeUtil.getTransformerSwitchHigh(zbList.get(0));
					zycswList = RuleExeUtil.getTransformerSwitchMiddle(zbList.get(0));
					dycswList = RuleExeUtil.getTransformerSwitchLow(zbList.get(0));
				}

				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(zbList.get(0), SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				for(PowerDevice gd : gdList) {
					RuleExeUtil.deviceStatusExecute(gd, "1", "0");
				}
				
				PowerDevice gycsw = new PowerDevice();
				PowerDevice zycsw = new PowerDevice();
				PowerDevice dycsw = new PowerDevice();

				if(gycswList.size()>0){
					gycsw = gycswList.get(0);
				}
				
				if(zycswList.size()>0){
					zycsw = zycswList.get(0);
				}
				
				if(dycswList.size()>0){
					dycsw = dycswList.get(0);
				}
				
				List<PowerDevice> gycswDzList = new ArrayList<PowerDevice>();
				
				if(!gycsw.getPowerDeviceID().equals("")){
					gycswDzList = RuleExeUtil.getDeviceDirectList(gycsw, SystemConstants.SwitchSeparate);
				}
				
				if(pd.getPowerVoltGrade() == zbList.get(0).getPowerVoltGrade()){//母线是高压侧
					int select = 1;
					
					if(gycswDzList.size()>2){//阿里塘、北营等，主变高压侧开关存在3个刀闸的情况
						String[] arr = {"倒母","不倒母"};
			 			
						select = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择"+CZPService.getService().getDevName(gycsw)+"是否需要倒母", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
			 			
						if(select==0){
							List<PowerDevice> gycmlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
							List<PowerDevice> zycmlkgList =  RuleExeUtil.getDeviceList(zycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
							List<PowerDevice> dycmlkgList =  RuleExeUtil.getDeviceList(dycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);

							if(gycmlkgList.size()>0){
								PowerDevice gycmlkg = gycmlkgList.get(0);
								
								motherLineDFS(gycmlkg,pd,xlkgList,zycmlkgList,dycmlkgList,"倒母");
							}
							
							for(PowerDevice gycswDz : gycswDzList){
								if(gycswDz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
									List<PowerDevice> directList = RuleExeUtil.getDeviceDirectList(gycswDz, SystemConstants.MotherLine);
									
									if(!directList.contains(pd)){
										RuleExeUtil.deviceStatusExecute(gycswDz, gycswDz.getDeviceStatus(), "0");
									}
								}
							}
							
							for(PowerDevice gycswDz : gycswDzList){
								if(gycswDz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
									List<PowerDevice> directList = RuleExeUtil.getDeviceDirectList(gycswDz, SystemConstants.MotherLine);
									
									if(directList.contains(pd)){
										RuleExeUtil.deviceStatusExecute(gycswDz, gycswDz.getDeviceStatus(), "1");
									}
								}
							}
							
							if(gycmlkgList.size()>0){
								for(PowerDevice gycmlkg : gycmlkgList){
									RuleExeUtil.deviceStatusExecute(gycmlkg, gycmlkg.getDeviceStatus(), "1");
								}
							}
							
							if(xlkgList.size()>0){
								for(PowerDevice xlkg : xlkgList){
									RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "1");
								}
							}
							
							if(end.equals("2")){
								if(gycmlkgList.size()>0){
									for(PowerDevice gycmlkg : gycmlkgList){
										RuleExeUtil.deviceStatusExecute(gycmlkg, gycmlkg.getDeviceStatus(), "2");
									}
								}
								
								if(xlkgList.size()>0){
									for(PowerDevice xlkg : xlkgList){
										RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "2");
									}
								}
							}
			 			}else if(select==-1){
			 				return false;
			 			}
					}

					if(select == 1){
						fhcswList.addAll(zycswList);
						fhcswList.addAll(dycswList);

						List<String> defaultStatusList = new ArrayList<String>();
						List<List<String>> expStatusList = new ArrayList<List<String>>();  //设备默认可选择状态集合
						for(PowerDevice sw : fhcswList) {
							defaultStatusList.add(end);
							List<String> expList = new ArrayList<String>();
							expList.add("0");
							expList.add("3");
							expStatusList.add(expList);
						}
						EquipStatusChooseView dialog = new EquipStatusChooseView(SystemConstants.getMainFrame(), true, fhcswList, defaultStatusList,expStatusList, "请选择设备的目标状态");
						Map tagStatusMap=dialog.getTagStatusMap();
						if(tagStatusMap.size() == 0)
							return false;
						
						List<PowerDevice> gycmlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
						
						if(!zycsw.getPowerDeviceID().equals("")){
							zycmlkgList =  RuleExeUtil.getDeviceList(zycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						}
						
						List<PowerDevice> dycmlkgList =  RuleExeUtil.getDeviceList(dycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);

						for(Iterator<PowerDevice> itor = xlkgList.iterator();itor.hasNext();){
							PowerDevice xlkg = itor.next();
							
							if(!xlkg.getDeviceStatus().equals("0")){
								itor.remove();
							}
						}
						
						if(gycmlkgList.size()>0){
							PowerDevice gycmlkg = gycmlkgList.get(0);
							
							boolean result =  motherLineDFS(gycmlkg,pd,xlkgList,zycmlkgList,dycmlkgList,"不倒母");
							
							if(!result){
								return false;
							}
							
							if(zycmlkgList.size()>0){
								for(PowerDevice zycmlkg : zycmlkgList){
									if(zycmlkg.getDeviceStatus().equals("1")){
										RuleExeUtil.deviceStatusExecute(zycmlkg, zycmlkg.getDeviceStatus(), "0");
									}
								}
							}
							
							if(dycmlkgList.size()>0){
								for(PowerDevice dycmlkg : dycmlkgList){
									if(dycmlkg.getDeviceStatus().equals("1")){
										RuleExeUtil.deviceStatusExecute(dycmlkg, dycmlkg.getDeviceStatus(), "0");
									}
								}
							}
							
							if(!dycsw.getPowerDeviceID().equals("")){
								RuleExeUtil.deviceStatusExecute(dycsw, dycsw.getDeviceStatus(), "1");
							}
							
							if(!zycsw.getPowerDeviceID().equals("")){
								RuleExeUtil.deviceStatusExecute(zycsw, zycsw.getDeviceStatus(), "1");
							}
							
							if(!gycsw.getPowerDeviceID().equals("")){
								RuleExeUtil.deviceStatusExecute(gycsw, gycsw.getDeviceStatus(), "1");
							}
							
							if(xlkgList.size()>0){
								for(PowerDevice xlkg : xlkgList){
									RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "1");
								}
							}
							
							RuleExeUtil.deviceStatusExecute(gycmlkg,gycmlkg.getDeviceStatus(), "1");
							
							if(end.equals("2")){
								RuleExeUtil.deviceStatusExecute(gycmlkg,gycmlkg.getDeviceStatus(), "2");
								
								xlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
								
								if(xlkgList.size()>0){
									for(PowerDevice xlkg : xlkgList){
										RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "2");
									}
								}
								
								if(!gycsw.getPowerDeviceID().equals("")){
									RuleExeUtil.deviceStatusExecute(gycsw, gycsw.getDeviceStatus(), "2");
								}
							}
							
							for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
								Map.Entry<PowerDevice, String> entry = it.next();
								
								RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
							}
						}else{//高压侧单母且不是分段母线
							List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
							
							List<PowerDevice> xlList =  RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);

							List<PowerDevice> xldzList =  RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXLS, "", false, true, true, true);

							if(dycmlkgList.size()>0){
								PowerDevice dycmlkg = dycmlkgList.get(0);
								
								if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycmlkg).equals("0")){
									String[] arr = {"运行","热备用"};
									
									int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择"+CZPService.getService().getDevName(dycmlkg)+"状态", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
									
									if(sel==1){
										RuleExeUtil.deviceStatusExecute(dycmlkg, dycmlkg.getDeviceStatus(),"1");
									}else if(sel==-1){
										return false;
									}
								}
							}
							
							RuleExeUtil.deviceStatusExecute(xlList.get(0), xlList.get(0).getDeviceStatus(), "1");
							
							for(PowerDevice fhcsw : fhcswList){
								RuleExeUtil.deviceStatusExecute(fhcsw, fhcsw.getDeviceStatus(),"1");
								
								List<PowerDevice> dycmxList = RuleExeUtil.getDeviceList(fhcsw, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

								for(PowerDevice dycmx : dycmxList){
									List<PowerDevice> drswitchList = RuleExeUtil.getDeviceList(dycmx, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDR,"",false,true, true, true);

									for(PowerDevice drswitch : drswitchList){
										RuleExeUtil.deviceStatusExecute(drswitch, drswitch.getDeviceStatus(), "1");
									}
								}
							}
							
							for(PowerDevice gycsws : gycswList){
								RuleExeUtil.deviceStatusExecute(gycsws, gycsws.getDeviceStatus(),"1");
							}
							
							if(xlkgList.size()>0){
								for(PowerDevice xlkg : xlkgList){
									RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "1");
								}
							}
							
							RuleExeUtil.deviceStatusReset(pd, pd.getDeviceStatus(), "1");
							
							if(end.equals("2")){
								for(PowerDevice switchs : switchList){
									RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "2");
								}
								
								if(xlList.size()>0&&xlkgList.size()==0){
									
									if(pd.getDeviceStatus().equals("0")){
										RuleExeUtil.deviceStatusSet(pd, "0", "1");
									}
									
									for(PowerDevice xldz : xldzList){
										RuleExeUtil.deviceStatusExecute(xldz, xldz.getDeviceStatus(), "1");
									}
								}
							}
							
							for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
								Map.Entry<PowerDevice, String> entry = it.next();
								
								RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
							}
						}
					}
				}else if(pd.getPowerVoltGrade()<zbList.get(0).getPowerVoltGrade()){//母线是低中压侧
					PowerDevice mlkg = new PowerDevice();
					List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
					List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
					List<PowerDevice> qtdzList =  RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeQT, "", true, true, true, true);

					if(com.tellhow.czp.app.yndd.rule.RuleExeUtil.isTransformerXBDY(zbList.get(0))||RuleExeUtil.isTransformerXBZ(zbList.get(0))){//线变组或者是线变单元接线
						List<PowerDevice> zbLists = new ArrayList<PowerDevice>();
						
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
								zbLists.add(dev);
							}
						}
						
						if(zbLists.size()==3){//3主变
							if(switchList.size()>0){
								for(PowerDevice switchs : switchList){
									if(!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&
											!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
									}
								}
								
								for(PowerDevice switchs : switchList){
									if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
									}
								}
								
								for(PowerDevice switchs : switchList){
									if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
									}
								}
								
								if(end.equals("2")){
									for(PowerDevice switchs : switchList){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "2");
									}
								}
							}
						}else if(zbLists.size()==2){//2主变，停边的母线
							List<PowerDevice> zjmlkgList = new ArrayList<PowerDevice>();
							
							for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
								PowerDevice dev = it2.next();
								
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

									if(tempList.size()>0&&dev.getDeviceStatus().equals("1")){
										zjmlkgList.add(dev);
									}
								}
							}
							
							if(zjmlkgList.size()>0){//如果在分位
								String flag  =  "能合环";
								
								if(flag.equals("能合环")){
									tagMap.put("是否合环", "能合环");

									for(PowerDevice zjmlkg : zjmlkgList){
										RuleExeUtil.deviceStatusExecute(zjmlkg, zjmlkg.getDeviceStatus(), "0");
										tagMap.put("合环开关", zjmlkg.getPowerDeviceID());
									}
									
									RuleExeUtil.deviceStatusExecute(dycsw, dycsw.getDeviceStatus(), "1");
									tagMap.put("断开开关", dycsw.getPowerDeviceID());

									if(mlkgList.size()>0){
										RuleExeUtil.deviceStatusExecute(mlkgList.get(0), mlkgList.get(0).getDeviceStatus(), "1");
									}
									
									
									if(end.equals("2")){
										for(PowerDevice switchs : switchList){
											RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "2");
										}
									}
								}else{
									tagMap.put("是否合环", "不能合环");
									
									RuleExeUtil.deviceStatusExecute(dycsw, dycsw.getDeviceStatus(), "1");
									tagMap.put("断开开关", dycsw.getPowerDeviceID());

									for(PowerDevice zjmlkg : zjmlkgList){
										RuleExeUtil.deviceStatusExecute(zjmlkg, zjmlkg.getDeviceStatus(), "0");
										tagMap.put("合环开关", zjmlkg.getPowerDeviceID());
									}
									
									if(mlkgList.size()>0){
										RuleExeUtil.deviceStatusExecute(mlkgList.get(0), mlkgList.get(0).getDeviceStatus(), "1");
									}
									
									if(end.equals("2")){
										for(PowerDevice switchs : switchList){
											RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "2");
										}
									}
								}
							}else{//如果在合位
								if(pd.getPowerVoltGrade() == 10){
									RuleExeUtil.deviceStatusExecute(dycsw, dycsw.getDeviceStatus(), "1");
								}

								if(end.equals("2")){
									for(PowerDevice switchs : switchList){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "2");
									}
								}
							}
						}else{
							if(switchList.size()>0){
								for(PowerDevice switchs : switchList){
									if(!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&
											!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
									}
								}
								
								for(PowerDevice switchs : switchList){
									if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
									}
								}
								
								for(PowerDevice switchs : switchList){
									if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
									}
								}
								
								if(end.equals("2")){
									for(PowerDevice switchs : switchList){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "2");
									}
								}
							}
						}
					}else{
						if(mlkgList.size() == 1){
							mlkg = mlkgList.get(0);
							
							if(mlkg.getDeviceStatus().equals("0")){
								PowerDevice bcfhczbkg = new PowerDevice();
								PowerDevice dcfhczbkg = new PowerDevice();

								List<PowerDevice> mxList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

								List<PowerDevice> bcfhczbkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
								
								if(bcfhczbkgList.size()>0){
									bcfhczbkg = bcfhczbkgList.get(0);
								}
								
								for(PowerDevice mx : mxList){
									List<PowerDevice> dcfhczbkgList =  RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
									
									if(dcfhczbkgList.size()>0){
										dcfhczbkg = dcfhczbkgList.get(0);
									}
								}
								
								if(bcfhczbkg.getDeviceStatus().equals("0")&&(dcfhczbkg.getDeviceStatus().equals("0")||dcfhczbkg.getPowerDeviceID().equals(""))){//如果都是运行状态
									for(PowerDevice switchs : switchList){
										if(!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&
												!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
											if(switchs.getDeviceStatus().equals("0")){
												RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
											}
										}
									}
									
									for(PowerDevice switchs : switchList){
										if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
											if(switchs.getDeviceStatus().equals("0")){
												RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
											}
										}
									}
									
									for(PowerDevice switchs : switchList){
										if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
											if(switchs.getDeviceStatus().equals("0")){
												RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
											}
										}
									}
								}else if(bcfhczbkg.getDeviceStatus().equals("1")&&dcfhczbkg.getDeviceStatus().equals("0")){//当前母线主变低压侧开关热备用
									for(PowerDevice switchs : switchList){
										if(!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&
												!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
											if(switchs.getDeviceStatus().equals("0")){
												RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
											}
										}
									}
									
									for(PowerDevice switchs : switchList){
										if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
											if(switchs.getDeviceStatus().equals("1")){
												RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
											}
										}
									}
									
									for(PowerDevice switchs : switchList){
										if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
											if(switchs.getDeviceStatus().equals("0")){
												RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
											}
										}
									}
								}else if(bcfhczbkg.getDeviceStatus().equals("0")&&dcfhczbkg.getDeviceStatus().equals("1")){//对侧母线主变低压侧开关热备用
									RuleExeUtil.deviceStatusExecute(dcfhczbkg, dcfhczbkg.getDeviceStatus(), "0");
									
									for(PowerDevice switchs : switchList){
										if(!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&
												!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
											if(switchs.getDeviceStatus().equals("0")){
												RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
											}
										}
									}
									
									for(PowerDevice switchs : switchList){
										if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
											if(switchs.getDeviceStatus().equals("0")){
												RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
											}
										}
									}
									
									for(PowerDevice switchs : switchList){
										if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
											if(switchs.getDeviceStatus().equals("0")){
												RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
											}
										}
									}
								}
							}else if(mlkg.getDeviceStatus().equals("1")){
								if(switchList.size()>0){
									for(PowerDevice switchs : switchList){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
									}
								}
							}
						}else if(mlkgList.size() == 2){
							for(PowerDevice switchs : switchList){
								RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
							}
							
							for(PowerDevice qtdz : qtdzList){
								if(qtdz.getPowerDeviceName().contains("站用变")){
									RuleExeUtil.deviceStatusExecute(qtdz, qtdz.getDeviceStatus(), "1");
								}
							}
						}else{//单母不分段接线
							for(PowerDevice switchs : switchList){
								RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
							}
							
							for(PowerDevice qtdz : qtdzList){
								if(qtdz.getPowerDeviceName().contains("站用变")){
									RuleExeUtil.deviceStatusExecute(qtdz, qtdz.getDeviceStatus(), "1");
								}
							}
							
							if(switchList.size()>0){
								for(PowerDevice switchs : switchList){
									RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), rbm.getEndState());
								}
							}
							
							PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
							
							if(station.getPowerVoltGrade() == 35){
								String[] arr = {"运行","热备用","冷备用"};
					 			
					 			int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择主变高压侧断路器状态", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
					 			
								if(sel==0){
									for(PowerDevice gycsws : gycswList){
										RuleExeUtil.deviceStatusExecute(gycsws, gycsws.getDeviceStatus(), "0");
									}
					 			}else if(sel==1){
					 				for(PowerDevice gycsws : gycswList){
										RuleExeUtil.deviceStatusExecute(gycsws, gycsws.getDeviceStatus(), "1");
									}
					 			}else if(sel==2){
					 				for(PowerDevice gycsws : gycswList){
										RuleExeUtil.deviceStatusExecute(gycsws, gycsws.getDeviceStatus(), "2");
									}
					 			}else if(sel==-1){
					 				return false;
					 			}
							}
						}
					}
				}
			}else{//线变组，中间母线
				List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
				List<PowerDevice> qtdzList =  RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeQT, "", true, true, true, true);

				if(switchList.size()>0){
					for(PowerDevice switchs : switchList){
						List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(switchs, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
						
						if(tempList.size()>0){
							for(PowerDevice temp : tempList){
								RuleExeUtil.deviceStatusExecute(temp, temp.getDeviceStatus(),end);
							}
						}
						
						RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(),end);
					}
				}
				
				if(end.equals("2")){
					for(PowerDevice qtdz : qtdzList){
						if(qtdz.getPowerDeviceName().contains("站用变")){
							RuleExeUtil.deviceStatusExecute(qtdz, qtdz.getDeviceStatus(), "1");
						}
					}
				}
			}
		}else{//复电
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
			
			if(zbList.size()>1){
				for(Iterator<PowerDevice> itor = zbList.iterator();itor.hasNext();){
					PowerDevice zb  = itor.next();
					
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(zb).equals("0")){
						itor.remove();
					}
				}
			}
			
			if(zbList.size()>0){
				List<PowerDevice> gycswList = new ArrayList<PowerDevice>();
				List<PowerDevice> zycswList = new ArrayList<PowerDevice>();
				List<PowerDevice> dycswList = new ArrayList<PowerDevice>();
				
				if(zbList.size()>1){
					for(PowerDevice zb : zbList){
						gycswList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
						zycswList.addAll(RuleExeUtil.getTransformerSwitchMiddle(zb));
						dycswList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
					}
				}else{
					gycswList = RuleExeUtil.getTransformerSwitchHigh(zbList.get(0));
					zycswList = RuleExeUtil.getTransformerSwitchMiddle(zbList.get(0));
					dycswList = RuleExeUtil.getTransformerSwitchLow(zbList.get(0));
				}

				PowerDevice gycsw = new PowerDevice();
				PowerDevice zycsw = new PowerDevice();
				PowerDevice dycsw = new PowerDevice();

				if(gycswList.size()>0){
					gycsw = gycswList.get(0);
				}
				
				if(zycswList.size()>0){
					zycsw = zycswList.get(0);
				}
				
				if(dycswList.size()>0){
					dycsw = dycswList.get(0);
				}
				
				List<PowerDevice> gycmlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				List<PowerDevice> zycmlkgList =  RuleExeUtil.getDeviceList(zycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				List<PowerDevice> dycmlkgList =  RuleExeUtil.getDeviceList(dycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);

				if(com.tellhow.czp.app.yndd.rule.RuleExeUtil.isTransformerXBDY(zbList.get(0))||RuleExeUtil.isTransformerXBZ(zbList.get(0))){//线变组或者是线变单元接线
					
					List<PowerDevice> zbLists = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							zbLists.add(dev);
						}
					}
					
					if(zbLists.size()==3){//3主变
						List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

						if(switchList.size()>0){
							for(PowerDevice switchs : switchList){
								RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
							}
							
							RuleExeUtil.deviceStatusExecute(dycsw, dycsw.getDeviceStatus(), "0");
							
							for(PowerDevice switchs : switchList){
								if(!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)
										&&!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)
										&&!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "0");
								}
							}
						}
					}else if(zbLists.size()==2){//边母线
						List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

						List<PowerDevice> mxList = new ArrayList<PowerDevice>();
						
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&dev.getPowerVoltGrade() == 10){
								mxList.add(dev);
							}
						}
						
						if(pd.getPowerVoltGrade() == 10){
							if(mxList.size()<4){
								List<PowerDevice> drdkzby = new ArrayList<PowerDevice>();
								
								for(PowerDevice switchs : switchList){
									if(switchs.getPowerDeviceName().contains("站用变")
											||switchs.getPowerDeviceName().contains("接地变")
											||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchZYB)
											||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)
											||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)
											||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)
											||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										drdkzby.add(switchs);
									}
								}
								

								EquipCheckChoose ec=new EquipCheckChoose(SystemConstants.getMainFrame(), true, drdkzby  , "请选择需要转热备用的断路器");

								List<PowerDevice> chooseEquips4=ec.getChooseEquip();
								if(chooseEquips4.size()==0)
									return false;
								
								for(PowerDevice chooseEquips : chooseEquips4){
									RuleExeUtil.deviceStatusExecute(chooseEquips, chooseEquips.getDeviceStatus(), "1");
								}
								//单母分段
								for(PowerDevice switchs : switchList){
									boolean flag = false;
					    	 		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(switchs,SystemConstants.SwitchSeparate);  

									if(dzList.size()>0){
										for(PowerDevice dz : dzList){
							    	 		List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dz,SystemConstants.SwitchFlowGroundLine);  

							    	 		for(PowerDevice jddz : jddzList){
							    	 			if(jddz.getDeviceStatus().equals("0")){
							    	 				flag = true;
							    	 				break;
							    	 			}
							    	 		}
										}
									}
									
									if(flag){
										continue;
									}
									
									if(switchs.getDeviceStatus().equals("2")&&!drdkzby.contains(switchs)){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
									}
								}
								
								List<PowerDevice> zbkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
								List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
								
								zbkgList.addAll(mlkgList);
								
								EquipCheckChoose ecc2=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbkgList  , "请选择需要对母线充电的断路器");

								List<PowerDevice> chooseEquips2=ecc2.getChooseEquip();
								if(chooseEquips2.size()==0)
									return false;
								
								for(PowerDevice chooseEquip : chooseEquips2){
									RuleExeUtil.deviceStatusExecute(chooseEquip, chooseEquip.getDeviceStatus(), "0");
								}
								
							 	List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSwitchZYB+","+CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, true);
								
							 	for(Iterator<PowerDevice> itor = xlkgList.iterator();itor.hasNext();){
									PowerDevice xlkg  = itor.next();
									
									if(xlkg.getPowerDeviceName().contains("备用")){
										itor.remove();
									}
								}
							 	
								if(pd.getPowerVoltGrade() == 35){
									zybList.addAll(xlkgList);
									
									EquipCheckChoose ecc3=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zybList  , "请选择需要转运行的断路器");

									List<PowerDevice> chooseEquips3=ecc3.getChooseEquip();
									if(chooseEquips3.size()==0)
										return false;
									
									for(PowerDevice chooseEquip : chooseEquips3){
										RuleExeUtil.deviceStatusExecute(chooseEquip, chooseEquip.getDeviceStatus(), "0");
									}
								}else if(pd.getPowerVoltGrade() == 10||pd.getPowerVoltGrade() == 6){
									for(Iterator<PowerDevice> itor = zybList.iterator();itor.hasNext();){
										PowerDevice zybkg  = itor.next();
										
										if(!zybkg.getPowerDeviceName().contains("站用变")&&!zybkg.getPowerDeviceName().contains("接地变")){
											itor.remove();
										}
										
										if(!chooseEquips4.contains(zybkg)){
											itor.remove();
										}
									}
									
									for(PowerDevice zyb : zybList){
										RuleExeUtil.deviceStatusExecute(zyb, zyb.getDeviceStatus(), "0");
									}
								}
							
							}else{
								for(PowerDevice switchs : switchList){
									if(!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)
											&&!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)
											&&!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "0");
									}
								}
								
								RuleExeUtil.deviceStatusExecute(dycsw, dycsw.getDeviceStatus(), "0");
								
								String flag =  "能合环";
								
								List<PowerDevice> zjmlkgList = new ArrayList<PowerDevice>();
								
								for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
									PowerDevice dev = it2.next();
									
									if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										if(dev.getPowerDeviceName().contains("002")){
											zjmlkgList.add(dev);
										}
									}
								}
								
								if(flag.equals("能合环")){
									tagMap.put("是否合环", "能合环");
									
									if(dycmlkgList.size()>0){
										RuleExeUtil.deviceStatusExecute(dycmlkgList.get(0), dycmlkgList.get(0).getDeviceStatus(), "0");
										tagMap.put("合环开关", dycmlkgList.get(0).getPowerDeviceID());
									}
									
									if(zjmlkgList.size()>0){
										RuleExeUtil.deviceStatusExecute(zjmlkgList.get(0), zjmlkgList.get(0).getDeviceStatus(), "1");
										tagMap.put("断开开关", zjmlkgList.get(0).getPowerDeviceID());
									}
								}else{
									tagMap.put("是否合环", "不能合环");

									if(zjmlkgList.size()>0){
										RuleExeUtil.deviceStatusExecute(zjmlkgList.get(0), zjmlkgList.get(0).getDeviceStatus(), "1");
										tagMap.put("断开开关", zjmlkgList.get(0).getPowerDeviceID());
									}
									
									if(dycmlkgList.size()>0){
										RuleExeUtil.deviceStatusExecute(dycmlkgList.get(0), dycmlkgList.get(0).getDeviceStatus(), "0");
										tagMap.put("合环开关", dycmlkgList.get(0).getPowerDeviceID());
									}
								}
							}
						}else{
							//单母分段
							for(PowerDevice switchs : switchList){
								boolean flag = false;
				    	 		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(switchs,SystemConstants.SwitchSeparate);  

								if(dzList.size()>0){
									for(PowerDevice dz : dzList){
						    	 		List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dz,SystemConstants.SwitchFlowGroundLine);  

						    	 		for(PowerDevice jddz : jddzList){
						    	 			if(jddz.getDeviceStatus().equals("0")){
						    	 				flag = true;
						    	 				break;
						    	 			}
						    	 		}
									}
								}
								
								if(flag){
									continue;
								}
								
								if(switchs.getDeviceStatus().equals("2")){
									RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
								}
							}
							
							List<PowerDevice> zbkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
							List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
							
							zbkgList.addAll(mlkgList);
							
							EquipCheckChoose ecc2=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbkgList  , "请选择需要对母线充电的断路器");

							List<PowerDevice> chooseEquips2=ecc2.getChooseEquip();
							if(chooseEquips2.size()==0)
								return false;
							
							for(PowerDevice chooseEquip : chooseEquips2){
								RuleExeUtil.deviceStatusExecute(chooseEquip, chooseEquip.getDeviceStatus(), "0");
							}
							
						 	List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSwitchZYB+","+CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, true);
							
						 	for(Iterator<PowerDevice> itor = xlkgList.iterator();itor.hasNext();){
								PowerDevice xlkg  = itor.next();
								
								if(xlkg.getPowerDeviceName().contains("备用")){
									itor.remove();
								}
							}
						 	
							if(pd.getPowerVoltGrade() == 35){
								zybList.addAll(xlkgList);
								
								EquipCheckChoose ecc3=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zybList  , "请选择需要转运行的断路器");

								List<PowerDevice> chooseEquips3=ecc3.getChooseEquip();
								if(chooseEquips3.size()==0)
									return false;
								
								for(PowerDevice chooseEquip : chooseEquips3){
									RuleExeUtil.deviceStatusExecute(chooseEquip, chooseEquip.getDeviceStatus(), "0");
								}
							}else if(pd.getPowerVoltGrade() == 10||pd.getPowerVoltGrade() == 6){
								for(Iterator<PowerDevice> itor = zybList.iterator();itor.hasNext();){
									PowerDevice zybkg  = itor.next();
									
									if(!zybkg.getPowerDeviceName().contains("站用变")&&!zybkg.getPowerDeviceName().contains("接地变")){
										itor.remove();
									}
								}
								
								for(PowerDevice zyb : zybList){
									RuleExeUtil.deviceStatusExecute(zyb, zyb.getDeviceStatus(), "0");
								}
							}
						}
					}else{//3主变
						List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

						if(switchList.size()>0){
							for(PowerDevice switchs : switchList){
								RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
							}
							
							RuleExeUtil.deviceStatusExecute(dycsw, dycsw.getDeviceStatus(), "0");
							
							for(PowerDevice switchs : switchList){
								if(!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)
										&&!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)
										&&!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "0");
								}
							}
						}
					}
				}else if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){//单母接线
					if(zbList.get(0).getPowerVoltGrade() == pd.getPowerVoltGrade()){//高压侧
						if(gycmlkgList.size()>0){//单母分段
							List<PowerDevice> gycswDzList = RuleExeUtil.getDeviceDirectList(gycsw, SystemConstants.SwitchSeparate);
							
							if(gycswDzList.size()>2){
								for(PowerDevice gycswDz : gycswDzList){
									if(gycswDz.getDeviceStatus().equals("1")){
										RuleExeUtil.deviceStatusExecute(gycswDz, gycswDz.getDeviceStatus(), "0");
									}
								}
								
								for(PowerDevice gycswDz : gycswDzList){
									if(!RuleExeUtil.isDeviceChanged(gycswDz)&&gycswDz.getDeviceStatus().equals("0")&&gycswDz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
										RuleExeUtil.deviceStatusExecute(gycswDz, gycswDz.getDeviceStatus(), "1");
									}
								}
							}
							xlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
							
							List<PowerDevice> tempList = new ArrayList<PowerDevice>();
							List<PowerDevice> chooseEquips2=  new ArrayList<PowerDevice>();
							tempList.addAll(xlkgList);
							tempList.addAll(gycmlkgList);	
							
							if(begin.equals("2")){
								if(tempList.size()>1){
									EquipCheckChoose ecc2=new EquipCheckChoose(SystemConstants.getMainFrame(), true, tempList , "请选择转热备用的断路器");

									chooseEquips2=ecc2.getChooseEquip();
									
									if(ecc2.isCancel()){
										return false;
									}
									
									for(PowerDevice choose : chooseEquips2){
										RuleExeUtil.deviceStatusExecute(choose, choose.getDeviceStatus(), "1");
									}
								}
							}else{
								chooseEquips2 = tempList;
							}
							
							
							for(PowerDevice gycmlkg : gycmlkgList){
								RuleExeUtil.deviceStatusExecute(gycmlkg, gycmlkg.getDeviceStatus(), "0");
							}
							
							for(PowerDevice gycsws : gycswList){
								RuleExeUtil.deviceStatusExecute(gycsws, gycsws.getDeviceStatus(), "1");
							}
							
							for(PowerDevice zycsws : zycswList){
								RuleExeUtil.deviceStatusExecute(zycsws, zycsws.getDeviceStatus(), "1");
							}
							
							for(PowerDevice dycsws : dycswList){
								RuleExeUtil.deviceStatusExecute(dycsws, dycsws.getDeviceStatus(), "1");
							}
							
							for(PowerDevice gycsws : gycswList){
								RuleExeUtil.deviceStatusExecute(gycsws, gycsws.getDeviceStatus(), "0");
							}
							
							for(PowerDevice zycsws : zycswList){
								RuleExeUtil.deviceStatusExecute(zycsws, zycsws.getDeviceStatus(), "0");
							}
							
							for(PowerDevice dycsws : dycswList){
								RuleExeUtil.deviceStatusExecute(dycsws, dycsws.getDeviceStatus(), "0");
							}
							
							for(PowerDevice dycmlkg:dycmlkgList){
								RuleExeUtil.deviceStatusExecute(dycmlkg, dycmlkg.getDeviceStatus(), "1");
							}
							
							for(PowerDevice zycmlkg:zycmlkgList){
								RuleExeUtil.deviceStatusExecute(zycmlkg, zycmlkg.getDeviceStatus(), "1");
							}
							
							if(chooseEquips2.size()>1){
								EquipCheckChoose ecc2=new EquipCheckChoose(SystemConstants.getMainFrame(), true, chooseEquips2 , "请选择最终状态为运行的断路器");

								List<PowerDevice> chooseEquips3=ecc2.getChooseEquip();
								
								if(ecc2.isCancel()){
									return false;
								}
								
								for(PowerDevice choose : chooseEquips2){
									if(chooseEquips3.contains(choose)){
										RuleExeUtil.deviceStatusExecute(choose, choose.getDeviceStatus(), "0");
									}else{
										RuleExeUtil.deviceStatusExecute(choose, choose.getDeviceStatus(), "1");
									}
								}
							}else{
								for(PowerDevice choose : chooseEquips2){
									RuleExeUtil.deviceStatusExecute(choose, choose.getDeviceStatus(), "0");
								}
							}
						}else{//单母不分段
							if(dycswList.size()>0){
								for(PowerDevice dycsws : dycswList){
									if(dycsws.getDeviceStatus().equals("2")){
										RuleExeUtil.deviceStatusExecute(dycsws, dycsws.getDeviceStatus(), "1");
									}
								}
							}
							
							List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

							
							for(PowerDevice switchs : switchList){
								if(switchs.getDeviceStatus().equals("2")){
									RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(), "1");
								}
							}
							
							if(xlkgList.size()>0){
								EquipCheckChoose ecc2=new EquipCheckChoose(SystemConstants.getMainFrame(), true, xlkgList , "请选择需要送电的线路断路器");

								List<PowerDevice> chooseEquips2=ecc2.getChooseEquip();
								if(chooseEquips2.size()==0)
									return false;
								
								if(chooseEquips2.size()>1){
									EquipCheckChoose ecc3=new EquipCheckChoose(SystemConstants.getMainFrame(), true, chooseEquips2 , "请选择"+CZPService.getService().getDevName(pd)+"充电线路断路器");

									List<PowerDevice> chooseEquips3=ecc3.getChooseEquip();
									if(chooseEquips3.size()==0)
										return false;
									
									for(PowerDevice chooseEquip : chooseEquips3){
										RuleExeUtil.deviceStatusExecute(chooseEquip,chooseEquip.getDeviceStatus(), "0");
									}
								}
								
								for(PowerDevice chooseEquip : chooseEquips2){
									RuleExeUtil.deviceStatusExecute(chooseEquip,chooseEquip.getDeviceStatus(), "0");
								}
							}
							
							List<PowerDevice> xldzList =  RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXLS, "", false, true, true, true);
							
							if(xldzList.size()>0){
								for(PowerDevice xldz : xldzList){
									RuleExeUtil.deviceStatusExecute(xldz, xldz.getDeviceStatus(), "0");
								}
							}
							
							for(PowerDevice gycsws : gycswList){
								RuleExeUtil.deviceStatusExecute(gycsws, gycsws.getDeviceStatus(), "0");
							}
							
							for(PowerDevice dycsws : dycswList){
								RuleExeUtil.deviceStatusExecute(dycsws, dycsws.getDeviceStatus(), "0");
							}
							
							String[] arr = {"运行","热备用"};
							
							if(dycmlkgList.size()>0){
								int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择"+CZPService.getService().getDevName(dycmlkgList.get(0))+"状态", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
								
								if(sel==0){
									RuleExeUtil.deviceStatusExecute(dycmlkgList.get(0), dycmlkgList.get(0).getDeviceStatus(),"0");
								}if(sel==1){
									RuleExeUtil.deviceStatusExecute(dycmlkgList.get(0), dycmlkgList.get(0).getDeviceStatus(),"1");
								}else if(sel==-1){
									return false;
								}
							}
						}
					}else{//低压侧
						List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

						List<PowerDevice> drdkzby = new ArrayList<PowerDevice>();
						
						for(PowerDevice switchs : switchList){
							if(switchs.getPowerDeviceName().contains("站用变")
									||switchs.getPowerDeviceName().contains("接地变")
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchZYB)
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
								drdkzby.add(switchs);
							}
						}
						
						for(PowerDevice switchs : switchList){
							boolean flag = false;
			    	 		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(switchs,SystemConstants.SwitchSeparate);  

							if(dzList.size()>0){
								for(PowerDevice dz : dzList){
					    	 		List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dz,SystemConstants.SwitchFlowGroundLine);  

					    	 		for(PowerDevice jddz : jddzList){
					    	 			if(jddz.getDeviceStatus().equals("0")){
					    	 				flag = true;
					    	 				break;
					    	 			}
					    	 		}
								}
							}
							
							if(flag){
								continue;
							}
						}
						
						List<PowerDevice> zbkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
						List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
						
						drdkzby.addAll(mlkgList);
						drdkzby.addAll(zbkgList);

						if(pd.getPowerVoltGrade() == 35){
							drdkzby.addAll(xlkgList);
						}
						
						EquipCheckChoose ec=new EquipCheckChoose(SystemConstants.getMainFrame(), true, drdkzby  , "请选择需要转热备用的断路器");

						List<PowerDevice> chooseEquips4=ec.getChooseEquip();
						if(chooseEquips4.size()==0)
							return false;
						
						for(PowerDevice chooseEquips : chooseEquips4){
							RuleExeUtil.deviceStatusExecute(chooseEquips, chooseEquips.getDeviceStatus(), "1");
						}
						
						if(end.equals("0")){
							if(pd.getPowerVoltGrade() == 35){
								List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
								
								for(PowerDevice chooseEquips : chooseEquips4){
									if(!zbkgList.contains(chooseEquips)&&!mlkgList.contains(chooseEquips)){
										rbykgList.add(chooseEquips);
									}
								}
								
								if(rbykgList.size()>0){
									EquipCheckChoose ecc3=new EquipCheckChoose(SystemConstants.getMainFrame(), true, rbykgList  , "请选择需要转运行的断路器");

									List<PowerDevice> chooseEquips3=ecc3.getChooseEquip();
									if(chooseEquips3.size()==0)
										return false;
									
									for(PowerDevice chooseEquip : chooseEquips3){
										RuleExeUtil.deviceStatusExecute(chooseEquip, chooseEquip.getDeviceStatus(), "0");
									}
								}
							}else if(pd.getPowerVoltGrade() == 10||pd.getPowerVoltGrade() == 6){
							 	List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSwitchZYB+","+CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, true);
								
								for(Iterator<PowerDevice> itor = zybList.iterator();itor.hasNext();){
									PowerDevice zybkg  = itor.next();
									
									if(!zybkg.getPowerDeviceName().contains("站用变")&&!zybkg.getPowerDeviceName().contains("接地变")){
										itor.remove();
										continue;
									}
									
									if(!chooseEquips4.contains(zybkg)){
										itor.remove();
									}
								}
								
								for(PowerDevice zyb : zybList){
									RuleExeUtil.deviceStatusExecute(zyb, zyb.getDeviceStatus(), "0");
								}
							}

							zbkgList.addAll(mlkgList);
							
							for(Iterator<PowerDevice> itor = zbkgList.iterator();itor.hasNext();){
								PowerDevice zbkg = itor.next();
								
								if(zbkg.getDeviceStatus().equals("2")){
									itor.remove();
								}
							}
							
							if(zbkgList.size()>1){
								EquipCheckChoose ecc2=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbkgList  , "请选择需要对母线充电的断路器");

								List<PowerDevice> chooseEquips2=ecc2.getChooseEquip();
								if(chooseEquips2.size()==0)
									return false;
								
								for(PowerDevice chooseEquip : chooseEquips2){
									RuleExeUtil.deviceStatusExecute(chooseEquip, chooseEquip.getDeviceStatus(), "0");
								}
							}else{
								for(PowerDevice chooseEquip : zbkgList){
									RuleExeUtil.deviceStatusExecute(chooseEquip, chooseEquip.getDeviceStatus(), "0");
								}
							}
						}
					}
				}else{
					RuleExeUtil.deviceStatusExecute(gycsw, gycsw.getDeviceStatus(), "1");
					RuleExeUtil.deviceStatusExecute(zycsw, zycsw.getDeviceStatus(), "1");
					RuleExeUtil.deviceStatusExecute(dycsw, dycsw.getDeviceStatus(), "1");


					if(xlkgList.size()>0){
						for(PowerDevice xlkg : xlkgList){
							RuleExeUtil.deviceStatusExecute(xlkg, "2", "1");
						}
					}
				}
			}else{
				//线变组，中间母线
				List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

				if(switchList.size()>0){
					for(Iterator<PowerDevice> itor = switchList.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(dev.getPowerDeviceName().contains("备用")){
							itor.remove();
						}
					}
					
					for(PowerDevice switchs : switchList){
						if(!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)
								&&!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)
								&&!switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							RuleExeUtil.deviceStatusExecute(switchs, switchs.getDeviceStatus(),end);
						}
					}
				}
				
				List<PowerDevice> zjmlkgList = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

						if(tempList.size()>0&&!dev.getDeviceStatus().equals("0")){
							zjmlkgList.add(dev);
						}
					}
				}
				
				List<PowerDevice> dycmlkgList =  RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				
				for (Iterator<PowerDevice> it2 = dycmlkgList.iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getPowerDeviceName().contains("002")||dev.getPowerDeviceName().contains("004")){
						it2.remove();
					}
				}
				
				zjmlkgList.addAll(dycmlkgList);
				
				if(zjmlkgList.size()>1){
					for(PowerDevice dev : zjmlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					EquipCheckChoose ecc2=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zjmlkgList , "请选择需要转运行的断路器");

					List<PowerDevice> chooseEquips2=ecc2.getChooseEquip();
					if(chooseEquips2.size()==0)
						return false;
					
					for(PowerDevice chooseEquips : chooseEquips2){
						RuleExeUtil.deviceStatusExecute(chooseEquips, chooseEquips.getDeviceStatus(), "0");
					}
				}else if(zjmlkgList.size()==1){
					RuleExeUtil.deviceStatusExecute(zjmlkgList.get(0), zjmlkgList.get(0).getDeviceStatus(), "0");
				}
			}
		}
		
		return true;
	}

	public boolean  motherLineDFS(PowerDevice gycmlkg,PowerDevice pd,List<PowerDevice> xlkgList,List<PowerDevice> zycmlkgList,List<PowerDevice> dycmlkgList,String isdm){
		if(gycmlkg.getDeviceStatus().equals("0")){
			if(isdm.equals("不倒母")){
				if(zycmlkgList.size()>0){
					for(PowerDevice zycmlkg : zycmlkgList){
						if(zycmlkg.getDeviceStatus().equals("1")){
							RuleExeUtil.deviceStatusExecute(zycmlkg, zycmlkg.getDeviceStatus(), "0");
						}
					}
				}
				
				if(dycmlkgList.size()>0){
					for(PowerDevice dycmlkg : dycmlkgList){
						if(dycmlkg.getDeviceStatus().equals("1")){
							RuleExeUtil.deviceStatusExecute(dycmlkg, dycmlkg.getDeviceStatus(), "0");
						}
					}
				}
			}
		}else if(gycmlkg.getDeviceStatus().equals("1")){
			RuleExeUtil.deviceStatusExecute(gycmlkg, gycmlkg.getDeviceStatus(), "0");
		}
		return true;
	}
	
}
