package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.LineTransChooseDialog;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.ShowMessage;

public class XSBNJudgeLinePowerSide implements RulebaseInf {
	
	public static PowerDevice sourceLineTrans=null;  //线路合环侧
	public static List<PowerDevice> loadLineTrans=new ArrayList<PowerDevice>(); //线路解环侧

	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		sourceLineTrans=null;
		loadLineTrans.clear();
		Map<PowerDevice,String> stationlines = QueryDeviceDao.getPowersLineByLine(pd);
		
		if(pd.getPowerDeviceName().contains("遮西T线")){
			for (Iterator<PowerDevice> iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
				PowerDevice dev = iterator.next();
				
				if("35kV六西线".equals(dev.getPowerDeviceName())){
					iterator.remove();
				}
			}
		}else if(pd.getPowerDeviceName().equals("35kV六西线")){
			for (Iterator<PowerDevice> iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
				PowerDevice dev = iterator.next();
				
				if(dev.getPowerDeviceName().contains("遮西T线")){
					iterator.remove();
				}
			}
		}
		
		if(stationlines.size() == 0) { //该线路是站内线路
			sourceLineTrans=pd;
			setSourceLoad(pd, sourceLineTrans, loadLineTrans);
			return true;
		}
		if(CBSystemConstants.cardbuildtype.equals("1")) {
			sourceLineTrans=pd;
			setSourceLoad(pd, sourceLineTrans, loadLineTrans);
			return true;
		}
		
		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
		
		if(pd.getPowerVoltGrade() > 35 && stationlines.size() < 2) { //线路接线方式不正确的提示客户，一条线路只连接一个变电站
			if(!station.getDeviceType().equals(SystemConstants.PowerFactory)){
				ShowMessage.view("线路没有找到对侧变电站！请注意票面正确性。");
			}
		}
		
		//打开线路关联的变电站接线图
		String filePath = "";
		if(CBSystemConstants.isCurrentSys) {
			filePath = SystemConstants.getGuiBuilder().getActivateSVGPanel().getFilePath();
		}
		PowerDevice dev=null; //变电站对象
		List<PowerDevice> trans=new ArrayList<PowerDevice>();
		
		for(PowerDevice pdNew:stationlines.keySet()){
			dev=pdNew;
			
			trans.add(dev);
			
			if(CBSystemConstants.isCurrentSys) {
				DeviceSVGPanelUtil.openSVGPanel(dev.getPowerStationID(), dev.getPowerDeviceID());
			}
		}
//		for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
//			dev=(PowerDevice)iterator.next();
//			trans.add(dev);
//			DeviceSVGPanelUtil.openSVGPanel(dev.getPowerStationID(), dev.getPowerDeviceID());
//		}
	
		if(CBSystemConstants.isCurrentSys && !"".equals(pd.getPowerStationName())) //如果是在站内操作线路，打开对侧变电站后还要回到当前变电站，如果是全网图线路则不管
		     SystemConstants.getGuiBuilder().activateTabbedPageByName(filePath);
		
		//选择线路两端变电站电源侧负荷侧
		PowerDevice checkDev=null;
		
		PowerDevice sourceDev=null;
//		for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
		for(PowerDevice pdNew:stationlines.keySet()){
			dev=pdNew;
//			dev=(PowerDevice)iterator.next();
			String flow = String.valueOf(RuleExeUtil.getLineFlow(dev));
			if(flow.equals("2")){
				sourceDev = dev;
				break;
			}
		}
		
		if(sourceDev != null) {
			for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
				dev=(PowerDevice)iterator.next();
				if(dev.equals(sourceDev))
					stationlines.put(dev, "0");
				else
					stationlines.put(dev, "1");
			}
		}
		
		if(checkDev==null){
			if(stationlines.size() == 1) {
				for (Iterator iterator = stationlines.keySet().iterator(); iterator
						.hasNext();) {
					checkDev = (PowerDevice) iterator.next();
				}
			}
			else if(!CBSystemConstants.isCurrentSys) {
				int volt = 0;
				
				for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
					PowerDevice line = (PowerDevice) iterator.next();
					PowerDevice stationdev = CBSystemConstants.getPowerStation(line.getPowerStationID());
					
					if(volt == 0){
						volt = (int)stationdev.getPowerVoltGrade();
						checkDev = line;
					}else if((int)stationdev.getPowerVoltGrade()>volt){
						checkDev = line;
					}
				}
				
				if(!CBSystemConstants.oneClickString.equals("")){
					for (Iterator iterator = stationlines.keySet().iterator(); iterator
							.hasNext();) {
						PowerDevice devPD = (PowerDevice) iterator.next();
						if(CBSystemConstants.oneClickString.equals(CZPService.getService().getDevName(CBSystemConstants.getPowerStation(checkDev.getPowerStationID())))){
							checkDev = devPD;
							break;
						}
					}
				}
						
//				if(!CBSystemConstants.oneClickString.equals("") && 
//						CBSystemConstants.oneClickString.equals(CZPService.getService().getDevName(CBSystemConstants.getPowerStation(checkDev.getPowerStationID())))){
//				}
			}
			else{
				for (Iterator iterator = stationlines.keySet().iterator(); iterator
						.hasNext();) {
					dev = (PowerDevice) iterator.next();
					
					if("110kVPASTJI".equals(dev.getPowerStationName())){
						iterator.remove();
					}
				}
				
				LineTransChooseDialog linetransChoose=new LineTransChooseDialog(SystemConstants.getMainFrame(), true, stationlines, "选择【"+CZPService.getService().getDevName(pd)+"】的电源侧");
				checkDev=linetransChoose.getReslut();
			}
		}
		if(checkDev==null)
			return false;
		
		sourceLineTrans=(PowerDevice)CBSystemConstants.getPowerDevice(checkDev.getPowerStationID(), checkDev.getPowerDeviceID());
		trans.remove(checkDev);
		CBSystemConstants.putCurOperateDev(sourceLineTrans);
		for (int i = 0; i < trans.size(); i++) {
			dev=(PowerDevice)CBSystemConstants.getPowerDevice(trans.get(i).getPowerStationID(), trans.get(i).getPowerDeviceID());
			CBSystemConstants.putCurOperateDev(dev);
			loadLineTrans.add(dev);
		}
		
//		for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
		for(PowerDevice pdNew:stationlines.keySet()){
			setSourceLoad(pdNew, sourceLineTrans, loadLineTrans);
		}

		return true;
	}
	
	private void setSourceLoad(PowerDevice pd, PowerDevice source, List<PowerDevice> load) {
		CBSystemConstants.putLineSource(pd.getPowerDeviceID(), source);
		CBSystemConstants.putLineLoad(pd.getPowerDeviceID(), load);
	}

}
