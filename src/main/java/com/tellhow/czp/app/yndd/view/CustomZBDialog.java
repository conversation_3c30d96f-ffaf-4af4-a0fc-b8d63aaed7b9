package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Frame;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.ScrollPaneConstants;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.app.yndd.dao.CustomCodexDao;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.view.CustomCodex;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;
/**
 * 自定义规则和术语
 * <AUTHOR>
 * 2016年5月20日
 *
 */
public class CustomZBDialog extends JDialog {

	JTable table;
	JButton addButton, delButton;

	public CustomZBDialog(Frame parent, boolean isModel) {
		super(parent, isModel);
		setTitle("自定义规则管理");
		setSize(700, 350);
		WindowUtils.centerWindow(parent, this);
		initComponents();
		addAction();
	}

	/**
	 * 初始化组件
	 */
	private void initComponents() {
		JPanel panel = new JPanel();
		addButton = new JButton("新增");
		addButton.setToolTipText("\u65b0\u589e");
		addButton.setFocusPainted(false);
		delButton = new JButton("删除");
		delButton.setFocusPainted(false);
		delButton.setToolTipText("\u5220\u9664");
		addButton.setFont(new Font("宋体", 0, 13));
		delButton.setFont(new Font("宋体", 0, 13));
		String[] tableHeads = new String[] { "序号", "厂站", "设备", "状态", "操作", "备注" };
		@SuppressWarnings("serial")
		DefaultTableModel jtableModel1 = new DefaultTableModel( null,tableHeads){
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				if (columnIndex ==0||columnIndex ==1 || columnIndex ==2||columnIndex == 3 || columnIndex == 4||columnIndex==5) {
					return false;
				} else {
					return true;
				}
			}		
		};
		CustomCodexDao ccdd = new CustomCodexDao();
		List<String[]> oneList=ccdd.getSpecialRule();
		if (oneList.size() > 0) {
			for (int l = 0; l < oneList.size(); l++) {
				
				String[] rowdata=oneList.get(l);
				jtableModel1.addRow(rowdata);
			}
			
		}	
		table = new JTable();
		table.setModel(jtableModel1);
		table.getColumnModel().getColumn(0).setMaxWidth(40);
		table.getColumnModel().getColumn(1).setMaxWidth(70);
		table.getColumnModel().getColumn(3).setMaxWidth(70);
		table.getColumnModel().getColumn(4).setMaxWidth(70);
		// 设置表中的列内容字体居中
		DefaultTableCellRenderer render = new DefaultTableCellRenderer();
		render.setHorizontalAlignment(SwingConstants.CENTER);
		table.getColumn("序号").setCellRenderer(render);
		table.addMouseListener(new java.awt.event.MouseAdapter() {
			public void mouseClicked(java.awt.event.MouseEvent evt) {
				tableMouseClicked(evt);
			}
		});
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(table);// 列名居中
		JScrollPane scrollPane = new JScrollPane();
		// 设置JScrollPane总是出现滚动条
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		// //设每一列的宽度
		// for(int i=1;i<table.getColumnCount();i++){
		// table.getColumnModel().getColumn(i).setPreferredWidth(50);
		// }
		table.setRowHeight(20);// 设定每一列的高度
		scrollPane.setViewportView(table);
		panel.setLayout(new FlowLayout(FlowLayout.RIGHT));
		panel.add(addButton);
		panel.add(delButton);
		this.add(panel, BorderLayout.NORTH);
		this.add(scrollPane, BorderLayout.CENTER);
		this.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);

	}

	private void addAction() {
		// 新增
		addButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				addButtonClick(e);
			}
		});
		// 删除
		delButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				delAction(evt);
			}
		});
	}

	/**
	 * 双击table进入规则添加界面
	 * 
	 * @param e
	 * <AUTHOR>
	 */
	private void tableMouseClicked(MouseEvent e) {
		if (e.getClickCount() == 2) {
			int[] selectRows = table.getSelectedRows();
			if (selectRows.length == 0) {
				ShowMessage.view(this, "请选择规则");
				return;
			}
			this.setVisible(false);
			JSplitPane splitPane = (JSplitPane) SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(0.57);
			//CustomCodex cc=CustomCodex.getInstance();
			CustomCodexGZ cc =new  CustomCodexGZ();
			splitPane.setRightComponent(cc);
			CBSystemConstants.svgAddPd = cc;
		}

	}

	/**
	 * 新增
	 * 
	 * @param e
	 * <AUTHOR>
	 */
	private void addButtonClick(ActionEvent e) {
		this.setVisible(false);
		JSplitPane splitPane = (JSplitPane) SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.57);
		CustomCodexGZ cc =new  CustomCodexGZ();
		splitPane.setRightComponent(cc);
		CBSystemConstants.svgAddPd = cc;

	}

	/**
	 * 删除
	 * 
	 * @param evt
	 * <AUTHOR>
	 */
	protected void delAction(ActionEvent evt) {
		// 从表删除按钮功能
		WindowUtils.removeTableRow(table);
	}

}
