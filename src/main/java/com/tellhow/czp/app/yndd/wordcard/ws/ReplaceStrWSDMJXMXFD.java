package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionWS;
import com.tellhow.graphicframework.constants.SystemConstants;

import com.tellhow.uitl.StringUtils;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSDMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山单母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);
			
			List<PowerDevice> kgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, false, true);

			boolean isSwitchControl = true;
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}	
			
			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			
			if(mlkgList.size()>0){//分段
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					replaceStr += stationName+"@核实"+deviceName+"相关现场工作任务已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），该设备的二次装置已正常投入，"+deviceName+"具备送电条件/r/n";
					String dzName = "";
					List<PowerDevice> ptdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, "", true, true, true, true);
					if (!ptdzList.isEmpty()) {
						for (PowerDevice dev : ptdzList) {
							String dzNum=CZPService.getService().getDevNum(dev);
							String dzNameTemp = CZPService.getService().getDevName(dev);
							if (dzNameTemp.contains("PT")) {
								dzNameTemp=dzNameTemp.replace("PT"+dzNum, "电压互感器" +"PT"+ dzNum);
							}else{
								dzNameTemp=dzNameTemp.replace(dzNum, "电压互感器" + dzNum);
							}
							if (CommonFunctionWS.ifSwitchSeparateControl(dev)) {
								replaceStr+="文山地调@遥控合上"+stationName+dzNameTemp+"/r/n";
								replaceStr+=stationName+"@确认"+dzNameTemp+"在合闸位置/r/n";
							}else{
								dzName+=dzNameTemp+"、";
							}
						}
						if (StringUtils.isNotEmpty(dzName)){
							replaceStr += stationName+"@将"+dzName+"由冷备用转运行/r/n";
						}
					}else{
						replaceStr += stationName+"@将"+deviceName+"电压互感器由冷备用转运行/r/n";
					}
					
					if(isSwitchControl&&isSwitchSeparateControl){
						for(PowerDevice dev : mlkgList){
							replaceStr += stationName+"@投入"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
						}
						
						replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
					}else{
						replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
					}
				}
				
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(isSwitchControl&&isSwitchSeparateControl){
						for(PowerDevice dev : kgList){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList , stationName);
							}
						}
						
						if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
							for(PowerDevice dev : kgList){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList , stationName);
								}
							}
						}else{
							for(PowerDevice dev : kgList){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList , stationName);
								}
							}
						}
					
						for(PowerDevice dev : mlkgList){
							replaceStr += stationName+"@退出"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
						}
						
						replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
					}else{
						replaceStr += stationName+"@确认"+deviceName+"具备送电条件/r/n";
						
						for(PowerDevice dev : mlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
							
								for(int i = 0; i < xlkgList.size() ; i++){
									PowerDevice xlkg = xlkgList.get(i);
									
									replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(xlkg)+"对线路充电/r/n";
								}
							}else{
								for(int i = 0; i < xlkgList.size() ; i++){
									PowerDevice xlkg = xlkgList.get(i);
									
									if(i == 0){
										if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
											replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(xlkg)+"对"+deviceName+"充电/r/n";
										}
									}else{
										replaceStr += "文山地调@用"+stationName+CZPService.getService().getDevName(xlkg)+"同期合环/r/n";
									}
								}
							}
						}
						
						List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, false, true);
						
						for(PowerDevice dev : zbList){
							List<PowerDevice> gycswList = RuleExeUtil.getTransformerSwitchHigh(dev);
							List<PowerDevice> zycswList = RuleExeUtil.getTransformerSwitchMiddle(dev);
							List<PowerDevice> dycswList = RuleExeUtil.getTransformerSwitchLow(dev);
							
							for(PowerDevice gycsw : gycswList){
								if(RuleExeUtil.getDeviceEndStatus(gycsw).equals("0")){
									replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(gycsw)+"对"+CZPService.getService().getDevName(dev)+"充电/r/n";
								}
							}
							
							for(PowerDevice zycsw : zycswList){
								if(RuleExeUtil.getDeviceEndStatus(zycsw).equals("0")){
									replaceStr += "文山地调@遥控用"+stationName+CZPService.getService().getDevName(zycsw)+"同期合环/r/n";
								}
								
								if(!zycsw.getPowerDeviceID().equals("")){
									List<PowerDevice> zycmlkgList = RuleExeUtil.getDeviceList(zycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
								
									for(PowerDevice zycmlkg : zycmlkgList){
										if(RuleExeUtil.getDeviceEndStatus(zycmlkg).equals("1")){
											replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(zycmlkg)+"/r/n";
										}
									}
								}
							}
							
							for(PowerDevice dycsw : dycswList){
								if(RuleExeUtil.getDeviceEndStatus(dycsw).equals("0")){
									replaceStr += "文山地调@遥控用"+stationName+CZPService.getService().getDevName(dycsw)+"同期合环/r/n";
								}
								
								if(!dycsw.getPowerDeviceID().equals("")){
									List<PowerDevice> dycmlkgList = RuleExeUtil.getDeviceList(dycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
								
									for(PowerDevice dycmlkg : dycmlkgList){
										if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("1")){
											replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
										}
									}
								}
							}
						}
					}
				}else{
					List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
					
					for(PowerDevice dev : zbkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
						}
					}
					
					for(PowerDevice dev : mlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
						}
					}
				}
			}else{
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					replaceStr += stationName+"@核实"+deviceName+"相关现场工作任务已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），该设备的二次装置已正常投入，"+deviceName+"具备送电条件/r/n";
					String dzName = "";
					List<PowerDevice> ptdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, "", true, true, true, true);
					if (!ptdzList.isEmpty()) {
						for (PowerDevice dev : ptdzList) {
							String dzNum=CZPService.getService().getDevNum(dev);
							String dzNameTemp = CZPService.getService().getDevName(dev);
							if (dzNameTemp.contains("PT")) {
								dzNameTemp=dzNameTemp.replace("PT"+dzNum, "电压互感器" +"PT"+ dzNum);
							}else{
								dzNameTemp=dzNameTemp.replace(dzNum, "电压互感器" + dzNum);
							}
							if (CommonFunctionWS.ifSwitchSeparateControl(dev)) {
								replaceStr+="文山地调@遥控合上"+stationName+dzNameTemp+"/r/n";
								replaceStr+=stationName+"@确认"+dzNameTemp+"在合闸位置/r/n";
							}else{
								dzName+=dzNameTemp+"、";
							}
						}
						if (StringUtils.isNotEmpty(dzName)){
							dzName = dzName.substring(0, dzName.length()-1);
							replaceStr += stationName+"@将"+dzName+"由冷备用转运行/r/n";
						}
					}else{
						replaceStr += stationName+"@将"+deviceName+"电压互感器由冷备用转运行/r/n";
					}
					
					if(isSwitchControl&&isSwitchSeparateControl){
						replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
					}else{
						replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
					}
				}
				
				if(isSwitchControl&&isSwitchSeparateControl){
					for(PowerDevice dev : kgList){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList , stationName);
						}
					}
					
					if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
						for(PowerDevice dev : kgList){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList , stationName);
							}
						}
					}else{
						for(PowerDevice dev : kgList){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList , stationName);
							}
						}
					}
				}else{
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
						}
					}
					
					List<PowerDevice> zbList = new ArrayList<PowerDevice>();
					
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							if(!dev.getPowerDeviceName().contains("接地变")){
								zbList.add(dev);
							}
						}
					}
					
					RuleExeUtil.swapDeviceList(zbList);
					
					for(PowerDevice dev : zbList){
						String zbName = CZPService.getService().getDevName(dev);
						
						List<PowerDevice> gycswList = RuleExeUtil.getTransformerSwitchHigh(dev);
						List<PowerDevice> zycswList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						List<PowerDevice> dycswList = RuleExeUtil.getTransformerSwitchLow(dev);
						
						for(PowerDevice gycsw : gycswList){
							if(RuleExeUtil.getDeviceEndStatus(gycsw).equals("0")){
								replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(gycsw)+"对"+zbName+"充电/r/n";
							}
						}
						
						for(PowerDevice zycsw : zycswList){
							if(RuleExeUtil.getDeviceEndStatus(zycsw).equals("0")){
								replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(zycsw)+"/r/n";
							}
						}
						
						for(PowerDevice dycsw : dycswList){
							if(RuleExeUtil.getDeviceEndStatus(dycsw).equals("0")){
								List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dycsw, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
								String mxName = CZPService.getService().getDevName(mxList);

								replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dycsw)+"对"+mxName+"充电/r/n";
							}
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
