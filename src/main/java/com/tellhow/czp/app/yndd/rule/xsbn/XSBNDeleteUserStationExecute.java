package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.czp.app.yndd.view.EquipCheckChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class XSBNDeleteUserStationExecute implements RulebaseInf {
	
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		
		PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
		List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
		
		List<Map<String, String>> stationLineList = CommonFunctionBN.getStationLineList(pd);
		List<String> userstations = new ArrayList<String>();
		
		if(stationLineList.size()>0){
			for(Map<String, String> map : stationLineList){
				String stationName = String.valueOf(map.get("UNIT")).trim();
				String lowerUnit = String.valueOf(map.get("LOWERUNIT")).trim();

				userstations.add(stationName);
				userstations.add(lowerUnit);
			}
		}
		
		String[] stationArr = {"版纳_220kV_普文牵引变","普洱_220kV_木乃河电站","版纳_35kV_流沙河六级水电站","版纳_35kV_流沙河七级水电站","版纳_35kV_流沙河四级水电站","版纳_110kV_勐拉变"};
		
		for (Iterator iterator = lineLoad.iterator(); iterator.hasNext();) {
			PowerDevice dev = (PowerDevice)iterator.next();
			
			for(String station : stationArr){
				if(dev.getPowerStationName().contains(station)){
					iterator.remove();
				}
			}
		}
		
		for(Iterator<PowerDevice> itor = lineLoad.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			for(String userstation : userstations){
				if(dev != null){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					if(stationName.contains(userstation)
							||userstation.contains(dev.getPowerStationName())){
						itor.remove();
						break;
					}
				}
			}
		}
	    
		return true;
	}

}
