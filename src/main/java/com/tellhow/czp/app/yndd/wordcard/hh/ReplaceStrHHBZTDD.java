package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHBZTDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河备自投调电".equals(tempStr)){
			CommonFunctionHH cf = new CommonFunctionHH();
			List<PowerDevice> zblist = new ArrayList<PowerDevice>();
			List<PowerDevice> lossmxList = new ArrayList<PowerDevice>();

			List<PowerDevice> curmxlist = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

			if(curmxlist.size()>0){
				zblist = RuleExeUtil.getDeviceList(curmxlist.get(0), SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, true, true);
			}
			
			List<PowerDevice> alldrkglist =  new ArrayList<PowerDevice>();
			List<PowerDevice> drkglist =  new ArrayList<PowerDevice>();
			
			if(zblist.size()>0){
				if(RuleUtil.isTransformerNQ(zblist.get(0))){//内桥接线
					PowerDevice hotkg = new PowerDevice();
					List<PowerDevice> dckglist =  new ArrayList<PowerDevice>();
					List<PowerDevice> zblists =  new ArrayList<PowerDevice>();
					List<PowerDevice> dycmlkglist = new ArrayList<PowerDevice>();//电源侧分段开关
					List<PowerDevice> fhcmlkglist = new ArrayList<PowerDevice>();//负荷侧分段开关
					List<PowerDevice> gycxlkglist = new ArrayList<PowerDevice>();
					Set<String> set = new HashSet<String>();
					List<PowerDevice> mxlists =  new ArrayList<PowerDevice>();
					
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						
						if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")&&
								(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))){
							hotkg = dev;
						}
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							zblists.add(dev);
						}
						
						if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
							mxlists.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&curDev.getPowerVoltGrade()==dev.getPowerVoltGrade()){
							gycxlkglist.add(dev);
						}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()==dev.getPowerVoltGrade()){
							dycmlkglist.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()>dev.getPowerVoltGrade()){
							fhcmlkglist.add(dev);
						}
					}
					
					List<PowerDevice> zbdyckglist =  RuleExeUtil.getTransformerSwitchLow(zblist.get(0));
					
					if(zbdyckglist.size()>0){
						for(PowerDevice zbdyckg : zbdyckglist){
							List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, false, false);

							for(PowerDevice mx : mxlist){
								if(!lossmxList.contains(mx)){
									lossmxList.add(mx);
								}
								
								List<PowerDevice> swlist = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
								
								for(PowerDevice sw : swlist){
									if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
										drkglist.add(sw);
									}
								}
							}
						}
					}
					
					
					if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						drkglist.addAll(fhcmlkglist);
					}
					
					if(drkglist.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(drkglist)+"热备用/r/n";
					}
					
					RuleExeUtil.swapDeviceList(mxlists);
					
					Collections.reverse(mxlists);
					
					for(PowerDevice dev : mxlists){
						if(dev.getPowerVoltGrade()>=10&&dev.getPowerVoltGrade()<curDev.getPowerVoltGrade()){
							set.add(""+(int)dev.getPowerVoltGrade());
						}
					}
					
					replaceStr += "核实"+CZPService.getService().getDevName(hotkg)+"热备用/r/n";
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "核实110kV备自投装置充电且运行正常/r/n";
					}else if(curDev.getPowerVoltGrade() == 35){
						replaceStr += "核实35kV备自投装置充电且运行正常/r/n";
					}
					RuleExeUtil.swapDeviceList(lossmxList);
					replaceStr += "红河地调配网调控组@核实"+CZPService.getService().getDevName(lossmxList)+"上属其管辖所有10kV出线断路器运行方式已自行考虑好/r/n";
					
					replaceStr += "退出XX线路低压解列保护/r/n";
					
					if(set.size()>0){
						String volt = "";
						
						for(String str : set){
							volt += str+"kV、";
						}
						
						if(volt.endsWith("、")){
							volt = volt.substring(0, volt.length()-1);
						}
						
						replaceStr += "退出"+volt+"备自投装置/r/n";
					}					
					
					List<PowerDevice> list = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
					
					if(list.size()>0){
						List<PowerDevice> otherlist = RuleExeUtil.getLineOtherSideList(list.get(0));
						
						if(otherlist.size()==1){
							for(PowerDevice other : otherlist){
								List<PowerDevice> otherswlist = RuleExeUtil.getLinkedSwitch(other);
								dckglist.addAll(otherswlist);

								PowerDevice station = CBSystemConstants.getPowerStation(other.getPowerStationID());
								replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(otherswlist)+"/r/n";
								replaceStr += CZPService.getService().getDevName(station)+"@核实"+CZPService.getService().getDevName(otherswlist)+"热备用/r/n";
							}
						}else if(otherlist.size()  >  1){
							List<PowerDevice> czlist = new ArrayList<PowerDevice>();
							
							for(PowerDevice other : otherlist){
								List<PowerDevice> otherswlist = RuleExeUtil.getLinkedSwitch(other);
								
								for(PowerDevice othersw : otherswlist){
									if(othersw.getDeviceStatus().equals("0")){
										czlist.add(othersw);
									}
								}
							}
							
							dckglist.addAll(czlist);
							
							for(PowerDevice cz : czlist){
								if(RuleExeUtil.getDeviceBeginStatus(cz).equals("0")){
									PowerDevice station = CBSystemConstants.getPowerStation(cz.getPowerStationID());
									
									replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(cz)+"/r/n";
									replaceStr += CZPService.getService().getDevName(station)+"@核实"+CZPService.getService().getDevName(cz)+"热备用/r/n";
									break;
								}
							}
						}
					}
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "核实110kV备自投正确动作/r/n";
					}else if(curDev.getPowerVoltGrade() == 35){
						replaceStr += "核实35kV备自投正确动作/r/n";
					}
					
					replaceStr += "核实"+CZPService.getService().getDevName(hotkg)+"运行正常/r/n";
					replaceStr += "核实"+CZPService.getService().getDevName(curDev)+"热备用/r/n";

					if(dckglist.size()>0){
						PowerDevice station = CBSystemConstants.getPowerStation(dckglist.get(0).getPowerStationID());

						replaceStr += "红河地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dckglist)+"/r/n";
						replaceStr += CZPService.getService().getDevName(station)+"@核实"+CZPService.getService().getDevName(dckglist)+"运行正常/r/n";
					}
					
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "核实110kV备自投装置充电且运行正常/r/n";
					}else if(curDev.getPowerVoltGrade() == 35){
						replaceStr += "核实35kV备自投装置充电且运行正常/r/n";
					}
					
					replaceStr += "投入XX线路低压解列保护/r/n";

					if(set.size()>0){
						String volt = "";
						
						for(String str : set){
							volt += str+"kV、";
						}
						
						if(volt.endsWith("、")){
							volt = volt.substring(0, volt.length()-1);
						}
						
						replaceStr += "投入"+volt+"备自投装置/r/n";
					}
					

					if(set.size()>0){
						for(String str : set){
							replaceStr += "投入"+CZPService.getService().getDevName(zblists)+"第Ⅰ、Ⅱ套"+str+"kV后备保护动作闭锁"+str+"kV备自投装置/r/n";
						}
						
					}
					replaceStr += "投入10kV#1、#2接地变保护动作闭锁10kV备自投装置/r/n";
					
					String bhtype = "";
					
					String[] arr = {"第Ⅰ、Ⅱ套差动、高后备、非电量保护","第Ⅰ、Ⅱ套差动、后备（跳两侧及桥）、非电量保护"};
					
					int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择保护类型", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
					if(sel==0){
						bhtype =  "第Ⅰ、Ⅱ套差动、高后备、非电量保护";
					}else if(sel==1){
						bhtype =  "第Ⅰ、Ⅱ套差动、后备（跳两侧及桥）、非电量保护";
					}else{
						bhtype =  "第Ⅰ、Ⅱ套差动、高后备、非电量保护";
					}
					
					if(dycmlkglist.size()>0){
						if(dycmlkglist.get(0).getDeviceStatus().equals("1")){
							
							RuleExeUtil.swapDeviceList(zblist);
							
							replaceStr += "投入"+CZPService.getService().getDevName(zblist)+bhtype+"动作闭锁110kV备自投装置/r/n";
						}else{
							if(gycxlkglist.size()>0){
								for(PowerDevice gycxlkg : gycxlkglist){
									if(gycxlkg.getDeviceStatus().equals("0")){
										List<PowerDevice> templist = RuleExeUtil.getDeviceList(gycxlkg, SystemConstants.PowerTransformer, SystemConstants.Switch, true, false, true);
										
										replaceStr += "退出"+CZPService.getService().getDevName(templist)+bhtype+"动作闭锁110kV备自投装置/r/n";
									}else if(gycxlkg.getDeviceStatus().equals("1")){
										List<PowerDevice> templist = RuleExeUtil.getDeviceList(gycxlkg, SystemConstants.PowerTransformer, SystemConstants.Switch, true, false, true);
										
										replaceStr += "投入"+CZPService.getService().getDevName(templist)+bhtype+"动作闭锁110kV备自投装置/r/n";
									}
								}
							}
						}
					}
				}else{//单母分段接线
					PowerDevice hotkg = new PowerDevice();
					List<PowerDevice> dckglist =  new ArrayList<PowerDevice>();
					List<PowerDevice> zblists =  new ArrayList<PowerDevice>();
					List<PowerDevice> mxlists =  new ArrayList<PowerDevice>();
					List<PowerDevice> gycmlkglist =  new ArrayList<PowerDevice>();
					List<PowerDevice> fhcmlkglist = new ArrayList<PowerDevice>();//负荷侧分段开关
					List<PowerDevice> dycmlkglist = new ArrayList<PowerDevice>();//低压侧分段开关
					List<PowerDevice> zycmlkglist = new ArrayList<PowerDevice>();//中压侧分段开关

					Set<String> set = new HashSet<String>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						
						if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")&&
								(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))){
							hotkg = dev;
						}
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							zblists.add(dev);
						}
						
						if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
							mxlists.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()>dev.getPowerVoltGrade()){
							fhcmlkglist.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()==dev.getPowerVoltGrade()){
							gycmlkglist.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&dev.getPowerVoltGrade()==10){
							dycmlkglist.add(dev);
						}
						
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
							alldrkglist.add(dev);
						}
					}
					
					for(PowerDevice fhcmlkg : fhcmlkglist){
						if(!dycmlkglist.contains(fhcmlkg)){
							zycmlkglist.add(fhcmlkg);
						}
					}
					
					for(PowerDevice zb : zblist){
						List<PowerDevice> zbdyckglist =  RuleExeUtil.getTransformerSwitchLow(zb);
						
						if(zbdyckglist.size()>0){
							for(PowerDevice zbdyckg : zbdyckglist){
								if(zbdyckg.getPowerVoltGrade() == 10){
									List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, false, false);

									for(PowerDevice mx : mxlist){
										if(!lossmxList.contains(mx)){
											lossmxList.add(mx);
										}
										
										List<PowerDevice> swlist = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										
										for(PowerDevice sw : swlist){
											if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
												drkglist.add(sw);
											}
										}
									}
								}
							}
						}
					}
					
					if(gycmlkglist.size()==0){
						if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							alldrkglist.addAll(fhcmlkglist);
						}
						
						if(alldrkglist.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(alldrkglist)+"热备用/r/n";
						}
					}else{
						if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							drkglist.addAll(fhcmlkglist);
						}
						
						if(drkglist.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(drkglist)+"热备用/r/n";
						}
					}
					
					RuleExeUtil.swapDeviceList(mxlists);
					
					Collections.reverse(mxlists);
					
					for(PowerDevice dev : mxlists){
						if(dev.getPowerVoltGrade()>=10&&dev.getPowerVoltGrade()<curDev.getPowerVoltGrade()){
							set.add(""+(int)dev.getPowerVoltGrade());
						}
					}
					
					replaceStr += "核实"+CZPService.getService().getDevName(hotkg)+"热备用/r/n";
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "核实110kV备自投装置充电且运行正常/r/n";
					}else if(curDev.getPowerVoltGrade() == 35){
						replaceStr += "核实35kV备自投装置充电且运行正常/r/n";
					}
					RuleExeUtil.swapDeviceList(lossmxList);
					replaceStr += "红河地调配网调控组@核实"+CZPService.getService().getDevName(lossmxList)+"上属其管辖所有10kV出线断路器运行方式已自行考虑好/r/n";
					
					replaceStr += "退出XX线路低压解列保护/r/n";
					
					if(dycmlkglist.size()>1&&zycmlkglist.size()>0){
						replaceStr += "退出"+cf.getBztStrReplace(dycmlkglist)+"、"+cf.getBztStrReplace(zycmlkglist)+"备自投装置/r/n";
					}else if(dycmlkglist.size()>1&&zycmlkglist.size()==0){
						replaceStr += "退出"+cf.getBztStrReplace(dycmlkglist)+"备自投装置/r/n";
					}else{
						if(set.size()>0){
							String volt = "";
							
							for(String str : set){
								volt += str+"kV、";
							}
							
							if(volt.endsWith("、")){
								volt = volt.substring(0, volt.length()-1);
							}
							
							replaceStr += "退出"+volt+"备自投装置/r/n";
						}	
					}
					
					List<PowerDevice> list = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
					
					if(list.size()>0){
						List<PowerDevice> otherlist = RuleExeUtil.getLineOtherSideList(list.get(0));
						
						if(otherlist.size()==1){
							for(PowerDevice other : otherlist){
								List<PowerDevice> otherswlist = RuleExeUtil.getLinkedSwitch(other);
								dckglist.addAll(otherswlist);

								PowerDevice station = CBSystemConstants.getPowerStation(other.getPowerStationID());
								replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(otherswlist)+"/r/n";
								replaceStr += CZPService.getService().getDevName(station)+"@核实"+CZPService.getService().getDevName(otherswlist)+"热备用/r/n";
							}
						}else if(otherlist.size()  >  1){
							List<PowerDevice> czlist = new ArrayList<PowerDevice>();
							
							for(PowerDevice other : otherlist){
								List<PowerDevice> otherswlist = RuleExeUtil.getLinkedSwitch(other);
								
								for(PowerDevice othersw : otherswlist){
									if(othersw.getDeviceStatus().equals("0")){
										czlist.add(othersw);
									}
								}
							}
							
							dckglist.addAll(czlist);
							
							for(PowerDevice cz : czlist){
								if(RuleExeUtil.getDeviceBeginStatus(cz).equals("0")){
									PowerDevice station = CBSystemConstants.getPowerStation(cz.getPowerStationID());
									
									replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(cz)+"/r/n";
									replaceStr += CZPService.getService().getDevName(station)+"@核实"+CZPService.getService().getDevName(cz)+"热备用/r/n";
									break;
								}
							}
						}
					}else if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());

						replaceStr += "红河地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(curDev)+"/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(curDev)+"热备用/r/n";
						
					}
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "核实110kV备自投正确动作/r/n";
					}else if(curDev.getPowerVoltGrade() == 35){
						replaceStr += "核实35kV备自投正确动作/r/n";
					}
					
					replaceStr += "核实"+CZPService.getService().getDevName(hotkg)+"运行正常/r/n";
					replaceStr += "核实"+CZPService.getService().getDevName(curDev)+"热备用/r/n";

					if(dckglist.size()>0){
						PowerDevice station = CBSystemConstants.getPowerStation(dckglist.get(0).getPowerStationID());

						replaceStr += "红河地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dckglist)+"/r/n";
						replaceStr += CZPService.getService().getDevName(station)+"@核实"+CZPService.getService().getDevName(dckglist)+"运行正常/r/n";
					}
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "核实110kV备自投装置充电且运行正常/r/n";
					}else if(curDev.getPowerVoltGrade() == 35){
						replaceStr += "核实35kV备自投装置充电且运行正常/r/n";
					}
					
					replaceStr += "投入XX线路低压解列保护/r/n";
					
					if(dycmlkglist.size()>1&&zycmlkglist.size()>0){
						replaceStr += "投入"+cf.getBztStrReplace(dycmlkglist)+"、"+cf.getBztStrReplace(zycmlkglist)+"备自投装置/r/n";
					}else if(dycmlkglist.size()>1&&zycmlkglist.size()==0){
						replaceStr += "投入"+cf.getBztStrReplace(dycmlkglist)+"备自投装置/r/n";
					}else{
						if(set.size()>0){
							String volt = "";
							
							for(String str : set){
								volt += str+"kV、";
							}
							
							if(volt.endsWith("、")){
								volt = volt.substring(0, volt.length()-1);
							}
							
							replaceStr += "投入"+volt+"备自投装置/r/n";
						}
					}
					
					if(zblists.size()>2){
						replaceStr += cf.getZbHbBhStrReplace(curDev, "midlow","投入");
					}else{
						if(set.size()>0){
							for(String str : set){
								replaceStr += "投入"+CZPService.getService().getDevName(zblists)+"第Ⅰ、Ⅱ套"+str+"kV后备保护动作闭锁"+str+"kV备自投装置/r/n";
							}
						}
					}
					
					replaceStr += "投入10kV#1、#2接地变保护动作闭锁10kV备自投装置/r/n";
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
