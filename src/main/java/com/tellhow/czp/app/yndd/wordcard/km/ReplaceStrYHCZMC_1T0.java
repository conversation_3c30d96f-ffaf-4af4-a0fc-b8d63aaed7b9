package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.TransformKDXLChoose;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

/**  
  用户厂站名称热备到运行
* <p>Description: </p>  
* <AUTHOR>
* @date 2021年9月2日    
*/
public class ReplaceStrYHCZMC_1T0 implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String result = "";

		String curLineId = curDev.getPowerDeviceID();
		String devName = CZPService.getService().getDevName(curDev);
		
		TransformKDXLChoose kdxl = new TransformKDXLChoose();
		
		String sql = "SELECT DEVICE_NUM,UNIT,OPERATION_KIND,LOWERUNIT,ENDPOINT_KIND FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (  SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+curLineId+"')";
		List<Map<String, Object>> stations = DBManager.queryForList(sql);
		
		if(stations != null && stations.size()>0) {
			
			if(kdxl.retMap.size()>0){
				for(Iterator<Map<String, Object>> itor = stations.iterator();itor.hasNext();){
					Map<String, Object> map = itor.next();
					String stationName = StringUtils.ObjToString(map.get("UNIT"));

					if(!kdxl.retMap.containsKey(stationName)){
						itor.remove();
					}
				}
			}
			
			for(Map<String, Object> station:stations) {
				String stationName = StringUtils.ObjToString(station.get("UNIT")).trim();
				String stationKind = StringUtils.ObjToString(station.get("OPERATION_KIND")).trim();
				String deviceNum = StringUtils.ObjToString(station.get("DEVICE_NUM")).trim();
				String lowerunit = StringUtils.ObjToString(station.get("LOWERUNIT")).trim();
				String endpointkind = StringUtils.ObjToString(station.get("ENDPOINT_KIND")).trim();

				if(stationKind.equals("下令")){
					if(endpointkind.equals("外接站用变")){
						result += ("昆明地调@遥控合上"+stationName+devName+ deviceNum +"断路器" + "/r/n");
					}else{
						if(lowerunit.equals("")){
							result += (stationName + "@合上"+devName+ deviceNum +"断路器" + "/r/n");
						}else{
							result += (stationName + "@合上"+lowerunit+devName+ deviceNum +"断路器" + "/r/n");
						}
					}
				}
			}
		}
		
		if(result.equals("")){
			result = null;
		}
		
		return result;
	}

}
