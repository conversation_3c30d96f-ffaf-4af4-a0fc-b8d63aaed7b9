package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSXLTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山线路停电".equals(tempStr)){
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			
			String sql = "SELECT UNIT,LOWERUNIT,SWITCH_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,OPERATION_KIND FROM "+CBSystemConstants.equipUser+"T_A_CARDWORDUSER WHERE LINE_ID = '"+sourceLineTrans.getCimID()+"'";
			List<Map<String,String>> userStationList = DBManager.queryForList(sql);
			
			List<String> normalList = new ArrayList<String>();
			
			/*
			 * 运行转热备用
			 */
			for(Map<String,String> userStationMap : userStationList){
				String unit = StringUtils.ObjToString(userStationMap.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(userStationMap.get("LOWERUNIT"));
				String switchName = StringUtils.ObjToString(userStationMap.get("SWITCH_NAME"));
				String endpointKind = StringUtils.ObjToString(userStationMap.get("ENDPOINT_KIND"));
				String operationKind = StringUtils.ObjToString(userStationMap.get("OPERATION_KIND"));

				if(endpointKind.equals("单线并网电厂")){
					replaceStr += unit+"@核实"+lowerunit+switchName+"处检修/r/n";
				}else{
					if(operationKind.equals("许可")){
						replaceStr += unit+"@核实"+lowerunit+switchName+"处热备用/r/n";
					}else if(operationKind.equals("下令")){
						replaceStr += unit+"@断开"+lowerunit+switchName+"/r/n";
					}
				}
			}

			if(sourceLineTrans.getPowerVoltGrade() > 110){
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					String maintenance = CommonFunctionBS.getMaintenance(stationName);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						String deviceName = CZPService.getService().getDevName(dev);

						if(maintenance.equals(stationName)){
							replaceStr += stationName+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
							String normal = stationName+"@核实"+deviceName+"一、二次设备无异常/r/n";
							normalList.add(normal);
						}else{
							replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
							String normal = maintenance+"@核实"+stationName+deviceName+"一、二次设备无异常/r/n";
							normalList.add(normal);
						}
					}
				}
			}
			
			if(sourceLineTrans!=null){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);
				
				if(zbList.size()>0){
					for(PowerDevice dev : zbList){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由运行转热备用/r/n";
					}
				}else{
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String deviceName = CZPService.getService().getDevName(dev);
						String maintenance = CommonFunctionBS.getMaintenance(stationName);

						if(maintenance.equals(stationName)){
							replaceStr += stationName+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
							String normal = stationName+"@核实"+deviceName+"一、二次设备无异常/r/n";
							normalList.add(normal);
						}else{
							replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
							String normal = maintenance+"@核实"+stationName+deviceName+"一、二次设备无异常/r/n";
							normalList.add(normal);
						}
					}
				}
			}

			/*
			 * 热备用转冷备用
			 */
			if(userStationList.size() > 0 || sourceLineTrans.getPowerVoltGrade() == 110 || curDev.getPowerVoltGrade() == 35){
				if(sourceLineTrans!=null){
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					for(PowerDevice dev : xlswList){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String deviceName = CZPService.getService().getDevName(dev);

						if(RuleExeUtil.getDeviceEndStatus(curDev).equals("1")){
							replaceStr += "保山地调@执行"+stationName+deviceName+"由运行转热备用程序操作/r/n";
						}else{
							if(CommonFunctionBS.ifSwitchSeparateControl(dev)){
								replaceStr += "保山地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
							}
						}
					}
				}
			}else{
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("1")){
					replaceStr += "保山地调@执行"+CZPService.getService().getDevName(curDev)+"由运行转热备用程序操作/r/n";
				}else{
					replaceStr += "保山地调@执行"+CZPService.getService().getDevName(curDev)+"由运行转冷备用程序操作/r/n";
				}
			}

			for(String normal : normalList){
				replaceStr += normal;
			}
			
			for(Map<String,String> userStationMap : userStationList){
				String unit = StringUtils.ObjToString(userStationMap.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(userStationMap.get("LOWERUNIT"));
				String switchName = StringUtils.ObjToString(userStationMap.get("SWITCH_NAME"));
				String endpointKind = StringUtils.ObjToString(userStationMap.get("ENDPOINT_KIND"));
				String operationKind = StringUtils.ObjToString(userStationMap.get("OPERATION_KIND"));

				if(endpointKind.equals("单线并网电厂")){
					
				}else{
					if(operationKind.equals("许可")){
						replaceStr += unit+"@核实"+lowerunit+switchName+"处冷备用/r/n";
					}else if(operationKind.equals("下令")){
						replaceStr += unit+"@将"+lowerunit+switchName+"由热备用转冷备用/r/n";
					}
				}
			}
			
			/*
			 * 冷备用转检修
			 */
			
			if(curDev.getDeviceStatus().equals("3")){
				if(userStationList.size() > 0){
					for(Map<String,String> userStationMap : userStationList){
						String unit = StringUtils.ObjToString(userStationMap.get("UNIT"));
						String lowerunit = StringUtils.ObjToString(userStationMap.get("LOWERUNIT"));
						String jddzName = StringUtils.ObjToString(userStationMap.get("GROUNDDISCONNECTOR_NAME"));

						replaceStr += unit+"@合上"+lowerunit+jddzName+"/r/n";
					}
				}else{
					for(PowerDevice dev : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String maintenance = CommonFunctionBS.getMaintenance(stationName);

						List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
						
						for(PowerDevice jddz : jddzList){
							if(maintenance.equals(stationName)){
								replaceStr += stationName+"@合上"+CZPService.getService().getDevName(jddz)+"/r/n";
							}else{
								replaceStr += maintenance+"@合上"+stationName+CZPService.getService().getDevName(jddz)+"/r/n";
							}
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					String maintenance = CommonFunctionBS.getMaintenance(stationName);

					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					  
					for(PowerDevice jddz : jddzList){
						if(maintenance.equals(stationName)){
							replaceStr += stationName+"@合上"+CZPService.getService().getDevName(jddz)+"/r/n";
						}else{
							replaceStr += maintenance+"@合上"+stationName+CZPService.getService().getDevName(jddz)+"/r/n";
						}
					}
				}
				
				
			}
		}
		
		return replaceStr;
	}

}
