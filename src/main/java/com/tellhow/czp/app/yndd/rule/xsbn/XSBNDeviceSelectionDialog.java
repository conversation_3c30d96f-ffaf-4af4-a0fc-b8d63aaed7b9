package com.tellhow.czp.app.yndd.rule.xsbn;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 母线多选开票开关选择界面
 */
public class XSBNDeviceSelectionDialog extends JDialog {
    private final JTable deviceTable;
    private final JCheckBox selectAllCheckBox;
	private boolean isCancel = true;
    public static Map<PowerDevice,String> pdMap = new HashMap<PowerDevice, String>();

    /**
     * @param parent 父级界面
     * @param modal modal
     * @param devices 所有设备选项
     * @param selected 上文已选择的设备选项
     * @param reverse 是否已选择（用于反向成票时）
     */
    public XSBNDeviceSelectionDialog(Frame parent, boolean modal, List<PowerDevice> deviceList,String type) {
    	
        super(parent, "设备选择", modal);
        pdMap.clear();
        getContentPane().setLayout(new BorderLayout());
        deviceTable = new JTable();
        
        JTableHeader header = deviceTable.getTableHeader();
        header.setFont(new Font("微软雅黑",Font.PLAIN,24));             //字体
        header.setPreferredSize(new Dimension(header.getWidth(),30));
        
        String[] columnNames = {"选择","厂站名称", "设备名称", "初始状态", "目标状态"};
        // 创建表格模型
        final DefaultTableModel tableModel = new DefaultTableModel(null,columnNames) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 0) {
                    return Boolean.class; // 第一列使用复选框
                } else {
                    return super.getColumnClass(columnIndex);
                }
            }
            
            @Override
            public boolean isCellEditable(int row, int column) {
            	if(column == 2){
            		return false;
            	}else{
            		return true;
            	}
            }
        };
        // 创建初始状态和目标状态的下拉框选项
        for (int i = 0 ; i < deviceList.size() ; i++) {
        	PowerDevice device = deviceList.get(i);
            PowerDevice station = CBSystemConstants.getPowerStation(device.getPowerStationID());
            String stationName = CZPService.getService().getDevName(station);

            String beginStatus = "";
            String endStatus = "";
            
            if(type.equals("停电")){
            	 if(device.getDeviceType().equals(SystemConstants.SwitchSeparate)){
                	 beginStatus = "合上";
                	 endStatus = "拉开";
                 }else{
            		 beginStatus = "运行";
            		 endStatus = "冷备用";
                 }
            }else{
            	if(device.getDeviceType().equals(SystemConstants.SwitchSeparate)){
                	beginStatus = "拉开";
                	endStatus = "合上";
                }else{
                	 beginStatus = "冷备用";
            		 endStatus = "运行";
                }
            }
            
            tableModel.addRow(new Object[]{true,stationName, device, getCodeNameModel(beginStatus), getCodeNameModel(endStatus)}); // 默认全选
        }

        // 创建表格
        deviceTable.setModel(tableModel);
        deviceTable.getColumnModel().getColumn(0).setMinWidth(70);
        deviceTable.getColumnModel().getColumn(0).setMaxWidth(80); // 设置选择列的宽度
        deviceTable.getColumnModel().getColumn(0).setPreferredWidth(70);// 设置初始状态列宽度
        deviceTable.getColumnModel().getColumn(1).setMinWidth(120);
        deviceTable.getColumnModel().getColumn(1).setMaxWidth(140); // 设置选择列的宽度
        deviceTable.getColumnModel().getColumn(1).setPreferredWidth(120);// 设置初始状态列宽度
        
        deviceTable.getColumnModel().getColumn(3).setMinWidth(100);
        deviceTable.getColumnModel().getColumn(3).setMaxWidth(110);
        deviceTable.getColumnModel().getColumn(3).setPreferredWidth(100);// 设置初始状态列宽度
        
        deviceTable.getColumnModel().getColumn(4).setMinWidth(100);
        deviceTable.getColumnModel().getColumn(4).setMaxWidth(110);
        deviceTable.getColumnModel().getColumn(4).setPreferredWidth(100);// 设置目标状态列宽度
        deviceTable.setRowHeight(26);
        // 创建初始状态和目标状态的单元格渲染器
        DefaultTableCellRenderer renderer = new DefaultTableCellRenderer();
        renderer.setHorizontalAlignment(SwingConstants.CENTER); // 居中显示
        deviceTable.getColumnModel().getColumn(3).setCellRenderer(renderer);
        deviceTable.getColumnModel().getColumn(4).setCellRenderer(renderer);
        deviceTable.getTableHeader().setDefaultRenderer(renderer);  //表头文字居中

        DefaultComboBoxModel comboBoxModel = createDefaultComboBoxModel();
        // 创建初始状态下拉框编辑器
        JComboBox initialStatusComboBox = new JComboBox(comboBoxModel);
        DefaultCellEditor initialStatusEditor = new DefaultCellEditor(initialStatusComboBox);

        // 创建目标状态下拉框编辑器
        final JComboBox targetStatusComboBox = new JComboBox(comboBoxModel);
        DefaultCellEditor targetStatusEditor = new DefaultCellEditor(targetStatusComboBox);
        deviceTable.getColumnModel().getColumn(3).setCellEditor(initialStatusEditor);
        deviceTable.getColumnModel().getColumn(4).setCellEditor(targetStatusEditor);
        JScrollPane scrollPane = new JScrollPane(deviceTable);
        getContentPane().add(scrollPane, BorderLayout.CENTER);

        // 创建全选复选框
        selectAllCheckBox = new JCheckBox("全选");
        selectAllCheckBox.setSelected(true);
        selectAllCheckBox.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                selectAll(selectAllCheckBox.isSelected()); // 全选或全不选
            }
        });
        JPanel topPanel = new JPanel();
        getContentPane().add(topPanel, BorderLayout.NORTH);
        topPanel.setLayout(new BorderLayout(0, 0));
        topPanel.add(selectAllCheckBox,BorderLayout.WEST);

        // 创建确定按钮
        JButton okButton = new JButton("确定");
        okButton.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png"))); // NOI18N
        
        topPanel.add(okButton,BorderLayout.EAST);
        okButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
            	if (deviceTable.isEditing())
            		deviceTable.getCellEditor().stopCellEditing();
            	
            	isCancel = false;
                // 获取选中的设备
                DefaultTableModel model = (DefaultTableModel) deviceTable.getModel();
                for (int i = 0; i < model.getRowCount(); i++) {
                    Boolean checked = (Boolean) model.getValueAt(i, 0);
                    PowerDevice device = (PowerDevice) model.getValueAt(i, 2);
                    String endStatus = ((CodeNameModel) model.getValueAt(i,4)).getCode();
                    if (checked != null && checked) {
                    	if(device.getDeviceType().equals(SystemConstants.Switch)
                    			||device.getDeviceType().equals(SystemConstants.SwitchSeparate)){
                    		if(endStatus.equals("4")){
                    			endStatus = "0";
                    		}else if(endStatus.equals("5")){
                    			endStatus = "1";
                    		}
                        	RuleExeUtil.deviceStatusExecute(device, device.getDeviceStatus(), endStatus);
                    	}
                    	
                    	pdMap.put(device,endStatus);
                    }
                }
                dispose();
            }
        });
        int maxVisibleButtons = 10; // 最多显示的按钮数量
        int actualHeight = Math.min(deviceList.size(), maxVisibleButtons) * 24 + 50; // 根据按钮数量计算实际的界面高度,+5避免出现滚动条影响美观
        scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setPreferredSize(new Dimension(600, actualHeight)); // 应用实际的界面高度
        pack();
        setLocationRelativeTo(null);    //居中显示
        setVisible(true);
    }

    /**
     * @param select 全选
     */
    private void selectAll(boolean select) {
        DefaultTableModel model = (DefaultTableModel) deviceTable.getModel();
        for (int i = 0; i < model.getRowCount(); i++) {
            model.setValueAt(select, i, 0);
        }
    }
    
    private DefaultComboBoxModel createDefaultComboBoxModel(){
        DefaultComboBoxModel comboBoxModel = new DefaultComboBoxModel();
        CodeNameModel codeNameModel1 = new CodeNameModel("0","运行");
        CodeNameModel codeNameModel2 = new CodeNameModel("1","热备用");
        CodeNameModel codeNameModel3 = new CodeNameModel("2","冷备用");
        CodeNameModel codeNameModel4 = new CodeNameModel("3","检修");
        CodeNameModel codeNameModel5 = new CodeNameModel("4","合上");
        CodeNameModel codeNameModel6 = new CodeNameModel("5","拉开");
        
        comboBoxModel.addElement(codeNameModel1);
        comboBoxModel.addElement(codeNameModel2);
        comboBoxModel.addElement(codeNameModel3);
        comboBoxModel.addElement(codeNameModel4);
        comboBoxModel.addElement(codeNameModel5);
        comboBoxModel.addElement(codeNameModel6);
        
        return comboBoxModel;
    }

    /**
     * @param status 当前状态
     * @return 获取当前状态的模型
     */
    private CodeNameModel getCodeNameModel(String status) {
        CodeNameModel cnm = new CodeNameModel();
        if (status.equals("运行")){
            cnm.setCode("0");
            cnm.setName(status);
        }else if (status.equals("热备用")){
            cnm.setCode("1");
            cnm.setName(status);
        }else if (status.equals("冷备用")){
            cnm.setCode("2");
            cnm.setName(status);
        }else if(status.equals("合上")){
            cnm.setCode("4");
            cnm.setName(status);
        }else if (status.equals("拉开")){
            cnm.setCode("5");
            cnm.setName(status);
        }else {
            cnm.setCode("3");
            cnm.setName(status);
        }
        return cnm;
    }

    public static Map<PowerDevice,String> getPdMap() {
        return pdMap;
    }
    
    public boolean isCancel() {
		return isCancel;
	}
}

