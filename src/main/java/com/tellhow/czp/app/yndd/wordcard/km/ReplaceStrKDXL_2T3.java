package com.tellhow.czp.app.yndd.wordcard.km;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.TransformKDXLChoose;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

/**  
 开断线路冷备到检修替换类
* <p>Description: </p>  
* <AUTHOR>
* @date 2021年9月7日    
*/
public class ReplaceStrKDXL_2T3 implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		if("开断线路冷备到检修".equals(tempStr)) {
			String devName = CZPService.getService().getDevName(curDev);
			if(TransformKDXLChoose.retMap != null && TransformKDXLChoose.retMap.size()>0) {
				String result = "";
				for(Object key : TransformKDXLChoose.retMap.keySet()) {
					// 已选择厂站为用户维护厂站，则出许可
					if(TransformKDXLChoose.userStations != null && TransformKDXLChoose.userStations.containsKey(String.valueOf(key))) {
						result += (String.valueOf(key)) + "@许可将"+ devName +"由冷备用转检修" + "\r\n";
					}
				}
				if(result.length() == 0) {
					return null;
				}
				return result;
			}
			return null;
		}
		return null;
	}

}
