package com.tellhow.czp.app.yndd.wordcard.hh;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrHHXLHHDD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("红河线路合环调电".equals(tempStr)){
			
			PowerDevice onswDevice = null;
			PowerDevice offswDevice = null;
			
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				if(dtd.getTransDevice().getDeviceType().equals(SystemConstants.Switch)){
					if(dtd.getBeginstatus().equals("1")&&dtd.getEndstate().equals("0")){
						onswDevice=dtd.getTransDevice();
					}else if(dtd.getBeginstatus().equals("0")&&dtd.getEndstate().equals("1")){
						offswDevice=dtd.getTransDevice();
					}
				}
			}
			
			
			if(onswDevice!=null&&offswDevice!=null){
				String onswDeviceStationName = CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(onswDevice.getPowerStationID()));
				String offswDeviceStationName = CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(offswDevice.getPowerStationID()));
				String onswDeviceName =  CZPService.getService().getDevName(onswDevice);
				String offswDeviceName =  CZPService.getService().getDevName(offswDevice);

				String czqState = RuleExeUtil.getStatus(RuleExeUtil.getDeviceBeginStatus(onswDevice));
				
				String tagMxName = "";
				
				if(onswDevice.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					List<PowerDevice>  mxdzList  =  RuleExeUtil.getDeviceList(onswDevice, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeMX, "", true, false, true, true);
					
					for(PowerDevice dev : mxdzList){
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
							List<PowerDevice>  mxList  =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
							
							tagMxName = "于"+CZPService.getService().getDevName(mxList);
							break;
						}
					}
				}
				
				replaceStr+= onswDeviceStationName+"@核实"+onswDeviceName+czqState+tagMxName+"\r\n";
				
				tagMxName = "";
				
				replaceStr+= onswDeviceStationName+"@投入"+onswDeviceName+"线路保护\r\n";
				replaceStr+= onswDeviceStationName+"@退出"+onswDeviceName+"重合闸\r\n";
				
				if(czqState.equals("冷备用")){
					replaceStr+= onswDeviceStationName+"@将"+onswDeviceName+"由冷备用转热备用\r\n";
				}
				
				replaceStr+= "红河地调@遥控用"+onswDeviceStationName+onswDeviceName+"同期合环\r\n";
				replaceStr+= "红河地调@遥控断开"+offswDeviceStationName+offswDeviceName+"\r\n";
				replaceStr+= onswDeviceStationName+"@核实"+onswDeviceName+"运行正常\r\n";
				
				if(offswDevice.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					List<PowerDevice>  mxdzList  =  RuleExeUtil.getDeviceList(offswDevice, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeMX, "", true, false, true, true);
					
					for(PowerDevice dev : mxdzList){
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
							List<PowerDevice>  mxList  =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
							
							tagMxName = "于"+CZPService.getService().getDevName(mxList);
							break;
						}
					}
				}
				
				replaceStr+= offswDeviceStationName+"@核实"+offswDeviceName+"热备用"+tagMxName+"\r\n";
				replaceStr+= onswDeviceStationName+"@投入"+onswDeviceName+"重合闸\r\n";

			}
	    }
		return replaceStr;
	}

}
