package com.tellhow.czp.app.yndd.view;


import java.awt.BorderLayout;
import java.awt.Button;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

import org.w3c.dom.Element;

import com.tellhow.graphicframework.algorithm.ElementSearch;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.stationstartup.StationDeviceToplogy;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;



public class DevStationTransformerDialog extends javax.swing.JDialog {
	private DefaultTableModel dTableModel;
	private JPanel topPanel;//查询条件及按钮面板
	private JPanel mainPanel;//信息面板
	private JTextField searchText;//查询框
	private JButton searchButton;//查询按钮
	private JButton addButton;//新增按钮
	private JButton updateButton;//修改按钮
	private JButton delButton;//删除按钮
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTableInfo;//信息列表

	public DevStationTransformerDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("厂站站用变维护");
		setLocationCenter();
		initTable("");
	}
	
	/**
	 * 初始化表格  传入参数关键字
	 */
	public void initTable(String gjz) {
		dTableModel.setRowCount(0);
		
		String sql="SELECT ID,DEV_ID,STATION_ID,STATION_NAME, DEV_NAME, ZYB_NAME, ZYB_DZNAME, ZYB_DEVID FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB ";
		 if(!gjz.equals("")){
			 sql+=" WHERE STATION_NAME LIKE '%"+gjz+"%' OR DEV_NAME LIKE '%"+gjz+"%'";
			 }	
		 sql+="ORDER BY STATION_NAME";
		 List<Map<String,String>> results=DBManager.queryForList(sql);

		 Map<String,String> temp=new HashMap<String,String>();
		 for (int i = 0; i < results.size(); i++) {
			 temp= results.get(i);
			 
			 String id=StringUtils.ObjToString(temp.get("ID"));
			 String dev_id=StringUtils.ObjToString(temp.get("DEV_ID"));
			 String station_id=StringUtils.ObjToString(temp.get("STATION_ID"));
			 String station_name=StringUtils.ObjToString(temp.get("STATION_NAME"));
			 String dev_name=StringUtils.ObjToString(temp.get("DEV_NAME"));
			 String zyb_name=StringUtils.ObjToString(temp.get("ZYB_NAME"));
			 String zyb_dzname=StringUtils.ObjToString(temp.get("ZYB_DZNAME"));
			 String zyb_devid=StringUtils.ObjToString(temp.get("ZYB_DEVID"));

			 Object[] rowData = {id,station_id,dev_id,i+1,station_name,dev_name,zyb_name,zyb_dzname,zyb_devid};
			 dTableModel.addRow(rowData);
		 }

		jTableInfo.setModel(dTableModel);
		DefaultTableCellRenderer r  = new  DefaultTableCellRenderer();   
		r.setHorizontalAlignment(JTextField.CENTER);   
		jTableInfo.getColumn("序号").setCellRenderer(r);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}


	private void initComponents() {
		getContentPane().setLayout(new BorderLayout());
		this.setSize(800, 480);
		topPanel =new JPanel();
		topPanel.setPreferredSize(new Dimension(0,45));
		getContentPane().add(topPanel,BorderLayout.NORTH);
		mainPanel =new JPanel();
		getContentPane().add(mainPanel,BorderLayout.CENTER);
		
		JLabel label1 = new JLabel("厂站关键字:");
		JLabel label2 = new JLabel("");//增加空位用
		label2.setPreferredSize(new Dimension(60,0));
		topPanel.setLayout(new FlowLayout(FlowLayout.CENTER,10,10));
		searchText =new JTextField();
		searchText.setPreferredSize(new Dimension(200,20));
		searchButton =new JButton("查询");
		searchButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				searchButtonActionPerformed(evt);
			}
		});
		addButton =new JButton("新增");
		addButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				addButtonActionPerformed(evt);
			}
		});
		updateButton =new JButton("修改");
		updateButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				updateButtonActionPerformed(evt);
			}
		});
		delButton =new JButton("删除");
		delButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				delButtonActionPerformed(evt);
			}
		});
		
		topPanel.add(label1);
		topPanel.add(searchText);
		topPanel.add(searchButton);
		topPanel.add(label2);
		topPanel.add(addButton);
		topPanel.add(updateButton);
		topPanel.add(delButton);
		
		dTableModel = new DefaultTableModel(null,new String[] { "ID","STATIONID","DEVID","序号","厂站名称","设备名称","站用变名称","站用变关联设备名称"}){
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		jTableInfo = new JTable();
		jTableInfo.setModel(dTableModel);
//		jTableInfo.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
		
		jTableInfo.getColumnModel().getColumn(0).setMinWidth(0);
		jTableInfo.getColumnModel().getColumn(0).setMaxWidth(0);
		jTableInfo.getColumnModel().getColumn(1).setMinWidth(0);
		jTableInfo.getColumnModel().getColumn(1).setMaxWidth(0);
		jTableInfo.getColumnModel().getColumn(2).setMinWidth(0);
		jTableInfo.getColumnModel().getColumn(2).setMaxWidth(0);
		jTableInfo.getColumnModel().getColumn(3).setPreferredWidth(60);
		jTableInfo.getColumnModel().getColumn(3).setMaxWidth(70);
		jTableInfo.getColumnModel().getColumn(4).setPreferredWidth(150);
		jTableInfo.getColumnModel().getColumn(4).setMaxWidth(200);
		jTableInfo.getColumnModel().getColumn(5).setPreferredWidth(150);
		jTableInfo.getColumnModel().getColumn(5).setMaxWidth(200);
		jTableInfo.setRowHeight(26);
		jScrollPane1 = new JScrollPane(jTableInfo);
		jScrollPane1.setPreferredSize(new Dimension(700, 370));
		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));
		mainPanel.add(jScrollPane1,BorderLayout.CENTER);
	}

	//修改
	private void updateButtonActionPerformed(java.awt.event.ActionEvent evt) {
		int[] selectRows = jTableInfo.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要修改的记录！");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view(this, "只能选择一条记录！");
			return;
		}
		String id = StringUtils.ObjToString(this.jTableInfo.getValueAt(selectRows[0], 0));
		String stid = StringUtils.ObjToString(this.jTableInfo.getValueAt(selectRows[0], 1));
		String devid = StringUtils.ObjToString(this.jTableInfo.getValueAt(selectRows[0], 2));

		DevStationTransformerEditDialog aud = new DevStationTransformerEditDialog(this, true,id,stid,devid);
		aud.setVisible(true);
		this.initTable(searchText.getText().trim());
	}

	//新增
	private void addButtonActionPerformed(java.awt.event.ActionEvent evt) {
		DevStationTransformerEditDialog aud = new DevStationTransformerEditDialog(this, true,"","","");
		aud.setVisible(true);
		this.initTable(searchText.getText().trim());
	}
	//查询
	private void searchButtonActionPerformed(java.awt.event.ActionEvent evt) {
		this.initTable(searchText.getText().trim());
	}

	//删除
	private void delButtonActionPerformed(java.awt.event.ActionEvent evt) {
		int[] selectRows = jTableInfo.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要删除的记录！");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view(this, "只能选择一条记录！");
			return;
		}
		String dfsid = this.jTableInfo.getValueAt(selectRows[0], 0).toString();
		
		int isOk=JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "是否确定删除该站用变维护记录？",SystemConstants.SYSTEM_TITLE, JOptionPane.YES_NO_OPTION);
		if(isOk==JOptionPane.NO_OPTION){
			return;
		}
		 String sqlDel="DELETE FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE ID ='"+dfsid+"' ";
		 DBManager.execute(sqlDel);
		
		this.initTable(searchText.getText().trim());
	}
}
