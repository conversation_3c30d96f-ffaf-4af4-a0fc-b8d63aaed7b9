package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.cx.CXGCBHDialog;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXPLDGTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄旁路代供停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			
			String plkgName = "";
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) || dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
						plkgName = CZPService.getService().getDevName(dev);
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						mlkgList.add(dev);					}
				}
			}
			
			if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				if(CXGCBHDialog.isgcbh){
					List<PowerDevice> lineList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice line : lineList){
						List<PowerDevice> linesList = RuleExeUtil.getLineAllSideList(line);
						
						for(PowerDevice dev : linesList){
							PowerDevice station2 = CBSystemConstants.getPowerStation(dev.getPowerStationID());
							String stationName2 = CZPService.getService().getDevName(station2); 
							String deviceName2 = CZPService.getService().getDevName(dev); 
							
							replaceStr += stationName2+"@退出"+deviceName2+"光差保护。/r/n";
						}
					}
				}
				
				replaceStr += voltStationName+"@"+plkgName+"保护定值按照“XXXXXX”定值通知单执行。/r/n";
				replaceStr += voltStationName+"@"+plkgName+"代"+deviceName+"运行，"+deviceName+"由运行转冷备用。/r/n";
				replaceStr += "楚雄地调@"+stationName+deviceName+"停电设备挂牌。/r/n";
			}else{
				boolean isSameMx = false;
				
				List<PowerDevice> plkgMXList = new ArrayList<PowerDevice>();
				List<PowerDevice> curMXList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, null, true, true, true);
				
				List<PowerDevice> mlList = RuleExeUtil.getDeviceList(curDev,null ,SystemConstants.Switch, SystemConstants.Switch, CBSystemConstants.RunTypeSwitchPL,null, false, true, false, true,true);
				if(mlList != null && mlList.size()>0) {
					for(PowerDevice pdDev : mlList) {
						List<PowerDevice> tempList = RuleExeUtil.getDeviceList(pdDev, SystemConstants.MotherLine, null, true, true, true);
						plkgMXList.addAll(tempList);
					}
				}
				
				if(curMXList == null) {
					isSameMx = false;
				}
				
				for(int i=0;i<curMXList.size();i++) {
					if(CBSystemConstants.RunTypeSideMother.equals(curMXList.get(i).getDeviceRunType())) {
						curMXList.remove(i);
					}
				}
				
				for(int i=0;i<plkgMXList.size();i++) {
					if(CBSystemConstants.RunTypeSideMother.equals(plkgMXList.get(i).getDeviceRunType())) {
						plkgMXList.remove(i);
					}
				}
				
				if(plkgMXList.size()>0 && curMXList.size()>0) {
					for(int i=0;i<plkgMXList.size();i++) {
						for(int j=0;j<curMXList.size();j++) {
							if(plkgMXList.get(i).getPowerDeviceID().equals(curMXList.get(j).getPowerDeviceID())) {
								isSameMx = true;
							}
						}
					}
				}
				
				replaceStr += voltStationName+"@"+plkgName+"保护定值按照“XXXXXX”定值通知单执行。/r/n";
				

				if(!isSameMx){
					for(PowerDevice dev : mlkgList){
						if(dev.getDeviceStatus().equals("1")){
							String mlkgName = CZPService.getService().getDevName(dev);
							replaceStr += "楚雄地调@遥控合上"+stationName+mlkgName+"。/r/n";
						}
					}
				}
				
				replaceStr += voltStationName+"@"+plkgName+"代"+deviceName+"运行，"+deviceName+"由运行转冷备用。/r/n";
				
				if(!isSameMx){
					for(PowerDevice dev : mlkgList){
						if(dev.getDeviceStatus().equals("1")){
							String mlkgName = CZPService.getService().getDevName(dev);
							replaceStr += "楚雄地调@遥控断开"+stationName+mlkgName+"。/r/n";
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
