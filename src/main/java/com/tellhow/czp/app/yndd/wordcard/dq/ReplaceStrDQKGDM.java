package com.tellhow.czp.app.yndd.wordcard.dq;


import java.util.Collections;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQKGDM implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("迪庆开关倒母".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			String curMxName = "";
			String otherMxName = "";
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

			for(PowerDevice dev : dzList){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
					
					if(dev.getDeviceStatus().equals("0")){
						for(PowerDevice mx : mxList){
							otherMxName = CZPService.getService().getDevName(mx);
							break;
						}
					}else if(dev.getDeviceStatus().equals("1")){
						for(PowerDevice mx : mxList){
							curMxName = CZPService.getService().getDevName(mx);
							break;
						}
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("1")){
				if(CommonFunctionDQ.ifSwitchControl(curDev)&&CommonFunctionDQ.ifSwitchSeparateControl(curDev)){
					replaceStr += "迪庆地调@遥控用"+stationName+deviceName+"解列/r/n";
					
					replaceStr += "迪庆地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					replaceStr += getKnifeOffCheckContent(dzList, stationName);
					
					String mxName = "";
					
					if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
						
						for(PowerDevice mx : mxList){
							mxName = "于"+CZPService.getService().getDevName(mx);
							break;
						}
					}
					
					replaceStr += "迪庆地调@执行"+stationName+deviceName+"由冷备用转热备用"+mxName+"程序操作/r/n";
					replaceStr += getKnifeOnCheckContent(dzList, stationName);
					
					replaceStr += "迪庆地调@遥控用"+stationName+deviceName+"同期并列/r/n";
				}else{
					replaceStr += stationName+"@将"+deviceName+"由"+curMxName+"热备用倒至"+otherMxName+"热备用/r/n";
				}
			}else if(curDev.getDeviceStatus().equals("0")){
				if(CommonFunctionDQ.ifSwitchControl(curDev)&&CommonFunctionDQ.ifSwitchSeparateControl(curDev)){
					for(PowerDevice dev : mlkgList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
							replaceStr += CommonFunctionDQ.getHhContent(dev, "迪庆地调", stationName);
						}
					}
					
					for(PowerDevice dev : mlkgList){
						replaceStr += stationName+"@确认已将"+CZPService.getService().getDevName(dev)+"设置为死开关/r/n";
					}
					
					replaceStr += "迪庆地调@执行"+stationName+deviceName+"由"+curMxName+"运行倒至"+otherMxName+"运行程序操作/r/n";
				
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"在合上位置/r/n";
						}
					}
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
						}
					}
					
					for(PowerDevice dev : mlkgList){
						replaceStr += stationName+"@确认已解除"+CZPService.getService().getDevName(dev)+"死开关/r/n";
					}
					
					for(PowerDevice dev : mlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += CommonFunctionDQ.getJhContent(dev, "迪庆地调", stationName);
						}
					}
				}else{
					replaceStr += stationName+"@将"+deviceName+"由"+curMxName+"运行倒至"+otherMxName+"运行/r/n";
				}
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
	public String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
			}
		}
		
		return replaceStr;
	}
}
