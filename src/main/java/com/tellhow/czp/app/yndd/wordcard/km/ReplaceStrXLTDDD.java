package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrXLTDDD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("线路停电调电".equals(tempStr)){
			PowerDevice onswDevice = null;
			PowerDevice offswDevice = null;
			
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				if(dtd.getTransDevice().getDeviceType().equals(SystemConstants.Switch)){
					if(dtd.getBeginstatus().equals("1")&&dtd.getEndstate().equals("0")){
						onswDevice=dtd.getTransDevice();
					}else if(dtd.getBeginstatus().equals("0")&&dtd.getEndstate().equals("1")){
						offswDevice=dtd.getTransDevice();
					}
				}
			}
			
			
			if(onswDevice!=null&&offswDevice!=null){
				List<PowerDevice> xlList = RuleExeUtil.getLineAllSideList(curDev);
				for(int i=0;i<xlList.size();i++){
					if(xlList.get(i).getPowerStationID().equals(onswDevice.getPowerStationID())
							||xlList.get(i).getPowerStationID().equals(offswDevice.getPowerStationID())){
						xlList.remove(i);
						i--;
					}
				}
				
				List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> xlsList = new ArrayList<PowerDevice>();

				for(PowerDevice xl:xlList){
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(xl.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							mlkgList.add(dev);
						}
						
						if (dev.getDeviceType().equals(SystemConstants.InOutLine)
								&&!xl.getPowerDeviceID().equals(dev.getPowerDeviceID())&&xl.getPowerVoltGrade() == dev.getPowerVoltGrade()){
							xlsList.add(dev);
						}
					}
				}
				
				RuleExeUtil.swapDeviceList(mlkgList);
				
				if(xlsList.size()>1){
					boolean flag = false;
					
					for(PowerDevice xl : xlsList){
						List<PowerDevice> xlkgList = RuleExeUtil.getLinkedSwitch(xl);
						
						if(xlkgList.size()>0){
							PowerDevice xlkg= xlkgList.get(0);
							
							if(xlkg.getDeviceStatus().equals("0")){
								flag = true;
							}
						}
					}
					
					if(flag){
						for(PowerDevice mlkg : mlkgList){
							String temp = CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(mlkg.getPowerStationID()))
									+"@退出"+(int)mlkg.getPowerVoltGrade()+"kV备自投装置\r\n";
							
							if(!replaceStr.contains(temp)){
								replaceStr+=temp;
							}
						}
					}
				}
				
				if(RuleExeUtil.getDeviceBeginStatus(onswDevice).equals("2")){
					String mbmx = "";
					if(onswDevice.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(onswDevice, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother, false,false, true, true);
						if(mxList.size()>0){
							mbmx = "上"+CZPService.getService().getDevName(mxList.get(0));
						}
					}
					
					replaceStr+=CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(onswDevice.getPowerStationID()))
							+"@将"+CZPService.getService().getDevName(onswDevice)+"由冷备用转热备用"+mbmx+"\r\n";
				}
				replaceStr+=CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(onswDevice.getPowerStationID()))+
						"@落实"+CZPService.getService().getDevName(curDev)+"线路保护及重合闸已投入\r\n";
				
				replaceStr+="昆明地调@遥控断开"
						+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(offswDevice.getPowerStationID()))
						+CZPService.getService().getDevName(offswDevice)+"\r\n";
				
				replaceStr+="昆明地调@遥控合上"
						+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(onswDevice.getPowerStationID()))
						+CZPService.getService().getDevName(onswDevice)+"\r\n";
				
				if(offswDevice.getDeviceStatus().equals("2")){
					replaceStr+=CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(offswDevice.getPowerStationID()))
							+"@将"+CZPService.getService().getDevName(offswDevice)+"由热备用转冷备用\r\n";
				}
				
				
				if(xlsList.size()>1){
					boolean flag = false;
					
					for(PowerDevice xl : xlsList){
						List<PowerDevice> xlkgList = RuleExeUtil.getLinkedSwitch(xl);
						
						if(xlkgList.size()>0){
							PowerDevice xlkg= xlkgList.get(0);
							
							if(xlkg.getDeviceStatus().equals("0")){
								flag = true;
							}
						}
					}
					
					if(flag){
						RuleExeUtil.swapLowDeviceList(mlkgList);
						
						for(PowerDevice mlkg : mlkgList){
							String temp = CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(mlkg.getPowerStationID()))
									+"@投入"+(int)mlkg.getPowerVoltGrade()+"kV备自投装置\r\n";
							
							if(!replaceStr.contains(temp)){
								replaceStr+=temp;
							}
						}
					}
				}
			}
	    }
		return replaceStr;
	}

}
