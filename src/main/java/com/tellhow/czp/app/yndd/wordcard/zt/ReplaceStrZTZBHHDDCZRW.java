package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.HashMap;
import java.util.Iterator;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTZBHHDDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通主变合环调电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			PowerDevice powerdev = new PowerDevice();
			PowerDevice powercutdev = new PowerDevice();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							powercutdev = dev;
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							powerdev = dev;
						}
					}
				}
			}
			
			replaceStr = stationName+deviceName+"由"+CZPService.getService().getDevName(powercutdev)+"供电倒由"+stationName+CZPService.getService().getDevName(powerdev)+"供电";
		}
		
		return replaceStr;
	}

}
