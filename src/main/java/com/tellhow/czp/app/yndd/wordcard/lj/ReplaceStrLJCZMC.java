package com.tellhow.czp.app.yndd.wordcard.lj;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJCZMC  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江厂站名称".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			replaceStr = stationName;
		}
		
		return replaceStr;
	}

}
