package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQKGTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆开关停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
				replaceStr += CommonFunctionDQ.getSwitchOffContent(curDev, stationName ,station);
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				if(CommonFunctionDQ.ifSwitchSeparateControl(curDev)){
					replaceStr += stationName+"@落实"+deviceName+"间隔具备程序化控制条件/r/n";
					replaceStr += "迪庆地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionDQ.getKnifeOffCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
