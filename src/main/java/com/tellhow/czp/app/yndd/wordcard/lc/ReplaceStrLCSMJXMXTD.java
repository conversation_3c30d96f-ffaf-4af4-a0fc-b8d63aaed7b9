package com.tellhow.czp.app.yndd.wordcard.lc;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLC;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLCSMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("临沧双母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			
			replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			
			for(PowerDevice dev : othermxList){
				replaceStr += stationName+"@将"+deviceName+"上运行的所有断路器倒至"+CZPService.getService().getDevName(dev)+"上运行/r/n";
			}
			
			replaceStr += stationName+"@核实"+deviceName+"具备停电条件/r/n";
			replaceStr += stationName+"@核实已断开"+deviceName+"TV二次空气开关/r/n";
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += CommonFunctionLC.getSwitchOffContent(dev, stationName, station);
				}
			}
			
			if(curDev.getDeviceStatus().equals("1")){
				replaceStr += "临沧地调@执行"+stationName+deviceName+"由运行转热备用程序操作/r/n";
			}else if(curDev.getDeviceStatus().equals("2")){
				replaceStr += "临沧地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
				replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(mlkgList, stationName);
				
				List<PowerDevice> tvdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, "", true, true, true, true);
				
				for(PowerDevice tvdz : tvdzList){
					replaceStr += CommonFunctionLC.getSequenceConfirmTdContent(tvdz, stationName);
				}
			}
			
			
//			220kV昔本变	将110kVⅠ母上运行的所有断路器倒至110kVⅡ母上运行
//			220kV昔本变	核实110kVⅠ母具备停电条件
//			220kV昔本变	核实已断开110kV?Ⅰ母TV二次空气开关
//			临沧地调	执行220kV昔本变110kVⅠ母由热备用转冷备用程序操作
//			220kV昔本变	核实110kV母联1121隔离开关在拉开位置
//			220kV昔本变	核实110kV母联1122隔离开关在拉开位置
//			220kV昔本变	核实110kVⅠ母TV1901隔离开关在拉开位置
			
		}
		
		return replaceStr;
	}
}
