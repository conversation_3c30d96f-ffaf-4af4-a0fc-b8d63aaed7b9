package com.tellhow.czp.app.yndd.rule.xsbn;

import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import javax.swing.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

public class XSBNChangePowerExecute implements RulebaseInf {
	EquipCheckChoose ecc =null;

	/*
	 * 合解环设备
	 */
	public static List<PowerDevice> powerOnDeviceList = new ArrayList<PowerDevice>();
	public static List<PowerDevice> powerOffDeviceList = new ArrayList<PowerDevice>();

	/*
	 * 供电线路
	 */
	public static List<PowerDevice> powerOnLineList = new ArrayList<PowerDevice>();
	public static List<PowerDevice> powerOffLineList = new ArrayList<PowerDevice>();

	/*
	 * 供电主变
	 */
	public static List<PowerDevice> powerOnZbList = new ArrayList<PowerDevice>();
	public static List<PowerDevice> powerOffZbList = new ArrayList<PowerDevice>();
	
	// 联络线转电
	public boolean execute(final RuleBaseMode rbm) {
		if(!CBSystemConstants.isCurrentSys)
			return true;
		if(CBSystemConstants.jh_tai == 1)
			return true;
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return true;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return true;
		if(!rbm.getPd().equals(pd))
			return true;

		if(CBSystemConstants.isCurrentSys) {
			powerOnDeviceList.clear();
			powerOffDeviceList.clear();

			powerOnLineList.clear();
			powerOffLineList.clear();
			
			powerOnZbList.clear();
			powerOffZbList.clear();
			
			final List<PowerDevice> loadMap = new ArrayList<PowerDevice>();

			if(!CBSystemConstants.isSame && CBSystemConstants.getSamepdlist().isEmpty()) {
				CBSystemConstants.isSame=true;
				new Thread(new Runnable() {
		   	 	    public void run() {
		   	 	    	ecc = new EquipCheckChoose(SystemConstants.getMainFrame(),
		   	 				false, loadMap, "请选择目标设备", CBSystemConstants.getCurRBM());
		   	 	    	if(ecc.isCancel()){
		   	 	    		SvgUtil.clear();
		   	 	    	}
			   	 	}
				}).start();
				CBSystemConstants.notRollBack =true;
				return false;
			}

			List<PowerDevice> higherKgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
				if(dev.getPowerVoltGrade() > pd.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")){
						higherKgList.add(dev);

						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
						powerOnDeviceList.add(dev);
					}
				}
			}

			for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
				if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
						powerOnDeviceList.add(dev);

						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchML,false,  true, true, true);
							powerOnLineList.addAll(lineList);
						}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
							List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchML,false,  true, true, true);
							powerOnZbList.addAll(zbList);
						}
					}else if(dev.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
						powerOffDeviceList.add(dev);

						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchML,false, true, true, true);
							powerOffLineList.addAll(lineList);
						}if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
							List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchML,false,  true, true, true);
							powerOffZbList.addAll(zbList);
						}
					}
				}
			}

			for(PowerDevice dev : higherKgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				powerOffDeviceList.add(dev);
			}

			PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());

			if(pd.getPowerVoltGrade() == station.getPowerVoltGrade()){
				if(powerOnLineList.size() == 0){
					List<PowerDevice> lineList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();

						if (dev.getDeviceType().equals(SystemConstants.InOutLine)){
							if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
								if(!dev.getPowerDeviceName().contains("备用")){
									if(!powerOffLineList.contains(dev)){
										lineList.add(dev);
									}
								}
							}
						}
					}

					if(lineList.size() == 1){
						for(PowerDevice dev : lineList){
							powerOnLineList.add(dev);
						}
					}else{
						czprule.rule.view.EquipCheckChoose ecc=new czprule.rule.view.EquipCheckChoose(SystemConstants.getMainFrame(), true, lineList , "请选择操作后供电的线路：");
						List<PowerDevice> chooseList = ecc.getChooseEquip();

						if(ecc.isCancel()){
							SvgUtil.clear();
							return false;
						}

						for(PowerDevice dev : chooseList){
							powerOnLineList.add(dev);
						}
					}
				}else if(powerOffLineList.size() == 0){
					List<PowerDevice> lineList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();

						if (dev.getDeviceType().equals(SystemConstants.InOutLine)){
							if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
								if(!dev.getPowerDeviceName().contains("备用")){
									if(!powerOnLineList.contains(dev)){
										lineList.add(dev);
									}
								}
							}
						}
					}

					if(lineList.size() == 1){
						for(PowerDevice dev : lineList){
							powerOffLineList.add(dev);
						}
					}else{
						czprule.rule.view.EquipCheckChoose ecc = new czprule.rule.view.EquipCheckChoose(SystemConstants.getMainFrame(), true, lineList , "请选择操作前供电的线路：");
						List<PowerDevice> chooseList = ecc.getChooseEquip();

						if(ecc.isCancel()){
							SvgUtil.clear();
							return false;
						}

						for(PowerDevice dev : chooseList){
							powerOffLineList.add(dev);
						}
					}
				}
			}else{
				if(powerOnZbList.size() == 0){
					List<PowerDevice> zbList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();

						if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							if(!dev.getPowerDeviceName().contains("备用")){
								if(!powerOffZbList.contains(dev)){
									zbList.add(dev);
								}
							}
						}
					}

					if(zbList.size() == 1){
						for(PowerDevice dev : zbList){
							powerOnZbList.add(dev);
						}
					}else{
						czprule.rule.view.EquipCheckChoose ecc=new czprule.rule.view.EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList , "请选择操作后供电的主变：");
						List<PowerDevice> chooseList = ecc.getChooseEquip();

						if(ecc.isCancel()){
							SvgUtil.clear();
							return false;
						}

						for(PowerDevice dev : chooseList){
							powerOnZbList.add(dev);
						}
					}
				}else if(powerOffZbList.size() == 0){
					List<PowerDevice> zbList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();

						if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							if(!dev.getPowerDeviceName().contains("备用")){
								if(!powerOnZbList.contains(dev)){
									zbList.add(dev);
								}
							}
						}
					}

					if(zbList.size() == 1){
						for(PowerDevice dev : zbList){
							powerOffZbList.add(dev);
						}
					}else{
						czprule.rule.view.EquipCheckChoose ecc = new czprule.rule.view.EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList , "请选择操作前供电的主变：");
						List<PowerDevice> chooseList = ecc.getChooseEquip();

						if(ecc.isCancel()){
							SvgUtil.clear();
							return false;
						}

						for(PowerDevice dev : chooseList){
							powerOffZbList.add(dev);
						}
					}
				}
			}
		}
		return true;
	}

	// 新增一个方法来继续执行后续步骤
	private boolean continueExecution(RuleBaseMode rbm, boolean dialogConfirmed) {
		if (!dialogConfirmed) {
			// 对话框未确认，清理相关状态
			CBSystemConstants.isSame = false;
			return false;
		}

		// 对话框已确认，继续原来的 execute 逻辑
		List<PowerDevice> higherKgList = new ArrayList<PowerDevice>();
		PowerDevice pd = rbm.getPd();

		for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
			if(dev.getPowerVoltGrade() > pd.getPowerVoltGrade()){
				if(dev.getDeviceStatus().equals("1")){
					higherKgList.add(dev);

					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					powerOnDeviceList.add(dev);
				}
			}
		}

		for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
			if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
				if(dev.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					powerOnDeviceList.add(dev);

					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchML,false,  true, true, true);
						powerOnLineList.addAll(lineList);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
						List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchML,false,  true, true, true);
						powerOnZbList.addAll(zbList);
					}
				}else if(dev.getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					powerOffDeviceList.add(dev);

					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchML,false, true, true, true);
						powerOffLineList.addAll(lineList);
					}if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
						List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchML,false,  true, true, true);
						powerOffZbList.addAll(zbList);
					}
				}
			}
		}

		for(PowerDevice dev : higherKgList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			powerOffDeviceList.add(dev);
		}

		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());

		if(pd.getPowerVoltGrade() == station.getPowerVoltGrade()){
			if(powerOnLineList.size() == 0){
				List<PowerDevice> lineList = new ArrayList<PowerDevice>();

				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();

					if (dev.getDeviceType().equals(SystemConstants.InOutLine)){
						if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
							if(!dev.getPowerDeviceName().contains("备用")){
								if(!powerOffLineList.contains(dev)){
									lineList.add(dev);
								}
							}
						}
					}
				}

				if(lineList.size() == 1){
					for(PowerDevice dev : lineList){
						powerOnLineList.add(dev);
					}
				}else{
					czprule.rule.view.EquipCheckChoose ecc=new czprule.rule.view.EquipCheckChoose(SystemConstants.getMainFrame(), true, lineList , "请选择操作后供电的线路：");
					List<PowerDevice> chooseList = ecc.getChooseEquip();

					if(ecc.isCancel()){
						SvgUtil.clear();
						return false;
					}

					for(PowerDevice dev : chooseList){
						powerOnLineList.add(dev);
					}
				}
			}else if(powerOffLineList.size() == 0){
				List<PowerDevice> lineList = new ArrayList<PowerDevice>();

				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();

					if (dev.getDeviceType().equals(SystemConstants.InOutLine)){
						if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
							if(!dev.getPowerDeviceName().contains("备用")){
								if(!powerOnLineList.contains(dev)){
									lineList.add(dev);
								}
							}
						}
					}
				}

				if(lineList.size() == 1){
					for(PowerDevice dev : lineList){
						powerOffLineList.add(dev);
					}
				}else{
					czprule.rule.view.EquipCheckChoose ecc = new czprule.rule.view.EquipCheckChoose(SystemConstants.getMainFrame(), true, lineList , "请选择操作前供电的线路：");
					List<PowerDevice> chooseList = ecc.getChooseEquip();

					if(ecc.isCancel()){
						SvgUtil.clear();
						return false;
					}

					for(PowerDevice dev : chooseList){
						powerOffLineList.add(dev);
					}
				}
			}
		}else{
			if(powerOnZbList.size() == 0){
				List<PowerDevice> zbList = new ArrayList<PowerDevice>();

				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();

					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						if(!dev.getPowerDeviceName().contains("备用")){
							if(!powerOffZbList.contains(dev)){
								zbList.add(dev);
							}
						}
					}
				}

				if(zbList.size() == 1){
					for(PowerDevice dev : zbList){
						powerOnZbList.add(dev);
					}
				}else{
					czprule.rule.view.EquipCheckChoose ecc=new czprule.rule.view.EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList , "请选择操作后供电的主变：");
					List<PowerDevice> chooseList = ecc.getChooseEquip();

					if(ecc.isCancel()){
						SvgUtil.clear();
						return false;
					}

					for(PowerDevice dev : chooseList){
						powerOnZbList.add(dev);
					}
				}
			}else if(powerOffZbList.size() == 0){
				List<PowerDevice> zbList = new ArrayList<PowerDevice>();

				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();

					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						if(!dev.getPowerDeviceName().contains("备用")){
							if(!powerOnZbList.contains(dev)){
								zbList.add(dev);
							}
						}
					}
				}

				if(zbList.size() == 1){
					for(PowerDevice dev : zbList){
						powerOffZbList.add(dev);
					}
				}else{
					czprule.rule.view.EquipCheckChoose ecc = new czprule.rule.view.EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList , "请选择操作前供电的主变：");
					List<PowerDevice> chooseList = ecc.getChooseEquip();

					if(ecc.isCancel()){
						SvgUtil.clear();
						return false;
					}

					for(PowerDevice dev : chooseList){
						powerOffZbList.add(dev);
					}
				}
			}
		}

		// ... 后面的所有代码 ...
		return true;
	}

	// 用于保存对话框结果的辅助类
	private static class DialogResult {
		EquipCheckChoose ecc;
		boolean canceled;
		boolean confirmed;
	}
}
