package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.HashMap;
import java.util.Iterator;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;
public class ReplaceStrBZTDDDCZRW implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";

		if ("备自投调电低操作任务".equals(tempStr)) {
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			PowerDevice zb = new PowerDevice();
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zb = dev;
					break;
				}
			}
			
			String midvolt = StringUtils.ObjToString((int)RuleExeUtil.getTransformerVolByType(zb, "middle"));
			String lowvolt = StringUtils.ObjToString((int)RuleExeUtil.getTransformerVolByType(zb, "low"));

			String volt = "";
			
			if(midvolt.equals("0")){
				midvolt = "";
			}
			
			if(lowvolt.equals("0")){
				lowvolt = "";
			}
			
			if(!midvolt.equals("")&&!lowvolt.equals("")){
				volt = midvolt+"kV、"+lowvolt+"kV";
			}else if(!midvolt.equals("")&&lowvolt.equals("")){
				volt = midvolt+"kV";
			}else if(midvolt.equals("")&&!lowvolt.equals("")){
				volt = lowvolt+"kV";
			}
			
			replaceStr += CZPService.getService().getDevName(station)+volt+"备自投调电/r/n";
		}
		
		return replaceStr;
	}
}
