package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMXFDHSNR implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("母线复电核实内容".equals(tempStr)){
			if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				
				if(mlkgList.size()>0){
					replaceStr = "核实"+CZPService.getService().getDevName(curDev)+"及母线设备、"+CZPService.getService().getDevName(mlkgList.get(0))+"冷备用";
				}
				
			}else if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				
			}
		}
		System.out.println(replaceStr);
		return replaceStr;
	}

}
