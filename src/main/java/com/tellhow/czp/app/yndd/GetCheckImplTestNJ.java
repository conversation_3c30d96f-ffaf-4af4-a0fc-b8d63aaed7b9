package com.tellhow.czp.app.yndd;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.service.CheckCzpImpl;
import com.tellhow.czp.service.CheckStatusImpl;
import com.tellhow.graphicframework.startup.StartupManager;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class GetCheckImplTestNJ {
    public static void main(String[] params) {
	    CheckCzpImpl check = new CheckCzpImpl();
	    CheckStatusImpl checkback = new CheckStatusImpl();
	
	    String param = "";
	
		StartupManager.startup();
		CZPService.getService().setArg(param);
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开110kV倪家营变110kV果松倪吴Ⅰ回181断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>220kV青山变</changzhan><caozuozhiling>将110kVⅠ母上运行的所有断路器倒至110kVⅡ母上运行</caozuozhiling><cbid>4C795159-5464694BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>昆明地调</changzhan><caozuozhiling>遥控断开220kV青山变110kVⅠ-Ⅱ母母联112断路器</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>220kV青山变</changzhan><caozuozhiling>将110kVⅠ母由热备用转冷备用</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F13123183592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>500kV草铺变</changzhan><caozuozhiling>将35kVⅣ母由热备用转冷备用</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>怒江地调</changzhan><caozuozhiling>执行220kV金岭变220kV金崇线262断路器由热备用转冷备用程序操作</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB998F83592</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>大理地调</changzhan><caozuozhiling>确认220kV福剑线220kV剑川变侧已处热备用状态</caozuozhiling><cbid>4C795159-94BF-41F8-804A-4AB9954546</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		check.execute(param);
    }
}
