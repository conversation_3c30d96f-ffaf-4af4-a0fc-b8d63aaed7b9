package com.tellhow.czp.app.yndd.wordcard.hh;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHH23JXMXFD implements TempStringReplace {

    @Override
    public String strReplace(String tempStr, PowerDevice curDev,
                             PowerDevice stationDev, String desc) {
        String replaceStr = "";

        if ("红河二分之三接线母线复电".equals(tempStr)) {
            PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
            String stationName = CZPService.getService().getDevName(station);
            String deviceName = CZPService.getService().getDevName(curDev);
			
			/*List<PowerDevice> kgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

			RuleExeUtil.swapDeviceListNum(kgList);
			Collections.reverse(kgList);*/
            String deviceStatus = RuleExeUtil.getStatus(RuleExeUtil.getDeviceEndStatus(curDev));

            replaceStr += stationName + "@确认" + deviceName + "相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，" +
                    "现场自行操（装设）的接地开关（接地线）已全部拉开（拆除），" + deviceName + "的二次装置已正常投入，" +
                    "现" + deviceName + deviceStatus + "，具备送电条件/r/n";
            replaceStr += stationName + "@确认" + deviceName + "保护及相关断路器重合闸方式已按运行操作手册要求调整/r/n";

            if (RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")) {
                replaceStr += "红河地调@执行" + stationName + deviceName + "由冷备用转热备用程序操作/r/n";
            }

            replaceStr += "红河地调@执行" + stationName + deviceName + "由热备用转运行程序操作/r/n";

            replaceStr += stationName + "@确认" + deviceName + "一、二次设备运行正常/r/n";

        }

        return replaceStr;
    }

}
