/**

 **/
package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.sun.java.help.search.Rule;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.rule.view.EquipChoose;
import czprule.rule.view.LineTransChooseDialog;
import czprule.system.CBSystemConstants;
import czprule.system.DeviceSVGPanelUtil;

public class KGBZTDDGExecute implements RulebaseInf {//开关备自投调电高
	/**
	 * 选择执行满足输入条件的开关  输入条件（变电站类型，设备运行类型，初始状态，执行动作）
	 */
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;

		Map<PowerDevice,String> otherStationMap = new HashMap<PowerDevice, String>();
		
		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
		PowerDevice dycstation = new PowerDevice();
		
		List<PowerDevice>  dycxlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice>  fhclineList = new ArrayList<PowerDevice>();
		List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false,true, true);

		if(lineList.size()>0){
			//打开线路关联的变电站接线图
			String filePath = "";
			if(CBSystemConstants.isCurrentSys) {
				filePath = SystemConstants.getGuiBuilder().getActivateSVGPanel().getFilePath();
			}
			
			Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineByLine(lineList.get(0));

			PowerDevice dev=null; //变电站对象
			List<PowerDevice> trans=new ArrayList<PowerDevice>();
			for(PowerDevice pdNew:stationlines.keySet()){
				dev=pdNew;
				
				trans.add(dev);
				if(CBSystemConstants.isCurrentSys) {
					DeviceSVGPanelUtil.openSVGPanel(dev.getPowerStationID(), dev.getPowerDeviceID());
				}
			}
			
			if(CBSystemConstants.isCurrentSys && !"".equals(pd.getPowerStationName())) //如果是在站内操作线路，打开对侧变电站后还要回到当前变电站，如果是全网图线路则不管
			     SystemConstants.getGuiBuilder().activateTabbedPageByName(filePath);
			
			List<PowerDevice> otherlineList = RuleExeUtil.getLineOtherSideList(lineList.get(0));
			List<PowerDevice> alllineList = RuleExeUtil.getLineAllSideList(lineList.get(0));

			for(PowerDevice otherline : otherlineList){
				PowerDevice otherstation = CBSystemConstants.getPowerStation(otherline.getPowerStationID());

				if(otherstation.getPowerVoltGrade() > station.getPowerVoltGrade()){
					otherStationMap.put(otherstation,"");
				}
			}
			
			if(otherStationMap.size()>1){
				LineTransChooseDialog linetransChoose=new LineTransChooseDialog(SystemConstants.getMainFrame(), true, otherStationMap, "选择【"+CZPService.getService().getDevName(pd)+"】的电源侧");
				PowerDevice checkDev = linetransChoose.getReslut();
				
				if(checkDev == null){
					return false;
				}
				
				dycstation = CBSystemConstants.getPowerStation(checkDev.getPowerDeviceID());
				
				for(PowerDevice otherline : otherlineList){
					if(checkDev.getPowerDeviceID().equals(otherline.getPowerStationID())){
						dycxlkgList = RuleExeUtil.getDeviceList(otherline, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true);
						
						if(dycxlkgList.size()>0){
							for(PowerDevice xlkg : dycxlkgList){
								if(xlkg.getDeviceStatus().equals("0")){
									RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "1");
								}
							}
						}
					}
				}
			}else{
				for(PowerDevice otherline : otherlineList){
					List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(otherline, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true);
					
					if(xlkgList.size()>0){
						for(PowerDevice xlkg : xlkgList){
							PowerDevice stationTemp = CBSystemConstants.getPowerStation(xlkg.getPowerStationID());

							if(otherStationMap.containsKey(stationTemp)){//电源侧
								dycxlkgList.add(xlkg);
								
								dycstation = stationTemp;
								
								if(xlkg.getDeviceStatus().equals("0")){
									RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "1");
								}
							}
						}
					}
				}
			}
			
			for(Iterator<PowerDevice> itor = alllineList.iterator();itor.hasNext();){
				PowerDevice line = itor.next();
				
				if(!dycstation.getPowerDeviceID().equals(line.getPowerStationID())){
					fhclineList.add(line);
				}
			}
		}
		
		if(fhclineList.size()>0){
			for(PowerDevice fhcline: fhclineList){
				PowerDevice curzb = new PowerDevice();
				
				HashMap<String, PowerDevice> fhcmapStationDevice = CBSystemConstants.getStationPowerDevices(fhcline.getPowerStationID());
				
				for (Iterator<PowerDevice> it2 = fhcmapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						curzb = dev;
					}
				}
				
				if(RuleExeUtil.isTransformerXBZ(curzb)){
					//线变组接线
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(fhcline, SystemConstants.PowerTransformer, "", true,false, true);

					if(zbList.size()>0){
						for(PowerDevice zb : zbList){
							List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(zb);
							
							if(zbzyckgList.size()>0){
								for(PowerDevice zbzyckg : zbzyckgList){
									RuleExeUtil.deviceStatusExecute(zbzyckg, zbzyckg.getDeviceStatus(), "1");
									
									List<PowerDevice>  fdswList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);

									if(fdswList.size()>0){
										RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
									}
								}
							}
							
							List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zb);

							if(zbdyckgList.size()>0){
								for(PowerDevice zbdyckg : zbdyckgList){
									RuleExeUtil.deviceStatusExecute(zbdyckg, zbdyckg.getDeviceStatus(), "1");
								}

								for(PowerDevice zbdyckg : zbdyckgList){
									List<PowerDevice>  fdswList = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,  CBSystemConstants.RunTypeSwitchFHC, false, true, false, true);

									if(fdswList.size()>0){
										if(fdswList.get(0).getDeviceStatus().equals("1")){
											RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
										}else{
											fdswList = RuleExeUtil.getDeviceList(fdswList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);
											
											if(fdswList.size()>0){
												if(fdswList.get(0).getDeviceStatus().equals("1")){
													RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
												}else{
													fdswList = RuleExeUtil.getDeviceList(fdswList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, true, true);

													if(fdswList.size()>0){
														if(fdswList.get(0).getDeviceStatus().equals("1")){
															RuleExeUtil.deviceStatusExecute(fdswList.get(0), fdswList.get(0).getDeviceStatus(), "0");
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}else{
					List<PowerDevice>  curxlkgList = RuleExeUtil.getDeviceList(fhcline, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true);
					
					if(curxlkgList.size()>0){
						RuleExeUtil.deviceStatusExecute(curxlkgList.get(0), curxlkgList.get(0).getDeviceStatus(), "1");
					}
				}
			}
			
			for(PowerDevice fhcline: fhclineList){
				List<PowerDevice> curxlkgList = RuleExeUtil.getDeviceList(fhcline, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, null, false, true, true, true);
				
				List<PowerDevice> fdswList = RuleExeUtil.getDeviceList(curxlkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, false);
				List<PowerDevice> otherrbyxlkgList = new ArrayList<PowerDevice>();

				if(fdswList.size()>0){
					for(PowerDevice fdsw : fdswList){
						if(fdsw.getDeviceStatus().equals("1")){
							RuleExeUtil.deviceStatusExecute(fdsw, fdsw.getDeviceStatus(), "0");
						}else{
							List<PowerDevice> curmxList = RuleExeUtil.getDeviceList(curxlkgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, false,true, true);
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(fdsw, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false,true, true);
							
							if(mxList.size()>0){
								PowerDevice othermx =  new PowerDevice();

								for(Iterator<PowerDevice> itor = mxList.iterator();itor.hasNext();){
									PowerDevice mx = itor.next();
									
									if(!curmxList.contains(mx)){
										othermx = mx;
										break;
									}
								}
								
								List<PowerDevice> otherxlkgList = RuleExeUtil.getDeviceList(othermx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true);
								
								if(otherxlkgList.size()>0){
									for(Iterator<PowerDevice> itor = otherxlkgList.iterator();itor.hasNext();){
										PowerDevice otherxlkg = itor.next();
										
										if(otherxlkg.getDeviceStatus().equals("1")){
											otherrbyxlkgList.add(otherxlkg);
										}
									}
								}
								
								if(otherrbyxlkgList.size()>1){
									EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, otherrbyxlkgList , "请选择需要合上的线路断路器：");
									List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

									if(choosedyckgList.size()==0){
										return false;
									}
									
									for(PowerDevice choosedev:choosedyckgList){
										RuleExeUtil.deviceStatusExecute(choosedev, choosedev.getDeviceStatus(), "0");
									}
								}else if(otherrbyxlkgList.size() == 1){
									for(PowerDevice otherrbyxlkg : otherrbyxlkgList){
										RuleExeUtil.deviceStatusExecute(otherrbyxlkg, otherrbyxlkg.getDeviceStatus(), "0");
									}
								}
							}
						}
					}
				}else{
					List<PowerDevice> otherxlkgList = RuleExeUtil.getDeviceList(curxlkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, null, false, true, false, true);

					if(otherxlkgList.size()>0){
						for(Iterator<PowerDevice> itor = otherxlkgList.iterator();itor.hasNext();){
							PowerDevice otherxlkg = itor.next();
							
							if(otherxlkg.getDeviceStatus().equals("1")){
								otherrbyxlkgList.add(otherxlkg);
							}
						}
					}
					
					if(otherrbyxlkgList.size()>1){
						EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, otherrbyxlkgList , "请选择需要合上的线路断路器：");
						List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

						if(choosedyckgList.size()==0){
							return false;
						}
						
						for(PowerDevice choosedev:choosedyckgList){
							RuleExeUtil.deviceStatusExecute(choosedev, choosedev.getDeviceStatus(), "0");
						}
					}else if(otherrbyxlkgList.size() == 1){
						for(PowerDevice otherrbyxlkg : otherrbyxlkgList){
							RuleExeUtil.deviceStatusExecute(otherrbyxlkg, otherrbyxlkg.getDeviceStatus(), "0");
						}
					}
				}
			}
		}
		
		if(dycxlkgList.size()>0){
			for(PowerDevice xlkg : dycxlkgList){
				if(xlkg.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "0");
				}
			}
		}
		
		return true;
	}

}
