package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.lj.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJKGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江开关复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			dzList = RuleExeUtil.sortByXLC(dzList);
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("1")){
					if(TicketKindChoose.flag.equals("部分程序化")){
						if(CommonFunctionLJ.ifSwitchSeparateControl(curDev)){
							replaceStr += "丽江地调@执行"+stationName+CZPService.getService().getDevName(curDev)+"由冷备用转热备用程序操作/r/n";
							
							for(PowerDevice dev : dzList){
								replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合上位置/r/n";
							}
						}else{
							replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
						}
					}else if(TicketKindChoose.flag.equals("全部程序化")){
						replaceStr += "丽江地调@执行"+stationName+CZPService.getService().getDevName(curDev)+"由冷备用转热备用程序操作/r/n";
						
						for(PowerDevice dev : dzList){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合上位置/r/n";
						}
					}else{
						replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
					}
				}else if(RuleExeUtil.getDeviceEndStatus(curDev).equals("0")){
					if(TicketKindChoose.flag.equals("部分程序化")){
						if(CommonFunctionLJ.ifSwitchSeparateControl(curDev)){
							replaceStr += "丽江地调@执行"+stationName+CZPService.getService().getDevName(curDev)+"由冷备用转热备用程序操作/r/n";
							
							for(PowerDevice dev : dzList){
								replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合上位置/r/n";
							}
						}else{
							replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
						}
						
						if(CommonFunctionLJ.ifSwitchControl(curDev)){
							replaceStr += "丽江地调@执行"+stationName+CZPService.getService().getDevName(curDev)+"由热备用转运行程序操作/r/n";
						}else{
							replaceStr += "丽江地调@遥控合上"+stationName+deviceName+"/r/n";
						}
					}else if(TicketKindChoose.flag.equals("全部程序化")){
						replaceStr += "丽江地调@执行"+stationName+CZPService.getService().getDevName(curDev)+"由冷备用转运行程序操作/r/n";
						
						for(PowerDevice dev : dzList){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合上位置/r/n";
						}
					}else{
						replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
						replaceStr += "丽江地调@遥控合上"+stationName+deviceName+"/r/n";
					}
				}
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				if(TicketKindChoose.flag.equals("部分程序化")){
					if(CommonFunctionLJ.ifSwitchControl(curDev)){
						replaceStr += "丽江地调@执行"+stationName+CZPService.getService().getDevName(curDev)+"由热备用转运行程序操作/r/n";
					}else{
						replaceStr += "丽江地调@遥控合上"+stationName+deviceName+"/r/n";
					}
				}else if(TicketKindChoose.flag.equals("全部程序化")){
					replaceStr += "丽江地调@执行"+stationName+CZPService.getService().getDevName(curDev)+"由热备用转运行程序操作/r/n";
				}else{
					replaceStr += "丽江地调@遥控合上"+stationName+deviceName+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
