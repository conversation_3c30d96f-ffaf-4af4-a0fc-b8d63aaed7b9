package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.List;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

public class OrganLineRelDialog extends JDialog {
	private DefaultTableModel dTableModel;
	private String search = "";
	public OrganLineRelDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("线路运维站维护");
		setLocationCenter();
		this.setSize(1000, 500);
		initTable(search);
	}

	/**
	 * 初始化表格
	 */
	public void initTable(String search) {
		dTableModel = new DefaultTableModel(null, new String[] {"序号", "线路ID", "线路名称" ,"所属运维站名称"});
		String sql = "";
		if(search.equals("")){
			sql = "SELECT ID,LINEID,LINENAME,STATIONNAME FROM "+CBSystemConstants.opcardUser+"T_A_LINEYWSTATION";
		}
		else if(!search.equals("")){
			sql = "SELECT ID,LINEID,LINENAME,STATIONNAME FROM "+CBSystemConstants.opcardUser+"T_A_LINEYWSTATION WHERE LINENAME LIKE '%"+search+"%'";
		}
		
		sql += " ORDER BY TO_NUMBER(ID)";
		
		List<Map<String,String>> dataLists=DBManager.queryForList(sql);
		
		for(Map<String,String> map : dataLists) {
			String id = StringUtils.ObjToString(map.get("ID"));
			String lineId = StringUtils.ObjToString(map.get("LINEID"));
			String lineName = StringUtils.ObjToString(map.get("LINENAME"));
			String lineOrganName = StringUtils.ObjToString(map.get("STATIONNAME"));
			
		 	dTableModel.addRow(new Object[]{id,lineId,lineName,lineOrganName});
		}
		
		 jTable1.setModel(dTableModel);
		
		JTableHeader tableHeader = jTable1.getTableHeader();
		tableHeader.setReorderingAllowed(false); // 设置表格列不可重排
		DefaultTableCellRenderer hr = (DefaultTableCellRenderer) tableHeader.getDefaultRenderer(); // 获得表格头的单元格对象
		hr.setHorizontalAlignment(DefaultTableCellRenderer.CENTER);
		
		jTable1.getColumnModel().getColumn(0).setMinWidth(0);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(0).setPreferredWidth(0);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2-200,
				(h - this.getSize().height) / 2);
	}

	private void initComponents() {
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		getContentPane().setLayout(new BorderLayout(0, 0));
		
		panel = new JPanel();
		panel.setSize(1000, 100);
		getContentPane().add(panel, BorderLayout.NORTH);
		
		JButton editButton = new JButton("编辑");
		editButton.setSize(200, 100);
		editButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jComboBox1ActionPerformed(evt);
            }

			private void jComboBox1ActionPerformed(ActionEvent evt) {
				int[] SelectRow = jTable1.getSelectedRows();
				
				if(SelectRow.length == 0){
					ShowMessage.view("请先选中其中一项！");
					return ;
				}
				
				String id = StringUtils.ObjToString(jTable1.getValueAt(SelectRow[0], 0));
				String lineId = StringUtils.ObjToString(jTable1.getValueAt(SelectRow[0], 1));
				String lineName = StringUtils.ObjToString(jTable1.getValueAt(SelectRow[0], 2));
				String lineOrganName = StringUtils.ObjToString(jTable1.getValueAt(SelectRow[0], 3));
				
				OrganLineRelEditDialog SEedit = new OrganLineRelEditDialog(SystemConstants.getMainFrame(), true, id,lineId,lineName,lineOrganName,"编辑");
				SEedit.setVisible(true);
				
				initTable(search);
			}
        });
		panel.setLayout(new FlowLayout(FlowLayout.RIGHT, 5, 5));
		
		final JTextField textField = new JTextField();
		panel.add(textField);
		textField.setColumns(20);
		
		JButton searchButton = new JButton("搜索");
		searchButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jComboBox1ActionPerformed(evt);
            }

			private void jComboBox1ActionPerformed(ActionEvent evt) {
				search = textField.getText();
				initTable(search);
				search = "";
				textField.setText("");
			}
        });
		panel.add(searchButton);
		
		JButton addButton = new JButton("新增");
		addButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jComboBox1ActionPerformed(evt);
            }

			private void jComboBox1ActionPerformed(ActionEvent evt) {
				OrganLineRelEditDialog SEedit = new OrganLineRelEditDialog(SystemConstants.getMainFrame(), true, "" ,"","","","新增");
				SEedit.setVisible(true);
				
				initTable(search);
			}
        });
		
		JButton deleteButton = new JButton("删除");
		
		deleteButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                deleteActionPerformed(evt);
            }

        });
		
		panel.add(addButton);
		panel.add(editButton);
		panel.add(deleteButton);

		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));

		jTable1.setFont(new java.awt.Font("宋体", 0, 13));
		jTable1.setModel(new DefaultTableModel(
			new Object[][] {},
			new String[] {"\u4E8C\u6B21\u8BBE\u5907\u540D\u79F0", "\u4E8C\u6B21\u8BBE\u5907\u7C7B\u578B", "\u4E8C\u6B21\u8BBE\u5907\u72B6\u6001", "\u72B6\u6001\u4FEE\u6539\u65F6\u95F4"
			}
		));
		
		jTable1.setRowHeight(30);
		jScrollPane1.setViewportView(jTable1);
		getContentPane().add(jScrollPane1, BorderLayout.CENTER);
		
		pack();
	}

	private void deleteActionPerformed(ActionEvent evt) {
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view("请选择需要删除的记录!");
			return;
		}
		int ok = JOptionPane.showConfirmDialog(this, "删除后不能恢复，你确定要删除吗？",
				"操作票提示框", JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.NO_OPTION) {
			return;
		}
		
		String id = StringUtils.ObjToString(jTable1.getValueAt(selectRows[0], 0));
		
		String sql = "DELETE FROM "+CBSystemConstants.opcardUser+"T_A_LINEYWSTATION WHERE ID = '"+id+"'";
		DBManager.execute(sql);
		
		initTable("");
	}
	
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTable1;
	private JPanel panel;
}
