package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.SwitchLoadSideMotherLine;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKMKGDPL implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";
 
		if("昆明开关倒旁路".equals(tempStr)) {
			String result = StringUtils.ObjToString(SwitchLoadSideMotherLine.tagMap.get("是否合环"));
			PowerDevice dkkg = CBSystemConstants.getPowerDevice(SwitchLoadSideMotherLine.tagMap.get("断开开关"));
			
			List<PowerDevice> lineList = RuleExeUtil.getSwitchLoadLine(curDev);
			
		 	List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			
		 	PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			PowerDevice gycmlkg = new PowerDevice();
			PowerDevice plkg = new PowerDevice();
			List<PowerDevice> bztkgList = new ArrayList<PowerDevice>();
			List<String> bztVoltList = new ArrayList<String>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
				
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						gycmlkg = dev;
					}
				}
				
				if(dev.getPowerVoltGrade() < station.getPowerVoltGrade()&&dev.getPowerVoltGrade()>=10){
					String volt = String.valueOf((int)dev.getPowerVoltGrade());
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&!bztVoltList.contains(volt)){
						bztkgList.add(dev);
						bztVoltList.add(volt);
					}
				}
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)){
						plkg = dev;
					}
				}
			}
			
			ReplaceBooBDGDLQLBY lby = new ReplaceBooBDGDLQLBY();
 			ReplaceBooBDGDLQRBY rby = new ReplaceBooBDGDLQRBY();
 			boolean islby = lby.strReplace("被代供断路器冷备用", curDev, stationDev, "");
 			boolean isrby = rby.strReplace("被代供断路器热备用", curDev, stationDev, "");
			String plkgName = CZPService.getService().getDevName(plkg);
		 	
			if(result.equals("能合环")){
				replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
				
 				replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
	 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
 				
	 			for(PowerDevice mlkg : mlkgList){
	 				replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
	 			}
	 			
	 			replaceStr += "将"+plkgName+"保护定值切换至代供"+CZPService.getService().getDevName(lineList)+"保护定值区/r/n";
	 			replaceStr += "将"+CZPService.getService().getDevName(lineList)+"调由"+plkgName+"代供/r/n";

	 			if(islby){
		 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由运行转冷备用/r/n";
	 			}else if(isrby){
		 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由运行转热备用/r/n";
	 			}
	 			
	 			for(PowerDevice mlkg : mlkgList){
	 				replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
	 			}
	 			
	 			if(RuleExeUtil.getDeviceEndStatus(dkkg).equals("0")){
		 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
	 			}
	 			
	 			if(RuleExeUtil.getDeviceEndStatus(gycmlkg).equals("1")){
		 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
	 			}
			}else if(result.equals("不能合环")){
				if(bztVoltList.size()>0){//多段母线
					for(String volt : bztVoltList){
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();

						for(PowerDevice bztkg : bztkgList){
							String bztkgvolt = String.valueOf((int)bztkg.getPowerVoltGrade());		
							
							if(volt.equals(bztkgvolt)){
								tempList.add(bztkg);
							}
						}
						
						if(tempList.size()>1){
							for(PowerDevice temp : tempList){
					 			replaceStr += "退出"+CZPService.getService().getDevName(temp)+"备自投装置/r/n";
							}
						}else if(tempList.size()==1){
				 			replaceStr += "退出"+volt+"kV备自投装置/r/n";
						}
					}
				}
			
	 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
 				replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
 				
	 			for(PowerDevice mlkg : mlkgList){
	 				replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
	 			}
	 			
	 			if(bztVoltList.size()>0){//多段母线
					for(String volt : bztVoltList){
						if(volt.equals("10")){
							List<PowerDevice> tempList = new ArrayList<PowerDevice>();

							for(PowerDevice bztkg : bztkgList){
								String bztkgvolt = String.valueOf((int)bztkg.getPowerVoltGrade());		
								
								if(volt.equals(bztkgvolt)){
									tempList.add(bztkg);
								}
							}
							
							if(tempList.size()>1){
								for(PowerDevice temp : tempList){
						 			replaceStr += "投入"+CZPService.getService().getDevName(temp)+"备自投装置/r/n";
								}
							}else if(tempList.size()==1){
					 			replaceStr += "投入"+volt+"kV备自投装置/r/n";
							}
						}
					}
				}
	 			
	 			replaceStr += "将"+plkgName+"保护定值切换至代供"+CZPService.getService().getDevName(lineList)+"保护定值区/r/n";
	 			replaceStr += "将"+CZPService.getService().getDevName(lineList)+"调由"+plkgName+"代供/r/n";
	 			
	 			if(islby){
		 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由运行转冷备用/r/n";
	 			}else if(isrby){
		 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由运行转热备用/r/n";
	 			}
	 			
	 			if(bztVoltList.size()>0){//多段母线
					for(String volt : bztVoltList){
						if(!volt.equals("10")){
							List<PowerDevice> tempList = new ArrayList<PowerDevice>();

							for(PowerDevice bztkg : bztkgList){
								String bztkgvolt = String.valueOf((int)bztkg.getPowerVoltGrade());		
								
								if(volt.equals(bztkgvolt)){
									tempList.add(bztkg);
								}
							}
							
							if(tempList.size()>1){
								for(PowerDevice temp : tempList){
						 			replaceStr += "投入"+CZPService.getService().getDevName(temp)+"备自投装置/r/n";
								}
							}else if(tempList.size()==1){
					 			replaceStr += "投入"+volt+"kV备自投装置/r/n";
							}
						}
					}
				}
	 			
	 			for(PowerDevice mlkg : mlkgList){
	 				replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
	 			}
			}else{
				for(PowerDevice mlkg : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(mlkg).equals("1")){
		 				replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
					}
	 			}
				replaceStr += "将"+plkgName+"保护定值切换至代供"+CZPService.getService().getDevName(lineList)+"保护定值区/r/n";
	 			replaceStr += "将"+CZPService.getService().getDevName(lineList)+"调由"+plkgName+"代供/r/n";
	 			
	 			if(islby){
		 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由运行转冷备用/r/n";
	 			}else if(isrby){
		 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由运行转热备用/r/n";
	 			}
	 			
	 			for(PowerDevice mlkg : mlkgList){
	 				replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
	 			}
	 			
			}
		}
		return replaceStr;
	}

}
