package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXMXGLZB implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪母线关联主变".equals(tempStr)){
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
			
			if(zbList.size()>0){
				replaceStr = CZPService.getService().getDevName(zbList.get(0));
			}
		}
		return replaceStr;
	}
	
}
