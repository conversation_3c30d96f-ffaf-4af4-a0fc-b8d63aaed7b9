package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQDMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆单母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchZYB, "", false, true, true, true);
			List<PowerDevice> jdbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, true);
			List<PowerDevice> drkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR, "", false, true, true, true);
			List<PowerDevice> dkkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDK, "", false, true, true, true);

			if(zybkgList.size() == 0){
				List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);

				for(PowerDevice dev : qtkgList){
					if(dev.getPowerDeviceName().contains("站用变")){
						zybkgList.add(dev);
					}
				}
			}
			
			if(jdbkgList.size() == 0){
				List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);

				for(PowerDevice dev : qtkgList){
					if(dev.getPowerDeviceName().contains("接地变")){
						zybkgList.add(dev);
					}
				}
			}
			
			replaceStr += stationName+"@落实迪庆供电局-XXXXXX检修申请工作任务已结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），该设备的二次装置已正常投入，确认设备具备送电条件/r/n";
			
			if(curDev.getPowerVoltGrade() < station.getPowerVoltGrade()){//负荷侧
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);

				if(curDev.getPowerVoltGrade() == 10){
					replaceStr += "迪庆配调@将对"+stationName+deviceName+"送电，配调管辖设备无影响"+deviceName+"送电情况/r/n";
				}
				
				if(station.getPowerVoltGrade() == 500){//顺控操作
					replaceStr += "迪庆地调@执行将"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(zbkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(zybkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(dkkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(drkgList, stationName);
					
					replaceStr += stationName+"@将"+deviceName+"电压互感器由冷备用转运行/r/n";
					
					for(PowerDevice dev  : zybkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
						}
					}
					
					for(PowerDevice dev  : zbkgList){
						if(!dev.getPowerDeviceName().contains("备用")){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
							}
						}
					}
				}else if(station.getPowerVoltGrade() == 220 && stationName.equals("220kV茶城变")){//220kV茶城变特殊判断
					PowerDevice zbdz = CBSystemConstants.getPowerDevice("114841790497959799");
					
					replaceStr += stationName+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					replaceStr += "迪庆地调@执行将"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(zbdz, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(zbkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(zybkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(jdbkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(dkkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(drkgList, stationName);
					
					replaceStr += stationName+"@将"+deviceName+"电压互感器由冷备用转运行/r/n";
					
					for(PowerDevice dev  : zybkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
						}
					}
					
					for(PowerDevice dev  : jdbkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
						}
					}
					
					for(PowerDevice dev  : zbkgList){
						if(!dev.getPowerDeviceName().contains("备用")){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
							}
						}
					}
				}else{
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
						for(PowerDevice dev : mlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += "投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
						
						if(curDev.getDeviceStatus().equals("0")){
							replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
							
							for(PowerDevice dev : zbkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
								}
							}

							for(PowerDevice dev : mlkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
								}
							}
							
							for(PowerDevice dev : xlkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
								}
							}
							
							for(PowerDevice dev : zybkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
								}
							}
							
							for(PowerDevice dev : jdbkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
								}
							}
						}else{
							replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
						}
					}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
						replaceStr += stationName+"@将"+deviceName+"由热备用转运行/r/n";
					}
				}
				
				if(curDev.getPowerVoltGrade() == 10){
					replaceStr += "迪庆配调@"+stationName+deviceName+"已送电/r/n";
				}
			}else{
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> mldzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeML, "", true, true, true, true);

				replaceStr += stationName+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				
				if(curDev.getPowerVoltGrade() >= 110){//顺控操作
					replaceStr += "迪庆地调@执行将"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(xlkgList, stationName);
					replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(zbkgList, stationName);

					if(mldzList.size() > 0){
						for(PowerDevice dev : mldzList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(dev, stationName);
							}
						}
					}else{
						replaceStr += CommonFunctionDQ.getSequenceConfirmFdContent(mlkgList, stationName);
					}
				}else{
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}
				
				replaceStr += stationName+"@将"+deviceName+"电压互感器由冷备用转运行/r/n";
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
						replaceStr += stationName+"@投入"+deviceName+"充电保护/r/n";
						replaceStr += CommonFunctionDQ.getSwitchOnContent(dev, stationName, station);
						replaceStr += stationName+"@退出"+deviceName+"充电保护/r/n";
					}
				}
				
				for(PowerDevice dev : xlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += CommonFunctionDQ.getHhContent(dev, "迪庆地调", stationName);
					}
				}
			}
		}
		
		return replaceStr;
	}

}
