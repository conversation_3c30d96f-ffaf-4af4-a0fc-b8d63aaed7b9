package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.qj.DevicePowerOnExecute;
import com.tellhow.czp.app.yndd.rule.qj.JDKGXZQJ;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJXLFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖线路复电".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			List<Map<String, String>> stationLineList = CommonFunctionQJ.getStationLineList(curDev);
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("3")){
				for(Map<String,String> stationLineMap : stationLineList){
					String unit = StringUtils.ObjToString(stationLineMap.get("UNIT"));
					String linename = StringUtils.ObjToString(stationLineMap.get("LINE_NAME"));
					String lowerunit = StringUtils.ObjToString(stationLineMap.get("LOWERUNIT"));
					String jddzName = StringUtils.ObjToString(stationLineMap.get("GROUNDDISCONNECTOR_NAME"));
					String endpointtype = StringUtils.ObjToString(stationLineMap.get("ENDPOINT_TYPE"));
					String switchName = StringUtils.ObjToString(stationLineMap.get("SWITCH_NAME")).trim();

					if(unit.equals("输电管理所")){
						continue;
					}

					boolean zsccdx =  false;
					
					for(PowerDevice dev : CommonFunctionQJ.groundWireList){
						if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
							zsccdx = true;
							replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧代替线路接地开关功能的三相接地线/r/n";
							break;
						 }
					}
					
					if(!zsccdx){
						if(!jddzName.equals("")){
							replaceStr += unit+"@拉开"+lowerunit+jddzName+"/r/n";
						}else{
							replaceStr += unit+"@拆除"+lowerunit+linename+"线路侧代替线路接地开关功能的三相接地线/r/n";
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(loadLineTran, SystemConstants.SwitchFlowGroundLine);
					
					if(CommonFunctionQJ.groundWireList.contains(loadLineTran)||jddzList.size()==0){
						replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(loadLineTran)+"线路侧代替线路接地开关功能的三相接地线/r/n";
					}else{
						for(PowerDevice dev : jddzList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					
					if(CommonFunctionQJ.groundWireList.contains(sourceLineTrans)||jddzList.size()==0){
						replaceStr += stationName+"@拆除"+CZPService.getService().getDevName(sourceLineTrans)+"线路侧代替线路接地开关功能的三相接地线/r/n";
					}else{
						for(PowerDevice dev : jddzList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
				}
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("3") || RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				for(Map<String, String> stationLine : stationLineList) {
					String stationName = StringUtils.ObjToString(stationLine.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(stationLine.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(stationLine.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(stationLine.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(stationLine.get("OPERATION_KIND")).trim();
					String endpointkind = StringUtils.ObjToString(stationLine.get("ENDPOINT_KIND")).trim();
					String lineName = StringUtils.ObjToString(stationLine.get("LINE_NAME")).trim();

					if(operationkind.equals("下令")){
						if(endpointkind.equals("风电场")){
							if(disconnectorName.equals("")){
								replaceStr += stationName+"@将"+lowerunit+switchName+"由冷备用转热备用/r/n";
							}
						}else{
							replaceStr += stationName+"@将"+lowerunit+switchName+"由冷备用转热备用/r/n";
						}
					}
				}
			}
			
			for(PowerDevice loadLineTran : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 
				
				List<Map<String, String>> stationZybList = CommonFunctionQJ.getStationZybList(loadLineTran);

				for(Map<String, String> stationZybMap : stationZybList){
					String zybDzName = StringUtils.ObjToString(stationZybMap.get("ZYB_DZNAME"));
					
					if(!zybDzName.equals("")){
						replaceStr += stationName+"@合上"+zybDzName+"/r/n";
					}
				}
				
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(loadLineTran,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				List<PowerDevice> linedzList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXLS+","+CBSystemConstants.RunTypeKnifeXL,"",false,true, true, true);

				if(mxlist.size()>0){
					if(mxlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.isDeviceHadStatus(dev, "2", "1")){
									replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
								}
							}
						}
						
						for(PowerDevice dev : xlkgList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.isDeviceHadStatus(dev, "2", "1")){
									replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
								}
							}
						}
					}else{
						if(linedzList.size() == 1 && xlkgList.size() == 0){
							for(PowerDevice dev : linedzList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionQJ.getKnifeOnContent(linedzList, stationName);
								}
							}
						}else{
							for(PowerDevice dev : linedzList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionQJ.getKnifeOnContent(linedzList, stationName);
								}
							}
							
							for(PowerDevice dev : xlkgList){
								if(RuleExeUtil.isDeviceHadStatus(dev, "2", "1")){
									replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
								}
							}
						}
					}
				}else{
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "2", "1")){
							replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
						} else if (RuleExeUtil.getDeviceBeginStatus(dev).equals("1") || RuleExeUtil.getDeviceBeginStatus(dev).equals("0")) {
							// 特殊接线，光伏电站以及风电场，存在断路器只有一侧隔离开关的情况，此时需要生成操作隔离开关的指令
							List<PowerDevice> dzList = czprule.rule.operationclass.RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							if (dzList.size() == 1) {
								replaceStr += stationName + "@核实" + CZPService.getService().getDevName(dzList.get(0)) + "处合上位置/r/n";
							}
						}
					}
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 

				List<Map<String, String>> stationZybList = CommonFunctionQJ.getStationZybList(sourceLineTrans);

				for(Map<String, String> stationZybMap : stationZybList){
					String zybDzName = StringUtils.ObjToString(stationZybMap.get("ZYB_DZNAME"));
					
					if(!zybDzName.equals("")){
						replaceStr += stationName+"@合上"+zybDzName+"/r/n";
					}
				}
				
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				List<PowerDevice> linedzList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXLS+","+CBSystemConstants.RunTypeKnifeXL,"",false,true, true, true);

				if(mxlist.size()>0){
					if(mxlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.isDeviceHadStatus(dev, "2", "1")){
									replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
								}
							}
						}
						
						for(PowerDevice dev : xlkgList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.isDeviceHadStatus(dev, "2", "1")){
									replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
								}
							}
						}
					}else{
						if(linedzList.size() == 1 && xlkgList.size() == 0){
							for(PowerDevice dev : linedzList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionQJ.getKnifeOnContent(linedzList, stationName);
								}
							}
						}else{
							for(PowerDevice dev : linedzList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionQJ.getKnifeOnContent(linedzList, stationName);
								}
							}
							
							for(PowerDevice dev : xlkgList){
								if(RuleExeUtil.isDeviceHadStatus(dev, "2", "1")){
									replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
								}
							}
						}
					}
				}
			}
			
			if(sourceLineTrans != null && !sourceLineTrans.getPowerDeviceID().equals("")){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 
				
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				
				if(xlkgList.size() == 2){
					for(PowerDevice xlkg : xlkgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
								replaceStr += CommonFunctionQJ.getCdOrHhContent(xlkg, "曲靖地调", stationName);
							}
						}
					}
					
					for(PowerDevice xlkg : xlkgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
								replaceStr += CommonFunctionQJ.getCdOrHhContent(xlkg, "曲靖地调", stationName);
							}
						}
					}
				}else{
					for(PowerDevice xlkg : xlkgList){
						if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
							replaceStr += CommonFunctionQJ.getCdOrHhContent(xlkg, "曲靖地调", stationName);
						}
					}
				}
			}
			
			for(PowerDevice loadLineTran : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 
				
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				List<PowerDevice> otherxlkgList = new ArrayList<PowerDevice>();

				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) && !xlkgList.contains(dev)){
							otherxlkgList.add(dev);
						}
					}
				}
				
				if(mxList.size()>0){
					if(mxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
						for(PowerDevice xlkg : xlkgList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
								if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
									replaceStr += CommonFunctionQJ.getCdOrHhContent(xlkg, "曲靖地调", stationName);
								}
							}
						}
						
						for(PowerDevice xlkg : xlkgList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
								if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
									replaceStr += CommonFunctionQJ.getCdOrHhContent(xlkg, "曲靖地调", stationName);
								}
							}
						}
					}else{
						for(PowerDevice xlkg : xlkgList){
							if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
								replaceStr += CommonFunctionQJ.getCdOrHhContent(xlkg, "曲靖地调", stationName);
							}
						}
						
						for(PowerDevice otherxlkg : otherxlkgList){
							if(RuleExeUtil.getDeviceEndStatus(otherxlkg).equals("1")){
								replaceStr += CommonFunctionQJ.getSwitchOffContent(otherxlkg, stationName, station);
							}
						}
						
						for(PowerDevice mlkg : mlkgList){
							if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
								replaceStr += CommonFunctionQJ.getSwitchOffContent(mlkg, stationName, station);
							}
						}
					}
				}else{//内桥接线
					for(PowerDevice xlkg : xlkgList){
						if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
							replaceStr += CommonFunctionQJ.getCdOrHhContent(xlkg, "曲靖地调", stationName);
						} else if (station.getDeviceType().equals(SystemConstants.PowerFactory)
								|| stationName.contains("电场") || stationName.contains("光伏")) {
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(xlkg)+"处断开位置/r/n";
							// 特殊接线，光伏电站以及风电场，存在断路器只有一侧隔离开关的情况，此时需要生成操作隔离开关的指令
							List<PowerDevice> dzList = czprule.rule.operationclass.RuleExeUtil.getDeviceDirectList(xlkg, SystemConstants.SwitchSeparate);
							if (dzList.size() == 1 && RuleExeUtil.getDeviceBeginStatus(dzList.get(0)).isEmpty()) {
								replaceStr += stationName + "@核实" + CZPService.getService().getDevName(dzList.get(0)) + "处断开位置/r/n";
							}
						}
					}
					
					for(PowerDevice mlkg : mlkgList){
						if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
							replaceStr += CommonFunctionQJ.getSwitchOffContent(mlkg, stationName, station);
						}
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("0")){
				for (Map<String, String> stationLine : stationLineList) {
					String stationName = StringUtils.ObjToString(stationLine.get("UNIT")).trim();
					String lowerunit = StringUtils.ObjToString(stationLine.get("LOWERUNIT")).trim();
					String switchName = StringUtils.ObjToString(stationLine.get("SWITCH_NAME")).trim();
					String chargeDevice = StringUtils.ObjToString(stationLine.get("CHARGE_DEVICE")).trim();
					
					if(!chargeDevice.equals("") && !switchName.equals("")){
						replaceStr += stationName+"@合上"+lowerunit+switchName+"对"+chargeDevice+"充电/r/n";
					}else if(chargeDevice.equals("") && !switchName.equals("")){
						replaceStr += stationName+"@合上"+lowerunit+switchName+"/r/n";
					}
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
