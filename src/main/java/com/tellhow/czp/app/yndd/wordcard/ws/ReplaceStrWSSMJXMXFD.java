package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionWS;
import com.tellhow.graphicframework.constants.SystemConstants;

import com.tellhow.uitl.StringUtils;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSSMJXMXFD  implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("文山双母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> plkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL +","+ CBSystemConstants.RunTypeSwitchMLPL, "", false, true, true, true);
			List<PowerDevice> ptdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, "", true, true, true, true);

			List<PowerDevice> othermxList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.isSwitchDoubleML(dev)){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

					for(PowerDevice mx : mxList){
						if(!mx.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
							othermxList.add(mx);
							break;
						}
					}
				}
			}
			
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade() && !dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
						mxList.add(dev);
					}
				}
			}
			
			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			}
			
			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : xlkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			replaceStr += stationName+"@核实"+deviceName+"相关现场工作任务已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），该设备的二次装置已正常投入，"+deviceName+"具备送电条件/r/n";
			
			if(ptdzList.size() > 0){
				String dzName = "";
				for (PowerDevice dev : ptdzList) {
					String dzNum=CZPService.getService().getDevNum(dev);
					String dzNameTemp = CZPService.getService().getDevName(dev);
					if (dzNameTemp.contains("PT")) {
						dzNameTemp=dzNameTemp.replace("PT"+dzNum, "电压互感器" +"PT"+ dzNum);
					}else{
						dzNameTemp=dzNameTemp.replace(dzNum, "电压互感器" + dzNum);
					}
					if (CommonFunctionWS.ifSwitchSeparateControl(dev)) {
						replaceStr += "文山地调@遥控合上" + stationName + dzNameTemp + "/r/n";
						replaceStr += stationName + "@确认"  + dzNameTemp + "在合闸位置/r/n";
					} else {
						dzName += dzNameTemp + "、";
					}
				}
				if (StringUtils.isNotEmpty(dzName)) {
					dzName = dzName.substring(0, dzName.length() - 1);
					replaceStr += stationName + "@将"  + dzName + "由冷备用转运行/r/n";
				}
			}

			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@投入"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				if(ptdzList.size() > 0){
					replaceStr += "文山地调@执行"+stationName+deviceName+"由热备用转空载运行程序操作/r/n";
				}else{
					replaceStr += stationName+"@将"+deviceName+"由热备用转运行/r/n";
				}
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				if(ptdzList.size() > 0){
					replaceStr += "文山地调@执行"+stationName+deviceName+"由冷备用转空载运行程序操作/r/n";
				}else{
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
					
					for(PowerDevice dev : mlkgList){
						replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@退出"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
			}
			
			replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice yxkg : yxkgList){
						if(CommonFunctionWS.ifSwitchControl(yxkg)&&CommonFunctionWS.ifSwitchSeparateControl(yxkg)){
							replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(yxkg)+"由"+CZPService.getService().getDevName(dev)+"运行倒至"+deviceName+"运行程序操作/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice yxkg : yxkgList){
						if(CommonFunctionWS.ifSwitchControl(yxkg)&&CommonFunctionWS.ifSwitchSeparateControl(yxkg)){
							
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(yxkg)+"由"+CZPService.getService().getDevName(dev)+"运行倒至"+deviceName+"运行/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
			}
			
			replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice rbykg : rbykgList){
						if(CommonFunctionWS.ifSwitchControl(rbykg)&&CommonFunctionWS.ifSwitchSeparateControl(rbykg)){
							replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由热备用转冷备用程序操作/r/n";
							replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由冷备用转热备用于"+CZPService.getService().getDevName(curDev)+"程序操作/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice rbykg : rbykgList){
						if(CommonFunctionWS.ifSwitchControl(rbykg)&&CommonFunctionWS.ifSwitchSeparateControl(rbykg)){
							
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(rbykg)+"由"+deviceName+"热备用倒至"+CZPService.getService().getDevName(dev)+"热备用/r/n";
						}
					}
				}
			}
		}
		return replaceStr;
	}
}
