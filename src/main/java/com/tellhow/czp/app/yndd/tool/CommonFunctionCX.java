package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.sun.java.help.search.Rule;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class CommonFunctionCX {
	public static String getCheckSwitchPosition(PowerDevice dev,String stationName){//检查开关操作前的位置
		String replaceStr = "";

		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
		
		if(dzList.size() == 1){
			if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
				replaceStr += stationName+"@检查"+CZPService.getService().getDevName(dev)+"在分闸位置。/r/n";
			}
		}else{
			if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
				replaceStr += stationName+"@检查"+CZPService.getService().getDevName(dev)+"在热备用状态。/r/n";
			}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
				replaceStr += stationName+"@检查"+CZPService.getService().getDevName(dev)+"在冷备用状态。/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getCheckLineNormal(String lineName,String stationName,String lowerunit){
		String replaceStr = "";
		
		if(lineName.equals("220kV鹿宇Ⅰ回线")){
			replaceStr += stationName+"@检查220kV鹿宇Ⅱ回线272断路器运行正常。/r/n";
			replaceStr += stationName+"@检查220kV母联212断路器运行正常。/r/n";
		}else if(lineName.equals("220kV鹿宇Ⅱ回线")){
			replaceStr += stationName+"@检查220kV鹿宇Ⅰ回线271断路器运行正常。/r/n";
			replaceStr += stationName+"@检查220kV母联212断路器运行正常。/r/n";
		}else if(lineName.equals("220kV腰新Ⅰ回线")){
			replaceStr += stationName+"@控制220kV新立钛业变全站负荷在90MW以内。/r/n";
			replaceStr += stationName+"@检查220kV腰新Ⅱ回线供电正常。/r/n";
			replaceStr += stationName+"@检查220kV分段212断路器运行正常。/r/n";
			replaceStr += stationName+"@检查220kV备自投装置退出。/r/n";
		}else if(lineName.equals("220kV腰新Ⅱ回线")){
			replaceStr += stationName+"@控制220kV新立钛业变全站负荷在90MW以内。/r/n";
			replaceStr += stationName+"@检查220kV腰新Ⅰ回线供电正常。/r/n";
			replaceStr += stationName+"@检查220kV分段212断路器运行正常。/r/n";
			replaceStr += stationName+"@检查220kV备自投装置退出。/r/n";
		}else if(lineName.equals("220kV鹿南牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV紫南牵线供电正常。/r/n";
		}else if(lineName.equals("220kV紫南牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV鹿南牵线供电正常。/r/n";
		}else if(lineName.equals("220kV和丰Ⅰ回线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV和丰Ⅱ回线供电正常。/r/n";
		}else if(lineName.equals("220kV和丰Ⅱ回线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV和丰Ⅰ回线供电正常。/r/n";
		}else if(lineName.equals("220kV仁麦牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV方麦牵线供电正常。/r/n";
		}else if(lineName.equals("220kV仁回牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV方回牵线供电正常。/r/n";
		}else if(lineName.equals("220kV苍广牵Ⅰ回线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV苍广牵Ⅱ回线供电正常。/r/n";
		}else if(lineName.equals("220kV苍广牵Ⅱ回线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV苍广牵Ⅰ回线供电正常。/r/n";
		}else if(lineName.equals("220kV方麦牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV仁麦牵线供电正常。/r/n";
		}else if(lineName.equals("220kV方回牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV仁回牵线供电正常。/r/n";
		}else if(lineName.equals("220kV方谋牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV力谋牵线供电正常。/r/n";
		}else if(lineName.equals("220kV力谋牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV方谋牵线供电正常。/r/n";
		}else if(lineName.equals("220kV力树牵Ⅰ回线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV力树牵Ⅱ回线供电正常。/r/n";
		}else if(lineName.equals("220kV力树牵Ⅱ回线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV力树牵Ⅰ回线供电正常。/r/n";
		}else if(lineName.equals("220kV紫楚北牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由220kV谢楚北牵线供电正常。/r/n";
		}else if(lineName.equals("110kV紫罗沙线")){
			replaceStr += stationName+"@检查"+lowerunit+"由110kV罗沙牵线供电正常。/r/n";
		}else if(lineName.equals("110kV罗沙牵线")){
			replaceStr += stationName+"@检查"+lowerunit+"由110kV紫罗沙线供电正常。/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getTotalStationMaintenance(String stationName){
		String replaceStr = "";

		String[] keyWordArr = {"110kV尖山营光伏电站","110kV竹溪光伏电站","110kV大湾电站","110kV后甸变","110kV三月山风电场"};
		
		for(String keyWord : keyWordArr){
			if(stationName.equals(keyWord)){
				replaceStr += "楚雄地调@"+stationName+"“全站检修”挂牌。/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getLightDifferentialProtect(String deviceName,String stationName,String operate){//线路光差保护
		String replaceStr = "";
		
		if(deviceName.contains("110kV")){
			replaceStr = stationName+"@"+operate+deviceName+"光差保护。/r/n";
		}else{
			String[] keyWordArr = {"220kV鹿宇Ⅰ回线","220kV鹿宇Ⅱ回线","220kV腰新Ⅰ回线","220kV腰新Ⅱ回线"};
			
			for(String keyWord : keyWordArr){
				if(deviceName.equals(keyWord)){
					replaceStr = stationName+"@"+operate+deviceName+"光差保护。/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getOpenFailureProtect(String deviceName,String stationName){//启动失灵保护
		String replaceStr = "";
		
		if(deviceName.contains("220kV")){
			replaceStr = stationName+"@检查"+deviceName+"启动失灵保护功能退出。/r/n";
		}
				
		return replaceStr;
	}
	
	public static String getCheckBeforeContent(String deviceName,String stationName,String mxName){
		String replaceStr = stationName+"@检查"+deviceName+"间隔已按远方操作前的要求进行设置。"+mxName+"/r/n";
		return replaceStr;
	}
	
	public static String getZxdJddzOffCheckContent(List<PowerDevice> zxdjddzList,String stationName){
		String replaceStr = "";

		boolean isZxdOff = false;
		
		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOff = true;
			}
		}
		
		if(isZxdOff){
			tagzxdjddzList = RuleExeUtil.sortByVoltHigh(tagzxdjddzList);
			
			for(PowerDevice dev : tagzxdjddzList) {
				replaceStr += "楚雄地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				replaceStr += stationName+"@检查"+CZPService.getService().getDevName(dev)+"在拉开位置。/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getZxdJddzOnCheckContent(List<PowerDevice> zxdjddzList,String stationName){
		String replaceStr = "";
		
		boolean isZxdOn = false;
		
		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOn = true;
			}
		}
		
		if(isZxdOn){
			tagzxdjddzList = RuleExeUtil.sortByVoltLow(tagzxdjddzList);

			for(PowerDevice dev : tagzxdjddzList) {
				replaceStr += "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				replaceStr += stationName+"@检查"+CZPService.getService().getDevName(dev)+"在合上位置。/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
					replaceStr += getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "楚雄地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
					replaceStr += getKnifeOffCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getSwitchOffContent(PowerDevice dev,String stationName){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(stationName.endsWith("电站") || stationName.endsWith("电厂")){
				replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"。/r/n";
			}else{
				stationName = StringUtils.killVoltInDevName(stationName);
				replaceStr = "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
			}
		}else{
			replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"。/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getSwitchOnContent(PowerDevice dev,String stationName){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(stationName.endsWith("电站") || stationName.endsWith("电厂")){
				replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"。/r/n";
			}else{
				stationName = StringUtils.killVoltInDevName(stationName);
				replaceStr = "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
			}
		}else{
			replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"。/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(dev.getPowerVoltGrade() > 35){
				if(stationName.contains("电站") || stationName.endsWith("电厂")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"合环。/r/n";
				}else{
					stationName = StringUtils.killVoltInDevName(stationName); 
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环。/r/n";
				}
			}else{
				if(stationName.contains("电站") || stationName.endsWith("电厂")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"合环。/r/n";
				}else{
					stationName = StringUtils.killVoltInDevName(stationName); 
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环。/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static List<Map<String, String>> getStationLineList(PowerDevice curDev){
		List<Map<String, String>> stationLineList = new ArrayList<Map<String,String>>();
		
		String sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,PTDISCONNECTOR_NAME "
				+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND "
						+ "WHERE ID = '"+curDev.getPowerDeviceID()+"')";
		
		stationLineList = DBManager.queryForList(sql);
		
		if(stationLineList.size() == 0){
			String lineName = CZPService.getService().getDevName(curDev);
			
			sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,PTDISCONNECTOR_NAME "
					+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_NAME = '"+lineName+"'";
			
			stationLineList = DBManager.queryForList(sql);
		}
		
		return stationLineList; 
	}
	
	public static String getKnifeOnCheckContent(PowerDevice dev){//dev是开关
		String replaceStr = "";
		
		PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station); 
		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
		
		boolean ismldz = false;
		
		for(PowerDevice dz : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dz, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		String deviceName = CZPService.getService().getDevName(dev);
		
		if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
			String mxName = "";
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
			
			for(PowerDevice mx : mxList){
				mxName = "于"+CZPService.getService().getDevName(mx);
				break;
			}
			
			replaceStr += stationName+"@检查"+deviceName+"热备用"+mxName+"。/r/n";
		}else{
			replaceStr += stationName+"@检查"+deviceName+"在热备用状态。/r/n";
		}
		
//		for(PowerDevice dz : dzList){
//			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
//				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
//					String deviceName = CZPService.getService().getDevName(dev); 
//					replaceStr += stationName+"@检查"+deviceName+"在热备用。/r/n";
//					break;
//				}else{
//					String dzName = CZPService.getService().getDevName(dz);
//					replaceStr += stationName+"@检查"+dzName+"在合上位置。/r/n";
//				}
//			}
//		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

					String deviceName = ""; 
					
					for(PowerDevice dev : swList){
						deviceName = CZPService.getService().getDevName(dev); 
					}
					
					replaceStr += stationName+"@检查"+deviceName+"在热备用状态。/r/n";
					break;
				}else{
					String dzName = CZPService.getService().getDevName(dz);
					replaceStr += stationName+"@检查"+dzName+"在合上位置。/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffCheckContent(PowerDevice dev){//dev是开关
		String replaceStr = "";
		
		PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station); 
		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
		
		boolean ismldz = false;
		
		for(PowerDevice dz : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dz, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		String deviceName = CZPService.getService().getDevName(dev); 
		
		if(dzList.size() == 1){
			replaceStr += stationName+"@检查"+CZPService.getService().getDevName(dzList.get(0))+"在拉开状态。/r/n";
		}else{
			replaceStr += stationName+"@检查"+deviceName+"在冷备用状态。/r/n";
		}
		
//		for(PowerDevice dz : dzList){
//			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
//				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
//					String deviceName = CZPService.getService().getDevName(dev); 
//					replaceStr += stationName+"@检查"+deviceName+"在冷备用状态。/r/n";
//					break;
//				}else{
//					String dzName = CZPService.getService().getDevName(dz);
//					replaceStr += stationName+"@检查"+dzName+"在拉开位置。/r/n";
//				}
//			}
//		}
		
		return replaceStr;
	}
	
	public static String getKnifeHalfControlOffContent(PowerDevice dev){//dev是开关
		String replaceStr = "";
		
		PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station); 
		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
		
		boolean ismldz = false;
		
		for(PowerDevice dz : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dz, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(CommonFunctionCX.ifSwitchSeparateControl(dz)){
				replaceStr += "楚雄地调@遥控拉开"+stationName+CZPService.getService().getDevName(dz)+"。/r/n";
				
				if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String deviceName = CZPService.getService().getDevName(dev); 
						replaceStr += stationName+"@检查"+deviceName+"在冷备用状态。/r/n";
						break;
					}else{
						String dzName = CZPService.getService().getDevName(dz);
						replaceStr += stationName+"@检查"+dzName+"在拉开位置。/r/n";
					}
				}
			}else{
				replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dz)+"。/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeHalfControlOnContent(PowerDevice dev){//dev是开关
		String replaceStr = "";
		
		PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station); 
		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
		
		boolean ismldz = false;
		
		for(PowerDevice dz : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dz, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				if(CommonFunctionCX.ifSwitchSeparateControl(dz)){
					replaceStr += "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(dz)+"。/r/n";

					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String deviceName = CZPService.getService().getDevName(dev); 
						replaceStr += stationName+"@检查"+deviceName+"在冷备用状态。/r/n";
						break;
					}else{
						String dzName = CZPService.getService().getDevName(dz);
						replaceStr += stationName+"@检查"+dzName+"在拉开位置。/r/n";
					}
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dz)+"。/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	
	public static String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

					String deviceName = ""; 
					
					for(PowerDevice dev : swList){
						deviceName = CZPService.getService().getDevName(dev); 
					}
					
					replaceStr += stationName+"@检查"+deviceName+"在冷备用状态。/r/n";
					break;
				}else{
					String dzName = CZPService.getService().getDevName(dz);
					replaceStr += stationName+"@检查"+dzName+"在拉开位置。/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static boolean ifSwitchControl(PowerDevice dev){
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
					+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dev.getPowerDeviceID()+"'";
			
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
				return Boolean.parseBoolean(ifcontrol);
			}
		}
		
		return true;
	}
	
	public static boolean ifSwitchSeparateControl(PowerDevice dev){//刀闸是否可控
		if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
					+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dev.getPowerDeviceID()+"'";
			
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
				return Boolean.parseBoolean(ifcontrol);
			}
		}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dz : dzList){
				String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
						+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dz.getPowerDeviceID()+"'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}
		}
		
		return true;
	}
	
	public static boolean ifSwitchSeparateHalfControl(PowerDevice dev){//刀闸是否一个可控一个不可控
		boolean result = false;
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			boolean ifControl = false;
			boolean ifNotControl = false;
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dz : dzList){
				String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
						+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dz.getPowerDeviceID()+"'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						ifNotControl = true;
					}else{
						ifControl = true;
					}
				}
			}
			
			if(ifNotControl && ifControl){
				result = true;
			}else{
				result = false;
			}
		}
		
		return result;
	}
	
	public static List<PowerDevice> getTransformerKnife(PowerDevice zb,PowerDevice zbkg){
		List<PowerDevice> dztagList = new ArrayList<PowerDevice>();

		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
		List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(zb, zbkg, SystemConstants.PowerTransformer, "", true, true);
		
		for(PowerDevice path : pathList){
			if(path.getDeviceType().equals(SystemConstants.SwitchSeparate)){
				if(!dzList.contains(path)){
					dztagList.add(path);
				}
			}
		}
		
		return dztagList;
	}
}
