package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHSMXDCKGZT  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		
		String replaceStr = "";
		
		if("核实母线对侧开关状态".equals(tempStr)){
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			
			if(lineList.size()>0){
				for(PowerDevice line : lineList){
					if(RuleExeUtil.getDeviceBeginStatus(line).equals("0")){
						List<PowerDevice> otherlineList = RuleExeUtil.getLineOtherSideList(line);
						
						if(otherlineList.size()>0){
							for(PowerDevice otherline : otherlineList){
								List<PowerDevice> xlkgList =RuleExeUtil.getLinkedSwitch(otherline);
								
								if(xlkgList.size()>0){
									PowerDevice xlkg = xlkgList.get(0);
									
									if(xlkg.getDeviceStatus().equals("1")){
										replaceStr += otherline.getPowerStationName()+"@核实"+CZPService.getService().getDevName(xlkg)+"热备用/r/n";
										
										if(!RuleExeUtil.getRunMode(otherline).equals("2")&&!curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
											replaceStr += xlkg.getPowerStationName()+"@退出"+StringUtils.getVolt(xlkg.getPowerVoltGrade())+"备自投装置/r/n";
										}
									}
								}
								
							}
						}
					}
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
