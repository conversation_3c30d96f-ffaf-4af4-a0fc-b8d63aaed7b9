package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrSJDWTQMX implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("上级电网同期母线".equals(tempStr)) {
			
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				if(dtd.getTransDevice().getDeviceType().equals(SystemConstants.Switch) && dtd.getEndstate().equals("0") && dtd.getTransDevice().getPowerStationID().equals(stationDev.getPowerStationID())) {
				
					try{
						List<PowerDevice> mlList = RuleExeUtil.getDeviceList(dtd.getTransDevice(), null, SystemConstants.MotherLine, SystemConstants.PowerTransformer, null, null, false, true, true, true);
						
						List<PowerDevice> tfList1 = RuleExeUtil.getDeviceList(mlList.get(0), dtd.getTransDevice(), SystemConstants.PowerTransformer, null, null, null, false, false, false, true);
						List<PowerDevice> tfList2 = RuleExeUtil.getDeviceList(mlList.get(1), dtd.getTransDevice(), SystemConstants.PowerTransformer, null, null, null, false, false, false, true);
						
						List<PowerDevice> gymxList1 = RuleExeUtil.getDeviceList(tfList1.get(0), null, SystemConstants.MotherLine, null, null, null, false, false, true, true, true);
						List<PowerDevice> gymxList2 = RuleExeUtil.getDeviceList(tfList2.get(0), null, SystemConstants.MotherLine, null, null, null, false, false, true, true, true);
						
						
						gymxList1.addAll(gymxList2);
						RuleExeUtil.swapDeviceList(gymxList1);
						replaceStr = CZPService.getService().getDevName(gymxList1);
					}
					catch(Exception ex) {
						ex.printStackTrace();
					}
				}
			}
		}
		return replaceStr;
	}


}