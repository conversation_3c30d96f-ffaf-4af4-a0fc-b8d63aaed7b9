package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXDMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄单母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName);
			String deviceName = CZPService.getService().getDevName(curDev);
			
			if(curDev.getPowerVoltGrade() == 10){
				List<PowerDevice> zbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
				List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				List<PowerDevice> drqList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.ElecCapacity, SystemConstants.PowerTransformer,true, true, true);
				
				List<PowerDevice> drdkzby = new ArrayList<PowerDevice>();
				drdkzby.addAll(mlkgList);
				drdkzby.addAll(zbkgList);
				
				for(PowerDevice dev : drdkzby){
					String devName = CZPService.getService().getDevName(dev);

					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += voltStationName+"@"+devName+"由冷备用转热备用。/r/n";
						replaceStr += voltStationName+"@合上"+devName+"对"+deviceName+"及母线电压互感器充电。/r/n";
					}
				}
				
				replaceStr += "楚雄配调@通知"+stationName+deviceName+"供电正常。/r/n";
				
				for(PowerDevice dev : drqList){
					String devName = CZPService.getService().getDevName(dev);

					replaceStr += voltStationName+"@"+devName+"由冷备用转热备用。/r/n";
				}
				
				replaceStr += voltStationName+"@10kV X号站用变由冷备用转运行。/r/n";
				
				for(PowerDevice dev : drdkzby){
					String devName = CZPService.getService().getDevName(dev);

					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += voltStationName+"@"+devName+"由冷备用转热备用。/r/n";
					}
				}
			}else{
				List<PowerDevice> zbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
				List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				
				for(PowerDevice dev : zbkgList){
					String devName = CZPService.getService().getDevName(dev);

					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += voltStationName+"@"+devName+"由冷备用转热备用。/r/n";
						replaceStr += voltStationName+"@合上"+devName+"对"+deviceName+"及母线电压互感器充电。/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					String devName = CZPService.getService().getDevName(dev);

					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += voltStationName+"@"+devName+"由冷备用转热备用。/r/n";
						replaceStr += voltStationName+"@合上"+devName+"对"+deviceName+"及母线电压互感器充电。/r/n";
					}
				}
				
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true);
				
				for(PowerDevice dev : xlkgList){
					String devName = CZPService.getService().getDevName(dev);
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += voltStationName+"@"+devName+"由冷备用转热备用。/r/n";
						replaceStr += "楚雄地调@遥控合上"+stationName+devName+"。/r/n";
					}else if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += voltStationName+"@"+devName+"由冷备用转热备用。/r/n";
					}
				}
				
				for(PowerDevice dev : zbkgList){
					String devName = CZPService.getService().getDevName(dev);

					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += voltStationName+"@"+devName+"由冷备用转热备用。/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					String devName = CZPService.getService().getDevName(dev);

					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += voltStationName+"@"+devName+"由冷备用转热备用。/r/n";
					}
				}
			}
			
			replaceStr += voltStationName+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置。/r/n";
		}
		
		return replaceStr;
	}

}
