package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.xsbn.XSBNZNHHDDExecute;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNZNTDDDRW implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("版纳站内停电调电任务".equals(tempStr)) {
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> itor = mapStationDevice.values().iterator(); itor.hasNext();) {
				PowerDevice dev = itor.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						hignVoltMlkgList.add(dev);
					}
				}
			}
			
			PowerDevice powercutLine = new PowerDevice();
			PowerDevice powerLine = new PowerDevice();

			for(PowerDevice dev : XSBNZNHHDDExecute.powercutDeviceList){
				powercutLine = dev;
			}
			
			for(PowerDevice dev : XSBNZNHHDDExecute.powerDeviceList){
				powerLine = dev;
			}
			
			boolean flag = true;
			
			if(hignVoltMlkgList.size()>0){
				for(PowerDevice dev : hignVoltMlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						flag = false;
					}
				}
			}else{
				flag = false;
			}
			
			if(flag){
				replaceStr += stationName+deviceName+"由"+CZPService.getService().getDevName(powercutLine)+"供电转由"+CZPService.getService().getDevName(powerLine)+"供电";
			}else{
				replaceStr += stationName+"由"+CZPService.getService().getDevName(powercutLine)+"供电转由"+CZPService.getService().getDevName(powerLine)+"供电";
			}
		}
		return replaceStr;
	}


}