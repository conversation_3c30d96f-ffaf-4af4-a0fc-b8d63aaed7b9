package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBHBBH implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if(CBSystemConstants.getCurRBM().getEndState().equals("0")){//节省时间，直接倒序反转输出转运行保护
			if(desc.contains("投入")){
				desc=desc.replace("投入", "退出");
			}else if(desc.contains("退出")){
				desc=desc.replace("退出", "投入");
			}
			
		}
		
		if("主变后备保护".equals(tempStr)){
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
			for (DispatchTransDevice dtd : dtds.values()) {
				PowerDevice dev = dtd.getTransDevice();
				if((dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)
						||(dev.getPowerStationName().contains("西湖变")&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchQT)))
						&&!mlkgList.contains(dev)
						&&dev.getPowerVoltGrade()!=stationDev.getPowerVoltGrade()){
					mlkgList.add(dev);
				}
			}
			RuleExeUtil.swapLowDeviceList(mlkgList);
			String caozuo = "投入";
			if(desc.contains("退出")){
				caozuo="退出";
				RuleExeUtil.swapDeviceList(mlkgList);
			}
			String zbc = "三";
			
			for(PowerDevice mlkg:mlkgList){
				String str = (int)mlkg.getPowerVoltGrade()+"kV侧";
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,
						"", CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother, false, true, false, true);
				RuleExeUtil.swapDeviceList(zbList);
				
				if(caozuo.equals("退出")&&!zbList.contains(stationDev)){
					continue;
				}
				
				if(caozuo.equals("退出")||stationDev.getPowerStationName().contains("龙井变")){
					zbList.clear();
					zbList.add(stationDev);
				}
				
//				if(!replaceStr.equals("")){
					replaceStr+=caozuo;
//				}
				replaceStr+=CZPService.getService().getDevName(zbList);
				if(stationDev.getPowerVoltGrade()>110||RuleExeUtil.isTransformerXBZ(stationDev)){
					replaceStr+="第Ⅰ、Ⅱ套";
				}
				if(RuleExeUtil.isTransformerXBZ(stationDev)){
					replaceStr+="10kV侧后备保护动作跳"+CZPService.getService().getDevName(mlkg);
				}else{
					replaceStr+=str+"后备保护动作跳"+CZPService.getService().getDevName(mlkg);
				}
				
						
				if(caozuo.equals("退出")&&RuleUtil.isTransformerNQ(stationDev)){
					if(RuleExeUtil.getTransformerVol(stationDev).size()==2){
						zbc = "两";
					}
					replaceStr+="、主变"+zbc+"侧及桥断路器";
				}
						
				replaceStr+="\r\n";
			}
			if(desc.contains("退出")&&stationDev.getPowerVoltGrade()==220){
				if(RuleUtil.isTransformerNQ(stationDev)){
					List<PowerDevice> zbbzkgList = RuleExeUtil.getTransformerSwitchMiddle(stationDev);
					if(zbbzkgList.size()>0){
						List<PowerDevice> mlswList = RuleExeUtil.getDeviceList(zbbzkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						if(zbbzkgList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							for(int i=0;i<mlswList.size();i++){
								if(!RuleExeUtil.isSwitchDoubleML(mlswList.get(i))){
									mlswList.remove(i);
									i--;
								}
							}
							if(mlswList.size()>0){
								replaceStr+="退出"+CZPService.getService().getDevName(stationDev)+"第Ⅰ、Ⅱ套中后备保护动作跳"+CZPService.getService().getDevName(mlswList.get(0))+"、主变三侧及桥断路器\r\n";
							}
								
						}
					}
					replaceStr+="退出"+CZPService.getService().getDevName(stationDev)+"非电量、第Ⅰ、Ⅱ套高后备保护跳主变"+zbc+"侧及桥断路器\r\n";
					replaceStr+="退出"+CZPService.getService().getDevName(stationDev)+"第Ⅰ、Ⅱ套中性点过流跳主变"+zbc+"侧及桥断路器\r\n";
					if(CBSystemConstants.getCurRBM().getBeginStatus().equals("0")){
						replaceStr+="核实"+CZPService.getService().getDevName(stationDev)+"第Ⅰ、Ⅱ套纵差、零差保护及第Ⅰ、Ⅱ套纵差、零差保护跳主变"+zbc+"侧及桥断路器投入";
					}
					
				}else if(RuleExeUtil.isTransformerXBZ(stationDev)){
					
				}else{
					List<PowerDevice> mlswList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.Switch, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
					RuleExeUtil.swapDeviceList(mlswList);
					for(PowerDevice mlsw:mlswList){
						//if(RuleExeUtil.isSwitchDoubleML(mlsw)){
						String temp = "退出"+CZPService.getService().getDevName(stationDev)+"第Ⅰ、Ⅱ套"+StringUtils.getVolt(mlsw.getPowerVoltGrade())
								+"侧后备保护动作跳"+CZPService.getService().getDevName(mlsw)+"\r\n";
						
						if(!replaceStr.contains(temp)){
							replaceStr+="退出"+CZPService.getService().getDevName(stationDev)+"第Ⅰ、Ⅱ套"+StringUtils.getVolt(mlsw.getPowerVoltGrade())
									+"侧后备保护动作跳"+CZPService.getService().getDevName(mlsw)+"\r\n";
						}
							
						//}
					}
					List<PowerDevice> zbbgkgList = RuleExeUtil.getTransformerSwitchHigh(stationDev);
					if(zbbgkgList.size()>0){
						replaceStr+="退出"+CZPService.getService().getDevName(zbbgkgList.get(0))+"启动失灵保护";
					}
					
				}
				
			}else if(desc.contains("退出")&&stationDev.getPowerVoltGrade()==110){
				if(RuleUtil.isTransformerNQ(stationDev)){
					replaceStr+="退出"+CZPService.getService().getDevName(stationDev)+"非电量保护及其动作跳主变"+zbc+"侧及桥断路器\r\n";
					if(CBSystemConstants.getCurRBM().getBeginStatus().equals("0")){
						replaceStr+="核实"+CZPService.getService().getDevName(stationDev)+"其余保护不作调整仍保持投入运行";
					}
					
				}else if(RuleExeUtil.isTransformerXBZ(stationDev)){
					
				}else{
//					String str = "#2";
//					if(stationDev.getPowerDeviceName().contains("#2")){
//						 str = "#1";
//					}
//					replaceStr+="核实站内仅投110kV"+str+"主变中性点及其零序保护";
				}
				
			}
		
		}
		
		if(CBSystemConstants.getCurRBM().getEndState().equals("0")){//节省时间，直接倒序反转输出转运行保护
			
			String[] strs = replaceStr.split("\r\n");
			replaceStr="";
			for(int i=strs.length-1;i>=0;i--){
				replaceStr+=strs[i]+"\r\n";
			}
			
			if(desc.contains("投入")){
				replaceStr=replaceStr.replace("投入", "退出");
			}else if(desc.contains("退出")){
				replaceStr=replaceStr.replace("退出", "投入");
			}
		}
		if(replaceStr.startsWith("退出")||replaceStr.startsWith("投入")){
			replaceStr=replaceStr.substring(2,replaceStr.length());
		}
		return replaceStr;
	}

}
