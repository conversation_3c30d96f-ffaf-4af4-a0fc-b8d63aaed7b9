package com.tellhow.czp.app.yndd.rule.yx;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class LineStatusExecuteYX implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		String beginStatus =rbm.getBeginStatus();
		String endStatus = rbm.getEndState();
		PowerDevice pd = rbm.getPd();
		
		PowerDevice lineSource = null;
		String paraID=pd.getPowerDeviceID();
		
		if(pd.getPowerVoltGrade()==110){
			List<PowerDevice> mxList =RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer
					, true, true, true);
			if(mxList.size()==0){
				List<PowerDevice> otherLines =RuleExeUtil.getLineOtherSideList(pd);
				for(PowerDevice line:otherLines){
					List<PowerDevice> mxs =RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer
							, true, true, true);
					if(mxs.size()>0){
						paraID=line.getPowerDeviceID();
						break;
					}
				}
			}
		}
		lineSource = CBSystemConstants.LineSource.get(paraID);
		List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(paraID);
		
		if(lineLoad==null){
			return true;
		}
		lineLoad = orderByLoadline(lineLoad);
				
		boolean result =false;
		
		if(beginStatus.equals("0")){
			result = xl0to1(lineSource, lineLoad);
			
			if(!result)
				return result;
		}else if(beginStatus.equals("1") && endStatus.equals("2")) { 
			result = xl1to2(lineSource, lineLoad);
			
			if(!result)
				return result;
		}else if(beginStatus.equals("2") && endStatus.equals("3")) { 
			result = xl2to3(lineSource, lineLoad);
			
			if(!result)
				return result;
		}else if(beginStatus.equals("2") && endStatus.equals("1")) { 
			result = xl2to1(lineSource, lineLoad);
			
			if(!result)
				return result;
		}else if(beginStatus.equals("3") && endStatus.equals("2")) { 
			result = xl3to2(lineSource, lineLoad);
			
			if(!result)
				return result;
		}
		else if(endStatus.equals("0")) {
			result = xl1to0(lineSource, lineLoad);
			
			if(!result)
				return result;
		}
		
		return true;
	}

	public boolean xl0to1(PowerDevice lineSource,List<PowerDevice> lineLoad){
		for(PowerDevice line : lineLoad) {
			if(line != null){
				if(line.getPowerVoltGrade()<220){
					boolean res = loadLine(line);
					if(!res){
						return res;
					}
				}
				
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				List<PowerDevice> swList = new ArrayList<PowerDevice>();
				
				boolean flag = false;
				
				for(PowerDevice dev : mxList){
					if(dev.getPowerDeviceName().contains("虚拟")){
						flag = true;
					}
				}
				
				if(flag){
					swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.PowerTransformer, true, false, true);
				}else{
					swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				}
				
				this.sortListBySwMiddle(swList);
				for(PowerDevice sw:swList){
					if(!sw.getDeviceStatus().equals("1")){
						PowerDevice station = CBSystemConstants.getPowerStation(sw.getPowerStationID());
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(sw.getPowerStationID());
						List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();

						
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev2 = it2.next();
							
							if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
								if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev2);
								}
							}
						}
						
						if(hignVoltXlkgList.size()==2){
							for(PowerDevice xlkg : hignVoltXlkgList){
								if(!xlkg.getPowerDeviceID().equals(sw.getPowerDeviceID())&&xlkg.getDeviceStatus().equals("1")){
									RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "0");
									RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
									break;
								}
							}
							
							if(sw.getDeviceStatus().equals("0")){
								RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
							}
						}else{
							if(sw.getDeviceStatus().equals("0")){
								RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
							}
						}
					}
				}
			}
		}
		
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		this.sortListBySwMiddle(swList);
		for(PowerDevice sw:swList){
			if(!sw.getDeviceStatus().equals("1")){
				RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
			}
		}
		
		return true;
	}
	
	
	public boolean xl1to0(PowerDevice lineSource,List<PowerDevice> lineLoad){
		List<PowerDevice> allswList = new ArrayList<PowerDevice>();
		
		if(lineSource!=null){
			List<PowerDevice> swList1 = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);
			this.sortListBySwMiddle(swList1);
			Collections.reverse(swList1);
			allswList.addAll(swList1);
			for(PowerDevice sw:swList1){
				if(!sw.getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "0");
				}
			}
		}
		
		for(PowerDevice line : lineLoad) {
			PowerDevice station = CBSystemConstants.getPowerStation(line.getPowerStationID());
			
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);
			
			this.sortListBySwMiddle(swList);
			Collections.reverse(swList);
			allswList.addAll(swList);
			for(PowerDevice sw:swList){
				if(!sw.getDeviceStatus().equals("0")&&(CBSystemConstants.getLineTagStatus(sw).equals("-1")||CBSystemConstants.getLineTagStatus(sw).equals("0"))){
					RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "0");
				}
			}
			
			if(swList.size()>0&&station.getPowerVoltGrade()<220){
				if(swList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
					LineExecuteLoadYX loadExe = new LineExecuteLoadYX();
					loadExe.executeLoadBack(line);
				}
			}
		}	
		
		for(PowerDevice sw:allswList){
			if(sw.getDeviceStatus().equals("0")&&CBSystemConstants.getLineTagStatus(sw).equals("1")){
				RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
			}
		}
		
		return true;
	}
	
	public boolean xl1to2(PowerDevice lineSource,List<PowerDevice> lineLoad){
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
		lineList.add(lineSource);
		lineList.addAll(lineLoad);
	
		for(PowerDevice line : lineList) {
			PowerDevice xldz = null;
			List<PowerDevice> linedzList = RuleExeUtil.getDeviceList(line, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXL,"",false,true, true, true);
			
			if(linedzList != null){
				for(PowerDevice dev : linedzList){
					xldz = dev;
					
					if(dev.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
			}
			
			List<PowerDevice> onSW =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> swList = new ArrayList<PowerDevice>();
			
			boolean flag = false;
			
			for(PowerDevice dev : mxList){
				if(dev.getPowerDeviceName().contains("虚拟")){
					flag = true;
				}
			}
			
			if(flag){
				swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.PowerTransformer, true, false, true);
			}else{
				swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			}
			
			this.sortListBySwMiddle(swList);
			
			if(swList!=null){
				for(PowerDevice sw : swList){
					if(("0").equals(CBSystemConstants.LineTagStatus.get(sw))){
						onSW.add(sw);
					}else{
						List<PowerDevice> dzList = RuleExeUtil.getDeviceList(sw, SystemConstants.SwitchSeparate,
								SystemConstants.PowerTransformer, true, true, true);
						
						for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();) {
							PowerDevice dz = itor.next();
							
							if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
								itor.remove();
							}
						}
						
						dzList = RuleExeUtil.sortByMXC(dzList);
						Collections.reverse(dzList);
						
						for(PowerDevice dz : dzList){
							RuleExeUtil.deviceStatusExecute(dz, dz.getDeviceStatus(), "1");
						}
					}
				}
			}
			
			Collections.reverse(onSW);
			for(PowerDevice sw:onSW){
				if(xldz.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusExecute(sw, "1", "0");
				}
			}
		}
		
		for(PowerDevice line:lineList){
			if(line!=null){
				if(line.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusSet(line, "1", "2");
				}
			}
		}
		
		return true;
	}
	
	
	/**
	 */
	public boolean xl2to1(PowerDevice lineSource,List<PowerDevice> lineLoad){
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
		
		lineList.addAll(lineLoad);
		
		if(lineSource != null){
			lineList.add(lineSource);
		}
		
		for(PowerDevice line : lineList) {
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);//搜索线路开关
			
			Collections.reverse(swList);
			for(PowerDevice sw:swList){
				if(sw.getDeviceStatus().equals("3")){
					RuleExeUtil.deviceStatusExecute(sw, "3", "2");
				}
				
				if(CBSystemConstants.LineTagStatus.get(sw)!=null){
					if(!CBSystemConstants.LineTagStatus.get(sw).equals("2")){
						RuleExeUtil.deviceStatusExecute(sw, "2", "1");//开关转热备用
					}
				}
			}

			PowerDevice xldz = null;
			List<PowerDevice> dzList = RuleExeUtil.getDeviceList(line, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer, true, true, true);//搜索线路关联刀闸
			
			if(dzList.size()==1){
				xldz = dzList.get(0);
				
				List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceDirectList(xldz, SystemConstants.Switch);
				
				boolean flag = false;
				
				if(xlkgList.size()>0){
					for(PowerDevice xlkg : xlkgList){
						List<PowerDevice> xlkgdzList =  RuleExeUtil.getDeviceDirectList(xlkg, SystemConstants.SwitchSeparate);

						if(xlkgdzList.size()>0){
							for(PowerDevice xlkgdz : xlkgdzList){
								if(xlkgdz.equals("0")&&!dzList.contains(xlkgdz)){
									flag = true;
									break;
								}
							}
						}
					}
				}else if(xldz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
					flag = true;
				}
				
				if(flag){
					if(xldz.getDeviceStatus().equals("1")){//合上线路侧刀闸
						RuleExeUtil.deviceStatusExecute(xldz, "1", "0");
					}
				}
			}
		}
		
		for(PowerDevice line:lineList){
			if(line.getDeviceStatus().equals("2")){
				RuleExeUtil.deviceStatusSet(line, "2", "1");
			}
		}

		return true;
	}
	
	public boolean xl2to3(PowerDevice lineSource,List<PowerDevice> lineLoad){
		List<PowerDevice> didao = RuleExeUtil.getDeviceDirectList(lineSource,SystemConstants.SwitchFlowGroundLine);
		
		if(didao.size()>0){
			for(PowerDevice dd:didao){
				RuleExeUtil.deviceStatusExecute(dd, "1", "0");
				
			}
		}else{
			RuleExeUtil.deviceStatusSet(lineSource, "2", "3");
		}
		
		lineLoad = orderByLoadlineDesc(lineLoad);
		for(int i=0;i<lineLoad.size();i++){
			List<PowerDevice> didaoLoad = RuleExeUtil.getDeviceDirectList(lineLoad.get(i), SystemConstants.SwitchFlowGroundLine);
			
			if(didaoLoad!=null){
				if(didaoLoad.size()>0){
					for(PowerDevice dd:didaoLoad){
						RuleExeUtil.deviceStatusExecute(dd, "1", "0");
					}
				}else{
					RuleExeUtil.deviceStatusSet(lineLoad.get(i), "2", "3");
				}
			}
		
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineLoad.get(i), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			
			if(swList!=null){
				for(PowerDevice sw:swList){
					if("3".equals(CBSystemConstants.LineTagStatus.get(sw))){
						RuleExeUtil.deviceStatusExecute(sw, "2", "3");
					}
				}
			}
		}
		
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		for(PowerDevice sw:swList){
			if("3".equals(CBSystemConstants.LineTagStatus.get(sw))){
				RuleExeUtil.deviceStatusExecute(sw, "2", "3");
			}
		}
		
		return true;
	}
	/**
	 */
	public boolean xl3to2(PowerDevice lineSource,List<PowerDevice> lineLoad){
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer,
				true, true, true);
		
		for(PowerDevice sw:swList){
			if(sw.getDeviceStatus().equals("3")){
				RuleExeUtil.deviceStatusExecute(sw, "3", "2");
			}
		}
		
		List<PowerDevice> didaoDY = RuleExeUtil.getDeviceList(lineSource,
				SystemConstants.SwitchFlowGroundLine, SystemConstants.PowerTransformer,true, true, true);
		
		if(didaoDY.size() > 0){
			for(PowerDevice dd:didaoDY){
				RuleExeUtil.deviceStatusExecute(dd, "0", "1");
			}
		}else{
			RuleExeUtil.deviceStatusSet(lineSource, "3", "2");
		}
		
		lineLoad =orderByLoadlineDesc(lineLoad);
		for(int i=0;i<lineLoad.size();i++){
			List<PowerDevice> swLoadList = RuleExeUtil.getDeviceList(lineLoad.get(i), SystemConstants.Switch, SystemConstants.PowerTransformer,
					true, true, true);
			
			if(swLoadList != null){
				for(PowerDevice sw:swLoadList){
					if(sw.getDeviceStatus().equals("3")){
						RuleExeUtil.deviceStatusExecute(sw, "3", "2");
					}
				}
			}
			
			List<PowerDevice> didao = RuleExeUtil.getDeviceList(lineLoad.get(i),
					SystemConstants.SwitchFlowGroundLine, SystemConstants.PowerTransformer,true, true, true);
			
			if(didao!=null){
				if(didao.size()>0){
					for(PowerDevice dd:didao){
						RuleExeUtil.deviceStatusExecute(dd, "0", "1");
					}
				}else{
					RuleExeUtil.deviceStatusSet(lineLoad.get(i), "3", "2");
				}
			}
		}
		
		return true;
	}
	/**
	 */
	public List<PowerDevice> orderByLoadline(List<PowerDevice> line){
		for(int i=0;i<line.size();i++){
			if(line.get(i)!=null){
				PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(line.get(i));
				if(lineSwitch!=null &&  lineSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
					line.add(0,line.remove(i));
				}
			}

		}
		
		return line;
	}
	
	/**
	 */
	public List<PowerDevice> orderByLoadlineDesc(List<PowerDevice> line){
		for(int i=0;i<line.size();i++){
			PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(line.get(i));
			if(lineSwitch!= null && lineSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
				line.add(line.size()-1,line.remove(i));
			}
			
		}
		
		return line;
	}
	
	
	
	/**

	 * @param line
	 * @param rbm
	 * @return
	 */
	public boolean loadLine(PowerDevice line) {
		boolean result = true;
		if(!line.getDeviceStatus().equals("0"))
			return true;
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(line, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, true, true);
		if(tfList.size() > 0) { 
			for (PowerDevice tf : tfList) {
				result = loadTransformer(line, tf);
				if(!result)
					return false;
			}
		}
		else {
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			for (PowerDevice ml : mlList) {
				if(!RuleExeUtil.isDeviceOperate(ml)&&!ml.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)&&!ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
					result = loadMotherLine(line, ml);
					if(!result)
						return false;
				}
			}
		}
		return true;
	}
	
	/**
	 * @param srcLine
	 * @param tf
	 * @return
	 */
	public boolean loadTransformer(PowerDevice srcLine, PowerDevice tf) {
		boolean result = true;
		PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(srcLine);
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(tf, lineSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT, "", false, false, false, true, true);
		for(Iterator it = swList.iterator();it.hasNext();) {
			PowerDevice mlsw = (PowerDevice)it.next();
			List<PowerDevice> srcList = RuleExeUtil.getDeviceList(mlsw, lineSwitch, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, false, false, true, true);
			if(srcList.size() == 0)
				it.remove();
		}
		
		PowerDevice dev = null; 
		if(swList.size() == 1)
			dev = swList.get(0);
		else if(swList.size() > 1) {
			EquipCheckChoose ecc = new EquipCheckChoose(SystemConstants.getMainFrame(), true, swList, "请选择["+CBSystemConstants.getPowerStation(srcLine.getPowerStationID()).getPowerStationName()+"]["+srcLine.getPowerDeviceName()+"]停电前合上的开关");
			if(ecc.getChooseEquip()!=null && ecc.getChooseEquip().size() > 0) {
				dev = ecc.getChooseEquip().get(0);
			}
		}
		if(dev != null)
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
		else{
			String endstate =CBSystemConstants.getCurRBM().getEndState();
			if(endstate.equals("3")){
				endstate="2";
			}
			result = RuleExeUtil.deviceStatusExecute(tf, tf.getDeviceStatus(), endstate);
		}
		
		return result;
	}
	
	/**
	 * @param srcLine
	 * @param ml
	 * @return
	 */
	public boolean loadMotherLine(PowerDevice srcLine, PowerDevice busbar) {//线路停电倒负荷
		if(busbar.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
			return true;
		}
		
		if(srcLine.getPowerVoltGrade() == 35){
			boolean ismlkgOff = false;
			List<PowerDevice> rbyxlkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(srcLine.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == srcLine.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(dev.getDeviceStatus().equals("1")){
							RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(),"0");
							ismlkgOff = true;
						}
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(dev.getDeviceStatus().equals("1")){
							rbyxlkgList.add(dev);
						}
					}
				}
			}
			
			if(!ismlkgOff){
				if(rbyxlkgList.size() > 0){
					List<String> defaultStatusList = new ArrayList<String>();
					for(PowerDevice hhzd : rbyxlkgList) {
						defaultStatusList.add(hhzd.getDeviceStatus());
					}
					List<String> expStatusList = new ArrayList<String>();
					for(PowerDevice hhzd : rbyxlkgList) {
						expStatusList.add("3");
					}
					
					Map tagStatusMap = new HashMap();
					if(CBSystemConstants.isCurrentSys) {
						
						EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, rbyxlkgList, defaultStatusList,expStatusList, "如需转电请选择线路开关要转换的状态：");
						tagStatusMap=dialog.getTagStatusMap();
					}
					
					if(tagStatusMap.size()==0){
						return false;
					}
					
					if(tagStatusMap.size()>0){
					   List<Map.Entry<PowerDevice,String>> list = new ArrayList<Map.Entry<PowerDevice,String>>(tagStatusMap.entrySet());
				        //然后通过比较器来实现排序
				        Collections.sort(list,new Comparator<Map.Entry<PowerDevice,String>>() {
				            //升序排序
				            public int compare(Entry<PowerDevice, String> o1,
				                    Entry<PowerDevice, String> o2) {
				                return o1.getValue().compareTo(o2.getValue());
				            }
				            
				        });
						
				        for(Map.Entry<PowerDevice,String> entry:list){
				    		if(!entry.getKey().getDeviceStatus().equals(entry.getValue())){//改变了才做操作
								RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(),entry.getValue());
							}
						}
					}
				}
			}
		}else{
			PowerDevice xlkg = RuleExeUtil.getDeviceSwitch(srcLine);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(busbar, xlkg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
			
			if(xlkg.getPowerDeviceID().equals("4546")){//峨塔矿线停电特殊判断
				for(PowerDevice dev : xlkgList) {
					if(dev.getDeviceStatus().equals("1")&&dev.getPowerDeviceID().equals("4541")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(),"0");
					}
				}
			}else if(xlkg.getPowerDeviceID().equals("4541")){//九塔施线停电特殊判断
				for(PowerDevice dev : xlkgList) {
					if(dev.getDeviceStatus().equals("1")&&dev.getPowerDeviceID().equals("4546")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(),"0");
					}
				}
			}
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(busbar, xlkg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
			
			for(Iterator<PowerDevice> itor = mlkgList.iterator();itor.hasNext();) {
				PowerDevice mlkg = itor.next();
				
				List<PowerDevice> tempkgList = RuleExeUtil.getDeviceList(mlkg, busbar, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true);
				
				if(tempkgList.size() == 0){
					itor.remove();
				}
			}
			
			if(xlkg.getPowerStationID().equals("SS-44")){//110kV四街变特殊判断
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(srcLine.getPowerStationID());
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getPowerVoltGrade() == srcLine.getPowerVoltGrade()){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							if(dev.getDeviceStatus().equals("1")){
								RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(),"0");
								break;
							}
						}
					}
				}
			}else if(xlkg.getPowerDeviceID().equals("1070")){//110kV秀圆河线特殊判断
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, mlkgList , "请选择需要合环的断路器：");
				List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

				if(choosedyckgList.size()==0&&ecc.isCancel()){
					return false;
				}
				
				for(PowerDevice choosedev:choosedyckgList){
					RuleExeUtil.deviceStatusExecute(choosedev, choosedev.getDeviceStatus(), "0");
				}
			}else if(mlkgList.size()==1&&!mlkgList.get(0).getDeviceStatus().equals("0")){
				RuleExeUtil.deviceStatusExecute(mlkgList.get(0), mlkgList.get(0).getDeviceStatus(),"0");
			}else if(mlkgList.size()>0){
				PowerDevice otherMX = RuleUtil.getAnotherMotherLine(mlkgList.get(0), busbar);
				List<PowerDevice> otherswxlList = RuleExeUtil.getDeviceList(otherMX, xlkg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
				for(PowerDevice sw : otherswxlList) {
					if(!sw.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(),"0");
					}else if(!mlkgList.get(0).getDeviceStatus().equals(("0"))){
						RuleExeUtil.deviceStatusExecute(mlkgList.get(0), mlkgList.get(0).getDeviceStatus(),"0");
					}
			
				}
			}
		}
		
		return true;
	}
	
	public void sortListBySwMiddle(List<PowerDevice> swlist){
		if(swlist==null){
			return;
		}else{
		    Collections.sort(swlist, new Comparator<PowerDevice>() {
		        @Override
		        public int compare(PowerDevice p1, PowerDevice p2) {
		          if(RuleExeUtil.isSwMiddleInThreeSecond(p1)) {
		            return -1;
		          }
		          else {
		            return 1;
		          }
		        }
		    });
		}
	}
}
