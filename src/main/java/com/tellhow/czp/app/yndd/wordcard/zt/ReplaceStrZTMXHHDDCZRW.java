package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTMXHHDDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通母线合环调电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			PowerDevice powerdev = new PowerDevice();
			PowerDevice powercutdev = new PowerDevice();
			
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,false, false, true);
			
			mxList.add(curDev);
			
			RuleExeUtil.swapDeviceList(mxList);
			
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC+","+CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			
			for(PowerDevice dev : zbkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					powerdev = dev;
				}
			}

			for(PowerDevice dev : zbkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					powercutdev = dev;
				}
			}
			
			replaceStr += stationName+CZPService.getService().getDevName(mxList)+"由"+CZPService.getService().getDevName(powercutdev)+"供电倒由"+CZPService.getService().getDevName(powerdev)+"供电";
		}
		
		return replaceStr;
	}

}
