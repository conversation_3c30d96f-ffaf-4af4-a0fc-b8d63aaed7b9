package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.km.KMSMJXMXExecute;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKMSMJXMXTDHHCZ implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";

		if("昆明双母接线母线停电合环操作".equals(tempStr)) {
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			
			List<PowerDevice> allmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();

			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
		 	PowerDevice mlkg = new PowerDevice();
		 	if(mlkgList.size()>0){
		 		mlkg = mlkgList.get(0);
		 	}
			PowerDevice gycmlkg = new PowerDevice();

		 	HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&!mlkgList.contains(dev)
						&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
					allmlkgList.add(dev);
				}
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						gycmlkg = dev;
					}
				}
				
				if(dev.getPowerVoltGrade() == 10){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						dycmlkgList.add(dev);
					}
				}
			}
		 	
			if(curDev.getPowerVoltGrade() == 35){
				if(RuleExeUtil.getDeviceBeginStatus(mlkg).equals("1")){
			 		if(KMSMJXMXExecute.tagMap.containsKey("能合环")){
			 			PowerDevice dev = KMSMJXMXExecute.tagMap.get("能合环");
			 			replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
			 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
			 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
			 			//replaceStr +=  "退出"+(int)gycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
				 		replaceStr +=  "退出"+(int)mlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
				 	}else if(KMSMJXMXExecute.tagMap.containsKey("不能合环")){
			 			PowerDevice dev = KMSMJXMXExecute.tagMap.get("不能合环");
			 			if(dycmlkgList.size()>0){
				 			for(PowerDevice dycmlkg : dycmlkgList){
						 		replaceStr += "退出"+CZPService.getService().getDevName(dycmlkg)+"备自投装置/r/n";
					 		}
				 		}		
			 			
				 		replaceStr += "退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				 		replaceStr += "退出"+(int)gycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
			 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				 		replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
				 		replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
				 	}else{
				 		replaceStr += "退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				 		replaceStr += "合上"+CZPService.getService().getDevName(mlkg)+"/r/n";
				 	}
			 	}
			}else if(curDev.getPowerVoltGrade() == 110){
				if(RuleExeUtil.getDeviceBeginStatus(mlkg).equals("0")){
			 		replaceStr = "退出110kV备自投装置";
			 	}else if(RuleExeUtil.getDeviceBeginStatus(mlkg).equals("1")){
			 		if(KMSMJXMXExecute.tagMap.containsKey("能合环")){
			 			PowerDevice dev = KMSMJXMXExecute.tagMap.get("能合环");
			 			replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
			 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
			 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			 			replaceStr += "退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				 	}else{
			 			PowerDevice dev = KMSMJXMXExecute.tagMap.get("不能合环");
				 		replaceStr += "退出110kV备自投装置/r/n";
				 		
				 		Set<String> set = new HashSet<String>();
				 		
				 		for(PowerDevice allmlkg : allmlkgList){
				 			int volt =  (int)allmlkg.getPowerVoltGrade();
				 			set.add(volt+"kV");
				 		}
				 		
				 		for(String str : set){
				 			replaceStr += "退出"+str+"备自投装置/r/n";
				 		}
				 		

			 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				 		replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
				 		
				 		for(String str : set){
				 			replaceStr += "投入"+str+"备自投装置/r/n";
				 		}
				 	}
			 	}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
