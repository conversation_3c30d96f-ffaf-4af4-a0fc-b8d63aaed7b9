package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTDMJXMXTDBZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通单母接线母线停电备注".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			String sql = "SELECT ZYB_DEVID,ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+curDev.getPowerDeviceID()+"'";
			List<Map<String,String>> zybList =  DBManager.queryForList(sql);

			for(Map<String,String> map : zybList){
				String zybName = StringUtils.ObjToString(map.get("ZYB_NAME"));
				replaceStr += "1、操作前核实站用负荷已倒供，"+zybName+"具备停电条件；";
			}
			
			List<PowerDevice> drList = RuleExeUtil.getDeviceList(curDev, SystemConstants.ElecCapacity, SystemConstants.PowerTransformer, true, true, true);

			if(drList.size() > 0){
				if(replaceStr.equals("")){
					replaceStr += "1、操作前核实";
				}else{
					replaceStr += "2、操作前核实";
				}
				
				List<PowerDevice> rbyList = new ArrayList<PowerDevice>(); 
				List<PowerDevice> lbyList = new ArrayList<PowerDevice>(); 

				for(PowerDevice drkg : drList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(drkg).equals("1")){
						rbyList.add(drkg);
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(drkg).equals("2")){
						lbyList.add(drkg);
					}
				}
				
				if(rbyList.size()>0 && lbyList.size()>0){
					replaceStr += CZPService.getService().getDevName(rbyList)+"已处热备用，"+CZPService.getService().getDevName(lbyList)+"已处冷备用";
				}else if(rbyList.size()>0 && lbyList.size()==0){
					replaceStr +=  CZPService.getService().getDevName(rbyList)+"已处热备用";
				}else if(rbyList.size()==0 && lbyList.size()>0){
					replaceStr +=  CZPService.getService().getDevName(lbyList)+"已处冷备用";
				}
			}
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML ,"", false, true, true, true);

			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "注意"+stationName+(int)dev.getPowerVoltGrade()+"kV备自投装置调整";
				}
			}
		}
		
		return replaceStr;
	}

}
