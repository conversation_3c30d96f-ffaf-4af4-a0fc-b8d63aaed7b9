package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHTR10kVBZTZZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河投入10kV备自投装置".equals(tempStr)){
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();

			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							dycmlkgList.add(dev);
						}
					}
				}
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
			
			String num = "";
			
			if(dycmlkgList.size()>0){
				for(PowerDevice zb : zbList){
					List<PowerDevice> zbdycList = RuleExeUtil.getTransformerSwitchLow(zb);
					
					if(zbdycList.size()>1){
						for(PowerDevice zbdyc : zbdycList){
							if(zbdyc.getDeviceStatus().equals("0")){
								String zbdycNum = CZPService.getService().getDevNum(zbdyc);
								
								num = "#"+zbdycNum.substring(zbdycNum.length()-1, zbdycNum.length());
							}
						}
					}
				}
				
				replaceStr += CZPService.getService().getDevName(station)+"@投入"+(int)dycmlkgList.get(0).getPowerVoltGrade()+"kV"+num+"备自投装置/r/n";
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
