package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dq.GCBHDialog;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQPLDG  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆旁路代供".equals(tempStr)){
			if(GCBHDialog.isgcbh){
				List<PowerDevice> lineList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice dev : lineList){
					List<PowerDevice> lineAllList = RuleExeUtil.getLineAllSideList(dev);
					
					for(PowerDevice line : lineAllList){
						String deviceName = CZPService.getService().getDevName(line);			
						
						PowerDevice station = CBSystemConstants.getPowerStation(line.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);

						replaceStr += stationName+"@退出"+deviceName+"光纤差动保护/r/n";
					}
				}
			}
			
			List<PowerDevice> plkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)){
						plkgList.add(dev);
					}
				}
			}
			
			replaceStr += "用"+CZPService.getService().getDevName(plkgList)+"代"+CZPService.getService().getDevName(curDev)+"运行，"+CZPService.getService().getDevName(curDev)+"由运行转"+RuleExeUtil.getStatus(curDev.getDeviceStatus())+"/r/n";
		}
		
		return replaceStr;
	}

}
