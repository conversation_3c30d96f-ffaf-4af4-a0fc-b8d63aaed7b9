package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHZNTDDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河站内停电调电".equals(tempStr)){
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> lossmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> alldrkglist =  new ArrayList<PowerDevice>();
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev);

			List<PowerDevice> curmxlist = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

			if(curmxlist.size()>0){
				zbList = RuleExeUtil.getDeviceList(curmxlist.get(0), SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, true, true);
			}
			
			List<PowerDevice> drkglist =  new ArrayList<PowerDevice>();
			
			if(zbList.size()>0){
				if(RuleUtil.isTransformerNQ(zbList.get(0))){//内桥接线
					List<PowerDevice> zbdyckglist =  RuleExeUtil.getTransformerSwitchLow(zbList.get(0));
					
					PowerDevice hotkg = new PowerDevice();
					List<PowerDevice> zblists =  new ArrayList<PowerDevice>();
					List<PowerDevice> mlkglist =  new ArrayList<PowerDevice>();
					List<PowerDevice> dycmlkglist = new ArrayList<PowerDevice>();//电源侧分段开关
					List<PowerDevice> fhcmlkglist = new ArrayList<PowerDevice>();//负荷侧分段开关
					List<PowerDevice> gycxlkglist = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						
						if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")&&
								(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))){
							hotkg = dev;
						}
						
						if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							mlkglist.add(dev);
						}
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							zblists.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&curDev.getPowerVoltGrade()==dev.getPowerVoltGrade()){
							gycxlkglist.add(dev);
						}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()==dev.getPowerVoltGrade()){
							dycmlkglist.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()>dev.getPowerVoltGrade()){
							fhcmlkglist.add(dev);
						}
					}
					
					if(zbdyckglist.size()>0){
						for(PowerDevice zbdyckg : zbdyckglist){
							List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, false, false);

							for(PowerDevice mx : mxlist){
								if(!lossmxList.contains(mx)){
									lossmxList.add(mx);
								}
								List<PowerDevice> swlist = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
								
								for(PowerDevice sw : swlist){
									if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
										drkglist.add(sw);
									}
								}
								
							}
						}
					}
					
					if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						drkglist.addAll(fhcmlkglist);
					}
					
					if(drkglist.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(drkglist)+"热备用/r/n";
					}
					
					replaceStr += "核实"+CZPService.getService().getDevName(hotkg)+"热备用/r/n";
					
					RuleExeUtil.swapDeviceList(lossmxList);
					
					replaceStr += "红河地调配网调控组@核实"+CZPService.getService().getDevName(lossmxList)+"上属其管辖所有10kV出线断路器运行方式已自行考虑好/r/n";
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "退出XX线路低压解列保护/r/n";
					}
					
					if(zbList.size()>0){
						String volt = "";
						
						for(Double doub : RuleExeUtil.getTransformerVol(zbList.get(0))){
							volt += doub.intValue()+"kV、";
						}
						
						if(volt.endsWith("、")){
							volt = volt.substring(0, volt.length()-1);
						}
						
						replaceStr += "退出"+volt+"备自投装置/r/n";
					}
					
					replaceStr += "红河地调@遥控断开"+stationName+deviceName+"/r/n";
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(hotkg)+"/r/n";
					replaceStr += stationName+"@核实"+deviceName+"热备用/r/n";
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(hotkg)+"运行正常/r/n";
					
					
//					if(curDev.getPowerVoltGrade() == 110){
//						replaceStr += "核实110kV备自投装置充电且运行正常/r/n";
//					}else if(curDev.getPowerVoltGrade() == 35){
//						replaceStr += "核实35kV备自投装置充电且运行正常/r/n";
//					}
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "投入XX线路低压解列保护/r/n";
					}

					if(zbList.size()>0){
						String volt = "";
						
						for(Double doub : RuleExeUtil.getTransformerVol(zbList.get(0))){
							volt += doub.intValue()+"kV、";
						}
						
						if(volt.endsWith("、")){
							volt = volt.substring(0, volt.length()-1);
						}
						
						replaceStr += "投入"+volt+"备自投装置/r/n";
					}
					
					if(zbList.size()>0){
						for(Double doub : RuleExeUtil.getTransformerVol(zbList.get(0))){
							if(doub < curDev.getPowerVoltGrade()){
								replaceStr += "投入"+CZPService.getService().getDevName(zblists)+"第Ⅰ、Ⅱ套"+doub.intValue()+"kV后备保护动作闭锁"+doub.intValue()+"kV备自投装置/r/n";
							}
						}
					}
					
					replaceStr += "投入10kV#1、#2接地变保护动作闭锁10kV备自投装置/r/n";
					
					replaceStr += CommonFunctionHH.getZbNqjxBhStrReplace(zbList,dycmlkglist,gycxlkglist);
				}else{
					PowerDevice hotkg = new PowerDevice();
					List<PowerDevice> gycmlkglist =  new ArrayList<PowerDevice>();
					List<PowerDevice> mlkglist =  new ArrayList<PowerDevice>();
					List<PowerDevice> fhcmlkglist =  new ArrayList<PowerDevice>();
					List<PowerDevice> dycmlkglist = new ArrayList<PowerDevice>();//低压侧分段开关
					List<PowerDevice> zycmlkglist = new ArrayList<PowerDevice>();//中压侧分段开关

					Set<String> set = new HashSet<String>();
					
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						
						if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")&&
								(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))){
							hotkg = dev;
						}
						
						if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							mlkglist.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()>dev.getPowerVoltGrade()){
							fhcmlkglist.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()==dev.getPowerVoltGrade()){
							gycmlkglist.add(dev);
						}
						
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&dev.getPowerVoltGrade()==10){
							dycmlkglist.add(dev);
						}
						
						
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
							alldrkglist.add(dev);
						}
					}
					
					for(PowerDevice fhcmlkg : fhcmlkglist){
						if(!dycmlkglist.contains(fhcmlkg)){
							zycmlkglist.add(fhcmlkg);
						}
					}
					
					for(PowerDevice zb : zbList){
						List<PowerDevice> zbdyckglist =  RuleExeUtil.getTransformerSwitchLow(zb);
						
						if(zbdyckglist.size()>0){
							for(PowerDevice zbdyckg : zbdyckglist){
								if(zbdyckg.getPowerVoltGrade() == 10){
									List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, false, false);

									for(PowerDevice mx : mxlist){
										if(!lossmxList.contains(mx)){
											lossmxList.add(mx);
										}
										List<PowerDevice> swlist = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										
										for(PowerDevice sw : swlist){
											if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
												drkglist.add(sw);
											}
										}
									}
								}
							}
						}
					}
					
					
					if(gycmlkglist.size()==0){
						if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							alldrkglist.addAll(fhcmlkglist);
						}
						
						if(alldrkglist.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(alldrkglist)+"热备用/r/n";
						}
					}else{
						if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							drkglist.addAll(fhcmlkglist);
						}
						
						if(drkglist.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(drkglist)+"热备用/r/n";
						}
					}
					
					replaceStr += "核实"+CZPService.getService().getDevName(hotkg)+"热备用/r/n";
					RuleExeUtil.swapDeviceList(lossmxList);
					
					replaceStr += "红河地调配网调控组@核实"+stationName+"属其管辖所有10kV出线断路器运行方式已自行考虑好，现"+stationName+CZPService.getService().getDevName(lossmxList)+"可以短时停电倒电/r/n";
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "退出XX线路低压解列保护/r/n";
					}
					
					if(dycmlkglist.size()>1&&zycmlkglist.size()>0){
						replaceStr += "退出"+CommonFunctionHH.getBztStrReplace(dycmlkglist)+"、"+CommonFunctionHH.getBztStrReplace(zycmlkglist)+"备自投装置/r/n";
					}else if(dycmlkglist.size()>1&&zycmlkglist.size()==0){
						replaceStr += "退出"+CommonFunctionHH.getBztStrReplace(dycmlkglist)+"备自投装置/r/n";
					}else{
						if(set.size()>0){
							String volt = "";
							
							for(String str : set){
								volt += str+"kV、";
							}
							
							if(volt.endsWith("、")){
								volt = volt.substring(0, volt.length()-1);
							}
							
							replaceStr += "退出"+volt+"备自投装置/r/n";
						}	
					}
					
					replaceStr += "红河地调@遥控断开"+stationName+deviceName+"/r/n";
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(hotkg)+"/r/n";
					replaceStr += stationName+"@核实"+deviceName+"热备用/r/n";
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(hotkg)+"运行正常/r/n";
					
					
//					if(curDev.getPowerVoltGrade() == 110){
//						replaceStr += "核实110kV备自投装置充电且运行正常/r/n";
//					}else if(curDev.getPowerVoltGrade() == 35){
//						replaceStr += "核实35kV备自投装置充电且运行正常/r/n";
//					}
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "投入XX线路低压解列保护/r/n";
					}

					if(dycmlkglist.size()>1&&zycmlkglist.size()>0){
						replaceStr += "投入"+CommonFunctionHH.getBztStrReplace(dycmlkglist)+"、"+CommonFunctionHH.getBztStrReplace(zycmlkglist)+"备自投装置/r/n";
					}else if(dycmlkglist.size()>1&&zycmlkglist.size()==0){
						replaceStr += "投入"+CommonFunctionHH.getBztStrReplace(dycmlkglist)+"备自投装置/r/n";
					}else{
						if(set.size()>0){
							String volt = "";
							
							for(String str : set){
								volt += str+"kV、";
							}
							
							if(volt.endsWith("、")){
								volt = volt.substring(0, volt.length()-1);
							}
							
							replaceStr += "投入"+volt+"备自投装置/r/n";
						}
					}
					
					
					if(zbList.size()>2){
						replaceStr += CommonFunctionHH.getZbHbBhStrReplace(curDev, "midlow","投入");
					}else{
						if(station.getPowerVoltGrade() > 35){
							for(Double doub : RuleExeUtil.getTransformerVol(zbList.get(0))){
								if(doub < curDev.getPowerVoltGrade()){
									replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+doub.intValue()+"kV后备保护动作闭锁"+doub.intValue()+"kV备自投装置/r/n";
								}
							}
						}
					}
					
					if(CommonFunctionHH.getZbIsJdzybStrReplace(zbList.get(0))){
						replaceStr += "投入10kV#1、#2接地变保护动作闭锁10kV备自投装置/r/n";
					}
					
					if(curDev.getPowerVoltGrade() == 110){
						replaceStr += "投入110kV母差保护动作闭锁110kV备自投装置/r/n";
					}else if(curDev.getPowerVoltGrade() == 35){
						replaceStr += "核实35kV备自投装置充电且运行正常";
					}
					
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}