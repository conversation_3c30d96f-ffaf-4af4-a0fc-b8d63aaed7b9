package com.tellhow.czp.app.yndd.wordcard.km;


import com.tellhow.czp.app.yndd.rule.km.TransformTDKindChoose;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooMXDSTD implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		
		if(TransformTDKindChoose.tdflag.equals("母线短时停电")){
			return true;
		}
		
		return false;
	}
}
