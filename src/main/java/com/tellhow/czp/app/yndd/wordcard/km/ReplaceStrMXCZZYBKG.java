package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMXCZZYBKG implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("母线操作站用变开关".equals(tempStr)){
			
			List<PowerDevice> kglist =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = kglist.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")){
						if((RuleExeUtil.getDeviceEndStatus(dev).equals("1")||RuleExeUtil.getDeviceEndStatus(dev).equals("2"))&&RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr = CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev);
							break;
						}else if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")&&RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr = CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev);
							break;
						}else if((RuleExeUtil.getDeviceEndStatus(dev).equals("0")||RuleExeUtil.getDeviceEndStatus(dev).equals("1"))&&RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							replaceStr = CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev);
							break;
						}
					}
				}
			}
	    }
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
