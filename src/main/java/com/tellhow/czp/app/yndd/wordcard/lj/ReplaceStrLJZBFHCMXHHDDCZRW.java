package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJZBFHCMXHHDDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江主变负荷侧母线合环倒电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			String otherzbName = "";
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						if(!curDev.getPowerDeviceID().equals(dev.getPowerDeviceID())){
							otherzbList.add(dev);
							
							otherzbName = CZPService.getService().getDevName(dev);
							break;
						}
					}
				}
			}
			
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> zycmxList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
				List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
				
				for(PowerDevice zbzyckg : zbzyckgList){
					zycmxList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				}
				
				for(PowerDevice zbdyckg : zbdyckgList){
					dycmxList = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				}
			}
			
			String mxNames = "";
			
			for(PowerDevice dev : zycmxList){
				mxNames += CZPService.getService().getDevName(dev) + "、";
			}
			
			for(PowerDevice dev : dycmxList){
				mxNames += CZPService.getService().getDevName(dev) + "、";
			}
			
			if(mxNames.endsWith("、")){
				mxNames = mxNames.substring(0, mxNames.length() - 1);
			}
			
			replaceStr += stationName + mxNames +"由"+deviceName+"供电倒由"+otherzbName+"供电";
		}
		
		return replaceStr;
	}

}
