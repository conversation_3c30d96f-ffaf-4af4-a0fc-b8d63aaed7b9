package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTXLHHDDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通线路合环调电操作任务".equals(tempStr)){
			List<PowerDevice> allLineList =  RuleExeUtil.getLineAllSideList(curDev);

			PowerDevice powercutdev = new PowerDevice();
			PowerDevice powerdev = new PowerDevice();

			PowerDevice beginStation = new PowerDevice();
			PowerDevice endStation = new PowerDevice();
			
			for(PowerDevice dev : allLineList){
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlkg : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){//断点
						powerdev = xlkg;
					}else if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("1")){
						powercutdev = xlkg;
					}
				}
			}
			
			List<PowerDevice> beginLineList = RuleExeUtil.getDeviceList(powercutdev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> endLineList = RuleExeUtil.getDeviceList(powerdev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			
			for(PowerDevice dev : beginLineList){
				PowerDevice curstation = CBSystemConstants.getPowerStation(dev.getPowerStationID());

				if(curstation.getPowerVoltGrade() == 220){
					beginStation = curstation;
					break;
				}
				
				List<PowerDevice> otherLineList =  RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, false, true);
				
				for(PowerDevice other : otherLineList){
					List<PowerDevice> anotherLineList =  RuleExeUtil.getLineOtherSideList(other);
					
					for(PowerDevice another : anotherLineList){
						PowerDevice anotherstation = CBSystemConstants.getPowerStation(another.getPowerStationID());
						
						if(anotherstation.getPowerVoltGrade() == 220){
							beginStation = anotherstation;
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : endLineList){
				PowerDevice curstation = CBSystemConstants.getPowerStation(dev.getPowerStationID());

				if(curstation.getPowerVoltGrade() == 220){
					endStation = curstation;
					break;
				}
				
				List<PowerDevice> otherLineList =  RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, false, true);
				
				for(PowerDevice other : otherLineList){
					List<PowerDevice> anotherLineList =  RuleExeUtil.getLineOtherSideList(other);
					
					for(PowerDevice another : anotherLineList){
						PowerDevice anotherstation = CBSystemConstants.getPowerStation(another.getPowerStationID());
						
						if(anotherstation.getPowerVoltGrade() == 220){
							endStation = anotherstation;
							break;
						}
					}
				}
			}
			
			String begin = StringUtils.killVoltInDevName(CZPService.getService().getDevName(beginStation));
			String end = StringUtils.killVoltInDevName(CZPService.getService().getDevName(endStation));

			if(begin.startsWith("西")&&end.startsWith("昭")){
				replaceStr = "220kV/110kV昭西电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}else if(begin.startsWith("昭")&&end.startsWith("西")){
				replaceStr = "220kV/110kV昭西电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}
			
			if(begin.startsWith("西")&&end.startsWith("发")){
				replaceStr = "220kV/110kV西发电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}else if(begin.startsWith("发")&&end.startsWith("西")){
				replaceStr = "220kV/110kV西发电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}
			
			if(begin.startsWith("大")&&end.startsWith("发")){
				replaceStr = "220kV/110kV发大电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}else if(begin.startsWith("发")&&end.startsWith("大")){
				replaceStr = "220kV/110kV发大电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}
			
			if(begin.startsWith("北")||end.startsWith("北")){
				replaceStr = "220kV/110kV北盐电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}
			
			if(begin.startsWith("镇")&&end.startsWith("徐")){
				replaceStr = "220kV/110kV镇徐电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}else if(begin.startsWith("徐")&&end.startsWith("镇")){
				replaceStr = "220kV/110kV镇徐电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}
			
			if(begin.startsWith("镇")&&end.startsWith("安")){
				replaceStr = "220kV/110kV安镇电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}else if(begin.startsWith("安")&&end.startsWith("镇")){
				replaceStr = "220kV/110kV安镇电磁环网断点由"+CZPService.getService().getDevName(powerdev)+"改为"+CZPService.getService().getDevName(powercutdev);
			}
		}
		
		return replaceStr;
	}

}
