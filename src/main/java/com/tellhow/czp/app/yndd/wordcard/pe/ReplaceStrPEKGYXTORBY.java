package com.tellhow.czp.app.yndd.wordcard.pe;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEKGYXTORBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱开关运行转热备用".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String kgName = CZPService.getService().getDevName(curDev);
			replaceStr += "普洱地调@遥控断开"+voltStationName+kgName+"/r/n";
		}
		
		return replaceStr;
	}

}
