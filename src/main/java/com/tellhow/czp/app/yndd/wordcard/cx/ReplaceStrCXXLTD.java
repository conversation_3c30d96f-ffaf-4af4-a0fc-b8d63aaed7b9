package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionCX;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.cx.JDKGXZCX;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXXLTD implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄线路停电".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());

			List<Map<String, String>> stationLineList = CommonFunctionCX.getStationLineList(curDev);
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			
			kgList.addAll(xlkgList);
			kgList.addAll(zbkgList);

			boolean isRunModelThreeTwo = false;
			
			for(PowerDevice dev : kgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					isRunModelThreeTwo = true;
					break;
				}
			}
			
			List<String> bztList = new ArrayList<String>();
			
			if(isRunModelThreeTwo){
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						replaceStr += stationName+"@断开"+lowerunit+switchName+"。/r/n";
					}else if(operationkind.equals("许可")){
						if(!switchName.equals("")){
							replaceStr += stationName+"@检查"+lowerunit+switchName+"在冷备用状态。/r/n";
						}else{
							replaceStr += stationName+"@检查"+lowerunit+disconnectorName+"在拉开位置。/r/n";
						}
					}else if(operationkind.equals("配合")){
						replaceStr += stationName+"@检查"+lowerunit+switchName+"在热备用状态。/r/n";
					}
				}
				
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> xlkgLoadList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					for(PowerDevice dev : xlkgLoadList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							String deviceName = CZPService.getService().getDevName(dev);
							replaceStr += "楚雄地调@遥控断开"+stationName+deviceName+"。/r/n";
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "楚雄地调@遥控断开"+stationName+deviceName+"。/r/n";
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "楚雄地调@遥控断开"+stationName+deviceName+"。/r/n";
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							if(CommonFunctionCX.ifSwitchSeparateControl(dev)){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "楚雄地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作。/r/n";
								
								if(curDev.getPowerVoltGrade() > 110){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									dzList = RuleExeUtil.sortByXLC(dzList);
									
									for(PowerDevice dz : dzList){
										if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
											replaceStr += stationName+"@检查"+CZPService.getService().getDevName(dz)+"在拉开位置。/r/n";
										}
									}
								}
							}else{
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += stationName+"@"+deviceName+"由热备用转冷备用。/r/n";
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						replaceStr += stationName+"@"+lowerunit+switchName+"由热备用转冷备用。/r/n";
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					replaceStr += stationName+"@"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制前的要求进行设置。/r/n";
					
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							
							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "楚雄地调@遥控拉开"+stationName+devname+"。/r/n";
									replaceStr += stationName+"@检查"+devname+"在拉开位置。/r/n";
								}
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							
							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "楚雄地调@遥控拉开"+stationName+devname+"。/r/n";
									replaceStr += stationName+"@检查"+devname+"在拉开位置。/r/n";
								}
							}
						}
					}
					
					replaceStr += stationName+"@"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制后的要求进行设置。/r/n";
				}
			}else{
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					replaceStr += CommonFunctionCX.getTotalStationMaintenance(stationName);
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						replaceStr += CommonFunctionCX.getCheckSwitchPosition(dev, stationName);
					}
					
					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+sourceLineTrans.getPowerDeviceID()+"'";
					List<Map<String,String>> zybNameList = DBManager.queryForList(sql);
					
					sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE STATION_ID = '"+station.getPowerDeviceID()+"' AND DEV_ID != '"+sourceLineTrans.getPowerDeviceID()+"'";
					List<Map<String,String>> otherzybNameList = DBManager.queryForList(sql);
					
					for(Map<String,String> zybNameMap : otherzybNameList){
						String zybName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
						replaceStr += stationName+"@检查"+zybName+"供电正常。/r/n";
					}
					
				    for(Map<String,String> zybNameMap : zybNameList){
			    		String zybName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
			    		
			    		replaceStr += stationName+"@做好"+zybName+"停电准备。/r/n";
			    		replaceStr += stationName+"@"+zybName+"由运行转冷备用。/r/n";
				    }
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> lineList = new ArrayList<PowerDevice>();
					List<PowerDevice> zbList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
							if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
								lineList.add(dev);
							}else if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
								zbList.add(dev);
							}
						}
					}
					
					if(lineList.size() == 1){
						String lineName = CZPService.getService().getDevName(loadLineTran);
						replaceStr += stationName+"@做好"+lineName+"停电准备。/r/n";
						
						for(PowerDevice dev : zbList){
							List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
							List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
							List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
							
							for(PowerDevice zbdyckg : zbdyckgList){
								replaceStr += CommonFunctionCX.getCheckSwitchPosition(zbdyckg, stationName);
							}
							
							for(PowerDevice zbzyckg : zbzyckgList){
								replaceStr += CommonFunctionCX.getCheckSwitchPosition(zbzyckg, stationName);
							}
							
							for(PowerDevice zbgyckg : zbgyckgList){
								replaceStr += CommonFunctionCX.getCheckSwitchPosition(zbgyckg, stationName);
							}
						}
					}
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						replaceStr += CommonFunctionCX.getCheckSwitchPosition(dev, stationName);
					}
					
					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+loadLineTran.getPowerDeviceID()+"'";
					List<Map<String,String>> zybNameList = DBManager.queryForList(sql);
					
					sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE STATION_ID = '"+station.getPowerDeviceID()+"' AND DEV_ID != '"+loadLineTran.getPowerDeviceID()+"'";
					List<Map<String,String>> otherzybNameList = DBManager.queryForList(sql);
					
					for(Map<String,String> zybNameMap : otherzybNameList){
						String zybName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
						replaceStr += stationName+"@检查"+zybName+"供电正常。/r/n";
					}
					
				    for(Map<String,String> zybNameMap : zybNameList){
			    		String zybName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
			    		
			    		replaceStr += stationName+"@做好"+zybName+"停电准备。/r/n";
			    		replaceStr += stationName+"@"+zybName+"由运行转冷备用。/r/n";
				    }
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					lowerunit = StringUtils.killVoltInDevName(lowerunit);
					//特殊判断
					replaceStr += CommonFunctionCX.getCheckLineNormal(lineName, stationName, lowerunit);
					replaceStr += stationName+"@做好"+lowerunit+lineName+"停电准备。/r/n";

					if(operationkind.equals("下令")){
						if(!switchName.equals("")){
							replaceStr += stationName+"@断开"+lowerunit+switchName+"。/r/n";
						}
					}else if(operationkind.equals("许可")){
						if(!disconnectorName.equals("")){//非标准接线
							if(!switchName.equals("")){
								replaceStr += stationName+"@检查"+lowerunit+switchName+"在分闸位置。/r/n";
							}
							
							if(disconnectorName.contains("、")){
								String[] disconnectorNameArr = disconnectorName.split("、");
								
								for(String dzName : disconnectorNameArr){
									replaceStr += stationName+"@检查"+lowerunit+dzName+"在拉开位置。/r/n";
								}
							}else{
								replaceStr += stationName+"@检查"+lowerunit+disconnectorName+"在拉开位置。/r/n";
							}
						}else{
							if(!switchName.equals("")){
								replaceStr += stationName+"@检查"+lowerunit+switchName+"在冷备用状态。/r/n";
							}
						}
					}else if(operationkind.equals("配合")){
						if(!switchName.equals("")){
							if(switchName.contains("、")){
								String[] switchNameArr = switchName.split("、");
								
								for(String kgName : switchNameArr){
									replaceStr += stationName+"@落实"+lowerunit+kgName+"在热备用状态。/r/n";
								}
							}else{
								replaceStr += stationName+"@落实"+lowerunit+switchName+"在热备用状态。/r/n";
							}
						}
						replaceStr += stationName+"@检查"+lowerunit+lineName+"具备停电条件。/r/n";
					}
				}
				
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String voltStationName = CZPService.getService().getDevName(station); 
					String stationName = StringUtils.killVoltInDevName(voltStationName); 
					
					List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								hignVoltMlkgList.add(dev);
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								hignVoltXlkgList.add(dev);
							}
						}
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(hignVoltXlkgList);
					tempList.addAll(hignVoltMlkgList);

					for(PowerDevice dev : tempList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
							String bztContent = voltStationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置。/r/n";
							bztList.add(bztContent);
							replaceStr += voltStationName+"@检查"+CZPService.getService().getDevName(dev)+"在热备用状态，具备供电条件。/r/n";
							replaceStr += CommonFunctionCX.getHhContent(dev,"楚雄地调", voltStationName);
						}
					}
					
					for(PowerDevice dev : tempList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionCX.getSwitchOffContent(dev, voltStationName);
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String voltStationName = CZPService.getService().getDevName(station); 
					String stationName = StringUtils.killVoltInDevName(voltStationName); 
					
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							if(dev.getPowerStationID().equals("SS-4")){//特殊判断
								if(dev.getPowerDeviceName().contains("220kV谢楚北牵线221断路器")){
									replaceStr += voltStationName+"@检查220kV谢楚北牵线旁母侧2214隔离开关在拉开位置。/r/n";
								}
							}
							
							replaceStr += CommonFunctionCX.getSwitchOffContent(dev, voltStationName);
						}
					}
				}
				
				/*
				 * 热备用转冷备用
				 */
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String voltStationName = CZPService.getService().getDevName(station); 
					String stationName = StringUtils.killVoltInDevName(voltStationName); 
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					List<PowerDevice> zbswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
					List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

					if(xldzList.size() == 1 && xlswList.size() == 0){
						for(PowerDevice dz : xldzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								replaceStr += voltStationName+"@拉开"+CZPService.getService().getDevName(dz)+"。/r/n";
							}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dz).equals("1")){
								replaceStr += voltStationName+"@检查"+CZPService.getService().getDevName(dz)+"在拉开位置。/r/n";
							}
						}
					}else if(xldzList.size() == 1 && xlswList.size() == 1){
						for(PowerDevice dev : xlswList){
							String switchStatus = "，"+CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatusNew(dev.getDeviceType(), dev.getDeviceStatus());
							String deviceName = CZPService.getService().getDevName(dev);
							replaceStr += voltStationName+"@"+deviceName+"由热备用转冷备用"+switchStatus+"。/r/n";
						}
					}else if(zbswList.size() == 1 && xlswList.size() == 0){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbswList.get(0), SystemConstants.SwitchSeparate);

						if(dzList.size() == 1){
							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
									replaceStr += voltStationName+"@拉开"+CZPService.getService().getDevName(dz)+"。/r/n";
								}
							}
						}
					}else{
						for(PowerDevice dev : xlswList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")||RuleExeUtil.getDeviceEndStatus(dev).equals("3")){
								if(CommonFunctionCX.ifSwitchSeparateControl(dev)){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += CommonFunctionCX.getCheckBeforeContent(deviceName,voltStationName,"");
									replaceStr += "楚雄地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作。/r/n";
									replaceStr += CommonFunctionCX.getKnifeOffCheckContent(dev);
								}else{
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += voltStationName+"@"+deviceName+"由热备用转冷备用。/r/n";
								}
							}
						}
					}
					
					String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+loadLineTran.getPowerDeviceID()+"'";
					List<Map<String,String>> zybNameList=DBManager.queryForList(sql);
					    
				    for(Map<String,String> zybNameMap : zybNameList){
			    		String zybName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
			    		replaceStr += voltStationName+"@"+zybName+"由运行转冷备用。/r/n";
				    }
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String voltStationName = CZPService.getService().getDevName(station); 
					String stationName = StringUtils.killVoltInDevName(voltStationName); 
					List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

					if(xldzList.size() > 0){
						for(PowerDevice dev : xlkgList){
							String switchStatus = "，"+CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatusNew(dev.getDeviceType(), dev.getDeviceStatus());
							String deviceName = CZPService.getService().getDevName(dev);
							replaceStr += voltStationName+"@"+deviceName+"由热备用转冷备用"+switchStatus+"。/r/n";
						}
					}else{
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunctionCX.ifSwitchSeparateControl(dev)){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += CommonFunctionCX.getCheckBeforeContent(deviceName,voltStationName,"");
									replaceStr += "楚雄地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作。/r/n";
									replaceStr += CommonFunctionCX.getKnifeOffCheckContent(dev);
								}else{
									if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
										String deviceName = CZPService.getService().getDevName(dev);
										replaceStr += voltStationName+"@"+deviceName+"由热备用转冷备用。/r/n";
									}
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String ptdisconnectorName = StringUtils.ObjToString(map.get("PTDISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					lowerunit = StringUtils.killVoltInDevName(lowerunit);
					
					if(operationkind.equals("下令")){
						if(!disconnectorName.equals("")){
							replaceStr += stationName+"@拉开"+lowerunit+disconnectorName+"。/r/n";
						}else if(!switchName.equals("")){
							replaceStr += stationName+"@"+lowerunit+switchName+"由热备用转冷备用。/r/n";
						}
						
						if(!ptdisconnectorName.equals("")){
							if(ptdisconnectorName.contains("、")){
								String[] ptdisconnectorNameArr = ptdisconnectorName.split("、");
								
								for(String dzName : ptdisconnectorNameArr){
									replaceStr += stationName+"@"+lowerunit+dzName+"由运行转冷备用。/r/n";
								}
							}else{
								if(ptdisconnectorName.contains("站用变")){
									replaceStr += stationName+"@"+lowerunit+ptdisconnectorName+"由运行转冷备用。/r/n";
								}else{
									replaceStr += stationName+"@"+lowerunit+ptdisconnectorName+"由运行转冷备用。/r/n";
								}
							}
						}
					}else if(operationkind.equals("许可")){
						if(!ptdisconnectorName.equals("")){
							if(ptdisconnectorName.contains("、")){
								String[] ptdisconnectorNameArr = ptdisconnectorName.split("、");
								
								for(String dzName : ptdisconnectorNameArr){
									if(dzName.contains("隔离开关")){
										replaceStr += stationName+"@检查"+lowerunit+dzName+"在拉开位置。/r/n";
									}else{
										replaceStr += stationName+"@检查"+lowerunit+dzName+"在冷备用状态。/r/n";
									}
								}
							}else{
								if(ptdisconnectorName.contains("隔离开关")){
									replaceStr += stationName+"@检查"+lowerunit+ptdisconnectorName+"在拉开位置。/r/n";
								}else{
									replaceStr += stationName+"@检查"+lowerunit+ptdisconnectorName+"在冷备用状态。/r/n";
								}
							}
						}
					}else if(operationkind.equals("配合")){
						if(!switchName.equals("")){
							if(switchName.contains("、")){
								String[] switchNameArr = switchName.split("、");
								
								for(String kgName : switchNameArr){
									replaceStr += stationName+"@通报进行"+lineName+"由热备用转冷备用操作。/r/n";
								}
							}else{
								replaceStr += stationName+"@通报进行"+lineName+"由热备用转冷备用操作。/r/n";
							}
						}
					}
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 
				String lineName = CZPService.getService().getDevName(sourceLineTrans); 

				replaceStr += CommonFunctionCX.getLightDifferentialProtect(lineName,stationName,"退出");
			}
			
			for(PowerDevice dev : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				String lineName = CZPService.getService().getDevName(dev); 

				replaceStr += CommonFunctionCX.getLightDifferentialProtect(lineName,stationName,"退出");
			}
			
			for(Map<String, String> map : stationLineList) {
				String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
				String lowerUnit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
				String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();

				if(lowerUnit.equals("")){
					replaceStr += CommonFunctionCX.getLightDifferentialProtect(lineName,stationName,"退出");
				}
			}
			
			for(PowerDevice dev : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);

				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					String deviceName = CZPService.getService().getDevName(xlsw);
					replaceStr += CommonFunctionCX.getOpenFailureProtect(deviceName,stationName);
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station); 

				for(PowerDevice xlsw : xlkgList){
					String deviceName = CZPService.getService().getDevName(xlsw);
					replaceStr += CommonFunctionCX.getOpenFailureProtect(deviceName,stationName);
				}
			}
			
			/*
			 * 冷备用转检修
			 */
			
			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("3")){
				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
					
					if(jddzList != null){
						if(JDKGXZCX.chooseEquips.contains(dev)||jddzList.size()==0){
						  replaceStr += stationName+"@在"+CZPService.getService().getDevName(curDev)+"线路侧装设一组接地线。/r/n";
						}else{
						  replaceStr += stationName+"@合上"+CZPService.getService().getDevName(jddzList.get(0))+"。/r/n";
					 	}
					}
				}
				
				for(Map<String,String> map : stationLineList){
					String unit = StringUtils.ObjToString(map.get("UNIT"));
					String linename = StringUtils.ObjToString(map.get("LINE_NAME"));
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
					String grounddisconnectorname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));
					String endpointtype = StringUtils.ObjToString(map.get("ENDPOINT_TYPE"));

					lowerunit = StringUtils.killVoltInDevName(lowerunit);
					
					boolean zsccdx =  false;
					
					for(PowerDevice dev : JDKGXZCX.chooseEquips){
						if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
							zsccdx = true;
							replaceStr += unit+"@在"+lowerunit+linename+"线路侧装设一组接地线。/r/n";
							break;
						 }
					}
					
					if(!zsccdx){
						if(operationkind.equals("下令")){
							if(!grounddisconnectorname.equals("")){
								replaceStr += unit+"@合上"+lowerunit+grounddisconnectorname+"。/r/n";
							}else{
								replaceStr += unit+"@在"+lowerunit+linename+"线路侧装设一组接地线。/r/n";
							}
						}else if(operationkind.equals("许可")){
							if(grounddisconnectorname.equals("")){
								replaceStr += unit+"@在"+lowerunit+linename+"线路侧装设一组接地线。/r/n";
							}else{
								replaceStr += unit+"@合上"+lowerunit+grounddisconnectorname+"。/r/n";
							}
						}else if(operationkind.equals("配合")){
							replaceStr += unit+"@通报进行"+linename+"由冷备用转检修操作。/r/n";
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					  
					if(JDKGXZCX.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
						 replaceStr += stationName+"@在"+CZPService.getService().getDevName(curDev)+"线路侧装设一组接地线。/r/n";
					}else{
						 replaceStr += stationName+"@合上"+CZPService.getService().getDevName(jddzList.get(0))+"。/r/n";
					}
				}
				
				for(Map<String,String> map : stationLineList){
					String unit = StringUtils.ObjToString(map.get("UNIT"));
					String linename = StringUtils.ObjToString(map.get("LINE_NAME"));
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
					String grounddisconnectorname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));
					String endpointtype = StringUtils.ObjToString(map.get("ENDPOINT_TYPE"));
					
					if(operationkind.equals("配合")){
						if(linename.equals("110kV狮牡禄线")){
							replaceStr += unit+"@通报"+linename+"狮山变、牡丹变侧已操作至检修状态。/r/n";
						}
						
						replaceStr += unit+"@落实"+linename+lowerunit+"侧在检修状态。/r/n";
					}
				}
			}
			
			for(String bzt : bztList){
				 replaceStr += bzt;
			}
			
			 replaceStr += "楚雄地调@"+CZPService.getService().getDevName(curDev)+"停电设备各侧挂牌。/r/n";
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

	
	
}
