package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dq.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQSMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆双母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getTransformerKnifeLoad(curDev);
			List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdzList){
				if(dev.getPowerVoltGrade() == lowvolt){
					zbdycdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}
			}
			
			boolean isSwitchSeparateNotControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
					isSwitchSeparateNotControl = false;
				}
			}
			
			replaceStr += stationName+"@落实迪庆供电局-XXXXXX检修申请工作任务已结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），该设备的二次装置已正常投入，确认设备具备送电条件/r/n";
			
			for(PowerDevice dev : zxdjddzList){
				String devName = CZPService.getService().getDevName(dev);

				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@落实"+devName+"具备遥控操作条件/r/n";
					replaceStr += "迪庆地调@遥控合上"+stationName+devName+"/r/n";
					replaceStr += stationName+"@确认"+devName+"在合闸位置/r/n";
				}
			}

			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				if(isSwitchSeparateNotControl){
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}else{
					for(PowerDevice dev : zbgyckgList){
						if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
							replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"间隔具备程序化控制条件/r/n";
							replaceStr += "迪庆地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbgyckgList,stationName);
							break;
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
							replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"间隔具备程序化控制条件/r/n";
							replaceStr += "迪庆地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbgyckgList,stationName);
							break;
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
							replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"间隔具备程序化控制条件/r/n";
							replaceStr += "迪庆地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbgyckgList,stationName);
							break;
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						if(CommonFunctionDQ.ifSwitchControl(dev)){
							replaceStr += "迪庆地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对主变充电/r/n";
						}
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"在合上位置/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						if(CommonFunctionDQ.ifSwitchControl(dev)){
							replaceStr += CommonFunctionDQ.getHhContent(dev, "迪庆地调", stationName);
						}
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						if(CommonFunctionDQ.ifSwitchControl(dev)){
							replaceStr += CommonFunctionDQ.getHhContent(dev, "迪庆地调", stationName);
						}
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			for(PowerDevice dev : zxdjddzList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "迪庆地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"在分闸位置/r/n";
				}
			}

			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
