package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.TransformKDXLChoose;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

/**  
  用户厂站名称检修到冷备
* <p>Description: </p>  
* <AUTHOR>
* @date 2021年9月2日    
*/
public class ReplaceStrYHCZMC_3T2 implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String result = "";

		TransformKDXLChoose kdxl = new TransformKDXLChoose();

		String curLineId = curDev.getPowerDeviceID();
		String devName = CZPService.getService().getDevName(curDev);
		String sql = "SELECT DEVICE_NUM,UNIT,OPERATION_KIND,LOWERUNIT,ENDPOINT_KIND,VOLTAGE FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (  SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+curLineId+"')";
		List<Map<String, Object>> stations = DBManager.queryForList(sql);
		if(stations != null && stations.size()>0) {
			
			if(kdxl.retMap.size()>0){
				for(Iterator<Map<String, Object>> itor = stations.iterator();itor.hasNext();){
					Map<String, Object> map = itor.next();
					String stationName = StringUtils.ObjToString(map.get("UNIT"));

					if(!kdxl.retMap.containsKey(stationName)){
						itor.remove();
					}
				}
			}
			
			for(Map<String, Object> station:stations) {
				String stationName = String.valueOf(station.get("UNIT")).trim();
				String stationKind = String.valueOf(station.get("OPERATION_KIND")).trim();
				String deviceNum = String.valueOf(station.get("DEVICE_NUM")).trim();
				String lowerunit = StringUtils.ObjToString(station.get("LOWERUNIT")).trim();
				String endpointkind = StringUtils.ObjToString(station.get("ENDPOINT_KIND")).trim();
				String voltage = StringUtils.ObjToString(station.get("VOLTAGE")).trim();

				if(stationKind.equals("下令")){
					if(endpointkind.equals("外接站用变")){
						result += (stationName+"@拆除"+devName+"线路侧三相短路接地线" + "/r/n");
					}else{
						if(voltage.equals("35")){
							result += (stationName+"@拆除"+devName+"线路侧三相短路接地线" + "/r/n");
						}else{
							if(lowerunit.equals("")){
								result += (stationName + "@拉开"+devName+deviceNum+"67线路接地开关" + "/r/n");
							}else{
								result += (stationName + "@拉开"+devName+deviceNum+"67线路接地开关" + "/r/n");
							}
						}
					}
				}else if(stationKind.equals("许可")){
					result += (stationName + "@许可将"+ devName +"由检修转冷备用" + "/r/n");
				}else if(stationKind.equals("落实")){
					if(endpointkind.equals("其它供电局")){
						result += (stationName + "@落实"+ devName +"已由检修转冷备用" + "/r/n");
					}else{
						result += (stationName + "@落实"+ devName +"已处冷备用" + "/r/n");
					}
				}
			}
		}
		
		if(result.equals("")){
			result = null;
		}
		
		return result;
	}

}
