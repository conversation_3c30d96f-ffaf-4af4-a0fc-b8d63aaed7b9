package com.tellhow.czp.app.yndd.wordcard.dl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDLSMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("大理双母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			PowerDevice othermx = new PowerDevice();
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.isSwitchDoubleML(dev)){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					for(PowerDevice mx : mxList){
						if(!mx.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
							othermx = mx ;
							break;
						}
					}
				}
			}
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);

			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "大理地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环/r/n";
					replaceStr += "大理地调@检查"+stationName+CZPService.getService().getDevName(dev)+"三相潮流指示正常/r/n";
				}
			}
			
			replaceStr += stationName+"@将"+deviceName+"上运行的所有断路器倒至"+CZPService.getService().getDevName(othermx)+"上运行/r/n";
			
			for(PowerDevice dev : xlkgList){
				if(dev.getDeviceStatus().equals("1")){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
					
					if(mxList.contains(othermx)){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由"+deviceName+"热备用倒至"+CZPService.getService().getDevName(othermx)+"热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				if(dev.getDeviceStatus().equals("1")){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
					
					if(mxList.contains(othermx)){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由"+deviceName+"热备用倒至"+CZPService.getService().getDevName(othermx)+"热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : mlkgList){
				replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				replaceStr += stationName+"@"+deviceName+"由热备用转冷备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
