package com.tellhow.czp.app.yndd.wordcard.hh;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTCCHZ implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		
		String replaceStr = "";
		
		if("退出重合闸".equals(tempStr)){
			
			replaceStr = curDev.getPowerDeviceName().replace(curDev.getPowerStationName() , "" )+"重合闸"+"/r/n";
			
		}
		
		return replaceStr;
	}

}
