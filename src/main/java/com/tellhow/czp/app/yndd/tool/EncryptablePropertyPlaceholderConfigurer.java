package com.tellhow.czp.app.yndd.tool;

import java.util.Properties;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;

public class EncryptablePropertyPlaceholderConfigurer extends PropertyPlaceholderConfigurer {
    protected void processProperties(ConfigurableListableBeanFactory beanFactory, Properties props)
        throws BeansException {
            try {
                String username = props.getProperty(MyWebConstant.JDBC_DATASOURCE_USERNAME_KEY);
                if (username != null) {
//                    props.setProperty(MyWebConstant.JDBC_DATASOURCE_USERNAME_KEY, "opcardhh");
                    props.setProperty(MyWebConstant.JDBC_DATASOURCE_USERNAME_KEY, "OPCARDBS");
                }
                
                String password = props.getProperty(MyWebConstant.JDBC_DATASOURCE_PASSWORD_KEY);
                if (password != null) {
//                    props.setProperty(MyWebConstant.JDBC_DATASOURCE_PASSWORD_KEY, "opcardhh");
                    props.setProperty(MyWebConstant.JDBC_DATASOURCE_PASSWORD_KEY, "OPCARDBS");
                }
                
                String url = props.getProperty(MyWebConstant.JDBC_DATASOURCE_URL_KEY);
                if (url != null) {
//                    props.setProperty(MyWebConstant.JDBC_DATASOURCE_URL_KEY, "*****************************************");
//                    props.setProperty(MyWebConstant.JDBC_DATASOURCE_URL_KEY, "******************************************");
                    props.setProperty(MyWebConstant.JDBC_DATASOURCE_URL_KEY, "****************************************");
                }
                
                super.processProperties(beanFactory, props);
            } catch (Exception e) {
                e.printStackTrace();
                throw new BeanInitializationException(e.getMessage());
            }
        }
    }
