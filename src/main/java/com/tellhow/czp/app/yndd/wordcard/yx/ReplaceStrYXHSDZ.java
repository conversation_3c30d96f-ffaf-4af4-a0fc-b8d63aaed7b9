package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXHSDZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪合上刀闸".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			if(CommonFunction.ifSwitchSeparateControl(curDev)){
				List<PowerDevice> dzList = new ArrayList<PowerDevice>();
				dzList.add(curDev);
				
				replaceStr += "玉溪地调@遥控合上"+stationName+deviceName+"/r/n";
				replaceStr += CommonFunction.getKnifeOnCheckContent(dzList , stationName);
			}else{
				replaceStr += stationName+"@合上"+deviceName+"/r/n";
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
