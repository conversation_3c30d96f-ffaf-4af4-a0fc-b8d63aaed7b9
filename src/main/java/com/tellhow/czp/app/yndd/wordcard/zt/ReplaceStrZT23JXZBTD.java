package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZT23JXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通二分之三接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			RuleExeUtil.swapRunmodelThreeTwoDeviceList(zbgyckgList);
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
