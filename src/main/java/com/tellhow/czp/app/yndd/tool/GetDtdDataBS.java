package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.log4j.Logger;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.CardWordMode;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.model.CardItemModel;

public class GetDtdDataBS extends GetDtdData{

	static {
		Map<String,String> modelMap = new HashMap<String, String>();
		modelMap.put("MODELDESC", "执行[厂站名称][设备名称]由[母线名称][设备状态]倒至[所在母线][设备状态]程序操作");
		modelMap.put("BEGINSTATUS", "");
		modelMap.put("ENDSTATUS", "");
		modelMap.put("OPERATION", "倒母");
		modelList.add(modelMap);
		Map<String,String> modelMap2 = new HashMap<String, String>();
		modelMap2.put("MODELDESC", "执行[厂站名称][设备名称]由[设备状态]转[设备状态]程序操作");
		modelMap2.put("BEGINSTATUS", "");
		modelMap2.put("ENDSTATUS", "");
		modelMap2.put("OPERATION", "");
		modelList.add(modelMap2);
	}
	
	public static ArrayList<String[]> getData(List<CardItemModel> itemModelsShow,String ddname){
		ArrayList<String[]> data = new ArrayList<String[]>();
		
		for(CardItemModel cim : itemModelsShow){
			String cznr = cim.getCardDesc();
			String stationid = cim.getCzdwID();
			String stationname = cim.getStationName();
			String uuids = cim.getUuIds();

			String isyk = "1";
			String devname= "";
			String startZT= "";
			String endZT= "";
			String deviceType= "";
			
			List<RuleBaseMode> rbm = getRBMList(stationname,cznr);
			
			String czname = "";
			
			if(rbm.size()>0){
				PowerDevice dev = rbm.get(0).getPd();
				
				if(dev!=null){
					if(!dev.getPowerStationID().equals("")){
						PowerDevice stationDev = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						czname = CZPService.getService().getDevName(stationDev);
					}else{
						czname = stationname;
					}
					
					if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
						String begin = rbm.get(0).getBeginStatus();
						String end = rbm.get(0).getEndState();
						
						if(!end.equals("")&&!begin.equals("")){
							List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(dev.getPowerDeviceID());
							PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(dev.getPowerDeviceID());
							
							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								if(begin.equals("0")){
									for(PowerDevice loadLineTran : loadLineTrans){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
										czname = CZPService.getService().getDevName(station);
										getSwitchTd(kgList, data, uuids, czname, ddname);
									}
									
									List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
									PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
									czname = CZPService.getService().getDevName(station);
									getSwitchTd(kgList, data, uuids, czname, ddname);
								}
									
								if(end.equals("2")){
									for(PowerDevice loadLineTran : loadLineTrans){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
										czname = CZPService.getService().getDevName(station);
										getSwitchSequenceTd(kgList,data,uuids,czname,ddname);
									}
									
									List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
									PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
									czname = CZPService.getService().getDevName(station);
									getSwitchSequenceTd(kgList, data, uuids, czname, ddname);
								}
							}else{
								if(begin.equals("2")){
									if(sourceLineTrans != null){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
										czname = CZPService.getService().getDevName(station);
										getSwitchSequenceFd(kgList, data, uuids, czname, ddname);
									}
									
									for(PowerDevice loadLineTran : loadLineTrans){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
										czname = CZPService.getService().getDevName(station);
										getSwitchSequenceFd(kgList,data,uuids,czname,ddname);
									}
								}
								
								if(end.equals("0")){
									if(sourceLineTrans != null){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
										czname = CZPService.getService().getDevName(station);
										getSwitchFd(kgList, data, uuids, czname, ddname);
									}
									
									for(PowerDevice loadLineTran : loadLineTrans){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
										czname = CZPService.getService().getDevName(station);
										getSwitchFd(kgList, data, uuids, czname, ddname);
									}
								}
							}
						}
					}else if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
						String begin = rbm.get(0).getBeginStatus();
						String end = rbm.get(0).getEndState();
						
						List<PowerDevice> swList = new ArrayList<PowerDevice>();
						List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT,"",true, true, true, true);
						
						//母线为高压侧，且为单电源单刀闸情况
						List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXLS,"",true, true, true, true);

						if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
							swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchFHC,false, true, true, true);
						}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
							swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						}
						
						List<PowerDevice> drswList = new ArrayList<PowerDevice>();
						List<PowerDevice> dkswList = new ArrayList<PowerDevice>();
						List<PowerDevice> xlswList = new ArrayList<PowerDevice>();
						List<PowerDevice> jdbswList = new ArrayList<PowerDevice>();
						List<PowerDevice> zybswList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
						List<PowerDevice> mlswList = new ArrayList<PowerDevice>();

						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
								drswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(drswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
								dkswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(dkswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								xlswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(xlswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getPowerDeviceName().contains("接地变")
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)){
								jdbswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(jdbswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getPowerDeviceName().contains("站用变")
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchZYB)
									){
								zybswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(zybswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								zbswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(zbswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								mlswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(mlswList);
						
						swList.clear();
						
						if(!end.equals("")&&!begin.equals("")){
							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
									swList.addAll(drswList);
									swList.addAll(dkswList);
									swList.addAll(xlswList);
									swList.addAll(jdbswList);
									swList.addAll(zybswList);
									swList.addAll(zbswList);
									swList.addAll(mlswList);

									List<PowerDevice> ptdzList = new ArrayList<PowerDevice>();
									
									for(PowerDevice qtdz : qtdzList){
										if(qtdz.getPowerDeviceName().contains("PT")){
											ptdzList.add(qtdz);
										}
									}		
									
									getSequencePTTd(ptdzList,data,uuids,czname,ddname);
									getSwitchSequenceTd(swList,data,uuids,czname,ddname);
									
									for(PowerDevice xldz : xldzList){
										String devid = xldz.getPowerDeviceID();

										if(RuleExeUtil.isDeviceChanged(xldz)){
											cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(xldz);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
											
											String  qzcznr = "检查"+CZPService.getService().getDevName(xldz)+"处分闸位置";
											data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
										}
									}
								}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
									getSequencePTTd(qtdzList, data, uuids, czname, ddname);
									swList.addAll(mlswList);
									getSwitchTd(swList, data, uuids, czname, ddname);
									getSwitchSequenceTd(swList,data,uuids,czname, ddname);
								}
							}else{//复电
								if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
									if(dev.getPowerVoltGrade() == 10){
										swList.addAll(zbswList);
									}else{
										swList.addAll(mlswList);
										swList.addAll(zbswList);
										swList.addAll(zybswList);
										swList.addAll(jdbswList);
										swList.addAll(xlswList);
										swList.addAll(dkswList);
										swList.addAll(drswList);
									}
									
									List<PowerDevice> ptdzList = new ArrayList<PowerDevice>();
									
									for(PowerDevice qtdz : qtdzList){
										if(qtdz.getPowerDeviceName().contains("PT")){
											ptdzList.add(qtdz);
										}
									}		
									
									getSequencePTFd(ptdzList,data,uuids,czname,ddname);
									getSwitchSequenceFd(swList,data,uuids,czname,ddname);
									
									for(PowerDevice qtdz : qtdzList){
										String devid = qtdz.getPowerDeviceID();

										if(qtdz.getPowerDeviceName().contains("站用变")){
											if(RuleExeUtil.isDeviceChanged(qtdz)){
												cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(qtdz);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
												
												String  qzcznr  =  "检查"+CZPService.getService().getDevName(qtdz)+"处合闸位置";
												data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
											}
										}
									}
									
								}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
									swList.addAll(mlswList);
									
									getSwitchSequenceFd(swList,data,uuids,czname, ddname);
									getSwitchFd(swList, data, uuids, czname, ddname);
									getSequencePTFd(qtdzList, data, uuids, czname, ddname);
								}
							}
						}else{
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
						}
					}else if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						String begin = rbm.get(0).getBeginStatus();
						String end = rbm.get(0).getEndState();
						
						List<PowerDevice> gyczbkgList = RuleExeUtil.getTransformerSwitchHigh(dev);
						List<PowerDevice> zyczbkgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						List<PowerDevice> dyczbkgList = RuleExeUtil.getTransformerSwitchLow(dev);

						if(RuleUtil.isTransformerNQ(dev)
								||RuleUtil.isTransformerKDNQ(dev)){
							gyczbkgList.clear();
						}
						
						if(!begin.equals("")&&!end.equals("")){
							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								getSwitchTd(zyczbkgList, data, uuids, czname, ddname);

								getSwitchTd(gyczbkgList, data, uuids, czname, ddname);
								
								getSwitchSequenceTd(zyczbkgList, data, uuids, czname, ddname);

								getSwitchSequenceTd(gyczbkgList, data, uuids, czname, ddname);
							}else{//复电
								getSwitchSequenceFd(gyczbkgList, data, uuids, czname, ddname);
								
								getSwitchSequenceFd(zyczbkgList, data, uuids, czname, ddname);
								
								getSwitchFd(gyczbkgList, data, uuids, czname, ddname);

								getSwitchFd(zyczbkgList, data, uuids, czname, ddname);
							}
						}else{
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
						}
					}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
						String begin = rbm.get(0).getBeginStatus();
						String end = rbm.get(0).getEndState();
						
						List<PowerDevice> swList = new ArrayList<PowerDevice>();
						swList.add(dev);
						
						if(!rbm.get(0).getBusBar().equals("")){
							getSwitchDM(swList, data, uuids, czname, ddname);
						}else if(begin.equals("0")){
							getSwitchTd(swList, data, uuids, czname, ddname);
							
							if(end.equals("2")){
								getSwitchSequenceTd(swList, data, uuids, czname, ddname);
							}
						}else if(begin.equals("1")){
							if(end.equals("2")){
								getSwitchSequenceTd(swList, data, uuids, czname, ddname);
							}else if(end.equals("0")){
								getSwitchFd(swList, data, uuids, czname, ddname);
							}
						}else if(begin.equals("2")){
							getSwitchSequenceFd(swList, data, uuids, czname, ddname);
						
							if(end.equals("0")){
								getSwitchFd(swList, data, uuids, czname, ddname);
							}
						}else{
							data.add(new String[]{"","",czname,czname,cznr,isyk,"",devname,startZT,endZT,deviceType,uuids});
						}
					}else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
						String begin = rbm.get(0).getBeginStatus();
						String end = rbm.get(0).getEndState();
						
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)){
							if(begin.equals("1")&&end.equals("0")){
								String devid = dev.getPowerDeviceID();

								cznr =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
								String  qzcznr  =  "检查"+czname+CZPService.getService().getDevName(dev)+"确已合上";
								data.add(new String[]{"","",ddname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								String devid = dev.getPowerDeviceID();

								cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								
								String  qzcznr  =  "检查"+CZPService.getService().getDevName(dev)+"处分闸位置";
								data.add(new String[]{"","",ddname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else{
								if(cznr.contains("主变中性点")&&cznr.contains("检查")){
									String devid = dev.getPowerDeviceID();
									String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
									String qzcznr  =  "检查"+CZPService.getService().getDevName(dev)+"处合闸位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,"",deviceType,uuids});
								}else if(cznr.contains("主变中性点")&&cznr.contains("落实")){
									String devid = dev.getPowerDeviceID();
									String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
									String qzcznr  =  "检查"+CZPService.getService().getDevName(dev)+"处合闸位置";
									data.add(new String[]{"","",ddname,czname,qzcznr,isyk,devid,devname,startZT,"",deviceType,uuids});
								}else{
									data.add(new String[]{"","",ddname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
								}
							}
						}else{
							if(begin.equals("1")&&end.equals("0")){
								String devid = dev.getPowerDeviceID();

								cznr =  "合上"+czname+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								String devid = dev.getPowerDeviceID();

								cznr =  "拉开"+czname+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
							}else{
								data.add(new String[]{"","",ddname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}
					}else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						String devid = dev.getPowerDeviceID();
						String begin = rbm.get(0).getBeginStatus();
						String end = rbm.get(0).getEndState();
						
						if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							if(begin.equals("1")&&end.equals("0")){
								cznr  =  "将"+czname+CZPService.getService().getDevName(dev)+"由试验位置摇至工作位置";
								data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上",deviceType,uuids});
								
								String  qzcznr  =  "检查"+CZPService.getService().getDevName(dev)+"在工作位置";
								
								String qzdevid = dev.getPowerDeviceID();
								
								data.add(new String[]{"","",czname,czname,qzcznr,"",qzdevid,"","","","",uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								cznr  =  "将"+czname+CZPService.getService().getDevName(dev)+"由工作位置摇至试验位置";
								data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
								
								String  qzcznr  =  "检查"+CZPService.getService().getDevName(dev)+"在试验位置";
								
								String qzdevid = dev.getPowerDeviceID();
								
								data.add(new String[]{"","",czname,czname,qzcznr,"",qzdevid,"","","","",uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}else{
							if(begin.equals("1")&&end.equals("0")){
								cznr =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
								String  qzcznr  =  "检查"+CZPService.getService().getDevName(dev)+"处合闸位置";
								data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								
								String  qzcznr  =  "检查"+CZPService.getService().getDevName(dev)+"处分闸位置";
								data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}
					}else if(cznr.contains("主变中性点")&&cznr.contains("检查")){
							List<PowerDevice> gdList = RuleExeUtil.getDeviceList(CBSystemConstants.getCurRBM().getPd(), SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
							
							RuleExeUtil.swapLowDeviceList(gdList);
							
							for(PowerDevice gd : gdList) {
								String devid = gd.getPowerDeviceID();
								String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(gd);
								data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}
							
							for(PowerDevice gd : gdList) {
								String devid = gd.getPowerDeviceID();
								String temp  =  "检查"+CZPService.getService().getDevName(gd)+"处合闸位置";
								data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"",deviceType,uuids});
							}
					}else if(cznr.contains("所有断路器倒至")){
						List<PowerDevice> swList = new ArrayList<PowerDevice>();
						
						PowerDevice	device = CBSystemConstants.getCurRBM().getPd();
						
						if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
							swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchFHC,false, true, true, true);
						}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
							swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						}
						
						List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
						List<PowerDevice> xlswList = new ArrayList<PowerDevice>();

						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
								zbswList.add(switchs);
							}
						}
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
								zbswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(zbswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
								xlswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(xlswList);
						
						List<PowerDevice> yxswList = new ArrayList<PowerDevice>();
						
						yxswList.addAll(xlswList);
						yxswList.addAll(zbswList);
						
						for(PowerDevice sw : yxswList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

							for(PowerDevice dz : dzList){
								String devid = dz.getPowerDeviceID();
								
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
									cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									
									String qzcznr =  "检查"+CZPService.getService().getDevName(dz)+"处合闸位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
								}
							}
							
							for(PowerDevice dz : dzList){
								String devid = dz.getPowerDeviceID();

								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
									cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
									
									String  qzcznr  =  "检查"+CZPService.getService().getDevName(dz)+"处分闸位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
								}
							}
						}
					}else if(cznr.contains("全站")){
						List<PowerDevice> xlkg10kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> mlkg10kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbkg10kVList = new ArrayList<PowerDevice>();

						List<PowerDevice> xlkg35kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> mlkg35kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbdyckg35kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbfhckg35kVList = new ArrayList<PowerDevice>();

						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationid);

						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice pd = it.next();
							
							if(pd.getPowerVoltGrade() == 6){
								
							}else if(pd.getPowerVoltGrade() == 10){
								if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									xlkg10kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									mlkg10kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
									zbkg10kVList.add(pd);
								}
							}else if(pd.getPowerVoltGrade() == 35){
								if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									xlkg35kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									mlkg35kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
									zbdyckg35kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
									zbfhckg35kVList.add(pd);
								}
							}
						}
						
						/*
						 * 10kV设备（只能作为低压侧）
						 */
						
						//线路开关
						getSwitchSequenceTd(xlkg10kVList,data,uuids,czname,ddname);
						//分段开关
						getSwitchSequenceTd(mlkg10kVList,data,uuids,czname,ddname);
						//主变开关	
						getSwitchSequenceTd(zbkg10kVList,data,uuids,czname,ddname);
						
						/*
						 * 35kV设备（可以作为高压侧，或者中压侧，或者低压侧）
						 */
						
						//电源侧主变开关	
						getSwitchSequenceTd(zbdyckg35kVList,data,uuids,czname,ddname);
						//线路开关
						getSwitchSequenceTd(xlkg35kVList,data,uuids,czname,ddname);
						//分段开关
						getSwitchSequenceTd(mlkg35kVList,data,uuids,czname,ddname);
						//负荷侧主变开关	
						getSwitchSequenceTd(zbfhckg35kVList,data,uuids,czname,ddname);
					}else if(cznr.startsWith("退出")||cznr.startsWith("投入")){
						startZT = rbm.get(0).getBeginStatus();
						endZT = rbm.get(0).getEndState();
						
						data.add(new String[]{"","",ddname,czname,cznr,"0",dev.getPowerDeviceID(),devname,startZT,endZT,deviceType,uuids});
					}else if(cznr.contains("站用变")&&!cznr.contains("核实")&&!cznr.contains("断路器")){
						List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(CBSystemConstants.getCurRBM().getPd(), SystemConstants.Switch, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);
						
						List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
						
						String beginstatus = "";
						String endstatus = "";

						for(PowerDevice qtkg : qtkgList){
							if(qtkg.getPowerDeviceName().contains("站用变")){
								beginstatus = RuleExeUtil.getDeviceBeginStatus(qtkg);
								endstatus = qtkg.getDeviceStatus();
								zybkgList.add(qtkg);
							}
						}
						
						if(beginstatus.equals("0")){
							getSwitchTd(zybkgList, data, uuids, czname, ddname);
						}
						
						if(endstatus.equals("2")){
							getSwitchSequenceTd(zybkgList,data,uuids,czname, ddname);
						}
						
						if(beginstatus.equals("2")){
							getSwitchSequenceFd(zybkgList,data,uuids,czname, ddname);
						}
						
						if(endstatus.equals("0")){
							getSwitchFd(zybkgList,data,uuids,czname, ddname);
						}
					}else if(cznr.contains("电压互感器")&&!cznr.contains("核实")){
						List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(CBSystemConstants.getCurRBM().getPd(), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeKnifeQT, "", true, true, true, true);
						
						List<PowerDevice> ptdzList = new ArrayList<PowerDevice>();
						
						String beginstatus = "";
						String endstatus = "";
						
						for(PowerDevice qtdz : qtdzList){
							if(qtdz.getPowerDeviceName().contains("PT")){
								beginstatus = RuleExeUtil.getDeviceBeginStatus(qtdz);
								endstatus = qtdz.getDeviceStatus();
								ptdzList.add(qtdz);
							}
						}
						
						if(beginstatus.equals("0")){
							getSequencePTTd(qtdzList, data, uuids, czname, ddname);
						}
						
						if(endstatus.equals("0")){
							getSequencePTFd(qtdzList, data, uuids, czname, ddname);
						}
					}else{
						List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(czname, cznr);
						
						for(RuleBaseMode rbm1 : rbmList){
							PowerDevice device = rbm1.getPd();
							String stationName = "";
							
							if(!device.getPowerDeviceID().equals("")){
								PowerDevice station = CBSystemConstants.getPowerStation(device.getPowerStationID());
								devname = device.getPowerDeviceName();
								stationName = CZPService.getService().getDevName(station);
								startZT = rbm1.getBeginStatus();
								endZT = rbm1.getEndState();
								deviceType = device.getDeviceType();
								startZT = RuleExeUtil.getStatusNew(device.getDeviceType(), startZT);
								endZT = RuleExeUtil.getStatusNew(device.getDeviceType(), endZT);
							}

							data.add(new String[]{"","",czname,stationName,cznr,"0",device.getPowerDeviceID(),devname,startZT,endZT,deviceType,uuids});
						}
					}
				}else{
					data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
				}
			}else{
				data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
			}
		}
		
		return data;
	}
	
	private static List<RuleBaseMode> getRBMList(String station, String word) {
		String wholeWord = word;
		
		/*
		 * 上级单位
		 */
		String superiorStationName = "";
		/*
		 * 下级单位
		 */
		String subordinateStationName = "";
		/*
		 * 指令单位
		 */
		String instructionStationName = "";
		
		/*
		 * 生成设备对象、初始状态、目标状态、操作、所在母线解析
		 */
		List<CardWordMode> wordRbmList = new ArrayList<CardWordMode>();
		
//	    List<Map<String,String>> modelList = new ArrayList<Map<String,String>>();

	    
//	    String sql="SELECT MODELDESC,BEGINSTATUS,ENDSTATUS,OPERATION FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL ORDER BY TO_NUMBER(ORDERID) ASC";
//	    List<Map<String,String>> modelList=DBManager.queryForList(sql);
	    
	    for(Map<String,String> model : modelList){
    		String modeldesc = StringUtils.ObjToString(model.get("MODELDESC"));
    		String beginStatus = StringUtils.ObjToString(model.get("BEGINSTATUS"));
    		String endStatus = StringUtils.ObjToString(model.get("ENDSTATUS"));
    		String operation = StringUtils.ObjToString(model.get("OPERATION"));
    		
			Object[] splitParams = SafeCheckUtilBS.init(modeldesc,'[',']');
			List<String> paramsKey=(ArrayList<String>)splitParams[1]; //标签集合
			
			if(paramsKey.size()>0){
				List<String> firstStr=(ArrayList)splitParams[0];
				String lastStr=splitParams[2].toString();

				StringBuffer descBuff=new StringBuffer();
				
				for(String first : firstStr){
					descBuff.append(first+"(.*)");
				}
				
				descBuff.append(lastStr);
				
				String regx = "^"+descBuff.toString()+"$";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){//解析
            		String[] splitregxs = regx.replace("^", "").replace("$", "").replace("(.*)", "|").split("\\|");
            		
            		for(String string : splitregxs){
            			if(!string.equals("")){
            				if(string.equals("倒由")){
            					word = word.replace("倒", "");
            				}
            				word = word.replace(string, "");
            			}
            		}
            		
            		List<Map<String,String>> returnList = SafeCheckUtilBS.getDeviceInfoByWord(paramsKey,word);
            		
            		CardWordMode cwm = new CardWordMode();
        			
        			if(!beginStatus.equals("")){
        				cwm.setBeginStatus(beginStatus);
        			}
        				
        			if(!endStatus.equals("")){
        				cwm.setEndStatus(endStatus);
        			}
        				
        			if(!operation.equals("")){
        				cwm.setOperaTion(operation);
        			}
        			
        			List<PowerDevice> devList = new ArrayList<PowerDevice>();
        			
        			for(Map<String,String> returnMap : returnList){
        				PowerDevice dev = new PowerDevice();

        				if(returnMap.containsKey("厂站名称")){
        					dev.setPowerStationName(returnMap.get("厂站名称"));
        					instructionStationName = returnMap.get("厂站名称");
        				}
        					
        				if(returnMap.containsKey("设备名称")){
        					dev.setPowerDeviceName(returnMap.get("设备名称"));
        				}
        					
        				if(returnMap.containsKey("设备状态")){
        					if(cwm.getBeginStatus().equals("")){
        						cwm.setBeginStatus(returnMap.get("设备状态"));
        					}else if(cwm.getEndStatus().equals("")){
        						cwm.setEndStatus(returnMap.get("设备状态"));
        					}
        				}
        				
        				if(returnMap.containsKey("所在母线")){
        					cwm.setBusBar(returnMap.get("所在母线"));
        				}
        				
        				if(returnMap.containsKey("设备类型")){
        					cwm.setDeviceKind(returnMap.get("设备类型"));
        				}
        				
        				if(dev.getPowerStationName().equals("")){
        					dev.setPowerStationName(station);
        				}
        				
        				if(!dev.getPowerDeviceName().equals("")){
        					devList.add(dev);
        				}
        			}
        			
        			cwm.setPdList(devList);
        			wordRbmList.add(cwm);
            		break;
                }
			}
	    }
	    
		String stationID = "";
		
		if(instructionStationName.equals("")){//指令单位为空，那么下级单位取station
			subordinateStationName = station;
		}else{//指令单位不为空，那么上级单位为station，下级单位为instructionStationName
			superiorStationName = station;
			subordinateStationName = instructionStationName;
		}
		
		//厂站名称校验
		for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();){
			PowerDevice st = it.next();
			
			String modelDevName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(st));
			subordinateStationName = StringUtils.killVoltInDevName(subordinateStationName);
			
			if(modelDevName.equals(subordinateStationName)) {
				stationID = st.getPowerDeviceID();
				
				if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
					CreatePowerStationToplogy.loadFacEquip(stationID);
				}
				
				HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationID);
				
				if(devMap != null){
					break;
				}else{
					continue;
				}
			}
		}
		
		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();
		
		if(stationID.equals("")){
			for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerLine().values().iterator();it.hasNext();){
				PowerDevice line = it.next();
				
				String lineName = CZPService.getService().getDevName(line);
				
				if(wholeWord.contains(lineName)){
					Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(line);
					
					for(PowerDevice ln:stationlines.keySet()){
						stationID = ln.getPowerStationID();
						break;
					}
				}
			}
		}
		
		//设备名称校验
		if(!stationID.equals("")){
			if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
				CreatePowerStationToplogy.loadFacEquip(stationID);
			}
			
			HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationID);
			
			if(devMap != null){
				String devNum = "";

				for(CardWordMode cwm : wordRbmList){
					List<PowerDevice> devList = cwm.getPdList();
					
					if(cwm.getDeviceKind().equals("二次设备")){
						
					}else{
						for(PowerDevice device : devList){
							String equipTypeFlag = "";
							String equipTypeName = "";
							String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
									SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine,
									SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate,SystemConstants.Switch, SystemConstants.SwitchSeparate,
									SystemConstants.SwitchSeparate, SystemConstants.Switch,
									SystemConstants.Switch,SystemConstants.VolsbTransformer, SystemConstants.MotherLine,
									SystemConstants.MotherLine, SystemConstants.InOutLine,SystemConstants.InOutLine,
									SystemConstants.PowerTransformer, SystemConstants.ElecShock,
									SystemConstants.ElecCapacity ,SystemConstants.PowerTransformer};
							String[] key = new String[] { "接地刀闸","接地开关", "地刀", "隔离刀闸","隔离开关","小车开关", "小车", "刀闸", "断路器",
									"开关","PT", "母线", "母", "线","回", "主变", "电抗器", "电容器","#变"};
							for (int i = 0; i < key.length; i++) {
								if (device.getPowerDeviceName().lastIndexOf(key[i]) >= 0) {
									equipTypeFlag = type[i];
									equipTypeName = key[i];
									break;
								}
							}
							
							devNum = CZPService.getService().getDevNum(device.getPowerDeviceName());
							
							String volStr = "";
							
							if(device.getPowerDeviceName().toLowerCase().split("kv").length >= 3){
								volStr = device.getPowerDeviceName().toLowerCase().substring(device.getPowerDeviceName().toLowerCase().indexOf("kv")+2);
							}else if(device.getPowerDeviceName().contains("中性点接地开关")){
								volStr = "";
							}else{
								volStr = device.getPowerDeviceName();
							}
							
							//获取电压等级
							String volt = StringUtils.getVoltInDevName(volStr);
							
							PowerDevice pd = new PowerDevice();
							
							for (PowerDevice dev : devMap.values()) {
								if (!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
									continue;
								if (!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
									continue;
								if(dev.getPowerDeviceName().contains("A相")||dev.getPowerDeviceName().contains("B相")||dev.getPowerDeviceName().contains("C相"))
									continue;
								
								if(equipTypeFlag.equals(SystemConstants.InOutLine)){
									if (CZPService.getService().getDevName(dev).equals(device.getPowerDeviceName())){
										pd = dev;
										break;
									}
								}else if (CZPService.getService().getDevNum(dev.getPowerDeviceName()).equals(devNum)&&!devNum.equals("")) {
									if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
										pd = dev;
										break;
									}
									else
										pd = dev;
								}
							}
							
							if(!pd.getPowerDeviceID().equals("")){
								String modelName = pd.getPowerDeviceName();
								String deviceName = device.getPowerDeviceName();
								String beginStatus = cwm.getBeginStatus();
								String endStatus = cwm.getEndStatus();
								
								RuleBaseMode rbm = new RuleBaseMode();
								rbm.setPd(pd);
								rbm.setBeginStatus(RuleExeUtil.getNumStatusNew(beginStatus));
								rbm.setEndState(RuleExeUtil.getNumStatusNew(endStatus));
								rbm.setBusBar(cwm.getBusBar());
								
								rbmList.add(rbm);
							}
						}
					}
				}
			}
		}
		
		if(rbmList.size()==0){
			RuleBaseMode rbm = new RuleBaseMode();
			PowerDevice dev = new PowerDevice();
			rbm.setPd(dev);
			rbmList.add(rbm);
		}
		
	    return rbmList;
	}

	public static String getVoltInDevName(String powerDeviceName) {
    	String volt = "";
    	String equipName = powerDeviceName;
    	int pos = equipName.toUpperCase().indexOf("KV");
		if (pos >= 0) {
			volt = "";
			for(int i = pos-1; i >=0; i--) {
				char ch = equipName.charAt(i);
				if (ch >= '0' && ch <= '9')
					volt = ch + volt;
				else
					break;
			}
        }
		else
			volt = "";
    	return volt;
    }
	
	public static  String getStationNameByCznr(String cznr) {
		String stationName = "";
		if(cznr.lastIndexOf("站") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("站")+1);
		else if(cznr.lastIndexOf("变") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("变")+1);
		else if(cznr.lastIndexOf("厂") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("厂")+1);
		
		if(stationName.indexOf("千伏") >= 0)
			stationName = stationName.substring(stationName.lastIndexOf("千伏")+2);
		else if(stationName.toLowerCase().indexOf("kv") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().lastIndexOf("kv")+2);
		else if(stationName.toLowerCase().indexOf("切换至") >= 0 || stationName.toLowerCase().indexOf("切至") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("至")+1);
		else if(stationName.toLowerCase().indexOf("切换到") >= 0 || stationName.toLowerCase().indexOf("切到") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("到")+1);
		else if(stationName.toLowerCase().indexOf("断开") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("断开")+2);
		else if(stationName.toLowerCase().indexOf("合上") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("合上")+2);
		return stationName;
	}
	
	/*
	 * 开关停电
	 */
	public static void getSwitchTd(List<PowerDevice> kgList,ArrayList<String[]> data, String uuids, String czname, String ddname) {
		for(PowerDevice dev : kgList){
			String devid = dev.getPowerDeviceID();
			String devname = CZPService.getService().getDevName(dev);
			String deviceType = dev.getDeviceType();

			if(!RuleExeUtil.isDeviceChanged(dev)){
				if(dev.getDeviceStatus().equals("1")){
					String qzcznr = "检查"+czname+CZPService.getService().getDevName(dev)+"三相确在分闸位置";
					data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,devname,"","",deviceType,uuids});
				}else if(dev.getDeviceStatus().equals("2")){
					String qzcznr = "检查"+czname+CZPService.getService().getDevName(dev)+"三相确处冷备用";
					data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,devname,"","",deviceType,uuids});
				}
			}else{
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					String cznr =  "遥控断开"+czname+CZPService.getService().getDevName(dev);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","断开",deviceType,uuids});
					
					String qzcznr = "检查"+czname+CZPService.getService().getDevName(dev)+"三相确已断开";
					data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,devname,"","",deviceType,uuids});
					
					String jycznr  = czname+CZPService.getService().getDevName(dev)+"模拟量校验";
					data.add(new String[]{"","",ddname,czname,jycznr,"",devid,devname,"","",deviceType,uuids});
				}
			}
		}
	}
	
	/*
	 * 开关复电
	 */
	public static void getSwitchFd(List<PowerDevice> kgList,ArrayList<String[]> data, String uuids, String czname, String ddname) {
		for(PowerDevice dev : kgList){
			String devid = dev.getPowerDeviceID();
			String devname = CZPService.getService().getDevName(dev);
			String deviceType = dev.getDeviceType();

			if(!RuleExeUtil.isDeviceChanged(dev)){
				if(dev.getDeviceStatus().equals("1")){
					String qzcznr = "检查"+czname+CZPService.getService().getDevName(dev)+"三相确在分闸位置";
					data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,devname,"","",deviceType,uuids});
				}else if(dev.getDeviceStatus().equals("2")){
					String qzcznr = "检查"+czname+CZPService.getService().getDevName(dev)+"三相确处冷备用";
					data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,devname,"","",deviceType,uuids});
				}
			}else{
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					String cznr =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上",deviceType,uuids});
					
					String qzcznr = "检查"+czname+CZPService.getService().getDevName(dev)+"三相确已合上";
					data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,devname,"","",deviceType,uuids});
					
					String jycznr  = czname+CZPService.getService().getDevName(dev)+"模拟量校验";
					data.add(new String[]{"","",ddname,czname,jycznr,"",devid,devname,"","",deviceType,uuids});
				}
			}
		}
	}
	
	/*
	 * 开关刀闸停电
	 */
	public static void getSwitchSequenceTd(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
					Collections.reverse(dzList);
				}
			}
			
			if(RuleExeUtil.isDeviceHadStatus(sw, "1", "2")){
				List<Map<String,String>> airswitchList = new ArrayList<Map<String,String>>();
				
				if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) 
						|| sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(sw,SystemConstants.PowerTransformer, SystemConstants.MotherLine ,true, true, true);
					
					if(zbList.size() > 0){
						airswitchList = getAirSwitchByTransformer(zbList.get(0).getPowerDeviceID());
					}
				}else{
					airswitchList = getAirSwitch(sw.getPowerDeviceID());
				}
				
				
				if(airswitchList.size() == 1){
					if(sw.getPowerVoltGrade()<500){
						for(Map<String,String> airswitchMap : airswitchList){
							String kkid = airswitchMap.get("ID");
							String kkName = airswitchMap.get("NAME");
							String cznr =  "合上"+czname+kkName;
							data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","合上","",uuids});
						}
					}
				}
				
				for(PowerDevice zbdz : dzList){
					if(RuleExeUtil.getDeviceBeginStatus(zbdz).equals("0")&&!zbdz.getPowerDeviceName().contains("PT")){
						String devid =  zbdz.getPowerDeviceID();
						String devname = CZPService.getService().getDevName(zbdz);
						String devnum = CZPService.getService().getDevNum(zbdz);
						String deviceType = zbdz.getDeviceType();
						
						if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							if(zbdz.getPowerDeviceName().contains("隔离手车")){
								String cznr  =  "将"+czname+CZPService.getService().getDevName(zbdz)+"由工作位置摇至试验位置";
								data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
								
								String  qzcznr  =  "检查"+czname+CZPService.getService().getDevName(zbdz)+"在试验位置";
								
								String qzdevid = zbdz.getPowerDeviceID();
								
								data.add(new String[]{"","",czname,czname,qzcznr,"",qzdevid,"","","","",uuids});
							}else{
								String cznr  =  "将"+czname+CZPService.getService().getDevName(sw)+"手车由工作位置摇至试验位置";
								
								data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
								
								String  qzcznr  =  "检查"+CZPService.getService().getDevName(sw)+"手车在试验位置";
								
								String qzdevid = zbdz.getPowerDeviceID();
								
								data.add(new String[]{"","",czname,czname,qzcznr,"",qzdevid,"","","","",uuids});
							}
						}else{
							if(airswitchList.size() > 1){
								for(Map<String,String> airswitchMap : airswitchList){
									String kkid = airswitchMap.get("ID");
									String kkName = airswitchMap.get("NAME");
									
									if(kkName.contains(devnum)){
										String cznr =  "合上"+czname+kkName;
										data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","合上","",uuids});
									}
								}
							}
							
							String cznr =  "遥控拉开"+czname+devname;
							data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});

							String qzcznr = "检查"+czname+devname+"三相确已拉开";
							data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,"","","","",uuids});
							
							if(airswitchList.size() > 1){
								for(Map<String,String> airswitchMap : airswitchList){
									String kkid = airswitchMap.get("ID");
									String kkName = airswitchMap.get("NAME");
									
									if(kkName.contains(devnum)){
										cznr =  "断开"+czname+kkName;
										data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","断开","",uuids});
									}
								}
							}
						}
					}
				}
				
				if(airswitchList.size() == 1){
					if(sw.getPowerVoltGrade()<500){
						for(Map<String,String> airswitchMap : airswitchList){
							String kkid = 	airswitchMap.get("ID");
							String kkName = airswitchMap.get("NAME");
							String cznr =  "断开"+czname+kkName;
							data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","断开","",uuids});
						}
					}
				}
			}
		}
	}
	
	/*
	 * 开关刀闸复电
	 */
	public static void getSwitchSequenceFd(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
					Collections.reverse(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
				}
			}
			
			if(RuleExeUtil.isDeviceHadStatus(sw, "2", "1")){
				List<Map<String,String>> airswitchList = new ArrayList<Map<String,String>>();
				
				if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) 
						|| sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(sw,SystemConstants.PowerTransformer, SystemConstants.MotherLine ,true, true, true);
					
					if(zbList.size() > 0){
						airswitchList = getAirSwitchByTransformer(zbList.get(0).getPowerDeviceID());
					}
				}else{
					airswitchList = getAirSwitch(sw.getPowerDeviceID());
				}
				
				if(airswitchList.size() == 1){
					if(sw.getPowerVoltGrade()<500){
						for(Map<String,String> airswitchMap : airswitchList){
							String kkid = 	airswitchMap.get("ID");
							String kkName = airswitchMap.get("NAME");
							String cznr =  "合上"+czname+kkName;
							data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","合上","",uuids});
						}
					}
				}
				
				for(PowerDevice zbdz : dzList){
					String devid = zbdz.getPowerDeviceID();

					if(RuleExeUtil.getDeviceEndStatus(zbdz).equals("0")&&!zbdz.getPowerDeviceName().contains("PT")){
						devid =  zbdz.getPowerDeviceID();
						String devname = CZPService.getService().getDevName(zbdz);
						String devnum = CZPService.getService().getDevNum(zbdz);
						String deviceType = zbdz.getDeviceType();
						
						if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							String cznr  =  "将"+czname+CZPService.getService().getDevName(sw)+"手车由试验位置摇至工作位置";
							data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});
							
							String  qzcznr  =  "检查"+czname+CZPService.getService().getDevName(sw)+"手车在工作位置";
							data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,"","","","",uuids});
						}else{
							if(airswitchList.size() > 1){
								for(Map<String,String> airswitchMap : airswitchList){
									String kkid = airswitchMap.get("ID");
									String kkName = airswitchMap.get("NAME");
									
									if(kkName.contains(devnum)){
										String cznr =  "合上"+czname+kkName;
										data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","合上","",uuids});
									}
								}
							}
							
							String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(zbdz);
							data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});

							String  qzcznr  =  "检查"+czname+CZPService.getService().getDevName(zbdz)+"三相确已合上";
							
							data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,"","","","",uuids});
							
							if(airswitchList.size() > 1){
								for(Map<String,String> airswitchMap : airswitchList){
									String kkid = airswitchMap.get("ID");
									String kkName = airswitchMap.get("NAME");
									
									if(kkName.contains(devnum)){
										cznr =  "断开"+czname+kkName;
										data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","断开","",uuids});
									}
								}
							}
						}
					}
				}
				
				if(airswitchList.size() == 1){
					if(sw.getPowerVoltGrade()<500){
						for(Map<String,String> airswitchMap : airswitchList){
							String kkid = 	airswitchMap.get("ID");
							String kkName = airswitchMap.get("NAME");
							String cznr =  "断开"+czname+kkName;
							data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","断开","",uuids});
						}
					}
				}
			}
		}
	}
	
	
	/*
	 * 开关倒母
	 */
	public static void getSwitchDM(List<PowerDevice> kgList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : kgList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			List<Map<String,String>> airswitchList = new ArrayList<Map<String,String>>();
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) 
					|| sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(sw,SystemConstants.PowerTransformer, SystemConstants.MotherLine ,true, true, true);
				
				if(zbList.size() > 0){
					airswitchList = getAirSwitchByTransformer(zbList.get(0).getPowerDeviceID());
				}
			}else{
				airswitchList = getAirSwitch(sw.getPowerDeviceID());
			}
			
			if(airswitchList.size() == 1){
				for(Map<String,String> airswitchMap : airswitchList){
					String kkid = 	airswitchMap.get("ID");
					String kkName = airswitchMap.get("NAME");
					String cznr =  "合上"+czname+kkName;
					data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","合上","",uuids});
				}
				
				for(PowerDevice dz : dzList){
					String devid = dz.getPowerDeviceID();
					String deviceType = dz.getDeviceType();
					String devname = CZPService.getService().getDevName(dz);

					if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
						String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});

						String  qzcznr  =  "检查"+czname+CZPService.getService().getDevName(dz)+"三相确已合上";
						
						data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,"","","","",uuids});
					}
				}
				
				for(PowerDevice dz : dzList){
					String devid = dz.getPowerDeviceID();
					String deviceType = dz.getDeviceType();
					String devname = CZPService.getService().getDevName(dz);
					
					if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
						String cznr =  "遥控拉开"+czname+devname;
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});

						String qzcznr = "检查"+czname+devname+"三相确已拉开";
						data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,"","","","",uuids});
					}
				}
				
				for(Map<String,String> airswitchMap : airswitchList){
					String kkid = 	airswitchMap.get("ID");
					String kkName = airswitchMap.get("NAME");
					String cznr =  "断开"+czname+kkName;
					data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","断开","",uuids});
				}
			}else if(airswitchList.size() > 1){
				for(PowerDevice dz : dzList){
					String devid = dz.getPowerDeviceID();
					String deviceType = dz.getDeviceType();
					String devname = CZPService.getService().getDevName(dz);
					String devnum = CZPService.getService().getDevNum(dz);

					if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
						for(Map<String,String> airswitchMap : airswitchList){
							String kkid = airswitchMap.get("ID");
							String kkName = airswitchMap.get("NAME");
							
							if(kkName.contains(devnum)){
								String cznr =  "合上"+czname+kkName;
								data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","合上","",uuids});
							}
						}
						
						String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});

						String qzcznr  =  "检查"+czname+CZPService.getService().getDevName(dz)+"三相确已合上";
						
						data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,"","","","",uuids});
						
						for(Map<String,String> airswitchMap : airswitchList){
							String kkid = airswitchMap.get("ID");
							String kkName = airswitchMap.get("NAME");
							
							if(kkName.contains(devnum)){
								cznr =  "断开"+czname+kkName;
								data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","断开","",uuids});
							}
						}
					}
				}
				
				for(PowerDevice dz : dzList){
					String devid = dz.getPowerDeviceID();
					String deviceType = dz.getDeviceType();
					String devname = CZPService.getService().getDevName(dz);
					String devnum = CZPService.getService().getDevNum(dz);

					if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
						for(Map<String,String> airswitchMap : airswitchList){
							String kkid = airswitchMap.get("ID");
							String kkName = airswitchMap.get("NAME");
							
							if(kkName.contains(devnum)){
								String cznr =  "合上"+czname+kkName;
								data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","合上","",uuids});
							}
						}
						
						String cznr =  "遥控拉开"+czname+devname;
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});

						String qzcznr = "检查"+czname+devname+"三相确已拉开";
						data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,"","","","",uuids});
						
						for(Map<String,String> airswitchMap : airswitchList){
							String kkid = airswitchMap.get("ID");
							String kkName = airswitchMap.get("NAME");
							
							if(kkName.contains(devnum)){
								cznr =  "断开"+czname+kkName;
								data.add(new String[]{"","",ddname,czname,cznr,"",kkid,kkName,"","断开","",uuids});
							}
						}
					}
				}
			}
		}
	}
	
	
	/*
	 * PT刀闸停电
	 */
	public static void getSequencePTTd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dev : dzList){
			String devid = dev.getPowerDeviceID();
			String devname = dev.getPowerDeviceName();
			String deviceType = dev.getDeviceType();

			if(dev.getPowerDeviceName().contains("PT")){
				List<Map<String,String>> airswitchList = getAirSwitchByMotherLine(dev.getPowerDeviceID());

				for(Map<String,String> airswitchMap : airswitchList){
					String airswitchid = airswitchMap.get("ID");
					String kkname = airswitchMap.get("NAME");
					
					if(!kkname.contains("电机电源")&&!kkname.contains("电机总电源")){
						String cznr =  "断开"+czname+kkname;
						data.add(new String[]{"","",ddname,czname,cznr,"",airswitchid,kkname,"","断开","",uuids});
					}
				}
				
				for(Map<String,String> airswitchMap : airswitchList){
					String airswitchid = airswitchMap.get("ID");
					String kkname = airswitchMap.get("NAME");
					
					if(kkname.contains("电机电源")||kkname.contains("电机总电源")){
						String cznr =  "合上"+czname+kkname;
						data.add(new String[]{"","",ddname,czname,cznr,"",airswitchid,kkname,"","合上","",uuids});
						break;
					}
				}
				
				String cznr =  "遥控拉开"+czname+devname;
				data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});

				String qzcznr = "检查"+czname+devname+"三相确已拉开";
				data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,"","","","",uuids});
				
				
				for(Map<String,String> airswitchMap : airswitchList){
					String airswitchid = airswitchMap.get("ID");
					String kkname = airswitchMap.get("NAME");
					
					if(kkname.contains("电机电源")||kkname.contains("电机总电源")){
						String lkcznr =  "断开"+czname+kkname;
						data.add(new String[]{"","",ddname,czname,lkcznr,"",airswitchid,kkname,"","断开","",uuids});
					}
				}
			}
		}
	}
	
	/*
	 * PT刀闸复电
	 */
	
	public static void getSequencePTFd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dev : dzList){
			String devid = dev.getPowerDeviceID();
			String devname = dev.getPowerDeviceName();
			String deviceType = dev.getDeviceType();

			if(dev.getPowerDeviceName().contains("PT")){
				List<Map<String,String>> airswitchList = getAirSwitchByMotherLine(dev.getPowerDeviceID());
				
				for(Map<String,String> airswitchMap : airswitchList){
					String airswitchid = airswitchMap.get("ID");
					String kkname = airswitchMap.get("NAME");
					
					if(kkname.contains("电机电源")||kkname.contains("电机总电源")){
						String cznr =  "合上"+czname+kkname;
						data.add(new String[]{"","",ddname,czname,cznr,"",airswitchid,kkname,"","合上","",uuids});
						break;
					}
				}
				
				String cznr =  "遥控合上"+czname+devname;
				data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上",deviceType,uuids});

				String qzcznr = "检查"+czname+devname+"三相确已合上";
				data.add(new String[]{"","",ddname,czname,qzcznr,"",devid,"","","","",uuids});
				
				
				for(Map<String,String> airswitchMap : airswitchList){
					String airswitchid = airswitchMap.get("ID");
					String kkname = airswitchMap.get("NAME");
					
					if(kkname.contains("电机电源")||kkname.contains("电机总电源")){
						String lkcznr =  "断开"+czname+kkname;
						data.add(new String[]{"","",ddname,czname,lkcznr,"",airswitchid,kkname,"","断开","",uuids});
						break;
					}
				}
				
				for(Map<String,String> airswitchMap : airswitchList){
					String airswitchid = airswitchMap.get("ID");
					String kkname = airswitchMap.get("NAME");
					
					if(!kkname.contains("电机电源")&&!kkname.contains("电机总电源")){
						cznr =  "合上"+czname+kkname;
						data.add(new String[]{"","",ddname,czname,cznr,"",airswitchid,kkname,"","合上","",uuids});
					}
				}
			}
		}
	}
	
	public static List<Map<String,String>> getAirSwitch(String equipid){
		PowerDevice devive = CBSystemConstants.getPowerDevice(equipid);
		
		String sql = "";
		
		if(devive.getPowerDeviceID().equals("6473925062230018")){//220kV兰通Ⅰ回251断路器特殊处理
			sql = "SELECT A.ID,A.NAME FROM "+CBSystemConstants.equipUser+"T_M_STATUSINPUT A,"+CBSystemConstants.equipUser+"T_EQUIPINFO B WHERE "
				+ "A.EQUIPOID = B.BAY_ID AND  B.EQUIP_ID = '"+equipid+"' AND A.REFMEASUREMENTTYPE = '压板' AND A.NAME NOT LIKE '%地刀%' AND A.NAME NOT LIKE '%接地开关%' AND A.NAME NOT LIKE '%备用%' AND A.NAME NOT LIKE '%未实验%' AND A.NAME NOT LIKE '%相%'";
		}else if(devive.getPowerDeviceID().equals("6473925068128259")){//220kV施甸变110kV何施线181断路器特殊处理
			sql = "SELECT A.ID,A.NAME FROM "+CBSystemConstants.equipUser+"T_M_STATUSINPUT A,"+CBSystemConstants.equipUser+"T_EQUIPINFO B WHERE "
					+ "A.EQUIPOID = B.BAY_ID AND  B.EQUIP_ID = '"+equipid+"' AND A.REFMEASUREMENTTYPE = '压板' AND A.NAME LIKE '%间隔隔离开关控制及电机电源总空开%' AND A.NAME NOT LIKE '%分位%'";
		}else if(devive.getPowerStationName().equals("保山220kV朝阳变") && devive.getPowerVoltGrade() == 110){
			sql = "SELECT A.ID,A.NAME FROM "+CBSystemConstants.equipUser+"T_M_STATUSINPUT A,"+CBSystemConstants.equipUser+"T_EQUIPINFO B WHERE "
				+ "A.EQUIPOID = B.BAY_ID AND  B.EQUIP_ID = '"+equipid+"' AND A.REFMEASUREMENTTYPE = '压板' AND A.NAME LIKE '%空开%'  AND A.NAME NOT LIKE '%地刀%' AND A.NAME NOT LIKE '%接地开关%' AND A.NAME NOT LIKE '%备用%' AND A.NAME NOT LIKE '%未实验%' AND A.NAME NOT LIKE '%相%'";
		}else{
			sql = "SELECT A.ID,A.NAME FROM "+CBSystemConstants.equipUser+"T_M_STATUSINPUT A,"+CBSystemConstants.equipUser+"T_EQUIPINFO B WHERE "
				+ "A.EQUIPOID = B.BAY_ID AND  B.EQUIP_ID = '"+equipid+"' AND A.REFMEASUREMENTTYPE = '压板' AND A.NAME LIKE '%空开%'  AND A.NAME NOT LIKE '%地刀%' AND A.NAME NOT LIKE '%接地开关%' AND A.NAME NOT LIKE '%备用%' AND A.NAME NOT LIKE '%未实验%' AND A.NAME NOT LIKE '%开关机%' AND A.NAME NOT LIKE '%相%'";
		}
		
		List<Map<String,String>> returnList = DBManager.queryForList(sql);
		
		return returnList;
	}
	
	public static List<Map<String,String>> getAirSwitchByTransformer(String equipid){
		String sql = "SELECT A.ID,A.NAME FROM "+CBSystemConstants.equipUser+"T_M_STATUSINPUT A,"+CBSystemConstants.equipUser+"T_EQUIPINFO B WHERE "
				+ "A.EQUIPOID = B.EQUIP_ID AND  B.EQUIP_ID = '"+equipid+"' AND A.REFMEASUREMENTTYPE = '压板' AND A.NAME LIKE '%空开%'  AND A.NAME NOT LIKE '%地刀%' AND A.NAME NOT LIKE '%接地开关%' AND A.NAME NOT LIKE '%备用%' AND A.NAME NOT LIKE '%未实验%' AND A.NAME NOT LIKE '%开关机%' AND A.NAME NOT LIKE '%相%'";
		
		List<Map<String,String>> returnList = DBManager.queryForList(sql);
		
		return returnList;
	}
	
	public static List<Map<String,String>> getAirSwitchByMotherLine(String equipid){
		String sql = "SELECT A.ID,A.NAME FROM "+CBSystemConstants.equipUser+"T_M_STATUSINPUT A,"+CBSystemConstants.equipUser+"T_EQUIPINFO B WHERE "
				+ "A.EQUIPOID = B.BAY_ID AND  B.EQUIP_ID = '"+equipid+"' AND A.REFMEASUREMENTTYPE = '压板' AND A.NAME LIKE '%空开%' AND A.NAME NOT LIKE '%备用%'  AND A.NAME NOT LIKE '%地刀%' AND A.NAME NOT LIKE '%接地开关%' AND A.NAME NOT LIKE '%未实验%' AND A.NAME NOT LIKE '%开关机%' AND A.NAME NOT LIKE '%相%'";
		
		List<Map<String,String>> returnList = DBManager.queryForList(sql);
		
		return returnList;
	}
}
