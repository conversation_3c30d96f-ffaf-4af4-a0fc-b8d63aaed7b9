package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.*;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionWS;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.uitl.StringUtils;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWS23JXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山二分之三接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					Collections.reverse(zbgyckgList);
				}
				break;
			}
			
			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(jddzList);
				
				otherzxdjddzList.addAll(jddzList);
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionWS.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				for(PowerDevice dev : zbgyckgList){
					if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
						Collections.reverse(dzList);
						
						for(PowerDevice dz : dzList){
							List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
							dzTempList.add(dz);
							
							replaceStr += CommonFunctionWS.getKnifeOnContent(dzTempList, stationName);
						}
					}else{
						String mxName = "";
						
						if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
							
							for(PowerDevice mx : mxList){
								mxName = "联"+CZPService.getService().getDevName(mx);
								break;
							}
						}
						
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转"+mxName+"热备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> zycdzList = CommonFunctionWS.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunctionWS.getKnifeOnContent(zycdzList,stationName);
					
					if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
						String mxName = "";
						
						if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
							
							for(PowerDevice mx : mxList){
								mxName = "联"+CZPService.getService().getDevName(mx);
								break;
							}
						}
						
						replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转"+mxName+"热备用程序操作/r/n";
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						replaceStr += CommonFunctionWS.getKnifeOnCheckContent(dzList, stationName);
					}else{
						String mxName = "";
						
						if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
							
							for(PowerDevice mx : mxList){
								mxName = "联"+CZPService.getService().getDevName(mx);
								break;
							}
						}
						
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转"+mxName+"热备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					List<PowerDevice> dycdzList = CommonFunctionWS.getTransformerKnife(curDev, dev);
					replaceStr += CommonFunctionWS.getKnifeOnContent(dycdzList,stationName);
					
					if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						replaceStr += CommonFunctionWS.getKnifeOnContent(dzList,stationName);
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : dycmxList){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已处热备用/r/n";
				String dzName = getptdzName(dev);
				if(StringUtils.isNotEmpty(dzName)){
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+dzName+"已处运行/r/n";
				}else{
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"电压互感器已处运行/r/n";
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionWS.getCdOrHhContent(dev, "文山地调", stationName,"对"+deviceName+"充电");
					//"文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					break;
				}
			}
			
			Collections.reverse(zbgyckgList);
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionWS.getCdOrHhContent(dev, "文山地调", stationName,"1");
					break;
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionWS.getCdOrHhContent(dev, "文山地调", stationName,"1");
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					String dycmxName = "";
					
					for(PowerDevice dycmx : dycmxList){
						dycmxName = CZPService.getService().getDevName(dycmx);
					}
					
					replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+dycmxName+"充电/r/n";
				}
			}
			
			if(zxdjddzList.size() > 0){					
				replaceStr += CommonFunctionWS.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
			}
			
			if(otherzxdjddzList.size() > 0){
				replaceStr += CommonFunctionWS.getZxdJddzOffCheckContent(otherzxdjddzList, stationName, station);
			}
		}
		
		return replaceStr;
	}

	private String getptdzName(PowerDevice curDev){
		String dzName = "";
		List<PowerDevice> ptdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, "", true, true, true, true);
		if (ptdzList.isEmpty())
			return dzName;
		for(PowerDevice dev : ptdzList){
			String devNum = CZPService.getService().getDevNum(dev);
			String devName = CZPService.getService().getDevName(dev);
			if(devName.contains("PT")){
				dzName+=devName.replace("PT"+devNum, "电压互感器"+"PT"+devNum)+"、";
			}else{
				dzName+=devName.replace(devNum, "电压互感器"+devNum)+"、";
			}
		}
		if(dzName.length()>0){
			dzName = dzName.substring(0, dzName.length()-1);
		}
		return dzName;
	}
}
