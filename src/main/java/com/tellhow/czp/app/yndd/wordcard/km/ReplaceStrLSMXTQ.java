package com.tellhow.czp.app.yndd.wordcard.km;

import com.tellhow.czp.app.yndd.rule.km.JudgeLoopClosing;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLSMXTQ implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("落实母线同期".equals(tempStr)) {
			/*List<PowerDevice> xls = new ArrayList<PowerDevice>();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev =  it.next();
				if (dev.getDeviceType().equals(SystemConstants.InOutLine)&& dev.getPowerVoltGrade()==curDev.getPowerVoltGrade()) {
					xls.add(dev);
				}
			}
			
			Set<String> set = new HashSet<String>();
			
			for(PowerDevice xl : xls){
				List<PowerDevice> list = RuleExeUtil.getLineOtherSideList(xl);
				
				if(list.size()>0){
					for(PowerDevice line : list){
						String stationName = line.getPowerStationName();
						
						if(stationName.contains("110kV")){
							continue;
						}
						
						set.add(stationName);
					}
				}else{
					ShowMessage.view(xl+"没有找到对侧变电站");
				}
			}
			
			if(set.size() == 1) {
				set.add("XX站");
			}
			
			replaceStr = "落实";
			
			for(String str : set){
				replaceStr += str+"220kV母线与";
			}
			
			if(replaceStr.endsWith("与")){
				replaceStr = replaceStr.substring(0, replaceStr.length()-1);
			}
			*/
			replaceStr +="落实220kVXXX变220kV母线与220kVXXX变220kV母线为同期系统";
			
			if(JudgeLoopClosing.flag.equals("不需要合环")){
				replaceStr = null;
			}
			
		}
		return replaceStr;
	}


}