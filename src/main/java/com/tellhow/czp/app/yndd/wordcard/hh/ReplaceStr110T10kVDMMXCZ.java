package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr110T10kVDMMXCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("110T10kV单母母线操作".equals(tempStr)){
			CommonFunctionHH cf  = new CommonFunctionHH();
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			
			String stationName = CZPService.getService().getDevName(station); 
			String sbName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> hsdevList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> bbzdzkgList = new ArrayList<PowerDevice>();//可能存在非标准接线刀闸
			
			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> fhczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> drqkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, true, true);
		    List<PowerDevice> mxkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		    List<PowerDevice> mxdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, true);

			List<PowerDevice> bbzdzList = new ArrayList<PowerDevice>();
			List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
		    List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
		    List<PowerDevice> yxmlkgList = new ArrayList<PowerDevice>();
		    List<PowerDevice> bbzmlkgdzList = new ArrayList<PowerDevice>();
		    
			List<PowerDevice> zybdzList = new ArrayList<PowerDevice>();
		    
		    if(mlkgList.size()>0){
		    	for(PowerDevice mlkg : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(mlkg).equals("0")){
						yxmlkgList.add(mlkg);
					}
				}
		    	
		    	List<PowerDevice> mlkgscList =  RuleExeUtil.getDeviceDirectList(mlkgList.get(0), SystemConstants.SwitchSeparate);
			    List<PowerDevice> mlkgdzList = RuleExeUtil.getDeviceList(mlkgList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
			    List<PowerDevice> mlkgkgList = RuleExeUtil.getDeviceList(mlkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, false);

			    if(mlkgdzList.size()>2){
			    	for(Iterator<PowerDevice> itor = mlkgdzList.iterator();itor.hasNext();){
						PowerDevice dz = itor.next();
						
						if(mlkgscList.contains(dz)){
							itor.remove();
						}
					}
			    	
			    	bbzmlkgdzList.addAll(mlkgdzList);
			    }else{
			    	bbzmlkgdzList.addAll(mlkgkgList);
			    }
		    }
		    
	    	if(mxkgList.size()>0){
	    		for(PowerDevice dev : mxkgList){
	    			if(dev.getDeviceType().equals(SystemConstants.Switch)){
						if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
							jdzybkgList.add(dev);
						}
						
						if(dev.getPowerDeviceName().contains("站用变")&&!dev.getPowerDeviceName().contains("接地站用变")){
							zybkgList.add(dev);
						}
					}
	    		}
	    	}
			
	    	if(mxdzList.size()>0){
	    		for(PowerDevice dev : mxdzList){
					if(dev.getPowerDeviceName().contains("站用变")&&!dev.getPowerDeviceName().contains("接地站用变")){
						zybdzList.add(dev);
					}
	    		}
	    	}
	    	
			bbzdzkgList.addAll(fhczbkgList);
			bbzdzkgList.addAll(drqkgList);
			bbzdzkgList.addAll(jdzybkgList);
			bbzdzkgList.addAll(zybkgList);
			
			for(PowerDevice bbzdzkg : bbzdzkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceList(bbzdzkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, false);
				
				if(dzList.size()>2){
					for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
						PowerDevice dz = itor.next();
						
						if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){//复电
							itor.remove();
						}
					}
					
					bbzdzList.addAll(dzList);
				}
			}
			
			hsdevList.addAll(mlkgList);
			hsdevList.addAll(drqkgList);
			hsdevList.addAll(zybkgList);
			hsdevList.addAll(fhczbkgList);
			
		    List<PowerDevice> hotdevList = new ArrayList<PowerDevice>();
		    List<PowerDevice> colddevList = new ArrayList<PowerDevice>();

		    for(PowerDevice hsdev :hsdevList){
		    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("1")){
		    		hotdevList.add(hsdev);
		    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("2")){
		    		colddevList.add(hsdev);
		    	}
		    }
		    
		    if(hotdevList.size()>0){
		    	replaceStr += "核实"+ CZPService.getService().getDevName(hotdevList)+"热备用/r/n";
		    }
		    
		    if(bbzdzList.size()>0){
			    replaceStr += "核实"+CZPService.getService().getDevName(bbzdzList)+"在合闸位置/r/n";
		    }
		    
		    if(zybdzList.size()>0){
			    replaceStr += "核实"+CZPService.getService().getDevName(zybdzList)+"在拉开位置/r/n";
			    replaceStr += "核实10kV#X站用变冷备用/r/n";
		    }
		    
		    if(colddevList.size()>0){
		    	replaceStr += "核实"+ CZPService.getService().getDevName(colddevList)+"冷备用/r/n";
		    }
		    
		    replaceStr += "红河配调@核实"+stationName+sbName+"上其所管辖的所有10kV出线断路器均己转冷备用/r/n";
		    
		    if(yxmlkgList.size() == 0){
			    replaceStr += "退出10kV备自投装置/r/n";
		    }			
		    
		    if(fhczbkgList.size()>0){
		    	fhczbkgList.addAll(0,yxmlkgList);
		    	fhczbkgList.addAll(0,jdzybkgList);
		    
		    	for(Iterator<PowerDevice> itor = fhczbkgList.iterator();itor.hasNext();){
					PowerDevice fhczbkg = itor.next();
					
					if(!RuleExeUtil.getDeviceBeginStatus(fhczbkg).equals("0")){
						itor.remove();
					}
				}
		    	
		    	replaceStr += cf.getYcDkStrReplace(fhczbkgList, stationName);
		    }
		    
		    fhczbkgList.addAll(drqkgList);
		    
		    for(PowerDevice mlkg : mlkgList){
		    	if(!fhczbkgList.contains(mlkg)){
				    fhczbkgList.add(mlkg);
			    }
		    }
		    
		    fhczbkgList.addAll(zybkgList);

		    if(bbzdzList.size()>0){
			    replaceStr += "拉开"+CZPService.getService().getDevName(bbzdzList)+"/r/n";
		    }
		    
		    for (Iterator<PowerDevice> it2 = fhczbkgList.iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
					it2.remove();
				}
			}
		    
		    replaceStr += cf.getCzMotherLineDevStrReplace(fhczbkgList, curDev, null, stationName, "由热备用转冷备用");
		    
    		if(bbzmlkgdzList.size()>0){
	    		 replaceStr += "核实10kV分段"+CZPService.getService().getDevNum(bbzmlkgdzList.get(0))+"隔离手车冷备用/r/n";
    		}
    		
    		List<PowerDevice> zbList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "", true, true, true);
    		
		    if(zbList.size()>0){
			    replaceStr += "退出"+CZPService.getService().getDevName(zbList)+"10kV侧后备保护动作跳主变三侧断路器/r/n";
		    }
		}
		
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
