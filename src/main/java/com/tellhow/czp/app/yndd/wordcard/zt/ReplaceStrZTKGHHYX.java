package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTKGHHYX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通开关合环运行".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<String> voltList = new ArrayList<String>();
			
			voltList.add((int)curDev.getPowerVoltGrade()+"kV备自投装置");
			
			replaceStr += CommonFunction.getBztResult(voltList , "退出");
			replaceStr += CommonFunction.getHhContent(curDev, "昭通地调", stationName);
		}
		
		return replaceStr;
	}

}
