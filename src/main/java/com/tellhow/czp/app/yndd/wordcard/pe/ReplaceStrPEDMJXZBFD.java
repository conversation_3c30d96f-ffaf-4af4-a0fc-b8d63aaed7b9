package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEDMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱单母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			replaceStr += stationName+"@核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
			
			if(TicketKindChoose.flag.equals("全部手动")){
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}else if(TicketKindChoose.flag.equals("全部程序化")){
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					replaceStr += "普洱地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					
					for(PowerDevice dev : zbgyckgList){
//						replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev);
					}
					
					for(PowerDevice dev : zbzyckgList){
//						replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev);
					}
					
					for(PowerDevice dev : zbdyckgList){
//						replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev);
					}
				}
				
				if(curDev.getDeviceStatus().equals("0")){
					replaceStr += "普洱地调@执行"+stationName+deviceName+"由热备用转运行程序操作/r/n";
				}
			}else{
				for(PowerDevice dev : zbgyckgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
							replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
//							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev);
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
							replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
//							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev);
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
				}
				
				List<PowerDevice> zbdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeZB, "", true, true, true, true);
				
				for(PowerDevice dev : zbdzList){
					replaceStr += stationName+"@合上"+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
							replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
//							replaceStr += CommonFunctionPE.getKnifeOnCheckContent(dev);
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
