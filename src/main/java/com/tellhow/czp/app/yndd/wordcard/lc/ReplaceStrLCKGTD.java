package com.tellhow.czp.app.yndd.wordcard.lc;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLC;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLCKGTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("临沧开关停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			String beginStatus = CBSystemConstants.getCurRBM().getBeginStatus();
			String endStatus = CBSystemConstants.getCurRBM().getEndState();

			if(beginStatus.equals("0") && endStatus.equals("1")){
				replaceStr += CommonFunctionLC.getSwitchOffContent(curDev, stationName ,station);
			}else if(beginStatus.equals("1") && endStatus.equals("2")){
				if(CommonFunctionLC.ifSwitchSeparateControlLC(curDev)){
					replaceStr += "临沧地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionLC.getKnifeOffCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
			}else if(beginStatus.equals("0") && endStatus.equals("2")){
				replaceStr += CommonFunctionLC.getSwitchOffContent(curDev, stationName ,station);
				
				if(CommonFunctionLC.ifSwitchSeparateControlLC(curDev)){
					replaceStr += "临沧地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionLC.getKnifeOffCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
