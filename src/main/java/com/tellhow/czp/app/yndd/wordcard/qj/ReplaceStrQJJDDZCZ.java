package com.tellhow.czp.app.yndd.wordcard.qj;


import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrQJJDDZCZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("曲靖接地刀闸操作".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			if(CommonFunctionQJ.ifSwitchSeparateControl(curDev)){
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
					replaceStr += "曲靖地调@遥控合上"+stationName+deviceName+"/r/n";
				}else{
					replaceStr += "曲靖地调@遥控拉开"+stationName+deviceName+"/r/n";
				}
			}else{
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
					replaceStr += stationName+"@合上"+deviceName+"/r/n";
				}else{
					replaceStr += stationName+"@拉开"+deviceName+"/r/n";
				}
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
