package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoosePw;
import czprule.system.CBSystemConstants;

public class ZTChooseSwitch implements RulebaseInf{
	  EquipCheckChoosePw ecc = null;
	  public static Map<String, String> deviceTdBeginState = new HashMap<String, String>();
	  public static Map<String, String> deviceTdEndState = new HashMap<String, String>();
	  
	  public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.isCurrentSys) {
			
			final List<PowerDevice> loadMap = new ArrayList<PowerDevice>();

			if ((!CBSystemConstants.isSame.booleanValue())
					&& (CBSystemConstants.getSamepdlist().size() == 0)) {
				CBSystemConstants.isSame = Boolean.valueOf(true);
				new Thread(new Runnable() {
					public void run() {
						ZTChooseSwitch.this.ecc = new EquipCheckChoosePw(
								SystemConstants.getMainFrame(), false, loadMap,
								"请选择目标设备", CBSystemConstants.getCurRBM());
					}
				}).start();
				return false;
			}

			for (int i = 0; i < CBSystemConstants.getSamepdlist().size(); i++) {
				PowerDevice pd = (PowerDevice) CBSystemConstants.getSamepdlist().get(i);

				if (pd.getDeviceType().equals(SystemConstants.Switch)) {
					RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), rbm.getEndState());
				}
			}
		}

		return true;
	  }
}