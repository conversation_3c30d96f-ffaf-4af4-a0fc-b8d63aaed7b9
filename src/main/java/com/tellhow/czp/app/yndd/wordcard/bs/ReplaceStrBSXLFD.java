package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.bs.HaveWorkDialog;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSXLFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山线路复电".equals(tempStr)){
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			String deviceName = CZPService.getService().getDevName(curDev);
			
			String sql = "SELECT UNIT,LOWERUNIT,SWITCH_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,OPERATION_KIND FROM "+CBSystemConstants.equipUser+"T_A_CARDWORDUSER WHERE LINE_ID = '"+sourceLineTrans.getCimID()+"'";
			List<Map<String,String>> userStationList = DBManager.queryForList(sql);

			List<PowerDevice> haveworkList = HaveWorkDialog.tagDevMapInStation.get("有工作");
			List<PowerDevice> nothaveworkList = HaveWorkDialog.tagDevMapInStation.get("无工作");

			if(haveworkList != null){
				for(PowerDevice dev : haveworkList){
					String switchName = CZPService.getService().getDevName(dev);
					
					if(!dev.getPowerDeviceDefaultSta().equals("")){
						String stationName = dev.getPowerDeviceDefaultSta();
						String lowerStationName = dev.getPowerStationName();
						
						if(stationName.equals(lowerStationName)){
							replaceStr += stationName +"@核实"+switchName+"间隔相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
						}else{
							replaceStr += stationName +"@核实"+lowerStationName+switchName+"间隔相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
						}
					}else{
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String maintenance = CommonFunctionBS.getMaintenance(stationName);
						
						if(stationName.equals(maintenance)){
							replaceStr += stationName +"@核实"+switchName+"间隔相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
						}else{
							replaceStr += maintenance +"@核实"+stationName+switchName+"间隔相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
						}
					}
				}
			}
			
			if(nothaveworkList != null){
				for(PowerDevice dev : nothaveworkList){
					
					if(!dev.getPowerDeviceDefaultSta().equals("")){
						String stationName = dev.getPowerDeviceDefaultSta();
						String lowerStationName = dev.getPowerStationName();
						
						if(stationName.equals(lowerStationName)){
							replaceStr += stationName +"@核实"+deviceName+"线停电期间，"+stationName+"站内未开展过相关俭修工作，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
						}else{
							replaceStr += stationName +"@核实"+deviceName+"线停电期间，"+lowerStationName+"站内未开展过相关俭修工作，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
						}
					}else{
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String maintenance = CommonFunctionBS.getMaintenance(stationName);
						
						if(stationName.equals(maintenance)){
							replaceStr += stationName +"@核实"+deviceName+"线停电期间，"+stationName+"站内未开展过相关俭修工作，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
						}else{
							replaceStr += maintenance +"@核实"+deviceName+"线停电期间，"+stationName+"站内未开展过相关俭修工作，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
						}
					}
				}
			}
			
			List<PowerDevice> haveworklineList = HaveWorkDialog.tagDevMapInLine.get("有工作");
			List<PowerDevice> nothaveworklineList = HaveWorkDialog.tagDevMapInLine.get("无工作");

			if(haveworklineList != null){
				for(PowerDevice dev : haveworklineList){
					String switchName = CZPService.getService().getDevName(dev);
					
					if(!dev.getPowerDeviceDefaultSta().equals("")){
						String stationName = dev.getPowerDeviceDefaultSta();
						String lowerStationName = dev.getPowerStationName();
						String linename = dev.getPowerDeviceName();

						if(stationName.equals(lowerStationName)){
							replaceStr += stationName +"@核实"+linename+"线路相关检修工作已全部结束，作业人员已全部撤离,现场所有临时措施已拆除，"+deviceName+"具备复电条件/r/n";
						}else{
							replaceStr += stationName +"@核实"+lowerStationName+linename+"线路相关检修工作已全部结束，作业人员已全部撤离,现场所有临时措施已拆除，"+deviceName+"具备复电条件/r/n";
						}
					}else{
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String maintenance = CommonFunctionBS.getMaintenance(stationName);
						
						if(stationName.equals(maintenance)){
							replaceStr += stationName +"@核实"+deviceName+"线路相关检修工作已全部结束，作业人员已全部撤离,现场所有临时措施已拆除，"+deviceName+"具备复电条件/r/n";
						}else{
							replaceStr += maintenance +"@核实"+stationName+switchName+"线路相关检修工作已全部结束，作业人员已全部撤离,现场所有临时措施已拆除，"+deviceName+"具备复电条件/r/n";
						}
					}
				}
			}
			
			if(nothaveworklineList != null){
				for(PowerDevice dev : nothaveworklineList){
					
					if(!dev.getPowerDeviceDefaultSta().equals("")){
						String stationName = dev.getPowerDeviceDefaultSta();
						String lowerStationName = dev.getPowerStationName();
						String linename = dev.getPowerDeviceName();

						if(stationName.equals(lowerStationName)){
							replaceStr += stationName +"@核实"+linename+"停电期间，"+linename+"线路未开展相关检修工作，"+linename+"具备复电条件/r/n";
						}else{
							replaceStr += stationName +"@核实"+lowerStationName+linename+"停电期间，"+linename+"线路未开展相关检修工作，"+linename+"具备复电条件/r/n";
						}
					}else{
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String maintenance = CommonFunctionBS.getMaintenance(stationName);
						
						if(stationName.equals(maintenance)){
							replaceStr += stationName +"@核实"+deviceName+"停电期间，"+deviceName+"线路未开展相关检修工作，"+deviceName+"具备复电条件/r/n";
						}else{
							replaceStr += maintenance +"@核实"+stationName+deviceName+"停电期间，"+deviceName+"线路未开展相关检修工作，"+deviceName+"具备复电条件/r/n";
						}
					}
				}
			}
			
			
			/*
			 * 检修转冷备用
			 */
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("3")){
				if(userStationList.size() > 0){
					for(Map<String,String> userStationMap : userStationList){
						String unit = StringUtils.ObjToString(userStationMap.get("UNIT"));
						String lowerunit = StringUtils.ObjToString(userStationMap.get("LOWERUNIT"));
						String jddzName = StringUtils.ObjToString(userStationMap.get("GROUNDDISCONNECTOR_NAME"));

						replaceStr += unit+"@拉开"+lowerunit+jddzName+"/r/n";
					}
				}else{
					for(PowerDevice dev : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String maintenance = CommonFunctionBS.getMaintenance(stationName);

						List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
						
						for(PowerDevice jddz : jddzList){
							if(maintenance.equals(stationName)){
								replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddz)+"/r/n";
							}else{
								replaceStr += maintenance+"@拉开"+stationName+CZPService.getService().getDevName(jddz)+"/r/n";
							}
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					String maintenance = CommonFunctionBS.getMaintenance(stationName);

					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					  
					for(PowerDevice jddz : jddzList){
						if(maintenance.equals(stationName)){
							replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(jddz)+"/r/n";
						}else{
							replaceStr += maintenance+"@拉开"+stationName+CZPService.getService().getDevName(jddz)+"/r/n";
						}
					}
				}
			}

			for(Map<String,String> userStationMap : userStationList){
				String unit = StringUtils.ObjToString(userStationMap.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(userStationMap.get("LOWERUNIT"));
				String switchName = StringUtils.ObjToString(userStationMap.get("SWITCH_NAME"));
				String endpointKind = StringUtils.ObjToString(userStationMap.get("ENDPOINT_KIND"));
				String operationKind = StringUtils.ObjToString(userStationMap.get("OPERATION_KIND"));
				
				if(endpointKind.equals("单线并网电厂")){
					
				}else{
					if(operationKind.equals("许可")){
						replaceStr += unit+"@核实"+lowerunit+switchName+"处热备用/r/n";
					}else if(operationKind.equals("下令")){
						replaceStr += unit+"@将"+lowerunit+switchName+"由冷备用转热备用/r/n";
					}
				}
			}
			
			List<String> normalList = new ArrayList<String>();
			
			if(sourceLineTrans!=null){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);
				
				if(zbList.size()>0){
					for(PowerDevice dev : zbList){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转运行/r/n";
					}
				}else{
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String switchName = CZPService.getService().getDevName(dev);
						String maintenance = CommonFunctionBS.getMaintenance(stationName);

						if(maintenance.equals(stationName)){
							replaceStr += stationName+"@核实"+switchName+"一、二次设备具备程序化操作条件/r/n";
							String normal = stationName+"@核实"+switchName+"一、二次设备运行正常/r/n";
							normalList.add(normal);
						}else{
							replaceStr += maintenance+"@核实"+stationName+switchName+"一、二次设备具备程序化操作条件/r/n";
							String normal = maintenance+"@核实"+stationName+switchName+"一、二次设备运行正常/r/n";
							normalList.add(normal);
						}
					}
				}
			}	

			if(sourceLineTrans.getPowerVoltGrade() > 110){
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					String maintenance = CommonFunctionBS.getMaintenance(stationName);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						String switchName = CZPService.getService().getDevName(dev);

						if(maintenance.equals(stationName)){
							replaceStr += stationName+"@核实"+switchName+"一、二次设备具备程序化操作条件/r/n";
							String normal = stationName+"@核实"+switchName+"一、二次设备运行正常/r/n";
							normalList.add(normal);
						}else{
							replaceStr += maintenance+"@核实"+stationName+switchName+"一、二次设备具备程序化操作条件/r/n";
							String normal = maintenance+"@核实"+stationName+switchName+"一、二次设备运行正常/r/n";
							normalList.add(normal);
						}
					}
				}
			}
			
			/*
			 * 冷备用转热备用
			 */
			if(userStationList.size() > 0 || sourceLineTrans.getPowerVoltGrade() == 110 || curDev.getPowerVoltGrade() == 35){
				if(sourceLineTrans!=null){
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					for(PowerDevice dev : xlswList){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						String switchName = CZPService.getService().getDevName(dev);

						if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
							replaceStr += "保山地调@执行"+stationName+switchName+"由热备用转运行程序操作/r/n";
						}else{
							replaceStr += "保山地调@执行"+stationName+switchName+"由冷备用转运行程序操作/r/n";
						}
					}
				}
			}else{
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
					replaceStr += "保山地调@执行"+CZPService.getService().getDevName(curDev)+"由热备用转运行程序操作/r/n";
				}else{
					replaceStr += "保山地调@执行"+CZPService.getService().getDevName(curDev)+"由冷备用转运行程序操作/r/n";
				}
			}

			for(String normal : normalList){
				replaceStr += normal;
			}
			
			for(Map<String,String> userStationMap : userStationList){
				String unit = StringUtils.ObjToString(userStationMap.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(userStationMap.get("LOWERUNIT"));
				String switchName = StringUtils.ObjToString(userStationMap.get("SWITCH_NAME"));
				String endpointKind = StringUtils.ObjToString(userStationMap.get("ENDPOINT_KIND"));
				String operationKind = StringUtils.ObjToString(userStationMap.get("OPERATION_KIND"));
				
				if(endpointKind.equals("单线并网电厂")){
					
				}else{
					if(operationKind.equals("许可")){
						replaceStr += unit+"@核实"+lowerunit+switchName+"处运行/r/n";
					}else if(operationKind.equals("下令")){
						replaceStr += unit+"@用"+lowerunit+switchName+"同期合环/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
