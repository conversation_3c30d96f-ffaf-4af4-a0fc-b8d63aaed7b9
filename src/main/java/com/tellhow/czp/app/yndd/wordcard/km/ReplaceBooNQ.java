package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooNQ implements TempBooleanReplace {
	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
		PowerDevice stationDev, String desc) {
	
		if (tempStr.equals("内桥")) {
			List<PowerDevice> motherLineList  = RuleExeUtil.getDeviceList(stationDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());
			
			if(curDev.getDeviceType().equals(SystemConstants.MotherLine)){
				motherLineList.add(curDev);
			}
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(RuleUtil.isTransformerNQ(dev)){
						if(motherLineList.size()>0){
							if(motherLineList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
								return true;
							}
						}
					}
				}
			}
		}
		
		return false;
	}
}
