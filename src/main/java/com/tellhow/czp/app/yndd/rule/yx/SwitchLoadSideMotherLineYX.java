package com.tellhow.czp.app.yndd.rule.yx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.SwitchChangeMotherLine;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 倒旁路执行类，传入线路开关/主变开关进行旁代操作
 * 作    者: 郑柯
 * 开发日期: 2012-9-18 下午02:22:51 
 */
public class SwitchLoadSideMotherLineYX implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {

		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		if(!pd.getDeviceType().equals(SystemConstants.Switch)){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]不是开关！");
        	return false;
        }
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) &&
				!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC) &&
				!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]不能倒旁路！");
        	return false;
        }
		if(!"0".equals(pd.getDeviceStatus())){
			ShowMessage.view("开关["+pd.getPowerDeviceName()+"]必须处于运行状态！");
        	return false;
		}
		
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
        inPara.put("tagDevType", SystemConstants.MotherLine);
        cs.execute(inPara, outPara);
		inPara.clear();
		PowerDevice sideML=null;
		PowerDevice sideKnife=null;
		List tempMLs = (ArrayList) outPara.get("linkedDeviceList");
		for (int i = 0; i < tempMLs.size(); i++) {
			PowerDevice tempML = (PowerDevice) tempMLs.get(i);
            if(tempML.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
            	sideML = tempML;
            	ArrayList<PowerDevice> tempDevs= ((HashMap<PowerDevice,ArrayList<PowerDevice>>)outPara.get("pathList")).get(sideML);
            	for (int j = 0; j < tempDevs.size(); j++) {
            		if(tempDevs.get(j).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
            			sideKnife = tempDevs.get(j);
            			break;
            		}
            	}
            	break;
            }
		}
		if(sideML == null){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]没有可导的旁路母线！");
        	return false;
        }
//		if(!sideML.getDeviceStatus().equals("0")){
//        	ShowMessage.view("["+pd.getPowerDeviceName()+"]旁路母线不在运行状态！");
//        	return false;
//        }
		if(sideKnife == null){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]没有可以连接的旁路刀闸！");
        	return false;
        }
		if(sideKnife.getDeviceStatus().equals("0")){
        	ShowMessage.view("["+sideKnife.getPowerDeviceName()+"]连接的旁路刀闸不在断开状态！");
        	return false;
        }
		inPara.put("oprSrcDevice", sideML);
        inPara.put("tagDevType", SystemConstants.Switch);
        cs.execute(inPara, outPara);
		inPara.clear();
		PowerDevice sideSwitch=null;
		List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
		for (int i = 0; i < tempSwitchs.size(); i++) {
			PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(i);
            if(tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) ||
            		tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
            	sideSwitch = tempSwitch;
            	break;
            }
		}
		if(sideSwitch == null){
			List<PowerDevice> othersideMLList = RuleExeUtil.getDeviceList(sideML, SystemConstants.MotherLine, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSideMother, "", false, true, true, true);
			
			for(PowerDevice othersideML : othersideMLList){
				inPara.put("oprSrcDevice", othersideML);
				inPara.put("tagDevType", SystemConstants.Switch);
				cs.execute(inPara, outPara);
				inPara.clear();
				tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
				for (int i = 0; i < tempSwitchs.size(); i++) {
					PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(i);
					if (tempSwitch.getDeviceRunType().equals(
							CBSystemConstants.RunTypeSwitchPL)||tempSwitch.getDeviceSetType().equals(
									CBSystemConstants.RunTypeSwitchMLPL)) {
						sideSwitch = tempSwitch;
						break;
					}
				}
			}
			
			if (sideSwitch == null) {
				ShowMessage.view("[" + sideML.getPowerDeviceName()
						+ "]没有连接到旁路开关！");
				return false;
			}
		}
		if(sideSwitch.getDeviceStatus().equals("0")){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]连接的["+CZPService.getService().getDevName(sideSwitch)+"开关]在运行状态！");
        	return false;
        }
		if(sideSwitch.getDeviceStatus().equals("3")){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]连接的["+CZPService.getService().getDevName(sideSwitch)+"开关]在检修状态！");
        	return false;
        }

		boolean flag = CommonFunction.isSwitchDiffMotherLine(pd);
		
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			if(flag){
				PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
				
				List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev2 = it2.next();
					
					if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
						if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							hignVoltMlkgList.add(dev2);
						}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							hignVoltXlkgList.add(dev2);
						}
					}
				}
				
				List<PowerDevice> xlkg = new ArrayList<PowerDevice>();
				for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = (PowerDevice) it2.next();
					
					if(hignVoltMlkgList.size() == 1 && hignVoltXlkgList.size() > 2){
						if(station.getPowerVoltGrade() == dev.getPowerVoltGrade() && (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) )){
							if(dev.getDeviceStatus().equals("1")){
								RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
							}
						}
						
						if(station.getPowerVoltGrade() == dev.getPowerVoltGrade()
								&& dev.getDeviceStatus().equals("0") && !RuleExeUtil.isDeviceChanged(dev)){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) ){
								xlkg.add(dev);
							}
						}
					}
				}
				
				String showMessage="请选择解环的断路器";
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, xlkg, showMessage);
				List<PowerDevice> chooseEquips = ecc.getChooseEquip();
				if(ecc.isCancel()){
					return false;
				}
				
				for(PowerDevice sw : chooseEquips){
					RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
				}
			}
			
			for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = (PowerDevice) it2.next();
				
				if(pd.getPowerVoltGrade() == dev.getPowerVoltGrade() && dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getDeviceStatus().equals("1")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
				}
			}
		}
		
		//搜索旁路开关的旁路刀闸
		inPara.put("oprSrcDevice", sideSwitch);
        inPara.put("tagDevType", SystemConstants.SwitchSeparate);
        cs.execute(inPara, outPara);
		inPara.clear();
		List tempPLKGDZs = (ArrayList) outPara.get("linkedDeviceList");
		PowerDevice tempplkgdz = null;
		for(int i=0;i<tempPLKGDZs.size();i++){
			PowerDevice paraDev = (PowerDevice)tempPLKGDZs.get(i);
			if(paraDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)){
				tempplkgdz = paraDev;
				break;
			}
		}
		if(tempplkgdz==null){
			ShowMessage.view("未找到旁路开关的旁路刀闸！");
			return false;
		}
		
		//检测代路前除旁路开关外其余旁路母线侧刀闸均应在分闸位置
		inPara.put("oprSrcDevice", sideML);
        inPara.put("tagDevType", SystemConstants.SwitchSeparate);
        cs.execute(inPara, outPara);
		inPara.clear();
		List tempPLDZs = (ArrayList) outPara.get("linkedDeviceList");
		for(int i=0;i<tempPLDZs.size();i++){
			PowerDevice temppldz = (PowerDevice)tempPLDZs.get(i);
			if(temppldz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)){
				if( (!temppldz.getPowerDeviceID().equals(sideKnife.getPowerDeviceID()))&&
						(!temppldz.getPowerDeviceID().equals(tempplkgdz.getPowerDeviceID()))&& temppldz.getDeviceStatus().equals("0")){
					ShowMessage.view("["+CZPService.getService().getDevName(temppldz)+"]刀闸不在断开状态！");
					return false;
				}
			}
		}
		
		boolean result = true;
		//旁路开关转热备用
		result = RuleExeUtil.deviceStatusExecute(sideSwitch, sideSwitch.getDeviceStatus(), "1");
		if(!result)
			return false;
		if(sideSwitch.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(sideSwitch, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			for(Iterator it = mlList.iterator();it.hasNext();) {
				PowerDevice ml = (PowerDevice)it.next();
				if(ml.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
					it.remove();
			}
			if(mlList.size() == 2) {
//				EquipRadioChooseGZ dcd = new EquipRadioChooseGZ(
//						SystemConstants.getMainFrame(), true, mlList,"请选择["+CZPService.getService().getDevName(sideSwitch)+"]挂接的母线");
				PowerDevice defaultML = null;
				List<PowerDevice> mlOnList = RuleExeUtil.getDeviceList(sideSwitch, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				for (int i = 0; i < mlList.size(); i++) {
					if(mlOnList.contains(mlList.get(i))){
						defaultML = mlList.get(i);
//						dcd.setChooseEquip(defaultML);
						break;
					}
				}
//				PowerDevice chooseML = dcd.getChooseEquip();
//				if(!chooseML.equals(defaultML)) {
//					SwitchChangeMotherLine scml = new SwitchChangeMotherLine();
//					RuleBaseMode rbmPLKG = new RuleBaseMode();
//					rbmPLKG.setPd(sideSwitch);
//					scml.execute(rbmPLKG);
//				}
				
				//旁路开关默认与被代开关挂接同一条母线
				
				//当前开关链接母线
				PowerDevice dqMX = null;
				List<PowerDevice> pdOnList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				if(pdOnList.size()<1){
					ShowMessage.view("拓扑错误！开关["+pd.getPowerDeviceName()+"]没有连接母线！");
					return false;
				}else{
					for(int i=0;i<pdOnList.size();i++){
						if(!pdOnList.get(i).getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
							dqMX = pdOnList.get(i);
							break;
						}
					}
				}
				
				if(dqMX==null){
					ShowMessage.view("开关["+pd.getPowerDeviceName()+"]未找到挂接母线！");
					return false;
				}
				if(!dqMX.equals(defaultML)) {
					SwitchChangeMotherLine scml = new SwitchChangeMotherLine();
					RuleBaseMode rbmPLKG = new RuleBaseMode();
					rbmPLKG.setPd(sideSwitch);
					scml.execute(rbmPLKG);
				}
				
			}
		}
		
		if(pd.getPowerVoltGrade()>110){
			PowerDevice source =null;
			List<PowerDevice> load =new ArrayList<PowerDevice>();
			List<PowerDevice> xlList =RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine
					, SystemConstants.PowerTransformer, true, true, true);
			if(xlList.size()>0){
				source=xlList.get(0);
				load=RuleExeUtil.getLineOtherSideList(source);
			}
			if(source!=null&&load.size()>0){
				CBSystemConstants.putLineSource(source.getPowerDeviceID(), source);
				CBSystemConstants.putLineLoad(source.getPowerDeviceID(), load);
			}
	
		}
		//合上旁路刀闸
		result = RuleExeUtil.deviceStatusExecute(sideKnife, "1", "0");
		if(!result)
			return false;
		//合上旁路开关
		result = RuleExeUtil.deviceStatusExecute(sideSwitch, "1", "0");
		if(!result)
			return false;

		//合上旁路开关
		if(!result)
			return false;
		
		
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)&&flag){
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
			PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
			
			for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = (PowerDevice) it2.next();
				
				if(station.getPowerVoltGrade() == dev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)
							||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
						}
					}
				}
			}
			
			for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = (PowerDevice) it2.next();
				
				if(pd.getPowerVoltGrade() == dev.getPowerVoltGrade() && dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
			}
		}
		
		return true;
	}

}
