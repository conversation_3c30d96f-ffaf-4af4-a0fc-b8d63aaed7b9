package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.sun.java.help.search.Rule;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEDMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱单母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();

			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			}
			
			if(TicketKindChoose.flag.equals("全部手动")){
				for(PowerDevice dev : zbkgList){
					if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}else{
					for(PowerDevice dev : zbkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
				
				if(curDev.getDeviceStatus().equals("2")){
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
			}else if(TicketKindChoose.flag.equals("全部程序化")){
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
					replaceStr += "普洱地调@执行"+stationName+deviceName+"由运行转热备用程序操作/r/n";
				}
				
				if(curDev.getDeviceStatus().equals("2")){
					replaceStr += "普洱地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";

					List<PowerDevice> ptdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

					for(PowerDevice dev : ptdzList){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
							if(dev.getPowerDeviceName().contains("901") || dev.getPowerDeviceName().contains("902")){
								// 具体是否需要集成或是单独展示 待定
								replaceStr += "普洱地调@遥控拉开"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
								replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev,stationName);
							}
						}
					}
					
					for(PowerDevice mlkg : mlkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(mlkg, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dev : dzList){
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev,stationName);						
						}
					}
					
					for(PowerDevice zbkg : zbkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dev : dzList){
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev,stationName);						
						}
					}
					
					for(PowerDevice xlkg : xlkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlkg, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dev : dzList){
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev,stationName);						
						}
					}
				}
			}else{
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
					replaceStr += "普洱地调@执行"+stationName+deviceName+"由运行转热备用程序操作/r/n";
				}

				if(curDev.getDeviceStatus().equals("2")){
					for(PowerDevice mlkg : mlkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(mlkg, SystemConstants.SwitchSeparate);
						
						dzList = RuleExeUtil.sortByCZMXC(dzList);
						
						for(PowerDevice dev : dzList){
							replaceStr += "普洱地调@遥控拉开"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev,stationName);						
						}
					}
					
					for(PowerDevice zbkg : zbkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
						
						dzList = RuleExeUtil.sortByCZMXC(dzList);
						Collections.reverse(dzList);
						
						for(PowerDevice dev : dzList){
							replaceStr += "普洱地调@遥控拉开"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev,stationName);						
						}
					}
					
					for(PowerDevice xlkg : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")||RuleExeUtil.getDeviceBeginStatus(xlkg).equals("1")){
							if(CommonFunctionPE.ifSwitchSeparateControl(xlkg)){
								replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(xlkg)+"由热备用转冷备用程序操作/r/n";
							}
						}
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlkg, SystemConstants.SwitchSeparate);
						
						dzList = RuleExeUtil.sortByXLC(dzList);
						
						for(PowerDevice dev : dzList){
							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev,stationName);						
						}
					}
					
					replaceStr += stationName+"@核实"+deviceName+"TV二次空开在分闸位置/r/n";
					
					List<PowerDevice> ptdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

					for(PowerDevice dev : ptdzList){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
							if(dev.getPowerDeviceName().contains("901") || dev.getPowerDeviceName().contains("902")){
								replaceStr += "普洱地调@遥控拉开"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
								replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev,stationName);							
							}
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
