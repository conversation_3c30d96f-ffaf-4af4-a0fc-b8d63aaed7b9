package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.yndd.view.EquipStatusChooseView;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 操作设备关联其他设备目标状态选择器
 * 作    者: 张余平
 * 开发日期: 2016年8月3日TransformTDKindChoose
 */
public class EquipOperateChoose implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
		
		String devRunType = curRBM.getDeviceruntype();
		
		//一、搜索设备连接的开关
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();  //执行开关集合
		List<PowerDevice> tempswitchs=new ArrayList<PowerDevice>();  //开关集合
		PowerDevice tempDev=null;
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
			 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
			 if(sourceLineTrans==null||loadLineTrans==null){
				 //ShowMessage.view("请先设置线路两端变电站属性！");
				 return true;
			 }
			 
			 List<PowerDevice> sources=new ArrayList<PowerDevice>();
			 inPara.put("oprSrcDevice", sourceLineTrans);
             inPara.put("tagDevType", SystemConstants.Switch+","+SystemConstants.ElecShock);
             inPara.put("excDevType", SystemConstants.PowerTransformer);
             inPara.put("excDevRunType", CBSystemConstants.RunTypeKnifeQT);
             cs.execute(inPara, outPara);
    	 	 inPara.clear();
    	 	 tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
    	  	 for (int i = 0; i < tempswitchs.size(); i++) {
    	 		tempDev=(PowerDevice)tempswitchs.get(i);
    	 		if (devRunType.equals("") || !tempDev.getDeviceType().equals(SystemConstants.Switch)) {
    	 			sources.add(tempDev);
				} else {
					if (tempDev.getDeviceRunType().equals(devRunType))
						sources.add(tempDev);
				}
			 }
    	  	 
    	  	List<PowerDevice> loads=new ArrayList<PowerDevice>();
    	  	for (int i = 0; i < loadLineTrans.size(); i++) {
				inPara.put("oprSrcDevice", loadLineTrans.get(i));
				inPara.put("tagDevType", SystemConstants.Switch+","+SystemConstants.ElecShock);
	            inPara.put("excDevType", SystemConstants.PowerTransformer);
	            inPara.put("excDevRunType", CBSystemConstants.RunTypeKnifeQT);
	            cs.execute(inPara, outPara);
	    		inPara.clear();
	    		tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
	    		for (int j = 0; j < tempswitchs.size(); j++) {
	    			tempDev=(PowerDevice)tempswitchs.get(j);
	    	 		if (devRunType.equals("") || !tempDev.getDeviceType().equals(SystemConstants.Switch)) {
	    	 			loads.add(tempDev);
					} else {
						if (tempDev.getDeviceRunType().equals(devRunType))
							loads.add(tempDev);
					}
				}
			}
    	  	RuleExeUtil.swapDeviceList(sources);
    	  	RuleExeUtil.swapDeviceList(loads);
    	  	if(Integer.valueOf(curRBM.getBeginStatus())>Integer.valueOf(curRBM.getEndState())) {
    	  		switchs.addAll(sources);
    	  		switchs.addAll(loads);
    	  	}
    	  	else {
    	  		switchs.addAll(loads);
    	  		switchs.addAll(sources);
    	  	}
		}
		else if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){ //主变
			tempswitchs = RuleExeUtil.getLinkedSwitch(pd);
   	  	    for (int i = 0; i < tempswitchs.size(); i++) {
	   	 		tempDev=(PowerDevice)tempswitchs.get(i);
	   	 		if (devRunType.equals("")) {
					switchs.add(tempDev);
				} else {
				    if (tempDev.getDeviceRunType().equals(devRunType))
					    switchs.add(tempDev);
				}
			}
   	  	    RuleExeUtil.swapLowDeviceList(switchs);
		}
		else{
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.Switch);
            inPara.put("excDevType", SystemConstants.PowerTransformer);
            cs.execute(inPara, outPara);
            tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
   	  	    for (int i = 0; i < tempswitchs.size(); i++) {
	   	 		tempDev=(PowerDevice)tempswitchs.get(i);
	   	 		if (devRunType.equals("")) {
					switchs.add(tempDev);
				} else {
				    if (tempDev.getDeviceRunType().equals(devRunType))
					    switchs.add(tempDev);
				}
			}
   	  	    RuleExeUtil.swapDeviceList(switchs);
		}
		
		
		
		
		String showMessage="请选择设备的目标状态";
		String defaultStatus = CBSystemConstants.getCurRBM().getEndState();
        List<String> defaultStatusList = new ArrayList<String>();  //设备默认状态
        List<List<String>> expStatusList = new ArrayList<List<String>>();  //设备默认可选择状态集合
     

        
        List<PowerDevice> expDevs = new ArrayList<PowerDevice>();
        for(PowerDevice sw : switchs) {
        	
	        	List<String> expStatus = new ArrayList<String>();
	        	if(!sw.getDeviceType().equals(SystemConstants.Switch)){
	        		//非开关设备只有冷备用或者检修才弹出提示
					if(defaultStatus.equals("2")||defaultStatus.equals("3")){
						defaultStatusList.add("2");
						expStatus.add("0");
						expStatus.add("1");
						expStatusList.add(expStatus);
					}else{
						expDevs.add(sw);
					}	
					continue;
	        	}
	        	
				PowerDevice linkKnifeXL = null; //当前开关连接的线路刀闸
				inPara.clear();
				outPara.clear();
				
		        PowerDevice tagDevice = null; //当前操作的设备，如果是线路则
				if(!pd.getDeviceType().equals(SystemConstants.InOutLine)){
					tagDevice = pd ;
				}else{
					tagDevice = CBSystemConstants.getPowerDeviceByCIMID(sw.getPowerStationID(), pd.getCimID());
				}
				
				inPara.put("oprSrcDevice", sw);
				inPara.put("tagDevice", tagDevice);
				inPara.put("excDevType", SystemConstants.PowerTransformer);
				inPara.put("isSearchOffPath", true);
				inPara.put("isStopOnBusbarSection", true);
				cs.execute(inPara, outPara);
				HashMap<PowerDevice,ArrayList<PowerDevice>> resultPath = (HashMap<PowerDevice,ArrayList<PowerDevice>>)outPara.get("pathList");
				List<PowerDevice> devs = resultPath.get(tagDevice);
				for (int j = 0; j < devs.size(); j++) {
					if(CBSystemConstants.getSourceDev().getDeviceType().equals(SystemConstants.InOutLine) && devs.get(j).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)) {
						linkKnifeXL = devs.get(j);
						break;
					}
					if(CBSystemConstants.getSourceDev().getDeviceType().equals(SystemConstants.PowerTransformer) && devs.get(j).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZB)) {
						linkKnifeXL = devs.get(j);
						break;
					}
					if(CBSystemConstants.getSourceDev().getDeviceType().equals(SystemConstants.MotherLine) && devs.get(j).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
						linkKnifeXL = devs.get(j);
						break;
					}
				}
	        	
				boolean isThreeTwoModel = sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo);
				//选择状态设置
				if(linkKnifeXL!=null && linkKnifeXL.getDeviceStatus().equals("1") && isThreeTwoModel){
					defaultStatusList.add(defaultStatus.equals("3")?"2":defaultStatus);
					expStatusList.add(expStatus);
				}else{
					//开关非3/2接线方式只有冷备用或者检修才弹出提示
					if(defaultStatus.equals("2")||defaultStatus.equals("3")){
						defaultStatusList.add("2");
						expStatus.add("0");
						expStatus.add("1");
						expStatusList.add(expStatus);
					}else{
						expDevs.add(sw);
					}	
				}
        }
        switchs.removeAll(expDevs);
		if(switchs.size() == 0)
			return true;
		EquipStatusChooseView dialog = new EquipStatusChooseView(SystemConstants.getMainFrame(), true, switchs, defaultStatusList,expStatusList, showMessage);
		Map tagStatusMap=dialog.getTagStatusMap();
		
		for (Iterator iterator = tagStatusMap.keySet().iterator(); iterator.hasNext();) {
			PowerDevice tempPd = (PowerDevice) iterator.next();
			String endStatus=tagStatusMap.get(tempPd).toString();
			RuleExecute ruleExecute=new RuleExecute();
			RuleBaseMode rbmode=new RuleBaseMode();
			rbmode.setPd(tempPd);
			rbmode.setBeginStatus(tempPd.getDeviceStatus());
			rbmode.setEndState(endStatus);
			ruleExecute.execute(rbmode);
		}
		return true;
	}
	
	
	private List<PowerDevice> getDeviceByRuntype(String devRunType,List<PowerDevice> devs){
		List<PowerDevice> chooseDevs = new ArrayList<PowerDevice>();
		for (PowerDevice powerDevice : devs) {
			if(powerDevice.getDeviceRunType().equals(devRunType)){
				chooseDevs.add(powerDevice);
			}
		}
		return chooseDevs;
	}

}
