package com.tellhow.czp.app.yndd.rule.zt;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.app.yndd.view.JAutoCompleteComboBox;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;

public class EquipStatusDialog extends JDialog {
	private DefaultTableModel jTable1Model = new DefaultTableModel();
	private JTable table = new JTable();
	private Map<String,String> tagStatusMap = new HashMap<String, String>();
	private Boolean isCancel = true;
	public EquipStatusDialog(JFrame mainFrame,Map<String,String> map) {
		super(mainFrame, "请选择设备状态", true);
		init(map);
	}
	
	public void init(Map<String,String> map){
		initComponents(map);
		setLocationCenter();
	}
	
	public void initComponents(Map<String,String> map) {
		this.setSize(600, 200);
		getContentPane().setLayout(new BorderLayout(0, 0));
	
        JPanel toolBar = new JPanel();
        toolBar.setLayout(new FlowLayout(FlowLayout.RIGHT, 5, 5));

        JButton truebutton = makeNavigationButton("true","true","确定","确定");
        truebutton.setPreferredSize(new Dimension(90,30));
        toolBar.add(truebutton);
        
        getContentPane().add(toolBar, BorderLayout.NORTH);
        
		JScrollPane rightScrollPane = new JScrollPane();
		
		initTable(map);
		
		table.setRowHeight(30);
		table.setFont(new java.awt.Font("宋体", 0, 13));
		
		rightScrollPane.setViewportView(table);
		getContentPane().add(rightScrollPane);
	}

	public void initTable(Map<String,String> map) {
		Object[][] tableDate = null;

		jTable1Model =  new DefaultTableModel(tableDate, new String[] {"厂站名称","设备名称","初始状态","目标状态"});
		
		String stationName = StringUtils.ObjToString(map.get("STATIONNAME"));
		String deviceName = StringUtils.ObjToString(map.get("DEVICENAME"));
		String beginstatus = StringUtils.ObjToString(map.get("BEGINSTATUS"));
		String endstatus = StringUtils.ObjToString(map.get("ENDSTATUS"));

		CodeNameModel codeNameModel6=new CodeNameModel(beginstatus,beginstatus);
		CodeNameModel codeNameModel5=new CodeNameModel(endstatus,endstatus);

		Object[] rowData = {stationName,deviceName,codeNameModel6,codeNameModel5};
		jTable1Model.addRow(rowData);
		
		table.setModel(jTable1Model);
		
		table.getColumnModel().getColumn(2).setMinWidth(100);   
		table.getColumnModel().getColumn(2).setMaxWidth(120);
		table.getColumnModel().getColumn(2).setPreferredWidth(100);
		
		table.getColumnModel().getColumn(3).setMinWidth(100);   
		table.getColumnModel().getColumn(3).setMaxWidth(120);
		table.getColumnModel().getColumn(3).setPreferredWidth(100);
		
		JAutoCompleteComboBox comboBox = new JAutoCompleteComboBox();
		comboBox.setFont(new Font("宋体",Font.PLAIN,14));
        DefaultComboBoxModel model = new DefaultComboBoxModel();
        
        CodeNameModel codeNameModel1=new CodeNameModel("运行","运行");
		model.addElement(codeNameModel1);
        
		CodeNameModel codeNameModel2=new CodeNameModel("热备用","热备用");
		model.addElement(codeNameModel2);
        
		CodeNameModel codeNameModel3=new CodeNameModel("冷备用","冷备用");
		model.addElement(codeNameModel3);
        
		CodeNameModel codeNameModel4=new CodeNameModel("检修","检修");
		model.addElement(codeNameModel4);
		
		comboBox.setModel(model);
		DefaultCellEditor tranTypeEditor = new DefaultCellEditor(comboBox);
		tranTypeEditor.setClickCountToStart(2);
		
		table.getColumnModel().getColumn(2).setCellEditor(tranTypeEditor);
		table.getColumnModel().getColumn(3).setCellEditor(tranTypeEditor);
	}
	
	private JButton makeNavigationButton(String imageName,String actionCommand,String toolTipText,String altText){
        //搜索图片
        ImageIcon icon = new ImageIcon(getClass().getResource("/tellhow/icons/"+imageName+".png"));
        //初始化工具按钮
        JButton button=new JButton();
        button.setText(altText);
        button.setHorizontalAlignment(SwingConstants.LEFT);
        //设置按钮的命令
        button.setActionCommand(actionCommand);
        //设置提示信息
        button.setToolTipText(toolTipText);
        button.addActionListener(new java.awt.event.ActionListener() {
		    public void actionPerformed(java.awt.event.ActionEvent evt) {
		        jButtonActionPerformed(evt);
		    }
		});
        button.setIcon(icon);
        return button;
    }
	
	public void jButtonActionPerformed(ActionEvent e) {
		if(e.getActionCommand().contentEquals("true")) {
			for (int i = 0; i < jTable1Model.getRowCount(); i++) {
				CodeNameModel cnm2 = (CodeNameModel)jTable1Model.getValueAt(i, 2);
				CodeNameModel cnm3 = (CodeNameModel)jTable1Model.getValueAt(i, 3);

				tagStatusMap.put("BEGINSTATUS",cnm2.getName());
				tagStatusMap.put("ENDSTATUS",cnm3.getName());
			}
			
			isCancel = false;
			this.setVisible(false);
		}
	}
	
	public Map<String,String> getTagStatusMap() {
		return tagStatusMap;
	}
	
	public Boolean isCancel() {
		return isCancel;
	}
	
	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
}
