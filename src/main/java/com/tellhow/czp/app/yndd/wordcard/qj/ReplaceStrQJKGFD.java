package com.tellhow.czp.app.yndd.wordcard.qj;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJKGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖开关复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			replaceStr += CommonFunctionQJ.getPowerOnCheckContent();
			
			String begin = CBSystemConstants.getCurRBM().getBeginStatus();
			String end = CBSystemConstants.getCurRBM().getEndState();
			
			if(begin.equals("2")){
				if(end.equals("1")){
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
						replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(curDev, stationName, station);
					}
				}else if(end.equals("0")){
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
						replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(curDev, stationName, station);
					}
					
					if(curDev.getDeviceStatus().equals("0")){
						replaceStr += CommonFunctionQJ.getCdOrHhContent(curDev, "曲靖地调", stationName);
					}
				}
			}else if(begin.equals("1")){
				if(curDev.getDeviceStatus().equals("0")){
					replaceStr += CommonFunctionQJ.getCdOrHhContent(curDev, "曲靖地调", stationName);
				}
			}
		}
		
		return replaceStr;
	}

}
