package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Component;
import java.awt.FlowLayout;
import java.awt.GridBagLayout;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import javax.swing.AbstractCellEditor;
import javax.swing.JCheckBox;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTable;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableCellEditor;
import javax.swing.table.TableCellRenderer;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.GetDtdDataKM;
import com.tellhow.czp.app.yndd.tool.GetDtdDataQJ;
import com.tellhow.czp.app.yndd.tool.GetDtdDataWS;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.model.CardItemModel;
 
 
public class MyButtonRender extends AbstractCellEditor implements TableCellRenderer,TableCellEditor{
	public JTable jTable2 = new JTable(); 
	private DefaultTableModel jTable1Model = null;
	private DefaultTableModel jTable2Model = null;
	
    public MyButtonRender(JTable jTable2){
    	 this.jTable2 = jTable2;
    	 jTable2Model =  (DefaultTableModel)jTable2.getModel();
    }
    
    @Override
	public Object getCellEditorValue() {
		return null;
	}
    
    @Override
    public boolean stopCellEditing() {
       return true;
    }
    
    @Override
    public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, final int row, int column) {
    	JPanel jPanel = new JPanel();
    	jTable1Model = (DefaultTableModel)table.getModel();
    	JLabel jLabel1 = new JLabel();
		
    	if(value == null){
    		
    	}else if(value.equals("两卷主变")){
    		jPanel.setLayout(new GridBagLayout());
    		
			JPanel northjPanel = new JPanel();
			northjPanel.setBackground(Color.WHITE);
			northjPanel.setLayout(new FlowLayout(FlowLayout.LEADING));
			northjPanel.add(jLabel1);

			JCheckBox checkBox1 = new JCheckBox();
			JCheckBox checkBox3 = new JCheckBox();

			checkBox1.setSelected(true);
			checkBox3.setSelected(true);
			
			checkBox1.setBackground(Color.WHITE);
			checkBox3.setBackground(Color.WHITE);

			northjPanel.add(checkBox1);
			northjPanel.add(checkBox3);

			jPanel.add(northjPanel);
			
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"高");
	                } else {
	                	setCzxlFskInZb(row,"高");
	                }
	            }
	        });
			
			
			checkBox3.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"低");
	                } else {
	                	setCzxlFskInZb(row,"低");
	                }
	            }
	        });
     	}else if(value.equals("三卷主变")){
    		jPanel.setLayout(new GridBagLayout());
			JPanel northjPanel = new JPanel();
			northjPanel.setLayout(new FlowLayout(FlowLayout.LEADING));
			northjPanel.add(jLabel1);

			JCheckBox checkBox1 = new JCheckBox();
			JCheckBox checkBox2 = new JCheckBox();
			JCheckBox checkBox3 = new JCheckBox();

			checkBox1.setBackground(Color.WHITE);
			checkBox2.setBackground(Color.WHITE);
			checkBox3.setBackground(Color.WHITE);
			
			northjPanel.add(checkBox1);
			northjPanel.add(checkBox2);
			northjPanel.add(checkBox3);

			checkBox1.setSelected(true);
			checkBox2.setSelected(true);
			checkBox3.setSelected(true);
			
			northjPanel.setBackground(Color.WHITE);
			jPanel.add(northjPanel);
			
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"高");
	                } else {
	                	setCzxlFskInZb(row,"高");
	                }
	            }
	        });
			
			checkBox2.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"中");
	                } else {
	                	setCzxlFskInZb(row,"中");
	                }
	            }
	        });
			
			checkBox3.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"低");
	                } else {
	                	setCzxlFskInZb(row,"低");
	                }
	            }
	        });
     	}else if(value.equals("开关")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);
			centerjPanel.add(checkBox1);

			checkBox1.setBackground(Color.WHITE);
			
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else if(value.equals("目标模式开关")||value.equals("目标模式刀闸")){
    		jPanel.setLayout(new GridBagLayout());
			JPanel northjPanel = new JPanel();
			northjPanel.setBackground(Color.WHITE);
			northjPanel.add(jLabel1);

			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);

			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			
			northjPanel.add(checkBox1);
			checkBox1.setBackground(Color.WHITE);
			jPanel.add(northjPanel);
     	}else if(value.equals("中性点")||value.equals("核实中性点")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(false);
			centerjPanel.add(checkBox1);
			checkBox1.setBackground(Color.WHITE);
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else if(value.equals("遥控中性点")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);
			centerjPanel.add(checkBox1);
			checkBox1.setBackground(Color.WHITE);
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else if(value.equals("母线")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);
			centerjPanel.add(checkBox1);
     		checkBox1.setBackground(Color.WHITE);

			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else if(value.equals("隔离开关")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);
			centerjPanel.add(checkBox1);
     		checkBox1.setBackground(Color.WHITE);

			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else{
    		
    	}
    	
    	jPanel.setBackground(Color.WHITE);
    	
    	return jPanel;
    }
 
    public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, final int row,int column){
    	JPanel jPanel = new JPanel();
    	jTable1Model = (DefaultTableModel)table.getModel();
    	JLabel jLabel1 = new JLabel();
		
    	if(value == null){
    		
    	}else if(value.equals("两卷主变")){
    		jPanel.setLayout(new GridBagLayout());
    		
			JPanel northjPanel = new JPanel();
			northjPanel.setBackground(Color.WHITE);
			northjPanel.setLayout(new FlowLayout(FlowLayout.LEADING));
			northjPanel.add(jLabel1);

			JCheckBox checkBox1 = new JCheckBox();
			JCheckBox checkBox3 = new JCheckBox();

			checkBox1.setSelected(true);
			checkBox3.setSelected(true);
			
			checkBox1.setBackground(Color.WHITE);
			checkBox3.setBackground(Color.WHITE);

			northjPanel.add(checkBox1);
			northjPanel.add(checkBox3);

			jPanel.add(northjPanel);
			
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"高");
	                } else {
	                	setCzxlFskInZb(row,"高");
	                }
	            }
	        });
			
			
			checkBox3.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"低");
	                } else {
	                	setCzxlFskInZb(row,"低");
	                }
	            }
	        });
     	}else if(value.equals("三卷主变")){
    		jPanel.setLayout(new GridBagLayout());
			JPanel northjPanel = new JPanel();
			northjPanel.setLayout(new FlowLayout(FlowLayout.LEADING));
			northjPanel.add(jLabel1);

			JCheckBox checkBox1 = new JCheckBox();
			JCheckBox checkBox2 = new JCheckBox();
			JCheckBox checkBox3 = new JCheckBox();

			checkBox1.setBackground(Color.WHITE);
			checkBox2.setBackground(Color.WHITE);
			checkBox3.setBackground(Color.WHITE);
			
			northjPanel.add(checkBox1);
			northjPanel.add(checkBox2);
			northjPanel.add(checkBox3);

			checkBox1.setSelected(true);
			checkBox2.setSelected(true);
			checkBox3.setSelected(true);
			
			northjPanel.setBackground(Color.WHITE);
			jPanel.add(northjPanel);
			
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"高");
	                } else {
	                	setCzxlFskInZb(row,"高");
	                }
	            }
	        });
			
			checkBox2.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"中");
	                } else {
	                	setCzxlFskInZb(row,"中");
	                }
	            }
	        });
			
			checkBox3.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if (e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSkInZb(row,"低");
	                } else {
	                	setCzxlFskInZb(row,"低");
	                }
	            }
	        });
     	}else if(value.equals("开关")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);
			centerjPanel.add(checkBox1);

			checkBox1.setBackground(Color.WHITE);
			
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else if(value.equals("目标模式开关")||value.equals("目标模式刀闸")){
    		jPanel.setLayout(new GridBagLayout());
			JPanel northjPanel = new JPanel();
			northjPanel.setBackground(Color.WHITE);
			northjPanel.add(jLabel1);

			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);

			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			
			northjPanel.add(checkBox1);
			checkBox1.setBackground(Color.WHITE);
			jPanel.add(northjPanel);
     	}else if(value.equals("中性点")||value.equals("核实中性点")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(false);
			centerjPanel.add(checkBox1);
			checkBox1.setBackground(Color.WHITE);
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else if(value.equals("遥控中性点")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);
			centerjPanel.add(checkBox1);
			checkBox1.setBackground(Color.WHITE);
			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else if(value.equals("母线")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);
			centerjPanel.add(checkBox1);
     		checkBox1.setBackground(Color.WHITE);

			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else if(value.equals("隔离开关")){
     		jPanel.setLayout(new GridBagLayout());
     		JPanel centerjPanel = new JPanel();
     		centerjPanel.setBackground(Color.WHITE);
			centerjPanel.add(jLabel1);
			JCheckBox checkBox1 = new JCheckBox();
			checkBox1.setSelected(true);
			centerjPanel.add(checkBox1);
     		checkBox1.setBackground(Color.WHITE);

			checkBox1.addItemListener(new ItemListener() {
	            public void itemStateChanged(ItemEvent e) {
	                if(e.getStateChange() == ItemEvent.SELECTED) {
	                	setCzxlSk(row);
	                } else {
	                	setCzxlNotSk(row);
	                }
	            }
	        });
			
			jPanel.add(centerjPanel);
    	}else{
    		
    	}
    	
    	jPanel.setBackground(Color.WHITE);
    	
    	return jPanel;
    }
    

	private void setCzxlSkInZb(int row,String voltgrade) {
		String talbe1idValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 5));
		String talbe1czpsxValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 2));
		String talbe1bdznameValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 7));

		int removenum = 0;
		List<String> removeList = new ArrayList<String>();
		ArrayList<String[]> djzl = new ArrayList<String[]>();

		for (int i = jTable2Model.getRowCount() - 1; i >= 0; i--) {
			String talbe2idValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 8));
			String talbe2cznrValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 2));
			String talbe2czdwValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 1));
			String talbe2devidValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 3));

			if (talbe2idValue.equals(talbe1idValue)) {
				PowerDevice device = CBSystemConstants.getPowerDevice(talbe2devidValue);
				PowerDevice station = CBSystemConstants.getPowerStation(device.getPowerStationID());
				
				double volt = station.getPowerVoltGrade();
				
				if(voltgrade.equals("高")){
					
				}else if(voltgrade.equals("中")){
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							volt =  RuleExeUtil.getTransformerVolByType(dev, "middle");
							break;
						}
					}
				}else{
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							volt =  RuleExeUtil.getTransformerVolByType(dev, "low");
							break;
						}
					}
				}
				
				if(device.getPowerVoltGrade() == volt){
					removenum = i;
					removeList.add(String.valueOf(i));

					CardItemModel cim = new CardItemModel();

					cim.setIssk(true);
					cim.setStationName(talbe2czdwValue);
					cim.setCardDesc(talbe2cznrValue);
					cim.setBdzName(talbe1bdznameValue);

					List<CardItemModel> itemModelsShow = new ArrayList<CardItemModel>();

					itemModelsShow.add(cim);
					
					if(CBSystemConstants.opcardUser.equals("OPCARDKM.")){
						djzl = GetDtdDataKM.getData(itemModelsShow, "昆明地调");
					}else if(CBSystemConstants.opcardUser.equals("OPCARDWS.")){
						djzl = GetDtdDataWS.getData(itemModelsShow, "文山地调");
					}else if(CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
						djzl = GetDtdDataQJ.getData(itemModelsShow, "曲靖地调");
					}
				}
			}
		}

		for (String num : removeList) {
			jTable2Model.removeRow(Integer.valueOf(num));
		}

		Collections.reverse(djzl);

		for (String[] arr : djzl) {
			String issk = "是";

			if (arr[4].contains("确认")) {
				issk = "否";
			}

			String[] arrnew = { "", arr[2], arr[4], arr[6], arr[7], arr[3],
					arr[9], issk, talbe1idValue, talbe1czpsxValue };

			jTable2Model.insertRow(removenum, arrnew);
		}

		for (int j = 0; j < jTable2Model.getRowCount(); j++) {
			jTable2Model.setValueAt(String.valueOf(j + 1), j, 0);
		}
	}
	
	private void setCzxlFskInZb(int row,String voltgrade) {
		String talbe1idValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 5));
		String talbe1cznrValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 4));
		String talbe1czdwValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 3));
		String talbe1czpsxValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 2));

		talbe1cznrValue = talbe1cznrValue.replace("落实", "确认");
		
		int removenum = 0;
		String czdw = "";
		List<String> removeList = new ArrayList<String>();

		String equipid = "";
		String equipname = "";
		String operate = "";

		
		for (int i = jTable2Model.getRowCount() - 1; i >= 0; i--) {
			String talbe2idValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 8));
			String talbe2czdwValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 5));
			String talbe2devidValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 3));

			if (talbe2idValue.equals(talbe1idValue)) {
				PowerDevice device = CBSystemConstants.getPowerDevice(talbe2devidValue);
				PowerDevice station = CBSystemConstants.getPowerStation(device.getPowerStationID());
				
				double volt = station.getPowerVoltGrade();
				
				if(voltgrade.equals("高")){
					
				}else if(voltgrade.equals("中")){
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							volt =  RuleExeUtil.getTransformerVolByType(dev, "middle");
							break;
						}
					}
				}else{
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							volt =  RuleExeUtil.getTransformerVolByType(dev, "low");
							break;
						}
					}
				}
				
				if(device.getPowerVoltGrade() == volt){
					removenum = i;
					czdw = talbe2czdwValue;
					removeList.add(String.valueOf(i));
					equipid = StringUtils.ObjToString(jTable2Model.getValueAt(i, 3));
					operate = StringUtils.ObjToString(jTable2Model.getValueAt(i, 6));
					
					List<PowerDevice> kgList = RuleExeUtil.getDeviceDirectList(device, SystemConstants.Switch);
					
					if(kgList.size() == 0){
						equipname = CZPService.getService().getDevName(device);
					}else{
						for(PowerDevice dev : kgList){
							equipname = CZPService.getService().getDevName(dev);
							break;
						}
					}
				}
			}
		}

		for (String num : removeList) {
			jTable2Model.removeRow(Integer.valueOf(num));
		}

		if (talbe1cznrValue.contains("确认")) {
			operate = "";
			equipid = "";
		}

		String issk = "否";
		String beginstatus = "";
		String operation = "";
		
		if (talbe1cznrValue.contains("转热备用")) {
			operate = "热备用";
			beginstatus = "冷备用";
			operation = "复电";
		} else if (talbe1cznrValue.contains("转冷备用")) {
			operate = "冷备用";
			beginstatus = "热备用";
			operation = "停电";
		}

		String cznr = "";
		
		if(equipname.contains("隔离开关")){
			if(operation.equals("停电")){
				cznr = "拉开"+equipname;
			}else{
				cznr = "合上"+equipname;
			}
		}else{
			cznr = "将"+equipname+"由"+beginstatus+"转"+operate;
		}
		
		String[] arr = { "", talbe1czdwValue, cznr, equipid, "",
				czdw, operate, issk, talbe1idValue, talbe1czpsxValue };

		jTable2Model.insertRow(removenum, arr);

		for (int j = 0; j < jTable2Model.getRowCount(); j++) {
			jTable2Model.setValueAt(String.valueOf(j + 1), j, 0);
		}
	}
	
	public void setCzxlSk(int row) {
		String talbe1idValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 5));
		String talbe1czpsxValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 2));
		String talbe1bdznameValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 7));

		int removenum = 0;
		List<String> removeList = new ArrayList<String>();
		ArrayList<String[]> djzl = new ArrayList<String[]>();

		for (int i = jTable2Model.getRowCount() - 1; i >= 0; i--) {
			String talbe2idValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 8));
			String talbe2cznrValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 2));
			String talbe2czdwValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 1));

			if (talbe2idValue.equals(talbe1idValue)) {
				removenum = i;
				removeList.add(String.valueOf(i));

				CardItemModel cim = new CardItemModel();

				cim.setIssk(true);
				cim.setStationName(talbe2czdwValue);
				cim.setCardDesc(talbe2cznrValue);
				cim.setBdzName(talbe1bdznameValue);

				List<CardItemModel> itemModelsShow = new ArrayList<CardItemModel>();

				itemModelsShow.add(cim);

				if(CBSystemConstants.opcardUser.equals("OPCARDKM.")){
					djzl = GetDtdDataKM.getData(itemModelsShow, "昆明地调");
				}else if(CBSystemConstants.opcardUser.equals("OPCARDWS.")){
					djzl = GetDtdDataWS.getData(itemModelsShow, "文山地调");
				}else if(CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
					djzl = GetDtdDataQJ.getData(itemModelsShow, "曲靖地调");
				}
			}
		}

		for (String num : removeList) {
			jTable2Model.removeRow(Integer.valueOf(num));
		}

		Collections.reverse(djzl);

		for (String[] arr : djzl) {
			String issk = "是";

			if (arr[4].contains("确认")) {
				issk = "否";
			}

			String[] arrnew = { "", arr[2], arr[4], arr[6], arr[7], arr[3],
					arr[9], issk, talbe1idValue, talbe1czpsxValue };

			jTable2Model.insertRow(removenum, arrnew);
		}

		for (int j = 0; j < jTable2Model.getRowCount(); j++) {
			jTable2Model.setValueAt(String.valueOf(j + 1), j, 0);
		}
	}
    
	public void setCzxlNotSk(int row) {
		String talbe1idValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 5));
		String talbe1cznrValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 4));
		String talbe1czdwValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 3));
		String talbe1czpsxValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 2));

		talbe1cznrValue = talbe1cznrValue.replace("落实", "确认");
		
		int removenum = 0;
		String czdw = "";
		List<String> removeList = new ArrayList<String>();

		String equipid = "";
		String operate = "";

		for (int i = jTable2Model.getRowCount() - 1; i >= 0; i--) {
			String talbe2idValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 8));
			String talbe2czdwValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 5));

			if (talbe2idValue.equals(talbe1idValue)) {
				removenum = i;
				czdw = talbe2czdwValue;
				removeList.add(String.valueOf(i));
				equipid = StringUtils.ObjToString(jTable2Model.getValueAt(i, 3));
				operate = StringUtils.ObjToString(jTable2Model.getValueAt(i, 6));
			}
		}

		for (String num : removeList) {
			jTable2Model.removeRow(Integer.valueOf(num));
		}

		if (talbe1cznrValue.contains("确认")) {
			operate = "";
			equipid = "";
		}

		String issk = "否";

		if (talbe1cznrValue.contains("转热备用")) {
			operate = "热备用";
		} else if (talbe1cznrValue.contains("转冷备用")) {
			operate = "冷备用";
		}

		String[] arr = { "", talbe1czdwValue, talbe1cznrValue, equipid, "",
				czdw, operate, issk, talbe1idValue, talbe1czpsxValue };

		jTable2Model.insertRow(removenum, arr);

		for (int j = 0; j < jTable2Model.getRowCount(); j++) {
			jTable2Model.setValueAt(String.valueOf(j + 1), j, 0);
		}
	}
	
	public void setCzxlTagModel(int row) {
		String talbe1idValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 5));

		for (int i = jTable2Model.getRowCount() - 1; i >= 0; i--) {
			String talbe2idValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 8));
			String talbe2cznrValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 2));

			if (talbe2idValue.equals(talbe1idValue)) {
				if(talbe2cznrValue.contains("确认")){
					jTable2Model.removeRow(Integer.valueOf(i));
				}
			}
		}
	}
	
	public void setCzxlTagModelInZb(int row,String voltgrade) {
		String talbe1idValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 5));

		for (int i = jTable2Model.getRowCount() - 1; i >= 0; i--) {
			String talbe2idValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 8));
			String talbe2cznrValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 2));
			String talbe2devidValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 3));

			if (talbe2idValue.equals(talbe1idValue)) {
				PowerDevice device = CBSystemConstants.getPowerDevice(talbe2devidValue);
				PowerDevice station = CBSystemConstants.getPowerStation(device.getPowerStationID());
				
				double volt = station.getPowerVoltGrade();
				
				if(voltgrade.equals("高")){
					
				}else if(voltgrade.equals("中")){
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							volt =  RuleExeUtil.getTransformerVolByType(dev, "middle");
							break;
						}
					}
				}else{
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							volt =  RuleExeUtil.getTransformerVolByType(dev, "low");
							break;
						}
					}
				}
				
				if(device.getPowerVoltGrade() == volt){
					if(talbe2cznrValue.contains("确认")){
						jTable2Model.removeRow(Integer.valueOf(i));
					}
				}
			}
		}
	}
	
	public void setCzxlFTagModel(int row) {
		String talbe1idValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 5));
		String talbe1czpsxValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 2));
		String talbe1czValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 6));
		
		for (int i = jTable2Model.getRowCount() - 1; i >= 0; i--) {
			String talbe2idValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 8));
			String talbe2devidValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 3));
			String talbe2devnameValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 4));
			String talbe2ssczValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 5));

			if (talbe2idValue.equals(talbe1idValue)) {
				PowerDevice device = CBSystemConstants.getPowerDevice(talbe2devidValue);
				PowerDevice station = CBSystemConstants.getPowerStation(device.getPowerStationID());
				
				String hscznr = "";
				
				if(talbe1czValue.equals("拉开")){
					if(device.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						hscznr = "确认"+CZPService.getService().getDevName(device)+"在实验位置";
					}else{
						hscznr = "确认"+CZPService.getService().getDevName(device)+"处分闸位置";
					}
				}else{
					if(device.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						hscznr = "确认"+CZPService.getService().getDevName(device)+"在工作位置";
					}else{
						hscznr = "确认"+CZPService.getService().getDevName(device)+"处合闸位置";
					}
				}
				
				String stationName = CZPService.getService().getDevName(station);
				
				String[] arrnew = { "",stationName,hscznr, talbe2devidValue, talbe2devnameValue, talbe2ssczValue,"", "否", talbe1idValue, talbe1czpsxValue };
				jTable2Model.insertRow(i+1, arrnew);
			}
		}
	}
	
	public void setCzxlFTagModelInZb(int row,String voltgrade) {
		String talbe1idValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 5));
		String talbe1czpsxValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 2));
		String talbe1czValue = StringUtils.ObjToString(jTable1Model.getValueAt(row, 6));

		for (int i = jTable2Model.getRowCount() - 1; i >= 0; i--) {
			String talbe2idValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 8));
			String talbe2devidValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 3));
			String talbe2devnameValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 4));
			String talbe2ssczValue = StringUtils.ObjToString(jTable2Model.getValueAt(i, 5));

			if (talbe2idValue.equals(talbe1idValue)) {
				PowerDevice device = CBSystemConstants.getPowerDevice(talbe2devidValue);
				PowerDevice station = CBSystemConstants.getPowerStation(device.getPowerStationID());
				double volt = station.getPowerVoltGrade();
				
				if(voltgrade.equals("高")){
					
				}else if(voltgrade.equals("中")){
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							volt =  RuleExeUtil.getTransformerVolByType(dev, "middle");
							break;
						}
					}
				}else{
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							volt =  RuleExeUtil.getTransformerVolByType(dev, "low");
							break;
						}
					}
				}
				
				if(device.getPowerVoltGrade() == volt){
					String hscznr = "";
					
					if(talbe1czValue.equals("拉开")){
						if(device.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							hscznr = "确认"+CZPService.getService().getDevName(device)+"在实验位置";
						}else{
							hscznr = "确认"+CZPService.getService().getDevName(device)+"处分闸位置";
						}
					}else{
						if(device.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							hscznr = "确认"+CZPService.getService().getDevName(device)+"在工作位置";
						}else{
							hscznr = "确认"+CZPService.getService().getDevName(device)+"处合闸位置";
						}
					}
					
					String stationName = CZPService.getService().getDevName(station);
					
					String[] arrnew = { "",stationName,hscznr, talbe2devidValue, talbe2devnameValue, talbe2ssczValue,"", "否", talbe1idValue, talbe1czpsxValue };
					jTable2Model.insertRow(i+1, arrnew);
				}
			}
		}
		
		for (int j = 0; j < jTable2Model.getRowCount(); j++) {
			jTable2Model.setValueAt(String.valueOf(j + 1), j, 0);
		}
	}
}
