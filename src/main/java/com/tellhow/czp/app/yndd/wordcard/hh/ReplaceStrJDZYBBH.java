package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrJDZYBBH implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("接地站用变保护".equals(tempStr)){
			List<PowerDevice> jdbList = new ArrayList<PowerDevice>();
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				
				if(dev.getPowerVoltGrade() == 10&&dev.getDeviceType().equals(SystemConstants.Switch)&&(dev.getPowerDeviceName().contains("接地站用变")||dev.getPowerDeviceName().contains("接地变"))){
					jdbList.add(dev);
				}
				
				if(dev.getPowerVoltGrade() == 10&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					mlkgList.add(dev);
				}
			}
			
			String num = "";
			
			RuleExeUtil.swapDeviceList(jdbList);
			
			for(PowerDevice jdb : jdbList){
				String jdbName = "";
				
				if(jdb.getPowerDeviceName().indexOf("接地站用变")>0){
					jdbName = jdb.getPowerDeviceName().substring(0, jdb.getPowerDeviceName().indexOf("接地站用变"));
				}else if(jdb.getPowerDeviceName().indexOf("接地变")>0){
					jdbName = jdb.getPowerDeviceName().substring(0, jdb.getPowerDeviceName().indexOf("接地变"));
				}
				
				
				num += CZPService.getService().getDevNum(jdbName)+"、";
			}
			
			if(num.endsWith("、")){
				num =num.substring(0, num.length()-1);
			}
			
			replaceStr = "10kV"+num+"接地站用变保护动作跳"+CZPService.getService().getDevName(mlkgList);
			
			if(num.equals("")||mlkgList.size()==0){
				replaceStr = null;
			}
			
		}
		return replaceStr;
	}

}
