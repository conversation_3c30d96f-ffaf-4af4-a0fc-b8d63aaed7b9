package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHFDKGRBYTOLBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河分段开关由热备用转冷备用".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",false, true, true, false);
			List<PowerDevice> kgdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			
			for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
				PowerDevice dev = (PowerDevice)it2.next();
				if(kgdzList.contains(dev))
					it2.remove();
			}
			
			replaceStr += "核实"+CZPService.getService().getDevName(curDev)+"热备用/r/n";
			
			if(station.getPowerVoltGrade() == 220){
				if(curDev.getPowerVoltGrade() == 220){
					
				}else if(curDev.getPowerVoltGrade() == 110){
					
				}else{
					replaceStr += "退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}else{
				replaceStr += "退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
			}
			
			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
			
			for(PowerDevice qtdz : qtdzList){
				String qtdzName = CZPService.getService().getDevName(qtdz);
				
				if(qtdzName.endsWith("隔离开关")){
					qtdzName = qtdzName.replace("隔离开关", "隔离手车");
				}
				
				replaceStr += "核实"+qtdzName+"冷备用/r/n";
				break;
			}
			
		}
		return replaceStr;
	}

}