package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 关联设备目标状态选择器
 * 作    者: 郑柯
 * 开发日期: 2013年8月17日 上午10:13:38 
 */
public class RelatedDeviceStatusChoose implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(CBSystemConstants.jh_tai == 1)
			return true;
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
		
		String devRunType = curRBM.getDeviceruntype();
		
		//一、搜索设备连接的开关
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();  //执行开关集合
		List<PowerDevice> tempswitchs=new ArrayList<PowerDevice>();  //开关集合
		PowerDevice tempDev=null;
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
			 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
			 if(sourceLineTrans==null||loadLineTrans==null){
				 //ShowMessage.view("请先设置线路两端变电站属性！");
				 return true;
			 }
			 
			 List<PowerDevice> sources=new ArrayList<PowerDevice>();
			 inPara.put("oprSrcDevice", sourceLineTrans);
             inPara.put("tagDevType", SystemConstants.Switch+","+SystemConstants.ElecShock);
             inPara.put("excDevType", SystemConstants.PowerTransformer);
             inPara.put("excDevRunType", CBSystemConstants.RunTypeKnifeQT);
             cs.execute(inPara, outPara);
    	 	 inPara.clear();
    	 	 tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
    	  	 for (int i = 0; i < tempswitchs.size(); i++) {
    	 		tempDev=(PowerDevice)tempswitchs.get(i);
    	 		if (devRunType.equals("") || !tempDev.getDeviceType().equals(SystemConstants.Switch)) {
    	 			sources.add(tempDev);
				} else {
					if (tempDev.getDeviceRunType().equals(devRunType))
						sources.add(tempDev);
				}
			 }
    	  	 
    	  	List<PowerDevice> loads=new ArrayList<PowerDevice>();
    	  	for (int i = 0; i < loadLineTrans.size(); i++) {
				inPara.put("oprSrcDevice", loadLineTrans.get(i));
				inPara.put("tagDevType", SystemConstants.Switch+","+SystemConstants.ElecShock);
	            inPara.put("excDevType", SystemConstants.PowerTransformer);
	            inPara.put("excDevRunType", CBSystemConstants.RunTypeKnifeQT);
	            cs.execute(inPara, outPara);
	    		inPara.clear();
	    		tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
	    		for (int j = 0; j < tempswitchs.size(); j++) {
	    			tempDev=(PowerDevice)tempswitchs.get(j);
	    	 		if (devRunType.equals("") || !tempDev.getDeviceType().equals(SystemConstants.Switch)) {
	    	 			loads.add(tempDev);
					} else {
						if (tempDev.getDeviceRunType().equals(devRunType))
							loads.add(tempDev);
					}
				}
	    		List<PowerDevice> zbList = RuleExeUtil.getDeviceList(loadLineTrans.get(i), SystemConstants.PowerTransformer, "", true, true, true);
	    		if(zbList.size()>0){
	    			loads.addAll(RuleExeUtil.getTransformerSwitchLow(zbList.get(0)));
	    		}
			}
    	  	RuleExeUtil.swapDeviceList(sources);
    	  	RuleExeUtil.swapDeviceList(loads);
    	  	
    	  	if(Integer.valueOf(curRBM.getBeginStatus())>Integer.valueOf(curRBM.getEndState())) {
    	  		Collections.reverse(loads);
    	  		switchs.addAll(sources);
    	  		switchs.addAll(loads);
    	  	}
    	  	else {
    	  		switchs.addAll(loads);
    	  		switchs.addAll(sources);
    	  	}
		}
		else if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){ //主变
			tempswitchs = RuleExeUtil.getLinkedSwitch(pd);
   	  	    for (int i = 0; i < tempswitchs.size(); i++) {
	   	 		tempDev=(PowerDevice)tempswitchs.get(i);
	   	 		
	   	 		if(tempDev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
	   	 			if(RuleUtil.isTransformerNQ(pd)){
	   	 				continue;
	   	 			}
	   	 		}
	   	 		
		   	 	if (!tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
			   	 	if (devRunType.equals("")) {
						switchs.add(tempDev);
					} else {
					    if (tempDev.getDeviceRunType().equals(devRunType))
						    switchs.add(tempDev);
					}
		   	 	}
			}

   	  	    RuleExeUtil.swapLowDeviceList(switchs);
   	  	    
   	  	    List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.Switch+","+SystemConstants.PowerTransformer, true, true, true);
   	  	    for(Iterator it = mlList.iterator(); it.hasNext();) {
   	  	    	PowerDevice dev = (PowerDevice)it.next();
   	  	    	if(dev.getPowerVoltGrade() != 66)
   	  	    		it.remove();
   	  	    }
   	  	    if(mlList.size() > 0) {
   	  	    	for(PowerDevice ml : mlList) {
   	  	    		List<PowerDevice> swList = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
   	  	    		switchs.addAll(swList); //加入66kV主开关
   	  	    	}
   	  	    }
		   	  	    
		   	  	    //
   	  	List<PowerDevice> compList = new ArrayList<PowerDevice>(); 
	   	  	List<PowerDevice> sw66List = new ArrayList<PowerDevice>(); 
	   	  	for(PowerDevice sw : switchs) {
		   	  	if(sw.getPowerVoltGrade() == 66) {
		   	  		sw66List.add(sw);
		   	  	}
	   	  	}
	   	  	
	     	for(PowerDevice sw : sw66List) {
	     		List<PowerDevice> ml66List = RuleExeUtil.getDeviceList(sw, SystemConstants.MotherLine, SystemConstants.Switch+","+SystemConstants.PowerTransformer, true, true, true);
	     		
	     		for(Iterator it = ml66List.iterator(); it.hasNext();) {
	     			PowerDevice ml = (PowerDevice)it.next();
	     			if(mlList.contains(ml))
	     				continue;
	   	  	    	List<PowerDevice> swList2 = RuleExeUtil.getDeviceList(ml, SystemConstants.ElecShock+","+SystemConstants.ElecCapacity+","+SystemConstants.Term, SystemConstants.PowerTransformer, true, true, true);
	   	  	    	mlList.add(ml);
	   	  	    	compList.addAll(swList2);
	   	  	    }
	   	  	}
	     	RuleExeUtil.swapDeviceListNum(compList);
	     	switchs.addAll(mlList);
	     	switchs.addAll(compList);
		}
		else{
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.Switch);
            inPara.put("excDevType", SystemConstants.PowerTransformer);
            cs.execute(inPara, outPara);
            tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
   	  	    for (int i = 0; i < tempswitchs.size(); i++) {
	   	 		tempDev=(PowerDevice)tempswitchs.get(i);
	   	 		if (devRunType.equals("")) {
					switchs.add(tempDev);
				} else {
				    if (tempDev.getDeviceRunType().equals(devRunType))
					    switchs.add(tempDev);
				}
			}
   	  	    RuleExeUtil.swapDeviceList(switchs);
		}
		
		String showMessage="请选择设备的目标状态";
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			
			boolean isLinkLineKnife = false;
			List<PowerDevice> lineList=RuleExeUtil.getLineAllSideList(pd);
			for(PowerDevice line : lineList) {
				List<PowerDevice> kfList=RuleExeUtil.getDeviceList(line, null, SystemConstants.SwitchSeparate, null, CBSystemConstants.RunTypeKnifeXL, null, true, true, true, true);
				if(kfList.size() > 0) {
					isLinkLineKnife = true;
					break;
				}
			}
			
			for (int i = switchs.size()-1; i >=0; i--) {
	   	 		tempDev=(PowerDevice)switchs.get(i);
//	   	 		if(Integer.valueOf(curRBM.getBeginStatus())>Integer.valueOf(curRBM.getEndState()) && tempDev.getDeviceType().equals(SystemConstants.Switch) && !tempDev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
//	   	 			switchs.remove(i);
//	   	 		else 
	   	 		if(Integer.valueOf(curRBM.getBeginStatus())>Integer.valueOf(curRBM.getEndState()) && Integer.valueOf(curRBM.getEndState())<=1 && Integer.valueOf(tempDev.getDeviceStatus())<=1
	   	 				&&!tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
	   	 			switchs.remove(i); //3/2接线方式下，如果操作为转运行或热备用，已处于运行或热备用的开关不提示目标状态
	   	 		else if(Integer.valueOf(curRBM.getBeginStatus())<Integer.valueOf(curRBM.getEndState())) {
		   	 		
	   	 		}
	   	 	}
		}
		else {
//			for(Iterator it = switchs.iterator(); it.hasNext();) {
//		  	    	PowerDevice dev = (PowerDevice)it.next();
//		  	    	if(Integer.valueOf(rbm.getBeginStatus())>Integer.valueOf(rbm.getEndState()) && dev.getDeviceType().equals(SystemConstants.Switch))
//		  	    		it.remove();
//		  	    }
		}
		
		String defaultStatus = CBSystemConstants.getCurRBM().getEndState();
		
		List<String> defaultStatusList = new ArrayList<String>();
		if(!curRBM.getEndState().equals("") && Integer.valueOf(curRBM.getBeginStatus()) < Integer.valueOf(curRBM.getEndState())) {
			for(PowerDevice sw : switchs) {
				if(sw.getDeviceType().equals(SystemConstants.Term))
					defaultStatusList.add("2");
				else
					defaultStatusList.add(defaultStatus.equals("3")?"2":defaultStatus);
			}
		}
		else {
			
			for(PowerDevice sw : switchs) {
				if(sw.getDeviceType().equals(SystemConstants.Switch))
					defaultStatusList.add("0");
				else if(sw.getDeviceType().equals(SystemConstants.ElecShock))
					defaultStatusList.add("1");
				else if(sw.getDeviceType().equals(SystemConstants.ElecCapacity))
					defaultStatusList.add("2");
				else
					defaultStatusList.add("0");
			}
		}
		if(switchs.size() == 0)
			return true;
		
		
		PowerDevice lineSource = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());

		List<PowerDevice>  lineSourceList = RuleExeUtil.getLinkedSwitch(lineSource);
		
		for(Iterator<PowerDevice> itor = switchs.iterator();itor.hasNext();){
			PowerDevice  sw = itor.next();
			
			if(lineSourceList.contains(sw)){
				itor.remove();
			}
		}
		
		Set<String>  stidList = new HashSet<String>();
		
		for(PowerDevice switch1 : switchs){
			stidList.add(switch1.getPowerStationID());
		}
		
		List<PowerDevice> newswitchs=new ArrayList<PowerDevice>();  //执行开关集合
		
		if(stidList.size()>0){
			for(String stid : stidList){
				for(PowerDevice switch1 : switchs){
					if(stid.equals(switch1.getPowerStationID())){
						newswitchs.add(switch1);
					}
				}
			}
		}
		
		RuleExeUtil.swapDeviceByHighVoltList(newswitchs);
		
		if(!CBSystemConstants.stateOfTheDrawer && CBSystemConstants.isCurrentSys){
			if(newswitchs.size()>0){
				EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, newswitchs, defaultStatusList, showMessage);
				Map tagStatusMap=dialog.getTagStatusMap();
				if(tagStatusMap.size() == 0)
					return false;
				
			
				for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
					Map.Entry<PowerDevice, String> entry = it.next();
					CBSystemConstants.LineTagStatus.put(entry.getKey(), entry.getValue());
				}
			}
		}
		else {
			for (int i = 0; i < switchs.size(); i++) {
				CBSystemConstants.LineTagStatus.put(switchs.get(i), defaultStatusList.get(i));
			}
		}
		return true;
	}
	

}
