package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXSMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄双母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> deviceList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
					RuleExeUtil.swapLowDeviceList(gdList);
					for(PowerDevice gd : gdList) {
						if(RuleExeUtil.getDeviceEndStatus(gd).equals("0")){
							deviceList.add(gd);
						}
					}
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX, "", true, true, true, true);
				
				for(PowerDevice mxdz : mxdzList){
					if(RuleExeUtil.getDeviceBeginStatus(mxdz).equals("0")){
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(mxdz, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						
						for(PowerDevice mx : mxList){
							zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
						}
					}
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX, "", true, true, true, true);
				
				for(PowerDevice mxdz : mxdzList){
					if(RuleExeUtil.getDeviceBeginStatus(mxdz).equals("0")){
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(mxdz, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						
						for(PowerDevice mx : mxList){
							gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
						}
					}
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += voltStationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置。/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += voltStationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置。/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}
			
			if(deviceList.size() == 0){
				for(PowerDevice dev : zxdjddzList){
					String devName = CZPService.getService().getDevName(dev); 
					replaceStr += voltStationName+"@检查"+devName+"在合上位置。/r/n";
				}
			}else{
				for(PowerDevice dev : deviceList){
					String devName = CZPService.getService().getDevName(dev); 
					replaceStr += "楚雄地调@遥控合上"+stationName+devName+"。/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
				}
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0") && RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "楚雄地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"。/r/n";
					}
				}
				
				replaceStr += voltStationName+"@"+"检查"+stationName+deviceName+"间隔已按远方操作前的要求进行设置。/r/n";
				replaceStr += "楚雄地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作。/r/n";
				
				for(PowerDevice dev : zbdyckgList){
					List<PowerDevice> zbdycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice zbdycdz : zbdycdzList){
						if(RuleExeUtil.getDeviceBeginStatus(zbdycdz).equals("0")){
							replaceStr += voltStationName+"@"+"检查"+stationName+CZPService.getService().getDevName(zbdycdz)+"在拉开位置。/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> zbzycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice zbzycdz : zbzycdzList){
						if(RuleExeUtil.getDeviceBeginStatus(zbzycdz).equals("0")){
							replaceStr += voltStationName+"@"+"检查"+stationName+CZPService.getService().getDevName(zbzycdz)+"在拉开位置。/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					List<PowerDevice> zbgycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					for(PowerDevice zbgycdz : zbgycdzList){
						if(RuleExeUtil.getDeviceBeginStatus(zbgycdz).equals("0")){
							replaceStr += voltStationName+"@"+"检查"+stationName+CZPService.getService().getDevName(zbgycdz)+"在拉开位置。/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				replaceStr += voltStationName+"@退出"+deviceName+"高后备保护联跳"+CZPService.getService().getDevName(dev)+"功能。/r/n";
			}
			
			for(PowerDevice dev : zycmlkgList){
				replaceStr += voltStationName+"@退出"+deviceName+"中后备保护联跳"+CZPService.getService().getDevName(dev)+"功能。/r/n";
			}
			
			for(PowerDevice dev : dycmlkgList){
				replaceStr += voltStationName+"@退出"+deviceName+"低后备保护联跳"+CZPService.getService().getDevName(dev)+"功能。/r/n";
			}
			
			for(PowerDevice dev : zbgyckgList){
				replaceStr += voltStationName+"@检查"+CZPService.getService().getDevName(dev)+"启动失灵保护退出。/r/n";
			}
			
			replaceStr += "楚雄地调@"+stationName+deviceName+"停电设备挂牌。/r/n";
		}
		
		return replaceStr;
	}

}
