package com.tellhow.czp.app.yndd.tool;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 用于增加北京DF8003CIM模型缺少的中性点地刀连接，系统最初搭建时执行即可
 * 作    者: 郑柯
 * 开发日期: 2014年4月3日 上午1:22:03 
 */
public class BuildZxdLink {

	public static void main(String[] args) {
		CBSystemConstants.equipUser= "opcardbs.";
		CBSystemConstants.opcardUser="opcardbs.";
		
//		CBSystemConstants.equipUser= "opcarddl.";
//		CBSystemConstants.opcardUser="opcarddl.";
		
		StringBuffer result = new StringBuffer();
		
//		String sql1 = "select t.station_id from "+CBSystemConstants.equipUser +"t_substation t where t.voltage_id IN ('7318349394608129','7318349394804737')";
//		
//		List<Map> stationidList = DBManager.queryForList(sql1);
//
//		for(Map map : stationidList){
			
			String stid = "5066549676802049";
			
			String sql = "select t.station_id,t.station_name,t.voltage_id from "+CBSystemConstants.equipUser +"t_substation t where t.voltage_id is not null and station_id = '"+stid+"'";

			List<Map> stationList = DBManager.queryForList(sql);
			for(Map station : stationList) {
				String station_id = station.get("station_id").toString();
				String station_name = station.get("station_name").toString();
				String voltage_id = station.get("voltage_id").toString();
				sql = "select t.equip_id,t.equip_name,t.voltage_id from "+CBSystemConstants.equipUser +"t_equipinfo t where t.station_id='"+station_id+"' and t.equiptype_id='6'";
				List<Map> tfList = DBManager.queryForList(sql);
				for(Map tf : tfList) { //主变
					String equip_id = tf.get("equip_id").toString();
					String equip_name = tf.get("equip_name").toString();
					String equip_num = StringUtils.getSwitchCode(equip_name);
					equip_num = equip_num.replace("#", "");
					sql = "select * from "+CBSystemConstants.equipUser +"t_equipinfo a,"+CBSystemConstants.opcardUser+"t_a_deviceequipinfo b where a.equip_id=b.equipid and a.station_id='"+station_id+"' and a.equiptype_id='858' "
							+ "and (a.equip_name like '%中性点ZK50"+equip_num+"0%' or a.equip_name like '%中性点20"+equip_num+"0%' or a.equip_name like '%中性点10"+equip_num+"0%')";
					List<Map> gdList = DBManager.queryForList(sql);
					String gdnames = "";
					for(Map gd : gdList) { //关联中性点地刀
						String gdid = gd.get("equip_id").toString();
						String gdname = gd.get("equip_name").toString();
						
						String devNum=StringUtils.getSwitchCode(gdname);
						
						gdnames = gdnames + gdname;
						
						sql = "select t.connectivitynode_id from "+CBSystemConstants.equipUser +"T_C_TERMINAL t where t.equip_id='"+gdid+"'";
						List<Map> terminalList = DBManager.queryForList(sql);
						
						if(terminalList.size() > 0 && ((Map)terminalList.get(0)).get("connectivitynode_id")!=null) {
							String connectivitynode_id = ((Map)terminalList.get(0)).get("connectivitynode_id").toString();
							
							sql = "select count(*) from "+CBSystemConstants.equipUser +"T_C_TERMINAL t where t.CONNECTIVITYNODE_ID='"+connectivitynode_id+"'";
							int count = DBManager.queryForInt(sql);
							if(count == 1) {
								sql = "insert into "+CBSystemConstants.equipUser +"T_C_TERMINAL(ID,NAME,EQUIP_ID,CONNECTIVITYNODE_ID,CIM_ID) values('"+UUID.randomUUID()+"',null,'"+equip_id+"','"+connectivitynode_id+"',null)";
								DBManager.execute(sql);
								if(!gdnames.equals("")) {
									System.out.println(station_name+"___"+equip_name+"___"+equip_num+"___"+gdnames);
									result.append(station_name+"___"+equip_name+"___"+equip_num+"___"+gdnames+"\r\n");
								}
							}
						}
					}
					
				}
			}
//		}
		
		/*FileWriter writer;
		try {
			writer = new FileWriter("执行结果.txt");
			BufferedWriter bw = new BufferedWriter(writer);

			bw.write(result.toString());

			bw.close();

			writer.close();
		} catch (IOException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}*/
		System.out.println("结束");
	}
	
//	public static void main(String[] args) {
//		StringBuffer result = new StringBuffer();
//		String sql = "";
//		sql = "select t.station_id,t.station_name,t.voltage_id from "+CBSystemConstants.opcardUser+"t_e_substation t where t.voltage_id is not null";
//
//		List<Map> stationList = DBManager.queryForList(sql);
//		for(Map station : stationList) {
//			String station_id = station.get("station_id").toString();
//			String station_name = station.get("station_name").toString();
//			String voltage_id = station.get("voltage_id").toString();
//			sql = "select t.equip_id,t.equip_name,t.voltage_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo t where t.station_id='"+station_id+"' and t.equiptype_id='22'";
//			List<Map> tfList = DBManager.queryForList(sql);
//			for(Map tf : tfList) {
//				String equip_id = tf.get("equip_id").toString();
//				String equip_name = tf.get("equip_name").toString();
//				String equip_num = StringUtils.getSwitchCode(equip_name);
//				equip_num = equip_num.replace("#", "");
//				sql = "select * from "+CBSystemConstants.opcardUser+"t_e_equipinfo a,"+CBSystemConstants.opcardUser+"t_a_deviceequipinfo b where a.equip_id=b.equipid and a.station_id='"+station_id+"' and b.deviceruntype='neutralgroundknife' and a.equip_name like '%7-"+equip_num+"%'";
//				List<Map> gdList = DBManager.queryForList(sql);
//				String gdnames = "";
//				for(Map gd : gdList) {
//					String gdid = gd.get("equip_id").toString();
//					String gdname = gd.get("equip_name").toString();
//					gdnames = gdnames + gdname;
//					
//					sql = "select t.connectivitynode_id from "+CBSystemConstants.opcardUser+"t_e_equipterminal t where t.equip_id='"+gdid+"'";
//					List<Map> terminalList = DBManager.queryForList(sql);
//					if(terminalList.size() > 0) {
//						String connectivitynode_id = ((Map)terminalList.get(0)).get("connectivitynode_id").toString();
//						sql = "insert into "+CBSystemConstants.opcardUser+"t_e_equipterminal(TERMINAL_ID,TERMINAL_NAME,EQUIP_ID,CONNECTIVITYNODE_ID,CIM_ID) values(guid,null,'"+equip_id+"','"+connectivitynode_id+"',null)";
//						DBManager.execute(sql);
//					}
//				}
//				if(!gdnames.equals("")) {
//					//System.out.println(station_name+"___"+equip_name+"___"+equip_num+"___"+gdnames);
//					result.append(station_name+"___"+equip_name+"___"+equip_num+"___"+gdnames+"\r\n");
//				}
//			}
//		}
//		FileWriter writer;
//		try {
//			writer = new FileWriter("执行结果.txt");
//			BufferedWriter bw = new BufferedWriter(writer);
//
//			bw.write(result.toString());
//
//			bw.close();
//
//			writer.close();
//		} catch (IOException e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		}
//		System.out.println("结束");
//	}
}
