package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.lj.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJNQJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江内桥接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> offdeviceList =  new ArrayList<PowerDevice>();
			List<PowerDevice> ondeviceList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(curDev);

			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgycdzList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				gycxlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true);
			}

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
					RuleExeUtil.swapLowDeviceList(gdList);
					for(PowerDevice gd : gdList) {
						if(RuleExeUtil.getDeviceEndStatus(gd).equals("1")){
							offdeviceList.add(gd);
						}
						
						if(RuleExeUtil.getDeviceBeginStatus(gd).equals("1")){
							ondeviceList.add(gd);
						}
					}
				}
			}
			
			replaceStr += stationName+"@核实"+deviceName+"相关工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，设备的二次装置已正常投入，确认"+deviceName+"具备复电条件/r/n";
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					break;
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : gycxlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : ondeviceList){
				String devName = CZPService.getService().getDevName(dev); 
				replaceStr += "丽江地调@遥控合上"+stationName+devName+"/r/n";
			}
			
			for(PowerDevice dev : ondeviceList){
				String devName = CZPService.getService().getDevName(dev); 
				replaceStr += stationName+"@确认"+devName+"处合上位置/r/n";
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				if(TicketKindChoose.flag.equals("全部手动")){
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}else if(TicketKindChoose.flag.equals("部分程序化")){
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
					
					for(PowerDevice dev : zbzyckgList){
						if(CommonFunctionLJ.ifSwitchSeparateControl(dev)){
							replaceStr += "版纳地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							replaceStr += CommonFunctionLJ.getSequenceConfirmFdContent(zbzyckgList,stationName);
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(CommonFunctionLJ.ifSwitchSeparateControl(dev)){
							replaceStr += "版纳地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							replaceStr += CommonFunctionLJ.getSequenceConfirmFdContent(zbgyckgList,stationName);
						}
					}
				}else{
					replaceStr += "丽江地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : gycxlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					break;
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionLJ.getHhContent(dev,"丽江地调",stationName);
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : offdeviceList){
				String devName = CZPService.getService().getDevName(dev); 
				replaceStr += "丽江地调@遥控拉开"+stationName+devName+"/r/n";
			}
			
			for(PowerDevice dev : offdeviceList){
				String devName = CZPService.getService().getDevName(dev); 
				replaceStr += stationName+"@确认"+devName+"处拉开位置/r/n";
			}
		}
		
		return replaceStr;
	}

}
