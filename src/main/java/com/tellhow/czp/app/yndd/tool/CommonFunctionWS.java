package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class CommonFunctionWS extends CommonFunction {
	//地线
	public static List<PowerDevice> groundWireList = new ArrayList<PowerDevice>();

	//合环
	public static List<PowerDevice> closedLoopList = new ArrayList<PowerDevice>();

	//充电
	public static List<PowerDevice> chargeDeviceList = new ArrayList<PowerDevice>();

	//内桥主变停电充电开关
	public static List<PowerDevice> chargeDeviceByNqZbTdList = new ArrayList<PowerDevice>();

	//内桥主变复电充电开关
	public static List<PowerDevice> chargeDeviceByNqZbFdList = new ArrayList<PowerDevice>();

	public static String getSwitchOffContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(station.getDeviceType().equals(SystemConstants.PowerFactory)){
				replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getSwitchOnContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(station.getDeviceType().equals(SystemConstants.PowerFactory)){
				replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getSwitchOnContent(PowerDevice dev,String stationName,PowerDevice station,String word){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(station.getDeviceType().equals(SystemConstants.PowerFactory)){
				replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+word+"/r/n";
			}else{
				replaceStr = "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+word+"/r/n";
			}
		}else{
			replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+word+"/r/n";
		}
		
		return replaceStr;
	}
	public static String getCdOrHhContent(PowerDevice dev,String ddname,String stationName,String descStr){
		String replaceStr = "";

		if(chargeDeviceList.contains(dev)){
			PowerDevice curDev = CBSystemConstants.getCurRBM().getPd();

			if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
				replaceStr = getCdContent(dev,ddname,stationName);
			}else{
				replaceStr = getCdContent(dev,ddname,stationName,CZPService.getService().getDevName(curDev));
			}
		}else if(closedLoopList.contains(dev)){
			replaceStr = getHhContent(dev,ddname,stationName);
		}else if(closedLoopList.isEmpty() && descStr.equals("1")){
			replaceStr=getHhContent(dev,ddname,stationName);
		}else{
			replaceStr = ddname+"@遥控合上"+stationName+CZPService.getService().getDevName(dev)+descStr+"/r/n";
		}

		return replaceStr;
	}
	
	public static String getHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
//			if(dev.getPowerVoltGrade() > 35){
				if(stationName.contains("电站")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}
//			}else{
//				if(stationName.contains("电站")){
//					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"合环/r/n";
//				}else{
//					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环/r/n";
//				}
//			}
		}
		
		return replaceStr;
	}
	public static String getCdContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";

		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(stationName.contains("电站")){
				replaceStr += stationName+"@合上"+stationName+CZPService.getService().getDevName(dev)+"对线路充电/r/n";
			}else{
				replaceStr += ddname+"@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对线路充电/r/n";
			}
		}

		return replaceStr;
	}

	public static String getCdContent(PowerDevice dev,String ddname,String stationName,String deviceName){
		String replaceStr = "";

		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(stationName.contains("电站")){
				replaceStr += stationName+"@合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
			}else{
				replaceStr += ddname+"@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
			}
		}

		return replaceStr;
	}
	
	public static List<PowerDevice> getTransformerKnife(PowerDevice zb,PowerDevice zbkg){
		List<PowerDevice> dztagList = new ArrayList<PowerDevice>();

		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
		List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(zb, zbkg, SystemConstants.PowerTransformer, "", true, true);
		
		for(PowerDevice path : pathList){
			if(path.getDeviceType().equals(SystemConstants.SwitchSeparate)){
				if(!dzList.contains(path)){
					dztagList.add(path);
				}
			}
		}
		
		return dztagList;
	}
	
	public static boolean ifSwitchControl(PowerDevice dev){//开关可控
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_BREAKER WHERE ID = '"+dev.getPowerDeviceID()+"'";
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
				
				if(ifcontrol.equals("0")){
					ifcontrol = "false";
				}else{
					ifcontrol = "true";
				}
				
				return Boolean.parseBoolean(ifcontrol);
			}
		}
		
		return false;
	}
	
	public static boolean ifSwitchSeparateControl(PowerDevice dev){//刀闸是否可控
		boolean result = true;
		
		if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_DISCONNECTOR WHERE ID = '"+dev.getPowerDeviceID()+"'";
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
				
				if(ifcontrol.equals("0")){
					ifcontrol = "false";
				}else{
					ifcontrol = "true";
				}
				
				if(!Boolean.parseBoolean(ifcontrol)){
					result = false;
				}
			}
			
			if(ifcontrolList.size() == 0){
				return false;
			}
			
			return result;
		}else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
			String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_GROUNDDISCONNECTOR WHERE ID = '"+dev.getPowerDeviceID()+"'";
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
				
				if(ifcontrol.equals("0")){
					ifcontrol = "false";
				}else{
					ifcontrol = "true";
				}
				
				if(!Boolean.parseBoolean(ifcontrol)){
					result = false;
				}
			}
			
			if(ifcontrolList.size() == 0){
				return false;
			}
			
			return result;
		}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dz : dzList){
				String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_DISCONNECTOR WHERE ID = '"+dz.getPowerDeviceID()+"'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
					
					if(ifcontrol.equals("0")){
						ifcontrol = "false";
					}else{
						ifcontrol = "true";
					}
					
					if(!Boolean.parseBoolean(ifcontrol)){
						result = false;
					}
				}
				
				if(ifcontrolList.size() == 0){
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						continue;
					}else{
						return false;
					}
				}
			}
			
			if(dzList.size() == 0){
				return false;
			}
			
			return result;
		}
		
		return false;
	}
	
	public static String getKnifeOnContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "文山地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOffCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		/*boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
			}
		}*/
		
		return replaceStr;
	}
	
	public static String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		/*boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
			}
		}*/
		
		return replaceStr;
	}
	
	public static String getZxdJddzOffCheckContent(List<PowerDevice> zxdjddzList,String stationName,PowerDevice station){
		String replaceStr = "";

		boolean isZxdOff = false;
		
		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOff = true;
			}
		}
		
		if(isZxdOff){
			tagzxdjddzList = RuleExeUtil.sortByVoltLow(tagzxdjddzList);
			
			if(station.getPowerVoltGrade() == 220){
				replaceStr += "文山地调@执行拉开"+stationName+CZPService.getService().getDevName(tagzxdjddzList)+"程序操作/r/n";
			}else{
				replaceStr += "文山地调@遥控拉开"+stationName+CZPService.getService().getDevName(tagzxdjddzList)+"/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getZxdJddzOnCheckContent(List<PowerDevice> zxdjddzList,String stationName,PowerDevice station){
		String replaceStr = "";
		
		boolean isZxdOn = false;
		
		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOn = true;
			}
		}
		
		if(isZxdOn){
			tagzxdjddzList = RuleExeUtil.sortByVoltHigh(tagzxdjddzList);

			if(station.getPowerVoltGrade() == 220){
				replaceStr += "文山地调@执行合上"+stationName+CZPService.getService().getDevName(tagzxdjddzList)+"程序操作/r/n";
			}else{
				replaceStr += "文山地调@遥控合上"+stationName+CZPService.getService().getDevName(tagzxdjddzList)+"/r/n";
			}
		}
		
		return replaceStr;
	}

	public static List<Map<String, String>> getStationLineList(PowerDevice curDev){
		List<Map<String, String>> stationLineList = new ArrayList<Map<String,String>>();
		
		String sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND "
				+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND "
						+ "WHERE ID = '"+curDev.getPowerDeviceID()+"')";
		
		stationLineList = DBManager.queryForList(sql);
		
		if(stationLineList.size() == 0){
			sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND "
					+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_NAME = '"+CZPService.getService().getDevName(curDev)+"'";
			
			stationLineList = DBManager.queryForList(sql);
		}
		
		return stationLineList; 
	}
}
