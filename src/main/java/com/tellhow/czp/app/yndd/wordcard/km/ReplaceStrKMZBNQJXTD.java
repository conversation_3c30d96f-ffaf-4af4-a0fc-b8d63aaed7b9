package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.km.TransformTDKindChoose;
import com.tellhow.czp.app.yndd.rule.km.JudgeLoopClosing;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKMZBNQJXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("昆明主变内桥接线停电".equals(tempStr)){
			List<PowerDevice> highvoltswlist = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> midvoltswlist = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> lowvoltswlist = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbgycdzlist = RuleExeUtil.getTransformerKnifeSource(curDev);
			List<PowerDevice> highvoltfbcxlswlist = new ArrayList<PowerDevice>();//高压侧非本侧线路开关
			List<PowerDevice> highvoltmlswlist = new ArrayList<PowerDevice>();
			List<PowerDevice> midvoltmlswlist = new ArrayList<PowerDevice>();
			List<PowerDevice> lowvoltmlswlist = new ArrayList<PowerDevice>();
			List<PowerDevice> firsthsswlist = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> lowvoltdrdkswlist = new ArrayList<PowerDevice>();

			double midvolt = RuleExeUtil.getTransformerVolByType(curDev, "middle");
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							firsthsswlist.add(dev);
						}
					}
					
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&!dev.getPowerDeviceName().contains("分位")&&!dev.getPowerDeviceName().contains("压板")){
						highvoltmlswlist.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&!highvoltswlist.contains(dev)){
						highvoltfbcxlswlist.add(dev);
					}
				}else if(dev.getPowerVoltGrade() == midvolt){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							midvoltmlswlist.add(dev);
						}
					}
				}else if(dev.getPowerVoltGrade() == lowvolt){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							lowvoltmlswlist.add(dev);
						}
					}
					
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
							lowvoltdrdkswlist.add(dev);
						}else if(dev.getPowerDeviceName().contains("站用变")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							lowvoltdrdkswlist.add(dev);
						}
					}
				}
			}
			
			if(lowvoltswlist.size()>0){
				dycmxList = RuleExeUtil.getDeviceList(lowvoltswlist.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			boolean bcxlkgrby = false;
			
			for(PowerDevice dev : highvoltswlist){
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
					bcxlkgrby = true;
				}
			}
			
			if(JudgeLoopClosing.flag.equals("能合环")){
				replaceStr += "云南省调@落实220kVXXX变220kV母线与220kVXXX变220kV母线为同期系统/r/n";
			}
			
			if(curDev.getPowerVoltGrade() > 35){
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				
				if(gdList.size() > 0){
					replaceStr += "落实"+CZPService.getService().getDevName(gdList)+"处合位/r/n";
				}
			}
			
			for(PowerDevice dev : lowvoltdrdkswlist){
				replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
			}
			
			if(!bcxlkgrby){
				if(JudgeLoopClosing.flag.equals("能合环")){
					for(PowerDevice dev : firsthsswlist){
						replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
					
					for(PowerDevice dev : highvoltswlist){
						replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}else if(JudgeLoopClosing.flag.equals("不能合环")&&curDev.getPowerVoltGrade() == 220){
					for(PowerDevice dev : highvoltswlist){
						replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
					
					for(PowerDevice dev : firsthsswlist){
						replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}else if(JudgeLoopClosing.flag.equals("不能合环")&&curDev.getPowerVoltGrade() < 220){
					for(PowerDevice dev : highvoltswlist){
						replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			if(JudgeLoopClosing.flag.equals("不能合环")&&curDev.getPowerVoltGrade() < 220){
				
			}else{
				for(PowerDevice dev : midvoltmlswlist){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : lowvoltmlswlist){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : lowvoltswlist){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : midvoltswlist){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : highvoltmlswlist){
					replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			
			for(PowerDevice dev : lowvoltmlswlist){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : midvoltmlswlist){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")&&dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
					replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}

			if(TransformTDKindChoose.tdflag.equals("母线一起停电")){
				for(PowerDevice dev : highvoltswlist){
					if(RuleExeUtil.isDeviceChanged(dev)){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
			}
			
			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
			
			for(PowerDevice dev : dycmxList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
					replaceStr += "将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
				}
			}
			
			for(PowerDevice dev : highvoltswlist){
				if(dev.getDeviceStatus().equals("0")){
					replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
				
			for(PowerDevice dev : highvoltmlswlist){
				if(dev.getDeviceStatus().equals("0")){
					replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}	
			}
			
			for(PowerDevice dev : highvoltfbcxlswlist){
				if(dev.getDeviceStatus().equals("1")){
					replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(highvoltswlist)+"/r/n";
					replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(highvoltmlswlist)+"/r/n";
					replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : highvoltmlswlist){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
					replaceStr += "将"+CZPService.getService().getDevName(highvoltmlswlist)+"由热备用转冷备用/r/n";
				}	
			}
			
			for(PowerDevice dev : highvoltswlist){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
					replaceStr += "将"+CZPService.getService().getDevName(highvoltswlist)+"由热备用转冷备用/r/n";
				}
			}
		
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}