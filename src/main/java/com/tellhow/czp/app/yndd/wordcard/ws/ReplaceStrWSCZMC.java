package com.tellhow.czp.app.yndd.wordcard.ws;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSCZMC  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山厂站名称".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			replaceStr = stationName;
		}
		
		return replaceStr;
	}

}
