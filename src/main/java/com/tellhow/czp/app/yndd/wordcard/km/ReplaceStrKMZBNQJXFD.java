package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.km.TransformTDKindChoose;
import com.tellhow.czp.app.yndd.rule.km.JudgeLoopClosing;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKMZBNQJXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("昆明主变内桥接线复电".equals(tempStr)){
			List<PowerDevice> highvoltswlist = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> midvoltswlist = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> lowvoltswlist = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbgycdzlist = RuleExeUtil.getTransformerKnifeSource(curDev);
			List<PowerDevice> highvoltfbcxlswlist = new ArrayList<PowerDevice>();//高压侧非本侧线路开关
			List<PowerDevice> highvoltmlswlist = new ArrayList<PowerDevice>();
			List<PowerDevice> midvoltmlswlist = new ArrayList<PowerDevice>();
			List<PowerDevice> lowvoltmlswlist = new ArrayList<PowerDevice>();
			List<PowerDevice> firsthsswlist = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> lowvoltdrdkswlist = new ArrayList<PowerDevice>();

			double midvolt = RuleExeUtil.getTransformerVolByType(curDev, "middle");
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							firsthsswlist.add(dev);
						}
					}
					
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&!dev.getPowerDeviceName().contains("分位")){
						highvoltmlswlist.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&!highvoltswlist.contains(dev)){
						highvoltfbcxlswlist.add(dev);
					}
				}else if(dev.getPowerVoltGrade() == midvolt){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							midvoltmlswlist.add(dev);
						}
					}
				}else if(dev.getPowerVoltGrade() == lowvolt){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							lowvoltmlswlist.add(dev);
						}
					}
					
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
							lowvoltdrdkswlist.add(dev);
						}else if(dev.getPowerDeviceName().contains("站用变")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							lowvoltdrdkswlist.add(dev);
						}
					}
				}
			}
			
			if(lowvoltswlist.size()>0){
				dycmxList = RuleExeUtil.getDeviceList(lowvoltswlist.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			

			if(TransformTDKindChoose.tdflag.equals("母线一起停电")){
				for(PowerDevice dev : highvoltmlswlist){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						replaceStr += "将"+CZPService.getService().getDevName(highvoltmlswlist)+"由冷备用转热备用/r/n";
					}	
				}
				
				for(PowerDevice dev : highvoltswlist){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						replaceStr += "将"+CZPService.getService().getDevName(highvoltswlist)+"由冷备用转热备用/r/n";
					}
				}
			}else{
				for(PowerDevice dev : firsthsswlist){
					replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : dycmxList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
					replaceStr += "将"+CZPService.getService().getDevName(dev)+"及各分路由冷备用转热备用/r/n";
				}
			}
			
			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";

			if(TransformTDKindChoose.tdflag.equals("母线一起停电")){
				for(PowerDevice dev : highvoltmlswlist){
					if(!RuleExeUtil.isDeviceChanged(dev)){
						replaceStr += "投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
			}
			
			for(PowerDevice dev : midvoltmlswlist){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")&&dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
					replaceStr += "投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : lowvoltmlswlist){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			if(curDev.getPowerVoltGrade() > 35){
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				
				if(gdList.size() > 0){
					replaceStr += "落实"+CZPService.getService().getDevName(gdList)+"处合位/r/n";
				}
			}
			
			for(PowerDevice dev : highvoltmlswlist){
				replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
			}
			
			
			for(PowerDevice dev : midvoltswlist){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : lowvoltswlist){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		
			for(PowerDevice dev : lowvoltmlswlist){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : midvoltmlswlist){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : lowvoltdrdkswlist){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(JudgeLoopClosing.flag.equals("能合环")){
				replaceStr += "云南省调@落实220kVXXX变220kV母线与220kVXXX变220kV母线为同期系统/r/n";
				
				
				for(PowerDevice dev : highvoltswlist){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : highvoltmlswlist){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}