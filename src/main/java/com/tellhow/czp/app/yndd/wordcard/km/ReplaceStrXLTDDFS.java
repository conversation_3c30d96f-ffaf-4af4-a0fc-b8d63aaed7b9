package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;
import czprule.wordcard.replaceclass.impl.ReplaceBooDMJX;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrXLTDDFS implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev,String desc) {
		String replaceStr = "";

		if("线路停电倒方式通用替换类".equals(tempStr)){
			List<PowerDevice> list = RuleExeUtil.getLineAllSideList(curDev);
			List<PowerDevice> lineList = new ArrayList<PowerDevice>();
			List<PowerDevice> stationList = new ArrayList<PowerDevice>();
			Map<Double,Integer> empMap = new HashMap<Double,Integer>();
			for(PowerDevice dev : list){
				PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
				if(sw != null){
					PowerDevice station = CBSystemConstants.getPowerStation(sw.getPowerStationID());
					stationList.add(station);
				}
			}
			for(PowerDevice dev : stationList) {
				if(empMap.containsKey(dev.getPowerVoltGrade())) {
					empMap.put(dev.getPowerVoltGrade(), empMap.get(dev.getPowerVoltGrade())+1);
				} else {
					empMap.put(dev.getPowerVoltGrade(), 1);
				}
			}
			double volt = 0;
			for(Double solt : empMap.keySet()) {
				if(empMap.get(solt) > 1) {
					volt = solt;
					break;
				}
			}
			for(PowerDevice dev : list){
				PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
				
				if(sw != null){
					PowerDevice station = CBSystemConstants.getPowerStation(sw.getPowerStationID());
					if(station.getPowerVoltGrade() == volt) {
						lineList.add(dev);
					}
				}
			}
//			PowerDevice onswDevice = null;
//			PowerDevice offswDevice = null;
//			
//			/*
//			 * 合环调电
//			 */
//			
//			if(lineList.size() == 2) {
//				for(PowerDevice dev : lineList) {
//					PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
//					
//					if(RuleExeUtil.getDeviceBeginStatus(sw).equals("0")&&offswDevice==null){
//						offswDevice = sw;
//					}else if(RuleExeUtil.getDeviceBeginStatus(sw).equals("1")&&onswDevice==null){
//						onswDevice = sw;
//					}
//				}
//			}
			
//			if(onswDevice != null && offswDevice != null) {
//				replaceStr  += "云南省调@落实"+onswDevice.getPowerStationName()+(int)volt+"kV系统与"+offswDevice.getPowerStationName()+(int)volt+"kV系统联络运行/r/n";
//				replaceStr  += "昆明地调@遥控合上"+onswDevice.getPowerStationName()+CZPService.getService().getDevName(onswDevice)+"/r/n";
//				replaceStr  += "昆明地调@遥控断开"+offswDevice.getPowerStationName()+CZPService.getService().getDevName(offswDevice)+"/r/n";
//				
//				return replaceStr;
//			}
			
			/*
			 * 负荷侧线路内桥接线合环
			 */
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());

			for(PowerDevice pd : loadLineTrans){
				boolean flag =false;
				
				List<PowerDevice> motherLineList  = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				List<PowerDevice> powerTransformerList  = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, false, true);

				/*
				 * 内桥
				 */
				
				for (Iterator<PowerDevice> it = motherLineList.iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
						it.remove();
					}
				}
				
				if(motherLineList.size()>0){
					if(motherLineList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)&&powerTransformerList.size()>0){
						if(!RuleExeUtil.isTransformerXBDY(powerTransformerList.get(0))){
							flag = true;
						}
					}
				}
			
				if(flag){
					for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
						DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
						PowerDevice dev = dtd.getTransDevice();
						if(dev.getPowerStationID().equals(pd.getPowerStationID())
								&&dev.getPowerVoltGrade()==pd.getPowerVoltGrade()){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
									||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								if(dtd.getBeginstatus().equals("1")&&dtd.getEndstate().equals("0")){
									replaceStr  += "昆明地调@遥控合上"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(dev.getPowerStationID()))
											+CZPService.getService().getDevName(dev)+"/r/n";
								}else if(dtd.getBeginstatus().equals("0")&&dtd.getEndstate().equals("1")){
									replaceStr  += "昆明地调@遥控断开"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(dev.getPowerStationID()))
											+CZPService.getService().getDevName(dev)+"/r/n";
								}
							}
						}
					}
				}else{
					if(!RuleExeUtil.getRunMode(pd).equals("2")){
						ReplaceBooDMJX dmjx = new ReplaceBooDMJX();
						
						if(dmjx.strReplace("单母接线", pd, pd, "")){
							for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
								DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
								PowerDevice dev = dtd.getTransDevice();
								if(dev.getPowerStationID().equals(pd.getPowerStationID())
										&&dev.getPowerVoltGrade()==pd.getPowerVoltGrade()){
									if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
											||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										if(dtd.getBeginstatus().equals("1")&&dtd.getEndstate().equals("0")){
											replaceStr  += "昆明地调@遥控合上"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(dev.getPowerStationID()))
													+CZPService.getService().getDevName(dev)+"/r/n";
										}else if(dtd.getBeginstatus().equals("0")&&dtd.getEndstate().equals("1")){
											replaceStr  += "昆明地调@遥控断开"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(dev.getPowerStationID()))
													+CZPService.getService().getDevName(dev)+"/r/n";
										}
									}
								}
							}
						}
					}
				}
			}
	    }
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}

