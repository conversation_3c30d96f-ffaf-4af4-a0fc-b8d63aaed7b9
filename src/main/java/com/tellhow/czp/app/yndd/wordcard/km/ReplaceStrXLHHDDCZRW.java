package com.tellhow.czp.app.yndd.wordcard.km;


import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrXLHHDDCZRW implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("线路合环调电操作任务".equals(tempStr)){
			
			PowerDevice first = null;
			PowerDevice second = null;
			
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				if(dtd.getTransDevice().getDeviceType().equals(SystemConstants.Switch)){
					if(dtd.getBeginstatus().equals("1")&&dtd.getEndstate().equals("0")){
						first=dtd.getTransDevice();
					}else if(dtd.getBeginstatus().equals("0")&&dtd.getEndstate().equals("1")){
						second=dtd.getTransDevice();
					}
				}
			}
			
			if(first != null && second != null) {
				replaceStr +=  CZPService.getService().getDevName(stationDev) + "由" + CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(second.getPowerStationID()))
				+ CZPService.getService().getDevNum(second)
				+ "断路器供电合环调由" 
				+ CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(first.getPowerStationID()))
				+ CZPService.getService().getDevNum(first) + "断路器供电 ";
				return replaceStr;
			}
	    }
		return null;
	}

}
