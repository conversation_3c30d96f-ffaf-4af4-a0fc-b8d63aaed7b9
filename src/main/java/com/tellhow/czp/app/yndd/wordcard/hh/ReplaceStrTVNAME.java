package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTVNAME  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("电压互感器名称".equals(tempStr)){
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",true, true, true, true);

			PowerDevice tvdz = new PowerDevice();
			
			for(PowerDevice dev : qtdzList){
				if(dev.getPowerDeviceName().contains("母线TV")||dev.getPowerDeviceName().contains("母TV")||dev.getPowerDeviceName().contains("母线PT")||dev.getPowerDeviceName().contains("母PT")){
					tvdz = dev;
				}
			}
			
			replaceStr += CZPService.getService().getDevName(tvdz);
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}