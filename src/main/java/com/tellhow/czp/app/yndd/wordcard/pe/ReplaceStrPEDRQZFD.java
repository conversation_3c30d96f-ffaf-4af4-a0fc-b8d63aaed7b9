package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEDRQZFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱电容器组停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			replaceStr += stationName+"@核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
			
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR, "", false, true, true, true);
			
			String knifeContent = CommonFunctionPE.get3KnifeContent(kgList, stationName, "合上");
			
			if(!knifeContent.equals("")){
				replaceStr += knifeContent;

				for(PowerDevice dev : kgList){
					if(dev.getDeviceStatus().equals("1")){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : kgList){
				if(dev.getDeviceStatus().equals("0")){
					replaceStr += CommonFunctionPE.getSwitchOnContent(dev, stationName, station);
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
