package com.tellhow.czp.app.yndd.wordcard.lc;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.lc.LCChangePowerExecute;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLCZNHHDDRW implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("临沧站内合环调电任务".equals(tempStr)) {
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			PowerDevice powerOnLine = new PowerDevice();
			PowerDevice powerOffLine = new PowerDevice();

			PowerDevice powerOnZb = new PowerDevice();
			PowerDevice powerOffZb = new PowerDevice();
			
			for(PowerDevice dev : LCChangePowerExecute.powerOffZbList){
				powerOffZb = dev;
			}
			
			for(PowerDevice dev : LCChangePowerExecute.powerOnZbList){
				powerOnZb = dev;
			}
			
			for(PowerDevice dev : LCChangePowerExecute.powerOffLineList){
				powerOffLine = dev;
			}
			
			for(PowerDevice dev : LCChangePowerExecute.powerOnLineList){
				powerOnLine = dev;
			}
			
			if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
				replaceStr += stationName+deviceName+"由"+CZPService.getService().getDevName(powerOffLine)+"供电转由"+CZPService.getService().getDevName(powerOnLine)+"供电";
			}else{
				replaceStr += stationName+deviceName+"由"+CZPService.getService().getDevName(powerOffZb)+"供电转由"+CZPService.getService().getDevName(powerOnZb)+"供电";
			}
		}
		return replaceStr;
	}


}