package com.tellhow.czp.app.yndd.wordcard.pe;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEZBHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱主变合环倒电".equals(tempStr)){
			for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					PowerDevice devstation = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(devstation); 
					String devName = CZPService.getService().getDevName(dev); 
					replaceStr += "普洱地调@遥控用"+stationName+devName+"同期合环/r/n";
				}
			}
			
			for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					PowerDevice devstation = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(devstation); 
					String devName = CZPService.getService().getDevName(dev); 
					replaceStr += "普洱地调@遥控断开"+stationName+devName+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
