package com.tellhow.czp.app.yndd.view;

import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JPanel;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DeviceSVGPanelUtil;
import czprule.wordcard.dao.DeviceStateMentManager;
import czprule.wordcard.view.InitDeviceTypeChockBox;

import javax.swing.JButton;

import java.awt.event.ActionListener;
import java.awt.Font;



public class ShowLoopViewDialog extends javax.swing.JDialog{
	private javax.swing.JComboBox jComboBox1 = new JComboBox();
	private javax.swing.JComboBox jComboBox2 = new JComboBox();
	private javax.swing.JComboBox jComboBox3 = new JComboBox();
	private javax.swing.JComboBox jComboBox4 = new JComboBox();
	
	private javax.swing.JComboBox jComboBoxmbzt1 = new JComboBox();
	private javax.swing.JComboBox jComboBoxmbzt2 = new JComboBox();
	
	private javax.swing.JLabel jLabelcszt1 = new JLabel();
	private javax.swing.JLabel jLabelcszt2 = new JLabel();
	
	private boolean isCancel = false;
	private Set<PowerDevice> deviceset = new HashSet<PowerDevice>();

	public ShowLoopViewDialog(java.awt.Frame parent, boolean modal,List<List<PowerDevice>> loopPathList) {
		super(parent, modal);
		this.setTitle("环路展示");
		initComponents(loopPathList);
		setLocationCenter();
		this.setVisible(true);
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	
	
	public boolean isCancel() {
		return isCancel;
	}

	private void initComponents(List<List<PowerDevice>> loopPathList) {
		getContentPane().setLayout(null);
		this.setSize(1100, 400);
		JPanel topPanel =new JPanel();
		topPanel.setBounds(0,0,1100, 28);
		getContentPane().add(topPanel);
		JPanel centerPanel =new JPanel();
		centerPanel.setBounds(0,28,1100, 100);
		getContentPane().add(centerPanel);
		JPanel southPanel =new JPanel();
		southPanel.setBounds(0,129,1100, 224);
		getContentPane().add(southPanel);
		southPanel.setLayout(null);
		topPanel.setLayout(new FlowLayout(FlowLayout.LEFT,15,5));
		centerPanel.setLayout(new FlowLayout(FlowLayout.LEFT,15,5));

		JLabel jLabel1= new JLabel("当前环路：");
		jLabel1.setBounds(40, 100, 80, 26);
		
		topPanel.add(jLabel1);
		
		JLabel linkDevice = new JLabel();  
		linkDevice.setBounds(40, 40, 80, 100);
		StringBuffer sbf = new StringBuffer();

		sbf.append("<html>");
		
		Set<PowerDevice> stationSet = new HashSet<PowerDevice>();
 		
		for(int i=0;i<loopPathList.size();i++){
			List<PowerDevice> pathList = loopPathList.get(i);
			
			for(int j=0;j<pathList.size();j++){
				PowerDevice station = pathList.get(j);
				
				if(CBSystemConstants.getSourceDev().getPowerVoltGrade() == 35&&(station.getPowerVoltGrade() > 220)){
					continue;
				}
				
				if(CBSystemConstants.getSourceDev().getPowerStationID().equals(station.getPowerDeviceID())){
					continue;
				}
				
				stationSet.add(station);
			}
			
			/*
			 * 打开关联的厂站图
			 */
			for(PowerDevice station : stationSet){
				CreatePowerStationToplogy.loadFacData(station.getPowerDeviceID());

				if(CBSystemConstants.isCurrentSys) {
					DeviceSVGPanelUtil.openSVGPanel(station.getPowerDeviceID(), station.getPowerDeviceID());
				}
			}

			/*
			 * 获取可能会倒负荷的设备
			 */
			for(PowerDevice station : stationSet){
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());

				if(mapStationDevice != null ){
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							if(CBSystemConstants.getSourceDev().getPowerVoltGrade() == 35&&(dev.getPowerVoltGrade() == 35||dev.getPowerVoltGrade() == 110)){
								deviceset.add(dev);
							}
						}
					}
				}
			}
			
			sbf.append(loopPathList.get(i));
			sbf.append("<br>");
		}
		
		sbf.append("</html>");
		linkDevice.setText(String.valueOf(sbf));
		
		JLabel jLabel2= new JLabel("\u8BF7\u9009\u62E9\u9700\u8981\u64CD\u4F5C\u7684\u5F00\u5173\uFF1A");
		jLabel2.setBounds(14, 10, 173, 32);
		
		//设备类型选择
        jComboBox1.setModel(getStationCheckBox(stationSet));
        jComboBox1.setBounds(92, 59, 126, 25);
        
        jComboBox1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jComboBox1ActionPerformed(evt);
            }
        });

        jComboBox2.setBounds(312, 59, 223, 25);
        
        jComboBox2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jComboBox2ActionPerformed(evt);
            }
        });
        
		centerPanel.add(linkDevice);
		southPanel.add(jLabel2);
		southPanel.add(jComboBox1);
		southPanel.add(jComboBox2);
		jLabelcszt1.setFont(new Font("SimSun-ExtB", Font.PLAIN, 15));
		southPanel.add(jLabelcszt1);
		southPanel.add(jLabelcszt2);

		JLabel label = new JLabel("厂站名称：");
		label.setBounds(14, 62, 79, 18);
		southPanel.add(label);
		
		JLabel label_1 = new JLabel("设备名称：");
		label_1.setBounds(232, 62, 79, 18);
		southPanel.add(label_1);
		
		jLabelcszt1.setBounds(641, 59, 92, 25);
		jLabelcszt2.setBounds(641, 109, 92, 25);
		
		JLabel label_3 = new JLabel("目标状态：");
		label_3.setBounds(762, 62, 79, 18);
		southPanel.add(label_3);
		
		JLabel label_4 = new JLabel("厂站名称：");
		label_4.setBounds(14, 112, 79, 18);
		southPanel.add(label_4);
		
		JLabel label_5 = new JLabel("设备名称：");
		label_5.setBounds(232, 112, 79, 18);
		southPanel.add(label_5);
		
		JLabel label_7 = new JLabel("目标状态：");
		label_7.setBounds(762, 112, 79, 18);
		southPanel.add(label_7);
		
		jComboBox3 = new JComboBox();
		jComboBox3.setBounds(92, 109, 126, 25);
		southPanel.add(jComboBox3);
		
		jComboBox3.setModel(getStationCheckBox(stationSet));
		jComboBox3.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
            	jComboBox3ActionPerformed(evt);
            }
        });
		
		jComboBox4 = new JComboBox();
		jComboBox4.setBounds(312, 109, 223, 25);
		southPanel.add(jComboBox4);
		
		jComboBox4.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
            	jComboBox4ActionPerformed(evt);
            }
        });
		
		jComboBoxmbzt1 = new JComboBox();
		jComboBoxmbzt1.setBounds(847, 59, 120, 28);
		southPanel.add(jComboBoxmbzt1);
		jComboBoxmbzt1.setModel(getDeviceStatusCheckBox());

		jComboBoxmbzt2 = new JComboBox();
		jComboBoxmbzt2.setBounds(847, 109, 120, 25);
		southPanel.add(jComboBoxmbzt2);
		jComboBoxmbzt2.setModel(getDeviceStatusCheckBox());
		
		JButton okButton = new JButton("确定");
		okButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});
		okButton.setBounds(740, 184, 113, 27);
		southPanel.add(okButton);
		
		JButton qxbutton = new JButton("取消");
		qxbutton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});
		qxbutton.setBounds(879, 184, 113, 27);
		southPanel.add(qxbutton);
		
		JLabel label_2 = new JLabel("初始状态：");
		label_2.setBounds(561, 62, 79, 18);
		southPanel.add(label_2);
		
		
		JLabel label_6 = new JLabel("初始状态：");
		label_6.setBounds(561, 112, 79, 18);
		southPanel.add(label_6);

	}
	
	private void jButton1ActionPerformed(ActionEvent evt) {
		this.setVisible(false);
		
		CodeNameModel cnm1 = (CodeNameModel)jComboBox2.getSelectedItem();
		CodeNameModel cnm3 = (CodeNameModel)jComboBoxmbzt1.getSelectedItem();
		
		PowerDevice dev1 = CBSystemConstants.getPowerDevice(cnm1.getCode());
		RuleExeUtil.deviceStatusExecute(dev1, dev1.getDeviceStatus(), cnm3.getCode());
		
		CodeNameModel cnm2 = (CodeNameModel)jComboBox4.getSelectedItem();
		CodeNameModel cnm4 = (CodeNameModel)jComboBoxmbzt2.getSelectedItem();
		
		PowerDevice dev2 = CBSystemConstants.getPowerDevice(cnm2.getCode());
		RuleExeUtil.deviceStatusExecute(dev2, dev2.getDeviceStatus(), cnm4.getCode());
	}
	
	private void jButton2ActionPerformed(ActionEvent evt) {
		this.setVisible(false);
		isCancel = true;
	}
	
	private void jComboBox1ActionPerformed(ActionEvent evt) {
		CodeNameModel cnm = (CodeNameModel) this.jComboBox1.getSelectedItem();
		this.jComboBox2.setModel(getDeviceCheckBox(cnm.getCode()));
	}
	
	private void jComboBox2ActionPerformed(ActionEvent evt) {
		CodeNameModel cnm = (CodeNameModel) this.jComboBox2.getSelectedItem();
		PowerDevice dev = CBSystemConstants.getPowerDevice(cnm.getCode());
		jLabelcszt1.setText(RuleExeUtil.getStatus(dev.getDeviceStatus()));
	}
	
	private void jComboBox3ActionPerformed(ActionEvent evt) {
		CodeNameModel cnm = (CodeNameModel) this.jComboBox3.getSelectedItem();
		this.jComboBox4.setModel(getDeviceCheckBox(cnm.getCode()));
	}
	
	private void jComboBox4ActionPerformed(ActionEvent evt) {
		CodeNameModel cnm = (CodeNameModel) this.jComboBox4.getSelectedItem();
		PowerDevice dev = CBSystemConstants.getPowerDevice(cnm.getCode());
		jLabelcszt2.setText(RuleExeUtil.getStatus(dev.getDeviceStatus()));
	}
	
	/**
	 * 厂站初始化下拉框
	 * @return
	 */
	private DefaultComboBoxModel getStationCheckBox(Set<PowerDevice> stationSet){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		for(PowerDevice station : stationSet){
			cnm=new CodeNameModel(station.getPowerDeviceID(),station.getPowerDeviceName());
			model.addElement(cnm);
		}
		return model;
	}
	
	/**
	 * 设备初始化下拉框
	 * @return
	 */
	private DefaultComboBoxModel getDeviceCheckBox(String stationid){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		
		for(Iterator<PowerDevice> itor= deviceset.iterator();itor.hasNext();){
			PowerDevice device = itor.next();
			
			if(device.getPowerStationID().equals(stationid)){
				cnm=new CodeNameModel(device.getPowerDeviceID(),CZPService.getService().getDevName(device));
				model.addElement(cnm);
			}
		}
		
		return model;
	}
	
	/**
	 * 设备状态下拉框
	 * @return
	 */
	private DefaultComboBoxModel getDeviceStatusCheckBox(){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		
		CodeNameModel cnm=null;
		
		cnm=new CodeNameModel("0","运行");
		model.addElement(cnm);
		
		cnm=new CodeNameModel("1","热备用");
		model.addElement(cnm);
		
		cnm=new CodeNameModel("2","冷备用");
		model.addElement(cnm);
		
		return model;
	}
}