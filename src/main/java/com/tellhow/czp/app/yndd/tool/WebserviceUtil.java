package com.tellhow.czp.app.yndd.tool;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.tellhow.czp.app.CZPImpl;


public class WebserviceUtil {
	public static List<Map<String, String>> getCheckResult(String param) {
		List<Map<String, String>> resultList = new ArrayList<Map<String,String>>();
		
		try {
			String endpoint = CZPImpl.getPropertyValue("WebserviceUrl");

			Service service = new Service();
			Call call = (Call) service.createCall();
			call.setTargetEndpointAddress(new java.net.URL(endpoint));
			call.setTimeout(20000);
			
			call.addParameter("key",
					org.apache.axis.encoding.XMLType.XSD_STRING,
					javax.xml.rpc.ParameterMode.IN);
			call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);

		    String operation = "getCheckOne";
			call.setOperationName(operation);
			String ret = (String) call.invoke(new Object[] { param });
			System.out.println(ret.replace("</ITEM><ITEM>", "</ITEM>\n<ITEM>").replace("</czrw><ITEM>", "</czrw>\n<ITEM>"));
			
			resultList = getReqInfo(ret);
		} catch (Exception e) {
			System.out.println(e.getMessage());
			e.printStackTrace();
		}
		return resultList;
	}
	
	private static List<Map<String, String>> getReqInfo(String arg) {
		List<Map<String, String>> voLists = new ArrayList<Map<String, String>>();

		try {
			InputStream is = new ByteArrayInputStream(arg.getBytes("UTF-8"));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("ITEM");
			//System.out.println(itemLists);
			for (int i = 0; i <itemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				voLists.add(mapInfo);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return voLists;
	}
}
