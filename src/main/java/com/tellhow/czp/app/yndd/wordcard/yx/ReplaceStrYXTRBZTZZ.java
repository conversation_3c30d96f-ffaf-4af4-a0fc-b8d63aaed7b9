package com.tellhow.czp.app.yndd.wordcard.yx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYXTRBZTZZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("玉溪投入备自投装置".equals(tempStr)){
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(dev.getDeviceStatus().equals("1")){
							mlkgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
						if(dev.getDeviceStatus().equals("0")){
							zbkgList.add(dev);
						}
					}
				}
			}
			
			if(zbkgList.size() == 2&&mlkgList.size()==1){
				replaceStr = "按当前运行方式投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置";
			}
			
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
