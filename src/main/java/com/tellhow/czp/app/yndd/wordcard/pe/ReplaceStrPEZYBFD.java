package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEZYBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱站用变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			replaceStr += stationName+"@"+"核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
			
			String sql = "SELECT ZYB_DEVID,ZYB_NAME,ZYB_DZNAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+stationDev.getPowerDeviceID()+"'";
			
			List<Map<String,String>> zybidList = DBManager.queryForList(sql);
			
			for(Map<String,String> zybidMap : zybidList){
				String zybid = StringUtils.ObjToString(zybidMap.get("ZYB_DEVID"));
				String zybName = StringUtils.ObjToString(zybidMap.get("ZYB_NAME"));
				String zybdzName = StringUtils.ObjToString(zybidMap.get("ZYB_DZNAME"));
				replaceStr += stationName+"@将"+zybName+"由冷备用转运行/r/n";
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
