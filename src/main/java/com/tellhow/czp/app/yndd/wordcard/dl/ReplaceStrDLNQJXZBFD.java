package com.tellhow.czp.app.yndd.wordcard.dl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dl.TransformExecute;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDL;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDLNQJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("大理内桥接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(curDev);
			
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycotherxlkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();

			boolean isSwitchControl = true;
			
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionDL.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionDL.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			}

			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(!zbgyckgList.contains(dev)){
							gycotherxlkgList.add(dev);
						}
					}
				}
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
					
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				otherzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(otherzxdjddzList);
				break;
			}
			
			for (Iterator<PowerDevice> it = zxdjddzList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == 35){
					it.remove();
				}
			}
			
			for (Iterator<PowerDevice> it = otherzxdjddzList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == 35){
					it.remove();
				}
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionDL.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			if(zbList.size() == 1){//单主变
				for(PowerDevice dev : dycmlkgList){
					replaceStr += "大理配调@核实"+stationName+CZPService.getService().getDevName(dev)+"已处运行/r/n";
				}
				
				for(PowerDevice dev : dycmxList){
					if(dev.getPowerVoltGrade() == 10){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"电压互感器已处运行/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+RuleExeUtil.getStatus(dev.getDeviceStatus())+"/r/n";
				}
				
				for(PowerDevice dev : gycotherxlkgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+RuleExeUtil.getStatus(dev.getDeviceStatus())+"/r/n";
				}
				
				for(PowerDevice dev : gycmlkgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+RuleExeUtil.getStatus(dev.getDeviceStatus())+"/r/n";
				}
				
				String gycmlkgstatus = "";
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						gycmlkgstatus = "用"+CZPService.getService().getDevNum(dev)+"断路器充电";
					}else{
						gycmlkgstatus = CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatus(dev.getDeviceStatus());
					}
				}
				
				String zbgyckgstatus = "";
				
				for(PowerDevice dev : zbgyckgList){
					if(!RuleExeUtil.isDeviceChanged(dev)||dev.getDeviceStatus().equals(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev))){
						zbgyckgstatus = "（备注："+CZPService.getService().getDevNum(dev)+"断路器不操作）";
					}else{
						zbgyckgstatus = "，"+CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatus(dev.getDeviceStatus());
					}
				}
				
				replaceStr += stationName+"@将"+deviceName+"由冷备用转运行，"+gycmlkgstatus+zbgyckgstatus+"/r/n";
				
				replaceStr += "投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
			}else{
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
							replaceStr += "大理地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
							replaceStr += "大理地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					for(PowerDevice dev : zbgycdzList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
								replaceStr += "大理地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
								replaceStr += CommonFunctionDL.getKnifeOnCheckContent(zbgycdzList, stationName);
							}else{
								replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						List<PowerDevice> zycdzList = CommonFunctionDL.getTransformerKnife(curDev, dev);
						replaceStr += CommonFunctionDL.getKnifeOnContent(zycdzList,stationName);
						
						if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
							replaceStr += "大理地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
						}else{
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
							}
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						List<PowerDevice> dycdzList = CommonFunctionDL.getTransformerKnife(curDev, dev);
						replaceStr += CommonFunctionDL.getKnifeOnContent(dycdzList,stationName);
						
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
					
					if(dycmlkgList.size() == 0){
						for(PowerDevice dev : dycmxList){
							for(PowerDevice zbdyckg : zbdyckgList){
								if(zbdyckg.getDeviceStatus().equals("0")){
									replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"电压互感器已处运行/r/n";
								}
							}
						}
					}
				}
				
				for(PowerDevice dev : TransformExecute.cdkgList){
					replaceStr += "大理地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(!TransformExecute.cdkgList.contains(dev)){
						replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(!TransformExecute.cdkgList.contains(dev)){
						replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						String dycmxName = "";
						
						if(dycmlkgList.size() == 0){
							for(PowerDevice dycmx : dycmxList){
								dycmxName = "对"+CZPService.getService().getDevName(dycmx)+"充电";
								break;
							}
							
							replaceStr += "大理地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+dycmxName+"/r/n";
						}else{
							replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
						}
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(isSwitchControl && isSwitchSeparateControl){
					
				}else{
					if(zxdjddzList.size() > 0){					
						replaceStr += CommonFunctionDL.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
					}
					
					if(otherzxdjddzList.size() > 0){
						replaceStr += CommonFunctionDL.getZxdJddzOffCheckContent(otherzxdjddzList, stationName, station);
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
