package com.tellhow.czp.app.yndd.view;


import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.swing.DefaultCellEditor;
import javax.swing.JButton;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.ListSelectionModel;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.mainframe.JPopupTextField;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;



public class CardwordSetEditDialog extends javax.swing.JDialog {
	private String dfsId = "";
	private DefaultTableModel dTableModel;
	private JPanel topPanel;//查询条件及按钮面板
	private JPanel mainPanel;//信息面板
	private JButton addButton;//新增按钮
	private JButton deleteButton;//
	private JButton saveButton;//保存按钮
	private JButton cancelButton;//修改按钮
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTableInfo;//信息列表
	
	public CardwordSetEditDialog(CardwordSetDialog parent,
			boolean modal, String id) {
		super(parent, modal);
		
		dfsId= id;
		initComponents();
		this.setTitle("术语模板维护");
		setLocationCenter();
		initTable();
	}
	
	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	
	private void initComponents() {
		getContentPane().setLayout(new BorderLayout());
		this.setSize(850, 480);
		topPanel =new JPanel();
		topPanel.setLayout(new FlowLayout(FlowLayout.LEADING,15,5));
		topPanel.setPreferredSize(new Dimension(0,45));
		getContentPane().add(topPanel,BorderLayout.NORTH);
		mainPanel =new JPanel();
		getContentPane().add(mainPanel,BorderLayout.CENTER);
		
		addButton =new JButton("新增");
		addButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				addButtonActionPerformed(evt);
			}
		});
		
		deleteButton =new JButton("删除");
		deleteButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				deleteButtonActionPerformed(evt);
			}
		});
		
		saveButton =new JButton("保存");
		saveButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveButtonActionPerformed(evt);
			}
		});
		
		cancelButton =new JButton("退出");
		cancelButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				cancelButtonActionPerformed(evt);
			}
		});
		
		topPanel.add(addButton);
		topPanel.add(deleteButton);
		topPanel.add(saveButton);
		topPanel.add(cancelButton);
		
		dTableModel = new DefaultTableModel(null,new String[] {"ID","模型描述","初始状态","目标状态","操作","排序"}){
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return true;
			}
		};
		jTableInfo = new JTable();
		jTableInfo.setModel(dTableModel);
		JPopupTextField jtf = new JPopupTextField();
		DefaultCellEditor editor = new DefaultCellEditor(jtf);
		jTableInfo.setDefaultEditor(Object.class, editor);
		jTableInfo.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		jTableInfo.getColumnModel().getColumn(0).setPreferredWidth(0);
		jTableInfo.getColumnModel().getColumn(0).setMinWidth(0);
		jTableInfo.getColumnModel().getColumn(0).setMaxWidth(0);
		jTableInfo.getColumnModel().getColumn(2).setPreferredWidth(100);
		jTableInfo.getColumnModel().getColumn(2).setMaxWidth(150);
		jTableInfo.getColumnModel().getColumn(3).setPreferredWidth(100);
		jTableInfo.getColumnModel().getColumn(3).setMaxWidth(150);
		jTableInfo.getColumnModel().getColumn(4).setPreferredWidth(100);
		jTableInfo.getColumnModel().getColumn(4).setMaxWidth(150);
		jTableInfo.getColumnModel().getColumn(5).setPreferredWidth(60);
		jTableInfo.getColumnModel().getColumn(5).setMaxWidth(80);
		jTableInfo.setRowHeight(26);
		jScrollPane1 = new JScrollPane(jTableInfo);
		jScrollPane1.setPreferredSize(new Dimension(800, 370));
		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));
		mainPanel.add(jScrollPane1,BorderLayout.CENTER);
	}
	
	public void initTable() {
		if(!dfsId.equals("")){
			dTableModel.setRowCount(0);
			
			String sql="SELECT ID, MODELDESC, BEGINSTATUS, ENDSTATUS, OPERATION, ORDERID FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL WHERE ID = '"+dfsId+"'";
			 List<Map<String,String>> results=DBManager.queryForList(sql);

			 Map<String,String> temp=new HashMap<String,String>();
			 for (int i = 0; i < results.size(); i++) {
				 temp= results.get(i);
				 String id=StringUtils.ObjToString(temp.get("ID"));
				 String modeldesc=StringUtils.ObjToString(temp.get("MODELDESC"));
				 String beginstatus=StringUtils.ObjToString(temp.get("BEGINSTATUS"));
				 String endstatus=StringUtils.ObjToString(temp.get("ENDSTATUS"));
				 String operation=StringUtils.ObjToString(temp.get("OPERATION"));
				 String orderid=StringUtils.ObjToString(temp.get("ORDERID"));

				 Object[] rowData = {id,modeldesc,beginstatus,endstatus,operation,orderid};
				 dTableModel.addRow(rowData);
			 }

			jTableInfo.setModel(dTableModel);
		}
	}
	
	private void addButtonActionPerformed(ActionEvent evt) {
		WindowUtils.addTableRow(jTableInfo);
	}
	
	private void deleteButtonActionPerformed(ActionEvent evt) {
		int[] selectRows = jTableInfo.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要删除的记录！");
			return;
		}
		
		int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"是否确认删除选中内容？",
				CBSystemConstants.SYSTEM_TITLE,
				JOptionPane.YES_NO_OPTION);
		if (isok != JOptionPane.YES_OPTION)
			return;
		WindowUtils.removeTableRow(jTableInfo);
	}
	
	private void cancelButtonActionPerformed(ActionEvent evt) {
		this.setVisible(false);
		this.dispose();
	}
	
	private void saveButtonActionPerformed(ActionEvent evt) {
		//单元格编辑状态下点击保存
		if (jTableInfo.isEditing())
			jTableInfo.getCellEditor().stopCellEditing();
		
		if(!dfsId.equals("")){
			 String sqlDel="DELETE FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL WHERE ID ='"+dfsId+"' ";
			 DBManager.execute(sqlDel);
		}
		
		for(int i=0;i<dTableModel.getRowCount();i++){
			String id = UUID.randomUUID().toString();
			String model = StringUtils.ObjToString(dTableModel.getValueAt(i, 1));
			String beginstatus = StringUtils.ObjToString(dTableModel.getValueAt(i, 2));
			String endstatus = StringUtils.ObjToString(dTableModel.getValueAt(i, 3));//可以为空
			String operation = StringUtils.ObjToString(dTableModel.getValueAt(i, 4));//可以为空
			String order = StringUtils.ObjToString(dTableModel.getValueAt(i, 5));
			
			String sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL (ID, MODELDESC, BEGINSTATUS, ENDSTATUS, OPERATION, ORDERID) "
					+ "VALUES ('"+id+"','"+model+"','"+beginstatus+"','"+endstatus+"','"+operation+"','"+order+"')";
			
			DBManager.execute(sql);
		}
	
		ShowMessage.view("保存成功！");
		this.setVisible(false);
		this.dispose();
	}
}
