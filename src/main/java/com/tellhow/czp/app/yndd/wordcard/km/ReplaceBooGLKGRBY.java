package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooGLKGRBY implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("关联开关热备用".equals(tempStr)){
			List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true,true);
			List<PowerDevice> gycmlkgList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true,true);

			if(gycmlkgList.size()>0&&xlswList.size()>0){
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(xlswList.get(0)).equals("1")
						||RuleExeUtil.getDeviceBeginStatusContainNotOperate(gycmlkgList.get(0)).equals("1")){
					 return true;
				}
			}
		}
        return false;
	}
}
