package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTXLRBYTOLBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通线路由热备用转冷备用".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			String sql = "SELECT B.ID,B.UNIT,B.LOWERUNIT,B.SWITCH_NAME,B.DISCONNECTOR_NAME FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER B WHERE B.LINE_ID = (SELECT A.ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND A WHERE A.ID = '"+curDev.getPowerDeviceID()+"')";
			List<Map<String, String>> list = DBManager.queryForList(sql);
		    
			for(Map<String,String> map : list){
				String unit = StringUtils.ObjToString(map.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				String switchname = StringUtils.ObjToString(map.get("SWITCH_NAME"));
				String disconnectorname = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));

				if(!disconnectorname.equals("")){
					replaceStr += unit+"@拉开"+lowerunit+disconnectorname+"/r/n";
				}else{
					replaceStr += unit+"@将"+lowerunit+switchname+"由热备用转冷备用/r/n";
				}
			}
			
			for(PowerDevice dev : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);

				List<PowerDevice> zbList =  RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, "", true, true, true);
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

				if(xlkgList.size() == 2){//二分之三接线
					for(PowerDevice xlkg : xlkgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.isDeviceHadStatus(xlkg, "1", "2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(xlkg)+"由热备用转冷备用/r/n";
							}
						}
					}
					
					for(PowerDevice xlkg : xlkgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.isDeviceHadStatus(xlkg, "1", "2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(xlkg)+"由热备用转冷备用/r/n";
							}
						}
					}
				}else{
					if(zbList.size()>0){
						if(RuleExeUtil.isTransformerXBZ(zbList.get(0))||RuleExeUtil.isTransformerXBDY(zbList.get(0))){
							replaceStr +=  stationName+"@将"+CZPService.getService().getDevName(zbList)+"由热备用转冷备用/r/n";
							continue;
						}
					}
					
					if(xlkgList.size() == 0){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
							PowerDevice dz = itor.next();
							
							if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
								itor.remove();
							}
						}
						
						if(dzList.size()==1){
							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(dz).equals("1")){
									replaceStr +=  stationName+"@拉开"+CZPService.getService().getDevName(dz)+"/r/n";
								}
							}
						}
					}else{
						for(PowerDevice xlkg : xlkgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlkg, SystemConstants.SwitchSeparate);
							
							for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
								PowerDevice dz = itor.next();
								
								if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
									itor.remove();
								}
							}
							
							if(dzList.size()==1){
								for(PowerDevice dz : dzList){
									if(RuleExeUtil.getDeviceEndStatus(dz).equals("1")){
										replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dz)+"/r/n";
									}
								}
							}else{
								if(RuleExeUtil.isDeviceHadStatus(xlkg, "1", "2")){
									sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_LINEZYB WHERE LINE_ID = '"+dev.getPowerDeviceID()+"'";
									List<Map<String,String>> zybList =  DBManager.queryForList(sql);

									for(Map<String,String> map : zybList){
										String zybName = StringUtils.ObjToString(map.get("ZYB_NAME"));
										replaceStr += stationName+"@将"+zybName+"由运行转冷备用/r/n";
									}
									
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(xlkg)+"由热备用转冷备用/r/n";
									
									for(PowerDevice dz : xldzList){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("1")){
											replaceStr +=  stationName+"@拉开"+CZPService.getService().getDevName(dz)+"/r/n";
										}
									}
								}
							}
						}
					}
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifeXLS,"", true, true, true, true);//搜索线路关联刀闸

				if(xlkgList.size() == 2){//二分之三接线
					for(PowerDevice xlkg : xlkgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.isDeviceHadStatus(xlkg, "1", "2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(xlkg)+"由热备用转冷备用/r/n";
							}
						}
					}
					
					for(PowerDevice xlkg : xlkgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
							if(RuleExeUtil.isDeviceHadStatus(xlkg, "1", "2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(xlkg)+"由热备用转冷备用/r/n";
							}
						}
					}
				}else{
					for(PowerDevice dev : xlkgList){
						sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_LINEZYB WHERE LINE_ID = '"+sourceLineTrans.getPowerDeviceID()+"'";
						List<Map<String,String>> zybList =  DBManager.queryForList(sql);

						for(Map<String,String> map : zybList){
							String zybName = StringUtils.ObjToString(map.get("ZYB_NAME"));
							replaceStr += stationName+"@将"+(int)dev.getPowerVoltGrade()+"kV"+zybName+"由运行转冷备用/r/n";
						}
						
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "2")){
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}
					}
					
					for(PowerDevice dev : xldzList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr +=  stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
