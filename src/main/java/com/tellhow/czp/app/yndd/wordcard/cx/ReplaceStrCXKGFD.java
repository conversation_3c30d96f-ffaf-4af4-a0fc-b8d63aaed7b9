package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionCX;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXKGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄开关复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				String mxName = "";
				
				if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : mxList){
						mxName = CZPService.getService().getDevName(mx);
						break;
					}
				}
				
				String otherContent = "（接"+mxName+"）";
				replaceStr += CommonFunctionCX.getCheckBeforeContent(deviceName,voltStationName,otherContent);
				
				if(CommonFunctionCX.ifSwitchSeparateControl(curDev)){
					if(!mxName.equals("")){
						replaceStr += "楚雄地调@执行"+stationName+deviceName+"由冷备用转热备用于"+mxName+"程序操作。/r/n";
					}else{
						replaceStr += "楚雄地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作。/r/n";
					}
					replaceStr += CommonFunctionCX.getKnifeOnCheckContent(curDev);
				}else{
					replaceStr += stationName+"@"+deviceName+"由冷备用转热备用。/r/n";
				}
			}
			
			if(curDev.getDeviceStatus().equals("0")){
				replaceStr += CommonFunctionCX.getSwitchOnContent(curDev, stationName);
			}
		}
		
		return replaceStr;
	}

}
