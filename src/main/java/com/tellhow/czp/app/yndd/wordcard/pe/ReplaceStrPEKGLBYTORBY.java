package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEKGLBYTORBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱开关冷备用转热备用".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String kgName = CZPService.getService().getDevName(curDev);
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			
			replaceStr += stationName+"@核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
			
			if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				List<PowerDevice> otherlineList = new ArrayList<PowerDevice>();
						
				for(PowerDevice dev : lineList){
					otherlineList = RuleExeUtil.getLineOtherSideList(dev);
				}
				
				for(PowerDevice dev : otherlineList){
					PowerDevice otherstation = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String otherStationName = CZPService.getService().getDevName(otherstation); 
					replaceStr += otherStationName+"@核实"+CZPService.getService().getDevName(dev)+"具备带电条件/r/n";
				}
			}
			
			if(CommonFunctionPE.ifSwitchSeparateControl(curDev)){
				boolean is35kVMlkg = false;
				
				if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(curDev.getPowerVoltGrade() == 35){
						is35kVMlkg = true;
					}
				}
				
				if(is35kVMlkg){
					replaceStr += CommonFunctionPE.getKnifeListOnContent(dzList,stationName);
				}else{
					replaceStr += "普洱地调@执行"+stationName+kgName+"由冷备用转热备用程序操作/r/n";
					
					/*for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
							String dzName = CommonFunctionPE.getSequentialDeviceName(dz);
							replaceStr += stationName+"@核实"+dzName+"处于合上位置/r/n";
						}
					}*/
				}
			}else{
				replaceStr += stationName+"@将"+kgName+"由冷备用转热备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
