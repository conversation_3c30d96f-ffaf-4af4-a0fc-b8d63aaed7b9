package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.qj.StationWorkSelectionDialog;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipChoose;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class XSBNHaveWorkDialog implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;

		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
//			List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(pd);
//			StationWorkSelectionDialog swsd = new StationWorkSelectionDialog(SystemConstants.getMainFrame(), true, lineList);
		}else{
			List<PowerDevice> deviceList = new ArrayList<PowerDevice>();
			deviceList.add(pd);
			StationWorkSelectionDialog swsd = new StationWorkSelectionDialog(SystemConstants.getMainFrame(), true, deviceList);
		}

		return true;
	}

	/*public static List<PowerDevice> tagDevList = new ArrayList<PowerDevice>();
	
	@Override
	public boolean execute(RuleBaseMode rbm) {

		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;

		tagDevList.clear();

		String showMessage="请选择站内及线路有工作的设备";
		List<PowerDevice> devList = new ArrayList<PowerDevice>();

		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			devList.addAll(RuleExeUtil.getLineAllSideList(pd));
		}else{
			devList.add(pd);
		}

		List<Map<String, String>> stationLineList = CommonFunctionBN.getStationLineList(pd);

	    if(stationLineList.size()>0){
			for(Map<String,String> map : stationLineList){
				PowerDevice newLine = new PowerDevice();
				newLine.setPowerDeviceID(StringUtils.ObjToString(map.get("ID")));
				newLine.setPowerDeviceName(StringUtils.ObjToString(map.get("LINE_NAME")));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				newLine.setPowerVoltGrade(10);
				if(!lowerunit.equals("")){
					newLine.setPowerStationName(lowerunit);
				}else{
					newLine.setPowerStationName(StringUtils.ObjToString(map.get("UNIT")));
				}

				devList.add(newLine);
			}
		}

		EquipChoose dialog = new EquipChoose(SystemConstants.getMainFrame(), true, devList, showMessage);

		if(dialog.isCancel()){
			return false;
		}

		tagDevList = dialog.getChooseEquip();

		return true;
	}*/
}

