package com.tellhow.czp.app.yndd.wordcard.yx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.yx.TransformExecuteYX;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYXKDNQJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("玉溪扩大内桥接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(curDev);
			
			double lowVoltage = RuleExeUtil.getTransformerVolByType(curDev,"low");
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycotherxlkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxsList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();

			boolean isSwitchControl = true;
			
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunction.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunction.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			}

			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(!zbgyckgList.contains(dev)){
							gycotherxlkgList.add(dev);
						}
					}
				}
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
					
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}else if(dev.getPowerVoltGrade() == lowVoltage){
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
						dycmxsList.add(dev);
					}
				}
			}
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				otherzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(otherzxdjddzList);
				break;
			}
			
			for (Iterator<PowerDevice> it = zxdjddzList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == 35){
					it.remove();
				}
			}
			
			for (Iterator<PowerDevice> it = otherzxdjddzList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == 35){
					it.remove();
				}
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunction.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			if(zbList.size() == 1){//单主变
				for(PowerDevice dev : dycmxsList){
					if(dev.getPowerVoltGrade() == 10){
						replaceStr += "玉溪配调@核实"+stationName+CZPService.getService().getDevName(dev)+"具备送电条件/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					replaceStr += "玉溪配调@核实"+stationName+CZPService.getService().getDevName(dev)+"已处运行/r/n";
				}
				
				for(PowerDevice dev : dycmxList){
					if(dev.getPowerVoltGrade() == 10){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"电压互感器已处运行/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+RuleExeUtil.getStatus(dev.getDeviceStatus())+"/r/n";
				}
				
				for(PowerDevice dev : gycotherxlkgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+RuleExeUtil.getStatus(dev.getDeviceStatus())+"/r/n";
				}
				
				for(PowerDevice dev : gycmlkgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+RuleExeUtil.getStatus(dev.getDeviceStatus())+"/r/n";
				}
				
				String gycmlkgstatus = "";
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						gycmlkgstatus = "用"+CZPService.getService().getDevNum(dev)+"断路器充电";
					}else{
						gycmlkgstatus = CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatus(dev.getDeviceStatus());
					}
				}
				
				String zbgyckgstatus = "";
				
				for(PowerDevice dev : zbgyckgList){
					if(!RuleExeUtil.isDeviceChanged(dev)||dev.getDeviceStatus().equals(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev))){
						zbgyckgstatus = "（备注："+CZPService.getService().getDevNum(dev)+"断路器不操作）";
					}else{
						zbgyckgstatus = "，"+CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatus(dev.getDeviceStatus());
					}
				}
				
				replaceStr += stationName+"@将"+deviceName+"由冷备用转运行，"+gycmlkgstatus+zbgyckgstatus+"/r/n";
				
				replaceStr += "按当前运行方式投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				
				for(PowerDevice dev : dycmxsList){
					if(dev.getPowerVoltGrade() == 10){
						replaceStr += "玉溪配调@通知"+stationName+CZPService.getService().getDevName(dev)+"已处运行/r/n";
					}
				}
			}else{
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						if(CommonFunction.ifSwitchSeparateControl(dev)){
							replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
						}else{
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
							}
						}
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						if(CommonFunction.ifSwitchSeparateControl(dev)){
							replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
						}else{
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
							}
						}
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					for(PowerDevice dev : zbgycdzList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							if(CommonFunction.ifSwitchSeparateControl(dev)){
								replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
								replaceStr += CommonFunction.getKnifeOnCheckContent(zbgycdzList, stationName);
							}else{
								replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						List<PowerDevice> zycdzList = CommonFunction.getTransformerKnife(curDev, dev);
						replaceStr += CommonFunction.getKnifeOnContent(zycdzList,stationName);
						
						if(CommonFunction.ifSwitchSeparateControl(dev)){
							replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunction.getKnifeOnCheckContent(dzList, stationName);
						}else{
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
							}
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						List<PowerDevice> dycdzList = CommonFunction.getTransformerKnife(curDev, dev);
						replaceStr += CommonFunction.getKnifeOnContent(dycdzList,stationName);
						
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
					
					if(dycmlkgList.size() == 0){
						for(PowerDevice dev : dycmxList){
							for(PowerDevice zbdyckg : zbdyckgList){
								if(zbdyckg.getDeviceStatus().equals("0")){
									replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"电压互感器已处运行/r/n";
								}
							}
						}
					}
				}
				
				boolean isxnqkgcd = false;
				
				for(PowerDevice dev : TransformExecuteYX.cdkgList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						isxnqkgcd = true;
					}
					replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						String dycmxName = "";
						
						if(dycmlkgList.size() == 0){
							for(PowerDevice dycmx : dycmxList){
								dycmxName = "对"+CZPService.getService().getDevName(dycmx)+"充电";
								break;
							}
							
							replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+dycmxName+"/r/n";
						}else{
							replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
						}
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(isxnqkgcd){
					for(PowerDevice dev : zbgyckgList){
						if(!TransformExecuteYX.cdkgList.contains(dev)){
							replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(!TransformExecuteYX.cdkgList.contains(dev)){
							replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
				
				
				if(isSwitchControl && isSwitchSeparateControl){
					
				}else{
					if(zxdjddzList.size() > 0){					
						replaceStr += CommonFunction.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
					}
					
					if(otherzxdjddzList.size() > 0){
						replaceStr += CommonFunction.getZxdJddzOffCheckContent(otherzxdjddzList, stationName, station);
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						replaceStr += "按当前运行方式投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "按当前运行方式投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "按当前运行方式投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}