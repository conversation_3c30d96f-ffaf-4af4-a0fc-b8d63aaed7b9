package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.Rectangle;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFileChooser;
import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTextField;

import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.hwpf.usermodel.Table;
import org.apache.poi.hwpf.usermodel.TableIterator;
import org.apache.poi.hwpf.usermodel.TableRow;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;
import czprule.wordcard.model.SequencesModel;

import java.awt.Font;

public class ChannelWordDialog extends JDialog{
	public ChannelWordDialog(JFrame mainFrame, boolean model) {
		super(mainFrame,model);
		
		initComponents();
		this.setTitle("导入Word");
		setLocationCenter();
	}
	
	private void initComponents() {
		this.setSize(495, 218);
		
		JPanel jpanel = new JPanel();
		jpanel.setLocation(0, 0);
		jpanel.setSize(378, 194);
		jpanel.setLayout(null);
		JButton btnSelect = new JButton("选择");
		btnSelect.setFont(new Font("微软雅黑", Font.PLAIN, 20));
		JButton btnChannel = new JButton("导入");
		btnChannel.setFont(new Font("微软雅黑", Font.PLAIN, 20));
		btnChannel.setLocation(358, 64);
		btnChannel.setSize(100, 35);
		
		btnSelect.setLocation(245, 64);
		btnSelect.setSize(100, 35);
		final JTextField txtSelect = new JTextField();
		
		txtSelect.setBounds(15, 64, 215, 35);
		
		btnSelect.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                JFileChooser fileChooser = new JFileChooser();
                int option = fileChooser.showDialog(ChannelWordDialog.this, "选择");
                if(option==JFileChooser.APPROVE_OPTION){
                    File file = fileChooser.getSelectedFile();
                    String fileName = file.getAbsolutePath();
                    txtSelect.setText(fileName);
                }else{
                    txtSelect.setText("");
                }
            }
        });
		
		btnChannel.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
               if(!txtSelect.getText().equals("")){
            	   String filepath = txtSelect.getText();
            	   if(filepath.endsWith(".doc")){
            		   readWordDoc(filepath);
            	   }else if(filepath.endsWith(".docx")){
            		   readWordDocX(filepath);
            	   }
               }else{
            	   ShowMessage.view("请先选择文件！");
               }
            }
        });
		
		jpanel.add(txtSelect);
		jpanel.add(btnSelect);
		jpanel.add(btnChannel);
		
		getContentPane().add(jpanel);
	}
	
	//小写转大写
	public String getStageInt(String str) {
		 str = str.replace("阶段", "");
		 
		 int stageint = RuleExeUtil.chineseNumber2Int(str);
		 
	    return String.valueOf(stageint);
    }
	
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	
	 /**
     * 读取word文档中后缀为doc的文件
     * @param filePath
     * @return
     */
    public String readWordDoc(String filePath){
    	String content = null;
        FileInputStream inputStream = null;
        String msg = "";
        
		try {
			inputStream = new FileInputStream(filePath);
		} catch (FileNotFoundException e1) {
			e1.printStackTrace();
		}
		HWPFDocument hwpf = null;
		try {
			POIFSFileSystem pfs = new POIFSFileSystem(inputStream);
	        hwpf = new HWPFDocument(pfs);
		} catch (Exception e) {
			System.out.println(e.getMessage());
			msg = "文档格式错误，请尝试另存为新的文件";
		}
		
		if(msg.equals("")) {
			String czrw = "";
			String bzsx = "";

     	    List<CardItemModel> cardItems = new ArrayList<CardItemModel>();
			
			Range range = hwpf.getRange();//区间
            
			TableIterator it = new TableIterator(range);
			
			while (it.hasNext()) {
				Table table = it.next();
				
				for(int i=0 ; i<table.numRows() ; i++){
					TableRow row = table.getRow(i);
                    String rowText = row.text().replace("", " ").trim();
                    
                    if(rowText.length() > 0){
                    	String firstword = rowText.substring(0, 1);
                    	
                    	if(rowText.startsWith("操作任务")){
                        	czrw = rowText.replace("操作任务", "").trim();
                        }else if(rowText.startsWith("备注")){
                        	TableRow nextrow = table.getRow(i+1);
                        	bzsx = nextrow.text().replace("", " ").trim();
                        }else if(StringUtils.isNum(firstword)&&!rowText.contains("、")){
                        	String[] rowArr = rowText.split(" ");
                        	
                        	CardItemModel cim = new CardItemModel();
                        	
							String cardItem = getStage(rowArr[0]);
							String cardNum = rowArr[0];
							String stationName = rowArr[1];
							String cardDesc = rowArr[2];
							
							cim.setCardItem(cardItem);
							cim.setCardNum(cardNum);
							cim.setStationName(stationName);
							cim.setCardDesc(cardDesc);
							
							cardItems.add(cim);
                        }
                    }
				}
			}
			
			CardModel cm = new CardModel();
	    	
    		cm.setCzrw(czrw);
    		cm.setBzsx(bzsx);
    		cm.setCardItems(cardItems);
	    	
	    	RuleBaseMode Srcrbm = new RuleBaseMode();
	    	
	    	CommonFunction.openTicketWindow(cm, Srcrbm);
	    	ShowMessage.view("导入成功！");
	    	this.setVisible(false);
		}else{
			ShowMessage.view(msg);
		}
		
		return content;
	}
    
    /**
     * 读取word文档中后缀为docx的文件
     * @param filePath
     * @return
     */
    public String readWordDocX(String filePath){
        String content = null;
        FileInputStream inputStream = null;
        String msg = "";
        
        try {
			try {
				inputStream = new FileInputStream(filePath);
			} catch (FileNotFoundException e1) {
				e1.printStackTrace();
				msg = "文档格式错误，请尝试另存为新的文件";
			}
			XWPFDocument docx = null;
			try {
				docx = new XWPFDocument(inputStream);
			} catch (Exception e) {
				System.out.println(e.getMessage());
			}
			
			String czrw = "";
			String bzsx = "";

     	    List<CardItemModel> cardItems = new ArrayList<CardItemModel>();
			
			Iterator<XWPFTable> itor = docx.getTablesIterator();// 得到word中的表格
				
			while (itor.hasNext()) {
				XWPFTable table = itor.next();
				
				List<XWPFTableRow> xwpfTableRowList = table.getRows();
				
				for(int i=0 ; i<xwpfTableRowList.size() ; i++){
					XWPFTableRow row = xwpfTableRowList.get(i);
					
					if(row.getCell(0) != null){
						String cellText = row.getCell(0).getText();
						
						if(cellText.equals("操作任务")){
							if(row.getCell(1) != null){
								czrw = row.getCell(1).getText();
							}
						}else if(cellText.equals("备注")){
							XWPFTableRow rownext = xwpfTableRowList.get(i+1);
							
							if(rownext.getCell(0) != null){
								bzsx = rownext.getCell(0).getText();
							}
						}else if(StringUtils.isNum(cellText)){
							CardItemModel cim = new CardItemModel();
							String cardItem = getStage(cellText);
							String cardNum = cellText;
							String stationName = "";
							String cardDesc = "";
							
							if(row.getCell(1) != null){
								stationName = row.getCell(1).getText();
							}
							
							if(row.getCell(2) != null){
								cardDesc = row.getCell(2).getText();
							}
							
							cim.setCardItem(cardItem);
							cim.setCardNum(cardNum);
							cim.setStationName(stationName);
							cim.setCardDesc(cardDesc);
							
							cardItems.add(cim);
						}else if(row.getCell(1) != null){
							if(row.getCell(0).getText().equals("")){
								bzsx = row.getCell(1).getText();
							}
						}
					}
				}
			}
			
	    	CardModel cm = new CardModel();
	    	
    		cm.setCzrw(czrw);
    		cm.setBzsx(bzsx);
    		cm.setCardItems(cardItems);
	    	
	    	RuleBaseMode Srcrbm = new RuleBaseMode();
	    	
	    	CommonFunction.openTicketWindow(cm, Srcrbm);
	    	ShowMessage.view("导入成功！");
	    	this.setVisible(false);
		} catch (Exception e) {
            e.printStackTrace();
			ShowMessage.view(msg);
        }
        return content;
    }
    
    public static String getStage(String str) {
		String stage = "阶段"+RuleExeUtil.int2chineseNum(Integer.valueOf(str));
		return stage;
    }
}
