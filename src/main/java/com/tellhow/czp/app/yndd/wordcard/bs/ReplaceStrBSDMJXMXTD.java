package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSDMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山单母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			String maintenanceName = CommonFunctionBS.getMaintenance(stationName);

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);

			if(mlkgList.size()>0){//分段
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					
					
				}else{
					
					
				}
			}else{
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					
					
				}else{
					for(PowerDevice dev : qtkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "保山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "保山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(maintenanceName.equals(stationName)){
						replaceStr += stationName+"@核实"+deviceName+"电压互感器二次侧空气开关已断开/r/n";
					}else{
						replaceStr += maintenanceName+"@核实"+stationName+deviceName+"电压互感器二次侧空气开关已断开/r/n";
					}
					
					if(maintenanceName.equals(stationName)){
						replaceStr += maintenanceName+"@核实"+deviceName+"停电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
					}else{
						replaceStr += maintenanceName+"@核实"+stationName+deviceName+"停电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
					}
					
					if(curDev.getDeviceStatus().equals("2")){
						replaceStr += "保山地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					}
					
					if(maintenanceName.equals(stationName)){
						replaceStr += maintenanceName+"@核实"+deviceName+"停电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
					}else{
						replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备无异常/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
