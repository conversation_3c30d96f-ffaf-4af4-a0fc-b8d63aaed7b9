package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.BorderFactory;
import javax.swing.DefaultCellEditor;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.event.ListSelectionEvent;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;

import com.tellhow.czp.app.yndd.dao.CustomCodexDao;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.view.CustomCodex;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.SVGAddDeviceInf;
import czprule.system.ShowMessage;
import czprule.wordcard.view.InitDeviceTypeChockBox;

/**
 * 
 * <AUTHOR> 2016年5月22日
 *
 */
public class CustomCodexGZ extends CustomCodex implements SVGAddDeviceInf {

	private PowerDevice choosePd = null; // 点击选择设备
	private PowerDevice executeDevice = null;// 执行子设备
	private boolean isLocked1 = true; // true:点击设备后切换源设备，false：添加关联设备
	CustomCodexDao ccdd = new CustomCodexDao();
	private DefaultTableModel jtableModel1;

	// 构造方法
	public CustomCodexGZ() {
		initComponents();
		initTable1(null);
		buttonAction();
		init(false);
	}

	// 添加设备入口
	public void addPowerDevice(PowerDevice pd) {
		addDevice(pd);
	}

	public void addDevice(PowerDevice pd) {
		if (isLocked1) { // 切换设备

			this.choosePd = pd;
			jTextField1.setText(this.choosePd.getPowerDeviceName());
			jComboBox1.setModel(InitDeviceTypeChockBox.getDeviceStatusCheckBox(this.choosePd.getDeviceType()));
			jComboBox2.setModel(InitDeviceTypeChockBox.getDeviceStateCheckBox(this.choosePd.getDeviceType(),
					CBSystemConstants.cardbuildtype));

			// 初始化表格
			// this.initTable1(this.getLinkDevices());
			initTable1(null);
			init(false);
		} else if (!isLocked1) {
			// 设备执行动作表添加设备
			this.executeDevice = pd;
			String stationName = executeDevice.getPowerStationName();// 所在变电站
			CodeNameModel cnm = new CodeNameModel();
			cnm.setCode(executeDevice.getDeviceStatus());
			cnm.setName(CBSystemConstants.getDeviceStatusName(executeDevice.getDeviceType(),
					executeDevice.getDeviceStatus()));
			Object[] rowData = { (this.jtableModel1.getRowCount() + 1), stationName, executeDevice, cnm, "" };
			this.jtableModel1.addRow(rowData);
		}
	}

	// 页面按钮管理
	public void buttonAction() {
		// 锁定关联设备
		jButton1.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				changeSuo1(e);
			}
		});
		// 删除
		jButton2.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				delAction(evt);
			}
		});
		// 上移
		jButton3.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				upAction(evt);
			}
		});
		// 下移
		jButton4.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent evt) {
				dowAction(evt);
			}
		});

		// 取消
		jButton7.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				CancelSet(e);
			}
		});

		// 保存
		jButton6.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveAction();
			}
		});
		//保存并且退出
		jButton5.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveAction();
				CancelSet(evt);
			}
			
		});
	}

	// 上移
	private void upAction(ActionEvent evt) {
		WindowUtils.moveupTableRow(jTable1);
	}

	// 下移
	private void dowAction(ActionEvent evt) {
		WindowUtils.movedownTableRow(jTable1);
	}

	// 取消按钮
	public void CancelSet(ActionEvent e) {
		CBSystemConstants.svgAddPd = null;
		this.setVisible(false);
	}

	// 删除自定义规则
	protected void delAction(ActionEvent evt) {
		WindowUtils.removeTableRow(jTable1);
	}

	// 表一添加
	public void customAcionBak(ActionEvent e) {
		jButton1.setText("锁定");
		this.isLocked1 = false;
		this.jtableModel1.addRow(new Object[] { "", "", "", "", "" });
	}
	//保存
	public void saveAction() {
		//操作任务
		String status = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
		String state = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		String remark = this.jTextArea1.getText();
		if (state.equals("") || status.equals("")) {
			ShowMessage.view("请选择设备起始状态或执行动作！");
			return;
		}

		int countRows = jTable1.getRowCount();
		PowerDevice tempDev = null;
		CodeNameModel statusCnm = null;
		CodeNameModel stateCnm = null;
		List<String[]> cblist = new ArrayList<String[]>();
		for (int j = 0; j < countRows; j++) {
			if (jTable1.getValueAt(j, 2) == null || jTable1.getValueAt(j, 3) == null
					|| jTable1.getValueAt(j, 4) == null) {
				continue;
			}
			if (jTable1.getValueAt(j, 2) == "" || jTable1.getValueAt(j, 3) == "" || jTable1.getValueAt(j, 4) == "") {
				continue;
			}
			tempDev = (PowerDevice) jTable1.getValueAt(j, 2);
			statusCnm = (CodeNameModel) jTable1.getValueAt(j, 3);
			stateCnm = (CodeNameModel) jTable1.getValueAt(j, 4);
			String[] tempObjs = new String[] { tempDev.getPowerDeviceID(), tempDev.getPowerStationID(),
					statusCnm.getCode(), stateCnm.getCode() };
			cblist.add(tempObjs);
		}

	
		//操作术语
		String czrw = jTextArea2.getText();// 操作任务
		String conter = jTextArea3.getText();
		String[] rowStrs = null;
		if (conter.indexOf("\r\n") >= 0) {
			rowStrs = conter.split("\r\n");
		} else if (conter.indexOf("\n") >= 0) {
			rowStrs = conter.split("\n");
		} else {
			rowStrs = new String[] { conter };
		}
		if(stateCnm==null || stateCnm.equals("") ){
			javax.swing.JOptionPane.showMessageDialog(this, "请选择执行状态!", CBSystemConstants.SYSTEM_TITLE,
					JOptionPane.WARNING_MESSAGE);
		}else{
			ccdd.insertCustom(this.choosePd.getPowerDeviceID(),tempDev.getPowerStationID(),status, state,remark,
					cblist,czrw,rowStrs);
					javax.swing.JOptionPane.showMessageDialog(this, "保存成功!", CBSystemConstants.SYSTEM_TITLE,
							JOptionPane.WARNING_MESSAGE);
		}
	}

	// 下拉监听
	public void itemStateChanged(ItemEvent e) {

		String status = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
		String state = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		if (status.equals("")) {
			return;
		}
		if (state.equals("")) {
			return;
		}
		init(true);
		String equipId = choosePd.getPowerDeviceID();// 设备id
		List<String[]> oneList = ccdd.getUserRuleExecuteEquip(equipId, status, state);
		jtableModel1.setRowCount(0);
		if (oneList.size() > 0) {
			String[] tempStr = new String[4];
			for (int l = 0; l < oneList.size(); l++) {
				tempStr = oneList.get(l);
				PowerDevice pdN = CBSystemConstants.getPowerDevice(tempStr[1], tempStr[0]);
				String stionName = pdN.getPowerStationName();// 设备变电站名称
				CodeNameModel statuscnm = new CodeNameModel();
				statuscnm.setCode(tempStr[2]);
				statuscnm.setName(CBSystemConstants.getDeviceStatusName(pdN.getDeviceType(), tempStr[2]));
				CodeNameModel statecnm = new CodeNameModel();
				statecnm.setCode(tempStr[3]);
				statecnm.setName(CBSystemConstants.getDeviceStateName(tempStr[3]));
				Object[] rowData = { l + 1, stionName, pdN, statuscnm, statecnm };
				jtableModel1.addRow(rowData);
			}
			jTable1.setModel(jtableModel1);
			TableColumnModel tcm = jTable1.getColumnModel();
			tcm.getColumn(0).setWidth(60);
			tcm.getColumn(0).setMaxWidth(60);
			tcm.getColumn(1).setWidth(200);
			tcm.getColumn(2).setWidth(200);
		} else {
			initTable1(null);
		}
	}

	// 锁状态的切换
	public void changeSuo1(ActionEvent e) {
		String suoStr = jButton1.getToolTipText();
		if ("未锁定".equals(suoStr.trim())) {
			jButton1.setToolTipText("锁定");
			jButton1.setBorder(null);
			jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/lock.png")));
			this.isLocked1 = false;
		} else {
			ShowMessage.view("请注意保存相关的记录!");
			jButton1.setToolTipText("未锁定");
			jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/unlock.png")));
			this.isLocked1 = true;
		}
	}

	// 初始化隐藏按钮
	public void init(boolean isHide) {
		this.jButton1.setEnabled(isHide);
		this.jButton2.setEnabled(isHide);
		this.jButton3.setEnabled(isHide);
		this.jButton4.setEnabled(isHide);
		this.jButton5.setEnabled(isHide);
		this.jButton6.setEnabled(isHide);
	}

	// 表格换行
	private void changeState(int row) {

		String status = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
		String state = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		if (state.equals("") || status.equals("")) {
			ShowMessage.view("请选择设备起始状态或执行动作！");
			return;
		}

		if (jTable1.getColumnModel().getColumn(3).getCellEditor() != null) {
			jTable1.getColumnModel().getColumn(3).setCellEditor(null);
		}
		if (jTable1.getColumnModel().getColumn(4).getCellEditor() != null) {
			jTable1.getColumnModel().getColumn(4).setCellEditor(null);
		}
		if (jTable1.getCellEditor() != null) {
			jTable1.getCellEditor().stopCellEditing();
		}

		PowerDevice pd = (PowerDevice) jtableModel1.getValueAt(row, 2);
		if (pd == null)
			return;
		// 设备状态下拉框
		JComboBox statusCheck = new JComboBox();
		statusCheck.setModel(InitDeviceTypeChockBox.getDeviceStatusCheckBox(pd.getDeviceType()));
		DefaultCellEditor statusEditor = new DefaultCellEditor(statusCheck);
		statusEditor.setClickCountToStart(2);
		jTable1.getColumnModel().getColumn(3).setCellEditor(statusEditor);
		// 设备动作下拉框
		JComboBox stateCheck = new JComboBox();
		stateCheck.setModel(InitDeviceTypeChockBox.getDeviceStateCheckBox(pd.getDeviceType(), "0"));
		DefaultCellEditor stateEditor = new DefaultCellEditor(stateCheck);
		stateEditor.setClickCountToStart(2);
		jTable1.getColumnModel().getColumn(4).setCellEditor(stateEditor);
		jButton1.setText("锁定");
		this.isLocked1 = false;
	}

	// 初始化执行设备表
	@SuppressWarnings("serial")
	public void initTable1(List<PowerDevice> pdLists) {
		jtableModel1 = new DefaultTableModel(null, new String[] { "序号", "厂站", "设备", "初始状态", "执行状态" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				if (columnIndex == 3 || columnIndex == 4) {
					return true;
				} else {
					return false;
				}
			}
		};
		if (pdLists != null) {
			PowerDevice tempDev = null;
			String stationName = ""; // 变电站名称
			for (int i = 0; i < pdLists.size(); i++) {
				tempDev = pdLists.get(i);
				stationName = tempDev.getPowerStationName();// 所在变电站
				CodeNameModel cnm = new CodeNameModel();
				cnm.setCode(tempDev.getDeviceStatus());
				cnm.setName(CBSystemConstants.getDeviceStatusName(tempDev.getDeviceType(), tempDev.getDeviceStatus()));
				Object[] rowData = { (i + 1), stationName, tempDev, cnm, "" };
				jtableModel1.addRow(rowData);
			}
		}
		jTable1.setModel(jtableModel1);
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);// 列名居中
		sjp.getDefaultRenderer(jTable1.getColumnClass(1), jTable1);

		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMaxWidth(40);
		tcm.getColumn(1).setMaxWidth(100);
		tcm.getColumn(2).setMinWidth(130);
	}

	// 搜索开关和直接接地刀闸
	@SuppressWarnings("unchecked")
	public List<PowerDevice> getLinkDevices() {

		List<PowerDevice> linkDevs = new ArrayList<PowerDevice>(); // 搜索设备集合
		if (this.choosePd == null)
			return linkDevs;

		Map<String, Object> inMap = new HashMap<String, Object>();
		Map<String, Object> outMap = new HashMap<String, Object>();
		CommonSearch cs = new CommonSearch();
		List<PowerDevice> searchDevs = null;
		if (choosePd.getDeviceType().equals(SystemConstants.InOutLine)) {
			Map<PowerDevice, String> stationlines = QueryDeviceDao.getPowersLineByLine(choosePd);
			List<PowerDevice> trans = new ArrayList<PowerDevice>();
			PowerDevice dev = null; // 变电站对象
			for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
				dev = (PowerDevice) iterator.next();
				trans.add(dev);
			}
			// 搜索开关
			inMap.put("oprSrcDevice", choosePd);
			inMap.put("tagDevType", SystemConstants.Switch);
			cs.execute(inMap, outMap);
			searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
			linkDevs.addAll(searchDevs);
			// 搜索电抗器
			inMap.put("oprSrcDevice", choosePd);
			inMap.put("tagDevType", SystemConstants.ElecShock);
			cs.execute(inMap, outMap);
			inMap.clear();
			searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
			linkDevs.addAll(searchDevs);

		} else {
			if (!choosePd.getDeviceType().equals(SystemConstants.Switch)) {
				// 搜索开关
				inMap.put("oprSrcDevice", choosePd);
				inMap.put("tagDevType", SystemConstants.Switch);
				cs.execute(inMap, outMap);
				searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
				linkDevs.addAll(searchDevs);
			} else {
				// 搜索刀闸
				inMap.put("oprSrcDevice", choosePd);
				inMap.put("tagDevType", SystemConstants.SwitchSeparate);
				cs.execute(inMap, outMap);
				inMap.clear();
				searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
				linkDevs.addAll(searchDevs);
				
			}
		}
		// 搜索直接连接接地刀闸
		inMap.put("oprSrcDevice", choosePd);
		inMap.put("tagDevType", SystemConstants.SwitchFlowGroundLine);// 目标直接接地刀闸
		inMap.put("isSearchDirectDevice", true);
		cs.execute(inMap, outMap);
		searchDevs = (ArrayList<PowerDevice>) outMap.get("linkedDeviceList");
		linkDevs.addAll(searchDevs);
		return linkDevs;
	}

	// 初始化组件
	private void initComponents() {
		jPanel1 = new JPanel();
		jPanel2 = new JPanel();
		jPanel3 = new JPanel();
		jPanel4 = new JPanel();
		jPanel5 = new JPanel();
		jPanel6 = new JPanel();
		jPanel7 = new JPanel();
		jPanel8 = new JPanel();
		jPanel9 = new JPanel();
		jPanel10 = new JPanel();
		jLabel1 = new JLabel();
		jLabel2 = new JLabel();
		jLabel3 = new JLabel();
		jLabel4 = new JLabel();
		jLabel5 = new JLabel();
		jLabel6 = new JLabel();
		jComboBox1 = new JComboBox();
		jComboBox2 = new JComboBox();
		jTextField1 = new JTextField(18);
		jTable1 = new JTable() {
			public void valueChanged(ListSelectionEvent e) {
				super.valueChanged(e);
				int row = getSelectedRow();
				if (row == -1) {
					return;
				}
				if (!this.getValueAt(row, 0).equals("")) {
					changeState(row);
				}
			}
		};
		jTabbedPane1 = new JTabbedPane();
		jScrollPane1 = new JScrollPane();
		jScrollPane2 = new JScrollPane(jPanel12);
		jButton1 = new JButton();
		jButton1.setSize(20, 20);
		jButton2 = new JButton();
		jButton3 = new JButton();
		jButton4 = new JButton();
		jButton5 = new JButton("保存并且退出");
		jButton6 = new JButton("保存");
		jButton7 = new JButton("取消");
		jTextArea1 = new JTextArea();
		jTextArea2 = new JTextArea();
		jTextArea3 = new JTextArea();
		jPanel11 = new JPanel();
		jPanel12 = new JPanel();
		jPanel13 = new JPanel();
		// 添加组件和组件布局
		jLabel1.setText(" 设备：");
		jTextField1.setEditable(false);
		jTextField1.setFont(new java.awt.Font("微软雅黑", 1, 14));
		jLabel2.setText(" 状态：");
		jComboBox1.setPreferredSize(new Dimension(70, 22));// 设置JComboBox宽度的代码
		jComboBox1.addItemListener(new java.awt.event.ItemListener() {
			public void itemStateChanged(java.awt.event.ItemEvent evt) {
				CustomCodexGZ.this.itemStateChanged(evt);
			}
		});
		jLabel3.setText(" 操作：");
		jComboBox2.addItemListener(new java.awt.event.ItemListener() {
			public void itemStateChanged(java.awt.event.ItemEvent evt) {
				CustomCodexGZ.this.itemStateChanged(evt);
			}
		});
		jLabel4.setText(" 备注：");
		jTextArea1.setLineWrap(true);
		jTextArea1.setPreferredSize(new Dimension(428, 42));
		jComboBox2.setPreferredSize(new Dimension(135, 22));// 设置JComboBox宽度的代码
		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/unlock.png"))); // NOI18N
		jButton1.setToolTipText("未锁定");
		jButton1.setBorder(null);
		jButton1.setBorderPainted(false);
		jButton1.setFocusPainted(false);

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton2.setToolTipText("删除");
		jButton2.setBorder(null);

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
		jButton3.setToolTipText("上移");
		jButton3.setBorder(null);

		jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
		jButton4.setToolTipText("下移");
		jButton4.setBorder(null);

		jScrollPane1.setViewportView(jTable1);

		jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
		jButton5.setToolTipText("保存并且退出");
		jButton5.setMargin(new java.awt.Insets(1, 1, 1, 1));

		jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
		jButton6.setToolTipText("保存");
		jButton6.setMargin(new java.awt.Insets(1, 1, 1, 1));
		jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
		jButton7.setToolTipText("取消");
		jButton7.setMargin(new java.awt.Insets(1, 1, 1, 1));
		// 添加组件设定布局 顶部
		jPanel5.setLayout(new BorderLayout());
		jPanel2.setLayout(new FlowLayout(FlowLayout.LEFT));
		jPanel2.add(jLabel1);
		jPanel2.add(jTextField1);
		jPanel13.setLayout(new FlowLayout(FlowLayout.LEFT));
		jPanel13.add(jLabel2);
		jPanel13.add(jComboBox1);
		jPanel13.add(jLabel3);
		jPanel13.add(jComboBox2);
		jPanel5.add(jPanel2, BorderLayout.NORTH);
		jPanel5.add(jPanel13, BorderLayout.CENTER);
		jPanel4.add(jLabel4);
		jPanel4.add(jTextArea1);
		jPanel1.setLayout(new BorderLayout());
		jPanel1.add(jPanel5, BorderLayout.NORTH);
		jPanel1.add(jPanel4, BorderLayout.WEST);

		// 中部
		jTabbedPane1.addTab("自定义规则", jPanel6);
		jTabbedPane1.addTab("自定义术语", jPanel7);
		jPanel10.add(jButton1);
		jPanel11.add(jButton2);
		jPanel11.add(jButton3);
		jPanel11.add(jButton4);
		jPanel8.setLayout(new BorderLayout());
		jPanel8.add(jPanel10, BorderLayout.WEST);
		jPanel8.add(jPanel11, BorderLayout.EAST);
		jScrollPane1.add(jPanel9);
		jScrollPane1.setBorder(BorderFactory.createTitledBorder("关联设备执行"));
		jScrollPane1.setViewportView(jTable1);
		jLabel5.setText(" 操作任务： ");
		jLabel5.setBounds(0, -5, 100, 50);
		jTextArea2.setLineWrap(true);
		jTextArea2.setBounds(60, 10, 417, 60);
		jLabel6.setText(" 操作指令： ");
		jLabel6.setBounds(0, 70, 100, 50);
		jTextArea3.setLineWrap(true);
		jScrollPane2.setViewportView(jTextArea3);
		jScrollPane2.setBounds(60, 90, 418, 330);

		jPanel7.setLayout(null);
		jPanel7.add(jLabel5);
		jPanel7.add(jTextArea2);
		jPanel7.add(jLabel6);
		jPanel7.add(jScrollPane2);
		jPanel6.setLayout(new BorderLayout());
		jPanel6.add(jPanel8, BorderLayout.NORTH);
		jPanel6.add(jScrollPane1, BorderLayout.CENTER);

		// 底部
		jPanel3.setLayout(new FlowLayout(FlowLayout.RIGHT));
		jPanel3.add(jButton5);
		jPanel3.add(jButton6);
		jPanel3.add(jButton7);
		this.setLayout(new BorderLayout()); // 将panel的默认布局flow设置为设置边界布局
		this.add(jPanel1, BorderLayout.NORTH);
		this.add(jTabbedPane1, BorderLayout.CENTER);
		this.add(jPanel3, BorderLayout.SOUTH);
		this.setVisible(true);

	}

	private JPanel jPanel1;
	private JPanel jPanel2;
	private JPanel jPanel3;
	private JPanel jPanel4;
	private JPanel jPanel5;
	private JPanel jPanel6;
	private JPanel jPanel7;
	private JPanel jPanel8;
	private JPanel jPanel9;
	private JPanel jPanel10;
	private JPanel jPanel11;
	private JPanel jPanel12;
	private JPanel jPanel13;
	private JLabel jLabel1;
	private JLabel jLabel2;
	private JLabel jLabel3;
	private JLabel jLabel4;
	private JLabel jLabel5;
	private JLabel jLabel6;
	private JComboBox jComboBox1;
	private JComboBox jComboBox2;
	private JTextField jTextField1;
	private JTable jTable1;
	private JTabbedPane jTabbedPane1;
	private JScrollPane jScrollPane1;
	private JScrollPane jScrollPane2;
	private JButton jButton1;
	private JButton jButton2;
	private JButton jButton3;
	private JButton jButton4;
	private JButton jButton5;
	private JButton jButton6;
	private JButton jButton7;
	private JTextArea jTextArea1;
	private JTextArea jTextArea2;
	private JTextArea jTextArea3;
}
