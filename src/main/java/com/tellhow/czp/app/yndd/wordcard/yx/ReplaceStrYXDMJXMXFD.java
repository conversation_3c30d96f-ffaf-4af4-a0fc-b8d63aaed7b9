package com.tellhow.czp.app.yndd.wordcard.yx;

import java.util.List;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYXDMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("玉溪单母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			if(curDev.getPowerVoltGrade() < station.getPowerVoltGrade()){//负荷侧
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				
				if(curDev.getPowerVoltGrade() == 10){
					replaceStr += "玉溪配调@核实"+stationName+deviceName+"具备送电条件/r/n";
				}
				
				String knifeContent1 = CommonFunction.get3KnifeContent(mlkgList, stationName, "合上");
				String knifeContent2 = CommonFunction.get3KnifeContent(zbkgList, stationName, "合上");

				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					boolean iszbkgcd = false;
					boolean ismlkgcd = false;

					if(curDev.getDeviceStatus().equals("0")){
						String powerContent = "";
						
						for(PowerDevice dev : zbkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								iszbkgcd = true;
								powerContent = "，用"+CZPService.getService().getDevNum(dev)+"断路器充电";
							}
						}

						for(PowerDevice dev : mlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								ismlkgcd = true;
								powerContent = "，用"+CZPService.getService().getDevNum(dev)+"断路器充电";
							}
						}
						
						if(mlkgList.size() == 0){
							powerContent = "";
						}
						
						replaceStr += stationName+"@将"+deviceName+"由冷备用转运行"+powerContent+"/r/n";
					}else{
						replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
					}
					
					if(iszbkgcd){
						replaceStr += knifeContent1;
					}

					if(ismlkgcd){
						replaceStr += knifeContent2;
					}
				}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
					String powerContent = "";
					
					for(PowerDevice dev : zbkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							powerContent = "，用"+CZPService.getService().getDevNum(dev)+"断路器充电";
						}
					}

					for(PowerDevice dev : mlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							powerContent = "，用"+CZPService.getService().getDevNum(dev)+"断路器充电";
						}
					}
					
					if(mlkgList.size() == 0){
						powerContent = "";
					}
					
					replaceStr += stationName+"@将"+deviceName+"由热备用转运行"+powerContent+"/r/n";
				}
				
				for(PowerDevice dev : zbkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						replaceStr += "按当前运行方式投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				if(curDev.getPowerVoltGrade() == 10){
					replaceStr += "玉溪配调@通知"+stationName+deviceName+"已处运行/r/n";
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}
}
