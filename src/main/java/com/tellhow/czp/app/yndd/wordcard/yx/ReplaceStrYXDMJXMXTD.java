package com.tellhow.czp.app.yndd.wordcard.yx;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYXDMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("玉溪单母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			
			if(curDev.getPowerVoltGrade() < station.getPowerVoltGrade()){//负荷侧
				if(curDev.getPowerVoltGrade() == 10){
					replaceStr += "玉溪配调@核实"+stationName+deviceName+"可以停电/r/n";
				}else if(curDev.getPowerVoltGrade() == 35){
					List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					for(PowerDevice dev  : xlkgList){
						if(!dev.getPowerDeviceName().contains("备用")){
							if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
								String status = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
								status = RuleExeUtil.getStatusNew(dev.getDeviceType(), status);
								replaceStr += "核实"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
							}
						}
					}
				}
				
				if(curDev.getPowerStationID().equals("SS-418")){//玉溪地调220kV寒武变
					replaceStr = stationName+"@核实站用电已倒由10kV#2站用变供电正常/r/n";
				}
				
				replaceStr += CommonFunction.getZybDrCheckContent(zbkgList);
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += stationName+"@退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				if(curDev.getDeviceStatus().equals("1")){
					replaceStr += stationName+"@将"+deviceName+"由运行转热备用/r/n";
				}else if(curDev.getDeviceStatus().equals("2")){
					replaceStr += stationName+"@将"+deviceName+"由运行转冷备用/r/n";
				}
				
				if(curDev.getPowerVoltGrade() == 10){
					replaceStr += "玉溪配调@通知"+stationName+deviceName+"已处冷备用/r/n";
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}
}
