package com.tellhow.czp.app.yndd.wordcard.lc;


import java.util.Collections;
import java.util.List;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLC;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLCKGDM implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("临沧开关倒母".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			String curMxName = "";
			String otherMxName = "";
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

			for(PowerDevice dev : dzList){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
					
					if(dev.getDeviceStatus().equals("0")){
						for(PowerDevice mx : mxList){
							otherMxName = CZPService.getService().getDevName(mx);
							break;
						}
					}else if(dev.getDeviceStatus().equals("1")){
						for(PowerDevice mx : mxList){
							curMxName = CZPService.getService().getDevName(mx);
							break;
						}
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("1")){
				if(CommonFunctionLC.ifSwitchControlLC(curDev)&&CommonFunctionLC.ifSwitchSeparateControlLC(curDev)){
					replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
					
					for(PowerDevice dev : mlkgList){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
					}
					
					replaceStr += "临沧地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					replaceStr += getKnifeOffCheckContent(dzList, stationName);
					
					replaceStr += "临沧地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					replaceStr += getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@将"+deviceName+"由"+curMxName+"热备用倒至"+otherMxName+"热备用/r/n";
				}
			}else if(curDev.getDeviceStatus().equals("0")){
				if(CommonFunctionLC.ifSwitchControlLC(curDev)&&CommonFunctionLC.ifSwitchSeparateControlLC(curDev)){
					replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
					
					for(PowerDevice dev : mlkgList){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
					}
					
					replaceStr += "临沧地调@执行"+stationName+deviceName+"由"+curMxName+"运行倒至"+otherMxName+"运行程序操作/r/n";
				
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
						}
					}
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
						}
					}
					
					for(PowerDevice dev : mlkgList){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
					}
					
					replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
					
				}else{
					replaceStr += stationName+"@将"+deviceName+"由"+curMxName+"运行倒至"+otherMxName+"运行/r/n";
				}
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
	public String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
			}
		}
		
		return replaceStr;
	}
}
