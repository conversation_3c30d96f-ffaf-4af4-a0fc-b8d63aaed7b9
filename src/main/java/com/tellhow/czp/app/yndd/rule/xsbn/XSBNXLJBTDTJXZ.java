package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.yndd.view.EquipCheckChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class XSBNXLJBTDTJXZ implements RulebaseInf {
	public static List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
	
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		
		/*chooseEquips.clear();
		
		String begin = CBSystemConstants.getCurRBM().getBeginStatus();
		String end = CBSystemConstants.getCurRBM().getEndState();
		
		String showMessage = "";
		
		if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
			showMessage = "请选择线路是否具备停电条件";
		}
		
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
	    List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
	    
		PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());

		if(sourceLineTrans == null){
			return false;
	    }
		
		lineList.addAll(loadLineTrans);
		lineList.add(sourceLineTrans);
	    
	    for (Iterator iterator = lineList.iterator(); iterator.hasNext();) {
			PowerDevice dev=(PowerDevice)iterator.next();
			
			if(dev.getPowerStationName().contains("虚拟站")){
				iterator.remove();
			}
		}
	    
	    EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, lineList , showMessage);
	    chooseEquips=ecc.getChooseEquip();
		
	    if(ecc.isCancel()){
	    	return false;
	    }*/
	    
		return true;
	}

}
