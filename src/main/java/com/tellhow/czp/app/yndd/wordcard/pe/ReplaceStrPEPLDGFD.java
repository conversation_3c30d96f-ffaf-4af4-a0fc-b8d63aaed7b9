package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.lj.LJGCBHDialog;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEPLDGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱旁路代供复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> plkgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						plkgList.add(dev);
					}
				}
			}
			
			if(RuleExeUtil.isDeviceHadStatus(curDev, "2", "1")){
				replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
			}
			
			String mxName = "";
			
			List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
			
			for(PowerDevice mx : tempmxList){
				mxName = "于"+CZPService.getService().getDevName(mx);
				break;
			}
			
			replaceStr += stationName+"@核实"+deviceName+"热备用"+mxName+"/r/n";
			replaceStr += stationName+"@核实"+deviceName+"重合闸退出/r/n";
			replaceStr += stationName+"@遥控用"+deviceName+"同期合环/r/n";
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev);
				replaceStr += stationName+"@断开"+plkgName+"/r/n";
			}
			
			replaceStr += stationName+"@投入"+deviceName+"重合闸/r/n";
			
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			
			for(PowerDevice line : lineList){
				List<PowerDevice> pldzList = RuleExeUtil.getDeviceList(line, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifePL,"",true, true, true, true);
				
				for(PowerDevice pldz : pldzList){
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(pldz)+"/r/n";
				}
			}
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev);
				replaceStr += stationName+"@退出"+plkgName+"重合闸/r/n";
			}
		}
		
		return replaceStr;
	}

}
