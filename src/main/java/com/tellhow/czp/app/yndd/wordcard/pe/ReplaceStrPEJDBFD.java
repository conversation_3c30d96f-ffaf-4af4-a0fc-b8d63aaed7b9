package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEJDBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱接地变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);
			String jdbName = "";

			replaceStr += stationName+"@"+"核实普洱供电局-XXX号检修申请工作已终结，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，二次装置正常投入，设备具备带电条件/r/n";
			
			for(PowerDevice dev : kgList){
				String deviceName = CZPService.getService().getDevName(dev);
				
				if(deviceName.contains("接地变")){
					jdbName = deviceName.substring(0, deviceName.indexOf("接地变")+3);
				}
				
				replaceStr += stationName+"@"+jdbName+"由冷备用转热备用/r/n";
				replaceStr += "普洱地调@遥控合上"+stationName+deviceName+"/r/n";
			}
		}
		
		return replaceStr;
	}

}
