package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTree;
import javax.swing.plaf.basic.BasicArrowButton;
import javax.swing.plaf.basic.BasicComboBoxUI;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeCellRenderer;
import javax.swing.tree.TreeModel;
import javax.swing.tree.TreePath;

import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.czp.util.GUIUtil;
import com.tellhow.czp.widget.OperateTicketPubTree;
import com.tellhow.graphicframework.basic.DefaultSimpleNode;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

/**
 * 变电站树列表面板
 * <AUTHOR>
 */
public class TransTreeWidget extends Widget {
	private JPanel panel=new JPanel();
	private JScrollPane sp_Trans;
	private JPopupMenu treePopupMenu;
	private JMenuItem searchMenuItem;
	private String lastSearchText = null;
	private JTree transTree;
	private List list = null;
	private String transsql="";
	
	private JLabel label;
	private JPanel searchPanel;
	final JComboBox jComboBox = new JAutoCompleteComboBox();
	public TransTreeWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		
		OperateTicketPubTree ottl = new OperateTicketPubTree(3);
		String orgaID = CBSystemConstants.getUser().getOrganID()==null?"":CBSystemConstants.getUser().getOrganID();
		transsql = CZPImpl.getPropertyValue("StationTreeSql").replace("[organ]", orgaID).replace("[areano]", CBSystemConstants.unitCode);
		
		if(transsql == null)
			transsql = OPEService.getService().TransTreeWidgetSql();
		transsql=transsql.replaceAll("equip\\.", CBSystemConstants.equipUser);
        list = ottl.getList(transsql, 1);
        transTree = ottl.buildLineTree("厂站", list, false);//由sql动态构建树
        
        transTree.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    clickTreeNode(simplePath);
                    //缓存解决方法（暂定）
                    
                    JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
                    int n=tabbedPane.getComponentCount();
                    if(n>8){
                    	for(int i=0;i<n;i++){
	            			SVGCanvasPanel selpanel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
	            			boolean isdestry = selpanel.isIsdestry();
	            			if(isdestry==false){
		            			final SVGCanvas canvas = selpanel.getSvgCanvas();
		            		    new Thread(new Runnable() {
		        		        	public void run() {
		        		        		canvas.destroyCanvas();
		        		        		System.gc();
		        		        	}
		    			 	    }).start();
		            		    selpanel.setIsdestry(true);
		            		    break;
	            			}
                    	}
                    }
                }
            }
            
            public void mouseReleased(MouseEvent e) {
            	TreePath path = transTree.getPathForLocation(e.getX(), e.getY());
				if (path == null) {
					if (e.getButton() == 3)
						treePopupMenu.show(transTree, e.getX(), e.getY());
					return;
				}
            }
        });
        
        transTree.setRootVisible(false);
        transTree.setToggleClickCount(1);
        sp_Trans = new JScrollPane();
        sp_Trans.setViewportView(transTree);
        searchMenuItem = new JMenuItem("查找");
        searchMenuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				String searchText = JOptionPane.showInputDialog(SystemConstants.getMainFrame(), "输入要查找的厂站名称", lastSearchText);
				if (searchText == null)
					return;
				else if (searchText.equals("")) {
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "查询条件不能为空！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
					return;
				}
				lastSearchText = searchText;
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
				}
			}
		});
        
        treePopupMenu = new JPopupMenu();
        treePopupMenu.add(searchMenuItem);
        
        panel.setLayout(new BorderLayout());
        panel.add(sp_Trans,BorderLayout.CENTER);
        addSearchItem();
        setleafIcon();
	}
	
	public void refresh() {
		OperateTicketPubTree ottl = new OperateTicketPubTree(3);
		String orgaID = CBSystemConstants.getUser().getOrganID()==null?"":CBSystemConstants.getUser().getOrganID();
		//临时取权限，只适合一人一种权限的情况
		String role = CBSystemConstants.roleCode;
		if(role.equals("")) {
			List rolelist=DBManager.queryForList("select rolecode from "+CBSystemConstants.opcardUser+"T_a_userrole a where a.userid='"+CBSystemConstants.getUser().getUserID()+"' order by rolecode");
			Map map=new HashMap();
			if(rolelist.size()>0){
				map=(Map) rolelist.get(0);
				role=StringUtils.ObjToString(map.get("rolecode"));
			}
		}
		if(orgaID.equals("")){
			if(Integer.parseInt(CBSystemConstants.unitCode)>0)
			orgaID = DBManager.queryForString("select organid from "+CBSystemConstants.platformUser+"T_TBP_ORGAN where AREANO = '"+CBSystemConstants.unitCode+"';");
		}
		if(CBSystemConstants.opCode.equals("1") && role.equals("2") && CZPImpl.getPropertyValue("JKStationTreeSql")!=null){
			transsql = CZPImpl.getPropertyValue("JKStationTreeSql").replace("[organ]", orgaID).replace("[areano]", CBSystemConstants.unitCode);
		}else{
			if(CBSystemConstants.unitCode.equals("-1"))
				transsql = CZPImpl.getPropertyValue("StationTreeSql1")==null?CZPImpl.getPropertyValue("StationTreeSql").replace("[organ]", orgaID):CZPImpl.getPropertyValue("StationTreeSql1");
			else
				transsql = CZPImpl.getPropertyValue("StationTreeSql").replace("[organ]", orgaID).replace("[areano]", CBSystemConstants.unitCode);
		}
		
		if(transsql == null)
			transsql = OPEService.getService().TransTreeWidgetSql();
		transsql=transsql.replaceAll("equip\\.", CBSystemConstants.equipUser);
        list = ottl.getList(transsql, 1);
        transTree = ottl.buildLineTree("厂站", list, false);//由sql动态构建树
        sp_Trans.setViewportView(transTree);
        transTree.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    clickTreeNode(simplePath);
                    //缓存解决方法（暂定）
                    
                    JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
                    int n=tabbedPane.getComponentCount();
                    if(n>8){
                    	for(int i=0;i<n;i++){
	            			SVGCanvasPanel selpanel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
	            			boolean isdestry = selpanel.isIsdestry();
	            			if(isdestry==false){
		            			final SVGCanvas canvas = selpanel.getSvgCanvas();
		            		    new Thread(new Runnable() {
		        		        	public void run() {
		        		        		canvas.destroyCanvas();
		        		        		System.gc();
		        		        	}
		    			 	    }).start();
		            		    selpanel.setIsdestry(true);
		            		    break;
	            			}
                    	}
                    }
                }
            }
            
            public void mouseReleased(MouseEvent e) {
            	TreePath path = transTree.getPathForLocation(e.getX(), e.getY());
				if (path == null) {
					if (e.getButton() == 3)
						treePopupMenu.show(transTree, e.getX(), e.getY());
					return;
				}
            }
        });
        transTree.setRootVisible(false);
        transTree.setToggleClickCount(1);
        searchMenuItem = new JMenuItem("查找");
        searchMenuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				String searchText = JOptionPane.showInputDialog(SystemConstants.getMainFrame(), "输入要查找的厂站名称", lastSearchText);
				if (searchText == null)
					return;
				else if (searchText.equals("")) {
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "查询条件不能为空！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
					return;
				}
				lastSearchText = searchText;
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
				}
			}
		});
        treePopupMenu = new JPopupMenu();
        treePopupMenu.add(searchMenuItem);
        panel.setLayout(new BorderLayout());
        panel.add(sp_Trans,BorderLayout.CENTER);
        addSearchItem();
        setleafIcon();
	}
	
	private void clickTreeNode(TreePath simplePath) {
		if(TempTicket.getTempTicket()==null){
			JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
			splitPane.setDividerLocation(1.0);
		}
		if(simplePath==null){
			return;
		}
		DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
        if (lastNode.isLeaf()) {
            DefaultSimpleNode dsn = (DefaultSimpleNode) lastNode.getUserObject();
            String filePath = "";
            String fileName = "";
            String stationID = "";
            String stationName = "";
            if(list == null) {
            	filePath = dsn.getItemCode();
                fileName = dsn.getItemName();
                stationName = dsn.getItemName().substring(0,dsn.getItemName().indexOf("."));
            }
            else {
                List<SVGFile> fileList = SystemConstants.getSVGFileByStationID(dsn.getItemCode());
            	if(fileList.size()!=1){
            		 CZPService.getService().filterMap(fileList, dsn.getItemCode());
				}
                if(fileList.size() == 0) {
                	if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive()){
                		jComboBox.setEnabled(false);
                		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
                		jComboBox.setEnabled(true);
                		jComboBox.setSelectedIndex(-1);
                	}else{
                		jComboBox.setEnabled(false);
                		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在" + dsn.getItemName() + "一次接线图！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
                		jComboBox.setEnabled(true);
                		jComboBox.setSelectedIndex(-1);
                	}
                	return;
                }
                else if(fileList.size() == 1) {
                	filePath = fileList.get(0).getFilePath();
                    fileName = fileList.get(0).getFileName();
                    jComboBox.setSelectedIndex(-1);
                }
                else {
                	Object[] options = fileList.toArray(); 
                	jComboBox.setEnabled(false);
                	int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);         	
                	jComboBox.setEnabled(true);     
                	jComboBox.setSelectedIndex(-1);
                	if(i == -1)
                		return;
                	filePath = fileList.get(i).getFilePath();
                    fileName = fileList.get(i).getFileName();
                }
                stationID = dsn.getItemCode();
                stationName = dsn.getItemName();
            }
            //8缓存
            SvgP svgp=new SvgP(stationID, stationName, fileName, filePath);
            CBSystemConstants.svgps.put(stationID, svgp);
            JTabbedPane tabbedPane = SystemConstants.getGuiBuilder().getSVGJTabbedPane();
    		int n=tabbedPane.getComponentCount();
    		for(int i=0;i<n;i++){
    			SVGCanvasPanel sel = (SVGCanvasPanel)tabbedPane.getComponentAt(i);
				String station=sel.getStationID();
				boolean isdestry=true;//sel.isIsdestry();
				if(station.equals(stationID)&&isdestry==true){
					File svgMapFile = new File(filePath);
					sel.loadSvgFile(svgMapFile);
					sel.setIsdestry(false);
				}
    		}
    		//
			CreatePowerStationToplogy.createSVGPanel(stationID, stationName, fileName, filePath);
		}
	}
	
	private void setleafIcon() {
		Thread thread =new Thread(){
       	 public void run(){
       		 
       		 if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive()){
        		    try {
        		    	SystemConstants.threadLoadFile.join();
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
        		    DifferentIconTreeCellRender render = new DifferentIconTreeCellRender();
        		    transTree.setCellRenderer(render); 
        		    transTree.setRowHeight(18);//防止linux系统DifferentIconTreeCellRender类会产生部分树节点高度很大，影响后面节点显示问题
       		 }
       	 }
       };
       thread.start();
	   
	}
	//搜索栏的添加
	private void addSearchItem() {
		label = new JLabel("搜索厂站：");
		searchPanel = new JPanel();
		searchPanel.setBackground(Color.white);
		searchPanel.setLayout(new BorderLayout());
		searchPanel.add(label,BorderLayout.WEST);
//		JComboBox jComboBox = new JAutoCompleteComboBox();
		String sql = CZPImpl.getPropertyValue("StationSearchSql");
		if(sql == null)
			sql = OPEService.getService().TransTreeWidgetSql1();
		sql=sql.replaceAll("equip\\.", CBSystemConstants.equipUser);
		GUIUtil.fillComboBox(jComboBox, sql);
		jComboBox.setSelectedIndex(-1);
		searchPanel.add(jComboBox,BorderLayout.CENTER);
		panel.add(searchPanel,BorderLayout.NORTH);
		
		jComboBox.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				if(jComboBox.getSelectedItem()==null || jComboBox.getSelectedItem() instanceof java.lang.String){
					return;
				}
				String searchText = ((CodeNameModel) jComboBox.getSelectedItem()).getCode();
				Object root = transTree.getModel().getRoot();
				TreePath treePath = new TreePath(root);
				
				treePath = findInPath(transTree, treePath, searchText);
				if (treePath == null) {
					transTree.setSelectionRow(0);
					treePath = findInPath(transTree, transTree.getSelectionPath(), searchText);
				}
				if (treePath != null) {
					transTree.setSelectionPath(treePath);
					transTree.scrollPathToVisible(treePath);
					if(!jComboBox.getName().equals("selecting"))
						clickTreeNode(treePath);
				}
			}
		});
		
	}

	
	private TreePath findInPath(JTree tree, TreePath treePath, String str) {
		Object object = treePath.getLastPathComponent();

		DefaultMutableTreeNode node = (DefaultMutableTreeNode)object;
		if(node.getUserObject() == null)
			return null;
		String value = "";
		if(node.getUserObject() instanceof DefaultSimpleNode) {
			value = ((DefaultSimpleNode)node.getUserObject()).getItemCode();
		}
		
		if (value.toLowerCase().equals(str.toLowerCase()) ) {
			return treePath;
		}else {
			TreeModel model = tree.getModel();
			int n = model.getChildCount(object);
			int curRow = tree.getRowForPath(tree.getSelectionPath());
			for (int i = 0; i < n; i++) {
				Object child = model.getChild(object, i);
				TreePath path = treePath.pathByAddingChild(child);

				path = findInPath(tree, path, str);
				if (path != null && tree.getRowForPath(path) > curRow) {
					return path;
				}
			}
			return null;
		}
	}   		   

	public TransTreeWidget(Widget parent, String name, String preset)
			throws GUIException {
		super(parent, name, preset);
	}

	@Override
	public Component getWidget() {
		return panel;
	}
}

