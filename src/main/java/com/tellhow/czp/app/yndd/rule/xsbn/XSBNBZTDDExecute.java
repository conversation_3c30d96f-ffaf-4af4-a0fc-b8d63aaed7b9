package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.view.EquipRadioChoose;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

public class XSBNBZTDDExecute implements RulebaseInf {
	public static List<PowerDevice> powerDeviceList = new ArrayList<PowerDevice>();//供电线路
	public static List<PowerDevice> powercutDeviceList = new ArrayList<PowerDevice>();//需要断开的线路
	public static List<PowerDevice> otherPowerDeviceList = new ArrayList<PowerDevice>();//需要断开的线路对侧开关

	@Override
	public boolean execute(RuleBaseMode rbm) {
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
	
		
		powerDeviceList.clear();
		powercutDeviceList.clear();
		otherPowerDeviceList.clear();
		
		PowerDevice hotkg = new PowerDevice();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

		for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = it2.next();
			
			if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")&&
					(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))){
				hotkg = dev;
			}
		}
		
		List<PowerDevice> list = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
		
		if(list.size()>0){
			List<PowerDevice> otherlist = RuleExeUtil.getLineOtherSideList(list.get(0));
			
			if(otherlist.size()==1){
				for(PowerDevice other : otherlist){
					List<PowerDevice> otherswlist = RuleExeUtil.getLinkedSwitch(other);
					
					for(PowerDevice othersw : otherswlist){
						if(othersw.getDeviceStatus().equals("0")){
							RuleExeUtil.deviceStatusExecute(othersw, othersw.getDeviceStatus(), "1");
							
							
							RuleExeUtil.deviceStatusExecute(hotkg, hotkg.getDeviceStatus(), "0");
							
							RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
							
							RuleExeUtil.deviceStatusExecute(othersw, othersw.getDeviceStatus(), "0");
							
							//转供线路
							List<PowerDevice> zglineList = RuleExeUtil.getDeviceList(hotkg, SystemConstants.InOutLine, SystemConstants.PowerTransformer,false, true, true);
							
							for(PowerDevice line : zglineList){
								powerDeviceList.add(line);
							}
							
							
							//停电线路
							List<PowerDevice> tdlineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer,false, true, true);
							
							for(PowerDevice line : tdlineList){
								powercutDeviceList.add(line);
							}
							
							otherPowerDeviceList.add(othersw);
						}
					}
				}
			}else if(otherlist.size()  >  1){
				List<PowerDevice> czlist = new ArrayList<PowerDevice>();
				
				for(PowerDevice other : otherlist){
					List<PowerDevice> otherswlist = RuleExeUtil.getLinkedSwitch(other);
					
					for(PowerDevice othersw : otherswlist){
						if(othersw.getDeviceStatus().equals("0")){
							czlist.add(othersw);
						}
					}
				}
				
				EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, czlist, "请选择需要断开的断路器");
				PowerDevice kgChangeOn = dcd.getChooseEquip();
				
				if(kgChangeOn==null){
					return false;
				}
				
				RuleExeUtil.deviceStatusExecute(kgChangeOn, kgChangeOn.getDeviceStatus(), "1");
				RuleExeUtil.deviceStatusExecute(hotkg, hotkg.getDeviceStatus(), "0");
				RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
				RuleExeUtil.deviceStatusExecute(kgChangeOn, kgChangeOn.getDeviceStatus(), "0");
				
				//转供线路
				List<PowerDevice> zglineList = RuleExeUtil.getDeviceList(hotkg, SystemConstants.InOutLine, SystemConstants.PowerTransformer,false, true, true);
				
				for(PowerDevice line : zglineList){
					powerDeviceList.add(line);
				}
				
				
				//停电线路
				List<PowerDevice> tdlineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer,false, true, true);
				
				for(PowerDevice line : tdlineList){
					powercutDeviceList.add(line);
				}
				
				otherPowerDeviceList.add(kgChangeOn);
			}else{
				ShowMessage.view("不存在对侧厂站！");
			}
		}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
			RuleExeUtil.deviceStatusExecute(hotkg, hotkg.getDeviceStatus(), "0");
			RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
		}
		
		return true;
	}
}
