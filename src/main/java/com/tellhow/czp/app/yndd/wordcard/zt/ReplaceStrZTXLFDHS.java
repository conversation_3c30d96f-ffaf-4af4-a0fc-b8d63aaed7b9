package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.zt.LineWorkSelectionDialog;
import com.tellhow.czp.app.yndd.rule.zt.StationWorkSelectionDialog;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTXLFDHS  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通线路复电核实".equals(tempStr)){
			String lineName = CZPService.getService().getDevName(curDev);

			for(Map<String,String> mapMap :  LineWorkSelectionDialog.deviceList){
				String result = mapMap.get("result");
				String stationName = mapMap.get("stationName");

				if(result.equals("是")){
					if(stationName.equals("直流融冰工作组")){
						replaceStr += stationName+"@核实"+lineName+"直流融冰相关工作已全部结束，安全措施已全部拆除，人员已全部撤离，融冰设备与系统可靠隔离，具备复电条件/r/n";
					}else{
						replaceStr += stationName+"@核实"+lineName+"线路相关工作已全部结束，安全措施已全部拆除，人员已全部撤离，具备复电条件/r/n";
					}
				}else{
					replaceStr += stationName+"@核实"+lineName+"停电期间，未开展相关工作，具备复电条件/r/n";
				}
			}
			
			for(Map<String,String> mapMap :  StationWorkSelectionDialog.deviceList){
				String result = mapMap.get("result");
				String stationName = mapMap.get("stationName");

				if(result.equals("是")){
					replaceStr += stationName+"@核实"+lineName+"站内相关工作已全部结束，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，人员已全部撤离，具备复电条件/r/n";
				}else if(result.equals("融冰")){
					replaceStr += stationName+"@核实"+lineName+"停电期间，站内未开展相关工作，融冰设备与系统可靠隔离，具备复电条件/r/n";
				}else{
					replaceStr += stationName+"@核实"+lineName+"停电期间，站内未开展相关工作，具备复电条件/r/n";
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
