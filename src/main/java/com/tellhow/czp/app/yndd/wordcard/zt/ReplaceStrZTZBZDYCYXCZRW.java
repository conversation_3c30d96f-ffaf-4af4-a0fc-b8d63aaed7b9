package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTZBZDYCYXCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通主变中低压侧运行操作任务".equals(tempStr)){
			String ddkgName = "";
			String zbgyckgName = "";
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev);

			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			for(PowerDevice dev : zbgyckgList){
				zbgyckgName = CZPService.getService().getDevName(dev);
			}
			
			for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
				ddkgName = CZPService.getService().getDevName(dev);
			}
			
			replaceStr += stationName+deviceName+"及以下电网由"+stationName+zbgyckgName+"供电倒由"+ddkgName+"供电";
		}
		
		return replaceStr;
	}

}
