package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEDMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱单母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

			for(PowerDevice dev : zbdyckgList){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
			}
			
			for(PowerDevice dev : zbzyckgList){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处于分闸位置/r/n";
			}
			
			if(TicketKindChoose.flag.equals("全部手动")){
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
			}else if(TicketKindChoose.flag.equals("全部程序化")){
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
					replaceStr += "普洱地调@执行"+stationName+deviceName+"由运行转热备用程序操作/r/n";
				}
				
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					replaceStr += "普洱地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					
					for(PowerDevice dev : zbgyckgList){
//						replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev);
					}
					
					for(PowerDevice dev : zbzyckgList){
//						replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev);
					}
					
					for(PowerDevice dev : zbdyckgList){
//						replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev);
					}
				}
			}else{
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
							replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
//							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev);
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
							replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
//							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev);
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						if(CommonFunctionPE.ifSwitchSeparateControl(dev)){
							replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
//							replaceStr += CommonFunctionPE.getKnifeOffCheckContent(dev);
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
