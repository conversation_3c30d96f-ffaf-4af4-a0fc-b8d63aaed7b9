package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrYXZBKDNQKGZT implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪主变扩大内桥接线开关状态".equals(tempStr)){
			
			List<PowerDevice>  highxlswList =RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true,true);
			
			List<PowerDevice>  hignVoltMlkgList = RuleExeUtil.getDeviceList(highxlswList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true,true);
			List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						hignVoltXlkgList.add(dev2);
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("2")){
					replaceStr = CZPService.getService().getDevNum(hignVoltMlkgList.get(0))+"断路器"+RuleExeUtil.getStatus(hignVoltMlkgList.get(0).getDeviceStatus())+"、"+CZPService.getService().getDevNum(highxlswList.get(0))+"断路器"+RuleExeUtil.getStatus(highxlswList.get(0).getDeviceStatus());
			}else{
				if(RuleExeUtil.getDeviceBeginStatus(hignVoltMlkgList.get(0)).equals("1")){
					replaceStr = "用"+CZPService.getService().getDevNum(hignVoltMlkgList.get(0))+"断路器充电";
				}else if(RuleExeUtil.getDeviceBeginStatus(highxlswList.get(0)).equals("1")){
					replaceStr = "用"+CZPService.getService().getDevNum(highxlswList.get(0))+"断路器充电";
				}
				
				if(RuleExeUtil.getDeviceBeginStatus(hignVoltMlkgList.get(0)).equals("0")){
					replaceStr += "，"+CZPService.getService().getDevNum(hignVoltMlkgList.get(0))+"热备用";
				}else if(RuleExeUtil.getDeviceBeginStatus(highxlswList.get(0)).equals("0")){
					replaceStr += "，"+CZPService.getService().getDevName(highxlswList.get(0))+"热备用";
				}
				
				if(RuleExeUtil.getDeviceBeginStatus(hignVoltMlkgList.get(0)).equals("2")
						&&RuleExeUtil.getDeviceBeginStatus(highxlswList.get(0)).equals("2")){
					replaceStr = "用"+CZPService.getService().getDevName(hignVoltMlkgList.get(0))+"充电，"+CZPService.getService().getDevName(highxlswList.get(0))+"热备用";
				}
			}
			
		}
		return replaceStr;
	}

}
