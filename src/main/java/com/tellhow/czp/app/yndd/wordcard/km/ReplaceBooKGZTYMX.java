package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

/** 
 *  开关在同一母线
 */
public class ReplaceBooKGZTYMX implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		List<PowerDevice> plkgMXList = new ArrayList<PowerDevice>();
		List<PowerDevice> curMXList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, null, true, true, true);
		
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, 
				SystemConstants.Switch, 
				CBSystemConstants.RunTypeSwitchPL,
				null, false, true, false, true);
		if(mlList != null && mlList.size()>0) {
			for(PowerDevice pdDev : mlList) {
				List<PowerDevice> tempList = RuleExeUtil.getDeviceList(pdDev, SystemConstants.MotherLine, null, true, true, true);
				plkgMXList.addAll(tempList);
			}
		}
		
		if(curMXList == null) {
			return false;
		}
		
		for(int i=0;i<curMXList.size();i++) {
			if(CBSystemConstants.RunTypeSideMother.equals(curMXList.get(i).getDeviceRunType())) {
				curMXList.remove(i);
			}
		}
		
		for(int i=0;i<plkgMXList.size();i++) {
			if(CBSystemConstants.RunTypeSideMother.equals(plkgMXList.get(i).getDeviceRunType())) {
				plkgMXList.remove(i);
			}
		}
		
		if(plkgMXList.size()>0 && curMXList.size()>0) {
			for(int i=0;i<plkgMXList.size();i++) {
				for(int j=0;j<curMXList.size();j++) {
					if(plkgMXList.get(i).getPowerDeviceID().equals(curMXList.get(j).getPowerDeviceID())) {
						return true;
					}
				}
			}
		}
		return false;
	}
}
