package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTSMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通双母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			String othermxName = "";
			List<PowerDevice> mlkgList =  new ArrayList<PowerDevice>();

			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					mlkgList.add(dev);
				}
				
				if (dev.getDeviceType().equals(SystemConstants.MotherLine)&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())&&dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						othermxName = CZPService.getService().getDevName(dev);
					}
				}
			}
			
			for(PowerDevice dev  : mlkgList){
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "落实"+CZPService.getService().getDevName(dev)+"处热备用状态/r/n";
						replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
					}
				}
			}
			
		 	List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
		 	List<PowerDevice> zbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			
		 	List<PowerDevice> yxswList = new ArrayList<PowerDevice>();
		 	List<PowerDevice> rbyswList = new ArrayList<PowerDevice>();

		 	List<PowerDevice> tempList = new ArrayList<PowerDevice>();
		 	
		 	tempList.addAll(xlkgList);
		 	tempList.addAll(zbkgList);
		 	
		 	for(PowerDevice dev : tempList){
		 		List<PowerDevice> dzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeMX, "", true, true, true, true);
		 		
		 		for(PowerDevice dz : dzList){
		 			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
		 				List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
		 				
		 				for(PowerDevice mx : mxList){
		 					if(mx.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
		 						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
		 							yxswList.add(dev);
		 						}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
		 							rbyswList.add(dev);
		 						}
		 					}
		 				}
		 			}
		 		}
		 	}
		 	
		 	if(yxswList.size()>1){
				replaceStr += "将"+deviceName+"上运行的所有断路器倒至"+othermxName+"上运行/r/n";
		 	}else{
		 		for(PowerDevice dev : yxswList){
			 		replaceStr += "将"+CZPService.getService().getDevName(dev)+"由"+deviceName+"运行倒至"+othermxName+"运行/r/n";
		 		}
		 	}
		 	
		 	for(PowerDevice dev : rbyswList){
		 		replaceStr += "将"+CZPService.getService().getDevName(dev)+"由"+deviceName+"热备用倒至"+othermxName+"热备用/r/n";
	 		}
		 	
			for(PowerDevice dev  : mlkgList){
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		 	
			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
				replaceStr += "将"+deviceName+"由热备用转冷备用/r/n";
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
