package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dq.BZTDDExecute;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQBZTDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆备自投调电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
				List<PowerDevice>  dycmxList = new ArrayList<PowerDevice>();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
				
				for (Iterator<PowerDevice> itor = mapStationDevice.values().iterator(); itor.hasNext();) {
					PowerDevice dev = itor.next();
					
					if(dev.getPowerVoltGrade() == 10&&dev.getDeviceType().equals(SystemConstants.MotherLine)){
						dycmxList.add(dev);
					}
				}
				
				RuleExeUtil.swapDeviceList(dycmxList);
				
				if(dycmxList.size()>0){
					replaceStr += "迪庆配调@确认"+stationName+CZPService.getService().getDevName(dycmxList)+"具备停电条件/r/n";
				}
				
				replaceStr += "确认"+(int)curDev.getPowerVoltGrade()+"kV备自投装置运行正常/r/n";
				
				for(PowerDevice dev : BZTDDExecute.otherPowerDeviceList){
					replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
				
				replaceStr += "确认"+(int)curDev.getPowerVoltGrade()+"kV备自投装置动作正确/r/n";
				
				for(PowerDevice dev : BZTDDExecute.otherPowerDeviceList){
					replaceStr += "迪庆地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
				
				if(dycmxList.size()>0){
					replaceStr += "迪庆配调@通知"+stationName+CZPService.getService().getDevName(dycmxList)+"已恢复供电/r/n";
				}
			}else{
				
			}
		}
		
		return replaceStr;
	}

}
