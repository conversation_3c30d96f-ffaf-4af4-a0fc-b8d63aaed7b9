package com.tellhow.czp.app.yndd.rule.yx;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-9-19 上午11:13:13 
 */
public class SwitchThreeSwitchSeparateExecute implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
			List<PowerDevice> list =  RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, "", true, true, false);
			
			for(PowerDevice dev : list){
				if(rbm.getBeginStatus().equals("0")&&rbm.getEndState().equals("1")){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}else if(rbm.getBeginStatus().equals("1")&&rbm.getEndState().equals("0")){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
		}
		
		return true;
	}
}
