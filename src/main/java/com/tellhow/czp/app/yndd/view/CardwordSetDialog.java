package com.tellhow.czp.app.yndd.view;


import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;



public class CardwordSetDialog extends javax.swing.JDialog {
	private DefaultTableModel dTableModel;
	private JPanel topPanel;//查询条件及按钮面板
	private JPanel mainPanel;//信息面板
	private JTextField searchText;//查询框
	private JButton searchButton;//查询按钮
	private JButton addButton;//新增按钮
	private JButton updateButton;//修改按钮
	private JButton delButton;//删除按钮
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTableInfo;//信息列表



	public CardwordSetDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("术语模板维护");
		setLocationCenter();
		initTable("");
	}
	
	/**
	 * 初始化表格  传入参数关键字
	 */
	public void initTable(String gjz) {
		dTableModel.setRowCount(0);
		
		String sql="SELECT ID, MODELDESC, BEGINSTATUS, ENDSTATUS, OPERATION, ORDERID FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL ";
		 if(!gjz.equals("")){
			 sql+=" WHERE MODELDESC LIKE '%"+gjz+"%' OR OPERATION LIKE '%"+gjz+"%'";
			 }	
		 sql+="ORDER BY ORDERID";
		 List<Map<String,String>> results=DBManager.queryForList(sql);

		 Map<String,String> temp=new HashMap<String,String>();
		 for (int i = 0; i < results.size(); i++) {
			 temp= results.get(i);
			 String id=StringUtils.ObjToString(temp.get("ID"));
			 String modeldesc=StringUtils.ObjToString(temp.get("MODELDESC"));
			 String beginstatus=StringUtils.ObjToString(temp.get("BEGINSTATUS"));
			 String endstatus=StringUtils.ObjToString(temp.get("ENDSTATUS"));
			 String operation=StringUtils.ObjToString(temp.get("OPERATION"));
			 String orderid=StringUtils.ObjToString(temp.get("ORDERID"));

			 Object[] rowData = {id,i+1,modeldesc,beginstatus,endstatus,operation,orderid};
			 dTableModel.addRow(rowData);
		 }

		jTableInfo.setModel(dTableModel);
		DefaultTableCellRenderer r  = new  DefaultTableCellRenderer();   
		r.setHorizontalAlignment(JTextField.CENTER);   
		jTableInfo.getColumn("序号").setCellRenderer(r);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}


	private void initComponents() {
		getContentPane().setLayout(new BorderLayout());
		this.setSize(900, 480);
		topPanel =new JPanel();
		topPanel.setPreferredSize(new Dimension(0,45));
		getContentPane().add(topPanel,BorderLayout.NORTH);
		mainPanel =new JPanel();
		getContentPane().add(mainPanel,BorderLayout.CENTER);
		
		JLabel label1 = new JLabel("关键字:");
		JLabel label2 = new JLabel("");//增加空位用
		label2.setPreferredSize(new Dimension(60,0));
		topPanel.setLayout(new FlowLayout(FlowLayout.CENTER,10,10));
		searchText =new JTextField();
		searchText.setPreferredSize(new Dimension(200,20));
		searchButton =new JButton("查询");
		searchButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				searchButtonActionPerformed(evt);
			}
		});
		addButton =new JButton("新增");
		addButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				addButtonActionPerformed(evt);
			}
		});
		updateButton =new JButton("修改");
		updateButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				updateButtonActionPerformed(evt);
			}
		});
		delButton =new JButton("删除");
		delButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				delButtonActionPerformed(evt);
			}
		});
		
		topPanel.add(label1);
		topPanel.add(searchText);
		topPanel.add(searchButton);
		topPanel.add(label2);
		topPanel.add(addButton);
		topPanel.add(updateButton);
		topPanel.add(delButton);
		
		dTableModel = new DefaultTableModel(null,new String[] { "ID","序号","模型描述","初始状态","目标状态","操作","排序"}){
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		jTableInfo = new JTable();
		jTableInfo.setModel(dTableModel);
//		jTableInfo.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
		
		jTableInfo.getColumnModel().getColumn(0).setMinWidth(0);
		jTableInfo.getColumnModel().getColumn(0).setMaxWidth(0);
		jTableInfo.getColumnModel().getColumn(1).setPreferredWidth(60);
		jTableInfo.getColumnModel().getColumn(1).setMaxWidth(80);
		jTableInfo.getColumnModel().getColumn(3).setPreferredWidth(80);
		jTableInfo.getColumnModel().getColumn(3).setMaxWidth(100);
		jTableInfo.getColumnModel().getColumn(4).setPreferredWidth(80);
		jTableInfo.getColumnModel().getColumn(4).setMaxWidth(100);
		jTableInfo.getColumnModel().getColumn(5).setPreferredWidth(100);
		jTableInfo.getColumnModel().getColumn(5).setMaxWidth(150);
		jTableInfo.getColumnModel().getColumn(6).setPreferredWidth(60);
		jTableInfo.getColumnModel().getColumn(6).setMaxWidth(80);
		jTableInfo.setRowHeight(26);
		jScrollPane1 = new JScrollPane(jTableInfo);
		jScrollPane1.setPreferredSize(new Dimension(800, 370));
		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));
		mainPanel.add(jScrollPane1,BorderLayout.CENTER);
	}

	//修改
	private void updateButtonActionPerformed(java.awt.event.ActionEvent evt) {
		int[] selectRows = jTableInfo.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要修改的记录！");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view(this, "只能选择一条记录！");
			return;
		}
		String dfsid = this.jTableInfo.getValueAt(selectRows[0], 0).toString();
		CardwordSetEditDialog aud = new CardwordSetEditDialog(this, true,dfsid);
		aud.setVisible(true);
		this.initTable(searchText.getText().trim());
	}

	//新增
	private void addButtonActionPerformed(java.awt.event.ActionEvent evt) {
		CardwordSetEditDialog aud = new CardwordSetEditDialog(this, true,"");
		aud.setVisible(true);
		this.initTable(searchText.getText().trim());
	}
	//查询
	private void searchButtonActionPerformed(java.awt.event.ActionEvent evt) {
		this.initTable(searchText.getText().trim());
	}

	//删除
	private void delButtonActionPerformed(java.awt.event.ActionEvent evt) {
		int[] selectRows = jTableInfo.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view(this, "请选择需要删除的记录！");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view(this, "只能选择一条记录！");
			return;
		}
		String dfsid = this.jTableInfo.getValueAt(selectRows[0], 0).toString();
		
		int isOk=JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "是否确定删除该维护记录？",SystemConstants.SYSTEM_TITLE, JOptionPane.YES_NO_OPTION);
		if(isOk==JOptionPane.NO_OPTION){
			return;
		}
		
		String sqlDel="DELETE FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL WHERE ID ='"+dfsid+"' ";
	    DBManager.execute(sqlDel);
		
		this.initTable(searchText.getText().trim());
	}
}
