package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.GridBagConstraints;
import java.awt.Insets;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.List;
import java.util.Map;

import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.app.yndd.tool.CommonFunctionKM;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

import javax.swing.JToolBar;
import javax.swing.JButton;



public class CheckResultDialog extends javax.swing.JDialog {
	private DefaultTableModel dTableModel;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTableInfo;//信息列表
	public boolean isCancel = false;
	private String content = "";
	
	public CheckResultDialog(java.awt.Frame parent, boolean modal,List<Map<String, String>> resultList) {
		super(parent, modal);
		
		content = resultList.toString();
		
		initComponents();
		this.setTitle("校核结果展示");
		setLocationCenter();
		initTable(resultList);
	}

	/**
	 * 初始化表格  传入参数关键字
	 */
	public void initTable(List<Map<String, String>> resultList) {
		dTableModel.setRowCount(0);
		
		 for (int i = 0; i < resultList.size(); i++) {
			 Map<String,String> temp = resultList.get(i);
			 String caozuozhiling = StringUtils.ObjToString(temp.get("caozuozhiling1"));
			 String changzhan = StringUtils.ObjToString(temp.get("changzhan"));
			 String remessage = StringUtils.ObjToString(temp.get("remessage"));
			 String xh = StringUtils.ObjToString(i+1);

			 Object[] rowData = {xh,changzhan,caozuozhiling,remessage};
			 dTableModel.addRow(rowData);
		 }

		jTableInfo.setModel(dTableModel);
		DefaultTableCellRenderer  r  =  new  DefaultTableCellRenderer();   
		r.setHorizontalAlignment(JTextField.CENTER);   
		jTableInfo.getColumn("序号").setCellRenderer(r);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}


	private void initComponents() {
		getContentPane().setLayout(new BorderLayout());
		this.setSize(700, 480);
		
		JLabel label2 = new JLabel("");//增加空位用
		label2.setPreferredSize(new Dimension(60,0));
		
		dTableModel = new DefaultTableModel(null,new String[] {"序号","操作单位","操作内容","校核结果"}){
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		
		jTableInfo = new JTable();
		jTableInfo.setModel(dTableModel);
		
		SetJTableProtery set = new SetJTableProtery();
		set.setLineWrap(jTableInfo);
		
		jTableInfo.getColumnModel().getColumn(0).setMinWidth(50);
		jTableInfo.getColumnModel().getColumn(0).setMaxWidth(100);
		jTableInfo.getColumnModel().getColumn(1).setPreferredWidth(100);
		jTableInfo.getColumnModel().getColumn(1).setMaxWidth(200);
		jTableInfo.getColumnModel().getColumn(2).setPreferredWidth(200);
		
		JPanel jPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 50, 10));
		getContentPane().add(jPanel, BorderLayout.SOUTH);
		
		JButton saveButton = new JButton("继续保存");
		saveButton.setForeground(Color.RED);
		jPanel.add(saveButton);
		
		JButton notSaveButton = new JButton("不保存");
		jPanel.add(notSaveButton);
		
		saveButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveButtonActionPerformed(evt);
			}
		});
		
		notSaveButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				notSaveButtonActionPerformed(evt);
			}
		});
		
		jTableInfo.setRowHeight(26);
		jScrollPane1 = new JScrollPane(jTableInfo);
		jScrollPane1.setPreferredSize(new Dimension(600, 370));
		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));
		getContentPane().add(jScrollPane1);
	}
	
	private void saveButtonActionPerformed(ActionEvent evt) {
		isCancel = true;
		
		CommonFunctionKM.insertRecode(CBSystemConstants.getUser().getUserName(), content, "输入");
		
		this.setVisible(false);
		this.dispose();
	}
	
	private void notSaveButtonActionPerformed(ActionEvent evt) {
		isCancel = false;
		this.setVisible(false);
		this.dispose();
	}
}
