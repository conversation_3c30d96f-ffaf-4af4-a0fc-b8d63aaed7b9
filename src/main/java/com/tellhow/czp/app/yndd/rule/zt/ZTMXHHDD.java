package com.tellhow.czp.app.yndd.rule.zt;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

public class ZTMXHHDD implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		
		List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(pd,null ,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC+","+CBSystemConstants.RunTypeSwitchML, "", false, false, false, false,true);
		
		for(PowerDevice dev : zbkgList){
			if(dev.getDeviceStatus().equals("1")){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
		}

		for(PowerDevice dev : zbkgList){
			if(dev.getDeviceStatus().equals("0")&&!RuleExeUtil.isDeviceChanged(dev)){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}
		}
		
		return true;
	}
}
