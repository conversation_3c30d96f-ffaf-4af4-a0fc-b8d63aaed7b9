package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr110T35kVDMMXCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("110T35kV单母母线操作".equals(tempStr)){
			CommonFunctionHH cf = new CommonFunctionHH();
			String endstatus = CBSystemConstants.getCurRBM().getEndState();
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			
			String stationName = CZPService.getService().getDevName(station); 
			
			replaceStr += cf.getHsdcnrStrReplace(curDev);
			
			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> fhczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> yxxlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> yxmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();//临时数组,使用完要clear
			List<PowerDevice> drdkkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, true, true);
			List<PowerDevice> allmxkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

			if(mlkgList.size()>0){
				for(PowerDevice mlkg : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(mlkg).equals("0")){
						yxmlkgList.add(mlkg);
					}
				}
			}
			
			if(xlkgList.size()>0){
				for(PowerDevice xlkg : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(xlkg).equals("0")){
						yxxlkgList.add(xlkg);
					}
				}
			}
			
			for(Iterator<PowerDevice> itor = xlkgList.iterator();itor.hasNext();){
				PowerDevice xlkg = itor.next();
				
				if(xlkg.getPowerDeviceName().contains("站用变")){
					itor.remove();
				}
			}
			
			List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice allmxkg : allmxkgList){
				if(allmxkg.getDeviceType().equals(SystemConstants.Switch)){
					if(allmxkg.getPowerDeviceName().contains("站用变")){
						zybkgList.add(allmxkg);
					}
				}
			}
			
			tempList.addAll(mlkgList);
			tempList.addAll(xlkgList);
			tempList.addAll(drdkkglist);
			tempList.addAll(zybkgList);
			
		    List<PowerDevice> hotdevList = new ArrayList<PowerDevice>();
		    List<PowerDevice> colddevList = new ArrayList<PowerDevice>();

		    for(PowerDevice dev :tempList){
		    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
		    		hotdevList.add(dev);
		    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
		    		colddevList.add(dev);
		    	}
		    }
		    
		    tempList.clear();
		    
		    if(hotdevList.size()>0){
		    	replaceStr += "核实"+ CZPService.getService().getDevName(hotdevList)+"热备用/r/n";
		    }
		    
		    if(colddevList.size()>0){
		    	replaceStr += "核实"+ CZPService.getService().getDevName(colddevList)+"冷备用/r/n";
		    }

		    List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		    
		    HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
		    
			replaceStr += cf.getXhxqStrReplace(zbList, "停电");
		    
			if(yxmlkgList.size()>0&&zbList.size()>1){
			    replaceStr += "退出35kV备自投装置/r/n";
			}
		    
			tempList.addAll(yxxlkgList);
			tempList.addAll(yxmlkgList);
			tempList.addAll(fhczbkgList);

		    if(tempList.size()>0){
		    	replaceStr += cf.getYcDkStrReplace(tempList, stationName);
		    }
		    
		    tempList.clear();
		    
		    if(endstatus.equals("1")){
		    	List<PowerDevice> xldzList = new ArrayList<PowerDevice>();
		    	
		    	for(PowerDevice xlkg : xlkgList){
					xldzList = RuleExeUtil.getDeviceList(xlkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL, "", false, true, true, true);
		    	}
		    	
		    	if(xldzList.size()>0){
			    	replaceStr += "核实"+CZPService.getService().getDevName(xldzList)+"在合闸位置/r/n";
		    	}
		    }
		    
		    if(endstatus.equals("2")){
		    	tempList.addAll(yxxlkgList);
		    	tempList.addAll(fhczbkgList);
		    	tempList.addAll(mlkgList);
			    
		    	replaceStr += cf.getCzMotherLineDevStrReplace(tempList, curDev, null, stationName, "由热备用转冷备用");
		    	
		    	tempList.clear();
		    	
		    	for(PowerDevice xlkg : xlkgList){
		    		tempList = RuleExeUtil.getDeviceList(xlkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXL, "", false, true, true, true);
		    	}
		    	
		    	if(tempList.size()>0){
			    	replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"在拉开位置/r/n";
		    	}
		    	
		    	tempList.clear();
		    	
		    	List<PowerDevice> curzbList = new ArrayList<PowerDevice>();
				    
			    if(fhczbkgList.size()>0){
					curzbList = RuleExeUtil.getDeviceList(fhczbkgList.get(0), SystemConstants.PowerTransformer, "", true, true, true);
			    }
			    
			    if(curzbList.size()>0){
				    replaceStr += "退出"+CZPService.getService().getDevName(curzbList)+"35kV侧后备保护动作跳主变三侧断路器/r/n";
			    }
		    }
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
