package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTZBHFYX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通主变恢复运行".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev);
					
			for(PowerDevice dev : CBSystemConstants.getSamepdlist()){
				RuleExeUtil.getPathAllByDevice(dev, curDev, "", "", true, false);
				
				PowerDevice otherstation = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				
				String otherstationName = CZPService.getService().getDevName(otherstation);
				
				replaceStr += "昭通地调@遥控断开"+otherstationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
			
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			for(PowerDevice dev : zbgyckgList){
				replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
			
			List<PowerDevice> zbzxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			for(PowerDevice dev : zbzxdjddzList){
				if(dev.getDeviceStatus().equals("1")){
					replaceStr += "核实"+deviceName+"中性点接地方式为间接接地，相关保护配合/r/n";
				}else if(dev.getDeviceStatus().equals("0")){
					replaceStr += "将"+deviceName+"中性点接地方式由直接接地改为间接接地，相关保护配合/r/n";
				}
			}
			
			double midvolt = RuleExeUtil.getTransformerVolByType(curDev, "middle");
			
			List<PowerDevice> zyckgList = new ArrayList<PowerDevice>();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
					if(midvolt==dev.getPowerVoltGrade()&&!dev.getPowerDeviceName().contains("备用")){
						zyckgList.add(dev);
					}
				}
			}
			
			EquipCheckChoose ec=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zyckgList , "请选择需要投入总跳闸出口功能的断路器");

			List<PowerDevice> chooseEquips=ec.getChooseEquip();
			
			for(PowerDevice dev : chooseEquips){
				replaceStr += "投入"+CZPService.getService().getDevName(dev)+"总跳闸出口功能/r/n";
			}
			
			EquipCheckChoose ec1=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zyckgList , "请选择需要切换保护定值区的断路器");

			List<PowerDevice> chooseEquips1=ec1.getChooseEquip();
			
			for(PowerDevice dev : chooseEquips1){
				replaceStr += "将"+CZPService.getService().getDevName(dev)+"保护定值区由X区切换至X区/r/n";
			}
		}
		
		return replaceStr;
	}

}
