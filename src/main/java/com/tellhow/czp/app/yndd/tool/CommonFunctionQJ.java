package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.qj.StationWorkSelectionDialog;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class CommonFunctionQJ {
    public static String orderContent = "";

    //地线
  	public static List<PowerDevice> groundWireList = new ArrayList<PowerDevice>();
  	
  	//合环
  	public static List<PowerDevice> closedLoopList = new ArrayList<PowerDevice>();
  	
  	//充电
  	public static List<PowerDevice> chargeDeviceList = new ArrayList<PowerDevice>();
    
  	//内桥主变停电充电开关
  	public static List<PowerDevice> chargeDeviceByNqZbTdList = new ArrayList<PowerDevice>();

    //内桥主变复电充电开关
  	public static List<PowerDevice> chargeDeviceByNqZbFdList = new ArrayList<PowerDevice>();
  	
	public static String getSwitchOffContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(station.getDeviceType().equals(SystemConstants.PowerFactory) || stationName.contains("电场")){
				replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getSwitchOnContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(station.getDeviceType().equals(SystemConstants.PowerFactory)  || stationName.contains("电场")){
				replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getSwitchRbyToLbyContent(PowerDevice dev,String stationName,PowerDevice station){//开关热备用转冷备用
		String replaceStr = "";
		if(CommonFunctionQJ.ifSwitchSeparateControl(dev) && !stationName.contains("电场")){
			replaceStr += "曲靖地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
		} else if (station.getDeviceType().equals(SystemConstants.PowerFactory)  || stationName.contains("电场") || stationName.contains("光伏") || stationName.contains("电厂")) {
			// 特殊接线，光伏电站以及风电场，存在断路器只有一侧隔离开关的情况，此时需要生成操作隔离开关的指令
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			if(dzList.size() == 1) {
				replaceStr += stationName + "@核实" + CZPService.getService().getDevName(dev) + "处断开位置/r/n";
				if (RuleExeUtil.getDeviceBeginStatus(dzList.get(0)).isEmpty()) {
					replaceStr += stationName + "@核实" + CZPService.getService().getDevName(dzList.get(0)) + "处断开位置/r/n";
				}else replaceStr += stationName + "@拉开" + CZPService.getService().getDevName(dzList.get(0)) + "/r/n";
			}else{
				replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
			}
		} else{
			replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getSwitchLbyToRbyContent(PowerDevice dev,String stationName,PowerDevice station){//开关冷备用转热备用
		String replaceStr = "";
		if(CommonFunctionQJ.ifSwitchSeparateControl(dev) && !stationName.contains("电场")){
			replaceStr += "曲靖地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
		} else if (station.getDeviceType().equals(SystemConstants.PowerFactory)  || stationName.contains("电场") || stationName.contains("光伏") || stationName.contains("电厂")) {
			// 特殊接线，光伏电站以及风电场，存在断路器只有一侧隔离开关的情况，此时需要生成操作隔离开关的指令
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			if (dzList.size() == 1) {
				replaceStr += stationName + "@合上" + CZPService.getService().getDevName(dzList.get(0)) + "/r/n";
			}else{
				replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
			}
		} else{
			replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getCdContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
				if(stationName.contains("电站")){
					replaceStr += stationName+"@合上"+stationName+CZPService.getService().getDevName(dev)+"对线路充电/r/n";
				}else{
					replaceStr += ddname+"@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对线路充电/r/n";
				}
			}
		
		return replaceStr;
	}
	
	public static String getCdContent(PowerDevice dev,String ddname,String stationName,String deviceName){
		String replaceStr = "";
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
				if(stationName.contains("电站")){
					replaceStr += stationName+"@合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
				}else{
					replaceStr += ddname+"@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
				}
			}
		
		return replaceStr;
	}
	
	public static String getCdOrHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";
		
		if(chargeDeviceList.contains(dev)){
			PowerDevice curDev = CBSystemConstants.getCurRBM().getPd();
			
			if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
				replaceStr = getCdContent(dev,ddname,stationName);
			}else{
				replaceStr = getCdContent(dev,ddname,stationName,CZPService.getService().getDevName(curDev));
			}
		}else if(closedLoopList.contains(dev)){
			replaceStr = getHhContent(dev,ddname,stationName);
		}else{
			replaceStr = ddname+"@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
//			if(dev.getPowerVoltGrade() > 35){
				if(stationName.contains("电站")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}
//			}else{
//				if(stationName.contains("电站")){
//					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"合环/r/n";
//				}else{
//					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环/r/n";
//				}
//			}
		}
		
		return replaceStr;
	}
	
	public static List<Map<String, String>> getStationZybList(PowerDevice curDev){
		String sql = "SELECT DEV_ID,STATION_ID,STATION_NAME, DEV_NAME, ZYB_NAME, ZYB_DZNAME, ZYB_DEVID FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+curDev.getPowerDeviceID()+"'";
		List<Map<String, String>> stationZybList = DBManager.queryForList(sql);
		
		return stationZybList; 
	}
	
	public static List<Map<String, String>> getStationLineList(PowerDevice curDev){
		List<Map<String, String>> stationLineList = new ArrayList<Map<String,String>>();
		
		String sql = "SELECT ID,LINE_ID, LINE_NAME,UNIT,VOLTAGE,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND, CHARGE_DEVICE "
				+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND "
						+ "WHERE ID = '"+curDev.getPowerDeviceID()+"')";
		
		stationLineList = DBManager.queryForList(sql);
		
		if(stationLineList.isEmpty()){
			sql = "SELECT ID,LINE_ID, LINE_NAME,UNIT,VOLTAGE,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND, CHARGE_DEVICE "
					+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_NAME = '"+CZPService.getService().getDevName(curDev)+"'";
			
			stationLineList = DBManager.queryForList(sql);
		}
		
		return stationLineList; 
	}
	
	public static List<PowerDevice> getTransformerKnife(PowerDevice zb,PowerDevice zbkg){
		List<PowerDevice> dztagList = new ArrayList<PowerDevice>();

		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
		List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(zb, zbkg, SystemConstants.PowerTransformer, "", true, true);
		
		for(PowerDevice path : pathList){
			if(path.getDeviceType().equals(SystemConstants.SwitchSeparate)){
				if(!dzList.contains(path)){
					dztagList.add(path);
				}
			}
		}
		
		return dztagList;
	}
	
	public static boolean ifSwitchControl(PowerDevice dev){//开关可控
		String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
		
		List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
		
		for(Map<String,String> ifcontrolMap : ifcontrolList){
			String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
			
			if(ifcontrol.equals("0")||ifcontrol.equals("")){
				return false;
			}else{
				return true;
			}
		}
		
		return true;
	}
	
	public static boolean ifSwitchSeparateControl(PowerDevice dev){
		if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			if(dev.getPowerVoltGrade() == 10){
				return false;
			}
			
			String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
			
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
				
				if(ifcontrol.equals("0")||ifcontrol.equals("")){
					return false;
				}else{
					return true;
				}
			}
		}else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
			if(dev.getPowerVoltGrade() == 10){
				return false;
			}
			
			String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
			
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
				
				if(ifcontrol.equals("0")||ifcontrol.equals("")){
					return false;
				}else{
					return true;
				}
			}
		}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(dev.getPowerVoltGrade() == 10){
				return false;
			}
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dz : dzList){
				String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dz.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
					
					if(ifcontrol.equals("0")||ifcontrol.equals("")){
						return false;
					}else{
						return true;
					}
				}
			}
			
			if(dzList.size() == 0){
				return false;
			}
		}
		
		
		return true;
	}
	
	public static String getKnifeOffContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "曲靖地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}else{
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"确处拉开位置/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
//					replaceStr += getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"确处合闸位置/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getZxdJddzOffCheckContent(List<PowerDevice> zxdjddzList,String stationName,PowerDevice station){
		String replaceStr = "";

		boolean isZxdOff = false;
		
		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOff = true;
			}
		}
		
		if(isZxdOff){
			tagzxdjddzList = RuleExeUtil.sortByVoltLow(tagzxdjddzList);
			
			for(PowerDevice dev : tagzxdjddzList){
				replaceStr += "曲靖地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getZxdJddzOnCheckContent(List<PowerDevice> zxdjddzList,String stationName,PowerDevice station){
		String replaceStr = "";
		
		boolean isZxdOn = false;
		
		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOn = true;
			}
		}
		
		if(isZxdOn){
			tagzxdjddzList = RuleExeUtil.sortByVoltHigh(tagzxdjddzList);

			for(PowerDevice dev : tagzxdjddzList){
				replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getPowerOnCheckContent(){
		String replaceStr = "";
		
		for(Map<String,String> mapMap :  StationWorkSelectionDialog.deviceList){
			String result = mapMap.get("result");
			String stName = mapMap.get("stationName");
			String devName = mapMap.get("devName");

			if(result.equals("是")){
				replaceStr += stName+"@核实"+devName+"现场工作任务已结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），"+devName+"的二次装置已正常投入，"+devName+"具备送电条件/r/n";
			}else{
				replaceStr += stName+"@核实"+devName+"停电期间，站内未开展相关工作，无作业人员，现场未布置临时措施，现场无自行操作（装设）的接地开关（接地线），"+devName+"的二次装置已正常投入，"+devName+"具备送电条件/r/n";
			}
		}
		
		return replaceStr;
	}
}
