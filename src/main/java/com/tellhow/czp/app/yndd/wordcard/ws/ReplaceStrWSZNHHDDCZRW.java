package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSZNHHDDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山站内合环调电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			List<PowerDevice> otherlineList = new ArrayList<PowerDevice>();
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.Switch, true, true, true);
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							otherlineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.Switch, true, true, true);
							break;
						}
					}
				}
			}
		
			
			replaceStr += stationName+"由"+CZPService.getService().getDevName(lineList)+"供电倒由"+CZPService.getService().getDevName(otherlineList)+"供电";
		}
		
		return replaceStr;
	}

}
