package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.EquipRadioChoose;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.CardWordMode;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;

public class GetDtdDataHH extends GetDtdData{

	public static ArrayList<String[]> getData(List<CardItemModel> itemModelsShow,String ddname){
		ArrayList<String[]> data = new ArrayList<String[]>();
		modelList = DBManager.queryForList(CommonUtils.WORD_RBM_SQL);

		itemModelsShow = splitCardDesc(itemModelsShow);
		
		for(CardItemModel cim : itemModelsShow){
			String cznr = cim.getCardDesc();
			String stationid = cim.getCzdwID();
			String stationname = cim.getStationName();
			String bdzname = cim.getBdzName();
			String uuids = cim.getUuIds();

			String isyk = "1";
			String devname = "";
			String startZT = "";
			String endZT= "";
			String deviceType= "";

			// 倒母时会出现特殊情况：即是遥控令，指令中又没有厂站名称；因此需要使用bdzName
			if (bdzname.isEmpty()) bdzname = stationname;
			List<RuleBaseMode> rbmList = getRBMList(bdzname,cznr);
			String czname = "";


			if(!rbmList.isEmpty()){
				PowerDevice dev = rbmList.get(0).getPd();
				
				if(!rbmList.get(0).getCheckout()){
					List<String> messageList = rbmList.get(0).getMessageList();
					
					for(String message : messageList){
						ShowMessage.viewWarning(SystemConstants.getMainFrame(), message);
					}
					
					data.clear();
					break;
				}
				
				if(dev!=null){
					String begin = rbmList.get(0).getBeginStatus();
					String end = rbmList.get(0).getEndState();
					
					if(!dev.getPowerStationID().isEmpty()){
						PowerDevice stationDev = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						czname = CZPService.getService().getDevName(stationDev);
						devname = dev.getPowerDeviceName();
					}else{
						czname = stationname;
						
						if(czname.equals("红河地调")){
							czname = bdzname;
						}
					}
					
					if(cznr.contains("程序操作") || cznr.contains("遥控")){
						if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
							List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(dev.getPowerDeviceID());
							PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(dev.getPowerDeviceID());
							//停电
							if(Integer.parseInt(begin)<Integer.parseInt(end)){
								if(begin.equals("0")){
									if(loadLineTrans != null){
										for(PowerDevice loadLineTran : loadLineTrans){
											List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch,
													SystemConstants.PowerTransformer, true, true, true);
											PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
											czname = CZPService.getService().getDevName(station);
											
											if(!ishh(loadLineTran)){
												for(PowerDevice kg : kgList){
													if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
														String devid = kg.getPowerDeviceID();
														cznr = "遥控断开"+czname+CZPService.getService().getDevName(kg);
														data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
													}
												}
											}
										}
									}
									
									List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch,
											SystemConstants.PowerTransformer, true, true, true);
									PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
									czname = CZPService.getService().getDevName(station);
									
									for(PowerDevice kg : kgList){
										if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
											String devid = kg.getPowerDeviceID();
											cznr = "遥控断开"+czname+CZPService.getService().getDevName(kg);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
										}
									}
								}
								
								if(end.equals("2")){
									if(loadLineTrans != null){
										for(PowerDevice loadLineTran : loadLineTrans){
											List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch,
													SystemConstants.PowerTransformer, true, true, true);
											if (!kgList.isEmpty() && CBSystemConstants.RunModelThreeTwo.equals(kgList.get(0).getDeviceRunModel())) {
												getSwitchSequenceTdStepIn23JX(kgList, data, uuids, czname, ddname);
											} else {
												PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
												czname = CZPService.getService().getDevName(station);
												getSwitchSequenceTdStep(kgList,data,uuids,czname,ddname);
											}
										}
									}
									
									List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch,
											SystemConstants.PowerTransformer, true, true, true);
									PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
									czname = CZPService.getService().getDevName(station);
									
									if(kgList.size() == 2){
										List<PowerDevice> newkgList = new ArrayList<PowerDevice>();
										
										for(PowerDevice kg : kgList){
											if(RuleExeUtil.isSwMiddleInThreeSecond(kg)){
												newkgList.add(kg);
											}
										}
										
										for(PowerDevice kg : kgList){
											if(!RuleExeUtil.isSwMiddleInThreeSecond(kg)){
												newkgList.add(kg);
											}
										}
										
										kgList.clear();
										kgList.addAll(newkgList);
									}

                                    if (!kgList.isEmpty() && CBSystemConstants.RunModelThreeTwo.equals(kgList.get(0).getDeviceRunModel())) {
										getSwitchSequenceTdStepIn23JX(kgList, data, uuids, czname, ddname);
                                    } else {
										getSwitchSequenceTdStep(kgList, data, uuids, czname, ddname);
									}
								}
							}else{
								if(begin.equals("2")){
									if(loadLineTrans != null){
										for(PowerDevice loadLineTran : loadLineTrans){
											List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch,
													SystemConstants.PowerTransformer, true, true, true);
											PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
											czname = CZPService.getService().getDevName(station);
                                            if (!kgList.isEmpty() && CBSystemConstants.RunModelThreeTwo.equals(kgList.get(0).getDeviceRunModel())) {
												getSwitchSequenceFdStepIn23JX(kgList, data, uuids, czname, ddname);
                                            } else {
												getSwitchSequenceFdStep(kgList,data,uuids,czname,ddname);
											}
                                        }
									}
									
									if(sourceLineTrans != null){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch,
												SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
										czname = CZPService.getService().getDevName(station);
										
										if(kgList.size() == 2){
											List<PowerDevice> newkgList = new ArrayList<PowerDevice>();
											
											for(PowerDevice kg : kgList){
												if(!RuleExeUtil.isSwMiddleInThreeSecond(kg)){
													newkgList.add(kg);
												}
											}
											
											for(PowerDevice kg : kgList){
												if(RuleExeUtil.isSwMiddleInThreeSecond(kg)){
													newkgList.add(kg);
												}
											}
											
											kgList.clear();
											kgList.addAll(newkgList);
										}

                                        if (!kgList.isEmpty() && CBSystemConstants.RunModelThreeTwo.equals(kgList.get(0).getDeviceRunModel())) {
											getSwitchSequenceFdStepIn23JX(kgList, data, uuids, czname, ddname);
                                        } else {
											getSwitchSequenceFdStep(kgList, data, uuids, czname, ddname);
										}
                                    }
								}
								
								if(end.equals("0")){
									if(sourceLineTrans != null){
										List<PowerDevice> kgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch,
												SystemConstants.PowerTransformer, true, true, true);
										PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
										czname = CZPService.getService().getDevName(station);

										for(PowerDevice kg : kgList){
											String devid = kg.getPowerDeviceID();
											cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(kg);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
										}
									}

									if(loadLineTrans != null){
										for(PowerDevice loadLineTran : loadLineTrans){
											List<PowerDevice> kgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch,
													SystemConstants.PowerTransformer, true, true, true);
											PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
											czname = CZPService.getService().getDevName(station);
											
											for(PowerDevice kg : kgList){
												String devid = kg.getPowerDeviceID();
												cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(kg);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
											}
										}
									}
								}
							}
						}
						else if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
							List<PowerDevice> swList = new ArrayList<PowerDevice>();
							//母线为高压侧，且为单电源单刀闸情况
							List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate,
									SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXLS,"",
									true, true, true, true);

							if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
								swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,
										"",CBSystemConstants.RunTypeSwitchFHC,
										false, true, true, true);
							}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
								swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch,
										SystemConstants.PowerTransformer, true, true, true);
							}
							
							List<PowerDevice> drswList = new ArrayList<PowerDevice>();
							List<PowerDevice> dkswList = new ArrayList<PowerDevice>();
							List<PowerDevice> xlswList = new ArrayList<PowerDevice>();
							List<PowerDevice> jdbswList = new ArrayList<PowerDevice>();
							List<PowerDevice> zybswList = new ArrayList<PowerDevice>();
							List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
							List<PowerDevice> mlswList = new ArrayList<PowerDevice>();

							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
									drswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(drswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
									dkswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(dkswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									xlswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(xlswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getPowerDeviceName().contains("接地变")
										||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)){
									jdbswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(jdbswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getPowerDeviceName().contains("站用变")
										||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchZYB)){
									zybswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(zybswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)||
										switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
									zbswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(zbswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									mlswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(mlswList);
							
							swList.clear();
							
							if(!end.equals("")&&!begin.equals("")){
								if(rbmList.get(0).getOperaTion().equals("停电倒电")){
									List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch,
											SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC,"",
											false, true, true, true);
									List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch,
											SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML,"",
											false, true, true, true);
									List<PowerDevice> kgList = new ArrayList<PowerDevice>();
									
									kgList.addAll(zbkgList);
									kgList.addAll(mlkgList);
									
									for(PowerDevice kg : kgList){
										if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
											String devid = kg.getPowerDeviceID();
											String devicename = kg.getPowerDeviceName();
											cznr  =  "遥控断开"+czname+CZPService.getService().getDevName(kg);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devicename,startZT,"断开",deviceType,uuids});
										}
									}
									
									for(PowerDevice kg : kgList){
										if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
											String devid = kg.getPowerDeviceID();
											String devicename = kg.getPowerDeviceName();
											cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(kg);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devicename,startZT,"合上",deviceType,uuids});
										}
									}
								}
								else if(Integer.valueOf(begin)<Integer.valueOf(end)){ //停电
									if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
										swList.addAll(drswList);
										swList.addAll(dkswList);
										swList.addAll(xlswList);
										swList.addAll(jdbswList);
										swList.addAll(zybswList);
										swList.addAll(zbswList);
										swList.addAll(mlswList);

										getSwitchSequenceTdStep(swList,data,uuids,czname,ddname);
										
										for(PowerDevice xldz : xldzList){
											String devid = xldz.getPowerDeviceID();

											if(RuleExeUtil.isDeviceChanged(xldz)){
												cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(xldz);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
											}
										}
									}
									else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
										boolean isSmSfd = CommonFunctionHH.isDoubleBusDoubleBranch(mlswList);
										for(PowerDevice kg : mlswList){
											// 如果是双母双分段，则只生成母联开关的指令（分段开关指令在之前单独生成了）
											if(isSmSfd && !RuleExeUtil.isSwitchDoubleML(kg)) continue;
											if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
												String devid = kg.getPowerDeviceID();
												String kgName = CZPService.getService().getDevName(kg);
												
												cznr = "遥控断开"+czname+kgName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
												
												String check = "确认"+kgName+"热备用";
												data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
											}
										}

										// 如果是双母双分段，则将分段开关集合拆分成母联和分段，先分段再母联
										if(isSmSfd){
											List<PowerDevice> tempSwList = new ArrayList<PowerDevice>();
											for(PowerDevice kg : mlswList){
												if(RuleExeUtil.isSwitchDoubleML(kg)){
													tempSwList.add(kg);
												}
											}
											mlswList.removeAll(tempSwList);
											getSwitchSequenceTdStep(mlswList, data, uuids, czname, ddname);
											getSwitchSequenceTdStep(tempSwList, data, uuids, czname, ddname);
										} else {
											swList.addAll(mlswList);
											getSwitchSequenceTdStep(swList,data,uuids,czname, ddname);
											// TODO 此处的处理方式通用性存疑，为适配智源站而做
                                            if (rbmList.size() > 1) {
                                                List<PowerDevice> dzList = CommonFunctionHH.getTripleBusZeroBranchKnife(dev);
                                                // 如果搜到了多个设备，且当前设备判断为三母0分段，那么需要刀闸代替分段开关操作
                                                for (PowerDevice dz : dzList) {
                                                    String devId = dz.getPowerDeviceID();
                                                    String devType = dz.getDeviceType();
                                                    String dzName = CZPService.getService().getDevName(dz);

                                                    cznr =  "遥控拉开"+czname+dzName;
                                                    data.add(new String[]{"","",ddname,czname,cznr,"",devId,dzName,"","拉开",devType,uuids});

                                                    String check = "确认"+dzName+"处拉开位置";
                                                    data.add(new String[]{"","",czname,czname,check,"",devId,dzName,"","",devType,uuids});
                                                }
                                            }
                                        }

									}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
										String devid = dev.getPowerDeviceID();
										devname = CZPService.getService().getDevName(dev);
                                        if ("0".equals(begin) && "1".equals(end)) {
											getSwitchSequenceTdStepIn23JX0to1(dev, data, isyk, startZT, deviceType, uuids);
											cznr = "确认" + devname + "热备用";
											data.add(new String[]{"","",bdzname,czname,cznr,"0",devid,devname,startZT,endZT,deviceType,uuids});
										} else if ("1".equals(begin) && "2".equals(end)) {
											swList.addAll(xlswList);
											swList.addAll(zbswList);

											RuleExeUtil.swapDeviceListNum(swList);
											Collections.reverse(swList);

											getSwitchSequenceTdStepIn23JX(swList,data,uuids,czname, ddname);
											cznr = "确认" + devname + "冷备用";
											data.add(new String[]{"","",bdzname,czname,cznr,"0",devid,devname,startZT,endZT,deviceType,uuids});
										}
                                    }
								}else{ //复电
									if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
										if(dev.getPowerVoltGrade() == 10){
											swList.addAll(zbswList);
										}else{
											swList.addAll(zbswList);
											swList.addAll(zybswList);
											swList.addAll(jdbswList);
											swList.addAll(xlswList);
											swList.addAll(mlswList);
											swList.addAll(dkswList);
											swList.addAll(drswList);
											
											getSwitchSequenceFdStep(swList,data,uuids,czname,ddname);
										}
									}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
										boolean isSmSfd = CommonFunctionHH.isDoubleBusDoubleBranch(mlswList);
										// 如果是双母双分段，则将分段开关集合拆分成母联合分段，先分段再母联
										if(isSmSfd) {
											List<PowerDevice> tempSwList = new ArrayList<PowerDevice>();
											for(PowerDevice kg : mlswList){
												if(RuleExeUtil.isSwitchDoubleML(kg)){
													tempSwList.add(kg);
												}
											}
											mlswList.removeAll(tempSwList);
											getSwitchSequenceFdStep(mlswList, data, uuids, czname, ddname);
											getSwitchSequenceFdStep(tempSwList, data, uuids, czname, ddname);
											for(PowerDevice kg : mlswList){
												if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
													String devid = kg.getPowerDeviceID();
													String kgName = CZPService.getService().getDevName(kg);

													cznr = "遥控合上"+czname+kgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});

													String check = "确认"+kgName+"运行正常";
													data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
												}
											}
											for(PowerDevice kg : tempSwList){
												if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
													String devid = kg.getPowerDeviceID();
													String kgName = CZPService.getService().getDevName(kg);

													cznr = "遥控合上"+czname+kgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});

													String check = "确认"+kgName+"运行正常";
													data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
												}
											}
										} else {
                                            if (rbmList.size() > 1) {
                                                List<PowerDevice> dzList = CommonFunctionHH.getTripleBusZeroBranchKnife(dev);
                                                for (PowerDevice dz : dzList) {
                                                    String devId = dz.getPowerDeviceID();
                                                    String devType = dz.getDeviceType();
                                                    String dzName = CZPService.getService().getDevName(dz);

                                                    cznr =  "遥控合上"+czname+dzName;
                                                    data.add(new String[]{"","",ddname,czname,cznr,"",devId,dzName,"","合上",devType,uuids});

                                                    String check = "确认"+dzName+"处合上位置";
                                                    data.add(new String[]{"","",czname,czname,check,"",devId,dzName,"","",devType,uuids});
                                                }
                                            }
                                            swList.addAll(mlswList);
											getSwitchSequenceFdStep(swList,data,uuids,czname, ddname);
											for(PowerDevice kg : mlswList){
												if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
													String devid = kg.getPowerDeviceID();
													String kgName = CZPService.getService().getDevName(kg);

													cznr = "遥控合上"+czname+kgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});

													String check = "确认"+kgName+"运行正常";
													data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
												}
											}
										}

									}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){

										if ("2".equals(begin) && "1".equals(end)) {
											swList.addAll(xlswList);
											swList.addAll(zbswList);

											RuleExeUtil.swapDeviceListNum(swList);
											Collections.reverse(swList);
											getSwitchSequenceFdStepIn23JX(swList,data,uuids,czname, ddname);
										} else if ("1".equals(begin) && "0".equals(end)) {
											getSwitchSequenceFdStepIn23JX1to0(dev, data, isyk, startZT, deviceType, uuids);
										}
                                    }
								}
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}
						else if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							List<PowerDevice> gyczbkgList = RuleExeUtil.getTransformerSwitchHigh(dev);
							List<PowerDevice> zyczbkgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
							List<PowerDevice> dyczbkgList = RuleExeUtil.getTransformerSwitchLow(dev);
							List<PowerDevice> zbgycdzkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate,
									SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeZBS,
									"", true, true, true, true);
							List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine,
									"", CBSystemConstants.RunTypeGroundZXDDD, "",
									true, true, true, true);
							RuleExeUtil.swapLowDeviceList(gdList);
							
							if(RuleUtil.isTransformerNQ(dev)
									||RuleUtil.isTransformerKDNQ(dev)){
								gyczbkgList.clear();
							}
							
							if(rbmList.get(0).getOperaTion().equals("停电倒电")){
								List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
								List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
								List<PowerDevice> kgList = new ArrayList<PowerDevice>();

								PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
								HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(dev.getPowerStationID());
								
								for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
									PowerDevice dev2 = it.next();
									
									if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
										if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
											hignVoltXlkgList.add(dev2);
										}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
											hignVoltMlkgList.add(dev2);
										}
									}
								}
								
								kgList.addAll(hignVoltXlkgList);
								kgList.addAll(hignVoltMlkgList);
								
								for(PowerDevice kg : kgList){
									if(RuleExeUtil.getDeviceBeginStatus(kg).equals("0")){
										String devid = kg.getPowerDeviceID();
										String zbkgName = CZPService.getService().getDevName(kg);

										cznr  =  "遥控断开"+czname+zbkgName;
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"断开",deviceType,uuids});
									}
								}
								
								for(PowerDevice kg : kgList){
									if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
										String devid = kg.getPowerDeviceID();
										String zbkgName = CZPService.getService().getDevName(kg);

										cznr  =  "遥控合上"+czname+zbkgName;
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"合上",deviceType,uuids});
									}
								}
							}else{
								if(!begin.equals("")&&!end.equals("")){
									if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
										boolean is23JX = false;
										
										if(gyczbkgList.size() == 2){
											is23JX = true;
											
											for(PowerDevice gyczbkg : gyczbkgList){
												if(!RuleExeUtil.isSwMiddleInThreeSecond(gyczbkg)){
													Collections.reverse(gyczbkgList);
												}
												break;
											}
										}
										
										if(zyczbkgList.size() == 2){
											for(PowerDevice zyczbkg : zyczbkgList){
												if(!RuleExeUtil.isSwMiddleInThreeSecond(zyczbkg)){
													Collections.reverse(zyczbkgList);
												}
												break;
											}
										}
										
										if(begin.equals("0")){
											for(PowerDevice gd : gdList){
												String devid = gd.getPowerDeviceID();
												String gdName = CZPService.getService().getDevName(gd);

												cznr = "遥控合上"+czname+gdName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gdName,startZT,"合上",deviceType,uuids});
											}
											
											for(PowerDevice dyczbkg : dyczbkgList){
												String devid = dyczbkg.getPowerDeviceID();
												String zbkgName = CZPService.getService().getDevName(dyczbkg);
												
												if(RuleExeUtil.getDeviceBeginStatus(dyczbkg).equals("0")){
													cznr = "遥控断开"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"断开",deviceType,uuids});
												}
											}
											
											for(PowerDevice zyczbkg : zyczbkgList){
												String devid = zyczbkg.getPowerDeviceID();
												String zbkgName = CZPService.getService().getDevName(zyczbkg);
												
												if(RuleExeUtil.getDeviceBeginStatus(zyczbkg).equals("0")){
													cznr = "遥控断开"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"断开",deviceType,uuids});
												}
											}
											
											for(PowerDevice gyczbkg : gyczbkgList){
												String devid = gyczbkg.getPowerDeviceID();
												String zbkgName = CZPService.getService().getDevName(gyczbkg);

												if(RuleExeUtil.getDeviceBeginStatus(gyczbkg).equals("0")){
													cznr = "遥控断开"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zbkgName,startZT,"断开",deviceType,uuids});
												}
											}
										}
										
										if(is23JX){
											getSwitchSequenceTdStepIn23JX(dyczbkgList, data, uuids, czname, ddname);
											
											getSwitchSequenceTdStepIn23JX(zyczbkgList, data, uuids, czname, ddname);

											getSwitchSequenceTdStepIn23JX(gyczbkgList, data, uuids, czname, ddname);
										}else{
											getSwitchSequenceTdStep(dyczbkgList, data, uuids, czname, ddname);
											
											getSwitchSequenceTdStep(zyczbkgList, data, uuids, czname, ddname);

											getSwitchSequenceTdStep(gyczbkgList, data, uuids, czname, ddname);
											
											getSwitchSeparateSequenceTd(zbgycdzkgList,data,uuids,czname,ddname);
											
											if(begin.equals("0")){
												for(PowerDevice gd : gdList){
													String devid = gd.getPowerDeviceID();
													String gdName = CZPService.getService().getDevName(gd);

													cznr = "遥控拉开"+czname+gdName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gd.getPowerDeviceName(),startZT,"拉开",deviceType,uuids});
												}
											}
										}
									}else{//复电
										boolean is23JX = false;
										
										if(gyczbkgList.size() == 2){
											is23JX = true;
											
											for(PowerDevice gyczbkg : gyczbkgList){
												if(RuleExeUtil.isSwMiddleInThreeSecond(gyczbkg)){
													Collections.reverse(gyczbkgList);
												}
												break;
											}
										}
										
										if(zyczbkgList.size() == 2){
											for(PowerDevice zyczbkg : zyczbkgList){
												if(RuleExeUtil.isSwMiddleInThreeSecond(zyczbkg)){
													Collections.reverse(zyczbkgList);
												}
												break;
											}
										}
										
										if(end.equals("0")){
											for(PowerDevice gd : gdList){
												String devid = gd.getPowerDeviceID();
												String gdName = CZPService.getService().getDevName(gd);

												cznr  =  "遥控合上"+czname+gdName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gd.getPowerDeviceName(),startZT,"合上",deviceType,uuids});
											}
										}
										
										
										if(is23JX){
											getSwitchSequenceFdStepIn23JX(gyczbkgList, data, uuids, czname, ddname);
											
											getSwitchSequenceFdStepIn23JX(zyczbkgList, data, uuids, czname, ddname);
											
											getSwitchSequenceFdStepIn23JX(dyczbkgList, data, uuids, czname, ddname);
										}else{
											getSwitchSeparateSequenceFd(zbgycdzkgList,data,uuids,czname,ddname);

											getSwitchSequenceFdStep(gyczbkgList, data, uuids, czname, ddname);
											
											getSwitchSequenceFdStep(zyczbkgList, data, uuids, czname, ddname);
											
											getSwitchSequenceFdStep(dyczbkgList, data, uuids, czname, ddname);
										}
										
										if(end.equals("0")){
											for(PowerDevice gyczbkg : gyczbkgList){
												String devid = gyczbkg.getPowerDeviceID();
												String zbkgName = CZPService.getService().getDevName(gyczbkg);

												cznr  =  "遥控合上"+czname+zbkgName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gyczbkg.getPowerDeviceName(),startZT,"合上",deviceType,uuids});
											}
											
											for(PowerDevice zyczbkg : zyczbkgList){
												if(RuleExeUtil.getDeviceEndStatus(zyczbkg).equals("0")){
													String devid = zyczbkg.getPowerDeviceID();
													String zbkgName = CZPService.getService().getDevName(zyczbkg);

													cznr  =  "遥控合上"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,zyczbkg.getPowerDeviceName(),startZT,"合上",deviceType,uuids});
												}
											}
											
											for(PowerDevice dyczbkg : dyczbkgList){
												if(RuleExeUtil.getDeviceEndStatus(dyczbkg).equals("0")){
													String devid = dyczbkg.getPowerDeviceID();
													String zbkgName = CZPService.getService().getDevName(dyczbkg);

													cznr  =  "遥控合上"+czname+zbkgName;
													data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,dyczbkg.getPowerDeviceName(),startZT,"合上",deviceType,uuids});
												}
											}
											
											for(PowerDevice gd : gdList){
												String devid = gd.getPowerDeviceID();
												String gdName = CZPService.getService().getDevName(gd);

												cznr = "遥控拉开"+czname+gdName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,gd.getPowerDeviceName(),startZT,"拉开",deviceType,uuids});
											}
										}
									}
								}else{
									data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
								}
							}
						}
						else if(dev.getDeviceType().equals(SystemConstants.Switch)){
							if((begin.equals("1")&&end.equals("2"))){
								List<PowerDevice> swList = new ArrayList<PowerDevice>();
								swList.add(dev);
                                if (!swList.isEmpty() && CBSystemConstants.RunModelThreeTwo.equals(swList.get(0).getDeviceRunModel())) {
									getSwitchSequenceTdStepIn23JX(swList, data, uuids, czname, ddname);
                                } else {
									getSwitchSequenceTdStep(swList , data, uuids, czname ,ddname);
								}
                            }else if(begin.equals("2")&&end.equals("1")){
								List<PowerDevice> swList = new ArrayList<PowerDevice>();
								swList.add(dev);
                                if (!swList.isEmpty() && CBSystemConstants.RunModelThreeTwo.equals(swList.get(0).getDeviceRunModel())) {
									getSwitchSequenceTdStepIn23JX(swList, data, uuids, czname, ddname);
                                } else {
									getSwitchSequenceFdStep(swList , data, uuids, czname ,ddname);
								}
                            }else if(begin.equals("1")&&end.equals("0")){
								String devid = dev.getPowerDeviceID();
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});

								String check = "确认"+CZPService.getService().getDevName(dev)+"运行正常";
								data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
								/*if(dev.getPowerVoltGrade() >= 220){
								}*/
							}else if(begin.equals("0")&&end.equals("1")){
								String devid = dev.getPowerDeviceID();
								devname = CZPService.getService().getDevName(dev);
								cznr = "遥控断开"+czname+devname;
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});

								String check = "确认"+devname+"热备用";
								data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
								/*if(dev.getPowerVoltGrade() >= 220){
								}*/
							}else if(begin.equals("0")&&end.equals("2")){
								String devid = dev.getPowerDeviceID();
								cznr  =  "遥控断开"+czname+devname;
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
								
								List<PowerDevice> swList = new ArrayList<PowerDevice>();
								swList.add(dev);
								getSwitchSequenceTdStep(swList , data, uuids, czname ,ddname);
							}else if(begin.equals("2")&&end.equals("0")){
								List<PowerDevice> swList = new ArrayList<PowerDevice>();
								swList.add(dev);
								getSwitchSequenceFdStep(swList , data, uuids, czname ,ddname);
								
								String devid = dev.getPowerDeviceID();
								cznr  =  "遥控合上"+czname+devname;
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}else if(cznr.contains("运行倒至")){
								for(RuleBaseMode rm : rbmList){
									if(rm.getPd().getDeviceType().equals(SystemConstants.Switch)){
										List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(rm.getPd(), SystemConstants.SwitchSeparate);

										for(PowerDevice dz : dzList){
											if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
												String devid = dz.getPowerDeviceID();
												devname= dz.getPowerDeviceName();
												String dzName = CZPService.getService().getDevName(dz);
												cznr = "遥控合上"+czname+dzName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
												
												String check = "确认"+dzName+"处合上位置";
												data.add(new String[]{"","",czname,czname,check,isyk,devid,devname,startZT,"",deviceType,uuids});
											}
										}
										
										for(PowerDevice dz : dzList){
											String devid = dz.getPowerDeviceID();
											devname= dz.getPowerDeviceName();
											String dzName = CZPService.getService().getDevName(dz);

											if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
												cznr = "遥控拉开"+czname+dzName;
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
												
												String check = "确认"+dzName+"处拉开位置";
												data.add(new String[]{"","",czname,czname,check,isyk,devid,devname,startZT,"",deviceType,uuids});
											}
										}
									}
								}
							}else if(cznr.contains("热备用倒至")){
								for(RuleBaseMode rm : rbmList){
									if(rm.getPd().getDeviceType().equals(SystemConstants.Switch)){
										List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(rm.getPd(), SystemConstants.SwitchSeparate);
										if(rm.getPd().getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
											dzList = RuleExeUtil.sortByCZMXC(dzList);
										}else{
											if(rm.getPd().getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
												dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
											}else{
												dzList = RuleExeUtil.sortByMXC(dzList);
												Collections.reverse(dzList);
											}
										}
										for(PowerDevice zbdz : dzList){
											if(RuleExeUtil.isDeviceHadStatus(zbdz, "0", "1")&&!zbdz.getPowerDeviceName().contains("PT")){
												String devid =  zbdz.getPowerDeviceID();
												devname = zbdz.getPowerDeviceName();
												deviceType = zbdz.getDeviceType();

												String dzName = CZPService.getService().getDevName(zbdz);

												cznr =  "遥控拉开"+czname+dzName;
												data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
												String check = "确认"+dzName+"处拉开位置";
												data.add(new String[]{"","",czname,czname,check,isyk,devid,devname,startZT,"",deviceType,uuids});
											}
										}

										if(rm.getPd().getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
											dzList = RuleExeUtil.sortByCZMXC(dzList);
											Collections.reverse(dzList);
										}else{
											if(rm.getPd().getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
												dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
												Collections.reverse(dzList);
											}else{
												dzList = RuleExeUtil.sortByMXC(dzList);
											}
										}
										for(PowerDevice zbdz : dzList){
											if(RuleExeUtil.isDeviceHadStatus(zbdz, "1", "0")&&!zbdz.getPowerDeviceName().contains("PT")){
												String devid = zbdz.getPowerDeviceID();
												devname = zbdz.getPowerDeviceName();
												deviceType = zbdz.getDeviceType();

												String dzName = CZPService.getService().getDevName(zbdz);

												cznr = "遥控合上"+czname+dzName;
												data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});
												String check = "确认"+dzName+"处合上位置";
												data.add(new String[]{"","",czname,czname,check,isyk,devid,devname,startZT,"",deviceType,uuids});
											}
										}
									}
								}
							}else{
								data.add(new String[]{"","",czname,czname,cznr,isyk,"",devname,startZT,endZT,deviceType,uuids});
							}
						}
						else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)){
								for(RuleBaseMode rbm : rbmList){
									if(begin.equals("1")&&end.equals("0")){
										String devid = rbm.getPd().getPowerDeviceID();
										devname = rbm.getPd().getPowerDeviceName();
										cznr = "遥控合上"+czname+CZPService.getService().getDevName(rbm.getPd());
										
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									}else if(begin.equals("0")&&end.equals("1")){
										String devid = rbm.getPd().getPowerDeviceID();
										devname = rbm.getPd().getPowerDeviceName();
										cznr = "遥控拉开"+czname+CZPService.getService().getDevName(rbm.getPd());
										
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
									}
								}
							}else{
								if(begin.equals("1")&&end.equals("0")){
									String devid = dev.getPowerDeviceID();
									
									cznr =  "合上"+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								}else if(begin.equals("0")&&end.equals("1")){
									String devid = dev.getPowerDeviceID();
									
									cznr =  "拉开"+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								}else{
									data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
								}
							}
						}
						else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
							String devid = dev.getPowerDeviceID();
							
							if(begin.equals("1")&&end.equals("0")){
								String dzName = CZPService.getService().getDevName(dev);

								cznr =  "遥控合上"+czname+dzName;
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
								String check = "确认"+dzName+"处合上位置";
								data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								String dzName = CZPService.getService().getDevName(dev);
								
								cznr =  "遥控拉开"+czname+dzName;
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								
								String check = "确认"+dzName+"处拉开位置";
								data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}
						else if(cznr.contains("所有断路器倒至")){
							List<PowerDevice> swList = new ArrayList<PowerDevice>();
							
							PowerDevice	device = CBSystemConstants.getCurRBM().getPd();
							
							if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
								swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch,
										SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchFHC,
										false, true, true, true);
							}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
								swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch,
										SystemConstants.PowerTransformer, true, true, true);
							}
							
							List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
							List<PowerDevice> xlswList = new ArrayList<PowerDevice>();

							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
									zbswList.add(switchs);
								}
							}
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
									zbswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(zbswList);
							
							for(PowerDevice switchs : swList){
								if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
									xlswList.add(switchs);
								}
							}
							
							RuleExeUtil.swapDeviceList(xlswList);
							
							List<PowerDevice> yxswList = new ArrayList<PowerDevice>();
							
							yxswList.addAll(xlswList);
							yxswList.addAll(zbswList);
							
							for(PowerDevice sw : yxswList){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

								for(PowerDevice dz : dzList){
									String devid = dz.getPowerDeviceID();
									
									if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
										cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									}
								}
								
								for(PowerDevice dz : dzList){
									String devid = dz.getPowerDeviceID();

									if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
										cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
									}
								}
							}
						}
						else if(cznr.contains("退出") || cznr.contains("投入")){
							if(cznr.contains("遥控")){
								data.add(new String[]{"","",ddname,czname,cznr,"0",dev.getPowerDeviceID(),devname,startZT,endZT,deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0",dev.getPowerDeviceID(),devname,startZT,endZT,deviceType,uuids});
							}
						}
						else{
							if(cim.getStationName().equals("云南省调")){
								czname = "云南省调";
								cznr = cznr.replace("落实", "落实省调");
							}
							
							if(cznr.contains("遥控")){
								data.add(new String[]{"","",ddname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}
					}else{
						String devid = dev.getPowerDeviceID();
						devname = dev.getPowerDeviceName();
						
						if(!begin.equals("")){
							startZT = RuleExeUtil.getStatusNew(dev.getDeviceType(), begin);
						}
						
						if(!end.equals("")){
							endZT = RuleExeUtil.getStatusNew(dev.getDeviceType(), end);
						}

						data.add(new String[]{"","",stationname,czname,cznr,"0",devid,devname,startZT,endZT,deviceType,uuids});
					}
				}else{
					data.add(new String[]{"","",stationname,stationname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
				}
			}else{
				data.add(new String[]{"","",stationname,stationname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
			}
		}
		
		return data;
	}
	
	private static List<RuleBaseMode> getRBMList(String station, String word){
		String kuohao = "";
		word = word.replace("(", "（").replace(")", "）");
		
		if(word.contains("（")&&word.contains("）")){
			if(word.indexOf("（")<word.indexOf("）")){
				kuohao = word.substring(word.indexOf("（"),word.indexOf("）")+1);
			}
		}
		
		word = word.replace(kuohao, "");
		String wholeWord = word;
		
		/*
		 * 上级单位
		 */
		String superiorStationName = "";
		/*
		 * 下级单位
		 */
		String subordinateStationName = "";
		/*
		 * 指令单位
		 */
		String instructionStationName = "";
		
		/*
		 * 生成设备对象、初始状态、目标状态、操作、所在母线解析
		 */
		List<CardWordMode> wordRbmList = new ArrayList<CardWordMode>();
		
//		String sql="SELECT MODELDESC,BEGINSTATUS,ENDSTATUS,OPERATION " +
//				"FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL ORDER BY TO_NUMBER(ORDERID) ASC";
//	    List<Map<String,String>> modelList=DBManager.queryForList(sql);
	    
	    for(Map<String,String> model : modelList){
    		String modeldesc = StringUtils.ObjToString(model.get("MODELDESC"));
    		String beginStatus = StringUtils.ObjToString(model.get("BEGINSTATUS"));
    		String endStatus = StringUtils.ObjToString(model.get("ENDSTATUS"));
    		String operation = StringUtils.ObjToString(model.get("OPERATION"));
    		
			Object[] splitParams = SafeCheckUtilHH.init(modeldesc,'[',']');
			List<String> paramsKey=(ArrayList<String>)splitParams[1]; //标签集合
			
			if(paramsKey.size()>0){
				List<String> firstStr=(ArrayList)splitParams[0];
				String lastStr=splitParams[2].toString();

				StringBuffer descBuff=new StringBuffer();
				
				for(String first : firstStr){
					descBuff.append(first+"(.*)");
				}
				
				descBuff.append(lastStr);
				
				String regx = "^"+descBuff.toString()+"$";
				Pattern compile = Pattern.compile(regx);
                Matcher matcher = compile.matcher(word);
				
                if(matcher.find()){ //解析
            		System.out.println("操作内容："+word);

            		String[] splitregxs = regx.replace("^", "").replace("$", "").replace("(.*)", "|").split("\\|");
            		
            		for(String string : splitregxs){
            			if(!string.equals("")){
            				if(string.equals("倒由")){
            					word = word.replace("倒", "");
            				}
            				word = word.replace(string, "");
            			}
            		}
            		
            		List<Map<String,String>> returnList = processDeviceList(SafeCheckUtilHH.getDeviceInfoByWord(paramsKey,word));
            		
            		CardWordMode cwm = new CardWordMode();
        			
        			if(!beginStatus.equals("")){
        				cwm.setBeginStatus(beginStatus);
        			}
        				
        			if(!endStatus.equals("")){
        				cwm.setEndStatus(endStatus);
        			}
        				
        			if(!operation.equals("")){
        				cwm.setOperaTion(operation);
        			}
        			
        			List<PowerDevice> devList = new ArrayList<PowerDevice>();
        			
        			for(Map<String,String> returnMap : returnList){
        				PowerDevice dev = new PowerDevice();

        				if(returnMap.containsKey("厂站名称")){
        					dev.setPowerStationName(returnMap.get("厂站名称"));
        					instructionStationName = returnMap.get("厂站名称");
        				}
        					
        				if(returnMap.containsKey("设备名称")){
        					dev.setPowerDeviceName(returnMap.get("设备名称"));
        				}
        					
        				if(returnMap.containsKey("设备状态")){
        					if(cwm.getBeginStatus().equals("")){
        						cwm.setBeginStatus(returnMap.get("设备状态"));
        					}else if(cwm.getEndStatus().equals("")){
        						cwm.setEndStatus(returnMap.get("设备状态"));
        					}
        				}
        				
        				if(returnMap.containsKey("所在母线")){
        					cwm.setBusBar(returnMap.get("所在母线"));
        				}
        				
        				if(returnMap.containsKey("设备类型")){
        					cwm.setDeviceKind(returnMap.get("设备类型"));
        				}
        				
        				if(dev.getPowerStationName().equals("")){
        					dev.setPowerStationName(station);
        				}
        				
        				if(!dev.getPowerDeviceName().equals("")){
        					devList.add(dev);
        				}
        			}
        			
        			cwm.setPdList(devList);
        			wordRbmList.add(cwm);
        			
        			for(PowerDevice device : devList){
        				System.out.println("厂站名称："+device.getPowerStationName());
        				System.out.println("设备名称："+device.getPowerDeviceName());
        			}
        			
        			System.out.println("初始状态："+cwm.getBeginStatus());
        			System.out.println("目标状态："+cwm.getEndStatus());
        			System.out.println("操作："+cwm.getOperaTion());
        			System.out.println("所在母线："+cwm.getBusBar());
        			System.out.println("****************************************");
            		
            		break;
                }
			}
	    }
	    
	    List<String> stationIDList = new ArrayList<String>();
		
		if(instructionStationName.equals("")){//指令单位为空，那么下级单位取station
			subordinateStationName = station;
		}else{//指令单位不为空，那么上级单位为station，下级单位为instructionStationName
			superiorStationName = station;
			subordinateStationName = instructionStationName;
		}
		
		//厂站名称校验
		for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();){
			PowerDevice st = it.next();
			
			String modelDevName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(st));
			subordinateStationName = StringUtils.killVoltInDevName(subordinateStationName);
			
			if(modelDevName.equals(subordinateStationName)) {
				String stationID = st.getPowerDeviceID();
				stationIDList.add(stationID);
				
				if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
					CreatePowerStationToplogy.loadFacEquip(stationID);
				}
			}
		}
		
		/*
		 * 只写线路的情况下特殊判断
		 */
		
		if(stationIDList.size() == 0){
			if(!wholeWord.contains("断路器")&&!wholeWord.contains("隔离开关")&&!wholeWord.contains("接地开关")){
				for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerLine().values().iterator();it.hasNext();){
					PowerDevice line = it.next();
					
					if(line.getPowerVoltGrade() > 10){
						String lineName = CZPService.getService().getDevName(line);
						
						if(!lineName.equals("备用")&&wholeWord.contains(lineName)){
							Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(line);
							
							for(PowerDevice ln:stationlines.keySet()){
								if(ln.getPowerStationName().contains("tase")){
									continue;
								}
								
								String stationID = ln.getPowerStationID();
								stationIDList.add(stationID);
								
								break;
							}
						}
						
						if(stationIDList.size() == 1){
							break;
						}
					}
				}
			}
		}
		
		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();
		
		if(stationIDList.size() > 1){
			ArrayList<String> messageList = new ArrayList<String>();
			messageList.add("当前成票中，操作单位匹配到多个厂站模型，生成序列失败！");
			
			RuleBaseMode rbm = new RuleBaseMode();
			rbm.setCheckout(false);
			rbm.setMessageList(messageList);
			rbmList.add(rbm);
		}
		
		//设备名称校验
		if(stationIDList.size() == 1){
			if (CBSystemConstants.getStationPowerDevices(stationIDList.get(0)) == null) {
				CreatePowerStationToplogy.loadFacEquip(stationIDList.get(0));
			}
			
			initDeviceRunType(stationIDList.get(0));
			
			HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationIDList.get(0));
			
			if(devMap != null){
				for(CardWordMode cwm : wordRbmList){
					List<PowerDevice> devList = cwm.getPdList();
					
					if(cwm.getDeviceKind().equals("二次设备")){
						
					}else if(cwm.getOperaTion().contains("地线")){
						
					}else{
						if(devList.size() > 1){
							for(PowerDevice device : devList){
								String equipTypeFlag = "";
								String equipTypeName = "";
								String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
										SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine,
										SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate,SystemConstants.Switch, SystemConstants.SwitchSeparate,
										SystemConstants.SwitchSeparate, SystemConstants.Switch,
										SystemConstants.Switch,SystemConstants.VolsbTransformer, SystemConstants.MotherLine,
										SystemConstants.MotherLine, SystemConstants.InOutLine,SystemConstants.InOutLine,
										SystemConstants.PowerTransformer, SystemConstants.ElecShock,
										SystemConstants.ElecCapacity ,SystemConstants.PowerTransformer};
								String[] key = new String[] { "接地刀闸","接地开关", "地刀", "隔离刀闸","隔离开关","小车开关", "小车", "刀闸", "断路器",
										"开关","PT", "母线", "母", "线","回", "主变", "电抗器", "电容器","#变"};
								for (int i = 0; i < key.length; i++) {
									if (device.getPowerDeviceName().lastIndexOf(key[i]) >= 0) {
										equipTypeFlag = type[i];
										equipTypeName = key[i];
										break;
									}
								}
								
								String devNum = CZPService.getService().getDevNum(device);
								
								String volStr = "";
								
								if(device.getPowerDeviceName().toLowerCase().split("kv").length >= 3){
									volStr = device.getPowerDeviceName().toLowerCase().substring(device.getPowerDeviceName().toLowerCase().indexOf("kv")+2);
								}else if(device.getPowerDeviceName().contains("中性点")&&device.getPowerDeviceName().contains("接地开关")){
									volStr = "";
								}else{
									volStr = device.getPowerDeviceName();
								}
								
								//获取电压等级
								String volt = StringUtils.getVoltInDevName(volStr);
								List<PowerDevice> pdList = new ArrayList<PowerDevice>();
								
								for (PowerDevice dev : devMap.values()) {
									if (!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
										continue;
									if (!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
										continue;
									if(dev.getPowerDeviceName().contains("A相")||dev.getPowerDeviceName().contains("B相")||dev.getPowerDeviceName().contains("C相"))
										continue;
									if(dev.getPowerDeviceName().contains("虚"))
										continue;
									
									if(equipTypeFlag.equals(SystemConstants.InOutLine)){
										if (CZPService.getService().getDevName(dev).equals(device.getPowerDeviceName())){
											pdList.add(dev);
											if(pdList.size()>1){
												break;
											}
										}
									}else if (CZPService.getService().getDevNum(dev).equals(devNum)&&!devNum.equals("")) {
										if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
											pdList.add(dev);
											if(pdList.size()>1){
												break;
											}
										}else{
											pdList.add(dev);
											if(pdList.size()>1){
												break;
											}
										}
									}
								}
								
								if(pdList.size() > 1){
									ArrayList<String> messageList = new ArrayList<String>();
									messageList.add("当前成票中，<"+device.getPowerDeviceName()+">匹配到多个模型设备，生成序列失败！");
									
									RuleBaseMode rbm = new RuleBaseMode();
									rbm.setPd(device);
									rbm.setCheckout(false);
									rbm.setMessageList(messageList);
									rbmList.add(rbm);
								}else if(pdList.size() == 1){
									String modelName = pdList.get(0).getPowerDeviceName();
									String deviceName = device.getPowerDeviceName();
									String beginStatus = cwm.getBeginStatus();
									String endStatus = cwm.getEndStatus();
									
									beginStatus = RuleExeUtil.getNumStatusNew(beginStatus);
									endStatus = RuleExeUtil.getNumStatusNew(endStatus);
								
									RuleBaseMode rbm = new RuleBaseMode();
									rbm.setPd(pdList.get(0));
									rbm.setBeginStatus(beginStatus);
									rbm.setEndState(endStatus);
									rbm.setBusBar(cwm.getBusBar());
									rbm.setOperaTion(cwm.getOperaTion());
									rbmList.add(rbm);
								}
							}
						}else{
							for(PowerDevice device : devList){
								String equipTypeFlag = "";
								String equipTypeName = "";
								String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
										SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine,
										SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate,SystemConstants.Switch, SystemConstants.SwitchSeparate,
										SystemConstants.SwitchSeparate, SystemConstants.Switch,
										SystemConstants.Switch,SystemConstants.VolsbTransformer, SystemConstants.MotherLine,
										SystemConstants.MotherLine, SystemConstants.InOutLine,SystemConstants.InOutLine,
										SystemConstants.PowerTransformer, SystemConstants.ElecShock,
										SystemConstants.ElecCapacity ,SystemConstants.PowerTransformer};
								String[] key = new String[] { "接地刀闸","接地开关", "地刀", "隔离刀闸","隔离开关","小车开关", "小车", "刀闸", "断路器",
										"开关","PT", "母线", "母", "线","回", "主变", "电抗器", "电容器","#变"};
								for (int i = 0; i < key.length; i++) {
									if (device.getPowerDeviceName().lastIndexOf(key[i]) >= 0) {
										equipTypeFlag = type[i];
										equipTypeName = key[i];
										break;
									}
								}
								
								String devName = CZPService.getService().getDevName(device);
								
								String volStr = "";
								
								if(device.getPowerDeviceName().toLowerCase().split("kv").length >= 3){
									volStr = device.getPowerDeviceName().toLowerCase().substring(device.getPowerDeviceName().toLowerCase().indexOf("kv")+2);
								}else if(device.getPowerDeviceName().contains("中性点")&&device.getPowerDeviceName().contains("接地开关")){
									volStr = "";
								}else{
									volStr = device.getPowerDeviceName();
								}
								
								//获取电压等级
								String volt = StringUtils.getVoltInDevName(volStr);
								List<PowerDevice> pdList = new ArrayList<PowerDevice>();
								
								for (PowerDevice dev : devMap.values()) {
									if (!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
										continue;
									if (!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
										continue;
									if(dev.getPowerDeviceName().contains("A相")||dev.getPowerDeviceName().contains("B相")||dev.getPowerDeviceName().contains("C相"))
										continue;
									if(dev.getPowerDeviceName().contains("虚"))
										continue;
									
									if(equipTypeFlag.equals(SystemConstants.InOutLine)){
										if (CZPService.getService().getDevName(dev).equals(device.getPowerDeviceName())){
											pdList.add(dev);
											if(pdList.size()>1){
												break;
											}
										}
									}else if (CZPService.getService().getDevName(dev).equals(devName)&&!devName.equals("")) {
										if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
											pdList.add(dev);
											if(pdList.size()>1){
												break;
											}
										}else{
											pdList.add(dev);
											if(pdList.size()>1){
												break;
											}
										}
									}
								}
								
								if(pdList.size() > 1){
									ArrayList<String> messageList = new ArrayList<String>();
									messageList.add("当前成票中，<"+device.getPowerDeviceName()+">匹配到多个模型设备，生成序列失败！");
									
									RuleBaseMode rbm = new RuleBaseMode();
									rbm.setPd(device);
									rbm.setCheckout(false);
									rbm.setMessageList(messageList);
									rbmList.add(rbm);
								}else if(pdList.size() == 1){
									String modelName = pdList.get(0).getPowerDeviceName();
									String deviceName = device.getPowerDeviceName();
									String beginStatus = cwm.getBeginStatus();
									String endStatus = cwm.getEndStatus();
									
									beginStatus = RuleExeUtil.getNumStatusNew(beginStatus);
									endStatus = RuleExeUtil.getNumStatusNew(endStatus);
								
									RuleBaseMode rbm = new RuleBaseMode();
									rbm.setPd(pdList.get(0));
									rbm.setBeginStatus(beginStatus);
									rbm.setEndState(endStatus);
									rbm.setBusBar(cwm.getBusBar());
									rbm.setOperaTion(cwm.getOperaTion());
									rbmList.add(rbm);
								}
							}
						}
					}
				}
			}
		}
		
		if(rbmList.size()==0){
			RuleBaseMode rbm = new RuleBaseMode();
			PowerDevice dev = new PowerDevice();
			rbm.setPd(dev);
			rbmList.add(rbm);
		}
		
	    return rbmList;
	}
	
	public static String getVoltInDevName(String powerDeviceName) {
    	String volt = "";
    	String equipName = powerDeviceName;
    	int pos = equipName.toUpperCase().indexOf("KV");
		if (pos >= 0) {
			volt = "";
			for(int i = pos-1; i >=0; i--) {
				char ch = equipName.charAt(i);
				if (ch >= '0' && ch <= '9')
					volt = ch + volt;
				else
					break;
			}
        }
		else
			volt = "";
    	return volt;
    }
	
	public static  String getStationNameByCznr(String cznr) {
		String stationName = "";
		if(cznr.lastIndexOf("站") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("站")+1);
		else if(cznr.lastIndexOf("变") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("变")+1);
		else if(cznr.lastIndexOf("厂") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("厂")+1);
		
		if(stationName.indexOf("千伏") >= 0)
			stationName = stationName.substring(stationName.lastIndexOf("千伏")+2);
		else if(stationName.toLowerCase().indexOf("kv") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().lastIndexOf("kv")+2);
		else if(stationName.toLowerCase().indexOf("切换至") >= 0 || stationName.toLowerCase().indexOf("切至") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("至")+1);
		else if(stationName.toLowerCase().indexOf("切换到") >= 0 || stationName.toLowerCase().indexOf("切到") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("到")+1);
		else if(stationName.toLowerCase().indexOf("断开") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("断开")+2);
		else if(stationName.toLowerCase().indexOf("合上") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("合上")+2);
		return stationName;
	}
	
	/*
	 * 刀闸停电（分布格式）
	 */
	public static void getSwitchSequenceTdStep(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			for(Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					if(dev.getPowerDeviceName().endsWith("1")){
						it.remove();
					}
				}
			}
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
					Collections.reverse(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				if(RuleExeUtil.isDeviceHadStatus(zbdz, "0", "1")&&!zbdz.getPowerDeviceName().contains("PT")){
					String devid =  zbdz.getPowerDeviceID();
					String devname = zbdz.getPowerDeviceName();
					String deviceType = zbdz.getDeviceType();
					
					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String cznr = "执行"+czname+CZPService.getService().getDevName(sw)+"由热备用转冷备用程序操作";
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
						break;
					}else{
						String dzName = CZPService.getService().getDevName(zbdz);
						
						String cznr =  "遥控拉开"+czname+dzName;
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
						
						String check = "确认"+dzName+"处拉开位置";
						data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
					}
				}
			}
		}
	}
	
	/*
	 * 开关复电获取刀闸序列（分布格式）
	 */
	public static void getSwitchSequenceFdStep(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			
			for(Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					if(dev.getPowerDeviceName().endsWith("1")){
						it.remove();
					}
				}
			}
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
					Collections.reverse(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				String devid = zbdz.getPowerDeviceID();

				if(RuleExeUtil.isDeviceHadStatus(zbdz, "1", "0")&&!zbdz.getPowerDeviceName().contains("PT")){
					devid = zbdz.getPowerDeviceID();
					String devname = zbdz.getPowerDeviceName();
					String deviceType = zbdz.getDeviceType();
					
					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String cznr = "执行"+czname+CZPService.getService().getDevName(sw)+"由冷备用转热备用程序操作";
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上",deviceType,uuids});
						break;
					}else{
						String dzName = CZPService.getService().getDevName(zbdz);

						String cznr = "遥控合上"+czname+dzName;
						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});
						
						String check = "确认"+dzName+"处合上位置";
						data.add(new String[]{"","",czname,czname,check,"",devid,devname,"","",deviceType,uuids});
					}
				}
			}
		}
	}
	
	/*
	 * 传入刀闸获取序列
	 */
	public static void getSwitchSeparateSequenceTd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dz : dzList){
			if(dz.getPowerDeviceName().contains("站用变")){
				if(RuleExeUtil.isDeviceChanged(dz)){
					String devid =  dz.getPowerDeviceID();
					String devname = dz.getPowerDeviceName();

					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String dzName = CZPService.getService().getDevName(dz);
						String zybname = dzName.substring(0, dzName.indexOf("站用变")+3);
						
						String cznr = "将"+czname+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车由工作位置摇至试验位置";
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
					}else{
						String cznr = "遥控拉开"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
					}
				}
			}else{
				String devid = dz.getPowerDeviceID();
				String devname = dz.getPowerDeviceName();

				if(RuleExeUtil.isDeviceChanged(dz)){
					String cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
				}
			}
		}
	}
	
	/*
	 * 传入刀闸获取序列
	 */
	public static void getSwitchSeparateSequenceFd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dz : dzList){
			if(dz.getPowerDeviceName().contains("站用变")){
				if(RuleExeUtil.isDeviceChanged(dz)){
					String devid =  dz.getPowerDeviceID();
					String devname = dz.getPowerDeviceName();

					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String dzName = CZPService.getService().getDevName(dz);
						String zybname = dzName.substring(0, dzName.indexOf("站用变")+3);
						
						String cznr = "将"+czname+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车由试验位置摇至工作位置";
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
					}else{
						String cznr = "遥控合上"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
					}
				}
			}else{
				String devid = dz.getPowerDeviceID();
				String devname = dz.getPowerDeviceName();

				if(RuleExeUtil.isDeviceChanged(dz)){
					String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
				}
			}
		}
	}
	
	public static boolean ishh(PowerDevice device){
		boolean ishh = false;

		List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(device.getPowerStationID());
		
		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getPowerVoltGrade() == device.getPowerVoltGrade()){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					hignVoltMlkgList.add(dev);
				}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
					hignVoltXlkgList.add(dev);
				}
			}
		}
		
		List<PowerDevice> tempList = new ArrayList<PowerDevice>();
		
		tempList.addAll(hignVoltXlkgList);
		tempList.addAll(hignVoltMlkgList);

		for(PowerDevice dev : tempList){
			if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
				ishh = true;
			}
		}
		return ishh;
	}
	
	
	public static void getSwitchSequenceTdStepIn23JX(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(dzList.size() > 1){
				if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, dzList, "请选择先操作的隔离开关");
					
					PowerDevice firstDev = dcd.getChooseEquip();
					tempList.add(0,firstDev);
					
					for(PowerDevice dev : dzList){
						if(!tempList.contains(dev)){
							tempList.add(dev);
						}
					}
					
					dzList.clear();
					dzList.addAll(tempList);
				}else{
					if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, dzList, "请选择先操作的隔离开关");
						
						PowerDevice firstDev = dcd.getChooseEquip();
						tempList.add(0,firstDev);
						
						for(PowerDevice dev : dzList){
							if(!tempList.contains(dev)){
								tempList.add(dev);
							}
						}
						
						dzList.clear();
						dzList.addAll(tempList);
					}else{
						dzList = RuleExeUtil.sortByMXC(dzList);
						Collections.reverse(dzList);
					}
				}
			}
			
			String kgName = CZPService.getService().getDevName(sw);
			
			for(PowerDevice zbdz : dzList){
				if(RuleExeUtil.isDeviceChanged(zbdz)&&!zbdz.getPowerDeviceName().contains("PT")){
					String devid =  zbdz.getPowerDeviceID();
					String deviceType = zbdz.getDeviceType();
					String dzName = CZPService.getService().getDevName(zbdz);

					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String cznr  =  "遥控将"+czname+kgName+"由热备用转冷备用";
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,sw.getPowerDeviceName(),"","拉开",deviceType,uuids});
					}else{
						String cznr =  "遥控拉开"+czname+dzName;
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,zbdz.getPowerDeviceName(),"","拉开",deviceType,uuids});
						
						String check = "确认"+dzName+"处拉开位置";
						data.add(new String[]{"","",czname,czname,check,"",devid,zbdz.getPowerDeviceName(),"","",deviceType,uuids});
					}
				}
			}
		}
	}
	
	public static void getSwitchSequenceFdStepIn23JX(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			
			if(dzList.size() > 1){
				if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, dzList, "请选择先操作的隔离开关");
					
					PowerDevice firstDev = dcd.getChooseEquip();
					tempList.add(0,firstDev);
					
					for(PowerDevice dev : dzList){
						if(!tempList.contains(dev)){
							tempList.add(dev);
						}
					}
					
					dzList.clear();
					dzList.addAll(tempList);
				}else{
					if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, dzList, "请选择先操作的隔离开关");
						
						PowerDevice firstDev = dcd.getChooseEquip();
						tempList.add(0,firstDev);
						
						for(PowerDevice dev : dzList){
							if(!tempList.contains(dev)){
								tempList.add(dev);
							}
						}
						
						dzList.clear();
						dzList.addAll(tempList);
					}else{
						dzList = RuleExeUtil.sortByMXC(dzList);
					}
				}
			}
			
			String kgName = CZPService.getService().getDevName(sw);

			for(PowerDevice zbdz : dzList){
				String devid = zbdz.getPowerDeviceID();

				if(RuleExeUtil.isDeviceChanged(zbdz)){
					devid =  zbdz.getPowerDeviceID();
					String dzName = CZPService.getService().getDevName(zbdz);
					String deviceType = zbdz.getDeviceType();
					
					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String cznr = "遥控将"+czname+kgName+"由冷备用转热备用";
						data.add(new String[]{"","",ddname,czname,cznr,sw.getPowerDeviceName(),devid,"","","热备用",deviceType,uuids});
					}else{
						String cznr = "遥控合上"+czname+dzName;
						data.add(new String[]{"","",ddname,czname,cznr,zbdz.getPowerDeviceName(),devid,"","","合上",deviceType,uuids});
						
						String check = "确认"+dzName+"处合上位置";
						data.add(new String[]{"","",czname,czname,check,zbdz.getPowerDeviceName(),devid,dzName,"","",deviceType,uuids});
					}
				}
			}
		}
	}

	/**
	 * 2分之3接线，母线复电热备用转运行相关指令生成
	 * @param curDev 当前设备
	 * @param data 用于存储操作步骤的ArrayList
	 * @param isyk 是否是遥控
	 * @param startZT 开始状态
	 * @param deviceType 设备类型
	 * @param uuids uuid
	 */
	public static void getSwitchSequenceFdStepIn23JX1to0(PowerDevice curDev, ArrayList<String[]> data, String isyk, String startZT, String deviceType, String uuids) {
		PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station);
		String deviceName = CZPService.getService().getDevName(curDev);
		String ddname = "红河地调";

		List<PowerDevice> kgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

		RuleExeUtil.swapDeviceListNum(kgList);
		Collections.reverse(kgList);
		if(kgList.size() > 0){
			EquipCheckChoose ecc = new EquipCheckChoose(SystemConstants.getMainFrame(), true, kgList, "请选择给母线充电的断路器：");
			List<PowerDevice> chooseEquips = ecc.getChooseEquip();
			String devid;
			String devName;
			String cznr = "";
			if(chooseEquips.size() > 0){
				for(PowerDevice dev : chooseEquips){
					devid = dev.getPowerDeviceID();
					devName = CZPService.getService().getDevName(dev);
					cznr = "遥控合上" + stationName + devName + "对" + deviceName + "充电";
					data.add(new String[]{"","",ddname,stationName,cznr,isyk,devid,devName,startZT,"合上",deviceType,uuids});

					String check = "确认"+devName+"、"+deviceName + "运行正常";
					data.add(new String[]{"","",stationName,stationName,check,"",devid,devName,"","",deviceType,uuids});
				}
			}

			// 创建一个新的List来存储未被选择的设备
			List<PowerDevice> remainingDevices = new ArrayList<PowerDevice>(kgList);
			remainingDevices.removeAll(chooseEquips);

			// 使用剩余的设备列表进行遍历

			for(PowerDevice dev : remainingDevices){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					devid = dev.getPowerDeviceID();
					devName = CZPService.getService().getDevName(dev);
					try {
						// 原始赋值语句
						cznr = CommonFunctionHH.getHhContent(dev, "红河地调", stationName);

						// 提取@前的部分到ddname
						int atIndex = cznr.indexOf("@");
						if (atIndex != -1) {
							ddname = cznr.substring(0, atIndex);
						}

						// 去掉@和末尾的/r/n
						if (atIndex != -1 && cznr.endsWith("/r/n")) {
							cznr = cznr.substring(atIndex + 1, cznr.length() - 4);
						}
					} catch (Exception e) {
						System.err.println("字符串处理出错: " + e.getMessage());
					}
					data.add(new String[]{"","",ddname,stationName,cznr,isyk,devid,devName,startZT,"合上",deviceType,uuids});

					String check = "确认"+devName+"运行正常";
					data.add(new String[]{"","",stationName,stationName,check,"",devid,devName,"","",deviceType,uuids});
				}
			}
		}
	}

	/**
	 * 2分之3接线，母线停电运行转热备用相关指令生成
	 * @param curDev 当前设备
	 * @param data 用于存储操作步骤的ArrayList
	 * @param isyk 是否是遥控
	 * @param startZT 开始状态
	 * @param deviceType 设备类型
	 * @param uuids uuid
	 */
	public static void getSwitchSequenceTdStepIn23JX0to1(PowerDevice curDev, ArrayList<String[]> data, String isyk, String startZT, String deviceType, String uuids) {
		PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station);
		String deviceName = CZPService.getService().getDevName(curDev);
		String ddname = "红河地调";

		List<PowerDevice> kgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

		RuleExeUtil.swapDeviceListNum(kgList);
		Collections.reverse(kgList);

		String devid = "";
		String cznr = "";
		String devName = "";
		for(PowerDevice dev : kgList){
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
				devid = dev.getPowerDeviceID();
				devName = CZPService.getService().getDevName(dev);
				cznr = "遥控断开"+stationName+devName;
				data.add(new String[]{"","",ddname,stationName,cznr,isyk,devid,devName,startZT,"断开",deviceType,uuids});

				String check = "确认"+devName+"热备用";
				data.add(new String[]{"","",stationName,stationName,check,"",devid,devName,"","",deviceType,uuids});
			}
		}
	}
	
	public static void initDeviceRunType(String stationID) {
		Map<String, ArrayList<String>> devicePointMap = SystemConstants.getMapToplogyDevicePoint().get(stationID);
		Map<String, ArrayList<String>> pointDeviceMap = SystemConstants.getMapToplogyPointDevice().get(stationID);

		List<PowerDevice> zxdList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbList = new ArrayList<PowerDevice>();

		Map<String,PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationID);
		
		if(mapStationDevice != null){
			for(Iterator<PowerDevice> iter = mapStationDevice.values().iterator();iter.hasNext();) {
				PowerDevice device = iter.next();
				if(device.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
					if(device.getPowerDeviceName().contains("中性点")){
						zxdList.add(device);
					}else{
						String[] devNumArr = {"1010","1020","1030","2010","2020","2030"};
						
						for(String devNum : devNumArr){
							if(device.getPowerDeviceName().contains(devNum)){
								zxdList.add(device);
								break;
							}
						}
					}
				}else if(device.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(device);
				}else if(device.getDeviceType().equals(SystemConstants.SwitchSeparate)
						&&device.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
					String[] devNumArr = {"0901","0902","1901","1902","2901","2902","3901","3902"};
					
					for(String devNum : devNumArr){
						if(device.getPowerDeviceName().contains(devNum)){
							device.setDeviceRunType(CBSystemConstants.RunTypeKnifePT);
							break;
						}
					}
				}
			}
		}
		
		for(PowerDevice dev : zxdList){
			dev.setDeviceRunType(CBSystemConstants.RunTypeGroundZXDDD);
			
			if(devicePointMap != null){
				List<String> pointList = devicePointMap.get(dev.getPowerDeviceID());
				
				if(pointList != null && pointList.size() == 1){
					for(PowerDevice zb : zbList){
						String zbName = zb.getPowerDeviceName();
						zbName = StringUtils.killVoltInDevName(zbName);
						String devName = dev.getPowerDeviceName();
						String devNum = "";
						
						if(devName.contains("010")){
							devNum = "1";
						}else if(devName.contains("020")){
							devNum = "2";
						}else if(devName.contains("030")){
							devNum = "3";
						}
						
						if(zbName.contains(devNum)){
							List<String> deviceList = pointDeviceMap.get(pointList.get(0));
							deviceList.add(zb.getPowerDeviceID());
							List<String> otherpointList = devicePointMap.get(zb.getPowerDeviceID());
							otherpointList.add(pointList.get(0));
						}
					}
				}
			}
		}
	}

	/**
	 * 拆分倒母操作指令
	 * @param itemModelsShow 原始指令集合
	 * @return 拆分后的指令集合
	 */
	public static List<CardItemModel> splitCardDesc(List<CardItemModel> itemModelsShow) {
		List<CardItemModel> result = new ArrayList<CardItemModel>();

		for (CardItemModel item : itemModelsShow) {
			String cardDesc = item.getCardDesc();

			// 判断是否为需要拆分的指令（包含"遥控将"、"由"和"倒至"关键词）
			if (cardDesc.contains("执行") && cardDesc.contains("由") && cardDesc.contains("倒至") && cardDesc.contains("程序操作")) {
				// 提取设备列表
				List<String> devices = extractDevices(cardDesc);

				if (devices.size() > 1) {
					// 获取设备前后的通用部分
					String prefix = extractPrefix(cardDesc);
					String middle = extractMiddle(cardDesc);
					String suffix = extractSuffix(cardDesc);

					// 为每个设备创建单独的指令
					for (String device : devices) {
						CardItemModel newItem = cloneCardItem(item);
						newItem.setCardDesc(prefix + device + middle + suffix);
						result.add(newItem);
					}
				} else {
					// 如果只有一个设备，不需要拆分
					result.add(item);
				}
			}
			// 判断是否包含罗马数字或数字的格式（如"220kVⅠ、Ⅲ母"）
			/*else if (cardDesc.contains("执行") && cardDesc.contains("由") && cardDesc.contains("转")
					&& cardDesc.contains("程序操作") && containsNumberPattern(cardDesc)) {
				// 处理特殊格式的拆分
				List<String> splitDescriptions = splitNumberPattern(cardDesc);

				// 为每个拆分后的描述创建新的CardItemModel
				for (String splitDesc : splitDescriptions) {
					CardItemModel newItem = cloneCardItem(item);
					newItem.setCardDesc(splitDesc);
					result.add(newItem);
				}
			}*/
			else {
				// 不需要拆分的指令直接添加
				result.add(item);
			}
		}

		return result;
	}

	/**
	 * 提取设备列表
	 * @param cardDesc 指令描述
	 * @return 设备列表
	 */
	private static List<String> extractDevices(String cardDesc) {
		List<String> devices = new ArrayList<String>();

		// 使用正则表达式提取"遥控将"和"由"之间的内容
		Pattern pattern = Pattern.compile("遥控将(.*?)由");
		Matcher matcher = pattern.matcher(cardDesc);

		if (matcher.find()) {
			String devicesStr = matcher.group(1);
			// 按顿号或者逗号拆分设备
			String[] deviceArray = devicesStr.split("[、，,]");
			for (String device : deviceArray) {
				if (!device.trim().isEmpty()) {
					devices.add(device.trim());
				}
			}
		}

		return devices;
	}

	/**
	 * 提取指令前缀（"遥控将"）
	 * @param cardDesc 指令描述
	 * @return 指令前缀
	 */
	private static String extractPrefix(String cardDesc) {
		return "遥控将";
	}

	/**
	 * 提取中间部分（"由"到第一个设备之后）
	 * @param cardDesc 指令描述
	 * @return 中间部分
	 */
	private static String extractMiddle(String cardDesc) {
		// 查找"由"的位置
		int byIndex = cardDesc.indexOf("由");

		if (byIndex != -1) {
			return "由" + cardDesc.substring(byIndex + 1, cardDesc.indexOf("倒至")) + "倒至";
		}

		return "由";
	}

	/**
	 * 提取后缀（"倒至"之后的内容）
	 * @param cardDesc 指令描述
	 * @return 后缀
	 */
	private static String extractSuffix(String cardDesc) {
		int toIndex = cardDesc.indexOf("倒至");

		if (toIndex != -1) {
			return cardDesc.substring(toIndex + 2);
		}

		return "";
	}

	/**
	 * 判断字符串是否包含需要特殊拆分的数字模式（如"220kVⅠ、Ⅲ母"）
	 */
	private static boolean containsNumberPattern(String text) {
		String regex = "(.*?)(\\d+|[A-Z]+|[Ⅰ-Ⅻ])、(\\d+|[A-Z]+|[Ⅰ-Ⅻ])(.*?)";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(text);
		return matcher.find();
	}
	/**
	 * 拆分包含特定数字模式的字符串
	 */
	private static List<String> splitNumberPattern(String text) {
		List<String> result = new ArrayList<String>();

		// 定义正则表达式
		String regex = "(.*?)(\\d+|[A-Z]+|[Ⅰ-Ⅻ])、(\\d+|[A-Z]+|[Ⅰ-Ⅻ])(.*)";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(text);

		if (matcher.find()) {
			String prefix = matcher.group(1);
			String first = matcher.group(2);
			String second = matcher.group(3);
			String suffix = matcher.group(4);

			result.add(prefix + first + suffix);
			result.add(prefix + second + suffix);
		} else {
			// 如果不匹配模式，返回原字符串
			result.add(text);
		}

		return result;
	}


	/**
	 * 克隆卡片项目（深拷贝）
	 * @param original 原始卡片项目
	 * @return 克隆后的卡片项目
	 */
	private static CardItemModel cloneCardItem(CardItemModel original) {
		CardItemModel clone = new CardItemModel();
		// 复制所有字段
		clone.setCardDesc(original.getCardDesc());
		// 复制其他字段...
		clone.setCardNum(original.getCardNum());
		clone.setCardItem(original.getCardItem());
		clone.setStationName(original.getStationName());
		clone.setShowName(original.getShowName());
		clone.setUuIds(original.getUuIds());
		clone.setBzbj(original.getBzbj());
		clone.setRemark(original.getRemark());
		clone.setTwoDevice(original.getTwoDevice());
		clone.setIssk(original.isIssk());
		clone.setOrderNumber(original.getOrderNumber());
		clone.setBdzName(original.getBdzName());
		clone.setZlxh(original.getZlxh());
		clone.setCzdwID(original.getCzdwID());
		clone.setPreMxid(original.getPreMxid());
		// clone.setXxx(original.getXxx());

		return clone;
	}

	public static List<Map<String, String>> processDeviceList(List<Map<String, String>> inputList) {
		List<Map<String, String>> resultList = new ArrayList<Map<String, String>>();

		for (Map<String, String> deviceMap : inputList) {
			// 检查是否包含"设备名称"键
			if (deviceMap.containsKey("设备名称")) {
				String deviceName = deviceMap.get("设备名称");
				List<String> splitNames = splitDeviceName(deviceName);

				// 如果设备名称需要拆分（返回的列表长度大于1）
				if (splitNames.size() > 1) {
					for (String name : splitNames) {
						// 创建新的Map并复制所有原始键值对
						Map<String, String> newDeviceMap = new HashMap<String, String>(deviceMap);
						// 更新设备名称
						newDeviceMap.put("设备名称", name);
						resultList.add(newDeviceMap);
					}
				} else {
					// 如果设备名称不需要拆分，直接添加原始Map
					resultList.add(deviceMap);
				}
			} else {
				// 如果不包含"设备名称"键，直接添加原始Map
				resultList.add(deviceMap);
			}
		}

		return resultList;
	}

	private static List<String> splitDeviceName(String deviceName) {
		List<String> result = new ArrayList<String>();

		// 定义正则表达式，匹配形如"前缀+数字/英文/罗马数字+、+数字/英文/罗马数字+后缀"的模式
		String regex = "^(.*?)(\\d+|[A-Z]+|[Ⅰ-Ⅻ])、(\\d+|[A-Z]+|[Ⅰ-Ⅻ])(.*?)$";

		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(deviceName);

		if (matcher.find()) {
			String prefix = matcher.group(1);
			String first = matcher.group(2);
			String second = matcher.group(3);
			String suffix = matcher.group(4);

			result.add(prefix + first + suffix);
			result.add(prefix + second + suffix);
		} else {
			// 如果不匹配模式，返回原字符串
			result.add(deviceName);
		}

		return result;
	}
}
