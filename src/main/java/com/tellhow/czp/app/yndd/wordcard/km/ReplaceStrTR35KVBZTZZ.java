package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTR35KVBZTZZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("投入35kV备自投装置".equals(tempStr)){
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getPowerVoltGrade() == 35){
						if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")&&RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							dycmlkgList.add(dev);
						}
					}
				}
			}
			
			if(dycmlkgList.size()>1){//多段母线
				for(PowerDevice dycmlkg : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("1")||RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("0")){
						replaceStr += "投入"+CZPService.getService().getDevName(dycmlkg)+"备自投装置/r/n";
					}
				}
			}else if(dycmlkgList.size()==1){
				replaceStr += "投入35kV备自投装置/r/n";
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
