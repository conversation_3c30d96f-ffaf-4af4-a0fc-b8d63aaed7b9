package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;

public class KGFDDFSExecute implements RulebaseInf {
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		
		PowerDevice pd=rbm.getPd();
		
		if(pd==null)
			return false;

		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

			if(mxList.size() > 0){
				for (PowerDevice mx : mxList) {
					List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(mx, pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
					List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> kgList = new ArrayList<PowerDevice>();
					
					if(mx.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
						if(mlkgList.size() > 0){//单母分段
							PowerDevice otherMX = RuleUtil.getAnotherMotherLine(mlkgList.get(0), mx);
							
							if(otherMX.getPowerVoltGrade() == mx.getPowerVoltGrade()){
								List<PowerDevice> otherswxlList = RuleExeUtil.getDeviceList(otherMX, pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
								for(PowerDevice sw : otherswxlList) {
									if(sw.getDeviceStatus().equals("0")){
										xlkgList.add(sw);
									}
								}
							}
						}else{
							List<PowerDevice> otherswxlList = RuleExeUtil.getDeviceList(mx, pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
							for(PowerDevice sw : otherswxlList) {
								if(sw.getDeviceStatus().equals("0")){
									xlkgList.add(sw);
								}
							}
						}
					}
					
					kgList.addAll(mlkgList);
					kgList.addAll(xlkgList);
					
					List<String> defaultStatusList = new ArrayList<String>();
					
					for(PowerDevice dev : kgList) {
						defaultStatusList.add("0");
					}
					
					if(xlkgList.size()>0){
						Map tagStatusMap = new HashMap();
						if(CBSystemConstants.isCurrentSys) {
							EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, kgList, defaultStatusList, "如需转电请选择线路开关要转换的状态：");
							tagStatusMap=dialog.getTagStatusMap();
						}
						
						if(tagStatusMap.size()>0){
						   List<Map.Entry<PowerDevice,String>> list = new ArrayList<Map.Entry<PowerDevice,String>>(tagStatusMap.entrySet());
					        //然后通过比较器来实现排序
					        Collections.sort(list,new Comparator<Map.Entry<PowerDevice,String>>() {
					            //升序排序
					            public int compare(Entry<PowerDevice, String> o1,
					                    Entry<PowerDevice, String> o2) {
					                return o1.getValue().compareTo(o2.getValue());
					            }
					        });
							
					        for(Map.Entry<PowerDevice,String> entry:list){
					    		if(!entry.getKey().getDeviceStatus().equals(entry.getValue())){//改变了才做操作
									RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(),entry.getValue());
								}
							}
						}else{
							return false;
						}
					}
				}
			}else{
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				List<PowerDevice> kgList = new ArrayList<PowerDevice>();
				
				kgList.addAll(mlkgList);
				kgList.addAll(xlkgList);
				
				List<String> defaultStatusList = new ArrayList<String>();
				
				for(PowerDevice dev : kgList) {
					defaultStatusList.add("0");
				}
				
				if(mlkgList.size()>0){
					Map tagStatusMap = new HashMap();
					if(CBSystemConstants.isCurrentSys) {
						EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, kgList,defaultStatusList, "如需转电请选择线路开关要转换的状态：");
						tagStatusMap = dialog.getTagStatusMap();
					}
					
					if(tagStatusMap.size()>0){
					   List<Map.Entry<PowerDevice,String>> list = new ArrayList<Map.Entry<PowerDevice,String>>(tagStatusMap.entrySet());
				        //然后通过比较器来实现排序
				        Collections.sort(list,new Comparator<Map.Entry<PowerDevice,String>>() {
				            //升序排序
				            public int compare(Entry<PowerDevice, String> o1,
				                    Entry<PowerDevice, String> o2) {
				                return o1.getValue().compareTo(o2.getValue());
				            }
				        });
						
				        for(Map.Entry<PowerDevice,String> entry:list){
				    		if(!entry.getKey().getDeviceStatus().equals(entry.getValue())){//改变了才做操作
								RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(),entry.getValue());
							}
						}
					}else{
						return false;
					}
				}
			}
		}
		
		return true;
	}

}
