package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJZYBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖站用变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			
			String begin = CBSystemConstants.getCurRBM().getBeginStatus();
			String end = CBSystemConstants.getCurRBM().getEndState();
			
			if(begin.equals("0")){
				if(end.equals("1")){
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}else if(end.equals("2")){
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}

					if(curDev.getDeviceStatus().equals("2")){
						replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
					}
				}
			}else if(begin.equals("1")){
				if(curDev.getDeviceStatus().equals("2")){
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
