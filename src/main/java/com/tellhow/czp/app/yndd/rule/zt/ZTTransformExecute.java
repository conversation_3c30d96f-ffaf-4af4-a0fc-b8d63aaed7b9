package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.EquipRadioChoose;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;

public class ZTTransformExecute implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		boolean result = true;
		PowerDevice pd = rbm.getPd();
		
		if(rbm.getBeginStatus().equals("0")) { //运行转热备用
			setOtherZxdOn(pd);
			ConvertSwitchOnToHotTS(pd);
			result = ConvertSwitchOnToHot(pd);
			
			if(!result)
				return result;
		}
		else if(rbm.getBeginStatus().equals("1") && rbm.getEndState().equals("2")) { //热备用转冷备用
			ConvertSwitchHotToColdTS(pd);
			result = ConvertSwitchHotToCold(pd);
			setCurZxdOff(pd);
			
			if(!result)
				return result;
		}
		else if(rbm.getBeginStatus().equals("2") && rbm.getEndState().equals("1")) { //冷备用转热备用
			setCurZxdOn(pd);
			ConvertSwitchColdToHotTS(pd);
			result = ConvertSwitchColdToHot(pd);
			
			if(!result)
				return result;
		}else if(rbm.getEndState().equals("0")) { //热备用转运行
			ConvertSwitchHotToOnTS(pd);
			result = ConvertSwitchHotToOn(pd);
			setCurZxdOff(pd);
			
			if(!result)
				return result;
		}
		
		return true;
	}
	
	/*
	 * 特殊接线运行转热备用
	 */
	
	public boolean ConvertSwitchOnToHotTS(PowerDevice pd) {
		
		return true;
	}
	
	/*
	 * 运行转热备用
	 */
	
	public boolean ConvertSwitchOnToHot(PowerDevice pd) {
		List<PowerDevice> zbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(pd);
		List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(pd);
		List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(pd);
		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> gycmxList= new ArrayList<PowerDevice>();
		List<PowerDevice> zycmxList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
		
		if(zbzyckgList.size()>0){
			for(PowerDevice zbzyckg : zbzyckgList){
				zycmlkgList.addAll(getMlkgList(zbzyckg));
			}
		}
		
		if(zbdyckgList.size()>0){
			dycmxList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			
			for(PowerDevice zbdyckg : zbdyckgList){
				List<PowerDevice> tempList = getMlkgList(zbdyckg);

				for(PowerDevice temp : tempList){
					if(!dycmlkgList.contains(temp)){
						dycmlkgList.add(temp);
					}
				}
			}
		}
		
		if(zbzyckgList.size()>0){
			zycmxList = RuleExeUtil.getDeviceList(zbzyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
		}
		
		if(zbgyckgList.size()>0){
			gycmxList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			
			if(gycmxList.size()>0){
				xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
				gycmlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
			}else{
				List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(pd);
				
				for(PowerDevice zbgycdz : zbgycdzList){
					gycmxList = RuleExeUtil.getDeviceList(zbgycdz, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				}
				
				if(gycmxList.size()>0){//西双版纳内桥接线无高压侧母线
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					gycmlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}else{
					for(PowerDevice zbgycdz : zbgycdzList){
						xlkgList = RuleExeUtil.getDeviceList(zbgycdz,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,CBSystemConstants.RunTypeSwitchML, false, true, true, true,true);
						gycmlkgList = RuleExeUtil.getDeviceList(zbgycdz,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
					}
				}
			}
		}else{
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);

			if(kfList.size() > 0){
				gycmxList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.MotherLine, SystemConstants.Switch, false, true, true);
				
				if(gycmxList.size()>0){
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					
					for(PowerDevice dev : gycmxList){
						 List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
						 
						 gycmlkgList.addAll(tempList);
					}
				}else{
					gycmlkgList = RuleExeUtil.getDeviceList(kfList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}
		}
		
		PowerDevice tagzb = new PowerDevice();//关联的主变
		
		if(dycmlkgList.size()==2){//存在2个低压侧分段开关
			if(zbdyckgList.size()==2){
				for(PowerDevice dycmlkg : dycmlkgList){
					RuleExeUtil.deviceStatusExecute(dycmlkg, dycmlkg.getDeviceStatus(), "0");
				}
			}else{
				List<PowerDevice> zbList = new ArrayList<PowerDevice>();
				
				if(zycmlkgList.size()==1){
					for(PowerDevice zycmlkg : zycmlkgList){
						List<PowerDevice> tempzycmxList = RuleExeUtil.getDeviceList(zycmlkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

						for(PowerDevice tempzycmx : tempzycmxList){
							if(!zycmxList.contains(tempzycmx)){
								List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(tempzycmx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
								zbList.addAll(tempzbList);
							}
						}
					}
				}else{
					for(PowerDevice dycmlkg : dycmlkgList){
						List<PowerDevice> tempdycmxList = RuleExeUtil.getDeviceList(dycmlkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

						for(PowerDevice tempdycmx : tempdycmxList){
							if(!dycmxList.contains(tempdycmx)){
								List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(tempdycmx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
								zbList.addAll(tempzbList);
							}
						}
					}
				}
				
				if(zbList.size() > 1) {
					//还要考虑中压侧分段开关的个数
					String showMessage="请选择需要合上的分段断路器";

					EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, dycmlkgList , showMessage);
					List<PowerDevice> chooseEquips=ecc.getChooseEquip();
					if(chooseEquips.size()==0)
						return false;
					
					for(PowerDevice dev : chooseEquips){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
				}else if(zbList.size() == 1){
					for(PowerDevice zb : zbList){
						List<PowerDevice> tempzbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(zb);

						if(zbdyckgList.size()>0&&tempzbdyckgList.size()>0){
							List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(zbdyckgList.get(0),tempzbdyckgList.get(0) , "", "", true, false);
							
							for(PowerDevice pathDev : pathList){
								if(pathDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									RuleExeUtil.deviceStatusExecute(pathDev, pathDev.getDeviceStatus(), "0");
								}
							}
						}
					}
				}
			}
		}else if(dycmlkgList.size()==1){//三主变厂站的边主变、或者两主变
			for(PowerDevice dev : dycmlkgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);

				for(PowerDevice mx : mxList){
					List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice tempzb : tempzbList){
						if(!tempzb.getPowerDeviceID().equals(pd.getPowerDeviceID())){
							tagzb  = tempzb;
						}
					}
				}
				
				if(dev.getDeviceStatus().equals("0")){//如果低压侧分段开关为合上状态，可能需要合上相关主变低压侧开关
					for(PowerDevice mx : mxList){
						boolean flag = true;
						
						List<PowerDevice> tempzbdyckgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
						
						for(PowerDevice tempzbdyckg : tempzbdyckgList){
							if(!zbdyckgList.contains(tempzbdyckg)&&tempzbdyckg.getDeviceStatus().equals("0")){
								flag = true;
							}
						}
						
						if(flag){
							for(PowerDevice tempzbdyckg : tempzbdyckgList){
								if(!zbdyckgList.contains(tempzbdyckg)&&tempzbdyckg.getDeviceStatus().equals("1")){
									RuleExeUtil.deviceStatusExecute(tempzbdyckg, tempzbdyckg.getDeviceStatus(), "0");
								}
							}
						}
					}
				}else{
					List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
					
					for(PowerDevice dycmx : dycmxList){
						if(dycmx.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
							List<PowerDevice> zbList = RuleExeUtil.getDeviceList(dycmx, SystemConstants.PowerTransformer, "", true, true, true);

							for(PowerDevice zb : zbList){
								if(!zb.getPowerDeviceID().equals(pd.getPowerDeviceID())&&!zb.getPowerDeviceName().contains("接地变")){
									otherzbList.add(zb);
								}
							}
						}
					}

					if(otherzbList.size()>0){//如果母线上存在2个主变，需要先合另一台主变的低压侧开关，不合低压侧分段开关
						for(PowerDevice otherzb : otherzbList){
							List<PowerDevice> otherzbdyckgList = RuleExeUtil.getTransformerSwitchLow(otherzb);
							
							for(PowerDevice otherzbdyckg : otherzbdyckgList){
								RuleExeUtil.deviceStatusExecute(otherzbdyckg, otherzbdyckg.getDeviceStatus(), "0");
							}
						}
					}else{
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
				}
			}
		}
		
		setDrDkZyb(pd,"1");
		
		for(PowerDevice dev : zbdyckgList){
			if(dev.getDeviceStatus().equals("0")){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
		}
		
		if(zycmlkgList.size() == 2){
			if(dycmlkgList.size()==1){//如果低压侧分段开关只有一个
				List<PowerDevice> zbList = new ArrayList<PowerDevice>();
				
				for(PowerDevice dycmlkg : dycmlkgList){
					List<PowerDevice> tempdycmxList = RuleExeUtil.getDeviceList(dycmlkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

					for(PowerDevice tempdycmx : tempdycmxList){
						if(!dycmxList.contains(tempdycmx)){
							List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(tempdycmx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
							zbList.addAll(tempzbList);
						}
					}
				}

				for(PowerDevice zb : zbList){
					List<PowerDevice> tempzbzyckgList =	 RuleExeUtil.getTransformerSwitchMiddle(zb);

					if(zbzyckgList.size()>0&&tempzbzyckgList.size()>0){
						List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(zbzyckgList.get(0),tempzbzyckgList.get(0) , "", "", true, false);
						
						for(PowerDevice pathDev : pathList){
							if(pathDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								RuleExeUtil.deviceStatusExecute(pathDev, pathDev.getDeviceStatus(), "0");
							}
						}
					}
				}
			}else{
				EquipRadioChoose erc = new EquipRadioChoose(SystemConstants.getMainFrame(), true, zycmlkgList,  "请选择需要合上的分段断路器：",true);
				PowerDevice kgChangeOn = erc.getChooseEquip();
				
				if(erc.isCancel()||kgChangeOn== null)
					return false;
				
				RuleExeUtil.deviceStatusExecute(kgChangeOn, kgChangeOn.getDeviceStatus(), "0");
			}
		}else{
			for(PowerDevice dev : zycmlkgList){
				if(dev.getDeviceStatus().equals("0")){//如果低压侧分段开关为断开状态，可能需要合上相关主变低压侧开关
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);

					for(PowerDevice mx : mxList){
						boolean flag = true;
						
						List<PowerDevice> tempzbdyckgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
						
						for(PowerDevice tempzbdyckg : tempzbdyckgList){
							if(!zbdyckgList.contains(tempzbdyckg)&&tempzbdyckg.getDeviceStatus().equals("0")){
								flag = true;
							}
						}
						
						if(flag){
							for(PowerDevice tempzbdyckg : tempzbdyckgList){
								if(!zbdyckgList.contains(tempzbdyckg)&&tempzbdyckg.getDeviceStatus().equals("1")){
									RuleExeUtil.deviceStatusExecute(tempzbdyckg, tempzbdyckg.getDeviceStatus(), "0");
								}
							}
						}
					}
				}else{
					List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
					
					for(PowerDevice zycmx : zycmxList){
						if(zycmx.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
							List<PowerDevice> zbList = RuleExeUtil.getDeviceList(zycmx, SystemConstants.PowerTransformer, "", false, true, true);

							for(PowerDevice zb : zbList){
								if(!zb.getPowerDeviceID().equals(pd.getPowerDeviceID())){
									otherzbList.add(zb);
								}
							}
						}
					}

					if(otherzbList.size()>0){
						for(PowerDevice otherzb : otherzbList){
							List<PowerDevice> otherzbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(otherzb);
							
							for(PowerDevice otherzbzyckg : otherzbzyckgList){
								RuleExeUtil.deviceStatusExecute(otherzbzyckg, otherzbzyckg.getDeviceStatus(), "0");
							}
						}
					}else{
						Boolean isneedop = true;
						
						if(!tagzb.getPowerDeviceID().equals("")){
							List<PowerDevice> tagzbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(tagzb);

							for(PowerDevice tagzbgyckg : tagzbgyckgList){
								List<PowerDevice> taggycmxList = RuleExeUtil.getDeviceList(tagzbgyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
								
								for(PowerDevice taggycmx : taggycmxList){
									if(gycmxList.contains(taggycmx)){
										isneedop = false;
									}
								}
							}
							
							if(isneedop){
								RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
							}
							
							if(gycmlkgList.size()==0){
								RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
							}
							
							for(PowerDevice gycmlkg : gycmlkgList){
								if(gycmlkg.getDeviceStatus().equals("0")){
									RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
								}
							}
						}
					}
				}
			}
		}
		
		for(PowerDevice dev : zbzyckgList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
		}
		
		for(Iterator<PowerDevice> itor = zbgyckgList.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
				itor.remove();
			}
		}
		
		if(zbgyckgList.size() == 2){
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}
		}else{
			for(PowerDevice dev : zbgyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
		}
		
		if(gycmxList.size()>0){
			if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母接线
				if(pd.getPowerVoltGrade() == 220){
					for(PowerDevice dev : gycmlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}else if(pd.getPowerVoltGrade() == 110){
					if(!tagzb.getPowerDeviceID().equals("")){
						Boolean isneedop = true;
						
						List<PowerDevice> tagzbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(tagzb);

						for(PowerDevice tagzbgyckg : tagzbgyckgList){
							List<PowerDevice> taggycmxList = RuleExeUtil.getDeviceList(tagzbgyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
							
							for(PowerDevice taggycmx : taggycmxList){
								if(gycmxList.contains(taggycmx)){
									isneedop = false;
								}
							}
						}
						
						if(isneedop&&zbzyckgList.size()>0){
							for(PowerDevice dev : gycmlkgList){
								RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
							}
						}
					}
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){//单母接线
				if(gycmlkgList.size()>0&&!RuleUtil.isTransformerNQ(pd)&&!RuleUtil.isTransformerKDNQ(pd)){
					for(PowerDevice dev : gycmlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
						}
					}
				}
			}
		}
		
		if(RuleUtil.isTransformerNQ(pd)){//内桥接线
			boolean bcxlkgrby = false;
			
			for(PowerDevice dev : xlkgList){
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
					bcxlkgrby = true;
				}
			}
			
			if(!bcxlkgrby){
				List<PowerDevice> firsthsswlist = new ArrayList<PowerDevice>();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
						if(dev.getDeviceStatus().equals("1")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								firsthsswlist.add(dev);
							}
						}
					}
				}
				
				for(PowerDevice dev : firsthsswlist){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
			
			for(PowerDevice dev : xlkgList){
				if(dev.getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}

			for(PowerDevice dev : gycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
		}
		
		if(RuleUtil.isTransformerKDNQ(pd)){//扩大内桥接线
			List<PowerDevice> firsthsswlist = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")&&!xlkgList.contains(dev)){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							firsthsswlist.add(dev);
						}
					}
				}
			}
			
			for(PowerDevice dev : firsthsswlist){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			//合高压侧内桥开关
			for(PowerDevice dev : zycmlkgList){
				List<PowerDevice>  otherzbList = new ArrayList<PowerDevice>();
				List<PowerDevice>  mlkgList = new ArrayList<PowerDevice>();

				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);

				for(PowerDevice mx : mxList){
					List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					for(PowerDevice tempzb : tempzbList){
						otherzbList.add(tempzb);
					}
				}
				
				List<PowerDevice> path = RuleExeUtil.getTransformersLinkDevice(otherzbList.get(0), otherzbList.get(1));
				if(path != null) {
					for (PowerDevice device : path) {
						if(device.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
							mlkgList.add(device);
						}
					}
				}
				
				for(PowerDevice mlkg : mlkgList){
					RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				List<PowerDevice>  otherzbList = new ArrayList<PowerDevice>();
				List<PowerDevice>  mlkgList = new ArrayList<PowerDevice>();

				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);

				for(PowerDevice mx : mxList){
					List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					for(PowerDevice tempzb : tempzbList){
						otherzbList.add(tempzb);
					}
				}
				
				List<PowerDevice> path = RuleExeUtil.getTransformersLinkDevice(otherzbList.get(0), otherzbList.get(1));
				if(path != null) {
					for (PowerDevice device : path) {
						if(device.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
							mlkgList.add(device);
						}
					}
				}
				
				for(PowerDevice mlkg : mlkgList){
					RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
				}
			}
		}

		if(RuleExeUtil.isTransformerXBDY(pd)||RuleExeUtil.isTransformerXBZ(pd)){
			for(PowerDevice dev : zbdyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbzyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbgyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
		}
		
		if(dycmlkgList.size()==0){
			for(PowerDevice dev : dycmxList){
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);

				if(zbkgList.size() == 1){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}
		}
		
		if(RuleExeUtil.getDeviceDirectList(pd, "").size() == 0){
			RuleExeUtil.deviceStatusSet(pd, pd.getDeviceStatus(), "1");
		}
		
		return true;
	}
	

	/*
	 * 热备用转冷备用特殊
	 */
	
	public boolean ConvertSwitchHotToColdTS(PowerDevice pd) {
		
		return true;
	}
	
	/*
	 * 热备用转冷备用
	 */
	
	public boolean ConvertSwitchHotToCold(PowerDevice pd) {
		setDrDkZyb(pd,"2");
		
		List<PowerDevice> highswList = RuleExeUtil.getTransformerSwitchHigh(pd);
		List<PowerDevice> midswList = RuleExeUtil.getTransformerSwitchMiddle(pd);
		List<PowerDevice> lowswList = RuleExeUtil.getTransformerSwitchLow(pd);
		List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();

		List<PowerDevice> gycmxList = new ArrayList<PowerDevice>();
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();

		
		if(highswList.size()>0){
			gycmxList = RuleExeUtil.getDeviceList(highswList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			
			if(gycmxList.size()>0){
				xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
				gycmlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
			}else{
				List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(pd);
				
				for(PowerDevice zbgycdz : zbgycdzList){
					gycmxList = RuleExeUtil.getDeviceList(zbgycdz, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				}
				
				if(gycmxList.size()>0){
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					gycmlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}
		}else{
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);

			if(kfList.size() > 0){
				gycmxList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.MotherLine, SystemConstants.Switch, false, true, true);
				
				if(gycmxList.size()>0){
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					
					for(PowerDevice dev : gycmxList){
						 List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
						 
						 gycmlkgList.addAll(tempList);
					}
				}else{
					gycmlkgList = RuleExeUtil.getDeviceList(kfList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}
		}
		
		if(lowswList.size()>0){
			dycmxList = RuleExeUtil.getDeviceList(lowswList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			
			for(PowerDevice zbdyckg : lowswList){
				List<PowerDevice> tempList = getMlkgList(zbdyckg);

				for(PowerDevice temp : tempList){
					if(!dycmlkgList.contains(temp)){
						dycmlkgList.add(temp);
					}
				}
			}
		}
		
		for(PowerDevice dev : lowswList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
		}
		
		for(PowerDevice dev : midswList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
		}
		
		for(PowerDevice dev : highswList){
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
			}
		}
		
		if(RuleUtil.isTransformerKDNQ(pd)||RuleUtil.isTransformerNQ(pd)){
			// 拉开主变刀闸
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
			for (PowerDevice kf : kfList) {
				RuleExeUtil.deviceStatusExecute(kf, kf.getDeviceStatus(), "1");
			}
			
			List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
			
			if(xlkgList.size()==0){
				for (PowerDevice kf : kfList) {
					xlkgList = RuleExeUtil.getDeviceList(kf,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, true, true,true);
				}
			}
			
			if(gycmlkgList.size()==0){
				for (PowerDevice kf : kfList) {
					gycmlkgList = RuleExeUtil.getDeviceList(kf,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}
			
			for(PowerDevice dev : xlkgList){
				if(dev.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
		}
		
		if(RuleExeUtil.isTransformerXBDY(pd)||RuleExeUtil.isTransformerXBZ(pd)){
			for(PowerDevice dev : highswList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
			}
		}
		
		if(dycmlkgList.size()==0){
			for(PowerDevice dev : dycmxList){
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);

				if(zbkgList.size() == 1){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
				}
			}
		}
		
		if(RuleExeUtil.getDeviceDirectList(pd, "").size() == 0){
			RuleExeUtil.deviceStatusSet(pd, pd.getDeviceStatus(), "2");
		}
		
		return true;
	}
	
	/*
	 * 冷备用转热备用
	 */
	
	public boolean ConvertSwitchColdToHotTS(PowerDevice pd) {
		return true;
	}
	
	/*
	 * 冷备用转热备用
	 */
	
	public boolean ConvertSwitchColdToHot(PowerDevice pd) {
		List<PowerDevice> zbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(pd);
		List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(pd);
		List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(pd);
		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> gycmxList = new ArrayList<PowerDevice>();

		if(zbgyckgList.size()>0){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			gycmlkgList.addAll(kgList);
		}else{
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			gycmlkgList.addAll(kgList);
		}
		
		if(zbzyckgList.size()>0){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbzyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			zycmlkgList.addAll(kgList);
		}
		
		if(zbdyckgList.size()>0){
			dycmxList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			
			for(Iterator<PowerDevice> itor = kgList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				
				if(tempmxList.size()<2){
					itor.remove();
				}
			}
			
			dycmlkgList.addAll(kgList);
		}
		
		if(zbgyckgList.size()>0){
			gycmxList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false,  true, true, true);
			
			if(gycmxList.size()>0){
				xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
			}
		}
		
		if(RuleUtil.isTransformerNQ(pd)){
			for(PowerDevice dev : gycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : xlkgList){
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice line : lineList){
					if(line.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
			}
			
			// 合上主变刀闸
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
			for (PowerDevice kf : kfList) {
				RuleExeUtil.deviceStatusExecute(kf, kf.getDeviceStatus(), "0");
			}
			
			
			for(PowerDevice dev : zbzyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbdyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			setDrDkZyb(pd,"1");
			
		}
		
		if(gycmxList.size()>0){
			if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母接线
				if(pd.getPowerVoltGrade() == 220){
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					for(PowerDevice dev : zbzyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					for(PowerDevice dev : zbdyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					setDrDkZyb(pd, "1");
				}else if(pd.getPowerVoltGrade() == 110){
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					for(PowerDevice dev : zbzyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					for(PowerDevice dev : zbdyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){//单母接线
				for(PowerDevice dev : zbgyckgList){
					if(dev.getDeviceStatus().equals("2")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
				
				for(PowerDevice dev : zbdyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
				for(PowerDevice dev : zbgyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
				
				for(PowerDevice dev : zbzyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
				
				for(PowerDevice dev : zbdyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
				
				setDrDkZyb(pd, "1");
			}
		}
		
		if(RuleExeUtil.isTransformerXBDY(pd)||RuleExeUtil.isTransformerXBZ(pd)){
			for(PowerDevice dev : zbgyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbzyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(CBSystemConstants.LineTagStatus.containsKey(dev)){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), CBSystemConstants.LineTagStatus.get(dev));
				}else{
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}			
			}
		}
		
		return true;
	}
	
	/*
	 * 热备用转运行特殊
	 */
	
	public boolean ConvertSwitchHotToOnTS(PowerDevice pd) {
		
		return true;
	}
	
	
	/*
	 * 热备用转运行
	 */
	
	public boolean ConvertSwitchHotToOn(PowerDevice pd) {
		List<PowerDevice> zbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(pd);
		List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(pd);
		List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(pd);
		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> gycmxList = new ArrayList<PowerDevice>();
		
		if(zbgyckgList.size()>0){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			gycmlkgList.addAll(kgList);
		}else{
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			gycmlkgList.addAll(kgList);
		}
		
		if(zbzyckgList.size()>0){
			List<PowerDevice> kgList = getMlkgList(zbzyckgList.get(0));
			zycmlkgList.addAll(kgList);
		}
		
		if(zbdyckgList.size()>0){
			List<PowerDevice> kgList = getMlkgList(zbdyckgList.get(0));
			
			dycmlkgList.addAll(kgList);
		}
		
		if(zbgyckgList.size()>0){
			gycmxList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false,  false, true, true);
			
			if(gycmxList.size()>0){
				xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
			}
		}
		
		if(gycmxList.size()>0){
			if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母接线
				if(pd.getPowerVoltGrade() == 220){
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbzyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbdyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					String showMessage="请选择设备的目标状态";
 					List<String> defaultStatusList = new ArrayList<String>();

 					List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
 					
 					tempList.addAll(dycmlkgList);
 					tempList.addAll(zycmlkgList);
 					
 					//设备初始状态
 					for(PowerDevice sw : tempList){
 						defaultStatusList.add("0");
 					}
 					
 					EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, tempList, defaultStatusList, showMessage);
 					Map tagStatusMap=dialog.getTagStatusMap();
 					
 					if(tagStatusMap.size() == 0)
 						return false;
 					
 					for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
 						Map.Entry<PowerDevice, String> entry = it.next();
 						RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
 					}
					
					setZyb(pd,zbdyckgList,"0");
				}else if(pd.getPowerVoltGrade() == 110){
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbzyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbdyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					

					for(PowerDevice dev : dycmlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}

					for(PowerDevice dev : zycmlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					String showMessage="请选择设备的目标状态";
 					List<String> defaultStatusList = new ArrayList<String>();

 					List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
 					
 					tempList.addAll(dycmlkgList);
 					tempList.addAll(zycmlkgList);
 					tempList.addAll(gycmlkgList);

 					//设备初始状态
 					for(PowerDevice sw : tempList){
 						defaultStatusList.add("1");
 					}
 					
 					EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, tempList, defaultStatusList, showMessage);
 					Map tagStatusMap=dialog.getTagStatusMap();
 					
 					if(tagStatusMap.size() == 0)
 						return false;
 					
 					for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
 						Map.Entry<PowerDevice, String> entry = it.next();
 						RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
 						
 					}
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)
					&&!RuleUtil.isTransformerNQ(pd)){//单母接线
				for(PowerDevice dev : gycmlkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
				
				for(PowerDevice dev : zbgyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
				
				for(PowerDevice dev : zbzyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
				
				for(PowerDevice dev : zbdyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
				
				String showMessage="请选择设备的目标状态";
				List<String> defaultStatusList = new ArrayList<String>();

				List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
				
				tempList.addAll(dycmlkgList);
				tempList.addAll(zycmlkgList);
				tempList.addAll(gycmlkgList);

				if(tempList.size()>0){
					//设备初始状态
					for(PowerDevice sw : tempList){
						defaultStatusList.add("1");
					}
					
					EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, tempList, defaultStatusList, showMessage);
					Map tagStatusMap=dialog.getTagStatusMap();
					
					if(tagStatusMap.size() == 0)
						return false;
					
					for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
						Map.Entry<PowerDevice, String> entry = it.next();
						RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
						
					}
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){//双母接线
				for(PowerDevice dev : zbgyckgList){
					if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
				
				for(PowerDevice dev : zbdyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
		}
		
		if(RuleUtil.isTransformerNQ(pd)){
			for(PowerDevice dev : gycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			String showMessage="请选择设备的目标状态";
			List<String> defaultStatusList = new ArrayList<String>();

			List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> otherzbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbdyckgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					
					if(!dev.getPowerDeviceID().equals(pd.getPowerDeviceID())){
						otherzbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						otherzbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
					}
				}
			}
			
			tempList.addAll(zbzyckgList);
			tempList.addAll(zbdyckgList);
			
			tempList.addAll(otherzbzyckgList);
			tempList.addAll(otherzbdyckgList);
			
			tempList.addAll(zycmlkgList);
			tempList.addAll(dycmlkgList);
			
			//设备初始状态
			for(PowerDevice sw : tempList){
				defaultStatusList.add("0");
			}
			
			EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, tempList, defaultStatusList, showMessage);
			Map tagStatusMap=dialog.getTagStatusMap();
			
			if(tagStatusMap.size() == 0)
				return false;
			
			for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
				Map.Entry<PowerDevice, String> entry = it.next();
				RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
			}
			
			for(PowerDevice dev : xlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : gycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			setZyb(pd,zbdyckgList,"0");
		}
		
		if(RuleExeUtil.isTransformerXBDY(pd)||RuleExeUtil.isTransformerXBZ(pd)){
			for(PowerDevice dev : zbgyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : zbzyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(CBSystemConstants.LineTagStatus.containsKey(dev)){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), CBSystemConstants.LineTagStatus.get(dev));
				}else{
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
			
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == 10){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						tempList.add(dev);
					}
				}
			}
			
			if(tempList.size() == 4){
				swapDeviceListNum(tempList);
				
				EquipRadioChoose erc = new EquipRadioChoose(SystemConstants.getMainFrame(), true, tempList,  "请选择需要断开的分段断路器：",true);
				PowerDevice kgChangeOn = erc.getChooseEquip();
				
				if(erc.isCancel()||kgChangeOn== null)
					return false;
				
				RuleExeUtil.deviceStatusExecute(kgChangeOn, kgChangeOn.getDeviceStatus(), "1");
			}else{
				for(PowerDevice dev : dycmlkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
				
				for(PowerDevice dev : zycmlkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}
		}
		
		return true;
	}
	

	/**
	 * 设备排序，先比较电压等级（低的在前），再比较设备类型，再比较设备编号
	 * 
	 * @param deviceList
	 */
	public void swapDeviceListNum(List<PowerDevice> deviceList) {
		Collections.sort(deviceList, new Comparator<PowerDevice>() {
			public int compare(PowerDevice pd1, PowerDevice pd2) {
				return CZPService.getService().getDevNum(pd1.getPowerDeviceName()).compareTo(CZPService.getService().getDevNum(pd2.getPowerDeviceName()));
			}
		});
	}
	
	public boolean setOtherZxdOn(PowerDevice pd) {
		List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade() && !dev.getPowerDeviceID().equals(pd.getPowerDeviceID())){
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					otherzbList.add(dev);
				}
			}
		}
		
		List<PowerDevice> otherzxdjddzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : otherzbList){
			otherzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			RuleExeUtil.swapLowDeviceList(otherzxdjddzList);
		}
		
		List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		RuleExeUtil.swapLowDeviceList(zxdjddzList);
		
		boolean isZxdJddzAllOff = true;

		for(PowerDevice dev : zxdjddzList){
			if(dev.getDeviceStatus().equals("0")){
				isZxdJddzAllOff = false;
			}
		}
		
		for(PowerDevice dev : otherzxdjddzList){
			if(dev.getDeviceStatus().equals("0")){
				isZxdJddzAllOff = false;
			}
		}
		
		if(!isZxdJddzAllOff){
			for(PowerDevice dev : otherzxdjddzList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
		}
		
		for(PowerDevice dev : zxdjddzList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
		}
		
		return true;
	}
	
	public boolean setCurZxdOff(PowerDevice pd) {
		List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		RuleExeUtil.swapLowDeviceList(gdList);
		for(PowerDevice gd : gdList) {
			RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "1");
		}
		
		return true;
	}
	
	private boolean setCurZxdOn(PowerDevice pd) {
		List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		RuleExeUtil.swapLowDeviceList(gdList);
		for(PowerDevice gd : gdList) {
			RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
		}
		
		return true;
	}
	
	public boolean setZyb(PowerDevice pd, List<PowerDevice> zbdyckgList,String endstate) {
		List<PowerDevice> zybdzList = new ArrayList<PowerDevice>();
		List<PowerDevice> zybswList = new ArrayList<PowerDevice>();

		for(PowerDevice dysw:zbdyckgList){
			if(dysw.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				List<PowerDevice>  mxList = RuleExeUtil.getDeviceList(dysw, SystemConstants.MotherLine, SystemConstants.PowerTransformer,
						true, true, true);
				if(mxList.size()>0){
					List<PowerDevice>  qtkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);
					
					List<PowerDevice>  qtdzList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeKnifeQT, "", false, true, true, true);
					
					for(PowerDevice dev : qtkgList){
						if(dev.getPowerDeviceName().contains("站用变")){
							zybswList.add(dev);
						}
					}
					
					for(PowerDevice dev : qtdzList){
						if(dev.getPowerDeviceName().contains("站用变")){
							zybdzList.add(dev);
						}
					}
				}
			}
		}
		
		for(PowerDevice dev : zybdzList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), endstate);
		}
		
		for(PowerDevice dev : zybswList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), endstate);
		}
		
		return true;
	}
	
	public boolean setDrDkZyb(PowerDevice pd,String endstate) {
		List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(pd);

		List<PowerDevice> drdkswList = new ArrayList<PowerDevice>();
		
		List<PowerDevice> zybdzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dysw:zbdyckgList){
			if(dysw.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				List<PowerDevice>  mxList = RuleExeUtil.getDeviceList(dysw, SystemConstants.MotherLine, SystemConstants.PowerTransformer,
						true, true, true);
				if(mxList.size()>0){
					drdkswList.addAll(RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, true, true));
					
					List<PowerDevice>  qtkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);
					
					for(PowerDevice dev : qtkgList){
						List<PowerDevice>  qtdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						if(dev.getPowerDeviceName().contains("站用变")){
							drdkswList.add(dev);
						}
						
						for(PowerDevice qtdz : qtdzList){
							if(qtdz.getPowerDeviceName().contains("站用变")){
								zybdzList.add(qtdz);
							}
						}
					}
				}
			}
		}
		
		for(PowerDevice zbdyckg : zbdyckgList){
			List<PowerDevice>  dycmlkgList = getMlkgList(zbdyckg);
			
			if(dycmlkgList.size()==0&&pd.getPowerVoltGrade()==220){
				for(PowerDevice dtdksw:drdkswList){
					RuleExeUtil.deviceStatusExecute(dtdksw, dtdksw.getDeviceStatus(), endstate);
				}
				
				for(PowerDevice zybdz : zybdzList){
					RuleExeUtil.deviceStatusExecute(zybdz, zybdz.getDeviceStatus(), "1");
				}
			}
		}
		
		return true;
	}
	
	
	public List<PowerDevice>  getMlkgList(PowerDevice pd){
		List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> kgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
		
		for(Iterator<PowerDevice> itor = kgList.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				
				if(tempmxList.size()<2){
					itor.remove();
				}else{
					for(PowerDevice tempmx : tempmxList){
						List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(tempmx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeKnifeQT, false, true, true, true);
						
						if(tempzbList.size() == 0){
							itor.remove();
						}
					}
				}
			}
		}
		
		if(kgList.size()==0){
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							kgList.add(dev);
						}
					}
				}
			}
		}
		
		mlkgList.addAll(kgList);
	
		return mlkgList;
	}
	
}
