package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.operationcard.EchoReplace;
import com.tellhow.czp.service.OperationCheckDefault;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;

/**
 * 校核执行类
 * 
 * <AUTHOR>
 * 
 */
public class WritCheck {

	private JTable jTable = null;// 操作指令表
	private JTextArea jTextArea = null;// 操作任务
	int dwI;//操作单位所在列
	int zlI;//操作指令所在列
	int jhI;//校核所在列
	boolean isSuccess = true;//操作票校验结果
	 EchoReplace ec = new EchoReplace();
	
	public WritCheck(JTable jTable, JTextArea jTextArea) {
		this.jTable = jTable;
		this.jTextArea = jTextArea;
		excute();
	}
	/**
	 * 执行类
	 */
	private void excute() {
			for(int i = 0;i<jTable.getColumnCount();i++){
				if(jTable.getColumnName(i).equals("操作单位")){
					dwI = i;
					continue;
				}
				if(jTable.getColumnName(i).equals("操作内容")){
					zlI = i;
					continue;
				}
				if(jTable.getColumnName(i).equals("操作项目")){
					zlI = i;
					continue;
				}
				if(jTable.getColumnName(i).equals("校验")){
					jhI = i;
					continue;
				}
			}
			CBSystemConstants.lcm = null;
			final ArrayList<int[]> errorList = new ArrayList<int[]>();
			errorList.clear();
			final EchoReplace ec = new EchoReplace();
			new Thread(new Runnable() {

				public void run() {
					DefaultTableModel jTableModel = (DefaultTableModel) jTable.getModel();
					jTable.getColumnModel().getColumn(jhI).setMinWidth(40);
					jTable.getColumnModel().getColumn(jhI).setMaxWidth(40);
					jTable.getColumnModel().getColumn(jhI).setWidth(40);
					jTable.getColumnModel().getColumn(jhI).setPreferredWidth(40);
					final String cardbuildtype_bak = CBSystemConstants.cardbuildtype;
				try{
					// TODO Auto-generated method stub
					final int preSize = DeviceOperate.getAlltransDevMap().size();
					final String sysStatus = CBSystemConstants.cardstatus;
					CBSystemConstants.cardbuildtype = "1";
					CBSystemConstants.jh_tai = 1;
					CreatePowerStationToplogy.loadSysData();
					
					//加载厂站
					List<String> loadStationList = new ArrayList<String>();
					
					String czrw = jTextArea.getText();
					
					//检查操作内容得到操作对象
					List<RuleBaseMode> rbmRw= new ArrayList<RuleBaseMode>();
					List<RuleBaseMode> rbmLi= new ArrayList<RuleBaseMode>();
					HashMap<String, List<RuleBaseMode>> rbmLiMap= new HashMap<String, List<RuleBaseMode>>();
					
					for(int i = 0; i < jTable.getRowCount(); i++) {
						jTable.setValueAt(null, i, jhI);
						String stationName = StringUtils.ObjToString(jTableModel.getValueAt(i, dwI)).trim();
						String oprdesc = StringUtils.ObjToString(jTableModel.getValueAt(i, zlI)).trim();
						//厂站为空查询操作指令中是否包含
						if(stationName.equals("")){
							if(oprdesc.indexOf("：") > -1) {
								stationName = oprdesc.substring(0, oprdesc.indexOf("："));
							}
							if(oprdesc.indexOf(":") > -1) {
								stationName = oprdesc.substring(0, oprdesc.indexOf("："));
							}
						}
						//厂站如仍为空查询上个厂站
						if(stationName.equals("")){
							
							for(int j = i;j>=0;j--){
								stationName = StringUtils.ObjToString(jTableModel.getValueAt(j, dwI)).trim();
								if(!stationName.equals("")) break;
							}
						}
						//未找到上个厂站则查询操作任务中所包含厂站
						if(stationName.equals("")){
							if(czrw.indexOf("：") > -1) {
								stationName = czrw.substring(0, czrw.indexOf("："));
							}
							if(czrw.indexOf(":") > -1) {
								stationName = czrw.substring(0, czrw.indexOf(":"));
							}
						}
						
//						if(i==0){
//							rbmRw.addAll(OperationCheckDefault.execute(stationName, czrw));
//						}
						List<RuleBaseMode> rbmStep= OperationCheckDefault.execute(stationName, oprdesc);
						rbmLi.addAll(rbmStep);
						rbmLiMap.put(String.valueOf(i), rbmStep);
						
						for(RuleBaseMode rbm : rbmStep) {
							if(rbm.getPd() != null) {
								String stationID = rbm.getPd().getPowerStationID();
								if(!stationID.equals("")) {
									if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
										CreatePowerStationToplogy.loadFacEquip(stationID);
									}
									if(!loadStationList.contains(stationID)) {
										InitDeviceStatus ie = new InitDeviceStatus();
										ie.initStatus_EMSToCache(stationID);
										loadStationList.add(stationID);
									}
								}
							}
						}
					}
					
					jTable.updateUI();
					
					//检查操作任务与操作内容（有待完善）
					if(rbmRw.size() ==1 && rbmRw.get(0).getPd() != null) { //旁代识别有两个设备，不处理这种情况
						int checkRs = OperationCheckDefault.CheckOperation(rbmRw, rbmLi);
						if(checkRs!=2){
							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
							pdlist.add(rbmRw.get(0).getPd());
							rbmLi.get(0).setCheckResult(2);
							CheckMessage cm = new CheckMessage();
							cm.setPd(pdlist);
							cm.setBottom("304");
							if(CBSystemConstants.lcm==null){
								CBSystemConstants.lcm = new ArrayList<CheckMessage>();
							}
							CBSystemConstants.lcm.add(cm);
					
							
							//操作任务不符结束校验
							/*
							 isSuccess = false;
							 setCellCoin(rbmLi.get(0),jTable, 0, ec);
							CBSystemConstants.cardbuildtype = cardbuildtype_bak;
							CBSystemConstants.jh_tai = 0;
							
							try {
								Thread.currentThread().join();
								Thread.currentThread().interrupt();
							} catch (InterruptedException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
							*/
						}
					}

					for (int i = 0; i < jTable.getRowCount(); i++) {
						int WarFlag = -1;
						jTable.setRowSelectionInterval(i, i);
						String oprdesc = StringUtils.ObjToString(
								jTableModel.getValueAt(i, zlI)).trim();
						List<RuleBaseMode> rbmList = rbmLiMap.get(String
								.valueOf(i)); // 根据厂站和操作指令生成rbm集合
						for (RuleBaseMode rbm : rbmList) {

							if (rbm.getPd() == null) {
								if (rbm.getCheckout()) {
									rbm.setCheckResult(0);
								} else {
									rbm.setCheckResult(2);
								}

								jTable.setValueAt(rbm, i, jhI);
								continue;
							}
							
							//如果该设备在校验过程中已操作过，则将校验模式改为即时校核
							Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
							for (int j = allMap.size(); j >preSize; j--) {
								PowerDevice pd = allMap.get(j).getTransDevice();
								if(pd.equals(rbm.getPd())) {
									CBSystemConstants.isRealTime = true;
									break;
								}
							}
							
							isSuccess = OperationCheckDefault.check(rbm);
							
							//校验完成后，将即时校核恢复为逻辑校核
							if(CBSystemConstants.isRealTime)
								CBSystemConstants.isRealTime = false;
							
							setCellCoin(rbm, jTable, i, ec);
							if (!isSuccess) {
								jTable.clearSelection();
								CBSystemConstants.cardbuildtype = cardbuildtype_bak;
								CBSystemConstants.jh_tai = 0;
								break;
							}
						}
						if (!isSuccess) {
							CBSystemConstants.cardbuildtype = cardbuildtype_bak;
							CBSystemConstants.jh_tai = 0;
							break;
						}
					}
					
					
					Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
					for (int i = allMap.size(); i >preSize; i--) {
						allMap.remove(i);
					}
					for(int i = 1; i <= allMap.size(); i++) {
						DispatchTransDevice dtd = allMap.get(i);
						dtd.getTransDevice().setDeviceStatus(dtd.getEndstate());
					}
					
					CBSystemConstants.cardstatus = sysStatus;
					CBSystemConstants.cardbuildtype = cardbuildtype_bak;
					CBSystemConstants.jh_tai = 0;
					
				}catch (Exception e) {
					e.printStackTrace();
				}finally{
					CBSystemConstants.cardbuildtype = cardbuildtype_bak;
					CBSystemConstants.jh_tai = 0;
				}
				
				}
			}).start();
	}
	
	public void setCellCoin(RuleBaseMode rbm,JTable jTable1,int i,EchoReplace ec){
		int WarFlag = -1;
		List<CheckMessage> msgList = new ArrayList<CheckMessage>();
		if(CBSystemConstants.lcm != null && CBSystemConstants.lcm.size() > 0) {
			WarFlag = 0;	
			for (CheckMessage msg : CBSystemConstants.lcm) {
				
				if (WarFlag != 1 && msg.getBottom().substring(0, 1).equals("1")) {
					WarFlag = 1;
				}else if(WarFlag != 2 && msg.getBottom().substring(0, 1).equals("3")) {
					WarFlag = 2;
				}
				//String str = ec.getEcho(msg, rbm.getPd());
				msgList.add(msg);
			}
			int result = 0;
			if(WarFlag == -1)
				result = 0;
			else if(WarFlag == 0)
				result = 1;
			else if(WarFlag == 1)
				result = 2;
			else if(WarFlag == 2)
				result = 2;
			
			rbm.setCheckResult(result);
			Map<String, List<CheckMessage>> map = new HashMap<String, List<CheckMessage>>();
			CBSystemConstants.lcm.clear();
			map.put("sbxx", msgList);
			rbm.setMessage(map);
		}
		else
			rbm.setCheckResult(0);
		jTable1.setValueAt(rbm, i, jhI);
		if(CBSystemConstants.lcm != null)
			CBSystemConstants.lcm.clear();
   }
	
}
