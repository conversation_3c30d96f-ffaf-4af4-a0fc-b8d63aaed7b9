package com.tellhow.czp.app.yndd.wordcard.nj;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.nj.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionNJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrNJDMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("怒江单母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getTransformerKnifeLoad(curDev);
			List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbdyckgList = new ArrayList<PowerDevice>();
			
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdzList){
				if(dev.getPowerVoltGrade() == lowvolt){
					zbdycdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			boolean isAlongTransformer = true;
			
			List<PowerDevice> mx10kVList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						isAlongTransformer = false;
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						otherzbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
						otherzbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
					if(dev.getPowerVoltGrade() == 10){
						mx10kVList.add(dev);
					}
				}
			}
			
			if(isAlongTransformer){
				replaceStr += "怒江配调@确认"+stationName+CZPService.getService().getDevName(mx10kVList)+"配调管辖10kV出线运行方式已调整完毕，具备停电条件/r/n";
			}else{
				replaceStr += CommonFunctionNJ.getMotherLineTdContent(dycmxList, stationName);
			}
			
			if(TicketKindChoose.flag.equals("全部程序化")){
				replaceStr += "怒江地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
				
				for(PowerDevice dev : zbdycdzList){
					replaceStr += CommonFunctionNJ.getSequenceConfirmTdContent(dev,stationName);
				}
				
				replaceStr += CommonFunctionNJ.getSequenceConfirmTdContent(zbdyckgList,stationName);
				replaceStr += CommonFunctionNJ.getSequenceConfirmTdContent(zbzyckgList,stationName);
				replaceStr += CommonFunctionNJ.getSequenceConfirmTdContent(zbgyckgList,stationName);
			}else{
				for(PowerDevice dev : zxdjddzList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "怒江地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合位/r/n";
					}
				}
				
				for(PowerDevice dev : otherzxdjddzList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "怒江地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合位/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionNJ.getHhContent(dev,"怒江地调", stationName);
					}
				}
				
				for(PowerDevice dev : otherzbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionNJ.getHhContent(dev,"怒江地调", stationName);
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionNJ.getHhContent(dev,"怒江地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "怒江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : otherzbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionNJ.getHhContent(dev,"怒江地调", stationName);
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionNJ.getHhContent(dev,"怒江地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "怒江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "怒江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "怒江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(station.getPowerVoltGrade() > 35){
					replaceStr += stationName+"@将"+(int)curDev.getPowerVoltGrade()+"kVX号主变保护定值区由XX区调整至XX区/r/n";
				}
				
				if(curDev.getDeviceStatus().equals("2")){
					if(TicketKindChoose.flag.equals("部分程序化")){
						for(PowerDevice dev : zbzyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunctionNJ.ifSwitchSeparateControl(dev)){
									String devName = CZPService.getService().getDevName(dev);
									replaceStr += "怒江地调@执行"+stationName+devName+"由热备用转冷备用程序操作/r/n";
									replaceStr += CommonFunctionNJ.getSequenceConfirmTdContent(zbzyckgList,stationName);
								}
							}
						}
						
						for(PowerDevice dev : zbgyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunctionNJ.ifSwitchSeparateControl(dev)){
									String devName = CZPService.getService().getDevName(dev);
									replaceStr += "怒江地调@执行"+stationName+devName+"由热备用转冷备用程序操作/r/n";
									replaceStr += CommonFunctionNJ.getSequenceConfirmTdContent(zbgyckgList,stationName);
								}
							}
						}
					}
					
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
					
					for(PowerDevice dev : zxdjddzList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "怒江地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}else{
					for(PowerDevice dycmx : dycmxList){
						if(RuleExeUtil.getDeviceBeginStatus(dycmx).equals("0")){
							replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
