package com.tellhow.czp.app.yndd.wordcard.dl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDL;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDLKGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("大理开关复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			boolean ifSwitchControl = false;
			boolean ifSwitchSeparateControl = false;

			if(CommonFunctionDL.ifSwitchControl(curDev)){
				ifSwitchControl = true;
			}
			
			if(CommonFunctionDL.ifSwitchSeparateControl(curDev)){
				ifSwitchSeparateControl = true;
			}
			
			String begin = CBSystemConstants.getCurRBM().getBeginStatus();
			String end = CBSystemConstants.getCurRBM().getEndState();
			
			if(begin.equals("1")){
				replaceStr += "大理地调@遥控合上"+stationName+deviceName+"/r/n";
			}else if(begin.equals("2")){
				if(end.equals("1")){
					if(ifSwitchSeparateControl){
						replaceStr += "大理地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
						replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
					}else{
						replaceStr += stationName+"@将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
					}
				}else if(end.equals("0")){
					if(ifSwitchControl && ifSwitchSeparateControl){
						replaceStr += "大理地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
						replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
					}else{
						if(ifSwitchSeparateControl){
							replaceStr += "大理地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
							replaceStr += CommonFunctionDL.getKnifeOnCheckContent(dzList, stationName);
						}else{
							replaceStr += stationName+"@将"+ CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
						}
						
						replaceStr += "大理地调@遥控合上"+stationName+deviceName+"/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
