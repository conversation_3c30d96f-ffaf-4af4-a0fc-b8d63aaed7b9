package com.tellhow.czp.app.yndd.wordcard.pe;


import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

import java.util.*;


public class ReplaceStrPEMXDM implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("普洱母线倒母".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> plkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
						mxList.add(dev);
					}
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						mlkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						xlkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)){
						plkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
						zbkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
						zbkgList.add(dev);
					}
				}
			}
			
			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : xlkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice rbykg : rbykgList){
				String otherMxName = "";
				
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(rbykg, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : dzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
						List<PowerDevice> curmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
						
						if(dev.getDeviceStatus().equals("0")){
							for(PowerDevice mx : curmxList){
								otherMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}
					}
				}
				
				if(CommonFunction.ifSwitchControl(rbykg)&&CommonFunction.ifSwitchSeparateControl(rbykg)){
					replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由热备用转冷备用程序操作/r/n";
					replaceStr += getKnifeOffCheckContent(dzList, stationName);
					
					replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由冷备用转联"+otherMxName+"热备用程序操作/r/n";
					replaceStr += getKnifeOnCheckContent(dzList, stationName);
				}
			}
			
			for(PowerDevice rbykg : rbykgList){
				String curMxName = "";
				String otherMxName = "";
				
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(rbykg, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : dzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
						List<PowerDevice> curmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
						
						if(dev.getDeviceStatus().equals("0")){
							for(PowerDevice mx : curmxList){
								otherMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}else if(dev.getDeviceStatus().equals("1")){
							for(PowerDevice mx : mxList){
								curMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}
					}
				}
				
				if(CommonFunction.ifSwitchControl(rbykg)&&CommonFunction.ifSwitchSeparateControl(rbykg)){
					
				}else{
					replaceStr += stationName+"@将"+CZPService.getService().getDevName(rbykg)+"由"+curMxName+"热备用倒至"+otherMxName+"热备用/r/n";
				}
			}
			
			replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
			}
			
			for(PowerDevice yxkg : yxkgList){
				String curMxName = "";
				String otherMxName = "";
				
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(yxkg, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : dzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
						List<PowerDevice> curmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
						
						if(dev.getDeviceStatus().equals("0")){
							for(PowerDevice mx : curmxList){
								otherMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}else if(dev.getDeviceStatus().equals("1")){
							for(PowerDevice mx : curmxList){
								curMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}
					}
				}
				
				if(CommonFunction.ifSwitchControl(yxkg)&&CommonFunction.ifSwitchSeparateControl(yxkg)){
					replaceStr += "普洱地调@执行"+stationName+CZPService.getService().getDevName(yxkg)+"由"+curMxName+"运行倒至"+otherMxName+"运行程序操作/r/n";
				
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
						}
					}
					
					for(PowerDevice dz : dzList){
						if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
						}
					}
				}
			}
			
			for(PowerDevice yxkg : yxkgList){
				String curMxName = "";
				String otherMxName = "";
				
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(yxkg, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : dzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
						List<PowerDevice> curmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
						
						if(dev.getDeviceStatus().equals("0")){
							for(PowerDevice mx : curmxList){
								otherMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}else if(dev.getDeviceStatus().equals("1")){
							for(PowerDevice mx : curmxList){
								curMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}
					}
				}
				
				if(CommonFunction.ifSwitchControl(yxkg)&&CommonFunction.ifSwitchSeparateControl(yxkg)){
				}else{
					replaceStr += stationName+"@将"+CZPService.getService().getDevName(yxkg)+"由"+curMxName+"运行倒至"+otherMxName+"运行/r/n";
				}
			}
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
			}
			
			replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
	public String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
			}
		}
		
		return replaceStr;
	}
}
