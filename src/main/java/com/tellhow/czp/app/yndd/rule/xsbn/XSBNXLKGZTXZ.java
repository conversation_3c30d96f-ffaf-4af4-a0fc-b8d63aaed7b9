package com.tellhow.czp.app.yndd.rule.xsbn;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;

public class XSBNXLKGZTXZ implements RulebaseInf {
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		
		/*XSBNDeviceSelectionDialog.pdMap.clear();
		
		if(pd.getPowerVoltGrade() == 35){
			List<PowerDevice> switchList = new ArrayList<PowerDevice>();
			PowerDevice lineSource = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
			List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
			
			if(lineSource != null && !lineSource.getPowerDeviceID().equals("")){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				switchList.addAll(xlswList);
				
				PowerDevice zyb = new PowerDevice();
				PowerDevice pt = new PowerDevice();

				String beginStatus = rbm.getBeginStatus();

				if(beginStatus.equals("0") || beginStatus.equals("1")){
					zyb.setDeviceStatus("0");
					pt.setDeviceStatus("0");
				}else{
					zyb.setDeviceStatus("2");
					pt.setDeviceStatus("2");
				}
				
				zyb.setPowerVoltGrade(lineSource.getPowerVoltGrade());
				zyb.setPowerDeviceID(UUID.randomUUID().toString());
				zyb.setPowerDeviceName("X号站用变");
				zyb.setPowerStationID(lineSource.getPowerStationID());
				zyb.setPowerStationName(lineSource.getPowerStationName());
				zyb.setDeviceType(SystemConstants.Term);
				
				switchList.add(zyb);
				
				pt.setPowerVoltGrade(lineSource.getPowerVoltGrade());
				pt.setPowerDeviceID(UUID.randomUUID().toString());
				pt.setPowerDeviceName("线路电压互感器");
				pt.setPowerStationID(lineSource.getPowerStationID());
				pt.setPowerStationName(lineSource.getPowerStationName());
				pt.setDeviceType(SystemConstants.VolsbTransformer);

				switchList.add(pt);
			}
			
			for(PowerDevice loadLineTran : lineLoad){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				switchList.addAll(xlswList);
				
				PowerDevice zyb = new PowerDevice();
				PowerDevice pt = new PowerDevice();

				String beginStatus = rbm.getBeginStatus();

				if(beginStatus.equals("0") || beginStatus.equals("1")){
					zyb.setDeviceStatus("0");
					pt.setDeviceStatus("0");
				}else{
					zyb.setDeviceStatus("2");
					pt.setDeviceStatus("2");
				}
				
				zyb.setPowerDeviceID(UUID.randomUUID().toString());
				zyb.setPowerVoltGrade(loadLineTran.getPowerVoltGrade());
				zyb.setPowerDeviceName("X号站用变");
				zyb.setPowerStationID(loadLineTran.getPowerStationID());
				zyb.setPowerStationName(loadLineTran.getPowerStationName());
				zyb.setDeviceType(SystemConstants.Term);

				switchList.add(zyb);
				
				pt.setPowerDeviceID(UUID.randomUUID().toString());
				pt.setPowerVoltGrade(loadLineTran.getPowerVoltGrade());
				pt.setPowerDeviceName("线路电压互感器");
				pt.setPowerStationID(loadLineTran.getPowerStationID());
				pt.setPowerStationName(loadLineTran.getPowerStationName());
				pt.setDeviceType(SystemConstants.VolsbTransformer);

				switchList.add(pt);	
			}

			List<String> endstatusList = new ArrayList<String>();

			for(PowerDevice dev : switchList){
				if(rbm.getEndState().equals("0")){
					if(dev.getDeviceType().equals(SystemConstants.Switch)){
						endstatusList.add("1");
					}else{
						endstatusList.add("0");
					}
				}else if(rbm.getEndState().equals("1")){
					if(dev.getDeviceType().equals(SystemConstants.Switch)){
						endstatusList.add(rbm.getEndState());
					}else{
						endstatusList.add("0");
					}
				}else if(rbm.getEndState().equals("2")){
					endstatusList.add(rbm.getEndState());
				}else if(rbm.getEndState().equals("3")){
					endstatusList.add("2");
				}
			}
			
			XSBNDeviceSelectionDialog dsd = new XSBNDeviceSelectionDialog(SystemConstants.getMainFrame(), true, switchList, endstatusList);
			
			if(dsd.isCancel()){
				return false;
			}
		}*/
	    
		return true;
	}

}
