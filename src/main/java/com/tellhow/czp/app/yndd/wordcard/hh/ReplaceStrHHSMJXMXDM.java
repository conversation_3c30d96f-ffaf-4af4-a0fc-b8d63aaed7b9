package com.tellhow.czp.app.yndd.wordcard.hh;


import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.czp.util.ApplicationContext;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.ReplaceUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

import java.util.*;


public class ReplaceStrHHSMJXMXDM implements TempStringReplace {
    @Override
    public String strReplace(String tempStr, PowerDevice curDev,
                             PowerDevice stationDev, String desc) {
        StringBuilder replaceStr = new StringBuilder();
        ApplicationContext context = ApplicationContext.getInstance();
        List<PowerDevice> selectedDevices = context.getList("DMSelectedDevices");
        List<PowerDevice> mxList = context.getList("DMBusBarList");
        PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
        String stationName = CZPService.getService().getDevName(station);
        if ("红河双母接线母线倒母".equals(tempStr)) {

            if (selectedDevices == null || selectedDevices.isEmpty()) return replaceStr.toString();

            boolean hasHot = false;     // 是否有热倒
            boolean hasCold = false;   // 是否有冷倒
            // 先添加确认所有设备具备倒母条件的指令
            StringBuilder allDevicesStr = new StringBuilder();
            // 重新构建只包含热备用设备的字符串
            StringBuilder coldDevicesStr = new StringBuilder();
            // 将设备按照状态，倒母方向分组：运行设备和热备用设备
            Map<String, List<PowerDevice>> deviceGroups = new HashMap<String, List<PowerDevice>>();
            for (int i = 0; i < selectedDevices.size(); i++) {
                PowerDevice device = selectedDevices.get(i);
                String devName = CZPService.getService().getDevName(device);
                allDevicesStr.append(devName);
                if (i < selectedDevices.size() - 1) {
                    allDevicesStr.append("、");
                }

                String initialBus = ReplaceUtil.strReplace("红河当前母线名称",
                        Collections.singletonList(selectedDevices.get(i)), selectedDevices.get(i), desc);
                String targetBus = ReplaceUtil.strReplace("红河目标母线名称",
                        Collections.singletonList(selectedDevices.get(i)), selectedDevices.get(i), desc);
                String devStatus = RuleExeUtil.getStatus(device.getDeviceStatus());
                String key = devStatus + "->" + initialBus + "->" + targetBus;
                if (devStatus.equals("运行")) hasHot = true;
                if (devStatus.equals("热备用")) {
                    coldDevicesStr.append(devName).append("、");
                    hasCold = true;
                }
                if (!deviceGroups.containsKey(key)) {
                    deviceGroups.put(key, new ArrayList<PowerDevice>());
                }
                deviceGroups.get(key).add(device);
            }
            String voltageGrade = StringUtils.getVolt(stationDev.getPowerVoltGrade());
            String mlkgName = "";
            List<PowerDevice> fdkgList = czprule.rule.operationclass.RuleExeUtil.getDeviceList(stationDev, SystemConstants.Switch,
                    SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "",
                    false, true, true, true);
            for (PowerDevice fdkg : fdkgList) {
                // 只取母联开关
                if (RuleExeUtil.isSwitchDoubleML(fdkg)) {
                    mlkgName = CZPService.getService().getDevName(fdkg);
                }
            }
            if (hasHot) {
                replaceStr.append(stationName).append("@确认").append(voltageGrade)
                        .append("母线保护已按运行操作手册执行，").append(mlkgName).append("操作电源已断开，具备倒母线操作条件/r/n");
            } else {
                replaceStr.append(stationName).append("@确认").append(allDevicesStr).append("间隔具备倒母条件/r/n");
            }

            // 先处理热倒的设备组
            for (Map.Entry<String, List<PowerDevice>> entry : deviceGroups.entrySet()) {
                String groupKey = entry.getKey();
                List<PowerDevice> groupDevices = entry.getValue();

                // 跳过非热倒的设备组
                if (!groupKey.startsWith("运行")) {
                    continue;
                }

                // 解析分组键获取初始母线和目标母线
                String[] keyParts = groupKey.split("->");
                String initialBus = keyParts[1];
                String targetBus = keyParts[2];

                // 添加运行倒指令
                StringBuilder groupDevicesStr = new StringBuilder();
                for (int i = 0; i < groupDevices.size(); i++) {
                    PowerDevice device = groupDevices.get(i);
                    groupDevicesStr.append(CZPService.getService().getDevName(device));
                    if (i < groupDevices.size() - 1) {
                        groupDevicesStr.append("、");
                    }
                }
                // 检查是否只有一个设备，如果是则添加厂站名称
                if (groupDevicesStr.toString().contains("、")) {
                    replaceStr.append("红河地调@执行").append(groupDevicesStr).append("由")
                            .append(initialBus).append("运行倒至").append(targetBus).append("运行程序操作/r/n");
                } else {
                    replaceStr.append("红河地调@执行").append(stationName).append(groupDevicesStr).append("由")
                            .append(initialBus).append("运行倒至").append(targetBus).append("运行程序操作/r/n");
                }

                // 为每个运行设备添加刀闸确认指令
                /*for (PowerDevice device : groupDevices) {
                    List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(device, SystemConstants.SwitchSeparate);
                    for (PowerDevice dz : dzList) {
                        if (RuleExeUtil.getDeviceBeginStatus(dz).equals("1")) {
                            String deviceName = CZPService.getService().getDevName(dz);
                            replaceStr.append(stationName).append("@确认").append(deviceName).append("处合上位置/r/n");
                        }
                    }

                    for (PowerDevice dz : dzList) {
                        if (RuleExeUtil.getDeviceBeginStatus(dz).equals("0")) {
                            String deviceName = CZPService.getService().getDevName(dz);
                            replaceStr.append(stationName).append("@确认").append(deviceName).append("处拉开位置/r/n");
                        }
                    }
                }*/
            }

            if (hasHot) {
                // 对mxList按名称进行排序
                RuleExeUtil.swapDeviceList(mxList);
                replaceStr.append(stationName).append("@确认").append("操作电源已投入，").append(voltageGrade)
                        .append("母线保护已按现场规程要求执行，互联压板已退出，")
                        .append(CZPService.getService().getDevName(mxList))
                        .append("相关一、二次设备运行正常/r/n");
                if (hasCold) {
                    // 删除结尾的顿号
                    // 如果同时有热倒和冷倒，那么需要将集合中热倒的设备剔除掉
                    replaceStr.append(stationName).append("@确认").append(coldDevicesStr.toString().endsWith("、")
                            ? coldDevicesStr.substring(0, coldDevicesStr.length() - 1) : coldDevicesStr).append("间隔具备倒母条件/r/n");
                }
            }

            // 再处理冷倒的设备组
            for (Map.Entry<String, List<PowerDevice>> entry : deviceGroups.entrySet()) {
                String groupKey = entry.getKey();
                List<PowerDevice> groupDevices = entry.getValue();

                // 跳过非冷倒的设备组
                if (!groupKey.startsWith("热备用")) {
                    continue;
                }

                // 解析分组键获取初始母线和目标母线
                String[] keyParts = groupKey.split("->");
                String initialBus = keyParts[1];
                String targetBus = keyParts[2];

                // 添加热备用倒指令
                StringBuilder groupDevicesStr = new StringBuilder();
                for (int i = 0; i < groupDevices.size(); i++) {
                    PowerDevice device = groupDevices.get(i);
                    groupDevicesStr.append(CZPService.getService().getDevName(device));
                    if (i < groupDevices.size() - 1) {
                        groupDevicesStr.append("、");
                    }
                }

                // 检查是否只有一个设备，如果是则添加厂站名称
                if (groupDevicesStr.toString().contains("、")) {
                    replaceStr.append("红河地调@执行").append(groupDevicesStr).append("由")
                            .append(initialBus).append("热备用倒至").append(targetBus).append("热备用程序操作/r/n");
                } else {
                    replaceStr.append("红河地调@执行").append(stationName).append(groupDevicesStr).append("由")
                            .append(initialBus).append("热备用倒至").append(targetBus).append("热备用程序操作/r/n");
                }

                // 为每个热备用设备添加刀闸断开和合上指令
                /*for (PowerDevice device : groupDevices) {
                    List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(device, SystemConstants.SwitchSeparate);
                    List<PowerDevice> reDzList = RuleExeUtil.sortByMXC(dzList);
                    Collections.reverse(reDzList);

                    for (PowerDevice dz : reDzList) {
                        if (RuleExeUtil.getDeviceBeginStatus(dz).equals("0")) {
                            String deviceName = CZPService.getService().getDevName(dz);
                            replaceStr.append(stationName).append("@确认").append(deviceName).append("处拉开位置/r/n");
                        }
                    }

                    Collections.reverse(reDzList);
                    for (PowerDevice dz : reDzList) {
                        if (RuleExeUtil.getDeviceEndStatus(dz).equals("0")) {
                            String deviceName = CZPService.getService().getDevName(dz);
                            replaceStr.append(stationName).append("@确认").append(deviceName).append("处合上位置/r/n");
                        }
                    }
                }*/
            }
        } else if ("红河双母接线母线倒母操作任务".equals(tempStr)) {
            if (selectedDevices.size() > 1) {
                replaceStr = new StringBuilder("调整" + stationName + "站内运行方式");
            } else if (selectedDevices.size() == 1) {
                PowerDevice device = selectedDevices.get(0);
                String curBusBarName  = ReplaceUtil.strReplace("当前母线名称", selectedDevices, device, desc);
                String targetBusBarName = ReplaceUtil.strReplace("目标母线名称", selectedDevices, device, desc);
                String devStatus = RuleExeUtil.getStatus(device.getDeviceStatus());
                String devName = CZPService.getService().getDevName(device);
                replaceStr = new StringBuilder(stationName + "将" + devName + "由" + curBusBarName + devStatus + "倒至"
                        + targetBusBarName + devStatus + "/r/n");
            }
        }
        if (replaceStr.length() == 0) {
            return null;
        }
        return replaceStr.toString();
    }
}
