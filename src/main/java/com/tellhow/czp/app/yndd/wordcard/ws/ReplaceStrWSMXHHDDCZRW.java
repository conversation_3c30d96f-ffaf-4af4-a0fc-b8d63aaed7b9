package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSMXHHDDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山母线合环倒电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC,"", false, true, true, true);
			
			String curzbName = "";
			
			for(PowerDevice dev : zbkgList){
				zbList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);
				
				for(PowerDevice zb : zbList){
					curzbName = CZPService.getService().getDevName(zb);
					break;
				}
			}
			
			String otherzbName = "";
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						if(!zbList.contains(dev)){
							otherzbName = CZPService.getService().getDevName(dev);
							break;
						}
					}
				}
			}
		
			
			replaceStr += stationName + deviceName +"由"+curzbName+"供电倒由"+otherzbName+"供电";
		}
		
		return replaceStr;
	}

}
