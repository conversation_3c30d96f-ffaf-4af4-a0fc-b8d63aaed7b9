package com.tellhow.czp.app.yndd.tool;

import java.util.*;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.view.StringCheckChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

public class CommonFunctionHH extends CommonFunction {
	/**
	 * @param device 开关
	 * @return	判断是否是主变开关
	 */
	public static boolean isTransSwitch(PowerDevice device){
        return device.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
				|| device.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC);
	}


	public static List<Map<String, String>> getStationLineList(PowerDevice curDev){
		List<Map<String, String>> stationLineList = new ArrayList<Map<String,String>>();

		String sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND "
				+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN " +
				"(SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND WHERE ID = '"+curDev.getPowerDeviceID()+"')";

		stationLineList = DBManager.queryForList(sql);

		if(stationLineList.isEmpty()){
			sql = "SELECT LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND "
					+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_NAME = '"+CZPService.getService().getDevName(curDev)+"'";

			stationLineList = DBManager.queryForList(sql);
		}

		return stationLineList;
	}


	public static boolean ifSwitchSeparateControl(PowerDevice dev){
		//刀闸是否可控
		boolean result = true;

		if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)
				|| dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
			String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT " +
					"WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);

			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));

				if(!Boolean.parseBoolean(ifcontrol)){
					result = false;
				}
			}

			if(ifcontrolList.isEmpty()){
				return false;
			}

			return result;
		}else if(dev.getDeviceType().equals(SystemConstants.Switch)) {
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

			// 如果刀闸列表为空，直接返回false
			if(dzList.isEmpty()) {
				return false;
			}

			// 收集所有需要查询的刀闸ID
			Set<String> deviceIds = new HashSet<String>();

			for(PowerDevice dz : dzList) {
				deviceIds.add(dz.getPowerDeviceID());
			}

			// 构建IN查询，一次性查询所有刀闸
			String sql = "SELECT MEMBEROF_PSR, IFCONTROL FROM " + CBSystemConstants.equipUser +
					"T_M_MEASUREMENT WHERE MEMBEROF_PSR IN ('" +
					CommonUtils.join(deviceIds, "','") + "') AND MEASUREMENTTYPE = 'MeasType-54'";

			List<Map<String, String>> allResults = DBManager.queryForList(sql);

			// 用于记录已查询到控制状态的设备ID
			Set<String> foundDevices = new HashSet<String>();

			// 处理查询结果
			for(Map<String, String> row : allResults) {
				String deviceId = StringUtils.ObjToString(row.get("MEMBEROF_PSR"));
				String ifcontrol = StringUtils.ObjToString(row.get("IFCONTROL"));

				foundDevices.add(deviceId);

				if(!Boolean.parseBoolean(ifcontrol)) {
					result = false;
					// 如果找到不可控的设备，可以提前退出
					// 注释掉下面的break是为了保持原代码逻辑，如果只需要知道是否存在不可控设备，可以启用
					// break;
				}
			}

			// 检查是否有刀闸没有查询到结果
			for(PowerDevice dz : dzList) {
				if(!foundDevices.contains(dz.getPowerDeviceID())) {
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)) {
						// 特殊类型KindKnifeXC，没查到结果继续检查其他刀闸
						continue;
					} else {
						// 其他类型，没查到结果直接返回false
						return false;
					}
				}
			}

			return result;
		}


		return false;
	}

	public static String getHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";

		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			String devName = CZPService.getService().getDevName(dev);
			if(dev.getPowerVoltGrade() > 35){
				if(stationName.contains("电站")){
					replaceStr += stationName+"@用"+devName+"同期合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+devName+"同期合环/r/n";
				}
			}else{
				if(stationName.contains("电站")){
					replaceStr += stationName+"@用"+devName+"合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+devName+"合环/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String get110kVZbZxdStrReplace(PowerDevice pd,List<PowerDevice> zbList) {
		//110kV母线操作
		String replaceStr="";

		if(pd != null){
			PowerDevice otherzb = new PowerDevice();
			String pdName = CZPService.getService().getDevName(pd);

			for(PowerDevice zb : zbList){
				if(!zb.getPowerDeviceID().equals(pd.getPowerDeviceID())){
					otherzb = zb;
				}
			}

			List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine,
					"", CBSystemConstants.RunTypeGroundZXDDD, "",
					true, true, true, true);
			List<PowerDevice> othergdList = RuleExeUtil.getDeviceList(otherzb, SystemConstants.SwitchFlowGroundLine,
					"", CBSystemConstants.RunTypeGroundZXDDD, "",
					true, true, true, true);

			if(!gdList.isEmpty() && !othergdList.isEmpty()){
				if(pd.getPowerVoltGrade()==220){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList.get(0)).equals("0")
							&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(othergdList.get(0)).equals("0")){
						//如果全部主变中性点操作前是合上的
						replaceStr += "确认"+pdName+"高、中压侧中性点及其零序保护已投入/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList.get(0)).equals("1")
							&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(othergdList.get(0)).equals("0")){
						//如果当前主变中性点操作前是分开的、其它主变是合上的
						replaceStr += "投入"+pdName+"中压侧中性点及其零序保护/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList.get(0)).equals("0")
							&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(othergdList.get(0)).equals("1")){
						//如果当前主变中性点操作前是合上的、其它主变是分开的
						replaceStr += "投入"+CZPService.getService().getDevName(otherzb)+"高、中压侧中性点及其零序保护/r/n";
						replaceStr += "退出"+pdName+"高压侧中性点及其零序保护/r/n";
					}
				}else if(pd.getPowerVoltGrade()==110){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList.get(0)).equals("0")
							&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(othergdList.get(0)).equals("0")){
						//如果全部主变中性点操作前是合上的
						replaceStr += "确认"+pdName+"中性点及其零序保护已投入/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList.get(0)).equals("1")
							&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(othergdList.get(0)).equals("0")){
						//如果当前主变中性点操作前是分开的、其它主变是合上的
						replaceStr += "确认"+pdName+"中性点及其零序保护已投入/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList.get(0)).equals("0")
							&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(othergdList.get(0)).equals("1")){
						//如果当前主变中性点操作前是合上的、其它主变是分开的
						replaceStr += "投入"+CZPService.getService().getDevName(otherzb)+"中性点及其零序保护/r/n";
						replaceStr += "确认"+pdName+"中性点及其零序保护已投入/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList.get(0)).equals("1")
							&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(othergdList.get(0)).equals("1")){
						//如果全部主变中性点操作前是分开的
						replaceStr += "确认"+pdName+"中性点及其零序保护已投入/r/n";
					}
				}
			}
		}

		return replaceStr;
	}

	public static String getZbZxdStrReplace(List<PowerDevice> zbList) {
		String replaceStr="";

		PowerDevice pd = CBSystemConstants.getCurRBM().getPd();
		String pdName = CZPService.getService().getDevName(pd);

		if(pd != null){
			PowerDevice otherzb = new PowerDevice();

			for(PowerDevice zb : zbList){
				if(!zb.getPowerDeviceID().equals(pd.getPowerDeviceID())){
					otherzb = zb;
				}
			}

			String ce = "";

			if(pd.getPowerVoltGrade() == 220){
				ce = "高、中压侧";
			}

			if(!otherzb.getPowerDeviceID().isEmpty()){
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(otherzb, SystemConstants.SwitchFlowGroundLine,
						"", CBSystemConstants.RunTypeGroundZXDDD, "",
						true, true, true, true);

				if(!gdList.isEmpty()){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList.get(0)).equals("1")){
						//如果另外的主变中性点操作前是分开的
						List<PowerDevice> gdList1 = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine,
								"", CBSystemConstants.RunTypeGroundZXDDD, "",
								true, true, true, true);

						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList1.get(0)).equals("1")){
							//当前主变中性点操作前是分开的
							replaceStr += "确认"+pdName+ce+"中性点及其零序保护己投入/r/n";
						}else{
							replaceStr += "投入"+CZPService.getService().getDevName(otherzb)+ce+"中性点及其零序保护/r/n";
							replaceStr += "确认"+pdName+ce+"中性点及其零序保护己投入/r/n";
						}
					}else{
						//如果另外的主变中性点操作前是合上的
						List<PowerDevice> gdList1 = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine,
								"", CBSystemConstants.RunTypeGroundZXDDD, "",
								true, true, true, true);

						if(!gdList1.isEmpty()){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gdList1.get(0)).equals("1")){
								//当前主变中性点操作前是分开的
								replaceStr += "确认"+pdName+ce+"中性点及其零序保护己投入/r/n";
							}else{
								replaceStr += "确认"+CZPService.getService().getDevName(zbList)+ce+"中性点及其零序保护己投入/r/n";
							}
						}
					}
				}
			}
		}

		return replaceStr;
	}

	public static String getYcHsStrReplace(List<PowerDevice> devList,String stationName){
		String replaceStr="";

		for(Iterator<PowerDevice> it2 = devList.iterator(); it2.hasNext();){
			PowerDevice dev = it2.next();

			if(!RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
				it2.remove();
			}
		}

		if(devList.size()==1){
			replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(devList)+"/r/n";
		}else if(devList.size()>1){
			replaceStr += "红河地调@遥控依次合上"+stationName+CZPService.getService().getDevName(devList)+"/r/n";
		}

		return replaceStr;
	}

	public static String getYcDkStrReplace(List<PowerDevice> devList,String stationName){
		String replaceStr="";

		for(Iterator<PowerDevice> it = devList.iterator(); it.hasNext();){
			PowerDevice dev = it.next();

			if(!RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
				it.remove();
			}
		}

		RuleExeUtil.swapLowDeviceList(devList);
		Collections.reverse(devList);

		if(devList.size()==1){
			replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(devList)+"/r/n";
		}else if(devList.size()>1){
			replaceStr += "红河地调@遥控依次断开"+stationName+CZPService.getService().getDevName(devList)+"/r/n";
		}
		return replaceStr;
	}

	public static String getTransformerLbyToRbyStrReplace(PowerDevice curDev,String stationName) {
		String replaceStr="";

		String deviceName = CZPService.getService().getDevName(curDev);

		List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
		List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
		List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

		List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbzycdzList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbgycdzList = new ArrayList<PowerDevice>();

		List<PowerDevice> tempList = new ArrayList<PowerDevice>();

		boolean isStandardDyc = true;
		boolean isStandardZyc = true;
		boolean isStandardGyc = true;

		boolean isContainZyc = true;

    	for(PowerDevice dev : zbdyckgList){
    		zbdycdzList =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

    		if(zbdycdzList.size()==1){
    			isStandardDyc = false;
    		}
    	}

    	if(zbzyckgList.isEmpty()){
    		isContainZyc = false;
    	}

    	for(PowerDevice dev : zbzyckgList){
    		zbzycdzList =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

    		if(zbzycdzList.size()==1){
    			isStandardZyc = false;
    		}
    	}

    	for(PowerDevice dev : zbgyckgList){
    		zbgycdzList =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

    		if(zbgycdzList.size()==1){
    			isStandardGyc = false;
    		}
    	}

    	if(isContainZyc){
    		if(isStandardGyc && isStandardZyc && isStandardDyc){
    			tempList.addAll(zbgyckgList);
        		tempList.addAll(zbzyckgList);

        		String addcontent = "";

        		for(PowerDevice dev : tempList){
        			String devName = CZPService.getService().getDevName(dev);

        			String mxName = "";

					if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						//双母
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine,
								SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,
								false, false, true, true);

						for(PowerDevice mx : mxList){
							mxName = "于"+CZPService.getService().getDevName(mx);
							break;
						}

						addcontent += devName+"热备用"+mxName+"、";
					}
        		}

        		if(addcontent.endsWith("、")){
        			addcontent = "（"+addcontent.substring(0, addcontent.length()-1)+"）";
        			addcontent = addcontent.replace("、"+deviceName, "、");
        		}

        		tempList.clear();

        		tempList.add(curDev);
        		tempList.addAll(zbgyckgList);
        		tempList.addAll(zbzyckgList);
        		tempList.addAll(zbdyckgList);

        		replaceStr += stationName+"@将"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用"+addcontent+"/r/n";
        	}else if(!isStandardGyc && !isStandardZyc && !isStandardDyc){
        		tempList.addAll(zbgycdzList);
        		tempList.addAll(zbzycdzList);
        		tempList.addAll(zbdycdzList);

        		replaceStr += getYcHsStrReplace(tempList, stationName);
        	}else{
        		if(!isStandardGyc){
        			tempList.addAll(zbgycdzList);
        		}

        		if(!isStandardZyc){
    				tempList.addAll(zbzycdzList);
    			}

    			if(!isStandardDyc){
    				tempList.addAll(zbdycdzList);
    			}

        		replaceStr += getYcHsStrReplace(tempList, stationName);

        		tempList.clear();

        		tempList.add(curDev);

        		if(isStandardGyc){
        			tempList.addAll(zbgyckgList);
        		}

        		if(isStandardZyc){
    				tempList.addAll(zbzyckgList);
    			}

    			if(isStandardDyc){
    				tempList.addAll(zbdyckgList);
    			}

    			String addcontent = "";

        		for(PowerDevice dev : tempList){
        			String devName = CZPService.getService().getDevName(dev);

        			String mxName = "";

					if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						//双母
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine,
								SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,
								false, false, true, true);

						for(PowerDevice mx : mxList){
							mxName = "于"+CZPService.getService().getDevName(mx);
							break;
						}

						addcontent += devName+"热备用"+mxName+"、";
					}
        		}

        		if(addcontent.endsWith("、")){
        			addcontent = "（"+addcontent.substring(0, addcontent.length()-1)+"）";
        			addcontent = addcontent.replace("、"+deviceName, "、");
        		}

        		replaceStr += stationName+"@将"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用"+addcontent+"/r/n";
        	}
    	}else{
    		if(isStandardGyc && isStandardDyc){
        		String addcontent = "";

        		for(PowerDevice dev : zbgyckgList){
        			String devName = CZPService.getService().getDevName(dev);

        			String mxName = "";

					if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						//双母
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine,
								SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,
								false, false, true, true);

						for(PowerDevice mx : mxList){
							mxName = "于"+CZPService.getService().getDevName(mx);
							break;
						}

						addcontent += devName+"热备用"+mxName+"、";
					}
        		}

        		if(addcontent.endsWith("、")){
        			addcontent = "（"+addcontent.substring(0, addcontent.length()-1)+"）";
        			addcontent = addcontent.replace("、"+deviceName, "、");
        		}

        		tempList.add(curDev);
        		tempList.addAll(zbgyckgList);
        		tempList.addAll(zbdyckgList);

        		replaceStr += stationName+"@将"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用/r/n";
        	}else if(!isStandardGyc && !isStandardDyc){
        		tempList.addAll(zbgycdzList);
        		tempList.addAll(zbdycdzList);

        		replaceStr += getYcHsStrReplace(tempList, stationName);
        	}else{
        		if(!isStandardGyc){
        			tempList.addAll(zbgycdzList);
        		}

    			if(!isStandardDyc){
    				tempList.addAll(zbdycdzList);
    			}

        		replaceStr += getYcHsStrReplace(tempList, stationName);

        		tempList.clear();

        		tempList.add(curDev);

        		if(isStandardGyc){
        			tempList.addAll(zbgyckgList);
        		}

    			if(isStandardDyc){
    				tempList.addAll(zbdyckgList);
    			}

    			String addcontent = "";

        		for(PowerDevice dev : tempList){
        			String devName = CZPService.getService().getDevName(dev);

        			String mxName = "";

					if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						//双母
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine,
								SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,
								false, false, true, true);

						for(PowerDevice mx : mxList){
							mxName = "于"+CZPService.getService().getDevName(mx);
							break;
						}

						addcontent += devName+"热备用"+mxName+"、";
					}
        		}

        		if(addcontent.endsWith("、")){
        			addcontent = "（"+addcontent.substring(0, addcontent.length()-1)+"）";
        			addcontent = addcontent.replace("、"+deviceName, "、");
        		}

        		replaceStr += stationName+"@将"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用"+addcontent+"/r/n";
        	}
    	}

		return replaceStr;
	}

	public static String getTransformerRbyToLbyStrReplace(PowerDevice curDev,String stationName) {
		String replaceStr="";

		List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
		List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
		List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

		List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbzycdzList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbgycdzList = new ArrayList<PowerDevice>();

		List<PowerDevice> tempList = new ArrayList<PowerDevice>();

		boolean isStandardDyc = true;
		boolean isStandardZyc = true;
		boolean isStandardGyc = true;

		boolean isContainZyc = true;

    	for(PowerDevice dev : zbdyckgList){
    		zbdycdzList =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

    		if(zbdycdzList.size()==1){
    			isStandardDyc = false;
    		}
    	}

    	if(zbzyckgList.isEmpty()){
    		isContainZyc = false;
    	}

    	for(PowerDevice dev : zbzyckgList){
    		zbzycdzList =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

    		if(zbzycdzList.size()==1){
    			isStandardZyc = false;
    		}
    	}

    	for(PowerDevice dev : zbgyckgList){
    		zbgycdzList =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

    		if(zbgycdzList.size()==1){
    			isStandardGyc = false;
    		}
    	}

    	if(isContainZyc){
    		if(isStandardGyc && isStandardZyc && isStandardDyc){
        		tempList.add(curDev);
        		tempList.addAll(zbdyckgList);
        		tempList.addAll(zbzyckgList);
        		tempList.addAll(zbgyckgList);

        		replaceStr += stationName+"@将"+CZPService.getService().getDevName(tempList)+"由热备用转冷备用/r/n";
        	}else if(!isStandardGyc && !isStandardZyc && !isStandardDyc){
        		tempList.addAll(zbdycdzList);
        		tempList.addAll(zbzycdzList);
        		tempList.addAll(zbgycdzList);

        		replaceStr += getYcLkStrReplace(tempList, stationName);
        	}else{
        		replaceStr += getYcLkStrReplace(tempList, stationName);

    			if(isStandardDyc){
    				tempList.addAll(zbdyckgList);
    			}

    			if(isStandardZyc){
    				tempList.addAll(zbzyckgList);
    			}

        		if(isStandardGyc){
        			tempList.addAll(zbgyckgList);
        		}

        		replaceStr += stationName+"@将"+CZPService.getService().getDevName(tempList)+"由热备用转冷备用/r/n";
        	}
    	}else{
    		if(isStandardGyc && isStandardDyc){
        		tempList.add(curDev);
        		tempList.addAll(zbdyckgList);
        		tempList.addAll(zbgyckgList);

        		replaceStr += stationName+"@将"+CZPService.getService().getDevName(tempList)+"由热备用转冷备用/r/n";
        	}else if(!isStandardGyc && !isStandardDyc){
        		tempList.addAll(zbdycdzList);
        		tempList.addAll(zbgycdzList);

        		replaceStr += getYcLkStrReplace(tempList, stationName);
        	}else{
        		replaceStr += getYcLkStrReplace(tempList, stationName);

    			if(isStandardDyc){
    				tempList.addAll(zbdyckgList);
    			}

        		if(isStandardGyc){
        			tempList.addAll(zbgyckgList);
        		}

        		replaceStr += stationName+"@将"+CZPService.getService().getDevName(tempList)+"由热备用转冷备用/r/n";
        	}
    	}

		return replaceStr;
	}

	public static String getZbBLTQStrReplace(PowerDevice zb) {
		String replaceStr="";

		if(!zb.getPowerDeviceID().isEmpty()){
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(zb.getPowerStationID());

			PowerDevice station = CBSystemConstants.getPowerStation(zb.getPowerStationID());

			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();

            if (mapStationDevice != null) {
                for (PowerDevice dev : mapStationDevice.values()) {
                    if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
                        if (RuleExeUtil.isDeviceChanged(dev) && zb.getPowerVoltGrade() > dev.getPowerVoltGrade()) {
                            mlkgList.add(dev);
                        }
                    }

                    if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)
                            && dev.getPowerVoltGrade() == station.getPowerVoltGrade()) {

                        zbList.add(dev);
                    }
                }
            }

            if(!mlkgList.isEmpty()){
				if(zbList.size()==2){
					RuleExeUtil.swapDeviceList(zbList);
					replaceStr += "确认"+CZPService.getService().getDevName(zbList)+"具备并列条件/r/n";
				}
			}

			if(zbList.size()==3){
				zbList.remove(zb);

				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList , "请选择具备并列条件的主变：");
				List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

				choosedyckgList.add(zb);

				replaceStr += "确认"+CZPService.getService().getDevName(choosedyckgList)+"具备并列条件/r/n";
			}
		}

		return replaceStr;
	}

	public static String getYcLkStrReplace(List<PowerDevice> devList,String stationName){
		String replaceStr="";

		for(Iterator<PowerDevice> it2 = devList.iterator(); it2.hasNext();){
			PowerDevice dev = it2.next();

			if(!RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
				it2.remove();
			}
		}

		RuleExeUtil.swapLowDeviceList(devList);
		Collections.reverse(devList);

		if(devList.size()==1){
			replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(devList)+"/r/n";
		}else if(devList.size()>1){
			replaceStr += stationName+"@依次拉开"+CZPService.getService().getDevName(devList)+"/r/n";
		}
		return replaceStr;
	}

	public static String getDqZbHbBhTMlkgStrReplace(PowerDevice curDev,String type,String kind,String czlx) {
		String replaceStr="";

		if(curDev.getPowerDeviceID().isEmpty()){
			return replaceStr;
		}

		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		List<PowerDevice> tempList = new ArrayList<PowerDevice>();

		PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());

		double gycvolt = 0;
		double zycvolt = 0;
		double dycvolt = 0;

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

        if (mapStationDevice != null) {
            for (PowerDevice dev : mapStationDevice.values()) {
                if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)) {
                    zbList.add(dev);

                    gycvolt = RuleExeUtil.getTransformerVolByType(dev, "high");
                    zycvolt = RuleExeUtil.getTransformerVolByType(dev, "middle");
                    dycvolt = RuleExeUtil.getTransformerVolByType(dev, "low");

                    if (gycvolt > 0 && zycvolt > 0 && dycvolt > 0) {
                        break;
                    }
                }
            }
        }

        if(czlx.equals("停电")){
			tempList.add(curDev);
		}else{
			tempList.addAll(zbList);
		}
		String tempName = CZPService.getService().getDevName(tempList);

		List<PowerDevice>  zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
		List<PowerDevice>  zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
		List<PowerDevice>  zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);

		if(!zbgyckgList.isEmpty()){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "",
					false, true, false, true);
			gycmlkgList.addAll(kgList);
		}

		if(!zbzyckgList.isEmpty()){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbzyckgList.get(0), SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "",
					false, true, false, true);
			zycmlkgList.addAll(kgList);
		}

		if(!zbdyckgList.isEmpty()){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "",
					false, true, false, true);
			dycmlkgList.addAll(kgList);
		}

		for(Iterator<PowerDevice> itor = dycmlkgList.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();

			if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
				itor.remove();
			}
		}


		if(type.equals("low")){
			if(!dycmlkgList.isEmpty()){
				PowerDevice mlkg = dycmlkgList.get(0);
				String mlkgName = CZPService.getService().getDevName(mlkg);
				if(station.getPowerVoltGrade() == 35){
					replaceStr += kind+tempName+(int)dycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
				}else{
					replaceStr += kind+tempName+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
				}
			}
		}

		if(type.equals("midlow")){
			if(!dycmlkgList.isEmpty()){
				PowerDevice mlkg = dycmlkgList.get(0);
				String mlkgName = CZPService.getService().getDevName(mlkg);
				if(station.getPowerVoltGrade() == 35){
					replaceStr += kind+tempName+(int)dycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
				}else{
					replaceStr += kind+tempName+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
				}
			}

			if(!zycmlkgList.isEmpty()){
				PowerDevice mlkg = zycmlkgList.get(0);
				String mlkgName = CZPService.getService().getDevName(mlkg);
				replaceStr += kind+tempName+"第Ⅰ、Ⅱ套"+(int)zycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
			}
		}

		if(type.equals("highmidlow")){
			if(!dycmlkgList.isEmpty()){
				PowerDevice mlkg = dycmlkgList.get(0);
				String mlkgName = CZPService.getService().getDevName(mlkg);
				if(station.getPowerVoltGrade() == 35){
					replaceStr += kind+tempName+(int)dycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
				}else{
					replaceStr += kind+tempName+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
				}
			}

			if(!zycmlkgList.isEmpty()){
				PowerDevice mlkg = zycmlkgList.get(0);
				String mlkgName = CZPService.getService().getDevName(mlkg);
				replaceStr += kind+tempName+"第Ⅰ、Ⅱ套"+(int)zycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
			}

			if(station.getPowerVoltGrade() == 35){
				if(!gycmlkgList.isEmpty()){
					PowerDevice mlkg = gycmlkgList.get(0);
					String mlkgName = CZPService.getService().getDevName(mlkg);
					replaceStr += kind+tempName+(int)gycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
				}
			}else{
				if(!gycmlkgList.isEmpty()){
					PowerDevice mlkg = gycmlkgList.get(0);
					String mlkgName = CZPService.getService().getDevName(mlkg);
					replaceStr += kind+tempName+"第Ⅰ、Ⅱ套"+(int)gycvolt+"kV侧后备保护动作跳"+mlkgName+"/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String getDqZbHbBhTMlkgStrReplace(List<PowerDevice> zbList,String kind) {
		String replaceStr="";

		if(zbList.isEmpty()){
			return "";
		}

		RuleExeUtil.swapDeviceList(zbList);

		boolean fbzjx = false;

		for(PowerDevice zb : zbList){
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zb);

			if(zbdyckgList.size()==2){
				fbzjx = true;
			}
		}

		if(fbzjx){
			for(PowerDevice zb : zbList){
				List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zb);
				List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother,
						false, true, false, true);

				if(zbdyckgList.size()==1){
					replaceStr += kind+CZPService.getService().getDevName(zb)+"第Ⅰ、Ⅱ套10kV侧后备保护动作跳"+CZPService.getService().getDevName(kgList)+"/r/n";
				}else if(zbdyckgList.size()==2){
					replaceStr += kind+CZPService.getService().getDevName(zb)+"第Ⅰ、Ⅱ套10kV侧分支X（X侧）后备保护动作跳"+CZPService.getService().getDevName(kgList)+"/r/n";
				}
			}
		}else{
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zbList.get(0));
			List<PowerDevice> dycmlkgList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother,
					false, true, false, true);

			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(zbList.get(0));
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();

			if(!zbzyckgList.isEmpty()){
				zycmlkgList = RuleExeUtil.getDeviceList(zbzyckgList.get(0), SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother,
						false, true, false, true);
			}

			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(zbList.get(0));

			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();

			if(!zbgyckgList.isEmpty()){
				gycmlkgList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother,
						false, true, false, true);
			}

			for(Iterator<PowerDevice> itor = dycmlkgList.iterator();itor.hasNext();){
				PowerDevice rbykg = itor.next();

				if(!RuleExeUtil.isDeviceChanged(rbykg)){
					itor.remove();
				}
			}

			if(!dycmlkgList.isEmpty()){
				if(zbList.get(0).getPowerVoltGrade()>35){
					replaceStr += kind+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"
							+(int)dycmlkgList.get(0).getPowerVoltGrade()+"kV侧后备保护动作跳"
							+CZPService.getService().getDevName(dycmlkgList)+"/r/n";
				}else{
					replaceStr += kind+CZPService.getService().getDevName(zbList)
							+(int)dycmlkgList.get(0).getPowerVoltGrade()+"kV侧后备保护动作跳"
							+CZPService.getService().getDevName(dycmlkgList)+"/r/n";
				}
			}

			for(Iterator<PowerDevice> itor = zycmlkgList.iterator();itor.hasNext();){
				PowerDevice rbykg = itor.next();

				if(!RuleExeUtil.isDeviceChanged(rbykg)){
					itor.remove();
				}
			}

			if(!zycmlkgList.isEmpty()){
				if(zbList.get(0).getPowerVoltGrade()>35){
					replaceStr += kind+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"
							+(int)zycmlkgList.get(0).getPowerVoltGrade()+"kV侧后备保护动作跳"
							+CZPService.getService().getDevName(zycmlkgList)+"/r/n";
				}else{
					replaceStr += kind+CZPService.getService().getDevName(zbList)
							+(int)zycmlkgList.get(0).getPowerVoltGrade()+"kV侧后备保护动作跳"
							+CZPService.getService().getDevName(zycmlkgList)+"/r/n";
				}
			}

			for(Iterator<PowerDevice> itor = gycmlkgList.iterator();itor.hasNext();){
				PowerDevice rbykg = itor.next();

				if(!RuleExeUtil.isDeviceChanged(rbykg)){
					itor.remove();
				}
			}

			if(!gycmlkgList.isEmpty()){
				replaceStr += kind+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"
						+(int)gycmlkgList.get(0).getPowerVoltGrade()+"kV侧后备保护动作跳"
						+CZPService.getService().getDevName(gycmlkgList)+"/r/n";
			}
		}

		return replaceStr;
	}

	//后备保护改投“012断路器合上的运行方式”定值
	public static String getDqZbHbBhTMlkgDzStrReplace(PowerDevice curDev,String type,String kind) {
		String replaceStr="";

		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
		List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
		List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

		PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());

		if(!zbgyckgList.isEmpty()){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "",
					false, true, false, true);
			gycmlkgList.addAll(kgList);
		}

		if(!zbzyckgList.isEmpty()){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbzyckgList.get(0), SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "",
					false, true, false, true);
			zycmlkgList.addAll(kgList);
		}

		if(!zbdyckgList.isEmpty()){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother,
					false, true, false, true);
			dycmlkgList.addAll(kgList);
		}

		List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchLow(curDev);

		for (PowerDevice sw : swList) {
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(sw, SystemConstants.MotherLine,
					SystemConstants.PowerTransformer, true, false, true);

			if(!mxList.isEmpty()){
				PowerDevice mx = mxList.get(0);

				dycmlkgList = RuleExeUtil.getDeviceList(mx,null,SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"",
						false, true, true, true,true);
			}
		}

		if(kind.equals("停电")){
			if(type.equals("midlow")){
				if(!zycmlkgList.isEmpty()){
					String volt = (int)zycmlkgList.get(0).getPowerVoltGrade()+"kV";

					replaceStr +=  "退出"+volt+"备自投装置/r/n";

					for(PowerDevice zycmlkg : zycmlkgList){
						List<PowerDevice> zbList = getMlkgGlZb(zycmlkg);

						if(RuleExeUtil.getDeviceEndStatus(zycmlkg).equals("0")){
							replaceStr +=  "将"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护改投“"
									+CZPService.getService().getDevNum(zycmlkgList.get(0))+"断路器合上的运行方式 ”定值/r/n";
						}

						replaceStr +=  "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护动作跳"
								+CZPService.getService().getDevName(zycmlkg)+"/r/n";
					}
				}

				if(!dycmlkgList.isEmpty()){
					String volt = (int)dycmlkgList.get(0).getPowerVoltGrade()+"kV";

					replaceStr += get10kVBztZzStrReplace(dycmlkgList,"退出");

					for(PowerDevice dycmlkg : dycmlkgList){
						List<PowerDevice> zbList = getMlkgGlZb(dycmlkg);

						if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("0")){
							replaceStr +=  "将"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护改投“"
									+CZPService.getService().getDevNum(dycmlkg)+"断路器合上的运行方式”定值/r/n";
						}

						replaceStr +=  "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护动作跳"
								+CZPService.getService().getDevName(dycmlkg)+"/r/n";
					}
				}
			}

			if(type.equals("low")){
				if(!dycmlkgList.isEmpty()){
					String volt = (int)dycmlkgList.get(0).getPowerVoltGrade()+"kV";

					replaceStr += get10kVBztZzStrReplace(dycmlkgList,"退出");

					for(PowerDevice dycmlkg : dycmlkgList){
						List<PowerDevice> zbList = getMlkgGlZb(dycmlkg);

						if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("0")){
							if(curDev.getPowerVoltGrade() > 35){
								replaceStr +=  "将"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护改投“"
										+CZPService.getService().getDevNum(dycmlkg)+"断路器合上的运行方式”定值/r/n";
							}else{
								replaceStr +=  "将"+CZPService.getService().getDevName(zbList)+volt+"侧后备保护改投“"+
										CZPService.getService().getDevNum(dycmlkg)+"断路器合上的运行方式”定值/r/n";
							}
						}

						if(curDev.getPowerVoltGrade() > 35){
							replaceStr += getDqZbHbBhTMlkgStrReplace(zbList, "投入");
						}
					}
				}
			}
		}else{
			if(type.equals("midlow")){
				if(!zycmlkgList.isEmpty()){
					String volt = (int)zycmlkgList.get(0).getPowerVoltGrade()+"kV";

					for(PowerDevice zycmlkg : zycmlkgList){
						List<PowerDevice> zbList = getMlkgGlZb(zycmlkg);

						if(RuleExeUtil.getDeviceEndStatus(zycmlkg).equals("1")){
							replaceStr +=  "投入"+volt+"备自投装置/r/n";

							replaceStr +=  "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护动作闭锁"+volt+"备自投装置/r/n";

							if(station.getPowerVoltGrade() == 110&&volt.equals("35kV")){
								replaceStr +=  "投入35kV#1、#2弧光保护动作闭锁35kV备自投装置/r/n";
							}

							replaceStr +=  "将"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护改投“"
									+CZPService.getService().getDevNum(zycmlkg)+"断路器断开的运行方式 ”定值/r/n";
						}
					}
				}

				if(!dycmlkgList.isEmpty()){
					String volt = (int)dycmlkgList.get(0).getPowerVoltGrade()+"kV";


					for(PowerDevice dycmlkg : dycmlkgList){
						List<PowerDevice> zbList = getMlkgGlZb(dycmlkg);

						if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("1")){
							replaceStr += get10kVBztZzStrReplace(dycmlkgList,"投入");

							replaceStr +=  "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护动作闭锁"+volt+"备自投装置/r/n";

							if(station.getPowerVoltGrade() == 110&&volt.equals("10kV")){
								replaceStr +=  "投入10kV#1、#2弧光保护动作闭锁10kV备自投装置/r/n";
							}
							replaceStr +=  "将"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护改投“"
									+CZPService.getService().getDevNum(dycmlkg)+"断路器断开的运行方式”定值/r/n";
						}
					}
				}
			}

			if(type.equals("low")){
				if(!dycmlkgList.isEmpty()){
					String volt = (int)dycmlkgList.get(0).getPowerVoltGrade()+"kV";


					for(PowerDevice dycmlkg : dycmlkgList){
						List<PowerDevice> zbList = getMlkgGlZb(dycmlkg);

						if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("1")){
							replaceStr += get10kVBztZzStrReplace(dycmlkgList,"投入");

							replaceStr +=  "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护动作闭锁"+volt+"备自投装置/r/n";

							if(station.getPowerVoltGrade() == 110&&volt.equals("10kV")){
								replaceStr +=  "投入10kV#1、#2弧光保护动作闭锁10kV备自投装置/r/n";
							}
							replaceStr +=  "将"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+volt+"侧后备保护改投“"
									+CZPService.getService().getDevNum(dycmlkg)+"断路器断开的运行方式”定值/r/n";
						}
					}
				}
			}
		}

		return replaceStr;
	}

	public static boolean getZbIsJdzybStrReplace(PowerDevice zb) {
		if(zb.getPowerDeviceID().isEmpty()){
			return false;
		}

		List<PowerDevice> jdzybkgList =  new ArrayList<PowerDevice>();
	    List<PowerDevice> zbdyckglist = RuleExeUtil.getTransformerSwitchLow(zb);

		 if(!zbdyckglist.isEmpty()){
	    	List<PowerDevice> mxList = RuleExeUtil.getDeviceList(zbdyckglist.get(0), SystemConstants.MotherLine,
					SystemConstants.PowerTransformer, true, true, true);

	    	if(!mxList.isEmpty()){
		    	List<PowerDevice> mxkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch,
						SystemConstants.PowerTransformer, true, true, true);

		    	if(!mxkgList.isEmpty()){
		    		for(PowerDevice dev : mxkgList){
		    			if(dev.getDeviceType().equals(SystemConstants.Switch)){
							if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
								jdzybkgList.add(dev);
							}
						}
		    		}
		    	}
	    	}
	    }


		 HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(zb.getPowerStationID());

		List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();

        if (mapStationDevice != null) {
            for (PowerDevice dev : mapStationDevice.values()) {
                if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
                    if (RuleExeUtil.isDeviceChanged(dev) && 10 == dev.getPowerVoltGrade()) {
                        mlkgList.add(dev);
                    }
                }
            }
        }

        return !jdzybkgList.isEmpty() && !mlkgList.isEmpty();
    }

	public static String getJdzybStrReplace(List<PowerDevice> zbList) {
		String replaceStr="";

		List<PowerDevice> jdzybkgList =  new ArrayList<PowerDevice>();
		List<PowerDevice> mlkgList =  new ArrayList<PowerDevice>();

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(zbList.get(0).getPowerStationID());

        if (mapStationDevice != null) {
            for (PowerDevice dev : mapStationDevice.values()) {
                if (dev.getPowerVoltGrade() == 10 &&
                        (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))) {
                    mlkgList.add(dev);
                }
            }
        }

        boolean flag = false;

		for(PowerDevice dev : mlkgList){
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
				flag = true;
			}
		}

		if(flag){
			List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
			EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList, "请选择需要投入接地变保护对应的主变：");
			chooseEquips=ecc.getChooseEquip();

			if(!chooseEquips.isEmpty()){
				List<PowerDevice> zbdyckglist = RuleExeUtil.getTransformerSwitchLow(chooseEquips.get(0));

				if(!zbdyckglist.isEmpty()){
			    	List<PowerDevice> mxList = RuleExeUtil.getDeviceList(zbdyckglist.get(0), SystemConstants.MotherLine,
							SystemConstants.PowerTransformer, true, true, true);

			    	if(!mxList.isEmpty()){
				    	List<PowerDevice> mxkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch,
								SystemConstants.PowerTransformer, true, true, true);

				    	if(!mxkgList.isEmpty()){
				    		for(PowerDevice dev : mxkgList){
				    			if(dev.getDeviceType().equals(SystemConstants.Switch)){
									if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
										jdzybkgList.add(dev);
									}
								}
				    		}
				    	}
			    	}
			    }

				if(!jdzybkgList.isEmpty()){
					String jdbName = jdzybkgList.get(0).getPowerDeviceName();

					jdbName = jdbName.substring(0, jdbName.lastIndexOf("接地")+2);

					String num = CZPService.getService().getDevNum(jdbName);

					replaceStr += "投入10kV"+num+"接地变小电阻自投切功能/r/n";
					replaceStr += "投入10kV"+num+"接地变保护动作跳"+CZPService.getService().getDevName(zbdyckglist)+"、"
							+CZPService.getService().getDevName(mlkgList)+"/r/n";
			    }
			}
		}

		return replaceStr;
	}


	public String getHsdcnrStrReplace(PowerDevice curDev) {
		String replaceStr="";

		String begin = CBSystemConstants.getCurRBM().getBeginStatus();
		String end = CBSystemConstants.getCurRBM().getEndState();

		List<PowerDevice> xlkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch,
				SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, "",
				false, true, true, true);

		for(Iterator<PowerDevice> itor = xlkglist.iterator();itor.hasNext();){
			PowerDevice xlkg = itor.next();

			//复电
			if(Integer.parseInt(begin)>Integer.parseInt(end)){
				if(!RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
					itor.remove();
				}
			}else{
				if(!RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
					itor.remove();
				}
			}
		}

	    for(PowerDevice xlkg : xlkglist){
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(xlkg, SystemConstants.InOutLine,
					SystemConstants.PowerTransformer, true, true, true);

			if(!lineList.isEmpty()){
				List<PowerDevice> otherlineList = RuleExeUtil.getLineOtherSideList(lineList.get(0));

				if(!otherlineList.isEmpty()){
					for(PowerDevice otherline: otherlineList){
						PowerDevice station = CBSystemConstants.getPowerStation(otherline.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);

						List<PowerDevice> otherlinexlkgList = RuleExeUtil.getLinkedSwitch(otherline);
						List<PowerDevice> dzlist = RuleExeUtil.getDeviceList(otherline, SystemConstants.SwitchSeparate,
								SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXLS, "",
								false, true, true, true);

						List<PowerDevice> otherlinexlkghotList = new ArrayList<PowerDevice>();
						List<PowerDevice> otherlinexlkgcoldList = new ArrayList<PowerDevice>();

						List<PowerDevice> otherlinexldzdkList = new ArrayList<PowerDevice>();

						for(PowerDevice otherlinexlkg : otherlinexlkgList){
							if(otherlinexlkg.getDeviceStatus().equals("1")){
								otherlinexlkghotList.add(otherlinexlkg);
							}

							if(otherlinexlkg.getDeviceStatus().equals("2")){
								otherlinexlkgcoldList.add(otherlinexlkg);
							}
						}

						for(PowerDevice dz : dzlist){
							if(dz.getDeviceStatus().equals("1")){
								otherlinexldzdkList.add(dz);
							}
						}

						if(!otherlinexlkghotList.isEmpty()){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(otherlinexlkghotList)+"热备用/r/n";
						}

						if(!otherlinexldzdkList.isEmpty()){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(otherlinexldzdkList)+"在拉开位置/r/n";
						}

						if(!otherlinexlkgcoldList.isEmpty()){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(otherlinexlkgcoldList)+"冷备用/r/n";
						}
					}
				}
			}
		}
		return replaceStr;
	}

	//主变零序保护联跳小电断路器（弹窗间隙、零序）
	public static String getXzZbLxbhltxdStrReplace(PowerDevice zb,String type) {
		String replaceStr="";

		if(zb != null && !zb.getPowerDeviceID().isEmpty()){
			String[] arr = {"零序","间隙","不选"};

			int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择主变保护类型",
					SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
			if(sel==0){
				replaceStr += type+CZPService.getService().getDevName(zb)+"零序保护联跳小电断路器/r/n";
			}else if(sel==1){
				replaceStr +=  type+CZPService.getService().getDevName(zb)+"间隙保护联跳小电断路器/r/n";
			}
		}

		return replaceStr;
	}

	//主变零序保护联跳小电断路器（弹窗间隙、零序）
	public static String getXzZbListLxbhltxdStrReplace(List<PowerDevice> zbList,String type) {
		String replaceStr="";

		if(!zbList.isEmpty()){
			String[] arr = {"零序","间隙","不选"};

			int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择主变保护类型",
					SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
			if(sel==0){
				replaceStr += type+CZPService.getService().getDevName(zbList)+"零序保护联跳小电断路器/r/n";
			}else if(sel==1){
				replaceStr +=  type+CZPService.getService().getDevName(zbList)+"间隙保护联跳小电断路器/r/n";

			}
		}

		return replaceStr;
	}

	public static String getXzZbZxdTcStrReplace(List<PowerDevice> zbList) {
		String replaceStr="";

		PowerDevice pd = CBSystemConstants.getCurRBM().getPd();

		if(pd != null){
			String ce = "";
			PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());

			if(station.getPowerVoltGrade() > 35){
				if(pd.getPowerVoltGrade() == 220){
					ce = "高、中压侧";
				}

				List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList, "请选择需要退出中性点的主变：");
				chooseEquips=ecc.getChooseEquip();

				if(!chooseEquips.isEmpty()){
					replaceStr += "退出"+CZPService.getService().getDevName(chooseEquips)+ce+"中性点及其零序保护/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String getXzZbZxdTrStrReplace(List<PowerDevice> zbList) {
		String replaceStr="";

		PowerDevice pd = CBSystemConstants.getCurRBM().getPd();

		if(pd != null){
			String ce = "";

			if(pd.getPowerVoltGrade() == 220){
				ce = "高、中压侧";
			}

			List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
			EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList, "请选择需要投入中性点的主变：");
			chooseEquips=ecc.getChooseEquip();

			if(!chooseEquips.isEmpty()){
				replaceStr += "投入"+CZPService.getService().getDevName(chooseEquips)+ce+"中性点及其零序保护/r/n";
			}
		}

		return replaceStr;
	}

	public static String getXzZbZxdStrReplace(List<PowerDevice> zbList) {
		String replaceStr="";

		PowerDevice pd = CBSystemConstants.getCurRBM().getPd();

		if(pd != null){
			String ce = "";

			if(pd.getPowerVoltGrade() == 220){
				ce = "高、中压侧";
			}

			List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
			EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList, "请选择需要投入中性点的主变：");
			chooseEquips=ecc.getChooseEquip();

			if(chooseEquips.size()==1){
				replaceStr += "确认站内仅投"+CZPService.getService().getDevName(chooseEquips)+ce+"中性点及其零序保护/r/n";
			}else if(chooseEquips.size()>1){
				replaceStr += "确认站内投"+CZPService.getService().getDevName(chooseEquips)+ce+"中性点及其零序保护/r/n";
			}
		}

		return replaceStr;
	}


	//后备保护改投“012断路器合上的运行方式”定值000
	public static String get10kVBztZzStrReplace(List<PowerDevice> mlkgList,String kind) {
		String replaceStr="";
		List<PowerDevice> alldycmlkgList = new ArrayList<PowerDevice>();

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(mlkgList.get(0).getPowerStationID());

        if (mapStationDevice != null) {
            for (PowerDevice dev : mapStationDevice.values()) {
                if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) && dev.getPowerVoltGrade() == 10) {
                    alldycmlkgList.add(dev);
                }
            }
        }

        if(alldycmlkgList.size()>1){
			for(PowerDevice mlkg : mlkgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine,
						SystemConstants.PowerTransformer, true, true, true);

				RuleExeUtil.swapDeviceList(mxList);

				String mxnum = "";

				for(PowerDevice mx : mxList){
					mxnum += CZPService.getService().getDevNum(mx)+"/";
				}

				if(mxnum.endsWith("/")){
					mxnum = mxnum.substring(0, mxnum.length()-1);
				}

				replaceStr +=  kind+"10kV"+mxnum+"备自投装置/r/n";
			}
		}else{
			replaceStr +=  kind+"10kV备自投装置/r/n";
		}

		return replaceStr;
	}

	public static String getZbHbBhStrReplace(PowerDevice curDev,String type,String kind) {
		String replaceStr="";

		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();

		PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());

		double gycvolt = 0;
		double zycvolt = 0;
		double dycvolt = 0;

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());


        if (mapStationDevice != null) {
            for (PowerDevice dev : mapStationDevice.values()) {
                if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)) {
                    gycvolt = RuleExeUtil.getTransformerVolByType(dev, "high");
                    zycvolt = RuleExeUtil.getTransformerVolByType(dev, "middle");
                    dycvolt = RuleExeUtil.getTransformerVolByType(dev, "low");

                    if (gycvolt > 0 && zycvolt > 0 && dycvolt > 0) {
                        break;
                    }
                }
            }
        }

        if (mapStationDevice != null) {
            for (PowerDevice dev : mapStationDevice.values()) {
                if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
                    if (zycvolt == dev.getPowerVoltGrade()) {
                        zycmlkgList.add(dev);
                    } else if (dycvolt == dev.getPowerVoltGrade()) {
                        dycmlkgList.add(dev);
                    } else if (gycvolt == dev.getPowerVoltGrade()) {
                        gycmlkgList.add(dev);
                    }
                }
            }
        }

        if(type.equals("low")){
			if(dycmlkgList.size() == 1){
				PowerDevice mlkg = dycmlkgList.get(0);

				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
						CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
						false, true, false, true);
				RuleExeUtil.swapDeviceList(zbList);

				String zbNames = CZPService.getService().getDevName(zbList);
				if(station.getPowerVoltGrade() == 220){
					replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护及35kV母差保护动作闭锁"+(int)dycvolt+"kV备自投装置/r/n";
				}else{
					if(station.getPowerVoltGrade() == 35){
						replaceStr += kind+zbNames+(int)dycvolt+"kV侧后备保护动作闭锁"+(int)dycvolt+"kV备自投装置/r/n";
					}else{
						replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护动作闭锁"+(int)dycvolt+"kV备自投装置/r/n";
					}
				}
			}else if(dycmlkgList.size() > 1){
				for(PowerDevice mlkg:dycmlkgList){

					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
							CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
							false, true, false, true);
					RuleExeUtil.swapDeviceList(zbList);

					String zbNames = CZPService.getService().getDevName(zbList);
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine,
							SystemConstants.PowerTransformer,true, false, true);

					String mxNum = "";

					RuleExeUtil.swapDeviceList(mxList);

					for(PowerDevice mx : mxList){
						mxNum += CZPService.getService().getDevNum(mx)+"/";
					}

					if(mxNum.endsWith("/")){
						mxNum = mxNum.substring(0, mxNum.length()-1);
					}

					replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护动作闭锁"+(int)dycvolt+"kV"+mxNum+"备自投装置/r/n";
				}
			}
		}

		if(type.equals("midlow")){
			if(dycmlkgList.size() == 1){
				PowerDevice mlkg = dycmlkgList.get(0);

				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
						CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
						false, true, false, true);
				RuleExeUtil.swapDeviceList(zbList);

				String zbNames = CZPService.getService().getDevName(zbList);
				replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护动作闭锁"+(int)dycvolt+"kV备自投装置/r/n";
			}else if(dycmlkgList.size() > 1){
				for(PowerDevice mlkg:dycmlkgList){

					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
							CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
							false, true, false, true);
					RuleExeUtil.swapDeviceList(zbList);

					String zbNames = CZPService.getService().getDevName(zbList);
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine,
							SystemConstants.PowerTransformer,true, false, true);

					String mxNum = "";

					RuleExeUtil.swapDeviceList(mxList);

					for(PowerDevice mx : mxList){
						mxNum += CZPService.getService().getDevNum(mx)+"/";
					}

					if(mxNum.endsWith("/")){
						mxNum = mxNum.substring(0, mxNum.length()-1);
					}

					replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护动作闭锁"+(int)dycvolt+"kV"+mxNum+"备自投装置/r/n";
				}
			}

			if(zycmlkgList.size() == 1){
				PowerDevice mlkg = zycmlkgList.get(0);

				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
						CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
						false, true, false, true);
				RuleExeUtil.swapDeviceList(zbList);

				String zbNames = CZPService.getService().getDevName(zbList);
				replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)zycvolt+"kV侧后备保护动作闭锁"+(int)zycvolt+"kV备自投装置/r/n";
			}else if(zycmlkgList.size() > 1){
				for(PowerDevice mlkg:zycmlkgList){

					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
							CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
							false, true, false, true);
					RuleExeUtil.swapDeviceList(zbList);

					String zbNames = CZPService.getService().getDevName(zbList);
					replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)zycvolt+"kV侧后备保护动作闭锁"+(int)zycvolt+"备自投装置/r/n";
				}
			}
		}

		if(type.equals("highmidlow")){
			if(dycmlkgList.size() == 1){
				PowerDevice mlkg = dycmlkgList.get(0);

				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
						CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
						false, true, false, true);
				RuleExeUtil.swapDeviceList(zbList);

				String zbNames = CZPService.getService().getDevName(zbList);
				replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护动作闭锁"+(int)dycvolt+"kV备自投装置/r/n";
			}else if(dycmlkgList.size() > 1){
				for(PowerDevice mlkg:dycmlkgList){

					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
							CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
							false, true, false, true);
					RuleExeUtil.swapDeviceList(zbList);

					String zbNames = CZPService.getService().getDevName(zbList);
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine,
							SystemConstants.PowerTransformer,true, false, true);

					String mxNum = "";

					RuleExeUtil.swapDeviceList(mxList);

					for(PowerDevice mx : mxList){
						mxNum += CZPService.getService().getDevNum(mx)+"/";
					}

					if(mxNum.endsWith("/")){
						mxNum = mxNum.substring(0, mxNum.length()-1);
					}

					replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)dycvolt+"kV侧后备保护动作闭锁"+(int)dycvolt+"kV"+mxNum+"备自投装置/r/n";
				}
			}

			if(zycmlkgList.size() == 1){
				PowerDevice mlkg = zycmlkgList.get(0);

				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
						CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
						false, true, false, true);
				RuleExeUtil.swapDeviceList(zbList);

				String zbNames = CZPService.getService().getDevName(zbList);
				replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)zycvolt+"kV侧后备保护动作闭锁"+(int)zycvolt+"kV备自投装置/r/n";
			}else if(zycmlkgList.size() > 1){
				for(PowerDevice mlkg:zycmlkgList){

					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
							CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
							false, true, false, true);
					RuleExeUtil.swapDeviceList(zbList);

					String zbNames = CZPService.getService().getDevName(zbList);
					replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)zycvolt+"kV侧后备保护动作闭锁"+(int)zycvolt+"备自投装置/r/n";
				}
			}

			if(gycmlkgList.size() == 1){
				PowerDevice mlkg = gycmlkgList.get(0);

				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
						CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
						false, true, false, true);
				RuleExeUtil.swapDeviceList(zbList);

				String zbNames = CZPService.getService().getDevName(zbList);
				replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)gycvolt+"kV侧后备保护动作闭锁"+(int)gycvolt+"kV备自投装置/r/n";
			}else if(gycmlkgList.size() > 1){
				for(PowerDevice mlkg:gycmlkgList){

					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, "",
							CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
							false, true, false, true);
					RuleExeUtil.swapDeviceList(zbList);

					String zbNames = CZPService.getService().getDevName(zbList);
					replaceStr += kind+zbNames+"第Ⅰ、Ⅱ套"+(int)gycvolt+"kV侧后备保护动作闭锁"+(int)gycvolt+"备自投装置/r/n";
				}
			}
		}

		return replaceStr;
	}


	//将母线及母线设备
	public static String getCzMotherLineDevStrReplace(List<PowerDevice> devList,PowerDevice motherline,PowerDevice curzb,String stationName,String operate){
		String replaceStr="";
		String mxName = CZPService.getService().getDevName(motherline);
		String devNames = CZPService.getService().getDevName(devList);

		if(!devList.isEmpty()){
			if(curzb!=null){
				if(RuleUtil.isTransformerNQ(curzb)){
					replaceStr += "将"+devNames+"、"+mxName+operate+"/r/n";
				}else{
					replaceStr += "将"+devNames+"、"+mxName+"及母线设备"+operate+"/r/n";
				}
			}else{
				replaceStr += "将"+devNames+"、"+mxName+"及母线设备"+operate+"/r/n";
			}
		}else{
			if(curzb!=null){
				if(RuleUtil.isTransformerNQ(curzb)){
					replaceStr += "将"+mxName+operate+"/r/n";
				}else{
					replaceStr += "将"+mxName+"及母线设备"+operate+"/r/n";
				}
			}else{
				replaceStr += "将"+mxName+"及母线设备"+operate+"/r/n";
			}
		}

		return replaceStr;
	}

	public static String getZbNqjxBhStrReplace(List<PowerDevice> zbList,List<PowerDevice> gycmlkglist,List<PowerDevice> gycxlkglist){
		String replaceStr="";

		String bhtype = "";

		String[] arr = {"第Ⅰ、Ⅱ套差动、高后备、非电量保护","第Ⅰ、Ⅱ套差动、后备（跳两侧及桥）、非电量保护","不选"};

		int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择保护类型",
				SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
		if(sel==0){
			bhtype =  "第Ⅰ、Ⅱ套差动、高后备、非电量保护";
		}else if(sel==1){
			bhtype =  "第Ⅰ、Ⅱ套差动、后备（跳两侧及桥）、非电量保护";
		}else if(sel==2){
			return replaceStr;
		}else{
			bhtype =  "第Ⅰ、Ⅱ套差动、高后备、非电量保护";
		}

		if(!gycmlkglist.isEmpty()){
			if(gycmlkglist.get(0).getDeviceStatus().equals("1")){

				RuleExeUtil.swapDeviceList(zbList);

				replaceStr += "投入"+CZPService.getService().getDevName(zbList)+bhtype+"动作闭锁110kV备自投装置/r/n";
			}else{
				if(!gycxlkglist.isEmpty()){
					for(PowerDevice gycxlkg : gycxlkglist){
						if(gycxlkg.getDeviceStatus().equals("0")){
							List<PowerDevice> templist = RuleExeUtil.getDeviceList(gycxlkg, SystemConstants.PowerTransformer,
									SystemConstants.Switch, true, false, true);

							replaceStr += "退出"+CZPService.getService().getDevName(templist)+bhtype+"动作闭锁110kV备自投装置/r/n";
						}else if(gycxlkg.getDeviceStatus().equals("1")){
							List<PowerDevice> templist = RuleExeUtil.getDeviceList(gycxlkg, SystemConstants.PowerTransformer,
									SystemConstants.Switch, true, false, true);

							replaceStr += "投入"+CZPService.getService().getDevName(templist)+bhtype+"动作闭锁110kV备自投装置/r/n";
						}
					}
				}
			}
		}

		return replaceStr;
	}

	public static String getXhxqStrReplace(List<PowerDevice> zbList,String kind) {
		String replaceStr="";

		PowerDevice curDev = CBSystemConstants.getCurRBM().getPd();
		String curDevName = CZPService.getService().getDevName(curDev);

		if(curDev.getDeviceType().equals(SystemConstants.MotherLine)){
			List<PowerDevice> zblist  = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer,
					"",true, true, true);

			if(!zblist.isEmpty()){
				 curDev = zblist.get(0);
			}
		}

		if(zbList.size()==2){
			List<PowerDevice> xhxqList = new ArrayList<PowerDevice>();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(zbList.get(0).getPowerStationID());

            if (mapStationDevice != null) {
                for (PowerDevice dev : mapStationDevice.values()) {
                    if (dev.getPowerDeviceName().contains("3010") || dev.getPowerDeviceName().contains("3020")) {
                        xhxqList.add(dev);
                    }
                }
            }

            if(!xhxqList.isEmpty()){
				if(kind.equals("停电")){
					for(PowerDevice xhxq : xhxqList){
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(xhxq).equals("0")){
							if(xhxq.getPowerDeviceName().contains("3010")&&curDev.getPowerDeviceName().contains("#1")){
								replaceStr += "确认35kV消弧线圈运行于"+curDevName+"35kV侧（3010隔离开关在合闸位置，3020隔离开关在拉开位置）/r/n";
								replaceStr += "将35kV消弧线圈由"+curDevName+"35kV侧运行倒至110kV#2主变35kV侧运行/r/n";
							}else if(xhxq.getPowerDeviceName().contains("3020")&&curDev.getPowerDeviceName().contains("#2")){
								replaceStr += "确认35kV消弧线圈运行于"+curDevName+"35kV侧（3020隔离开关在合闸位置，3010隔离开关在拉开位置）/r/n";
								replaceStr += "将35kV消弧线圈由"+curDevName+"35kV侧运行倒至110kV#1主变35kV侧运行/r/n";
							}
						}
					}

					if(curDev.getPowerDeviceName().contains("#1")){
						replaceStr += "确认35kV消弧线圈3010隔离开关在拉开位置/r/n";
					}else{
						replaceStr += "确认35kV消弧线圈3020隔离开关在拉开位置/r/n";
					}
				}else if(kind.equals("全部拉开")){
					replaceStr += "确认35kV消弧线圈3010隔离开关在拉开位置/r/n";
					replaceStr += "确认35kV消弧线圈3020隔离开关在拉开位置/r/n";
					replaceStr += "确认35kV消弧线圈冷备用/r/n";
				}else{
					boolean isallxhxqdk = true;

					for(PowerDevice xhxq : xhxqList){
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(xhxq).equals("0")){
							isallxhxqdk = false;
						}
					}

					if(!isallxhxqdk){
						if(curDev.getPowerDeviceName().contains("#1")){
							replaceStr += "确认35kV消弧线圈运行于110kV#2主变35kV侧（3010隔离开关在拉开位置，3020隔离开关在合闸位置）/r/n";
							replaceStr += "将35kV消弧线圈由110kV#2主变35kV侧运行倒至110kV#1主变35kV侧运行/r/n";
							replaceStr += "确认35kV消弧线圈3020隔离开关在拉开位置/r/n";
							replaceStr += "确认35kV消弧线圈3010隔离开关在合闸位置/r/n";
						}else if(curDev.getPowerDeviceName().contains("#2")){
							replaceStr += "确认35kV消弧线圈运行于110kV#1主变35kV侧（3010隔离开关在合闸位置，3020隔离开关在拉开位置）/r/n";
							replaceStr += "将35kV消弧线圈由110kV#1主变35kV侧运行倒至110kV#2主变35kV侧运行/r/n";
							replaceStr += "确认35kV消弧线圈3010隔离开关在拉开位置/r/n";
							replaceStr += "确认35kV消弧线圈3020隔离开关在合闸位置/r/n";
						}
					}
				}
			}
		}

		return replaceStr;
	}

	public static String getBztStrReplace(List<PowerDevice> mlkgList) {
		String replaceStr="";

		if(mlkgList.size() > 1){
			for(PowerDevice mlkg:mlkgList){

				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.PowerTransformer,
						SystemConstants.PowerTransformer, "",
						CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSideMother,
						false, true, false, true);
				RuleExeUtil.swapDeviceList(zbList);

				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine,
						SystemConstants.PowerTransformer,true, false, true);

				String mxNum = "";

				RuleExeUtil.swapDeviceList(mxList);

				for(PowerDevice mx : mxList){
					mxNum += CZPService.getService().getDevNum(mx)+"/";
				}

				if(mxNum.endsWith("/")){
					mxNum = mxNum.substring(0, mxNum.length()-1);
				}

				replaceStr += "10kV"+mxNum+"、";
			}

			if(replaceStr.endsWith("、")){
				replaceStr = replaceStr.substring(0, replaceStr.length()-1);
			}
		}else if(mlkgList.size() == 1){
			replaceStr = (int)mlkgList.get(0).getPowerVoltGrade()+"kV";
		}

		return replaceStr;
	}

	public static String get3KnifeContent(List<PowerDevice> kgList,String stationName,String operation){
		String replaceStr = "";

		for(PowerDevice dev : kgList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			List<PowerDevice> alldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate,
					SystemConstants.MotherLine, true, true, false);

			for(PowerDevice dz : alldzList){
				if(!dzList.contains(dz)&&!dz.getPowerDeviceName().endsWith("1")){
					if(operation.equals("拉开")){
						List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
						dzTempList.add(dz);
						replaceStr = getKnifeOffContent(dzTempList,stationName);
					}else{
						List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
						dzTempList.add(dz);
						replaceStr = getKnifeOnContent(dzTempList,stationName);
					}
					break;
				}
			}
		}

		return replaceStr;
	}

	public static String getKnifeOffContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";

		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "红河地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}else{
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String getKnifeOnContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";

		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";

		for(Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
			PowerDevice dev = it.next();

			if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
				if(dev.getPowerDeviceName().endsWith("1")){
					it.remove();
				}
			}
		}

		boolean ismldz = false;

		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,
					"", true, true, true, true);

			if(!mlkgList.isEmpty()){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}

		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}

		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(swList)+"已处冷备用/r/n";
				}else{
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){//纯确认的方法
		String replaceStr = "";

		for(Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
			PowerDevice dev = it.next();

			if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
				if(dev.getPowerDeviceName().endsWith("1")){
					it.remove();
				}
			}
		}

		boolean ismldz = false;

		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,
					"", true, true, true, true);

			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}

		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}

		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);

					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(swList)+"已处热备用/r/n";
				}else{
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处合上位置/r/n";
				}
			}
		}

		return replaceStr;
	}


	public static String getZxdJddzOnCheckContent(List<PowerDevice> zxdjddzList,String stationName,PowerDevice station){
		String replaceStr = "";

		boolean isZxdOn = false;

		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();

		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOn = true;
			}
		}

		if(isZxdOn){
			tagzxdjddzList = RuleExeUtil.sortByVoltHigh(tagzxdjddzList);

			for(PowerDevice dev : tagzxdjddzList) {
				replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处合上位置/r/n";
			}
		}

		return replaceStr;
	}

	public static String getZxdJddzOffCheckContent(List<PowerDevice> zxdjddzList,String stationName,PowerDevice station){
		String replaceStr = "";

		boolean isZxdOff = false;

		List<PowerDevice> tagzxdjddzList = new ArrayList<PowerDevice>();

		for(PowerDevice dev : zxdjddzList) {
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
				tagzxdjddzList.add(dev);
				isZxdOff = true;
			}
		}

		if(isZxdOff){
			tagzxdjddzList = RuleExeUtil.sortByVoltLow(tagzxdjddzList);

			for(PowerDevice dev : tagzxdjddzList) {
				replaceStr += "红河地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"处拉开位置/r/n";
			}
		}

		return replaceStr;
	}
}
