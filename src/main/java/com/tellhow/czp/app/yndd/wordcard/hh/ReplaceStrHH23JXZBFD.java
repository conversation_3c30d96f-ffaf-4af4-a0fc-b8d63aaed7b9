package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHH23JXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("红河二分之三接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);

			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					Collections.reverse(zbgyckgList);
				}
				break;
			}
			
			kgList.addAll(zbgyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbdyckgList);
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(jddzList);
				
				otherzxdjddzList.addAll(jddzList);
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += "红河地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionHH.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					replaceStr += CommonFunctionHH.getHhContent(dev, "红河地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
					replaceStr += CommonFunctionHH.getHhContent(dev, "红河地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionHH.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
			}
		}
		
		return replaceStr;
	}

}
