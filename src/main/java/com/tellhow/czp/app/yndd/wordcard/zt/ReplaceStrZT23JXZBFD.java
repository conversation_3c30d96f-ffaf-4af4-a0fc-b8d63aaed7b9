package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.Collections;
import java.util.List;

import com.sun.java.help.search.Rule;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZT23JXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通二分之三接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			RuleExeUtil.swapRunmodelThreeTwoDeviceList(zbgyckgList);
			Collections.reverse(zbgyckgList);
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
			}
			
			int count = 0;
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					if(count == 0){
						replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对主变充电/r/n";
					}else{
						replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
					}
					count++;
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "昭通地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对母线充电/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
