package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr35T35kVDMMXCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("35T35kV单母母线操作".equals(tempStr)){
			String beginstatus = CBSystemConstants.getCurRBM().getBeginStatus();
			String endstatus = CBSystemConstants.getCurRBM().getEndState();
			CommonFunctionHH cf = new CommonFunctionHH();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());

			String stationName = CZPService.getService().getDevName(station);
			
			if(Integer.valueOf(beginstatus)>Integer.valueOf(endstatus)){//复电
				List<PowerDevice> coldxlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> coldzbList = new ArrayList<PowerDevice>();
				List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> yxxlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> hotxlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(curDev.getPowerVoltGrade() == dev.getPowerVoltGrade()){
						if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
								coldzbList.add(dev);
							}
						}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								xlkgList.add(dev);
								
								if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
									coldxlkgList.add(dev);
								}
								
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
									hotxlkgList.add(dev);
								}if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									yxxlkgList.add(dev);
								}
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkgList.add(dev);
							}
						}
					}
				}
				
				String zbName = "";
				
				if(coldzbList.size()>0){
					for(PowerDevice coldzb : coldzbList){
						zbName += CZPService.getService().getDevName(coldzb)+"、";
					}
					
					if(zbName.endsWith("、")){
						zbName = zbName.substring(0, zbName.length()-1);
					}
				}
				
				if(gycmlkgList.size()==0){//单母不分段
					List<PowerDevice> allkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					
					if(allkgList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(allkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备冷备用/r/n";
					}
					
					List<PowerDevice> lbykgList = new ArrayList<PowerDevice>();
					
					for(PowerDevice kg : allkgList){
						if(RuleExeUtil.getDeviceBeginStatus(kg).equals("2")){
							lbykgList.add(kg);
						}
					}
					
					replaceStr += "将"+CZPService.getService().getDevName(lbykgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由冷备用转热备用/r/n";
					
					List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
					
					for(PowerDevice kg : allkgList){
						if(RuleExeUtil.getDeviceEndStatus(kg).equals("0")){
							yxkgList.add(kg);
						}
					}
					
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(yxkgList)+"/r/n";

					replaceStr += "核实"+CZPService.getService().getDevName(yxkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备运行正常/r/n";
					replaceStr += "投入35kV备自投装置/r/n";
				}else{
					List<PowerDevice> allkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					List<PowerDevice> coldDevList = new ArrayList<PowerDevice>();
					List<PowerDevice> zbdyckgList =  new ArrayList<PowerDevice>();
					List<PowerDevice> zbgyckgList =  new ArrayList<PowerDevice>();
					List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					
					if(zbList.size()>0){
						zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zbList.get(0));
						zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(zbList.get(0));
					}
					
					if(zbdyckgList.size()>0){
						dycmlkgList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
					
					for(PowerDevice kg : allkgList){
						if(!kg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(kg).equals("2")){
								coldDevList.add(kg);
							}
						}
					}
					
					List<PowerDevice> bzzbkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> fbzzbkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> zbkgList =  new ArrayList<PowerDevice>();
			    	
					zbkgList.addAll(zbgyckgList);
					zbkgList.addAll(zbdyckgList);

					for(PowerDevice zbkg : zbkgList){
			    		
			    		if(zbkg.getDeviceType().equals(SystemConstants.PowerTransformer)){
			    			continue;
			    		}
			    		
			    		List<PowerDevice> dzList =  RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
			    		
			    		if(dzList.size()==1){
			    			fbzzbkgList.add(zbkg);
			    		}else{
			    			bzzbkgList.add(zbkg);
			    		}
			    	}
					
					bzzbkgList.addAll(0,zbList);

					List<PowerDevice>  rbytempList = new ArrayList<PowerDevice>();
					List<PowerDevice>  lbytempList = new ArrayList<PowerDevice>();

					for(PowerDevice bzzbkg : bzzbkgList){
						if(RuleExeUtil.getDeviceBeginStatus(bzzbkg).equals("1")){
							rbytempList.add(bzzbkg);
						}else if(RuleExeUtil.getDeviceBeginStatus(bzzbkg).equals("2")){
							lbytempList.add(bzzbkg);
						}
					}
					
					if(rbytempList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(rbytempList)+"热备用/r/n";
					}
					
					if(lbytempList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(lbytempList)+"冷备用/r/n";
					}
						
					if(fbzzbkgList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(fbzzbkgList)+"在分闸位置/r/n";
					}
					
					for(PowerDevice fbzzbkg : fbzzbkgList){
						
		    			List<PowerDevice> dzList =  RuleExeUtil.getDeviceDirectList(fbzzbkg, SystemConstants.SwitchSeparate);
		    			
		    			for(PowerDevice dz : dzList){
		    				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dz).equals("0")){
				    			replaceStr += "核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
		    				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dz).equals("1")){
				    			replaceStr += "核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
		    				}
		    			}
		    		}
					
					if(coldDevList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(coldDevList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备冷备用/r/n";
					}
					
					replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"35kV侧后备保护动作跳"+CZPService.getService().getDevName(gycmlkgList)+"/r/n";
					replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"10kV侧后备保护动作跳"+CZPService.getService().getDevName(dycmlkgList)+"/r/n";
					
					if(coldDevList.size()>0){
						replaceStr += "将"+CZPService.getService().getDevName(coldDevList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由冷备用转热备用/r/n";
					}
					
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkgList)+"/r/n";
					replaceStr += "核实"+CZPService.getService().getDevName(gycmlkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备运行正常/r/n";
					
					if(yxxlkgList.size()==0){
						replaceStr += "投入35kV备自投装置/r/n";
					}
					
					replaceStr += cf.getZbBLTQStrReplace(zbList.get(0));
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(zbgyckgList);
					tempList.addAll(zbdyckgList);		
					
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(tempList)+"/r/n";
					
					tempList.addAll(0,zbList);
					
					replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"运行正常/r/n";
					
					if(dycmlkgList.size()>0){
						for(PowerDevice dycmlkg : dycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("1")){
								replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
								replaceStr += "核实"+CZPService.getService().getDevName(dycmlkg)+"热备用/r/n";
								replaceStr += "投入10kV备自投装置/r/n";
							}
						}
					}
					
					if(yxxlkgList.size()>0){
						replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(yxxlkgList)+"/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(yxxlkgList)+"运行正常/r/n";
						
						replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(gycmlkgList)+"/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(gycmlkgList)+"热备用/r/n";
						
						replaceStr += "投入35kV备自投装置/r/n";
					}
				}
			}else{
				List<PowerDevice> yxxlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(curDev.getPowerVoltGrade() == dev.getPowerVoltGrade()){
						if(dev.getDeviceType().equals(SystemConstants.Switch)){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
									yxxlkgList.add(dev);
								}
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkgList.add(dev);
							}
						}
					}
				}
				
				
				if(gycmlkgList.size()==0){//单母不分段
					List<PowerDevice> allkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					List<PowerDevice> lbykgList = new ArrayList<PowerDevice>();
					List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();		
					List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();		
					List<PowerDevice> zybdzList = new ArrayList<PowerDevice>();
					List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
					List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					if(allkgList.size()>0){
						List<PowerDevice> zbsList =  new ArrayList<PowerDevice>();

						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
						
							if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
								zbsList.add(dev);
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
								rbykgList.add(dev);
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
								rbykgList.add(dev);
							}
							
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
								if(dev.getPowerDeviceName().contains("站用变")&&!dev.getPowerDeviceName().contains("接地站用变")){
									zybdzList.add(dev);
								}
							}
							
							
							if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&dev.getPowerVoltGrade() == 10&&!dev.getPowerDeviceName().contains("400")){
								dycmxList.add(dev);
							}
						}
					
						for(PowerDevice kg : allkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(kg).equals("1")){
								rbykgList.add(kg);
							}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(kg).equals("2")){
								lbykgList.add(kg);
							}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(kg).equals("0")){
								yxkgList.add(kg);
							}
						}
						
						if(rbykgList.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(rbykgList)+"热备用/r/n";
						}
						
						List<PowerDevice> gycbbzdzList = new ArrayList<PowerDevice>();
						List<PowerDevice> dycbbzdzList = new ArrayList<PowerDevice>();

						List<PowerDevice> zbgyckgList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbdyckgList = new ArrayList<PowerDevice>();
						
						for(PowerDevice zb : zbsList){
							zbgyckgList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
							zbdyckgList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
						}
						
						for(PowerDevice zbgyckg : zbgyckgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(zbgyckg).equals("1")){
								List<PowerDevice> zbgycdzList = RuleExeUtil.getDeviceDirectList(zbgyckg, SystemConstants.SwitchSeparate);
								
								if(zbgycdzList.size()==1){
									gycbbzdzList.addAll(zbgycdzList);
								}
							}
						}
						
						for(PowerDevice dev : zbdyckgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
								List<PowerDevice> zbgycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								
								if(zbgycdzList.size()==1){
									dycbbzdzList.addAll(zbgycdzList);
								}
							}
						}
						
						
						for(PowerDevice dycbbzdz : dycbbzdzList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycbbzdz).equals("1")){
								replaceStr += "核实"+CZPService.getService().getDevName(dycbbzdz)+"在拉开位置/r/n";
							}else{
								replaceStr += "核实"+CZPService.getService().getDevName(dycbbzdz)+"在合闸位置/r/n";
							}
						}
						
						for(PowerDevice gycbbzdz : gycbbzdzList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gycbbzdz).equals("1")){
								replaceStr += "核实"+CZPService.getService().getDevName(gycbbzdz)+"在拉开位置/r/n";
							}else{
								replaceStr += "核实"+CZPService.getService().getDevName(gycbbzdz)+"在合闸位置/r/n";
							}
						}
						
						if(zybdzList.size()>0){
							if(zybdzList.get(0).getPowerVoltGrade()>10){
								replaceStr += "核实"+CZPService.getService().getDevName(zybdzList)+"在拉开位置/r/n";
								replaceStr += "核实"+(int)zybdzList.get(0).getPowerVoltGrade()+"kV#X站用变冷备用/r/n";
							}
						}
						
						replaceStr += "核实10kV#X站用变冷备用/r/n";
						replaceStr += "核实10kV#X站用变高压侧跌落保险在拉开位置/r/n";
						
						if(lbykgList.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(lbykgList)+"冷备用/r/n";
						}
						
						RuleExeUtil.swapDeviceList(dycmxList);
						
						if(dycmxList.size()>0){
						    replaceStr += "红河配调@核实"+stationName+CZPService.getService().getDevName(dycmxList)+"上其所管辖的所有10kV出线断路器均己转冷备用/r/n";
						}
						
						List<PowerDevice> yxmlkgList = new ArrayList<PowerDevice>();

						if(gycmlkgList.size()>0){
							for(PowerDevice mlkg : gycmlkgList){
								if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(mlkg).equals("0")){
									yxmlkgList.add(mlkg);
								}
							}
						}
						
						if(zbsList.size()==2){
							replaceStr += "退出10kV备自投装置/r/n";
							
							if(yxmlkgList.size()>0){
							    replaceStr += "退出35kV备自投装置/r/n";
							}
						}
						
						List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(zbdyckgList);
						tempList.addAll(zbgyckgList);
						
						for(Iterator<PowerDevice> itor = tempList.iterator();itor.hasNext();){
							PowerDevice dev = itor.next();
							
							if(!RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								itor.remove();
							}
						}
						
						replaceStr += cf.getYcDkStrReplace(tempList, stationName);
						
						gycbbzdzList.clear();
						
						for(PowerDevice zbgyckg : zbgyckgList){
							List<PowerDevice> zbgycdzList = RuleExeUtil.getDeviceDirectList(zbgyckg, SystemConstants.SwitchSeparate);
							
							if(zbgycdzList.size()==1){
								gycbbzdzList.addAll(zbgycdzList);
							}
						}
						
						dycbbzdzList.clear();
						
						for(PowerDevice dev : zbdyckgList){
							List<PowerDevice> zbgycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(zbgycdzList.size()==1){
								dycbbzdzList.addAll(zbgycdzList);
							}
						}
						
						if(dycbbzdzList.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(dycbbzdzList)+"在合闸位置/r/n";
						}
						
						if(gycbbzdzList.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(gycbbzdzList)+"在合闸位置/r/n";
						}
						
						List<PowerDevice>  bbzgyckgList = new ArrayList<PowerDevice>();
						List<PowerDevice>  bbzdyckgList = new ArrayList<PowerDevice>();

						List<PowerDevice>  bzgyckgList = new ArrayList<PowerDevice>();
						List<PowerDevice>  bzdyckgList = new ArrayList<PowerDevice>();
						
						for(PowerDevice zbdyckg : zbdyckgList){
							List<PowerDevice> zbdycdzList = RuleExeUtil.getDeviceDirectList(zbdyckg, SystemConstants.SwitchSeparate);
							
							if(zbdycdzList.size()==1){
								bbzdyckgList.add(zbdyckg);
							}else{
								bzdyckgList.add(zbdyckg);
							}
						}
						
						
						for(PowerDevice zbgyckg : zbgyckgList){
							List<PowerDevice> zbgycdzList = RuleExeUtil.getDeviceDirectList(zbgyckg, SystemConstants.SwitchSeparate);
							
							if(zbgycdzList.size()==1){
								bbzgyckgList.add(zbgyckg);
							}else{
								bzgyckgList.add(zbgyckg);
							}
						}

						if(bbzdyckgList.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(bbzdyckgList)+"在分闸位置/r/n";
						}
						
						if(bbzgyckgList.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(bbzgyckgList)+"在分闸位置/r/n";
						}
						
						String zbnr = "";
						
						RuleExeUtil.swapDeviceList(zbsList);
						
						for(PowerDevice zb : zbsList){
							tempList.clear();
							
							List<PowerDevice> tempzbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(zb);
							List<PowerDevice> tempzbdyckgList =  RuleExeUtil.getTransformerSwitchLow(zb);
							
							for(PowerDevice dev : tempzbdyckgList){
								List<PowerDevice> zbdycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								
								if(zbdycdzList.size()>1){
									tempList.add(dev);
								}
							}
							
							for(PowerDevice dev : tempzbgyckgList){
								List<PowerDevice> zbdycdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								
								if(zbdycdzList.size()>1){
									tempList.add(dev);
								}
							}
							
							
							for(Iterator<PowerDevice> itor = tempList.iterator();itor.hasNext();){
								PowerDevice dev = itor.next();
								
								if(!RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
									itor.remove();
								}
							}
							
							tempList.add(0,zb);
							
							zbnr += CZPService.getService().getDevName(tempList)+"、";
						}
						
						
						RuleExeUtil.swapDeviceList(dycmxList);

						String mxnr = CZPService.getService().getDevName(dycmxList)+"及母线设备、";
						mxnr += CZPService.getService().getDevName(curDev)+"及母线设备";
						
						replaceStr += "核实"+zbnr+mxnr+"热备用/r/n";
						
						xlkgList.addAll(0,bzgyckgList);
						xlkgList.addAll(0,bzdyckgList);

						if(xlkgList.size()>0){
							replaceStr += "将"+CZPService.getService().getDevName(xlkgList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由热备用转冷备用/r/n";
						}else{
							replaceStr += "将"+CZPService.getService().getDevName(curDev)+"及母线设备由热备用转冷备用/r/n";
						}
						
						if(zbdyckgList.get(0).getDeviceStatus().equals("2")){
							if(dycbbzdzList.size()>0){
								replaceStr +=  "拉开"+CZPService.getService().getDevName(dycbbzdzList)+"/r/n";
							}
						}
						
						if(gycbbzdzList.size()>0){
							replaceStr +=  "拉开"+CZPService.getService().getDevName(gycbbzdzList)+"/r/n";
						}
						
						if(dycbbzdzList.size()>0){
							replaceStr += "核实"+CZPService.getService().getDevName(zbsList)+"冷备用/r/n";
						}
					}
				}else{
					if(yxxlkgList.size()>0){
						for(PowerDevice yxxlkg : yxxlkgList){
							List<PowerDevice> yxxlList = RuleExeUtil.getDeviceList(yxxlkg, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
							
							List<PowerDevice> otheryxxlList = RuleExeUtil.getLineOtherSideList(yxxlList.get(0));
							
							if(otheryxxlList.size()>0){
								for(PowerDevice otheryxxl : otheryxxlList){
									List<PowerDevice> otheryxxlkgList = RuleExeUtil.getDeviceList(otheryxxl, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true);
									
									for(PowerDevice otheryxxlkg : otheryxxlkgList){
										if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(otheryxxlkg).equals("1")){
											
											PowerDevice sttemp = CBSystemConstants.getPowerStation(otheryxxlkg.getPowerStationID());
											
											replaceStr +=CZPService.getService().getDevName(sttemp)+"@核实"+CZPService.getService().getDevName(otheryxxlkg)+"热备用/r/n";
											
											List<PowerDevice> mxList = RuleExeUtil.getDeviceList(otheryxxlkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
											
											if(mxList.size()>0){
												if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(mxList.get(0)).equals("0")){
													replaceStr +=CZPService.getService().getDevName(sttemp)+"@退出"+(int)mxList.get(0).getPowerVoltGrade()+"kV备自投装置/r/n";
												}
											}
										}
									}
								}
							}
						}
					}
					
					List<PowerDevice> hotDevList = new ArrayList<PowerDevice>();
					List<PowerDevice> coldDevList = new ArrayList<PowerDevice>();
					
					List<PowerDevice> allkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, false, false);
					List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
					
					List<PowerDevice> zbdyckgList =  new ArrayList<PowerDevice>();
					List<PowerDevice> zbgyckgList =  new ArrayList<PowerDevice>();
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					
					if(zbList.size()>0){
						zbdyckgList = RuleExeUtil.getTransformerSwitchLow(zbList.get(0));
						zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(zbList.get(0));
					}
					
					if(zbdyckgList.size()>0){
						dycmlkgList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
					
					for(PowerDevice kg : allkgList){
						if(RuleExeUtil.getDeviceBeginStatus(kg).equals("2")){
							coldDevList.add(kg);
						}else if(RuleExeUtil.getDeviceBeginStatus(kg).equals("1")){
							hotDevList.add(kg);
						}
					}
					
					List<PowerDevice> hotallDevList = new ArrayList<PowerDevice>();
					
					hotallDevList.addAll(hotDevList);
					hotallDevList.addAll(dycmlkgList);
					
					if(hotallDevList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(hotallDevList)+"热备用/r/n";
					}
					
					if(coldDevList.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(coldDevList)+"冷备用/r/n";
					}
					
					ReplaceStrHHTC10kVBZTZZ bzt = new ReplaceStrHHTC10kVBZTZZ();
					replaceStr += "退出35kV备自投装置/r/n";
					replaceStr += bzt.strReplace("红河退出10kV备自投装置", curDev, stationDev, desc);
					
					replaceStr += cf.getZbBLTQStrReplace(zbList.get(0));
					
					if(dycmlkgList.size()>0){
						for(PowerDevice dycmlkg : dycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("0")){
								replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
								replaceStr += "核实"+CZPService.getService().getDevName(dycmlkg)+"运行正常/r/n";
							}
						}
					}
					
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

					if(mxList.size()>0){
						List<PowerDevice> otherxlkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

						if(otherxlkgList.size()>0){
							for(PowerDevice otherxlkg : otherxlkgList){
								if(RuleExeUtil.getDeviceEndStatus(otherxlkg).equals("0")){
									replaceStr += "红河地调@遥控用"+stationName+CZPService.getService().getDevName(otherxlkg)+"同期合环/r/n";
									replaceStr += "核实"+CZPService.getService().getDevName(otherxlkg)+"运行正常/r/n";
								}
							}
						}
						
						List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						
						for(PowerDevice xlkg : xlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
								replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkg)+"/r/n";
								replaceStr += "核实"+CZPService.getService().getDevName(xlkg)+"热备用/r/n";
							}
						}
					}
					
					if(zbList.size()>0){
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(zbdyckgList);
						tempList.addAll(zbgyckgList);
						tempList.addAll(gycmlkgList);
						
						hotDevList.addAll(gycmlkgList);
						
						replaceStr += "将"+CZPService.getService().getDevName(hotDevList)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备由热备用转冷备用/r/n";	

						List<PowerDevice> fbzzbkgList = new ArrayList<PowerDevice>();

				    	for(PowerDevice zbkg : zbgyckgList){
				    		
				    		List<PowerDevice> dzList =  RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
				    		
				    		if(dzList.size()==1){
				    			fbzzbkgList.add(zbkg);
				    		}
				    	}
						
				    	for(PowerDevice fbzzbkg : fbzzbkgList){
			    			List<PowerDevice> dzList =  RuleExeUtil.getDeviceDirectList(fbzzbkg, SystemConstants.SwitchSeparate);
			    			replaceStr += "拉开"+CZPService.getService().getDevName(dzList)+"/r/n";
			    		}
				    	
				    	if(cf.getZbIsJdzybStrReplace(zbList.get(0))){
							replaceStr += "退出10kV#X接地站用变小电阻自投切功能/r/n";
						}
						
						replaceStr += cf.getDqZbHbBhTMlkgStrReplace(zbList.get(0), "low", "退出","停电");
					}
				} 
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
