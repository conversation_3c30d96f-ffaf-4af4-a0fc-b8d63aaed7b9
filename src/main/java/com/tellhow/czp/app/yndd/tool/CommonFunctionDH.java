package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dh.DHLineWorkSelectionDialog;
import com.tellhow.czp.app.yndd.rule.dh.DHStationWorkSelectionDialog;
import com.tellhow.czp.app.yndd.view.StringCheckChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

public class CommonFunctionDH {
	public static String orderContent = "";

	public static String getSwitchOffContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(station.getDeviceType().equals(SystemConstants.PowerFactory)){
				replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "德宏地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getSwitchOnContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";
		
		if(ifSwitchControl(dev)){
			if(station.getDeviceType().equals(SystemConstants.PowerFactory)){
				replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "德宏地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
		}
		
		return replaceStr;
	}
	
	public static List<PowerDevice> getTransformerKnife(PowerDevice zb,PowerDevice zbkg){
		List<PowerDevice> dztagList = new ArrayList<PowerDevice>();

		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
		List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(zb, zbkg, SystemConstants.PowerTransformer, "", true, true);
		
		for(PowerDevice path : pathList){
			if(path.getDeviceType().equals(SystemConstants.SwitchSeparate)){
				if(!dzList.contains(path)){
					dztagList.add(path);
				}
			}
		}
		
		return dztagList;
	}
	
	public static String getKnifeOffContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "德宏地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOffCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				if(ifSwitchSeparateControl(dev)){
					replaceStr += "德宏地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}
		
		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处合上位置/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";
		
		boolean ismldz = false;
		
		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);
			
			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}
		
		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}
		
		for(PowerDevice dz : dzList){
//			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
//			}
		}
		
		return replaceStr;
	}
	
	public static List<Map<String, String>> getStationLineList(PowerDevice curDev){
		List<Map<String, String>> stationLineList = new ArrayList<Map<String,String>>();
		
		String sql = "SELECT ID,LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,PTDISCONNECTOR_NAME "
				+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND "
						+ "WHERE ID = '"+curDev.getPowerDeviceID()+"')";
		
		stationLineList = DBManager.queryForList(sql);
		
		if(stationLineList.size() == 0){
			String lineName = CZPService.getService().getDevName(curDev);
			
			sql = "SELECT ID,LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,PTDISCONNECTOR_NAME "
					+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_NAME = '"+lineName+"'";
			
			stationLineList = DBManager.queryForList(sql);
		}
		
		return stationLineList; 
	}
	
	public static String getHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(dev.getPowerVoltGrade() > 35){
				if(stationName.contains("电站")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
				}
			}else{
				if(stationName.contains("电站")){
					replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"合环/r/n";
				}else{
					replaceStr += ddname+"@遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环/r/n";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getHhContentNew(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";
		
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(dev.getPowerVoltGrade() > 35){
				if(stationName.contains("电站")){
					replaceStr += "用"+CZPService.getService().getDevName(dev)+"同期合环";
				}else{
					replaceStr += "遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环";
				}
			}else{
				if(stationName.contains("电站")){
					replaceStr += "用"+CZPService.getService().getDevName(dev)+"合环";
				}else{
					replaceStr += "遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环";
				}
			}
		}
		
		return replaceStr;
	}
	
	public static boolean ifSwitchControl(PowerDevice dev){//开关可控
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_BREAKER WHERE ID = '"+dev.getPowerDeviceID()+"'";
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
				
				if(ifcontrol.equals("0")){
					ifcontrol = "false";
				}else{
					ifcontrol = "true";
				}
				
				return Boolean.parseBoolean(ifcontrol);
			}
		}
		
		return false;
	}
	
	public static boolean ifSwitchSeparateControl(PowerDevice dev){//刀闸是否可控
		boolean result = true;
		
		if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_DISCONNECTOR WHERE ID = '"+dev.getPowerDeviceID()+"'";
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
				
				if(ifcontrol.equals("0")){
					ifcontrol = "false";
				}else{
					ifcontrol = "true";
				}
				
				if(!Boolean.parseBoolean(ifcontrol)){
					result = false;
				}
			}
			
			if(ifcontrolList.size() == 0){
				return false;
			}
			
			return result;
		}else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
			String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_GROUNDDISCONNECTOR WHERE ID = '"+dev.getPowerDeviceID()+"'";
			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
			
			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
				
				if(ifcontrol.equals("0")){
					ifcontrol = "false";
				}else{
					ifcontrol = "true";
				}
				
				if(!Boolean.parseBoolean(ifcontrol)){
					result = false;
				}
			}
			
			if(ifcontrolList.size() == 0){
				return false;
			}
			
			return result;
		}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			
			for(PowerDevice dz : dzList){
				String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_DISCONNECTOR WHERE ID = '"+dz.getPowerDeviceID()+"'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
					
					if(ifcontrol.equals("0")){
						ifcontrol = "false";
					}else{
						ifcontrol = "true";
					}
					
					if(!Boolean.parseBoolean(ifcontrol)){
						result = false;
					}
				}
				
				if(ifcontrolList.size() == 0){
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						continue;
					}else{
						return false;
					}
				}
			}
			
			if(dzList.size() == 0){
				return false;
			}
			
			return result;
		}
		
		return false;
	}
	
	public static String getMotherLineTdContent(List<PowerDevice> mxList,String stationName){
		String replaceStr = "";
		
		boolean ifMotherLineTd = true;
		
		for(PowerDevice dev : mxList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, SystemConstants.PowerTransformer, false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC, SystemConstants.PowerTransformer, false, true, true, true);
			
			if(mlkgList.size() == 0){
				for(PowerDevice zbkg : zbkgList){
					if(zbkg.getDeviceStatus().equals("0")){
						ifMotherLineTd = false;
					}
				}
			}else{
				for(PowerDevice mlkg : mlkgList){
					if(mlkg.getDeviceStatus().equals("0")){
						ifMotherLineTd = false;
					}
				}
				
				for(PowerDevice zbkg : zbkgList){
					if(zbkg.getDeviceStatus().equals("0")){
						ifMotherLineTd = false;
					}
				}
			}
		}
		
		if(ifMotherLineTd){
			String mxName = "";
			
			boolean isHaveXlkg = false;
			
			for(PowerDevice dev : mxList){
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, SystemConstants.PowerTransformer, false, true, true, true);
				
				if(xlkgList.size() > 0){
					isHaveXlkg = true;
				}
				mxName = CZPService.getService().getDevName(dev);
			}
			
			if(!mxName.equals("")&&isHaveXlkg){
				replaceStr += "德宏配调@确认"+stationName+mxName+"配调管辖10kV出线运行方式已调整完毕，具备停电条件/r/n";
			}
			
			for(PowerDevice dev : mxList){
				List<PowerDevice> drqList = RuleExeUtil.getDeviceList(dev, SystemConstants.ElecCapacity, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice drqkg : drqList){
					replaceStr += "德宏地调@确认"+stationName+CZPService.getService().getDevName(drqkg)+"处热备用/r/n";
				}
				
				List<PowerDevice> dkqList = RuleExeUtil.getDeviceList(dev, SystemConstants.ElecShock, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice drqkg : dkqList){
					replaceStr += "德宏地调@确认"+stationName+CZPService.getService().getDevName(drqkg)+"处热备用/r/n";
				}
				
				/*List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSwitchZYB, SystemConstants.PowerTransformer, false, true, true, true);
				List<PowerDevice> zybdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT, SystemConstants.PowerTransformer, false, true, true, true);
				List<PowerDevice> zybList = new ArrayList<PowerDevice>();
				
				zybList.addAll(zybkgList);
				zybList.addAll(zybdzList);
				
				for(PowerDevice zyb : zybList){
					if(zyb.getPowerDeviceName().contains("站用变")){
						String devName = CZPService.getService().getDevName(zyb);
						
						if(devName.contains("站用变")){
							String zybName = devName.substring(0, devName.indexOf("站用变")+3);
							replaceStr += stationName+"@确认"+zybName+"具备停电条件/r/n";
						}
						break;
					}
				}*/
			}	
		}
		
		return replaceStr;
	}
	
	public static boolean isMotherLineFd(List<PowerDevice> mxList){
		boolean ifMotherLineFd = false;
		
		for(PowerDevice dev : mxList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, SystemConstants.PowerTransformer, false, true, true, true);
			
			if(mlkgList.size() == 0){
				ifMotherLineFd = true;
			}else{
				for(PowerDevice mlkg : mlkgList){
					if(!RuleExeUtil.getDeviceBeginStatus(mlkg).equals("0")){
						ifMotherLineFd = true;
					}
				}
			}
		}
		
		return ifMotherLineFd;
	}
	
	public static String getSequenceConfirmTdContent(List<PowerDevice> swList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
					Collections.reverse(dzList);
				}
			}
			
			for(PowerDevice dz : dzList){
				if(!dz.getPowerDeviceName().contains("PT")){
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(sw)+"处冷备用/r/n";
					}else{
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getSequenceConfirmFdContent(List<PowerDevice> swList,String stationName){
		String replaceStr = "";
		
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
					Collections.reverse(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
				}
			}
			
			for(PowerDevice dz : dzList){
				if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")&&!dz.getPowerDeviceName().contains("PT")){
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(sw)+"处热备用/r/n";
					}else{
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处合上位置/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}
	
	public static String getSequenceConfirmTdContent(PowerDevice dz,String stationName){
		String replaceStr = "";
		
		if(!dz.getPowerDeviceName().contains("PT")){
			replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getSequenceConfirmFdContent(PowerDevice dz,String stationName){
		String replaceStr = "";
		
		if(!dz.getPowerDeviceName().contains("PT")){
			replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处合上位置/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getBztResult(List<String> voltList,String kind){
		String result = "";
		
		List<Map<String, String>> chooseEquips = new ArrayList<Map<String, String>>();
		StringCheckChoose ecc = new StringCheckChoose(SystemConstants.getMainFrame(), true, voltList, "请选择需要"+kind+"的备自投：");
		chooseEquips = ecc.getChooseEquip();
		
		if(chooseEquips.size()>0){
			for(Map<String, String> chooseEquip : chooseEquips){
				String bzt = chooseEquip.get("备自投");
				
				result += kind+bzt+"/r/n";
			}
		}
		
		return result;
	}

	public static PowerDevice getDeviceInfoList(String station, String word) {
		PowerDevice result = new PowerDevice();
		

		String kuohao = "";

		word = word.trim();
		station = station.trim();
		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();

		try{
			word = word.replaceAll(" ","");
			word = word.replace("(", "（").replace(")", "）");
			
			if(word.contains("（")&&word.contains("）")){
				if(word.indexOf("（")<word.indexOf("）")){
					kuohao = word.substring(word.indexOf("（"),word.indexOf("）")+1);
				}
			}
			
			if(word.contains("《")&&word.contains("》")){
				if(word.indexOf("《")<word.indexOf("》")){
					kuohao = word.substring(word.indexOf("《"),word.indexOf("》")+1);
				}
			}
			
			word = word.replace(kuohao, "");
			
			/*
			 * 解析指令的设备、状态
			 */
			String stationId = "";
			
			/*
			 * 得到厂站的实体
			 */
			
			//厂站名称校验
			for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();){
				PowerDevice st = it.next();
				
				String modelDevName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(st));
				station = StringUtils.killVoltInDevName(station);
				
				if(modelDevName.equals(station)) {
					stationId = st.getPowerDeviceID();
					
					if (CBSystemConstants.getStationPowerDevices(stationId) == null) {
						CreatePowerStationToplogy.loadFacEquip(stationId);
					}
					
					HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationId);
					
					if(devMap != null){
						break;
					}else{
						continue;
					}
				}
			}
		
			System.out.println("厂站ID："+stationId);
			
			/*
			 * 加载厂站缓存
			 */
			if (CBSystemConstants.getStationPowerDevices(stationId) == null) {
				CreatePowerStationToplogy.loadFacEquip(stationId);
			}
			
			HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationId);
			
			if(devMap != null){
				String deviceName = word;
				
				String equipTypeFlag = "";
				String equipTypeName = "";
				String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
						SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchSeparate,
						SystemConstants.SwitchSeparate,SystemConstants.Switch, SystemConstants.SwitchSeparate,
						SystemConstants.SwitchSeparate, SystemConstants.Switch,
						SystemConstants.Switch,SystemConstants.VolsbTransformer, SystemConstants.MotherLine,SystemConstants.MotherLine,
						SystemConstants.MotherLine, SystemConstants.InOutLine,SystemConstants.PowerTransformer,
						SystemConstants.PowerTransformer, SystemConstants.ElecShock,
						SystemConstants.ElecCapacity ,SystemConstants.PowerTransformer,SystemConstants.PowerTransformer,SystemConstants.PowerGenerator};
				String[] key = new String[] { "接地刀闸","接地开关", "地刀", "隔离开关", "隔离刀闸","小车开关", "小车", "刀闸", "断路器",
						"开关","PT", "母线", "母" , "M", "线", "T","主变", "电抗器", "电容器","#变","发变组","发电机"};
				for (int i = 0; i < key.length; i++) {
					if (deviceName.lastIndexOf(key[i]) >= 0) {
						equipTypeFlag = type[i];
						equipTypeName = key[i];
						break;
					}
				}
				
				String devNum = CZPService.getService().getDevNum(deviceName);
				
				String volStr = "";
				
				if(deviceName.toLowerCase().split("kv").length >= 3){
					volStr = deviceName.toLowerCase().substring(deviceName.toLowerCase().indexOf("kv")+2);
				}else{
					volStr = deviceName;
				}
				
				//获取电压等级
				String volt = StringUtils.getVoltInDevName(volStr);
				
				PowerDevice pd = new PowerDevice();
				
				for (PowerDevice dev : devMap.values()) {
					if (!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
						continue;
					if (!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
						continue;
					if(dev.getPowerDeviceName().contains("A相")||dev.getPowerDeviceName().contains("B相")||dev.getPowerDeviceName().contains("C相"))
						continue;
					if(dev.getPowerDeviceName().contains("压板"))
						continue;
					
					String name = CZPService.getService().getDevName(dev);
					
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
				    	name = StringUtils.killVoltInDevName(name).trim();
				    	deviceName = StringUtils.killVoltInDevName(deviceName).trim();

				    	name = name.replace("母线", "").replace("母", "").replace("M", "");
				    	deviceName = deviceName.replace("母线", "").replace("母", "").replace("M", "");
				    	
						if (name.equals(deviceName)) {
							if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
								pd = dev;
								break;
							}
							else
								pd = dev;
						}else if (deviceName.contains(name)) {
							pd = dev;
						}
					}else{
						String num = CZPService.getService().getDevNum(name);
						
						if(!devNum.equals("")){
							if (num.equals(devNum)) {
								if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
									pd = dev;
									break;
								}
								else
									pd = dev;
							}
						}else{
							if (name.equals(deviceName)) {
								if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
									pd = dev;
									break;
								}
								else
									pd = dev;
							}
						}
					}
				}
				
				if(!pd.getPowerDeviceID().equals("")){
					result = pd;
				}
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		
		return result;
	}

	public static String get3KnifeContent(List<PowerDevice> mlkgList,String stationName,String operation){
		String replaceStr = "";
		
		for(PowerDevice dev : mlkgList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			List<PowerDevice> alldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.MotherLine, true, true, false);
			
			for(PowerDevice dz : alldzList){
				if(!dzList.contains(dz)&&!dz.getPowerDeviceName().endsWith("1")){
					if(operation.equals("拉开")){
						List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
						dzTempList.add(dz);
						replaceStr = getKnifeOffContent(dzTempList,stationName);
					}else{
						List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
						dzTempList.add(dz);
						replaceStr = getKnifeOnContent(dzTempList,stationName);
					}
					break;
				}
			}
		}
		
		return replaceStr; 
	}

	public static boolean isContactLine(PowerDevice dev){
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			
			for(PowerDevice line : lineList){
				List<PowerDevice> lineAllSideList = RuleExeUtil.getLineAllSideList(line);
				
				if(lineAllSideList.size() > 1){
					for(PowerDevice lineSide : lineAllSideList){
						if(lineSide.getPowerStationName().contains("电厂")
								||lineSide.getPowerStationName().contains("光伏")){
							return false;
						}
					}
					
					return true;
				}
			}
		}
		
		return false;
	}

	public static String getSwitchConfirmContent(PowerDevice dev,String stationName) {
		String replaceStr = "";

		if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
			String status = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
			status = RuleExeUtil.getStatusNew(dev.getDeviceType(), status);
			replaceStr += "确认"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
		}else{
			replaceStr += "确认"+CZPService.getService().getDevName(dev)+"已处热备用/r/n";
		}
		
		return replaceStr;
	}
	
	public static String getPowerOnLineCheckContent(){
		String replaceStr = "";
		
		for(Map<String,String> mapMap : DHLineWorkSelectionDialog.deviceList){
			String result = mapMap.get("result");
			String stName = mapMap.get("stationName");
			String devName = mapMap.get("devName");

			if(result.equals("是")){
				replaceStr += stName+"@落实"+devName+"线路相关工作已全部结束，安全措施已全部拆除，人员已全部撤离，线路具备复电条件/r/n";
			}
		}
		
		return replaceStr;
	}
	
	public static String getPowerOnCheckContent(){
		String replaceStr = "";
		
		for(Map<String,String> mapMap : DHStationWorkSelectionDialog.deviceList){
			String result = mapMap.get("result");
			String stName = mapMap.get("stationName");
			String devName = mapMap.get("devName");

			if(result.equals("是")){
				replaceStr += stName+"@核实"+devName+"现场工作任务已结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作（装设）的接地开关（接地线）已全部拉开（拆除），"+devName+"的二次装置已正常投入，"+devName+"具备送电条件/r/n";
			}else{
				replaceStr += stName+"@核实"+devName+"停电期间，站内未开展相关工作，无作业人员，现场未布置临时措施，现场无自行操作（装设）的接地开关（接地线），"+devName+"的二次装置已正常投入，"+devName+"具备送电条件/r/n";
			}
		}
		
		return replaceStr;
	}
}
