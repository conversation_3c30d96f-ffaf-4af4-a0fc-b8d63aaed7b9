package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.model.DevParam;
import czprulepw.PWSystemConstants;

public class DeviceFind {
	public List<DispatchTransDevice> dtdLists=new ArrayList<DispatchTransDevice>(); //每行命令对应的设备对象集合
	//String [] devParam=new String[]{"","","","",""}; //数组值分别是：设备名称;源状态；目标状态；设备属性；电压等级
	public DevParam devParam = new DevParam();
	PowerDevice stationDevice=null;//所属变电站
	/**
	 * 
	 * @param param 设备属性参数
	 * @param pd 变电站
	 * @return
	 */
	public String execute(String param,PowerDevice stationDev) {
		// TODO Auto-generated method stub
		if(param==null||"".equals(param))
			return "";
		this.stationDevice=stationDev;
		
		String resultStr=""; //返回结果
		String[] params=null;
		String[] devParams=null;
		if(param.indexOf(",")>0){
			params=param.split(",");
		}else{
			params=new String[]{param};
		}
		String tempStr="";
		for (int i = 0; i < params.length; i++) {
			tempStr=params[i];
			if(tempStr.indexOf(">=")>0){
				 String[] paramMap=tempStr.split(">=");
				 for (int j = 0; j < paramMap.length; j++) {
					 if("V".equals(paramMap[0].trim().toUpperCase())){
							devParam.setDevMinVol(paramMap[1].trim().toUpperCase());
					 }
				 }
			}
			else if(tempStr.indexOf("=")>0){
				 String[] paramMap=tempStr.split("=");
				 for (int j = 0; j < paramMap.length; j++) {
					if("N".equals(paramMap[0].trim().toUpperCase())){
						devParam.setDevDesc(paramMap[1].trim().toUpperCase());
					}
					if("M".equals(paramMap[0].trim().toUpperCase())){
						String runModel = paramMap[1].trim().toUpperCase();
						if(runModel.equals("单母接线"))
							devParam.setDevRunModel(CBSystemConstants.RunModelOneMotherLine);
						else if(runModel.equals("双母接线"))
							devParam.setDevRunModel(CBSystemConstants.RunModelDoubleMotherLine);
						else if(runModel.equals("3/2接线"))
							devParam.setDevRunModel(CBSystemConstants.RunModelThreeTwo);
					}
					if("S".equals(paramMap[0].trim().toUpperCase())){
						devParam.setDevSrcStatus(paramMap[1].trim().toUpperCase());
					}
					if("T".equals(paramMap[0].trim().toUpperCase())){
						devParam.setDevTagStatus(paramMap[1].trim().toUpperCase());
					}
					if("B".equals(paramMap[0].trim().toUpperCase())){
						devParam.setDevBeginStatus(paramMap[1].trim().toUpperCase());
					}
					if("E".equals(paramMap[0].trim().toUpperCase())){
						devParam.setDevEndStatus(paramMap[1].trim().toUpperCase());
					}
					if("PM".equals(paramMap[0].trim().toUpperCase())){
						devParam.setDevProp(paramMap[1].trim().toUpperCase());
					}
					if("IC".equals(paramMap[0].trim().toUpperCase())){
						devParam.setDevProp(paramMap[1].trim().toUpperCase());
					}
					if("V".equals(paramMap[0].trim().toUpperCase())){
						devParam.setDevVol(paramMap[1].trim().toUpperCase());
					}
					if("RC".equals(paramMap[0].trim().toUpperCase())){
						devParam.setDevControl(paramMap[1].trim().toUpperCase());
					}
				 }
			}
		}
		if(!"".equals(devParam.getDevDesc())){
			//术语表达式中例如：EQ 后面的名称为线路开关|母联开关
			if(devParam.getDevDesc().indexOf("|")>0){
				devParams=devParam.getDevDesc().split("\\|");
			}else{
				devParams = new String[]{devParam.getDevDesc()};
			}
			String devType = "";
			for(int i=0;i<devParams.length;i++){
				devParam.setDevDesc(devParams[i].toString());
				if("操作设备".equals(devParam.getDevDesc())){
				    Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
					DispatchTransDevice dtd=null;
					PowerDevice dev=null;
					for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();) {
						 dtd=(DispatchTransDevice)iterator.next();
						 dev=dtd.getTransDevice();
						 if(dev.getPowerDeviceID().equals(stationDev.getPowerDeviceID())){
							 this.dtdLists.add(dtd);
							 break;
						 }
					}
			  }
				  else if("线路".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.InOutLine);
					  this.findDevByDevType(devParam);
				  }
				  else if("主变".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.PowerTransformer);
					  this.findDevByDevType(devParam);
	            	  }
				  else if("母线".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.MotherLine);
					  this.findDevByDevType(devParam);
					  }
				  else if("发电机".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.PowerGenerator);
					  this.findDevByDevType(devParam);
	            	  }
				  else if("电抗器".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.ElecShock);
					  this.findDevByDevType(devParam);
					  }
				  else if("电容器".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.ElecCapacity);
					  this.findDevByDevType(devParam);
	            	  }
				  else if("电抗器|电容器".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.ElecShock+","+SystemConstants.ElecCapacity);
					  this.findDevByDevType(devParam);
	            	  }
				 else if("电压互感器".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.VolsbTransformer);
					  this.findDevByDevType(devParam);
            	  }
				  else if("开关".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.Switch);
					  this.findDevByDevType(devParam);
					  }
				  else if("刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.SwitchSeparate);
					  this.findDevByDevType(devParam);
					 }
				  else if("接地刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.SwitchFlowGroundLine);
					  this.findDevByDevType(devParam);
					  }
				  else if("线路接地刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("alllinegroundknife");
					  this.findDevBySetType(devParam);
					  }
				  else if("线路侧接地刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("linegroundknife");
				      this.findDevBySetType(devParam);
				  }
				  else if("自投".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.BztDevice);
					  this.findDevByDevType(devParam);
					  }
				  else if("跳投".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.BztRelate);
					  this.findDevByDevType(devParam);
					  }
				  else if("可拆卸设备".equals(devParam.getDevDesc())){
					  devParam.setDevType(SystemConstants.RemovableDevice);
					  this.findDevByDevType(devParam);
				  }
				  
				  else if("中性点接地刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeGroundZXDDD);
					  this.findDevBySetType(devParam);
					  }
				  else if("线路开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchXL);
					  this.findDevBySetType(devParam);
					  }
				  else if("母联开关|线路开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchXL);
					  this.findDevBySetType(devParam);
	            	  }
				  else if("分段开关(双母双分)".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("FDmotherlinkSwitch");
					  this.findDevBySetType(devParam);
	            	  }
				  else if("母联兼旁路开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("monthlineorsideswitch");
					  this.findDevBySetType(devParam);
					  }
				  else if("母联开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("mlkg");
					  this.findDevBySetType(devParam);
					  }
				  else if("分段开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("fdkg");
					  this.findDevBySetType(devParam);
					  }
				  else if("双母母联开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("dbmonthlinelinkswitch");
					  this.findDevBySetType(devParam);
					  }
				  else if("旁路开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchPL);
					  this.findDevBySetType(devParam);
					  devType += ","+devParam.getDevSetType();
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchMLPL);
					  this.findDevBySetType(devParam);
					  }
				  else if("母线侧开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("mlsideswitch");
					  this.findDevBySetType(devParam);
					  }
				  else if("中间开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("middleswitch");
					  this.findDevBySetType(devParam);
					  }
				  else if("主变开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchDYC);
					  devType += ","+devParam.getDevSetType();
					  this.findDevBySetType(devParam);
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchFHC);
					  this.findDevBySetType(devParam);
					  }
				  else if("电源侧主变开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchDYC);
					  this.findDevBySetType(devParam);
					  }
				  else if("负荷侧主变开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchFHC);
					  this.findDevBySetType(devParam);
					  }
				  else if("低压侧主变开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("lowsideswitch");
					  this.findDevBySetType(devParam);
					  }
				  else if("中压侧主变开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("middlesideswitch");
					  this.findDevBySetType(devParam);
					  }
				  else if("高压侧主变开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchDYC);
					  this.findDevBySetType(devParam);
					  }
				  else if("母线侧高压侧主变开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("highmlsideswitch");
					  this.findDevBySetType(devParam);
					  }
				  else if("中间高压侧主变开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("highmiddleswitch");
					  this.findDevBySetType(devParam);
					  }
				  else if("电抗开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchDK);
					  this.findDevBySetType(devParam);
					  }
				  else if("电抗器开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchDK);
					  this.findDevBySetType(devParam);
					  }
				  else if("电容开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchDR);
					  this.findDevBySetType(devParam);
					  }
				  else if("电容器开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchDR);
					  this.findDevBySetType(devParam);
					  }
				  else if("站用变开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchZYB);
					  this.findDevBySetType(devParam);
					  }
				  else if("配网支线开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(PWSystemConstants.PWRunTypeSwitchFZX);
					  this.findDevBySetType(devParam);
					  }
				  else if("配网分段开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(PWSystemConstants.PWRunTypeSwitchFD);
					  this.findDevBySetType(devParam);
					  }
				  else if("配网联络开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(PWSystemConstants.PWRunTypeSwitchLL);
					  this.findDevBySetType(devParam);
					  }
				  else if("配网主线开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(PWSystemConstants.PWRunTypeSwitchZX);
					  this.findDevBySetType(devParam);
					  }
				  else if("配网末端开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType("pwmdkg");
					  this.findDevBySetType(devParam);
					  }
				  else if("线路刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeXL);
					  this.findDevBySetType(devParam);
					  }
				  else if("旁路刀闸".equals(devParam.getDevDesc())){
						  devParam.setDevSetType(CBSystemConstants.RunTypeKnifePL);
						  this.findDevBySetType(devParam);
					  }
				  else if("刀闸(代替开关)".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeXLS);
					  this.findDevBySetType(devParam);
					  devType += ","+devParam.getDevSetType();
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeZBS);
					  this.findDevBySetType(devParam);
					  }
				  else if("主变刀闸(代替开关)".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeZBS);
					  this.findDevBySetType(devParam);
					  }
				  else if("桥型主变刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeZBS);
					  this.findDevBySetType(devParam);
					  }
				  else if("母线对侧刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeDY);
					  this.findDevBySetType(devParam);
					  }
				  else if("主变侧刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeZBC);
					  this.findDevBySetType(devParam);
					  }
				  else if("母线刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeMX);
					  this.findDevBySetType(devParam);
					  for(int z=0;z<dtdLists.size();z++){
						  if(dtdLists.get(z).getTransDevice().getPowerDeviceName().contains("小车")||dtdLists.get(z).getTransDevice().getPowerDeviceName().contains("手车")){
							  dtdLists.remove(z);
							  z--;
						  }
					  }
					  }
				  else if("母联刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeML);
					  this.findDevBySetType(devParam);
					  }	
				  else if("主变刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeZB);
					  this.findDevBySetType(devParam);
					  }	
				  else if("高压电抗器刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeGYDKQ);
					  this.findDevBySetType(devParam);
					  }	
				  else if("电压互感器刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifePT);
					  this.findDevBySetType(devParam);
					  }	
				  else if("避雷器刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeBLQ);
					  this.findDevBySetType(devParam);
					  }	
				  else if("其它刀闸".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeQT);
					  this.findDevBySetType(devParam);
					  }	
				  else if("电机开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchDJ);
					  this.findDevBySetType(devParam);
					  }	
				  else if("其它开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchQT);
					  this.findDevBySetType(devParam);
					  }	
				  else if("主线开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(PWSystemConstants.PWRunTypeSwitchZX);
					  this.findDevBySetType(devParam);
					  }	
				  else if("联络开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(PWSystemConstants.PWRunTypeSwitchLL);
					  this.findDevBySetType(devParam);
					  }	
				  else if("支线开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(PWSystemConstants.PWRunTypeSwitchFZX);
					  this.findDevBySetType(devParam);
					  }	
				  else if("旁路母线".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSideMother);
					  this.findDevBySetType(devParam);
					  }
				  else if("接带线路开关".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeSwitchXL);
					  this.findDevBySetType(devParam);
					  }
				  else if("母线对侧刀闸-线路".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeDY);
					  this.findDevBySetType(devParam);
					  for(int z=0;z<dtdLists.size();z++){
						  PowerDevice swDevice  = RuleExeUtil.getDeviceSwitch(dtdLists.get(z).getTransDevice());
						  if(swDevice!=null&&!swDevice.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							  dtdLists.remove(z);
							  z--;
						  }
					  }
					  }
				  else if("母线刀闸-线路".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeMX);
					  this.findDevBySetType(devParam);
					  for(int z=0;z<dtdLists.size();z++){
						  PowerDevice swDevice  = RuleExeUtil.getDeviceSwitch(dtdLists.get(z).getTransDevice());
						  if(swDevice!=null&&!swDevice.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							  dtdLists.remove(z);
							  z--;
						  }
					  }
					  }
				 	else if("母线刀闸-母联".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeMX);
					  this.findDevBySetType(devParam);
					  for(int z=0;z<dtdLists.size();z++){
						  PowerDevice swDevice  = RuleExeUtil.getDeviceSwitch(dtdLists.get(z).getTransDevice());
						  if(swDevice!=null&&!swDevice.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							  dtdLists.remove(z);
							  z--;
						  }
					  }
					}
				 	else if("母线刀闸-小车".equals(devParam.getDevDesc())){
					  devParam.setDevSetType(CBSystemConstants.RunTypeKnifeMX);
					  this.findDevBySetType(devParam);
					  for(int z=0;z<dtdLists.size();z++){
						  if(!dtdLists.get(z).getTransDevice().getPowerDeviceName().contains("小车")&&!dtdLists.get(z).getTransDevice().getPowerDeviceName().contains("手车")){
							  dtdLists.remove(z);
							  z--;
						  }
					  }
					}	
					devType += ","+devParam.getDevSetType();
				}

				devType = devType.substring(1);
				//多个开关或者其他设备的时候，组合去掉前面的","
				devParam.setDevSetType(devType);
		}
		if(!"".equals(devParam.getDevProp())){
			 DispatchTransDevice tempDtd=null; 
			 PowerDevice tempDev=null;
			 resultStr = devParam.getDevProp();
			 if(devParam.getDevProp().indexOf("ESN") >= 0){ 
				  //所属变电站名称
				  if(dtdLists.size()>0){
					  tempDtd=dtdLists.get(0);
					  tempDev=tempDtd.getTransDevice();
					  resultStr = resultStr.replace("ESN", CZPService.getService().getDevName(CBSystemConstants.getPowerStation(tempDev.getPowerStationID())));
				  }
			 }
			 if(devParam.getDevProp().indexOf("EVO") >= 0){ 
				  //设备电压等级
//					 if(dtdLists.size()>0){
//						  tempDtd=dtdLists.get(0);
//						  tempDev=tempDtd.getTransDevice();
//						  String volt=String.valueOf(tempDev.getPowerVoltGrade());
//						  volt=volt.substring(0,volt.indexOf("."))+"kV";
//						  resultStr = resultStr.replace("EVO", volt);
//					  }
				  List<String> numList=new ArrayList<String>();
				  String volt="";
				  for (int i = 0; i < dtdLists.size(); i++) {
					  tempDtd=dtdLists.get(i);
					  tempDev=tempDtd.getTransDevice();
					  volt=String.valueOf(tempDev.getPowerVoltGrade());
					  volt=volt.substring(0,volt.indexOf("."))+"kV";
					  if(!"".equals(volt.trim())&&!numList.contains(volt))
						    numList.add(volt);
				   }	
				  resultStr = resultStr.replace("EVO", this.getNumStr(numList));
			 }
			 if(devParam.getDevProp().indexOf("ENO") >= 0){ 
			    //设备编码
					  List<String> numList=new ArrayList<String>();
					  String numStr="";
					  for (int i = 0; i < dtdLists.size(); i++) {
						  tempDtd=dtdLists.get(i);
						  tempDev=tempDtd.getTransDevice();
						  numStr=CZPService.getService().getDevNum(tempDev);
						  if(!"".equals(numStr.trim()))
							    numList.add(numStr);
					   }	
					  resultStr = resultStr.replace("ENO", this.getNumStr(numList));
			 }
			 if(devParam.getDevProp().indexOf("GCN") >= 0){ 
				    //没有经过处理的设备名称
						  String DevName="";
						  for (int i = 0; i < dtdLists.size(); i++) {
							  tempDtd=dtdLists.get(i);
							  tempDev=tempDtd.getTransDevice();
							  String nameStr = CZPService.getService().getDevName(tempDev);
							  if(tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
									  ||tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								  if(nameStr.contains("主变")){
									  nameStr=nameStr.substring(nameStr.indexOf("主变")+2);
								  }
							  }
							  if("".equals(DevName)){
								  DevName = nameStr;
							  }else{
								  DevName = DevName +"、"+ nameStr;
							  }
						   }	
						  resultStr = resultStr.replace("GCN", DevName);
						  
			 }
			 if(devParam.getDevProp().indexOf("ECN") >= 0){ 
				   //设备名称
					  if(dtdLists.size() == 1){
						  tempDtd=dtdLists.get(0);
						  tempDev=tempDtd.getTransDevice();
						  resultStr = resultStr.replace("ECN", StringUtils.getEquipName(tempDev.getPowerDeviceName()));
					  }
					  else {
						  List<String> numList=new ArrayList<String>();
						  String numStr="";
						  for (int i = 0; i < dtdLists.size(); i++) {
							  tempDtd=dtdLists.get(i);
							  tempDev=tempDtd.getTransDevice();
							  numStr=StringUtils.getEquipName(tempDev.getPowerDeviceName());
							  numList.add(numStr);
						   }
						  resultStr = resultStr.replace("ECN", this.getNumStr(numList));
					  }
				  
			 }
			 if(devParam.getDevProp().indexOf("EDN") >= 0){ 
				   //调度命名
					  if(dtdLists.size() == 1){
						  tempDtd=dtdLists.get(0);
						  tempDev=tempDtd.getTransDevice();
						  resultStr = resultStr.replace("EDN", CZPService.getService().getDevName(tempDev));
					  }
					  else {
						  List<PowerDevice> devList=new ArrayList<PowerDevice>();
						  String numStr="";
						  for (int i = 0; i < dtdLists.size(); i++) {
							  tempDtd=dtdLists.get(i);
							  tempDev=tempDtd.getTransDevice();
							  devList.add(tempDev);
						   }
						  if(devList.size() == 1)
							  numStr=CZPService.getService().getDevName(devList.get(0));
						  else if(devList.size() > 1)
							  numStr=CZPService.getService().getDevName(devList);
						  resultStr = resultStr.replace("EDN", numStr);
					  }
			 }
			 if(devParam.getDevProp().indexOf("ENS") >= 0){ 
				  //源状态
				  if(dtdLists.size()>0){
					  tempDtd=dtdLists.get(0);
					  tempDev=tempDtd.getTransDevice();
					  resultStr = resultStr.replace("ENS", CBSystemConstants.getDeviceStatusName(tempDev.getDeviceType(),tempDtd.getBeginstatus()));
				  }
			 }
			 if(devParam.getDevProp().indexOf("ENT") >= 0){ 
				  //目标状态
				  if(dtdLists.size()>0){
					  tempDtd=dtdLists.get(0);
					  tempDev=tempDtd.getTransDevice();
					  resultStr = resultStr.replace("ENT", CBSystemConstants.getDeviceStatusName(tempDev.getDeviceType(),tempDtd.getEndstate()));
				  }
			 }
			
		}
		
		return resultStr;
	}
	/**
	 * 
	 * @param devSetType 设备运行状态
	 * @param srcStatus 源状态
	 * @param tagStatus 目标状态
	 */
	private void findDevBySetType(DevParam devParam){
		String devSetType = devParam.getDevSetType();
		String devRunModel = devParam.getDevRunModel();
		String srcStatus = devParam.getDevSrcStatus();
		String tagStatus = devParam.getDevTagStatus();
		String beginStatus = devParam.getDevBeginStatus();
		String endStatus = devParam.getDevEndStatus();
		String voltLevel = devParam.getDevVol();
		String voltMinLevel = devParam.getDevMinVol();
		String control = devParam.getDevControl();
		Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
		DispatchTransDevice dtd=null;
		PowerDevice dev=null;
		List<PowerDevice> devList = new ArrayList<PowerDevice>();
		for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();) {
			 dtd=(DispatchTransDevice)iterator.next();
			 dev=dtd.getTransDevice();
			 if(devList.contains(dev))
				 continue;
			 if(!"".equals(devSetType)){
				 
				 if(StringUtils.compareStr("mlsideswitch", devSetType) == 0) {
					 if(!dev.getDeviceType().equals(SystemConstants.Switch) || RuleExeUtil.isSwMiddleInThreeSecond(dev))
						 continue;
				 }
				 else if(StringUtils.compareStr("mlkg", devSetType) == 0) {
					 if(!dev.getDeviceType().equals(SystemConstants.Switch) || !dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) || !RuleExeUtil.isSwitchDoubleML(dev))
						 continue;
				 }
				 else if(StringUtils.compareStr("fdkg", devSetType) == 0) {
					 if(!dev.getDeviceType().equals(SystemConstants.Switch) || !dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) || RuleExeUtil.isSwitchDoubleML(dev))
						 continue;
				 }
				 else if(StringUtils.compareStr("pwmdkg", devSetType) == 0) {
					 
					 List<PowerDevice> equips = new ArrayList<PowerDevice>();
						for(int i=0;i<CBSystemConstants.getSamepdlist().size();i++){
				   	 	    PowerDevice pd = CBSystemConstants.getSamepdlist().get(i);
				   	 	    equips.add(pd);
						}
						if(!equips.contains(dev))
							continue;
				 }
				 else if(StringUtils.compareStr("middleswitch", devSetType) == 0) {
					 if(!dev.getDeviceType().equals(SystemConstants.Switch) || !RuleExeUtil.isSwMiddleInThreeSecond(dev))
						 continue;
				 }
				 else if(StringUtils.compareStr("lowsideswitch", devSetType) == 0) {
					 if(!dev.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchFHC) || !RuleExeUtil.isSwitchLowVolSide(dev))
						 continue;
				 }
				 else if(StringUtils.compareStr("middlesideswitch", devSetType) == 0) {
					 if(!dev.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchFHC) || !RuleExeUtil.isSwitchMiddleVolSide(dev))
						 continue;
				 }
				 else if(StringUtils.compareStr("dbmonthlinelinkswitch", devSetType) == 0) {
					 if((!dev.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchML) && 
							 !dev.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchMLPL))
							 || !RuleExeUtil.isSwitchDoubleML(dev))
						 continue;
				 }
				 else if(StringUtils.compareStr("FDmotherlinkSwitch", devSetType) == 0) {
					 if(!RuleExeUtil.isFDSwitchSMSF(dev))
						 continue;
				 }
				 else if(StringUtils.compareStr("linegroundknife", devSetType) == 0) {
					 if(!dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine))
						 continue;
					 List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.InOutLine);
					 if(list.size() == 0)
						 continue;
				 } else if(StringUtils.compareStr("alllinegroundknife", devSetType) == 0) {
					 if(!dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine))
						 continue;
				//	 List<PowerDevice> linelist = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.InOutLine);
					 List<PowerDevice> knifelist = RuleExeUtil.getDeviceDirectList(dev,SystemConstants.SwitchSeparate,CBSystemConstants.RunTypeKnifeXL);
//					 if(linelist.size() == 0 && knifelist.size() == 0)
//						 continue;
					 if( knifelist.size() == 0){
						 List<PowerDevice> linelist = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.InOutLine);
						 if(linelist.size() == 0){
							 knifelist = RuleExeUtil.getDeviceDirectList(dev,SystemConstants.SwitchSeparate);

							 boolean flag = false;
							 
							 for(PowerDevice knife : knifelist){
								 linelist =  RuleExeUtil.getDeviceDirectList(knife,SystemConstants.InOutLine);
								 if(linelist.size()>0&&CZPService.getService().getDevNum(dev).endsWith("67")){
									 flag = true;
									 break;
								 }
							 }
							 
							 if(!flag){
								 continue;
							 }
						 }
							
					 }
						 
				 }
				 else if(StringUtils.compareStr("linesideknife", devSetType) == 0) {
					 if(!dev.getDeviceType().equals(SystemConstants.SwitchSeparate))
						 continue;
					 List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.InOutLine);
					 if(list.size() == 0)
						 continue;
				 }
				 else if(StringUtils.compareStr(dev.getDeviceSetType(), devSetType) != 0)
					 	 continue;
			 }
			 if(!"".equals(devRunModel)&& StringUtils.compareStr(dev.getDeviceRunModel(), devRunModel)==1){
				 continue;
			 }
			 if(!"".equals(srcStatus)&&!dtd.getBeginstatus().equals(srcStatus)){
				 continue;
			 }
			 if(!"".equals(tagStatus)&&!dtd.getEndstate().equals(tagStatus)){
				 continue;
			 }
			 if(!"".equals(beginStatus)&&!RuleExeUtil.getDeviceBeginStatus(dtd.getTransDevice()).equals(beginStatus)){
				 continue;
			 }
			 if(!"".equals(endStatus)&&!dtd.getTransDevice().getDeviceStatus().equals(endStatus)){
				 continue;
			 }
			 if(!"".equals(voltLevel)&&dtd.getTransDevice().getPowerVoltGrade()!=Double.valueOf(voltLevel)){
				 continue;
			 }
			 if(!"".equals(voltMinLevel)&&dtd.getTransDevice().getPowerVoltGrade()<Double.valueOf(voltMinLevel)){
				 continue;
			 }
			 if(!dev.isPW()&&!stationDevice.isPW()&&null!=stationDevice&&!stationDevice.getPowerStationID().equals(dev.getPowerStationID())){
				 continue;
			 }
			 if(!"".equals(control)&&!dtd.getTransDevice().getIsLoseElec().equals(control)){
				 continue;
			 }
			 if(!this.dtdLists.contains(dtd)){
				 devList.add(dev);
				 dtdLists.add(dtd);
			 }
		}
	}
	/**
	 * 
	 * @param devType 设备类型
	 * @param srcStatus 源状态
	 * @param tagStatus 目标状态
	 */
	private void findDevByDevType(DevParam devParam){
		String devType = devParam.getDevType();
		String devRunModel = devParam.getDevRunModel();
		String srcStatus = devParam.getDevSrcStatus();
		String tagStatus = devParam.getDevTagStatus();
		String beginStatus = devParam.getDevBeginStatus();
		String endStatus = devParam.getDevEndStatus();
		String voltLevel = devParam.getDevVol();
		String voltMinLevel = devParam.getDevMinVol();
		String control = devParam.getDevControl();
		Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
		DispatchTransDevice dtd=null;
		PowerDevice dev=null;
		List<PowerDevice> devList = new ArrayList<PowerDevice>();
		for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();) {
			 dtd=(DispatchTransDevice)iterator.next();
			 dev=dtd.getTransDevice();
			 if(devList.contains(dev))
				 continue;
			 if(!"".equals(devType)&& StringUtils.compareStr(dev.getDeviceType(), devType)==1){
				 continue;
			 }
			 if(!"".equals(devRunModel)&& StringUtils.compareStr(dev.getDeviceRunModel(), devRunModel)==1){
				 continue;
			 }
			 if(!"".equals(srcStatus)&&!dtd.getBeginstatus().equals(srcStatus)){
				 continue;
			 }
			 //if(!"".equals(tagStatus)&&!CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals(tagStatus)){
			 if(!"".equals(tagStatus)&&!dtd.getEndstate().equals(tagStatus)){
				 continue;
			 }
			 if(!"".equals(beginStatus)&&!RuleExeUtil.getDeviceBeginStatus(dtd.getTransDevice()).equals(beginStatus)){
				 continue;
			 }
			 if(!"".equals(endStatus)&&!dtd.getTransDevice().getDeviceStatus().equals(endStatus)){
				 continue;
			 }
			 if(!"".equals(voltLevel)&&dtd.getTransDevice().getPowerVoltGrade()!=Double.valueOf(voltLevel)){
				 continue;
			 }
			 if(!"".equals(voltMinLevel)&&dtd.getTransDevice().getPowerVoltGrade()<Double.valueOf(voltMinLevel)){
				 continue;
			 }
			 if(!dev.isPW()&&!stationDevice.isPW()&&null!=stationDevice&&!stationDevice.getPowerStationID().equals(dev.getPowerStationID())){
				 continue;
			 }
			 if(!"".equals(control)&&!dtd.getTransDevice().getIsLoseElec().equals(control)){
				 continue;
			 }
			 if(!this.dtdLists.contains(dtd)){
				 devList.add(dev);
				 dtdLists.add(dtd);
			 }
		}
	}
	
	/**
	 * 
	 * @param numList 编号集合
	 * @return 设备编号组合
	 */
	private String getNumStr(List<String> numList){
		if(numList.size()==0)
			return "";
		if(numList.size()==1)
			return numList.get(0);
         String numStr="";
         for (int i = 0; i < numList.size(); i++) {
        	 if("".equals(numStr))
        		    numStr=numList.get(i);
				else
					numStr=numStr+"、"+numList.get(i);
		 }
		return numStr;
	}

}
