package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.lj.LJGCBHDialog;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEPLDGTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱旁路代供停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> plkgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						plkgList.add(dev);
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev);
				replaceStr += stationName+"@将"+plkgName+"保护定值置于#X定值区/r/n";
				replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV旁路保护投入/r/n";
				replaceStr += stationName+"@核实"+plkgName+"重合闸退出/r/n";
				
				String mxName = "";
				
				List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
				
				for(PowerDevice mx : tempmxList){
					mxName = "于"+CZPService.getService().getDevName(mx);
					break;
				}
				
				replaceStr += stationName+"@核实"+plkgName+"热备用"+mxName+"/r/n";
				replaceStr += stationName+"@合上"+plkgName+"/r/n";
				replaceStr += stationName+"@断开"+plkgName+"/r/n";
				
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice line : lineList){
					List<PowerDevice> pldzList = RuleExeUtil.getDeviceList(line, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifePL,"",true, true, true, true);
					
					for(PowerDevice pldz : pldzList){
						replaceStr += stationName+"@合上"+CZPService.getService().getDevName(pldz)+"/r/n";
					}
				}
				
				replaceStr += stationName+"@用"+plkgName+"同期合环/r/n";
				replaceStr += stationName+"@退出"+deviceName+"重合闸/r/n";
				replaceStr += stationName+"@投入"+plkgName+"重合闸/r/n";
			}
			
			replaceStr += "普洱地调@遥控断开"+stationName+deviceName+"/r/n";
			
			if(RuleExeUtil.isDeviceHadStatus(curDev, "1", "2")){
				replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
