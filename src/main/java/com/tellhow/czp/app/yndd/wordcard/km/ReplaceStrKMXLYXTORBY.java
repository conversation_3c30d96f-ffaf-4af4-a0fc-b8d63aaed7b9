package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKMXLYXTORBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昆明线路由运行转热备用".equals(tempStr)){
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			String devName = CZPService.getService().getDevName(curDev);

			String sql = "SELECT DEVICE_NUM,UNIT,OPERATION_KIND,LOWERUNIT,ENDPOINT_KIND FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (  SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+curDev.getPowerDeviceID()+"')";
			List<Map<String, Object>> stations = DBManager.queryForList(sql);
			
			for(Map<String, Object> station:stations) {
				String stationName = StringUtils.ObjToString(station.get("UNIT")).trim();
				String stationKind = StringUtils.ObjToString(station.get("OPERATION_KIND")).trim();
				String deviceNum = StringUtils.ObjToString(station.get("DEVICE_NUM")).trim();
				String lowerunit = StringUtils.ObjToString(station.get("LOWERUNIT")).trim();
				String endpointkind = StringUtils.ObjToString(station.get("ENDPOINT_KIND")).trim();

				if(stationKind.equals("下令")){
					if(endpointkind.equals("外接站用变")){
						replaceStr += "昆明地调@遥控断开"+stationName+devName+ deviceNum +"断路器/r/n";
					}else{
						if(lowerunit.equals("")){
							replaceStr += stationName + "@断开"+devName+ deviceNum +"断路器/r/n";
						}else{
							replaceStr += stationName + "@断开"+lowerunit+devName+ deviceNum +"断路器/r/n";
						}
					}
				}else if(stationKind.equals("许可")){
					replaceStr += stationName + "@许可将"+ devName +"由运行转热备用/r/n";
				}else if(stationKind.equals("落实")){
					if(endpointkind.equals("其它供电局")){
						replaceStr += stationName + "@落实"+ devName +"已由运行转热备用/r/n";
					}else{
						replaceStr += stationName + "@落实"+ devName +"已由运行转热备用/r/n";
					}
				}
			}
			
			if(loadLineTrans.size()>0){
				for(PowerDevice loadLineTran : loadLineTrans){
					List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
					List<PowerDevice> xlkglist = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					List<PowerDevice> mlkglist = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					List<PowerDevice> zbkglist = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDYC + "," + CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);

					if(mxlist.size()>0){
						if(mxlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
							for(PowerDevice mlkg : mlkglist){
								if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
									replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID()))+CZPService.getService().getDevName(mlkg)+"/r/n";
								}
							}
							
							for(PowerDevice xlkg : xlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
									replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID()))+CZPService.getService().getDevName(xlkg)+"/r/n";
								}
							}
						}else if(mxlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
							xlkglist.addAll(zbkglist);
							
							for(PowerDevice xlkg : xlkglist){
								if(RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
									if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
										replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID()))+CZPService.getService().getDevName(xlkg)+"/r/n";
									}
								}
							}
							
							for(PowerDevice xlkg : xlkglist){
								if(!RuleExeUtil.isSwMiddleInThreeSecond(xlkg)){
									if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")){
										replaceStr += "昆明地调@遥控断开"+CZPService.getService().getDevName(CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID()))+CZPService.getService().getDevName(xlkg)+"/r/n";
									}
								}
							}
						}
					}
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
