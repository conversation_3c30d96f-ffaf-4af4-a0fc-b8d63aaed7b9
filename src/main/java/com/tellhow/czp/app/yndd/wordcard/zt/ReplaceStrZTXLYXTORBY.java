package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTXLYXTORBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通线路由运行转热备用".equals(tempStr)){
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			PowerDevice sourceStation = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());

			List<PowerDevice> stationList = new ArrayList<PowerDevice>();
			
			stationList.add(sourceStation);
			
			for(PowerDevice dev : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				stationList.add(station);
			}
			
			String volt = (int)curDev.getPowerVoltGrade()+"kV";
			
			String sql = "SELECT B.ID,B.UNIT,B.LOWERUNIT,B.SWITCH_NAME FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER B WHERE B.LINE_ID = (SELECT A.ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND A WHERE A.ID = '"+curDev.getPowerDeviceID()+"')";
			List<Map<String, String>> list = DBManager.queryForList(sql);
		    
			for(Map<String,String> map : list){
				String unit = StringUtils.ObjToString(map.get("UNIT"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				String switchname = StringUtils.ObjToString(map.get("SWITCH_NAME"));

				if(!switchname.equals("")){
					if(switchname.contains("、")){
						String[] switchnameArr = switchname.split("、");
						
						for(String str : switchnameArr){
							replaceStr += unit+"@断开"+lowerunit+str+"/r/n";
						}
					}else{
						replaceStr += unit+"@断开"+lowerunit+switchname+"/r/n";
					}
				}
			}
			
			if(loadLineTrans.size()>0){
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);

					if(xlkgList.size() == 2){
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
									replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
								}
							}
						}
						
						for(PowerDevice dev : xlkgList){
							if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
									replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
								}
							}
						}
					}else{
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
						
						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice dev = it.next();
							
							if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									hignVoltMlkgList.add(dev);
								}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									hignVoltXlkgList.add(dev);
								}
							}
						}
						
						boolean ishh = false;
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(hignVoltXlkgList);
						tempList.addAll(hignVoltMlkgList);

						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
								ishh = true;
							}
						}
						
						if(ishh){
							replaceStr += CommonFunction.getLineBztResult(stationList,volt,"退出");
						}
						
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
								replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
							}
						}
						
						for(PowerDevice dev : xlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += CommonFunction.getControlContentZT(stationName, "断开", deviceName, "", "昭通地调");
							}
						}
					}
				}
			}
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				
				if(xlkgList.size() == 2){
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : xlkgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
				}else{
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"处热备用/r/n";
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
			}			
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
