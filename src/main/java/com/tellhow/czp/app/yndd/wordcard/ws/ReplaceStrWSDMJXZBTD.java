package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionWS;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSDMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山单母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();

			boolean isSwitchControl = true;
			
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			
			for(PowerDevice dev : zbgyckgList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			}

			
			
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						gycxlkgList.add(dev);
					}
				}
			}
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				otherzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(otherzxdjddzList);
				break;
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			for(PowerDevice dev : zbdyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				
			}else{
				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunctionWS.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
				}
				
				if(otherzxdjddzList.size() > 0){
					replaceStr += CommonFunctionWS.getZxdJddzOnCheckContent(otherzxdjddzList, stationName, station);
				}
			}
			
			if(dycmlkgList.size() == 0){
				for(PowerDevice dev : dycmxList){
					if(dev.getPowerVoltGrade() == 10){
						for(PowerDevice zbdyckg : zbdyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
								replaceStr += "文山配调@确认"+stationName+CZPService.getService().getDevName(dev)+"可以停电/r/n";
							}
						}
					}
				}
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
					}
				}
				
				replaceStr += "文山地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
			}else{
				if(station.getPowerVoltGrade() == 35){
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
					
					if(curDev.getPowerStationID().equals("SS-185")&&curDev.getPowerDeviceID().equals("19488")){
						for(PowerDevice dev : gycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
								replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
							}
						}
						
						for(PowerDevice gycxlkg : gycxlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("0")){
								replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(gycxlkg)+"/r/n";
							}
						}
					}
					
					boolean isSeparateControl = false;
					
					for(PowerDevice dev : kgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(!dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
								if(CommonFunctionWS.ifSwitchSeparateControl(dz)){
									isSeparateControl = true;
								}
							}
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
						}
					}

					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(curDev.getPowerStationID().equals("SS-185")&&curDev.getPowerDeviceID().equals("19488")){
						for(PowerDevice dev : gycxlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
							}
						}
						
						for(PowerDevice dev : gycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
					
					if(isSeparateControl){
						boolean isKnifeXC = false;
						boolean isKnifeFB = false;

						for(PowerDevice dev : zbdyckgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(dzList.size() == 1){
								isKnifeFB = true;
								break;
							}else{
								for(PowerDevice dz : dzList){
									if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
										isKnifeXC = true;
										break;
									}
								}
							}
						}
						
						if(isKnifeFB){
							for(PowerDevice dev : zbdyckgList){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffContent(dzList, stationName);
							}
						}else if(isKnifeXC){
							for(PowerDevice dev : zbdyckgList){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
							}
						}else{
							for(PowerDevice dev : zbdyckgList){
								replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
							}
						}
						

						isKnifeXC = false;
						isKnifeFB = false;

						for(PowerDevice dev : zbgyckgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
							
							if(dzList.size() == 1){
								isKnifeFB = true;
								break;
							}else{
								for(PowerDevice dz : dzList){
									if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
										isKnifeXC = true;
										break;
									}
								}
							}
						}
						
						if(isKnifeFB){
							for(PowerDevice dev : zbgyckgList){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffContent(dzList, stationName);
							}
						}else if(isKnifeXC){
							for(PowerDevice dev : zbgyckgList){
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
							}
						}else{
							for(PowerDevice dev : zbgyckgList){
								replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
							}
						}
					}else{
						if(curDev.getDeviceStatus().equals("2")){
							replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
						}
					}
				}else{
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
						}
					}
					
					for(PowerDevice gycxlkg : gycxlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("0")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(gycxlkg)+"/r/n";
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
						}
					}

					for(PowerDevice zbdyckg : zbdyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
						}
					}

					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : gycxlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(curDev.getDeviceStatus().equals("2")){
						for(PowerDevice dev : zbdyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
									replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
									
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
								}else{
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
								}
								
								List<PowerDevice> dzList = CommonFunctionWS.getTransformerKnife(curDev, dev);
								
								for (Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
									PowerDevice dz = it.next();
									
									if(dz.getPowerDeviceName().endsWith("1")){
										it.remove();
									}
								}
								
								replaceStr += CommonFunctionWS.getKnifeOffContent(dzList,stationName);
							}
						}
						
						for(PowerDevice dev : zbzyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
									replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
									
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
								}else{
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
								}
								
								List<PowerDevice> dzList = CommonFunctionWS.getTransformerKnife(curDev, dev);
								replaceStr += CommonFunctionWS.getKnifeOffContent(dzList,stationName);
							}
						}
						
						for(PowerDevice dev : zbgyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
									replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
									
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
								}else{
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
								}
							}
						}
					}
				}
			}
			
			if(dycmlkgList.size() == 0){
				for(PowerDevice dev : dycmxList){
					if(dev.getPowerVoltGrade() == 10){
						for(PowerDevice zbdyckg : zbdyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
								replaceStr += "文山配调@通知"+stationName+deviceName+"已处冷备用/r/n";
							}
						}
					}
				}
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				
			}else{
				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunctionWS.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
				}
			}

			if(curDev.getPowerVoltGrade() > 35){
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
