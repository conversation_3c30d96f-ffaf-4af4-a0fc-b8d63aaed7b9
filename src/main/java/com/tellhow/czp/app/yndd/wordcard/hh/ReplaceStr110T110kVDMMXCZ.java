package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr110T110kVDMMXCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("110T110kV单母母线操作".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String sbName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> mlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycrbyxlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycyxxlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycyxzbkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zbdycyxzbkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dyczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			
			PowerDevice curzb = new PowerDevice();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
						mlkgList.add(dev);
					}
				}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
						gycrbyxlkgList.add(dev);
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						gycyxxlkgList.add(dev);
					}
				}
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					List<PowerDevice> zbdzList =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					if(zbdzList.size()>1){
						zbList.add(dev);
					}
					
					List<PowerDevice> highkgList = RuleExeUtil.getTransformerSwitchHigh(dev);
					List<PowerDevice> loadkgList = RuleExeUtil.getTransformerSwitchLoad(dev);

					zbdycyxzbkgList.addAll(loadkgList);
					
					if(highkgList.size()>0){
						for(PowerDevice highkg: highkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(highkg).equals("0")){
								gycyxzbkgList.add(highkg);
							}
						}
						gycmlkgList = RuleExeUtil.getDeviceList(highkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
					
					List<PowerDevice> midkgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
					
					if(midkgList.size()>0){
						zycmlkgList = RuleExeUtil.getDeviceList(midkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					
						if(zycmlkgList.size()>0){
							zycmxList = RuleExeUtil.getDeviceList(zycmlkgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						}
					}
					
					List<PowerDevice> lowkgList = RuleExeUtil.getTransformerSwitchLow(dev);
					
					if(lowkgList.size()>0){
						dycmlkgList = RuleExeUtil.getDeviceList(lowkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						
						for(Iterator<PowerDevice> itor = dycmlkgList.iterator();itor.hasNext();){
							PowerDevice dycmlkg = itor.next();
							
							dycmxList = RuleExeUtil.getDeviceList(dycmlkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
							
							if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycmlkg).equals("1")){
								itor.remove();
							}
						}
					}
				}
			}
			
			for(Iterator<PowerDevice> itor = zbList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(RuleExeUtil.isDeviceChanged(dev)){
					curzb = dev;
				}
			}
			
			if(gycmlkgList.size()==0){//单母不分段
				List<PowerDevice> allrbykgList = new ArrayList<PowerDevice>();
				List<PowerDevice> alllbykgList = new ArrayList<PowerDevice>();
				List<PowerDevice> zybdzList = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
						if(dev.getPowerVoltGrade()>10){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								allrbykgList.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								allrbykgList.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								allrbykgList.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								allrbykgList.add(dev);
							}
						}else{
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								allrbykgList.add(dev);
							}
						}
					}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						if(dev.getPowerVoltGrade()>10){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								alllbykgList.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								alllbykgList.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								alllbykgList.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								alllbykgList.add(dev);
							}
						}else{
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								alllbykgList.add(dev);
							}
						}
					}
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT)){
						if(dev.getPowerDeviceName().contains("站用变")&&!dev.getPowerDeviceName().contains("接地站用变")){
							zybdzList.add(dev);
						}
					}
				}
				
				RuleExeUtil.swapDeviceList(allrbykgList);
				RuleExeUtil.swapDeviceList(alllbykgList);

				if(allrbykgList.size()>0){
					replaceStr += "核实"+CZPService.getService().getDevName(allrbykgList)+"热备用/r/n";
				}
				
				if(alllbykgList.size()>0){
					replaceStr += "核实"+CZPService.getService().getDevName(alllbykgList)+"冷备用/r/n";
				}

				if(zybdzList.size()>0){
				    replaceStr += "核实"+CZPService.getService().getDevName(zybdzList)+"在拉开位置/r/n";
				    replaceStr += "核实10kV#X站用变冷备用/r/n";
			    }
				
				replaceStr += CommonFunctionHH.getXhxqStrReplace(zbList, "全部拉开");
				
			    replaceStr += "红河配调@核实"+stationName+CZPService.getService().getDevName(dycmxList)+"上其所管辖的所有10kV出线断路器均己转冷备用/r/n";
			    
			    replaceStr += "退出10kV备自投装置/r/n";
			    replaceStr += "退出35kV备自投装置/r/n";
				replaceStr += "退出110kV备自投装置/r/n";

				replaceStr += "核实"+CZPService.getService().getDevName(zbList)+"中性点及其零序保护己投入/r/n";

				List<PowerDevice> dkallkgList = new ArrayList<PowerDevice>();
				
				RuleExeUtil.swapDeviceList(zbdycyxzbkgList);
				
				dkallkgList.addAll(zbdycyxzbkgList);
				dkallkgList.addAll(gycyxzbkgList);
				dkallkgList.addAll(gycyxxlkgList);
				
				if(dkallkgList.size()>0){
					for(Iterator<PowerDevice> itor = dkallkgList.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							itor.remove();
						}
					}
					
					replaceStr += CommonFunctionHH.getYcDkStrReplace(dkallkgList, stationName);
					
					String zbnr = "";
					String mxnr = "";

					for(PowerDevice zb : zbList){
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
						tempList.addAll(RuleExeUtil.getTransformerSwitchMiddle(zb));
						tempList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
						
						for(Iterator<PowerDevice> itor = tempList.iterator();itor.hasNext();){
							PowerDevice dev = itor.next();
							
							if(!RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								itor.remove();
							}
						}
						
						tempList.add(0,zb);
						
						zbnr += CZPService.getService().getDevName(tempList)+"、";
					}
					
					RuleExeUtil.swapDeviceList(dycmxList);
					RuleExeUtil.swapDeviceList(zycmxList);

					mxnr += CZPService.getService().getDevName(dycmxList)+"及母线设备、";
					mxnr += CZPService.getService().getDevName(zycmxList)+"及母线设备、";
					mxnr += CZPService.getService().getDevName(curDev)+"及母线设备、";
					
					replaceStr += "核实"+zbnr+mxnr+CZPService.getService().getDevName(gycyxxlkgList)+"热备用/r/n";
					
					zbnr = "";
					
					for(PowerDevice zb : zbList){
						List<PowerDevice> tempList = new ArrayList<PowerDevice>();
						
						tempList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
						tempList.addAll(RuleExeUtil.getTransformerSwitchMiddle(zb));
						tempList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
						
						for(Iterator<PowerDevice> itor = tempList.iterator();itor.hasNext();){
							PowerDevice dev = itor.next();
							
							if(!RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								itor.remove();
							}
						}
						
						if(zbdycyxzbkgList.get(0).getDeviceStatus().equals("2")){
							tempList.add(0,zb);
						}
						
						zbnr += CZPService.getService().getDevName(tempList)+"、";
					}
					
					if(zbdycyxzbkgList.get(0).getDeviceStatus().equals("2")){
						replaceStr += "将"+zbnr+mxnr+CZPService.getService().getDevName(xlkgList)+"由热备用转冷备用/r/n";
					}else{
						mxnr = CZPService.getService().getDevName(curDev)+"及母线设备、";
						replaceStr += "将"+zbnr+mxnr+CZPService.getService().getDevName(xlkgList)+"由热备用转冷备用/r/n";
					}
				}
			}else{
				List<PowerDevice> otherzbdyclist = new ArrayList<PowerDevice>();
				List<PowerDevice> curzbdyclist = new ArrayList<PowerDevice>();

				List<PowerDevice> zndyckglist =  RuleExeUtil.getTransformerSwitchLoad(curzb);
				List<PowerDevice> zbdyckglist =  RuleExeUtil.getTransformerSwitchLow(curzb);
				List<PowerDevice> zbzyckglist =  RuleExeUtil.getTransformerSwitchMiddle(curzb);
				List<PowerDevice> zbgyckglist =  RuleExeUtil.getTransformerSwitchHigh(curzb);
				
				for(PowerDevice zbzdyckg : zndyckglist){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(zbzdyckg).equals("1")){
						curzbdyclist.add(zbzdyckg);
					}
					
					List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbzdyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);

					for(PowerDevice mlkg : kgList){
						if(mlkg.getDeviceStatus().equals("0")){
							List<PowerDevice> zbdycList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, false, true);

							for(PowerDevice zbdyc : zbdycList){
								if(!zbzdyckg.getPowerDeviceID().equals(zbdyc.getPowerDeviceID())){
									if(RuleExeUtil.getDeviceEndStatus(zbdyc).equals("0")){
										otherzbdyclist.add(zbdyc);
										break;
									}
								}
							}
						}
					}
				}
				
				if(mlkgList.size()>0){
					mlkgList.addAll(gycrbyxlkgList);
					mlkgList.addAll(otherzbdyclist);
					mlkgList.addAll(curzbdyclist);
					
					replaceStr += "核实"+CZPService.getService().getDevName(mlkgList)+"热备用/r/n";
				}else{
					replaceStr += "核实"+CZPService.getService().getDevName(gycrbyxlkgList)+"热备用/r/n";
				}
				
				if(dyczbkgList.size()>0){
					List<PowerDevice> mxdzList = RuleExeUtil.getDeviceList(dyczbkgList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeMX, "", false, true, true, true);
					
					if(mxdzList.size()>0){
						for(PowerDevice mxdz : mxdzList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(mxdz).equals("1")){
								replaceStr += "核实"+CZPService.getService().getDevName(mxdz)+"在拉开位置/r/n";
							}
						}
					}
				}
				
				replaceStr += CommonFunctionHH.getXhxqStrReplace(zbList, "停电");
				
				replaceStr += "退出110kV备自投装置/r/n";

				String hsTemp ="";
				
				if(gycrbyxlkgList.size()>0){
					if(RuleExeUtil.getDeviceBeginStatus(gycrbyxlkgList.get(0)).equals("1")&&!xlkgList.contains(gycrbyxlkgList.get(0))){
						replaceStr += "红河地调@遥控用"+stationName+CZPService.getService().getDevName(gycrbyxlkgList)+"同期合环/r/n";
						hsTemp += "核实"+CZPService.getService().getDevName(gycrbyxlkgList)+"运行正常/r/n";
					}
				}
				
				if(gycmlkgList.size()>0){
					if(RuleExeUtil.getDeviceBeginStatus(gycmlkgList.get(0)).equals("1")){
						replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkgList)+"/r/n";
						hsTemp += "核实"+CZPService.getService().getDevName(gycmlkgList)+"运行正常/r/n";
					}
				}
				
				if(xlkgList.size()>0){
					if(RuleExeUtil.getDeviceBeginStatus(xlkgList.get(0)).equals("0")){
						replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkgList)+"/r/n";
						hsTemp += "核实"+CZPService.getService().getDevName(xlkgList)+"热备用/r/n";
					}
				}

				if(!hsTemp.equals("")){
					replaceStr += hsTemp;
				}
				
				replaceStr += CommonFunctionHH.getDqZbHbBhTMlkgDzStrReplace(curzb, "midlow", "停电");
				
				for(PowerDevice dev : gycmlkgList){
					replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+(int)dev.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(dev)+"/r/n";
				}
				
				replaceStr += CommonFunctionHH.getZbBLTQStrReplace(curzb);
				
				replaceStr += CommonFunctionHH.get110kVZbZxdStrReplace(curzb, zbList);
				
				List<PowerDevice> loadmlkglist = new ArrayList<PowerDevice>();
				
				loadmlkglist.addAll(zycmlkgList);
				loadmlkglist.addAll(dycmlkgList);
				
				for(Iterator<PowerDevice> itor = loadmlkglist.iterator();itor.hasNext();){
					PowerDevice loadmlkg = itor.next();
					
					if(!RuleExeUtil.getDeviceEndStatus(loadmlkg).equals("0")){
						itor.remove();
					}
				}
				
				replaceStr += CommonFunctionHH.getYcHsStrReplace(loadmlkglist, stationName);
				
				List<PowerDevice> tempkglist = new ArrayList<PowerDevice>();

				if(zbList.size()>0){
					tempkglist.addAll(RuleExeUtil.getTransformerSwitchLow(curzb));
					tempkglist.addAll(RuleExeUtil.getTransformerSwitchMiddle(curzb));
					tempkglist.addAll(RuleExeUtil.getTransformerSwitchHigh(curzb));
					
					if(otherzbdyclist.size()>0){
						replaceStr += CommonFunctionHH.getYcHsStrReplace(otherzbdyclist, stationName);
					}
					
					for(Iterator<PowerDevice> itor = tempkglist.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							itor.remove();
						}
					}
					
					tempkglist.addAll(gycmlkgList);
					
					if(tempkglist.size()==1){
						replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(tempkglist)+"/r/n";
					}else if(tempkglist.size()>1){
						replaceStr += "红河地调@遥控依次断开"+stationName+CZPService.getService().getDevName(tempkglist)+"/r/n";
					}
				}
				
				if(loadmlkglist.size()>0){
					replaceStr += "核实"+stationName+CZPService.getService().getDevName(loadmlkglist)+"运行正常/r/n";
				}
				
				if(zbList.size()>0){
					if(otherzbdyclist.size()>0){
						replaceStr += "核实"+stationName+CZPService.getService().getDevName(otherzbdyclist)+"运行正常/r/n";
					}
					
					tempkglist.add(0,curzb);
					
					List<PowerDevice> zbdzList = RuleExeUtil.getDeviceList(curzb, SystemConstants.SwitchSeparate , "", CBSystemConstants.RunTypeKnifeZBS, "", true, true, true, true);
					
					if(zbdzList.size()>0){
						replaceStr += "拉开"+CZPService.getService().getDevName(zbdzList)+"/r/n";
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(zbdyckglist);
					tempList.addAll(zbzyckglist);
					tempList.addAll(zbgyckglist);
					
					boolean allkglby = true;
					
					for(PowerDevice dev : tempList){
						if(!dev.getDeviceStatus().equals("2")){
							allkglby = false;
							break;
						}
					}
					
					if(allkglby){
						tempList.addAll(0,zbList);
					}
					
					for(PowerDevice gycmlkg : gycmlkgList){
						if(!tempList.contains(gycmlkg)){
							tempList.add(gycmlkg);
						}
					}
					
					for(PowerDevice xlkg : xlkgList){
						if(!tempList.contains(xlkg)){
							tempList.add(xlkg);
						}
					}
					
					for(Iterator<PowerDevice> itor = tempList.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							itor.remove();
						}
					}
					
					if(zbList.size()==3){
						String zbnr = "";
						
						for(PowerDevice zb : zbList){
							List<PowerDevice> tempkgList =  new ArrayList<PowerDevice>();
							
							if(RuleExeUtil.isDeviceChanged(zb)){
								List<PowerDevice> dyckglist =  RuleExeUtil.getTransformerSwitchLow(zb);
								List<PowerDevice> zyckglist =  RuleExeUtil.getTransformerSwitchMiddle(zb);
								List<PowerDevice> gyckglist =  RuleExeUtil.getTransformerSwitchHigh(zb);
								
								tempkgList.add(zb);
								tempkgList.addAll(dyckglist);
								tempkgList.addAll(zyckglist);
								tempkgList.addAll(gyckglist);
								
								zbnr += CZPService.getService().getDevName(tempkgList)+"、";
							}
						}
						
						replaceStr += "将"+zbnr+sbName+"及母线设备由热备用转冷备用/r/n";
					}else{
						replaceStr += CommonFunctionHH.getCzMotherLineDevStrReplace(tempList, curDev, curzb, stationName, "由热备用转冷备用");
					}
					
					if(CommonFunctionHH.getZbIsJdzybStrReplace(curzb)){
						replaceStr += "退出10kV#X接地站用变小电阻自投切功能/r/n";
					}
					
					replaceStr += CommonFunctionHH.getDqZbHbBhTMlkgStrReplace(curzb, "midlow", "退出","停电");
					
					replaceStr += CommonFunctionHH.getXzZbLxbhltxdStrReplace(curzb, "退出");
					
					if(CommonFunctionHH.getZbIsJdzybStrReplace(curzb)){
						replaceStr += "退出10kV#X接地变小电阻自投切功能/r/n";
					}
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
