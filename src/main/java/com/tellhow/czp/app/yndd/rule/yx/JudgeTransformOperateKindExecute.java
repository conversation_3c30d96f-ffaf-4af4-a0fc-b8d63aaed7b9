package com.tellhow.czp.app.yndd.rule.yx;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

public class JudgeTransformOperateKindExecute implements RulebaseInf {
	public static boolean isLoopClosing = true;
	
	@Override
	public boolean execute(RuleBaseMode rbm) {
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
		
		/*String[] arr = {"停电倒电","合环倒电"};
		
		int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), 
				"请选择主变倒电类型", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
		
		if(sel==0){
			isLoopClosing = false;
		}else if(sel==1){
			isLoopClosing = true;
		}else if(sel==-1){
			return false;
		}*/
		
		return true;
	}
}
