package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEKGBLYX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱开关并列运行".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName =  StringUtils.killVoltInDevName(voltStationName);
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> checkdeviceList = new ArrayList<PowerDevice>();
			List<PowerDevice> lkdeviceList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
					
					List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
					RuleExeUtil.swapLowDeviceList(gdList);
					for(PowerDevice gd : gdList) {
						if(RuleExeUtil.getDeviceEndStatus(gd).equals("1")){
							lkdeviceList.add(gd);
						}else if(!RuleExeUtil.isDeviceChanged(dev)){
							checkdeviceList.add(gd);
						}
					}
				}
				
				if (dev.getPowerVoltGrade() == 220){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						gycmlkgList.add(dev);
					}
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				replaceStr += voltStationName+"@检查"+CZPService.getService().getDevName(dev)+"运行正常/r/n";
			}
			
			String zbNums = "";
			
			RuleExeUtil.swapDeviceList(zbList);
			
			for(PowerDevice dev : zbList){
				zbNums += CZPService.getService().getDevNum(dev) + "、";
			}
			
			if(zbNums.endsWith("、")){
				zbNums = zbNums.substring(0, zbNums.length() - 1);
			}
			
			replaceStr += voltStationName+"@检查"+zbNums+"号主变220kV侧档位一致，并列运行正常/r/n";
			
			replaceStr += "普洱地调@遥控合上"+stationName+deviceName+"/r/n";
			
			for(PowerDevice dev : checkdeviceList){
				String devName = CZPService.getService().getDevName(dev); 
				replaceStr += voltStationName+"@检查"+devName+"在合上位置/r/n";
			}
			
			for(PowerDevice dev : lkdeviceList){
				String devName = CZPService.getService().getDevName(dev); 
				replaceStr += "普洱地调@遥控拉开"+stationName+devName+"/r/n";
			}
		}
		
		return replaceStr;
	}

}
