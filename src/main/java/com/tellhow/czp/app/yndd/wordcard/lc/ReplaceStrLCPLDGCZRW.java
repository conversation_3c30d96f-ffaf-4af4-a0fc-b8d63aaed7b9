package com.tellhow.czp.app.yndd.wordcard.lc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLCPLDGCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("临沧旁路代供操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev);

			List<PowerDevice> plkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)){
						mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, false, true, true);
						
						plkgList.add(dev);
					}
				}
			}
			
			replaceStr = stationName+deviceName+"转由"+CZPService.getService().getDevName(plkgList)+"联"+CZPService.getService().getDevName(mxList)+"代供";
		}
		
		return replaceStr;
	}

}
