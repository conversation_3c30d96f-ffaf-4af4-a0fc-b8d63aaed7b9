package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJDRQFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖电容器复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			
			String begin = CBSystemConstants.getCurRBM().getBeginStatus();
			String end = CBSystemConstants.getCurRBM().getEndState();
			
			if(begin.equals("2")){
				if(end.equals("1")){
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
						replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
					}
				}else if(end.equals("0")){
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
						replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
					}
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
						}
					}
				}
			}else if(begin.equals("1")){
				for(PowerDevice dev : kgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
