package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrJDYZBHHCZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("金刀营主变合环操作".equals(tempStr)){
			List<PowerDevice> zbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> gycmxList= new ArrayList<PowerDevice>();
			List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();

			if(zbzyckgList.size()>0){
				List<PowerDevice> kgList = getMlkgList(zbzyckgList.get(0));
				zycmlkgList.addAll(kgList);
			}
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			if(zbgyckgList.size()>0){
				gycmxList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				
				if(gycmxList.size()>0){
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					gycmlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}else{
				List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(curDev);

				gycmxList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.MotherLine, SystemConstants.Switch, false, true, true);
				
				if(gycmxList.size()>0){
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					
					for(PowerDevice dev : gycmxList){
						 List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
						 
						 gycmlkgList.addAll(tempList);
					}
				}else{
					gycmlkgList = RuleExeUtil.getDeviceList(kfList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}			
			
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
                DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
                
                if (gycmlkgList.contains(dtd.getTransDevice())&&dtd.getEndstate().equals("0")&&dtd.getBeginstatus().equals("1")) {
                	String temp = "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dtd.getTransDevice())+"/r/n";
                	
                	if(!replaceStr.contains(temp)){
                		replaceStr += temp;
                	}
                }else  if (xlkgList.contains(dtd.getTransDevice())&&dtd.getEndstate().equals("0")&&dtd.getBeginstatus().equals("1")) {
            		String temp = "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dtd.getTransDevice())+"/r/n";
                	
                	if(!replaceStr.contains(temp)){
                		replaceStr += temp;
                	}
                }
                
                if(!replaceStr.equals("")){
                	for(PowerDevice dev : zbzyckgList){
                    	replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
        			}
        			
        			for(PowerDevice dev : zbdyckgList){
                    	replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
        			}
        			
        			break;
                }
            }
			
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
                DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
                
                if (gycmlkgList.contains(dtd.getTransDevice())&&dtd.getEndstate().equals("0")&&dtd.getBeginstatus().equals("1")) {
                	String temp = "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dtd.getTransDevice())+"/r/n";
                	
                	if(!replaceStr.contains(temp)){
                		replaceStr += temp;
                	}
                }else  if (xlkgList.contains(dtd.getTransDevice())&&dtd.getEndstate().equals("0")&&dtd.getBeginstatus().equals("1")) {
            		String temp = "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dtd.getTransDevice())+"/r/n";
                	
                	if(!replaceStr.contains(temp)){
                		replaceStr += temp;
                	}
                }
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
	            	replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
			
		}
		return replaceStr;
	}
	
	public List<PowerDevice>  getMlkgList(PowerDevice pd){
		List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> kgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
		
		for(Iterator<PowerDevice> itor = kgList.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				
				if(tempmxList.size()<2){
					itor.remove();
				}else{
					for(PowerDevice tempmx : tempmxList){
						List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(tempmx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeKnifeQT, false, true, true, true);
						
						if(tempzbList.size() == 0){
							itor.remove();
						}
					}
				}
			}
		}
		
		if(kgList.size()==0){
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							kgList.add(dev);
						}
					}
				}
			}
		}
		
		mlkgList.addAll(kgList);
	
		return mlkgList;
	}
}
