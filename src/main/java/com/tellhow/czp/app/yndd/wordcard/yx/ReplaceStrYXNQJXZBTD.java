package com.tellhow.czp.app.yndd.wordcard.yx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYXNQJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("玉溪内桥接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(curDev);
			
			if(stationDev.getPowerStationID().equals("SS-64")){//35kV小石桥变特殊判断
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(stationDev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dev : dzList){
					if(dev.getPowerVoltGrade() == stationDev.getPowerVoltGrade()){
						List<PowerDevice> dz2List = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz2 : dz2List){
							zbgycdzList.add(dz2);
						}
						
						List<PowerDevice> gycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						
						for(PowerDevice mx : gycmxList){
							zbgyckgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
						}
					}
				}
			}
			
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycotherxlkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();

			boolean isSwitchControl = true;
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunction.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunction.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			}

			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(!zbgyckgList.contains(dev)){
							gycotherxlkgList.add(dev);
						}
					}
				}
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
					
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				otherzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(otherzxdjddzList);
				break;
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			for (Iterator<PowerDevice> it = zxdjddzList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == 35){
					it.remove();
				}
			}
			
			for (Iterator<PowerDevice> it = otherzxdjddzList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == 35){
					it.remove();
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
				
				if(!beginstatus.equals("0")){
					String status = RuleExeUtil.getStatusNew(dev.getDeviceType(), beginstatus);
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
				}
			}
			
			List<PowerDevice> zbzdyckgList = new ArrayList<PowerDevice>();
			
			if(dycmlkgList.size() == 0){
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						zbzdyckgList.add(dev);
					}
				}
			}
			
			if(zycmlkgList.size() == 0){
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						zbzdyckgList.add(dev);
					}
				}
			}
			
			if(zbzdyckgList.size() > 0){
				replaceStr += CommonFunction.getZybDrCheckContent(zbzdyckgList);
			}
			
			if(dycmlkgList.size() == 0){
				for(PowerDevice dev : dycmxList){
					if(dev.getPowerVoltGrade() == 10){
						for(PowerDevice zbdyckg : zbdyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
								replaceStr += "玉溪配调@核实"+stationName+CZPService.getService().getDevName(dev)+"可以停电/r/n";
							}
						}
					}
				}
			}
			
			if(zbList.size() == 1){//单主变
				replaceStr += stationName+"@退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				
				String zbgyckgstatus = "";
				
				for(PowerDevice dev : zbgyckgList){
					zbgyckgstatus = CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatus(dev.getDeviceStatus());
				}
				
				String gycmlkgstatus = "";
				
				for(PowerDevice dev : gycmlkgList){
					gycmlkgstatus = CZPService.getService().getDevNum(dev)+"断路器"+RuleExeUtil.getStatus(dev.getDeviceStatus());
				}
				
				replaceStr += stationName+"@将"+deviceName+"由运行转冷备用，"+gycmlkgstatus+"，"+zbgyckgstatus+"/r/n";
				
				for(PowerDevice dev : zbdyckgList){
					if(dev.getDeviceStatus().equals("2") && dev.getPowerVoltGrade() == 10){
						replaceStr += "玉溪配调@通知"+stationName+CZPService.getService().getDevName(dev)+"已处冷备用/r/n";
					}
				}
			}else{
				if(curDev.getPowerVoltGrade() < 220){
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){//存在合环操作
							replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
							
							for(PowerDevice gycxlkg : zbgyckgList){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("0")){
									replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(gycxlkg)+"/r/n";
								}
							}
						}
					}
				}
				
				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunction.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
				}
				
				if(otherzxdjddzList.size() > 0){
					replaceStr += CommonFunction.getZxdJddzOnCheckContent(otherzxdjddzList, stationName, station);
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(!RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){//不存在合环操作
						for(PowerDevice gycxlkg : zbgyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("0")){
								replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(gycxlkg)+"/r/n";
							}
						}
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
					}
				}

				for(PowerDevice zbdyckg : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
						replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
					}
				}

				for(PowerDevice zbzyckg : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(zbzyckg).equals("0")){
						replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(zbzyckg)+"/r/n";
					}
				}
				
				for(PowerDevice dev : gycotherxlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
					}
				}
				
				if(curDev.getPowerVoltGrade() < 220){
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
							replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
				
				if(isSwitchControl && isSwitchSeparateControl){
					replaceStr += "玉溪地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
				}else{
					if(station.getPowerVoltGrade() == 220){
						for(PowerDevice dev : zbgyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "玉溪地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					if(curDev.getDeviceStatus().equals("2")){
						for(PowerDevice dev : zbdyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunction.ifSwitchSeparateControl(dev)){
									replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
									
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									
									replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
								}else{
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
								}
								
								List<PowerDevice> dzList = CommonFunction.getTransformerKnife(curDev, dev);
								replaceStr += CommonFunction.getKnifeOffContent(dzList,stationName);
							}
						}
						
						for(PowerDevice dev : zbzyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								if(CommonFunction.ifSwitchSeparateControl(dev)){
									replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
									
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									
									replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
								}else{
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
								}
								
								List<PowerDevice> dzList = CommonFunction.getTransformerKnife(curDev, dev);
								replaceStr += CommonFunction.getKnifeOffContent(dzList,stationName);
							}
						}
						
						replaceStr += CommonFunction.getKnifeOffContent(zbgycdzList,stationName);
						
						for(PowerDevice dev : zbgyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								
								if(CommonFunction.ifSwitchSeparateControl(dev)){
									replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
									
									replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
								}else{
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
								}
								
								if(curDev.getPowerStationID().equals("SS-59")||curDev.getPowerStationID().equals("SS-64")){//110kV长春变、35kV小石桥特殊判断
									List<PowerDevice> linedzList = new ArrayList<PowerDevice>();
									
									for(PowerDevice dz : dzList){
										List<PowerDevice> dz3List = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);
										
										for(PowerDevice dz3 : dz3List){
											if(!dz3.getDeviceRunType().equals(CBSystemConstants.KindKnifeXC)){
												linedzList.add(dz3);
												break;
											}
										}
									}
									
									replaceStr += CommonFunction.getKnifeOffContent(linedzList, stationName);
								}
							}
						}
						
						for(PowerDevice dev : gycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

								if(CommonFunction.ifSwitchSeparateControl(dev)){
									replaceStr += "玉溪地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
									replaceStr += CommonFunction.getKnifeOffCheckContent(dzList, stationName);
								}else{
									replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
								}
								
								if(curDev.getPowerStationID().equals("SS-64")){//110kV长春变、35kV小石桥特殊判断
									for(PowerDevice dz : dzList){
										List<PowerDevice> dz3List = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);
										replaceStr += CommonFunction.getKnifeOffContent(dz3List, stationName);
										break;
									}
								}
							}
						}
					}
					
					if(station.getPowerVoltGrade() == 220){
						for(PowerDevice dev : zbgyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
						
						for(PowerDevice dev : gycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunction.getHhContent(dev, "玉溪地调", stationName);
							}
						}
					}else{
						for(PowerDevice dev : zbgyckgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
						
						for(PowerDevice dev : gycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "玉溪地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}
				}
				
				if(dycmlkgList.size() == 0){
					for(PowerDevice dev : dycmxList){
						if(dev.getPowerVoltGrade() == 10){
							for(PowerDevice zbdyckg : zbdyckgList){
								if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
									replaceStr += "玉溪配调@通知"+stationName+deviceName+"已处冷备用/r/n";
								}
							}
						}
					}
				}
				
				if(isSwitchControl && isSwitchSeparateControl){
					
				}else{
					if(zxdjddzList.size() > 0){
						replaceStr += CommonFunction.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						replaceStr += "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}