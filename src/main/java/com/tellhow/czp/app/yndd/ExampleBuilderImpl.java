package com.tellhow.czp.app.yndd;

import java.net.URL;

import javax.swing.JDialog;
import javax.swing.JSplitPane;

import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;

import com.tellhow.czp.ExampleBuilder;
import com.tellhow.czp.app.yndd.view.ConfigCardNextBHDialog;
import com.tellhow.czp.app.yndd.view.CustomZBDialog;
import com.tellhow.czp.app.yndd.view.OperateTicketChange;
import com.tellhow.czp.app.yndd.view.OperateTicketXTP;
import com.tellhow.czp.operationcard.OperateTicketSGP;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

/**
 * 
 * <AUTHOR>
 * 界面操作事件实现类   
 */

public class ExampleBuilderImpl extends ExampleBuilderYNDD {

	public ExampleBuilderImpl(URL url) throws GUIException {
		super(url);
	}

	@Override
	public void eventOccured(GUIEvent e) {
		super.eventOccured(e);
		String name = e.getName();
		try {
			if(name.equals("setPONum")){//设置票号
				ConfigCardNextBHDialog bhDialog = new ConfigCardNextBHDialog();
				bhDialog.setVisible(true);
			}
			
			if(name.equals("test")){//设置票号
				ConfigCardNextBHDialog bhDialog = new ConfigCardNextBHDialog();
				bhDialog.setVisible(true);
			}
			
			if(name.equals("newSGTicket")){
				if(TempTicket.getTempTicket()!=null){
					if(CBSystemConstants.roleCode.equals("0")){
						ShowMessage.view("请保存并关闭操作票！");
						return;
					}else{
						ShowMessage.view("请保存并关闭监控票！ ");
						return;
					}
				}
				JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
				splitPane.setDividerLocation(0.0);
				OperateTicketXTP xtp = new OperateTicketXTP();
				xtp.setVisible(true);
				splitPane.setRightComponent(xtp);
			}else if(name.equals("fastTicket")){
				OperateTicketChange aa = new OperateTicketChange(SystemConstants.getMainFrame(),false);
				aa.setVisible(true);
			}else if(name.equals("customCodex")){
				/*String sql = "";
				try {
					sql = "DROP TABLE "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER";
					DBManager.execute(sql);
				}catch (Exception et) {
					et.printStackTrace();
				}
				
				sql = "create table "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER(ID VARCHAR2(100) not null,LINE_ID   VARCHAR2(100),LINE_NAME  VARCHAR2(100),SWITCH_NAME  VARCHAR2(100),DISCONNECTOR_NAME  VARCHAR2(100),GROUNDDISCONNECTOR_NAME VARCHAR2(100),ENDPOINT_KIND  VARCHAR2(100),OPERATION_KIND  VARCHAR2(100),UNIT  VARCHAR2(100),VOLTAGE  VARCHAR2(100),LOWERUNIT  VARCHAR2(100),ISREMOVE  VARCHAR2(100),SOURCE  VARCHAR2(100))";
				DBManager.execute(sql);
				
				ShowMessage.view("修改成功！");
				
				try {
					sql = "DROP TABLE "+CBSystemConstants.opcardUser+"T_A_STATIONZYB";
					DBManager.execute(sql);
				}catch (Exception et) {
					et.printStackTrace();
				}
				
				sql = "create table "+CBSystemConstants.opcardUser+"T_A_STATIONZYB (ID  VARCHAR2(100) not null,DEV_ID  VARCHAR2(100),STATION_NAME VARCHAR2(100),DEV_NAME  VARCHAR2(100),ZYB_NAME     VARCHAR2(100),ZYB_DZNAME   VARCHAR2(100),ZYB_DEVID    VARCHAR2(100),STATION_ID   VARCHAR2(100))";
				DBManager.execute(sql);
				
				ShowMessage.view("修改成功！");*/
				
				/*try {
					String sql = "ALTER TABLE OPCARDWS.T_SK_OPERATION MODIFY CZZL VARCHAR2(500)";
					DBManager.execute(sql);
				}catch (Exception et) {
					et.printStackTrace();
				}
				
				ShowMessage.view("修改成功！");*/
			}
		}catch (Exception et) {
			et.printStackTrace();
		}
	}
}
