package com.tellhow.czp.app.yndd.wordcard.hh;


import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

import java.util.List;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrHHMXMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,PowerDevice stationDev,String desc) {
		String replaceStr = "";
		
		if("红河母线名称".equals(tempStr)){
			if (curDev.getDeviceType().equals(SystemConstants.MotherLine)) {
				// 主要针对特殊情况进行处理：两根母线由刀闸相连，他们会同时操作
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.MotherLine,
						SystemConstants.Switch, true, true, true);
				mxList.add(stationDev);
				// 排序
				RuleExeUtil.swapDeviceList(mxList);
				replaceStr = CZPService.getService().getDevName(mxList);
			}
		}
		return replaceStr;
	}

}
