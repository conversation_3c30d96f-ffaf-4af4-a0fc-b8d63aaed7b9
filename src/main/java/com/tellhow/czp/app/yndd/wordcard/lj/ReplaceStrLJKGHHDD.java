package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJKGHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江开关合环倒电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			List<PowerDevice> otherlineList = new ArrayList<PowerDevice>();
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, SystemConstants.Switch, true, true, true);
			
			List<PowerDevice> loopkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> unloopkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if (dev.getDeviceType().equals(SystemConstants.InOutLine)){
						if(!lineList.contains(dev)){
							otherlineList.add(dev);
						}
					}
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							loopkgList.add(dev);
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							unloopkgList.add(dev);
						}
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							loopkgList.add(dev);
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							unloopkgList.add(dev);
						}
					}
				}
			}
			
			for(PowerDevice dev : unloopkgList){
				replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
			}
			
			replaceStr += "丽江地调@落实"+CZPService.getService().getDevName(lineList)+"、"+CZPService.getService().getDevName(otherlineList)+"相关断面潮流满足合环要求/r/n";
			
			for(PowerDevice dev : loopkgList){
				replaceStr += CommonFunctionLJ.getHhContent(dev,"丽江地调",stationName);
			}
			
			
			for(PowerDevice dev : unloopkgList){
				String kgName = CZPService.getService().getDevName(dev); 

				replaceStr += "丽江地调@遥控断开"+stationName+kgName+"/r/n";
			}
		}
		
		return replaceStr;
	}

}
