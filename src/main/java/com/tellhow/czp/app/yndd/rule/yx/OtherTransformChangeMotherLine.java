package com.tellhow.czp.app.yndd.rule.yx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.yndd.rule.SwitchChangeMotherLineGZ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.SwitchChangeMotherLine;
import czprule.rule.view.EquipCheckChoose;
import czprule.rule.view.EquipChoose;
import czprule.system.CBSystemConstants;


public class OtherTransformChangeMotherLine implements RulebaseInf {
	public static Map<String,String> map = new HashMap<String,String>();
	
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		
		PowerDevice pd=rbm.getPd();
		
		if(pd==null)
			return false;
		
		map.clear();
		
		List<PowerDevice> allzbList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbgyckgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbzyckgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbdyckgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zbmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		
		for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = it2.next();
			if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
				List<PowerDevice> temp =  RuleExeUtil.getTransformerSwitchMiddle(dev);
				List<PowerDevice> temp1 =  RuleExeUtil.getTransformerSwitchLow(dev);
				
				zbzyckgList.addAll(temp);
				zbdyckgList.addAll(temp1);

				allzbList.add(dev);
			}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
				zbgyckgList.add(dev);
			}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				if((dev.getDeviceStatus().equals("1")||dev.getDeviceStatus().equals("2"))&&!dev.getPowerDeviceName().contains("相")){
					zbmlkgList.add(dev);
				}
			}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
				if((dev.getDeviceStatus().equals("0"))&&!dev.getPowerDeviceName().contains("相")){
					xlkgList.add(dev);
				}
			}
		}
		
		if(allzbList.size()<3){
			return true;
		}
		
		if(zbgyckgList.size()>0){
			List<PowerDevice> gylist =  RuleExeUtil.getTransformerSwitchHigh(pd);
			
			for (Iterator<PowerDevice> it2 = gylist.iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					it2.remove();
				}
			}
			
			if(gylist.size()>0){
				if(gylist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					List<PowerDevice> curmxlist =  RuleExeUtil.getDeviceList(gylist.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, false, true, true);
					boolean flag =false;
					
					for(PowerDevice zbkg : zbgyckgList){
						if(!zbkg.getPowerDeviceID().equals(gylist.get(0).getPowerDeviceID())){
							List<PowerDevice> mxlist =  RuleExeUtil.getDeviceList(zbkg, SystemConstants.MotherLine,  SystemConstants.PowerTransformer, false, true, true);
							
							if(mxlist.size()>0&&curmxlist.size()>0){
								if(!mxlist.get(0).getPowerDeviceID().equals(curmxlist.get(0).getPowerDeviceID())){
									flag = true;
								}else{
									flag = false;
									break;
								}
							}
						}
					}
					
					if(!flag){
						map.put("高压侧需要倒母", "false");
					}else{
						map.put("高压侧需要倒母", "true");
					}
				}
			}
		}else{
			map.put("高压侧需要倒母", "false");
		}
		
		List<PowerDevice> zylist =  RuleExeUtil.getTransformerSwitchMiddle(pd);
		
		if(zylist.size()>0){
			
			if(zylist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				List<PowerDevice> curmxlist =  RuleExeUtil.getDeviceList(zylist.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false,false, true, true);
				boolean flag =false;
				
				for(PowerDevice zbkg : zbzyckgList){
					if(!zbkg.getPowerDeviceID().equals(zylist.get(0).getPowerDeviceID())){
						List<PowerDevice> mxlist =  RuleExeUtil.getDeviceList(zbkg, SystemConstants.MotherLine,  SystemConstants.PowerTransformer, false, true, true);
						
						if(mxlist.size()>0&&curmxlist.size()>0){
							if(!mxlist.get(0).getPowerDeviceID().equals(curmxlist.get(0).getPowerDeviceID())){
								flag = true;
							}else{
								flag = false;
								break;
							}
						}
					}
				}
				
				if(!flag){
					map.put("中压侧需要倒母", "false");
				}else{
					map.put("中压侧需要倒母", "true");
				}
			}
		}else{
			map.put("中压侧需要倒母", "false");
		}
		
		List<PowerDevice> dylist =  RuleExeUtil.getTransformerSwitchLow(pd);
		
		if(dylist.size()>0){
			if(dylist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				List<PowerDevice> curmxlist =  RuleExeUtil.getDeviceList(dylist.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false,false, true, true);
				boolean flag =false;
				
				for(PowerDevice zbkg : zbdyckgList){
					if(!zbkg.getPowerDeviceID().equals(dylist.get(0).getPowerDeviceID())){
						List<PowerDevice> mxlist =  RuleExeUtil.getDeviceList(zbkg, SystemConstants.MotherLine,  SystemConstants.PowerTransformer, false, true, true);
						
						if(mxlist.size()>0&&curmxlist.size()>0){
							if(!mxlist.get(0).getPowerDeviceID().equals(curmxlist.get(0).getPowerDeviceID())){
								flag = true;
							}else{
								flag = false;
								break;
							}
						}
					}
				}
				
				if(!flag){
					map.put("低压侧需要倒母", "false");
				}else{
					map.put("低压侧需要倒母", "true");
				}
			}
		}else{
			map.put("低压侧需要倒母", "false");
		}
		
		if(!map.isEmpty()){
			boolean gycflag = true;
			boolean zycflag = true;
			boolean dycflag = true;

			if(map.containsKey("高压侧需要倒母")){
				if(map.get("高压侧需要倒母").equals("false")){
					gycflag = false;
				}
			}else{
				gycflag = false;
				map.put("高压侧需要倒母", "false");
			}
			
			if(map.containsKey("中压侧需要倒母")){
				if(map.get("中压侧需要倒母").equals("false")){
					zycflag = false;
				}
			}else{
				zycflag = false;
				map.put("中压侧需要倒母", "false");
			}
			
			if(map.containsKey("低压侧需要倒母")){
				if(map.get("低压侧需要倒母").equals("false")){
					dycflag = false;
				}
			}else{
				dycflag = false;
				map.put("低压侧需要倒母", "false");
			}
			
			if(!gycflag&&!zycflag&&!dycflag){
				return true;
			}
		}else{
			return true;
		}
		
		List<PowerDevice> zbList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, null,
				true, false, true);
		RuleExeUtil.swapDeviceList(zbList);
		if(zbList.size()>0){
			EquipChoose ecc = new EquipChoose(SystemConstants.getMainFrame(), true, zbList, "请选择需要倒母的主变：");
			
			List<PowerDevice> chooseEquipList = ecc.getChooseEquip();
			
			if(ecc.isCancel()){
				return false;
			}
			
			if(chooseEquipList.size()>0){
				PowerDevice  chooseEquip = chooseEquipList.get(0);
				
				SwitchChangeMotherLineGZ scml = new SwitchChangeMotherLineGZ();
				
				List<PowerDevice> switchs = RuleExeUtil.getDeviceList(chooseEquip, SystemConstants.Switch, "", true, true, true);
				for(int i=0;i<switchs.size();i++){
					if(!switchs.get(i).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						switchs.remove(i);
						i--;
					}
				}
						
				for(PowerDevice swDev : switchs){
					if(map.get("高压侧需要倒母").equals("true")&&RuleExeUtil.getTransformerSwitchHigh(chooseEquip).contains(swDev)){
						RuleBaseMode rbmKG = new RuleBaseMode();
						rbmKG.setPd(swDev);
						scml.execute(rbmKG);
						
						List<PowerDevice> mlswList = RuleExeUtil.getDeviceList(swDev, SystemConstants.Switch, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						for(PowerDevice mlsw:mlswList){
							if(mlsw.getDeviceStatus().equals("1")&&RuleExeUtil.isSwitchDoubleML(mlsw)){
								RuleExeUtil.deviceStatusExecute(mlsw,mlsw.getDeviceStatus(), "0");
								
								if(xlkgList.size()>1&&pd.getPowerVoltGrade() == 110){
									String showMessage="请选择用来解环的断路器";

									EquipCheckChoose ecc2=new EquipCheckChoose(SystemConstants.getMainFrame(), true, xlkgList , showMessage);
									List<PowerDevice> chooseEquips=ecc2.getChooseEquip();
									if(chooseEquips.size()==0)
										return false;
									
									PowerDevice dev = chooseEquips.get(0);
									RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
								}else if(xlkgList.size()==1){
									for(PowerDevice dev : xlkgList){
										RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
									}
								}
							}
						}
					}else if(map.get("中压侧需要倒母").equals("true")&&RuleExeUtil.getTransformerSwitchMiddle(chooseEquip).contains(swDev)){
						RuleBaseMode rbmKG = new RuleBaseMode();
						rbmKG.setPd(swDev);
						scml.execute(rbmKG);
						
						List<PowerDevice> mlswList = RuleExeUtil.getDeviceList(swDev, SystemConstants.Switch, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						for(PowerDevice mlsw:mlswList){
							if(mlsw.getDeviceStatus().equals("1")&&RuleExeUtil.isSwitchDoubleML(mlsw)){
								RuleExeUtil.deviceStatusExecute(mlsw,mlsw.getDeviceStatus(), "0");
							}
						}
					}else if(map.get("低压侧需要倒母").equals("true")&&RuleExeUtil.getTransformerSwitchLow(chooseEquip).contains(swDev)){
						RuleBaseMode rbmKG = new RuleBaseMode();
						rbmKG.setPd(swDev);
						scml.execute(rbmKG);
						
						List<PowerDevice> mlswList = RuleExeUtil.getDeviceList(swDev, SystemConstants.Switch, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						for(PowerDevice mlsw:mlswList){
							if(mlsw.getDeviceStatus().equals("1")&&RuleExeUtil.isSwitchDoubleML(mlsw)){
								RuleExeUtil.deviceStatusExecute(mlsw,mlsw.getDeviceStatus(), "0");
							}
						}
					}
				}
			}else{
				map.put("高压侧需要倒母", "false");
				map.put("中压侧需要倒母", "false");
				map.put("低压侧需要倒母", "false");
			}
		}
		return true;
	}
}
