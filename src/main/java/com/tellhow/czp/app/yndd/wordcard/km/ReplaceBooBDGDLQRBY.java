package com.tellhow.czp.app.yndd.wordcard.km;


import com.tellhow.czp.app.yndd.rule.km.JudgeSwitchBDGDLQZT;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooBDGDLQRBY implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("被代供断路器热备用".equals(tempStr)){
			return "热备用".equals(JudgeSwitchBDGDLQZT.flag);
		}
        return false;
	}
}
