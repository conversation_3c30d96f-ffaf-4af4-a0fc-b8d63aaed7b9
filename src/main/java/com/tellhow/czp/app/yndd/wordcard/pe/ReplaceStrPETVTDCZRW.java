package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPETVTDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱TV停电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> dzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeQT, "", true, true, true, true);
			
			PowerDevice device = new PowerDevice(); 
			
			for(PowerDevice dev : dzList){
				String dzNum = CZPService.getService().getDevNum(dev);
				
				if(dzNum.contains("901") || dzNum.contains("902")){
					device = dev;
				}
			}
			
			if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
				if(CommonFunctionPE.ifSwitchSeparateControl(device)){
					replaceStr = stationName+deviceName+"线路TV由运行转冷备用";
				}else{
					replaceStr = "将"+deviceName+"线路TV由运行转冷备用";
				}
			}else{
				if(CommonFunctionPE.ifSwitchSeparateControl(device)){
					replaceStr = stationName+deviceName+"TV由运行转冷备用";
				}else{
					replaceStr = "将"+deviceName+"TV由运行转冷备用";
				}
			}
		}
		
		return replaceStr;
	}

}
