package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.km.LoneMotherLineExecute;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;
import czprule.wordcard.replaceclass.impl.ReplaceBooDMJX;

public class ReplaceStrKMDMJXMXTD implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";

		if("昆明单母接线母线停电".equals(tempStr)) {
			String result = "";
			PowerDevice hskg = new PowerDevice();
			PowerDevice dkkg = new PowerDevice();

			if(LoneMotherLineExecute.tagMap != null){
				if(LoneMotherLineExecute.tagMap.size()>0){
					result = LoneMotherLineExecute.tagMap.get("是否合环");
					hskg = CBSystemConstants.getPowerDevice(LoneMotherLineExecute.tagMap.get("合环开关"));
					dkkg = CBSystemConstants.getPowerDevice(LoneMotherLineExecute.tagMap.get("断开开关"));
				}
			}
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			String stationName = CZPService.getService().getDevName(station);
			
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "", true, true, true);
			
			if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
				for(Iterator<PowerDevice> itor = zbList.iterator();itor.hasNext();){
					PowerDevice zb = itor.next();
					
					if(!RuleExeUtil.isDeviceChanged(zb)){
						itor.remove();
					}
				}
			}
			
			RuleExeUtil.sortListByDevName(zbList);
			
			if(zbList.size()>0){
				PowerDevice powertransformer = zbList.get(0);
				
				List<PowerDevice> gycswList = new ArrayList<PowerDevice>();
				List<PowerDevice> zycswList = new ArrayList<PowerDevice>();
				List<PowerDevice> dycswList = new ArrayList<PowerDevice>();
				
				
				if(zbList.size()>1){
					for(PowerDevice zb : zbList){
						gycswList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
						zycswList.addAll(RuleExeUtil.getTransformerSwitchMiddle(zb));
						dycswList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
					}
				}else{
					gycswList = RuleExeUtil.getTransformerSwitchHigh(zbList.get(0));
					zycswList = RuleExeUtil.getTransformerSwitchMiddle(zbList.get(0));
					dycswList = RuleExeUtil.getTransformerSwitchLow(zbList.get(0));
				}

				PowerDevice gycmlkg = new PowerDevice();
				PowerDevice zycmlkg = new PowerDevice();
				PowerDevice dycmlkg = new PowerDevice();

				List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();

				for(PowerDevice gycsw : gycswList){
					List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(gycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					gycmlkgList.addAll(tempList);
				}
				
				for(PowerDevice zycsw : zycswList){
					List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(zycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					zycmlkgList.addAll(tempList);
				}
				
				for(PowerDevice dycsw : dycswList){
					List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(dycsw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					dycmlkgList.addAll(tempList);
				}
				
			 	List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

				for(Iterator<PowerDevice> itor = xlkgList.iterator();itor.hasNext();){
					PowerDevice xlkg = itor.next();
					
					if(xlkg.getPowerDeviceName().contains("备用")){
						itor.remove();
					}
				}
			 	
				if(gycmlkgList.size()>0){
					gycmlkg = gycmlkgList.get(0);
				}
				
				if(zycmlkgList.size()>0){
					zycmlkg = zycmlkgList.get(0);
				}
				
				if(dycmlkgList.size()>0){
					dycmlkg = dycmlkgList.get(0);
				}
				
				if(!dycmlkg.getPowerDeviceID().equals("")){
					if(curDev.getPowerVoltGrade() == dycmlkg.getPowerVoltGrade()){
						dycmlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
						dycmlkg = dycmlkgList.get(0);
					}
				}
				
				if(curDev.getPowerVoltGrade() < station.getPowerVoltGrade() ){//母线是负荷侧
					if(curDev.getPowerVoltGrade() == 10||curDev.getPowerVoltGrade() == 6){
						ReplaceBooDMJX dmjx = new ReplaceBooDMJX();
						
						if(xlkgList.size()>0){
							replaceStr +=  "配网调度@落实"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(curDev)+"各出线已转热备用/r/n";
						}
						
						if(com.tellhow.czp.app.yndd.rule.RuleExeUtil.isTransformerXBDY(powertransformer)||RuleExeUtil.isTransformerXBZ(powertransformer)){//线变组或者是线变单元接线
							List<PowerDevice> drdkList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, false, true);
							
						 	List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, false, true);
							
							if(result.equals("能合环")){
					 			replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
					 			
					 			for(PowerDevice drdk : drdkList){
							 		if(RuleExeUtil.getDeviceBeginStatus(drdk).equals("0")){
							 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(drdk)+"/r/n";
							 		}
							 	}
							 	
							 	for(PowerDevice zyb : zybList){
							 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
								 		if(RuleExeUtil.getDeviceBeginStatus(zyb).equals("0")){
								 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
								 		}
							 		}
							 	}
					 			
					 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(hskg)+"/r/n";
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
							}else if(result.equals("不能合环")){
								for(PowerDevice drdk : drdkList){
							 		if(RuleExeUtil.getDeviceBeginStatus(drdk).equals("0")){
							 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(drdk)+"/r/n";
							 		}
							 	}
							 	
							 	for(PowerDevice zyb : zybList){
							 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
								 		if(RuleExeUtil.getDeviceBeginStatus(zyb).equals("0")){
								 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
								 		}
							 		}
							 	}
								
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
								replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(hskg)+"/r/n";
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
							}else{
								for(PowerDevice drdk : drdkList){
							 		if(RuleExeUtil.getDeviceBeginStatus(drdk).equals("0")){
							 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(drdk)+"/r/n";
							 		}
							 	}
							 	
							 	for(PowerDevice zyb : zybList){
							 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
								 		if(RuleExeUtil.getDeviceBeginStatus(zyb).equals("0")){
								 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
								 		}
							 		}
							 	}
								
								if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("0")){
						 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
								}
					 			
					 			for(PowerDevice dycsw : dycswList){
					 				if(RuleExeUtil.getDeviceBeginStatus(dycsw).equals("0")){
							 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycsw)+"/r/n";
					 				}
					 			}
							}
							
							
					 		if(!dycmlkg.getPowerDeviceID().equals("")){
					 			replaceStr +=  "退出"+CZPService.getService().getDevName(dycmlkg)+"备自投装置/r/n";
							}
					 		
					 		if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
					 		}
						}else if(dmjx.strReplace("单母接线", curDev, stationDev, desc)){
							List<PowerDevice> drdkList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, false, true);
							
						 	List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, false, true);
						 	
						 	for(PowerDevice drdk : drdkList){
						 		if(RuleExeUtil.getDeviceBeginStatus(drdk).equals("0")){
						 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(drdk)+"/r/n";
						 		}
						 	}
						 	
						 	for(PowerDevice zyb : zybList){
						 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
							 		if(RuleExeUtil.getDeviceBeginStatus(zyb).equals("0")){
							 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
							 		}
						 		}
						 	}
						 	
							for(PowerDevice dycsw : dycswList){
								if(RuleExeUtil.getDeviceBeginStatus(dycsw).equals("0")){
						 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycsw)+"/r/n";
								}
				 			}
						 	
							for(PowerDevice gycsw : gycswList){
								if(RuleExeUtil.getDeviceEndStatus(gycsw).equals("1")){
						 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycsw)+"/r/n";
								}else if(RuleExeUtil.getDeviceEndStatus(gycsw).equals("2")){
						 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycsw)+"/r/n";
								}
				 			}
						 	
													
				 			for(PowerDevice gycsw : gycswList){
								if(RuleExeUtil.getDeviceEndStatus(gycsw).equals("2")){
						 			replaceStr += "将"+CZPService.getService().getDevName(gycsw)+"由热备用转冷备用/r/n";
								}
				 			}
				 			
				 			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
					 		}	
						}else{
						 	List<PowerDevice> drdkList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, false, true);
							
						 	List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSwitchZYB+","+CBSystemConstants.RunTypeSwitchJDB, "", false, true, false, true);
						 	
						 	for(PowerDevice drdk : drdkList){
						 		if(RuleExeUtil.getDeviceBeginStatus(drdk).equals("0")){
						 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(drdk)+"/r/n";
						 		}
						 	}
						 	
						 	for(PowerDevice zyb : zybList){
						 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
							 		if(RuleExeUtil.getDeviceBeginStatus(zyb).equals("0")){
							 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
							 		}
						 		}
						 	}
						 	
						 	for(PowerDevice dycsw : dycswList){
						 		if(RuleExeUtil.getDeviceBeginStatus(dycsw).equals("0")){
						 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycsw)+"/r/n";
						 		}
				 			}
						 	
					 		if(!dycmlkg.getPowerDeviceID().equals("")){
								if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
						 			replaceStr +=  "退出"+(int)dycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
								}
							}
							
					 		if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("0")){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
							}
					 		
							if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
					 		}						
						}
					}else if(curDev.getPowerVoltGrade() == 35){
						List<PowerDevice> dqmlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);

						for(PowerDevice dqmlkg : dqmlkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dqmlkg).equals("1")){
								List<PowerDevice> dqxlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+ CBSystemConstants.RunTypeSwitchQT, "", false, true, false, true);

								for(PowerDevice dqxlkg : dqxlkgList){
							 		if(RuleExeUtil.getDeviceBeginStatus(dqxlkg).equals("0")){
							 			String temp = "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dqxlkg)+"/r/n";

							 			if(!replaceStr.contains(temp)){
							 				replaceStr += temp;
							 			}
							 		}
								}
								
								List<PowerDevice> dqzbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, false, true);

								for(PowerDevice dqzbkg : dqzbkgList){
							 		if(RuleExeUtil.getDeviceBeginStatus(dqzbkg).equals("0")){
							 			String temp = "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dqzbkg)+"/r/n";
							 			
							 			if(!replaceStr.contains(temp)){
							 				replaceStr += temp;
							 			}
							 		}
								}
							}
						}
						
						if(!dycmlkg.getPowerDeviceID().equals("")){
				 			replaceStr +=  "退出35kV备自投装置/r/n";
						}
						
						if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
				 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
				 		}					
					}
				}else{
					if(gycmlkg.getPowerDeviceID().equals("")){
						List<PowerDevice>  dycmxList = new ArrayList<PowerDevice>();

						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&dev.getPowerVoltGrade() == 10){
								dycmxList.add(dev);
							}
						}
						
						if(dycmxList.size()==1){
				 			replaceStr +=  "配网调度@落实"+stationName+"10kV母线具备停电条件/r/n";
						}else if(dycmxList.size()==2){
							String num = "";
							
							for(PowerDevice dev : dycmxList){
								num += CZPService.getService().getDevNum(dev)+"、";
							}
							
							if(num.endsWith("、")){
								num = num.substring(0,num.length()-1);
							}
							
				 			replaceStr +=  "配网调度@落实"+stationName+"10kV"+num+"段母线具备停电条件/r/n";
						}
						
						
						for(PowerDevice dycmx : dycmxList){
							List<PowerDevice> drswList = RuleExeUtil.getDeviceList(dycmx, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDR,"",false,true, true, true);
						
							for(PowerDevice drsw : drswList){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(drsw)+"/r/n";
				 			}
						}
						
					
			 			
			 			for(PowerDevice dycsw : dycswList){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycsw)+"/r/n";
			 			}		 			
			 			
			 			if(RuleExeUtil.getDeviceEndStatus(dycmlkg).equals("1")){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
			 			}
			 			
						for(PowerDevice zycsw : zycswList){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(zycsw)+"/r/n";
			 			}
						
						for(PowerDevice gycsw : gycswList){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycsw)+"/r/n";
			 			}	
			 			
						
						if(xlkgList.size()>0){
			 				for(PowerDevice xlkg : xlkgList){
			 					if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")&&!dkkg.getPowerDeviceID().equals(xlkg.getPowerDeviceID())){
			 			 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkg)+"/r/n";
			 					}
			 				}
			 			}

						if(dycmxList.size()==1){
							for(PowerDevice dycsw : dycswList){
					 			if(RuleExeUtil.getDeviceEndStatus(dycsw).equals("2")){
									List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dycsw, SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);

                                    for (PowerDevice temp : tempList) {
                                        replaceStr += "将"+CZPService.getService().getDevName(temp)+"由热备用转冷备用/r/n";
                                    }
                                }
				 			}
						}

						if(!dycmlkg.getPowerDeviceID().equals("")){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycmlkg).equals("1")){
					 			replaceStr +=  "退出"+(int)dycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
						
						if(xlkgList.size()>1){
				 			replaceStr +=  "退出35kV备自投装置/r/n";
						}
											
//						if(dycmxList.size()==2){
//							for(PowerDevice dycsw : dycswList){
//					 			if(RuleExeUtil.getDeviceEndStatus(dycsw).equals("2")){
//						 			replaceStr += "将"+CZPService.getService().getDevName(dycsw)+"由热备用转冷备用/r/n";
//					 			}
//				 			}
//						}
						
						for(PowerDevice dev : zbList){
							List<PowerDevice> zbdyckgList =  RuleExeUtil.getTransformerSwitchLow(dev);
							
							for(PowerDevice zbdyckg : zbdyckgList){
								if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("2")){
						 			replaceStr += "将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					 			}
							}
			 			}
						
						if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
				 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
				 		}	
					}else{
						if(powertransformer.getPowerVoltGrade()>35){
							List<PowerDevice> gdList = RuleExeUtil.getDeviceList(powertransformer, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

							if(gdList.size()>0){
								if(gdList.size() > 0){
                                    for (PowerDevice gd : gdList) {
                                        replaceStr += "落实"+CZPService.getService().getDevName(gd)+"处合位/r/n";
                                    }
                                }
							}
						}
						
						if(result.equals("能合环")){
				 			replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
				 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(hskg)+"/r/n";
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
				 			
				 			
				 			if(!zycmlkg.getPowerDeviceID().equals("")){
					 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zycmlkg)+"/r/n";
							}

							if(!dycmlkg.getPowerDeviceID().equals("")){
					 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
							}
							
							for(PowerDevice dycsw : dycswList){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycsw)+"/r/n";
				 			}		 			
				 			
							for(PowerDevice zycsw : zycswList){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(zycsw)+"/r/n";
				 			}
							
							for(PowerDevice gycsw : gycswList){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycsw)+"/r/n";
				 			}		 			
				 			if(xlkgList.size()>0){
				 				for(PowerDevice xlkg : xlkgList){
				 					if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")&&!dkkg.getPowerDeviceID().equals(xlkg.getPowerDeviceID())){
				 			 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkg)+"/r/n";
				 					}
				 				}
				 			}
				 			
				 			if(!gycmlkg.getPowerDeviceID().equals("")){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
				 			}
						}else if(result.equals("不能合环")){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycswList)+"/r/n";
				 			
				 			for(PowerDevice xlkg : xlkgList){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkg)+"/r/n";
				 			}
						}else{
				 			if(!zycmlkg.getPowerDeviceID().equals("")){
					 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(zycmlkg)+"/r/n";
							}

							if(!dycmlkg.getPowerDeviceID().equals("")){
					 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dycmlkg)+"/r/n";
							}
							
							for(PowerDevice dycsw : dycswList){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dycsw)+"/r/n";
				 			}		 			
				 			
							for(PowerDevice zycsw : zycswList){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(zycsw)+"/r/n";
				 			}
							
							for(PowerDevice gycsw : gycswList){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycsw)+"/r/n";
				 			}		 			
				 			if(xlkgList.size()>0){
				 				for(PowerDevice xlkg : xlkgList){
				 					if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")&&!dkkg.getPowerDeviceID().equals(xlkg.getPowerDeviceID())){
				 			 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkg)+"/r/n";
				 					}
				 				}
				 			}
				 			
				 			if(!gycmlkg.getPowerDeviceID().equals("")){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
				 			}
						}
						
						if(!dycmlkg.getPowerDeviceID().equals("")){
							if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
					 			replaceStr +=  "退出"+(int)dycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
						
						for(PowerDevice dev : zycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					 			replaceStr +=  "退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
						
			 			replaceStr +=  "退出"+(int)gycmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
						
			 			for(PowerDevice dycsw : dycswList){
				 			if(RuleExeUtil.getDeviceEndStatus(dycsw).equals("2")){
								List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dycsw, SystemConstants.PowerTransformer, SystemConstants.MotherLine, true, true, true);
				 				
					 			replaceStr += "将"+CZPService.getService().getDevName(tempList)+"由热备用转冷备用/r/n";
				 			}
			 			}
			 			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
				 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
				 		}
					}
				}
			}else{
				if(curDev.getPowerVoltGrade() < station.getPowerVoltGrade()){//负荷侧
					if(curDev.getPowerVoltGrade() == 10){
						replaceStr +=  "配网调度@落实"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(curDev)+"各出线已转热备用/r/n";
					}else if(curDev.getPowerVoltGrade() > 10){
					 	List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true);
					 	
					 	for(PowerDevice dev : xlkgList){
					 		if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					 		}
					 	}
					}
					
				 	List<PowerDevice> drdkList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, false, true);
					
				 	List<PowerDevice> zybList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, false, true);
				 	
				 	for(PowerDevice drdk : drdkList){
				 		if(RuleExeUtil.getDeviceBeginStatus(drdk).equals("0")){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(drdk)+"/r/n";
				 		}
				 	}
				 	
				 	for(PowerDevice zyb : zybList){
				 		if(zyb.getPowerDeviceName().contains("站用变")||zyb.getPowerDeviceName().contains("接地变")){
					 		if(RuleExeUtil.getDeviceBeginStatus(zyb).equals("0")){
					 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(zyb)+"/r/n";
					 		}
				 		}
				 	}
				 	
					List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

					
					if(mlkgList.size()>0){
						for(PowerDevice mlkg : mlkgList){
							if(!mlkg.getPowerDeviceName().contains("002")&&!mlkg.getPowerDeviceName().contains("002")){
								if(RuleExeUtil.getDeviceBeginStatus(mlkg).equals("0")){
						 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
						 		}
							}
						}
						
						for(PowerDevice mlkg : mlkgList){
							List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(mlkg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
							
							if(mlkg.getPowerDeviceName().contains("002")||mlkg.getPowerDeviceName().contains("002")){
								if(RuleExeUtil.getDeviceBeginStatus(mlkg).equals("0")){
						 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
						 		}
							}
							
							if(tempList.size()>0){
								for(PowerDevice temp : tempList){
									if(RuleExeUtil.getDeviceBeginStatus(temp).equals("0")){
							 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(temp)+"/r/n";
									}
								}
							}
						}
					}
					
					if(CZPService.getService().getDevNum(curDev).equals("Ⅲ")||CZPService.getService().getDevNum(curDev).equals("Ⅱ")){
			 			replaceStr +=  "退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
		 			
		 			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
		 				for(PowerDevice mlkg : mlkgList){
							List<PowerDevice> tempList =  RuleExeUtil.getDeviceList(mlkg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
							
							if(tempList.size()>0){
								for(PowerDevice temp : tempList){
									if(RuleExeUtil.getDeviceEndStatus(temp).equals("2")){
							 			replaceStr += "将"+CZPService.getService().getDevName(temp)+"由热备用转冷备用/r/n";
									}
								}
							}
						}
		 				
			 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
			 		}
				}else{
					List<PowerDevice> gycmlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				 	List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					List<PowerDevice> gyczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true,  true, true);
				 	
					if(result.equals("能合环")){
			 			replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
			 			replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(hskg)+"/r/n";
			 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
					}else if(result.equals("不能合环")){
						PowerDevice zb = new PowerDevice();
						
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
								zb = dev;
								break;
							}
						}
						
						List<PowerDevice>  zbzdyckgList =  RuleExeUtil.getTransformerSwitchLoad(zb);
						
						for(PowerDevice zbzdyckg : zbzdyckgList){
							replaceStr +=  "退出"+(int)zbzdyckg.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
						
						if(dkkg!=null){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dkkg)+"/r/n";
						}
						
						if(hskg!=null){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(hskg)+"/r/n";
						}
					}
					
					List<PowerDevice>  ymxList = new ArrayList<PowerDevice>(); 
					List<PowerDevice>  tagmxList = new ArrayList<PowerDevice>(); 
					
					for(PowerDevice gyczbkg : gyczbkgList){
						List<PowerDevice>  dzList =  RuleExeUtil.getDeviceDirectList(gyczbkg, SystemConstants.SwitchSeparate);
						
						for(PowerDevice dz : dzList){
							if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
								ymxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
							}else if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
								tagmxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
							}
						}
					}
					
					if(ymxList.size()>0&&tagmxList.size()>0){
 			 			replaceStr += "将"+CZPService.getService().getDevName(gyczbkgList)+"由"+CZPService.getService().getDevName(ymxList)+"运行倒至"+CZPService.getService().getDevName(tagmxList)+"运行/r/n";
					}
					
	 				for(PowerDevice xlkg : xlkgList){
	 					if(dkkg!=null){
	 						if(RuleExeUtil.getDeviceBeginStatus(xlkg).equals("0")&&!dkkg.getPowerDeviceID().equals(xlkg.getPowerDeviceID())){
		 			 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkg)+"/r/n";
		 					}
	 					}
	 				}
		 			
	 				for(PowerDevice gycmlkg : gycmlkgList){
	 					if(RuleExeUtil.isDeviceHadStatus(gycmlkg, "0", "1")){
				 			replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
	 					}
	 				}
					
		 			replaceStr +=  "退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
		 			
					if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
			 			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
			 		}
				}
	 		}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
