package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.JDKGXZPE;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEXLTDBZ  implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱线路停电备注".equals(tempStr)){
			int number = 1;
			
			replaceStr = number+"、操作前已对设备状态进行核对确认，操作人员已到达现场，经现场勘查认定具备操作条件。";
			
			String otherContent = "";
			
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			List<Map<String, String>> stationLineList = CommonFunctionPE.getStationLineList(curDev);
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchSeparate);
				
				if(JDKGXZPE.chooseEquips.contains(sourceLineTrans) && jddzList.size()>0){
					number++;
					
					String jddzName = CZPService.getService().getDevName(jddzList.get(0));
					
					otherContent += number+"、操作前已核实后续有工作需对"+jddzName+"试分合，"
							+ "故采用三相短路接地线代替"+jddzName+"。";
				}
			}
			
			for(PowerDevice loadLineTran : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
				String voltStationName = CZPService.getService().getDevName(station); 
				
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(loadLineTran, SystemConstants.SwitchFlowGroundLine);
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(loadLineTran, SystemConstants.SwitchSeparate);

				if(JDKGXZPE.chooseEquips.contains(loadLineTran) && jddzList.size()>0){
					number++;
					
					String jddzName = CZPService.getService().getDevName(jddzList.get(0));
					
					otherContent += number+"、操作前已核实后续有工作需对"+jddzName+"试分合，"
							+ "故采用三相短路接地线代替"+jddzName+"。";
				}
			}
			
			for(Map<String,String> map : stationLineList){
				String unit = StringUtils.ObjToString(map.get("UNIT"));
				String linename = StringUtils.ObjToString(map.get("LINE_NAME"));
				String switchname = StringUtils.ObjToString(map.get("SWITCH_NAME"));
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
				String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND"));
				String disconnectorname = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME"));
				String grounddisconnectorname = StringUtils.ObjToString(map.get("GROUNDDISCONNECTOR_NAME"));
				String endpointtype = StringUtils.ObjToString(map.get("ENDPOINT_TYPE"));

				for(PowerDevice dev : JDKGXZPE.chooseEquips){
					if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
						number++;
						
						otherContent += number+"、操作前已核实后续有工作需对"+grounddisconnectorname+"试分合，"
								+ "故采用三相短路接地线代替"+grounddisconnectorname+"。";
					 }
				}
			}

			if(otherContent.equals("")){
				replaceStr = replaceStr.replace("1、", "");
			}else{
				replaceStr += otherContent;
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
