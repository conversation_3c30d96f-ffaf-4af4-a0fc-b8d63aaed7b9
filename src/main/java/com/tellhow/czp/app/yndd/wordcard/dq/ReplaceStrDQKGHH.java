package com.tellhow.czp.app.yndd.wordcard.dq;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQKGHH  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆开关合环".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			if(curDev.getDeviceStatus().equals("0")){
				replaceStr += stationName+"@退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				replaceStr += CommonFunctionDQ.getHhContent(curDev, "迪庆地调",stationName);
			}
		}
		
		return replaceStr;
	}

}
