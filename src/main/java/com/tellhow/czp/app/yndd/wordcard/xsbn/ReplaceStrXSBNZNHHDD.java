package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.xsbn.XSBNChangePowerExecute;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNZNHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳站内合环调电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			
			if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
				PowerDevice powercutLine = new PowerDevice();
				PowerDevice powerLine = new PowerDevice();

				for(PowerDevice dev : XSBNChangePowerExecute.powerOffLineList){
					powercutLine = dev;
				}
				
				for(PowerDevice dev : XSBNChangePowerExecute.powerOnLineList){
					powerLine = dev;
				}
				
				String powercutLineName = CZPService.getService().getDevName(powercutLine);
				String powerLineName = CZPService.getService().getDevName(powerLine);

				replaceStr += "版纳地调@确认XXkVXX线（XX→XX）+ XXkVXX线（XX→XX）负荷 ≤ XXMW/r/n";
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += stationName+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				replaceStr += "版纳地调@执行"+stationName+deviceName+"由"+powercutLineName+"供电转由"+powerLineName+"供电程序操作/r/n";
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += stationName+"@退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
			}else{
				PowerDevice powerOnZb = new PowerDevice();
				PowerDevice powerOffZb = new PowerDevice();
				
				for(PowerDevice dev : XSBNChangePowerExecute.powerOffZbList){
					powerOffZb = dev;
				}
				
				for(PowerDevice dev : XSBNChangePowerExecute.powerOnZbList){
					powerOnZb = dev;
				}
				
				replaceStr += "版纳地调@确认XXkVXX线（XX→XX）+ XXkVXX线（XX→XX）负荷 ≤ XXMW/r/n";
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += stationName+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}
				
				replaceStr += "版纳地调@执行"+stationName+deviceName+"由"+CZPService.getService().getDevName(powerOffZb)+"供电转由"+CZPService.getService().getDevName(powerOnZb)+"供电程序操作/r/n";
				
				if(curDev.getPowerVoltGrade() == 10){
					replaceStr += stationName+"@将XX主变定值区由XX区切换至XX区/r/n";
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += stationName+"@退出"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}

			}
		}
		
		return replaceStr;
	}

}
