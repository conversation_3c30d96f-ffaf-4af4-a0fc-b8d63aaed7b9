package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.PowerDevice;
import czprule.rule.model.CardWordMode;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;

public class GetDtdDataKM extends GetDtdData{
	public static ArrayList<String[]> getData(List<CardItemModel> itemModelsShow,String ddname){
		ArrayList<String[]> data = new ArrayList<String[]>();
		modelList = DBManager.queryForList(CommonUtils.WORD_RBM_SQL);
		CommonFunctionKM.checkResult = true;
		
		for(CardItemModel cim : itemModelsShow){
			String cznr = cim.getCardDesc();
			String stationid = cim.getCzdwID();
			String stationname = cim.getStationName();
			String bdzname = cim.getBdzName();
			String uuids = cim.getUuIds();

			String isyk = "1";
			String devname = "";
			String startZT = "";
			String endZT= "";
			String deviceType= "";
			
			List<RuleBaseMode> rbmList = getRBMList(stationname,cznr);
			
			String czname = "";
			
			if(rbmList.size()>0){
				PowerDevice dev = rbmList.get(0).getPd();
				
				if(!rbmList.get(0).getCheckout()){
					List<String> messageList = rbmList.get(0).getMessageList();
					
					for(String message : messageList){
						ShowMessage.viewWarning(SystemConstants.getMainFrame(), message);
					}
					
					CommonFunctionKM.checkResult = false;
					
					data.clear();
					break;
				}
				
				if(dev!=null && !dev.getPowerDeviceID().equals("")){
					String begin = rbmList.get(0).getBeginStatus();
					String end = rbmList.get(0).getEndState();
					
					if(!dev.getPowerStationID().equals("")){
						PowerDevice stationDev = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						czname = CZPService.getService().getDevName(stationDev);
						devname = dev.getPowerDeviceName();
					}else{
						czname = stationname;
						
						if(czname.equals("昆明地调")){
							czname = bdzname;
						}
					}
					
					if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
						if(rbmList.get(0).getOperaTion().contains("地线")){
							data.add(new String[]{"","",czname,czname,cznr,isyk,"",devname,startZT,endZT,deviceType,uuids});
						}else if(!end.equals("")&&!begin.equals("")){
							List<PowerDevice> swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								List<PowerDevice> newSwList = new ArrayList<PowerDevice>();
								
								for(PowerDevice sw : swList){
									if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
										if(RuleExeUtil.isSwMiddleInThreeSecond(sw)){
											newSwList.add(0, sw);
										}else{
											newSwList.add(sw);
										}
									}else{
										newSwList.add(sw);
									}
								}
								
								if((begin.equals("0")&&end.equals("1"))){
									for(PowerDevice kg : newSwList){
										String devid = kg.getPowerDeviceID();
										cznr  =  "遥控断开"+czname+CZPService.getService().getDevName(kg);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
									}
								}else if((begin.equals("1")&&end.equals("2"))){
									getSwitchSequenceTdMerge(newSwList, data, uuids, czname, ddname);
								}
							}else{
								List<PowerDevice> newSwList = new ArrayList<PowerDevice>();
								
								for(PowerDevice sw : swList){
									if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
										if(!RuleExeUtil.isSwMiddleInThreeSecond(sw)){
											newSwList.add(0, sw);
										}else{
											newSwList.add(sw);
										}
									}else{
										newSwList.add(sw);
									}
								}
								
								if((begin.equals("1")&&end.equals("0"))){
									for(PowerDevice kg : newSwList){
										String devid = kg.getPowerDeviceID();
										cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(kg);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									}
								}else if((begin.equals("2")&&end.equals("1"))){
									getSwitchSequenceFdMerge(newSwList, data, uuids, czname, ddname);
								}
							}
						}
					}
					else if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
						if(rbmList.get(0).getOperaTion().contains("母线倒母")){
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							continue;
						}
						
						List<PowerDevice> swList = new ArrayList<PowerDevice>();
						List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT,"",true, true, true, true);
						
						//母线为高压侧，且为单电源单刀闸情况
						List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXLS,"",true, true, true, true);

						if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
							swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchFHC,false, true, true, true);
						}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
							swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						}
						
						List<PowerDevice> drswList = new ArrayList<PowerDevice>();
						List<PowerDevice> dkswList = new ArrayList<PowerDevice>();
						List<PowerDevice> xlswList = new ArrayList<PowerDevice>();
						List<PowerDevice> jdbswList = new ArrayList<PowerDevice>();
						List<PowerDevice> zybswList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
						List<PowerDevice> mlswList = new ArrayList<PowerDevice>();

						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
								drswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(drswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
								dkswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(dkswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								xlswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(xlswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getPowerDeviceName().contains("接地变")
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)){
								jdbswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(jdbswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getPowerDeviceName().contains("站用变")
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchZYB)
									){
								zybswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(zybswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								zbswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(zbswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								mlswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(mlswList);
						
						swList.clear();
						
						if(!end.equals("")&&!begin.equals("")){
							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
									swList.addAll(drswList);
									swList.addAll(dkswList);
									swList.addAll(xlswList);
									swList.addAll(jdbswList);
									swList.addAll(zybswList);
									swList.addAll(zbswList);
									swList.addAll(mlswList);

									getSwitchSequenceTdStep(swList,data,uuids,czname,ddname);
									
									getSwitchSeparateSequenceTd(qtdzList,data,uuids,czname,ddname);
									
									for(PowerDevice xldz : xldzList){
										String devid = xldz.getPowerDeviceID();

										if(RuleExeUtil.isDeviceChanged(xldz)){
											cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(xldz);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
											
											String  qzcznr  =  "确认"+CZPService.getService().getDevName(xldz)+"处拉开位置";
											data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
										}
									}
								}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
									swList.addAll(mlswList);
									
									getSwitchSequenceTdStep(swList,data,uuids,czname, ddname);
								}
							}else{//复电
								if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
									if(dev.getPowerVoltGrade() == 10){
										swList.addAll(zbswList);
									}else{
										swList.addAll(mlswList);
										swList.addAll(zbswList);
										swList.addAll(zybswList);
										swList.addAll(jdbswList);
										swList.addAll(xlswList);
										swList.addAll(dkswList);
										swList.addAll(drswList);
									}
									
									for(PowerDevice qtdz : qtdzList){
										String devid = qtdz.getPowerDeviceID();

										if(qtdz.getPowerDeviceName().contains("站用变")){
											if(RuleExeUtil.isDeviceChanged(qtdz)){
												cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(qtdz);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
												
												String  qzcznr  =  "确认"+CZPService.getService().getDevName(qtdz)+"处合上位置";
												data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
											}
										}
									}
									
									getSwitchSeparateSequenceFd(qtdzList,data,uuids,czname,ddname);
								}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
									swList.addAll(mlswList);
									
									getSwitchSequenceFdStep(swList,data,uuids,czname,ddname);
								}
							}
						}else{
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
						}
					}
					else if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						List<PowerDevice> gyczbkgList = RuleExeUtil.getTransformerSwitchHigh(dev);
						List<PowerDevice> zyczbkgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						List<PowerDevice> dyczbkgList = RuleExeUtil.getTransformerSwitchLow(dev);
						List<PowerDevice> zbgycdzkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeZBS, "", true, true, true, true);

						if(RuleUtil.isTransformerNQ(dev)
								||RuleUtil.isTransformerKDNQ(dev)){
							gyczbkgList.clear();
						}
						
						if(!begin.equals("")&&!end.equals("")){
							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								getSwitchSequenceTdMerge(dyczbkgList, data, uuids, czname, ddname);
								
								getSwitchSequenceTdMerge(zyczbkgList, data, uuids, czname, ddname);

								getSwitchSequenceTdMerge(gyczbkgList, data, uuids, czname, ddname);
								
								getSwitchSeparateSequenceTd(zbgycdzkgList,data,uuids,czname,ddname);

							}else{//复电
								getSwitchSeparateSequenceFd(zbgycdzkgList,data,uuids,czname,ddname);

								getSwitchSequenceFdMerge(gyczbkgList, data, uuids, czname, ddname);
								
								getSwitchSequenceFdMerge(zyczbkgList, data, uuids, czname, ddname);
								
								getSwitchSequenceFdMerge(dyczbkgList, data, uuids, czname, ddname);
							}
						}else{
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
						}
					}
					else if(dev.getDeviceType().equals(SystemConstants.Switch)){
						if((begin.equals("1")&&end.equals("2"))){
							List<PowerDevice> swList = new ArrayList<PowerDevice>();
							swList.add(dev);
							getSwitchSequenceTdStep(swList , data, uuids, czname ,ddname);
						}else if(begin.equals("2")&&end.equals("1")){
							List<PowerDevice> swList = new ArrayList<PowerDevice>();
							swList.add(dev);
							getSwitchSequenceFdStep(swList , data, uuids, czname ,ddname);
						}else if(begin.equals("1")&&end.equals("0")){
							String devid = dev.getPowerDeviceID();
							
							if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
								if(!cznr.contains("遥控")){
									cznr = "合上"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								}else{
									cznr = "遥控合上"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								}
							}
						}else if(begin.equals("0")&&end.equals("1")){
							String devid = dev.getPowerDeviceID();
							
							if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
								if(!cznr.contains("遥控")){
									cznr  =  "断开"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
								}else{
									cznr  =  "遥控断开"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
								}
							}
						}else if(cznr.contains("热备用倒至")){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortByXLC(dzList);
							
							for(PowerDevice dz : dzList){
								String devid = dz.getPowerDeviceID();
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
									cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								}
							}
							String devid = dev.getPowerDeviceID();

							String qzcznr  = "确认"+CZPService.getService().getDevName(dev)+"处冷备用";
							data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});

							dzList = RuleExeUtil.sortByMXC(dzList);
							
							for(PowerDevice dz : dzList){
								devid = dz.getPowerDeviceID();

								if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
									cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,"合上",endZT,deviceType,uuids});
								}
							}
							

							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
									List<PowerDevice> mxlist =  RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
									
									if(mxlist.size()>0){
										devid = dev.getPowerDeviceID();

										qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处热备用于"+CZPService.getService().getDevName(mxlist);
										data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
									}
								}
							}
						}else if(cznr.contains("运行倒至")){
							for(RuleBaseMode rm : rbmList){
								if(rm.getPd().getDeviceType().equals(SystemConstants.Switch)){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(rm.getPd(), SystemConstants.SwitchSeparate);

									for(PowerDevice dz : dzList){
										if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
											String devid = dz.getPowerDeviceID();
											
											cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
											
											String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处合上位置";
											data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
										}
									}
									
									for(PowerDevice dz : dzList){
										String devid = dz.getPowerDeviceID();

										if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
											cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
											
											String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处拉开位置";
											data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
										}
									}
								}
							}
						}else{
							data.add(new String[]{"","",czname,czname,cznr,isyk,"",devname,startZT,endZT,deviceType,uuids});
						}
					}else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)){
							String devid = dev.getPowerDeviceID();

							if(cznr.contains("落实")){
								String devName=CZPService.getService().getDevName(dev);
								devName=devName.replace("中性点",(int)dev.getPowerVoltGrade()+"kV侧中性点");
								String  qzcznr  =  "确认"+devName+"处合上位置";
								data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else if(begin.equals("1")&&end.equals("0")){
								String devName=CZPService.getService().getDevName(dev);
								devName=devName.replace("中性点",(int)dev.getPowerVoltGrade()+"kV侧中性点");
								cznr =  "遥控合上"+czname+devName;
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
								String  qzcznr  =  "确认"+devName+"处合上位置";
								data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								String devName=CZPService.getService().getDevName(dev);
								devName=devName.replace("中性点",(int)dev.getPowerVoltGrade()+"kV侧中性点");
								cznr =  "遥控拉开"+czname+devName;
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								
								String  qzcznr  =  "确认"+devName+"处拉开位置";
								data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else{
								if(cznr.contains("主变中性点")&&cznr.contains("确认")){
									String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
									String qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处合上位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,"",deviceType,uuids});
								}else if(cznr.contains("主变中性点")&&cznr.contains("落实")){
									String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
									String qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处合上位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,"",deviceType,uuids});
								}else{
									data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
								}
							}
						}else{
							if(begin.equals("1")&&end.equals("0")){
								String devid = dev.getPowerDeviceID();

								cznr =  "合上"+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								String devid = dev.getPowerDeviceID();

								cznr =  "拉开"+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}
					}else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						String devid = dev.getPowerDeviceID();
						
						if(begin.equals("1")&&end.equals("0")){
							cznr =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
							data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							
							String  qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处合上位置";
							data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
						}else if(begin.equals("0")&&end.equals("1")){
							cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(dev);
							data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
							
							String  qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处拉开位置";
							data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
						}else{
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
						}
					}else if(cznr.contains("中性点")&&cznr.contains("确认")){
							List<PowerDevice> gdList = RuleExeUtil.getDeviceList(CBSystemConstants.getCurRBM().getPd(), SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
							
							RuleExeUtil.swapLowDeviceList(gdList);
							
							for(PowerDevice gd : gdList) {
								String devid = gd.getPowerDeviceID();
								devname = gd.getPowerDeviceName();
								String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(gd);
								data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}
							
							for(PowerDevice gd : gdList) {
								String devid = gd.getPowerDeviceID();
								devname = gd.getPowerDeviceName();
								String temp  =  "确认"+CZPService.getService().getDevName(gd)+"处合上位置";
								data.add(new String[]{"","",czname,czname,temp,isyk,devid,devname,startZT,"",deviceType,uuids});
							}
					}else if(cznr.contains("所有断路器倒至")){
						List<PowerDevice> swList = new ArrayList<PowerDevice>();
						
						PowerDevice	device = CBSystemConstants.getCurRBM().getPd();
						
						if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
							swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchFHC,false, true, true, true);
						}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
							swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						}
						
						List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
						List<PowerDevice> xlswList = new ArrayList<PowerDevice>();

						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
								zbswList.add(switchs);
							}
						}
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
								zbswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(zbswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
								xlswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(xlswList);
						
						List<PowerDevice> yxswList = new ArrayList<PowerDevice>();
						
						yxswList.addAll(xlswList);
						yxswList.addAll(zbswList);
						
						for(PowerDevice sw : yxswList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

							for(PowerDevice dz : dzList){
								String devid = dz.getPowerDeviceID();
								
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
									cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									
									String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处合上位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
								}
							}
							
							for(PowerDevice dz : dzList){
								String devid = dz.getPowerDeviceID();

								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
									cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
									
									String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处拉开位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
								}
							}
						}
					}else if(cznr.contains("站用变")){
						String zybName  =  cznr.substring(1, cznr.indexOf("站用变")+3);
						
						if(!zybName.equals("")){
							String zbysql = "SELECT ZYB_DZNAME,ZYB_DEVID FROM "+CBSystemConstants.opcardUser+"T_A_LINEZYB WHERE ZYB_NAME = '"+zybName+"' AND STATION_NAME = '"+stationname+"'";
							List<Map<String,String>> zybNameList = DBManager.queryForList(zbysql);
							
							for(Map<String,String> zybNameMap : zybNameList){
								String zbyName = StringUtils.ObjToString(zybNameMap.get("ZYB_DZNAME"));
								String zybDevid = StringUtils.ObjToString(zybNameMap.get("ZYB_DEVID"));

								cznr  =  "遥控拉开"+czname+zbyName;
								data.add(new String[]{"","",ddname,czname,cznr,"",zybDevid,"","","断开","",uuids});
								
								String  qzcznr  =  "确认"+zbyName+"处拉开位置";
								data.add(new String[]{"","",czname,czname,qzcznr,"",zybDevid,"","","","",uuids});
							}
						}
					}else if(cznr.contains("全站")){
						List<PowerDevice> xlkg10kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> mlkg10kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbkg10kVList = new ArrayList<PowerDevice>();

						List<PowerDevice> xlkg35kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> mlkg35kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbdyckg35kVList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbfhckg35kVList = new ArrayList<PowerDevice>();

						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationid);

						for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
							PowerDevice pd = it.next();
							
							if(pd.getPowerVoltGrade() == 6){
								
							}else if(pd.getPowerVoltGrade() == 10){
								if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									xlkg10kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									mlkg10kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
									zbkg10kVList.add(pd);
								}
							}else if(pd.getPowerVoltGrade() == 35){
								if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
									xlkg35kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									mlkg35kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
									zbdyckg35kVList.add(pd);
								}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
									zbfhckg35kVList.add(pd);
								}
							}
						}
						
						/*
						 * 10kV设备（只能作为低压侧）
						 */
						
						//线路开关
						getSwitchSequenceTdStep(xlkg10kVList,data,uuids,czname,ddname);
						//分段开关
						getSwitchSequenceTdStep(mlkg10kVList,data,uuids,czname,ddname);
						//主变开关	
						getSwitchSequenceTdStep(zbkg10kVList,data,uuids,czname,ddname);
						
						/*
						 * 35kV设备（可以作为高压侧，或者中压侧，或者低压侧）
						 */
						
						//电源侧主变开关	
						getSwitchSequenceTdStep(zbdyckg35kVList,data,uuids,czname,ddname);
						//线路开关
						getSwitchSequenceTdStep(xlkg35kVList,data,uuids,czname,ddname);
						//分段开关
						getSwitchSequenceTdStep(mlkg35kVList,data,uuids,czname,ddname);
						//负荷侧主变开关	
						getSwitchSequenceTdStep(zbfhckg35kVList,data,uuids,czname,ddname);
					}else if(cznr.contains("退出") || cznr.contains("投入")){
						startZT = rbmList.get(0).getBeginStatus();
						endZT = rbmList.get(0).getEndState();
						data.add(new String[]{"","",czname,czname,cznr,"0",dev.getPowerDeviceID(),devname,startZT,endZT,deviceType,uuids});
					}else{
						if(cim.getStationName().equals("云南省调")){
							czname = "云南省调";
							cznr = cznr.replace("落实", "落实省调");
						}
						
						data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
					}
				}else{
					if(czname.equals("")){
						czname = stationname;
					}
					
					if(czname.equals("昆明地调")){
						czname = bdzname;
					}
					
					data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
				}
			}else{
				data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
			}
		}
		
		return data;
	}
	
	public static String getVoltInDevName(String powerDeviceName) {
    	String volt = "";
    	String equipName = powerDeviceName;
    	int pos = equipName.toUpperCase().indexOf("KV");
		if (pos >= 0) {
			volt = "";
			for(int i = pos-1; i >=0; i--) {
				char ch = equipName.charAt(i);
				if (ch >= '0' && ch <= '9')
					volt = ch + volt;
				else
					break;
			}
        }
		else
			volt = "";
    	return volt;
    }
	
	public static  String getStationNameByCznr(String cznr) {
		String stationName = "";
		if(cznr.lastIndexOf("站") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("站")+1);
		else if(cznr.lastIndexOf("变") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("变")+1);
		else if(cznr.lastIndexOf("厂") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("厂")+1);
		
		if(stationName.indexOf("千伏") >= 0)
			stationName = stationName.substring(stationName.lastIndexOf("千伏")+2);
		else if(stationName.toLowerCase().indexOf("kv") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().lastIndexOf("kv")+2);
		else if(stationName.toLowerCase().indexOf("切换至") >= 0 || stationName.toLowerCase().indexOf("切至") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("至")+1);
		else if(stationName.toLowerCase().indexOf("切换到") >= 0 || stationName.toLowerCase().indexOf("切到") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("到")+1);
		else if(stationName.toLowerCase().indexOf("断开") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("断开")+2);
		else if(stationName.toLowerCase().indexOf("合上") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("合上")+2);
		return stationName;
	}
	
	/*
	 * 合并序列
	 */
	public static void getSwitchSequenceTdMerge(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
					Collections.reverse(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				if(RuleExeUtil.isDeviceChanged(zbdz)&&!zbdz.getPowerDeviceName().contains("PT")){
					String devid =  zbdz.getPowerDeviceID();
					String devname = CZPService.getService().getDevName(zbdz);
					String deviceType = zbdz.getDeviceType();
					
//					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
//						if(zbdz.getPowerDeviceName().contains("隔离手车")){
//							String cznr  =  "将"+czname+CZPService.getService().getDevName(zbdz)+"由工作位置摇至试验位置";
//							data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
//							
//							String  qzcznr  =  "确认"+czname+CZPService.getService().getDevName(zbdz)+"在试验位置";
//							
//							String qzdevid = zbdz.getPowerDeviceID();
//							
//							data.add(new String[]{"","",czname,czname,qzcznr,"",qzdevid,"","","","",uuids});
//						}else{
//							String cznr  =  "将"+czname+CZPService.getService().getDevName(sw)+"手车由工作位置摇至试验位置";
//							
//							data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});
//							
//							String  qzcznr  =  "确认"+CZPService.getService().getDevName(sw)+"手车在试验位置";
//							
//							String qzdevid = zbdz.getPowerDeviceID();
//							
//							data.add(new String[]{"","",czname,czname,qzcznr,"",qzdevid,"","","","",uuids});
//						}
//					}else{
						String cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(zbdz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});

						String qzcznr = "确认"+devname+"处拉开位置";
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
//					}
				}
			}
		}
	}
	
	/*
	 * 刀闸停电（分布格式）
	 */
	public static void getSwitchSequenceTdStep(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
					Collections.reverse(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				if(RuleExeUtil.isDeviceChanged(zbdz)&&!zbdz.getPowerDeviceName().contains("PT")){
					String devid =  zbdz.getPowerDeviceID();
					String devname = CZPService.getService().getDevName(zbdz);
					String deviceType = zbdz.getDeviceType();
					
					String cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(zbdz);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});

					String qzcznr = "确认"+devname+"处拉开位置";
					data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
				}
			}
		}
	}
	
	/*
	 * 刀闸复电（合并格式）
	 */
	public static void getSwitchSequenceFdMerge(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
					Collections.reverse(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				String devid = zbdz.getPowerDeviceID();

				if(RuleExeUtil.isDeviceChanged(zbdz)){
					devid =  zbdz.getPowerDeviceID();
					String devname = CZPService.getService().getDevName(zbdz);
					String deviceType = zbdz.getDeviceType();
					
//					if(zbdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
//						String cznr  =  "将"+czname+CZPService.getService().getDevName(sw)+"手车由试验位置摇至工作位置";
//						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});
//						
//						String  qzcznr  =  "确认"+CZPService.getService().getDevName(sw)+"手车在工作位置";
//						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
//					}else{
						String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(zbdz);
						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});

						String  qzcznr  =  "确认"+CZPService.getService().getDevName(zbdz)+"处合上位置";
						
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
//					}
				}
			}
		}
	}
	
	/*
	 * 开关复电获取刀闸序列（分布格式）
	 */
	public static void getSwitchSequenceFdStep(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
					Collections.reverse(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
				}
			}
			
			for(PowerDevice zbdz : dzList){
				String devid = zbdz.getPowerDeviceID();

				if(RuleExeUtil.isDeviceChanged(zbdz)){
					devid =  zbdz.getPowerDeviceID();
					String devname = CZPService.getService().getDevName(zbdz);
					String deviceType = zbdz.getDeviceType();
					
					String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(zbdz);
					data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});

					String  qzcznr  =  "确认"+CZPService.getService().getDevName(zbdz)+"处合上位置";
					data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
				}
			}
		}
	}
	
	/*
	 * 传入刀闸获取序列
	 */
	public static void getSwitchSeparateSequenceTd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dz : dzList){
			if(dz.getPowerDeviceName().contains("站用变")){
				if(RuleExeUtil.isDeviceChanged(dz)){
					String devid =  dz.getPowerDeviceID();
					String devname = dz.getPowerDeviceName();

//					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
//						String dzName = CZPService.getService().getDevName(dz);
//						String zybname = dzName.substring(0, dzName.indexOf("站用变")+3);
//						
//						String cznr  =  "将"+czname+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车由工作位置摇至试验位置";
//						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
//						
//						String  qzcznr  =  "确认"+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车在试验位置";
//						
//						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
//					}else{
						String cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
						
						String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处拉开位置";
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
//					}
				}
			}else{
				String devid = dz.getPowerDeviceID();
				String devname = dz.getPowerDeviceName();

				if(RuleExeUtil.isDeviceChanged(dz)){
					String cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
					
					String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处拉开位置";
					data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
				}
			}
		}
	}
	
	/*
	 * 传入刀闸获取序列
	 */
	public static void getSwitchSeparateSequenceFd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dz : dzList){
			if(dz.getPowerDeviceName().contains("站用变")){
				if(RuleExeUtil.isDeviceChanged(dz)){
					String devid =  dz.getPowerDeviceID();
					String devname = dz.getPowerDeviceName();

//					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
//						String dzName = CZPService.getService().getDevName(dz);
//						String zybname = dzName.substring(0, dzName.indexOf("站用变")+3);
//						
//						String cznr  =  "将"+czname+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车由试验位置摇至工作位置";
//						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
//						
//						String  qzcznr  =  "确认"+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车在工作位置";
//						
//						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
//					}else{
						String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
						
						String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处合上位置";
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
//					}
				}
			}else{
				String devid = dz.getPowerDeviceID();
				String devname = dz.getPowerDeviceName();

				if(RuleExeUtil.isDeviceChanged(dz)){
					String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
					
					String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处合上位置";
					data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
				}
			}
		}
	}

	public static List<RuleBaseMode> getRBMList(String station, String word) {
//		String kuohao = "";
//		word = word.replace("(", "（").replace(")", "）");
//
//		if(word.contains("（")&&word.contains("）")){
//			if(word.indexOf("（")<word.indexOf("）")){
//				kuohao = word.substring(word.indexOf("（"),word.indexOf("）")+1);
//			}
//		}
//
//		word = word.replace(kuohao, "");
		String wholeWord = word;

		/*
		 * 上级单位
		 */
		String superiorStationName = "";
		/*
		 * 下级单位
		 */
		String subordinateStationName = "";
		/*
		 * 指令单位
		 */
		String instructionStationName = "";

		/*
		 * 生成设备对象、初始状态、目标状态、操作、所在母线解析
		 */
		List<CardWordMode> wordRbmList = new ArrayList<CardWordMode>();

//		String sql="SELECT MODELDESC,BEGINSTATUS,ENDSTATUS,OPERATION " +
//				"FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDMODEL ORDER BY TO_NUMBER(ORDERID) ASC";
//		List<Map<String,String>> modelList=DBManager.queryForList(sql);

		for(Map<String,String> model : modelList){
			String modeldesc = StringUtils.ObjToString(model.get("MODELDESC"));
			String beginStatus = StringUtils.ObjToString(model.get("BEGINSTATUS"));
			String endStatus = StringUtils.ObjToString(model.get("ENDSTATUS"));
			String operation = StringUtils.ObjToString(model.get("OPERATION"));

			Object[] splitParams = SafeCheckUtilKM.init(modeldesc,'[',']');
			List<String> paramsKey=(ArrayList<String>)splitParams[1]; //标签集合

			if(paramsKey.size()>0){
				List<String> firstStr=(ArrayList)splitParams[0];
				String lastStr=splitParams[2].toString();

				StringBuffer descBuff=new StringBuffer();

				for(String first : firstStr){
					descBuff.append(first+"(.*)");
				}

				descBuff.append(lastStr);

				String regx = "^"+descBuff.toString()+"$";
				Pattern compile = Pattern.compile(regx);
				Matcher matcher = compile.matcher(word);

				if(matcher.find()){//解析
					System.out.println("操作内容："+word);

					String[] splitregxs = regx.replace("^", "").replace("$", "").replace("(.*)", "|").split("\\|");

					for(String string : splitregxs){
						if(!string.equals("")){
							if(string.contains("转") && word.contains("转线")){
								word = word.substring(0, word.lastIndexOf("转"))+word.substring(word.lastIndexOf("转")+1, word.length());
								continue;
							}

							if(string.equals("倒由")){
								word = word.replace("倒", "");
							}
							word = word.replace(string, "");
						}
					}

					List<Map<String,String>> returnList = SafeCheckUtilKM.getDeviceInfoByWord(paramsKey,word);

					CardWordMode cwm = new CardWordMode();

					if(!beginStatus.equals("")){
						cwm.setBeginStatus(beginStatus);
					}

					if(!endStatus.equals("")){
						cwm.setEndStatus(endStatus);
					}

					if(!operation.equals("")){
						cwm.setOperaTion(operation);
					}

					List<PowerDevice> devList = new ArrayList<PowerDevice>();

					for(Map<String,String> returnMap : returnList){
						PowerDevice dev = new PowerDevice();

						if(returnMap.containsKey("厂站名称")){
							dev.setPowerStationName(returnMap.get("厂站名称"));
							instructionStationName = returnMap.get("厂站名称");
						}

						if(returnMap.containsKey("设备名称")){
							dev.setPowerDeviceName(returnMap.get("设备名称"));
						}

						if(returnMap.containsKey("设备状态")){
							if(cwm.getBeginStatus().equals("")){
								cwm.setBeginStatus(returnMap.get("设备状态"));
							}else if(cwm.getEndStatus().equals("")){
								cwm.setEndStatus(returnMap.get("设备状态"));
							}
						}

						if(returnMap.containsKey("所在母线")){
							cwm.setBusBar(returnMap.get("所在母线"));
						}

						if(returnMap.containsKey("设备类型")){
							cwm.setDeviceKind(returnMap.get("设备类型"));
						}

						if(dev.getPowerStationName().equals("")){
							dev.setPowerStationName(station);
						}

						if(!dev.getPowerDeviceName().equals("")){
							devList.add(dev);
						}
					}

					cwm.setPdList(devList);
					wordRbmList.add(cwm);

					for(PowerDevice device : devList){
						System.out.println("厂站名称："+device.getPowerStationName());
						System.out.println("设备名称："+device.getPowerDeviceName());
					}

					System.out.println("初始状态："+cwm.getBeginStatus());
					System.out.println("目标状态："+cwm.getEndStatus());
					System.out.println("操作："+cwm.getOperaTion());
					System.out.println("所在母线："+cwm.getBusBar());
					System.out.println("****************************************");

					break;
				}
			}
		}

		List<String> stationIDList = new ArrayList<String>();

		if(instructionStationName.equals("")){//指令单位为空，那么下级单位取station
			subordinateStationName = station;
		}else{//指令单位不为空，那么上级单位为station，下级单位为instructionStationName
			superiorStationName = station;
			subordinateStationName = instructionStationName;
		}

		//厂站名称校验
		for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();){
			PowerDevice st = it.next();

			String modelDevName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(st));
			subordinateStationName = StringUtils.killVoltInDevName(subordinateStationName);

			if(modelDevName.equals(subordinateStationName)) {
				String stationID = st.getPowerDeviceID();
				stationIDList.add(stationID);

				if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
					CreatePowerStationToplogy.loadFacEquip(stationID);
				}
			}
		}

		if(stationIDList.size() > 1){
			for(Iterator<String> it = stationIDList.iterator();it.hasNext();){
				String stationID = it.next();

				String sql = "SELECT STATIONID FROM " + CBSystemConstants.equipUser + "T_SUBSTATION_TREE WHERE STATIONID = '"+stationID+"' AND ISREMOVE = '1'";
				List<Map<String,String>> idList = DBManager.queryForList(sql);

				if(idList.size()==1){
					it.remove();
				}
			}
		}

		/*
		 * 只写线路的情况下特殊判断
		 */

		if(stationIDList.size() == 0 && wordRbmList.size() > 0){
			if(!wholeWord.contains("断路器")&&!wholeWord.contains("隔离开关")&&!wholeWord.contains("接地开关")){
				for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerLine().values().iterator();it.hasNext();){
					PowerDevice line = it.next();

					if(line.getPowerVoltGrade() > 10){
						String lineName = CZPService.getService().getDevName(line);

						if(!lineName.equals("备用")&&wholeWord.contains(lineName)){
							Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(line);

							for(PowerDevice ln:stationlines.keySet()){
								if(ln.getPowerStationName().contains("tase")){
									continue;
								}

								String stationID = ln.getPowerStationID();
								stationIDList.add(stationID);

								break;
							}
						}
					}
				}
			}
		}

		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();

		if(stationIDList.size() > 1){
			ArrayList<String> messageList = new ArrayList<String>();
			messageList.add("当前成票中，操作单位匹配到多个厂站模型，成票失败！");

			RuleBaseMode rbm = new RuleBaseMode();
			rbm.setCheckout(false);
			rbm.setMessageList(messageList);
			rbmList.add(rbm);
		}

		//设备名称校验
		if(stationIDList.size() == 1){
			if (CBSystemConstants.getStationPowerDevices(stationIDList.get(0)) == null) {
				CreatePowerStationToplogy.loadFacEquip(stationIDList.get(0));
			}

			HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationIDList.get(0));

			if(devMap != null){
				for(CardWordMode cwm : wordRbmList){
					List<PowerDevice> devList = cwm.getPdList();

					if(cwm.getDeviceKind().equals("二次设备")){

					}else if(cwm.getOperaTion().contains("地线")){

					}else{
						for(PowerDevice device : devList){
							String equipTypeFlag = "";
							String equipTypeName = "";
							String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
									SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine,
									SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate,SystemConstants.Switch, SystemConstants.SwitchSeparate,
									SystemConstants.SwitchSeparate, SystemConstants.Switch,
									SystemConstants.Switch,SystemConstants.VolsbTransformer, SystemConstants.MotherLine,
									SystemConstants.MotherLine, SystemConstants.InOutLine,SystemConstants.InOutLine,
									SystemConstants.PowerTransformer, SystemConstants.ElecShock,
									SystemConstants.ElecCapacity ,SystemConstants.PowerTransformer,SystemConstants.Term};
							String[] key = new String[] { "接地刀闸","接地开关", "地刀", "隔离刀闸","隔离开关","小车开关", "小车", "刀闸", "断路器",
									"开关","PT", "母线", "母", "线","回", "主变", "电抗器", "电容器","#变","站用变"};
							for (int i = 0; i < key.length; i++) {
								if (device.getPowerDeviceName().lastIndexOf(key[i]) >= 0) {
									equipTypeFlag = type[i];
									equipTypeName = key[i];
									break;
								}
							}

							String devName = CZPService.getService().getDevName(device);
							devName = devName.replaceAll("\\d+kV侧中性点","中性点");
							String volStr = "";

							if(device.getPowerDeviceName().toLowerCase().split("kv").length >= 3){
								volStr = device.getPowerDeviceName().toLowerCase().substring(device.getPowerDeviceName().toLowerCase().indexOf("kv")+2);
							}else if(device.getPowerDeviceName().contains("中性点")&&device.getPowerDeviceName().contains("接地开关")){
								volStr = "";
							}else{
								volStr = device.getPowerDeviceName();
							}

							//获取电压等级
							String volt = StringUtils.getVoltInDevName(volStr);
							List<PowerDevice> pdList = new ArrayList<PowerDevice>();

							for (PowerDevice dev : devMap.values()) {
								if (!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
									continue;
								if (!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
									continue;
								if(dev.getPowerDeviceName().contains("A相")||dev.getPowerDeviceName().contains("B相")||dev.getPowerDeviceName().contains("C相"))
									continue;
								if(dev.getPowerDeviceName().contains("虚"))
									continue;

								if(equipTypeFlag.equals(SystemConstants.InOutLine)){
									if (CZPService.getService().getDevName(dev).equals(device.getPowerDeviceName())){
										pdList.add(dev);
										if(pdList.size()>1){
											break;
										}
									}
								}else if (CZPService.getService().getDevName(dev).equals(devName)&&!devName.equals("")) {
									if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
										pdList.add(dev);
										if(pdList.size()>1){
											break;
										}
									}else{
										pdList.add(dev);
										if(pdList.size()>1){
											break;
										}
									}
								}
							}

							if(pdList.size() > 1){
								ArrayList<String> messageList = new ArrayList<String>();
								messageList.add("当前成票中，<"+device.getPowerDeviceName()+">匹配到多个模型设备，成票失败！");

								RuleBaseMode rbm = new RuleBaseMode();
								rbm.setPd(device);
								rbm.setCheckout(false);
								rbm.setMessageList(messageList);
								rbmList.add(rbm);
							}else if(pdList.size() == 1){
								String modelName = pdList.get(0).getPowerDeviceName();
								String deviceName = device.getPowerDeviceName();
								String beginStatus = cwm.getBeginStatus();
								String endStatus = cwm.getEndStatus();

								beginStatus = RuleExeUtil.getNumStatusNew(beginStatus);
								endStatus = RuleExeUtil.getNumStatusNew(endStatus);

								RuleBaseMode rbm = new RuleBaseMode();
								rbm.setPd(pdList.get(0));
								rbm.setBeginStatus(beginStatus);
								rbm.setEndState(endStatus);
								rbm.setBusBar(cwm.getBusBar());
								rbm.setOperaTion(cwm.getOperaTion());
								rbmList.add(rbm);
							}
						}
					}
				}
			}
		}

		if(rbmList.size()==0){
			RuleBaseMode rbm = new RuleBaseMode();
			PowerDevice dev = new PowerDevice();
			rbm.setPd(dev);
			rbmList.add(rbm);
		}

		return rbmList;
	}
}
