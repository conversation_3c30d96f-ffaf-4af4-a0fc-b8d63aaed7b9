package com.tellhow.czp.app.yndd.wordcard.dh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dh.DHHaveWorkDialog;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDHSMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("德宏双母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			replaceStr += CommonFunctionDH.getPowerOnCheckContent();
			
			List<PowerDevice> mlkgList =  new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					mlkgList.add(dev);
				}
			}
			
			replaceStr += "落实"+deviceName+"保护已按正常方式投入/r/n";
				
			List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> plkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL +","+ CBSystemConstants.RunTypeSwitchMLPL, "", false, true, true, true);
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			}
			
			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : xlkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			replaceStr += stationName+"@将"+deviceName+"电压互感器由冷备用转运行/r/n";

			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@投入"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				replaceStr += "德宏地调@执行"+stationName+deviceName+"由热备用转空载运行程序操作/r/n";
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += "德宏地调@执行"+stationName+deviceName+"由冷备用转空载运行程序操作/r/n";
			}
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@退出"+CZPService.getService().getDevName(dev)+"充电保护/r/n";
			}
			
			replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice yxkg : yxkgList){
						if(CommonFunctionDH.ifSwitchControl(yxkg)&&CommonFunctionDH.ifSwitchSeparateControl(yxkg)){
							replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(yxkg)+"由"+deviceName+"运行倒至"+CZPService.getService().getDevName(dev)+"运行程序操作/r/n";
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(yxkg, SystemConstants.SwitchSeparate);
							
							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
									replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在合闸位置/r/n";
								}
							}
							
							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
									replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
								}
							}
						}
					}
				}
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice yxkg : yxkgList){
						if(CommonFunctionDH.ifSwitchControl(yxkg)&&CommonFunctionDH.ifSwitchSeparateControl(yxkg)){
							
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(yxkg)+"由"+deviceName+"运行倒至"+CZPService.getService().getDevName(dev)+"运行/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : mlkgList){
				replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
			}
			
			replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice rbykg : rbykgList){
						if(CommonFunctionDH.ifSwitchControl(rbykg)&&CommonFunctionDH.ifSwitchSeparateControl(rbykg)){
							replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由热备用转冷备用程序操作/r/n";
							replaceStr += "德宏地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由冷备用转联"+CZPService.getService().getDevName(curDev)+"热备用程序操作/r/n";
						}
					}
				}
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice rbykg : rbykgList){
						if(CommonFunctionDH.ifSwitchControl(rbykg)&&CommonFunctionDH.ifSwitchSeparateControl(rbykg)){
							
						}else{
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(rbykg)+"由"+deviceName+"热备用倒至"+CZPService.getService().getDevName(dev)+"热备用/r/n";
						}
					}
				}
			}
		}
		
		return replaceStr;
	}
}
