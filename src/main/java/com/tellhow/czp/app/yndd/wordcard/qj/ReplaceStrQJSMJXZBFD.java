package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJSMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖双母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);
			
			replaceStr += CommonFunctionQJ.getPowerOnCheckContent();
			
			List<PowerDevice> otherzbzxdjddzList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zbList =  new ArrayList<PowerDevice>();

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate, CBSystemConstants.RunTypeKnifeZB);
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> itor = mapStationDevice.values().iterator(); itor.hasNext();) {
				PowerDevice dev = itor.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
					
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						for(PowerDevice gd : gdList) {
							otherzbzxdjddzList.add(gd);
						}
					}
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				for(PowerDevice dev : zbgyckgList){
					if (RuleExeUtil.isDeviceChanged(dev)) {
						replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if (RuleExeUtil.isDeviceChanged(dev)) {
						replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
                    if (RuleExeUtil.isDeviceChanged(dev)) {
                    	replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
                    }
                }
				
				zbdzList = RuleExeUtil.sortByVoltHigh(zbdzList);
				
				for(PowerDevice dev : zbdzList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionQJ.getKnifeOnContent(zbdzList, stationName);
                    }
                }
			}
			
			if(curDev.getDeviceStatus().equals("0")){
				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunctionQJ.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += CommonFunctionQJ.getCdOrHhContent(dev, "曲靖地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += CommonFunctionQJ.getCdOrHhContent(dev, "曲靖地调", stationName);
					}
				}

				if(zxdjddzList.size() > 0){
					replaceStr += CommonFunctionQJ.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
				}

				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}

				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}

				for(PowerDevice dev : gycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
