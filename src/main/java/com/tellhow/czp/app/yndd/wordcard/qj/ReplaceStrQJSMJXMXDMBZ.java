package com.tellhow.czp.app.yndd.wordcard.qj;


import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrQJSMJXMXDMBZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("曲靖双母接线母线倒母备注".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> plkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
						mxList.add(dev);
					}
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						mlkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						xlkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)){
						plkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
						zbkgList.add(dev);
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
						zbkgList.add(dev);
					}
				}
			}
			
			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : xlkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			replaceStr += "1、操作前，已对设备状态进行核实；2、";
			
			for(PowerDevice rbykg : rbykgList){
				String curMxName = "";
				String otherMxName = "";
				
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(rbykg, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : dzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
						List<PowerDevice> curmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
						
						if(dev.getDeviceStatus().equals("0")){
							for(PowerDevice mx : curmxList){
								otherMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}else if(dev.getDeviceStatus().equals("1")){
							for(PowerDevice mx : mxList){
								curMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}
					}
				}
				
				replaceStr += stationName+CZPService.getService().getDevName(rbykg)+"由"+curMxName+"热备用倒至"+otherMxName+"热备用；";
			}
			
			for(PowerDevice yxkg : yxkgList){
				String curMxName = "";
				String otherMxName = "";
				
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(yxkg, SystemConstants.SwitchSeparate);

				for(PowerDevice dev : dzList){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
						List<PowerDevice> curmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
						
						if(dev.getDeviceStatus().equals("0")){
							for(PowerDevice mx : curmxList){
								otherMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}else if(dev.getDeviceStatus().equals("1")){
							for(PowerDevice mx : curmxList){
								curMxName = CZPService.getService().getDevName(mx);
								break;
							}
						}
					}
				}
				
				replaceStr += stationName+CZPService.getService().getDevName(yxkg)+"由"+curMxName+"运行倒至"+otherMxName+"运行；";
			}
		}
		
		if(replaceStr.endsWith("；")){
			replaceStr = replaceStr.substring(0, replaceStr.length()-1)+"。";
		}
		
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		
		return replaceStr;
	}
}
