package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJXBZJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江线变组接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			PowerDevice otherzb = new PowerDevice();
			
			List<PowerDevice> otherzbzxdjddzList =  new ArrayList<PowerDevice>();

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(curDev);
			
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgycdzList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				gycxlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true);
			}

			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zbList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					
					if(!dev.getPowerDeviceName().contains("接地变")){
						zbList.add(dev);
					}
					
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzb = dev;
						
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						for(PowerDevice gd : gdList) {
							otherzbzxdjddzList.add(gd);
						}
					}
				}
			}
			
			List<PowerDevice> otherzbdyckgList = RuleExeUtil.getTransformerSwitchLow(otherzb);
			List<PowerDevice> otherzbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(otherzb);
			
			for(PowerDevice dev : gycmlkgList){
				replaceStr += voltStationName+"@投入"+deviceName+"高后备保护跳"+CZPService.getService().getDevName(dev)+"功能/r/n";
			}

			for(PowerDevice dev : gycxlkgList){
				replaceStr += voltStationName+"@投入"+deviceName+"高后备保护跳"+CZPService.getService().getDevName(dev)+"功能/r/n";
			}
			
			for(PowerDevice dev : zycmlkgList){
				replaceStr += voltStationName+"@投入"+deviceName+"中后备保护跳"+CZPService.getService().getDevName(dev)+"功能/r/n";
			}
			
			for(PowerDevice dev : dycmlkgList){
				replaceStr += voltStationName+"@投入"+deviceName+"低后备保护跳"+CZPService.getService().getDevName(dev)+"功能/r/n";
			}
			
			//110kV东瓜变	投入110kV 1号主变间隙保护联跳35kV东田线373断路器功能
			
			for(PowerDevice dev : gycxlkgList){
				replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
			
			String kuohaonr = "其中";

			for(PowerDevice dev : zbgycdzList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					kuohaonr += CZPService.getService().getDevNum(dev)+"隔离开关操作至合上位置，";
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				kuohaonr += CZPService.getService().getDevNum(dev)+"、";
			}
			
			for(PowerDevice dev : zbdyckgList){
				kuohaonr += CZPService.getService().getDevNum(dev);
			}
			
			kuohaonr += "断路器均操作至热备用";
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += "将"+deviceName+"由冷备用转热备用（"+kuohaonr+"）/r/n";
			}

			//110kV勤丰变	检查1号主变110kV中性点1010接地开关在合上位置
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+StringUtils.killVoltInDevName(deviceName)+"充电/r/n";
				}
			}
			
			//110kV勤丰变	检查1号主变110kV中性点1010接地开关在拉开位置
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : otherzbzyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : otherzbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			//110kV勤丰变	35kV消弧线圈由运行转冷备用
//			110kV勤丰变	35kV消弧线圈由冷备用转接1号主变35kV侧中性点运行


			for(PowerDevice dev : gycxlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionLJ.getHhContent(dev,"丽江地调",stationName);
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += voltStationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += voltStationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}

		}
		
		return replaceStr;
	}

}
