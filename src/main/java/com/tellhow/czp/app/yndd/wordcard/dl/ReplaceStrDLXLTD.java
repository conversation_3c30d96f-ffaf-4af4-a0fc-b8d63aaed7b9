package com.tellhow.czp.app.yndd.wordcard.dl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDL;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.dl.JDKGXZDL;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDLXLTD implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("大理线路停电".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());

			List<Map<String, String>> stationLineList = CommonFunctionDL.getStationLineList(curDev);
			
			boolean isControl = true;

			for(PowerDevice dev : loadLineTrans){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionDL.ifSwitchControl(xlsw)||
							!CommonFunctionDL.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(sourceLineTrans!=null){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					if(!CommonFunctionDL.ifSwitchControl(xlsw)||
							!CommonFunctionDL.ifSwitchSeparateControl(xlsw)){
						isControl = false;
						break;
					}
				}
			}
			
			if(loadLineTrans.size() == 0){
				isControl = false;
			}
			
			if(stationLineList.size() > 0){
				isControl = false;
			}
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(sourceLineTrans, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			
			kgList.addAll(xlkgList);
			kgList.addAll(zbkgList);

			boolean isRunModelThreeTwo = false;
			
			for(PowerDevice dev : kgList){
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					isRunModelThreeTwo = true;
					break;
				}
			}
			
			for(PowerDevice dev : loadLineTrans){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				
				for(PowerDevice xlsw : xlswList){
					String beginstatus = RuleExeUtil.getDeviceBeginStatusContainNotOperate(xlsw);

					if(!beginstatus.equals("0")){
						String status = RuleExeUtil.getStatusNew(xlsw.getDeviceType(), beginstatus);
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(xlsw)+"已处"+status+"/r/n";
					}
				}
			}
			
			for(Map<String, String> map : stationLineList) {
				String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
				String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
				String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
				String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
				String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
				String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();
				String endpointkind = StringUtils.ObjToString(map.get("ENDPOINT_KIND")).trim();

				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += stationName+"@核实"+lineName+"可以停电/r/n";

					if(operationkind.equals("下令")&&!switchName.equals("")){
						replaceStr += stationName+"@断开"+lowerunit+switchName+"/r/n";
					}else if(operationkind.equals("许可")){
						if(endpointkind.equals("牵引变")){
							if(disconnectorName.contains("、")){
								String[] disconnectorNameArr = disconnectorName.split("、");
								
								for(String str : disconnectorNameArr){
									replaceStr += stationName+"@核实"+lowerunit+str+"在拉开位置/r/n";
								}
							}else{
								replaceStr += stationName+"@核实"+lowerunit+disconnectorName+"在拉开位置/r/n";
							}
						}else{
							if(!switchName.equals("")){
								replaceStr += stationName+"@核实"+lowerunit+switchName+"已处冷备用/r/n";
							}else{
								replaceStr += stationName+"@核实"+lowerunit+disconnectorName+"在拉开位置/r/n";
							}
						}
					}else if(operationkind.equals("配合")){
						replaceStr += stationName+"@核实"+lowerunit+switchName+"已处热备用/r/n";
					}
				}else{
					String checkLine = stationName+"@核实"+lineName+"可以停电/r/n";
					
					if(endpointkind.equals("带站用变用户")){
						replaceStr += stationName+"@核实"+lowerunit+"35kV#X站用变已处冷备用/r/n";
					}
					
					if(operationkind.equals("下令")){
						replaceStr += stationName+"@断开"+lowerunit+switchName+"/r/n";
					}else if(operationkind.equals("许可")){
						if(disconnectorName.equals("")){
							if(switchName.contains("熔断器")){
								replaceStr += stationName+"@核实"+lowerunit+switchName+"在拉开位置/r/n";
							}else{
								replaceStr += stationName+"@核实"+lowerunit+switchName+"已处冷备用/r/n";
							}
						}else{
							if(switchName.equals("")){
								replaceStr += stationName+"@核实"+lowerunit+disconnectorName+"在拉开位置/r/n";
							}else{
								if(switchName.contains("（三刀闸）")){
									switchName = switchName.replace("（三刀闸）", "");
									replaceStr += stationName+"@核实"+lowerunit+switchName+"已处冷备用/r/n";
								}else{
									replaceStr += stationName+"@核实"+lowerunit+switchName+"在分闸位置/r/n";
								}
								
								if(disconnectorName.contains("、")){
									String[] dzNameArr = disconnectorName.split("、");
									
									for(String dzName : dzNameArr){
										replaceStr += stationName+"@核实"+lowerunit+dzName+"在拉开位置/r/n";
									}
								}else{
									replaceStr += stationName+"@核实"+lowerunit+disconnectorName+"在拉开位置/r/n";
								}
							}
						}
						
						if(!replaceStr.contains(checkLine)){
							replaceStr += checkLine;
						}
					}
				}
			}
			
			if(isRunModelThreeTwo){
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					List<PowerDevice> xlkgLoadList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

					for(PowerDevice dev : xlkgLoadList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							String deviceName = CZPService.getService().getDevName(dev);
							
							if(stationName.contains("钢铁变")){
								replaceStr += stationName+"@断开"+deviceName+"/r/n";
							}else{
								replaceStr += "大理地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
								
								if(curDev.getPowerVoltGrade() > 110){
									List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
									dzList = RuleExeUtil.sortByXLC(dzList);
									
									for(PowerDevice dz : dzList){
										if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
											replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dz)+"在拉开位置/r/n";
										}
									}
								}
							}else{
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
									String deviceName = CZPService.getService().getDevName(dev);
									replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();

					if(operationkind.equals("下令")){
						replaceStr += stationName+"@将"+lowerunit+switchName+"由热备用转冷备用/r/n";
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制前的要求进行设置/r/n";
					
					
					for(PowerDevice dev : kgList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							
							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "大理地调@遥控拉开"+stationName+devname+"/r/n";
									replaceStr += stationName+"@核实"+devname+"在拉开位置/r/n";
								}
							}
						}
					}
					
					for(PowerDevice dev : kgList){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(dev)){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							
							for(PowerDevice zbdz : dzList){
								if(RuleExeUtil.getDeviceBeginStatus(zbdz).equals("0")){
									String devname = CZPService.getService().getDevName(zbdz);
									
									replaceStr += "大理地调@遥控拉开"+stationName+devname+"/r/n";
									replaceStr += stationName+"@核实"+devname+"在拉开位置/r/n";
								}
							}
						}
					}
					
					replaceStr += stationName+"@将"+stationName+CZPService.getService().getDevName(curDev)+"按远方控制后的要求进行设置/r/n";
				}
			}else if(isControl){
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								hignVoltMlkgList.add(dev);
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								hignVoltXlkgList.add(dev);
							}
						}
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(hignVoltXlkgList);
					tempList.addAll(hignVoltMlkgList);

					for(PowerDevice dev : tempList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
							replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								hignVoltMlkgList.add(dev);
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								hignVoltXlkgList.add(dev);
							}
						}
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(hignVoltXlkgList);
					tempList.addAll(hignVoltMlkgList);

					boolean ishh = false;
					
					for(PowerDevice dev : tempList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
							ishh = true;
							replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
						}
					}
					
					if(ishh){
						for(PowerDevice dev : tempList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
				}
				
				replaceStr += "大理地调@执行"+CZPService.getService().getDevName(curDev)+"由运行转冷备用程序操作/r/n";

				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
					
					for(PowerDevice xlsw : xlswList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
						
						if(xlsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							dzList = RuleExeUtil.sortByCZMXC(dzList);
						}else{
							if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
								dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							}else{
								dzList = RuleExeUtil.sortByMXC(dzList);
								Collections.reverse(dzList);
							}
						}
						replaceStr += CommonFunctionDL.getKnifeOffCheckContent(dzList, stationName);
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice xlsw : xlkgList){
						List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlsw, SystemConstants.SwitchSeparate);
						
						if(xlsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							dzList = RuleExeUtil.sortByCZMXC(dzList);
						}else{
							if(xlsw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
								dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
							}else{
								dzList = RuleExeUtil.sortByMXC(dzList);
								Collections.reverse(dzList);
							}
						}
						replaceStr += CommonFunctionDL.getKnifeOffCheckContent(dzList, stationName);
					}
				}
			}else{
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								hignVoltMlkgList.add(dev);
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								hignVoltXlkgList.add(dev);
							}
						}
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(hignVoltXlkgList);
					tempList.addAll(hignVoltMlkgList);

					for(PowerDevice dev : tempList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
							replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}
				
				if(curDev.getPowerVoltGrade() == 35){
					for(PowerDevice loadLineTran : loadLineTrans){
						PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
						String stationName = CZPService.getService().getDevName(station);
						
						if(station.getPowerVoltGrade() == 35){
							String bztContent = stationName+"@退出"+(int)loadLineTran.getPowerVoltGrade()+"kV备自投装置/r/n";

							if(!replaceStr.contains(bztContent)){
								replaceStr += bztContent;
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
					List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();

					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(loadLineTran.getPowerStationID());
					
					for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
						PowerDevice dev = it.next();
						
						if(dev.getPowerVoltGrade() == loadLineTran.getPowerVoltGrade()){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								hignVoltMlkgList.add(dev);
							}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								hignVoltXlkgList.add(dev);
							}
						}
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(hignVoltXlkgList);
					tempList.addAll(hignVoltMlkgList);

					for(PowerDevice dev : tempList){
						if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
							replaceStr += CommonFunctionDL.getHhContent(dev, "大理地调", stationName);
						}
					}
					
					for(PowerDevice dev : tempList){
						if(dev.getDeviceType().equals(SystemConstants.Switch)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice dev : xlkgList){
						if(dev.getDeviceType().equals(SystemConstants.Switch)){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								String deviceName = CZPService.getService().getDevName(dev);
								replaceStr += "大理地调@遥控断开"+stationName+deviceName+"/r/n";
							}
						}
					}
				}
				
				if(sourceLineTrans!=null){
					PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station); 
					
					for(PowerDevice dev : xlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							String deviceName = CZPService.getService().getDevName(dev);
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
								replaceStr += "大理地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
								replaceStr += CommonFunctionDL.getKnifeOffCheckContent(dzList, stationName);
							}else{
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
									if(curDev.getPowerVoltGrade() == 35){
										if(dzList.size() == 1){
											replaceStr += CommonFunctionDL.getKnifeOffContent(dzList, stationName);
										}else{
											replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
										}
									}else{
										replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
									}
								}
							}
							
							if(curDev.getPowerVoltGrade() == 35){//
								List<PowerDevice> linedzList = new ArrayList<PowerDevice>();
								
								for(PowerDevice dz : dzList){
									List<PowerDevice> dz3List = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);
									
									for(PowerDevice dz3 : dz3List){
										if(dz3.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
											linedzList.add(dz3);
											break;
										}
									}
								}
								
								replaceStr += CommonFunctionDL.getKnifeOffContent(linedzList, stationName);
							}
							
							if(curDev.getPowerVoltGrade() == 35){
								String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+sourceLineTrans.getPowerDeviceID()+"'";
								List<Map<String,String>> zybList = DBManager.queryForList(sql);
								
								for(Map<String,String> zybMap : zybList){
									replaceStr += stationName+"@将"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"由运行转冷备用/r/n";
									break;
								}
							}
						}
					}
				}
				
				for(PowerDevice loadLineTran : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(loadLineTran.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(loadLineTran, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
					for(PowerDevice dev : xlswList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							String deviceName = CZPService.getService().getDevName(dev);
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							if(CommonFunctionDL.ifSwitchSeparateControl(dev)){
								replaceStr += "大理地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
								replaceStr += CommonFunctionDL.getKnifeOffCheckContent(dzList, stationName);
							}else{
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
									if(curDev.getPowerVoltGrade() == 35){
										if(dzList.size() == 1){
											replaceStr += CommonFunctionDL.getKnifeOffContent(dzList, stationName);
										}else{
											replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
										}
									}else{
										replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
									}
								}
							}

							if(curDev.getPowerVoltGrade() == 35){
								List<PowerDevice> linedzList = new ArrayList<PowerDevice>();
								
								for(PowerDevice dz : dzList){
									List<PowerDevice> dz3List = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);
									
									for(PowerDevice dz3 : dz3List){
										if(dz3.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
											linedzList.add(dz3);
											break;
										}
									}
								}
								
								replaceStr += CommonFunctionDL.getKnifeOffContent(linedzList, stationName);
							}
							
							if(curDev.getPowerVoltGrade() == 35){
								String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+loadLineTran.getPowerDeviceID()+"'";
								List<Map<String,String>> zybList = DBManager.queryForList(sql);
								
								for(Map<String,String> zybMap : zybList){
									replaceStr += stationName+"@将"+StringUtils.ObjToString(zybMap.get("ZYB_NAME"))+"由运行转冷备用/r/n";
									break;
								}
							}
						}
					}
				}
				
				for(Map<String, String> map : stationLineList) {
					String stationName = StringUtils.ObjToString(map.get("UNIT")).trim();
					String lineName = StringUtils.ObjToString(map.get("LINE_NAME")).trim();
					String switchName = StringUtils.ObjToString(map.get("SWITCH_NAME")).trim();
					String disconnectorName = StringUtils.ObjToString(map.get("DISCONNECTOR_NAME")).trim();
					String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT")).trim();
					String operationkind = StringUtils.ObjToString(map.get("OPERATION_KIND")).trim();
					String endpointkind = StringUtils.ObjToString(map.get("ENDPOINT_KIND")).trim();

					if(operationkind.equals("下令")){
						if(!disconnectorName.equals("")){
							replaceStr += stationName+"@拉开"+lowerunit+disconnectorName+"/r/n";
						}
					}else if(operationkind.equals("许可")){
						
					}else if(operationkind.equals("配合")){
						
					}
				}
			}
			
			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("3")){
				String preStationName = "";
				
				for(Map<String,String> stationLineMap : stationLineList){
					String unit = StringUtils.ObjToString(stationLineMap.get("UNIT"));
					String linename = StringUtils.ObjToString(stationLineMap.get("LINE_NAME"));
					String lowerunit = StringUtils.ObjToString(stationLineMap.get("LOWERUNIT"));
					String grounddisconnectorname = StringUtils.ObjToString(stationLineMap.get("GROUNDDISCONNECTOR_NAME"));
					String endpointtype = StringUtils.ObjToString(stationLineMap.get("ENDPOINT_TYPE"));
					String switchName = StringUtils.ObjToString(stationLineMap.get("SWITCH_NAME")).trim();

					if(preStationName.equals("")){
						preStationName = unit;
					}else if(preStationName.equals(unit)){
						continue;
					}
					
					boolean zsccdx =  false;
					
					for(PowerDevice dev : JDKGXZDL.chooseEquips){
						if(dev.getPowerStationName().equals(unit)||dev.getPowerStationName().equals(lowerunit)){
							zsccdx = true;
							replaceStr += unit+"@在"+lowerunit+linename+"侧装设一组三相短路接地线/r/n";
							break;
						 }
					}
					
					if(!zsccdx){
						if(!grounddisconnectorname.equals("")){
							replaceStr += unit+"@合上"+lowerunit+grounddisconnectorname+"/r/n";
						}else{
							replaceStr += unit+"@在"+lowerunit+linename+"侧装设一组三相短路接地线/r/n";
						}
					}
				}
				
				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
					
					if(jddzList != null ){
						if(JDKGXZDL.chooseEquips.contains(dev) && jddzList.size() == 1){
							replaceStr += stationName+"@在"+CZPService.getService().getDevName(curDev)+"线路侧装设一组代替线路接地开关功能的三相短路接地线/r/n";
						}else if(JDKGXZDL.chooseEquips.contains(dev)||jddzList.size()==0){
							replaceStr += stationName+"@在"+CZPService.getService().getDevName(curDev)+"侧装设一组三相短路接地线/r/n";
						}else{
							replaceStr += stationName+"@合上"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
					 	}
					}
				}
				
				if(sourceLineTrans!=null){
					 PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());
					 String stationName = CZPService.getService().getDevName(station);
					
					 List<PowerDevice> jddzList = RuleExeUtil.getDeviceDirectList(sourceLineTrans, SystemConstants.SwitchFlowGroundLine);
					  
					 if(JDKGXZDL.chooseEquips.contains(sourceLineTrans) && jddzList.size() == 1){
						  replaceStr += stationName+"@在"+CZPService.getService().getDevName(curDev)+"线路侧装设一组代替线路接地开关功能的三相短路接地线/r/n";
					 }else if(JDKGXZDL.chooseEquips.contains(sourceLineTrans)||jddzList.size()==0){
						  replaceStr += stationName+"@在"+CZPService.getService().getDevName(curDev)+"侧装设一组三相短路接地线"+"/r/n";
					 }else{
						  replaceStr += stationName+"@合上"+CZPService.getService().getDevName(jddzList.get(0))+"/r/n";
					 }
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

	
	
}
