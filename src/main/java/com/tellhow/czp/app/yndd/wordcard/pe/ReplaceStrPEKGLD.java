package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionPE;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEKGLD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱开关冷倒".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);

			for(PowerDevice dev : mxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					 mlkgList =  RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice mlkg : mlkgList){
				List<PowerDevice> tempList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

				for(PowerDevice mx : tempList){
					List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);

					for(PowerDevice tempzb : tempzbList){
						if(!zbList.contains(tempzb)){
							zbList.add(tempzb);
						}
					}
				}
			}
			
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
				replaceStr += "普洱地调@遥控断开"+stationName+deviceName+"/r/n";
			}
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);

			boolean ifcontrol = false;
			
			for(PowerDevice dz : dzList){
				if(CommonFunctionPE.ifSwitchSeparateControl(dz)){
					ifcontrol = true;
				}
			}
			
			if(ifcontrol){
				replaceStr += "普洱地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
//				replaceStr += CommonFunctionPE.getKnifeOffCheckContent(curDev);
				String mxName = "";
				
				if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : tempmxList){
						mxName = "于"+CZPService.getService().getDevName(mx);
						break;
					}
				}
				
				replaceStr += "普洱地调@执行"+stationName+deviceName+"由冷备用转热备用"+mxName+"程序操作/r/n";
//				replaceStr += CommonFunctionPE.getKnifeOnCheckContent(curDev);
			}else{
				if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					String mxName = "";
					
					List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
					
					for(PowerDevice mx : tempmxList){
						mxName = "于"+CZPService.getService().getDevName(mx);
						break;
					}
					
					replaceStr += stationName+"@核实"+deviceName+"热备用于"+mxName+"/r/n";
				}
				
				dzList = RuleExeUtil.sortByXLC(dzList);
				
				for(PowerDevice dev : dzList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控拉开"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
						replaceStr += stationName+"@核实"+CommonFunctionPE.getSequentialDeviceName(dev)+"处于拉开位置/r/n";
					}
				}
				
				for(PowerDevice dev : dzList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "普洱地调@遥控合上"+stationName+CommonFunctionPE.getSequentialDeviceName(dev)+"/r/n";
						replaceStr += stationName+"@核实"+CommonFunctionPE.getSequentialDeviceName(dev)+"处于合上位置/r/n";
					}
				}
			}
			
			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("0")){
				replaceStr += "普洱地调@遥控合上"+stationName+deviceName+"/r/n";
			}
		}
		
		return replaceStr;
	}

}
