package com.tellhow.czp.app.yndd.wordcard.zt;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTKGHHDDBZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通开关合环调电备注".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			if(station.getPowerDeviceName().contains("高桥院")){
				replaceStr = "注：1、操作前注意核实环网断面满足要求，具备合环条件；2、操作后提醒110kV高桥院变110kV备自投装置适应当前运行方式";
			}else{
				replaceStr = "注：1、操作前注意核实环网断面满足要求，具备合环条件";
			}
		}
		
		return replaceStr;
	}

}
