package com.tellhow.czp.app.yndd.wordcard.qj;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJKGTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖开关停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			String begin = CBSystemConstants.getCurRBM().getBeginStatus();
			String end = CBSystemConstants.getCurRBM().getEndState();
			
			if(begin.equals("0")){
				if(end.equals("1")){
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
						replaceStr += "曲靖地调@遥控断开"+stationName+deviceName+"/r/n";
					}
				}else if(end.equals("2")){
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
						replaceStr += "曲靖地调@遥控断开"+stationName+deviceName+"/r/n";
					}

					if(curDev.getDeviceStatus().equals("2")){
						replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(curDev, stationName, station);
					}
				}
			}else if(begin.equals("1")){
				if(curDev.getDeviceStatus().equals("2")){
					replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(curDev, stationName, station);
				}
			}
		}
		
		return replaceStr;
	}

}
