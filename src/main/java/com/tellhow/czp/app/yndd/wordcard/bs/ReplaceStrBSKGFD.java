package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSKGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山开关复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev);
			String maintenanceName = CommonFunctionBS.getMaintenance(stationName);
			
			boolean ifSwitchControl = false;
			boolean ifSwitchSeparateControl = false;
			
			if(CommonFunctionBS.ifSwitchControl(curDev)){
				ifSwitchControl = true;
			}
			
			if(CommonFunctionBS.ifSwitchSeparateControl(curDev)){
				ifSwitchSeparateControl = true;
			}
			
			List<PowerDevice> zbzxdList = new ArrayList<PowerDevice>();
			
			if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) || curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)){
						zbzxdList.add(dev);
					}
				}				
			}
			
			if(curDev.getPowerVoltGrade()<500){
				for(PowerDevice dev : zbzxdList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "保山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				if(curDev.getDeviceStatus().equals("1")){
					if(maintenanceName.equals(stationName)){
						replaceStr += stationName+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
						replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
						replaceStr += stationName+"@核实"+deviceName+"一、二次设备运行正常/r/n";
					}else{
						replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
						replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
						replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
					}
				}else if(curDev.getDeviceStatus().equals("0")){
					if(curDev.getPowerVoltGrade() > 110){
						if(CommonFunctionBS.isContactLine(curDev)){
							if(maintenanceName.equals(stationName)){
								replaceStr += stationName+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
								replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
								replaceStr += stationName+"@核实"+deviceName+"一、二次设备运行正常/r/n";
							}else{
								replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
								replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
								replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
							}
						}else{
							if(maintenanceName.equals(stationName)){
								replaceStr += stationName+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
								replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								replaceStr += stationName+"@核实"+deviceName+"一、二次设备运行正常/r/n";
							}else{
								replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
								replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
								replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
							}
							
							if(curDev.getPowerVoltGrade() == 500){
								replaceStr += maintenanceName+"@核实"+stationName+deviceName+"具备遥控操作条件/r/n";
							}
							
							replaceStr += "保山地调@遥控用"+stationName+deviceName+"同期合环/r/n";
						}
					}else{
						if(ifSwitchControl && ifSwitchSeparateControl){
							if(maintenanceName.equals(stationName)){
								replaceStr += stationName+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
								replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
								replaceStr += stationName+"@核实"+deviceName+"一、二次设备运行正常/r/n";
							}else{
								replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
								replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
								replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
							}
						}else{
							
							
						}
					}
				}
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				if(maintenanceName.equals(stationName)){
					replaceStr += stationName+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
					replaceStr += "保山地调@执行"+stationName+deviceName+"由热备用转运行程序操作/r/n";
					replaceStr += stationName+"@核实"+deviceName+"一、二次设备运行正常/r/n";
				}else{
					replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
					replaceStr += "保山地调@执行"+stationName+deviceName+"由热备用转运行程序操作/r/n";
					replaceStr += maintenanceName+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
				}
			}
			
			if(curDev.getPowerVoltGrade()<500){
				for(PowerDevice dev : zbzxdList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "保山地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
