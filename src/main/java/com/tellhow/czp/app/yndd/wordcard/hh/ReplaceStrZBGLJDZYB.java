package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBGLJDZYB  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("主变关联接地站用变".equals(tempStr)){
			List<PowerDevice> zbdycmxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, "", true, true, true);
			List<PowerDevice>  jdbList = new ArrayList<PowerDevice>();

			if(zbdycmxList.size()>0){
				List<PowerDevice> zbdycswList = RuleExeUtil.getDeviceList(zbdycmxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				
				if(zbdycswList.size()>0){
					for(PowerDevice zbdycsw : zbdycswList){
						if(zbdycsw.getPowerDeviceName().contains("接地站用变")){
							jdbList.add(zbdycsw);
						}
					}
				}
			}
			
			String num = "";
			
			for(PowerDevice jdb : jdbList){
				String jdbName = jdb.getPowerDeviceName().substring(0, jdb.getPowerDeviceName().indexOf("接地站用变"));
				
				num += CZPService.getService().getDevNum(jdbName)+"、";
			}
			
			if(num.endsWith("、")){
				num =num.substring(0, num.length()-1);
			}
			
			replaceStr = "10kV"+num+"接地站用变";
			
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
