package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTCXLGQCDBH implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";
		
		if ("退出线路光纤差动保护".equals(tempStr)) {
			String curLineId = curDev.getPowerDeviceID();
			String sql = "SELECT UNIT,LINE_NAME,LOWERUNIT,SWITCH_NAME FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE  ISREMOVE = '0' AND  LINE_ID IN "
							+ "(  SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+curLineId+"')";
			List<Map<String, Object>> stations = DBManager.queryForList(sql);
			
			List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(curDev);
			
			for(PowerDevice line : lineList){
				PowerDevice station = CBSystemConstants.getPowerStation(line.getPowerStationID());
				String stationName = CZPService.getService().getDevName(station);
				
				List<PowerDevice> switchList =  RuleExeUtil.getLinkedSwitch(line);
				
				for(PowerDevice dev : switchList){
					replaceStr += stationName+"@退出"+CZPService.getService().getDevName(line)+CZPService.getService().getDevNum(dev)+"线路光纤差动保护/r/n";
				}
			}
			
			for(Map<String, Object> station:stations) {
				String stationName = StringUtils.ObjToString(station.get("UNIT")).trim();
				String lowerUnit = StringUtils.ObjToString(station.get("LOWERUNIT")).trim();
				String lineName = StringUtils.ObjToString(station.get("LINE_NAME")).trim();
				String switchNum = StringUtils.ObjToString(station.get("SWITCH_NAME")).trim();

				if(!lowerUnit.equals("")){
					replaceStr += lowerUnit+"@退出"+lineName+switchNum+"线路光纤差动保护/r/n";
				}else{
					replaceStr += stationName+"@退出"+lineName+"光纤差动保护/r/n";
				}
			}
		}
		
		return replaceStr;
	}
}
