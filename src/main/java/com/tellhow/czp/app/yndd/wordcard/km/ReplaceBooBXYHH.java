package com.tellhow.czp.app.yndd.wordcard.km;


import com.tellhow.czp.app.yndd.rule.km.JudgeLoopClosing;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooBXYHH implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("不需要合环".equals(tempStr)){
			if(JudgeLoopClosing.flag.equals("不需要合环")){
				return true;
			}
		}
        return false;
	}
}
