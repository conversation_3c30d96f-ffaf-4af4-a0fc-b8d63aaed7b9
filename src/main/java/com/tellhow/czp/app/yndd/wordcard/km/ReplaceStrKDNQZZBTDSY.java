package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.km.TransformTDKindChoose;
import com.tellhow.czp.app.yndd.rule.km.JudgeLoopClosing;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKDNQZZBTDSY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("扩大内桥中主变停电术语".equals(tempStr)){
			String stationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(curDev.getPowerStationID()));
			
			List<PowerDevice> gyckgList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true,true);
			List<PowerDevice> gycmlkgList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true,true);
			List<PowerDevice> zyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> dyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			
			if(dyckgList.size()>0){
				dycmlkgList = RuleExeUtil.getDeviceList(dyckgList.get(0),SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true);
			}
			
			if(zyckgList.size()>0){
				zycmlkgList = RuleExeUtil.getDeviceList(zyckgList.get(0),SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true);
			}
			
			boolean fnqkgrby = false;
			
			for(PowerDevice gyckg : gyckgList){
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gyckg).equals("1")){
					fnqkgrby = true;
				}
			}
			
			if(TransformTDKindChoose.tdflag.equals("母线一起停电")){
				
				if(JudgeLoopClosing.flag.equals("不能合环")){

					for(PowerDevice dev : dycmlkgList){
						replaceStr += "退出"+CZPService.getService().getDevName(dev)+"备自投装置/r/n";
					}
					
					replaceStr += "退出110kV备自投装置/r/n";
				}
				
				replaceStr += "落实"+CZPService.getService().getDevName(curDev)+"中性点1010接地开关已处合位/r/n";
				
				PowerDevice hsdycmlkg = new PowerDevice();
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						hsdycmlkg = dev;
					}
				}
				
				if(JudgeLoopClosing.flag.equals("能合环")){
					replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
					
					for(PowerDevice gyckg : gyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(gyckg).equals("1")){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gyckg)+"/r/n";
						}
					}
					
					for(PowerDevice gycmlkg : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("1")){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						String num = CZPService.getService().getDevNum(hsdycmlkg).replace("0", "1");
						
						if(!dev.getPowerDeviceName().contains(num)){
							replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}else if(JudgeLoopClosing.flag.equals("不能合环")){
					for(PowerDevice dev : gycmlkgList){
						String num = CZPService.getService().getDevNum(hsdycmlkg).replace("0", "1");
						
						if(!dev.getPowerDeviceName().contains(num)){
							replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice gyckg : gyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(gyckg).equals("1")){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gyckg)+"/r/n";
						}
					}
					
					for(PowerDevice gycmlkg : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("1")){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
						}
					}
				}
				
				replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(hsdycmlkg)+"/r/n";
				
				replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dyckgList)+"/r/n";

				for(PowerDevice dev : gycmlkgList){
					String num = CZPService.getService().getDevNum(hsdycmlkg).replace("0", "1");
					
					if(dev.getPowerDeviceName().contains(num)){
						replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(JudgeLoopClosing.flag.equals("能合环")){
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "退出"+CZPService.getService().getDevName(dev)+"备自投装置/r/n";
						}
					}
					
					replaceStr += "退出110kV备自投装置/r/n";
				}
				
				replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
				
				for(PowerDevice dev : gycmlkgList){
					replaceStr +=  "将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
				}
			}else{
				PowerDevice hsdycmlkg = new PowerDevice();
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						hsdycmlkg = dev;
					}
				}
				
				if(fnqkgrby){//非内桥开关在热备用
					if(JudgeLoopClosing.flag.equals("不能合环")){
						for(PowerDevice dev : dycmlkgList){
							replaceStr += "退出"+CZPService.getService().getDevName(dev)+"备自投装置/r/n";
						}
					}
					replaceStr += "落实"+CZPService.getService().getDevName(curDev)+"中性点1010接地开关已处合位/r/n";
					
					if(JudgeLoopClosing.flag.equals("能合环")){
						replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
						
						for(PowerDevice gyckg : gyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(gyckg).equals("1")){
								replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gyckg)+"/r/n";
							}
						}
						
						for(PowerDevice dev : gycmlkgList){
							String num = CZPService.getService().getDevNum(hsdycmlkg).replace("0", "1");
							
							if(!dev.getPowerDeviceName().contains(num)){
								replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
								break;
							}
						}
					}else if(JudgeLoopClosing.flag.equals("不能合环")){
						for(PowerDevice dev : gycmlkgList){
							String num = CZPService.getService().getDevNum(hsdycmlkg).replace("0", "1");
							
							if(!dev.getPowerDeviceName().contains(num)){
								replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
								break;
							}
						}
						
						for(PowerDevice gyckg : gyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(gyckg).equals("1")){
								replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(gyckg)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dyckgList)+"/r/n";

					
					for(PowerDevice dev : gycmlkgList){
						String num = CZPService.getService().getDevNum(hsdycmlkg).replace("0", "1");
						
						if(dev.getPowerDeviceName().contains(num)){
							replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							break;
						}
					}
					
					if(JudgeLoopClosing.flag.equals("能合环")){
						for(PowerDevice dev : dycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "退出"+CZPService.getService().getDevName(dev)+"备自投装置/r/n";
							}
						}
						
						for(PowerDevice dev : zycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "退出"+CZPService.getService().getDevName(dev)+"备自投装置/r/n";
							}
						}
					}
					
					
					replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}else{
					if(JudgeLoopClosing.flag.equals("能合环")){
						replaceStr += "云南省调@落实XXkVXX变XXkV母线与XXkVXX变XXkV母线为同期系统/r/n";
					}
					
					replaceStr += "落实"+CZPService.getService().getDevName(curDev)+"中性点1010接地开关已处合位/r/n";
					
					List<PowerDevice> zdycmlkgList = new ArrayList<PowerDevice>();
					
					zdycmlkgList.addAll(zycmlkgList);
					zdycmlkgList.addAll(dycmlkgList);
					
					if(JudgeLoopClosing.flag.equals("能合环")){
						for(PowerDevice dev : zycmlkgList){
							List<PowerDevice> zbList = new ArrayList<PowerDevice>();
									
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								List<PowerDevice> tempdycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

								for(PowerDevice tempdycmx : tempdycmxList){
									List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(tempdycmx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
									
									zbList.addAll(tempzbList);
								}
							}
							
							List<PowerDevice> tempgycmlkgList = RuleExeUtil.getTransformersLinkDevice(zbList.get(0),zbList.get(1));
							
							for(PowerDevice tempgycmlkg : tempgycmlkgList){
								if(tempgycmlkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									if(RuleExeUtil.getDeviceBeginStatus(tempgycmlkg).equals("1")){
										replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(tempgycmlkg)+"/r/n";

										for(PowerDevice gycmlkg : gycmlkgList){
											if(!gycmlkg.getPowerDeviceID().equals(tempgycmlkg.getPowerDeviceID())){
												replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
											}
										}
									}
								}
							}
						}
						
						for(PowerDevice dev : zycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
						
						for(PowerDevice dev : dycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
						
						for(PowerDevice dev : dyckgList){
							replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
						
						for(PowerDevice dev : zyckgList){
							replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
						
						for(PowerDevice dev : gycmlkgList){
							String num = CZPService.getService().getDevNum(hsdycmlkg).replace("0", "1");
							
							if(dev.getPowerDeviceName().contains(num)){
								replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
							}
						}
					}else{
						for(PowerDevice gycmlkg : gycmlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("0")){
								replaceStr += "昆明地调@遥控断开"+stationName+CZPService.getService().getDevName(gycmlkg)+"/r/n";
							}
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "退出"+CZPService.getService().getDevName(dev)+"备自投装置/r/n";
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "退出"+CZPService.getService().getDevName(dev)+"备自投装置/r/n";
						}
					}
					
					replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由热备用转冷备用/r/n";
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += "昆明地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
				}
			}
		}
		return replaceStr;
	}

}
