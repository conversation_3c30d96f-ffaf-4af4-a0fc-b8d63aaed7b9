package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHDRQTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河电容器停电".equals(tempStr)){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",false, true, true, false);
			List<PowerDevice> kgdzList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : kgList){
				kgdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			}
			
			if(qtdzList.size()>2){//1、电容器开关直接连接2把手车2、电容器开关直接连接2把刀闸
				replaceStr += "核实"+CZPService.getService().getDevName(kgList)+"热备用/r/n";

				boolean isxcdz = false;
				
				for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
					PowerDevice dev = (PowerDevice)it2.next();
					if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						isxcdz = true;
						break;
					}
				}
				
				if(isxcdz){
					for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
						PowerDevice dev = (PowerDevice)it2.next();
						if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC))
							it2.remove();
					}
					
					replaceStr += "核实"+CZPService.getService().getDevName(qtdzList)+"在合闸位置/r/n";
					replaceStr += "拉开"+CZPService.getService().getDevName(qtdzList)+"/r/n";
					replaceStr += "将"+CZPService.getService().getDevName(kgList)+"由热备用转冷备用/r/n";
				}else{
					for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
						PowerDevice dev = (PowerDevice)it2.next();
						if(kgdzList.contains(dev))
							it2.remove();
					}
					
					replaceStr += "将"+CZPService.getService().getDevName(kgList)+"由热备用转冷备用/r/n";
					replaceStr += "核实"+CZPService.getService().getDevName(qtdzList)+"在拉开位置/r/n";

				}
			}else if(qtdzList.size() == 2){//1、电容器开关直接连接1把手车2、正常接线
				replaceStr += "核实"+CZPService.getService().getDevName(kgList)+"热备用/r/n";

				boolean isxcdz = false;
				
				for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
					PowerDevice dev = (PowerDevice)it2.next();
					if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						isxcdz = true;
						break;
					}
				}
				
				if(isxcdz){
					for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
						PowerDevice dev = (PowerDevice)it2.next();
						if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC))
							it2.remove();
					}
					
					replaceStr += "核实"+CZPService.getService().getDevName(qtdzList)+"在合闸位置/r/n";
					replaceStr += "拉开"+CZPService.getService().getDevName(qtdzList)+"/r/n";
					replaceStr += "将"+CZPService.getService().getDevName(kgList)+"由热备用转冷备用/r/n";
				}else{
					replaceStr += "将"+CZPService.getService().getDevName(kgList)+"由热备用转冷备用/r/n";
				}
			}else if(qtdzList.size() == 1){//虹溪变，非标准接线
				replaceStr += "核实"+CZPService.getService().getDevName(kgList).replace("及", "")+"在分闸位置/r/n";
				replaceStr += "核实"+CZPService.getService().getDevName(qtdzList)+"在合闸位置/r/n";
				replaceStr += "拉开"+CZPService.getService().getDevName(qtdzList)+"/r/n";
				replaceStr += "核实"+CZPService.getService().getDevName(curDev)+"冷备用/r/n";
			}

			if(replaceStr.equals("")){
				return null;
			}
		}
		return replaceStr;
	}

}