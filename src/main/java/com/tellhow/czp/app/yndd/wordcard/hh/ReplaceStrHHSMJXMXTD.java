package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.*;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

import javax.enterprise.inject.Stereotype;

public class ReplaceStrHHSMJXMXTD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
		String stationName = CZPService.getService().getDevName(station);
		String deviceName = CZPService.getService().getDevName(curDev);
		if("红河双母接线母线停电".equals(tempStr)){

			List<PowerDevice> adjacentBuses = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine,
					SystemConstants.Switch, true, true, true);
			adjacentBuses.add(stationDev);
			RuleExeUtil.swapDeviceList(adjacentBuses);
			ReplaceStrHHMXMC replaceStrHHMXMC = new ReplaceStrHHMXMC();
			String currentMxName = replaceStrHHMXMC.strReplace("红河母线名称", curDev, stationDev, desc);
			if (currentMxName.isEmpty()) currentMxName = deviceName;

            List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
			Map<PowerDevice, String> deviceToBusMap = new HashMap<PowerDevice, String>();	// 设备及其所属母线
			for (PowerDevice bus : adjacentBuses) {
				String busName = CZPService.getService().getDevName(bus);
				List<PowerDevice> tempList = RuleExeUtil.getDeviceList(bus, SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "",
						false, true, true, true);
				for (PowerDevice dev : tempList) {
					deviceToBusMap.put(dev, busName);
				}
				xlkgList.addAll(tempList);
			}
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>(RuleExeUtil.getDeviceList(stationDev, SystemConstants.Switch,
                    SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "",
                    false, true, false, true));
            List<PowerDevice> plkgList = new ArrayList<PowerDevice>(RuleExeUtil.getDeviceList(stationDev, SystemConstants.Switch,
                    SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL + "," + CBSystemConstants.RunTypeSwitchMLPL,
                    "", false, true, true, true));
            List<PowerDevice> qtdzList = new ArrayList<PowerDevice>(RuleExeUtil.getDeviceList(stationDev, SystemConstants.SwitchSeparate,
                    SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, CBSystemConstants.RunTypeSideMother,
                    true, true, true, true));

            List<PowerDevice> othermxList = new ArrayList<PowerDevice>();
			for (PowerDevice mlkg : mlkgList) {
				// 只取母联开关
				if (RuleExeUtil.isSwitchDoubleML(mlkg)) {
					List<PowerDevice> tempOtherMxList = czprule.rule.operationclass.RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine,
							SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother,
							false, true, true, true);
					tempOtherMxList.remove(stationDev);
					for (PowerDevice otherMx : tempOtherMxList) {
						List<PowerDevice> tempAdjBusList = CommonFunctionHH.isTripleBusZeroBranch(otherMx);
						if (tempAdjBusList != null && !new HashSet<PowerDevice>(tempOtherMxList).containsAll(tempAdjBusList)) {
							othermxList.addAll(tempAdjBusList);
						}
					}
					othermxList.addAll(tempOtherMxList);
				}
			}
			RuleExeUtil.swapDeviceList(othermxList);
			// 如果即有母联开关又有分段开关，则认为是双母双分段类型
			boolean isSmSfd = CommonFunctionHH.isDoubleBusDoubleBranch(mlkgList);	// 双母双分段

			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();

			//电源侧
			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "",
						false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "",
						false, true, true, true);
			}
			// 统一获取一遍设备的名称，存入map中，避免后续反复获取
			HashMap<PowerDevice, String> devNameMap = new HashMap<PowerDevice, String>();

			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			CommonFunctionHH.categorizeSwitch(xlkgList, yxkgList, rbykgList, devNameMap);
			CommonFunctionHH.categorizeSwitch(zbkgList, yxkgList, rbykgList, devNameMap);
			CommonFunctionHH.categorizeSwitch(plkgList, yxkgList, rbykgList, devNameMap);

			RuleExeUtil.swapDeviceList(yxkgList);
			RuleExeUtil.swapDeviceList(rbykgList);

			for (PowerDevice mlkg : mlkgList) {
				CommonFunctionHH.getDeviceNameAndStoreInMap(mlkg, devNameMap);
			}
			Map<PowerDevice, List<PowerDevice>> busToSwitchMap = new HashMap<PowerDevice, List<PowerDevice>>();
			for (PowerDevice othermx : othermxList) {
				CommonFunctionHH.getDeviceNameAndStoreInMap(othermx, devNameMap);
				List<PowerDevice> swList = RuleExeUtil.getDeviceList(othermx, SystemConstants.Switch,
						SystemConstants.PowerTransformer, false, true, true);
				busToSwitchMap.put(othermx, swList);
			}


			String originalMxName = deviceName;	// 初始母线名称
			for(PowerDevice dev : othermxList){
				for(PowerDevice rbykg : rbykgList){
					if (!busToSwitchMap.get(dev).contains(rbykg)) continue;
					if(CommonFunctionHH.ifSwitchSeparateControl(rbykg)){
						if (deviceToBusMap.containsKey(rbykg)) {
							originalMxName = deviceToBusMap.get(rbykg);
						} else {
							originalMxName = deviceName;
						}
						replaceStr += "红河地调@执行"+stationName+devNameMap.get(rbykg)+"由"+originalMxName+"热备用倒至"+devNameMap.get(dev)+"热备用程序操作/r/n";
					}
				}
			}
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice rbykg : rbykgList){
					if (!busToSwitchMap.get(dev).contains(rbykg)) continue;
					if(!CommonFunctionHH.ifSwitchSeparateControl(rbykg)){
						if (deviceToBusMap.containsKey(rbykg)) {
							originalMxName = deviceToBusMap.get(rbykg);
						} else {
							originalMxName = deviceName;
						}
						replaceStr += stationName+"@将"+devNameMap.get(rbykg)+"由"+originalMxName+"热备用倒至"+devNameMap.get(dev)+"热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : mlkgList){
                if (RuleExeUtil.isSwitchDoubleML(dev)) {
                    replaceStr += stationName+"@确认"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按运行操作手册执行，"+devNameMap.get(dev)+"操作电源已断开，具备倒母线操作条件/r/n";
                }
            }
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice yxkg : yxkgList){
					if (!busToSwitchMap.get(dev).contains(yxkg)) continue;
                    if (CommonFunctionHH.ifSwitchSeparateControl(yxkg) && CommonFunctionHH.isTransSwitch(yxkg)) {
                        if (deviceToBusMap.containsKey(yxkg)) {
                            originalMxName = deviceToBusMap.get(yxkg);
                        } else {
                            originalMxName = deviceName;
                        }
                        replaceStr += "红河地调@执行" + stationName + devNameMap.get(yxkg) + "由" + originalMxName + "运行倒至" + devNameMap.get(dev) + "运行程序操作/r/n";
                    }
				}
			}
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice yxkg : yxkgList){
					if (!busToSwitchMap.get(dev).contains(yxkg)) continue;
                    if (CommonFunctionHH.ifSwitchSeparateControl(yxkg) && !CommonFunctionHH.isTransSwitch(yxkg)) {
                        if (deviceToBusMap.containsKey(yxkg)) {
                            originalMxName = deviceToBusMap.get(yxkg);
                        } else {
                            originalMxName = deviceName;
                        }
                        replaceStr += "红河地调@执行" + stationName + devNameMap.get(yxkg) + "由" + originalMxName + "运行倒至" + devNameMap.get(dev) + "运行程序操作/r/n";
                    }
				}
			}
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice yxkg : yxkgList){
					if (!busToSwitchMap.get(dev).contains(yxkg)) continue;
                    if (!CommonFunctionHH.ifSwitchSeparateControl(yxkg) && CommonFunctionHH.isTransSwitch(yxkg)) {
                        if (deviceToBusMap.containsKey(yxkg)) {
                            originalMxName = deviceToBusMap.get(yxkg);
                        } else {
                            originalMxName = deviceName;
                        }
                        replaceStr += stationName + "@将" + devNameMap.get(yxkg) + "由" + originalMxName + "运行倒至" + devNameMap.get(dev) + "运行/r/n";
                    }
				}
			}
			
			for(PowerDevice dev : othermxList){
				for(PowerDevice yxkg : yxkgList){
					if (!busToSwitchMap.get(dev).contains(yxkg)) continue;
                    if (!CommonFunctionHH.ifSwitchSeparateControl(yxkg) && !CommonFunctionHH.isTransSwitch(yxkg)) {
                        if (deviceToBusMap.containsKey(yxkg)) {
                            originalMxName = deviceToBusMap.get(yxkg);
                        } else {
                            originalMxName = deviceName;
                        }
                        replaceStr += stationName + "@将" + devNameMap.get(yxkg) + "由" + originalMxName + "运行倒至" + devNameMap.get(dev) + "运行/r/n";
                    }
				}
			}
			
			for(PowerDevice dev : mlkgList){
                if (RuleExeUtil.isSwitchDoubleML(dev)) {
                    if (isSmSfd) {
                        replaceStr += stationName+"@确认"+currentMxName+"已腾空，"+devNameMap.get(dev)+"操作电源已投入，"
                                +(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程要求执行，互联压板已退出/r/n";
                    } else {
						replaceStr += stationName+"@确认"+currentMxName+"已腾空，"+devNameMap.get(dev)+"操作电源已投入，互联压板已退出，"
								+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程要求执行，"+deviceName+"电压互感器二次侧空气开关已断开/r/n";
                    }
                }
            }

			// 双母双分段接线停母线，先断分段开关，再断母线TV二次空气开关，拉开母线TV隔离开关,断开母联断路器
			if (isSmSfd) {
				for (PowerDevice dev : mlkgList) {
					if (!RuleExeUtil.isSwitchDoubleML(dev) && RuleExeUtil.isDeviceHadStatus(dev, "0", "1")) {
						replaceStr += stationName+"@执行"+stationName+devNameMap.get(dev)+"由运行转热备用程序操作/r/n";
						replaceStr += stationName+"@确认"+deviceName+"TV二次侧空气开关已断开/r/n";
					}
				}
			}
			
			List<PowerDevice> ptdzList = new ArrayList<PowerDevice>();
			
			for(PowerDevice qtdz : qtdzList){
				if(RuleExeUtil.getDeviceBeginStatus(qtdz).equals("0")){
					ptdzList.add(qtdz);
				}
			}
			
			if(!ptdzList.isEmpty()){
				replaceStr += CommonFunctionHH.getKnifeOffContent(ptdzList, stationName);
			}

			if(curDev.getDeviceStatus().equals("1")){
				replaceStr += "红河地调@执行"+stationName+currentMxName+"由运行转热备用程序操作/r/n";
			}else if(curDev.getDeviceStatus().equals("2")){
				replaceStr += "红河地调@执行"+stationName+currentMxName+"由运行转冷备用程序操作/r/n";
			}
		} else if ("红河双母接线母线停电备注".equals(tempStr)) {
			PowerDevice other = RuleExeUtil.getMLDoubleOther(stationDev);
			ReplaceStrHHMXMC replaceStrHHMXMC = new ReplaceStrHHMXMC();
			String currentMxName = replaceStrHHMXMC.strReplace("红河母线名称", curDev, stationDev, desc);
			String otherMxName = replaceStrHHMXMC.strReplace("红河母线名称", other, stationDev, desc);
			replaceStr += "双母接线备注：执行第2～10条指令过程中，在合上" + otherMxName + "侧隔离开关形成设备双跨母线状态时，调控员需检查母联开关三相电流情况，" +
					"确认母联断路器不存在明显三相电流不平衡情况后，方可拉开" + currentMxName + "侧隔离开关。";
		}
		return replaceStr;
	}





}
