package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEJDBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱接地变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);
			String jdbName = "";

			for(PowerDevice dev : kgList){
				String deviceName = CZPService.getService().getDevName(dev);
				
				replaceStr += "普洱地调@遥控断开"+stationName+deviceName+"/r/n";
				
				if(deviceName.contains("接地变")){
					jdbName = deviceName.substring(0, deviceName.indexOf("接地变")+3);
				}
			}
			
			replaceStr += stationName+"@"+jdbName+"由热备用转冷备用/r/n";
		}
		
		return replaceStr;
	}

}
