package com.tellhow.czp.app.yndd.view;


import java.awt.BorderLayout;
import java.awt.Button;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.MouseEvent;
import java.util.List;

import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableModel;

import org.beryl.gui.swing.JBreakingLabel;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.app.yndd.dao.RealtimeData;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.user.UserDao;
import com.tellhow.czp.util.GUIUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;



public class RealtimeDataDialog extends javax.swing.JDialog {
	private DefaultTableModel dTableModel;
	private JPanel topPanel;//查询条件及按钮面板
	private JPanel mainPanel;//信息面板
	private JButton searchButton;//查询按钮
	private JComboBox jComboBox;//厂站搜索下拉框
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable jTableInfo;//信息列表



	public RealtimeDataDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setTitle("实时数据检测");
		setLocationCenter();
//		initTable();
	}

	/**
	 * 初始化表格  传入参数关键字
	 */
	public void initTable(String stationId) {
	
		RealtimeData realtimeData = new RealtimeData();
		List<RealtimeData> result = realtimeData.getData(stationId);
		dTableModel.setRowCount(0);
		for (int i = 0; i < result.size(); i++) {
			RealtimeData ret = result.get(i);
			Object[] rowData = {ret.getEquipid(),i+1,ret.getCzmc() ,ret.getType(),ret.getEquipname(),ret.getState()};
			dTableModel.addRow(rowData);
		}
		jTableInfo.setModel(dTableModel);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}


	private void initComponents() {
		this.setLayout(new BorderLayout());
		this.setSize(700, 480);
		topPanel =new JPanel();
		topPanel.setPreferredSize(new Dimension(0,45));
		this.add(topPanel,BorderLayout.NORTH);
		mainPanel =new JPanel();
		this.add(mainPanel,BorderLayout.CENTER);
		
		JLabel label1 = new JLabel("厂站:");
		
		jComboBox = new JAutoCompleteComboBox();
		jComboBox.setPreferredSize(new Dimension(160,22));
		String sql = CZPImpl.getPropertyValue("StationSearchSql");
		if(sql == null)
			sql = OPEService.getService().TransTreeWidgetSql1();
		sql=sql.replaceAll("equip\\.", CBSystemConstants.equipUser+"\\.");
		GUIUtil.fillComboBox(jComboBox, sql);
		jComboBox.setSelectedIndex(-1);
		
		
		topPanel.setLayout(new FlowLayout(FlowLayout.LEFT,10,10));

		searchButton =new JButton("查询");
		searchButton.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				searchButtonActionPerformed(evt);
			}
		});
	
		JLabel labelx = new JLabel("");
		labelx.setPreferredSize(new Dimension(20,0));
		topPanel.add(labelx);
		topPanel.add(label1);
		topPanel.add(jComboBox);
		topPanel.add(searchButton);

		

		
		dTableModel = new DefaultTableModel(null,new String[] { "ID","序号", "厂站名称","设备类型","设备名称","设备状态"}){
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		jTableInfo = new JTable();
		jTableInfo.setModel(dTableModel);
//		jTableInfo.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
		
		jTableInfo.getColumnModel().getColumn(0).setMinWidth(0);
		jTableInfo.getColumnModel().getColumn(0).setMaxWidth(0);
		jTableInfo.getColumnModel().getColumn(1).setPreferredWidth(0);
		jTableInfo.getColumnModel().getColumn(1).setPreferredWidth(40);
		jTableInfo.getColumnModel().getColumn(2).setPreferredWidth(80);
		jTableInfo.getColumnModel().getColumn(3).setPreferredWidth(80);
		jTableInfo.getColumnModel().getColumn(4).setPreferredWidth(260);
		jTableInfo.getColumnModel().getColumn(5).setPreferredWidth(80);
		
		jTableInfo.setRowHeight(26);
		jScrollPane1 = new JScrollPane(jTableInfo);
		jScrollPane1.setPreferredSize(new Dimension(600,370));
		jScrollPane1.setFont(new java.awt.Font("宋体", 0, 13));
		mainPanel.add(jScrollPane1,BorderLayout.CENTER);
		

		
	}



	//查询
		private void searchButtonActionPerformed(java.awt.event.ActionEvent evt) {
			CodeNameModel codeNameModel = (CodeNameModel)jComboBox.getSelectedItem();
			this.initTable(codeNameModel.getCode());
		}

	
	


	



}
