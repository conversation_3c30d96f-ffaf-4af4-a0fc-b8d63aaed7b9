package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Toolkit;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.ListSelectionModel;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;
import javax.swing.table.TableModel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tellhow.czp.app.yndd.dao.TicketDBManager;
import com.tellhow.czp.app.yndd.impl.WebServiceUtil;
import com.tellhow.czp.app.yndd.tool.Client;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.mainframe.JPopupTextField;
import com.tellhow.czp.operationcard.ExcelAdapter;
import com.tellhow.czp.operationcard.OperateTicketDXPMX;
import com.tellhow.czp.operationcard.ReplaceWord;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.replaceclass.impl.ReplaceStrDYDJ;
/**
 * @典型票明细展示
 * <AUTHOR>
 * 修改：殷旺旺
 */
public class OperateTicketCZPMXYNDD extends OperateTicketDXPMX {
	private DefaultTableModel jTable2Model = null;
	private SetJTableProtery sjp2 = new SetJTableProtery();
	private javax.swing.JScrollPane jScrollPanefj;
	private JTabbedPane jTabbedpane = new JTabbedPane();// 存放选项卡的组件 

	public OperateTicketCZPMXYNDD() {
		super(SystemConstants.getMainFrame(),"操作票编辑");
	}
	
	public void init(java.awt.Frame parent, CodeNameModel cnm) {
		//super(parent, "操作票编辑", true);
		this.cnm = cnm;
		initComponents();
		setLocationCenter();
		init();
	}
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	private void inittable(){
		if(jTable1Model!=null){
			int n=jTable1Model.getRowCount();
			for(int i=0;i<n;i++){
				jTable1Model.getValueAt(i, 0);
				
				String stage = "阶段"+RuleExeUtil.int2chineseNum(Integer.valueOf(i+1));

				jTable1Model.setValueAt(stage, i, 0);
				jTable1Model.setValueAt(i+1, i, 1);

			}
		}
	}
	private void init() {
		this.jTextArea1.setText(this.cnm.getName().split(";")[0].toString());
		if (jTable1Model == null) {
			Object[][] tableData = null;
			jTable1Model = new DefaultTableModel(tableData, new String[] {
					"操作票ID","阶段","序号","操作单位","操作项目"}) {
			};
		}
		else{
			while(jTable1Model.getRowCount() != 0)
				jTable1Model.removeRow(jTable1Model.getRowCount()-1);
		}
		
		TicketDBManager tdb = new TicketDBManager();
		
		String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		if(zbresults!=null){
			if(zbresults.length>0){
				this.jTextArea2.setText(zbresults[11]);
				
				if(zbresults[8].equals("1")){
					cmb.setSelectedItem("逐项票");
				}else if(zbresults[8].equals("2")){
					cmb.setSelectedItem("综合票");
				}else if(zbresults[8].equals("3")){
					if(CBSystemConstants.opcardUser.equals("OPCARDYX.")){
						cmb.setSelectedItem("监控逐项票");
					}else{
						cmb.setSelectedItem("监控票");
					}
				}else if(zbresults[8].equals("4")){
					cmb.setSelectedItem("顺控票");
				}else if(zbresults[8].equals("5")){
					cmb.setSelectedItem("书面命令");
				}else if(zbresults[8].equals("6")){
					cmb.setSelectedItem("投产指令");
				}
			}
		}
		
		List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
		String[] tempStr = null;
		for (int i = 0; i < results.size(); i++) {
			tempStr = results.get(i);
			
			String stage = "阶段"+RuleExeUtil.int2chineseNum(Integer.valueOf(tempStr[1]));
			
			Object[] rowData = {tempStr[0],stage,tempStr[3], tempStr[2], tempStr[4]};
			jTable1Model.addRow(rowData);
		}

		if (jTable2Model == null)
		{
			jTable2Model = new DefaultTableModel(null, new String[] 
					
			  {"序列ID","序号", "操作单位", "操作内容","设备ID","设备名称","所属厂站","目标状态","是否顺控","关联ID","操作票顺序"});
		}
		
		List<String[]> resultsk = tdb.queryDXTicketSK(this.cnm.getCode());

		String[] tempStrSk = null;
		for (int i = 0; i < resultsk.size(); i++) {
			tempStrSk = resultsk.get(i);
			
			String czpsx = "";
			
			for (String[] arr : results) {
				if(arr[0].equals(tempStrSk[9])){
					czpsx = arr[3];
				}
			}
			
			String ssyk = "";
			
			if(tempStrSk[8].equals("1")){
				ssyk = "是";
			}else if(tempStrSk[8].equals("0")){
				ssyk = "否";
			}
			
			Object[] rowData = {tempStrSk[0],tempStrSk[1], tempStrSk[2], tempStrSk[3],tempStrSk[4], tempStrSk[5], tempStrSk[6],tempStrSk[7],ssyk, tempStrSk[9],czpsx};
			jTable2Model.addRow(rowData);
		}
		
		jTable2.setModel(jTable2Model);
		
		sjp2.makeFace(jTable2);
		sjp2.getTableHeader(jTable2);//列名居中
		
		jTable2.getColumnModel().getColumn(0).setMinWidth(0);
		jTable2.getColumnModel().getColumn(0).setMaxWidth(0);
		jTable2.getColumnModel().getColumn(1).setMinWidth(50);
		jTable2.getColumnModel().getColumn(1).setMaxWidth(60);
		jTable2.getColumnModel().getColumn(2).setMinWidth(120);
		jTable2.getColumnModel().getColumn(2).setMaxWidth(150);
		jTable2.getColumnModel().getColumn(4).setMinWidth(130);
		jTable2.getColumnModel().getColumn(4).setMaxWidth(150);
		jTable2.getColumnModel().getColumn(5).setMinWidth(0);
		jTable2.getColumnModel().getColumn(5).setMaxWidth(0);
		jTable2.getColumnModel().getColumn(6).setMinWidth(100);
		jTable2.getColumnModel().getColumn(6).setMaxWidth(120);
		jTable2.getColumnModel().getColumn(7).setMinWidth(70);
		jTable2.getColumnModel().getColumn(7).setMaxWidth(80);
		jTable2.getColumnModel().getColumn(8).setMinWidth(70);
		jTable2.getColumnModel().getColumn(8).setMaxWidth(80);
		jTable2.getColumnModel().getColumn(9).setMinWidth(0);
		jTable2.getColumnModel().getColumn(9).setMaxWidth(0);
		jTable2.getColumnModel().getColumn(10).setMinWidth(0);
		jTable2.getColumnModel().getColumn(10).setMaxWidth(0);
		
		jTable1.setModel(jTable1Model);
		List<Integer> columnList = new ArrayList<Integer>();
		columnList.add(0);
		columnList.add(1);
		columnList.add(2);
		sjp.getTableHeader(jTable1);//列名居中
		sjp.makeFaceCenter(jTable1,columnList);
		sjp.getTableHeader(jTable2);//列名居中
		sjp.makeFaceCenter(jTable2,columnList);
		
		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMinWidth(0);
		tcm.getColumn(0).setMaxWidth(0);
		tcm.getColumn(1).setMinWidth(100);
		tcm.getColumn(1).setMaxWidth(110);
		tcm.getColumn(2).setMinWidth(70);
		tcm.getColumn(2).setMaxWidth(80);
		tcm.getColumn(3).setMinWidth(120);
		tcm.getColumn(3).setMaxWidth(130);
		
		JAutoCompleteComboBox comboBox2 = new JAutoCompleteComboBox();
		comboBox2.setFont(new Font("宋体",Font.PLAIN,14));
        DefaultComboBoxModel model2 = new DefaultComboBoxModel();
        
        for(int i=1;i<31;i++) {
        	CodeNameModel codeNameModel=new CodeNameModel(String.valueOf(i),"阶段"+RuleExeUtil.int2chineseNum(Integer.valueOf(i)));
    		model2.addElement(codeNameModel);
        }
	
		comboBox2.setModel(model2);
		DefaultCellEditor tranTypeEditor2 = new DefaultCellEditor(comboBox2);
		tranTypeEditor2.setClickCountToStart(2);
		jTable1.getColumnModel().getColumn(1).setCellEditor(tranTypeEditor2);
		
		//单位下拉框
		TicketDBManager ticketDB = new TicketDBManager();
		JAutoCompleteComboBox comboBox = new JAutoCompleteComboBox();
		comboBox.setFont(new Font("宋体", Font.PLAIN, 14));
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		 List<String> sldws = new ArrayList<String>();
	        sldws=ticketDB.getSLDW();
	        for (String string : sldws) {
	        	CodeNameModel codeNameModel=new CodeNameModel(string,string);
	        	model.addElement(codeNameModel);
			}
	        comboBox.setModel(model);
		DefaultCellEditor tranTypeEditor = new DefaultCellEditor(comboBox);
		tranTypeEditor.setClickCountToStart(2);
		jTable1.getColumnModel().getColumn(3).setCellEditor(tranTypeEditor);
	}
	private void initComponents() {
		setBounds(500, 500, 991, 657);
		getContentPane().setLayout(new BorderLayout());
		//setResizable(false);
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		getContentPane().add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(new BorderLayout(0, 0));
		{
			JPanel panel = new JPanel();
			contentPanel.add(panel, BorderLayout.NORTH);
			panel.setLayout(new BorderLayout(0, 0));
			{
				JLabel label = new JLabel("\u64CD\u4F5C\u4EFB\u52A1\uFF1A");
				panel.add(label, BorderLayout.WEST);
			}
			{
				jScrollPane2 = new JScrollPane();
				panel.add(jScrollPane2, BorderLayout.CENTER);
				{
					jTextArea1 = new JTextArea();
					jTextArea1.setColumns(20);
					jTextArea1.setPreferredSize(new Dimension(10, 50));
					jTextArea1.setFont(new java.awt.Font("宋体", 1, 14));
					jScrollPane2.setViewportView(jTextArea1);
				}
			}
		}
		jScrollPane1 = new JScrollPane();
		jTabbedpane.addTab("操作票", null, jScrollPane1);// 加入第一个页面
		contentPanel.add(jTabbedpane, BorderLayout.SOUTH);
		{
			{
				jTable1 = new JTable();
				jTable1.setRowHeight(30);
				jTable1.setFont(new java.awt.Font("宋体", 0, 14));
				jScrollPane1.setViewportView(jTable1);
			}
		}
			
		{
			JPanel panel2 = new JPanel();
			contentPanel.add(panel2, BorderLayout.CENTER);
			panel2.setLayout(new BorderLayout(0, 0));
			{
				JLabel label = new JLabel("备注事项：");
				panel2.add(label, BorderLayout.WEST);
			}
			{
				JPanel panel_1 = new JPanel();
				panel2.add(panel_1, BorderLayout.SOUTH);
				panel_1.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_2 = new JPanel();
					panel_1.add(panel_2, BorderLayout.EAST);
					{
				    	TicketDBManager tdb = new TicketDBManager();
						String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
						
						cmb = new JComboBox();
						
						if(CBSystemConstants.opcardUser.equals("OPCARDKM.")){
							cmb.addItem("监控票");
							cmb.addItem("顺控票");
							cmb.addItem("书面命令");
							cmb.addItem("投产指令");
							
							if(zbresults[8].equals("3")){
								cmb.setSelectedIndex(0);
							}else if(zbresults[8].equals("4")){
								cmb.setSelectedIndex(1);
							}else if(zbresults[8].equals("5")){
								cmb.setSelectedIndex(2);
							}else if(zbresults[8].equals("6")){
								cmb.setSelectedIndex(3);
							}
						}else if(CBSystemConstants.opcardUser.equals("OPCARDYX.")){
							cmb.addItem("监控逐项票");
							cmb.addItem("综合票");
							
							if(zbresults[8].equals("3")){
								cmb.setSelectedIndex(0);
							}else if(zbresults[8].equals("2")){
								cmb.setSelectedIndex(1);
							}
						}else{
							cmb.addItem("监控票");
							cmb.addItem("逐项票");
							cmb.addItem("综合票");
							
							if(zbresults[8].equals("1")){
								cmb.setSelectedIndex(0);
							}else if(zbresults[8].equals("2")){
								cmb.setSelectedIndex(1);
							}else if(zbresults[8].equals("3")){
								cmb.setSelectedIndex(2);
							}
						}
						
						panel_2.add(cmb);
					}
//					{
//						jButton4 = new JButton();
//						jButton4.setIcon(
//								new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
//						jButton4.setText("导入网络发令");
//						jButton4.setToolTipText("导入网络发令");
//						jButton4.setMargin(new java.awt.Insets(1, 1, 1, 1));
//						jButton4.setFocusPainted(false);
//						jButton4.addActionListener(new java.awt.event.ActionListener() {
//							public void actionPerformed(java.awt.event.ActionEvent evt) {
//								jButton9ActionPerformed(evt);
//							}
//						});
//						panel_2.add(jButton4);
//					}
					{
						jButton4 = new JButton();
						jButton4.setIcon(
								new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
						jButton4.setText("导入网络发令(CS)");
						jButton4.setToolTipText("导入网络发令(CS)");
						jButton4.setMargin(new java.awt.Insets(1, 1, 1, 1));
						jButton4.setFocusPainted(false);
						jButton4.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton9ActionPerformed(evt,"0");
							}
						});
						panel_2.add(jButton4);
					}
					{
						jButton2 = new JButton();
						jButton2.setIcon(
								new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
						jButton2.setText("本地保存");
						jButton2.setToolTipText("本地保存");
						jButton2.setMargin(new java.awt.Insets(1, 1, 1, 1));
						jButton2.setFocusPainted(false);
						jButton2.addActionListener(new java.awt.event.ActionListener() {

							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton3ActionPerformed(evt);
							}
						});
						panel_2.add(jButton2);
					}
					{
						jButton1 = new JButton();
						jButton1.setIcon(
								new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
						jButton1.setText("增加");
						jButton1.setToolTipText("增加");
						jButton1.setMargin(new java.awt.Insets(1, 1, 1, 1));
						jButton1.setFocusPainted(false);
						jButton1.addActionListener(new java.awt.event.ActionListener() {

							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton1ActionPerformed(evt);
							}
						});
						panel_2.add(jButton1);
					}
					{
						jButton3 = new JButton();
						jButton3.setIcon(
								new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
						jButton3.setText("删除");
						jButton3.setToolTipText("删除");
						jButton3.setMargin(new java.awt.Insets(1, 1, 1, 1));
						jButton3.setFocusPainted(false);
						jButton3.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton2ActionPerformed(evt);
							}
						});
						panel_2.add(jButton3);
					}
					{
						jButton9 = new JButton();
						jButton9.setIcon(
								new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/replace.png"))); // NOI18N
						jButton9.setText("替换");
						jButton9.setToolTipText("替换");
						jButton9.setMargin(new java.awt.Insets(1, 1, 1, 1));
						jButton9.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton11ActionPerformed(evt);
							}
						});
						panel_2.add(jButton9);
					}
					{
						jButton1 = new JButton();
						jButton1.setIcon(
								new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
						jButton1.setText("上移");
						jButton1.setToolTipText("上移");
						jButton1.setMargin(new java.awt.Insets(1, 1, 1, 1));
						jButton1.setFocusPainted(false);// 是否指示了输入焦点
						jButton1.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton4ActionPerformed(evt); // 给按钮赋予方法，次方法定义在后面
							}
						});
						panel_2.add(jButton1);
					}
					{
						jButton8 = new JButton();
						jButton8.setIcon(
								new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
						jButton8.setText("下移");
						jButton8.setToolTipText("下移");
						jButton8.setMargin(new java.awt.Insets(1, 1, 1, 1));
						jButton8.setFocusPainted(false);
						jButton8.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton5ActionPerformed(evt);
							}
						});
						panel_2.add(jButton8);
					}
					{
						jButton6 = new JButton();
						jButton6.setText("合项");
						jButton6.setMargin(new java.awt.Insets(1,1,1,1));
						jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/merge.png"))); // NOI18N
						jButton6.setToolTipText("合项");
						jButton6.setFocusPainted(false);
						jButton6.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton6ActionPerformed(evt);
							}
						});
						panel_2.add(jButton6);
					}
				}
			}
			{
				jScrollPane3 = new JScrollPane();
				panel2.add(jScrollPane3, BorderLayout.CENTER);
				{
					jTextArea2 = new JTextArea();
					jTextArea2.setColumns(20);
					jTextArea2.setPreferredSize(new Dimension(1, 10));
					jTextArea2.setFont(new java.awt.Font("宋体", 1, 14));
					jScrollPane3.setViewportView(jTextArea2);
				}
			}
		}
		
		{
			jScrollPanefj = new JScrollPane();
			jTabbedpane.addTab("操作序列", null, jScrollPanefj);// 加入第二个页面
			//设置不可编辑单元格
			jTable2 = new JTable() {
				public boolean isCellEditable(int rowindex,int colindex){
			        	return false;
				}
			};
			JPopupTextField jtf2 = new JPopupTextField();
			jtf2.setFont(new Font("宋体",Font.PLAIN,14));
			DefaultCellEditor editor = new DefaultCellEditor(jtf2);
			jTable2.setDefaultEditor(Object.class, editor);


			jTable2.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
			jTable2.setPreferredScrollableViewportSize(new Dimension(450, 200));
			jTable2.setFont(new Font("宋体",Font.PLAIN,14)); // NOI18N	
	        jTable2.setRowHeight(26);
	   
			jScrollPanefj.setViewportView(jTable2);
			
		}		
		
	}
	
	//保存
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		String preStationName = "";
		int row = jTable1.getRowCount();
		if (row == 0) {
			ShowMessage.view("数据为空,保存失败！");
			return;
		}
		if (jTextArea1.getText().trim().equals("")) {
			ShowMessage.view("操作任务不能为空,保存失败！");
			return;
		}
		//单元格编辑状态下点击保存
		if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
		String czrw = jTextArea1.getText().trim();//编辑修改后的操作任务名称
		List<CardItemModel> DescList = new ArrayList<CardItemModel>();
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			CardItemModel cim = new CardItemModel();
			cim.setCardNum(String.valueOf(i+1));
			cim.setBzbj(StringUtils.ObjToString(jTable1.getValueAt(i, 1)));
			String showName = StringUtils.ObjToString(jTable1.getValueAt(i, 3)).trim();
			cim.setShowName(showName);
			cim.setCardItem(StringUtils.ObjToString(jTable1.getValueAt(i, 2)).trim());
			String stationName =StringUtils.ObjToString(jTable1.getValueAt(i, 3)).trim();
			cim.setStationName(stationName);
			String cardDesc = StringUtils.ObjToString(jTable1.getValueAt(i, 4)).trim();
			cim.setCardDesc(cardDesc);
			if (cardDesc.equals(""))
				stationName = "";
			else if ((stationName.equals("") || stationName.indexOf("（") >= 0))
				stationName = preStationName;
			cim.setStationName(stationName);
			DescList.add(cim);
			if (!stationName.equals("") && stationName.indexOf("（") == -1)
				preStationName = stationName;
		}
		//保存设备术语，设备预令状态
		TicketDBManager ticketDB = new TicketDBManager();
		
		String selectItem = StringUtils.ObjToString(cmb.getSelectedItem());
		
		ticketDB.updateTicketDB(cnm.getCode(),DescList,czrw,jTextArea2.getText(),"",selectItem);
		
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			CardItemModel cim = new CardItemModel();
			
			
//			String czxlsql = "INSERT INTO  " + CBSystemConstants.opcardUser + "T_SK_OPERATION(FID,DEV_ID,CZP_ID,CZ_NUMBER,CREATE_TIME,MX_ID,DEV_NAME,CZZL,SFKK,SSCZ,CZCZ,TARGET_STATUS) "
//			+ "VALUES('" + StringUtils.getUUID() + "','" + czxl[2] + "','" + zbid + "',"
//					+ (i+1) + "," + date + ",'" + czxl[6] + "','" + czxl[3] + "','" + czxl[1] + "','1','"+ czxl[4]+"','"+ czxl[0]+"','"+ czxl[5]+"')";
//			DBManager.execute(czxlsql);
		}
	}
		
	//新增
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		
		if( jTable1.getSelectedRow() == -1){
			int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"请选择在哪里增加新的操作内容",
					CBSystemConstants.SYSTEM_TITLE,
					JOptionPane.YES_NO_OPTION);
			if (isok != JOptionPane.YES_OPTION)
				return;
		}else{
			WindowUtils.addTableRow(jTable1);
			paixuTableRow(jTable1,1);
		}
	}
	//删除
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"是否确认删除选中的操作内容？",
				CBSystemConstants.SYSTEM_TITLE,
				JOptionPane.YES_NO_OPTION);
		if (isok != JOptionPane.YES_OPTION)
			return;
		WindowUtils.removeTableRow(jTable1);
		
		
		int rowCount = jTable1.getRowCount();
        for (int i = 0; i <= rowCount - 1; i++) {//对行数组遍历--hx
        	jTable1.setValueAt(i+1, i, 1);
    	}     
	}
	//上移
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {  //上移方法
		if(jTable1.getSelectedRow() == 0){
			return;
		}else if(jTable1.getSelectedRow() == -1){
			ShowMessage.viewWarning(this, "未选择操作票指令！");
			return;
		}
		WindowUtils.moveupTableRow(jTable1);
		WindowUtils.paixuTableRow(jTable1,2);
		
		String bfStage = getStageInt(""+jTable1.getValueAt(jTable1.getSelectedRow(), 1));
		String curStage = getStageInt(""+jTable1.getValueAt(jTable1.getSelectedRow()+1, 1));
        
		jTable1.setValueAt(getStage(bfStage), jTable1.getSelectedRow()+1, 1);
		jTable1.setValueAt(getStage(curStage), jTable1.getSelectedRow(), 1);

        //jTable1.setValueAt(getStage(String.valueOf(nextStage)), jTable1.getSelectedRow()+1, 1);
		this.changeTable2Czpsx();
	}
	
	private void changeTable2Czpsx() {
		for (int i = 0; i < jTable2.getRowCount(); i++) {
			String xlglid = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 9));//序列的关联id
			
			for(int j = 0; j < jTable1.getRowCount(); j++){
				String zlglid = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 0));
				String sxglid = StringUtils.ObjToString(jTable1.getModel().getValueAt(j, 2));

				if(zlglid.equals(xlglid)){
					jTable2.getModel().setValueAt(sxglid, i, 10);
					break;
				}
			}
		}
		
		ArrayList<String[]> djzl = new ArrayList<String[]>();
		
		for(int i = 0; i < jTable2.getRowCount(); i++){
			String lxid  =  StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 0));//序列id
			String xlsx  =  StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 1));
			String czdw =  StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 2));
			String cznr = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 3));
			String devid = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 4));
			String devname  = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 5));
			String sscz = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 6));
			String endstate = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 7));
			String isyk = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 8));
			String glid = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 9));
			String czpsx = StringUtils.ObjToString(jTable2.getModel().getValueAt(i, 10));
			
			String[] arr = {lxid,xlsx,czdw, cznr,devid,devname,sscz,endstate,isyk,glid,czpsx};
			
			djzl.add(arr);
		}
		
		Collections.sort(djzl, new Comparator<String[]>() {
			@Override
			public int compare(String[] p1, String[] p2) {
				if(Integer.valueOf(p1[10]) <= Integer.valueOf(p2[10])){
					return -1;
				}else{
					return 1;
				}
			}
		});
		
		jTable2Model.getDataVector().clear();
		jTable2Model.fireTableDataChanged();
		
		for(int i=0;i<djzl.size();i++){
			String xlid = djzl.get(i)[0];
			String czdw = djzl.get(i)[2];
			String cznr = djzl.get(i)[3];
			String devid = djzl.get(i)[4];
			String devname = djzl.get(i)[5];
			String sscz = djzl.get(i)[6];
			String endstate = djzl.get(i)[7];
			String isyk = djzl.get(i)[8];
			String glid = djzl.get(i)[9];
			String czpsx = djzl.get(i)[10];
			
			Object[] rowData = new Object[]{xlid,String.valueOf(i+1),czdw, cznr,devid,devname,sscz,endstate,isyk,glid,czpsx};	
			jTable2Model.addRow(rowData);
		}
		
		jTable2.updateUI();
	}
	
	//下移
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		if(jTable1.getSelectedRow() == jTable1.getRowCount()-1){
			return;
		}else if(jTable1.getSelectedRow() == -1){
			ShowMessage.viewWarning(this, "未选择操作票指令！");
			return;
		}
		
		WindowUtils.movedownTableRow(jTable1);
		WindowUtils.paixuTableRow(jTable1,2);
		
		String bfStage = getStageInt(""+jTable1.getValueAt(jTable1.getSelectedRow(), 1));
		String curStage = getStageInt(""+jTable1.getValueAt(jTable1.getSelectedRow()-1, 1));
        
		jTable1.setValueAt(getStage(bfStage), jTable1.getSelectedRow()-1, 1);
		jTable1.setValueAt(getStage(curStage), jTable1.getSelectedRow(), 1);
		
		 this.changeTable2Czpsx();
	}
	//分项
	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {
		WindowUtils.splitTableRow(jTable1, 1);
	}
	
	//合项
	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
		int col = 1;
		TableModel model = jTable1.getModel();
		int rowCount = jTable1.getSelectedRowCount();
		//没选择行，直接返回
		if (rowCount == 0)
			return;
		if (jTable1.isEditing()) {
			jTable1.getCellEditor().stopCellEditing();
		}
		
		int selectRow = jTable1.getSelectedRow(); //选中的第一行
		if(model.getValueAt(selectRow, col) == null)
			return;
		int item0 = getRowNum(jTable1, selectRow-1, col); //选中的前一行项号
		
		int xiangcha=0;
		if(jTable1.getRowCount()>selectRow+rowCount){
			xiangcha = getRowNum(jTable1, selectRow+rowCount, col)-item0-2;
		}
		
		for(int i = 0; i < jTable1.getRowCount(); i++) {
			if(model.getValueAt(i, col) != null) {
				if(i>selectRow-1&&i<selectRow+rowCount){
					model.setValueAt(getStage(String.valueOf(item0+1)), i, col);
				}else if(i>=selectRow+rowCount){
				
					model.setValueAt(getStage(String.valueOf(getRowNum(jTable1, i, col)-xiangcha)), i, col);
				}
				
			}
		}
	}
	
	 
	//小写转大写
	public static String getStage(String str) {
		String stage = "阶段"+RuleExeUtil.int2chineseNum(Integer.valueOf(str));
		return stage;
    }
	
	//小写转大写
	public static String getStageInt(String str) {
		str = str.replace("阶段", "");
		int stageint = RuleExeUtil.chineseNumber2Int(str);
		 
	    return String.valueOf(stageint);
    }
	 
	
	private int getRowNum(JTable table, int row, int col) {
		TableModel model = table.getModel();
		int item0 = 0;
		for(int k = row; k >= 0; k--) {
			if(model.getValueAt(k, col) != null && !model.getValueAt(k, col).toString().equals("")) {
				item0 = Integer.valueOf(getStageInt(model.getValueAt(k, col).toString()));
				break;
			}
		}
		return item0;
	}
	
    //保存为典型票
    private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {                                         
    	TicketDBManager tdb = new TicketDBManager();
		boolean result = tdb.InsertDXTicketDB(cnm.getCode());
		if(result)
			ShowMessage.view("保存成功！");
		else
			ShowMessage.view("保存失败！");
    } 
    
    public boolean importWLFLYX(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNYXWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNYXWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
			String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "玉溪供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			if(cmb.getSelectedItem().equals("顺控票")){
				List<String[]> results = tdb.queryDXTicketSK(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = "";
					String operationContent = tempStr[3];
					String devId = tempStr[4];
					String devName = tempStr[5];
					String facName = tempStr[6];
					String op = tempStr[7];
					
					if(op.equals("断开")){
						op = "0";
					}else if(op.equals("合上")){
						op = "1";
					}
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					jsonObj.put("devId", devId);
					jsonObj.put("devName", devName);
					jsonObj.put("facName", facName);
					jsonObj.put("op", op);
					
					ticketList.add(jsonObj);
				}
			}else{
				List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = tempStr[3];
					String operationContent = tempStr[4];
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					
					ticketList.add(jsonObj);
				}
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "50400");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    public boolean importWLFLKM(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNKMWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNKMWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
			String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "昆明供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			if(cmb.getSelectedItem().equals("顺控票")){
				List<String[]> results = tdb.queryDXTicketSK(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = "";
					String operationContent = tempStr[3];
					String devId = tempStr[4];
					String devName = tempStr[5];
					String facName = tempStr[6];
					String op = tempStr[7];
					
					if(op.equals("断开")){
						op = "0";
					}else if(op.equals("合上")){
						op = "1";
					}
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					jsonObj.put("devId", devId);
					jsonObj.put("devName", devName);
					jsonObj.put("facName", facName);
					jsonObj.put("op", op);
					
					ticketList.add(jsonObj);
				}
			}else{
				List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = tempStr[3];
					String operationContent = tempStr[4];
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					
					ticketList.add(jsonObj);
				}
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "50100");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    public boolean importWLFLHH(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNHHWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNHHWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
			String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "红河供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
			String[] tempStr = null;
			for (int i = 0; i < results.size(); i++) {
				tempStr = results.get(i);
				
				JSONObject jsonObj = new JSONObject();
				
				String number =	tempStr[1];
				String operationCompany = tempStr[2];
				String stage = tempStr[3];
				String operationContent = tempStr[4];
				
				jsonObj.put("number", number);
				jsonObj.put("operationCompany", operationCompany);
				jsonObj.put("stage", stage);
				jsonObj.put("operationContent", operationContent);
				
				ticketList.add(jsonObj);
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "52500");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    public boolean importWLFLQJ(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNQJWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNQJWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
			String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "曲靖供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
			String[] tempStr = null;
			for (int i = 0; i < results.size(); i++) {
				tempStr = results.get(i);
				
				JSONObject jsonObj = new JSONObject();
				
				String number =	tempStr[1];
				String operationCompany = tempStr[2];
				String stage = tempStr[3];
				String operationContent = tempStr[4];
				
				jsonObj.put("number", number);
				jsonObj.put("operationCompany", operationCompany);
				jsonObj.put("stage", stage);
				jsonObj.put("operationContent", operationContent);
				
				ticketList.add(jsonObj);
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "50300");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    public boolean importWLFLXSBN(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNBNWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNBNWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
			String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "版纳供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
			String[] tempStr = null;
			for (int i = 0; i < results.size(); i++) {
				tempStr = results.get(i);
				
				JSONObject jsonObj = new JSONObject();
				
				String number =	tempStr[1];
				String operationCompany = tempStr[2];
				String stage = tempStr[3];
				String operationContent = tempStr[4];
				
				jsonObj.put("number", number);
				jsonObj.put("operationCompany", operationCompany);
				jsonObj.put("stage", stage);
				jsonObj.put("operationContent", operationContent);
				
				ticketList.add(jsonObj);
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "52800");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    public boolean importWLFLDL(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNDLWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNDLWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
			String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "大理供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			if(cmb.getSelectedItem().equals("顺控票")){
				List<String[]> results = tdb.queryDXTicketSK(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = "";
					String operationContent = tempStr[3];
					String devId = tempStr[4];
					String devName = tempStr[5];
					String facName = tempStr[6];
					String op = tempStr[7];
					
					if(op.equals("断开")){
						op = "0";
					}else if(op.equals("合上")){
						op = "1";
					}
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					jsonObj.put("devId", devId);
					jsonObj.put("devName", devName);
					jsonObj.put("facName", facName);
					jsonObj.put("op", op);
					
					ticketList.add(jsonObj);
				}
			}else{
				List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = tempStr[3];
					String operationContent = tempStr[4];
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					
					ticketList.add(jsonObj);
				}
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "52900");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    public boolean importWLFLWS(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNWSWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNWSWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
			String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "文山供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			if(cmb.getSelectedItem().equals("顺控票")){
				List<String[]> results = tdb.queryDXTicketSK(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = "";
					String operationContent = tempStr[3];
					String devId = tempStr[4];
					String devName = tempStr[5];
					String facName = tempStr[6];
					String op = tempStr[7];
					
					if(op.equals("断开")){
						op = "0";
					}else if(op.equals("合上")){
						op = "1";
					}
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					jsonObj.put("devId", devId);
					jsonObj.put("devName", devName);
					jsonObj.put("facName", facName);
					jsonObj.put("op", op);
					
					ticketList.add(jsonObj);
				}
			}else{
				List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = tempStr[3];
					String operationContent = tempStr[4];
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					
					ticketList.add(jsonObj);
				}
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "52600");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    public boolean importWLFLLJ(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNLJWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNLJWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
			String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "丽江供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			if(cmb.getSelectedItem().equals("顺控票")){
				List<String[]> results = tdb.queryDXTicketSK(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = "";
					String operationContent = tempStr[3];
					String devId = tempStr[4];
					String devName = tempStr[5];
					String facName = tempStr[6];
					String op = tempStr[7];
					
					if(op.equals("断开")){
						op = "0";
					}else if(op.equals("合上")){
						op = "1";
					}
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					jsonObj.put("devId", devId);
					jsonObj.put("devName", devName);
					jsonObj.put("facName", facName);
					jsonObj.put("op", op);
					
					ticketList.add(jsonObj);
				}
			}else{
				List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = tempStr[3];
					String operationContent = tempStr[4];
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					
					ticketList.add(jsonObj);
				}
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "50700");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    public boolean importWLFLPE(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNPEWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNPEWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			String voltageLevel = "";
			if(!zbresults[12].equals("")){
				ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
				voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			}else{
				voltageLevel = StringUtils.getVoltByWord(zbresults[2]);
			}
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "普洱供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			if(cmb.getSelectedItem().equals("顺控票")){
				List<String[]> results = tdb.queryDXTicketSK(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = "";
					String operationContent = tempStr[3];
					String devId = tempStr[4];
					String devName = tempStr[5];
					String facName = tempStr[6];
					String op = tempStr[7];
					
					if(op.equals("断开")){
						op = "0";
					}else if(op.equals("合上")){
						op = "1";
					}
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					jsonObj.put("devId", devId);
					jsonObj.put("devName", devName);
					jsonObj.put("facName", facName);
					jsonObj.put("op", op);
					
					ticketList.add(jsonObj);
				}
			}else{
				List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = tempStr[3];
					String operationContent = tempStr[4];
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					
					ticketList.add(jsonObj);
				}
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "50800");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    public boolean importWLFLDQ(String zbid,String systemType){
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zbresults = tdb.queryTicketZB(this.cnm.getCode());
		
		Properties pro = WebServiceUtil.pro;
		String host = pro.getProperty("YNDQWEB_IP");
		int port = Integer.valueOf(pro.getProperty("YNDQWEB_PORT"));
		
		if(zbresults.length>0){
			PowerDevice dev = CBSystemConstants.getPowerDevice(zbresults[12]);
			JSONObject operationTicket = new JSONObject();
			JSONObject operationTicketNr = new JSONObject();
			JSONObject user = new JSONObject();
			JSONArray ticketList = new JSONArray();
			ReplaceStrDYDJ dydj = new ReplaceStrDYDJ();
			String voltageLevel = dydj.strReplace("电压等级", dev, dev, "");
			
			operationTicketNr.put("commandType", "正令");
			operationTicketNr.put("systemType", systemType);
			operationTicketNr.put("voltageLevel", voltageLevel);
			operationTicketNr.put("operationTask", zbresults[2]);
			operationTicketNr.put("ticketType", zbresults[13]);
			operationTicketNr.put("type", zbresults[8]);
			operationTicketNr.put("company", "迪庆供电局");
			operationTicketNr.put("remark", jTextArea2.getText().replaceAll("\r\n", ""));
			operationTicket.put("operationTicket", operationTicketNr);
			
			if(cmb.getSelectedItem().equals("顺控票")){
				List<String[]> results = tdb.queryDXTicketSK(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = "";
					String operationContent = tempStr[3];
					String devId = tempStr[4];
					String devName = tempStr[5];
					String facName = tempStr[6];
					String op = tempStr[7];
					
					if(op.equals("断开")){
						op = "0";
					}else if(op.equals("合上")){
						op = "1";
					}
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					jsonObj.put("devId", devId);
					jsonObj.put("devName", devName);
					jsonObj.put("facName", facName);
					jsonObj.put("op", op);
					
					ticketList.add(jsonObj);
				}
			}else{
				List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
				String[] tempStr = null;
				for (int i = 0; i < results.size(); i++) {
					tempStr = results.get(i);
					
					JSONObject jsonObj = new JSONObject();
					
					String number =	tempStr[1];
					String operationCompany = tempStr[2];
					String stage = tempStr[3];
					String operationContent = tempStr[4];
					
					jsonObj.put("number", number);
					jsonObj.put("operationCompany", operationCompany);
					jsonObj.put("stage", stage);
					jsonObj.put("operationContent", operationContent);
					
					ticketList.add(jsonObj);
				}
			}
			
			operationTicketNr.put("ticketList", ticketList);
			
			user.put("employeeName", CBSystemConstants.getUser().getUserName());
			user.put("employeeCompany", "53400");
			
			String sql = "SELECT LOGINNAME FROM "+CBSystemConstants.opcardUser+"T_A_POWERUSERINFO WHERE USERNAME = '"+CBSystemConstants.getUser().getUserName()+"'";
			List<Map<String,String>> loginnamelist =  DBManager.queryForList(sql);
			
			if(loginnamelist.size()>0){
				user.put("employeeCode", loginnamelist.get(0).get("LOGINNAME"));
			}else{
				user.put("employeeCode", CBSystemConstants.getUser().getUserName());
			}
			
			operationTicketNr.put("user", user);
			System.out.println("传入网络发令参数："+operationTicket);
			
			try {
				new Client(host, port, operationTicket.toString()).push();
			} catch (IOException e) {
				ShowMessage.view(this, "操作票传入网络发令系统失败！");
				e.printStackTrace();
			}
		}else{
			ShowMessage.view(this, "操作票传入网络发令系统失败！");
		}
		
		return true;
	}
    
    private void jButton9ActionPerformed(java.awt.event.ActionEvent evt,String systemType) {
    	String zbid = cnm.getCode();
    	
    	if(!zbid.equals("")){
    		if(CBSystemConstants.opcardUser.equals("OPCARDKM.")){
        		importWLFLKM(zbid,systemType);
    		}else if(CBSystemConstants.opcardUser.equals("OPCARDYX.")){
        		importWLFLYX(zbid,systemType);
    		}else if(CBSystemConstants.opcardUser.equals("OPCARDHH.")){
        		importWLFLHH(zbid,systemType);
    		}else if(CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
        		importWLFLQJ(zbid,systemType);
    		}else if(CBSystemConstants.opcardUser.equals("OPCARDXSBN.")){
        		importWLFLXSBN(zbid,systemType);
    		}else if(CBSystemConstants.opcardUser.equals("OPCARDDL.")){
        		importWLFLDL(zbid,systemType);
    		}else if(CBSystemConstants.opcardUser.equals("OPCARDWS.")){
        		importWLFLWS(zbid,systemType);
    		}else if(CBSystemConstants.opcardUser.equals("OPCARDPE.")){
        		importWLFLPE(zbid,systemType);
    		}else if(CBSystemConstants.opcardUser.equals("OPCARDLJ.")){
        		importWLFLLJ(zbid,systemType);
    		}else if(CBSystemConstants.opcardUser.equals("OPCARDDQ.")){
        		importWLFLDQ(zbid,systemType);
    		}
    		this.setVisible(false);
    	}else{
    		ShowMessage.view("操作票传入网络发令系统失败！");
    	}
    } 
    
  //排序行
  	public void paixuTableRow(JTable table , int col) {
    	DefaultTableModel model = (DefaultTableModel)table.getModel();
		int rowCount = table.getRowCount();
        for (int i = 0; i <= rowCount - 1; i++) {//对行数组遍历--hx
    		table.setValueAt(i+1, i, col);
    	}     
        
        String curStage = getStageInt(""+table.getValueAt(table.getSelectedRow(), 0));
        int nextStage = Integer.valueOf(curStage)+1;
        
        table.setValueAt(getStage(String.valueOf(nextStage)), table.getSelectedRow()+1, 0);
  	}
  	
  	
  	
  	
  	// 替换
 	private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {
 		ReplaceWord rw = new ReplaceWord(this, true, jTable1);
 		rw.setVisible(true);
 	}
	private JButton jButton1=null;
	private JButton jButton2=null;
	private JButton jButton3=null;
	private JButton jButton4=null;
	private JButton jButton5=null;
	private JButton jButton6=null;
	private JButton jButton7=null;
	private JButton jButton8=null;
	private JButton jButton9=null;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JScrollPane jScrollPane3;
	private JComboBox cmb = new JComboBox();;
	
	private JTextArea jTextArea1=null;
	private JTextArea jTextArea2=null;

}
