package com.tellhow.czp.app.yndd.wordcard.dh;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDHXLBHDZQH  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("德宏线路保护定值切换".equals(tempStr)){
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			String[] arr = {"01区","02区","03区"};
			
			int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择保护定值所在区域", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
			
			if(sel==0){
				replaceStr += "将"+deviceName+"保护定值切至01区运行";
			}else if(sel==1){
				replaceStr += "将"+deviceName+"保护定值切至02区运行";
			}else{
				replaceStr += "将"+deviceName+"保护定值切至03区运行";
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
