package com.tellhow.czp.app.yndd.rule.zt;

import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

public class SelectTicketKind implements RulebaseInf {
	public static String ticketKind = "";
	
	@Override
	public boolean execute(RuleBaseMode rbm) {
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
		
		ticketKind = "";
		
		String[] arr = {"逐项票","综合票"};
		
		int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择当前操作票类型", 
				SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
		
		if(sel==0){
			ticketKind =  "逐项票";
		}else if(sel==1){
			ticketKind =  "综合票";
		}else if(sel==-1){
			return false;
		}
		
		return true;
	}
}
