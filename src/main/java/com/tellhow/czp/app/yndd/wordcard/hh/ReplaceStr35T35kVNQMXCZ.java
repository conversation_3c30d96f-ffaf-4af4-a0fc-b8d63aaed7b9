package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr35T35kVNQMXCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("35T35kV内桥母线操作".equals(tempStr)){
			String beginstatus = CBSystemConstants.getCurRBM().getBeginStatus();
			String endstatus = CBSystemConstants.getCurRBM().getEndState();
			
			CommonFunctionHH cf = new CommonFunctionHH();
			
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			
			String stationName = CZPService.getService().getDevName(station); 
			String sbName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycrbyxlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycyxxlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycyxzbkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zdycyxzbkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zbList =  new ArrayList<PowerDevice>();

			List<PowerDevice> mlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dyczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			
			PowerDevice curzb = new PowerDevice();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
						mlkgList.add(dev);
					}
				}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
						gycrbyxlkgList.add(dev);
					}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
						gycyxxlkgList.add(dev);
					}
				}
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
					List<PowerDevice> highkgList = RuleExeUtil.getTransformerSwitchHigh(dev);
					List<PowerDevice> loadkgList = RuleExeUtil.getTransformerSwitchLoad(dev);

					zdycyxzbkgList.addAll(loadkgList);
					
					if(highkgList.size()>0){
						for(PowerDevice highkg: highkgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(highkg).equals("0")){
								gycyxzbkgList.add(highkg);
							}
						}
						gycmlkgList = RuleExeUtil.getDeviceList(highkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
					
					List<PowerDevice> midkgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
					
					if(midkgList.size()>0){
						zycmlkgList = RuleExeUtil.getDeviceList(midkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					}
					
					List<PowerDevice> lowkgList = RuleExeUtil.getTransformerSwitchLow(dev);
					
					if(lowkgList.size()>0){
						dycmlkgList = RuleExeUtil.getDeviceList(lowkgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
						
						for(Iterator<PowerDevice> itor = dycmlkgList.iterator();itor.hasNext();){
							PowerDevice dycmlkg = itor.next();
							
							if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dycmlkg).equals("1")){
								itor.remove();
							}
						}
					}
				}
			}
			
			for(Iterator<PowerDevice> itor = zbList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(RuleExeUtil.isDeviceChanged(dev)){
					curzb = dev;
				}
			}
			
			List<PowerDevice> otherzbdyclist = new ArrayList<PowerDevice>();
			List<PowerDevice> curzbdyclist = new ArrayList<PowerDevice>();

			List<PowerDevice> zndyckglist =  RuleExeUtil.getTransformerSwitchLoad(curzb);
			
			
			List<PowerDevice> zbdyckglist =  RuleExeUtil.getTransformerSwitchLow(curzb);
			List<PowerDevice> zbzyckglist =  RuleExeUtil.getTransformerSwitchMiddle(curzb);
			List<PowerDevice> zbgyckglist =  RuleExeUtil.getTransformerSwitchHigh(curzb);
			
			for(PowerDevice zbzdyckg : zndyckglist){
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(zbzdyckg).equals("1")){
					curzbdyclist.add(zbzdyckg);
				}
				
				List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbzdyckg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);

				for(PowerDevice mlkg : kgList){
					if(mlkg.getDeviceStatus().equals("0")){
						List<PowerDevice> zbdycList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, false, true);

						for(PowerDevice zbdyc : zbdycList){
							if(!zbzdyckg.getPowerDeviceID().equals(zbdyc.getPowerDeviceID())){
								if(RuleExeUtil.getDeviceEndStatus(zbdyc).equals("0")){
									otherzbdyclist.add(zbdyc);
									break;
								}
							}
						}
					}
				}
			}
			
			if(beginstatus.equals("0")&&endstatus.equals("2")){
				if(mlkgList.size()>0){
					mlkgList.addAll(gycrbyxlkgList);
					mlkgList.addAll(otherzbdyclist);
					mlkgList.addAll(curzbdyclist);
					
					replaceStr += "核实"+CZPService.getService().getDevName(mlkgList)+"热备用/r/n";
				}else{
					replaceStr += "核实"+CZPService.getService().getDevName(gycrbyxlkgList)+"热备用/r/n";
				}
				
				if(dyczbkgList.size()>0){
					List<PowerDevice> mxdzList = RuleExeUtil.getDeviceList(dyczbkgList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeMX, "", false, true, true, true);
					
					if(mxdzList.size()>0){
						for(PowerDevice mxdz : mxdzList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(mxdz).equals("1")){
								replaceStr += "核实"+CZPService.getService().getDevName(mxdz)+"在拉开位置/r/n";
							}
						}
					}
				}
				
				replaceStr += cf.getXhxqStrReplace(zbList, "停电");
				
				replaceStr += "退出35kV备自投装置/r/n";

				String hsTemp ="";
				
				if(gycrbyxlkgList.size()>0){
					if(RuleExeUtil.getDeviceBeginStatus(gycrbyxlkgList.get(0)).equals("1")&&!xlkgList.contains(gycrbyxlkgList.get(0))){
						replaceStr += "红河地调@遥控用"+stationName+CZPService.getService().getDevName(gycrbyxlkgList)+"同期合环/r/n";
						hsTemp += "核实"+CZPService.getService().getDevName(gycrbyxlkgList)+"运行正常/r/n";
					}
				}
				
				if(gycmlkgList.size()>0){
					if(RuleExeUtil.getDeviceBeginStatus(gycmlkgList.get(0)).equals("1")){
						replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(gycmlkgList)+"/r/n";
						hsTemp += "核实"+CZPService.getService().getDevName(gycmlkgList)+"运行正常/r/n";
					}
				}
				
				if(xlkgList.size()>0){
					if(RuleExeUtil.getDeviceBeginStatus(xlkgList.get(0)).equals("0")){
						replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkgList)+"/r/n";
						hsTemp += "核实"+CZPService.getService().getDevName(xlkgList)+"热备用/r/n";
					}
				}

				if(!hsTemp.equals("")){
					replaceStr += hsTemp;
				}
				
				replaceStr += cf.getDqZbHbBhTMlkgDzStrReplace(curzb, "midlow", "停电");
				
				replaceStr += cf.getZbBLTQStrReplace(curzb);
				
				List<PowerDevice> loadmlkglist = new ArrayList<PowerDevice>();
				
				loadmlkglist.addAll(zycmlkgList);
				loadmlkglist.addAll(dycmlkgList);
				
				for(Iterator<PowerDevice> itor = loadmlkglist.iterator();itor.hasNext();){
					PowerDevice loadmlkg = itor.next();
					
					if(!RuleExeUtil.getDeviceEndStatus(loadmlkg).equals("0")){
						itor.remove();
					}
				}
				
				replaceStr += cf.getYcHsStrReplace(loadmlkglist, stationName);
				
				List<PowerDevice> tempkglist = new ArrayList<PowerDevice>();

				if(zbList.size()>0){
					tempkglist.addAll(RuleExeUtil.getTransformerSwitchLow(curzb));
					tempkglist.addAll(RuleExeUtil.getTransformerSwitchMiddle(curzb));
					tempkglist.addAll(RuleExeUtil.getTransformerSwitchHigh(curzb));
					
					if(otherzbdyclist.size()>0){
						replaceStr += cf.getYcHsStrReplace(otherzbdyclist, stationName);
					}
					
					for(Iterator<PowerDevice> itor = tempkglist.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							itor.remove();
						}
					}
					
					replaceStr += "红河地调@遥控依次断开"+stationName+CZPService.getService().getDevName(tempkglist)+"、"+CZPService.getService().getDevName(gycmlkgList)+"/r/n";
				}
				
				if(loadmlkglist.size()>0){
					replaceStr += "核实"+stationName+CZPService.getService().getDevName(loadmlkglist)+"运行正常/r/n";
				}
				
				if(zbList.size()>0){
					if(otherzbdyclist.size()>0){
						replaceStr += "核实"+stationName+CZPService.getService().getDevName(otherzbdyclist)+"运行正常/r/n";
					}
					
					tempkglist.add(0,curzb);
					if(RuleUtil.isTransformerNQ(curzb)){
						replaceStr += "核实"+CZPService.getService().getDevName(tempkglist)+"、"+sbName+"热备用/r/n";
					}else{
						replaceStr += "核实"+CZPService.getService().getDevName(tempkglist)+"、"+sbName+"及母线设备热备用/r/n";
					}
					
					List<PowerDevice> zbdzList = RuleExeUtil.getDeviceList(curzb, SystemConstants.SwitchSeparate , "", CBSystemConstants.RunTypeKnifeZBS, "", true, true, true, true);
					
					if(zbdzList.size()>0){
						replaceStr += "拉开"+CZPService.getService().getDevName(zbdzList)+"/r/n";
					}
					
					List<PowerDevice> tempList = new ArrayList<PowerDevice>();
					
					tempList.addAll(zbdyckglist);
					tempList.addAll(zbzyckglist);
					tempList.addAll(zbgyckglist);
					
					boolean allkglby = true;
					
					for(PowerDevice dev : tempList){
						if(!dev.getDeviceStatus().equals("2")){
							allkglby = false;
							break;
						}
					}
					
					if(allkglby){
						tempList.addAll(0,zbList);
					}
					
					for(PowerDevice gycmlkg : gycmlkgList){
						if(!tempList.contains(gycmlkg)){
							tempList.add(gycmlkg);
						}
					}
					
					for(PowerDevice xlkg : xlkgList){
						if(!tempList.contains(xlkg)){
							tempList.add(xlkg);
						}
					}
					
					for(Iterator<PowerDevice> itor = tempList.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							itor.remove();
						}
					}
					
					if(RuleUtil.isTransformerNQ(curzb)){
						replaceStr += "将"+CZPService.getService().getDevName(tempList)+"、"+sbName+"由热备用转冷备用/r/n";
					}else{
						replaceStr += "将"+CZPService.getService().getDevName(tempList)+"、"+sbName+"及母线设备由热备用转冷备用/r/n";
					}
					
					for(PowerDevice xlkg : xlkgList){
					 	List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(xlkg, SystemConstants.SwitchSeparate);
						
	 				 	for(PowerDevice dz : dzList){
					 		if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					 			List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(dz, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, true, true, true);
					 			
					 			if(xldzList.size()==1){
									replaceStr += "拉开"+CZPService.getService().getDevName(xldzList)+"/r/n";
					 			}
					 		}else{
					 			if(dzList.size()==1){
									replaceStr += "拉开"+CZPService.getService().getDevName(dz)+"/r/n";
					 			}
					 		}
					 	}
					}
					
					if(cf.getZbIsJdzybStrReplace(curzb)){
						replaceStr += "退出10kV#X接地站用变小电阻自投切功能/r/n";
					}
					
					replaceStr += cf.getDqZbHbBhTMlkgStrReplace(curzb, "midlow", "退出","停电");
				}
			}else if(beginstatus.equals("2")&&endstatus.equals("0")){
				List<PowerDevice> jdzybkgList =  new ArrayList<PowerDevice>();

				List<PowerDevice> xlkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
				List<PowerDevice> zbgycmlkglist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

				for(Iterator<PowerDevice> itor = zbgyckglist.iterator();itor.hasNext();){
					PowerDevice dev = itor.next();
					
					if(!RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
						itor.remove();
					}
				}					
				
				List<PowerDevice> tempList = new ArrayList<PowerDevice>();
				List<PowerDevice> zbjzdyckglist = new ArrayList<PowerDevice>();
				List<PowerDevice> zbzdyckglist = new ArrayList<PowerDevice>();

		    	zbzyckglist = RuleExeUtil.getTransformerSwitchMiddle(curzb);
				zbdyckglist = RuleExeUtil.getTransformerSwitchLow(curzb);
			    
				zbzdyckglist.addAll(zbzyckglist);
				zbzdyckglist.addAll(zbdyckglist);
				
				for(Iterator<PowerDevice> itor = zbzdyckglist.iterator();itor.hasNext();){
					PowerDevice dev = itor.next();
					
					if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
						itor.remove();
					}
				}
				
				if(zbzdyckglist.size()>0){
					tempList.addAll(0,zbzdyckglist);
					tempList.add(0,curzb);
				}
				
				tempList.addAll(zbgycmlkglist);
				
				if(RuleUtil.isTransformerNQ(curzb)){
					replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"、"+CZPService.getService().getDevName(tempList)+"冷备用/r/n";
				}else{
					replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备、"+CZPService.getService().getDevName(tempList)+"冷备用/r/n";
				}
			    
			    if(zbdyckglist.size()>0){
			    	List<PowerDevice> mxList = RuleExeUtil.getDeviceList(zbdyckglist.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

			    	if(mxList.size()>0){
				    	List<PowerDevice> mxkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

				    	if(mxkgList.size()>0){
				    		for(PowerDevice dev : mxkgList){
				    			if(dev.getDeviceType().equals(SystemConstants.Switch)){
									if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
										jdzybkgList.add(dev);
									}
								}
				    		}
				    	}
			    	}
			    }
			    
			    zbjzdyckglist.addAll(zbzyckglist);
			    zbjzdyckglist.addAll(zbdyckglist);
			    
			    for(Iterator<PowerDevice> itor = zbjzdyckglist.iterator();itor.hasNext();){
					PowerDevice dev = itor.next();
					
					if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
						itor.remove();
					}
				}
			    
			    if(zbjzdyckglist.size()>0){
			    	zbjzdyckglist.add(0,curzb);
					replaceStr += "核实"+CZPService.getService().getDevName(zbjzdyckglist)+"热备用/r/n";
			    }
				
				replaceStr += cf.getDqZbHbBhTMlkgStrReplace(curzb, "midlow", "投入","停电");
				
				if(RuleUtil.isTransformerNQ(curzb)){
					replaceStr += "将"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"、"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用/r/n";
				}else{
					replaceStr += "将"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备、"+CZPService.getService().getDevName(tempList)+"由冷备用转热备用/r/n";
				}
				
				List<PowerDevice> zbdzList = RuleExeUtil.getDeviceList(curzb, SystemConstants.SwitchSeparate , "", CBSystemConstants.RunTypeKnifeZBS, "", true, true, true, true);
				
				if(zbdzList.size()>0){
					replaceStr += "合上"+CZPService.getService().getDevName(zbdzList)+"/r/n";
				}
				
				if(zbgyckglist.size()>0){
					List<PowerDevice> mxdzList = RuleExeUtil.getDeviceList(zbgyckglist.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeMX, "", false, true, true, true);
					
					if(mxdzList.size()>0){
						for(PowerDevice mxdz : mxdzList){
							if(!RuleExeUtil.isDeviceChanged(mxdz)){
								replaceStr += "核实"+CZPService.getService().getDevName(mxdz)+"在拉开位置/r/n";
							}
						}
					}
				}
				
				if(xlkglist.size()>0){
					replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(xlkglist)+"/r/n";
					
					if(RuleUtil.isTransformerNQ(curzb)){
						replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"、"+CZPService.getService().getDevName(curzb)+"运行正常/r/n";
					}else{
						replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"、"+CZPService.getService().getDevName(curDev)+"及母线设备运行正常/r/n";
					}
					
					if(zbgycmlkglist.size()>0){
						replaceStr += "红河地调@遥控用"+stationName+CZPService.getService().getDevName(zbgycmlkglist)+"同期合环/r/n";
					}
					
					replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(xlkglist)+"/r/n";
					
					if(zbgycmlkglist.size()>0){
						replaceStr += "核实"+CZPService.getService().getDevName(zbgycmlkglist)+"运行正常/r/n";
					}
					
					replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"热备用/r/n";
					
				}
				
				
				replaceStr += "投入35kV备自投装置/r/n";
				
				replaceStr += cf.getZbBLTQStrReplace(curzb);
				
			    List<PowerDevice> zbgzdyckglist = new ArrayList<PowerDevice>();
			    List<PowerDevice> zdycmlkglist = new ArrayList<PowerDevice>();
			    List<PowerDevice> dycmlkglist = new ArrayList<PowerDevice>();
			    zbgzdyckglist.addAll(zbzyckglist);
			    zbgzdyckglist.addAll(zbdyckglist);
			    
				replaceStr += cf.getYcHsStrReplace(zbgzdyckglist, stationName);
				
				if(zbdyckglist.size()>0){
					dycmlkglist = RuleExeUtil.getDeviceList(zbdyckglist.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					zdycmlkglist.addAll(dycmlkglist);
				}
				
				if(zbzyckglist.size()>0){
					List<PowerDevice> zycmlkglist = RuleExeUtil.getDeviceList(zbzyckglist.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
					zdycmlkglist.addAll(zycmlkglist);
				}
				
				for(Iterator<PowerDevice> itor = zdycmlkglist.iterator();itor.hasNext();){
					PowerDevice dev = itor.next();
					
					if(!RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						itor.remove();
					}
				}
				
				if(zdycmlkglist.size()>0){
					replaceStr += cf.getYcDkStrReplace(zdycmlkglist, stationName);
				}
				
				if(!RuleUtil.isTransformerNQ(curzb)){
					zbgzdyckglist.add(0,curzb);
				}
				
				replaceStr += "核实"+CZPService.getService().getDevName(zbgzdyckglist)+"运行正常/r/n";

				if(zdycmlkglist.size()>0){
					replaceStr += "核实"+CZPService.getService().getDevName(zdycmlkglist)+"热备用/r/n";
				}
				

				replaceStr += cf.getDqZbHbBhTMlkgDzStrReplace(curzb, "midlow", "复电");
				
				if(jdzybkgList.size()>0){
					replaceStr += "投入10kV#X接地站用变小电阻自投切功能/r/n";
					
					if(dycmlkglist.size()>0&&zbdyckglist.size()>0){
						dycmlkglist.addAll(zbdyckglist);
						replaceStr += "投入10kV#X接地站用变保护动作跳"+CZPService.getService().getDevName(dycmlkglist)+"/r/n";
					}
					replaceStr += "投入10kV#X接地站用变保护动作闭锁10kV备自投装置/r/n";
				}
				
				replaceStr += cf.getXzZbLxbhltxdStrReplace(curzb, "投入");
				
				if(xlkglist.size()>0){
					if(RuleExeUtil.getDeviceEndStatus(xlkglist.get(0)).equals("0")){
						replaceStr += "红河地调@遥控用"+stationName+CZPService.getService().getDevName(xlkglist)+"同期合环/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(xlkglist)+"运行正常/r/n";
						
						replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(zbgycmlkglist)+"/r/n";
						replaceStr += "核实"+CZPService.getService().getDevName(zbgycmlkglist)+"热备用/r/n";
						
						replaceStr += "核实35kV备自投装置充电且运行正常/r/n";
					}
				}
				
				replaceStr += cf.getXhxqStrReplace(zbList, "复电");
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
