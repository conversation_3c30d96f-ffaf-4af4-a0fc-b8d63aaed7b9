package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

public class ZTZBHHDD implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		
		List<PowerDevice> dzList = RuleExeUtil.getTransformerKnifeSource(pd);
		
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();

		for(PowerDevice dev : dzList){
			xlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,CBSystemConstants.RunTypeSwitchML, false, true, true, true,true);
			mlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
		}
		
		if(xlkgList.size()>0&&mlkgList.size()>0){
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();
			
			tempList.addAll(xlkgList);
			tempList.addAll(mlkgList);

			for(PowerDevice dev : tempList){
				if(dev.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
			
			for(PowerDevice dev : tempList){
				if(dev.getDeviceStatus().equals("0")&&!RuleExeUtil.isDeviceChanged(dev)){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}
		}
		
		return true;
	}
}
