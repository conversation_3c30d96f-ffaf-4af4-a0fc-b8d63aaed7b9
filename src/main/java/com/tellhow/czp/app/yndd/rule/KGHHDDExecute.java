/**

 **/
package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.rule.view.EquipChoose;
import czprule.system.CBSystemConstants;

public class KGHHDDExecute implements RulebaseInf {
	/**
	 * 选择执行满足输入条件的开关  输入条件（变电站类型，设备运行类型，初始状态，执行动作）
	 */
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;

		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

		for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = it2.next();
			if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
				List<PowerDevice> gdList1 = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				
				for(PowerDevice gd : gdList1) {
					RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
				}
			}
		}
		
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
			
			List<PowerDevice>  mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			if(mlList.size() > 0) {
				PowerDevice mx1 = mlList.get(0);
				List<PowerDevice>  swList = RuleExeUtil.getDeviceList(mx1, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, false, false, true);
				for (Iterator<PowerDevice> it2 = swList.iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if(!dev.getDeviceStatus().equals("1")) {
						it2.remove();
					}
				}
				if(swList.size() > 0) {
					List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
					if(swList.size() > 1){
						String showMessage="请选择用来合环的开关";
						EquipChoose ecc=new EquipChoose(SystemConstants.getMainFrame(), true, swList, showMessage,true,1);
						chooseEquips = ecc.getChooseEquip();
						if(ecc.isCancel()){
							return false;
						}
					}else{
						chooseEquips.add(swList.get(0));
					}
					
					if(chooseEquips.size() > 0) {
						PowerDevice dyml = chooseEquips.get(0);
						List<PowerDevice> mlList2 = RuleExeUtil.getDeviceList(dyml, mx1, SystemConstants.MotherLine, SystemConstants.PowerTransformer, null, null, false, true, true, true);
						PowerDevice mx2 = mlList2.get(0);
						
						List<PowerDevice> tfList1 = RuleExeUtil.getDeviceList(mx1, dyml, SystemConstants.PowerTransformer, null, null, null, false, false, false, true);
						List<PowerDevice> tfList2 = RuleExeUtil.getDeviceList(mx2, dyml, SystemConstants.PowerTransformer, null, null, null, false, false, false, true);
						
						List<PowerDevice> gymxList1 = RuleExeUtil.getDeviceList(tfList1.get(0), null, SystemConstants.MotherLine, null, null, null, false, false, true, true, true);
						List<PowerDevice> gymxList2 = RuleExeUtil.getDeviceList(tfList2.get(0), null, SystemConstants.MotherLine, null, null, null, false, false, true, true, true);
						
						if(gymxList1.size()>0&&gymxList2.size()>0){
							PowerDevice gymx1 = gymxList1.get(0);
							PowerDevice gymx2 = gymxList2.get(0);
							
							PowerDevice gyml = RuleExeUtil.getMLSwitch(gymx1, gymx2);
							
							if(gyml!=null){
								if(gyml.getDeviceStatus().equals("1")){
									List<PowerDevice> xlList1 = RuleExeUtil.getDeviceList(gymx1, SystemConstants.InOutLine, SystemConstants.PowerTransformer, null, null, false, false, false, true);
									List<PowerDevice> xlList2 = RuleExeUtil.getDeviceList(gymx2, SystemConstants.InOutLine, SystemConstants.PowerTransformer, null, null, false, false, false, true);
									
									List<PowerDevice> dcxlList1 = RuleExeUtil.getLineOtherSideList(xlList1.get(0));
									List<PowerDevice> dcxlList2 = RuleExeUtil.getLineOtherSideList(xlList2.get(0));
									List<PowerDevice> dcmxList2 = new ArrayList<PowerDevice>();
									if(dcxlList2.size()>0){
										dcmxList2 = RuleExeUtil.getDeviceList(dcxlList2.get(0), SystemConstants.MotherLine, null, null, null, false, false, true, true);
									}
									
									PowerDevice dcml = new PowerDevice();
									
									if(dcxlList1.size()>0){
										List<PowerDevice> dcmxList1 = RuleExeUtil.getDeviceList(dcxlList1.get(0), SystemConstants.MotherLine, null, null, null, false, false, true, true);

										if(dcmxList2.size()>0&&dcmxList1.size()>0){
											dcml = RuleExeUtil.getMLSwitch(dcmxList1.get(0), dcmxList2.get(0));
											
											if(dcml!=null){
												RuleExeUtil.deviceStatusExecute(dcml, dcml.getDeviceStatus(), "0");
											}
										}
									}
									
									RuleExeUtil.deviceStatusExecute(gyml, gyml.getDeviceStatus(), "0");
									
									List<PowerDevice> choosedkkgList = new ArrayList<PowerDevice>();
									
									if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&(pd.getPowerVoltGrade() == 10||pd.getPowerVoltGrade() == 35)){
										List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();
										
										for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
											PowerDevice dev2 = it2.next();
											
											if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
												if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
													if(dev2.getDeviceStatus().equals("0")){
														gycxlkgList.add(dev2);
													}
												}
											}
										}
										
										if(gycxlkgList.size()>1){
											String showMessage="请选择需要用来解环的断路器";
											EquipChoose ecc=new EquipChoose(SystemConstants.getMainFrame(), true, gycxlkgList, showMessage,true,1);
											choosedkkgList = ecc.getChooseEquip();
											if(ecc.isCancel()){
												return false;
											}
											
											RuleExeUtil.deviceStatusExecute(choosedkkgList.get(0), choosedkkgList.get(0).getDeviceStatus(), "1");
										}else if(gycxlkgList.size()==1){
											RuleExeUtil.deviceStatusExecute(gycxlkgList.get(0), gycxlkgList.get(0).getDeviceStatus(), "1");
										}
									}
									
									if(choosedkkgList.size()>0){
										RuleExeUtil.deviceStatusExecute(choosedkkgList.get(0), choosedkkgList.get(0).getDeviceStatus(), "0");
									}
									
									RuleExeUtil.deviceStatusExecute(gyml, gyml.getDeviceStatus(), "1");
									if(dcml!=null){
										if(!dcml.getPowerDeviceID().equals("")){
											RuleExeUtil.deviceStatusExecute(dcml, dcml.getDeviceStatus(), "1");
										}
									}
								}
							}
							
							RuleExeUtil.deviceStatusExecute(dyml, dyml.getDeviceStatus(), "0");
							RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
						}else{//线变组
							RuleExeUtil.deviceStatusExecute(chooseEquips.get(0), chooseEquips.get(0).getDeviceStatus(), "0");
							RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
						}
					}
				}else{
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev2 = it2.next();
						if(dev2.getPowerVoltGrade()==10&&dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							if(dev2.getPowerDeviceName().contains("002")||dev2.getPowerDeviceName().contains("004")){
								RuleExeUtil.deviceStatusExecute(dev2, dev2.getDeviceStatus(), "0");
							}
						}
					}
					
					RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
				}
			}
		}else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
			
			List<PowerDevice>  dyzbswList = new ArrayList<PowerDevice>();
			if(pd.getPowerVoltGrade()<CBSystemConstants.getMapPowerStation().get(pd.getPowerStationID()).getPowerVoltGrade()){
				dyzbswList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer,
						CBSystemConstants.RunTypeSwitchFHC,"",false, false, false, true);
			}else{
				dyzbswList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer,
						CBSystemConstants.RunTypeSwitchXL,"",false, false, false, true);
			}
		
			for(int i=0;i<dyzbswList.size();i++){
				if(!dyzbswList.get(i).getDeviceStatus().equals("1")) {
					dyzbswList.remove(i);
					i--;
				}
			}
			if(dyzbswList.size() > 0) {
				List<PowerDevice> chooseEquips  = new ArrayList<PowerDevice>();
				if(dyzbswList.size()>1){
					String showMessage="请选择用来合环的开关";
					EquipChoose ecc=new EquipChoose(SystemConstants.getMainFrame(), true, dyzbswList, showMessage,true,1);
					chooseEquips = ecc.getChooseEquip();
					if(ecc.isCancel()){
						return false;
					}
				}else{
					chooseEquips.add(dyzbswList.get(0));
				}
				if(chooseEquips.size() > 0) {
					PowerDevice dyzb = chooseEquips.get(0);

					if(pd.getPowerVoltGrade()<CBSystemConstants.getMapPowerStation().get(pd.getPowerStationID()).getPowerVoltGrade()){
						List<PowerDevice>  mlList = RuleExeUtil.getDeviceList(dyzb, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
						if(mlList.size() > 0) {
							PowerDevice mx1 = mlList.get(0);
							List<PowerDevice> mlList2 = RuleExeUtil.getDeviceList(pd, mx1, SystemConstants.MotherLine, SystemConstants.PowerTransformer, null, null, false, true, true, true);
							PowerDevice mx2 = mlList2.get(0);
							
							List<PowerDevice> tfList1 = RuleExeUtil.getDeviceList(dyzb,SystemConstants.PowerTransformer, null, true, true, true);
							List<PowerDevice> tfList2 = RuleExeUtil.getDeviceList(mx2, pd, SystemConstants.PowerTransformer, null, null, null, false, false, false, true);
							
							if(tfList1.size()>0&&tfList2.size()>0){
								List<PowerDevice> gymxList1 = RuleExeUtil.getDeviceList(tfList1.get(0), null, SystemConstants.MotherLine, null, null, null, false, false, true, true, true);
								List<PowerDevice> gymxList2 = RuleExeUtil.getDeviceList(tfList2.get(0), null, SystemConstants.MotherLine, null, null, null, false, false, true, true, true);
								
								if(gymxList1.size()>0&&gymxList2.size()>0){
									PowerDevice gymx1 = gymxList1.get(0);
									PowerDevice gymx2 = gymxList2.get(0);
									
									PowerDevice gyml = RuleExeUtil.getMLSwitch(gymx1, gymx2);
									
									List<PowerDevice> xlList1 = RuleExeUtil.getDeviceList(gymx1, SystemConstants.InOutLine, SystemConstants.PowerTransformer, null, null, false, false, false, true);
									List<PowerDevice> xlList2 = RuleExeUtil.getDeviceList(gymx2, SystemConstants.InOutLine, SystemConstants.PowerTransformer, null, null, false, false, false, true);
									
									List<PowerDevice> dcxlList1 = RuleExeUtil.getLineOtherSideList(xlList1.get(0));
									List<PowerDevice> dcxlList2 = RuleExeUtil.getLineOtherSideList(xlList2.get(0));
									
									PowerDevice dcml = new PowerDevice();
									
									if(dcxlList1.size()>0){
										List<PowerDevice> dcmxList1 = RuleExeUtil.getDeviceList(dcxlList1.get(0), SystemConstants.MotherLine, null, null, null, false, false, true, true);

										if(dcxlList2.size()>0){
											List<PowerDevice> dcmxList2 = RuleExeUtil.getDeviceList(dcxlList2.get(0), SystemConstants.MotherLine, null, null, null, false, false, true, true);
											dcml = RuleExeUtil.getMLSwitch(dcmxList1.get(0), dcmxList2.get(0));
										}
									}
									
									if(dcml!=null){
										if(!dcml.getPowerDeviceID().equals("")){
											RuleExeUtil.deviceStatusExecute(dcml, dcml.getDeviceStatus(), "0");
										}
									}
									RuleExeUtil.deviceStatusExecute(gyml, gyml.getDeviceStatus(), "0");
								
									List<PowerDevice> choosedkkgList = new ArrayList<PowerDevice>();
									
									if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(pd.getPowerVoltGrade() == 10||pd.getPowerVoltGrade() == 35)){
										List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();
										
										for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
											PowerDevice dev2 = it2.next();
											
											if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
												if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
													if(dev2.getDeviceStatus().equals("0")){
														gycxlkgList.add(dev2);
													}
												}
											}
										}
										
										if(gycxlkgList.size()>1){
											String showMessage="请选择需要用来解环的断路器";
											EquipChoose ecc=new EquipChoose(SystemConstants.getMainFrame(), true, gycxlkgList, showMessage,true,1);
											choosedkkgList = ecc.getChooseEquip();
											if(ecc.isCancel()){
												return false;
											}
											
											RuleExeUtil.deviceStatusExecute(choosedkkgList.get(0), choosedkkgList.get(0).getDeviceStatus(), "1");
										}else if(gycxlkgList.size()==1){
											RuleExeUtil.deviceStatusExecute(gycxlkgList.get(0), gycxlkgList.get(0).getDeviceStatus(), "1");
										}
									}
									
									RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
									RuleExeUtil.deviceStatusExecute(dyzb, dyzb.getDeviceStatus(), "0");
									
									if(choosedkkgList.size()>0){
										RuleExeUtil.deviceStatusExecute(choosedkkgList.get(0), choosedkkgList.get(0).getDeviceStatus(), "0");
									}
									
									RuleExeUtil.deviceStatusExecute(gyml, gyml.getDeviceStatus(), "1");
									if(dcml!=null){
										RuleExeUtil.deviceStatusExecute(dcml, dcml.getDeviceStatus(), "1");
									}
								}else{
									RuleExeUtil.deviceStatusExecute(chooseEquips.get(0), chooseEquips.get(0).getDeviceStatus(), "0");
									RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
								}
							}else{
								RuleExeUtil.deviceStatusExecute(chooseEquips.get(0), chooseEquips.get(0).getDeviceStatus(), "0");
								RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
							}
						}
					}else{
						RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
						RuleExeUtil.deviceStatusExecute(dyzb, dyzb.getDeviceStatus(), "0");
					}
				}
			}else{
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev2 = it2.next();
					
					if(dev2.getPowerVoltGrade() == pd.getPowerVoltGrade()){
						if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)
								||dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							if(dev2.getDeviceStatus().equals("1")){
								RuleExeUtil.deviceStatusExecute(dev2, dev2.getDeviceStatus(), "0");
							}
						}
					}
				}
				
				RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
			}
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
			List<PowerDevice>  mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			if(mlList.size() > 0) {
				PowerDevice mx1 = mlList.get(0);
				List<PowerDevice>  swList = RuleExeUtil.getDeviceList(mx1, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);
				for (Iterator<PowerDevice> it2 = swList.iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if(!dev.getDeviceStatus().equals("1")) {
						it2.remove();
					}
				}
				if(swList.size()==0){
					swList = RuleExeUtil.getDeviceList(mx1,pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, null, false, false, false, true);
				}
				if(swList.size() > 0) {
					List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
					
					for(Iterator<PowerDevice> itor = swList.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!dev.getDeviceStatus().equals("1")){
							itor.remove();
						}
					}
					
					if(swList.size() > 1){
						String showMessage="请选择用来合环的断路器";
						EquipChoose ecc=new EquipChoose(SystemConstants.getMainFrame(), true, swList, showMessage,true,1);
						chooseEquips = ecc.getChooseEquip();
						if(ecc.isCancel()){
							return false;
						}
					}else{
						chooseEquips.addAll(swList);
					}
					
					if(chooseEquips.size() > 0) {
						
						RuleExeUtil.deviceStatusExecute(pd, "0", "1");
						RuleExeUtil.deviceStatusExecute(chooseEquips.get(0), "1", "0");
					}
				}else{
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev2 = it2.next();
						
						if(dev2.getPowerVoltGrade() == pd.getPowerVoltGrade()){
							if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)
									||dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								if(dev2.getDeviceStatus().equals("1")){
									RuleExeUtil.deviceStatusExecute(dev2, dev2.getDeviceStatus(), "0");
								}
							}
						}
					}
					
					RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
				}
			}else{
				List<PowerDevice>  swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, true, false, true);
				for (Iterator<PowerDevice> it2 = swList.iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if(!dev.getDeviceStatus().equals("1")) {
						it2.remove();
					}
				}
				if(swList.size()==0){
					swList = RuleExeUtil.getDeviceList(pd,null, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, null, false, false, false, true);
				}
				if(swList.size() > 0) {
					List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
					
					for(Iterator<PowerDevice> itor = swList.iterator();itor.hasNext();){
						PowerDevice dev = itor.next();
						
						if(!dev.getDeviceStatus().equals("1")){
							itor.remove();
						}
					}
					
					if(swList.size() > 1){
						String showMessage="请选择用来合环的断路器";
						EquipChoose ecc=new EquipChoose(SystemConstants.getMainFrame(), true, swList, showMessage,true,1);
						chooseEquips = ecc.getChooseEquip();
						if(ecc.isCancel()){
							return false;
						}
					}else{
						chooseEquips.add(swList.get(0));
					}
					
					if(chooseEquips.size() > 0) {
						
						RuleExeUtil.deviceStatusExecute(pd, "0", "1");
						RuleExeUtil.deviceStatusExecute(chooseEquips.get(0), "1", "0");
					}
				}else{
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev2 = it2.next();
						
						if(dev2.getPowerVoltGrade() == pd.getPowerVoltGrade()){
							if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)
									||dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								if(dev2.getDeviceStatus().equals("1")){
									RuleExeUtil.deviceStatusExecute(dev2, dev2.getDeviceStatus(), "0");
								}
							}
						}
					}
					
					RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
				}
			}
		}
		/*
		List<PowerDevice>  kgList = new ArrayList<PowerDevice>();
		List<PowerDevice>  dycmlList = new ArrayList<PowerDevice>();

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
		
		for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = it2.next();
			if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				if(dev.getPowerVoltGrade() < pd.getPowerVoltGrade()){
					dycmlList.add(dev);
				}else if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
					kgList.add(dev);
				}
			}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&!pd.getPowerDeviceID().equals(dev.getPowerDeviceID())){
				if(dev.getPowerVoltGrade() ==  pd.getPowerVoltGrade()){
					kgList.add(dev);
				}
			}
		}
		
		for(PowerDevice dev : dycmlList){
			if(dev.getDeviceStatus().equals("1")){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
		}
		
		for(PowerDevice dev : kgList){
			if(dev.getDeviceStatus().equals("0")){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
		}
		
		for(PowerDevice dev : kgList){
			if(dev.getDeviceStatus().equals("1")&&!RuleExeUtil.isDeviceChanged(dev)){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
		}
		
		for(PowerDevice dev : dycmlList){
			if(dev.getDeviceStatus().equals("0")){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
		}
		*/
		return true;
	}

}
