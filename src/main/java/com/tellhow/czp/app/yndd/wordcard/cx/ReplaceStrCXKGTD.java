package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionCX;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXKGTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄开关停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
				replaceStr += CommonFunctionCX.getSwitchOffContent(curDev, stationName);
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				replaceStr += CommonFunctionCX.getCheckBeforeContent(deviceName,voltStationName,"");
				
				if(CommonFunctionCX.ifSwitchSeparateControl(curDev)){
					replaceStr += "楚雄地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作。/r/n";
					replaceStr += CommonFunctionCX.getKnifeOffCheckContent(curDev);
				}else{
					replaceStr += stationName+"@"+deviceName+"由热备用转冷备用。/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
