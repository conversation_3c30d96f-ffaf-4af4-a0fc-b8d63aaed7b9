package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJSMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江双母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			PowerDevice othermx = new PowerDevice();
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.isSwitchDoubleML(dev)){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					for(PowerDevice mx : mxList){
						if(!mx.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
							othermx = mx ;
							break;
						}
					}
				}
			}
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);

			replaceStr += stationName+"@将"+deviceName+"上运行的所有断路器倒至"+CZPService.getService().getDevName(othermx)+"运行/r/n";
			
			for(PowerDevice dev : xlkgList){
				if(dev.getDeviceStatus().equals("1")){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
					
					if(mxList.contains(othermx)){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"上由"+deviceName+"热备用倒至"+CZPService.getService().getDevName(othermx)+"热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				if(dev.getDeviceStatus().equals("1")){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
					
					if(mxList.contains(othermx)){
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由"+deviceName+"热备用倒至"+CZPService.getService().getDevName(othermx)+"热备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
				replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
