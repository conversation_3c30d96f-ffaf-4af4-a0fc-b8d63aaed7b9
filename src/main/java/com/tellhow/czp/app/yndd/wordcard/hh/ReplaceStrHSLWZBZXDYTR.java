package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHSLWZBZXDYTR  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("核实另外主变中性点已投入".equals(tempStr)){
			PowerDevice otherZb = new PowerDevice();
			
			PowerDevice zb = new PowerDevice();
			
			if(curDev.getDeviceType().equals(SystemConstants.MotherLine)){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "", true, true, true);
				
				if(zbList.size()>0){
					zb = zbList.get(0);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)&&!dev.getPowerDeviceID().equals(zb.getPowerDeviceID())){
					otherZb = dev;
					break;
				}
			}
			
			List<PowerDevice> gdList = RuleExeUtil.getDeviceList(otherZb, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			
			if(gdList.size()>0){
				PowerDevice gd = gdList.get(0);
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(gd).equals("1")){
					replaceStr += "投入"+CZPService.getService().getDevName(otherZb)+"高、中压侧中性点及其零序保护";
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
			
		}
		return replaceStr;
	}
}
