package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHFDKGLBYTORBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河分段开关由冷备用转热备用".equals(tempStr)){
			List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT+","+CBSystemConstants.RunTypeKnifeMX,"",false, true, true, false);
			List<PowerDevice> kgdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());

			for(Iterator<PowerDevice> it2 = qtdzList.iterator();it2.hasNext();) {
				PowerDevice dev = (PowerDevice)it2.next();
				if(kgdzList.contains(dev))
					it2.remove();
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
						jdzybkgList.add(dev);
					}
				}
			}
			
			tempList.add(curDev);
			
			for(PowerDevice qtdz : qtdzList){
				tempList.add(qtdz);
				break;
			}
			
			replaceStr += "核实"+CZPService.getService().getDevName(tempList)+"冷备用/r/n";
			replaceStr += "将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
			replaceStr += "投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
			replaceStr += "投入"+(int)curDev.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(curDev)+"/r/n";
			
			if(jdzybkgList.size()>0&&curDev.getPowerVoltGrade() == 10){
				String num = "";
				
				for(PowerDevice jdzybkg : jdzybkgList){
					String jdbName = jdzybkg.getPowerDeviceName().substring(0, jdzybkg.getPowerDeviceName().indexOf("接地"));
					
					num += CZPService.getService().getDevNum(jdbName)+"、";
				}
				
				if(num.endsWith("、")){
					num = num.substring(0, num.length()-1);
				}
				
				replaceStr += "投入10kV"+num+"接地变保护动作跳"+CZPService.getService().getDevName(curDev)+"/r/n";
			}
			
			for(PowerDevice qtdz : qtdzList){
				replaceStr += "核实"+CZPService.getService().getDevName(qtdz)+"在合闸位置/r/n";
				break;
			}
			
			replaceStr += "投入"+CZPService.getService().getDevName(zbList)+(int)curDev.getPowerVoltGrade()+"kV侧后备保护动作闭锁"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";

			if(jdzybkgList.size()>0&&curDev.getPowerVoltGrade() == 10){
				String num = "";
				
				for(PowerDevice jdzybkg : jdzybkgList){
					String jdbName = jdzybkg.getPowerDeviceName().substring(0, jdzybkg.getPowerDeviceName().indexOf("接地"));
					
					num += CZPService.getService().getDevNum(jdbName)+"、";
				}
				
				if(num.endsWith("、")){
					num = num.substring(0, num.length()-1);
				}
				
				replaceStr += "投入10kV"+num+"接地变保护动作闭锁10kV备自投装置/r/n";
			}
			
			replaceStr += "投入"+(int)curDev.getPowerVoltGrade()+"kV母差保护动作闭锁"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
		}
		return replaceStr;
	}

}