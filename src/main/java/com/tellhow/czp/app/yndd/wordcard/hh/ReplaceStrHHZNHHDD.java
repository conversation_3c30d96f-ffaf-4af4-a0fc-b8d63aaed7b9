package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.app.yndd.rule.hh.HHKGHHDDExecute;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHZNHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河站内合环调电".equals(tempStr)){
			String stationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(curDev.getPowerStationID()));
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkglist = new ArrayList<PowerDevice>();//电源侧分段开关
			List<PowerDevice> fhcmlkglist = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkglist = new ArrayList<PowerDevice>();
			List<PowerDevice> lbykglist = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykglist = new ArrayList<PowerDevice>();
			List<PowerDevice> jdzybkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zdyczbkgList =  new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()>dev.getPowerVoltGrade()){
					fhcmlkglist.add(dev);
				}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&curDev.getPowerVoltGrade()==dev.getPowerVoltGrade()){
					gycxlkglist.add(dev);
				}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&curDev.getPowerVoltGrade()==dev.getPowerVoltGrade()){
					dycmlkglist.add(dev);
				}
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getPowerDeviceName().contains("接地变")||dev.getPowerDeviceName().contains("接地站用变")){
						jdzybkgList.add(dev);
					}
				}
				
				if(dev.getDeviceType().equals(SystemConstants.Switch)
						&&curDev.getPowerVoltGrade()==dev.getPowerVoltGrade()){
					if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
						rbykglist.add(dev);
					}else if(dev.getDeviceStatus().equals("2")){
						lbykglist.add(dev);
					}
				}
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
					if(!zdyczbkgList.contains(dev)){
						zdyczbkgList.add(dev);
					}
				}
			}
			
			if(zbList.size()>0){
				if(RuleUtil.isTransformerNQ(zbList.get(0))){//内桥接线
					if(curDev.getPowerVoltGrade() == 110){
						if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){//
							List<PowerDevice> tempList = new ArrayList<PowerDevice>();
							
							for(PowerDevice dev : fhcmlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
									tempList.add(dev);
								}
							}
							
							for(PowerDevice dev : gycxlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
									tempList.add(dev);
								}
							}
							
							replaceStr += CommonFunctionHH.getJdzybStrReplace(zbList);
							
							replaceStr += CommonFunctionHH.getYcDkStrReplace(fhcmlkglist, stationName);
							
							for(PowerDevice gycxlkg : gycxlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(gycxlkg, "红河地调", stationName);
								}
							}
							
							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						}else{
							dycmlkglist.addAll(gycxlkglist);
							
							for(PowerDevice dycmlkg : dycmlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
									replaceStr += CommonFunctionHH.getXzZbListLxbhltxdStrReplace(zbList,"投入");
								}
							}
							
							for(PowerDevice dycmlkg : dycmlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(dycmlkg, "红河地调", stationName);
									break;
								}
							}

							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						}
						
						replaceStr += "核实110kV备自投装置充电且运行正常/r/n";
						
						for(PowerDevice dev : dycmlkglist){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
								replaceStr += CommonFunctionHH.getXzZbZxdTcStrReplace(zbList);
							}
						}
						
						replaceStr += CommonFunctionHH.getZbNqjxBhStrReplace(zbList,dycmlkglist,gycxlkglist);
						
						RuleExeUtil.swapLowDeviceList(fhcmlkglist);
						
						for(PowerDevice fhcmlkg : fhcmlkglist){
							if(RuleExeUtil.getDeviceEndStatus(fhcmlkg).equals("1")){
								replaceStr += "投入"+(int)fhcmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
								replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+(int)fhcmlkg.getPowerVoltGrade()+"kV侧后备保护动作闭锁"+(int)fhcmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";								
								
								if(fhcmlkg.getPowerVoltGrade() == 10){
									String num = "";
											
									for(PowerDevice jdzybkg : jdzybkgList){
										String jdbName = jdzybkg.getPowerDeviceName();
										jdbName = jdbName.substring(0, jdbName.lastIndexOf("接地")+2);
										
										num += CZPService.getService().getDevNum(jdbName)+"、";
									}
									
									if(num.endsWith("、")){
										num = num.substring(0, num.length()-1);
									}
									
									replaceStr += "投入10kV"+num+"接地变保护动作闭锁10kV备自投装置/r/n";
								}
							}
						}
					}else if(curDev.getPowerVoltGrade() == 35){
						if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){//
							RuleExeUtil.swapLowDeviceList(fhcmlkglist);
							List<PowerDevice> tempList = new ArrayList<PowerDevice>();
							tempList.addAll(fhcmlkglist);
							
							for(PowerDevice gycxlkg : gycxlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("1")){
									tempList.add(gycxlkg);
								}
							}
							
							for(PowerDevice gycxlkg : gycxlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(gycxlkg, "红河地调", stationName);
								}
							}
							
							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
							
							
						}else{
							dycmlkglist.addAll(gycxlkglist);
							
						
							
							for(PowerDevice dycmlkg : dycmlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(dycmlkg, "红河地调", stationName);
									break;
								}
							}
							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						}
						
						if(dycmlkglist.size()>0){
							if(dycmlkglist.get(0).getDeviceStatus().equals("1")){
								RuleExeUtil.swapDeviceList(zbList);
								
								replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"差动、高后备、非电量保护动作闭锁35kV备自投装置/r/n";
							}else{
								if(gycxlkglist.size()>0){
									for(PowerDevice gycxlkg : gycxlkglist){
										if(gycxlkg.getDeviceStatus().equals("0")){
											List<PowerDevice> templist = RuleExeUtil.getDeviceList(gycxlkg, SystemConstants.PowerTransformer, SystemConstants.Switch, true, false, true);
											
											replaceStr += "退出"+CZPService.getService().getDevName(templist)+"差动、高后备、非电量保护动作闭锁35kV备自投装置/r/n";
										}else if(gycxlkg.getDeviceStatus().equals("1")){
											List<PowerDevice> templist = RuleExeUtil.getDeviceList(gycxlkg, SystemConstants.PowerTransformer, SystemConstants.Switch, true, false, true);
											
											replaceStr += "投入"+CZPService.getService().getDevName(templist)+"差动、高后备、非电量保护动作闭锁35kV备自投装置/r/n";
										}
									}
								}
							}
						}
					}
				}else if(RuleExeUtil.isTransformerZBDM(zbList.get(0))&&dycmlkglist.size()>0){//单母分段接线
					if(curDev.getPowerVoltGrade() == 220){
						if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){//
							RuleExeUtil.swapLowDeviceList(fhcmlkglist);
							
							for(PowerDevice gycxlkg : gycxlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(gycxlkg, "红河地调", stationName);
								}
							}
							
							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						}else{
							dycmlkglist.addAll(gycxlkglist);
							
							for(PowerDevice dycmlkg : dycmlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(dycmlkg, "红河地调", stationName);
									break;
								}
							}

							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						}
						
						replaceStr += "投入220kV母差保护动作闭锁220kV备自投装置/r/n";
					}else if(curDev.getPowerVoltGrade() == 110){
						if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){//
							RuleExeUtil.swapLowDeviceList(fhcmlkglist);
							
							List<PowerDevice> tempList = new ArrayList<PowerDevice>();
							
							for(PowerDevice gycxlkg : gycxlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("1")){
									tempList.add(gycxlkg);
								}
							}
							
							for(PowerDevice dev : zdyczbkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									tempList.add(dev);
								}
							}
							
							for(Iterator<PowerDevice> itor = fhcmlkglist.iterator();itor.hasNext();){
								PowerDevice dev = itor.next();
								
								List<PowerDevice> mxList =  RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
								
								if(mxList.size()>=2){
									if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
										tempList.add(dev);
									}
								}
							}
							
							replaceStr += CommonFunctionHH.getZbBLTQStrReplace(zbList.get(0));
							replaceStr += CommonFunctionHH.getJdzybStrReplace(zbList);
							
							for(PowerDevice dev : zdyczbkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += "红河地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
								}
							}
							
							replaceStr += CommonFunctionHH.getYcDkStrReplace(fhcmlkglist, stationName);
							
							for(PowerDevice gycxlkg : gycxlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(gycxlkg, "红河地调", stationName);
								}
							}
							
							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						}else{
							List<PowerDevice> tempList = new ArrayList<PowerDevice>();
							
							tempList.addAll(dycmlkglist);
							tempList.addAll(gycxlkglist);
							
							/*for(PowerDevice dev : tempList){
								if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
									replaceStr += CommonFunctionHH.getXzZbListLxbhltxdStrReplace(zbList,"投入");
								}
							}*/
							
							for(PowerDevice dycmlkg : tempList){
								if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(dycmlkg, "红河地调", stationName);
									break;
								}
							}
							
							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						}
						
						for(PowerDevice dev : dycmlkglist){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
								replaceStr += CommonFunctionHH.getXzZbZxdTcStrReplace(zbList);
							}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += CommonFunctionHH.getXzZbZxdTrStrReplace(zbList);
							}
						}
						
						for(PowerDevice dev : dycmlkglist){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
								replaceStr += CommonFunctionHH.getXzZbListLxbhltxdStrReplace(zbList,"退出");
							}
						}
						
						RuleExeUtil.swapLowDeviceList(fhcmlkglist);
						
						for(PowerDevice fhcmlkg : fhcmlkglist){
							if(RuleExeUtil.getDeviceEndStatus(fhcmlkg).equals("1")){
								replaceStr += "投入"+(int)fhcmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
								replaceStr += "投入"+CZPService.getService().getDevName(zbList)+"第Ⅰ、Ⅱ套"+(int)fhcmlkg.getPowerVoltGrade()+"kV侧后备保护动作闭锁"+(int)fhcmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";								
								
								if(fhcmlkg.getPowerVoltGrade() == 10){
									String num = "";
											
									for(PowerDevice jdzybkg : jdzybkgList){
										String jdbName = jdzybkg.getPowerDeviceName();
										jdbName = jdbName.substring(0, jdbName.lastIndexOf("接地")+2);
										
										num += CZPService.getService().getDevNum(jdbName)+"、";
									}
									
									if(num.endsWith("、")){
										num = num.substring(0, num.length()-1);
									}
									
									replaceStr += "投入10kV"+num+"接地变保护动作闭锁10kV备自投装置/r/n";
								}
							}
						}
					}else if(curDev.getPowerVoltGrade() == 35){
						if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){//
							RuleExeUtil.swapLowDeviceList(fhcmlkglist);
							
							List<PowerDevice> tempList = new ArrayList<PowerDevice>();
							tempList.addAll(fhcmlkglist);
							
							for(PowerDevice gycxlkg : gycxlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("1")){
									tempList.add(gycxlkg);
								}
							}
							
							for(PowerDevice gycxlkg : gycxlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(gycxlkg, "红河地调", stationName);
								}
							}
							
							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						}else{
							dycmlkglist.addAll(gycxlkglist);
							
							for(PowerDevice dycmlkg : dycmlkglist){
								if(RuleExeUtil.getDeviceBeginStatus(dycmlkg).equals("1")){
									replaceStr += CommonFunctionHH.getHhContent(dycmlkg, "红河地调", stationName);
									break;
								}
							}

							replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						}
					}
				}else if(curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					PowerDevice hhkg = new PowerDevice();
					
					if(HHKGHHDDExecute.chooseDmEquips.size()>0){
						for(PowerDevice rbykg : rbykglist){
							if(RuleExeUtil.getDeviceEndStatus(rbykg).equals("0")){
								hhkg = rbykg;
							}
						}
						
						List<PowerDevice> yx1mxList = new ArrayList<PowerDevice>();
						List<PowerDevice> yx2mxList = new ArrayList<PowerDevice>();

						List<PowerDevice> rby1mxList = new ArrayList<PowerDevice>();
						List<PowerDevice> rby2mxList = new ArrayList<PowerDevice>();
						
						for(PowerDevice xlkg : HHKGHHDDExecute.chooseDmEquips){
							List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(xlkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
							
							for(PowerDevice mxdz : mxdzList){
								if(RuleExeUtil.getDeviceBeginStatus(mxdz).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
									
									if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(xlkg).equals("0")){
										if(CZPService.getService().getDevNum(mxList.get(0)).equals("Ⅰ")){
											yx1mxList.add(xlkg);
										}else if(CZPService.getService().getDevNum(mxList.get(0)).equals("Ⅱ")){
											yx2mxList.add(xlkg);
										}
									}
									
									if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(xlkg).equals("1")){
										if(CZPService.getService().getDevNum(mxList.get(0)).equals("Ⅰ")){
											rby1mxList.add(xlkg);
										}else if(CZPService.getService().getDevNum(mxList.get(0)).equals("Ⅱ")){
											rby2mxList.add(xlkg);
										}
									}
								}
							}
						}
						
						if(yx1mxList.size()>0){
							StringBuffer reBuffer = new StringBuffer();
							
							for(PowerDevice yxkf: yx1mxList){
								reBuffer.append(CZPService.getService().getDevName(yxkf)+"、");
							}
							
							reBuffer.delete(reBuffer.length()-1, reBuffer.length());
							
							List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(yx1mxList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
							
							PowerDevice tagmx = new PowerDevice();

							for(PowerDevice mxdz : mxdzList){
								if(RuleExeUtil.getDeviceBeginStatus(mxdz).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
									
									tagmx = mxList.get(0);
								}
							}
							
							replaceStr += "核实"+reBuffer+"运行于"+CZPService.getService().getDevName(tagmx)+"/r/n";
						}
						
						if(yx2mxList.size()>0){
							StringBuffer reBuffer = new StringBuffer();
							
							for(PowerDevice yxkf: yx2mxList){
								reBuffer.append(CZPService.getService().getDevName(yxkf)+"、");
							}
							
							reBuffer.delete(reBuffer.length()-1, reBuffer.length());
							
							List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(yx2mxList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
							
							PowerDevice tagmx = new PowerDevice();

							for(PowerDevice mxdz : mxdzList){
								if(RuleExeUtil.getDeviceBeginStatus(mxdz).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
									
									tagmx = mxList.get(0);
								}
							}
							
							replaceStr += "核实"+reBuffer+"运行于"+CZPService.getService().getDevName(tagmx)+"/r/n";
						}
						
						if(rby1mxList.size()>0){
							StringBuffer reBuffer = new StringBuffer();
							
							for(PowerDevice yxkf: rby1mxList){
								reBuffer.append(CZPService.getService().getDevName(yxkf)+"、");
							}
							
							reBuffer.delete(reBuffer.length()-1, reBuffer.length());
							
							List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(rby1mxList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
							
							PowerDevice tagmx = new PowerDevice();

							for(PowerDevice mxdz : mxdzList){
								if(RuleExeUtil.getDeviceBeginStatus(mxdz).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
									
									tagmx = mxList.get(0);
								}
							}
							
							replaceStr += "核实"+reBuffer+"热备用于"+CZPService.getService().getDevName(tagmx)+"/r/n";
						}
						
						if(rby2mxList.size()>0){
							StringBuffer reBuffer = new StringBuffer();
							
							for(PowerDevice yxkf: rby2mxList){
								reBuffer.append(CZPService.getService().getDevName(yxkf)+"、");
							}
							
							reBuffer.delete(reBuffer.length()-1, reBuffer.length());
							
							List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(rby2mxList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
							
							PowerDevice tagmx = new PowerDevice();

							for(PowerDevice mxdz : mxdzList){
								if(RuleExeUtil.getDeviceBeginStatus(mxdz).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
									
									tagmx = mxList.get(0);
								}
							}
							
							replaceStr += "核实"+reBuffer+"热备用于"+CZPService.getService().getDevName(tagmx)+"/r/n";
						}
						
						List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
						List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

						List<PowerDevice> yxmxList1 = new ArrayList<PowerDevice>();
						List<PowerDevice> yxmxList2 = new ArrayList<PowerDevice>();

						List<PowerDevice> rbymxList1 = new ArrayList<PowerDevice>();
						List<PowerDevice> rbymxList2 = new ArrayList<PowerDevice>();

						
						for(PowerDevice dev : HHKGHHDDExecute.chooseDmEquips){
							String state = RuleExeUtil.getStatusNew(dev.getDeviceType(), RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev));
							
							if(state.equals("运行")){
								yxkgList.add(dev);
							}
							
							if(state.equals("热备用")){
								rbykgList.add(dev);
							}
						}
						
						if(yxkgList.size()>0){
							for(PowerDevice yxkg : yxkgList){
								List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(yxkg, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
								
								for(PowerDevice mxdz : mxdzList){
									if(RuleExeUtil.getDeviceBeginStatus(mxdz).equals("0")){
										List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
										
										if(CZPService.getService().getDevNum(mxList.get(0)).equals("Ⅰ")){
											yxmxList1.add(yxkg);
										}else if(CZPService.getService().getDevNum(mxList.get(0)).equals("Ⅱ")){
											yxmxList2.add(yxkg);
										}
									}
								}
							}
						}
						
						
						if(rbykgList.size()>0){
							for(PowerDevice rbykf: rbykgList){
								List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(rbykf, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
								
								for(PowerDevice mxdz : mxdzList){
									if(RuleExeUtil.getDeviceBeginStatus(mxdz).equals("0")){
										List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
										
										if(CZPService.getService().getDevNum(mxList.get(0)).equals("Ⅰ")){
											rbymxList1.add(rbykf);
										}else if(CZPService.getService().getDevNum(mxList.get(0)).equals("Ⅱ")){
											rbymxList2.add(rbykf);
										}
									}
								}
							}
						}
						
						if(yxmxList1.size()>0){
							StringBuffer reBuffer = new StringBuffer();
							
							for(PowerDevice yxkf: yxmxList1){
								reBuffer.append(CZPService.getService().getDevName(yxkf)+"、");
							}
							
							reBuffer.delete(reBuffer.length()-1, reBuffer.length());
							
							List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(yxmxList1.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
							
							PowerDevice tagmx = new PowerDevice();
							PowerDevice othermx = new PowerDevice();

							for(PowerDevice mxdz : mxdzList){
								if(RuleExeUtil.getDeviceEndStatus(mxdz).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
									tagmx = mxList.get(0);
									
									List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(tagmx, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
									
									othermx = othermxList.get(0);
								}
							}
							
							replaceStr += "将"+reBuffer+"由"+CZPService.getService().getDevName(othermx)+"运行倒至"+CZPService.getService().getDevName(tagmx)+"运行/r/n";
						}
						
						
						if(yxmxList2.size()>0){
							StringBuffer reBuffer = new StringBuffer();

							for(PowerDevice rbykf: yxmxList2){
								reBuffer.append(CZPService.getService().getDevName(rbykf)+"、");
							}
							
							reBuffer.delete(reBuffer.length()-1, reBuffer.length());
							
							List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(yxmxList2.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
							
							PowerDevice tagmx = new PowerDevice();
							PowerDevice othermx = new PowerDevice();

							for(PowerDevice mxdz : mxdzList){
								if(RuleExeUtil.getDeviceEndStatus(mxdz).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
									tagmx = mxList.get(0);
									
									List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(tagmx, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
									
									othermx = othermxList.get(0);
								}
							}
							
							replaceStr += "将"+reBuffer+"由"+CZPService.getService().getDevName(othermx)+"运行倒至"+CZPService.getService().getDevName(tagmx)+"运行/r/n";
						}
						
						
						if(rbymxList1.size()>0){
							StringBuffer reBuffer = new StringBuffer();

							for(PowerDevice yxkf: rbymxList1){
								reBuffer.append(CZPService.getService().getDevName(yxkf)+"、");
							}
							
							reBuffer.delete(reBuffer.length()-1, reBuffer.length());
							
							List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(rbymxList1.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
							
							PowerDevice tagmx = new PowerDevice();
							PowerDevice othermx = new PowerDevice();

							for(PowerDevice mxdz : mxdzList){
								if(RuleExeUtil.getDeviceEndStatus(mxdz).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
									tagmx = mxList.get(0);
									
									List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(tagmx, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
									
									othermx = othermxList.get(0);
								}
							}
							
							replaceStr += "将"+reBuffer+"由"+CZPService.getService().getDevName(othermx)+"热备用倒至"+CZPService.getService().getDevName(tagmx)+"热备用/r/n";
						
						}
						
						
						if(rbymxList2.size()>0){
							StringBuffer reBuffer = new StringBuffer();

							for(PowerDevice rbykf: rbymxList2){
								reBuffer.append(CZPService.getService().getDevName(rbykf)+"、");
							}
							
							reBuffer.delete(reBuffer.length()-1, reBuffer.length());
							
							List<PowerDevice> mxdzList =  RuleExeUtil.getDeviceList(rbymxList2.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeMX,"", true,true, true, true);
							
							PowerDevice tagmx = new PowerDevice();
							PowerDevice othermx = new PowerDevice();

							for(PowerDevice mxdz : mxdzList){
								if(RuleExeUtil.getDeviceEndStatus(mxdz).equals("0")){
									List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(mxdz, SystemConstants.MotherLine);
									tagmx = mxList.get(0);
									
									List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(tagmx, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
									
									othermx = othermxList.get(0);
								}
							}
							replaceStr += "将"+reBuffer+"由"+CZPService.getService().getDevName(othermx)+"热备用倒至"+CZPService.getService().getDevName(tagmx)+"热备用/r/n";
						}
						
						replaceStr += "核实主变中性点接地刀闸切换装置的功能投退已按现场运行规程执行/r/n";
						
						replaceStr += "退出"+CZPService.getService().getDevName(hhkg)+"重合闸/r/n";
						
						
						if(hhkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							List<PowerDevice> lineList =  RuleExeUtil.getDeviceList(hhkg, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
							
							if(lineList.size()>0){
								List<PowerDevice> otherlineList =RuleExeUtil.getLineOtherSideList(lineList.get(0));
								
								for(PowerDevice otherline : otherlineList){
									String otherstationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(otherline.getPowerStationID()));
									
									List<PowerDevice> linekgList =  RuleExeUtil.getLinkedSwitch(otherline);
									
									replaceStr += otherstationName+"@将"+CZPService.getService().getDevName(otherline)+CZPService.getService().getDevNum(linekgList.get(0))+"线路保护改投“”定值/r/n";
								}
								
								for(PowerDevice otherline : otherlineList){
									String otherstationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(otherline.getPowerStationID()));

									replaceStr += otherstationName+"@投入"+CZPService.getService().getDevName(RuleExeUtil.getLinkedSwitch(otherline))+"重合闸/r/n";
								}
							}
						}
						
						replaceStr += CommonFunctionHH.getHhContent(hhkg, "红河地调", stationName);
						replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
						
						if(hhkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							List<PowerDevice> lineList =  RuleExeUtil.getDeviceList(hhkg, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);

							replaceStr += "退出"+CZPService.getService().getDevName(lineList)+CZPService.getService().getDevNum(hhkg)+"线路保护/r/n";
						}
					}
				}else if(RuleExeUtil.isTransformerZBDM(zbList.get(0))&&dycmlkglist.size()==0){
					for(PowerDevice dycxlkg : gycxlkglist){
						if(RuleExeUtil.getDeviceEndStatus(dycxlkg).equals("0")){
							replaceStr += CommonFunctionHH.getHhContent(dycxlkg, "红河地调", stationName);
						}
					}
					
					replaceStr += "红河地调@遥控断开"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
				}
			}
			
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}