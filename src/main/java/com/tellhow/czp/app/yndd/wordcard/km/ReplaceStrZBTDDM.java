package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.km.OtherTransformChangeMotherLine;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBTDDM implements TempStringReplace {

    @Override
    public String strReplace(String tempStr, PowerDevice curDev,
                             PowerDevice stationDev, String desc) {
        StringBuilder replaceStr = new StringBuilder();
        if ("主变停电倒母".equals(tempStr)) {
            int curvolt = (int) curDev.getPowerVoltGrade();
            int gycvolt = (int) RuleExeUtil.getTransformerVolByType(curDev, "high");
            int zycvolt = (int) RuleExeUtil.getTransformerVolByType(curDev, "middle");
            int dycvolt = (int) RuleExeUtil.getTransformerVolByType(curDev, "low");

            PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
            String stationName = CZPService.getService().getDevName(station);

            List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
            List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
            List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
            List<PowerDevice> xlList = new ArrayList<PowerDevice>();

            HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

            for (PowerDevice dev : mapStationDevice.values()) {
                if (dev.getDeviceType().equals(SystemConstants.Switch)) {
                    if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
                        if (dev.getPowerVoltGrade() == stationDev.getPowerVoltGrade() && RuleExeUtil.getDeviceBeginStatus(dev).equals("0")) {
                            xlList.add(dev);
                        }
                    }
                }
            }

            for (PowerDevice dev : mapStationDevice.values()) {
                if (dev.getDeviceType().equals(SystemConstants.Switch)
                        && dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
                    if (dycvolt == dev.getPowerVoltGrade()) {
                        if (RuleExeUtil.getDeviceBeginStatus(dev).equals("1")) {
                            dycmlkgList.add(dev);
                        }
                    } else if (zycvolt == dev.getPowerVoltGrade()) {
                        if (RuleExeUtil.getDeviceBeginStatus(dev).equals("1")) {
                            zycmlkgList.add(dev);
                        }
                    } else if (gycvolt == dev.getPowerVoltGrade()) {
                        if (RuleExeUtil.getDeviceBeginStatus(dev).equals("1")) {
                            gycmlkgList.add(dev);
                        }
                    }
                }
            }


            List<PowerDevice> gdList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

            if (!gdList.isEmpty()) {
                for (PowerDevice gd : gdList) {
                    String devName = CZPService.getService().getDevName(gd);
                    devName=devName.replace("主变中性点", "主变"+(int)gd.getPowerVoltGrade()+"kV侧中性点");
                    replaceStr.append("落实").append(devName).append("处合位/r/n");
                }
            }

            if (!gycmlkgList.isEmpty() && curvolt != dycvolt) {
                if (!OtherTransformChangeMotherLine.map.isEmpty()) {
                    replaceStr.append("云南省调@落实220kVXXX变220kV母线与220kVXXX变220kV母线为同期系统/r/n");

                    replaceStr.append("昆明地调@遥控合上").append(stationName).append(CZPService.getService().getDevName(gycmlkgList.get(0))).append("/r/n");

                    for (PowerDevice xl : xlList) {
                        replaceStr.append("昆明地调@遥控断开").append(stationName).append(CZPService.getService().getDevName(xl)).append("/r/n");
                    }
                }
            }

            if (!zycmlkgList.isEmpty()) {
                if (RuleExeUtil.getDeviceBeginStatus(zycmlkgList.get(0)).equals("0") || RuleExeUtil.getDeviceBeginStatus(zycmlkgList.get(0)).isEmpty()) {
                    for (PowerDevice zycmlkg : zycmlkgList) {
                        replaceStr.append("落实").append(CZPService.getService().getDevName(zycmlkg)).append("已处合位/r/n");
                    }
                } else {
                    if (!OtherTransformChangeMotherLine.map.isEmpty()) {
                        if (OtherTransformChangeMotherLine.map.get("中压侧需要倒母").equals("true")) {
                            for (PowerDevice zycmlkg : zycmlkgList) {
                                replaceStr.append("昆明地调@遥控合上").append(stationName).append(CZPService.getService().getDevName(zycmlkg)).append("/r/n");
                            }
                        } else {
                            for (PowerDevice zycmlkg : zycmlkgList) {
                                if (RuleExeUtil.isDeviceHadStatus(zycmlkg, "1", "0")) {
                                    replaceStr.append("昆明地调@遥控合上").append(stationName).append(CZPService.getService().getDevName(zycmlkg)).append("/r/n");
                                }
                            }
                        }
                    }
                }

            }

            List<PowerDevice> kgList = new ArrayList<PowerDevice>();

            for (PowerDevice dev : mapStationDevice.values()) {
                if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) && !CZPService.getService().getDevName(dev).contains("相")) {
                    if (dev.getPowerVoltGrade() <= station.getPowerVoltGrade()) {
                        kgList.add(dev);
                    }
                }
            }

            if (kgList.isEmpty()) {
                return null;
            }

            RuleExeUtil.swapLowDeviceList(kgList);

            for (PowerDevice dycmlkg : dycmlkgList) {
                if (RuleExeUtil.isDeviceHadStatus(dycmlkg, "1", "0")) {
                    replaceStr.append("昆明地调@遥控合上").append(stationName).append(CZPService.getService().getDevName(dycmlkg)).append("/r/n");
                }
            }

            DispatchTransDevice dtd;
            PowerDevice dev;
            CommonSearch cs = new CommonSearch();
            Map<String, Object> inPara = new HashMap<String, Object>();
            Map<String, Object> outPara = new HashMap<String, Object>();
            List<PowerDevice> zylist = RuleExeUtil.getTransformerSwitchMiddle(curDev);
            List<PowerDevice> dylist = RuleExeUtil.getTransformerSwitchLow(curDev);
            List searchDevs;

            Map<Integer, DispatchTransDevice> dtds = CBSystemConstants.getDtdMap();

            for (DispatchTransDevice dispatchTransDevice : dtds.values()) {
                dtd = dispatchTransDevice;
                dev = dtd.getTransDevice();

                if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
                    inPara.put("oprSrcDevice", dev);
                    inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
                    inPara.put("isSearchDirectDevice", "true");
                    cs.execute(inPara, outPara);
                    searchDevs = (ArrayList) outPara.get("linkedDeviceList");
                    if (searchDevs.isEmpty())
                        continue;

                    PowerDevice kg;
                    List<PowerDevice> swList = RuleExeUtil.getKnifeRelateSwitch(dev);

                    if (!swList.isEmpty()) {
                        kg = swList.get(0);

                        PowerDevice yml = (PowerDevice) searchDevs.get(0);
                        PowerDevice mbml;
                        if (!RuleExeUtil.isDeviceChanged(kg) && dtd.getBeginstatus().equals("0") &&
                                yml.getPowerStationID().equals(stationDev.getPowerStationID()) &&
                                yml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
                            mbml = RuleExeUtil.getMLDoubleOther(yml);

                            if (OtherTransformChangeMotherLine.map.containsKey("高压侧需要倒母")) {
                                if (OtherTransformChangeMotherLine.map.get("高压侧需要倒母").equals("true") && kg.getPowerVoltGrade() == curDev.getPowerVoltGrade()) {
                                    replaceStr.append("将").append(CZPService.getService().getDevName(kg)).append("由").append(CZPService.getService().getDevName(yml)).append("运行倒至").append(CZPService.getService().getDevName(mbml)).append("运行/r/n");
                                }
                            }
                        }
                    }
                }
            }

            for (DispatchTransDevice dispatchTransDevice : dtds.values()) {
                dtd = dispatchTransDevice;
                dev = dtd.getTransDevice();

                if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
                    inPara.put("oprSrcDevice", dev);
                    inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
                    inPara.put("isSearchDirectDevice", "true");
                    cs.execute(inPara, outPara);
                    searchDevs = (ArrayList) outPara.get("linkedDeviceList");
                    if (searchDevs.isEmpty())
                        continue;

                    PowerDevice kg;
                    List<PowerDevice> swList = RuleExeUtil.getKnifeRelateSwitch(dev);

                    if (!swList.isEmpty()) {
                        kg = swList.get(0);

                        PowerDevice yml = (PowerDevice) searchDevs.get(0);
                        PowerDevice mbml;
                        if (!RuleExeUtil.isDeviceChanged(kg) && dtd.getBeginstatus().equals("0") &&
                                yml.getPowerStationID().equals(stationDev.getPowerStationID()) &&
                                yml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
                            mbml = RuleExeUtil.getMLDoubleOther(yml);

                            if (OtherTransformChangeMotherLine.map.containsKey("中压侧需要倒母")) {
                                if (OtherTransformChangeMotherLine.map.get("中压侧需要倒母").equals("true") && kg.getPowerVoltGrade() == zylist.get(0).getPowerVoltGrade()) {
                                    replaceStr.append("将").append(CZPService.getService().getDevName(kg)).append("由").append(CZPService.getService().getDevName(yml)).append("运行倒至").append(CZPService.getService().getDevName(mbml)).append("运行/r/n");
                                }
                            }
                        }
                    }
                }
            }

            for (DispatchTransDevice dispatchTransDevice : dtds.values()) {
                dtd = dispatchTransDevice;
                dev = dtd.getTransDevice();

                if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
                    inPara.put("oprSrcDevice", dev);
                    inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
                    inPara.put("isSearchDirectDevice", "true");
                    cs.execute(inPara, outPara);
                    searchDevs = (ArrayList) outPara.get("linkedDeviceList");
                    if (searchDevs.isEmpty())
                        continue;

                    PowerDevice kg;
                    List<PowerDevice> swList = RuleExeUtil.getKnifeRelateSwitch(dev);

                    if (!swList.isEmpty()) {
                        kg = swList.get(0);

                        PowerDevice yml = (PowerDevice) searchDevs.get(0);
                        PowerDevice mbml;
                        if (!RuleExeUtil.isDeviceChanged(kg) && dtd.getBeginstatus().equals("0") &&
                                yml.getPowerStationID().equals(stationDev.getPowerStationID()) &&
                                yml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
                            mbml = RuleExeUtil.getMLDoubleOther(yml);

                            if (OtherTransformChangeMotherLine.map.containsKey("低压侧需要倒母")) {
                                if (OtherTransformChangeMotherLine.map.get("低压侧需要倒母").equals("true") && kg.getPowerVoltGrade() == dylist.get(0).getPowerVoltGrade()) {
                                    replaceStr.append("将").append(CZPService.getService().getDevName(kg)).append("由").append(CZPService.getService().getDevName(yml)).append("运行倒至").append(CZPService.getService().getDevName(mbml)).append("运行/r/n");
                                }
                            }
                        }
                    }
                }
            }

            if (replaceStr.length() == 0) {
                replaceStr = null;
            }
        }

        return replaceStr == null ? null : replaceStr.toString();
    }


}