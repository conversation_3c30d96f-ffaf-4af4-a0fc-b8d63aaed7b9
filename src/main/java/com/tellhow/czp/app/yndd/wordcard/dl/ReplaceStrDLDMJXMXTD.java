package com.tellhow.czp.app.yndd.wordcard.dl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDLDMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("大理单母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);
			
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

			double zycvolt = 0;
			double dycvolt = 0;

			/*
			 * 电源侧
			 */
			if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
				List<PowerDevice> zdycmlkgList = new ArrayList<PowerDevice>();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						zbList.add(dev);
						
						zycvolt = RuleExeUtil.getTransformerVolByType(dev, "middle");
						dycvolt = RuleExeUtil.getTransformerVolByType(dev, "low");
					}else if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
						if(dev.getPowerVoltGrade() == 10){
							dycmxList.add(dev);
						}
					}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(dev.getPowerVoltGrade() < station.getPowerVoltGrade()){
							zdycmlkgList.add(dev);
						}
					}
				}
				
				for(PowerDevice dev : zdycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						if(dev.getPowerVoltGrade() == zycvolt){
							zycmlkgList.add(dev);
						}else if(dev.getPowerVoltGrade() == dycvolt){
							dycmlkgList.add(dev);
						}
					}
				}
				
				if(dycmxList.size() > 0 && mlkgList.size() == 0){
					RuleExeUtil.swapDeviceListNum(dycmxList);
					
					replaceStr += "配网调度@落实"+stationName+"10kV负荷已转供，"+CZPService.getService().getDevName(dycmxList)+"具备停电条件/r/n";
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "大理地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
						replaceStr += "大理地调@检查"+stationName+CZPService.getService().getDevName(dev)+"三相潮流指示正常/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "大理地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
						replaceStr += "大理地调@检查"+stationName+CZPService.getService().getDevName(dev)+"三相潮流指示正常/r/n";
					}
				}
				
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "大理地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
						replaceStr += "大理地调@检查"+stationName+CZPService.getService().getDevName(dev)+"三相潮流指示正常/r/n";
					}
				}
				
				for(PowerDevice dev : zbList){
					if(RuleExeUtil.isDeviceChanged(dev)){
						List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
						List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						
						for(PowerDevice zbdyckg : zbdyckgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(zbdyckg).equals("1")){
								replaceStr += stationName+"@落实"+CZPService.getService().getDevName(zbdyckg)+"处热备用/r/n";
							}
						}
						
						for(PowerDevice zbzyckg : zbzyckgList){
							if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(zbzyckg).equals("1")){
								replaceStr += stationName+"@落实"+CZPService.getService().getDevName(zbzyckg)+"处热备用/r/n";
							}
						}
						
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

						for(PowerDevice zxddz : gdList){
							replaceStr += stationName+"@落实"+CZPService.getService().getDevName(zxddz)+"在合上位置/r/n";
						}
					}
				}
				
				for(PowerDevice dev : zbList){
					List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
					List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
					List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
					
					for(PowerDevice zbdyckg : zbdyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbdyckg).equals("0")){
							replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
					
					for(PowerDevice zbzyckg : zbzyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbzyckg).equals("0")){
							replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(zbzyckg)+"/r/n";
						}
					}
					
					for(PowerDevice zbgyckg : zbgyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(zbgyckg).equals("0")){
							replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(zbgyckg)+"/r/n";
						}
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}else{//负荷侧
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
				List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);

				for(PowerDevice dev : zbkgList){
					if(RuleExeUtil.isDeviceHadStatus(dev, "0", "1")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : xlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : qtkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "大理地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
