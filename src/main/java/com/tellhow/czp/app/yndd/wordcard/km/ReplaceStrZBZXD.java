package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 */
public class ReplaceStrZBZXD implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("主变中性点".equals(tempStr)){
			List<PowerDevice> zbLists = new ArrayList<PowerDevice>();
			for(Iterator<PowerDevice> iter = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID()).values().iterator();iter.hasNext();) {
				PowerDevice pd =  iter.next();
				Map<String,Object> inMap = new HashMap<String,Object>();
				inMap.put("oprSrcDevice", pd);
				if(SystemConstants.PowerTransformer.equals(pd.getDeviceType())){
					if(!pd.getPowerDeviceName().contains("所用变")){
						zbLists.add(pd);
					}
				}
			}
			
			RuleExeUtil.swapDeviceListNum(zbLists);
			
			if(zbLists.size()==1){
				replaceStr = CZPService.getService().getDevName(zbLists.get(0));
			}else if(zbLists.size()>1){
				int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择主变", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, zbLists.toArray(new PowerDevice[]{}), null);
				
				if(sel!=-1){
					replaceStr = CZPService.getService().getDevName(zbLists.get(sel));
				}else{
					return null;
				}
			}
		}
		
		return replaceStr;
	}

}
