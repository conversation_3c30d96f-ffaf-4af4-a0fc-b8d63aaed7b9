package com.tellhow.czp.app.yndd.wordcard.dq;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dq.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDQ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQSMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆双母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getTransformerKnifeLoad(curDev);

			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			double midvolt = RuleExeUtil.getTransformerVolByType(curDev, "middle");
			double lowvolt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			List<PowerDevice> zbdycdzList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList =  new ArrayList<PowerDevice>();
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			Collections.reverse(zxdjddzList);
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> gycmlkgList =  new ArrayList<PowerDevice>();			
			
			for(PowerDevice dev : zbdzList){
				if(dev.getPowerVoltGrade() == lowvolt){
					zbdycdzList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}		
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					gycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
						RuleExeUtil.swapLowDeviceList(gdList);
						
						for(PowerDevice gd : gdList) {
							otherzxdjddzList.add(gd);
						}
					}
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			if(replaceStr.length() > 0){
				for(PowerDevice dev : gycmlkgList){
					replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"在合上位置/r/n";
				}
			}
			
			for(PowerDevice dev : zxdjddzList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"具备遥控操作条件/r/n";
					replaceStr += "迪庆地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"在合上位置/r/n";
				}
			}
			
			for(PowerDevice dev : otherzxdjddzList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"具备遥控操作条件/r/n";
					replaceStr += "迪庆地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"在合上位置/r/n";
				}
			}
			
			if(dycmlkgList.size() == 0){
				replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dycmxList)+"具备停电条件/r/n";
			}
			
			boolean isSwitchSeparateNotControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
					isSwitchSeparateNotControl = false;
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionDQ.getHhContent(dev, "迪庆地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(CommonFunctionDQ.ifSwitchControl(dev)){
					replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionDQ.getHhContent(dev, "迪庆地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(CommonFunctionDQ.ifSwitchControl(dev)){
					replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(CommonFunctionDQ.ifSwitchControl(dev)){
					replaceStr += "迪庆地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
			
			if(isSwitchSeparateNotControl){
				replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
			}else{
				for(PowerDevice dev : zbdyckgList){
					if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
						replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"间隔具备程序化控制条件/r/n";
						replaceStr += "迪庆地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
						replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbdyckgList,stationName);
						break;
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
						replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"间隔具备程序化控制条件/r/n";
						replaceStr += "迪庆地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
						replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbzyckgList,stationName);
						break;
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(CommonFunctionDQ.ifSwitchSeparateControl(dev)){
						replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"间隔具备程序化控制条件/r/n";
						replaceStr += "迪庆地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
						replaceStr += CommonFunctionDQ.getSequenceConfirmTdContent(zbgyckgList,stationName);
						break;
					}else{
						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
					}
				}
			}
			
			for(PowerDevice dev : zxdjddzList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += "迪庆地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += stationName+"@落实"+CZPService.getService().getDevName(dev)+"在拉开位置/r/n";
				}
			}
		}
		
		return replaceStr;
	}
}
