package com.tellhow.czp.app.yndd.wordcard.km;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooFJDY implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("非金刀营特殊接线".equals(tempStr)){
			if(!stationDev.getPowerStationName().contains("金刀营")){
				return true;
			}
		}
        return false;
	}
}
