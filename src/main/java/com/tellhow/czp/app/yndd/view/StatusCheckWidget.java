package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.Font;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import org.beryl.gui.GUIException;
import org.beryl.gui.Widget;
import org.beryl.gui.component.ImagePanel;
import com.tellhow.czp.sysconfig.UnitManager;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.PowerDevice;
import czprule.stationstartup.InitDeviceStatus;
import czprule.stationstartup.StationStartupManager;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;

public class StatusCheckWidget extends Widget implements ItemListener{
	private ImagePanel attributepanel=new ImagePanel(new javax.swing.ImageIcon(ImagePanel.class.getResource("/tellhow/icons/bg2.png")));
	private JComboBox nodeattributecomboBox=new JComboBox();
	public static JComboBox jComboBox = new JComboBox();
	public static JComboBox jComboBoxWord = new JComboBox();
	public static String offlineTime = "";
	public static String lineName = "";
	public JLabel sssjsj =new JLabel();
	private Thread timeThread;
	
	public JComboBox getNodeattributecomboBox() {
		return nodeattributecomboBox;
	}
	
	public void setNodeattributecomboBox(JComboBox nodeattributecomboBox) {
		this.nodeattributecomboBox = nodeattributecomboBox;
	}
	
	public JComboBox getjComboBox() {
		return jComboBox;
	}
	
	public void setjComboBox(JComboBox jComboBox) {
		this.jComboBox = jComboBox;
	}
	
	public StatusCheckWidget(Widget parent, String name) throws GUIException {
		super(parent, name);
		CBSystemConstants.cardflag="1";
		 
		 if(CBSystemConstants.roleCode.equals("0")) {
			sssjsj.setText("");
			sssjsj.setFont(new Font("宋体", Font.PLAIN, 12));
			attributepanel.add(sssjsj,BorderLayout.EAST);

			timeThread = new Thread(new Runnable() {
				public void run() {
					while (true) {
					    try {
					    	 timeThread.currentThread().sleep(10000);
							 String updateTime ="";
							 
							 if(CBSystemConstants.opcardUser.equals("OPCARDZT.") || CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
								 String sql = "SELECT TRUNC(sysdate-TO_DATE(REPLACE(CASE WHEN INSTR(TIME1, '.') > 0 THEN  SUBSTR(TIME1, 1, INSTR(TIME1, '.') - 1)  ELSE TIME1 END,'T',' '),'YYYY-MM-DD HH24:MI:SS'),3) "
								 		+ "AS TIMEOUT,REPLACE(TIME1,'T',' ') TIME1 FROM "+CBSystemConstants.opcardUser+"T_S_SYSTEM";
								 List<Map<String,String>> sqllist = DBManager.queryForList(sql);
								 
								 if(sqllist.size()>0){
									 updateTime = StringUtils.ObjToString(sqllist.get(0).get("TIME1"));

									 if(updateTime.contains(".")){
										 updateTime = updateTime.substring(0, updateTime.lastIndexOf("."));
									 }
									 
									 if(!updateTime.equals("")){
										 if(Double.parseDouble(StringUtils.ObjToString(sqllist.get(0).get("TIMEOUT")))*24*60>5){
											 sssjsj.setText("<font color='red'>当前解析实时数据时间已超过五分钟，最新解析时间："+updateTime+"</font>");
										 }else{
											 sssjsj.setText("实时数据最新解析时间："+updateTime+"");
										 }
									 }
								 }
							 }else if(CBSystemConstants.opcardUser.equals("OPCARDWS.")){
								 String sql = "SELECT 可读时间  FROM "+CBSystemConstants.opcardUser+"T_S_SYSTEM";
								 List sqllist = DBManager.queryForList(sql);
								 if(sqllist.size()>0){
									 updateTime = StringUtils.ObjToString(((Map)sqllist.get(0)).get("可读时间"));
								 }
							 }else if(CBSystemConstants.opcardUser.equals("OPCARDKM.")){
								 String sql = "SELECT TRUNC(sysdate-TO_DATE(REPLACE(TIME1,'T',''),'YYYY-MM-DD HH24:MI:SS'),3)AS TIMEOUT,REPLACE(TIME1,'T',' ') TIME1 FROM "+CBSystemConstants.opcardUser+"T_S_SYSTEM";
								 List<Map<String,String>> sqllist = DBManager.queryForList(sql);
								 
								 if(sqllist.size()>0){
									 updateTime = StringUtils.ObjToString(sqllist.get(0).get("TIME1"));

									 if(!updateTime.equals("")){
										 if(Double.parseDouble(StringUtils.ObjToString(sqllist.get(0).get("TIMEOUT")))*24*60>5){
											 sssjsj.setText("<font color='red'>当前解析实时数据时间已超过五分钟，最新解析时间："+updateTime+"</font>");
										 }else{
											 sssjsj.setText("实时数据最新解析时间："+updateTime+"");
										 }
									 }
								 }
							 }else if(CBSystemConstants.opcardUser.equals("OPCARDNJ.")){
								 String sql = "SELECT TRUNC(sysdate-TO_DATE(REPLACE(TIME1,'T',''),'YYYY-MM-DD HH24:MI:SS'),3)AS TIMEOUT,REPLACE(TIME1,'T',' ') TIME1 FROM "+CBSystemConstants.opcardUser+"T_S_SYSTEM";
								 List<Map<String,String>> sqllist = DBManager.queryForList(sql);
								 
								 if(sqllist.size()>0){
									 updateTime = StringUtils.ObjToString(sqllist.get(0).get("TIME1"));

									 if(!updateTime.equals("")){
										 if(Double.parseDouble(StringUtils.ObjToString(sqllist.get(0).get("TIMEOUT")))*24*60>5){
											 sssjsj.setText("<font color='red'>当前解析实时数据时间已超过五分钟，最新解析时间："+updateTime+"</font>");
										 }else{
											 sssjsj.setText("实时数据最新解析时间："+updateTime+"");
										 }
									 }
								 }
							 }else if(CBSystemConstants.opcardUser.equals("OPCARDDQ.")){
								 String sql = "SELECT TRUNC(sysdate-TO_DATE(REPLACE(可读时间,'T',''),'YYYY-MM-DD HH24:MI:SS'),3) AS TIMEOUT,REPLACE(可读时间,'T',' ') TIME1 FROM "+CBSystemConstants.opcardUser+"T_S_SYSTEM";
								 List<Map<String,String>> sqllist = DBManager.queryForList(sql);
								 
								 if(sqllist.size()>0){
									 updateTime = StringUtils.ObjToString(sqllist.get(0).get("TIME1"));

									 if(!updateTime.equals("")){
										 if(Double.parseDouble(StringUtils.ObjToString(sqllist.get(0).get("TIMEOUT")))*24*60>5){
											 sssjsj.setText("<font color='red'>当前解析实时数据时间已超过五分钟，最新解析时间："+updateTime+"</font>");
										 }else{
											 sssjsj.setText("实时数据最新解析时间："+updateTime+"");
										 }
									 }
								 }
							 }
							 
							 if(!updateTime.equals("")){
								 if(offlineTime.equals("")){
									 String cimTimeout="";
									 String sql1 = "SELECT TRUNC((sysdate-TIME),3) AS TIMEOUT, TIME FROM "+CBSystemConstants.opcardUser+"T_A_PARAMETER WHERE TYPE = 'cimimport' ";
									 List<Map<String,String>> sqllist1 = DBManager.queryForList(sql1);
									 
									 if(sqllist1.size()>0){
										 if(Double.parseDouble(StringUtils.ObjToString(sqllist1.get(0).get("TIMEOUT")))>1){
											 String time = StringUtils.ObjToString(sqllist1.get(0).get("TIME"));
											 
											 if(time.contains(".")){
												 time = time.substring(0, time.lastIndexOf("."));
											 }
											 
											 cimTimeout ="<br><font color='red'>当前解析模型时间已超过一天，最新解析时间："+time+"</font>";
											 
											 String result = sssjsj.getText();
											 sssjsj.setText("<html>"+result+cimTimeout+"</html>");
										 }else{
											 String time = StringUtils.ObjToString(sqllist1.get(0).get("TIME"));
											 cimTimeout ="<br>模型最新解析时间："+time+"";
											 
											 String result = sssjsj.getText();
											 sssjsj.setText("<html>"+result+cimTimeout+"</html>");
										 }
									 }
								 }else{
									 sssjsj.setFont(new Font("宋体", Font.PLAIN, 11));
									 sssjsj.setText("<html>实时数据最近更新时间："+updateTime+"<br><font color='red'>上次中断时间："+offlineTime+"</font></html>");
								 }
							 }
						} catch (InterruptedException e) {
							e.printStackTrace();
						} catch(Exception ex) {
							ex.printStackTrace();
							SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							offlineTime = sdf.format(new Date());
							sssjsj.setFont(new Font("宋体", Font.PLAIN, 11));
							sssjsj.setText("<html><font color='red'>  网络"+offlineTime+"中断，系统自动重连中......</font></html>");
						}
					}
				}
			});
			timeThread.start();
	  	 }
	}
	
	@Override
	public void itemStateChanged(ItemEvent e) {	
		if(e.getStateChange() == ItemEvent.SELECTED){
			UnitManager getuse=(UnitManager)nodeattributecomboBox.getSelectedItem();
			String getcode=getuse.getCode();
			if(getcode.equals("0")){
				CBSystemConstants.cardstatus="0";
			}else if(getcode.equals("1")){
				CBSystemConstants.cardstatus="1";
			}else{
				CBSystemConstants.cardstatus="2";
			}
			refushing();

		}
	}
	
	@Override
	public Component getWidget() {
		return attributepanel;
	}

	public static void refushing(){
		SVGCanvasPanel panelsel= SystemConstants.getGuiBuilder().getActivateSVGPanel();
		String stationIDsel = "";
		if(panelsel != null) {
			stationIDsel = panelsel.getStationID();
			if(!stationIDsel.equals("")) {
				refushingStation(stationIDsel);
			}
		}
		SvgUtil.clear(); 
	}
	
	public static void refushingStation(String stationID){
		InitDeviceStatus ie = new InitDeviceStatus();
		if(CBSystemConstants.cardstatus.equals("0"))
			StationStartupManager.startup(stationID);
		else if(CBSystemConstants.cardstatus.equals("1"))
			ie.initStatus_EMSToCache(stationID);
		else if(CBSystemConstants.cardstatus.equals("2"))
			StationStartupManager.startup(stationID);
		String[] sidarr = stationID.split(",");
		for(String sid : sidarr) {
			Map deviceMap=(Map)CBSystemConstants.getMapPowerStationDevice().get(sid);
			PowerDevice pd=null;
		
	    	for (Iterator iter = deviceMap.values().iterator(); iter.hasNext();) {
	    		pd=(PowerDevice) iter.next();
	    		if(pd==null)
	    			continue;
	    		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
			}
		}
	}
}
