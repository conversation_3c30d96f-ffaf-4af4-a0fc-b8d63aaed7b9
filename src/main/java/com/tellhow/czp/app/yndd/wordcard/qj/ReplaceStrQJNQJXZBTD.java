package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJNQJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖内桥接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate, CBSystemConstants.RunTypeKnifeZB);

			for (Iterator<PowerDevice> it = zbdzList.iterator(); it.hasNext();) {
				PowerDevice zbdz = it.next();
				
				if(zbgycdzList.contains(zbdz)){
					it.remove();
				}
			}
			
			List<PowerDevice> zbgycmxList = new ArrayList<PowerDevice>();

			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycotherxlkgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgycdzList){
				zbgycmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				gycxlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true);
			}

			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						if(!gycxlkgList.contains(dev)){
							gycotherxlkgList.add(dev);
						}
					}
				}
			}
					
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(jddzList);
				otherzxdjddzList.addAll(jddzList);
					}
					
			boolean ismxlby = false;
						
			for(PowerDevice dev : zbgycmxList){
				if(dev.getDeviceStatus().equals("2")){
					ismxlby = true;
					break;
				}
			}
			
			if(ismxlby){
				
				
			}else{
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
					for(PowerDevice dev : gycotherxlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
						}
					}
					
					for(PowerDevice dev : gycxlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionQJ.getSwitchOffContent(dev, stationName, station);
						}
					}
				
					if(zxdjddzList.size() > 0){
						replaceStr += CommonFunctionQJ.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
					}
					
					if(otherzxdjddzList.size() > 0){
						replaceStr += CommonFunctionQJ.getZxdJddzOnCheckContent(otherzxdjddzList, stationName, station);
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += "曲靖地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += "曲靖地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : gycmlkgList){
						replaceStr += CommonFunctionQJ.getSwitchOffContent(dev, stationName, station);
					}
					
					// 运行到热备用拉开中性点地刀
					if (!zxdjddzList.isEmpty()) {
						replaceStr += CommonFunctionQJ.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
					}
				}
				
				if(curDev.getDeviceStatus().equals("2")){
					for(PowerDevice dev : zbgycdzList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionQJ.getKnifeOffContent(zbgycdzList, stationName);
						}
					}
					
					for(PowerDevice dev : CommonFunctionQJ.chargeDeviceByNqZbTdList){
						replaceStr += CommonFunctionQJ.getSwitchOnContent(dev, stationName, station);
					}
					
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
						}
					}
				
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
						}
					}
					
					replaceStr += CommonFunctionQJ.getKnifeOffContent(zbdzList, stationName);
				}
			}
		}
		
		return replaceStr;
	}

}
