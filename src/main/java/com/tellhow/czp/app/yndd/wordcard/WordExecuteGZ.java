package com.tellhow.czp.app.yndd.wordcard;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.dao.CustomCodexDao;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.wordcard.WordExecute;
import czprule.wordcard.dao.DeviceStateMentManager;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;


/**
 * 操作票术语生成类（北京）
 * <AUTHOR>
 *
 */
public class WordExecuteGZ extends WordExecute {
	
	/**
	 * 智能票术语
	 * @param Srcrbm
	 * @return
	 */
	public CardModel execute(RuleBaseMode Srcrbm){
		
		CardModel results=new CardModel(); //返回结果
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		CustomCodexDao ccdd = new CustomCodexDao();
		PowerDevice curDev=Srcrbm.getPd();
		String srcStatus=Srcrbm.getBeginStatus();
		String stateCode=Srcrbm.getStateCode();
		String czrw="";//操作任务
		String bzsx="";
		List<String> descLists=null;  //操作指令集合
		
		//获取操作任务及术语模板
		String[]userCzrw=ccdd.getCzrw(curDev.getPowerDeviceID(), srcStatus, stateCode, CBSystemConstants.cardbuildtype); //自定义术语
		if("".equals(userCzrw[0].trim())){
		    czrw=dsmm.getCZRW(curDev.getDeviceType(), srcStatus, stateCode, CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);
		    bzsx=dsmm.getBZSX(curDev.getDeviceType(), srcStatus, stateCode, CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);
		    descLists=dsmm.getStateMents(curDev.getDeviceType(), srcStatus, stateCode,  CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);

		}else{
			czrw=userCzrw[0].trim();
			descLists=ccdd.getShuYu(userCzrw[1].trim());
		}
		
		
		//获取操作任务
		WordCardBuild wcb=new WordCardBuild();
		List<CardItemModel> tempItem=null;
		CardItemModel tempcim=null;
		String result = "";
		String[] rws = null;
		if(czrw.contains("\n"))
			rws = czrw.split("\n");
		else
			rws = czrw.split("，");
		for (int i = 0; i < rws.length; i++) {
			tempItem= wcb.execute(curDev,rws[i]);
			if(tempItem.size()>0){
				tempcim=tempItem.get(0);
				result = result + tempcim.getCardDesc() + "，";
			}
		}
		if(result.endsWith("，"))
			result = result.substring(0, result.length()-1);
		results.setCzrw(result);

		List<CardItemModel>  itemModelsPre=new ArrayList<CardItemModel>();
		List<CardItemModel>  itemModelsEnd=new ArrayList<CardItemModel>();
		List<CardItemModel>  itemModels=new ArrayList<CardItemModel>();

		String[] bzs = bzsx.replace("\r\n", "\n").split("\n");

		String tempBzsx = "";
		
		for (int i = 0; i < bzs.length; i++) {
			tempItem = wcb.execute(curDev, bzs[i]);
			for(CardItemModel item:tempItem){
				if (!item.getCardDesc().equals(""))
					tempBzsx +=item.getCardDesc()+"\r\n";
			}
		}
		
		if (tempBzsx.endsWith("\r\n"))
			tempBzsx= tempBzsx.substring(0, tempBzsx.length()-2);
		
		results.setBzsx(tempBzsx);		
		
		for (int i = 0; i < descLists.size(); i++) {
			String desc=descLists.get(i);
			tempItem= wcb.execute(curDev,desc);
			for (int j = 0; j < tempItem.size(); j++) {
				itemModels.add(tempItem.get(j));
			}
		}
		
		//设置步骤
		String preItem = "";
		int curStep = 0;
		for (int i = 0; i < itemModels.size(); i++) {
			String temp = itemModels.get(i).getCardItem();
			if(i == 0) {
				itemModels.get(i).setCardItem("1");
				curStep = 1;
			}
			else if(itemModels.get(i).getCardItem().equals(preItem)) {
				itemModels.get(i).setCardItem("");
			}
			else {
				curStep++;
				itemModels.get(i).setCardItem(String.valueOf(curStep));
			}
			preItem = temp;
		}
		
		results.setCardItems(itemModels);
		
		
		return results;
	}
	
	
	
	
	
	
	/**
	 * 根据规则执行结果生成术语
	 * @param notNeedDtd
	 * @return
	 */
	public List<CardItemModel> getCardItemByDtdAll() {
		
		List<DispatchTransDevice> dtdZXList = new ArrayList<DispatchTransDevice>();
		for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
			DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
			String deviceType = dtd.getTransDevice().getDeviceType();
			if(deviceType.equals(SystemConstants.Switch) || 
					deviceType.equals(SystemConstants.SwitchSeparate) ||
					deviceType.equals(SystemConstants.SwitchFlowGroundLine)) {
				if(dtd.getBeginstatus().equals("0") || dtd.getEndstate().equals("0")) {
					dtdZXList.add(dtd);
				}
			}
		}
		return getCardItemByDtd(dtdZXList,false);
	}
	
	

	public List<CardItemModel> getCardItemByDtd(List<DispatchTransDevice> dtdZXList, boolean isJudgeLength) {
		List<CardItemModel>  itemModels=new ArrayList<CardItemModel>();

		DeviceStateMentManager dsmm=new DeviceStateMentManager();

		for(DispatchTransDevice dtd : dtdZXList) {
			RuleBaseMode rbm = new RuleBaseMode();
			rbm.setPd(dtd.getTransDevice());
    		rbm.setBeginStatus(dtd.getBeginstatus());
    		rbm.setEndState(dtd.getEndstate());
    		rbm.setStateCode(dsmm.getStateCodeByStatus(dtd.getTransDevice().getDeviceType(), dtd.getEndstate()));
    		
    		CardModel cm=executeItem(rbm);
    		for (int j = 0; j < cm.getCardItems().size(); j++) {
    			CardItemModel item=new CardItemModel();
        		item.setCardDesc(cm.getCardItems().get(j).getCardDesc());
        		item.setStationName(cm.getCardItems().get(j).getStationName());
    			String uuID = cm.getCardItems().get(j).getUuIds();
    			item.setUuIds(uuID);
    			itemModels.add(item);
    			dtd.setUuID(uuID);
    		}
		}
	
		return itemModels;
	}
	
	/**
	 * 术语合并
	 * @param rbmlist
	 * @return
	 */
	public CardModel execute(List<RuleBaseMode> rbmlist) {
		CardModel results=new CardModel(); //返回结果
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		CustomCodexDao ccdd = new CustomCodexDao();
		PowerDevice curDev=new PowerDevice();
		//单个设备
		curDev=rbmlist.get(0).getPd();
		//设备集合
		List<PowerDevice> curDevlist = new ArrayList<PowerDevice>();
		
		for(int i=0;i<rbmlist.size();i++){
			PowerDevice curDevlinshi = new PowerDevice();
			curDevlinshi=rbmlist.get(i).getPd();
			curDevlist.add(curDevlinshi);
		}
		String srcStatus=rbmlist.get(0).getBeginStatus();
		String stateCode=rbmlist.get(0).getStateCode();
		String czrw=dsmm.getCZRW(curDev.getDeviceType(), srcStatus, stateCode, CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);

		List<String> descLists=dsmm.getStateMents(curDev.getDeviceType(), srcStatus, stateCode,  CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);
		
		
		WordCardBuild wcb=new WordCardBuild();
		List<CardItemModel> tempItem=null;
		CardItemModel tempcim=null;
		//操作任务替换
		String result = "";
		//czrw = czrw.replace(",", "，");
		String[] rws = czrw.split("，");
		for (int i = 0; i < rws.length; i++) {
			tempItem= wcb.execute(curDevlist,rws[i]);
			if(tempItem.size()>0){
				tempcim=tempItem.get(0);
				if(!tempcim.getCardDesc().equals("")){
					result = result + tempcim.getCardDesc() + "，";
				}
				
			}
		}
		if(result.endsWith("，"))
			result = result.substring(0, result.length()-1);

		if(CBSystemConstants.cardbuildtype.equals("0"))
			results.setCzrw(result);
		else if(CBSystemConstants.roleCode.equals("0"))
			results.setCzrw("");
		else
			results.setCzrw(result);
		

		List<CardItemModel>  itemModels=new ArrayList<CardItemModel>();
		for (int i = 0; i < descLists.size(); i++) {
			String desc=descLists.get(i);
			tempItem= wcb.execute(curDevlist,desc);
			for (int j = 0; j < tempItem.size(); j++) {
				itemModels.add(tempItem.get(j));
			}
		}
		
		List<String> notNeedDtd = new ArrayList<String>();
		
		if(itemModels.size() == 0) {
			itemModels = getCardItemByDtdAll();
		}
		
		for (int i = 0; i < itemModels.size(); i++) {
			//itemModels.get(i).setCardItem(String.valueOf(i+1));
			itemModels.get(i).setCardNum(String.valueOf(i+1));
		}
		
		results.setCardItems(itemModels);
		return results;
	}
	
	/**
	 * 术语合并
	 * @param rbmlist
	 * @return
	 */
	public CardModel executeItem(RuleBaseMode rbm) {
		CardModel results=new CardModel(); //返回结果
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		CustomCodexDao ccdd = new CustomCodexDao();
		PowerDevice curDev=new PowerDevice();
		//单个设备
		curDev=rbm.getPd();
		//设备集合
		List<PowerDevice> curDevlist = new ArrayList<PowerDevice>();
		
		PowerDevice curDevlinshi = rbm.getPd();
		curDevlist.add(curDevlinshi);
		String srcStatus=rbm.getBeginStatus();
		String endStatus=rbm.getEndState();
		
		String preBuildType = CBSystemConstants.cardbuildtype;
	
		CBSystemConstants.cardbuildtype = "1";
		String stateCode = dsmm.getStateCodeByStatus(curDevlinshi.getDeviceType(), endStatus);
		
		String czrw=dsmm.getCZRW(curDev.getDeviceType(), srcStatus, stateCode, CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);

		List<String> descLists =dsmm.getStateMents(curDev.getDeviceType(), srcStatus, stateCode,  CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);
		
		
		
		WordCardBuild wcb=new WordCardBuild();
		List<CardItemModel> tempItem=null;
		CardItemModel tempcim=null;
		//操作任务替换
		String result = "";
		//czrw = czrw.replace(",", "，");
		String[] rws = czrw.split("，");
		for (int i = 0; i < rws.length; i++) {
			tempItem= wcb.execute(curDevlist,rws[i]);
			if(tempItem.size()>0){
				tempcim=tempItem.get(0);
				if(!tempcim.getCardDesc().equals("")){
					result = result + tempcim.getCardDesc() + "，";
				}
				
			}
		}
		if(result.endsWith("，"))
			result = result.substring(0, result.length()-1);

		if(CBSystemConstants.roleCode.equals("0"))
			results.setCzrw("");
		else
			results.setCzrw(result);
		

		List<CardItemModel>  itemModels=new ArrayList<CardItemModel>();
		for (int i = 0; i < descLists.size(); i++) {
			String desc=descLists.get(i);
			tempItem= wcb.execute(curDevlist,desc);
			PowerDevice st = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			for (int j = 0; j < tempItem.size(); j++) {
				itemModels.add(tempItem.get(j));
			}
		}
		
		for (int i = 0; i < itemModels.size(); i++) {
			//itemModels.get(i).setCardItem(String.valueOf(i+1));
			itemModels.get(i).setCardNum(String.valueOf(i+1));
		}
		
		results.setCardItems(itemModels);
		
		
		CBSystemConstants.cardbuildtype = preBuildType;
		return results;
	}
	
	public List<CardItemModel> getCartItemList(RuleBaseMode Srcrbm){
		
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		PowerDevice curDev=Srcrbm.getPd();
		String srcStatus=Srcrbm.getBeginStatus();
		String stateCode=Srcrbm.getStateCode();

		WordCardBuild wcb=new WordCardBuild();
		List<CardItemModel> tempItem=null;
		List<String> descLists=dsmm.getStateMents(curDev.getDeviceType(), srcStatus, stateCode,  CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);

		//操作指令替换
		List<CardItemModel>  itemModels=new ArrayList<CardItemModel>();
		if(descLists.size() != 0) {
			for (int i = 0; i < descLists.size(); i++) {
				String desc=descLists.get(i);
				tempItem= wcb.execute(curDev,desc);
				for (int j = 0; j < tempItem.size(); j++) {
					itemModels.add(tempItem.get(j));
				}
			}
		}

		return itemModels;
	}

	
	
	
	

	
	
}
