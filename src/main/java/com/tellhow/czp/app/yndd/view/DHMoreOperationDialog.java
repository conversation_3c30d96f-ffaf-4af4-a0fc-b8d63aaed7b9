package com.tellhow.czp.app.yndd.view;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.Frame;
import java.awt.GridLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import javax.swing.BorderFactory;
import javax.swing.BoxLayout;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.SwingUtilities;
import javax.swing.border.Border;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDH;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;


public class DHMoreOperationDialog extends JDialog {
	public static JComboBox comboBox1 = null;
	public static JComboBox comboBox2 = null;
	public static JComboBox comboBox3 = null;
	public static JComboBox comboBox4 = null;
	public static JComboBox comboBox5 = null;
	public static JComboBox comboBox6 = null;
	public static JTextField text1 = null;
	public static JTextField text2 = null;
	public static JTextField text3 = null;
	
	public String kind = "";
	public boolean isCancel = true;
	
    public DHMoreOperationDialog(Frame owner, PowerDevice pd,String kind) {
        super(owner, kind, true);
        this.isCancel = true;
        this.kind = kind;
        
        initializeUI(pd,kind);
        setLocationRelativeTo(null); // 窗口居中显示  
    }

    private void initializeUI(PowerDevice pd, String kind) {
    	if(kind.equals("主变调档")){
    		JPanel comboBoxPanel2 = new JPanel(new GridLayout(2, 4, 10, 10));
    		String deviceName = CZPService.getService().getDevName(pd);
    		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
    		
    		text1 = new JTextField("将"+stationName+deviceName+"高压侧档位由");
            comboBoxPanel2.add(text1); 
           
            comboBox1 = new JComboBox(new String[]{"","1档","2档","3档","4档","5档","6档","7档","8档"}); 
            comboBox1.setPreferredSize(new Dimension(10,10));
            comboBoxPanel2.add(comboBox1);
            
            text2 = new JTextField("调至");
            comboBoxPanel2.add(text2); 
            
            comboBox2 = new JComboBox(new String[]{"","1档","2档","3档","4档","5档","6档","7档","8档"}); 
            comboBox2.setPreferredSize(new Dimension(10,10));
            comboBoxPanel2.add(comboBox2);
            
            text3 = new JTextField("运行");
            comboBoxPanel2.add(text3); 
            
            Border emptyBorder2 = BorderFactory.createEmptyBorder(10, 10, 10, 10);
            // 创建其他边框  
            Border etchedBorder2 = BorderFactory.createEtchedBorder();  
            Border titledBorder2 = BorderFactory.createTitledBorder("主变调档");  
            Border innerBorder2 = BorderFactory.createCompoundBorder(emptyBorder2, titledBorder2);  
            Border outerBorder2 = BorderFactory.createCompoundBorder(etchedBorder2, innerBorder2); 
            comboBoxPanel2.setBorder(outerBorder2);
            JPanel comboBoxContainer = new JPanel();  
            comboBoxContainer.setLayout(new BoxLayout(comboBoxContainer, BoxLayout.Y_AXIS));  
            comboBoxContainer.add(comboBoxPanel2);  
            add(comboBoxContainer, BorderLayout.NORTH);

            // 创建按钮面板  
            JPanel buttonPanel = new JPanel(); // 默认FlowLayout  
            JButton saveButton = new JButton("保存");  
            JButton cancelButton = new JButton("取消");  
            buttonPanel.add(saveButton);  
            buttonPanel.add(cancelButton);  
      
            // 将按钮面板添加到JFrame的SOUTH位置  
            add(buttonPanel, BorderLayout.SOUTH);
            setSize(800, 250);  
            
            saveButton.addActionListener(new java.awt.event.ActionListener() {
    		    public void actionPerformed(java.awt.event.ActionEvent evt) {
    		        savejButtonActionPerformed(evt);
    		    }
    		});
            
            cancelButton.addActionListener(new java.awt.event.ActionListener() {
    		    public void actionPerformed(java.awt.event.ActionEvent evt) {
    		    	canceljButtonActionPerformed(evt);
    		    }
    		});
    	}
    }
    
    private void savejButtonActionPerformed(ActionEvent evt){
    	isCancel = false;
    	
    	if(kind.equals("主变调档")){
    		CommonFunctionDH.orderContent = text1.getText()+comboBox1.getSelectedItem()+text2.getText()+comboBox2.getSelectedItem()+text3.getText();
    	}
    	
    	this.setVisible(false);
	}

	private void canceljButtonActionPerformed(ActionEvent evt){
		this.setVisible(false);
	}
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                DHMoreOperationDialog dialog = new DHMoreOperationDialog(null, new PowerDevice(),"定值调整");
                dialog.setVisible(true);
            }
        });
    }
}


