package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMXTDSMJXTCBH implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("母线停电双母接线退出保护".equals(tempStr)){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());

			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
			
			StringBuffer reBuffer = new StringBuffer();

			if(mlkgList.size()>0){
				
				RuleExeUtil.swapDeviceListNum(zbList);
				
				for(PowerDevice zb : zbList){
					reBuffer.append(CZPService.getService().getDevNum(zb)+"、");
				}
				
				reBuffer.delete(reBuffer.length()-1, reBuffer.length());
				
				replaceStr += "退出"+(int)station.getPowerVoltGrade()+"kV"+reBuffer+"主变第Ⅰ、Ⅱ套"+(int)curDev.getPowerVoltGrade()+"kV侧后备保护动作跳"+CZPService.getService().getDevName(mlkgList.get(0))+"/r/n";
			}
		}
		System.out.println(replaceStr);
		return replaceStr;
	}

}
