package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrXLHHDDDKKG implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev,String desc) {
		if("线路合环调电断开开关替换类".equals(tempStr)){
			String replaceStr = "";
			List<PowerDevice> list = RuleExeUtil.getLineAllSideList(curDev);
			List<PowerDevice> lineList = new ArrayList<PowerDevice>();
			List<PowerDevice> stationList = new ArrayList<PowerDevice>();
			Map<Double,Integer> empMap = new HashMap<Double,Integer>();
			for(PowerDevice dev : list){
				PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
				PowerDevice station = CBSystemConstants.getPowerStation(sw.getPowerStationID());
				stationList.add(station);
			}
			for(PowerDevice dev : stationList) {
				if(empMap.containsKey(dev.getPowerVoltGrade())) {
					empMap.put(dev.getPowerVoltGrade(), empMap.get(dev.getPowerVoltGrade())+1);
				} else {
					empMap.put(dev.getPowerVoltGrade(), 1);
				}
			}
			double volt = 0;
			for(Double solt : empMap.keySet()) {
				if(empMap.get(solt) > 1) {
					volt = solt;
					break;
				}
			}
			for(PowerDevice dev : list){
				PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
				PowerDevice station = CBSystemConstants.getPowerStation(sw.getPowerStationID());
				if(station.getPowerVoltGrade() == volt) {
					lineList.add(dev);
				}
			}
			PowerDevice first = null;
			PowerDevice second = null;
			String statusFirst = "";
			String statusSecond = "";
			if(lineList.size() == 2) {
				for(PowerDevice dev : lineList) {
					PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
					String sql = "SELECT EQUIPSTATUS FROM "+CBSystemConstants.opcardUser+"T_A_DEVICEPOWERCUTSTATE WHERE EQUIPID = '" + sw.getPowerDeviceID() + "' and PARENTID='" + dev.getPowerDeviceID() + "'";
					List<Map<String,Object>> statusList = DBManager.query(sql);
					if(statusList != null && statusList.size()>0) {
						String status = String.valueOf(statusList.get(0).get("EQUIPSTATUS"));
						if("1".equals(status) || "2".equals(status)) {
							first = dev;
							statusFirst = status;
						}else {
							second = dev;
							statusSecond  = status;
						}
					}
					
				}
			}
			
			if(first != null && second != null) {
				PowerDevice swFirst = RuleExeUtil.getDeviceSwitch(first);
				
				if(statusFirst.equals("1")) {
					replaceStr  += "昆明地调@遥控断开"+swFirst.getPowerStationName()+CZPService.getService().getDevName(swFirst)+"/r/n";
				}
				return replaceStr;
			}
	    }
		return null;
	}

}
