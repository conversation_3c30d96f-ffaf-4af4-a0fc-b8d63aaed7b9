package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Component;
import java.awt.Font;
import java.util.List;

import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.JTableHeader;

import com.tellhow.graphicframework.utils.StringUtils;

public class SetJTableProtery {
	public void getTableHeader(JTable table) {
		JTableHeader tableHeader = table.getTableHeader();
		tableHeader.setReorderingAllowed(false); // 设置表格列不可重排
		DefaultTableCellRenderer hr = (DefaultTableCellRenderer) tableHeader.getDefaultRenderer(); // 获得表格头的单元格对象
		hr.setHorizontalAlignment(DefaultTableCellRenderer.CENTER); // 列名居中
	}

	public void getDefaultRenderer(Class<?> columnClass, JTable table) {
		DefaultTableCellRenderer cr = (DefaultTableCellRenderer) table.getDefaultRenderer(columnClass);
		cr.setHorizontalAlignment(DefaultTableCellRenderer.CENTER); // 单元格内容居中
	}
	/**
	 * 单元格内容居左
	 * @param columnClass
	 * @param table
	 */
	public void getDefaultLeft(Class<?> columnClass, JTable table) {
		DefaultTableCellRenderer cr = (DefaultTableCellRenderer) table.getDefaultRenderer(columnClass);
		cr.setHorizontalAlignment(DefaultTableCellRenderer.LEFT); // 单元格内容居左
	}
	
	/**
	 * 功能：JTable行背景换色
	 */
	@SuppressWarnings("serial")
	public void makeFace(JTable table) {
		try {
			DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
				public Component getTableCellRendererComponent(JTable table,
						Object value, boolean isSelected, boolean hasFocus,
						int row, int column) {
					if(column == 0 || column == 1||table.getColumnName(column).equals("类型")){
					    setHorizontalAlignment(SwingConstants.CENTER);
					}else{
						setHorizontalAlignment(SwingConstants.LEFT);
					}
					//自动换行
					if(table.getColumnName(column).equals("校核结果")) {
						JTextArea textArea = new JTextArea();
						if(isSelected){
							textArea.setBackground(new Color(206, 231, 255));
						}
				
						textArea.setLineWrap(true); 
						textArea.setWrapStyleWord(true); 

					    int maxPreferredHeight = 25; 
					    String xString = (String) table.getValueAt(row,column);
					    textArea.setText(xString); 
					    textArea.setSize(table.getColumnModel().getColumn(column).getWidth(),10);
				        maxPreferredHeight = Math.max(maxPreferredHeight, textArea.getPreferredSize().height); 


				        if (table.getRowHeight(row) != maxPreferredHeight)  // 少了这行则处理器瞎忙 
				            table.setRowHeight(row, maxPreferredHeight);

				        textArea.setText(value == null ? "" : xString); 
						return textArea;
					}
					
					return super.getTableCellRendererComponent(table, value,
							isSelected, hasFocus, row, column);
				}
			};
			for (int i = 0; i < table.getColumnCount(); i++) {
				//tcr.setHorizontalAlignment(javax.swing.SwingConstants.CENTER);
				table.getColumn(table.getColumnName(i)).setCellRenderer(tcr);
			}
		   
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	/**
	 * 功能：JTable行背景换色
	 * @param table
	 * @param colnumList
	 * <AUTHOR>
	 */
	public void makeFaceCenter(JTable table,final List<Integer> colnumList) {
		try {
			DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
				public Component getTableCellRendererComponent(JTable table,
						Object value, boolean isSelected, boolean hasFocus,
						int row, int column) {
					if (row % 2 == 0)
						setBackground(Color.white); // 设置奇数行底色
					else if (row % 2 == 1)
						setBackground(new Color(206, 231, 255)); // 设置偶数行底色
					if(colnumList.contains(column)){
					    setHorizontalAlignment(SwingConstants.CENTER);
					}else{
						setHorizontalAlignment(SwingConstants.LEFT);
					}
					return super.getTableCellRendererComponent(table, value,
							isSelected, hasFocus, row, column);
				}
			};
			for (int i = 0; i < table.getColumnCount(); i++) {
				table.getColumn(table.getColumnName(i)).setCellRenderer(tcr);
			}
		   
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	@SuppressWarnings("serial")
	public void setLineWrap(JTable table) {
		try {
			DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
				public Component getTableCellRendererComponent(JTable table,
						Object value, boolean isSelected, boolean hasFocus,
						int row, int column) {
					if(column == 0 || column == 1||table.getColumnName(column).equals("类型")){
					    setHorizontalAlignment(SwingConstants.CENTER);
					}else{
						setHorizontalAlignment(SwingConstants.LEFT);
					}
					//自动换行
					if(table.getColumnName(column).equals("校核结果")) {
						JTextArea textArea = new JTextArea();
						if(isSelected){
							textArea.setBackground(new Color(206, 231, 255));
						}
				
						textArea.setLineWrap(true); 
						textArea.setWrapStyleWord(true); 

					    int maxPreferredHeight = 25; 
					    String xString = (String) table.getValueAt(row,column);
					    textArea.setText(xString); 
					    textArea.setSize(table.getColumnModel().getColumn(column).getWidth(),10);
				        maxPreferredHeight = Math.max(maxPreferredHeight, textArea.getPreferredSize().height); 


				        if (table.getRowHeight(row) != maxPreferredHeight)  // 少了这行则处理器瞎忙 
				            table.setRowHeight(row, maxPreferredHeight);

				        textArea.setText(value == null ? "" : xString); 
						return textArea;
					}
					
					
					
					return super.getTableCellRendererComponent(table, value,
							isSelected, hasFocus, row, column);
				}
			};
			for (int i = 0; i < table.getColumnCount(); i++) {
				//tcr.setHorizontalAlignment(javax.swing.SwingConstants.CENTER);
				table.getColumn(table.getColumnName(i)).setCellRenderer(tcr);
			}
		   
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
}
