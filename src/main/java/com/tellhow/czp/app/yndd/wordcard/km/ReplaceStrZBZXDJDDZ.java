package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 */
public class ReplaceStrZBZXDJDDZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("主变中性点接地刀闸".equals(tempStr)){
			if(curDev.getDeviceType().equals(SystemConstants.PowerTransformer)){
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				if(gdList.size()>0){
					String devNum = "";
					for(PowerDevice zxddd : gdList){
						devNum += (int)zxddd.getPowerVoltGrade()+"kV侧中性点"+CZPService.getService().getDevNum(zxddd)+"接地开关、";
					}
					
					if(devNum.endsWith("、")){
						devNum = devNum.substring(0, devNum.length()-1);
					}
					
					replaceStr += devNum;
				}
			}else if(curDev.getDeviceType().equals(SystemConstants.MotherLine)){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "",true, true, true);
				
				if(zbList.size()>1){
					for(Iterator<PowerDevice> itor = zbList.iterator();itor.hasNext();){
						PowerDevice zb = itor.next();
						List<PowerDevice> gyckgList = RuleExeUtil.getTransformerSwitchHigh(zb);
						
						if(gyckgList.size()>0){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(gyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);

							if(mxList.size()>1){
								itor.remove();
							}
						}
					}
				}
				
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(zbList.get(0), SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", false, true, true, true);
				if(gdList.size()>0){
					String devNum = "";
					for(PowerDevice zxddd : gdList){
						devNum += CZPService.getService().getDevNum(zxddd)+"、";
					}
					
					if(devNum.endsWith("、")){
						devNum = devNum.substring(0, devNum.length()-1);
					}
					
					replaceStr +=  CZPService.getService().getDevName(zbList)+"中性点"+devNum+"接地开关";
				}
			}else if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.PowerTransformer, "",true, true, true);
				
				if(zbList.size()>0){
					List<PowerDevice> gdList = RuleExeUtil.getDeviceList(zbList.get(0), SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", false, true, true, true);
					if(gdList.size()>0){
						String devNum = "";
						for(PowerDevice zxddd : gdList){
							devNum += CZPService.getService().getDevNum(zxddd)+"、";
						}
						
						if(devNum.endsWith("、")){
							devNum = devNum.substring(0, devNum.length()-1);
						}
						
						replaceStr +=  CZPService.getService().getDevName(zbList)+"中性点"+devNum+"接地开关";
					}
				}else{
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.MotherLine, "",true, true, true);
					
					if(mxList.size()>0){
						zbList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.PowerTransformer, "",true, true, true);
						
						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(zbList.get(0), SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", false, true, true, true);
						if(gdList.size()>0){
							String devNum = "";
							for(PowerDevice zxddd : gdList){
								devNum += CZPService.getService().getDevNum(zxddd)+"、";
							}
							
							if(devNum.endsWith("、")){
								devNum = devNum.substring(0, devNum.length()-1);
							}
							
							replaceStr +=  CZPService.getService().getDevName(zbList)+"中性点"+devNum+"接地开关";
						}
					}
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		return replaceStr;
	}

}
