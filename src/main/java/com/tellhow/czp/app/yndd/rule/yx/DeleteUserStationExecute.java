package com.tellhow.czp.app.yndd.rule.yx;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.yndd.tool.CommonFunction;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

public class DeleteUserStationExecute implements RulebaseInf {
	
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		
		List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
		
		List<Map<String, String>> stationLineList = CommonFunction.getStationLineList(pd);
		List<String> userstations = new ArrayList<String>();
		
		for(Map<String, String> map : stationLineList){
			String stationName = String.valueOf(map.get("UNIT")).trim();
			String lowerUnit = String.valueOf(map.get("LOWERUNIT")).trim();

			userstations.add(stationName);
			userstations.add(lowerUnit);
		}
		
		for(Iterator<PowerDevice> itor = lineLoad.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			for(String userstation : userstations){
				if(dev != null){
					String stationName = dev.getPowerStationName();
					userstation = userstation.replace("升压", "");
					
					if(stationName.equals("110kV禄丰电站")){
						stationName = "110kV禄丰水电站";
					}else if(stationName.equals("110kV糯租电站")){
						stationName = "110kV糯租水电站";
					}
					
					if(stationName.contains(userstation)
							||userstation.contains(dev.getPowerStationName())){
						itor.remove();
					}
				}
			}
		}
	    
		for(Iterator<PowerDevice> itor = lineLoad.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			if(dev.getPowerStationName().contains("tase")){
				itor.remove();
			}
		}
		
		return true;
	}

}
