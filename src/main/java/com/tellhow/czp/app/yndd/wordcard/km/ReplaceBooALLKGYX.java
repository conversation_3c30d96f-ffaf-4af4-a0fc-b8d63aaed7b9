package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooALLKGYX implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("所有开关运行".equals(tempStr)){

			List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
			
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev2 = it2.next();
				
				if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						hignVoltMlkgList.add(dev2);
					}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						hignVoltXlkgList.add(dev2);
					}
				}
			}
			
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			
			if(hignVoltMlkgList.size() == 2 && hignVoltXlkgList.size() == 2  && mxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				
				hignVoltXlkgList.addAll(hignVoltMlkgList);
				
				boolean flag = true;
				
				for(PowerDevice hignVoltXlkg : hignVoltXlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(hignVoltXlkg).equals("0")
							||(RuleExeUtil.getDeviceBeginStatus(hignVoltXlkg).equals("")&&hignVoltXlkg.getDeviceStatus().equals("0"))){
						
					}else{
						flag = false;
					}
				}
				 return flag;
				
			}
		
		}
        return false;
	}
}
