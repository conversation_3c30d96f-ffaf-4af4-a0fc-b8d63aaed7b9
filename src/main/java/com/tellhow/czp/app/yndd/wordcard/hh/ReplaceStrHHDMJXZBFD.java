package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrHHDMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("红河单母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev);

			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> otherzbdyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbgyckgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();

			zbkgList.addAll(zbgyckgList);
			zbkgList.addAll(zbzyckgList);
			zbkgList.addAll(zbdyckgList);

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
			
			for(PowerDevice dev : zbList){
				if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
					otherzbList.add(dev);
				}
			}
			
			for(PowerDevice dev : otherzbList){
				otherzbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
				otherzbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
				otherzbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
			}
			
			List<PowerDevice> gycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> zycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbzyckgList){
				gycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			for(PowerDevice dev : zbzyckgList){
				zycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice dycmx : dycmxList){
					dycmlkgList  = RuleExeUtil.getDeviceList(dycmx,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true);
				}
			}
			
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbzyckgList){
				zycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice zycmx : zycmxList){
					zycmlkgList  = RuleExeUtil.getDeviceList(zycmx,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				gycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += stationName+"@投入"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}

			boolean isKnifeXC = false;
			boolean isKnifeFB = false;

			for(PowerDevice dev : zbgyckgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				if(dzList.size() == 1){
					isKnifeFB = true;
					break;
				}else{
					for(PowerDevice dz : dzList){
						if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							isKnifeXC = true;
							break;
						}
					}
				}
			}
			
			if(isKnifeFB){
				for(PowerDevice dev : zbgyckgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionHH.getKnifeOnContent(dzList, stationName);
				}
			}else if(isKnifeXC){
				for(PowerDevice dev : zbgyckgList){
					replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
				}
			}else{
				for(PowerDevice dev : zbgyckgList){
					replaceStr += "红河地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
				}
			}
			
			isKnifeXC = false;
			isKnifeFB = false;

			for(PowerDevice dev : zbdyckgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				if(dzList.size() == 1){
					isKnifeFB = true;
					break;
				}else{
					for(PowerDevice dz : dzList){
						if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							isKnifeXC = true;
							break;
						}
					}
				}
			}
			
			if(isKnifeFB){
				for(PowerDevice dev : zbdyckgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionHH.getKnifeOnContent(dzList, stationName);
				}
			}else if(isKnifeXC){
				for(PowerDevice dev : zbdyckgList){
					replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
				}
			}else{
				for(PowerDevice dev : zbdyckgList){
					replaceStr += "红河地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用程序操作/r/n";
				}
			}
		
			
			if(zbList.size() == 2){
				replaceStr +=  stationName+"@确认"+CZPService.getService().getDevName(zbList)+"具备并列条件/r/n";
			}
			
			for(PowerDevice dev : zbkgList){
				String devName = CZPService.getService().getDevName(dev);
				replaceStr += "红河地调@遥控合上"+stationName+devName+"/r/n";
			}
			
			List<PowerDevice> jhkgList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					jhkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : otherzbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					jhkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					jhkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : otherzbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					jhkgList.add(dev);
				}
			}
			
			for(PowerDevice dev : jhkgList){
				String devName = CZPService.getService().getDevName(dev);
				replaceStr += "红河地调@遥控断开"+stationName+devName+"/r/n";
			}
			
			for(PowerDevice dev : otherzbList){
				String devName = CZPService.getService().getDevName(dev);

				if(dev.getPowerVoltGrade() > 35){
					replaceStr += stationName+"@确认站内仅投"+devName+"中性点/r/n";
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}