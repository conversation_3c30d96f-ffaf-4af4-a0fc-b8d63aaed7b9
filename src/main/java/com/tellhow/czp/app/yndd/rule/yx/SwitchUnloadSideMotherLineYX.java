package com.tellhow.czp.app.yndd.rule.yx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-9-19 上午11:13:13 
 */
public class SwitchUnloadSideMotherLineYX implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		if(!pd.getDeviceType().equals(SystemConstants.Switch)){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]不是开关！");
        	return false;
        }
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)||pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
        	ShowMessage.view("该开关为旁路开关，请选择要倒回的开关操作！");
        	return false;
        }
		if(!"0".equals(pd.getDeviceStatus())){
			ShowMessage.view("开关["+pd.getPowerDeviceName()+"]不处于运行状态！");
        	return false;
		}
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)||pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
        	
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
        inPara.put("tagDevType", SystemConstants.MotherLine);
        cs.execute(inPara, outPara);
		inPara.clear();
		PowerDevice sideML=null;
		PowerDevice sideKnife=null;
		List tempMLs = (ArrayList) outPara.get("linkedDeviceList");
		for (int i = 0; i < tempMLs.size(); i++) {
			PowerDevice tempML = (PowerDevice) tempMLs.get(i);
            if(tempML.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
            	sideML = tempML;
            	ArrayList<PowerDevice> tempDevs= ((HashMap<PowerDevice,ArrayList<PowerDevice>>)outPara.get("pathList")).get(sideML);
            	for (int j = 0; j < tempDevs.size(); j++) {
            		if(tempDevs.get(j).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
            			sideKnife = tempDevs.get(j);
            			break;
            		}
            	}
            	break;
            }
		}
		if(sideML == null){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]没有连接到旁路母线！");
        	return false;
        }
		if(!sideML.getDeviceStatus().equals("0")){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]连接的旁路母线不在运行状态！");
        	return false;
        }
		if(sideKnife == null){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]没有连接到旁路刀闸！");
        	return false;
        }
		if(sideKnife.getDeviceStatus().equals("1")){
        	ShowMessage.view("["+sideKnife.getPowerDeviceName()+"]连接的旁路刀闸不在合上状态！");
        	return false;
        }
		inPara.put("oprSrcDevice", sideML);
        inPara.put("tagDevType", SystemConstants.Switch);
        cs.execute(inPara, outPara);
		inPara.clear();
		PowerDevice sideSwitch=null;
		List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
		for (int i = 0; i < tempSwitchs.size(); i++) {
			PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(i);
            if(tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) ||
            		tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
            	sideSwitch = tempSwitch;
            	break;
            }
		}
		if(sideSwitch == null){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]没有连接到旁路开关！");
        	return false;
        }
		if(!sideSwitch.getDeviceStatus().equals("0")){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]连接的旁路开关不在运行状态！");
        	return false;
        }
		
		boolean flag = CommonFunction.isSwitchDiffMotherLine(pd);
		
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			if(flag){
				PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
				
				List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev2 = it2.next();
					
					if(dev2.getPowerVoltGrade() == station.getPowerVoltGrade()){
						if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							hignVoltMlkgList.add(dev2);
						}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							hignVoltXlkgList.add(dev2);
						}
					}
				}
				
				List<PowerDevice> xlkg = new ArrayList<PowerDevice>();
				for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = (PowerDevice) it2.next();
					
					if(hignVoltMlkgList.size() == 1 && hignVoltXlkgList.size() > 2){
						if(station.getPowerVoltGrade() == dev.getPowerVoltGrade() && (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) )){
							if(dev.getDeviceStatus().equals("1")){
								RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
							}
						}
						
						if(station.getPowerVoltGrade() == dev.getPowerVoltGrade()
								&& dev.getDeviceStatus().equals("0") && !RuleExeUtil.isDeviceChanged(dev)){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) ){
								xlkg.add(dev);
							}
						}
					}
				}
				
				String showMessage="请选择解环的断路器";
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, xlkg, showMessage);
				List<PowerDevice> chooseEquips = ecc.getChooseEquip();
				if(ecc.isCancel()){
					return false;
				}
				
				for(PowerDevice sw : chooseEquips){
					RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
				}
			}
			
			for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = (PowerDevice) it2.next();
				
				if(pd.getPowerVoltGrade() == dev.getPowerVoltGrade() && dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getDeviceStatus().equals("1")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
				}
			}
		}
		
		if(pd.getPowerVoltGrade()>110){
			PowerDevice source =null;
			List<PowerDevice> load =new ArrayList<PowerDevice>();
			List<PowerDevice> xlList =RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine
					, SystemConstants.PowerTransformer, true, true, true);
			if(xlList.size()>0){
				source=xlList.get(0);
				load=RuleExeUtil.getLineOtherSideList(source);
			}
			if(source!=null&&load.size()>0){
				CBSystemConstants.putLineSource(source.getPowerDeviceID(), source);
				CBSystemConstants.putLineLoad(source.getPowerDeviceID(), load);
			}
	
		}
		
		boolean result = true;
		result = RuleExeUtil.deviceStatusExecute(sideSwitch, sideSwitch.getDeviceStatus(), "1");
		if(!result)
			return false;
		result = RuleExeUtil.deviceStatusExecute(sideKnife, "0", "1");
		if(!result)
			return false;
		
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)&&flag){
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());
			PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
			
			for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = (PowerDevice) it2.next();
				
				if(station.getPowerVoltGrade() == dev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)
							||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
						}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
						}
					}
				}
			}
			
			for (Iterator it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = (PowerDevice) it2.next();
				
				if(pd.getPowerVoltGrade() == dev.getPowerVoltGrade() && dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
			}
		}
	}
	return true;
	}

}
