package com.tellhow.czp.app.yndd.rule.zt;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

public class ZTXLHHDD implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();

		List<PowerDevice> allLineList =  RuleExeUtil.getLineAllSideList(pd);
		
		for(PowerDevice dev : allLineList){
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			
			for(PowerDevice xlkg : xlkgList){
				if(xlkg.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "0");
				}
			}
		}
		
		for(PowerDevice dev : allLineList){
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			
			for(PowerDevice xlkg : xlkgList){
				if(xlkg.getDeviceStatus().equals("0")&&!RuleExeUtil.isDeviceChanged(xlkg)){
					RuleExeUtil.deviceStatusExecute(xlkg, xlkg.getDeviceStatus(), "1");
				}
			}
		}

		return true;
	}
}
