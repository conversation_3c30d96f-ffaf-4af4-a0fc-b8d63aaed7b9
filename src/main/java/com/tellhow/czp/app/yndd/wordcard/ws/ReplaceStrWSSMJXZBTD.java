package com.tellhow.czp.app.yndd.wordcard.ws;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionWS;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrWSSMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("文山双母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();

			kgList.addAll(zbdyckgList);
			kgList.addAll(zbzyckgList);
			kgList.addAll(zbgyckgList);
			
			List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> zycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzxdjddzList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			for(PowerDevice dev : dycmxList){
				dycmlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
			}
			
			for(PowerDevice dev : zbzyckgList){
				zycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			}
			
			for (Iterator<PowerDevice> it = zycmxList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					it.remove();
				}
			}
			
			for(PowerDevice dev : zycmxList){
				zycmlkgList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			RuleExeUtil.swapLowDeviceList(zxdjddzList);
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(jddzList);
				
				otherzxdjddzList.addAll(jddzList);
			}
			
			replaceStr += stationName+"@确认"+deviceName+"具备停电条件/r/n";
			
			boolean isWholeStationTD = true;
			
			for(PowerDevice dev : otherzbList){
				if(dev.getDeviceStatus().equals("0")){
					isWholeStationTD = false;
					break;
				}
			}
			
			if(isWholeStationTD){
				List<PowerDevice> mxList = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
						if(dev.getPowerVoltGrade() < curDev.getPowerVoltGrade()){
							if(dev.getDeviceStatus().equals("0")){
								mxList.add(dev);
							}
						}
					}
				}

				mxList = RuleExeUtil.sortByVoltLowAndNum(mxList);
				
				for(PowerDevice dev : mxList){
					replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"具备停电条件/r/n";
				}
			}
			
			List<PowerDevice> zbzdyckgList = new ArrayList<PowerDevice>();
			
			if(dycmlkgList.size() == 0){
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						zbzdyckgList.add(dev);
					}
				}
			}
			
			if(zycmlkgList.size() == 0){
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						zbzdyckgList.add(dev);
					}
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += stationName+"@退出"+(int)dev.getPowerVoltGrade()+"kV备自投装置/r/n";
				}
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionWS.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			if(otherzxdjddzList.size() > 0){
				replaceStr += CommonFunctionWS.getZxdJddzOnCheckContent(otherzxdjddzList, stationName, station);
			}
		
			if(dycmlkgList.size() == 0&&station.getPowerVoltGrade() <= 110){
				for(PowerDevice dev : dycmxList){
					if(dev.getPowerVoltGrade() == 10){
						replaceStr += "文山配调@确认"+stationName+CZPService.getService().getDevName(dev)+"可以停电/r/n";
					}
				}
			}
			
			boolean isSwitchControl = true;
			
			/*
			 * 判断开关是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchControl(dev)){
					isSwitchControl = false;
				}
			}
			
			boolean isSwitchSeparateControl = true;
			
			/*
			 * 判断刀闸是否可控
			 */
			for(PowerDevice dev : kgList){
				if(!CommonFunctionWS.ifSwitchSeparateControl(dev)){
					isSwitchSeparateControl = false;
				}
			}
			
			if(isSwitchControl && isSwitchSeparateControl){
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("1")){
					replaceStr += "文山地调@执行"+stationName+deviceName+"由运行转热备用程序操作/r/n";
				}else if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
						replaceStr += "文山地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
					}else{
						replaceStr += "文山地调@执行"+stationName+deviceName+"由运行转冷备用程序操作/r/n";
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					boolean isxcknife = false;
					
					for(PowerDevice dz : dzList){
						if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							isxcknife = true;
							break;
						}
					}
					
					if(isxcknife){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已处冷备用/r/n";
						}
					}else{
						replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					
					boolean isxcknife = false;
					
					for(PowerDevice dz : dzList){
						if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							isxcknife = true;
							break;
						}
					}
					
					if(isxcknife){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dev)+"已处冷备用/r/n";
						}
					}else{
						replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
				}
			}else{
				for(PowerDevice dev : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbdyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += CommonFunctionWS.getHhContent(dev, "文山地调", stationName);
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "文山地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				if(curDev.getDeviceStatus().equals("2")){
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
								replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
							}else{
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
							}
							
							List<PowerDevice> dzList = CommonFunctionWS.getTransformerKnife(curDev, dev);
							
							for (Iterator<PowerDevice> it = dzList.iterator(); it.hasNext();) {
								PowerDevice dz = it.next();
								
								if(dz.getPowerDeviceName().endsWith("1")){
									it.remove();
								}
							}
							
							replaceStr += CommonFunctionWS.getKnifeOffContent(dzList,stationName);
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
								replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
							}else{
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
							}
							
							List<PowerDevice> dzList = CommonFunctionWS.getTransformerKnife(curDev, dev);
							replaceStr += CommonFunctionWS.getKnifeOffContent(dzList,stationName);
						}
					}
					
					for(PowerDevice dev : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							if(CommonFunctionWS.ifSwitchSeparateControl(dev)){
								replaceStr += "文山地调@执行"+stationName+CZPService.getService().getDevName(dev)+"由热备用转冷备用程序操作/r/n";
								
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								replaceStr += CommonFunctionWS.getKnifeOffCheckContent(dzList, stationName);
							}else{
								replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用/r/n";
							}
						}
					}
				}
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionWS.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
			}
		}
		
		return replaceStr;
	}

}
