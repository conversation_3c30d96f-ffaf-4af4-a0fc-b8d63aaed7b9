package com.tellhow.czp.app.yndd;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.service.CheckCzpImpl;
import com.tellhow.czp.service.CheckStatusImpl;
import com.tellhow.graphicframework.startup.StartupManager;

public class GetCheckImplTestPE {
    public static void main(String[] params) {
	    CheckCzpImpl check = new CheckCzpImpl();
	    CheckStatusImpl checkback = new CheckStatusImpl();
	
	    String param = "";
	
		StartupManager.startup();
		CZPService.getService().setArg(param);
		
		param = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Datas><CZRW>操作票-校核验证</CZRW>"
				+ "<ITEM><changzhan>普洱地调</changzhan><caozuozhiling>执行35kV北郊变10kV民主线056断路器由运行转热备用程序操作</caozuozhiling><cbid>8</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "<ITEM><changzhan>普洱地调</changzhan><caozuozhiling>执行35kV茶博园变35kV#1主变由热备用转冷备用程序操作</caozuozhiling><cbid>7</cbid><refresh>true</refresh><zhixing>false</zhixing><roleCode>0</roleCode><ischeck>1</ischeck><isrealtime>1</isrealtime></ITEM>"
				+ "</Datas>";
		
		check.execute(param);
    }
}
