package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZNHHDDRW implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("站内合环调电任务".equals(tempStr)) {
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			List<PowerDevice> lowVoltmlkgList = new ArrayList<PowerDevice>();

			String stName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(stationDev.getPowerStationID()));
			if(stationDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev2 = it2.next();
					if(dev2.getPowerVoltGrade()==stationDev.getPowerVoltGrade()){
						if(dev2.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
							if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&
									(RuleExeUtil.getDeviceBeginStatus(dev2).equals("1")||RuleExeUtil.getDeviceBeginStatus(dev2).equals("0"))){
								lowVoltmlkgList.add(dev2);
							}
						}
					}
				}
				
				List<PowerDevice>  mlList = RuleExeUtil.getDeviceList(stationDev,lowVoltmlkgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", "",false, false, false, false);
				
				if(mlList.size() > 0) {
					RuleExeUtil.swapDeviceList(mlList);
					
					String mxName = CZPService.getService().getDevName(mlList);
					
					String tdmlName = "";
					for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
						DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
						if(dtd.getTransDevice().getDeviceType().equals(SystemConstants.Switch) && dtd.getEndstate().equals("0") && dtd.getTransDevice().getDeviceStatus().equals("0")) {
							tdmlName = CZPService.getService().getDevName(dtd.getTransDevice());
							break;
						}
					}
					replaceStr = stName+mxName+"由"+CZPService.getService().getDevName(curDev)+"供电合环调由"+tdmlName+"供电";
				}
			}else{
				List<PowerDevice> hignVoltMlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> lowVoltzbkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> hignVoltXlkgList = new ArrayList<PowerDevice>();
				List<PowerDevice> hignVoltXlkgList2 = new ArrayList<PowerDevice>();

				List<PowerDevice> curXlkgList = new ArrayList<PowerDevice>();

				if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
					if(!curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);

						if(mlkgList.size()>0){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
							if(mxList.size()>0){
								curXlkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
							}else{
								curXlkgList.add(curDev);
							}
						}
					}else{
						curXlkgList.add(curDev);
					}
				}
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev2 = it2.next();
					if(dev2.getPowerVoltGrade()!=6&&dev2.getPowerVoltGrade()!=10){
						if(dev2.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
							if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								hignVoltMlkgList.add(dev2);
							}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&!curXlkgList.contains(dev2)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev2).equals("0")){
								hignVoltXlkgList.add(dev2);
							}else if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&!curXlkgList.contains(dev2)){
								hignVoltXlkgList2.add(dev2);
							}
						}
					}else {
						if(dev2.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&
								(RuleExeUtil.getDeviceBeginStatus(dev2).equals("1")||RuleExeUtil.getDeviceBeginStatus(dev2).equals("0"))){
							lowVoltzbkgList.add(dev2);
						}
					}
				}
				
				boolean mlkgfw = false;

				for(PowerDevice hignVoltMlkg : hignVoltMlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(hignVoltMlkg).equals("1")){//母联开关在分位
						mlkgfw = true;
					}
				}
				
				
				if(mlkgfw){
					if(hignVoltXlkgList.size()>1){
						String showMessage="请选择供电线路断路器";
						EquipChoose ecc=new EquipChoose(SystemConstants.getMainFrame(), true, hignVoltXlkgList, showMessage,true,1);
						List<PowerDevice> chooseEquips = ecc.getChooseEquip();
						if(ecc.isCancel()){
							replaceStr = null;
						}
						
						replaceStr += stName+"全站由线路变压器组方式供电合环调由"+CZPService.getService().getDevName(chooseEquips)+"供电";
					}else if(hignVoltXlkgList.size()==1){
						for(PowerDevice hignVoltXlkg : hignVoltXlkgList){
							if(!hignVoltXlkg.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
								replaceStr += stName+"全站由线路变压器组方式供电合环调由"+CZPService.getService().getDevName(hignVoltXlkg)+"供电";
								break;
							}
						}
					}
				}else{
					if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if(hignVoltXlkgList.size()>1){
							String showMessage="请选择操作前用来供电的开关";
							EquipChoose ecc=new EquipChoose(SystemConstants.getMainFrame(), true, hignVoltXlkgList, showMessage,true,1);
							List<PowerDevice> chooseEquips = ecc.getChooseEquip();
							if(ecc.isCancel()){
								replaceStr = null;
							}
							
							replaceStr += stName+"全站由"+CZPService.getService().getDevName(chooseEquips)+"供电合环调由线路变压器组方式供电";
						}else if(hignVoltXlkgList.size()==1){
							replaceStr += stName+"全站由"+CZPService.getService().getDevName(hignVoltXlkgList)+"供电合环调由线路变压器组方式供电";
						}else{//线变组
							List<PowerDevice>  mlList = RuleExeUtil.getDeviceList(lowVoltzbkgList.get(0),curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", "",false, false, false, false);
							
							if(mlList.size() > 0) {
								RuleExeUtil.swapDeviceList(mlList);

								String mxName = CZPService.getService().getDevName(mlList);
								
								String tdmlName = "";
								for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
									DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
									if(dtd.getTransDevice().getDeviceType().equals(SystemConstants.Switch) && dtd.getEndstate().equals("0") && dtd.getTransDevice().getDeviceStatus().equals("0")) {
										tdmlName = CZPService.getService().getDevName(dtd.getTransDevice());
										break;
									}
								}
								replaceStr = stName+mxName+"由"+CZPService.getService().getDevName(curDev)+"供电合环调由"+tdmlName+"供电";
							}
						}
					}else{
						for(PowerDevice hignVoltXlkg2 : hignVoltXlkgList2){
							if(RuleExeUtil.getDeviceEndStatus(hignVoltXlkg2).equals("0")){
								replaceStr += stName+"全站由"+CZPService.getService().getDevName(curDev)+"供电合环调由"+CZPService.getService().getDevName(hignVoltXlkg2)+"供电";
								break;
							}
						}
					}
				}
			}
		}
		return replaceStr;
	}


}