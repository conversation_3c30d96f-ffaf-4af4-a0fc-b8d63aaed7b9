package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionHH;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr220T110kVDMMXCZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("220T110kV单母母线操作".equals(tempStr)){
			CommonFunctionHH cf = new CommonFunctionHH();
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			String stationName = CZPService.getService().getDevName(station);
			
			double volt = 0;
			
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();//临时数组,使用完要clear
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkglist = new ArrayList<PowerDevice>();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
					volt = RuleExeUtil.getTransformerVolByType(dev, "low");
				}
			}
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&volt == dev.getPowerVoltGrade()){
					dycmlkglist.add(dev);
				}
			}
			
			replaceStr += cf.getHsdcnrStrReplace(curDev);
			
			List<PowerDevice> hsdevList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> fhczbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			
		    List<PowerDevice> yxdevList = new ArrayList<PowerDevice>();
		    List<PowerDevice> hotdevList = new ArrayList<PowerDevice>();
		    List<PowerDevice> colddevList = new ArrayList<PowerDevice>();

		    hsdevList.addAll(dycmlkglist);
		    
		    for(PowerDevice hsdev :hsdevList){
		    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("0")&&hsdev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
		    		yxdevList.add(hsdev);
		    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("1")){
		    		hotdevList.add(hsdev);
		    	}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(hsdev).equals("2")){
		    		colddevList.add(hsdev);
		    	}
		    }
		    
		    for(PowerDevice mlkg : mlkgList){
		    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(mlkg).equals("0")){
				    yxdevList.add(mlkg);
		    	}
		    }
		    
		    for(PowerDevice fhczbkg : fhczbkgList){
		    	if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(fhczbkg).equals("0")){
				    yxdevList.add(fhczbkg);
		    	}
		    }
		    
		    if(hotdevList.size()>0){
				replaceStr += "核实"+CZPService.getService().getDevName(hotdevList)+"热备用/r/n";
			}
			
			if(colddevList.size()>0){
				replaceStr += "核实"+CZPService.getService().getDevName(colddevList)+"冷备用/r/n";
			}
		    
		    List<PowerDevice> curzbList = new ArrayList<PowerDevice>();
		    
		    if(fhczbkgList.size()>0){
				curzbList = RuleExeUtil.getDeviceList(fhczbkgList.get(0), SystemConstants.PowerTransformer, "", true, true, true);
				
				if(curzbList.size()>0){
					replaceStr += cf.get110kVZbZxdStrReplace(curzbList.get(0), zbList);
					replaceStr += "核实主变中性点切换装置已按现场规程调整好/r/n";
				}
		    }
		    
		    replaceStr += cf.getYcDkStrReplace(yxdevList, stationName);
		    
			tempList.addAll(yxdevList);
			tempList.addAll(hotdevList);
			tempList.remove(dycmlkglist.get(0));
			
			replaceStr += cf.getCzMotherLineDevStrReplace(tempList, curDev,null, stationName, "由热备用转冷备用");
			
			if(curzbList.size()>0){
				replaceStr += "退出"+CZPService.getService().getDevName(curzbList)+"110kV侧后备保护动作跳主变三侧断路器";
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
