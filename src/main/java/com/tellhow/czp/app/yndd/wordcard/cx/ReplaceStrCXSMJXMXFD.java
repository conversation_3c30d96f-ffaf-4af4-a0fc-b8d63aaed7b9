package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXSMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄双母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			replaceStr += "楚雄地调@"+stationName+deviceName+"停电设备摘牌。/r/n";
			
			PowerDevice othermx = new PowerDevice();
			PowerDevice mlkg = new PowerDevice();

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			
			for(PowerDevice dev : mlkgList){
				if(RuleExeUtil.isSwitchDoubleML(dev)){
					mlkg = dev;
					
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					for(PowerDevice mx : mxList){
						if(!mx.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
							othermx = mx ;
							break;
						}
					}
				}
			}
			
			String othermxName = CZPService.getService().getDevName(othermx);
			
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			
			String mlkgName = CZPService.getService().getDevName(mlkg);
			
			replaceStr += voltStationName+"@"+mlkgName+"由冷备用转热备用。/r/n";
			replaceStr += voltStationName+"@投入"+mlkgName+"充电保护。/r/n";
			replaceStr += voltStationName+"@合上"+mlkgName+"对"+deviceName+"及母线电压互感器充电。/r/n";
			replaceStr += voltStationName+"@退出"+mlkgName+"充电保护。/r/n";
			
			List<PowerDevice> yxList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbyList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();

			tempList.addAll(zbkgList);
			tempList.addAll(xlkgList);

			for(PowerDevice dev : tempList){
				List<PowerDevice> dzList =  RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbyList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : yxList){
				String devName = CZPService.getService().getDevName(dev);
				replaceStr += voltStationName+"@"+devName+"由"+othermxName+"运行倒至"+deviceName+"运行。/r/n";
			}
			
			for(PowerDevice dev : rbyList){
				String devName = CZPService.getService().getDevName(dev);
				replaceStr += voltStationName+"@"+devName+"由"+othermxName+"运行倒至"+deviceName+"热备用。/r/n";
			}
		}
		
		return replaceStr;
	}

}
