package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.HashMap;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBFDXHXQCZ implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";

		if("主变复电消弧线圈操作".equals(tempStr)) {
			List<PowerDevice>  zbList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.PowerTransformer, null, true, false, true);
			for(int i=0;i<zbList.size();i++){
				if(zbList.get(i).getPowerDeviceName().contains("站用")||zbList.get(i).getPowerDeviceName().contains("所内")
						||zbList.get(i).getPowerDeviceName().contains("所用")||zbList.get(i).getPowerDeviceName().contains("接地")
						||RuleExeUtil.isDeviceInDtd(zbList.get(i))){
					zbList.remove(i);
					i--;
				}
			}
			
			if(zbList.size()>0){
				replaceStr += "将35kV消弧线圈由"+CZPService.getService().getDevName(zbList.get(0))+"35kV侧运行倒至"+CZPService.getService().getDevName(curDev)+"35kV侧运行/r/n";
			}
			
			//消弧线圈
			if(stationDev.getPowerVoltGrade()==110){
				HashMap<String, PowerDevice> devMap = CBSystemConstants
						.getMapPowerStationDevice().get(stationDev.getPowerStationID());
				PowerDevice xhdz1 = null;
				PowerDevice xhdz2 = null;
				for (PowerDevice dev : devMap.values()) {
					if((dev.getDeviceType().equals(SystemConstants.SwitchSeparate)||dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine))
							){
						if(CZPService.getService().getDevNum(dev).equals("3010")){
							xhdz1=dev;
						}
						else if(CZPService.getService().getDevNum(dev).equals("3020")){
							xhdz2 = dev;
						}
					}
				}
				if(xhdz1!=null&&xhdz2!=null){
					if(xhdz1.getDeviceStatus().equals("0")&&xhdz2.getDeviceStatus().equals("1")){
						replaceStr 	+= "核实35kV消弧线圈3020隔离开关在拉开位置/r/n";
						replaceStr 	+= "核实35kV消弧线圈3010隔离开关在合闸位置/r/n";
					}else if(xhdz1.getDeviceStatus().equals("1")&&xhdz2.getDeviceStatus().equals("0")){
						replaceStr	+="核实35kV消弧线圈3010隔离开关在拉开位置/r/n";
						replaceStr	+="核实35kV消弧线圈3020隔离开关在合闸位置/r/n";
					}else if(xhdz1.getDeviceStatus().equals("1")&&xhdz2.getDeviceStatus().equals("1")){
						replaceStr	+="核实35kV消弧线圈3020隔离开关在拉开位置/r/n";
						replaceStr	+="核实35kV消弧线圈3010隔离开关在拉开位置/r/n";
					}
				}
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
