package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.view.EquipCheckChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class ZTJDKGXZ implements RulebaseInf {
	public static List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
	
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		
		chooseEquips.clear();
		
		String begin = CBSystemConstants.getCurRBM().getBeginStatus();
		String end = CBSystemConstants.getCurRBM().getEndState();
		
		String showMessage = "";
		
		if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
			showMessage = "请选择装设三相接地线的线路";
		}else{
			showMessage = "请选择拆除三相接地线的线路";
		}

		List<String> stationNameList = new ArrayList<String>();
	    List<PowerDevice> lineList = new ArrayList<PowerDevice>();
	    
	    PowerDevice lineSource = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
 		List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
 	    
 		lineList.add(lineSource);
 		lineList.addAll(lineLoad);
 		
		String sql = "SELECT B.ID,B.UNIT,B.LINE_NAME,B.LOWERUNIT FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER B WHERE B.LINE_ID = (SELECT A.ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND A WHERE A.ID = '"+pd.getPowerDeviceID()+"')";
		List<Map<String, String>> list = DBManager.queryForList(sql);
	    
		for(Map<String,String> map : list){
			String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
			
			if(!lowerunit.equals("")){
				stationNameList.add(lowerunit);
			}else{
				stationNameList.add(StringUtils.ObjToString(map.get("UNIT")));
			}
		}
    
		for (Iterator iterator = lineList.iterator(); iterator.hasNext();) {
			PowerDevice dev=(PowerDevice)iterator.next();
			
			if(dev.getPowerStationName().contains("虚拟站")){
				iterator.remove();
			}
			
			PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			
			if(stationNameList.contains(stationName)){
				iterator.remove();
			}
		}
		
		for(Map<String,String> map : list){
			PowerDevice newLine = new PowerDevice();
			newLine.setPowerDeviceID(StringUtils.ObjToString(map.get("ID")));
			newLine.setPowerDeviceName(StringUtils.ObjToString(map.get("LINE_NAME")));
			
			String lowerunit = StringUtils.ObjToString(map.get("LOWERUNIT"));
			
			if(!lowerunit.equals("")){
				newLine.setPowerStationName(lowerunit);
			}else{
				newLine.setPowerStationName(StringUtils.ObjToString(map.get("UNIT")));
			}
			
			stationNameList.add(newLine.getPowerStationName());
			
			lineList.add(newLine);
		}
		
	    EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, lineList , showMessage);
	    chooseEquips=ecc.getChooseEquip();
		
	    if(ecc.isCancel()){
	    	return false;
	    }
	    
		return true;
	}

}
