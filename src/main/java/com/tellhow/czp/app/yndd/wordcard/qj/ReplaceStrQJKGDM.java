package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJKGDM  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖开关倒母".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			
			String thismxName = "";
			String othermxName = "";
			
			for(PowerDevice dz : dzList){
				if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
					if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
						List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
						othermxName = CZPService.getService().getDevName(mxList);
					}else if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
						List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
						thismxName = CZPService.getService().getDevName(mxList);
					}
				}
			}
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			
			for(Iterator<PowerDevice> itor = mlkgList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(!RuleExeUtil.isSwitchDoubleML(dev)){
					itor.remove();
				}
			}
			
			if(curDev.getDeviceStatus().equals("0")){
				replaceStr += stationName+"@将"+deviceName+"由"+thismxName+"运行倒至"+othermxName+"运行/r/n";
			}else if(curDev.getDeviceStatus().equals("1")){
				replaceStr += stationName+"@将"+deviceName+"由"+thismxName+"热备用倒至"+othermxName+"热备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
