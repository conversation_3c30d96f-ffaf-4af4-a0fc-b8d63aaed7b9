package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.pe.TicketKindChoose;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSSMJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山双母接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
//			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
//			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
//			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			String maintenance =  CommonFunctionBS.getMaintenance(stationName);
			
			if(maintenance.equals(stationName)){
				stationName = "";
			}
			
			replaceStr += maintenance+"@核实"+stationName+deviceName+"相关检修工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，"+deviceName+"的保护装置已正常投入，"+deviceName+"具备复电条件/r/n";
			
			if(curDev != null){
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(gdList);
				
				for(PowerDevice dev : gdList) {
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						replaceStr += "保山地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}else if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						replaceStr += "保山地调@检查"+stationName+CZPService.getService().getDevName(dev)+"确已合上/r/n";
					}
				}
			}
			
//			for(PowerDevice dev : zbdyckgList){
//				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
//					if(stationName.equals(maintenance)){
//						replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
//					}else{
//						replaceStr += maintenance+"@将"+stationName+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
//					}
//				}
//			}
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
				}

				replaceStr += "保山地调@执行"+stationName+deviceName+"由热备用转运行程序操作/r/n";
				
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备运行正常/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
				}
				
//				for(PowerDevice dev : zbgyckgList){
//					String zbgyckgName = CZPService.getService().getDevName(dev);
//
//					if(stationName.equals(maintenance)){
//						replaceStr += maintenance+"@核实"+zbgyckgName+"一、二次设备具备程序化操作条件/r/n";
//					}else{
//						replaceStr += maintenance+"@核实"+stationName+zbgyckgName+"一、二次设备具备程序化操作条件/r/n";
//					}
//					
//					replaceStr += "保山地调@执行"+stationName+zbgyckgName+"由热备用转运行程序操作/r/n";
//				}
//				
//				for(PowerDevice dev : zbzyckgList){
//					String zbgyckgName = CZPService.getService().getDevName(dev);
//
//					if(stationName.equals(maintenance)){
//						replaceStr += maintenance+"@核实"+zbgyckgName+"一、二次设备具备程序化操作条件/r/n";
//					}else{
//						replaceStr += maintenance+"@核实"+stationName+zbgyckgName+"一、二次设备具备程序化操作条件/r/n";
//					}
//					
//					replaceStr += "保山地调@执行"+stationName+zbgyckgName+"由热备用转运行程序操作/r/n";
//				}
//				
//				for(PowerDevice dev : zbdyckgList){
//					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
//						replaceStr += "保山地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
//					}
//				}
//				
//				if(stationName.equals(maintenance)){
//					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备运行正常/r/n";
//				}else{
//					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
//				}
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备具备程序化操作条件/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备具备程序化操作条件/r/n";
				}

				replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转运行程序操作/r/n";
				
				if(stationName.equals(maintenance)){
					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备运行正常/r/n";
				}else{
					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
				}
				
				
//				for(PowerDevice dev : zbgyckgList){
//					String zbgyckgName = CZPService.getService().getDevName(dev);
//					
//					if(stationName.equals(maintenance)){
//						replaceStr += maintenance+"@核实"+zbgyckgName+"一、二次设备具备程序化操作条件/r/n";
//					}else{
//						replaceStr += maintenance+"@核实"+stationName+zbgyckgName+"一、二次设备具备程序化操作条件/r/n";
//					}
//					
//					replaceStr += "保山地调@执行"+stationName+zbgyckgName+"由冷备用转运行程序操作/r/n";
//				}
//				
//				for(PowerDevice dev : zbzyckgList){
//					String zbgyckgName = CZPService.getService().getDevName(dev);
//					
//					if(stationName.equals(maintenance)){
//						replaceStr += maintenance+"@核实"+zbgyckgName+"一、二次设备具备程序化操作条件/r/n";
//					}else{
//						replaceStr += maintenance+"@核实"+stationName+zbgyckgName+"一、二次设备具备程序化操作条件/r/n";
//					}
//					
//					replaceStr += "保山地调@执行"+stationName+zbgyckgName+"由冷备用转运行程序操作/r/n";
//				}
//				
//				for(PowerDevice dev : zbdyckgList){
//					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
//						replaceStr += "保山地调@遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环/r/n";
//					}
//				}
//				
//				if(stationName.equals(maintenance)){
//					replaceStr += maintenance+"@核实"+deviceName+"一、二次设备运行正常/r/n";
//				}else{
//					replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
//				}
			}
			
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
			
			for(PowerDevice zb : zbList){
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(zb, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(gdList);
				
				for(PowerDevice dev : gdList) {
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
						replaceStr += "保山地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
