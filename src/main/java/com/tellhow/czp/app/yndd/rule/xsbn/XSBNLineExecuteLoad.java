package com.tellhow.czp.app.yndd.rule.xsbn;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.km.TransformExecuteKM;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013年10月30日 下午8:25:50 
 */
public class XSBNLineExecuteLoad implements RulebaseInf {
	
	private static PowerDevice preSwitch = null;
	private static List<PowerDevice> onLineList = new ArrayList<PowerDevice>();

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		
		String paraID=pd.getPowerDeviceID();
		
		if(pd.getPowerVoltGrade()==110){
			PowerDevice  lineSource = CBSystemConstants.LineSource.get(paraID);
			
			List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(paraID);
			
			if(lineLoad==null){
				return true;
			}
			for(PowerDevice line : lineLoad) {
				boolean res = loadLine(line);
				if(!res){
					return res;
				}			
			}
		}
		
		
		return true;
	}
	
	public String getRunMode(PowerDevice pd) {
		String mode = "";
		List<PowerDevice> mls = RuleUtil.getLinkedDeviceByType2(pd, SystemConstants.MotherLine);
		if(mls.size() > 0) { //通过母线连接主变
			PowerDevice lineSwitch = RuleUtil.getLineSwitch(pd);
			if(lineSwitch != null) { //存在线路开关
				List<PowerDevice> lines = RuleUtil.getLinkedDeviceByType2(lineSwitch, SystemConstants.InOutLine);
				if(lines.size() > 1)
					mode = "0";
				else
					mode = "1";
			}
			else {
				PowerDevice ml = mls.get(0); //线路连接的母线
				List<PowerDevice> zbkgs = RuleUtil.getLinkedDeviceByType2(ml, SystemConstants.Switch);
				for(Iterator it=zbkgs.iterator();it.hasNext();){
					PowerDevice zbkg = (PowerDevice)it.next();
					if(!zbkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) &&
							!zbkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
						it.remove();
					}
				}
				if(zbkgs.size() == 0)
					mode = "4";
				else {
					List<PowerDevice> lines = RuleUtil.getLinkedDeviceByType2(ml, SystemConstants.InOutLine);
					for (PowerDevice line : lines) {
						 if(line.getPowerDeviceID().equals(pd.getPowerDeviceID()))
							 continue;
						 PowerDevice sw = RuleUtil.getLineSwitch(line);
						 if(sw == null)
							 mode = "2"; //不存在线路开关，即多条线路共用主变开关
					}
					if(!mode.equals("2"))
						mode = "3";
				}
			}
		}
		else {
			mode = "4";
		}
		
		
		if(!mode.equals("0") && !mode.equals("2")) {
			List<PowerDevice> lineList = RuleExeUtil.getLineOtherSideList(pd);
			for (PowerDevice line : lineList) {
				List<PowerDevice> mlList = RuleUtil.getLinkedDeviceByType2(line, SystemConstants.MotherLine);
				if(mlList.size() > 0) { //通过母线连接主变
					PowerDevice lineSwitch = RuleUtil.getLineSwitch(line);
					if(lineSwitch == null) { //不存在线路开关
						PowerDevice ml = mlList.get(0); //线路连接的母线
						List<PowerDevice> zbkgs = RuleUtil.getLinkedDeviceByType2(ml, SystemConstants.Switch);
						for(Iterator it=zbkgs.iterator();it.hasNext();){
							PowerDevice zbkg = (PowerDevice)it.next();
							if(!zbkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) &&
									!zbkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
								it.remove();
							}
						}
						if(zbkgs.size() > 0) {
							List<PowerDevice> lines = RuleUtil.getLinkedDeviceByType2(ml, SystemConstants.InOutLine);
							for (PowerDevice dev : lines) {
								 if(dev.getPowerDeviceID().equals(pd.getPowerDeviceID()))
									 continue;
								 PowerDevice sw = RuleUtil.getLineSwitch(dev);
								 if(sw == null)
									 mode = "2"; //不存在线路开关，即多条线路共用主变开关
							}
						}
					}
				}
			}
		}
		
		if(!mode.equals("0")) {
			List<PowerDevice> lineList = RuleExeUtil.getLineOtherSideList(pd);
			for (PowerDevice line : lineList) {
				List<PowerDevice> mlList = RuleUtil.getLinkedDeviceByType2(line, SystemConstants.MotherLine);
				if(mlList.size() > 0) { //通过母线连接主变
					PowerDevice lineSwitch = RuleUtil.getLineSwitch(line);
					if(lineSwitch != null) { //存在线路开关
						List<PowerDevice> lines = RuleUtil.getLinkedDeviceByType2(lineSwitch, SystemConstants.InOutLine);
						if(lines.size() > 1)
							mode = "0";
					}
				}
			}
		}
		
		return mode;
	}
	
	public boolean isLineCable(PowerDevice pd) {
		String mode = getRunMode(pd);
		return mode.equals("0");
	}
	
	public boolean isLineShareSwitch(PowerDevice pd) {
		String mode = "";
		List<PowerDevice> mls = RuleUtil.getLinkedDeviceByType2(pd, SystemConstants.MotherLine);
		if(mls.size() > 0) { //通过母线连接主变
			PowerDevice lineSwitch = RuleUtil.getLineSwitch(pd);
			if(lineSwitch != null) { //存在线路开关
				List<PowerDevice> lines = RuleUtil.getLinkedDeviceByType2(lineSwitch, SystemConstants.InOutLine);
				if(lines.size() > 1)
					mode = "0";
				else
					mode = "1";
			}
			else {
				PowerDevice ml = mls.get(0); //线路连接的母线
				List<PowerDevice> zbkgs = RuleUtil.getLinkedDeviceByType2(ml, SystemConstants.Switch);
				for(Iterator it=zbkgs.iterator();it.hasNext();){
					PowerDevice zbkg = (PowerDevice)it.next();
					if(!zbkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) &&
							!zbkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
						it.remove();
					}
				}
				if(zbkgs.size() == 0)
					mode = "4";
				else {
					List<PowerDevice> lines = RuleUtil.getLinkedDeviceByType2(ml, SystemConstants.InOutLine);
					for (PowerDevice line : lines) {
						 if(line.getPowerDeviceID().equals(pd.getPowerDeviceID()))
							 continue;
						 PowerDevice sw = RuleUtil.getLineSwitch(line);
						 if(sw == null)
							 mode = "2"; //不存在线路开关，即多条线路共用主变开关
					}
					if(!mode.equals("2"))
						mode = "3";
				}
			}
		}
		else {
			mode = "4";
		}
		
		
		if(!mode.equals("0") && !mode.equals("2")) {
			List<PowerDevice> lineList = RuleExeUtil.getLineOtherSideList(pd);
			for (PowerDevice line : lineList) {
				List<PowerDevice> mlList = RuleUtil.getLinkedDeviceByType2(line, SystemConstants.MotherLine);
				if(mlList.size() > 0) { //通过母线连接主变
					PowerDevice lineSwitch = RuleUtil.getLineSwitch(line);
					if(lineSwitch == null) { //不存在线路开关
						PowerDevice ml = mlList.get(0); //线路连接的母线
						List<PowerDevice> zbkgs = RuleUtil.getLinkedDeviceByType2(ml, SystemConstants.Switch);
						for(Iterator it=zbkgs.iterator();it.hasNext();){
							PowerDevice zbkg = (PowerDevice)it.next();
							if(!zbkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) &&
									!zbkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
								it.remove();
							}
						}
						if(zbkgs.size() > 0) {
							List<PowerDevice> lines = RuleUtil.getLinkedDeviceByType2(ml, SystemConstants.InOutLine);
							for (PowerDevice dev : lines) {
								 if(dev.getPowerDeviceID().equals(pd.getPowerDeviceID()))
									 continue;
								 PowerDevice sw = RuleUtil.getLineSwitch(dev);
								 if(sw == null)
									 mode = "2"; //不存在线路开关，即多条线路共用主变开关
							}
						}
					}
				}
			}
		}
		return mode.equals("2");
	}
	

	public static PowerDevice getPreSwitch() {
		return preSwitch;
	}
	
	public static void setPreSwitch(PowerDevice preSwitch) {
		XSBNLineExecuteLoad.preSwitch = preSwitch;
	}

	public static List<PowerDevice> getOnLineList() {
		return onLineList;
	}

	public static void setOnLineList(List<PowerDevice> onLineList) {
		XSBNLineExecuteLoad.onLineList = onLineList;
	}
	
	
	public boolean convertLineToCold(PowerDevice pd) {
		
		List<PowerDevice> sourceList = new ArrayList<PowerDevice>();
		List<PowerDevice> searchedList = new ArrayList<PowerDevice>();
		List<PowerDevice> loadList = new ArrayList<PowerDevice>();
		searchedList.add(pd);
		if(!RuleExeUtil.isSourceSide(pd))
			sourceList.add(pd);
		searchSourceOff(pd, sourceList, searchedList);
		
		searchLoadOff(sourceList.get(0), sourceList, loadList);
		
		for(int i = loadList.size()-1; i >= 0; i--) {
			PowerDevice load = loadList.get(i);
			loadLine(load);
			PowerDevice sw = RuleExeUtil.getDeviceSwitch(load);
			if(sw != null)
				RuleUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "2");
		}
		
		return true;
	}
	
	public boolean convertLineToOn(PowerDevice pd) {
		
		List<PowerDevice> sourceList = new ArrayList<PowerDevice>();
		List<PowerDevice> searchedList = new ArrayList<PowerDevice>();
		List<PowerDevice> loadList = new ArrayList<PowerDevice>();
		searchedList.add(pd);
		searchSourceOn(pd, sourceList, searchedList);
		
		for(PowerDevice source : sourceList) {
			searchLoadOn(source, sourceList, loadList);
			
			for(int i = loadList.size()-1; i >= 0; i--) {
				PowerDevice load = loadList.get(i);
				PowerDevice sw = RuleExeUtil.getDeviceSwitch(load);
				if(sw != null)
					RuleUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "0");
				executeLoadBack(load);
			}
		}
		
		
		return true;
	}
	
	
	
	/**
	 * 线路倒负荷
	 * @param line
	 * @param rbm
	 * @return
	 */
	public boolean loadLine(PowerDevice line) {
		boolean result = true;
		if(!line.getDeviceStatus().equals("0"))
			return true;
//		if(EMSService.getService().getLineFlow(line.getPowerStationID(), line.getPowerDeviceID()).equals("2"))
//			return true;
		//如果线路直接连接主变，执行主变的主变状态转换
		boolean isTFLoad = false;
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(line, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, true, true);
		if(tfList.size() > 0) {
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			if(mlList.size()>0){
				result = RuleExeUtil.deviceStatusChange(tfList.get(0), tfList.get(0).getDeviceStatus(), "1");
				result = loadMotherLine(line, mlList.get(0));
				List<PowerDevice> swList =RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
				if(swList.size()>0){
					RuleExeUtil.deviceStatusChange(swList.get(0), swList.get(0).getDeviceStatus(), "1");
				}
				 RuleExeUtil.deviceStatusChange(tfList.get(0), tfList.get(0).getDeviceStatus(), "2");
				 return true;
			}else{
				for (PowerDevice tf : tfList) {
//					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(tf, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, false, true);
//					if(lineList.size() <= 1) {
//						result = RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), "2");
//						if(!result)
//							return false;
//						isTFLoad = true;
//					}
					result = RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), "2");
					if(!result)
						return false;
					isTFLoad = true;
				}
			}	
		}
		if(isTFLoad){
			return result;
		}

		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
		for (PowerDevice ml : mlList) {
			if(CBSystemConstants.getCurRBM().getPd().equals(ml)||!RuleExeUtil.isDeviceOperate(ml)) {
				result = loadMotherLine(line, ml);
				if(!result)
					return false;
			}
		}
		return true;
	}
	
	/**
	 * 母线倒负荷
	 * @param srcLine
	 * @param ml
	 * @param rbm
	 * @return
	 */
	public boolean loadMotherLine(PowerDevice srcLine, PowerDevice ml) {
		
		List<PowerDevice> lineList = null;
		
		//查找可以代供的线路
		PowerDevice xlSwitch = RuleExeUtil.getDeviceSwitch(srcLine);
		lineList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, false, false, true);
		for(Iterator it = lineList.iterator();it.hasNext();) {
			PowerDevice ln = (PowerDevice)it.next();
			if(RuleExeUtil.judgeLineFlow(ln).equals("2"))
				it.remove();
		}
		if(lineList.size() > 0)
			return true;
		
		List<PowerDevice> swList = new ArrayList<PowerDevice>();
		
		//查找热备用的线路开关
		List<PowerDevice> swxlList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, true, true);
		for(Iterator it = swxlList.iterator();it.hasNext();) {
			PowerDevice sw = (PowerDevice)it.next();
			if(!sw.getDeviceStatus().equals("1"))
				it.remove();
		}
		
		
		//List<PowerDevice> swmlList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
		
		List<PowerDevice> swmlList = new ArrayList<PowerDevice>();
		HashMap<PowerDevice, ArrayList<PowerDevice>> map = RuleExeUtil.getPathByDevice(ml, srcLine, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, true, false, true);
		 for(Iterator it = map.entrySet().iterator();it.hasNext();) {  
	            Entry e = (Entry)it.next();  
	            ArrayList<PowerDevice> eqList =  (ArrayList<PowerDevice>)e.getValue();
	            for(PowerDevice eq : eqList) {
	            	if(eq.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) && eq.getDeviceStatus().equals("1")) {
	            		if(!swmlList.contains(eq))
	            			swmlList.add(eq);
	            	}
	            }
		 }
		
		//关联的母联
		PowerDevice mupd=RuleExeUtil.getSwitchBackupSwitch(xlSwitch);
		
		
		//如果是边开关，自动合上关联母联开关；如果是中间开关，给出母联开关选项并默认选中关联的母联开关
		List<PowerDevice> mlkgList =RuleExeUtil.getDeviceList(xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,
				"", false, true, false, true);
		
		if(mupd != null && swmlList.contains(mupd)&&mlkgList.size()<2) {
			swmlList.clear();
			swmlList.add(mupd);
		}
		swList.addAll(swxlList);
		swList.addAll(swmlList);
		
		PowerDevice dev = null;
		
		if(swList.size() == 1)
			dev = swList.get(0);
		else if(swList.size() > 1) {
			EquipCheckChoose ecc = new EquipCheckChoose(SystemConstants.getMainFrame(), true, swList, "请选择["+CBSystemConstants.getPowerStation(srcLine.getPowerStationID()).getPowerStationName()+"]["+srcLine.getPowerDeviceName()+"]停电前合上的开关",mupd);
			if(ecc.getChooseEquip()!=null && ecc.getChooseEquip().size() > 0) {
				dev = ecc.getChooseEquip().get(0);
			}
		}
		if(dev != null)
			RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");
		else {
			//如果是电源侧母线，执行母线状态转换
			if(RuleExeUtil.isSourceSide(ml))
				RuleExeUtil.deviceStatusChange(ml, ml.getDeviceStatus(), "2");
		}
		

		return true;
	}

	public boolean executeLoadBack(PowerDevice pd) {
		List<PowerDevice> mls =  RuleUtil.getMotherLineListAllVolOff(pd);
		PowerDevice xlSwitch = RuleExeUtil.getDeviceSwitch(pd);
		
		if(mls.size() > 0){
			for (PowerDevice ml : mls) {
				List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
				List<PowerDevice> hhzdList = new ArrayList<PowerDevice>();
				
				if(ml.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
					if(mlkgList.size()>0){
						PowerDevice otherMX = RuleUtil.getAnotherMotherLine(mlkgList.get(0), ml);
						List<PowerDevice> otherswxlList = RuleExeUtil.getDeviceList(otherMX, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
						for(PowerDevice sw : otherswxlList) {
							if(sw.getDeviceStatus().equals("0")||sw.getDeviceStatus().equals("1"))
								hhzdList.add(sw);
						}
						for(PowerDevice sw : mlkgList){
							if(sw.getDeviceStatus().equals("0"))
								hhzdList.add(sw);
						}
					}else{//搜不到母联开关，搜运行的线路，如需转电，选择开关状态
						List<PowerDevice> otheryxSwList = RuleExeUtil.getDeviceList(ml,xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
					
						for(PowerDevice sw : otheryxSwList){
							if(sw.getDeviceStatus().equals("0")){
								hhzdList.add(sw);
							}
						}
					}
				}
				
				if(hhzdList.size() > 0){
					List<String> defaultStatusList = new ArrayList<String>();
					for(PowerDevice hhzd : hhzdList) {
						defaultStatusList.add(hhzd.getDeviceStatus());
					}
					List<String> expStatusList = new ArrayList<String>();
					for(PowerDevice hhzd : hhzdList) {
						expStatusList.add("3");
					}
					
					Map tagStatusMap = new HashMap();
					if(CBSystemConstants.isCurrentSys) {
						EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, hhzdList, defaultStatusList,expStatusList, "如需转电请选择线路开关要转换的状态：");
						tagStatusMap=dialog.getTagStatusMap();
					}
					
					if(tagStatusMap.size()==0){
						return false;
					}
					
					if(tagStatusMap.size()>0){
					   List<Map.Entry<PowerDevice,String>> list = new ArrayList<Map.Entry<PowerDevice,String>>(tagStatusMap.entrySet());
				        //然后通过比较器来实现排序
				        Collections.sort(list,new Comparator<Map.Entry<PowerDevice,String>>() {
				            //升序排序
				            public int compare(Entry<PowerDevice, String> o1,
				                    Entry<PowerDevice, String> o2) {
				                return o1.getValue().compareTo(o2.getValue());
				            }
				            
				        });
						
				        for(Map.Entry<PowerDevice,String> entry:list){
				    		if(!entry.getKey().getDeviceStatus().equals(entry.getValue())){//改变了才做操作
								RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(),entry.getValue());
							}
						}
					}
				}
			}
		}else{
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
			
			if(mlkgList.size() > 0){
				List<String> defaultStatusList = new ArrayList<String>();
				for(PowerDevice hhzd : mlkgList) {
					defaultStatusList.add(hhzd.getDeviceStatus());
				}
				List<String> expStatusList = new ArrayList<String>();
				for(PowerDevice hhzd : mlkgList) {
					expStatusList.add("3");
				}
				
				Map tagStatusMap = new HashMap();
				if(CBSystemConstants.isCurrentSys) {
					EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, mlkgList, defaultStatusList,expStatusList, "如需转电请选择线路开关要转换的状态：");
					tagStatusMap=dialog.getTagStatusMap();
				}
				
				if(tagStatusMap.size()==0){
					return false;
				}
				
				if(tagStatusMap.size()>0){
				   List<Map.Entry<PowerDevice,String>> list = new ArrayList<Map.Entry<PowerDevice,String>>(tagStatusMap.entrySet());
			        //然后通过比较器来实现排序
			        Collections.sort(list,new Comparator<Map.Entry<PowerDevice,String>>() {
			            //升序排序
			            public int compare(Entry<PowerDevice, String> o1,
			                    Entry<PowerDevice, String> o2) {
			                return o1.getValue().compareTo(o2.getValue());
			            }
			            
			        });
					
			        for(Map.Entry<PowerDevice,String> entry:list){
			    		if(!entry.getKey().getDeviceStatus().equals(entry.getValue())){//改变了才做操作
							RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(),entry.getValue());
						}
					}
				}
			}
		}
	
		return true;
	}
	
	
	public void searchLoadOff(PowerDevice pd, List<PowerDevice> sourceList, List<PowerDevice> loadList) {
		List<PowerDevice> list = new ArrayList<PowerDevice>();
		List<PowerDevice> cableList = RuleExeUtil.getLineCable(pd);
		for(PowerDevice ln : cableList) {
			if(!sourceList.contains(ln) && !loadList.contains(ln) && ln.getDeviceStatus().equals("0"))
				list.add(ln);
		}
		List<PowerDevice> otherSideList = RuleExeUtil.getLineOtherSideList(pd);
		for(PowerDevice ln : otherSideList) {
			if(!sourceList.contains(ln) && !loadList.contains(ln) && ln.getDeviceStatus().equals("0")) //20160312 因城西线停电
				list.add(ln);
		}
		
		if(list.size() > 0) {
			for(PowerDevice line : list) {
				if(!loadList.contains(line))
					loadList.add(line);
				if(line.getDeviceStatus().equals("0"))
					searchLoadOff(line, sourceList, loadList);
			}
			
		}
		else
			return;
	}
	
	/**
	 * 查找电源线路，包括共用电源的线路
	 * @param pd
	 * @param sourceList
	 * @param searchedList
	 */
	public void searchSourceOff(PowerDevice pd, List<PowerDevice> sourceList, List<PowerDevice> searchedList) {
		List<PowerDevice> list = new ArrayList<PowerDevice>();
		String lineFlow = RuleExeUtil.judgeLineFlow(pd);
		if(lineFlow.equals("1")) {
			List<PowerDevice> otherSideList = RuleExeUtil.getLineOtherSideList(pd);
			for(PowerDevice ln : otherSideList) {
				if(!searchedList.contains(ln) && ln.getDeviceStatus().equals("0") && RuleExeUtil.judgeLineFlow(ln).equals("2")) {
					list.add(ln);
				}
				searchedList.add(ln);
			}
			for(PowerDevice line : list) {
				
				searchSourceOff(line, sourceList, searchedList);	
			}
		}
		else if(lineFlow.equals("2"))  { 
			List<PowerDevice> cableList = RuleExeUtil.getLineCable(pd);
			if(!RuleExeUtil.isSourceSide(pd)) {  //如果线路在负荷侧，一定是电源侧
				sourceList.add(0, pd);
				if(cableList.size() > 0)
					sourceList.addAll(cableList);
				List<PowerDevice> cablOthereList = RuleExeUtil.getLineCableOther(pd);
				if(cablOthereList.size() > 0)
					sourceList.addAll(cablOthereList);
			}
			else if(cableList.size() > 0) {
				for(PowerDevice ln : cableList) {
					if(!searchedList.contains(ln) && ln.getDeviceStatus().equals("0") && RuleExeUtil.judgeLineFlow(ln).equals("1"))
						list.add(ln);
					searchedList.add(ln);
				}
				for(PowerDevice line : list) {
					
					searchSourceOff(line, sourceList, searchedList);	
				}
			}
			else {
				sourceList.add(0,pd);
				List<PowerDevice> cablOthereList = RuleExeUtil.getLineCableOther(pd);
				if(cablOthereList.size() > 0)
					sourceList.addAll(cablOthereList);
				
			}
			 
				
			
		}
		return;
	}
	
	public void searchLoadOn(PowerDevice pd, List<PowerDevice> sourceList, List<PowerDevice> loadList) {
		List<PowerDevice> list = new ArrayList<PowerDevice>();
		List<PowerDevice> cableList = RuleExeUtil.getLineCable(pd);
		for(PowerDevice ln : cableList) {
			
			boolean isExistOn = false;
			List<PowerDevice> lineOtherSideList = RuleExeUtil.getLineOtherSideList(ln);
			for(PowerDevice ln2 : lineOtherSideList) {
				if(ln2.getDeviceStatus().equals("0")) {
					isExistOn = true;
					//list.add(ln2); //20160229
					break;
				}
			}
			if(isExistOn)
				continue;
			
			if(!sourceList.contains(ln) && !loadList.contains(ln))
				list.add(ln);
		}
		List<PowerDevice> otherSideList = RuleExeUtil.getLineOtherSideList(pd);
		for(PowerDevice ln : otherSideList) {
			if(!sourceList.contains(ln) && !loadList.contains(ln))
				list.add(ln);
		}
		
		if(list.size() > 0) {
			for(PowerDevice line : list) {
				if(!loadList.contains(line))
					loadList.add(line);
				searchLoadOn(line, sourceList, loadList);
					
			}
			
		}
		else
			return;
	}
	
	public void searchSourceOn(PowerDevice pd, List<PowerDevice> sourceList, List<PowerDevice> searchedList) {
		List<PowerDevice> list = new ArrayList<PowerDevice>();
		String lineFlow = RuleExeUtil.judgeLineFlow(pd);
		if(lineFlow.equals("1")) {
			List<PowerDevice> otherSideList = RuleExeUtil.getLineOtherSideList(pd);
			for(PowerDevice ln : otherSideList) {
				if(!searchedList.contains(ln) && RuleExeUtil.judgeLineFlow(ln).equals("2")) {
					list.add(ln);
				}
				searchedList.add(ln);
			}
			for(PowerDevice line : list) {
				
				searchSourceOn(line, sourceList, searchedList);	
			}
		}
		else if(lineFlow.equals("2"))  {
			List<PowerDevice> cableList = RuleExeUtil.getLineCable(pd);
			if(!RuleExeUtil.isSourceSide(pd)) {  //如果线路在负荷侧，一定是电源侧
				sourceList.add(pd);
				List<PowerDevice> cablOthereList = RuleExeUtil.getLineCableOther(pd);
				if(cablOthereList.size() > 0)
					sourceList.addAll(cablOthereList);
				if(cableList.size() > 0)
					sourceList.addAll(cableList);
			}
			else if(cableList.size() > 0) {
				for(PowerDevice ln : cableList) {
					if(!searchedList.contains(ln) && RuleExeUtil.judgeLineFlow(ln).equals("1"))
						list.add(ln);
					searchedList.add(ln);
				}
				for(PowerDevice line : list) {
					
					searchSourceOn(line, sourceList, searchedList);	
				}
			}
			else {
				sourceList.add(pd);
				List<PowerDevice> cablOthereList = RuleExeUtil.getLineCableOther(pd);
				if(cablOthereList.size() > 0)
					sourceList.addAll(cablOthereList);
				
			}
			 
				
			
		}
		return;
	}
	
	/**
	 *获得线路所在的T接线路 
	 */
	public List<PowerDevice> getLineRelatedLine(PowerDevice  pd) {
		List<PowerDevice> sourceList = new ArrayList<PowerDevice>();
		List<PowerDevice> searchedList = new ArrayList<PowerDevice>();
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
		
		searchedList.add(pd);
		if(CBSystemConstants.getCurRBM().getBeginStatus().equals("0"))
			searchSourceOff(pd, sourceList, searchedList); //先找电源侧线路
		else
			searchSourceOn(pd, sourceList, searchedList);
		for(PowerDevice source : sourceList) {
			
			lineList = new ArrayList<PowerDevice>();
			lineList.add(source);
			if(CBSystemConstants.getCurRBM().getBeginStatus().equals("0"))
				searchLoadOff(source, sourceList, lineList);
			else
				searchLoadOn(source, sourceList, lineList);
			
			if(lineList.contains(pd))
				break;
		}
		return lineList;
	}
	
	/**
	 *获得线路关联的T接线路 
	 */
	public List<PowerDevice> getLineRelatedOtherLine(PowerDevice pd) {
		List<PowerDevice> sourceList = new ArrayList<PowerDevice>();
		List<PowerDevice> searchedList = new ArrayList<PowerDevice>();
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
		
		searchedList.add(pd);
		searchSourceOff(pd, sourceList, searchedList); //先找电源侧线路
		
		for(PowerDevice source : sourceList) {
			
			lineList = new ArrayList<PowerDevice>();
			lineList.add(source);
			searchLoadOff(source, sourceList, lineList);
			if(!lineList.contains(pd)) {
				return lineList;
			}
		}
		return new ArrayList<PowerDevice>();
	}
}
