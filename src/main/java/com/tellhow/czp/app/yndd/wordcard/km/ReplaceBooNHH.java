package com.tellhow.czp.app.yndd.wordcard.km;

import com.tellhow.czp.app.yndd.rule.km.JudgeLoopClosing;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooNHH implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("能合环".equals(tempStr)){
			if(JudgeLoopClosing.flag.equals("不能合环")){
				return false;
			}else if(JudgeLoopClosing.flag.equals("不需要合环")){
				return true;
			}else{
				return true;
			}
		}
        return false;
	}
}
