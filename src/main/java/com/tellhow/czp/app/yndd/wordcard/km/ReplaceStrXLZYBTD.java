package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXLZYBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("线路站用变停电".equals(tempStr)){
			List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(curDev);
			
			for(PowerDevice dev : lineList){
				PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
				
				String zbysql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_LINEZYB WHERE LINE_ID = '"+dev.getPowerDeviceID()+"'";
				List<Map<String,String>> zybNameList = DBManager.queryForList(zbysql);
				
				for(Map<String,String> zybNameMap : zybNameList){
					String zbyName = StringUtils.ObjToString(zybNameMap.get("ZYB_NAME"));
					replaceStr += CZPService.getService().getDevName(station)+"@将"+zbyName+"由运行转冷备用/r/n";
				}
			}
		}
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
