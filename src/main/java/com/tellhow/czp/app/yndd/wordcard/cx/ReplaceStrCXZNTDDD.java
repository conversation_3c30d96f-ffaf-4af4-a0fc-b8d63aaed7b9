package com.tellhow.czp.app.yndd.wordcard.cx;

import java.util.HashMap;
import java.util.Iterator;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCXZNTDDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("楚雄站内停电调电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String voltStationName = CZPService.getService().getDevName(station); 
			String stationName = StringUtils.killVoltInDevName(voltStationName);
			String deviceName = CZPService.getService().getDevName(curDev);
			
			PowerDevice hotkg = new PowerDevice();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						hotkg = dev;
					}
				}
			}
			
			replaceStr += "楚雄配调@做好"+voltStationName+"短时停电准备。/r/n";
			replaceStr += "楚雄地调@遥控断开"+stationName+deviceName+"。/r/n";
			replaceStr += "楚雄地调@遥控合上"+stationName+CZPService.getService().getDevName(hotkg)+"。/r/n";
			replaceStr += "楚雄配调@通知"+voltStationName+"供电正常。/r/n";
		}
		
		return replaceStr;
	}

}
