package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

/**
 * Gny
 * 判断是否监控展示
 */
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooDMJX implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		boolean ret = false;
		if (tempStr.equals("单母接线")) {
			List<PowerDevice> mlkgList  = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getPowerVoltGrade() == stationDev.getPowerVoltGrade()){
						mlkgList.add(dev);
					}
				}
				
				if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
					List<PowerDevice> zbList  = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.PowerTransformer);
					
					if(zbList.size()>0){
						return false;
					}
				}
			}
			
			
			if(stationDev.getDeviceType().equals(SystemConstants.InOutLine)){
				PowerDevice equip = RuleExeUtil.getDeviceSwitch(stationDev);
				if(equip!=null&&equip.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)&&mlkgList.size()==0){
					return true;
				}
			}else if(stationDev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)&&mlkgList.size()==0){
				return true;
			}else if(stationDev.getDeviceType().equals(SystemConstants.PowerTransformer)&&mlkgList.size()==0&&!RuleExeUtil.isTransformerXBZ(stationDev)){
				return true;
			}
		}
		return ret;
	}
		
}
