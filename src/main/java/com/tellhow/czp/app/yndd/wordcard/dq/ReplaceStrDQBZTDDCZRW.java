package com.tellhow.czp.app.yndd.wordcard.dq;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.dq.BZTDDExecute;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDQBZTDDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("迪庆备自投调电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			
			PowerDevice powercutLine = new PowerDevice();
			PowerDevice powerLine = new PowerDevice();

			for(PowerDevice dev : BZTDDExecute.powercutDeviceList){
				powercutLine = dev;
			}
			
			for(PowerDevice dev : BZTDDExecute.powerDeviceList){
				powerLine = dev;
			}
			
			replaceStr += stationName+"由"+CZPService.getService().getDevName(powercutLine)+"供电转由"+CZPService.getService().getDevName(powerLine)+"供电";
		}
		
		return replaceStr;
	}

}
