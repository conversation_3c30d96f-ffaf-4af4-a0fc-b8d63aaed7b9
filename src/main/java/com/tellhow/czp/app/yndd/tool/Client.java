package com.tellhow.czp.app.yndd.tool;

import com.alibaba.fastjson.JSONObject;

import czprule.system.ShowMessage;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;

public class Client{

    private String host;
    private int port;
    private String json;

    public Client(String host, int port,String json) {
        this.host = host;
        this.port = port;
        this.json = json;
    }

    public void push() throws IOException {
        ObjectOutputStream oos = null;
        ObjectInputStream ois = null;
        Socket socket = null;
        try {
            socket = new Socket(host, port);
            System.out.println("业务socket链接成功");
           // socket.connect(new InetSocketAddress(host,port),5000);
            oos = new ObjectOutputStream(socket.getOutputStream());
            ois = new ObjectInputStream(socket.getInputStream());
            JSONObject jsonObject = JSONObject.parseObject(json);
            oos.writeObject(jsonObject);
            oos.flush();
            String message = ois.readUTF();
            System.out.println("接收到服务端响应" + message);
            
            JSONObject jsonReturn = JSONObject.parseObject(message);
            
			if(jsonReturn.getString("code").equals("0")){
				ShowMessage.view("操作票传入网络发令系统成功！");
			}else{
				ShowMessage.view("操作票传入网络发令系统失败！");
				System.out.println(jsonReturn.getString("msg"));
			}
        } catch (IOException e) {
        	ShowMessage.view("操作票传入网络发令系统失败！");
            e.printStackTrace();
        } finally {
            if(ois != null) {
                ois.close();
            }
            if(oos !=null) {
                oos.close();
            }
            if(socket != null) {
                socket.close();
            }
        }
    }
}

