package com.tellhow.czp.app.yndd.wordcard.hh;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBHSRBY implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("主变核实热备用".equals(tempStr)){
			if(CBSystemConstants.getCurRBM().getBeginStatus().equals("0")){
				List<PowerDevice>  rbyList = new ArrayList<PowerDevice>();
				List<PowerDevice>  lbyList = new ArrayList<PowerDevice>();

				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
							rbyList.add(dev);
						}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("2")){
							lbyList.add(dev);
						}
					}
				}
			
				replaceStr+="核实"+CZPService.getService().getDevName(rbyList)+"热备用/r/n";
				replaceStr+="核实"+CZPService.getService().getDevName(lbyList)+"冷备用/r/n";
			}else if(CBSystemConstants.getCurRBM().getEndState().equals("0")){
				List<PowerDevice>  mxList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.MotherLine, 
						SystemConstants.PowerTransformer, true, true, true);
				RuleExeUtil.swapDeviceList(mxList);
				if(mxList.size()>0){
					PowerDevice dymx = mxList.get(0);
					List<PowerDevice> mlSwitchList = RuleExeUtil.getDeviceList(dymx, SystemConstants.Switch, 
							SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML,"",false, true, true, true);
					if(dymx.getPowerStationName().contains("西湖变")&&dymx.getPowerVoltGrade()==35){
						mlSwitchList =  RuleExeUtil.getDeviceList(dymx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, false);
					}
					if(mlSwitchList.size()>0){//低压有分段开关
						
					}else{
						List<PowerDevice> drqswList = RuleExeUtil.getDeviceList(dymx, SystemConstants.Switch, 
								SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDR,"",false, true, true, true);
						for(int i=0;i<drqswList.size();i++){
							if(!RuleExeUtil.getDeviceBeginStatus(drqswList.get(i)).equals("1")){
								drqswList.remove(i);
								i--;
							}
						}
						
						List<PowerDevice> zybswList = RuleExeUtil.getDeviceList(dymx, SystemConstants.Switch, 
								SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSwitchZYB,"",false, true, true, true);
						for(int i=0;i<zybswList.size();i++){
							if(!zybswList.get(i).getPowerDeviceName().contains("站用变")||!RuleExeUtil.getDeviceBeginStatus(zybswList.get(i)).equals("1")){
								zybswList.remove(i);
								i--;
							}
						}
						for(PowerDevice sw:drqswList){
							replaceStr+=StringUtils.getVolt(sw.getPowerVoltGrade())+StringUtils.killVoltInDevName(sw.getPowerDeviceName()).replace("组", "组及")+"、";
						}
						for(PowerDevice sw:zybswList){
							replaceStr+=StringUtils.getVolt(sw.getPowerVoltGrade())+StringUtils.killVoltInDevName(sw.getPowerDeviceName()).replace("变", "变及")+"、";
						}
						if(replaceStr.endsWith("、")){
							replaceStr=replaceStr.substring(0,replaceStr.length()-1);
						}
						replaceStr="核实"+CZPService.getService().getDevName(mxList.get(0))+"及母线设备、"+replaceStr+"热备用";
					}
					
					/*
					 * 桥型接线
					 */
					if(stationDev.getDeviceRunModel().equals(CBSystemConstants.RunModelBridgeLine)){
						HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());

						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getPowerVoltGrade() == stationDev.getPowerVoltGrade()){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
										||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
									if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")||(RuleExeUtil.getDeviceBeginStatus(dev).equals("")&&dev.getDeviceStatus().equals("1"))){
										replaceStr+="核实"+CZPService.getService().getDevName(dev)+"热备用";
									}
								}
							}
						}
					}
					
					//消弧线圈
					if(stationDev.getPowerVoltGrade()==110){
						HashMap<String, PowerDevice> devMap = CBSystemConstants
								.getMapPowerStationDevice().get(stationDev.getPowerStationID());
						PowerDevice xhdz1 = null;
						PowerDevice xhdz2 = null;
						for (PowerDevice dev : devMap.values()) {
							if((dev.getDeviceType().equals(SystemConstants.SwitchSeparate)||dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine))
									){
								if(CZPService.getService().getDevNum(dev).equals("3010")){
									xhdz1=dev;
								}
								else if(CZPService.getService().getDevNum(dev).equals("3020")){
									xhdz2 = dev;
								}
//								replaceStr+="\r\n核实35kV消弧线圈冷备用（110kV#1主变35kV侧中性点3010、110kV#2主变35kV侧中性点3020隔离开关在拉开位置）";
							}
						}
						if(xhdz1!=null&&xhdz2!=null){
							if(xhdz1.getDeviceStatus().equals("0")&&xhdz2.getDeviceStatus().equals("1")){
								replaceStr+="\r\n核实35kV消弧线圈运行于110kV#1主变35kV侧（3010隔离开关在合闸位置，3020隔离开关在拉开位置）";
//								if(stationDev.getPowerDeviceName().contains("#1")){
//									replaceStr+="\r\n将35kV消弧线圈由110kV#1主变35kV侧运行倒至110kV#2主变35kV侧运行";
//									replaceStr+="\r\n核实35kV消弧线圈3010隔离开关在拉开位置";
//								}
							}else if(xhdz1.getDeviceStatus().equals("1")&&xhdz2.getDeviceStatus().equals("0")){
								replaceStr+="\r\n核实35kV消弧线圈运行于110kV#2主变35kV侧（3020隔离开关在合闸位置，3010隔离开关在拉开位置）";
//								if(stationDev.getPowerDeviceName().contains("#2")){
//									replaceStr+="\r\n将35kV消弧线圈由110kV#2主变35kV侧运行倒至110kV#1主变35kV侧运行";
//									replaceStr+="\r\n核实35kV消弧线圈3020隔离开关在拉开位置";
//								}
							}else if(xhdz1.getDeviceStatus().equals("1")&&xhdz2.getDeviceStatus().equals("1")){
								replaceStr+="\r\n核实35kV消弧线圈冷备用（110kV#1主变35kV侧中性点3010、110kV#2主变35kV侧中性点3020隔离开关在拉开位置）";
							}
						}
						
					}
				}
			}
			
		}
		
		if(replaceStr.startsWith("\r\n")){
			replaceStr=replaceStr.replaceFirst("\r\n", "");
		}
 		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
