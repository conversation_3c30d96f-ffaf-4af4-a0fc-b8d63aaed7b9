package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSKGDM  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山开关倒母".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			String deviceName = CZPService.getService().getDevName(curDev);
			String maintenance = CommonFunctionBS.getMaintenance(stationName);

			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			
			String thismxName = "";
			String othermxName = "";
			
			for(PowerDevice dz : dzList){
				if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
					othermxName = CZPService.getService().getDevName(mxList);
				}else if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
					thismxName = CZPService.getService().getDevName(mxList);
				}
			}
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			
			for(Iterator<PowerDevice> itor = mlkgList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(!RuleExeUtil.isSwitchDoubleML(dev)){
					itor.remove();
				}
			}
			
			if(maintenance.equals(stationName)){
				replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
				
				for(PowerDevice dev : mlkgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
				}
				
				replaceStr += maintenance+"@核实"+deviceName+"停电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
			}else{
				replaceStr += maintenance+"@核实"+stationName+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
				
				for(PowerDevice dev : mlkgList){
					replaceStr += maintenance+"@核实"+stationName+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
				}
				
				replaceStr += maintenance+"@核实"+stationName+deviceName+"停电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
			}
			
			
			if(curDev.getDeviceStatus().equals("0")){
				replaceStr += "保山地调@执行"+stationName+deviceName+"由"+thismxName+"运行倒至"+othermxName+"运行程序操作/r/n";
			}else if(curDev.getDeviceStatus().equals("1")){
				replaceStr += "保山地调@执行"+stationName+deviceName+"由热备用转冷备用程序操作/r/n";
				replaceStr += "保山地调@执行"+stationName+deviceName+"由冷备用转热备用于"+othermxName+"程序操作/r/n";
			}
			
			if(maintenance.equals(stationName)){
				replaceStr += stationName+"@核实"+deviceName+"一、二次设备运行正常/r/n";
			}else{
				replaceStr += maintenance+"@核实"+stationName+deviceName+"一、二次设备运行正常/r/n";
			}
			
			if(curDev.getDeviceStatus().equals("0")){
				if(maintenance.equals(stationName)){
					replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
					
					for(PowerDevice dev : mlkgList){
						replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
					}
				}else{
					replaceStr += maintenance+"@核实"+stationName+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
					
					for(PowerDevice dev : mlkgList){
						replaceStr += maintenance+"@核实"+stationName+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
