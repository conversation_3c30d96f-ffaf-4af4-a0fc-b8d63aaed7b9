package com.tellhow.czp.app.yndd.rule.xsbn;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.EquipRadioChoose;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;

import java.util.*;

public class XSBNTransformExecuteBak implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		boolean result = true;
		PowerDevice pd = rbm.getPd();
		if(rbm.getBeginStatus().equals("0")) { //运行转热备用
			setOtherZxdOn(pd);
			ConvertSwitchOnToHotTS(pd);
			result = ConvertSwitchOnToHot(pd);
			
			if(!result)
				return result;
		}
		else if(rbm.getBeginStatus().equals("1") && rbm.getEndState().equals("2")) { //热备用转冷备用
			ConvertSwitchHotToColdTS(pd);
			result = ConvertSwitchHotToCold(pd);
			setCurZxdOff(pd);
			
			if(!result)
				return result;
		}
		else if(rbm.getBeginStatus().equals("2") && rbm.getEndState().equals("1")) { //冷备用转热备用
			setCurZxdOn(pd);
			ConvertSwitchColdToHotTS(pd);
			result = ConvertSwitchColdToHot(pd);
			if(!result)
				return result;
		}else if(rbm.getEndState().equals("0")) { //热备用转运行
			ConvertSwitchHotToOnTS(pd);
			result = ConvertSwitchHotToOn(pd);
			setCurZxdOff(pd);
			if(!result)
				return result;
		}
		
		return true;
	}
	
	/*
	 * 特殊接线运行转热备用
	 */
	
	public boolean ConvertSwitchOnToHotTS(PowerDevice pd) {
		
		return true;
	}
	
	/*
	 * 运行转热备用
	 */
	
	public boolean ConvertSwitchOnToHot(PowerDevice pd) {
		List<PowerDevice> zbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(pd);
		List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(pd);
		List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(pd);
		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> gycmxList= new ArrayList<PowerDevice>();
		List<PowerDevice> zycmxList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
		
		if(zbzyckgList.size()>0){
			for(PowerDevice zbzyckg : zbzyckgList){
				zycmlkgList.addAll(getMlkgList(zbzyckg));
			}
		}
		
		if(zbdyckgList.size()>0){
			dycmxList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			
			for(PowerDevice zbdyckg : zbdyckgList){
				List<PowerDevice> tempList = getMlkgList(zbdyckg);

				for(PowerDevice temp : tempList){
					if(!dycmlkgList.contains(temp)){
						dycmlkgList.add(temp);
					}
				}
			}
		}
		
		if(zbzyckgList.size()>0){
			zycmxList = RuleExeUtil.getDeviceList(zbzyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
		}
		
		if(zbgyckgList.size()>0){
			gycmxList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			
			if(gycmxList.size()>0){
				xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
				gycmlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
			}else{
				List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(pd);
				
				for(PowerDevice zbgycdz : zbgycdzList){
					gycmxList = RuleExeUtil.getDeviceList(zbgycdz, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				}
				
				if(gycmxList.size()>0){//西双版纳内桥接线无高压侧母线
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					gycmlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}else{
					for(PowerDevice zbgycdz : zbgycdzList){
						xlkgList = RuleExeUtil.getDeviceList(zbgycdz,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,CBSystemConstants.RunTypeSwitchML, false, true, true, true,true);
						gycmlkgList = RuleExeUtil.getDeviceList(zbgycdz,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
					}
				}
			}
		}else{
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);

			if(kfList.size() > 0){
				gycmxList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.MotherLine, SystemConstants.Switch, false, true, true);
				
				if(gycmxList.size()>0){
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					
					for(PowerDevice dev : gycmxList){
						 List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
						 
						 gycmlkgList.addAll(tempList);
					}
				}else{
					gycmlkgList = RuleExeUtil.getDeviceList(kfList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}
		}
		
		
		setDrDkZyb(pd,"1");
		

		String showMessage="请选择设备的目标状态";
		List<String> defaultStatusList = new ArrayList<String>();

		List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dev : dycmlkgList){
			if(dev.getDeviceStatus().equals("1")){
				tempList.add(dev);
			}
		}

		for(PowerDevice dev : zycmlkgList){
			if(dev.getDeviceStatus().equals("1")){
				tempList.add(dev);
			}
		}
		
		if(gycmxList.size()>0){
			if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){//单母接线
				if(!RuleUtil.isTransformerNQ(pd)&&!RuleUtil.isTransformerKDNQ(pd)){
					for(PowerDevice dev : gycmlkgList){
						if(dev.getDeviceStatus().equals("1")){
							tempList.add(dev);
						}
					}
				}
			}
		}

		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getDeviceStatus().equals("1")){
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
					if(!zbdyckgList.contains(dev)&&!zbzyckgList.contains(dev)){
						tempList.add(dev);
					}
				}
			}
		}
		
		//设备初始状态
		for(PowerDevice sw : tempList){
			defaultStatusList.add("0");
		}
		
		if(tempList.size() > 0){
			EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, tempList, defaultStatusList, showMessage);
			Map tagStatusMap=dialog.getTagStatusMap();
			
			if(tagStatusMap.size() == 0)
				return false;
			
			for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
				Map.Entry<PowerDevice, String> entry = it.next();
				RuleExeUtil.deviceStatusChange(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
			}
		}
		
		for(PowerDevice dev : zbdyckgList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
		}
		
		for(PowerDevice dev : zbzyckgList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
		}
		
		for(Iterator<PowerDevice> itor = zbgyckgList.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
				itor.remove();
			}
		}
		
		for(PowerDevice dev : zbgyckgList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
		}
		
		if(gycmxList.size()>0){
			if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母接线
				for(PowerDevice dev : gycmlkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){//单母接线
				if(gycmlkgList.size()>0&&!RuleUtil.isTransformerNQ(pd)&&!RuleUtil.isTransformerKDNQ(pd)){
					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
						}
					}
				}
			}
		}
		
		if(RuleUtil.isTransformerNQ(pd)){//内桥接线
			boolean bcxlkgrby = false;
			
			for(PowerDevice dev : xlkgList){
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){
					bcxlkgrby = true;
				}
			}
			
			if(!bcxlkgrby){
				List<PowerDevice> firsthsswlist = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					
					if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
						if(dev.getDeviceStatus().equals("1")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								firsthsswlist.add(dev);
								break;
							}
						}
					}
				}
				
				for(PowerDevice dev : firsthsswlist){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
			
			for(PowerDevice dev : xlkgList){
				if(dev.getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}

			for(PowerDevice dev : gycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
		}
		
		if(RuleUtil.isTransformerKDNQ(pd)){//扩大内桥接线
			List<PowerDevice> firsthsswlist = new ArrayList<PowerDevice>();
			
			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")&&!xlkgList.contains(dev)){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							firsthsswlist.add(dev);
						}
					}
				}
			}
			
			for(PowerDevice dev : firsthsswlist){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			//合高压侧内桥开关
			for(PowerDevice dev : zycmlkgList){
				List<PowerDevice>  otherzbList = new ArrayList<PowerDevice>();
				List<PowerDevice>  mlkgList = new ArrayList<PowerDevice>();

				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);

				for(PowerDevice mx : mxList){
					
					List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice tempzb : tempzbList){
						otherzbList.add(tempzb);
					}
				}
				
				List<PowerDevice> path = RuleExeUtil.getTransformersLinkDevice(otherzbList.get(0), otherzbList.get(1));
				if(path != null) {
					for (PowerDevice device : path) {
						if(device.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
							mlkgList.add(device);
						}
					}
				}
				
				for(PowerDevice mlkg : mlkgList){
					RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				List<PowerDevice>  otherzbList = new ArrayList<PowerDevice>();
				List<PowerDevice>  mlkgList = new ArrayList<PowerDevice>();

				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);

				for(PowerDevice mx : mxList){
					
					List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice tempzb : tempzbList){
						otherzbList.add(tempzb);
					}
				}
				
				List<PowerDevice> path = RuleExeUtil.getTransformersLinkDevice(otherzbList.get(0), otherzbList.get(1));
				if(path != null) {
					for (PowerDevice device : path) {
						if(device.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
							mlkgList.add(device);
						}
					}
				}
				
				for(PowerDevice mlkg : mlkgList){
					RuleExeUtil.deviceStatusExecute(mlkg, mlkg.getDeviceStatus(), "0");
				}
			}
		}
		

		if(RuleExeUtil.isTransformerXBDY(pd)||RuleExeUtil.isTransformerXBZ(pd)){
			for(PowerDevice dev : zbdyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbzyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbgyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
		}
		
		return true;
	}
	

	/*
	 * 热备用转冷备用特殊
	 */
	
	public boolean ConvertSwitchHotToColdTS(PowerDevice pd) {
		
		return true;
	}
	
	/*
	 * 热备用转冷备用
	 */
	
	public boolean ConvertSwitchHotToCold(PowerDevice pd) {
		setDrDkZyb(pd,"2");
		
		List<PowerDevice> highswList = RuleExeUtil.getTransformerSwitchHigh(pd);
		List<PowerDevice> midswList = RuleExeUtil.getTransformerSwitchMiddle(pd);
		List<PowerDevice> lowswList = RuleExeUtil.getTransformerSwitchLow(pd);

		
		List<PowerDevice> gycmxList = new ArrayList<PowerDevice>();
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();

		if(highswList.size()>0){
			gycmxList = RuleExeUtil.getDeviceList(highswList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			
			if(gycmxList.size()>0){
				xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
				gycmlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
			}else{
				List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(pd);
				
				for(PowerDevice zbgycdz : zbgycdzList){
					gycmxList = RuleExeUtil.getDeviceList(zbgycdz, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				}
				
				if(gycmxList.size()>0){
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					gycmlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}
		}else{
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);

			if(kfList.size() > 0){
				gycmxList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.MotherLine, SystemConstants.Switch, false, true, true);
				
				if(gycmxList.size()>0){
					xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
					
					for(PowerDevice dev : gycmxList){
						 List<PowerDevice> tempList = RuleExeUtil.getDeviceList(dev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
						 
						 gycmlkgList.addAll(tempList);
					}
				}else{
					gycmlkgList = RuleExeUtil.getDeviceList(kfList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}
		}
		
		List<PowerDevice> zbdzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeZBS+","+CBSystemConstants.RunTypeKnifeZB, "", true, true, true, true);
		
		for(PowerDevice dev : lowswList){
			for(PowerDevice zbdz : zbdzList){
				if(zbdz.getPowerVoltGrade() == dev.getPowerVoltGrade()){
					RuleExeUtil.deviceStatusExecute(zbdz, zbdz.getDeviceStatus(), "1");
				}
			}
			
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
		}
		
		for(PowerDevice dev : midswList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
		}
		
		for(PowerDevice dev : highswList){
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
			}
		}
		
		if(RuleUtil.isTransformerKDNQ(pd)||RuleUtil.isTransformerNQ(pd)){
			// 拉开主变刀闸
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
			for (PowerDevice kf : kfList) {
				RuleExeUtil.deviceStatusExecute(kf, kf.getDeviceStatus(), "1");
			}
			
			List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
			
			if(xlkgList.size()==0){
				for (PowerDevice kf : kfList) {
					xlkgList = RuleExeUtil.getDeviceList(kf,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, true, true,true);
				}
			}
			
			if(gycmlkgList.size()==0){
				for (PowerDevice kf : kfList) {
					gycmlkgList = RuleExeUtil.getDeviceList(kf,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, true, true,true);
				}
			}
			
			for(PowerDevice dev : xlkgList){
				if(dev.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
		}
		
		if(RuleExeUtil.isTransformerXBDY(pd)||RuleExeUtil.isTransformerXBZ(pd)){
			for(PowerDevice dev : highswList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "2");
			}
		}
		
		return true;
	}
	
	/*
	 * 冷备用转热备用
	 */
	
	public boolean ConvertSwitchColdToHotTS(PowerDevice pd) {
		return true;
	}
	
	/*
	 * 冷备用转热备用
	 */
	
	public boolean ConvertSwitchColdToHot(PowerDevice pd) {
		List<PowerDevice> zbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(pd);
		List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(pd);
		List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(pd);
		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmxList = new ArrayList<PowerDevice>();
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> gycmxList = new ArrayList<PowerDevice>();

		if(zbgyckgList.size()>0){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			gycmlkgList.addAll(kgList);
		}else{
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			gycmlkgList.addAll(kgList);
		}
		
		if(zbzyckgList.size()>0){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbzyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			zycmlkgList.addAll(kgList);
		}
		
		if(zbdyckgList.size()>0){
			dycmxList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbdyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			
			for(Iterator<PowerDevice> itor = kgList.iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				
				if(tempmxList.size()<2){
					itor.remove();
				}
			}
			
			dycmlkgList.addAll(kgList);
		}
		
		if(zbgyckgList.size()>0){
			gycmxList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false,  true, true, true);
			
			if(gycmxList.size()>0){
				xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
			}
		}
		
		if(RuleUtil.isTransformerNQ(pd)){
			for(PowerDevice dev : gycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbgyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			// 合上主变刀闸
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
			for (PowerDevice kf : kfList) {
				RuleExeUtil.deviceStatusExecute(kf, kf.getDeviceStatus(), "0");
			}
			
			
			for(PowerDevice dev : zbzyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbdyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			setDrDkZyb(pd,"1");
			
		}
		
		if(gycmxList.size()>0){
			if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母接线
				if(pd.getPowerVoltGrade() == 220){
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					for(PowerDevice dev : zbzyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					List<PowerDevice> zbdzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeZBS+","+CBSystemConstants.RunTypeKnifeZB, "", true, true, true, true);
					
					for(PowerDevice dev : zbdyckgList){
						for(PowerDevice zbdz : zbdzList){
							if(zbdz.getPowerVoltGrade() == dev.getPowerVoltGrade()){
								RuleExeUtil.deviceStatusExecute(zbdz, zbdz.getDeviceStatus(), "0");
							}
						}
						
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					setDrDkZyb(pd, "1");
				}else if(pd.getPowerVoltGrade() == 110){
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					for(PowerDevice dev : zbzyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					for(PowerDevice dev : zbdyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){//单母接线
				for(PowerDevice dev : gycmlkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(dev.getDeviceStatus().equals("2")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
				
				for(PowerDevice dev : zbdyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){//3/2接线
				for(PowerDevice dev : zbgyckgList){
					if(dev.getDeviceStatus().equals("2")){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
				}
				
				for(PowerDevice dev : zbzyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
				
				for(PowerDevice dev : zbdyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}
		}
		
		if(RuleExeUtil.isTransformerXBDY(pd)||RuleExeUtil.isTransformerXBZ(pd)){
			for(PowerDevice dev : zbgyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbzyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(CBSystemConstants.LineTagStatus.containsKey(dev)){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), CBSystemConstants.LineTagStatus.get(dev));
				}else{
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}			
			}
		}
		
		return true;
	}
	
	/*
	 * 热备用转运行特殊
	 */
	
	public boolean ConvertSwitchHotToOnTS(PowerDevice pd) {
		
		return true;
	}
	
	
	/*
	 * 热备用转运行
	 */
	
	public boolean ConvertSwitchHotToOn(PowerDevice pd) {
		List<PowerDevice> zbgyckgList =  RuleExeUtil.getTransformerSwitchHigh(pd);
		List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(pd);
		List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(pd);
		List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> zycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> gycmxList = new ArrayList<PowerDevice>();
		
		if(zbgyckgList.size()>0){
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			gycmlkgList.addAll(kgList);
		}else{
			List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(kfList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
			gycmlkgList.addAll(kgList);
		}
		
		if(zbzyckgList.size()>0){
			List<PowerDevice> kgList = getMlkgList(zbzyckgList.get(0));
			zycmlkgList.addAll(kgList);
		}
		
		if(zbdyckgList.size()>0){
			List<PowerDevice> kgList = getMlkgList(zbdyckgList.get(0));
			
			dycmlkgList.addAll(kgList);
		}
		
		if(zbgyckgList.size()>0){
			gycmxList = RuleExeUtil.getDeviceList(zbgyckgList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false,  false, true, true);
			
			if(gycmxList.size()>0){
				xlkgList = RuleExeUtil.getDeviceList(gycmxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL,"", false, true, true, true,true);
			}
		}
		
		if(gycmxList.size()>0){
			if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母接线
				if(pd.getPowerVoltGrade() == 220){
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbzyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbdyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					String showMessage="请选择设备的目标状态";
 					List<String> defaultStatusList = new ArrayList<String>();

 					List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
 					
 					tempList.addAll(dycmlkgList);
 					tempList.addAll(zycmlkgList);
 					
 					//设备初始状态
 					for(PowerDevice sw : tempList){
 						defaultStatusList.add("0");
 					}
 					
 					EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, tempList, defaultStatusList, showMessage);
 					Map tagStatusMap=dialog.getTagStatusMap();
 					
 					if(tagStatusMap.size() == 0)
 						return false;
 					
 					for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
 						Map.Entry<PowerDevice, String> entry = it.next();
 						RuleExeUtil.deviceStatusChange(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
 					}
					
					setZyb(pd,zbdyckgList,"0");
				}else if(pd.getPowerVoltGrade() == 110){
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbzyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbdyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					

					for(PowerDevice dev : dycmlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}

					for(PowerDevice dev : zycmlkgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
					}
					
					String showMessage="请选择设备的目标状态";
 					List<String> defaultStatusList = new ArrayList<String>();

 					List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
 					
 					tempList.addAll(dycmlkgList);
 					tempList.addAll(zycmlkgList);
 					tempList.addAll(gycmlkgList);

 					//设备初始状态
 					for(PowerDevice sw : tempList){
 						defaultStatusList.add("1");
 					}
 					
 					EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, tempList, defaultStatusList, showMessage);
 					Map tagStatusMap=dialog.getTagStatusMap();
 					
 					if(tagStatusMap.size() == 0)
 						return false;
 					
 					for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
 						Map.Entry<PowerDevice, String> entry = it.next();
 						RuleExeUtil.deviceStatusChange(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
 						
 					}
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)
					&&!RuleUtil.isTransformerNQ(pd)){//单母接线
				for(PowerDevice dev : zbgyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
				
				for(PowerDevice dev : zbzyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
				
				for(PowerDevice dev : zbdyckgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
				
				String showMessage="请选择设备的目标状态";
				List<String> defaultStatusList = new ArrayList<String>();

				List<PowerDevice>  tempList = new ArrayList<PowerDevice>();
				
				tempList.addAll(dycmlkgList);
				tempList.addAll(zycmlkgList);
				tempList.addAll(gycmlkgList);

				//设备初始状态
				for(PowerDevice sw : tempList){
					defaultStatusList.add("1");
				}
				
				if(tempList.size()>0){
					EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, tempList, defaultStatusList, showMessage);
					Map tagStatusMap=dialog.getTagStatusMap();
					
					if(tagStatusMap.size() == 0)
						return false;
					
					for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
						Map.Entry<PowerDevice, String> entry = it.next();
						RuleExeUtil.deviceStatusChange(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
						
					}
				}
			}else if(gycmxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){//3/2接线
				if(pd.getPowerVoltGrade() == 500){
					RuleExeUtil.swapRunmodelThreeTwoDeviceList(zbgyckgList);
					Collections.reverse(zbgyckgList);
					
					for(PowerDevice dev : zbgyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbzyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
					
					for(PowerDevice dev : zbdyckgList){
						RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
					}
				}
			}
		}
		
		if(RuleUtil.isTransformerNQ(pd)){
			for(PowerDevice dev : gycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : zbzyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : zbdyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : dycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			for(PowerDevice dev : zycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : zbgyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : gycmlkgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
			}
			
			setZyb(pd,zbdyckgList,"0");
		}
		
		if(RuleExeUtil.isTransformerXBDY(pd)||RuleExeUtil.isTransformerXBZ(pd)){
			for(PowerDevice dev : zbgyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : zbzyckgList){
				RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(CBSystemConstants.LineTagStatus.containsKey(dev)){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), CBSystemConstants.LineTagStatus.get(dev));
				}else{
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
				}
			}
			
			List<PowerDevice> tempList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == 10){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						tempList.add(dev);
					}
				}
			}
			
			if(tempList.size() == 4){
				swapDeviceListNum(tempList);
				
				EquipRadioChoose erc = new EquipRadioChoose(SystemConstants.getMainFrame(), true, tempList,  "请选择需要断开的分段断路器：",true);
				PowerDevice kgChangeOn = erc.getChooseEquip();
				
				if(erc.isCancel()||kgChangeOn== null)
					return false;
				
				RuleExeUtil.deviceStatusExecute(kgChangeOn, kgChangeOn.getDeviceStatus(), "1");
			}else{
				for(PowerDevice dev : dycmlkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
				
				for(PowerDevice dev : zycmlkgList){
					RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "1");
				}
			}
		}
		
		return true;
	}
	

	/**
	 * 设备排序，先比较电压等级（低的在前），再比较设备类型，再比较设备编号
	 * 
	 * @param deviceList
	 */
	public void swapDeviceListNum(List<PowerDevice> deviceList) {
		Collections.sort(deviceList, new Comparator<PowerDevice>() {
			public int compare(PowerDevice pd1, PowerDevice pd2) {
				return CZPService.getService().getDevNum(pd1.getPowerDeviceName()).compareTo(CZPService.getService().getDevNum(pd2.getPowerDeviceName()));
			}
		});
	}
	
	public boolean setOtherZxdOn(PowerDevice pd) {
		List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade() && !dev.getPowerDeviceID().equals(pd.getPowerDeviceID())){
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					otherzbList.add(dev);
				}
			}
		}
		
		if(pd.getPowerVoltGrade() == 220){
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(gdList);
				for(PowerDevice gd : gdList) {
					RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
				}
			}
		}
		
		List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		RuleExeUtil.swapLowDeviceList(gdList);
		for(PowerDevice gd : gdList) {
			RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
		}
		
		return true;
	}
	
	public boolean setCurZxdOff(PowerDevice pd) {
		List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		RuleExeUtil.swapLowDeviceList(gdList);
		for(PowerDevice gd : gdList) {
			RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "1");
		}
		
		return true;
	}
	
	private boolean setCurZxdOn(PowerDevice pd) {
		List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		RuleExeUtil.swapLowDeviceList(gdList);
		for(PowerDevice gd : gdList) {
			RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
		}
		
		return true;
	}
	
	private boolean setChooseOff(PowerDevice pd) {
		List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		
		HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

		for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
			PowerDevice dev = it.next();
			
			if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbList.add(dev);
				}
			}
		}
		
		List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
		EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, zbList, "请选择需要退出中性点的主变：");
		chooseEquips=ecc.getChooseEquip();
		
		if(chooseEquips.size()>0){
			for(PowerDevice dev : chooseEquips){
				List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				
				for(PowerDevice gd : gdList) {
					RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "1");
				}
			}
		}
		
		return true;
	}
	
	public boolean setZyb(PowerDevice pd, List<PowerDevice> zbdyckgList,String endstate) {
		List<PowerDevice> zybdzList = new ArrayList<PowerDevice>();
		List<PowerDevice> zybswList = new ArrayList<PowerDevice>();

		for(PowerDevice dysw:zbdyckgList){
			if(dysw.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				List<PowerDevice>  mxList = RuleExeUtil.getDeviceList(dysw, SystemConstants.MotherLine, SystemConstants.PowerTransformer,
						true, true, true);
				if(mxList.size()>0){
					List<PowerDevice>  qtkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);
					
					List<PowerDevice>  qtdzList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeKnifeQT, "", false, true, true, true);
					
					for(PowerDevice dev : qtkgList){
						if(dev.getPowerDeviceName().contains("站用变")){
							zybswList.add(dev);
						}
					}
					
					for(PowerDevice dev : qtdzList){
						if(dev.getPowerDeviceName().contains("站用变")){
							zybdzList.add(dev);
						}
					}
				}
			}
		}
		
		for(PowerDevice dev : zybdzList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), endstate);
		}
		
		for(PowerDevice dev : zybswList){
			RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), endstate);
		}
		
		return true;
	}
	
	public boolean setDrDkZyb(PowerDevice pd,String endstate) {
		List<PowerDevice> zbdyckgList =	 RuleExeUtil.getTransformerSwitchLow(pd);

		List<PowerDevice> drdkswList = new ArrayList<PowerDevice>();
		
		List<PowerDevice> zybdzList = new ArrayList<PowerDevice>();
		
		for(PowerDevice dysw:zbdyckgList){
			if(dysw.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				List<PowerDevice>  mxList = RuleExeUtil.getDeviceList(dysw, SystemConstants.MotherLine, SystemConstants.PowerTransformer,
						true, true, true);
				if(mxList.size()>0){
					drdkswList.addAll(RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK, "", false, true, true, true));
					
					List<PowerDevice>  qtkgList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);
					
					for(PowerDevice dev : qtkgList){
						List<PowerDevice>  qtdzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
						
						if(dev.getPowerDeviceName().contains("站用变")){
							drdkswList.add(dev);
						}
						
						for(PowerDevice qtdz : qtdzList){
							if(qtdz.getPowerDeviceName().contains("站用变")){
								zybdzList.add(qtdz);
							}
						}
					}
				}
			}
		}
		
		for(PowerDevice zbdyckg : zbdyckgList){
			List<PowerDevice>  dycmlkgList = getMlkgList(zbdyckg);
			
			if(dycmlkgList.size()==0&&pd.getPowerVoltGrade()==220){
				for(PowerDevice dtdksw:drdkswList){
					RuleExeUtil.deviceStatusExecute(dtdksw, dtdksw.getDeviceStatus(), endstate);
				}
				
				for(PowerDevice zybdz : zybdzList){
					RuleExeUtil.deviceStatusExecute(zybdz, zybdz.getDeviceStatus(), "1");
				}
			}
		}
		
		return true;
	}
	
	
	public List<PowerDevice>  getMlkgList(PowerDevice pd){
		List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> kgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, CBSystemConstants.RunTypeSideMother, false, true, false, true);
		
		for(Iterator<PowerDevice> itor = kgList.iterator();itor.hasNext();){
			PowerDevice dev = itor.next();
			
			if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				List<PowerDevice> tempmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
				
				if(tempmxList.size()<2){
					itor.remove();
				}else{
					for(PowerDevice tempmx : tempmxList){
						List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(tempmx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeKnifeQT, false, true, true, true);
						
						if(tempzbList.size() == 0){
							itor.remove();
						}
					}
				}
			}
		}
		
		if(kgList.size()==0){
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
					if(dev.getDeviceStatus().equals("1")){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							kgList.add(dev);
						}
					}
				}
			}
		}
		
		mlkgList.addAll(kgList);
	
		return mlkgList;
	}
	
}
