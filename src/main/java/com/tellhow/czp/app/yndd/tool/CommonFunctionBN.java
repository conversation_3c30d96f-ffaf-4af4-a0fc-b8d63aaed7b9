package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.view.StringCheckChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

public class CommonFunctionBN {

	//地线
	public static List<PowerDevice> groundWireList = new ArrayList<PowerDevice>();

	//合环
	public static List<PowerDevice> closedLoopList = new ArrayList<PowerDevice>();

	//充电
	public static List<PowerDevice> chargeDeviceList = new ArrayList<PowerDevice>();

	//内桥主变停电充电开关
	public static List<PowerDevice> chargeDeviceByNqZbTdList = new ArrayList<PowerDevice>();

	//内桥主变复电充电开关
	public static List<PowerDevice> chargeDeviceByNqZbFdList = new ArrayList<PowerDevice>();

	public static String getSwitchOffContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";

		if(ifSwitchControlBN(dev)){
			if(station.getDeviceType().equals(SystemConstants.PowerFactory)){
				replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "版纳地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@断开"+CZPService.getService().getDevName(dev)+"/r/n";
		}

		return replaceStr;
	}

	public static String getSwitchOnContent(PowerDevice dev,String stationName,PowerDevice station){
		String replaceStr = "";

		if(ifSwitchControlBN(dev)){
			if(station.getDeviceType().equals(SystemConstants.PowerFactory)){
				replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
			}else{
				replaceStr = "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}else{
			replaceStr = stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
		}

		return replaceStr;
	}

	public static List<PowerDevice> getTransformerKnife(PowerDevice zb,PowerDevice zbkg){
		List<PowerDevice> dztagList = new ArrayList<PowerDevice>();

		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zbkg, SystemConstants.SwitchSeparate);
		List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(zb, zbkg, SystemConstants.PowerTransformer, "", true, true);

		for(PowerDevice path : pathList){
			if(path.getDeviceType().equals(SystemConstants.SwitchSeparate)){
				if(!dzList.contains(path)){
					dztagList.add(path);
				}
			}
		}

		return dztagList;
	}

	public static String getKnifeOffContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";

		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
				if(CommonFunctionBN.ifSwitchSeparateControlBN(dev)){
					replaceStr += "版纳地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOffCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@拉开"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String getKnifeOnContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";

		for(PowerDevice dev : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
				if(CommonFunctionBN.ifSwitchSeparateControlBN(dev)){
					replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					replaceStr += getKnifeOnCheckContent(dzList, stationName);
				}else{
					replaceStr += stationName+"@合上"+CZPService.getService().getDevName(dev)+"/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String getKnifeOnCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";

		boolean ismldz = false;

		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);

			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
				break;
			}
		}

		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
		}

		for(PowerDevice dz : dzList){
			if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处合上位置/r/n";
			}
		}

		return replaceStr;
	}

	public static String getKnifeOffCheckContent(List<PowerDevice> dzList,String stationName){
		String replaceStr = "";

		boolean ismldz = false;

		for(PowerDevice dev : dzList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", true, true, true, true);

			if(mlkgList.size() > 0){
				ismldz = true;
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				break;
			}
		}

		if(!ismldz){
			dzList = RuleExeUtil.sortByMXC(dzList);
			Collections.reverse(dzList);
		}

		for(PowerDevice dz : dzList){
//			if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
				replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
//			}
		}

		return replaceStr;
	}

	public static List<Map<String, String>> getStationLineList(PowerDevice curDev){
		List<Map<String, String>> stationLineList = new ArrayList<Map<String,String>>();

		String sql = "SELECT ID,LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,PTDISCONNECTOR_NAME "
				+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND "
						+ "WHERE ID = '"+curDev.getPowerDeviceID()+"')";

		stationLineList = DBManager.queryForList(sql);

		if(stationLineList.size() == 0){
			String lineName = CZPService.getService().getDevName(curDev);

			sql = "SELECT ID,LINE_NAME,UNIT,LOWERUNIT,OPERATION_KIND,SWITCH_NAME,DISCONNECTOR_NAME,GROUNDDISCONNECTOR_NAME,ENDPOINT_KIND,PTDISCONNECTOR_NAME "
					+ "FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_NAME = '"+lineName+"'";

			stationLineList = DBManager.queryForList(sql);
		}

		return stationLineList;
	}

	public static String getHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";

		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(dev.getPowerVoltGrade() > 35){
				if(ifSwitchControlBN(dev)) {
                    replaceStr += ddname + "@遥控用" + stationName + CZPService.getService().getDevName(dev) + "同期合环/r/n";
                } else {
                    replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"同期合环/r/n";
                }
			}else{
				if(ifSwitchControlBN(dev)) {
                    replaceStr += ddname + "@遥控用" + stationName + CZPService.getService().getDevName(dev) + "合环/r/n";
                } else {
                    replaceStr += stationName+"@用"+CZPService.getService().getDevName(dev)+"合环/r/n";
                }
			}
		}

		return replaceStr;
	}

	public static String getHhContentNew(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";

		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(dev.getPowerVoltGrade() > 35){
				if(stationName.contains("电站")){
					replaceStr += "用"+CZPService.getService().getDevName(dev)+"同期合环";
				}else{
					replaceStr += "遥控用"+stationName+CZPService.getService().getDevName(dev)+"同期合环";
				}
			}else{
				if(stationName.contains("电站")){
					replaceStr += "用"+CZPService.getService().getDevName(dev)+"合环";
				}else{
					replaceStr += "遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环";
				}
			}
		}

		return replaceStr;
	}

	//开关可控
	public static boolean ifSwitchControlBN(PowerDevice dev){
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";

			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);

			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));

				if(ifcontrol.equals("false")||ifcontrol.equals("")){
					return Boolean.parseBoolean(ifcontrol);
				}
			}
		}

		return true;
	}

	//刀闸是否可控
	public static boolean ifSwitchSeparateControlBN(PowerDevice dev){
		if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";

			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);

			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));

				if(ifcontrol.equals("false")||ifcontrol.equals("")){
					return Boolean.parseBoolean(ifcontrol);
				}
			}
		}else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
			String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";

			List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);

			for(Map<String,String> ifcontrolMap : ifcontrolList){
				String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));

				if(ifcontrol.equals("false")||ifcontrol.equals("")){
					return Boolean.parseBoolean(ifcontrol);
				}
			}
		}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

			for(PowerDevice dz : dzList){
				String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dz.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";

				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);

				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));

					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}
		}

		return true;
	}

	/**
	 * @param dev 设备
	 * @return 判断设备所属的自设备是否可控
	 */
	public static String ifDeviceControlBN(PowerDevice dev) {
		String isControl = "全部可控";
		String devType = dev.getDeviceType();
        if (devType.equals(SystemConstants.Switch)) {
			if (ifSwitchControlBN(dev) && ifSwitchControlBN(dev)) {
				isControl = "全部可控";
			} else if (ifSwitchControlBN(dev) || ifSwitchSeparateControlBN(dev)) {
				isControl = "部分可控";
			} else isControl = "全不可控";
		} else if (devType.equals(SystemConstants.SwitchSeparate)
				|| devType.equals(SystemConstants.SwitchFlowGroundLine)) {
			if (ifSwitchSeparateControlBN(dev)) {
				isControl = "全部可控";
			} else isControl = "全不可控";
		} else if (devType.equals(SystemConstants.MotherLine)) {
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);
			isControl = checkDevicesControlStatus(swList);
		} else if (devType.equals(SystemConstants.InOutLine)) {
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "",
					false, true, true, true);
			isControl = checkDevicesControlStatus(swList);
		} else if (devType.equals(SystemConstants.PowerTransformer)) {
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
			// swList等于三侧开关的合集
			List<PowerDevice> swList = new ArrayList<PowerDevice>();
			swList.addAll(zbdyckgList);
			swList.addAll(zbzyckgList);
			swList.addAll(zbgyckgList);
			isControl = checkDevicesControlStatus(swList);
		}
		return isControl;
	}

	/**
	 * 判断设备集合的可控状态
	 * @param swList 设备集合
	 * @return 可控状态：0-全部可控，1-部分可控，2-全不可控
	 */
	public static String checkDevicesControlStatus(List<PowerDevice> swList) {
		// 如果设备集合为空，直接返回全不可控
		if (swList == null || swList.isEmpty()) {
			return "全不可控"; // 全不可控
		}

		int fullyControlCount = 0;    // 全部可控的设备数量
		int nonControlCount = 0;      // 全不可控的设备数量

		// 遍历设备集合
		for (PowerDevice sw : swList) {
			boolean controlResult1 = ifSwitchControlBN(sw);
			boolean controlResult2 = ifSwitchSeparateControlBN(sw);

			if (controlResult1 && controlResult2) {
				// 两个方法都返回true，该设备全部可控
				fullyControlCount++;
			} else if (!controlResult1 && !controlResult2) {
				// 两个方法都返回false，该设备全不可控
				nonControlCount++;
			}
			// 其他情况为部分可控，不需要特别计数
		}

		// 判断最终的可控状态
		if (fullyControlCount == swList.size()) {
			return "全部可控"; // 所有设备都是全部可控
		} else if (nonControlCount == swList.size()) {
			return "全不可控"; // 所有设备都是全不可控
		} else {
			return "部分可控"; // 部分可控
		}
	}

	public static String getMotherLineTdContent(List<PowerDevice> mxList,String stationName){
		String replaceStr = "";

		boolean ifMotherLineTd = true;

		for(PowerDevice dev : mxList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, SystemConstants.PowerTransformer, false, true, true, true);
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC, SystemConstants.PowerTransformer, false, true, true, true);

			if(mlkgList.size() == 0){
				for(PowerDevice zbkg : zbkgList){
					if(zbkg.getDeviceStatus().equals("0")){
						ifMotherLineTd = false;
					}
				}
			}else{
				for(PowerDevice mlkg : mlkgList){
					if(mlkg.getDeviceStatus().equals("0")){
						ifMotherLineTd = false;
					}
				}

				for(PowerDevice zbkg : zbkgList){
					if(zbkg.getDeviceStatus().equals("0")){
						ifMotherLineTd = false;
					}
				}
			}
		}

		if(ifMotherLineTd){
			String mxName = "";

			boolean isHaveXlkg = false;

			for(PowerDevice dev : mxList){
				List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL, SystemConstants.PowerTransformer, false, true, true, true);

				if(xlkgList.size() > 0){
					isHaveXlkg = true;
				}
				mxName = CZPService.getService().getDevName(dev);
			}

			if(!mxName.equals("")&&isHaveXlkg){
				replaceStr += "版纳配调@确认"+stationName+mxName+"配调管辖10kV出线运行方式已调整完毕，具备停电条件/r/n";
			}

			for(PowerDevice dev : mxList){
				List<PowerDevice> drqList = RuleExeUtil.getDeviceList(dev, SystemConstants.ElecCapacity, SystemConstants.PowerTransformer, true, true, true);

				for(PowerDevice drqkg : drqList){
					replaceStr += "版纳地调@确认"+stationName+CZPService.getService().getDevName(drqkg)+"处热备用/r/n";
				}

				List<PowerDevice> dkqList = RuleExeUtil.getDeviceList(dev, SystemConstants.ElecShock, SystemConstants.PowerTransformer, true, true, true);

				for(PowerDevice drqkg : dkqList){
					replaceStr += "版纳地调@确认"+stationName+CZPService.getService().getDevName(drqkg)+"处热备用/r/n";
				}
				
				/*List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchQT+","+CBSystemConstants.RunTypeSwitchZYB, SystemConstants.PowerTransformer, false, true, true, true);
				List<PowerDevice> zybdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT, SystemConstants.PowerTransformer, false, true, true, true);
				List<PowerDevice> zybList = new ArrayList<PowerDevice>();
				
				zybList.addAll(zybkgList);
				zybList.addAll(zybdzList);
				
				for(PowerDevice zyb : zybList){
					if(zyb.getPowerDeviceName().contains("站用变")){
						String devName = CZPService.getService().getDevName(zyb);
						
						if(devName.contains("站用变")){
							String zybName = devName.substring(0, devName.indexOf("站用变")+3);
							replaceStr += stationName+"@确认"+zybName+"具备停电条件/r/n";
						}
						break;
					}
				}*/
			}
		}

		return replaceStr;
	}

	public static boolean isMotherLineFd(List<PowerDevice> mxList){
		boolean ifMotherLineFd = false;

		for(PowerDevice dev : mxList){
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML, SystemConstants.PowerTransformer, false, true, true, true);

			if(mlkgList.size() == 0){
				ifMotherLineFd = true;
			}else{
				for(PowerDevice mlkg : mlkgList){
					if(!RuleExeUtil.getDeviceBeginStatus(mlkg).equals("0")){
						ifMotherLineFd = true;
					}
				}
			}
		}

		return ifMotherLineFd;
	}

	public static String getSequenceConfirmTdContent(List<PowerDevice> swList,String stationName){
		String replaceStr = "";

		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
					Collections.reverse(dzList);
				}
			}

			for(PowerDevice dz : dzList){
				if(!dz.getPowerDeviceName().contains("PT")){
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(sw)+"处冷备用/r/n";
					}else{
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
					}
				}
			}
		}

		return replaceStr;
	}

	public static String getSequenceConfirmFdContent(List<PowerDevice> swList,String stationName){
		String replaceStr = "";

		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
					Collections.reverse(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
				}
			}

			for(PowerDevice dz : dzList){
				if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")&&!dz.getPowerDeviceName().contains("PT")){
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(sw)+"处热备用/r/n";
					}else{
						replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处合上位置/r/n";
					}
				}
			}
		}

		return replaceStr;
	}

	public static String getSequenceConfirmTdContent(PowerDevice dz,String stationName){
		String replaceStr = "";

		if(!dz.getPowerDeviceName().contains("PT")){
			replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处拉开位置/r/n";
		}

		return replaceStr;
	}

	public static String getSequenceConfirmFdContent(PowerDevice dz,String stationName){
		String replaceStr = "";

		if(!dz.getPowerDeviceName().contains("PT")){
			replaceStr += stationName+"@确认"+CZPService.getService().getDevName(dz)+"处合上位置/r/n";
		}

		return replaceStr;
	}

	/*
	 * 电容器电抗器断路器特殊判断
	 */
	public static String getSwitchConfirmContent(PowerDevice dev,String stationName){
		String replaceStr = "";

		if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){
			String status = RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev);
			status = RuleExeUtil.getStatusNew(dev.getDeviceType(), status);
			replaceStr += "确认"+CZPService.getService().getDevName(dev)+"已处"+status+"/r/n";
		}else{
			replaceStr += "确认"+CZPService.getService().getDevName(dev)+"已处热备用/r/n";
		}

		return replaceStr;
	}

	public static String getTcSwitchProtect(PowerDevice dev,String stationName){
		String replaceStr = "";
		String[] stationNameIn35kVArr = {"110kV金象变","110kV嘎栋变"};
		String[] stationNameIn10kVArr = {"110kV勐腊变","110kV金象变","110kV勐仑变","110kV辉凰变","110kV嘎栋变"};

		if(dev.getPowerVoltGrade() == 35){
			for(String stName : stationNameIn35kVArr){
				if(stationName.equals(stName)){
					replaceStr += stationName+"@退出"+CZPService.getService().getDevName(dev)+"保护/r/n";
				}
			}
		}else if(dev.getPowerVoltGrade() == 10){
			for(String stName : stationNameIn10kVArr){
				if(stationName.equals(stName)){
					replaceStr += stationName+"@退出"+CZPService.getService().getDevName(dev)+"保护/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String getTrSwitchProtect(PowerDevice dev,String stationName){
		String replaceStr = "";
		String[] stationNameIn35kVArr = {"110kV金象变","110kV嘎栋变"};
		String[] stationNameIn10kVArr = {"110kV勐腊变","110kV金象变","110kV勐仑变","110kV辉凰变","110kV嘎栋变"};

		if(dev.getPowerVoltGrade() == 35){
			for(String stName : stationNameIn35kVArr){
				if(stationName.equals(stName)){
					replaceStr += stationName+"@投入"+CZPService.getService().getDevName(dev)+"保护/r/n";
				}
			}
		}else if(dev.getPowerVoltGrade() == 10){
			for(String stName : stationNameIn10kVArr){
				if(stationName.equals(stName)){
					replaceStr += stationName+"@投入"+CZPService.getService().getDevName(dev)+"保护/r/n";
				}
			}
		}

		return replaceStr;
	}

	public static String getBztResult(List<String> voltList,String kind){
		String result = "";

		List<Map<String, String>> chooseEquips = new ArrayList<Map<String, String>>();
		StringCheckChoose ecc = new StringCheckChoose(SystemConstants.getMainFrame(), true, voltList, "请选择需要"+kind+"的备自投：");
		chooseEquips = ecc.getChooseEquip();

		if(chooseEquips.size()>0){
			for(Map<String, String> chooseEquip : chooseEquips){
				String bzt = chooseEquip.get("备自投");

				result += kind+bzt+"/r/n";
			}
		}

		return result;
	}

	public static PowerDevice getDeviceInfoList(String station, String word) {
		PowerDevice result = new PowerDevice();


		String kuohao = "";

		word = word.trim();
		station = station.trim();
		List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();

		try{
			word = word.replaceAll(" ","");
			word = word.replace("(", "（").replace(")", "）");

			if(word.contains("（")&&word.contains("）")){
				if(word.indexOf("（")<word.indexOf("）")){
					kuohao = word.substring(word.indexOf("（"),word.indexOf("）")+1);
				}
			}

			if(word.contains("《")&&word.contains("》")){
				if(word.indexOf("《")<word.indexOf("》")){
					kuohao = word.substring(word.indexOf("《"),word.indexOf("》")+1);
				}
			}

			word = word.replace(kuohao, "");

			/*
			 * 解析指令的设备、状态
			 */
			String stationId = "";

			/*
			 * 得到厂站的实体
			 */

			//厂站名称校验
			for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();){
				PowerDevice st = it.next();

				String modelDevName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(st));
				station = StringUtils.killVoltInDevName(station);

				if(modelDevName.equals(station)) {
					stationId = st.getPowerDeviceID();

					if (CBSystemConstants.getStationPowerDevices(stationId) == null) {
						CreatePowerStationToplogy.loadFacEquip(stationId);
					}

					HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationId);

					if(devMap != null){
						break;
					}else{
						continue;
					}
				}
			}

			System.out.println("厂站ID："+stationId);

			/*
			 * 加载厂站缓存
			 */
			if (CBSystemConstants.getStationPowerDevices(stationId) == null) {
				CreatePowerStationToplogy.loadFacEquip(stationId);
			}

			HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationId);

			if(devMap != null){
				String deviceName = word;

				String equipTypeFlag = "";
				String equipTypeName = "";
				String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
						SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchSeparate,
						SystemConstants.SwitchSeparate,SystemConstants.Switch, SystemConstants.SwitchSeparate,
						SystemConstants.SwitchSeparate, SystemConstants.Switch,
						SystemConstants.Switch,SystemConstants.VolsbTransformer, SystemConstants.MotherLine,SystemConstants.MotherLine,
						SystemConstants.MotherLine, SystemConstants.InOutLine,SystemConstants.PowerTransformer,
						SystemConstants.PowerTransformer, SystemConstants.ElecShock,
						SystemConstants.ElecCapacity ,SystemConstants.PowerTransformer,SystemConstants.PowerTransformer,SystemConstants.PowerGenerator};
				String[] key = new String[] { "接地刀闸","接地开关", "地刀", "隔离开关", "隔离刀闸","小车开关", "小车", "刀闸", "断路器",
						"开关","PT", "母线", "母" , "M", "线", "T","主变", "电抗器", "电容器","#变","发变组","发电机"};
				for (int i = 0; i < key.length; i++) {
					if (deviceName.lastIndexOf(key[i]) >= 0) {
						equipTypeFlag = type[i];
						equipTypeName = key[i];
						break;
					}
				}

				String devNum = CZPService.getService().getDevNum(deviceName);

				String volStr = "";

				if(deviceName.toLowerCase().split("kv").length >= 3){
					volStr = deviceName.toLowerCase().substring(deviceName.toLowerCase().indexOf("kv")+2);
				}else{
					volStr = deviceName;
				}

				//获取电压等级
				String volt = StringUtils.getVoltInDevName(volStr);

				PowerDevice pd = new PowerDevice();

				for (PowerDevice dev : devMap.values()) {
					if (!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
						continue;
					if (!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
						continue;
					if(dev.getPowerDeviceName().contains("A相")||dev.getPowerDeviceName().contains("B相")||dev.getPowerDeviceName().contains("C相"))
						continue;
					if(dev.getPowerDeviceName().contains("压板"))
						continue;

					String name = CZPService.getService().getDevName(dev);

					if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
				    	name = StringUtils.killVoltInDevName(name).trim();
				    	deviceName = StringUtils.killVoltInDevName(deviceName).trim();

				    	name = name.replace("母线", "").replace("母", "").replace("M", "");
				    	deviceName = deviceName.replace("母线", "").replace("母", "").replace("M", "");

						if (name.equals(deviceName)) {
							if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
								pd = dev;
								break;
							}
							else
								pd = dev;
						}else if (deviceName.contains(name)) {
							pd = dev;
						}
					}else{
						String num = CZPService.getService().getDevNum(name);

						if(!devNum.equals("")){
							if (num.equals(devNum)) {
								if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
									pd = dev;
									break;
								}
								else
									pd = dev;
							}
						}else{
							if (name.equals(deviceName)) {
								if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
									pd = dev;
									break;
								}
								else
									pd = dev;
							}
						}
					}
				}

				if(!pd.getPowerDeviceID().equals("")){
					result = pd;
				}
			}
		}catch(Exception e){
			e.printStackTrace();
		}

		return result;
	}

	public static String get3KnifeContent(List<PowerDevice> mlkgList,String stationName,String operation){
		String replaceStr = "";

		for(PowerDevice dev : mlkgList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			List<PowerDevice> alldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.MotherLine, true, true, false);

			for(PowerDevice dz : alldzList){
				if(!dzList.contains(dz)&&!dz.getPowerDeviceName().endsWith("1")){
					if(operation.equals("拉开")){
						List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
						dzTempList.add(dz);
						replaceStr = getKnifeOffContent(dzTempList,stationName);
					}else{
						List<PowerDevice> dzTempList = new ArrayList<PowerDevice>();
						dzTempList.add(dz);
						replaceStr = getKnifeOnContent(dzTempList,stationName);
					}
					break;
				}
			}
		}

		return replaceStr;
	}

	public static String getCdOrHhContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";

		if(chargeDeviceList.contains(dev)){
			PowerDevice curDev = CBSystemConstants.getCurRBM().getPd();

			if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
				replaceStr = getCdContent(dev,ddname,stationName);
			}else{
				replaceStr = getCdContent(dev,ddname,stationName,CZPService.getService().getDevName(curDev));
			}
		}else if(closedLoopList.contains(dev)){
			replaceStr = getHhContent(dev,ddname,stationName);
		}else{
			replaceStr = ddname+"@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
		}

		return replaceStr;
	}

	public static String getCdContent(PowerDevice dev,String ddname,String stationName){
		String replaceStr = "";

		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(stationName.contains("电站")){
				replaceStr += stationName+"@合上"+stationName+CZPService.getService().getDevName(dev)+"对线路充电/r/n";
			}else{
				replaceStr += ddname+"@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对线路充电/r/n";
			}
		}

		return replaceStr;
	}

	public static String getCdContent(PowerDevice dev,String ddname,String stationName,String deviceName){
		String replaceStr = "";

		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(stationName.contains("电站")){
				replaceStr += stationName+"@合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
			}else{
				replaceStr += ddname+"@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
			}
		}

		return replaceStr;
	}
}
