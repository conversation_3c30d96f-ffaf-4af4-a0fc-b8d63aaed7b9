package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBS;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSSMJXMXTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山双母接线母线停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			String maintenance = CommonFunctionBS.getMaintenance(stationName);

			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> plkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL +","+ CBSystemConstants.RunTypeSwitchMLPL, "", false, true, true, true);
			List<PowerDevice> othermxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"", CBSystemConstants.RunTypeSideMother, false, true, true, true);
			
			List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = new ArrayList<PowerDevice>();

			if(stationDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
			}else{
				zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
			}
			
			for (Iterator<PowerDevice> it = othermxList.iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(dev, curDev, "", "", true, true);
				
				for(PowerDevice path : pathList){
					if(path.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						if (!RuleExeUtil.isSwitchDoubleML(path)) {
							it.remove();
							break;
		                }
					}
				}
			}
			
			mxList.add(curDev);
			mxList.addAll(othermxList);
			
			List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : xlkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : zbkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			for(PowerDevice dev : plkgList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
				
				for(PowerDevice dz : dzList){
					if(RuleExeUtil.isDeviceChanged(dz)){
						if(dev.getDeviceStatus().equals("0")){
							yxkgList.add(dev);
							break;
						}else if(dev.getDeviceStatus().equals("1")){
							rbykgList.add(dev);
							break;
						}
					}
				}
			}
			
			if(maintenance.equals(stationName)){
				replaceStr += maintenance+"@核实"+deviceName+"停电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
			}else{
				replaceStr += maintenance+"@核实"+stationName+deviceName+"停电操作涉及的相关一、二次设备具备程序化操作条件/r/n";
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice rbykg : rbykgList){
						replaceStr += "保山地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由热备用转冷备用程序操作/r/n";
						replaceStr += "保山地调@执行"+stationName+CZPService.getService().getDevName(rbykg)+"由冷备用转热备用于"+CZPService.getService().getDevName(dev)+"程序操作/r/n";
					}
				}
			}
			
			if(maintenance.equals(stationName)){
				replaceStr += stationName+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
				
				for(PowerDevice dev : mlkgList){
					replaceStr += stationName+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
				}
			}else{
				replaceStr += maintenance+"@核实"+stationName+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
				
				for(PowerDevice dev : mlkgList){
					replaceStr += maintenance+"@核实"+stationName+CZPService.getService().getDevName(dev)+"操作电源已断开,具备倒母线操作条件/r/n";
				}
			}
			
			for(PowerDevice dev : othermxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					for(PowerDevice yxkg : yxkgList){
						replaceStr += "保山地调@执行"+stationName+CZPService.getService().getDevName(yxkg)+"由"+deviceName+"运行倒至"+CZPService.getService().getDevName(dev)+"运行程序操作/r/n";
					}
				}
			}
			
			if(stationName.equals(maintenance)){
				for(PowerDevice dev : mlkgList){
					replaceStr += maintenance+"@核实"+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
				}
				
				replaceStr += maintenance+"@核实"+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
				
				for(PowerDevice dev : othermxList){
					replaceStr += maintenance+"@核实"+deviceName+"上运行的所有断路器已倒至"+CZPService.getService().getDevName(dev)+"运行，"+deviceName+"电压互感器二次侧空开已断开，"+deviceName+"已处空载运行状态,具备停电条件/r/n";
				}
			}else{
				for(PowerDevice dev : mlkgList){
					replaceStr += maintenance+"@核实"+stationName+CZPService.getService().getDevName(dev)+"操作电源已合上/r/n";
				}
				
				replaceStr += maintenance+"@核实"+stationName+(int)curDev.getPowerVoltGrade()+"kV母线保护已按现场规程执行/r/n";
				
				for(PowerDevice dev : othermxList){
					replaceStr += maintenance+"@核实"+stationName+deviceName+"上运行的所有断路器已倒至"+CZPService.getService().getDevName(dev)+"运行，"+deviceName+"已处空载运行状态,具备停电条件/r/n";
				}
			}
			
			if(curDev.getDeviceStatus().equals("1")){
				replaceStr += "保山地调@执行"+stationName+deviceName+"由空载运行转热备用程序操作/r/n";
			}else if(curDev.getDeviceStatus().equals("2")){
				replaceStr += "保山地调@执行"+stationName+deviceName+"由空载运行转冷备用程序操作/r/n";
			}
			
			if(mxList.size() > 0){
				RuleExeUtil.swapDeviceList(mxList);
				
				replaceStr += "保山地调@核实"+stationName+CZPService.getService().getDevName(mxList)+"一、二次设备无异常/r/n";
			}
		}
		
		return replaceStr;
	}

}
