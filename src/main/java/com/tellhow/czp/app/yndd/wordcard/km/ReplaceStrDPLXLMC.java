package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

/** 
 * 倒旁路线路名称
 * <AUTHOR>
 * @dare 2021年9月3日下午3:30:10
 */
public class ReplaceStrDPLXLMC  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("倒旁路线路名称".equals(tempStr)){
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, null, true, true, true);
			if(lineList != null && lineList.size()>0) {
				for(int i=0; i<lineList.size();i++) {
					if(!lineList.get(i).getPowerDeviceName().contains("支线")) {
						replaceStr = lineList.get(i).getPowerDeviceName();
						break;
					}
				}
			}
		}
		
		return replaceStr;
	}

}
