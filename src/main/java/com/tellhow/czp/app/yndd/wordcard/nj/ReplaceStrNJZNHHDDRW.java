package com.tellhow.czp.app.yndd.wordcard.nj;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.nj.ChangePowerExecute;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrNJZNHHDDRW implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("怒江站内合环调电任务".equals(tempStr)) {
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			PowerDevice powerOnLine = new PowerDevice();
			PowerDevice powerOffLine = new PowerDevice();

			PowerDevice powerOnZb = new PowerDevice();
			PowerDevice powerOffZb = new PowerDevice();
			
			for(PowerDevice dev : ChangePowerExecute.powerOffZbList){
				powerOffZb = dev;
			}
			
			for(PowerDevice dev : ChangePowerExecute.powerOnZbList){
				powerOnZb = dev;
			}
			
			for(PowerDevice dev : ChangePowerExecute.powerOffLineList){
				powerOffLine = dev;
			}
			
			for(PowerDevice dev : ChangePowerExecute.powerOnLineList){
				powerOnLine = dev;
			}
			
			if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
				replaceStr += stationName+deviceName+"由"+CZPService.getService().getDevName(powerOffLine)+"供电转由"+CZPService.getService().getDevName(powerOnLine)+"供电";
			}else{
				replaceStr += stationName+deviceName+"由"+CZPService.getService().getDevName(powerOffZb)+"供电转由"+CZPService.getService().getDevName(powerOnZb)+"供电";
			}
		}
		return replaceStr;
	}


}