package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**  

* <p>Description: </p>  
* <AUTHOR>
* @date 2021年9月13日    
*/
public class ReplaceStrLSDYCMLKGRBY implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";
		
		if("落实低压侧母联开关热备用".equals(tempStr)) {
			List<PowerDevice> kgList = new ArrayList<PowerDevice>();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());
			
			double volt = RuleExeUtil.getTransformerVolByType(curDev, "low");
			
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&!CZPService.getService().getDevName(dev).contains("相")){
					if(dev.getPowerVoltGrade() == volt&&RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						kgList.add(dev);
					}
				}
			}
			
			for(PowerDevice dev : kgList){
				replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dev)+"\r\n"; 
			}
			
			if(replaceStr.length() == 0) {
				return null;
			}
		}
		return replaceStr;
	}

}
