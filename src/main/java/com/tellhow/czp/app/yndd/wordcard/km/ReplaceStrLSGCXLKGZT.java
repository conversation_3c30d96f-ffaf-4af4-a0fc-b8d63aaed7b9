package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLSGCXLKGZT implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";

		if("落实各侧线路开关状态".equals(tempStr)) {
			List<PowerDevice> list =  new ArrayList<PowerDevice>();
			
			PowerDevice lineSource = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			if(lineSource!=null){
				list.add(lineSource);
			}
			list.addAll(lineLoad);
			
			for(PowerDevice pd : list){
				List<PowerDevice> linekglist = RuleExeUtil.getDeviceList(pd,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, true, true);
				
				if(linekglist.size()>0){
					for(PowerDevice linekg : linekglist){
						PowerDevice station = CBSystemConstants.getPowerStation(linekg.getPowerStationID());
						
						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(linekg).equals("1")){
							replaceStr += CZPService.getService().getDevName(station)+"@落实"+CZPService.getService().getDevName(linekg)+"处热备用/r/n";
						}
					}
				}
			}
			
			
			for(PowerDevice pd : list){
				List<PowerDevice> linekglist = RuleExeUtil.getDeviceList(pd,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, true, true);
				
				if(linekglist.size()>0){
					for(PowerDevice linekg : linekglist){
						PowerDevice station = CBSystemConstants.getPowerStation(linekg.getPowerStationID());

						if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(linekg).equals("2")){
							replaceStr += CZPService.getService().getDevName(station)+"@落实"+CZPService.getService().getDevName(linekg)+"处冷备用/r/n";
						}
					}
				}
			}
			
		 	if(replaceStr.equals("")){
		 		replaceStr = null;
		 	}
		}
		return replaceStr;
	}

}
