package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKDNQZBFDTC10KVBZTZZ  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("扩大内桥主变复电退出10kV备自投装置".equals(tempStr)){
			List<PowerDevice> dyckgList = RuleExeUtil.getDeviceList(curDev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, false, true);

			List<PowerDevice> dycmlkgList = RuleExeUtil.getDeviceList(dyckgList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true,true);

			if(dycmlkgList.size()>0){
				List<PowerDevice> curmxList = RuleExeUtil.getDeviceList(dyckgList.get(0),SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, false, true);
				
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dycmlkgList.get(0),SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, false, true);

				for(Iterator<PowerDevice> itor = mxList.iterator();itor.hasNext();){
					PowerDevice mx = itor.next();
					
					if(curmxList.contains(mx)){
						itor.remove();
					}
				}
				
				if(mxList.size()>0){
					List<PowerDevice> alldycmlkgList = RuleExeUtil.getDeviceList(mxList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true,true);
				
					for(PowerDevice alldycmlkg : alldycmlkgList){
						if(!dycmlkgList.contains(alldycmlkg)){
							replaceStr = "退出"+CZPService.getService().getDevName(alldycmlkg)+"备自投装置";
							break;
						}
					}
				}
			}
			
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
