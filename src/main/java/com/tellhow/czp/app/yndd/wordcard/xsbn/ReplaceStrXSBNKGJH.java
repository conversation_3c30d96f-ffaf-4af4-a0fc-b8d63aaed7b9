package com.tellhow.czp.app.yndd.wordcard.xsbn;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNKGJH  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳开关解环".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			
			if(stationName.equals("110kV嘎栋变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += stationName+"@投入110kV备自投装置/r/n";
				}
			}else if(stationName.equals("110kV曼弄枫变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += stationName+"@投入110kV备自投装置/r/n";
				}
			}else if(stationName.equals("110kV勐罕变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += stationName+"@投入110kV备自投装置/r/n";
				}
			}else if(stationName.equals("110kV辉凰变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += stationName+"@投入110kV备自投装置/r/n";
				}
			}else if(stationName.equals("110kV东盟变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += stationName+"@投入110kV备自投装置/r/n";
				}
			}else if(stationName.equals("110kV勐宽变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += stationName+"@投入110kV备自投装置/r/n";
				}
			}
			
			replaceStr += CommonFunctionBN.getSwitchOffContent(curDev, stationName, station);
			
			if(stationName.equals("110kV嘎栋变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV景洪-嘎栋-傣乡电磁环网已开环运行/r/n";
				}
			}else if(stationName.equals("110kV曼弄枫变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV景洪-曼弄枫-黎明电磁环网已开环运行/r/n";
				}
			}else if(stationName.equals("110kV勐罕变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV景洪-勐罕-黎明电磁环网已开环运行/r/n";
				}
			}else if(stationName.equals("110kV辉凰变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV景洪-江北-辉凰-傣乡电磁环网已开环运行/r/n";
				}
			}else if(stationName.equals("110kV东盟变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV茶城-东盟-勐腊-黎明电磁环网已开环运行/r/n";
				}
			}else if(stationName.equals("110kV勐宽变")){
				if(curDev.getPowerVoltGrade() == 110){
					replaceStr += "云南中调@汇报220kV/110kV傣乡-巴奇-勐宽-黎明电磁环网已开环运行/r/n";
				}
			}
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
