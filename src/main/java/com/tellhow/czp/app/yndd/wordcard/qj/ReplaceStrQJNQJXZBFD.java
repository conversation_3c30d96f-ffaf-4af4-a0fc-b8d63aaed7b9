package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJNQJXZBFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖内桥接线主变复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			replaceStr += CommonFunctionQJ.getPowerOnCheckContent();
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(curDev);
			List<PowerDevice> zbdzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate, CBSystemConstants.RunTypeKnifeZB);

			for (Iterator<PowerDevice> it = zbdzList.iterator(); it.hasNext();) {
				PowerDevice zbdz = it.next();
				
				if(zbgycdzList.contains(zbdz)){
					it.remove();
				}
			}
			
			List<PowerDevice> zbgycmxList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgycdzList){
				zbgycmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				gycxlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true);
			}

			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			List<PowerDevice> zbzxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

			boolean ismxlby = false;
			
			for(PowerDevice dev : zbgycmxList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
					ismxlby = true;
					break;
				}
			}
			
			if(ismxlby){
				
				
			}else{
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					for(PowerDevice dev : zbzyckgList){
                        if (RuleExeUtil.getDeviceBeginStatus(dev).equals("2")) {
                            replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
                        }
                    }
					
					for(PowerDevice dev : zbdyckgList){
                        if (RuleExeUtil.getDeviceBeginStatus(dev).equals("2")) {
                            replaceStr += CommonFunctionQJ.getSwitchLbyToRbyContent(dev, stationName, station);
                        }
                    }
					
					for(PowerDevice dev : gycxlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionQJ.getSwitchOffContent(dev, stationName, station);
						}
					}

					for(PowerDevice dev : gycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
							replaceStr += CommonFunctionQJ.getSwitchOffContent(dev, stationName, station);
						}
					}
					
					for(PowerDevice dev : zbgycdzList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionQJ.getKnifeOnContent(zbgycdzList, stationName);
						}
					}
					
					zbdzList = RuleExeUtil.sortByVoltHigh(zbdzList);
					
					for(PowerDevice dev : zbdzList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += CommonFunctionQJ.getKnifeOnContent(zbdzList, stationName);
						}
					}
				}
				
				if(curDev.getDeviceStatus().equals("0")){//转运行
					for(PowerDevice dev : zbzxdjddzList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : CommonFunctionQJ.chargeDeviceByNqZbFdList){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							replaceStr += CommonFunctionQJ.getCdContent(dev, "曲靖地调", stationName,deviceName);

							for(PowerDevice gycmlkg : gycmlkgList){
								replaceStr += CommonFunctionQJ.getHhContent(gycmlkg, "曲靖地调", stationName);
							}
							
							for(PowerDevice gycxlkg : gycxlkgList){
								replaceStr += CommonFunctionQJ.getSwitchOffContent(gycxlkg, stationName, station);
							}
						}else{
							replaceStr += CommonFunctionQJ.getCdContent(dev, "曲靖地调", stationName,deviceName);
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
						}
					}
					
					for(PowerDevice dev : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
						}
					}
					
					for(PowerDevice dev : dycmlkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "曲靖地调@遥控断开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice dev : zbzxdjddzList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
							replaceStr += "曲靖地调@遥控拉开"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
						}
					}
					
					for(PowerDevice gycxlkg : gycxlkgList){
						if(gycxlkg.getDeviceStatus().equals("0")){
							replaceStr += CommonFunctionQJ.getCdOrHhContent(gycxlkg, "曲靖地调", stationName);
						}
					}
					
					for(PowerDevice gycmlkg : gycmlkgList){
						if(gycmlkg.getDeviceStatus().equals("1")){
							replaceStr += CommonFunctionQJ.getSwitchOffContent(gycmlkg, stationName, station);
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
