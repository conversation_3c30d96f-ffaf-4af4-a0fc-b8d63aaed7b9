package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;

public class StationAllDeviceTDExecute implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
        List<PowerDevice> zbList = new ArrayList<PowerDevice>();
        List<PowerDevice> xlList = new ArrayList<PowerDevice>();
        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
        List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
        List<PowerDevice> dzList = new ArrayList<PowerDevice>();
        List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

        List<String> voltList = new ArrayList<String>();
        Set<String> set = new HashSet<String>();
        List<String> bztvoltList = new ArrayList<String>();
        Set<String> bztset = new HashSet<String>();

        HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(rbm.getPd().getPowerStationID());
		
		for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
			PowerDevice dev = it2.next();
			if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
				zbList.add(dev);
			}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
				set.add(String.valueOf((int)dev.getPowerVoltGrade()));
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getDeviceStatus().equals("1")){
						bztset.add(String.valueOf((int)dev.getPowerVoltGrade()));
					}
					mlkgList.add(dev);
				}
				
				if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() > 10&&!dev.getPowerDeviceName().contains("相")){
					rbykgList.add(dev);
				}else if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() == 10&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					rbykgList.add(dev);
				}
			}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS)){
				dzList.add(dev);
			}else if (dev.getDeviceType().equals(SystemConstants.InOutLine)&&dev.getPowerVoltGrade() == 10){
				xlList.add(dev);
			}
		}
		
		RuleExeUtil.swapDeviceListNum(rbykgList);

		for(Iterator<String> itor = set.iterator();itor.hasNext();){
			String volt = itor.next();
			
			if(!volt.equals("0")){
				voltList.add(volt);
			}
		}
		
		Collections.sort(voltList, new Comparator<String>() {
	        public int compare(String p1, String p2) {
	           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
	              return -1;
	           }
	           else {
	              return 1;
	           }
	        }
	    });
		
		
		RuleExeUtil.sortListByDevName(zbkgList);
		CardModel cm = new CardModel();
    	cm.setCardItems(new ArrayList<CardItemModel>());
		RuleExeUtil.swapDeviceListNum(zbList);
		
		for(PowerDevice dev : zbList){
			if(dev.getPowerVoltGrade() > 35){
				
			}
		}
		
		List<PowerDevice> drkgList = new ArrayList<PowerDevice>();
        List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
        List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
        List<PowerDevice> dyczbkgList = new ArrayList<PowerDevice>();

		int dycvolt = Integer.valueOf(voltList.get(0));

		
		for(PowerDevice zb : zbList){
			List<PowerDevice> gdList = RuleExeUtil.getDeviceList(zb, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			for(PowerDevice gd : gdList) {
				RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
			}
		}
		
		if(dycvolt == 6||dycvolt == 10){
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				
				 if(dev.getDeviceStatus().equals("1")){
					if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
						bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
					}
				 }
				
				if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
					if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
							drkgList.add(dev);
						}
						
						if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
							if (dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")||dev.getPowerDeviceName().contains("接地变")){
								zybkgList.add(dev);
							}
						}
						
						if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkgList.add(dev);
							}
						}
						
						if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								dyczbkgList.add(dev);
							}
						}
					}
				}
			}
			
			for(PowerDevice dev : drkgList){
				
			}
			
			for(PowerDevice dev : zybkgList){
				
			}
			
			for(PowerDevice dev : dycmlkgList){
				
			}
			
			for(PowerDevice dev : dyczbkgList){
				
			}
			
			if(voltList.size() == 2){
				int gycvolt = Integer.valueOf(voltList.get(1));

				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getDeviceStatus().equals("1")){
						if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
							bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
					}
					
					if(dev.getPowerVoltGrade() == gycvolt){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							allgyckg.add(dev);
							
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								gyczbkg.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg.add(dev);
							}
						}
					}
				}
				
				for(PowerDevice dev : gyczbkg){
					
				}
				
				for(PowerDevice dev : allgyckg){
					
				}
				
				for(PowerDevice dev : gycmlkg){
					
				}
				
			}else if(voltList.size() == 3){
				int zycvolt = Integer.valueOf(voltList.get(1));
				int gycvolt = Integer.valueOf(voltList.get(2));
				
				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
				
				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getDeviceStatus().equals("1")){
						if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
							bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
					}
					
					if(dev.getPowerVoltGrade() == zycvolt){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							allzyckg.add(dev);
							
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								zyczbkg.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								zycmlkg.add(dev);
							}
						}
					}else if(dev.getPowerVoltGrade() == gycvolt){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							allgyckg.add(dev);
							
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								gyczbkg.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg.add(dev);
							}
						}
					}
				}
				
				for(PowerDevice dev : allzyckg){}
				
				for(PowerDevice dev : zycmlkg){}
				
				for(PowerDevice dev : zyczbkg){}
				
				for(PowerDevice dev : gyczbkg){}
				
				for(PowerDevice dev : allgyckg){}
				
				for(PowerDevice dev : gycmlkg){
				}
		}else{
			if(voltList.size() == 2){
				int gycvolt = Integer.valueOf(voltList.get(1));

				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
				
				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getDeviceStatus().equals("1")){
						if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
							bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
					}
					
					if(dev.getPowerDeviceName().contains("A相")||
							dev.getPowerDeviceName().contains("B相")||
							dev.getPowerDeviceName().contains("C相")){
						continue;
					}
					
					if(dev.getPowerVoltGrade() == gycvolt){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							allgyckg.add(dev);
							
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								gyczbkg.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg.add(dev);
							}
						}
					}else if(dev.getPowerVoltGrade() == dycvolt){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							alldyckg.add(dev);
							
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								dyczbkg.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkg.add(dev);
							}
						}
					}
				}
				
				for(PowerDevice dev : alldyckg){
					
				}
				
				for(PowerDevice dev : dycmlkg){
					
				}
				
				for(PowerDevice dev : dyczbkg){
					
				}
				
				for(PowerDevice dev : gyczbkg){
					
				}
				
				for(PowerDevice dev : allgyckg){
					
				}
				
				for(PowerDevice dev : gycmlkg){
					
				}
			}else if(voltList.size() == 3){
				int zycvolt = Integer.valueOf(voltList.get(1));
				int gycvolt = Integer.valueOf(voltList.get(2));

				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
				
				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
				
				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getDeviceStatus().equals("1")){
						if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
							bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
					}
					
					if(dev.getPowerDeviceName().contains("A相")||
							dev.getPowerDeviceName().contains("B相")||
							dev.getPowerDeviceName().contains("C相")){
						continue;
					}
					
					if(dev.getPowerVoltGrade() == gycvolt){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							allgyckg.add(dev);
							
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								gyczbkg.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg.add(dev);
							}
						}
					}else if(dev.getPowerVoltGrade() == dycvolt){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							alldyckg.add(dev);
							
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								dyczbkg.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkg.add(dev);
							}
						}
					}else if(dev.getPowerVoltGrade() == zycvolt){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							allzyckg.add(dev);
							
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								zyczbkg.add(dev);
							}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								zycmlkg.add(dev);
							}
						}
					}
				}
				
				for(PowerDevice dev : alldyckg){
					
				}
				
				for(PowerDevice dev : dycmlkg){
					
				}
				
				for(PowerDevice dev : dyczbkg){
					
				}
				
				
				for(PowerDevice dev : allzyckg){
					
				}
				
				for(PowerDevice dev : zycmlkg){
					
				}
				
				for(PowerDevice dev : zyczbkg){
					
				}
				
				for(PowerDevice dev : gyczbkg){
					
				}
				
				for(PowerDevice dev : allgyckg){
					
				}
				
				for(PowerDevice dev : gycmlkg){
					
				}
			}
		}
		
		
		
		Collections.sort(bztvoltList, new Comparator<String>() {
	        public int compare(String p1, String p2) {
	           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
	              return -1;
	           }
	           else {
	              return 1;
	           }
	        }
	    });
		
		for(String volt : bztvoltList){
			if(volt.equals("10")){
				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					if(dev.getDeviceStatus().equals("1")){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() == 10){
							dycmlkg.add(dev);
						}
					}
				}
				
				if(dycmlkg.size()>1){
					for(PowerDevice dev : dycmlkg){
						
					}
				}
			}
		}
	}
		return true;
	}
}
