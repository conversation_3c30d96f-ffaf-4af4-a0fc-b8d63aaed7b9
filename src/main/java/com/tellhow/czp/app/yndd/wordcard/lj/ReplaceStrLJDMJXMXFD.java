package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJDMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江单母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);
			
			
			List<PowerDevice> curmlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			List<PowerDevice> curxlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true);
			List<PowerDevice> curzbkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, false, true);

			replaceStr += stationName+"@核实"+deviceName+"相关工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，设备的二次装置已正常投入，确认"+deviceName+"具备复电条件/r/n";
			
			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
				replaceStr += stationName+"@将"+CZPService.getService().getDevName(curDev)+"由冷备用转热备用/r/n";
			}
			
			if(curmlkgList.size()>0){//分段
				if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){//电源侧
					for(PowerDevice zbdyckg : curxlkgList){
						if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("0")){
							replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
					
					for(PowerDevice curmlkg : curmlkgList){
						if(RuleExeUtil.isDeviceHadStatus(curmlkg, "1", "0")){
							replaceStr += CommonFunctionLJ.getHhContent(curmlkg,"丽江地调",stationName);
						}
					}
					
					List<PowerDevice> zbList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
					
					for(PowerDevice dev : zbList){
						List<PowerDevice> zbzxdjddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);

						List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
						List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);

						List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
						List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
						List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();

						for(PowerDevice zbdyckg : zbdyckgList){
							dycmxList = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

							for(PowerDevice mx : dycmxList){
								dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
							}
						}
						
						for(PowerDevice zbzyckg : zbzyckgList){
							List<PowerDevice> mxList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
							
							for(PowerDevice mx : mxList){
								zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
							}
						}
						
						for(PowerDevice zbdyckg : zbgyckgList){
							if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("0")){
								replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
							}
						}
						
						for(PowerDevice zbzyckg : zbzyckgList){
							if(RuleExeUtil.getDeviceEndStatus(zbzyckg).equals("0")){
								replaceStr += CommonFunctionLJ.getHhContent(zbzyckg,"丽江地调",stationName);
							}
						}
						
						for(PowerDevice zbzyckg : zycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(zbzyckg).equals("1")){
								replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbzyckg)+"/r/n";
							}
						}
						
						for(PowerDevice zbdyckg : zbdyckgList){
							if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("0")){
								replaceStr += CommonFunctionLJ.getHhContent(zbdyckg,"丽江地调",stationName);
							}
						}
						
						for(PowerDevice zbdyckg : dycmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("1")){
								replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
							}
						}
						
						for(PowerDevice curmlkg : curmlkgList){
							if(RuleExeUtil.getDeviceEndStatus(curmlkg).equals("1")){
								replaceStr += "丽江地调@遥控断开"+stationName+CZPService.getService().getDevName(curmlkg)+"/r/n";
							}
						}
						
						for(PowerDevice zbzxdjddz : zbzxdjddzList){
							replaceStr += "丽江地调@遥控拉开"+stationName+CZPService.getService().getDevName(zbzxdjddz)+"/r/n";
							replaceStr += "丽江地调@检查"+stationName+CZPService.getService().getDevName(zbzxdjddz)+"在拉开位置/r/n";
						}
					}
					
					for(PowerDevice curmlkg : curmlkgList){
						if(RuleExeUtil.isDeviceHadStatus(curmlkg, "1", "0")){
							replaceStr += "投入"+(int)curmlkg.getPowerVoltGrade()+"kV备自投装置/r/n";
						}
					}
				}else{
					for(PowerDevice zbdyckg : curxlkgList){
						if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("0")){
							replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
					
					for(PowerDevice gycmlkg : curmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("1")){
							replaceStr += CommonFunctionLJ.getHhContent(gycmlkg,"丽江地调",stationName);
						}
					}
					
					for(PowerDevice zbdyckg : curzbkgList){
						if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("0")){
							replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
				}
			}else{
				List<PowerDevice> zbList = new ArrayList<PowerDevice>();

				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						
						if(!dev.getPowerDeviceName().contains("接地变")){
							zbList.add(dev);
						}
					}
				}
				
				for(PowerDevice curxlkg : curxlkgList){
					if(RuleExeUtil.getDeviceEndStatus(curxlkg).equals("0")){
						replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(curxlkg)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbList){
					List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
					List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
					List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
					
					for(PowerDevice zbgyckg : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(zbgyckg).equals("0")){
							replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(zbgyckg)+"/r/n";
						}
					}
					
					for(PowerDevice zbzyckg : zbzyckgList){
						if(RuleExeUtil.getDeviceEndStatus(zbzyckg).equals("0")){
							replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(zbzyckg)+"/r/n";
						}
					}
					
					for(PowerDevice zbdyckg : zbdyckgList){
						if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("0")){
							replaceStr += "丽江地调@遥控合上"+stationName+CZPService.getService().getDevName(zbdyckg)+"/r/n";
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
