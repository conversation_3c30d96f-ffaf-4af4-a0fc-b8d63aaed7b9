package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class Replace<PERSON><PERSON><PERSON>KGRBY implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("本侧开关热备用".equals(tempStr)){
			List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true,true);

			if(xlswList.size()>0){
				if(RuleExeUtil.getDeviceBeginStatus(xlswList.get(0)).equals("1")||RuleExeUtil.getDeviceBeginStatus(xlswList.get(0)).equals("")){
					 return true;
				}
			}
		}
        return false;
	}
}
