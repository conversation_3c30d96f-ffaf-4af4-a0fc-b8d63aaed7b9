package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJDMJXZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖单母接线主变停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(curDev);
			List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(curDev);
			List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(curDev);
			
			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgyckgList){
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
			}

			List<PowerDevice> zycmlkgList =  new ArrayList<PowerDevice>();
			List<PowerDevice> dycmlkgList =  new ArrayList<PowerDevice>();
			
			List<PowerDevice> dycmxList =  new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : zbdyckgList){
				dycmxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : dycmxList){
					dycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
				
				for(PowerDevice mx : mxList){
					zycmlkgList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			List<PowerDevice> otherzbList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbzyckgList = new ArrayList<PowerDevice>();
			List<PowerDevice> otherzbdyckgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
						if(dev.getPowerVoltGrade() == RuleExeUtil.getTransformerVolByType(curDev, "middle")){
							otherzbzyckgList.add(dev);
						}else if(dev.getPowerVoltGrade() == RuleExeUtil.getTransformerVolByType(curDev, "low")){
							otherzbdyckgList.add(dev);
						}
					}
				}else if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					if(!dev.getPowerDeviceID().equals(curDev.getPowerDeviceID())){
						otherzbList.add(dev);
					}
				}
			}
			
			List<PowerDevice> zxdjddzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			List<PowerDevice> otherzxdjddzList = new ArrayList<PowerDevice>();
			
			for(PowerDevice dev : otherzbList){
				List<PowerDevice> jddzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
				RuleExeUtil.swapLowDeviceList(jddzList);
				otherzxdjddzList.addAll(jddzList);
			}
			
			String otherZbName = "";
			
			for(PowerDevice dev : otherzbList){
				otherZbName += CZPService.getService().getDevName(dev) + "、";
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
				}
			}
			
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionQJ.getZxdJddzOnCheckContent(zxdjddzList, stationName, station);
			}
			
			if(otherzxdjddzList.size() > 0){
				replaceStr += CommonFunctionQJ.getZxdJddzOnCheckContent(otherzxdjddzList, stationName, station);
			}
			
			for(PowerDevice dev : otherzbdyckgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
				}
			}
			
			for(PowerDevice dev : dycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbdyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += CommonFunctionQJ.getSwitchOffContent(dev, stationName, station);
				}
			}
			
			for(PowerDevice dev : otherzbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
				}
			}
			
			for(PowerDevice dev : zycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
					replaceStr += CommonFunctionQJ.getHhContent(dev, "曲靖地调", stationName);
				}
			}
			
			for(PowerDevice dev : zbzyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += CommonFunctionQJ.getSwitchOffContent(dev, stationName, station);
				}
			}
			
			for(PowerDevice dev : gycmlkgList){
				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += CommonFunctionQJ.getSwitchOffContent(dev, stationName, station);
				}
			}
			
			for(PowerDevice dev : zbgyckgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					replaceStr += CommonFunctionQJ.getSwitchOffContent(dev, stationName, station);
				}
			}

			// 运行到热备用拉开中性点地刀
			if(zxdjddzList.size() > 0){
				replaceStr += CommonFunctionQJ.getZxdJddzOffCheckContent(zxdjddzList, stationName, station);
			}
			
			if(curDev.getDeviceStatus().equals("2")){
				for(PowerDevice dev : zbdyckgList){
                    if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
                    	replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
                    }
                }
				
				for(PowerDevice dev : zbzyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
					}
				}
				
				for(PowerDevice dev : zbgyckgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
						replaceStr += CommonFunctionQJ.getSwitchRbyToLbyContent(dev, stationName, station);
					}
				}
			}
		}
		
		return replaceStr;
	}

}
