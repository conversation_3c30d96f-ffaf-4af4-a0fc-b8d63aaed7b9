package com.tellhow.czp.app.yndd.wordcard.qj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionQJ;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrQJDMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("曲靖单母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);
			List<PowerDevice> xlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);

			replaceStr += CommonFunctionQJ.getPowerOnCheckContent();
			
			if(curDev.getPowerVoltGrade() == 10){
				xlkgList.clear();
			}
			
			if(curDev.getPowerVoltGrade() == station.getPowerVoltGrade()){
				List<PowerDevice> zbList = new ArrayList<PowerDevice>();
				
				HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
					PowerDevice dev = it.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						if(RuleExeUtil.isDeviceChanged(dev)){
							zbList.add(dev);
						}
					}
				}
				
				int count = 0;
				
				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}
				
				for(PowerDevice dev : zbList){
					List<PowerDevice> zbdyckgList = RuleExeUtil.getTransformerSwitchLow(dev);
					List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
					List<PowerDevice> zbgyckgList = RuleExeUtil.getTransformerSwitchHigh(dev);
					
					for(PowerDevice zbgyckg : zbgyckgList){
						if(RuleExeUtil.getDeviceEndStatus(zbgyckg).equals("0")){
							replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(zbgyckg)+"对"+CZPService.getService().getDevName(dev)+"充电/r/n";
						}
					}
					
					for(PowerDevice zbzyckg : zbzyckgList){
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						
						for(PowerDevice mx : mxList){
							if(RuleExeUtil.getDeviceEndStatus(zbzyckg).equals("0")){
								replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(zbzyckg)+"对"+CZPService.getService().getDevName(mx)+"充电/r/n";
							}
						}
					}
					
					for(PowerDevice zbdyckg : zbdyckgList){
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(zbdyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						
						for(PowerDevice mx : mxList){
							if(RuleExeUtil.getDeviceEndStatus(zbdyckg).equals("0")){
								if(count == 0 && zbList.size() == 2){
									count ++;
									replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(zbdyckg)+"对"+CZPService.getService().getDevName(mx)+"充电/r/n";
								}else{
									replaceStr += "曲靖地调@遥控用"+stationName+CZPService.getService().getDevName(zbdyckg)+"同期合环/r/n";
								}
							}
						}
					}
				}
			}else{
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
				List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);

				if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
					for(PowerDevice dev : zbkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
					
					for(PowerDevice dev : mlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							replaceStr += stationName+"@将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用/r/n";
						}
					}
				}
				
				for(PowerDevice dev : xlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"/r/n";
					}
				}
				
				for(PowerDevice dev : zbkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					}
				}
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += "曲靖地调@遥控合上"+stationName+CZPService.getService().getDevName(dev)+"对"+deviceName+"充电/r/n";
					}
				}
			}
		}
		
		return replaceStr;
	}

}
