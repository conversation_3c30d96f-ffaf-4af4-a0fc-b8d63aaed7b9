package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionBN;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNDMJXMXFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳单母接线母线复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> mlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
			List<PowerDevice> xlkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);
			List<PowerDevice> zybkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchZYB, "", false, true, true, true);
			List<PowerDevice> jdbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, true);
			List<PowerDevice> drkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR, "", false, true, true, true);
			List<PowerDevice> dkkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDK, "", false, true, true, true);

			if(zybkgList.size() == 0){
				List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);

				for(PowerDevice dev : qtkgList){
					if(dev.getPowerDeviceName().contains("站用变")){
						zybkgList.add(dev);
					}
				}
			}
			
			if(jdbkgList.size() == 0){
				List<PowerDevice> qtkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true);

				for(PowerDevice dev : qtkgList){
					if(dev.getPowerDeviceName().contains("接地变")){
						zybkgList.add(dev);
					}
				}
			}
			
			if(curDev.getPowerVoltGrade() < station.getPowerVoltGrade()){//负荷侧
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);

				if(curDev.getPowerVoltGrade() == 10){
					replaceStr += "版纳配调@将对"+stationName+deviceName+"送电，配调管辖设备无影响"+deviceName+"送电情况/r/n";
				}
				
				if(station.getPowerVoltGrade() == 500){//顺控操作
					replaceStr += "版纳地调@执行将"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbkgList, stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zybkgList, stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(dkkgList, stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(drkgList, stationName);
					
					replaceStr += stationName+"@将"+deviceName+"电压互感器由冷备用转运行/r/n";
					
					for(PowerDevice dev  : zybkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
						}
					}
					
					for(PowerDevice dev  : zbkgList){
						if(!dev.getPowerDeviceName().contains("备用")){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
							}
						}
					}
				}else if(station.getPowerVoltGrade() == 220 && stationName.equals("220kV茶城变")){//220kV茶城变特殊判断
					PowerDevice zbdz = CBSystemConstants.getPowerDevice("114841790497959799");
					
					replaceStr += stationName+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					replaceStr += "版纳地调@执行将"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbdz, stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbkgList, stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zybkgList, stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(jdbkgList, stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(dkkgList, stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(drkgList, stationName);
					
					replaceStr += stationName+"@将"+deviceName+"电压互感器由冷备用转运行/r/n";
					
					for(PowerDevice dev  : zybkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
						}
					}
					
					for(PowerDevice dev  : jdbkgList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
						}
					}
					
					for(PowerDevice dev  : zbkgList){
						if(!dev.getPowerDeviceName().contains("备用")){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
							}
						}
					}
				}else{
					if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("2")){
						for(PowerDevice dev : mlkgList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
								replaceStr += "投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
							}
						}
						
						if(curDev.getDeviceStatus().equals("0")){
							replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
							
							for(PowerDevice dev : zbkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
								}
							}

							for(PowerDevice dev : mlkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
								}
							}
							
							for(PowerDevice dev : xlkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
								}
							}
							
							for(PowerDevice dev : zybkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
								}
							}
							
							for(PowerDevice dev : jdbkgList){
								if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
									replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
								}
							}
						}else{
							replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
						}
					}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
						replaceStr += stationName+"@将"+deviceName+"由热备用转运行/r/n";
					}
				}
				
				if(curDev.getPowerVoltGrade() == 10){
					replaceStr += "版纳配调@"+stationName+deviceName+"已送电/r/n";
				}
			}else{
				List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC, "", false, true, true, true);
				List<PowerDevice> mldzList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeML, "", true, true, true, true);

				replaceStr += stationName+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
				
				if(curDev.getPowerVoltGrade() >= 110){//顺控操作
					replaceStr += "版纳地调@执行将"+stationName+deviceName+"由冷备用转热备用程序操作/r/n";
					
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(xlkgList, stationName);
					replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(zbkgList, stationName);

					if(mldzList.size() > 0){
						for(PowerDevice dev : mldzList){
							if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
								replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(dev, stationName);
							}
						}
					}else{
						replaceStr += CommonFunctionBN.getSequenceConfirmFdContent(mlkgList, stationName);
					}
				}else{
					replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
				}
				
				replaceStr += stationName+"@将"+deviceName+"电压互感器由冷备用转运行/r/n";
				
				for(PowerDevice dev : mlkgList){
					if(RuleExeUtil.isDeviceHadStatus(dev, "1", "0")){
						replaceStr += stationName+"@投入"+deviceName+"充电保护/r/n";
						replaceStr += CommonFunctionBN.getSwitchOnContent(dev, stationName, station);
						replaceStr += stationName+"@退出"+deviceName+"充电保护/r/n";
					}
				}
				
				for(PowerDevice dev : xlkgList){
					if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
						replaceStr += CommonFunctionBN.getHhContent(dev, "版纳地调", stationName);
					}
				}
			}
		}
		
		return replaceStr;
	}

}
