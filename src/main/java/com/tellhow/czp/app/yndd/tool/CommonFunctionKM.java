package com.tellhow.czp.app.yndd.tool;

import java.awt.Dimension;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.Rectangle;
import java.awt.Toolkit;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.swing.JOptionPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;
import org.xml.sax.SAXException;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.view.StringCheckChoose;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.CardWordMode;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardModel;

public class CommonFunctionKM {
	public static boolean checkResult = true; 
	

	
	public static List<PowerDevice> getPowerDeviceList(String subordinateStationName,String devName) {
	    List<String> stationIDList = new ArrayList<String>();
		List<PowerDevice> deviceList = new ArrayList<PowerDevice>();

		//厂站名称校验
		for(Iterator<PowerDevice> it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();){
			PowerDevice st = it.next();
			
			String modelDevName = StringUtils.killVoltInDevName(CZPService.getService().getDevName(st));
			subordinateStationName = StringUtils.killVoltInDevName(subordinateStationName);
			
			if(modelDevName.equals(subordinateStationName)) {
				String stationID = st.getPowerDeviceID();
				stationIDList.add(stationID);
				
				if (CBSystemConstants.getStationPowerDevices(stationID) == null) {
					CreatePowerStationToplogy.loadFacEquip(stationID);
				}
			}
		}
		
		if(stationIDList.size() > 1){
			for(Iterator<String> it = stationIDList.iterator();it.hasNext();){
				String stationID = it.next();
				
				String sql = "SELECT STATIONID FROM " + CBSystemConstants.equipUser + "T_SUBSTATION_TREE WHERE STATIONID = '"+stationID+"' AND ISREMOVE = '1'";
				List<Map<String,String>> idList = DBManager.queryForList(sql);
				
				if(idList.size()==1){
					it.remove();
				}
			}
		}
		
		//设备名称校验
		if(stationIDList.size() == 1){
			if (CBSystemConstants.getStationPowerDevices(stationIDList.get(0)) == null) {
				CreatePowerStationToplogy.loadFacEquip(stationIDList.get(0));
			}
			
			HashMap<String, PowerDevice> devMap = CBSystemConstants.getMapPowerStationDevice().get(stationIDList.get(0));
			
			if(devMap != null){
				String equipTypeFlag = "";
				String equipTypeName = "";
				String[] type = new String[] { SystemConstants.SwitchFlowGroundLine,
						SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine,
						SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate,SystemConstants.Switch, SystemConstants.SwitchSeparate,
						SystemConstants.SwitchSeparate, SystemConstants.Switch,
						SystemConstants.Switch,SystemConstants.VolsbTransformer, SystemConstants.MotherLine,
						SystemConstants.MotherLine, SystemConstants.InOutLine,SystemConstants.InOutLine,
						SystemConstants.PowerTransformer, SystemConstants.ElecShock,
						SystemConstants.ElecCapacity ,SystemConstants.PowerTransformer,SystemConstants.Term};
				String[] key = new String[] { "接地刀闸","接地开关", "地刀", "隔离刀闸","隔离开关","小车开关", "小车", "刀闸", "断路器",
						"开关","PT", "母线", "母", "线","回", "主变", "电抗器", "电容器","#变","站用变"};
				for (int i = 0; i < key.length; i++) {
					if (devName.lastIndexOf(key[i]) >= 0) {
						equipTypeFlag = type[i];
						equipTypeName = key[i];
						break;
					}
				}
				
				String volStr = "";
				
				if(devName.toLowerCase().split("kv").length >= 3){
					volStr = devName.toLowerCase().substring(devName.toLowerCase().indexOf("kv")+2);
				}else if(devName.contains("中性点")&&devName.contains("接地开关")){
					volStr = "";
				}else{
					volStr = devName;
				}
				
				//获取电压等级
				String volt = StringUtils.getVoltInDevName(volStr);
				List<PowerDevice> pdList = new ArrayList<PowerDevice>();
				
				for (PowerDevice dev : devMap.values()) {
					if (!equipTypeFlag.equals("") && !dev.getDeviceType().equals(equipTypeFlag))
						continue;
					if (!volt.equals("") && dev.getPowerVoltGrade() != Double.valueOf(volt))
						continue;
					if(dev.getPowerDeviceName().contains("A相")||dev.getPowerDeviceName().contains("B相")||dev.getPowerDeviceName().contains("C相"))
						continue;
					if(dev.getPowerDeviceName().contains("虚"))
						continue;
					
					if(equipTypeFlag.equals(SystemConstants.InOutLine)){
						if (CZPService.getService().getDevName(dev).equals(devName)){
							pdList.add(dev);
							if(pdList.size()>1){
								break;
							}
						}
					}else if (CZPService.getService().getDevName(dev).equals(devName)&&!devName.equals("")) {
						if(dev.getPowerDeviceName().indexOf(equipTypeName) >= 0) {
							pdList.add(dev);
							if(pdList.size()>1){
								break;
							}
						}else{
							pdList.add(dev);
							if(pdList.size()>1){
								break;
							}
						}
					}
				}
				
				if(pdList.size() == 1){
					deviceList.add(pdList.get(0));
				}
			}
		}
		
	    return deviceList;
	}
	
	public static void insertRecode(String name, String content,String inOrOutput) {
		try{
			String sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_JHRZ(TIME,NAME,CONTENT,TYPE) values (sysdate,'"+name+"',?,'"+inOrOutput+"')";
			DBManager.execute(sql,content);
		}
		catch(Exception ex) {
			ex.printStackTrace();
		}
	}
	
	/**
	 *
	 * @param xmlInput xml报文的输入流
	 * @return xml报文的根元素
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 * @throws IOException
	 */
	public static Element getRootElement(InputStream xmlInput) throws ParserConfigurationException, SAXException, IOException {
		DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
		DocumentBuilder db = dbf.newDocumentBuilder();
		org.w3c.dom.Document document = db.parse(xmlInput);
		DOMReader domReader = new DOMReader();
		Document ret = domReader.read(document);
		return ret.getRootElement();
	}
	
	public static String handleDeviceName(String deviceName) {
        deviceName = deviceName.replaceAll("及(各侧开关|三侧开关)", "");
        deviceName = deviceName.replace("付", "副");
        deviceName = deviceName.replace("母压变", "母线压变");
        deviceName = deviceName.replace("千伏", "kV");
        deviceName = deviceName.replace("KV", "kV");

        deviceName=deviceName.replaceAll("IV", "Ⅳ");
        deviceName=deviceName.replaceAll("III", "Ⅲ");
        deviceName=deviceName.replaceAll("II", "Ⅱ");
        deviceName=deviceName.replaceAll("I", "Ⅰ");

        deviceName=deviceName.replaceAll("＃", "#");
        deviceName=deviceName.replaceAll("－", "-");

        return deviceName;
    }
	
	/**
     * 根据设备名确定设备类型；如果是二次设备则这里的返回值不能使用
     *
     * @param deviceName        设备名
     * @param equipTypeNameFlag true：返回设备类型名，false：返回设备类型
     * @return 设备类型或设备类型名
     */
    public static String determineEquipType(String deviceName, boolean equipTypeNameFlag) {
        String equipType = ""; //设备类型
        String equipTypeName = ""; //设备类型名
        String[] equipTypes = new String[]{SystemConstants.Arrester, SystemConstants.SwitchFlowGroundLine, SystemConstants.SwitchFlowGroundLine,
                SystemConstants.SwitchFlowGroundLine, SystemConstants.SwitchFlowGroundLine, SystemConstants.SwitchSeparate,
                SystemConstants.SwitchSeparate, SystemConstants.Switch, SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate,
                SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate,
                SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate,
                SystemConstants.SwitchSeparate, SystemConstants.Switch, SystemConstants.Switch, SystemConstants.VolsbTransformer,
                SystemConstants.VolsbTransformer, SystemConstants.VolsbTransformer, SystemConstants.VolsbTransformer,
                SystemConstants.MotherLine, SystemConstants.MotherLine, SystemConstants.MotherLine, SystemConstants.MotherLine, SystemConstants.InOutLine,
                SystemConstants.PowerTransformer, SystemConstants.ElecShock,
                SystemConstants.ElecCapacity, SystemConstants.PowerTransformer, SystemConstants.EarthingTransformer, SystemConstants.PowerTransformer,
                SystemConstants.Term };
        String[] keyWords = new String[]{"避雷器", "接地开关", "接地刀闸", "接地闸刀", "地刀", "隔离开关", "隔离刀闸", "小车开关",
                "小车", "线路刀闸", "副母刀闸", "正母刀闸", "线路闸刀", "副母闸刀", "正母闸刀", "刀闸", "闸刀", "断路器",
                "开关", "PT", "正母压变", "副母压变", "压变", "母线", "正母", "副母", "母", "线", "主变", "电抗器", "电容器", "#变", "接地变", "机", "站用变"};
        for (int i = 0; i < keyWords.length; i++) {
            if (deviceName.lastIndexOf(keyWords[i]) >= 0) {
                equipType = equipTypes[i];
                equipTypeName = keyWords[i];
                break;
            }
        }
        if (equipTypeNameFlag) {
            return equipTypeName;
        }
        return equipType;
    }
    
    /**
     * 判断是否是二次设备
     * @param deviceName 设备名
     * @return true-是，false-不是
     */
    public static boolean isSecondaryDevice(String deviceName) {
        return deviceName.endsWith("保护") || deviceName.endsWith("备自投")||deviceName.endsWith("重合闸");
    }
    
    /**
     * 根据 变电站名、设备名、设备类型 找到对应设备
     *
     * @param deviceName  设备名；如果是厂站设备，可传null
     * @param stationName 变电站名
     * @param equipType   设备类型
     * @return 匹配的设备，或返回null
     */
    


    public static PowerDevice getMatchedDevice(String deviceName, String stationName, String equipType) {
        CZPService czpService = CZPService.getService();

        //对“设备名”、“设备类型”、"厂站名"补充处理
        if (StringUtils.isNotEmpty(deviceName)) {
            deviceName = handleDeviceName(deviceName);

            //线路设备、开关设备，去除设备名的电压等级后进行匹配；母线压变不可去除电压等级进行比较
            if (SystemConstants.InOutLine.equals(equipType) || SystemConstants.Switch.equals(equipType)) {
                int voltageIndex = deviceName.indexOf("kV");
                if (voltageIndex > 0) deviceName = deviceName.substring(voltageIndex + 2);
            }

            if (StringUtils.isEmpty(equipType)) {
                equipType = determineEquipType(deviceName, false);
            }
        }

        if (SystemConstants.Arrester.equals(equipType)) {//避雷器设备
            PowerDevice powerDevice = new PowerDevice();
            powerDevice.setPowerDeviceName(deviceName);
            powerDevice.setPowerStationName(stationName);
            PowerDevice station = getMatchedDevice(stationName,stationName,SystemConstants.PowerStation);
            if (station != null){
                powerDevice.setPowerStationName(czpService.getDevName(station));
            }
            return powerDevice;
        }

        PowerDevice matchedDevice = null;
        for (PowerDevice station : CBSystemConstants.getMapPowerStation().values()) {
            String stationNameToMatch = czpService.getDevName(station);
            if (stationNameToMatch.equals(stationName)) {
                if (SystemConstants.PowerStation.equals(equipType)) return station;//厂站设备

                HashMap<String, PowerDevice> stationDeviceMap = CBSystemConstants.getStationPowerDevices(station.getPowerDeviceID());
                //表中数据存在“江苏.下蜀变”、“江苏.下蜀变保信2”的厂站名，但“江苏.下蜀变保信2"厂站是无效数据，其T_EQUIPINFO没有关联的设备，
                // 通过java.lang.String#contains方法会误匹配到“江苏.下蜀变保信2"厂站，但如果匹配到了，其stationDeviceMap变量值会为null
                if (stationDeviceMap == null) continue;
                findDeviceLabel:
                for (PowerDevice powerDevice : stationDeviceMap.values()) {
                    String powerDeviceName = czpService.getDevName(powerDevice);
                    String deviceType = powerDevice.getDeviceType();
                    String powerDeviceNameOrigin = powerDevice.getPowerDeviceName();
                    if (powerDeviceNameOrigin.contains("/"))
                        powerDeviceNameOrigin = powerDeviceNameOrigin.substring(powerDeviceNameOrigin.indexOf('/')+1);
                    if (powerDeviceName.contains(deviceName) || powerDeviceNameOrigin.contains(deviceName)) {//存在设备初始名称与用户填写名称相同的情况
                        if (StringUtils.isNotEmpty(equipType) && !equipType.equals(deviceType)) {
                            continue;
                        } else {
                            String[] suffixNames = {"A相", "B相", "C相"};
                            for (String suffixName : suffixNames) {//如果设备名以这些文字结尾，则进行过滤；有如：访永4Y63开关A相、访永4Y63开关 设备名，需要取开关，而不是某相
                                if (powerDeviceName.endsWith(suffixName)) continue findDeviceLabel;
                            }
                            matchedDevice = powerDevice;
                            break;
                        }
                    }
                }
                //break; //发现江苏和镇江存在变电站同名的情况，所以不break，继续遍及查找
            }
        }

        if (matchedDevice == null) {//没匹配到设备，修改文本再进行匹配
            if (StringUtils.isNotEmpty(deviceName) && deviceName.contains("内桥")) {
                deviceName = deviceName.replace("内桥", "分段");
                return getMatchedDevice(deviceName, stationName, equipType);
            }

            if (StringUtils.isNotEmpty(stationName) && !stationName.endsWith("变") && !stationName.endsWith("（老）")) {
                stationName = stationName + "变";
                return getMatchedDevice(deviceName, stationName, equipType);
            }

            Pattern pattern = Pattern.compile("(.*)(I|II|III|IV|Ⅰ|Ⅱ|Ⅲ|Ⅳ)(.*)");
            Matcher matcher = pattern.matcher(deviceName);
            if (matcher.find() && !deviceName.contains("段")) {
                deviceName = matcher.group(1) + matcher.group(2) + "段" + matcher.group(3);
                return getMatchedDevice(deviceName, stationName, equipType);
            }

            pattern = Pattern.compile("IV|III|II|I");
            matcher = pattern.matcher(deviceName);
            if (matcher.find()) {
                deviceName = deviceName.replaceAll("IV", "Ⅳ");
                deviceName = deviceName.replaceAll("III", "Ⅲ");
                deviceName = deviceName.replaceAll("II", "Ⅱ");
                deviceName = deviceName.replaceAll("I", "Ⅰ");
                return getMatchedDevice(deviceName, stationName, equipType);
            }

            if(SystemConstants.Switch.equals(equipType)||deviceName.contains("主变")){//特别处理写为【2503#3主变开关】，实际应为【#3主变2503开关】的情况
                Pattern patternTFSwitch = Pattern.compile("(\\d+)#(\\d)主变开关");
                Matcher matcherTFSwitch = patternTFSwitch.matcher(deviceName);
                if (matcherTFSwitch.find()){
                    deviceName = deviceName.replaceAll("(\\d+)#(\\d)主变开关","#$2主变$1开关");
                    return getMatchedDevice(deviceName, stationName, equipType);
                }
            }
            
            if(SystemConstants.Switch.equals(equipType)||deviceName.contains("主变")){//特别处理写为【2503#3主变开关】，实际应为【#3主变2503开关】的情况
                Pattern patternTFSwitch = Pattern.compile("(2\\d+)#(\\d)主变开关");
                Matcher matcherTFSwitch = patternTFSwitch.matcher(deviceName);
                if (matcherTFSwitch.find()){
                    deviceName = deviceName.replaceAll("(\\d+)#(\\d)主变开关","#$2主变$1开关");
                    return getMatchedDevice(deviceName, stationName, equipType);
                }
            }

            if (stationName.matches("^前.*")){//特别处理【前港务变将】
                stationName = stationName.replaceFirst("前","");
                return getMatchedDevice(deviceName, stationName, equipType);
            }


            if (!stationName.endsWith("（老）")){
                stationName = stationName + "（老）";//不带后缀匹配不到，则匹配老站
                return getMatchedDevice(deviceName, stationName, equipType);
            }
        }

        return matchedDevice;
    }
}
