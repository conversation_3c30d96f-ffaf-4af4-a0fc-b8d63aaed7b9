package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooBZB implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if("边主变".equals(tempStr)){
			List<PowerDevice> gycmlkgList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true,true);

			if(gycmlkgList.size() == 1){
				 return true;
			}
			
		}
        return false;
	}
}
