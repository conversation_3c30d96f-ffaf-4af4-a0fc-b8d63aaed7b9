package com.tellhow.czp.app.yndd.wordcard.bs;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBSXLJHC  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("保山线路解环侧".equals(tempStr)){
			if(curDev.getPowerVoltGrade() >= 220){
				List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
				
				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					String stationName = CZPService.getService().getDevName(station);
					
					replaceStr = "（从"+stationName+"侧解环）";
					break;
				}
			}
		}
		
		return replaceStr;
	}

}
