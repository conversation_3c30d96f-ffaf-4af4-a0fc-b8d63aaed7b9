package com.tellhow.czp.app.yndd.view;

import javax.swing.JPanel;

import com.tellhow.czp.app.CZPImpl;

import czprule.model.CodeNameModel;

public abstract class OperateTicketSGP2 extends JPanel{

	protected static OperateTicketSGP2 sgpTicket;
	public  static boolean ops = false;

	public static OperateTicketSGP2 getInstance() {
		if (sgpTicket == null) {
			
		
			sgpTicket=(OperateTicketSGP2)CZPImpl.getInstance("OperateTicketSGP2");
//			if(sgpTicket == null)
//				sgpTicket = new OperateTicketSGPDefault();

			return sgpTicket;
		}
		else
			return sgpTicket;
	}
	
	public void initTable(CodeNameModel cnm) {
		
	}
	/**
	 * kind 宗令票 ，逐项票 -- 操作票界面到转拟至手工开票时操作票类型
	 * @param cnm
	 * @param kind
	 */
	public void initTable(CodeNameModel cnm, String kind) {
		// TODO Auto-generated method stub
		
	}
}
