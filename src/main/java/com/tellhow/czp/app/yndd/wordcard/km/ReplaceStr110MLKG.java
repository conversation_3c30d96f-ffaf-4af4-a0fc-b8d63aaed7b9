package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStr110MLKG  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("110母联开关".equals(tempStr)){
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(stationDev,  SystemConstants.PowerTransformer, "", true, true, true);
			if(zbList.size()>0){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(zbList.get(0),  SystemConstants.MotherLine, "", true, true, true);
				PowerDevice mxDevice = null;
				for(PowerDevice mx:mxList){
					if(mx.getPowerVoltGrade()==110&&!mx.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
						mxDevice= mx;
					}
				}
				if(mxDevice!=null){
					List<PowerDevice> mlswList = RuleExeUtil.getDeviceList(mxDevice, SystemConstants.Switch, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
					for(PowerDevice mlsw:mlswList){
						if(RuleExeUtil.isSwitchDoubleML(mlsw)){
							return CZPService.getService().getDevName(mlsw);
						}
					}
				}
				
			}
			
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
