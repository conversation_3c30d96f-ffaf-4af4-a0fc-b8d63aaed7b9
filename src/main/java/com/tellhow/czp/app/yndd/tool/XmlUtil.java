package com.tellhow.czp.app.yndd.tool;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.DOMReader;

import com.sun.org.apache.bcel.internal.generic.NEW;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import tbp.common.util.StringUtil;

public class XmlUtil {

	public static String getXMLcode(String str){
		if(str.toUpperCase().contains("UTF-8")){
			return "UTF-8";
		}else{
			return "GBK";
		}
	}
	
	public static Element getItemByCbid(Element datas, String cbid) {
		Element target=null;
		for (Element element : (List<Element>)datas.elements()) {
			Element cbElement=element.element("cbid");
			if(cbElement!=null){
				String checkid=cbElement.getTextTrim();
				if(checkid.equals(cbid)){
					target=element;
				}
			}
		}
		return target;
	}
	/**
	 * Xml转换为List<Map<String,String>> 对象
	 * */
	public static  List<Map<String, String>> getReqInfo(String arg) {
		List<Map<String, String>> voLists = new ArrayList<Map<String, String>>();
		String xmlCode = XmlUtil.getXMLcode(arg);
		try {
			InputStream is = new ByteArrayInputStream(arg.getBytes(xmlCode));
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder db = dbf.newDocumentBuilder();
			org.w3c.dom.Document document = db.parse(is);
			DOMReader domReader = new DOMReader();
			Document ret = domReader.read(document);
			Element root = ret.getRootElement();
			//获取ITEM节点DOM
			List<Element> itemLists =root.elements("ITEM");
			//System.out.println(itemLists);
			for (int i = 0; i <itemLists.size(); i++) {
				Map<String, String> mapInfo =new HashMap<String,String>();
				Element element = itemLists.get(i);
				List<Element> elist = element.elements();
				for (int j = 0; j < elist.size(); j++) {
					Element el = elist.get(j);
					//将节点名称与值放入集合
					mapInfo.put(el.getName(), el.getTextTrim());				
				}
				voLists.add(mapInfo);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return voLists;
	}
	
	//根据监控票ID获取操作票明细XML字符串
	public static String getCzpXml(String jkpid,String isrealtime){
		String sql="select cardorder,czdw ,mxid,cznr,f_zbid from "+CBSystemConstants.opcardUser+"t_a_czpmx where  f_zbid in("
				+" select czpzbid from "+CBSystemConstants.opcardUser+"t_a_jkpzb where zbid = '"+jkpid+"') order by cardorder ";

		
		StringBuffer xmlData = new StringBuffer("");
		List<Map<String, Object>>  czrwList = DBManager.queryForList(sql);
		if(czrwList!=null && czrwList.size()>0){
			xmlData.append("<?xml version=\"1.0\" encoding=\"GB2312\" ?>");
			xmlData.append("<Datas>");
			for (int i = 0; i < czrwList.size(); i++) {
				String changzhan = StringUtils.ObjToString(czrwList.get(i).get("CZDW"));
				String caozuozhiling = StringUtils.ObjToString(czrwList.get(i).get("CZNR"));
				String cbid = StringUtils.ObjToString(czrwList.get(i).get("MXID"));
				String zbid = StringUtils.ObjToString(czrwList.get(i).get("F_ZBID"));
				String ischeck = "1";
				xmlData.append("	<ITEM>");
				xmlData.append("		<changzhan>").append(changzhan).append("</changzhan>");
				xmlData.append("		<caozuozhiling>").append(caozuozhiling).append("</caozuozhiling>");
				xmlData.append("		<cbid>").append(cbid).append("</cbid>");
				xmlData.append("		<zbid>").append(zbid).append("</zbid>");
				xmlData.append("		<ischeck>").append(ischeck).append("</ischeck>");
				xmlData.append("		<isrealtime>").append(isrealtime).append("</isrealtime>");
				xmlData.append("	</ITEM>");
			}
			xmlData.append("</Datas>");
		}
		return xmlData.toString();
	}

	//根据监控票ID获取操作票明细XML字符串(金华)
	public static String getJhCzpXml(String jkpid,String isrealtime){
		String sql="select cardorder,czdw ,mxid,cznr,f_zbid from "+CBSystemConstants.opcardUser+"t_czp_mx where  f_zbid in("
				+" select czpzbid from "+CBSystemConstants.opcardUser+"th_dc_dci_dcs_jkpzb where zbid = '"+jkpid+"') order by cardorder ";


		StringBuffer xmlData = new StringBuffer("");
		List<Map<String, Object>>  czrwList = DBManager.queryForList(sql);
		if(czrwList!=null && czrwList.size()>0){
			xmlData.append("<?xml version=\"1.0\" encoding=\"GB2312\" ?>");
			xmlData.append("<Datas>");
			for (int i = 0; i < czrwList.size(); i++) {
				String changzhan = StringUtils.ObjToString(czrwList.get(i).get("CZDW"));
				String caozuozhiling = StringUtils.ObjToString(czrwList.get(i).get("CZNR"));
				String cbid = StringUtils.ObjToString(czrwList.get(i).get("MXID"));
				String zbid = StringUtils.ObjToString(czrwList.get(i).get("F_ZBID"));
				String ischeck = "1";
				xmlData.append("	<ITEM>");
				xmlData.append("		<changzhan>").append(changzhan).append("</changzhan>");
				xmlData.append("		<caozuozhiling>").append(caozuozhiling).append("</caozuozhiling>");
				xmlData.append("		<cbid>").append(cbid).append("</cbid>");
				xmlData.append("		<zbid>").append(zbid).append("</zbid>");
				xmlData.append("		<ischeck>").append(ischeck).append("</ischeck>");
				xmlData.append("		<isrealtime>").append(isrealtime).append("</isrealtime>");
				xmlData.append("	</ITEM>");
			}
			xmlData.append("</Datas>");
		}
		return xmlData.toString();
	}

	//根据监控票ID获取监控票明细XML字符串(金华)
	public static String getJhJkpXml(String jkpid){
		String sql="select z.zbid as F_ZBID,m.mx_id MXID,m.cardorder,z.jkdw as CZDW ,m.cznr from "+CBSystemConstants.opcardUser+"th_dc_dci_dcs_jkpzb z,"+CBSystemConstants.opcardUser+"th_dc_dci_dcs_jkpmx m "
				+ "where z.zbid = m.zb_id and  z.zbid = '"+jkpid+"'  order by m.cardorder";

		StringBuffer xmlData = new StringBuffer("");
		List<Map<String, Object>>  czrwList = DBManager.queryForList(sql);
		if(czrwList!=null && czrwList.size()>0){
			xmlData.append("<?xml version=\"1.0\" encoding=\"GB2312\" ?>");
			xmlData.append("<Datas>");
			for (int i = 0; i < czrwList.size(); i++) {
				String changzhan = StringUtils.ObjToString(czrwList.get(i).get("CZDW"));
				String caozuozhiling = StringUtils.ObjToString(czrwList.get(i).get("CZNR"));
				String cbid = StringUtils.ObjToString(czrwList.get(i).get("MXID"));
				String zbid = StringUtils.ObjToString(czrwList.get(i).get("F_ZBID"));
				String ischeck = "1";
				xmlData.append("	<ITEM>");
				xmlData.append("		<changzhan>").append(changzhan).append("</changzhan>");
				xmlData.append("		<caozuozhiling>").append(caozuozhiling).append("</caozuozhiling>");
				xmlData.append("		<cbid>").append(cbid).append("</cbid>");
				xmlData.append("		<zbid>").append(zbid).append("</zbid>");
				xmlData.append("		<ischeck>").append(ischeck).append("</ischeck>");
				xmlData.append("		<isrealtime>").append("1").append("</isrealtime>");
				xmlData.append("	</ITEM>");
			}
			xmlData.append("</Datas>");
		}
		return xmlData.toString();
	}

	//根据监控票ID获取监控票明细XML字符串
	public static String getJkpXml(String jkpid){
		String sql="select z.zbid as F_ZBID,m.mxid,m.czsx,z.jkdw as CZDW ,m.cznr from "+CBSystemConstants.opcardUser+"t_a_jkpzb z,"+CBSystemConstants.opcardUser+"t_a_jkpmx m "
				+ "where z.zbid = m.f_zbid and  z.zbid = '"+jkpid+"'  order by m.czsx";
		
		StringBuffer xmlData = new StringBuffer("");
		List<Map<String, Object>>  czrwList = DBManager.queryForList(sql);
		if(czrwList!=null && czrwList.size()>0){
			xmlData.append("<?xml version=\"1.0\" encoding=\"GB2312\" ?>");
			xmlData.append("<Datas>");
			for (int i = 0; i < czrwList.size(); i++) {
				String changzhan = StringUtils.ObjToString(czrwList.get(i).get("CZDW"));
				String caozuozhiling = StringUtils.ObjToString(czrwList.get(i).get("CZNR"));
				String cbid = StringUtils.ObjToString(czrwList.get(i).get("MXID"));
				String zbid = StringUtils.ObjToString(czrwList.get(i).get("F_ZBID"));
				String ischeck = "1";
				xmlData.append("	<ITEM>");
				xmlData.append("		<changzhan>").append(changzhan).append("</changzhan>");
				xmlData.append("		<caozuozhiling>").append(caozuozhiling).append("</caozuozhiling>");
				xmlData.append("		<cbid>").append(cbid).append("</cbid>");
				xmlData.append("		<zbid>").append(zbid).append("</zbid>");
				xmlData.append("		<ischeck>").append(ischeck).append("</ischeck>");
				xmlData.append("		<isrealtime>").append("1").append("</isrealtime>");
				xmlData.append("	</ITEM>");
			}
			xmlData.append("</Datas>");
		}
		return xmlData.toString();
	}
	public static String insertJHJG(String str,String jhType){
		String ret = "3";//通过
		String sql ="";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		
		List<Map<String, String>> JHJGlist =  getReqInfo(str);
		for(int i=0;i<JHJGlist.size();i++){
//			System.out.println("-------------JHJGlist.size()："+JHJGlist.size()+"------------------------");
			if(i==0){
				String zbid = StringUtils.ObjToString(JHJGlist.get(i).get("zbid"));
				sql = "delete from "+CBSystemConstants.opcardUser+"t_a_czpjh  where czpzbid = '"+zbid+"' and jhlx = '"+jhType+"' ";
				DBManager.execute(sql);
//				System.out.println("-------------deleteSQL："+sql+"------------------------");
			}
			String message = StringUtils.ObjToString(JHJGlist.get(i).get("remessage"));
			sql = "insert into "+CBSystemConstants.opcardUser+"t_a_czpjh (jhid,czpzbid,czpmxid,czdw,cznr,jhlx,jhjg,cardorder,jhsj) "
					+ " values ('"+StringUtils.getUUID()+"','"+StringUtils.ObjToString(JHJGlist.get(i).get("zbid"))+"','"+StringUtils.ObjToString(JHJGlist.get(i).get("cbid"))+"'"
							+ ",'"+StringUtils.ObjToString(JHJGlist.get(i).get("changzhan"))+"','"+StringUtils.ObjToString(JHJGlist.get(i).get("caozuozhiling1"))+"','"+jhType+"','"
							+message+"','"+(i+1)+"','"+sdf.format(new Date())+"') ";
			DBManager.execute(sql);
//			System.out.println("-------------insertSQL："+sql+"------------------------");
			if(!message.equals("成功")&&!message.contains("不需要")&&!message.contains("返校正确")){
				ret ="4";//不通过
			}
		}
		
		return ret;
	}

	//根据操作票ID获取操作票明细XML字符串
	public static String getCzpMxXml(String czpid,String isrealtime){

		String sql="select m.f_zbid as F_ZBID,m.mxid,m.czsn as CZDW ,m.cznr,m.czdwids from "+CBSystemConstants.opcardUser+"t_a_czpmx m where  m.f_zbid= '"+czpid+"' order by to_number(m.zlxh)";
		String zbSql = "select areano,czrw from "+CBSystemConstants.opcardUser+"t_a_czpzb where zbid = '"+czpid+"'";
		StringBuffer xmlData = new StringBuffer("");
		List<Map<String, Object>>  czrwList = DBManager.queryForList(sql);
		Map<String,Object> map = DBManager.queryForMap(zbSql);
		if(czrwList!=null && czrwList.size()>0){
			xmlData.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>");
			xmlData.append("<Datas>");
			if(map != null){
				String czrw = map.get("czrw")!=null?map.get("czrw").toString():"";
				String areano = map.get("areano")!=null?map.get("areano").toString():"";
				xmlData.append("		<CZRW>").append(czrw).append("</CZRW>");
				//区域编码
				xmlData.append("		<unit>").append(areano).append("</unit>");
			}
			for (int i = 0; i < czrwList.size(); i++) {
				String changzhan = StringUtils.ObjToString(czrwList.get(i).get("CZDW"));
				String caozuozhiling = StringUtils.ObjToString(czrwList.get(i).get("CZNR"));
				String czdwids = StringUtils.ObjToString(czrwList.get(i).get("czdwids"));
				if(StringUtil.isBlank(changzhan) && StringUtil.isBlank(caozuozhiling)){
					continue;
				}
				String cbid = StringUtils.ObjToString(czrwList.get(i).get("MXID"));
				String zbid = StringUtils.ObjToString(czrwList.get(i).get("F_ZBID"));
				String ischeck = "1";
				xmlData.append("	<ITEM>");
				xmlData.append("		<changzhan>").append(changzhan).append("</changzhan>");
				xmlData.append("		<caozuozhiling>").append(caozuozhiling).append("</caozuozhiling>");
				xmlData.append("		<cbid>").append(cbid).append("</cbid>");
				xmlData.append("		<zbid>").append(zbid).append("</zbid>");
				xmlData.append("		<ischeck>").append(ischeck).append("</ischeck>");
				xmlData.append("		<czdwids>").append(czdwids).append("</czdwids>");
				xmlData.append("		<isrealtime>").append(isrealtime).append("</isrealtime>");
				xmlData.append("	</ITEM>");
			}
			xmlData.append("</Datas>");
		}
		return xmlData.toString();
	}
	//根据操作票ID获取操作票明细XML字符串
		public static String getCzpMxXmlOld(String czpid,String isrealtime){

			String sql="select m.f_zbid as F_ZBID,m.mxid,m.czdw as CZDW ,m.cznr from "+CBSystemConstants.opcardUser+"t_a_czpmx m where  m.f_zbid= '"+czpid+"' order by to_number(m.cardorder)";
			String zbSql = "select czrw from "+CBSystemConstants.opcardUser+"t_a_czpzb where zbid = '"+czpid+"'";
			StringBuffer xmlData = new StringBuffer("");
			List<Map<String, Object>>  czrwList = DBManager.queryForList(sql);
			String czrw = DBManager.queryForString(zbSql);
			if(czrwList!=null && czrwList.size()>0){
				xmlData.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>");
				xmlData.append("<Datas>");
				if(czrw != null){
					xmlData.append("		<CZRW>").append(czrw).append("</CZRW>");
				}
				for (int i = 0; i < czrwList.size(); i++) {
					String changzhan = StringUtils.ObjToString(czrwList.get(i).get("CZDW"));
					String caozuozhiling = StringUtils.ObjToString(czrwList.get(i).get("CZNR"));
					if(StringUtil.isBlank(changzhan) && StringUtil.isBlank(caozuozhiling)){
						continue;
					}
					String cbid = StringUtils.ObjToString(czrwList.get(i).get("MXID"));
					String zbid = StringUtils.ObjToString(czrwList.get(i).get("F_ZBID"));
					String ischeck = "1";
					xmlData.append("	<ITEM>");
					xmlData.append("		<changzhan>").append(changzhan).append("</changzhan>");
					xmlData.append("		<caozuozhiling>").append(caozuozhiling).append("</caozuozhiling>");
					xmlData.append("		<cbid>").append(cbid).append("</cbid>");
					xmlData.append("		<zbid>").append(zbid).append("</zbid>");
					xmlData.append("		<ischeck>").append(ischeck).append("</ischeck>");
					xmlData.append("		<isrealtime>").append(isrealtime).append("</isrealtime>");
					xmlData.append("	</ITEM>");
				}
				xmlData.append("</Datas>");
			}
			return xmlData.toString();
		}
	
}
