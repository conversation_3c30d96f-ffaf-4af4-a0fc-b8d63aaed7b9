package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTMXHHDD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通母线合环调电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> highVoltMlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> curVoltMlkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				 PowerDevice dev = it.next();
				
				 if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						highVoltMlkgList.add(dev);
					}
				 }
				 
				 if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						curVoltMlkgList.add(dev);
					}
				 }
				 
				 if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					 zbList.add(dev);
				 }
			}
			
			PowerDevice powerdev = new PowerDevice();
			PowerDevice powercutdev = new PowerDevice();
			
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,false, false, true);
			
			mxList.add(curDev);
			
			RuleExeUtil.swapDeviceList(mxList);
			
			List<PowerDevice> zbkgList = RuleExeUtil.getDeviceList(curDev,null ,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC+","+CBSystemConstants.RunTypeSwitchML, "", false, false, false, false,true);
			
			for(PowerDevice dev : zbkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
					powerdev = dev;
				}
			}

			for(PowerDevice dev : zbkgList){
				if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
					powercutdev = dev;
				}
			}
			
			replaceStr += "昭通地调@核实"+stationName+CZPService.getService().getDevName(highVoltMlkgList)+"处运行/r/n";
			replaceStr += CommonFunction.getHhContent(powerdev, "昭通地调", stationName);
			replaceStr += "昭通地调@遥控断开"+stationName+CZPService.getService().getDevName(powercutdev)+"/r/n";
			
			String sql = "SELECT SWITCHID,SWITCHNAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONBOARD WHERE STATIONID = '"+station.getPowerDeviceID()+"'";
			List<Map<String,String>> list = DBManager.queryForList(sql);
			
			List<String> switchidList = new ArrayList<String>();
			
			for(Map<String,String> map : list){
				String switchid = StringUtils.ObjToString(map.get("SWITCHID"));
				
				switchidList.add(switchid);
			}
			
			for(PowerDevice dev : curVoltMlkgList){
				if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0")){//分段开关在合位
					for(PowerDevice zb : zbList){
						List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchByVol(zb, curDev.getPowerVoltGrade());
						
						for(PowerDevice zbzyckg : zbzyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(zbzyckg).equals("1")){//投入全部的断路器联跳功能
								replaceStr += "投入"+CZPService.getService().getDevName(zb)+"间隙保护联跳";
								
								String switchnames = "";
								
								for(Map<String,String> map : list){
									String switchname = StringUtils.ObjToString(map.get("SWITCHNAME"));
									
									switchnames += switchname+"、";
								}
								
								if(switchnames.endsWith("、")){
									switchnames = switchnames.substring(0, switchnames.length()-1);
								}
								
								replaceStr += switchnames+"功能/r/n";
							}
						}
						
						for(PowerDevice zbzyckg : zbzyckgList){
							if(RuleExeUtil.getDeviceBeginStatus(zbzyckg).equals("0")){//退出全部的断路器联跳功能
								replaceStr += "退出"+CZPService.getService().getDevName(zb)+"间隙保护联跳";
								
								String switchnames = "";
								
								for(Map<String,String> map : list){
									String switchname = StringUtils.ObjToString(map.get("SWITCHNAME"));
									
									switchnames += switchname+"、";
								}
								
								if(switchnames.endsWith("、")){
									switchnames = switchnames.substring(0, switchnames.length()-1);
								}
								
								replaceStr += switchnames+"功能/r/n";
							}
						}
					}
				}else if(RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("1")){//分段开关在分位
					List<PowerDevice> tagDevList = new ArrayList<PowerDevice>();
					
					for(PowerDevice zb : zbList){
						List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchByVol(zb, curDev.getPowerVoltGrade());
						
						for(PowerDevice zbzyckg : zbzyckgList){
							if(RuleExeUtil.getDeviceEndStatus(zbzyckg).equals("1")){
								List<PowerDevice> motherLineList = RuleExeUtil.getDeviceList(zbzyckg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
								
								if(motherLineList.size()>0){
									List<PowerDevice> kgList = RuleExeUtil.getDeviceList(motherLineList.get(0) ,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, true, true, true);

									for(PowerDevice kg : kgList){
										if(switchidList.contains(kg.getPowerDeviceID())){
											tagDevList.add(kg);
										}
									}
								}
							}
						}
					}
					
					
					for(PowerDevice zb : zbList){
						List<PowerDevice> zbzyckgList = RuleExeUtil.getTransformerSwitchByVol(zb, curDev.getPowerVoltGrade());
						
						for(PowerDevice zbzyckg : zbzyckgList){
							if(!RuleExeUtil.isDeviceChanged(zbzyckg)){
								replaceStr += "投入"+CZPService.getService().getDevName(zb)+"间隙保护联跳";
								
								String switchnames = "";
								
								for(Map<String,String> map : list){
									String switchid = StringUtils.ObjToString(map.get("SWITCHID"));
									String switchname = StringUtils.ObjToString(map.get("SWITCHNAME"));
									
									for(PowerDevice tagDev : tagDevList){
										if(tagDev.getPowerDeviceID().equals(switchid)){
											switchnames += switchname+"、";
										}
									}
								}
								
								if(switchnames.endsWith("、")){
									switchnames = switchnames.substring(0, switchnames.length()-1);
								}
								
								replaceStr += switchnames+"功能/r/n";
							}
						}
						
						for(PowerDevice zbzyckg : zbzyckgList){
							if(RuleExeUtil.getDeviceEndStatus(zbzyckg).equals("1")){
								replaceStr += "退出"+CZPService.getService().getDevName(zb)+"间隙保护联跳";
								
								String switchnames = "";
								
								for(Map<String,String> map : list){
									String switchid = StringUtils.ObjToString(map.get("SWITCHID"));
									String switchname = StringUtils.ObjToString(map.get("SWITCHNAME"));
									
									for(PowerDevice tagDev : tagDevList){
										if(tagDev.getPowerDeviceID().equals(switchid)){
											switchnames += switchname+"、";
										}
									}
								}
								
								if(switchnames.endsWith("、")){
									switchnames = switchnames.substring(0, switchnames.length()-1);
								}
								
								replaceStr += switchnames+"功能/r/n";
							}
						}
					}
				}
			}
			
			String filter = CZPImpl.getPropertyValue("HHProtectedLocation");
			
			String[] filterArr = filter.split(";");
			
			List<String> filterList =  Arrays.asList(filterArr);
			

			List<PowerDevice> tagkgList = new ArrayList<PowerDevice>();
			
			tagkgList.add(powerdev);
			tagkgList.add(powercutdev);

			for(Iterator<PowerDevice> itor = tagkgList .iterator();itor.hasNext();){
				PowerDevice dev = itor.next();
				
				if(!filterList.contains(dev.getPowerDeviceID())){
					itor.remove();
				}
			}
			
			if(tagkgList.size()>0){
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, tagkgList , "请选择需要调整保护定值区的断路器：");
				List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

				for(PowerDevice dev : choosedyckgList){
					PowerDevice stationNew = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					
					replaceStr += CZPService.getService().getDevName(stationNew)+"@将"+CZPService.getService().getDevName(dev)+"保护定值区由0X区切换至0X区/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
