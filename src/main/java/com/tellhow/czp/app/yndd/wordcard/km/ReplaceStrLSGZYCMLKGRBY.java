package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.km.DeviceOperateBeforeMLKGExecute;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**  

* <p>Description: </p>  
* <AUTHOR>
* @date 2021年9月13日    
*/
public class ReplaceStrLSGZYCMLKGRBY implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String result = "";

		if("落实高中压侧母联开关热备用".equals(tempStr)) {
			int curvolt = (int)curDev.getPowerVoltGrade();
			int gycvolt = 0;
			int zycvolt = 0;
			int dycvolt = 0;

			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			List<PowerDevice>  dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice>  zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice>  gycmlkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					gycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "high");
					zycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "middle");
					dycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "low");

				}
			}
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if(dev.getDeviceType().equals(SystemConstants.Switch)
						&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dycvolt==dev.getPowerVoltGrade()){
//						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							dycmlkgList.add(dev);
//						}
					}else if(zycvolt==dev.getPowerVoltGrade()){
//						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							zycmlkgList.add(dev);
//						}
					}else if(gycvolt==dev.getPowerVoltGrade()){
//						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							gycmlkgList.add(dev);
//						}
					}
				}
			}
			
			if(dycmlkgList.size()>1){
				for(PowerDevice pd : dycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(pd).equals("1")&&station.getPowerVoltGrade()!=220){
						result += "退出"+CZPService.getService().getDevName(pd)+"备自投装置/r/n";
					}
					
				}
			}else if(dycmlkgList.size()==1&&station.getPowerVoltGrade()!=220){
				if(RuleExeUtil.getDeviceBeginStatus(dycmlkgList.get(0)).equals("1")){
					result += "退出"+dycvolt+"kV备自投装置/r/n";
				}
				
			}
			
			
			if(zycmlkgList.size()>1){
				for(PowerDevice pd : zycmlkgList){
					if(RuleExeUtil.getDeviceBeginStatus(pd).equals("1")&&station.getPowerVoltGrade()!=220){
						result += "退出"+CZPService.getService().getDevName(pd)+"备自投装置/r/n";
					}
					
				}
			}else if(zycmlkgList.size()==1){
				if(RuleExeUtil.getDeviceBeginStatus(zycmlkgList.get(0)).equals("1")&&station.getPowerVoltGrade()!=220){
					result += "退出"+zycvolt+"kV备自投装置/r/n";
				}
				
			}
			
			if(gycmlkgList.size()>0&&curvolt!=dycvolt){
				if(DeviceOperateBeforeMLKGExecute.tagDevList.size()>0){
					result += "昆明地调@遥控断开"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(DeviceOperateBeforeMLKGExecute.tagDevList.get(0))+"/r/n";
				}
				if(RuleExeUtil.getDeviceBeginStatus(gycmlkgList.get(0)).equals("0")||RuleExeUtil.getDeviceBeginStatus(gycmlkgList.get(0)).equals("")){
					if(curvolt== gycvolt&&gycmlkgList.get(0).getDeviceStatus().equals("0"))
						result += "落实"+CZPService.getService().getDevName(gycmlkgList.get(0))+"已处合位/r/n";
				}else{
					result += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(gycmlkgList.get(0))+"/r/n";
				}
				
			}
			
			if(curvolt<= zycvolt&&zycmlkgList.size()>0){
				if(RuleExeUtil.getDeviceBeginStatus(zycmlkgList.get(0)).equals("0")||RuleExeUtil.getDeviceBeginStatus(zycmlkgList.get(0)).equals("")){
					result += "落实"+CZPService.getService().getDevName(zycmlkgList)+"已处合位/r/n";
				}else{
					result += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(zycmlkgList)+"/r/n";
				}
			
			}
			
			
			if(curvolt == dycvolt&&dycmlkgList.size()>0&&dycmlkgList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				if(RuleExeUtil.getDeviceBeginStatus(dycmlkgList.get(0)).equals("0")||RuleExeUtil.getDeviceBeginStatus(dycmlkgList.get(0)).equals("")){
					result += "落实"+CZPService.getService().getDevName(dycmlkgList)+"已处合位/r/n";
				}else{
					result += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(dycmlkgList)+"/r/n";
				}
			
			}
			
			if(curvolt > zycvolt){
				if(dycmlkgList.size()>1){
					for(PowerDevice pd : dycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(pd).equals("1")&&station.getPowerVoltGrade()!=220){
							result += "投入"+CZPService.getService().getDevName(pd)+"备自投装置/r/n";
						}
						
					}
				}else if(dycmlkgList.size()==1){
					if(RuleExeUtil.getDeviceBeginStatus(dycmlkgList.get(0)).equals("1")&&station.getPowerVoltGrade()!=220){
						result += "投入"+dycvolt+"kV备自投装置/r/n";
					}
					
				}
			}
		
		
			
			if(curvolt > zycvolt){
				if(zycmlkgList.size()>1){
					for(PowerDevice pd : zycmlkgList){
						if(RuleExeUtil.getDeviceBeginStatus(pd).equals("1")&&station.getPowerVoltGrade()!=220){
							result += "投入"+CZPService.getService().getDevName(pd)+"备自投装置/r/n";
						}
						
					}
				}else if(zycmlkgList.size()==1){
					if(RuleExeUtil.getDeviceBeginStatus(zycmlkgList.get(0)).equals("1")&&station.getPowerVoltGrade()!=220){
						result += "投入"+zycvolt+"kV备自投装置/r/n";
					}
					
				}
			}
		}
		if(result.equals("")){
			return null;
		}
		return result;
	}

}
