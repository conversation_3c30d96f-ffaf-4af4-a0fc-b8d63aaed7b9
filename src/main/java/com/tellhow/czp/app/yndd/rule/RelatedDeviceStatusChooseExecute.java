package com.tellhow.czp.app.yndd.rule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 关联设备目标状态选择器
 * 作    者: 郑柯
 * 开发日期: 2013年8月17日 上午10:13:38 
 */
public class RelatedDeviceStatusChooseExecute implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(CBSystemConstants.jh_tai == 1)
			return true;
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
		
		
		for (Iterator<Map.Entry<PowerDevice, String>> it = CBSystemConstants.LineTagStatus.entrySet().iterator(); it.hasNext();) {
			Map.Entry<PowerDevice, String> entry = it.next();
			
			RuleExeUtil.deviceStatusChange(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
		}
		
		return true;
	}
	

}
