package com.tellhow.czp.app.yndd.wordcard.pe;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPEKGRD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("普洱开关热倒".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> zbList = new ArrayList<PowerDevice>();
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);

			for(PowerDevice dev : mxList){
				if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					 mlkgList =  RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				}
			}
			
			for(PowerDevice mlkg : mlkgList){
				List<PowerDevice> tempList = RuleExeUtil.getDeviceList(mlkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);

				for(PowerDevice mx : tempList){
					List<PowerDevice> tempzbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);

					for(PowerDevice tempzb : tempzbList){
						if(!zbList.contains(tempzb)){
							zbList.add(tempzb);
						}
					}
				}
			}
			
			for(PowerDevice mlkg : mlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(mlkg).equals("1")){
					if(zbList.size() == 2){
						RuleExeUtil.swapDeviceList(zbList);
						
						replaceStr += stationName+"@核实"+(int)station.getPowerVoltGrade()+"kV"+CZPService.getService().getDevName(zbList)+"具备并列运行条件，负荷满足要求/r/n";
					}
					
					replaceStr += "普洱地调@遥控用"+stationName+CZPService.getService().getDevName(mlkg)+"同期合环/r/n";
				}
			}
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			
			String soucemxName = "";//源母线名称
			String tagmxName = "";//目标母线名称
			
			for(PowerDevice dev : dzList){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
					List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
					
					if(RuleExeUtil.getDeviceBeginStatus(dev).equals("0")){
						if(devList.size()>0){
							soucemxName = CZPService.getService().getDevName(devList.get(0));
						}
					}else{
						if(devList.size()>0){
							tagmxName = CZPService.getService().getDevName(devList.get(0));
						}
					}
				}
			}
			
			replaceStr += stationName+"@将"+deviceName+"由"+soucemxName+"运行倒至"+tagmxName+"运行/r/n";
			
			for(PowerDevice mlkg : mlkgList){
				if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
					replaceStr += "普洱地调@遥控断开"+stationName+CZPService.getService().getDevName(mlkg)+"/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
