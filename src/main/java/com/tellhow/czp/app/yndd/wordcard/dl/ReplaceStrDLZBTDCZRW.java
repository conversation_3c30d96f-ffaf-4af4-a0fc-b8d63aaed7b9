package com.tellhow.czp.app.yndd.wordcard.dl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDLZBTDCZRW  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("大理主变停电操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			List<PowerDevice> zbgycdzList = RuleExeUtil.getTransformerKnifeSource(curDev);
			List<PowerDevice> zbgycmxList = new ArrayList<PowerDevice>();

			List<PowerDevice> gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> gycxlkgList = new ArrayList<PowerDevice>();

			for(PowerDevice dev : zbgycdzList){
				zbgycmxList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.MotherLine);
				gycmlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
				gycxlkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true);
			}

			if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
				if(zbgycmxList.size() > 0){
					for(PowerDevice dev : zbgycmxList){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("2")){
							List<PowerDevice> yxList = new ArrayList<PowerDevice>();
							List<PowerDevice> rbyList = new ArrayList<PowerDevice>();
							
							for(PowerDevice gycxlkg : gycxlkgList){
								if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("0")){
									yxList.add(gycxlkg);
								}else if(RuleExeUtil.getDeviceBeginStatus(gycxlkg).equals("1")){
									rbyList.add(gycxlkg);
								}
							}
							
							for(PowerDevice gycmlkg : gycmlkgList){
								if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("0")){
									yxList.add(gycmlkg);
								}else if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("1")){
									rbyList.add(gycmlkg);
								}
							}
							
							
							for(PowerDevice yxkg : yxList){
								if(RuleExeUtil.getDeviceEndStatus(yxkg).equals("2")){
									replaceStr += stationName+CZPService.getService().getDevName(yxkg)+"及"+deviceName+"由运行转冷备用";
								}
							}
							
							for(PowerDevice rbykg : rbyList){
								if(RuleExeUtil.getDeviceEndStatus(rbykg).equals("2")){
									replaceStr += "，"+CZPService.getService().getDevName(rbykg)+"由热备用转冷备用/r/n";
								}
							}
						}else{
							replaceStr += stationName+deviceName+"由运行转冷备用/r/n";
						}
					}
				}else{
					replaceStr += stationName+deviceName+"由运行转冷备用/r/n";
				}
			}else if(RuleExeUtil.getDeviceEndStatus(curDev).equals("1")){
				replaceStr += stationName+deviceName+"由运行转热备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
