package com.tellhow.czp.app.yndd;

import java.awt.*;
import java.io.File;
import java.net.URL;
import java.util.Enumeration;

import javax.swing.*;
import javax.swing.plaf.FontUIResource;

import com.tellhow.czp.WorkerBTT;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonUtils;
import com.tellhow.czp.basic.ReaderConfiguration;
import com.tellhow.czp.mainframe.LoadWindow;
import com.tellhow.czp.user.UserDao;
import com.tellhow.czp.user.UserLoginInter;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.mainframe.GuiBuilder;
import com.tellhow.graphicframework.startup.StartupManager;
import com.tellhow.resource.file.frame.CallbackHander;
import com.tellhow.resource.file.logic.GraphResourceEntityLogic;
import com.tellhow.resource.file.logic.GraphResourceLogic;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
 
public class Main extends com.tellhow.czp.Main {
	
	private static LoadWindow loadWindow = null;
	
	public static void main(String[] args) {
//		if (!WorkerBTT.bindAndListen()) {
//            System.out.println("系统已经启动!");
//            if(args.length > 0)
//            	WorkerBTT.MSG_BTT = args[0];
//            WorkerBTT.sendBringToTop(); //激活已经启动的实例
//            System.exit(-1);
//        }
		
		//设置外观
		try {
			String lookAndFeel = "javax.swing.plaf.metal.MetalLookAndFeel";
			UIManager.setLookAndFeel(lookAndFeel);
		}catch(Exception ex) {
			ex.printStackTrace();
		}
		
//		//设置外观
//		try {
//			UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
//		}
//		catch(Exception ex) {
//			ex.printStackTrace();
//		}
		
		//显示启动界面
		loadWindow = new LoadWindow();
		loadWindow.loadwin();
		CBSystemConstants.splitEQ =true;
		//DBManager.isInterfaceOpened = true;
		
		//启动加载项
		StartupManager.startup();
		
		if(DBManager.isConnected()) {
			loadWindow.closeWindow();
			//显示登录窗口
			if(args.length == 0) {
				UserLoginInter.getInstance().setVisible(true);
			}else {
				String param = args[0];
				CZPService.getService().setArg(param);
			}
			
			//设置数据库用户
			//CZPService.getService().setDataBaseUser();
			
			//判断用户MAC地址
			if(CBSystemConstants.isvalidateMAC){
				new  ReaderConfiguration().judgeMacAddress();
			}
	
			
			//读取用户配置
			UserDao userdao=new UserDao();
			userdao.LoadUserLike(CBSystemConstants.getUser());
			
			//创建图形界面
			try {
				if(CBSystemConstants.isAutoLoadSVGFile.equals("1")){
					CallbackHander callbackHander = new CallbackHander() {
						public void execute() {
							init();
						}
					};
						GraphResourceLogic graphResourceLogic = new GraphResourceEntityLogic(CBSystemConstants.opcardUser,"tbp.sys.DataSource1");
						graphResourceLogic.downloadResourceFromServer(CBSystemConstants.projectID, "资源下载中....", callbackHander);
					}
				else
					init();
			}catch(Exception ex) {
				ex.printStackTrace();
			}finally {
				loadWindow.closeWindow();
			}
		}else { 
			System.exit(-1);
		}
	}
	
    public static void init() {
		try {
			//初始化图形
			SystemConstants.initMapSVGFile();
			//设置默认图形
			CZPService.getService().setMapSVGFileDefault();
			//设置图形文件类型
			SystemConstants.loadMapSVGFile();
			//关闭启动界面
			loadWindow.closeWindow();

			// 设置全局默认字体
			setGlobalFont(new Font("宋体", Font.PLAIN, 18));
			//创建主界面
			URL url = (new File("config/GUIBuilder.xml")).toURI().toURL();
			GuiBuilder.createGuiBuilder(ExampleBuilderImpl.class, url);

			String ddName = CommonUtils.getCurrentArea(false);
			String title = SystemConstants.getMainFrame().getTitle().replace("云南地调", ddName)
					.replace("-v1.0.0", " -V3.4.4"); // 替换版本号
			// 2025年7月17日18:38:57
			// 版本号以 10 进制
			SystemConstants.getMainFrame().setTitle(title);
		}
		catch(Exception ex) {
			ex.printStackTrace();
		}
     }

	public static void setGlobalFont(Font font) {
		FontUIResource fontResource = new FontUIResource(font);
		for (Enumeration<Object> keys = UIManager.getDefaults().keys(); keys.hasMoreElements();) {
			Object key = keys.nextElement();
			Object value = UIManager.get(key);
			if (value instanceof FontUIResource) {
				UIManager.put(key, fontResource);
			}
		}
	}

}
