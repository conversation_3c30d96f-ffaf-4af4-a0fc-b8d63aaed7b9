package com.tellhow.czp.app.yndd.wordcard.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZTKGTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昭通开关停电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 

			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			dzList = RuleExeUtil.sortByXLC(dzList);
			
			List<PowerDevice> stationList = new ArrayList<PowerDevice>();
			List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();

			stationList.add(station);
			String volt = (int)curDev.getPowerVoltGrade()+"kV";

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
						||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceEndStatus(dev).equals("0")){
							rbykgList.add(dev);
						}
					}
				}
			}

			// 判断厂站名称是否以数字结尾
			if (stationName.matches(".*\\d$")) {
				stationName += "=N=";
			}
			
			for(PowerDevice dev : rbykgList){
				replaceStr += CommonFunction.getLineBztResult(stationList,volt , "退出");
				replaceStr += CommonFunction.getHhContent(dev, "昭通地调", stationName);
			}

			if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("0")){
				replaceStr += "昭通地调@遥控断开"+stationName+deviceName+"/r/n";
			
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
			}else if(RuleExeUtil.getDeviceBeginStatus(curDev).equals("1")){
				if(RuleExeUtil.getDeviceEndStatus(curDev).equals("2")){
					replaceStr += stationName+"@将"+deviceName+"由热备用转冷备用/r/n";
				}
			}
		}
		
		return replaceStr;
	}

}
