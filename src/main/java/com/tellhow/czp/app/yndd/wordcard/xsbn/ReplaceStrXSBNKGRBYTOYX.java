package com.tellhow.czp.app.yndd.wordcard.xsbn;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXSBNKGRBYTOYX  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("版纳开关由热备用转运行".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station);
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true, true, true);
			
			for(PowerDevice dev : mxList){
				if(!RuleExeUtil.getDeviceBeginStatusContainNotOperate(dev).equals("0") && dev.getDeviceStatus().equals("0")){
					replaceStr += "版纳地调@遥控用"+stationName+CZPService.getService().getDevName(curDev)+"同期合环/r/n";
				}else{
					replaceStr += "版纳地调@遥控合上"+stationName+CZPService.getService().getDevName(curDev)+"/r/n";
				}
				break;
			}
		}
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return replaceStr;
	}

}
