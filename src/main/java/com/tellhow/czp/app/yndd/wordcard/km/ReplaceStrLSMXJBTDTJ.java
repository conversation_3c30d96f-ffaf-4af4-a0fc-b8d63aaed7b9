package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLSMXJBTDTJ implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("落实母线具备停电条件".equals(tempStr)) {
			List<PowerDevice> mxList =  new ArrayList<PowerDevice>();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceType().equals(SystemConstants.MotherLine)&&dev.getPowerVoltGrade() == 10){
					mxList.add(dev);
				}
			}
			
			if(mxList.size()>0){
				List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);

				if(mlkgList.size()==0){
					replaceStr +="落实"+CZPService.getService().getDevName(CBSystemConstants.getPowerStation(curDev.getPowerStationID()))+"10kV母线具备停电条件";
				}
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
			
		}
		return replaceStr;
	}


}