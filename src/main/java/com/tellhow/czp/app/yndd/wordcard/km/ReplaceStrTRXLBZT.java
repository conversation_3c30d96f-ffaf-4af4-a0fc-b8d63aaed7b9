package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTRXLBZT  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("投入线路备自投".equals(tempStr)){
			
			List<PowerDevice> xlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				
				if(dev.getPowerVoltGrade() == stationDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						mlkgList.add(dev);
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						xlkgList.add(dev);
					}
				}
			}
			
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			
			if(!station.getPowerDeviceName().equals("220kV金刀营变")){
				boolean hkg = false;//合开关
				boolean dkg = false;//断开关

				for(PowerDevice xlkg : xlkgList){
					if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("1")){
						dkg =true;
					}else if(RuleExeUtil.getDeviceEndStatus(xlkg).equals("0")){
						hkg =true;
					}
				}
				
				for(PowerDevice mlkg : mlkgList){
					if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("1")){
						dkg =true;
					}else if(RuleExeUtil.getDeviceEndStatus(mlkg).equals("0")){
						hkg =true;
					}
				}
				
				if(hkg&&dkg){
					replaceStr += "投入"+(int)stationDev.getPowerVoltGrade()+"kV备自投装置";
				}
				
			}
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
