package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDYCXLKG  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("电源侧线路开关".equals(tempStr)){
			if(CBSystemConstants.LineSource.get(curDev.getPowerDeviceID())!=null){
				List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(CBSystemConstants.LineSource.get(curDev.getPowerDeviceID()),
						SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				//合环调电
				
				List<PowerDevice> list = RuleExeUtil.getLineAllSideList(curDev);
				List<PowerDevice> lineList = new ArrayList<PowerDevice>();
				List<PowerDevice> stationList = new ArrayList<PowerDevice>();
				Map<Double,Integer> empMap = new HashMap<Double,Integer>();
				for(PowerDevice dev : list){
					PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
					PowerDevice station = CBSystemConstants.getPowerStation(sw.getPowerStationID());
					stationList.add(station);
				}
				for(PowerDevice dev : stationList) {
					if(empMap.containsKey(dev.getPowerVoltGrade())) {
						empMap.put(dev.getPowerVoltGrade(), empMap.get(dev.getPowerVoltGrade())+1);
					} else {
						empMap.put(dev.getPowerVoltGrade(), 1);
					}
				}
				double volt = 0;
				for(Double solt : empMap.keySet()) {
					if(empMap.get(solt) > 1) {
						volt = solt;
						break;
					}
				}
				for(PowerDevice dev : list){
					PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
					PowerDevice station = CBSystemConstants.getPowerStation(sw.getPowerStationID());
					if(station.getPowerVoltGrade() == volt) {
						lineList.add(dev);
					}
				}
				PowerDevice onswDevice = null;
				PowerDevice offswDevice = null;
				
				/*
				 * 合环调电
				 */
				
				if(lineList.size() == 2) {
					for(PowerDevice dev : lineList) {
						PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
						
						if(RuleExeUtil.getDeviceBeginStatus(sw).equals("0")&&offswDevice==null){
							offswDevice = sw;
						}else if(RuleExeUtil.getDeviceBeginStatus(sw).equals("1")&&onswDevice==null){
							onswDevice = sw;
						}
					}
				}
				
				
				if(onswDevice != null && offswDevice != null) {
					return  CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(onswDevice.getPowerStationID()))+CZPService.getService().getDevName(onswDevice);
				}
				
				if(xlswList.size()>1){
					PowerDevice middleswDevice = null;
					for(PowerDevice sw:xlswList){
						if(RuleExeUtil.isSwMiddleInThreeSecond(sw)){
							middleswDevice= sw;
							xlswList.remove(sw);
							break;
						}
					}
					if(middleswDevice!=null){
						if(desc.contains("断开")){
							xlswList.add(0,middleswDevice);
						}else{
							xlswList.add(middleswDevice);
						}
					}
					for(PowerDevice sw:xlswList){
						if(replaceStr.equals("")){
							replaceStr+=CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(sw.getPowerStationID()))+CZPService.getService().getDevName(sw);
						}else{
							if(desc.contains("断开")){
								replaceStr+="/r/n"+"昆明地调@遥控断开"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(sw.getPowerStationID()))+CZPService.getService().getDevName(sw);
							}else{
								if(RuleExeUtil.getDeviceEndStatus(sw).equals("0")){
									replaceStr+="/r/n"+"昆明地调@遥控合上"+CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(sw.getPowerStationID()))+CZPService.getService().getDevName(sw);
								}
							}
							
						}
					}
				
				}
				else if(xlswList.size()>0){
					return CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(xlswList.get(0).getPowerStationID()))+CZPService.getService().getDevName(xlswList.get(0));
				}
			}
			
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}
