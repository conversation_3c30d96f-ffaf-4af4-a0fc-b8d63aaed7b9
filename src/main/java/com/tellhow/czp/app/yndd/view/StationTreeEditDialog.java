package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.List;
import java.util.Map;

import javax.swing.ButtonGroup;
import javax.swing.ComboBoxModel;
import javax.swing.JButton;
import javax.swing.JLabel;
import javax.swing.JRadioButton;
import javax.swing.JTextField;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

import javax.swing.JComboBox;
import javax.swing.DefaultComboBoxModel;
import javax.swing.event.ListDataListener;



public class StationTreeEditDialog extends javax.swing.JDialog{
	private String stationId = "";
	private String stationName = "";
	private JRadioButton yesRadioButton ;
	private JRadioButton noRadioButton ;
	private JTextField textField;
	private JComboBox voltcomboBox = new JComboBox();
	private JComboBox stationtypecomboBox = new JComboBox();
	
	public StationTreeEditDialog(java.awt.Frame parent, boolean modal,String stationId,String stationName) {
		super(parent, modal);
		this.stationId = stationId;
		this.stationName = stationName;

		initComponents();
		this.setTitle("厂站是否启用维护");
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	private void initComponents() {
		getContentPane().setLayout(null);
		
		JLabel label_1 = new JLabel("厂站名称");
		label_1.setBounds(40, 38, 87, 19);
		getContentPane().add(label_1);
		
		
		JLabel label_3 = new JLabel("是否启用");
		label_3.setBounds(40, 207, 87, 18);
		getContentPane().add(label_3);
		
		JButton button = new JButton("\u786E\u5B9A");
		button.setBounds(50, 281, 57, 23);
		getContentPane().add(button);
		
		JButton button_1 = new JButton("\u53D6\u6D88");
		button_1.setBounds(141, 281, 57, 23);
		getContentPane().add(button_1);
		this.setSize(283, 386);


		button.setToolTipText("\u786e\u5b9a");
		button.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png")));
		button.setFont(new Font("宋体", Font.PLAIN, 14));
		button_1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png")));
		button_1.setToolTipText("\u53d6\u6d88");
		button_1.setFont(new Font("宋体", Font.PLAIN, 14));

		button.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button.setFocusPainted(false);
		button.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveButtonActionPerformed(evt);
			}
		});

		button_1.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button_1.setFocusPainted(false);
		
		ButtonGroup group=new ButtonGroup();
		
		yesRadioButton = new JRadioButton("是");
		yesRadioButton.setBounds(50, 234, 57, 27);
		getContentPane().add(yesRadioButton);
		
		noRadioButton = new JRadioButton("否");
		noRadioButton.setBounds(141, 234, 57, 27);
		
		group.add(yesRadioButton);
		group.add(noRadioButton);
		
		getContentPane().add(noRadioButton);
		
		textField = new JTextField();
		textField.setBounds(40, 70, 187, 23);
		getContentPane().add(textField);
		textField.setColumns(10);
		
		JLabel stationtypelabel = new JLabel("\u5382\u7AD9\u7C7B\u578B");
		stationtypelabel.setBounds(40, 118, 72, 18);
		getContentPane().add(stationtypelabel);
		
		
		stationtypecomboBox.setModel(new DefaultComboBoxModel(new String[] {"","\u5BA2\u6237", "\u7535\u5382", "\u53D8\u7535\u7AD9"}));
		stationtypecomboBox.setBounds(40, 149, 87, 24);
		
		voltcomboBox.setBounds(150, 149, 87, 24);
		getContentPane().add(voltcomboBox);
		getContentPane().add(stationtypecomboBox);
		
		JLabel voltlabel = new JLabel("\u7535\u538B\u7B49\u7EA7");
		voltlabel.setBounds(150, 118, 72, 18);
		getContentPane().add(voltlabel);
		
		
		button_1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				cancelButtonActionPerformed(evt);
			}
		});
		
		if(!stationId.equals("")){
			getSelectDfsInfo(stationId);
			
			if(CBSystemConstants.opcardUser.equals("OPCARDHH.")){
				if(stationtypecomboBox.getSelectedItem().equals("客户")){
					voltcomboBox.setModel(new DefaultComboBoxModel(new String[] {"220kV客户", "110kV客户", "35kV客户"}));
				}else if(stationtypecomboBox.getSelectedItem().equals("电厂")){
					voltcomboBox.setModel(new DefaultComboBoxModel(new String[] {"500kV", "220kV", "110kV直调电厂", "35kV直调电厂", "110kV共调电厂", "35kV共调电厂"}));
				}else if(stationtypecomboBox.getSelectedItem().equals("变电站")){
					voltcomboBox.setModel(new DefaultComboBoxModel(new String[] {"500kV", "220kV", "110kV", "35kV"}));
				}
			}else{
				voltcomboBox.setModel(new DefaultComboBoxModel(new String[] {"","500kV", "220kV", "110kV", "35kV"}));
			}
		}
	}
	
	//获取修改界面信息WW
	public void getSelectDfsInfo(String staionId) {
		String sql = "SELECT STATIONID,STATIONNAME,STATIONTYPE,VOLT,ISREMOVE FROM "+CBSystemConstants.opcardUser+"T_SUBSTATION_TREE WHERE STATIONID ='"+staionId+"'";
		List<Map<String,String>> stationList=DBManager.queryForList(sql);
		
		if(stationList.size() > 0){
			for(Map<String,String> stationMap : stationList){
				String station_name = StringUtils.ObjToString(stationMap.get("STATIONNAME"));
				String isremove = StringUtils.ObjToString(stationMap.get("ISREMOVE"));
				String stationtype = StringUtils.ObjToString(stationMap.get("STATIONTYPE"));
				String volt = StringUtils.ObjToString(stationMap.get("VOLT"));
				
				textField.setText(station_name);
				
				if(isremove.equals("0")){
					yesRadioButton.setSelected(true);
				}else{
					noRadioButton.setSelected(true);
				}
				
				stationtypecomboBox.setSelectedItem(stationtype);
				voltcomboBox.setSelectedItem(volt);
			}
		}else{
			textField.setText(stationName);
		}
	}
	
	//确定
	private void saveButtonActionPerformed(java.awt.event.ActionEvent evt) {
		if(textField.getText().trim().equals("")){
			ShowMessage.view(this, "请选择厂站！");
			return;
		}
		
		String isremove = "";
		
		if(yesRadioButton.isSelected()){
			isremove = "0";
		}else{
			isremove = "1";
		}
		
		String type = String.valueOf(stationtypecomboBox.getSelectedItem());
		String volt = String.valueOf(voltcomboBox.getSelectedItem());
		
		if(CBSystemConstants.opcardUser.equals("OPCARDHH.")){
			String xh = "";
			
			if(type.equals("变电站")){
				if(volt.contains("500kV")){
					xh = "1";
				}else if(volt.contains("220kV")){
					xh = "2";
				}else if(volt.contains("110kV")){
					xh = "3";
				}else if(volt.contains("35kV")){
					xh = "4";
				}
			}if(type.equals("电厂")){
				if(volt.contains("500kV")){
					xh = "5";
				}else if(volt.contains("220kV")){
					xh = "6";
				}else if(volt.contains("110kV共调电厂")){
					xh = "7";
				}else if(volt.contains("110kV直调电厂")){
					xh = "8";
				}else if(volt.contains("35kV共调电厂")){
					xh = "9";
				}else if(volt.contains("35kV直调电厂")){
					xh = "10";
				}
			}if(type.equals("客户")){
				if(volt.contains("220kV")){
					xh = "11";
				}else if(volt.contains("110kV")){
					xh = "12";
				}else if(volt.contains("35kV")){
					xh = "13";
				}
			}
			
			String sql = "DELETE FROM "+CBSystemConstants.opcardUser+"T_SUBSTATION_TREE WHERE STATIONID = '"+stationId+"'";
			DBManager.execute(sql);
			
			sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_SUBSTATION_TREE (STATIONTYPE,VOLT,ISREMOVE,STATIONID,STATIONNAME,XH) VALUES ('"+type+"','"+volt+"','"+isremove+"','"+stationId+"','"+textField.getText()+"','"+xh+"')";
			DBManager.execute(sql);
		}else if(CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
			String xh = "";
			
			if(type.equals("变电站")){
				if(volt.contains("500kV")){
					xh = "1";
				}else if(volt.contains("220kV")){
					xh = "2";
				}else if(volt.contains("110kV")){
					xh = "3";
				}else if(volt.contains("35kV")){
					xh = "4";
				}
			}if(type.equals("电厂")){
				if(volt.contains("220kV")){
					xh = "5";
				}else if(volt.contains("110kV")){
					xh = "6";
				}else if(volt.contains("35kV")){
					xh = "7";
				}
			}if(type.equals("用户站")){
				if(volt.contains("220kV")){
					xh = "8";
				}else if(volt.contains("110kV")){
					xh = "9";
				}else if(volt.contains("35kV")){
					xh = "10";
				}
			}
			
			String sql = "DELETE FROM "+CBSystemConstants.opcardUser+"T_SUBSTATION_TREE WHERE STATIONID = '"+stationId+"'";
			DBManager.execute(sql);
			
			sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_SUBSTATION_TREE (STATIONTYPE,VOLT,ISREMOVE,STATIONID,STATIONNAME,XH) VALUES ('"+type+"','"+volt+"','"+isremove+"','"+stationId+"','"+textField.getText()+"','"+xh+"')";
			DBManager.execute(sql);
		}else{
			String sql = "DELETE FROM "+CBSystemConstants.opcardUser+"T_SUBSTATION_TREE WHERE STATIONID = '"+stationId+"'";
			DBManager.execute(sql);
			
			sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_SUBSTATION_TREE (STATIONTYPE,VOLT,ISREMOVE,STATIONID,STATIONNAME) VALUES ('"+type+"','"+volt+"','"+isremove+"','"+stationId+"','"+textField.getText()+"')";
			DBManager.execute(sql);
		}
		
		this.setVisible(false);
		this.dispose();
		//刷新树列表
		if(CBSystemConstants.opcardUser.equals("OPCARDHH.")){
			TransTreeWidgetHH tree2 = (TransTreeWidgetHH)SystemConstants.getGuiBuilder().getWidget("Tree2");
			tree2.refresh();	
		}else if(CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
			TransTreeWidgetQJ tree2 = (TransTreeWidgetQJ)SystemConstants.getGuiBuilder().getWidget("Tree2");
			tree2.refresh();	
		}else{
			TransTreeWidget tree2 = (TransTreeWidget)SystemConstants.getGuiBuilder().getWidget("Tree2");
			tree2.refresh();	
		}
	}
	public void paint(Graphics g) {
		super.paint(g);
		Graphics2D g_2d = (Graphics2D) g;
		g_2d.setColor(Color.GRAY);
		g_2d.drawLine(20, 40, this.getSize().width - 20, 40);

	}
	//取消
	private void cancelButtonActionPerformed(java.awt.event.ActionEvent evt) {
		this.setVisible(false);
		this.dispose();
	}
}