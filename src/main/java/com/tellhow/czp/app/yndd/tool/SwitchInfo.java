package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.List;

import czprule.model.PowerDevice;

/**
 * 主变开关倒负荷关联信息
 * <AUTHOR>
 *
 */
public class SwitchInfo {
	private PowerDevice loadSwitch = null; //主变开关
	private List<PowerDevice> loadMLLineList = new ArrayList<PowerDevice>(); //主变开关连接的母线
	private PowerDevice loadMLSwitch = null; //用于倒负荷的母联开关
	private List<PowerDevice> sourceMLSwitchList = new ArrayList<PowerDevice>(); //高压侧母联开关
	private PowerDevice transformer = null; //倒负荷后代供的主变
	private PowerDevice earthSwitch = null; //接地变开关
	private boolean isMLNeedOperate; //母线是否需要停电
	private String loadType; //0变低 1变中
	
	
	public PowerDevice getLoadSwitch() {
		return loadSwitch;
	}
	public void setLoadSwitch(PowerDevice loadSwitch) {
		this.loadSwitch = loadSwitch;
	}
	public List<PowerDevice> getLoadMLLine() {
		return loadMLLineList;
	}
	public void setLoadMLLine(List<PowerDevice> loadMLLine) {
		this.loadMLLineList = loadMLLine;
	}
	public PowerDevice getLoadMLSwitch() {
		return loadMLSwitch;
	}
	public void setLoadMLSwitch(PowerDevice loadMLSwitch) {
		this.loadMLSwitch = loadMLSwitch;
	}
	
	public List<PowerDevice> getSourceMLSwitchList() {
		return sourceMLSwitchList;
	}
	public void setSourceMLSwitchList(List<PowerDevice> sourceMLSwitchList) {
		this.sourceMLSwitchList = sourceMLSwitchList;
	}
	public PowerDevice getEarthSwitch() {
		return earthSwitch;
	}
	public void setEarthSwitch(PowerDevice earthSwitch) {
		this.earthSwitch = earthSwitch;
	}
	public PowerDevice getTransformer() {
		return transformer;
	}
	public void setTransformer(PowerDevice transformer) {
		this.transformer = transformer;
	}
	public boolean isMLNeedOperate() {
		return isMLNeedOperate;
	}
	public void setMLNeedOperate(boolean isMLNeedOperate) {
		this.isMLNeedOperate = isMLNeedOperate;
	}
	public String getLoadType() {
		return loadType;
	}
	public void setLoadType(String loadType) {
		this.loadType = loadType;
	}
	
}
