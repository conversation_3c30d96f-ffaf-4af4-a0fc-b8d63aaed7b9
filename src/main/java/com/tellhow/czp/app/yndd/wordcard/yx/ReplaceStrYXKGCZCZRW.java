package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrYXKGCZCZRW implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("玉溪开关操作操作任务".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			String begin = CBSystemConstants.getCurRBM().getBeginStatus();
			String end = CBSystemConstants.getCurRBM().getEndState();
			
			if(curDev.getPowerDeviceName().contains("站用变")){
				String zybName = curDev.getPowerDeviceName().substring(0,  curDev.getPowerDeviceName().indexOf("站用变")+3);
				
				replaceStr += CZPService.getService().getDevName(station)+zybName+"由"+RuleExeUtil.getStatus(begin)+"转"+RuleExeUtil.getStatus(end);
			}else if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				replaceStr += CZPService.getService().getDevName(station)+CZPService.getService().getDevName(curDev)+"由"+RuleExeUtil.getStatus(begin)+"转"+RuleExeUtil.getStatus(end);
			
				List<PowerDevice> list =  RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, "", true, true, false);
				List<PowerDevice> list2 =  RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			
				if(list.size() > 2){
					for(PowerDevice dev : list){
						if(!list2.contains(dev)&&RuleExeUtil.isDeviceHadStatus(dev,"0","1")){
							replaceStr += ",拉开"+CZPService.getService().getDevName(dev);
						}if(!list2.contains(dev)&&RuleExeUtil.isDeviceHadStatus(dev,"1","0")){
							replaceStr += ",合上"+CZPService.getService().getDevName(dev);
						}
					}
				}
			}else{
				replaceStr += CZPService.getService().getDevName(station)+CZPService.getService().getDevName(curDev)+"由"+RuleExeUtil.getStatus(begin)+"转"+RuleExeUtil.getStatus(end);
			}
		}
		if(replaceStr == null || replaceStr.length() == 0) {
			return null;
		}
		return replaceStr;
	}
	
}
