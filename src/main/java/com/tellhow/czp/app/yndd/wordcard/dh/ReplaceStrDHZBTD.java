package com.tellhow.czp.app.yndd.wordcard.dh;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.tool.CommonFunctionDH;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDHZBTD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("德宏主变调档".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev); 
			
			if(!CommonFunctionDH.orderContent.equals("")){
				replaceStr += "德宏地调@"+CommonFunctionDH.orderContent;
			}else{
				replaceStr += "德宏地调@将"+stationName+deviceName+"高压侧档位由X档调至X档运行";
			}
			
			if(replaceStr.equals("")){
				replaceStr = null;
			}
		}
		
		return replaceStr;
	}

}
