package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.TransformKDXLChoose;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKMXLLBYTORBY  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("昆明线路由冷备用转热备用".equals(tempStr)){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(curDev.getPowerDeviceID());
			
			if(sourceLineTrans!=null){
				PowerDevice station = CBSystemConstants.getPowerStation(sourceLineTrans.getPowerStationID());

				List<PowerDevice> loadLineTransSwitchList =  RuleExeUtil.getLinkedSwitch(sourceLineTrans);
				
				List<PowerDevice> newloadLineTransSwitchList = new ArrayList<PowerDevice>();
				
				for(PowerDevice sw : loadLineTransSwitchList){
					if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
						if(!RuleExeUtil.isSwMiddleInThreeSecond(sw)){
							newloadLineTransSwitchList.add(0, sw);
						}else{
							newloadLineTransSwitchList.add(sw);
						}
					}else{
						newloadLineTransSwitchList.add(sw);
					}
				}
				
				for(PowerDevice loadLineTransSwitch : newloadLineTransSwitchList){
					if(RuleExeUtil.isDeviceHadStatus(loadLineTransSwitch, "2", "1")){
						replaceStr += CZPService.getService().getDevName(station)+"@将"+CZPService.getService().getDevName(loadLineTransSwitch)+"由冷备用转热备用/r/n";
					}
				}
			}
			
			String curLineId = curDev.getPowerDeviceID();
			String devName = CZPService.getService().getDevName(curDev);
			
			TransformKDXLChoose kdxl = new TransformKDXLChoose();
			
			String sql = "SELECT DEVICE_NUM,UNIT,OPERATION_KIND,LOWERUNIT,ENDPOINT_KIND FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDUSER WHERE LINE_ID IN (  SELECT ACLINE_ID FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND  WHERE ID = '"+curLineId+"')";
			List<Map<String, Object>> stations = DBManager.queryForList(sql);
			
			if(stations != null && stations.size()>0) {
				
				if(kdxl.retMap.size()>0){
					for(Iterator<Map<String, Object>> itor = stations.iterator();itor.hasNext();){
						Map<String, Object> map = itor.next();
						String stationName = StringUtils.ObjToString(map.get("UNIT"));

						if(!kdxl.retMap.containsKey(stationName)){
							itor.remove();
						}
					}
				}
				
				for(Map<String, Object> station:stations) {
					String stationName = StringUtils.ObjToString(station.get("UNIT")).trim();
					String stationKind = StringUtils.ObjToString(station.get("OPERATION_KIND")).trim();
					String deviceNum = StringUtils.ObjToString(station.get("DEVICE_NUM")).trim();
					String lowerunit = StringUtils.ObjToString(station.get("LOWERUNIT")).trim();
					String endpointkind = StringUtils.ObjToString(station.get("ENDPOINT_KIND")).trim();

					if(stationKind.equals("下令")){
						if(endpointkind.equals("外接站用变")){
							replaceStr += (stationName+"@将"+devName+ deviceNum +"断路器由冷备用转热备用" + "/r/n");
						}else{
							if(lowerunit.equals("")){
								replaceStr += (stationName + "@将"+devName+ deviceNum +"断路器由冷备用转热备用" + "/r/n");
							}else{
								replaceStr += (stationName + "@将"+lowerunit+devName+ deviceNum +"断路器由冷备用转热备用" + "/r/n");
							}
						}
					}else if(stationKind.equals("落实")){
						if(endpointkind.equals("其它供电局")){
							replaceStr += (stationName + "@落实"+ devName +"已由冷备用转热备用" + "/r/n");
						}
					}
				}
			}
			
			if(loadLineTrans.size()>0){
				RuleExeUtil.swapDeviceByLowVoltList(loadLineTrans);
				
				for(PowerDevice dev : loadLineTrans){
					PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
					List<PowerDevice> zbList =  RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer, "", true, false, true);
					
					if(zbList.size()>0){
						if(RuleExeUtil.isTransformerXBZ(zbList.get(0))||RuleExeUtil.isTransformerXBDY(zbList.get(0))){
							if(RuleExeUtil.isDeviceHadStatus(zbList.get(0), "2", "1")){
								replaceStr +=  CZPService.getService().getDevName(station)+"@将"+CZPService.getService().getDevName(zbList)+"由冷备用转热备用/r/n";
							}
							
							ReplaceStrTR10KVBZTZZ bzt10kv = new ReplaceStrTR10KVBZTZZ();
							String temp = bzt10kv.strReplace("投入10kV备自投装置", dev, dev, desc);
							
							if(temp!=null){
								replaceStr +=  temp;
							}
							continue;
						}
					}
					
					List<PowerDevice> loadLineLoadSwitchList = RuleExeUtil.getLinkedSwitch(dev);
					
					if(loadLineLoadSwitchList.size()>0){
						for(PowerDevice loadLineLoadSwitch : loadLineLoadSwitchList){
							if(RuleExeUtil.isDeviceHadStatus(loadLineLoadSwitch, "2", "1")){
								replaceStr += CZPService.getService().getDevName(station)+"@将"+CZPService.getService().getDevName(loadLineLoadSwitch)+"由冷备用转热备用/r/n";
								PowerDevice tempstation = CBSystemConstants.getPowerStation(loadLineLoadSwitch.getPowerStationID());

								if(loadLineLoadSwitch.getPowerVoltGrade() == station.getPowerVoltGrade()){
									if(dev.getPowerVoltGrade() == 220&&station.getPowerVoltGrade() == 220){
										if(!loadLineLoadSwitch.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
											replaceStr += CZPService.getService().getDevName(tempstation)+"@投入"+(int)loadLineLoadSwitch.getPowerVoltGrade()+"kV备自投装置/r/n";
										}
									}else{
										replaceStr += CZPService.getService().getDevName(tempstation)+"@投入"+(int)loadLineLoadSwitch.getPowerVoltGrade()+"kV备自投装置/r/n";
									}
								}
							}
						}
					}
				}
			}
		}
		
		return replaceStr;
	}

}
