package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JTextField;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;



public class LineVolsbTransformerEditDialog extends javax.swing.JDialog{
	private String dfsId = "";
	
	private JTextField textField = new JTextField();
	private JTextField textField_1 = new JTextField();
	private JAutoCompleteComboBox equipComboBox = new JAutoCompleteComboBox();
	private JAutoCompleteComboBox stationComboBox = new JAutoCompleteComboBox();
	private JAutoCompleteComboBox zybdzComboBox = new JAutoCompleteComboBox();

	public LineVolsbTransformerEditDialog(JDialog parent, boolean modal,String dfsid) {
		super(parent, modal);
		dfsId= dfsid;
		initComponents();
		this.setTitle("线路关联电压互感器维护");
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	private void initComponents() {
		getContentPane().setLayout(null);
		
		JLabel label_3 = new JLabel("\u5382 \u7AD9 \u540D \u79F0");
		label_3.setBounds(24, 23, 99, 18);
		getContentPane().add(label_3);
		
		JLabel label_1 = new JLabel("\u7EBF \u8DEF \u540D \u79F0");
		label_1.setBounds(24, 78, 113, 19);
		getContentPane().add(label_1);
		
		JLabel lblPt = new JLabel("PT\u540D\u79F0");
		lblPt.setBounds(24, 134, 113, 18);
		getContentPane().add(lblPt);
		
		JLabel lblPt_1 = new JLabel("PT\u5200\u95F8\u540D\u79F0");
		lblPt_1.setBounds(24, 192, 113, 18);
		getContentPane().add(lblPt_1);
		
		JLabel lblNewLabel = new JLabel("\u8BF7\u9009\u62E9PT\u5200\u95F8");
		lblNewLabel.setBounds(24, 256, 136, 18);
		getContentPane().add(lblNewLabel);
		
		
		JButton button = new JButton("\u786E\u5B9A");
		button.setBounds(51, 332, 57, 23);
		getContentPane().add(button);
		
		JButton button_1 = new JButton("\u53D6\u6D88");
		button_1.setBounds(148, 332, 57, 23);
		getContentPane().add(button_1);
		this.setSize(280, 430);

		button.setToolTipText("\u786e\u5b9a");
		button.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png")));
		button.setFont(new Font("宋体", Font.PLAIN, 14));
		button_1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png")));
		button_1.setToolTipText("\u53d6\u6d88");
		button_1.setFont(new Font("宋体", Font.PLAIN, 14));

		button.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button.setFocusPainted(false);
		button.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveButtonActionPerformed(evt);
			}
		});

		button_1.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button_1.setFocusPainted(false);
		
		String sql = "SELECT STATION_ID,STATION_NAME FROM "+CBSystemConstants.opcardUser+"T_SUBSTATION";
		List<Map<String,String>> stationlist=DBManager.queryForList(sql);
		
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		for(int i=0;i<stationlist.size();i++){
			Map<String,String> temp=stationlist.get(i);
			String code = StringUtils.ObjToString(temp.get("STATION_ID"));
			String name = StringUtils.ObjToString(temp.get("STATION_NAME"));
			CodeNameModel cnm=new CodeNameModel(code,name);
			model.addElement(cnm);
		}
		
		stationComboBox.addActionListener(new java.awt.event.ActionListener() {
             public void actionPerformed(java.awt.event.ActionEvent evt) {
               jComboBox1ActionPerformed(evt);
             }
        });
		
		stationComboBox.setModel(model);
		stationComboBox.setBounds(24, 41, 210, 26);
		stationComboBox.setSelectedIndex(-1);
		stationComboBox.setEditable(true);
		getContentPane().add(stationComboBox);
		
		equipComboBox.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
              jComboBox2ActionPerformed(evt);
            }
       });
		
		equipComboBox.setBounds(24, 97, 210, 26);
		equipComboBox.setSelectedIndex(-1);
		equipComboBox.setEditable(true);
		getContentPane().add(equipComboBox);
		
		zybdzComboBox.setBounds(24, 276, 210, 26);
		zybdzComboBox.setSelectedIndex(-1);
		zybdzComboBox.setEditable(true);
		getContentPane().add(zybdzComboBox);
		
		textField_1.setBounds(24, 155, 210, 24);
		getContentPane().add(textField_1);
		textField_1.setColumns(10);
		
		textField = new JTextField();
		textField.setBounds(24, 219, 210, 24);
		getContentPane().add(textField);
		textField.setColumns(10);
		
		button_1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				cancelButtonActionPerformed(evt);
			}
		});
		
		if(!dfsId.equals("")){
			getSelectDfsInfo(dfsId);
//			stationComboBox.setEnabled(false);
//			equipComboBox.setEnabled(false);
		}
	}
	
	private void jComboBox1ActionPerformed(ActionEvent evt) {
		CodeNameModel stationcnm = (CodeNameModel) stationComboBox.getSelectedItem();
		
		if(stationcnm!=null){
			String sql = "SELECT ID AS LINEID ,NAME AS LINENAME FROM "+CBSystemConstants.opcardUser+"T_C_ACLINEEND WHERE ST_ID = '"+stationcnm.getCode()+"'";
			List<Map<String,String>> linelist=DBManager.queryForList(sql);
			
			DefaultComboBoxModel model2 = new DefaultComboBoxModel();
			for(int i=0;i<linelist.size();i++){
				Map<String,String> temp=linelist.get(i);
				String code = StringUtils.ObjToString(temp.get("LINEID"));
				String name = StringUtils.ObjToString(temp.get("LINENAME"));
				CodeNameModel cnm=new CodeNameModel(code,name);
				model2.addElement(cnm);
			}
			equipComboBox.setModel(model2);
		}
	}
	
	private void jComboBox2ActionPerformed(ActionEvent evt) {
		CodeNameModel stationcnm = (CodeNameModel) stationComboBox.getSelectedItem();
		
		if(stationcnm!=null){
			CodeNameModel cnm2 =new CodeNameModel("","");
			
			String sql = "SELECT EQUIP_ID,EQUIP_NAME,EQUIPTYPE_ID FROM "+CBSystemConstants.opcardUser+"T_EQUIPINFO WHERE STATION_ID = '"+stationcnm.getCode()+"'";
			List<Map<String,String>> linelist=DBManager.queryForList(sql);
			
			DefaultComboBoxModel model2 = new DefaultComboBoxModel();
			
			model2.addElement(cnm2);
			
			for(int i=0;i<linelist.size();i++){
				Map<String,String> temp=linelist.get(i);
				String code = StringUtils.ObjToString(temp.get("EQUIP_ID"));
				String name = StringUtils.ObjToString(temp.get("EQUIP_NAME"));
				String type = StringUtils.ObjToString(temp.get("EQUIPTYPE_ID"));

				if(type.equals("8")){
					CodeNameModel cnm=new CodeNameModel(code,name);
					model2.addElement(cnm);
				}
			}
			zybdzComboBox.setModel(model2);
		}
	}
	
	//获取修改界面信息WW
	public void getSelectDfsInfo(String dfsId) {
		String sql = "SELECT LINE_ID, STATION_NAME, LINE_NAME, PT_NAME, PT_DZNAME, PT_DEVID,STATION_ID FROM "+CBSystemConstants.opcardUser+"T_A_LINEPT WHERE LINE_ID ='"+dfsId+"'";
		List<Map<String,String>> zyblist=DBManager.queryForList(sql);
		
		for(Map<String,String> temp : zyblist){
			String line_id=StringUtils.ObjToString(temp.get("LINE_ID"));
			String line_name=StringUtils.ObjToString(temp.get("LINE_NAME"));
			String station_id=StringUtils.ObjToString(temp.get("STATION_ID"));
			String station_name=StringUtils.ObjToString(temp.get("STATION_NAME"));
			String zyb_name=StringUtils.ObjToString(temp.get("PT_NAME"));
			String zyb_dzid=StringUtils.ObjToString(temp.get("PT_DEVID"));
			String zyb_dzname=StringUtils.ObjToString(temp.get("PT_DZNAME"));

			textField_1.setText(zyb_name);
			CodeNameModel cnm=new CodeNameModel(station_id,station_name);
			stationComboBox.setSelectedItem(cnm);
			
			CodeNameModel cnm2=new CodeNameModel(line_id,line_name);
			equipComboBox.setSelectedItem(cnm2);
			
			textField.setText(zyb_dzname);
			
			PowerDevice zybdz = CBSystemConstants.getPowerDevice(zyb_dzid);
			
			if(zybdz != null){
				CodeNameModel cnm3=new CodeNameModel(zyb_dzid,zybdz.getPowerDeviceName());
				zybdzComboBox.setSelectedItem(cnm3);
			}
		}
	}
	
	//确定
	private void saveButtonActionPerformed(java.awt.event.ActionEvent evt) {
		//先删除原来的数据
		String sql = "DELETE "+CBSystemConstants.opcardUser+"T_A_LINEPT WHERE LINE_ID = '"+dfsId+"'";
		DBManager.execute(sql);
		
		//再插入新数据
		String lineid = ((CodeNameModel)equipComboBox.getSelectedItem()).getCode();
		String linename = ((CodeNameModel)equipComboBox.getSelectedItem()).getName();
		String stationid = ((CodeNameModel)stationComboBox.getSelectedItem()).getCode();
		String stationname = ((CodeNameModel)stationComboBox.getSelectedItem()).getName();
		String zybdzid = "";

		if(zybdzComboBox.getSelectedItem() != null){
			zybdzid = ((CodeNameModel)zybdzComboBox.getSelectedItem()).getCode();
		}
		
		String zybname = textField_1.getText();
		String zybdzname = textField.getText();

		sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_LINEPT (LINE_ID, STATION_NAME, LINE_NAME, PT_NAME, PT_DZNAME, PT_DEVID, STATION_ID)"
				+ "VALUES ('"+lineid+"', '"+stationname+"', '"+linename+"', '"+zybname+"', '"+zybdzname+"', '"+zybdzid+"', '"+stationid+"')";
		DBManager.execute(sql);

		this.setVisible(false);
		this.dispose();
	}
	
	public void paint(Graphics g) {
		super.paint(g);
		Graphics2D g_2d = (Graphics2D) g;
		g_2d.setColor(Color.GRAY);
		g_2d.drawLine(20, 40, this.getSize().width - 20, 40);

	}
	//取消
	private void cancelButtonActionPerformed(java.awt.event.ActionEvent evt) {
		this.setVisible(false);
		this.dispose();
	}
}