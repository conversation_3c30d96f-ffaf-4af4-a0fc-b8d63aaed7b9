package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrZBGLFBCDYCKGBZTZZ implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("主变关联非本侧低压侧开关备自投装置".equals(tempStr)){
			List<PowerDevice> dyckgList =  RuleExeUtil.getDeviceList(curDev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, false, true);
			List<PowerDevice> dycmlkgList =  RuleExeUtil.getDeviceList(dyckgList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true,true);
			
			String opr = "";
			
			if(curDev.getDeviceStatus().equals("0")){
				opr = "投入";
			}else{
				opr = "退出";
			}
			
			for(PowerDevice dev : dycmlkgList){
				replaceStr += opr+CZPService.getService().getDevName(dev)+"备自投装置/r/n";
			}
		}
		return replaceStr;
	}
	
}
