package com.tellhow.czp.app.yndd.wordcard.hh;

import java.awt.Component;
import java.awt.FlowLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

import javax.swing.ButtonGroup;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JRadioButton;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrTCZBHBTLJCJQFDLBH implements TempStringReplace {
	
	private static JFrame frame;
	private static JPanel pane1;
	
	String info = "";

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		
		String replaceStr = "";
		
		if("退出主变差动、后备跳两侧及桥、非电量保护闭锁".equals(tempStr)){
			
			
			frame = new JFrame("选择线路");   //顶层容器
			frame.setSize(300, 150);          //窗口大小
			pane1 = new JPanel();       //中间容器
			//单选框
			JRadioButton c1 = new JRadioButton("I线",true);//创建单选框，true为默认选中，不需要可去掉
			JRadioButton c2 = new JRadioButton("II线");//创建单选框
			JRadioButton c3 = new JRadioButton("I线 + II线");//创建单选框
			ButtonGroup group = new ButtonGroup(); //创建单选框按钮组
			JLabel l1 = new JLabel("线路:");
			group.add(c1);//将单选框组件加入单选框按钮组，否则两个都可以选择
			group.add(c2);
			group.add(c3);
			
			pane1.add(l1);
			
			pane1.add(c1);//将单选框组件加入面板
			pane1.add(c2);
			pane1.add(c3);
			//按钮
			JButton btn = new JButton("确定");
			JButton jButton = new JButton("取消");
			
			pane1.add(btn);//将按钮加入面板
			pane1.add(jButton);
			pane1.setLayout(new FlowLayout(FlowLayout.CENTER,20,20));
			
			frame.add(pane1);
			frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
			frame.setVisible(true);//显示
			frame.setLocationRelativeTo(null); // 居中显示
			
		    btn.addActionListener(new ActionListener() {//确认按钮监听
				@Override
				public void actionPerformed(ActionEvent e) {
					
	                //通过面板属性名获取到该面板上的所有组件
	                for(Component c:pane1.getComponents()){
	                    if(c instanceof JRadioButton){
	                        if(((JRadioButton) c).isSelected()){
	                          info  = ((JRadioButton)c).getText();   // 获取你选择的信息
	                           
	                        }
	                    }
	                }
	                frame.setVisible(false);
				}
			});
			
			jButton.addActionListener(new ActionListener() {  //取消按钮监听
				
				@Override
				public void actionPerformed(ActionEvent arg0) {
					
					 frame.setVisible(false);
				}
			});
			
		
		if(info == "I线"){
			List<PowerDevice> powerTransformers = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "","","", false, true, true, true);
			if(powerTransformers.size() > 0){
				for(PowerDevice powerDevice : powerTransformers){
					int voltage = (int) powerDevice.getPowerVoltGrade();
				  String powerTransformer = powerDevice.getPowerDeviceName().replace(powerDevice.getPowerStationName(), "");
				  replaceStr =voltage+"kV"+ powerTransformer+"第I套差动、后备（跳三侧及桥）、非电量保护动作闭锁"+voltage+"kV";
				}
			}
		}
		
		if(info == "II线"){
			List<PowerDevice> powerTransformers = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "","","", false, true, true, true);
			if(powerTransformers.size() > 0){
				for(PowerDevice powerDevice : powerTransformers){
					int voltage = (int) powerDevice.getPowerVoltGrade();
				  String powerTransformer = powerDevice.getPowerDeviceName().replace(powerDevice.getPowerStationName(), "");
				  replaceStr =voltage+"kV"+ powerTransformer+"第II套差动、后备（跳三侧及桥）、非电量保护动作闭锁"+voltage+"kV";
				}
			}
		}
		
		if(info == "I线 + II线"){
			List<PowerDevice> powerTransformers = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "","","", false, true, true, true);
			if(powerTransformers.size() > 0){
				for(PowerDevice powerDevice : powerTransformers){
					int voltage = (int) powerDevice.getPowerVoltGrade();
				  String powerTransformer = powerDevice.getPowerDeviceName().replace(powerDevice.getPowerStationName(), "");
				  replaceStr =voltage+"kV"+ powerTransformer+"第I、II套差动、后备（跳三侧及桥）、非电量保护动作闭锁"+voltage+"kV";
				}
			}
		}
	}
		return replaceStr;
  }
}	
	

	

