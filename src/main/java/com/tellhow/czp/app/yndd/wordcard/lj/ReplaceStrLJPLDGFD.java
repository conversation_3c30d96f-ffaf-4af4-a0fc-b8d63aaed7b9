package com.tellhow.czp.app.yndd.wordcard.lj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.lj.LJGCBHDialog;
import com.tellhow.czp.app.yndd.tool.CommonFunctionLJ;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLJPLDGFD  implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		
		if("丽江旁路代供复电".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(station); 
			String deviceName = CZPService.getService().getDevName(curDev);
			
			List<PowerDevice> plkgList = new ArrayList<PowerDevice>();
			
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());

			for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
					if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) || dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
						plkgList.add(dev);
						break;
					}
				}
			}
			
			if(RuleExeUtil.isDeviceHadStatus(curDev, "2", "1")){
				replaceStr += stationName+"@将"+deviceName+"由冷备用转热备用/r/n";
			}
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev); 
				replaceStr += stationName+"@退出"+plkgName+"重合闸/r/n";
			}
			
			replaceStr += CommonFunctionLJ.getHhContent(curDev,"丽江地调",stationName);
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev); 
				replaceStr += "丽江地调@遥控断开"+stationName+plkgName+"/r/n";
			}
			
			replaceStr += stationName+"@投入"+deviceName+"重合闸/r/n";

			List<PowerDevice> pldzList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePL, "", false, true, true, true);
			
			for(PowerDevice dev : pldzList){
				String pldzName = CZPService.getService().getDevName(dev); 

				if(RuleExeUtil.getDeviceEndStatus(dev).equals("1")){
					replaceStr += stationName+"@拉开"+pldzName+"/r/n";
				}
			}
			
			for(PowerDevice dev : plkgList){
				String plkgName = CZPService.getService().getDevName(dev); 
				replaceStr += stationName+"@将"+plkgName+"由热备用转冷备用/r/n";
			}
		}
		
		return replaceStr;
	}

}
