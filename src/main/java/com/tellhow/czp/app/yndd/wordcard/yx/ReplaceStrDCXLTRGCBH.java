package com.tellhow.czp.app.yndd.wordcard.yx;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;
import czprule.wordcard.replaceclass.TempStringReplace;


public class ReplaceStrDCXLTRGCBH implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("对侧线路投入光差保护".equals(tempStr)){
			List<PowerDevice> lineList =RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, "", true, true, true);
			
			if(lineList.size()>0){
				List<PowerDevice> list = RuleExeUtil.getLineOtherSideList(lineList.get(0));
				
				if(list.size()>0){
					for(PowerDevice dev : list){
						PowerDevice station = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						String stationName=CZPService.getService().getDevName(station);
						replaceStr += stationName+"@投入"+CZPService.getService().getDevName(lineList)+"线路光差保护/r/n";
					}
				}else{
					ShowMessage.view("没有找到对侧厂站！");
					return null;
				}
			}
		}
		return replaceStr;
	}
	
}
