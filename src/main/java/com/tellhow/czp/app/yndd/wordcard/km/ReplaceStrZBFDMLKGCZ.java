package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**  

* <p>Description: </p>  
* <AUTHOR>
* @date 2021年9月13日    
*/
public class ReplaceStrZBFDMLKGCZ implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String result = "";

		if("主变复电母联开关操作".equals(tempStr)) {
			int gycvolt = 0;
			int zycvolt = 0;
			int dycvolt = 0;

			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			List<PowerDevice>  dycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice>  zycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice>  gycmlkgList = new ArrayList<PowerDevice>();
			List<PowerDevice>  gycxlkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					gycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "high");
					zycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "middle");
					dycvolt = (int)RuleExeUtil.getTransformerVolByType(dev, "low");
				}
			}
			
			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if(dev.getDeviceType().equals(SystemConstants.Switch)
						&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dycvolt==dev.getPowerVoltGrade()){
//						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							dycmlkgList.add(dev);
//						}
					}else if(zycvolt==dev.getPowerVoltGrade()){
//						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							zycmlkgList.add(dev);
//						}
					}else if(gycvolt==dev.getPowerVoltGrade()){
//						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							gycmlkgList.add(dev);
//						}
					}
				}else if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
					if(gycvolt==dev.getPowerVoltGrade()){
//						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("1")){
							gycxlkgList.add(dev);
//						}
					}
				}
			}
			
			for(PowerDevice pd : dycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(pd).equals("0")){
					result += "投入"+dycvolt+"kV备自投装置/r/n";
				}
			}
			
			for(PowerDevice pd : zycmlkgList){
				if(RuleExeUtil.getDeviceBeginStatus(pd).equals("1")){
					result += "投入"+zycvolt+"kV备自投装置/r/n";
				}
			}
		}
		if(result.equals("")){
			return null;
		}
		return result;
	}

}
