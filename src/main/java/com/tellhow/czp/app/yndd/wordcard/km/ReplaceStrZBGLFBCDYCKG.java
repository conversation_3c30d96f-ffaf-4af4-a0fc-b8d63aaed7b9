package com.tellhow.czp.app.yndd.wordcard.km;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBGLFBCDYCKG implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("主变关联非本侧低压侧开关".equals(tempStr)){
			List<PowerDevice> dyckgList =  RuleExeUtil.getDeviceList(curDev,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, false, true);
			List<PowerDevice> dycmlkgList =  RuleExeUtil.getDeviceList(dyckgList.get(0),null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,"", false, true, false, true,true);
			
			String opr = "";
			
			if(curDev.getDeviceStatus().equals("0")){
				opr = "断开";
			}else{
				opr = "合上";
			}
			
			for(PowerDevice dev : dycmlkgList){
				replaceStr += "遥控"+opr+dev.getPowerStationName()+CZPService.getService().getDevName(dev)+"/r/n";
			}
		}
		return replaceStr;
	}
	
}
