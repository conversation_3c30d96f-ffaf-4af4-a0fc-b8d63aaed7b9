package com.tellhow.czp.app.yndd.rule.zt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;

public class KGTDDFSExecute implements RulebaseInf {
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		
		PowerDevice pd=rbm.getPd();
		
		if(pd==null)
			return false;

		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			List<PowerDevice> swList = new ArrayList<PowerDevice>();

			if(mxList.size() > 0) {
				PowerDevice mx = mxList.get(0);
				//查找热备用的线路开关和母联开关
				List<PowerDevice> swxlList = RuleExeUtil.getDeviceList(mx, pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
				
				for(PowerDevice sw : swxlList) {
					if(sw.getDeviceStatus().equals("1"))
						swList.add(sw);
					else if(sw.getDeviceStatus().equals("0")) {
						List<PowerDevice> lnlist = RuleExeUtil.getSwitchLoadLine(sw);
						if(lnlist.size() == 1) {
							List<PowerDevice> otherLinelist = RuleExeUtil.getLineOtherSideList(lnlist.get(0));
							for(PowerDevice otherLine : otherLinelist) {
								PowerDevice otherSw = RuleExeUtil.getDeviceSwitch(otherLine);
								if(otherSw != null && otherSw.getDeviceStatus().equals("1"))
									swList.add(otherSw);
							}
						}
					}
				}
				
				
				List<PowerDevice> swmlList = RuleExeUtil.getDeviceList(mx, pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
				
				for(int i=0;i<swmlList.size();i++){//母联开关排除母线ml，搜到的另一端有线路开关或者母联开关，才有可能是可转电的相关母联开关
					if(RuleExeUtil.getDeviceList(swmlList.get(i), mx, SystemConstants.Switch, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true).size()==0){
						swmlList.remove(i);
						i--;
					}
				}
				
				if(swmlList.size()==1&&!swmlList.get(0).getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusExecute(swmlList.get(0), swmlList.get(0).getDeviceStatus(),"0");
				}else if(swmlList.size()>0){
					List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
					
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()&&dev.getDeviceStatus().equals("1")){
								rbykgList.add(dev);
							}
						}
					}
					
					if(rbykgList.size()>1){
						EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, rbykgList , "请选择需要合上的线路断路器：");
						List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

						if(ecc.isCancel()){
							return false;
						}
						
						RuleExeUtil.deviceStatusExecute(choosedyckgList.get(0), choosedyckgList.get(0).getDeviceStatus(), "0");
					}else if(rbykgList.size()==1){
						RuleExeUtil.deviceStatusExecute(rbykgList.get(0), rbykgList.get(0).getDeviceStatus(), "0");
					}
				}else if(swmlList.size()==0){//没有母联开关，则搜是否有其他线路可以转供
					List<PowerDevice> xlkgList= new ArrayList<PowerDevice>();
					
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(mx.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getDeviceStatus().equals("1")&&!dev.getPowerDeviceName().contains("备用")){
							if(dev.getPowerVoltGrade() == mx.getPowerVoltGrade()){
								xlkgList.add(dev);
							}
						}
					}
					
					if(xlkgList.size()>1){
						EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, xlkgList , "请选择用来合环的断路器：");
						List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

						if(ecc.isCancel()){
							return false;
						}
						
						RuleExeUtil.deviceStatusExecute(choosedyckgList.get(0), choosedyckgList.get(0).getDeviceStatus(), "0");
					}else if(xlkgList.size()==1){
						RuleExeUtil.deviceStatusExecute(xlkgList.get(0), xlkgList.get(0).getDeviceStatus(), "0");
					}
				}
			}else{
				List<PowerDevice> swxlList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
				
				for(PowerDevice sw : swxlList) {
					if(sw.getDeviceStatus().equals("1"))
						swList.add(sw);
					else if(sw.getDeviceStatus().equals("0")) {
						List<PowerDevice> lnlist = RuleExeUtil.getSwitchLoadLine(sw);
						if(lnlist.size() == 1) {
							List<PowerDevice> otherLinelist = RuleExeUtil.getLineOtherSideList(lnlist.get(0));
							for(PowerDevice otherLine : otherLinelist) {
								PowerDevice otherSw = RuleExeUtil.getDeviceSwitch(otherLine);
								if(otherSw != null && otherSw.getDeviceStatus().equals("1"))
									swList.add(otherSw);
							}
						}
					}
				}
				
				
				List<PowerDevice> swmlList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
				
				if(swmlList.size()==1&&!swmlList.get(0).getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusExecute(swmlList.get(0), swmlList.get(0).getDeviceStatus(),"0");
				}else if(swmlList.size()>0){
					List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
					
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()&&dev.getDeviceStatus().equals("1")){
								rbykgList.add(dev);
							}
						}
					}
					
					if(rbykgList.size()>1){
						EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, rbykgList , "请选择需要合上的线路断路器：");
						List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

						if(ecc.isCancel()){
							return false;
						}
						
						RuleExeUtil.deviceStatusExecute(choosedyckgList.get(0), choosedyckgList.get(0).getDeviceStatus(), "0");
					}else if(rbykgList.size()==1){
						RuleExeUtil.deviceStatusExecute(rbykgList.get(0), rbykgList.get(0).getDeviceStatus(), "0");
					}
				}else if(swmlList.size()==0){//没有母联开关，则搜是否有其他线路可以转供
					List<PowerDevice> xlkgList= new ArrayList<PowerDevice>();
					
					HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(pd.getPowerStationID());

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&dev.getDeviceStatus().equals("1")&&!dev.getPowerDeviceName().contains("备用")){
							if(dev.getPowerVoltGrade() == pd.getPowerVoltGrade()){
								xlkgList.add(dev);
							}
						}
					}
					
					if(xlkgList.size()>1){
						EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, xlkgList , "请选择用来合环的断路器：");
						List<PowerDevice> choosedyckgList = ecc.getChooseEquip();

						if(ecc.isCancel()){
							return false;
						}
						
						RuleExeUtil.deviceStatusExecute(choosedyckgList.get(0), choosedyckgList.get(0).getDeviceStatus(), "0");
					}else if(xlkgList.size()==1){
						RuleExeUtil.deviceStatusExecute(xlkgList.get(0), xlkgList.get(0).getDeviceStatus(), "0");
					}
				}
			}
		}
		
		return true;
	}

}
