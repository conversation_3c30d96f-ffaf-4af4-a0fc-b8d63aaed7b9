package com.tellhow.czp.app.yndd.view;

import java.awt.Dimension;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.Rectangle;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPopupMenu;
import javax.swing.JSplitPane;

import org.w3c.dom.Document;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.app.yndd.rule.RuleExeUtil;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.czp.app.yndd.rule.StationAllDeviceTDExecute;
import com.tellhow.czp.app.yndd.wordcard.km.ReplaceStrZBZXDJDDZ;
import com.tellhow.czp.mainframe.menu.DeviceMenuBuild;
import com.tellhow.czp.mainframe.menu.provider.ScaleMenuProvider;
import com.tellhow.czp.mainframe.menu.provider.SearchEquipForm;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.menu.provider.LayerListDialog;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DeviceSVGPanelUtil;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-9-4 下午02:38:43 
 */
public class ScaleMenuProviderGZ extends ScaleMenuProvider {
	
	@Override
	public JPopupMenu createMenu() {
		final SVGCanvas fSvgCanvas = SystemConstants.getGuiBuilder().getActivateSVGPanel().getSvgCanvas();
		String stationID=SystemConstants.getGuiBuilder().getActivateSVGPanel().getStationID();
		final PowerDevice pd=CBSystemConstants.getPowerStation(stationID);
		JMenuItem menuItem = null;
		
		DeviceMenuBuild dmb=new DeviceMenuBuild(pd);
		JPopupMenu popupMenu = dmb.createMenu();
		
		popupMenu.addSeparator();
		
		menuItem = new JMenuItem("放大显示");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				fSvgCanvas.zoomCanvas(1, 0.2);
			}
		});
		popupMenu.add(menuItem);
		
		menuItem = new JMenuItem("缩小显示");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				fSvgCanvas.zoomCanvas(-1, 0.2);
			}
		});
		popupMenu.add(menuItem);
		
		menuItem = new JMenuItem("正常显示");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				fSvgCanvas.zoomCanvas(0, 0);
			}
		});
		popupMenu.add(menuItem);
		
		popupMenu.addSeparator();
		
		menuItem = new JMenuItem("图层设置");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				Document document = fSvgCanvas.getSVGDocument();
				LayerListDialog myDialog = new LayerListDialog(SystemConstants.getMainFrame(), document);
				myDialog.setVisible(true);
			}
		});
		popupMenu.add(menuItem);
		
		menuItem = new JMenuItem("搜索设备");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				SearchEquipForm sef = new SearchEquipForm(fSvgCanvas);
			}
		});
		popupMenu.add(menuItem);
		
		menuItem = new JMenuItem("全站转热备用");
		menuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				RuleBaseMode Srcrbm = new RuleBaseMode();
				Srcrbm.setPd(new PowerDevice());
				
				String powerStationID = null;
				SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
		        czprule.model.PowerDevice station =null;
		        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
		            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
		            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
		        }
		        if(powerStationID == ""||station==null)
		        	return ;
				String stationName = CZPService.getService().getDevName(station);
				String ddName = "昆明地调";

		        List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		        List<PowerDevice> xlList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dzList = new ArrayList<PowerDevice>();
		        List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> yxxlkgList = new ArrayList<PowerDevice>();

		        List<String> voltList = new ArrayList<String>();
		        Set<String> set = new HashSet<String>();
		        List<String> bztvoltList = new ArrayList<String>();
		        Set<String> bztset = new HashSet<String>();

		        RuleBaseMode rbm = new RuleBaseMode();
		        rbm.setBeginStatus("0");
		        rbm.setEndState("1");
		        rbm.setPd(pd);
		        pd.setPowerStationID(powerStationID);
		        StationAllDeviceTDExecute td = new StationAllDeviceTDExecute();
				td.execute(rbm);
		        
		        HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(powerStationID);
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
						zbList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
						set.add(String.valueOf((int)dev.getPowerVoltGrade()));
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							if(dev.getDeviceStatus().equals("1")){
								bztset.add(String.valueOf((int)dev.getPowerVoltGrade()));
							}
							mlkgList.add(dev);
						}
						
						if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() > 10&&!dev.getPowerDeviceName().contains("相")){
							rbykgList.add(dev);
						}else if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() == 10&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							rbykgList.add(dev);
						}else if(dev.getDeviceStatus().equals("0")&&dev.getPowerVoltGrade() == 10&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							yxxlkgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS)){
						dzList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.InOutLine)&&dev.getPowerVoltGrade() == 10){
						xlList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}
				}
				
				RuleExeUtil.swapDeviceListNum(rbykgList);

				for(Iterator<String> itor = set.iterator();itor.hasNext();){
					String volt = itor.next();
					
					if(!volt.equals("0")){
						voltList.add(volt);
					}
				}
				
				Collections.sort(voltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
				
				
				RuleExeUtil.sortListByDevName(zbkgList);
				CardModel cm = new CardModel();
		    	cm.setCardItems(new ArrayList<CardItemModel>());
    			String sta = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(powerStationID));
				
    			ReplaceStrZBZXDJDDZ zxdjddz = new ReplaceStrZBZXDJDDZ();
    			
    			RuleExeUtil.swapDeviceListNum(zbList);
    			
    			for(PowerDevice dev : zbList){
    				if(dev.getPowerVoltGrade() > 35){
    					String devName = zxdjddz.strReplace("主变中性点接地刀闸", dev, dev, "")==null?"":zxdjddz.strReplace("主变中性点接地刀闸", dev, dev, "");
        				
//						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
//						for(PowerDevice gd : gdList) {
//							RuleExeUtil.deviceStatusSet(gd, gd.getDeviceStatus(), "0", false);
//		    				DeviceOperate.putDeviceStatus(gd);
//						}
    					
            			if(!devName.equals("")){
            				CardItemModel item=new CardItemModel();
        	    			item.setUuIds(StringUtils.getUUID());
        					item.setCardDesc("落实"+CZPService.getService().getDevName(dev)+devName+"处合位");
        	    			item.setShowName(sta);
        	    			item.setStationName(sta);
        	    			cm.getCardItems().add(item);
            			}
    				}
				}
    			
    			if(station.getPowerVoltGrade() >= 10){
    				int dycvolt = Integer.valueOf(voltList.get(0));
    		        
					CardItemModel item=new CardItemModel();
    				
    				if(dycvolt == 10&&xlList.size()>0){
    					item=new CardItemModel();
    	    			item.setUuIds(StringUtils.getUUID());
    					item.setCardDesc("落实"+CZPService.getService().getDevName(station)+"10kV各出线已转热备用状态");
    	    			item.setShowName("配网调度");
    	    			item.setStationName("配网调度");
    	    			cm.getCardItems().add(item);
    				}
    			}
    			
    			for(PowerDevice dev : yxxlkgList){
    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
    			}
    			
				CardItemModel dritem=new CardItemModel();
    			
    			List<PowerDevice> drkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dyczbkgList = new ArrayList<PowerDevice>();

				int dycvolt = Integer.valueOf(voltList.get(0));

				if(dycvolt == 6||dycvolt == 10){
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						
						 if(dev.getDeviceStatus().equals("1")){
							if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
								bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
							}
						 }
						
						if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
							if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
								if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
									drkgList.add(dev);
								}
								
								if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
									if (dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")||dev.getPowerDeviceName().contains("接地变")){
										zybkgList.add(dev);
									}
								}
								
								if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
									if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										dycmlkgList.add(dev);
									}
								}
								
								if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										dyczbkgList.add(dev);
									}
								}
							}
						}
					}
	    			
	    			for(PowerDevice dev : drkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : zybkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : dycmlkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : dyczbkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			if(voltList.size() == 2){
	    				int gycvolt = Integer.valueOf(voltList.get(1));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getPowerDeviceName().contains("A相")||
									dev.getPowerDeviceName().contains("B相")||
									dev.getPowerDeviceName().contains("C相")){
								continue;
							}
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
						for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : zycmlkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : zyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : gyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							dritem=new CardItemModel();
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}else{
	    			if(voltList.size() == 2){
	    				int gycvolt = Integer.valueOf(voltList.get(1));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerDeviceName().contains("A相")||
									dev.getPowerDeviceName().contains("B相")||
									dev.getPowerDeviceName().contains("C相")){
								continue;
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == dycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									alldyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										dyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										dycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerDeviceName().contains("A相")||
									dev.getPowerDeviceName().contains("B相")||
									dev.getPowerDeviceName().contains("C相")){
								continue;
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == dycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									alldyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										dyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										dycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				
	    				for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : zycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : zyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}
				

    			Collections.sort(bztvoltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
    			
				for(String volt : bztvoltList){
	    			if(volt.equals("10")){
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() == 10){
									dycmlkg.add(dev);
								}
							}
						}
	    				
	    				if(dycmlkg.size()>1){
	    					for(PowerDevice dev : dycmlkg){
	    						CardItemModel item=new CardItemModel();
	        	    			item.setUuIds(StringUtils.getUUID());
								item.setCardDesc("退出"+CZPService.getService().getDevName(dev)+"备自投装置");
								item.setShowName(sta);
				    			item.setStationName(sta);
				    			cm.getCardItems().add(item);
	    					}
	    				}else{
	    					CardItemModel item=new CardItemModel();
	    	    			item.setUuIds(StringUtils.getUUID());
							item.setCardDesc("退出"+volt+"kV备自投装置");
							item.setShowName(sta);
			    			item.setStationName(sta);
			    			cm.getCardItems().add(item);
	    				}
	    			}else{
	    				CardItemModel item=new CardItemModel();
    	    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("退出"+volt+"kV备自投装置");
						item.setShowName(sta);
		    			item.setStationName(sta);
		    			cm.getCardItems().add(item);
	    			}
				}
				
				cm.setCzrw(sta+"全站设备由运行转热备用");
				
	 			ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
				
		    	TempTicket ttk=TempTicket.getInstance();
		    	ttk.init(cm,Srcrbm);
		    	openNPWindow(ttk);
			}
		});
		
		
		if(CBSystemConstants.opcardUser.equals("OPCARDKM.")
				||CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
			popupMenu.add(menuItem);
		}
		
		menuItem = new JMenuItem("全站转冷备用");
		menuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				RuleBaseMode Srcrbm = new RuleBaseMode();
				Srcrbm.setPd(new PowerDevice());
				
				String powerStationID = null;
				SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
		        czprule.model.PowerDevice station =null;
		        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
		            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
		            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
		        }
		        if(powerStationID == ""||station==null)
		        	return ;
				String stationName = CZPService.getService().getDevName(station);
				String ddName = "昆明地调";

		        List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		        List<PowerDevice> xlList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dzList = new ArrayList<PowerDevice>();
		        List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> yxkgList = new ArrayList<PowerDevice>();

		        List<String> voltList = new ArrayList<String>();
		        Set<String> set = new HashSet<String>();
		        List<String> bztvoltList = new ArrayList<String>();
		        Set<String> bztset = new HashSet<String>();

		        RuleBaseMode rbm = new RuleBaseMode();
		        rbm.setBeginStatus("0");
		        rbm.setEndState("1");
		        rbm.setPd(pd);
		        pd.setPowerStationID(powerStationID);
		        StationAllDeviceTDExecute td = new StationAllDeviceTDExecute();
				td.execute(rbm);
		        
		        HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(powerStationID);
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
						zbList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
						if((int)dev.getPowerVoltGrade()!=380){
							set.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							if(dev.getDeviceStatus().equals("1")){
								bztset.add(String.valueOf((int)dev.getPowerVoltGrade()));
							}
							mlkgList.add(dev);
						}
						
						if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() > 10&&!dev.getPowerDeviceName().contains("相")){
							rbykgList.add(dev);
						}else if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() == 10&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							rbykgList.add(dev);
						}else if(dev.getDeviceStatus().equals("0")&&dev.getPowerVoltGrade() == 10&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							yxkgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS)){
						dzList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.InOutLine)&&dev.getPowerVoltGrade() == 10){
						xlList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}
				}
				
				RuleExeUtil.swapDeviceListNum(rbykgList);

				for(Iterator<String> itor = set.iterator();itor.hasNext();){
					String volt = itor.next();
					
					if(!volt.equals("0")){
						voltList.add(volt);
					}
				}
				
				Collections.sort(voltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
				
				
				RuleExeUtil.sortListByDevName(zbkgList);
				CardModel cm = new CardModel();
		    	cm.setCardItems(new ArrayList<CardItemModel>());
    			String sta = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(powerStationID));
    			ReplaceStrZBZXDJDDZ zxdjddz = new ReplaceStrZBZXDJDDZ();
    			
    			RuleExeUtil.swapDeviceListNum(zbList);
    			
    			for(PowerDevice dev : zbList){
    				if(dev.getPowerVoltGrade() > 35){
    					String devName = zxdjddz.strReplace("主变中性点接地刀闸", dev, dev, "")==null?"":zxdjddz.strReplace("主变中性点接地刀闸", dev, dev, "");
        				
//						List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
//						for(PowerDevice gd : gdList) {
//							RuleExeUtil.deviceStatusSet(gd, gd.getDeviceStatus(), "0", true);
//		    				DeviceOperate.putDeviceStatus(gd);
//						}
    					
            			if(!devName.equals("")){
            				CardItemModel item=new CardItemModel();
        	    			item.setUuIds(StringUtils.getUUID());
        					item.setCardDesc("落实"+CZPService.getService().getDevName(dev)+devName+"处合位");
        	    			item.setShowName(sta);
        	    			item.setStationName(sta);
        	    			cm.getCardItems().add(item);
            			}
    				}
				}
    			
    			if(station.getPowerVoltGrade() >= 10){
    				int dycvolt = Integer.valueOf(voltList.get(0));
    		        
					CardItemModel item=new CardItemModel();
    				
    				if(dycvolt == 10&&xlList.size()>0){
    					item=new CardItemModel();
    	    			item.setUuIds(StringUtils.getUUID());
    					item.setCardDesc("落实"+CZPService.getService().getDevName(station)+"10kV各出线已转热备用状态");
    	    			item.setShowName("配网调度");
    	    			item.setStationName("配网调度");
    	    			cm.getCardItems().add(item);
    				}
    			}
    			
    			for(PowerDevice dev : yxkgList){
    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
    			}
    			
				CardItemModel dritem=new CardItemModel();
    			
    			List<PowerDevice> drkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dyczbkgList = new ArrayList<PowerDevice>();

				int dycvolt = Integer.valueOf(voltList.get(0));

				if(dycvolt == 6||dycvolt == 10){
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						
						 if(dev.getDeviceStatus().equals("1")){
							if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
								bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
							}
						 }
						
						if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
							if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
								if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
									drkgList.add(dev);
								}
								
								if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
									if (dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")||dev.getPowerDeviceName().contains("接地变")){
										zybkgList.add(dev);
									}
								}
								
								if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
									if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										dycmlkgList.add(dev);
									}
								}
								
								if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										dyczbkgList.add(dev);
									}
								}
							}
						}
					}
	    			
	    			for(PowerDevice dev : drkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : zybkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : dycmlkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : dyczbkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			if(voltList.size() == 2){
	    				int gycvolt = Integer.valueOf(voltList.get(1));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getPowerDeviceName().contains("A相")||
									dev.getPowerDeviceName().contains("B相")||
									dev.getPowerDeviceName().contains("C相")){
								continue;
							}
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
						for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : zycmlkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : zyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : gyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							dritem=new CardItemModel();
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}else{
	    			if(voltList.size() == 2){
	    				int gycvolt = Integer.valueOf(voltList.get(1));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerDeviceName().contains("A相")||
									dev.getPowerDeviceName().contains("B相")||
									dev.getPowerDeviceName().contains("C相")){
								continue;
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == dycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									alldyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										dyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										dycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerDeviceName().contains("A相")||
									dev.getPowerDeviceName().contains("B相")||
									dev.getPowerDeviceName().contains("C相")){
								continue;
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == dycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									alldyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										dyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										dycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				
	    				for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : zycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : zyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}
				

    			Collections.sort(bztvoltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
    			
				for(String volt : bztvoltList){
	    			if(volt.equals("10")){
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() == 10){
									dycmlkg.add(dev);
								}
							}
						}
	    				
	    				if(dycmlkg.size()>1){
	    					for(PowerDevice dev : dycmlkg){
	    						CardItemModel item=new CardItemModel();
	        	    			item.setUuIds(StringUtils.getUUID());
								item.setCardDesc("退出"+CZPService.getService().getDevName(dev)+"备自投装置");
								item.setShowName(sta);
				    			item.setStationName(sta);
				    			cm.getCardItems().add(item);
	    					}
	    				}else{
	    					CardItemModel item=new CardItemModel();
	    	    			item.setUuIds(StringUtils.getUUID());
							item.setCardDesc("退出"+volt+"kV备自投装置");
							item.setShowName(sta);
			    			item.setStationName(sta);
			    			cm.getCardItems().add(item);
	    				}
	    			}else{
	    				CardItemModel item=new CardItemModel();
    	    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("退出"+volt+"kV备自投装置");
						item.setShowName(sta);
		    			item.setStationName(sta);
		    			cm.getCardItems().add(item);
	    			}
				}
				
				cm.setCzrw(sta+"全站设备由运行转冷备用");
				
				CardItemModel item=new CardItemModel();
    			item.setUuIds(StringUtils.getUUID());
				item.setCardDesc("将全站设备由热备用转冷备用");
    			item.setShowName(sta);
    			item.setStationName(sta);
    			item.setCzdwID(powerStationID);
    			cm.getCardItems().add(item);

	 			ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
    			
		    	TempTicket ttk=TempTicket.getInstance();
		    	ttk.init(cm,Srcrbm);
		    	openNPWindow(ttk);
			}
		});
		
		if(CBSystemConstants.opcardUser.equals("OPCARDKM.")
				||CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
			popupMenu.add(menuItem);
		}
		
		
		menuItem = new JMenuItem("全站停电");
		menuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				RuleBaseMode Srcrbm = new RuleBaseMode();
				Srcrbm.setPd(new PowerDevice());
				
				String powerStationID = null;
				SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
		        czprule.model.PowerDevice station =null;
		        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
		            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
		            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
		        }
		        if(powerStationID == ""||station==null)
		        	return ;
				String stationName = CZPService.getService().getDevName(station);
				String ddName = "红河地调";

		        List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		        List<PowerDevice> xlList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dzList = new ArrayList<PowerDevice>();
		        List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbdzList = new ArrayList<PowerDevice>();

		        List<String> voltList = new ArrayList<String>();
		        Set<String> set = new HashSet<String>();
		        List<String> bztvoltList = new ArrayList<String>();
		        Set<String> bztset = new HashSet<String>();

		        RuleBaseMode rbm = new RuleBaseMode();
		        rbm.setBeginStatus("0");
		        rbm.setEndState("1");
		        rbm.setPd(pd);
		        pd.setPowerStationID(powerStationID);
		        StationAllDeviceTDExecute td = new StationAllDeviceTDExecute();
				td.execute(rbm);
		        
		        HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(powerStationID);
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
						zbList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
						set.add(String.valueOf((int)dev.getPowerVoltGrade()));
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							if(dev.getDeviceStatus().equals("1")){
								bztset.add(String.valueOf((int)dev.getPowerVoltGrade()));
							}
							mlkgList.add(dev);
						}
						
						if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() > 10&&!dev.getPowerDeviceName().contains("相")){
							rbykgList.add(dev);
						}else if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() == 10&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							rbykgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS)){
						dzList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.InOutLine)&&dev.getPowerVoltGrade() == 10){
						xlList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.InOutLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}
					
					 if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZBS)){
						 zbdzList.add(dev);
	    				//DeviceOperate.putDeviceStatus(dev);
					}
				}
				
				RuleExeUtil.swapDeviceListNum(rbykgList);

				for(Iterator<String> itor = set.iterator();itor.hasNext();){
					String volt = itor.next();
					
					if(!volt.equals("0")){
						voltList.add(volt);
					}
				}
				
				Collections.sort(voltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
				
				
				RuleExeUtil.sortListByDevName(zbkgList);
				CardModel cm = new CardModel();
		    	cm.setCardItems(new ArrayList<CardItemModel>());
    			String sta = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(powerStationID));
				
//    			ReplaceStrZBZXDJDDZ zxdjddz = new ReplaceStrZBZXDJDDZ();
    			
    			RuleExeUtil.swapDeviceListNum(zbList);
    			
//    			for(PowerDevice dev : zbList){
//    				if(dev.getPowerVoltGrade() > 35){
//    					String devName = zxdjddz.strReplace("主变中性点接地刀闸", dev, dev, "")==null?"":zxdjddz.strReplace("主变中性点接地刀闸", dev, dev, "");
//    					
//            			if(!devName.equals("")){
//            				CardItemModel item=new CardItemModel();
//        	    			item.setUuIds(StringUtils.getUUID());
//        					item.setCardDesc("落实"+CZPService.getService().getDevName(dev)+devName+"处合位");
//        	    			item.setShowName(sta);
//        	    			item.setStationName(sta);
//        	    			cm.getCardItems().add(item);
//            			}
//    				}
//				}
    			
				CardItemModel dritem=new CardItemModel();
    			
    			List<PowerDevice> drkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dyczbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mxList = new ArrayList<PowerDevice>();
		        List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> kgdy10kVList = new ArrayList<PowerDevice>();

				int dycvolt = Integer.valueOf(voltList.get(0));

				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					 if(dev.getDeviceStatus().equals("1")){
						if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
							bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
					 }
					
					if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								dyczbkgList.add(dev);
							}
						}
						
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							if (dev.getPowerDeviceName().contains("接地站用变")||dev.getPowerDeviceName().contains("接地变")){
								jdzybkgList.add(dev);
							}
						}
						
						
						if(dev.getDeviceStatus().equals("1")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
								drkgList.add(dev);
							}
							
							if (dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")){
								zybkgList.add(dev);
							}
						
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkgList.add(dev);
							}
						}
						
						if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
							if(!dev.getPowerDeviceName().contains("400")){
								mxList.add(dev);
							}
						}
					}else if(dev.getPowerVoltGrade() > 10&&dev.getDeviceType().equals(SystemConstants.Switch)&&dev.getDeviceStatus().equals("1")){
						kgdy10kVList.add(dev);
					}
				}
				
				if(dycvolt == 6||dycvolt == 10){
					//核实内容
    				gethsnr(dycmlkgList,drkgList,zybkgList,kgdy10kVList,cm,ddName);
	    			
    				RuleExeUtil.swapDeviceList(mxList);
    				
    				dritem=new CardItemModel();
	    			dritem.setUuIds(StringUtils.getUUID());
					dritem.setCardDesc("核实"+CZPService.getService().getDevName(mxList)+"上其所管辖的所有10kV出线断路器均己转冷备用");
	    			dritem.setShowName(ddName);
	    			dritem.setStationName(ddName);
	    			cm.getCardItems().add(dritem);
    				
	    			for(String volt : voltList){
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
						dritem.setCardDesc("退出"+volt+"kV备自投装置");
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : jdzybkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : drkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
//	    				//DeviceOperate.putDeviceStatus(dev);
//	    				
//	    				dritem=new CardItemModel();
//		    			dritem.setUuIds(StringUtils.getUUID());
//						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
//		    			dritem.setShowName(ddName);
//		    			dritem.setStationName(ddName);
//		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : zybkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
//	    				dritem=new CardItemModel();
//		    			dritem.setUuIds(StringUtils.getUUID());
//		    			
//						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
//		    			dritem.setShowName(ddName);
//		    			dritem.setStationName(ddName);
//		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : dycmlkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : dyczbkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			if(voltList.size() == 2){
	    				int gycvolt = Integer.valueOf(voltList.get(1));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
						for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : zycmlkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : zyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : gyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							dritem=new CardItemModel();
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}else{
					//核实内容
    				gethsnr(dycmlkgList,drkgList,zybkgList,kgdy10kVList,cm,ddName);
					
	    			if(voltList.size() == 2){
	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
									drkgList.add(dev);
								}
								
								if (dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")||dev.getPowerDeviceName().contains("接地变")){
									if(!dev.getPowerDeviceName().endsWith("站用变")&&!dev.getPowerDeviceName().endsWith("所用变")&&!dev.getPowerDeviceName().endsWith("接地变")){
										zybkgList.add(dev);
									}
								}
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == dycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									alldyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										dyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										dycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				
	    				for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : zycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : zyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}
				

    			Collections.sort(bztvoltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
    			
//				for(String volt : bztvoltList){
//	    			if(volt.equals("10")){
//	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
//	    				
//	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
//							PowerDevice dev = it2.next();
//							
//							if(dev.getDeviceStatus().equals("1")){
//								if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() == 10){
//									dycmlkg.add(dev);
//								}
//							}
//						}
//	    				
//	    				if(dycmlkg.size()>1){
//	    					for(PowerDevice dev : dycmlkg){
//	    						CardItemModel item=new CardItemModel();
//	        	    			item.setUuIds(StringUtils.getUUID());
//								item.setCardDesc("退出"+CZPService.getService().getDevName(dev)+"备自投装置");
//								item.setShowName(sta);
//				    			item.setStationName(sta);
//				    			cm.getCardItems().add(item);
//	    					}
//	    				}else{
//	    					CardItemModel item=new CardItemModel();
//	    	    			item.setUuIds(StringUtils.getUUID());
//							item.setCardDesc("退出"+volt+"kV备自投装置");
//							item.setShowName(sta);
//			    			item.setStationName(sta);
//			    			cm.getCardItems().add(item);
//	    				}
//	    			}else{
//	    				CardItemModel item=new CardItemModel();
//    	    			item.setUuIds(StringUtils.getUUID());
//						item.setCardDesc("退出"+volt+"kV备自投装置");
//						item.setShowName(sta);
//		    			item.setStationName(sta);
//		    			cm.getCardItems().add(item);
//	    			}
//				}
				
				cm.setCzrw(sta+"全站设备由运行转冷备用");
				
				
				
				CardItemModel item=new CardItemModel();
    			item.setUuIds(StringUtils.getUUID());
				item.setCardDesc("将全站设备由热备用转冷备用");
    			item.setShowName(sta);
    			item.setStationName(sta);
    			cm.getCardItems().add(item);

    			if(zbList.size()>0){
    				if(RuleUtil.isTransformerNQ(zbList.get(0))){
    					item=new CardItemModel();
    	    			item.setUuIds(StringUtils.getUUID());
    					item.setCardDesc("拉开"+CZPService.getService().getDevName(zbdzList));
    	    			item.setShowName(sta);
    	    			item.setStationName(sta);
    	    			cm.getCardItems().add(item);

    				}
    			}
    			
    			item=new CardItemModel();
    			item.setUuIds(StringUtils.getUUID());
				item.setCardDesc("退出XX线路线路光纤差动保护");
    			item.setShowName(sta);
    			item.setStationName(sta);
    			cm.getCardItems().add(item);
    			
	 			ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
    			
		    	TempTicket ttk=TempTicket.getInstance();
		    	ttk.init(cm,Srcrbm);
		    	openNPWindow(ttk);
			}
		});
		
		if(CBSystemConstants.opcardUser.equals("OPCARDHH.")){
			popupMenu.add(menuItem);
		}
		
		menuItem = new JMenuItem("全站停电");
		menuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				RuleBaseMode Srcrbm = new RuleBaseMode();
				Srcrbm.setPd(new PowerDevice());
				
				String powerStationID = null;
				SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
		        czprule.model.PowerDevice station =null;
		        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
		            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
		            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
		        }
		        if(powerStationID == ""||station==null)
		        	return ;
				String stationName = CZPService.getService().getDevName(station);
				String ddName = "昭通地调";

				CardModel cm = new CardModel();
		    	cm.setCardItems(new ArrayList<CardItemModel>());
		    	
		    	List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		        List<PowerDevice> xlList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		    	
		    	HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(powerStationID);
					
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						zbList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							mlkgList.add(dev);
						}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							xlList.add(dev);
						}
					}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
					}
				}
				
				CardItemModel dritem=new CardItemModel();
		    	
				for(PowerDevice dev : mlkgList){
					if(dev.getDeviceStatus().equals("0")){
						if(dev.getPowerVoltGrade() == 10){
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
						}
					}
					
					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
					
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
	    			
	    			for(PowerDevice dz : dzList){
	    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "1");
	    			}
				}
				
				for(PowerDevice dev : zbList){
					List<PowerDevice> gyczbkgList = RuleExeUtil.getTransformerSwitchHigh(dev);
					List<PowerDevice> zyczbkgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
					List<PowerDevice> dyczbkgList = RuleExeUtil.getTransformerSwitchLow(dev);
					
					for(PowerDevice dyczbkg : dyczbkgList){
						if(dev.getDeviceStatus().equals("0")){
							RuleExeUtil.deviceStatusSet(dyczbkg, dyczbkg.getDeviceStatus(), "2", true);
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dyczbkg));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
			    			
			    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dyczbkg, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "1");
			    			}
						}
					}
					
					for(PowerDevice zyczbkg : zyczbkgList){
						if(dev.getDeviceStatus().equals("0")){
							RuleExeUtil.deviceStatusSet(zyczbkg, zyczbkg.getDeviceStatus(), "2", true);
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(zyczbkg));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
			    			
			    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zyczbkg, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "1");
			    			}
						}
					}
					
					for(PowerDevice gyczbkg : gyczbkgList){
						if(dev.getDeviceStatus().equals("0")){
							RuleExeUtil.deviceStatusSet(gyczbkg, gyczbkg.getDeviceStatus(), "2", true);
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(gyczbkg));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
			    			
			    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(gyczbkg, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "1");
			    			}
						}
					}
					
					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
				}
				
				for(PowerDevice dev : mlkgList){
					if(dev.getDeviceStatus().equals("0")){
						if(dev.getPowerVoltGrade() > 10){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
			    			
			    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "1");
			    			}
						}
					}
					
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
	    			
	    			for(PowerDevice dz : dzList){
	    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "1");
	    			}
				}
				
				for(PowerDevice dev : xlList){
					if(dev.getDeviceStatus().equals("0")){
						if(dev.getPowerVoltGrade() > 10){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
			    			
			    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "1");
			    			}
						}
					}
				}
				
				CardItemModel item=new CardItemModel();
    			item.setUuIds(StringUtils.getUUID());
				item.setCardDesc("将全站设备由热备用转冷备用");
    			item.setShowName(stationName);
    			item.setStationName(stationName);
    			cm.getCardItems().add(item);
				
				cm.setCzrw(stationName+"全站设备由运行转冷备用");
				
				ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
				
				TempTicket ttk=TempTicket.getInstance();
		    	ttk.init(cm,Srcrbm);
		    	openNPWindow(ttk);
			}
		});
		
		if(CBSystemConstants.opcardUser.equals("OPCARDZT.")){
			popupMenu.add(menuItem);
		}
		
		menuItem = new JMenuItem("全站复电");
		menuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				RuleBaseMode Srcrbm = new RuleBaseMode();
				Srcrbm.setPd(new PowerDevice());
				
				String powerStationID = null;
				SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
		        czprule.model.PowerDevice station =null;
		        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
		            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
		            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
		        }
		        if(powerStationID == ""||station==null)
		        	return ;
				String stationName = CZPService.getService().getDevName(station);
				String ddName = "昭通地调";

				CardModel cm = new CardModel();
		    	cm.setCardItems(new ArrayList<CardItemModel>());
		    	
		    	List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		        List<PowerDevice> xlList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		    	
		    	HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(powerStationID);
					
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						zbList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							mlkgList.add(dev);
						}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
							if(dev.getPowerVoltGrade()>10){
								xlList.add(dev);
							}
						}
					}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "0", true);
					}
				}
				
				CardItemModel item=new CardItemModel();
    			item.setUuIds(StringUtils.getUUID());
				item.setCardDesc("将全站设备由冷备用转热备用");
    			item.setShowName(stationName);
    			item.setStationName(stationName);
    			cm.getCardItems().add(item);
				
    			CardItemModel dritem=new CardItemModel();
    			
    			if(xlList.size()>1){
    				EquipCheckChoose ec=new EquipCheckChoose(SystemConstants.getMainFrame(), true, xlList, "请选择需要转运行的断路器");

    				List<PowerDevice> chooseEquips4=ec.getChooseEquip();
    				
    				for(PowerDevice chooseEquips : chooseEquips4){
    					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(chooseEquips, SystemConstants.SwitchSeparate);
		    			
		    			for(PowerDevice dz : dzList){
		    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "0");
		    			}
    					
    					RuleExeUtil.deviceStatusSet(chooseEquips, chooseEquips.getDeviceStatus(), "0", true);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
						dritem.setCardDesc("遥控合上"+stationName+CZPService.getService().getDevName(chooseEquips));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
    				}
    			}
				
				for(PowerDevice dev : mlkgList){
					if(dev.getDeviceStatus().equals("2")){
						if(dev.getPowerVoltGrade() > 10){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "0", true);
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控合上"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
			    			
			    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "1");
			    			}
						}
					}
					
					List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
	    			
	    			for(PowerDevice dz : dzList){
	    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "1");
	    			}
				}
				
				for(PowerDevice dev : zbList){
					List<PowerDevice> gyczbkgList = RuleExeUtil.getTransformerSwitchHigh(dev);
					List<PowerDevice> zyczbkgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
					List<PowerDevice> dyczbkgList = RuleExeUtil.getTransformerSwitchLow(dev);
					
					for(PowerDevice dyczbkg : dyczbkgList){
						if(dev.getDeviceStatus().equals("2")){
							RuleExeUtil.deviceStatusSet(dyczbkg, dyczbkg.getDeviceStatus(), "0", true);
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控合上"+stationName+CZPService.getService().getDevName(dyczbkg));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
			    			
			    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dyczbkg, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "0");
			    			}
						}
					}
					
					for(PowerDevice zyczbkg : zyczbkgList){
						if(dev.getDeviceStatus().equals("2")){
							RuleExeUtil.deviceStatusSet(zyczbkg, zyczbkg.getDeviceStatus(), "0", true);
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控合上"+stationName+CZPService.getService().getDevName(zyczbkg));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
			    			
			    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zyczbkg, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "0");
			    			}
						}
					}
					
					for(PowerDevice gyczbkg : gyczbkgList){
						if(dev.getDeviceStatus().equals("2")){
							RuleExeUtil.deviceStatusSet(gyczbkg, gyczbkg.getDeviceStatus(), "0", true);
		    				dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控合上"+stationName+CZPService.getService().getDevName(gyczbkg));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
			    			
			    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(gyczbkg, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "0");
			    			}
						}
					}
					
					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "0", true);
				}
				
				List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
				
				for(PowerDevice dev : mlkgList){
					if(dev.getDeviceStatus().equals("2")){
						if(dev.getPowerVoltGrade() == 10){
							dycmlkgList.add(dev);
						}
					}
				}
				
				if(dycmlkgList.size()>0){
					String[] arr = {"运行","热备用"};
		 			
					int select = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择"+CZPService.getService().getDevName(dycmlkgList)+"状态", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, arr, null);
		 			
					if(select==0){
						for(PowerDevice dev : mlkgList){
							if(dev.getDeviceStatus().equals("2")){
								if(dev.getPowerVoltGrade() == 10){
									RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "0", true);
				    				dritem=new CardItemModel();
					    			dritem.setUuIds(StringUtils.getUUID());
									dritem.setCardDesc("遥控用"+stationName+CZPService.getService().getDevName(dev)+"合环");
					    			dritem.setShowName(ddName);
					    			dritem.setStationName(ddName);
					    			cm.getCardItems().add(dritem);
					    			
					    			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					    			
					    			for(PowerDevice dz : dzList){
					    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "0");
					    			}
								}
							}
							
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
			    			
			    			for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "0");
			    			}
						}
					}else{
						for(PowerDevice dev : mlkgList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							for(PowerDevice dz : dzList){
			    				RuleExeUtil.deviceStatusSet(dz, dz.getDeviceStatus(), "0");
			    			}
						}
					}
				}
				
				cm.setCzrw(stationName+"全站设备由冷备用转运行");
				
				ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
				
				TempTicket ttk=TempTicket.getInstance();
		    	ttk.init(cm,Srcrbm);
		    	openNPWindow(ttk);
			}
		});
		
		if(CBSystemConstants.opcardUser.equals("OPCARDZT.")){
			popupMenu.add(menuItem);
		}
		
		menuItem = new JMenuItem("全站停电");
		menuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				RuleBaseMode Srcrbm = new RuleBaseMode();
				Srcrbm.setPd(new PowerDevice());
				
				String powerStationID = null;
				SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
		        czprule.model.PowerDevice station =null;
		        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
		            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
		            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
		        }
		        if(powerStationID == ""||station==null)
		        	return ;
				String stationName = CZPService.getService().getDevName(station);
				String ddName = "丽江地调";

		        List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		        List<PowerDevice> xlList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dzList = new ArrayList<PowerDevice>();
		        List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbdzList = new ArrayList<PowerDevice>();

		        List<String> voltList = new ArrayList<String>();
		        Set<String> set = new HashSet<String>();
		        List<String> bztvoltList = new ArrayList<String>();
		        Set<String> bztset = new HashSet<String>();

		        RuleBaseMode rbm = new RuleBaseMode();
		        rbm.setBeginStatus("0");
		        rbm.setEndState("1");
		        rbm.setPd(pd);
		        pd.setPowerStationID(powerStationID);
		        StationAllDeviceTDExecute td = new StationAllDeviceTDExecute();
				td.execute(rbm);
		        
		        HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(powerStationID);
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
						zbList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
						set.add(String.valueOf((int)dev.getPowerVoltGrade()));
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							if(dev.getDeviceStatus().equals("1")){
								bztset.add(String.valueOf((int)dev.getPowerVoltGrade()));
							}
							mlkgList.add(dev);
						}
						
						if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() > 10&&!dev.getPowerDeviceName().contains("相")){
							rbykgList.add(dev);
						}else if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() == 10&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							rbykgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS)){
						dzList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.InOutLine)&&dev.getPowerVoltGrade() == 10){
						xlList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.InOutLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}
					
					 if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZBS)){
						 zbdzList.add(dev);
	    				//DeviceOperate.putDeviceStatus(dev);
					}
				}
				
				RuleExeUtil.swapDeviceListNum(rbykgList);

				for(Iterator<String> itor = set.iterator();itor.hasNext();){
					String volt = itor.next();
					
					if(!volt.equals("0")){
						voltList.add(volt);
					}
				}
				
				Collections.sort(voltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
				
				
				RuleExeUtil.sortListByDevName(zbkgList);
				CardModel cm = new CardModel();
		    	cm.setCardItems(new ArrayList<CardItemModel>());
    			String sta = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(powerStationID));
				
    			RuleExeUtil.swapDeviceListNum(zbList);
    			
				CardItemModel dritem=new CardItemModel();
    			
    			List<PowerDevice> drkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dyczbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mxList = new ArrayList<PowerDevice>();
		        List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> kgdy10kVList = new ArrayList<PowerDevice>();

				int dycvolt = Integer.valueOf(voltList.get(0));

				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					 if(dev.getDeviceStatus().equals("1")){
						if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
							bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
					 }
					
					if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								dyczbkgList.add(dev);
							}
						}
						
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							if (dev.getPowerDeviceName().contains("接地站用变")||dev.getPowerDeviceName().contains("接地变")){
								jdzybkgList.add(dev);
							}
						}
						
						
						if(dev.getDeviceStatus().equals("1")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
								drkgList.add(dev);
							}
							
							if (dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")){
								zybkgList.add(dev);
							}
						
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkgList.add(dev);
							}
						}
						
						if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
							if(!dev.getPowerDeviceName().contains("400")){
								mxList.add(dev);
							}
						}
					}else if(dev.getPowerVoltGrade() > 10&&dev.getDeviceType().equals(SystemConstants.Switch)&&dev.getDeviceStatus().equals("1")){
						kgdy10kVList.add(dev);
					}
				}
				
				if(dycvolt == 6||dycvolt == 10){
					//核实内容
    				gethsnr(dycmlkgList,drkgList,zybkgList,kgdy10kVList,cm,ddName);
	    			
    				RuleExeUtil.swapDeviceList(mxList);
    				
    				dritem=new CardItemModel();
	    			dritem.setUuIds(StringUtils.getUUID());
					dritem.setCardDesc("落实"+stationName+"10kV负荷已转供，"+CZPService.getService().getDevName(mxList)+"具备停电条件");
	    			dritem.setShowName(ddName);
	    			dritem.setStationName(ddName);
	    			cm.getCardItems().add(dritem);
    				
	    			for(PowerDevice dev : drkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    			}
	    			
	    			for(PowerDevice dev : zybkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    			}
	    			
	    			for(PowerDevice dev : dycmlkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : dyczbkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			if(voltList.size() == 2){
	    				int gycvolt = Integer.valueOf(voltList.get(1));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
						for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : zycmlkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : zyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : gyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							dritem=new CardItemModel();
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}else{
					//核实内容
    				gethsnr(dycmlkgList,drkgList,zybkgList,kgdy10kVList,cm,ddName);
					
	    			if(voltList.size() == 2){
	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
									drkgList.add(dev);
								}
								
								if (dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")||dev.getPowerDeviceName().contains("接地变")){
									if(!dev.getPowerDeviceName().endsWith("站用变")&&!dev.getPowerDeviceName().endsWith("所用变")&&!dev.getPowerDeviceName().endsWith("接地变")){
										zybkgList.add(dev);
									}
								}
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == dycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									alldyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										dyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										dycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				
	    				for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : zycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : zyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}
				

    			Collections.sort(bztvoltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
    			
				cm.setCzrw(sta+"全站设备由运行转冷备用");
				
				for(PowerDevice dev : zbList){
					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
					dritem=new CardItemModel();
	    			dritem.setUuIds(StringUtils.getUUID());
					dritem.setCardDesc("将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用");
	    			dritem.setShowName(stationName);
	    			dritem.setStationName(stationName);
	    			cm.getCardItems().add(dritem);
				}
				
				for(PowerDevice dev : mxList){
					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
					dritem=new CardItemModel();
	    			dritem.setUuIds(StringUtils.getUUID());
					dritem.setCardDesc("将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用");
	    			dritem.setShowName(stationName);
	    			dritem.setStationName(stationName);
	    			cm.getCardItems().add(dritem);
				}
				
				CardItemModel item=new CardItemModel();

    			if(zbList.size()>0){
    				if(RuleUtil.isTransformerNQ(zbList.get(0))){
    					item=new CardItemModel();
    	    			item.setUuIds(StringUtils.getUUID());
    					item.setCardDesc("拉开"+CZPService.getService().getDevName(zbdzList));
    	    			item.setShowName(sta);
    	    			item.setStationName(sta);
    	    			cm.getCardItems().add(item);
    				}
    			}
    			
	 			ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
    			
		    	TempTicket ttk=TempTicket.getInstance();
		    	ttk.init(cm,Srcrbm);
		    	openNPWindow(ttk);
			}
		});
		
		if(CBSystemConstants.opcardUser.equals("OPCARDLJ.")){
			popupMenu.add(menuItem);
		}
		
		menuItem = new JMenuItem("全站复电");
		menuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				RuleBaseMode Srcrbm = new RuleBaseMode();
				Srcrbm.setPd(new PowerDevice());
				
				String powerStationID = null;
				SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
		        czprule.model.PowerDevice station =null;
		        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
		            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
		            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
		        }
		        if(powerStationID == ""||station==null)
		        	return ;
		        
		        List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dzList = new ArrayList<PowerDevice>();

		        List<String> voltList = new ArrayList<String>();
		        Set<String> set = new HashSet<String>();
		        List<String> bztvoltList = new ArrayList<String>();
		        Set<String> bztset = new HashSet<String>();
		        
		        HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(powerStationID);
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						zbList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
						set.add(String.valueOf((int)dev.getPowerVoltGrade()));
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							bztset.add(String.valueOf((int)dev.getPowerVoltGrade()));
							mlkgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS)){
						dzList.add(dev);
					}
				}

				for(Iterator<String> itor = set.iterator();itor.hasNext();){
					String volt = itor.next();
					
					if(!volt.equals("0")){
						voltList.add(volt);
					}
				}
				
				RuleExeUtil.sortListByDevName(zbkgList);
				CardModel cm = new CardModel();
		    	cm.setCardItems(new ArrayList<CardItemModel>());
    			String sta = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(powerStationID));
				
    			String ddName = "丽江地调";
    			
    			Collections.sort(voltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)>Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
    			
    			List<PowerDevice> mxList = new ArrayList<PowerDevice>();
    			
    			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&dev.getPowerVoltGrade()>=10){
						if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))){
							bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
					}
					
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&dev.getPowerVoltGrade()==10){
						mxList.add(dev);
					}
				}
    			
    			CardItemModel item1=new CardItemModel();
    			item1.setUuIds(StringUtils.getUUID());
				item1.setCardDesc("核实"+sta+"相关工作已结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，设备的保护装置已正常投入，确认"+sta+"具备复电条件");
    			item1.setShowName(sta);
    			item1.setStationName(sta);
    			cm.getCardItems().add(item1);
    			
    			RuleExeUtil.swapDeviceList(zbList);
    			RuleExeUtil.swapDeviceList(mxList);

    			for(PowerDevice dev : zbList){
    				CardItemModel item=new CardItemModel();
					item=new CardItemModel();
	    			item.setUuIds(StringUtils.getUUID());
					item.setCardDesc("将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用");
	    			item.setShowName(sta);
	    			item.setStationName(sta);
	    			cm.getCardItems().add(item);
				}
				
				for(PowerDevice dev : mxList){
    				CardItemModel item=new CardItemModel();
					item=new CardItemModel();
	    			item.setUuIds(StringUtils.getUUID());
					item.setCardDesc("将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用");
	    			item.setShowName(sta);
	    			item.setStationName(sta);
	    			cm.getCardItems().add(item);
				}
    			
    			Collections.sort(bztvoltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)>Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });

				if(voltList.size() == 2){
    				int dycvolt = Integer.valueOf(voltList.get(1));
    				int gycvolt = Integer.valueOf(voltList.get(0));
    				
    				List<PowerDevice>  gycmlkg = new ArrayList<PowerDevice>();
    				
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  gycxlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								gycxlkg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  dycmlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == dycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkg.add(dev);
							}
						}
					}
					
					for(PowerDevice dev : gycxlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					/*
					 * 从电源侧复电，先依次恢复主变（高中低）即主变三侧母线带电后，再恢复母线分路
					 */
					
					for(PowerDevice zb : zbList){
						List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
						zbswList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
						zbswList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
						
		    			for(PowerDevice sw : zbswList){
		    				CardItemModel item=new CardItemModel();
			    			item.setUuIds(StringUtils.getUUID());
							item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(sw));
							item.setShowName(ddName);
			    			item.setStationName(ddName);
			    			cm.getCardItems().add(item);
						}
					}
    			}else if(voltList.size() == 3){
    				List<PowerDevice>  dyckg = new ArrayList<PowerDevice>();
    				List<PowerDevice>  zyckg = new ArrayList<PowerDevice>();
    				List<PowerDevice>  gyckg = new ArrayList<PowerDevice>();
    				
    				int dycvolt = Integer.valueOf(voltList.get(2));
    				int zycvolt = Integer.valueOf(voltList.get(1));
    				int gycvolt = Integer.valueOf(voltList.get(0));

    				List<PowerDevice>  dycmlkg = new ArrayList<PowerDevice>();
    				
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == dycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkg.add(dev);
							}
						}
					}
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == dycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								dyckg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  zycmlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == zycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								zycmlkg.add(dev);
							}
						}
					}
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == zycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								zyckg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  zycxlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == zycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								zycxlkg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  gycmlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  gycxlkg = new ArrayList<PowerDevice>();

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								gycxlkg.add(dev);
							}
						}
					}
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								gyckg.add(dev);
							}
						}
					}
					
					/*
					 * 从电源侧复电，先依次恢复主变（高中低）即主变三侧母线带电后，再恢复母线分路
					 */
					
					for(PowerDevice dev : dyckg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : gycxlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : gycmlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : zycxlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : zycmlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : dycmlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : gyckg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : zyckg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}

					
					if(station.getPowerVoltGrade() >= 110){
	    		        List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();

	    				CardItemModel item=new CardItemModel();
	    				
		    			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							if(dev.getPowerVoltGrade() == 10&&dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
								if (dev.getPowerDeviceName().contains("站用变")){
									zybkgList.add(dev);
								}
							}
						}
		    			
		    			for(PowerDevice dev : zybkgList){
		    				item=new CardItemModel();
			    			item.setUuIds(StringUtils.getUUID());
							item.setCardDesc("遥控合上"+CZPService.getService().getDevName(dev));
			    			item.setShowName(ddName);
			    			item.setStationName(ddName);
			    			cm.getCardItems().add(item);
		    			}
	    			}
    			}
    			
    			cm.setCzrw(sta+"全站设备由冷备用转运行");
		    	TempTicket ttk=TempTicket.getInstance();
		    	ttk.init(cm,Srcrbm);
		    	openNPWindow(ttk);
			}
		});
		
		if(CBSystemConstants.opcardUser.equals("OPCARDLJ.")){
			popupMenu.add(menuItem);
		}
		
		menuItem = new JMenuItem("全站停电");
		menuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				RuleBaseMode Srcrbm = new RuleBaseMode();
				Srcrbm.setPd(new PowerDevice());
				
				String powerStationID = null;
				SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
		        czprule.model.PowerDevice station =null;
		        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
		            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
		            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
		        }
		        if(powerStationID == ""||station==null)
		        	return ;
				String stationName = CZPService.getService().getDevName(station);
				String ddName = "文山地调";

		        List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		        List<PowerDevice> xlList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dzList = new ArrayList<PowerDevice>();
		        List<PowerDevice> rbykgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbdzList = new ArrayList<PowerDevice>();

		        List<String> voltList = new ArrayList<String>();
		        Set<String> set = new HashSet<String>();
		        List<String> bztvoltList = new ArrayList<String>();
		        Set<String> bztset = new HashSet<String>();

		        RuleBaseMode rbm = new RuleBaseMode();
		        rbm.setBeginStatus("0");
		        rbm.setEndState("1");
		        rbm.setPd(pd);
		        pd.setPowerStationID(powerStationID);
		        StationAllDeviceTDExecute td = new StationAllDeviceTDExecute();
				td.execute(rbm);
		        
		        HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(powerStationID);
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
						zbList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
						set.add(String.valueOf((int)dev.getPowerVoltGrade()));
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							if(dev.getDeviceStatus().equals("1")){
								bztset.add(String.valueOf((int)dev.getPowerVoltGrade()));
							}
							mlkgList.add(dev);
						}
						
						if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() > 10&&!dev.getPowerDeviceName().contains("相")){
							rbykgList.add(dev);
						}else if(dev.getDeviceStatus().equals("1")&&dev.getPowerVoltGrade() == 10&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							rbykgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS)){
						dzList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.InOutLine)&&dev.getPowerVoltGrade() == 10){
						xlList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.InOutLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "1", true);
	    				//DeviceOperate.putDeviceStatus(dev);
					}
					
					 if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZBS)){
						 zbdzList.add(dev);
	    				//DeviceOperate.putDeviceStatus(dev);
					}
				}
				
				RuleExeUtil.swapDeviceListNum(rbykgList);

				for(Iterator<String> itor = set.iterator();itor.hasNext();){
					String volt = itor.next();
					
					if(!volt.equals("0")){
						voltList.add(volt);
					}
				}
				
				Collections.sort(voltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
				
				
				RuleExeUtil.sortListByDevName(zbkgList);
				CardModel cm = new CardModel();
		    	cm.setCardItems(new ArrayList<CardItemModel>());
    			String sta = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(powerStationID));
				
    			RuleExeUtil.swapDeviceListNum(zbList);
    			
				CardItemModel dritem=new CardItemModel();
    			
    			List<PowerDevice> drkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dycmlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dyczbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mxList = new ArrayList<PowerDevice>();
		        List<PowerDevice> jdzybkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> kgdy10kVList = new ArrayList<PowerDevice>();

				int dycvolt = Integer.valueOf(voltList.get(0));

				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					
					 if(dev.getDeviceStatus().equals("1")){
						if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
							bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
					 }
					
					if((dev.getPowerVoltGrade() == 10||dev.getPowerVoltGrade() == 6)){
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								dyczbkgList.add(dev);
							}
						}
						
						if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							if (dev.getPowerDeviceName().contains("接地站用变")||dev.getPowerDeviceName().contains("接地变")){
								jdzybkgList.add(dev);
							}
						}
						
						
						if(dev.getDeviceStatus().equals("1")&&dev.getDeviceType().equals(SystemConstants.Switch)){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
								drkgList.add(dev);
							}
							
							if (dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")){
								zybkgList.add(dev);
							}
						
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkgList.add(dev);
							}
						}
						
						if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
							if(!dev.getPowerDeviceName().contains("400")){
								mxList.add(dev);
							}
						}
					}else if(dev.getPowerVoltGrade() > 10&&dev.getDeviceType().equals(SystemConstants.Switch)&&dev.getDeviceStatus().equals("1")){
						kgdy10kVList.add(dev);
					}
				}
				
				if(dycvolt == 6||dycvolt == 10){
					//核实内容
    				gethsnr(dycmlkgList,drkgList,zybkgList,kgdy10kVList,cm,ddName);
	    			
    				RuleExeUtil.swapDeviceList(mxList);
    				
    				dritem=new CardItemModel();
	    			dritem.setUuIds(StringUtils.getUUID());
					dritem.setCardDesc("核实"+stationName+"具备停电条件");
	    			dritem.setShowName("文山配调");
	    			dritem.setStationName(ddName);
	    			cm.getCardItems().add(dritem);
    				
	    			for(PowerDevice dev : drkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    			}
	    			
	    			for(PowerDevice dev : zybkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    			}
	    			
	    			for(PowerDevice dev : dycmlkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			for(PowerDevice dev : dyczbkgList){
	    				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
	    				//DeviceOperate.putDeviceStatus(dev);
	    				dritem=new CardItemModel();
		    			dritem.setUuIds(StringUtils.getUUID());
		    			
						dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
		    			dritem.setShowName(ddName);
		    			dritem.setStationName(ddName);
		    			cm.getCardItems().add(dritem);
	    			}
	    			
	    			if(voltList.size() == 2){
	    				int gycvolt = Integer.valueOf(voltList.get(1));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
						for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}
						}
	    				
						for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
				    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : zycmlkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : zyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : gyczbkg){
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
			    			RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							dritem=new CardItemModel();
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}else{
					//核实内容
    				gethsnr(dycmlkgList,drkgList,zybkgList,kgdy10kVList,cm,ddName);
					
	    			if(voltList.size() == 2){
	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}else if(voltList.size() == 3){
	    				int zycvolt = Integer.valueOf(voltList.get(1));
	    				int gycvolt = Integer.valueOf(voltList.get(2));

	    				List<PowerDevice> allgyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> gycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> allzyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> zycmlkg = new ArrayList<PowerDevice>();
	    				
	    				List<PowerDevice> alldyckg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dyczbkg = new ArrayList<PowerDevice>();
	    				List<PowerDevice> dycmlkg = new ArrayList<PowerDevice>();
	    				
	    				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							
							if(dev.getDeviceStatus().equals("1")){
								if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))&&dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&(int)dev.getPowerVoltGrade() !=0){
									bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
								}
							}
							
							if(dev.getDeviceType().equals(SystemConstants.Switch)){
								if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
									drkgList.add(dev);
								}
								
								if (dev.getPowerDeviceName().contains("站用变")||dev.getPowerDeviceName().contains("所用变")||dev.getPowerDeviceName().contains("接地变")){
									if(!dev.getPowerDeviceName().endsWith("站用变")&&!dev.getPowerDeviceName().endsWith("所用变")&&!dev.getPowerDeviceName().endsWith("接地变")){
										zybkgList.add(dev);
									}
								}
							}
							
							if(dev.getPowerVoltGrade() == gycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allgyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
										gyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										gycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == dycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									alldyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										dyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										dycmlkg.add(dev);
									}
								}
							}else if(dev.getPowerVoltGrade() == zycvolt){
								if(dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
									allzyckg.add(dev);
									
									if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
										zyczbkg.add(dev);
									}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
										zycmlkg.add(dev);
									}
								}
							}
						}
	    				
	    				for(PowerDevice dev : alldyckg){
							if(!dyczbkg.contains(dev)&&!dycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : dycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : dyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				
	    				for(PowerDevice dev : allzyckg){
							if(!zyczbkg.contains(dev)&&!zycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
	    				
	    				for(PowerDevice dev : zycmlkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : zyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    				
	    				for(PowerDevice dev : gyczbkg){
	    					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
						
						for(PowerDevice dev : allgyckg){
							if(!gyczbkg.contains(dev)&&!gycmlkg.contains(dev)){
								RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
			    				//DeviceOperate.putDeviceStatus(dev);
								dritem=new CardItemModel();
				    			dritem.setUuIds(StringUtils.getUUID());
								dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
				    			dritem.setShowName(ddName);
				    			dritem.setStationName(ddName);
				    			cm.getCardItems().add(dritem);
							}
		    			}
						
						for(PowerDevice dev : gycmlkg){
							RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
		    				//DeviceOperate.putDeviceStatus(dev);
							dritem=new CardItemModel();
			    			dritem.setUuIds(StringUtils.getUUID());
							dritem.setCardDesc("遥控断开"+stationName+CZPService.getService().getDevName(dev));
			    			dritem.setShowName(ddName);
			    			dritem.setStationName(ddName);
			    			cm.getCardItems().add(dritem);
		    			}
	    			}
				}
				

    			Collections.sort(bztvoltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)<Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
    			
				cm.setCzrw(sta+"全站设备由运行转冷备用");
				
				for(PowerDevice dev : zbList){
					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
					dritem=new CardItemModel();
	    			dritem.setUuIds(StringUtils.getUUID());
					dritem.setCardDesc("将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用");
	    			dritem.setShowName(stationName);
	    			dritem.setStationName(stationName);
	    			cm.getCardItems().add(dritem);
				}
				
				for(PowerDevice dev : mxList){
					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), "2", true);
					dritem=new CardItemModel();
	    			dritem.setUuIds(StringUtils.getUUID());
					dritem.setCardDesc("将"+CZPService.getService().getDevName(dev)+"由热备用转冷备用");
	    			dritem.setShowName(stationName);
	    			dritem.setStationName(stationName);
	    			cm.getCardItems().add(dritem);
				}
				
				CardItemModel item=new CardItemModel();

    			if(zbList.size()>0){
    				if(RuleUtil.isTransformerNQ(zbList.get(0))){
    					item=new CardItemModel();
    	    			item.setUuIds(StringUtils.getUUID());
    					item.setCardDesc("拉开"+CZPService.getService().getDevName(zbdzList));
    	    			item.setShowName(sta);
    	    			item.setStationName(sta);
    	    			cm.getCardItems().add(item);
    				}
    			}
    			
	 			ExecuteDeviceSVGAction(CBSystemConstants.getDtdMap());
    			
		    	TempTicket ttk=TempTicket.getInstance();
		    	ttk.init(cm,Srcrbm);
		    	openNPWindow(ttk);
			}
		});
		
		if(CBSystemConstants.opcardUser.equals("OPCARDWS.")){
			popupMenu.add(menuItem);
		}
		
		menuItem = new JMenuItem("全站复电");
		menuItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				RuleBaseMode Srcrbm = new RuleBaseMode();
				Srcrbm.setPd(new PowerDevice());
				
				String powerStationID = null;
				SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
		        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
		        czprule.model.PowerDevice station =null;
		        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
		            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
		            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
		        }
		        if(powerStationID == ""||station==null)
		        	return ;
		        
		        List<PowerDevice> zbList = new ArrayList<PowerDevice>();
		        List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> zbkgList = new ArrayList<PowerDevice>();
		        List<PowerDevice> dzList = new ArrayList<PowerDevice>();

		        List<String> voltList = new ArrayList<String>();
		        Set<String> set = new HashSet<String>();
		        List<String> bztvoltList = new ArrayList<String>();
		        Set<String> bztset = new HashSet<String>();
		        
		        HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(powerStationID);
				
				for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						zbList.add(dev);
					}else if (dev.getDeviceType().equals(SystemConstants.Switch)){
						set.add(String.valueOf((int)dev.getPowerVoltGrade()));
						if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
							bztset.add(String.valueOf((int)dev.getPowerVoltGrade()));
							mlkgList.add(dev);
						}
					}else if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS)){
						dzList.add(dev);
					}
				}

				for(Iterator<String> itor = set.iterator();itor.hasNext();){
					String volt = itor.next();
					
					if(!volt.equals("0")){
						voltList.add(volt);
					}
				}
				
				RuleExeUtil.sortListByDevName(zbkgList);
				CardModel cm = new CardModel();
		    	cm.setCardItems(new ArrayList<CardItemModel>());
    			String sta = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(powerStationID));
				
    			String ddName = "文山地调";
    			
    			Collections.sort(voltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)>Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });
    			
    			List<PowerDevice> mxList = new ArrayList<PowerDevice>();
    			
    			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
					PowerDevice dev = it2.next();
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)&&dev.getPowerVoltGrade()>=10){
						if(!bztvoltList.contains(String.valueOf((int)dev.getPowerVoltGrade()))){
							bztvoltList.add(String.valueOf((int)dev.getPowerVoltGrade()));
						}
					}
					
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&dev.getPowerVoltGrade()==10){
						mxList.add(dev);
					}
				}
    			
    			CardItemModel item1=new CardItemModel();
    			item1.setUuIds(StringUtils.getUUID());
				item1.setCardDesc("核实站内相关工作已全部结束，作业人员已全部撤离，现场所有临时措施已拆除，现场自行操作的接地开关已全部拉开，该设备的保护装置已正常投入，设备具备送电条件");
    			item1.setShowName(sta);
    			item1.setStationName(sta);
    			cm.getCardItems().add(item1);
    			
    			RuleExeUtil.swapDeviceList(zbList);
    			RuleExeUtil.swapDeviceList(mxList);

    			for(PowerDevice dev : zbList){
    				CardItemModel item=new CardItemModel();
					item=new CardItemModel();
	    			item.setUuIds(StringUtils.getUUID());
					item.setCardDesc("将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用");
	    			item.setShowName(sta);
	    			item.setStationName(sta);
	    			cm.getCardItems().add(item);
				}
				
				for(PowerDevice dev : mxList){
    				CardItemModel item=new CardItemModel();
					item=new CardItemModel();
	    			item.setUuIds(StringUtils.getUUID());
					item.setCardDesc("将"+CZPService.getService().getDevName(dev)+"由冷备用转热备用");
	    			item.setShowName(sta);
	    			item.setStationName(sta);
	    			cm.getCardItems().add(item);
				}
    			
    			Collections.sort(bztvoltList, new Comparator<String>() {
			        public int compare(String p1, String p2) {
			           if(Integer.valueOf(p1)>Integer.valueOf(p2)) {
			              return -1;
			           }
			           else {
			              return 1;
			           }
			        }
			    });

				if(voltList.size() == 2){
    				int dycvolt = Integer.valueOf(voltList.get(1));
    				int gycvolt = Integer.valueOf(voltList.get(0));
    				
    				List<PowerDevice>  gycmlkg = new ArrayList<PowerDevice>();
    				
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  gycxlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								gycxlkg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  dycmlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == dycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkg.add(dev);
							}
						}
					}
					
					for(PowerDevice dev : gycxlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					/*
					 * 从电源侧复电，先依次恢复主变（高中低）即主变三侧母线带电后，再恢复母线分路
					 */
					
					for(PowerDevice zb : zbList){
						List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
						zbswList.addAll(RuleExeUtil.getTransformerSwitchHigh(zb));
						zbswList.addAll(RuleExeUtil.getTransformerSwitchLow(zb));
						
		    			for(PowerDevice sw : zbswList){
		    				CardItemModel item=new CardItemModel();
			    			item.setUuIds(StringUtils.getUUID());
							item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(sw));
							item.setShowName(ddName);
			    			item.setStationName(ddName);
			    			cm.getCardItems().add(item);
						}
					}
    			}else if(voltList.size() == 3){
    				List<PowerDevice>  dyckg = new ArrayList<PowerDevice>();
    				List<PowerDevice>  zyckg = new ArrayList<PowerDevice>();
    				List<PowerDevice>  gyckg = new ArrayList<PowerDevice>();
    				
    				int dycvolt = Integer.valueOf(voltList.get(2));
    				int zycvolt = Integer.valueOf(voltList.get(1));
    				int gycvolt = Integer.valueOf(voltList.get(0));

    				List<PowerDevice>  dycmlkg = new ArrayList<PowerDevice>();
    				
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == dycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								dycmlkg.add(dev);
							}
						}
					}
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == dycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								dyckg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  zycmlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == zycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								zycmlkg.add(dev);
							}
						}
					}
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == zycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								zyckg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  zycxlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == zycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								zycxlkg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  gycmlkg = new ArrayList<PowerDevice>();
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								gycmlkg.add(dev);
							}
						}
					}
					
    				List<PowerDevice>  gycxlkg = new ArrayList<PowerDevice>();

					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								gycxlkg.add(dev);
							}
						}
					}
					
					for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
						PowerDevice dev = it2.next();
						if(dev.getPowerVoltGrade() == gycvolt&&dev.getDeviceStatus().equals("0")){
							if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
								gyckg.add(dev);
							}
						}
					}
					
					/*
					 * 从电源侧复电，先依次恢复主变（高中低）即主变三侧母线带电后，再恢复母线分路
					 */
					
					for(PowerDevice dev : dyckg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : gycxlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : gycmlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : zycxlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : zycmlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : dycmlkg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : gyckg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}
					
					for(PowerDevice dev : zyckg){
						CardItemModel item=new CardItemModel();
		    			item.setUuIds(StringUtils.getUUID());
						item.setCardDesc("遥控合上"+sta+CZPService.getService().getDevName(dev));
		    			item.setShowName(ddName);
		    			item.setStationName(ddName);
		    			cm.getCardItems().add(item);
					}

					
					if(station.getPowerVoltGrade() >= 110){
	    		        List<PowerDevice> zybkgList = new ArrayList<PowerDevice>();

	    				CardItemModel item=new CardItemModel();
	    				
		    			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
							PowerDevice dev = it2.next();
							if(dev.getPowerVoltGrade() == 10&&dev.getDeviceStatus().equals("0")&&dev.getDeviceType().equals(SystemConstants.Switch)){
								if (dev.getPowerDeviceName().contains("站用变")){
									zybkgList.add(dev);
								}
							}
						}
		    			
		    			for(PowerDevice dev : zybkgList){
		    				item=new CardItemModel();
			    			item.setUuIds(StringUtils.getUUID());
							item.setCardDesc("遥控合上"+CZPService.getService().getDevName(dev));
			    			item.setShowName(ddName);
			    			item.setStationName(ddName);
			    			cm.getCardItems().add(item);
		    			}
	    			}
    			}
    			
    			cm.setCzrw(sta+"全站设备由冷备用转运行");
		    	TempTicket ttk=TempTicket.getInstance();
		    	ttk.init(cm,Srcrbm);
		    	openNPWindow(ttk);
			}
		});
		
		if(CBSystemConstants.opcardUser.equals("OPCARDWS.")){
			popupMenu.add(menuItem);
		}
		
		return popupMenu;
	}
	

	
	/**
	 * 更新设备版本记录
	 * @param rs
	 * @param stam
	 * @param ver_id
	 * @param powerStationID
	 * @throws SQLException 
	 */
	protected void updateDevice(Connection conn, ResultSet rs, String ver_id, String powerStationID) throws SQLException {//黄翔修改
			
			//更新设备
	        Map<String, czprule.model.PowerDevice> deviceMap = CBSystemConstants.getMapPowerStationDevice().get(powerStationID);
	        if(deviceMap == null){
	        	 CreatePowerStationToplogy.loadFacEquip(powerStationID);
	        	 deviceMap = CBSystemConstants.getMapPowerStationDevice().get(powerStationID);
	        	 if(deviceMap==null){
	        		 System.out.println(powerStationID+"无法加载");
	        		 return;
	        		 
	        	 }
	        	}
	        PreparedStatement pstam = (PreparedStatement)conn.prepareStatement("update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = ? where ver_id=? and obj_id = ?");
	        PreparedStatement pstam1 = (PreparedStatement)conn.prepareStatement("insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values (?,'1',?,?,?)");
	        for(Map.Entry<String, czprule.model.PowerDevice> deviceEntry : deviceMap.entrySet()){
	        	String obj_id = deviceEntry.getValue().getPowerDeviceID();
	        	String obj_state = deviceEntry.getValue().getDeviceStatus();
	        	
	        	//String updateDeviceSql = "update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = '" + obj_state + "' where ver_id='" + ver_id + "' and obj_id = '" + obj_id + "'";
	        	int i = 0;
	        	try {
	        		pstam.setString(1,obj_state);
	        		pstam.setString(2,ver_id);
	        		pstam.setString(3,obj_id);
	        		i = pstam.executeUpdate();
					if(i == 0){//如果更新失败，表明没有这个设备的数据，则插入当前设备的版本信息到表中
						//String insertDeviceSql = "insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values ('" + obj_id + "','1','" + powerStationID + "','" + obj_state + "','" + ver_id + "')";
						//stam.executeUpdate(insertDeviceSql);
						pstam1.setString(1,obj_id);
		        		pstam1.setString(2,powerStationID);
		        		pstam1.setString(3,obj_state);
		        		pstam1.setString(4,ver_id);
						pstam1.addBatch();
					}
				} catch (SQLException e1) {
					e1.printStackTrace();
				}
	        }
	        try {
				pstam1.executeBatch();
				pstam1.clearBatch();
			} catch (SQLException e2) {
				// TODO Auto-generated catch block
				e2.printStackTrace();
			}
			//更新安置设备
//            String anzhiSql = "select t1.scs_id, t1.station_id,t2.change_state from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t1, "+CBSystemConstants.opcardUser+"T_A_ECSAUTRECORD t2,"+CBSystemConstants.opcardUser+"t_e_equipinfo t3 where t1.scs_id = t2.scs_id and t1.scs_obj_code=t3.equip_id and t3.station_id = '" + powerStationID + "'";//查询当前厂站的安置设备
            try {
            	Statement stmt = conn.createStatement();
            	//edit 2014.6.24
				rs = stmt.executeQuery(OPEService.getService().updateDevice1(powerStationID));
				pstam = (PreparedStatement)conn.prepareStatement("update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = ? where ver_id=? and obj_id = ?");
				pstam1 = (PreparedStatement)conn.prepareStatement("insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values (?,'2',?,?,?)");
				while(rs.next()){
					String scs_id = rs.getString("scs_id");
					String change_state = rs.getString("change_state");
					//String updateAnZhiSql = "update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = '" + change_state + "' where ver_id='" + ver_id + "' and obj_id = '" + scs_id + "'";//更新安置设备
					pstam.setString(1,change_state);
	        		pstam.setString(2,ver_id);
	        		pstam.setString(3,scs_id);
					int i = 0;
					i = pstam.executeUpdate();
					if(i == 0){//如果更新失败，表明没有这个安置设备的数据，则插入当前安置设备的版本信息到表中
						//String insertAnZhiSql = "insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values ('" + scs_id + "','2','" + powerStationID + "','" + change_state + "','" + ver_id + "')";
						//stam.executeUpdate(insertAnZhiSql);
						pstam1.setString(1,scs_id);
		        		pstam1.setString(2,powerStationID);
		        		pstam1.setString(3,change_state);
		        		pstam1.setString(4,ver_id);
						pstam1.addBatch();
					}
				}
				pstam1.executeBatch();
				pstam1.clearBatch();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
            //更新跳投关系
//            String tiaotouSql = "select t2.relation_id,t3.change_state from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t1, "+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE t2,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD t3,"+CBSystemConstants.opcardUser+"t_e_equipinfo t4 where t1.scs_id = t2.scs_id and t1.scs_obj_code=t4.equip_id and t2.relation_id = t3.device_id and t4.station_id = '" + powerStationID + "'";
            try {
            	Statement stmt = conn.createStatement();
            	//edit 2014.6.24
				rs = stmt.executeQuery(OPEService.getService().updateDevice2(powerStationID));
				pstam = (PreparedStatement)conn.prepareStatement("update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = ? where ver_id=? and obj_id = ?");
				pstam1 = (PreparedStatement)conn.prepareStatement("insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values (?,'3',?,?,?)");
				while(rs.next()){
					String relation_id = rs.getString("relation_id");
					String change_state = rs.getString("change_state");
					//String updateTiaoTouSql = "update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = '" + change_state + "' where ver_id='" + ver_id + "' and obj_id = '" + relation_id + "'";
					pstam.setString(1,change_state);
	        		pstam.setString(2,ver_id);
	        		pstam.setString(3,relation_id);
					int i = 0;
					i = pstam.executeUpdate();
					if(i == 0){//如果更新失败，表明没有这个跳投关系的数据，则插入当前跳投关系的版本信息到表中
						//String insertTiaoTouSql = "insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values ('" + relation_id + "','3','" + powerStationID + "','" + change_state + "','" + ver_id + "')";
						//stam.executeUpdate(insertTiaoTouSql);
						pstam1.setString(1,relation_id);
		        		pstam1.setString(2,powerStationID);
		        		pstam1.setString(3,change_state);
		        		pstam1.setString(4,ver_id);
						pstam1.addBatch();
					}
				}
				pstam1.executeBatch();
				pstam1.clearBatch();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
            if(rs != null) {
				try {
					rs.close();
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
            }
	}
	
	//打开拟票窗口
	private void openNPWindow(TempTicket ttk){
		if(SystemConstants.isInitNewWin.equals("0")){
	    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
	    	Dimension scrsiz = Toolkit.getDefaultToolkit().getScreenSize();
//						    	System.out.println("屏幕大小"+scrsiz);
	    	double bl = 0;
	    	if(scrsiz.width>1280){
	    		bl = 0.52;
	    	}else if(scrsiz.width<=1280&&scrsiz.width>1024){
	    		bl = 0.42;
	    	}else if(scrsiz.width<=1024&&scrsiz.width>800){
	    		bl = 0.32;
	    	}else{
	    		bl =0.15;
	    	}
	    	double minbl = 1-Double.valueOf(ttk.getPanelLength())/scrsiz.width;
	    	if(minbl < bl && minbl > 0 && minbl < 1)
	    		bl = minbl;
	    	splitPane.setDividerLocation(bl);
			splitPane.setRightComponent(ttk);
    	}else{
			//修改版窗体可移动
    		//双屏
    		if(SystemConstants.isInitDoubleScreen.equals("1")){
    			GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
    	        GraphicsDevice[] gs = ge.getScreenDevices();
    	        int x=0;
    	        int y=0;
    	        int width=0;
    	        int height=0;
    	        if(gs.length>1){
    	        	Rectangle fram=SystemConstants.getGuiBuilder().getJFrame().getBounds();
    	        	int x0=(int)gs[0].getDefaultConfiguration().getBounds().getX();
    	        	int y0=(int)gs[0].getDefaultConfiguration().getBounds().getY();
    	        	int width0=(int)gs[0].getDefaultConfiguration().getBounds().getWidth();
    	        	int height0=(int)gs[0].getDefaultConfiguration().getBounds().getHeight()-100;
    	        	int x1=(int)gs[1].getDefaultConfiguration().getBounds().getX();
    	        	int y1=(int)gs[1].getDefaultConfiguration().getBounds().getY();
    	        	int width1=(int)gs[1].getDefaultConfiguration().getBounds().getWidth();
    	        	int height1=(int)gs[1].getDefaultConfiguration().getBounds().getHeight()-100;
    	        	int xg=(int)fram.getX();
    	        	if(xg-x0>100){
    	        		x=x0;
    	        		y=y0;
    	        		width=width0;
    	        		height=height0;
    	        	}else{
    	        		x=x1;
    	        		y=y1;
    	        		width=width1;
    	        		height=height1;
    	        	}
    	        	
    				ttk.setBounds(x, y, width, height);
    	        }
    		}
    	}
	}
		
	 private void ExecuteDeviceSVGAction(Map<Integer, DispatchTransDevice> curtransDevMap){
		DispatchTransDevice dtd=null;
		PowerDevice dev=null;
		for (int j = 1; j < curtransDevMap.size() + 1; j++) {    //遍历元件状态 排除开关
        	dtd = curtransDevMap.get(j);
        	dev = dtd.getTransDevice();
        	//20131119
        	//if(SystemConstants.Switch.equals(dev.getDeviceType())&&dtd.getFlag().equals("1"))
        	//	continue;
        	if((dtd.getBeginstatus().equals("3")&&dtd.getEndstate().equals("2"))
        			||(dtd.getBeginstatus().equals("2")&&dtd.getEndstate().equals("3"))){
        		CBSystemConstants.jxDtd=true;
        	}else {
        		CBSystemConstants.jxDtd=false;
			}
        	DeviceSVGPanelUtil.changeDeviceSVGColor(dev);
        	CBSystemConstants.jxDtd=false;
        }
	}
	 
	 private void gethsnr(List<PowerDevice> dycmlkgList,List<PowerDevice> drkgList,List<PowerDevice> zybkgList,List<PowerDevice> kgdy10kVList,CardModel cm,String ddName){
		List<PowerDevice> hsyxkgList = new ArrayList<PowerDevice>();
		List<PowerDevice> hsrbykgList = new ArrayList<PowerDevice>();
		List<PowerDevice> hslbykgList = new ArrayList<PowerDevice>();
		CardItemModel dritem = new CardItemModel();

		//核实低压侧分段断路器、电容器组及断路器、电抗器及断路器、站用变及断路器热备用
		
		for(PowerDevice mlkg : dycmlkgList){
			if(mlkg.getDeviceStatus().equals("1")){
				hsrbykgList.add(mlkg);
			}
		}
		
		for(PowerDevice drkg : drkgList){
			if(drkg.getDeviceStatus().equals("1")){
				hsrbykgList.add(drkg);
			}
		}
		
		for(PowerDevice zybkg : zybkgList){
			if(zybkg.getDeviceStatus().equals("1")){
				hsrbykgList.add(zybkg);
			}
		}
		
		hsrbykgList.addAll(kgdy10kVList);
		
		if(hsrbykgList.size()>0){
			dritem=new CardItemModel();
 			dritem.setUuIds(StringUtils.getUUID());
			dritem.setCardDesc("核实"+CZPService.getService().getDevName(hsrbykgList)+"热备用");
 			dritem.setShowName(ddName );
 			dritem.setStationName(ddName);
 			cm.getCardItems().add(dritem);
		}
		
		//2、遥控断开站用变及断路器（如果在运行）；核实站用变及断路器冷备用（在冷备用才核实，热备用不出）
		
		for(PowerDevice zybkg : zybkgList){
			if(zybkg.getDeviceStatus().equals("2")){
				hslbykgList.add(zybkg);
			}
		}
		
		if(hslbykgList.size()>0){
			dritem=new CardItemModel();
		dritem.setUuIds(StringUtils.getUUID());
			dritem.setCardDesc("核实"+CZPService.getService().getDevName(hslbykgList)+"冷备用");
		dritem.setShowName(ddName);
		dritem.setStationName(ddName);
		cm.getCardItems().add(dritem);
		}
		
		for(PowerDevice zybkg : zybkgList){
			if(zybkg.getDeviceStatus().equals("0")){
				hsyxkgList.add(zybkg);
			}
		}
		
		if(hsyxkgList.size()>0){
			dritem=new CardItemModel();
			dritem.setUuIds(StringUtils.getUUID());
			dritem.setCardDesc("遥控断开"+CZPService.getService().getDevName(hsyxkgList));
			dritem.setShowName(ddName);
			dritem.setStationName(ddName);
			cm.getCardItems().add(dritem);
		
			dritem=new CardItemModel();
			dritem.setUuIds(StringUtils.getUUID());
			dritem.setCardDesc("核实"+CZPService.getService().getDevName(hsyxkgList)+"热备用");
			dritem.setShowName(ddName);
			dritem.setStationName(ddName);
			cm.getCardItems().add(dritem);
		}
	 }
}

