package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMLKGFDTRBZTZZ implements TempStringReplace {


	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if("母联开关复电投入备自投装置".equals(tempStr)){
			PowerDevice station = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			
			List<PowerDevice> zbLists = new ArrayList<PowerDevice>();
			List<PowerDevice> mxLists = new ArrayList<PowerDevice>();
			List<PowerDevice> mlkgList = new ArrayList<PowerDevice>();

			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(stationDev.getPowerStationID());

			for (Iterator<PowerDevice> it2 = mapStationDevice.values().iterator(); it2.hasNext();) {
				PowerDevice dev = it2.next();
				if (dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					if(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade()){
						if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
							mlkgList.add(dev);
						}
					}
				}
				
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
					zbLists.add(dev);
				}else if (dev.getDeviceType().equals(SystemConstants.MotherLine)&&(dev.getPowerVoltGrade() == curDev.getPowerVoltGrade())){
					if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
						mxLists.add(dev);
					}
				}
			}
			
			if(mlkgList.size()>0){
				if(station.getPowerVoltGrade() == 220&&curDev.getPowerVoltGrade()<220){
					return 	null;
				}else if(station.getPowerVoltGrade() == 220&&curDev.getPowerVoltGrade()==220&&curDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					return 	null;
				}
				
				if(zbLists.size() == 2){
					if(station.getPowerVoltGrade() == 220&&curDev.getPowerVoltGrade()<220){
						
					}else{
						replaceStr += CZPService.getService().getDevName(station)+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}
				}else if(zbLists.size() == 3){
					if(mxLists.size() == 2){
						replaceStr += CZPService.getService().getDevName(station)+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
					}else {
						boolean flag = false;
						
						List<PowerDevice> tagdevList = new ArrayList<PowerDevice>();
						
						for(PowerDevice dev : mlkgList){
							if(RuleExeUtil.getDeviceBeginStatus(dev).equals("2")){
								List<PowerDevice> mxList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
								
								for(PowerDevice mx : mxList){
									List<PowerDevice> zbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);

									if(zbList.size()==0){
										flag = true;
										break;
									}else{
										if(!tagdevList.contains(dev)){
											tagdevList.add(dev);
										}
									}
								}
							}
						}
						
						if(flag){
							replaceStr += CZPService.getService().getDevName(station)+"@投入"+(int)curDev.getPowerVoltGrade()+"kV备自投装置/r/n";
						}else{
							for(PowerDevice tagdev : tagdevList){
								replaceStr += CZPService.getService().getDevName(station)+"@投入"+CZPService.getService().getDevName(tagdev)+"备自投装置/r/n";
							}
						}
					}
				}
			}
		}	
		
		if(replaceStr.equals("")){
			replaceStr = null;
		}
		
		return 	replaceStr;
	}

}
