package com.tellhow.czp.app.yndd.rule.zt;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JDialog;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

public class ZTZYBZTXZ implements RulebaseInf {
	public static Map<String,String> retMap = new HashMap<String, String>();
	
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		
		String zybName = "";
		
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_LINEZYB WHERE LINE_ID = '"+pd.getPowerDeviceID()+"'";
			List<Map<String,String>> zybList =  DBManager.queryForList(sql);

			for(Map<String,String> map : zybList){
				zybName = StringUtils.ObjToString(map.get("ZYB_NAME"));
			}
		}else{
			String sql = "SELECT ZYB_NAME FROM "+CBSystemConstants.opcardUser+"T_A_STATIONZYB WHERE DEV_ID = '"+pd.getPowerDeviceID()+"'";
			List<Map<String,String>> zybList =  DBManager.queryForList(sql);

			for(Map<String,String> map : zybList){
				zybName = StringUtils.ObjToString(map.get("ZYB_NAME"));
			}
		}
		
		if(zybName.equals("")){
			ShowMessage.view("当前设备上未维护站用变，请检查！");
			return false;
		}
		
		Map<String,String> map = new HashMap<String, String>();
		
		PowerDevice station = CBSystemConstants.getPowerStation(pd.getPowerStationID());
		
		map.put("STATIONNAME", CZPService.getService().getDevName(station));
		map.put("DEVICENAME", zybName);
		map.put("BEGINSTATUS", RuleExeUtil.getStatus(rbm.getBeginStatus()));
		map.put("ENDSTATUS", RuleExeUtil.getStatus(rbm.getEndState()));

		EquipStatusDialog esd = new EquipStatusDialog(SystemConstants.getMainFrame(),map);
		esd.setVisible(true);
		
		if(esd.isCancel()){
			return false;
		}
		
		retMap =  esd.getTagStatusMap();
		
		return true;
	}

}
