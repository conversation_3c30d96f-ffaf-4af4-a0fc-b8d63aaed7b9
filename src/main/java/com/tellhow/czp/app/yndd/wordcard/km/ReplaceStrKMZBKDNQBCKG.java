package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrKMZBKDNQBCKG implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";
		
		if(tempStr.equals("昆明主变扩大内桥本侧开关")){
			List<PowerDevice> gyckgList = RuleExeUtil.getDeviceList(curDev,null,SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, CBSystemConstants.RunTypeSwitchML, false, true, false, true,true);
			
			replaceStr = CZPService.getService().getDevName(gyckgList);
			
		}
		
		return replaceStr;
	}
}
