package com.tellhow.czp.app.yndd.tool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.yndd.rule.RuleUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.model.CardItemModel;

public class GetDtdDataZT extends GetDtdData{
	public static ArrayList<String[]> getData(List<CardItemModel> itemModelsShow,String ddname){
		ArrayList<String[]> data = new ArrayList<String[]>();
		
		for(CardItemModel cim : itemModelsShow){
			String cznr = cim.getCardDesc();
			String czname = cim.getBdzName();
			String stationid = cim.getCzdwID();
			String stationName = cim.getStationName();
			String uuids = cim.getUuIds();

			String isyk = "1";
			String devname= "";
			String startZT= "";
			String endZT= "";
			String deviceType= "";

			if (stationName.endsWith("=N=")) {
				stationName = stationName.replace("=N=", "");
			}

			List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(stationName,cznr);

			if (cznr.contains("=N=")) {
				cznr = cznr.replace("=N=", "");
			}
			
			if(rbmList.size()>0){
				PowerDevice dev = rbmList.get(0).getPd();
				
				if(dev!=null){
					String begin = rbmList.get(0).getBeginStatus();
					String end = rbmList.get(0).getEndState();
					String operation = rbmList.get(0).getOperaTion();
					
					if(!dev.getPowerStationID().equals("")){
						PowerDevice stationDev = CBSystemConstants.getPowerStation(dev.getPowerStationID());
						czname = CZPService.getService().getDevName(stationDev);
						devname = dev.getPowerDeviceName();
					}else{
						czname = stationName;
					}
					
					if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
						if(!end.equals("")&&!begin.equals("")){
							List<PowerDevice> swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								List<PowerDevice> newSwList = new ArrayList<PowerDevice>();
								
								for(PowerDevice sw : swList){
									if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
										if(RuleExeUtil.isSwMiddleInThreeSecond(sw)){
											newSwList.add(0, sw);
										}else{
											newSwList.add(sw);
										}
									}else{
										newSwList.add(sw);
									}
								}
								
								if((begin.equals("0")&&end.equals("1"))){
									for(PowerDevice kg : newSwList){
										String devid = kg.getPowerDeviceID();
										cznr  =  "遥控断开"+czname+CZPService.getService().getDevName(kg);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
									}
								}else if((begin.equals("1")&&end.equals("2"))){
									getSwitchSequenceTd(newSwList, data, uuids, czname, ddname);
								}
							}else{
								List<PowerDevice> newSwList = new ArrayList<PowerDevice>();
								
								for(PowerDevice sw : swList){
									if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
										if(!RuleExeUtil.isSwMiddleInThreeSecond(sw)){
											newSwList.add(0, sw);
										}else{
											newSwList.add(sw);
										}
									}else{
										newSwList.add(sw);
									}
								}
								
								if((begin.equals("1")&&end.equals("0"))){
									for(PowerDevice kg : newSwList){
										String devid = kg.getPowerDeviceID();
										cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(kg);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									}
								}else if((begin.equals("2")&&end.equals("1"))){
									getSwitchSequenceFd(newSwList, data, uuids, czname, ddname);
								}
							}
						}
					}else if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
						List<PowerDevice> swList = new ArrayList<PowerDevice>();
						List<PowerDevice> qtdzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeQT,"",true, true, true, true);
						
						//母线为高压侧，且为单电源单刀闸情况
						List<PowerDevice> xldzList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXLS,"",true, true, true, true);

						if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
							swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchFHC,false, true, true, true);
						}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
							swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						}
						
						List<PowerDevice> drswList = new ArrayList<PowerDevice>();
						List<PowerDevice> dkswList = new ArrayList<PowerDevice>();
						List<PowerDevice> xlswList = new ArrayList<PowerDevice>();
						List<PowerDevice> jdbswList = new ArrayList<PowerDevice>();
						List<PowerDevice> zybswList = new ArrayList<PowerDevice>();
						List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
						List<PowerDevice> mlswList = new ArrayList<PowerDevice>();

						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDR)){
								drswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(drswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDK)){
								dkswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(dkswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
								xlswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(xlswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getPowerDeviceName().contains("接地变")
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)){
								jdbswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(jdbswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getPowerDeviceName().contains("站用变")
									||switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchZYB)
									){
								zybswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(zybswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
								zbswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(zbswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
								mlswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(mlswList);
						
						swList.clear();
						
						if(!end.equals("")&&!begin.equals("")){
							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								if(begin.equals("0")){
									for(PowerDevice mlsw : mlswList){
										String devid = mlsw.getPowerDeviceID();
										cznr = "遥控断开"+czname+CZPService.getService().getDevName(mlsw);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
									}
								}
								
								if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
									swList.addAll(drswList);
									swList.addAll(dkswList);
									swList.addAll(xlswList);
									swList.addAll(jdbswList);
									swList.addAll(zybswList);
									swList.addAll(zbswList);
									swList.addAll(mlswList);

									getSwitchSequenceTd(swList,data,uuids,czname,ddname);
									
									getSwitchSeparateSequenceTd(qtdzList,data,uuids,czname,ddname);
									
									for(PowerDevice xldz : xldzList){
										String devid = xldz.getPowerDeviceID();

										if(RuleExeUtil.isDeviceChanged(xldz)){
											cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(xldz);
											data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
											
											String  qzcznr  =  "确认"+CZPService.getService().getDevName(xldz)+"处分闸位置";
											data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
										}
									}
								}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
									swList.addAll(mlswList);
									
									getSwitchSequenceTd(swList,data,uuids,czname, ddname);
								}
							}else{//复电
								if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
									if(dev.getPowerVoltGrade() == 10){
										swList.addAll(zbswList);
									}else{
										swList.addAll(mlswList);
										swList.addAll(zbswList);
										swList.addAll(zybswList);
										swList.addAll(jdbswList);
										swList.addAll(xlswList);
										swList.addAll(dkswList);
										swList.addAll(drswList);
									}
									
									for(PowerDevice qtdz : qtdzList){
										String devid = qtdz.getPowerDeviceID();

										if(qtdz.getPowerDeviceName().contains("站用变")){
											if(RuleExeUtil.isDeviceChanged(qtdz)){
												cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(qtdz);
												data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
												
												String  qzcznr  =  "确认"+CZPService.getService().getDevName(qtdz)+"处合闸位置";
												data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
											}
										}
									}
									
									getSwitchSeparateSequenceFd(qtdzList,data,uuids,czname,ddname);
								}else if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
									swList.addAll(mlswList);
									
									getSwitchSequenceFd(swList,data,uuids,czname,ddname);
								}
								
								if(end.equals("0")){
									for(PowerDevice mlsw : mlswList){
										String devid = mlsw.getPowerDeviceID();
										cznr = "遥控合上"+czname+CZPService.getService().getDevName(mlsw);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									}
								}
							}
						}else{
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
						}
					}else if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
						List<PowerDevice> gyczbkgList = RuleExeUtil.getTransformerSwitchHigh(dev);
						List<PowerDevice> zyczbkgList = RuleExeUtil.getTransformerSwitchMiddle(dev);
						List<PowerDevice> dyczbkgList = RuleExeUtil.getTransformerSwitchLow(dev);

						if(RuleUtil.isTransformerNQ(dev)
								||RuleUtil.isTransformerKDNQ(dev)){
							gyczbkgList.clear();
						}
						
						if(operation.equals("中性点")){
							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
								
								RuleExeUtil.swapLowDeviceList(gdList);
								
								for(PowerDevice gd : gdList) {
									if(cznr.contains("220kV侧")){
										if(gd.getPowerVoltGrade() == 220){
											String devid = gd.getPowerDeviceID();
											String temp  =  "遥控拉开"+czname+CZPService.getService().getDevName(gd);
											data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
											
											temp  =  "确认"+CZPService.getService().getDevName(gd)+"处拉开位置";
											data.add(new String[]{"","",czname,czname,temp,isyk,devid,devname,startZT,"",deviceType,uuids});
										}
									}else if(cznr.contains("110kV侧")){
										if(gd.getPowerVoltGrade() == 110){
											String devid = gd.getPowerDeviceID();
											String temp  =  "遥控拉开"+czname+CZPService.getService().getDevName(gd);
											data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
											
											temp  =  "确认"+CZPService.getService().getDevName(gd)+"处拉开位置";
											data.add(new String[]{"","",czname,czname,temp,isyk,devid,devname,startZT,"",deviceType,uuids});
										}
									}
								}
							}else{
								List<PowerDevice> gdList = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
								
								RuleExeUtil.swapLowDeviceList(gdList);
								
								for(PowerDevice gd : gdList) {
									if(cznr.contains("220kV侧")){
										if(gd.getPowerVoltGrade() == 220){
											String devid = gd.getPowerDeviceID();
											String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(gd);
											data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
											
											temp  =  "确认"+CZPService.getService().getDevName(gd)+"处合上位置";
											data.add(new String[]{"","",czname,czname,temp,isyk,devid,devname,startZT,"",deviceType,uuids});
										}
									}else if(cznr.contains("110kV侧")){
										if(gd.getPowerVoltGrade() == 110){
											String devid = gd.getPowerDeviceID();
											String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(gd);
											data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
											
											temp  =  "确认"+CZPService.getService().getDevName(gd)+"处合上位置";
											data.add(new String[]{"","",czname,czname,temp,isyk,devid,devname,startZT,"",deviceType,uuids});
										}
									}
								}
							}
						}else if(!begin.equals("")&&!end.equals("")){
							if(Integer.valueOf(begin)<Integer.valueOf(end)){//停电
								getSwitchSequenceTd(dyczbkgList, data, uuids, czname, ddname);
								
								getSwitchSequenceTd(zyczbkgList, data, uuids, czname, ddname);

								if(gyczbkgList.size() == 2){
									List<PowerDevice> gyczbkgNewList = new ArrayList<PowerDevice>();
									
									for(PowerDevice gyczbkg : gyczbkgList){
										if(RuleExeUtil.isSwMiddleInThreeSecond(gyczbkg)){
											gyczbkgNewList.add(gyczbkg);
										}
									}
									
									for(PowerDevice gyczbkg : gyczbkgList){
										if(!RuleExeUtil.isSwMiddleInThreeSecond(gyczbkg)){
											gyczbkgNewList.add(gyczbkg);
										}
									}
									
									getSwitchSequenceTd(gyczbkgNewList, data, uuids, czname, ddname);
								}else{
									getSwitchSequenceTd(gyczbkgList, data, uuids, czname, ddname);
								}
							}else{//复电
								if(gyczbkgList.size() == 2){
									List<PowerDevice> gyczbkgNewList = new ArrayList<PowerDevice>();
									
									for(PowerDevice gyczbkg : gyczbkgList){
										if(!RuleExeUtil.isSwMiddleInThreeSecond(gyczbkg)){
											gyczbkgNewList.add(gyczbkg);
										}
									}
									
									for(PowerDevice gyczbkg : gyczbkgList){
										if(RuleExeUtil.isSwMiddleInThreeSecond(gyczbkg)){
											gyczbkgNewList.add(gyczbkg);
										}
									}
									
									getSwitchSequenceFd(gyczbkgNewList, data, uuids, czname, ddname);
								}else{
									getSwitchSequenceFd(gyczbkgList, data, uuids, czname, ddname);
								}
								
								getSwitchSequenceFd(zyczbkgList, data, uuids, czname, ddname);
								
								getSwitchSequenceFd(dyczbkgList, data, uuids, czname, ddname);
							}
						}else{
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
						}
					}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
						if((begin.equals("1")&&end.equals("2"))){
							List<PowerDevice> swList = new ArrayList<PowerDevice>();
							
							swList.add(dev);
							
							getSwitchSequenceTd(swList , data, uuids, czname ,ddname);
						}else if(begin.equals("2")&&end.equals("1")){
							List<PowerDevice> swList = new ArrayList<PowerDevice>();
							
							swList.add(dev);
							
							getSwitchSequenceFd(swList , data, uuids, czname ,ddname);
						}else if(begin.equals("1")&&end.equals("0")){
							String devid = dev.getPowerDeviceID();
							
							if(cznr.contains("遥控")){
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}
						}else if(begin.equals("0")&&end.equals("1")){
							String devid = dev.getPowerDeviceID();

							if(cznr.contains("遥控")){
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
							}
						}else if(cznr.contains("热备用倒至")){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);

							dzList = RuleExeUtil.sortByXLC(dzList);
							
							for(PowerDevice dz : dzList){
								String devid = dz.getPowerDeviceID();
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
									cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
								}
							}
							String devid = dev.getPowerDeviceID();

							String qzcznr  = "确认"+CZPService.getService().getDevName(dev)+"处冷备用";
							data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});

							dzList = RuleExeUtil.sortByMXC(dzList);
							
							for(PowerDevice dz : dzList){
								devid = dz.getPowerDeviceID();

								if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
									cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,"合上",endZT,deviceType,uuids});
								}
							}
							

							for(PowerDevice dz : dzList){
								if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
									List<PowerDevice> mxlist =  RuleExeUtil.getDeviceDirectList(dz, SystemConstants.MotherLine);
									
									if(mxlist.size()>0){
										devid = dev.getPowerDeviceID();

										qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处热备用于"+CZPService.getService().getDevName(mxlist);
										data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
									}
								}
							}
						}else if(cznr.contains("运行倒至")){
							for(RuleBaseMode rm : rbmList){
								List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(rm.getPd(), SystemConstants.SwitchSeparate);

								for(PowerDevice dz : dzList){
									if(RuleExeUtil.getDeviceEndStatus(dz).equals("0")){
										String devid = dz.getPowerDeviceID();
										
										cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
										
										String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处合闸位置";
										data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
									}
								}
								
								for(PowerDevice dz : dzList){
									String devid = dz.getPowerDeviceID();

									if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
										cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
										data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
										
										String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处分闸位置";
										data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
									}
								}
							}
						}else{
							data.add(new String[]{"","",czname,czname,cznr,isyk,"",devname,startZT,endZT,deviceType,uuids});
						}
					}else if(dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
						if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)){
							String devid = dev.getPowerDeviceID();

							if(cznr.contains("落实")){
								String  qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处合闸位置";
								data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else if(begin.equals("1")&&end.equals("0")){
								cznr =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
								String  qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处合闸位置";
								data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								
								String  qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处分闸位置";
								data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
							}else{
								if(cznr.contains("主变中性点")&&cznr.contains("确认")){
									String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
									String qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处合闸位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,"",deviceType,uuids});
								}else if(cznr.contains("主变中性点")&&cznr.contains("落实")){
									String temp  =  "遥控合上"+czname+CZPService.getService().getDevName(dev);
									data.add(new String[]{"","",ddname,czname,temp,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								
									String qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处合闸位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,"",deviceType,uuids});
								}else{
									data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
								}
							}
						}else{
							if(begin.equals("1")&&end.equals("0")){
								String devid = dev.getPowerDeviceID();

								cznr =  "合上"+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								String devid = dev.getPowerDeviceID();

								cznr =  "拉开"+CZPService.getService().getDevName(dev);
								data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}
					}else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						String devid = dev.getPowerDeviceID();
						if(dev.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
							if(begin.equals("1")&&end.equals("0")){
								cznr  =  "遥控将"+czname+CZPService.getService().getDevName(dev)+"由冷备用转热备用";
								data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","热备用",deviceType,uuids});
								
								String  qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处热备用";
								
								String qzdevid = dev.getPowerDeviceID();
								
								data.add(new String[]{"","",czname,czname,qzcznr,"",qzdevid,"","","","",uuids});
							}else if(begin.equals("0")&&end.equals("1")){
								cznr  =  "遥控将"+czname+CZPService.getService().getDevName(dev)+"由热备用转冷备用";
								data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","冷备用",deviceType,uuids});
								
								String  qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处冷备用";
								
								String qzdevid = dev.getPowerDeviceID();
								
								data.add(new String[]{"","",czname,czname,qzcznr,"",qzdevid,"","","","",uuids});
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}else{
							if(begin.equals("1")&&end.equals("0")){
								if(cznr.contains("遥控")){
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									
									String  qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处合上位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
								}else{
									data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								}
							}else if(begin.equals("0")&&end.equals("1")){
								if(cznr.contains("遥控")){
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
									
									String  qzcznr  =  "确认"+CZPService.getService().getDevName(dev)+"处拉开位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
								}else{
									data.add(new String[]{"","",czname,czname,cznr,isyk,devid,devname,startZT,"拉开",deviceType,uuids});
								}
							}else{
								data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
							}
						}
					}else if(dev.getDeviceType().equals(SystemConstants.ElecCapacity)){
						List<PowerDevice> drkgList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDR, "", false, true, true, true);

						if(begin.equals("0")){
							for(PowerDevice drkg : drkgList){
								String devid = drkg.getPowerDeviceID();
								cznr  =  "遥控断开"+czname+CZPService.getService().getDevName(drkg);
								data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
							}
							
							if(end.equals("2")){
								getSwitchSequenceTd(drkgList, data, uuids, czname, ddname);
							}
						}else if(begin.equals("1")){
							if(end.equals("2")){
								getSwitchSequenceTd(drkgList, data, uuids, czname, ddname);
							}else if(end.equals("0")){
								for(PowerDevice drkg : drkgList){
									String devid = drkg.getPowerDeviceID();
									cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(drkg);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								}
							}
						}else if(begin.equals("2")){
							getSwitchSequenceFd(drkgList, data, uuids, czname, ddname);
							
							if(end.equals("0")){
								for(PowerDevice drkg : drkgList){
									String devid = drkg.getPowerDeviceID();
									cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(drkg);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
								}
							}
						}
					}else if(cznr.contains("中性点")){
						if(cznr.contains("核实")&&cznr.contains("主变")&&cznr.contains("侧")){
							String devName = cznr.substring(0, cznr.indexOf("侧")+1).replace("核实", "确认");
							String devNum = CZPService.getService().getDevNum(devName.substring(0, devName.indexOf("主变")+2)).replace("#", "");
							
							if(devName.contains("220kV侧")){
								cznr = devName+"20"+devNum+"0中性点接地开关处合上位置";
							}else{
								cznr = devName+"10"+devNum+"0中性点接地开关处合上位置";
							}
							
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
						}else{
							data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
						}
					}else if(cznr.contains("所有断路器倒至")){
						List<PowerDevice> swList = new ArrayList<PowerDevice>();
						
						PowerDevice	device = CBSystemConstants.getCurRBM().getPd();
						
						if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.PowerTransformer)){
							swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSwitchFHC,false, true, true, true);
						}else if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.MotherLine)){
							swList = RuleExeUtil.getDeviceList(device, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						}
						
						List<PowerDevice> zbswList = new ArrayList<PowerDevice>();
						List<PowerDevice> xlswList = new ArrayList<PowerDevice>();

						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
								zbswList.add(switchs);
							}
						}
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
								zbswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(zbswList);
						
						for(PowerDevice switchs : swList){
							if(switchs.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)&&RuleExeUtil.getDeviceBeginStatusContainNotOperate(switchs).equals("0")){
								xlswList.add(switchs);
							}
						}
						
						RuleExeUtil.swapDeviceList(xlswList);
						
						List<PowerDevice> yxswList = new ArrayList<PowerDevice>();
						
						yxswList.addAll(xlswList);
						yxswList.addAll(zbswList);
						
						for(PowerDevice sw : yxswList){
							List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

							for(PowerDevice dz : dzList){
								String devid = dz.getPowerDeviceID();
								
								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("1")){
									cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"合上",deviceType,uuids});
									
									String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处合闸位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
								}
							}
							
							for(PowerDevice dz : dzList){
								String devid = dz.getPowerDeviceID();

								if(RuleExeUtil.getDeviceBeginStatus(dz).equals("0")){
									cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
									data.add(new String[]{"","",ddname,czname,cznr,isyk,devid,devname,startZT,"断开",deviceType,uuids});
									
									String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处分闸位置";
									data.add(new String[]{"","",czname,czname,qzcznr,isyk,devid,devname,startZT,endZT,deviceType,uuids});
								}
							}
						}
					}else if(cznr.contains("站用变")){
						String zybName  =  cznr.substring(1, cznr.indexOf("站用变")+3);
						
						if(!zybName.equals("")){
							String zbysql = "SELECT ZYB_DZNAME,ZYB_DEVID FROM "+CBSystemConstants.opcardUser+"T_A_LINEZYB WHERE ZYB_NAME = '"+zybName+"' AND STATION_NAME = '"+stationName+"'";
							List<Map<String,String>> zybNameList = DBManager.queryForList(zbysql);
							
							for(Map<String,String> zybNameMap : zybNameList){
								String zbyName = StringUtils.ObjToString(zybNameMap.get("ZYB_DZNAME"));
								String zybDevid = StringUtils.ObjToString(zybNameMap.get("ZYB_DEVID"));

								cznr  =  "遥控拉开"+czname+zbyName;
								data.add(new String[]{"","",ddname,czname,cznr,"",zybDevid,"","","断开","",uuids});
								
								String  qzcznr  =  "确认"+zbyName+"处分闸位置";
								data.add(new String[]{"","",czname,czname,qzcznr,"",zybDevid,"","","","",uuids});
							}
						}
					}else{
						data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
					}
				}
			}else{
				data.add(new String[]{"","",czname,czname,cznr,"0","",devname,startZT,endZT,deviceType,uuids});
			}
		}
		
		return data;
	}
	
	public static String getVoltInDevName(String powerDeviceName) {
    	String volt = "";
    	String equipName = powerDeviceName;
    	int pos = equipName.toUpperCase().indexOf("KV");
		if (pos >= 0) {
			volt = "";
			for(int i = pos-1; i >=0; i--) {
				char ch = equipName.charAt(i);
				if (ch >= '0' && ch <= '9')
					volt = ch + volt;
				else
					break;
			}
        }
		else
			volt = "";
    	return volt;
    }
	
	public static  String getStationNameByCznr(String cznr) {
		String stationName = "";
		if(cznr.lastIndexOf("站") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("站")+1);
		else if(cznr.lastIndexOf("变") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("变")+1);
		else if(cznr.lastIndexOf("厂") >= 0)
			stationName = cznr.substring(0, cznr.lastIndexOf("厂")+1);
		
		if(stationName.indexOf("千伏") >= 0)
			stationName = stationName.substring(stationName.lastIndexOf("千伏")+2);
		else if(stationName.toLowerCase().indexOf("kv") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().lastIndexOf("kv")+2);
		else if(stationName.toLowerCase().indexOf("切换至") >= 0 || stationName.toLowerCase().indexOf("切至") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("至")+1);
		else if(stationName.toLowerCase().indexOf("切换到") >= 0 || stationName.toLowerCase().indexOf("切到") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("到")+1);
		else if(stationName.toLowerCase().indexOf("断开") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("断开")+2);
		else if(stationName.toLowerCase().indexOf("合上") >= 0)
			stationName = stationName.substring(stationName.toLowerCase().indexOf("合上")+2);
		return stationName;
	}
	
	public static void getSwitchSequenceTd(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);

			/*
			 * 35kV巴爪二级电站特殊判断
			 */
			boolean isxcdz = false;
			boolean iszcdz = false;
			
			for(PowerDevice dz : dzList){
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					isxcdz = true;
				}else{
					iszcdz = true;
				}
			}
			
			if(isxcdz && iszcdz){
				for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
					PowerDevice dz = itor.next();
					
					if(!dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						itor.remove();
					}
				}
			}
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
			}else{
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				}else{
					dzList = RuleExeUtil.sortByMXC(dzList);
					Collections.reverse(dzList);
				}
			}
			
			for(PowerDevice dz : dzList){
				if(RuleExeUtil.isDeviceChanged(dz)&&!dz.getPowerDeviceName().contains("PT")){
					String devid =  dz.getPowerDeviceID();
					String devname = CZPService.getService().getDevName(dz);
					String deviceType = dz.getDeviceType();
					
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String cznr  =  "将"+czname+CZPService.getService().getDevName(sw)+"由热备用转冷备用";
						
						data.add(new String[]{"","",czname,czname,cznr,"",devid,devname,"","冷备用",deviceType,uuids});
						
						String  qzcznr  =  "确认"+CZPService.getService().getDevName(sw)+"处冷备用";
						
						String qzdevid = dz.getPowerDeviceID();
						
						data.add(new String[]{"","",czname,czname,qzcznr,"",qzdevid,"","","","",uuids});
					}else{
						String cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});

						String qzcznr = "确认"+devname+"处拉开位置";
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
					}
				}
			}
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
				for(PowerDevice dz : dzList){
					List<PowerDevice> threedzList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);

					for(PowerDevice threedz : threedzList){
						if(RuleExeUtil.getDeviceEndStatus(threedz).equals("1")){
							String devid = threedz.getPowerDeviceID();
							String devname = threedz.getPowerDeviceName();
							String deviceType = threedz.getDeviceType();
							
							String cznr =  "遥控拉开"+czname+CZPService.getService().getDevName(threedz);
							data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开",deviceType,uuids});

							String qzcznr = "确认"+devname+"处拉开位置";
							data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
						}
					}
				}
			}
		}
	}
	
	public static void getSwitchSequenceFd(List<PowerDevice> swList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice sw : swList){
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			
			/*
			 * 35kV巴爪二级电站特殊判断
			 */
			boolean isxcdz = false;
			boolean iszcdz = false;
			
			for(PowerDevice dz : dzList){
				if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					isxcdz = true;
				}else{
					iszcdz = true;
				}
			}
			
			if(isxcdz && iszcdz){
				for(Iterator<PowerDevice> itor = dzList.iterator();itor.hasNext();){
					PowerDevice dz = itor.next();
					
					if(!dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						itor.remove();
					}
				}
			}
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				dzList = RuleExeUtil.sortByCZMXC(dzList);
				Collections.reverse(dzList);
			}else{
				dzList = RuleExeUtil.sortDzListByMXDZ(dzList);
				Collections.reverse(dzList);
			}
			
			for(PowerDevice dz : dzList){
				String devid = dz.getPowerDeviceID();

				if(RuleExeUtil.isDeviceChanged(dz)){
					devid =  dz.getPowerDeviceID();
					String devname = CZPService.getService().getDevName(dz);
					String deviceType = dz.getDeviceType();
					
					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String cznr  =  "将"+czname+CZPService.getService().getDevName(sw)+"由冷备用转热备用";
						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","热备用",deviceType,uuids});
						
						String  qzcznr  =  "确认"+CZPService.getService().getDevName(sw)+"处热备用";
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
					}else{
						String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});

						String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处合上位置";
						
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
					}
				}
			}
			
			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
				for(PowerDevice dz : dzList){
					List<PowerDevice> threedzList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.SwitchSeparate);

					for(PowerDevice threedz : threedzList){
						if(RuleExeUtil.getDeviceEndStatus(threedz).equals("0")){
							String devid = threedz.getPowerDeviceID();
							String devname = threedz.getPowerDeviceName();
							String deviceType = threedz.getDeviceType();

							String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(threedz);
							data.add(new String[]{"","",ddname,czname,cznr,devname,devid,"","","合上",deviceType,uuids});

							String  qzcznr  =  "确认"+CZPService.getService().getDevName(threedz)+"处合上位置";
							data.add(new String[]{"","",czname,czname,qzcznr,"",devid,"","","","",uuids});
						}
					}
				}
			}
		}
	}
	
	
	/*
	 * 传入刀闸获取序列
	 */
	public static void getSwitchSeparateSequenceTd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dz : dzList){
			if(dz.getPowerDeviceName().contains("站用变")){
				if(RuleExeUtil.isDeviceChanged(dz)){
					String devid =  dz.getPowerDeviceID();
					String devname = dz.getPowerDeviceName();

					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String dzName = CZPService.getService().getDevName(dz);
						String zybname = dzName.substring(0, dzName.indexOf("站用变")+3);
						
						String cznr  =  "将"+czname+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车由工作位置摇至试验位置";
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
						
						String  qzcznr  =  "确认"+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车在试验位置";
						
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
					}else{
						String cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
						
						String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处分闸位置";
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
					}
				}
			}else{
				String devid = dz.getPowerDeviceID();
				String devname = dz.getPowerDeviceName();

				if(RuleExeUtil.isDeviceChanged(dz)){
					String cznr  =  "遥控拉开"+czname+CZPService.getService().getDevName(dz);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","拉开","",uuids});
					
					String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处分闸位置";
					data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
				}
			}
		}
	}
	
	/*
	 * 传入刀闸获取序列
	 */
	public static void getSwitchSeparateSequenceFd(List<PowerDevice> dzList,ArrayList<String[]> data,String uuids,String czname,String ddname){
		for(PowerDevice dz : dzList){
			if(dz.getPowerDeviceName().contains("站用变")){
				if(RuleExeUtil.isDeviceChanged(dz)){
					String devid =  dz.getPowerDeviceID();
					String devname = dz.getPowerDeviceName();

					if(dz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						String dzName = CZPService.getService().getDevName(dz);
						String zybname = dzName.substring(0, dzName.indexOf("站用变")+3);
						
						String cznr  =  "将"+czname+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车由试验位置摇至工作位置";
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
						
						String  qzcznr  =  "确认"+zybname+CZPService.getService().getDevNum(dz)+"隔离开关手车在工作位置";
						
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
					}else{
						String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
						data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
						
						String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处合闸位置";
						data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
					}
				}
			}else{
				String devid = dz.getPowerDeviceID();
				String devname = dz.getPowerDeviceName();

				if(RuleExeUtil.isDeviceChanged(dz)){
					String cznr  =  "遥控合上"+czname+CZPService.getService().getDevName(dz);
					data.add(new String[]{"","",ddname,czname,cznr,"",devid,devname,"","合上","",uuids});
					
					String  qzcznr  =  "确认"+CZPService.getService().getDevName(dz)+"处合闸位置";
					data.add(new String[]{"","",czname,czname,qzcznr,"",devid,devname,"","","",uuids});
				}
			}
		}
	}
}
