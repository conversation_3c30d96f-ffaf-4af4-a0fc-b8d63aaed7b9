package com.tellhow.czp.app.yndd.view;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Toolkit;
import java.sql.Timestamp;
import java.util.Iterator;
import java.util.UUID;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JTextField;

import com.tellhow.czp.mainframe.JAutoCompleteComboBox;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;



public class OrganLineRelEditDialog extends javax.swing.JDialog{
	private String id = "";
	private String lineId = "";
	private String lineName = "";
	private String lineOrganName = "";
	private String kind = "";

	private long time = System.currentTimeMillis();
	Timestamp now = new Timestamp(time);
	
	private JTextField textField = new JTextField();
	private JAutoCompleteComboBox lineComboBox = new JAutoCompleteComboBox();

	public OrganLineRelEditDialog(JFrame parent, boolean modal,String id,String organId,String lineName,String lineOrganName,String kind) {
		super(parent, modal);
		this.id = id;
		this.kind = kind;
		this.lineId = organId;
		this.lineName = lineName;
		this.lineOrganName = lineOrganName;
		initComponents();
		this.setTitle("线路运维站关系修改");
		setLocationCenter();
	}

	/**
	 * @屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	private void initComponents() {
		getContentPane().setLayout(null);
		
		JLabel label_2 = new JLabel("\u7EBF\u8DEF\u540D\u79F0");
		label_2.setBounds(14, 13, 113, 18);
		getContentPane().add(label_2);
		
		JLabel lblNewLabel = new JLabel("\u6240\u5C5E\u8FD0\u7EF4\u7AD9\u540D\u79F0");
		lblNewLabel.setBounds(14, 81, 136, 18);
		getContentPane().add(lblNewLabel);
		
		
		JButton button = new JButton("\u786E\u5B9A");
		button.setBounds(37, 199, 57, 23);
		getContentPane().add(button);
		
		JButton button_1 = new JButton("\u53D6\u6D88");
		button_1.setBounds(127, 199, 57, 23);
		getContentPane().add(button_1);
		this.setSize(260, 310);

		button.setToolTipText("\u786e\u5b9a");
		button.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ok.png")));
		button.setFont(new Font("宋体", Font.PLAIN, 14));
		button_1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/back.png")));
		button_1.setToolTipText("\u53d6\u6d88");
		button_1.setFont(new Font("宋体", Font.PLAIN, 14));

		button.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button.setFocusPainted(false);
		button.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				saveButtonActionPerformed(evt);
			}
		});

		button_1.setMargin(new java.awt.Insets(1, 1, 1, 1));
		button_1.setFocusPainted(false);
		
		DefaultComboBoxModel model = new DefaultComboBoxModel();

		for(Iterator<PowerDevice>  itor = CBSystemConstants.getMapPowerFeeder().values().iterator();itor.hasNext();){
			PowerDevice line = itor.next();
			
			CodeNameModel cnm = new CodeNameModel(line.getPowerDeviceID(), line.getPowerDeviceName());
			
			model.addElement(cnm);
		}
		
		lineComboBox.setModel(model);
		
		lineComboBox.setBounds(14, 44, 206, 26);
		lineComboBox.setSelectedIndex(-1);
		lineComboBox.setEditable(true);
		getContentPane().add(lineComboBox);
		
		textField = new JTextField();
		textField.setBounds(14, 112, 206, 24);
		getContentPane().add(textField);
		textField.setColumns(10);
		
		button_1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				cancelButtonActionPerformed(evt);
			}
		});
		
		getSelectDfsInfo();
	}
	
	//获取修改界面信息WW
	public void getSelectDfsInfo() {
		CodeNameModel cnm = new CodeNameModel(this.lineId, this.lineName);
		lineComboBox.setSelectedItem(cnm);
		textField.setText(this.lineOrganName);
	}
	
	//确定
	private void saveButtonActionPerformed(java.awt.event.ActionEvent evt) {
		String id = String.valueOf(UUID.randomUUID());
		String stationName = textField.getText();
		
		CodeNameModel stationcnm = (CodeNameModel) lineComboBox.getSelectedItem();

		String lineId = stationcnm.getCode();
		String lineName = stationcnm.getName();
		
		if(kind.equals("新增")){
			String sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_LINEYWSTATION "
					+ "(ID, LINEID, LINENAME, STATIONNAME) VALUES ('"+id+"','"+lineId+"','"+lineName+"','"+stationName+"')";
			DBManager.execute(sql);
		}else{
			String sql = "UPDATE "+CBSystemConstants.opcardUser+"T_A_LINEYWSTATION "
					+ "SET LINEID = '"+lineId+"', LINENAME = '"+lineName+"', STATIONNAME = '"+stationName+"' WHERE ID = '"+this.id+"'";
			DBManager.execute(sql);
		}
		
		this.setVisible(false);
		this.dispose();
	}
	
	public void paint(Graphics g) {
		super.paint(g);
		Graphics2D g_2d = (Graphics2D) g;
		g_2d.setColor(Color.GRAY);
		g_2d.drawLine(20, 40, this.getSize().width - 20, 40);
	}
	//取消
	private void cancelButtonActionPerformed(java.awt.event.ActionEvent evt) {
		this.setVisible(false);
		this.dispose();
	}
}