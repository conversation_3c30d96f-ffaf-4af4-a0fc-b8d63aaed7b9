package com.tellhow.czp.app.yndd.wordcard.km;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrLSMLKGYHS implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev, PowerDevice stationDev, String desc) {
		String replaceStr = "";

		if("落实母联开关已合上".equals(tempStr)) {
			PowerDevice station = CBSystemConstants.getPowerStation(curDev.getPowerStationID());
			
			List<PowerDevice> mlkgList =  RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, false, true);
		 	PowerDevice mlkg = new PowerDevice();
		 	if(mlkgList.size()>0){
		 		mlkg = mlkgList.get(0);
		 	}
		 	
		 	PowerDevice gycmlkg = new PowerDevice();
			HashMap<String, PowerDevice> mapStationDevice = CBSystemConstants.getStationPowerDevices(curDev.getPowerStationID());
				
				for (Iterator<PowerDevice> it = mapStationDevice.values().iterator(); it.hasNext();) {
				PowerDevice dev = it.next();
				
				if(dev.getPowerVoltGrade() == station.getPowerVoltGrade()){
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
						gycmlkg = dev;
						break;
					}
				}
			}
				
		 	
		 	if(RuleExeUtil.getDeviceBeginStatus(mlkg).equals("1")){
		 		if(curDev.getPowerVoltGrade() == 220){
			 		replaceStr = "云南省调@落实"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(mlkg)+"具备运行条件/r/n";
			 		replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(gycmlkg)+"/r/n";

		 		}else if(curDev.getPowerVoltGrade() == 110){
		 			
		 			if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("1")){
				 		replaceStr += "云南省调@落实"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(gycmlkg)+"具备运行条件/r/n";
				 		replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(gycmlkg)+"/r/n";
		 			}
		 			
			 		replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(mlkg)+"/r/n";
		 		}else if(curDev.getPowerVoltGrade() == 35){
		 			if(RuleExeUtil.getDeviceBeginStatus(gycmlkg).equals("1")){
				 		replaceStr += "云南省调@落实"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(gycmlkg)+"具备运行条件/r/n";
				 		replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(gycmlkg)+"/r/n";
		 			}
			 		replaceStr += "昆明地调@遥控合上"+CZPService.getService().getDevName(station)+CZPService.getService().getDevName(mlkg)+"/r/n";
		 		}
		 	}
		 	if(replaceStr.equals("")){
		 		replaceStr = null;
		 	}
		}
		return replaceStr;
	}

}
