package com.tellhow.czp.operationcard.dao;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.PowerSystemDBOperator;

public class DeviceStatusManager {
	
	/**
	 * 插入设备元件状态变更，方便以后调用静态安全分析
	 * @param conn 数据库连接
	 * @param cardid 票号
	 * @throws SQLException
	 */
	public void  insertDevState(Connection conn,String cardid) throws SQLException{
		if(conn==null)
			return;
		Statement state=conn.createStatement();
		Map<Integer, DispatchTransDevice> mapdtd=new HashMap<Integer, DispatchTransDevice>();
		DispatchTransDevice dtd=null;
		PowerDevice dev=null;
		String equipid="";
	    String sql="";
	    
        for (int j = 1; j < DeviceOperate.getAlltransDevMap().size() + 1; j++) {    //遍历设备状态
        	dtd = (DispatchTransDevice) DeviceOperate.getAlltransDevMap().get(j);
        	dev = (PowerDevice) dtd.getTransDevice();
        	
        	equipid=dev.getPowerDeviceID();
        	String beginStatus = dtd.getBeginstatus();
        	String endState = dtd.getEndstate();
        	sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE VALUES('"+java.util.UUID.randomUUID().toString()+"','"
		    	 +equipid+"','"+beginStatus+"','"+endState+"','"+cardid+"','"+dtd.getUuID()+"',sysdate,"+j+")";
		    state.execute(sql);
        }
        
        
        state.close();
	}
	
	public void insertDevStateByWord(String cardid) {
		DBManager.execute("delete from "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE where cardid='"+cardid+"'");
		List list = DBManager.queryForList("select mxid,czdw,cznr from "+CBSystemConstants.opcardUser+"t_a_czpmx where f_zbid='"+cardid+"' order by cardorder");
		int j = 1;
		for(int i = 0; i < list.size(); i++) {
			Map map = (Map)list.get(i);
			String mxid = map.get("mxid").toString();
			String czdw = map.get("czdw").toString();
			String cznr = map.get("cznr").toString();
			List<RuleBaseMode> rbmList = CZPService.getService().getRBMList(czdw, cznr);
			for(RuleBaseMode rbm : rbmList) {
				if(rbm.getPd() != null && !rbm.getBeginStatus().equals("") && !rbm.getEndState().equals("")){
					String sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE VALUES('"+java.util.UUID.randomUUID().toString()+"','"
					    	 +rbm.getPd().getPowerDeviceID()+"','"+rbm.getBeginStatus()+"','"+rbm.getEndState()+"','"+cardid+"','"+mxid+"',sysdate,"+j+")";
					DBManager.execute(sql);
					j++;
				}
			}
		}
		
	}
	

	//设备对位操作生成插入设备状态	
	public void insertDevStatu(Connection conn) throws SQLException{
		Statement state=conn.createStatement();
		
		DispatchTransDevice dtd=null;
		PowerDevice dev=null;
		Map<String,DispatchTransDevice> compStates=new HashMap<String,DispatchTransDevice>();
		Map<String,DispatchTransDevice> protectStates=new HashMap<String,DispatchTransDevice>(); //保护设备状态变更
		Map<String,DispatchTransDevice> removableStates=new HashMap<String,DispatchTransDevice>();
						
		//原版遍历设备状态
		for (int j = 1; j < DeviceOperate.getAlltransDevMap().size() + 1; j++) {    //遍历设备状态
        	dtd = DeviceOperate.getAlltransDevMap().get(j);
        	dev = dtd.getTransDevice();
        	if(SystemConstants.Switch.equals(dev.getDeviceType())&&dtd.getFlag().equals("1"))
        		continue;
        	if(CBSystemConstants.getProtect(dev.getPowerStationID(), dev.getPowerDeviceID()) != null) {
        		protectStates.put(dev.getPowerDeviceID(), dtd);
        	}else if(CBSystemConstants.getRMDeviceByDetailAndType(dev.getRmType(), dev.getPowerStationID(), dev.getKnife(), dev.getDevice())!=null){
        		removableStates.put(dev.getPowerDeviceID(), dtd);
        	}else if(compStates.get(dev.getPowerDeviceID())==null){
        		compStates.put(dev.getPowerDeviceID(), dtd);
        	}else{
        		DispatchTransDevice tempDtd=compStates.get(dev.getPowerDeviceID());
        		if(CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals(tempDtd.getBeginstatus()))
        			//如果同一个设备后一个DTD的目标状态和前一个DTD的源状态相同 说明这个设备的最终状态时不变的
        		   compStates.remove(dev.getPowerDeviceID());
        		else
        		   tempDtd.setEndstate(dtd.getEndstate());
        	}
        }
        
        String equipid="";
        String sql="";
        for (Iterator iter = compStates.keySet().iterator(); iter.hasNext();) {
    	    equipid=iter.next().toString();
		    dtd=compStates.get(equipid);
		    if(CBSystemConstants.cardstatus.equals("0")){
		    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET DEVICESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"',LOADELECSTATUS='"+dtd.getTransDevice().getIsLoseElec()+"' WHERE EQUIPID='"+equipid+"'";
		    }else if(CBSystemConstants.cardstatus.equals("1")){
		    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET DISPATCH='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
		    }else{
		    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET MONITORING='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
		    }
		    state.addBatch(sql);
		}
        for (Iterator iter = protectStates.keySet().iterator(); iter.hasNext();) {
    	    equipid=iter.next().toString();
		    dtd=protectStates.get(equipid);
		    sql="UPDATE "+CBSystemConstants.opcardUser+"t_a_protectequip SET PROTECTSTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE PROTECTID='"+equipid+"'";
		    state.addBatch(sql);
		}
        for (Iterator iter = removableStates.keySet().iterator(); iter.hasNext();) {
    	    equipid=iter.next().toString();
		    dtd=removableStates.get(equipid);
		    PowerDevice pd = dtd.getTransDevice();
		    int rmstatu = TicketDBManager.getRMStatus(pd.getDevice(),pd.getKnife(),pd.getRmType());
		    if(rmstatu==-1){
		    	PowerSystemDBOperator.addStationGroundLine(pd.getPowerDeviceID(), pd.getDevice(), pd.getKnife(), pd.getDeviceStatus(), pd.getRmType());
		    }else{
		    sql="UPDATE "+CBSystemConstants.opcardUser+"t_A_REMOVABLEDEVICE SET ROMVESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE REMOVEID='"+equipid+"'  and opcode='"+CBSystemConstants.opCode+"'";
		    state.addBatch(sql);}
		}
        state.executeBatch();
        state.close();
	}
	//设备对位操作生成插入设备状态	dubbo调用
	public void insertDevStatu() {

		DispatchTransDevice dtd=null;
		PowerDevice dev=null;
		Map<String,DispatchTransDevice> compStates=new HashMap<String,DispatchTransDevice>();
		Map<String,DispatchTransDevice> protectStates=new HashMap<String,DispatchTransDevice>(); //保护设备状态变更
		Map<String,DispatchTransDevice> removableStates=new HashMap<String,DispatchTransDevice>();
						
		//原版遍历设备状态
		for (int j = 1; j < DeviceOperate.getAlltransDevMap().size() + 1; j++) {    //遍历设备状态
        	dtd = DeviceOperate.getAlltransDevMap().get(j);
        	dev = dtd.getTransDevice();
        	if(SystemConstants.Switch.equals(dev.getDeviceType())&&dtd.getFlag().equals("1"))
        		continue;
        	if(CBSystemConstants.getProtect(dev.getPowerStationID(), dev.getPowerDeviceID()) != null) {
        		protectStates.put(dev.getPowerDeviceID(), dtd);
        	}else if(CBSystemConstants.getRMDeviceByDetailAndType(dev.getRmType(), dev.getPowerStationID(), dev.getKnife(), dev.getDevice())!=null){
        		removableStates.put(dev.getPowerDeviceID(), dtd);
        	}else if(compStates.get(dev.getPowerDeviceID())==null){
        		compStates.put(dev.getPowerDeviceID(), dtd);
        	}else{
        		DispatchTransDevice tempDtd=compStates.get(dev.getPowerDeviceID());
        		if(CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals(tempDtd.getBeginstatus()))
        			//如果同一个设备后一个DTD的目标状态和前一个DTD的源状态相同 说明这个设备的最终状态时不变的
        		   compStates.remove(dev.getPowerDeviceID());
        		else
        		   tempDtd.setEndstate(dtd.getEndstate());
        	}
        }
        
        String equipid="";
        String sql="";
        for (Iterator iter = compStates.keySet().iterator(); iter.hasNext();) {
    	    equipid=iter.next().toString();
		    dtd=compStates.get(equipid);
		    if(CBSystemConstants.cardstatus.equals("0")){
		    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET DEVICESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"',LOADELECSTATUS='"+dtd.getTransDevice().getIsLoseElec()+"' WHERE EQUIPID='"+equipid+"'";
		    }else if(CBSystemConstants.cardstatus.equals("1")){
		    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET DISPATCH='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
		    }else{
		    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET MONITORING='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
		    }
		    DBManager.execute(sql);
		}
        for (Iterator iter = protectStates.keySet().iterator(); iter.hasNext();) {
    	    equipid=iter.next().toString();
		    dtd=protectStates.get(equipid);
		    sql="UPDATE "+CBSystemConstants.opcardUser+"t_a_protectequip SET PROTECTSTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE PROTECTID='"+equipid+"'";
		    DBManager.execute(sql);
		}
        for (Iterator iter = removableStates.keySet().iterator(); iter.hasNext();) {
    	    equipid=iter.next().toString();
		    dtd=removableStates.get(equipid);
		    PowerDevice pd = dtd.getTransDevice();
		    int rmstatu = TicketDBManager.getRMStatus(pd.getDevice(),pd.getKnife(),pd.getRmType());
		    if(rmstatu==-1){
		    	PowerSystemDBOperator.addStationGroundLine(pd.getPowerDeviceID(), pd.getDevice(), pd.getKnife(), pd.getDeviceStatus(), pd.getRmType());
		    }else{
		    sql="UPDATE "+CBSystemConstants.opcardUser+"t_A_REMOVABLEDEVICE SET ROMVESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE REMOVEID='"+equipid+"'  and opcode='"+CBSystemConstants.opCode+"'";
		    DBManager.execute(sql);}
		}

	}
	/**
	 * 操作票生成后插入设备状态
	 */
	public void  insertDevStatus(Connection conn) throws SQLException{
				
		    Statement state=conn.createStatement();
				
			DispatchTransDevice dtd=null;
			PowerDevice dev=null;
			Map<String,DispatchTransDevice> compStates=new HashMap<String,DispatchTransDevice>();
			Map<String,DispatchTransDevice> protectStates=new HashMap<String,DispatchTransDevice>(); //保护设备状态变更
			Map<String,DispatchTransDevice> removableStates=new HashMap<String,DispatchTransDevice>();
			
			/*
			//新版遍历设备状态
			Map<Integer, DispatchTransDevice> mapdtd=new HashMap<Integer, DispatchTransDevice>();
			for(int m=0;m<DeviceOperate.getOnetoAlltransDevMap().size();m++){
				mapdtd=DeviceOperate.getOnetoAlltransDevMap().get(m);
				for(int n=1;n<mapdtd.size()+1;n++){
					dtd = mapdtd.get(n);
		        	dev = dtd.getTransDevice();
		        	if(SystemConstants.Switch.equals(dev.getDeviceType())&&dtd.getFlag().equals("1"))
		        		continue;
		        	if(CBSystemConstants.getProtect(dev.getPowerStationID(), dev.getPowerDeviceID()) != null) {
		        		protectStates.put(dev.getPowerDeviceID(), dtd);
		        	}else if(CBSystemConstants.getRMDeviceByDetailAndType(dev.getRmType(), dev.getPowerStationID(), dev.getKnife(), dev.getDevice())!=null){
		        		removableStates.put(dev.getPowerDeviceID(), dtd);
		        	}else if(compStates.get(dev.getPowerDeviceID())==null){
		        		compStates.put(dev.getPowerDeviceID(), dtd);
		        	}else{
		        		DispatchTransDevice tempDtd=compStates.get(dev.getPowerDeviceID());
		        		if(CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals(tempDtd.getBeginstatus()))
		        			//如果同一个设备后一个DTD的目标状态和前一个DTD的源状态相同 说明这个设备的最终状态时不变的
		        		   compStates.remove(dev.getPowerDeviceID());
		        		else
		        		   tempDtd.setEndstate(dtd.getEndstate());
		        	}
				}
			}
			*/
			
			//原版遍历设备状态
			for (int j = 1; j < DeviceOperate.getAlltransDevMap().size() + 1; j++) {    //遍历设备状态
	        	dtd = DeviceOperate.getAlltransDevMap().get(j);
	        	dev = dtd.getTransDevice();
	        	if(SystemConstants.Switch.equals(dev.getDeviceType())&&dtd.getFlag().equals("1"))
	        		continue;
	        	if(CBSystemConstants.getProtect(dev.getPowerStationID(), dev.getPowerDeviceID()) != null) {
	        		protectStates.put(dev.getPowerDeviceID(), dtd);
	        	}else if(CBSystemConstants.getRMDeviceByDetailAndType(dev.getRmType(), dev.getPowerStationID(), dev.getKnife(), dev.getDevice())!=null){
	        		removableStates.put(dev.getPowerDeviceID(), dtd);
	        	}else if(compStates.get(dev.getPowerDeviceID())==null){
	        		compStates.put(dev.getPowerDeviceID(), dtd);
	        	}else{
	        		DispatchTransDevice tempDtd=compStates.get(dev.getPowerDeviceID());
	        		if(CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals(tempDtd.getBeginstatus()))
	        			//如果同一个设备后一个DTD的目标状态和前一个DTD的源状态相同 说明这个设备的最终状态时不变的
	        		   compStates.remove(dev.getPowerDeviceID());
	        		else
	        		   tempDtd.setEndstate(dtd.getEndstate());
	        	}
	        }
	        
	        String equipid="";
	        String sql="";
	        for (Iterator iter = compStates.keySet().iterator(); iter.hasNext();) {
        	    equipid=iter.next().toString();
			    dtd=compStates.get(equipid);
			    if(CBSystemConstants.roleCode.equals("0")){
			    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET DEVICESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
			    }else if(CBSystemConstants.roleCode.equals("2")){
			    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET MONITORING='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
			    }else{
			    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET DEVICESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
			    }
			    state.addBatch(sql);
			}
	        for (Iterator iter = protectStates.keySet().iterator(); iter.hasNext();) {
        	    equipid=iter.next().toString();
			    dtd=protectStates.get(equipid);
			    sql="UPDATE "+CBSystemConstants.opcardUser+"t_a_protectequip SET PROTECTSTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE PROTECTID='"+equipid+"'";
			    state.addBatch(sql);
			}
	        for (Iterator iter = removableStates.keySet().iterator(); iter.hasNext();) {
	        	 equipid=iter.next().toString();
	 		    dtd=removableStates.get(equipid);
	 		    PowerDevice pd = dtd.getTransDevice();
	 		    int rmstatu = TicketDBManager.getRMStatus(pd.getDevice(),pd.getKnife(),pd.getRmType());
	 		    if(rmstatu==-1){
	 		    	PowerSystemDBOperator.addStationGroundLine(pd.getPowerDeviceID(), pd.getDevice(), pd.getKnife(), pd.getDeviceStatus(), pd.getRmType());
	 		    }else{
	 		    sql="UPDATE "+CBSystemConstants.opcardUser+"t_A_REMOVABLEDEVICE SET ROMVESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE REMOVEID='"+equipid+"'  and opcode='"+CBSystemConstants.opCode+"'";
	 		    state.addBatch(sql);}
			}
	        state.executeBatch();
	        state.close();
		        		        
//	    		if(SystemConstants.InOutLine.equals(pd.getDeviceType())){
//	    		//全网图设备状态
//		    		tempDev = SystemConstants.getMapPowerLine(QueryDeviceDao.getAllMapLine(pd.getCimID()));
//		    		sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_DEVICESTATUS VALUES("+CBSystemConstants.opcardUser+"t_a_SEQ_STATUSID.NEXTVAL,'"+tempDev.getPowerDeviceID()+"'," +status+",SYSDATE,0,'')";
//			  	    state2.execute(sql);
//	    		}
	}

	public void insertDevLoseElecStatus(PowerDevice pd){
		String sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET LOADELECSTATUS='"+pd.getIsLoseElec()+"' WHERE EQUIPID='"+pd.getPowerDeviceID()+"'";
		DBManager.execute(sql);
	}
	
	
//<<<<<<< DeviceStatusManager.java
	/*public List<RuleBaseMode> getActionStates(String zbid){
		List<RuleBaseMode> ruleCBs=new ArrayList<RuleBaseMode>();
		String sql = "select t.equipid,s.station_id,t.beginstatus,t.endstate from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t,"+CBSystemConstants.opcardUser+"t_e_equipinfo s where t.equipid=s.equip_id and t.cardid='"+zbid+"' and t.beginstatus in ('4','5') order by t.stateorder";
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		String equipID = "";
		String stationID="";
		String beginStatus = ""; //起始状态
		String devstate = ""; //操作动作
		String trantype=""; //变电站类型
		String deviceRunType=""; //设备运行类型
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			RuleBaseMode rbm=new RuleBaseMode();
			equipID = StringUtils.ObjToString(temp.get("equipid"));
			stationID = StringUtils.ObjToString(temp.get("station_id"));
			beginStatus = StringUtils.ObjToString(temp.get("beginstatus"));
			devstate = StringUtils.ObjToString(temp.get("endstate"));
			CreatePowerStationToplogy.loadFacData(stationID);
			PowerDevice pd = CBSystemConstants.getPowerDevice(stationID, equipID);
			rbm.setPd(pd);
			if(pd.getDeviceType().equals(SystemConstants.Switch)) {
				beginStatus = CBSystemConstants.getDeviceStateValue(beginStatus);
				devstate = CBSystemConstants.getDeviceStateValue(devstate);
			}
			rbm.setBeginStatus(beginStatus);
			rbm.setEndState(devstate);
			ruleCBs.add(rbm);
		}
		return ruleCBs;
	}*/
//=======
	
	public List<RuleBaseMode> getActionStates(String zbid){
		List<RuleBaseMode> ruleCBs=new ArrayList<RuleBaseMode>();
//		String sql = "select t.equipid,s.station_id,t.beginstatus,t.endstate from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t,"+CBSystemConstants.opcardUser+"t_e_equipinfo s where t.equipid=s.equip_id and t.cardid='"+zbid+"' and t.beginstatus in ('4','5') order by t.stateorder";
		//edit 2014.6.24
		List results = DBManager.query(OPEService.getService().DeviceStatusManager(zbid));
		Map temp = new HashMap();
		String equipID = "";
		String stationID="";
		String beginStatus = ""; //起始状态
		String devstate = ""; //操作动作
		String trantype=""; //变电站类型
		String deviceRunType=""; //设备运行类型
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			RuleBaseMode rbm=new RuleBaseMode();
			equipID = StringUtils.ObjToString(temp.get("equipid"));
			stationID = StringUtils.ObjToString(temp.get("station_id"));
			beginStatus = StringUtils.ObjToString(temp.get("beginstatus"));
			devstate = StringUtils.ObjToString(temp.get("endstate"));
			CreatePowerStationToplogy.loadFacData(stationID);
			PowerDevice pd = CBSystemConstants.getPowerDevice(stationID, equipID);
			rbm.setPd(pd);
			if(pd.getDeviceType().equals(SystemConstants.Switch)) {
				beginStatus = CBSystemConstants.getDeviceStateValue(beginStatus);
				devstate = CBSystemConstants.getDeviceStateValue(devstate);
			}
			rbm.setBeginStatus(beginStatus);
			rbm.setEndState(devstate);
			ruleCBs.add(rbm);
		}
		return ruleCBs;
	}
///>>>>>>> 1.10


}
