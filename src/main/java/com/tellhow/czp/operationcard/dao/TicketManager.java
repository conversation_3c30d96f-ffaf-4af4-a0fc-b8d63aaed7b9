package com.tellhow.czp.operationcard.dao;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.OperationInfo;
import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.securitycheck.view.CheckWord;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.PowerSystemDBOperator;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;

/**
 * 因dubbo服务要用spring版方法，把原来的部分jdbc方法做了修改，放到这个类
 */

public class TicketManager {
	private String username = ""+CBSystemConstants.opcardUser;

	public String getZxsjInNpz(){ //默认执行时间,对金华地调拟票中执行时间特殊处理 --shenzs20200518
        Calendar cal1 = Calendar.getInstance();
//        if(this.getAreaNo().equals("330700")){ 
//         cal1.add(Calendar.DATE,2);
//        }else{
         cal1.add(Calendar.DATE,3);
//        }
        //cal1.set(2000,1,29);
        SimpleDateFormat   sdf   =   new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
        String threeDaysAfter = sdf.format(cal1.getTime());
        return threeDaysAfter;
	}
	
	public String getCzpBh(String areaNo) throws UnsupportedEncodingException{
			  //票号规则：19XXXX，前面2位是年份，后面4位是全年的流水号
			  String bh = "";
			  SimpleDateFormat sdf = new SimpleDateFormat("yy");
			  //获取当前年份（19）
			  String date = sdf.format(new Date());
			  try {
			   // 根据地区id，当前年份加月份查询该地区票号的最大值
			   String bhString = getBHLinShi(areaNo, "N","N"+date + "%",date+"%");
			   // 如果查询最大票号为空则该票为此地区第一张票
			   if ("null".equals(bhString) || bhString == null) {
				   bh = date +"0001";
				   return bh;
			   }
			   // 截取查询出的最大编号的后几位并增加1
			   int endBh = Integer.parseInt(bhString) + 1;
			   if (endBh < 1000) {
				    //得到bh之间应该有几个0
				    int count = 6-String.valueOf(endBh).length()-date.length();
				    StringBuffer sb = new StringBuffer();
				    for(int i=0;i<count;i++){
				     sb = sb.append("0");
				    }
				    bh = date + sb +String.valueOf(endBh);
			   } else {
				   bh = date + String.valueOf(endBh);
			   }
			  } catch (Exception e) {
			   e.getMessage();
			   System.out.println("操作票获取票号报错");
			  }
			  return bh;
		 }
	public String getBHLinShi(String areano,String qz,String tj,String addParm) {
		  String  bh = ""; 
		  String  sql = "select substr(max(bh),'3') as num from (select replace(bh,'"+qz+"','') as bh from (select bh,areano from  opcard.t_a_czpzb "
		    + "union select bh,areano from opcard.t_a_czpzb_history) where areano= "+areano+" and (bh like '"+tj+"' or bh like '"+addParm+"') and length(bh)<8)";
		  List<Map<String,String>> bhMax = DBManager.queryForList(sql);
		  
		  if(bhMax.size()>0){
			  bh = bhMax.get(0).get("NUM").toString();
		  }
		  
		  return bh;
	}
	/**
	 * 描述：保存新建操作票(新操作票表单结构保存方法)
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czrw
	 *            操作任务
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 * @throws UnsupportedEncodingException 
	 */
	public String InsertTicketDBNEW( String zbid, List<CardItemModel> list, String czrw, String isModel, String cardKind, String buildKind,
			String bz, String jxpNo, String czlx) throws SQLException, UnsupportedEncodingException {
		Date date = new Date();
		SimpleDateFormat   sdf   =   new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
	    String now = sdf.format(date.getTime());
		// 插入操作票主表
		String areaNo = "";
		String curUser = CBSystemConstants.getUser().getUserName();
		String sql3 = "select dispatchdeptid from PLATFORM.T_TBP_USER where useralias = '"+curUser+"'";
		List<Map<String,String>> list3 = DBManager.queryForList(sql3);
		
		if(list3.size()>0){
			areaNo = list3.get(0).get("DISPATCHDEPTID");
		}
		
		String sql2 = "SELECT A.ID AS PARENTID,B.ID,B.TYPE AS NAME FROM OPCARD.T_A_CZPTYPE  A ,OPCARD.T_A_CZPTYPE  B WHERE A.ID = B.PARENTID AND B.TYPE IS NOT NULL AND B.AREANO = '330000'";
		List<Map<String,String>> list2 = DBManager.queryForList(sql2);
		
		String zbsql = "INSERT INTO " + username + "T_A_CZPZB(ZBID,CZRW,BUILDKIND,NPRNAME,STARTDATE,ISMODEL,CARDKIND,OPCODE,BZSX,JXPNO,AREANO,CZLX,DATASOURCE,CARDSTATES,"
				+ "YLFLAG,YUFA,AUTHORIZATION,ISLOCK,YLLOCK,TYLX,BH,NPSJ) VALUES('" + zbid + "','"
				+ czrw + "','" + buildKind + "','" + curUser + "','" + getZxsjInNpz() + "','" + isModel + "','1','"
				+ CBSystemConstants.opCode + "','" + bz + "','" + jxpNo + "','" + areaNo + "','" + czlx + "','1','0','0','0','0','1','1','"+list2.get(0).get("ID")+"','"+getCzpBh(areaNo)+"','"+now+"')";
		DBManager.execute(zbsql);

		System.out.println(zbsql);
		
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			String operateItem = bcm.getCardItem();
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();
			String operateContent = bcm.getCardDesc();
			String zlxh = bcm.getOrderNumber();
			String remark = bcm.getRemark();
			String mxid = bcm.getUuIds();
			String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,ZLXH,UNDOFLAG,LEAPFROGFLAG,CARDSTATES,ISDHL) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ operateUnit + "','" + operateContent + "','" + operateNum + "','" + operateItem + "','" + operateShow + "','" + zlxh + "','9','0','0','0')";
			
			System.out.println(zbsql);
			DBManager.execute(mxsql);
		}

		return zbid;
	}
	
	/**
	 * 描述：保存新建操作票
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czrw
	 *            操作任务
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public String InsertTicketDB(String zbid, List<CardItemModel> list, String czrw, String bzsx, String isModel, String cardKind,
			String buildKind, String equipID, String jxpNo) {
		Statement state;
		String date = "sysdate";
		
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		// 插入操作票主表
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BZSX,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO) VALUES('" + zbid + "','"
				+ czrw + "','" + bzsx + "'," + buildKind + ",'" + CBSystemConstants.getUser().getUserName() + "'," + date + ",'" + isModel + "'," + cardKind
				+ ",'" + CBSystemConstants.opCode + "','" + equipID + "','" + jxpNo + "')";
		DBManager.execute(zbsql);
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			// String operateNum = String.valueOf(i+1);
			String operateItem = bcm.getCardItem();
			//解决操作票不一致和czdw为空的bug
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();;
			
			String operateContent = bcm.getCardDesc();
			String mxid = bcm.getUuIds();
			if (operateItem.equals("")) {
				operateItem = operateNum;
				// operateItem= bcm.getCardNum();
			}
			String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,CZDWID) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ operateShow + "','" + operateContent + "'," + operateNum + "," + operateItem + ",'" + operateShow + "','"+bcm.getCzdwID()+"')";
			DBManager.execute(mxsql);
			
		}
		/*
		 * sql=sql+"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
		 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
		 * cbid.length()-1); sqls.add(sql); for (String s : sqls) {
		 * System.out.println(s); }
		 */

		
		return zbid;
	}
	/**
	 * 描述：保存新建操作票
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czxlList
	 *            操作票序列集合
	 * @param czrw
	 *            操作任务
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public String InsertTicketDB(String zbid, List<CardItemModel> list,List<String[]>czxlList, String czrw, String bzsx, String isModel, String cardKind,
			String buildKind, String equipID, String jxpNo) {
		String date = "sysdate";
		
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		// 插入操作票主表
		String zbsql = "INSERT INTO " + username + "T_A_CZPZB(ZBID,CZRW,BZSX,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO) VALUES('" + zbid + "','"
				+ czrw + "','" + bzsx + "'," + buildKind + ",'" + CBSystemConstants.getUser().getUserName() + "'," + date + ",'" + isModel + "'," + cardKind
				+ ",'" + CBSystemConstants.opCode + "','" + equipID + "','" + jxpNo + "')";
		DBManager.execute(zbsql);
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			// String operateNum = String.valueOf(i+1);
			String operateItem = bcm.getCardItem();
			//解决操作票不一致和czdw为空的bug
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();;
			
			String operateContent = bcm.getCardDesc();
			String mxid = bcm.getUuIds();
			if (operateItem.equals("")) {
				operateItem = operateNum;
				// operateItem= bcm.getCardNum();
			}
			String mxsql = "INSERT INTO " + username + "T_A_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ operateShow + "','" + operateContent + "'," + operateNum + "," + operateItem + ",'" + operateShow + "')";
			DBManager.execute(mxsql);
			
		}
		for (int i = 0; i < czxlList.size(); i++) {//偷懒不建序列对象了 
			//设备ID(dev_id),起始状态D5000(original_status),目标状态D5000(target_status)
			//操作票明细ID(mx_id),设备名称(dev_name)
			//操作指令(czzl),操作厂站(czcz)
			String[] czxl = czxlList.get(i);
			String czxlsql = "INSERT INTO " + username + "t_sk_operation(fid,dev_id,original_status,target_status,czp_id,cz_number,create_time,mx_id,dev_name,czzl,czcz,sfkk) "
			+ "VALUES('" + StringUtils.getUUID() + "','" + czxl[0] + "','"+ czxl[1] + "','" + czxl[2] + "','" + zbid + "',"
					+ (i+1) + "," + date + ",'" + czxl[3] + "','" + czxl[4] + "','" + czxl[5] + "','" + czxl[6] + "','1')";
			DBManager.execute(czxlsql);
		}
		return zbid;
	}
	/**
	 * 操作票删除
	 * 
	 * @param MLPCode
	 */
	public void delTicket(String zbid) {
		String sqlzb = "delete from " + username + "t_a_czpzb t where t.zbid='" + zbid + "'";
		String sqlmx = "delete from " + username + "t_a_czpmx t where t.f_zbid='" + zbid + "'";
		// int
		// ishere=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb where zbid='"+zbid+"'");
		// int
		// num=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb where zbid='"+zbid+"' and dmisflag=1");
		// if(ishere>0&&num==0){
		DBManager.execute(sqlzb);
		DBManager.execute(sqlmx);
		// }else if(ishere>0&&num>0){
		// ShowMessage.view("已导入OMS,不能删除！");
		// }

	}
	
	/**
	 * 插入设备元件状态变更，方便以后调用静态安全分析
	 * @param conn 数据库连接
	 * @param cardid 票号
	 * @throws SQLException
	 */
	public void  insertDevState(String cardid) {
		
		Map<Integer, DispatchTransDevice> mapdtd=new HashMap<Integer, DispatchTransDevice>();
		DispatchTransDevice dtd=null;
		PowerDevice dev=null;
		String equipid="";
	    String sql="";
	   
        for (int j = 1; j < DeviceOperate.getAlltransDevMap().size() + 1; j++) {    //遍历设备状态
        	dtd = (DispatchTransDevice) DeviceOperate.getAlltransDevMap().get(j);
        	dev = (PowerDevice) dtd.getTransDevice();
        	
        	equipid=dev.getPowerDeviceID();
        	String beginStatus = dtd.getBeginstatus();
        	String endState = dtd.getEndstate();
        	sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE VALUES('"+java.util.UUID.randomUUID().toString()+"','"
		    	 +equipid+"','"+beginStatus+"','"+endState+"','"+cardid+"','"+dtd.getUuID()+"',sysdate,"+j+")";
		    DBManager.execute(sql);
        }
        
	}
	
	/**
	 * 操作票生成后插入设备状态
	 */
	public void  insertDevStatus() throws SQLException{
				
		    
			DispatchTransDevice dtd=null;
			PowerDevice dev=null;
			Map<String,DispatchTransDevice> compStates=new HashMap<String,DispatchTransDevice>();
			Map<String,DispatchTransDevice> protectStates=new HashMap<String,DispatchTransDevice>(); //保护设备状态变更
			Map<String,DispatchTransDevice> removableStates=new HashMap<String,DispatchTransDevice>();
			
			
			
			//原版遍历设备状态
			for (int j = 1; j < DeviceOperate.getAlltransDevMap().size() + 1; j++) {    //遍历设备状态
	        	dtd = DeviceOperate.getAlltransDevMap().get(j);
	        	dev = dtd.getTransDevice();
	        	if(SystemConstants.Switch.equals(dev.getDeviceType())&&dtd.getFlag().equals("1"))
	        		continue;
	        	if(CBSystemConstants.getProtect(dev.getPowerStationID(), dev.getPowerDeviceID()) != null) {
	        		protectStates.put(dev.getPowerDeviceID(), dtd);
	        	}else if(CBSystemConstants.getRMDeviceByDetailAndType(dev.getRmType(), dev.getPowerStationID(), dev.getKnife(), dev.getDevice())!=null){
	        		removableStates.put(dev.getPowerDeviceID(), dtd);
	        	}else if(compStates.get(dev.getPowerDeviceID())==null){
	        		compStates.put(dev.getPowerDeviceID(), dtd);
	        	}else{
	        		DispatchTransDevice tempDtd=compStates.get(dev.getPowerDeviceID());
	        		if(CBSystemConstants.getDeviceStateValue(dtd.getEndstate()).equals(tempDtd.getBeginstatus()))
	        			//如果同一个设备后一个DTD的目标状态和前一个DTD的源状态相同 说明这个设备的最终状态时不变的
	        		   compStates.remove(dev.getPowerDeviceID());
	        		else
	        		   tempDtd.setEndstate(dtd.getEndstate());
	        	}
	        }
	        
	        String equipid="";
	        String sql="";
	        List<String> state = new ArrayList<String>();
	        for (Iterator iter = compStates.keySet().iterator(); iter.hasNext();) {
        	    equipid=iter.next().toString();
			    dtd=compStates.get(equipid);
			    if(CBSystemConstants.roleCode.equals("0")){
			    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET DEVICESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
			    }else if(CBSystemConstants.roleCode.equals("2")){
			    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET MONITORING='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
			    }else{
			    	sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SET DEVICESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE EQUIPID='"+equipid+"'";
			    }
			    state.add(sql);
			}
	        for (Iterator iter = protectStates.keySet().iterator(); iter.hasNext();) {
        	    equipid=iter.next().toString();
			    dtd=protectStates.get(equipid);
			    sql="UPDATE "+CBSystemConstants.opcardUser+"t_a_protectequip SET PROTECTSTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE PROTECTID='"+equipid+"'";
			    state.add(sql);
			}
	        for (Iterator iter = removableStates.keySet().iterator(); iter.hasNext();) {
	        	 equipid=iter.next().toString();
	 		    dtd=removableStates.get(equipid);
	 		    PowerDevice pd = dtd.getTransDevice();
	 		    int rmstatu = TicketDBManager.getRMStatus(pd.getDevice(),pd.getKnife(),pd.getRmType());
	 		    if(rmstatu==-1){
	 		    	PowerSystemDBOperator.addStationGroundLine(pd.getPowerDeviceID(), pd.getDevice(), pd.getKnife(), pd.getDeviceStatus(), pd.getRmType());
	 		    }else{
	 		    sql="UPDATE "+CBSystemConstants.opcardUser+"t_A_REMOVABLEDEVICE SET ROMVESTATUS='"+CBSystemConstants.getDeviceStateValue(dtd.getEndstate())+"' WHERE REMOVEID='"+equipid+"'  and opcode='"+CBSystemConstants.opCode+"'";
	 		    state.add(sql);
	 		    }
			}
	       
	        if(state.size()>0){
	        	 DBManager.batchUpdate(state.toArray(new String[0]));
	        }
	       
		        		        
//	    		if(SystemConstants.InOutLine.equals(pd.getDeviceType())){
//	    		//全网图设备状态
//		    		tempDev = SystemConstants.getMapPowerLine(QueryDeviceDao.getAllMapLine(pd.getCimID()));
//		    		sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_DEVICESTATUS VALUES("+CBSystemConstants.opcardUser+"t_a_SEQ_STATUSID.NEXTVAL,'"+tempDev.getPowerDeviceID()+"'," +status+",SYSDATE,0,'')";
//			  	    state2.execute(sql);
//	    		}
	}
	
	
	/**
	 * 描述：查询典型操作票主表
	 * 
	 * @param beginTime
	 *            查询起始时间
	 * @param endTime
	 *            查询结束时间
	 * @param conditionTask
	 *            操作任务
	 * @param kind
	 *            类型
	 * @param npr
	 *            拟票人
	 * @param devType
	 *            设备类型
	 * @return [主表ID、操作任务、拟票人、拟票时间]
	 * @throws SQLException
	 */
	public List<String[]> queryDXTicketZB(String beginTime, String endTime, String conditionTask, String kind) {

		List<String[]> results = new ArrayList<String[]>();
		String queryStr = "";
		
		if (!beginTime.equals("")) {
			queryStr = " NPSJ BETWEEN TO_DATE('" + beginTime + " 00:00:00','YYYY-MM-DD HH24:MI:SS') ";
		} else {
			queryStr = " NPSJ BETWEEN TO_DATE('1900-1-1','YYYY-MM-DD') ";
		}
		if (!endTime.equals("")) {
			queryStr = queryStr + " AND TO_DATE('" + endTime + " 23:59:59','YYYY-MM-DD HH24:MI:SS') ";
		} else {
			queryStr = queryStr + " AND TO_DATE('2200-1-1','YYYY-MM-DD') ";
		}
		if (!"".equals(conditionTask)) {
			queryStr = queryStr + " AND T.CZRW LIKE '%" + conditionTask + "%' ";
		}
		String sql = "SELECT T.ZBID,T.CZRW,(SELECT T2.USERNAME FROM " + username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,TO_CHAR(T.NPSJ,'YYYY-MM-DD hh24:mi:ss') NPSJ " + "FROM " + username
				+ "t_a_CZPZB T WHERE " + queryStr + "AND T.ISMODEL='1' AND T.OPCODE='" + kind + "' ORDER BY T.NPSJ DESC";
		try {
			List<Map> list2 = DBManager.queryForList(sql);
			for (Map map2 : list2) {
				String[] tempStr = new String[] { StringUtils.ObjToString(map2.get("zbid")), StringUtils.ObjToString(map2.get("czrw")), StringUtils.ObjToString(map2.get("npr")), StringUtils.ObjToString(map2.get("njsj")) };
				results.add(tempStr);
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			try {
				
			} catch (Exception e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}

		}

		return results;
	}
	
	
	/**
	 * 描述：将操作票保存为典型票
	 * 
	 * @param zbid
	 *            主表ID
	 */
	public boolean InsertDXTicketDB(String zbid) {
		Connection conn = DBManager.getConnection();
		Statement state = null;
		String kind = CBSystemConstants.apptype;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String dxpzbid = java.util.UUID.randomUUID().toString();// 主表ID
			String sql = "";
			if (kind.equals("0")) {
				sql = "INSERT INTO "
						+ username
						+ "t_a_CZPZB(ZBID,CARDKIND,CZRW,BZSX,NPR,NPSJ,ISMODEL,OPCODE,EQUIPID) "// 增加了BZSX
						+ " SELECT '" + dxpzbid + "',CARDKIND,CZRW" + ",BZSX,'" + CBSystemConstants.getUser().getUserName() + "',sysdate,'1','"
						+ CBSystemConstants.opCode + "',EQUIPID FROM " + username
						+ "t_a_CZPZB WHERE ZBID='" + zbid + "'";
			} else {
				sql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,NPR,NPSJ,ISMODEL,OPCODE,EQUIPID,CARDKIND) " + " SELECT '" + dxpzbid + "',CZRW,'"
						+ CBSystemConstants.getUser().getUserName() + "',sysdate,'1','"
						+ CBSystemConstants.opCode + "',EQUIPID ,'3' FROM " + username
						+ "t_a_CZPZB WHERE ZBID='" + zbid + "'";
			}
			state.execute(sql);
			sql = "SELECT (" + username + "GUID) ID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,remark FROM " + username + "t_a_CZPMX WHERE F_ZBID='" + zbid + "'";
			List czpList = DBManager.query(sql);
			Map temp = new HashMap();
			for (int i = 0; i < czpList.size(); i++) {
				temp = (Map) czpList.get(i);
//				String zbid2 = java.util.UUID.randomUUID().toString();// 主表ID
				String[] tempStr = new String[] { java.util.UUID.randomUUID().toString(), dxpzbid, StringUtils.ObjToString(temp.get("CZDW")),
						StringUtils.ObjToString(temp.get("CZNR")), StringUtils.ObjToString(temp.get("CARDORDER")),
						StringUtils.ObjToString(temp.get("CARDITEM")), StringUtils.ObjToString(temp.get("CZSN")),StringUtils.ObjToString(temp.get("REMARK")) };
				sql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,REMARK) values('" + tempStr[0] + "'," + "'" + tempStr[1]
						+ "'," + "'" + tempStr[2] + "'," + "'" + tempStr[3] + "'," + "'" + tempStr[4] + "'," + "'" + tempStr[5] + "'," + "'" + tempStr[6]
						+ "','"+tempStr[7]+"')";
				state.execute(sql);
				conn.commit();
			}
			state.close();
			conn.close();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			return false;
		}
		return true;
	}
	
	
}
