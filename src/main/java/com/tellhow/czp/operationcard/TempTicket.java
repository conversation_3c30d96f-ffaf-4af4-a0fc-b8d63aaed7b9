package com.tellhow.czp.operationcard;

import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.ArrayList;
import java.util.List;

import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.text.JTextComponent;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;

public abstract class TempTicket extends JPanel {
	private static Class tempticketclass;
	protected static TempTicket tempTicket;
	public static boolean isRoll = false;
	protected static JTextComponent tjc;
	public static JFrame doubleScreenJFame = new JFrame();
	protected int panelLength;
	protected  List<CardItemModel> itemModels = new ArrayList<CardItemModel>();

	public static TempTicket getTempTicket() {
		return tempTicket;
	}
	
	public static TempTicket getInstance() {
			if (tempTicket == null) {
				if(SystemConstants.isInitDoubleScreen.equals("1")){
					doubleScreenJFame.addWindowListener(new WindowAdapter() {
				        public void windowClosing(WindowEvent e) {
				        	isRoll=true;
				        	
				        	try{
				        		DeviceOperate.RollbackDeviceStatus();
				    			CBSystemConstants.getDtdMap().clear();
				    			DeviceOperate.ClearDevMap();
				    			CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
				    			CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
				    	    	CBSystemConstants.bztRelationRecord.clear();
				    			CBSystemConstants.bztStateRecord.clear();
				    			CBSystemConstants.bztOrganRecord.clear();
				    			doubleScreenJFame.setVisible(false);
				    			tempTicket = null;
				    			SvgUtil.clear();
				    		}catch(Exception exc){
				    			exc.printStackTrace();
				    		}
				        	
				            isRoll=false;
				        }
				    });
				}
				
				if(SystemConstants.isInitNewWin.equals("0")){
					if(CBSystemConstants.cardflag.equals("1")){
						tempTicket=(TempTicket)CZPImpl.getInstance("TempTicket");
					}else if(CBSystemConstants.cardflag.equals("0")&&CZPImpl.getInstance("TempZhTicket")!=null){
						tempTicket=(TempTicket)CZPImpl.getInstance("TempZhTicket");
					}else{
						if(tempTicket == null)
							tempTicket=(TempTicket)CZPImpl.getInstance("TempTicket");
					}
					
				}
				else {
					tempTicket=(TempTicket)CZPImpl.getInstance("TempTicket");
					
				}
			}
//			if(tempTicket==null){
//				tempTicket=(TempTicket) tempticketclass.newInstance();
//			}
	
		return tempTicket;
	}
	
	public static TempTicket getNewInstance() {

		return (TempTicket)CZPImpl.getInstance("TempTicket");
	}

	public static void setTempTicket(Class clazz) {
		if(clazz == null)
			tempticketclass = null;
		else if (TempTicket.class.isAssignableFrom(clazz)) {
			tempticketclass = clazz;
		} else {
			String errMsg = clazz.getName()
					+ " is not a subclass of TempTicket.";
			throw new RuntimeException(errMsg);
		}
	}

	public abstract List<CardItemModel> getCimlist();

	public abstract void init(CardModel cm, RuleBaseMode ruleBaseMode);


	public static JTextComponent getTjc() {
		return tjc;
	}

	public static void setTjc(JTextComponent tjc) {
		TempTicket.tjc = tjc;
	}

	public int getPanelLength() {
		return panelLength;
	}

	public void setPanelLength(int panelLength) {
		this.panelLength = panelLength;
	}

	
	
	

}
