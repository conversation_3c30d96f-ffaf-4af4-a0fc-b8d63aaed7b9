/*
 * OperateTicketTypePanelbak.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.operationcard;

import java.awt.Cursor;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPopupMenu;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.SwingUtilities;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;

import org.jfree.report.JFreeReportBoot;

import com.tellhow.czp.Robot.CzpRobot;
import com.tellhow.czp.app.PrintOperationCard;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.DeviceStatusManager;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.czp.user.User;
import com.tellhow.czp.user.UserDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.DateUtil;

import czprule.datemodule.JCalendarPanel;
import czprule.model.CodeNameModel;
import czprule.rule.model.RuleBaseMode;
import czprule.securitycheck.view.CheckDialog;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;
import czprule.wordcard.view.InitDeviceTypeChockBox;

/**
 * @命令票 和整个窗口的框架
 * <AUTHOR>
 */
public class OperateTicketConvertPanel extends javax.swing.JPanel {
	private SetJTableProtery sjp = new SetJTableProtery();
	private javax.swing.table.DefaultTableModel jtableModel1;
	private JPopupMenu jPopupMenu = new JPopupMenu();
	private JMenuItem inversion = new JMenuItem("反演",new javax.swing.ImageIcon(getClass().getResource(
			"/tellhow/btnIcon/import.gif")));
	/** Creates new form OperateTicketTypePanelbak */
	public OperateTicketConvertPanel() {
		initComponents();
		init();
		jPopupMenu.add(inversion);
		inversion.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent arg0) {
				
				// TODO Auto-generated method stub
				CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
						jTable1.getSelectedRow(), 1);
				
				boolean ci=this.isExitInversion(cnm.getCode());
				if(!ci)
				{
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "原系统老版本操作票数据暂不支持回演", "提示信息", JOptionPane.INFORMATION_MESSAGE);
				}
				else
				{
					JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
					splitPane.setDividerLocation(1.0);
//					String czrw = jTable1.getValueAt(jTable1.getSelectedRow(), 2)
//							.toString().trim();
//					String npr = jTable1.getValueAt(jTable1.getSelectedRow(), 3)
//							.toString().trim();
//					String npsj = jTable1.getValueAt(jTable1.getSelectedRow(), 4)
//							.toString().trim();
//					String shr = jTable1.getValueAt(jTable1.getSelectedRow(), 5)
//							.toString().trim();
//					String[] cards = new String[] { czrw, npr, npsj, shr };
//					
//					OperateTicketMLPMXPanel tip = new OperateTicketMLPMXPanel( cnm, cards);
//					JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
//					splitPane.setDividerLocation(0.65);
//					
//					splitPane.setRightComponent(tip);
//					
//					tip.setVisible(true);
					new CzpRobot().doInversion(cnm.getCode());
				}
			}
			public boolean isExitInversion(String cardId)
			{
				int obj=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpactionstate where CARDID='"+cardId+"'");
				if(obj==0)
					return false;
				else
					return true;
			}
		}
		
		);
	}
	
	
	

	/**
	 * tableModel
	 */
	private void init() {

		//初始化起始时间
		Date today = new Date();
		DateUtil dateUtil = new DateUtil();
		jTextField1.setText(dateUtil.getHistoryTime(today.getDate() - 1,
				"yyyy-MM-dd"));
		jTextField2.setText(dateUtil.getCurTime("yyyy-MM-dd"));

		//初始化拟票人下拉框
		DefaultComboBoxModel dcbUser = new DefaultComboBoxModel();
		UserDao userdao = new UserDao();
		List<User> allusers = userdao.getAllUser();
		User user = new User();
		user.setUserID("");
		user.setUserName("请选择");
		dcbUser.addElement(user);
		for (int i = 0; i < allusers.size(); i++) {
			user = allusers.get(i);
			dcbUser.addElement(user);
		}
		this.jComboBox1.setModel(dcbUser);

		//初始化设备类型下拉框
		this.jComboBox2.setModel(InitDeviceTypeChockBox.getDevTypeCheckBox());

		//初始化命令票
		if (jtableModel1 == null) {
			jtableModel1 = new DefaultTableModel(new String[][] {},
					new String[] { "序号", "操作票编号", "操作任务", "拟票人", "拟票时间", "审核人",
							"拟票类型", "状态", "操作票类型","是否导入OMS","预令状态" }) {
				public boolean isCellEditable(int rowIndex, int columnIndex) {
					return false;
				}
			};
			jTable1.setModel(jtableModel1);
		}
		this.initTable();

		//OperateTicketDXP dxp = new OperateTicketDXP();
		//jTabbedPane1.setComponentAt(1, dxp);
	}

	/**
	 * 初始化表格
	 */
	public int initTable() {
		
		DefaultTableModel tableModel = (DefaultTableModel) jTable1.getModel();
		tableModel.setRowCount(0);// 清除原有行
		
		String beginTime = jTextField1.getText().trim(); //查询起始时间
		String endTime = jTextField2.getText().trim(); //查询结束时间
		String queryczrw = jTextField3.getText().trim(); //操作任务
		String querynpr = ""; //拟票人
		
		if (this.jComboBox1.getSelectedItem() != null) {
			querynpr = ((User) this.jComboBox1.getSelectedItem()).getUserID();
		}
		String devType = ""; //设备类型
		if (this.jComboBox1.getSelectedItem() != null) {
			devType = ((CodeNameModel) this.jComboBox2.getSelectedItem())
					.getCode();
		}
		TicketDBManager tdb = new TicketDBManager();
		//[主表ID、序号、操作任务、拟票人、拟票时间、审核人、开票类型、状态]
		List<String[]> czpList = tdb.queryTicketZB(beginTime, endTime,
				queryczrw, querynpr, devType);
		
		for (int i = 0; i < czpList.size(); i++) {
			CodeNameModel cnm = new CodeNameModel();
			CodeNameModel state = new CodeNameModel();
			String[] tempStr = czpList.get(i);
			cnm.setCode(tempStr[0]);
			cnm.setName(tempStr[1]);
			state.setCode(tempStr[0]);
			state.setName(tempStr[7]);
			String cartKind = "";
			if(tempStr[8].equals("0"))
				cartKind = "综令票";
			else if(tempStr[8].equals("1"))
				cartKind = "逐项票";
			else if(tempStr[8].equals("2"))
				cartKind = "新投票";
			else if(tempStr[8].equals("3"))
				cartKind = "监控票";
			Object[] rowData = { String.valueOf(i + 1), cnm, tempStr[2],
					tempStr[3], tempStr[4], tempStr[5],
					tempStr[6], tempStr[7] ,cartKind ,tempStr[9] ,tempStr[10]};
			tableModel.addRow(rowData);
		}
		jTable1.setModel(tableModel);
		sjp.makeFace(jTable1);
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultRenderer(jTable1.getColumnClass(1), jTable1);//单元格内容居中
		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMinWidth(60);
		tcm.getColumn(0).setMaxWidth(60);
		tcm.getColumn(2).setMinWidth(300);
		tcm.getColumn(4).setMinWidth(120);
		tcm.getColumn(5).setMinWidth(0);   
		tcm.getColumn(5).setMaxWidth(0);
		tcm.getColumn(7).setMinWidth(0);   
		tcm.getColumn(7).setMaxWidth(0);
		tcm.getColumn(10).setMinWidth(0);   
		tcm.getColumn(10).setMaxWidth(0);
		return czpList.size();
	}

	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">                          
    private void initComponents() {

        jTabbedPane1 = new javax.swing.JTabbedPane();
        jPanel1 = new javax.swing.JPanel();
        jButton2 = new javax.swing.JButton();
        jButton3 = new javax.swing.JButton();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        jPanel5 = new javax.swing.JPanel();
        jLabel1 = new javax.swing.JLabel();
        jLabel3 = new javax.swing.JLabel();
        jTextField1 = new javax.swing.JTextField();
        jLabel2 = new javax.swing.JLabel();
        jTextField2 = new javax.swing.JTextField();
        jButton1 = new javax.swing.JButton();
        jTextField3 = new javax.swing.JTextField();
        jLabel4 = new javax.swing.JLabel();
        jComboBox1 = new javax.swing.JComboBox();
        jLabel5 = new javax.swing.JLabel();
        jComboBox2 = new javax.swing.JComboBox();
        jButton4 = new javax.swing.JButton();
        jButton5 = new javax.swing.JButton();
        jButton6 = new javax.swing.JButton();
        jButton7 = new javax.swing.JButton();
        jButton8 = new javax.swing.JButton();
        jButton9 = new javax.swing.JButton();
//        jButton10 = new javax.swing.JButton();
        jButton11 = new javax.swing.JButton();
        jButton12 = new javax.swing.JButton();
        jButton13 = new javax.swing.JButton();
//        jPanel2 = new OperateTicketDXPDefault();
        jPanel2 = OperateTicketDXP.getInstance();
        jButton3.setVisible(false);
        jButton5.setVisible(false);
        jButton9.setVisible(false);
        
        jButton4.setVisible(false);
        //jButton6.setVisible(false);
        jButton7.setVisible(false);
        jButton8.setVisible(false);
//        jButton10.setVisible(false);
        jButton11.setVisible(false);
        jButton13.setVisible(false);
        
        jTabbedPane1.setBackground(new java.awt.Color(244, 243, 243));
        jTabbedPane1.setOpaque(true);
        jPanel1.setBackground(new java.awt.Color(244, 243, 243));
        jPanel5.setBackground(new java.awt.Color(244, 243, 243));
        jScrollPane1.getViewport().setBackground(new java.awt.Color(244, 243, 243));

        jTabbedPane1.addChangeListener(new javax.swing.event.ChangeListener() {
            public void stateChanged(javax.swing.event.ChangeEvent evt) {
                jTabbedPane1StateChanged(evt);
            }
        });

        jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
        jButton2.setText("删除");
        jButton2.setToolTipText("删除");
        jButton2.setMargin(new java.awt.Insets(1,1,1,1));
        jButton2.setFocusPainted(false);
        jButton2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });

        jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/poll.gif"))); // NOI18N
        jButton3.setToolTipText("归档");
        jButton3.setBorder(null);
        jButton3.setFocusPainted(false);
        jButton3.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton3ActionPerformed(evt);
            }
        });

        jTable1.setFont(new java.awt.Font("宋体", 0, 13)); // NOI18N
        jTable1.setRowHeight(26);
        jTable1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                MLPTableMouseClicked(evt);
            }
        });
        jScrollPane1.setViewportView(jTable1);

        jLabel1.setText("拟票时间 ：");

        jLabel3.setText("操作任务：");

        jTextField1.setPreferredSize(new java.awt.Dimension(6, 26));
        jTextField1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                BeginTimeMouseClicked(evt);
            }
        });

        jLabel2.setText(" 至：");

        jTextField2.setPreferredSize(new java.awt.Dimension(6, 26));
        jTextField2.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                EndTimeMouseClicked(evt);
            }
        });

        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/icons/query.gif"))); // NOI18N
        jButton1.setToolTipText("查询");
        jButton1.setMargin(new java.awt.Insets(1,1,1,1));
        jButton1.setFocusPainted(false);
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        jTextField3.setPreferredSize(new java.awt.Dimension(6, 26));

        jLabel4.setText("拟票人:");

        jLabel5.setText("设备类型:");

        org.jdesktop.layout.GroupLayout jPanel5Layout = new org.jdesktop.layout.GroupLayout(jPanel5);
        jPanel5.setLayout(jPanel5Layout);
        jPanel5Layout.setHorizontalGroup(
            jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jPanel5Layout.createSequentialGroup()
                .addContainerGap()
                .add(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                    .add(jLabel1)
                    .add(jLabel3, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 73, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING, false)
                    .add(jPanel5Layout.createSequentialGroup()
                        .add(jTextField1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 80, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jLabel2, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 31, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jTextField2, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 80, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
                    .add(jTextField3, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.UNRELATED)
                .add(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING, false)
                    .add(jLabel4, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .add(jLabel5, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING, false)
                    .add(jComboBox2, 0, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .add(jComboBox1, 0, 90, Short.MAX_VALUE))
                .add(18, 18, 18)
                .add(jButton1)
                .addContainerGap(0, Short.MAX_VALUE))
        );
        jPanel5Layout.setVerticalGroup(
            jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(org.jdesktop.layout.GroupLayout.TRAILING, jPanel5Layout.createSequentialGroup()
                .addContainerGap(org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .add(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                    .add(jLabel1)
                    .add(jLabel4)
                    .add(jComboBox1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                    .add(jTextField1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                    .add(jLabel2)
                    .add(jTextField2, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                    .add(jLabel3)
                    .add(jPanel5Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                        .add(jTextField3, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                        .add(jLabel5)
                        .add(jComboBox2, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)))
                .addContainerGap())
            .add(jPanel5Layout.createSequentialGroup()
                .addContainerGap()
                .add(jButton1)
                .addContainerGap(org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
        );

        jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/toggle-lt.gif"))); // NOI18N
        jButton4.setToolTipText("收缩");
        jButton4.setBorder(null);
        jButton4.setBorderPainted(false);
        jButton4.setFocusPainted(false);
        jButton4.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton4ActionPerformed(evt);
            }
        });

        jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/edit.png"))); // NOI18N
        jButton5.setToolTipText("审核");
        jButton5.setBorder(null);
        jButton5.setBorderPainted(false);
        jButton5.setFocusPainted(false);
        jButton5.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton5ActionPerformed(evt);
            }
        });

        jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/import.gif"))); // NOI18N
        jButton6.setText("生成监控操作票");
        jButton6.setToolTipText("生成监控操作票");
        jButton6.setMargin(new java.awt.Insets(1,1,1,1));
        jButton6.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        jButton6.setFocusPainted(false);
        jButton6.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton6ActionPerformed(evt);
            }
        });
        
        jButton13.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/network.gif"))); // NOI18N
        jButton13.setText("导入预令票");
        jButton13.setToolTipText("导入预令票");
        jButton13.setMargin(new java.awt.Insets(1,1,1,1));
        jButton13.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        jButton13.setFocusPainted(false);
        jButton13.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton13ActionPerformed(evt);
            }
        });
        
        jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.gif"))); // NOI18N
        jButton7.setText("保存为文件");
        jButton7.setToolTipText("保存为文件");
        jButton7.setMargin(new java.awt.Insets(1,1,1,1));
        jButton7.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        jButton7.setFocusPainted(false);
        jButton7.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
            	jButton7ActionPerformed(evt);
            }
        });
        
        jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/favorite.png"))); // NOI18N
        jButton8.setText("保存为典型票");
        jButton8.setToolTipText("保存为典型票");
        jButton8.setMargin(new java.awt.Insets(1,1,1,1));
        jButton8.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        jButton8.setFocusPainted(false);
        jButton8.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
            	jButton8ActionPerformed(evt);
            }
        });
        
        jButton9.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/check.png"))); // NOI18N
        jButton9.setToolTipText("安全检验");
        jButton9.setBorder(null);
        jButton9.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        jButton9.setFocusPainted(false);
        jButton9.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
            	jButton9ActionPerformed(evt);
            }
        });
        
//        jButton10.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/ico6-1.gif"))); // NOI18N
//        jButton10.setText("打印");
//        jButton10.setToolTipText("打印");
//        jButton10.setMargin(new java.awt.Insets(1,1,1,1));
//        jButton10.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
//        jButton10.setFocusPainted(false);
//        jButton10.addActionListener(new java.awt.event.ActionListener() {
//            public void actionPerformed(java.awt.event.ActionEvent evt) {
//            	jButton10ActionPerformed(evt);
//            }
//        });
        
        jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/edit.png"))); // NOI18N
        jButton11.setText("修改");
        jButton11.setToolTipText("修改");
        jButton11.setMargin(new java.awt.Insets(1,1,1,1));
        jButton11.setFocusPainted(false);
        jButton11.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton11ActionPerformed(evt);
            }
        });
        
        jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/play.png"))); // NOI18N
        jButton12.setText("演示");
        jButton12.setToolTipText("演示");
        jButton12.setMargin(new java.awt.Insets(1,1,1,1));
        jButton12.setFocusPainted(false);
        jButton12.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton12ActionPerformed(evt);
            }
        });

        org.jdesktop.layout.GroupLayout jPanel1Layout = new org.jdesktop.layout.GroupLayout(jPanel1);
        jPanel1.setLayout(jPanel1Layout);
        jPanel1Layout.setHorizontalGroup(
            jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jScrollPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 1019, Short.MAX_VALUE)
            .add(jPanel1Layout.createSequentialGroup()
                .add(jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                    .add(jPanel1Layout.createSequentialGroup()
                        .add(jButton4)
                        .add(0, 0, Short.MAX_VALUE))
                    .add(org.jdesktop.layout.GroupLayout.TRAILING, jPanel1Layout.createSequentialGroup()
                        .add(jPanel5, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                        .add(jButton6)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton11)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton2)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton5)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton3)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton7)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton8)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton13)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
//                        .add(jButton10)
//                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton12)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton9)))
                .addContainerGap())
        );
        jPanel1Layout.setVerticalGroup(
            jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jPanel1Layout.createSequentialGroup()
                .add(jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.TRAILING)
                    .add(jPanel1Layout.createSequentialGroup()
                        .add(jButton4)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jPanel5, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 77, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
                    .add(jButton11)
                    .add(jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING, false)
                    	.add(org.jdesktop.layout.GroupLayout.TRAILING, jButton2, 0, 0, Short.MAX_VALUE)
                        .add(org.jdesktop.layout.GroupLayout.TRAILING, jButton3, 0, 0, Short.MAX_VALUE)
                        .add(org.jdesktop.layout.GroupLayout.TRAILING, jButton5, 0, 0, Short.MAX_VALUE)
                        .add(org.jdesktop.layout.GroupLayout.TRAILING, jButton8, 0, 0, Short.MAX_VALUE)
                        .add(org.jdesktop.layout.GroupLayout.TRAILING, jButton9, 0, 0, Short.MAX_VALUE)
//                        .add(org.jdesktop.layout.GroupLayout.TRAILING, jButton10, 0, 0, Short.MAX_VALUE)
                        .add(org.jdesktop.layout.GroupLayout.TRAILING, jButton12, 0, 0, Short.MAX_VALUE)
                        .add(org.jdesktop.layout.GroupLayout.TRAILING, jButton13, 0, 0, Short.MAX_VALUE)
                        .add(org.jdesktop.layout.GroupLayout.TRAILING, jButton7, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                    .add(jButton6))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jScrollPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 518, Short.MAX_VALUE))
        );

        jTabbedPane1.addTab("调度命令票", jPanel1);

        org.jdesktop.layout.GroupLayout jPanel2Layout = new org.jdesktop.layout.GroupLayout(jPanel2);
        jPanel2.setLayout(jPanel2Layout);
        jPanel2Layout.setHorizontalGroup(
            jPanel2Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(0, 1019, Short.MAX_VALUE)
        );
        jPanel2Layout.setVerticalGroup(
            jPanel2Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(0, 617, Short.MAX_VALUE)
        );

        //jTabbedPane1.addTab("典型票", jPanel2);

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(this);
        this.setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jTabbedPane1)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jTabbedPane1)
        );
    }// </editor-fold>
	//GEN-END:initComponents

    private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	String preBuildType = CBSystemConstants.cardbuildtype;
    	CBSystemConstants.cardbuildtype = "1";
    	setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
    	((JButton)evt.getSource()).setEnabled(false);
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择调度操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
		String zbid = cnm.getCode();
        
		OperateTicketSGPDefault ots = new OperateTicketSGPDefault();
		ots.getjTextArea1().setText(jTable1.getValueAt(selectRows[0], 2).toString());
		
		DefaultTableModel tableModel = new DefaultTableModel(null,
				new String[] { "顺序", "操作单位", "操作内容" });
		JTable jTable1 = ots.getjTable1();
		jTable1.setModel(tableModel);
		
		RuleBaseMode rbm = new RuleBaseMode();
		DeviceStatusManager dsm = new DeviceStatusManager();
		List<RuleBaseMode> list = dsm.getActionStates(zbid);
		int index = 1;
		for(int i = 0; i < list.size(); i++) {
			CardModel cm=WordExecute.getInstance().execute(list.get(i));
			for(int j = 0; j < cm.getCardItems().size(); j++) {
				CardItemModel item = cm.getCardItems().get(j);
				tableModel.addRow(new Object[]{String.valueOf(index),item.getStationName(),item.getCardDesc()});
				index++;
			}
			
		}

		SetJTableProtery sjp = ots.getSjp();
		sjp.makeFace(jTable1);
		sjp.getTableHeader(jTable1);// 列名居中
		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMaxWidth(70);
		tcm.getColumn(0).setMinWidth(50);
		tcm.getColumn(1).setMaxWidth(140);
		tcm.getColumn(1).setMinWidth(140);
		JSplitPane splitPane = (JSplitPane) SystemConstants.getGuiBuilder()
				.getComponent("splitPane");
		splitPane.setDividerLocation(0.0);
		splitPane.setRightComponent(ots);
    	
        //initTable();
        ((JButton)evt.getSource()).setEnabled(true);
        setCursor(Cursor.getDefaultCursor());
        CBSystemConstants.cardbuildtype = preBuildType;
    }        
    
    private void jButton13ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要预令的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
		String zbid = cnm.getCode();
        //CZPOperator.getOperator().importYL(zbid);
        initTable();
    }   
    
    private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要保存的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
    	this.setCursor(new Cursor(Cursor.WAIT_CURSOR));
    	JFreeReportBoot.getInstance().start();
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zb = tdb.queryTicketZB(cnm.getCode());
    	List<BaseCardModel> mx = tdb.queryTicketMX(cnm.getCode());
    	//PrintOperationCard print = new PrintOperationCard(this, "", "", "", "", "", "", jtableModel1, 1,"");
    	PrintOperationCard print = new PrintOperationCard(SystemConstants.getMainFrame(), zb, mx);
    	print.ExportPDF();
    	this.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
    }       
    
    private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要保存为典型票的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
    	TicketDBManager tdb = new TicketDBManager();
		boolean result = tdb.InsertDXTicketDB(cnm.getCode());
		if(result)
			ShowMessage.view("保存成功！");
		else
			ShowMessage.view("保存失败！");
    }  
    
    private void jButton9ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要安全检验的操作票");
			return;
		}
    	CheckDialog dialog = new CheckDialog(SystemConstants.getMainFrame(), true);
    	dialog.setVisible(true);
    }  
    
    /*
    private void jButton10ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要打印的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
    	this.setCursor(new Cursor(Cursor.WAIT_CURSOR));
    	JFreeReportBoot.getInstance().start();
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zb = tdb.queryTicketZB(cnm.getCode());
    	List<BaseCardModel> mx = tdb.queryTicketMX(cnm.getCode());
    	//PrintOperationCard print = new PrintOperationCard(this, "", "", "", "", "", "", jtableModel1, 1,"");
    	PrintOperationCard print = new PrintOperationCard(SystemConstants.getMainFrame(), zb, mx);
    	print.PrintPreview();
    	this.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
    }  
    */
    
    private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要编辑的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
		String czrw = jTable1.getValueAt(selectRows[0], 2).toString().trim();
		OperateTicketDXPMX pot = OperateTicketDXPMX.getInstance();
		pot.init(SystemConstants.getMainFrame(), new CodeNameModel(cnm.getCode(), czrw));
		pot.setVisible(true);
		initTable();
    }  
    
    private void jButton12ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要演示的操作票");
			return;
		}
    	CBSystemConstants.isInversion = true;
    	SystemConstants.getMainFrame().setTitle(CBSystemConstants.SYSTEM_TITLE+"---模拟演示");
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(jTable1.getSelectedRow(), 1);
    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(1.0);
		String zbid = cnm.getCode();
		//buildDZP(zbid);
		new CzpRobot().doInversion(cnm.getCode());
    }  
    
    public void buildDZP(String zbid) {
//    	List list = DBManager.queryForList("select b.station_name,a.equip_name,c.equiptype_name,t.beginstatus,t.endstate from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t,"+CBSystemConstants.opcardUser+"t_e_equipinfo a,"+CBSystemConstants.opcardUser+"t_e_substation b,"+CBSystemConstants.opcardUser+"t_e_equiptype c where t.equipid=a.equip_id and a.station_id=b.station_id and a.equiptype_id=c.equiptype_id and t.cardid='"+zbid+"' order by t.stateorder");
		//edit 2014.6.30
    	List list = DBManager.queryForList(OPEService.getService().OperateTicketConvertPanel(zbid));
		List wordList = new ArrayList();
		String sta = "";
		for(int i = 0; i < list.size(); i++) {
			Map map = (Map)list.get(i);
			String station_name = map.get("station_name").toString();
			String equip_name = map.get("equip_name").toString();
			String equiptype_name = map.get("equiptype_name").toString();
			String beginstatus = map.get("beginstatus").toString();
			String endstate = map.get("endstate").toString();
			if(sta.equals(""))
				sta = station_name;
			if(!station_name.equals(sta))
				continue;
			if( (beginstatus.equals("0") && endstate.equals("1")) || (beginstatus.equals("1") && endstate.equals("0"))) {
				String word = "";
				String opr = "";
				if(equiptype_name.equals("开关") && endstate.equals("0")) {
					opr = "合上";
				}
				else if(equiptype_name.equals("开关") && endstate.equals("1")) {
					opr = "断开";
				}
				else if(equiptype_name.equals("刀闸") && endstate.equals("0")) {
					opr = "推上";
				}
				else if(equiptype_name.equals("刀闸") && endstate.equals("1")) {
					opr = "拉开";
				}
				else if(equiptype_name.equals("接地刀闸") && endstate.equals("0")) {
					opr = "推上";
				}
				else if(equiptype_name.equals("接地刀闸") && endstate.equals("1")) {
					opr = "拉开";
				}
				if(equip_name.indexOf(equiptype_name) == -1)
					equip_name = equip_name + equiptype_name;
				if(!opr.equals("")) {
					word = opr + equip_name;
					wordList.add(word);
				}
			}
		}
		String dzzbid = java.util.UUID.randomUUID().toString();
		
		DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_czpzb select '"+dzzbid+"',t.czrw,t.states,1,t.ylflag,t.cardkind,t.buildkind,t.islock,t.ismodel,t.opcode,t.npr,t.npsj,t.shr,t.bh,t.shsj,t.cardstates,t.equipid,t.jkfzr_user from "+CBSystemConstants.opcardUser+"t_a_czpzb t where t.zbid='"+zbid+"'");
		for(int i = 0; i < wordList.size(); i++) {
			String order = String.valueOf(i+1);
			String dzmxid = java.util.UUID.randomUUID().toString();
			DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_czpmx(mxid,f_zbid,czdw,cznr,cardorder,carditem) values('"+dzmxid+"','"+dzzbid+"','"+sta+"','"+wordList.get(i)+"','"+order+"','"+order+"')");
		}
    }

	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view("请选择要删除的操作票");
			return;
		}
		int ok = JOptionPane.showConfirmDialog(this, "删除后不能恢复，你确定要删除吗？",
				"操作票提示框", JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.NO_OPTION) {
			return;
		}
		jTable1.removeEditor();
		TicketDBManager tdb = new TicketDBManager();
		for (int i = selectRows.length - 1; i >= 0; i--) {
			CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
					selectRows[i], 1);
			String zbid = cnm.getCode();
			tdb.delTicket(zbid);
		}
		initTable();
	}

	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view("请选择需要归档的命令票!");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view("不能同时归档多条命令票!");
			return;
		}
		String gdStr = jTable1.getValueAt(selectRows[0], 7).toString().trim();
		if ("已归档".equals(gdStr)) {
			ShowMessage.view("命令票已经归档!");
			return;
		}
		int ok = JOptionPane.showConfirmDialog(this, "确定将此命令票归档吗？", "操作票提示框",
				JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.YES_OPTION) {
			CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
					selectRows[0], 1);
			String zbid = cnm.getCode();
			TicketDBManager tdb = new TicketDBManager();
			tdb.updateState(zbid);
			jTable1.setValueAt("  已归档", selectRows[0], 7);
		}
	}

	private void MLPTableMouseClicked(java.awt.event.MouseEvent evt) {
	
		if (SwingUtilities.isLeftMouseButton(evt) && evt.getClickCount() == 2) {
				CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
						jTable1.getSelectedRow(), 1);
				String czrw = jTable1.getValueAt(jTable1.getSelectedRow(), 2)
						.toString().trim();
				String npr = jTable1.getValueAt(jTable1.getSelectedRow(), 3)
						.toString().trim();
				String npsj = jTable1.getValueAt(jTable1.getSelectedRow(), 4)
						.toString().trim();
				String shr = jTable1.getValueAt(jTable1.getSelectedRow(), 8)
						.toString().trim();
				String[] cards = new String[] { czrw, npr, npsj, shr };
				
				OperateTicketMLPMX ti = OperateTicketMLPMX.getInstance();
				ti.init(cnm, cards);
				ti.setVisible(true);
				
//				OperateTicketDXPMX pot = new OperateTicketDXPMX(
//						SystemConstants.getMainFrame(), new CodeNameModel(
//								cnm.getCode(), czrw));
//				pot.setVisible(true);
				
		} else if (jTable1.getSelectedRow() != -1
				&& SwingUtilities.isRightMouseButton(evt)
				&& evt.getClickCount() == 1) {
				//jPopupMenu.show(jTable1,evt.getX(),evt.getY());
		}
	}

	//tab切换
	private void jTabbedPane1StateChanged(javax.swing.event.ChangeEvent evt) {
		// TODO add your handling code here:

		//		int index = jTabbedPane1.getSelectedIndex();
		//		if (index == 0) {
		//			init();//初始化命令票面板
		//		}
		//		if (index == 1) {
		//			OperateTicketDXP dxp = new OperateTicketDXP();
		//			this.jPanel2.add(dxp);
		//		}

	}

	//审核
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view("请选择需要审核的命令票!");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view("不能同时审核多条命令票!");
			return;
		}
		String shrName = jTable1.getValueAt(selectRows[0], 5).toString().trim();
		if (!shrName.equals("")) {
			ShowMessage.view("该票已审核！");
			return;
		}
		int ok = JOptionPane.showConfirmDialog(this, "是否确定审核该操作命令？", "操作票提示框",
				JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.YES_OPTION) {
			CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
					selectRows[0], 1);
			User shr = CBSystemConstants.getUser();
			jTable1.setValueAt(shr.getUserName(), selectRows[0], 5);
			TicketDBManager tdb = new TicketDBManager();
			tdb.updateShr(shr, cnm);//插入审核人
		}
		return;
	}

	//隐藏查询条件
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if (this.jButton4.getToolTipText().equals("收缩")) {
			this.jPanel5.setVisible(false);
			this.jButton4.setToolTipText("扩展");
			this.jButton4.setIcon(new javax.swing.ImageIcon(getClass()
					.getResource("/tellhow/btnIcon/toggle-lt.gif")));
		} else {
			this.jPanel5.setVisible(true);
			this.jButton4.setToolTipText("收缩");
			this.jButton4.setIcon(new javax.swing.ImageIcon(getClass()
					.getResource("/tellhow/btnIcon/toggle-rt.gif")));
		}
	}

	//查询
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		int count = initTable();
		if(count == 0) {
			ShowMessage.view("没有查询到操作票记录！");
			return;
		}
	}

	//起始查询时间选择
	private void BeginTimeMouseClicked(java.awt.event.MouseEvent evt) {
		String selectTime = "";
		if (evt.getButton() == 1 && evt.getClickCount() == 2) {
			JCalendarPanel calendarPanel = new JCalendarPanel(
					jTextField1.getX() + 12, jTextField1.getY() + 19);
			selectTime = calendarPanel.getDateStr();
		}
		if (!selectTime.equals("")) {
			String[] beginTime = selectTime.split(" ");
			jTextField1.setText(beginTime[0]);
		}
	}

	//结束查询时间选择
	private void EndTimeMouseClicked(java.awt.event.MouseEvent evt) {
		String selectTime = "";
		if (evt.getButton() == 1 && evt.getClickCount() == 2) {
			JCalendarPanel calendarPanel = new JCalendarPanel(
					jTextField2.getX() + 12, jTextField2.getY() + 19);
			selectTime = calendarPanel.getDateStr();
		}
		if (!selectTime.equals("")) {
			String[] beginTime = selectTime.split(" ");
			jTextField2.setText(beginTime[0]);
		}
	}

	//GEN-BEGIN:variables
    // Variables declaration - do not modify                     
    private javax.swing.JButton jButton1;
    private javax.swing.JButton jButton2;
    private javax.swing.JButton jButton3;
    private javax.swing.JButton jButton4;
    private javax.swing.JButton jButton5;
    private javax.swing.JButton jButton6;
    private javax.swing.JButton jButton7;
    private javax.swing.JButton jButton8;
    private javax.swing.JButton jButton9;
//    private javax.swing.JButton jButton10;
    private javax.swing.JButton jButton11;
    private javax.swing.JButton jButton12;
    private javax.swing.JButton jButton13;
    private javax.swing.JComboBox jComboBox1;
    private javax.swing.JComboBox jComboBox2;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JPanel jPanel1;
    private javax.swing.JPanel jPanel2;
    private javax.swing.JPanel jPanel5;
    private javax.swing.JScrollPane jScrollPane1;
    public javax.swing.JTabbedPane jTabbedPane1;
    private javax.swing.JTable jTable1;
    private javax.swing.JTextField jTextField1;
    private javax.swing.JTextField jTextField2;
    private javax.swing.JTextField jTextField3;
	// End of variables declaration//GEN-END:variables

}
