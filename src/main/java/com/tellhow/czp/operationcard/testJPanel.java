package com.tellhow.czp.operationcard;
import java.awt.BorderLayout;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import javax.swing.BoxLayout;
import javax.swing.JButton;

import javax.swing.WindowConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableModel;
import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextArea;

/**
* This code was edited or generated using CloudGarden's Jigloo
* SWT/Swing GUI Builder, which is free for non-commercial
* use. If Jigloo is being used commercially (ie, by a corporation,
* company or business for any purpose whatever) then you
* should purchase a license for each developer using Jigloo.
* Please visit www.cloudgarden.com for details.
* Use of Jigloo implies acceptance of these licensing terms.
* A COMMERCIAL LICENSE HAS NOT BEEN PURCHASED FOR
* THIS MACHINE, SO JIGLOO OR THIS CODE CANNOT BE USED
* LEGALLY FOR ANY CORPORATE OR COMMERCIAL PURPOSE.
*/
public class testJPanel extends javax.swing.JPanel {
	private JPanel jPanel1;
	private JButton jButton1;
	private JTable jTable1;
	private JTextArea jTextArea2;
	private JTextArea jTextArea1;
	private JButton jButton2;
	private JPanel jPanel3;
	private JPanel jPanel2;

	
	public testJPanel() {
		super();
		initGUI();
	}
	
	private void initGUI() {
		try {
			BoxLayout thisLayout = new BoxLayout(this, javax.swing.BoxLayout.Y_AXIS);
			this.setLayout(thisLayout);
			{
				jPanel1 = new JPanel();
				jPanel1.setBackground(new Color(206, 231, 255));
				FlowLayout jPanel1Layout = new FlowLayout();
				jPanel1Layout.setAlignment(FlowLayout.LEFT);
				this.add(jPanel1);
				jPanel1.setLayout(jPanel1Layout);
				{
					jButton1 = new JButton();
					jPanel1.add(jButton1);
					jButton1.setText("jButton1");
				}
				{
					jButton2 = new JButton();
					jPanel1.add(jButton2);
					jButton2.setText("jButton2");
				}
			}
			{
				jPanel2 = new JPanel();
				FlowLayout jPanel2Layout = new FlowLayout();
				jPanel2Layout.setAlignment(FlowLayout.LEFT);
				jPanel2.setLayout(jPanel2Layout);
				jPanel2.setBackground(new Color(0, 0, 255));
				this.add(jPanel2);
				{
					JScrollPane scrollPane_1 = new JScrollPane();
					jPanel2.add(scrollPane_1);
					JTextArea upjtextArea = new JTextArea();
					upjtextArea.setLineWrap(true);
					upjtextArea.setColumns(68);
					upjtextArea.setRows(5);
					upjtextArea.setMaximumSize(new Dimension(300,100));
					upjtextArea.setFont(new java.awt.Font("宋体", 0, 14)); 
					scrollPane_1.setViewportView(upjtextArea);
				}
				{
					JScrollPane scrollPane_2 = new JScrollPane();
					jPanel2.add(scrollPane_2);
					JTextArea upjtextArea = new JTextArea();
					upjtextArea.setLineWrap(true);
					upjtextArea.setColumns(68);
					upjtextArea.setRows(4);
					upjtextArea.setMaximumSize(new Dimension(300,100));
					upjtextArea.setFont(new java.awt.Font("宋体", 0, 14)); 
					scrollPane_2.setViewportView(upjtextArea);
				}
			}
			{
				jPanel3 = new JPanel();
				BorderLayout jPanel3Layout = new BorderLayout();
				jPanel3.setLayout(jPanel3Layout);
				jPanel3.setBackground(new Color(0, 255, 255));
				this.add(jPanel3);
				{
					TableModel jTable1Model = 
						new DefaultTableModel(
								new String[][] { { "One", "Two" }, { "Three", "Four" } },
								new String[] { "Column 1", "Column 2" });
					jTable1 = new JTable();
					jPanel3.add(jTable1, BorderLayout.CENTER);
					jTable1.setModel(jTable1Model);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
