package com.tellhow.czp.operationcard;

import java.awt.Dialog;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.JTable;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.basic.SetJTableProtery;

import czprule.model.CodeNameModel;

public abstract class OperateTicketDXPMX extends JDialog{

	protected static OperateTicketDXPMX instance;
	protected static JFrame parentFrame;
	protected CodeNameModel cnm;
	protected DefaultTableModel jTable1Model = null;
	protected SetJTableProtery sjp = new SetJTableProtery();
	
	protected final JPanel contentPanel = new JPanel();
	protected JTable jTable1;
	protected JTable jTable2;
	 
	public static OperateTicketDXPMX getInstance() {

		if (instance == null) {
			instance=(OperateTicketDXPMX)CZPImpl.getInstance("OperateTicketDXPMX");
			if(instance == null)
				instance = new OperateTicketDXPMXDefault();
			instance.addWindowListener( new WindowAdapter() {
			     @Override
			     public void windowClosing(WindowEvent we) {
			    	 instance.dispose();
					 instance = null;
			     }
			});
			return instance;
		}
		else
			return instance;
	}
	
	public OperateTicketDXPMX(JFrame parent,boolean isModel) {
		super(parent,isModel);
	}
	
	public abstract void init(java.awt.Frame parent, CodeNameModel cnm);
	
	public OperateTicketDXPMX() {
		
	}
	
	public OperateTicketDXPMX(java.awt.Frame parent, String name, boolean isModel) {
		super(parent, name, isModel);
	}
	
	public OperateTicketDXPMX(java.awt.Frame parent, String name) {
		super(parent, name,Dialog.ModalityType.APPLICATION_MODAL);
	}
}
