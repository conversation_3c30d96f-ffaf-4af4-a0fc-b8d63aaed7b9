/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：
 * 功能说明 : 
 * 作    者 : 姚星乐
 * 开发日期 : 2012-11-19
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.operationcard;
import java.awt.Dimension;
import java.util.List;

import javax.swing.BorderFactory;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;

import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;

import czprule.model.CodeNameModel;
//import com.tellhow.czp.userrule.testDevStatusAction;
//import com.tellhow.czp.userrule.testFlash;

/**
* This code was edited or generated using CloudGarden's Jigloo
* SWT/Swing GUI Builder, which is free for non-commercial
* use. If Jigloo is being used commercially (ie, by a corporation,
* company or business for any purpose whatever) then you
* should purchase a license for each developer using Jigloo.
* Please visit www.cloudgarden.com for details.
* Use of Jigloo implies acceptance of these licensing terms.
* A COMMERCIAL LICENSE HAS NOT BEEN PURCHASED FOR
* THIS MACHINE, SO JIGLOO OR THIS CODE CANNOT BE USED
* LEGALLY FOR ANY CORPORATE OR COMMERCIAL PURPOSE.
*/
public class OperateTicketMLPMXPanel extends JPanel {
	private JTable jTable1;
	private JLabel jLabel3;
	private JLabel jLabel5;
	private JLabel jLabel_OperateTask;
	private JLabel jLabel_WriteTime;
	private JLabel jLabel_CheckPerson;
	private JLabel jLabel_WritePerson;
	private JLabel jLabel_TicketNo;
	private JLabel jLabel6;
	private JLabel jLabel4;
	private JLabel jLabel1;
	private String[] cards;
	private CodeNameModel cnm = null;
	
	private javax.swing.table.DefaultTableModel jtableModel1=null;
	private JScrollPane jScrollPane1;
	private JPanel jPanel2;
	private JPanel jPanel1;

	private Object[][] tableData = null;
	
	private SetJTableProtery sjp = new SetJTableProtery();
	
	public OperateTicketMLPMXPanel(CodeNameModel cnm,
			String[] cards)
	{
		this.cards=cards;
		this.cnm=cnm;
		initGUI();
	}
	List CzpActionStateList = null;
	
	
	private void initGUI() {
		try {
			{
				this.setPreferredSize(new java.awt.Dimension(413, 427));
				this.setLayout(null);
				this.setBorder(BorderFactory.createCompoundBorder(
						null, 
						null));
				this.setOpaque(false);
				{
					jPanel1 = new JPanel();
					this.add(jPanel1);
					jPanel1.setBounds(0, 177, 413, 250);
					jPanel1.setBorder(BorderFactory.createTitledBorder("\u64cd\u4f5c\u660e\u7ec6"));
					jPanel1.setLayout(null);
				}
				{
					jPanel2 = new JPanel();
					this.add(jPanel2);
					jPanel2.setBounds(0, 4, 413, 173);
					jPanel2.setLayout(null);
					jPanel2.setBorder(BorderFactory.createTitledBorder("\u57fa\u672c\u4fe1\u606f"));
					jPanel2.setEnabled(false);
					{
						jLabel_OperateTask = new JLabel();
						jPanel2.add(jLabel_OperateTask);
						jLabel_OperateTask.setBounds(65, 151, 340, 15);
					}
					{
						jLabel_WriteTime = new JLabel();
						jPanel2.add(jLabel_WriteTime);
						jLabel_WriteTime.setBounds(65, 119, 170, 15);
					}
					{
						jLabel_CheckPerson = new JLabel();
						jPanel2.add(jLabel_CheckPerson);
						jLabel_CheckPerson.setBounds(64, 88, 95, 15);
					}
					{
						jLabel_WritePerson = new JLabel();
						jPanel2.add(jLabel_WritePerson);
						jLabel_WritePerson.setBounds(64, 57, 95, 15);
					}
					{
						jLabel_TicketNo = new JLabel();
						jPanel2.add(jLabel_TicketNo);
						jLabel_TicketNo.setBounds(65, 30, 95, 15);
						jLabel_TicketNo.setVerticalTextPosition(SwingConstants.TOP);
					}
					{
						jLabel6 = new JLabel();
						jPanel2.add(jLabel6);
						jLabel6.setText("\u64cd\u4f5c\u4efb\u52a1\uff1a");
						jLabel6.setBounds(5, 151, 60, 15);
					}
					{
						jLabel5 = new JLabel();
						jPanel2.add(jLabel5);
						jLabel5.setText("\u62df\u7968\u65f6\u95f4\uff1a");
						jLabel5.setBounds(5, 119, 60, 15);
					}
					{
						jLabel4 = new JLabel();
						jPanel2.add(jLabel4);
						jLabel4.setText("\u62df\u7968\u4eba\uff1a");
						jLabel4.setBounds(16, 58, 48, 15);
					}
					{
						jLabel3 = new JLabel();
						jPanel2.add(jLabel3);
						jLabel3.setText("\u5ba1\u6838\u4eba\uff1a");
						jLabel3.setBounds(16, 89, 48, 15);
					}
					{
						jLabel1 = new JLabel();
						jPanel2.add(jLabel1);
						jLabel1.setText("\u64cd\u4f5c\u7968\u53f7\uff1a");
						jLabel1.setBounds(5, 30, 60, 15);
					}
				}
				this.setAutoscrolls(true);
				this.jLabel_TicketNo.setText(this.cnm.getName());
				this.jLabel_WritePerson.setText(this.cards[1]);
				this.jLabel_CheckPerson.setText(this.cards[3]);
				this.jLabel_WriteTime.setText(this.cards[2]);
				this.jLabel_OperateTask.setText(this.cards[0]);
				jTable1=new JTable();
				Dimension size = jTable1.getTableHeader().getPreferredSize();
				size.height = 43;//设置新的表头高度40 
				
				jTable1.getTableHeader().setPreferredSize(size);
				jTable1.setOpaque(false);
				jtableModel1 = new DefaultTableModel(new Object[][]{}, new String[] { "序号",
						"操作单位", "操作内容"});
				
				jTable1.setRowHeight(26);
				jTable1.setModel(jtableModel1);
				jTable1.setFont(new java.awt.Font("宋体", 0, 13));
				jTable1.setBorder(BorderFactory.createCompoundBorder(
						null, 
						null));
				{
					jScrollPane1 = new JScrollPane();
					jPanel1.add(jScrollPane1);
					jScrollPane1.setBounds(5, 18, 403, 227);
					{
						jScrollPane1.setViewportView(jTable1);
						
						jTable1.setBounds(160, 121, 248, 112);
					}
				}
				

				TicketDBManager tdb = new TicketDBManager();
				List<BaseCardModel> results = tdb.queryTicketMX(this.cnm.getCode());
				BaseCardModel bcm = null;
				
				for (int i = 0; i < results.size(); i++) {
					bcm = results.get(i);
					Object[] rowData = {
							new CodeNameModel(bcm.getMxid(), String.valueOf(i + 1)),
							bcm.getStationName(), bcm.getCardDesc()};
					jtableModel1.addRow(rowData);
				}
				
				TableColumnModel tcm = jTable1.getColumnModel();
				tcm.getColumn(0).setMaxWidth(50);
				tcm.getColumn(1).setMaxWidth(90);
				tcm.getColumn(2).setMinWidth(280);
				
				sjp.makeFace(jTable1);
				sjp.getTableHeader(jTable1);//列名居中
				
			}
			
		} catch(Exception e) {
			e.printStackTrace();
		}
	}

}
