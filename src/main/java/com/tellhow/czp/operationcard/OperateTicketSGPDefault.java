package com.tellhow.czp.operationcard;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Insets;
import java.awt.Point;
import java.awt.event.ActionEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;
import javax.swing.text.JTextComponent;

import com.tellhow.czp.app.service.OMSService;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.service.OperationCheck;
import com.tellhow.czp.service.OperationCheckDefault;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.securitycheck.view.CheckWord;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;
/**
 *泰豪软件科技有限公司
 * <AUTHOR>
 * 修改：张余平
 * 界面修改：王豪
 */
public class OperateTicketSGPDefault extends OperateTicketSGP{
	private boolean Kindflag = false;
	private DefaultTableModel TableModel = null;
	ArrayList<int[]> errorList = new ArrayList<int[]>();
	private SetJTableProtery sjp = new SetJTableProtery();
	private final JPanel contentPanel = new JPanel();
	private JTable jTable1;
	public static JTextComponent jc;
	public static OperateTicketSGPDefault ptsgp = null;
	public static synchronized OperateTicketSGPDefault getInstance() {
		if (ptsgp == null)
			ptsgp = new OperateTicketSGPDefault();
		return ptsgp;
	}
	public static JTextComponent getJc(){
		return jc;
	}
	public static void setJc(JTextComponent jc){
		jc=OperateTicketSGPDefault.jc;
	}
	public SetJTableProtery getSjp() {
		return sjp;
	}
	public void setSjp(SetJTableProtery sjp) {
		this.sjp = sjp;
	}
	public JTable getjTable1() {
		return jTable1;
	}
	public void setjTable1(JTable jTable1) {
		this.jTable1 = jTable1;
	}
	public JTextArea getjTextArea1() {
		return jTextArea1;
	}
	public void setjTextArea1(JTextArea jTextArea1) {
		this.jTextArea1 = jTextArea1;
	}
	/**
	 * Launch the application.
	 */
/*	public static void main(String[] args) {
		try {
			OperateTicketSGP dialog = new OperateTicketSGP();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}*/
	/** Creates new form OperateTicketSGP */
	public OperateTicketSGPDefault() {
		initComponents();
		init();
	}
	private void inittable(){
		if(TableModel!=null){
			int n=TableModel.getRowCount();
			for(int i=0;i<n;i++){
				TableModel.getValueAt(i, 0);
//				TableModel.setValueAt(i+1, i, 0);
			}
		}
	}
	/**
	 * @初始化
	 */
	public void init() {
		if (TableModel == null) {
			TableModel = new DefaultTableModel(null, new String[] { "操作顺序", "操作单位",
					"操作内容" }) {
				public boolean isCellEditable(int rowIndex, int columnIndex) {
					return true;
				}
			};
			TableModel.setRowCount(10);
			jTable1.setModel(TableModel);
			jTable1.addMouseListener(new MouseAdapter() {
				@Override
				public void mouseClicked(MouseEvent e) {
					// TODO Auto-generated method stub
					Point mousepoint; 
					mousepoint =e.getPoint();
					int row=jTable1.rowAtPoint(mousepoint);
					int column=jTable1.columnAtPoint(mousepoint);
					if(jTable1.editCellAt(row, column)){
						 Component editor = jTable1.getEditorComponent();
						    editor.requestFocusInWindow();
						    Component c = editor.getComponentAt(0, 0);
						    if (c != null && c instanceof JTextComponent) {
						        ((JTextComponent) c).getCaretPosition();
						        jc=(JTextComponent)c;
						    }
					}
				}
			});
		}
		TableModel.setRowCount(10);
		sjp.makeFace(jTable1);
		sjp.getTableHeader(jTable1);//列名居中
		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMaxWidth(70);
		tcm.getColumn(0).setMinWidth(50);
		tcm.getColumn(1).setMaxWidth(140);
		tcm.getColumn(1).setMinWidth(140);
		/*
		TextAreaRenderer textAreaRenderer=new TextAreaRenderer();
		TextAreaEditor textAreaEditor=new TextAreaEditor();
		tcm.getColumn(2).setCellRenderer(textAreaRenderer);
		tcm.getColumn(2).setCellEditor(textAreaEditor);
		*/
		if("1".equals(CBSystemConstants.unitCode)){
			jTable1.getColumnModel().getColumn(1).setMaxWidth(0);
			jTable1.getColumnModel().getColumn(1).setMinWidth(0);
			jTable1.getColumnModel().getColumn(1).setPreferredWidth(0);
			jTable1.getColumnModel().getColumn(1).setResizable(false);
		}
	}
	public void initTable(CodeNameModel cnm) {
		if (cnm == null)
			return;
		if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
		TableModel.setRowCount(0);
		this.jTextArea1.setText(cnm.getName());
		TicketDBManager tdb = new TicketDBManager();
		List<BaseCardModel> results = tdb.queryTicketMX(cnm.getCode());
		BaseCardModel bcm = null;
		String cardSub = "";
		for (int i = 0; i < results.size(); i++) {
			bcm = results.get(i);
			String rowid = String.valueOf(i+1);
//			Object[] rowData = { bcm.getCardSub().equals(cardSub)?"": bcm.getCardSub(), bcm.getCzsn().equals("")?bcm.getStationName():bcm.getCzsn(), bcm.getCardDesc() };
			Object[] rowData = { rowid, bcm.getCzsn().equals("")?bcm.getStationName():bcm.getCzsn(), bcm.getCardDesc() };
			cardSub = cardSub.equals(bcm.getCardSub())?cardSub:bcm.getCardSub();
			TableModel.addRow(rowData);
		}
		jTable1.setModel(TableModel);
	
	}
	public boolean checkContent() {

		if(jTextArea1.getText().trim().equals("")) {
			ShowMessage.viewWarning(this, "操作任务不能为空!");
			return false;
		}
		int countRowsI = jTable1.getRowCount();
		for (int i = 0; i < countRowsI; i++) {
			String czsx = jTable1.getValueAt(i, 0) == null?"":jTable1.getValueAt(i, 0).toString().trim();
			String czdw = jTable1.getValueAt(i, 1) == null?"":jTable1.getValueAt(i, 1).toString().trim();
			String cznr = jTable1.getValueAt(i, 2) == null?"":jTable1.getValueAt(i, 2).toString().trim();
			if(cznr.equals("")) {
//				if(i == 0) {
//					ShowMessage.viewWarning(this, "第一项操作步骤的操作内容不能为空!");
//					return false;
//				}
//				for (int j = i+1; j < countRowsI; j++) {
//					cznr = jTable1.getValueAt(j, 2) == null?"":jTable1.getValueAt(j, 2).toString();
//					if(!cznr.equals("")) {
//						ShowMessage.viewWarning(this, "由于后一行有操作内容，第"+String.valueOf(j)+"行不能为空!");
//						return false;
//					}
//				}
//				if(!czdw.equals("") || !czsx.equals("")) {
//					ShowMessage.view(this, "第"+String.valueOf(i+1)+"行没有填写操作内容，该行不会被保存!");
//					break;
//				}
			}
			if(!cznr.equals("")) {
				if(czdw.equals("")) {
//					if(i == 0) {
//						ShowMessage.viewWarning(this, "第一项操作步骤的操作单位不能为空!");
//						return false;
//					}
//					else {
//						jTable1.setValueAt(jTable1.getValueAt(i-1, 1).toString(), i, 1);
//					}
				}
				//操作顺序补全
				if(czsx.equals("") ) {
					if(i == 0)
						jTable1.setValueAt("1", i, 0);
					else
						jTable1.setValueAt(String.valueOf(jTable1.getValueAt(i-1, 0).toString()), i, 0);
				}
				else if(!tbp.common.util.StringUtil.isNum(czsx)) {
					String sz = StringUtils.getSZ(czsx);
					if(sz.equals("")) {
						ShowMessage.viewWarning(this, "第"+String.valueOf(i+1)+"行操作顺序填写的不是数字!");
						return false;
					}
					else
						jTable1.setValueAt(sz, i, 0);
				}
			}
		}
		return true;
	
	}
	public void checkCard() {
		final ArrayList<int[]> errorList = new ArrayList<int[]>();
		errorList.clear();
		DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component cell = super.getTableCellRendererComponent  
                        (table, value, isSelected, hasFocus, row, column);
                boolean isError = false;
                for(int[] c : errorList) {
                	if(c[0] == row && c[1] == column && cell.isBackgroundSet()) { 
                		cell.setForeground(Color.red);
                		isError = true;
                		break;
                	}
                if(!isError)
                	cell.setForeground(Color.black);
                }
                return cell;
            }
        };
		jTable1.getColumnModel().getColumn(2).setCellRenderer(tcr);
		
		new Thread(new Runnable() {
			
			public void run() {
				// TODO Auto-generated method stub
				final int preSize = DeviceOperate.getAlltransDevMap().size();
				final String sysBuild = CBSystemConstants.cardbuildtype;
				final String sysStatus = CBSystemConstants.cardstatus;
				
				CBSystemConstants.cardstatus = "1";
				CBSystemConstants.cardbuildtype = "1";
				
				CreatePowerStationToplogy.loadSysData();
				
				List<String> loadStationList = new ArrayList<String>();
				
				for(int i = 0; i < jTable1.getRowCount(); i++) {
					
					jTable1.setRowSelectionInterval(i, i);
					String stationName = StringUtils.ObjToString(TableModel.getValueAt(i, 1)).trim();
					String oprdesc = StringUtils.ObjToString(TableModel.getValueAt(i, 2)).trim();
					List<RuleBaseMode> rbmList=OperationCheckDefault.execute(stationName, oprdesc);
					if(rbmList.size() == 0)
						break;
					else if(!rbmList.get(0).getCheckout()) {
						errorList.add(new int[]{i,2});
						jTable1.clearSelection();
						break;
					}
					
					boolean isSuccess = true;
					for(RuleBaseMode rbm : rbmList) {
						if(rbm.getBeginStatus().equals("") || rbm.getEndState().equals(""))
							continue;
						String stationID = rbm.getPd().getPowerStationID();
						if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
							CreatePowerStationToplogy.loadFacEquip(stationID);
						}
						if(!loadStationList.contains(stationID)) {
							InitDeviceStatus ie = new InitDeviceStatus();
							ie.initStatus_EMSToCache(stationID);
							loadStationList.add(stationID);
						}
						isSuccess = OperationCheckDefault.check(rbm);
						if(!isSuccess) {
							errorList.add(new int[]{i,2});
							jTable1.clearSelection();
							break;
						}
					}
					if(!isSuccess)
						break;
				}
				
				
				Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
				for (int i = allMap.size(); i >preSize; i--) {
					allMap.remove(i);
				}
				for(int i = 1; i <= allMap.size(); i++) {
					DispatchTransDevice dtd = allMap.get(i);
					dtd.getTransDevice().setDeviceStatus(dtd.getEndstate());
				}
				
				CBSystemConstants.cardstatus = sysStatus;
				CBSystemConstants.cardbuildtype = sysBuild;
			}
		}).start();
	}
	public void initComponents(){
		setBounds(100, 100, 911, 477);
		this.setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		this.add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(new BorderLayout(0, 0));
		{
			JPanel panel = new JPanel();
			contentPanel.add(panel, BorderLayout.NORTH);
			panel.setLayout(new BorderLayout(0, 0));
			{
				JPanel panel_1 = new JPanel();
				panel.add(panel_1, BorderLayout.NORTH);
				panel_1.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_2 = new JPanel();
					{
						jButton10 = new JButton();
						jButton10.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/much.png")));
						jButton10.setToolTipText("导入文档");
						jButton10.setText("导入文档");
						jButton10.setMargin(new Insets(1, 1, 1, 1));
						jButton10.setFocusPainted(false);
						jButton10.setBorderPainted(false);
						jButton10.addActionListener(new java.awt.event.ActionListener(){
							@Override
							public void actionPerformed(ActionEvent evt) {
								// TODO Auto-generated method stub
								itButtonActionPerformed(evt);
							}
						});
						panel_2.add(jButton10);
					}
					panel_1.add(panel_2, BorderLayout.EAST);
					{
						jButton6 = new JButton();
				        jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/favorite.png"))); // NOI18N
				        jButton6.setText("典型票导入");
				        jButton6.setToolTipText("典型票导入");
				        jButton6.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton6.setBorderPainted(false);
				        jButton6.setFocusPainted(false);
				        jButton6.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				                jButton6ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton6);
					}
					{
						jButton7 = new JButton();
				        jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/history.png"))); // NOI18N
				        jButton7.setText("历史票导入");
				        jButton7.setToolTipText("历史票导入");
				        jButton7.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton7.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				                jButton7ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton7);
					}
				}
			}
			{
				jLabel1 = new JLabel();
				jLabel1.setText(" 操作任务：");
				panel.add(jLabel1, BorderLayout.WEST);
			}
			{
				jScrollPane2 = new JScrollPane();
				panel.add(jScrollPane2, BorderLayout.CENTER);
				{
					jTextArea1 = new JTextArea();
					jTextArea1.setColumns(20);
			        jTextArea1.setFont(new java.awt.Font("宋体", 1, 14)); // NOI18N
					jTextArea1.setPreferredSize(new Dimension(4, 60));
					jTextArea1.addMouseListener(new MouseAdapter() {
						@Override
						public void mouseClicked(MouseEvent e) {
							// TODO Auto-generated method stub
							Point mousepoint; 
							mousepoint =e.getPoint();
							Component texteditor=jTextArea1.findComponentAt(mousepoint);
							texteditor.requestFocusInWindow();
							Component c = texteditor.getComponentAt(0, 0);
						    if (c != null && c instanceof JTextComponent) {
						        ((JTextComponent) c).getCaretPosition();
						        jc=(JTextComponent)c;
						    }
						}
					});
				
					jScrollPane2.setViewportView(jTextArea1);
				}
			}
			{
				JPanel panel_1 = new JPanel();
				panel.add(panel_1, BorderLayout.SOUTH);
				panel_1.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_2 = new JPanel();
					panel_1.add(panel_2, BorderLayout.WEST);
					{
						jLabel2 = new JLabel("");
						panel_2.add(jLabel2);
					}
				}
				{
					JPanel panel_2 = new JPanel();
					panel_1.add(panel_2, BorderLayout.EAST);
					{
						jButton12 = new JButton();
						jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/check.png"))); // NOI18N
				        jButton12.setToolTipText("校验");
				        jButton12.setText("校验");
				        jButton12.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton12.setBorderPainted(false);
				        jButton12.setFocusPainted(false);
				        jButton12.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				            	jButton12ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton12);
					}
					{
						jButton2 = new JButton();
				        jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
				        jButton2.setText("增加");
				        jButton2.setToolTipText("增加");
				        jButton2.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton2.setFocusPainted(false);
				        jButton2.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				                jButton2ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton2);
					}
					{
						jButton3 = new JButton();
				        jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
				        jButton3.setText("删除");
				        jButton3.setToolTipText("删除");
				        jButton3.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton3.setFocusPainted(false);
				        jButton3.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				                jButton3ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton3);
					}
					{
						jButton9 = new JButton();
				        jButton9.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/replace.png"))); // NOI18N
				        jButton9.setText("替换");
				        jButton9.setToolTipText("替换");
				        jButton9.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton9.setBorderPainted(false);
				        jButton9.setFocusPainted(false);
				        jButton9.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				                jButton9ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton9);
					}
					{
						jButton1 = new JButton("\u4E0A\u79FB");
				        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
				        jButton1.setText("上移");
				        jButton1.setToolTipText("上移");
				        jButton1.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton1.setFocusPainted(false);
				        jButton1.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				                jButton1ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton1);
					}
					{
						jButton8 = new JButton();
				        jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
				        jButton8.setText("下移");
				        jButton8.setToolTipText("下移");
				        jButton8.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton8.setBorderPainted(false);
				        jButton8.setFocusPainted(false);
				        jButton8.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				                jButton8ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton8);
					}
					{
						jButton10 = new JButton();
				        jButton10.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/merge.png"))); // NOI18N
				        jButton10.setText("合项");
				        jButton10.setToolTipText("合项");
				        jButton10.setVisible(false);
				        jButton10.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton10.setBorderPainted(false);
				        jButton10.setFocusPainted(false);
				        jButton10.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				            	jButton10ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton10);
					}
					{
						jButton11 = new JButton("\u5206\u9879");
				        jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/split.png"))); // NOI18N
				        jButton11.setText("分项");
				        jButton11.setToolTipText("分项");
				        jButton11.setVisible(false);
				        jButton11.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton11.setBorderPainted(false);
				        jButton11.setFocusPainted(false);
				        jButton11.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				            	jButton11ActionPerformed(evt);
				            }
				        });
						panel_2.add(jButton11);
					}
				}
			}
		}
		{
			jScrollPane1 = new JScrollPane();
			contentPanel.add(jScrollPane1, BorderLayout.CENTER);
			{
				jTable1 = new JTable();
				jTable1.setRowHeight(24);
				/*jTable1.setModel(new DefaultTableModel(
					new Object[][] {
						{null, null, null},
						{null, null, null},
					},
					new String[] {
						"New column", "New column", "New column"
					}
				));*/
				jScrollPane1.setViewportView(jTable1);
			}
		}
		{
			JPanel buttonPane = new JPanel();
			buttonPane.setLayout(new FlowLayout(FlowLayout.RIGHT));
			this.add(buttonPane, BorderLayout.SOUTH);
			{
				jCheckBox1 = new JCheckBox();
				jCheckBox1.setForeground(new java.awt.Color(204, 0, 0));
		        jCheckBox1.setText("同时存为典型票");
				buttonPane.add(jCheckBox1);
			}
			{
				jButton5 = new JButton("\u4FDD\u5B58");
		        jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
		        jButton5.setText("正式保存");
		        jButton5.setToolTipText("正式保存");
		        jButton5.setMargin(new java.awt.Insets(1,1,1,1));
		        jButton5.setBorderPainted(false);
		        jButton5.setFocusPainted(false);
		        jButton5.addActionListener(new java.awt.event.ActionListener() {
		            public void actionPerformed(java.awt.event.ActionEvent evt) {
		                jButton5ActionPerformed(evt, true);
		            }
		        });
		        jButton5.setActionCommand("OK");
				//buttonPane.add(jButton5);
				//getRootPane().setDefaultButton(okButton);
			}
			
			{
				jButton13 = new JButton();
				jButton13.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
				jButton13.setText("保存");
				jButton13.setToolTipText("保存");
				jButton13.setMargin(new java.awt.Insets(1,1,1,1));
				jButton13.addActionListener(new java.awt.event.ActionListener() {
		            public void actionPerformed(java.awt.event.ActionEvent evt) {
		                jButton5ActionPerformed(evt, false);
		            }
		        });
				buttonPane.add(jButton13);
			}
			
			{
				jButton4 = new JButton();
		        jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
		        jButton4.setText("取消");
		        jButton4.setToolTipText("取消");
		        jButton4.setMargin(new java.awt.Insets(1,1,1,1));
		        jButton4.setBorderPainted(false);
		        jButton4.setFocusPainted(false);
		        jButton4.addActionListener(new java.awt.event.ActionListener() {
		            public void actionPerformed(java.awt.event.ActionEvent evt) {
		                jButton4ActionPerformed(evt);
		            }
		        });
		        jButton4.setActionCommand("Cancel");
				buttonPane.add(jButton4);
			}
		}
	}
	//典型票导入
	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
		addHD("0");
	}
	//历史票导入
	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {
		addHD("1");
	}
	//检查
    private void jButton12ActionPerformed(java.awt.event.ActionEvent evt) {
        // TODO add your handling code here:
    	if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
    	checkCard();
    }
	//增加
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.addTableRow(jTable1);
//		inittable();
	}
	//删除
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
//		if(!CheckWord.isCanDelete(jTable1, 1, 2))
//			return;
		WindowUtils.removeTableRow(jTable1);
//		inittable();
	}
	//替换
    private void jButton9ActionPerformed(java.awt.event.ActionEvent evt) {
        // TODO add your handling code here:
    	ReplaceWord rw = new ReplaceWord(SystemConstants.getMainFrame(), false, jTable1);
    	rw.setVisible(true);
    }
	//上移
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if(!CheckWord.isCanMoveUp(jTable1, 2))
			return;
		WindowUtils.moveupTableRow(jTable1);
	}
	//下移
	private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if(!CheckWord.isCanMoveDown(jTable1, 2))
			return;
		WindowUtils.movedownTableRow(jTable1);
	}
	//合项
    private void jButton10ActionPerformed(java.awt.event.ActionEvent evt) {
        // TODO add your handling code here:
    	WindowUtils.mergeTableRow(jTable1, 0);
    }
    //分项
    private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {
        // TODO add your handling code here:
    	WindowUtils.splitTableRow(jTable1, 0);
    }
  //保存
  	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt,boolean flag) {

		// TODO add your handling code here:
		if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
//		if(!checkCard(true)) {
//			ShowMessage.view(SystemConstants.getMainFrame(), "操作票未通过校验，保存失败！");
//			return;
//		}
		String czrw = jTextArea1.getText().trim();
		//String jxpdh = jTextField1.getText().trim();//检修票单号 刘志刚 2013-7-16
		if (czrw.equals("")) {
			ShowMessage.view(SystemConstants.getMainFrame(), "操作任务不能为空!");
			return;
		}
		int countRowsI = jTable1.getRowCount();
		String cardKind;
		if(Kindflag){
			cardKind="2";
		}else{
			cardKind="0";
		}
		String preStationName = "";
		List<CardItemModel> list = new ArrayList<CardItemModel>();
		if(!checkContent())
			return;
		for (int i = 0; i < countRowsI; i++) {
//			String cznr = jTable1.getValueAt(i, 2) == null?"":jTable1.getValueAt(i, 2).toString().trim();
//			if (cznr.equals(""))
//				continue;
			
			String temp= StringUtils.ObjToString(jTable1.getValueAt(i, 1)).trim();
		    String stationName = "";
		    if(!temp.equals("") && temp.indexOf("（")==-1) {
		    	stationName = temp;
		    }
		    else {
		    	stationName = preStationName;
		    }
		    if(jTable1.getValueAt(i, 0) !=null) {
				CardItemModel bcm = new CardItemModel();
				bcm = new CardItemModel();
				bcm.setCardNum(String.valueOf(i+1));
				bcm.setCardItem(jTable1.getValueAt(i, 0).toString().trim());
				bcm.setStationName(stationName);
				if(jTable1.getValueAt(i, 1) == null) {
					bcm.setShowName("");

				}else{
					bcm.setShowName(jTable1.getValueAt(i, 1).toString().trim());
				}
				if(jTable1.getValueAt(i, 2) == null) {
					bcm.setCardDesc("");
				}else {
					bcm.setCardDesc(jTable1.getValueAt(i, 2).toString().trim());
				}

//				bcm.setShowName(jTable1.getValueAt(i, 1).toString().trim());
//				bcm.setCardDesc(jTable1.getValueAt(i, 2).toString().trim());
				list.add(bcm);
		    }

			if(!cardKind.equals("1")&&!cardKind.equals("2")) {
				
				if(!preStationName.equals("") && !stationName.equals("") && !stationName.equals(preStationName))
					cardKind = "1";
			}
			if(!stationName.equals(""))
				preStationName = stationName;
		}
		if(CBSystemConstants.roleCode.equals("2"))
			cardKind = "3";
		TicketDBManager ticketDB = new TicketDBManager();
		Connection conn = null;
		if(CBSystemConstants.isOffline){
			conn=DBManager.getLOCConnection();
		}else{
			conn=DBManager.getConnection();
		}
		try {
			conn.setAutoCommit(false);
			String zbid;
			if(CBSystemConstants.getImportInfo()!=null && !CBSystemConstants.getImportInfo().equals("")){
				zbid=CBSystemConstants.getImportInfo();
			}else{
				zbid=StringUtils.getUUID();
			} 
			ticketDB.InsertTicketDB(conn,zbid, list, czrw, "0", cardKind, "2", "","");
			conn.commit();
			conn.close();
			
			//存储区域编码
			if(!CBSystemConstants.qybm.equals("")) {
				String sql = "update opcard.t_a_CZPZB t set t.AREANO='"+CBSystemConstants.qybm+"' where ZBID='"+zbid+"'";
				DBManager.execute(sql);
			}
			
			if (this.jCheckBox1.isSelected()) {
				ticketDB.InsertDXTicketDB(zbid);
			}
			if(flag){
				OMSService.getService().importOMS(zbid);
			}
			else
				ShowMessage.view(SystemConstants.getMainFrame(), "保存成功!");
		} catch (Exception e2) {
			// TODO: handle exception
			try {
				conn.rollback();
				conn.close();
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			ShowMessage.view(SystemConstants.getMainFrame(), "保存失败！");
			e2.printStackTrace();
		}
		this.setVisible(false);
		this.ptsgp=null;
		OperateTicketSGP.sgpTicket = null;
  	}
  	//取消
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.setVisible(false);
		this.ptsgp=null;
		OperateTicketSGP.sgpTicket = null;
		
	}
	//导入文档
	private void itButtonActionPerformed(java.awt.event.ActionEvent evt){
		int countRowsI = jTable1.getRowCount();
		DefaultTableModel model = (DefaultTableModel)jTable1.getModel();
		for(int i=countRowsI-1;i>=0;i--){
			model.removeRow(i);
		}
		HistoryCardEdit cardEdit = new HistoryCardEdit(SystemConstants.getMainFrame(),true);
		cardEdit.setVisible(true);
		cardEdit.setModal(true);
		List oldCards=cardEdit.getOldCards();
        for (int i = 0; i < oldCards.size(); i++) {
            String[] card = (String[])oldCards.get(i);
            String[] outCont=new String[]{String.valueOf(i+1),card[0],card[1]}; //"顺序","操作单位","操作内容""
//            Kindflag = true;
            model.addRow(outCont);
       }
		
	}
	private void addHD(String flag){

		// TODO add your handling code here:
		OperateTicketMLPLoad mlpld = new OperateTicketMLPLoad(
				SystemConstants.getMainFrame(), true, flag);
		mlpld.setVisible(true);
		
		if(mlpld.getMXS()!=null&&mlpld.getMXS().size()>0){
			int trc = jTable1.getSelectedRowCount();
			int dlh = mlpld.getMXS().size()/2;//导入的行数
			int kh = 0;
			if(trc>0){
				for(int i =0;i<dlh;i++){
						WindowUtils.addTableRow(jTable1);
				}
			}else{
				for(int i = jTable1.getRowCount()-1;i>=0;i--){
					if((jTable1.getValueAt(i, 1)==null||jTable1.getValueAt(i, 1).equals(""))&&
							(jTable1.getValueAt(i, 2)==null||jTable1.getValueAt(i, 2).equals(""))){
								kh++;
					}else{
						break;
					}
				}
					for(int i = 0;i<dlh-kh;i++){
						WindowUtils.addTableRow(jTable1);
					}
			}
			for(int i =0;i<dlh;i++){
				int row = 0;
				if(trc>0){
					row = jTable1.getSelectedRow()+trc+i;
				}else{
					int ch = dlh-kh>0?dlh-kh:0;
					row =jTable1.getRowCount()-kh-ch+i;
				}
					jTable1.setValueAt(mlpld.getMXS().get(2*i),row,1);
					jTable1.setValueAt(mlpld.getMXS().get(2*i+1),row,2);
			}
		}else{
			CodeNameModel cnm = mlpld.getMLP();
			this.initTable(cnm);
		}
	
	}
	private JButton jButton1;
	private JButton jButton2;
	private JButton jButton3;
	private JButton jButton4;
	private JButton jButton5;
	private JButton jButton6;
	private JButton jButton7;
	private JButton jButton8 ;
	private JButton jButton9;
	private JButton jButton10;
	private JButton jButton11;
	private JButton jButton12;
	private JButton jButton13;
    private JLabel jLabel1;
    private JLabel jLabel2;
	private JTextArea jTextArea1;
	private JScrollPane jScrollPane1;
    private JScrollPane jScrollPane2;
	private JCheckBox jCheckBox1;
}
