package com.tellhow.czp.operationcard;

import java.awt.Color;
import java.awt.Component;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.TableCellRenderer;
import javax.swing.table.TableColumn;
import javax.swing.table.TableColumnModel;
/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 作    者: 王豪
 * 开发日期: 2014年01月08日 上午8:50:14 
 */
public class TextAreaRenderer extends JTextArea implements TableCellRenderer{
	private final DefaultTableCellRenderer adaptee =
		   new DefaultTableCellRenderer();
	/** map from table to map of rows to map of column heights */
	private final Map cellSizes = new HashMap();
	public TextAreaRenderer() {
		 setLineWrap(true);
		 setWrapStyleWord(true);
	}
	@Override
	public Component getTableCellRendererComponent(JTable table, Object value,
			boolean isSelected, boolean hasFocus, int row, int column) {
		// TODO Auto-generated method stub
		if (row % 2 == 0){
        	setBackground(Color.white); // 设置奇数行底色
 		  	setForeground(Color.black);
 	   	}else if ((row % 2 == 1)){
 		   	setBackground(new Color(206, 231, 255)); // 设置偶数行底色
 		  	setForeground(Color.black);
 	   	}
	    setBorder(adaptee.getBorder());
	    setFont(adaptee.getFont());
	    setText(adaptee.getText());
	    // This line was very important to get it working with JDK1.4
	    TableColumnModel columnModel = table.getColumnModel();
	    setSize(columnModel.getColumn(column).getWidth(), 100000);
	    int height_wanted = (int) getPreferredSize().getHeight();
	    addSize(table, row, column, height_wanted);
	    height_wanted = findTotalMaximumRowSize(table, row);
	    if (height_wanted != table.getRowHeight(row)) {
	      table.setRowHeight(row, 30);
	    }
	    return this;
	}
   private void addSize(JTable table, int row, int column,int height) {
		 Map rows = (Map) cellSizes.get(table);
		 if (rows == null) {
			 cellSizes.put(table, rows = new HashMap());
		 }
		 Map rowheights = (Map) rows.get(new Integer(row));
		 if (rowheights == null) {
			 rows.put(new Integer(row), rowheights = new HashMap());
		 }
		 rowheights.put(new Integer(column), new Integer(height));
	}
   /**
    * Look through all columns and get the renderer.  If it is
    * also a TextAreaRenderer, we look at the maximum height in
    * its hash table for this row.
    */
   private int findTotalMaximumRowSize(JTable table, int row) {
     int maximum_height = 0;
     Enumeration columns = table.getColumnModel().getColumns();
     while (columns.hasMoreElements()) {
       TableColumn tc = (TableColumn) columns.nextElement();
       TableCellRenderer cellRenderer = tc.getCellRenderer();
       if (cellRenderer instanceof TextAreaRenderer) {
         TextAreaRenderer tar = (TextAreaRenderer) cellRenderer;
         maximum_height = Math.max(maximum_height,
             tar.findMaximumRowSize(table, row));
       }
     }
     return maximum_height;
   }
   private int findMaximumRowSize(JTable table, int row) {
     Map rows = (Map) cellSizes.get(table);
     if (rows == null) return 0;
     Map rowheights = (Map) rows.get(new Integer(row));
     if (rowheights == null) return 0;
     int maximum_height = 0;
     for (Iterator it = rowheights.entrySet().iterator();
          it.hasNext();) {
       Map.Entry entry = (Map.Entry) it.next();
       int cellHeight = ((Integer) entry.getValue()).intValue();
       maximum_height = Math.max(maximum_height, cellHeight);
     }
     return maximum_height;
   }
}
