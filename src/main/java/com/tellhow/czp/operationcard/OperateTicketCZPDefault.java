package com.tellhow.czp.operationcard;

import java.awt.BorderLayout;
import java.awt.Font;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.swing.JButton;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;

import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.dao.TicketManager;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.DateUtil;

import czprule.datemodule.JCalendarPanel;
import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;
/**
 * @典型票
 * <AUTHOR>
 * 修改：张余平
 * 界面修改：王豪
 */
public class OperateTicketCZPDefault extends OperateTicketCZP {
	private SetJTableProtery sjp = new SetJTableProtery();
	private final JPanel contentPanel = new JPanel();
	private JTextField jTextField1;
	private JTextField jTextField2;
	private JTextField jTextField3;
	private JTable jTable1;

	/**
	 * Launch the application.
	 */
/*	public static void main(String[] args) {
		try {
			OperateTicketDXP dialog = new OperateTicketDXP();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}*/

	/** Creates new form DXPOperateTicket */
	public OperateTicketCZPDefault() {
		initComponents();
		Date today = new Date();
		DateUtil dateUtil = new DateUtil();
		jTextField1.setText("");
		jTextField2.setText(dateUtil.getCurTime("yyyy-MM-dd"));
		initDXP();
		this.setVisible(true);
	}
	public void initComponents(){
		//setBounds(100, 100, 599, 392);
		this.setLayout(new BorderLayout());
		setBackground(new java.awt.Color(244, 243, 243));
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		this.add(contentPanel, BorderLayout.CENTER);
		contentPanel.setBackground(new java.awt.Color(244, 243, 243));
		contentPanel.setLayout(new BorderLayout(0, 0));
		{
			JPanel panel = new JPanel();
			panel.setBackground(new java.awt.Color(244, 243, 243));
			contentPanel.add(panel, BorderLayout.NORTH);
			panel.setLayout(new BorderLayout(0, 0));
			{
				JPanel panel_1 = new JPanel();
				panel_1.setBackground(new java.awt.Color(244, 243, 243));
				panel.add(panel_1, BorderLayout.NORTH);
				panel_1.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_2 = new JPanel();
					panel_2.setBackground(new java.awt.Color(244, 243, 243));
					panel_1.add(panel_2, BorderLayout.WEST);
					{
						jLabel1 = new JLabel("\u5F00\u7968\u65F6\u95F4\uFF1A");
						panel_2.add(jLabel1);
					}
					{
						jTextField1 = new JTextField();
						panel_2.add(jTextField1);
						jTextField1.setColumns(16);
						jTextField1.setPreferredSize(new java.awt.Dimension(6, 26));
						jTextField1.addMouseListener(new java.awt.event.MouseAdapter() {
							public void mouseClicked(java.awt.event.MouseEvent evt) {
								beginTime(evt);
							}
						});
					}
					{
						jLabel2 = new JLabel("\u81F3\uFF1A");
						panel_2.add(jLabel2);
					}
					{
						jTextField2 = new JTextField();
						panel_2.add(jTextField2);
						jTextField2.setColumns(16);
						jTextField2.setPreferredSize(new java.awt.Dimension(6, 26));
						jTextField2.addMouseListener(new java.awt.event.MouseAdapter() {
							public void mouseClicked(java.awt.event.MouseEvent evt) {
								endTime(evt);
							}
						});
					}
				}
			}
			{
				JPanel panel_1 = new JPanel();
				panel_1.setBackground(new java.awt.Color(244, 243, 243));
				panel.add(panel_1, BorderLayout.WEST);
				{
					jLabel3 = new JLabel("\u64CD\u4F5C\u4EFB\u52A1\uFF1A");
					panel_1.add(jLabel3);
				}
				{
					jTextField3 = new JTextField();
					panel_1.add(jTextField3);
					jTextField3.setColumns(39);
					jTextField3.setMinimumSize(new java.awt.Dimension(6, 26));
					jTextField3.setPreferredSize(new java.awt.Dimension(6, 26));
				}
				{
					jButton1 = new JButton("");
					jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/icons/query.gif"))); // NOI18N
					jButton1.setToolTipText("\u67e5\u8be2");
					jButton1.setBorder(null);
					jButton1.setFocusPainted(false);
					jButton1.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton1ActionPerformed(evt);
						}
					});
					panel_1.add(jButton1);
				}
			}
			{
				JPanel panel_1 = new JPanel();
				panel_1.setBackground(new java.awt.Color(244, 243, 243));
				panel.add(panel_1, BorderLayout.EAST);
				
				{
					jButtonshuaxin = new JButton();
					jButtonshuaxin.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/replace.gif"))); // NOI18N
					jButtonshuaxin.setText("刷新");
					jButtonshuaxin.setToolTipText("刷新");
					jButtonshuaxin.setMargin(new java.awt.Insets(1,1,1,1));
					jButtonshuaxin.setFocusPainted(false);
					jButtonshuaxin.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton1ActionPerformed(evt);
						}
					});
					panel_1.add(jButtonshuaxin);
				}
				
				{
					jButton2 = new JButton();
					jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/delete.png"))); // NOI18N
					jButton2.setText("删除");
					jButton2.setToolTipText("删除");
					jButton2.setMargin(new java.awt.Insets(1,1,1,1));
					jButton2.setFocusPainted(false);
					jButton2.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton2ActionPerformed(evt);
						}
					});
					panel_1.add(jButton2);
				}
				
				{
					jButton3 = new JButton();
					jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/edit.png"))); // NOI18N
					jButton3.setText("修改");
					jButton3.setToolTipText("修改");
					jButton3.setMargin(new java.awt.Insets(1,1,1,1));
					jButton3.setFocusPainted(false);
					jButton3.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton3ActionPerformed(evt);
						}
					});
					panel_1.add(jButton3);
				}
				
				/*
				{
					jButton4 = new JButton();
					jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/import.gif"))); // NOI18N
					jButton4.setText("导入OMS");
					jButton4.setToolTipText("导入OMS");
					jButton4.setMargin(new java.awt.Insets(1,1,1,1));
					jButton4.setFocusPainted(false);
					jButton4.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton4ActionPerformed(evt);
						}
					});
					panel_1.add(jButton4);
				}
				*/
				
				{
					jButton5 = new JButton();
					jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/network.gif"))); // NOI18N
					jButton5.setText("旧票新编");
					jButton5.setToolTipText("旧票新编");
					jButton5.setMargin(new java.awt.Insets(1,1,1,1));
					jButton5.setFocusPainted(false);
					jButton5.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton5ActionPerformed(evt);
						}
					});
					panel_1.add(jButton5);
				}
				{
					JButton jButton12 = new JButton("关闭");
					jButton12.setToolTipText("关闭");
					jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource(
					"/tellhow/btnIcon/back.gif"))); // NOI18N
					jButton12.setMargin(new java.awt.Insets(1,1,1,1));
					jButton12.setFocusPainted(false);
				    jButton12.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				            	jButton14ActionPerformed(evt);
				            }
				        });
					panel_1.add(jButton12,BorderLayout.EAST);
				}
				
				/*
				{
					jButton6 = new JButton();
					jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/save.png"))); // NOI18N
					jButton6.setText("保存为文件");
					jButton6.setToolTipText("保存为文件");
					jButton6.setMargin(new java.awt.Insets(1,1,1,1));
					jButton6.setFocusPainted(false);
					jButton6.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton6ActionPerformed(evt);
						}
					});
					panel_1.add(jButton6);
				}
				*/
			}
		}
		{
			jScrollPane1 = new JScrollPane();
			jScrollPane1.getViewport().setBackground(new java.awt.Color(244, 243, 243));
			contentPanel.add(jScrollPane1, BorderLayout.CENTER);
			{
				jTable1 = new JTable();
				jTable1.setFont(new java.awt.Font("宋体", 0, 13));
				jTable1.setModel(new javax.swing.table.DefaultTableModel(
						new Object[][] { { null, null, null, null },
								{ null, null, null, null }, { null, null, null, null },
								{ null, null, null, null } }, new String[] { "Title 1",
								"Title 2", "Title 3", "Title 4" }));
				jTable1.setRowHeight(26);
				jTable1.addMouseListener(new java.awt.event.MouseAdapter() {
					public void mouseClicked(java.awt.event.MouseEvent evt) {
						jTable1MouseClicked(evt);
					}
				});
				/*jTable1.setModel(new DefaultTableModel(
					new Object[][] {
						{null, null, null},
						{null, null, null},
					},
					new String[] {
						"New column", "New column", "New column"
					}
				));*/
				jScrollPane1.setViewportView(jTable1);
			}
		}
	
	}
	/**
	 * @典型票初始化
	 */
	public void initDXP() {
		Object[][] tableData = null;
		DefaultTableModel jTableModel = new DefaultTableModel(tableData,
				new String[] { "序 号", "操作任务", "拟票时间" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		TicketManager tdb = new TicketManager();
		List<String[]> results = tdb.queryDXTicketZB(jTextField1.getText()
				.trim(), jTextField2.getText().trim(), jTextField3.getText()
				.trim(),CBSystemConstants.opCode);
		String[] tempStr = null;
		//[主表ID、操作任务、拟票人、拟票时间]
		for (int i = 0; i < results.size(); i++) {
			tempStr = results.get(i);
			Object[] rowData = {
					new CodeNameModel(tempStr[0],String.valueOf(i + 1)),
					tempStr[1], tempStr[3] };
			jTableModel.addRow(rowData);
		}
		List<Integer> columnList = new ArrayList<Integer>();
		columnList.add(0);
		columnList.add(2);
		jTable1.setModel(jTableModel);
		sjp.makeFaceCenter(jTable1,columnList);
		sjp.getTableHeader(jTable1);//列名居中
		jTable1.setFont(new Font("宋体",Font.PLAIN,14)); // NOI18N	
        jTable1.setRowHeight(30);
		sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);//单元格内容居左
		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMaxWidth(60);
		tcm.getColumn(2).setMinWidth(180);
		tcm.getColumn(2).setMaxWidth(200);
	}

	//查询
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		initDXP();
	}
	//删除
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view("请选择需要删除的记录!");
			return;
		}
		int ok = JOptionPane.showConfirmDialog(this, "删除后不能恢复，你确定要删除吗？",
				"操作票提示框", JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.NO_OPTION) {
			return;
		}
		jTable1.removeEditor();
		TicketDBManager tdb = new TicketDBManager();
		for (int i = selectRows.length - 1; i >= 0; i--) {
			CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
					selectRows[i], 0);

			tdb.delTicket(cnm.getCode());
		}
		initDXP();
	}
	//修改
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要编辑的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 0);
		String czrw = jTable1.getValueAt(selectRows[0], 1).toString().trim();
		OperateTicketDXPMX pot = OperateTicketDXPMX.getInstance();
		pot.init(SystemConstants.getMainFrame(), new CodeNameModel(cnm.getCode(), czrw));
		pot.setVisible(true);
		initDXP();
	}
	//导入OMS
//	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
//		
//	}
	//手工拟票
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要编辑的操作票");
			return;
		}
    	Object codename= jTable1.getValueAt(selectRows[0], 0);
    	Object codename1= jTable1.getValueAt(selectRows[0], 1);
    	CodeNameModel cnm = new CodeNameModel();
    	
    	if(codename instanceof CodeNameModel){
    		cnm.setCode(((CodeNameModel) codename).getCode());
    		String usecodename=codename1.toString();
    		cnm.setName(usecodename);
    	}
    	
    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.0);
    	OperateTicketSGP otsgp = OperateTicketSGP.getInstance();
    	splitPane.setRightComponent(otsgp);
    	otsgp.initTable(cnm);
		this.setVisible(false);
	}
	//保存为文件
//	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
//		
//	}
	//起始时间查询
	private void beginTime(java.awt.event.MouseEvent evt) {
		// TODO add your handling code here:
		String selectTime = "";
		if (evt.getButton() == 1 && evt.getClickCount() == 2) {
			JCalendarPanel calendarPanel = new JCalendarPanel(
					jTextField1.getX() + 12, jTextField1.getY() + 19);
			selectTime = calendarPanel.getDateStr();
		}
		if (!selectTime.equals("")) {
			String[] beginTime = selectTime.split(" ");
			jTextField1.setText(beginTime[0]);
		}
	}
	//结束时间查询
	private void endTime(java.awt.event.MouseEvent evt) {
		// TODO add your handling code here:
		String selectTime = "";
		if (evt.getButton() == 1 && evt.getClickCount() == 2) {
			JCalendarPanel calendarPanel = new JCalendarPanel(
					jTextField2.getX() + 12, jTextField2.getY() + 19);
			selectTime = calendarPanel.getDateStr();
		}
		if (!selectTime.equals("")) {
			String[] beginTime = selectTime.split(" ");
			jTextField2.setText(beginTime[0]);
		}
	}
	//表单双击展开明细表
	private void jTable1MouseClicked(java.awt.event.MouseEvent evt) {
		// TODO add your handling code here:
		if (evt.getClickCount() == 2) {
			CodeNameModel cn = (CodeNameModel) jTable1.getValueAt(
					jTable1.rowAtPoint(evt.getPoint()), 0);
			String czrw = jTable1
					.getValueAt(jTable1.rowAtPoint(evt.getPoint()), 1)
					.toString().trim();
			CodeNameModel cnm = new CodeNameModel(cn.getCode(), czrw);
			OperateTicketDXPMX pot = OperateTicketDXPMX.getInstance();
			pot.init(SystemConstants.getMainFrame(), cnm);
			pot.setVisible(true);
		}
	}
    private void jButton14ActionPerformed(java.awt.event.ActionEvent evt){
    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.99999);
		splitPane.setOneTouchExpandable(true);
		splitPane.setDividerSize(12);
		CBSystemConstants.svgAddPd = null;
    }
	private JButton jButton1;
	private JButton jButton2;
	private JButton jButton3;
//	private JButton jButton4;
	private JButton jButton5;
	private JButton jButtonshuaxin;
//	private JButton jButton6;
	private JLabel jLabel1;
	private JLabel jLabel2;
	private JLabel jLabel3;
	private JScrollPane jScrollPane1;
	//private javax.swing.JTextField jTextField1;
	//private javax.swing.JTextField jTextField2;
	//private javax.swing.JTextField jTextField3;
}
