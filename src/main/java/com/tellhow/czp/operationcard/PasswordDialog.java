package com.tellhow.czp.operationcard;

import java.awt.Toolkit;
import java.util.List;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JOptionPane;

import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.user.User;
import com.tellhow.czp.user.UserDao;
import com.tellhow.graphicframework.utils.DateUtil;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;

/**
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class PasswordDialog extends javax.swing.JDialog {
    
	private boolean isEnterSystem=false;
	private User user =null;
	private DateUtil jdu = new DateUtil();
	
	
    public PasswordDialog(java.awt.Frame parent, boolean Model,CodeNameModel cnm) {
        super(parent, Model);
        initComponents();
        this.setLocationCenter();
        this.cnm=cnm;
        initUserList();
        this.setTitle(CBSystemConstants.SYSTEM_TITLE);
    }
    
    private void initComponents() {

        jLabel1 = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        cb_UserName = new JComboBox();
        jPasswordField1 = new javax.swing.JPasswordField();
        jButton1 = new javax.swing.JButton();
        jButton2 = new javax.swing.JButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

        jLabel1.setText("登录用户：");

        jLabel2.setText("登录密码：");

        jPasswordField1.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                EnterAction(evt);
            }
        });

        jButton1.setText("审 核");
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        jButton2.setText("取 消");
        jButton2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                    .add(layout.createSequentialGroup()
                        .add(50, 50, 50)
                        .add(jButton1)
                        .add(29, 29, 29)
                        .add(jButton2))
                    .add(layout.createSequentialGroup()
                        .addContainerGap()
                        .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.TRAILING)
                            .add(jLabel1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 60, Short.MAX_VALUE)
                            .add(org.jdesktop.layout.GroupLayout.LEADING, jLabel2))
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.UNRELATED)
                        .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING, false)
                            .add(jPasswordField1)
                            .add(cb_UserName, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 143, Short.MAX_VALUE))))
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .addContainerGap()
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                    .add(jLabel1)
                    .add(cb_UserName))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.UNRELATED)
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                    .add(jLabel2)
                    .add(jPasswordField1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 16, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
                .add(18, 18, 18)
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                    .add(jButton2)
                    .add(jButton1))
                .addContainerGap(org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
        );
        pack();
    }
    
    /**
     * 初始化用户下拉列表
     */
    private void initUserList(){
    	dcbUser=new DefaultComboBoxModel();
		UserDao userdao = new UserDao();
		List<User> allusers = userdao.getAllUser();
		for (int i = 0; i < allusers.size(); i++) {
			user = allusers.get(i);
			dcbUser.addElement(user);
		}
        cb_UserName.setModel(dcbUser);
    }

    private void EnterAction(java.awt.event.KeyEvent evt) {
         if(evt.getKeyCode() == evt.VK_ENTER){
        	 if(isEnterSystem=validateLogin()){
        		 this.closeDialog();
             }
         }
    }
    
    /**
     * 确定按钮
     * @param evt
     */
    private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
    	if(isEnterSystem=validateLogin()){
    		this.closeDialog();
        }
    }
    
    /**
     * 验证用户登入信息
     */
    private boolean validateLogin(){
        char[] a_password = jPasswordField1.getPassword();
        String password = new String(a_password);
        User user = (User)cb_UserName.getSelectedItem();
        
        if(user==null){
        	JOptionPane.showMessageDialog(this, "用户名不能为空！",CBSystemConstants.SYSTEM_TITLE,javax.swing.JOptionPane.INFORMATION_MESSAGE);
            return false;
        }else if(password == null || password.equals("")){
        	JOptionPane.showMessageDialog(this, "密码不能为空！",CBSystemConstants.SYSTEM_TITLE,javax.swing.JOptionPane.INFORMATION_MESSAGE);
            return false;
        }else{
            if(user.getPassword().equals(tbp.common.util.StringUtil.getMD5(password))){
            	this.user=user;
            	if(cnm!=null)
            	   insert(user);//插入审核人
                return true;
            }else{
            	JOptionPane.showMessageDialog(this, "密码不正确！",CBSystemConstants.SYSTEM_TITLE,javax.swing.JOptionPane.INFORMATION_MESSAGE);
            	jPasswordField1.setText("");
            }
        }
        return false;
    }
    
    private void insert(User user){
    	TicketDBManager tdb=new TicketDBManager();
    	tdb.updateShr(user,cnm);//插入审核人
    }

    
    /**
     * 取消按钮
     * @param evt
     */
    private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
        this.closeDialog();
    }
    /**
     * 关闭对话框
     */
    private void closeDialog() {
        this.setVisible(false);
        this.dispose();
    }
    
    public User openDialog(){
    	 jPasswordField1.requestFocus();
         this.setVisible(true);
         return user;
    }
    
    /**
     * 屏幕中央位置
     */
    public void setLocationCenter() {
        int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
        int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
        this.setLocation((w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
    }
    
    public  javax.swing.JButton jButton1;
    private javax.swing.JButton jButton2;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private JComboBox cb_UserName;     //用户名下拉框
    private DefaultComboBoxModel dcbUser;
    private javax.swing.JPasswordField jPasswordField1;
    private CodeNameModel cnm=null;
    
}
