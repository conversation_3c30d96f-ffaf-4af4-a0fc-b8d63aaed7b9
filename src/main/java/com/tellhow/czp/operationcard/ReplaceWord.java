package com.tellhow.czp.operationcard;

import java.awt.Toolkit;

import javax.swing.JTable;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.mainframe.JPopupTextField;

import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-10-22 下午01:49:35 
 */
public class ReplaceWord extends javax.swing.JDialog {
    javax.swing.JTable jTable;
    javax.swing.JTextArea jTextArea = null;
    /** Creates new form ReplaceWord */
    
    public ReplaceWord(javax.swing.JDialog parent, boolean modal,javax.swing.JTable jTable) {     
        super(parent, modal);
        this.jTable=jTable;
        initComponents();
        this.setLocationCenter();
        this.setTitle("内容替换");
    }
    public ReplaceWord(javax.swing.JFrame parent, boolean modal,javax.swing.JTable jTable) {     
        super(parent, modal);
        this.jTable=jTable;
        initComponents();
        this.setLocationCenter();
        this.setTitle("内容替换");
    }
    public ReplaceWord(javax.swing.JFrame parent, boolean modal,javax.swing.JTable jTable,javax.swing.JTextArea jTextArea) {     
        super(parent, modal);
        this.jTable=jTable;
        this.jTextArea=jTextArea;
        initComponents();
        this.setLocationCenter();
        this.setTitle("内容替换");
    }
    public void replaceContents(){
      
        String srcStr=this.JPopupTextField1.getText();   //被替换内容
        String tagStr=this.JPopupTextField2.getText();   //替换内容 即最终内容    
//        replaceStr[2]=this.actionStr;
        
        if(srcStr.equals("")){
            javax.swing.JOptionPane.showMessageDialog(this, "替换内容为空！", CBSystemConstants.SYSTEM_TITLE, 1);
            return;
        }
        if(jTable.isEditing()){
        	jTable.getCellEditor().stopCellEditing();
        }
        boolean isSearch = false;

        DefaultTableModel tableModel=(DefaultTableModel)jTable.getModel();
        for(int j = 0; j < tableModel.getColumnCount(); j++)
        {
	        for (int i = 0; i < tableModel.getRowCount(); i++) {
	            String rowValue = String.valueOf(tableModel.getValueAt(i, j));
	            if (!rowValue.equals("")||!(rowValue.equals("null"))) {
	
	                if (rowValue.indexOf(srcStr) >=0) {
	                    isSearch = true;
	                    rowValue = rowValue.replaceAll(srcStr.trim(), tagStr.trim());                    
	                    tableModel.setValueAt(rowValue, i, j);  //将替换后的行存入表数据模型中
	                }
	            }
	        }
        }
        if(jTextArea != null) {
        	if(jTextArea.getText().contains(srcStr)) {
        		jTextArea.setText(jTextArea.getText().replace(srcStr.trim(), tagStr.trim()));
        		isSearch = true;
        	}
        }
        if (!isSearch)
            javax.swing.JOptionPane.showMessageDialog(this, "没有找到要替换的内容！", CBSystemConstants.SYSTEM_TITLE, 1);
    }
    
        /**
     * 屏幕中央位置
     */
    public void setLocationCenter() {
        int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
        int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
        this.setLocation((w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
    }
    /** This method is called from within the constructor to
     * initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is
     * always regenerated by the Form Editor.
     */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jLabel1 = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        JPopupTextField1 = new JPopupTextField();
        JPopupTextField2 = new JPopupTextField();
        jButton1 = new javax.swing.JButton();
        jButton2 = new javax.swing.JButton();
        jButton3 = new javax.swing.JButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

        jLabel1.setText(" 查找内容：");

        jLabel2.setText(" 替 换 为：");

        jButton1.setText("全部替换");
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        jButton2.setText("取   消");
        jButton2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });

        jButton3.setText("替    换");
        jButton3.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton3ActionPerformed(evt);
            }
        });

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .addContainerGap()
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                    .add(layout.createSequentialGroup()
                        .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                            .add(jLabel2)
                            .add(jLabel1))
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                            .add(JPopupTextField2, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 198, Short.MAX_VALUE)
                            .add(JPopupTextField1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 198, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                            .add(layout.createSequentialGroup()
                                .add(jButton1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED))
                            .add(jButton3)).addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED))
                    .add(org.jdesktop.layout.GroupLayout.TRAILING, jButton2))
                .addContainerGap())
        );

        layout.linkSize(new java.awt.Component[] {jLabel1, jLabel2}, org.jdesktop.layout.GroupLayout.HORIZONTAL);

        layout.linkSize(new java.awt.Component[] {jButton1, jButton2}, org.jdesktop.layout.GroupLayout.HORIZONTAL);

        layout.linkSize(new java.awt.Component[] {JPopupTextField1, JPopupTextField2}, org.jdesktop.layout.GroupLayout.HORIZONTAL);

        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .add(18, 18, 18)
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                    .add(jLabel1)
                    .add(JPopupTextField1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                    .add(jButton3))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                    .add(jLabel2)
                    .add(JPopupTextField2, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                    .add(jButton1))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jButton2)
                .addContainerGap(23, Short.MAX_VALUE))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents
 
    /**
     * 全部替换
     * @param evt
     */
    private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton1ActionPerformed
        //全部替换
        this.replaceContents();
//        this.setVisible(false);
//        this.dispose();
    }//GEN-LAST:event_jButton1ActionPerformed

    private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton2ActionPerformed
         //取消操作
         this.setVisible(false);
         this.dispose();
    }//GEN-LAST:event_jButton2ActionPerformed

    /**
     * 替换
     * @param evt
     */
    private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton3ActionPerformed
        // TODO add your handling code here:
        String srcStr=this.JPopupTextField1.getText();   //被替换内容
        String tagStr=this.JPopupTextField2.getText();   //替换内容 即最终内容    
//        replaceStr[2]=this.actionStr;
        
        if(srcStr.equals("")){
            javax.swing.JOptionPane.showMessageDialog(this, "替换内容为空！", CBSystemConstants.SYSTEM_TITLE, 1);
            return;
        }
        if(jTable.isEditing()){
        	jTable.getCellEditor().stopCellEditing();
        }
        boolean isSearch = false;
        
        DefaultTableModel tableModel=(DefaultTableModel)jTable.getModel();
        for(int j = 0; j < tableModel.getColumnCount(); j++)
        {
	        for (int i = 0; i < tableModel.getRowCount(); i++) {
	        	if(jTable.isRowSelected(i)) {
		            String rowValue = String.valueOf(tableModel.getValueAt(i, j));
		            if (!rowValue.equals("")||!(rowValue.equals("null"))) {
		                if (rowValue.indexOf(srcStr) >=0) {
		                    isSearch = true;
		                    rowValue = rowValue.replaceAll(srcStr.trim(), tagStr.trim());                    
		                    tableModel.setValueAt(rowValue, i, j);  //将替换后的行存入表数据模型中
		                }
		            }
	        	}
	        }
        }
        if (!isSearch)
            javax.swing.JOptionPane.showMessageDialog(this, "没有找到要替换的内容！", CBSystemConstants.SYSTEM_TITLE, 1);
    }//GEN-LAST:event_jButton3ActionPerformed
    
    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                ReplaceWord dialog = new ReplaceWord(new javax.swing.JDialog(), true,new JTable());
                dialog.addWindowListener(new java.awt.event.WindowAdapter() {
                    public void windowClosing(java.awt.event.WindowEvent e) {
                        System.exit(0);
                    }
                });
                dialog.setVisible(true);
            }
        });
    }
    
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton jButton1;
    private javax.swing.JButton jButton2;
    private javax.swing.JButton jButton3;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private JPopupTextField JPopupTextField1;
    private JPopupTextField JPopupTextField2;
    // End of variables declaration//GEN-END:variables
    
}
