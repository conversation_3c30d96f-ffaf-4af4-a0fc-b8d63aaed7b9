package com.tellhow.czp.operationcard;

import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

import com.tellhow.czp.app.CZPImpl;

import czprule.model.CodeNameModel;

public abstract class OperateTicketMLPMX extends javax.swing.JDialog {
	protected static OperateTicketMLPMX ticketMLPMX;
	public static OperateTicketMLPMX getInstance(){
	if (ticketMLPMX == null) {
		if(CZPImpl.getInstance("OperateTicketMLPMX") !=null)
			ticketMLPMX=(OperateTicketMLPMX)CZPImpl.getInstance("OperateTicketMLPMX");
		if(ticketMLPMX == null)
			ticketMLPMX = new OperateTicketMLPMXDefault();
			ticketMLPMX.addWindowListener(new WindowAdapter() {
				public void windowClosing(WindowEvent we) {
					ticketMLPMX.dispose();
			    	 ticketMLPMX = null;
			     }
			});
		return ticketMLPMX;
		}
		else
			return ticketMLPMX;
	}
	public OperateTicketMLPMX(java.awt.Frame parent, String string,
			boolean b) {
		super(parent, string, b);
	}
	public abstract void init(CodeNameModel cnm,String[] cards);
}
