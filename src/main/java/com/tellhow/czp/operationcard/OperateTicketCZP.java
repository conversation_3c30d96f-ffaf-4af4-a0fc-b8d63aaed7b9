package com.tellhow.czp.operationcard;

import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

import javax.swing.JPanel;

import com.tellhow.czp.app.CZPImpl;

public abstract class OperateTicketCZP  extends JPanel {
	public static OperateTicketCZP instance;
	public static OperateTicketCZP getInstance(){
		if(instance == null){
			instance=(OperateTicketCZP)CZPImpl.getInstance("OperateTicketCZP");
			if(instance == null)
				instance = new OperateTicketCZPDefault();
				return instance;
		}
		else
			return instance;
	}
	
	public abstract void initDXP();
}

