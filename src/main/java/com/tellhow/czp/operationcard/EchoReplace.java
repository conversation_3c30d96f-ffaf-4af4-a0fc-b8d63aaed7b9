package com.tellhow.czp.operationcard;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/**
 * 异常警告过滤及获取
 * 
 * <AUTHOR>
 * 
 */
public class EchoReplace {
	/**
	 * 获取异常警告消息
	 * 
	 * @param msg
	 * @param pd
	 * @return
	 */
	public Map<String,String> getEchoMap(CheckMessage msg, PowerDevice pd) {
		Map<String,String> resultMap = new HashMap<String, String>();
		List<Map<String,String>> list = new ArrayList<Map<String,String>>();
		String sql = "";
		
		if(CBSystemConstants.opcardUser.equals("OPCARDKM.")){
			sql = "SELECT ECHO_NAME NAME,ECHO_MESSAGE MSG,ECHO_CODE CODE,ECHO_KIND KIND FROM "+CBSystemConstants.opcardUser+"T_A_RULE_ECHO WHERE ECHO_ID = '"
					+ msg.getBottom() + "'";
			
			list = DBManager.query(sql);
			
			if (list.size() < 1){
				resultMap.put("msg", "此操作将导致异常");
				resultMap.put("code", "1");
				resultMap.put("kind", "2");
				return resultMap;
			}
		}else{
			sql = "SELECT ECHO_NAME NAME,ECHO_MESSAGE MSG FROM "+CBSystemConstants.opcardUser+"T_A_RULE_ECHO WHERE ECHO_ID = '"
					+ msg.getBottom() + "'";
			
			list = DBManager.query(sql);
			
			if (list.size() < 1){
				resultMap.put("msg", "此操作将导致异常");
				resultMap.put("code", "1");
				resultMap.put("kind", "2");
				return resultMap;
			}
		}
		
		Map<String, String> map = list.get(0);
		
		String name = StringUtils.ObjToString(map.get("NAME"));
		String message = StringUtils.ObjToString(map.get("MSG"));
		String code = StringUtils.ObjToString(map.get("CODE"));
		String kind = StringUtils.ObjToString(map.get("KIND"));

		if (message.equals("") || message == null){
			resultMap.put("msg", name);
			resultMap.put("code", code);
			resultMap.put("kind", kind);
			return resultMap;
		}

		String newmsg = "校核码:"+msg.getBottom()+",信息:"+replaceStr(message, msg, pd);
		
		resultMap.put("msg", newmsg);
		resultMap.put("code", code);
		resultMap.put("kind", kind);
		
		return resultMap;
	}

	/**
	 * 消息过滤
	 * 
	 * @param str
	 * @param pd
	 * @param msg
	 * @return
	 */
	private String replaceStr(String str, CheckMessage msg, PowerDevice pd) {
		StringBuffer infPd = new StringBuffer("");
		for (PowerDevice dev : msg.getPd()) {
			if (dev == null) { // 防止异常
				dev = new PowerDevice();
				dev.setPowerDeviceName("");
			}
			if (msg.getBottom().equals("300")|| msg.getBottom().equals("309")) {  // 300语句特殊处理
				if (dev.getPowerStationID().equals(""))
					infPd.append("<" + dev.getPowerStationName() + ">");
				else
					infPd.append("<" + CZPService.getService().getDevName(CBSystemConstants.getPowerStation(dev.getPowerStationID())) + ">");
			} else {
				if(!("").equals(dev.getPowerStationID())){
					infPd.append("<" + CZPService.getService().getDevName(CBSystemConstants.getPowerStation(dev.getPowerStationID())) + ">");
				}
				if (!dev.getPowerDeviceID().equals(""))
					infPd.append("<" + CZPService.getService().getDevName(dev) + ">");
				else{
					String devName=dev.getPowerDeviceName();
					infPd.append("<" + devName + ">");
				}
			}
			if (msg.getPd().size()==1&&str.contains("{设备状态}")) {
				String status = StringUtils.ObjToString(msg.getStatus());
				
				if(status.equals("")){
					status = dev.getDeviceStatus();
				}
				
				String devStatus = RuleExeUtil.getStatus(status);
				if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)||dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
					if(dev.getDeviceStatus().equals("0")){
						devStatus="合位";
					}else{
						devStatus="分位";
					}
				}
				str = str.replace("{设备状态}", "<" + devStatus + ">");
			}
			if(msg.getPd().size()==1&&str.contains("{告警信息}")){
				str = str.replace("{告警信息}", "<" + msg.getMessage() + ">");
			}
		}
		
		if(str.contains("{当前设备}")) {
			if(msg.getPd().size()>0&&!msg.getPd().get(0).getActionWord().equals("")){//如果PD的ActionWord属性有值，则替换设备名称为ActionWord的值
				str = str.replace("{当前设备}", "<" + msg.getPd().get(0).getActionWord() + ">");
			}else{
				str = str.replace("{当前设备}", "<" + CZPService.getService().getDevName(pd) + ">");
			}
		}

		String regx = "\\{[\u4e00-\u9fa5]*(（[\u4e00-\u9fa5]*）)?\\}";
		Pattern compile = Pattern.compile(regx);
        Matcher matcher = compile.matcher(str);
		
        if(matcher.find()){//解析
        	str = str.replace(matcher.group(),infPd.toString()); 
        }
		
		return str;
	}
}
