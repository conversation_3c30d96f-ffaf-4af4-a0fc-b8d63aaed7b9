package com.tellhow.czp.operationcard;

import javax.swing.JPanel;

import com.tellhow.czp.app.CZPImpl;

import czprule.model.CodeNameModel;

public abstract class OperateTicketSGP extends JPanel{

	protected static OperateTicketSGP sgpTicket;
	public  static boolean ops = false;
	public static OperateTicketSGP getInstance() {

		if (sgpTicket == null) {
			sgpTicket=(OperateTicketSGP)CZPImpl.getInstance("OperateTicketSGP");
			if(sgpTicket == null)
				sgpTicket = new OperateTicketSGPDefault();

			return sgpTicket;
		}
		else
			return sgpTicket;
	}
	
	public void initTable(CodeNameModel cnm) {
		
	}
	/**
	 * kind 宗令票 ，逐项票 -- 操作票界面到转拟至手工开票时操作票类型
	 * @param cnm
	 * @param kind
	 */
	public void initTable(CodeNameModel cnm, String kind) {
		// TODO Auto-generated method stub
		
	}
}
