/*
 * OperateTicketMLPMX.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.operationcard;

import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.Toolkit;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.imageio.ImageIO;
import javax.swing.ImageIcon;
import javax.swing.JOptionPane;
import javax.swing.SwingUtilities;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;

import org.jfree.data.category.IntervalCategoryDataset;
import org.jfree.data.gantt.Task;
import org.jfree.data.gantt.TaskSeries;
import org.jfree.data.gantt.TaskSeriesCollection;
import org.jfree.report.JFreeReportBoot;

import com.tellhow.czp.app.PrintOperationCard;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.user.User;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.system.ShowMessage;

/**
 *
 * <AUTHOR>
 */
public class OperateTicketMLPMXDefault extends OperateTicketMLPMX {
	int number1 = 1700;
	int number2 = 2600;
	int number3 = 3700;
	private String[] cards;
	private CodeNameModel cnm = null;
	private SetJTableProtery sjp = new SetJTableProtery();
	private Object[][] tableData = null;
	private javax.swing.table.DefaultTableModel jtableModel1;
	//	List mx = new ArrayList();
	//MusicAwoke msa = new MusicAwoke();
	java.util.Timer timer = new java.util.Timer();
	java.util.Date date = new java.util.Date();

	/** Creates new form OperateTicketMLPMX */
	public OperateTicketMLPMXDefault() {
		super(SystemConstants.getMainFrame(), "操作票明细", true);
	}
	public void init(CodeNameModel cnm,String[] cards){
		this.cnm = cnm;
		this.cards = cards;
		initComponents();
		initJscorrpane();
		init();
		getRowAndValue(number1);
		//creatGannt();
		this.setSize(1100, 580);
		this.jComboBox1.setVisible(false);
		this.jComboBox2.setVisible(false);
		this.jLabel7.setVisible(false);
		setLocationCenter();
		jSplitPane1.setDividerLocation(1.0);
	}

	//设置jsplitpanel
	public void initJscorrpane() {
		//解决操作票明细滚动条问题
//		jScrollPane2.setVerticalScrollBar(jScrollPane1.getVerticalScrollBar());
		jSplitPane1.setOneTouchExpandable(true);
	}

	/**
	 * 创建甘特图
	 */
	public void creatGannt() {
		try {
			jLabel6.setIcon(new ImageIcon(ImageIO
					.read(new File("D:\\gantt.jpg"))));
			jPanel3.add(jLabel6);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 屏幕中央显示
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/**
	 * 初始化
	 */
	private void init() {

		this.jLabel8.setText(this.cnm.getName());
		this.jLabel9.setText(this.cards[3]);
		this.jLabel11.setText(this.cards[1]);
		this.jLabel12.setText(this.cards[2]);
		this.jLabel10.setText(cards[0]);

		Dimension size = jTable1.getTableHeader().getPreferredSize();
		size.height = 43;//设置新的表头高度40 
		jTable1.getTableHeader().setPreferredSize(size);
		jtableModel1 = new DefaultTableModel(tableData, new String[] { "顺序",
				"操作单位", "操作内容", "下令人", "受令人", "下令时间", "回令人", "回令时间", "状态" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		TicketDBManager tdb = new TicketDBManager();
		List<BaseCardModel> results = tdb.queryTicketMX(this.cnm.getCode());
		BaseCardModel bcm = null;
		for (int i = 0; i < results.size(); i++) {
			bcm = results.get(i);
			Object[] rowData = {
					new CodeNameModel(bcm.getMxid(), bcm.getCardSub()),
					bcm.getCzsn().equals("")?bcm.getStationName():bcm.getCzsn(), bcm.getCardDesc(),
					bcm.getXlr(), bcm.getSlr(), bcm.getXlsj(),
					bcm.getHlr(), bcm.getHlsj(), bcm.getStatus() };
			jtableModel1.addRow(rowData);
		}
		//		jTable1 = new JTable();
		jTable1.setRowHeight(26);
		jTable1.setModel(jtableModel1);
		jTable1.setFont(new java.awt.Font("宋体", 0, 13));
		sjp.makeFace(jTable1);
		sjp.getTableHeader(jTable1);//列名居中
		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMaxWidth(40);
		tcm.getColumn(1).setMinWidth(140);
		tcm.getColumn(1).setMaxWidth(140);
		tcm.getColumn(2).setMinWidth(300);
		
		tcm.getColumn(3).setMaxWidth(60);
		tcm.getColumn(4).setMaxWidth(60);
		tcm.getColumn(5).setWidth(85);
		tcm.getColumn(6).setMaxWidth(60);
		tcm.getColumn(7).setWidth(85);
		tcm.getColumn(8).setMaxWidth(60);
		
		setColumnHidden(tcm, 3);
		setColumnHidden(tcm, 4);
		setColumnHidden(tcm, 5);
		setColumnHidden(tcm, 6);
		setColumnHidden(tcm, 7);
		setColumnHidden(tcm, 8);
		
		jTable1.addMouseListener(new MouseAdapter() {
			public void mouseClicked(MouseEvent me) {
				jTableMouseClicked(me);
			}
		});
	}
	
	private void setColumnHidden(TableColumnModel tcm, int i) {
		tcm.getColumn(i).setMinWidth(0);   
		tcm.getColumn(i).setMaxWidth(0);
		tcm.getColumn(i).setWidth(0);
		tcm.getColumn(i).setPreferredWidth(0);
	}

	/**
	 * Table左键单击事件
	 * @param evt
	 */
	private void jTableMouseClicked(MouseEvent me) {

		if (SwingUtilities.isLeftMouseButton(me) && me.getClickCount() == 2) {
			int row = jTable1.rowAtPoint(me.getPoint());

			String xlrName = ((String) jTable1.getValueAt(row, 3)).trim();
			CodeNameModel mxcnm = (CodeNameModel) jTable1.getValueAt(row, 0);

			// 下令
			if (jTable1.columnAtPoint(me.getPoint()) == 3) {
				if (jLabel9.getText() == null || jLabel9.getText().equals("")) {
					ShowMessage.view("对不起，该票未审核,不能下令！");
					return;
				}

				if (!xlrName.equals("")) {
					ShowMessage.view("该令已下发！");
					return;
				}
				PasswordDialog pd = new PasswordDialog(
						SystemConstants.getMainFrame(), true, null);
				pd.jButton1.setText("下 令");
				User xlr = pd.openDialog();
				if (xlr != null) {
					TicketDBManager tdb = new TicketDBManager();
					tdb.updateXlrXlsj(xlr.getUserID(), mxcnm.getCode());
					init();//更新表数据
					//msa.execute(timer, date);
				}
			}

			//  受令
			if (jTable1.columnAtPoint(me.getPoint()) == 4) {
				if ("".equals(xlrName)) {
					ShowMessage.view("对不起！未下令不能受令！");
					return;
				}
				String slrName = ((String) jTable1.getValueAt(row, 4)).trim();
				if (!slrName.equals("")) {
					ShowMessage.view("命令票已经受理！");
					return;
				}
				slrName = JOptionPane.showInputDialog(this, "请输入受令人姓名：");
				if ("".equals(slrName) || null == slrName)
					return;
				TicketDBManager tdb = new TicketDBManager();
				tdb.updateSlr(mxcnm.getCode(), slrName);
				init();//更新表数据
			}

			//回令
			if (jTable1.columnAtPoint(me.getPoint()) == 6) {
				if ("".equals(xlrName)) {
					ShowMessage.view("对不起！未下令不能回令！");
					return;
				}
				String slrName = ((String) jTable1.getValueAt(row, 4)).trim();
				if (slrName.equals("")) {
					ShowMessage.view("对不起！未受令不能回令！");
					return;
				}
				String hlrName = (String) jTable1.getValueAt(row, 6);
				if (!hlrName.equals("")) {
					ShowMessage.view("命令票已经回令！");
					return;
				}
				hlrName = JOptionPane.showInputDialog(this, "请输入回令人姓名：");
				if ("".equals(hlrName) || null == hlrName)
					return;
				TicketDBManager tdb = new TicketDBManager();
				tdb.updateHlrHLSJ(mxcnm.getCode(), hlrName);
				timer.cancel();
				//creatGannt();
				init();//更新表数据
			}
		}
	}

	/**
	 * @动态甘特图
	 * @param result
	 */
	public void getRowAndValue(int num) {
		int M = 0;
		int D = 0;
		int H = 0;
		int F = 0;
		int countRow = jTable1.getRowCount();
		if (countRow != 0) {
			for (int i = 0; i < countRow; i++) {
				String revalue = jTable1.getValueAt(i, 5).toString();
				if (!revalue.equals("")) {
					M = Integer.parseInt(revalue.substring(0, 2));
					D = Integer.parseInt(revalue.substring(3, 5));
					H = Integer.parseInt(revalue.substring(6, 8));
					F = Integer.parseInt(revalue.substring(9, 11));
					break;
				}
			}
		}
		/*
		// 组装甘特图
		IntervalCategoryDataset dataset = createSampleDataset();
		JFreeChart chart = ChartFactory.createGanttChart(null, null, null,
				dataset, false, true, true);
		CategoryPlot plot = chart.getCategoryPlot();
		BarRenderer bar = new BarRenderer();
		bar.setItemMargin(0.2f);
		Font xfont = new Font("宋体", Font.PLAIN, 12);
		plot.setAxisOffset(new RectangleInsets(0.0D, 0.0D, 0.0D, 0.0D));
		plot.setDomainGridlinePaint(Color.pink);
		plot.setBackgroundPaint(Color.WHITE);// 设置网格的背景颜色
		plot.setRangeGridlinePaint(Color.blue);// 设置网格的颜色
		plot.setForegroundAlpha(0.5f);// 设置Task的颜色透明度
		CategoryPlot catplot = chart.getCategoryPlot(); //获取区域对象。 
		catplot.setNoDataMessage("无数据显示");//没有数据的时候显示的内容  
		catplot.setNoDataMessagePaint(Color.RED);
		CategoryItemRenderer rend = plot.getRenderer();
		rend.setSeriesPaint(0, Color.ORANGE);// 设置Task的颜色
		plot.setRenderer(rend);
		// 用来控制时间轴的显示,防止乱码
		DateAxis da = (DateAxis) plot.getRangeAxis(0);
		try {
			if (num == 1700) {
				da.setRange(
						new GregorianCalendar(2011, M, D, 00, 00).getTime(),
						new GregorianCalendar(2011, M, D + 1, 23, 00).getTime());// 设置时间范围
			} else if (num == 2600) {
				da.setRange(
						new GregorianCalendar(2011, M, D, 00, 00).getTime(),
						new GregorianCalendar(2011, M, D + 2, 23, 00).getTime());// 设置时间范围
			} else if (num == 3700) {
				da.setRange(
						new GregorianCalendar(2011, M, D, 00, 00).getTime(),
						new GregorianCalendar(2011, M, D + 3, 23, 00).getTime());// 设置时间范围
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		da.setDateFormatOverride(new SimpleDateFormat("dd HH"));
		da.setLabelFont(xfont);// 轴标题 
		da.setTickLabelPaint(Color.RED);
		DateFormat format = new SimpleDateFormat("dd HH");
		DateTickUnit dtu = new DateTickUnit(DateTickUnitType.HOUR, 1, format);
		da.setTickUnit(dtu);//设置日期轴的日期标签  
		FileOutputStream fop = null;
		if (countRow == 1) {
			plot.setAxisOffset(new RectangleInsets(17.0D, 0.0D, 0.0D, 0.0D));
			try {
				fop = new FileOutputStream("D:\\gantt.jpg");
				ChartUtilities.writeChartAsJPEG(fop, 1f, chart, num,
						(29 * countRow) + 40, null);
			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				try {
					fop.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		} else {
			try {
				fop = new FileOutputStream("D:\\gantt.jpg");
				ChartUtilities.writeChartAsJPEG(fop, 1f, chart, num,
						(29 * countRow) + 29, null);
			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				try {
					fop.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		*/
	}

	/**
	 * 日期格式
	 */
	private static Date date(int minute, int hourOfDay, int day, int month,
			int year) {

		final Calendar calendar = Calendar.getInstance();
		calendar.set(year, month, day, hourOfDay, minute);

		final Date result = calendar.getTime();
		return result;
	}

	/** 
	 * 动态构造甘特图
	 */
	private IntervalCategoryDataset createSampleDataset() {
		final TaskSeries s1 = new TaskSeries(" ");
		int countRow = jTable1.getRowCount();
		Task temp = null;
		String n = null;
		String m = null;
		String starmouth = null;
		String endmouth = null;
		String starday = null;
		String endday = null;
		String starhour = null;
		String endhour = null;
		String starminute = null;
		String endminute = null;
		int sminute;
		int eminute;
		int shour;
		int ehour;
		int sday;
		int eday;
		int smouth;
		int emouth;
		int year;
		List mouthList = new ArrayList();
		List dayList = new ArrayList();
		List hourList = new ArrayList();
		List minuteList = new ArrayList();
		Calendar nowtime = Calendar.getInstance();//获得系统当前日期
		year = nowtime.get(Calendar.YEAR);
		smouth = nowtime.get(Calendar.MONTH) + 1;//系统日期从0开始算起
		sday = nowtime.get(Calendar.DAY_OF_MONTH);
		shour = nowtime.get(Calendar.HOUR);
		sminute = nowtime.get(Calendar.MINUTE);
		for (int c = 0; c < countRow; c++) {
			n = jTable1.getValueAt(c, 5).toString();
			m = jTable1.getValueAt(c, 7).toString();
			if (!n.equals("") && !m.equals("")) {
				starmouth = n.substring(0, 2);
				mouthList.add(starmouth);
				endmouth = m.substring(0, 2);
				starday = n.substring(3, 5);
				dayList.add(starday);
				endday = m.substring(3, 5);
				starhour = n.substring(6, 8);
				hourList.add(starhour);
				endhour = m.substring(6, 8);
				starminute = n.substring(9, 11);
				minuteList.add(starminute);
				endminute = m.substring(9, 11);

				sminute = Integer.parseInt(starminute);
				eminute = Integer.parseInt(endminute);
				shour = Integer.parseInt(starhour);
				ehour = Integer.parseInt(endhour);
				sday = Integer.parseInt(starday);
				eday = Integer.parseInt(endday);
				smouth = Integer.parseInt(starmouth);
				emouth = Integer.parseInt(endmouth);
				temp = new Task(String.valueOf(c + 1), date(sminute, shour,
						sday, smouth, year), date(eminute, ehour, eday, emouth,
						year));
				s1.add(temp);
			} else if (!n.equals("") && m.equals("")) {
				String mouthmin = n.substring(0, 2);
				int mouth = Integer.parseInt(mouthmin);
				String daymin = n.substring(3, 5);
				int day = Integer.parseInt(daymin);
				String hourmin = n.substring(6, 8);
				int hour = Integer.parseInt(hourmin);
				String mintemin = n.substring(9, 11);
				int minute = Integer.parseInt(mintemin);
				temp = new Task(String.valueOf(c + 1), date(minute, hour, day,
						mouth, year), date(minute, hour, day, mouth, year));
				s1.add(temp);
			} else if (countRow == 1 && n == "" && m == "") {

			} else {
				temp = new Task(String.valueOf(c + 1), date(sminute, shour,
						sday, smouth - 1, year), date(sminute, shour, sday,
						smouth - 1, year));
				s1.add(temp);
			}
		}
		final TaskSeriesCollection collection = new TaskSeriesCollection();
		collection.add(s1);

		return collection;
	}

	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jPanel2 = new javax.swing.JPanel();
		jLabel1 = new javax.swing.JLabel();
		jLabel8 = new javax.swing.JLabel();
		jLabel3 = new javax.swing.JLabel();
		jLabel11 = new javax.swing.JLabel();
		jLabel5 = new javax.swing.JLabel();
		jLabel10 = new javax.swing.JLabel();
		jLabel2 = new javax.swing.JLabel();
		jLabel9 = new javax.swing.JLabel();
		jLabel4 = new javax.swing.JLabel();
		jLabel12 = new javax.swing.JLabel();
		jSplitPane1 = new javax.swing.JSplitPane();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();
		jScrollPane2 = new javax.swing.JScrollPane();
		jPanel3 = new javax.swing.JPanel();
		jLabel6 = new javax.swing.JLabel();
		jComboBox1 = new javax.swing.JComboBox();
		jComboBox2 = new javax.swing.JComboBox();
		jLabel7 = new javax.swing.JLabel();
		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jLabel13 = new javax.swing.JLabel();
		jComboBox3 = new javax.swing.JComboBox();
		jButton1.setVisible(false);
		jButton2.setVisible(false);
		jLabel13.setVisible(false);
		jComboBox3.setVisible(false);

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jLabel1.setText("\u64cd\u4f5c\u7f16\u53f7\uff1a");

		jLabel8.setFont(new java.awt.Font("微软雅黑", 1, 14));
		jLabel8.setText("jLabel8");

		jLabel3.setText("\u62df \u7968 \u4eba\uff1a");

		jLabel11.setFont(new java.awt.Font("微软雅黑", 1, 14));
		jLabel11.setText("jLabel11");

		jLabel5.setText("\u64cd\u4f5c\u4efb\u52a1\uff1a");

		jLabel10.setFont(new java.awt.Font("微软雅黑", 1, 14));
		jLabel10.setText("jLabel10");

		jLabel2.setText("操作票类型：");

		jLabel9.setFont(new java.awt.Font("微软雅黑", 1, 14));
		jLabel9.setText("jLabel9");

		jLabel4.setText("\u62df\u7968\u65f6\u95f4\uff1a");

		jLabel12.setFont(new java.awt.Font("微软雅黑", 1, 14));
		jLabel12.setText("jLabel12");

		jTable1.setModel(new javax.swing.table.DefaultTableModel(
				new Object[][] { { null, null, null, null },
						{ null, null, null, null }, { null, null, null, null },
						{ null, null, null, null } }, new String[] { "Title 1",
						"Title 2", "Title 3", "Title 4" }));
		jScrollPane1.setViewportView(jTable1);

		jSplitPane1.setLeftComponent(jScrollPane1);

		jPanel3.add(jLabel6);

		jScrollPane2.setViewportView(jPanel3);

		//jSplitPane1.setRightComponent(jScrollPane2);

		jComboBox1.setModel(new javax.swing.DefaultComboBoxModel(new String[] {
				"一天", "二天", "三天" }));
		jComboBox1.addItemListener(new java.awt.event.ItemListener() {
			public void itemStateChanged(java.awt.event.ItemEvent evt) {
				ganttLength(evt);
			}
		});

		jComboBox2.setModel(new javax.swing.DefaultComboBoxModel(new String[] {
				"0%", "40%", "100%" }));
		jComboBox2.addItemListener(new java.awt.event.ItemListener() {
			public void itemStateChanged(java.awt.event.ItemEvent evt) {
				ganttShow(evt);
			}
		});

		jLabel7.setText("\u7518\u7279\u56fe\u8303\u56f4\uff1a");

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/ico6-1.gif"))); // NOI18N
		jButton1.setToolTipText("\u6253\u5370");
		jButton1.setBorder(null);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/favorite.png"))); // NOI18N
		jButton2.setToolTipText("\u4fdd\u5b58\u5178\u578b\u7968");
		jButton2.setBorder(null);
		jButton2.setFocusPainted(false);
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jLabel13.setText("\u5bfc\u51fa\uff1a");

		jComboBox3.setModel(new javax.swing.DefaultComboBoxModel(new String[] {
				"PDF", "Word", "Txt" }));

		org.jdesktop.layout.GroupLayout jPanel2Layout = new org.jdesktop.layout.GroupLayout(
				jPanel2);
		jPanel2.setLayout(jPanel2Layout);
		jPanel2Layout
				.setHorizontalGroup(jPanel2Layout
						.createParallelGroup(
								org.jdesktop.layout.GroupLayout.LEADING)
						.add(org.jdesktop.layout.GroupLayout.TRAILING,
								jPanel2Layout
										.createSequentialGroup()
										.addContainerGap()
										.add(jPanel2Layout
												.createParallelGroup(
														org.jdesktop.layout.GroupLayout.LEADING)
												.add(jPanel2Layout
														.createSequentialGroup()
														.addPreferredGap(
																org.jdesktop.layout.LayoutStyle.RELATED)
														.add(jPanel2Layout
																.createParallelGroup(
																		org.jdesktop.layout.GroupLayout.TRAILING,
																		false)
																.add(org.jdesktop.layout.GroupLayout.LEADING,
																		jLabel3,
																		org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																		org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																		Short.MAX_VALUE)
																.add(org.jdesktop.layout.GroupLayout.LEADING,
																		jLabel1,
																		org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																		org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																		Short.MAX_VALUE)))
												.add(jLabel5))
										.add(3, 3, 3)
										.add(jPanel2Layout
												.createParallelGroup(
														org.jdesktop.layout.GroupLayout.TRAILING)
												.add(org.jdesktop.layout.GroupLayout.LEADING,
														jPanel2Layout
																.createSequentialGroup()
																.add(jPanel2Layout
																		.createParallelGroup(
																				org.jdesktop.layout.GroupLayout.LEADING,
																				false)
																		.add(jLabel11,
																				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																				Short.MAX_VALUE)
																		.add(jLabel8,
																				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																				167,
																				Short.MAX_VALUE))
																.addPreferredGap(
																		org.jdesktop.layout.LayoutStyle.RELATED)
																.add(jPanel2Layout
																		.createParallelGroup(
																				org.jdesktop.layout.GroupLayout.LEADING)
																		.add(jLabel2)
																		.add(jLabel4))
																.addPreferredGap(
																		org.jdesktop.layout.LayoutStyle.RELATED)
																.add(jPanel2Layout
																		.createParallelGroup(
																				org.jdesktop.layout.GroupLayout.LEADING)
																		.add(jLabel12,
																				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																				176,
																				Short.MAX_VALUE)
																		.add(jLabel9,
																				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																				176,
																				Short.MAX_VALUE)))
												.add(jLabel10,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														417, Short.MAX_VALUE))
										.add(jPanel2Layout
												.createParallelGroup(
														org.jdesktop.layout.GroupLayout.LEADING)
												.add(jPanel2Layout
														.createSequentialGroup()
														.add(173, 173, 173)
														.add(jLabel7)
														.addPreferredGap(
																org.jdesktop.layout.LayoutStyle.RELATED)
														.add(jComboBox2,
																org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
																org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
																org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
														.addPreferredGap(
																org.jdesktop.layout.LayoutStyle.RELATED)
														.add(jComboBox1,
																org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
																63,
																org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
												.add(jPanel2Layout
														.createSequentialGroup()
														.add(45, 45, 45)
														.add(jButton1)
														.addPreferredGap(
																org.jdesktop.layout.LayoutStyle.RELATED)
														.add(jButton2,
																org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
																16,
																org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
														.addPreferredGap(
																org.jdesktop.layout.LayoutStyle.RELATED)
														.add(jLabel13)
														.addPreferredGap(
																org.jdesktop.layout.LayoutStyle.RELATED)
														.add(jComboBox3,
																org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
																91,
																org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)))
										.addContainerGap())
						.add(jScrollPane1,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								884, Short.MAX_VALUE));
		jPanel2Layout
				.setVerticalGroup(jPanel2Layout
						.createParallelGroup(
								org.jdesktop.layout.GroupLayout.LEADING)
						.add(jPanel2Layout
								.createSequentialGroup()
								.addContainerGap()
								.add(jPanel2Layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jLabel1)
										.add(jLabel8)
										.add(jLabel2)
										.add(jLabel9)
										.add(jLabel13)
										.add(jComboBox3,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jButton1)
										.add(jButton2,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												16,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(jPanel2Layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jLabel3).add(jLabel11)
										.add(jLabel4).add(jLabel12))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(jPanel2Layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jLabel5)
										.add(jLabel10,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												29,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jComboBox2,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jComboBox1,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jLabel7))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(jScrollPane1,
										org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
										438, Short.MAX_VALUE)));

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jPanel2,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE));
		layout.setVerticalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(
				layout.createSequentialGroup()
						.add(jPanel2,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.addContainerGap(16, Short.MAX_VALUE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents
	
	//打印
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.setCursor(new Cursor(Cursor.WAIT_CURSOR));
    	JFreeReportBoot.getInstance().start();
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zb = tdb.queryTicketZB(this.cnm.getCode());
    	List<BaseCardModel> mx = tdb.queryTicketMX(this.cnm.getCode());
    	//PrintOperationCard print = new PrintOperationCard(this, "", "", "", "", "", "", jtableModel1, 1,"");
    	PrintOperationCard print = new PrintOperationCard(this, zb, mx);
    	print.PrintPreview();
    	this.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
	}

	//导入典型票
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		TicketDBManager tdb = new TicketDBManager();
		boolean result = tdb.InsertDXTicketDB(cnm.getCode());
		if(result)
			ShowMessage.view("保存成功！");
		else
			ShowMessage.view("保存失败！");
	}

	//甘特图长度
	private void ganttLength(java.awt.event.ItemEvent evt) {
		// TODO add your handling code here:
		if (evt.getStateChange() == 1) {
			String date = evt.getItem().toString();
			if ("一天".equals(date)) {
				getRowAndValue(number1);
				creatGannt();
			} else if ("二天".equals(date)) {
				getRowAndValue(number2);
				creatGannt();
			} else if ("三天".equals(date)) {
				getRowAndValue(number3);
				creatGannt();
			}
		}
	}

	//甘特图显示范围
	private void ganttShow(java.awt.event.ItemEvent evt) {
		// TODO add your handling code here:
		String ganttShow = this.jComboBox2.getSelectedItem().toString();
		if ("0%".equals(ganttShow)) {
			this.setSize(1100, 580);
			jSplitPane1.setDividerLocation(1.0);
			setLocationCenter();
			this.jComboBox1.setVisible(false);

		} else if ("40%".equals(ganttShow)) {
			this.setSize(1300, 580);
			jSplitPane1.setDividerLocation(0.75);
			setLocationCenter();
			this.jComboBox1.setVisible(true);
		} else {
			this.setSize(1100, 580);
			jSplitPane1.setDividerLocation(0.0);
			setLocationCenter();
			this.jComboBox1.setVisible(true);
		}

	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton2;
	private javax.swing.JComboBox jComboBox1;
	private javax.swing.JComboBox jComboBox2;
	private javax.swing.JComboBox jComboBox3;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel10;
	private javax.swing.JLabel jLabel11;
	private javax.swing.JLabel jLabel12;
	private javax.swing.JLabel jLabel13;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JLabel jLabel3;
	private javax.swing.JLabel jLabel4;
	private javax.swing.JLabel jLabel5;
	private javax.swing.JLabel jLabel6;
	private javax.swing.JLabel jLabel7;
	private javax.swing.JLabel jLabel8;
	private javax.swing.JLabel jLabel9;
	private javax.swing.JPanel jPanel2;
	private javax.swing.JPanel jPanel3;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JSplitPane jSplitPane1;
	private javax.swing.JTable jTable1;
	// End of variables declaration//GEN-END:variables

}
