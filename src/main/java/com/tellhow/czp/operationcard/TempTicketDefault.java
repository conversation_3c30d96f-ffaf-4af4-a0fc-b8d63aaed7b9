package com.tellhow.czp.operationcard;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Point;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultCellEditor;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.text.JTextComponent;

import com.tellhow.czp.app.service.OMSService;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.DeviceStatusManager;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.service.OperationCheck;
import com.tellhow.czp.service.OperationCheckDefault;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.stationstartup.InitDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;


public class TempTicketDefault extends TempTicket  {

	protected final JPanel contentPanel = new JPanel();
	private JTable jTable1;
	protected String bccardid;//保存主表id
	protected boolean isInversing = false; //是否在演示
	public static JTextComponent getJc(){
		return getTjc();
	}
	public static void setJc(JTextComponent tjc){
		TempTicketDefault.setTjc(tjc);
	}
//	public static int getn(){
//		return n;
//	}
	/**
	 * Launch the application.
	 */
/*	public static void main(String[] args) {
		try {
			TempTicket dialog = new TempTicket();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}*/
	/**
	 * Create the dialog.
	 */
	public   TempTicketDefault() {
		initComponents(); 
	}
	protected void initComponents() {
		setBounds(100, 100, 534, 451);
		this.setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		this.add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(new BorderLayout(0, 0));
		{
			JPanel northpanel = new JPanel();
			contentPanel.add(northpanel, BorderLayout.NORTH);
			northpanel.setLayout(new BorderLayout(0, 0));
			{
				JPanel panel = new JPanel();
				northpanel.add(panel, BorderLayout.CENTER);
				panel.setLayout(new BorderLayout(0, 0));
				{
					jLabel5 = new JLabel("\u64CD\u4F5C\u4EFB\u52A1\uFF1A");
					panel.add(jLabel5, BorderLayout.WEST);
				}
				{
					jScrollPane1 = new JScrollPane();
					panel.add(jScrollPane1, BorderLayout.CENTER);
					{
						jTextArea1 = new JTextArea();
						jTextArea1.setPreferredSize(new Dimension(4, 80));
						jTextArea1.setColumns(20);
				        jTextArea1.setFont(new java.awt.Font("宋体", 1, 14)); // NOI18N
				        jTextArea1.setLineWrap(true);
				        jTextArea1.addMouseListener(new MouseAdapter() {
							@Override
							public void mouseClicked(MouseEvent e) {
								// TODO Auto-generated method stub
								Point mousepoint; 
								mousepoint =e.getPoint();
								Component texteditor=jTextArea1.findComponentAt(mousepoint);
								texteditor.requestFocusInWindow();
								Component c = texteditor.getComponentAt(0, 0);
							    if (c != null && c instanceof JTextComponent) {
							        ((JTextComponent) c).selectAll();
							        tjc=(JTextComponent)c;
							    }
							}
						});
						jScrollPane1.setViewportView(jTextArea1);
					}
				}
			}
			{
				JPanel panel = new JPanel();
				northpanel.add(panel, BorderLayout.SOUTH);
				panel.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_1 = new JPanel();
					panel_1.setPreferredSize(new Dimension(60, 40));
					panel.add(panel_1, BorderLayout.WEST);
					{
						jButton1 = new JButton();
				        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
				        jButton1.setText("增加");
				        jButton1.setToolTipText("增加");
				        jButton1.setMargin(new java.awt.Insets(1,1,1,1));
				        jButton1.setFocusPainted(false);
				        jButton1.addActionListener(new java.awt.event.ActionListener() {
				            public void actionPerformed(java.awt.event.ActionEvent evt) {
				                jButton1ActionPerformed(evt);
				            }
				        });
						panel_1.add(jButton1);
					}
				}
				{
					JPanel panel_1 = new JPanel();
					panel.add(panel_1, BorderLayout.CENTER);
					panel_1.setLayout(new BorderLayout(0, 0));
					{
						JPanel panel_2 = new JPanel();
						panel_1.add(panel_2, BorderLayout.WEST);
						{
							jButton2 = new JButton();
							jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
					        jButton2.setText("删除");
					        jButton2.setToolTipText("删除");
					        jButton2.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton2.setFocusPainted(false);
					        jButton2.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton2ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton2);
						}
						{
							jButton11 = new JButton();
							jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/import.gif"))); // NOI18N
							jButton11.setText("撤销");
							jButton11.setToolTipText("撤销");
							jButton11.setMargin(new java.awt.Insets(1,1,1,1));
							jButton11.setFocusPainted(false);
							jButton11.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton11ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton11);
//							if(CBSystemConstants.cardbuildtype.equals("0"))
//								jButton11.setVisible(false);
						}
						{
							jButton10 = new JButton();
					        jButton10.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/replace.png"))); // NOI18N
					        jButton10.setText("替换");
					        jButton10.setToolTipText("替换");
					        jButton10.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton10.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton10ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton10);
						}
						{
							jButton3 = new JButton();
							jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
					        jButton3.setText("上移");
					        jButton3.setToolTipText("上移");
					        jButton3.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton3.setFocusPainted(false);//是否指示了输入焦点--黄翔
					        jButton3.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton3ActionPerformed(evt);     //给按钮赋予方法，次方法定义在后面--hx
					            }
					        });
							panel_2.add(jButton3);
						}
						{
							jButton4 = new JButton();
					        jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
					        jButton4.setText("下移");
					        jButton4.setToolTipText("下移");
					        jButton4.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton4.setFocusPainted(false);
					        jButton4.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton4ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton4);
						}
						{
							jButton7 = new JButton();
					        jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/merge.png"))); // NOI18N
					        jButton7.setText("合项");
					        jButton7.setToolTipText("合项");
					        jButton7.setVisible(false);
					        jButton7.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton7.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton7ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton7);
							jButton7.setVisible(false);
						}
						{
							jButton8 = new JButton("\u5206\u9879");
							jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/split.png"))); // NOI18N
							jButton8.setText("分项");
							jButton8.setToolTipText("分项");
							jButton8.setMargin(new java.awt.Insets(1,1,1,1));
							jButton8.addActionListener(new java.awt.event.ActionListener() {
								public void actionPerformed(java.awt.event.ActionEvent evt) {
									jButton8ActionPerformed(evt);
								}
							});
							panel_2.add(jButton8);
							jButton8.setVisible(false);
						}
						{
							jButton9 = new JButton("校验");
					        jButton9.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/check.png"))); // NOI18N
					        jButton9.setText("校验");
					        jButton9.setToolTipText("校验");
					        jButton9.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton9.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton9ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton9);
						}
						{
							jButton12 = new JButton("演示");
							jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/play.png"))); // NOI18N
							jButton12.setText("演示");
							jButton12.setToolTipText("演示");
					        jButton12.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton12.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton12ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton12);
						}
					}
				}
			}
			{
				JPanel panel = new JPanel();
				northpanel.add(panel, BorderLayout.NORTH);
				panel.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_1 = new JPanel();
					panel.add(panel_1, BorderLayout.CENTER);
					panel_1.setLayout(new BorderLayout(0, 0));
					{
						//JPanel panel_2 = new JPanel();
						JPanel panel_3 = new JPanel();
						JPanel panel_4 = new JPanel();
//						panel_1.add(panel_2, BorderLayout.WEST);
//						{
//							lblNewLabel_1 = new JLabel("单位：");
//							panel_2.add(lblNewLabel_1);
//						}
//						{
//							lblNewLabel = new JLabel("");
//							lblNewLabel.setVerticalAlignment(SwingConstants.TOP);
//							lblNewLabel.setFont(new java.awt.Font("微软雅黑", 1, 13)); // NOI18N
//							panel_2.add(lblNewLabel);
//						}
						panel_1.add(panel_3, BorderLayout.WEST);
						{
							jLabel1 = new JLabel("拟票人：");
							//jLabel1.setPreferredSize(new Dimension(48, 50));
							panel_3.add(jLabel1);
						}
						{
							jLabel4 = new JLabel("");
							jLabel4.setFont(new java.awt.Font("微软雅黑", 1, 13)); // NOI18N
					        jLabel4.setText("jLabel4");
							panel_3.add(jLabel4);
						}
						panel_1.add(panel_4, BorderLayout.EAST);
						{
							jLabel2 = new JLabel("拟票时间：");
							panel_4.add(jLabel2);
						}
						{
							jLabel3 = new JLabel("");
							jLabel3.setFont(new java.awt.Font("微软雅黑", 1, 13)); // NOI18N
					        jLabel3.setText("jLabel3");
							panel_4.add(jLabel3);
						}
					}
				}
			}
		}
		{
			jScrollPane2 = new JScrollPane();
			contentPanel.add(jScrollPane2, BorderLayout.CENTER);
			{
				jTable1 = new JTable();
				jTable1.setPreferredScrollableViewportSize(new Dimension(450, 200));
				jTable1.setFont(new java.awt.Font("宋体", 0, 13)); // NOI18N	
		        jTable1.setRowHeight(26);
				/*jTable1.setModel(new DefaultTableModel(
					new Object[][] {
						{null, null, null},
						{null, null, null},
						{null, null, null},
						{null, null, null},
						{null, null, null},
					},
					new String[] {
						"New column", "New column", "New column"
					}
				));*/
				jScrollPane2.setViewportView(jTable1);
			}
		}
		{
			JPanel buttonPane = new JPanel();
			buttonPane.setLayout(new FlowLayout(FlowLayout.RIGHT));
			{
				jButton5 = new JButton();
		        jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
		        jButton5.setText("保存");
		        jButton5.setToolTipText("保存");
		        jButton5.setMargin(new java.awt.Insets(1,1,1,1));
		        jButton5.addActionListener(new java.awt.event.ActionListener() {
		            public void actionPerformed(java.awt.event.ActionEvent evt) {
		                jButton5ActionPerformed(evt);
		            }
		        });
				//cancelButton.setActionCommand("Cancel");
				buttonPane.add(jButton5);
			}
			{
				btnoms = new JButton();
				btnoms.addActionListener(new ActionListener() {
					public void actionPerformed(ActionEvent evt) {
						 jButton5ActionPerformed(evt);
					}
					
				});
				if(CBSystemConstants.roleCode.equals("0"))
					btnoms.setVisible(false);
				btnoms.setText("导入OMS");
				btnoms.setToolTipText("导入OMS");
				btnoms.setMargin(new java.awt.Insets(1,1,1,1));
				buttonPane.add(btnoms);
			}
			contentPanel.add(buttonPane, BorderLayout.SOUTH);
			{
				jCheckBox1 = new JCheckBox();
				jCheckBox1.setForeground(new java.awt.Color(204, 0, 0));
		        jCheckBox1.setText("另存为典型票");
				buttonPane.add(jCheckBox1);
			}
		}
		{
			JPanel panel = new JPanel();
			this.add(panel, BorderLayout.NORTH);
			panel.setLayout(new BorderLayout(0, 0));
			{
				jButton6 = new JButton();
				//jButton6.setPreferredSize(new Dimension(33, 20));
		        jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
		        jButton6.setText("取消");
		        jButton6.setToolTipText("取消");
		        jButton6.setMargin(new java.awt.Insets(1,1,1,1));
		        jButton6.setFocusPainted(false);
		        jButton6.addActionListener(new java.awt.event.ActionListener() {
		            public void actionPerformed(java.awt.event.ActionEvent evt) {
		            	TempTicket.isRoll=true;
		                jButton6ActionPerformed(evt);
		                TempTicket.isRoll=false;
		            }
		        });
				panel.add(jButton6, BorderLayout.EAST);
			}
			{
				panel_5 = new JPanel();
//				if(CZPOperator.getOperator().getClass().getName().equals("com.tellhow.czp.app.CZPOperatorJC"))
//					panel.add(panel_5, BorderLayout.WEST);
				{
					lblNewLabel_2 = new JLabel("关联检修票");
					panel_5.add(lblNewLabel_2);
				}
				{
					textField = new JTextField();
					textField.setEditable(false);
					panel_5.add(textField);
					textField.setColumns(10);
				}
				{
					btnNewButton = new JButton("选择");
					btnNewButton.addActionListener(new ActionListener() {
						public void actionPerformed(ActionEvent e) {
							String jxpno = OMSService.getService().getJXPNO();
							if (!jxpno.equals("")){
								textField.setText(jxpno);
							}
						}
					});
					panel_5.add(btnNewButton);
				}
				{
					lblNewLabel_3 = new JLabel("关联操作票");
					panel_5.add(lblNewLabel_3);
				}
				{
					textField_1 = new JTextField();
					textField_1.setEditable(false);
					panel_5.add(textField_1);
					textField_1.setColumns(10);
				}
				{
					btnNewButton_1 = new JButton("选择");
					btnNewButton_1.addActionListener(new ActionListener() {
						public void actionPerformed(ActionEvent e) {
							String czpno = OMSService.getService().getCZPNO();
							if (!czpno.equals("")){
								textField_1.setText(czpno);
							}
						}
					});
					panel_5.add(btnNewButton_1);
				}
			}
		}
	
	}
	
	private List<CardItemModel> getItemModel() {
		String preStationName = "";
		List<CardItemModel> DescList = new ArrayList<CardItemModel>();
		if(jTable1Model.getRowCount() > 0) {
			for (int i = 0; i < jTable1Model.getRowCount(); i++) {
				CardItemModel cim = new CardItemModel();
				if(jTable1Model.getValueAt(i, 0) instanceof CardItemModel) {
					CardItemModel cnm=(CardItemModel)jTable1Model.getValueAt(i, 0);
				    cim.setUuIds(cnm.getUuIds());
				    cim.setBzbj(cnm.getBzbj());
				}
				else {
					cim.setUuIds(StringUtils.getUUID());
				}
				
//			    cim.setCardNum(jTable1Model.getValueAt(i, 1).toString());
				cim.setCardNum(String.valueOf(i+1));
			    cim.setCardItem(StringUtils.ObjToString(jTable1Model.getValueAt(i, 2)));
			    
			    String showName = StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
			    cim.setShowName(showName);
			    
			    String cardDesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
				cim.setCardDesc(cardDesc);
				
			    String stationName= StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
			    if(cardDesc.equals(""))
			    	stationName = "";
			    else if((stationName.equals("") || stationName.indexOf("（")>=0))
			    	stationName = preStationName;
				cim.setStationName(stationName);
				
				DescList.add(cim);
				if(!stationName.equals("") && stationName.indexOf("（")==-1)
					preStationName = stationName;
			}
		}
		return DescList;
	}

	//保存
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if(isInversing) {
			ShowMessage.view(this, "请先停止操作票演示再保存！");
			return;
		}
		int row = jTable1.getRowCount();
		if (row == 0) {
			ShowMessage.view(this, "数据为空,保存失败！");
			return;
		}
		if (jTextArea1.getText().trim().equals("")) {
			ShowMessage.view(this, "操作任务不能为空,保存失败！");
			return;
		}
		if(!checkContent())
			return;
		
		//单元格编辑状态下点击保存
		if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
		String czrw = jTextArea1.getText().trim();//编辑修改后的操作任务名称
		String jxpdh = "";//检修票单号
		String cardKind = "0";
		List<CardItemModel> DescList = getItemModel();
		jTable1Model =  (DefaultTableModel) jTable1.getModel();
		
		List<String> stationList = new ArrayList<String>();
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			
		    String cardDesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
		    String stationName= StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
		    if(!stationName.equals("") && !cardDesc.equals("") && !stationList.contains(stationName))
		    	stationList.add(stationName);
		}
		if(CBSystemConstants.roleCode.equals("2"))
			cardKind = "3";
		else if(stationList.size() >= 2)
			cardKind = "1";
		
		if(CBSystemConstants.cardflag.equals("0")){
			cardKind = "0";
		}else if(CBSystemConstants.cardflag.equals("1")){
			cardKind = "1";
		}
		//保存设备术语，设备预令状态
		Connection conn = DBManager.getConnection();

		TicketDBManager ticketDB = new TicketDBManager();
		try {
			String cardid = StringUtils.getUUID();

			conn.setAutoCommit(false);
           //插入票信息
			ticketDB.InsertTicketDB(conn, cardid,DescList, czrw, "0", cardKind, CBSystemConstants.cardbuildtype, Srcrbm.getPd().getPowerDeviceID(),jxpdh);
			
		
			DeviceStatusManager dsm = new DeviceStatusManager();
			dsm.insertDevState(conn, cardid); // 保存操作票对应设备元件状态变更
			dsm.insertDevStatus(conn); // 保存操作票对应设备预令状态
			
			conn.commit();
			conn.close();
			if (this.jCheckBox1.isSelected()) {
				ticketDB.InsertDXTicketDB(cardid);
			}
			
			//初始化操作票面板
//			OperateTicketTypePanelDefault otp = new OperateTicketTypePanelDefault();
			OperateTicketTypePanel otp = OperateTicketTypePanel.getInstance();
//			otp.jTabbedPane1.setSelectedIndex(0);
			otp.initTable();


			CBSystemConstants.clearLineSourceAndLoad();
			ShowMessage.view(this, "保存成功！");
		} catch (SQLException e) {
			try {
				conn.rollback();
				conn.close();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
			//回滚
			DeviceOperate.RollbackDeviceStatus();
			ShowMessage.view(this, "保存失败！");  
		} finally {
			
			if(CBSystemConstants.roleCode.equals("2")){
//				n=n+1;
//				ShowMessage.view(this,"哈哈哈");
				Map<Integer, DispatchTransDevice> all=DeviceOperate.getAlltransDevMap();
				for(int i=1;i<all.size()+1;i++){
					DispatchTransDevice dtd=all.get(i);
					dtd.setIssave("1");
				}
			}
			//监控票操作任务未解决！！！
//			if(n>0){
////				CBSystemConstants.getDtdMap().clear();
////				DeviceOperate.ClearDevMap();
//			}else{
//				//清空
//				DeviceOperate.ClearDevMap();
//				CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
//				CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
//		    	//CBSystemConstants.bztRelationRecord.clear();
//				CBSystemConstants.bztStateRecord.clear();
//				//CBSystemConstants.bztOrganRecord.clear();
//				SvgUtil.clear();
//				this.setVisible(false);
//				this.tempTicket = null;
//			}
			//清空
			DeviceOperate.ClearDevMap();
			CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
			CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
	    	//CBSystemConstants.bztRelationRecord.clear();
			CBSystemConstants.bztStateRecord.clear();
			//CBSystemConstants.bztOrganRecord.clear();
			SvgUtil.clear();
//			setVisible(false);
			setdel();
			tempTicket = null;
		}
		
	}
	
	public void setdel(){
		this.setVisible(false);
	}
	
	public boolean checkContent() {
		int countRowsI = jTable1.getRowCount();
		for (int i = 0; i < countRowsI; i++) {
			String czsx = jTable1.getValueAt(i, 1) == null?"":jTable1.getValueAt(i, 1).toString().trim();
			String czdw = jTable1.getValueAt(i, 3) == null?"":jTable1.getValueAt(i, 3).toString().trim();
			String cznr = jTable1.getValueAt(i, 4) == null?"":jTable1.getValueAt(i, 4).toString().trim();
			if(cznr.equals("")) {
			}
			if(!cznr.equals("")) {
				if(czdw.equals("")) {
				}
				if(czsx.equals("")) {
					if(i == 0)
						jTable1.setValueAt("1", i, 1);
					else if(jTable1.getValueAt(i, 1).toString().equals(jTable1.getValueAt(i-1, 1).toString()))
						jTable1.setValueAt(jTable1.getValueAt(i-1, 1).toString(), i, 1);
					else
						jTable1.setValueAt(String.valueOf(Integer.valueOf(jTable1.getValueAt(i-1, 1).toString())+1), i, 1);
				}
				else if(!tbp.common.util.StringUtil.isNum(czsx)) {
					String sz = StringUtils.getSZ(czsx);
					if(sz.equals("")) {
						ShowMessage.viewWarning(this, "第"+String.valueOf(i+1)+"行操作顺序填写的不是数字!");
						return false;
					}
					else
						jTable1.setValueAt(sz, i, 1);
				}
			}
		}
		return true;
	}
	/**
	  * 创建时间 2013年11月26日 上午9:51:43
	  * 操作票校核
	  * <AUTHOR>
	  * @Title checkOps
	  * @return
	  */
	public boolean checkOps(){
		//复制原来操作的缓存
		//Map<Integer, DispatchTransDevice> dtdmap =new HashMap<Integer, DispatchTransDevice>(CBSystemConstants.getDtdMap());
		//Map<Integer, DispatchTransDevice> allMap =new HashMap<Integer, DispatchTransDevice>(DeviceOperate.getAlltransDevMap());
		//Map<Integer, PowerDevice> curOprDev = CBSystemConstants.getCurOperateDevs();
		
		//DeviceOperate.getAlltransDevMap().clear();
		
		//DeviceOperate.CheckRollback();
		/*Map<String, HashMap<String, PowerDevice>> map = CBSystemConstants.getMapPowerStationDevice();
		for (Iterator<String> iterator = map.keySet().iterator(); iterator.hasNext();) {
			String stid = (String) iterator.next();
			//首先初始化所有关联厂站的缓存
			new StationDeviceToplogy().StationLoad(stid);
		}*/
		
		
		String sysBuild = CBSystemConstants.cardbuildtype;
		
		//DeviceOperate.ClearDevMap();
		int preSize = DeviceOperate.getAlltransDevMap().size();
		
		final ArrayList<int[]> errorList = new ArrayList<int[]>();
		errorList.clear();
	
		String stationname;
		String oprdesc;
		RuleBaseMode b;	
		boolean result=true;
		
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			stationname = StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
			oprdesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
			b=OperationCheck.execute(stationname, oprdesc);
			if(!b.getCheckout()){
				if(b.getMessageList().size()!=0){
					ShowMessage.view(b.getMessageList().get(0));
				}
				
				
				errorList.add(new int[]{i,2});
				ShowMessage.view("校核["+stationname+"]站["+oprdesc+"]操作发现异常");
				result=false;
				break;
			}
			
		}
		
		DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component cell = super.getTableCellRendererComponent  
                        (table, value, isSelected, hasFocus, row, column);
                boolean isError = false;
                for(int[] c : errorList) {
                	if(c[0] == row && c[1] == column && cell.isBackgroundSet()){ {
                		cell.setForeground(Color.red);
                		isError = true;
                		break;
                	}
                }
                if(!isError)
                	cell.setForeground(Color.black);
                   
              }
                return cell;
            }
        };
		jTable1.getColumnModel().getColumn(2).setCellRenderer(tcr);
		jTable1.updateUI();
		
//		if(result){
//			Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
//			for (int i = allMap.size(); i >0; i--) {
//				DispatchTransDevice dtd = allMap.get(i);
//				DeviceSVGPanelUtil.changeDeviceSVGColor(dtd.getTransDevice());
//			}
//			
//		}
		
		if(result) {
			Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
			for (int i = allMap.size(); i >preSize; i--) {
				allMap.remove(i);
			}
		}
		
		CBSystemConstants.cardstatus="0";
		OperationCheck.loadStation.clear();
		CBSystemConstants.cardbuildtype=sysBuild;
		return result;
	}
	//新增
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.addTableRow(jTable1);
		inittable();
	}
	//删除
		private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
			// TODO add your handling code here:
			/*
			if(CBSystemConstants.cardbuildtype.equals("1")) { //点图票删除任务时要回滚设备状态
				int[] selectRows = jTable1.getSelectedRows();
				if(selectRows.length == 0)
					return;
				else if(selectRows[selectRows.length-1] != jTable1Model.getRowCount()-1) {
					ShowMessage.view(this, "只能从最后一项开始删除！");
					return;
				}
				else {
					for (int i = selectRows.length - 1; i >= 0; i--) {
						if(i > 0) {
							if(selectRows[i] - selectRows[i-1] > 1) {
								ShowMessage.view(this, "不能跨项删除！");
								return;
							}
						}
					}
				}
				for (int i = selectRows.length - 1; i >= 0; i--) {
//					DeviceOperate.RollbackDeviceStatus(jTable1Model.getValueAt(selectRows[i], 2).toString());
//					DeviceOperate.RollbackDeviceStatusStep();
				}
			}
			int allrow=jTable1Model.getRowCount();
			String bz=bzbj.get(bzbj.size()-1);
			if(allrow-1>=0){
				jTable1Model.removeRow(allrow-1);
			}else{
				return;
			}
			if(cimlist.size()>=allrow){
				cimlist.remove(allrow-1);
			}
			
			int sum=0;
			for(int i=0;i<allrow-1;i++){
				if(i<=cimlist.size()){
					if(bz.equals(cimlist.get(i).getBzbj())){
					sum=sum+1;
					}
				}
				
			}
			if(sum==0){
				DeviceOperate.RollbackLastStatusStep(bz);
				if(cmlist.size()-1>0){
					cmlist.remove(cmlist.size()-1);
					bzbj.remove(bzbj.size()-1);
				}else{
					return;
				}
			}
			*/
			WindowUtils.removeTableRow(jTable1);
//			jButton13.setEnabled(false);
			inittable();
		}
		
	//撤销
	private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {
		//if(CBSystemConstants.cardbuildtype.endsWith("1")){
			int all=cimlist.size();
			int sum=0;
			int allrow=jTable1Model.getRowCount();
			
			int bzbj = DeviceOperate.getBzIndex();
			if(bzbj > 0) {
				DeviceOperate.RollbackLastStatusStep(String.valueOf(bzbj));
				for(int i=itemModels.size()-1;i>=0;i--){
					if(itemModels.get(i).getBzbj().equals(String.valueOf(bzbj)))
						itemModels.remove(i);
				}
				DeviceOperate.setBzIndex(bzbj-1);
			}
			
			initTable ();
			
			/*
			String bz=bzbj.get(bzbj.size()-1);
			
			DeviceOperate.RollbackLastStatusStep(bz);

			for(int i=0;i<all;i++){
				if(cimlist.get(i).getBzbj().equals(bz)){
					sum=sum+1;
				}
			}
			if(all==allrow){
				for(int i=all-1;i>all-sum-1;i--){
					jTable1Model.removeRow(i);
					cimlist.remove(i);
					
				}
			}else{
				for(int i=all-1;i>all-sum-1;i--){
					cimlist.remove(i);
				}
				for(int i=allrow-1;i>=all-sum-1;i--){
					jTable1Model.removeRow(i);
				}
			}
			if(cmlist.size()-1>0){
				cmlist.remove(cmlist.size()-1);
				bzbj.remove(bzbj.size()-1);
			}else{
				return;
			}
//			for(int i=allrow;i>allrow-sum;i--){
//				jTable1Model.removeRow(i);
//			}
			*/
//		}
	}
	//上移
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {  //上移方法
		// TODO add your handling code here:
		//if(!CheckWord.isCanMoveUp(jTable1, 2))        //这行我做了修改--黄翔修改    隐藏了
	//		return;                                   // 这行我做了修改 -- 黄翔修改  隐藏了  
		WindowUtils.moveupTableRow(jTable1);
		WindowUtils.paixuTableRow(jTable1,1);
	}
	//下移
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		//if(!CheckWord.isCanMoveDown(jTable1, 2)) //这行我做了修改--黄翔修改    隐藏了
		//	return;// 这行我做了修改 -- 黄翔修改  隐藏了
		WindowUtils.movedownTableRow(jTable1);
		WindowUtils.paixuTableRow(jTable1,1);
	}
	//取消
	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		
		if(isInversing) {
			ShowMessage.view(this, "请先停止操作票演示再关闭！");
			return;
		}
		//回滚
		DeviceOperate.RollbackDeviceStatus();
		//清空
		DeviceOperate.ClearDevMap();
		CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
		CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
    	CBSystemConstants.bztRelationRecord.clear();
		CBSystemConstants.bztStateRecord.clear();
		CBSystemConstants.bztOrganRecord.clear();
			//重新加载系统备自投缓存
			//List relationlist = DBManager
			//		.queryForList("select device_id, change_state from "+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD a,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE b,"+CBSystemConstants.opcardUser+"t_e_equipinfo c where a.device_id =b.relation_id and b.scs_obj=c.equip_id and c.station_id = '"+stationID+"'  ");
	//		List relationlist =DBManager.queryForList("select device_id, change_state from "+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD");
	//		for (int i = 0; i < relationlist.size(); i++) {
	//			Map temp = (Map) relationlist.get(i);
	//			String deviceid = (String) temp.get("device_id");
	//			
	//			int state = Integer.parseInt(temp.get("change_state").toString());
	//			CBSystemConstants.bztRelationRecord.put(deviceid, state);
	//			CBSystemConstants.bztOrganRecord.put(deviceid, state);
	//
	//		}
		this.setVisible(false);
		CBSystemConstants.lcm = null;
		this.tempTicket = null;
		SvgUtil.clear();
		//n=0;
		bccardid=null;
	}
	//合项
	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.mergeTableRow(jTable1, 0);
	}
	//分项
	private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.splitTableRow(jTable1, 0);
	}
	//校核
	private void jButton9ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		checkOperation();
	}
	//演示
	private void jButton12ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if(isInversing) {
			isInversing = false;
			jButton12.setText("演示");
		}
		else {
			isInversing = true;
			jButton12.setText("停止演示");
			inverseOperation();
		}
	}
	
	public void inverseOperation(){
		
		final ArrayList<int[]> errorList = new ArrayList<int[]>();
		errorList.clear();
		DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component cell = super.getTableCellRendererComponent  
                        (table, value, isSelected, hasFocus, row, column);
                boolean isError = false;
                for(int[] c : errorList) {
                	if(c[0] == row && c[1] == column && cell.isBackgroundSet()) { 
                		cell.setForeground(Color.red);
                		isError = true;
                		break;
                	}
                if(!isError)
                	cell.setForeground(Color.black);
                }
                return cell;
            }
        };
		jTable1.getColumnModel().getColumn(4).setCellRenderer(tcr);

	
			
			final Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
			for(int i = allMap.size(); i >= 1; i--) {
				DispatchTransDevice dtd = allMap.get(i);
				dtd.getTransDevice().setDeviceStatus(dtd.getBeginstatus());
				DeviceSVGPanelUtil.changeDeviceSVGColor(dtd.getTransDevice());
			}
			CBSystemConstants.getDtdMap().clear();
			
	    	new Thread(new Runnable() {
				
				public void run() {
					// TODO Auto-generated method stub
					try {
						Thread.sleep(500);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					String sysBuild = CBSystemConstants.cardbuildtype;
					CBSystemConstants.cardbuildtype = "1";
					for(int i = 0; i < jTable1.getRowCount(); i++) {
						
						if(!isInversing) {
							break;
						}
						jTable1.setRowSelectionInterval(i, i);
	
						String czmx = jTable1.getValueAt(jTable1.getSelectedRow(), 4).toString();
		            	String station = jTable1.getValueAt(jTable1.getSelectedRow(), 3).toString();
		            	List<RuleBaseMode> rbmList=OperationCheckDefault.execute(station, czmx);
		            	if(rbmList.size() == 0)
							break;
						else if(!rbmList.get(0).getCheckout()) {
							errorList.add(new int[]{i,4});
							jTable1.clearSelection();
							break;
						}
		        		boolean result = OperationCheckDefault.inverse(rbmList);
		        		if(!result)
		        			break;
		        		try {
							Thread.sleep(4500);
						} catch (InterruptedException e) {
							// TODO Auto-generated catch block
								e.printStackTrace();
							}
						}
						for(int i = 1; i <= allMap.size(); i++) {
							DispatchTransDevice dtd = allMap.get(i);
							dtd.getTransDevice().setDeviceStatus(dtd.getEndstate());
							DeviceSVGPanelUtil.changeDeviceSVGColor(dtd.getTransDevice());
						}
						CBSystemConstants.cardbuildtype = sysBuild;
						isInversing = false;
						jButton12.setText("演示");
					}
				}).start();

	}
	
	public void checkOperation(){
		
		final ArrayList<int[]> errorList = new ArrayList<int[]>();
		errorList.clear();
		DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component cell = super.getTableCellRendererComponent  
                        (table, value, isSelected, hasFocus, row, column);
                boolean isError = false;
                for(int[] c : errorList) {
                	if(c[0] == row && c[1] == column && cell.isBackgroundSet()) { 
                		cell.setForeground(Color.red);
                		isError = true;
                		break;
                	}
                if(!isError)
                	cell.setForeground(Color.black);
                }
                return cell;
            }
        };
		jTable1.getColumnModel().getColumn(4).setCellRenderer(tcr);
		
		new Thread(new Runnable() {
			
			public void run() {
				// TODO Auto-generated method stub
				final int preSize = DeviceOperate.getAlltransDevMap().size();
				final String sysBuild = CBSystemConstants.cardbuildtype;
				final String sysStatus = CBSystemConstants.cardstatus;
				
				CBSystemConstants.cardstatus = "1";
				CBSystemConstants.cardbuildtype = "1";
				
				CreatePowerStationToplogy.loadSysData();
				
				List<String> loadStationList = new ArrayList<String>();
				
				for(int i = 0; i < jTable1.getRowCount(); i++) {
					
					jTable1.setRowSelectionInterval(i, i);
					String stationName = StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
					String oprdesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
					List<RuleBaseMode> rbmList=OperationCheckDefault.execute(stationName, oprdesc);
					if(rbmList.size() == 0)
						break;
					else if(!rbmList.get(0).getCheckout()) {
						errorList.add(new int[]{i,4});
						jTable1.clearSelection();
						break;
					}
					
					boolean isSuccess = true;
					for(RuleBaseMode rbm : rbmList) {
						
						String stationID = rbm.getPd().getPowerStationID();
						if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
							CreatePowerStationToplogy.loadFacEquip(stationID);
						}
						if(!loadStationList.contains(stationID)) {
							InitDeviceStatus ie = new InitDeviceStatus();
							ie.initStatus_EMSToCache(stationID);
							loadStationList.add(stationID);
						}
						isSuccess = OperationCheckDefault.check(rbm);
						if(!isSuccess) {
							errorList.add(new int[]{i,4});
							jTable1.clearSelection();
							break;
						}
					}
					if(!isSuccess)
						break;
				}
				
				
				Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
				for (int i = allMap.size(); i >preSize; i--) {
					allMap.remove(i);
				}
				for(int i = 1; i <= allMap.size(); i++) {
					DispatchTransDevice dtd = allMap.get(i);
					dtd.getTransDevice().setDeviceStatus(dtd.getEndstate());
				}
				
				CBSystemConstants.cardstatus = sysStatus;
				CBSystemConstants.cardbuildtype = sysBuild;
			}
		}).start();
	}
	public void checkOpsStep(){
		final String sysBuild = CBSystemConstants.cardbuildtype;
		final int preSize = DeviceOperate.getAlltransDevMap().size();
		final ArrayList<int[]> errorList = new ArrayList<int[]>();
		errorList.clear();
		DefaultTableCellRenderer tcr = new DefaultTableCellRenderer() {
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component cell = super.getTableCellRendererComponent  
                        (table, value, isSelected, hasFocus, row, column);
                boolean isError = false;
                for(int[] c : errorList) {
                	if(c[0] == row && c[1] == column && cell.isBackgroundSet()) { 
                		cell.setForeground(Color.red);
                		isError = true;
                		break;
                	}
                if(!isError)
                	cell.setForeground(Color.black);
                }
                return cell;
            }
        };
		jTable1.getColumnModel().getColumn(2).setCellRenderer(tcr);
		new Thread(new Runnable() {
			public void run() {
				// TODO Auto-generated method stub
				try {
					boolean result=true;
					List<PowerDevice> pdList = new ArrayList<PowerDevice>();
					for (int i = 0; i < jTable1.getRowCount(); i++) {
						jTable1.setRowSelectionInterval(i, i);
						//JudgeDoWhat jdw = new JudgeDoWhat();
						//String czmx = jTable1.getValueAt(i, 2).toString();
						//String station = jTable1.getValueAt(i, 1).toString();
				    	//jdw.excute(czmx, jTextArea1.getText(), String.valueOf(i+1), station, null);
						String stationname = StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
						if((stationname.indexOf("（")>=0&&stationname.indexOf("）")>=0)||stationname.equals("")){
							for(int j=i;j>=0;j--){
								stationname = StringUtils.ObjToString(jTable1Model.getValueAt(j, 3)).trim();
								if((stationname.indexOf("（")>=0&&stationname.indexOf("）")>=0)||stationname.equals("")){
									continue;
								}else{
									break;
								}
							}
						}
						String oprdesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
						if(oprdesc.equals("")){
							continue;
						}
						if(oprdesc.indexOf("报：") >= 0)
							break;
						RuleBaseMode b=OperationCheck.execute(stationname, oprdesc);
						/*
						Map<Integer, DispatchTransDevice> dtdMap = CBSystemConstants.getDtdMap();
						for(PowerDevice pd : pdList) {
							ChooseDeviceRectFlashingAction cdrfa = new ChooseDeviceRectFlashingAction(pd);
						    cdrfa.backexecute();
						}
						pdList.clear();
						for (Iterator iter = dtdMap.values().iterator();iter.hasNext();) { 
							DispatchTransDevice dtd = (DispatchTransDevice) iter.next(); 
						    PowerDevice pd = (PowerDevice)dtd.getTransDevice();
						    if(!pdList.contains(pd))
						    	pdList.add(pd);
						}
						for(PowerDevice pd : pdList) {
							ChooseDeviceRectFlashingAction cdrfa = new ChooseDeviceRectFlashingAction(pd);
						    cdrfa.execute();
						}
						*/
						
						if(!b.getCheckout()){
							if(b.getMessageList().size()!=0){
								ShowMessage.view(b.getMessageList().get(0));
							}
							errorList.add(new int[]{i,2});
							//jTable1.updateUI();
							//ShowMessage.view("校核["+stationname+"]站["+oprdesc+"]操作发现异常");
							jTable1.clearSelection();
							result=false;
							break;
						}
						Thread.sleep(500);
					}
					if(result) {
						Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
						for (int i = allMap.size(); i >preSize; i--) {
							allMap.remove(i);
						}
					}
					CBSystemConstants.cardstatus="0";
					OperationCheck.loadStation.clear();
					CBSystemConstants.cardbuildtype=sysBuild;
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}).start();
	}
	//替换
	private void jButton10ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		ReplaceWord rw = new ReplaceWord(SystemConstants.getMainFrame(), false, jTable1);
    	rw.setVisible(true);
	}
	private void inittable(){
		if(jTable1Model!=null){
			int n=jTable1Model.getRowCount();
			for(int i=0;i<n;i++){
				if(jTable1Model.getValueAt(i, 1) == null) {
					if(i == 0)
						jTable1Model.setValueAt(1, i, 1);
					else
						jTable1Model.setValueAt(Integer.valueOf(jTable1Model.getValueAt(i-1, 1).toString())+1, i, 1);
				}
				if(jTable1Model.getValueAt(i, 2) == null) {
					if(i == 0)
						jTable1Model.setValueAt(1, i, 2);
					else
						jTable1Model.setValueAt(Integer.valueOf(jTable1Model.getValueAt(i-1, 2).toString())+1, i, 2);
				}
			}
		}
	}
	private RuleBaseMode Srcrbm;
	private List<CardItemModel> cimlist=new ArrayList<CardItemModel>();
	//private List<String> bzbj=new ArrayList<String>();
	
	
	public void init(CardModel cm,RuleBaseMode Srcrbm) {
		if (cm == null)
			return;

		this.Srcrbm=Srcrbm;
		//cmlist.add(cm);
		for(int i=0;i<cm.getCardItems().size();i++){
			cimlist.add(cm.getCardItems().get(i));
		}
		//if(cm.getCardItems().size() > 0)
		//	bzbj.add(cm.getCardItems().get(0).getBzbj());
		Object[][] tableDate = null;
		if (jTable1Model == null)
			jTable1Model = new DefaultTableModel(tableDate, new String[] {
					"","序号","顺序", "操作单位", "操作内容" });

		if (jTable1 == null)
			jTable1 = new JTable();
		jTable1.setModel(jTable1Model);
		
		//itemModels.addAll(cm.getCardItems());
		
		//bzbj++;
		
		itemModels.clear();
		List<CardItemModel> DescList = getItemModel();
		for(CardItemModel cim : DescList) {
			if(!cim.getCardDesc().equals(""))
				itemModels.add(cim);
		}
		
		
		for(CardItemModel cim: cm.getCardItems()) {
			//cim.setBzbj(String.valueOf(bzbj));
			itemModels.add(cim);
		}
		
		initTable ();
		

		
		jTable1.addMouseListener(new MouseAdapter() {
			@Override
			public void mouseClicked(MouseEvent e) {
				// TODO Auto-generated method stub
				Point mousepoint; 
				mousepoint =e.getPoint();
				int row=jTable1.rowAtPoint(mousepoint);
				int column=jTable1.columnAtPoint(mousepoint);
				if(jTable1.editCellAt(row, column)){
					 Component editor = jTable1.getEditorComponent();
					    editor.requestFocusInWindow();
					    Component c = editor.getComponentAt(0, 0);
					    if (c != null && c instanceof JTextComponent) {
					        ((JTextComponent) c).selectAll();
					        tjc=(JTextComponent)c;
					    }
				}
			}
		});
	
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);

		DefaultCellEditor cellEdit = new DefaultCellEditor(new JTextField());
		cellEdit.setClickCountToStart(2);//双击后使选择的格子可编辑
		jTable1.getColumnModel().getColumn(1).setMinWidth(50);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(60);
		jTable1.getColumnModel().getColumn(3).setMinWidth(120);
		jTable1.getColumnModel().getColumn(3).setMaxWidth(140);
		jScrollPane2.setViewportView(jTable1);
		//拟票时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date dt = new Date();
		jLabel3.setText(sdf.format(dt));
		//拟票人
		jLabel4.setText(CBSystemConstants.getUser().getUserName());
		//操作任务
		if(CBSystemConstants.roleCode.equals("0")) {
			if (!cm.getCzrw().equals("") && jTextArea1.getText().equals(""))
				jTextArea1.setText(cm.getCzrw());
		}
		else if(CBSystemConstants.roleCode.equals("2")) {
			if (!cm.getCzrw().equals(""))
				jTextArea1.setText(cm.getCzrw());
		}
		
	
		
		jTable1.getColumnModel().getColumn(0).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(0).setMinWidth(0);
		jTable1.getColumnModel().getColumn(0).setPreferredWidth(0);
		jTable1.getColumnModel().getColumn(2).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(2).setMinWidth(0);
		jTable1.getColumnModel().getColumn(2).setPreferredWidth(0);
		if("1".equals(CBSystemConstants.unitCode)){
			jTable1.getColumnModel().getColumn(3).setMaxWidth(0);
			jTable1.getColumnModel().getColumn(3).setMinWidth(0);
			jTable1.getColumnModel().getColumn(3).setPreferredWidth(0);
			jTable1.getColumnModel().getColumn(3).setResizable(false);
		}
		this.setVisible(true);
	}
	
	public void initTable () {
		
		List<CardItemModel> itemModelsShow = new ArrayList<CardItemModel>();
		
		
		
		for(CardItemModel cim : itemModels) {
			itemModelsShow.add(cim);
		}
		//itemModels.addAll(itemModelsAll);
		
	
		
		//设置顺序
		String usedNum = "1";
		for (int i = 0; i < itemModelsShow.size(); i++) {
			if(itemModelsShow.get(i).getCardItem().equals("")) {
				if(i == 0)
					itemModelsShow.get(i).setCardItem("1");
				else
//					itemModelsShow.get(i).setCardItem(itemModelsShow.get(i-1).getCardItem());
					itemModelsShow.get(i).setCardItem(String.valueOf(i+1));
			}
			
			if(itemModelsShow.get(i).getCardItem().equals(usedNum)&&!itemModelsShow.get(i).getCardNum().equals(usedNum)){
				itemModelsShow.get(i).setCardNum("");
			}else if(!itemModelsShow.get(i).getCardItem().equals(usedNum)){
				usedNum = itemModelsShow.get(i).getCardItem() ;
				itemModelsShow.get(i).setCardNum(usedNum);
			}
//			itemModelsShow.get(i).setCardNum(String.valueOf(i+1));
		}
		
		//DescList.addAll(cm.getCardItems());
		//List<CardItemModel> DescList = cm.getCardItems();
		while(jTable1Model.getRowCount() != 0) {
			jTable1Model.removeRow(jTable1Model.getRowCount()-1);
		}
		if (itemModelsShow != null) {
			for (int i = 0; i < itemModelsShow.size(); i++) {
				CardItemModel cim = itemModelsShow.get(i);
				//CodeNameModel cnm=new CodeNameModel(bcm.getUuIds(),bcm.getCardNum());
				Object[] rowData = { cim, cim.getCardNum(),cim.getCardItem(),cim.getShowName(), cim.getCardDesc() };
				jTable1Model.addRow(rowData);
			}
		}
	}
	
	public void init (String[] zb,List<BaseCardModel> mx) {
		int startItem = 0;
		this.Srcrbm=Srcrbm;
		Object[][] tableDate = null;
		if (jTable1Model == null)
			jTable1Model = new DefaultTableModel(tableDate, new String[] {
					"顺序", "操作单位", "操作内容" });
		else if(jTable1Model.getRowCount() > 0) {
			startItem = Integer.valueOf(((CodeNameModel)jTable1Model.getValueAt(jTable1Model.getRowCount()-1, 0)).getName());
		}
		
		for (int i = 0; i < mx.size(); i++) {
			BaseCardModel bcm = mx.get(i);
			CodeNameModel cnm=new CodeNameModel(bcm.getMxid(),bcm.getCardSub());
			Object[] rowData = { cnm, bcm.getStationName(), bcm.getCardDesc() };
			jTable1Model.addRow(rowData);
		}
		
		if (jTable1 == null)
			jTable1 = new JTable();
		jTable1.setModel(jTable1Model);
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);

		DefaultCellEditor cellEdit = new DefaultCellEditor(new JTextField());
		cellEdit.setClickCountToStart(2);//双击后使选择的格子可编辑
		jTable1.getColumnModel().getColumn(0).setMinWidth(50);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(60);
		jTable1.getColumnModel().getColumn(1).setMinWidth(100);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
		jScrollPane2.setViewportView(jTable1);
		
		//拟票时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date dt = new Date();
		jLabel3.setText(sdf.format(dt));
		//拟票人
		jLabel4.setText(CBSystemConstants.getUser().getUserName());
		//操作任务
		if (jTextArea1.getText().trim().equals(""))
			jTextArea1.setText(zb[2]);
		
		boolean isMultiUnit = false;
		String preStationName = "";
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			String stationName = jTable1.getValueAt(i, 1).toString();
			if(!stationName.equals("")) {
				if(!preStationName.equals("") && !preStationName.equals(stationName)) {
					isMultiUnit = true;
					break;
				}
				preStationName = stationName;
			}
		}
		if(isMultiUnit)
			lblNewLabel.setText("调控中心");
		else{
			if("0".equals(CBSystemConstants.unitCode)){
				lblNewLabel.setText(preStationName);
			}else{
				lblNewLabel.setText("调控中心("+preStationName+")");
			}
		}
		if("1".equals(CBSystemConstants.unitCode)){
			jTable1.getColumnModel().getColumn(1).setMaxWidth(0);
			jTable1.getColumnModel().getColumn(1).setMinWidth(0);
			jTable1.getColumnModel().getColumn(1).setPreferredWidth(0);
			jTable1.getColumnModel().getColumn(1).setResizable(false);
		}
		this.setVisible(true);
	}
	
	public List<CardItemModel> getCimlist() {
		return cimlist;
	}

	public void setCimlist(List<CardItemModel> cimlist) {
		this.cimlist = cimlist;
	}
	
	private JLabel jLabel1;
	private JLabel jLabel2;
	private JLabel jLabel3;
	private JLabel jLabel4;
	private JTextArea jTextArea1;
	private JCheckBox jCheckBox1;
	private JButton jButton1;
	private JButton jButton2;
	private JButton jButton3;
	private JButton jButton4;
	private JButton jButton5;
	private JButton jButton6;
	private JButton jButton7;
	private JButton jButton8;
	private JButton jButton9;
	private JButton jButton10;
	private JButton jButton11;
	private JButton jButton12;
	private javax.swing.JLabel jLabel5;
	//private javax.swing.JTextField jTextField2;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private DefaultTableModel jTable1Model = null;
	private JLabel lblNewLabel;
	private JLabel lblNewLabel_1;
	private JButton btnoms;
	private JPanel panel_5;
	private JLabel lblNewLabel_2;
	private JTextField textField;
	private JButton btnNewButton;
	private JLabel lblNewLabel_3;
	private JTextField textField_1;
	private JButton btnNewButton_1;
}
