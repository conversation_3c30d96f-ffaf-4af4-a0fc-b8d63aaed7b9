package com.tellhow.czp.operationcard;

import java.awt.BorderLayout;
import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;

import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;
/**
 * @典型票明细展示
 * <AUTHOR>
 * 修改：王豪
 */
public class OperateTicketDXPMXFS extends JDialog {
	private CodeNameModel cnm;
	private String czrw;
	private DefaultTableModel jTable1Model = null;
	private SetJTableProtery sjp = new SetJTableProtery();
	
	private final JPanel contentPanel = new JPanel();
	private JTable jTable1;
	//.............................
	/**
	 * Launch the application.
	 */
	/** Creates new form PreviewOperateTicket */
	public OperateTicketDXPMXFS(java.awt.Frame parent, CodeNameModel cnm,String czrw) {
		super(parent, "操作票编辑", true);
		this.cnm = cnm;
		this.czrw = czrw;
		initComponents();
		setLocationCenter();
		init();
	}
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	private void inittable(){
		if(jTable1Model!=null){
			int n=jTable1Model.getRowCount();
			for(int i=0;i<n;i++){
				jTable1Model.getValueAt(i, 0);
				jTable1Model.setValueAt(i+1, i, 0);
			}
		}
	}
	private void init() {
		this.jTextArea1.setText(this.czrw);
		if (jTable1Model == null) {
			Object[][] tableData = null;
			jTable1Model = new DefaultTableModel(tableData, new String[] {
					"序号", "顺序", "操作单位", "操作内容" }) {
				public boolean isCellEditable(int rowIndex, int columnIndex) {
					if (columnIndex != 0)
						return true;
					else
						return false;
				}
			};
		}
		TicketDBManager tdb = new TicketDBManager();
		List<String[]> results = tdb.queryDXTicketMX(this.cnm.getCode());
		String[] tempStr = null;
		for (int i = 0; i < results.size(); i++) {
			tempStr = results.get(i);
			Object[] rowData = {tempStr[1], tempStr[2], tempStr[5], tempStr[4]};
			jTable1Model.addRow(rowData);
		}
		jTable1.setModel(jTable1Model);
		sjp.makeFace(jTable1);
		TableColumnModel tcm = jTable1.getColumnModel();
		//num
		tcm.getColumn(0).setMaxWidth(50);
		tcm.getColumn(0).setMinWidth(50);
//				tcm.getColumn(0).setPreferredWidth(0);
		//item
		tcm.getColumn(1).setMaxWidth(0);
		tcm.getColumn(1).setMinWidth(0);
		tcm.getColumn(1).setPreferredWidth(0);
		tcm.getColumn(1).setResizable(false);
		//
		tcm.getColumn(2).setMaxWidth(120);
		tcm.getColumn(2).setMinWidth(120);
	}
	private void initComponents() {
		setBounds(100, 100, 591, 417);
		getContentPane().setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		getContentPane().add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(new BorderLayout(0, 0));
		{
			JPanel panel = new JPanel();
			contentPanel.add(panel, BorderLayout.NORTH);
			panel.setLayout(new BorderLayout(0, 0));
			{
				JLabel label = new JLabel("\u64CD\u4F5C\u4EFB\u52A1\uFF1A");
				panel.add(label, BorderLayout.WEST);
			}
			{
				JPanel panel_1 = new JPanel();
				panel.add(panel_1, BorderLayout.SOUTH);
				panel_1.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_2 = new JPanel();
					panel_1.add(panel_2, BorderLayout.EAST);
					{
						jButton3 = new JButton();
						jButton3.setText("保存");
						jButton3.setMargin(new java.awt.Insets(1,1,1,1));
						jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/save.gif")));
						jButton3.setToolTipText("保存");
						jButton3.setFocusPainted(false);
						jButton3.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton3ActionPerformed(evt);
							}
						});
						{
							jButton8 = new JButton();
					        jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/network.gif"))); // NOI18N
					        jButton8.setText("转至拟票");
					        jButton8.setToolTipText("转至拟票");
					        jButton8.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton8.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
					        jButton8.setFocusPainted(false);
					        jButton8.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					            	jButton8ActionPerformed(evt);
					            }
					        });
							panel_2.add(jButton8);
						}
						panel_2.add(jButton3);
					}
					{
						jButton1 = new JButton();
						jButton1.setText("增加");
						jButton1.setMargin(new java.awt.Insets(1,1,1,1));
						jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/add.png"))); // NOI18N
						jButton1.setToolTipText("\u589e\u52a0");
						jButton1.setFocusPainted(false);
						jButton1.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton1ActionPerformed(evt);
							}
						});
						panel_2.add(jButton1);
					}
					{
						jButton2 = new JButton();
						jButton2.setText("删除");
						jButton2.setMargin(new java.awt.Insets(1,1,1,1));
						jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/delete.png"))); // NOI18N
						jButton2.setToolTipText("\u5220\u9664");
						jButton2.setFocusPainted(false);
						jButton2.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton2ActionPerformed(evt);
							}
						});
						panel_2.add(jButton2);
					}
					{
						jButton4 = new JButton();
						jButton4.setText("上移");
						jButton4.setMargin(new java.awt.Insets(1,1,1,1));
						jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/btn_up.png"))); // NOI18N
						jButton4.setToolTipText("\u4e0a\u79fb");
						jButton4.setFocusPainted(false);
						jButton4.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton4ActionPerformed(evt);
							}
						});
						panel_2.add(jButton4);
					}
					{
						jButton5 = new JButton();
						jButton5.setText("下移");
						jButton5.setMargin(new java.awt.Insets(1,1,1,1));
						jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/btn_down.png"))); // NOI18N
						jButton5.setToolTipText("\u4e0b\u79fb");
						jButton5.setFocusPainted(false);
						jButton5.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton5ActionPerformed(evt);
							}
						});
						panel_2.add(jButton5);
					}
					{
						jButton6 = new JButton();
						jButton6.setText("合项");
						jButton6.setMargin(new java.awt.Insets(1,1,1,1));
						jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/merge.png"))); // NOI18N
						jButton6.setToolTipText("合项");
						jButton6.setFocusPainted(false);
						jButton6.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton6ActionPerformed(evt);
							}
						});
						panel_2.add(jButton6);
					}
					{
						jButton7 = new JButton();
						jButton7.setText("分项");
						jButton7.setMargin(new java.awt.Insets(1,1,1,1));
						jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/split.png"))); // NOI18N
						jButton7.setToolTipText("分项");
						jButton7.setFocusPainted(false);
						jButton7.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton7ActionPerformed(evt);
							}
						});
						panel_2.add(jButton7);
					}
				}
			}
			{
				jScrollPane2 = new JScrollPane();
				panel.add(jScrollPane2, BorderLayout.CENTER);
				{
					jTextArea1 = new JTextArea();
					jTextArea1.setColumns(20);
					jTextArea1.setFont(new java.awt.Font("宋体", 1, 14));
					jScrollPane2.setViewportView(jTextArea1);
				}
			}
		}
		{
			jScrollPane1 = new JScrollPane();
			contentPanel.add(jScrollPane1, BorderLayout.CENTER);
			{
				jTable1 = new JTable();
				jTable1.setRowHeight(26);
				jTable1.setFont(new java.awt.Font("宋体", 0, 13));
				jScrollPane1.setViewportView(jTable1);
			}
		}
	
	}
	//保存
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		int row = jTable1.getRowCount();
		if (row == 0) {
			ShowMessage.view("数据为空,保存失败！");
			return;
		}
		if (jTextArea1.getText().trim().equals("")) {
			ShowMessage.view("操作任务不能为空,保存失败！");
			return;
		}
		//单元格编辑状态下点击保存
		if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
		String czrw = jTextArea1.getText().trim();//编辑修改后的操作任务名称
		List<CardItemModel> DescList = new ArrayList<CardItemModel>();
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			CardItemModel cim = new CardItemModel();
			cim.setCardNum(String.valueOf(i+1));
			cim.setCardItem(StringUtils.ObjToString(jTable1.getValueAt(i, 1)).trim());
			cim.setStationName(StringUtils.ObjToString(jTable1.getValueAt(i, 2)).trim());
			cim.setCardDesc(StringUtils.ObjToString(jTable1.getValueAt(i, 3)).trim());
			DescList.add(cim);
		}
		//保存设备术语，设备预令状态
		TicketDBManager ticketDB = new TicketDBManager();
		ticketDB.updateTicketDB(cnm.getCode(), DescList, czrw);
	}
	//新增
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		WindowUtils.addTableRow(jTable1);
		inittable();
	}
	//删除
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		WindowUtils.removeTableRow(jTable1);
		WindowUtils.paixuTableRow(jTable1,1);
		inittable();
	}
	//上移
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		WindowUtils.moveupTableRow(jTable1);
		WindowUtils.paixuTableRow(jTable1,1);
	}
	//下移
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		WindowUtils.movedownTableRow(jTable1);
		WindowUtils.paixuTableRow(jTable1,1);
	}
	//分项
	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {
		WindowUtils.splitTableRow(jTable1, 1);
	}
	
	//合项
	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
		WindowUtils.mergeTableRow(jTable1, 1);
	}
    //转至拟票
    private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {                                         
    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.0);
    	OperateTicketSGPDefault otsgp = new OperateTicketSGPDefault();
    	splitPane.setRightComponent(otsgp);
    	cnm.setName(czrw);
    	otsgp.initTable(cnm);
		this.setVisible(false);
    } 
	
	private JButton jButton1=null;
	private JButton jButton2=null;
	private JButton jButton3=null;
	private JButton jButton4=null;
	private JButton jButton5=null;
	private JButton jButton6=null;
	private JButton jButton7=null;
	private JButton jButton8=null;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private JTextArea jTextArea1=null;
}
