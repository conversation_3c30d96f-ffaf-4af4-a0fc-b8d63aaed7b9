package com.tellhow.czp.operationcard;

import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

import javax.swing.JPanel;

import com.tellhow.czp.app.CZPImpl;

public abstract class OperateTicketDXP  extends JPanel {
	public static OperateTicketDXP instance;
	public static OperateTicketDXP getInstance(){
		if(instance == null){
			instance=(OperateTicketDXP)CZPImpl.getInstance("OperateTicketDXP");
			if(instance == null)
				instance = new OperateTicketDXPDefault();
				return instance;
		}
		else
			return instance;
	}
	
	public abstract void initDXP();
}

