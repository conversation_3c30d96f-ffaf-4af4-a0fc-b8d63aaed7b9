package com.tellhow.czp.operationcard;

import javax.swing.JPanel;

import com.tellhow.czp.app.CZPImpl;

public abstract class OperateTicketTypePanel extends JPanel{
	 static OperateTicketTypePanel ticketTypePanel;
	 public static OperateTicketTypePanel getInstance() {
			if (ticketTypePanel == null) {
				ticketTypePanel=(OperateTicketTypePanel)CZPImpl.getInstance("OperateTicketTypePanel");
				if(ticketTypePanel == null)
					ticketTypePanel = new OperateTicketTypePanelDefault();

				return ticketTypePanel;
			}
			else
				return ticketTypePanel;
	 }
	 public abstract int initTable();
	 
	 public void settab(int tab) {
		// TODO Auto-generated method stub
		
	 }
}
