/*
 * HistoryCardEdit.java
 *
 * Created on 2010年2月8日, 上午11:12
 */

package com.tellhow.czp.operationcard;

import java.awt.Color;
import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import javax.swing.text.AttributeSet;
import javax.swing.text.BadLocationException;
import javax.swing.text.Document;
import javax.swing.text.SimpleAttributeSet;
import javax.swing.text.StyleConstants;


public class HistoryCardEdit extends javax.swing.JDialog {
    private List oldCards=new ArrayList();
    /** Creates new form HistoryCardEdit */
    public HistoryCardEdit(java.awt.Frame parent, boolean modal) {
    	super(parent, modal);
        initComponents();
        this.setTitle("文档导入");
        int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
        int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
        this.setLocation((w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
    }
    
    /** This method is called from within the constructor to
     * initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is
     * always regenerated by the Form Editor.
     */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jScrollPane1 = new javax.swing.JScrollPane();
        jEditorPane1 = new javax.swing.JTextPane();
        jButton1 = new javax.swing.JButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

        jEditorPane1.setBackground(new java.awt.Color(255, 255, 255));
        jEditorPane1.setFont(new java.awt.Font("宋体", 0, 13));
        jScrollPane1.setViewportView(jEditorPane1);
        jButton1.setText("生成操作票");
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 562, Short.MAX_VALUE)
            .add(layout.createSequentialGroup()
                .addContainerGap()
                .add(jButton1)
                .addContainerGap(459, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(org.jdesktop.layout.GroupLayout.TRAILING, layout.createSequentialGroup()
                .addContainerGap(30, Short.MAX_VALUE)
                .add(jButton1)
                .add(18, 18, 18)
                .add(jScrollPane1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 432, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton1ActionPerformed

             String conter=this.jEditorPane1.getText().trim();
         
             while(true){
                 if(conter.indexOf("  ")>0)
                    conter=conter.replaceAll("  ", " ");
                 else
                     break;
             } 
             oldCards.clear();
             String[] rowStrs=conter.split("\r\n");
             String rowStr="";
             for (int i = 0; i < rowStrs.length; i++) {
                rowStr= rowStrs[i].trim();
                if(!rowStr.trim().equals("")){
                    if(rowStr.indexOf(" ")<=0){
                    	 jEditorPane1.setText("");
                        javax.swing.JOptionPane.showMessageDialog(rootPane, "操作票样板第"+(i+1)+"条格式不正确,缺少分隔符号!");
                        reSetText(rowStrs);
                        oldCards.clear();
                        return;
                     }
                     String[] colStrs=rowStr.split(" ");
                     if(colStrs.length!=2){
                    	 jEditorPane1.setText("");
                        javax.swing.JOptionPane.showMessageDialog(rootPane, "操作票样板第"+(i+1)+"条格式不正确,可能存在多个分隔符号!");
                        reSetText(rowStrs);
                        oldCards.clear();
                        return;
                     }
                     oldCards.add(colStrs);
                 }
             }      
             this.setVisible(false);
             this.dispose();
    }//GEN-LAST:event_jButton1ActionPerformed
    public List getOldCards(){
        return this.oldCards;
    }
    /**
     * @param args the command line arguments
     */
/*    public static void main(String args[]) {
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                HistoryCardEdit dialog = new HistoryCardEdit(new javax.swing.JFrame(), true);
                dialog.addWindowListener(new java.awt.event.WindowAdapter() {
                    public void windowClosing(java.awt.event.WindowEvent e) {
                        System.exit(0);
                    }
                });
                dialog.setVisible(true);
            }
        });
    }*/
    public   void   insert(String   str,   AttributeSet   attrSet)   {   
        Document   doc   =   jEditorPane1.getDocument(); 
        str = str+"\n";
        try   {   
            doc.insertString(doc.getLength(),str,attrSet);   
        }   
        catch   (BadLocationException   e)   {   
            System.out.println( "BadLocationException:   "   +   e);   
        }   
    }   
    public   void   setDocs(String   str,Color   col,boolean   bold,int   fontSize)   {
        SimpleAttributeSet   attrSet   =   new   SimpleAttributeSet();   
        StyleConstants.setForeground(attrSet,col);   
        //颜色   
        if(bold==true){   
            StyleConstants.setBold(attrSet,   true);   
        }//字体类型   
        StyleConstants.setFontSize(attrSet,   fontSize);   
        //字体大小   
        insert(str,attrSet);   
    }   
    public void reSetText(String[] rowStrs){
    	String rowStr;
    	  for(int j = 0; j < rowStrs.length; j++){
    		  rowStr= rowStrs[j].trim();
    		  String[] colStrs=rowStr.split(" ");
         	 if(!rowStr.trim().equals("")){
                  if(rowStr.indexOf(" ")<=0||colStrs.length!=2){
                 	 setDocs(rowStr.trim(),Color.RED,true,13);  
                  }else{
                 	 setDocs(rowStr.trim(),Color.black,false,13);
                  }
         	 }
         }
    }
    
    
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton jButton1;
    private javax.swing.JTextPane jEditorPane1;
    private javax.swing.JScrollPane jScrollPane1;
    // End of variables declaration//GEN-END:variables
    
}
