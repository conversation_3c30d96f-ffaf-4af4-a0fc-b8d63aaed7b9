package com.tellhow.czp.operationcard;

import java.util.List;

import javax.swing.JDialog;

import com.tellhow.czp.app.CZPImpl;

import czprule.model.CheckMessage;
import czprule.rule.model.RuleBaseMode;

/**
 * 防误提示界面。 VOS humanerror preventive view dailog .
 * 
 * <AUTHOR>
 * 
 */
public abstract class VOSViewPanel extends JDialog {
	public static VOSViewPanel instance;

	public static VOSViewPanel getInstance() {
		instance = null;
		if (instance == null) {
			instance = (VOSViewPanel) CZPImpl.getInstance("VOSViewPanel");
			if (instance == null)
				instance = new VOSViewPanelDefault();
			return instance;
		} else
			return instance;
	}

	public VOSViewPanel(java.awt.Frame parent, boolean isModel) {
		super(parent, isModel);
	}
	public abstract void initData(RuleBaseMode rmb);
	public abstract void initData(RuleBaseMode rbm, List<CheckMessage> lcm);
	public VOSViewPanel() {
	}
	public abstract  boolean isOk() ;
}
