package com.tellhow.czp.operationcard;

import java.awt.Color;
import java.awt.Component;

import javax.swing.ImageIcon;
import javax.swing.JLabel;
import javax.swing.JTable;
import javax.swing.table.TableCellRenderer;

import czprule.rule.model.RuleBaseMode;

public class VOSViewCellRenderer  implements TableCellRenderer{
	RuleBaseMode rbm = null;
	   public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column)  {
	          //根据特定的单元格设置不同的Renderer,假如你要在第2行第3列显示图标
		   
		   Object obj = table.getValueAt(row, column);
		   if(obj != null && obj instanceof RuleBaseMode)
			   rbm = (RuleBaseMode)obj;
		   else
			   rbm = null;
		   ImageIcon icon = null;
		   if(rbm == null)
			   icon = null;
		   else if(rbm.getCheckResult()==0) 
			   icon = new ImageIcon(getClass().getResource("/tellhow/icons/tongguo.png"));
		   else if(rbm.getCheckResult()==1) 
			   icon = new ImageIcon(getClass().getResource("/tellhow/icons/jinggao.png"));
		   else if(rbm.getCheckResult()==2) 
			   icon = new ImageIcon(getClass().getResource("/tellhow/icons/butongguo.png"));
		   else if(rbm.getCheckResult()==100) 
			   icon = new ImageIcon(getClass().getResource("/tellhow/icons/bukekong.png"));
		   else
				icon = null;
    JLabel label = new JLabel(icon);
    label.setOpaque(true);
    if (row % 2 == 0)
 	   label.setBackground(Color.white); // 设置奇数行底色
		else if (row % 2 == 1)
			label.setBackground(new Color(206, 231, 255)); // 设置偶数行底色
    //label.setForeground(new Color(206, 231, 255));
    return label;
	}
}

