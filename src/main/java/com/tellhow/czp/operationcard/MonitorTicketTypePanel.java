/*
 * OperateTicketTypePanelbak.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.operationcard;

import java.awt.Cursor;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPopupMenu;
import javax.swing.JSplitPane;
import javax.swing.SwingUtilities;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;
import javax.swing.table.TableModel;
import javax.swing.table.TableRowSorter;

import org.jfree.report.JFreeReportBoot;

import com.tellhow.czp.Robot.CzpRobot;
import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.PrintOperationCard;
import com.tellhow.czp.app.service.OMSService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.basic.ReaderConfiguration;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.czp.user.User;
import com.tellhow.czp.user.UserDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.DateUtil;

import czprule.datemodule.JCalendarPanel;
import czprule.model.CodeNameModel;
import czprule.securitycheck.view.CheckDialog;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.view.InitDeviceTypeChockBox;

/**
 * @监控票 和整个窗口的框架
 * <AUTHOR>
 */
public abstract class MonitorTicketTypePanel extends javax.swing.JPanel {
	static MonitorTicketTypePanel ticketTypePanel;
	 public static MonitorTicketTypePanel getInstance() {
			if (ticketTypePanel == null) {
				ticketTypePanel=(MonitorTicketTypePanel)CZPImpl.getInstance("MonitorTicketTypePanel");
				if(ticketTypePanel == null)
					ticketTypePanel = new MonitorTicketTypePanelDefault();

				return ticketTypePanel;
			}
			else
				return ticketTypePanel;
	 }
	 public abstract int initTable(); 
	 
	 public void settab(int tab) {
		// TODO Auto-generated method stub
		
	 }

}
