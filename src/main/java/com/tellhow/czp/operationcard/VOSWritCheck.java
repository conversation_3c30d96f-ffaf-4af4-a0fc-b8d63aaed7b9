package com.tellhow.czp.operationcard;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.operationcard.EchoReplace;
import com.tellhow.czp.service.OperationCheckDefault;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.stationstartup.InitDeviceStatus;
import czprule.stationstartup.StationStartupManager;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;

/**
 * 校核执行类
 * 
 * <AUTHOR>
 * 
 */
public class VOSWritCheck {

	private JTable jTable = null;// 操作指令表
	private JTextArea jTextArea = null;// 操作任务
	int dwI;//操作单位所在列
	int zlI;//操作指令所在列
	int jhI;//校核所在列
	boolean isSuccess = true;//操作票校验结果
	 EchoReplace ec = new EchoReplace();
	
	public VOSWritCheck(JTable jTable, JTextArea jTextArea) {
		this.jTable = jTable;
		this.jTextArea = jTextArea;
		excute();
	}
	/**
	 * 执行类
	 */
	private void excute() {
			for(int i = 0;i<jTable.getColumnCount();i++){
				if(jTable.getColumnName(i).indexOf("单位")>=0){
					dwI = i;
					continue;
				}
				if(jTable.getColumnName(i).indexOf("厂站")>=0){
					dwI = i;
					continue;
				}
				if(jTable.getColumnName(i).equals("操作内容")){
					zlI = i;
					continue;
				}
				if(jTable.getColumnName(i).equals("操作项目")){
					zlI = i;
					continue;
				}
				if(jTable.getColumnName(i).equals("校验")){
					jhI = i;
					continue;
				}
			}
			CBSystemConstants.lcm = null;
			final ArrayList<int[]> errorList = new ArrayList<int[]>();
			errorList.clear();
			final EchoReplace ec = new EchoReplace();
			new Thread(new Runnable() {

				public void run() {
					DefaultTableModel jTableModel = (DefaultTableModel) jTable.getModel();
					jTable.getColumnModel().getColumn(jhI).setMinWidth(40);
					jTable.getColumnModel().getColumn(jhI).setMaxWidth(40);
					jTable.getColumnModel().getColumn(jhI).setWidth(40);
					jTable.getColumnModel().getColumn(jhI).setPreferredWidth(40);
					final String cardbuildtype_bak = CBSystemConstants.cardbuildtype;
				try{
					// TODO Auto-generated method stub
					final int preSize = DeviceOperate.getAlltransDevMap().size();
					final String sysStatus = CBSystemConstants.cardstatus;
					CBSystemConstants.cardbuildtype = "1";
					CBSystemConstants.jh_tai = 1;
					CreatePowerStationToplogy.loadSysData();
					
					//加载厂站
					List<String> loadStationList = new ArrayList<String>();
					
					String czrw = jTextArea.getText();
					
					//检查操作内容得到操作对象
					List<RuleBaseMode> rbmRw= new ArrayList<RuleBaseMode>();
					List<RuleBaseMode> rbmLi= new ArrayList<RuleBaseMode>();
					HashMap<String, List<RuleBaseMode>> rbmLiMap= new HashMap<String, List<RuleBaseMode>>();
			
					for(int i = 0; i < jTable.getRowCount(); i++) {
						jTable.setValueAt(null, i, jhI);
						String stationName = StringUtils.ObjToString(jTableModel.getValueAt(i, dwI)).trim();
						String oprdesc = StringUtils.ObjToString(jTableModel.getValueAt(i, zlI)).trim();
						
						//25字换行校核
						boolean huanhang =false;
						if(oprdesc.endsWith("、")){
							if(i<jTable.getRowCount()-1){
								oprdesc+=StringUtils.ObjToString(jTableModel.getValueAt(i+1, zlI)).trim();
								huanhang=true;
							}
						}
						
							
						
						if(oprdesc.trim().equals(""))
							continue;
						
						
						
						
						
						
						
						
						//厂站为空查询操作指令中是否包含
						if(stationName.equals("")){
							if(oprdesc.indexOf("：") > -1) {
								stationName = oprdesc.substring(0, oprdesc.indexOf("："));
							}
							if(oprdesc.indexOf(":") > -1) {
								stationName = oprdesc.substring(0, oprdesc.indexOf("："));
							}
						}
						//若包含括号，查询上个厂站
						if(stationName.indexOf("（") >=0){
							
							for(int j = i;j>=0;j--){
								stationName = StringUtils.ObjToString(jTableModel.getValueAt(j, dwI)).trim();
								if(!stationName.equals("") && stationName.indexOf("（") == -1) break;
							}
						}
						//厂站如仍为空查询上个厂站
						if(stationName.equals("")){
							
							for(int j = i;j>=0;j--){
								stationName = StringUtils.ObjToString(jTableModel.getValueAt(j, dwI)).trim();
								if(!stationName.equals("") && stationName.indexOf("（") == -1) break;
							}
						}
						//未找到上个厂站则查询操作任务中所包含厂站
						if(stationName.equals("")){
							if(czrw.indexOf("：") > -1) {
								stationName = czrw.substring(0, czrw.indexOf("："));
							}
							if(czrw.indexOf(":") > -1) {
								stationName = czrw.substring(0, czrw.indexOf(":"));
							}
						}
						
						if(i==0 && rbmRw.size() == 0 && !stationName.contains("调") && !stationName.contains("监控") && !stationName.contains("集控")&& !stationName.contains("区控")){
							rbmRw.addAll(OperationCheckDefault.execute(stationName, czrw, true));
						}
						List<RuleBaseMode> rbmStep= OperationCheckDefault.execute(stationName, oprdesc);
						rbmLi.addAll(rbmStep);
						rbmLiMap.put(String.valueOf(i), rbmStep);
						
						
						
						if(huanhang){
							i++;
						}
						
					}
					
					jTable.updateUI();
					
//					//检查操作任务与操作内容（有待完善）
//					if(rbmRw.size() ==1 && rbmRw.get(0).getPd() != null) { //旁代识别有两个设备，不处理这种情况
//						
//						int checkRs = OperationCheckDefault.CheckOperation(rbmRw, rbmLi);
//						if(checkRs!=2){
//							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
//							pdlist.add(rbmRw.get(0).getPd());
//							rbmLi.get(0).setCheckResult(2);
//							CheckMessage cm = new CheckMessage();
//							cm.setPd(pdlist);
//							cm.setBottom("304");
//							if(CBSystemConstants.lcm==null){
//								CBSystemConstants.lcm = new ArrayList<CheckMessage>();
//							}
//							CBSystemConstants.lcm.add(cm);
//						}
//						
//						
////						int checkRs = OperationCheckDefault.CheckOperation(rbmRw, rbmLi);
////						if(checkRs!=2){
////							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
////							pdlist.add(rbmRw.get(0).getPd());
////							rbmLi.get(0).setCheckResult(2);
////							CheckMessage cm = new CheckMessage();
////							cm.setPd(pdlist);
////							cm.setBottom("304");
////							if(CBSystemConstants.lcm==null){
////								CBSystemConstants.lcm = new ArrayList<CheckMessage>();
////							}
////							CBSystemConstants.lcm.add(cm);
//					
//							
//							//操作任务不符结束校验
//							/*
//							 isSuccess = false;
//							 setCellCoin(rbmLi.get(0),jTable, 0, ec);
//							CBSystemConstants.cardbuildtype = cardbuildtype_bak;
//							CBSystemConstants.jh_tai = 0;
//							
//							try {
//								Thread.currentThread().join();
//								Thread.currentThread().interrupt();
//							} catch (InterruptedException e) {
//								// TODO Auto-generated catch block
//								e.printStackTrace();
//							}
//							*/
//						//}
//					}
					
					//操作任务与操作指令关联校验（目前只做了各侧都带线路开关的线路）
					Map<PowerDevice, String> relateDevMap = new HashMap<PowerDevice, String>();
					if(rbmRw.size() ==1 && rbmRw.get(0).getPd() != null) { //旁代识别有两个设备，不处理这种情况
//						if(CZPService.getService().getClass().getName().indexOf("app.jx") >= 0){
							if(rbmRw.get(0).getPd().getDeviceType().equals(SystemConstants.InOutLine)){
								List<PowerDevice> xlList = RuleExeUtil.getLineAllSideList(rbmRw.get(0).getPd());
								for(PowerDevice xl:xlList){
									List<PowerDevice> xlDevList = RuleExeUtil.getDeviceList(xl, SystemConstants.Switch+","+SystemConstants.SwitchSeparate+","+SystemConstants.SwitchFlowGroundLine,
											SystemConstants.PowerTransformer, true, true, false);
									for(PowerDevice dev:xlDevList){
										relateDevMap.put(dev,"1");
									}
								}
							}
//						}
					}	
					
					
					
			
					
					
					
					for(RuleBaseMode rbm : rbmLi) {
						if(rbm.getPd() != null) {
							String stationID = rbm.getPd().getPowerStationID();
							if(!stationID.equals("")) {
								if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
									CreatePowerStationToplogy.loadFacEquip(stationID);
								}
								if(!loadStationList.contains(stationID)) {
									InitDeviceStatus ie = new InitDeviceStatus();
									ie.initStatus_EMSToCache(stationID);
									loadStationList.add(stationID);
								}
							}
						}
					}
					
					CBSystemConstants.LineTagStatus.clear();
					if(rbmRw.size() > 0 && rbmRw.get(0).getPd()!=null && !rbmRw.get(0).getPd().getDeviceStatus().equals(rbmRw.get(0).getBeginStatus()))
						RuleExeUtil.deviceStatusReset(rbmRw.get(0).getPd(), rbmRw.get(0).getPd().getDeviceStatus(), rbmRw.get(0).getBeginStatus());


					for (int i = 0; i < jTable.getRowCount(); i++) {
						int WarFlag = -1;
						jTable.setRowSelectionInterval(i, i);
						String oprdesc = StringUtils.ObjToString(
								jTableModel.getValueAt(i, zlI)).trim();
						if(oprdesc.contains("程序操作")||oprdesc.contains("程序化操作")){
							RuleBaseMode rbm = new RuleBaseMode();
							rbm.setCheckResult(0);
							RuleBaseMode rbmPre = null;
							Object obj = jTable.getValueAt(i, jhI);
							if(obj != null && obj instanceof RuleBaseMode)
						   		rbmPre = (RuleBaseMode)obj;
					   		if(rbmPre == null || rbmPre.getCheckResult() == 0)
						   		jTable.setValueAt(rbm, i, jhI);
					  		else if(rbm.getCheckResult() != 0){
						   		rbmPre.getMessage().get("sbxx").addAll(rbm.getMessage().get("sbxx"));
					   		}
							continue;	
						}
						List<RuleBaseMode> rbmList = rbmLiMap.get(String
								.valueOf(i)); // 根据厂站和操作指令生成rbm集合
						if(rbmList == null)
							continue;
						for (RuleBaseMode rbm : rbmList) {

							if (rbm.getPd() == null) {
								if (rbm.getCheckout()) {
									rbm.setCheckResult(0);
								} else {
									rbm.setCheckResult(2);
								}
								
								//jTable.setValueAt(rbm, i, jhI);
								RuleBaseMode rbmPre = null;
								Object obj = jTable.getValueAt(i, jhI);
							   if(obj != null && obj instanceof RuleBaseMode)
								   rbmPre = (RuleBaseMode)obj;
							   if(rbmPre == null || rbmPre.getCheckResult() == 0)
								   jTable.setValueAt(rbm, i, jhI);
							   else if(rbm.getCheckResult() != 0){
								   rbmPre.getMessage().get("sbxx").addAll(rbm.getMessage().get("sbxx"));
							   }
								continue;
							}
							
							
//							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
//							pdlist.add(rbmRw.get(0).getPd());
//							rbm.setCheckResult(2);
//							CheckMessage cm = new CheckMessage();
//							cm.setPd(pdlist);
//							cm.setBottom("304");
//							if(CBSystemConstants.lcm==null){
//								CBSystemConstants.lcm = new ArrayList<CheckMessage>();
//							}
//							CBSystemConstants.lcm.add(cm);
							
							//如果该设备在校验过程中已操作过，则将校验模式改为即时校核
							Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
							for (int j = allMap.size(); j >preSize; j--) {
								PowerDevice pd = allMap.get(j).getTransDevice();
								if(pd.equals(rbm.getPd())) {
									CBSystemConstants.isRealTime = true;
									break;
								}
							}
							
							
							//操作任务与操作指令关联校验（目前只做了各侧都带线路开关的线路）
							if(relateDevMap.size()>0){
								if(relateDevMap.get(rbm.getPd())==null){
									List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
									pdlist.add(rbm.getPd());
									CheckMessage cm = new CheckMessage();
									cm.setPd(pdlist);
									cm.setBottom("304");
									if(CBSystemConstants.lcm==null){
										CBSystemConstants.lcm = new ArrayList<CheckMessage>();
									}
									CBSystemConstants.lcm.add(cm);
								}
							}
							
							
							//目前图形票的实时校核都取消
//							isSuccess = OperationCheckDefault.check(rbm);
							
							
							//校验完成后，将即时校核恢复为逻辑校核
							if(CBSystemConstants.isRealTime)
								CBSystemConstants.isRealTime = false;
							
							setCellCoin(rbm, jTable, i, ec);
							if (!isSuccess) {
								jTable.clearSelection();
								CBSystemConstants.cardbuildtype = cardbuildtype_bak;
								CBSystemConstants.jh_tai = 0;
								break;
							}
						}
						if (!isSuccess) {
							CBSystemConstants.cardbuildtype = cardbuildtype_bak;
							CBSystemConstants.jh_tai = 0;
							break;
						}
					}
					
					
					Map<Integer, DispatchTransDevice> allMap = DeviceOperate.getAlltransDevMap();
					for (int i = allMap.size(); i >preSize; i--) {
						allMap.remove(i);
					}
					for(int i = 1; i <= allMap.size(); i++) {
						DispatchTransDevice dtd = allMap.get(i);
						dtd.getTransDevice().setDeviceStatus(dtd.getEndstate());
					}
					
					CBSystemConstants.cardstatus = sysStatus;
					CBSystemConstants.cardbuildtype = cardbuildtype_bak;
					CBSystemConstants.jh_tai = 0;
					
				}catch (Exception e) {
					e.printStackTrace();
				}finally{
					CBSystemConstants.cardbuildtype = cardbuildtype_bak;
					CBSystemConstants.jh_tai = 0;
					if(CBSystemConstants.lcm != null)
					CBSystemConstants.lcm.clear();
				}
				
				}
			}).start();
	}
	
	public void setCellCoin(RuleBaseMode rbm,JTable jTable1,int i,EchoReplace ec){
		int WarFlag = -1;
		List<CheckMessage> msgList = new ArrayList<CheckMessage>();
		if(CBSystemConstants.lcm != null && CBSystemConstants.lcm.size() > 0) {
			WarFlag = 0;	
			boolean check =true;
			String czrw = jTable1.getModel().getValueAt(i, zlI).toString();
			for (CheckMessage msg : CBSystemConstants.lcm) {
				if(msg.getPd().size()>0){
					check=false;
					if (WarFlag != 1 && msg.getBottom().substring(0, 1).equals("1")) {
						WarFlag = 1;
					}else if(WarFlag != 2 && msg.getBottom().substring(0, 1).equals("3")) {
						WarFlag = 2;
					}
					msgList.add(msg);
				}
			}
			
			if(check){
				rbm.setCheckResult(0);
				RuleBaseMode rbmPre = null;
				Object obj = jTable1.getValueAt(i, jhI);
			   if(obj != null && obj instanceof RuleBaseMode)
				   rbmPre = (RuleBaseMode)obj;
			   if(rbmPre == null || rbmPre.getCheckResult() == 0)
				   jTable1.setValueAt(rbm, i, jhI);
			   else if(rbm.getCheckResult() != 0){
				   rbmPre.getMessage().get("sbxx").addAll(rbm.getMessage().get("sbxx"));
			   }
//				if(CBSystemConstants.lcm != null)
//					CBSystemConstants.lcm.clear();
				return;
			}
			
			
			
			int result = 0;
			if(WarFlag == -1)
				result = 0;
			else if(WarFlag == 0)
				result = 1;
			else if(WarFlag == 1)
				result = 2;
			else if(WarFlag == 2)
				result = 2;
			
			rbm.setCheckResult(result);
			Map<String, List<CheckMessage>> map = new HashMap<String, List<CheckMessage>>();
			CBSystemConstants.lcm.clear();
			map.put("sbxx", msgList);
			rbm.setMessage(map);
		}
		else
			rbm.setCheckResult(0);
		
		RuleBaseMode rbmPre = null;
		Object obj = jTable1.getValueAt(i, jhI);
	   if(obj != null && obj instanceof RuleBaseMode)
		   rbmPre = (RuleBaseMode)obj;
	   if(rbmPre == null || rbmPre.getCheckResult() == 0)
		   jTable1.setValueAt(rbm, i, jhI);
	   else if(rbm.getCheckResult() != 0){
		   rbmPre.getMessage().get("sbxx").addAll(rbm.getMessage().get("sbxx"));
	   }
//		if(CBSystemConstants.lcm != null)
//			CBSystemConstants.lcm.clear();
   }
	
}
