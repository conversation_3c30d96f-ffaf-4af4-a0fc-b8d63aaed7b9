package com.tellhow.czp.operationcard;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableCellRenderer;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Font;
import java.awt.GridBagLayout;
import java.awt.GridBagConstraints;

import javax.swing.JTextField;

import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.swing.JTabbedPane;
import javax.swing.GroupLayout;
import javax.swing.GroupLayout.Alignment;
import javax.swing.JTextArea;
import javax.swing.LayoutStyle.ComponentPlacement;
import javax.swing.JButton;
import javax.swing.JScrollPane;
import javax.swing.JTable;

/**
 * 防误信息展示实现
 * 
 * <AUTHOR>
 * 
 */
public class VOSViewPanelDefault extends VOSViewPanel {
	private JScrollPane jScrollPane1;
	private JScrollPane jScrollPane2;
	private JScrollPane jScrollPane3;
	protected DefaultTableModel jTable1Model1 = null;
	protected DefaultTableModel jTable1Model2 = null;
	protected DefaultTableModel jTable1Model3 = null;
	private JLabel jLabel1;
	private JTextField textField;
	private JTextField textField_1;
	private JTextField textField_2;
	private JTextField textField_3;
	private JTable table;
	private JTable table_1;
	private JTable table_2;
	private JButton btnNewButton;
	private JButton btnNewButton_1;
	private JButton btnNewButton_2;
	private JLabel lblNewLabel_4;
	private boolean isOk = true;
	private int WarFlag = 0;// 0非闭锁，1闭锁 
	private boolean isDTKP = false; //是否点图开票过程中校验

	public VOSViewPanelDefault() {
		super(SystemConstants.getMainFrame(), true);
		addWindowListener(new WindowAdapter() {
			public void windowClosing(WindowEvent we) {
				/*
				 * 预演功能
				 */
//				if(isDTKP) {
//					if (TempTicket.tempTicket != null&&CBSystemConstants.jh_tai==0) {
//						DeviceOperate.RollbackDeviceStatus();
//						//清空
//						DeviceOperate.ClearDevMap();
//						CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
//						CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
//				    	CBSystemConstants.bztRelationRecord.clear();
//						CBSystemConstants.bztStateRecord.clear();
//						CBSystemConstants.bztOrganRecord.clear();
//						TempTicket.tempTicket.setVisible(false);
//						TempTicket.tempTicket = null;
//						SvgUtil.clear();
//					}
//					CBSystemConstants.jh_tai = 0;
//					isOk = false;
//					CBSystemConstants.lcm = null;
//					dispose();
//				}
//				else {
					 continueAct(null);
//				}
			}
		});

	}

	/**
	 * 初始化数据
	 * 
	 * @param rbm
	 *            (RuleBaseMode)
	 */
	public void initData(RuleBaseMode rbm) {
		isDTKP = true;
		initData(rbm, CBSystemConstants.lcm);
	}
	
	/**
	 * 初始化数据
	 * 
	 * @param rbm
	 *            (RuleBaseMode)
	 */
	public void initData(RuleBaseMode rbm, List<CheckMessage> lcm) {
		
		EchoReplace ec = new EchoReplace();
		// 校验详情
		if (rbm == null)
			return;
		if (lcm == null || lcm.size() == 0)
			return;
		Object[][] xxtb = null;
		Object xxRowData[] = null;
		jTable1Model1 = new DefaultTableModel(xxtb, new String[] { "序号", "厂站",
				"设备", "内容" });
		int i = 0;
		for (CheckMessage msg : lcm) {
			i++;
			StringBuffer pdname = new StringBuffer();
			/*if (WarFlag != 1 && msg.getBottom().substring(0, 1).equals("1")) {
				WarFlag = 1;
			}else if(WarFlag != 2 && msg.getBottom().substring(0, 1).equals("3")) {
				WarFlag = 2;
			}*/

			WarFlag = 0;
			
			for (PowerDevice pd : msg.getPd()) {
				pdname.append( CZPService.getService().getDevName(pd));
			}
			String powerStationName = "";
			if (msg.getPd().size() > 0) {
				String stid = msg.getPd().get(0).getPowerStationID();
				if(!stid.equals(""))
					powerStationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(stid));
				else
					powerStationName = msg.getPd().get(0).getPowerStationName();
			}
			
			xxRowData = new Object[] { i, powerStationName, pdname.toString(),ec.getEchoMap(msg, rbm.getPd()).get("msg")};
			jTable1Model1.addRow(xxRowData);
		}
		init(rbm);

	}
	
	// 初始化
	public void init(RuleBaseMode rbm) {
		initComponents();
		initActData(rbm);
		complete();
		this.setVisible(true);
	}

	/**
	 * 初始化操作信息
	 * 
	 * @param rbm
	 * 
	 */
	private void initActData(RuleBaseMode rbm) {
		Date date = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		textField.setText(CBSystemConstants.getUser().getUserName());
		if(rbm.getPd()!=null&&rbm.getPd().getPowerDeviceName()!=null){
			textField_1.setText(rbm.getPd().getPowerDeviceName());
		}else{
			textField_1.setText("");
		}
		textField_2.setText(format.format(date));
	}

	/**
	 * 根据变量修改界面变量，完成。
	 */
	private void complete() {
		if (!isDTKP) {
			btnNewButton.setVisible(false);
			btnNewButton_1.setVisible(false);
		}
		else
			btnNewButton_2.setVisible(false);
		 if (WarFlag == 1) {
//			btnNewButton_1.setVisible(false);
			btnNewButton.setVisible(false);
			lblNewLabel_4.setText("闭锁信息!");
			isOk = false;
		}else if (WarFlag == 2){
//			btnNewButton_1.setVisible(false);
			lblNewLabel_4.setText("严重警告!");
			btnNewButton.setVisible(false);
		}
	}

	/**
	 * 初始化界面
	 */
	private void initComponents() {
		setBounds(400, 100, 900, 531);
		JPanel panel_1 = new JPanel();
		setResizable(false);
		setTitle("防误信息");
		{
			panel_1.setLayout(new BorderLayout(0, 0));
			jLabel1 = new JLabel("【操作信息】");
			panel_1.add(jLabel1);

		}
		JPanel panel_2 = new JPanel();
		{

			GridBagLayout gbl_panel_2 = new GridBagLayout();
			gbl_panel_2.columnWidths = new int[] { 0, 0, 0 };
			gbl_panel_2.rowHeights = new int[] { 0, 0, 0, 0, 0 };
			gbl_panel_2.columnWeights = new double[] { 0.0, 1.0,
					Double.MIN_VALUE };
			gbl_panel_2.rowWeights = new double[] { 0.0, 0.0, 0.0, 0.0,
					Double.MIN_VALUE };
			panel_2.setLayout(gbl_panel_2);

			JLabel label = new JLabel("\u4EBA\u5458");
			GridBagConstraints gbc_label = new GridBagConstraints();
			gbc_label.insets = new Insets(0, 0, 5, 5);
			gbc_label.gridx = 0;
			gbc_label.gridy = 0;
			panel_2.add(label, gbc_label);

			textField = new JTextField();
			GridBagConstraints gbc_textField = new GridBagConstraints();
			gbc_textField.insets = new Insets(0, 0, 5, 5);
			gbc_textField.fill = GridBagConstraints.HORIZONTAL;
			gbc_textField.gridx = 1;
			gbc_textField.gridy = 0;
			panel_2.add(textField, gbc_textField);
			textField.setColumns(10);

			JLabel lblNewLabel = new JLabel("\u8BBE\u5907");
			GridBagConstraints gbc_lblNewLabel = new GridBagConstraints();
			gbc_lblNewLabel.insets = new Insets(0, 0, 5, 5);
			gbc_lblNewLabel.gridx = 0;
			gbc_lblNewLabel.gridy = 1;
			panel_2.add(lblNewLabel, gbc_lblNewLabel);

			textField_1 = new JTextField();
			GridBagConstraints gbc_textField_1 = new GridBagConstraints();
			gbc_textField_1.insets = new Insets(0, 0, 5, 5);
			gbc_textField_1.fill = GridBagConstraints.HORIZONTAL;
			gbc_textField_1.gridx = 1;
			gbc_textField_1.gridy = 1;
			panel_2.add(textField_1, gbc_textField_1);
			textField_1.setColumns(10);

			JLabel label_1 = new JLabel("\u65F6\u95F4");
			GridBagConstraints gbc_label_1 = new GridBagConstraints();
			gbc_label_1.insets = new Insets(0, 0, 5, 5);
			gbc_label_1.gridx = 0;
			gbc_label_1.gridy = 2;
			panel_2.add(label_1, gbc_label_1);

			textField_2 = new JTextField();
			GridBagConstraints gbc_textField_2 = new GridBagConstraints();
			gbc_textField_2.insets = new Insets(0, 0, 5, 5);
			gbc_textField_2.fill = GridBagConstraints.HORIZONTAL;
			gbc_textField_2.gridx = 1;
			gbc_textField_2.gridy = 2;
			panel_2.add(textField_2, gbc_textField_2);
			textField_2.setColumns(10);

			/*
			 * JLabel label_2 = new JLabel("\u64CD\u4F5C\u5185\u5BB9");
			 * GridBagConstraints gbc_label_2 = new GridBagConstraints();
			 * gbc_label_2.insets = new Insets(0, 0, 5, 5); gbc_label_2.gridx =
			 * 0; gbc_label_2.gridy = 3; panel_2.add(label_2, gbc_label_2);
			 * 
			 * textField_3 = new JTextField(); GridBagConstraints
			 * gbc_textField_3 = new GridBagConstraints(); gbc_textField_3.fill
			 * = GridBagConstraints.HORIZONTAL; gbc_textField_3.insets = new
			 * Insets(0, 0, 5, 5); gbc_textField_3.gridx = 1;
			 * gbc_textField_3.gridy = 3; panel_2.add(textField_3,
			 * gbc_textField_3); textField_3.setColumns(10);
			 */
		}

		JPanel panel_3 = new JPanel();
		panel_3.setLayout(new BorderLayout(0, 0));

		JTabbedPane tabbedPane = new JTabbedPane(JTabbedPane.TOP);
		panel_3.add(tabbedPane);
		{
			JPanel panel1 = new JPanel();
			JPanel panel2 = new JPanel();
			JPanel panel3 = new JPanel();
			tabbedPane.add(panel1, "校验详情");
			{
				GridBagLayout gbl_panel1 = new GridBagLayout();
				gbl_panel1.columnWidths = new int[] { 0, 0, 0 };
				gbl_panel1.rowHeights = new int[] { 0, 0, 0, 0, 0 };
				gbl_panel1.columnWeights = new double[] { 0.0, 1.0,
						Double.MIN_VALUE };
				gbl_panel1.rowWeights = new double[] { 0.0, 0.0, 1.0,
						Double.MIN_VALUE };
				panel1.setLayout(gbl_panel1);

				JLabel lblNewLabel_1 = new JLabel("类型：");
				GridBagConstraints gbc_lblNewLabel_1 = new GridBagConstraints();
				gbc_lblNewLabel_1.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_1.gridx = 0;
				gbc_lblNewLabel_1.gridy = 0;
				panel1.add(lblNewLabel_1, gbc_lblNewLabel_1);

				lblNewLabel_4 = new JLabel("提醒警告!");
				GridBagConstraints gbc_lblNewLabel_4 = new GridBagConstraints();
				gbc_lblNewLabel_4.anchor = GridBagConstraints.WEST;
				gbc_lblNewLabel_4.insets = new Insets(0, 0, 5, 0);
				gbc_lblNewLabel_4.gridx = 1;
				gbc_lblNewLabel_4.gridy = 0;
				panel1.add(lblNewLabel_4, gbc_lblNewLabel_4);

				/*
				 * JLabel lblNewLabel_2 = new JLabel("描述："); GridBagConstraints
				 * gbc_lblNewLabel_2 = new GridBagConstraints();
				 * gbc_lblNewLabel_2.insets = new Insets(0, 0, 5, 5);
				 * gbc_lblNewLabel_2.gridx = 0; gbc_lblNewLabel_2.gridy = 1;
				 * panel1.add(lblNewLabel_2, gbc_lblNewLabel_2);
				 * 
				 * JTextArea textArea = new JTextArea(); GridBagConstraints
				 * gbc_textArea = new GridBagConstraints(); gbc_textArea.insets
				 * = new Insets(0, 0, 5, 0); gbc_textArea.fill =
				 * GridBagConstraints.BOTH; gbc_textArea.gridx = 1;
				 * gbc_textArea.gridy = 1; panel1.add(textArea, gbc_textArea);
				 */

				JLabel lblNewLabel_3 = new JLabel("【影响设备】");
				GridBagConstraints gbc_lblNewLabel_3 = new GridBagConstraints();
				gbc_lblNewLabel_3.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_3.gridx = 0;
				gbc_lblNewLabel_3.gridy = 1;
				panel1.add(lblNewLabel_3, gbc_lblNewLabel_3);
				jScrollPane1 = new JScrollPane();
				GridBagConstraints gbc_table = new GridBagConstraints();
				gbc_table.gridwidth = 2;
				gbc_table.fill = GridBagConstraints.BOTH;
				gbc_table.gridx = 0;
				gbc_table.gridy = 2;
				panel1.add(jScrollPane1, gbc_table);
				{
					table = new JTable();
					table.setFont(new java.awt.Font("宋体", 0, 13));

					table.setModel(jTable1Model1);
					table.setRowHeight(20);
					table.getColumnModel().getColumn(0).setMinWidth(50);
					table.getColumnModel().getColumn(0).setMaxWidth(50);
					table.getColumnModel().getColumn(1).setMinWidth(100);
					table.getColumnModel().getColumn(1).setMaxWidth(100);
					table.getColumnModel().getColumn(2).setMinWidth(100);
					table.getColumnModel().getColumn(2).setMaxWidth(160);
					table.setDefaultRenderer(Object.class, new TableViewRenderer());
					jScrollPane1.setViewportView(table);
				}
			}
			/*
			 * tabbedPane.add(panel2, "潮流校验"); { jScrollPane2 = new
			 * JScrollPane(); panel2.add(jScrollPane2); table_1 = new JTable();
			 * table_1.setModel(new javax.swing.table.DefaultTableModel( new
			 * Object[][] {}, new String[] { "", "设备名称", "信息", "操作前", "限值",
			 * "操作后" })); table_1.setRowHeight(20);
			 * jScrollPane2.setViewportView(table_1);
			 * 
			 * } tabbedPane.add(panel3, " 备 投 "); { jScrollPane3 = new
			 * JScrollPane(); panel3.add(jScrollPane3); table_2 = new JTable();
			 * table_2.setModel(new javax.swing.table.DefaultTableModel( new
			 * Object[][] {}, new String[] { "", "备投设备", "动作" }));
			 * table_2.setRowHeight(20); jScrollPane3.setViewportView(table_2);
			 * }
			 */

		}

		JPanel panel_4 = new JPanel();
		{
			btnNewButton_2 = new JButton("关闭");
			panel_4.add(btnNewButton_2);
			btnNewButton_2.addActionListener(new ActionListener() {
				@Override
				public void actionPerformed(ActionEvent e) {
					// TODO Auto-generated method stub
					continueAct(e);
				}
			});
			btnNewButton = new JButton("继续");
			panel_4.add(btnNewButton);
			btnNewButton.addActionListener(new ActionListener() {
				@Override
				public void actionPerformed(ActionEvent e) {
					// TODO Auto-generated method stub
					continueAct(e);
				}
			});
			btnNewButton_1 = new JButton("取消操作");
			btnNewButton_1.addActionListener(new ActionListener() {
				@Override
				public void actionPerformed(ActionEvent e) {
					// TODO Auto-generated method stub
					cancelAct(e);
				}
			});
			panel_4.add(btnNewButton_1);
		}
		GroupLayout groupLayout = new GroupLayout(getContentPane());
		groupLayout.setHorizontalGroup(groupLayout.createParallelGroup(
				Alignment.LEADING).addGroup(
				groupLayout.createSequentialGroup().addGroup(
						groupLayout.createParallelGroup(Alignment.LEADING)
								.addComponent(panel_4,
										GroupLayout.DEFAULT_SIZE, 884,
										Short.MAX_VALUE).addComponent(panel_1,
										GroupLayout.PREFERRED_SIZE, 884,
										GroupLayout.PREFERRED_SIZE)
								.addComponent(panel_2,
										GroupLayout.PREFERRED_SIZE, 884,
										GroupLayout.PREFERRED_SIZE)
								.addComponent(panel_3,
										GroupLayout.PREFERRED_SIZE, 884,
										GroupLayout.PREFERRED_SIZE))
						.addContainerGap()));
		groupLayout.setVerticalGroup(groupLayout.createParallelGroup(
				Alignment.LEADING).addGroup(
				groupLayout.createSequentialGroup().addComponent(panel_1,
						GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE,
						GroupLayout.PREFERRED_SIZE).addGap(2).addComponent(
						panel_2, GroupLayout.PREFERRED_SIZE, 113,
						GroupLayout.PREFERRED_SIZE).addPreferredGap(
						ComponentPlacement.RELATED).addComponent(panel_3,
						GroupLayout.PREFERRED_SIZE, 315,
						GroupLayout.PREFERRED_SIZE).addPreferredGap(
						ComponentPlacement.RELATED).addComponent(panel_4,
						GroupLayout.PREFERRED_SIZE, 34,
						GroupLayout.PREFERRED_SIZE).addContainerGap(12,
						Short.MAX_VALUE)));
		getContentPane().setLayout(groupLayout);

	}

	// 继续
	public void continueAct(ActionEvent e) {
		isOk = true;
		this.setVisible(false);
		this.dispose();
		   CBSystemConstants.lcm = null;
	}

	// 取消动作
	public void cancelAct(ActionEvent e) {
//		if (TempTicket.tempTicket != null&&CBSystemConstants.jh_tai==0) {
//			DeviceOperate.RollbackDeviceStatus();
//			//清空
//			DeviceOperate.ClearDevMap();
//			CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
//			CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
//	    	CBSystemConstants.bztRelationRecord.clear();
//			CBSystemConstants.bztStateRecord.clear();
//			CBSystemConstants.bztOrganRecord.clear();
//			TempTicket.tempTicket.setVisible(false);
//			TempTicket.tempTicket = null;
//			SvgUtil.clear();
//		}
		isOk = false;
		this.setVisible(false);
		CBSystemConstants.lcm = null;
		this.dispose();
	}

	public boolean isOk() {
		return this.isOk;
	}
}
//自定义的表格绘制器
class TableViewRenderer extends JTextArea implements TableCellRenderer{
	public TableViewRenderer(){
		//将表格设为自动换行
		setLineWrap(true); //利用JTextArea的自动换行方法
	}
	public Component getTableCellRendererComponent(JTable table, Object value,boolean isSelected, boolean hasFocus, int row, int column){
		//自动换行
		JTextArea textArea = new JTextArea();

		textArea.setLineWrap(true); 
		textArea.setWrapStyleWord(true); 
		

		textArea.setFont(new Font("宋体",Font.PLAIN,14));
		int maxPreferredHeight = 26; 

		String xString = StringUtils.ObjToString(table.getValueAt(row,column));
		textArea.setText(xString); 
		textArea.setSize(table.getColumnModel().getColumn(column).getWidth(),10);
	    maxPreferredHeight = Math.max(maxPreferredHeight, textArea.getPreferredSize().height); 


        if (table.getRowHeight(row) < maxPreferredHeight)  // 少了这行则处理器瞎忙 
            table.setRowHeight(row, maxPreferredHeight);

        textArea.setText(value == null ? "" : xString); 
	      

		return textArea;
	}
}

