package com.tellhow.czp.operationcard;

import java.awt.Rectangle;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JOptionPane;

import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.system.CBSystemConstants;

public class TempTicketWinDefault extends TempTicketDefault {
	private JFrame jf=new JFrame("操作票系统拟票界面");
	
	public TempTicketWinDefault() {
		initComponents();
		jf.add(contentPanel);
		jf.setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);
		jf.addWindowListener(new WindowAdapter() {
			public void windowClosing(WindowEvent e) {
				int isclose=JOptionPane.showConfirmDialog(tempTicket, "是否关闭拟票界面","确认窗口",JOptionPane.YES_NO_OPTION);
				if(isclose==0){
					isRoll=true;
	                jButton6ActionPerformed(e);
	                isRoll=false;
				}
				}
		});
		
		Rectangle  rtl=SystemConstants.getGuiBuilder().getJFrame().getBounds();
		int x=(int) (rtl.getWidth()-534);
		int y=(int) (rtl.getHeight()-100);
		jf.setBounds(x-30,100,534,y);
		jf.setVisible(true);
	}
	
	private void jButton6ActionPerformed(WindowEvent e) {
		//回滚
		DeviceOperate.RollbackDeviceStatus();
		//清空
		DeviceOperate.ClearDevMap();
		CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
		CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
    	CBSystemConstants.bztRelationRecord.clear();
		CBSystemConstants.bztStateRecord.clear();
		CBSystemConstants.bztOrganRecord.clear();
		jf.setVisible(false);
		jf.dispose();
		tempTicket = null;
		SvgUtil.clear();
		bccardid=null;
	}
	
	public void setdel(){
		jf.setVisible(false);
		jf.dispose();
		tempTicket = null;
	}
	
}
