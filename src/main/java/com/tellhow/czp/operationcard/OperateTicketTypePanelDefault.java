package com.tellhow.czp.operationcard;

import java.awt.BorderLayout;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Date;
import java.util.List;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.SwingUtilities;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;
import javax.swing.table.TableModel;
import javax.swing.table.TableRowSorter;

import org.jfree.report.JFreeReportBoot;

import com.tellhow.czp.Robot.CzpRobot;
import com.tellhow.czp.app.PrintOperationCard;
import com.tellhow.czp.app.service.OMSService;
import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.user.User;
import com.tellhow.czp.user.UserDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.DateUtil;

import czprule.datemodule.JCalendarPanel;
import czprule.model.CodeNameModel;
import czprule.securitycheck.view.CheckDialog;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.view.InitDeviceTypeChockBox;
/**
 * @命令票 和整个窗口的框架
 * <AUTHOR>
 * 修改：王豪
 */
public class OperateTicketTypePanelDefault extends OperateTicketTypePanel {
	private SetJTableProtery sjp = new SetJTableProtery();
	private javax.swing.table.DefaultTableModel jtableModel1;
	private final JPanel contentPanel = new JPanel();
	private JPopupMenu jPopupMenu = new JPopupMenu();
	private JMenuItem inversion = new JMenuItem("反演",new javax.swing.ImageIcon(getClass().getResource(
			"/tellhow/btnIcon/import.gif")));
	/** Creates new form OperateTicketTypePanelbak */
	/**
	 * Launch the application.
	 */
/*	public static void main(String[] args) {
		try {
			OperateTicketTypePanel dialog = new OperateTicketTypePanel();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}*/

	/**
	 * Create the dialog.
	 */
	public OperateTicketTypePanelDefault() {
		initComponents();
		//jTabbedPane1.setTabPlacement(JTabbedPane.TOP);
		this.add(jTabbedPane1);
    	jTabbedPane1.addTab("操作票", contentPanel);
		jTabbedPane1.addTab("典型票", jPanel2);
		init();
		jPopupMenu.add(inversion);
		inversion.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent arg0) {
				// TODO Auto-generated method stub
				CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
						jTable1.getSelectedRow(), 1);
				
				boolean ci=this.isExitInversion(cnm.getCode());
				if(!ci)
				{
					JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "原系统老版本操作票数据暂不支持回演", "提示信息", JOptionPane.INFORMATION_MESSAGE);
				}
				else
				{
					JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
					splitPane.setDividerLocation(1.0);
//					String czrw = jTable1.getValueAt(jTable1.getSelectedRow(), 2)
//							.toString().trim();
//					String npr = jTable1.getValueAt(jTable1.getSelectedRow(), 3)
//							.toString().trim();
//					String npsj = jTable1.getValueAt(jTable1.getSelectedRow(), 4)
//							.toString().trim();
//					String shr = jTable1.getValueAt(jTable1.getSelectedRow(), 5)
//							.toString().trim();
//					String[] cards = new String[] { czrw, npr, npsj, shr };
//					
//					OperateTicketMLPMXPanel tip = new OperateTicketMLPMXPanel( cnm, cards);
//					JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
//					splitPane.setDividerLocation(0.65);
//					
//					splitPane.setRightComponent(tip);
//					
//					tip.setVisible(true);
					new CzpRobot().doInversion(cnm.getCode());
				}
			}
			public boolean isExitInversion(String cardId)
			{
				int obj=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpactionstate where CARDID='"+cardId+"'");
				if(obj==0)
					return false;
				else
					return true;
			}
		}
	  );
		//jPanel1.setLayout((LayoutManager) contentPanel);
	}
	/**
	 * tableModel
	 */
	private void init() {
		//初始化起始时间
		Date today = new Date();
		DateUtil dateUtil = new DateUtil();
		jTextField1.setText(dateUtil.getHistoryTime(today.getDate() - 1,
				"yyyy-MM-dd"));
		jTextField2.setText(dateUtil.getCurTime("yyyy-MM-dd"));
		//初始化拟票人下拉框
		DefaultComboBoxModel dcbUser = new DefaultComboBoxModel();
		UserDao userdao = new UserDao();
		List<User> allusers = userdao.getAllUser(CBSystemConstants.getUser().getUnitCode());
		User user = new User();
		user.setUserID("");
		user.setUserName("请选择");
		dcbUser.addElement(user);
		for (int i = 0; i < allusers.size(); i++) {
			user = allusers.get(i);
			dcbUser.addElement(user);
		}
		this.jComboBox1.setModel(dcbUser);

		//初始化设备类型下拉框
		this.jComboBox2.setModel(InitDeviceTypeChockBox.getDevTypeCheckBox());
//		this.jComboBox3.setModel(InitDeviceTypeChockBox.getRoleCheckBox());

		//初始化命令票
		if (jtableModel1 == null) {
			jtableModel1 = new DefaultTableModel(new String[][] {},
					new String[] { "序号", "操作票编号", "操作任务", "拟票人", "拟票时间", "审核人",
							"拟票方式", "状态", "操作票类型","是否导入OMS","预令状态" }) {
				public boolean isCellEditable(int rowIndex, int columnIndex) {
					return false;
				}
			};
			jTable1.setModel(jtableModel1);
			jTable1.setRowSorter(new TableRowSorter<TableModel>(jtableModel1));//表头排序
		}
		this.initTable();
		if(jTabbedPane1.getComponentCount() >= 2) {
//			OperateTicketDXPDefault dxp = new OperateTicketDXPDefault();
			  OperateTicketDXP  dxp = OperateTicketDXP.getInstance();
			jTabbedPane1.setComponentAt(1, dxp);
		}
	
	}
	/**
	 * 初始化表格
	 */
	public int initTable() {
		DefaultTableModel tableModel = (DefaultTableModel) jTable1.getModel();
		tableModel.setRowCount(0);// 清除原有行
		String beginTime = jTextField1.getText().trim(); //查询起始时间
		String endTime = jTextField2.getText().trim(); //查询结束时间
		String queryczrw = jTextField3.getText().trim(); //操作任务
		String querynpr = ""; //拟票人
		if (!((User)this.jComboBox1.getSelectedItem()).getUserID().equals("")) {
			querynpr = ((User) this.jComboBox1.getSelectedItem()).getUserName();
		}
		String devType = ""; //设备类型
		if (this.jComboBox1.getSelectedItem() != null) {
			devType = ((CodeNameModel) this.jComboBox2.getSelectedItem())
					.getCode();
		}
		String role = "";
		/*if (this.jComboBox3.getSelectedItem() != null) {
			role = ((CodeNameModel) this.jComboBox3.getSelectedItem())
					.getCode();
		}*/
		TicketDBManager tdb = new TicketDBManager();
		//[主表ID、序号、操作任务、拟票人、拟票时间、审核人、开票类型、状态]
		List<String[]> czpList = tdb.queryTicketZBByOpcode(beginTime, endTime,
				queryczrw, querynpr, devType);
		
		for (int i = 0; i < czpList.size(); i++) {
			CodeNameModel cnm = new CodeNameModel();
			CodeNameModel state = new CodeNameModel();
			String[] tempStr = czpList.get(i);
			cnm.setCode(tempStr[0]);
			cnm.setName(tempStr[1]);
			state.setCode(tempStr[0]);
			state.setName(tempStr[7]);
			String cartKind = "";
			if(tempStr[8].equals("0"))
				cartKind = "综令票";
			else if(tempStr[8].equals("1"))
				cartKind = "逐项票";
			else if(tempStr[8].equals("2"))
				cartKind = "新投票";
			else if(tempStr[8].equals("3"))
				cartKind = "监控票";
			Object[] rowData = { String.valueOf(i + 1), cnm, tempStr[2],
					tempStr[3], tempStr[4], tempStr[5],
					tempStr[6], tempStr[7] ,cartKind ,tempStr[9] ,tempStr[10]};
			tableModel.addRow(rowData);
		}
		jTable1.setModel(tableModel);
		sjp.makeFace(jTable1);
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultRenderer(jTable1.getColumnClass(1), jTable1);//单元格内容居中
		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMinWidth(60);
		tcm.getColumn(0).setMaxWidth(60);
		tcm.getColumn(2).setMinWidth(300);
		tcm.getColumn(4).setMinWidth(120);
		tcm.getColumn(5).setMinWidth(0);   
		tcm.getColumn(5).setMaxWidth(0);
		tcm.getColumn(7).setMinWidth(0);   
		tcm.getColumn(7).setMaxWidth(0);
		tcm.getColumn(9).setMinWidth(0);   
		tcm.getColumn(9).setMaxWidth(0);
		tcm.getColumn(10).setMinWidth(0);   
		tcm.getColumn(10).setMaxWidth(0);
		return czpList.size();
	}
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">                          
    private void initComponents() {
    	jScrollPane1 = new JScrollPane();
    	jTabbedPane1 = new javax.swing.JTabbedPane();
    	jPanel1 = new javax.swing.JPanel();
    	jPanel5 = new javax.swing.JPanel();
    	jButton3 = new javax.swing.JButton();
        jButton4 = new javax.swing.JButton();
        jButton5 = new javax.swing.JButton();
        jButton9 = new javax.swing.JButton();
        jButton13 = new javax.swing.JButton();
//        jPanel2 = new OperateTicketDXPDefault();
        jPanel2 = OperateTicketDXP.getInstance();
        jButton3.setVisible(false);
        jButton4.setVisible(false);
        jButton5.setVisible(false);
        jButton9.setVisible(false);
        jTabbedPane1.setBackground(new java.awt.Color(244, 243, 243));
        jTabbedPane1.setOpaque(true);
        jScrollPane1.getViewport().setBackground(new java.awt.Color(244, 243, 243));
        jPanel1.setBackground(new java.awt.Color(244, 243, 243));
        jPanel5.setBackground(new java.awt.Color(244, 243, 243));
        contentPanel.setBackground(new java.awt.Color(244, 243, 243));
        jTabbedPane1.addChangeListener(new javax.swing.event.ChangeListener() {
            public void stateChanged(javax.swing.event.ChangeEvent evt) {
                jTabbedPane1StateChanged(evt);
            }
        });
		setBounds(100, 100, 732, 398);
		this.setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		this.add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(new BorderLayout(0, 0));
		{
			JPanel panel = new JPanel();
			panel.setBackground(new java.awt.Color(244, 243, 243));
			contentPanel.add(panel, BorderLayout.NORTH);
			panel.setLayout(new BorderLayout(0, 0));
			{
				JPanel panel_1 = new JPanel();
				panel_1.setBackground(new java.awt.Color(244, 243, 243));
				panel.add(panel_1, BorderLayout.WEST);
				{
					jLabel1 = new JLabel();
					jLabel1.setText("\u62DF\u7968\u65F6\u95F4\uFF1A");
					panel_1.add(jLabel1);
				}
				{
					jTextField1 = new JTextField();
					jTextField1.setPreferredSize(new Dimension(6, 25));
					panel_1.add(jTextField1);
					jTextField1.setColumns(11);
				}
				{
					jLabel2 = new JLabel();
					jLabel2.setText(" 至：");
					panel_1.add(jLabel2);
				}
				{
					jTextField2 = new JTextField();
					jTextField2.setPreferredSize(new Dimension(6, 25));
					panel_1.add(jTextField2);
					jTextField2.setColumns(11);
				}
				{
					jLabel4 = new JLabel();
					jLabel4.setText(" 拟票人： ");
					panel_1.add(jLabel4);
				}
				{
					jComboBox1 = new JComboBox();
					panel_1.add(jComboBox1);
				}
				{
					jButton1 = new JButton();
			        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/icons/query.gif"))); // NOI18N
			        jButton1.setToolTipText("查询");
			        jButton1.setMargin(new java.awt.Insets(1,1,1,1));
			        jButton1.setFocusPainted(false);
			        jButton1.addActionListener(new java.awt.event.ActionListener() {
			            public void actionPerformed(java.awt.event.ActionEvent evt) {
			                jButton1ActionPerformed(evt);
			            }
			        });
					panel_1.add(jButton1);
				}
			}
			{
//				JButton jButton12 = new JButton("关闭");
//				jButton12.setToolTipText("关闭");
//				jButton12.setMargin(new java.awt.Insets(1,1,1,1));
//				jButton12.setPreferredSize(new Dimension(50,20));
				
				jButton12 = new JButton();
				jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
				jButton12.setText("取消");
				jButton12.setToolTipText("取消");
				jButton12.setMargin(new java.awt.Insets(1,1,1,1));
		        jButton12.setBorderPainted(false);
		        jButton12.setFocusPainted(false);
		        jButton12.setPreferredSize(new Dimension(50,10));
		        
//				jButton12 = new JButton("关闭");
//				jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/icons/query.gif"))); // NOI18N
//				jButton12.setToolTipText("关闭");
//				jButton12.setMargin(new java.awt.Insets(1,1,1,1));
//				jButton12.setFocusPainted(false);
				
				//panel.add(jButton12,BorderLayout.EAST);
				 jButton12.addActionListener(new java.awt.event.ActionListener() {
			            public void actionPerformed(java.awt.event.ActionEvent evt) {
			            	jButton14ActionPerformed(evt);
			            }
			        });
			}
			{
				JPanel panel_1 = new JPanel();
				panel_1.setBackground(new java.awt.Color(244, 243, 243));
				panel.add(panel_1, BorderLayout.SOUTH);
				panel_1.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_2 = new JPanel();
					panel_2.setBackground(new java.awt.Color(244, 243, 243));
					panel_1.add(panel_2, BorderLayout.WEST);
					{
						jLabel3 = new JLabel();
						jLabel3.setText("操作任务：");
						panel_2.add(jLabel3);
					}
					{
						jTextField3 = new JTextField();
						jTextField3.setPreferredSize(new Dimension(6, 25));
						panel_2.add(jTextField3);
						jTextField3.setColumns(30);
					}
					{
						jLabel5 = new JLabel("\u8BBE\u5907\u7C7B\u578B\uFF1A");
						jLabel5.setText("设备类型：");
						panel_2.add(jLabel5);
					}
					{
						jComboBox2 = new JComboBox();
						panel_2.add(jComboBox2);
					}
					/*{
						jLabel6 = new JLabel("角色");
						jLabel6.setText("角色：");
						panel_2.add(jLabel6);
					}
					{
						jComboBox3 = new JComboBox();
						panel_2.add(jComboBox3);
					}*/
					
				}
				{
					JPanel panel_2 = new JPanel();
					panel_2.setBackground(new java.awt.Color(244, 243, 243));
					panel_1.add(panel_2, BorderLayout.SOUTH);
					panel_2.setLayout(new BorderLayout(0, 0));
					{
						JPanel panel_3 = new JPanel();
						panel_3.setBackground(new java.awt.Color(244, 243, 243));
						panel_2.add(panel_3, BorderLayout.EAST);
						{
							jButton6 = new JButton();
					        jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/import.gif"))); // NOI18N
					        jButton6.setText("导入OMS");
					        jButton6.setToolTipText("导入OMS");
					        jButton6.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton6.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
					        jButton6.setFocusPainted(false);
					        jButton6.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton6ActionPerformed(evt);
					            }
					        });
							panel_3.add(jButton6);
						}
						{
							jButton11 = new JButton();
							jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/edit.png"))); // NOI18N
					        jButton11.setText("修改");
					        jButton11.setToolTipText("修改");
					        jButton11.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton11.setFocusPainted(false);
					        jButton11.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton11ActionPerformed(evt);
					            }
					        });
							panel_3.add(jButton11);
						}
						{
							jButton2 = new JButton("\u5220\u9664");
					        jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
					        jButton2.setText("删除");
					        jButton2.setToolTipText("删除");
					        jButton2.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton2.setFocusPainted(false);
					        jButton2.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton2ActionPerformed(evt);
					            }
					        });
							panel_3.add(jButton2);
						}
						{
							jButton7 = new JButton();
					        jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.gif"))); // NOI18N
					        jButton7.setText("保存为文件");
					        jButton7.setToolTipText("保存为文件");
					        jButton7.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton7.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
					        jButton7.setFocusPainted(false);
					        jButton7.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					            	jButton7ActionPerformed(evt);
					            }
					        });
							panel_3.add(jButton7);
						}
						{
							jButton8 = new JButton();
					        jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/favorite.png"))); // NOI18N
					        jButton8.setText("保存为典型票");
					        jButton8.setToolTipText("保存为典型票");
					        jButton8.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton8.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
					        jButton8.setFocusPainted(false);
					        jButton8.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					            	jButton8ActionPerformed(evt);
					            }
					        });
							panel_3.add(jButton8);
						}
						
						panel_3.add(jButton13);
						/*
						{
							jButton10 = new JButton("\u6253\u5370");
					        jButton10.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/ico6-1.gif"))); // NOI18N
					        jButton10.setText("打印");
					        jButton10.setToolTipText("打印");
					        jButton10.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton10.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
					        jButton10.setFocusPainted(false);
					        jButton10.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					            	jButton10ActionPerformed(evt);
					            }
					        });
							panel_3.add(jButton10);
						}
						*/
						
						/*
						{
							jButton12 = new JButton("\u6F14\u793A");
					        jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/play.png"))); // NOI18N
					        jButton12.setText("演示");
					        jButton12.setToolTipText("演示");
					        jButton12.setMargin(new java.awt.Insets(1,1,1,1));
					        jButton12.setFocusPainted(false);
					        jButton12.addActionListener(new java.awt.event.ActionListener() {
					            public void actionPerformed(java.awt.event.ActionEvent evt) {
					                jButton12ActionPerformed(evt);
					            }
					        });
							panel_3.add(jButton12);
						}
						*/
					}
				}
			}
		}
		{
			contentPanel.add(jScrollPane1, BorderLayout.CENTER);
			{
				jTable1 = new JTable();
		        jTable1.setFont(new java.awt.Font("宋体", 0, 13)); // NOI18N
		        jTable1.setRowHeight(26);
		        jTable1.addMouseListener(new java.awt.event.MouseAdapter() {
		            public void mouseClicked(java.awt.event.MouseEvent evt) {
		                MLPTableMouseClicked(evt);
		            }
		        });
				jScrollPane1.setViewportView(jTable1);
			}
		}
        jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/poll.gif"))); // NOI18N
        jButton3.setToolTipText("归档");
        jButton3.setBorder(null);
        jButton3.setFocusPainted(false);
        jButton3.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton3ActionPerformed(evt);
            }
        });
        jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/toggle-lt.gif"))); // NOI18N
        jButton4.setToolTipText("收缩");
        jButton4.setBorder(null);
        jButton4.setBorderPainted(false);
        jButton4.setFocusPainted(false);
        jButton4.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton4ActionPerformed(evt);
            }
        });
        jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/edit.png"))); // NOI18N
        jButton5.setToolTipText("审核");
        jButton5.setBorder(null);
        jButton5.setBorderPainted(false);
        jButton5.setFocusPainted(false);
        jButton5.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton5ActionPerformed(evt);
            }
        });
        jButton9.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/check.png"))); // NOI18N
        jButton9.setToolTipText("安全检验");
        jButton9.setBorder(null);
        jButton9.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        jButton9.setFocusPainted(false);
        jButton9.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
            	jButton9ActionPerformed(evt);
            }
        });
        jButton13.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/network.gif"))); // NOI18N
        jButton13.setText("转至拟票");
        jButton13.setToolTipText("转至拟票");
        jButton13.setMargin(new java.awt.Insets(1,1,1,1));
        jButton13.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        jButton13.setFocusPainted(false);
        jButton13.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton13ActionPerformed(evt);
            }
        });
        
    }
    //jTable1事件
	private void MLPTableMouseClicked(java.awt.event.MouseEvent evt) {
		if (SwingUtilities.isLeftMouseButton(evt) && evt.getClickCount() == 2) {
				CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
						jTable1.getSelectedRow(), 1);
				String czrw = jTable1.getValueAt(jTable1.getSelectedRow(), 2)
						.toString().trim();
				String npr = jTable1.getValueAt(jTable1.getSelectedRow(), 3)
						.toString().trim();
				String npsj = jTable1.getValueAt(jTable1.getSelectedRow(), 4)
						.toString().trim();
				String shr = jTable1.getValueAt(jTable1.getSelectedRow(), 8)
						.toString().trim();
				String[] cards = new String[] { czrw, npr, npsj, shr };
				OperateTicketMLPMX ti = OperateTicketMLPMX.getInstance();
				ti.init(cnm, cards);
				ti.setVisible(true);
		} else if (jTable1.getSelectedRow() != -1
				&& SwingUtilities.isRightMouseButton(evt)
				&& evt.getClickCount() == 1) {
				//jPopupMenu.show(jTable1,evt.getX(),evt.getY());
		}
	}
	//tab切换
	private void jTabbedPane1StateChanged(javax.swing.event.ChangeEvent evt) {
		// TODO add your handling code here:

				init();//初始化命令票面板

	}
	@Override
	public void settab(int tab){
		jTabbedPane1.setSelectedIndex(tab);
	}
	//审核
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view("请选择需要审核的命令票!");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view("不能同时审核多条命令票!");
			return;
		}
		String shrName = jTable1.getValueAt(selectRows[0], 5).toString().trim();
		if (!shrName.equals("")) {
			ShowMessage.view("该票已审核！");
			return;
		}
		int ok = JOptionPane.showConfirmDialog(this, "是否确定审核该操作命令？", "操作票提示框",
				JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.YES_OPTION) {
			CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
					selectRows[0], 1);
			User shr = CBSystemConstants.getUser();
			jTable1.setValueAt(shr.getUserName(), selectRows[0], 5);
			TicketDBManager tdb = new TicketDBManager();
			tdb.updateShr(shr, cnm);//插入审核人
		}
		return;
	}
	//隐藏查询条件 收缩
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if (this.jButton4.getToolTipText().equals("收缩")) {
			this.jPanel5.setVisible(false);
			this.jButton4.setToolTipText("扩展");
			this.jButton4.setIcon(new javax.swing.ImageIcon(getClass()
					.getResource("/tellhow/btnIcon/toggle-lt.gif")));
		} else {
			this.jPanel5.setVisible(true);
			this.jButton4.setToolTipText("收缩");
			this.jButton4.setIcon(new javax.swing.ImageIcon(getClass()
					.getResource("/tellhow/btnIcon/toggle-rt.gif")));
		}
	}

	//起始查询时间选择
	private void BeginTimeMouseClicked(java.awt.event.MouseEvent evt) {
		String selectTime = "";
		if (evt.getButton() == 1 && evt.getClickCount() == 2) {
			JCalendarPanel calendarPanel = new JCalendarPanel(
					jTextField1.getX() + 12, jTextField1.getY() + 19);
			selectTime = calendarPanel.getDateStr();
		}
		if (!selectTime.equals("")) {
			String[] beginTime = selectTime.split(" ");
			jTextField1.setText(beginTime[0]);
		}
	}

	//结束查询时间选择
	private void EndTimeMouseClicked(java.awt.event.MouseEvent evt) {
		String selectTime = "";
		if (evt.getButton() == 1 && evt.getClickCount() == 2) {
			JCalendarPanel calendarPanel = new JCalendarPanel(
					jTextField2.getX() + 12, jTextField2.getY() + 19);
			selectTime = calendarPanel.getDateStr();
		}
		if (!selectTime.equals("")) {
			String[] beginTime = selectTime.split(" ");
			jTextField2.setText(beginTime[0]);
		}
	}
	//查询
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		int count = initTable();
		if(count == 0) {
			ShowMessage.view("没有查询到操作票记录！");
			return;
		}
	}
	//导入OMS
    private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
    	((JButton)evt.getSource()).setEnabled(false);
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要导入OMS的操作票");
			 ((JButton)evt.getSource()).setEnabled(true);
		        setCursor(Cursor.getDefaultCursor());
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
		String zbid = cnm.getCode();
//		if(CBSystemConstants.getImportInfo()!=null){
//			
//		}else{
			//CZPOperator.getOperator().importOMS(zbid);
		try{
			OMSService.getService().importOMS(zbid);
    	}
    	catch(Exception ex) {
		}
        initTable();
        ((JButton)evt.getSource()).setEnabled(true);
        setCursor(Cursor.getDefaultCursor());
    } 
    //修改
    private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要编辑的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
		String czrw = jTable1.getValueAt(selectRows[0], 2).toString().trim();
		OperateTicketDXPMX pot = OperateTicketDXPMX.getInstance();
		pot.init(SystemConstants.getMainFrame(), new CodeNameModel(cnm.getCode(), czrw));
		pot.setVisible(true);
		initTable();
    }  
    //删除
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view("请选择要删除的操作票");
			return;
		}
		int ok = JOptionPane.showConfirmDialog(this, "删除后不能恢复，你确定要删除吗？",
				"操作票提示框", JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.NO_OPTION) {
			return;
		}
		jTable1.removeEditor();
		TicketDBManager tdb = new TicketDBManager();
		for (int i = selectRows.length - 1; i >= 0; i--) {
			CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
					selectRows[i], 1);
			String zbid = cnm.getCode();
			tdb.delTicket(zbid);
		}
		initTable();
	}
	//保存为文件
    private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要保存的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
    	this.setCursor(new Cursor(Cursor.WAIT_CURSOR));
    	JFreeReportBoot.getInstance().start();
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zb = tdb.queryTicketZB(cnm.getCode());
    	List<BaseCardModel> mx = tdb.queryTicketMX(cnm.getCode());
    	//PrintOperationCard print = new PrintOperationCard(this, "", "", "", "", "", "", jtableModel1, 1,"");
    	PrintOperationCard print = new PrintOperationCard(SystemConstants.getMainFrame(), zb, mx);
    	print.ExportPDF();
    	this.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
    }
    //保存为典型票
    private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要保存为典型票的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
    	TicketDBManager tdb = new TicketDBManager();
		boolean result = tdb.InsertDXTicketDB(cnm.getCode());
		if(result)
			ShowMessage.view("保存成功！");
		else
			ShowMessage.view("保存失败！");
    } 
    //打印
    /*
    private void jButton10ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要打印的操作票");
			return;
		}
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(selectRows[0], 1);
    	this.setCursor(new Cursor(Cursor.WAIT_CURSOR));
    	JFreeReportBoot.getInstance().start();
    	TicketDBManager tdb = new TicketDBManager();
    	String[] zb = tdb.queryTicketZB(cnm.getCode());
    	List<BaseCardModel> mx = tdb.queryTicketMX(cnm.getCode());
    	//PrintOperationCard print = new PrintOperationCard(this, "", "", "", "", "", "", jtableModel1, 1,"");
    	PrintOperationCard print = new PrintOperationCard(SystemConstants.getMainFrame(), zb, mx);
    	print.PrintPreview();
    	this.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
    }
    */
    //演示
    private void jButton12ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要演示的操作票");
			return;
		}
    	CBSystemConstants.isInversion = true;
    	SystemConstants.getMainFrame().setTitle(CBSystemConstants.SYSTEM_TITLE+"---模拟演示");
    	CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(jTable1.getSelectedRow(), 1);
    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(1.0);
		String zbid = cnm.getCode();
		//buildDZP(zbid);
		new CzpRobot().doInversion(cnm.getCode());
    }
    //归档
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		int[] selectRows = jTable1.getSelectedRows();
		if (selectRows.length == 0) {
			ShowMessage.view("请选择需要归档的命令票!");
			return;
		}
		if (selectRows.length > 1) {
			ShowMessage.view("不能同时归档多条命令票!");
			return;
		}
		String gdStr = jTable1.getValueAt(selectRows[0], 7).toString().trim();
		if ("已归档".equals(gdStr)) {
			ShowMessage.view("命令票已经归档!");
			return;
		}
		int ok = JOptionPane.showConfirmDialog(this, "确定将此命令票归档吗？", "操作票提示框",
				JOptionPane.YES_NO_OPTION);
		if (ok == JOptionPane.YES_OPTION) {
			CodeNameModel cnm = (CodeNameModel) jTable1.getValueAt(
					selectRows[0], 1);
			String zbid = cnm.getCode();
			TicketDBManager tdb = new TicketDBManager();
			tdb.updateState(zbid);
			jTable1.setValueAt("  已归档", selectRows[0], 7);
		}
	}
	//安全检验
    private void jButton9ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要安全检验的操作票");
			return;
		}
    	CheckDialog dialog = new CheckDialog(SystemConstants.getMainFrame(), true);
    	dialog.setVisible(true);
    }
    //导入预令票
    private void jButton13ActionPerformed(java.awt.event.ActionEvent evt) {                                         
        // TODO add your handling code here:
    	int[] selectRows = jTable1.getSelectedRows();
    	if (selectRows.length == 0) {
			ShowMessage.view("请选择要编辑的操作票");
			return;
		}
    	Object codename= jTable1.getValueAt(selectRows[0], 1);
    	Object codename1= jTable1.getValueAt(selectRows[0], 2);
    	CodeNameModel cnm=(CodeNameModel) codename;
    	if(codename instanceof CodeNameModel){
    		String usecodename=codename1.toString();
    		cnm.setName(usecodename);
    	}
    	
    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.0);
    	OperateTicketSGP otsgp = OperateTicketSGP.getInstance();
    	splitPane.setRightComponent(otsgp);
    	otsgp.initTable(cnm);
		this.setVisible(false);
    }
    private void jButton14ActionPerformed(java.awt.event.ActionEvent evt){
    	JSplitPane splitPane = (JSplitPane)SystemConstants.getGuiBuilder().getComponent("splitPane");
		splitPane.setDividerLocation(0.99999);
		splitPane.setOneTouchExpandable(true);
		splitPane.setDividerSize(12);
		CBSystemConstants.svgAddPd = null;
    }
    private JButton jButton1;
    private JButton jButton2;
    private javax.swing.JButton jButton3;
    private javax.swing.JButton jButton4;
    private javax.swing.JButton jButton5;
	private JButton jButton6;
	private JButton jButton7;
	private JButton jButton8;
    private javax.swing.JButton jButton9;
//	private JButton jButton10;
	private JButton jButton11;
	private JButton jButton12;
    private javax.swing.JButton jButton13;
	private JLabel jLabel1;
	private JLabel jLabel2;
	private JLabel jLabel3;
	private JLabel jLabel4;
	private JLabel jLabel5;
	private JLabel jLabel6;
	private JComboBox jComboBox1;
	private JComboBox jComboBox2;
	private JComboBox jComboBox3;
    private javax.swing.JPanel jPanel1;
    private javax.swing.JPanel jPanel2;
    private javax.swing.JPanel jPanel5;
	private JScrollPane jScrollPane1;
    public javax.swing.JTabbedPane jTabbedPane1;
	private JTable jTable1;
	private JTextField jTextField1;
	private JTextField jTextField2;
	private JTextField jTextField3;
}
