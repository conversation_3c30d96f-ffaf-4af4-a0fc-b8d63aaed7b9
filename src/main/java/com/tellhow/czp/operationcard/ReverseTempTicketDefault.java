package com.tellhow.czp.operationcard;

import java.awt.BorderLayout;
import java.awt.FlowLayout;
import java.awt.Frame;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JPanel;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.JCheckBox;
import java.awt.Color;
import java.awt.Insets;

import javax.swing.DefaultCellEditor;
import javax.swing.GroupLayout;
import javax.swing.GroupLayout.Alignment;
import javax.swing.JLabel;
import javax.swing.JTextField;
import javax.swing.LayoutStyle.ComponentPlacement;
import javax.swing.JTextArea;
import java.awt.Font;
import java.awt.Dimension;
import java.awt.event.WindowEvent;
import java.awt.event.WindowListener;
import java.awt.event.WindowStateListener;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.ImageIcon;

import sun.awt.WindowClosingListener;

import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.DeviceStatusManager;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.util.SvgUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;

public class ReverseTempTicketDefault extends JDialog implements WindowListener{

	private final JPanel contentPanel = new JPanel();
	private JTextField jTextField4;
	private JTextField jTextField3;
	private JTextArea jTextArea1;
	private JTable jTable1;
	private JScrollPane jScrollPane2;
	private DefaultTableModel jTable1Model = null;
	private List<CardItemModel> cimlist=new ArrayList<CardItemModel>();
	protected  List<CardItemModel> itemModels = new ArrayList<CardItemModel>();
	private RuleBaseMode Srcrbm;
	private CardModel cm;



	/**
	 * Launch the application.
	 */
//	public static void main(String[] args) {
//		try {
//			ReverseTempTicketDefault dialog = new ReverseTempTicketDefault(null, false);
//			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
//			dialog.setVisible(true);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}

	/**
	 * Create the dialog.
	 */
	public ReverseTempTicketDefault(Frame parent, boolean isModel) {
		super(parent, isModel);
//		this.setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);
//		this.setUndecorated(true);
		setTitle("\u53CD\u5411\u6210\u7968");
		setBounds(100, 100, 599, 520);
		getContentPane().setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		getContentPane().add(contentPanel, BorderLayout.CENTER);
		
		JLabel jLabel1 = new JLabel("\u62DF\u7968\u4EBA\uFF1A");
		
		jTextField4 = new JTextField();
		jTextField4.setEnabled(false);
		jTextField4.setColumns(10);
		
		JLabel jLabel2 = new JLabel("\u62DF\u7968\u65F6\u95F4\uFF1A");
		
		jTextField3 = new JTextField();
		jTextField3.setColumns(10);
		
		JLabel jLabel5 = new JLabel("\u64CD\u4F5C\u4EFB\u52A1\uFF1A");
		
		jTextArea1 = new JTextArea();
		jTextArea1.setPreferredSize(new Dimension(4, 80));
		jTextArea1.setLineWrap(true);
		jTextArea1.setFont(new Font("宋体", Font.BOLD, 14));
		jTextArea1.setColumns(20);
		
		JButton jButton1 = new JButton();
		jButton1.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/add.gif")));
		jButton1.setToolTipText("\u589E\u52A0");
		jButton1.setText("\u589E\u52A0");
		jButton1.setMargin(new Insets(1, 1, 1, 1));
		jButton1.setFocusPainted(false);
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });
		
		JButton jButton2 = new JButton();
		jButton2.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/delete.gif")));
		jButton2.setToolTipText("\u5220\u9664");
		jButton2.setText("\u5220\u9664");
		jButton2.setMargin(new Insets(1, 1, 1, 1));
		jButton2.setFocusPainted(false);
        jButton2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });
		
		JButton jButton11 = new JButton();
		jButton11.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/import.gif")));
		jButton11.setToolTipText("\u64A4\u9500");
		jButton11.setText("\u64A4\u9500");
		jButton11.setMargin(new Insets(1, 1, 1, 1));
		jButton11.setFocusPainted(false);
        jButton11.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton11ActionPerformed(evt);
            }
        });
		
		JButton jButton10 = new JButton();
		jButton10.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/replace.png")));
		jButton10.setToolTipText("\u66FF\u6362");
		jButton10.setText("\u66FF\u6362");
		jButton10.setMargin(new Insets(1, 1, 1, 1));
        jButton10.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton10ActionPerformed(evt);
            }
        });
		
		JButton jButton3 = new JButton();
		jButton3.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/btn_up.png")));
		jButton3.setToolTipText("\u4E0A\u79FB");
		jButton3.setText("\u4E0A\u79FB");
		jButton3.setMargin(new Insets(1, 1, 1, 1));
		jButton3.setFocusPainted(false);
		
		JButton jButton4 = new JButton();
		jButton4.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/btn_down.png")));
		jButton4.setToolTipText("\u4E0B\u79FB");
		jButton4.setText("\u4E0B\u79FB");
		jButton4.setMargin(new Insets(1, 1, 1, 1));
		jButton4.setFocusPainted(false);
		
		JButton jButton7 = new JButton();
		jButton7.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/merge.png")));
		jButton7.setToolTipText("\u5408\u9879");
		jButton7.setText("\u5408\u9879");
		jButton7.setMargin(new Insets(1, 1, 1, 1));
		jButton7.setFocusPainted(false);
		
		JButton jButton8 = new JButton();
		jButton8.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/split.png")));
		jButton8.setToolTipText("\u5206\u9879");
		jButton8.setText("\u5206\u9879");
		jButton8.setMargin(new Insets(1, 1, 1, 1));
		jButton8.setFocusPainted(false);
		
		jScrollPane2 = new JScrollPane();
		GroupLayout gl_contentPanel = new GroupLayout(contentPanel);
		gl_contentPanel.setHorizontalGroup(
			gl_contentPanel.createParallelGroup(Alignment.LEADING)
				.addGroup(gl_contentPanel.createSequentialGroup()
					.addContainerGap()
					.addGroup(gl_contentPanel.createParallelGroup(Alignment.LEADING, false)
						.addGroup(gl_contentPanel.createSequentialGroup()
							.addComponent(jLabel1, GroupLayout.PREFERRED_SIZE, 48, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.UNRELATED)
							.addComponent(jTextField4, GroupLayout.PREFERRED_SIZE, 106, GroupLayout.PREFERRED_SIZE)
							.addGap(98)
							.addComponent(jLabel2)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jTextField3))
						.addGroup(gl_contentPanel.createSequentialGroup()
							.addComponent(jLabel5, GroupLayout.PREFERRED_SIZE, 60, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jTextArea1, GroupLayout.PREFERRED_SIZE, 470, GroupLayout.PREFERRED_SIZE))
						.addGroup(gl_contentPanel.createSequentialGroup()
							.addGap(24)
							.addComponent(jButton1, GroupLayout.PREFERRED_SIZE, 50, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton2, GroupLayout.PREFERRED_SIZE, 50, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton11, GroupLayout.PREFERRED_SIZE, 50, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton10, GroupLayout.PREFERRED_SIZE, 51, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton3, GroupLayout.PREFERRED_SIZE, 50, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton4, GroupLayout.PREFERRED_SIZE, 50, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton7, GroupLayout.PREFERRED_SIZE, 50, GroupLayout.PREFERRED_SIZE)
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jButton8, GroupLayout.PREFERRED_SIZE, 50, GroupLayout.PREFERRED_SIZE))
						.addComponent(jScrollPane2))
					.addContainerGap(29, Short.MAX_VALUE))
		);
		gl_contentPanel.setVerticalGroup(
			gl_contentPanel.createParallelGroup(Alignment.LEADING)
				.addGroup(gl_contentPanel.createSequentialGroup()
					.addContainerGap()
					.addGroup(gl_contentPanel.createParallelGroup(Alignment.BASELINE)
						.addComponent(jLabel1)
						.addComponent(jLabel2)
						.addComponent(jTextField3, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
						.addComponent(jTextField4, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))
					.addGroup(gl_contentPanel.createParallelGroup(Alignment.LEADING)
						.addGroup(gl_contentPanel.createSequentialGroup()
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jTextArea1, GroupLayout.PREFERRED_SIZE, 55, GroupLayout.PREFERRED_SIZE))
						.addGroup(Alignment.TRAILING, gl_contentPanel.createSequentialGroup()
							.addPreferredGap(ComponentPlacement.RELATED)
							.addComponent(jLabel5, GroupLayout.PREFERRED_SIZE, 57, GroupLayout.PREFERRED_SIZE)))
					.addPreferredGap(ComponentPlacement.UNRELATED)
					.addGroup(gl_contentPanel.createParallelGroup(Alignment.BASELINE)
						.addComponent(jButton8, GroupLayout.PREFERRED_SIZE, 22, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton7, GroupLayout.PREFERRED_SIZE, 22, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton4, GroupLayout.PREFERRED_SIZE, 22, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton3, GroupLayout.PREFERRED_SIZE, 22, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton10)
						.addComponent(jButton11, GroupLayout.PREFERRED_SIZE, 22, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton2, GroupLayout.PREFERRED_SIZE, 22, GroupLayout.PREFERRED_SIZE)
						.addComponent(jButton1, GroupLayout.PREFERRED_SIZE, 22, GroupLayout.PREFERRED_SIZE))
					.addPreferredGap(ComponentPlacement.RELATED)
					.addComponent(jScrollPane2, GroupLayout.DEFAULT_SIZE, 253, Short.MAX_VALUE)
					.addContainerGap())
		);
		
		jTable1 = new JTable();
		jScrollPane2.setColumnHeaderView(jTable1);
		contentPanel.setLayout(gl_contentPanel);
		{
			JPanel buttonPane = new JPanel();
			buttonPane.setLayout(new FlowLayout(FlowLayout.RIGHT));
			getContentPane().add(buttonPane, BorderLayout.SOUTH);
			
			JButton jButton5 = new JButton();
			jButton5.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/save.png")));
			jButton5.setToolTipText("\u4FDD\u5B58");
			jButton5.setText("\u4FDD\u5B58");
			jButton5.setMargin(new Insets(1, 1, 1, 1));
			jButton5.addActionListener(new java.awt.event.ActionListener() {
	            public void actionPerformed(java.awt.event.ActionEvent evt) {
	                jButton5ActionPerformed(evt);
	            }
	        });
			buttonPane.add(jButton5);
			{
				JPanel panel = new JPanel();
				buttonPane.add(panel);
				panel.setLayout(new FlowLayout(FlowLayout.RIGHT));
				{
					JButton button = new JButton();
					button.setIcon(new ImageIcon(ReverseTempTicketDefault.class.getResource("/tellhow/btnIcon/back.png")));
					button.setToolTipText("\u53D6\u6D88");
					button.setText("\u53D6\u6D88");
					button.setMargin(new Insets(1, 1, 1, 1));
					button.addActionListener(new java.awt.event.ActionListener() {
			            public void actionPerformed(java.awt.event.ActionEvent evt) {
//			                jButton6ActionPerformed(evt);
			                TempTicket.isRoll=true;
			                jButton6ActionPerformed(evt);
			                TempTicket.isRoll=false;
			            }
			        });
					panel.add(button);
				}
			}
		}
	}
	
	//新增
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.addTableRow(jTable1);
		inittable();
	}
	
	//取消
	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		
//		if(isInversing) {
//			ShowMessage.view(this, "请先停止操作票演示再关闭！");
//			return;
//		}
		//回滚
		int k = DeviceOperate.getAlltransDevMap().size();
		DeviceOperate.getAlltransDevMap().clear();
		for(int i =1;i<=DeviceOperate.getReversetransDevMap().size();i++){
			if(DeviceOperate.getReversetransDevMap().get(i).getComment().equals("是反向成票")){
				for(int j = 1; j < i; j++){
					System.out.println(j+k+1);
					DeviceOperate.getReversetransDevMap().get(j+k+1);
					DeviceOperate.getAlltransDevMap().put(j,DeviceOperate.getReversetransDevMap()
							.get(j+k+1));
				}
				break;
			}
			
		}
		CBSystemConstants.reverseCancel = true;

		DeviceOperate.RollbackDeviceStatus();
//		DispatchTransDevice dtd = new DispatchTransDevice();
//		for(int j=DeviceOperate.getAlltransDevMap().size();j>0;j--){
//			dtd = DeviceOperate.getAlltransDevMap().get(j);
//			String begin = dtd.getBeginstatus();
//			String end = dtd.getEndstate();
//			dtd.setBeginstatus(end);
//			dtd.setEndstate(begin);
//			Map<Integer, DispatchTransDevice> devMap  = new HashMap<Integer, DispatchTransDevice>();
////			devMap.put(j, dtd);
////			DeviceOperate.setAlltransDevMap(devMap);
//		}
		//清空
		DeviceOperate.ClearDevMap();
		CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
		CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
    	CBSystemConstants.bztRelationRecord.clear();
		CBSystemConstants.bztStateRecord.clear();
		CBSystemConstants.bztOrganRecord.clear();
		this.setVisible(false);
		SvgUtil.clear();
	}
	
	private void inittable(){
		if(jTable1Model!=null){
			int n=jTable1Model.getRowCount();
			for(int i=0;i<n;i++){
				if(jTable1Model.getValueAt(i, 1) == null) {
					if(i == 0)
						jTable1Model.setValueAt(1, i, 1);
					else
						jTable1Model.setValueAt(Integer.valueOf(jTable1Model.getValueAt(i-1, 1).toString())+1, i, 1);
				}
				if(jTable1Model.getValueAt(i, 2) == null) {
					if(i == 0)
						jTable1Model.setValueAt(1, i, 2);
					else
						jTable1Model.setValueAt(Integer.valueOf(jTable1Model.getValueAt(i-1, 2).toString())+1, i, 2);
				}
			}
		}
	}
	
	//删除
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.removeTableRow(jTable1);
		inittable();
	}
	
	//撤销
	private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {
		//if(CBSystemConstants.cardbuildtype.endsWith("1")){
			int all=cimlist.size();
			int sum=0;
			int allrow=jTable1Model.getRowCount();
			
			int bzbj = DeviceOperate.getBzIndex();
			if(bzbj > 0) {
				DeviceOperate.RollbackLastStatusStep(String.valueOf(bzbj));
				for(int i=itemModels.size()-1;i>=0;i--){
					if(itemModels.get(i).getBzbj().equals(String.valueOf(bzbj)))
						itemModels.remove(i);
				}
				DeviceOperate.setBzIndex(bzbj-1);
			}
			initTable();
	}
	
	public void initTable() {
		List<CardItemModel> itemModelsShow = new ArrayList<CardItemModel>();
		for(CardItemModel cim : itemModels) {
			itemModelsShow.add(cim);
		}
		//设置顺序
		String usedNum = "1";
		for (int i = 0; i < itemModelsShow.size(); i++) {
			if(itemModelsShow.get(i).getCardItem().equals("")) {
				if(i == 0)
					itemModelsShow.get(i).setCardItem("1");
				else
					itemModelsShow.get(i).setCardItem(String.valueOf(i+1));
			}
			
			if(itemModelsShow.get(i).getCardItem().equals(usedNum)&&!itemModelsShow.get(i).getCardNum().equals(usedNum)){
				itemModelsShow.get(i).setCardNum("");
			}else if(!itemModelsShow.get(i).getCardItem().equals(usedNum)){
				usedNum = itemModelsShow.get(i).getCardItem() ;
				itemModelsShow.get(i).setCardNum(usedNum);
			}
		}
		while(jTable1Model.getRowCount() != 0) {
			jTable1Model.removeRow(jTable1Model.getRowCount()-1);
		}
		if (itemModelsShow != null) {
			for (int i = 0; i < itemModelsShow.size(); i++) {
				CardItemModel cim = itemModelsShow.get(i);
				Object[] rowData = { cim, cim.getCardNum(),cim.getCardItem(),cim.getShowName(), cim.getCardDesc() };
				jTable1Model.addRow(rowData);
			}
		}
	}
	
	//替换
	private void jButton10ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		ReplaceWord rw = new ReplaceWord(SystemConstants.getMainFrame(), false, jTable1);
    	rw.setVisible(true);
	}
	
	/**
	 * 
	 * @param zb
	 * @param mx
	 * <AUTHOR>
	 * @since 2014-10-17 8:44
	 */
	public void init (String[] zb,List<BaseCardModel> mx) {
		int startItem = 0;
		this.Srcrbm=Srcrbm;
		Object[][] tableDate = null;
		if (jTable1Model == null)
			jTable1Model = new DefaultTableModel(tableDate, new String[] {
					"顺序", "操作单位", "操作内容" });
		else if(jTable1Model.getRowCount() > 0) {
			startItem = Integer.valueOf(((CodeNameModel)jTable1Model.getValueAt(jTable1Model.getRowCount()-1, 0)).getName());
		}
		
		for (int i = 0; i < mx.size(); i++) {
			BaseCardModel bcm = mx.get(i);
			CodeNameModel cnm=new CodeNameModel(bcm.getMxid(),bcm.getCardSub());
			Object[] rowData = { cnm, bcm.getStationName(), bcm.getCardDesc() };
			jTable1Model.addRow(rowData);
		}
		
		if (jTable1 == null)
			jTable1 = new JTable();
		jTable1.setModel(jTable1Model);
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);

		DefaultCellEditor cellEdit = new DefaultCellEditor(new JTextField());
		cellEdit.setClickCountToStart(2);//双击后使选择的格子可编辑
		jTable1.getColumnModel().getColumn(0).setMinWidth(50);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(60);
		jTable1.getColumnModel().getColumn(1).setMinWidth(100);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(120);
		jScrollPane2.setViewportView(jTable1);
		
		//拟票时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date dt = new Date();
		jTextField3.setText(sdf.format(dt));
		//拟票人
		jTextField4.setText(CBSystemConstants.getUser().getUserName());
		//操作任务
		if (jTextArea1.getText().trim().equals(""))
			jTextArea1.setText(zb[2]);
		
		boolean isMultiUnit = false;
		String preStationName = "";
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			String stationName = jTable1.getValueAt(i, 1).toString();
			if(!stationName.equals("")) {
				if(!preStationName.equals("") && !preStationName.equals(stationName)) {
					isMultiUnit = true;
					break;
				}
				preStationName = stationName;
			}
		}
		if("1".equals(CBSystemConstants.unitCode)){
			jTable1.getColumnModel().getColumn(1).setMaxWidth(0);
			jTable1.getColumnModel().getColumn(1).setMinWidth(0);
			jTable1.getColumnModel().getColumn(1).setPreferredWidth(0);
			jTable1.getColumnModel().getColumn(1).setResizable(false);
		}
		this.setVisible(true);
	}
	
	public void init(CardModel cm,RuleBaseMode Srcrbm) {
		if (cm == null)
			return;

		this.Srcrbm=Srcrbm;
		this.cm = cm;
		//cmlist.add(cm);
		for(int i=0;i<cm.getCardItems().size();i++){
			cimlist.add(cm.getCardItems().get(i));
		}
		//if(cm.getCardItems().size() > 0)
		//	bzbj.add(cm.getCardItems().get(0).getBzbj());
		Object[][] tableDate = null;
		if (jTable1Model == null)
			jTable1Model = new DefaultTableModel(tableDate, new String[] {
					"","序号","顺序", "操作单位", "操作内容" });

		if (jTable1 == null)
			jTable1 = new JTable();
		jTable1.setModel(jTable1Model);
		itemModels.clear();
		List<CardItemModel> DescList = getItemModel();
		for(CardItemModel cim : DescList) {
			if(!cim.getCardDesc().equals(""))
				itemModels.add(cim);
		}
		for(CardItemModel cim: cm.getCardItems()) {
			//cim.setBzbj(String.valueOf(bzbj));
			itemModels.add(cim);
		}
		initTable ();

//		jTable1.addMouseListener(new MouseAdapter() {
//			@Override
//			public void mouseClicked(MouseEvent e) {
//				// TODO Auto-generated method stub
//				Point mousepoint; 
//				mousepoint =e.getPoint();
//				int row=jTable1.rowAtPoint(mousepoint);
//				int column=jTable1.columnAtPoint(mousepoint);
//				if(jTable1.editCellAt(row, column)){
//					 Component editor = jTable1.getEditorComponent();
//					    editor.requestFocusInWindow();
//					    Component c = editor.getComponentAt(0, 0);
//					    if (c != null && c instanceof JTextComponent) {
//					        ((JTextComponent) c).selectAll();
//					        tjc=(JTextComponent)c;
//					    }
//				}
//			}
//		});
	
		SetJTableProtery sjp = new SetJTableProtery();
		sjp.getTableHeader(jTable1);//列名居中
		sjp.getDefaultLeft(jTable1.getColumnClass(1), jTable1);

		DefaultCellEditor cellEdit = new DefaultCellEditor(new JTextField());
		cellEdit.setClickCountToStart(2);//双击后使选择的格子可编辑
		jTable1.getColumnModel().getColumn(1).setMinWidth(50);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(60);
		jTable1.getColumnModel().getColumn(3).setMinWidth(120);
		jTable1.getColumnModel().getColumn(3).setMaxWidth(140);
		jScrollPane2.setViewportView(jTable1);
		//拟票时间
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date dt = new Date();
		jTextField3.setText(sdf.format(dt));
		//拟票人
		jTextField4.setText(CBSystemConstants.getUser().getUserName());
		//操作任务
		if(CBSystemConstants.roleCode.equals("0")) {
			if (!cm.getCzrw().equals("") && jTextArea1.getText().equals(""))
				jTextArea1.setText(cm.getCzrw());
		}
		else if(CBSystemConstants.roleCode.equals("2")) {
			if (!cm.getCzrw().equals(""))
				jTextArea1.setText(cm.getCzrw());
		}
		jTable1.getColumnModel().getColumn(0).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(0).setMinWidth(0);
		jTable1.getColumnModel().getColumn(0).setPreferredWidth(0);
		
		jTable1.getColumnModel().getColumn(2).setMaxWidth(0);
		jTable1.getColumnModel().getColumn(2).setMinWidth(0);
		jTable1.getColumnModel().getColumn(2).setPreferredWidth(0);
		
		if("1".equals(CBSystemConstants.unitCode)){
			jTable1.getColumnModel().getColumn(3).setMaxWidth(0);
			jTable1.getColumnModel().getColumn(3).setMinWidth(0);
			jTable1.getColumnModel().getColumn(3).setPreferredWidth(0);
			jTable1.getColumnModel().getColumn(3).setResizable(false);
		}
		this.setVisible(true);
	}
	
	private List<CardItemModel> getItemModel() {
		String preStationName = "";
		List<CardItemModel> DescList = new ArrayList<CardItemModel>();
		if(jTable1Model.getRowCount() > 0) {
			for (int i = 0; i < jTable1Model.getRowCount(); i++) {
				CardItemModel cim = new CardItemModel();
				if(jTable1Model.getValueAt(i, 0) instanceof CardItemModel) {
					CardItemModel cnm=(CardItemModel)jTable1Model.getValueAt(i, 0);
				    cim.setUuIds(cnm.getUuIds());
				    cim.setBzbj(cnm.getBzbj());
				}
				else {
					cim.setUuIds(StringUtils.getUUID());
				}
				
			    cim.setCardNum(jTable1Model.getValueAt(i, 1).toString());
			    cim.setCardItem(StringUtils.ObjToString(jTable1Model.getValueAt(i, 2)));
			    
			    String showName = StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
			    cim.setShowName(showName);
			    
			    String cardDesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
				cim.setCardDesc(cardDesc);
				
			    String stationName= StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
			    if(cardDesc.equals(""))
			    	stationName = "";
			    else if((stationName.equals("") || stationName.indexOf("（")>=0))
			    	stationName = preStationName;
				cim.setStationName(stationName);
				
				DescList.add(cim);
				if(!stationName.equals("") && stationName.indexOf("（")==-1)
					preStationName = stationName;
			}
		}
		return DescList;
	}
	
	//保存
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
//		if(isInversing) {
//			ShowMessage.view(this, "请先停止操作票演示再保存！");
//			return;
//		}
		int row = jTable1.getRowCount();
		if (row == 0) {
			ShowMessage.view(this, "数据为空,保存失败！");
			return;
		}
		if (jTextArea1.getText().trim().equals("")) {
			ShowMessage.view(this, "操作任务不能为空,保存失败！");
			return;
		}
//		if(!checkContent())
//			return;
		
		//单元格编辑状态下点击保存
		if (jTable1.isEditing())
			jTable1.getCellEditor().stopCellEditing();
		String czrw = jTextArea1.getText().trim();//编辑修改后的操作任务名称
		String jxpdh = "";//检修票单号
		String cardKind = "0";
		List<CardItemModel> DescList = getItemModel();
		jTable1Model =  (DefaultTableModel) jTable1.getModel();
		
		List<String> stationList = new ArrayList<String>();
		for (int i = 0; i < jTable1.getRowCount(); i++) {
			
		    String cardDesc = StringUtils.ObjToString(jTable1Model.getValueAt(i, 4)).trim();
		    String stationName= StringUtils.ObjToString(jTable1Model.getValueAt(i, 3)).trim();
		    if(!stationName.equals("") && !cardDesc.equals("") && !stationList.contains(stationName))
		    	stationList.add(stationName);
		}
		if(CBSystemConstants.roleCode.equals("2"))
			cardKind = "3";
		else if(stationList.size() >= 2)
			cardKind = "1";
		//保存设备术语，设备预令状态
		Connection conn = DBManager.getConnection();

		TicketDBManager ticketDB = new TicketDBManager();
		try {
			String cardid = StringUtils.getUUID();

			conn.setAutoCommit(false);
           //插入票信息
			ticketDB.InsertTicketDB(conn, cardid,DescList, czrw, "0", cardKind, CBSystemConstants.cardbuildtype, Srcrbm.getPd().getPowerDeviceID(),jxpdh);
			
		
			DeviceStatusManager dsm = new DeviceStatusManager();
			dsm.insertDevState(conn, cardid); // 保存操作票对应设备元件状态变更
			dsm.insertDevStatus(conn); // 保存操作票对应设备预令状态
			
			conn.commit();
			conn.close();
//			if (this.jCheckBox1.isSelected()) {
//				ticketDB.InsertDXTicketDB(cardid);
//			}
			
			//初始化操作票面板
//			OperateTicketTypePanelDefault otp = new OperateTicketTypePanelDefault();
			OperateTicketTypePanel otp = OperateTicketTypePanel.getInstance();
//			otp.jTabbedPane1.setSelectedIndex(0);
			otp.initTable();


			CBSystemConstants.clearLineSourceAndLoad();
			ShowMessage.view(this, "保存成功！");
			
			//DeviceOperate.RollbackDeviceStatus();
		} catch (SQLException e) {
			try {
				conn.rollback();
				conn.close();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
			//回滚
			DeviceOperate.RollbackDeviceStatus();
			ShowMessage.view(this, "保存失败！");  
		} finally {
			
			if(CBSystemConstants.roleCode.equals("2")){
//				n=n+1;
//				ShowMessage.view(this,"哈哈哈");
				Map<Integer, DispatchTransDevice> all=DeviceOperate.getAlltransDevMap();
				for(int i=1;i<all.size()+1;i++){
					DispatchTransDevice dtd=all.get(i);
					dtd.setIssave("1");
				}
			}
			
			//监控票操作任务未解决！！！
//			if(n>0){
////				CBSystemConstants.getDtdMap().clear();
////				DeviceOperate.ClearDevMap();
//			}else{
//				//清空
//				DeviceOperate.ClearDevMap();
//				CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
//				CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
//		    	//CBSystemConstants.bztRelationRecord.clear();
//				CBSystemConstants.bztStateRecord.clear();
//				//CBSystemConstants.bztOrganRecord.clear();
//				SvgUtil.clear();
//				this.setVisible(false);
//				this.tempTicket = null;
//			}
			//清空
			DeviceOperate.ClearDevMap();
			CBSystemConstants.bztMLOperatedList.removeAll(CBSystemConstants.bztMLOperatedList);
			CBSystemConstants.bztRelationOperatedList.removeAll(CBSystemConstants.bztRelationOperatedList);
	    	//CBSystemConstants.bztRelationRecord.clear();
			CBSystemConstants.bztStateRecord.clear();
			//CBSystemConstants.bztOrganRecord.clear();
			SvgUtil.clear();
//			setVisible(false);
			this.setVisible(false);
			//tempTicket = null;
		}
	}

	@Override
	public void windowOpened(WindowEvent e) {
		// TODO Auto-generated method stub
		System.out.println(3);
	}

	@Override
	public void windowClosing(WindowEvent e) {
		// TODO Auto-generated method stub
		System.out.println(3);
	}

	@Override
	public void windowClosed(WindowEvent e) {
		// TODO Auto-generated method stub
		System.out.println(3);
	}

	@Override
	public void windowIconified(WindowEvent e) {
		// TODO Auto-generated method stub
		System.out.println(3);
	}

	@Override
	public void windowDeiconified(WindowEvent e) {
		// TODO Auto-generated method stub
		System.out.println(3);
	}

	@Override
	public void windowActivated(WindowEvent e) {
		// TODO Auto-generated method stub
		System.out.println(3);
	}

	@Override
	public void windowDeactivated(WindowEvent e) {
		// TODO Auto-generated method stub
		System.out.println(3);
	}
}
