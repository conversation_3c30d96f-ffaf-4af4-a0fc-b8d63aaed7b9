/*
 * OperateTicketMLPLoadMX.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.operationcard;

import java.awt.Toolkit;
import java.util.List;

import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;

import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.operationcard.model.BaseCardModel;

import czprule.model.CodeNameModel;

/**
 *
 * <AUTHOR>
 */
public class OperateTicketMLPLoadMX extends javax.swing.JDialog {
	private SetJTableProtery sjp = new SetJTableProtery();

	/** Creates new form OperateTicketMLPLoadMX */
	public OperateTicketMLPLoadMX(javax.swing.JDialog parent, boolean modal) {
		super(parent, modal);
		initComponents();
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	public void init(CodeNameModel cnm) {
		if (cnm == null)
			return;
		this.setTitle("操作票明细");
		this.jTextArea1.setEditable(false);
		this.jTextArea1.setText(cnm.getName());
		DefaultTableModel jtableModel1 = new DefaultTableModel(null,
				new String[] { "序号", "操作单位", "操作内容" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		TicketDBManager tdb = new TicketDBManager();
		List<BaseCardModel> results = tdb.queryTicketMX(cnm.getCode());
		BaseCardModel bcm = null;
		for (int i = 0; i < results.size(); i++) {
			bcm = results.get(i);
			Object[] rowData = { String.valueOf(i+1), bcm.getCzsn().equals("")?bcm.getStationName():bcm.getCzsn(),
					bcm.getCardDesc() };
			jtableModel1.addRow(rowData);
		}
		jTable1.setRowHeight(26);
		jTable1.setModel(jtableModel1);
		sjp.makeFace(jTable1);
		sjp.getTableHeader(jTable1);//列名居中
		TableColumnModel tcm = jTable1.getColumnModel();
		tcm.getColumn(0).setMaxWidth(60);
		tcm.getColumn(1).setMaxWidth(140);
		tcm.getColumn(1).setMinWidth(100);
		this.setVisible(true);
	}

	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jLabel1 = new javax.swing.JLabel();
		jScrollPane1 = new javax.swing.JScrollPane();
		jTextArea1 = new javax.swing.JTextArea();
		jScrollPane2 = new javax.swing.JScrollPane();
		jTable1 = new javax.swing.JTable();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jLabel1.setText("\u64cd\u4f5c\u4efb\u52a1:");

		jTextArea1.setColumns(20);
		jTextArea1.setFont(new java.awt.Font("宋体", 1, 14));
		jScrollPane1.setViewportView(jTextArea1);

		jTable1.setFont(new java.awt.Font("宋体", 0, 13));
		jScrollPane2.setViewportView(jTable1);

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(org.jdesktop.layout.GroupLayout.TRAILING,
						layout.createSequentialGroup()
								.addContainerGap()
								.add(layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.TRAILING)
										.add(org.jdesktop.layout.GroupLayout.LEADING,
												jScrollPane2,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												547, Short.MAX_VALUE)
										.add(layout
												.createSequentialGroup()
												.add(jLabel1,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
														60,
														org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
												.addPreferredGap(
														org.jdesktop.layout.LayoutStyle.RELATED)
												.add(jScrollPane1,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														482, Short.MAX_VALUE)))
								.addContainerGap()));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.addContainerGap()
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.LEADING)
								.add(jLabel1)
								.add(jScrollPane1,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
										52,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(jScrollPane2,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								374, Short.MAX_VALUE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				OperateTicketMLPLoadMX dialog = new OperateTicketMLPLoadMX(
						null, true);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JLabel jLabel1;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JTable jTable1;
	private javax.swing.JTextArea jTextArea1;
	// End of variables declaration//GEN-END:variables

}
