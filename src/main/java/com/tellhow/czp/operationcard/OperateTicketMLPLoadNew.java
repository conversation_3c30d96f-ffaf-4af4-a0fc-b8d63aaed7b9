package com.tellhow.czp.operationcard;

import java.awt.BorderLayout;
import java.awt.FlowLayout;
import java.util.List;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JPanel;
import javax.swing.UIManager;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;
import javax.swing.JScrollPane;
import javax.swing.JTable;

import com.tellhow.czp.basic.SetJTableProtery;
import com.tellhow.czp.operationcard.dao.TicketDBManager;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;

import javax.swing.JLabel;
import javax.swing.JTextField;
import javax.swing.border.TitledBorder;
import java.awt.Color;
import java.awt.Dimension;

public class OperateTicketMLPLoadNew extends JDialog {
	private JTable zbTable;
	DefaultTableModel zbTableModel;
	private JTable mxTable;
	DefaultTableModel mxTableModel;
	private String cardType = ""; //0：典型票 1：正常票
	private JTextField jTextField1;
	private JTextField jTextField2;
	private JTextField jTextField3;
	private SetJTableProtery sjp = new SetJTableProtery();

	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		
		//设置外观
				try {
					UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
				}
				catch(Exception ex) {
					ex.printStackTrace();
				}
		try {
			OperateTicketMLPLoadNew dialog = new OperateTicketMLPLoadNew();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * Create the dialog.
	 */
	public OperateTicketMLPLoadNew() {
		setBounds(100, 100, 800, 600);
		getContentPane().setLayout(new BorderLayout(0, 0));
		{
			JPanel buttonPane = new JPanel();
			getContentPane().add(buttonPane, BorderLayout.NORTH);
			buttonPane.setPreferredSize(new Dimension(800, 100));
			buttonPane.setSize(new Dimension(800, 100));
			buttonPane.setLayout(null);
			
			jTextField1 = new JTextField();
			jTextField1.setBounds(288, 5, 66, 21);
			buttonPane.add(jTextField1);
			jTextField1.setColumns(10);
			
			jTextField2 = new JTextField();
			jTextField2.setBounds(359, 5, 66, 21);
			buttonPane.add(jTextField2);
			jTextField2.setColumns(10);
			
			jTextField3 = new JTextField();
			jTextField3.setBounds(430, 5, 66, 21);
			buttonPane.add(jTextField3);
			jTextField3.setColumns(10);
		}
		{
			JScrollPane zbPane = new JScrollPane();
			zbPane.setPreferredSize(new Dimension(800, 200));
			zbPane.setSize(new Dimension(800, 200));
			zbPane.setMinimumSize(new Dimension(800, 200));
			zbPane.setBorder(new TitledBorder(UIManager.getBorder("TitledBorder.border"), "\u64CD\u4F5C\u4EFB\u52A1", TitledBorder.LEADING, TitledBorder.TOP, null, new Color(0, 0, 0)));
			getContentPane().add(zbPane, BorderLayout.CENTER);
			{
				zbTable = new JTable();
				zbTable.setPreferredSize(new Dimension(800, 200));
				zbTable.setSize(new Dimension(800, 200));
				
				zbPane.setViewportView(zbTable);
			}
		}
		{
			JScrollPane mxPane = new JScrollPane();
			mxPane.setPreferredSize(new Dimension(800, 200));
			mxPane.setSize(new Dimension(800, 200));
			mxPane.setMinimumSize(new Dimension(800, 200));
			mxPane.setBorder(new TitledBorder(null, "\u64CD\u4F5C\u5185\u5BB9", TitledBorder.LEADING, TitledBorder.TOP, null, null));
			getContentPane().add(mxPane, BorderLayout.SOUTH);
			{
				mxTable = new JTable();
				mxTable.setPreferredSize(new Dimension(800, 600));
				mxTable.setSize(new Dimension(800, 600));
				mxPane.setViewportView(mxTable);
			}
		}
		initMLPLoad();
		initMLPLoadEx();
	}
	
	/**
	 * 初始化表格
	 */
	public void initMLPLoadEx() {

		if (mxTableModel == null) {
			mxTableModel = new DefaultTableModel(null, new String[] { "序号",
					"操作任务", "拟票时间" }) {
				public boolean isCellEditable(int rowIndex, int columnIndex) {
					return false;
				}
			};
		}
		mxTableModel.setRowCount(0);
		TicketDBManager tdb = new TicketDBManager();
		List<String[]> results = null;
		if ("0".equals(cardType)) {
			results = tdb.queryDXTicketZB(this.jTextField1.getText().trim(),
					this.jTextField2.getText().trim(), this.jTextField3.getText().trim(),CBSystemConstants.roleCode);
		} else {
			if(CBSystemConstants.roleCode.equals("0"))
				results = tdb.queryTicketZB(this.jTextField1.getText().trim(),this.jTextField2.getText().trim(), this.jTextField3.getText().trim(), "", "");
			else if(CBSystemConstants.roleCode.equals("2"))
				results = tdb.queryTicketZBJK(this.jTextField1.getText().trim(),this.jTextField2.getText().trim(), this.jTextField3.getText().trim(), "", "");
		}
		String[] tempStr = null;
		CodeNameModel cnn = null;
		String npsj = "";
		for (int i = 0; i < results.size(); i++) {
			tempStr = results.get(i);

			if ("0".equals(cardType)) {
				cnn = new CodeNameModel(tempStr[0], tempStr[1]);
				npsj = tempStr[3];
			} else {
				cnn = new CodeNameModel(tempStr[0], tempStr[2]);
				npsj = tempStr[4];
			}
			Object[] rowData = { String.valueOf(i + 1), cnn, npsj };
			mxTableModel.addRow(rowData);
		}
		mxTable.setModel(mxTableModel);
		sjp.makeFace(mxTable);
		sjp.getTableHeader(mxTable);//列名居中
		TableColumnModel tcm = mxTable.getColumnModel();
		tcm.getColumn(0).setMaxWidth(50);
		tcm.getColumn(2).setMaxWidth(170);
		tcm.getColumn(2).setMinWidth(145);
	}
	
	/**
	 * 初始化表格
	 */
	public void initMLPLoad() {

		if (zbTableModel == null) {
			zbTableModel = new DefaultTableModel(null, new String[] { "序号",
					"操作任务", "拟票时间" }) {
				public boolean isCellEditable(int rowIndex, int columnIndex) {
					return false;
				}
			};
		}
		zbTableModel.setRowCount(0);
		TicketDBManager tdb = new TicketDBManager();
		List<String[]> results = null;
		if ("0".equals(cardType)) {
			results = tdb.queryDXTicketZB(this.jTextField1.getText().trim(),
					this.jTextField2.getText().trim(), this.jTextField3.getText().trim(),CBSystemConstants.roleCode);
		} else {
			if(CBSystemConstants.roleCode.equals("0"))
				results = tdb.queryTicketZB(this.jTextField1.getText().trim(),this.jTextField2.getText().trim(), this.jTextField3.getText().trim(), "", "");
			else if(CBSystemConstants.roleCode.equals("2"))
				results = tdb.queryTicketZBJK(this.jTextField1.getText().trim(),this.jTextField2.getText().trim(), this.jTextField3.getText().trim(), "", "");
		}
		String[] tempStr = null;
		CodeNameModel cnn = null;
		String npsj = "";
		for (int i = 0; i < results.size(); i++) {
			tempStr = results.get(i);

			if ("0".equals(cardType)) {
				cnn = new CodeNameModel(tempStr[0], tempStr[1]);
				npsj = tempStr[3];
			} else {
				cnn = new CodeNameModel(tempStr[0], tempStr[2]);
				npsj = tempStr[4];
			}
			Object[] rowData = { String.valueOf(i + 1), cnn, npsj };
			zbTableModel.addRow(rowData);
		}
		zbTable.setModel(zbTableModel);
		sjp.makeFace(zbTable);
		sjp.getTableHeader(zbTable);//列名居中
		TableColumnModel tcm = zbTable.getColumnModel();
		tcm.getColumn(0).setMaxWidth(50);
		tcm.getColumn(2).setMaxWidth(170);
		tcm.getColumn(2).setMinWidth(145);
	}
}
