package com.tellhow.czp.sysconfig;

import java.awt.Font;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;

import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartPanel;
import org.jfree.chart.ChartUtilities;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.CategoryAxis;
import org.jfree.chart.axis.ValueAxis;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.data.category.CategoryDataset;
import org.jfree.data.category.DefaultCategoryDataset;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class TimeSeriesChart {
	ChartPanel frame1;
	JFreeChart jfreechart;
	
	private static FileOutputStream fileOut = null;
	private static BufferedImage bufferImg = null;
	public TimeSeriesChart(String zb,String zq,String bt,String et){
		CategoryDataset xydataset = createDataset(bt,et,zq);
		System.out.println(xydataset+"=======是否获取折线图的数据===============");
		 jfreechart = ChartFactory.createLineChart3D("执行操作票数曲线", zq, zb,xydataset, PlotOrientation.VERTICAL, true, true, true);
		//JFreeChart jfreechart = ChartFactory.createTimeSeriesChart("", zq, zb,xydataset, true, true, true);
		CategoryPlot plot=jfreechart.getCategoryPlot();//获取图表区域对象
       
		CategoryAxis domainAxis=plot.getDomainAxis();         //水平底部列表
		domainAxis.setMaximumCategoryLabelLines(4);// 这边的4代表最多显示4行字
         domainAxis.setLabelFont(new Font("宋体",Font.BOLD,14));         //水平底部标题
         domainAxis.setTickLabelFont(new Font("宋体",Font.BOLD,12));  //垂直标题
         ValueAxis rangeAxis=plot.getRangeAxis();//获取柱状
         rangeAxis.setLabelFont(new Font("黑体",Font.BOLD,15));
          jfreechart.getLegend().setItemFont(new Font("黑体", Font.BOLD, 15));
          jfreechart.getTitle().setFont(new Font("宋体",Font.BOLD,20));//设置标题字体
		
		
		
		

        frame1=new ChartPanel(jfreechart,true);

	} 
	public void exportexcel(String pathOfPicture,String pathOfExcel){      
		try {
			OutputStream os = new FileOutputStream(pathOfPicture);
			//OutputStream osExl = new FileOutputStream(pathOfExcel);
			try {
				// 由ChartUtilities生成文件到一个体outputStream中去
				ChartUtilities.writeChartAsJPEG(os, jfreechart, 1000, 800);
				//ChartUtilities.writeChartAsJPEG(osExl, chart, 100, 80);
			} catch (IOException e) {
				e.printStackTrace();
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		}
		//处理图片文件，以便产生ByteArray
		ByteArrayOutputStream handlePicture = new ByteArrayOutputStream();
		handlePicture = handlePicture(pathOfPicture);
		//创建一个工作簿
		HSSFWorkbook wb = new HSSFWorkbook();
		HSSFSheet sheet = wb.createSheet("picture sheet");
		HSSFPatriarch patriarch = sheet.createDrawingPatriarch();
		HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0, 100, 50, (short) 1, 1, (short) 10, 20);
		//插入图片
		patriarch.createPicture(anchor, wb.addPicture(handlePicture.toByteArray(), HSSFWorkbook.PICTURE_TYPE_JPEG));
		//写入excel文件
		try {
			fileOut = new FileOutputStream(pathOfExcel);
			wb.write(fileOut);
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			if (fileOut != null) {
				try {
					fileOut.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
	}
	private static ByteArrayOutputStream handlePicture(String pathOfPicture) {
		ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
		try {
			bufferImg = ImageIO.read(new File(pathOfPicture));
			ImageIO.write(bufferImg, "jpeg", byteArrayOut);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return byteArrayOut;
	}
	
	
	
	
	 private static CategoryDataset createDataset(String bt,String et,String zq) {  //这个数据集有点多，但都不难理解

		 DefaultCategoryDataset dataset = new DefaultCategoryDataset();
//	        String   sql=    " SELECT  TO_CHAR(add_months(to_date('"+bt+"', 'yyyy-mm-dd'), ROWNUM - 1),"
//	        		   +    "        'YYYY-MM'  "
//	        		   +    "       ) as npsj    "
//	        		   +    "  FROM "+CBSystemConstants.opcardUser+"t_a_czpzb " 
//	        		   +    " where ROWNUM <= (       "
//	        		   +    "                   select    months_between(to_date('"+et+"', 'yyyy-mm-dd'),"
//	        		   +    "                   to_date('"+bt+"', 'yyyy-mm-dd'))     from dual              ) ";
//	        		  System.out.println(sql+"======sql语句====");
//	        		        List czp=DBManager.query(sql);
//	        		        System.out.println(czp.size()+"======获得数据的数量===============");
//	        		        for(int i=0;i<czp.size();++i)
//	        		        {   Map temp0=(Map)czp.get(i);
//	        		           // Map temp1=(Map)czp.get(i+1);
//	        		        int value=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_char(npsj,'yyyy-MM') = '"+temp0.get("npsj")+"' ");
//	        		        //    int    value=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where     npsj between     to_date('"+temp0.get("npsj")+"', 'yyyy-mm')  and to_date('"+temp1.get("npsj")+"', 'yyyy-mm') ");	
//	        		         String key=temp0.get("npsj").toString();
//	        		         
//	        		         System.out.println(key+"=======月列表的时间==========");
//	        		         System.out.println(value+"=======操作票的数量==========");
//	        		        dataset.addValue(value,"操作票数量统计折线图" , key) ;
//	        		      
//	        		         
//	        		       }
		 
         System.out.println(zq+"===========周期的数值为=======================");
         if(zq.equals("日"))
         {
      	String   sqlday="select  to_char((to_date( ' "+bt+" ' ,  ' yyyy-mm-dd ' )  +  rownum  -   1),'yyyy-mm-dd') as npsj   from  "+CBSystemConstants.opcardUser+"t_a_czpzb  where  rownum  <=   (to_date( ' "+et+" ' ,  ' yyyy-mm-dd ' ) - to_date( ' "+bt+" ' ,  ' yyyy-mm-dd ' )  + 1 )";
      	   List czpday=DBManager.query(sqlday);
		        System.out.println(sqlday+"======查询日期的sql语句===============");
		        for(int i=0;i<czpday.size();++i)
		        {   Map temp0=(Map)czpday.get(i);
		           // Map temp1=(Map)czp.get(i+1);
		         int value0=DBManager.queryForInt(" select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_date('"+temp0.get("npsj")+"','yyyy-mm-dd') like npsj and cardstates = '0'");
		         int value1=DBManager.queryForInt(" select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_date('"+temp0.get("npsj")+"','yyyy-mm-dd') like npsj and cardstates = '1'");
		         int value2=DBManager.queryForInt(" select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_date('"+temp0.get("npsj")+"','yyyy-mm-dd') like npsj and cardstates = '2'");
		       // int value=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_char(npsj,'yyyy-mm-dd') = '"+temp0.get("npsj")+"' ");
		        //    int    value=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where     npsj between     to_date('"+temp0.get("npsj")+"', 'yyyy-mm')  and to_date('"+temp1.get("npsj")+"', 'yyyy-mm') ");	
		         String key=temp0.get("npsj").toString();
		         
		         System.out.println(key+"=======日列表的时间==========");
		         System.out.println(value0+"=======操作票的数量==========");
		         dataset.addValue(value0,"未下令操作票折线图" ,key) ;
		         dataset.addValue(value1, "已下令操作票折线图",key);
		         dataset.addValue(value2, "已执行操作票折线图",key);
      	      
              }
         }
         if(zq.equals("年"))
         {
      	String   sqlyear="SELECT distinct  TO_CHAR(add_months(to_date('"+bt.substring(0, 4)+"', 'yyyy'), ROWNUM -1),'YYYY' ) as npsj  FROM "+CBSystemConstants.opcardUser+"t_a_czpzb  where ROWNUM <= (select    months_between(to_date('"+et.substring(0, 4)+"', 'yyyy'),  to_date('"+bt.substring(0, 4)+"', 'yyyy'))  from dual )order by npsj";
      	System.out.println(sqlyear+"======查询年的sql语句====");
	        List czpyear=DBManager.query(sqlyear);
	        System.out.println(czpyear.size()+"======获得数据的数量===============");
	        for(int i=0;i<czpyear.size();++i)
	        {   Map temp0=(Map)czpyear.get(i);
	           // Map temp1=(Map)czp.get(i+1);
	        int value0=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_char(npsj,'yyyy') = '"+temp0.get("npsj")+"' and cardstates='0' ");
	        int value1=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_char(npsj,'yyyy') = '"+temp0.get("npsj")+"' and cardstates='1'");
	        int value2=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_char(npsj,'yyyy') = '"+temp0.get("npsj")+"' and cardstates='2'");
	        //    int    value=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where     npsj between     to_date('"+temp0.get("npsj")+"', 'yyyy-mm')  and to_date('"+temp1.get("npsj")+"', 'yyyy-mm') ");	
	         String key=temp0.get("npsj").toString();
	         
	         System.out.println(key+"=======年列表的时间==========");
	         System.out.println(value0+"=======操作票的数量==========");
	         dataset.addValue(value0,"未下令操作票折线图" ,key) ;
	         dataset.addValue(value1, "已下令操作票折线图",key);
	         dataset.addValue(value2, "已执行操作票折线图",key);
	        }
      	
         }
         if(zq.equals("月"))
         {
          String    sqlmonth=    " SELECT  TO_CHAR(add_months(to_date('"+bt.substring(0, 7)+"', 'yyyy-mm'), ROWNUM - 1),"
      		   +    "        'YYYY-MM'  "
      		   +    "       ) as npsj    "
      		   +    "  FROM "+CBSystemConstants.opcardUser+"t_a_czpzb " 
      		   +    " where ROWNUM <= (       "
      		   +    "                   select    months_between(to_date('"+et.substring(0, 7)+"', 'yyyy-mm'),"
      		   +    "                   to_date('"+bt.substring(0, 7)+"', 'yyyy-mm'))     from dual              ) ";
           System.out.println(sqlmonth+"======sql语句====");
	 	        List czpmonth=DBManager.query(sqlmonth);
		        System.out.println(czpmonth.size()+"======获得数据的数量===============");
		        for(int i=0;i<czpmonth.size();++i)
		        {   Map temp0=(Map)czpmonth.get(i);
		           // Map temp1=(Map)czp.get(i+1);
		        int value0=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_char(npsj,'yyyy-MM') = '"+temp0.get("npsj")+"' and cardstates='0'");
		        int value1=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_char(npsj,'yyyy-MM') = '"+temp0.get("npsj")+"' and cardstates='1'");
		        int value2=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where    to_char(npsj,'yyyy-MM') = '"+temp0.get("npsj")+"' and cardstates='2'");
		        //    int    value=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb    where     npsj between     to_date('"+temp0.get("npsj")+"', 'yyyy-mm')  and to_date('"+temp1.get("npsj")+"', 'yyyy-mm') ");	
		         String key=temp0.get("npsj").toString();
		         
		         System.out.println(key+"=======月列表的时间==========");
		         System.out.println(value0+"=======操作票的数量==========");
		         dataset.addValue(value0,"未下令操作票折线图" ,key) ;
		         dataset.addValue(value1, "已下令操作票折线图",key);
		         dataset.addValue(value2, "已执行操作票折线图",key);
		        }
           }
		  return dataset;

	    }
	  public ChartPanel getChartPanel(){
	    	return frame1;
	    	
	    }
}

