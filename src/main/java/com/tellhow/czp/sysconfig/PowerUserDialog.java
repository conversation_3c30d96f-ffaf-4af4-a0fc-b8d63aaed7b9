package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.CardLayout;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.JTree;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.TreeSelectionEvent;
import javax.swing.event.TreeSelectionListener;
import javax.swing.table.DefaultTableModel;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreeSelectionModel;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class PowerUserDialog extends JDialog  {
  private JTree tree;
  private Map temp;
  private JScrollPane treeview;
  private OrganTree orgt;//存储子节点的对象
  private OrganTree orgf;//存储父节点的对象
  private DefaultTableModel dtm;
  private JTable table;
  private JPanel panel;//右边主面板
  private JPanel panel_1;//机构人员展示面板
  private JPanel panel_2;//人员详细信息面板
  private CardLayout card;
  private JButton psmodbutton;//密码修改按钮
  private Map ctemp3;//部门列表
  private Map ctemp2;//单位列表
  private JTextField textField;//姓名文本框
  private JComboBox comboBox_1;//所属部门选择框
  private JComboBox comboBox_3;//岗位选择框
  private JComboBox comboBox;//所属单位选择框
  private JComboBox comboBox_2;//人员状态选择框
  private JPasswordField  passwordField;//密码文本框
  private JPasswordField  passwordField_1;//密码确认文本框
  private JLabel lblNewLabel_7;//密码确认文本栏 
  private boolean newsave;//判断是否为新增后的保存
  private String userid;//存储用户ID
	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			PowerUserDialog dialog = new PowerUserDialog();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
//	public void add(Component c,GridBagConstraints constraints,int x,int y,int w,int h)
//	{//此方法用来添加控件到panel5中，panel5为gridbaglayout布局
//		constraints.gridx=x;
//		constraints.gridy=y;
//		constraints.gridwidth=w;
//		constraints.gridheight=h;
//		getContentPane().add(c,constraints);
//	}
	/**
	 * Create the dialog.
	 */
	public PowerUserDialog() {
		setTitle("用户管理");
		setBounds(100, 100, 550, 400);
		getContentPane().setLayout(new BorderLayout(0, 0));
		 treeview=new JScrollPane();
			treeview.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
			treeview.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
			
			getContentPane().add(treeview, BorderLayout.WEST);
			
			
		//构建左边的树
			   tree = new JTree();
			   tree.setModel(new DefaultTreeModel(
				new DefaultMutableTreeNode("电网") {
					{
						DefaultMutableTreeNode node_1;
						List l1=DBManager.query("select t.organname , t.organid from "+CBSystemConstants.opcardUser+"t_a_organ t where t.parentid=1  ");
					    OrganTree	orgt1 = new OrganTree(StringUtils.ObjToString(((Map) l1.get(0)).get("organid")),StringUtils.ObjToString(((Map) l1.get(0)).get("organname")));
						node_1 = new DefaultMutableTreeNode(orgt1);
						add(node_1);
						List results = DBManager.query("select t.organid,t.organname from "+CBSystemConstants.opcardUser+"t_a_organ t where t.parentid="+((Map) l1.get(0)).get("organid") +"");
						 temp = new HashMap();  
						System.out.println(results);
						for(int i = 0; i < results.size(); i++){
							temp = (Map) results.get(i);
							 orgt = new OrganTree(StringUtils.ObjToString(temp.get("organid")),StringUtils.ObjToString(temp.get("organname")));
							DefaultMutableTreeNode node = new  DefaultMutableTreeNode( orgt) ;
							node_1.add(node);
							//System.out.println(node);		
						}			
						DefaultMutableTreeNode node_2;
						//List l2=DBManager.query("select t.organname,  t.organid from "+CBSystemConstants.opcardUser+"t_a_organ t where t.parentid=2  ");
						OrganTree	orgt2 = new OrganTree(StringUtils.ObjToString(((Map) l1.get(1)).get("organid")),StringUtils.ObjToString(((Map) l1.get(1)).get("organname")));
						node_2 = new DefaultMutableTreeNode(orgt2);
						add(node_2);
						List results2 = DBManager.query("select t.organid,t.organname from "+CBSystemConstants.opcardUser+"t_a_organ t where t.parentid="+((Map) l1.get(1)).get("organid") +"");
						 temp = new HashMap();  
						System.out.println(results2);
						for(int i = 0; i < results2.size(); i++){
							temp = (Map) results2.get(i);
							 orgt = new OrganTree(StringUtils.ObjToString(temp.get("organid")),StringUtils.ObjToString(temp.get("organname")));
							DefaultMutableTreeNode node = new  DefaultMutableTreeNode( orgt) ;
							node_2.add(node);
							System.out.println(node);		
						}			
					}
				}
			));
			//只有一个节点被选中
			tree.addTreeSelectionListener(new treevalueChanged());
			tree.getSelectionModel().setSelectionMode(TreeSelectionModel.SINGLE_TREE_SELECTION);  
				
			treeview.setViewportView(tree);
			
			panel = new JPanel();
			getContentPane().add(panel, BorderLayout.CENTER);
			card=new CardLayout();
			//panel.setLayout(new CardLayout(0, 0));
			panel.setLayout(card);
			
			 panel_1 = new JPanel();
			panel.add(panel_1, "organman");
			panel_1.setLayout(new BorderLayout(0, 0));
			
			JPanel panel_3 = new JPanel();
			panel_1.add(panel_3, BorderLayout.NORTH);
			
			JButton addbutton = new JButton("新增");
			panel_3.add(addbutton);
			addbutton.addActionListener(new ActionListener() {
				public void actionPerformed(ActionEvent e) {
					addbuttonActionPerformed(e);
				}
			});
			
			JButton modifybutton = new JButton("编辑");
			panel_3.add(modifybutton);
			modifybutton.addActionListener(new ActionListener(){
			    public void actionPerformed(ActionEvent e){
			    	modifybuttonactionPerformed(e);
			}
	       });
			JButton delbutton = new JButton("删除");
			panel_3.add(delbutton);
			delbutton.addActionListener(new ActionListener(){
				 public void actionPerformed(ActionEvent e){
				    	delbuttonactionPerformed(e);
				}
			});
			
			JScrollPane scrollPane = new JScrollPane();
			panel_1.add(scrollPane, BorderLayout.CENTER);
			
			table = new JTable();
			scrollPane.setViewportView(table);
			
			 panel_2 = new JPanel();
			panel.add(panel_2, "user");
			panel_2.setLayout(new BorderLayout(0, 0));
			
			JPanel panel_4 = new JPanel();
			panel_2.add(panel_4, BorderLayout.NORTH);
			
			JButton backbutton = new JButton("返回");
			panel_4.add(backbutton);
			backbutton.addActionListener(new ActionListener(){
				 public void actionPerformed(ActionEvent e){
				    	backbuttonactionPerformed(e);
				}
			});
			
			JButton savebutton = new JButton("保存");
			panel_4.add(savebutton);
			savebutton.addActionListener(new ActionListener(){
				 public void actionPerformed(ActionEvent e){
				    	savebuttonactionPerformed(e);
				}
			});
			
			JPanel panel_5 = new JPanel();
			panel_2.add(panel_5, BorderLayout.CENTER);
			GridBagLayout gbl_panel_5 = new GridBagLayout();
			gbl_panel_5.columnWidths = new int[]{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
			gbl_panel_5.rowHeights = new int[]{0, 0, 0, 0, 0, 0, 0, 0};
			gbl_panel_5.columnWeights = new double[]{0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, Double.MIN_VALUE};
			gbl_panel_5.rowWeights = new double[]{0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Double.MIN_VALUE};
			panel_5.setLayout(gbl_panel_5);
			
			JLabel lblNewLabel = new JLabel("顺序");
			GridBagConstraints gbc_lblNewLabel = new GridBagConstraints();
			gbc_lblNewLabel.anchor = GridBagConstraints.EAST;
			gbc_lblNewLabel.insets = new Insets(0, 0, 5, 5);
			gbc_lblNewLabel.gridx = 0;
			gbc_lblNewLabel.gridy = 0;
			panel_5.add(lblNewLabel, gbc_lblNewLabel);
			
			JLabel lblNewLabel_1 = new JLabel("姓名");
			GridBagConstraints gbc_lblNewLabel_1 = new GridBagConstraints();
			gbc_lblNewLabel_1.fill = GridBagConstraints.BOTH;
			gbc_lblNewLabel_1.anchor = GridBagConstraints.WEST;
			gbc_lblNewLabel_1.insets = new Insets(0, 0, 5, 5);
			gbc_lblNewLabel_1.gridx = 1;
			gbc_lblNewLabel_1.gridy = 2;
			panel_5.add(lblNewLabel_1, gbc_lblNewLabel_1);
			
			 textField = new JTextField();
			GridBagConstraints gbc_textField = new GridBagConstraints();
			gbc_textField.insets = new Insets(0, 0, 5, 5);
			gbc_textField.fill = GridBagConstraints.BOTH;
			gbc_textField.gridx = 2;
			gbc_textField.gridy = 2;
			panel_5.add(textField, gbc_textField);
			textField.setColumns(10);
			
			JLabel lblNewLabel_2 = new JLabel("岗位");
			GridBagConstraints gbc_lblNewLabel_2 = new GridBagConstraints();
			gbc_lblNewLabel_2.fill = GridBagConstraints.BOTH;
			gbc_lblNewLabel_2.anchor = GridBagConstraints.WEST;
			gbc_lblNewLabel_2.insets = new Insets(0, 0, 5, 5);
			gbc_lblNewLabel_2.gridx = 3;
			gbc_lblNewLabel_2.gridy = 2;
			panel_5.add(lblNewLabel_2, gbc_lblNewLabel_2);
			
			 comboBox_3 = new JComboBox();
			//comboBox_3.setModel(new DefaultComboBoxModel(new String[] {"调度员", "副职调度长", "正职调度长", "普通用户", "管理员"}));
			DefaultComboBoxModel dcbm3=new DefaultComboBoxModel();
			CodeNameModel cnm4=new CodeNameModel("0","管理员");
			CodeNameModel cnm5=new CodeNameModel("1","调度员");
			CodeNameModel cnm6=new CodeNameModel("2","副职调度长");
			CodeNameModel cnm7=new CodeNameModel("3","正职调度长");
			CodeNameModel cnm8=new CodeNameModel("4","普遍用户");
			dcbm3.addElement(cnm4);
			dcbm3.addElement(cnm5);
			dcbm3.addElement(cnm6);
			dcbm3.addElement(cnm7);
			dcbm3.addElement(cnm8);
			comboBox_3.setModel(dcbm3);
			GridBagConstraints gbc_comboBox_3 = new GridBagConstraints();
			gbc_comboBox_3.insets = new Insets(0, 0, 5, 5);
			gbc_comboBox_3.fill = GridBagConstraints.BOTH;
			gbc_comboBox_3.gridx = 4;
			gbc_comboBox_3.gridy = 2;
			panel_5.add(comboBox_3, gbc_comboBox_3);
			
			JLabel lblNewLabel_3 = new JLabel("所属单位");
			GridBagConstraints gbc_lblNewLabel_3 = new GridBagConstraints();
			gbc_lblNewLabel_3.fill = GridBagConstraints.BOTH;
			gbc_lblNewLabel_3.anchor = GridBagConstraints.WEST;
			gbc_lblNewLabel_3.insets = new Insets(0, 0, 5, 5);
			gbc_lblNewLabel_3.gridx = 1;
			gbc_lblNewLabel_3.gridy = 3;
			panel_5.add(lblNewLabel_3, gbc_lblNewLabel_3);
			
			 comboBox = new JComboBox(comboBox());
			GridBagConstraints gbc_comboBox = new GridBagConstraints();
			gbc_comboBox.insets = new Insets(0, 0, 5, 5);
			gbc_comboBox.fill = GridBagConstraints.BOTH;
			gbc_comboBox.gridx = 2;
			gbc_comboBox.gridy = 3;
			panel_5.add(comboBox, gbc_comboBox);
			comboBox.addItemListener(new ItemListener(){
					@Override
					public void itemStateChanged(ItemEvent e) {
						// TODO Auto-generated method stub
						 jlianactionPerformed(e);
					}	
			});
			
			JLabel lblNewLabel_4 = new JLabel("所属部门");
			GridBagConstraints gbc_lblNewLabel_4 = new GridBagConstraints();
			gbc_lblNewLabel_4.fill = GridBagConstraints.BOTH;
			gbc_lblNewLabel_4.anchor = GridBagConstraints.WEST;
			gbc_lblNewLabel_4.insets = new Insets(0, 0, 5, 5);
			gbc_lblNewLabel_4.gridx = 3;
			gbc_lblNewLabel_4.gridy = 3;
			panel_5.add(lblNewLabel_4, gbc_lblNewLabel_4);
			
//			DefaultComboBoxModel mode =new DefaultComboBoxModel(v);
			comboBox_1 = new JComboBox();
			GridBagConstraints gbc_comboBox_1 = new GridBagConstraints();
			gbc_comboBox_1.insets = new Insets(0, 0, 5, 5);
			gbc_comboBox_1.fill = GridBagConstraints.BOTH;
			gbc_comboBox_1.gridx = 4;
			gbc_comboBox_1.gridy = 3;
			panel_5.add(comboBox_1, gbc_comboBox_1);
			
			
			JLabel lblNewLabel_5 = new JLabel("人员状态");
			GridBagConstraints gbc_lblNewLabel_5 = new GridBagConstraints();
			gbc_lblNewLabel_5.fill = GridBagConstraints.BOTH;
			gbc_lblNewLabel_5.anchor = GridBagConstraints.WEST;
			gbc_lblNewLabel_5.insets = new Insets(0, 0, 5, 5);
			gbc_lblNewLabel_5.gridx = 1;
			gbc_lblNewLabel_5.gridy = 4;
			panel_5.add(lblNewLabel_5, gbc_lblNewLabel_5);
			
			 comboBox_2 = new JComboBox();
		    //comboBox_2.setModel(new DefaultComboBoxModel(new String[] {"在岗", "离岗"}));
			DefaultComboBoxModel dcbm2=new DefaultComboBoxModel();
			CodeNameModel cnm2=new CodeNameModel("0","在岗");
			CodeNameModel cnm3=new CodeNameModel("1","离岗");
			dcbm2.addElement(cnm2);
			dcbm2.addElement(cnm3);
			comboBox_2.setModel(dcbm2);
			GridBagConstraints gbc_comboBox_2 = new GridBagConstraints();
			gbc_comboBox_2.insets = new Insets(0, 0, 5, 5);
			gbc_comboBox_2.fill = GridBagConstraints.BOTH;
			gbc_comboBox_2.gridx = 2;
			gbc_comboBox_2.gridy = 4;
			panel_5.add(comboBox_2, gbc_comboBox_2);
			
			JLabel lblNewLabel_6 = new JLabel("密码");
			GridBagConstraints gbc_lblNewLabel_6 = new GridBagConstraints();
			gbc_lblNewLabel_6.fill = GridBagConstraints.BOTH;
			gbc_lblNewLabel_6.anchor = GridBagConstraints.WEST;
			gbc_lblNewLabel_6.insets = new Insets(0, 0, 5, 5);
			gbc_lblNewLabel_6.gridx = 1;
			gbc_lblNewLabel_6.gridy = 5;
			panel_5.add(lblNewLabel_6, gbc_lblNewLabel_6);
			
			 passwordField = new JPasswordField();
			GridBagConstraints gbc_passwordField = new GridBagConstraints();
			gbc_passwordField.insets = new Insets(0, 0, 5, 5);
			gbc_passwordField.fill = GridBagConstraints.BOTH;
			gbc_passwordField.gridx = 2;
			gbc_passwordField.gridy = 5;
			panel_5.add(passwordField, gbc_passwordField);
			
//			 psmodbutton = new JButton("密码修改");      //密码修改按钮
//			GridBagConstraints gbc_btnNewButton = new GridBagConstraints();
//			gbc_btnNewButton.fill = GridBagConstraints.BOTH;
//			gbc_btnNewButton.insets = new Insets(0, 0, 5, 5);
//			gbc_btnNewButton.gridx = 3;
//			gbc_btnNewButton.gridy = 5;
//			panel_5.add(psmodbutton, gbc_btnNewButton);
//			psmodbutton.setVisible(false);
			
			lblNewLabel_7 = new JLabel("密码确认");
			GridBagConstraints gbc_lblNewLabel_7 = new GridBagConstraints();
			gbc_lblNewLabel_7.fill = GridBagConstraints.BOTH;
			gbc_lblNewLabel_7.anchor = GridBagConstraints.WEST;
			gbc_lblNewLabel_7.insets = new Insets(0, 0, 0, 5);
			gbc_lblNewLabel_7.gridx = 1;
			gbc_lblNewLabel_7.gridy = 6;
			panel_5.add(lblNewLabel_7, gbc_lblNewLabel_7);
			
			 passwordField_1 = new JPasswordField();
			GridBagConstraints gbc_passwordField_1 = new GridBagConstraints();
			gbc_passwordField_1.insets = new Insets(0, 0, 0, 5);
			gbc_passwordField_1.fill = GridBagConstraints.BOTH;
			gbc_passwordField_1.gridx = 2;
			gbc_passwordField_1.gridy = 6;
			panel_5.add(passwordField_1, gbc_passwordField_1);

		}

	protected void jlianactionPerformed(ItemEvent e) {
		// TODO Auto-generated method stub
		
		comboBox_1.removeAllItems();
		
		OrganTree unit=  (OrganTree) comboBox.getSelectedItem();
	   String	danwei=unit.getorganid();//获取机构选择值
	    if(e.getStateChange()==ItemEvent.SELECTED)
	    {
		comboBox_1.setModel(comboBox1(danwei));
	    }
		System.out.println(danwei+"=======级联下拉框单位=======================");
	}
	protected void savebuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		
		System.out.println(newsave+"=======ture是新增false是编辑=======================");
		if(newsave)
	{		
		String name=  textField.getText().trim();//姓名
		if(name.equals(""))
		{
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "输入的姓名不允许为空");
		}
		CodeNameModel duty=  (CodeNameModel) comboBox_3.getSelectedItem();
		String gangwei=duty.getCode();//岗位
		
		OrganTree dept=  (OrganTree) comboBox_1.getSelectedItem();
		String bumen=dept.getorganid();//部门
		
		
		OrganTree unit=  (OrganTree) comboBox.getSelectedItem();
		String danwei=unit.getorganid();//单位
		
		CodeNameModel status=  (CodeNameModel) comboBox_2.getSelectedItem();
		String renyuanzt=status.getCode();//人员状态
		String  psd=new String(passwordField.getPassword());//密码
		String psdqr=new String(passwordField_1.getPassword());//密码确认
		if(psdqr.equals("")||psd.equals(""))
		{
			 JOptionPane.showMessageDialog(null, "输入的密码不允许为空");
		}
		else if(!psdqr.equals(psd)){
			   JOptionPane.showMessageDialog(null, "两次密码输入不相同，请再次输入","警告",JOptionPane.WARNING_MESSAGE);
			   passwordField.setText("");
			   passwordField_1.setText("");
			  }
		 else if(!name.equals("")&&!psdqr.equals("")&&!psd.equals("")&&psdqr.equals(psd))
		 {
			int usernum= DBManager.queryForInt("select count(0) from "+CBSystemConstants.opcardUser+"t_a_poweruserinfo");
			 String sql = "insert into "+CBSystemConstants.opcardUser+"t_a_poweruserinfo (userid,username,userpassword,userduty,unitcode,ordernum,unitid,organid,userstatus) values('"
				      + java.util.UUID.randomUUID().toString() + "','" + name + "','" + tbp.common.util.StringUtil.getMD5(psd) + "','"+gangwei+"','"+CBSystemConstants.unitCode.toString()+"','"+usernum+"','"+danwei+"','"+bumen+"','"+renyuanzt+"')";
				    DBManager.execute(sql);
				    JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "新增用户成功");
				    card.show(panel, "organman");
		 }
	}else if(!newsave)
	{
		String name=  textField.getText().trim();//姓名
		if(name.equals(""))
		{
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "输入的姓名不允许为空");
		}
		CodeNameModel duty=  (CodeNameModel) comboBox_3.getSelectedItem();
		String gangwei=duty.getCode();//岗位
		System.out.println(duty.getCode()+"-------------岗位的selectitem----------------------------");
		
		OrganTree dept=  (OrganTree) comboBox_1.getSelectedItem();
		String bumen=dept.getorganid();//部门
		
		
		OrganTree unit=  (OrganTree) comboBox.getSelectedItem();
		String danwei=unit.getorganid();//单位
		
		CodeNameModel status=  (CodeNameModel) comboBox_2.getSelectedItem();
		String renyuanzt=status.getCode();//人员状态
		String  psd=new String(passwordField.getPassword());//密码
		String psdqr=new String(passwordField_1.getPassword());//密码确认
		System.out.println(name+"=======这是姓名=======");	
	    System.out.println(gangwei+"=======这是岗位=======");	
	    System.out.println(bumen+"=======这是部门=======");	
	    System.out.println(danwei+"=======这是单位=======");	
	    System.out.println(renyuanzt+"=======这是人员状态=======");	
	    System.out.println(psd+"=======这是密码=======");	
	    System.out.println(psdqr+"=======这是密码确认=======");	
		if(psdqr.equals("")||psd.equals(""))
		{
			 JOptionPane.showMessageDialog(null, "输入的密码不允许为空");
		}
		 if(!psdqr.equals(psd)){
			   JOptionPane.showMessageDialog(null, "两次密码输入不相同，请再次输入","警告",JOptionPane.WARNING_MESSAGE);
			   passwordField.setText("");
			   passwordField_1.setText("");
			  }
		 else if(!name.equals("")&&!psdqr.equals("")&&!psd.equals("")&&psdqr.equals(psd))
		 {
			
			 String sql = "update "+CBSystemConstants.opcardUser+"t_a_poweruserinfo set username='"+name+"',userduty='"+gangwei
			      + "',organid = '" + bumen + "',unitid = '" + danwei+"',userstatus='"+renyuanzt+"',userpassword='"+psd
			      + "' where userid = '" + userid+"'";
				    DBManager.execute(sql);
				    JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "保存成功");
				    card.show(panel, "organman");
		 }
	}
		 
		 
		 
		 
		 
		 
		 
    
    
    
		
	}
	protected void backbuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		card.show(panel, "organman");
	}
	protected void delbuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		
		int si=table.getSelectedRow();// 首先得到这是哪一行
		if(si==-1)
		{
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "请选中一行数据进行删除");
		}
		else{
		String userid=((String)table.getValueAt(si, 0));
		((DefaultTableModel) table.getModel()).removeRow(si);
          System.out.println(userid+"=========sisisiisisisisisi=======");
		 String sql="update "+CBSystemConstants.opcardUser+"t_a_poweruserinfo set isdel= 1 where userid='"+userid+"'";
		 DBManager.execute(sql);
		 JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "删除成功");
		}
		
		
	}

	protected void modifybuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		
		Integer mod=table.getSelectedRow();// 首先得到这是哪一行
		System.out.println(mod+"============选中行序号=========");
		
		if(mod==null||mod==-1)
		{
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "请选中一行数据");
		}
		else{
			newsave=false;
			String statusnum = null;//定义人员状态的标志位
			String dutynum=null;//定义岗位的标志位
			userid=((String)table.getValueAt(mod, 0));
			List userinfo=DBManager.query("select userduty,userstatus,userpassword from "+CBSystemConstants.opcardUser+"t_a_poweruserinfo where userid='"+userid+"'");
           
	 		String  password=(String)((Map) userinfo.get(0)).get("userpassword");
			String name = (String) table.getValueAt(mod, 2);
			String duty = (String) table.getValueAt(mod, 3);
			
			if(duty.equals("管理员"))
			{
				dutynum="0";
			}else if(duty.equals("调度员"))
			{
				dutynum="1";
			}else if(duty.equals("副职调度长"))
			{
				dutynum="2";
			}else if(duty.equals("正职调度长"))
			{
				dutynum="3";
			}else if(duty.equals("普遍用户"))
			{
				dutynum="4";
				}
			 System.out.println(duty+"======用户信息岗位表======");
			 System.out.println(dutynum+"======用户信息岗位表id======");
			CodeNameModel cnmduty=new CodeNameModel(dutynum,duty);
			
			String status = (String) table.getValueAt(mod, 4);
			if(status.equals("在岗"))
			{
				 statusnum="0";
			}else if(status.equals("离岗"))
			{
				statusnum="1";
			}
			CodeNameModel cnmstatus=new CodeNameModel(statusnum,status);
			
			System.out.println(status+"======人员状态==========");
			System.out.print(duty+"--------岗位----------------");
			System.out.println(password+"======人员密码==========");
			card.show(panel, "user");
			
			textField.setText(name);
			comboBox_2.getModel().setSelectedItem(cnmstatus);
			comboBox_3.getModel().setSelectedItem(cnmduty);
			comboBox_1.getModel().setSelectedItem(orgt);
		    comboBox.getModel().setSelectedItem(orgf);
		    comboBox_1.setEnabled(true);
		    comboBox.setEnabled(true);
			passwordField.setText(password);
			passwordField_1.setText(password);
			
		}
		
		
		
	}

	protected void addbuttonActionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		
		card.show(panel, "user");
		textField.setText("");
		passwordField.setText("");
	    passwordField_1.setText("");
	    comboBox_1.getModel().setSelectedItem(orgt);
	    comboBox_1.setEnabled(false);
	    comboBox.getModel().setSelectedItem(orgf);
	    comboBox.setEnabled(false); 
	    comboBox_1.repaint();
		newsave=true;
			
		
	}
    class treevalueChanged implements TreeSelectionListener{
    	
         public treevalueChanged() {
        	
			// TODO Auto-generated constructor stub
		}
		@Override
		public void valueChanged(TreeSelectionEvent e) {
			// TODO Auto-generated method stub
			JTree tree = (JTree) e.getSource();
			 DefaultMutableTreeNode node = (DefaultMutableTreeNode)tree.getLastSelectedPathComponent();  
			 
			 if(node.isLeaf()){
			 orgt=(OrganTree)node.getUserObject();
			 DefaultMutableTreeNode  nodef=(DefaultMutableTreeNode)node.getParent();
			 orgf=(OrganTree)nodef.getUserObject();
				String organid=orgt.getorganid();
		 		//System.out.println(organid+"======this is =======nodename");
		 		List organman = DBManager.query("select t.userid, t.username,decode(t.userduty,0,'管理员',1,'调度员',2,'副职调度长',3,'正职调度长',4,'普遍用户') as userduty,decode(t.userstatus,0,'在岗',1,'离岗' ) as userstatus  from "+CBSystemConstants.opcardUser+"t_a_poweruserinfo t where t.isdel =0 and t.organid='"+organid+"'" );
		 		 
		 		 String[] tableHeads=new String[]{"用户ID","序号", "姓名", "岗位", "状态"};
				
		 		System.out.println(organman+"=======list  organman======");
		 		  dtm=new DefaultTableModel(){
		 			 public boolean isCellEditable(int rowIndex, int columnIndex) {
						 if(columnIndex==0||columnIndex==1||columnIndex==2||columnIndex==3) return false;
						 return true;
					 
					 } 
		 		 };
		 		table.setModel(dtm);
		 		
		 		dtm.setColumnIdentifiers(tableHeads);
		 		table.getTableHeader().getColumnModel().getColumn(0).setMaxWidth(0);
		        table.getTableHeader().getColumnModel().getColumn(0).setMinWidth(0);
		        table.getTableHeader().getColumnModel().getColumn(0).setPreferredWidth(0);
		 		for(int i = 0; i < organman.size(); i++){
		 			temp = (Map) organman.get(i);
					 Vector v = new Vector();
		 		 /******转化成Vector***********/
			      v.add(temp.get("userid"));
		 		  v.add(i+1);
		 		  v.add(temp.get("username"));
		 		  v.add(temp.get("userduty"));
		 		  v.add(temp.get("userstatus")); 
		 		 dtm.addRow(v);
		 		 
		 		}	 
		    }
		}
     }
	
	

	public DefaultComboBoxModel comboBox1(String s){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		
		//CodeNameModel cnm0=null;
		List results = DBManager.query("select t.organid, t.organname from "+CBSystemConstants.opcardUser+"t_a_organ t where t.parentid='"+s+"' ");
		//List results = DBManager.query("select t.organid, t.organname from "+CBSystemConstants.opcardUser+"t_a_organ t where t.organtype='dept' ");
		ctemp3=new HashMap();
		
		for(int i=0;i<results.size();i++)
		{
		ctemp3=(Map) results.get(i);
		String nowselectcode=StringUtils.ObjToString(ctemp3.get("organid"));
		
		String nowselectname =StringUtils.ObjToString(ctemp3.get("organname"));
		//cnm0=new CodeNameModel(nowselectcode,nowselectname);
	    OrganTree  orgdept =new OrganTree(nowselectcode,nowselectname);
		//model.addElement(cnm0);
	    //System.out.println(orgdept+"==============级联下拉框部门的所有制====");
	    model.addElement(orgdept);
		comboBox_1.addItem(orgdept);
		}
		return model;
	
	}
	public DefaultComboBoxModel comboBox(){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		//CodeNameModel cnm1=null;
		
		List results = DBManager.query("select t.organid, t.organname from "+CBSystemConstants.opcardUser+"t_a_organ t where t.organtype='unit' ");
		ctemp2=new HashMap();
		for(int i=0;i<results.size();i++)
		{
		ctemp2=(Map) results.get(i);
		String nowselectcode=StringUtils.ObjToString(ctemp2.get("organid"));
		String nowselectname =StringUtils.ObjToString(ctemp2.get("organname"));
		OrganTree organunit= new OrganTree(nowselectcode,nowselectname);
		//cnm1=new CodeNameModel(nowselectcode,nowselectname);
		//model.addElement(cnm1);
		model.addElement(organunit);
		}
		return model;
	}
	
}


