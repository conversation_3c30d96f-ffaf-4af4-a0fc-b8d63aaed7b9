package com.tellhow.czp.sysconfig;

import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JRadioButton;
import javax.swing.JTextField;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

public class EquipManagerDialogG extends JDialog {

	private EquipOperationManageDialog eomd=null;
	private final JPanel contentPanel = new JPanel();
	private Map temp;
	private JTextField textField;
	private JButton btnNewButton, btnNewButton_1;
	private String statecode,code,cardtype;
	private JLabel lblNewLabel;
	private JRadioButton rdbtnNewRadioButton, rdbtnNewRadioButton_1, rdbtnNewRadioButton_2;
	
	
	public EquipManagerDialogG(EquipOperationManageDialog eomd,final JFrame jFrame, boolean modal,String code,String cardtype){
		super(jFrame, modal);
		this.code=code;
		this.eomd=eomd;
		this.cardtype=cardtype;
		getContentPane().setLayout(null);
		initRdbtnNewRadioButton();
		initLblNewLabel();
		initTextField("");
		initBtnNewButton();
		this.setLocationCenter();
	}
	
	public EquipManagerDialogG(EquipOperationManageDialog eomd, final JFrame jFrame, boolean modal, String code,String statecode,String cardtype) {
		super(jFrame, modal);
		this.code=code;
		this.statecode = statecode;
		this.cardtype=cardtype;
		this.eomd = eomd;
		getContentPane().setLayout(null);
		initRdbtnNewRadioButton();
		initLblNewLabel();
		initTextField(initContext());
		initBtnNewButton();
		this.setLocationCenter();
	}

	private void initRdbtnNewRadioButton() {
		rdbtnNewRadioButton = new JRadioButton("操作");
		rdbtnNewRadioButton.setBounds(106, 27, 121, 23);
		rdbtnNewRadioButton.setEnabled(false);
		getContentPane().add(rdbtnNewRadioButton);
		
		rdbtnNewRadioButton_1 = new JRadioButton("分类",true);
		rdbtnNewRadioButton_1.setBounds(287, 27, 121, 23);
		rdbtnNewRadioButton_1.setEnabled(false);
		getContentPane().add(rdbtnNewRadioButton_1);
		
		rdbtnNewRadioButton_2 = new JRadioButton("分割线");
		rdbtnNewRadioButton_2.setBounds(484, 27, 121, 23);
		rdbtnNewRadioButton_2.setEnabled(false);
		getContentPane().add(rdbtnNewRadioButton_2);
	}
	
	private void initBtnNewButton() {
		btnNewButton = new JButton("确定");
		btnNewButton.setBounds(175, 264, 93, 23);
		getContentPane().add(btnNewButton);
		
		btnNewButton_1 = new JButton("取消");
		btnNewButton_1.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent arg0) {
				dispose();
			}
		});
		btnNewButton_1.setBounds(424, 264, 93, 23);
		getContentPane().add(btnNewButton_1);
	}
	
	private void initLblNewLabel() {
		lblNewLabel = new JLabel("名称");
		lblNewLabel.setBounds(175, 110, 76, 35);
		getContentPane().add(lblNewLabel);		
	}
	
	private String initContext() {
		List results = DBManager.query("select statecode, statename  from  "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and statecode='"+statecode+"'");
		temp=new HashMap();
		temp=(Map) results.get(0);
		return StringUtils.ObjToString(temp.get("statename"));
	}
	
	private void initTextField(String context) {
		//context=initContext();
		textField = new JTextField(context);
		textField.setBounds(362, 110, 128, 35);
		getContentPane().add(textField);
		textField.setColumns(10);
	}
	
	public JTextField getTextField() {
		return textField;
	}
	
	private void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
		this.setBounds( 400, 200,(w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
	}
	

	
	public void addBtnNewButtonListenernxz(){
		btnNewButton.addActionListener(new BtnNewButtonListenernxz(this, eomd, code,cardtype));
	}
	
	class BtnNewButtonListenernxz implements ActionListener{
		private EquipManagerDialogG emdg;
		private EquipOperationManageDialog eomd;
		private String code;
		private String cardtype;
		public BtnNewButtonListenernxz(EquipManagerDialogG emdg, EquipOperationManageDialog eomd,String code,String cardtype){
			this.emdg=emdg;
			this.eomd=eomd;
			this.code=code;
			this.cardtype=cardtype;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			List list=DBManager.query("select max(stateorder) stateorder from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and devicetypeid='"+code+"'");
			Map map=new HashMap();
			map=(Map)list.get(0);
			String stateorders=StringUtils.ObjToString(map.get("stateorder"));
			int stateorder=Integer.parseInt(stateorders==""?"0":stateorders)+1;
			String statecode=java.util.UUID.randomUUID().toString();
			String txt = emdg.getTextField().getText();
			String unitcode=CBSystemConstants.opCode;
			if(!txt.equals("")){
				DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_devicestateinfo (statecode,statename,statevalue,opcode,devicetypeid,parentcode,statetype,stateorder,cardbuildtype) values ('"+statecode+"','"+txt+"',-1,'"+unitcode+"','"+code+"',0,1,'"+stateorder+"','"+cardtype+"')");
				eomd.shuaxin();
				emdg.dispose();
			}else{
				ShowMessage.view("名称不能为空");
			}
		}
		
	}
	
	
	
	public void addBtnNewButtonListenerxg() {
		btnNewButton.addActionListener(new BtnNewButtonListenerxg(this, eomd, code,statecode,cardtype));
	}
	
	class BtnNewButtonListenerxg implements ActionListener {
		private EquipOperationManageDialog eomd;
		private EquipManagerDialogG emdg;
		private String code,statecode,cardtype;
		public BtnNewButtonListenerxg(EquipManagerDialogG emdg, EquipOperationManageDialog eomd,String code, String statecode,String cardtype) {
			this.emdg = emdg;
			this.eomd = eomd;
			this.code=code;
			this.statecode = statecode;
			this.cardtype=cardtype;
		}
		
		@Override
		public void actionPerformed(ActionEvent e) {
			String txt = emdg.getTextField().getText();
			if(!txt.equals("")){
				DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set statename='"+txt+"' where statecode='"+statecode+"' and devicetypeid='"+code+"' and cardbuildtype='"+cardtype+"'");
				eomd.shuaxin();
				emdg.dispose();
			}else{
				ShowMessage.view("名称不能为空");
			}
		}		
	}
	
//	public static void main(String[] args) {
//		java.awt.EventQueue.invokeLater(new Runnable() {
//			public void run() {
//				EquipOperationManageDialog dialog = new EquipOperationManageDialog(new javax.swing.JFrame(), true);
//				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
//					public void windowClosing(java.awt.event.WindowEvent e) {
//						System.exit(0);
//					}
//				});
//				dialog.setVisible(true);
//			}
//		});
//	}
	
}
