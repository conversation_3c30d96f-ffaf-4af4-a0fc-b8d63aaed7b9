package com.tellhow.czp.sysconfig;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTree;
import javax.swing.table.DefaultTableModel;
import javax.swing.tree.DefaultMutableTreeNode;

import org.apache.commons.collections.CollectionUtils;

import com.tellhow.czp.user.User;
import com.tellhow.czp.util.GUIUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class AuthManagerDialog extends JDialog {
	private DefaultMutableTreeNode root;
	private Map temp,tabletemp,tabletemp1,zdrtemp;
	private List results,zdrresults;
	private JTable table;
	private JTable table_1;
	private JTree tree;
	private JButton btnNewButton,btnNewButton_1,button;
	private static User user,zduser;
	private static List<Auth> guiauth,guiauth1;
	private JComboBox comboBox;
	private JCheckBox chckbxNewCheckBox;

	//指定人下拉框
	private DefaultComboBoxModel inintcombox(){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		String sql="SELECT a.USERID,a.USERNAME,a.USERPASSWORD,DECODE(a.USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTYNAME FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO a ,"+CBSystemConstants.opcardUser+"T_a_userrole b WHERE a.UNITCODE='"+CBSystemConstants.getUser().getUnitCode()+"' AND b.rolecode='"+CBSystemConstants.roleCode+"' and a.userid=b.userid and a.ISDEL=0 AND a.USERID NOT IN ('0','1') ORDER BY ORDERNUM";
		zdrresults=DBManager.queryForList(sql);
		zdrtemp=new HashMap();
		for(int i = 0; i < zdrresults.size(); i++){
			CodeNameModel cm=new CodeNameModel();
			zdrtemp = (Map) zdrresults.get(i);
			String userID=StringUtils.ObjToString(zdrtemp.get("userID"));
			String userName=StringUtils.ObjToString(zdrtemp.get("userName"));
			cm.setCode(userID);
			cm.setName(userName);
			model.addElement(cm);
		}
		return model;
	}
	//树
	private void inittree(){
		String sql="SELECT T.USERID,T.USERNAME,T.USERPASSWORD,DECODE(T.USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTY FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO T ,"+CBSystemConstants.opcardUser+"T_a_userrole T1 WHERE T1.ROLECODE='"+CBSystemConstants.roleCode+"' AND T.USERID=T1.USERID AND T.ISDEL=0 ORDER BY ORDERNUM";
		results=DBManager.queryForList(sql);
		
		temp=new HashMap();
		for(int i = 0; i < results.size(); i++){
			temp = (Map) results.get(i);
			String userID=StringUtils.ObjToString(temp.get("userID"));
			String userName=StringUtils.ObjToString(temp.get("userName"));
			String password=StringUtils.ObjToString(temp.get("password"));
			String userDuty=StringUtils.ObjToString(temp.get("userDuty"));
			String unitCode=StringUtils.ObjToString(temp.get("unitCode"));
			User user=new User(userID,userName,password,userDuty,unitCode);
			DefaultMutableTreeNode node = new  DefaultMutableTreeNode(user) ;
			root.add(node);
		}
	}
	
	//上表
	private void inittable(){
		DefaultTableModel dtm=new DefaultTableModel(){
			public Class<?> getColumnClass(int columnIndex) {
				return getValueAt(0, columnIndex).getClass();
				}
			};
		Object[] tableHeads=new String[]{"序号", "权限名称","选择"};
		dtm.setColumnIdentifiers(tableHeads);
		table.setModel(dtm);
		
		GUIUtil g=new GUIUtil();
		g.anaGUI();
		List<CodeNameModel> lcnm=g.getmenuitem();
		
		List results = DBManager.query("select userid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO where userid='"+AuthManagerDialog.zduser.getUserID()+"' and classname='MenuItem'");
		tabletemp=new HashMap();
		List<Auth> resultslist=new ArrayList<Auth>();
		List<String> resultsstring=new ArrayList<String>();
		for(int j=0;j<results.size();j++){
			tabletemp = (Map) results.get(j);
			String userid = StringUtils.ObjToString(tabletemp.get("userid"));
			String authid=StringUtils.ObjToString(tabletemp.get("authid"));
			String authname=StringUtils.ObjToString(tabletemp.get("authname"));
			String isckeck=StringUtils.ObjToString(tabletemp.get("ischeck"));
			int ischeck=Integer.parseInt(isckeck);
			Auth auth=new Auth(userid, authid, authname, ischeck);
			resultslist.add(auth);
			resultsstring.add(authid);
		}
		guiauth=resultslist;
		List <Auth> authlist=new ArrayList<Auth>();
		List <String> authliststring=new ArrayList<String>();
		for(int i=0;i<lcnm.size();i++){
			CodeNameModel cnm=lcnm.get(i);
			Auth auth=new Auth();
			auth.setUsername(zduser.getUserID());
			auth.setAuthid(cnm.getCode());
			auth.setAuthname(cnm.getName());
			auth.setIscheck(0);
			authlist.add(auth);
			authliststring.add(cnm.getCode());
		}
		
		Collection<String> disjunction =CollectionUtils.disjunction(authliststring,resultsstring);
		for(int i=0;i<authliststring.size();i++){
			
				Auth auth=authlist.get(i);
				String au=authliststring.get(i);
				if(disjunction.contains(au)){
					resultslist.add(auth);
				}
		}
		
		for(int i=0;i<resultslist.size();i++){
			Vector v = new Vector();
			v.add(i+1);
			v.add(resultslist.get(i));
			v.add(resultslist.get(i).getIscheck()==0?false:true);
			dtm.addRow(v);
		}
	}
	
	//下表
	private void inittable_1(){
		DefaultTableModel dtm=new DefaultTableModel(){
			public Class<?> getColumnClass(int columnIndex) {
				return getValueAt(0, columnIndex).getClass();
				}
			};
		Object[] tableHeads=new String[]{"序号", "权限名称","选择"};
		dtm.setColumnIdentifiers(tableHeads);
		table_1.setModel(dtm);
		
		GUIUtil g=new GUIUtil();
		g.anaGUI();
		List<CodeNameModel> lcnm=g.gettoolbar();
		
		List results = DBManager.query("select userid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO where userid='"+AuthManagerDialog.zduser.getUserID()+"' and classname='Button'");
		tabletemp1=new HashMap();
		List<Auth> resultslist=new ArrayList<Auth>();
		List<String> resultsstring=new ArrayList<String>();
		for(int j=0;j<results.size();j++){
			tabletemp1 = (Map) results.get(j);
			
			String userid = StringUtils.ObjToString(tabletemp1.get("userid"));
			String authid=StringUtils.ObjToString(tabletemp1.get("authid"));
			String authname=StringUtils.ObjToString(tabletemp1.get("authname"));
			String isckeck=StringUtils.ObjToString(tabletemp1.get("ischeck"));
			int ischeck=Integer.parseInt(isckeck);
			Auth auth=new Auth(userid, authid, authname, ischeck);
			resultslist.add(auth);
			resultsstring.add(authid);
		}
		guiauth1=resultslist;
		List <Auth> authlist=new ArrayList<Auth>();
		List <String> authliststring=new ArrayList<String>();
		for(int i=0;i<lcnm.size();i++){
			CodeNameModel cnm=lcnm.get(i);
			Auth auth=new Auth();
			auth.setUsername(zduser.getUserID());
			auth.setAuthid(cnm.getCode());
			auth.setAuthname(cnm.getName());
			auth.setIscheck(0);
			authlist.add(auth);
			authliststring.add(cnm.getCode());
		}
		
		Collection<String> disjunction =CollectionUtils.disjunction(authliststring,resultsstring);
		for(int i=0;i<authliststring.size();i++){
			
				Auth auth=authlist.get(i);
				String au=authliststring.get(i);
				if(disjunction.contains(au)){
					resultslist.add(auth);
				}
		}
		
		
		for(int i=0;i<resultslist.size();i++){
			Vector v = new Vector();
			v.add(i+1);
			v.add(resultslist.get(i));
			v.add(resultslist.get(i).getIscheck()==0?false:true);
			dtm.addRow(v);
		}
	}
	
	public static void main(String[] args) {
		try {
			AuthManagerDialog dialog = new AuthManagerDialog(new javax.swing.JFrame(), true);
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	//界面
	public AuthManagerDialog(java.awt.Frame parent, boolean modal) {
		super(parent,modal);
		this.setSize(714, 410);
		this.setResizable(false);
		this.setLocation(300, 150);
		getContentPane().setLayout(null);
		this.setTitle("人员权限配置");
		
		JScrollPane scrollPane = new JScrollPane();
		scrollPane.setBounds(0, 0, 118, 381);
		getContentPane().add(scrollPane);
		
		JPanel panel = new JPanel();
		panel.setBounds(117, 0, 581, 381);
		getContentPane().add(panel);
		panel.setLayout(null);
		
		btnNewButton = new JButton("确认提交");
		btnNewButton.addActionListener(new BtnNewButton(this));
		btnNewButton.setBounds(478, 12, 93, 27);
		btnNewButton.setEnabled(false);
		panel.add(btnNewButton);
		
		JScrollPane scrollPane_1 = new JScrollPane();
		scrollPane_1.setBounds(0, 45, 581, 131);
		panel.add(scrollPane_1);
		
		table = new JTable();
		scrollPane_1.setViewportView(table);
		
		JScrollPane scrollPane_2 = new JScrollPane();
		scrollPane_2.setBounds(0, 201, 581, 180);
		panel.add(scrollPane_2);
		
		table_1 = new JTable();
		scrollPane_2.setViewportView(table_1);
		
		JLabel lblNewLabel = new JLabel("菜单权限配置");
		lblNewLabel.setBounds(10, 16, 107, 23);
		panel.add(lblNewLabel);
		
		JLabel lblNewLabel_1 = new JLabel("工具栏权限配置");
		lblNewLabel_1.setBounds(10, 176, 107, 23);
		panel.add(lblNewLabel_1);
		
		btnNewButton_1 = new JButton("取消");
		btnNewButton_1.setBounds(408, 12, 60, 27);
		btnNewButton_1.setEnabled(false);
		btnNewButton_1.addActionListener(new BtnNewButton_1Listener(this));
		panel.add(btnNewButton_1);
		
		button = new JButton("全选");
		button.setBounds(341, 12, 57, 27);
		button.setEnabled(false);
		button.addActionListener(new ButtonListener(this));
		panel.add(button);
		
		chckbxNewCheckBox = new JCheckBox("指定权限");
		chckbxNewCheckBox.setBounds(128, 16, 112, 23);
		chckbxNewCheckBox.setEnabled(false);
		chckbxNewCheckBox.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent arg0) {
				// TODO Auto-generated method stub
				checkboxActionPerformed(arg0);
			}
		} );
		
		panel.add(chckbxNewCheckBox);
		comboBox = new JComboBox();
		comboBox.setBounds(246, 13, 85, 26);
		comboBox.setModel(inintcombox());
		comboBox.setEditable(false);
		comboBox.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent arg0) {
				// TODO Auto-generated method stub
				comboBoxActionPerformed(arg0);
			}
		});
		panel.add(comboBox);
		
		User user=new User("root","调度人员","root","root","root");
		root=new DefaultMutableTreeNode(user);
		inittree();
		tree = new JTree(root);
		tree.addMouseListener(new TreeListener(this));
		scrollPane.setViewportView(tree);
	}
	//指定人checkbox
	public void checkboxActionPerformed(ActionEvent arg0){
		
		CodeNameModel cnm=(CodeNameModel)comboBox.getSelectedItem();
		String code=cnm.getCode();
		String sql="SELECT USERID,USERNAME,USERPASSWORD,DECODE(USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTY FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO WHERE USERID='"+code+"'";
		List l=DBManager.queryForList(sql);
		Map rmap=(Map)l.get(0);
		String userID=StringUtils.ObjToString(rmap.get("userID"));
		String userName=StringUtils.ObjToString(rmap.get("userName"));
		String password=StringUtils.ObjToString(rmap.get("password"));
		String userDuty=StringUtils.ObjToString(rmap.get("userDuty"));
		String unitCode=StringUtils.ObjToString(rmap.get("unitCode"));
		User user=new User(userID,userName,password,userDuty,unitCode);
		if(chckbxNewCheckBox.isSelected()==true){
			this.zduser=user;
			inittable();
			inittable_1();
//			System.out.println("被选中的人");
		}else{
			DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
			User userd=new User();
			userd=(User)path.getUserObject();
			this.zduser=userd;
			inittable();
			inittable_1();
//			System.out.println("----------2b---------");
		}
	}
	//指定人下拉框事件
	public void comboBoxActionPerformed(ActionEvent arg0) {
		
		CodeNameModel cnm=(CodeNameModel)comboBox.getSelectedItem();
		String code=cnm.getCode();
		String sql="SELECT USERID,USERNAME,USERPASSWORD,DECODE(USERDUTY,'0','管理员','1','调度员', '2','副职调度长', '3','正职调度长','4','普遍用户') USERDUTY FROM "+CBSystemConstants.opcardUser+"T_a_POWERUSERINFO WHERE USERID='"+code+"'";
		List l=DBManager.queryForList(sql);
		Map rmap=(Map)l.get(0);
		String userID=StringUtils.ObjToString(rmap.get("userID"));
		String userName=StringUtils.ObjToString(rmap.get("userName"));
		String password=StringUtils.ObjToString(rmap.get("password"));
		String userDuty=StringUtils.ObjToString(rmap.get("userDuty"));
		String unitCode=StringUtils.ObjToString(rmap.get("unitCode"));
		User user=new User(userID,userName,password,userDuty,unitCode);
		if(chckbxNewCheckBox.isSelected()==true){
			this.zduser=user;
			inittable();
			inittable_1();
//			System.out.println("被选中的人");
		}else{
//			System.out.println("---------sb-----------");
		}
	}
	
	//全选按钮事件
	class ButtonListener implements ActionListener{
		private AuthManagerDialog amd;
		public ButtonListener(AuthManagerDialog amd){
			this.amd=amd;
		}
		
		@Override
		public void actionPerformed(ActionEvent e) {
			// TODO Auto-generated method stub
			DefaultTableModel dtm1=new DefaultTableModel(){
				public Class<?> getColumnClass(int columnIndex) {
					return getValueAt(0, columnIndex).getClass();
					}
				};
			Object[] tableHeads1=new String[]{"序号", "权限名称","选择"};
			dtm1.setColumnIdentifiers(tableHeads1);
			table_1.setModel(dtm1);
			
			GUIUtil g1=new GUIUtil();
			g1.anaGUI();
			List<CodeNameModel> lcnm1=g1.gettoolbar();
			List <Auth> authlist1=new ArrayList<Auth>();
			List <String> authliststring1=new ArrayList<String>();
			for(int i=0;i<lcnm1.size();i++){
				CodeNameModel cnm=lcnm1.get(i);
				Auth auth=new Auth();
				auth.setUsername(user.getUserID());
				auth.setAuthid(cnm.getCode());
				auth.setAuthname(cnm.getName());
				auth.setIscheck(1);
				authlist1.add(auth);
				authliststring1.add(cnm.getCode());
			}
			for(int i=0;i<authlist1.size();i++){
				Vector v = new Vector();
				v.add(i+1);
				v.add(authlist1.get(i));
				v.add(authlist1.get(i).getIscheck()==0?false:true);
				dtm1.addRow(v);
			}
			
			DefaultTableModel dtm=new DefaultTableModel(){
				public Class<?> getColumnClass(int columnIndex) {
					return getValueAt(0, columnIndex).getClass();
					}
				};
			Object[] tableHeads=new String[]{"序号", "权限名称","选择"};
			dtm.setColumnIdentifiers(tableHeads);
			table.setModel(dtm);
			
			GUIUtil g=new GUIUtil();
			g.anaGUI();
			List<CodeNameModel> lcnm=g.getmenuitem();
			List <Auth> authlist=new ArrayList<Auth>();
			List <String> authliststring=new ArrayList<String>();
			for(int i=0;i<lcnm.size();i++){
				CodeNameModel cnm=lcnm.get(i);
				Auth auth=new Auth();
				auth.setUsername(user.getUserID());
				auth.setAuthid(cnm.getCode());
				auth.setAuthname(cnm.getName());
				auth.setIscheck(1);
				authlist.add(auth);
				authliststring.add(cnm.getCode());
			}
			for(int i=0;i<authlist.size();i++){
				Vector v = new Vector();
				v.add(i+1);
				v.add(authlist.get(i));
				v.add(authlist.get(i).getIscheck()==0?false:true);
				dtm.addRow(v);
			}
		}
		
	}
	
	//全部取消按钮
	class BtnNewButton_1Listener implements ActionListener{
		private AuthManagerDialog amd;
		public BtnNewButton_1Listener(AuthManagerDialog amd){
			this.amd=amd;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			// TODO Auto-generated method stub
			DefaultTableModel dtm1=new DefaultTableModel(){
				public Class<?> getColumnClass(int columnIndex) {
					return getValueAt(0, columnIndex).getClass();
					}
				};
			Object[] tableHeads1=new String[]{"序号", "权限名称","选择"};
			dtm1.setColumnIdentifiers(tableHeads1);
			table_1.setModel(dtm1);
			
			GUIUtil g1=new GUIUtil();
			g1.anaGUI();
			List<CodeNameModel> lcnm1=g1.gettoolbar();
			List <Auth> authlist1=new ArrayList<Auth>();
			List <String> authliststring1=new ArrayList<String>();
			for(int i=0;i<lcnm1.size();i++){
				CodeNameModel cnm=lcnm1.get(i);
				Auth auth=new Auth();
				auth.setUsername(user.getUserID());
				auth.setAuthid(cnm.getCode());
				auth.setAuthname(cnm.getName());
				auth.setIscheck(0);
				authlist1.add(auth);
				authliststring1.add(cnm.getCode());
			}
			for(int i=0;i<authlist1.size();i++){
				Vector v = new Vector();
				v.add(i+1);
				v.add(authlist1.get(i));
				v.add(authlist1.get(i).getIscheck()==0?false:true);
				dtm1.addRow(v);
			}
			
			DefaultTableModel dtm=new DefaultTableModel(){
				public Class<?> getColumnClass(int columnIndex) {
					return getValueAt(0, columnIndex).getClass();
					}
				};
			Object[] tableHeads=new String[]{"序号", "权限名称","选择"};
			dtm.setColumnIdentifiers(tableHeads);
			table.setModel(dtm);
			
			GUIUtil g=new GUIUtil();
			g.anaGUI();
			List<CodeNameModel> lcnm=g.getmenuitem();
			List <Auth> authlist=new ArrayList<Auth>();
			List <String> authliststring=new ArrayList<String>();
			for(int i=0;i<lcnm.size();i++){
				CodeNameModel cnm=lcnm.get(i);
				Auth auth=new Auth();
				auth.setUsername(user.getUserID());
				auth.setAuthid(cnm.getCode());
				auth.setAuthname(cnm.getName());
				auth.setIscheck(0);
				authlist.add(auth);
				authliststring.add(cnm.getCode());
			}
			for(int i=0;i<authlist.size();i++){
				Vector v = new Vector();
				v.add(i+1);
				v.add(authlist.get(i));
				v.add(authlist.get(i).getIscheck()==0?false:true);
				dtm.addRow(v);
			}
		}
		
	}
	
	public static String backusercode(User user){
		//用户为空
		if(user==null){
			return null;
		}
		//是否有权限
		List results = DBManager.query("select userid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO where userid='"+user.getUserID()+"'");
		if(results.size()==0){//没有权限
			/*List searchid=DBManager.query("select userid from "+CBSystemConstants.opcardUser+"T_a_poweruserinfo where userduty!=0 and unitcode='"+CBSystemConstants.unitCode+"' and rownum=1");
			Map searchidmap=(Map) searchid.get(0);
			String searchuserid=StringUtils.ObjToString(searchidmap.get("userid"));*/
			List result = DBManager.query("select roleid,authid,authname,ischeck,classname from "+CBSystemConstants.opcardUser+"T_A_POWERROLEAUTHINFO where roleid='4'");
			Map updatetemp = new HashMap();
			for(int j=0;j<result.size();j++){
				updatetemp = (Map) result.get(j);
				String userid = StringUtils.ObjToString(updatetemp.get("userid"));
				String authid=StringUtils.ObjToString(updatetemp.get("authid"));
				String authname=StringUtils.ObjToString(updatetemp.get("authname"));
				String isckeck=StringUtils.ObjToString(updatetemp.get("ischeck"));
				String classs=StringUtils.ObjToString(updatetemp.get("classname"));
				int ischeck=Integer.parseInt(isckeck);
				DBManager.execute("insert into "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO (USERID,AUTHID,AUTHNAME,ISCHECK,CLASSNAME) values ('"+user.getUserID()+"','"+authid+"','"+authname+"','"+ischeck+"','"+classs+"')");
			}
		}
		return user.getUserID();
	}
	
	//提交按钮事件
	class BtnNewButton implements ActionListener{
		private AuthManagerDialog amd;
		private Map updatetemp,updatetempq;
		public BtnNewButton(AuthManagerDialog amd){
			this.amd=amd;
		}
		
		@Override
		public void actionPerformed(ActionEvent arg0) {
			int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"是否确认提交" ,CBSystemConstants.SYSTEM_TITLE,JOptionPane.YES_NO_OPTION);
			if (isok == JOptionPane.OK_OPTION) {
				int z=table.getModel().getRowCount();
				List results = DBManager.query("select userid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO where userid='"+user.getUserID()+"' and classname='MenuItem'");
				updatetemp = new HashMap();
				List<Auth> resultslist=new ArrayList<Auth>();
				List<String> resultsstring=new ArrayList<String>();
				for(int j=0;j<results.size();j++){
					updatetemp = (Map) results.get(j);
					String userid = StringUtils.ObjToString(updatetemp.get("userid"));
					String authid=StringUtils.ObjToString(updatetemp.get("authid"));
					String authname=StringUtils.ObjToString(updatetemp.get("authname"));
					String isckeck=StringUtils.ObjToString(updatetemp.get("ischeck"));
					int ischeck=Integer.parseInt(isckeck);
					Auth auth=new Auth(userid, authid, authname, ischeck);
					resultslist.add(auth);
					resultsstring.add(authid);
				}
				for (int zz=0;zz<z;zz++){
					boolean bll=(Boolean)table.getModel().getValueAt(zz, 2);
					Auth auth=(Auth)table.getModel().getValueAt(zz, 1);
					int boo=0;
					if(bll==true){
						boo=1;
					}
					if(resultsstring.contains(auth.getAuthid())){
						DBManager.execute("update "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO set ischeck='"+boo+"' where userid='"+AuthManagerDialog.user.getUserID()+"' and authid='"+auth.getAuthid()+"' and classname='MenuItem'");
					}else{
						DBManager.execute("insert into "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO (USERID,AUTHID,AUTHNAME,ISCHECK,CLASSNAME) values ('"+AuthManagerDialog.user.getUserID()+"','"+auth.getAuthid()+"','"+auth.getAuthname()+"','"+boo+"','MenuItem')");
					}
				}
			
				int zq=table_1.getModel().getRowCount();
				List resultsq = DBManager.query("select userid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO where userid='"+user.getUserID()+"' and classname='Button'");
				updatetempq = new HashMap();
				List<Auth> resultslistq=new ArrayList<Auth>();
				List<String> resultsstringq=new ArrayList<String>();
				for(int jq=0;jq<resultsq.size();jq++){
					updatetempq = (Map) resultsq.get(jq);
					String useridq = StringUtils.ObjToString(updatetempq.get("userid"));
					String authidq=StringUtils.ObjToString(updatetempq.get("authid"));
					String authnameq=StringUtils.ObjToString(updatetempq.get("authname"));
					String isckeckq=StringUtils.ObjToString(updatetempq.get("ischeck"));
					int ischeckq=Integer.parseInt(isckeckq);
					Auth authq=new Auth(useridq, authidq, authnameq, ischeckq);
					resultslistq.add(authq);
					resultsstringq.add(authidq);
				}
				for (int zzq=0;zzq<zq;zzq++){
					boolean bllq=(Boolean)table_1.getModel().getValueAt(zzq, 2);
					Auth authq=(Auth)table_1.getModel().getValueAt(zzq, 1);
					int booq=0;
					if(bllq==true){
						booq=1;
					}
					if(resultsstringq.contains(authq.getAuthid())){
						DBManager.execute("update "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO set ischeck='"+booq+"' where userid='"+AuthManagerDialog.user.getUserID()+"' and authid='"+authq.getAuthid()+"' and classname='Button'");
					}else{
						DBManager.execute("insert into "+CBSystemConstants.opcardUser+"T_A_POWERUSERAUTHINFO (USERID,AUTHID,AUTHNAME,ISCHECK,CLASSNAME) values ('"+AuthManagerDialog.user.getUserID()+"','"+authq.getAuthid()+"','"+authq.getAuthname()+"','"+booq+"','Button')");
					}
				}
			}else{
				
				return;
			}
		}
	}
	
	//树点击事件
	class TreeListener extends MouseAdapter {
		private AuthManagerDialog amd;
		public TreeListener(AuthManagerDialog amd) {
			this.amd = amd;
		}
		@Override
		public void mouseClicked(MouseEvent e) {
			if(e.getButton()==e.BUTTON1){
				amd.btnNewButton.setEnabled(true);
				amd.btnNewButton_1.setEnabled(true);
				amd.button.setEnabled(true);
				amd.comboBox.setEnabled(true);
				amd.chckbxNewCheckBox.setEnabled(true);
				if(tree.isSelectionEmpty()!=true){
					DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
					User user=new User();
					user=(User)path.getUserObject();
					if(user.getUserID().equals("root")){
						AuthManagerDialog.user=null;
						amd.btnNewButton.setEnabled(false);
						amd.btnNewButton_1.setEnabled(false);
						amd.button.setEnabled(false);
						amd.comboBox.setEnabled(false);
						amd.chckbxNewCheckBox.setEnabled(false);
					}else{
						AuthManagerDialog.user=user;
						AuthManagerDialog.zduser=user;
						amd.inittable();
						amd.inittable_1();
					}
				}
				amd.chckbxNewCheckBox.setSelected(false);
			}
		}
	}
}
