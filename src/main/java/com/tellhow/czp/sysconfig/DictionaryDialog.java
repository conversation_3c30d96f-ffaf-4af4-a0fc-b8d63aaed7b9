package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.GraphicsEnvironment;
import java.awt.GridLayout;
import java.awt.Point;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.JTree;
import javax.swing.ListSelectionModel;
import javax.swing.table.DefaultTableModel;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;

import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class DictionaryDialog extends JDialog {
	public static  JPopupMenu popupMenuP;//父弹出式菜单
	public static  JPopupMenu popupMenuS;//子弹出式菜单
	public static  JMenuItem addItem;//新增
	public static JMenuItem deleteItem;//删除
	public static JMenuItem upItem;//修改
	public static JMenuItem upMoveiItem;//上移
	public static JMenuItem downMoveiItem;//下移
	private boolean DEBUG = true;//初始值设为true
	private static	DefaultTreeModel model2=null;
	public static JTree tree = new JTree();
	//private static DefaultMutableTreeNode rootStr=null;
	//public static Object [][]data=new Object[5][4];
	//public static Vector<String> data=new Vector<String>();
	public static WindowUtils utils=new WindowUtils();//window工具类
	public static StringUtils uuid=new StringUtils();
	public static final DictionaryDialog dialog = new DictionaryDialog();
	private JScrollPane scrollPane;
	private  JTable table=null;
	
	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setSize(500, 300);
			dialog.setResizable(false);//固定窗口的大小不变
			Point p = GraphicsEnvironment.getLocalGraphicsEnvironment().getCenterPoint();    
			dialog.setBounds(p.x - 500 / 2, p.y - 400 / 2, 500, 300); //将对话框设置为居中位置 
			dialog.setVisible(true);
			
			dialog.pack();
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * Create the dialog.
	 */
	public DictionaryDialog() {
		setIconImage(Toolkit.getDefaultToolkit().getImage(DictionaryDialog.class.getResource("/resources/xmlgui/icons/manager.png")));
		setBounds(100, 100, 450, 300);
		getContentPane().setLayout(new BorderLayout(0, 0));
		
		scrollPane = new JScrollPane();
		getContentPane().add(scrollPane, BorderLayout.WEST);
		/*DefaultMutableTreeNode codeType=new DefaultMutableTreeNode("代码类型");
		DefaultMutableTreeNode connectionMode=new DefaultMutableTreeNode("接线方式");
		DefaultMutableTreeNode switchcraft=new DefaultMutableTreeNode("开关");
		DefaultMutableTreeNode disconnectinglink=new DefaultMutableTreeNode("刀闸");
		codeType.add(connectionMode);
		codeType.add(switchcraft);
		codeType.add(disconnectinglink);
		DefaultTreeModel dm=new DefaultTreeModel(codeType);
		tree.setModel(dm);*/
		
		init();//初始化tree
		scrollPane.setViewportView(tree);
		JPanel panel = new JPanel();
		getContentPane().add(panel, BorderLayout.CENTER);
		panel.setLayout(new BorderLayout(0, 0));
		
		JPanel panel_1 = new JPanel();
		panel.add(panel_1, BorderLayout.NORTH);
		//设置表格属性
		//修改按钮
		final JButton ediNewButton = new JButton("");
		ediNewButton.setIcon(new ImageIcon(DictionaryDialog.class.getResource("/tellhow/btnIcon/edit.gif")));
		//删除按钮
		final JButton deleteNewButton = new JButton("");
		deleteNewButton.setIcon(new ImageIcon(DictionaryDialog.class.getResource("/resources/xmlgui/icons/delete.png")));
		//新增按钮
		JButton addNewButton = new JButton("");
		addNewButton.setIcon(new ImageIcon(DictionaryDialog.class.getResource("/resources/builder/icons/add.png")));
		//按钮的初始状态
		addNewButton.setEnabled(true);//新增
		deleteNewButton.setEnabled(false);//删除
		ediNewButton.setEnabled(false);//修改
		//新增按钮
		addNewButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				//获取当前节点
				TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
				if(parentPath==null){return;}
			    DefaultMutableTreeNode currentNode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
			   	final CodeNameModel currentcodeNamemodel=(CodeNameModel) currentNode.getUserObject();
			   	final String currentcode=currentcodeNamemodel.getCode();//当前节点
				//代码类型维护界面
				final JDialog codeTypemaintenance=new JDialog();
				codeTypemaintenance.setSize(270, 200);
				codeTypemaintenance.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
				codeTypemaintenance.setTitle("新增界面");
				codeTypemaintenance.setResizable(false);//固定对话框的大小
				codeTypemaintenance.setLocationRelativeTo(dialog);
				codeTypemaintenance.setModal(true);//对话框这是为模式状态(父不操作)	
				//Container contentPane=codeTypemaintenance.getContentPane();
				JPanel contentpanel=new JPanel();
				codeTypemaintenance.setContentPane(contentpanel);
				//标签 文本框 按钮
				JLabel typeCode=new JLabel("代码编码:");
				JLabel typeName=new JLabel("代码描述:");
				final	JTextField typeCodeField=new JTextField(10);
				final	JTextField typeNameField=new JTextField(10);
				JButton save=new JButton("保存");
				JButton cancel=new JButton("取消");
				//布局
				JPanel westPanel=new JPanel();//南
				westPanel.setLayout(new GridLayout(1, 1,20,0));
				JPanel centerPanel=new JPanel();//中
				centerPanel.setLayout(new GridLayout(3,2,20,0));
				//面板布局位置
				codeTypemaintenance.getContentPane().setLayout(new FlowLayout());
				contentpanel.add(centerPanel);
				//按钮显示位置
				centerPanel.add(typeName);
				centerPanel.add(typeNameField);
				centerPanel.add(typeCode);
				centerPanel.add(typeCodeField);
				centerPanel.add(save);
				centerPanel.add(cancel);
				
				save.addMouseListener(new MouseAdapter() {
					@Override
					public void mouseReleased(MouseEvent e) {
						// TODO Auto-generated method stub
						codeTypemaintenance.dispose();
					}
					@Override
					public void mousePressed(MouseEvent e) {
						// TODO Auto-generated method stub
						String textNameField=typeNameField.getText();
						String textCodeField=typeCodeField.getText();
						if(textNameField.equalsIgnoreCase("")){
							return;
						}
						if(textCodeField.equalsIgnoreCase("")){
							return;
						}
						try {
							Map typecodeM=(Map) DBManager.query("select typecode from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb where typeid='"+currentcode+"'").get(0);
							String typecode=(String)typecodeM.get("TYPECODE");
							DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_dictionary(code,name,codetype,parentid,codeid) values('"+textCodeField+"','"+textNameField+"','"+typecode+"','"+currentcode+"','"+uuid.getUUID()+"')");
							//DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_dictionaryzb set typecode='"+textCodeField+"',typename='"+textNameField+"' where typeid='"+currentcode+"'");
							//inittable();
							//清除table中的内容  先清除再查询数据
							DefaultTableModel model =(DefaultTableModel) table.getModel(); 
							while(model.getRowCount()>0){     
								model.removeRow(model.getRowCount()-1);
							}
							Integer seq=1;//序号初始为1
							try {
								TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
								if(parentPath==null){
									return;
								}
							    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
							   	CodeNameModel codeNamemodel=(CodeNameModel) currentnode.getUserObject();
							   	String id=codeNamemodel.getCode();
								String sql="select t2.code,t2.name,t2.codetype from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb t1,"+CBSystemConstants.opcardUser+"t_a_dictionary t2 where t1.typeid=t2.parentid and t2.parentid='"+id+"'";
								List list=DBManager.queryForList(sql);
								for(int index=0;index<list.size();index++){
										Map map=(Map)list.get(index);
										String code = (String) map.get("CODE");
										String name = (String) map.get("Name");
										String[] str_row = {seq.toString(),code, name};
									    model.addRow(str_row);// 添加在表模板中
									    seq++;
								}
								
							} catch (Exception e2) {
								// TODO: handle exception
								e2.printStackTrace();
							}
							deleteNewButton.setEnabled(true);//删除
							ediNewButton.setEnabled(true);//修改
							//init(); 不能再次初始化 那么新增按钮会被重置 默认值为null
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
						codeTypemaintenance.dispose();
					}
				});
				cancel.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e) {
						if(codeTypemaintenance!=null){
							codeTypemaintenance.dispose();
						}
					}
				});
				codeTypemaintenance.setVisible(true);
			}
		});
		panel_1.add(addNewButton);
		//删除按钮
		deleteNewButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				Object[] options = {"确定","取消"};
				int response=JOptionPane.showOptionDialog(null, "删除后就不能恢复，你确定要删除吗？", "删除", JOptionPane.YES_OPTION, JOptionPane.QUESTION_MESSAGE, null, options, options[0]);
				if(response==0) { 
					TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
				    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
				   	final CodeNameModel codeNamemodel=(CodeNameModel) currentnode.getUserObject();
					int index=table.getSelectedRow();//获得选中行索引号
					if(index==-1){
						return;
					}
					//btnNewButton_1.setEnabled(true);
					int column=table.getColumnCount();//获得table中的列数
					//Vector<Vector<Object>> vector=new Vector<Vector<Object>>();//创建一个用于保存行的集合
					Vector<Object> object=new Vector<Object>();//创建第二集合，用于保存到第一个集合中
					for(int n=0;n<column;n++){
						object.add(table.getValueAt(index, n));
					}
					//PreparedStatement pstmt = null;
					try {
						String sql="delete from "+CBSystemConstants.opcardUser+"T_A_DICTIONARY where CODE='"+(String) object.get(1)+"' and PARENTID='"+codeNamemodel.getCode()+"'";
						DBManager.execute(sql);
						/*pstmt=DBManager.getConnection().prepareStatement(sql);
						pstmt.setString(1, (String) object.get(1));
						pstmt.setString(2, codeNamemodel.getCode());*/
						//pstmt.setString(3, "36017000000");
						//pstmt.setString(4, object.get(3).toString().equals("开关")?"Breaker":( object.get(3).toString().equals("接线方式")?"runmodel":"Disconnector"));
						//pstmt.setString(3, codeNamemodel.getCode());
						//pstmt.executeUpdate();
						utils.removeTableRow(table);//删除表中选中行
						//System.out.println("删除成功...");
					} catch (Exception e2) {
						// TODO: handle exception
						e2.printStackTrace();
					}
				}else if(response==1){
					return;
				}
			}
		});
		panel_1.add(deleteNewButton);
		//修改按钮
		ediNewButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				//获取当前节点
				TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
			    DefaultMutableTreeNode currentNode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
			   	final CodeNameModel currentcodeNamemodel=(CodeNameModel) currentNode.getUserObject();
			   	final String currentcode=currentcodeNamemodel.getCode();//当前节点
			   	//获取选项的参数值
				int index=table.getSelectedRow();//获得选中行索引号
				if(index==-1){
					return;
				}
				int column=table.getColumnCount();//获得table中的列数
				//Vector<Vector<Object>> vector=new Vector<Vector<Object>>();//创建一个用于保存行的集合
				final Vector<Object> object=new Vector<Object>();//创建一个用于保存行的集合
				for(int n=0;n<column;n++){
					object.add(table.getValueAt(index, n));
				}
				//代码类型维护界面
				final JDialog codeTypemaintenance=new JDialog();
				codeTypemaintenance.setSize(270, 200);
				codeTypemaintenance.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
				codeTypemaintenance.setTitle("修改界面");
				codeTypemaintenance.setResizable(false);//固定对话框的大小
				codeTypemaintenance.setLocationRelativeTo(dialog);
				codeTypemaintenance.setModal(true);//对话框这是为模式状态(父不操作)	
				//Container contentPane=codeTypemaintenance.getContentPane();
				JPanel contentpanel=new JPanel();
				codeTypemaintenance.setContentPane(contentpanel);
				//标签 文本框 按钮
				JLabel typeCode=new JLabel("代码编码:");
				JLabel typeName=new JLabel("代码描述:");
				final	JTextField typeCodeField=new JTextField(10);
				final	JTextField typeNameField=new JTextField(10);
				JButton save=new JButton("保存");
				JButton cancel=new JButton("取消");
				//布局
				JPanel westPanel=new JPanel();//南
				westPanel.setLayout(new GridLayout(1, 1,20,0));
				JPanel centerPanel=new JPanel();//中
				centerPanel.setLayout(new GridLayout(3,2,20,0));
				//面板布局位置
				codeTypemaintenance.getContentPane().setLayout(new FlowLayout());
				contentpanel.add(centerPanel);
				//按钮显示位置
				centerPanel.add(typeName);
				centerPanel.add(typeNameField);
				centerPanel.add(typeCode);
				centerPanel.add(typeCodeField);
				centerPanel.add(save);
				centerPanel.add(cancel);
				//文本框设置默认值
				typeCodeField.setText((String) object.get(1));
				typeNameField.setText((String) object.get(2));
				String textNameField=typeNameField.getText();
				String textCodeField=typeCodeField.getText();
				//获得codeid
				Map codeidMap=(Map) DBManager.queryForList("select codeid from "+CBSystemConstants.opcardUser+"t_a_dictionary where code='"+textCodeField+"' and name='"+textNameField+"'").get(0);
				final String codeid=(String)codeidMap.get("CODEID");
				save.addMouseListener(new MouseAdapter() {
					@Override
					public void mouseReleased(MouseEvent e) {
						// TODO Auto-generated method stub
						codeTypemaintenance.dispose();
					}
					@Override
					public void mousePressed(MouseEvent e) {
						// TODO Auto-generated method stub
						String textNameField=typeNameField.getText();
						String textCodeField=typeCodeField.getText();
						if(textNameField.equalsIgnoreCase("")){
							return;
						}
						if(textCodeField.equalsIgnoreCase("")){
							return;
						}
						try {
							DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_dictionary set code='"+textCodeField+"',name='"+textNameField+"' where codeid='"+codeid+"'");
							//清除table中的内容  先清除再查询数据
							DefaultTableModel model =(DefaultTableModel) table.getModel(); 
							while(model.getRowCount()>0){     
								model.removeRow(model.getRowCount()-1);
							}
							Integer seq=1;//序号初始为1
							try {
								TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
								if(parentPath==null){
									return;
								}
							    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
							   	CodeNameModel codeNamemodel=(CodeNameModel) currentnode.getUserObject();
							   	String id=codeNamemodel.getCode();
								String sql="select t2.code,t2.name,t2.codetype from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb t1,"+CBSystemConstants.opcardUser+"t_a_dictionary t2 where t1.typeid=t2.parentid and t2.parentid='"+id+"'";
								List list=DBManager.queryForList(sql);
								for(int index=0;index<list.size();index++){
										Map map=(Map)list.get(index);
										String code = (String) map.get("CODE");
										String name = (String) map.get("Name");
										String[] str_row = {seq.toString(),code, name};
									    model.addRow(str_row);// 添加在表模板中
									    seq++;
								}
							} catch (Exception e2) {
								// TODO: handle exception
								e2.printStackTrace();
							}
							//init(); 不能再次初始化 那么新增按钮会被重置 默认值为null
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
						codeTypemaintenance.dispose();
					}
				});
				cancel.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e) {
						if(codeTypemaintenance!=null){
							codeTypemaintenance.dispose();
						}
					}
				});
				codeTypemaintenance.setVisible(true);
			
				/*
				TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
			    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
			   	final CodeNameModel codeNamemodel=(CodeNameModel) currentnode.getUserObject();
				int index=table.getSelectedRow();//获得选中行索引号
				if(index==-1){
					return;
				}
				int column=table.getColumnCount();//获得table中的列数
				Vector<Vector<Object>> vector=new Vector<Vector<Object>>();//创建一个用于保存行的集合
				Vector<Object> object=new Vector<Object>();//创建第二集合，用于保存到第一个集合中
				Vector<Object> object=new Vector<Object>();//创建一个用于保存行的集合
				for(int n=0;n<column;n++){
					object.add(table.getValueAt(index, n));
				}
				PreparedStatement pstmt = null;
				try {
					//String sql="insert into "+CBSystemConstants.opcardUser+"T_A_DICTIONARY(CODE,NAME,UNITCODE,CODETYPE,PARENTID)values('"+(String) object.get(1+"','"+(String) object.get(2)+"','"+CBSystemConstants.unitCode+"','"++"','"++"')";
					pstmt=DBManager.getConnection().prepareStatement(sql);
					pstmt.setString(1, (String) object.get(1));
					pstmt.setString(2, (String) object.get(2));
					pstmt.setString(3, CBSystemConstants.unitCode);
					pstmt.setString(4, "test");
					pstmt.setString(5, codeNamemodel.getCode());
					//pstmt.setString(4, object.get(3).toString().equals("开关")?"Breaker":( object.get(3).toString().equals("接线方式")?"runmodel":"Disconnector"));
					pstmt.executeUpdate();
					//System.out.println("保存成功");
				} catch (Exception e2) {
					// TODO: handle exception
					e2.printStackTrace();
				}
			*/}
		});
		panel_1.add(ediNewButton);
		final JScrollPane scrollPane_1 = new JScrollPane();
		panel.add(scrollPane_1, BorderLayout.CENTER);
		String []columnNames ={"序号", "编码", "描述"};
		final DefaultTableModel model = new DefaultTableModel(columnNames, 0); // 定义一个表的模板
		//表格不允许被编辑,只能选中;重写isCellEditable方法
		table=new JTable(){
			public boolean isCellEditable(int row, int column)
              {return false;}
        };
		//设置表头行高         
		table.getTableHeader().setPreferredSize(new Dimension(0, 20));         
		//设置表内容行高         
		table.setRowHeight(20);         
		//设置单选模式         
		table.getSelectionModel().setSelectionMode(ListSelectionModel.SINGLE_SELECTION);         
		//设置单元格不可拖动         
		table.getTableHeader().setReorderingAllowed(false);         
		//设置不可改变列宽         
		table.getTableHeader().setResizingAllowed(false);
		//设置表格内容为不可编辑状态
		//table.setEnabled(false);
		table.setSelectionBackground(new Color(123,180,120));
		table.setModel(model);
		scrollPane_1.setViewportView(table);
/*		if(DEBUG){
		table.addMouseListener(new MouseAdapter(){
			     public void mouseClicked(MouseEvent e){
			      //printDebugData(table);
			    	 System.out.println("表格选中测试成功");
			     }
			    });
		}*/
		//tree鼠标事件
		tree.addMouseListener(new MouseAdapter() {
			@Override
			public void mouseReleased(MouseEvent e) {
				// TODO Auto-generated method stub
				JTree treeCurrentJTree=(JTree)e.getSource();
				int location=treeCurrentJTree.getRowForLocation(e.getX(), e.getY());//找出当前所在位置
				//System.out.println("location........................:"+location);
				//int jt=e.getButton();
				popupMenuP=new JPopupMenu();
				popupMenuS=new JPopupMenu();
				addItem=new JMenuItem("新增");
				deleteItem =new JMenuItem("删除");
				upItem=new JMenuItem("修改");
				upMoveiItem=new JMenuItem("上移");
				downMoveiItem=new JMenuItem("下移");
				popupMenuP.add(addItem);
				popupMenuS.add(deleteItem);
				popupMenuS.add(upItem);
				popupMenuS.addSeparator();
				popupMenuS.add(upMoveiItem);
				popupMenuS.add(downMoveiItem);
				popupMenuS.setLocation(e.getLocationOnScreen());
				if(location==0){
					if(e.isPopupTrigger()){//右键单击弹出父菜单
						 popupMenuP.show(e.getComponent(),e.getX(),e.getY());
						 popupMenuP.show(tree, e.getX(), e.getY());
					}
				}else{
					if(location==-1){
						return;
					}
					if(e.isPopupTrigger()){//右键单击弹出子菜单
						 popupMenuS.show(e.getComponent(),e.getX(),e.getY());
						 popupMenuS.show(tree, e.getX(), e.getY());
					}
				}
				//popupMenu.show(tree, e.getX(), e.getY());
				
				/*if(jt==MouseEvent.BUTTON3 && location==0){//右键单击事件
					popupMenu.show(tree, e.getX(), e.getY());
				}else if(jt==MouseEvent.BUTTON3 && location==1){
					popupMenu=new JPopupMenu();
					popupMenu.add(upItem);
					popupMenu.addSeparator();
					popupMenu.add(upMoveiItem);
					popupMenu.add(downMoveiItem);
					popupMenu.setLocation(e.getLocationOnScreen());
					popupMenu.show(tree, e.getX(), e.getY());
				}else if(jt==MouseEvent.BUTTON3 && location==2){
					popupMenu=new JPopupMenu();
					popupMenu.add(upItem);
					popupMenu.addSeparator();
					popupMenu.add(upMoveiItem);
					popupMenu.add(downMoveiItem);
					popupMenu.setLocation(e.getLocationOnScreen());
					popupMenu.show(tree, e.getX(), e.getY());
				}else if(jt==MouseEvent.BUTTON3 && location==3){
					popupMenu=new JPopupMenu();
					popupMenu.add(upItem);
					popupMenu.addSeparator();
					popupMenu.add(upMoveiItem);
					popupMenu.add(downMoveiItem);
					popupMenu.setLocation(e.getLocationOnScreen());
					popupMenu.show(tree, e.getX(), e.getY());
				}*/
				//新增
				addItem.addMouseListener(new MouseAdapter() {
					@Override
					public void mousePressed(MouseEvent e) {
						// TODO Auto-generated method stub
						final DefaultMutableTreeNode newChild =new DefaultMutableTreeNode("新节点");
						TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
					    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
					   	final CodeNameModel codeNamemodel=(CodeNameModel) currentnode.getUserObject();
					   	//System.out.println("codeNamemodel.getCode:"+codeNamemodel.getCode());
						model2=(DefaultTreeModel)tree.getModel();//model=(DefaultTreeModel) tree.getModel();
						model2.insertNodeInto(newChild, currentnode, currentnode.getChildCount());
						//代码类型维护界面
						final JDialog codeTypemaintenance=new JDialog();
						codeTypemaintenance.setSize(270, 200);
						codeTypemaintenance.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
						codeTypemaintenance.setTitle("代码维护界面");
						codeTypemaintenance.setResizable(false);//固定对话框的大小
						codeTypemaintenance.setLocationRelativeTo(dialog);
						codeTypemaintenance.setModal(true);//对话框这是为模式状态(父不操作)	
						//Container contentPane=codeTypemaintenance.getContentPane();
						JPanel contentpanel=new JPanel();
						codeTypemaintenance.setContentPane(contentpanel);
						//标签 文本框 按钮
						JLabel typeCode=new JLabel("类型编码:");
						JLabel typeName=new JLabel("类型名称:");
						final	JTextField typeCodeField=new JTextField(10);
						final	JTextField typeNameField=new JTextField(10);
						JButton save=new JButton("保存");
						JButton cancel=new JButton("取消");
						//布局
						JPanel westPanel=new JPanel();//南
						westPanel.setLayout(new GridLayout(1, 1,20,0));
						JPanel centerPanel=new JPanel();//中
						centerPanel.setLayout(new GridLayout(3,2,20,0));
						//面板布局位置
						codeTypemaintenance.getContentPane().setLayout(new FlowLayout());
						contentpanel.add(centerPanel);
						//按钮显示位置
						centerPanel.add(typeName);
						centerPanel.add(typeNameField);
						centerPanel.add(typeCode);
						centerPanel.add(typeCodeField);
						centerPanel.add(save);
						centerPanel.add(cancel);
						save.addMouseListener(new MouseAdapter() {
							@Override
							public void mouseReleased(MouseEvent e) {
								// TODO Auto-generated method stub
								codeTypemaintenance.dispose();
							}
							@Override
							public void mousePressed(MouseEvent e) {
								// TODO Auto-generated method stub
								StringUtils utils=new StringUtils();
								String textNameField=typeNameField.getText();
								String textCodeField=typeCodeField.getText();
								TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
								if(textNameField.equalsIgnoreCase("")){
									if(parentPath!=null){
								    	model2=(DefaultTreeModel)tree.getModel();
										model2.removeNodeFromParent(newChild);
										codeTypemaintenance.dispose();
								    }
									return;
								}
								if(textCodeField.equalsIgnoreCase("")){
									if(parentPath!=null){
								    	model2=(DefaultTreeModel)tree.getModel();
										model2.removeNodeFromParent(newChild);
										codeTypemaintenance.dispose();
								    }
									return;
								}
								/*if(textNameField==""||textCodeField==""){
									System.exit(0);
								}*/
								PreparedStatement pstmt = null;
								try {
									String sql="insert into "+CBSystemConstants.opcardUser+"T_A_DICTIONARYZB(TYPEID,TYPECODE,TYPENAME,UNITCODE,PARENTID)values(?,?,?,?,?)";
									pstmt=DBManager.getConnection().prepareStatement(sql);
									pstmt.setString(1, utils.getUUID());
									pstmt.setString(2, textCodeField);
									pstmt.setString(3, textNameField);
									pstmt.setString(4, CBSystemConstants.unitCode);
									pstmt.setString(5, codeNamemodel.getCode());
									pstmt.executeUpdate();
									init();
								} catch (Exception e2) {
									// TODO: handle exception
									e2.printStackTrace();
								}
								codeTypemaintenance.dispose();
							}
						});
						cancel.addMouseListener(new MouseAdapter() {
							public void mousePressed(MouseEvent e) {
								TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
							    if(parentPath!=null){
							    	model2=(DefaultTreeModel)tree.getModel();//model=(DefaultTreeModel) tree.getModel();
									model2.removeNodeFromParent(newChild);
									codeTypemaintenance.dispose();
							    }
							}
						});
						codeTypemaintenance.setVisible(true);
					}
				});
				//删除
				deleteItem.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e){
						Object[] options = {"确定","取消"};
						int response=JOptionPane.showOptionDialog(null, "删除后就不能恢复，你确定要删除吗？", "删除", JOptionPane.YES_OPTION, JOptionPane.QUESTION_MESSAGE, null, options, options[0]);
						if(response==0) { 
						TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
					    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
					   	final CodeNameModel codeNamemodel=(CodeNameModel) currentnode.getUserObject();
					   	//System.out.println("codeNamemodel.getCode:"+codeNamemodel.getCode());
					   	PreparedStatement pstmt = null;
						try {
							String sql="delete from "+CBSystemConstants.opcardUser+"T_A_DICTIONARYZB where TYPEID=? ";
							pstmt=DBManager.getConnection().prepareStatement(sql);
							pstmt.setString(1, codeNamemodel.getCode());
							pstmt.executeUpdate();
							//System.out.println("删除成功...");
							init();
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
						}else if(response==1){
							return;
						}
					}
				});
				//下移
				downMoveiItem.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e){
						TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
					    DefaultMutableTreeNode currentNode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
					   	final CodeNameModel currentcodeNamemodel=(CodeNameModel) currentNode.getUserObject();
					   	String currentcode=currentcodeNamemodel.getCode();//当前节点
					   	DefaultMutableTreeNode nextNode=currentNode.getNextNode();
					   	if(nextNode==null)return;
					   	CodeNameModel nextcodeNamemodel=(CodeNameModel) nextNode.getUserObject();
					   	String nextcode=nextcodeNamemodel.getCode();//下移节点
						try {
							int current=DBManager.queryForInt("select type_order from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb where typeid='"+currentcode+"'");
							int next=DBManager.queryForInt("select type_order from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb where typeid='"+nextcode+"'");
							//System.out.println("current:"+current+",next:"+next);
							DBManager.execute("update "+CBSystemConstants.opcardUser+"T_A_DICTIONARYZB set type_order="+next+"where typeid='"+currentcode+"'");
							DBManager.execute("update "+CBSystemConstants.opcardUser+"T_A_DICTIONARYZB set type_order="+(current)+"where typeid='"+nextcode+"'");
							//DBManager.execute("update "+CBSystemConstants.opcardUser+"T_A_DICTIONARYZB set type_order="+(next)+"where typeid='"+nextcode+"'");
							//System.out.println("current2:"+current+",next2:"+next);
							//System.out.println("上移成功...");
							init();
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
					   	//System.out.println("codeNamemodel.getCode:"+codeNamemodel.getCode());
					   	
					}
				});
				//上移
				upMoveiItem.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e){
						TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
					    DefaultMutableTreeNode currentNode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
					   	final CodeNameModel currentcodeNamemodel=(CodeNameModel) currentNode.getUserObject();
					   	String currentcode=currentcodeNamemodel.getCode();//当前节点
					   	DefaultMutableTreeNode prevNode=currentNode.getPreviousNode();
					   	CodeNameModel prevcodeNamemodel=(CodeNameModel) prevNode.getUserObject();
					   	String prevcode=prevcodeNamemodel.getCode();//下移节点
						try {
							int prev=DBManager.queryForInt("select type_order from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb where typeid='"+prevcode+"'");
							int current=DBManager.queryForInt("select type_order from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb where typeid='"+currentcode+"'");
							//if(prevcode==null)return;
							DBManager.execute("update "+CBSystemConstants.opcardUser+"T_A_DICTIONARYZB set type_order="+prev+"where typeid='"+currentcode+"'");
							DBManager.execute("update "+CBSystemConstants.opcardUser+"T_A_DICTIONARYZB set type_order="+current+"where typeid='"+prevcode+"'");
							init();
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
					   	//System.out.println("codeNamemodel.getCode:"+codeNamemodel.getCode());
					   	
					}
				});
				//修改
				upItem.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e){
						//获取当前节点
						TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
					    DefaultMutableTreeNode currentNode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
					   	final CodeNameModel currentcodeNamemodel=(CodeNameModel) currentNode.getUserObject();
					   	final String currentcode=currentcodeNamemodel.getCode();//当前节点
						//代码类型维护界面
						final JDialog codeTypemaintenance=new JDialog();
						codeTypemaintenance.setSize(270, 200);
						codeTypemaintenance.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
						codeTypemaintenance.setTitle("代码修改界面");
						codeTypemaintenance.setResizable(false);//固定对话框的大小
						codeTypemaintenance.setLocationRelativeTo(dialog);
						codeTypemaintenance.setModal(true);//对话框这是为模式状态(父不操作)	
						//Container contentPane=codeTypemaintenance.getContentPane();
						JPanel contentpanel=new JPanel();
						codeTypemaintenance.setContentPane(contentpanel);
						//标签 文本框 按钮
						JLabel typeCode=new JLabel("类型编码:");
						JLabel typeName=new JLabel("类型名称:");
						final	JTextField typeCodeField=new JTextField(10);
						final	JTextField typeNameField=new JTextField(10);
						JButton save=new JButton("保存");
						JButton cancel=new JButton("取消");
						//布局
						JPanel westPanel=new JPanel();//南
						westPanel.setLayout(new GridLayout(1, 1,20,0));
						JPanel centerPanel=new JPanel();//中
						centerPanel.setLayout(new GridLayout(3,2,20,0));
						//面板布局位置
						codeTypemaintenance.getContentPane().setLayout(new FlowLayout());
						contentpanel.add(centerPanel);
						//按钮显示位置
						centerPanel.add(typeName);
						centerPanel.add(typeNameField);
						centerPanel.add(typeCode);
						centerPanel.add(typeCodeField);
						centerPanel.add(save);
						centerPanel.add(cancel);
						save.addMouseListener(new MouseAdapter() {
							@Override
							public void mouseReleased(MouseEvent e) {
								// TODO Auto-generated method stub
								codeTypemaintenance.dispose();
							}
							@Override
							public void mousePressed(MouseEvent e) {
								// TODO Auto-generated method stub
								String textNameField=typeNameField.getText();
								String textCodeField=typeCodeField.getText();
								if(textNameField.equalsIgnoreCase("")){
									return;
								}
								if(textCodeField.equalsIgnoreCase("")){
									return;
								}
								try {
									DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_dictionaryzb set typecode='"+textCodeField+"',typename='"+textNameField+"' where typeid='"+currentcode+"'");
									init();
								} catch (Exception e2) {
									// TODO: handle exception
									e2.printStackTrace();
								}
								codeTypemaintenance.dispose();
							}
						});
						cancel.addMouseListener(new MouseAdapter() {
							public void mousePressed(MouseEvent e) {
								if(codeTypemaintenance!=null){
									codeTypemaintenance.dispose();
								}
							}
						});
						codeTypemaintenance.setVisible(true);
					}
				});
			}
			/* (non-Javadoc)
			 * @see java.awt.event.MouseListener#mousePressed(java.awt.event.MouseEvent)
			 */
			@Override
			//按下事件
			public void mousePressed(MouseEvent e) {
				//System.out.println("e.getSource():"+e.getSource());
				//if(e.getSource()==null){return;}
				/*tree=(JTree)e.getSource();
				int location=tree.getRowForLocation(e.getX(), e.getY());//找出当前所在位置
				if(location==0){
					//后续补上
					//清除table中的内容  先清除再查询数据
					DefaultTableModel model =(DefaultTableModel) table.getModel(); 
					while(model.getRowCount()>0){     
						model.removeRow(model.getRowCount()-1);
					}
				}else if(location==1){
					//清除table中的内容  先清除再查询数据
					DefaultTableModel model =(DefaultTableModel) table.getModel(); 
					while(model.getRowCount()>0){     
						model.removeRow(model.getRowCount()-1);
						}
					Integer i=1;
					PreparedStatement pstmt = null;
					try {
						String sql="select t2.code,t2.name,t2.codetype from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb t1,"+CBSystemConstants.opcardUser+"t_a_dictionary t2 where t1.typecode=t2.codetype and t1.typecode='runmodel'";
						pstmt=DBManager.getConnection().prepareStatement(sql,ResultSet.TYPE_SCROLL_INSENSITIVE,ResultSet.CONCUR_READ_ONLY);
						//pstmt=DBManager.getConnection().prepareStatement(sql,ResultSet.TYPE_SCROLL_INSENSITIVE,ResultSet.CONCUR_READ_ONLY);
						ResultSet rs=pstmt.executeQuery();
						//rs.last();   
						//int n = rs.getRow();    //结果集指针知道最后一行数据  
						//rs.beforeFirst();//将结果集指针指回到开始位置，这样才能通过while获取rs中的数据 
							for(int i=0;i<n;i++){
							System.out.println("n:"+n);
							if(rs.next()){
							//data[i][1]=rs.getString(1);
							//data[i][2]=rs.getString(2);
							//data[i][3]=rs.getString(3);
							//data[i][4]=rs.getString(4);
							}
						}
						while(rs.next()){
							String code = rs.getString(1);
						    String name = rs.getString(2);
						    String codetype = rs.getString(3);
						    String[] str_row = {i.toString(),code, name,(codetype.equalsIgnoreCase("runmodel"))?"接线方式":""}; // 将一行的数据存在str_row
						    model.addRow(str_row);// 添加在表模板中
						    i++;
						}
						  table.setModel(model);// 将table这个表 设置为刚刚定义的模板
						  scrollPane_1.setViewportView(table);
					} catch (Exception e2) {
						// TODO: handle exception
						e2.printStackTrace();
					}
				}else if(location==2){
					//清除table中的内容  先清除再查询数据
					DefaultTableModel model =(DefaultTableModel) table.getModel(); 
					while(model.getRowCount()>0){     
						model.removeRow(model.getRowCount()-1);
					}
					Integer i=1;
					PreparedStatement pstmt = null;
					try {
						String sql="select t2.code,t2.name,t2.codetype from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb t1,"+CBSystemConstants.opcardUser+"t_a_dictionary t2 where t1.typecode=t2.codetype and t1.typecode='Breaker'";
						pstmt=DBManager.getConnection().prepareStatement(sql);
						ResultSet rs=pstmt.executeQuery();
						while(rs.next()){
							String code = rs.getString(1);
						    String name = rs.getString(2);
						    String codetype = rs.getString(3);
						    String[] str_row = {i.toString(),code, name,(codetype.equalsIgnoreCase("Breaker"))?"开关":""};// 将一行的数据存在str_row
						    model.addRow(str_row);// 添加在表模板中
						    i++;
						}
						  table.setModel(model);// 将table这个表 设置为刚刚定义的模板
						  scrollPane_1.setViewportView(table);
						//判断行集
							int index=model.getRowCount();
							if(index!=0){
								btnNewButton_1.setEnabled(true);
							}
					} catch (Exception e2) {
						// TODO: handle exception
						e2.printStackTrace();
					}
				
				}else if(location==3){
					//清除table中的内容  先清除再查询数据
					DefaultTableModel model =(DefaultTableModel) table.getModel(); 
					while(model.getRowCount()>0){     
						model.removeRow(model.getRowCount()-1);
						}
					Integer i=1;
					PreparedStatement pstmt = null;
					try {
						String sql="select t2.code,t2.name,t2.codetype from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb t1,"+CBSystemConstants.opcardUser+"t_a_dictionary t2 where t1.typecode=t2.codetype and t1.typecode='Disconnector'";
						pstmt=DBManager.getConnection().prepareStatement(sql);
						ResultSet rs=pstmt.executeQuery();
						while(rs.next()){
							String code = rs.getString(1);
						    String name = rs.getString(2);
						    String codetype = rs.getString(3);
						    String[] str_row = {i.toString(),code, name,(codetype.equalsIgnoreCase("Disconnector"))?"刀闸":""}; // 将一行的数据存在str_row
						    model.addRow(str_row);// 添加在表模板中
						    i++;
						}
						  table.setModel(model);// 将table这个表 设置为刚刚定义的模板
						  scrollPane_1.setViewportView(table);
					} catch (Exception e2) {
						// TODO: handle exception
						e2.printStackTrace();
					}
				
				}*/
				//清除table中的内容  先清除再查询数据
				DefaultTableModel model =(DefaultTableModel) table.getModel(); 
				while(model.getRowCount()>0){     
					model.removeRow(model.getRowCount()-1);
				}
				Integer seq=1;//序号初始为1
				//PreparedStatement pstmt = null;
				try {
					TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
					if(parentPath==null){
						return;
					}
				    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
				   	CodeNameModel codeNamemodel=(CodeNameModel) currentnode.getUserObject();
				   	String id=codeNamemodel.getCode();
					String sql="select t2.code,t2.name,t2.codetype from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb t1,"+CBSystemConstants.opcardUser+"t_a_dictionary t2 where t1.typeid=t2.parentid and t2.parentid='"+id+"'";
					//pstmt=DBManager.getConnection().prepareStatement(sql);
					List list=DBManager.queryForList(sql);
					//System.out.println("list...............................:"+list);
					for(int index=0;index<list.size();index++){
							Map map=(Map)list.get(index);
							String code = (String) map.get("CODE");
							String name = (String) map.get("Name");
							String[] str_row = {seq.toString(),code, name};
						    model.addRow(str_row);// 添加在表模板中
						    seq++;
					}
					/*ResultSet rs=pstmt.executeQuery();
					while(rs.next()){
						String code = rs.getString(1);
					    String name = rs.getString(2);
					    String[] str_row = {seq.toString(),code, name};
					    model.addRow(str_row);// 添加在表模板中
					    seq++;
					}*/
					//rs.close();
					//pstmt.close();
					//DBManager.getConnection().close();
					//判断行集
					int index=model.getRowCount();
					if(index!=0){
						ediNewButton.setEnabled(true);
						deleteNewButton.setEnabled(true);
					}else{
						ediNewButton.setEnabled(false);
						deleteNewButton.setEnabled(false);
					}
					table.setModel(model);// 将table这个表 设置为刚刚定义的模板
					scrollPane_1.setViewportView(table);
						
				} catch (Exception e2) {
					// TODO: handle exception
					e2.printStackTrace();
				}

			}
		});
	}
	//tree初始化
	public static void init(){
		//定义两个集合,枝节点集合和叶节点集合
		final Vector<CodeNameModel> root=new Vector<CodeNameModel>();
		final Vector<CodeNameModel> leafs=new Vector<CodeNameModel>();
		CodeNameModel codeNameModel=null;
		PreparedStatement pstmt = null;
		Connection conn=null;
		try {
				conn=DBManager.getConnection();
				String rootsql="select TYPEID,TYPENAME from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb where PARENTID='0' order by type_order asc";
				pstmt=conn.prepareStatement(rootsql);
				ResultSet rs=pstmt.executeQuery();
				while(rs.next()){
				codeNameModel=new CodeNameModel();
				codeNameModel.setCode(rs.getString(1));
				codeNameModel.setName(rs.getString(2));
				root.add(codeNameModel);
				//nodes2.add((MutableTreeNode) codeNameModel);
				//conn.close();
				//System.out.println(",root:"+rs.getString(1)+","+rs.getString(2));
			}
				String leafsql="select t2.typeid,t2.typename from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb t1,"+CBSystemConstants.opcardUser+"t_a_dictionaryzb t2 where t1.typeid=t2.parentid order by t2.type_order asc";
				pstmt=conn.prepareStatement(leafsql);
				rs=pstmt.executeQuery();
				while(rs.next()){
					codeNameModel=new CodeNameModel();
					codeNameModel.setCode(rs.getString(1));
					codeNameModel.setName(rs.getString(2));
					leafs.add(codeNameModel);
					//System.out.println(",leafs:"+rs.getString(1)+","+rs.getString(2));
				}
				//System.out.println("leafs:"+leafs);
				DefaultMutableTreeNode rootStr=null;
				for(int root2=0;root2<root.size();root2++){
					rootStr=new DefaultMutableTreeNode(root.get(root2));
					for(int leaf=0;leaf<leafs.size();leaf++){
							DefaultMutableTreeNode leafStr=new DefaultMutableTreeNode(leafs.get(leaf));
							rootStr.add(leafStr);
					}
			}
			model2=new DefaultTreeModel(rootStr);
			tree.setModel(model2);
			if(rs!=null || pstmt!=null || conn!=null){
			rs.close();
			pstmt.close();
			conn.close();}
		}catch (Exception e){
				// TODO: handle exception
			e.printStackTrace();
		}
	}
	public static void inittable(){/*
		JTable table=new JTable(){
			public boolean isCellEditable(int row, int column)
              {return false;}
        };
		//清除table中的内容  先清除再查询数据
		DefaultTableModel model =(DefaultTableModel) table.getModel(); 
		while(model.getRowCount()>0){     
			model.removeRow(model.getRowCount()-1);
		}
		Integer seq=1;//序号初始为1
		try {
			TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
			if(parentPath==null){
				return;
			}
		    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
		   	CodeNameModel codeNamemodel=(CodeNameModel) currentnode.getUserObject();
		   	String id=codeNamemodel.getCode();
			String sql="select t2.code,t2.name,t2.codetype from "+CBSystemConstants.opcardUser+"t_a_dictionaryzb t1,"+CBSystemConstants.opcardUser+"t_a_dictionary t2 where t1.typeid=t2.parentid and t2.parentid='"+id+"'";
			List list=DBManager.queryForList(sql);
			for(int index=0;index<list.size();index++){
					Map map=(Map)list.get(index);
					String code = (String) map.get("CODE");
					String name = (String) map.get("Name");
					String[] str_row = {seq.toString(),code, name};
				    model.addRow(str_row);// 添加在表模板中
				    seq++;
			}
		} catch (Exception e2) {
			// TODO: handle exception
			e2.printStackTrace();
		}
		table.setModel(model);
	*/}
 }


