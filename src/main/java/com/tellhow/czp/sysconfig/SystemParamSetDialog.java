package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.Frame;
import java.awt.Panel;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.ScrollPaneConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumn;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.czp.user.UserDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.DOMUtil;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.stationstartup.ProjectConfigLoader;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class SystemParamSetDialog extends JDialog {
	private Document doc;
    private NodeList  allVlaues;
    private JTable table;
    private JTable chotable;
    private  JComboBox combox;
	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			SystemParamSetDialog dialog = new SystemParamSetDialog(null, false);
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * Create the dialog.
	 */
	public SystemParamSetDialog(Frame parent, boolean isModel) {
		super(parent, isModel);
		setTitle("操作票系统设置");
		setSize(450, 300);
		WindowUtils.centerWindow(parent, this);
		
		JTabbedPane tabbedPane = new JTabbedPane(JTabbedPane.TOP);
		getContentPane().add(tabbedPane, BorderLayout.CENTER);
		
		JPanel panel = new JPanel();
		tabbedPane.addTab("系统参数设置", null, panel, null);
		panel.setLayout(new BorderLayout(0, 0));
		
		JPanel panelcho =new JPanel();
		tabbedPane.addTab("系统参数选择",null,panelcho, null);
		panelcho.setLayout(new BorderLayout(0, 0));
		
		JScrollPane scrollPanecho = new JScrollPane();
		scrollPanecho.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPanecho.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
		panelcho.add(scrollPanecho, BorderLayout.CENTER);
		
		
		JScrollPane scrollPane = new JScrollPane();
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
		panel.add(scrollPane, BorderLayout.CENTER);
		
		table = new JTable();
		scrollPane.setViewportView(table); 
		
		chotable = new JTable();
		scrollPanecho.setViewportView(chotable);                                                                                            
		
		Panel panelbutton= new Panel();
		panel.add(panelbutton,BorderLayout.SOUTH);
        
		Panel panelchobutton =new Panel();
		panelcho.add(panelchobutton, BorderLayout.SOUTH);
		JButton setchobutton= new JButton("确定");
		panelchobutton.add(setchobutton);
		setchobutton.addActionListener(new ActionListener() 
		{
			public void actionPerformed(ActionEvent e)
			{
				setbuttonchoactionPerformed(e);
			}
		});

		JButton cancechobutton = new JButton("关闭");
		panelchobutton.add(cancechobutton);
		cancechobutton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				
				SystemParamSetDialog.this.dispose();
			}
		});
		
		
		JButton setbutton = new JButton("设置");
		panelbutton.add(setbutton);
		
		setbutton.addActionListener(new ActionListener() 
		{
			public void actionPerformed(ActionEvent e)
			{
				setbuttonactionPerformed(e);
			}
		});
				
		JButton cancelbutton = new JButton("关闭");
		panelbutton.add(cancelbutton);
		cancelbutton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				
				SystemParamSetDialog.this.dispose();
			}
		});
		
		String[] tableHeads=new String[]{"系统设置编码","序号", "系统设置选项", "是否启用"};
		DefaultTableModel  dtm=new DefaultTableModel(){
 			 public boolean isCellEditable(int rowIndex, int columnIndex) {
				 if(columnIndex==0||columnIndex==1||columnIndex==2) return false;
				 return true;
			 
			 } 
 		 };
 		 table.setModel(dtm);
		dtm.setColumnIdentifiers(tableHeads);
		table.getTableHeader().getColumnModel().getColumn(0).setMaxWidth(0);
        table.getTableHeader().getColumnModel().getColumn(0).setMinWidth(0);
        table.getTableHeader().getColumnModel().getColumn(0).setPreferredWidth(0);
        
//        List cholist= getSystemChoose();
        List cholist = DBManager.queryForList(this.getLikeKey(CBSystemConstants.getUser().getUserID()));
        Map map = new HashMap();
        for( int i = 0; i < cholist.size(); i++){
			Map temp = (Map) cholist.get(i);
//        Element    codeElem = (Element) cholist.get(i);
//        String      codename =codeElem.getAttribute("name").trim();
//        String      codeKey = codeElem.getAttribute("key").trim();
//        String     codeValue=codeElem.getTextContent().trim();
        
		String codename = StringUtils.ObjToString(temp.get("LIKENAME"));
        String codeKey = StringUtils.ObjToString(temp.get("LIKEID"));
        String codeValue = StringUtils.ObjToString(temp.get("LIKEVALUE"));
        if("1".equals(codeValue)){
        	codeValue = "true";
        }else {
        	codeValue = "false";
        }
        boolean	codevalue = false;
        codevalue= Boolean.valueOf(codeValue);
        Vector v = new Vector();
 		 /******转化成Vector***********/
 		  v.add(codeKey);
 		  v.add(i+1);
 		  v.add(codename);
 		  v.add(codevalue);
 		
 		
 		  dtm.addRow(v);
 		  TableColumn tc = table.getTableHeader().getColumnModel().getColumn(0);
 		  TableColumn   aColumn   =  table.getColumnModel().getColumn(3);
 		  aColumn.setCellEditor(table.getDefaultEditor(Boolean.class));   
 		  aColumn.setCellRenderer(table.getDefaultRenderer(Boolean.class));

 		  tc.setMaxWidth(0);
 		  tc.setPreferredWidth(0);
 		  tc.setWidth(0);
 		  tc.setMinWidth(0);
       
		}      
        //系统设置选择
        String[] tablechoHeads=new String[]{"系统设置编码","序号", "系统设置", "选项"};
		DefaultTableModel  chodtm=new DefaultTableModel(){
 			 public boolean isCellEditable(int rowIndex, int columnIndex) {
				 if(columnIndex==0||columnIndex==1||columnIndex==2) return false;
				 return true;
			 
			 } 
 		 };
 		 chotable.setModel(chodtm);
		chodtm.setColumnIdentifiers(tablechoHeads);
		chotable.getTableHeader().getColumnModel().getColumn(0).setMaxWidth(0);
        chotable.getTableHeader().getColumnModel().getColumn(0).setMinWidth(0);
        chotable.getTableHeader().getColumnModel().getColumn(0).setPreferredWidth(0);
        
         combox= new JComboBox();
        DefaultComboBoxModel dcbm=new DefaultComboBoxModel();
		CodeNameModel zhl=new CodeNameModel("0","综合令");
		CodeNameModel fbl=new CodeNameModel("1","分步令");
		
		dcbm.addElement(zhl);
		dcbm.addElement(fbl);
		combox.setModel(dcbm);
//	combox.addItem("综合令");
//		combox.addItem("分步令");
		List chospelist= getSpeSystemChoose("cardtype");     
        for( int i = 0; i < chospelist.size(); i++){
			//Map temp = (Map) cholist.get(i);
        Element    codeElem = (Element) chospelist.get(i);
        //String      codename =codeElem.getAttribute("name").trim();
        String codename="开票方式";
        String      codeKey = codeElem.getAttribute("key").trim();
        String    codevalue=codeElem.getTextContent().trim();
        
//        if(codeValue.equals("1"))
//        	combox.getModel().setSelectedItem(fbl);
			 Vector v = new Vector();
			 
			
 		 /******转化成Vector***********/
 		  v.add(codeKey);
 		  v.add(i+1);
 		  v.add(codename);
 		 if(codevalue.equals("0"))
 		    v.add("综合令");combox.getModel().setSelectedItem(zhl);
 		  if(codevalue.equals("1"))
 			v.add("分步令");combox.getModel().setSelectedItem(fbl);
 		
 		 chodtm.addRow(v);
 		TableColumn tc = chotable.getTableHeader().getColumnModel().getColumn(0);
 		TableColumn   aColumn   =  chotable.getColumnModel().getColumn(3);   
 		aColumn.setCellEditor(new DefaultCellEditor(combox));   
 		//aColumn.setCellRenderer(chotable.getDefaultRenderer(Boolean.class));
         tc.setMaxWidth(0);
         tc.setPreferredWidth(0);
         tc.setWidth(0);
         tc.setMinWidth(0);
       
		}    
   
	}
	
	/*
	 * 描述 获取T_A_USERLIKEKEY表数据
	 * @param
	 * @param
	 * @param
	 * */
//	public static String getLikeKey(String userid) {
//		String sql = "select * from T_A_USERLIKE t where t.userid ='"+userid+"' and t.likekey !='refreshrate'";
//		return sql;
//	}
	public static String getLikeKey(String userid) {
		String sql = "select t.likeid,t.likename, s.userlike as likevalue from "+CBSystemConstants.opcardUser+"t_a_userlikekey t ,"+CBSystemConstants.opcardUser+"t_a_userlike s where t.likeid = s.likekey " +
				"and s.userid = '"+userid+"' and t.likeid !='refreshrate'";
		return sql;
	}
	
	protected void setbuttonchoactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		for(int j=0;j<chotable.getRowCount();j++)
		{
			String keys=chotable.getValueAt(j, 0).toString();
			//String values=chotable.getValueAt(j, 3).toString();
			CodeNameModel chomode =(CodeNameModel) combox.getSelectedItem();
			String values =chomode.getCode();
			setCodeKey(keys, values);
		}
		new ProjectConfigLoader().load(); //重新读取系统配置
		 JOptionPane.showMessageDialog(null,"确定成功","提示", JOptionPane.INFORMATION_MESSAGE);
	}

	protected void setbuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		boolean isApplyToAll = false;
		if(CBSystemConstants.getUser().getUserDuty().equals("0")) {
			int ok = JOptionPane.showConfirmDialog(this, "是否应用到所有用户？",
					"操作票提示框", JOptionPane.YES_NO_OPTION);
			if (ok == JOptionPane.YES_OPTION) {
				isApplyToAll = true;
			}
		}
		
		if(isApplyToAll){
			for(int j=0;j<table.getRowCount();j++) {
				String keys=table.getValueAt(j, 0).toString();
				String values=table.getValueAt(j, 3).toString();
				setCodeKey(keys, values);
				DBManager.execute("update "+CBSystemConstants.opcardUser+"t_A_USERLIKEKEY t set t.likevalue='"+(values.equals("true")?"1":"0")+"' " +
						"where t.likeid='"+keys+"'");
				DBManager.execute("update "+CBSystemConstants.opcardUser+"t_A_USERLIKE t set t.userlike='"+(values.equals("true")?"1":"0")+"' " +
						"where t.likekey='"+keys+"'");
	
			}
		}
		else {
			for(int j=0;j<table.getRowCount();j++) {
				String keys=table.getValueAt(j, 0).toString();
				String values=table.getValueAt(j, 3).toString();
				setCodeKey(keys, values);
				DBManager.execute("update "+CBSystemConstants.opcardUser+"t_A_USERLIKE t set t.userlike='"+(values.equals("true")?"1":"0")+"' " +
						"where t.likekey='"+keys+"' and t.userid='"+CBSystemConstants.getUser().getUserID()+"'");
	
			}
		}
		new ProjectConfigLoader().load(); //重新读取系统配置
		new UserDao().LoadUserLike(CBSystemConstants.getUser());
		 JOptionPane.showMessageDialog(null,"设置成功","提示", JOptionPane.INFORMATION_MESSAGE);
	}
	
	public List getSystemChoose()
	{
		 doc = DOMUtil.readXMLFile(CBSystemConstants.SYS_CONFIG_XML_FILE);
			Element rootE = doc.getDocumentElement();
			Element childE = null;
	        NodeList childEs = rootE.getChildNodes();
	        List syschoose=new ArrayList();
	        for (int i = 0; i < childEs.getLength(); i++) {
	        	if(childEs.item(i).getNodeName().equals("#text"))
	        		continue;
	            childE = (Element) childEs.item(i);
	           
	          if (childE.getAttribute("name").toUpperCase().equals("PROJECTPARAM")) { 
	        	  allVlaues = childE.getElementsByTagName("value");
	              for (int j = 0; j < allVlaues.getLength(); j++) {
	              Element    codeElem = (Element) allVlaues.item(j);
	              String      codename =codeElem.getAttribute("name").trim();
	                  if(!codename.equals("")){
	                	  syschoose.add(codeElem);
	                	  
	                  }
	              }
	          }
	        }
		return syschoose;
	}
	//根据传入的参数查找系统设置中相匹配的数值
	public List getSpeSystemChoose(String key)
	{
		 doc = DOMUtil.readXMLFile(CBSystemConstants.SYS_CONFIG_XML_FILE);
			Element rootE = doc.getDocumentElement();
			Element childE = null;
	        NodeList childEs = rootE.getChildNodes();
	        List sysspechoose=new ArrayList();
	        for (int i = 0; i < childEs.getLength(); i++) {
	        	if(childEs.item(i).getNodeName().equals("#text"))
	        		continue;
	            childE = (Element) childEs.item(i);
	           
	          if (childE.getAttribute("name").toUpperCase().equals("PROJECTPARAM")) { 
	        	  allVlaues = childE.getElementsByTagName("value");
	              for (int j = 0; j < allVlaues.getLength(); j++) {
	              Element    codeElem = (Element) allVlaues.item(j);
	              String      codekey =codeElem.getAttribute("key").trim();
	                  if(codekey.equals(key)){
	                	  sysspechoose.add(codeElem);
	                	  
	                  }
	              }
	          }
	        }
		return sysspechoose;
	}
	//设置key的值得函数
		 public void setCodeKey(String key, String value) {
				Element codeElem = null;
				String codeKey = "";
				if (allVlaues == null) {
					return;
				}
				for (int j = 0; j < allVlaues.getLength(); j++) {
					codeElem = (Element) allVlaues.item(j);
					codeKey = codeElem.getAttribute("key").trim();
					if (key.equals(codeKey)) {
						codeElem.setTextContent(value);
						DOMUtil
								.writeXMLFile(doc,
										CBSystemConstants.SYS_CONFIG_XML_FILE);

						break;
					}
				}
			}

}
