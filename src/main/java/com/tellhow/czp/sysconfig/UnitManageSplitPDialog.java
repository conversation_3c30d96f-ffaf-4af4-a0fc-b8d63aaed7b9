package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.GraphicsEnvironment;
import java.awt.Point;
import java.awt.Toolkit;

import javax.swing.JDialog;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;

public class UnitManageSplitPDialog extends JDialog {

	/**
	 * 单元管理主界面
	 */
	
	public static void main(String[] args) {
		try {
			UnitManageSplitPDialog dialog = new UnitManageSplitPDialog();
			dialog.setResizable(false);
			dialog.setSize(600, 500);
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			Point p = GraphicsEnvironment.getLocalGraphicsEnvironment().getCenterPoint();    
			dialog.setBounds(p.x - 600 / 2, p.y - 500 / 2, 600, 500); //将对话框设置为居中位置 
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
			
		}
	}

	/**
	 * Create the dialog.
	 */
	public UnitManageSplitPDialog() {
		setIconImage(Toolkit.getDefaultToolkit().getImage(DictionaryDialog.class.getResource("/resources/xmlgui/icons/unit.png")));
		setBounds(100, 100, 450, 300);
		JSplitPane splitPane = new JSplitPane();
		getContentPane().setLayout(new BorderLayout());
		{	
			getContentPane().add(splitPane, BorderLayout.CENTER);
		}
		JScrollPane scrollPane = new JScrollPane();
		UnitManageSplitLeftPDialog splitLeft=new UnitManageSplitLeftPDialog();
		///UnitManager unitManager=new UnitManager();//初始化实体类都为空
		UnitManageSplitRightPDialog splitRight=new UnitManageSplitRightPDialog();
		splitLeft.setSplitRight(splitRight);//初始对象
		UnitManageSplitRightPDialog.init();
		splitPane.setLeftComponent(scrollPane);//左边
		splitPane.setRightComponent(splitRight.getConterPanel());//右边
		splitPane.setDividerLocation(120);//设置分割条的位置
		splitPane.setDividerSize(2);//设置分割条的大小
		scrollPane.setViewportView(splitLeft.getTree());//创建一个视口，如果必要的话，然后将其为视图。
	}

}
