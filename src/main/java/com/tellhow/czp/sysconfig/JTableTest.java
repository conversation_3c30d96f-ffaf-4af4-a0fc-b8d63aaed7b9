package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.Container;
import java.awt.Dimension;

import javax.swing.JFrame;
import javax.swing.JTable;
import javax.swing.table.DefaultTableModel;
public class JTableTest extends J<PERSON>rame {
private JTable table;
private DefaultTableModel model;
private Object[] header={"1","2","3"};
private Object[][] data={{"sdf",new Integer(1),new Boolean(false)},{"abc",new Integer(2),new Boolean(true)}};
public JTableTest()
{
initComponent();
this.setMinimumSize(new Dimension(300,300));
this.setLocationRelativeTo(null);
this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
}
private void initComponent()
{
Container content=this.getContentPane();
model=new DefaultTableModel(data,header){
public Class<?> getColumnClass(int columnIndex) {
return getValueAt(0, columnIndex).getClass();
}

};
table=new JTable(model);
content.add(table.getTableHeader(),BorderLayout.NORTH);
content.add(table);
}
public static void main(String[] args) {
new JTableTest().setVisible(true);
}
}
