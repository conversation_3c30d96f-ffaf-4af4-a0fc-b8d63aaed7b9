package com.tellhow.czp.sysconfig;

import java.awt.Component;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;

import com.tellhow.czp.user.User;


import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

@SuppressWarnings("serial")
public class ConfigTicketDialog extends JDialog {
	public ConfigTicketDialog() {
	}
	private JLabel lbTitle;
	private JLabel lbZX;
	private JLabel lbZXXYZ;
	private JLabel lbZL;
	private JLabel lbZLXYZ;
	private JPopupTextField tfZX;
	private JPopupTextField tfZL;
	private JButton bSave;
	private JButton bCancel;
	SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	String sysdate;
        private JButton bSee;
        private String ZXBanh;
        private String ZLBanh;
    	{
    		this.setModal(true);
    		this.setTitle("操作票编号设置");
    		this.setSize(300,300);
    		getContentPane().setLayout(null);
    		int w=(int)Toolkit.getDefaultToolkit().getScreenSize().getWidth();
            int h=(int)Toolkit.getDefaultToolkit().getScreenSize().getHeight();
            this.setLocation((w-this.getSize().width)/2, (h-this.getSize().height)/2);
            this.setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    		lbTitle = new JLabel("设置操作票编号最后四位");
    		lbTitle.setFont(new java.awt.Font("Dialog", 1, 14));
    		lbTitle.setForeground(new java.awt.Color(0, 0, 153));
    		lbTitle.setAlignmentX(Component.CENTER_ALIGNMENT);
    		lbZL = new JLabel("初始综令票");
    		lbZLXYZ = new JLabel("下一张综令票");
    		lbZX = new JLabel("初始逐项票");
    		lbZXXYZ = new JLabel("下一张逐项票");
    		tfZL = new JPopupTextField();
    		tfZX = new JPopupTextField();
    		bSave = new JButton("保存");
    		bCancel = new JButton("取消");
            bSee=new JButton("查看");
    		
    		lbTitle.setBounds(50, 20, 350, 30);
    		lbZL.setBounds(50, 70, 80, 30);
    		lbZLXYZ.setBounds(50, 70, 80, 30);
    		tfZL.setBounds(130, 70, 120, 30);
    		lbZX.setBounds(50, 120, 80, 30);
    		lbZXXYZ.setBounds(50, 120, 80, 30);
    		tfZX.setBounds(130, 120, 120, 30);
    		bSave.setBounds(40, 180, 60, 25);
    		bCancel.setBounds(110, 180, 60, 25);
            bSee.setBounds(180, 180, 60, 25);
    		
    		getContentPane().add(lbTitle);
    		getContentPane().add(lbZX);
    		getContentPane().add(lbZL);
    		getContentPane().add(lbZLXYZ);
    		getContentPane().add(tfZX);
    		getContentPane().add(lbZXXYZ);
    		getContentPane().add(tfZL);
    		getContentPane().add(bSave);
    		getContentPane().add(bCancel);
            getContentPane().add(bSee);
    		
    		getBanh();
    		
    		bSave.addActionListener(new java.awt.event.ActionListener() {
                public void actionPerformed(java.awt.event.ActionEvent evt) {
                	String zxbh = tfZX.getText().trim();
                	String zlbh = tfZL.getText().trim();
                	if(zxbh.length() ==4 && isNumeric(zxbh) && zlbh.length() ==4 && isNumeric(zlbh))
                		setBanh();
                	else
                		JOptionPane.showMessageDialog(null, "请输入四位数字!", "操作票提示框", JOptionPane.WARNING_MESSAGE);
                }
                	
            });
    		
    		bCancel.addActionListener(new java.awt.event.ActionListener() {
                public void actionPerformed(java.awt.event.ActionEvent evt) {
                	ConfigTicketDialog.this.dispose();
                }
            });
            
            bSee.addActionListener(new ActionListener() {

                public void actionPerformed(ActionEvent e) {
                	
                    TicketHistory bhh =new TicketHistory();
                    bhh.setModal(true);
                    bhh.setVisible(true);
                    
                }
            });
            
    	}
    	public boolean isNumeric(String str)
    	{
    	Pattern pattern = Pattern.compile("[0-9]*");
    	Matcher isNum = pattern.matcher(str);
    	if( !isNum.matches() )
    	{
    	return false;
    	}
    	return true;
    	}
    	public void setBanh() {
	        String userName=((User)CBSystemConstants.getUser()).getUserName();
                boolean isSave=false;
                Date date = new Date();
                sysdate = df1.format(date);
	        try {
	            Connection conn = DBManager.getConnection();
	            Statement state = conn.createStatement();	             
	            if(!tfZX.getText().trim().equals(this.ZXBanh)){
                          state.executeUpdate("update t_a_czpno set cardno='"+tfZX.getText().trim()+"' where cardkind=1 and unitcode=0");	
                          state.executeUpdate("insert into t_a_czpno values(1,'"+tfZX.getText().trim()+"',to_DATE('"+ sysdate+ "','yyyy-mm-dd HH24:mi:ss'),1)");
                          isSave=true;
                    }     
                    if(!tfZL.getText().trim().equals(this.ZLBanh)){
                          state.executeUpdate("update t_a_czpno set cardno='"+tfZL.getText().trim()+"' where cardkind=0 and unitcode=0");
                          state.executeUpdate("insert into t_a_czpno values(0,'"+tfZL.getText().trim()+"',to_DATE('"+ sysdate+ "','yyyy-mm-dd HH24:mi:ss'),1)");  
                          isSave=true;
                    }
                    if(isSave){
                    	    JOptionPane.showMessageDialog(null, "修改成功!", "操作票提示框", JOptionPane.INFORMATION_MESSAGE);
	                    ConfigTicketDialog.this.dispose();
                    }else{
                            JOptionPane.showMessageDialog(null, "数据值没有改动!", "操作票提示框", JOptionPane.INFORMATION_MESSAGE);
                            ConfigTicketDialog.this.dispose();
                    }
	        }
	        catch (SQLException ex) {
//	            Logger.getLogger(OperCardLoadtoOMS.class.getName()).log(Level.SEVERE, null, ex);
	        	ex.printStackTrace();
	        }
	}
    	public void getBanh() {
            
            try {
                Connection conn = DBManager.getConnection();
                Statement state = conn.createStatement();
                ResultSet rs = null;
                String sql = "";
                
                int count = 0;
                String bh = "";
                
            	sql = "select count(*) count from t_a_czpno where cardkind=0 and unitcode=0";
            	rs = state.executeQuery(sql);
                while (rs.next()) {
                	count = rs.getInt("count");
                }
                
                //综令票
            	if(count == 0){
                	state.execute("insert into t_a_czpno values(0,'0002',null,0)");
            		lbZL.setVisible(true);
            		lbZLXYZ.setVisible(false);
            	}else{
            		lbZL.setVisible(false);
            		lbZLXYZ.setVisible(true);
//                    tfZL.setEditable(false);
                	rs = state.executeQuery("select cardno from t_a_czpno where cardkind=0 and unitcode=1");
                	while (rs.next()) {
                    	bh = rs.getString("cardno");
                    }
                	tfZL.setText(bh);
                    this.ZLBanh=bh;            		
            	}
            	
            	sql = "select count(*) count from t_a_czpno where cardkind=1 and unitcode=0";
            	rs = state.executeQuery(sql);
                while (rs.next()) {
                	count = rs.getInt("count");
                }
            	
                //逐项票
                if(count == 0){
                	state.execute("insert into t_a_czpno values(1,'0002',null,0)");
            		lbZX.setVisible(true);
            		lbZXXYZ.setVisible(false);
                }else{
            		lbZX.setVisible(false);
            		lbZXXYZ.setVisible(true);
//                    tfZX.setEditable(false);
                    rs = state.executeQuery("select cardno from t_a_czpno where cardkind=1 and unitcode=0");
                    while (rs.next()) {
                    	bh = rs.getString("cardno");
                    }
                }
                tfZX.setText(bh);
                this.ZXBanh=bh;
            
            	

      
            }
            catch (SQLException ex) {
//                Logger.getLogger(OperCardLoadtoOMS.class.getName()).log(Level.SEVERE, null, ex);
            	ex.printStackTrace();
            }
    	}
}
