package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.Container;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextField;
import javax.swing.JTree;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.TreeSelectionEvent;
import javax.swing.event.TreeSelectionListener;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreeSelectionModel;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.DBManager;

public class EquipTypeDialog extends JDialog implements TreeSelectionListener {
    private JTree tree;
    private Map temp;
    
	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			EquipTypeDialog dialog = new EquipTypeDialog();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * Create the dialog.
	 */
	public EquipTypeDialog() {
		setTitle("设备类型维护");
		setBounds(100, 100, 450, 300);
		getContentPane().setLayout(new BorderLayout(0, 0));
		JScrollPane treeview=new JScrollPane();
		treeview.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		treeview.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
		getContentPane().add(treeview, BorderLayout.WEST);
		
		
		JTree tree = new JTree();
		tree.setModel(new DefaultTreeModel(
			new DefaultMutableTreeNode("设备类型维护") {
				{
					DefaultMutableTreeNode node_1;
					node_1 = new DefaultMutableTreeNode("一次设备");
					add(node_1);
//					List results = DBManager.query("select equiptype_id,equiptype_parent_id,equiptype_code,equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.cim_id is not null");
					//edit 2014.6.25
					List results = DBManager.query(OPEService.getService().EquipTypeDialogSql());
					temp=new HashMap();  
					System.out.println(results);
					for(int i = 0; i < results.size(); i++){
						temp = (Map) results.get(i);
						EquiptypeState equs = new EquiptypeState(StringUtils.ObjToString(temp.get("equiptype_id")),StringUtils.ObjToString(temp.get("equiptype_name")));
						DefaultMutableTreeNode node = new  DefaultMutableTreeNode( equs) ;
						node_1.add(node);
						System.out.println(node);		
					}
					
				}
			}
		));
		treeview.setViewportView(tree);
		
		JPanel panel = new JPanel();
		getContentPane().add(panel, BorderLayout.CENTER);
		panel.setLayout(new BoxLayout(panel, BoxLayout.X_AXIS));
	    
//只有一个节点被选中
      tree.addTreeSelectionListener(this);
      tree.getSelectionModel().setSelectionMode(TreeSelectionModel.SINGLE_TREE_SELECTION);
	}
	@Override
	public void valueChanged(TreeSelectionEvent e) {
		// TODO Auto-generated method stub
		
	}
    public void BoxLayoutFrame()
    {
    	Container con =getContentPane();
    	JLabel lab1=new JLabel("类型编码：");
    	JTextField text1=new JTextField(10);
    	text1.setMaximumSize(text1.getPreferredSize());
    	Box box1=Box.createHorizontalBox();
    	box1.add(lab1);
    	box1.add(Box.createHorizontalStrut(20));
    	box1.add(text1);
    	
    	JLabel lab2=new JLabel("类型名称：");
    	JTextField text2=new JTextField(10);
    	text2.setMaximumSize(text1.getPreferredSize());
    	Box box2=Box.createHorizontalBox();
    	box2.add(lab2);
    	box2.add(Box.createHorizontalStrut(20));
    	box2.add(text2);
    	
    	
    }
}
