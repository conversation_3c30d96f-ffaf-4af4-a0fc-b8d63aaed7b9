package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.io.File;
import java.util.Calendar;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFileChooser;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.border.TitledBorder;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;

public class CzpCountDialog extends JDialog {
	private JTextField textField_1;//开始时间
	private JTextField textField_2;//结束时间
	private JComboBox  comboBox;//指标
	private JComboBox  comboBox_1;//周期
	private JComboBox  comboBox_2;//展现方式
	private JComboBox  comboBox_3;//导出选择器
	private JPanel panel_1;//图形显示面板
	
	private PieChart piechart;//初始化饼图的数据
	private TimeSeriesChart serieschart;//初始化折线图的数据
	private BarChart barchart;//初始化柱状图的数据
	private String index;//指标
	private String period;//周期
	
	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			CzpCountDialog dialog = new CzpCountDialog();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public CzpCountDialog() {
		init();
	}

	public CzpCountDialog(JFrame parent, boolean isModel) {
		super(parent, isModel);
		init();
	}
	/**
	 * Create the dialog.
	 */
	public  void init() {
		setTitle("操作票统计");
		setBounds(100, 100, 700, 580);
		getContentPane().setLayout(new BorderLayout(0, 0));
		{
			JPanel panel = new JPanel();
			panel.setBorder(new TitledBorder(null, "查询条件", TitledBorder.LEADING, TitledBorder.TOP, null, null));
			getContentPane().add(panel, BorderLayout.NORTH);
			GridBagLayout gbl_panel = new GridBagLayout();
			gbl_panel.columnWidths = new int[]{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
			gbl_panel.rowHeights = new int[]{0, 0, 0, 0};
			gbl_panel.columnWeights = new double[]{0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, Double.MIN_VALUE};
			gbl_panel.rowWeights = new double[]{0.0, 0.0, 1.0, Double.MIN_VALUE};
			panel.setLayout(gbl_panel);
			{
				JLabel lblNewLabel = new JLabel("开始时间");
				GridBagConstraints gbc_lblNewLabel = new GridBagConstraints();
				gbc_lblNewLabel.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel.anchor = GridBagConstraints.EAST;
				gbc_lblNewLabel.gridx = 0;
				gbc_lblNewLabel.gridy = 0;
				panel.add(lblNewLabel, gbc_lblNewLabel);
				
			}
			{
				textField_1 = new JTextField();
				GridBagConstraints gbc_textField_1 = new GridBagConstraints();
				gbc_textField_1.insets = new Insets(0, 0, 5, 5);
				gbc_textField_1.fill = GridBagConstraints.HORIZONTAL;
				gbc_textField_1.gridx = 1;
				gbc_textField_1.gridy = 0;
				panel.add(textField_1, gbc_textField_1);
				textField_1.setColumns(10);
				Calendar cal=Calendar.getInstance();    
			    int	y=cal.get(Calendar.YEAR);    
				int m=cal.get(Calendar.MONTH)+1;    
				int d=cal.get(Calendar.DATE);
				//String rq= "2013"+"-"+String.valueOf(m)+"-"+"1";
				String rq=String.valueOf(y)+"-"+String.valueOf(m)+"-"+"1";
				textField_1.setText(rq);
				textField_1.addMouseListener(new MouseListener() {		
					@Override
					public void mouseReleased(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mousePressed(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseExited(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseEntered(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseClicked(MouseEvent e) {
						// TODO Auto-generated method stub
					SelfCalendar calendar =new SelfCalendar(textField_1);
					}
				});
			}
			{
				JLabel lblNewLabel_2 = new JLabel("结束时间");
				GridBagConstraints gbc_lblNewLabel_2 = new GridBagConstraints();
				gbc_lblNewLabel_2.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_2.anchor = GridBagConstraints.WEST;
				gbc_lblNewLabel_2.gridx = 2;
				gbc_lblNewLabel_2.gridy = 0;
				panel.add(lblNewLabel_2, gbc_lblNewLabel_2);
			}
			{
				textField_2 = new JTextField();
				GridBagConstraints gbc_textField_2 = new GridBagConstraints();
				gbc_textField_2.insets = new Insets(0, 0, 5, 5);
				gbc_textField_2.fill = GridBagConstraints.HORIZONTAL;
				gbc_textField_2.gridx = 3;
				gbc_textField_2.gridy = 0;
				panel.add(textField_2, gbc_textField_2);
				textField_2.setColumns(10);
				Calendar cal=Calendar.getInstance();    
			    int	y=cal.get(Calendar.YEAR);    
				int m=cal.get(Calendar.MONTH)+1;    
				int d=cal.get(Calendar.DATE);
				String rq= String.valueOf(y)+"-"+String.valueOf(m)+"-"+String.valueOf(d);
				textField_2.setText(rq);
				textField_2.addMouseListener(new MouseListener() {		
					@Override
					public void mouseReleased(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mousePressed(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseExited(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseEntered(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseClicked(MouseEvent e) {
						// TODO Auto-generated method stub
					SelfCalendar calendar =new SelfCalendar(textField_2);
					}
				});	
			}
			{
				JLabel lblNewLabel_1 = new JLabel("指标");
				
				GridBagConstraints gbc_lblNewLabel_1 = new GridBagConstraints();
				gbc_lblNewLabel_1.anchor = GridBagConstraints.WEST;
				gbc_lblNewLabel_1.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_1.gridx = 0;
				gbc_lblNewLabel_1.gridy = 1;
				panel.add(lblNewLabel_1, gbc_lblNewLabel_1);
			}
			{
				 comboBox = new JComboBox();
				DefaultComboBoxModel dcbmzb=new DefaultComboBoxModel();
				CodeNameModel cnm1=new CodeNameModel("0","执行操作票数");
				CodeNameModel cnm2=new CodeNameModel("1","合格率");
				CodeNameModel cnm3=new CodeNameModel("2","停送电操作时间");
				dcbmzb.addElement(cnm1);
				dcbmzb.addElement(cnm2);
				dcbmzb.addElement(cnm3);
				comboBox.setModel(dcbmzb);
				GridBagConstraints gbc_comboBox = new GridBagConstraints();
				gbc_comboBox.insets = new Insets(0, 0, 5, 5);
				gbc_comboBox.fill = GridBagConstraints.HORIZONTAL;
				gbc_comboBox.gridx = 1;
				gbc_comboBox.gridy = 1;
				panel.add(comboBox, gbc_comboBox);
			}
			{
				JLabel lblNewLabel_3 = new JLabel("周期");
				GridBagConstraints gbc_lblNewLabel_3 = new GridBagConstraints();
				gbc_lblNewLabel_3.anchor = GridBagConstraints.WEST;
				gbc_lblNewLabel_3.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_3.gridx = 2;
				gbc_lblNewLabel_3.gridy = 1;
				panel.add(lblNewLabel_3, gbc_lblNewLabel_3);
			}
			{
				 comboBox_1 = new JComboBox();
				DefaultComboBoxModel dcbmzq=new DefaultComboBoxModel();
				CodeNameModel cnm1=new CodeNameModel("0","年");
				CodeNameModel cnm2=new CodeNameModel("1","月");
				CodeNameModel cnm3=new CodeNameModel("2","日");
				dcbmzq.addElement(cnm1);
				dcbmzq.addElement(cnm2);
				dcbmzq.addElement(cnm3);
				comboBox_1.setModel(dcbmzq);
				GridBagConstraints gbc_comboBox = new GridBagConstraints();
				gbc_comboBox.insets = new Insets(0, 0, 5, 5);
				gbc_comboBox.fill = GridBagConstraints.HORIZONTAL;
				gbc_comboBox.gridx = 3;
				gbc_comboBox.gridy = 1;
				panel.add(comboBox_1, gbc_comboBox);
			}
			{
				JLabel lblNewLabel_4 = new JLabel("展现方式");
				GridBagConstraints gbc_lblNewLabel_4 = new GridBagConstraints();
				gbc_lblNewLabel_4.anchor = GridBagConstraints.EAST;
				gbc_lblNewLabel_4.insets = new Insets(0, 0, 0, 5);
				gbc_lblNewLabel_4.gridx = 0;
				gbc_lblNewLabel_4.gridy = 2;
				panel.add(lblNewLabel_4, gbc_lblNewLabel_4);
			}
			{
				 comboBox_2 = new JComboBox();
				DefaultComboBoxModel dcbmzxfs=new DefaultComboBoxModel();
				CodeNameModel cnm1=new CodeNameModel("0","柱形图");
				CodeNameModel cnm2=new CodeNameModel("1","折线图");
				CodeNameModel cnm3=new CodeNameModel("2","饼图");
				dcbmzxfs.addElement(cnm1);
				dcbmzxfs.addElement(cnm2);
				dcbmzxfs.addElement(cnm3);
				comboBox_2.setModel(dcbmzxfs);
				GridBagConstraints gbc_comboBox = new GridBagConstraints();
				gbc_comboBox.insets = new Insets(0, 0, 0, 5);
				gbc_comboBox.fill = GridBagConstraints.HORIZONTAL;
				gbc_comboBox.gridx = 1;
				gbc_comboBox.gridy = 2;
				panel.add(comboBox_2, gbc_comboBox);
			}
			{
				JButton viewbutton = new JButton("查看");
				GridBagConstraints gbc_btnNewButton = new GridBagConstraints();
				gbc_btnNewButton.insets = new Insets(0, 0, 0, 5);
				gbc_btnNewButton.gridx = 2;
				gbc_btnNewButton.gridy = 2;
				panel.add(viewbutton, gbc_btnNewButton);
				viewbutton.addActionListener(new ActionListener(){
					 public void actionPerformed(ActionEvent e){
					    	viewbuttonactionPerformed(e);
					}
				});
			}
			{
				 comboBox_3 = new JComboBox();
				 DefaultComboBoxModel dcbmdc=new DefaultComboBoxModel();
			     CodeNameModel cnm1=new CodeNameModel("0","excel格式");
				 CodeNameModel cnm2=new CodeNameModel("1","pdf格式");
				 dcbmdc.addElement(cnm1);
				 dcbmdc.addElement(cnm2);
				 comboBox_3.setModel(dcbmdc);
				GridBagConstraints gbc_comboBox_3 = new GridBagConstraints();
				gbc_comboBox_3.fill = GridBagConstraints.HORIZONTAL;
				gbc_comboBox_3.insets = new Insets(0, 0, 0, 5);
				gbc_comboBox_3.gridx = 3;
				gbc_comboBox_3.gridy = 2;
				panel.add(comboBox_3, gbc_comboBox_3);
			}
			JButton exportbutton = new JButton("导出");
			GridBagConstraints gbc_exportbutton = new GridBagConstraints();
			gbc_exportbutton.insets = new Insets(0, 0, 0, 5);
			gbc_exportbutton.gridx = 4;
			gbc_exportbutton.gridy = 2;
			panel.add(exportbutton, gbc_exportbutton);
			exportbutton.addActionListener(new ActionListener(){
				 public void actionPerformed(ActionEvent e){
				    	exportbuttonactionPerformed(e);
				}
			});
		}
		{
			 panel_1 = new JPanel();
			getContentPane().add(panel_1, BorderLayout.CENTER);
		}
		InitCzpInfo();
		
	}
    protected void InitCzpInfo(){
    	String begtime= textField_1.getText();//开始时间
  		String endtime=textField_2.getText();//结束时间
    	panel_1.removeAll();
		 barchart=new BarChart(index,"日",begtime,endtime);
		panel_1.add(barchart.getChartPanel());
		panel_1.updateUI();
    }
	protected void exportbuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		String begtime= textField_1.getText();//开始时间
  		String endtime=textField_2.getText();//结束时间
		CodeNameModel dcfs = (CodeNameModel) comboBox_3.getSelectedItem();
		String geshi=dcfs.getName();
		CodeNameModel tx = (CodeNameModel) comboBox_2.getSelectedItem();
		String tulx=tx.getName();
		String str2 = null;//导出图像的格式
		String str1;//图形的显示方式
		str1=begtime+"----"+endtime+index+period+tulx;
		if(geshi.equals("pdf格式"))
		{
			 str2="pdf";
		}
		if(geshi.equals("excel格式"))
		{
			 str2="xls";
		}
		JFileChooser jf = new JFileChooser();
        jf.setFileSelectionMode(JFileChooser.SAVE_DIALOG | JFileChooser.DIRECTORIES_ONLY);
        jf.showDialog(null,null);
        File fi = jf.getSelectedFile();
        String fpho=fi.getAbsolutePath()+"\\"+str1+".jpeg";
        String fxls = fi.getAbsolutePath()+"\\"+str1+"."+str2+"";
        System.out.println("save: "+fxls);
        if(tulx.equals("饼图"))
        {
        piechart.exportexcel(fpho,fxls);
        System.out.println("save0=== fpho: "+fpho);
        System.out.println("save1====fxls: "+fxls);
        }
        if(tulx.equals("柱形图"))
        {
          barchart.exportexcel(fpho, fxls);
        }
        if(tulx.equals("折线图"))
        {
        	serieschart.exportexcel(fpho, fxls);
        }
		
	}

	protected void viewbuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		String begtime= textField_1.getText();//开始时间
  		String endtime=textField_2.getText();//结束时间
		CodeNameModel czpzb=  (CodeNameModel) comboBox.getSelectedItem();
		 index=czpzb.getName();//获取选中的指标名称
		CodeNameModel czpzq=  (CodeNameModel) comboBox_1.getSelectedItem();
		 period=czpzq.getName();//获取选中周期名称
		//计算相差时间用来提示用户：按日查询则相差时间需大于一，按月查询则相差时间需大于30，按年查询则相差时间大于365
//		DateFormat df = new SimpleDateFormat("yyyy-MM-dd ");
//
//		try
//		{
//		Date d1 = df.parse(endtime);
//		Date d2 = df.parse(begtime);
//		long diff = d1.getTime() - d2.getTime();
//		long days = diff / (1000 * 60 * 60 * 24);
//		System.out.println(days+"=======相差的时间=======");
//		}
//		catch (Exception ex)
//		{
//		}
		if(begtime!=null&&!begtime.equals("")&&endtime!=null&&!endtime.equals(""))
		{
		
		
		
		if(comboBox_2.getSelectedIndex()==0)
		{
			panel_1.removeAll();
			 barchart=new BarChart(index,period,begtime,endtime);
			panel_1.add(barchart.getChartPanel());
			panel_1.updateUI();
		}
		if(comboBox_2.getSelectedIndex()==1)
		{
			panel_1.removeAll();
			 serieschart =new TimeSeriesChart(index,period,begtime,endtime);
			panel_1.add(serieschart.getChartPanel());
			panel_1.updateUI();
		}
		if(comboBox_2.getSelectedIndex()==2)
		{
			panel_1.removeAll();
			 piechart =new PieChart(index,period,begtime,endtime);
			panel_1.add(piechart.getChartPanel());
			panel_1.updateUI();
		}
		
		
		
		}
		else 
		{
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "开始或结束时间不能为空");
		}
	}

}
