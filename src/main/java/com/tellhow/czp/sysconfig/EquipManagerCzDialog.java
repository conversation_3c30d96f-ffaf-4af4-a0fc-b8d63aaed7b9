package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JScrollPane;
import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class EquipManagerCzDialog extends JDialog {
	private javax.swing.JTree tree;
	private DefaultMutableTreeNode root;
	private javax.swing.JScrollPane scrollPane;
	private Map temp;
	private String code,cardtype;
	private EquipOperationManageDialog eomd;
	
	public EquipManagerCzDialog(EquipOperationManageDialog eomd, String code,final JFrame jFrame, boolean modal,String cardtype) {
		super(jFrame, modal);
		this.setSize(240, 300);
		this.setLocationRelativeTo(null);
		this.eomd=eomd;
		this.code=code;
		this.cardtype=cardtype;
		scrollPane = new JScrollPane();
		getContentPane().add(scrollPane, BorderLayout.CENTER);
		EquipTable czcd=new EquipTable("-1","操作分类","0","0","-1","0","0"," "," ",cardtype);
		root=new DefaultMutableTreeNode(czcd);
		tree = new JTree(root);
		inittree(code,cardtype);
		tree.addMouseListener(new TreeMouseListener(this,eomd,cardtype));
		scrollPane.setViewportView(tree);
	}
	
	private void inittree(String code,String cardtype){
		root.removeAllChildren();
		List more = DBManager.query("select statecode,statename,statevalue,devicetypeid,parentcode,statetype,stateorder,secondtypeid ,secondstate,cardbuildtype from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and  opcode='"+CBSystemConstants.opCode+"' and devicetypeid='"+code+"' and parentcode='0' and statetype=1 and cardbuildtype='"+cardtype+"' order by stateorder asc ");
		temp=new HashMap();
		EquipTable equiptable=new EquipTable("0","主目录","1","-1",code,"0","0"," "," ",cardtype);
		DefaultMutableTreeNode zhunode = new  DefaultMutableTreeNode( equiptable) ;
		root.add(zhunode);
		for(int i = 0; i < more.size(); i++){
			temp = (Map) more.get(i);
			String statecode=StringUtils.ObjToString(temp.get("statecode"));
			String statename=StringUtils.ObjToString(temp.get("statename"));
			String statetype=StringUtils.ObjToString(temp.get("statetype"));
			String statevalue=StringUtils.ObjToString(temp.get("statevalue"));
			String devicetypeid=StringUtils.ObjToString(temp.get("devicetypeid"));
			String parentcode=StringUtils.ObjToString(temp.get("parentcode"));
			String stateorder=StringUtils.ObjToString(temp.get("stateorder"));
			String secondtypeid=StringUtils.ObjToString(temp.get("secondtypeid"));
			String secondstate=StringUtils.ObjToString(temp.get("secondstate"));
			String cardbuildtype=StringUtils.ObjToString(temp.get("cardbuildtype"));
			EquipTable equiptable0=new EquipTable(statecode,statename,statetype,statevalue,devicetypeid,parentcode,stateorder,secondtypeid,secondstate,cardbuildtype);
			DefaultMutableTreeNode morenode = new  DefaultMutableTreeNode( equiptable0) ;
			root.add(morenode);
		}
		tree.updateUI();
		tree.repaint();
	}
	
	public void shuaxin(String code,String cardtype){
		this.code=code;
		this.cardtype=cardtype;
		inittree(code,cardtype);
	}
	
	class TreeMouseListener extends MouseAdapter {
		private EquipOperationManageDialog eomd;
		private EquipManagerCzDialog emcd;
		private String cardtype;
		public TreeMouseListener(EquipManagerCzDialog emcd,EquipOperationManageDialog eomd,String cardtype) {
			this.emcd = emcd;
			this.eomd=eomd;
			this.cardtype=cardtype;
		}
		@Override
		public void mouseClicked(MouseEvent e) {
			if(e.getButton()==e.BUTTON1){
				DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
				EquipTable et=new EquipTable();
				et=(EquipTable)path.getUserObject();
				EquipManagerDialogC drm=new EquipManagerDialogC(et,code,eomd,emcd, SystemConstants.getMainFrame(),true,cardtype);
				drm.addBtnNewButtonListenerxz();
				drm.setVisible(true);
			}
			dispose();
		}
	}
	
}
