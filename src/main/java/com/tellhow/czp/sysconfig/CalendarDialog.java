/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：
* 功能说明 : 时间选择对话框
* 作    者 : 邹力兴
* 开发日期 : 2008-10-7
* 修改日期 ：
* 修改说明 ：
* 修 改 人 ：
**/

package com.tellhow.czp.sysconfig;

import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import javax.swing.*;
import javax.swing.table.*;

/**
 *
 * <AUTHOR>
 */
public class CalendarDialog extends javax.swing.JDialog {
    
    public CalendarDialog(JDialog parent,boolean model) {
        super(parent,model);
        setTitle("时间选择");
        initComponents();
        this.setLocation(400, 300);
        initECalendarForm();
    }
    /** This method is called from within the constructor to
     * initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is
     * always regenerated by the Form Editor.
     */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        sub_year_b = new javax.swing.JButton();
        add_year_b = new javax.swing.JButton();
        sub_month_b = new javax.swing.JButton();
        add_month_b = new javax.swing.JButton();
        year_lab = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        month_lab = new javax.swing.JLabel();
        jLabel4 = new javax.swing.JLabel();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        jLabel5 = new javax.swing.JLabel();
        today_lab = new javax.swing.JLabel();
        hour_sp = new javax.swing.JSpinner();
        jLabel1 = new javax.swing.JLabel();
        minute_sp = new javax.swing.JSpinner();
        jLabel3 = new javax.swing.JLabel();
        second_sp = new javax.swing.JSpinner();
        jLabel6 = new javax.swing.JLabel();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
        setResizable(false);

        sub_year_b.setText("<<");
        sub_year_b.addMouseListener(new MouseAdapter() {
            Timer timer = new Timer();
            TimerTask task = null;
            public void mousePressed(MouseEvent e) {
                task = new TimerTask() {
                    public void run() {
                        changeECalendar(3);
                    }
                };
                timer.scheduleAtFixedRate(task, 0, 200);
            }
            public void mouseReleased(MouseEvent e) {
                task.cancel();
                task = null;
            }
        });

        add_year_b.setText(">>");
        add_year_b.addMouseListener(new MouseAdapter() {
            Timer timer = new Timer();
            TimerTask task = null;
            public void mousePressed(MouseEvent e) {
                task = new TimerTask() {
                    public void run() {
                        changeECalendar(4);
                    }
                };
                timer.scheduleAtFixedRate(task, 0, 200);
            }
            public void mouseReleased(MouseEvent e) {
                task.cancel();
                task = null;
            }
        });

        sub_month_b.setText("<<");
        sub_month_b.addMouseListener(new MouseAdapter() {
            Timer timer = new Timer();
            TimerTask task = null;
            public void mousePressed(MouseEvent e) {
                task = new TimerTask() {
                    public void run() {
                        changeECalendar(1);
                    }
                };
                timer.scheduleAtFixedRate(task, 0, 200);
            }
            public void mouseReleased(MouseEvent e) {
                task.cancel();
                task = null;
            }
        });

        add_month_b.setText(">>");
        add_month_b.addMouseListener(new MouseAdapter() {
            Timer timer = new Timer();
            TimerTask task = null;
            public void mousePressed(MouseEvent e) {
                task = new TimerTask() {
                    public void run() {
                        changeECalendar(2);
                    }
                };
                timer.scheduleAtFixedRate(task, 0, 200);
            }
            public void mouseReleased(MouseEvent e) {
                task.cancel();
                task = null;
            }
        });

        year_lab.setText("2008");

        jLabel2.setText("年");

        month_lab.setText("1");

        jLabel4.setText("月");

        dtm=new javax.swing.table.DefaultTableModel(
            new Object [6][7],
            new String[] {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"}
        ){
            boolean[] canEdit = new boolean [] {
                false, false, false, false, false, false, false
            };

            public boolean isCellEditable(int rowIndex, int columnIndex) {
                return canEdit [columnIndex];
            }
        };
        jTable1.setModel(dtm);
        jTable1.setRowSelectionAllowed(false);
        jTable1.getTableHeader().setReorderingAllowed(false);
        jTable1.setSelectionForeground(new java.awt.Color(255, 51, 51));
        jTable1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                jTable1MouseClicked(evt);
            }
        });
        jTable1.setShowHorizontalLines(false);
        jTable1.setShowVerticalLines(false);
        jScrollPane1.setViewportView(jTable1);

        jLabel5.setText("当前时间");

        today_lab.setText("2008-1-1");

        hour_sp.addChangeListener(new javax.swing.event.ChangeListener() {
            public void stateChanged(javax.swing.event.ChangeEvent evt) {
                hour_spStateChanged(evt);
            }
        });

        jLabel1.setText("时");

        minute_sp.addChangeListener(new javax.swing.event.ChangeListener() {
            public void stateChanged(javax.swing.event.ChangeEvent evt) {
                minute_spStateChanged(evt);
            }
        });

        jLabel3.setText("分");

        second_sp.addChangeListener(new javax.swing.event.ChangeListener() {
            public void stateChanged(javax.swing.event.ChangeEvent evt) {
                second_spStateChanged(evt);
            }
        });

        jLabel6.setText("秒");

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .add(sub_year_b)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(add_year_b)
                .add(55, 55, 55)
                .add(year_lab)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jLabel2)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(month_lab)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jLabel4)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED, 49, Short.MAX_VALUE)
                .add(sub_month_b)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(add_month_b))
            .add(jScrollPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 386, Short.MAX_VALUE)
            .add(layout.createSequentialGroup()
                .addContainerGap()
                .add(jLabel5)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(today_lab)
                .add(61, 61, 61)
                .add(hour_sp, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 39, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jLabel1)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(minute_sp, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 38, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jLabel3)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(second_sp, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 41, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jLabel6)
                .add(41, 41, 41))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                    .add(sub_year_b)
                    .add(add_year_b)
                    .add(add_month_b)
                    .add(year_lab)
                    .add(jLabel2)
                    .add(month_lab)
                    .add(jLabel4)
                    .add(sub_month_b))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jScrollPane1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 117, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                    .add(jLabel5)
                    .add(today_lab)
                    .add(hour_sp, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                    .add(jLabel1)
                    .add(minute_sp, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                    .add(jLabel3)
                    .add(second_sp, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                    .add(jLabel6))
                .addContainerGap())
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents
    public String showCalendar(){
        setVisible(true);
        return selectDate;
    }
    private void hour_spStateChanged(javax.swing.event.ChangeEvent evt) {//GEN-FIRST:event_hour_spStateChanged
        // TODO add your handling code here:
        Integer k=(Integer)hour_sp.getValue();
        if(k>=24){
            k=0;
        }else if(k<0){
            k=23;
        }
        hour_sp.setValue(k);
    }//GEN-LAST:event_hour_spStateChanged

    private void minute_spStateChanged(javax.swing.event.ChangeEvent evt) {//GEN-FIRST:event_minute_spStateChanged
        // TODO add your handling code here:
        Integer k=(Integer)minute_sp.getValue();
        if(k>=60){
            k=0;
        }else if(k<0){
            k=59;
        }
        minute_sp.setValue(k);
    }//GEN-LAST:event_minute_spStateChanged

    private void second_spStateChanged(javax.swing.event.ChangeEvent evt) {//GEN-FIRST:event_second_spStateChanged
        // TODO add your handling code here:
        Integer k=(Integer)second_sp.getValue();
        if(k>=60){
            k=0;
        }else if(k<0){
            k=59;
        }
        second_sp.setValue(k);
    }//GEN-LAST:event_second_spStateChanged
    
    private void initECalendarForm(){
        Calendar ec = new Calendar();
        year_lab.setText(ec.getYearTime());
        month_lab.setText(ec.getMonthTime());
        today_lab.setText(ec.getTadayTime());
        hour_sp.setValue(ec.getHourTime());
        minute_sp.setValue(ec.getMinuteTime());
        second_sp.setValue(ec.getSecondTime());
        
        this.setdate(Integer.parseInt(year_lab.getText()), Integer.parseInt(month_lab.getText()));
    }
    private void changeECalendar(int tag){
        Integer year=0,month=0;
        year=Integer.parseInt(year_lab.getText());
        month=Integer.parseInt(month_lab.getText());
        if(tag==1){
            month--;
            if(month<1){
                year--;
                month=12;
            }
        }
        if(tag==2){
            month++;
            if(month>12){
                year++;
                month=1;
            }
        }
        if(tag==3){
            year--;
        }
        if(tag==4){
            year++;
        }
        year_lab.setText(year.toString());
        month_lab.setText(month.toString());
        this.setdate(year, month);
    }
    private void setdate(int year,int month){
        if(month==1||month==3||month==5||month==7||month==8||month==10||month==12){
            this.setTableValue(year, month, 31);
        }else if(month==2){
            if(Calendar.isRYear(year)){
                this.setTableValue(year, month, 29);
            }
            else{
                this.setTableValue(year, month, 28);
            }
        }else{
            this.setTableValue(year, month, 30);
        }
    }
    private void setTableValue(int year,int month,int type){
        Date date = new Date(year-1900, month-1, 1);
        int day = 0;
        int j = date.getDay();
        for (int a = 0; a < 6; a++){for (int b = 0; b < 7; b++) {dtm.setValueAt("", a, b);} }
        for (int i = 0; i < 6; i++) {
            for (; j < 7; j++) {
                dtm.setValueAt(++day, i, j);
                if (day == type) { break;}
            }
            j = 0;
            if (day == type) { break; }
        }
    }
    public void jTable1MouseClicked(java.awt.event.MouseEvent evt) {                                     
        // TODO add your handling code here:
        String day="";
        if(evt.getButton()==1&&evt.getClickCount()==2){
            day = jTable1.getValueAt(jTable1.getSelectedRow(), jTable1.getSelectedColumn()).toString();
            selectDate=year_lab.getText()+"-"+month_lab.getText()+"-"+day+" "+hour_sp.getValue()+":"+minute_sp.getValue()+":"+second_sp.getValue();
            this.setVisible(false);
            this.dispose();
        }
    }
    
    private String selectDate="";
    private javax.swing.table.DefaultTableModel dtm;
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton add_month_b;
    private javax.swing.JButton add_year_b;
    private javax.swing.JSpinner hour_sp;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JLabel jLabel6;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTable jTable1;
    private javax.swing.JSpinner minute_sp;
    private javax.swing.JLabel month_lab;
    private javax.swing.JSpinner second_sp;
    private javax.swing.JButton sub_month_b;
    private javax.swing.JButton sub_year_b;
    private javax.swing.JLabel today_lab;
    private javax.swing.JLabel year_lab;
    // End of variables declaration//GEN-END:variables
    
}
