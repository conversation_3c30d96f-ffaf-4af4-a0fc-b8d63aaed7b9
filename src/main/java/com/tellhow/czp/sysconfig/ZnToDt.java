package com.tellhow.czp.sysconfig;


public class ZnToDt {
	public static void main(String[] args) {
		
		//点图开票
//		String sql="select * from "+CBSystemConstants.opcardUser+"t_a_rulezb where cardbuildtype='2'";
//		List result=DBManager.queryForList(sql);
//		for(int i=0;i<result.size();i++){
//			Map temp=(Map)result.get(i);
//			String old_zbid=StringUtils.ObjToString(temp.get("zbid"));
//			String zbid=StringUtils.getUUID();
//			String devicetypeid=StringUtils.ObjToString(temp.get("devicetypeid"));
//			String devicerunmodel=StringUtils.ObjToString(temp.get("devicerunmodel"));
//			String beginstatus=StringUtils.ObjToString(temp.get("beginstatus"));
//			String endstate=StringUtils.ObjToString(temp.get("endstate"));
//			//状态
//			String statesql="select * from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where statecode='"+endstate+"'";
//			List stateresult=DBManager.queryForList(statesql);
//			Map state=(Map)stateresult.get(0);
//			String statename=StringUtils.ObjToString(state.get("statename"));
//			String stateunitcode=StringUtils.ObjToString(state.get("unitcode"));
//			String statedevicetypeid=StringUtils.ObjToString(state.get("devicetypeid"));
//			String statesqllast="select * from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where statename='"+statename+"' and unitcode='"+stateunitcode+"' and devicetypeid='"+statedevicetypeid+"' and cardbuildtype='1'";
//			List stateresultlast=DBManager.queryForList(statesqllast);
//			String endstatelast;
//			if(stateresultlast.size()>0){
//				Map statelast=(Map) stateresultlast.get(0);
//				endstatelast=StringUtils.ObjToString(statelast.get("statecode"));
//			}else{
//				continue;
//			}
//			//
//			String volt=StringUtils.ObjToString(temp.get("volt"));
//			String unitcode=StringUtils.ObjToString(temp.get("unitcode"));
//			String old_cardbuildtype=StringUtils.ObjToString(temp.get("cardbuildtype"));
//			String cardbuildtype="1";
//			String cbsql="select * from "+CBSystemConstants.opcardUser+"t_a_rulecb where f_zbid='"+old_zbid+"'";
//			List cbresult=DBManager.queryForList(cbsql);
//			for(int j=0;j<cbresult.size();j++){
//				Map cbtemp=(Map) cbresult.get(j);
//				String cbf_zbid=StringUtils.ObjToString(cbtemp.get("f_zbid"));
//				String cbruleid=StringUtils.ObjToString(cbtemp.get("ruleid"));
//				String cbbeginstatus=StringUtils.ObjToString(cbtemp.get("beginstatus"));
//				String cbendstate=StringUtils.ObjToString(cbtemp.get("endstate"));
//				String cborderid=StringUtils.ObjToString(cbtemp.get("orderid"));
//				String cbtrantype=StringUtils.ObjToString(cbtemp.get("trantype"));
//				String cbdeviceruntype=StringUtils.ObjToString(cbtemp.get("deviceruntype"));
//				String insertcb="insert into "+CBSystemConstants.opcardUser+"t_a_rulecb (f_zbid,ruleid,beginstatus,endstate,orderid,trantype,deviceruntype) values ('"+zbid+"','"+cbruleid+"','"+cbbeginstatus+"','"+cbendstate+"','"+cborderid+"','"+cbtrantype+"','"+cbdeviceruntype+"') ";
//				DBManager.execute(insertcb);
//				System.out.println("2");
//			}
//			String inert="insert into "+CBSystemConstants.opcardUser+"t_a_rulezb (zbid,devicetypeid,devicerunmodel,beginstatus,endstate,volt,unitcode,cardbuildtype) values ('"+zbid+"','"+devicetypeid+"','"+devicerunmodel+"','"+beginstatus+"','"+endstatelast+"','"+volt+"','"+unitcode+"','"+cardbuildtype+"')";
//			DBManager.execute(inert);
//			System.out.println("1");
//		}
//		int n=0;
//		System.out.println("0");
	}
	
	
	
	
	
	
	
	
	
	
	//设备对位
//	public static void main(String[] args) {
//		String sql="select * from "+CBSystemConstants.opcardUser+"t_a_rulezb ";
//		List result=DBManager.queryForList(sql);
//		for(int i=0;i<result.size();i++){
//			Map temp=(Map)result.get(i);
//			String old_zbid=StringUtils.ObjToString(temp.get("zbid"));
//			String zbid=StringUtils.getUUID();
//			String devicetypeid=StringUtils.ObjToString(temp.get("devicetypeid"));
//			String devicerunmodel=StringUtils.ObjToString(temp.get("devicerunmodel"));
//			String beginstatus=StringUtils.ObjToString(temp.get("beginstatus"));
//			String endstate=StringUtils.ObjToString(temp.get("endstate"));
//			//状态
//			String statesql="select * from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where statecode='"+endstate+"'";
//			List stateresult=DBManager.queryForList(statesql);
//			Map state=(Map)stateresult.get(0);
//			String statename=StringUtils.ObjToString(state.get("statename"));
//			String stateunitcode=StringUtils.ObjToString(state.get("unitcode"));
//			String statedevicetypeid=StringUtils.ObjToString(state.get("devicetypeid"));
//			String statesqllast="select * from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where statename='"+statename+"' and unitcode='"+stateunitcode+"' and devicetypeid='"+statedevicetypeid+"' and cardbuildtype='2'";
//			List stateresultlast=DBManager.queryForList(statesqllast);
//			String endstatelast;
//			if(stateresultlast.size()>0){
//				Map statelast=(Map) stateresultlast.get(0);
//				endstatelast=StringUtils.ObjToString(statelast.get("statecode"));
//			}else{
//				continue;
//			}
//			//
//			String volt=StringUtils.ObjToString(temp.get("volt"));
//			String unitcode=StringUtils.ObjToString(temp.get("unitcode"));
//			String old_cardbuildtype=StringUtils.ObjToString(temp.get("cardbuildtype"));
//			String cardbuildtype="2";
//			String cbsql="select * from "+CBSystemConstants.opcardUser+"t_a_rulecb where f_zbid='"+old_zbid+"'";
//			List cbresult=DBManager.queryForList(cbsql);
//			for(int j=0;j<cbresult.size();j++){
//				Map cbtemp=(Map) cbresult.get(j);
//				String cbf_zbid=StringUtils.ObjToString(cbtemp.get("f_zbid"));
//				String cbruleid=StringUtils.ObjToString(cbtemp.get("ruleid"));
//				String cbbeginstatus=StringUtils.ObjToString(cbtemp.get("beginstatus"));
//				String cbendstate=StringUtils.ObjToString(cbtemp.get("endstate"));
//				String cborderid=StringUtils.ObjToString(cbtemp.get("orderid"));
//				String cbtrantype=StringUtils.ObjToString(cbtemp.get("trantype"));
//				String cbdeviceruntype=StringUtils.ObjToString(cbtemp.get("deviceruntype"));
//				String insertcb="insert into "+CBSystemConstants.opcardUser+"t_a_rulecb (f_zbid,ruleid,beginstatus,endstate,orderid,trantype,deviceruntype) values ('"+zbid+"','"+cbruleid+"','"+cbbeginstatus+"','"+cbendstate+"','"+cborderid+"','"+cbtrantype+"','"+cbdeviceruntype+"') ";
//				DBManager.execute(insertcb);
//				System.out.println("2");
//			}
//			String inert="insert into "+CBSystemConstants.opcardUser+"t_a_rulezb (zbid,devicetypeid,devicerunmodel,beginstatus,endstate,volt,unitcode,cardbuildtype) values ('"+zbid+"','"+devicetypeid+"','"+devicerunmodel+"','"+beginstatus+"','"+endstatelast+"','"+volt+"','"+unitcode+"','"+cardbuildtype+"')";
//			DBManager.execute(inert);
//			System.out.println("1");
//		}
//		int n=0;
//		System.out.println("0");
//	}
}
