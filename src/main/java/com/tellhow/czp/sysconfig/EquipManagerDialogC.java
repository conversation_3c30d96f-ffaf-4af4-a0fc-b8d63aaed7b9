package com.tellhow.czp.sysconfig;

import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JRadioButton;
import javax.swing.JTextField;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.mainframe.MultiComboBox;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

public class EquipManagerDialogC extends JDialog {

	private final JPanel contentPanel = new JPanel();
	private JRadioButton rdbtnNewRadioButton,rdbtnNewRadioButton_1,rdbtnNewRadioButton_2;
	private JButton btnNewButton,btnNewButton_1;
	private JLabel lblNewLabel,lblNewLabel_1,lblNewLabel_2,lblNewLabel_3,lblNewLabel_4,lblNewLabel_5,lblNewLabel_6,lblNewLabel_7;
	private EquipOperationManageDialog eomd=null;
	private EquipManagerCzDialog emcd=null;
	private String statecode;
	private Map temp,ctemp1,ctemp2,ctemp3,ctemp4,ctemp0,checktemp1,ckecktemp,hastemp,secondhastemp,borntemp,firsttemp,com4temp,com1temp;
	private JTextField textField;
	private JComboBox comboBox_1,comboBox_2,comboBox_4,comboBox;
	private MultiComboBox comboBox_3;
	private String code,cardtype;
	private EquipTable et;
	private StringBuffer statevalueend,secondvalueend;
	private List <Map> usemap;
	private List <JCheckBox> jcbx,jcbxb;
	private JPanel panel;
	

	
	/**
	 * @wbp.parser.constructor
	 */
	public EquipManagerDialogC( String code,EquipOperationManageDialog eomd, final JFrame jFrame, boolean modal, String statecode,String cardtype) {
		super(jFrame, modal);
		this.setAlwaysOnTop(true);
		getContentPane().setLayout(null);
		initradiobutton();
		this.code=code;
		this.statecode = statecode;
		this.cardtype=cardtype;
		this.eomd = eomd;
		initBtnNewButton();
		initlbltext();
		initcomboxxg();
		this.setLocationCenter();
	}

	public EquipManagerDialogC(EquipTable et,String code,EquipOperationManageDialog eomd,EquipManagerCzDialog emcd,final JFrame jFrame, boolean modal,String cardtype){
		super(jFrame, modal);
		this.setAlwaysOnTop(true);
		getContentPane().setLayout(null);
		this.et=et;
		this.code=code;
		this.eomd=eomd;
		this.emcd=emcd;
		this.cardtype=cardtype;
		initradiobutton();
		initlbltext();
		initcomboxxz();
		initBtnNewButton();
		this.setLocationCenter();
	}
	
	public EquipManagerDialogC(EquipTable et,String code,EquipOperationManageDialog eomd,final JFrame jFrame, boolean modal,String cardtype){
		super(jFrame, modal);
		this.setAlwaysOnTop(true);
		getContentPane().setLayout(null);
		this.et=et;
		this.code=code;
		this.eomd=eomd;
		this.cardtype=cardtype;
		initradiobutton();
		initlbltext();
		initcomboxxz();
		initBtnNewButton();
		this.setLocationCenter();
	}
	
	private void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,(h - this.getSize().height) / 2);
		this.setBounds( 400, 200,683, 464);
	}

	private void initradiobutton(){
		rdbtnNewRadioButton = new JRadioButton("操作",true);
		rdbtnNewRadioButton.setBounds(106, 27, 121, 23);
		rdbtnNewRadioButton.setEnabled(false);
		getContentPane().add(rdbtnNewRadioButton);
		
		rdbtnNewRadioButton_1 = new JRadioButton("分类");
		rdbtnNewRadioButton_1.setBounds(287, 27, 121, 23);
		rdbtnNewRadioButton_1.setEnabled(false);
		getContentPane().add(rdbtnNewRadioButton_1);
		
		rdbtnNewRadioButton_2 = new JRadioButton("分割线");
		rdbtnNewRadioButton_2.setBounds(484, 27, 121, 23);
		rdbtnNewRadioButton_2.setEnabled(false);
		getContentPane().add(rdbtnNewRadioButton_2);
	}
	
	private void initBtnNewButton() {
		btnNewButton = new JButton("确定");
		btnNewButton.setBounds(175, 392, 131, 23);
		getContentPane().add(btnNewButton);
		
		btnNewButton_1 = new JButton("取消");
		btnNewButton_1.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent arg0) {
				dispose();
			}
		});
		btnNewButton_1.setBounds(408, 392, 131, 23);
		getContentPane().add(btnNewButton_1);
	}
	
	private void initcomboxxz(){
		textField = new JTextField();
		textField.setBounds(408, 78, 151, 21);
		getContentPane().add(textField);
		textField.setColumns(10);
		
		
//		String sql ="select equiptype_id from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='"+code+"'";
		//2014.6.25
		String sql =OPEService.getService().EquipManagerDialogSql()+code+"'";
		checktemp1 = new HashMap();
		List checkb = DBManager.query(sql);
		checktemp1=(Map)checkb.get(0);
		String equiptype_id=StringUtils.ObjToString(checktemp1.get("equiptype_id"));
//		String sqll="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='"+equiptype_id+"'";
		//edit 2014.6.25
		String sqll=OPEService.getService().EquipManagerDialogSql1()+code+"'";
		ckecktemp = new HashMap();
		List checkboxs=DBManager.query(sqll);
		jcbx=new ArrayList<JCheckBox>();
		for(int i=0;i<checkboxs.size();i++){
			ckecktemp=(Map) checkboxs.get(i);
			String state_name=StringUtils.ObjToString(ckecktemp.get("state_name"));
			String state_code=StringUtils.ObjToString(ckecktemp.get("state_code"));
			JCheckBox jcb=new JCheckBox(state_name);
			jcb.setName(state_code);
			jcb.setBounds(250+80*i, 117, 79, 21);
			getContentPane().add(jcb);
			jcbx.add(jcb);
		}
		
		comboBox_1 = new JComboBox();
		comboBox_1.setBounds(408, 187, 151, 21);
		comboBox_1.setModel(combox1(""));
		getContentPane().add(comboBox_1);
		
		comboBox_2 = new JComboBox();
		comboBox_2.setBounds(408, 223, 151, 21);
		comboBox_2.setModel(combox2(""));
		getContentPane().add(comboBox_2);
		
		comboBox_3 = new MultiComboBox(combox3(""), new String[]{});
		comboBox_3.setBounds(408, 256, 151, 21);
		getContentPane().add(comboBox_3);
		
		comboBox_4 = new JComboBox();
		comboBox_4.setBounds(408, 293, 151, 21);
		comboBox_4.setModel(combox4(""));
		comboBox_4.addItemListener(new CheckBoxListener(this));
//		getContentPane().add(comboBox_4);
		
		comboBox = new JComboBox();
		comboBox.setBounds(408, 151, 151, 21);
		comboBox.setModel(combox0(""));
		getContentPane().add(comboBox);
		
		panel = new JPanel();
		panel.setBounds(316, 326, 341, 26);
		getContentPane().add(panel);
	}
	
	private void initcomboxxg(){
		textField = new JTextField(initContext());
		textField.setBounds(408, 78, 151, 21);
		getContentPane().add(textField);
		textField.setColumns(10);
		
		
//		String sql ="select equiptype_id from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='"+code+"'";
		//edit 2014.6.25
		String sql =OPEService.getService().EquipManagerDialogSql2()+code+"'";
		checktemp1 = new HashMap();
		List checkb = DBManager.query(sql);
		checktemp1=(Map)checkb.get(0);
		String equiptype_id=StringUtils.ObjToString(checktemp1.get("equiptype_id"));
//		String sqll="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='"+equiptype_id+"'";
		//edit 2014.6.25
		String sqll=OPEService.getService().EquipManagerDialogSql3()+code+"'";
		ckecktemp = new HashMap();
		List checkboxs=DBManager.query(sqll);
		String sqlll="select operatecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and statecode='"+statecode+"'";
		List hasselect=DBManager.query(sqlll);
		hastemp = new HashMap();
		hastemp=(Map) hasselect.get(0);
		String nowselectcode=StringUtils.ObjToString(hastemp.get("operatecode"));
		String arr[]=nowselectcode.split(",");
		jcbx=new ArrayList<JCheckBox>();
		for(int i=0;i<checkboxs.size();i++){
			ckecktemp=(Map) checkboxs.get(i);
			String state_name=StringUtils.ObjToString(ckecktemp.get("state_name"));
			String state_code=StringUtils.ObjToString(ckecktemp.get("state_code"));
			JCheckBox jcb=new JCheckBox(state_name);
			for (int j = 0; j < arr.length; j++) {
				String string = arr[j];
				if(string.equals(state_code)){
					jcb.setSelected(true);
				}
			}
			jcb.setName(state_code);
			jcb.setBounds(250+80*i, 117, 79, 21);
			getContentPane().add(jcb);
			jcbx.add(jcb);
		}
		
		
		comboBox_1 = new JComboBox();
		comboBox_1.setBounds(408, 187, 151, 21);
		List resultss = DBManager.query("select statecode, runmodel  from  "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and  statecode='"+statecode+"'");
		ctemp1=new HashMap();
		ctemp1=(Map) resultss.get(0);
		String nowselectcodes=StringUtils.ObjToString(ctemp1.get("runmodel"));
		comboBox_1.setModel(combox1(nowselectcodes));
		getContentPane().add(comboBox_1);
		
		comboBox_2 = new JComboBox();
		comboBox_2.setBounds(408, 223, 151, 21);
		List resultsss = DBManager.query("select statecode, hasside  from  "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and  statecode='"+statecode+"'");
		ctemp2=new HashMap();
		ctemp2=(Map) resultsss.get(0);	
		String nowselectcodess=StringUtils.ObjToString(ctemp2.get("hasside"));
		comboBox_2.setModel(combox2(nowselectcodess));
		getContentPane().add(comboBox_2);
		
		
		List resultssss = DBManager.query("select statecode, runtype  from  "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and  statecode='"+statecode+"'");
		ctemp3=new HashMap();
		ctemp3=(Map) resultssss.get(0);
		String nowselectcodesss=StringUtils.ObjToString(ctemp3.get("runtype"));
		
		if(!nowselectcodesss.equals(""))
			comboBox_3 = new MultiComboBox(combox3(""), combox3(nowselectcodesss));
		else
			comboBox_3 = new MultiComboBox(combox3(""), new String[]{});
		comboBox_3.setBounds(408, 256, 151, 21);
		getContentPane().add(comboBox_3);
		
		comboBox_4 = new JComboBox();
		comboBox_4.setBounds(408, 293, 151, 21);
		List checksecond = DBManager.query("select statecode,secondtypeid from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and statecode='"+statecode+"'");
		ctemp4=new HashMap(); 
		ctemp4=(Map) checksecond.get(0);
		String checksecondd=StringUtils.ObjToString(ctemp4.get("secondtypeid"));
		if(!checksecondd.equals("")){
			comboBox_4.setModel(combox4(checksecondd));
		}else{
			comboBox_4.setModel(combox4(""));
		}
		comboBox_4.addItemListener(new CheckBoxListener(this));
		getContentPane().add(comboBox_4);
		
		comboBox = new JComboBox();
		comboBox.setBounds(408, 151, 151, 21);
		List permission=DBManager.query("select statecode,statevalue from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and statecode='"+statecode+"'");
		ctemp0=(Map) permission.get(0);
		String permissions=StringUtils.ObjToString(ctemp0.get("statevalue"));
		comboBox.setModel(combox0(permissions));
		getContentPane().add(comboBox);
		
		panel = new JPanel();
		panel.setBounds(316, 326, 341, 26);
		getContentPane().add(panel);
		
		if(!checksecondd.equals("")){
//			String sqlc="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='"+checksecondd+"'";
			//edit 2014.6.25
			String sqlc=OPEService.getService().EquipManagerDialogSql4()+checksecondd+"'";
			firsttemp = new HashMap();
			List checkboxsb=DBManager.query(sqlc);
			jcbxb=new ArrayList<JCheckBox>();
			List  checkboxsx=DBManager.query("select secondstate from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and statecode='"+statecode+"'");
			secondhastemp=new HashMap();
			secondhastemp= (Map) checkboxsx.get(0);
			String secondstatevalue=StringUtils.ObjToString(secondhastemp.get("secondstate"));
			String arrr[]=secondstatevalue.split(",");
			for(int i=0;i<checkboxsb.size();i++){
				firsttemp=(Map) checkboxsb.get(i);
				String state_name=StringUtils.ObjToString(firsttemp.get("state_name"));
				String state_code=StringUtils.ObjToString(firsttemp.get("state_code"));
				JCheckBox jcbxx=new JCheckBox(state_name);
				for (int j = 0; j < arrr.length; j++) {
					String string = arrr[j];
					if(string.equals(state_code)){
						jcbxx.setSelected(true);
					}
				}
				jcbxx.setName(state_code);
				jcbxx.setBounds(80*i, 0, 79, 21);
				panel.add(jcbxx);
				jcbxb.add(jcbxx);
			}
		}
	}
	
	private String initContext() {
		List results = DBManager.query("select statecode, statename  from  "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and statecode='"+statecode+"'");
		temp=new HashMap();
		temp=(Map) results.get(0);
		return StringUtils.ObjToString(temp.get("statename"));
	}
	
	private String initContextsecond(){
		List results = DBManager.query("select statecode, secondtypeid  from  "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and statecode='"+statecode+"'");
		temp=new HashMap();
		temp=(Map) results.get(0);
		return StringUtils.ObjToString(temp.get("secondtypeid"));
	}
	
	private DefaultComboBoxModel combox0(String nowselectcode){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm0,cnm1=null;
		String nowselectname = null;
		if(nowselectcode.equals("")){
			nowselectname="";
		}else if(nowselectcode.equals("-1")){
			nowselectname="";
		}else{
//			String sql ="select equiptype_id from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='"+code+"'";
			//edit 2014.6.25
			String sql =OPEService.getService().EquipManagerDialogSql5()+code+"'";
			checktemp1 = new HashMap();
			List checkb = DBManager.query(sql);
			checktemp1=(Map)checkb.get(0);
			String equiptype_id=StringUtils.ObjToString(checktemp1.get("equiptype_id"));
			String sqll="select state_name, state_id from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_flag='"+code+"' and state_code='"+nowselectcode+"'";
//			String sql="select t.statecode, b.statename as statename from "+CBSystemConstants.opcardUser+"t_a_devicestatevalue t,(select statename,statevalue, statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo) b where devicetypeid='"+code+"' and b.statecode =t.statecode order by t.stateorder asc";
			List resultss=DBManager.query(sqll);
	        Map mtemp=new HashMap();
	        mtemp=(Map)resultss.get(0);
			String state_name=StringUtils.ObjToString(mtemp.get("state_name"));
			nowselectname=state_name;
		}
		cnm0=new CodeNameModel(nowselectcode,nowselectname);
		//model.addElement(cnm0);
		cnm1=new CodeNameModel("-1","无");
		if(nowselectname==""){
			model.addElement(cnm1);
		}else{
			model.addElement(cnm0);
		}
//		String sql ="select equiptype_id from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='"+code+"'";
		//edit 2014.6.25
		String sql =OPEService.getService().EquipManagerDialogSql6()+code+"'";
		checktemp1 = new HashMap();
		List checkb = DBManager.query(sql);
		checktemp1=(Map)checkb.get(0);
		String equiptype_id=StringUtils.ObjToString(checktemp1.get("equiptype_id"));
//		String sqll="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='"+equiptype_id+"'";
		//edit 2014.6.25
		String sqll=OPEService.getService().EquipManagerDialogSql7()+code+"'";
//		String sql="select t.statecode, b.statename as statename from "+CBSystemConstants.opcardUser+"t_a_devicestatevalue t,(select statename,statevalue, statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo) b where devicetypeid='"+code+"' and b.statecode =t.statecode order by t.stateorder asc";
		List resultss=DBManager.query(sqll);
        Map mtemp=new HashMap();
		for (int i = 0; i < resultss.size(); i++) {
			mtemp=(Map)resultss.get(i);
			String state_code=StringUtils.ObjToString(mtemp.get("state_code"));
			
			if(state_code.equals(nowselectcode)){
				continue;
			}else{
				CodeNameModel cnm=new CodeNameModel(StringUtils.ObjToString(mtemp.get("state_code")),StringUtils.ObjToString(mtemp.get("state_name")));
				model.addElement(cnm);
			}
		}	
		return model;
	}
	
	private DefaultComboBoxModel combox1(String nowselectcode){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm0,cnm1=null;
		String nowselectname = null;
		if(nowselectcode.equals("")){
			nowselectname="";
		}else{
			String sql="select code, name from "+CBSystemConstants.opcardUser+"t_a_dictionary where code='"+nowselectcode+"' and (unitcode='"+CBSystemConstants.unitCode+"' or unitcode='system') and codetype='runmodel'";
			List result=DBManager.queryForList(sql);
			com1temp=new HashMap();
			com1temp=(Map) result.get(0);
			
			nowselectname=StringUtils.ObjToString(com1temp.get("name"));
		}
		cnm0=new CodeNameModel(nowselectcode,nowselectname);
		model.addElement(cnm0);
		cnm1=new CodeNameModel("","");
		if(cnm0.equals(cnm1)){
			
		}else{
			model.addElement(cnm1);
		}
		String sql="select code, name from "+CBSystemConstants.opcardUser+"t_a_dictionary where ((unitcode='"+CBSystemConstants.unitCode+"' or unitcode='system') or unitcode='system') and codetype='runmodel'";
        List resultss=DBManager.query(sql);
        Map mtemp=new HashMap();
		for (int i = 0; i < resultss.size(); i++) {
			mtemp=(Map)resultss.get(i);
			String code=StringUtils.ObjToString(mtemp.get("code"));
			if(code.equals(nowselectcode)){
				continue;
			}else{
				CodeNameModel cnm=new CodeNameModel(StringUtils.ObjToString(mtemp.get("code")),StringUtils.ObjToString(mtemp.get("name")));
				model.addElement(cnm);
			}
		}	
		return model;
	}
	
	private DefaultComboBoxModel combox2(String nowselectcode){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm,cnm1,cnm2=null;
		String nowselectname = null;
		if(nowselectcode.equals("0")){
			nowselectname="没有";
		}else if(nowselectcode.equals("1")){
			nowselectname="有";
		}else{
			nowselectname="";
		}
		cnm=new CodeNameModel(nowselectcode,nowselectname);
		cnm1=new CodeNameModel("0","没有");
		cnm2=new CodeNameModel("1","有");
		model.addElement(cnm);
		if(cnm.equals(cnm1)){
			model.addElement(cnm2);
		}else{
			model.addElement(cnm1);
		}
		return model;
	}

	private CodeNameModel[] combox3(String nowselectcode){
		ArrayList<CodeNameModel> cnmList = new ArrayList<CodeNameModel>();
		String sql="select code, name from "+CBSystemConstants.opcardUser+"t_a_dictionary where unitcode='system' and codetype in ('Breaker','Disconnector','BusbarSection','PWBreaker')";
        
		String tiaojian = "";
		if(!nowselectcode.equals("")) {
			
			tiaojian = " and code in ('"+nowselectcode.replace(",", "','")+"')";
			sql = sql + tiaojian;
		}
		
		List resultss=DBManager.query(sql);
        Map mtemp=new HashMap();
		for (int i = 0; i < resultss.size(); i++) {
			mtemp=(Map)resultss.get(i);
			String code=StringUtils.ObjToString(mtemp.get("code"));
			String name=StringUtils.ObjToString(mtemp.get("name"));
			CodeNameModel cnm=new CodeNameModel(code,name);
			cnmList.add(cnm);
		}	
		return cnmList.toArray(new CodeNameModel[]{});
	}
	
	private DefaultComboBoxModel combox4(String nowselectcode){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm0,cnm1=null;
		String nowselectname = null;
		if(nowselectcode.equals("")){
			nowselectname="";
		}else{
//			List result=DBManager.query("select equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_id='"+nowselectcode+"'");
			List result=DBManager.query(OPEService.getService().EquipManagerDialogSql8()+nowselectcode+"'");
			com4temp=new HashMap();
			com4temp=(Map) result.get(0);
			nowselectname=StringUtils.ObjToString(com4temp.get("equiptype_name"));
		}
		cnm0=new CodeNameModel(nowselectcode,nowselectname);
		model.addElement(cnm0);
		cnm1=new CodeNameModel("","");
		if(cnm0.equals(cnm1)){
			
		}else{
			model.addElement(cnm1);
		}
//		String sql="select equiptype_id,equiptype_name,equiptype_flag from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='Reclosing'";
		//edit 2014.6.25
		List resultss=DBManager.query(OPEService.getService().EquipManagerDialogSql9());
        Map mtemp=new HashMap();
		for (int i = 0; i < resultss.size(); i++) {
			mtemp=(Map)resultss.get(i);
			String equiptype_id=StringUtils.ObjToString(mtemp.get("equiptype_id"));
			if(equiptype_id.equals(nowselectcode)){
				continue;
			}else{
				CodeNameModel cnm=new CodeNameModel(StringUtils.ObjToString(mtemp.get("equiptype_id")),StringUtils.ObjToString(mtemp.get("equiptype_name")));
				model.addElement(cnm);;
			}
		}	
		return model;
	}
	
	private void initlbltext(){

		lblNewLabel = new JLabel("名称");
		lblNewLabel.setBounds(175, 81, 112, 15);
		getContentPane().add(lblNewLabel);
		
		lblNewLabel_1 = new JLabel("无权限状态");
		lblNewLabel_1.setBounds(175, 120, 112, 15);
		getContentPane().add(lblNewLabel_1);
		
		lblNewLabel_2 = new JLabel("接线方式");
		lblNewLabel_2.setBounds(175, 190, 112, 15);
		getContentPane().add(lblNewLabel_2);
		
		lblNewLabel_3 = new JLabel("是否带旁路");
		lblNewLabel_3.setBounds(175, 226, 112, 15);
		getContentPane().add(lblNewLabel_3);
		
		lblNewLabel_4 = new JLabel("安装类型");
		lblNewLabel_4.setBounds(175, 259, 112, 15);
		getContentPane().add(lblNewLabel_4);
		
//		lblNewLabel_5 = new JLabel("二次设备类型标识");
//		lblNewLabel_5.setBounds(175, 296, 131, 15);
//		getContentPane().add(lblNewLabel_5);
//		
//		lblNewLabel_6 = new JLabel("二次设备状态");
//		lblNewLabel_6.setBounds(175, 336, 131, 15);
//		getContentPane().add(lblNewLabel_6);
		
		lblNewLabel_7 = new JLabel("目标状态");
		lblNewLabel_7.setBounds(175, 154, 112, 15);
		getContentPane().add(lblNewLabel_7);
	}
	
	public List<JCheckBox> getstatevalue(){
		
		return jcbx;
	}
	
	public List<JCheckBox> getsecondvalue(){
		return jcbxb;
	}
	
	public JTextField getTextField(JTextField text) {
		
		return text;
	}
	
	public Object getcodename(JComboBox combobox) {
		return combobox.getSelectedItem();
	}
	
	public void addBtnNewButtonListenerxz(){
		btnNewButton.addActionListener(new BtnNewButtonListenerxz(emcd,this, eomd, code,et,cardtype));
	}
	
	class BtnNewButtonListenerxz implements ActionListener{
		private EquipManagerCzDialog emcd;
		private EquipOperationManageDialog eomd;
		private EquipManagerDialogC emdg;
		private EquipTable et;
		private String code,cardtype;
		public BtnNewButtonListenerxz(EquipManagerCzDialog emcd,EquipManagerDialogC emdg, EquipOperationManageDialog eomd,String code,EquipTable et,String cardtype){
			this.emcd=emcd;
			this.emdg = emdg;
			this.eomd = eomd;
			this.et=et;
			this.code=code;
			this.cardtype=cardtype;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			List list=DBManager.query("select max(stateorder)+1 stateorder from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and devicetypeid='"+code+"' and parentcode='"+et.getStatecode()+"' and statetype=0");
			Map map=new HashMap();
			map=(Map)list.get(0);
			String stateorders=StringUtils.ObjToString(map.get("stateorder")==null?"0":map.get("stateorder"));
			int stateorder=Integer.parseInt(stateorders);
			String statecode=java.util.UUID.randomUUID().toString();
			CodeNameModel code1=(CodeNameModel) emdg.getcodename(comboBox_1);
			CodeNameModel code2=(CodeNameModel) emdg.getcodename(comboBox_2);
			String txt = emdg.getTextField(textField).getText();
			CodeNameModel code4=(CodeNameModel) emdg.getcodename(comboBox_4);
			CodeNameModel code0=(CodeNameModel) emdg.getcodename(comboBox);
			
			statevalueend=new StringBuffer();
			List <JCheckBox> jcb=emdg.getstatevalue();
			for(int i=0;i<jcb.size();i++){
				JCheckBox jb=jcb.get(i);
				if(jb.isSelected()==true){
					statevalueend.append(jb.getName()+",");
				}
				
			}
			if(statevalueend.length()==0){
				statevalueend.append("-1");
			}
			
			String stateValue = statevalueend.toString();
			if(stateValue.endsWith(","))
				stateValue = stateValue.substring(0, stateValue.length()-1);
			
			secondvalueend=new StringBuffer();
			List <JCheckBox> jcbb=emdg.getsecondvalue();

			if(jcbb!=null){
				for(int i=0;i<jcbb.size();i++){
					JCheckBox jb=jcbb.get(i);
					if(jb.isSelected()==true){
						secondvalueend.append(jb.getName()+",");
					}
					if(secondvalueend.length()==0){
						secondvalueend.append("-1");
					}
				}
			}else{
				secondvalueend.append("-1");
			}
			String stateSecondValue = secondvalueend.toString();
			if(stateSecondValue.endsWith(","))
				stateSecondValue = stateSecondValue.substring(0, stateSecondValue.length()-1);
			
			
			String operatecode=code0.getCode();
			String runmodel=code1.getCode();
			String hasside=code2.getCode();
			String runtype=comboBox_3.getSelectedCode();
			String secondtypeid=code4.getCode();
			String parentcode=et.getStatecode();
			String unitcode=CBSystemConstants.opCode;
			if(!txt.equals("")){
				DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_devicestateinfo (statecode,statename,operatecode,opcode,devicetypeid,parentcode,statetype,runmodel,hasside,runtype,stateorder,secondtypeid,secondstate,cardbuildtype,statevalue,islock) values ('"+statecode+"','"+txt+"','"+stateValue+"','"+unitcode+"','"+code+"','"+parentcode+"',0,'"+runmodel+"','"+hasside+"','"+runtype+"','"+stateorders+"','"+secondtypeid+"','"+stateSecondValue+"','"+cardtype+"','"+operatecode+"','0')");
				eomd.shuaxin();
				emdg.dispose();
			}else{
				ShowMessage.view("名称不能为空");
			}
		}
	}
	
	public void addBtnNewButtonListenerxg() {
		btnNewButton.addActionListener(new BtnNewButtonListenerxg(this, eomd, statecode,code,cardtype));	
	}
	
	class CheckBoxListener implements ItemListener{
		private EquipManagerDialogC emdg;
		public CheckBoxListener(EquipManagerDialogC emdg){
			this.emdg=emdg;
		}
		@Override
		public void itemStateChanged(ItemEvent e) {
			// TODO Auto-generated method stub
			emdg.panel.removeAll();
			CodeNameModel code4=(CodeNameModel) emdg.getcodename(comboBox_4);
			String secondtypeid=code4.getCode();
			if(secondtypeid!=null){
//				String sqll="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='"+secondtypeid+"'";
				//edit 2104.6.25
				String sqll=OPEService.getService().EquipManagerDialogSql10()+secondtypeid+"'";
				borntemp = new HashMap();
				List checkboxs=DBManager.query(sqll);
				jcbxb=new ArrayList<JCheckBox>();
				for(int i=0;i<checkboxs.size();i++){
					borntemp=(Map) checkboxs.get(i);
					String state_name=StringUtils.ObjToString(borntemp.get("state_name"));
					String state_code=StringUtils.ObjToString(borntemp.get("state_code"));
					JCheckBox jcbxx=new JCheckBox(state_name);
					jcbxx.setName(state_code);
					jcbxx.setBounds(80*i, 0, 79, 21);
					emdg.panel.add(jcbxx);
					jcbxb.add(jcbxx);
					
					emdg.panel.updateUI();
					
				}
			}
		}
		
	}
	
	class BtnNewButtonListenerxg implements ActionListener {
		private EquipOperationManageDialog eomd;
		private EquipManagerDialogC emdg;
		private String statecode,code,cardtype;
		public BtnNewButtonListenerxg(EquipManagerDialogC emdg, EquipOperationManageDialog eomd, String statecode,String code,String cartype) {
			this.emdg = emdg;
			this.eomd = eomd;
			this.statecode = statecode;
			this.code=code;
			this.cardtype=cartype;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			CodeNameModel code1=(CodeNameModel) emdg.getcodename(comboBox_1);
			CodeNameModel code2=(CodeNameModel) emdg.getcodename(comboBox_2);
			CodeNameModel code4=(CodeNameModel) emdg.getcodename(comboBox_4);
			CodeNameModel code0=(CodeNameModel) emdg.getcodename(comboBox);
			String txt = emdg.getTextField(textField).getText();
			
			statevalueend=new StringBuffer();
			List <JCheckBox> jcb=emdg.getstatevalue();
			
			for(int i=0;i<jcb.size();i++){
				JCheckBox jb=jcb.get(i);
				if(jb.isSelected()==true){
					statevalueend.append(jb.getName()+",");
				}
				
			}
			if(statevalueend.length()==0){
				statevalueend.append("-1");
			}
			String stateValue = statevalueend.toString();
			if(stateValue.endsWith(","))
				stateValue = stateValue.substring(0, stateValue.length()-1);
			
			
			secondvalueend=new StringBuffer();
			List <JCheckBox> jcbb=emdg.getsecondvalue();
			if(jcbb!=null){
				for(int i=0;i<jcbb.size();i++){
					JCheckBox jb=jcbb.get(i);
					if(jb.isSelected()==true){
						secondvalueend.append(jb.getName()+",");
					}
				}
				if(secondvalueend.length()==0){
					secondvalueend.append("-1");
				}
			}else{
				secondvalueend.append("-1");
			}
			String stateSecondValue = secondvalueend.toString();
			if(stateSecondValue.endsWith(","))
				stateSecondValue = stateSecondValue.substring(0, stateSecondValue.length()-1);
			
			String operatecode=code0.getCode();
			String runmodel=code1.getCode();
			String hasside=code2.getCode();
			String runtype=comboBox_3.getSelectedCode();
			String secondtypeid=code4.getCode();
			if(!txt.equals("")){
				DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set statename='"+txt+"' , hasside='"+hasside+"',runmodel='"+runmodel+"' ,runtype='"+runtype+"' ,operatecode='"+stateValue+"',secondtypeid='"+secondtypeid+"',secondstate='"+stateSecondValue+"',statevalue='"+operatecode+"'  where statecode='"+statecode+"' and cardbuildtype='"+cardtype+"'");
				eomd.shuaxin();
				emdg.dispose();
			}else{
				ShowMessage.view("名称不能为空");
			}
		}	
	}
}
