package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.EventQueue;
import java.awt.event.MouseEvent;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JRadioButton;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ScrollPaneConstants;
import javax.swing.border.TitledBorder;

public class CzpManager extends JDialog {
	private JTextField textField;
	private JTextField textField_1;
	private JTextField textField_2;
	private JTable table;
	private JPanel panel,panel_1,panel_2;
	private JLabel lblNewLabel;

	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		EventQueue.invokeLater(new Runnable() {
			public void run() {
				try {
					CzpManager dialog = new CzpManager(new javax.swing.JFrame(),true);
					dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
					dialog.setVisible(true);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		});
	}
//	public static void main(String[] args) {
//		java.awt.EventQueue.invokeLater(new Runnable() {
//			public void run() {
//				EquipOperationManageDialog dialog = new EquipOperationManageDialog(new javax.swing.JFrame(), true);
//				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
//					public void windowClosing(java.awt.event.WindowEvent e) {
//						System.exit(0);
//					}
//				});
//				dialog.setVisible(true);
//			}
//		});
//	}
	
	

	/**
	 * Create the dialog.
	 */
	public CzpManager(java.awt.Frame parent, boolean modal) {
		super(parent,modal);
		setBounds(100, 100, 519, 300);
		getContentPane().setLayout(new BorderLayout(0, 0));
		
		panel = new JPanel();
		getContentPane().add(panel, BorderLayout.NORTH);
		panel.setLayout(new BorderLayout(0, 0));
		
		panel_1 = new JPanel();
		panel.add(panel_1, BorderLayout.NORTH);
		
		lblNewLabel = new JLabel("检修票环节：");
		panel_1.add(lblNewLabel);
		
		JRadioButton rdbtnNewRadioButton = new JRadioButton("已下达待执行");
		panel_1.add(rdbtnNewRadioButton);
		
		JRadioButton rdbtnNewRadioButton_1 = new JRadioButton("执行中检修票");
		panel_1.add(rdbtnNewRadioButton_1);
		
		panel_2 = new JPanel();
		panel_2.setBorder(new TitledBorder(null, "查询条件", TitledBorder.LEADING, TitledBorder.TOP, null, null));
		panel.add(panel_2, BorderLayout.SOUTH);
		
		JLabel lblNewLabel_1 = new JLabel("日期：");
		panel_2.add(lblNewLabel_1);
		
		textField = new JTextField();
		panel_2.add(textField);
		textField.setColumns(10);
		textField.addMouseListener(new java.awt.event.MouseAdapter() {
	            public void mouseClicked(java.awt.event.MouseEvent evt) {
	                jTextBeginTime_mouseClick(evt);
	            }
	        });
		
		JLabel lblNewLabel_2 = new JLabel("-");
		panel_2.add(lblNewLabel_2);
		
		textField_1 = new JTextField();
		panel_2.add(textField_1);
		textField_1.setColumns(10);
		textField_1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                jTextEndTime_mouseClick(evt);
            }
        });
		
		JLabel lblNewLabel_3 = new JLabel("申请单位：");
		panel_2.add(lblNewLabel_3);
		
		textField_2 = new JTextField();
		panel_2.add(textField_2);
		textField_2.setColumns(10);
		
		JButton btnNewButton = new JButton("查询");
		panel_2.add(btnNewButton);
		
		JScrollPane scrollPane = new JScrollPane();
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
		getContentPane().add(scrollPane, BorderLayout.CENTER);
		
		table = new JTable();
		scrollPane.setViewportView(table);

	}

	public void jTextEndTime_mouseClick(MouseEvent evt) {
		// TODO Auto-generated method stub
		  String selectTime = "";
	        if (evt.getButton() == 1 && evt.getClickCount() == 2) {
//	            CalendarDialog cd = new CalendarDialog(this, true);
//	            selectTime = cd.showCalendar();
	        }
	        if (!selectTime.equals("")) {
	            String[] beginTime = selectTime.split(" ");
	            textField_1.setText(beginTime[0]);
	        }
	}

	public void jTextBeginTime_mouseClick(MouseEvent evt) {
		// TODO Auto-generated method stub
		 String selectTime = "";
	        if (evt.getButton() == 1 && evt.getClickCount() == 2) {
	          
//	        	CalendarDialog cd = new CalendarDialog(this, true);
//	            selectTime = cd.showCalendar();
	        }
	        if (!selectTime.equals("")) {
	            String[] beginTime = selectTime.split(" ");
	            textField.setText(beginTime[0]);
	        }
	}

}
