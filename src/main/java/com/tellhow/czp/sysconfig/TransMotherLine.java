package com.tellhow.czp.sysconfig;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

import javax.swing.BoxLayout;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.ListSelectionModel;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.TableModelEvent;
import javax.swing.event.TableModelListener;
import javax.swing.table.DefaultTableModel;

public class TransMotherLine extends JDialog {
	
	private MyLady l1,l2,l3;
	private MySon s1;
	private MySon s2;
	private MySon s3;
	private MySon s4;
	private List<MySon> list,listt,listtt;
	private JLabel lblNewLabel;
	private JButton jb;
	private JPanel jpp;
	
	private void init(){
		s1=new MySon("1号","1",false);
		s2=new MySon("2号","2",true);
		s3=new MySon("3号","3",false);
		s4=new MySon("4号","4",true);
		list=new ArrayList<MySon>();
		listt=new ArrayList<MySon>();
		listtt=new ArrayList<MySon>();
		list.add(s1);
		list.add(s2);
		listt.add(s4);
		listtt.add(s3);
		l1=new MyLady("1母", list);
		l2=new MyLady("2母", listt);
		l3=new MyLady("3母", listtt);
	}
	
	public TransMotherLine() {
		setBounds(400, 100, 400, 600);
		jpp=(JPanel)getContentPane();
		jpp.setLayout(new BoxLayout(getContentPane(), BoxLayout.PAGE_AXIS));
		init();
		List<MyLady> ist=new ArrayList<MyLady>();
		ist.add(l1);
		ist.add(l2);
		ist.add(l3);
		for(int i=0;i<ist.size();i++){
			JPanel jpnei=new JPanel();
			MyLady ld=new MyLady();
			ld=ist.get(i);
			String title=ld.getName();
			lblNewLabel = new JLabel(title);
			lblNewLabel.setBounds(0, 200*i, 150, 25);
			jpnei.add(lblNewLabel);
			
			JScrollPane jsp=new JScrollPane();
			jsp.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
			jsp.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
			
			final JTable table=new JTable(){
			      public void tableChanged(TableModelEvent e) {
			          super.tableChanged(e);
			          repaint();
			        }
			      };
			DefaultTableModel dtm=new DefaultTableModel(){
				public Class<?> getColumnClass(int columnIndex) {
					return getValueAt(0, columnIndex).getClass();
					}
				};
			Object[] tableHeads=new String[]{"姓名", "年龄","选择"};
			
			table.setModel(dtm);
			dtm.setColumnIdentifiers(tableHeads);
			List<MySon> ms=ld.getMyson();
			for(int j=0;j<ms.size();j++){
				MySon sn=new MySon();
				Vector v = new Vector();
				sn=ms.get(j);
				v.add(sn.getName());
				v.add(sn.getAge());
				v.add(sn.getIslink());
				dtm.addRow(v);
			}
			table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
			table.getModel().addTableModelListener(new TableModelListener() {
				
				@Override
				public void tableChanged(TableModelEvent e) {
					int m=table.getSelectedRow();
					boolean bl = (Boolean)table.getModel().getValueAt(m, 2);
					int z=table.getModel().getRowCount();
					int num=0;
					for (int zz=0;zz<z;zz++){
						boolean bll=(Boolean)table.getModel().getValueAt(zz, 2);
						if (bll==true){
							num=num+1;
						}
						if(num>1){
							JOptionPane.showMessageDialog(null, "只能选择一个", "提示", JOptionPane.INFORMATION_MESSAGE);
							
							table.getModel().setValueAt(false, zz, 2);
							break;
						}
						System.out.println(bll);
						}
					}
			});
			jsp.add(table);
			jsp.setViewportView(table);
			jpp.add(jpnei);
			jpp.add(jsp);
		}
		jb=new JButton("提交");
		jb.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent arg0) {
				JOptionPane.showMessageDialog(null, "只能选择一个", "提示", JOptionPane.INFORMATION_MESSAGE);
			}
		});
		jpp.add(jb);
	}
	
	
	public static void main(String[] args) {
		try {
			TransMotherLine dialog = new TransMotherLine();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}


