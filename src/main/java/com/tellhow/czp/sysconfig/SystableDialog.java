package com.tellhow.czp.sysconfig;

import java.awt.Component;
import java.util.ArrayList;

import javax.swing.JComboBox;
import javax.swing.JLabel;

import org.beryl.gui.GUIEvent;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.mainframe.JAutoCompleteComboBox;
import com.tellhow.czp.mainframe.TableDialog;
import com.tellhow.czp.staticsql.OpeInfo;

import czprule.model.CodeNameModel;
import czprule.model.TableField;
import czprule.model.TableParam;
import czprule.system.CBSystemConstants;

/**
 * <AUTHOR>
 *
 */
public class SystableDialog {
	//新设备规则类解析规则类管理
	public void NewEquipOperationManager(GUIEvent e){
		ArrayList<TableField> fieldList = new ArrayList<TableField>();
		fieldList.add(new TableField("", true));
		fieldList.add(new TableField("WORDID", "", "", true));
		fieldList.add(new TableField("WORDTYPE","类型","select '3' code, '新投票' name from dual",false));
		fieldList.add(new TableField("WORDVALUE", "规则名称", "", false));
		fieldList.add(new TableField("WORDBEANCLASS", "规则类名", "", false));
		
		TableParam tableParam = new TableParam();
		tableParam.setTableTitle("新设备规则类解析规则类管理");
		tableParam.setTableWidth(600);
		tableParam.setTableHeight(400);
		tableParam.setQueryCondition(" and wordtype=3");
		tableParam.setTableName(""+CBSystemConstants.opcardUser+"t_a_cardwordbean");
		tableParam.setFieldList(fieldList);
		
		TableDialog dialog = new TableDialog();
		dialog.init(tableParam);
		dialog.setVisible(true);
	}
	//票号规则管理
	public void ruleManagerAction(GUIEvent e){
		ArrayList<TableField> fieldList = new ArrayList<TableField>();
		fieldList.add(new TableField("", true));
		fieldList.add(new TableField("CARDNOID", "", "", true));
		fieldList.add(new TableField("CARDKIND", "指令票类型", "", false));
		fieldList.add(new TableField("WORDID", "票号规则", "select t.wordid,t.wordvalue from "+CBSystemConstants.opcardUser+"t_a_cardwordbean t where wordtype=2", false));
		
		TableParam tableParam = new TableParam();
		tableParam.setTableTitle("票号规则管理");
		tableParam.setTableWidth(600);
		tableParam.setTableHeight(400);
		tableParam.setTableName(""+CBSystemConstants.opcardUser+"t_a_rulecardno");
		tableParam.setFieldList(fieldList);
		
		TableDialog dialog = new TableDialog();
		dialog.init(tableParam);
		dialog.setVisible(true);
	}
	
	//票号生成规则类管理
	public void ruleClassManagerAction(GUIEvent e){
		ArrayList<TableField> fieldList = new ArrayList<TableField>();
		fieldList.add(new TableField("", true));
		fieldList.add(new TableField("WORDID", "", "", true));
		fieldList.add(new TableField("WORDTYPE","类型","select '2' code, '票号' name from dual",false));
		fieldList.add(new TableField("WORDVALUE", "规则名称", "", false));
		fieldList.add(new TableField("WORDBEANCLASS", "规则类名", "", false));
		
		TableParam tableParam = new TableParam();
		tableParam.setTableTitle("票号生成规则类管理");
		tableParam.setTableWidth(600);
		tableParam.setTableHeight(400);
		tableParam.setQueryCondition(" and wordtype=2");
		tableParam.setTableName(""+CBSystemConstants.opcardUser+"t_a_cardwordbean");
		tableParam.setFieldList(fieldList);
		
		TableDialog dialog = new TableDialog();
		dialog.init(tableParam);
		dialog.setVisible(true);
	}
	
	//设备类型维护
	public void protectTypeAction(GUIEvent e){
		ArrayList<TableField> fieldList = new ArrayList<TableField>();
		fieldList.add(new TableField("", true));
		fieldList.add(new TableField("PROTECTTYPEID", "", "", true));
		fieldList.add(new TableField("PROTECTTYPENAME", "保护类型", "", false));
		fieldList.add(new TableField("PROTECTKIND", "保护性质", "select '0' code, '主保护' name from dual union all select '1', '后备保护' from dual", false));
//		fieldList.add(new TableField("EQUIPTYPEID", "设备类型", "select t.equiptype_id,t.equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_flag in (select devicetypeid from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO ) order by t.equiptype_order", false));
		//edit 2014.6.25
		fieldList.add(new TableField("EQUIPTYPEID", "设备类型", OPEService.getService().SystableDialogSql(), false));
		
		TableParam tableParam = new TableParam();
		tableParam.setTableTitle("保护类型维护");
		tableParam.setTableWidth(600);
		tableParam.setTableHeight(400);
		tableParam.setTableName(""+CBSystemConstants.opcardUser+"t_a_protectinfo");
		tableParam.setFieldList(fieldList);
		
		TableDialog dialog = new TableDialog();
		dialog.init(tableParam);
		dialog.setVisible(true);
	}
	
	//保护操作维护
	public void protectOperateAction(GUIEvent e){
		
		final TableDialog dialog = new TableDialog();
		final TableParam tableParam = new TableParam();
		
		//查询组件
		ArrayList<Component> compList = new ArrayList<Component>();
		JLabel label = new JLabel("设备类型");
		compList.add(label);
		final JComboBox combo = new JAutoCompleteComboBox();
//		dialog.fillComboBox(combo, "select t.equiptype_id,t.equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_flag in (select devicetypeid from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO ) order by t.equiptype_order");
		//edit 2014.6.25
		dialog.fillComboBox(combo,OPEService.getService().SystableDialogSql1());
		compList.add(combo);
		String code = ((CodeNameModel)combo.getSelectedItem()).getCode();
		combo.setSelectedIndex(0);
		
		//查询组件事件
		combo.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				if(combo.getSelectedItem() instanceof CodeNameModel) {
					String code = ((CodeNameModel)combo.getSelectedItem()).getCode();
					
					
					//tableParam.setQueryCondition("and SCS_OBJ= '"+code2+"'");
					
					//mxField1.setFieldFillSQL(mxSql1.replace("?", code).replace("#", code2));
					//kgField.setFieldValue(code2);
					
					tableParam.getField("PROTECTTYPEID").setFieldFillSQL("select t.protecttypeid,t.protecttypename from "+CBSystemConstants.opcardUser+"t_a_protectinfo t where protecttypeid in (select protecttypeid from "+CBSystemConstants.opcardUser+"T_A_PROTECTINFO where equiptypeid='"+code+"')");
//					tableParam.getField("BEGINSTATUS").setFieldFillSQL("select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='"+code+"'");
					//edit 2014.6.25
					tableParam.getField("BEGINSTATUS").setFieldFillSQL(OPEService.getService().SystableDialogSql2()+code+"'");
//					tableParam.getField("ENDSTATUS").setFieldFillSQL("select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='"+code+"'");
					//edit 2014.6.25
					tableParam.getField("ENDSTATUS").setFieldFillSQL(OPEService.getService().SystableDialogSql3()+code+"'");
					tableParam.setQueryCondition("and protecttypeid in (select protecttypeid from "+CBSystemConstants.opcardUser+"T_A_PROTECTINFO where equiptypeid='"+code+"')");
					dialog.refresh(tableParam);
				}
			}
		});
				
		ArrayList<TableField> fieldList = new ArrayList<TableField>();
		fieldList.add(new TableField("", true));
		fieldList.add(new TableField("PROTECTWORDID", "", "", true));
		fieldList.add(new TableField("PROTECTTYPEID", "保护类型", "select t.protecttypeid,t.protecttypename from "+CBSystemConstants.opcardUser+"t_a_protectinfo t where protecttypeid in (select protecttypeid from "+CBSystemConstants.opcardUser+"T_A_PROTECTINFO where equiptypeid='"+code+"')", false));
//		fieldList.add(new TableField("BEGINSTATUS", "起始状态", "select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='"+code+"'", false));
		//edit 2014.6.25
		fieldList.add(new TableField("BEGINSTATUS", "起始状态", OPEService.getService().SystableDialogSql4()+code+"'", false));
//		fieldList.add(new TableField("ENDSTATUS", "目标状态", "select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='"+code+"'", false));
		//edit 2014.6.25
		fieldList.add(new TableField("ENDSTATUS", "目标状态", OPEService.getService().SystableDialogSql5()+code+"'", false));
		fieldList.add(new TableField("ACTIONTYPE", "操作类型", "select '0' code, '投入' name from dual union all select '1', '退出' from dual union all select '2', '检核' from dual", false));
		fieldList.add(new TableField("ISCOMPLETED", "状态转换是否完成", " select '1' code, '是' name from dual union all select '0', '否' from dual", false));
		fieldList.add(new TableField("ACTIONWORD", "操作命令", "", false));
		
		
		tableParam.setTableTitle("保护操作维护");
		tableParam.setTableWidth(600);
		tableParam.setTableHeight(400);
		tableParam.setTableName(""+CBSystemConstants.opcardUser+"t_a_protectword");
		tableParam.setFieldList(fieldList);
		tableParam.setCompList(compList);
		tableParam.setQueryCondition("and protecttypeid in (select protecttypeid from "+CBSystemConstants.opcardUser+"T_A_PROTECTINFO where equiptypeid='"+code+"')");
		
		dialog.init(tableParam);
		dialog.setVisible(true);
	}
	
	//区域规则术语管理
	public void aeraManagerAction(GUIEvent e){
		ArrayList<TableField> fieldList = new ArrayList<TableField>();
		fieldList.add(new TableField("", true));
		fieldList.add(new TableField("OPCODE", "编码", "", false));
		fieldList.add(new TableField("AREANO","区域","select '2' code, '票号' name from dual",false));
		fieldList.add(new TableField("ROLECODE", "角色", "select '0' code, '调度' name from dual union all select '1' code, '配调' name from dual union all select '2' code, '监控' name from dual", false));
		
		TableParam tableParam = new TableParam();
		tableParam.setTableTitle("区域规则术语管理");
		tableParam.setTableWidth(600);
		tableParam.setTableHeight(400);
		//tableParam.setQueryCondition(" and wordtype=2");
		tableParam.setTableName(""+CBSystemConstants.opcardUser+"T_A_OPCODEINFO");
		tableParam.setFieldList(fieldList);
		
		TableDialog dialog = new TableDialog();
		dialog.init(tableParam);
		dialog.setVisible(true);
	}
		
	public static void main(String args[]) {
		SystableDialog dialog = new SystableDialog();
		dialog.protectOperateAction(null);
	}
}
