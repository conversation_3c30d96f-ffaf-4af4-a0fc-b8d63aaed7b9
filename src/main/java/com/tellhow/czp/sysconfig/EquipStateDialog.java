package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.EventQueue;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTree;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.TableModelEvent;
import javax.swing.event.TableModelListener;
import javax.swing.event.TreeSelectionEvent;
import javax.swing.event.TreeSelectionListener;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumn;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreeSelectionModel;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class EquipStateDialog extends JDialog implements TreeSelectionListener {
	private JTable table;
	private Map temp;
	private JTree tree;
	private List<Boolean> ori;// 判断这行是不是数据库中的
	private List<Boolean> updated;// 判断是不是修改过
	private EquiptypeState eqys;
    private	DefaultTableModel dtm;
 
	//private Object nodeInfo;//存储选中节点信息
	//DefaultMutableTreeNode node;
	 
	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		EventQueue.invokeLater(new Runnable() {
			public void run() {
				try {
					EquipStateDialog dialog = new EquipStateDialog();
					dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
					dialog.setVisible(true);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		});
	}
    
	/**
	 * Create the dialog.
	 */
	public EquipStateDialog() {
		setTitle("设备状态管理");
		setBounds(100, 100, 650, 500);
		getContentPane().setLayout(new BorderLayout(0, 0));
		
		 JScrollPane treeview=new JScrollPane();
		treeview.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		treeview.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
		getContentPane().add(treeview, BorderLayout.WEST);
		
		
		
	//构建左边的树
		    tree = new JTree();
		tree.setModel(new DefaultTreeModel(
			new DefaultMutableTreeNode("设备类型") {
				{
					DefaultMutableTreeNode node_1;
					node_1 = new DefaultMutableTreeNode("一次设备");
					add(node_1);
//					List results = DBManager.query("select equiptype_id,equiptype_parent_id,equiptype_code,equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.cim_id is not null");
					//edit 2014.6.25 --am11.53 
					List results = DBManager.query(OPEService.getService().EquipStateDialogSql());
					temp=new HashMap();  
					System.out.println(results);
					for(int i = 0; i < results.size(); i++){
						temp = (Map) results.get(i);
						EquiptypeState equs = new EquiptypeState(StringUtils.ObjToString(temp.get("equiptype_id")),StringUtils.ObjToString(temp.get("equiptype_name")));
						DefaultMutableTreeNode node = new  DefaultMutableTreeNode( equs) ;
						node_1.add(node);
						System.out.println(node);
						
					}
						
 					
						
					
				}
			}
		));
		//只有一个节点被选中
		tree.addTreeSelectionListener(this);
		tree.getSelectionModel().setSelectionMode(TreeSelectionModel.SINGLE_TREE_SELECTION);  
			
		treeview.setViewportView(tree);
		//右键弹出菜单
	
		JPanel panel = new JPanel();
		getContentPane().add(panel, BorderLayout.CENTER);
		panel.setLayout(new BorderLayout(0, 0));
		
		JPanel panel_1 = new JPanel();
		panel.add(panel_1, BorderLayout.NORTH);
		
		JButton addbutton = new JButton("新增");
		addbutton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				addbuttonActionPerformed(e);
			}
		});
		panel_1.add(addbutton);
		
		JButton delbutton = new JButton("删除");
		delbutton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				delbuttonActionPerformed(e);
			}
		});
		panel_1.add(delbutton);
		
		JButton savebutton = new JButton("保存");
		savebutton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				savebuttonActionPerformed(e);
			}
		});
		panel_1.add(savebutton);
		
		JScrollPane scrollPane = new JScrollPane();
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		panel.add(scrollPane, BorderLayout.CENTER);
		
		 table = new JTable();
		 
		scrollPane.setViewportView(table);

	}
  
	@Override
	public void valueChanged(TreeSelectionEvent e) {
		// TODO Auto-generated method stub
		 JTree tree = (JTree) e.getSource();

		 DefaultMutableTreeNode node = (DefaultMutableTreeNode)tree.getLastSelectedPathComponent();
		if(!node.isLeaf())
		{
			
		}else {
			eqys=(EquiptypeState)node.getUserObject();
			String nodename=eqys.getEquiptypename();
		
		  ori = new ArrayList<Boolean>();
		  updated = new ArrayList<Boolean>();		
		 		System.out.println(nodename+"======this is =======nodename");
		 		//List equipstate = DBManager.query("select t.state_id,nvl(t.state_code,''),nvl(t.state_name,''), nvl(t.switchstate_code,''),nvl(t.switchstate_name,''),equiptype_id from "+CBSystemConstants.opcardUser+"t_e_equiptypestate t where t.equiptype_id=( select EQUIPTYPE_ID from "+CBSystemConstants.opcardUser+"t_e_equiptype   where CIM_ID is not null and  EQUIPTYPE_NAME='"+nodename+"')");
//		 		List equipstate = DBManager.query("select t.* from "+CBSystemConstants.opcardUser+"t_e_equiptypestate t where t.equiptype_id=( select EQUIPTYPE_ID from "+CBSystemConstants.opcardUser+"t_e_equiptype   where CIM_ID is not null and  EQUIPTYPE_NAME='"+nodename+"')");
		 		//edit 2014.6.25
		 		List equipstate = DBManager.query(OPEService.getService().EquipStateDialogSql1()+nodename+"')");
		 		System.out.println(equipstate);
		 		 String[] tableHeads=new String[]{"状态ID","序号", "状态编码", "状态描述", "开合状态编码", "开合状态描述"};
				//DefaultTableModel dtm=(DefaultTableModel)table.getModel();
		 		
		 		  dtm=new DefaultTableModel(){
		 			 public boolean isCellEditable(int rowIndex, int columnIndex) {
						 if(columnIndex==0||columnIndex==1) return false;
						 return true;
					 
					 } 
		 		 };
		 		table.setModel(dtm);
		 		
		 		
		 		//dtm.fireTableStructureChanged();
		 		//dtm.fireTableDataChanged();
				dtm.setColumnIdentifiers(tableHeads);
		
		 		((DefaultTableModel) table.getModel())
		 		   .addTableModelListener(new TableModelListener(){
					public void tableChanged(TableModelEvent e) {
						// TODO Auto-generated method stub
						if (e.getType() == TableModelEvent.UPDATE) {  // 判断这行是不是被修改过
					        updated.set(e.getLastRow(), true);
					        System.out.println(updated+"====================updated.get()");
					        
					       }
						
					}
		 		});
		 		
		 		table.getTableHeader().getColumnModel().getColumn(0).setMaxWidth(0);
		        table.getTableHeader().getColumnModel().getColumn(0).setMinWidth(0);
		        table.getTableHeader().getColumnModel().getColumn(0).setPreferredWidth(0);
				
				for( int i = 0; i < equipstate.size(); i++){
					temp = (Map) equipstate.get(i);
					 Vector v = new Vector();
					 
					
		 		 /******转化成Vector***********/
		 		  v.add(temp.get("state_id"));
		 		 System.out.println(temp.get("state_id"));
		 		 
		 		  v.add(i+1);
		 		  v.add(temp.get("state_code"));
		 		  v.add(temp.get("state_name"));
		 		  v.add(temp.get("switchstate_code"));
		 		  v.add(temp.get("switchstate_name"));
		 		 v.add(temp.get("equiptype_id"));
		 		 ori.add(true);//这行在数据库中
		 		 updated.add(false);//没有修改过
		 		 //stateid.add(Integer.valueOf(temp.get("state_id").toString()));//得到设备类型ID
		 	  
		 		 System.out.println(temp.get("state_id")+"===-------------------===");
		 		 //System.out.println(stateid+"stateid ========stateid");
		 		 System.out.println(temp.get("equiptype_id")+"equiptype_id ========equiptype_id");
		 		 dtm.addRow(v);
		 		 TableColumn tc = table.getTableHeader().getColumnModel().getColumn(0);
		         tc.setMaxWidth(0);
		         tc.setPreferredWidth(0);
		         tc.setWidth(0);
		         tc.setMinWidth(0);
		         
				}
		 		  
		}
	}
	private void addbuttonActionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		Vector v = new Vector();
		v.add("");
		v.add(ori.size()+1);//自动填充序号
		v.add("");
		v.add("");
		v.add("");
		v.add("");
		v.add("");
		((DefaultTableModel) table.getModel()).addRow(v);// 加入空行
		//addi=ori.size();
		//int addi=table.getSelectedRow();
		System.out.println(ori.size()+"=======新增时被选中行为======");
	    //((DefaultTableModel) table.getModel()).setValueAt(addi+1, addi, 1);
		// table.getModel().setValueAt(ori.size()+1, ori.size(), 1);
		
		
		 ori.add(false);
		 updated.add(false);
		
		 //stateid.add(null);
	}
	private void delbuttonActionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		int si=table.getSelectedRow();// 首先得到这是哪一行
		System.out.println(si+"sisisiisisisisisi");
		String stateid=((String)table.getValueAt(si, 0));
		((DefaultTableModel) table.getModel()).removeRow(si);
           System.out.println(si+"sisisiisisisisisi");
          // System.out.println(stateid+"=========sisisiisisisisisi=======");
		 String sql="delete from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where state_id='"+stateid+"'";
		 DBManager.execute(sql);
		 JOptionPane.showMessageDialog(null, "删除成功");
		 ori.remove(si);
		 updated.remove(si);
		 //dtm.fireTableDataChanged();
		 }//刷新
		// stateid.remove(si);// 去掉所有的信息
	
	private void savebuttonActionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		 for (int i = 0; i < ori.size(); ++i) {
			   if (!ori.get(i)) {
				   System.out.println(ori.get(i)+"====================ori.get()"); 
			    String state_id = (String) table.getValueAt(i, 0);
			    //(i, 1行为序号)
			    String state_code = (String) table.getValueAt(i, 2);
			    String state_name = (String) table.getValueAt(i, 3);
			    String switchstate_code = (String) table.getValueAt(i, 4);
			    String switchstate_name = (String) table.getValueAt(i, 5);
			 
				String nodeid=eqys.getEquiptypeid();
				state_code = state_code==null?"":state_code;
				state_name = state_name==null?"":state_name;				
				switchstate_code = switchstate_code==null?"":switchstate_code;
				switchstate_name = switchstate_name==null?"":switchstate_name;
				//System.out.println(node+"======this is  nodeinfo===========");
				 System.out.println(nodeid+"=====================this is equiptypeid=========");
			    String sql = "insert into "+CBSystemConstants.opcardUser+"t_e_equiptypestate (state_id,state_code,state_name,switchstate_code,switchstate_name,equiptype_id) values('"
			      + java.util.UUID.randomUUID().toString() + "','" + state_code + "','" + state_name + "','"+switchstate_code+"','"+switchstate_name+"','"+eqys.getEquiptypeid()+"')";
			    DBManager.execute(sql);
			    
			 
			     ori.set(i, true);
			  
			   } else if (updated.get(i)) {
				  // int si=table.getSelectedRow();// 首先得到这是哪一行
					System.out.println(updated.get(i)+"====================updated.get()");
					
					
				    String state_id = (String) table.getValueAt(i, 0);
				    String state_code = (String) table.getValueAt(i, 2);
				    String state_name = (String) table.getValueAt(i, 3);
				    String switchstate_code = (String) table.getValueAt(i, 4);
				    String switchstate_name = (String) table.getValueAt(i, 5);
				    state_code=state_code==null?"":state_code;
				    state_name=state_name==null?"":state_name;
					switchstate_code=switchstate_code==null?"":switchstate_code;
					switchstate_name=switchstate_name==null?"":switchstate_name;
				    System.out.println(state_id+"stateid=====================stateid");
				    
			    String sql = "update "+CBSystemConstants.opcardUser+"t_e_equiptypestate set state_code='"+state_code+"',state_name='"+state_name
			      + "',switchstate_code = '" + switchstate_code + "',switchstate_name = '" + switchstate_name+"',equiptype_id='"+temp.get("equiptype_id")
			      + "' where state_id = '" + state_id+"'";
			    ;
			    DBManager.execute(sql);
			    updated.set(i, false);
			
			      //JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "更新成功");
				   //dtm.fireTableDataChanged();
			 
			   }
			 }
		 JOptionPane.showMessageDialog(null, "保存成功");
	}
	
	
	
	
	
	
	
	
	
	
	
}
