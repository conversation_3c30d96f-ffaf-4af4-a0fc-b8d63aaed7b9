package com.tellhow.czp.sysconfig;

public class UnitManagerFind {
	private String code="";//机构父id
	private String name="";//机构名称
	private String id="";//机构id
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public UnitManagerFind(String code,String name){
		this.code=code;
		this.name=name;
	}
	public UnitManagerFind(){
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	 
	@Override
	public String toString(){
		return this.name;
	}
	
	@Override
   public boolean equals(Object obj) {
       if (obj == null) {
           return false;
       }
       if (getClass() != obj.getClass()) {
           return false;
       }
       final UnitManagerFind other = (UnitManagerFind) obj;
       if (this.code != other.code && (this.code == null || !this.code.equals(other.code))) {
           return false;
       }
       other.setName(name);
       return true;
   }
		 
	public UnitManagerFind copy(){
		return new UnitManagerFind(this.code,this.name);
	}


}
