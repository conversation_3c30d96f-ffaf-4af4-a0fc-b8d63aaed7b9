package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.AbstractAction;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreeNode;
import javax.swing.tree.TreePath;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.dao.DeviceStateMentManager;

public class EquipOperationManageDialog extends JDialog {

	private JPanel contentPanel;
	private Map temp;
	private Map notemp;
	private DefaultMutableTreeNode root;
	private javax.swing.JComboBox comboBox,comboBoxcardtype;
	private javax.swing.JScrollPane scrollPane;
	private javax.swing.JTree tree;
	private javax.swing.JLabel lblNewLabel,cardtypelblNewLabel;
	private javax.swing.JPanel panel;
	private javax.swing.JPopupMenu pop;
	private JButton btnNewButton;
	private JButton btnNewButton_1;

	public EquipOperationManageDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		setPopMenu();
		this.setLocationCenter();
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
		this.setBounds( 400, 250,(w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
	}

	 //保护类型修改后的数据
    private DefaultComboBoxModel getcombobox1(){
    	List<CodeNameModel> devtypes=new ArrayList<CodeNameModel>();
//		List results = DBManager.query("select equiptype_flag ,equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.cim_id is not null");
		//edit 2014.6.25
    	List results = DBManager.query(OPEService.getService().EquipOperationManagerDialogSql());
		Map temp=new HashMap();  
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			cnm.setCode(StringUtils.ObjToString(temp.get("EQUIPTYPE_FLAG")));
			cnm.setName(StringUtils.ObjToString(temp.get("EQUIPTYPE_NAME")));
			devtypes.add(cnm);
		}
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		for(int i=0;i<devtypes.size();i++){
			cnm=devtypes.get(i);
			model.addElement(cnm);
		}
		return model;
    }
	
	public void initComponents() {
		this.setTitle("设备操作管理");
		contentPanel = (JPanel) getContentPane();
		panel = new JPanel();
		contentPanel.add(panel, BorderLayout.NORTH);
		
		lblNewLabel = new JLabel("New label");
		lblNewLabel.setText("设备类型    ");
		panel.add(lblNewLabel);
		
		comboBox = new JComboBox();
//		comboBox.setModel(InitDeviceTypeChockBox.getDevTypeCheckBox());
		comboBox.setModel(getcombobox1());
		comboBox.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                comboBoxActionPerformed(evt);
            }
        });
		panel.add(comboBox);
		
		cardtypelblNewLabel = new JLabel("New label");
		cardtypelblNewLabel.setText("        菜单类型    ");
		panel.add(cardtypelblNewLabel);
		
		comboBoxcardtype = new JComboBox();
		comboBoxcardtype.setModel(initcomboBoxcardtype());
		comboBoxcardtype.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent arg0) {
				comboBoxcardtypeActionPerformed(arg0);
			}
		});
		panel.add(comboBoxcardtype);
		
		btnNewButton = new JButton("上移");
		btnNewButton.addActionListener(new ButtonSyListener(this));
		panel.add(btnNewButton);
		
		btnNewButton_1 = new JButton("下移");
		btnNewButton_1.addActionListener(new ButtonXyListener(this));
		panel.add(btnNewButton_1);
		
		CodeNameModel cnm1=(CodeNameModel) comboBoxcardtype.getSelectedItem();
		String cardtype=cnm1.getCode();
		scrollPane = new JScrollPane();
		contentPanel.add(scrollPane, BorderLayout.CENTER);
		EquipTable czcd=new EquipTable("-1","操作菜单","0","0","-1","0","0","菜单"," ",cardtype);
		root=new DefaultMutableTreeNode(czcd);
		tree=new JTree(root);
		

		tree.addMouseListener(new TreeMouseListener(this));
		scrollPane.setViewportView(tree);
	}
	
	private DefaultComboBoxModel initcomboBoxcardtype(){
		List<CodeNameModel> devtypes=new ArrayList<CodeNameModel>();
		List results = DBManager.query(OPEService.getService().EquipOperationManagerDialogSqltype1());
		if(results.size()==0){
			results = DBManager.query(OPEService.getService().EquipOperationManagerDialogSqltype2());
		}
		Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			cnm.setCode(StringUtils.ObjToString(temp.get("code")));
			cnm.setName(StringUtils.ObjToString(temp.get("name")));
			devtypes.add(cnm);
		}
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		for(int i=0;i<devtypes.size();i++){
			cnm=devtypes.get(i);
			model.addElement(cnm);
		}
		return model;
	}


	public void comboBoxActionPerformed(ActionEvent evt) {
		CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
		CodeNameModel cnm1 = (CodeNameModel) comboBoxcardtype.getSelectedItem();
		initTree(cnm.getCode(),cnm1.getCode());
	}
	
	public void comboBoxcardtypeActionPerformed(ActionEvent evt){
		CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
		CodeNameModel cnm1 = (CodeNameModel) comboBoxcardtype.getSelectedItem();
		initTree(cnm.getCode(),cnm1.getCode());
	}
	
	public void setPopMenu(){
	
		pop=new JPopupMenu();
		JMenuItem menuItem = new JMenuItem("修改");
		menuItem.addActionListener(new PopMouseXgListener(this));
		pop.add(menuItem);
		menuItem = new JMenuItem("新增分割线");
		menuItem.addActionListener(new PopMouseXzFgxListener(this));
		pop.add(menuItem);
		menuItem = new JMenuItem("新增分类");
		menuItem.addActionListener(new PopMouseXzFlListener(this));
		pop.add(menuItem);
		menuItem = new JMenuItem("新增操作");
//		menuItem.addActionListener(new PopMouseXzCzListener(this));
		menuItem.addActionListener(new PopMouseNewXzCzListener(this));
		pop.add(menuItem);
		menuItem = new JMenuItem("上移");
		menuItem.addActionListener(new PopMouseSyListener(this));
		pop.add(menuItem);
		menuItem = new JMenuItem("下移");
		menuItem.addActionListener(new PopMouseXyListener(this));
		pop.add(menuItem);
		menuItem = new JMenuItem("删除");
		menuItem.addActionListener(new PopMouseScListener(this));
		pop.add(menuItem);
		

	}
	
	private void initTree(String code,String cardtype) {
		root.removeAllChildren();
		String opcode = CBSystemConstants.opCode;
		String sql = "select statecode,statename,statevalue,devicetypeid,parentcode,statetype,stateorder,secondtypeid,secondstate,cardbuildtype from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock='0' and cardbuildtype='"+cardtype+"' and opcode='"+opcode+"' and devicetypeid='"+code+"' and parentcode='0'  order by stateorder asc";
		List results = DBManager.query(sql);
		sql="select statecode,statename,statevalue,devicetypeid,parentcode,statetype,stateorder,secondtypeid,secondstate,cardbuildtype from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock='0' and cardbuildtype='"+cardtype+"' and opcode='"+opcode+"' and devicetypeid='"+code+"' and parentcode <> '0' and statetype=0 order by stateorder asc";
		temp=new HashMap();
		List noresults= DBManager.query(sql);
		notemp=new HashMap();
		for(int i = 0; i < results.size(); i++){
			temp = (Map) results.get(i);
			String statecode=StringUtils.ObjToString(temp.get("statecode"));
			String statename=StringUtils.ObjToString(temp.get("statename"));
			String statetype=StringUtils.ObjToString(temp.get("statetype"));
			String statevalue=StringUtils.ObjToString(temp.get("statevalue"));
			String devicetypeid=StringUtils.ObjToString(temp.get("devicetypeid"));
			String parentcode=StringUtils.ObjToString(temp.get("parentcode"));
			String stateorder=StringUtils.ObjToString(temp.get("stateorder"));
			String secondtypeid=StringUtils.ObjToString(temp.get("secondtypeid"));
			String secondstate=StringUtils.ObjToString(temp.get("secondstate"));
			String cardbuildtype=StringUtils.ObjToString(temp.get("cardbuildtype"));
			EquipTable equiptable=new EquipTable(statecode,statename,statetype,statevalue,devicetypeid,parentcode,stateorder,secondtypeid,secondstate,cardbuildtype);
			DefaultMutableTreeNode node = new  DefaultMutableTreeNode( equiptable) ;
			root.add(node);
			String parentcode0=String.valueOf((equiptable.getStatecode()));
			for(int k = 0; k < noresults.size(); k++){
				notemp = (Map) noresults.get(k);
				String statecode1=StringUtils.ObjToString(notemp.get("statecode"));
				String statename1=StringUtils.ObjToString(notemp.get("statename"));
				String statetype1=StringUtils.ObjToString(notemp.get("statetype"));
				String statevalue1=StringUtils.ObjToString(notemp.get("statevalue"));
				String devicetypeid1=StringUtils.ObjToString(notemp.get("devicetypeid"));
				String parentcode1=StringUtils.ObjToString(notemp.get("parentcode"));
				String stateorder1=StringUtils.ObjToString(notemp.get("stateorder"));
				String secondtypeid1=StringUtils.ObjToString(notemp.get("secondtypeid"));
				String secondstate1=StringUtils.ObjToString(notemp.get("secondstate"));
				String cardbuildtype1=StringUtils.ObjToString(notemp.get("cardbuildtype"));
				EquipTable equiptable1=new EquipTable(statecode1,statename1,statetype1,statevalue1,devicetypeid1,parentcode1,stateorder1,secondtypeid1,secondstate1,cardbuildtype1);
				DefaultMutableTreeNode morenode = new  DefaultMutableTreeNode( equiptable1) ;
				if(parentcode1.equals(parentcode0)){
					node.add(morenode);
				}
			}
		}
		tree.repaint();
		tree.updateUI();
		expandAll(tree,new TreePath(root),true);
	}
	
	public void shuaxin(){
		CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
		CodeNameModel cnm1 = (CodeNameModel) comboBoxcardtype.getSelectedItem();
		initTree(cnm.getCode(),cnm1.getCode());
	} 
	
	
	
	private static void expandAll(JTree tree, TreePath parent, boolean expand) {  
		  
        TreeNode node = (TreeNode) parent.  
                        getLastPathComponent();  
        if (node.getChildCount() >= 0) {  
            for (Enumeration e = node.children(); e.hasMoreElements(); ) {  
                TreeNode n = (TreeNode) e.nextElement();  
                TreePath path = parent.pathByAddingChild(n);  
                expandAll(tree, path, expand);  
            }  
        }   
  
        if (expand) {  
            tree.expandPath(parent);  
        } else {  
            tree.collapsePath(parent);  
        }  
    }  
	
	
	class PopMouseXyListener extends AbstractAction{
		private EquipOperationManageDialog eomd;
		public PopMouseXyListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent arg0) {
			DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
			EquipTable table=new EquipTable();
			table=(EquipTable)path.getUserObject();
			String statecode=table.getStatecode();
			int stateorder=Integer.parseInt(table.getStateorder());
			DefaultMutableTreeNode paths=path.getNextSibling();
		    if(paths==null){
		    	return;
		    }
			EquipTable table1=new EquipTable();
			table1=(EquipTable)paths.getUserObject();
			String statecode1=table1.getStatecode();
			int stateorder1 = Integer.parseInt(table1.getStateorder());
			DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set stateorder='"+stateorder1+"' where statecode='"+statecode+"'");
			DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set stateorder='"+stateorder+"' where statecode='"+statecode1+"'");
			shuaxin();
	
		}
		
	}
	
	class ButtonXyListener implements ActionListener{
		private EquipOperationManageDialog eomd;
		public ButtonXyListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent arg0) {
			DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
			if(path==null){
				ShowMessage.view("请选中一个节点");
				return;
			}
			EquipTable table=new EquipTable();
			EquipTable tablecopy=new EquipTable();
			table=(EquipTable)path.getUserObject();
			tablecopy=table.clone();
			String statecode=table.getStatecode();
			int stateorder=Integer.parseInt(table.getStateorder());
			DefaultMutableTreeNode paths=path.getNextSibling();
		    if(paths==null){
		    	return;
		    }
			EquipTable table1=new EquipTable();
			table1=(EquipTable)paths.getUserObject();
			String statecode1=table1.getStatecode();
			int stateorder1 = Integer.parseInt(table1.getStateorder());
			if((stateorder==stateorder1)&&stateorder>0){
				stateorder1=stateorder1+1;
			}
			DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set stateorder='"+stateorder1+"' where statecode='"+statecode+"'");
			tablecopy.setStateorder(String.valueOf(stateorder1));
			DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set stateorder='"+stateorder+"' where statecode='"+statecode1+"'");
			shuaxin();
			Enumeration e = root.breadthFirstEnumeration();
			while (e.hasMoreElements()) {
		       DefaultMutableTreeNode node = (DefaultMutableTreeNode) e.nextElement();
		       EquipTable eqt=new EquipTable();
		       eqt=(EquipTable) node.getUserObject();
		       String eqcode=eqt.getStatecode();
		       if (eqcode.equals(tablecopy.getStatecode())){
		    	  tree.setSelectionPath(new TreePath(node.getPath()));  
		    	  tree.scrollPathToVisible(new TreePath(node.getPath()));
		    	  tree.makeVisible(new TreePath(node.getPath()));
		       }
			}
		}
		
	}
	
	class PopMouseSyListener extends AbstractAction{
		private EquipOperationManageDialog eomd;
		public PopMouseSyListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent arg0) {
			DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
			EquipTable table=new EquipTable();
			table=(EquipTable)path.getUserObject();
			String statecode=table.getStatecode();
			int stateorder=Integer.parseInt(table.getStateorder());
		    DefaultMutableTreeNode paths=path.getPreviousSibling();
		    if(paths==null){
		    	return;
		    }
		    EquipTable table1=new EquipTable();
			table1=(EquipTable)paths.getUserObject();
			String statecode1=table1.getStatecode();
			int stateorder1 = Integer.parseInt(table1.getStateorder());
			DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set stateorder='"+stateorder1+"' where statecode='"+statecode+"'");
			DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set stateorder='"+stateorder+"' where statecode='"+statecode1+"'");
			shuaxin();
		}
		
	}
	
	class ButtonSyListener implements ActionListener{
		private EquipOperationManageDialog eomd;
		public ButtonSyListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent arg0) {
			DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
			if(path==null){
				ShowMessage.view("请选中一个节点");
				return;
			}
			EquipTable table=new EquipTable();
			EquipTable tablecopy=new EquipTable();
			table=(EquipTable)path.getUserObject();
			tablecopy=table.clone();
			String statecode=table.getStatecode();
			int stateorder=Integer.parseInt(table.getStateorder());
		    DefaultMutableTreeNode paths=path.getPreviousSibling();
		    if(paths==null){
		    	return;
		    }
		    EquipTable table1=new EquipTable();
			table1=(EquipTable)paths.getUserObject();
			String statecode1=table1.getStatecode();
			int stateorder1 = Integer.parseInt(table1.getStateorder());
			if((stateorder1==stateorder)&&stateorder1>0){
				stateorder1=stateorder-1;
			}
			DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set stateorder='"+stateorder1+"' where statecode='"+statecode+"'");
			tablecopy.setStateorder(String.valueOf(stateorder1));
			DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set stateorder='"+stateorder+"' where statecode='"+statecode1+"'");
			shuaxin();
			Enumeration e = root.breadthFirstEnumeration();
			while (e.hasMoreElements()) {
		       DefaultMutableTreeNode node = (DefaultMutableTreeNode) e.nextElement();
		       EquipTable eqt=new EquipTable();
		       eqt=(EquipTable) node.getUserObject();
		       String eqcode=eqt.getStatecode();
		       if (eqcode.equals(tablecopy.getStatecode())){
		    	  tree.setSelectionPath(new TreePath(node.getPath()));  
		    	  tree.scrollPathToVisible(new TreePath(node.getPath()));
		    	  tree.makeVisible(new TreePath(node.getPath()));
		       }
			}
		}
		
	}
	
	class PopMouseScListener extends AbstractAction{
		private EquipOperationManageDialog eomd;
		public PopMouseScListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
			CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
			String code=cnm.getCode();
			EquipTable table=new EquipTable();
			table=(EquipTable)path.getUserObject();
			String statecode=table.getStatecode();
			int message=JOptionPane.showConfirmDialog(null, "确认是否删除","删除提示框",JOptionPane.YES_NO_OPTION);
			if(message==JOptionPane.OK_OPTION){
				DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set islock='1' where statecode='"+statecode+"'");
				shuaxin();
			}
		}
		
	}
	
	class PopMouseXzFgxListener extends AbstractAction{
		private EquipOperationManageDialog eomd;
		public PopMouseXzFgxListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
			String code=cnm.getCode();
			CodeNameModel cnm1 = (CodeNameModel) comboBoxcardtype.getSelectedItem();
			String cardtype=cnm1.getCode();
			EquipManagerDialogF drm=new EquipManagerDialogF(eomd,SystemConstants.getMainFrame(),true,code,cardtype);
			drm.addBtnNewButtonListenernxz();
			drm.setVisible(true);
		}
	}
	
	class PopMouseXzFlListener extends AbstractAction{
		private EquipOperationManageDialog eomd;
		public PopMouseXzFlListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
			String code=cnm.getCode();
			CodeNameModel cnm1 = (CodeNameModel) comboBoxcardtype.getSelectedItem();
			String cardtype=cnm1.getCode();
			EquipManagerDialogG drm=new EquipManagerDialogG(eomd,SystemConstants.getMainFrame(),true,code,cardtype);
			drm.addBtnNewButtonListenernxz();
			drm.setVisible(true);
		}
	}
	//新新增操作
	class PopMouseNewXzCzListener extends AbstractAction{
		private EquipOperationManageDialog eomd;
		public PopMouseNewXzCzListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
			EquipTable table=new EquipTable();
			table=(EquipTable)path.getUserObject();
			CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
			String code=cnm.getCode();
			CodeNameModel cnm1 = (CodeNameModel) comboBoxcardtype.getSelectedItem();
			String cardtype=cnm1.getCode();
			int n=0;
			EquipTable czcd=null;
			if(table.getStatetype().equals("0")&&table.getParentcode().equals("0")){
				czcd=new EquipTable("0","主目录","1","-1",code,"0","0"," "," ",cardtype);
			}else{
				String sql = "select statecode,statename,statevalue,devicetypeid,parentcode,statetype,stateorder,secondtypeid ,secondstate,cardbuildtype from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock='0' and  opcode='"+CBSystemConstants.opCode+"' and devicetypeid='"+code+"' and parentcode='0' and statetype=1 and cardbuildtype='"+cardtype+"' and statecode='"+table.getStatecode()+"' order by stateorder asc ";
				List more = DBManager.query(sql);
				Map flmap=new HashMap();
				for(int i = 0; i < more.size(); i++){
					temp = (Map) more.get(i);
					String statecode=StringUtils.ObjToString(temp.get("statecode"));
					String statename=StringUtils.ObjToString(temp.get("statename"));
					String statetype=StringUtils.ObjToString(temp.get("statetype"));
					String statevalue=StringUtils.ObjToString(temp.get("statevalue"));
					String devicetypeid=StringUtils.ObjToString(temp.get("devicetypeid"));
					String parentcode=StringUtils.ObjToString(temp.get("parentcode"));
					String stateorder=StringUtils.ObjToString(temp.get("stateorder"));
					String secondtypeid=StringUtils.ObjToString(temp.get("secondtypeid"));
					String secondstate=StringUtils.ObjToString(temp.get("secondstate"));
					String cardbuildtype=StringUtils.ObjToString(temp.get("cardbuildtype"));
					czcd=new EquipTable(statecode,statename,statetype,statevalue,devicetypeid,parentcode,stateorder,secondtypeid,secondstate,cardbuildtype);
				}
			}
			EquipManagerDialogC drm=new EquipManagerDialogC(czcd,code,eomd,SystemConstants.getMainFrame(),true,cardtype);
			drm.addBtnNewButtonListenerxz();
			drm.setVisible(true);
			
		}
	}
	//原新增操作
	class PopMouseXzCzListener extends AbstractAction{
		private EquipOperationManageDialog eomd;
		public PopMouseXzCzListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
			EquipTable table=new EquipTable();
			table=(EquipTable)path.getUserObject();
			String statename=table.getStatename();
			CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
			String code=cnm.getCode();
			CodeNameModel cnm1 = (CodeNameModel) comboBoxcardtype.getSelectedItem();
			String cardtype=cnm1.getCode();
			EquipManagerCzDialog emcd=new EquipManagerCzDialog(eomd,code, SystemConstants.getMainFrame(),true,cardtype);
			emcd.setVisible(true);
		}
		
	}	

	class PopMouseXgListener extends AbstractAction{
		private EquipOperationManageDialog eomd;
		public PopMouseXgListener(EquipOperationManageDialog eomd){
			this.eomd=eomd;
		}
		@Override
		public void actionPerformed(ActionEvent arg0) {
			DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
			CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
			String code=cnm.getCode();
			CodeNameModel cnm1 = (CodeNameModel) comboBoxcardtype.getSelectedItem();
			String cardtype=cnm1.getCode();
			EquipTable table=new EquipTable();
			table=(EquipTable)path.getUserObject();
			String statecode=table.getStatecode();
			String statename=table.getStatename();
			String statetype=table.getStatetype();
			if(statename!="操作菜单"){
				if(statetype.equals("2")){
					EquipManagerDialogF drm=new EquipManagerDialogF(eomd,SystemConstants.getMainFrame(),true,code,statecode,cardtype);
					drm.setVisible(true);
				}else if(statetype.equals("1")){
					EquipManagerDialogG drm=new EquipManagerDialogG(eomd, SystemConstants.getMainFrame(),true,code,statecode,cardtype);
					drm.addBtnNewButtonListenerxg();
					drm.setVisible(true);
				}else{
					
					EquipManagerDialogC drm=new EquipManagerDialogC(code,eomd, SystemConstants.getMainFrame(),true,statecode,cardtype);
					drm.addBtnNewButtonListenerxg();
					drm.setVisible(true);
				}
			}
		}
	}
	
	class TreeMouseListener extends MouseAdapter {
		private EquipOperationManageDialog eomd;
		public TreeMouseListener(EquipOperationManageDialog eomd) {
			this.eomd = eomd;
		}
		@Override
		public void mouseClicked(MouseEvent e) {
			if(e.getButton()==e.BUTTON3){
				if(tree.isSelectionEmpty()!=true){
					DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
					
					EquipTable table=new EquipTable();
					table=(EquipTable)path.getUserObject();
					CodeNameModel cnm = (CodeNameModel) comboBox.getSelectedItem();
					String statename=cnm.getName();
					if(statename!="请选择"){
						pop.show(tree,e.getX(),e.getY());
					}
				}
			}
		}
	}
	
	public static void main(String[] args) {
		CBSystemConstants.unitCode="1";
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				EquipOperationManageDialog dialog = new EquipOperationManageDialog(new javax.swing.JFrame(), true);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}
}
