package com.tellhow.czp.sysconfig;

import java.awt.Color;
import java.awt.Component;
import java.awt.Font;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.text.DateFormatSymbols;
import java.util.Calendar;
import java.util.GregorianCalendar;

import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableModel;

/**
 * 一个小的日历
 * 用于输入合法时间
 * <AUTHOR>
 */
public class SelfCalendar extends JDialog{
	private static JPanel mainPanel;           ///主面板,用来放置 各个组件
	private static JTextField textYear;      //文本框,显示输入年 份
	private static JComboBox comboMonth;   //下拉框,显示输入月份
	private JButton btnYearUp;        ///按钮,年份加一
	private JButton btnYearDown;    //按钮,年份减一
	private JPanel topPanel;            ///顶部面板,放置 textYear,comboMonth,btnYearUp,btnYearDown
	private JScrollPane scrollPane;
	private static  JTable  tbCalendar;          //放置日历主体
	private JLabel  nowDate;             //显示当前年月日
	private JPanel nowDatePanel=new JPanel();
	private String[] strMonth= 
{"01","02","03","04","05","06","07","08","09","10","11","12"};
	private Object[] col;                      ////表列名,星期几
	private Object [][] data;// 
	private TableModel tableModel;
	private static  GregorianCalendar gc=new GregorianCalendar();
	private int selfYear;
	private int selfMonth;
	private static String selfDate;
	private 	int k=0;
	private boolean flag;
	private JTextField txtTime;
	
	/**
	 * 自定义日历的构造方法
	 */
	 
	public SelfCalendar(JTextField txtTime)
	{
		//super(frame,true);
		this.txtTime=txtTime;
		this.setSize(190, 200);
        this.setModal(true);            ///设置为模式窗口
        this.setFocusable(true);      ///
      //  this.setUndecorated(true);  ///去掉修饰
        this.setLocationRelativeTo(null);   ///简单的居中
		this.init();
		this.addListener();
		this.setDefaultCloseOperation(DISPOSE_ON_CLOSE);
		this.setLayout(null);
		this.setVisible(true);
		this.setResizable(false);

		
	}
	
	private void init(){
		mainPanel=new JPanel();
		mainPanel.setBounds(0,0,180, 170);
		mainPanel.setLayout(null);
		mainPanel.setBackground(Color.BLACK);
		topPanel=new JPanel();
		topPanel.setBounds(2,2,176,35);
		topPanel.setBackground(new Color(255,255,225));
		topPanel.setLayout(null);
		textYear=new JTextField();
		textYear.setBounds(6, 6, 34,20);
		selfYear=gc.get(Calendar.YEAR);
		selfMonth=gc.get(Calendar.MONTH);
		textYear.setText(selfYear+"");
		textYear.setEditable(false);
		Font font=new Font("宋体",Font.BOLD,8);
		btnYearUp=new JButton("▲");                           
           //年份加按钮,
		btnYearUp.setMargin(new Insets(0,0,0,0));
		
		btnYearUp.setFont(font);
		btnYearDown=new JButton("▼");                         
    // 年份减按钮
		btnYearDown.setFont(font);
		btnYearDown.setMargin(new Insets(0,0,0,0));
		btnYearUp.setBounds(40, 6, 20, 10);                    
        //按钮定位
		btnYearDown.setBounds(40, 16, 20, 10); 
		
		comboMonth=new JComboBox(strMonth);  ///月份选择
		comboMonth.setMaximumRowCount(4);
		comboMonth.setSelectedIndex(selfMonth);
		comboMonth.setBounds(65, 6, 65, 20);
	
		data=getdata(selfYear,selfMonth);                /// 设置默认显示时间
		col=new Object[7];
		for(int i=0;i<7;i++)
		{
			col[i]=getWeek()[i+1].substring(2);
		}
		
		tableModel=new DefaultTableModel(data, col);
	    tbCalendar=new JTable(tableModel){///日历显示部分

			@Override
			public boolean isCellEditable(int row, int  
column) {
				return false;
			}

	    };                                       
	    tbCalendar.setRowSelectionAllowed(false);
	    DefaultTableCellRenderer todayrender=new  
DefaultTableCellRenderer(){    ///设置背景色
			@Override
			public Component  
getTableCellRendererComponent(JTable table,
					Object value, boolean  
isSelected, boolean hasFocus,
					int row, int column) {
				Object o=tbCalendar.getValueAt(row,  
column);
				if(o!=null && o.equals(gc.get 
(Calendar.DATE)+""))
				{
					setBackground(new Color 
(179,144,82));
				}
				else setBackground(new Color 
(255,255,225));
				return  
super.getTableCellRendererComponent(table, value, isSelected,  
hasFocus,
						row, column);
			}
	    	
	    };
	    tbCalendar.setDefaultRenderer(Object.class, todayrender);
	    tbCalendar.setColumnSelectionAllowed(false);
	    tbCalendar .getTableHeader().setReorderingAllowed(false);  
 //设置列顺序不可改变
	    tbCalendar .getTableHeader().setResizingAllowed(false);    
   /////设置列大小不可改变
	    nowDate=new JLabel();
	    nowDate.setText("今天是:"+gc.get(Calendar.YEAR)+"年"+
	                                       ( gc.get 
(Calendar.MONTH)+1)+"月"+gc.get(Calendar.DATE)+"日");
	    nowDate.setHorizontalAlignment(JLabel.CENTER);
	    nowDate.setBounds(0, 0, 176, 20);
	    nowDatePanel.setBounds(2, 148, 176, 20);
	    nowDatePanel.setLayout(null);
	    nowDatePanel.setBackground(new Color(255,255,225));
        nowDatePanel.add(nowDate);
        /////////////////////////////////传来的文本框设置为默认的今天
        txtTime.setText(gc.get(Calendar.YEAR)+"-"+
	                                       ( gc.get 
(Calendar.MONTH)+1)+"-"+gc.get(Calendar.DATE));
		topPanel.add(textYear);
		topPanel.add(btnYearUp);
		topPanel.add(btnYearDown);
		topPanel.add(comboMonth);
		scrollPane=new JScrollPane(tbCalendar);
		scrollPane.setBounds(2, 32, 176, 120);
		mainPanel.add(topPanel);
		mainPanel.add(scrollPane);
		mainPanel.add(nowDatePanel);
		this.add(mainPanel);
		
	}
	
	private void addListener()
	{
	   /*
	    * 事件监听,在点击年份的文本域时,允许编辑
	    */
	
		textYear.addMouseListener(new MouseAdapter() {
			@Override
			public void mouseClicked(MouseEvent e) {
				super.mouseClicked(e);
				textYear.setEditable(true);
			}
		});
		/*
		 * 回车时,更新
		 */
		textYear.addKeyListener(new KeyAdapter() {
			@Override
			public void keyReleased(KeyEvent e) {
				super.keyReleased(e);
				if(e.getKeyCode()==KeyEvent.VK_ENTER)
				{ selfYear=Integer.parseInt 
(textYear.getText());
					setCalendar( );
					textYear.setEditable(false);
					int  
row=tbCalendar.getSelectedRow();
					int  
column=tbCalendar.getSelectedColumn();
					 setSelfDate(row,column);
				}
			}
		});
		/*
		 * 按钮年份加一,更新
		 */
	  btnYearUp.addActionListener(new ActionListener() {
		
		@Override
		public void actionPerformed(ActionEvent e) {
		selfYear+=1;
		textYear.setText(selfYear+"");
		setCalendar();
		int row=tbCalendar.getSelectedRow();
		int column=tbCalendar.getSelectedColumn();
		 setSelfDate(row,column);
		}
	})	;
	  /*
	   * 按钮年份减一,更新
	   */
	  btnYearDown.addActionListener(new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
		selfYear-=1;
		textYear.setText(selfYear+"");
		setCalendar();
		int row=tbCalendar.getSelectedRow();
		int column=tbCalendar.getSelectedColumn();
		 setSelfDate(row,column);
		}
	});
	  /*
	   * 月份选择,更新
	   */
	  comboMonth.addItemListener(new ItemListener() {
		@Override
		public void itemStateChanged(ItemEvent e) {
			selfMonth=comboMonth.getSelectedIndex();
			setCalendar();
			int row=tbCalendar.getSelectedRow();
			int column=tbCalendar.getSelectedColumn();
			 setSelfDate(row,column);
		}
	});
	tbCalendar.addMouseListener(new MouseAdapter() {

		@Override
		public void mouseClicked(MouseEvent e) {
			super.mouseClicked(e);
			getSelfCalendar();
			SelfCalendar.this.dispose();
		}
		
	});
	  
	}
	
	/**
	 * 得到日历的JTable,对其点击事件重写
	 * @return  tbCalendar
	 */
	public  JTable getTable()
	{
		return tbCalendar;
	}
	
	/**
	 *   得到选择的事件
	 * @return  String date
	 */
	public   String getSelfCalendar()
	{
		int row=tbCalendar.getSelectedRow();
		int column=tbCalendar.getSelectedColumn();
		 setSelfDate(row,column);
		 //txtTime.setText(selfDate);
		    return selfDate;
	}
     /**
      * 将选择的单元格的时间设置为selfDate
      * @param row
      * @param column
      */
    private  void setSelfDate(int row,int column)
    {   String date;
         if(row==-1||column==-1||tbCalendar.getValueAt(row,  
column).equals(" "))  
			   date=textYear.getText 
()+"-"+comboMonth.getSelectedItem()+"-"+gc.get(Calendar.DATE);
         else 
		{
			 date=textYear.getText 
()+"-"+comboMonth.getSelectedItem()+"-"+tbCalendar.getValueAt(row,  
column);
		}  
         
         selfDate=date;
         txtTime.setText(selfDate);  ////文本框设置时间
    }
/**
 * 日历的Table显示部分
 */
	private  void setCalendar()
	{
		DefaultTableModel dtm=(DefaultTableModel)  
tbCalendar.getModel();
		data=getdata(selfYear,selfMonth);                /// 设置默认显示时间
		for(int i=0;i<7;i++)
		{
			col[i]=getWeek()[i+1].substring(2);
		}
		dtm.setDataVector(data, col);
	   
	}
	
	/**
	 * 得到日历的标头(星期几)
	 * @return  
	 */
	private String[] getWeek()
	{GregorianCalendar d=new GregorianCalendar();
		String [] weekdayNames=new DateFormatSymbols 
().getShortWeekdays();
		return  weekdayNames;
	}
	
	/**
	 * 得到日历的主体显示部分
	 * @param selfyear
	 * @param selfmonth
	 * @return
	 */
	private String[][] getdata(int selfyear,int selfmonth)
	{
        GregorianCalendar d=new GregorianCalendar();
        
        d.set(Calendar.YEAR,selfyear );d.set(Calendar.MONTH,  
selfmonth);
		int today=d.get(Calendar.DAY_OF_MONTH);
		int month=d.get(Calendar.MONTH);
		d.set(Calendar.DAY_OF_MONTH,1);
		int weekday=d.get(Calendar.DAY_OF_WEEK);
		int firstDayOfWeek=d.getFirstDayOfWeek();
		int indent =0;
		String[][] data=new String[6][7];
		while(weekday!=firstDayOfWeek)
		{
			indent++;
			d.add(Calendar.DAY_OF_MONTH, -1);
			weekday=d.get(Calendar.DAY_OF_WEEK);
		}
		do
		{
			d.add(Calendar.DAY_OF_MONTH,1);
			weekday=d.get(Calendar.DAY_OF_WEEK);
		}while (weekday!=firstDayOfWeek);
		d.set(Calendar.DAY_OF_MONTH, 1);
		 int i=0;
		 do
		{ int j=0;
		   for(j=0;j<7;j++)
		   {		if(i==0&&j<indent)
					{
						data[0][j]=" ";
					}
		   else if(d.get(Calendar.MONTH)==month){
			    int day=d.get(Calendar.DAY_OF_MONTH);
		    data[i][j]=day+"";
		    d.add(Calendar.DAY_OF_MONTH, 1);
		   }  
		   else   if(d.get(Calendar.MONTH)>month)
		   {
			   data[i][j]=" ";
		   }

		   }
		   i++;
		}while (i<6);
		return data;
	}
	public static void main(String[] args) {
	    new SelfCalendar(new JTextField());
	     
	}
}

