package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.Font;
import java.awt.GridLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Map;
import java.util.Vector;

import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.SwingConstants;
import javax.swing.UIManager;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class UnitManageSplitRightPDialog{
	
	/**
	 * 单元管理右边布局界面
	 */
	
	private static JPanel conterPanel=new JPanel();
	private static JPanel rightPanel=new JPanel();
	private static JPanel rightwestPanel=new JPanel();
	private static JButton save=new JButton("保存");
	private static JButton cancel=new JButton("取消");
	//private static JButton find=new JButton("查看");
	private static int mark=0;//1表示修改，2表示新增
	private static UnitManager unitManager=new UnitManager();//单位管理实体对象
	private static UnitManagerFind unitManagerFind=new UnitManagerFind();//单位管理查找实体对象
	private static UnitManagerEdit unitManagerEdit=new UnitManagerEdit();//单位管理修改实体对象
	//机构字段
	private static JTextField nodecodeField=new JTextField();//机构编码
	private static JTextField nodenameField=new JTextField();//机构名称
	private static JComboBox nodetypecomboBox=new JComboBox();//机构类型
	private static JComboBox nodeattributecomboBox=new JComboBox();//机构属性
	private static JComboBox isenabledcomboBox=new JComboBox();//机构是否启用
	private static JComboBox gradecomboBox=new JComboBox();//机构级别
	//机构集合
	private static Vector<UnitManager> organtypelist=new Vector<UnitManager>();//机构类型
	private static Vector<UnitManager> organkindlist=new Vector<UnitManager>();//机构属性
	private static Vector<UnitManager> isenabledlist=new Vector<UnitManager>();//机构是否停用
	private static Vector<UnitManager> organgradelist=new Vector<UnitManager>();//机构级别
	//机构字段get,set
	public static JTextField getNodecodeField() {
		return nodecodeField;
	}
	public static void setNodecodeField(JTextField nodecodeField) {
		UnitManageSplitRightPDialog.nodecodeField = nodecodeField;
	}
	public static JTextField getNodenameField() {
		return nodenameField;
	}
	public static void setNodenameField(JTextField nodenameField) {
		UnitManageSplitRightPDialog.nodenameField = nodenameField;
	}
	public static JComboBox getNodetypecomboBox() {
		return nodetypecomboBox;
	}
	public static void setNodetypecomboBox(JComboBox nodetypecomboBox) {
		UnitManageSplitRightPDialog.nodetypecomboBox = nodetypecomboBox;
	}
	public static JComboBox getNodeattributecomboBox() {
		return nodeattributecomboBox;
	}
	public static void setNodeattributecomboBox(JComboBox nodeattributecomboBox) {
		UnitManageSplitRightPDialog.nodeattributecomboBox = nodeattributecomboBox;
	}
	public static JComboBox getIsenabledcomboBox() {
		return isenabledcomboBox;
	}
	public static void setIsenabledcomboBox(JComboBox isenabledcomboBox) {
		UnitManageSplitRightPDialog.isenabledcomboBox = isenabledcomboBox;
	}
	public static JComboBox getGradecomboBox() {
		return gradecomboBox;
	}
	public static void setGradecomboBox(JComboBox gradecomboBox) {
		UnitManageSplitRightPDialog.gradecomboBox = gradecomboBox;
	}
	//实体类
	public static UnitManager getUnitManager() {
		return unitManager;
	}
	public static void setUnitManager(UnitManager unitManager) {
		UnitManageSplitRightPDialog.unitManager = unitManager;
	}
	public JPanel getConterPanel() {
		return conterPanel;
	}
	public void setConterPanel(JPanel conterPanel) {
		this.conterPanel = conterPanel;
	}
	public static UnitManagerFind getUnitManagerFind() {
		return unitManagerFind;
	}
	public static void setUnitManagerFind(UnitManagerFind unitManagerFind) {
		UnitManageSplitRightPDialog.unitManagerFind = unitManagerFind;
	}
	public static UnitManagerEdit getUnitManagerEdit() {
		return unitManagerEdit;
	}
	public static void setUnitManagerEdit(UnitManagerEdit unitManagerEdit) {
		UnitManageSplitRightPDialog.unitManagerEdit = unitManagerEdit;
	}
	//保存，取消按钮
	public static JButton getSave() {
		return save;
	}
	public static void setSave(JButton save) {
		UnitManageSplitRightPDialog.save = save;
	}
	public static JButton getCancel() {
		return cancel;
	}
	public static void setCancel(JButton cancel) {
		UnitManageSplitRightPDialog.cancel = cancel;
	}
	public UnitManageSplitRightPDialog(){
		 //testList=new ArrayList<String>();
		 //System.out.println("start.....>UnitManager.getCode()......................>"+UnitManager.getCode());
		 /*JLabel nodecode=new JLabel("机构编码:");
		 nodecode.setVerticalAlignment(SwingConstants.TOP);
		 nodecode.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 //nodecode.setSize(1000, 10);
		 nodecode.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel nodename=new JLabel("机构名称:");
		 nodename.setVerticalAlignment(SwingConstants.TOP);
		 nodename.setHorizontalAlignment(SwingConstants.CENTER);
		 nodename.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 JLabel nodetype=new JLabel("机构类型:");
		 nodetype.setVerticalAlignment(SwingConstants.TOP);
		 nodetype.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 nodetype.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel nodeattribute=new JLabel("机构属性:");
		 nodeattribute.setVerticalAlignment(SwingConstants.TOP);
		 nodeattribute.setHorizontalAlignment(SwingConstants.CENTER);
		 nodeattribute.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 JLabel isenabled=new JLabel("机构启用:");
		 isenabled.setVerticalAlignment(SwingConstants.TOP);
		 isenabled.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 isenabled.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel grade=new JLabel("机构级别:");
		 grade.setVerticalAlignment(SwingConstants.TOP);
		 grade.setHorizontalAlignment(SwingConstants.CENTER);
		 grade.setFont(new Font("华文细黑", Font.PLAIN, 16));
		  
		 Vector<UnitManager> organtypelist=new Vector<UnitManager>();//机构类型
		 Vector<UnitManager> organkindlist=new Vector<UnitManager>();//机构属性
		 Vector<UnitManager> isenabledlist=new Vector<UnitManager>();//机构是否停用
		 Vector<UnitManager> organgradelist=new Vector<UnitManager>();//机构级别
	   	 
		 //机构类型
		 UnitManager 	UnitManager0= new UnitManager();
	   	 UnitManager	UnitManager1= new UnitManager();
		 UnitManager0.setCode("0");
		 UnitManager0.setName("单位");
		 organtypelist.add(UnitManager0);
		 UnitManager1.setCode("1");
		 UnitManager1.setName("部门");
		 organtypelist.add(UnitManager1);
		 
		 //机构属性
		 UnitManager 	UnitManager10= new UnitManager();
	   	 UnitManager	UnitManager11= new UnitManager();
	   	 UnitManager 	UnitManager12= new UnitManager();
		 UnitManager10.setCode("0");
		 UnitManager10.setName("调度");
		 organkindlist.add(UnitManager10);
		 UnitManager11.setCode("1");
		 UnitManager11.setName("监控");
		 organkindlist.add(UnitManager11);
		 UnitManager12.setCode("2");
		 UnitManager12.setName("厂站");
		 organkindlist.add(UnitManager12);
		 
		 //机构是否停用
		 UnitManager 	UnitManager20= new UnitManager();
	   	 UnitManager	UnitManager21= new UnitManager();
		 UnitManager20.setCode("0");
		 UnitManager20.setName("启用");
		 isenabledlist.add(UnitManager20);
		 UnitManager21.setCode("1");
		 UnitManager21.setName("未启用");
		 isenabledlist.add(UnitManager21);
		 //机构级别
		 UnitManager 	UnitManager30= new UnitManager();
	   	 UnitManager	UnitManager31= new UnitManager();
	   	 UnitManager 	UnitManager32= new UnitManager();
	   	 UnitManager	UnitManager33= new UnitManager();
	   	 UnitManager 	UnitManager34= new UnitManager();
		 UnitManager30.setCode("0");
		 UnitManager30.setName("国家级");
		 organgradelist.add(UnitManager30);
		 UnitManager31.setCode("1");
		 UnitManager31.setName("区域级");
		 organgradelist.add(UnitManager31);
		 UnitManager32.setCode("2");
		 UnitManager32.setName("省级");
		 organgradelist.add(UnitManager32);
		 UnitManager33.setCode("3");
		 UnitManager33.setName("地级");
		 organgradelist.add(UnitManager33);
		 UnitManager34.setCode("4");
		 UnitManager34.setName("县级");
		 organgradelist.add(UnitManager34);
		 final JTextField nodecodeField=new JTextField();//机构编码
		 nodecodeField.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 final JTextField nodenameField=new JTextField();//机构名称
		 nodenameField.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 final JComboBox nodetypecomboBox=new JComboBox(organtypelist);//机构类型
		 nodetypecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 final JComboBox nodeattributecomboBox=new JComboBox(organkindlist);//机构属性
		 nodeattributecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 final JComboBox isenabledcomboBox=new JComboBox(isenabledlist);//机构是否启用
		 isenabledcomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 final JComboBox gradecomboBox=new JComboBox(organgradelist);//机构级别
		 gradecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 conterPanel.add(rightPanel, BorderLayout.NORTH);
		 conterPanel.add(rightwestPanel, BorderLayout.SOUTH);
		 rightPanel.setLayout(new GridLayout(2, 3, 0, 10));
		 rightPanel.add(nodecode);
		 rightPanel.add(nodecodeField);
		 rightPanel.add(nodename);
		 rightPanel.add(nodenameField);
		 
		 rightPanel.add(nodetype);
		 rightPanel.add(nodetypecomboBox);
		 rightPanel.add(nodeattribute);
		 rightPanel.add(nodeattributecomboBox);
		 
		 rightPanel.add(isenabled);
		 rightPanel.add(isenabledcomboBox);
		 rightPanel.add(grade);
		 rightPanel.add(gradecomboBox);
		 rightwestPanel.add(save);
		 rightwestPanel.add(cancel);
		 save.setBackground(UIManager.getColor("CheckBox.background"));
		 save.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 cancel.setFont(new Font("华文细黑", Font.PLAIN, 16));*/
		 //testList.add(UnitManager.getCode());//
		 /*final UnitManager UnitManager2=new UnitManager();
		 UnitManager2.setCode(UnitManager.getCode());*/
		 //System.out.println("end.....>UnitManager.getCode()......................>"+UnitManager.getCode());
		 //if(UnitManager==null){return;}
		//save
		// save.addMouseListener(new MouseAdapter() {
		 /*
			 public void mousePressed(MouseEvent e) {
					// TODO Auto-generated method stub
					//if(UnitManager==null){return;}
					StringUtils utils=new StringUtils();
					String organid=utils.getUUID();//子id(机构id)
					//String parentid=(String) list.get(0);//父id
					String parentid=UnitManager2.getCode();
					//System.out.println("end2s.....>UnitManager.getCode()......................>"+UnitManager.getCode());
					System.out.println("parentid...................>"+parentid);
					String nodecodeStr=nodecodeField.getText();//机构编码
					String nodenameStr=	nodenameField.getText();//机构名称
					UnitManager nodetypeBox=(UnitManager)nodetypecomboBox.getSelectedItem();//机构类型
					UnitManager nodeattributeBox=(UnitManager) nodeattributecomboBox.getSelectedItem();//机构属性
					UnitManager isenabledboBox=(UnitManager) isenabledcomboBox.getSelectedItem();//机构是否启用
					UnitManager gradeBox=(UnitManager) gradecomboBox.getSelectedItem();//机构级别
					//PreparedStatement pstmt = null;
					try {
						System.out.println("parentid...........>"+parentid);
						DBManager.execute("insert into "+CBSystemConstants.opcardUser+"T_A_POWERORGAN(ORGANID,ORGANNAME,ORGANTYPE,PARENTID,ISENABLED,ORGANKIND,ORGANGRADE,ORGANCODE)values('"+organid+"','"+nodenameStr+"','"+nodetypeBox.getCode()+"','"+parentid+"','"+isenabledboBox.getCode()+"','"+nodeattributeBox.getCode()+"','"+gradeBox.getCode()+"','"+nodecodeStr+"')");
						System.out.println("保存成功");
					} catch (Exception e2) {
						// TODO: handle exception
						e2.printStackTrace();
					}
					//System.out.println("nodecodeStr:"+nodecodeStr+",nodenameStr"+nodenameStr+",nodetypeBox"+nodetypeBox+",nodeattributeBox"+nodeattributeBox+",isenabledboBox"+isenabledboBox+",gradeBox"+gradeBox);
				
			 }
		 */
			 //});
		/* save.addActionListener(new ActionListener() {
					@Override
					
					public void actionPerformed(ActionEvent e) {
						// TODO Auto-generated method stub
						//if(UnitManager==null){return;}
						UnitManageSplitRightPDialog rightPane=new UnitManageSplitRightPDialog();
						StringUtils utils=new StringUtils();
						String organid=utils.getUUID();//子id(机构id)
						UnitManager code=rightPane.getUnitManager();//
						String parentid=code.getCode();
						System.out.println("parentid.......>"+parentid);
						String nodecodeStr=nodecodeField.getText();//机构编码
						String nodenameStr=	nodenameField.getText();//机构名称
						UnitManager nodetypeBox=(UnitManager)nodetypecomboBox.getSelectedItem();//机构类型
						UnitManager nodeattributeBox=(UnitManager) nodeattributecomboBox.getSelectedItem();//机构属性
						UnitManager isenabledboBox=(UnitManager) isenabledcomboBox.getSelectedItem();//机构是否启用
						UnitManager gradeBox=(UnitManager) gradecomboBox.getSelectedItem();//机构级别
						//PreparedStatement pstmt = null;
						try {
							System.out.println("parentid...........>"+parentid);
							DBManager.execute("insert into "+CBSystemConstants.opcardUser+"T_A_POWERORGAN(ORGANID,ORGANNAME,ORGANTYPE,PARENTID,ISENABLED,ORGANKIND,ORGANGRADE,ORGANCODE)values('"+organid+"','"+nodenameStr+"','"+nodetypeBox.getCode()+"','"+parentid+"','"+isenabledboBox.getCode()+"','"+nodeattributeBox.getCode()+"','"+gradeBox.getCode()+"','"+nodecodeStr+"')");
							System.out.println("保存成功");
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
						//System.out.println("nodecodeStr:"+nodecodeStr+",nodenameStr"+nodenameStr+",nodetypeBox"+nodetypeBox+",nodeattributeBox"+nodeattributeBox+",isenabledboBox"+isenabledboBox+",gradeBox"+gradeBox);
					}
				});*/
			 //cancel
		/* cancel.addActionListener(new ActionListener() {
					@Override
					public void actionPerformed(ActionEvent e) {
						// TODO Auto-generated method stub
						//model2.removeNodeFromParent(parentNode);
						rightpanel.setVisible(false);
					}
				});*/
		
	}
	//标记按钮
	public static int getMark() {
		return mark;
	}
	public static void setMark(int mark) {
		UnitManageSplitRightPDialog.mark = mark;
	}
	/*public static void main(String[] args) {
		try {
			UnitManageSplitRightPDialog dialog = new UnitManageSplitRightPDialog();
			dialog.setSize(600, 500);
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}*/
	public static void init(){//UnitManager unitManager
		 JLabel nodecode=new JLabel("机构编码:");
		 nodecode.setVerticalAlignment(SwingConstants.TOP);
		 nodecode.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 //nodecode.setSize(1000, 10);
		 nodecode.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel nodename=new JLabel("机构名称:");
		 nodename.setVerticalAlignment(SwingConstants.TOP);
		 nodename.setHorizontalAlignment(SwingConstants.CENTER);
		 nodename.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 JLabel nodetype=new JLabel("机构类型:");
		 nodetype.setVerticalAlignment(SwingConstants.TOP);
		 nodetype.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 nodetype.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel nodeattribute=new JLabel("机构属性:");
		 nodeattribute.setVerticalAlignment(SwingConstants.TOP);
		 nodeattribute.setHorizontalAlignment(SwingConstants.CENTER);
		 nodeattribute.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 JLabel isenabled=new JLabel("机构启用:");
		 isenabled.setVerticalAlignment(SwingConstants.TOP);
		 isenabled.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 isenabled.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel grade=new JLabel("机构级别:");
		 grade.setVerticalAlignment(SwingConstants.TOP);
		 grade.setHorizontalAlignment(SwingConstants.CENTER);
		 grade.setFont(new Font("华文细黑", Font.PLAIN, 16));
		  
/*		 Vector<UnitManager> organtypelist=new Vector<UnitManager>();//机构类型
		 Vector<UnitManager> organkindlist=new Vector<UnitManager>();//机构属性
		 Vector<UnitManager> isenabledlist=new Vector<UnitManager>();//机构是否停用
		 Vector<UnitManager> organgradelist=new Vector<UnitManager>();//机构级别
*/	   	 
		 //机构类型
		 UnitManager 	UnitManager0= new UnitManager();
	   	 UnitManager	UnitManager1= new UnitManager();
		 UnitManager0.setCode("0");
		 UnitManager0.setName("单位");
		 organtypelist.add(UnitManager0);
		 UnitManager1.setCode("1");
		 UnitManager1.setName("部门");
		 organtypelist.add(UnitManager1);
		 
		 //机构属性
		 UnitManager 	unitManager10= new UnitManager();
	   	 UnitManager	unitManager11= new UnitManager();
	   	 UnitManager 	unitManager12= new UnitManager();
		 unitManager10.setCode("0");
		 unitManager10.setName("调度");
		 organkindlist.add(unitManager10);
		 unitManager11.setCode("1");
		 unitManager11.setName("监控");
		 organkindlist.add(unitManager11);
		 unitManager12.setCode("2");
		 unitManager12.setName("厂站");
		 organkindlist.add(unitManager12);
		 
		 //机构是否停用
		 UnitManager 	unitManager20= new UnitManager();
	   	 UnitManager	unitManager21= new UnitManager();
		 unitManager20.setCode("0");
		 unitManager20.setName("启用");
		 isenabledlist.add(unitManager20);
		 unitManager21.setCode("1");
		 unitManager21.setName("未启用");
		 isenabledlist.add(unitManager21);
		 //机构级别
		 UnitManager 	unitManager30= new UnitManager();
	   	 UnitManager	unitManager31= new UnitManager();
	   	 UnitManager 	unitManager32= new UnitManager();
	   	 UnitManager	unitManager33= new UnitManager();
	   	 UnitManager 	unitManager34= new UnitManager();
		 unitManager30.setCode("0");
		 unitManager30.setName("国家级");
		 organgradelist.add(unitManager30);
		 unitManager31.setCode("1");
		 unitManager31.setName("区域级");
		 organgradelist.add(unitManager31);
		 unitManager32.setCode("2");
		 unitManager32.setName("省级");
		 organgradelist.add(unitManager32);
		 unitManager33.setCode("3");
		 unitManager33.setName("地级");
		 organgradelist.add(unitManager33);
		 unitManager34.setCode("4");
		 unitManager34.setName("县级");
		 organgradelist.add(unitManager34);
		 
		 
		// final JTextField nodecodeField=new JTextField();//机构编码
		 nodecodeField.setFont(new Font("华文细黑", Font.PLAIN, 16));
		// final JTextField nodenameField=new JTextField();//机构名称
		 nodenameField.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		// final JComboBox nodetypecomboBox=new JComboBox(organtypelist);//机构类型
		 nodetypecomboBox=new JComboBox(organtypelist);//机构类型
		 nodetypecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		// final JComboBox nodeattributecomboBox=new JComboBox(organkindlist);//机构属性
		 nodeattributecomboBox=new JComboBox(organkindlist);//机构属性
		 nodeattributecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		// final JComboBox isenabledcomboBox=new JComboBox(isenabledlist);//机构是否启用
		 isenabledcomboBox=new JComboBox(isenabledlist);//机构是否启用
		 isenabledcomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		// final JComboBox gradecomboBox=new JComboBox(organgradelist);//机构级别
		 gradecomboBox=new JComboBox(organgradelist);//机构级别
		 gradecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 conterPanel.add(rightPanel, BorderLayout.NORTH);
		 conterPanel.add(rightwestPanel, BorderLayout.SOUTH);
		 rightPanel.setLayout(new GridLayout(2, 3, 0, 10));
		 
		 rightPanel.add(nodecode);
		 rightPanel.add(nodecodeField);
		 rightPanel.add(nodename);
		 rightPanel.add(nodenameField);
		 
		 rightPanel.add(nodetype);
		 rightPanel.add(nodetypecomboBox);
		 rightPanel.add(nodeattribute);
		 rightPanel.add(nodeattributecomboBox);
		 
		 rightPanel.add(isenabled);
		 rightPanel.add(isenabledcomboBox);
		 rightPanel.add(grade);
		 rightPanel.add(gradecomboBox);
		 rightwestPanel.add(save);
		 rightwestPanel.add(cancel);
		 //rightwestPanel.add(find);
		 String nodecodeStr=nodecodeField.getText();//机构编码
		 String nodenameStr=nodenameField.getText();//机构名称
		 UnitManager nodetypeBox=(UnitManager)nodetypecomboBox.getSelectedItem();//机构类型
		 UnitManager nodeattributeBox=(UnitManager) nodeattributecomboBox.getSelectedItem();//机构属性
		 UnitManager isenabledboBox=(UnitManager) isenabledcomboBox.getSelectedItem();//机构是否启用
		 UnitManager gradeBox=(UnitManager) gradecomboBox.getSelectedItem();//机构级别
		 save.setBackground(UIManager.getColor("CheckBox.background"));
		 save.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 cancel.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 /*find.setBackground(UIManager.getColor("CheckBox.background"));
		 find.setFont(new Font("华文细黑", Font.PLAIN, 16));*/
		 //System.out.println("unitManagerFind.getId().........>"+unitManagerFind.getId());
		 //查询的数据
		/* if(unitManagerFind.getId().equalsIgnoreCase("")){return;}
		 Map mapFind=(Map)DBManager.queryForList("select ORGANCODE,ORGANNAME,ORGANTYPE,ORGANKIND,ISENABLED,ORGANGRADE from "+CBSystemConstants.opcardUser+"t_a_powerorgan where organid='"+unitManagerFind.getId()+"'").get(0);
		 //if(mapFind==null){return;}
		 System.out.println("mapFind....."+mapFind);
		 nodecodeField.setText((String) mapFind.get("ORGANCODE"));//机构编码
		 nodenameField.setText((String) mapFind.get("ORGANNAME"));//机构名称*/	 
		 save.addActionListener(new ActionListener() {
				@Override
				public void actionPerformed(ActionEvent e) {
					// TODO Auto-generated method stub
					/*UnitManageSplitRightPDialog rightPane=new UnitManageSplitRightPDialog();
					UnitManager code=rightPane.getUnitManager();//(error)
					//String parentid=code.getCode();
					String parentid=code.getId();*/
					StringUtils utils=new StringUtils();
					String organid=utils.getUUID();//子id(机构id)
					String nodecodeStr=nodecodeField.getText();//机构编码
					String nodenameStr=	nodenameField.getText();//机构名称
					if(nodecodeStr.equalsIgnoreCase("") || nodenameStr.equalsIgnoreCase("")){
						return;
					}
					UnitManager nodetypeBox=(UnitManager)nodetypecomboBox.getSelectedItem();//机构类型
					UnitManager nodeattributeBox=(UnitManager) nodeattributecomboBox.getSelectedItem();//机构属性
					UnitManager isenabledboBox=(UnitManager) isenabledcomboBox.getSelectedItem();//机构是否启用
					UnitManager gradeBox=(UnitManager) gradecomboBox.getSelectedItem();//机构级别
					if(getMark()==1){//修改
						String organid2=unitManagerEdit.getId();
						if(!organid2.equalsIgnoreCase("")){
							DBManager.execute("update "+CBSystemConstants.opcardUser+"T_A_POWERORGAN set ORGANNAME='"+nodenameStr+"',ORGANTYPE='"+nodetypecomboBox.getSelectedIndex()+"',ISENABLED='"+isenabledcomboBox.getSelectedIndex()+"',ORGANKIND='"+nodeattributecomboBox.getSelectedIndex()+"',ORGANGRADE='"+gradecomboBox.getSelectedIndex()+"',ORGANCODE='"+nodecodeStr+"' where organid='"+organid2+"'");
							//System.out.println("修改成功...");
							nodecodeField.setText("");
							nodenameField.setText("");
							nodetypecomboBox.setSelectedIndex(0);//机构类型
							nodeattributecomboBox.setSelectedIndex(0);//机构属性
							isenabledcomboBox.setSelectedIndex(0);//机构是否启用
							gradecomboBox.setSelectedIndex(0);;//机构级别
							UnitManageSplitLeftPDialog.initTree();
						}
					}else if(getMark()==2){//新增
						String parentid=unitManager.getId();//			 
						if(parentid.equalsIgnoreCase("")){
							return;
						}
						//PreparedStatement pstmt = null;
						try {
							/*System.out.println("parentid22222222....>.>"+parentid);
							System.out.println("organName.....>.....>"+organName);
							//System.out.println("....................................."+nodecodeField.getText()+","+nodenameField.getText()+","+","+UnitManager0.getCode()+","+UnitManager10.getCode()+","+UnitManager20.getCode()+","+UnitManager30.getCode());
							List lists=DBManager.queryForList("select organid from "+CBSystemConstants.opcardUser+"t_a_powerorgan where parentid='"+parentid+"' and organname='"+organName+"'");
							System.out.println("lists........>"+lists);
							Map map=(Map) lists.get(0);
							String parentid2=(String) map.get("ORGANID");*/
							Map seqMax=(Map) DBManager.queryForList("select max(seq) from "+CBSystemConstants.opcardUser+"t_a_powerorgan").get(0);
							//System.out.println("seqMax....>"+seqMax);
							//System.out.println("seqMax.get('SEQ')"+seqMax.get("MAX(SEQ)"));
							Integer seq=Integer.parseInt((seqMax.get("MAX(SEQ)")).toString());//seqMax.get("MAX(SEQ)")是Object类型不能直接转换为String类型，需要toString()转换才行
							DBManager.execute("insert into "+CBSystemConstants.opcardUser+"T_A_POWERORGAN(ORGANID,ORGANNAME,ORGANTYPE,PARENTID,ISENABLED,ORGANKIND,ORGANGRADE,ORGANCODE,SEQ)values('"+organid+"','"+nodenameStr+"','"+nodetypeBox.getCode()+"','"+parentid+"','"+isenabledboBox.getCode()+"','"+nodeattributeBox.getCode()+"','"+gradeBox.getCode()+"','"+nodecodeStr+"','"+(++seq)+"')");
							//保存后，设初值为空
							/*nodecodeField.getText();
							nodenameField.getText();
							UnitManager0.getCode();
							UnitManager10.getCode();
							UnitManager20.getCode();
							UnitManager30.getCode();*/
							//System.out.println(""+nodecodeField.getText()+","+nodenameField.getText()+","+","+UnitManager0.getCode()+","+UnitManager10.getCode()+","+UnitManager20.getCode()+","+UnitManager30.getCode());
							nodecodeField.setText("");
							nodenameField.setText("");
							nodetypecomboBox.setSelectedIndex(0);//机构类型
							nodeattributecomboBox.setSelectedIndex(0);//机构属性
							isenabledcomboBox.setSelectedIndex(0);//机构是否启用
							gradecomboBox.setSelectedIndex(0);;//机构级别
							
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
					
						UnitManageSplitLeftPDialog.initTree();
					}	
			  }
			});
		 cancel.addActionListener(new ActionListener() {
				@Override
				public void actionPerformed(ActionEvent e) {
					nodecodeField.setText("");//机构编码
					nodenameField.setText("");//机构名称
					nodetypecomboBox.setSelectedIndex(0);//机构类型
					nodeattributecomboBox.setSelectedIndex(0);//机构属性
					isenabledcomboBox.setSelectedIndex(0);//机构是否启用
					gradecomboBox.setSelectedIndex(0);;//机构级别
					UnitManageSplitLeftPDialog.initTree();
				}
		 });
		/* find.addActionListener(new ActionListener(){

			@Override
			public void actionPerformed(ActionEvent e) {
				// TODO Auto-generated method stub
				 if(unitManagerFind.getId().equalsIgnoreCase("")){return;}
				 Map mapFind=(Map)DBManager.queryForList("select ORGANCODE,ORGANNAME,ORGANTYPE,ORGANKIND,ISENABLED,ORGANGRADE from "+CBSystemConstants.opcardUser+"t_a_powerorgan where organid='"+unitManagerFind.getId()+"'").get(0);
				 //System.out.println("mapFind....."+mapFind);
				 nodecodeField.setText((String) mapFind.get("ORGANCODE"));//机构编码
				 nodenameField.setText((String) mapFind.get("ORGANNAME"));//机构名称	
				 nodetypecomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANTYPE")));//机构类型
				 nodeattributecomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANKIND")));//机构属性
				 isenabledcomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ISENABLED")));//机构是否启用
				 gradecomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANGRADE")));;//机构级别
			}});*/
 		}
	/*public static void find(UnitManager unitManager){
		 //查询的数据
		 if(unitManager.getId().equalsIgnoreCase("")){return;}
		 Map mapFind=(Map)DBManager.queryForList("select ORGANCODE,ORGANNAME,ORGANTYPE,ORGANKIND,ISENABLED,ORGANGRADE from "+CBSystemConstants.opcardUser+"t_a_powerorgan where organid='"+unitManager.getId()+"'").get(0);
		 //if(mapFind==null){return;}
		 System.out.println("mapFind....."+mapFind);
		 nodecodeField.setText((String) mapFind.get("ORGANCODE"));//机构编码
		 nodenameField.setText((String) mapFind.get("ORGANNAME"));//机构名称
		 System.out.println(","+Integer.parseInt((String) mapFind.get("ORGANTYPE"))+","+Integer.parseInt((String) mapFind.get("ORGANKIND"))+","+Integer.parseInt((String) mapFind.get("ISENABLED"))+","+Integer.parseInt((String) mapFind.get("ORGANGRADE")));
		 nodetypecomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANTYPE")));//机构类型
		 nodeattributecomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANKIND")));//机构属性
		 isenabledcomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ISENABLED")));//机构是否启用
		 gradecomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANGRADE")));;//机构级别
	}*/

}
