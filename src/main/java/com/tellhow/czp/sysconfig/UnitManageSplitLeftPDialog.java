package com.tellhow.czp.sysconfig;

import java.awt.Font;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPopupMenu;
import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
public class UnitManageSplitLeftPDialog {
	/**
	 * 单元管理左边树界面
	 */
	
	public static  JPopupMenu popupMenu;//弹出式菜单
	public static  JMenuItem addItem;//新增
	public static JMenuItem deleteItem;//删除
	public static JMenuItem upItem;//修改
	public static JMenuItem upMoveiItem;//上移
	public static JMenuItem downMoveiItem;//下移
	//public static UnitManager unit=new UnitManager();
	
	private boolean DEBUG = true;//初始值设为true
	private static	DefaultTreeModel model2=null;
	private static	UnitManageSplitRightPDialog splitRight=null;
	//public static WindowUtils utils=new WindowUtils();//window工具类
	//public static StringUtils uuid=new StringUtils();
	//public static final DictionaryDialog dialog = new DictionaryDialog();
	private static JTree tree=new JTree();
	public static UnitManageSplitRightPDialog getSplitRight() {
		return splitRight;
	}

	public static void setSplitRight(UnitManageSplitRightPDialog splitRight) {
		UnitManageSplitLeftPDialog.splitRight = splitRight;
	}

	public JTree getTree() {
		return tree;
	}

	public void setTree(JTree tree) {
		this.tree = tree;
	}

	public UnitManageSplitLeftPDialog(){
	//定义三个集合,根节集合、枝节点集合和叶节点集合
	final Vector<UnitManager> root=new Vector<UnitManager>();
	final Vector<UnitManager> nodes=new Vector<UnitManager>();
	final Vector<UnitManager> leafs=new Vector<UnitManager>();
	DefaultMutableTreeNode rootNode=null;
	try {
		//获得root的节点集合
		String rootsql="select ORGANID,ORGANNAME from "+CBSystemConstants.opcardUser+"t_a_powerorgan  where parentid='0'";
		Map rootMap=(Map)DBManager.queryForList(rootsql).get(0);
		String rootId=(String)rootMap.get("ORGANID");
		String rootName=(String)rootMap.get("ORGANNAME");
		UnitManager rootCNModel=new UnitManager();
		rootCNModel.setId(rootId);
		rootCNModel.setName(rootName);
		root.add(rootCNModel);
		//获得nodes的节点集合
		List nodeLists=(List)DBManager.queryForList("select ORGANID,ORGANNAME from "+CBSystemConstants.opcardUser+"t_a_powerorgan  where parentid='"+rootId+"' order by seq");
		for(int index=0;index<nodeLists.size();index++){
			Map nodeMap=(Map)nodeLists.get(index);
			String nodeId=(String)nodeMap.get("ORGANID");
			String nodeName=(String)nodeMap.get("ORGANNAME");
			UnitManager nodeCNModel=new UnitManager();
			//nodeCNModel.setCode(nodeId);
			nodeCNModel.setId(nodeId);
			nodeCNModel.setName(nodeName);
			nodes.add(nodeCNModel);
		}
		//获得leafs的节点集合
		List leafLists=(List)DBManager.queryForList("select t2.parentid,t2.organname,t2.organid,t2.seq from "+CBSystemConstants.opcardUser+"t_a_powerorgan t1,"+CBSystemConstants.opcardUser+"t_a_powerorgan t2 where t1.organid=t2.parentid order by seq");
		for(int index=0;index<leafLists.size();index++){
			Map leafMap=(Map)leafLists.get(index);
			String leafId=(String)leafMap.get("PARENTID");
			String leafName=(String)leafMap.get("ORGANNAME");
			String organid=(String)leafMap.get("ORGANID");
			//System.out.println("organid..............>...>"+organid);
			UnitManager leafCNModel=new UnitManager();
			leafCNModel.setCode(leafId);
			leafCNModel.setId(organid);
			leafCNModel.setName(leafName);
			leafs.add(leafCNModel);
		}
		//各节点累加形成树状
		if(root!=null){
			rootNode=new DefaultMutableTreeNode(root.get(0));
			for(int index_1=0;index_1<nodes.size();index_1++){
				DefaultMutableTreeNode nodeNode=new DefaultMutableTreeNode(nodes.get(index_1));
				String p_id=nodes.get(index_1).getId();
				for(int index_2=0;index_2<leafs.size();index_2++){
					String s_id=leafs.get(index_2).getCode();//父id
					String currentNodeId=leafs.get(index_2).getId();//当前节点id
					if(p_id.equalsIgnoreCase(s_id)){
						DefaultMutableTreeNode leafNode1=new DefaultMutableTreeNode(leafs.get(index_2));
						for(int index_3=0;index_3<leafs.size();index_3++){
							DefaultMutableTreeNode leafNode2=new DefaultMutableTreeNode(leafs.get(index_3));
							String ss_id=leafs.get(index_3).getCode();//叶子节点的父id
							if(currentNodeId.equalsIgnoreCase(ss_id)){
								leafNode1.add(leafNode2);	//叶子节点下的叶节点添加
							}
						}
						nodeNode.add(leafNode1);
						//System.out.println("nodeNode.....................>"+nodeNode);
					}
				}
				rootNode.add(nodeNode);
			}
		}
		//System.out.println("rootNode..............................>"+rootNode);
		final DefaultTreeModel model=new DefaultTreeModel(rootNode);
		tree.setFont(new Font("华文细黑", Font.PLAIN, 16));
		tree.setModel(model);
		//tree鼠标事件
		tree.addMouseListener(new MouseAdapter() {
			@Override
			public void mouseReleased(MouseEvent e) {
				// TODO Auto-generated method stub
				JTree treeCurrentJTree=(JTree)e.getSource();
				int location=treeCurrentJTree.getRowForLocation(e.getX(), e.getY());//找出当前所在位置
				//System.out.println("location........................:"+location);
				//int jt=e.getButton();
				popupMenu=new JPopupMenu();
				addItem=new JMenuItem("新增");
				deleteItem =new JMenuItem("删除");
				upItem=new JMenuItem("修改");
				upMoveiItem=new JMenuItem("上移");
				downMoveiItem=new JMenuItem("下移");
				popupMenu.add(addItem);
				popupMenu.add(deleteItem);
				popupMenu.add(upItem);
				popupMenu.addSeparator();
				popupMenu.add(upMoveiItem);
				popupMenu.add(downMoveiItem);
				popupMenu.setLocation(e.getLocationOnScreen());
				if(e.isPopupTrigger()){//右键单击弹出父菜单
					if(location==-1){//右击空白区返回
						return;
					}
					popupMenu.show(e.getComponent(),e.getX(),e.getY());
					popupMenu.show(tree, e.getX(), e.getY());
				}
				//新增
				addItem.addMouseListener(new MouseAdapter() {
					@Override
					public void mousePressed(MouseEvent e) {
						// TODO Auto-generated method stub
						//初始选项状态
						getSplitRight().getNodecodeField().setText("");//机构编码
						getSplitRight().getNodenameField().setText("");//机构名称
						getSplitRight().getNodetypecomboBox().setSelectedIndex(0);//机构类型
						getSplitRight().getNodeattributecomboBox().setSelectedIndex(0);//机构属性
						getSplitRight().getIsenabledcomboBox().setSelectedIndex(0);//机构是否启用
						getSplitRight().getGradecomboBox().setSelectedIndex(0);
						DefaultMutableTreeNode newChild =new DefaultMutableTreeNode("新节点");
						TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
						if(parentPath==null){return;}
					    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
					    UnitManager unitManager=(UnitManager) currentnode.getUserObject();
					    //System.out.println("UnitManager..."+unitManager);
					    //System.out.println("UnitManager......code....>>.....>"+unitManager.getCode());
					    //System.out.println("UnitManager......id....>>.....>"+unitManager.getId());
						model2=(DefaultTreeModel)tree.getModel();
						model2.insertNodeInto(newChild, currentnode, currentnode.getChildCount());
						UnitManageSplitRightPDialog rightPane=new UnitManageSplitRightPDialog();
						rightPane.setUnitManager(unitManager);
						getSplitRight().setMark(2);//新增
					}
				}
				);
				//删除
				deleteItem.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e){
						Object[] options = {"确定","取消"};
						int response=JOptionPane.showOptionDialog(null, "删除后就不能恢复，你确定要删除吗？", "删除", JOptionPane.YES_OPTION, JOptionPane.QUESTION_MESSAGE, null, options, options[0]);
						if(response==0) { 
							TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
							if(parentPath==null){return;}
						    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
						   	final UnitManager unitManager=(UnitManager) currentnode.getUserObject();
							try {
								String sql="delete from "+CBSystemConstants.opcardUser+"t_a_powerorgan where organid='"+unitManager.getId()+"' or parentid='"+unitManager.getId()+"'";
								DBManager.execute(sql);
								UnitManageSplitLeftPDialog.initTree();
							} catch (Exception e2) {
								// TODO: handle exception
								e2.printStackTrace();
							}
						}else if(response==1){
							return;
						}
					}
				});
				//下移
				downMoveiItem.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e){
						TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
						if(parentPath==null){return;}
					    DefaultMutableTreeNode currentNode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
					   	final UnitManager currentUnitManager=(UnitManager) currentNode.getUserObject();
					   	String currentcode=currentUnitManager.getId();//当前节点
					   	DefaultMutableTreeNode nextNode=currentNode.getNextNode();
					   	//if(nextNode==null)return;
					   	UnitManager nextUnitManager=(UnitManager) nextNode.getUserObject();
					   	String nextcode=nextUnitManager.getId();//下移节点
						try {
							int current=DBManager.queryForInt("select seq from "+CBSystemConstants.opcardUser+"t_a_powerorgan where organid='"+currentcode+"'");
							int next=DBManager.queryForInt("select seq from "+CBSystemConstants.opcardUser+"t_a_powerorgan where organid='"+nextcode+"'");
							//System.out.println("下移：current....>"+current+",next....>"+next);
							DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_powerorgan set seq="+next+"where organid='"+currentcode+"'");
							DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_powerorgan set seq="+(current)+"where organid='"+nextcode+"'");
							initTree();
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
					   	
					}
				});
				//上移
				upMoveiItem.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e){
						//System.out.println("上移...");
						TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
						if(parentPath==null){return;}
					    DefaultMutableTreeNode currentNode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
					   	final UnitManager currentUnitManager=(UnitManager) currentNode.getUserObject();
					   	String currentcode=currentUnitManager.getId();//当前节点
					   	DefaultMutableTreeNode prevNode=currentNode.getPreviousNode();
					   	UnitManager prevUnitManager=(UnitManager) prevNode.getUserObject();
					   	String prevcode=prevUnitManager.getId();//下移节点
						try {
							int prev=DBManager.queryForInt("select seq from "+CBSystemConstants.opcardUser+"t_a_powerorgan where organid='"+prevcode+"'");
							int current=DBManager.queryForInt("select seq from "+CBSystemConstants.opcardUser+"t_a_powerorgan where organid='"+currentcode+"'");
							//if(prevcode==null)return;
							//System.out.println("上移：current....>"+current+",prev....>"+prev);
							DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_powerorgan set seq="+prev+"where organid='"+currentcode+"'");
							DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_powerorgan set seq="+current+"where organid='"+prevcode+"'");
							initTree();
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
					   	//System.out.println("UnitManager.getCode:"+UnitManager.getCode());
					   	
					}
				});
				//修改
				upItem.addMouseListener(new MouseAdapter() {
					public void mousePressed(MouseEvent e){
						//JOptionPane.showConfirmDialog(null, "是否确认修改？", "修改", JOptionPane.YES_NO_OPTION);  
						Object[] options = {"确定","取消"};
						int response=JOptionPane.showOptionDialog(null, " 是否确定修改", "修改", JOptionPane.YES_OPTION, JOptionPane.QUESTION_MESSAGE, null, options, options[0]);
						if(response==0) { 
							TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
							if(parentPath==null){return;}
						    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
						    UnitManager unitManager=(UnitManager) currentnode.getUserObject();
							final   UnitManagerEdit unitManagerEdit=new UnitManagerEdit();
						    unitManagerEdit.setCode(unitManager.getCode());
						    unitManagerEdit.setId(unitManager.getId());
						    unitManagerEdit.setName(unitManager.getName());
							UnitManageSplitRightPDialog rightPane=new UnitManageSplitRightPDialog();
							getSplitRight().setMark(1);//修改
							rightPane.setUnitManagerEdit(unitManagerEdit);
						}else if(response==1){
							return;
						}
					}
				});
			}
			@Override
			//查看
			public void mousePressed(MouseEvent e) {
				//System.out.println("查看...");
				TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
				if(parentPath==null){return;}
			    DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode)parentPath.getLastPathComponent();//获得选中节点的最后一个节点
			    UnitManager unitManager=(UnitManager) currentnode.getUserObject();
			    UnitManagerFind unitManagerFind=new UnitManagerFind();
			    unitManagerFind.setCode(unitManager.getCode());
			    unitManagerFind.setId(unitManager.getId());
			    unitManagerFind.setName(unitManager.getName());
			    //UnitManageSplitRightPDialog unit=new UnitManageSplitRightPDialog();
			    //System.out.println("unitManagerFind。。。。。。。。》"+unitManagerFind);
			    //System.out.println("unitManagerFind.getId()。。。left。。。。。》"+unitManagerFind.getId());
			    //unit.find(unitManager);	
				 if(unitManagerFind.getId().equalsIgnoreCase("")){return;}
				 Map mapFind=(Map)DBManager.queryForList("select ORGANCODE,ORGANNAME,ORGANTYPE,ORGANKIND,ISENABLED,ORGANGRADE from "+CBSystemConstants.opcardUser+"t_a_powerorgan where organid='"+unitManagerFind.getId()+"'").get(0);
				 getSplitRight().getNodecodeField().setText((String) mapFind.get("ORGANCODE"));//机构编码
				 getSplitRight().getNodenameField().setText((String) mapFind.get("ORGANNAME"));//机构名称
				 getSplitRight().getNodetypecomboBox().setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANTYPE")));//机构类型
				 getSplitRight().getNodeattributecomboBox().setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANKIND")));//机构属性
				 getSplitRight().getIsenabledcomboBox().setSelectedIndex(Integer.parseInt((String) mapFind.get("ISENABLED")));//机构是否启用
				 getSplitRight().getGradecomboBox().setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANGRADE")));
				 //unit.setUnitManagerFind(unitManagerFind);
				 //nodecodeField.setText((String) mapFind.get("ORGANCODE"));//机构编码
				 //nodenameField.setText((String) mapFind.get("ORGANNAME"));//机构名称	
				 //nodetypecomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANTYPE")));//机构类型
				 //nodeattributecomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANKIND")));//机构属性
				 //isenabledcomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ISENABLED")));//机构是否启用
				 //gradecomboBox.setSelectedIndex(Integer.parseInt((String) mapFind.get("ORGANGRADE")));;//机构级别
			
			}
		});
	} catch (Exception e) {
		// TODO: handle exception
		e.printStackTrace();
	}
  }
	public static void initTree(){
		
		//定义三个集合,根节集合、枝节点集合和叶节点集合
		final Vector<UnitManager> root=new Vector<UnitManager>();
		final Vector<UnitManager> nodes=new Vector<UnitManager>();
		final Vector<UnitManager> leafs=new Vector<UnitManager>();
		DefaultMutableTreeNode rootNode=null;
		try {
			//获得root的节点集合
			String rootsql="select ORGANID,ORGANNAME from "+CBSystemConstants.opcardUser+"t_a_powerorgan  where parentid='0'";	
			Map rootMap=(Map)DBManager.queryForList(rootsql).get(0);
			String rootId=(String)rootMap.get("ORGANID");
			String rootName=(String)rootMap.get("ORGANNAME");
			UnitManager rootCNModel=new UnitManager();
			rootCNModel.setId(rootId);
			rootCNModel.setName(rootName);
			root.add(rootCNModel);
			//获得nodes的节点集合
			List nodeLists=(List)DBManager.queryForList("select ORGANID,ORGANNAME from "+CBSystemConstants.opcardUser+"t_a_powerorgan  where parentid='"+rootId+"' order by seq");
			for(int index=0;index<nodeLists.size();index++){
				Map nodeMap=(Map)nodeLists.get(index);
				String nodeId=(String)nodeMap.get("ORGANID");
				String nodeName=(String)nodeMap.get("ORGANNAME");
				UnitManager nodeCNModel=new UnitManager();
				nodeCNModel.setId(nodeId);
				nodeCNModel.setName(nodeName);
				nodes.add(nodeCNModel);
			}
			//获得leafs的节点集合
			List leafLists=(List)DBManager.queryForList("select t2.parentid,t2.organname,t2.organid,t2.seq from "+CBSystemConstants.opcardUser+"t_a_powerorgan t1,"+CBSystemConstants.opcardUser+"t_a_powerorgan t2 where t1.organid=t2.parentid order by seq");
			for(int index=0;index<leafLists.size();index++){
				Map leafMap=(Map)leafLists.get(index);
				String leafId=(String)leafMap.get("PARENTID");
				String organid=(String)leafMap.get("ORGANID");
				String leafName=(String)leafMap.get("ORGANNAME");
				UnitManager leafCNModel=new UnitManager();
				leafCNModel.setCode(leafId);
				leafCNModel.setId(organid);
				leafCNModel.setName(leafName);
				leafs.add(leafCNModel);
			}
			//各节点累加形成树状
			if(root!=null){
				rootNode=new DefaultMutableTreeNode(root.get(0));
				for(int index_1=0;index_1<nodes.size();index_1++){
					DefaultMutableTreeNode nodeNode=new DefaultMutableTreeNode(nodes.get(index_1));
					String p_id=nodes.get(index_1).getId();
					for(int index_2=0;index_2<leafs.size();index_2++){
						String s_id=leafs.get(index_2).getCode();//父id
						String currentNodeId=leafs.get(index_2).getId();//当前节点id
						if(p_id.equalsIgnoreCase(s_id)){
							DefaultMutableTreeNode leafNode1=new DefaultMutableTreeNode(leafs.get(index_2));
							for(int index_3=0;index_3<leafs.size();index_3++){
								DefaultMutableTreeNode leafNode2=new DefaultMutableTreeNode(leafs.get(index_3));
								String ss_id=leafs.get(index_3).getCode();//叶子节点的父id
								if(currentNodeId.equalsIgnoreCase(ss_id)){
									leafNode1.add(leafNode2);	//叶子节点下的叶节点添加
								}
							
							}
							nodeNode.add(leafNode1);
						}
					}
					rootNode.add(nodeNode);
				}
			}
			final DefaultTreeModel model=new DefaultTreeModel(rootNode);
			tree.setFont(new Font("华文细黑", Font.PLAIN, 16));
			tree.setModel(model);
		}catch(Exception e){
			e.printStackTrace();
		}
	}
}
