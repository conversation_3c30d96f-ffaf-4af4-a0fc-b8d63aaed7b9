package com.tellhow.czp.sysconfig;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTree;
import javax.swing.table.DefaultTableModel;
import javax.swing.tree.DefaultMutableTreeNode;

import org.apache.commons.collections.CollectionUtils;

import com.tellhow.czp.user.User;
import com.tellhow.czp.util.GUIUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.DictionarysModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class CodeAuthManagerDialog extends JDialog {
	private DefaultMutableTreeNode root;
	private Map temp,tabletemp,tabletemp1,zdrtemp;
	private List results,zdrresults;
	private JTable table;
	private JTable table_1;
	private JTree tree;
	private JButton btnNewButton,btnNewButton_1,button;
	private static DictionarysModel code,zdcode;
	private static List<Auth> guiauth,guiauth1;
	private JComboBox comboBox;
	private JCheckBox chckbxNewCheckBox;
	
	//树
	private void inittree(){
		
		String sql="select code,name from "+CBSystemConstants.opcardUser+"T_A_DICTIONARY t where t.codetype = 'Role' and unitcode = 'system' order by t.code";
		results=DBManager.queryForList(sql);
		
		temp=new HashMap();
		for(int i = 0; i < results.size(); i++){
			temp = (Map) results.get(i);
			String code=StringUtils.ObjToString(temp.get("code"));
			String name=StringUtils.ObjToString(temp.get("name"));
			DictionarysModel model = new DictionarysModel();
			model.setCode(code);
			model.setName(name);
			DefaultMutableTreeNode node = new  DefaultMutableTreeNode(model) ;
			root.add(node);
		}
	}
	
	//上表
	private void inittable(){
		DefaultTableModel dtm=new DefaultTableModel(){
			public Class<?> getColumnClass(int columnIndex) {
				return getValueAt(0, columnIndex).getClass();
				}
			};
		Object[] tableHeads=new String[]{"序号", "权限名称","选择"};
		dtm.setColumnIdentifiers(tableHeads);
		table.setModel(dtm);
		
		GUIUtil g=new GUIUtil();
		g.anaGUI();
		List<CodeNameModel> lcnm=g.getmenuitem();
		
		List results = DBManager.query("select roleid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"t_a_Powerroleauthinfo where roleid='"+CodeAuthManagerDialog.zdcode.getCode()+"' and classname='MenuItem'");
		tabletemp=new HashMap();
		List<Auth> resultslist=new ArrayList<Auth>();
		List<String> resultsstring=new ArrayList<String>();
		for(int j=0;j<results.size();j++){
			tabletemp = (Map) results.get(j);
			String userid = StringUtils.ObjToString(tabletemp.get("roleid"));
			String authid=StringUtils.ObjToString(tabletemp.get("authid"));
			String authname=StringUtils.ObjToString(tabletemp.get("authname"));
			String isckeck=StringUtils.ObjToString(tabletemp.get("ischeck"));
			int ischeck=Integer.parseInt(isckeck);
			Auth auth=new Auth(userid, authid, authname, ischeck);
			resultslist.add(auth);
			resultsstring.add(authid);
		}
		guiauth=resultslist;
		List <Auth> authlist=new ArrayList<Auth>();
		List <String> authliststring=new ArrayList<String>();
		for(int i=0;i<lcnm.size();i++){
			CodeNameModel cnm=lcnm.get(i);
			Auth auth=new Auth();
			auth.setUsername(zdcode.getName());
			auth.setAuthid(cnm.getCode());
			auth.setAuthname(cnm.getName());
			auth.setIscheck(0);
			authlist.add(auth);
			authliststring.add(cnm.getCode());
		}
		
		Collection<String> disjunction =CollectionUtils.disjunction(authliststring,resultsstring);
		for(int i=0;i<authliststring.size();i++){
			
				Auth auth=authlist.get(i);
				String au=authliststring.get(i);
				if(disjunction.contains(au)){
					resultslist.add(auth);
				}
		}
		
		int jjj=0;
		for(int i=0;i<resultslist.size();i++){
			Vector v = new Vector();
			v.add(i+1);
			v.add(resultslist.get(i));
			v.add(resultslist.get(i).getIscheck()==0?false:true);
			dtm.addRow(v);
		}
	}
	
	//下表
	private void inittable_1(){
		DefaultTableModel dtm=new DefaultTableModel(){
			public Class<?> getColumnClass(int columnIndex) {
				return getValueAt(0, columnIndex).getClass();
				}
			};
		Object[] tableHeads=new String[]{"序号", "权限名称","选择"};
		dtm.setColumnIdentifiers(tableHeads);
		table_1.setModel(dtm);
		
		GUIUtil g=new GUIUtil();
		g.anaGUI();
		List<CodeNameModel> lcnm=g.gettoolbar();
		
		List results = DBManager.query("select roleid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"t_a_Powerroleauthinfo where roleid='"+CodeAuthManagerDialog.zdcode.getCode()+"' and classname='Button'");
		tabletemp1=new HashMap();
		List<Auth> resultslist=new ArrayList<Auth>();
		List<String> resultsstring=new ArrayList<String>();
		for(int j=0;j<results.size();j++){
			tabletemp1 = (Map) results.get(j);
			
			String userid = StringUtils.ObjToString(tabletemp1.get("roleid"));
			String authid=StringUtils.ObjToString(tabletemp1.get("authid"));
			String authname=StringUtils.ObjToString(tabletemp1.get("authname"));
			String isckeck=StringUtils.ObjToString(tabletemp1.get("ischeck"));
			int ischeck=Integer.parseInt(isckeck);
			Auth auth=new Auth(userid, authid, authname, ischeck);
			resultslist.add(auth);
			resultsstring.add(authid);
		}
		guiauth1=resultslist;
		List <Auth> authlist=new ArrayList<Auth>();
		List <String> authliststring=new ArrayList<String>();
		for(int i=0;i<lcnm.size();i++){
			CodeNameModel cnm=lcnm.get(i);
			Auth auth=new Auth();
			auth.setUsername(zdcode.getName());
			auth.setAuthid(cnm.getCode());
			auth.setAuthname(cnm.getName());
			auth.setIscheck(0);
			authlist.add(auth);
			authliststring.add(cnm.getCode());
		}
		
		Collection<String> disjunction =CollectionUtils.disjunction(authliststring,resultsstring);
		for(int i=0;i<authliststring.size();i++){
			
				Auth auth=authlist.get(i);
				String au=authliststring.get(i);
				if(disjunction.contains(au)){
					resultslist.add(auth);
				}
		}
		
		
		for(int i=0;i<resultslist.size();i++){
			Vector v = new Vector();
			v.add(i+1);
			v.add(resultslist.get(i));
			v.add(resultslist.get(i).getIscheck()==0?false:true);
			dtm.addRow(v);
		}
	}
	
	public static void main(String[] args) {
		try {
			CodeAuthManagerDialog dialog = new CodeAuthManagerDialog(new javax.swing.JFrame(), true);
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	//界面
	public CodeAuthManagerDialog(java.awt.Frame parent, boolean modal) {
		super(parent,modal);
		this.setSize(714, 410);
		this.setResizable(false);
		this.setLocation(300, 150);
		getContentPane().setLayout(null);
		this.setTitle("角色权限配置");
		
		JScrollPane scrollPane = new JScrollPane();
		scrollPane.setBounds(0, 0, 118, 381);
		getContentPane().add(scrollPane);
		
		JPanel panel = new JPanel();
		panel.setBounds(117, 0, 581, 381);
		getContentPane().add(panel);
		panel.setLayout(null);
		
		btnNewButton = new JButton("提交");
		btnNewButton.addActionListener(new BtnNewButton(this));
		btnNewButton.setBounds(478, 12, 93, 27);
		btnNewButton.setEnabled(false);
		panel.add(btnNewButton);
		
		JScrollPane scrollPane_1 = new JScrollPane();
		scrollPane_1.setBounds(0, 45, 581, 131);
		panel.add(scrollPane_1);
		
		table = new JTable();
		scrollPane_1.setViewportView(table);
		
		JScrollPane scrollPane_2 = new JScrollPane();
		scrollPane_2.setBounds(0, 201, 581, 180);
		panel.add(scrollPane_2);
		
		table_1 = new JTable();
		scrollPane_2.setViewportView(table_1);
		
		JLabel lblNewLabel = new JLabel("菜单权限配置");
		lblNewLabel.setBounds(10, 16, 107, 23);
		panel.add(lblNewLabel);
		
		JLabel lblNewLabel_1 = new JLabel("工具栏权限配置");
		lblNewLabel_1.setBounds(10, 176, 107, 23);
		panel.add(lblNewLabel_1);
		
		btnNewButton_1 = new JButton("取消");
		btnNewButton_1.setBounds(408, 12, 93, 27);
		btnNewButton_1.setEnabled(false);
		btnNewButton_1.addActionListener(new BtnNewButton_1Listener(this));
		panel.add(btnNewButton_1);
		
		button = new JButton("全选");
		button.setBounds(341, 12, 93, 27);
		button.setEnabled(false);
		button.addActionListener(new ButtonListener(this));
		panel.add(button);
		
		DictionarysModel model = new DictionarysModel();
		model.setCode("root");
		model.setName("调度区域");
		root=new DefaultMutableTreeNode(model);
		inittree();
		tree = new JTree(root);
		tree.addMouseListener(new TreeListener(this));
		scrollPane.setViewportView(tree);
	}

	//全选按钮事件
	class ButtonListener implements ActionListener{
		private CodeAuthManagerDialog amd;
		public ButtonListener(CodeAuthManagerDialog amd){
			this.amd=amd;
		}
		
		@Override
		public void actionPerformed(ActionEvent e) {
			// TODO Auto-generated method stub
			DefaultTableModel dtm1=new DefaultTableModel(){
				public Class<?> getColumnClass(int columnIndex) {
					return getValueAt(0, columnIndex).getClass();
					}
				};
			Object[] tableHeads1=new String[]{"序号", "权限名称","选择"};
			dtm1.setColumnIdentifiers(tableHeads1);
			table_1.setModel(dtm1);
			
			GUIUtil g1=new GUIUtil();
			g1.anaGUI();
			List<CodeNameModel> lcnm1=g1.gettoolbar();
			List <Auth> authlist1=new ArrayList<Auth>();
			List <String> authliststring1=new ArrayList<String>();
			for(int i=0;i<lcnm1.size();i++){
				CodeNameModel cnm=lcnm1.get(i);
				Auth auth=new Auth();
				auth.setUsername(code.getCode());
				auth.setAuthid(cnm.getCode());
				auth.setAuthname(cnm.getName());
				auth.setIscheck(1);
				authlist1.add(auth);
				authliststring1.add(cnm.getCode());
			}
			for(int i=0;i<authlist1.size();i++){
				Vector v = new Vector();
				v.add(i+1);
				v.add(authlist1.get(i));
				v.add(authlist1.get(i).getIscheck()==0?false:true);
				dtm1.addRow(v);
			}
			
			DefaultTableModel dtm=new DefaultTableModel(){
				public Class<?> getColumnClass(int columnIndex) {
					return getValueAt(0, columnIndex).getClass();
					}
				};
			Object[] tableHeads=new String[]{"序号", "权限名称","选择"};
			dtm.setColumnIdentifiers(tableHeads);
			table.setModel(dtm);
			
			GUIUtil g=new GUIUtil();
			g.anaGUI();
			List<CodeNameModel> lcnm=g.getmenuitem();
			List <Auth> authlist=new ArrayList<Auth>();
			List <String> authliststring=new ArrayList<String>();
			for(int i=0;i<lcnm.size();i++){
				CodeNameModel cnm=lcnm.get(i);
				Auth auth=new Auth();
				auth.setUsername(code.getCode());
				auth.setAuthid(cnm.getCode());
				auth.setAuthname(cnm.getName());
				auth.setIscheck(1);
				authlist.add(auth);
				authliststring.add(cnm.getCode());
			}
			for(int i=0;i<authlist.size();i++){
				Vector v = new Vector();
				v.add(i+1);
				v.add(authlist.get(i));
				v.add(authlist.get(i).getIscheck()==0?false:true);
				dtm.addRow(v);
			}
		}
		
	}
	
	//全部取消按钮
	class BtnNewButton_1Listener implements ActionListener{
		private CodeAuthManagerDialog amd;
		public BtnNewButton_1Listener(CodeAuthManagerDialog amd){
			this.amd=amd;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			// TODO Auto-generated method stub
			DefaultTableModel dtm1=new DefaultTableModel(){
				public Class<?> getColumnClass(int columnIndex) {
					return getValueAt(0, columnIndex).getClass();
					}
				};
			Object[] tableHeads1=new String[]{"序号", "权限名称","选择"};
			dtm1.setColumnIdentifiers(tableHeads1);
			table_1.setModel(dtm1);
			
			GUIUtil g1=new GUIUtil();
			g1.anaGUI();
			List<CodeNameModel> lcnm1=g1.gettoolbar();
			List <Auth> authlist1=new ArrayList<Auth>();
			List <String> authliststring1=new ArrayList<String>();
			for(int i=0;i<lcnm1.size();i++){
				CodeNameModel cnm=lcnm1.get(i);
				Auth auth=new Auth();
				auth.setUsername(code.getCode());
				auth.setAuthid(cnm.getCode());
				auth.setAuthname(cnm.getName());
				auth.setIscheck(0);
				authlist1.add(auth);
				authliststring1.add(cnm.getCode());
			}
			for(int i=0;i<authlist1.size();i++){
				Vector v = new Vector();
				v.add(i+1);
				v.add(authlist1.get(i));
				v.add(authlist1.get(i).getIscheck()==0?false:true);
				dtm1.addRow(v);
			}
			
			DefaultTableModel dtm=new DefaultTableModel(){
				public Class<?> getColumnClass(int columnIndex) {
					return getValueAt(0, columnIndex).getClass();
					}
				};
			Object[] tableHeads=new String[]{"序号", "权限名称","选择"};
			dtm.setColumnIdentifiers(tableHeads);
			table.setModel(dtm);
			
			GUIUtil g=new GUIUtil();
			g.anaGUI();
			List<CodeNameModel> lcnm=g.getmenuitem();
			List <Auth> authlist=new ArrayList<Auth>();
			List <String> authliststring=new ArrayList<String>();
			for(int i=0;i<lcnm.size();i++){
				CodeNameModel cnm=lcnm.get(i);
				Auth auth=new Auth();
				auth.setUsername(code.getCode());
				auth.setAuthid(cnm.getCode());
				auth.setAuthname(cnm.getName());
				auth.setIscheck(0);
				authlist.add(auth);
				authliststring.add(cnm.getCode());
			}
			for(int i=0;i<authlist.size();i++){
				Vector v = new Vector();
				v.add(i+1);
				v.add(authlist.get(i));
				v.add(authlist.get(i).getIscheck()==0?false:true);
				dtm.addRow(v);
			}
		}
		
	}
	
	//提交按钮事件
	class BtnNewButton implements ActionListener{
		private CodeAuthManagerDialog amd;
		private Map updatetemp,updatetempq;
		public BtnNewButton(CodeAuthManagerDialog amd){
			this.amd=amd;
		}
		
		@Override
		public void actionPerformed(ActionEvent arg0) {
			int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"是否确认提交" ,CBSystemConstants.SYSTEM_TITLE,JOptionPane.YES_NO_OPTION);
			if (isok == JOptionPane.OK_OPTION) {
				int z=table.getModel().getRowCount();
				List results = DBManager.query("select roleid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"t_a_Powerroleauthinfo where roleid='"+code.getCode()+"' and classname='MenuItem'");
				updatetemp = new HashMap();
				List<Auth> resultslist=new ArrayList<Auth>();
				List<String> resultsstring=new ArrayList<String>();
				for(int j=0;j<results.size();j++){
					updatetemp = (Map) results.get(j);
					String userid = StringUtils.ObjToString(updatetemp.get("roleid"));
					String authid=StringUtils.ObjToString(updatetemp.get("authid"));
					String authname=StringUtils.ObjToString(updatetemp.get("authname"));
					String isckeck=StringUtils.ObjToString(updatetemp.get("ischeck"));
					int ischeck=Integer.parseInt(isckeck);
					Auth auth=new Auth(userid, authid, authname, ischeck);
					resultslist.add(auth);
					resultsstring.add(authid);
				}
				for (int zz=0;zz<z;zz++){
					boolean bll=(Boolean)table.getModel().getValueAt(zz, 2);
					Auth auth=(Auth)table.getModel().getValueAt(zz, 1);
					int boo=0;
					if(bll==true){
						boo=1;
					}
					if(resultsstring.contains(auth.getAuthid())){
						DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_Powerroleauthinfo set ischeck='"+boo+"' where roleid='"+CodeAuthManagerDialog.code.getCode()+"' and authid='"+auth.getAuthid()+"' and classname='MenuItem'");
					}else{
						DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_Powerroleauthinfo (roleid,AUTHID,AUTHNAME,ISCHECK,CLASSNAME) values ('"+CodeAuthManagerDialog.code.getCode()+"','"+auth.getAuthid()+"','"+auth.getAuthname()+"','"+boo+"','MenuItem')");
					}
				}
			
				int zq=table_1.getModel().getRowCount();
				List resultsq = DBManager.query("select roleid,authid,authname,ischeck from "+CBSystemConstants.opcardUser+"t_a_Powerroleauthinfo where roleid='"+code.getCode()+"' and classname='Button'");
				updatetempq = new HashMap();
				List<Auth> resultslistq=new ArrayList<Auth>();
				List<String> resultsstringq=new ArrayList<String>();
				for(int jq=0;jq<resultsq.size();jq++){
					updatetempq = (Map) resultsq.get(jq);
					String useridq = StringUtils.ObjToString(updatetempq.get("userid"));
					String authidq=StringUtils.ObjToString(updatetempq.get("authid"));
					String authnameq=StringUtils.ObjToString(updatetempq.get("authname"));
					String isckeckq=StringUtils.ObjToString(updatetempq.get("ischeck"));
					int ischeckq=Integer.parseInt(isckeckq);
					Auth authq=new Auth(useridq, authidq, authnameq, ischeckq);
					resultslistq.add(authq);
					resultsstringq.add(authidq);
				}
				for (int zzq=0;zzq<zq;zzq++){
					boolean bllq=(Boolean)table_1.getModel().getValueAt(zzq, 2);
					Auth authq=(Auth)table_1.getModel().getValueAt(zzq, 1);
					int booq=0;
					if(bllq==true){
						booq=1;
					}
					if(resultsstringq.contains(authq.getAuthid())){
						DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_Powerroleauthinfo  set ischeck='"+booq+"' where roleid='"+CodeAuthManagerDialog.code.getCode()+"' and authid='"+authq.getAuthid()+"' and classname='Button'");
					}else{
						DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_Powerroleauthinfo (roleid,AUTHID,AUTHNAME,ISCHECK,CLASSNAME) values ('"+CodeAuthManagerDialog.code.getCode()+"','"+authq.getAuthid()+"','"+authq.getAuthname()+"','"+booq+"','Button')");
					}
				}
			}else{
				
				return;
			}
		}
	}
	
	//树点击事件
	class TreeListener extends MouseAdapter {
		private CodeAuthManagerDialog amd;
		public TreeListener(CodeAuthManagerDialog amd) {
			this.amd = amd;
		}
		@Override
		public void mouseClicked(MouseEvent e) {
			if(e.getButton()==e.BUTTON1){
				amd.btnNewButton.setEnabled(true);
				amd.btnNewButton_1.setEnabled(true);
				amd.button.setEnabled(true);
				if(tree.isSelectionEmpty()!=true){
					DefaultMutableTreeNode path=(DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
					DictionarysModel model = new DictionarysModel();
					model = (DictionarysModel)path.getUserObject();
					if(model.getCode().equals("root")){
						CodeAuthManagerDialog.code=null;
						amd.btnNewButton.setEnabled(false);
						amd.btnNewButton_1.setEnabled(false);
						amd.button.setEnabled(false);
					}else{
						//model.setCode("code");
						CodeAuthManagerDialog.code=model;						
						CodeAuthManagerDialog.zdcode=model;
						amd.inittable();
						amd.inittable_1();
					}
				}
			}
		}
	}
}
