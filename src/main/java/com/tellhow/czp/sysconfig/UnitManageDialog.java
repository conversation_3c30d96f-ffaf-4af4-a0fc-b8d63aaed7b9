package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.Font;
import java.awt.GridLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Vector;

import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JTextField;
import javax.swing.JTree;
import javax.swing.SwingConstants;
import javax.swing.UIManager;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class UnitManageDialog extends JDialog {
	//public static JMenuItem moves;//移动
	public static  JPopupMenu popupMenu;//弹出式菜单
	public static  JMenuItem addItem;//新增
	public static JMenuItem deleteItem;//删除
	public static JMenuItem updateItem;//修改
	public static JMenuItem upMoveiItem;//上移
	public static JMenuItem downMoveiItem;//下移
	public static final JTree tree = new JTree();//树
	public static 	JPanel  rightpanel = new JPanel();
	private final JPanel northpanel = new JPanel();//北
	private final static JPanel centerpanel = new JPanel();//中
	private static DefaultTreeModel model2=null;
	private static JButton save=new JButton("保存");//右面板保存按钮
	private static JButton cancel=new JButton("取消");//右面板取消按钮
	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			UnitManageDialog dialog = new UnitManageDialog();
			dialog.setSize(600, 500);
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * Create the dialog.
	 */
	public UnitManageDialog() {
		setFont(new Font("Dialog", Font.PLAIN, 12));
		setTitle("\u5355\u5143\u7BA1\u7406");
		setBounds(100, 100, 450, 300);
		getContentPane().setLayout(new BorderLayout(0, 0));
		JScrollPane scrollPane = new JScrollPane();
		getContentPane().add(scrollPane, BorderLayout.WEST);
		DefaultMutableTreeNode root=new DefaultMutableTreeNode("国家电网");
		 //定义两个集合，枝节点集合和叶节点集合
		//final Vector<String> nodes=new Vector<String>();
		//final Vector<String> leafs=new Vector<String>();
		//final Vector<String> pids=new Vector<String>();//存放父类id
		//final Vector<String> ids=new Vector<String>();//存放子类id
		//final Vector<CodeNameModel> root=new Vector<CodeNameModel>();
		final Vector<CodeNameModel> nodes2=new Vector<CodeNameModel>();
		final Vector<CodeNameModel> leafs2=new Vector<CodeNameModel>();
		CodeNameModel codeNameModel=null;
		PreparedStatement pstmt = null;
		try {
			String nodesql="select ORGANID,ORGANNAME,ORGANTYPE from "+CBSystemConstants.opcardUser+"t_a_powerorgan t1 where t1.parentid=0";
			pstmt=DBManager.getConnection().prepareStatement(nodesql);
			ResultSet rs=pstmt.executeQuery();
			while(rs.next()){
				codeNameModel=new CodeNameModel();
				//pids.add(rs.getString(1));
				codeNameModel.setCode(rs.getString(1));
				codeNameModel.setName(rs.getString(2));
				nodes2.add(codeNameModel);
				System.out.println(",nodes:"+rs.getString(1)+","+rs.getString(2));
			}
			System.out.println("nodes2:"+nodes2);
			String leafsql="select t2.PARENTID,t2.organname from "+CBSystemConstants.opcardUser+"t_a_powerorgan t1,"+CBSystemConstants.opcardUser+"t_a_powerorgan t2 where t1.organid=t2.parentid";
			pstmt=DBManager.getConnection().prepareStatement(leafsql);
			rs=pstmt.executeQuery();
			while(rs.next()){
				//ids.add(rs.getString(1));
				//leafs.add(rs.getString(2));
				codeNameModel=new CodeNameModel();
				codeNameModel.setCode(rs.getString(1));
				codeNameModel.setName(rs.getString(2));
				leafs2.add(codeNameModel);
				System.out.println(",leafs2:"+rs.getString(1)+","+rs.getString(2));
			}
			System.out.println("leafs2:"+leafs2);
			/*for(int node=0;node<nodes.size();node++){
				DefaultMutableTreeNode nodeStr=new DefaultMutableTreeNode(nodes.get(node));
				int p_id=Integer.parseInt(pids.get(node));
				//System.out.println("pid.get(node)"+pids.get(node));
				for(int leaf=0;leaf<leafs.size();leaf++){
					int id=Integer.parseInt(ids.get(leaf));
					if(p_id==id){
						DefaultMutableTreeNode leafStr=new DefaultMutableTreeNode(leafs.get(leaf));
						nodeStr.add(leafStr);
					}
				}
				root.add(nodeStr);
			}*/
			for(int node=0;node<nodes2.size();node++){
				DefaultMutableTreeNode nodeStr=new DefaultMutableTreeNode(nodes2.get(node));
				//System.out.println("nodes2.get(ndode)"+nodes2.get(node).getCode());
				int p_id=Integer.parseInt(nodes2.get(node).getCode());
				for(int leaf=0;leaf<leafs2.size();leaf++){
					int id=Integer.parseInt(leafs2.get(leaf).getCode());
					if(p_id==id){
						DefaultMutableTreeNode leafStr=new DefaultMutableTreeNode(leafs2.get(leaf));
						nodeStr.add(leafStr);
					}
				}
				root.add(nodeStr);
			}
		} catch (Exception e2) {
			// TODO: handle exception
			e2.printStackTrace();
		}
		final DefaultTreeModel model=new DefaultTreeModel(root);
		tree.setFont(new Font("华文细黑", Font.PLAIN, 16));
		tree.setModel(model);
		//
		
		//model.asksAllowsChildren();
		
		tree.addMouseListener(new MouseAdapter() {
			@Override
			public void mousePressed(MouseEvent e) {}
			@Override
			public void mouseReleased(MouseEvent e) {
					JTree treeCurrentJTree=(JTree)e.getSource();
					int location=treeCurrentJTree.getRowForLocation(e.getX(), e.getY());//找出当前所在位置
					popupMenu=new JPopupMenu();
					addItem=new JMenuItem("新增");
					updateItem=new JMenuItem("修改");
					upMoveiItem=new JMenuItem("上移");
					downMoveiItem=new JMenuItem("下移");
					deleteItem =new JMenuItem("删除");
					popupMenu.add(addItem);
					popupMenu.addSeparator();
					popupMenu.add(updateItem);
					popupMenu.addSeparator();
					popupMenu.add(upMoveiItem);
					popupMenu.addSeparator();
					popupMenu.add(downMoveiItem);
					popupMenu.addSeparator();
					popupMenu.add(deleteItem);
					popupMenu.setLocation(e.getLocationOnScreen());
				 if(e.isPopupTrigger()){//右键单击弹出式菜单
					 popupMenu.show(e.getComponent(),e.getX(),e.getY());
					 popupMenu.show(tree, e.getX(), e.getY());
				    }
				 //
				 //新增中的增加子菜单、在节点之前增加的相邻的节点、在节点之后增加的相邻的节点
				 //final JMenuItem addItem2=new JMenuItem("增加的子菜单");
				 //final JMenuItem beforeaddItem =new JMenuItem("在节点之前增加的相邻的节点");
				 //final JMenuItem afteraddItem =new JMenuItem("在节点之后增加的相邻的节点");
				 addItem.addMouseListener(new MouseAdapter() {
					 @Override
					 public void mousePressed(MouseEvent e) {
						 /*if(rightpanel!=null){
							 rightpanel.removeAll();
						 }*/
						 System.out.println("rightpanel"+rightpanel.size());
						 DefaultMutableTreeNode newChild =new DefaultMutableTreeNode("新节点");
						 TreePath parentPath=tree.getSelectionPath();//返回首选节点的路径
						 DefaultMutableTreeNode parentNode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();//获得选中节点的最后一个节点
						// System.out.println("tree.getSelectionPath()"+tree.getSelectionPath());
						// System.out.println("parentPath.getLastPathComponent();"+(CodeNameModel)parentPath.getLastPathComponent().getClass());
						/* Object object=	parentPath.getLastPathComponent();
						 DefaultMutableTreeNode node=(DefaultMutableTreeNode) object;
						 Object useObject=node.getUserObject();*/
						 
						 /*CodeNameModel dnModel=(CodeNameModel) useObject;
						 CodeNameModel codee= (CodeNameModel) parentPath.getLastPathComponent();
						 System.out.println("codee.getCode():"+codee.getCode());
						 codee.getCode();
						 System.out.println("dnModel.getCode()"+dnModel.getCode());*/
						 //Object obj= parentPath.getLastPathComponent();
						 DefaultMutableTreeNode currentnode=(DefaultMutableTreeNode) parentPath.getLastPathComponent();
					   	 CodeNameModel codeNamemodel=(CodeNameModel) currentnode.getUserObject();
						 model2=(DefaultTreeModel) tree.getModel();
						 model2.insertNodeInto(newChild, parentNode, parentNode.getChildCount());
						 initrightpanel(codeNamemodel);
						 //UnitManageDialog unitManageDialog=new UnitManageDialog(codeNamemodel);
						//设置右边面板为可见的
						 rightpanel.setVisible(true);
					 }
					 
				});
				 //
				//移动中的上移，下移，升级，降级
				/* moves.addMouseListener(new MouseAdapter() {
					 @Override
					 public void mousePressed(MouseEvent e) {
					 popupMenu=new JPopupMenu();
					 upMoveiItem=new JMenuItem("上移");
					 downMoveiItem=new JMenuItem("下移");
					 JMenuItem up=new JMenuItem("升级");
					 JMenuItem down =new JMenuItem("降级");
					 popupMenu.add(upMoveiItem);
					 popupMenu.add(downMoveiItem);
					 popupMenu.addSeparator();
					 popupMenu.add(up);
					 popupMenu.add(down);
					 popupMenu.setLocation(e.getLocationOnScreen());
					 popupMenu.show(moves, e.getX(), e.getY()); 
					 }
				});*/
			}
		});
		scrollPane.setViewportView(tree);
		getContentPane().add(rightpanel, BorderLayout.CENTER);
		rightpanel.setLayout(new BorderLayout(0, 0));
		rightpanel.add(northpanel, BorderLayout.NORTH);
		rightpanel.add(centerpanel, BorderLayout.CENTER);
		//initrightpanel(codeNameModel);//初始化右边面板
		rightpanel.setVisible(true);//初始化设置面板为不可见得
	}
	public void initrightpanel(final CodeNameModel codeNamemodel){
		 JLabel nodecode=new JLabel("节点编码:");
		 nodecode.setVerticalAlignment(SwingConstants.TOP);
		 nodecode.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 nodecode.setSize(1000, 10);
		 nodecode.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel nodename=new JLabel("节点名称:");
		 nodename.setVerticalAlignment(SwingConstants.TOP);
		 nodename.setHorizontalAlignment(SwingConstants.CENTER);
		 nodename.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 JLabel nodetype=new JLabel("节点类型:");
		 nodetype.setVerticalAlignment(SwingConstants.TOP);
		 nodetype.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 nodetype.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel nodeattribute=new JLabel("节点属性:");
		 nodeattribute.setVerticalAlignment(SwingConstants.TOP);
		 nodeattribute.setHorizontalAlignment(SwingConstants.CENTER);
		 nodeattribute.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 JLabel isenabled=new JLabel("启用:");
		 isenabled.setVerticalAlignment(SwingConstants.TOP);
		 isenabled.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 isenabled.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel grade=new JLabel("级别:");
		 grade.setVerticalAlignment(SwingConstants.TOP);
		 grade.setHorizontalAlignment(SwingConstants.CENTER);
		 grade.setFont(new Font("华文细黑", Font.PLAIN, 16));
		  
		 Vector<CodeNameModel> organtypelist=new Vector<CodeNameModel>();//机构类型
		 Vector<CodeNameModel> organkindlist=new Vector<CodeNameModel>();//机构属性
		 Vector<CodeNameModel> isenabledlist=new Vector<CodeNameModel>();//机构是否停用
		 Vector<CodeNameModel> organgradelist=new Vector<CodeNameModel>();//机构级别
	   	 
		 //机构类型
		 CodeNameModel 	codenameModel0= new CodeNameModel();
	   	 CodeNameModel	codenameModel1= new CodeNameModel();
		 codenameModel0.setCode("0");
		 codenameModel0.setName("单位");
		 organtypelist.add(codenameModel0);
		 codenameModel1.setCode("1");
		 codenameModel1.setName("部门");
		 organtypelist.add(codenameModel1);
		 
		 //机构属性
		 CodeNameModel 	codenameModel10= new CodeNameModel();
	   	 CodeNameModel	codenameModel11= new CodeNameModel();
	   	 CodeNameModel 	codenameModel12= new CodeNameModel();
		 codenameModel10.setCode("0");
		 codenameModel10.setName("调度");
		 organkindlist.add(codenameModel10);
		 codenameModel11.setCode("1");
		 codenameModel11.setName("监控");
		 organkindlist.add(codenameModel11);
		 codenameModel12.setCode("2");
		 codenameModel12.setName("厂站");
		 organkindlist.add(codenameModel12);
		 
		 //机构是否停用
		 CodeNameModel 	codenameModel20= new CodeNameModel();
	   	 CodeNameModel	codenameModel21= new CodeNameModel();
		 codenameModel20.setCode("0");
		 codenameModel20.setName("启用");
		 isenabledlist.add(codenameModel20);
		 codenameModel21.setCode("1");
		 codenameModel21.setName("未启用");
		 isenabledlist.add(codenameModel21);
		 //机构级别
		 CodeNameModel 	codenameModel30= new CodeNameModel();
	   	 CodeNameModel	codenameModel31= new CodeNameModel();
	   	 CodeNameModel 	codenameModel32= new CodeNameModel();
	   	 CodeNameModel	codenameModel33= new CodeNameModel();
	   	 CodeNameModel 	codenameModel34= new CodeNameModel();
		 codenameModel30.setCode("0");
		 codenameModel30.setName("国家级");
		 organgradelist.add(codenameModel30);
		 codenameModel31.setCode("1");
		 codenameModel31.setName("区域级");
		 organgradelist.add(codenameModel31);
		 codenameModel32.setCode("2");
		 codenameModel32.setName("省级");
		 organgradelist.add(codenameModel32);
		 codenameModel33.setCode("3");
		 codenameModel33.setName("地级");
		 organgradelist.add(codenameModel33);
		 codenameModel34.setCode("4");
		 codenameModel34.setName("县级");
		 organgradelist.add(codenameModel34);
		 final JTextField nodecodeField=new JTextField();//机构编码
		 nodecodeField.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 final JTextField nodenameField=new JTextField();//机构名称
		 nodenameField.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 final JComboBox nodetypecomboBox=new JComboBox(organtypelist);//机构类型
		 nodetypecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 final JComboBox nodeattributecomboBox=new JComboBox(organkindlist);//机构属性
		 nodeattributecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 final JComboBox isenabledcomboBox=new JComboBox(isenabledlist);//机构是否启用
		 isenabledcomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 final JComboBox gradecomboBox=new JComboBox(organgradelist);//机构级别
		 gradecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 northpanel.setLayout(new GridLayout(3, 2, 0, 10));
		 northpanel.add(nodecode);
		 northpanel.add(nodecodeField);
		 northpanel.add(nodename);
		 northpanel.add(nodenameField);
		 
		 northpanel.add(nodetype);
		 northpanel.add(nodetypecomboBox);
		 northpanel.add(nodeattribute);
		 northpanel.add(nodeattributecomboBox);
		 
		 northpanel.add(isenabled);
		 northpanel.add(isenabledcomboBox);
		 northpanel.add(grade);
		 northpanel.add(gradecomboBox);
		 
		 save.setBackground(UIManager.getColor("CheckBox.background"));
		 save.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 cancel.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 centerpanel.add(save);
		 centerpanel.add(cancel);
			//save
		 save.addActionListener(new ActionListener() {
					@Override
					public void actionPerformed(ActionEvent e) {
						// TODO Auto-generated method stub
						StringUtils utils=new StringUtils();
						String organid=utils.getUUID();//子id(机构id)
						String parentid=codeNamemodel.getCode();//父id
						String nodecodeStr=nodecodeField.getText();//机构编码
						String nodenameStr=	nodenameField.getText();//机构名称
						CodeNameModel nodetypeBox=(CodeNameModel)nodetypecomboBox.getSelectedItem();//机构类型
						CodeNameModel nodeattributeBox=(CodeNameModel) nodeattributecomboBox.getSelectedItem();//机构属性
						CodeNameModel isenabledboBox=(CodeNameModel) isenabledcomboBox.getSelectedItem();//机构是否启用
						CodeNameModel gradeBox=(CodeNameModel) gradecomboBox.getSelectedItem();//机构级别
						
						PreparedStatement pstmt = null;
						try {
							String sql="insert into "+CBSystemConstants.opcardUser+"T_A_POWERORGAN(ORGANID,ORGANNAME,ORGANTYPE,PARENTID,ISENABLED,ORGANKIND,ORGANGRADE,ORGANCODE)values(?,?,?,?,?,?,?,?)";
							pstmt=DBManager.getConnection().prepareStatement(sql);
							pstmt.setString(1, organid);//机构id
							pstmt.setString(2, nodenameStr);//机构名称
							pstmt.setString(3, nodetypeBox.getCode());//机构类型
							pstmt.setString(4, parentid);//父机构id
							pstmt.setString(5, isenabledboBox.getCode());//机构是否启用
							pstmt.setString(6, nodeattributeBox.getCode());//机构属性
							pstmt.setString(7, gradeBox.getCode());//机构级别
							pstmt.setString(8, nodecodeStr);//机构编码
							pstmt.executeUpdate();
							rightpanel.setVisible(false);
							rightpanel.removeAll();
							//tree.repaint();
							System.out.println("保存成功");
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
						System.out.println("nodecodeStr:"+nodecodeStr+",nodenameStr"+nodenameStr+",nodetypeBox"+nodetypeBox+",nodeattributeBox"+nodeattributeBox+",isenabledboBox"+isenabledboBox+",gradeBox"+gradeBox);
					}
				});
			 //cancel
		 cancel.addActionListener(new ActionListener() {
					@Override
					public void actionPerformed(ActionEvent e) {
						// TODO Auto-generated method stub
						//model2.removeNodeFromParent(parentNode);
						rightpanel.setVisible(false);
					}
				});
		
	}
	public UnitManageDialog(final CodeNameModel codeNamemodel){
		 JLabel nodecode=new JLabel("节点编码:");
		 nodecode.setVerticalAlignment(SwingConstants.TOP);
		 nodecode.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 nodecode.setSize(1000, 10);
		 nodecode.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel nodename=new JLabel("节点名称:");
		 nodename.setVerticalAlignment(SwingConstants.TOP);
		 nodename.setHorizontalAlignment(SwingConstants.CENTER);
		 nodename.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 JLabel nodetype=new JLabel("节点类型:");
		 nodetype.setVerticalAlignment(SwingConstants.TOP);
		 nodetype.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 nodetype.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel nodeattribute=new JLabel("节点属性:");
		 nodeattribute.setVerticalAlignment(SwingConstants.TOP);
		 nodeattribute.setHorizontalAlignment(SwingConstants.CENTER);
		 nodeattribute.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 JLabel isenabled=new JLabel("启用:");
		 isenabled.setVerticalAlignment(SwingConstants.TOP);
		 isenabled.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 isenabled.setHorizontalAlignment(SwingConstants.CENTER);
		 JLabel grade=new JLabel("级别:");
		 grade.setVerticalAlignment(SwingConstants.TOP);
		 grade.setHorizontalAlignment(SwingConstants.CENTER);
		 grade.setFont(new Font("华文细黑", Font.PLAIN, 16));
		  
		 Vector<CodeNameModel> organtypelist=new Vector<CodeNameModel>();//机构类型
		 Vector<CodeNameModel> organkindlist=new Vector<CodeNameModel>();//机构属性
		 Vector<CodeNameModel> isenabledlist=new Vector<CodeNameModel>();//机构是否停用
		 Vector<CodeNameModel> organgradelist=new Vector<CodeNameModel>();//机构级别
	   	 
		 //机构类型
		 CodeNameModel 	codenameModel0= new CodeNameModel();
	   	 CodeNameModel	codenameModel1= new CodeNameModel();
		 codenameModel0.setCode("0");
		 codenameModel0.setName("单位");
		 organtypelist.add(codenameModel0);
		 codenameModel1.setCode("1");
		 codenameModel1.setName("部门");
		 organtypelist.add(codenameModel1);
		 
		 //机构属性
		 CodeNameModel 	codenameModel10= new CodeNameModel();
	   	 CodeNameModel	codenameModel11= new CodeNameModel();
	   	 CodeNameModel 	codenameModel12= new CodeNameModel();
		 codenameModel10.setCode("0");
		 codenameModel10.setName("调度");
		 organkindlist.add(codenameModel10);
		 codenameModel11.setCode("1");
		 codenameModel11.setName("监控");
		 organkindlist.add(codenameModel11);
		 codenameModel12.setCode("2");
		 codenameModel12.setName("厂站");
		 organkindlist.add(codenameModel12);
		 
		 //机构是否停用
		 CodeNameModel 	codenameModel20= new CodeNameModel();
	   	 CodeNameModel	codenameModel21= new CodeNameModel();
		 codenameModel20.setCode("0");
		 codenameModel20.setName("启用");
		 isenabledlist.add(codenameModel20);
		 codenameModel21.setCode("1");
		 codenameModel21.setName("未启用");
		 isenabledlist.add(codenameModel21);
		 //机构级别
		 CodeNameModel 	codenameModel30= new CodeNameModel();
	   	 CodeNameModel	codenameModel31= new CodeNameModel();
	   	 CodeNameModel 	codenameModel32= new CodeNameModel();
	   	 CodeNameModel	codenameModel33= new CodeNameModel();
	   	 CodeNameModel 	codenameModel34= new CodeNameModel();
		 codenameModel30.setCode("0");
		 codenameModel30.setName("国家级");
		 organgradelist.add(codenameModel30);
		 codenameModel31.setCode("1");
		 codenameModel31.setName("区域级");
		 organgradelist.add(codenameModel31);
		 codenameModel32.setCode("2");
		 codenameModel32.setName("省级");
		 organgradelist.add(codenameModel32);
		 codenameModel33.setCode("3");
		 codenameModel33.setName("地级");
		 organgradelist.add(codenameModel33);
		 codenameModel34.setCode("4");
		 codenameModel34.setName("县级");
		 organgradelist.add(codenameModel34);
		 final JTextField nodecodeField=new JTextField();//机构编码
		 nodecodeField.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 final JTextField nodenameField=new JTextField();//机构名称
		 nodenameField.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 final JComboBox nodetypecomboBox=new JComboBox(organtypelist);//机构类型
		 nodetypecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 final JComboBox nodeattributecomboBox=new JComboBox(organkindlist);//机构属性
		 nodeattributecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 final JComboBox isenabledcomboBox=new JComboBox(isenabledlist);//机构是否启用
		 isenabledcomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 final JComboBox gradecomboBox=new JComboBox(organgradelist);//机构级别
		 gradecomboBox.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 
		 northpanel.setLayout(new GridLayout(3, 2, 0, 10));
		 northpanel.add(nodecode);
		 northpanel.add(nodecodeField);
		 northpanel.add(nodename);
		 northpanel.add(nodenameField);
		 
		 northpanel.add(nodetype);
		 northpanel.add(nodetypecomboBox);
		 northpanel.add(nodeattribute);
		 northpanel.add(nodeattributecomboBox);
		 
		 northpanel.add(isenabled);
		 northpanel.add(isenabledcomboBox);
		 northpanel.add(grade);
		 northpanel.add(gradecomboBox);
		 
		 save.setBackground(UIManager.getColor("CheckBox.background"));
		 save.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 cancel.setFont(new Font("华文细黑", Font.PLAIN, 16));
		 centerpanel.add(save);
		 centerpanel.add(cancel);
			//save
		 save.addActionListener(new ActionListener() {
					@Override
					public void actionPerformed(ActionEvent e) {
						// TODO Auto-generated method stub
						StringUtils utils=new StringUtils();
						String organid=utils.getUUID();//子id(机构id)
						String parentid=codeNamemodel.getCode();//父id
						String nodecodeStr=nodecodeField.getText();//机构编码
						String nodenameStr=	nodenameField.getText();//机构名称
						CodeNameModel nodetypeBox=(CodeNameModel)nodetypecomboBox.getSelectedItem();//机构类型
						CodeNameModel nodeattributeBox=(CodeNameModel) nodeattributecomboBox.getSelectedItem();//机构属性
						CodeNameModel isenabledboBox=(CodeNameModel) isenabledcomboBox.getSelectedItem();//机构是否启用
						CodeNameModel gradeBox=(CodeNameModel) gradecomboBox.getSelectedItem();//机构级别
						
						PreparedStatement pstmt = null;
						try {
							String sql="insert into "+CBSystemConstants.opcardUser+"T_A_POWERORGAN(ORGANID,ORGANNAME,ORGANTYPE,PARENTID,ISENABLED,ORGANKIND,ORGANGRADE,ORGANCODE)values(?,?,?,?,?,?,?,?)";
							pstmt=DBManager.getConnection().prepareStatement(sql);
							pstmt.setString(1, organid);//机构id
							pstmt.setString(2, nodenameStr);//机构名称
							pstmt.setString(3, nodetypeBox.getCode());//机构类型
							pstmt.setString(4, parentid);//父机构id
							pstmt.setString(5, isenabledboBox.getCode());//机构是否启用
							pstmt.setString(6, nodeattributeBox.getCode());//机构属性
							pstmt.setString(7, gradeBox.getCode());//机构级别
							pstmt.setString(8, nodecodeStr);//机构编码
							pstmt.executeUpdate();
							rightpanel.setVisible(false);
							rightpanel.removeAll();
							//tree.repaint();
							System.out.println("保存成功");
						} catch (Exception e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
						System.out.println("nodecodeStr:"+nodecodeStr+",nodenameStr"+nodenameStr+",nodetypeBox"+nodetypeBox+",nodeattributeBox"+nodeattributeBox+",isenabledboBox"+isenabledboBox+",gradeBox"+gradeBox);
					}
				});
			 //cancel
		 cancel.addActionListener(new ActionListener() {
					@Override
					public void actionPerformed(ActionEvent e) {
						// TODO Auto-generated method stub
						//model2.removeNodeFromParent(parentNode);
						rightpanel.setVisible(false);
					}
				});
		
	}
}
