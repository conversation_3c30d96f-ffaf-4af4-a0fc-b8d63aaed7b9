package com.tellhow.czp.sysconfig;


public class EquipTable implements Cloneable{
	private String statecode;
	private String statename;
	private String statetype;
	private String statevalue;
	private String devicetypeid;
	private String parentcode;
	private String stateorder;
	private String secondtypeid;
	private String secondstate;
	private String cardbuildtype;
	public String getCardbuildtype() {
		return cardbuildtype;
	}
	public void setCardbuildtype(String cardbuildtype) {
		this.cardbuildtype = cardbuildtype;
	}
	public String getSecondstate() {
		return secondstate;
	}
	public void setSecondstate(String secondstate) {
		this.secondstate = secondstate;
	}
	public EquipTable(String statecode,String statename,String statetype,String statevalue,String devicetypeid,String parentcode,String stateorder,String secondtypeid,String secondstate,String cardbuildtype){
		this.statecode=statecode;
		this.statename=statename;
		this.statetype=statetype;
		this.statevalue=statevalue;
		this.devicetypeid=devicetypeid;
		this.parentcode=parentcode;
		this.stateorder=stateorder;
		this.secondtypeid=secondtypeid;
		this.secondstate=secondstate;
		this.cardbuildtype=cardbuildtype;
	}
	public String getSecondtypeid() {
		return secondtypeid;
	}
	public void setSecondtypeid(String secondtypeid) {
		this.secondtypeid = secondtypeid;
	}
	public String getStateorder() {
		return stateorder;
	}
	public void setStateorder(String stateorder) {
		this.stateorder = stateorder;
	}
	public EquipTable(){
		
	}
	@Override
	public String toString(){
		
		return statename;	
	}
	public String getStatevalue() {
		return statevalue;
	}
	public void setStatevalue(String statevalue) {
		this.statevalue = statevalue;
	}
	public String getDevicetypeid() {
		return devicetypeid;
	}
	public void setDevicetypeid(String devicetypeid) {
		this.devicetypeid = devicetypeid;
	}
	public String getParentcode() {
		return parentcode;
	}
	public void setParentcode(String parentcode) {
		this.parentcode = parentcode;
	}
	public String getStatetype() {
		return statetype;
	}
	public void setStatetype(String statetype) {
		this.statetype = statetype;
	}
	public String getStatecode() {
		return statecode;
	}
	public void setStatecode(String statecode) {
		this.statecode = statecode;
	}
	public String getStatename() {
		return statename;
	}
	public void setStatename(String statename) {
		this.statename = statename;
	}
	
	public EquipTable clone() { 
		EquipTable o = null; 
		try { 
		o = (EquipTable) super.clone(); 
		} catch (CloneNotSupportedException e) {} 
		return o;
	}
}
