package com.tellhow.czp.sysconfig;

import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.JRadioButton;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class EquipManagerDialogF extends JDialog {

	private EquipOperationManageDialog eomd=null;
	private final JPanel contentPanel = new JPanel();
	private JRadioButton rdbtnNewRadioButton,rdbtnNewRadioButton_1,rdbtnNewRadioButton_2;
	private JButton btnNewButton,btnNewButton_1;
	private String code,statecode,cardtype;

	public EquipManagerDialogF(EquipOperationManageDialog eomd,JFrame jFrame, boolean modal, String code,String cardtype){
		super(jFrame, modal);
		this.eomd=eomd;
		this.code=code;
		this.cardtype=cardtype;
		getContentPane().setLayout(null);
		this.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
		initradiobtn();
		initbtn();
		this.setLocationCenter();
	}
	
	public EquipManagerDialogF(EquipOperationManageDialog eomd, JFrame jFrame, boolean modal, String code,String statecode,String cardtype) {
		super(jFrame, modal);
		this.eomd=eomd;
		this.code=code;
		this.statecode=statecode;
		this.cardtype=cardtype;
		getContentPane().setLayout(null);
		this.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
		initradiobtn();
		initbtn();
		this.setLocationCenter();
	}
	
	private void initradiobtn(){
		rdbtnNewRadioButton = new JRadioButton("操作");
		rdbtnNewRadioButton.setBounds(106, 27, 121, 23);
		rdbtnNewRadioButton.setEnabled(false);
		getContentPane().add(rdbtnNewRadioButton);
		
		rdbtnNewRadioButton_1 = new JRadioButton("分类");
		rdbtnNewRadioButton_1.setBounds(287, 27, 121, 23);
		rdbtnNewRadioButton_1.setEnabled(false);
		getContentPane().add(rdbtnNewRadioButton_1);
		
		rdbtnNewRadioButton_2 = new JRadioButton("分割线",true);
		rdbtnNewRadioButton_2.setBounds(484, 27, 121, 23);
		rdbtnNewRadioButton_2.setEnabled(false);
		getContentPane().add(rdbtnNewRadioButton_2);
	}
	
	private void initbtn(){
		btnNewButton = new JButton("确定");
		btnNewButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent arg0) {
				dispose();
			}
		});
		btnNewButton.setBounds(175, 264, 93, 23);
		getContentPane().add(btnNewButton);		
		btnNewButton_1 = new JButton("取消");
		btnNewButton_1.addActionListener(new ActionListener() {			
			public void actionPerformed(ActionEvent arg0) {
				dispose();
			}
		});
		btnNewButton_1.setBounds(424, 264, 93, 23);
		getContentPane().add(btnNewButton_1);
	}
	
	private void setLocationCenter() {

		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,(h - this.getSize().height) / 2);
		this.setBounds( 400, 200,(w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
	}
	
	public void addBtnNewButtonListenernxz(){
		btnNewButton.addActionListener(new BtnNewButtonListenernxz(this, eomd, code,cardtype));
	}
	
	class BtnNewButtonListenernxz implements ActionListener{
		private EquipManagerDialogF emdf;
		private EquipOperationManageDialog eomd;
		private String code;
		private String cardtype;
		public BtnNewButtonListenernxz(EquipManagerDialogF emdf, EquipOperationManageDialog eomd,String code,String cardtype){
			this.emdf=emdf;
			this.eomd=eomd;
			this.code=code;
			this.cardtype=cardtype;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			List list=DBManager.query("select max(stateorder) stateorder from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and devicetypeid='"+code+"'");
			Map map=new HashMap();
			map=(Map)list.get(0);
			String stateorders=StringUtils.ObjToString(map.get("stateorder"));
			int stateorder=Integer.parseInt(stateorders==""?"0":stateorders)+1;
			String statecode=java.util.UUID.randomUUID().toString();
			String unitcode=CBSystemConstants.opCode;
			String statename="-----";
			DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_devicestateinfo (statecode,statename,statevalue,opcode,devicetypeid,parentcode,statetype,stateorder,secondtypeid,secondstate,cardbuildtype) values ('"+statecode+"','"+statename+"',-1,'"+unitcode+"','"+code+"',0,2,'"+stateorder+"',' ',' ','"+cardtype+"')");
			eomd.shuaxin();
			emdf.dispose();
		}
		
	}
	
//	public static void main(String[] args) {
//		java.awt.EventQueue.invokeLater(new Runnable() {
//			public void run() {
//				EquipOperationManageDialog dialog = new EquipOperationManageDialog(new javax.swing.JFrame(), true);
//				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
//					public void windowClosing(java.awt.event.WindowEvent e) {
//						System.exit(0);
//					}
//				});
//				dialog.setVisible(true);
//			}
//		});
//	}

}
