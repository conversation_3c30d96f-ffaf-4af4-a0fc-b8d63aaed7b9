package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.EventQueue;
import java.awt.Image;
import java.awt.Toolkit;

import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.border.EmptyBorder;
import javax.swing.JLabel;
import javax.swing.JTextField;
import javax.swing.JButton;

import com.tellhow.czp.util.DesUtil;
import com.tellhow.graphicframework.utils.WindowUtils;

import java.awt.event.ActionListener;
import java.awt.event.ActionEvent;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AuthServerFrame extends JFrame {

	private JPanel contentPane;
	private JTextField textField;
	private JTextField textField_1;
	private JTextField textField_2;

	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		EventQueue.invokeLater(new Runnable() {
			public void run() {
				try {
					AuthServerFrame frame = new AuthServerFrame();
					frame.setVisible(true);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		});
	}

	/**
	 * Create the frame.
	 */
	public AuthServerFrame() {
		setIconImage(Toolkit.getDefaultToolkit().getImage(AuthServerFrame.class.getResource("/tellhow/icons/title.png")));
		
		this.setTitle("生成授权码");
		setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
		setBounds(100, 100, 450, 300);
		contentPane = new JPanel();
		contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));
		setContentPane(contentPane);
		contentPane.setLayout(null);
		
		JLabel lblNewLabel = new JLabel("\u673A\u5668\u7801");
		lblNewLabel.setBounds(53, 27, 81, 21);
		contentPane.add(lblNewLabel);
		
		textField = new JTextField();
		textField.setBounds(149, 24, 205, 27);
		contentPane.add(textField);
		textField.setColumns(10);
		
		JLabel lblNewLabel_1 = new JLabel("\u6709\u6548\u671F");
		lblNewLabel_1.setBounds(53, 85, 81, 21);
		contentPane.add(lblNewLabel_1);
		
		textField_1 = new JTextField();
		textField_1.setBounds(149, 82, 205, 27);
		contentPane.add(textField_1);
		textField_1.setColumns(10);
		
		 Calendar c = Calendar.getInstance();
		 c.add(Calendar.MONTH, 6);
         SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
		textField_1.setText(dateFormat.format(c.getTime()));
		
		JButton btnNewButton = new JButton("\u751F\u6210\u6388\u6743\u7801");
		btnNewButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				try {  
					String date =textField_1.getText();
					String regEx = "^(\\d{8}|)$";
				    String regEx2 = "^[0-9_]+$";//纯数字
				    Pattern pattern = Pattern.compile(regEx);
				    Matcher matcher = pattern.matcher(date);
				    boolean rs = matcher.matches();
				    if(!rs) {
				    	WindowUtils.showMessage("日期格式输入错误");
				    	return;
				    }

					
		            String authcode =textField.getText();  
		              
		            String encryptResult = DesUtil.encrypt(authcode+"@"+date);
		            textField_2.setText(encryptResult);
				}
				catch(Exception ex) {
					
				}
			}
		});
		btnNewButton.setBounds(53, 185, 123, 29);
		contentPane.add(btnNewButton);
		
		JButton btnNewButton_1 = new JButton("\u5173\u95ED");
		btnNewButton_1.setBounds(231, 185, 123, 29);
		contentPane.add(btnNewButton_1);
		
		btnNewButton_1.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				AuthServerFrame.this.dispose();
			}
		});
		
		textField_2 = new JTextField();
		textField_2.setBounds(149, 140, 205, 27);
		contentPane.add(textField_2);
		textField_2.setColumns(10);
		
		
		JLabel label = new JLabel("\u6388\u6743\u7801");
		label.setBounds(53, 143, 81, 21);
		contentPane.add(label);
		WindowUtils.centerWindow(null, this);
		
	}
}
