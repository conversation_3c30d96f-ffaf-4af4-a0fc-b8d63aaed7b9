package com.tellhow.czp.sysconfig;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.ButtonGroup;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JRadioButton;
import javax.swing.JTabbedPane;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.utils.DOMUtil;

import czprule.stationstartup.ProjectConfigLoader;
import czprule.system.CBSystemConstants;

public class SystemSetDialog extends JDialog {
    private	ButtonGroup bg ;//单选按钮组颜色渲染
    private ButtonGroup bg1;//单选按钮组潮流显示
    private  NodeList allVlaues;//存储查找出来的系统参数链表
    private ProjectConfigLoader projectcon;//解析函数
    private String issys;//存储查找出来的isUseSysColor的值
    private Element codeElem = null;
    private String codeKey = "";
    private String codeValue="";
    private Document doc;
	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			SystemSetDialog dialog = new SystemSetDialog();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * Create the dialog.
	 */

	public SystemSetDialog() {
		setTitle("操作票系统设置");
		setBounds(100, 100, 350, 300);
		getContentPane().setLayout(null);
		
		JTabbedPane tabbedPane = new JTabbedPane(JTabbedPane.TOP);
		tabbedPane.setBounds(0, 0, 334, 262);
		getContentPane().add(tabbedPane);
		
		JPanel panel = new JPanel();
		tabbedPane.addTab("颜色设置", null, panel, null);
		panel.setLayout(null);
		
		JPanel panel_1 = new JPanel();
		tabbedPane.addTab("潮流设置", null, panel_1, null);
		panel_1.setLayout(null);
		
		JLabel lblNewLabel_1 = new JLabel("厂站潮流显示");
		lblNewLabel_1.setBounds(10, 10, 83, 15);
		panel_1.add(lblNewLabel_1);
		
		JRadioButton flowshowbutton = new JRadioButton("显示厂站潮流信息");
		flowshowbutton.setBounds(83, 41, 142, 23);
		panel_1.add(flowshowbutton);
	    flowshowbutton.setActionCommand("showflow");
	
		
		JRadioButton flownshowbutton = new JRadioButton("不显示厂站潮流信息");
		flownshowbutton.setBounds(83, 82, 142, 23);
		panel_1.add(flownshowbutton);
		flownshowbutton.setActionCommand("noshowflow");
		
		 bg1 = new ButtonGroup();
			bg1.add(flowshowbutton);
			bg1.add(flownshowbutton);
		
		JButton flowconfirmbutton = new JButton("确认");
		flowconfirmbutton.setBounds(66, 135, 93, 23);
		panel_1.add(flowconfirmbutton);
		flowconfirmbutton.addActionListener(new ActionListener()
		{
			public void actionPerformed(ActionEvent e)
			{
				flowconfirmbuttonactionPerformed(e);
			}
		});
		
		JButton flowcancelbutton = new JButton("取消");
		flowcancelbutton.setBounds(196, 135, 93, 23);
		panel_1.add(flowcancelbutton);
		flowcancelbutton.addActionListener(new ActionListener()
		{
			public void actionPerformed(ActionEvent e)
			{
				System.exit(0);
			}
		});
		
		JLabel lblNewLabel = new JLabel("系统颜色渲染");
		lblNewLabel.setBounds(10, 10, 79, 15);
		panel.add(lblNewLabel);
		
		 
		 
		JRadioButton sysradbutton = new JRadioButton("启用系统自身颜色渲染方案");
		sysradbutton.setBounds(71, 47, 227, 23);
		panel.add(sysradbutton);
		sysradbutton.setActionCommand("usesyscolor");
		
		
		//rdbtnNewRadioButton.setSelected(true);
		
		JRadioButton emsradbutton = new JRadioButton("启用原始EMS图源颜色渲染方案");
		emsradbutton.setBounds(71, 85, 227, 23);
		panel.add(emsradbutton);
		emsradbutton.setActionCommand("useemscolor");
		
		 bg = new ButtonGroup();
		bg.add(sysradbutton);
		bg.add(emsradbutton);
		 
		JButton confirmbutton = new JButton("确认");
		confirmbutton.setBounds(71, 145, 93, 23);
		panel.add(confirmbutton);
		confirmbutton.addActionListener(new ActionListener()
		{
			public void actionPerformed(ActionEvent e)
			{
				confirmbuttonactionPerformed(e);
			}
		});
		
		JButton cancelbutton = new JButton("取消");
		cancelbutton.setBounds(205, 145, 93, 23);
		panel.add(cancelbutton);
		cancelbutton.addActionListener(new ActionListener()
		{
			public void actionPerformed(ActionEvent e)
			{
				System.exit(0);
			}
		});
		//projectcon=new ProjectConfigLoader();
		//String issys=projectcon.getCodeKey("isUseSysColor");
		// String key="isUseSysColor"; 
		// getsys("isUseSysColor");
		 //if(issys.equals("true"))
		if(CBSystemConstants.isUseSysColor == true)
		   {
			sysradbutton.setSelected(true);
			System.out.println("====启用系统自身渲染===");
		   }
		     if(CBSystemConstants.isUseSysColor==false)
		   {
			emsradbutton.setSelected(true);
			System.out.println("=====EMS系统渲染=============");
		   }
	   // getsys("isFlowShow");
	    if(CBSystemConstants.isFlowShow==true)
		   {
			flowshowbutton.setSelected(true);
			System.out.println("===显示厂站潮流====");
		   }
		   if(CBSystemConstants.isFlowShow==false)
		   {
			flownshowbutton.setSelected(true);
			System.out.println("=====不显示厂站潮流=============");
		   }
}
	protected void flowconfirmbuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		String choice=bg1.getSelection().getActionCommand();

		  if(choice.equals("showflow"))
		  {   //String syscolor="true";
			 
			  setCodeKey("isFlowShow", "true");		
		  }
		  if(choice.equals("noshowflow"))
		  {
			  setCodeKey("isFlowShow", "false");	
		  }
		  JOptionPane.showMessageDialog(null, "设置成功");
	}

	//设置key的值得函数
	 public void setCodeKey(String key, String value) {
			Element codeElem = null;
			String codeKey = "";
			if (allVlaues == null) {
				return;
			}
			for (int j = 0; j < allVlaues.getLength(); j++) {
				codeElem = (Element) allVlaues.item(j);
				codeKey = codeElem.getAttribute("key").trim();
				if (key.equals(codeKey)) {
					codeElem.setTextContent(value);
					DOMUtil
							.writeXMLFile(doc,
									CBSystemConstants.SYS_CONFIG_XML_FILE);

					break;
				}
			}
		}
	public String  getsys(String key) {
		 doc = DOMUtil.readXMLFile(CBSystemConstants.SYS_CONFIG_XML_FILE);
		 Element rootE = doc.getDocumentElement();
         NodeList childEs = rootE.getChildNodes();
         Element childE = null;
         Element values = null;
         for (int i = 0; i < childEs.getLength(); i++) {
         	if(childEs.item(i).getNodeName().equals("#text"))
         		continue;
             childE = (Element) childEs.item(i);
             if (childE.getAttribute("name").toUpperCase().equals("PROJECTPARAM")) { 
                       allVlaues = childE.getElementsByTagName("value");
                 for (int j = 0; j < allVlaues.getLength(); j++) 
                 {
                     codeElem = (Element) allVlaues.item(j);
                     codeKey = codeElem.getAttribute("key").trim();
                     codeValue=codeElem.getTextContent().trim();
                 }
             }
         }
       
             for (int j = 0; j < allVlaues.getLength(); j++) {
     			codeElem = (Element) allVlaues.item(j);
     			codeKey = codeElem.getAttribute("key").trim();
     			if (key.equals(codeKey)) {
     			 issys= codeElem.getTextContent();
     			}
             }
//             System.out.println(issys+"==================获取的系统值==============");
             return issys;
            // String issys;
		   
		   
	}
	protected void confirmbuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
		String choice=bg.getSelection().getActionCommand();

		  if(choice.equals("usesyscolor"))
		  {   //String syscolor="true";
			 
			  setCodeKey("isUseSysColor", "true");		
		  }
		  if(choice.equals("useemscolor"))
		  {
			  setCodeKey("isUseSysColor", "false");	
		  }
		  JOptionPane.showMessageDialog(null, "设置成功");
	}
}
