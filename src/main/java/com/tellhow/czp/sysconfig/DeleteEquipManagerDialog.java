package com.tellhow.czp.sysconfig;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class DeleteEquipManagerDialog extends JDialog {
	private EquipOperationManageDialog eomd=null;
	private String statecode;
	private JButton btnNewButton,btnNewButton_1;

	public DeleteEquipManagerDialog(EquipOperationManageDialog eomd, final JFrame jFrame, boolean modal, String statecode) {
		super(jFrame, modal);
		this.setSize(240, 100);
		this.setLocationRelativeTo(null);
		this.eomd=eomd;
		getContentPane().setLayout(null);
		
		btnNewButton = new JButton("确定");
		btnNewButton.addActionListener(new BtnNewButtonListenersc(eomd, this, statecode));
		btnNewButton.setBounds(10, 34, 93, 23);
		getContentPane().add(btnNewButton);
		
		btnNewButton_1 = new JButton("取消");
		btnNewButton_1.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				dispose();
			}
		});
		btnNewButton_1.setBounds(113, 34, 93, 23);
		getContentPane().add(btnNewButton_1);
		
		JLabel lblNewLabel = new JLabel("是否确定删除");
		lblNewLabel.setBounds(26, 0, 180, 25);
		getContentPane().add(lblNewLabel);
	}
	
	class BtnNewButtonListenersc implements ActionListener{
		private EquipOperationManageDialog eomd;
		private DeleteEquipManagerDialog demd;
		private String statecode;
		public BtnNewButtonListenersc(EquipOperationManageDialog eomd,DeleteEquipManagerDialog demd,String statecode){
			this.eomd=eomd;
			this.demd=demd;
			this.statecode=statecode;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			// TODO Auto-generated method stub
			DBManager.execute("delete from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where statecode='"+statecode+"'");
			eomd.shuaxin();
			demd.dispose();
		}
		
	}
	
	public static void main(String[] args) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				EquipOperationManageDialog dialog = new EquipOperationManageDialog(
						new javax.swing.JFrame(), true);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}
}
