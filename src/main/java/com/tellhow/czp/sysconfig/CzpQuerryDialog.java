package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ScrollPaneConstants;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class CzpQuerryDialog extends JDialog {
	private JTextField textField;//开始时间
	private JTextField textField_1;//结束时间
	private JTextField textField_2;//票号
	private JTextField textField_3;//操作任务
	private JComboBox  comboBox;//类型
	private JComboBox comboBox_1;//状态
	private JTable table;

	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			CzpQuerryDialog dialog = new CzpQuerryDialog();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public CzpQuerryDialog() {
		init();
	}

	public CzpQuerryDialog(JFrame parent, boolean isModel) {
		super(parent, isModel);
		init();
	}
	/**
	 * Create the dialog.
	 */
	public void init() {
		setTitle("操作票查询");
		setBounds(100, 100, 785, 540);
		getContentPane().setLayout(new BorderLayout(0, 0));
		{
			JPanel panel = new JPanel();
			panel.setBorder(new TitledBorder(null, "查询条件", TitledBorder.LEADING, TitledBorder.TOP, null, null));
			getContentPane().add(panel, BorderLayout.NORTH);
			GridBagLayout gbl_panel = new GridBagLayout();
			gbl_panel.columnWidths = new int[]{0, 0, 0, 0, 0, 0};
			gbl_panel.rowHeights = new int[]{0, 0, 0, 0, 0};
			gbl_panel.columnWeights = new double[]{0.0, 1.0, 0.0, 1.0, 1.0, Double.MIN_VALUE};
			gbl_panel.rowWeights = new double[]{0.0, 0.0, 0.0, 0.0, Double.MIN_VALUE};
			panel.setLayout(gbl_panel);
			{
				JLabel lblNewLabel = new JLabel("开始时间");
				GridBagConstraints gbc_lblNewLabel = new GridBagConstraints();
				gbc_lblNewLabel.fill = GridBagConstraints.BOTH;
				gbc_lblNewLabel.anchor = GridBagConstraints.WEST;
				gbc_lblNewLabel.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel.gridx = 0;
				gbc_lblNewLabel.gridy = 0;
				panel.add(lblNewLabel, gbc_lblNewLabel);
			}
			{
				textField = new JTextField();
				GridBagConstraints gbc_textField = new GridBagConstraints();
				gbc_textField.insets = new Insets(0, 0, 5, 5);
				gbc_textField.fill = GridBagConstraints.BOTH;
				gbc_textField.gridx = 1;
				gbc_textField.gridy = 0;
				panel.add(textField, gbc_textField);
				textField.setColumns(10);
			    
				Calendar cal=Calendar.getInstance();    
			    int	y=cal.get(Calendar.YEAR);    
				int m=cal.get(Calendar.MONTH)+1;    
				int d=cal.get(Calendar.DATE);
				//String rq= "2013"+"-"+String.valueOf(m)+"-"+"1";
				String rq=String.valueOf(y)+"-"+String.valueOf(m)+"-"+"1";
				textField.setText(rq);
				textField.addMouseListener(new MouseListener() {		
					@Override
					public void mouseReleased(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mousePressed(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseExited(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseEntered(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseClicked(MouseEvent e) {
						// TODO Auto-generated method stub
					SelfCalendar calendar =new SelfCalendar(textField);
					}
				});
				
				
			}
			{
				JLabel lblNewLabel_1 = new JLabel("结束时间");
				GridBagConstraints gbc_lblNewLabel_1 = new GridBagConstraints();
				gbc_lblNewLabel_1.fill = GridBagConstraints.VERTICAL;
				gbc_lblNewLabel_1.anchor = GridBagConstraints.EAST;
				gbc_lblNewLabel_1.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_1.gridx = 2;
				gbc_lblNewLabel_1.gridy = 0;
				panel.add(lblNewLabel_1, gbc_lblNewLabel_1);
			}
			{
				textField_1 = new JTextField();
				GridBagConstraints gbc_textField_1 = new GridBagConstraints();
				gbc_textField_1.insets = new Insets(0, 0, 5, 5);
				gbc_textField_1.fill = GridBagConstraints.BOTH;
				gbc_textField_1.gridx = 3;
				gbc_textField_1.gridy = 0;
				panel.add(textField_1, gbc_textField_1);
				textField_1.setColumns(10);
				Calendar cal=Calendar.getInstance();    
			    int	y=cal.get(Calendar.YEAR);    
				int m=cal.get(Calendar.MONTH)+1;    
				int d=cal.get(Calendar.DATE);
				String rq= String.valueOf(y)+"-"+String.valueOf(m)+"-"+String.valueOf(d);
				textField_1.setText(rq);
				textField_1.addMouseListener(new MouseListener() {		
					@Override
					public void mouseReleased(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mousePressed(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseExited(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseEntered(MouseEvent e) {
						// TODO Auto-generated method stub	
					}
					@Override
					public void mouseClicked(MouseEvent e) {
						// TODO Auto-generated method stub
					SelfCalendar calendar =new SelfCalendar(textField_1);
					}
				});
			}
			{
				JLabel lblNewLabel_2 = new JLabel("票号");
				GridBagConstraints gbc_lblNewLabel_2 = new GridBagConstraints();
				gbc_lblNewLabel_2.fill = GridBagConstraints.BOTH;
				gbc_lblNewLabel_2.anchor = GridBagConstraints.WEST;
				gbc_lblNewLabel_2.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_2.gridx = 0;
				gbc_lblNewLabel_2.gridy = 1;
				panel.add(lblNewLabel_2, gbc_lblNewLabel_2);
			}
			{
				textField_2 = new JTextField();
				GridBagConstraints gbc_textField_2 = new GridBagConstraints();
				gbc_textField_2.insets = new Insets(0, 0, 5, 5);
				gbc_textField_2.fill = GridBagConstraints.BOTH;
				gbc_textField_2.gridx = 1;
				gbc_textField_2.gridy = 1;
				panel.add(textField_2, gbc_textField_2);
				textField_2.setColumns(10);
			}
			{
				JLabel lblNewLabel_3 = new JLabel("类型");
				GridBagConstraints gbc_lblNewLabel_3 = new GridBagConstraints();
				gbc_lblNewLabel_3.fill = GridBagConstraints.VERTICAL;
				gbc_lblNewLabel_3.anchor = GridBagConstraints.WEST;
				gbc_lblNewLabel_3.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_3.gridx = 2;
				gbc_lblNewLabel_3.gridy = 1;
				panel.add(lblNewLabel_3, gbc_lblNewLabel_3);
			}
			{
				 comboBox = new JComboBox();
				DefaultComboBoxModel dcbmlx=new DefaultComboBoxModel();
				CodeNameModel cnm1=new CodeNameModel("0","综令票");
				CodeNameModel cnm2=new CodeNameModel("1","逐项票");
				CodeNameModel cnm3=new CodeNameModel("2","新投票");
				dcbmlx.addElement(cnm1);
				dcbmlx.addElement(cnm2);
				dcbmlx.addElement(cnm3);
				comboBox.setModel(dcbmlx);
				GridBagConstraints gbc_comboBox = new GridBagConstraints();
				gbc_comboBox.insets = new Insets(0, 0, 5, 5);
				gbc_comboBox.fill = GridBagConstraints.BOTH;
				gbc_comboBox.gridx = 3;
				gbc_comboBox.gridy = 1;
				panel.add(comboBox, gbc_comboBox);
			}
			{
				JLabel lblNewLabel_4 = new JLabel("操作任务");
				GridBagConstraints gbc_lblNewLabel_4 = new GridBagConstraints();
				gbc_lblNewLabel_4.fill = GridBagConstraints.BOTH;
				gbc_lblNewLabel_4.anchor = GridBagConstraints.EAST;
				gbc_lblNewLabel_4.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_4.gridx = 0;
				gbc_lblNewLabel_4.gridy = 2;
				panel.add(lblNewLabel_4, gbc_lblNewLabel_4);
			}
			{
				textField_3 = new JTextField();
				GridBagConstraints gbc_textField_3 = new GridBagConstraints();
				gbc_textField_3.insets = new Insets(0, 0, 5, 5);
				gbc_textField_3.fill = GridBagConstraints.BOTH;
				gbc_textField_3.gridx = 1;
				gbc_textField_3.gridy = 2;
				panel.add(textField_3, gbc_textField_3);
				textField_3.setColumns(10);
			}
			{
				JLabel lblNewLabel_5 = new JLabel("状态");
				GridBagConstraints gbc_lblNewLabel_5 = new GridBagConstraints();
				gbc_lblNewLabel_5.fill = GridBagConstraints.VERTICAL;
				gbc_lblNewLabel_5.anchor = GridBagConstraints.WEST;
				gbc_lblNewLabel_5.insets = new Insets(0, 0, 5, 5);
				gbc_lblNewLabel_5.gridx = 2;
				gbc_lblNewLabel_5.gridy = 2;
				panel.add(lblNewLabel_5, gbc_lblNewLabel_5);
			}
			{
				 comboBox_1 = new JComboBox();
				DefaultComboBoxModel dcbmzt=new DefaultComboBoxModel();
				CodeNameModel cnm1=new CodeNameModel("0","未归档");
				CodeNameModel cnm2=new CodeNameModel("1","已归档");
				dcbmzt.addElement(cnm1);
				dcbmzt.addElement(cnm2);
				comboBox_1.setModel(dcbmzt);
				GridBagConstraints gbc_comboBox = new GridBagConstraints();
				gbc_comboBox.insets = new Insets(0, 0, 5, 5);
				gbc_comboBox.fill = GridBagConstraints.BOTH;
				gbc_comboBox.gridx = 3;
				gbc_comboBox.gridy = 2;
				panel.add(comboBox_1, gbc_comboBox);
			}
			{
				JButton  querybutton = new JButton("查询");
				GridBagConstraints gbc_btnNewButton = new GridBagConstraints();
				gbc_btnNewButton.insets = new Insets(0, 0, 0, 5);
				gbc_btnNewButton.gridx = 1;
				gbc_btnNewButton.gridy = 3;
				panel.add(querybutton, gbc_btnNewButton);
				querybutton.addActionListener(new ActionListener(){
					 public void actionPerformed(ActionEvent e){
					    	querybuttonactionPerformed(e);
					}
				});
				
			}
			{
				JButton resetbutton = new JButton("重置");
				GridBagConstraints gbc_btnNewButton_1 = new GridBagConstraints();
				gbc_btnNewButton_1.insets = new Insets(0, 0, 0, 5);
				gbc_btnNewButton_1.gridx = 3;
				gbc_btnNewButton_1.gridy = 3;
				panel.add(resetbutton, gbc_btnNewButton_1);
				resetbutton.addActionListener(new ActionListener(){
					 public void actionPerformed(ActionEvent e){
					    	resetbuttonactionPerformed(e);
					}
				});
			}
		}
		{
			JScrollPane scrollPane = new JScrollPane();
			scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
			scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
			getContentPane().add(scrollPane, BorderLayout.CENTER);
			{
				table = new JTable();
				scrollPane.setViewportView(table);
			}
		}
		initCzpInfo();
	}
	protected void initCzpInfo(){
		String begtime= textField.getText();
  		String endtime=textField_1.getText();
  	    String	sql1=" and npsj between to_date('"+begtime+" 00:00:00','yyyy-mm-dd hh24:mi:ss') and to_date('" +endtime
                + " 23:59:59','yyyy-mm-dd hh24:mi:ss')";
		 String sql="select bh,zbid,czrw,decode(cardkind,0,'综令票',1,'逐项票',2,'新投票') as cardkind ,username,npsj,decode(states,0,'未归档',1,'已归档')as states from "+CBSystemConstants.opcardUser+"t_a_czpzb a,"+CBSystemConstants.opcardUser+"t_a_poweruserinfo b where a.npr=b.userid "
		    	//  + "and cardkind='"+kind+"' and states='"+ state+"' " 
		         
		          +sql1
		          +"order by bh";
		        inintable(sql);
	}
    protected void querybuttonactionPerformed(ActionEvent e) {
		// TODO Auto-generated method stub
    	String begtime= textField.getText();
  		String endtime=textField_1.getText();
  		String pmbh=textField_2.getText();
  		String czrw=textField_3.getText();
  		CodeNameModel czplx=  (CodeNameModel) comboBox.getSelectedItem();
		String kind=czplx.getCode();//操作票类型
		CodeNameModel czpzt=  (CodeNameModel) comboBox_1.getSelectedItem();
		String state=czpzt.getCode();//操作票状态
		System.out.println(begtime+"=======这是开始时间=======");	
	    System.out.println(endtime+"=======这是结束时间=======");	
	    System.out.println(pmbh+"=======这是票号=======");	
	    System.out.println(czrw+"=======这是操作任务=======");	
	    System.out.println(kind+"=======这是操作票类型=======");	
	    System.out.println(state+"=======这是操作票状态=======");
	    String sql0=" 1=1 ";
	    String sql1="";
	    String sql2="";
	    String sql3="";
//	    boolean isdo;
//	    if((begtime==null||begtime.equals(""))&&endtime!=null&&!endtime.equals(""))
//	    {
//	    	JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "开始时间不能为空");
//
//	    	isdo=false;
//	    }
//	     if(begtime!=null&&!begtime.equals("")&&(endtime==null||endtime.equals("")))
//	    {
//	    	JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "结束时间不能为空");
//	    	isdo=false;
//	    }
	     if(begtime!=null&&endtime!=null&&!begtime.equals("")&&!endtime.equals(""))
	    {
	    	sql1=" and npsj between to_date('"+begtime+" 00:00:00','yyyy-mm-dd hh24:mi:ss') and to_date('" +endtime
                  + "  23:59:59','yyyy-mm-dd hh24:mi:ss')";
	    	
	    	System.out.println("sql1 = "+sql1);
	    }
	    	
	    if(pmbh!=null&&!pmbh.equals(""))
	    {
	    	 sql2 = " and bh like'%" + pmbh + "%' ";
	          System.out.println("sql2 = "+sql2);
	    }
	    if(czrw!=null&&!czrw.equals(""))
	    {
	    	 sql3 = " and czrw like '%" + czrw + "%' ";
	           System.out.println("sql3 = "+sql3);
	    }
	    String sql="select bh,zbid,czrw,decode(cardkind,0,'综令票',1,'逐项票',2,'新投票') as cardkind ,username,npsj,decode(states,0,'未归档',1,'已归档')as states from "+CBSystemConstants.opcardUser+"t_a_czpzb a,"+CBSystemConstants.opcardUser+"t_a_poweruserinfo b where a.npr=b.userid "
	    	  + "and cardkind='"+kind+"' and states='"+ state+"' " 
	         
	          +sql1
	          +sql2
	          +sql3
	          +"order by bh";
	    System.out.println("sql = "+sql);
	        inintable(sql);
	}
    protected void resetbuttonactionPerformed(ActionEvent e) {
  		// TODO Auto-generated method stub
    	textField.setText("");
  		textField_1.setText("");
  		textField_2.setText("");
  	    textField_3.setText("");
  	    comboBox.setSelectedIndex(0);
  	    comboBox_1.setSelectedIndex(1);
  	}
	public  void inintable(String s){ 
	 String[] tableHeads=new String[]{"主票ID","序号","票号", "类型", "操作任务", "拟票人","拟票时间","状态"};
	    DefaultTableModel		  dtm=new DefaultTableModel(){
			 public boolean isCellEditable(int rowIndex, int columnIndex) {
				 return false;			 
			 } 
		 };
		table.setModel(dtm);
		
	 dtm.setColumnIdentifiers(tableHeads);
	 table.getTableHeader().getColumnModel().getColumn(0).setMaxWidth(0);
     table.getTableHeader().getColumnModel().getColumn(0).setMinWidth(0);
     table.getTableHeader().getColumnModel().getColumn(0).setPreferredWidth(0);
     List czpinfo = DBManager.query(s);
 	System.out.println(czpinfo+"=======list  操作票的查询的信息======");
 	for(int i = 0; i < czpinfo.size(); i++){
		 Map	temp = (Map) czpinfo.get(i);
		 Vector v = new Vector();
		 /******转化成Vector***********/
          v.add(temp.get("zbid"));
          v.add(i+1);
		  v.add(temp.get("bh"));
		  v.add(temp.get("cardkind"));
		  v.add(temp.get("czrw"));
		  v.add(temp.get("username")); 
		  v.add(temp.get("npsj"));
		  v.add(temp.get("states"));
		 dtm.addRow(v); 
		}	 
    }
}
