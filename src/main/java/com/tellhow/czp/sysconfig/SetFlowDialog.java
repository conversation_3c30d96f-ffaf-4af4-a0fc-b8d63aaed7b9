package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.Font;
import java.awt.GridLayout;
import java.awt.Image;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;

import czprule.stationstartup.ProjectConfigLoader;

/**
 * 设置操作票票号
 * */
public class SetFlowDialog extends JFrame {

	public SetFlowDialog() {
		this.setTitle("设置票号格式");
		Image frame_icon = Toolkit.getDefaultToolkit().createImage(
				"resources/tellhow/icons/title.png");
		this.setIconImage(frame_icon);
		this.add(createContentPane());
		this.setSize(400, 250);
		this.setLocationRelativeTo(null);
		this.setResizable(false);

	}

	private JPanel createContentPane() {
		JPanel panel = new JPanel(new BorderLayout());
		panel.setBorder(new EmptyBorder(20, 20, 20, 20));
		final ProjectConfigLoader p = new ProjectConfigLoader();
		p.load();
		String str=p.getCodeKey("flowNum");
		JLabel label = new JLabel(
				"<html>当前票号格式："
						+ str
						+ "<br/>注：yyyyMMdd表示的是时间格式<br/>yyyy为年MM表示月dd表示日<br/>0表示数字  例如000表示三位数字，空出的字用0填充   <br/>只解析这两个格式，其他原样输出</html>");
		label.setFont(new Font("宋体", 0, 18));

		final JTextField tfd = new JTextField("");
		JButton btn = new JButton("确认修改");
		panel.add(label, BorderLayout.CENTER);
		JPanel p1 = new JPanel(new GridLayout(1, 2, 50, 0));
		p1.add(tfd);
		p1.add(btn);
		panel.add(p1, BorderLayout.SOUTH);

		btn.addActionListener(new ActionListener() {

			public void actionPerformed(ActionEvent e) {
				String text=tfd.getText();
				if (!text.equals("")&&text != null) {
					p.setCodeKey("flowNum", text);
				}
				SetFlowDialog.this.setVisible(false);
			}
		});

		return panel;
	}

}
