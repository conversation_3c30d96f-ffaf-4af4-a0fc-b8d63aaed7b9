package com.tellhow.czp.sysconfig;

import java.awt.BorderLayout;
import java.awt.EventQueue;

import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.border.EmptyBorder;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JTextField;
import javax.swing.JButton;

import com.tellhow.czp.util.DesUtil;
import com.tellhow.czp.util.HardwareUtil;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.system.CBSystemConstants;

import java.awt.event.ActionListener;
import java.awt.event.ActionEvent;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.awt.Toolkit;

public class AuthClientDialog extends JDialog {

	private JPanel contentPane;
	private JTextField textField;
	private JTextField textField_2;

	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		EventQueue.invokeLater(new Runnable() {
			public void run() {
				try {
					AuthClientDialog dialog = new AuthClientDialog();
					dialog.setVisible(true);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		});
	}
	
	public static void auth() {
		String file_path = CBSystemConstants.AUTH_CONFIG_XML_FILE;
		String authcode = "";
		try {  
            authcode = tbp.common.util.file.FileOperation.readFile(file_path);  
	    } catch (Exception e) {  
	        // TODO Auto-generated catch block  
	        e.printStackTrace();  
	    }  
		
		try {  
            if(authcode.equals("") || !DesUtil.isAuthCodeValid(authcode)) {
            	AuthClientDialog dialog = new AuthClientDialog();
            	dialog.setModal(true);
            	dialog.setVisible(true);
            }
            authcode = tbp.common.util.file.FileOperation.readFile(file_path);
            if(authcode.equals("") || !DesUtil.isAuthCodeValid(authcode)) {
            	 System.exit(-1);
            }   
	    } catch (Exception e) {  
	        // TODO Auto-generated catch block  
	        e.printStackTrace();  
	        System.exit(-1);
	    } 
	}

	/**
	 * Create the frame.
	 */
	public AuthClientDialog() {
		setIconImage(Toolkit.getDefaultToolkit().getImage(AuthClientDialog.class.getResource("/tellhow/icons/title.png")));
		this.setTitle("请输入授权码");
		setBounds(100, 100, 450, 300);
		contentPane = new JPanel();
		contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));
		setContentPane(contentPane);
		contentPane.setLayout(null);
		
		JLabel lblNewLabel = new JLabel("\u673A\u5668\u7801");
		lblNewLabel.setBounds(53, 42, 81, 21);
		contentPane.add(lblNewLabel);
		
		textField = new JTextField();
		textField.setEditable(false);
		textField.setBounds(149, 39, 205, 27);
		contentPane.add(textField);
		textField.setColumns(10);
		
		JButton btnNewButton = new JButton("\u4FDD\u5B58\u6388\u6743\u7801");
		btnNewButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				String file_path = CBSystemConstants.AUTH_CONFIG_XML_FILE;
				 String authcode = textField_2.getText();
				
				 boolean isValid = DesUtil.isAuthCodeValid(authcode);
				 
				 if(!isValid) {
                	 WindowUtils.showMessage("授权码错误");
		                return;
                }
				 
				
				 
				 tbp.common.util.file.FileOperation.writeFile(file_path, authcode);
					
				 AuthClientDialog.this.dispose();;
			}
		});
		btnNewButton.setBounds(53, 185, 123, 29);
		contentPane.add(btnNewButton);
		
		JButton btnNewButton_1 = new JButton("\u5173\u95ED");
		btnNewButton_1.setBounds(231, 185, 123, 29);
		contentPane.add(btnNewButton_1);
		
		btnNewButton_1.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
			
				 AuthClientDialog.this.dispose();;
			}
		});
		
		textField_2 = new JTextField();
		textField_2.setBounds(149, 111, 205, 27);
		contentPane.add(textField_2);
		textField_2.setColumns(10);
		
		JLabel label = new JLabel("\u6388\u6743\u7801");
		label.setBounds(53, 114, 81, 21);
		contentPane.add(label);
		

		try {  
	        //读取电脑信息
			String macAddress=HardwareUtil.getMACAddress(); 
            String cpuSerial=HardwareUtil.getCPUSerial(); 
            String str =macAddress+"@"+cpuSerial;  
            String encryptResult = DesUtil.encrypt(str);
            textField.setText(encryptResult);
		}
		catch(Exception ex) {
			
		}
		WindowUtils.centerWindow(null, this);
	}
}
