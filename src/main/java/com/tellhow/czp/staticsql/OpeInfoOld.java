package com.tellhow.czp.staticsql;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class OpeInfoOld extends OPEService{
	
	/*
	 * 描述：获取执行设备信息
	 * @param
	 * @param
	 * @param
	 * */
	public  String getExeEqu(String equip, String starOne,String endTwo) {
		String sql="SELECT RU.EQUIPID,E.STATION_ID,RU.SRCSTATUS,RU.STATECODE FROM "+CBSystemConstants.opcardUser+"t_a_RULEUSERCB_ACTION RU,"+CBSystemConstants.opcardUser+"T_E_EQUIPINFO E WHERE RU.EQUIPID=E.EQUIP_ID AND RU.F_RULEID =(SELECT T.RULEID FROM "+CBSystemConstants.opcardUser+"t_a_RULEUSERZB T WHERE T.EQUIPID='"+equip+"' AND T.SRCSTATUS='"+starOne+"' AND T.STATECODE='"+endTwo+"')";
		return sql;
	}
	
	/*
	 * 描述：获取前置设备信息
	 * @param
	 * @param
	 * @param
	 * */
	public  String getPreEqu(String pid, String srcsta, String status) {
		 String sql=" select eq.station_id, con.equipid,con.srcstatus,con.isnegated from "+CBSystemConstants.opcardUser+"t_a_ruleusercb_condition con , "+CBSystemConstants.opcardUser+"t_e_equipinfo eq  where  eq.equip_id =con.equipid and con.F_RULEID =(select zb.ruleid from "+CBSystemConstants.opcardUser+"t_a_ruleuserzb zb where zb.equipid='"+pid+"' and zb.srcstatus= '"+srcsta+"' and zb.statecode='"+status+"')";
		 return sql;
	}
	
	/*
	 * 描述：获取关联开关信息
	 * @param
	 * @param
	 * @param
	 * */
	public  String getLs() {
		 String sql="select t.equip_id,t.equip_name from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_equiptype s,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype='monthlinelinkswitch' and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='?' order by a.voltage_code desc,t.equip_name";
		 return sql;
	}	
	
	/*
	 * 描述：查询组件事件
	 * @param
	 * @param
	 * @param
	 * */
	public  String getQueCompement(String code) {
		 String sql = "and DEVICE_TYPE='1' and SCS_OBJ_CODE in (select a.equip_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo a where a.station_id= '"+code+"')";
		 return sql;
	}
	
	/*
	 * 描述：获取备自投参数
	 * @param
	 * @param
	 * @param
	 * */
	public  String getBasParam(String code) {
		 String sql = "and DEVICE_TYPE='1' and SCS_OBJ_CODE in (select a.equip_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo a where a.station_id= '"+code+"')";
		 return sql;
	}
	
	/*
	 * 描述：获取母联开关下拉
	 * @param
	 * @param
	 * @param
	 * */
	public  String getCs() {
		 String sql = "select t.equip_id,t.equip_name from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_equiptype s,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype='monthlinelinkswitch' and t.equip_id in (select e.scs_obj_code from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE e where e.device_type='1' and e.scs_obj_code is not null) and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='?' order by a.voltage_code desc,t.equip_name";
		 return sql;
	}
	
	/*
	 * 描述：获取备自投跳闸关系参数
	 * @param
	 * @param
	 * @param
	 * */
	public  String getTriParam(String code) {
		 String sql = "and SCS_OBJ in (select a.equip_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo a where a.station_id= '"+code+"')";
		 return sql;
	}
	
	/*
	 * 描述：获取EquipOrganCheckChoose参数
	 * @param
	 * @param
	 * @param
	 * */
	public  String getEOCCParam(String organid,PowerDevice pd) {
		 String sql = "update "+CBSystemConstants.opcardUser+"t_e_equipinfo set orga_id ='"+organid+"' where equip_id='"+pd.getPowerDeviceID()+"'";
		 return sql;
	}
	
	/*
	 * 描述：获取EquipPermissionCheckChoose参数
	 * @param
	 * @param
	 * @param
	 * */
	public  String getEPCCParam(String organid,PowerDevice pd) {
		 String sql = "update "+CBSystemConstants.opcardUser+"t_e_equipinfo set dispatch_permission_id ='"+organid+"' where equip_id='"+pd.getPowerDeviceID()+"'";
		 return sql;
	}
	
	/*
	 * 描述：GetDifferStatusDevices同步DEVICEEQUIPINFO表设备
	 * @param
	 * @param
	 * @param
	 * */
	public  String getDsd() {
		 String sql="INSERT INTO "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO SELECT T.EQUIP_ID,'-1',NULL,NULL,NULL,'-1','-1','-1','-1'  FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T WHERE T.EQUIP_ISDEL=0 and t.EQUIP_ID not in (SELECT T2.EQUIPID FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T2)";
		 return sql;
	}
	
	/*
	 * 描述：GetDifferStatusDevices同步DEVICEEQUIPINFO表设备
	 * @param
	 * @param
	 * @param
	 * */
	public  String getDsd2() {
		 String sql="delete from "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO t where t.equipid not in (select a.equip_id from "+CBSystemConstants.opcardUser+"T_E_EQUIPINFO a)";
		 return sql;
	}
	
	/*
	 * 描述 InitDeviceRunType sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getIdrt(String stationID) {
		String sql = "SELECT COUNT(*) FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO A,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO B WHERE A.EQUIPID=B.EQUIP_ID AND A.DEVICESTATUS='-1' AND B.STATION_ID='"+stationID+"'";
		return sql;
	}
	
	/*
	 * 描述 InitDeviceRunStatus sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getIdrs(String stationID) {
    	String sql = "SELECT COUNT(*) FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO A,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO B WHERE A.EQUIPID=B.EQUIP_ID AND A.DEVICESTATUS='-1' AND B.STATION_ID='"+stationID+"'";
		return sql;
	}
	
	/*
	 * 描述 InitDeviceRunStatus sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getIdrs0(String stationID) {
		String sql="SELECT T.EQUIPID ,T.DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T1 WHERE T.EQUIPID=T1.EQUIP_ID  AND T1.STATION_ID='"+stationID+"'";
		return sql;
	}
	
	/*
	 * 描述 InitDeviceRunStatus sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getIdrs1(String stationID) {
		String sql="SELECT T.EQUIPID ,T.DISPATCH DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T1 WHERE T.EQUIPID=T1.EQUIP_ID  AND T1.STATION_ID='"+stationID+"'";
		return sql;
	}
	
	/*
	 * 描述 InitDeviceRunStatus sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getIdrs2(String stationID) {
		String sql="SELECT T.EQUIPID ,T.MONITORING DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T1 WHERE T.EQUIPID=T1.EQUIP_ID  AND T1.STATION_ID='"+stationID+"'";
		return sql;
	}
	
	/*
	 * 描述 CBSystemConstants sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getPowDevice(String powerEquipID) {
		String sql="select * from "+CBSystemConstants.opcardUser+"t_e_equipinfo e,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO s where e.equip_id =s.EQUIPID and e.equip_id='"+powerEquipID+"' ";
		return sql;
	}
	
	/*
	 * 描述 CBSystemConstants sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getOrga(String powerDeviceID) {
		String sql="select * from "+CBSystemConstants.opcardUser+"t_e_equipinfo e,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO s where e.equip_id =s.EQUIPID and e.equip_id='"+powerDeviceID+"' ";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getStationDeviceList(String powerStationID) {
		String ver_id = null;
		String unitcode = CBSystemConstants.unitCode;//获取unitcode
		String selectSql = "select a.ver_id from "+CBSystemConstants.opcardUser+"t_a_statenormalver a where a.ver_flag='1' and a.opcode='"+CBSystemConstants.opCode+"'";
		try {
			ver_id = DBManager.queryForString(selectSql);//获取到常状态最新版本号
		} catch (Exception e) {
			// TODO: handle exception
		}
		String sql="select F.*,G.OBJ_STATE from (SELECT A.ORGA_ID,A.DISPATCH_PERMISSION_ID,A.EQUIP_ID,A.EQUIP_CODE,NVL(E.EQUIPNAME,A.EQUIP_NAME) EQUIP_NAME,A.CIM_ID,B.STATION_ID,B.STATION_CODE,B.STATION_NAME,C.VOLTAGE_CODE,D.EQUIPTYPE_FLAG,E.DEVICERUNTYPE,E.DEVICERUNMODEL,E."+CBSystemConstants.getStatusField()+" from "+CBSystemConstants.opcardUser+"t_e_EQUIPINFO A,"+CBSystemConstants.opcardUser+"t_e_SUBSTATION B,"+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL C, "+CBSystemConstants.opcardUser+"t_e_EQUIPTYPE D, "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO E where A.STATION_ID=B.STATION_ID and A.EQUIP_ISDEL<>1 and A.VOLTAGE_ID=C.VOLTAGE_ID and A.EQUIPTYPE_ID=D.EQUIPTYPE_ID and A.EQUIP_ID=E.EQUIPID and B.STATION_ID='"+powerStationID+"') F Left JOIN " +
		"( select STATION_ID, obj_id, obj_state from "+CBSystemConstants.opcardUser+"T_A_STATENORMAL where ver_id='"+ver_id+"' and STATION_ID='"+powerStationID+"') G " +
		"On f.EQUIP_ID = G.obj_id ";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getEquipType() {
		String sql="SELECT T.EQUIPTYPE_FLAG, T.EQUIPTYPE_NAME FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPTYPE T WHERE T.EQUIPTYPE_ISDEL=0 AND T.EQUIPTYPE_FLAG IS NOT NULL AND T.CIM_ID IS NOT NULL ORDER BY T.EQUIPTYPE_ORDER";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getStationProtectList(String powerStationID) {
		String sql="SELECT A.equipid,A.PROTECTID,A.PROTECTNAME,C.DEVICETYPEID,C.PROTECTTYPENAME,A.PROTECTSTATUS FROM "+CBSystemConstants.opcardUser+"T_A_PROTECTEQUIP A,"+CBSystemConstants.opcardUser+"T_E_EQUIPINFO B,"+CBSystemConstants.opcardUser+"T_A_PROTECTINFO C WHERE A.EQUIPID=B.EQUIP_ID AND A.PROTECTTYPEID=C.PROTECTTYPEID AND B.STATION_ID='"+powerStationID+"'";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getStationToplogy(String powerStationID) {
		String sql="SELECT A.CONNECTIVITYNODE_ID,B.EQUIP_ID,B.EQUIP_NAME,B.STATION_ID FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPTERMINAL A join "+CBSystemConstants.opcardUser+"t_e_EQUIPINFO B on A.EQUIP_ID=B.EQUIP_ID where B.STATION_ID='"+powerStationID+"'";
		return sql;
	}
	
	public  String getStationToplogyLine(String powerStationID) {
		String sql="SELECT A.CONNECTIVITYNODE_ID,B.EQUIP_ID,B.EQUIP_NAME,B.STATION_ID FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPTERMINAL A join "+CBSystemConstants.opcardUser+"t_e_EQUIPINFO B on A.EQUIP_ID=B.EQUIP_ID where B.STATION_ID='"+powerStationID+"'";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getSysTelemetering() {
		String sql="select a.equipid,a.meastype,a.measid,a.measvalue,a.measname from "+CBSystemConstants.opcardUser+"t_a_telemetering a,"+CBSystemConstants.opcardUser+"t_e_equipinfo b,"+CBSystemConstants.opcardUser+"t_e_substationtopology c where a.equipid=b.equip_id and b.equip_id=c.equip_id";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getFacTelemetering() {
		String sql="select a.equipid,a.meastype,a.measid,a.measvalue,a.measname from "+CBSystemConstants.opcardUser+"t_a_telemetering a,"+CBSystemConstants.opcardUser+"t_e_equipinfo b,"+CBSystemConstants.opcardUser+"t_e_substation c where a.equipid=b.equip_id and b.station_id=c.station_id and c.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getDeviceState() {
		String sql="select a.EQUIP_ID,a.EQUIP_STATE from "+CBSystemConstants.opcardUser+"t_e_equipstate a,"+CBSystemConstants.opcardUser+"t_e_equipinfo b where a.equip_id=b.equip_id and b.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getActionNote() {
		String sql="select * from "+CBSystemConstants.opcardUser+"t_g_actionnote a,"+CBSystemConstants.opcardUser+"t_e_equipinfo b where a.tobjectid=b.equip_id and b.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getActionGroundLine() {
		String sql="select * from "+CBSystemConstants.opcardUser+"t_g_actiongroundline a,"+CBSystemConstants.opcardUser+"t_e_equipinfo b where a.tobjectid=b.equip_id and b.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getActionCard() {
		String sql="select * from "+CBSystemConstants.opcardUser+"t_g_actioncard a,"+CBSystemConstants.opcardUser+"t_e_equipinfo b where a.tobjectid=b.equip_id and b.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getStationRMDevice() {
		String sql="select * from "+CBSystemConstants.opcardUser+"t_a_removabledevice g where ? in  (select e.station_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo e where e.equip_id=g.equipid) and opcode='"+CBSystemConstants.opCode+"'";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getStationBZTDevice(String stationID) {
		String sql="select * from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE a,"+CBSystemConstants.opcardUser+"T_A_ECSAUTRECORD b, "+CBSystemConstants.opcardUser+"t_e_equipinfo c,"+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL v where c.VOLTAGE_ID=v.VOLTAGE_ID and a.device_type='1' and a.scs_id=b.scs_id and a.scs_obj_code=c.equip_id and c.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getStationRelateDevice() {
		String sql="select * from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE a,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD b, "+CBSystemConstants.opcardUser+"t_e_equipinfo c,"+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL v where c.VOLTAGE_ID=v.VOLTAGE_ID and t.scs_id=a.scs_id and a.relation_id=b.device_id and a.scs_obj=c.equip_id and c.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 WebServiceUtil sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getYXFromDB(String stationID) {
		String sql="select t.equip_id,decode(n.value,1,1,2,0) status from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YXVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=to_char(n.pid) and n.type!=3 union all select t.equip_id,decode(n.value,1,1,2,0) status from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YXVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR||'1'=t.cim_id and m.aliasName=to_char(n.pid) and n.type!=3 and t.cim_id like '%XC1'";
		return sql;
	}
	
	/*
	 * 描述 WebServiceUtil sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getYCFromDB(String stationID) {
		String sql="select to_char(n.pid) pid,round(n.value,1) value from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YCVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=to_char(n.pid)";
		return sql;
	}
	
	/*
	 * 描述 WebServiceUtil sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getXLYGFromDB(String equipID) {
		String sql="select value from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YCVALUE n where m.measurementtype='MeasType-12' and t.equip_id='"+equipID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=to_char(n.pid)";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getPowersLineByLine(PowerDevice line) {
		String equipID=line.getPowerDeviceID();
		String sql="SELECT T.EQUIP_ID,T.EQUIP_NAME,T2.VOLTAGE_CODE,T7.EQUIPTYPE_FLAG,T.STATION_ID,T3.STATION_NAME,T.CIM_ID,T4.DEVICESTATUS,T4.DEVICERUNTYPE ,t8.flag,T.CIM_ID " +
		   " FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T left join "+CBSystemConstants.opcardUser+"T_A_LINEWAY t8 on t.equip_id=t8.lineequipid,"+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL T2,"+CBSystemConstants.opcardUser+"t_e_SUBSTATION T3,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T4,\n" +
	       " "+CBSystemConstants.opcardUser+"t_e_SUBSTATIONTOPOLOGY T6,"+CBSystemConstants.opcardUser+"t_e_EQUIPTYPE T7 WHERE T.EQUIPTYPE_ID=T7.EQUIPTYPE_ID AND T.VOLTAGE_ID=T2.VOLTAGE_ID AND T.STATION_ID=T3.STATION_ID AND  T.EQUIP_ID=T4.EQUIPID " +
	       " AND T.EQUIP_ID=T6.EQUIP_ID AND T6.LINE_ID IN (SELECT B.LINE_ID FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPINFO A,"+CBSystemConstants.opcardUser+"t_e_SUBSTATIONTOPOLOGY B WHERE A.EQUIP_ID=B.EQUIP_ID AND A.EQUIP_ID='"+equipID+"')";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getLineTidy(String line_CIM_ID) {
		String sql="SELECT T1.STATION_ID,T.FLAG FROM "+CBSystemConstants.opcardUser+"t_a_LINEWAY T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T1 WHERE\n" +
	       "T.LINEEQUIPID=T1.EQUIP_ID AND T1.CIM_ID='"+line_CIM_ID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getStationID_(String EquipID) {
		String sql="select t.station_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo t  Where t.equip_id='"+EquipID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getEquipName(String EquipID) {
		String sql="select t.equip_name from "+CBSystemConstants.opcardUser+"t_e_equipinfo t  Where t.equip_id='"+EquipID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getEquipID(String StationID,String EquipName) {
		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo t Where t.station_id ='"+StationID+"' and t.equip_name like '%"+EquipName+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getrevdiv(String StationID,String EquipName) {
		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo t Where t.station_id ='"+StationID+"' and replace(t.equip_name,'-','') like '%"+EquipName+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getGKdiv(String StationID,String EquipName) {
		String tempname=EquipName.substring(0, 4);
		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_equiptype a Where t.equiptype_id=a.equiptype_id and t.station_id ='"+StationID+"' and a.equiptype_flag='GroundDisconnector' and t.equip_name like '%"+tempname+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getdiv(String StationID,String EquipName) {
		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo t Where t.station_id ='"+StationID+"' and t.equip_name='"+EquipName+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getswitchdiv(String StationID, String EquipName) {
		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo t Where t.station_id ='"+StationID+"' and t.equip_name='"+EquipName+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getEquipID(String stationID, String equipNum, String equipType) {
		String sql="select t.equip_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_equiptype a Where t.equiptype_id=a.equiptype_id and t.station_id ='"+stationID+"' and a.equiptype_flag='"+equipType+"' and t.equip_name like '%"+equipNum+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getEquipIDByNameType(String stationID, String equipNum, String equipType) {
		String sql="select t.equip_id,t.equip_name from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_equiptype a Where t.equiptype_id=a.equiptype_id and a.equiptype_flag='"+equipType+"' and t.station_id ='"+stationID+"' and  t.equip_name like '%"+equipNum+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getDevice(String devCode, String StationName,String devType) {
		
		String tj="";
		if(devType.equals(SystemConstants.InOutLine)){
			tj="t.equip_name like '%"+devCode.substring(0,1)+"%' and t.equip_name like '%"+devCode.substring(1,2)+"%' ";
			if(devCode.substring(2,3).equals("Ⅰ"))
				tj=tj+" and (t.equip_name like '%Ⅰ%' or t.equip_name like '%I%') and  t.equip_name not like '%II%'" ;
			if(devCode.substring(2,3).equals("Ⅱ"))
				tj=tj+" and (t.equip_name like '%Ⅱ%' or t.equip_name like '%II%')" ;
			
		}else{
			if(devCode.indexOf("、")>0)
			    tj="t.equip_name like '%"+devCode.split("、")[1]+"%'";
			else 
				tj="t.equip_name like '%"+devCode+"%'";
		}
		
		String sql="select t.equip_id,t.station_id from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_substation s,"+CBSystemConstants.opcardUser+"t_e_equiptype t2\n" +
      " Where t.station_id=s.station_id and t.equiptype_id=t2.equiptype_id and " +
      tj+" and s.station_name like '%"+StationName+"%' and t2.equiptype_flag='"+devType+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String updateDevVoltage(PowerDevice pd,CodeNameModel codeNameModel) {		
		String equipID = pd.getPowerDeviceID();
		String VOLTAGE_CODE=codeNameModel.getCode();
		String sql = null;
		//查找要更新的电压等级ID
		String sql2="select t.VOLTAGE_ID from "+CBSystemConstants.opcardUser+"t_e_voltagelevel t where t.VOLTAGE_CODE='"+VOLTAGE_CODE+"'";
		List voltagelist2=DBManager.queryForList(sql2);
		if(voltagelist2.size()>0){
			Map map2=(Map)voltagelist2.get(0);
			String VOLTAGE_ID2=(String) map2.get("VOLTAGE_ID");
			//更新电压等级ID
			sql="update "+CBSystemConstants.opcardUser+"t_e_equipinfo t SET t.VOLTAGE_ID='"+VOLTAGE_ID2+"' WHERE t.EQUIP_ID='"+equipID+"'";
		}
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String updateDevtypemethods(PowerDevice pd,CodeNameModel codeNameModel) {		
		String code = codeNameModel.getCode();
		String equipID = pd.getPowerDeviceID();
		String sql="UPDATE "+CBSystemConstants.opcardUser+"t_e_equipinfo t SET t.EQUIPTYPE_ID='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String updateAdjustablePipeOrgan(PowerDevice pd,CodeNameModel codeNameModel) {		
		String code = codeNameModel.getCode();
		String equipID = pd.getPowerDeviceID();
		String sql="UPDATE "+CBSystemConstants.opcardUser+"t_e_equipinfo t SET t.ORGA_ID='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String updatePermitOrgan(PowerDevice pd,CodeNameModel codeNameModel) {		
		String code = codeNameModel.getCode();
		String equipID = pd.getPowerDeviceID();
		//修改调管机构中的机构ID
		String sql="UPDATE "+CBSystemConstants.opcardUser+"t_e_equipinfo t SET t.dispatch_permission_id='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String updateDevice1(String powerStationID) {		
        String sql = "select t1.scs_id, t1.station_id,t2.change_state from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t1, "+CBSystemConstants.opcardUser+"T_A_ECSAUTRECORD t2,"+CBSystemConstants.opcardUser+"t_e_equipinfo t3 where t1.scs_id = t2.scs_id and t1.scs_obj_code=t3.equip_id and t3.station_id = '" + powerStationID + "'";//查询当前厂站的安置设备
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String updateDevice2(String powerStationID) {		
		String sql = "select t2.relation_id,t3.change_state from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t1, "+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE t2,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD t3,"+CBSystemConstants.opcardUser+"t_e_equipinfo t4 where t1.scs_id = t2.scs_id and t1.scs_obj_code=t4.equip_id and t2.relation_id = t3.device_id and t4.station_id = '" + powerStationID + "'";
		return sql;
	}
	
	/*
	 * 描述 AdjustablePipeOrgan sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String checkValue() {		
		String sql = "select t.ORGA_ID from "+CBSystemConstants.opcardUser+"t_e_equipinfo t where t.EQUIP_ID='";
		return sql;
	}
	
	/*
	 * 描述 AdjustablePipeOrgan sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverProperty2(PowerDevice pd) {		
		String sql = "select t.ORGA_ID from "+CBSystemConstants.opcardUser+"t_e_equipinfo t where t.EQUIP_ID='"+pd.getPowerDeviceID()+"'";
		return sql;
	}
	
	/*
	 * 描述 AdjustablePipeOrgan sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverProperty3(PowerDevice pd) {		
		String sql = "select t.DISPATCH_PERMISSION_ID from "+CBSystemConstants.opcardUser+"t_e_equipinfo t where t.EQUIP_ID='"+pd.getPowerDeviceID()+"'";
		return sql;
	}
	
	/*
	 * 描述 PermitOrgan sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String PermitOrgan2() {		
		String sql = "select t.DISPATCH_PERMISSION_ID from "+CBSystemConstants.opcardUser+"t_e_equipinfo t where t.EQUIP_ID='";
		return sql;
	}
	
	/*
	 * 描述 TwodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TwodeviceDialog1() {		
		String sql = "select t.equip_id,t.equip_name from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_equiptype s,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype in('lineswitch') and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='";
		return sql;
	}
	
	/*
	 * 描述 TwodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TwodeviceDialog2() {		
		String sql = "select t.equip_id,t.equip_name from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_equiptype s,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype in('lowtransswitch','monthlinelinkswitch','jiedibianswitch') and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='";
		return sql;
	}
	
	/*
	 * 描述 TwodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TwodeviceDialog3() {		
		String sql = "select t.equip_name from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_equiptype s,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype in('lineswitch') and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='";
		return sql;
	}
	
	/*
	 * 描述 TwodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TwodeviceDialog4() {		
		String sql = "select t.equip_name from "+CBSystemConstants.opcardUser+"t_e_equipinfo t,"+CBSystemConstants.opcardUser+"t_e_equiptype s,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype in('lowtransswitch','monthlinelinkswitch') and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='";
		return sql;
	}
	
	/*
	 * 描述 DeviceStatusManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DeviceStatusManager(String zbid) {		
		String sql = "select t.equipid,s.station_id,t.beginstatus,t.endstate from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t,"+CBSystemConstants.opcardUser+"t_e_equipinfo s where t.equipid=s.equip_id and t.cardid='"+zbid+"' and t.beginstatus in ('4','5') order by t.stateorder";
		return sql;
	}
	
	/*
	 * 描述 TicketDBManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TicketDBManager1() {		
		String sql = "t_a_CZPZB T left join "+CBSystemConstants.opcardUser+"t_e_equipinfo a on t.equipid=a.equip_id left join "+CBSystemConstants.opcardUser+"t_e_equiptype b on a.equiptype_id=b.equiptype_id WHERE  ";
		return sql;
	}
	
	/*
	 * 描述 TicketDBManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TicketDBManager2() {		
		String sql = "t_a_CZPZB T left join "+CBSystemConstants.opcardUser+"t_e_equipinfo a on t.equipid=a.equip_id left join "+CBSystemConstants.opcardUser+"t_e_equiptype b on a.equiptype_id=b.equiptype_id WHERE  ";
		return sql;
	}
	
	/*
	 * 描述 TicketDBManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public String TicketDBManagerjk() {		
		String sql = "t_a_CZPZB T "
				+ "left join "+CBSystemConstants.opcardUser+"t_a_Czpmx F  on T.ZBID = F.F_ZBID "
				+ "left join (select a.equip_id,s.equiptype_flag from "+CBSystemConstants.equipUser+"T_EQUIPINFO a,"+CBSystemConstants.equipUser+"T_EQUIPTYPE s "
				+ "where a.equiptype_id=s.equiptype_id union all select c.id,'ACLineSegment' from "+CBSystemConstants.equipUser+"T_C_ACLINEEND c) b "
				+ "on t.equipid=b.equip_id WHERE  ";
		return sql;
	}
	
	/*
	 * 描述 MonitorTicketTypePanel sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String MonitorTicketTypePanel(String zbid) {		
		String sql = "select b.station_name,a.equip_name,c.equiptype_name,t.beginstatus,t.endstate from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t,"+CBSystemConstants.opcardUser+"t_e_equipinfo a,"+CBSystemConstants.opcardUser+"t_e_substation b,"+CBSystemConstants.opcardUser+"t_e_equiptype c where t.equipid=a.equip_id and a.station_id=b.station_id and a.equiptype_id=c.equiptype_id and t.cardid='"+zbid+"' order by t.stateorder";
		return sql;
	}
	
	/*
	 * 描述 OperateTicketConvertPanel sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String OperateTicketConvertPanel(String zbid) {		
		String sql = "select b.station_name,a.equip_name,c.equiptype_name,t.beginstatus,t.endstate from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t,"+CBSystemConstants.opcardUser+"t_e_equipinfo a,"+CBSystemConstants.opcardUser+"t_e_substation b,"+CBSystemConstants.opcardUser+"t_e_equiptype c where t.equipid=a.equip_id and a.station_id=b.station_id and a.equiptype_id=c.equiptype_id and t.cardid='"+zbid+"' order by t.stateorder";
		return sql;
	}
	
	/*
	 * 描述 TermInversion sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TermInversion() {		
		String sql = "select t6.equipid,t6.beginstatus,t6.endstate,t7.station_id,t8.station_name from "+CBSystemConstants.opcardUser+"t_a_czpactionstate t6 ,"+CBSystemConstants.opcardUser+"t_e_equipinfo t7 ,"+CBSystemConstants.opcardUser+"t_e_substation t8"
		+ " where t6.equipid=t7.equip_id and t7.station_id=t8.station_id  order by t6.stateid asc";
		return sql;
	}
	
	/*
	 * 描述 RuleCustomDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String RuleCustomDialog() {		
		String sql = "select equiptype_flag ,equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 DictionarysModelInit sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DictionarysModelInit() {		
		String sql = "SELECT T.STATE_CODE,T.STATE_NAME,T.SWITCHSTATE_CODE,T.SWITCHSTATE_NAME,S.EQUIPTYPE_FLAG FROM "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE T,"+CBSystemConstants.opcardUser+"T_E_EQUIPTYPE S WHERE T.EQUIPTYPE_ID=S.EQUIPTYPE_ID";
		return sql;
	}
	
	/*
	 * 描述 DeviceStateMentManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getDeviceTypeSql() {		
		String sql=" SELECT T.EQUIPTYPE_NAME,T.EQUIPTYPE_FLAG FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPTYPE T WHERE T.EQUIPTYPE_FLAG IN"+
				   " (SELECT T.DEVICETYPEID FROM "+CBSystemConstants.opcardUser+"t_a_devicestateinfo T where t.islock = '0'  GROUP BY T.DEVICETYPEID)  ORDER BY T.EQUIPTYPE_CODE";
		return sql;
	}
	
	/*
	 * 描述 DeviceStateMentManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getDeviceStatusSql(String deviceType) {		
        String sql="SELECT NVL(T.SWITCHSTATE_CODE,T.STATE_CODE) STATECODE,NVL(T.STATE_NAME,T.SWITCHSTATE_NAME) STATENAME FROM "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE T,"+CBSystemConstants.opcardUser+"T_E_EQUIPTYPE S WHERE T.EQUIPTYPE_ID=S.EQUIPTYPE_ID AND S.EQUIPTYPE_FLAG='"+deviceType+"' ORDER BY T.STATE_CODE";
		return sql;
	}
	
	/*
	 * 描述 CardDescDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getcombobox1Sql() {		
		String sql="select equiptype_flag ,equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String importOMSSql(String zbid) {		
		String sql="select t.*,t2.*,t4.equiptype_name from "+CBSystemConstants.opcardUser+"t_a_czpzb t join "+CBSystemConstants.opcardUser+"t_a_poweruserinfo t2 on t.npr=t2.userid left join "+CBSystemConstants.opcardUser+"t_e_equipinfo t3 on t.equipid=t3.equip_id left join "+CBSystemConstants.opcardUser+"t_e_equiptype t4 on t3.equiptype_id=t4.equiptype_id  where t.zbid='"+ zbid + "'";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXBD5000 sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String importOMSSql1(String zbid) {		
		String sql="select t.*,t2.*,t4.equiptype_name from "+CBSystemConstants.opcardUser+"t_a_czpzb t join "+CBSystemConstants.opcardUser+"t_a_poweruserinfo t2 on t.npr=t2.userid left join "+CBSystemConstants.opcardUser+"t_e_equipinfo t3 on t.equipid=t3.equip_id left join "+CBSystemConstants.opcardUser+"t_e_equiptype t4 on t3.equiptype_id=t4.equiptype_id  where t.zbid='"+ zbid + "'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getPowersLineBySysLineSql(PowerDevice line) {
		String lineID=line.getPowerDeviceID();
		String sql="SELECT T.EQUIP_ID,T.EQUIP_NAME,T2.VOLTAGE_CODE,T7.EQUIPTYPE_FLAG,T.STATION_ID,T3.STATION_NAME,T.CIM_ID,T4.DEVICESTATUS,T4.DEVICERUNTYPE,T4.DEVICERUNMODEL,'-1',T.CIM_ID " +
		   " FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T,"+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL T2,"+CBSystemConstants.opcardUser+"t_e_SUBSTATION T3,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T4,\n" +
	       " "+CBSystemConstants.opcardUser+"t_e_SUBSTATIONTOPOLOGY T6,"+CBSystemConstants.opcardUser+"t_e_EQUIPTYPE T7 WHERE T.EQUIPTYPE_ID=T7.EQUIPTYPE_ID AND T.VOLTAGE_ID=T2.VOLTAGE_ID AND T.STATION_ID=T3.STATION_ID AND  T.EQUIP_ID=T4.EQUIPID " +
	       " AND T.EQUIP_ID=T6.EQUIP_ID AND T6.LINE_ID IN (SELECT B.LINE_ID FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPINFO A,"+CBSystemConstants.opcardUser+"t_e_SUBSTATIONTOPOLOGY B WHERE A.EQUIP_ID=B.EQUIP_ID AND B.LINE_ID='"+lineID+"')";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String getProtectByTypeSql(PowerDevice pd,String protectType) {
		String sql="select p.protecttypeid,p.protecttypename,p.runtype from "+CBSystemConstants.opcardUser+"t_a_protectinfo p ,"+CBSystemConstants.opcardUser+"t_e_equiptype t  where protectkind='"+protectType+"' and t.equiptype_id=p.equiptypeid and t.cim_id ='"+pd.getDeviceType()+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String updateDevtypemethods(CodeNameModel codeNameModel) {
		String code = codeNameModel.getCode();
		String sql="select t.equiptype_flag from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_id='"+code+"'";
		return sql;
	}
	
	/*
	 * 描述 GetDeviceMenuModel sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String executeSql() {
		String sql="select equiptype_code from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 Devicetypemethods sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DevicetypemethodsSql() {
		String sql="select t.equiptype_id,t.equiptype_name,t.equiptype_flag  from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.EQUIPTYPE_FLAG is not null and t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorpetySql() {
		String sql="select t.equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorpetySql1() {
		String sql="select equiptype_code from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorpetySql2() {
		String sql="select b.state_name, b.state_code from "+CBSystemConstants.opcardUser+"t_a_protectequip a,"+CBSystemConstants.opcardUser+"t_e_equiptypestate b where a.protectid='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorpetySql3() {
		String sql="select equiptype_code from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorpetySql4() {
		String sql="select b.state_name ,b.state_code from "+CBSystemConstants.opcardUser+"t_e_equiptypestate b where  b.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql() {
		String sql="select equiptype_id from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql1() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql2() {
		String sql="select equiptype_id from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql3() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql4() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql5() {
		String sql="select equiptype_id from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql6() {
		String sql="select equiptype_id from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql7() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql8() {
		String sql="select equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql9() {
		String sql="select equiptype_id,equiptype_name,equiptype_flag from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='Reclosing'";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipManagerDialogSql10() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"t_e_equiptypestate where equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipOperationManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipOperationManagerDialogSql() {
		String sql="select equiptype_flag ,equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 EquipOperationManagerDialogtype1 sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipOperationManagerDialogSqltype1() {
		String sql= "select code ,name from "  
		      + ""+CBSystemConstants.opcardUser+"T_A_DICTIONARY t where t.unitcode='system' and codetype='cardbuildtype'";
	return sql;
	}
	
	/*
	 * 描述 EquipOperationManagerDialogtype2 sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipOperationManagerDialogSqltype2() {
		String sql= "select '0','智能票' from dual union select '1','点图票' from dual union select '2','设备对位' from dual union select '9','基本' from dual";
		return sql;
	}
	
	/*
	 * 描述 EquipStateDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipStateDialogSql() {
		String sql="select equiptype_id,equiptype_parent_id,equiptype_code,equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 EquipStateDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipStateDialogSql1() {
		String sql="select t.* from "+CBSystemConstants.opcardUser+"t_e_equiptypestate t where t.equiptype_id=( select EQUIPTYPE_ID from "+CBSystemConstants.opcardUser+"t_e_equiptype   where CIM_ID is not null and  EQUIPTYPE_NAME='";
		return sql;
	}
	
	/*
	 * 描述 EquipTypeDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String EquipTypeDialogSql() {
		String sql="select equiptype_id,equiptype_parent_id,equiptype_code,equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String SystableDialogSql() {
		String sql="select t.equiptype_id,t.equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_flag in (select devicetypeid from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO where islock = '0'  ) order by t.equiptype_order";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String SystableDialogSql1() {
		String sql="select t.equiptype_id,t.equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_flag in (select devicetypeid from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO where islock = '0' ) order by t.equiptype_order";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String SystableDialogSql2() {
		String sql="select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String SystableDialogSql3() {
		String sql="select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String SystableDialogSql4() {
		String sql="select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String SystableDialogSql5() {
		String sql="select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String PowerSystemDBOperatorSql() {
		String sql="SELECT A.LINE_ID,A.LINE_CODE,A.LINE_NAME,A.CIM_ID,B.VOLTAGE_CODE,C.DEVICESTATUS FROM "+CBSystemConstants.opcardUser+"t_e_LINEINFO A,"+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL B,(SELECT D.LINE_ID,MIN(F.DEVICESTATUS) DEVICESTATUS FROM "+CBSystemConstants.opcardUser+"t_e_LINEINFO D,"+CBSystemConstants.opcardUser+"t_e_SUBSTATIONTOPOLOGY E,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO F WHERE D.LINE_ID=E.LINE_ID AND E.EQUIP_ID=F.EQUIPID GROUP BY D.LINE_ID) C WHERE A.VOLTAGE_ID=B.VOLTAGE_ID AND A.LINE_ISDEL=0 AND A.LINE_ID=C.LINE_ID";
		return sql;
	}
	
	public  String PowerFeederOperatorSql() {
		String sql="SELECT A.LINE_ID,A.LINE_CODE,A.LINE_NAME,A.CIM_ID,B.VOLTAGE_CODE,C.DEVICESTATUS FROM "+CBSystemConstants.opcardUser+"t_e_LINEINFO A,"+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL B,(SELECT D.LINE_ID,MIN(F.DEVICESTATUS) DEVICESTATUS FROM "+CBSystemConstants.opcardUser+"t_e_LINEINFO D,"+CBSystemConstants.opcardUser+"t_e_SUBSTATIONTOPOLOGY E,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO F WHERE D.LINE_ID=E.LINE_ID AND E.EQUIP_ID=F.EQUIPID GROUP BY D.LINE_ID) C WHERE A.VOLTAGE_ID=B.VOLTAGE_ID AND A.LINE_ISDEL=0 AND A.LINE_ID=C.LINE_ID";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql() {
		String sql="select t.line_id from  "+CBSystemConstants.opcardUser+"t_e_lineinfo t where t.cim_id=?";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql1() {
		String sql="select t.line_id from "+CBSystemConstants.opcardUser+"t_e_lineinfo t where t.line_name like '%";
		return sql;
	}
	
	/*
	 * 描述 InOutLineTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String InOutLineTreeWidgetSql() {
		String sql = "select '线路' as line, d.voltage_code||'kV',"
			+ "c.line_id nodecode,c.line_name nodename from "+CBSystemConstants.opcardUser+"t_e_lineinfo c,"+CBSystemConstants.opcardUser+"t_e_voltagelevel d where c.voltage_id = d.voltage_id and d.voltage_value>=35 and c.line_id in (select a.line_id from "+CBSystemConstants.opcardUser+"t_e_substationtopology a group by a.line_id) order by line, d.voltage_value desc, nodename asc";
		return sql;
	}
	
	/*
	 * 描述 InOutLineTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String InOutLineTreeWidgetSql1() {
		String sql = "select t.LINE_ID,t.LINE_NAME from "+CBSystemConstants.opcardUser+"t_e_lineinfo t,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a where t.voltage_id=a.voltage_id and a.voltage_code in ('500','220','110') and t.LINE_NAME not like '%T接%' and t.LINE_NAME not like '%系统%' order by a.voltage_code desc,t.LINE_NAME";
		return sql;
	}
	
	/*
	 * 描述 ElecIslandAlgorithm sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String ElecIslandAlgorithmSql() {
		String sql = "select t.line_id,t.station_id from "+CBSystemConstants.opcardUser+"t_e_substationtopology t";
		return sql;
	}
	
	/*
	 * 描述 SautodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String SautodeviceDialogSql() {
		String sql = "select t.station_id,t.station_name from "+CBSystemConstants.opcardUser+"t_e_substation t,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a where t.voltage_id=a.voltage_id and a.voltage_code in ('220','110') and t.station_name not like '%T接%' and t.station_name not like '%系统%' order by a.voltage_code desc,t.station_name";
		return sql;
	}
	
	/*
	 * 描述 TriprelateDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TriprelateDialogSql() {
		String sql = "select t.station_id,t.station_name from "+CBSystemConstants.opcardUser+"t_e_substation t,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a where t.voltage_id=a.voltage_id and a.voltage_code in ('220','110') and t.station_name not like '%T接%' and t.station_name not like '%系统%' order by a.voltage_code desc,t.station_name";
		return sql;
	}
	
	/*
	 * 描述 GetDifferStatusDevices sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String GetDifferStatusDevicesSql() {
		String sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO SELECT E.EQUIP_ID,'0' FROM "+CBSystemConstants.opcardUser+"t_e_SUBSTATIONTOPOLOGY E WHERE E.EQUIP_ID not in (SELECT T.EQUIP_ID FROM "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO T)";
		return sql;
	}
	
	/*
	 * 描述 StationDeviceToplogy sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String StationDeviceToplogySql() {
		String sql = "SELECT T.* FROM "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO T,"+CBSystemConstants.opcardUser+"T_E_SUBSTATIONTOPOLOGY S WHERE T.EQUIP_ID=S.EQUIP_ID AND S.STATION_ID=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String PowerSystemDBOperatorSql1() {
		String sql = "SELECT STATION_NAME FROM "+CBSystemConstants.opcardUser+"t_e_SUBSTATION  WHERE STATION_ID=?";
		return sql;
	}
	
	public  String OrganListSql() {
		String sql = "select t.organid,t.organname,t.shortname from "+CBSystemConstants.opcardUser+"t_a_powerorgan t where t.isenabled='0'";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String PowerSystemDBOperatorSql2() {
		String sql = "SELECT A.STATION_ID,A.STATION_CODE,A.STATION_NAME,A.STATION_FLAG,A.CIM_ID,B.VOLTAGE_CODE,A.STATION_GRAPH FROM "+CBSystemConstants.opcardUser+"t_e_SUBSTATION A,"+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL B WHERE A.VOLTAGE_ID=B.VOLTAGE_ID AND A.STATION_ISDEL=0";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String PowerSystemDBOperatorSql3() {
		String sql = "SELECT T.LINE_ID,T.STATION_ID,T.EQUIP_ID FROM "+CBSystemConstants.opcardUser+"T_E_SUBSTATIONTOPOLOGY T where t.station_id is not null";
		return sql;
	}
	
	/*
	 * 描述 DataToDemo sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DataToDemoSql() {
		String sql = "update "+CBSystemConstants.opcardUser+"t_e_substation set station_name='演示厂站";
		return sql;
	}
	
	/*
	 * 描述 CZPOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorSql(String cardid) {
		String sql = "select s.equip_name,r.station_name,t.endstate from "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE t,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO s,"+CBSystemConstants.opcardUser+"t_e_SUBSTATION r where t.equipid=s.equip_id and s.station_id=r.station_id and t.cardid='"+cardid+"' and t.stateid=(select max(stateid) from "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE where cardid='"+cardid+"')";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorSD sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorsdSql(String cardid) {
		String sql = "select s.equip_name,r.station_name,t.endstate from "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE t,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO s,"+CBSystemConstants.opcardUser+"t_e_SUBSTATION r where t.equipid=s.equip_id and s.station_id=r.station_id and t.cardid="+cardid+" and t.stateid=(select max(stateid) from "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE where cardid="+cardid+")";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorSD sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorsdSql1() {
		String sql = "select t.orga_id,t.areacode from "+CBSystemConstants.opcardUser+"t_e_SUBSTATION t where t.station_name='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorSD sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorsdSql2() {
		String sql = "select t.station_id,t.orga_id,t.areacode from "+CBSystemConstants.opcardUser+"t_e_SUBSTATION t where t.station_name='";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql2(String facLineID) {
		String sql = "select t.line_id from "+CBSystemConstants.opcardUser+"t_e_substationtopology t where t.equip_id='"+facLineID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql3(String StationName) {
		String sql = "select * from "+CBSystemConstants.opcardUser+"t_e_substation t Where t.station_name='"+StationName+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql4(String StationName) {
		String sql = "select * from (select * from "+CBSystemConstants.opcardUser+"t_e_substation t Where t.station_name like '%"+StationName+"%' order by t.station_name) where rownum=1";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql5(String StationID) {
		String sql = "select t.station_name from "+CBSystemConstants.opcardUser+"t_e_substation t where t.station_id='"+StationID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql6(String StationName) {
		String sql="select STATION_ID from "+CBSystemConstants.opcardUser+"t_e_substation t Where replace(replace(t.station_name,'厂',''),'站','') = '"+StationName+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql7(String StationName) {
		String sql="select STATION_ID,STATION_NAME from "+CBSystemConstants.opcardUser+"t_e_substation t Where t.station_name like '%"+StationName+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql8() {
		String sql="select s.station_id,s.station_name from "+CBSystemConstants.opcardUser+"t_e_substation s where s.station_name like ?";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql9(String StationName) {
		String sql="select STATION_ID,STATION_NAME from "+CBSystemConstants.opcardUser+"t_e_substation t Where t.station_name like '%"+StationName+"%'";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpery sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorperySql() {
		String sql="select t.LINE_ID from "+CBSystemConstants.opcardUser+"t_e_substationtopology t where t.equip_id='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpery sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorperySql1() {
		String sql="select t.STATION_ID,t.EQUIP_ID from "+CBSystemConstants.opcardUser+"t_e_substationtopology t where t.LINE_ID='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpery sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorperySql2() {
		String sql="select t.STATION_NAME from "+CBSystemConstants.opcardUser+"t_e_substation t where t.STATION_ID='";
		return sql;
	}
	
	/*
	 * 描述 InOutLineTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String InOutLineTreeWidgetSql2() {
		String sql = "select a.station_name,a.station_id from "+CBSystemConstants.opcardUser+"t_e_substation a where a.station_id in(select t.station_id from "+CBSystemConstants.opcardUser+"t_e_substationtopology t where t.line_id = '";
		return sql;
	}
	
	/*
	 * 描述 DictionaryModelInit sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DictionaryModelInitSql() {
		String sql = "select * from "+CBSystemConstants.opcardUser+"T_E_VOLTAGELEVEL";
		return sql;
	}
	
	/*
	 * 描述 DeviceStateMentManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DeviceStateMentManagerSql() {
		String sql = "SELECT T.VOLTAGE_CODE,T.VOLTAGE_NAME FROM "+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL T ORDER BY T.VOLTAGE_CODE";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql2() {
		String sql = "select t.voltage_code,t.voltage_name from "+CBSystemConstants.opcardUser+"t_e_voltagelevel t order by t.voltage_code";
		return sql;
	}
	
	/*
	 * 描述 TransTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TransTreeWidgetSql() {
		String sql = "select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,d.voltage_code||'kV',c.station_id nodecode,c.station_name nodename from "+CBSystemConstants.opcardUser+"t_e_substation c,"+CBSystemConstants.opcardUser+"t_e_voltagelevel d where c.station_isdel=0 and c.voltage_id=d.voltage_id order by station,d.voltage_value desc,nodename asc";
		return sql;
	}
	
	/*
	 * 描述 TransTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TransTreeWidgetSql1() {
		String sql = "select t.station_id,t.station_name from "+CBSystemConstants.opcardUser+"t_e_substation t,"+CBSystemConstants.opcardUser+"t_e_voltagelevel a where t.voltage_id=a.voltage_id order by a.voltage_code desc,t.station_name";
		return sql;
	}
	
	/*
	 * 描述 GetDIfferStatusDevices sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String GetDIfferStatusDevicesSql1() {
		String sql = "SELECT B.STATION_ID FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO A,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO B WHERE A.EQUIPID=B.EQUIP_ID AND A.DEVICESTATUS='-1' GROUP BY B.STATION_ID";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorJC sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorJCSql() {
		String sql = "SELECT DISTINCT(T2.STATION_ID) AS STATION FROM "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T2 WHERE T.EQUIPID=T2.EQUIP_ID AND T.CARDID='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorJC sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorJCSql1() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"t_s_breaker t,"+CBSystemConstants.opcardUser+"t_e_equipinfo s where t.开关id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorJC sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorJCSql2() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"t_s_DISCONNECTOR t,"+CBSystemConstants.opcardUser+"t_e_equipinfo s where t.刀闸id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorJC sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorJCSql3() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"t_s_GROUNDDISCONNECTOR t,"+CBSystemConstants.opcardUser+"t_e_equipinfo s where t.接地刀闸id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBSql() {
		String sql = "SELECT DISTINCT(T2.STATION_ID) AS STATION FROM "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T2 WHERE T.EQUIPID=T2.EQUIP_ID AND T.CARDID='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBSql1() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"t_s_breaker t,"+CBSystemConstants.opcardUser+"t_e_equipinfo s where t.status=1 and t.开关id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBSql2() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"t_s_DISCONNECTOR t,"+CBSystemConstants.opcardUser+"t_e_equipinfo s where t.status=1 and t.刀闸id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBSql3() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"t_s_GROUNDDISCONNECTOR t,"+CBSystemConstants.opcardUser+"t_e_equipinfo s where t.status=1 and t.接地刀闸id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXBD5000 sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBD5000Sql() {
		String sql = "SELECT DISTINCT(T2.STATION_ID) AS STATION FROM "+CBSystemConstants.opcardUser+"t_a_CZPACTIONSTATE T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T2 WHERE T.EQUIPID=T2.EQUIP_ID AND T.CARDID='";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param  设备名称查询是否存在数据库中存在设备
	 * @param
	 * @param
	 * */
	public String getEquipCheck(String EquipName) {
		String sql= "select t.equip_name from "  
			      + ""+CBSystemConstants.opcardUser+""
			      + "t_e_equipinfo t  Where t.equip_name= '"+EquipName+"'";
		return sql;
	}
	public String getdelDevicedql() {
		 String sql= "delete  "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO t where not exists (select * from equip.v_equipinfo v where v.equip_id=t.equipid)";
		 return sql;
	}
	
}
