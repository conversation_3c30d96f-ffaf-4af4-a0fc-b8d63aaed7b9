package com.tellhow.czp.staticsql;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class OpeInfo extends OPEService{
	/*
	 * 描述：获取执行设备信息
	 * @param
	 * @param
	 * @param
	 * */
	public String getExeEqu(String equip, String starOne,String endTwo) {
		String sql="SELECT RU.EQUIPID,E.STATION_ID,RU.SRCSTATUS,RU.STATECODE FROM "+CBSystemConstants.opcardUser+"T_a_RULEUSERCB_ACTION RU,"
			     + name
			     + "T_EQUIPINFO E WHERE RU.EQUIPID=E.EQUIP_ID AND RU.F_RULEID =(SELECT T.RULEID FROM "+CBSystemConstants.opcardUser+"T_a_RULEUSERZB T WHERE T.EQUIPID='"+equip+"' AND T.SRCSTATUS='"+starOne+"' AND T.STATECODE='"+endTwo+"')";
		return sql;
	}
	
	/*
	 * 描述：获取前置设备信息
	 * @param
	 * @param
	 * @param
	 * */
	public String getPreEqu(String pid, String srcsta, String status) {
		 String sql=" select eq.station_id, con.equipid,con.srcstatus,con.isnegated from "+CBSystemConstants.opcardUser+"T_a_ruleusercb_condition con , "
			 	+ name
			 	+ "T_EQUIPINFO eq  where  eq.equip_id =con.equipid and con.F_RULEID =(select zb.ruleid from "+CBSystemConstants.opcardUser+"T_a_ruleuserzb zb where zb.equipid='"+pid+"' and zb.srcstatus= '"+srcsta+"' and zb.statecode='"+status+"')";
		 return sql;
	}
	
	/*
	 * 描述：获取关联开关信息
	 * @param
	 * @param
	 * @param
	 * */
	public String getLs() {
		 String sql="select t.equip_id,t.equip_name from "
			      + name
			      + "T_EQUIPINFO t," 
			      + name 
			      + "T_EQUIPTYPE s," 
			      + name 
			      +	"T_VOLTAGELEVEL a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype='monthlinelinkswitch' and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='?' order by a.voltage_code desc,t.equip_name";
		 return sql;
	}	
	
	/*
	 * 描述：查询组件事件
	 * @param
	 * @param
	 * @param
	 * */
	public String getQueCompement(String code) {
		 String sql = "and DEVICE_TYPE='1' and SCS_OBJ_CODE in (select a.equip_id from " 
			 	    + name
			 	    + "T_EQUIPINFO a where a.station_id= '"+code+"')";
		 return sql;
	}
	
	/*
	 * 描述：获取备自投参数
	 * @param
	 * @param
	 * @param
	 * */
	public String getBasParam(String code) {
		 String sql = "and DEVICE_TYPE='1' and SCS_OBJ_CODE in (select a.equip_id from " 
			        + name
			        + "T_EQUIPINFO a where a.station_id= '"+code+"')";
		 return sql;
	}
	
	/*
	 * 描述：获取母联开关下拉
	 * @param
	 * @param
	 * @param
	 * */
	public String getCs() {
		 String sql = "select t.equip_id,t.equip_name from " 
			        + name
			        + "T_EQUIPINFO t," 
			        + name
			        + "T_EQUIPTYPE s," 
			        + name
			        + "T_VOLTAGELEVEL a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype='monthlinelinkswitch' and t.equip_id in (select e.scs_obj_code from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE e where e.device_type='1' and e.scs_obj_code is not null) and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='?' order by a.voltage_code desc,t.equip_name";
		 return sql;
	}
	
	/*
	 * 描述：获取备自投跳闸关系参数
	 * @param
	 * @param
	 * @param
	 * */
	public String getTriParam(String code) {
		 String sql = "and SCS_OBJ in (select a.equip_id from "  
			        +name
			        + "T_EQUIPINFO a where a.station_id= '"+code+"')";
		 return sql;
	}
	
	/*
	 * 描述：获取EquipOrganCheckChoose参数
	 * @param
	 * @param
	 * @param
	 * */
	public String getEOCCParam(String organid,PowerDevice pd) {
		
		String equipID = pd.getPowerDeviceID();
		String sql = "";
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			if(CBSystemConstants.getEquiplinemap().containsKey(equipID)) {
				sql= "UPDATE "  
					      + name
					      + "T_C_ACLINEEND t SET t.orga_id='"+organid+"' WHERE t.ID='"+equipID+"'";
			}
		}
		else
			sql= "UPDATE "  
				      + name
				      + "T_EQUIPINFO t SET t.orga_id='"+organid+"' WHERE t.EQUIP_ID='"+equipID+"'";
		 return sql;
	}
	
	/*
	 * 描述：获取EquipPermissionCheckChoose参数
	 * @param
	 * @param
	 * @param
	 * */
	public String getEPCCParam(String organid,PowerDevice pd) {
		String equipID = pd.getPowerDeviceID();
		String sql = "";
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			if(CBSystemConstants.getEquiplinemap().containsKey(equipID)) {
				List<String> list = CBSystemConstants.getEquiplinemap().get(equipID);
				sql= "UPDATE "  
					      + name
					      + "T_C_LINE t SET t.dispatch_permission_id='"+organid+"' WHERE t.LINE_ID='"+list.get(0)+"'";
			}
		}
		else
			sql= "UPDATE "  
				      + name
				      + "T_EQUIPINFO t SET t.dispatch_permission_id='"+organid+"' WHERE t.EQUIP_ID='"+equipID+"'";
		 return sql;
	}
	
	/*
	 * 描述：GetDifferStatusDevices同步DEVICEEQUIPINFO表设备
	 * @param
	 * @param
	 * @param
	 * */
	public String getDsd() {
		 String sql= "INSERT INTO "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO(EQUIPID, DEVICESTATUS, DEVICERUNTYPE, DEVICERUNMODEL, EQUIPNAME, DISPATCH, MONITORING, LOADELECSTATUS, BELONGSWTICHEQUIPID) \n" +
						 "  SELECT distinct * from(SELECT T.EQUIP_ID a, '-1' b, NULL c, NULL d, NULL e, '-1' f, '-1' g, null h,null i\n" + 
						 "    FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO T\n" + 
						 "   WHERE T.EQUIP_ISDEL = 0\n" + 
						 "     and not exists\n" + 
						 "         (SELECT T2.EQUIPID FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T2 where t2.equipid=t.equip_id)\n" + 
						 "  union all\n" + 
						 "  SELECT distinct A.ID, '-1', NULL, NULL, NULL, '-1', '-1', null,null\n" + 
						 "    FROM "+CBSystemConstants.equipUser+"T_C_ACLINEEND A\n" + 
						 "   WHERE not exists (SELECT T2.EQUIPID FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T2 where t2.equipid=a.id)\n" + 
						 "  union all\n" + 
						 "  SELECT b.equip_id, '-1', NULL, NULL, NULL, '-1', '-1', null,null\n" + 
						 "    FROM "+CBSystemConstants.equipUser+"T_PD_EQUIPINFO b\n" + 
						 "   WHERE not exists\n" + 
						 "         (SELECT T2.EQUIPID FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T2 where t2.equipid=b.equip_id))";

		 return sql;
	}
	
	public String getdelDevicedql() {
		 String sql= "delete "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO t where not exists (select * from "+CBSystemConstants.equipUser+"T_equipinfo v where v.equip_id=t.equipid)";
		 return sql;
	}
	
	/*
	 * 描述：GetDifferStatusDevices同步DEVICEEQUIPINFO表设备
	 * @param
	 * @param
	 * @param
	 * */
	public String getDsd2() {
		 String sql= "delete from "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO t where not exists (select a.equip_id from "+CBSystemConstants.equipUser+"t_equipinfo a where a.equip_id=t.equipid)\n" +
						 "and not exists (select b.id from "+CBSystemConstants.equipUser+"t_c_aclineend b where t.equipid = b.id) and not exists (select c.equip_id from "+CBSystemConstants.equipUser+"t_pd_equipinfo c where c.equip_id=t.equipid)";
		 return sql;
	}
	
	/*
	 * 描述 InitDeviceRunType sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getIdrt(String stationID) {
		String sql = "SELECT COUNT(*) FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO A,"  
			       + name
			       + "T_EQUIPINFO B WHERE A.EQUIPID=B.EQUIP_ID AND A.DEVICESTATUS='-1' AND B.STATION_ID='"+stationID+"'";
		return sql;
	}
	
	/*
	 * 描述 InitDeviceRunStatus sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getIdrs(String stationID) {
    	String sql = "SELECT COUNT(*) FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO A,"  
    		       + name
    		       + "T_EQUIPINFO B WHERE A.EQUIPID=B.EQUIP_ID AND A.DEVICESTATUS='-1' AND B.STATION_ID='"+stationID+"'";
		return sql;
	}
	
	/*
	 * 描述 InitDeviceRunStatus sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getIdrs0(String stationID) {
		String sql="SELECT T.EQUIPID ,T.DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"  
			      + name
			      + "T_EQUIPINFO T1 WHERE T.EQUIPID=T1.EQUIP_ID  AND T1.STATION_ID='"+stationID+"'"
			      +" union all select t.equipid,t.devicestatus from "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO t,"
			      + name
			      +"t_c_aclineend t1 where t.equipid=t1.id and t1.st_id='"+stationID+"' \n"+
			      "union SELECT T.EQUIPID ,T.DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"+name+"T_PD_EQUIPINFO P,"+name+"t_c_Aclineend D WHERE T.EQUIPID=P.EQUIP_ID AND P.Line_Id=D.Id  AND d.st_id='"+stationID+"' ";
		return sql;
	}
	
	/*
	 * 描述 InitDeviceRunStatus sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getIdrs1(String stationID) {
		String sql= "SELECT T.EQUIPID ,T.DISPATCH DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"  
			      + name
			      + "T_EQUIPINFO T1 WHERE T.EQUIPID=T1.EQUIP_ID  AND T1.STATION_ID='"+stationID+"'"
			      +"union select t.equipid,t.DISPATCH devicestatus from "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO t,"
			      + name
			      +"t_c_aclineend t1 where t.equipid=t1.id and t1.st_id='"+stationID+"'"; 
		if(CBSystemConstants.roleCode.equals("1")) {
			sql = sql +" union select t.equipid,t.DISPATCH devicestatus from "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO t,"
				      + name
				      +"T_PD_EQUIPINFO t1 where t.equipid=t1.equip_id and instr(t1.line_id,'"+stationID+"')>0";
		}
			     
		return sql;
	}
	
	/*
	 * 描述 InitDeviceRunStatus sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getIdrs2(String stationID) {
		String sql= "SELECT T.EQUIPID ,T.MONITORING DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"  
			      + name
			      + "T_EQUIPINFO T1 WHERE T.EQUIPID=T1.EQUIP_ID  AND T1.STATION_ID='"+stationID+"'"
  			      +"union select t.equipid,t.MONITORING devicestatus from "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO t,"
			      + name
			      +"t_c_aclineend t1 where t.equipid=t1.id and t1.st_id='"+stationID+"'";
		return sql;
	}
	
	/*
	 * 描述 CBSystemConstants sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getPowDevice(String powerEquipID) {
		String sql= "select DEVICESTATUS,VOLTAGE_ID,e.EQUIP_NAME,orga_id,station_id,(select EQUIPTYPE_NAME from "+CBSystemConstants.equipUser+"T_EQUIPTYPE WHERE e.EQUIPTYPE_ID = EQUIPTYPE_ID) EQUIPTYPE_NAME,(select EQUIPTYPE_FLAG from "+CBSystemConstants.equipUser+"T_EQUIPTYPE WHERE e.EQUIPTYPE_ID = EQUIPTYPE_ID) EQUIPTYPE_FLAG from "  
			      + name
			      + "T_EQUIPINFO e,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO s where e.equip_id =s.EQUIPID and e.equip_id='"+powerEquipID+"'  union select DEVICESTATUS,voltage_id,e.name,'',st_id,'线路','ACLineSegment' from "  + name + "t_c_aclineend e,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO s where e.id =s.EQUIPID and e.id='"+powerEquipID+"'";
		
		if(CBSystemConstants.roleCode.equals("1")&&CBSystemConstants.usePwRole)
			sql = sql + " union all select DEVICESTATUS,VOLTAGELVL,e.EQUIP_NAME,RUN_UNIT_NO,LINE_ID,(select EQUIPTYPE_NAME from "+CBSystemConstants.equipUser+"T_EQUIPTYPE WHERE e.EQUIP_TYPE = EQUIPTYPE_ID) EQUIPTYPE_NAME,(select EQUIPTYPE_FLAG from "+CBSystemConstants.equipUser+"T_EQUIPTYPE WHERE e.EQUIP_TYPE = EQUIPTYPE_ID) EQUIPTYPE_FLAG from "  
				      + name
				      + "T_PD_EQUIPINFO e,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO s where e.equip_id =s.EQUIPID and e.equip_id='"+powerEquipID+"'";
		
		return sql;
	}
	
	/*
	 * 描述 CBSystemConstants sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getOrga(String powerDeviceID) {
		String sql= "select * from "  
			      + name
			      + "T_EQUIPINFO e,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO s where e.equip_id =s.EQUIPID and e.equip_id='"+powerDeviceID+"' ";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getStationDeviceList(String powerStationID) {
		String ver_id = null;
		String unitcode = CBSystemConstants.unitCode;//获取unitcode
		String selectSql = "select a.ver_id from "+CBSystemConstants.opcardUser+"T_a_statenormalver a where a.ver_flag='1' and a.opcode='0'";
		try {
			ver_id = DBManager.queryForString(selectSql);//获取到常状态最新版本号
		} catch (Exception e) {
			// TODO: handle exception
		}
		
		String selField = "A.ORGA_ID";
		try {
			DBManager.queryForList("select orga_id from "+CBSystemConstants.equipUser+"T_C_ACLINEEND where rownum=1");
		} catch (Exception e) {
			selField = "D.ORGA_ID";
		}
		
		
		String sql = "select " + 
				"        '0' ispw, " + 
				"        F.*," + 
				"        G.OBJ_STATE  " + 
				"from " + 
				"        ( " + 
				"        SELECT " + 
				"                A.ORGA_ID, " + 
				"                A.SUPERVISIONRIGHT_ID, " + 
				"                A.DISPATCH_PERMISSION_ID, " + 
				"                A.EQUIP_ID, " + 
				"                A.EQUIP_CODE, " + 
				"                NVL(E.EQUIPNAME,A.EQUIP_NAME) EQUIP_NAME, " + 
				"                A.CIM_ID, " + 
				"                B.STATION_ID, " + 
				"                '' ROOM_ID," + 
				"                B.STATION_NAME, " + 
				"                nvl(C.VOLTAGE_CODE,0) VOLTAGE_CODE, " + 
				"                D.EQUIPTYPE_FLAG, " + 
				"                E.DEVICERUNTYPE, " + 
				"                E.DEVICERUNMODEL, " + 
				"                E.DEVICESTATUS, " + 
				"                E.DISPATCH, " + 
				"                E.MONITORING, " + 
				"                E.LOADELECSTATUS " + 
				"        from " + 
				"                (select * from "+CBSystemConstants.equipUser+"T_EQUIPINFO where STATION_ID in ('"+powerStationID.replace(",", "','")+"')) A left join "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL C on A.VOLTAGE_ID  =C.VOLTAGE_ID, " + 
				"                "+CBSystemConstants.equipUser+"T_SUBSTATION B, " + 
				"                "+CBSystemConstants.equipUser+"T_EQUIPTYPE D, " + 
				"                "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO E " + 
				"        where " + 
				"                A.STATION_ID=B.STATION_ID " + 
				"            and A.EQUIP_ISDEL   =0 " + 
				"            and A.EQUIPTYPE_ID    =D.EQUIPTYPE_ID " + 
				"            and A.EQUIP_ID     =E.EQUIPID ";

		sql+=
				"            and B.STATION_ID   in ('"+powerStationID.replace(",", "','")+"') " + 
				"        union all " + 
				"        SELECT " + 
				"                "+selField+" ORGA_ID, " + 
				"                D.SUPERVISIONRIGHT_ID SUPERVISIONRIGHT_ID, " + 
				"                D.DISPATCH_PERMISSION_ID DISPATCH_PERMISSION_ID, " + 
				"                A.ID EQUIP_ID, " + 
				"                A.CODE EQUIP_CODE, " + 
				"                NVL(E.EQUIPNAME,A.NAME) EQUIP_NAME, " + 
				"                A.CIM_ID, " + 
				"                B.STATION_ID, " + 
				"                '' ROOM_ID," + 
				"                B.STATION_NAME, " + 
				"                C.VOLTAGE_CODE, " + 
				"                'ACLineSegment', " + 
				"                E.DEVICERUNTYPE, " + 
				"                E.DEVICERUNMODEL, " + 
				"                E.DEVICESTATUS, " + 
				"                E.DISPATCH, " + 
				"                E.MONITORING, " + 
				"                E.LOADELECSTATUS " + 
				"        from " + 
				"                "+CBSystemConstants.equipUser+"T_C_ACLINEEND A, " + 
				"                "+CBSystemConstants.equipUser+"T_SUBSTATION B, " + 
				"                "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL C, " + 
				"                "+CBSystemConstants.equipUser+"T_C_LINE D, " + 
				"                "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO E " + 
				"        where " + 
				"                A.ST_ID=B.STATION_ID " + 
				"            and A.acline_id      =d.line_id " + 
				"            and A.EQUIP_ISDEL     =0 " + 
				"            and A.VOLTAGE_ID  =C.VOLTAGE_ID " + 
				"            and A.ID          =E.EQUIPID ";

		sql += "            and B.STATION_ID  in ('"+powerStationID.replace(",", "','")+"') " + 
				"        ) " + 
				"        f " + 
				"Left JOIN " + 
				"        ( " + 
				"        select " + 
				"                STATION_ID, " + 
				"                obj_id, " + 
				"                obj_state " + 
				"        from " + 
				"                "+CBSystemConstants.opcardUser+"T_A_STATENORMAL " + 
				"        where " + 
				"                ver_id='"+ver_id+"' " + 
				"            and STATION_ID      in ('"+powerStationID.replace(",", "','")+"') " + 
				"        ) " + 
				"        G " + 
				"On " + 
				"        f.EQUIP_ID = G.obj_id";
		if(CBSystemConstants.roleCode.equals("1")) {
			if(CBSystemConstants.isDevMultiLine)
				sql = sql + " union all SELECT '1' ispw," + 
						"                A.RUN_UNIT_NO ORGA_ID," + 
						"                '' SUPERVISIONRIGHT_ID," + 
						"                '' DISPATCH_PERMISSION_ID," + 
						"                A.EQUIP_ID," + 
						"                A.EQUIP_CODE," + 
						"                NVL(E.EQUIPNAME,A.EQUIP_NAME) EQUIP_NAME," + 
						"                A.CIM_ID," + 
						"                A.LINE_ID ST_ID," + 
						"                ROOM_ID ROOM_ID," + 
						"                '' NAME," + 
						"                C.VOLTAGE_CODE," + 
						"                D.EQUIPTYPE_FLAG," + 
						"                E.DEVICERUNTYPE," + 
						"                E.DEVICERUNMODEL," + 
						"                E.DEVICESTATUS," + 
						"                E.DISPATCH, " + 
						"                E.MONITORING, " + 
						"                E.LOADELECSTATUS,null OBJ_STATE " + 
						"        from " + 
						"                "+CBSystemConstants.equipUser+"T_pd_EQUIPINFO A," + 
						"                "+CBSystemConstants.equipUser+"T_C_ACLINEEND B," + 
						"                "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL C, " + 
						"                "+CBSystemConstants.equipUser+"T_EQUIPTYPE D, " + 
						"                "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO E " + 
						"        where " + 
						"                B.ID='"+powerStationID+"' " + 
						"            and A.EQUIP_ISDEL   =0 " + 
						"            and A.VOLTAGELVL =C.VOLTAGE_ID " + 
						"            and A.EQUIP_TYPE =D.EQUIPTYPE_ID " + 
						"            and A.EQUIP_ID   =E.EQUIPID " + 
						"            and instr(A.LINE_ID,'"+powerStationID+"')>0";
			else
				sql = sql + " union all SELECT '1' ispw," + 
						"                A.RUN_UNIT_NO ORGA_ID," + 
						"                '' SUPERVISIONRIGHT_ID," + 
						"                '' DISPATCH_PERMISSION_ID," + 
						"                A.EQUIP_ID," + 
						"                A.EQUIP_CODE," + 
						"                NVL(E.EQUIPNAME,A.EQUIP_NAME) EQUIP_NAME," + 
						"                A.CIM_ID," + 
						"                A.LINE_ID ST_ID," + 
						"                ROOM_ID ROOM_ID," + 
						"                '' NAME," + 
						"                C.VOLTAGE_CODE," + 
						"                D.EQUIPTYPE_FLAG," + 
						"                E.DEVICERUNTYPE," + 
						"                E.DEVICERUNMODEL," + 
						"                E.DEVICESTATUS," + 
						"                E.DISPATCH, " + 
						"                E.MONITORING, " + 
						"                E.LOADELECSTATUS,null OBJ_STATE " + 
						"        from " + 
						"                "+CBSystemConstants.equipUser+"T_pd_EQUIPINFO A," + 
						"                "+CBSystemConstants.equipUser+"T_C_ACLINEEND B," + 
						"                "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL C, " + 
						"                "+CBSystemConstants.equipUser+"T_EQUIPTYPE D, " + 
						"                "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO E " + 
						"        where " + 
						"                B.ID=A.LINE_ID " + 
						"            and A.EQUIP_ISDEL   =0 " + 
						"            and A.VOLTAGELVL =C.VOLTAGE_ID " + 
						"            and A.EQUIP_TYPE =D.EQUIPTYPE_ID " + 
						"            and A.EQUIP_ID   =E.EQUIPID " + 
						"            and A.LINE_ID in ('"+powerStationID.replace(",", "','")+"')";
		}
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getEquipType() {
		String sql= "SELECT T.EQUIPTYPE_FLAG, T.EQUIPTYPE_NAME FROM "  
			      + name
			      + "T_EQUIPTYPE T WHERE T.EQUIPTYPE_ISDEL=0 AND T.EQUIPTYPE_FLAG IS NOT NULL AND T.CIM_ID IS NOT NULL ORDER BY T.EQUIPTYPE_ORDER";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getStationProtectList(String powerStationID) {
		String sql= "SELECT A.equipid,A.PROTECTID,A.PROTECTNAME,C.DEVICETYPEID,C.PROTECTTYPENAME,A.PROTECTSTATUS FROM "+CBSystemConstants.opcardUser+"T_A_PROTECTEQUIP A,"  
				  + name
				  + "T_EQUIPINFO B,"+CBSystemConstants.opcardUser+"T_A_PROTECTINFO C WHERE A.EQUIPID=B.EQUIP_ID AND A.PROTECTTYPEID=C.PROTECTTYPEID AND B.STATION_ID='"+powerStationID+"'";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getStationToplogy(String powerStationID) {
		String sql = "";
		if(CBSystemConstants.pjzMap.containsKey(powerStationID)){
			 sql = "select A.CONNECTIVITYNODE_ID,B.EQUIP_ID,B.EQUIP_NAME,B.STATION_ID FROM "+CBSystemConstants.equipUser+"T_C_TERMINAL A, "+CBSystemConstants.equipUser+"T_EQUIPINFO B where A.CONNECTIVITYNODE_ID is not null and A.EQUIP_ID=B.EQUIP_ID and B.STATION_ID in ('"+powerStationID.replace(",", "','")+"') AND A.CIM_ID IS NULL"; 
		}else{
			 sql = "select A.CONNECTIVITYNODE_ID,B.EQUIP_ID,B.EQUIP_NAME,B.STATION_ID FROM "+CBSystemConstants.equipUser+"T_C_TERMINAL A, "+CBSystemConstants.equipUser+"T_EQUIPINFO B where A.CONNECTIVITYNODE_ID is not null and A.EQUIP_ID=B.EQUIP_ID and B.STATION_ID in ('"+powerStationID.replace(",", "','")+"')"; 
		}
		
		if(DBManager.queryForInt("select count(0) from "+CBSystemConstants.equipUser+"T_pd_EQUIPINFO") > 0) {
			if(CBSystemConstants.isDevMultiLine)
				sql = sql + " union all select A.CONNECTIVITYNODE_ID,B.EQUIP_ID,B.EQUIP_NAME,B.LINE_ID STATION_ID FROM "+CBSystemConstants.equipUser+"T_C_TERMINAL A, "+CBSystemConstants.equipUser+"T_PD_EQUIPINFO B where A.CONNECTIVITYNODE_ID is not null and A.EQUIP_ID=B.EQUIP_ID and instr(B.LINE_ID,'"+powerStationID+"')>0";
			else
				sql = sql + " union all select A.CONNECTIVITYNODE_ID,B.EQUIP_ID,B.EQUIP_NAME,B.LINE_ID STATION_ID FROM "+CBSystemConstants.equipUser+"T_C_TERMINAL A, "+CBSystemConstants.equipUser+"T_PD_EQUIPINFO B where A.CONNECTIVITYNODE_ID is not null and A.EQUIP_ID=B.EQUIP_ID and B.LINE_ID in ('"+powerStationID.replace(",", "','")+"')";
			
		}
		return sql;
	}
	
	public String getStationToplogyLine(String powerStationID) {
		String sql= "select A.CONNECTIVITYNODE_ID,B.ID EQUIP_ID,B.NAME EQUIP_NAME,B.ST_ID STATION_ID FROM "+CBSystemConstants.equipUser+"T_C_TERMINAL A, "+CBSystemConstants.equipUser+"T_c_Aclineend B where A.EQUIP_ID=B.ID and B.ST_ID in ('"+powerStationID.replace(",", "','")+"')"; 
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getSysTelemetering() {
		String sql= "select a.equipid,a.meastype,a.measid,a.measvalue,a.measname from "+CBSystemConstants.opcardUser+"T_a_telemetering a,"  
			      + name
			      + "T_EQUIPINFO b,"+CBSystemConstants.equipUser+"T_c_aclineend c where a.equipid=b.equip_id and b.equip_id=c.id and c.acline_id is not null";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getFacTelemetering() {
		String sql= "select a.equipid,a.meastype,a.measid,a.measvalue,a.measname from "+CBSystemConstants.opcardUser+"T_a_telemetering a,"  
			      + name
			      + "T_EQUIPINFO b,"  
			      + name
			      + "T_SUBSTATION c where a.equipid=b.equip_id and b.station_id=c.station_id and c.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getDeviceState() {
		String sql= "select a.EQUIP_ID,a.EQUIP_STATE from "+CBSystemConstants.opcardUser+"T_e_equipstate a,"  
			      + name
			      + "T_EQUIPINFO b where a.equip_id=b.equip_id and b.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getActionNote() {
		String sql= "select * from "+CBSystemConstants.opcardUser+"T_g_actionnote a,"  
			      + name
			      + "T_EQUIPINFO b where a.tobjectid=b.equip_id and b.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getActionGroundLine() {
		String sql= "select * from "+CBSystemConstants.opcardUser+"T_g_actiongroundline a,"  
			      + name
			      + "T_EQUIPINFO b where a.tobjectid=b.equip_id and b.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getActionCard() {
		String sql= "select * from "+CBSystemConstants.opcardUser+"T_g_actioncard a,"  
			      + name
			      + "T_EQUIPINFO b where a.tobjectid=b.equip_id and b.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getStationRMDevice() {
		String sql= "select g.* from "+CBSystemConstants.opcardUser+"T_a_removabledevice g,"+CBSystemConstants.equipUser+"T_EQUIPINFO e where e.equip_id=g.equipid and e.station_id=? and g.opcode='"+CBSystemConstants.opCode+"' union all select g.* from "+CBSystemConstants.opcardUser+"T_a_removabledevice g,"+CBSystemConstants.equipUser+"T_C_ACLINEEND e where e.id=g.equipid and e.st_id=? and g.opcode='"+CBSystemConstants.opCode+"'";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getStationBZTDevice(String stationID) {
		
		String ver_id = null;
		String selectSql = "select a.ver_id from "+CBSystemConstants.opcardUser+"T_a_statenormalver a where a.ver_flag='1' and a.opcode='0'";
		try {
			ver_id = DBManager.queryForString(selectSql);//获取到常状态最新版本号
		} catch (Exception e) {
			// TODO: handle exception
		}
		
		String sql= "select a.SCS_SCSNAME,a.SCS_OBJ_CODE,a.SCS_ID,a.DEVICE_TYPE,b.CHANGE_STATE,c.ORGA_ID,v.VOLTAGE_CODE,s.obj_state from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE a left join "+CBSystemConstants.opcardUser+"T_A_STATENORMAL s on a.scs_id=s.obj_id,"+CBSystemConstants.opcardUser+"T_A_ECSAUTRECORD b, "  
			      + name
			      + "T_EQUIPINFO c,"  
			      + name
			      + "T_VOLTAGELEVEL v where s.ver_id='"+ver_id+"' and c.VOLTAGE_ID=v.VOLTAGE_ID and a.device_type='1' and a.scs_id=b.scs_id and a.scs_obj_code=c.equip_id and c.station_id='"+stationID+"' and a.station_id='"+stationID+"'";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getStationRelateDevice() {
		String ver_id = null;
		String selectSql = "select a.ver_id from "+CBSystemConstants.opcardUser+"T_a_statenormalver a where a.ver_flag='1' and a.opcode='0'";
		try {
			ver_id = DBManager.queryForString(selectSql);//获取到常状态最新版本号
		} catch (Exception e) {
			// TODO: handle exception
		}
		
		String sql= "select * from "+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE a left join "+CBSystemConstants.opcardUser+"T_A_STATENORMAL s on a.relation_id=s.obj_id,"+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD b, "  
			      + name
			      + "T_EQUIPINFO c,"  
			      + name
			      + "T_VOLTAGELEVEL v where s.ver_id='"+ver_id+"' and c.VOLTAGE_ID=v.VOLTAGE_ID and t.scs_id=a.scs_id and a.relation_id=b.device_id and a.scs_obj=c.equip_id and c.station_id=? and t.station_id=?";
		return sql;
	}
	
	/*
	 * 描述 WebServiceUtil sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getYXFromDB(String stationID) {
//		String sql= "select t.equip_id,decode(n.value,1,1,2,0) status from "  
//			      + name
//			      + "T_EQUIPINFO t,"+CBSystemConstants.opcardUser+"T_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YXVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=to_char(n.pid) and n.type!=3 union all select t.equip_id,decode(n.value,1,1,2,0) status from "  
//			      + name
//			      + "T_EQUIPINFO t,"+CBSystemConstants.opcardUser+"T_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YXVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR||'1'=t.cim_id and m.aliasName=to_char(n.pid) and n.type!=3 and t.cim_id like '%XC1'";
		String sql= "select t.equip_id,decode(n.value,1,1,2,0) status from "  
			      + name
			      + "T_EQUIPINFO t,"+CBSystemConstants.opcardUser+"T_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YXVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=to_char(n.pid) and n.type!=3";
		return sql;
	}
	
	/*
	 * 描述 WebServiceUtil sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getYCFromDB(String stationID) {
		String sql= "select to_char(n.pid) pid,round(n.value,1) value from "  
			      + name
			      + "T_EQUIPINFO t,"+CBSystemConstants.opcardUser+"T_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YCVALUE n where t.station_id='"+stationID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=n.pid";
		return sql;
	}
	
	/*
	 * 描述 WebServiceUtil sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getXLYGFromDB(String equipID) {
		String sql= "select value from "  
			      + name
			      + "T_EQUIPINFO t,"+CBSystemConstants.opcardUser+"T_m_Measurement m,"+CBSystemConstants.opcardUser+"T_S_YCVALUE n where m.measurementtype='MeasType-12' and t.equip_id='"+equipID+"' and m.MemberOf_PSR=t.cim_id and m.aliasName=n.pid";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getPowersLineByLine(PowerDevice line) {
		String sql= "";
		List list = CBSystemConstants.getEquiplinemap().get(line.getPowerDeviceID());
		if(list!= null){
			String lineID = list.size()>0?list.get(0).toString():"";
			sql= "SELECT " + 
					"        T.ID EQUIP_ID," + 
					"        T.NAME EQUIP_NAME," + 
					"        T2.VOLTAGE_CODE," + 
					"        'ACLineSegment' EQUIPTYPE_FLAG," + 
					"        T.ST_ID STATION_ID," + 
					"        T3.STATION_NAME," + 
					"        T.CIM_ID," + 
					"        T4.DEVICESTATUS," + 
					"        T4.DEVICERUNTYPE," + 
					"        T4.DEVICERUNMODEL," + 
					"        T8.FLAG," + 
					"        T.CIM_ID " + 
					"FROM " + 
					"        "+CBSystemConstants.equipUser+"T_C_ACLINEEND T " + 
					"left join " + 
					"        "+CBSystemConstants.opcardUser+"T_A_LINEWAY T8 " + 
					"on " + 
					"        t.id=t8.lineequipid," + 
					"        "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL T2," + 
					"        "+CBSystemConstants.equipUser+"T_SUBSTATION T3," + 
					"        "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T4 " + 
					" WHERE " + 
					"        T.VOLTAGE_ID=T2.VOLTAGE_ID " + 
					"    AND T.ST_ID     =T3.STATION_ID " + 
					"    AND T.ID        =T4.EQUIPID " + 
					"    AND T.ACLINE_ID='"+lineID+"' AND T3.STATION_NAME NOT LIKE '%虚%' AND T3.STATION_NAME NOT LIKE '%PAST%'";
		}
		
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getLineTidy(String line_CIM_ID) {
		String sql= "SELECT T1.STATION_ID,T.FLAG FROM "+CBSystemConstants.opcardUser+"T_a_LINEWAY T,"  
			      + name
			      + "T_EQUIPINFO T1 WHERE\n" 
			      + "T.LINEEQUIPID=T1.EQUIP_ID AND T1.CIM_ID='"+line_CIM_ID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getStationID_(String EquipID) {
		String sql= "select t.station_id from "  
			      + name
			      + "T_EQUIPINFO t  Where t.equip_id='"+EquipID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getEquipName(String EquipID) {
		String sql= "select t.equip_name from "  
			      + name
			      + "T_EQUIPINFO t  Where t.equip_id='"+EquipID+"'";
		return sql;
	}
	
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param  设备名称查询是否存在数据库中存在设备
	 * @param
	 * @param
	 * */
	public String getEquipCheck(String EquipName) {
		String sql= "select t.equip_name from "  
			      + ""+CBSystemConstants.opcardUser+""
			      + "t_e_equipinfo t  Where t.equip_name = '"+EquipName+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getEquipID(String StationID,String EquipName) {
		String sql= "select t.equip_id from "  
			      + name 
			      + "T_EQUIPINFO t Where t.station_id ='"+StationID+"' and t.equip_name like '%"+EquipName+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getrevdiv(String StationID,String EquipName) {
		String sql= "select t.equip_id from "  
			      + name
			      + "T_EQUIPINFO t Where t.station_id ='"+StationID+"' and replace(t.equip_name,'-','') like '%"+EquipName+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getGKdiv(String StationID,String EquipName) {
		String tempname=EquipName.substring(0, 4);
		String sql= "select t.equip_id from "  
			      + name
			      + "T_EQUIPINFO t,"  
			      + name
			      + "T_EQUIPTYPE a Where t.equiptype_id=a.equiptype_id and t.station_id ='"+StationID+"' and a.equiptype_flag='GroundDisconnector' and t.equip_name like '%"+tempname+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getdiv(String StationID,String EquipName) {
		String sql= "select t.equip_id from "  
			      + name
			      + "T_EQUIPINFO t Where t.station_id ='"+StationID+"' and t.equip_name='"+EquipName+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getswitchdiv(String StationID, String EquipName) {
		String sql= "select t.equip_id from "  
			      + name
			      + "T_EQUIPINFO t Where t.station_id ='"+StationID+"' and t.equip_name='"+EquipName+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getEquipID(String stationID, String equipNum, String equipType) {
		String sql= "select t.equip_id from "  
			      + name
			      + "T_EQUIPINFO t,"  
			      + name
			      + "T_EQUIPTYPE a Where t.equiptype_id=a.equiptype_id and t.station_id ='"+stationID+"' and a.equiptype_flag='"+equipType+"' and t.equip_name like '%"+equipNum+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getEquipIDByNameType(String stationID, String equipNum, String equipType) {
		String sql= "select t.equip_id,t.equip_name from "  
			      + name
			      + "T_EQUIPINFO t,"  
			      + name
			      + "T_EQUIPTYPE a Where t.equiptype_id=a.equiptype_id and a.equiptype_flag='"+equipType+"' and t.station_id ='"+stationID+"' and  t.equip_name like '%"+equipNum+"%'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getDevice(String devCode, String StationName,String devType) {
		
		String tj="";
		if(devType.equals(SystemConstants.InOutLine)){
			tj="t.equip_name like '%"+devCode.substring(0,1)+"%' and t.equip_name like '%"+devCode.substring(1,2)+"%' ";
			if(devCode.substring(2,3).equals("Ⅰ"))
				tj=tj+" and (t.equip_name like '%Ⅰ%' or t.equip_name like '%I%') and  t.equip_name not like '%II%'" ;
			if(devCode.substring(2,3).equals("Ⅱ"))
				tj=tj+" and (t.equip_name like '%Ⅱ%' or t.equip_name like '%II%')" ;
			
		}else{
			if(devCode.indexOf("、")>0)
			    tj="t.equip_name like '%"+devCode.split("、")[1]+"%'";
			else 
				tj="t.equip_name like '%"+devCode+"%'";
		}
		
		String sql= "select t.equip_id,t.station_id from "  
			      + name
			      + "T_EQUIPINFO t,"  
			      + name
			      + "T_SUBSTATION s,"  
			      + name
			      + "T_EQUIPTYPE t2\n" 
			      + " Where t.station_id=s.station_id and t.equiptype_id=t2.equiptype_id and " +
      tj+" and s.station_name like '%"+StationName+"%' and t2.equiptype_flag='"+devType+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String updateDevVoltage(PowerDevice pd,CodeNameModel codeNameModel) {		
		String equipID = pd.getPowerDeviceID();
		String VOLTAGE_CODE=codeNameModel.getCode();
		String sql = null;
		//查找要更新的电压等级ID
		String sql2= "select t.VOLTAGE_ID from "  
			       + name
			       + "T_VOLTAGELEVEL t where t.VOLTAGE_CODE='"+VOLTAGE_CODE+"'";
		List voltagelist2=DBManager.queryForList(sql2);
		if(voltagelist2.size()>0){
			Map map2=(Map)voltagelist2.get(0);
			String VOLTAGE_ID2=(String) map2.get("VOLTAGE_ID");
			//更新电压等级ID
			sql= "update "  
			   + name
			   + "T_EQUIPINFO t SET t.VOLTAGE_ID='"+VOLTAGE_ID2+"' WHERE t.EQUIP_ID='"+equipID+"'";
		}
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String updateDevtypemethods(PowerDevice pd,CodeNameModel codeNameModel) {		
		String code = codeNameModel.getCode();
		String equipID = pd.getPowerDeviceID();
		String sql= "UPDATE "  
			      + name
			      + "T_EQUIPINFO t SET t.EQUIPTYPE_ID='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String updateAdjustablePipeOrgan(PowerDevice pd,CodeNameModel codeNameModel) {		
		String code = codeNameModel.getCode();
		String equipID = pd.getPowerDeviceID();
		String sql = "";
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			if(CBSystemConstants.getEquiplinemap().containsKey(equipID)) {
				sql= "UPDATE "  
					      + name
					      + "T_C_ACLINEEND t SET t.ORGA_ID='"+code+"' WHERE t.ID='"+equipID+"'";
			}
		}
		else
			sql= "UPDATE "  
				      + name
				      + "T_EQUIPINFO t SET t.ORGA_ID='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String updatePermitOrgan(PowerDevice pd,CodeNameModel codeNameModel) {		
		String code = codeNameModel.getCode();
		String equipID = pd.getPowerDeviceID();
		String sql = "";
		//修改调管机构中的机构ID
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			if(CBSystemConstants.getEquiplinemap().containsKey(equipID)) {
				List<String> list = CBSystemConstants.getEquiplinemap().get(equipID);
				sql= "UPDATE "  
					      + name
					      + "T_C_LINE t SET t.dispatch_permission_id='"+code+"' WHERE t.LINE_ID='"+list.get(0)+"'";
			}
		}
		else
			sql= "UPDATE "  
				      + name
				      + "T_EQUIPINFO t SET t.dispatch_permission_id='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String updateDevice1(String powerStationID) {		
        String sql = "select t1.scs_id, t1.station_id,t2.change_state from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t1, "+CBSystemConstants.opcardUser+"T_A_ECSAUTRECORD t2,"  
        	       + name
        	       + "T_EQUIPINFO t3 where t1.scs_id = t2.scs_id and t1.scs_obj_code=t3.equip_id and t3.station_id = '" + powerStationID + "'";//查询当前厂站的安置设备
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public String updateDevice2(String powerStationID) {		
		String sql = "select t2.relation_id,t3.change_state from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t1, "+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE t2,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD t3,"  
			       + name
			       + "T_EQUIPINFO t4 where t1.scs_id = t2.scs_id and t1.scs_obj_code=t4.equip_id and t2.relation_id = t3.device_id and t4.station_id = '" + powerStationID + "'";
		return sql;
	}
	
	/*
	 * 描述 AdjustablePipeOrgan sql
	 * @param
	 * @param
	 * @param
	 * */
	public String checkValue() {		
		String sql = "select t.ORGA_ID from "  
			       + name
			       + "T_EQUIPINFO t where t.EQUIP_ID='";
		return sql;
	}
	
	/*
	 * 描述 AdjustablePipeOrgan sql
	 * @param
	 * @param
	 * @param
	 * */
	public String DriverProperty2(PowerDevice pd) {		
		String sql = "";
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			sql = "select t.orga_id from "+CBSystemConstants.equipUser+"T_C_LINE t where t.line_id in (select a.acline_id from "+CBSystemConstants.equipUser+"T_C_ACLINEEND a where a.id='"+pd.getPowerDeviceID()+"')";
		}
		else
			sql = "select t.ORGA_ID from "  
				       + name
				       + "T_EQUIPINFO t where t.EQUIP_ID='"+pd.getPowerDeviceID()+"'";
		return sql;
	}
	
	/*
	 * 描述 AdjustablePipeOrgan sql
	 * @param
	 * @param
	 * @param
	 * */
	public String DriverProperty3(PowerDevice pd) {		
		String sql = "";
		if(pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			sql = "select t.DISPATCH_PERMISSION_ID from "+CBSystemConstants.equipUser+"T_C_LINE t where t.line_id in (select a.acline_id from "+CBSystemConstants.equipUser+"T_C_ACLINEEND a where a.id='"+pd.getPowerDeviceID()+"')";
		}
		else
			sql = "select t.DISPATCH_PERMISSION_ID from "  
				       + name
				       + "T_EQUIPINFO t where t.EQUIP_ID='"+pd.getPowerDeviceID()+"'";
		return sql;
	}
	
	/*
	 * 描述 PermitOrgan sql
	 * @param
	 * @param
	 * @param
	 * */
	public String PermitOrgan2() {		
		String sql = "select t.DISPATCH_PERMISSION_ID from "  
			       + name
			       + "T_EQUIPINFO t where t.EQUIP_ID='";
		return sql;
	}
	
	/*
	 * 描述 TwodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public String TwodeviceDialog1() {		
		String sql = "select t.equip_id,t.equip_name from "  
			       + name
			       + "T_EQUIPINFO t,"  
			       + name
			       + "T_EQUIPTYPE s,"  
			       + name
			       + "T_VOLTAGELEVEL a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype in('lineswitch') and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='";
		return sql;
	}
	
	/*
	 * 描述 TwodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public String TwodeviceDialog2() {		
		String sql = "select t.equip_id,t.equip_name from "  
			       + name
			       + "T_EQUIPINFO t,"  
			       + name
			       + "T_EQUIPTYPE s,"  
			       + name
			       + "T_VOLTAGELEVEL a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype in('lowtransswitch','monthlinelinkswitch','jiedibianswitch') and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='";
		return sql;
	}
	
	/*
	 * 描述 TwodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public String TwodeviceDialog3() {		
		String sql = "select t.equip_name from "  
			       + name
			       + "T_EQUIPINFO t,"  
			       + name
			       + "T_EQUIPTYPE s,"  
			       + name
			       + "T_VOLTAGELEVEL a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype in('lineswitch') and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='";
		return sql;
	}
	
	/*
	 * 描述 TwodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public String TwodeviceDialog4() {		
		String sql = "select t.equip_name from "  
			       + name
			       + "T_EQUIPINFO t,"  
			       + name 
			       + "T_EQUIPTYPE s,"  
			       + name
			       + "T_VOLTAGELEVEL a,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO b where t.equip_id=b.equipid and b.deviceruntype in('lowtransswitch','monthlinelinkswitch') and t.voltage_id=a.voltage_id and s.equiptype_id=t.equiptype_id and s.equiptype_flag='Breaker' and t.station_id='";
		return sql;
	}
	
	/*
	 * 描述 DeviceStatusManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public String DeviceStatusManager(String zbid) {		
		String sql = "select t.equipid,s.station_id,t.beginstatus,t.endstate from "+CBSystemConstants.opcardUser+"T_a_czpactionstate t,"  
			       + name
			       + "T_EQUIPINFO s where t.equipid=s.equip_id and t.cardid='"+zbid+"' and t.beginstatus in ('4','5') order by t.stateorder";
		return sql;
	}
	
	/*
	 * 描述 TicketDBManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public String TicketDBManager1() {		
		String sql = "t_a_CZPZB T ";
		sql += " WHERE  ";
		return sql;
	}
	
	/*
	 * 描述 TicketDBManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public String TicketDBManager2() {		
		String sql = "t_a_CZPZB T left join "  
			       + name
			       + "T_EQUIPINFO a on t.equipid=a.equip_id left join "  
			       + name
			       + "T_EQUIPTYPE b on a.equiptype_id=b.equiptype_id WHERE  ";
		return sql;
	}
	
	/*
	 * 描述 TicketDBManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public String TicketDBManagerjk() {		
		String sql = "t_a_CZPZB T "
				+ "left join "+CBSystemConstants.opcardUser+"T_a_Czpmx F  on T.ZBID = F.F_ZBID "
				+ "left join (select a.equip_id,s.equiptype_flag from "+CBSystemConstants.equipUser+"T_EQUIPINFO a,"+CBSystemConstants.equipUser+"T_EQUIPTYPE s "
				+ "where a.equiptype_id=s.equiptype_id union all select c.id,'ACLineSegment' from "+CBSystemConstants.equipUser+"T_C_ACLINEEND c) b "
				+ "on t.equipid=b.equip_id WHERE  ";
		return sql;
	}
	
	/*
	 * 描述 MonitorTicketTypePanel sql
	 * @param
	 * @param
	 * @param
	 * */
	public String MonitorTicketTypePanel(String zbid) {		
		String sql = "select b.station_name,a.equip_name,c.equiptype_name,t.beginstatus,t.endstate from "+CBSystemConstants.opcardUser+"T_a_czpactionstate t,"  
			       + name
			       + "T_EQUIPINFO a,"  
			       + name
			       + "T_SUBSTATION b,"  
			       + name
			       + "T_EQUIPTYPE c where t.equipid=a.equip_id and a.station_id=b.station_id and a.equiptype_id=c.equiptype_id and t.cardid='"+zbid+"' order by t.stateorder";
		return sql;
	}
	
	/*
	 * 描述 OperateTicketConvertPanel sql
	 * @param
	 * @param
	 * @param
	 * */
	public String OperateTicketConvertPanel(String zbid) {		
		String sql = "select b.station_name,a.equip_name,c.equiptype_name,t.beginstatus,t.endstate from "+CBSystemConstants.opcardUser+"T_a_czpactionstate t,"  
			       + name
			       + "T_EQUIPINFO a,"  
			       + name
			       + "T_SUBSTATION b,"  
			       + name
			       + "T_EQUIPTYPE c where t.equipid=a.equip_id and a.station_id=b.station_id and a.equiptype_id=c.equiptype_id and t.cardid='"+zbid+"' order by t.stateorder";
		return sql;
	}
	
	/*
	 * 描述 TermInversion sql
	 * @param
	 * @param
	 * @param
	 * */
	public String TermInversion() {		
		String sql = "select t6.equipid,t6.beginstatus,t6.endstate,t7.station_id,t8.station_name from "+CBSystemConstants.opcardUser+"T_a_czpactionstate t6 ,"  
			       + name
			       + "T_EQUIPINFO t7 ,"  
			       +name+ "T_SUBSTATION t8"
		+ " where t6.equipid=t7.equip_id and t7.station_id=t8.station_id  order by t6.stateid asc";
		return sql;
	}
	
	/*
	 * 描述 RuleCustomDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public String RuleCustomDialog() {		
		String sql = "select equiptype_flag ,equiptype_name from "  
			       + name 
			       + "T_EQUIPTYPE t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 DictionarysModelInit sql
	 * @param
	 * @param
	 * @param
	 * */
	public String DictionarysModelInit() {		
		String sql = "SELECT T.STATE_CODE,T.STATE_NAME,T.SWITCHSTATE_CODE,T.SWITCHSTATE_NAME,T.EQUIPTYPE_FLAG FROM "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE T where T.EQUIPTYPE_FLAG is not null";
		return sql;
	}
	
	/*
	 * 描述 DeviceStateMentManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getDeviceTypeSql() {		
		String sql=" SELECT T.EQUIPTYPE_NAME,T.EQUIPTYPE_FLAG FROM "  
			      + name
			      + "T_EQUIPTYPE T WHERE T.EQUIPTYPE_FLAG IN"+
				   " (SELECT T.DEVICETYPEID FROM "+CBSystemConstants.opcardUser+"T_a_devicestateinfo T GROUP BY T.DEVICETYPEID)  ORDER BY T.EQUIPTYPE_CODE";
		return sql;
	}
	
	/*
	 * 描述 DeviceStateMentManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getDeviceStatusSql(String deviceType) {		
        String sql= "SELECT NVL(T.SWITCHSTATE_CODE,T.STATE_CODE) STATECODE,NVL(T.STATE_NAME,T.SWITCHSTATE_NAME) STATENAME FROM "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE T,"  
        	      + name
        	      + "T_EQUIPTYPE S WHERE T.EQUIPTYPE_FLAG=S.EQUIPTYPE_FLAG AND S.EQUIPTYPE_FLAG='"+deviceType+"' ORDER BY T.STATE_CODE";
		return sql;
	}
	
	/*
	 * 描述 CardDescDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	public String getcombobox1Sql() {		
		String sql= "select equiptype_flag ,equiptype_name from "  
			      + name
			      + "T_EQUIPTYPE t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public String importOMSSql(String zbid) {		
		String sql= "select t.*,t2.*,t4.equiptype_name from "+CBSystemConstants.opcardUser+"T_a_czpzb t join "+CBSystemConstants.opcardUser+"T_a_poweruserinfo t2 on t.npr=t2.userid left join "  
			      + name
			      + "T_EQUIPINFO t3 on t.equipid=t3.equip_id left join "  
			      + name
			      + "T_EQUIPTYPE t4 on t3.equiptype_id=t4.equiptype_id  where t.zbid='"+ zbid + "'";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXBD5000 sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String importOMSSql1(String zbid) {		
		String sql= "select t.*,t2.*,t4.equiptype_name from "+CBSystemConstants.opcardUser+"T_a_czpzb t join "+CBSystemConstants.opcardUser+"T_a_poweruserinfo t2 on t.npr=t2.userid left join "  
			      + name
			      + "T_EQUIPINFO t3 on t.equipid=t3.equip_id left join "  
			      + name
			      + "T_EQUIPTYPE t4 on t3.equiptype_id=t4.equiptype_id  where t.zbid='"+ zbid + "'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String getPowersLineBySysLineSql(PowerDevice line) {
		String lineID=line.getPowerDeviceID();
		String sql="SELECT T.ID EQUIP_ID,T.NAME EQUIP_NAME,T2.VOLTAGE_CODE,'ACLineSegment' EQUIPTYPE_FLAG,T.ST_ID STATION_ID,T3.STATION_NAME,T.CIM_ID,T4.DEVICESTATUS,T4.DEVICERUNTYPE,T4.DEVICERUNMODEL,'-1',T.CIM_ID FROM "+CBSystemConstants.equipUser+"T_c_aclineend T,"+CBSystemConstants.equipUser+"T_VOLTAGELEVEL T2, "+CBSystemConstants.equipUser+"T_SUBSTATION T3, "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T4 WHERE T.VOLTAGE_ID = T2.VOLTAGE_ID AND T.ST_ID = T3.STATION_ID AND T.ID = T4.EQUIPID AND T.acline_id = '"+lineID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String getProtectByTypeSql(PowerDevice pd,String protectType) {
		String sql= "select p.protecttypeid,p.protecttypename,p.runtype from "+CBSystemConstants.opcardUser+"T_a_protectinfo p ,"  
			      + name
			      + "T_EQUIPTYPE t  where protectkind='"+protectType+"' and t.equiptype_id=p.equiptypeid and t.cim_id ='"+pd.getDeviceType()+"'";
		return sql;
	}
	
	/*
	 * 描述 DriverPopertyDao sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String updateDevtypemethods(CodeNameModel codeNameModel) {
		String code = codeNameModel.getCode();
		String sql="select t.equiptype_flag from "  
			      + name 
			      + "T_EQUIPTYPE t where t.equiptype_id='"+code+"'";
		return sql;
	}
	
	/*
	 * 描述 GetDeviceMenuModel sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String executeSql() {
		String sql= "select equiptype_code from "  
			      + name
			      + "T_EQUIPTYPE where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 Devicetypemethods sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String DevicetypemethodsSql() {
		String sql= "select t.equiptype_id,t.equiptype_name,t.equiptype_flag  from "  
			      + name
			      + "T_EQUIPTYPE t where t.EQUIPTYPE_FLAG is not null and t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String DriverPorpetySql() {
		String sql= "select t.equiptype_name from "  
			      + name
			      + "T_EQUIPTYPE t where t.equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String DriverPorpetySql1() {
		String sql= "select equiptype_code from "  
			      + name
			      + "T_EQUIPTYPE where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String DriverPorpetySql2() {
		String sql="select b.state_name, b.state_code from "+CBSystemConstants.opcardUser+"T_a_protectequip a,"+CBSystemConstants.opcardUser+"T_e_equiptypestate b where a.protectid='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String DriverPorpetySql3() {
		String sql= "select equiptype_code from "  
			      + name
			      + "T_EQUIPTYPE where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpety sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String DriverPorpetySql4() {
		String sql="select b.state_name ,b.state_code from "+CBSystemConstants.opcardUser+"T_e_equiptypestate b where  b.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql() {
		String sql= "select equiptype_id from "  
			      + name
			      + "T_EQUIPTYPE where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql1() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"T_e_equiptypestate where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql2() {
		String sql= "select equiptype_id from "  
			      + name
			      + "T_EQUIPTYPE where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql3() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"T_e_equiptypestate where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql4() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"T_e_equiptypestate where equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql5() {
		String sql= "select equiptype_id from "  
			      + name
			      + "T_EQUIPTYPE where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql6() {
		String sql= "select equiptype_id from "  
			      + name 
			      + "T_EQUIPTYPE where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql7() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"T_e_equiptypestate where equiptype_flag='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql8() {
		String sql= "select equiptype_name from "  
			      + name
			      + "T_EQUIPTYPE where equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql9() {
		String sql= "select equiptype_id,equiptype_name,equiptype_flag from "  
			      + name
			      + "T_EQUIPTYPE where equiptype_flag='Reclosing'";
		return sql;
	}
	
	/*
	 * 描述 EquipManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipManagerDialogSql10() {
		String sql="select state_code, state_name from "+CBSystemConstants.opcardUser+"T_e_equiptypestate where equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 EquipOperationManagerDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipOperationManagerDialogSql() {
		String sql= "select equiptype_flag ,equiptype_name from "  
			      + name 
			      + "T_EQUIPTYPE t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 EquipOperationManagerDialogtype1 sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipOperationManagerDialogSqltype1() {
		String sql= "select code ,name from "  
			      + ""+CBSystemConstants.opcardUser+"T_A_DICTIONARY t where t.unitcode='system' and codetype='cardbuildtype'";
		return sql;
	}
	
	/*
	 * 描述 EquipOperationManagerDialogtype2 sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipOperationManagerDialogSqltype2() {
		String sql= "select '0' as code,'智能票' as name from dual union select '1','点图票' from dual union select '2','设备对位' from dual union select '9','基本' from dual";
		return sql;
	}
	
	/*
	 * 描述 EquipStateDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipStateDialogSql() {
		String sql= "select equiptype_id,equiptype_parent_id,equiptype_code,equiptype_name from "  
			      + name 
			      + "T_EQUIPTYPE t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 EquipStateDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipStateDialogSql1() {
		String sql= "select t.* from "+CBSystemConstants.opcardUser+"T_e_equiptypestate t where t.equiptype_id=( select EQUIPTYPE_ID from "  
			      + name 
			      + "T_EQUIPTYPE   where CIM_ID is not null and  EQUIPTYPE_NAME='";
		return sql;
	}
	
	/*
	 * 描述 EquipTypeDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String EquipTypeDialogSql() {
		String sql= "select equiptype_id,equiptype_parent_id,equiptype_code,equiptype_name from "  
			      + name
			      + "T_EQUIPTYPE t where t.cim_id is not null";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String SystableDialogSql() {
		String sql= "select t.equiptype_id,t.equiptype_name from "  
			      + name 
			      + "T_EQUIPTYPE t where t.equiptype_flag in (select devicetypeid from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO ) order by t.equiptype_order";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String SystableDialogSql1() {
		String sql= "select t.equiptype_id,t.equiptype_name from "  
			      + name
			      + "T_EQUIPTYPE t where t.equiptype_flag in (select devicetypeid from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO ) order by t.equiptype_order";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String SystableDialogSql2() {
		String sql="select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String SystableDialogSql3() {
		String sql="select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String SystableDialogSql4() {
		String sql="select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 SystableDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String SystableDialogSql5() {
		String sql="select t.state_code,t.state_name from "+CBSystemConstants.opcardUser+"T_E_EQUIPTYPESTATE t where t.equiptype_id='";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String PowerSystemDBOperatorSql() {
		String sql= "SELECT " + 
				"        A.LINE_ID," + 
				"        ''," + 
				"        A.LINE_NAME," + 
				"        A.CIM_ID," + 
				"        B.VOLTAGE_CODE," + 
				"        c.status " + 
				"FROM " + 
				"        "+CBSystemConstants.equipUser+"T_C_LINE A " + 
				"join " + 
				"        "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL B " + 
				"on " + 
				"        A.VOLTAGE_ID=B.VOLTAGE_ID " + 
				"left join " + 
				"(select c.acline_id,decode(max(d.devicestatus),'3','3',min(d.devicestatus)) status from "+CBSystemConstants.equipUser+"T_C_ACLINEEND c,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO d where c.id=d.equipid group by c.acline_id) c" + 
				" on a.line_id=c.acline_id where  A.EQUIP_ISDEL=0 ";
		return sql;
	}
	
	
	public  String PowerFeederOperatorSql() {
		String sql= "SELECT A.id LINE_ID, A.st_id, '',  A.name LINE_NAME, A.CIM_ID, B.VOLTAGE_CODE, c.devicestatus,D.orga_id FROM  "+CBSystemConstants.equipUser+"T_C_ACLINEEND A join "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL B on A.VOLTAGE_ID=B.VOLTAGE_ID left join "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO c on  a.id=c.equipid join "+CBSystemConstants.equipUser+"T_C_LINE D on A.ID=D.LINE_ID  where  A.EQUIP_ISDEL=0";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String QueryDeviceDaoSql() {
		String sql= "select t.line_id from  "  
			      + name
			      + "T_C_LINE t where t.cim_id=?";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String QueryDeviceDaoSql1() {
		String sql= "select t.line_id from "  
			      + name 
			      + "T_C_LINE t where t.line_name like '%";
		return sql;
	}
	
	/*
	 * 描述 InOutLineTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String InOutLineTreeWidgetSql() {
		String sql = "select '线路' as line, d.voltage_code||'kV',"
			       + "c.line_id nodecode,c.line_name nodename from "  
			       + name
			       + "T_C_LINE c,"  
			       + name 
			       + "T_VOLTAGELEVEL d where c.voltage_id = d.voltage_id and d.voltage_value>=35 order by line, d.voltage_value desc, nodename asc";
		return sql;
	}
	
	/*
	 * 描述 InOutLineTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String InOutLineTreeWidgetSql1() {
		String sql = "select t.LINE_ID,t.LINE_NAME from "  
			       + name
			       + "T_C_LINE t,"  
			       + name
			       + "T_VOLTAGELEVEL a where t.voltage_id=a.voltage_id and a.voltage_code in ('500','220','110') and t.LINE_NAME not like '%T接%' and t.LINE_NAME not like '%系统%' order by a.voltage_code desc,t.LINE_NAME";
		return sql;
	}
	
	/*
	 * 描述 ElecIslandAlgorithm sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String ElecIslandAlgorithmSql() {
		String sql = "select t.acline_id line_id,t.st_id station_id from "+CBSystemConstants.equipUser+"T_c_aclineend t where t.acline_id is not null";
		return sql;
	}
	
	/*
	 * 描述 SautodeviceDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String SautodeviceDialogSql() {
		String sql = "select t.station_id,t.station_name from "  
			       + name
			       + "T_SUBSTATION t,"  
			       + name
			       + "T_VOLTAGELEVEL a where t.voltage_id=a.voltage_id and a.voltage_code in ('220','110') and t.station_name not like '%T接%' and t.station_name not like '%系统%' order by a.voltage_code desc,t.station_name";
		return sql;
	}
	
	/*
	 * 描述 TriprelateDialog sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String TriprelateDialogSql() {
		String sql = "select t.station_id,t.station_name from "  
			       + name
			       + "T_SUBSTATION t,"  
			       + name
			       + "T_VOLTAGELEVEL a where t.voltage_id=a.voltage_id and a.voltage_code in ('220','110') and t.station_name not like '%T接%' and t.station_name not like '%系统%' order by a.voltage_code desc,t.station_name";
		return sql;
	}
	
	/*
	 * 描述 GetDifferStatusDevices sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String GetDifferStatusDevicesSql() {
		String sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO SELECT E.ID, '0' FROM "+CBSystemConstants.equipUser+"T_c_aclineend E WHERE not exists (SELECT 1 FROM "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO T where T.EQUIP_ID=E.ID)";
		return sql;
	}
	
	/*
	 * 描述 StationDeviceToplogy sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String StationDeviceToplogySql() {
		String sql = "SELECT T.* FROM "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO T,"+CBSystemConstants.equipUser+"T_c_aclineend S WHERE T.EQUIP_ID=S.ID AND S.acline_id is not null AND S.STATION_ID=?";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	
	public  String PowerSystemDBOperatorSql1() {
		String sql = "SELECT STATION_NAME FROM "  
			       + name
			       + "T_SUBSTATION  WHERE STATION_ID=?";
		return sql;
	}
	
	public  String OrganListSql() {
		String sql = "";
		sql="select * from (select t.organid,t.organname,nvl(t.organshortname,t.organname) shortname,seq,cim_id from "+CBSystemConstants.platformUser+"T_tbp_organ t order by organname) union all select t.organid,t.organname,t.organname,seq,cim_id from "+CBSystemConstants.opcardUser+"T_a_powerorgan t";
		
		return sql;
				
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String PowerSystemDBOperatorSql2() {
		String sql = "SELECT A.STATION_ID,A.ORGA_ID,A.STATION_NAME,A.STATION_FLAG,A.CIM_ID,B.VOLTAGE_CODE,B.VOLTAGE_VALUE,A.STATION_GRAPH FROM "+CBSystemConstants.equipUser+"T_SUBSTATION A join "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL B on A.VOLTAGE_ID=B.VOLTAGE_ID  and B.VOLTAGE_CODE NOT IN ('中性点')  WHERE A.STATION_ISDEL=0";
		return sql;
	}
	
	/*
	 * 描述 PowerSystemDBOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String PowerSystemDBOperatorSql3() {
		String sql = "SELECT T.ACLINE_ID LINE_ID,T.ST_ID STATION_ID,T.ID EQUIP_ID FROM "+CBSystemConstants.equipUser+"T_C_ACLINEEND T,"+CBSystemConstants.equipUser+"T_SUBSTATION A WHERE T.ACLINE_ID IS NOT NULL AND T.ST_ID IS NOT NULL "
				+ "AND T.ST_ID = A.STATION_ID AND A.STATION_NAME NOT LIKE '%虚%' AND A.STATION_NAME NOT LIKE '%T接%' AND A.STATION_NAME NOT LIKE '%PAS%'";
		return sql;
	}
	
	/*
	 * 描述 DataToDemo sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DataToDemoSql() {
		String sql = "update "  
			       + name
			       + "T_SUBSTATION set station_name='演示厂站";
		return sql;
	}
	
	/*
	 * 描述 CZPOperator sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorSql(String cardid) {
		String sql = "select s.equip_name,r.station_name,t.endstate from "+CBSystemConstants.opcardUser+"T_a_CZPACTIONSTATE t,"  
			       + name
			       + "T_EQUIPINFO s,"  
			       + name
			       + "T_SUBSTATION r where t.equipid=s.equip_id and s.station_id=r.station_id and t.cardid='"+cardid+"' and t.stateid=(select max(stateid) from "+CBSystemConstants.opcardUser+"T_a_CZPACTIONSTATE where cardid='"+cardid+"')";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorSD sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorsdSql(String cardid) {
		String sql = "select s.equip_name,r.station_name,t.endstate from "+CBSystemConstants.opcardUser+"T_a_CZPACTIONSTATE t,"  
			       + name
			       + "T_EQUIPINFO s,"  
			       + name
			       + "T_SUBSTATION r where t.equipid=s.equip_id and s.station_id=r.station_id and t.cardid="+cardid+" and t.stateid=(select max(stateid) from "+CBSystemConstants.opcardUser+"T_a_CZPACTIONSTATE where cardid="+cardid+")";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorSD sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorsdSql1() {
		String sql = "select t.orga_id,t.areacode from "  
			       + name
			       + "T_SUBSTATION t where t.station_name='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorSD sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorsdSql2() {
		String sql = "select t.station_id,t.orga_id,t.areacode from "  
			       + name
			       + "T_SUBSTATION t where t.station_name='";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql2(String facLineID) {
		String sql = "select t.acline_id line_id from "+CBSystemConstants.equipUser+"T_c_aclineend t where t.id='"+facLineID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql3(String StationName) {
		String sql = "select * from "  
			       + name
			       + "T_SUBSTATION t Where t.station_name='"+StationName+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql4(String StationName) {
		String sql = "select * from (select * from "  
			       + name
			       + "T_SUBSTATION t Where t.station_name like '%"+StationName+"%' order by t.station_name) where rownum=1";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql5(String StationID) {
		String sql = "select t.station_name from "  
			       + name
			       + "T_SUBSTATION t where t.station_id='"+StationID+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql6(String StationName) {
		String sql= "select STATION_ID from "  
			      + name
			      + "T_SUBSTATION t Where replace(replace(t.station_name,'厂',''),'站','') = '"+StationName+"'";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql7(String StationName) {
		String sql= "select STATION_ID,STATION_NAME from "  
			      + name
			      + "T_SUBSTATION t Where t.station_name like '%"+StationName+"%' and (select count(*) from "+CBSystemConstants.equipUser+"T_EQUIPINFO a where a.station_id=t.station_id)>0";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql8() {
		String sql= "select t.station_id from "  
			      + name
			      + "T_SUBSTATION t where t.station_name like ? and (select count(*) from "+CBSystemConstants.equipUser+"T_EQUIPINFO a where a.station_id=t.station_id)>0";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql9(String StationName) {
		String sql= "select STATION_ID,STATION_NAME from "  
			      + name
			      + "T_SUBSTATION t Where t.station_name like '%"+StationName+"%'";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpery sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorperySql() {
//		String sql="select t.ACLINE_ID LINE_ID from equip.t_c_aclineend t where t.equip_id='";
		String sql="select t.ACLINE_ID LINE_ID from "+CBSystemConstants.equipUser+"T_c_aclineend t where t.id='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpery sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorperySql1() {
		String sql="select t.ST_ID STATION_ID,t.ID EQUIP_ID from "+CBSystemConstants.equipUser+"T_c_aclineend t where t.ACLINE_ID='";
		return sql;
	}
	
	/*
	 * 描述 DriverPorpery sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DriverPorperySql2() {
		String sql= "select t.STATION_NAME from "  
			      + name
			      + "T_SUBSTATION t where t.STATION_ID='";
		return sql;
	}
	
	/*
	 * 描述 InOutLineTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String InOutLineTreeWidgetSql2() {
		String sql = "select a.station_name,a.station_id from "  
			       + name
			       + "T_SUBSTATION a where a.station_id in(select t.st_id from "+CBSystemConstants.equipUser+"T_c_aclineend t where t.acline_id = '";
		return sql;
	}
	
	/*
	 * 描述 DictionaryModelInit sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DictionaryModelInitSql() {
		String sql = "select * from "  
			       + name.replace("..", ".")
			       + "T_VOLTAGELEVEL";
		return sql;
	}
	
	/*
	 * 描述 DeviceStateMentManager sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String DeviceStateMentManagerSql() {
		String sql = "SELECT T.VOLTAGE_CODE,T.VOLTAGE_NAME FROM "  
			       + name
			       + "T_VOLTAGELEVEL T ORDER BY T.VOLTAGE_CODE";
		return sql;
	}
	
	/*
	 * 描述 QueryDeviceDao sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String QueryDeviceDaoSql2() {
		String sql = "select t.voltage_code,t.voltage_name from "
			       + name
			       + "T_VOLTAGELEVEL t order by t.voltage_code";
		return sql;
	}
	
	/*
	 * 描述 TransTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TransTreeWidgetSql() {
		String sql = "select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,d.voltage_code||'kV',c.station_id nodecode,c.station_name nodename from "
			       + name
			       + "T_SUBSTATION c,"
			       + name 
			       + "T_VOLTAGELEVEL d where c.station_isdel=0 and c.voltage_id=d.voltage_id order by station,d.voltage_value desc,nodename asc";
		return sql;
	}
	
	/*
	 * 描述 TransTreeWidget sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String TransTreeWidgetSql1() {
		String sql = "select t.station_id,t.station_name from "
			       + name 
			       + "T_SUBSTATION t,"
			       + name 
			       + "T_VOLTAGELEVEL a where t.voltage_id=a.voltage_id order by a.voltage_code desc,t.station_name";
		return sql;
	}
	
	/*
	 * 描述 GetDIfferStatusDevices sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String GetDIfferStatusDevicesSql1() {
		String sql ="SELECT distinct b.STATION_ID FROM (SELECT equipid FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO A WHERE A.DEVICESTATUS = '-1') a,"+CBSystemConstants.equipUser+"T_EQUIPINFO B WHERE A.EQUIPID = B.EQUIP_ID";

		return sql;
	}
	
	/*
	 * 描述 CZPOperatorJC sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorJCSql() {
		String sql = "SELECT DISTINCT(T2.STATION_ID) AS STATION FROM "+CBSystemConstants.opcardUser+"T_a_CZPACTIONSTATE T,"
			       + name 
			       + "T_EQUIPINFO T2 WHERE T.EQUIPID=T2.EQUIP_ID AND T.CARDID='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorJC sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorJCSql1() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"T_s_breaker t,"
			       + name 
			       + "T_EQUIPINFO s where t.开关id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorJC sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorJCSql2() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"T_s_DISCONNECTOR t,"
			       + name 
			       + "T_EQUIPINFO s where t.刀闸id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorJC sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorJCSql3() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"T_s_GROUNDDISCONNECTOR t,"
			       + name 
			       + "T_EQUIPINFO s where t.接地刀闸id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBSql() {
		String sql = "SELECT DISTINCT(T2.STATION_ID) AS STATION FROM "+CBSystemConstants.opcardUser+"T_a_CZPACTIONSTATE T,"
			       + name 
			       + "T_EQUIPINFO T2 WHERE T.EQUIPID=T2.EQUIP_ID AND T.CARDID='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBSql1() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"T_s_breaker t,"
			       + name 
			       + "T_EQUIPINFO s where t.status=1 and t.开关id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBSql2() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"T_s_DISCONNECTOR t,"
			       + name 
			       + "T_EQUIPINFO s where t.status=1 and t.刀闸id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXB sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBSql3() {
		String sql = "select s.equip_id equip_id,decode(t.yx,'0','1','0') status from "+CBSystemConstants.opcardUser+"T_s_GROUNDDISCONNECTOR t,"
			       + name 
			       + "T_EQUIPINFO s where t.status=1 and t.接地刀闸id=s.cim_id and t.厂站id='";
		return sql;
	}
	
	/*
	 * 描述 CZPOperatorXBD5000 sql
	 * @param
	 * @param
	 * @param
	 * */
	public  String CZPOperatorXBD5000Sql() {
		String sql = "SELECT DISTINCT(T2.STATION_ID) AS STATION FROM "+CBSystemConstants.opcardUser+"T_a_CZPACTIONSTATE T,"
			       + name 
			       + "T_EQUIPINFO T2 WHERE T.EQUIPID=T2.EQUIP_ID AND T.CARDID='";
		return sql;
	}
	
}
