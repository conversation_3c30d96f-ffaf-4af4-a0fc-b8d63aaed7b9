package com.tellhow.czp.mainframe;

import java.awt.BorderLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.FilenameFilter;
import java.util.ArrayList;
import java.util.List;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextField;
import javax.swing.JTree;
import javax.swing.border.EmptyBorder;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;

import com.tellhow.graphicframework.constants.SystemConstants;

public class svgFileChoose extends JDialog {

	private final JPanel contentPanel = new JPanel();
	private JTextField textField;
	private JTree tree;
	public String returnFileName = "" ;
	
	public svgFileChoose(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setLocationCenter();
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	public String bulidsvgFileTree(){
		this.setVisible(true);
		return this.returnFileName;
	}

	/**
	 * Create the dialog.
	 * @return 
	 */
	public void initComponents() {
		setBounds(100, 100, 270, 442);
		getContentPane().setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		getContentPane().add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(null);
		
		textField = new JTextField();
		textField.setBounds(10, 10, 167, 21);
		contentPanel.add(textField);
		textField.setColumns(10);
		
		JButton button = new JButton("\u67E5\u8BE2");
		button.setBounds(187, 9, 67, 23);
		button.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent arg0) {
				bulidTree();
			}
		});
		contentPanel.add(button);
		
		JScrollPane scrollPane = new JScrollPane();
		scrollPane.setBounds(10, 41, 244, 363);
		contentPanel.add(scrollPane);
		
		tree = new JTree();
		scrollPane.setViewportView(tree);
		this.bulidTree();
	}

	
	
	private void bulidTree(){
        DefaultMutableTreeNode rootNode = new DefaultMutableTreeNode("SVG单线图");   //创建根节点     
        DefaultTreeModel treeModel = new DefaultTreeModel(rootNode);
	    DefaultMutableTreeNode newNode;   //为每个序号新建一个树节点
        String filterSvgName = this.textField.getText().trim();
	    
	    File file = new File(SystemConstants.FILE_SVGMAP_PATH);
		List<File> fileList = listFileWhole(file);
		File svgFile = null;
		String fileName = "";
		for (int i = 0; i < fileList.size(); i++) {
			svgFile = fileList.get(i);
			fileName = svgFile.getName();
			if(!"".equals(filterSvgName)&&fileName.indexOf(filterSvgName) < 0){
				continue;
			}
	    	newNode = new DefaultMutableTreeNode(fileName);
			rootNode.add(newNode);
		}
		tree.setModel(treeModel);
		tree.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
                    if (lastNode.isLeaf()) {
                    	returnFileName = lastNode.toString();
                    	setVisible(false);
                    }
                }
            }   
        });
	}

	public static List<File> listFileWhole(File f) {
		List<File> fileList = new ArrayList<File>();
		File[] files = f.listFiles(new FilenameFilter() {
			public boolean accept(File dir, String name) {
				if (name.endsWith(".svg")) {
					return true;
				}
				return false;
			}
		});
		for(File file:files){
			fileList.add(file);
		}
		return fileList;
	}

}
