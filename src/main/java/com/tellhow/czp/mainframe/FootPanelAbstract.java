package com.tellhow.czp.mainframe;

import javax.swing.ImageIcon;

import org.beryl.gui.component.ImagePanel;

public abstract class FootPanelAbstract extends ImagePanel{

	protected javax.swing.JLabel jLabel2;
	
	public FootPanelAbstract(ImageIcon icon) {
		super(icon);
		// TODO Auto-generated constructor stub
	}

	public FootPanel getFootPane() {
		return null;
		
	}
	
	public javax.swing.JLabel getjLabel2() {
		return jLabel2;
	}
}
