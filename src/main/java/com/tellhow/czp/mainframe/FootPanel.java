/*
 * FootPanel_.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.mainframe;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.LineNumberReader;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.swing.JFrame;

import org.beryl.gui.component.ImagePanel;

import com.tellhow.czp.user.User;

import czprule.system.CBSystemConstants;

/**
 *
 * <AUTHOR>
 */
public class FootPanel extends FootPanelAbstract {
	private String memory;
	private String time;
	private Thread timeThread;

	//	private JProgressBar jProgressBar1;
	//	private JProgressBar memoryRatio;

	/** Creates new form FootPanel_ */
	public FootPanel() {
		super(new javax.swing.ImageIcon(ImagePanel.class.getResource("/tellhow/icons/bg2.png")));
		initComponents();
		this.initData();

	}

	private void initData() {
		User user = CBSystemConstants.getUser();
		jLabel2.setText(user.getUserName());
		String IP = "";
		
		try {
			InetAddress addr = InetAddress.getLocalHost();
			IP=addr.getHostAddress();//获得本机IP
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			//e1.printStackTrace();
		}
		jLabel1.setText(IP);
		jProgressBar1.setStringPainted(true);

		//即时更新内存进度条
		timeThread = new Thread(new Runnable() {
			long maxMemory = 0;
			long useMemory = 0;
			long freeMemory = 0;
			SimpleDateFormat sim = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

			public void run() {
				while (true) {

					maxMemory = Runtime.getRuntime().maxMemory() / 1024 / 1024;
//					useMemory = getUsedMemory() / 1024;
					freeMemory = Runtime.getRuntime().freeMemory()/1024/1024;
					if(freeMemory<=100){
						 Runtime.getRuntime().gc();
						 System.gc();
					}
					useMemory = maxMemory-freeMemory;
					memory = useMemory + "M/" + maxMemory + "M";
					jProgressBar1.setValue((int) (useMemory * 100 / maxMemory));
					time = sim.format(new Date());
					jProgressBar1.setString(memory);
					jLabel3.setText(time);

					try {
						timeThread.currentThread().sleep(1000);
					} catch (InterruptedException e) {

						e.printStackTrace();
					}
				}

			}

		});
		timeThread.start();

	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jProgressBar1 = new javax.swing.JProgressBar();
        jLabel1 = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        jLabel3 = new javax.swing.JLabel();
        jLabel5 = new javax.swing.JLabel();
        jLabel6 = new javax.swing.JLabel();
        jLabel7 = new javax.swing.JLabel();
        jLabel8 = new javax.swing.JLabel();

        jLabel1.setText("*********");

        jLabel2.setText("xxx");

        jLabel3.setText("time");

        jLabel5.setBackground(new java.awt.Color(255, 51, 51));
        jLabel5.setText("内存：");

        jLabel6.setText("IP：");

        jLabel7.setText("当前用户：");

        jLabel8.setText("当前时间：");

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(this);
        this.setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .addContainerGap()
                .add(jLabel5)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.UNRELATED)
                .add(jProgressBar1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .add(190, 190, 190)
                .add(jLabel6)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jLabel1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 105, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .add(jLabel7)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jLabel2, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 140, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .add(200, 200, 200)
                .add(jLabel8)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jLabel3, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 138, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jLabel5)
            .add(jProgressBar1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
            .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.BASELINE)
                .add(jLabel3)
                .add(jLabel8)
                .add(jLabel2)
                .add(jLabel7)
                .add(jLabel1)
                .add(jLabel6))
        );
    }// </editor-fold>//GEN-END:initComponents

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JLabel jLabel6;
    private javax.swing.JLabel jLabel7;
    private javax.swing.JLabel jLabel8;
    private javax.swing.JProgressBar jProgressBar1;
    // End of variables declaration//GEN-END:variables

//	static {
//		//		//加载文件
//		try {
//			File customer = new File("C:/WINDOWS/system32/tcnative-1.dll");
//			if (customer.exists()) {
//			} else {
//				FileInputStream fin = new FileInputStream(new File(
//						"src/tcnative-1.dll"));
//				FileOutputStream fout = new FileOutputStream(customer);
//				byte cache[] = new byte[fin.available()];
//				fin.read(cache);
//				fout.write(cache);
//
//				fout.close();
//				fin.close();
//			}
//
//		} catch (Exception e) {
//
//			e.printStackTrace();
//		}
//		System.loadLibrary("tcnative-1");
//	}
//	int pid = Stdlib.getpid();

	public int getUsedMemory() {
		int result = 0;
		try {
			Process pro = Runtime.getRuntime().exec("tasklist");
			InputStreamReader reader = new InputStreamReader(
					pro.getInputStream());
			LineNumberReader lineReader = new LineNumberReader(reader);
			String line;

			StringBuffer sb = new StringBuffer("");
			while ((line = lineReader.readLine()) != null) {
				sb.append(line + "\n");
				if (line.indexOf("javaw.exe") >= 0) {
					int len = line.length();
					String xx = line.substring(0, len);
					int indexK=line.toLowerCase().lastIndexOf("k");
					int countSpace=0;
					StringBuilder memory=new StringBuilder();
					for(int i=indexK;i>=0;i--){
						char ch=line.charAt(i);
						if(ch>='0'&&ch<='9'){
							memory.append(ch);
						}
						if(ch==' '){
							countSpace++;
						}
						if(countSpace==2){
							break;
						}
					}
					memory=backToHead(memory);
					result=Integer.parseInt(memory.toString());
					/*xx = xx.substring(58, xx.length());
					xx = xx.replaceAll(",", "");
					xx = xx.replaceAll("K", "");
					xx = xx.trim();
					while(xx.indexOf(" ")>=0)
						xx = xx.replace(" ", "");

					int t = Integer.valueOf((xx == null || xx.equals("") ? "0"
							: xx));

					String _pid = line.substring(28, 32);
					String __pid=_pid.trim();
					if(__pid.equals("")){
					
					}else{
						int pid_ = Integer.valueOf(__pid);
						if (pid_ == getPid()) {
							result += t;
						}
					}*/
				}
			}

		} catch (IOException e) {
			System.err.println("获取任务列表异常");
			e.printStackTrace();
		}

		return result;
	}
	
	private StringBuilder backToHead(StringBuilder memory2) {
		StringBuilder out=new StringBuilder();
		for (int i =memory2.length()-1; i >= 0; i--) {
			out.append(memory2.charAt(i));
		}
		return out;
	}

	private static int getPid() {   
        RuntimeMXBean runtime = ManagementFactory.getRuntimeMXBean();   
        String name = runtime.getName(); // format: "pid@hostname"   
        try {   
            return Integer.parseInt(name.substring(0, name.indexOf('@')));   
        } catch (Exception e) {   
            return -1;   
        }   
    }

	public javax.swing.JLabel getjLabel2() {
		return jLabel2;
	}

	public static void main(String args[]) {
		JFrame f = new JFrame();
		f.setContentPane(new FootPanel());
		f.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
		f.setSize(1000, 300);
		f.setVisible(true);
	}

}
