/*
 * DeviceRuntypeChoose.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.mainframe;

import java.awt.Toolkit;
import java.util.List;

import javax.swing.DefaultComboBoxModel;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.DictionarysModel;
import czprule.system.CBSystemConstants;

/**
 *
 * <AUTHOR>
 */
public class DeviceRuntypeChoose extends javax.swing.JDialog {

	private DictionarysModel returnDM = null; //返回结果

	//flag 0 :开关类型 1：刀闸类型 2:母线类型 3：接线方式
	/** Creates new form DeviceRuntypeChoose */
	public DeviceRuntypeChoose(java.awt.Frame parent, boolean modal, String flag, String type) {
		super(parent, modal);
		this.setTitle("设备属性修改");
		initComponents();
		init(flag, type);
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width),
				(h - this.getSize().height) / 2);
	}

	public DictionarysModel chooseDM() {
		this.setVisible(true);
		return this.returnDM;
	}

	private void init(String flag, String type) {

		List<DictionarysModel> dictionModels = null;
		//判断设备是否配置装备
		String pro = "PW";
		if(flag.contains("PW")){
			flag = flag.replace("PW", "");
			if ("0".equals(flag)) {
				//配网装备 类型前缀为“PW”
				dictionModels = CBSystemConstants
						.getDictionary(pro+SystemConstants.Switch);
				this.jLabel1.setText("请选择设备类型：");
			} else if ("1".equals(flag)) {
				dictionModels = CBSystemConstants
						.getDictionary(pro+SystemConstants.SwitchSeparate);
				this.jLabel1.setText("请选择设备类型：");
			} else if ("2".equals(flag)) {
				dictionModels = CBSystemConstants
						.getDictionary(pro+SystemConstants.MotherLine);
				this.jLabel1.setText("请选择设备类型：");
			} else if ("3".equals(flag)){
				dictionModels = CBSystemConstants.getDictionary("ACLineSegment");
				this.jLabel1.setText("请选择设备类型：");
			} else if ("4".equals(flag)){
				dictionModels = CBSystemConstants.getDictionary("ACLineSegment");
				this.jLabel1.setText("请选择设备接线方式：");
			} else if ("5".equals(flag)){
				dictionModels = CBSystemConstants.getDictionary("runmodel");
				this.jLabel1.setText("请选择设备接线方式：");
			}else if ("6".equals(flag)) {
				dictionModels = CBSystemConstants
						.getDictionary(pro+SystemConstants.SwitchFlowGroundLine);
				this.jLabel1.setText("请选择设备类型：");
			}
		}else{
			if ("0".equals(flag)) {
				dictionModels = CBSystemConstants
						.getDictionary(SystemConstants.Switch);
				this.jLabel1.setText("请选择设备类型：");
			} else if ("1".equals(flag)) {
				dictionModels = CBSystemConstants
						.getDictionary(SystemConstants.SwitchSeparate);
				this.jLabel1.setText("请选择设备类型：");
			} else if ("2".equals(flag)) {
				dictionModels = CBSystemConstants
						.getDictionary(SystemConstants.MotherLine);
				this.jLabel1.setText("请选择设备类型：");
			} else if ("3".equals(flag)){
				dictionModels = CBSystemConstants.getDictionary("ACLineSegment");
				this.jLabel1.setText("请选择设备类型：");
			} else if ("4".equals(flag)){
				dictionModels = CBSystemConstants.getDictionary("ACLineSegment");
				this.jLabel1.setText("请选择设备接线方式：");
			} else if ("5".equals(flag)){
				dictionModels = CBSystemConstants.getDictionary("runmodel");
				this.jLabel1.setText("请选择设备接线方式：");
			}else if ("6".equals(flag)) {
				dictionModels = CBSystemConstants
						.getDictionary(SystemConstants.SwitchFlowGroundLine);
				this.jLabel1.setText("请选择设备类型：");
			}
		}
		
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DictionarysModel dm = null;
		for (int i = 0; i < dictionModels.size(); i++) {
			dm = dictionModels.get(i);
			model.addElement(dm);
			if(dm.getName().equals(type))
				model.setSelectedItem(dm);
		}
		this.jComboBox1.setModel(model);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jComboBox1 = new javax.swing.JComboBox();
		jLabel1 = new javax.swing.JLabel();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jComboBox1.setModel(new javax.swing.DefaultComboBoxModel(new String[] {
				"Item 1", "Item 2", "Item 3", "Item 4" }));
		jComboBox1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jComboBox1ActionPerformed(evt);
			}
		});

		jLabel1.setText("\u8bf7\u9009\u62e9\u8bbe\u5907\u8fd0\u884c\u7c7b\u578b\uff1a");

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(
				layout.createSequentialGroup()
						.addContainerGap()
						.add(jLabel1)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.UNRELATED)
						.add(jComboBox1, 0, 129, Short.MAX_VALUE)
						.addContainerGap()));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.add(28, 28, 28)
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.BASELINE)
								.add(jLabel1)
								.add(jComboBox1,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
										org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
						.addContainerGap(31, Short.MAX_VALUE)));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	private void jComboBox1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		returnDM = (DictionarysModel) this.jComboBox1.getSelectedItem();
		this.setVisible(false);
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				DeviceRuntypeChoose dialog = new DeviceRuntypeChoose(
						new javax.swing.JFrame(), true, "", "");
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JComboBox jComboBox1;
	private javax.swing.JLabel jLabel1;
	// End of variables declaration//GEN-END:variables

}
