package com.tellhow.czp.mainframe;

import java.awt.Component;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Vector;

import javax.swing.ComboBoxEditor;
import javax.swing.ComboBoxModel;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JTextField;
import javax.swing.border.Border;

import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tellhow.czp.util.PinYinUtil;

import czprule.model.CodeNameModel;
import czprule.system.DBManager;

/**
 * 版权声明: 泰豪软件股份有限公司版权所有 功能说明: 作 者: 郑柯 开发日期: 2011-5-18 上午09:11:47
 */
public class JXComboBox extends JComboBox {
	public JXComboBox() {
		super();
		this.setEditor(new PopupComboEditor());
		this.setEditable(true);
		final JTextField textfield = (JTextField) getComboBox().getEditor().getEditorComponent();
	}

	public JComboBox getComboBox() {
		return this;
	}

	public void findMatchingStrings(String entered) {
		if (entered.equals(""))
			return;
		List<CodeNameModel> v1 = new Vector<CodeNameModel>();
		List<CodeNameModel> v2 = new Vector<CodeNameModel>();
		List<CodeNameModel> v3 = new Vector<CodeNameModel>();
		List<CodeNameModel> v4 = new Vector<CodeNameModel>();
		ComboBoxModel model = this.getModel();
		entered = entered.toLowerCase();
		String stationName;
		List<String> stationPYs;// 厂站拼音
		List<String> stationPYHeads;// 厂站拼音首字母缩写

		for (int i = 0; i < model.getSize(); i++) {
			stationName = model.getElementAt(i).toString();
			stationPYs = PinYinUtil.getPinYin(stationName);
			stationPYHeads = PinYinUtil.getHead(stationName);
			String stationPYHead;
			boolean hasAdd=false;
			for (int j = 0; j < stationPYHeads.size(); j++) {
				String stationPY = stationPYs.get(j);
				stationPYHead = stationPYHeads.get(j);
				if (stationName.toLowerCase().equals(entered)
						|| stationPY.equals(entered)
						|| stationPYHead.equals(entered)) {
					v1.add((CodeNameModel) model.getElementAt(i));
					hasAdd=true;
					break;
				} else if (stationName.toLowerCase().startsWith(entered)
						|| stationPY.startsWith(entered)
						|| stationPYHead.startsWith(entered)) {
					v2.add((CodeNameModel) model.getElementAt(i));
					hasAdd=true;
					break;
				} 
				else if (stationName.toLowerCase().indexOf(entered) >= 0
						|| stationPY.indexOf(entered) >= 0
						|| stationPYHead.indexOf(entered) >= 0) {
					v3.add((CodeNameModel) model.getElementAt(i));
					hasAdd=true;
					break;
				}
				else if (stationName.equals("")){
					v4.add((CodeNameModel) model.getElementAt(i));
					hasAdd=true;
				}
			}
			
			if(!hasAdd)
				v4.add((CodeNameModel) model.getElementAt(i));
		}
		v1.addAll(v2);
		v1.addAll(v3);
		v1.addAll(v4);
		JPopupTextField textField = (JPopupTextField) this.getEditor()
				.getEditorComponent();
		int caretPosition = textField.getCaretPosition();
		this.setModel(new DefaultComboBoxModel(v1.toArray()));
		textField.setText(entered);
		textField.setCaretPosition(caretPosition);
		if (!this.getSelectedItem().toString().equals(entered))
			System.out.println(this.isShowing());
			this.showPopup();
	}
	
	public void fillComboBox(String sql) {
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		SqlRowSet set = DBManager.queryForRowSet(sql);
		while (set.next()){
			cnm=new CodeNameModel();
			cnm.setCode(set.getString(1));
			cnm.setName(set.getString(2));
			model.addElement(cnm);
		}
		this.setModel(model);
	}
	
	public void setSelected(String code) {
		for(int i = 0; i < this.getModel().getSize(); i++) {
			CodeNameModel cnm = (CodeNameModel)this.getModel().getElementAt(i);
			if(cnm.getCode().equals(code)) {
				this.setSelectedIndex(i);
				break;
			}
		}
	}
}
