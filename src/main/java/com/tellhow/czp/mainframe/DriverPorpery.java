package com.tellhow.czp.mainframe;

import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.Font;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.mainframe.dao.DriverPoperyDao;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.model.DictionarysModel;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.SVGAddDeviceInf;
import czprule.system.ShowMessage;
/**
 * 泰豪软件科技有限公司
 * <AUTHOR>
 * 修改：张余平
 * 界面修改：王豪
 */
public class DriverPorpery extends JPanel implements SVGAddDeviceInf{
	private PowerDevice choosePd = null;
	private QueryDeviceDao dao = new QueryDeviceDao();
	private final JPanel contentPanel = new JPanel();
	private JTextField jTextField3;
	private JTextField jTextField4;
	private JTextField jTextField2;
	private JTextField jTextField1;
	private JTextField jTextField5;
	private JTextField jTextField6;
	private JTextField jTextField7;
	private JTable jTable2;
	private List equip_idlist=null;
	/**
	 * Launch the application.
	 */
/*	public static void main(String[] args) {
		try {
			DriverPorpery dialog = new DriverPorpery();
			dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
			dialog.setVisible(true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}*/

	@Override
	public void addPowerDevice(PowerDevice pd) {
		// TODO Auto-generated method stub
		this.choosePd=pd;
		initPanel();
	}
	/**
	 * 根据当前操作添加相应的设备
	 * @param pd 设备
	 */
	public void initPanel() {
		//---设备属性
		String volt = String.valueOf(choosePd.getPowerVoltGrade());
		jTextField1.setText(choosePd.getPowerDeviceName());
		jTextField2.setText(volt.substring(0, volt.indexOf(".")) + "kV");
		jTextField3.setText(choosePd.getPowerStationName());
		jTextField4.setText(CBSystemConstants.getDeviceStatusName(
				choosePd.getDeviceType(), choosePd.getDeviceStatus()));
		if(choosePd.isPW()){
			jTextField5.setText(CBSystemConstants.getDictionaryName(
					choosePd.getDeviceRunType(), "PW"+choosePd.getDeviceType()));
		}else{
			jTextField5.setText(CBSystemConstants.getDictionaryName(
					choosePd.getDeviceRunType(), choosePd.getDeviceType()));
		}

		jTextField6.setText(CBSystemConstants.getDictionaryName(
				choosePd.getDeviceRunModel(), "runmodel"));
//		String sql="select t.equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_flag='"+choosePd.getDeviceType()+"'";
		String sql = OPEService.getService().DriverPorpetySql()+choosePd.getDeviceType()+"'";
		List equiptype_namelist=DBManager.queryForList(sql);
		if(equiptype_namelist.size()>0){
			Map map=(Map) equiptype_namelist.get(0);
			String equiptype_name=(String) map.get("EQUIPTYPE_NAME");
			jTextField7.setText(equiptype_name);
		}else{
			jTextField7.setText("");
		}
		//调管机构初始化
		jTextField8.setText("");
		for(Iterator it = CBSystemConstants.getMapPowerOrgan().values().iterator();it.hasNext();) {
			PowerDevice organ = (PowerDevice)it.next();
			if(!choosePd.getOrgaId().equals("") && choosePd.getOrgaId().equals(organ.getPowerDeviceID()))
				jTextField8.setText(organ.getPowerDeviceName());
		}
		
		//许可机构初始化
		jTextField9.setText("");
		for(Iterator it = CBSystemConstants.getMapPowerOrgan().values().iterator();it.hasNext();) {
			PowerDevice organ = (PowerDevice)it.next();
			if(!choosePd.getPermissionOrganID().equals("") && choosePd.getPermissionOrganID().equals(organ.getPowerDeviceID()))
				jTextField9.setText(organ.getPowerDeviceName());
		}
		//监控机构初始化
		jTextField10.setText("");
		for(Iterator it = CBSystemConstants.getMapPowerOrgan().values().iterator();it.hasNext();) {
			PowerDevice organ = (PowerDevice)it.next();
			if(!choosePd.getSupervisionright_id().equals("") && choosePd.getSupervisionright_id().equals(organ.getPowerDeviceID()))
				jTextField10.setText(organ.getPowerDeviceName());
		}
		
		jButton7.setEnabled(true);
		jButton10.setEnabled(true);
		jButton11.setEnabled(true);
		jButton12.setEnabled(true);
		jButton13.setEnabled(true);
		if (SystemConstants.Switch.equals(choosePd.getDeviceType())
				|| SystemConstants.SwitchSeparate.equals(choosePd.getDeviceType())
				|| SystemConstants.SwitchFlowGroundLine.equals(choosePd.getDeviceType())
				|| SystemConstants.MotherLine.equals(choosePd.getDeviceType())) {
			jButton8.setEnabled(true);
		} else {
			jButton8.setEnabled(false);
		}
		if (SystemConstants.SwitchFlowGroundLine.equals(choosePd
				.getDeviceType())
				|| SystemConstants.SwitchSeparate.equals(choosePd
						.getDeviceType()) || choosePd.isPW()==true) {
			jButton9.setEnabled(false);
		} else {
			jButton9.setEnabled(true);
		}
		this.repaint();
		//---设备保护
		jTable1.getColumnModel().getColumn(0).setCellEditor(new CustomComboBoxEditor());
		jTable1.getColumnModel().getColumn(1).setCellEditor(new CustomComboBoxEditor());
		jTable1.getColumnModel().getColumn(3).setCellEditor(new CustomComboBoxEditor());
		DefaultTableModel model = (DefaultTableModel)jTable1.getModel();
		for(int i = model.getRowCount()-1; i >= 0; i--) {
			model.removeRow(i);
		}
		List<Object[]> types=getProTypeById(choosePd.getPowerDeviceID());
		for(int i = 0; i < types.size(); i++) {
			model.insertRow(jTable1.getRowCount(), new Object[] {types.get(i)[0] , types.get(i)[1], types.get(i)[2],types.get(i)[3] });
		}
		//---线路解合环
		//清除table中的内容  先清除再查询数据
		DefaultTableModel model2 =(DefaultTableModel) jTable2.getModel(); 
		while(model2.getRowCount()>0){     
			model2.removeRow(model2.getRowCount()-1);
		}
		DefaultComboBoxModel comboboxmodel = new DefaultComboBoxModel();
		CodeNameModel codeNameModel1=new CodeNameModel();
		codeNameModel1.setCode("1");
		codeNameModel1.setName("进线");
		CodeNameModel codeNameModel2=new CodeNameModel();
		codeNameModel2.setCode("2");
		codeNameModel2.setName("出线");
		comboboxmodel.addElement(codeNameModel1);
		comboboxmodel.addElement(codeNameModel2);
		JComboBox comboBox=new JComboBox();
		comboBox.setModel(comboboxmodel);
		DefaultCellEditor cellEditor = new DefaultCellEditor(comboBox);
		jTable2.getColumnModel().getColumn(2).setCellEditor(cellEditor);
//		String sql4="select t.LINE_ID from "+CBSystemConstants.opcardUser+"t_e_substationtopology t where t.equip_id='"+choosePd.getPowerDeviceID()+"'";
		//edit 2014.6.26
		String sql4=OPEService.getService().DriverPorperySql()+choosePd.getPowerDeviceID()+"'";
		List line_ids=DBManager.queryForList(sql4);
		String LINE_ID="";
		if(line_ids.size()>0){
			Map map=(Map) line_ids.get(0);
			LINE_ID=(String) map.get("LINE_ID");
		}
//		String sql5="select t.STATION_ID,t.EQUIP_ID from "+CBSystemConstants.opcardUser+"t_e_substationtopology t where t.LINE_ID='"+LINE_ID+"'";
		//edit 2014.6.26
		String sql5=OPEService.getService().DriverPorperySql1()+LINE_ID+"'";
		List lists=DBManager.queryForList(sql5);
		String STATION_ID="";
		String EQUIP_ID="";
		equip_idlist=new ArrayList();
		Vector v_row=null;
		DefaultTableModel model3 =(DefaultTableModel) jTable2.getModel();
		for(int index=0;index<lists.size();index++){
			Map map=(Map) lists.get(index);
			STATION_ID = (String) map.get("STATION_ID");
			if(STATION_ID.equals(choosePd.getPowerStationID())) {
				lists.remove(map);
				lists.add(0,map);
				break;
			}
		}
		for(int index=0;index<lists.size();index++){
			CodeNameModel codenamemodel=new CodeNameModel();
			Map map=(Map) lists.get(index);
			STATION_ID = (String) map.get("STATION_ID");
			EQUIP_ID = (String) map.get("EQUIP_ID");
			equip_idlist.add(EQUIP_ID);
			String sql6="select t.FLOWINFO from "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO t where t.EQUIP_ID='"+EQUIP_ID+"'";
			List importlinelist=DBManager.queryForList(sql6);
			String IMPORTLINE="";
			if(importlinelist.size()>0){
				Map map2=(Map) importlinelist.get(0);
				IMPORTLINE=(String) map2.get("FLOWINFO");
			}
			if(IMPORTLINE.toString().equals("2")){
					codenamemodel.setCode(IMPORTLINE.toString());
					codenamemodel.setName("出线");
			}else if(IMPORTLINE.toString().equals("1")){
					codenamemodel.setCode(IMPORTLINE.toString());
					codenamemodel.setName("进线");
			}
			else {
				codenamemodel.setCode("-1");
				codenamemodel.setName("");
			}
			v_row=new Vector();
			v_row.add(choosePd.getPowerDeviceName());//线路名称
//			String sql7="select t.STATION_NAME from "+CBSystemConstants.opcardUser+"t_e_substation t where t.STATION_ID='"+STATION_ID+"'";
			//edit 2014.6.26
			String sql7=OPEService.getService().DriverPorperySql2()+STATION_ID+"'";
			List stationNames=DBManager.queryForList(sql7);
			String STATION_NAME="";
			if(stationNames.size()>0){
				Map map3=(Map) stationNames.get(0);
				STATION_NAME=(String) map3.get("STATION_NAME");
			}
			v_row.add(STATION_NAME);//变电站名称
			v_row.add(codenamemodel);//进出线
			model3.addRow(v_row);
		}
		jTable2.setModel(model3);
	}
	/**
	 * Create the dialog.
	 */
	public DriverPorpery(){
		initComponents();
		jButton7.setEnabled(false);
		jButton8.setEnabled(false);
		jButton9.setEnabled(false);
		jButton10.setEnabled(false);
		jButton11.setEnabled(false);
		jButton12.setEnabled(false);
		jButton13.setEnabled(false);
		{
			{
				{
					buttonPane = new JPanel();
					add(buttonPane, BorderLayout.NORTH);
					buttonPane.setLayout(new BorderLayout(0, 0));
					jButton3 = new JButton();
					jButton3.setText("取消");
					jButton3.setFont(new Font("宋体", Font.PLAIN, 13));
					buttonPane.add(jButton3, BorderLayout.EAST);
					jButton3.setActionCommand("Cancel");
					jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/back.png"))); // NOI18N
					jButton3.setToolTipText("取消");
					jButton3.setMargin(new java.awt.Insets(1,1,1,1));
					jButton3.setFocusPainted(false);
					jButton3.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton3ActionPerformed(evt);
						}
					});
					
					//jButton11 = new JButton();
					//jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/edit.png"))); // NOI18N
			        
				}
			}
		}
	}
	
	public void initComponents() {
		
		setBounds(100, 100, 283, 505);
		this.setLayout(new BorderLayout());
		contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
		this.add(contentPanel, BorderLayout.CENTER);
		contentPanel.setLayout(new BorderLayout(0, 0));
		{
			JPanel northpanel = new JPanel();
			northpanel.setBorder(new TitledBorder(null, "\u8BBE\u5907\u5C5E\u6027", TitledBorder.LEADING, TitledBorder.TOP, null, null));
			contentPanel.add(northpanel, BorderLayout.NORTH);
			northpanel.setLayout(new BorderLayout(0, 0));
			{
				JPanel panel = new JPanel();
				northpanel.add(panel, BorderLayout.SOUTH);
				panel.setLayout(new BorderLayout(0, 0));
				{
					jLabel6 = new JLabel("\u63A5\u7EBF\u65B9\u5F0F\uFF1A");
					jLabel6.setFont(new java.awt.Font("宋体", 0, 12));
					panel.add(jLabel6, BorderLayout.WEST);
				}
				{
					jTextField6 = new JTextField();
					jTextField6.setEditable(false);
					jTextField6.setPreferredSize(new java.awt.Dimension(6, 28));
					panel.add(jTextField6, BorderLayout.CENTER);
					jTextField6.setColumns(10);
				}
				{
					jButton9 = new JButton("");
					panel.add(jButton9, BorderLayout.EAST);
					jButton9.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/much.png"))); // NOI18N
					jButton9.setBorder(null);
					jButton9.setFocusPainted(false);
					{
						panel_3 = new JPanel();
						panel.add(panel_3, BorderLayout.SOUTH);
						panel_3.setLayout(new BorderLayout(0, 0));
						{
							jButton12 = new JButton();
							jButton12.setIcon(new javax.swing.ImageIcon(getClass().getResource(
									"/tellhow/btnIcon/much.png"))); // NOI18N
							jButton12.setAutoscrolls(true);
							jButton12.setBorder(null);
							jButton12.setFocusPainted(false);
							jButton12.addActionListener(new java.awt.event.ActionListener() {
								public void actionPerformed(java.awt.event.ActionEvent evt) {
									jButton12ActionPerformed(evt);
								}
							});
							panel_3.add(jButton12, BorderLayout.EAST);
						}
						{
							jLabel7 = new JLabel();
							jLabel7.setText("调管机构：");
							jLabel7.setFont(new java.awt.Font("宋体", 0, 12));
							panel_3.add(jLabel7, BorderLayout.WEST);
						}
						{
							jTextField8 = new JTextField();
							jTextField8.setEditable(false);
							jTextField8.setPreferredSize(new java.awt.Dimension(6, 28));
							panel_3.add(jTextField8, BorderLayout.CENTER);
							jTextField8.setColumns(10);
							
							
						}
						{
							panel_5 = new JPanel();
							panel_3.add(panel_5, BorderLayout.SOUTH);
							panel_5.setLayout(new BorderLayout(0, 0));
							{
								jButton13 = new JButton();
								jButton13.setIcon(new javax.swing.ImageIcon(getClass().getResource(
										"/tellhow/btnIcon/much.png"))); // NOI18N
								jButton13.setAutoscrolls(true);
								jButton13.setBorder(null);
								jButton13.setFocusPainted(false);
								jButton13.addActionListener(new java.awt.event.ActionListener() {
									public void actionPerformed(java.awt.event.ActionEvent evt) {
										jButton13ActionPerformed(evt);
									}
								});
								panel_5.add(jButton13, BorderLayout.EAST);
							}
							{
								jLabel8 = new JLabel();
								jLabel8.setText("许可机构：");
								jLabel8.setFont(new java.awt.Font("宋体", 0, 12));
								panel_5.add(jLabel8, BorderLayout.WEST);
							}
							{
								jTextField9 = new JTextField();
								jTextField9.setEditable(false);
								jTextField9.setPreferredSize(new java.awt.Dimension(6, 28));
								panel_5.add(jTextField9, BorderLayout.CENTER);
								jTextField9.setColumns(10);
							}
						}
						{
							panel_7 = new JPanel();
							panel_5.add(panel_7, BorderLayout.SOUTH);
							panel_7.setLayout(new BorderLayout(0, 0));
							{
								jButton15 = new JButton();
								jButton15.setIcon(new javax.swing.ImageIcon(getClass().getResource(
										"/tellhow/btnIcon/much.png"))); // NOI18N
								jButton15.setAutoscrolls(true);
								jButton15.setBorder(null);
								jButton15.setFocusPainted(false);
								jButton15.addActionListener(new java.awt.event.ActionListener() {
									public void actionPerformed(java.awt.event.ActionEvent evt) {
										jButton15ActionPerformed(evt);
									}
								});
								panel_7.add(jButton15, BorderLayout.EAST);
							}
							{
								jLabel9 = new JLabel();
								jLabel9.setText("监控机构：");
								jLabel9.setFont(new java.awt.Font("宋体", 0, 12));
								panel_7.add(jLabel9, BorderLayout.WEST);
							}
							{
								jTextField10 = new JTextField();
								jTextField10.setEditable(false);
								jTextField10.setPreferredSize(new java.awt.Dimension(6, 28));
								panel_7.add(jTextField10, BorderLayout.CENTER);
								jTextField10.setColumns(10);
							}
						}
					}
					jButton9.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton9ActionPerformed(evt);
						}
					});
				}
			}
			{
				JPanel panel = new JPanel();
				northpanel.add(panel, BorderLayout.CENTER);
				panel.setLayout(new BorderLayout(0, 0));
				{
					jLabel5 = new JLabel("\u5B89\u88C5\u7C7B\u578B\uFF1A");
					jLabel5.setFont(new java.awt.Font("宋体", 0, 12));
					panel.add(jLabel5, BorderLayout.WEST);
				}
				{
					jTextField5 = new JTextField();
					jTextField5.setEditable(false);
					jTextField5.setPreferredSize(new java.awt.Dimension(6, 28));
					panel.add(jTextField5, BorderLayout.CENTER);
					jTextField5.setColumns(10);
				}
				{
					jButton8 = new JButton("");
					panel.add(jButton8, BorderLayout.EAST);
					jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/much.png"))); // NOI18N
					jButton8.setAutoscrolls(true);
					jButton8.setBorder(null);
					jButton8.setFocusPainted(false);
					{
						panel_4 = new JPanel();
						panel.add(panel_4, BorderLayout.NORTH);
						panel_4.setLayout(new BorderLayout(0, 0));
						{
							lblNewLabel = new JLabel("\u8BBE\u5907\u7C7B\u578B\uFF1A");
							panel_4.add(lblNewLabel, BorderLayout.WEST);
						}
						{
							jTextField7 = new JTextField();
							panel_4.add(jTextField7, BorderLayout.CENTER);
							jTextField7.setEditable(false);
							jTextField7.setPreferredSize(new java.awt.Dimension(6, 28));
							panel_4.add(jTextField7, BorderLayout.CENTER);
							jTextField7.setColumns(10);
							
						}
						{
							jButton11 = new JButton("");
							panel_4.add(jButton11, BorderLayout.EAST);
							jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource(
									"/tellhow/btnIcon/much.png"))); // NOI18N
							jButton11.setAutoscrolls(true);
							jButton11.setBorder(null);
							jButton11.setFocusPainted(false);
							jButton11.addActionListener(new java.awt.event.ActionListener() {
								public void actionPerformed(java.awt.event.ActionEvent evt) {
									jButton11ActionPerformed(evt);
								}
							});
						}
					}
					jButton8.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton8ActionPerformed(evt);
						}
					});
				}
			}
			{
				JPanel panel = new JPanel();
				northpanel.add(panel, BorderLayout.NORTH);
				panel.setLayout(new BorderLayout(0, 0));
				{
					JPanel panel_1 = new JPanel();
					panel.add(panel_1, BorderLayout.SOUTH);
					panel_1.setLayout(new BorderLayout(0, 0));
					{
						jLabel3 = new JLabel("\u53D8 \u7535 \u7AD9\uFF1A");
						jLabel3.setFont(new java.awt.Font("宋体", 0, 12));
						panel_1.add(jLabel3, BorderLayout.WEST);
					}
					{
						jTextField3 = new JTextField();
						jTextField3.setEditable(false);
						jTextField3.setPreferredSize(new java.awt.Dimension(6, 28));
						panel_1.add(jTextField3, BorderLayout.CENTER);
						jTextField3.setColumns(10);
					}
					{
						JPanel panel_2 = new JPanel();
						panel_1.add(panel_2, BorderLayout.SOUTH);
						panel_2.setLayout(new BorderLayout(0, 0));
						{
							jLabel4 = new JLabel("\u8BBE\u5907\u72B6\u6001\uFF1A");
							jLabel4.setFont(new java.awt.Font("宋体", 0, 12));
							panel_2.add(jLabel4, BorderLayout.WEST);
						}
						{
							jTextField4 = new JTextField();
							jTextField4.setEditable(false);
							jTextField4.setPreferredSize(new java.awt.Dimension(6, 28));
							panel_2.add(jTextField4, BorderLayout.CENTER);
							jTextField4.setColumns(10);
						}
					}
				}
				{
					jLabel2 = new JLabel("\u7535\u538B\u7B49\u7EA7\uFF1A");
					jLabel2.setFont(new java.awt.Font("宋体", 0, 12));
					panel.add(jLabel2, BorderLayout.WEST);
				}
				{
					jTextField2 = new JTextField();
					jTextField2.setEditable(false);
					jTextField2.setPreferredSize(new java.awt.Dimension(6, 28));
					panel.add(jTextField2, BorderLayout.CENTER);
					jTextField2.setColumns(10);
				}
				{
					JPanel panel_1 = new JPanel();
					panel.add(panel_1, BorderLayout.NORTH);
					panel_1.setLayout(new BorderLayout(0, 0));
					{
						jLabel1 = new JLabel("\u8BBE\u5907\u540D\u79F0\uFF1A");
						jLabel1.setFont(new java.awt.Font("宋体", 0, 12));
						panel_1.add(jLabel1, BorderLayout.WEST);
					}
					{
						jTextField1 = new JTextField();
						jTextField1.setEditable(false);
						jTextField1.setMinimumSize(new Dimension(6, 20));
						jTextField1.setPreferredSize(new java.awt.Dimension(6, 28));
						panel_1.add(jTextField1, BorderLayout.CENTER);
						jTextField1.setColumns(10);
					}
					{
						jButton7 = new JButton("");
						panel_1.add(jButton7, BorderLayout.EAST);
						jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/much.png"))); // NOI18N
						jButton7.setBorder(null);
						jButton7.setFocusPainted(false);
						jButton7.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton7ActionPerformed(evt);
							}
						});
					}
				}
				{
					jButton10 = new JButton("");
					panel.add(jButton10, BorderLayout.EAST);
					jButton10.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/much.png"))); // NOI18N
					jButton10.setBorder(null);
					jButton10.setFocusPainted(false);
					jButton10.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton10ActionPerformed(evt);
						}
					});
				}
			}
		}
		{
			JPanel southpanel = new JPanel();
			southpanel.setPreferredSize(new Dimension(10, 155));
			southpanel.setBorder(new TitledBorder(null, "\u7EBF\u8DEF\u89E3\u5408\u73AF", TitledBorder.LEADING, TitledBorder.TOP, null, null));
			contentPanel.add(southpanel, BorderLayout.SOUTH);
			southpanel.setLayout(new BorderLayout(0, 0));
			{
				jScrollPane2 = new JScrollPane();
				southpanel.add(jScrollPane2, BorderLayout.CENTER);
				{
					jTable2 = new JTable();
					Object[][] tableDate = null;
					DefaultTableModel jTable1Mode2 = new DefaultTableModel(tableDate, new String[] {"线路名称", "变电站名称","进出线"});
					jTable2.setPreferredScrollableViewportSize(new Dimension(450, 120));
					jTable2.setModel(new DefaultTableModel(
						new Object[][] {
						},
						new String[] {
						}
					));
					jScrollPane2.setViewportView(jTable2);
					jTable2.setModel(jTable1Mode2);
				}
			}
			{
				panel_6 = new JPanel();
				southpanel.add(panel_6, BorderLayout.NORTH);
				panel_6.setLayout(new BorderLayout(0, 0));
				{
					jButton14 = new JButton("New button");
					jButton14.setText("保存");
					jButton14.setFont(new Font("宋体", Font.PLAIN, 13));
					jButton14.setIcon(new javax.swing.ImageIcon(getClass().getResource(
							"/tellhow/btnIcon/save.gif"))); // NOI18N
					jButton14.setToolTipText("\u4fdd\u5b58");
					jButton14.setMargin(new java.awt.Insets(1,1,1,1));
					jButton14.setFocusPainted(false);
					jButton14.addActionListener(new java.awt.event.ActionListener() {
						public void actionPerformed(java.awt.event.ActionEvent evt) {
							jButton14ActionPerformed(evt);
						}
					});
					panel_6.add(jButton14, BorderLayout.EAST);
				}
			}
		}
		{
			JPanel conterpanel = new JPanel();
			conterpanel.setBorder(new TitledBorder(null, "\u8BBE\u5907\u4FDD\u62A4", TitledBorder.LEADING, TitledBorder.TOP, null, null));
			contentPanel.add(conterpanel, BorderLayout.CENTER);
			conterpanel.setLayout(new BorderLayout(0, 0));
			{
				jScrollPane1 = new JScrollPane();
				conterpanel.add(jScrollPane1, BorderLayout.CENTER);
				{
					jTable1 = new JTable();
					jTable1.setPreferredScrollableViewportSize(new Dimension(450, 30));
					Object[][] tableDate = null;
					jTable1.setModel(new DefaultTableModel(
						new Object[][] {
						},
						new String[] {
							"保护种类", "保护类型", "名称", "状态"
						}
					));
					jTable1.getColumnModel().getColumn(0).setMaxWidth(80);
					jScrollPane1.setViewportView(jTable1);
				}
			}
			{
				northpanel = new JPanel();
				conterpanel.add(northpanel, BorderLayout.NORTH);
				northpanel.setLayout(new BorderLayout(0, 0));
				{
					eastpanel = new JPanel();
					northpanel.add(eastpanel, BorderLayout.EAST);
					{
						jButton5 = new JButton();
						jButton5.setText("新增");
						jButton5.setFont(new Font("宋体", Font.PLAIN, 13));
						jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/add.png"))); // NOI18N
						jButton5.setToolTipText("新增");
						jButton5.setMargin(new java.awt.Insets(1,1,1,1));
						jButton5.setFocusPainted(false);
						jButton5.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton5ActionPerformed(evt);
							}
						});
						eastpanel.add(jButton5);
					}
					{
						jButton4 = new JButton();
						eastpanel.add(jButton4);
						jButton4.setText("删除");
						jButton4.setFont(new Font("宋体", Font.PLAIN, 13));
						jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource(
								"/tellhow/btnIcon/delete.png"))); // NOI18N
						jButton4.setToolTipText("\u589e\u52a0");
						//jButton4.setBorder(null);
						jButton4.setMargin(new java.awt.Insets(1,1,1,1));
						jButton4.setFocusPainted(false);
						{
							jButton6 = new JButton();
							eastpanel.add(jButton6);
							jButton6.setText("保存");
							jButton6.setFont(new Font("宋体", Font.PLAIN, 13));
							jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource(
									"/tellhow/btnIcon/save.gif"))); // NOI18N
							jButton6.setToolTipText("\u4fdd\u5b58");
							//jButton6.setBorder(null);
							jButton6.setMargin(new java.awt.Insets(1,1,1,1));
							jButton6.setFocusPainted(false);
							jButton6.addActionListener(new java.awt.event.ActionListener() {
								public void actionPerformed(java.awt.event.ActionEvent evt) {
									jButton6ActionPerformed(evt);
								}
							});
						}
						jButton4.addActionListener(new java.awt.event.ActionListener() {
							public void actionPerformed(java.awt.event.ActionEvent evt) {
								jButton4ActionPerformed(evt);
							}
						});
					}
				}
			}
			{
				southpanel = new JPanel();
				conterpanel.add(southpanel, BorderLayout.SOUTH);
				southpanel.setLayout(new BorderLayout(0, 0));
			}
		}
	}
	//修改设备名称
	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		String devName = JOptionPane.showInputDialog(this, "请输入设备名称：", jTextField1.getText());
		if (devName == null)
			return;
		else if ("".equals(devName))
			return;
		else
			devName = devName.trim();
		this.jTextField1.setText(devName);
		this.choosePd.setPowerDeviceName(devName);
		DriverPoperyDao dpd = new DriverPoperyDao();
		dpd.updateDevName(this.choosePd.getPowerDeviceID(), devName);
	}
	//修改设备类型
	private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		String flag = "";
		String pro = "PW";//配网前缀  PW;
		if (SystemConstants.Switch.equals(choosePd.getDeviceType())) {
			flag = "0";
		}
		if (SystemConstants.SwitchSeparate.equals(choosePd.getDeviceType())) {
			flag = "1";
		}
		if (SystemConstants.MotherLine.equals(choosePd.getDeviceType())) {
			flag = "2";
		}
		if (SystemConstants.InOutLine.equals(choosePd.getDeviceType())) {
			flag = "3";
		}
		if (SystemConstants.SwitchFlowGroundLine.equals(choosePd.getDeviceType())) {
			flag = "6";
		}
		if ("".equals(flag))
			return;
		//设备为配网装备，加前缀“PW”
		if(choosePd.isPW()){
			flag = pro + flag;
		}
		DeviceRuntypeChoose drc = new DeviceRuntypeChoose(
				SystemConstants.getMainFrame(), true, flag, this.jTextField5.getText());
		DictionarysModel dm = drc.chooseDM();
		if (dm != null) {
			this.jTextField5.setText(dm.getName());
//			if(choosePd.isPW()){
//				this.choosePd.setDeviceRunType(dm.getCode().replace("pw", ""));
//				this.choosePd.setDeviceSetType(dm.getCode().replace("pw", ""));
//			}else{
			this.choosePd.setDeviceRunType(dm.getCode());
//			}
			DriverPoperyDao dpd = new DriverPoperyDao();
//			if(choosePd.isPW()){
//				dm.setCode(dm.getCode().replace("pw", ""));
//			}
			dpd.updateDevRunType(this.choosePd.getPowerDeviceID(), dm.getCode());
		}
	}
	//修改设备运行方式
	private void jButton9ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		String flag = "";
		if (SystemConstants.InOutLine.equals(choosePd.getDeviceType())) {
			flag = "4";
		}
		else {
			flag = "5";
		}
		DeviceRuntypeChoose drc = new DeviceRuntypeChoose(
				SystemConstants.getMainFrame(), true, flag, this.jTextField6.getText());
		DictionarysModel dm = drc.chooseDM();
		if (dm != null) {
			this.jTextField6.setText(dm.getName());
			this.choosePd.setDeviceRunModel(dm.getCode());
			DriverPoperyDao dpd = new DriverPoperyDao();
			dpd.updateDevRunModel(this.choosePd.getPowerDeviceID(),
					dm.getCode());
			//线路接线方式对侧
			if(flag.equals("4")){
				//对侧厂站线路
				PowerDevice otheresidecircuit=null;
				List<PowerDevice> othersidecircuits=RuleExeUtil.getLineOtherSideList(choosePd);
				if(othersidecircuits.size()==0){
					return;
				}else{
					otheresidecircuit=othersidecircuits.get(0);
				}
				dpd.updateDevRunModel(otheresidecircuit.getPowerDeviceID(), dm.getCode());
				Map<String, HashMap<String, PowerDevice>>  all=CBSystemConstants.getMapPowerStationDevice();
				if(all.get(otheresidecircuit.getPowerStationID())!=null){
					all.get(otheresidecircuit.getPowerStationID()).get(otheresidecircuit.getPowerDeviceID()).setDeviceRunModel(dm.getCode());
				}
			}
		}
	}
	//许可机构
	private void jButton13ActionPerformed(java.awt.event.ActionEvent evt){
		PermitOrgan	permitOrgan=new PermitOrgan(SystemConstants.getMainFrame(),true,jTextField9,choosePd); 
	}
	//调管机构
	private void jButton12ActionPerformed(java.awt.event.ActionEvent evt){
		String flag="";
		if (SystemConstants.InOutLine.equals(choosePd.getDeviceType())) {
			flag = "4";
		}
		else {
			flag = "5";
		}
		AdjustablePipeOrgan adjustablePipeOrgan=new AdjustablePipeOrgan(SystemConstants.getMainFrame(),true,jTextField8,choosePd);
		CodeNameModel cnm = adjustablePipeOrgan.chooseCNM();
		if (cnm != null) {
			this.jTextField8.setText(cnm.getName());
			this.choosePd.setOrgaId(cnm.getCode());
			DriverPoperyDao dpd = new DriverPoperyDao();
			dpd.updateAdjustablePipeOrgan(this.choosePd, cnm);
			//线路接线方式对侧
			if(flag.equals("4")){
				//对侧厂站线路
				PowerDevice otheresidecircuit=null;
				List<PowerDevice> othersidecircuits=RuleExeUtil.getLineOtherSideList(choosePd);
				if(othersidecircuits.size()==0){
					return;
				}else{
					otheresidecircuit=othersidecircuits.get(0);
				}
				dpd.updateAdjustablePipeOrgan(otheresidecircuit, cnm);
				Map<String, HashMap<String, PowerDevice>>  all=CBSystemConstants.getMapPowerStationDevice();
				if(all.get(otheresidecircuit.getPowerStationID())!=null){
					all.get(otheresidecircuit.getPowerStationID()).get(otheresidecircuit.getPowerDeviceID()).setOrgaId(cnm.getCode());
				}
			}
		}
	}
	//监控机构
	private void jButton15ActionPerformed(java.awt.event.ActionEvent evt){
		SupervisoryOragan Soragan = new SupervisoryOragan(SystemConstants.getMainFrame(),true, jTextField10, choosePd);
	}
	//修改设备类型
	private void jButton11ActionPerformed(java.awt.event.ActionEvent evt){
		Devicetypemethods devicetypemethod=new Devicetypemethods(SystemConstants.getMainFrame(),true,jTextField7,choosePd);
	}
	//修改电压等级
	private void jButton10ActionPerformed(java.awt.event.ActionEvent evt) {
		DeviceVoltage deviceVoltage=new DeviceVoltage(SystemConstants.getMainFrame(), true, jTextField2, choosePd);
	}
	//取消
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		CBSystemConstants.svgAddPd = null;
		this.setVisible(false);
	}
	//增加每行
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.removeTableRow(jTable1);
	}
	//删除每行
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if (choosePd == null)
			return;
		WindowUtils.addTableRow(jTable1);
	}
	//保存
	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if (choosePd == null)
			return;
//		if(choosePd.isPW()){
//			choosePd.setDeviceRunType(choosePd.getDeviceRunType().replace("pw", ""));
//			choosePd.setDeviceSetType(choosePd.getDeviceSetType().replace("pw", ""));
//		}
		if (jTable1.getCellEditor() != null) {
			jTable1.getCellEditor().stopCellEditing();
		}
		int row = jTable1.getRowCount();
		List protect = new ArrayList();
		for (int i = 0; i < row; i++) {
			if(jTable1.getValueAt(i, 1) == null) {
				ShowMessage.view("第"+(i+1)+"行没有选择保护类型，不能保存！");
				return;
			}
			else if(jTable1.getValueAt(i, 2) == null) {
				ShowMessage.view("第"+(i+1)+"行没有填写保护名称，不能保存！");
				return;
			}
			else {
				if(protect.contains(jTable1.getValueAt(i, 2).toString())) {
					ShowMessage.view("第"+(i+1)+"行保护名称重复，不能保存！");
					return;
				}
				else
					protect.add(jTable1.getValueAt(i, 2).toString());
			}
		}
		delAll(choosePd.getPowerDeviceID());
		CBSystemConstants.delProtects(choosePd);
		PowerDevice pd;
		HashMap<String, PowerDevice> protectMap=CBSystemConstants.getProtect(choosePd.getPowerStationID());
		String protectID;
		String protectTypeID;
		String protectName;
		CodeNameModel protectStatusd=null;
		String protectStatus;
		for (int i = 0; i < row; i++) {
			pd= new PowerDevice();
			protectTypeID=((CodeNameModel)jTable1.getValueAt(i, 1)).getCode();
			List rs=DBManager.query("select devicetypeid from "+CBSystemConstants.opcardUser+"t_a_protectinfo where protecttypeid='"+protectTypeID+"'");
			Map mp=(Map) rs.get(0);
			String devicetype=StringUtils.ObjToString(mp.get("devicetypeid"));
			protectName = jTable1.getValueAt(i, 2).toString();
			protectID = StringUtils.getUUID();
			protectStatusd = (CodeNameModel)jTable1.getValueAt(i, 3);
			protectStatus=protectStatusd==null?"":protectStatusd.getCode();
			pd.setDevice(choosePd.getPowerDeviceID());
			pd.setPowerDeviceID(protectID);
			pd.setDeviceStatus(protectStatus);
			pd.setPowerStationID(choosePd.getPowerStationID());
			pd.setDeviceType(devicetype);
			pd.setPowerDeviceName(protectName);
			pd.setRmType(0);
			protectMap.put(protectID, pd);
			insertPro(choosePd.getPowerDeviceID(), protectID, protectName, protectTypeID,protectStatus, i+1);
		}
//		CBSystemConstants.putMapPowerStationProtects(choosePd.getPowerStationID(), protectMap);
		CBSystemConstants.putMaptoProtects(choosePd.getPowerStationID(), protectMap);
		ShowMessage.view("保存成功！");
	}
	//线路解合环---保存
	private void jButton14ActionPerformed(java.awt.event.ActionEvent evt) {
		String importline="";
		for(int index=0;index<jTable2.getRowCount();index++){
			String equip_id=(String) equip_idlist.get(index);
			CodeNameModel codenamemodel=(CodeNameModel) jTable2.getValueAt(index, 2);
			importline=codenamemodel.getCode();
			String sql="update "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO t set t.FLOWINFO='"+importline+"' where t.EQUIP_ID='"+equip_id+"'";
			int count = DBManager.update(sql);
			if(count == 0) {
				sql="insert into "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO(EQUIP_ID,FLOWINFO) values ('"+equip_id+"','"+importline+"')";
				DBManager.update(sql);
			}
		}
		ShowMessage.view("保存成功！");
	}
	private void delAll(String powerDeviceID) {
		DBManager.update("delete from "+CBSystemConstants.opcardUser+"T_A_PROTECTEQUIP where EQUIPID=?", new Object[] { powerDeviceID });
	}
	private String getProtectName(String protectTypeID) {
		List<Object> list = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"t_a_protectinfo t where t.protecttypeid=?",protectTypeID);
		String protectTypeName="";
		if(list.size()==1){
			for(Iterator iter = list.iterator();iter.hasNext();) {
				Map map = (Map) iter.next();
				protectTypeName = StringUtils.ObjToString(map.get("PROTECTTYPENAME"));
			}
		}
		return protectTypeName;
	}
	private void insertPro(String powerDeviceID, String protectID,String powerDeviceName,
			String protectTypeID, String status, int order) {
		DBManager.update("insert into "+CBSystemConstants.opcardUser+"T_A_PROTECTEQUIP(PROTECTID,EQUIPID,PROTECTNAME,PROTECTTYPEID,PROTECTSTATUS,ORDERKEY) values(?,?,?,?,?,?)",new Object[] {protectID, powerDeviceID, powerDeviceName, protectTypeID, status, order });
	}
	/**
	 * 根据设备id从数据库获得设备保护类型
	 * 
	 * */
	private List<Object[]> getProTypeById(String id) {
		List<Object[]> str = new ArrayList<Object[]>();
		List results = DBManager.query("select b.protectkind,decode(b.protectkind,'0','主保护','1','备保护') protectkindname,b.protecttypeid,b.protecttypename,a.protectname,a.protectstatus,a.protectid from "+CBSystemConstants.opcardUser+"t_a_protectequip a,"+CBSystemConstants.opcardUser+"t_a_protectinfo b where a.protecttypeid=b.protecttypeid and a.equipid='"+id+"' order by a.orderkey");
		for (int i = 0; i < results.size(); i++) {
			Map temp = (Map) results.get(i);
			String protecttypeid=temp.get("protecttypeid").toString();
			CodeNameModel m1 = new CodeNameModel(temp.get("protectkind").toString(), temp.get("protectkindname").toString());
			CodeNameModel m2 = new CodeNameModel(temp.get("protecttypeid").toString(), temp.get("protecttypename").toString());
			String protectName = temp.get("protectname").toString();
			String protectstatus=temp.get("protectstatus").toString();
			String protectid=temp.get("protectid").toString();
			List r1=DBManager.query("select devicetypeid from "+CBSystemConstants.opcardUser+"t_a_protectinfo where protecttypeid='"+protecttypeid+"'");
			Map r1m=(Map) r1.get(0);
			String devicetypeid=r1m.get("devicetypeid").toString();
//			List r2=DBManager.query("select equiptype_code from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='"+devicetypeid+"'");
			//edit 2014.6.25
			List r2=DBManager.query(OPEService.getService().DriverPorpetySql1()+devicetypeid+"'");
			Map r2m=(Map) r2.get(0);
			String equiptype_code=r2m.get("equiptype_code").toString();
//			List r3=DBManager.query("select b.state_name, b.state_code from "+CBSystemConstants.opcardUser+"t_a_protectequip a,"+CBSystemConstants.opcardUser+"t_e_equiptypestate b where a.protectid='"+protectid+"' and a.equipid='"+id+"' and b.equiptype_id='"+equiptype_code+"' and a.protectstatus=b.state_code");
			//edit 2014.6.25
			String state_name = "";
			String state_code = "";
			List r3=DBManager.query(OPEService.getService().DriverPorpetySql2()+protectid+"' and a.equipid='"+id+"' and b.equiptype_id='"+equiptype_code+"' and a.protectstatus=b.state_code");
			if(r3.size() > 0) {
				Map r3m=(Map) r3.get(0);
				state_name=r3m.get("state_name").toString();
				state_code=r3m.get("state_code").toString();
			}
			CodeNameModel m3=new CodeNameModel(state_code, state_name);
			str.add(new Object[]{m1, m2, protectName,m3});
		}
		return str;
	}
	class CustomComboBoxEditor extends DefaultCellEditor {
		private DefaultComboBoxModel model;
		private List<CodeNameModel> protectType;
//		private List<CodeNameModel> InvestmentType;
		private List<CodeNameModel> primaryProtect;
		private List<CodeNameModel> backupProtect;
	    public CustomComboBoxEditor(){
	    	super(new JComboBox());
	    	this.model = (DefaultComboBoxModel)((JComboBox)getComponent()).getModel();
	    	protectType = new ArrayList<CodeNameModel>();
//	    	InvestmentType = new ArrayList<CodeNameModel>();
	    	protectType.add(new CodeNameModel("0", "主保护"));
	    	protectType.add(new CodeNameModel("1", "备保护"));
//	    	InvestmentType.add(new CodeNameModel("0","退出"));
//	    	InvestmentType.add(new CodeNameModel("1","投入"));
	    	//InvestmentType.add(new CodeNameModel("2","其他"));
	    	primaryProtect = dao.getProtectByType(choosePd,"0");
	    	backupProtect = dao.getProtectByType(choosePd,"1");
	    	
	    	
	    }
	    public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
	    	if(column == 0) {
	    		if(model.getSize() == 0) {
			    	for(int i=0;i<protectType.size();i++){
			    		CodeNameModel cnm=protectType.get(i);
			    		model.addElement(cnm);
			    	}
	    		}
	    		final int r = row;
	    		final String srcType = jTable1.getValueAt(row, 0)==null?"":jTable1.getValueAt(row, 0).toString();
	    		((JComboBox)getComponent()).removeItemListener(((JComboBox)getComponent()).getItemListeners()[0]);
	    		((JComboBox)getComponent()).addItemListener(new java.awt.event.ItemListener() {
					public void itemStateChanged(java.awt.event.ItemEvent evt) {
						if(evt.getStateChange() == java.awt.event.ItemEvent.SELECTED){
							String tagType = model.getSelectedItem().toString();
							if(!tagType.equals(srcType))
								jTable1.setValueAt(null, r, 1);
						}
					}
				});		    	
	    	}
	    	else if(column == 1) {
	    		model.removeAllElements();
		    	if(jTable1.getValueAt(row, 0) != null) {
			    	String type = ((CodeNameModel)jTable1.getValueAt(row, 0)).getCode();
					List<CodeNameModel> protects = null;
					if(type.equals("0"))
						protects = primaryProtect;
					else if(type.equals("1"))
						protects = backupProtect;
			    	for(int i=0;i<protects.size();i++){
			    		CodeNameModel cnm=protects.get(i);
			    		model.addElement(cnm);
			    	}
		    	}
	    	}
	    	else if(column == 3){
	    		model.removeAllElements();
	    		List<CodeNameModel> Investments = new ArrayList<CodeNameModel>();
		    	if(jTable1.getValueAt(row, 0) != null) {
			    	String type = ((CodeNameModel)jTable1.getValueAt(row, 0)).getCode();
			    	String ss=((CodeNameModel)jTable1.getValueAt(row, 1)).getCode();
			    	List r1=DBManager.query("select devicetypeid from "+CBSystemConstants.opcardUser+"t_a_protectinfo where protecttypeid='"+ss+"'");
					Map r1m=(Map) r1.get(0);
					String devicetypeid=r1m.get("devicetypeid").toString();
//					List r2=DBManager.query("select equiptype_code from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='"+devicetypeid+"'");
					//edit 2014.6.25
					List r2=DBManager.query(OPEService.getService().DriverPorpetySql3()+devicetypeid+"'");
					Map r2m=(Map) r2.get(0);
					String equiptype_code=r2m.get("equiptype_code").toString();
//					List r3=DBManager.query("select b.state_name ,b.state_code from "+CBSystemConstants.opcardUser+"t_e_equiptypestate b where  b.equiptype_id='"+equiptype_code+"'");
					//edit 2014.6.25
					List r3=DBManager.query(OPEService.getService().DriverPorpetySql4()+equiptype_code+"'");
					for(int i=0;i<r3.size();i++){
						Map r3m=(Map) r3.get(i);
						CodeNameModel cm=new CodeNameModel();
						String state_name=r3m.get("state_name").toString();
						String state_code=r3m.get("state_code").toString();
						cm.setCode(state_code);
						cm.setName(state_name);
						Investments.add(cm);
					}
					
			    	for(int i=0;i<Investments.size();i++){
			    		CodeNameModel cnm=Investments.get(i);
			    		model.addElement(cnm);
			    	}
		    	}
	    	}
	    	return super.getTableCellEditorComponent(table, value, isSelected, row, column);
	    }
	}
	private JButton jButton3;
	private JButton jButton4;
	private JButton jButton5;
	private JButton jButton6;
	private javax.swing.JButton jButton7;
	private javax.swing.JButton jButton8;
	private javax.swing.JButton jButton9;
	private javax.swing.JButton jButton10;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JLabel jLabel3;
	private javax.swing.JLabel jLabel4;
	private javax.swing.JLabel jLabel5;
	private javax.swing.JLabel jLabel6;
	
	private javax.swing.JTable jTable1;
	private javax.swing.JScrollPane jScrollPane1;
	private JScrollPane jScrollPane2;
	private JPanel northpanel;
	private JPanel eastpanel;
	private JPanel southpanel;
	private JPanel panel_4;
	private JLabel lblNewLabel;
	private JButton jButton11;
	private JPanel panel_3;
	private JButton jButton12;
	private JLabel jLabel7;
	private JTextField jTextField8;
	private JPanel panel_5;
	private JButton jButton13;
	private JLabel jLabel8;
	private JLabel jLabel9;
	private JTextField jTextField9;
	private JTextField jTextField10;
	private JPanel buttonPane;
	private JPanel panel_6;
	private JButton jButton14;
	private JButton jButton15;
	private JPanel panel_7;
}
