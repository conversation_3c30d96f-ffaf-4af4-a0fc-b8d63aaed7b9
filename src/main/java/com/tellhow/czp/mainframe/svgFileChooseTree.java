package com.tellhow.czp.mainframe;

import java.awt.EventQueue;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.FilenameFilter;
import java.util.ArrayList;
import java.util.List;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JScrollPane;
import javax.swing.JTextField;
import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.TreePath;
import com.tellhow.graphicframework.constants.SystemConstants;

public class svgFileChooseTree {

	private JFrame frame;
	private JTextField textField;
	private JScrollPane scrollPane;
	private JTree tree;
	public String returnFileName = "" ;
	

	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		EventQueue.invokeLater(new Runnable() {
			public void run() {
				try {
					svgFileChooseTree window = new svgFileChooseTree();
					window.frame.setVisible(true);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		});
	}

	/**
	 * Create the application.
	 */
	public svgFileChooseTree() {
		initialize();
		tree = bulidTree();
	}
	
	public String bulidsvgFileTree(){
		this.frame.setVisible(true);
		return this.returnFileName;
	}

	/**
	 * Initialize the contents of the frame.
	 */
	private void initialize() {
		frame = new JFrame();
		frame.setBounds(100, 100, 302, 435);
		frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
		frame.getContentPane().setLayout(null);
		
		textField = new JTextField();
		textField.setBounds(10, 10, 201, 28);
		frame.getContentPane().add(textField);
		textField.setColumns(10);
		
		JButton btnNewButton = new JButton("\u67E5\u8BE2");
		btnNewButton.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent arg0) {
				tree = bulidTree();
			}
		});
		btnNewButton.setBounds(219, 9, 57, 28);
		frame.getContentPane().add(btnNewButton);
		
		scrollPane = new JScrollPane();
		scrollPane.setBounds(10, 48, 266, 322);
		frame.getContentPane().add(scrollPane);
		
		tree = new JTree();
		scrollPane.setColumnHeaderView(tree);
		
	}
	
	
	
	private JTree bulidTree(){
        DefaultMutableTreeNode rootNode = new DefaultMutableTreeNode("SVG单线图");   //创建根节点     
	    DefaultMutableTreeNode newNode;   //为每个序号新建一个树节点
        String filterSvgName = this.textField.getText().trim();
	    
	    File file = new File(SystemConstants.FILE_SVGMAP_PATH);
		List<File> fileList = listFileWhole(file);
		File svgFile = null;
		String fileName = "";
		for (int i = 0; i < fileList.size(); i++) {
			svgFile = fileList.get(i);
			fileName = svgFile.getName();
			if(!"".equals(filterSvgName)&&fileName.indexOf(filterSvgName) < 0){
				continue;
			}
	    	newNode = new DefaultMutableTreeNode(fileName);
			rootNode.add(newNode);
		}
        JTree fileTree =  new JTree(rootNode);
        fileTree.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && e.getButton() == 1) {
                    JTree tree = (JTree) e.getSource();
                    TreePath simplePath = tree.getSelectionPath();
                    DefaultMutableTreeNode lastNode = (DefaultMutableTreeNode) simplePath.getLastPathComponent();
                    if (lastNode.isLeaf()) {
                    	returnFileName = lastNode.toString();
                    	frame.setVisible(false);
                    }
                }
            }   
        });
        return fileTree;
	}

	public static List<File> listFileWhole(File f) {
		List<File> fileList = new ArrayList<File>();
		File[] files = f.listFiles(new FilenameFilter() {
			public boolean accept(File dir, String name) {
				if (name.endsWith(".svg")) {
					return true;
				}
				return false;
			}
		});
		for(File file:files){
			fileList.add(file);
		}
		return fileList;
	}
}
