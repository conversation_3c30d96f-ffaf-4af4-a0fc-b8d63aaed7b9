package com.tellhow.czp.mainframe;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.Toolkit;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;

import com.tellhow.czp.mainframe.dao.DriverPoperyDao;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;

public class DeviceVoltage extends javax.swing.JDialog implements ItemListener{
	private JComboBox comboBox = new JComboBox();
	private final JPanel contentPanel2 = new JPanel();
	private PowerDevice choosePd = null;
	private JTextField jTextField2=null;
	public DeviceVoltage(java.awt.Frame parent, boolean modal,JTextField jTextField2,PowerDevice pd) {
		super(parent, modal);
		this.choosePd=pd;
		this.jTextField2=jTextField2;
		this.setTitle("修改电压等级");
		initComponents();//初始化组件
		comboBox.addItemListener(this);
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width),
				(h - this.getSize().height) / 2);
		this.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
		this.setVisible(true);
	}
	public void initComponents(){
		// TODO Auto-generated method stub
		this.setBounds(100, 100, 301, 122);
		this.getContentPane().setLayout(new BorderLayout());
		contentPanel2.setBorder(new EmptyBorder(5, 5, 5, 5));
		this.getContentPane().add(contentPanel2, BorderLayout.CENTER);
		contentPanel2.setLayout(new BorderLayout(0, 0));
		JPanel panel = new JPanel();
		contentPanel2.add(panel, BorderLayout.NORTH);
		panel.setLayout(new BorderLayout(0, 0));
		JPanel voltagepanel_1 = new JPanel();
		panel.add(voltagepanel_1, BorderLayout.SOUTH);
		JLabel voltageLabel = new JLabel("电压等级：");
		voltageLabel.setPreferredSize(new Dimension(60, 15));
		voltagepanel_1.add(voltageLabel);
		comboBox.setPreferredSize(new Dimension(150, 21));
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel codeNameModel=new CodeNameModel();
		codeNameModel.setCode("500");
		codeNameModel.setName("500KV");
		CodeNameModel codeNameModel2=new CodeNameModel();
		codeNameModel2.setCode("220");
		codeNameModel2.setName("220KV");
		CodeNameModel codeNameModel3=new CodeNameModel();
		codeNameModel3.setCode("110");
		codeNameModel3.setName("110KV");
		CodeNameModel codeNameModel4=new CodeNameModel();
		codeNameModel4.setCode("35");
		codeNameModel4.setName("35KV");
		CodeNameModel codeNameModel5=new CodeNameModel();
		codeNameModel5.setCode("10");
		codeNameModel5.setName("10KV");
		model.addElement(codeNameModel);
		model.addElement(codeNameModel2);
		model.addElement(codeNameModel3);
		model.addElement(codeNameModel4);
		model.addElement(codeNameModel5);
		comboBox.setModel(model);
		//JTextField  textfield= (JTextField) evt.getSource();
		//String text=textfield.getText();
		//CodeNameModel codenamemodel=new CodeNameModel();
		//codenamemodel.setCode(StringUtils.getSwitchCode(text));
		//codenamemodel.setName(text);
		//model.setSelectedItem(codenamemodel);
		CodeNameModel codenamemodel=new CodeNameModel();
		int voltgrade=(int) choosePd.getPowerVoltGrade();
		codenamemodel.setCode(""+voltgrade);
		codenamemodel.setName(""+voltgrade);
		model.setSelectedItem(codenamemodel);
		voltagepanel_1.add(comboBox);
		JPanel panel_2 = new JPanel();
		panel.add(panel_2, BorderLayout.NORTH);
		JPanel panel_3 = new JPanel();
		panel.add(panel_3, BorderLayout.WEST);
	}
	@Override
	public void itemStateChanged(ItemEvent e) {
		// TODO Auto-generated method stub
		if(e.getStateChange() == ItemEvent.SELECTED){
			JComboBox combobox=(JComboBox) e.getSource();
			CodeNameModel codeNameModel=(CodeNameModel) combobox.getSelectedItem();
			jTextField2.setText(codeNameModel.getName());
			DriverPoperyDao dpd = new DriverPoperyDao();
			dpd.updateDevVoltage(this.choosePd, codeNameModel);
			this.setVisible(false);
		}
	
	}
}
