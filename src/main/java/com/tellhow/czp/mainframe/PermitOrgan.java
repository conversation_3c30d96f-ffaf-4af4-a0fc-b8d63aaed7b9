package com.tellhow.czp.mainframe;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.Toolkit;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.mainframe.dao.DriverPoperyDao;
import com.tellhow.czp.staticsql.OpeInfo;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 许可机构类
 * 作    者: 王豪
 * 开发日期: 2013年12月30日 上午10:32:14 
 */
public class PermitOrgan extends javax.swing.JDialog implements ItemListener{
	//许可机构
	private JComboBox comboBox = new JComboBox();
	private final JPanel contentPanel2 = new JPanel();
	private JTextField jTextField9=null;
	private PowerDevice choosePd = null;
	public PermitOrgan(){}
	public PermitOrgan(java.awt.Frame parent, boolean modal,JTextField jTextField9,PowerDevice choosePd) {
		super(parent, modal);
		this.jTextField9=jTextField9;
		this.choosePd=choosePd;
		this.setTitle("许可机构修改");
		initComponents();
		comboBox.addItemListener(this);
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width),
				(h - this.getSize().height) / 2);
		this.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
		this.setVisible(true);
	}
	@Override
	public void itemStateChanged(ItemEvent e) {
		// TODO Auto-generated method stub
		if(e.getStateChange() == ItemEvent.SELECTED){
			JComboBox combobox=(JComboBox) e.getSource();
			CodeNameModel codeNameModel=(CodeNameModel) combobox.getSelectedItem();
			jTextField9.setText(codeNameModel.getName());
			DriverPoperyDao dpd = new DriverPoperyDao();
			dpd.updatePermitOrgan(this.choosePd, codeNameModel);
			this.setVisible(false);
		}
	}
	public void initComponents(){
		// TODO Auto-generated method stub
		this.setBounds(100, 100, 301, 122);
		this.getContentPane().setLayout(new BorderLayout());
		contentPanel2.setBorder(new EmptyBorder(5, 5, 5, 5));
		this.getContentPane().add(contentPanel2, BorderLayout.CENTER);
		contentPanel2.setLayout(new BorderLayout(0, 0));
		JPanel panel = new JPanel();
		contentPanel2.add(panel, BorderLayout.NORTH);
		panel.setLayout(new BorderLayout(0, 0));
		JPanel voltagepanel_1 = new JPanel();
		panel.add(voltagepanel_1, BorderLayout.SOUTH);
		JLabel voltageLabel = new JLabel("许可机构：");
		voltageLabel.setPreferredSize(new Dimension(60, 15));
		voltagepanel_1.add(voltageLabel);
		comboBox.setPreferredSize(new Dimension(150, 21));
		
		//许可机构下拉框选值
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=new CodeNameModel("", "");
		model.addElement(cnm);
		for(Iterator it = CBSystemConstants.getMapPowerOrgan().values().iterator();it.hasNext();) {
			PowerDevice organ = (PowerDevice)it.next();
			if(organ.getPowerDeviceName().indexOf("调") == -1)
				continue;
			CodeNameModel codeNameModel=new CodeNameModel(organ.getPowerDeviceID(),organ.getPowerDeviceName());
			model.addElement(codeNameModel);
			//对话框一开始选中值
			if(!choosePd.getPermissionOrganID().equals("") && choosePd.getPermissionOrganID().equals(organ.getPowerDeviceID()))
				model.setSelectedItem(codeNameModel);
		}
		comboBox.setModel(model);
		voltagepanel_1.add(comboBox);
		JPanel panel_2 = new JPanel();
		panel.add(panel_2, BorderLayout.NORTH);
		JPanel panel_3 = new JPanel();
		panel.add(panel_3, BorderLayout.WEST);
	}
}
