package com.tellhow.czp.mainframe.menu;

//显示测量
import java.awt.GridLayout;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.Map;

import javax.swing.BorderFactory;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;

import com.tellhow.czp.app.service.EMSService;

public class MeasurementInfoMenuProvider {

	private String relatedID = "";
	private String relatedField;
	private String measID = "";
	private String measType = ""; //遥测 0 遥信 1

	
	public MeasurementInfoMenuProvider(String measID, String measType) {
		this.measID = measID;
		this.measType = measType;
	}

	public JPopupMenu createMenu() {
		final JPopupMenu popupMenu = new JPopupMenu();
		JPanel infoPanel = new JPanel();
		JPanel infoPanel1 = new JPanel();
		JPanel infoPanel2 = new JPanel();

		infoPanel.setBorder(BorderFactory.createTitledBorder(null, "量测信息",
				javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
				javax.swing.border.TitledBorder.DEFAULT_POSITION,
				new java.awt.Font("宋体", 1, 12)));
		infoPanel.addMouseListener(new MouseAdapter() {

			public void mouseClicked(MouseEvent e) {
				popupMenu.setVisible(false);
			}
		});

		GridLayout gridLayout = new GridLayout(0, 1);
		infoPanel1.setLayout(gridLayout);
		infoPanel2.setLayout(gridLayout);
		JLabel measureNameLabel = new JLabel("名称：");
		//JLabel measureTypeLabel = new JLabel("类型：");
		String dataName = measType.equals("0")?"数值":"状态";
		JLabel measureValueLabel = new JLabel(dataName+"：");

		// CommonSearch cs=new CommonSearch();
		// Map<String,Object> inPara = new HashMap<String,Object>();
		// Map<String,Object> outPara = new HashMap<String,Object>();
		// inPara.put("oprSrcDevice", pd);
		// inPara.put("isSearchOffPath", "false"); //搜索闭合通路
		// inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
		// inPara.put("excDevType", SystemConstants.PowerTransformer); //排除主变
		// cs.execute(inPara, outPara);
		// List searchDevs = (ArrayList) outPara.get("linkedDeviceList");
		// Map allPath = (HashMap) outPara.get("allPathList");
		// List excDevs=new ArrayList();
		// excDevs.add(pd);
		// DeviceSearchManager dsm=new DeviceSearchManager();
		// List devs=dsm.getMotherLinesByML(pd,excDevs,true);
		
		String name="";
		String value="";
		Map map = null;
		if(measType.equals("0"))
			map = EMSService.getService().getMeasAnalogData(measID);
		else if(measType.equals("1"))
			map = EMSService.getService().getMeasPointData(measID);
		if(map != null && map.size() > 0) {
			name = map.get("name").toString();
			value = map.get("value").toString();
		}
		/*
		//从数据库获得测量信息
		List<Map> list = PowerSystemDBOperator.getMeasure(relatedID,relatedField);
		if(list==null||list.size()==0){
			return popupMenu;
		}
		Map map = list.get(0);
		String name = (String) map.get("measname");
		//String type = (String) map.get("meastype");
		String value = (String) map.get("measvalue");
		*/
		JLabel measureName = new JLabel(name);
		//JLabel measureType = new JLabel(type);
		JLabel measurevalue = new JLabel(value);

		infoPanel1.add(measureNameLabel);
		infoPanel2.add(measureName);
		//infoPanel1.add(measureTypeLabel);
		//infoPanel2.add(measureType);
		infoPanel1.add(measureValueLabel);
		infoPanel2.add(measurevalue);

		infoPanel.add(infoPanel1);
		infoPanel.add(infoPanel2);
		popupMenu.add(infoPanel);
		return popupMenu;
	}

}
