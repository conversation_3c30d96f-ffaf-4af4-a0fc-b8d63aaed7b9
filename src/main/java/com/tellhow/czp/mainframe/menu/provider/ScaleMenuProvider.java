package com.tellhow.czp.mainframe.menu.provider;

import javax.swing.JPopupMenu;

import org.w3c.dom.Element;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.graphicframework.menu.SvgCanvasMenuProvider;
import com.tellhow.graphicframework.svg.SVGCanvas;

import czprule.model.PowerDevice;

public class ScaleMenuProvider implements SvgCanvasMenuProvider{
	
	public ScaleMenuProvider() {
		
	}
	
	public static ScaleMenuProvider instance;
	
	public static ScaleMenuProvider getInstance(SVGCanvas svgCanvas, PowerDevice pd){
		if(instance == null){
			instance=(ScaleMenuProvider)CZPImpl.getInstance("ScaleMenuProvider");
			if(instance == null)
				instance = new ScaleMenuProviderDefault();
				return instance;
		}
		else
			return instance;
	}

	@Override
	public JPopupMenu createMenu() {
		// TODO Auto-generated method stub
		return null;
	}
	
	public JPopupMenu createMenu(Element ele) {
		// TODO Auto-generated method stub
		return null;
	}
}
