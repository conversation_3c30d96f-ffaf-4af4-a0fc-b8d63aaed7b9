package com.tellhow.czp.mainframe.menu.provider;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.geom.AffineTransform;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPopupMenu;

import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.tellhow.graphicframework.action.impl.ShowSvgLayerAction;
import com.tellhow.graphicframework.basic.LineLinkCache;
import com.tellhow.graphicframework.basic.LinkEquipVO;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.menu.annotation.MenuProvider;
import com.tellhow.graphicframework.menu.provider.DefaultMenuProvider;
import com.tellhow.graphicframework.model.ConnectivityNode;
import com.tellhow.graphicframework.model.EquipTerminal;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.DOMUtil;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

@MenuProvider(id="ScaleMenuProvider")
public class ModelMenuProvider extends DefaultMenuProvider {
	private SVGCanvas fSvgCanvas;
	public ModelMenuProvider(SVGCanvas svgCanvas, Element element) {
		super(element);
		this.fSvgCanvas = svgCanvas;
	}
	
	@Override
	public JPopupMenu createMenu() {
		JPopupMenu popupMenu = new JPopupMenu();
		JMenuItem menuItem = new JMenuItem("编辑模型");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				setLinkData(fElement.getOwnerDocument());
			}
		});
		popupMenu.add(menuItem);
		
		
		return popupMenu;
	}
	
	private static void setLinkData(Document doc) {
		
		HashMap<String, HashMap<String, ConnectivityNode>> nodeMap = new HashMap<String, HashMap<String, ConnectivityNode>>();
		
		HashMap<String, HashMap<String, EquipTerminal>> terminalMap = new HashMap<String, HashMap<String, EquipTerminal>>();
		
		LineLinkCache lineLinkCache = new LineLinkCache();
		//Map<String, Map<String, LinkEquipVO>> equipLinkMap = SVGToolkit.getEquipLinkMap(doc);
		Map<String, Map<String, LinkEquipVO>> equipLinkMap = SystemConstants.getMapEquipLink().get(doc);
		lineLinkCache.setEquipLinkMap(equipLinkMap);

		
			HashMap<String, ConnectivityNode> newNodeMap = new HashMap<String, ConnectivityNode>();
			HashMap<String, EquipTerminal> newTerminalMap = new HashMap<String, EquipTerminal>();
			String stationID = DOMUtil.getStationID(doc);
			if(!nodeMap.containsKey(stationID))
				nodeMap.put(stationID, new HashMap<String, ConnectivityNode>());
			if(!terminalMap.containsKey(stationID))
				terminalMap.put(stationID, new HashMap<String, EquipTerminal>());
			List<String> searchedLine = new ArrayList<String>();
			for (Iterator iter = equipLinkMap.entrySet().iterator();iter.hasNext();) {
				Map.Entry entry = (Map.Entry) iter.next();
				String lineElementID = (String)entry.getKey();
				if(searchedLine.contains(lineElementID))
					continue;	
				List<LinkEquipVO> linkEquipList = lineLinkCache.getLinkEquipForLine(doc, lineElementID, searchedLine);
				if(linkEquipList.size() >= 2) {
					String nodeID = StringUtils.getUUID();
					String voltegeID = "";
					for (int i=0; i< linkEquipList.size(); i++) {
						LinkEquipVO linkEquipVO = linkEquipList.get(i);
						Element equipElement = doc.getElementById(linkEquipVO.getT_id());
						String equipID =DOMUtil.getEquipID(equipElement);
						
						PowerDevice equip = CBSystemConstants.getPowerDevice(stationID, equipID);
						
						if(equip != null) {
							if(!equip.isPW())
								continue;
							if(voltegeID.equals("") && !equip.getDeviceType().equals("PowerTransformer"))
								voltegeID = StringUtils.getUUID();//
							removeTerminal(nodeMap.get(stationID), terminalMap.get(stationID), equipID);
							EquipTerminal terminal = new EquipTerminal(StringUtils.getUUID(),getNewTerminalName(newTerminalMap,equip.getPowerDeviceName()),equipID,nodeID);
							newTerminalMap.put(terminal.getTerminal_id(), terminal);
						}
					}
					if(!voltegeID.equals("")) {
						ConnectivityNode node = new ConnectivityNode(nodeID, stationID, voltegeID);
						newNodeMap.put(node.getConnectivitynode_id(), node);
					}
				}
			}
			nodeMap.put(stationID, newNodeMap);
			terminalMap.put(stationID, newTerminalMap);
			//saveStationTerminal(stationID, newTerminalMap);
		
	}
	
	public void saveStationNode(String stationID, HashMap<String, ConnectivityNode> stationNodeMap) {
		//String sql = "delete from "+CBSystemConstants.opcardUser+"t_e_connectivitynode t where t.station_id=?";
		//DBManager.update(sql, new Object[]{stationID});
//		String sql = "insert into EQUIP.T_C_CONNECTIVITYNODE (id,station_id,voltagelvl) values(?,?,?)";
//		List parameterList = new ArrayList();
//		for(Iterator iterator = stationNodeMap.values().iterator();iterator.hasNext();)
//		{
//			ConnectivityNode node = (ConnectivityNode)iterator.next();
//			Object[] parameters = new Object[]{node.getConnectivitynode_id(),node.getStation_id(),node.getVoltage_id()};
//			parameterList.add(parameters);
//		}
//		DBManager.update(sql, parameterList);
	}
	
	public static void saveStationTerminal(String stationID, HashMap<String, EquipTerminal> stationTerminalMap) {
		
		
		String equipIDs = "";
		for(Iterator iterator = stationTerminalMap.values().iterator();iterator.hasNext();)
		{
			EquipTerminal terminal = (EquipTerminal)iterator.next();
			equipIDs = equipIDs + "'" + terminal.getEquip_id() +"',";
		}
		if(!equipIDs.equals(""))
			equipIDs = equipIDs.substring(0, equipIDs.length()-1);
		String sql = "delete from "+CBSystemConstants.equipUser+"T_C_TERMINAL t where t.equip_id in ("+equipIDs+")";
		DBManager.update(sql);
		
		
		
		
		List parameterList = new ArrayList(); 
		sql = "insert into "+CBSystemConstants.equipUser+"T_C_TERMINAL (id,name,equip_id,connectivitynode_id) values(?,?,?,?)";
		
		for(Iterator iterator = stationTerminalMap.values().iterator();iterator.hasNext();)
		{
			EquipTerminal terminal = (EquipTerminal)iterator.next();
			Object[] parameters = new Object[]{terminal.getTerminal_id(),terminal.getTerminal_name(),terminal.getEquip_id(),terminal.getConnectivitynode_id()};
			parameterList.add(parameters);
		}
		DBManager.update(sql, parameterList);
	}
	
	public static String getNewTerminalName(HashMap<String, EquipTerminal> stationTerminalMap, String equipName) {
		int index = 1;
		String terminalName = equipName + "_T" + index;
		while(isTerminalNameExist(stationTerminalMap,terminalName)) {
			index++;
			terminalName = equipName + "_T" + index;
		}
		return terminalName;
	}
	
	public static boolean isTerminalNameExist(HashMap<String, EquipTerminal> stationTerminalMap, String terminalName) {
		boolean isExist = false;
		for(Iterator iter = stationTerminalMap.values().iterator(); iter.hasNext();) {
			EquipTerminal terminal = (EquipTerminal)iter.next();
			if(terminal.getTerminal_name().equals(terminalName)) {
				isExist = true;
				break;
			}
		}
		return isExist;
	}
	
	public static void removeTerminal(HashMap<String, ConnectivityNode> nodeMap, HashMap<String, EquipTerminal> terminalMap, String equipID) {
		for(Iterator iter = terminalMap.values().iterator(); iter.hasNext();) {
			EquipTerminal terminal = (EquipTerminal)iter.next();
			if(terminal.getEquip_id().equals(equipID)) {
				nodeMap.remove(terminal.getConnectivitynode_id());
				iter.remove();
			}
		}
	}
	
}

