package com.tellhow.czp.mainframe.menu;
import java.awt.GridLayout;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.BorderFactory;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprulepw.PWSystemConstants;


public class DeviceInfoMenuProvider{

	private PowerDevice pd=null;
	public DeviceInfoMenuProvider(PowerDevice pd) {
		this.pd=pd;
	}
	
	public JPopupMenu createMenu() {
		final JPopupMenu popupMenu = new JPopupMenu();
        JPanel infoPanel = new JPanel();
        JPanel infoPanel1 = new JPanel();
        JPanel infoPanel2 = new JPanel();
        
        infoPanel.setBorder(BorderFactory.createTitledBorder(null, "设备信息", javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION, javax.swing.border.TitledBorder.DEFAULT_POSITION, new java.awt.Font("宋体", 1, 12)));
        infoPanel.addMouseListener(new MouseAdapter() {

            public void mouseClicked(MouseEvent e) {
            	popupMenu.setVisible(false);
            }
        });
        
        GridLayout gridLayout = new GridLayout(0,1);
        infoPanel1.setLayout(gridLayout);
        infoPanel2.setLayout(gridLayout);
        JLabel pdNameLabel = new JLabel("设备名称：");
        JLabel pdStatusLabel = new JLabel("设备状态：");  
        JLabel pdTypeLabel = new JLabel("设备类型：");
        JLabel pdRunModelLabel = new JLabel("接线方式：");  
        JLabel pdOrganLabel = new JLabel("调度机构：");
        
        
     

        
        
        
        
//		CommonSearch cs=new CommonSearch();
//        Map<String,Object> inPara = new HashMap<String,Object>();
//        Map<String,Object> outPara = new HashMap<String,Object>();
//        inPara.put("oprSrcDevice", pd);
// 	    inPara.put("isSearchOffPath", "false"); //搜索闭合通路
//	    inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
//	    inPara.put("excDevType", SystemConstants.PowerTransformer); //排除主变  
//	    cs.execute(inPara, outPara);
//	    List searchDevs = (ArrayList) outPara.get("linkedDeviceList");
//	    Map allPath = (HashMap) outPara.get("allPathList");
//        List excDevs=new ArrayList();
//        excDevs.add(pd);
//        DeviceSearchManager dsm=new DeviceSearchManager();
//        List devs=dsm.getMotherLinesByML(pd,excDevs,true);
        
        String lineID = "";
        if(pd.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)) {
			List<PowerDevice> linkdevList = RuleExeUtil.getDeviceDirectList(pd, "");
			for(PowerDevice linkdev  : linkdevList) {
				if(linkdev.isPW()) {
					lineID = linkdev.getPowerStationID();
					break;
				}
			}
			if(lineID.equals("")) {
				for(PowerDevice linkdev  : linkdevList) {
					List<PowerDevice> linklinkdevList = RuleExeUtil.getDeviceDirectList(linkdev, "");
					for(PowerDevice linklinkdev  : linklinkdevList) {
						if(linklinkdev.isPW()) {
							lineID = linklinkdev.getPowerStationID();
							break;
						}
					}
				}
			}
		}
        
        
       // JLabel pdName = new JLabel(pd.getPowerDeviceName()+"_"+pd.getPowerDeviceID()+"_"+pd.getPowerStationID()+"_"+lineID);
        JLabel pdName = new JLabel(pd.getPowerDeviceName());
        String deviceruntype = "";
        if(pd.isPW() && pd.getDeviceType().equals(SystemConstants.Switch))
        	deviceruntype=CBSystemConstants.getDictionaryName(pd.getDeviceSetType(),"PW"+pd.getDeviceType());
        else
        	deviceruntype=CBSystemConstants.getDictionaryName(pd.getDeviceSetType(),pd.getDeviceType());
        if(deviceruntype.equals(""))
        	deviceruntype = SystemConstants.getMapEquipType().get(pd.getDeviceType());
        if(pd.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchML) && !RuleExeUtil.isSwitchDoubleML(pd))
        	deviceruntype = "分段开关";	
        if(pd.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchMLPL) && 
        		!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL))
        	deviceruntype = deviceruntype + "(" + CBSystemConstants.getDictionaryName(pd.getDeviceRunType(),pd.getDeviceType())+")";
        JLabel pdType = new JLabel(deviceruntype);
        String devicerunmodel="";
        if(pd.getDeviceType().equals(SystemConstants.InOutLine))
        	devicerunmodel = CBSystemConstants.getDictionaryName(pd.getDeviceRunModel(),"ACLineSegment");
        else if(pd.getDeviceType().equals(SystemConstants.PowerTransformer))
        	devicerunmodel = CBSystemConstants.getDictionaryName(pd.getDeviceRunModel(),"PowerTransformer");
        else
        	devicerunmodel = CBSystemConstants.getDictionaryName(pd.getDeviceRunModel(),"runmodel");
        JLabel pdRunModel = new JLabel(devicerunmodel);  
        
        String orgaNameStr = "";
//        String organIdStr = pd.getOrgaId();//所属机构
        String permission = pd.getPermissionOrganName();//许可机构
//        String selectOrganSql = "select t.ORGANNAME from "+CBSystemConstants.opcardUser+"T_A_POWERORGAN t where t.ORGANID = '" + organIdStr + "'";
//        
//        List resultList = DBManager.query(selectOrganSql);
//        if(resultList.size() > 0){
//        	Map<String, String> orgaEntry = (Map)resultList.get(0);
//        	orgaNameStr = orgaEntry.get("ORGANNAME");
//        	if(!permission.equals("")){
//        		orgaNameStr=orgaNameStr+"("+permission+"许可)";
//        	}
//        }
        
        orgaNameStr = pd.getOrgaName();
    	if(!permission.equals("")){
    		orgaNameStr=orgaNameStr+"("+permission+"许可)";
    	}
        JLabel orgaName = new JLabel(orgaNameStr);
        
	    String stateName = CBSystemConstants.getDeviceStatusName(pd.getDeviceType(),pd.getDeviceStatus());
	    JLabel pdStatus = new JLabel(stateName);
	    
	   
        infoPanel1.add(pdNameLabel);
        infoPanel2.add(pdName);
        infoPanel1.add(pdStatusLabel);
        infoPanel2.add(pdStatus);
        infoPanel1.add(pdTypeLabel);
        infoPanel2.add(pdType);
        infoPanel1.add(pdRunModelLabel);
        infoPanel2.add(pdRunModel);
        infoPanel1.add(pdOrganLabel);
        infoPanel2.add(orgaName);
        infoPanel.add(infoPanel1);
        infoPanel.add(infoPanel2);
        //if(CBSystemConstants.getUser().getUserName().contains("管理员")){
        	JLabel linkLabel = new JLabel();
        	List<PowerDevice> li = RuleExeUtil.getDeviceDirectList(pd, "");
        	List<String> devNamelist = new ArrayList<String>();
        	
        	for(PowerDevice pd : li){
        		if(pd.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
        			devNamelist.add(CZPService.getService().getDevNum(pd)+"手车"+SystemConstants.getMapEquipType().get(pd.getDeviceType()));
        		}else{
        			if(pd.getPowerDeviceName().contains("主变")&&
        					(pd.getDeviceType().equals(SystemConstants.SwitchSeparate)||pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)||pd.getDeviceType().equals(SystemConstants.Switch))){
            			//devNamelist.add(CZPService.getService().getDevNum(pd.getPowerDeviceName().replace("主变", ""))+SystemConstants.getMapEquipType().get(pd.getDeviceType()));
            			devNamelist.add(pd.getPowerDeviceName());
        			}else{
            			//devNamelist.add(CZPService.getService().getDevNum(pd)+SystemConstants.getMapEquipType().get(pd.getDeviceType()));
            			devNamelist.add(pd.getPowerDeviceName());
            		}
        		}
    		}
        	
        	//List li = RuleExeUtil.getDeviceList(pd, null, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, true, false, false);
        	if(li.size() > 0) {
        		JLabel linkDevice = new JLabel();  
    			StringBuffer sbf = new StringBuffer();

        		if(li.size()>11){
        			sbf.append("<html>");
        			
        			for(int i=0;i<11;i++){
        				sbf.append(devNamelist.get(i)+"、");
        			}
        			
        			sbf.append("<br>");
        			
        			for(int i=11;i<devNamelist.size();i++){
        				sbf.append(devNamelist.get(i)+"、");
        			}
        			sbf.delete(sbf.length()-1, sbf.length());
        			sbf.append("</html>");
            		linkDevice.setText(String.valueOf(sbf));
        			linkLabel.setText("<html>连接设备：<br>-</html>");
        		}else{
        			for(int i=0;i<devNamelist.size();i++){
        				sbf.append(devNamelist.get(i)+"、");
        			}
        			
        			sbf.delete(sbf.length()-1, sbf.length());
        			
        			linkDevice.setText(sbf.toString());
        			linkLabel.setText("连接设备：");
        		}
        		
		        infoPanel1.add(linkLabel);
		        infoPanel2.add(linkDevice);
        	}
        
//        System.out.println("厂站名称："+pd.getPowerStationName()+"|"+"厂站ID："+pd.getPowerStationID()+"|"+"设备名称："+pd.getPowerDeviceName()+"|"+"设备ID："+pd.getPowerDeviceID());
        	
        System.out.println("厂站名称："+pd.getPowerStationName()+"|"+"厂站ID："+pd.getPowerStationID()+"|"+"设备名称："+pd.getPowerDeviceName()+"|"+"设备ID："+pd.getPowerDeviceID()+"|"+"ACLINEID："+pd.getCimID()+"|"+"电压等级："+pd.getPowerVoltGrade());
        if(CBSystemConstants.roleCode.equals("1") && PWSystemConstants.deviceSourceMap.containsKey(pd)){
        	ArrayList<ArrayList<PowerDevice>> listlist = PWSystemConstants.deviceSourceMap.get(pd);
    		for(ArrayList<PowerDevice> list : listlist) {
    			String path = "<html>";
    			JLabel pathLabel = new JLabel("供电路径：");
            	JLabel pathDevice = null;
    			path = path + list.toString() + "";
    			path = path + "</html>";
        		pathDevice = new JLabel(path);
            	infoPanel1.add(pathLabel);
    	        infoPanel2.add(pathDevice);
    		}
        }

        JLabel pdisZnLabel = new JLabel("是否顺控：");
        JLabel pathisZn = null;
        
        if(pd.getDeviceType().equals(SystemConstants.Switch)||
        		pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
        	if(ifControl(pd)){
    			pathisZn = new JLabel("是");
        		infoPanel1.add(pdisZnLabel);
                infoPanel2.add(pathisZn);
        	}else{
        		pathisZn = new JLabel("否");
        		infoPanel1.add(pdisZnLabel);
                infoPanel2.add(pathisZn);
        	}
        }
        
        popupMenu.add(infoPanel);
    	return popupMenu;
	}
	
	public static boolean ifControl(PowerDevice dev){//开关可控
		if(dev.getDeviceType().equals(SystemConstants.Switch)){
			if(CBSystemConstants.opcardUser.equals("OPCARDKM.")){
				String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
					return Boolean.parseBoolean(ifcontrol);
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDPE.")){
				String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
						+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dev.getPowerDeviceID()+"'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					return Boolean.parseBoolean(ifcontrol);
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDXSBN.")){
				String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDHH.")){
				String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDYX.")){
				String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54' AND DESCRIPTION NOT LIKE '%相%'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					return Boolean.parseBoolean(ifcontrol);
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDLC.")){
				String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDNJ.")){
				String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDDQ.")){
				String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
				String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
					
					if(ifcontrol.equals("0")||ifcontrol.equals("")){
						return false;
					}else{
						return true;
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDWS.")){
				String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_BREAKER WHERE ID = '"+dev.getPowerDeviceID()+"'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
					
					if(ifcontrol.equals("0")){
						ifcontrol = "false";
					}else{
						ifcontrol = "true";
					}
					
					return Boolean.parseBoolean(ifcontrol);
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDDH.")){
				String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_BREAKER WHERE ID = '"+dev.getPowerDeviceID()+"'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
					
					if(ifcontrol.equals("0")){
						ifcontrol = "false";
					}else{
						ifcontrol = "true";
					}
					
					return Boolean.parseBoolean(ifcontrol);
				}
			}
		}else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			if(CBSystemConstants.opcardUser.equals("OPCARDKM.")){
				
				
			}else if(CBSystemConstants.opcardUser.equals("OPCARDPE.")){
				String sql = "SELECT A.IFCONTROL FROM "+CBSystemConstants.equipUser+"T_EQUIPINFO B,"+CBSystemConstants.equipUser+"T_M_MEASUREMENT A "
						+ "WHERE B.EQUIP_ID = A.MEMBEROF_PSR  AND A.MEASUREMENTTYPE = 'MeasType-54' AND A.NAME like '%_S' AND B.EQUIP_ID = '"+dev.getPowerDeviceID()+"'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					return Boolean.parseBoolean(ifcontrol);
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDXSBN.")){
				String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDHH.")){
				String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDYX.")){
				String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(!Boolean.parseBoolean(ifcontrol)||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
				
				if(ifcontrolList.size() == 0){
					return false;
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDLC.")){
				String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
				
				if(ifcontrolList.size() == 0){
					return false;
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDNJ.")){
				String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
				
				if(ifcontrolList.size() == 0){
					return false;
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDDQ.")){
				String sql = "SELECT IFCONTROL FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = 'MeasType-54'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFCONTROL"));
					
					if(ifcontrol.equals("false")||ifcontrol.equals("")){
						return false;
					}else{
						return Boolean.parseBoolean(ifcontrol);
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDQJ.")){
				String sql = "SELECT IFYK FROM "+CBSystemConstants.equipUser+"T_M_MEASUREMENT WHERE MEMBEROF_PSR = '"+dev.getPowerDeviceID()+"' AND MEASUREMENTTYPE = '40740'";
				
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("IFYK"));
					
					if(ifcontrol.equals("0")||ifcontrol.equals("")){
						return false;
					}else{
						return true;
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDWS.")){
				String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_DISCONNECTOR WHERE ID = '"+dev.getPowerDeviceID()+"'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
					
					if(ifcontrol.equals("0")||ifcontrol.equals("")){
						return false;
					}else{
						return true;
					}
				}
			}else if(CBSystemConstants.opcardUser.equals("OPCARDDH.")){
				String sql = "SELECT ISCONTROL FROM "+CBSystemConstants.equipUser+"T_M_DISCONNECTOR WHERE ID = '"+dev.getPowerDeviceID()+"'";
				List<Map<String,String>> ifcontrolList = DBManager.queryForList(sql);
				
				for(Map<String,String> ifcontrolMap : ifcontrolList){
					String ifcontrol = StringUtils.ObjToString(ifcontrolMap.get("ISCONTROL"));
					
					if(ifcontrol.equals("0")||ifcontrol.equals("")){
						return false;
					}else{
						return true;
					}
				}
			}
		}
		
		return false;
	}
}
