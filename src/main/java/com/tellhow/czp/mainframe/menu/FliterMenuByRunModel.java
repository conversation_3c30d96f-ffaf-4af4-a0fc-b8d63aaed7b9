package com.tellhow.czp.mainframe.menu;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

/**
 * 去除不必要的右键关联菜单项
 * */
public class FliterMenuByRunModel {
	public final static String RUNMODEL = "runmodel";// 约束条件：接线方式
	public final static String HASSIDE = "hasside";// 约束条件：旁路
	private PowerDevice pd;
	private Map<String, DeviceMenuModel> map;

	public FliterMenuByRunModel(PowerDevice pd,
			Map<String, DeviceMenuModel> devMenusMap) {
		this.pd = pd;
		this.map = devMenusMap;
	}

	public void execute() {
		if (pd == null)
			return;
		boolean rlm = false;
		boolean rb = false;
		if (!judgeMotherLoad(pd)) {
			// 不是双母接线方式就移除倒母操作
			removeLoadMother(map);
			rlm = true;
		}
		if(!judgeBridgeLine(pd)){
			//不是桥型接线就移除主变、开关同时停电操作
			removeBridgeLine(map);
			rlm = true;
		}
		if (!judgebyPass(pd)) {
			// 没旁路母线就移除旁路相关操作
			removeBypass(map);
			rb = true;
		}
		
//		if(CBSystemConstants.isSpecialMenu){//是否存在特殊的菜单，比如大渡河龚山线
//			removeSpecialMenu(map);
//		}
		
		removeByRunType(map,RUNMODEL);
		
		Iterator<String> ite = map.keySet().iterator();
		while (ite.hasNext()) {
			DeviceMenuModel dmm = map.get(ite.next());
			if (!dmm.getRuntype().equals("")) {
				String[] arr = dmm.getRuntype().split(",");
				if(!Arrays.asList(arr).contains(pd.getDeviceRunType())) {
					map.remove(ite);
					ite.remove();
				}
			}
			if (!dmm.getRunmodel().equals("")) {//电压等级过滤，数据库规定用英文分号";"分隔，大于等于前一个，小于后一个
				if(dmm.getRunmodel().indexOf(pd.getDeviceRunModel()) == -1){
					String[] para=dmm.getRunmodel().split(";");
					if(para.length>1){					
						if(Integer.valueOf(para[0])<=pd.getPowerVoltGrade()&&Integer.valueOf(para[1])>pd.getPowerVoltGrade()){

						}	
						else {
							map.remove(ite);
							ite.remove();
						}
					}
				}
			}
			//电压等级过滤，数据库规定用英文分号";"分隔，大于等于前一个，小于后一个（注意：设定1为菜单不显示条件，但是规则不能过滤，否则影响设备的智能转换）
			if (!dmm.getStatekind().equals("") && !dmm.getStatekind().equals("1")) {
				String[] para=dmm.getStatekind().split(";");
				boolean isRemove = true;
				for(String p : para) {
					if(pd.getPowerVoltGrade() == Double.valueOf(p)) {
						isRemove = false;
						break;
					}
				}
				
				if(isRemove){					
					map.remove(ite);
					ite.remove();
				}
			}
//			if((dmm.getStatename().equals("变低转负荷")||dmm.getStatename().equals("转电恢复")) && pd.getPowerVoltGrade() >= 500) {
//				map.remove(ite);
//				ite.remove();
//			}
			
		}
		
		removeNullParent(map);
		
		 //两者都移除了就移除父级“更多”
//		if (rlm && rb) {
//			removeByKeyWord(map, "更多");
//		}
	}

	/**
	 * 判断是否是双母接线方式
	 * */
	private boolean judgeMotherLoad(PowerDevice pd2) {
		if (pd2.getDeviceRunModel().equals(
				CBSystemConstants.RunModelDoubleMotherLine)) {
			return true;
		}
		return false;
	}
	
	/**
	 * 判断是否是桥型接线
	 */
	private boolean judgeBridgeLine(PowerDevice pd2){
		if(pd2.getDeviceRunModel().equals(CBSystemConstants.RunModelBridgeLine))
			return true;
		return false;
	}
	

	/**
	 * 判断是否有旁路母线
	 * */
	private boolean judgebyPass(PowerDevice device) {
		List<PowerDevice> lines = RuleUtil.getPowerDevice(
				device.getPowerStationID(), SystemConstants.MotherLine,
				device.getPowerVoltGrade());
		for (PowerDevice pd : lines) {
			// 安装类型是否为旁路母线
			if (pd.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSideMother)) {
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 去除倒母选项
	 * 
	 * @param map2
	 */
	public static void removeLoadMother(Map<String, DeviceMenuModel> map2) {
		removeByRequire(map2, RUNMODEL);
	}
	
	/**
	 * 去除主变、开关同时停电
	 * 
	 * @param map2
	 */
	public static void removeBridgeLine(Map<String, DeviceMenuModel> map2){
		removeByBridge(map2, RUNMODEL);
	}

	/**
	 * 去除旁路相关选项
	 * 
	 * @param map2
	 * */
	public static void removeBypass(Map<String, DeviceMenuModel> map2) {
		removeByRequire(map2, HASSIDE);
	}

	/**
	 * 根据约束条件去除菜单选项
	 * 
	 * @param require
	 *            约束条件
	 * @param map2
	 *            菜单数据模型
	 * */
	public static void removeByRequire(Map<String, DeviceMenuModel> map2,
			String require) {
		Iterator<String> ite = map2.keySet().iterator();
		while (ite.hasNext()) {
			String sCode = ite.next();
			String stateName = map2.get(sCode).getStatename();
			String runMode = map2.get(sCode).getRunmodel();
			String hasside = map2.get(sCode).getHasside();
			if (require != null) {
				if (require.toLowerCase().equals(RUNMODEL)) {
					// 非双母接线方式移除
					if (runMode.equals("runmodeldoublemotherline")) {
						map2.remove(ite);
						ite.remove();
					}
				}
				if (require.toLowerCase().equals(HASSIDE)) {
					// 没有旁路母线的移除
					if (hasside.equals("1")) {
						map2.remove(ite);
						ite.remove();
					}
				}
			}

		}
	}
	
	/**
	 * 根据当前设备类型及约束条件去除菜单选项
	 * 
	 * @param require
	 *            约束条件
	 * @param map2
	 *            菜单数据模型
	 * */
	public void removeByRunType(Map<String, DeviceMenuModel> map2,
			String require) {
		Iterator<String> ite = map2.keySet().iterator();
		while (ite.hasNext()) {
			String sCode = ite.next();
			String runMode = map2.get(sCode).getRunmodel();
			if (require != null && 
					!runMode.equals("") && 
					require.toLowerCase().equals(RUNMODEL)) {
				// 按接线方式移除
				if (!runMode.equals(pd.getDeviceRunModel())) {
					map2.remove(ite);
					ite.remove();
				}
			}

		}
	}
	
	//如果不是桥型接线方式则移除主变、开关同时停电
	private static void removeByBridge(Map<String, DeviceMenuModel> map2,
			String require) {
		Iterator<String> ite = map2.keySet().iterator();
		String sCode;
		while (ite.hasNext()) {
			sCode = ite.next();
			String runMode = map2.get(sCode).getRunmodel();
			String hasside = map2.get(sCode).getHasside();
			if (require != null) {
				if (require.toLowerCase().equals(RUNMODEL)) {
					//非桥型接线移除
					if(runMode.equals("runmodelbridgeline")){
						map2.remove(ite);
						ite.remove();
					}
				}
			}
		}
	}
	
	/** 移除更多 */
	public static void removeNullParent(Map<String, DeviceMenuModel> map2) {
		Iterator<String> ite = map2.keySet().iterator();
		while (ite.hasNext()) {
			DeviceMenuModel dmm = map2.get(ite.next());
			if (dmm.getStatetype().equals("1")) {
				
				boolean isNeedDelete = true;
				Iterator<String> ite2 = map2.keySet().iterator();
				while (ite2.hasNext()) {
					DeviceMenuModel dmm2 = map2.get(ite2.next());
					if(dmm2.getParentcode().equals(dmm.getStatecode())) {
						isNeedDelete = false;
						break;
					}
				}
				if(isNeedDelete) {
					map2.remove(ite);
					ite.remove();
				}
			}
		}
	}

	/** 移除与含关键字的选项 */
	public static void removeByKeyWord(Map<String, DeviceMenuModel> map2,
			String keyword) {
		Iterator<String> ite = map2.keySet().iterator();
		String statename;
		while (ite.hasNext()) {
			statename = map2.get(ite.next()).getStatename();
			if (statename.contains(keyword)) {
				map2.remove(ite);
				ite.remove();
			}
		}
	}
	
	
	//如果不是桥型接线方式则移除主变、开关同时停电
//	private void removeSpecialMenu(Map<String, DeviceMenuModel> map2) {
//		
//		if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
//			if(pd.getPowerDeviceName().contains("龚山线")){
//				Iterator<String> ite = map2.keySet().iterator();
//				String sCode;
//				while (ite.hasNext()) {
//					sCode = ite.next();
//					String value = map2.get(sCode).getStatename();
//					
//					if(value.equals("运行")&&!CBSystemConstants.cardbuildtype.equals("1")&&!CBSystemConstants.cardbuildtype.equals("2")){
//						ite.remove();
//					}
//				}
//			}else{
//				Iterator<String> ite = map2.keySet().iterator();
//				String sCode;
//				while (ite.hasNext()) {
//					sCode = ite.next();
//					String value = map2.get(sCode).getStatename();
//					
//					if(value.equals("1B带运行")||value.equals("2B带运行")||value.equals("合环运行")){
//						ite.remove();
//					}
//				}
//			}
//		}
//	}
//	
}
