/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : devicestate表对应内存对象；设备右键操作菜单对象 
 * 作    者 : 张余平
 * 开发日期 : 2010-09-14
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.mainframe.menu;

import czprule.model.PowerDevice;


public class DeviceMenuModel {
	
	private PowerDevice pd=null;   //右键操作对象
	private String statecode=null;    //操作ID
	private String parentcode="";   //上级操作编码
	private String stateorder="";   //排序
	private String statename="";    //操作名称
	private String statevalue="";   //状态值
	//菜单约束条件
	private String runmodel="";//接线方式
	private String hasside="";//是否有旁路 0 表示有 1表示没有
	private String runtype="";//安装类型
	private String statetype="";//类型
	private String operatecode=null;    //操作编码
	private String statekind="";//用于设置哪些电压等级才有菜单权限，空表示都有权限
	public DeviceMenuModel(){
		
	}
	
	public PowerDevice getPd() {
		return pd;
	}
	public void setPd(PowerDevice pd) {
		this.pd = pd;
	}
	
	
	public String getStatecode() {
		return statecode;
	}
	public void setStatecode(String statecode) {
		this.statecode = statecode;
	}
	public String getStatename() {
		return statename;
	}
	public void setStatename(String statename) {
		this.statename = statename;
	}
	public String getStateorder() {
		return stateorder;
	}
	public void setStateorder(String stateorder) {
		this.stateorder = stateorder;
	}
	public String getStatevalue() {
		return statevalue;
	}
	public void setStatevalue(String statevalue) {
		this.statevalue = statevalue;
	}
	public String getParentcode() {
		return parentcode;
	}
	public void setParentcode(String parentcode) {
		this.parentcode = parentcode;
	}
	
	public String getRunmodel() {
		return runmodel;
	}
	public void setRunmodel(String runmodel) {
		this.runmodel = runmodel;
	}
	public String getHasside() {
		return hasside;
	}
	public void setHasside(String hasside) {
		this.hasside = hasside;
	}
	public String getRuntype() {
		return runtype;
	}
	public void setRuntype(String runtype) {
		this.runtype = runtype;
	}
	
	public String getStatekind() {
		return statekind;
	}

	public void setStatekind(String statekind) {
		this.statekind = statekind;
	}

	@Override
	public String toString(){
		return this.statename;
	}
	public String getStatetype() {
		return statetype;
	}
	public void setStatetype(String statetype) {
		this.statetype = statetype;
	}
	public String getOperatecode() {
		return operatecode;
	}
	public void setOperatecode(String operatecode) {
		this.operatecode = operatecode;
	}
	

}
