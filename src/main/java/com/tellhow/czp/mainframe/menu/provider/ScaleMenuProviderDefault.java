package com.tellhow.czp.mainframe.menu.provider;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPopupMenu;

import org.w3c.dom.Document;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.mainframe.menu.DeviceMenuBuild;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.menu.provider.DefaultMenuProvider;
import com.tellhow.graphicframework.menu.provider.LayerListDialog;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-9-4 下午02:38:43 
 */
public class ScaleMenuProviderDefault extends ScaleMenuProvider {
	
	
	public ScaleMenuProviderDefault() {
	}
	
	@Override
	public JPopupMenu createMenu() {
		final SVGCanvas fSvgCanvas = SystemConstants.getGuiBuilder().getActivateSVGPanel().getSvgCanvas();
		String stationID=SystemConstants.getGuiBuilder().getActivateSVGPanel().getStationID();
		final PowerDevice pd=CBSystemConstants.getPowerStation(stationID);
		JMenuItem menuItem = null;
		
		DeviceMenuBuild dmb=new DeviceMenuBuild(pd);
		JPopupMenu popupMenu = dmb.createMenu();
		
//		if(CBSystemConstants.apptype.equals("1")) {
//			menuItem = new JMenuItem("全站AVC投入");
//			menuItem.addActionListener(new ActionListener() {
//				
//				public void actionPerformed(ActionEvent e) {
//					RemovableUtil.changeRemoveDeviceStatus(pd, PowerDevice.AVC, "0");
//				}
//			});
//			popupMenu.add(menuItem);
//			
//			menuItem = new JMenuItem("全站AVC退出");
//			menuItem.addActionListener(new ActionListener() {
//				
//				public void actionPerformed(ActionEvent e) {
//					RemovableUtil.changeRemoveDeviceStatus(pd, PowerDevice.AVC, "1");
//				}
//			});
//			popupMenu.add(menuItem);
//			
//			menuItem = new JMenuItem("全站VQC投入");
//			menuItem.addActionListener(new ActionListener() {
//				
//				public void actionPerformed(ActionEvent e) {
//					RemovableUtil.changeRemoveDeviceStatus(pd, PowerDevice.VQC, "0");
//				}
//			});
//			popupMenu.add(menuItem);
//			
//			menuItem = new JMenuItem("全站VQC退出");
//			menuItem.addActionListener(new ActionListener() {
//				
//				public void actionPerformed(ActionEvent e) {
//					RemovableUtil.changeRemoveDeviceStatus(pd, PowerDevice.VQC, "1");
//				}
//			});
//			popupMenu.add(menuItem);
//			
//			popupMenu.addSeparator();
//		}
		
		menuItem = new JMenuItem("放大显示");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				fSvgCanvas.zoomCanvas(1, 0.2);
			}
		});
		popupMenu.add(menuItem);
		
		menuItem = new JMenuItem("缩小显示");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				fSvgCanvas.zoomCanvas(-1, 0.2);
			}
		});
		popupMenu.add(menuItem);
		
		menuItem = new JMenuItem("正常显示");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				fSvgCanvas.zoomCanvas(0, 0);
			}
		});
		popupMenu.add(menuItem);
		
		popupMenu.addSeparator();
		
		menuItem = new JMenuItem("图层设置");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				Document document = fSvgCanvas.getSVGDocument();
				LayerListDialog myDialog = new LayerListDialog(SystemConstants.getMainFrame(), document);
				myDialog.setVisible(true);
			}
		});
		popupMenu.add(menuItem);
		
		menuItem = new JMenuItem("搜索设备");
		menuItem.addActionListener(new ActionListener() {
			
			public void actionPerformed(ActionEvent e) {
				SearchEquipForm sef = new SearchEquipForm(fSvgCanvas);
			}
		});
		popupMenu.add(menuItem);
		
		//if(CZPOperator.getOperator().getClass().getName().equals("com.tellhow.czp.app.CZPOperatorBJ")) {
			/*menuItem = new JMenuItem("更新本站常方式");
			menuItem.addActionListener(new ActionListener() {
				
				public void actionPerformed(ActionEvent e) {
					String powerStationID = null;
					SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
			        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
			        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
			            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
			            czprule.model.PowerDevice station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
			        }
			        if(powerStationID == "")
			        	return ;
			        
			        //更新版本
			        String ver_id = null;
			        String unitcode = CBSystemConstants.unitCode;//获取unitcode
			        Statement stam = null;
					ResultSet rs = null;
					Connection conn = DBManager.getConnection();
					try {
						stam = conn.createStatement();
						String selectSql = "select a.ver_id from "+CBSystemConstants.opcardUser+"t_a_statenormalver a where a.ver_flag='1' and a.unitcode='"+unitcode+"'";
						rs = stam.executeQuery(selectSql);
						while(rs.next())
							ver_id = rs.getString("ver_id");
					} catch (SQLException e2) {
						e2.printStackTrace();
					}
					if(ver_id == null) {
						ver_id = java.util.UUID.randomUUID().toString();//获取存入数据库的一个随机的uuid
						String ver_author = CBSystemConstants.getUser().getUserName();//获取当前登录用户名
			            String fmt = "yyyyMMdd";
			            SimpleDateFormat sdf = new SimpleDateFormat(fmt);
			            Date date = new Date();
			            String ver_name = sdf.format(date);//以20131031的格式作为版本名称
						String insertSql = "insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMALVER(VER_ID,VER_NAME,VER_AUTHOR,VER_DATE,VER_FLAG,UNITCODE) values ('" + ver_id + "','" + ver_name + "','" + ver_author + "', sysdate, 1 , '" + unitcode + "')";//向版本表插入新版本
						try {
			            	stam = conn.createStatement();
							stam.executeUpdate(insertSql);
						} catch (SQLException e1) {
							e1.printStackTrace();
						}
					}
			        updateDevice(conn, rs, stam, ver_id, powerStationID);
			        try {
			        	stam.close();
						conn.close();
						ShowMessage.view(SystemConstants.getMainFrame(), "保存成功！");
					} catch (SQLException e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}
				}
			});
			popupMenu.add(menuItem);*/
			
			///////////////////////////////////黄翔修改
			menuItem = new JMenuItem("更新厂站常方式");
			menuItem.addActionListener(new ActionListener() {
				
				public void actionPerformed(ActionEvent e) {
					Object[] bzANDsy=new String[1];
						bzANDsy[0]="更新本站";
//						bzANDsy[1]="更新所有厂站";/*暂时关闭*/
						
					int bzORsy = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择更新范围", "选择", JOptionPane.DEFAULT_OPTION, JOptionPane.INFORMATION_MESSAGE, null, bzANDsy, bzANDsy[0]);
					if(bzORsy==-1){
						ShowMessage.view("没有选择范围！");
					}
					Iterator it;
					if(bzANDsy[bzORsy].equals("更新本站")){
						String powerStationID = null;
						SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
				        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
				        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
				            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
				            //czprule.model.PowerDevice station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
				        }
				        if(powerStationID == "")
				        	return ;
				        List data = new ArrayList();
				        data.add(powerStationID);
				        it = data.iterator();	
					}else{
						 it=CBSystemConstants.getMapPowerStation().keySet().iterator();
					}//更新版本
			        String ver_id = null;
			        String unitcode = CBSystemConstants.unitCode;//获取unitcode
			        Statement stam = null;
					ResultSet rs = null;
					Connection conn = DBManager.getConnection();
					try {
						stam = conn.createStatement();
						String selectSql = "select a.ver_id from "+CBSystemConstants.opcardUser+"t_a_statenormalver a where a.ver_flag='1' and a.opcode='"+"0"+"'";
						rs = stam.executeQuery(selectSql);
						while(rs.next())
							ver_id = rs.getString("ver_id");
					} catch (SQLException e2) {
						e2.printStackTrace();
					}
					if(ver_id == null) {
						ver_id = java.util.UUID.randomUUID().toString();//获取存入数据库的一个随机的uuid
						String ver_author = CBSystemConstants.getUser().getUserName();//获取当前登录用户名
			            String fmt = "yyyyMMdd";
			            SimpleDateFormat sdf = new SimpleDateFormat(fmt);
			            Date date = new Date();
			            String ver_name = sdf.format(date);//以20131031的格式作为版本名称
						String insertSql = "insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMALVER(VER_ID,VER_NAME,VER_AUTHOR,VER_DATE,VER_FLAG,opcode) values ('" + ver_id + "','" + ver_name + "','" + ver_author + "', sysdate, 1 , '" + "0" + "')";//向版本表插入新版本
						try {
			            	stam = conn.createStatement();
							stam.executeUpdate(insertSql);
						} catch (SQLException e1) {
							e1.printStackTrace();
						}
					}
					while (it.hasNext()) {
						String stationID = (String)it.next();
			        try {
						updateDevice(conn, rs, ver_id, stationID);
					} catch (SQLException e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}
					}
			        try {
			        	stam.close();
						conn.close();
						ShowMessage.view(SystemConstants.getMainFrame(), "保存成功！");
					} catch (SQLException e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}
				}
			});
			popupMenu.add(menuItem);
			//////////////////////////////
			menuItem = new JMenuItem("生成系统常方式");
			menuItem.addActionListener(new ActionListener() {
				
				public void actionPerformed(ActionEvent e) {
	//				LoadOrganization.loadOrganization();//加载所有厂站和当前打开厂站的设备的组织机构，可不在此调用
					
					String powerStationID = null;
					SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
			        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
			        czprule.model.PowerDevice station;
			        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
			            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
			            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
			        }
			        if(powerStationID == "")
			        	return ;
			        
			        //插入新版本
			        Statement stam = null;
					ResultSet rs = null;
					Connection conn = DBManager.getConnection();
			        String ver_id = java.util.UUID.randomUUID().toString();//获取存入数据库的一个随机的uuid
		            String unitcode = CBSystemConstants.unitCode;//获取unitcode
		            
		            String ver_author = CBSystemConstants.getUser().getUserName();//获取当前登录用户名
		            String fmt = "yyyyMMdd";
		            SimpleDateFormat sdf = new SimpleDateFormat(fmt);
		            Date date = new Date();
		            String ver_name = sdf.format(date);//以20131031的格式作为版本名称
		            
		            String sql = "insert into "+CBSystemConstants.opcardUser+"t_a_statenormal select t.obj_id,t.obj_type,t.station_id,t.obj_state,'"+ver_id+"' from "+CBSystemConstants.opcardUser+"t_a_statenormal t where t.ver_id=(select a.ver_id from "+CBSystemConstants.opcardUser+"t_a_statenormalver a where a.ver_flag='1' and a.opcode='"+"0"+"')";
		            String updateSql = "update "+CBSystemConstants.opcardUser+"T_A_STATENORMALVER set ver_flag = 0 where ver_flag = 1 and opcode = '" + "0" + "'";//先把当前机构正在启用的版本停用
		            
		            String insertSql = "insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMALVER(VER_ID,VER_NAME,VER_AUTHOR,VER_DATE,VER_FLAG,opcode) values ('" + ver_id + "','" + ver_name + "','" + ver_author + "', sysdate, 1 , '" + "0" + "')";//向版本表插入新版本
		            
		            
		            
		            try {
		            	stam = conn.createStatement();
		            	stam.executeUpdate(sql);
						stam.executeUpdate(updateSql);
						stam.executeUpdate(insertSql);
					} catch (SQLException e1) {
						e1.printStackTrace();
					}
		            
		            
		            
		            //更新版本记录里的数据
		            try {
						updateDevice(conn, rs,ver_id,powerStationID);
					} catch (SQLException e2) {
						// TODO Auto-generated catch block
						e2.printStackTrace();
					}
		            try {
		            	stam.close();
						conn.close();
						ShowMessage.view(SystemConstants.getMainFrame(), "保存成功！");
					} catch (SQLException e1) {
						e1.printStackTrace();
					}
				}
			});
			popupMenu.add(menuItem);
			
			menuItem = new JMenuItem("以常方式更新本站");
			menuItem.addActionListener(new ActionListener() {
				
				public void actionPerformed(ActionEvent e) {
					String powerStationID = null;
					SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
			        String mapType = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("MapType");
			        czprule.model.PowerDevice station;
			        if(mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
			            powerStationID = resolver.resolveSvgElement(fSvgCanvas.getSVGDocument()).getAttribute("StationID");//获取厂站ID
			            station = CBSystemConstants.getMapPowerStation().get(powerStationID);//获取当前厂站对象
			        }
			        if(powerStationID == "")
			        	return ;
			        
			        Statement stam = null;
					ResultSet rs = null;
					Connection conn = DBManager.getConnection();
					String unitcode = CBSystemConstants.unitCode;//获取unitcode
					
					String sql = "select t.OBJ_ID, t.OBJ_STATE, t.OBJ_TYPE from "+CBSystemConstants.opcardUser+"T_A_STATENORMAL t where t.STATION_ID = '" + powerStationID + "' and t.VER_ID in(select t2.VER_ID from "+CBSystemConstants.opcardUser+"T_A_STATENORMALVER t2 where t2.VER_FLAG = 1 and t2.opcode = '" + "0" +"')";
					try {
						stam = conn.createStatement();
						rs = stam.executeQuery(sql);
						
						
						while(rs.next()){
//							String updateStatementSql = "";
							String obj_type = rs.getString("OBJ_TYPE");
							String ohb_state = rs.getString("OBJ_STATE");
							String obj_id = rs.getString("OBJ_ID");
/*							if(obj_type.equals("1"))//更新设备
								updateStatementSql = "update "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO set DEVICESTATUS = '" + ohb_state + "' where EQUIPID = '" + obj_id +"'";
							else if(obj_type.equals("2"))//更新安置设备
								updateStatementSql = "update "+CBSystemConstants.opcardUser+"T_A_ECSAUTRECORD set CHANGE_STATE = '"  + ohb_state + "' where SCS_ID = '" + obj_id +"'";
							else if(obj_type.equals("3"))//更新跳投关系
								updateStatementSql = "update "+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD set CHANGE_STATE = '"  + ohb_state + "' where DEVICE_ID = '" + obj_id +"'";
							updateStatementList.add(updateStatementSql);*/
							PowerDevice device = CBSystemConstants.getPowerDevice(powerStationID, obj_id);
							if(device==null){
								return;
							}else{
								device.setDeviceStatus(ohb_state);
								DeviceSVGPanelUtil.changeDeviceSVGColor(device);
							}
						}
//						for(String updateSqlTemp : updateStatementList){
//							stam.addBatch(updateSqlTemp);
//						}
//						stam.executeBatch();
					} catch (Exception e2) {
						ShowMessage.view(SystemConstants.getMainFrame(), "更新失败！");
						e2.printStackTrace();
					}
					/*try {
						stam.close();
						conn.close();
						if(updateStatementList.size() == 0)
							ShowMessage.view(SystemConstants.getMainFrame(), "常方式记录中无本站数据！");
						else
							ShowMessage.view(SystemConstants.getMainFrame(), "更新成功！");
					} catch (SQLException e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}*/
				}
			});
			popupMenu.add(menuItem);
		
		return popupMenu;
		
	}
	

	
	/**
	 * 更新设备版本记录
	 * @param rs
	 * @param stam
	 * @param ver_id
	 * @param powerStationID
	 * @throws SQLException 
	 */
	protected void updateDevice(Connection conn, ResultSet rs, String ver_id, String powerStationID) throws SQLException {//黄翔修改
			
			//更新设备
	        Map<String, czprule.model.PowerDevice> deviceMap = CBSystemConstants.getMapPowerStationDevice().get(powerStationID);
	        if(deviceMap == null){
	        	 CreatePowerStationToplogy.loadFacEquip(powerStationID);
	        	 deviceMap = CBSystemConstants.getMapPowerStationDevice().get(powerStationID);
	        	 if(deviceMap==null){
	        		 System.out.println(powerStationID+"无法加载");
	        		 return;
	        		 
	        	 }
	        	}
	        PreparedStatement pstam = (PreparedStatement)conn.prepareStatement("update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = ? where ver_id=? and obj_id = ?");
	        PreparedStatement pstam1 = (PreparedStatement)conn.prepareStatement("insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values (?,'1',?,?,?)");
	        for(Map.Entry<String, czprule.model.PowerDevice> deviceEntry : deviceMap.entrySet()){
	        	String obj_id = deviceEntry.getValue().getPowerDeviceID();
	        	String obj_state = deviceEntry.getValue().getDeviceStatus();
	        	
	        	//String updateDeviceSql = "update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = '" + obj_state + "' where ver_id='" + ver_id + "' and obj_id = '" + obj_id + "'";
	        	int i = 0;
	        	try {
	        		pstam.setString(1,obj_state);
	        		pstam.setString(2,ver_id);
	        		pstam.setString(3,obj_id);
	        		i = pstam.executeUpdate();
					if(i == 0){//如果更新失败，表明没有这个设备的数据，则插入当前设备的版本信息到表中
						//String insertDeviceSql = "insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values ('" + obj_id + "','1','" + powerStationID + "','" + obj_state + "','" + ver_id + "')";
						//stam.executeUpdate(insertDeviceSql);
						pstam1.setString(1,obj_id);
		        		pstam1.setString(2,powerStationID);
		        		pstam1.setString(3,obj_state);
		        		pstam1.setString(4,ver_id);
						pstam1.addBatch();
					}
				} catch (SQLException e1) {
					e1.printStackTrace();
				}
	        }
	        try {
				pstam1.executeBatch();
				pstam1.clearBatch();
			} catch (SQLException e2) {
				// TODO Auto-generated catch block
				e2.printStackTrace();
			}
			//更新安置设备
//            String anzhiSql = "select t1.scs_id, t1.station_id,t2.change_state from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t1, "+CBSystemConstants.opcardUser+"T_A_ECSAUTRECORD t2,"+CBSystemConstants.opcardUser+"t_e_equipinfo t3 where t1.scs_id = t2.scs_id and t1.scs_obj_code=t3.equip_id and t3.station_id = '" + powerStationID + "'";//查询当前厂站的安置设备
            try {
            	Statement stmt = conn.createStatement();
            	//edit 2014.6.24
				rs = stmt.executeQuery(OPEService.getService().updateDevice1(powerStationID));
				pstam = (PreparedStatement)conn.prepareStatement("update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = ? where ver_id=? and obj_id = ?");
				pstam1 = (PreparedStatement)conn.prepareStatement("insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values (?,'2',?,?,?)");
				while(rs.next()){
					String scs_id = rs.getString("scs_id");
					String change_state = rs.getString("change_state");
					//String updateAnZhiSql = "update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = '" + change_state + "' where ver_id='" + ver_id + "' and obj_id = '" + scs_id + "'";//更新安置设备
					pstam.setString(1,change_state);
	        		pstam.setString(2,ver_id);
	        		pstam.setString(3,scs_id);
					int i = 0;
					i = pstam.executeUpdate();
					if(i == 0){//如果更新失败，表明没有这个安置设备的数据，则插入当前安置设备的版本信息到表中
						//String insertAnZhiSql = "insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values ('" + scs_id + "','2','" + powerStationID + "','" + change_state + "','" + ver_id + "')";
						//stam.executeUpdate(insertAnZhiSql);
						pstam1.setString(1,scs_id);
		        		pstam1.setString(2,powerStationID);
		        		pstam1.setString(3,change_state);
		        		pstam1.setString(4,ver_id);
						pstam1.addBatch();
					}
				}
				pstam1.executeBatch();
				pstam1.clearBatch();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
            //更新跳投关系
//            String tiaotouSql = "select t2.relation_id,t3.change_state from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t1, "+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE t2,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD t3,"+CBSystemConstants.opcardUser+"t_e_equipinfo t4 where t1.scs_id = t2.scs_id and t1.scs_obj_code=t4.equip_id and t2.relation_id = t3.device_id and t4.station_id = '" + powerStationID + "'";
            try {
            	Statement stmt = conn.createStatement();
            	//edit 2014.6.24
				rs = stmt.executeQuery(OPEService.getService().updateDevice2(powerStationID));
				pstam = (PreparedStatement)conn.prepareStatement("update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = ? where ver_id=? and obj_id = ?");
				pstam1 = (PreparedStatement)conn.prepareStatement("insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values (?,'3',?,?,?)");
				while(rs.next()){
					String relation_id = rs.getString("relation_id");
					String change_state = rs.getString("change_state");
					//String updateTiaoTouSql = "update "+CBSystemConstants.opcardUser+"T_A_STATENORMAL set obj_state = '" + change_state + "' where ver_id='" + ver_id + "' and obj_id = '" + relation_id + "'";
					pstam.setString(1,change_state);
	        		pstam.setString(2,ver_id);
	        		pstam.setString(3,relation_id);
					int i = 0;
					i = pstam.executeUpdate();
					if(i == 0){//如果更新失败，表明没有这个跳投关系的数据，则插入当前跳投关系的版本信息到表中
						//String insertTiaoTouSql = "insert into "+CBSystemConstants.opcardUser+"T_A_STATENORMAL(OBJ_ID,OBJ_TYPE,STATION_ID,OBJ_STATE,VER_ID) values ('" + relation_id + "','3','" + powerStationID + "','" + change_state + "','" + ver_id + "')";
						//stam.executeUpdate(insertTiaoTouSql);
						pstam1.setString(1,relation_id);
		        		pstam1.setString(2,powerStationID);
		        		pstam1.setString(3,change_state);
		        		pstam1.setString(4,ver_id);
						pstam1.addBatch();
					}
				}
				pstam1.executeBatch();
				pstam1.clearBatch();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
            if(rs != null) {
				try {
					rs.close();
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
            }
	}
}

