/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 在设备右键菜单中封装DeviceMenuModel对象
 * 作    者 : 张余平
 * 开发日期 : 2010-09-14
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.mainframe.menu;

import javax.swing.JMenuItem;


public class AbstractJMenuItem extends JMenuItem {
    
	/**
	 * 
	 */
	private static final long serialVersionUID = -325614558391841498L;
	private DeviceMenuModel dmm;
	
    public AbstractJMenuItem(DeviceMenuModel dmm) {
        super(dmm==null?"":dmm.getStatename());
        this.dmm=dmm;
    }

	public DeviceMenuModel getDeviceMenuModel() {
		return dmm;
	}

	public void setDeviceMenuModel(DeviceMenuModel dmm) {
		this.dmm = dmm;
	}
}
