/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 数据库获取设备右键操作菜单对象 
 * 作    者 : 张余平
 * 开发日期 : 2010-09-14
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.mainframe.menu;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/**
 * 用 途:根据设备动态生成右键菜单
 * zyp
 * @参数1(Map)：输入参数 设备（powerDevice）
 * @参数2(Map): 输出参数 右键菜单集合
 * @返回值：无
 */
public class GetDeviceMenuModel{
	
	boolean islock=CBSystemConstants.isLock;
	boolean isoutcard=CBSystemConstants.isOutCard;
	String  cardbuidtype=CBSystemConstants.cardbuildtype;
	
	@SuppressWarnings("unused")
	public Map<String,DeviceMenuModel> execute(PowerDevice pd){
		 if(pd==null)
			 return null;
//		 if(cardbuidtype.equals("0")&&islock==true&&isoutcard==true){	
			 Map<String,DeviceMenuModel> devMenus=new LinkedHashMap<String,DeviceMenuModel>();
			 String opcode = CBSystemConstants.opRuleCode;
	         String sql= "select t.statecode,t.parentcode,t.stateorder, t.statename,t.statevalue,t.statetype,t.RUNMODEL,t.HASSIDE,t.STATEKIND,t.RUNTYPE,t.operatecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t " +
	        	         " where t.opcode='"+opcode+"' and t.devicetypeid='"+pd.getDeviceType()+"' and t.cardbuildtype='"+CBSystemConstants.cardbuildtype+"' and t.islock='0' and (statekind is null or statekind <> '1') order by t.stateorder asc";
	         List results=DBManager.queryForList(sql);
	         
	         //List results1=DBManager.queryForList(sql1);
	         Map result=null;
	         Map result1=null;
	         
	         for (int i = 0; i < results.size(); i++) {
	        	 result = (Map)results.get(i);
	        	 DeviceMenuModel dmm=new DeviceMenuModel();
	        	 String stateCode = StringUtils.ObjToString(result.get("statecode"));
	        	 dmm.setStatecode(stateCode);
	        	 dmm.setParentcode(StringUtils.ObjToString(result.get("parentcode")));
	        	 dmm.setStatevalue(StringUtils.ObjToString(result.get("statevalue")));
	        	 dmm.setStatename(StringUtils.ObjToString(result.get("statename")));
	        	 dmm.setStatetype(StringUtils.ObjToString(result.get("statetype")));
	        	 dmm.setStateorder(StringUtils.ObjToString(result.get("stateorder")));
	        	 dmm.setStatekind(StringUtils.ObjToString(result.get("STATEKIND")));
	        	 String runmodel=StringUtils.ObjToString(result.get("runmodel"));
	        	 
	        	 
	        	 if(runmodel.equals("")){
	        		 
	        	 }else{
	        		 dmm.setRunmodel(runmodel);
	        	 }
//	        	 dmm.setRunmodel(StringUtils.ObjToString(result.get("runmodel")));
	        	 String hasside=StringUtils.ObjToString(result.get("hasside"));
	        	 if(hasside.equals("0")||hasside.equals("")){
	        		
	        	 }else{
	        		 dmm.setHasside(hasside);
	        	 }
//	        	 dmm.setHasside(StringUtils.ObjToString(result.get("hasside")));
	        	 String runtype=StringUtils.ObjToString(result.get("runtype"));
	        	 if(runtype.equals("")){
	        		 
	        	 }else{
	        		 dmm.setRuntype(runtype);
	        	 }
//	        	 dmm.setRuntype(StringUtils.ObjToString(result.get("runtype")));
	        	 dmm.setOperatecode(StringUtils.ObjToString(result.get("operatecode")));
	        	 dmm.setPd(pd);
	        	 devMenus.put(stateCode, dmm);
         }
	         
	         /*
	         if(islock==true&&isoutcard==true){
		         
		 }else{
			 for (int i = 0; i < results1.size(); i++) {
	        	 result1 = (Map)results1.get(i);
	        	 DeviceMenuModel dmm=new DeviceMenuModel();
	        	 String stateCode = StringUtils.ObjToString(result1.get("statecode"));
	        	 dmm.setStatecode(stateCode);
	        	 dmm.setParentcode(StringUtils.ObjToString(result1.get("parentcode")));
	        	 dmm.setStatevalue(StringUtils.ObjToString(result1.get("statevalue")));
	        	 dmm.setStatename(StringUtils.ObjToString(result1.get("statename")));
	        	 dmm.setStatetype(StringUtils.ObjToString(result1.get("statetype")));
	        	 dmm.setStateorder(StringUtils.ObjToString(result1.get("stateorder")));
	        	 dmm.setRunmodel(StringUtils.ObjToString(result1.get("runmodel")));
	        	 dmm.setHasside(StringUtils.ObjToString(result1.get("hasside")));
	        	 dmm.setRuntype(StringUtils.ObjToString(result1.get("runtype")));
	        	 dmm.setOperatecode(StringUtils.ObjToString(result1.get("operatecode")));
	        	 dmm.setPd(pd);
	        	 devMenus.put(stateCode, dmm);
			 }
			 */
		// }
	         //点路开票
	         if(CBSystemConstants.cardbuildtype.equals("1")){
	         //动态生成保护设备
	         String bhsql="select a.protectid,a.equipid,a.protectname,a.protecttypeid,a.protectstatus,a.orderkey, b.protecttypename from "+CBSystemConstants.opcardUser+"t_a_protectequip a,"+CBSystemConstants.opcardUser+"t_a_protectinfo b where a.protecttypeid=b.protecttypeid and a.equipid='"+pd.getPowerDeviceID()+"'";
	         List bh=DBManager.queryForList(bhsql);
	         if(bh.size()>0){
	        	 for(int i=0;i<bh.size();i++){
	        		 Map dmmap=(Map) bh.get(i);
	        		 DeviceMenuModel dmml=new DeviceMenuModel();
	        		 String codel=StringUtils.ObjToString(dmmap.get("protectid"));
	        		 dmml.setStatecode(codel);
	        		 dmml.setStatevalue(StringUtils.ObjToString(dmmap.get("protectstatus")));
	        		 dmml.setParentcode("0");
	        		 dmml.setStatename(StringUtils.ObjToString(dmmap.get("protecttypename")));
	        		 dmml.setStateorder(String.valueOf(Integer.valueOf(StringUtils.ObjToString(dmmap.get("orderkey")))+100));
	        		 dmml.setOperatecode("-1");
	        		 dmml.setStatetype("1");
	        		 PowerDevice bhpd=CBSystemConstants.getProtect(pd.getPowerStationID(), codel);
	        		 dmml.setPd(pd);
	        		 devMenus.put(codel, dmml);
	        		 String protecttypeid=StringUtils.ObjToString(dmmap.get("protecttypeid"));
	        		 List r1=DBManager.query("select devicetypeid from "+CBSystemConstants.opcardUser+"t_a_protectinfo where protecttypeid='"+protecttypeid+"'");
	        		 Map r1m=(Map) r1.get(0);
	        		 String devicetypeid=r1m.get("devicetypeid").toString();
//	        		 List r2=DBManager.query("select equiptype_code from "+CBSystemConstants.opcardUser+"t_e_equiptype where equiptype_flag='"+devicetypeid+"'");
	        		 //edit 2014.6.25
	        		 List r2=DBManager.query(OPEService.getService().executeSql()+devicetypeid+"'");
	        		 Map r2m=(Map) r2.get(0);
	        		 String equiptype_code=r2m.get("equiptype_code").toString();
//	        		 List r3=DBManager.query("select b.state_id,b.state_name ,b.state_code from "+CBSystemConstants.opcardUser+"t_e_equiptypestate b where  b.equiptype_id='"+equiptype_code+"'");
	        		 List r3=DBManager.queryForList("select a.statecode, a.statename, a.statevalue, a.devicetypeid, a.statetype, a.operatecode,a.stateorder from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo a  where a.islock = '0' and  a.devicetypeid='"+devicetypeid+"' and a.cardbuildtype='0'");
	        		 for(int j=0;j<r3.size();j++){
	        			 Map dmmapl=(Map) r3.get(j);
		        		 DeviceMenuModel dmmll=new DeviceMenuModel();
		        		 String codell=StringUtils.ObjToString(dmmapl.get("statecode"))+":"+codel;
		        		 dmmll.setStatecode(codell);
		        		 dmmll.setStatevalue(StringUtils.ObjToString(dmmapl.get("statevalue")));
		        		 dmmll.setParentcode(codel);
		        		 dmmll.setStatename(StringUtils.ObjToString(dmmapl.get("statename")));
		        		 dmmll.setStateorder(StringUtils.ObjToString(dmmapl.get("stateorder")));
		        		 dmmll.setOperatecode(StringUtils.ObjToString(dmmapl.get("operatecode")));
//		        		 dmmll.setOperatecode("-1");
		        		 dmmll.setStatetype(StringUtils.ObjToString(dmmapl.get("statetype")));
		        		 dmmll.setPd(bhpd);
		        		 devMenus.put(codell, dmmll);
	        		 }
	        	 }
	         }
	         }
		return devMenus;
	
		 }
	
	
	public DeviceMenuModel getStateCode(PowerDevice pd,int operatecode){
		 if(pd==null)
			 return null;
			 Map<String,DeviceMenuModel> devMenus=new HashMap<String,DeviceMenuModel>();
			 String opcode = CBSystemConstants.opRuleCode;
			 if(CBSystemConstants.czrw.contains("送电空载")){
				 operatecode = 4;
			 }
	         String sql= "select t.statecode,t.parentcode,t.stateorder, t.statename,t.statevalue,t.statetype,t.RUNMODEL,t.HASSIDE,t.RUNTYPE,t.operatecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t " +
	        	         " where t.opcode='"+opcode+"' and t.devicetypeid='"+pd.getDeviceType()+"' and t.cardbuildtype='"+CBSystemConstants.cardbuildtype+"' and t.islock='0' and t.operatecode='"+(operatecode==4 ? "3,1,2" : operatecode)+"'  order by t.stateorder desc";
	         List results=DBManager.queryForList(sql);
	         Map result=null;
	         Map result1=null;
	         DeviceMenuModel dmm=new DeviceMenuModel();
	         for (int i = 0; i < results.size(); i++) {

	        	 result = (Map)results.get(i);
	        	
	        	 String stateCode = StringUtils.ObjToString(result.get("statecode"));
	        	 dmm.setStatecode(stateCode);
	        	 dmm.setParentcode(StringUtils.ObjToString(result.get("parentcode")));
	        	 dmm.setStatevalue(StringUtils.ObjToString(result.get("statevalue")));
	        	 dmm.setStatename(StringUtils.ObjToString(result.get("statename")));
	        	 dmm.setStatetype(StringUtils.ObjToString(result.get("statetype")));
	        	 dmm.setStateorder(StringUtils.ObjToString(result.get("stateorder")));
	        	 String runmodel=StringUtils.ObjToString(result.get("runmodel"));
	        	 if(runmodel.equals("")){
	        		 
	        	 }else{
	        		 dmm.setRunmodel(runmodel);
	        	 }
//	        	 dmm.setRunmodel(StringUtils.ObjToString(result.get("runmodel")));
	        	 String hasside=StringUtils.ObjToString(result.get("hasside"));
	        	 if(hasside.equals("0")||hasside.equals("")){
	        		
	        	 }else{
	        		 dmm.setHasside(hasside);
	        	 }
//	        	 dmm.setHasside(StringUtils.ObjToString(result.get("hasside")));
	        	 String runtype=StringUtils.ObjToString(result.get("runtype"));
	        	 if(runtype.equals("")){
	        		 
	        	 }else{
	        		 dmm.setRuntype(runtype);
	        	 }
//	        	 dmm.setRuntype(StringUtils.ObjToString(result.get("runtype")));
	        	 dmm.setOperatecode(StringUtils.ObjToString(result.get("operatecode")));
	        	 dmm.setPd(pd);
	        	 
	         }
		return dmm;
	
		 }
	
}
