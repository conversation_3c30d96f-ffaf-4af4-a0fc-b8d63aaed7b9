package com.tellhow.czp.mainframe.menu.provider;

import java.awt.BorderLayout;
import java.awt.Dialog;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowEvent;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.table.DefaultTableModel;

import org.apache.batik.dom.svg.SVGOMGElement;
import org.apache.batik.gvt.GraphicsNode;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.tellhow.graphicframework.action.impl.ChangeDeviceRectFlashingAction;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvas;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.system.DeviceSearch;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-9-4 下午02:26:02 
 */
public class SearchEquipForm extends javax.swing.JDialog {
    private DefaultTableModel dtableModel;
    private String mapType = "";
    private String powerStationID = "";
    private SVGCanvas svgCanvas = null;
    
    /** Creates new form SearchEquipForm2 */
    public SearchEquipForm(SVGCanvas svgCanvas) {
		super(new JFrame(), "查询厂站/设备",false);

        int w=(int)Toolkit.getDefaultToolkit().getScreenSize().getWidth() / 2;
        int h=(int)Toolkit.getDefaultToolkit().getScreenSize().getHeight() / 2;
        this.setLocation(w-100,h-100);
        this.setSize(300, 200);
        this.setLayout(new BorderLayout());
        initComponents();
        this.svgCanvas = svgCanvas;
        SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
        this.mapType = resolver.resolveSvgElement(svgCanvas.getSVGDocument()).getAttribute("MapType");
        if(this.mapType.equals(SystemConstants.MAP_TYPE_FAC)) {
            this.powerStationID = resolver.resolveSvgElement(svgCanvas.getSVGDocument()).getAttribute("StationID");
        }
        else if(this.mapType.equals(SystemConstants.MAP_TYPE_LINE)) {
            this.powerStationID = resolver.resolveSvgElement(svgCanvas.getSVGDocument()).getAttribute("LineID");
        }
        this.setVisible(true); 
    }

    
    private void autoToScrollToEquip(PowerDevice pd)
    {
    	SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
    	Element groupElement = resolver.getDeviceGroupElement(pd);
    	final  Document document = svgCanvas.getSVGDocument();
    	Element svgg=resolver.resolveSvgElement(document);
    	String viewbox=svgg.getAttribute("viewBox");
    	
    	
    	if(groupElement == null)
    	{
    		JOptionPane.showMessageDialog(svgCanvas, "接线图上不存在该厂站/设备！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
    		return;
    	}

    	
    	
    	SVGOMGElement gElement = (SVGOMGElement) groupElement;
 

    	
    	// Convert Screen coordinates to Document Coordinates.
    	int tagScreenX = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getX() + svgCanvas.getWidth()/2;
    	int tagScreenY = (int)SystemConstants.getGuiBuilder().getSVGJTabbedPane().getLocationOnScreen().getY() + svgCanvas.getHeight()/2;
    	GraphicsNode node = svgCanvas.getUpdateManager().getBridgeContext().getGraphicsNode(gElement);
    	  // Rectangle2D ddd = node.getTransformedBounds(node.getTransform());
    	
    	if(node!=null){
    		Rectangle2D bounds = svgCanvas.getViewBoxTransform().createTransformedShape(node.getBounds()).getBounds();  
        	int srcScreenX= (int)(bounds.getX()+bounds.getWidth()/2)+(int)svgCanvas.getLocationOnScreen().getX();
        	int srcScreenY = (int)(bounds.getY()+bounds.getHeight()/2)+(int)svgCanvas.getLocationOnScreen().getY();
        	int offX = (int)(tagScreenX-srcScreenX);
        	int offY = (int)(tagScreenY-srcScreenY);
        	AffineTransform at = svgCanvas.getRenderingTransform();
        	at.translate(offX/svgCanvas.getRenderingTransform().getScaleX(),  offY/svgCanvas.getRenderingTransform().getScaleY());
        	svgCanvas.setRenderingTransform(at, true);
        	
            //闪烁效果
        	ChangeDeviceRectFlashingAction action = new ChangeDeviceRectFlashingAction(pd, "3");
        	action.backexecute();
    		action.execute();
    	}
    }
    
    /** This method is called from within the constructor to
     * initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is
     * always regenerated by the Form Editor.
     */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {
        jPanel1 = new javax.swing.JPanel();
        jPanel2 = new javax.swing.JPanel();
        jLabel1 = new javax.swing.JLabel();
        jTextField1 = new javax.swing.JTextField();
        jButton1 = new javax.swing.JButton();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();

        jTextField1.addActionListener(new ActionListener(){
        	public void actionPerformed(ActionEvent evt)
        	{
        		String searchStr = jTextField1.getText().trim();
        		if (!searchStr.equals(""))
        			searchEquipByCondition();
        	}
        });
        jLabel1.setText("查询条件：");
        jButton1.setText("查询");
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        dtableModel = new DefaultTableModel(
            new String [] {"设备"},
            0){
            @Override
			public boolean isCellEditable(int rowIndex, int columnIndex) {
                return false;
            }
        };
        jTable1.setModel(dtableModel);
        //jTable1.getColumnModel().getColumn(0).setMinWidth(200);
        jScrollPane1.setViewportView(jTable1);
        jTable1.setSelectionMode(javax.swing.ListSelectionModel.SINGLE_SELECTION);
        javax.swing.ListSelectionModel rowSM = jTable1.getSelectionModel();
        rowSM.addListSelectionListener(new javax.swing.event.ListSelectionListener(){
        	public void valueChanged(javax.swing.event.ListSelectionEvent e)
        	{
        		searchEquip(e);
        	}
        });
        jTextField1.setColumns(16);
        jPanel1.setLayout(new BorderLayout());
        jPanel2.setLayout(new FlowLayout());
       
        jPanel2.add(jTextField1);
        jPanel2.add(jButton1);
        
        jPanel1.add(jLabel1,BorderLayout.NORTH);
        jPanel1.add(jPanel2,BorderLayout.CENTER);
        getContentPane().add(jPanel1,BorderLayout.NORTH);
        getContentPane().add(jScrollPane1,BorderLayout.CENTER);
    }

    private void searchEquip(javax.swing.event.ListSelectionEvent e)
    {
    	if (e.getValueIsAdjusting())
    		return;
    	
    	javax.swing.ListSelectionModel lsm = (javax.swing.ListSelectionModel) e.getSource();
    	PowerDevice pd = null;
    	if (!lsm.isSelectionEmpty())
    	{
    		int selectedRow = lsm.getMinSelectionIndex();
    		pd = (PowerDevice) dtableModel.getValueAt(selectedRow, 0);
    		this.autoToScrollToEquip(pd);
    	}
    }
    
    private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) 
    {
    	searchEquipByCondition();
    }
    
    private void searchEquipByCondition()
    {
    	if (this.jTextField1.getText().trim().equals(""))
    	{
    		JOptionPane.showMessageDialog(svgCanvas, "请输入目标厂站/设备名称！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
    		return;
    	}
    	
    	//-----------清空列表中已有设备数据--------------
    	int rowCount=jTable1.getRowCount();
        for(int i=rowCount-1;i>=0;i--){
            dtableModel.removeRow(i);
        }

        String searchStr = this.jTextField1.getText().trim();
        PowerDevice pd = null;
    	DeviceSearch ds = new DeviceSearch();
    	Map inMap = new HashMap();
    	Map outMap = new HashMap();
    	inMap.put("MapType", mapType);
    	if(mapType.equals(SystemConstants.MAP_TYPE_FAC)||this.mapType.equals(SystemConstants.MAP_TYPE_LINE)){
    		inMap.put("powerStationID", powerStationID);
    	}
    	inMap.put("searchStr", searchStr);
    	ds.execute(inMap, outMap);
    	ArrayList<PowerDevice> deviceList = (ArrayList<PowerDevice>)outMap.get("resultList");
    	for(Iterator iter = deviceList.iterator();iter.hasNext();) 
		{
    		pd = (PowerDevice)iter.next();
    		Object[] rowData = {pd};
			dtableModel.addRow(rowData);
		}
    	
    	
    	if (dtableModel.getRowCount() < 1)
    	{
    		JOptionPane.showMessageDialog(svgCanvas, "查询不到符合条件的厂站/设备！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
    		return;
    	}
    	
    	if (dtableModel.getRowCount() == 1)
    	{
    		autoToScrollToEquip(pd);
    		this.dispose();
    	}
    }
   
    
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton jButton1;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JPanel jPanel1;
    private javax.swing.JPanel jPanel2;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTable jTable1;
    private javax.swing.JTextField jTextField1;
    // End of variables declaration//GEN-END:variables
    
}
