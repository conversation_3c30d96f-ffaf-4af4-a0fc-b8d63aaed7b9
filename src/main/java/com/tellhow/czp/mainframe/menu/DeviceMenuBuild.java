/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 动态构建在设备右键菜单
 * 作    者 : 张余平
 * 开发日期 : 2010-09-14
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package com.tellhow.czp.mainframe.menu;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.swing.JMenu;
import javax.swing.JPopupMenu;

import com.tellhow.czp.svg.listener.PopupmenuActionListenter;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.menu.provider.DefaultMenuProvider;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.ReplaceUtil;

public class DeviceMenuBuild extends DefaultMenuProvider{

	private JPopupMenu popupMenu = new JPopupMenu();
	private Map<String,DeviceMenuModel> devMenusMap=null;
	private TreeMap<String,AbstractJMenuItem> menuItemTreeMap=new TreeMap<String,AbstractJMenuItem>();
	private TreeMap<String,JMenu> menuTreeMap=new TreeMap<String,JMenu>();


	
	public DeviceMenuBuild(PowerDevice pd) {
		super(pd);
		GetDeviceMenuModel gdmm=new GetDeviceMenuModel();
		this.devMenusMap=gdmm.execute(pd);
		//根据设备过滤菜单
		new FliterMenuByRunModel(pd,devMenusMap).execute();
		
		if(pd != null) {
			DeviceMenuModel dmm = new DeviceMenuModel();
			List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(pd, "");
			if(pd.getPowerStationID().equals("")) {
				dmm.setStatename(pd.getPowerDeviceName());
    			AbstractJMenuItem aj = new AbstractJMenuItem(dmm);
    			aj.setForeground(java.awt.Color.blue);
    			popupMenu.add(aj);
    		    popupMenu.addSeparator();
			}
			else if(CBSystemConstants.roleCode.equals("1")){ 
				if(!pd.getDeviceType().equals(SystemConstants.InOutLine) &&
						!pd.getDeviceType().equals(SystemConstants.PowerStation) &&
						!pd.getDeviceType().equals(SystemConstants.PowerFactory) &&
						!pd.getDeviceType().equals("Stability") &&
						CBSystemConstants.getMapPowerStationDevice().containsKey(pd.getPowerStationID()) && 
						CBSystemConstants.getMapPowerStationDevice().get(pd.getPowerStationID()).size()>0 && 
						devList.size() == 0) { //是设备且无相连设备
	    			dmm.setStatename(pd.getPowerDeviceName());
	    			AbstractJMenuItem aj = new AbstractJMenuItem(dmm);
	    			aj.setForeground(java.awt.Color.red);
	    			popupMenu.add(aj);
	    		    popupMenu.addSeparator();
	    		}
	    		else {
	    			String state = CBSystemConstants.getDeviceStatusName(pd.getDeviceType(), pd.getDeviceStatus());
	    			if(state.equals(""))
	    				dmm.setStatename(pd.getPowerDeviceName());
	    			else
	    				dmm.setStatename(pd.getPowerDeviceName()+"（"+CBSystemConstants.getDeviceStatusName(pd.getDeviceType(), pd.getDeviceStatus())+"）");
	    			AbstractJMenuItem aj = new AbstractJMenuItem(dmm);
	    			aj.setForeground(java.awt.Color.blue);
	    			popupMenu.add(aj);
	    		    popupMenu.addSeparator();
	    		}
			}
			else { 
				if(!pd.getDeviceType().equals(SystemConstants.PowerStation) &&
						!pd.getDeviceType().equals(SystemConstants.PowerFactory) &&
						!pd.getDeviceType().equals("Stability") &&
						CBSystemConstants.getMapPowerStationDevice().containsKey(pd.getPowerStationID()) && 
						CBSystemConstants.getMapPowerStationDevice().get(pd.getPowerStationID()).size()>0 && 
						devList.size() == 0) { //是设备且无相连设备
	    			dmm.setStatename(pd.getPowerDeviceName());
	    			AbstractJMenuItem aj = new AbstractJMenuItem(dmm);
	    			aj.setForeground(java.awt.Color.red);
	    			popupMenu.add(aj);
	    		    popupMenu.addSeparator();
	    		}
	    		else {
	    			String state = CBSystemConstants.getDeviceStatusName(pd.getDeviceType(), pd.getDeviceStatus());
	    			if(state.equals(""))
	    				dmm.setStatename(pd.getPowerDeviceName());
	    			else
	    				dmm.setStatename(pd.getPowerDeviceName()+"（"+CBSystemConstants.getDeviceStatusName(pd.getDeviceType(), pd.getDeviceStatus())+"）");
	    			AbstractJMenuItem aj = new AbstractJMenuItem(dmm);
	    			aj.setForeground(java.awt.Color.blue);
	    			popupMenu.add(aj);
	    		    popupMenu.addSeparator();
	    		}
			}
	       
		}
	}
	/**
	 * 暂时只默认有两级节点
	 */
	public JPopupMenu createMenu() {
		menuTreeMap.clear();
		DeviceMenuModel dmm=null;
		ArrayList<String> pCodes=new ArrayList<String>();
		
		/**
		 * 按预先设置的order 升序排列
		 * */
		if(devMenusMap != null) {
			List<DeviceMenuModel> sorted=new ArrayList<DeviceMenuModel>( devMenusMap.values()) ;
			Collections.sort(sorted,new Comparator<DeviceMenuModel>() {
	
				public int compare(DeviceMenuModel o1, DeviceMenuModel o2) {
					if(o1.getParentcode().equals(o2.getParentcode())) {
						if(Integer.parseInt(o1.getStateorder())>Integer.parseInt(o2.getStateorder()))
							return 1;
						else 
							return 0;
					}
					else
						return o1.getParentcode().compareTo(o2.getParentcode());
					
					
				}
	
			});
			
			for (Iterator iter = sorted.iterator(); iter.hasNext();) {
				dmm=(DeviceMenuModel) iter.next();
				boolean isParentExist = false;
				for (Iterator iter2 = sorted.iterator(); iter2.hasNext();) {
					DeviceMenuModel dmm2=(DeviceMenuModel) iter2.next();
					if(dmm.getParentcode().equals("0") || dmm.getParentcode().equals(dmm2.getStatecode())) {
						isParentExist = true;
						break;
					}
				}
				if(!isParentExist) {
					iter.remove();
					continue;
				}
			}
			
			while(sorted.size() != 0) {
				for (Iterator iter = sorted.iterator(); iter.hasNext();) {
					dmm=(DeviceMenuModel) iter.next();
					dmm.setStatename(ReplaceUtil.strReplaceSP(dmm.getStatename(), (PowerDevice) super.fPowerDevice));
					if(!dmm.getRuntype().equals("")&&!dmm.getRuntype().contains(dmm.getPd().getDeviceRunType())){
						iter.remove();
						continue;
					}
					if(dmm.getParentcode().equals("0") || menuTreeMap.containsKey(dmm.getParentcode())) {
						if(dmm.getStatetype().equals("0")) {
							
							 AbstractJMenuItem menuItem=new AbstractJMenuItem(dmm);
							 menuItem.addActionListener(new PopupmenuActionListenter());
							 menuItem.setEnabled(FilterMenuEnable.execute(dmm));
							 if(!dmm.getOperatecode().equals("-1") && 
									 StringUtils.compareStr(dmm.getPd().getDeviceStatus(), dmm.getOperatecode())==0){
								 menuItem.setEnabled(false);
							 }
							 if(dmm.getParentcode().equals("0"))
								 popupMenu.add(menuItem);
							 else {
								 menuTreeMap.get(dmm.getParentcode()).add(menuItem);
							 }
							
						}
						else if(dmm.getStatetype().equals("1")) {
							JMenu menu=new JMenu(dmm.getStatename());
							if(dmm.getParentcode().equals("0"))
								popupMenu.add(menu);
							else
								menuTreeMap.get(dmm.getParentcode()).add(menu);
							menuTreeMap.put(dmm.getStatecode(), menu);
						}
						else if(dmm.getStatetype().equals("2")) {
							if(dmm.getParentcode().equals("0"))
								popupMenu.addSeparator();
							 else {
								 menuTreeMap.get(dmm.getParentcode()).addSeparator();
							 }
						}
						iter.remove();
					}
				}
			}
		}
		
		/*
		int hasMore=0;//是否有多级菜单
		//先过滤有父级的记录
		for (Iterator iter = sorted.iterator(); iter.hasNext();) {
			dmm=(DeviceMenuModel) iter.next();
			// dmm = (DeviceMenuModel)devMenusMap.get(iter.next());
			
			 if(!dmm.getParentcode().equals("-1")){
				 
				 hasMore++;
				 if(hasMore==1){
				   popupMenu.addSeparator();
			     }
				 DeviceMenuModel parentDmm=this.devMenusMap.get(dmm.getParentcode());
				 JMenu menu=menuTreeMap.get(parentDmm.getStatecode());
				 if(menu==null){
					 menu=new JMenu(parentDmm.getStatename());
					 popupMenu.add(menu);
				 }
			
				 AbstractJMenuItem menuItem=new AbstractJMenuItem(dmm);
				 menuItem.addActionListener(new PopupmenuActionListenter());
				 menu.add(menuItem);
				 menuTreeMap.put(parentDmm.getStatecode(), menu);
				 
				 iter.remove();
				 pCodes.add(parentDmm.getStatecode());
			 }
		}
		for (int i = 0; i < pCodes.size(); i++) {
			sorted.remove(devMenusMap.get(pCodes.toArray()[i]));
		}
		
	
		Collections.sort(sorted,new Comparator<DeviceMenuModel>() {

			public int compare(DeviceMenuModel o1, DeviceMenuModel o2) {
				if(Integer.parseInt(o1.getStateorder())>Integer.parseInt(o2.getStateorder()))
				return 0;
				else return 1;
			}

		});
		//再加载排除父级后的记录
		for (Iterator iter = sorted.iterator(); iter.hasNext();) {
			// dmm = (DeviceMenuModel)devMenusMap.get(iter.next());
			dmm=(DeviceMenuModel) iter.next();
			 if(dmm.getParentcode().equals("-1")){
				 AbstractJMenuItem menuItem=new AbstractJMenuItem(dmm);
				 if(CBSystemConstants.getDeviceStateValue(dmm.getStatecode()).equals(dmm.getPd().getDeviceStatus())&&!dmm.getOperatecode().equals("-1")){
					 menuItem.setEnabled(false);
				 }
					 
				 menuItem.addActionListener(new PopupmenuActionListenter());		
				// menuItemTreeMap.put(dmm.getStatecode(),menuItem);
				 popupMenu.add(menuItem,0);
			 }
		}

		*/

		return popupMenu;
	}
}
	
