/*
 * CZPStatisticsChartJFrame.java
 *
 * Created on __DATE__, __TIME__
 */

package com.tellhow.czp.mainframe;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Font;
import java.awt.Toolkit;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JOptionPane;

import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartPanel;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.AxisLocation;
import org.jfree.chart.axis.CategoryAxis;
import org.jfree.chart.axis.NumberAxis;
import org.jfree.chart.labels.StandardCategoryItemLabelGenerator;
import org.jfree.chart.labels.StandardPieSectionLabelGenerator;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PiePlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.title.TextTitle;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;

import czprule.system.CBSystemConstants;

/**
 * 
 * <AUTHOR>
 */
public class CZPStatisticsChartJFrame extends javax.swing.JFrame {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private CallableStatement stmt;
	private Connection conn;
	ChartPanel panel;

	//	private BaseJdbcDao dao = new BaseJdbcDao();

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	public void displayChart(int year, int month) {

		List<String> zhangshu = new ArrayList<String>();
		List<String> xiangshu = new ArrayList<String>();
		List<String> maxzhangshu = new ArrayList<String>();
		List<String> listSix = new ArrayList<String>();
		List<String> maxxiangshu = new ArrayList<String>();
		List<String> listEig = new ArrayList<String>();
		int resultzs = 0;
		int resultxs = 0;
		ResultSet rs;

		//		conn = dao.getConn();
		try {
			stmt = conn
					.prepareCall("{call "+CBSystemConstants.opcardUser+"t_a_pk_ticketinfo.p_ticketOfYear(?,?)}");
			stmt.setString(1, year + "");
			stmt.registerOutParameter(2, oracle.jdbc.OracleTypes.CURSOR);
			stmt.execute();
			rs = (ResultSet) stmt.getObject(2);

			while (rs.next()) {
				String a = rs.getString(2);
				if (a != null) {
					zhangshu.add(a);
					resultzs += Integer.valueOf(a);// 统计张数
				}
				if (a == null) {
					zhangshu.add("0");
				}

				String b = rs.getString(3);
				if (b != null) {
					xiangshu.add(b);
					resultxs += Integer.valueOf(b);// 统计项数
				}
				if (b == null) {
					xiangshu.add("0");
				}

				String c = rs.getString(4);
				if (c != null) {
					maxzhangshu.add(c);
				}
				if (c == null) {
					maxzhangshu.add("0");
				}

				String d = rs.getString(6);
				if (d != null) {
					listSix.add(d);
				}
				if (d == null) {
					listSix.add("0");
				}

				String f = rs.getString(5);
				if (f != null) {
					maxxiangshu.add(f);
				}
				if (f == null) {
					maxxiangshu.add("0");
				}

				String g = rs.getString(7);
				if (g != null) {
					listEig.add(g);

				}
				if (g == null) {
					listEig.add("0");
				}
			}
		} catch (SQLException e) {
			System.err.print("统计查询有误" + e.getMessage());
			e.printStackTrace();
			//			dao.closeAll();
		} finally {
		}
		// 月统计
		if (month != 13) {
			//饼状图
			if (jComboBox1.getSelectedIndex() == 1) {
				// 构造DataSet
				DefaultPieDataset DataSet = new DefaultPieDataset();
				DataSet.setValue("张数",
						Integer.valueOf((String) zhangshu.get(month - 1)));
				DataSet.setValue("项数",
						Integer.valueOf((String) zhangshu.get(month - 1)));
				DataSet.setValue("日最多张数",
						Integer.valueOf(maxzhangshu.get(month - 1)));
				DataSet.setValue("日最多项数",
						Integer.valueOf(maxxiangshu.get(month - 1)));

				JFreeChart chart = ChartFactory.createPieChart("饼状图", DataSet,
						true, true, Locale.CHINA); // 整个图表区域

				chart.getTitle().setFont(new Font("黑体", Font.BOLD, 20)); // 设置标题字体
				PiePlot piePlot = (PiePlot) chart.getPlot(); // 获取图表区域对象
				piePlot.setLabelFont(new Font("黑体", Font.PLAIN, 12));
				chart.getLegend().setItemFont(new Font("黑体", Font.PLAIN, 12));
				//			chart.setBackgroundPaint(Color.DARK_GRAY);

				// 0、section 1、数目 2、百分比 3、总数
				piePlot.setNoDataMessage("无数据可供显示！"); // 没有数据的时候显示的内容
				piePlot.setLabelGenerator(new StandardPieSectionLabelGenerator(
						("{0}:{1}张{2}"), NumberFormat.getNumberInstance(),
						new DecimalFormat("0.00%")));

				// 图表容器
				ChartPanel panel = new ChartPanel(chart);
				panel.setSize(500, 500);
				storeChart.removeAll();
				storeChart.add(panel, BorderLayout.CENTER);
				storeChart.updateUI();

			}
			//柱状图
			if (jComboBox1.getSelectedIndex() == 2) {
				DefaultCategoryDataset DataSet = new DefaultCategoryDataset();
				int y = 1;

				DataSet.addValue(
						Integer.valueOf((String) zhangshu.get(month - 1)),
						"张数", "");
				DataSet.addValue(
						Integer.valueOf((String) zhangshu.get(month - 1)),
						"项数", "");
				DataSet.addValue(Integer.valueOf(maxzhangshu.get(month - 1)),
						"日最大张数", "");
				DataSet.addValue(Integer.valueOf(maxxiangshu.get(month - 1)),
						"日最大项数", "");
				y++;

				JFreeChart chart = ChartFactory.createBarChart3D("操作票统计", // 图表标题
						null, // 目录轴的显示标签
						"张数", // 数值轴的显示标签
						DataSet, // 数据集
						PlotOrientation.VERTICAL, // 图表方向：水平、垂直
						true, // 是否显示图例(对于简单的柱状图必须是false)
						true, // 是否生成工具
						true // 是否生成URL链接
						);

				CategoryPlot plot = chart.getCategoryPlot();// 获得图表区域对象
				// 设置图表的纵轴和横轴org.jfree.chart.axis.CategoryAxis
				CategoryAxis domainAxis = plot.getDomainAxis();
				NumberAxis numberaxis = (NumberAxis) plot.getRangeAxis();

				// 解决中文乱码问题
				TextTitle textTitle = chart.getTitle();
				textTitle.setFont(new Font("黑体", Font.PLAIN, 20));
				domainAxis.setTickLabelFont(new Font("sans-serif", Font.PLAIN,
						11));
				domainAxis.setLabelFont(new Font("宋体", Font.PLAIN, 12));
				numberaxis.setTickLabelFont(new Font("sans-serif", Font.PLAIN,
						12));
				numberaxis.setLabelFont(new Font("黑体", Font.PLAIN, 12));
				chart.getLegend().setItemFont(new Font("宋体", Font.PLAIN, 12));

				domainAxis.setLowerMargin(0.1);// 设置距离图片左端距离此时为10%
				domainAxis.setUpperMargin(0.1);// 设置距离图片右端距离此时为百分之10
				domainAxis.setCategoryLabelPositionOffset(10);// 图表横轴与标签的距离(10像素)
				domainAxis.setCategoryMargin(0.1);// 横轴标签之间的距离20%
				// domainAxis.setMaximumCategoryLabelLines(1);
				// domainAxis.setMaximumCategoryLabelWidthRatio(0);

				// 设定柱子的属性
				org.jfree.chart.axis.ValueAxis rangeAxis = plot.getRangeAxis();
				rangeAxis.setUpperMargin(0.1);// 设置最高的一个柱与图片顶端的距离(最高柱的10%)

				// 设置图表的颜色
				org.jfree.chart.renderer.category.BarRenderer3D renderer;
				renderer = new org.jfree.chart.renderer.category.BarRenderer3D();
				renderer.setBaseOutlinePaint(Color.red);
				//				renderer.setSeriesPaint(0, new Color(0, 255, 255));// 计划柱子的颜色为青色
				renderer.setSeriesOutlinePaint(0, Color.BLACK);// 边框为黑色
				//				renderer.setSeriesPaint(1, new Color(0, 255, 0));// 实报柱子的颜色为绿色
				renderer.setSeriesOutlinePaint(1, Color.red);// 边框为红色
				renderer.setItemMargin(0.1);// 组内柱子间隔为组宽的10%

				// 显示每个柱的数值，并修改该数值的字体属性
				renderer.setItemLabelGenerator(new StandardCategoryItemLabelGenerator());
				renderer.setItemLabelFont(new Font("黑体", Font.BOLD, 12));// 12号黑体加粗
				renderer.setItemLabelPaint(Color.black);// 字体为黑色
				renderer.setItemLabelsVisible(true);

				plot.setRenderer(renderer);// 使用我们设计的效果

				// 设置纵横坐标的显示位置
				plot.setDomainAxisLocation(AxisLocation.BOTTOM_OR_LEFT);// 学校显示在下端(柱子竖直)
				// 或左侧(柱子水平)
				plot.setRangeAxisLocation(AxisLocation.BOTTOM_OR_LEFT); // 人数显示在下端(柱子水平)
				// 或左侧(柱子竖直)

				ChartPanel panel = new ChartPanel(chart);

				storeChart.removeAll();
				storeChart.add(panel, BorderLayout.CENTER);
				storeChart.validate();
			}

		}
		// 年统计
		else {

			DefaultCategoryDataset DataSet = new DefaultCategoryDataset();
			int y = 1;
			for (int i = 0; i < 12; i++) {
				DataSet.addValue(Integer.valueOf(zhangshu.get(i)), "张数", y
						+ "月");
				DataSet.addValue(Integer.valueOf(xiangshu.get(i)), "项数", y
						+ "月");
				DataSet.addValue(Integer.valueOf(maxzhangshu.get(i)), "日最大张数",
						y + "月");
				DataSet.addValue(Integer.valueOf(maxxiangshu.get(i)), "日最大项数",
						y + "月");
				y++;
			}

			JFreeChart chart = ChartFactory.createBarChart3D("操作票统计", // 图表标题
					null, // 目录轴的显示标签
					"张数", // 数值轴的显示标签
					DataSet, // 数据集
					PlotOrientation.VERTICAL, // 图表方向：水平、垂直
					true, // 是否显示图例(对于简单的柱状图必须是false)
					true, // 是否生成工具
					true // 是否生成URL链接
					);

			CategoryPlot plot = chart.getCategoryPlot();// 获得图表区域对象
			// 设置图表的纵轴和横轴org.jfree.chart.axis.CategoryAxis
			CategoryAxis domainAxis = plot.getDomainAxis();
			NumberAxis numberaxis = (NumberAxis) plot.getRangeAxis();

			// 解决中文乱码问题
			TextTitle textTitle = chart.getTitle();
			textTitle.setFont(new Font("黑体", Font.PLAIN, 20));
			domainAxis.setTickLabelFont(new Font("sans-serif", Font.PLAIN, 11));
			domainAxis.setLabelFont(new Font("宋体", Font.PLAIN, 12));
			numberaxis.setTickLabelFont(new Font("sans-serif", Font.PLAIN, 12));
			numberaxis.setLabelFont(new Font("黑体", Font.PLAIN, 12));
			chart.getLegend().setItemFont(new Font("宋体", Font.PLAIN, 12));

			domainAxis.setLowerMargin(0.1);// 设置距离图片左端距离此时为10%
			domainAxis.setUpperMargin(0.1);// 设置距离图片右端距离此时为百分之10
			domainAxis.setCategoryLabelPositionOffset(10);// 图表横轴与标签的距离(10像素)
			domainAxis.setCategoryMargin(0.2);// 横轴标签之间的距离20%
			// domainAxis.setMaximumCategoryLabelLines(1);
			// domainAxis.setMaximumCategoryLabelWidthRatio(0);

			// 设定柱子的属性
			org.jfree.chart.axis.ValueAxis rangeAxis = plot.getRangeAxis();
			rangeAxis.setUpperMargin(0.1);// 设置最高的一个柱与图片顶端的距离(最高柱的10%)

			// 设置图表的颜色
			org.jfree.chart.renderer.category.BarRenderer3D renderer;
			renderer = new org.jfree.chart.renderer.category.BarRenderer3D();
			renderer.setBaseOutlinePaint(Color.red);
			//				renderer.setSeriesPaint(0, new Color(0, 255, 255));// 计划柱子的颜色为青色
			renderer.setSeriesOutlinePaint(0, Color.BLACK);// 边框为黑色
			//				renderer.setSeriesPaint(1, new Color(0, 255, 0));// 实报柱子的颜色为绿色
			renderer.setSeriesOutlinePaint(1, Color.red);// 边框为红色
			renderer.setItemMargin(0.1);// 组内柱子间隔为组宽的10%

			// 显示每个柱的数值，并修改该数值的字体属性
			renderer.setItemLabelGenerator(new StandardCategoryItemLabelGenerator());
			renderer.setItemLabelFont(new Font("黑体", Font.BOLD, 12));// 12号黑体加粗
			renderer.setItemLabelPaint(Color.black);// 字体为黑色
			renderer.setItemLabelsVisible(true);

			plot.setRenderer(renderer);// 使用我们设计的效果

			// 设置纵横坐标的显示位置
			plot.setDomainAxisLocation(AxisLocation.BOTTOM_OR_LEFT);// 学校显示在下端(柱子竖直)
			// 或左侧(柱子水平)
			plot.setRangeAxisLocation(AxisLocation.BOTTOM_OR_LEFT); // 人数显示在下端(柱子水平)
			// 或左侧(柱子竖直)

			ChartPanel panel = new ChartPanel(chart);

			storeChart.removeAll();
			storeChart.add(panel, BorderLayout.CENTER);

		}

	}

	private boolean isExistPane(String name) {

		return true;

	}

	/** Creates new form CZPStatisticsChartJFrame */
	public CZPStatisticsChartJFrame() {
		initComponents();
		this.initData();
		this.setLocationCenter();
	}

	public void initData() {
		DefaultComboBoxModel yearModel = new DefaultComboBoxModel(new String[] {
				"-请选择-", "2020", "2019", "2018", "2017", "2016", "2015",
				"2014", "2013", "2012", "2011", "2010" });

		yearChoose.setModel(yearModel);
		DefaultComboBoxModel yearModel2 = new DefaultComboBoxModel(
				new String[] { "-请选择-", "2020", "2019", "2018", "2017", "2016",
						"2015", "2014", "2013", "2012", "2011", "2010" });

		DefaultComboBoxModel monthModel = new DefaultComboBoxModel(
				new String[] { "-请选择-", "1", "2", "3", "0", "1", "6", "7", "8",
						"9", "10", "11", "12" });

		monthChoose.setModel(monthModel);

		SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
		String nowYear = yearFormat.format(new Date());
		SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
		String nowMonth = monthFormat.format(new Date());
		yearChoose.setSelectedItem(nowYear);
		// byYear.setSelectedItem(nowYear);
		monthChoose.setSelectedIndex(Integer.valueOf(nowMonth));
		jComboBox1.setModel(new DefaultComboBoxModel(new String[] { "-请选择-",
				"饼状图", "柱状图" }));
		jComboBox1.setSelectedIndex(1);
		// this.getStatisticsData(Integer.valueOf(nowYear), 6);

	}

	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jPanel1 = new javax.swing.JPanel();
		storeChart = new javax.swing.JPanel();
		jPanel2 = new javax.swing.JPanel();
		monthChoose = new javax.swing.JComboBox();
		monthLabel = new javax.swing.JLabel();
		yearChoose = new javax.swing.JComboBox();
		yearLabel = new javax.swing.JLabel();
		jLabel1 = new javax.swing.JLabel();
		jComboBox1 = new javax.swing.JComboBox();

		setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

		jPanel1.setLayout(new java.awt.BorderLayout());

		storeChart.setLayout(new java.awt.BorderLayout());
		jPanel1.add(storeChart, java.awt.BorderLayout.CENTER);

		monthChoose.setModel(new javax.swing.DefaultComboBoxModel(new String[] {
				"Item 1", "Item 2", "Item 3", "Item 4" }));
		monthChoose.addItemListener(new java.awt.event.ItemListener() {
			public void itemStateChanged(java.awt.event.ItemEvent evt) {
				monthChooseItemStateChanged(evt);
			}
		});
		monthChoose.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				monthChooseActionPerformed(evt);
			}
		});

		monthLabel.setText("\u8bf7\u9009\u62e9\u6708\u4efd\uff1a");

		yearChoose.setModel(new javax.swing.DefaultComboBoxModel(new String[] {
				"Item 1", "Item 2", "Item 3", "Item 4" }));
		yearChoose.addItemListener(new java.awt.event.ItemListener() {
			public void itemStateChanged(java.awt.event.ItemEvent evt) {
				yearChooseItemStateChanged(evt);
			}
		});
		yearChoose.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				yearChooseActionPerformed(evt);
			}
		});

		yearLabel.setText("\u8bf7\u9009\u62e9\u5e74\u4efd\uff1a");

		jLabel1.setText("\u8bf7\u9009\u62e9\u5f62\u72b6\uff1a");

		jComboBox1.setModel(new javax.swing.DefaultComboBoxModel(new String[] {
				"Item 1", "Item 2", "Item 3", "Item 4" }));
		jComboBox1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jComboBox1ActionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout jPanel2Layout = new org.jdesktop.layout.GroupLayout(
				jPanel2);
		jPanel2.setLayout(jPanel2Layout);
		jPanel2Layout.setHorizontalGroup(jPanel2Layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(
				jPanel2Layout
						.createSequentialGroup()
						.addContainerGap()
						.add(yearLabel)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(yearChoose,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.add(18, 18, 18)
						.add(monthLabel)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED)
						.add(monthChoose,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.add(18, 18, 18)
						.add(jLabel1)
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.UNRELATED)
						.add(jComboBox1,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
						.addContainerGap(220, Short.MAX_VALUE)));
		jPanel2Layout
				.setVerticalGroup(jPanel2Layout
						.createParallelGroup(
								org.jdesktop.layout.GroupLayout.LEADING)
						.add(jPanel2Layout
								.createSequentialGroup()
								.add(45, 45, 45)
								.add(jPanel2Layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(yearLabel)
										.add(yearChoose,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(monthLabel)
										.add(monthChoose,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
										.add(jLabel1)
										.add(jComboBox1,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
												org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
												org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
								.addContainerGap(56, Short.MAX_VALUE)));

		jPanel1.add(jPanel2, java.awt.BorderLayout.PAGE_START);

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jPanel1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 701,
				Short.MAX_VALUE));
		layout.setVerticalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jPanel1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 588,
				Short.MAX_VALUE));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	private void jComboBox1ActionPerformed(java.awt.event.ActionEvent evt) {
		if (yearChoose.getSelectedIndex() == 0) {
			JOptionPane.showMessageDialog(null, "请选择年份", "警告",
					JOptionPane.WARNING_MESSAGE);
		} else if (monthChoose.getSelectedIndex() == 0) {
			JOptionPane.showMessageDialog(null, "请选择月份", "警告",
					JOptionPane.WARNING_MESSAGE);
		} else if (jComboBox1.getSelectedIndex() == 0) {
			JOptionPane.showMessageDialog(null, "请选择图表形状", "警告",
					JOptionPane.WARNING_MESSAGE);
		} else
			this.query();
	}

	private void yearChooseItemStateChanged(java.awt.event.ItemEvent evt) {

	}

	private void yearChooseActionPerformed(java.awt.event.ActionEvent evt) {
		if (yearChoose.getSelectedIndex() != 0
				&& monthChoose.getSelectedIndex() != 0) {
			this.query();
		}

	}

	private void query() {
		int year = Integer.valueOf((String) yearChoose.getSelectedItem());
		int month = Integer.valueOf((String) monthChoose.getSelectedItem());
		this.displayChart(year, month);
	}

	private void monthChooseActionPerformed(java.awt.event.ActionEvent evt) {
		if (yearChoose.getSelectedIndex() == 0) {
			JOptionPane.showMessageDialog(null, "请选择年份", "警告",
					JOptionPane.WARNING_MESSAGE);
		} else if (monthChoose.getSelectedIndex() == 0) {
			JOptionPane.showMessageDialog(null, "请选择月份", "警告",
					JOptionPane.WARNING_MESSAGE);
		} else
			this.query();
	}

	private void monthChooseItemStateChanged(java.awt.event.ItemEvent evt) {

	}

	/**
	 * @param args
	 *            the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				new CZPStatisticsChartJFrame().setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JComboBox jComboBox1;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JPanel jPanel1;
	private javax.swing.JPanel jPanel2;
	private javax.swing.JComboBox monthChoose;
	private javax.swing.JLabel monthLabel;
	private javax.swing.JPanel storeChart;
	private javax.swing.JComboBox yearChoose;
	private javax.swing.JLabel yearLabel;
	// End of variables declaration//GEN-END:variables

}
