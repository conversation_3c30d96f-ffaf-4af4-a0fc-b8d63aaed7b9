package com.tellhow.czp.mainframe;

import java.awt.Toolkit;

import javax.swing.GroupLayout;
import javax.swing.ImageIcon;
import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JWindow;
import javax.swing.SwingConstants;

/**
 *
 * <AUTHOR>
 */
public class LoadWindow {
    JWindow splashFrame;
    JPanel content;
    ImageIcon img;
    
    public void loadwin(){
        splashFrame = new JWindow();
        content = new JPanel();
        img= new ImageIcon(getClass().getResource("/tellhow/icons/newload.gif"));
        //背景图片面板
		content.setLayout(null);
		content.setOpaque(false);
        //全局布局
		GroupLayout thisLayout = new GroupLayout((JComponent)splashFrame.getContentPane());
		splashFrame.getContentPane().setLayout(thisLayout);
		thisLayout.setVerticalGroup(thisLayout.createSequentialGroup()
				.addComponent(content, GroupLayout.PREFERRED_SIZE, img.getIconHeight(), GroupLayout.PREFERRED_SIZE));
		thisLayout.setHorizontalGroup(thisLayout.createSequentialGroup()
				.addComponent(content, GroupLayout.PREFERRED_SIZE, img.getIconWidth(), GroupLayout.PREFERRED_SIZE));
        //初始化组件位置
		JProgressBar pbar = new JProgressBar(SwingConstants.HORIZONTAL);
        pbar.setString("加载中...");
        pbar.setStringPainted(true);
        pbar.setIndeterminate(true);
        pbar.setBounds(0, 185, img.getIconWidth(), 25);
        //添加到内容面板
		content.add(pbar);
        //添加到主面板
		((JPanel)splashFrame.getContentPane()).setOpaque(false);
		JLabel info = new JLabel(img);
		splashFrame.getLayeredPane().add(info,new Integer(Integer.MIN_VALUE));
		info.setBounds(0, 0, img.getIconWidth(), img.getIconHeight());
		
        splashFrame.pack();
        setLocationCenter();
        splashFrame.setVisible(true);
    }
    /**
     * 屏幕中央位置
     */
    public void setLocationCenter(){
        int w=(int)Toolkit.getDefaultToolkit().getScreenSize().getWidth();
        int h=(int)Toolkit.getDefaultToolkit().getScreenSize().getHeight();
        splashFrame.setLocation((w-splashFrame.getSize().width)/2, (h-splashFrame.getSize().height)/2);
    }
    /**
     * 关闭
     */
    public void closeWindow(){
        splashFrame.setVisible(false);
        splashFrame.dispose();
    }
}
