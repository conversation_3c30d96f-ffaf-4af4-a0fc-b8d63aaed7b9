package com.tellhow.czp.mainframe;

import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.Frame;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultCellEditor;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.UIManager;
import javax.swing.event.TableModelEvent;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableCellRenderer;
import javax.swing.table.TableModel;
import javax.swing.table.TableRowSorter;

import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.model.TableField;
import czprule.model.TableParam;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013年7月25日 上午11:11:49 
 */
public class TableDialog extends JDialog {
	private JTable table;
	private JPanel comboPane;
	private String keyField;
	private String[] colNames;
	private String[] fieldIDs;
	private ArrayList newKeyValueList = new ArrayList();

	/**
	 * Create the dialog.
	 */
	public TableDialog() {
	}
	
	public TableDialog(Frame parent, boolean isModel) {
		super(parent, isModel);
	}
	
	public void init(final TableParam tableParam) {
		
		String title = tableParam.getTableTitle();
		final String tableName = tableParam.getTableName();
		final List<TableField> fieldList = tableParam.getFieldList();
		
		this.setTitle(title);
		this.setSize(tableParam.getTableWidth(), tableParam.getTableHeight());
		this.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
		getContentPane().setLayout(new BorderLayout());
		{
			JScrollPane scrollPane = new JScrollPane();
			getContentPane().add(scrollPane, BorderLayout.CENTER);
			{
				table = new JTable(){
					public boolean isCellEditable(int rowIndex, int columnIndex) {
						return true;
					}
				};
				table.setRowHeight(25);
				fieldIDs = new String[fieldList.size()];
				colNames = new String[fieldList.size()];
				String str = "";
				for (int i = 0; i < fieldList.size(); i++) {
					TableField field = fieldList.get(i);
					colNames[i] = field.getFieldName();
					if(field.isCheck())
						continue;
					fieldIDs[i] = field.getFieldID();
					str = str + field.getFieldID() + ",";
					if(field.isPrimaryKey())
						keyField = field.getFieldID();
				}
				str = str.substring(0, str.length()-1);
				String sql = "select "+str+" from " + tableName + " where 1=1 ";
				if(tableParam.getQueryCondition() != null && !tableParam.getQueryCondition().equals(""))
					sql += tableParam.getQueryCondition();
				DefaultTableModel dtm = new DefaultTableModel(
						null,
							colNames
						);

				
				table.setModel(dtm);
				
				for (int i = 0; i < fieldList.size(); i++) {
					TableField field = fieldList.get(i);
					if(!field.getFieldFillSQL().equals("")) {
						JComboBox comboBox = new JComboBox();
						fillComboBox(comboBox, field.getFieldFillSQL());
						HashMap codeMap = new HashMap();
						for (int j = 0; j < comboBox.getModel().getSize(); j++) {
							CodeNameModel cnm = (CodeNameModel)comboBox.getModel().getElementAt(j);
							codeMap.put(cnm.getCode(), cnm.getName());
						}
						field.setCodeMap(codeMap);
						DefaultCellEditor cellEditor = new DefaultCellEditor(comboBox);
						table.getColumnModel().getColumn(i).setCellEditor(cellEditor);
						//table.getColumnModel().getColumn(i).setCellRenderer(new CheckBoxCellRenderer(comboBox));
					}
					else if(field.isCheck()) {
						JCheckBox checkBox = new JCheckBox();
						DefaultCellEditor cellEditor = new DefaultCellEditor(checkBox);
						table.getColumnModel().getColumn(i).setCellEditor(cellEditor);
						table.getColumnModel().getColumn(i).setCellRenderer(new CheckBoxCellRenderer());
					}
				}
				
				List results = DBManager.queryForList(sql);
				for (int i = 0; i < results.size(); i++) {
					Map temp = (Map) results.get(i);
					Object[] obj = new Object[fieldList.size()] ;
					for(int j=0;j<fieldList.size();j++){
						TableField field = fieldList.get(j);
						if(field.getFieldFillSQL().equals(""))
							obj[j]=temp.get(field.getFieldID());
						else {
							if(temp==null || !temp.containsKey(field.getFieldID()) || temp.get(field.getFieldID())==null)
								continue;
							String code = temp.get(field.getFieldID()).toString();
							if(field.getCodeMap().containsKey(code)) {
								String name = field.getCodeMap().get(code).toString();
								obj[j] = new CodeNameModel(code, name);
							}
						}
					}
					dtm.addRow(obj);
				}
				
				TableRowSorter<TableModel> sorter = new TableRowSorter<TableModel>(dtm);
			    table.setRowSorter(sorter);
				
			    for (int i = 0; i < fieldList.size(); i++) {
					TableField field = fieldList.get(i);
					if(field.isPrimaryKey() && !field.isVisible()) {
						table.getColumnModel().getColumn(i).setMaxWidth(0);
						table.getColumnModel().getColumn(i).setMinWidth(0);
						table.getColumnModel().getColumn(i).setPreferredWidth(0);
					}
					else if(field.isCheck()) {
						table.getColumnModel().getColumn(i).setMaxWidth(30);
						table.getColumnModel().getColumn(i).setMinWidth(30);
						table.getColumnModel().getColumn(i).setPreferredWidth(30);
					}
			    }
				scrollPane.setViewportView(table);
			}
		}
		{
			JPanel operatePane = new JPanel();
			getContentPane().add(operatePane, BorderLayout.NORTH);
			operatePane.setLayout(new BorderLayout(0, 0));
			{
				{
					comboPane = new JPanel();
					operatePane.add(comboPane, BorderLayout.WEST);
				}
				{
					JPanel buttonPane = new JPanel();
					operatePane.add(buttonPane, BorderLayout.EAST);
					JButton addButton = new JButton("\u589E\u52A0");
					buttonPane.add(addButton);
					{
						JButton delButton = new JButton("\u5220\u9664");
						buttonPane.add(delButton);
						{
							JButton okButton = new JButton("\u4FDD\u5B58");
							buttonPane.add(okButton);
							okButton.addActionListener(new ActionListener() {
								public void actionPerformed(ActionEvent e) {
									if (table.getCellEditor() != null) {
										table.getCellEditor().stopCellEditing();
									}
									String sql = "";
									int rowCount = table.getModel().getRowCount();
									if(rowCount == 0)
										return;
									List sqls = new ArrayList();
									for (int i = 0; i < rowCount; i++) {
										String keyValue = table.getValueAt(i, 1).toString();
										String str1 = "";
										String str2 = "";
										boolean isNullRow = true; //判断是否空行
										for(int j=0;j<fieldList.size();j++){
											TableField field = fieldList.get(j);
											if(field.isPrimaryKey() || !field.isVisible() || field.isCheck())
												continue;
											if(table.getValueAt(i, j) != null && !table.getValueAt(i, j).toString().equals(""))
												isNullRow = false;
										}
										if(isNullRow)
											continue;
										if(!isDataValid(table)) {
											return;
										}
										if(newKeyValueList.contains(keyValue)) {
											for(int j=0;j<fieldList.size();j++){
												TableField field = fieldList.get(j);
												if(field.isCheck())
													continue;
												str1 = str1 + field.getFieldID() + ",";
											}
											str1 = str1.substring(0, str1.length()-1);
											for(int j=0;j<fieldList.size();j++){
												TableField field = fieldList.get(j);
												if(field.isCheck())
													continue;
												str2 = str2 + getStr(table.getValueAt(i, j)) + ",";
											}
											str2 = str2.substring(0, str2.length()-1);
											sql = "insert into "+tableName+"("+str1+") values("+str2+")";
											sqls.add(sql);
										}
										else {
											for(int j=0;j<fieldList.size();j++){
												TableField field = fieldList.get(j);
												if(field.isPrimaryKey() || field.isCheck())
													continue;
												str1 = str1 + fieldIDs[j] + "=" + getStr(table.getValueAt(i, j)) + ",";
											}
											str1 = str1.substring(0, str1.length()-1);
											sql = "update "+tableName+" set "+str1+" where "+keyField+"='"+keyValue+"'";
											sqls.add(sql);
										}
										for(int j=0;j<tableParam.getAfterSqlList().size();j++){
											sqls.add(tableParam.getAfterSqlList().get(j).toString().replace("?", keyValue));
										}
									}
									if(sqls.size() == 0) {
										JOptionPane.showMessageDialog(null, "数据不能为空", "提示", JOptionPane.INFORMATION_MESSAGE);
										return;
									}
									DBManager.batchUpdate((String[])sqls.toArray(new String[0]));
									newKeyValueList.clear();
									JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "保存成功！", "提示", JOptionPane.INFORMATION_MESSAGE);
								}
							});
							okButton.setActionCommand("OK");
							getRootPane().setDefaultButton(okButton);
						}
						{
							JButton cancelButton = new JButton("\u5173\u95ED");
							buttonPane.add(cancelButton);
							cancelButton.addActionListener(new ActionListener() {
								public void actionPerformed(ActionEvent e) {
									TableDialog.this.dispose();
								}
							});
							cancelButton.setActionCommand("Cancel");
						}
						delButton.addActionListener(new ActionListener() {
							public void actionPerformed(ActionEvent e) {
								if (table.getCellEditor() != null) {
									table.getCellEditor().stopCellEditing();
								}
								int rowCount = table.getModel().getRowCount();
								boolean isSelectRow = false;
								boolean isWaring = false;
								for (int i = rowCount-1; i >= 0; i--) {
									if(table.getValueAt(i, 0) == null)
										continue;
									boolean isCheck = Boolean.valueOf(table.getValueAt(i, 0).toString());
									if(isCheck) {
										isSelectRow = true;
										String keyValue = table.getValueAt(i, 1).toString();
										if(!newKeyValueList.contains(keyValue))
											isWaring = true;
									}
								}
								if(!isSelectRow) {
									JOptionPane.showMessageDialog(null, "请勾选需要删除的记录", "提示", JOptionPane.INFORMATION_MESSAGE);
									return;
								}
								int cfm;
								if(isWaring)
									cfm = JOptionPane.showConfirmDialog(null, "是否删除已勾选的记录？", "提示",JOptionPane.OK_CANCEL_OPTION);
								else
									cfm = JOptionPane.OK_OPTION;
								if (cfm == JOptionPane.OK_OPTION) {
									for (int i = rowCount-1; i >= 0; i--) {
										if(table.getValueAt(i, 0) == null)
											continue;
										boolean isCheck = Boolean.valueOf(table.getValueAt(i, 0).toString());
										if(isCheck) {
											String keyValue = table.getValueAt(i, 1).toString();
											DefaultTableModel model = (DefaultTableModel)table.getModel();
											model.removeRow(i);
											String sql = "delete from "+tableName+" where "+keyField+"='"+keyValue+"'";
											DBManager.execute(sql);
										}
									}
								}
							}
						});
					}
					addButton.addActionListener(new ActionListener() {
						public void actionPerformed(ActionEvent e) {
							int row = WindowUtils.addTableRow(table);
							String keyValue = "";
							for (int i = 0; i < fieldList.size(); i++) {
								TableField field = fieldList.get(i);
								if(field.isPrimaryKey()) {
									keyValue = StringUtils.getUUID();
									table.setValueAt(keyValue, row, i);
								}
								else if(field.isCheck()) {
									table.setValueAt(false, row, i);
								}
								else if(!field.isVisible()) {
									table.setValueAt(field.getFieldValue(), row, i);
								}
						    }
							
							newKeyValueList.add(keyValue);
						}
					});
				}
			}
		}
		
		if(tableParam.getCompList() != null) {
			for(int i = 0; i < tableParam.getCompList().size(); i++) {
				getComboPane().add(tableParam.getCompList().get(i));
			}
		}
	}
	
	public void refresh(TableParam tableParam) {
		((DefaultTableModel) table.getModel()).getDataVector().clear();   //清除表格数据
		((DefaultTableModel) table.getModel()).fireTableDataChanged();//通知模型更新
		
		List<TableField> fieldList = tableParam.getFieldList();
		String str = "";
		for (int i = 0; i < fieldList.size(); i++) {
			TableField field = fieldList.get(i);
			if(field.isCheck())
				continue;
			fieldIDs[i] = field.getFieldID();
			colNames[i] = field.getFieldName();
			str = str + field.getFieldID() + ",";
			if(field.isPrimaryKey())
				keyField = field.getFieldID();
		}
		str = str.substring(0, str.length()-1);
		String sql = "select "+str+" from " + tableParam.getTableName() + " where 1=1 ";
		if(tableParam.getQueryCondition() != null && !tableParam.getQueryCondition().equals(""))
			sql += tableParam.getQueryCondition();
		
		DefaultTableModel dtm = (DefaultTableModel)table.getModel();
		
		for (int i = 0; i < fieldList.size(); i++) {
			TableField field = fieldList.get(i);
			if(!field.getFieldFillSQL().equals("")) {
				JComboBox comboBox = new JComboBox();
				fillComboBox(comboBox, field.getFieldFillSQL());
				HashMap codeMap = new HashMap();
				for (int j = 0; j < comboBox.getModel().getSize(); j++) {
					CodeNameModel cnm = (CodeNameModel)comboBox.getModel().getElementAt(j);
					codeMap.put(cnm.getCode(), cnm.getName());
				}
				field.setCodeMap(codeMap);
				DefaultCellEditor cellEditor = new DefaultCellEditor(comboBox);
				table.getColumnModel().getColumn(i).setCellEditor(cellEditor);
				//table.getColumnModel().getColumn(i).setCellRenderer(new CheckBoxCellRenderer(comboBox));
			}
			else if(field.isCheck()) {
				JCheckBox checkBox = new JCheckBox();
				DefaultCellEditor cellEditor = new DefaultCellEditor(checkBox);
				table.getColumnModel().getColumn(i).setCellEditor(cellEditor);
				table.getColumnModel().getColumn(i).setCellRenderer(new CheckBoxCellRenderer());
			}
		}
		
		List results = DBManager.queryForList(sql);
		for (int i = 0; i < results.size(); i++) {
			Map temp = (Map) results.get(i);
			Object[] obj = new Object[fieldList.size()] ;
			for(int j=0;j<fieldList.size();j++){
				TableField field = fieldList.get(j);
				if(field.isCheck())
					obj[j]=false;
				else if(field.getFieldFillSQL().equals(""))
					obj[j]=temp.get(field.getFieldID());
				else {
					String code = temp.get(field.getFieldID()).toString();
					String name = field.getCodeMap().get(code).toString();
					obj[j] = new CodeNameModel(code, name);
				}
			}
			dtm.addRow(obj);
		}
	}
	
	public boolean isDataValid(JTable table) {
		return true;
	}
	
	private String getStr(Object obj) {
		if(obj == null)
			return "null";
		else if(obj instanceof CodeNameModel)
			return "'" + ((CodeNameModel)obj).getCode() + "'";
		else
			return "'" + obj.toString() + "'";
	}
	
	public void fillComboBox(JComboBox comboBox, String sql) {
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm=null;
		SqlRowSet set = DBManager.queryForRowSet(sql);
		while (set.next()){
			cnm=new CodeNameModel();
			cnm.setCode(set.getString(1));
			cnm.setName(set.getString(2));
			model.addElement(cnm);
		}
		comboBox.setModel(model);
	}
	
	public JPanel getComboPane() {
		return comboPane;
	}
	
	
	/**
	 * Launch the application.
	 */
	public static void main(String[] args) {
		try {
			
			try {
				UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
			}
			catch(Exception ex) {
				ex.printStackTrace();
			}
			
			
			final TableDialog dialog = new TableDialog();
			final TableParam tableParam = new TableParam();
			ArrayList<TableField> fieldList = new ArrayList<TableField>();
			
			/*
			ArrayList<Component> compList = new ArrayList<Component>();
			JComboBox combo = new JComboBox();
			dialog.fillComboBox(combo, "select t.equiptype_id,t.equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_flag in (select devicetypeid from "+CBSystemConstants.opcardUser+"t_a_devicestatevalue ) order by t.equiptype_order");
			compList.add(combo);
			
			
			fieldList.add(new TableField("PROTECTWORDID", "", "", true));
			fieldList.add(new TableField("PROTECTTYPEID", "保护类型", "select t.protecttypeid,t.protecttypename from "+CBSystemConstants.opcardUser+"t_a_protectinfo t", false));
			final TableField beginStatus = new TableField("BEGINSTATUS", "初始状态", "select a.statecode,a.statename from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo a,"+CBSystemConstants.opcardUser+"t_a_devicestatevalue b,"+CBSystemConstants.opcardUser+"t_e_equiptype c where a.statecode=b.statecode and b.devicetypeid=c.equiptype_flag and a.statevalue!=-1 and c.equiptype_id='"+((CodeNameModel)combo.getSelectedItem()).getCode()+"'", false);
			fieldList.add(beginStatus);
			final TableField endStatus = new TableField("ENDSTATUS", "目标状态", "select a.statecode,a.statename from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo a,"+CBSystemConstants.opcardUser+"t_a_devicestatevalue b,"+CBSystemConstants.opcardUser+"t_e_equiptype c where a.statecode=b.statecode and b.devicetypeid=c.equiptype_flag and a.statevalue!=-1 and c.equiptype_id='"+((CodeNameModel)combo.getSelectedItem()).getCode()+"'", false);
			fieldList.add(endStatus);
			fieldList.add(new TableField("ACTIONTYPE", "操作类型", "select '0' code, '投入' name from dual union all select '1', '退出' from dual union all select '2', '检核' from dual", false));
			fieldList.add(new TableField("ISCOMPLETED", "状态转换是否完成", " select '1' code, '是' name from dual union all select '0', '否' from dual", false));
			fieldList.add(new TableField("ACTIONWORD", "操作命令", "", false));
			
			combo.addItemListener(new java.awt.event.ItemListener() {
				public void itemStateChanged(java.awt.event.ItemEvent evt) {
					JComboBox combo = (JComboBox)evt.getSource();
					String code = ((CodeNameModel)combo.getSelectedItem()).getCode();
					tableParam.setQueryCondition("and protecttypeid in (select protecttypeid from "+CBSystemConstants.opcardUser+"t_a_protectinfo where equiptypeid='"+code+"')");
					beginStatus.setFieldFillSQL("select a.statecode,a.statename from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo a,"+CBSystemConstants.opcardUser+"t_a_devicestatevalue b,"+CBSystemConstants.opcardUser+"t_e_equiptype c where a.statecode=b.statecode and b.devicetypeid=c.equiptype_flag and a.statevalue!=-1 and c.equiptype_id='"+((CodeNameModel)combo.getSelectedItem()).getCode()+"'");
					endStatus.setFieldFillSQL("select a.statecode,a.statename from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo a,"+CBSystemConstants.opcardUser+"t_a_devicestatevalue b,"+CBSystemConstants.opcardUser+"t_e_equiptype c where a.statecode=b.statecode and b.devicetypeid=c.equiptype_flag and a.statevalue!=-1 and c.equiptype_id='"+((CodeNameModel)combo.getSelectedItem()).getCode()+"'");
					dialog.refresh(tableParam);
				}
			});
			
			tableParam.setTableTitle("保护操作管理");
			tableParam.setTableWidth(800);
			tableParam.setTableHeight(600);
			tableParam.setTableName(""+CBSystemConstants.opcardUser+"t_a_protectword");
			tableParam.setQueryCondition("and protecttypeid in (select protecttypeid from "+CBSystemConstants.opcardUser+"t_a_protectinfo where equiptypeid='"+((CodeNameModel)combo.getSelectedItem()).getCode()+"')");
			tableParam.setFieldList(fieldList);
			tableParam.setCompList(compList);
			*/
			
			
			/*
			ArrayList<TableField> fieldList = new ArrayList<TableField>();
			fieldList.add(new TableField("PROTECTTYPEID", "", "", true));
			fieldList.add(new TableField("PROTECTTYPENAME", "保护类型", "", false));
			fieldList.add(new TableField("PROTECTKIND", "保护性质", "select '0' code, '主保护' name from dual union all select '1', '后备保护' from dual", false));
			fieldList.add(new TableField("EQUIPTYPEID", "设备类型", "select t.equiptype_id,t.equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_flag in (select devicetypeid from "+CBSystemConstants.opcardUser+"t_a_devicestatevalue ) order by t.equiptype_order", false));
			
			tableParam.setTableTitle("保护类型管理");
			tableParam.setTableWidth(600);
			tableParam.setTableHeight(400);
			tableParam.setTableName(""+CBSystemConstants.opcardUser+"t_a_protectinfo");
			tableParam.setFieldList(fieldList);
			*/
			
			
			/*
			fieldList.add(new TableField("PROTECTWORDID", "", "", true));
			fieldList.add(new TableField("PROTECTTYPEID", "规则名称", "", false));
			fieldList.add(new TableField("BEGINSTATUS", "规则类名", "", false));
			String tableName = ""+CBSystemConstants.opcardUser+"t_a_protectword";
			TableDialog dialog = new TableDialog("票号生成规则类管理", new Dimension(800, 600), tableName, fieldList);
			
			
			fieldList.add(new TableField("PROTECTWORDID", "", "", true));
			fieldList.add(new TableField("PROTECTTYPEID", "指令票类型", "select '0','综令票' from dual", false));
			fieldList.add(new TableField("BEGINSTATUS", "票号规则", "select '0','江西综令票票号规则' from dual", false));
			String tableName = ""+CBSystemConstants.opcardUser+"t_a_protectword";
			TableDialog dialog = new TableDialog("票号规则管理", new Dimension(800, 600), tableName, fieldList);
			*/
			
			/*
			fieldList.add(new TableField("PROTECTWORDID", "", "", true));
			fieldList.add(new TableField("PROTECTTYPEID", "规则名称", "", false));
			fieldList.add(new TableField("BEGINSTATUS", "规则类名", "", false));
			String tableName = ""+CBSystemConstants.opcardUser+"t_a_protectword";
			TableDialog dialog = new TableDialog("新设备投运票解析规则类管理", new Dimension(800, 600), tableName, fieldList);
			*/
			/*
			fieldList.add(new TableField("PROTECTWORDID", "", "", true));
			fieldList.add(new TableField("PROTECTTYPEID", "规则名称", "", false));
			fieldList.add(new TableField("BEGINSTATUS", "规则类名", "", false));
			String tableName = ""+CBSystemConstants.opcardUser+"t_a_protectword";
			TableDialog dialog = new TableDialog("检修申请校核规则类管理", new Dimension(800, 600), tableName, fieldList);
			
			
			fieldList.add(new TableField("PROTECTWORDID", "", "", true));
			fieldList.add(new TableField("PROTECTTYPEID", "指标名称", "", false));
			fieldList.add(new TableField("BEGINSTATUS", "指标类名", "", false));
			String tableName = ""+CBSystemConstants.opcardUser+"t_a_protectword";
			TableDialog dialog = new TableDialog("操作票统计指标类管理", new Dimension(800, 600), tableName, fieldList);
			*/
			
			
			fieldList.add(new TableField("CBID", "", "", true));
			fieldList.add(new TableField("F_ZBID", "操作名称", "", false));
			fieldList.add(new TableField("BH", "操作目标状态", "select 'sd1','运行' from dual union all select 'sd2','热备用' from dual union all select 'sd3','冷备用' from dual union all select 'sd4','检修' from dual union all select 'sd5','合上' from dual union all select 'sd6','断开' from dual union all select 'sd7','推上' from dual union all select 'sd8','拉开' from dual", false));
			
			tableParam.setTableTitle("设备操作管理");
			tableParam.setTableWidth(800);
			tableParam.setTableHeight(600);
			tableParam.setTableName(""+CBSystemConstants.opcardUser+"t_a_czpcb");
			tableParam.setFieldList(fieldList);
			dialog.init(tableParam);
			dialog.setVisible(true);
			
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}

class CheckBoxCellRenderer extends JCheckBox implements TableCellRenderer {

	CheckBoxCellRenderer() {
	    setHorizontalAlignment(JLabel.CENTER);
	  }

	  public Component getTableCellRendererComponent(JTable table, Object value,
	      boolean isSelected, boolean hasFocus, int row, int column) {
	    if (isSelected) {
	      setForeground(table.getSelectionForeground());
	      setBackground(table.getSelectionBackground());
	    } else {
	      setForeground(table.getForeground());
	      setBackground(table.getBackground());
	    }
	    setSelected((value != null && ((Boolean) value).booleanValue()));
	    return this;
	  }
	}


class ComboBoxCellRenderer implements TableCellRenderer {
    JComboBox combo;
    public ComboBoxCellRenderer(JComboBox comboBox) {
    	combo = comboBox;
    }
    public Component getTableCellRendererComponent(JTable jtable, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
        
    	combo.setSelectedItem(value);
        return combo;
    }
}
