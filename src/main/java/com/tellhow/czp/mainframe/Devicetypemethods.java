package com.tellhow.czp.mainframe;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.Toolkit;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.mainframe.dao.DriverPoperyDao;
import com.tellhow.czp.staticsql.OpeInfo;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.DBManager;

public class Devicetypemethods extends javax.swing.JDialog implements ItemListener{
	private JComboBox comboBox = new JComboBox();
	private final JPanel contentPanel2 = new JPanel();
	private JTextField jTextField7=null;
	private PowerDevice choosePd = null;
	//private Object codeNameModel5;
	public Devicetypemethods(){}
	public Devicetypemethods(java.awt.Frame parent, boolean modal,JTextField jTextField7,PowerDevice choosePd) {
		super(parent, modal);
		this.jTextField7=jTextField7;
		this.choosePd=choosePd;
		this.setTitle("设备类型修改");
		initComponents();
		comboBox.addItemListener(this);
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width),
				(h - this.getSize().height) / 2);
		this.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
		this.setVisible(true);
	}
	@Override
	public void itemStateChanged(ItemEvent e) {
		// TODO Auto-generated method stub
		if(e.getStateChange() == ItemEvent.SELECTED){
			JComboBox combobox=(JComboBox) e.getSource();
			CodeNameModel codeNameModel=(CodeNameModel) combobox.getSelectedItem();
			jTextField7.setText(codeNameModel.getName());
			DriverPoperyDao dpd = new DriverPoperyDao();
			dpd.updateDevtypemethods(this.choosePd, codeNameModel);
			this.setVisible(false);
		}
	}
	public void initComponents(){
		// TODO Auto-generated method stub
		this.setBounds(100, 100, 301, 122);
		this.getContentPane().setLayout(new BorderLayout());
		contentPanel2.setBorder(new EmptyBorder(5, 5, 5, 5));
		this.getContentPane().add(contentPanel2, BorderLayout.CENTER);
		contentPanel2.setLayout(new BorderLayout(0, 0));
		JPanel panel = new JPanel();
		contentPanel2.add(panel, BorderLayout.NORTH);
		panel.setLayout(new BorderLayout(0, 0));
		JPanel voltagepanel_1 = new JPanel();
		panel.add(voltagepanel_1, BorderLayout.SOUTH);
		JLabel voltageLabel = new JLabel("设备类型：");
		voltageLabel.setPreferredSize(new Dimension(60, 15));
		voltagepanel_1.add(voltageLabel);
		comboBox.setPreferredSize(new Dimension(150, 21));
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		/*
		CodeNameModel codeNameModel=new CodeNameModel();
		codeNameModel.setCode("17");
		codeNameModel.setName("开关");
		CodeNameModel codeNameModel2=new CodeNameModel();
		codeNameModel2.setCode("20");
		codeNameModel2.setName("刀闸");
		CodeNameModel codeNameModel3=new CodeNameModel();
		codeNameModel3.setCode("18");
		codeNameModel3.setName("母线");
		CodeNameModel codeNameModel4=new CodeNameModel();
		codeNameModel4.setCode("22");
		codeNameModel4.setName("主变");
		/*CodeNameModel codeNameModel5=new CodeNameModel();
		codeNameModel5.setCode("10");
		codeNameModel5.setName("10KV");
		model.addElement(codeNameModel);
		model.addElement(codeNameModel2);
		model.addElement(codeNameModel3);
		model.addElement(codeNameModel4);
		//model.addElement(codeNameModel5);
		*/
//		List lists=	DBManager.queryForList("select t.equiptype_id,t.equiptype_name,t.equiptype_flag  from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.EQUIPTYPE_FLAG is not null and t.cim_id is not null");
		//edit 2014.6.25
		List lists= DBManager.queryForList(OPEService.getService().DevicetypemethodsSql());
		for(int i = 0; i < lists.size(); i++){
			Map map=(Map) lists.get(i);
			String EQUIPTYPE_ID=(String) map.get("EQUIPTYPE_ID");
			String EQUIPTYPE_NAME=(String) map.get("EQUIPTYPE_NAME");
			String EQUIPTYPE_FLAG=(String) map.get("EQUIPTYPE_FLAG");
			CodeNameModel codenamemodel=new CodeNameModel();
			codenamemodel.setCode(EQUIPTYPE_ID);
			codenamemodel.setName(EQUIPTYPE_NAME);
			model.addElement(codenamemodel);
			if(EQUIPTYPE_FLAG.equals(choosePd.getDeviceType()))
				model.setSelectedItem(codenamemodel);
		}
		
		comboBox.setModel(model);
//		CodeNameModel codenamemodel=new CodeNameModel();
//		String devicetype=this.choosePd.getDeviceType();
//		lists=	DBManager.queryForList("select t.equiptype_id,t.equiptype_name  from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_flag='"+devicetype+"'");
//		if(lists.size()>0){
//			Map map=(Map) lists.get(0);
//			String EQUIPTYPE_ID=(String) map.get("EQUIPTYPE_ID");
//			String EQUIPTYPE_NAME=(String) map.get("EQUIPTYPE_NAME");
//			codenamemodel.setCode(EQUIPTYPE_ID);
//			codenamemodel.setName(EQUIPTYPE_NAME);
//		}
//		model.setSelectedItem(codenamemodel);
		voltagepanel_1.add(comboBox);
		JPanel panel_2 = new JPanel();
		panel.add(panel_2, BorderLayout.NORTH);
		JPanel panel_3 = new JPanel();
		panel.add(panel_3, BorderLayout.WEST);
	}
}
