package com.tellhow.czp.mainframe.dao;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.staticsql.OpeInfo;

import czprule.model.CodeNameModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;

public class DriverPoperyDao {

	
	/**
	 * 作用：修改设备名称
	 * @param equipID 设备ID
	 * @param equipName 设备名称
	 */
	public void updateDevName(String equipID,String equipName){
		String sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.EQUIPNAME='"+equipName+"' WHERE T.EQUIPID='"+equipID+"'";
		DBManager.execute(sql);
	}
	/**
	 * 作用：修改设备电压等级
	 * @param equipID 设备ID
	 * @param equipName 电压等级名称
	 */
	public void updateDevVoltage(PowerDevice pd,CodeNameModel codeNameModel){
		String equipID = pd.getPowerDeviceID();
//		sql暂时没用到
//		String sql="select t.VOLTAGE_ID from "+CBSystemConstants.opcardUser+"t_e_equipinfo t where t.EQUIP_ID='"+equipID+"'";
		String VOLTAGE_CODE=codeNameModel.getCode();
			//查找要更新的电压等级ID
//			String sql2="select t.VOLTAGE_ID from "+CBSystemConstants.opcardUser+"t_e_voltagelevel t where t.VOLTAGE_CODE='"+VOLTAGE_CODE+"'";
//			List voltagelist2=DBManager.queryForList(sql2);
//			if(voltagelist2.size()>0){
//				Map map2=(Map)voltagelist2.get(0);
//				String VOLTAGE_ID2=(String) map2.get("VOLTAGE_ID");
//				//更新电压等级ID
//				String sql3="update "+CBSystemConstants.opcardUser+"t_e_equipinfo t SET t.VOLTAGE_ID='"+VOLTAGE_ID2+"' WHERE t.EQUIP_ID='"+equipID+"'";
//				DBManager.execute(sql3);
//				pd.setPowerVoltGrade(Double.valueOf(VOLTAGE_CODE));
//				DeviceSVGPanelUtil.setDeviceSVGColor(pd);//改变svg颜色
//			}
			DBManager.execute(OPEService.getService().updateDevVoltage(pd, codeNameModel));
			pd.setPowerVoltGrade(Double.valueOf(VOLTAGE_CODE));
			DeviceSVGPanelUtil.setDeviceSVGColor(pd);//改变svg颜色
	}
	/**
	 * 作用：修改设备类型
	 * @param equipID 设备ID
	 * @param equipName 设备类型名称
	 */
	//修改设备类型
	public void updateDevtypemethods(PowerDevice pd,CodeNameModel codeNameModel){
//		String code = codeNameModel.getCode();
//		String equipID = pd.getPowerDeviceID();
//		String sql="UPDATE "+CBSystemConstants.opcardUser+"t_e_equipinfo t SET t.EQUIPTYPE_ID='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		//修改设备id
		//edit 2014.6.24
		DBManager.execute(OPEService.getService().updateDevtypemethods(pd, codeNameModel));
//		List equiptype_flaglist=DBManager.queryForList("select t.equiptype_flag from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.equiptype_id='"+code+"'");
		//edit 2014.6.24
		List equiptype_flaglist=DBManager.queryForList(OPEService.getService().updateDevtypemethods(codeNameModel));
		if(equiptype_flaglist.size()>0){
			Map map=(Map)equiptype_flaglist.get(0);
			String EQUIPTYPE_FLAG=(String) map.get("EQUIPTYPE_FLAG");
			//修改缓存中的设备类型
			pd.setDeviceType(EQUIPTYPE_FLAG);
		}
	}
	/**
	 * 作用：修改安装类型
	 * @param equipID 设备ID
	 * @param devRunType 安装类型
	 */
	public void updateDevRunType(String equipID,String devRunType){
		String sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO  SET DEVICERUNTYPE='"+devRunType+"' WHERE EQUIPID='"+equipID+"'";
		//System.out.println(sql);
		DBManager.execute(sql);
	}
	/**
	 * 作用：修改调管机构
	 * @param equipID 设备ID
	 */
	//调管机构
	public void updateAdjustablePipeOrgan(PowerDevice pd,CodeNameModel codeNameModel){
		String code = codeNameModel.getCode();
//		String equipID = pd.getPowerDeviceID();
		//修改调管机构中的机构ID
//		String sql="UPDATE "+CBSystemConstants.opcardUser+"t_e_equipinfo t SET t.ORGA_ID='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		//edit 2014.6.24
		DBManager.execute(OPEService.getService().updateAdjustablePipeOrgan(pd, codeNameModel));
		pd.setOrgaId(code);
	}
	//许可机构
	public void updatePermitOrgan(PowerDevice pd,CodeNameModel codeNameModel){
		String code = codeNameModel.getCode();
//		String equipID = pd.getPowerDeviceID();
//		//修改调管机构中的机构ID
//		String sql="UPDATE "+CBSystemConstants.opcardUser+"t_e_equipinfo t SET t.dispatch_permission_id='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		//edit 2014.6.24
		DBManager.execute(OPEService.getService().updatePermitOrgan(pd, codeNameModel));
		pd.setPermissionOrganID(code);
	}
	//监控机构
	public void updateSupervisoryOrgan(PowerDevice pd,CodeNameModel codeNameModel){
		String code = codeNameModel.getCode();
		String equipID = pd.getPowerDeviceID();
		String sql="";
		if(CBSystemConstants.getEquiplinemap().containsKey(equipID)){
				List<String> list = CBSystemConstants.getEquiplinemap().get(equipID);
			 sql="UPDATE  "+CBSystemConstants.equipUser+"T_C_LINE t SET t.SUPERVISIONRIGHT_ID='"+code+"' WHERE t.LINE_ID='"+list.get(0)+"'";
		}else{
			 sql="UPDATE  "+CBSystemConstants.equipUser+"T_EQUIPINFO t SET t.SUPERVISIONRIGHT_ID='"+code+"' WHERE t.EQUIP_ID='"+equipID+"'";
		}
		DBManager.execute(sql);
		pd.setSupervisionright_id(code);
	}
	/**
	 * 作用：修改设备接线方式
	 * @param equipID 设备ID
	 * @param runModel 设备接线方式
	 */
	public void updateDevRunModel(String equipID,String runModel){
		String sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO  SET devicerunmodel='"+runModel+"' WHERE EQUIPID='"+equipID+"'";
		DBManager.execute(sql);
	}	
}
