#实现类
CZPImpl=com.tellhow.czp.app.yndd.impl.CZPImplZT
OMSImpl=com.tellhow.czp.app.yndd.impl.OMSImplZT
EMSImpl=com.tellhow.czp.app.yndd.impl.EMSImplZT
TempTicket=com.tellhow.czp.app.yndd.view.TempTicketZT
TempZhTicket=com.tellhow.czp.app.yndd.view.TempZhTicketYNDD
OperateTicketCZP =com.tellhow.czp.app.yndd.view.OperateTicketCZPYNDD
OperateTicketCZPMX =com.tellhow.czp.app.yndd.view.OperateTicketCZPMXYNDD
OperateTicketDXP =com.tellhow.czp.app.yndd.view.OperateTicketDXPYNDD
OperateTicketDXPMX =com.tellhow.czp.app.yndd.view.OperateTicketDXPMXYNDD
OperateTicketTypePanel = com.tellhow.czp.app.yndd.view.OperateTicketTypePanelYNDD
OperateTicketSGPZL = com.tellhow.czp.app.yndd.view.OperateTicketSGPZLGZ
OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPGZ
#OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPLine
OperateTicketSGP2 = com.tellhow.czp.app.yndd.view.OperateTicketSGPStation
WordExecute=com.tellhow.czp.app.yndd.wordcard.WordExecuteGZ
WordCardBuild=com.tellhow.czp.app.yndd.wordcard.WordCardBuild
ScaleMenuProvider=com.tellhow.czp.app.yndd.view.ScaleMenuProviderGZ
FilePattenDel=zyyh;供电局;edit
#SQL
JKStationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and e.organname='集控区' order by station,d.voltage_value desc,nodename asc
DDJKStationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d where c.station_isdel=0 and c.station_name not like '%T接%' and c.station_name not like '%系统%' and c.station_name not like '%边界%' and c.station_name not like '%AVC%' and c.station_name not like '%D5000%' and c.station_name not like '%负荷%' and c.station_name not like '%低频%' and c.station_name not like '%虚拟%' and c.station_name not like '%T%' and c.voltage_id=d.voltage_id and ('[areano]'='0' or '[areano]'='110000' or c.orga_id='[organ]') order by station,d.voltage_value desc,nodename asc
StationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d where c.station_isdel=0 and c.station_name not like '%T接%' and c.station_name not like '%系统%' and c.station_name not like '%边界%' and c.station_name not like '%AVC%' and c.station_name not like '%越南%' and c.station_name not like '%虚%' and c.station_name not like '%人工%' and c.station_name not like '%D5000%' and c.station_name not like '%负荷%' and c.station_name not like '%低频%' and c.station_name not like '%虚拟%' and c.station_name not like '%T%'  and c.station_name not like '%test%' and c.station_name not like '%æ°æ®%' and c.station_name not like '%PAS%' and c.voltage_id=d.voltage_id and ('[areano]'='0' or '[areano]'='110000' or c.orga_id='[organ]' or c.station_id in (select distinct e.station_id from equip.t_equipinfo e where e.orga_id='[organ]')) order by station,d.voltage_value desc,nodename asc
StationSearchSql=select c.station_id,substr(c.station_name,instr(c.station_name,'.')+1) station_name from equip.t_substation c,equip.t_voltagelevel a where c.voltage_id=a.voltage_id and c.station_name not like '%T接%' and c.station_name not like '%系统%' and c.station_name not like '%边界%' order by a.voltage_code desc,c.station_name
ECStationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station ,substr(c.station_name,1,instr(c.station_name,'.')-1) station_name,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and d.voltage_code='110' and c.station_name not like '%北京.%' and c.station_name not like '%T%' and c.station_name not like '%虚%' and c.station_name not like '%系统%' order by station,d.voltage_value desc,nodename asc

#无保护装置
ProtectiveDevice = 114560315521311109;

#总跳闸出口
TripExport = 114560315521310223;114560315521312478;114560315521310316;

#合环调整保护区
HHProtectedLocation = 114560315521310356;114560315521310354;114560315521310835;114560315521310775;114560315521310779;

#解环调整保护区
JHProtectedLocation = 114560315521313595;114560315521310841;

FilterMap = YN_ZT_æ­é_220kV_åææ°´çµå_èå°.fac.svg;YN_ZT_éé_110kV_ä¸­å±¯å_èå°.fac.svg;YN_ZT_æ°¸å_110kV_åååèå°æ¥çº¿å¾.fac.svg;YN_ZT_æ­é_110kV_å²©å£å.fac.svg;YN_ZT_æ­é_110kV_ä»¥åå.fac.svg;YN_ZT_å¤§å³_35kV_å³æ²³çµç«äºè½¦é´.fac.svg;YN_ZT_æ­é_110kV_ä¹å³°å.fac.svg;YN_ZT_çæ´¥_35kV_æ®æ´±åçµç«.fac.svg;YN_ZT_ç»¥æ±_35kV_ååºå¼å³ç«.fac.svg;YN_ZT_å¨ä¿¡_35kV_ä¸åªçµç«.fac.svg;YN_ZT_ç»¥æ±_35kV_ååºå¼å³ç«.fac.svg;YN_ZT_å¨ä¿¡_35kV_12å.fac.svg;YN_ZT_é²ç¸_35kV_æ¢­å±±å0824.fac.svg

