#实现类
CZPImpl=com.tellhow.czp.app.yndd.impl.CZPImplNJ
OMSImpl=com.tellhow.czp.app.yndd.impl.OMSImplNJ
EMSImpl=com.tellhow.czp.app.yndd.impl.EMSImplNJ
TempTicket=com.tellhow.czp.app.yndd.view.TempTicketNJ
TempZhTicket=com.tellhow.czp.app.yndd.view.TempZhTicketGZ
OperateTicketDXPMX =com.tellhow.czp.app.yndd.view.OperateTicketDXPMXYNDD
OperateTicketDXP =com.tellhow.czp.app.yndd.view.OperateTicketDXPYNDD
OperateTicketTypePanel = com.tellhow.czp.app.yndd.view.OperateTicketTypePanelYNDD
OperateTicketSGPZL = com.tellhow.czp.app.yndd.view.OperateTicketSGPZLGZ
OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPGZ
#OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPLine
OperateTicketSGP2 = com.tellhow.czp.app.yndd.view.OperateTicketSGPStation
WordExecute=com.tellhow.czp.app.yndd.wordcard.WordExecuteGZ
WordCardBuild=com.tellhow.czp.app.yndd.wordcard.WordCardBuild
ScaleMenuProvider=com.tellhow.czp.app.yndd.view.ScaleMenuProviderGZ
FilePattenDel=zyyh;供电局;edit
#SQL
JKStationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and e.organname='集控区' order by station,d.voltage_value desc,nodename asc
DDJKStationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d where c.station_isdel=0 and c.station_name not like '%T接%' and c.station_name not like '%系统%' and c.station_name not like '%边界%' and c.station_name not like '%AVC%' and c.station_name not like '%D5000%' and c.station_name not like '%负荷%' and c.station_name not like '%低频%' and c.station_name not like '%虚拟%' and c.station_name not like '%T%' and c.voltage_id=d.voltage_id and ('[areano]'='0' or '[areano]'='110000' or c.orga_id='[organ]') order by station,d.voltage_value desc,nodename asc
StationTreeSql=select stationtype as station,volt as voltage_code,stationid  as nodecode,stationname nodename from equip.t_substation_tree where isremove = '0' order by to_number(replace(voltage_code,'kV','')) desc,nodename asc
StationSearchSql=select stationid as station_id,stationname as station_name from equip.t_substation_tree where isremove = '0' order by to_number(replace(volt,'kV','')) desc
ECStationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station ,substr(c.station_name,1,instr(c.station_name,'.')-1) station_name,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and d.voltage_code='110' and c.station_name not like '%北京.%' and c.station_name not like '%T%' and c.station_name not like '%虚%' and c.station_name not like '%系统%' order by station,d.voltage_value desc,nodename asc


#xianchang
WebserviceUrl = http://************:18088/powernet-graphic-czpinerfaceoms/services/CzpService?wsdl

#gongsi
#WebserviceUrl = http://**************:18085/powernet-graphic-czpinerfaceoms/services/CzpService?wsdl

#local
#WebserviceUrl = http://127.0.0.1:18085/powernet-graphic-czpinerfaceoms/services/CzpService?wsdl