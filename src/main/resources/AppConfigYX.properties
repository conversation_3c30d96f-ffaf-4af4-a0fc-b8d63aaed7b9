#å®ç°ç±»
CZPImpl=com.tellhow.czp.app.yndd.impl.CZPImplYX
OMSImpl=com.tellhow.czp.app.yndd.impl.OMSImplGZ
EMSImpl=com.tellhow.czp.app.yndd.impl.EMSImplYX
TempTicket=com.tellhow.czp.app.yndd.view.TempTicketYX
TempZhTicket=com.tellhow.czp.app.yndd.view.TempZhTicketGZ
OperateTicketDXPMX =com.tellhow.czp.app.yndd.view.OperateTicketDXPMXYNDD
OperateTicketDXP =com.tellhow.czp.app.yndd.view.OperateTicketDXPYNDD
OperateTicketTypePanel = com.tellhow.czp.app.yndd.view.OperateTicketTypePanelYNDD
OperateTicketSGPZL = com.tellhow.czp.app.yndd.view.OperateTicketSGPZLGZ
OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPGZ
#OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPLine
OperateTicketSGP2 = com.tellhow.czp.app.yndd.view.OperateTicketSGPStation
WordExecute=com.tellhow.czp.app.yndd.wordcard.WordExecuteGZ
WordCardBuild=com.tellhow.czp.app.yndd.wordcard.WordCardBuild
ScaleMenuProvider=com.tellhow.czp.app.yndd.view.ScaleMenuProviderGZ
FilePattenDel=zyyh;ä¾çµå±;edit

#SQL
JKStationTreeSql=select decode(c.station_type,'0','åçµç«','1','å¼å³ææå¼é­æ','2','éæ§ç«','3','ç«çµå','4','æ°´çµå','5','å°ç«çµ','6','å°æ°´çµ') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and e.organname='éæ§åº' order by station,d.voltage_value desc,nodename asc
DDJKStationTreeSql=select decode(c.station_type,'0','åçµç«','1','å¼å³ææå¼é­æ','2','éæ§ç«','3','ç«çµå','4','æ°´çµå','5','å°ç«çµ','6','å°æ°´çµ') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d where c.station_isdel=0 and c.station_name not like '%Tæ¥%' and c.station_name not like '%ç³»ç»%' and c.station_name not like '%è¾¹ç%' and c.station_name not like '%AVC%' and c.station_name not like '%D5000%' and c.station_name not like '%è´è·%' and c.station_name not like '%ä½é¢%' and c.station_name not like '%èæ%' and c.station_name not like '%T%' and c.voltage_id=d.voltage_id and ('[areano]'='0' or '[areano]'='110000' or c.orga_id='[organ]') order by station,d.voltage_value desc,nodename asc
StationTreeSql=select decode(c.station_type,'0','åçµç«','1','å¼å³ææå¼é­æ','2','éæ§ç«','3','ç«çµå','4','æ°´çµå','5','å°ç«çµ','6','å°æ°´çµ') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d where c.station_isdel=0 and c.station_name not like '%Tæ¥%' and c.station_name not like '%ç³»ç»%' and c.station_name not like '%è¾¹ç%' and c.station_name not like '%AVC%' and c.station_name not like '%è¶å%' and c.station_name not like '%è%' and c.station_name not like '%äººå·¥%' and c.station_name not like '%D5000%' and c.station_name not like '%è´è·%' and c.station_name not like '%ä½é¢%' and c.station_name not like '%èæ%' and c.station_name not like '%T%' and c.station_name not like '%tase%' and d.voltage_code != '10' and c.station_name != 'ç¨æ·å' and c.station_name != 'å¤ç½ç­å¼ç«' and c.voltage_id=d.voltage_id and ('[areano]'='0' or '[areano]'='110000' or c.orga_id='[organ]' or c.station_id in (select distinct e.station_id from equip.t_equipinfo e where e.orga_id='[organ]')) order by station,d.voltage_value desc,nodename asc
StationSearchSql=select c.station_id,substr(c.station_name,instr(c.station_name,'.')+1) station_name from equip.t_substation c,equip.t_voltagelevel a where c.voltage_id=a.voltage_id and c.station_name not like '%Tæ¥%' and c.station_name not like '%ç³»ç»%' and c.station_name not like '%è¾¹ç%' order by a.voltage_code desc,c.station_name
ECStationTreeSql=select decode(c.station_type,'0','åçµç«','1','å¼å³ææå¼é­æ','2','éæ§ç«','3','ç«çµå','4','æ°´çµå','5','å°ç«çµ','6','å°æ°´çµ') station ,substr(c.station_name,1,instr(c.station_name,'.')-1) station_name,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and d.voltage_code='110' and c.station_name not like '%åäº¬.%' and c.station_name not like '%T%' and c.station_name not like '%è%' and c.station_name not like '%ç³»ç»%' order by station,d.voltage_value desc,nodename asc


#xianchang
#WebserviceUrl = http://************:18088/powernet-graphic-czpinerfaceoms/services/CzpService?wsdl

#gongsi
#WebserviceUrl = http://**************:18082/powernet-graphic-czpinerfaceoms/services/CzpService?wsdl

#local
WebserviceUrl = http://127.0.0.1:18082/powernet-graphic-czpinerfaceoms/services/CzpService?wsdl