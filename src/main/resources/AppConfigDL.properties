#实现类
CZPImpl=com.tellhow.czp.app.yndd.impl.CZPImplDL
OMSImpl=com.tellhow.czp.app.yndd.impl.OMSImplGZ
EMSImpl=com.tellhow.czp.app.yndd.impl.EMSImplDL
TempTicket=com.tellhow.czp.app.yndd.view.TempTicketDL
TempZhTicket=com.tellhow.czp.app.yndd.view.TempZhTicketYNDD
OperateTicketCZP =com.tellhow.czp.app.yndd.view.OperateTicketCZPYNDD
OperateTicketCZPMX =com.tellhow.czp.app.yndd.view.OperateTicketCZPMXYNDD
OperateTicketDXP =com.tellhow.czp.app.yndd.view.OperateTicketDXPYNDD
OperateTicketDXPMX =com.tellhow.czp.app.yndd.view.OperateTicketDXPMXYNDD
OperateTicketTypePanel = com.tellhow.czp.app.yndd.view.OperateTicketTypePanelYNDD
OperateTicketSGPZL = com.tellhow.czp.app.yndd.view.OperateTicketSGPZLGZ
OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPGZ
#OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPLine
OperateTicketSGP2 = com.tellhow.czp.app.yndd.view.OperateTicketSGPStation
WordExecute=com.tellhow.czp.app.yndd.wordcard.WordExecuteGZ
WordCardBuild=com.tellhow.czp.app.yndd.wordcard.WordCardBuild
ScaleMenuProvider=com.tellhow.czp.app.yndd.view.ScaleMenuProviderGZ
FilePattenDel=zyyh;供电局;edit
#SQL
JKStationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and e.organname='集控区' order by station,d.voltage_value desc,nodename asc
DDJKStationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d where c.station_isdel=0 and c.station_name not like '%T接%' and c.station_name not like '%系统%' and c.station_name not like '%边界%' and c.station_name not like '%AVC%' and c.station_name not like '%D5000%' and c.station_name not like '%负荷%' and c.station_name not like '%低频%' and c.station_name not like '%虚拟%' and c.station_name not like '%T%' and c.voltage_id=d.voltage_id and ('[areano]'='0' or '[areano]'='110000' or c.orga_id='[organ]') order by station,d.voltage_value desc,nodename asc
StationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station,a.volt voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d,equip.t_Substation_Tree a where c.station_isdel=0 and  a.stationid = c.station_id and a.isremove = '0' and c.voltage_id=d.voltage_id and ('[areano]'='0' or '[areano]'='110000' or c.orga_id='[organ]' or c.station_id in (select distinct e.station_id from equip.t_equipinfo e where e.orga_id='[organ]')) order by station,d.voltage_value desc,nodename asc
StationSearchSql=select c.station_id,substr(c.station_name,instr(c.station_name,'.')+1) station_name from equip.t_substation c,equip.t_voltagelevel a where c.voltage_id=a.voltage_id and c.station_name not like '%T接%' and c.station_name not like '%系统%' and c.station_name not like '%边界%' order by a.voltage_code desc,c.station_name
ECStationTreeSql=select decode(c.station_type,'0','变电站','1','开关所或开闭所','2','集控站','3','火电厂','4','水电厂','5','小火电','6','小水电') station ,substr(c.station_name,1,instr(c.station_name,'.')-1) station_name,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and d.voltage_code='110' and c.station_name not like '%北京.%' and c.station_name not like '%T%' and c.station_name not like '%虚%' and c.station_name not like '%系统%' order by station,d.voltage_value desc,nodename asc


#StationTreeSql=select '' station,t.stationtype indeed,t.volt pathname,s.station_id nodecode,s.station_name nodename from opcardkm.t_substation_tree t,opcardkm.t_substation s where instr(s.station_name,t.shortname)>0 and t.isremove = '0' order by to_number(t.xh), stationtype, to_number(regexp_replace(t.volt,'[^0-9.]','')) desc
#StationSearchSql=select c.station_id,substr(c.station_name,instr(c.station_name,'.')+1) station_name from equip.t_substation c,equip.t_voltagelevel a where c.voltage_id=a.voltage_id and c.station_name not like '%T接%' and c.station_name not like '%系统%' and c.station_name not like '%边界%' order by a.voltage_code desc,c.station_name
