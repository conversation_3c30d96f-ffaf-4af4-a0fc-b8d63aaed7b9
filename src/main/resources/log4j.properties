log4j.rootLogger=INFO,File
log4j.category.org.springframework = ERROR
log4j.category.org.hibernate=ERROR
log4j.logger.org.beryl.gui=ERROR
log4j.category.com.alibaba.dubbo=ERROR
log4j.category.org.beryl=File
log4j.category.org.beryl.additivity=false


log4j.appender.File=org.apache.log4j.ConsoleAppender

#print log to file
#log4j.appender.File=org.apache.log4j.RollingFileAppender
#log4j.appender.File.File=/czp.log
#log4j.appender.File.MaxFileSize=10MB

log4j.appender.File.Threshold=ALL
log4j.appender.File.layout=org.apache.log4j.PatternLayout
log4j.appender.File.layout.ConversionPattern=[%p][%t][%d{yyyy-MM-dd HH\:mm\:ss}][%C %l] - %m%n