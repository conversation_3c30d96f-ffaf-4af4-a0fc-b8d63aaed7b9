#实现类
CZPImpl=com.tellhow.czp.app.yndd.impl.CZPImplHH
OMSImpl=com.tellhow.czp.app.yndd.impl.OMSImplGZ
EMSImpl=com.tellhow.czp.app.yndd.impl.EMSImplHH
TempTicket=com.tellhow.czp.app.yndd.view.TempTicketHH
TempZhTicket=com.tellhow.czp.app.yndd.view.TempZhTicketGZ
OperateTicketDXPMX =com.tellhow.czp.app.yndd.view.OperateTicketDXPMXYNDD
OperateTicketDXP =com.tellhow.czp.app.yndd.view.OperateTicketDXPYNDD
OperateTicketTypePanel = com.tellhow.czp.app.yndd.view.OperateTicketTypePanelYNDD
OperateTicketSGPZL = com.tellhow.czp.app.yndd.view.OperateTicketSGPZLGZ
OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPGZ
#OperateTicketSGP = com.tellhow.czp.app.yndd.view.OperateTicketSGPLine
OperateTicketSGP2 = com.tellhow.czp.app.yndd.view.OperateTicketSGPStation
WordExecute=com.tellhow.czp.app.yndd.wordcard.WordExecuteGZ
WordCardBuild=com.tellhow.czp.app.yndd.wordcard.WordCardBuild
ScaleMenuProvider=com.tellhow.czp.app.yndd.view.ScaleMenuProviderGZ
FilePattenDel=zyyh;¹©µç¾Ö;edit
#SQL
JKStationTreeSql=select decode(c.station_type,'0','åçµç«','1','å¼å³ææå¼é­æ','2','éæ§ç«','3','ç«çµå','4','æ°´çµå','5','å°ç«çµ','6','å°æ°´çµ') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and e.organname='éæ§åº' order by station,d.voltage_value desc,nodename asc
DDJKStationTreeSql=select decode(c.station_type,'0','åçµç«','1','å¼å³ææå¼é­æ','2','éæ§ç«','3','ç«çµå','4','æ°´çµå','5','å°ç«çµ','6','å°æ°´çµ') station,d.voltage_code||'kV' voltage_code,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d where c.station_isdel=0 and c.station_name not like '%Tæ¥%' and c.station_name not like '%ç³»ç»%' and c.station_name not like '%è¾¹ç%' and c.station_name not like '%AVC%' and c.station_name not like '%D5000%' and c.station_name not like '%è´è·%' and c.station_name not like '%ä½é¢%' and c.station_name not like '%èæ%' and c.station_name not like '%T%' and c.voltage_id=d.voltage_id and ('[areano]'='0' or '[areano]'='110000' or c.orga_id='[organ]') order by station,d.voltage_value desc,nodename asc
#StationTreeSql=select '' station,t.stationtype indeed,t.volt pathname,s.station_id nodecode,s.station_name nodename from opcardhh.t_substation_tree t,opcardhh.t_substation s where  s.station_id = t.stationid   order by  to_number(t.xh), nlssort(replace(replace(replace(t.stationname,'220kV',''),'110kV',''),'35kV',''),'NLS_SORT=SCHINESE_PINYIN_M')
StationTreeSql=SELECT * FROM (SELECT '' station, CASE WHEN s.station_name LIKE '%变' THEN '变电站' WHEN s.station_name LIKE '%电站' THEN '电厂' END as indeed, v.voltage_value || 'kV' as pathname, s.station_id as nodecode, s.station_name as nodename, v.voltage_value as voltage_sort FROM opcardhh.t_substation s JOIN opcardhh.T_VOLTAGELEVEL v ON s.VOLTAGE_ID = v.voltage_id WHERE s.station_name LIKE '%变' OR s.station_name LIKE '%电站' UNION ALL SELECT '' station, t.stationtype as indeed, SUBSTR(t.volt, 1, INSTR(t.volt, 'kV') + 1) as pathname, s.station_id as nodecode, s.station_name as nodename, to_number(SUBSTR(t.volt, 1, INSTR(t.volt, 'kV')-1)) as voltage_sort FROM opcardhh.t_substation_tree t JOIN opcardhh.t_substation s ON s.station_id = t.stationid WHERE t.stationtype = '客户') ORDER BY DECODE(indeed, '变电站', 1, '电厂', 2, '客户', 3, 4), voltage_sort DESC, nlssort(replace(replace(replace(nodename,'220kV',''),'110kV',''),'35kV',''),'NLS_SORT=SCHINESE_PINYIN_M')
#StationTreeSql=select '' station,t.stationtype indeed,t.volt pathname,s.station_id nodecode,s.station_name nodename from opcardhh.t_substation_tree t,opcardhh.t_substation s where  s.station_id =  t.stationid and t.isremove = '0' order by  to_number(t.xh), nlssort(replace(replace(replace(t.stationname,'220kV',''),'110kV',''),'35kV',''),'NLS_SORT=SCHINESE_PINYIN_M')  
StationSearchSql=select c.station_id,substr(c.station_name,instr(c.station_name,'.')+1) station_name from equip.t_substation c,equip.t_voltagelevel a where c.voltage_id=a.voltage_id and c.station_name not like '%T½Ó%' and c.station_name not like '%ÏµÍ³%' and c.station_name not like '%±ß½ç%' order by a.voltage_code desc,c.station_name
ECStationTreeSql=select decode(c.station_type,'0','±äµçÕ¾','1','¿ª¹ØËù»ò¿ª±ÕËù','2','¼¯¿ØÕ¾','3','»ðµç³§','4','Ë®µç³§','5','Ð¡»ðµç','6','Ð¡Ë®µç') station ,substr(c.station_name,1,instr(c.station_name,'.')-1) station_name,c.station_id nodecode,c.station_name nodename from equip.t_substation c,equip.t_voltagelevel d ,platform.t_tbp_organ e where c.station_isdel=0 and c.voltage_id=d.voltage_id  and c.orga_id=e.organid and d.voltage_code='110' and c.station_name not like '%±±¾©.%' and c.station_name not like '%T%' and c.station_name not like '%Ðé%' and c.station_name not like '%ÏµÍ³%' order by station,d.voltage_value desc,nodename asc
