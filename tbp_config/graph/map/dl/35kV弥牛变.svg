<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549593047042" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id=":线路发电机1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="30"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.3333333333333321" y2="29.99999999999999"/>
  </symbol>
  <symbol id="Accessory:35kVPT4561_0" viewBox="0,0,40,25">
   <use terminal-index="0" type="0" x="2.25" xlink:href="#terminal" y="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.25" x2="17.75" y1="13" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.859556584393186" x2="7.859556584393186" y1="17.48802716924653" y2="17.48802716924653"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.44827268622592" x2="7.44827268622592" y1="17.80194186387092" y2="17.80194186387092"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.48218200993318" x2="2.227635318869364" y1="13.02857511364184" y2="13.02857511364184"/>
   <rect fill-opacity="0" height="10.1" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(90,7.37,13.07) scale(1,1) translate(0,0)" width="5.26" x="4.74" y="8.02"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="22.25" y1="4" y2="13"/>
   <ellipse cx="21.96" cy="13.18" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.01963465760507" x2="15.01963465760507" y1="9.329745300799431" y2="9.329745300799431"/>
   <ellipse cx="25.46" cy="19.43" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.59047479452226" x2="22.11428617960368" y1="11.06018441966319" y2="13.0105740510387"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.11428617960369" x2="24.76741768080052" y1="13.01057405103871" y2="12.50818132594489"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.11428617960368" x2="20.98496606348827" y1="13.01057405103871" y2="15.46335640750804"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.66666666666666" x2="26.06666666666666" y1="20.06919649193173" y2="21.30673647628384"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46666666666665" x2="26.46666666666665" y1="17.83333333333334" y2="21.18381314555852"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.76666666666667" x2="26.16666666666666" y1="18.95042649975577" y2="17.71288651540366"/>
   <ellipse cx="29.21" cy="13.18" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.26963465760507" x2="21.26963465760507" y1="9.329745300799431" y2="9.329745300799431"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.36428617960369" x2="32.01741768080052" y1="13.01057405103871" y2="12.50818132594489"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.36428617960368" x2="28.23496606348827" y1="13.01057405103871" y2="15.46335640750804"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.84047479452226" x2="29.36428617960368" y1="11.06018441966319" y2="13.0105740510387"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.69828218872542" x2="37.69828218872542" y1="6.025677169707021" y2="1.91898839137645"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="39.56619958988381" x2="39.56619958988381" y1="4.827892942693938" y2="2.945660585959093"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="38.63224088930464" x2="38.63224088930464" y1="5.341229039985261" y2="2.60343652109821"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.22848029163819" x2="37.66210982000456" y1="4.061608623548921" y2="4.061608623548921"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="22.33333333333333" x2="24.41234905445432" y1="4.061608623548921" y2="4.061608623548921"/>
   <rect fill-opacity="0" height="10.82" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(-90,29.82,4.07) scale(1,1) translate(0,0)" width="5.38" x="27.13" y="-1.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.17243107769424" x2="29.14440647072226" y1="4.061608623548917" y2="5.668573797678272"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.41234905445432" x2="31.10483025746183" y1="4.061608623548921" y2="4.061608623548921"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.17243107769424" x2="29.14440647072226" y1="4.061608623548917" y2="2.454643449419562"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.01340482573727" x2="10.01340482573727" y1="11.16666666666667" y2="12.83842547222383"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6934763181412" x2="10.01340482573727" y1="14.51018427778098" y2="12.83842547222382"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.333333333333332" x2="10.01340482573726" y1="14.51018427778098" y2="12.83842547222382"/>
  </symbol>
  <symbol id="Accessory:线路PT带避雷器0904_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,11) scale(1,1) translate(0,0)" width="6" x="7" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.988213005145578" x2="9.988213005145578" y1="1.029523490692871" y2="18.75"/>
   <ellipse cx="9.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Accessory:FS-2WU型信号电源_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.06516290726816" xlink:href="#terminal" y="1.16820085279563"/>
   <rect fill-opacity="0" height="3.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.02,4.34) scale(1,1) translate(0,0)" width="1.58" x="14.23" y="2.72"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13.94736842105263" y1="8.947368421052632" y2="10"/>
   <ellipse cx="15" cy="13.25" fill-opacity="0" rx="2.35" ry="2.35" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="16.05263157894737" y1="8.947368421052632" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.47368421052632" x2="19.47368421052632" y1="13.42105263157895" y2="17.63157894736842"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="7.10526315789474" y2="0.9649122807017569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="19.47368421052632" y1="13.42105263157895" y2="13.42105263157895"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="7.894736842105263" y2="8.947368421052632"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.015664160401" x2="15.015664160401" y1="25.87719298245614" y2="27.54385964912281"/>
   <rect fill-opacity="0" height="8.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.78,21.62) scale(1,1) translate(0,0)" width="15" x="7.28" y="17.46"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.07957393483709" x2="16.95175438596491" y1="28.42949815407688" y2="28.42949815407688"/>
   <text fill="rgb(0,0,0)" font-family="宋体" font-size="12" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="14.94736842105263" xml:space="preserve" y="22.75622831828122">EFS-2WU型</text>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.14348370927318" x2="18.88784461152882" y1="27.61638503774951" y2="27.61638503774951"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.14442355889723" x2="16.08051378446114" y1="29.24261127040424" y2="29.24261127040424"/>
   <text fill="rgb(0,0,0)" font-family="宋体" font-size="12" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="14.92982456140351" xml:space="preserve" y="27.16741467092673">信号电源</text>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="16.05263157894737" y1="13.42105263157895" y2="14.47368421052632"/>
   <ellipse cx="15" cy="9.470000000000001" fill-opacity="0" rx="2.35" ry="2.35" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.36842105263158" y2="13.42105263157895"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13.94736842105263" y1="13.42105263157895" y2="14.47368421052632"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(85,255,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV弥牛变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="35kV弥牛变_软压板.svg"><rect fill-opacity="0" height="40" width="97.14" x="65.52" y="497.48" zvalue="731"/></g>
  <g href="35kV弥牛变_直流监控.svg"><rect fill-opacity="0" height="40" width="97.14" x="65.52" y="553.48" zvalue="732"/></g>
  <g href="单厂站信息-dali.svg"><rect fill-opacity="0" height="40" width="97.14" x="64" y="608" zvalue="958"/></g>
 </g>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,156.06,49.125) scale(1,1) translate(0,0)" writing-mode="lr" x="156.06" xml:space="preserve" y="66.13" zvalue="729">     弥牛变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="46" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,114.091,517.484) scale(1,1) translate(0,0)" width="97.14" x="65.52" y="497.48" zvalue="731"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,114.091,517.484) scale(1,1) translate(0,0)" writing-mode="lr" x="114.09" xml:space="preserve" y="521.48" zvalue="731">软压板</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="45" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,114.091,573.484) scale(1,1) translate(0,0)" width="97.14" x="65.52" y="553.48" zvalue="732"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,114.091,573.484) scale(1,1) translate(0,0)" writing-mode="lr" x="114.09" xml:space="preserve" y="577.48" zvalue="732">直流监控</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="15" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,146.714,149.143) scale(1,1) translate(0,0)" width="97.14" x="98.14" y="129.14" zvalue="956"/>
  <text fill="rgb(255,170,0)" font-family="FangSong" font-size="19" id="5" stroke="rgb(255,170,0)" text-anchor="middle" transform="rotate(0,146.714,149.143) scale(1,1) translate(0,0)" writing-mode="lr" x="146.71" xml:space="preserve" y="155.64" zvalue="956">全站可控</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="18" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,112.571,628) scale(1,1) translate(0,0)" width="97.14" x="64" y="608" zvalue="958"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,112.571,628) scale(1,1) translate(0,0)" writing-mode="lr" x="112.57" xml:space="preserve" y="637" zvalue="958">AVC</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1450.23,412.987) scale(1,1) translate(0,0)" writing-mode="lr" x="1450.23" xml:space="preserve" y="415.99" zvalue="3">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,364.501,809.055) scale(1,1) translate(0,0)" writing-mode="lr" x="364.5" xml:space="preserve" y="812.0599999999999" zvalue="42">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1249.3,579.418) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.3" xml:space="preserve" y="582.42" zvalue="95">35kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1274.17,464.587) scale(1,1) translate(0,0)" writing-mode="lr" x="1274.17" xml:space="preserve" y="467.59" zvalue="96">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1286.83,519.198) scale(1,1) translate(0,0)" writing-mode="lr" x="1286.83" xml:space="preserve" y="522.2" zvalue="103">39010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,594.395,1087.89) scale(1,1) translate(0,0)" writing-mode="lr" x="594.4" xml:space="preserve" y="1090.89" zvalue="153">10kV大桥线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,605.366,862.927) scale(1,1) translate(0,0)" writing-mode="lr" x="605.37" xml:space="preserve" y="865.9299999999999" zvalue="155">0611</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,607.412,911.801) scale(1,1) translate(0,0)" writing-mode="lr" x="607.41" xml:space="preserve" y="914.8" zvalue="158">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,607.982,960.889) scale(1,1) translate(0,0)" writing-mode="lr" x="607.98" xml:space="preserve" y="963.89" zvalue="159">0612</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,369.078,860.173) scale(1,1) translate(0,0)" writing-mode="lr" x="369.08" xml:space="preserve" y="863.17" zvalue="201">4614</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.796,115.96) scale(1,1) translate(0,0)" writing-mode="lr" x="703.8" xml:space="preserve" y="118.96" zvalue="208">35kV德苴牛街线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.356,388.569) scale(1,1) translate(0,0)" writing-mode="lr" x="747.36" xml:space="preserve" y="391.57" zvalue="211">36110</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.171,280.225) scale(1,1) translate(0,0)" writing-mode="lr" x="733.17" xml:space="preserve" y="283.22" zvalue="213">3611</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.494,524.73) scale(1,1) translate(0,0)" writing-mode="lr" x="932.49" xml:space="preserve" y="527.73" zvalue="267">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.494,743.014) scale(1,1) translate(0,0)" writing-mode="lr" x="932.49" xml:space="preserve" y="746.01" zvalue="278">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,928.994,788.098) scale(1,1) translate(0,0)" writing-mode="lr" x="928.99" xml:space="preserve" y="791.1" zvalue="280">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,959.172,1087.89) scale(1,1) translate(0,0)" writing-mode="lr" x="959.17" xml:space="preserve" y="1090.89" zvalue="375">10kV树密线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.948,862.927) scale(1,1) translate(0,0)" writing-mode="lr" x="995.95" xml:space="preserve" y="865.9299999999999" zvalue="377">0631</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.994,911.801) scale(1,1) translate(0,0)" writing-mode="lr" x="997.99" xml:space="preserve" y="914.8" zvalue="380">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.564,960.889) scale(1,1) translate(0,0)" writing-mode="lr" x="998.5599999999999" xml:space="preserve" y="963.89" zvalue="381">0632</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1275.19,346.608) scale(1,1) translate(0,0)" writing-mode="lr" x="1275.19" xml:space="preserve" y="349.61" zvalue="439">362</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1249.51,115.96) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.51" xml:space="preserve" y="118.96" zvalue="441">35kV泰牛线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1275.17,290.08) scale(1,1) translate(0,0)" writing-mode="lr" x="1275.17" xml:space="preserve" y="293.08" zvalue="446">3626</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1275.17,398.403) scale(1,1) translate(0,0)" writing-mode="lr" x="1275.17" xml:space="preserve" y="401.4" zvalue="456">3621</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1288.59,239.16) scale(1,1) translate(0,0)" writing-mode="lr" x="1288.59" xml:space="preserve" y="242.16" zvalue="464">36267</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,758.818,1087.89) scale(1,1) translate(0,0)" writing-mode="lr" x="758.8200000000001" xml:space="preserve" y="1090.89" zvalue="501">10kV马鞍线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,799.594,862.927) scale(1,1) translate(0,0)" writing-mode="lr" x="799.59" xml:space="preserve" y="865.9299999999999" zvalue="503">0621</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.64,911.801) scale(1,1) translate(0,0)" writing-mode="lr" x="801.64" xml:space="preserve" y="914.8" zvalue="506">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.21,960.889) scale(1,1) translate(0,0)" writing-mode="lr" x="802.21" xml:space="preserve" y="963.89" zvalue="507">0622</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1163.47,1087.89) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.47" xml:space="preserve" y="1090.89" zvalue="667">10kV保邑线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.25,862.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1200.25" xml:space="preserve" y="865.9299999999999" zvalue="669">0641</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1202.29,911.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1202.29" xml:space="preserve" y="914.8" zvalue="672">064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1202.86,960.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1202.86" xml:space="preserve" y="963.89" zvalue="673">0642</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383.92,1087.89) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.92" xml:space="preserve" y="1090.89" zvalue="682">10kV木掌线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1412.44,862.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.44" xml:space="preserve" y="865.9299999999999" zvalue="684">0651</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1414.48,911.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1414.48" xml:space="preserve" y="914.8" zvalue="687">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415.05,960.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.05" xml:space="preserve" y="963.89" zvalue="688">0652</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,384.452,976.944) scale(1,1) translate(0,0)" writing-mode="lr" x="384.45" xml:space="preserve" y="979.9400000000001" zvalue="721">10kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.347,302.83) scale(1,1) translate(0,0)" writing-mode="lr" x="603.35" xml:space="preserve" y="305.83" zvalue="746">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,464.773,1089) scale(1,1) translate(0,0)" writing-mode="lr" x="464.77" xml:space="preserve" y="1092" zvalue="760">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" glyph-orientation-vertical="0" id="13" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1574.72,977.591) scale(1,1) translate(0,0)" writing-mode="tb" x="1574.72" xml:space="preserve" y="977.59" zvalue="783">预留间隔6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.7,646.612) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.7" xml:space="preserve" y="652.61" zvalue="785">4MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.7,615.023) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.7" xml:space="preserve" y="621.02" zvalue="839">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,928.994,472.062) scale(1,1) translate(0,0)" writing-mode="lr" x="928.99" xml:space="preserve" y="476.06" zvalue="851">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1575.7,900.362) scale(1,1) translate(0,0)" writing-mode="lr" x="1575.7" xml:space="preserve" y="906.36" zvalue="860">10kV</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.1488,253.429) scale(1,1) translate(0,0)" writing-mode="lr" x="86.15000000000001" xml:space="preserve" y="259.43" zvalue="873">事故</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,105.149,320.179) scale(1,1) translate(0,0)" writing-mode="lr" x="105.15" xml:space="preserve" y="326.18" zvalue="874">是否失压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186.649,320.179) scale(1,1) translate(0,0)" writing-mode="lr" x="186.65" xml:space="preserve" y="326.18" zvalue="876">失压排除</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,146.149,253.429) scale(1,1) translate(0,0)" writing-mode="lr" x="146.15" xml:space="preserve" y="259.43" zvalue="878">异常</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,206.149,253.429) scale(1,1) translate(0,0)" writing-mode="lr" x="206.15" xml:space="preserve" y="259.43" zvalue="879">告知</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.7321,379.179) scale(1,1) translate(0,0)" writing-mode="lr" x="86.73" xml:space="preserve" y="385.18" zvalue="880">负荷总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.9821,414.929) scale(1,1) translate(0,0)" writing-mode="lr" x="82.98" xml:space="preserve" y="420.93" zvalue="881">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.7321,449.929) scale(1,1) translate(0,0)" writing-mode="lr" x="86.73" xml:space="preserve" y="455.93" zvalue="882">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,213.935,415.929) scale(1,1) translate(0,0)" writing-mode="lr" x="213.93" xml:space="preserve" y="421.93" zvalue="883">弥牛变-02-2022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.935,452.25) scale(1,1) translate(0,0)" writing-mode="lr" x="200.93" xml:space="preserve" y="458.25" zvalue="884">2023-02-08</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="2">
   <path class="kv35" d="M 529.29 432.69 L 1486.91 432.69" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674278244356" ObjectName="35kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674278244356"/></metadata>
  <path d="M 529.29 432.69 L 1486.91 432.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 316.67 828.74 L 1770 828.74" stroke-width="6" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674278309892" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674278309892"/></metadata>
  <path d="M 316.67 828.74 L 1770 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="80">
   <use class="kv35" height="18" transform="rotate(0,1249.3,547.017) scale(2.35443,2.35443) translate(-708.526,-302.492)" width="15" x="1231.643911047112" xlink:href="#Accessory:PT8_0" y="525.8273956293925" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451318579205" ObjectName="35kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1249.3,547.017) scale(2.35443,2.35443) translate(-708.526,-302.492)" width="15" x="1231.643911047112" y="525.8273956293925"/></g>
  <g id="123">
   <use class="kv10" height="26" transform="rotate(0,597.764,1029.38) scale(-1.0017,1.0017) translate(-1194.5,-1.72702)" width="12" x="591.7534179664808" xlink:href="#Accessory:避雷器1_0" y="1016.353341483991" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451318841349" ObjectName="10kV大桥线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,597.764,1029.38) scale(-1.0017,1.0017) translate(-1194.5,-1.72702)" width="12" x="591.7534179664808" y="1016.353341483991"/></g>
  <g id="154">
   <use class="kv35" height="26" transform="rotate(270,746.198,191.763) scale(1.0017,1.0017) translate(-1.25775,-0.303722)" width="12" x="740.187473760667" xlink:href="#Accessory:避雷器1_0" y="178.7408697716232" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451319496709" ObjectName="35kV德苴牛街线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,746.198,191.763) scale(1.0017,1.0017) translate(-1.25775,-0.303722)" width="12" x="740.187473760667" y="178.7408697716232"/></g>
  <g id="220">
   <use class="kv10" height="26" transform="rotate(0,945.084,1029.86) scale(-1.0017,1.0017) translate(-1888.55,-1.72784)" width="12" x="939.074282251422" xlink:href="#Accessory:避雷器1_0" y="1016.840023755009" zvalue="382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320086533" ObjectName="10kV树密线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,945.084,1029.86) scale(-1.0017,1.0017) translate(-1888.55,-1.72784)" width="12" x="939.074282251422" y="1016.840023755009"/></g>
  <g id="112">
   <use class="kv35" height="26" transform="rotate(270,1289.94,192.494) scale(1.0017,1.0017) translate(-2.18168,-0.304965)" width="12" x="1283.924801278327" xlink:href="#Accessory:避雷器1_0" y="179.4722772599906" zvalue="442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320741894" ObjectName="35kV泰牛线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1289.94,192.494) scale(1.0017,1.0017) translate(-2.18168,-0.304965)" width="12" x="1283.924801278327" y="179.4722772599906"/></g>
  <g id="297">
   <use class="kv10" height="26" transform="rotate(0,749.732,1031.87) scale(-1.0017,1.0017) translate(-1498.18,-1.73125)" width="12" x="743.721943066622" xlink:href="#Accessory:避雷器1_0" y="1018.843427998341" zvalue="508"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320872965" ObjectName="10kV马鞍线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,749.732,1031.87) scale(-1.0017,1.0017) translate(-1498.18,-1.73125)" width="12" x="743.721943066622" y="1018.843427998341"/></g>
  <g id="199">
   <use class="kv10" height="26" transform="rotate(0,1149.38,1031.87) scale(-1.0017,1.0017) translate(-2296.8,-1.73125)" width="12" x="1143.373855664585" xlink:href="#Accessory:避雷器1_0" y="1018.843427998341" zvalue="674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321200646" ObjectName="10kV保邑线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1149.38,1031.87) scale(-1.0017,1.0017) translate(-2296.8,-1.73125)" width="12" x="1143.373855664585" y="1018.843427998341"/></g>
  <g id="258">
   <use class="kv10" height="26" transform="rotate(0,1361.57,1029.86) scale(-1.0017,1.0017) translate(-2720.83,-1.72784)" width="12" x="1355.564493291144" xlink:href="#Accessory:避雷器1_0" y="1016.840023755009" zvalue="689"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321462790" ObjectName="10kV木掌线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1361.57,1029.86) scale(-1.0017,1.0017) translate(-2720.83,-1.72784)" width="12" x="1355.564493291144" y="1016.840023755009"/></g>
  <g id="72">
   <use class="kv10" height="26" transform="rotate(0,1717.93,890.604) scale(-1.0017,1.0017) translate(-3432.94,-1.49121)" width="12" x="1711.92410600044" xlink:href="#Accessory:避雷器1_0" y="877.5813888085095" zvalue="705"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321790470" ObjectName="EFS-2WU型信号电源避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1717.93,890.604) scale(-1.0017,1.0017) translate(-3432.94,-1.49121)" width="12" x="1711.92410600044" y="877.5813888085095"/></g>
  <g id="16">
   <use class="kv10" height="25" transform="rotate(90,392.936,928.611) scale(1.66533,-1.66533) translate(-143.678,-1477.91)" width="40" x="359.6290309678935" xlink:href="#Accessory:35kVPT4561_0" y="907.7947684253647" zvalue="720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451319103493" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(90,392.936,928.611) scale(1.66533,-1.66533) translate(-143.678,-1477.91)" width="40" x="359.6290309678935" y="907.7947684253647"/></g>
  <g id="56">
   <use class="kv35" height="40" transform="rotate(0,1210.86,268.29) scale(1.61956,1.66873) translate(-457.016,-94.1402)" width="20" x="1194.665982855684" xlink:href="#Accessory:线路PT带避雷器0904_0" y="234.9152703670966" zvalue="752"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320610822" ObjectName="35kV泰牛线AB相"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1210.86,268.29) scale(1.61956,1.66873) translate(-457.016,-94.1402)" width="20" x="1194.665982855684" y="234.9152703670966"/></g>
  <g id="5">
   <use class="kv10" height="30" transform="rotate(0,1738.6,956.467) scale(4.25222,4.25222) translate(-1280.95,-682.75)" width="30" x="1674.821047795419" xlink:href="#Accessory:FS-2WU型信号电源_0" y="892.6835362829911" zvalue="774"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451827793923" ObjectName="FS-2WU型信号电源"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1738.6,956.467) scale(4.25222,4.25222) translate(-1280.95,-682.75)" width="30" x="1674.821047795419" y="892.6835362829911"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="79">
   <use class="kv35" height="30" transform="rotate(0,1247.18,468.388) scale(-1.11322,0.816053) translate(-2366.66,102.82)" width="15" x="1238.831588221439" xlink:href="#Disconnector:刀闸_0" y="456.1470408070064" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451318513669" ObjectName="35kVⅠ母电压互感器3901"/>
   <cge:TPSR_Ref TObjectID="6192451318513669"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1247.18,468.388) scale(-1.11322,0.816053) translate(-2366.66,102.82)" width="15" x="1238.831588221439" y="456.1470408070064"/></g>
  <g id="126">
   <use class="kv10" height="30" transform="rotate(0,575.788,861.174) scale(-1.11322,0.816053) translate(-1092.16,191.358)" width="15" x="567.4389556957937" xlink:href="#Disconnector:刀闸_0" y="848.9335592246488" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451318972421" ObjectName="10kV大桥线0611"/>
   <cge:TPSR_Ref TObjectID="6192451318972421"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,575.788,861.174) scale(-1.11322,0.816053) translate(-1092.16,191.358)" width="15" x="567.4389556957937" y="848.9335592246488"/></g>
  <g id="124">
   <use class="kv10" height="30" transform="rotate(0,575.761,959.888) scale(-1.11322,0.816053) translate(-1092.11,213.609)" width="15" x="567.4116897208576" xlink:href="#Disconnector:刀闸_0" y="947.6467515128647" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451318906885" ObjectName="10kV大桥线0612"/>
   <cge:TPSR_Ref TObjectID="6192451318906885"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,575.761,959.888) scale(-1.11322,0.816053) translate(-1092.11,213.609)" width="15" x="567.4116897208576" y="947.6467515128647"/></g>
  <g id="134">
   <use class="kv10" height="30" transform="rotate(0,393.954,861.174) scale(-1.11322,0.816053) translate(-746.99,191.358)" width="15" x="385.6048467511995" xlink:href="#Disconnector:刀闸_0" y="848.9335596009292" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451319169029" ObjectName="10kVⅠ母电压互感器4614"/>
   <cge:TPSR_Ref TObjectID="6192451319169029"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,393.954,861.174) scale(-1.11322,0.816053) translate(-746.99,191.358)" width="15" x="385.6048467511995" y="848.9335596009292"/></g>
  <g id="152">
   <use class="kv35" height="30" transform="rotate(0,705.222,280.779) scale(-1.11322,0.816053) translate(-1337.87,60.5313)" width="15" x="696.8731663266354" xlink:href="#Disconnector:刀闸_0" y="268.5386051339268" zvalue="212"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451319300101" ObjectName="35kV德苴牛街线3611"/>
   <cge:TPSR_Ref TObjectID="6192451319300101"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.222,280.779) scale(-1.11322,0.816053) translate(-1337.87,60.5313)" width="15" x="696.8731663266354" y="268.5386051339268"/></g>
  <g id="197">
   <use class="kv10" height="30" transform="rotate(0,966.341,788.43) scale(-1.11322,0.816053) translate(-1833.55,174.961)" width="15" x="957.9914554651648" xlink:href="#Disconnector:刀闸_0" y="776.1887411871793" zvalue="279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451319693317" ObjectName="#1主变10kV侧0011"/>
   <cge:TPSR_Ref TObjectID="6192451319693317"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,966.341,788.43) scale(-1.11322,0.816053) translate(-1833.55,174.961)" width="15" x="957.9914554651648" y="776.1887411871793"/></g>
  <g id="223">
   <use class="kv10" height="30" transform="rotate(0,966.37,861.174) scale(-1.11322,0.816053) translate(-1833.6,191.358)" width="15" x="958.0210813054631" xlink:href="#Disconnector:刀闸_0" y="848.9335592246488" zvalue="376"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320217605" ObjectName="10kV树密线0631"/>
   <cge:TPSR_Ref TObjectID="6192451320217605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,966.37,861.174) scale(-1.11322,0.816053) translate(-1833.6,191.358)" width="15" x="958.0210813054631" y="848.9335592246488"/></g>
  <g id="221">
   <use class="kv10" height="30" transform="rotate(0,966.343,959.888) scale(-1.11322,0.816053) translate(-1833.55,213.609)" width="15" x="957.9938153305272" xlink:href="#Disconnector:刀闸_0" y="947.6467524350301" zvalue="379"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320152069" ObjectName="10kV树密线0632"/>
   <cge:TPSR_Ref TObjectID="6192451320152069"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,966.343,959.888) scale(-1.11322,0.816053) translate(-1833.55,213.609)" width="15" x="957.9938153305272" y="947.6467524350301"/></g>
  <g id="110">
   <use class="kv35" height="30" transform="rotate(0,1247.14,292.779) scale(-1.11322,0.816053) translate(-2366.59,63.2362)" width="15" x="1238.792312026113" xlink:href="#Disconnector:刀闸_0" y="280.5386044166279" zvalue="445"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320676358" ObjectName="35kV泰牛线3626"/>
   <cge:TPSR_Ref TObjectID="6192451320676358"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1247.14,292.779) scale(-1.11322,0.816053) translate(-2366.59,63.2362)" width="15" x="1238.792312026113" y="280.5386044166279"/></g>
  <g id="97">
   <use class="kv35" height="30" transform="rotate(0,1247.14,394.953) scale(-1.11322,0.816053) translate(-2366.59,86.2672)" width="15" x="1238.792312026113" xlink:href="#Disconnector:刀闸_0" y="382.7122349179942" zvalue="455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320479750" ObjectName="35kV泰牛线3621"/>
   <cge:TPSR_Ref TObjectID="6192451320479750"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1247.14,394.953) scale(-1.11322,0.816053) translate(-2366.59,86.2672)" width="15" x="1238.792312026113" y="382.7122349179942"/></g>
  <g id="300">
   <use class="kv10" height="30" transform="rotate(0,770.016,861.174) scale(-1.11322,0.816053) translate(-1460.87,191.358)" width="15" x="761.6670399989976" xlink:href="#Disconnector:刀闸_0" y="848.9335592246488" zvalue="502"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321004038" ObjectName="10kV马鞍线0621"/>
   <cge:TPSR_Ref TObjectID="6192451321004038"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,770.016,861.174) scale(-1.11322,0.816053) translate(-1460.87,191.358)" width="15" x="761.6670399989976" y="848.9335592246488"/></g>
  <g id="298">
   <use class="kv10" height="30" transform="rotate(0,769.989,959.888) scale(-1.11322,0.816053) translate(-1460.81,213.609)" width="15" x="761.639774024062" xlink:href="#Disconnector:刀闸_0" y="947.6467524350301" zvalue="505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320938501" ObjectName="10kV马鞍线0622"/>
   <cge:TPSR_Ref TObjectID="6192451320938501"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,769.989,959.888) scale(-1.11322,0.816053) translate(-1460.81,213.609)" width="15" x="761.639774024062" y="947.6467524350301"/></g>
  <g id="208">
   <use class="kv10" height="30" transform="rotate(0,1170.67,861.174) scale(-1.11322,0.816053) translate(-2221.42,191.358)" width="15" x="1162.320654718626" xlink:href="#Disconnector:刀闸_0" y="848.9335592246488" zvalue="668"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321331718" ObjectName="10kV保邑线0641"/>
   <cge:TPSR_Ref TObjectID="6192451321331718"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1170.67,861.174) scale(-1.11322,0.816053) translate(-2221.42,191.358)" width="15" x="1162.320654718626" y="848.9335592246488"/></g>
  <g id="204">
   <use class="kv10" height="30" transform="rotate(0,1170.64,959.888) scale(-1.11322,0.816053) translate(-2221.37,213.609)" width="15" x="1162.293388743691" xlink:href="#Disconnector:刀闸_0" y="947.6467524350301" zvalue="671"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321266182" ObjectName="10kV保邑线0642"/>
   <cge:TPSR_Ref TObjectID="6192451321266182"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1170.64,959.888) scale(-1.11322,0.816053) translate(-2221.37,213.609)" width="15" x="1162.293388743691" y="947.6467524350301"/></g>
  <g id="263">
   <use class="kv10" height="30" transform="rotate(0,1382.86,861.174) scale(-1.11322,0.816053) translate(-2624.22,191.358)" width="15" x="1374.511292345185" xlink:href="#Disconnector:刀闸_0" y="848.9335592246488" zvalue="683"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321593862" ObjectName="10kV木掌线0651"/>
   <cge:TPSR_Ref TObjectID="6192451321593862"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1382.86,861.174) scale(-1.11322,0.816053) translate(-2624.22,191.358)" width="15" x="1374.511292345185" y="848.9335592246488"/></g>
  <g id="260">
   <use class="kv10" height="30" transform="rotate(0,1382.83,959.888) scale(-1.11322,0.816053) translate(-2624.17,213.609)" width="15" x="1374.484026370249" xlink:href="#Disconnector:刀闸_0" y="947.6467524350301" zvalue="686"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321528326" ObjectName="10kV木掌线0652"/>
   <cge:TPSR_Ref TObjectID="6192451321528326"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1382.83,959.888) scale(-1.11322,0.816053) translate(-2624.17,213.609)" width="15" x="1374.484026370249" y="947.6467524350301"/></g>
  <g id="17">
   <use class="kv35" height="30" transform="rotate(0,966.343,473.062) scale(-1.11322,0.816053) translate(-1833.55,103.874)" width="15" x="957.9938148554236" xlink:href="#Disconnector:刀闸_0" y="460.821650708113" zvalue="850"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451319758853" ObjectName="#1主变35kV侧3011"/>
   <cge:TPSR_Ref TObjectID="6192451319758853"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,966.343,473.062) scale(-1.11322,0.816053) translate(-1833.55,103.874)" width="15" x="957.9938148554236" y="460.821650708113"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="82">
   <use class="kv35" height="30" transform="rotate(90,1289.34,502.752) scale(1.01422,-0.867474) translate(-17.9962,-1084.3)" width="12" x="1283.25069441964" xlink:href="#GroundDisconnector:地刀12_0" y="489.7394406221074" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451318710277" ObjectName="35kVⅠ母电压互感器39010"/>
   <cge:TPSR_Ref TObjectID="6192451318710277"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1289.34,502.752) scale(1.01422,-0.867474) translate(-17.9962,-1084.3)" width="12" x="1283.25069441964" y="489.7394406221074"/></g>
  <g id="153">
   <use class="kv35" height="30" transform="rotate(270,748.006,373.635) scale(-1.01422,0.867474) translate(-1485.44,55.0931)" width="12" x="741.9205104395367" xlink:href="#GroundDisconnector:地刀12_0" y="360.6226956458701" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451319431173" ObjectName="35kV德苴牛街线36110"/>
   <cge:TPSR_Ref TObjectID="6192451319431173"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,748.006,373.635) scale(-1.01422,0.867474) translate(-1485.44,55.0931)" width="12" x="741.9205104395367" y="360.6226956458701"/></g>
  <g id="89">
   <use class="kv35" height="30" transform="rotate(270,1289.92,222.491) scale(-1.01422,0.867474) translate(-2561.67,32.0025)" width="12" x="1283.839656597986" xlink:href="#GroundDisconnector:地刀12_0" y="209.4786850117289" zvalue="463"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320414213" ObjectName="35kV泰牛线36267"/>
   <cge:TPSR_Ref TObjectID="6192451320414213"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1289.92,222.491) scale(-1.01422,0.867474) translate(-2561.67,32.0025)" width="12" x="1283.839656597986" y="209.4786850117289"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="83">
   <path class="kv35" d="M 1276.91 502.74 L 1247.11 502.74" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 1276.91 502.74 L 1247.11 502.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv10" d="M 575.69 849.34 L 575.69 828.74" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="25@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.69 849.34 L 575.69 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 575.72 873.21 L 575.7 891.75" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@1" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.72 873.21 L 575.7 891.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 575.88 927.82 L 575.88 948.05" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="124@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.88 927.82 L 575.88 948.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv10" d="M 575.69 971.92 L 575.69 1037.52" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@1" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.69 971.92 L 575.69 1037.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv35" d="M 705.12 174.22 L 705.12 268.94" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.12 174.22 L 705.12 268.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv35" d="M 733.81 191.73 L 705.12 191.73" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.81 191.73 L 705.12 191.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 966.32 722.96 L 966.34 672.62" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="930@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.32 722.96 L 966.34 672.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 966.51 759.03 L 966.51 776.59" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="197@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.51 759.03 L 966.51 776.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv10" d="M 966.3 873.21 L 966.28 891.75" stroke-width="1" zvalue="384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@1" LinkObjectIDznd="222@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.3 873.21 L 966.28 891.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 966.47 927.82 L 966.47 948.05" stroke-width="1" zvalue="385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@1" LinkObjectIDznd="221@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.47 927.82 L 966.47 948.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 966.27 971.92 L 966.27 1037.52" stroke-width="1" zvalue="386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="224@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.27 971.92 L 966.27 1037.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1247.04 174.45 L 1247.04 280.94" stroke-width="1" zvalue="451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="110@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.04 174.45 L 1247.04 280.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 1277.55 192.46 L 1247.04 192.46" stroke-width="1" zvalue="452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.55 192.46 L 1247.04 192.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 1247.04 383.12 L 1247.04 364.13" stroke-width="1" zvalue="458"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="143@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.04 383.12 L 1247.04 364.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv35" d="M 1247.23 328.06 L 1247.23 304.81" stroke-width="1" zvalue="459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.23 328.06 L 1247.23 304.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 1277.5 222.48 L 1247.04 222.48" stroke-width="1" zvalue="465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.5 222.48 L 1247.04 222.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv10" d="M 769.92 849.34 L 769.92 828.74" stroke-width="1" zvalue="509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@0" LinkObjectIDznd="25@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.92 849.34 L 769.92 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv10" d="M 769.95 873.21 L 769.93 891.75" stroke-width="1" zvalue="510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@1" LinkObjectIDznd="299@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.95 873.21 L 769.93 891.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv10" d="M 770.11 927.82 L 770.11 948.05" stroke-width="1" zvalue="511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="299@1" LinkObjectIDznd="298@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 770.11 927.82 L 770.11 948.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv10" d="M 769.92 971.92 L 769.92 1037.52" stroke-width="1" zvalue="512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@1" LinkObjectIDznd="301@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.92 971.92 L 769.92 1037.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv35" d="M 735.58 373.62 L 705.15 373.61" stroke-width="1" zvalue="661"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="107" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.58 373.62 L 705.15 373.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1170.57 849.34 L 1170.57 828.74" stroke-width="1" zvalue="675"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="25@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.57 849.34 L 1170.57 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1170.6 873.21 L 1170.58 891.75" stroke-width="1" zvalue="676"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@1" LinkObjectIDznd="207@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.6 873.21 L 1170.58 891.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 1170.76 927.82 L 1170.76 948.05" stroke-width="1" zvalue="677"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@1" LinkObjectIDznd="204@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.76 927.82 L 1170.76 948.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1170.57 971.92 L 1170.57 1037.52" stroke-width="1" zvalue="678"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@1" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.57 971.92 L 1170.57 1037.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv10" d="M 1382.76 849.34 L 1382.76 828.74" stroke-width="1" zvalue="690"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="25@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.76 849.34 L 1382.76 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv10" d="M 1382.79 873.21 L 1382.77 891.75" stroke-width="1" zvalue="691"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@1" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.79 873.21 L 1382.77 891.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv10" d="M 1382.96 927.82 L 1382.96 948.05" stroke-width="1" zvalue="692"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@1" LinkObjectIDznd="260@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.96 927.82 L 1382.96 948.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv10" d="M 1382.77 971.92 L 1382.77 1037.52" stroke-width="1" zvalue="693"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@1" LinkObjectIDznd="268@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.77 971.92 L 1382.77 1037.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 393.77 899.05 L 393.77 873.21" stroke-width="1" zvalue="722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="134@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 393.77 899.05 L 393.77 873.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 393.86 849.34 L 393.86 828.74" stroke-width="1" zvalue="723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="25@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 393.86 849.34 L 393.86 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 1247.11 480.42 L 1247.11 527.33" stroke-width="1" zvalue="724"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@1" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.11 480.42 L 1247.11 527.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv35" d="M 1210.86 236.44 L 1210.86 222.48 L 1247.04 222.48" stroke-width="1" zvalue="753"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1210.86 236.44 L 1210.86 222.48 L 1247.04 222.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv35" d="M 705.12 192.36 L 619.66 192.36 L 619.66 216.23" stroke-width="1" zvalue="754"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.12 192.36 L 619.66 192.36 L 619.66 216.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 575.69 982.75 L 597.73 982.75 L 597.73 1016.99" stroke-width="1" zvalue="760"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.69 982.75 L 597.73 982.75 L 597.73 1016.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 769.92 986.05 L 749.7 986.05 L 749.7 1019.48" stroke-width="1" zvalue="761"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293" LinkObjectIDznd="297@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.92 986.05 L 749.7 986.05 L 749.7 1019.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 966.27 986.02 L 945.05 986.02 L 945.05 1017.47" stroke-width="1" zvalue="763"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216" LinkObjectIDznd="220@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.27 986.02 L 945.05 986.02 L 945.05 1017.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1170.57 986.54 L 1149.35 986.54 L 1149.35 1019.48" stroke-width="1" zvalue="764"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178" LinkObjectIDznd="199@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.57 986.54 L 1149.35 986.54 L 1149.35 1019.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 1382.77 986.75 L 1361.54 986.75 L 1361.54 1017.47" stroke-width="1" zvalue="765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226" LinkObjectIDznd="258@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.77 986.75 L 1361.54 986.75 L 1361.54 1017.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 575.69 981.28 L 500.62 981.28 L 500.62 1000.47" stroke-width="1" zvalue="766"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119" LinkObjectIDznd="66@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.69 981.28 L 500.62 981.28 L 500.62 1000.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv35" d="M 705.15 292.81 L 705.15 432.69" stroke-width="1" zvalue="769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@1" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.15 292.81 L 705.15 432.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv10" d="M 1738.88 897.65 L 1738.88 828.74" stroke-width="1" zvalue="775"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="25@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1738.88 897.65 L 1738.88 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv10" d="M 966.27 849.34 L 966.27 828.74" stroke-width="1" zvalue="778"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.27 849.34 L 966.27 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 966.27 800.46 L 966.27 828.74" stroke-width="1" zvalue="779"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@1" LinkObjectIDznd="25@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.27 800.46 L 966.27 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv10" d="M 1738.88 869 L 1717.9 869 L 1717.9 878.22" stroke-width="1" zvalue="781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1738.88 869 L 1717.9 869 L 1717.9 878.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv35" d="M 966.47 543.25 L 966.38 589.75" stroke-width="1" zvalue="856"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="930@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.47 543.25 L 966.38 589.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 966.28 507.18 L 966.27 485.09" stroke-width="1" zvalue="857"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="17@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.28 507.18 L 966.27 485.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv35" d="M 966.25 461.23 L 966.25 432.69" stroke-width="1" zvalue="858"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.25 461.23 L 966.25 432.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv35" d="M 1247.08 456.55 L 1247.08 432.69" stroke-width="1" zvalue="953"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="2@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.08 456.55 L 1247.08 432.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 1247.33 432.69 L 1247.33 406.98" stroke-width="1" zvalue="954"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@3" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.33 432.69 L 1247.33 406.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="127">
   <use class="kv10" height="30" transform="rotate(180,575.693,1056.05) scale(1.39125,1.3727) translate(-159.551,-281.139)" width="12" x="567.3452830761748" xlink:href="#EnergyConsumer:负荷_0" y="1035.463605547112" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451319037957" ObjectName="10kV大桥线"/>
   <cge:TPSR_Ref TObjectID="6192451319037957"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,575.693,1056.05) scale(1.39125,1.3727) translate(-159.551,-281.139)" width="12" x="567.3452830761748" y="1035.463605547112"/></g>
  <g id="224">
   <use class="kv10" height="30" transform="rotate(180,966.275,1056.05) scale(1.39125,1.3727) translate(-269.392,-281.139)" width="12" x="957.9274086858443" xlink:href="#EnergyConsumer:负荷_0" y="1035.463605573648" zvalue="374"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451320283141" ObjectName="10kV树密线"/>
   <cge:TPSR_Ref TObjectID="6192451320283141"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,966.275,1056.05) scale(1.39125,1.3727) translate(-269.392,-281.139)" width="12" x="957.9274086858443" y="1035.463605573648"/></g>
  <g id="301">
   <use class="kv10" height="30" transform="rotate(180,769.921,1056.05) scale(1.39125,1.3727) translate(-214.172,-281.139)" width="12" x="761.5733673793786" xlink:href="#EnergyConsumer:负荷_0" y="1035.463605573648" zvalue="500"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321069574" ObjectName="10kV马鞍线"/>
   <cge:TPSR_Ref TObjectID="6192451321069574"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,769.921,1056.05) scale(1.39125,1.3727) translate(-214.172,-281.139)" width="12" x="761.5733673793786" y="1035.463605573648"/></g>
  <g id="209">
   <use class="kv10" height="30" transform="rotate(180,1170.57,1056.05) scale(1.39125,1.3727) translate(-326.845,-281.139)" width="12" x="1162.226982099008" xlink:href="#EnergyConsumer:负荷_0" y="1035.463605573648" zvalue="666"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321397254" ObjectName="10kV保邑线"/>
   <cge:TPSR_Ref TObjectID="6192451321397254"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1170.57,1056.05) scale(1.39125,1.3727) translate(-326.845,-281.139)" width="12" x="1162.226982099008" y="1035.463605573648"/></g>
  <g id="268">
   <use class="kv10" height="30" transform="rotate(180,1382.77,1056.05) scale(1.39125,1.3727) translate(-386.518,-281.139)" width="12" x="1374.417619725566" xlink:href="#EnergyConsumer:负荷_0" y="1035.463605573648" zvalue="681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451321659398" ObjectName="10kV木掌线"/>
   <cge:TPSR_Ref TObjectID="6192451321659398"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1382.77,1056.05) scale(1.39125,1.3727) translate(-386.518,-281.139)" width="12" x="1374.417619725566" y="1035.463605573648"/></g>
  <g id="49">
   <use class="kv35" height="30" transform="rotate(0,619.661,250.884) scale(-2.50426,2.44927) translate(-852.061,-126.713)" width="20" x="594.6179652859162" xlink:href="#EnergyConsumer:站用变带熔断器_0" y="214.1452863907149" zvalue="745"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451318448133" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,619.661,250.884) scale(-2.50426,2.44927) translate(-852.061,-126.713)" width="20" x="594.6179652859162" y="214.1452863907149"/></g>
  <g id="66">
   <use class="kv10" height="30" transform="rotate(0,500.621,1035.13) scale(2.50426,2.44927) translate(-285.67,-590.762)" width="20" x="475.5787053605603" xlink:href="#EnergyConsumer:站用变带熔断器_0" y="998.3887469178301" zvalue="759"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451827728387" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,500.621,1035.13) scale(2.50426,2.44927) translate(-285.67,-590.762)" width="20" x="475.5787053605603" y="998.3887469178301"/></g>
 </g>
 <g id="BreakerClass">
  <g id="125">
   <use class="kv10" height="20" transform="rotate(0,575.761,909.797) scale(1.83111,1.88671) translate(-257.173,-418.715)" width="10" x="566.6053194260713" xlink:href="#Breaker:开关_0" y="890.9300765800493" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924681334789" ObjectName="10kV大桥线061"/>
   <cge:TPSR_Ref TObjectID="6473924681334789"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,575.761,909.797) scale(1.83111,1.88671) translate(-257.173,-418.715)" width="10" x="566.6053194260713" y="890.9300765800493"/></g>
  <g id="206">
   <use class="kv35" height="20" transform="rotate(0,966.343,525.231) scale(1.83111,1.88671) translate(-434.452,-237.979)" width="10" x="957.1874449273114" xlink:href="#Breaker:开关_0" y="506.363453134352" zvalue="266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924681465861" ObjectName="#1主变35kV侧301"/>
   <cge:TPSR_Ref TObjectID="6473924681465861"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,966.343,525.231) scale(1.83111,1.88671) translate(-434.452,-237.979)" width="10" x="957.1874449273114" y="506.363453134352"/></g>
  <g id="198">
   <use class="kv10" height="20" transform="rotate(0,966.384,741.01) scale(1.83111,1.88671) translate(-434.47,-339.39)" width="10" x="957.2282553486205" xlink:href="#Breaker:开关_0" y="722.1432696638501" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924681400325" ObjectName="#1主变10kV侧001"/>
   <cge:TPSR_Ref TObjectID="6473924681400325"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,966.384,741.01) scale(1.83111,1.88671) translate(-434.47,-339.39)" width="10" x="957.2282553486205" y="722.1432696638501"/></g>
  <g id="222">
   <use class="kv10" height="20" transform="rotate(0,966.343,909.797) scale(1.83111,1.88671) translate(-434.452,-418.715)" width="10" x="957.1874450357412" xlink:href="#Breaker:开关_0" y="890.9300765674591" zvalue="378"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924681531397" ObjectName="10kV树密线063"/>
   <cge:TPSR_Ref TObjectID="6473924681531397"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,966.343,909.797) scale(1.83111,1.88671) translate(-434.452,-418.715)" width="10" x="957.1874450357412" y="890.9300765674591"/></g>
  <g id="143">
   <use class="kv35" height="20" transform="rotate(0,1247.29,346.107) scale(1.83111,1.88671) translate(-561.971,-153.795)" width="10" x="1238.138671271972" xlink:href="#Breaker:开关_0" y="327.2400967365352" zvalue="438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924681596933" ObjectName="35kV泰牛线362"/>
   <cge:TPSR_Ref TObjectID="6473924681596933"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1247.29,346.107) scale(1.83111,1.88671) translate(-561.971,-153.795)" width="10" x="1238.138671271972" y="327.2400967365352"/></g>
  <g id="299">
   <use class="kv10" height="20" transform="rotate(0,769.989,909.797) scale(1.83111,1.88671) translate(-345.33,-418.715)" width="10" x="760.8334037292755" xlink:href="#Breaker:开关_0" y="890.9300765674591" zvalue="504"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924681662469" ObjectName="10kV马鞍线062"/>
   <cge:TPSR_Ref TObjectID="6473924681662469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,769.989,909.797) scale(1.83111,1.88671) translate(-345.33,-418.715)" width="10" x="760.8334037292755" y="890.9300765674591"/></g>
  <g id="207">
   <use class="kv10" height="20" transform="rotate(0,1170.64,909.797) scale(1.83111,1.88671) translate(-527.18,-418.715)" width="10" x="1161.487018448905" xlink:href="#Breaker:开关_0" y="890.9300765674591" zvalue="670"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924681728005" ObjectName="10kV保邑线064"/>
   <cge:TPSR_Ref TObjectID="6473924681728005"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1170.64,909.797) scale(1.83111,1.88671) translate(-527.18,-418.715)" width="10" x="1161.487018448905" y="890.9300765674591"/></g>
  <g id="261">
   <use class="kv10" height="20" transform="rotate(0,1382.83,909.797) scale(1.83111,1.88671) translate(-623.49,-418.715)" width="10" x="1373.677656075463" xlink:href="#Breaker:开关_0" y="890.9300766868714" zvalue="685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924681793541" ObjectName="10kV木掌线065"/>
   <cge:TPSR_Ref TObjectID="6473924681793541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1382.83,909.797) scale(1.83111,1.88671) translate(-623.49,-418.715)" width="10" x="1373.677656075463" y="890.9300766868714"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="155">
   <use class="kv35" height="30" transform="rotate(0,705.125,151.439) scale(2.00315,1.53404) translate(-349.606,-44.7094)" width="7" x="698.1136224835946" xlink:href="#ACLineSegment:线路_0" y="128.4285583496093" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249318817798" ObjectName="35kV德苴牛街线"/>
   <cge:TPSR_Ref TObjectID="8444249318817798_5066549593047042"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.125,151.439) scale(2.00315,1.53404) translate(-349.606,-44.7094)" width="7" x="698.1136224835946" y="128.4285583496093"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="930">
   <g id="9300">
    <use class="kv35" height="30" transform="rotate(0,966.343,631) scale(3.09833,2.96282) translate(-629.272,-388.585)" width="24" x="929.16" xlink:href="#PowerTransformer2:可调不带中性点_0" y="586.5599999999999" zvalue="838"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467966979" ObjectName="35"/>
    </metadata>
   </g>
   <g id="9301">
    <use class="kv10" height="30" transform="rotate(0,966.343,631) scale(3.09833,2.96282) translate(-629.272,-388.585)" width="24" x="929.16" xlink:href="#PowerTransformer2:可调不带中性点_1" y="586.5599999999999" zvalue="838"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874468032515" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399462617091" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399462617091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,966.343,631) scale(3.09833,2.96282) translate(-629.272,-388.585)" width="24" x="929.16" y="586.5599999999999"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="109" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,710.136,29.4286) scale(1,1) translate(-2.9649e-13,0)" writing-mode="lr" x="710.14" xml:space="preserve" y="35.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129523675140" ObjectName="P"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="118" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1248.98,28.9286) scale(1,1) translate(0,0)" writing-mode="lr" x="1248.98" xml:space="preserve" y="35.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129529835525" ObjectName="P"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="128" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,710.136,49.4286) scale(1,1) translate(-2.9649e-13,0)" writing-mode="lr" x="710.14" xml:space="preserve" y="55.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129523740676" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="129" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1248.98,48.9286) scale(1,1) translate(0,0)" writing-mode="lr" x="1248.98" xml:space="preserve" y="55.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129529901061" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="131" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,710.136,69.4286) scale(1,1) translate(-2.9649e-13,0)" writing-mode="lr" x="710.14" xml:space="preserve" y="75.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129523806212" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="132" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1248.98,68.9286) scale(1,1) translate(0,0)" writing-mode="lr" x="1248.98" xml:space="preserve" y="75.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129529966597" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.136,89.6892) scale(1,1) translate(0,-2.31472e-13)" writing-mode="lr" x="710.14" xml:space="preserve" y="96.17" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129524068356" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1248.98,88.7963) scale(1,1) translate(0,2.37699e-13)" writing-mode="lr" x="1248.98" xml:space="preserve" y="95.28" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129530228740" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="172" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1450.23,332.296) scale(1,1) translate(9.37738e-13,0)" writing-mode="lr" x="1450.23" xml:space="preserve" y="338.78" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520005124" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="173" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,364.278,725.462) scale(1,1) translate(0,-4.76926e-13)" writing-mode="lr" x="364.28" xml:space="preserve" y="731.9400000000001" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520529412" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="174">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="174" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1450.23,352.296) scale(1,1) translate(9.37738e-13,4.94755e-13)" writing-mode="lr" x="1450.23" xml:space="preserve" y="358.78" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520070660" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="175">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="175" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,364.278,745.462) scale(1,1) translate(0,0)" writing-mode="lr" x="364.28" xml:space="preserve" y="751.9400000000001" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520594948" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="177">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="177" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1450.23,372.296) scale(1,1) translate(9.37738e-13,0)" writing-mode="lr" x="1450.23" xml:space="preserve" y="378.78" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520136196" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="179">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="179" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,364.278,765.462) scale(1,1) translate(0,0)" writing-mode="lr" x="364.28" xml:space="preserve" y="771.9400000000001" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520660484" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="180">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="180" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1450.23,313.408) scale(1,1) translate(-9.37738e-13,0)" writing-mode="lr" x="1450.23" xml:space="preserve" y="319.89" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520267268" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,364.278,705.462) scale(1,1) translate(0,0)" writing-mode="lr" x="364.28" xml:space="preserve" y="711.9400000000001" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520791556" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="182" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1450.23,389.963) scale(1,1) translate(9.37738e-13,0)" writing-mode="lr" x="1450.23" xml:space="preserve" y="396.44" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520463876" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="184">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="184" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,364.278,783.128) scale(1,1) translate(0,0)" writing-mode="lr" x="364.28" xml:space="preserve" y="789.61" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129520988164" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="185" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,850.663,507.058) scale(1,1) translate(-1.7967e-13,0)" writing-mode="lr" x="850.66" xml:space="preserve" y="513.54" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129525772293" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="187">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="187" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,850.663,527.058) scale(1,1) translate(-1.7967e-13,0)" writing-mode="lr" x="850.66" xml:space="preserve" y="533.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129525837829" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="192" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,850.663,729.942) scale(1,1) translate(1.7967e-13,0)" writing-mode="lr" x="850.66" xml:space="preserve" y="736.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129525903364" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="200" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,850.663,749.942) scale(1,1) translate(1.7967e-13,0)" writing-mode="lr" x="850.66" xml:space="preserve" y="756.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129525968900" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="201" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,850.663,547.058) scale(1,1) translate(-1.7967e-13,0)" writing-mode="lr" x="850.66" xml:space="preserve" y="553.54" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129526034436" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="202" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.663,617.045) scale(1,1) translate(0,0)" writing-mode="lr" x="866.66" xml:space="preserve" y="622.02" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129526231044" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="205" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.663,646.612) scale(1,1) translate(0,0)" writing-mode="lr" x="866.66" xml:space="preserve" y="651.59" zvalue="1">档位:ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129526296580" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="215">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="215" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,850.663,769.942) scale(1,1) translate(1.7967e-13,0)" writing-mode="lr" x="850.66" xml:space="preserve" y="776.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129526362117" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="219" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,576.193,1111.96) scale(1,1) translate(0,0)" writing-mode="lr" x="576.1900000000001" xml:space="preserve" y="1118.44" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129522757636" ObjectName="P"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="225" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,962.775,1111.96) scale(1,1) translate(0,3.30475e-12)" writing-mode="lr" x="962.77" xml:space="preserve" y="1118.44" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129528328197" ObjectName="P"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="228" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,763.073,1111.96) scale(1,1) translate(0,3.30475e-12)" writing-mode="lr" x="763.0700000000001" xml:space="preserve" y="1118.44" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129531932676" ObjectName="P"/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="229" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1167.73,1111.96) scale(1,1) translate(0,3.30475e-12)" writing-mode="lr" x="1167.73" xml:space="preserve" y="1118.44" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129533964293" ObjectName="P"/>
   </metadata>
  </g>
  <g id="230">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="230" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1383.92,1111.96) scale(1,1) translate(0,3.30475e-12)" writing-mode="lr" x="1383.92" xml:space="preserve" y="1118.44" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129535406085" ObjectName="P"/>
   </metadata>
  </g>
  <g id="231">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="231" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,576.193,1131.96) scale(1,1) translate(0,0)" writing-mode="lr" x="576.1900000000001" xml:space="preserve" y="1138.44" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129522823172" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="232" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,962.775,1131.96) scale(1,1) translate(0,0)" writing-mode="lr" x="962.77" xml:space="preserve" y="1138.44" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129528393733" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="233" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,763.073,1131.96) scale(1,1) translate(0,0)" writing-mode="lr" x="763.0700000000001" xml:space="preserve" y="1138.44" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129531998212" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="234" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1167.73,1131.96) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.73" xml:space="preserve" y="1138.44" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129534029828" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="235" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1383.92,1131.96) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.92" xml:space="preserve" y="1138.44" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129535471621" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="236" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,576.193,1151.96) scale(1,1) translate(0,0)" writing-mode="lr" x="576.1900000000001" xml:space="preserve" y="1158.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129522888708" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="237" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,962.775,1151.96) scale(1,1) translate(0,0)" writing-mode="lr" x="962.77" xml:space="preserve" y="1158.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129528459269" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="238" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,763.073,1151.96) scale(1,1) translate(0,0)" writing-mode="lr" x="763.0700000000001" xml:space="preserve" y="1158.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129532063748" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="239" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1167.73,1151.96) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.73" xml:space="preserve" y="1158.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129534095364" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1383.92,1151.96) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.92" xml:space="preserve" y="1158.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129535537157" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,577.845,1170.96) scale(1,1) translate(0,0)" writing-mode="lr" x="577.85" xml:space="preserve" y="1177.44" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129523281925" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="242" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.775,1170.96) scale(1,1) translate(0,0)" writing-mode="lr" x="962.77" xml:space="preserve" y="1177.44" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129528852485" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,763.073,1170.96) scale(1,1) translate(0,0)" writing-mode="lr" x="763.0700000000001" xml:space="preserve" y="1177.44" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129532456965" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="245" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.73,1170.96) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.73" xml:space="preserve" y="1177.44" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129534488580" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="246" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383.92,1170.96) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.92" xml:space="preserve" y="1177.44" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129535930373" ObjectName="Cos"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="467">
   <use height="30" stroke="rgb(255,255,255)" transform="rotate(0,90.9702,219.429) scale(0.958333,0.916667) translate(3.33023,18.6981)" width="30" x="76.59999999999999" xlink:href="#State:红绿圆_0" y="205.68" zvalue="870"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549593047042" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,90.9702,219.429) scale(0.958333,0.916667) translate(3.33023,18.6981)" width="30" x="76.59999999999999" y="205.68"/></g>
  <g id="462">
   <use height="30" transform="rotate(0,148.399,219.429) scale(0.958333,0.916667) translate(5.82712,18.6981)" width="30" x="134.02" xlink:href="#State:红绿圆_0" y="205.68" zvalue="871"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549593047042" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,148.399,219.429) scale(0.958333,0.916667) translate(5.82712,18.6981)" width="30" x="134.02" y="205.68"/></g>
  <g id="449">
   <use height="30" transform="rotate(0,208.399,219.429) scale(0.958333,0.916667) translate(8.43582,18.6981)" width="30" x="194.02" xlink:href="#State:红绿圆_0" y="205.68" zvalue="872"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549593047042" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,208.399,219.429) scale(0.958333,0.916667) translate(8.43582,18.6981)" width="30" x="194.02" y="205.68"/></g>
  <g id="441">
   <use height="30" transform="rotate(0,101.899,288.179) scale(0.958333,0.916667) translate(3.80538,24.9481)" width="30" x="87.52" xlink:href="#State:红绿圆_0" y="274.43" zvalue="875"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549593047042" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,101.899,288.179) scale(0.958333,0.916667) translate(3.80538,24.9481)" width="30" x="87.52" y="274.43"/></g>
  <g id="440">
   <use height="30" transform="rotate(0,189.399,288.179) scale(0.958333,0.916667) translate(7.60973,24.9481)" width="30" x="175.02" xlink:href="#State:红绿圆_0" y="274.43" zvalue="877"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549593047042" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,189.399,288.179) scale(0.958333,0.916667) translate(7.60973,24.9481)" width="30" x="175.02" y="274.43"/></g>
 </g>
</svg>