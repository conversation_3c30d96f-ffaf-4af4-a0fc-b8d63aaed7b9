<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549601697794" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Accessory:中间电缆_0" viewBox="0,0,8,21">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="10.5"/>
   <path d="M 1.08333 0.5 L 7 0.5 L 4 7.13889 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4" x2="4" y1="20.83333333333333" y2="0.5000000000000036"/>
   <path d="M 1.08333 20.6389 L 7 20.6389 L 4 14 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:四卷35kVPT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.01340482573727" x2="10.01340482573727" y1="11.16666666666667" y2="12.83842547222383"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6934763181412" x2="10.01340482573727" y1="14.51018427778098" y2="12.83842547222382"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.333333333333332" x2="10.01340482573726" y1="14.51018427778098" y2="12.83842547222382"/>
  </symbol>
  <symbol id=":线路负荷_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="30"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.55" x2="0.5" y1="0.1499999999999968" y2="4.4"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.583333333333333" x2="3.583333333333333" y1="0.266666666666671" y2="29.93333333333333"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.55" x2="6.416666666666667" y1="0.1499999999999968" y2="4.4"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV移动变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass">
  
  
  
  
  
 </g>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,145.339,46.375) scale(1,1) translate(0,0)" writing-mode="lr" x="145.34" xml:space="preserve" y="63.38" zvalue="2593">    移动变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="678" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5536,130.464) scale(1,1) translate(0,0)" writing-mode="lr" x="80.55" xml:space="preserve" y="136.46" zvalue="3019">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="677" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.3036,165.464) scale(1,1) translate(0,0)" writing-mode="lr" x="84.3" xml:space="preserve" y="171.46" zvalue="3020">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="666" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.506,131.036) scale(1,1) translate(0,0)" writing-mode="lr" x="199.51" xml:space="preserve" y="137.04" zvalue="3031"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="665" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.506,166.036) scale(1,1) translate(0,0)" writing-mode="lr" x="195.51" xml:space="preserve" y="172.04" zvalue="3032">2023-01-28</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,745.865,686.093) scale(1,1) translate(0,0)" writing-mode="lr" x="745.87" xml:space="preserve" y="689.09" zvalue="3144">0064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,749.145,638.151) scale(1,1) translate(0,0)" writing-mode="lr" x="749.15" xml:space="preserve" y="641.15" zvalue="3146">006</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,452.572,706.074) scale(1,1) translate(0,0)" writing-mode="lr" x="452.57" xml:space="preserve" y="709.0700000000001" zvalue="3149">10kVⅣ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,429.79,825.121) scale(1,1) translate(0,0)" writing-mode="lr" x="429.79" xml:space="preserve" y="828.12" zvalue="3151">081</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,425.97,762.995) scale(1,1) translate(0,0)" writing-mode="lr" x="425.97" xml:space="preserve" y="765.99" zvalue="3153">0814</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,535.706,825.121) scale(1,1) translate(0,0)" writing-mode="lr" x="535.71" xml:space="preserve" y="828.12" zvalue="3156">082</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,530.402,762.995) scale(1,1) translate(0,0)" writing-mode="lr" x="530.4" xml:space="preserve" y="765.99" zvalue="3158">0824</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,864.475,771.854) scale(1,1) translate(0,0)" writing-mode="lr" x="864.48" xml:space="preserve" y="774.85" zvalue="3161">0904</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.717,481.875) scale(1,1) translate(0,0)" writing-mode="lr" x="851.72" xml:space="preserve" y="487.88" zvalue="3162">10MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1000.93,924.603) scale(1,1) translate(0,0)" writing-mode="lr" x="1000.93" xml:space="preserve" y="927.6" zvalue="3164">站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.36,924.603) scale(1,1) translate(0,0)" writing-mode="lr" x="887.36" xml:space="preserve" y="927.6" zvalue="3168">电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,741.71,296.979) scale(1,1) translate(0,0)" writing-mode="lr" x="741.71" xml:space="preserve" y="299.98" zvalue="3170">3064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,743.965,358.696) scale(1,1) translate(0,0)" writing-mode="lr" x="743.97" xml:space="preserve" y="361.7" zvalue="3172">306</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,822.983,339.702) scale(1,1) translate(0,0)" writing-mode="lr" x="822.98" xml:space="preserve" y="343.7" zvalue="3175">30647</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,488.555,885.105) scale(1,1) translate(0,0)" writing-mode="lr" x="488.55" xml:space="preserve" y="889.1" zvalue="3186">08167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,594.755,883.776) scale(1,1) translate(0,0)" writing-mode="lr" x="594.75" xml:space="preserve" y="887.78" zvalue="3191">08267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.706,825.121) scale(1,1) translate(0,0)" writing-mode="lr" x="643.71" xml:space="preserve" y="828.12" zvalue="3197">083</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.402,762.995) scale(1,1) translate(0,0)" writing-mode="lr" x="638.4" xml:space="preserve" y="765.99" zvalue="3199">0834</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702.755,883.776) scale(1,1) translate(0,0)" writing-mode="lr" x="702.75" xml:space="preserve" y="887.78" zvalue="3204">08367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,749.706,825.121) scale(1,1) translate(0,0)" writing-mode="lr" x="749.71" xml:space="preserve" y="828.12" zvalue="3210">084</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.402,762.995) scale(1,1) translate(0,0)" writing-mode="lr" x="744.4" xml:space="preserve" y="765.99" zvalue="3212">0844</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,808.755,883.776) scale(1,1) translate(0,0)" writing-mode="lr" x="808.75" xml:space="preserve" y="887.78" zvalue="3217">08467</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.359,771.161) scale(1,1) translate(0,0)" writing-mode="lr" x="970.36" xml:space="preserve" y="775.66" zvalue="3223">0854</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.717,450.877) scale(1,1) translate(0,0)" writing-mode="lr" x="851.72" xml:space="preserve" y="456.88" zvalue="3225">#6主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.36,902.589) scale(1,1) translate(0,0)" writing-mode="lr" x="887.36" xml:space="preserve" y="908.59" zvalue="3231">10kVⅣ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1000.93,902.589) scale(1,1) translate(0,0)" writing-mode="lr" x="1000.93" xml:space="preserve" y="908.59" zvalue="3232">10kV6号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,464.923,1150.92) scale(1,1) translate(0,0)" writing-mode="lr" x="464.92" xml:space="preserve" y="1153.92" zvalue="3255">至海南线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.765,1150.39) scale(1,1) translate(0,0)" writing-mode="lr" x="573.77" xml:space="preserve" y="1153.39" zvalue="3257">至海北线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.27,259.674) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.27" xml:space="preserve" y="262.67" zvalue="3259">35kVⅠ母电压互感器</text>
 </g>
 <g id="DisconnectorClass">
  <g id="261">
   <use class="kv10" height="30" transform="rotate(0,777.246,686.843) scale(-1.11133,0.814667) translate(-1475.79,153.474)" width="15" x="768.910807742727" xlink:href="#Disconnector:刀闸_0" y="674.6230167978459" zvalue="3143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452655054851" ObjectName="#6主变10kV侧0064"/>
   <cge:TPSR_Ref TObjectID="6192452655054851"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,777.246,686.843) scale(-1.11133,0.814667) translate(-1475.79,153.474)" width="15" x="768.910807742727" y="674.6230167978459"/></g>
  <g id="250">
   <use class="kv10" height="30" transform="rotate(0,457.204,763.745) scale(-1.11133,0.814667) translate(-867.771,170.969)" width="15" x="448.8694806880879" xlink:href="#Disconnector:刀闸_0" y="751.5245208740236" zvalue="3152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654989315" ObjectName="10kV0814"/>
   <cge:TPSR_Ref TObjectID="6192452654989315"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,457.204,763.745) scale(-1.11133,0.814667) translate(-867.771,170.969)" width="15" x="448.8694806880879" y="751.5245208740236"/></g>
  <g id="247">
   <use class="kv10" height="30" transform="rotate(0,563.12,763.745) scale(-1.11133,0.814667) translate(-1068.99,170.969)" width="15" x="554.7854873656163" xlink:href="#Disconnector:刀闸_0" y="751.5245208740236" zvalue="3157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654858243" ObjectName="10kV0824"/>
   <cge:TPSR_Ref TObjectID="6192452654858243"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,563.12,763.745) scale(-1.11133,0.814667) translate(-1068.99,170.969)" width="15" x="554.7854873656163" y="751.5245208740236"/></g>
  <g id="237">
   <use class="kv10" height="30" transform="rotate(0,895.71,770.911) scale(-1.11133,0.814667) translate(-1700.85,172.599)" width="15" x="887.3748635336367" xlink:href="#Disconnector:刀闸_0" y="758.6911938476561" zvalue="3160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654727171" ObjectName="10kVⅣ母电压互感器0904"/>
   <cge:TPSR_Ref TObjectID="6192452654727171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,895.71,770.911) scale(-1.11133,0.814667) translate(-1700.85,172.599)" width="15" x="887.3748635336367" y="758.6911938476561"/></g>
  <g id="220">
   <use class="kv35" height="30" transform="rotate(0,776.945,297.729) scale(-1.11133,0.814667) translate(-1475.22,64.9522)" width="15" x="768.6100183793526" xlink:href="#Disconnector:刀闸_0" y="285.5093688964843" zvalue="3169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654530563" ObjectName="#6主变35kV侧3064"/>
   <cge:TPSR_Ref TObjectID="6192452654530563"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,776.945,297.729) scale(-1.11133,0.814667) translate(-1475.22,64.9522)" width="15" x="768.6100183793526" y="285.5093688964843"/></g>
  <g id="656">
   <use class="kv10" height="30" transform="rotate(0,671.12,763.745) scale(-1.11133,0.814667) translate(-1274.17,170.969)" width="15" x="662.7854873656163" xlink:href="#Disconnector:刀闸_0" y="751.5245208740236" zvalue="3198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653744131" ObjectName="10kV0834"/>
   <cge:TPSR_Ref TObjectID="6192452653744131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,671.12,763.745) scale(-1.11133,0.814667) translate(-1274.17,170.969)" width="15" x="662.7854873656163" y="751.5245208740236"/></g>
  <g id="181">
   <use class="kv10" height="30" transform="rotate(0,777.12,763.745) scale(-1.11133,0.814667) translate(-1475.55,170.969)" width="15" x="768.7854873656163" xlink:href="#Disconnector:刀闸_0" y="751.5245208740236" zvalue="3211"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653416451" ObjectName="10kV0844"/>
   <cge:TPSR_Ref TObjectID="6192452653416451"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,777.12,763.745) scale(-1.11133,0.814667) translate(-1475.55,170.969)" width="15" x="768.7854873656163" y="751.5245208740236"/></g>
  <g id="158">
   <use class="kv10" height="30" transform="rotate(0,1000.83,770.911) scale(-1.11133,0.814667) translate(-1900.56,172.599)" width="15" x="992.4956101399122" xlink:href="#Disconnector:刀闸_0" y="758.6911938476561" zvalue="3222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653088771" ObjectName="10kV6号站用变0854"/>
   <cge:TPSR_Ref TObjectID="6192452653088771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1000.83,770.911) scale(-1.11133,0.814667) translate(-1900.56,172.599)" width="15" x="992.4956101399122" y="758.6911938476561"/></g>
 </g>
 <g id="BreakerClass">
  <g id="260">
   <use class="kv10" height="20" transform="rotate(0,777.026,638.151) scale(1.83111,1.88671) translate(-348.524,-291.048)" width="10" x="767.870626689619" xlink:href="#Breaker:开关_0" y="619.2837748747236" zvalue="3145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924818370563" ObjectName="#6主变10kV侧006"/>
   <cge:TPSR_Ref TObjectID="6473924818370563"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,777.026,638.151) scale(1.83111,1.88671) translate(-348.524,-291.048)" width="10" x="767.870626689619" y="619.2837748747236"/></g>
  <g id="256">
   <use class="kv10" height="20" transform="rotate(0,457.198,825.121) scale(1.83111,1.88671) translate(-203.359,-378.92)" width="10" x="448.0419862820201" xlink:href="#Breaker:开关_0" y="806.2534790039064" zvalue="3150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924818305027" ObjectName="10kV081"/>
   <cge:TPSR_Ref TObjectID="6473924818305027"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,457.198,825.121) scale(1.83111,1.88671) translate(-203.359,-378.92)" width="10" x="448.0419862820201" y="806.2534790039064"/></g>
  <g id="248">
   <use class="kv10" height="20" transform="rotate(0,563.114,825.121) scale(1.83111,1.88671) translate(-251.432,-378.92)" width="10" x="553.9579929595491" xlink:href="#Breaker:开关_0" y="806.2534790039064" zvalue="3155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924818239491" ObjectName="10kV082"/>
   <cge:TPSR_Ref TObjectID="6473924818239491"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,563.114,825.121) scale(1.83111,1.88671) translate(-251.432,-378.92)" width="10" x="553.9579929595491" y="806.2534790039064"/></g>
  <g id="213">
   <use class="kv35" height="20" transform="rotate(0,776.938,358.696) scale(1.83111,1.88671) translate(-348.484,-159.711)" width="10" x="767.7825239732853" xlink:href="#Breaker:开关_0" y="339.8292312622071" zvalue="3171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924818173955" ObjectName="#6主变35kV侧306"/>
   <cge:TPSR_Ref TObjectID="6473924818173955"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,776.938,358.696) scale(1.83111,1.88671) translate(-348.484,-159.711)" width="10" x="767.7825239732853" y="339.8292312622071"/></g>
  <g id="657">
   <use class="kv10" height="20" transform="rotate(0,671.114,825.121) scale(1.83111,1.88671) translate(-300.452,-378.92)" width="10" x="661.9579929595491" xlink:href="#Breaker:开关_0" y="806.2534790039064" zvalue="3196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924818108419" ObjectName="10kV083"/>
   <cge:TPSR_Ref TObjectID="6473924818108419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,671.114,825.121) scale(1.83111,1.88671) translate(-300.452,-378.92)" width="10" x="661.9579929595491" y="806.2534790039064"/></g>
  <g id="182">
   <use class="kv10" height="20" transform="rotate(0,777.114,825.121) scale(1.83111,1.88671) translate(-348.564,-378.92)" width="10" x="767.9579929595491" xlink:href="#Breaker:开关_0" y="806.2534790039064" zvalue="3209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924818042883" ObjectName="10kV084"/>
   <cge:TPSR_Ref TObjectID="6473924818042883"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,777.114,825.121) scale(1.83111,1.88671) translate(-348.564,-378.92)" width="10" x="767.9579929595491" y="806.2534790039064"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="258">
   <path class="kv10" d="M 777.15 656.17 L 777.15 675.03" stroke-width="1" zvalue="3147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@1" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.15 656.17 L 777.15 675.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 457.14 775.76 L 457.14 807.07" stroke-width="1" zvalue="3165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@1" LinkObjectIDznd="256@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 457.14 775.76 L 457.14 807.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv35" d="M 776.88 309.74 L 776.88 340.65" stroke-width="1" zvalue="3173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@1" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.88 309.74 L 776.88 340.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv35" d="M 813.06 323.17 L 776.88 323.17" stroke-width="1" zvalue="3176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 813.06 323.17 L 776.88 323.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv35" d="M 777.06 376.71 L 777.06 427.49" stroke-width="1" zvalue="3177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@1" LinkObjectIDznd="157@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.06 376.71 L 777.06 427.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv35" d="M 990.45 257.94 L 776.85 257.94 L 776.85 285.91" stroke-width="1" zvalue="3180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266" LinkObjectIDznd="220@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.45 257.94 L 776.85 257.94 L 776.85 285.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv35" d="M 883.51 240.29 L 883.51 257.94" stroke-width="1" zvalue="3181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="206" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.51 240.29 L 883.51 257.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv35" d="M 846.92 236.54 L 846.92 257.94" stroke-width="1" zvalue="3183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="206" MaxPinNum="2"/>
   </metadata>
  <path d="M 846.92 236.54 L 846.92 257.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 457.32 868 L 438.54 868 L 438.54 892" stroke-width="1" zvalue="3188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301" LinkObjectIDznd="249@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 457.32 868 L 438.54 868 L 438.54 892" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 563.05 775.76 L 563.05 807.07" stroke-width="1" zvalue="3189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@1" LinkObjectIDznd="248@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 563.05 775.76 L 563.05 807.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 563.24 843.14 L 563.24 940.32" stroke-width="1" zvalue="3193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@1" LinkObjectIDznd="661@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 563.24 843.14 L 563.24 940.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 584.83 867.6 L 563.24 867.6" stroke-width="1" zvalue="3194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="662@0" LinkObjectIDznd="192" MaxPinNum="2"/>
   </metadata>
  <path d="M 584.83 867.6 L 563.24 867.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 563.24 867.6 L 542.65 867.6 L 542.65 892" stroke-width="1" zvalue="3195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194" LinkObjectIDznd="246@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 563.24 867.6 L 542.65 867.6 L 542.65 892" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 671.05 775.76 L 671.05 807.07" stroke-width="1" zvalue="3202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="656@1" LinkObjectIDznd="657@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.05 775.76 L 671.05 807.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 671.24 843.14 L 671.24 940.32" stroke-width="1" zvalue="3206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="657@1" LinkObjectIDznd="3" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.24 843.14 L 671.24 940.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 692.83 867.6 L 671.24 867.6" stroke-width="1" zvalue="3207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="185" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.83 867.6 L 671.24 867.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv10" d="M 671.24 867.6 L 650.65 867.6 L 650.65 892" stroke-width="1" zvalue="3208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187" LinkObjectIDznd="655@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.24 867.6 L 650.65 867.6 L 650.65 892" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv10" d="M 777.05 775.76 L 777.05 807.07" stroke-width="1" zvalue="3215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@1" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.05 775.76 L 777.05 807.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 777.24 843.14 L 777.24 940.32" stroke-width="1" zvalue="3219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@1" LinkObjectIDznd="4" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.24 843.14 L 777.24 940.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 798.83 867.6 L 777.24 867.6" stroke-width="1" zvalue="3220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="159" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.83 867.6 L 777.24 867.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 777.24 867.6 L 756.65 867.6 L 756.65 892" stroke-width="1" zvalue="3221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163" LinkObjectIDznd="180@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.24 867.6 L 756.65 867.6 L 756.65 892" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv10" d="M 777.03 512.22 L 777.03 581.94" stroke-width="1" zvalue="3227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@1" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.03 512.22 L 777.03 581.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 777.03 581.94 L 776.97 620.1" stroke-width="1" zvalue="3228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="260@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.03 581.94 L 776.97 620.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 802.64 606.95 L 776.99 606.95" stroke-width="1" zvalue="3230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 802.64 606.95 L 776.99 606.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv35" d="M 990.45 257.94 L 1308.6 257.94" stroke-width="1" zvalue="3234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.45 257.94 L 1308.6 257.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv10" d="M 563.02 751.93 L 563.02 728.34" stroke-width="1" zvalue="3236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="257@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 563.02 751.93 L 563.02 728.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 895.61 759.1 L 895.61 728.34" stroke-width="1" zvalue="3245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="257@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.61 759.1 L 895.61 728.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv10" d="M 1000.73 759.1 L 1000.73 728.34" stroke-width="1" zvalue="3246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="257@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.73 759.1 L 1000.73 728.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv10" d="M 895.64 782.92 L 895.64 843.48" stroke-width="1" zvalue="3247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.64 782.92 L 895.64 843.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 1000.76 782.92 L 1000.76 811.83" stroke-width="1" zvalue="3248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="235@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.76 782.92 L 1000.76 811.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv10" d="M 777.02 728.34 L 777.02 751.93" stroke-width="1" zvalue="3250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@3" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.02 728.34 L 777.02 751.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 457.11 751.93 L 457.11 728.34" stroke-width="1" zvalue="3251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="257@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 457.11 751.93 L 457.11 728.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv10" d="M 563.24 940.32 L 563.24 1060.79" stroke-width="1" zvalue="3253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 563.24 940.32 L 563.24 1060.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv10" d="M 671.02 751.93 L 671.02 728.34" stroke-width="1" zvalue="3273"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="656@0" LinkObjectIDznd="257@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.02 751.93 L 671.02 728.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv10" d="M 777.18 698.85 L 777.18 728.34" stroke-width="1" zvalue="3274"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@1" LinkObjectIDznd="257@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.18 698.85 L 777.18 728.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv10" d="M 457.32 843.14 L 457.32 940.32" stroke-width="1" zvalue="3275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@1" LinkObjectIDznd="200@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 457.32 843.14 L 457.32 940.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="308">
   <path class="kv10" d="M 478.63 868 L 457.32 868" stroke-width="1" zvalue="3276"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 478.63 868 L 457.32 868" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv10" d="M 457.32 940.32 L 457.32 1061.32" stroke-width="1" zvalue="3277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 457.32 940.32 L 457.32 1061.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 671.24 940.32 L 671.24 1062.89" stroke-width="1" zvalue="3280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="7@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.24 940.32 L 671.24 1062.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 777.24 940.32 L 777.24 1063.42" stroke-width="1" zvalue="3281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="8@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.24 940.32 L 777.24 1063.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="257">
   <path class="kv10" d="M 414.29 728.34 L 1037.27 728.34" stroke-width="6" zvalue="3148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674309963779" ObjectName="10kVⅣ母"/>
   <cge:TPSR_Ref TObjectID="9288674309963779"/></metadata>
  <path d="M 414.29 728.34 L 1037.27 728.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="249">
   <use class="kv10" height="26" transform="rotate(0,438.506,904.364) scale(1,1) translate(0,0)" width="12" x="432.5063966921289" xlink:href="#Accessory:避雷器1_0" y="891.3642272949219" zvalue="3154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654923779" ObjectName="10kV081避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,438.506,904.364) scale(1,1) translate(0,0)" width="12" x="432.5063966921289" y="891.3642272949219"/></g>
  <g id="246">
   <use class="kv10" height="26" transform="rotate(0,542.618,904.364) scale(1,1) translate(0,0)" width="12" x="536.6175078032404" xlink:href="#Accessory:避雷器1_0" y="891.3642272949219" zvalue="3159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654792707" ObjectName="10kV082避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,542.618,904.364) scale(1,1) translate(0,0)" width="12" x="536.6175078032404" y="891.3642272949219"/></g>
  <g id="232">
   <use class="kv10" height="42" transform="rotate(180,883.741,868.386) scale(1.25,1.22255) translate(-172.998,-153.408)" width="30" x="864.9907959596387" xlink:href="#Accessory:4卷PT带容断器_0" y="842.712341308594" zvalue="3167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654596099" ObjectName="10kVⅣ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(180,883.741,868.386) scale(1.25,1.22255) translate(-172.998,-153.408)" width="30" x="864.9907959596387" y="842.712341308594"/></g>
  <g id="208">
   <use class="kv35" height="21" transform="rotate(90,990.449,257.936) scale(1.18303,1.29809) translate(-152.5,-56.1023)" width="8" x="985.7173139844366" xlink:href="#Accessory:中间电缆_0" y="244.3059600597131" zvalue="3178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654333955" ObjectName="#6主变35kV侧中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(90,990.449,257.936) scale(1.18303,1.29809) translate(-152.5,-56.1023)" width="8" x="985.7173139844366" y="244.3059600597131"/></g>
  <g id="207">
   <use class="kv35" height="26" transform="rotate(180,883.479,227.924) scale(-1,1) translate(-1766.96,0)" width="12" x="877.4787446056534" xlink:href="#Accessory:避雷器1_0" y="214.9238050759601" zvalue="3179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654268419" ObjectName="#6主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,883.479,227.924) scale(-1,1) translate(-1766.96,0)" width="12" x="877.4787446056534" y="214.9238050759601"/></g>
  <g id="202">
   <use class="kv35" height="18" transform="rotate(180,846.916,223.924) scale(-1.04857,1.44444) translate(-1654.17,-64.8996)" width="18" x="837.4787446056534" xlink:href="#Accessory:四卷35kVPT_0" y="210.9238050759601" zvalue="3182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654202883" ObjectName="#6主变35kV侧PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(180,846.916,223.924) scale(-1.04857,1.44444) translate(-1654.17,-64.8996)" width="18" x="837.4787446056534" y="210.9238050759601"/></g>
  <g id="200">
   <use class="kv10" height="21" transform="rotate(0,457.32,940.324) scale(1.18303,1.29809) translate(-70.0196,-212.806)" width="8" x="452.5875169431155" xlink:href="#Accessory:中间电缆_0" y="926.6940576546037" zvalue="3184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654137347" ObjectName="10kVⅠ母Ⅳ母联络线中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,457.32,940.324) scale(1.18303,1.29809) translate(-70.0196,-212.806)" width="8" x="452.5875169431155" y="926.6940576546037"/></g>
  <g id="661">
   <use class="kv10" height="21" transform="rotate(0,563.236,940.324) scale(1.18303,1.29809) translate(-86.4058,-212.806)" width="8" x="558.5035236206439" xlink:href="#Accessory:中间电缆_0" y="926.6940576546037" zvalue="3192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653809667" ObjectName="10kV082中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,563.236,940.324) scale(1.18303,1.29809) translate(-86.4058,-212.806)" width="8" x="558.5035236206439" y="926.6940576546037"/></g>
  <g id="655">
   <use class="kv10" height="26" transform="rotate(0,650.618,904.364) scale(1,1) translate(0,0)" width="12" x="644.6175078032404" xlink:href="#Accessory:避雷器1_0" y="891.3642272949219" zvalue="3200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653678595" ObjectName="10kV083避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,650.618,904.364) scale(1,1) translate(0,0)" width="12" x="644.6175078032404" y="891.3642272949219"/></g>
  <g id="188">
   <use class="kv10" height="21" transform="rotate(0,671.236,940.324) scale(1.18303,1.29809) translate(-103.114,-212.806)" width="8" x="666.5035236206439" xlink:href="#Accessory:中间电缆_0" y="926.6940576546037" zvalue="3205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653481987" ObjectName="10kV083中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,671.236,940.324) scale(1.18303,1.29809) translate(-103.114,-212.806)" width="8" x="666.5035236206439" y="926.6940576546037"/></g>
  <g id="180">
   <use class="kv10" height="26" transform="rotate(0,756.618,904.364) scale(1,1) translate(0,0)" width="12" x="750.6175078032404" xlink:href="#Accessory:避雷器1_0" y="891.3642272949219" zvalue="3213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653350915" ObjectName="10kV084避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,756.618,904.364) scale(1,1) translate(0,0)" width="12" x="750.6175078032404" y="891.3642272949219"/></g>
  <g id="164">
   <use class="kv10" height="21" transform="rotate(0,777.236,940.324) scale(1.18303,1.29809) translate(-119.514,-212.806)" width="8" x="772.5035236206439" xlink:href="#Accessory:中间电缆_0" y="926.6940576546037" zvalue="3218"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653154307" ObjectName="10kV084中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,777.236,940.324) scale(1.18303,1.29809) translate(-119.514,-212.806)" width="8" x="772.5035236206439" y="926.6940576546037"/></g>
  <g id="155">
   <use class="kv10" height="21" transform="rotate(180,777.026,581.936) scale(1.18303,1.29809) translate(-119.481,-130.506)" width="8" x="772.2940832171865" xlink:href="#Accessory:中间电缆_0" y="568.3059600597129" zvalue="3226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653023235" ObjectName="#6主变10kV侧中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(180,777.026,581.936) scale(1.18303,1.29809) translate(-119.481,-130.506)" width="8" x="772.2940832171865" y="568.3059600597129"/></g>
  <g id="151">
   <use class="kv10" height="26" transform="rotate(270,815.008,606.983) scale(1,1) translate(0,0)" width="12" x="809.0081563703593" xlink:href="#Accessory:避雷器1_0" y="593.9826286053719" zvalue="3229"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452652957699" ObjectName="#6主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,815.008,606.983) scale(1,1) translate(0,0)" width="12" x="809.0081563703593" y="593.9826286053719"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="235">
   <use class="kv10" height="30" transform="rotate(0,1000.76,848.942) scale(3.12084,2.62289) translate(-658.883,-500.932)" width="20" x="969.5542773068586" xlink:href="#EnergyConsumer:站用变带熔断器_0" y="809.5991418244051" zvalue="3163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654661635" ObjectName="10kV6号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1000.76,848.942) scale(3.12084,2.62289) translate(-658.883,-500.932)" width="20" x="969.5542773068586" y="809.5991418244051"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="211">
   <use class="kv35" height="30" transform="rotate(270,825.483,323.184) scale(-1.01422,0.867474) translate(-1639.3,47.3857)" width="12" x="819.3978738280207" xlink:href="#GroundDisconnector:地刀12_0" y="310.1719690077791" zvalue="3174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654465027" ObjectName="#6主变35kV侧30647"/>
   <cge:TPSR_Ref TObjectID="6192452654465027"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,825.483,323.184) scale(-1.01422,0.867474) translate(-1639.3,47.3857)" width="12" x="819.3978738280207" y="310.1719690077791"/></g>
  <g id="199">
   <use class="kv10" height="30" transform="rotate(270,491.055,868.012) scale(-1.01422,0.867474) translate(-975.137,130.62)" width="12" x="484.969302399449" xlink:href="#GroundDisconnector:地刀12_0" y="854.9995195791269" zvalue="3185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452654071811" ObjectName="10kV08167"/>
   <cge:TPSR_Ref TObjectID="6192452654071811"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,491.055,868.012) scale(-1.01422,0.867474) translate(-975.137,130.62)" width="12" x="484.969302399449" y="854.9995195791269"/></g>
  <g id="662">
   <use class="kv10" height="30" transform="rotate(270,597.255,867.612) scale(-1.01422,0.867474) translate(-1186.05,130.559)" width="12" x="591.1693023994492" xlink:href="#GroundDisconnector:地刀12_0" y="854.5995195791269" zvalue="3190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653940739" ObjectName="10kV08267"/>
   <cge:TPSR_Ref TObjectID="6192452653940739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,597.255,867.612) scale(-1.01422,0.867474) translate(-1186.05,130.559)" width="12" x="591.1693023994492" y="854.5995195791269"/></g>
  <g id="189">
   <use class="kv10" height="30" transform="rotate(270,705.255,867.612) scale(-1.01422,0.867474) translate(-1400.53,130.559)" width="12" x="699.1693023994492" xlink:href="#GroundDisconnector:地刀12_0" y="854.5995195791269" zvalue="3203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653613059" ObjectName="10kV08367"/>
   <cge:TPSR_Ref TObjectID="6192452653613059"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,705.255,867.612) scale(-1.01422,0.867474) translate(-1400.53,130.559)" width="12" x="699.1693023994492" y="854.5995195791269"/></g>
  <g id="165">
   <use class="kv10" height="30" transform="rotate(270,811.255,867.612) scale(-1.01422,0.867474) translate(-1611.05,130.559)" width="12" x="805.1693023994492" xlink:href="#GroundDisconnector:地刀12_0" y="854.5995195791269" zvalue="3216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452653285379" ObjectName="10kV08467"/>
   <cge:TPSR_Ref TObjectID="6192452653285379"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,811.255,867.612) scale(-1.01422,0.867474) translate(-1611.05,130.559)" width="12" x="805.1693023994492" y="854.5995195791269"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="157">
   <g id="1570">
    <use class="kv35" height="30" transform="rotate(0,777.026,469.667) scale(3.09833,3.02933) translate(-501.058,-284.187)" width="24" x="739.85" xlink:href="#PowerTransformer2:可调两卷变_0" y="424.23" zvalue="3224"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874494836739" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1571">
    <use class="kv10" height="30" transform="rotate(0,777.026,469.667) scale(3.09833,3.02933) translate(-501.058,-284.187)" width="24" x="739.85" xlink:href="#PowerTransformer2:可调两卷变_1" y="424.23" zvalue="3224"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874494902275" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399475003395" ObjectName="#6主变"/>
   <cge:TPSR_Ref TObjectID="6755399475003395"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,777.026,469.667) scale(3.09833,3.02933) translate(-501.058,-284.187)" width="24" x="739.85" y="424.23"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="275">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="275" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,669.846,344.727) scale(1,1) translate(0,0)" writing-mode="lr" x="669.85" xml:space="preserve" y="351.21" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134938783747" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="276">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="276" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,669.846,364.727) scale(1,1) translate(0,0)" writing-mode="lr" x="669.85" xml:space="preserve" y="371.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134938849283" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="278" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,669.846,609.607) scale(1,1) translate(0,0)" writing-mode="lr" x="669.85" xml:space="preserve" y="616.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134938914819" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="279" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,669.846,629.607) scale(1,1) translate(0,0)" writing-mode="lr" x="669.85" xml:space="preserve" y="636.09" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134938980355" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="280" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,669.846,384.727) scale(1,1) translate(0,0)" writing-mode="lr" x="669.85" xml:space="preserve" y="391.21" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134939045891" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="281">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="281" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,669.846,649.607) scale(1,1) translate(0,0)" writing-mode="lr" x="669.85" xml:space="preserve" y="656.09" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134939373571" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="282">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="282" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.846,450.417) scale(1,1) translate(0,0)" writing-mode="lr" x="669.85" xml:space="preserve" y="455.4" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134939242499" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="283">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="283" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.846,485.417) scale(1,1) translate(0,0)" writing-mode="lr" x="669.85" xml:space="preserve" y="490.4" zvalue="1">档位:ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134939308035" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="284">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="284" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,455.786,624.34) scale(1,1) translate(0,0)" writing-mode="lr" x="455.79" xml:space="preserve" y="630.8200000000001" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134941405187" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="285">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="285" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,455.786,644.34) scale(1,1) translate(0,0)" writing-mode="lr" x="455.79" xml:space="preserve" y="650.8200000000001" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134941470723" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="286">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="286" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,455.786,664.34) scale(1,1) translate(0,0)" writing-mode="lr" x="455.79" xml:space="preserve" y="670.8200000000001" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134941536259" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="287" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,455.786,605.59) scale(1,1) translate(0,0)" writing-mode="lr" x="455.79" xml:space="preserve" y="612.0700000000001" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134941667331" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="298" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,455.786,682.84) scale(1,1) translate(0,0)" writing-mode="lr" x="455.79" xml:space="preserve" y="689.3200000000001" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134944485379" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="25" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,995.999,944.312) scale(1,1) translate(0,0)" writing-mode="lr" x="996" xml:space="preserve" y="949.75" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134940815363" ObjectName="P"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="26" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,995.999,954.312) scale(1,1) translate(0,0)" writing-mode="lr" x="996" xml:space="preserve" y="959.75" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134940880899" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,995.999,964.312) scale(1,1) translate(0,0)" writing-mode="lr" x="996" xml:space="preserve" y="969.75" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134940946435" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.95,975.365) scale(1,1) translate(1.08963e-13,0)" writing-mode="lr" x="1003.95" xml:space="preserve" y="980.8" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134941143043" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="29" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,441.974,1095.62) scale(1,1) translate(0,0)" writing-mode="lr" x="441.97" xml:space="preserve" y="1101.06" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134969520131" ObjectName="P"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="30" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,547.237,1095.1) scale(1,1) translate(0,0)" writing-mode="lr" x="547.24" xml:space="preserve" y="1100.53" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135021883395" ObjectName="P"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="31" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,655.658,1097.2) scale(1,1) translate(0,0)" writing-mode="lr" x="655.66" xml:space="preserve" y="1102.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135054454787" ObjectName="P"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="32" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,760.921,1097.73) scale(1,1) translate(0,0)" writing-mode="lr" x="760.92" xml:space="preserve" y="1103.16" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135056027651" ObjectName="P"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="33" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,441.974,1105.62) scale(1,1) translate(0,0)" writing-mode="lr" x="441.97" xml:space="preserve" y="1111.06" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134969585667" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="34" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,547.237,1105.1) scale(1,1) translate(0,0)" writing-mode="lr" x="547.24" xml:space="preserve" y="1110.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135022866435" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="35" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,655.658,1107.2) scale(1,1) translate(0,0)" writing-mode="lr" x="655.66" xml:space="preserve" y="1112.64" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135054520323" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="36" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,760.921,1107.73) scale(1,1) translate(0,6.98152e-12)" writing-mode="lr" x="760.92" xml:space="preserve" y="1113.16" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135056093187" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="37" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,441.974,1115.62) scale(1,1) translate(0,0)" writing-mode="lr" x="441.97" xml:space="preserve" y="1121.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134969651203" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,547.237,1115.1) scale(1,1) translate(0,0)" writing-mode="lr" x="547.24" xml:space="preserve" y="1120.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135022931971" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="39" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,655.658,1117.2) scale(1,1) translate(0,0)" writing-mode="lr" x="655.66" xml:space="preserve" y="1122.64" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135055110147" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="40" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,760.921,1117.73) scale(1,1) translate(0,0)" writing-mode="lr" x="760.92" xml:space="preserve" y="1123.16" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135056158723" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,452.526,1128.9) scale(1,1) translate(-5.73576e-13,0)" writing-mode="lr" x="452.53" xml:space="preserve" y="1134.34" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134982168579" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557.789,1128.38) scale(1,1) translate(0,0)" writing-mode="lr" x="557.79" xml:space="preserve" y="1133.81" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135045148675" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.211,1130.48) scale(1,1) translate(0,0)" writing-mode="lr" x="666.21" xml:space="preserve" y="1135.92" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135055372291" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,771.474,1131.01) scale(1,1) translate(0,0)" writing-mode="lr" x="771.47" xml:space="preserve" y="1136.45" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135056420867" ObjectName="Cos"/>
   </metadata>
  </g>
 </g>
</svg>