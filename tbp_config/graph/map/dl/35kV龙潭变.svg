<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549590949890" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带负荷1_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="9.062458524793254" xlink:href="#terminal" y="41.29040359122629"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,25.61,15.59) scale(1,1) translate(0,0)" width="11" x="20.11" y="13.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.61245852479332" x2="29.61245852479332" y1="19.34040359122622" y2="12.34040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666667" x2="25.8624585247933" y1="23.94225544307809" y2="23.94225544307809"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.86245852479331" x2="25.86245852479331" y1="23.93299618381883" y2="21.09040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.6124585247933" x2="21.6124585247933" y1="21.34040359122623" y2="19.34040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.61245852479333" x2="29.61245852479333" y1="12.34040359122622" y2="10.34040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.062458524793264" x2="9.062458524793264" y1="31.39040359122627" y2="25.89040359122627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.64772110866599" x2="25.64772110866599" y1="10.08674821859629" y2="3.405450010721147"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.71438777533266" x2="23.71438777533266" y1="3.016127685651949" y2="3.016127685651949"/>
   <ellipse cx="5.54" cy="23.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.55048569403981" x2="13.55048569403981" y1="35.78084700683308" y2="35.78084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.7608382776216" x2="23.48402243293775" y1="3.254208141873306" y2="3.254208141873306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.01083827762157" x2="24.4006890996044" y1="2.00420814187332" y2="2.00420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.51083827762159" x2="25.06735576627107" y1="0.5042081418732955" y2="0.5042081418732955"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="25.30654513693459" y2="22.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="22.79040359122621" y2="22.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="25.40299989806297" y2="22.78208583485582"/>
   <ellipse cx="5.54" cy="16.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="23.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.8644003886642" x2="13.8644003886642" y1="36.19213090500035" y2="36.19213090500035"/>
   <ellipse cx="12.54" cy="16.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="18.07076893362115" y2="18.07076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="24.32076893362116" y2="24.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="16.72611741162254" y2="17.85543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="18.24992879670395" y2="16.72611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="16.72611741162252" y2="14.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="17.57076893362115" y2="17.57076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="23.97611741162255" y2="21.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="25.49992879670398" y2="23.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="23.97611741162255" y2="25.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="16.72611741162253" y2="14.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="18.24992879670396" y2="16.72611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="16.72611741162254" y2="17.85543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.09103363843511" x2="9.09103363843511" y1="31.15822158129307" y2="41.41276827235689"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Accessory:10kV接地信号源_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="2"/>
   <rect fill-opacity="0" height="16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,10) scale(1,1) translate(0,0)" width="20" x="2.5" y="2"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(85,255,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV龙潭变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="35kV龙潭变_软压板.svg"><rect fill-opacity="0" height="40" width="97.14" x="84.89" y="527.89" zvalue="1416"/></g>
  <g href="35kV龙潭变_直流监控.svg"><rect fill-opacity="0" height="40" width="97.14" x="84.89" y="583.89" zvalue="1417"/></g>
  <g href="单厂站信息-dali.svg"><rect fill-opacity="0" height="40" width="97.14" x="84" y="639" zvalue="1505"/></g>
  <g href="主网厂站告警信息.svg"><rect fill-opacity="0" height="40" width="97.14" x="84" y="699" zvalue="1507"/></g>
 </g>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,171.56,54.375) scale(1,1) translate(0,0)" writing-mode="lr" x="171.56" xml:space="preserve" y="71.37" zvalue="1303">     龙潭变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="86" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,133.46,547.889) scale(1,1) translate(0,0)" width="97.14" x="84.89" y="527.89" zvalue="1416"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,133.46,547.889) scale(1,1) translate(0,0)" writing-mode="lr" x="133.46" xml:space="preserve" y="551.89" zvalue="1416">软压板</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="85" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,133.46,603.889) scale(1,1) translate(0,0)" width="97.14" x="84.89" y="583.89" zvalue="1417"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,133.46,603.889) scale(1,1) translate(0,0)" writing-mode="lr" x="133.46" xml:space="preserve" y="607.89" zvalue="1417">直流监控</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="30" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,195.571,150) scale(1,1) translate(0,0)" width="97.14" x="147" y="130" zvalue="1503"/>
  <text fill="rgb(255,170,0)" font-family="FangSong" font-size="19" id="5" stroke="rgb(255,170,0)" text-anchor="middle" transform="rotate(0,195.571,150) scale(1,1) translate(0,0)" writing-mode="lr" x="195.57" xml:space="preserve" y="156.5" zvalue="1503">全站可控</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="31" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,132.571,659) scale(1,1) translate(0,0)" width="97.14" x="84" y="639" zvalue="1505"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,132.571,659) scale(1,1) translate(0,0)" writing-mode="lr" x="132.57" xml:space="preserve" y="668" zvalue="1505">AVC</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="32" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,132.571,719) scale(1,1) translate(0,0)" width="97.14" x="84" y="699" zvalue="1507"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="24" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,132.571,719) scale(1,1) translate(0,0)" writing-mode="lr" x="132.57" xml:space="preserve" y="728" zvalue="1507">今日告警</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1448.03,430.036) scale(1,1) translate(0,0)" writing-mode="lr" x="1448.03" xml:space="preserve" y="433.04" zvalue="975">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1111.6,629.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1111.6" xml:space="preserve" y="632.17" zvalue="977">35kV1号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,397.092,801.519) scale(1,1) translate(0,0)" writing-mode="lr" x="397.09" xml:space="preserve" y="804.52" zvalue="979">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,989.218,485.729) scale(1,1) translate(0,0)" writing-mode="lr" x="989.22" xml:space="preserve" y="488.73" zvalue="981">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,992.718,535.446) scale(1,1) translate(0,0)" writing-mode="lr" x="992.72" xml:space="preserve" y="538.45" zvalue="983">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,989.218,786.843) scale(1,1) translate(0,0)" writing-mode="lr" x="989.22" xml:space="preserve" y="789.84" zvalue="986">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,992.718,742.901) scale(1,1) translate(0,0)" writing-mode="lr" x="992.72" xml:space="preserve" y="745.9" zvalue="988">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,797.723,502.298) scale(1,1) translate(0,0)" writing-mode="lr" x="797.72" xml:space="preserve" y="505.3" zvalue="990">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,738.464,486.901) scale(1,1) translate(0,0)" writing-mode="lr" x="738.46" xml:space="preserve" y="489.9" zvalue="992">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,738.464,536.132) scale(1,1) translate(0,0)" writing-mode="lr" x="738.46" xml:space="preserve" y="539.13" zvalue="994">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,799.074,363.371) scale(1,1) translate(0,0)" writing-mode="lr" x="799.0700000000001" xml:space="preserve" y="366.37" zvalue="1000">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.074,314.347) scale(1,1) translate(0,0)" writing-mode="lr" x="802.0700000000001" xml:space="preserve" y="317.35" zvalue="1002">3716</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774.711,133.191) scale(1,1) translate(0,0)" writing-mode="lr" x="774.71" xml:space="preserve" y="136.19" zvalue="1004">35kV跃龙线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.293,413.495) scale(1,1) translate(0,0)" writing-mode="lr" x="802.29" xml:space="preserve" y="416.49" zvalue="1006">3711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,740.093,290.317) scale(1,1) translate(0,0)" writing-mode="lr" x="740.09" xml:space="preserve" y="293.32" zvalue="1008">37167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1302.96,488.823) scale(1,1) translate(0,0)" writing-mode="lr" x="1302.96" xml:space="preserve" y="491.82" zvalue="1060">3731</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,775.745,904.158) scale(1,1) translate(0,0)" writing-mode="lr" x="775.74" xml:space="preserve" y="907.16" zvalue="1062">073</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,775.267,950.366) scale(1,1) translate(0,0)" writing-mode="lr" x="775.27" xml:space="preserve" y="953.37" zvalue="1064">0736</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.052,858.616) scale(1,1) translate(0,0)" writing-mode="lr" x="777.05" xml:space="preserve" y="861.62" zvalue="1066">0731</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.44,1072.01) scale(1,1) translate(0,0)" writing-mode="lr" x="786.4400000000001" xml:space="preserve" y="1075.01" zvalue="1068">10kV密古线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,783.758,596.409) scale(1,1) translate(0,0)" writing-mode="lr" x="783.76" xml:space="preserve" y="599.41" zvalue="1079">35kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,403.006,856.373) scale(1,1) translate(0,0)" writing-mode="lr" x="403.01" xml:space="preserve" y="859.37" zvalue="1100">0721</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,429.936,979.167) scale(1,1) translate(0,0)" writing-mode="lr" x="429.94" xml:space="preserve" y="982.17" zvalue="1102">10kV故障信号源</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,571.888,858.616) scale(1,1) translate(0,0)" writing-mode="lr" x="571.89" xml:space="preserve" y="861.62" zvalue="1128">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,571.888,906.282) scale(1,1) translate(0,0)" writing-mode="lr" x="571.89" xml:space="preserve" y="909.28" zvalue="1130">0905</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,597.333,979.167) scale(1,1) translate(0,0)" writing-mode="lr" x="597.33" xml:space="preserve" y="982.17" zvalue="1132">10kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.745,907.158) scale(1,1) translate(0,0)" writing-mode="lr" x="995.74" xml:space="preserve" y="910.16" zvalue="1139">074</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.267,950.366) scale(1,1) translate(0,0)" writing-mode="lr" x="995.27" xml:space="preserve" y="953.37" zvalue="1141">0746</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.149,861.616) scale(1,1) translate(0,0)" writing-mode="lr" x="997.15" xml:space="preserve" y="864.62" zvalue="1143">0741</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.38,1073.01) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.38" xml:space="preserve" y="1076.01" zvalue="1145">10kV富厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1215.74,904.158) scale(1,1) translate(0,0)" writing-mode="lr" x="1215.74" xml:space="preserve" y="907.16" zvalue="1153">075</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1215.27,950.366) scale(1,1) translate(0,0)" writing-mode="lr" x="1215.27" xml:space="preserve" y="953.37" zvalue="1155">0756</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1217.15,858.616) scale(1,1) translate(0,0)" writing-mode="lr" x="1217.15" xml:space="preserve" y="861.62" zvalue="1157">0751</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1239.88,1074.01) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.88" xml:space="preserve" y="1080.51" zvalue="1159">10kV龙瓦线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="430" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1303.59,362.601) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.59" xml:space="preserve" y="365.6" zvalue="1254">372</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="428" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1298.2,315.116) scale(1,1) translate(0,0)" writing-mode="lr" x="1298.2" xml:space="preserve" y="318.12" zvalue="1256">3726</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="426" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1274.74,133.191) scale(1,1) translate(0,0)" writing-mode="lr" x="1274.74" xml:space="preserve" y="136.19" zvalue="1258">35kV龙鸡线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="419" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1300.13,412.725) scale(1,1) translate(0,0)" writing-mode="lr" x="1300.13" xml:space="preserve" y="415.73" zvalue="1260">3721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="417" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238.31,290.317) scale(1,1) translate(0,0)" writing-mode="lr" x="1238.31" xml:space="preserve" y="293.32" zvalue="1262">37267</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.95,592.282) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.95" xml:space="preserve" y="595.28" zvalue="1277">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="471" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.164,1002.71) scale(1,1) translate(0,0)" writing-mode="lr" x="848.16" xml:space="preserve" y="1005.71" zvalue="1280">0738</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="469" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.206,1073.01) scale(1,1) translate(0,0)" writing-mode="lr" x="891.21" xml:space="preserve" y="1076.01" zvalue="1282">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="487" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415.74,904.158) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.74" xml:space="preserve" y="907.16" zvalue="1287">076</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="486" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415.27,950.366) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.27" xml:space="preserve" y="953.37" zvalue="1289">0766</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="485" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1417.15,858.616) scale(1,1) translate(0,0)" writing-mode="lr" x="1417.15" xml:space="preserve" y="861.62" zvalue="1291">0761</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="484" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1442.83,1073.01) scale(1,1) translate(0,0)" writing-mode="lr" x="1442.83" xml:space="preserve" y="1076.01" zvalue="1293">10kV清河线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,806.128,251.284) scale(1,1) translate(0,0)" writing-mode="lr" x="806.13" xml:space="preserve" y="254.28" zvalue="1318">AB相</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,834.394,205.591) scale(1,1) translate(0,0)" writing-mode="lr" x="834.39" xml:space="preserve" y="208.59" zvalue="1320">3719</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1306.34,257.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1306.34" xml:space="preserve" y="260.62" zvalue="1324">AB相</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1331.43,205.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1331.43" xml:space="preserve" y="208.59" zvalue="1327">3729</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109.61,652.775) scale(1,1) translate(0,0)" writing-mode="lr" x="1109.61" xml:space="preserve" y="655.78" zvalue="1400">(1.25MVA)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,109.875,334.429) scale(1,1) translate(0,0)" writing-mode="lr" x="109.88" xml:space="preserve" y="340.43" zvalue="1431">是否失压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,191.375,334.429) scale(1,1) translate(0,0)" writing-mode="lr" x="191.38" xml:space="preserve" y="340.43" zvalue="1432">失压排除</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98.2679,268.75) scale(1,1) translate(0,0)" writing-mode="lr" x="98.27" xml:space="preserve" y="274.75" zvalue="1436">事故</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,158.268,268.75) scale(1,1) translate(0,0)" writing-mode="lr" x="158.27" xml:space="preserve" y="274.75" zvalue="1439">异常</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.268,268.75) scale(1,1) translate(0,0)" writing-mode="lr" x="218.27" xml:space="preserve" y="274.75" zvalue="1440">告知</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,106.232,389.179) scale(1,1) translate(0,0)" writing-mode="lr" x="106.23" xml:space="preserve" y="395.18" zvalue="1489">负荷总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,102.482,424.929) scale(1,1) translate(0,0)" writing-mode="lr" x="102.48" xml:space="preserve" y="430.93" zvalue="1490">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,106.232,459.929) scale(1,1) translate(0,0)" writing-mode="lr" x="106.23" xml:space="preserve" y="465.93" zvalue="1491">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.435,425.5) scale(1,1) translate(0,0)" writing-mode="lr" x="221.43" xml:space="preserve" y="431.5" zvalue="1492">龙潭变-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,217.435,460.5) scale(1,1) translate(0,0)" writing-mode="lr" x="217.43" xml:space="preserve" y="466.5" zvalue="1493">2023-04-21</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="410">
   <path class="kv35" d="M 672.86 449.75 L 1482.86 449.75" stroke-width="6" zvalue="974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674270904325" ObjectName="35kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674270904325"/></metadata>
  <path d="M 672.86 449.75 L 1482.86 449.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="408">
   <path class="kv10" d="M 362 822.91 L 1561.43 822.91" stroke-width="6" zvalue="978"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674270838789" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674270838789"/></metadata>
  <path d="M 362 822.91 L 1561.43 822.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="409">
   <g id="4090">
    <use class="kv35" height="30" transform="rotate(0,1023.91,637.667) scale(3.09833,2.96282) translate(-668.257,-393.001)" width="24" x="986.73" xlink:href="#PowerTransformer2:可调两卷变_0" y="593.22" zvalue="976"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874459971587" ObjectName="35"/>
    </metadata>
   </g>
   <g id="4091">
    <use class="kv10" height="30" transform="rotate(0,1023.91,637.667) scale(3.09833,2.96282) translate(-668.257,-393.001)" width="24" x="986.73" xlink:href="#PowerTransformer2:可调两卷变_1" y="593.22" zvalue="976"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874460037123" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399458684931" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399458684931"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1023.91,637.667) scale(3.09833,2.96282) translate(-668.257,-393.001)" width="24" x="986.73" y="593.22"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="407">
   <use class="kv35" height="30" transform="rotate(0,1022.61,485.729) scale(-1.11133,0.814667) translate(-1941.94,107.721)" width="15" x="1014.276685046019" xlink:href="#Disconnector:刀闸_0" y="473.509375665838" zvalue="980"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132850181" ObjectName="#1主变35kV侧3011"/>
   <cge:TPSR_Ref TObjectID="6192451132850181"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1022.61,485.729) scale(-1.11133,0.814667) translate(-1941.94,107.721)" width="15" x="1014.276685046019" y="473.509375665838"/></g>
  <g id="401">
   <use class="kv10" height="30" transform="rotate(0,1022.76,786.843) scale(-1.11133,0.814667) translate(-1942.22,176.224)" width="15" x="1014.422578533083" xlink:href="#Disconnector:刀闸_0" y="774.6230120294742" zvalue="985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132784645" ObjectName="#1主变10kV侧0011"/>
   <cge:TPSR_Ref TObjectID="6192451132784645"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1022.76,786.843) scale(-1.11133,0.814667) translate(-1942.22,176.224)" width="15" x="1014.422578533083" y="774.6230120294742"/></g>
  <g id="399">
   <use class="kv35" height="30" transform="rotate(0,775.612,498.452) scale(-1.11133,0.814667) translate(-1472.69,110.616)" width="15" x="767.2766850460195" xlink:href="#Disconnector:刀闸_0" y="486.2315978880603" zvalue="989"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132719109" ObjectName="35kVⅠ母电压互感器3901"/>
   <cge:TPSR_Ref TObjectID="6192451132719109"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,775.612,498.452) scale(-1.11133,0.814667) translate(-1472.69,110.616)" width="15" x="767.2766850460195" y="486.2315978880603"/></g>
  <g id="382">
   <use class="kv35" height="30" transform="rotate(0,775.809,313.828) scale(-1.11133,0.814667) translate(-1473.06,68.6146)" width="15" x="767.4736547851562" xlink:href="#Disconnector:刀闸_0" y="301.6078578601484" zvalue="1001"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132391429" ObjectName="35kV跃龙线3716"/>
   <cge:TPSR_Ref TObjectID="6192451132391429"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,775.809,313.828) scale(-1.11133,0.814667) translate(-1473.06,68.6146)" width="15" x="767.4736547851562" y="301.6078578601484"/></g>
  <g id="380">
   <use class="kv35" height="30" transform="rotate(0,776.028,413.495) scale(-1.11133,0.814667) translate(-1473.48,91.2883)" width="15" x="767.6930710143463" xlink:href="#Disconnector:刀闸_0" y="401.2745271809895" zvalue="1005"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132260357" ObjectName="35kV跃龙线3711"/>
   <cge:TPSR_Ref TObjectID="6192451132260357"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,776.028,413.495) scale(-1.11133,0.814667) translate(-1473.48,91.2883)" width="15" x="767.6930710143463" y="401.2745271809895"/></g>
  <g id="331">
   <use class="kv10" height="30" transform="rotate(0,802.877,950.616) scale(-1.11133,0.814667) translate(-1524.49,213.481)" width="15" x="794.5418366033379" xlink:href="#Disconnector:刀闸_0" y="938.3957366480272" zvalue="1063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131801605" ObjectName="10kV密古线0736"/>
   <cge:TPSR_Ref TObjectID="6192451131801605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,802.877,950.616) scale(-1.11133,0.814667) translate(-1524.49,213.481)" width="15" x="794.5418366033379" y="938.3957366480272"/></g>
  <g id="327">
   <use class="kv10" height="30" transform="rotate(0,802.787,858.616) scale(-1.11133,0.814667) translate(-1524.32,192.552)" width="15" x="794.452154032283" xlink:href="#Disconnector:刀闸_0" y="846.3957366001243" zvalue="1065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131736069" ObjectName="10kV密古线0731"/>
   <cge:TPSR_Ref TObjectID="6192451131736069"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,802.787,858.616) scale(-1.11133,0.814667) translate(-1524.32,192.552)" width="15" x="794.452154032283" y="846.3957366001243"/></g>
  <g id="264">
   <use class="kv10" height="30" transform="rotate(0,598.483,858.616) scale(-1.11133,0.814667) translate(-1136.18,192.552)" width="15" x="590.1481640482775" xlink:href="#Disconnector:刀闸_0" y="846.3957366943359" zvalue="1127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131342853" ObjectName="10kVⅠ母电压互感器0901"/>
   <cge:TPSR_Ref TObjectID="6192451131342853"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,598.483,858.616) scale(-1.11133,0.814667) translate(-1136.18,192.552)" width="15" x="590.1481640482775" y="846.3957366943359"/></g>
  <g id="317">
   <use class="kv10" height="30" transform="rotate(0,1022.88,950.616) scale(-1.11133,0.814667) translate(-1942.45,213.481)" width="15" x="1014.541836603338" xlink:href="#Disconnector:刀闸_0" y="938.3957366480272" zvalue="1140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131080709" ObjectName="10kV富厂线0746"/>
   <cge:TPSR_Ref TObjectID="6192451131080709"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1022.88,950.616) scale(-1.11133,0.814667) translate(-1942.45,213.481)" width="15" x="1014.541836603338" y="938.3957366480272"/></g>
  <g id="316">
   <use class="kv10" height="30" transform="rotate(0,1022.88,858.616) scale(-1.11133,0.814667) translate(-1942.46,192.552)" width="15" x="1014.548877333327" xlink:href="#Disconnector:刀闸_0" y="846.3957366001243" zvalue="1142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131015173" ObjectName="10kV富厂线0741"/>
   <cge:TPSR_Ref TObjectID="6192451131015173"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1022.88,858.616) scale(-1.11133,0.814667) translate(-1942.46,192.552)" width="15" x="1014.548877333327" y="846.3957366001243"/></g>
  <g id="332">
   <use class="kv10" height="30" transform="rotate(0,1242.88,950.616) scale(-1.11133,0.814667) translate(-2360.41,213.481)" width="15" x="1234.541836603338" xlink:href="#Disconnector:刀闸_0" y="938.3957366480272" zvalue="1154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451130818565" ObjectName="10kV龙瓦线0756"/>
   <cge:TPSR_Ref TObjectID="6192451130818565"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1242.88,950.616) scale(-1.11133,0.814667) translate(-2360.41,213.481)" width="15" x="1234.541836603338" y="938.3957366480272"/></g>
  <g id="330">
   <use class="kv10" height="30" transform="rotate(0,1242.88,858.616) scale(-1.11133,0.814667) translate(-2360.42,192.552)" width="15" x="1234.548877333327" xlink:href="#Disconnector:刀闸_0" y="846.3957366001243" zvalue="1156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451130753029" ObjectName="10kV龙瓦线0751"/>
   <cge:TPSR_Ref TObjectID="6192451130753029"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1242.88,858.616) scale(-1.11133,0.814667) translate(-2360.42,192.552)" width="15" x="1234.548877333327" y="846.3957366001243"/></g>
  <g id="457">
   <use class="kv35" height="30" transform="rotate(0,1275.81,313.828) scale(-1.11133,0.814667) translate(-2422.97,68.6146)" width="15" x="1267.473654785156" xlink:href="#Disconnector:刀闸_0" y="301.6078578601484" zvalue="1255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133374469" ObjectName="35kV龙鸡线3726"/>
   <cge:TPSR_Ref TObjectID="6192451133374469"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1275.81,313.828) scale(-1.11133,0.814667) translate(-2422.97,68.6146)" width="15" x="1267.473654785156" y="301.6078578601484"/></g>
  <g id="455">
   <use class="kv35" height="30" transform="rotate(0,1276.02,413.495) scale(-1.11133,0.814667) translate(-2423.38,91.2883)" width="15" x="1267.686030284357" xlink:href="#Disconnector:刀闸_0" y="401.2745271809895" zvalue="1259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133243397" ObjectName="35kV龙鸡线3721"/>
   <cge:TPSR_Ref TObjectID="6192451133243397"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1276.02,413.495) scale(-1.11133,0.814667) translate(-2423.38,91.2883)" width="15" x="1267.686030284357" y="401.2745271809895"/></g>
  <g id="496">
   <use class="kv10" height="30" transform="rotate(0,1442.88,950.616) scale(-1.11133,0.814667) translate(-2740.37,213.481)" width="15" x="1434.541836603338" xlink:href="#Disconnector:刀闸_0" y="938.3957366480272" zvalue="1288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133833221" ObjectName="10kV清河线0766"/>
   <cge:TPSR_Ref TObjectID="6192451133833221"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1442.88,950.616) scale(-1.11133,0.814667) translate(-2740.37,213.481)" width="15" x="1434.541836603338" y="938.3957366480272"/></g>
  <g id="495">
   <use class="kv10" height="30" transform="rotate(0,1442.88,858.616) scale(-1.11133,0.814667) translate(-2740.38,192.552)" width="15" x="1434.548877333327" xlink:href="#Disconnector:刀闸_0" y="846.3957366001243" zvalue="1290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133767685" ObjectName="10kV清河线0761"/>
   <cge:TPSR_Ref TObjectID="6192451133767685"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1442.88,858.616) scale(-1.11133,0.814667) translate(-2740.38,192.552)" width="15" x="1434.548877333327" y="846.3957366001243"/></g>
 </g>
 <g id="BreakerClass">
  <g id="404">
   <use class="kv35" height="20" transform="rotate(0,1023.85,534.696) scale(1.828,1.8835) translate(-459.617,-241.977)" width="10" x="1014.709958161125" xlink:href="#Breaker:开关_0" y="515.8612926964488" zvalue="982"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924653744132" ObjectName="#1主变35kV侧301"/>
   <cge:TPSR_Ref TObjectID="6473924653744132"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1023.85,534.696) scale(1.828,1.8835) translate(-459.617,-241.977)" width="10" x="1014.709958161125" y="515.8612926964488"/></g>
  <g id="400">
   <use class="kv10" height="20" transform="rotate(0,1022.76,742.151) scale(1.828,1.8835) translate(-459.122,-339.288)" width="10" x="1013.617578591118" xlink:href="#Breaker:开关_0" y="723.3158381509942" zvalue="987"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924653678596" ObjectName="#1主变10kV侧001"/>
   <cge:TPSR_Ref TObjectID="6473924653678596"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1022.76,742.151) scale(1.828,1.8835) translate(-459.122,-339.288)" width="10" x="1013.617578591118" y="723.3158381509942"/></g>
  <g id="383">
   <use class="kv35" height="20" transform="rotate(0,775.809,362.621) scale(1.828,1.8835) translate(-347.266,-161.261)" width="10" x="766.6686547851563" xlink:href="#Breaker:开关_0" y="343.7855351206912" zvalue="999"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924653613060" ObjectName="35kV跃龙线371"/>
   <cge:TPSR_Ref TObjectID="6473924653613060"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,775.809,362.621) scale(1.828,1.8835) translate(-347.266,-161.261)" width="10" x="766.6686547851563" y="343.7855351206912"/></g>
  <g id="333">
   <use class="kv10" height="20" transform="rotate(0,802.877,903.408) scale(1.828,1.8835) translate(-359.526,-414.93)" width="10" x="793.7368366033379" xlink:href="#Breaker:开关_0" y="884.5734100341797" zvalue="1061"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924653547524" ObjectName="10kV密古线073"/>
   <cge:TPSR_Ref TObjectID="6473924653547524"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,802.877,903.408) scale(1.828,1.8835) translate(-359.526,-414.93)" width="10" x="793.7368366033379" y="884.5734100341797"/></g>
  <g id="318">
   <use class="kv10" height="20" transform="rotate(0,1022.88,903.408) scale(1.828,1.8835) translate(-459.176,-414.93)" width="10" x="1013.736836603338" xlink:href="#Breaker:开关_0" y="884.5734100341797" zvalue="1138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924653481988" ObjectName="10kV富厂线074"/>
   <cge:TPSR_Ref TObjectID="6473924653481988"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1022.88,903.408) scale(1.828,1.8835) translate(-459.176,-414.93)" width="10" x="1013.736836603338" y="884.5734100341797"/></g>
  <g id="336">
   <use class="kv10" height="20" transform="rotate(0,1242.88,903.408) scale(1.828,1.8835) translate(-558.826,-414.93)" width="10" x="1233.736836603338" xlink:href="#Breaker:开关_0" y="884.5734100341797" zvalue="1152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924653416452" ObjectName="10kV龙瓦线075"/>
   <cge:TPSR_Ref TObjectID="6473924653416452"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1242.88,903.408) scale(1.828,1.8835) translate(-558.826,-414.93)" width="10" x="1233.736836603338" y="884.5734100341797"/></g>
  <g id="459">
   <use class="kv35" height="20" transform="rotate(0,1275.8,362.621) scale(1.828,1.8835) translate(-573.74,-161.261)" width="10" x="1266.661614055167" xlink:href="#Breaker:开关_0" y="343.7855351206912" zvalue="1253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924653809668" ObjectName="35kV龙鸡线372"/>
   <cge:TPSR_Ref TObjectID="6473924653809668"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1275.8,362.621) scale(1.828,1.8835) translate(-573.74,-161.261)" width="10" x="1266.661614055167" y="343.7855351206912"/></g>
  <g id="497">
   <use class="kv10" height="20" transform="rotate(0,1442.88,903.408) scale(1.828,1.8835) translate(-649.417,-414.93)" width="10" x="1433.736836603338" xlink:href="#Breaker:开关_0" y="884.5734100341797" zvalue="1286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924653875204" ObjectName="10kV清河线076"/>
   <cge:TPSR_Ref TObjectID="6473924653875204"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1442.88,903.408) scale(1.828,1.8835) translate(-649.417,-414.93)" width="10" x="1433.736836603338" y="884.5734100341797"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="403">
   <path class="kv35" d="M 1023.97 552.68 L 1023.94 596.41" stroke-width="1" zvalue="984"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="404@1" LinkObjectIDznd="409@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.97 552.68 L 1023.94 596.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="396">
   <path class="kv35" d="M 775.92 548.25 L 775.92 510.46" stroke-width="1" zvalue="995"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="399@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.92 548.25 L 775.92 510.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="393">
   <path class="kv35" d="M 775.51 486.64 L 775.51 449.75" stroke-width="1" zvalue="996"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="410@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.51 486.64 L 775.51 449.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="385">
   <path class="kv35" d="M 751.97 523.41 L 775.92 523.41" stroke-width="1" zvalue="997"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="397@0" LinkObjectIDznd="396" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.97 523.41 L 775.92 523.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv35" d="M 775.75 344.6 L 775.74 325.84" stroke-width="1" zvalue="1012"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="383@0" LinkObjectIDznd="382@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.75 344.6 L 775.74 325.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="344">
   <path class="kv10" d="M 1022.7 724.13 L 1022.7 679.28" stroke-width="1" zvalue="1050"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="400@0" LinkObjectIDznd="409@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.7 724.13 L 1022.7 679.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv35" d="M 746.84 205.83 L 746.84 187.59 L 775.71 187.59" stroke-width="1" zvalue="1055"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342@0" LinkObjectIDznd="313" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.84 205.83 L 746.84 187.59 L 775.71 187.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="338">
   <path class="kv35" d="M 751.97 275.51 L 775.71 275.51" stroke-width="1" zvalue="1056"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="379@0" LinkObjectIDznd="313" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.97 275.51 L 775.71 275.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="323">
   <path class="kv10" d="M 802.72 870.63 L 802.82 885.39" stroke-width="1" zvalue="1071"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327@1" LinkObjectIDznd="333@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 802.72 870.63 L 802.82 885.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv10" d="M 803 921.4 L 803 938.8" stroke-width="1" zvalue="1072"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333@1" LinkObjectIDznd="331@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 803 921.4 L 803 938.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="321">
   <path class="kv10" d="M 802.81 1026.36 L 802.81 962.63" stroke-width="1" zvalue="1073"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@0" LinkObjectIDznd="331@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 802.81 1026.36 L 802.81 962.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="320">
   <path class="kv10" d="M 785.22 994.29 L 785.22 979.38 L 802.81 979.38" stroke-width="1" zvalue="1074"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@0" LinkObjectIDznd="321" MaxPinNum="2"/>
   </metadata>
  <path d="M 785.22 994.29 L 785.22 979.38 L 802.81 979.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv35" d="M 775.71 302.01 L 775.71 171.57" stroke-width="1" zvalue="1077"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@0" LinkObjectIDznd="381@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.71 302.01 L 775.71 171.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="kv35" d="M 775.93 380.61 L 775.93 401.68" stroke-width="1" zvalue="1091"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="383@1" LinkObjectIDznd="380@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.93 380.61 L 775.93 401.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv10" d="M 1022.66 775.03 L 1022.88 760.14" stroke-width="1" zvalue="1093"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="401@0" LinkObjectIDznd="400@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.66 775.03 L 1022.88 760.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv35" d="M 1022.54 497.74 L 1022.54 516.68" stroke-width="1" zvalue="1095"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="407@1" LinkObjectIDznd="404@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.54 497.74 L 1022.54 516.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv10" d="M 598.39 846.8 L 598.39 822.91" stroke-width="1" zvalue="1134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="408@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 598.39 846.8 L 598.39 822.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv10" d="M 598.42 870.63 L 598.42 898.49" stroke-width="1" zvalue="1135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 598.42 870.63 L 598.42 898.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 598.09 915.89 L 598.09 898.22" stroke-width="1" zvalue="1136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="260" MaxPinNum="2"/>
   </metadata>
  <path d="M 598.09 915.89 L 598.09 898.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv10" d="M 598.42 883.76 L 620.33 883.76 L 620.33 897.22" stroke-width="1" zvalue="1137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260" LinkObjectIDznd="288@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 598.42 883.76 L 620.33 883.76 L 620.33 897.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv10" d="M 1022.82 870.63 L 1022.82 885.39" stroke-width="1" zvalue="1148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316@1" LinkObjectIDznd="318@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.82 870.63 L 1022.82 885.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 1023 921.4 L 1023 938.8" stroke-width="1" zvalue="1149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318@1" LinkObjectIDznd="317@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023 921.4 L 1023 938.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 1022.83 1027.36 L 1022.81 962.63" stroke-width="1" zvalue="1150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="317@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.83 1027.36 L 1022.81 962.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv10" d="M 1242.79 846.8 L 1242.79 822.91" stroke-width="1" zvalue="1161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="408@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1242.79 846.8 L 1242.79 822.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 1242.82 870.63 L 1242.82 885.39" stroke-width="1" zvalue="1162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@1" LinkObjectIDznd="336@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1242.82 870.63 L 1242.82 885.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 1243 921.4 L 1243 938.8" stroke-width="1" zvalue="1163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@1" LinkObjectIDznd="332@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1243 921.4 L 1243 938.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv10" d="M 1242.83 1027.36 L 1242.81 962.63" stroke-width="1" zvalue="1164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@0" LinkObjectIDznd="332@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1242.83 1027.36 L 1242.81 962.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="446">
   <path class="kv35" d="M 1275.74 344.6 L 1275.74 325.84" stroke-width="1" zvalue="1266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="459@0" LinkObjectIDznd="457@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.74 344.6 L 1275.74 325.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="434">
   <path class="kv35" d="M 1275.92 380.61 L 1275.92 401.68" stroke-width="1" zvalue="1273"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="459@1" LinkObjectIDznd="455@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.92 380.61 L 1275.92 401.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="472">
   <path class="kv10" d="M 873.25 993.49 L 873.21 1018.07" stroke-width="1" zvalue="1283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="476@0" LinkObjectIDznd="475@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.25 993.49 L 873.21 1018.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="491">
   <path class="kv10" d="M 1442.82 870.63 L 1442.82 885.39" stroke-width="1" zvalue="1296"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@1" LinkObjectIDznd="497@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.82 870.63 L 1442.82 885.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="490">
   <path class="kv10" d="M 1443 921.4 L 1443 938.8" stroke-width="1" zvalue="1297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="497@1" LinkObjectIDznd="496@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1443 921.4 L 1443 938.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="489">
   <path class="kv10" d="M 1442.83 1027.36 L 1442.81 962.63" stroke-width="1" zvalue="1298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@0" LinkObjectIDznd="496@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.83 1027.36 L 1442.81 962.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="kv10" d="M 429.12 849.54 L 429.12 822.91" stroke-width="1" zvalue="1300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@0" LinkObjectIDznd="408@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 429.12 849.54 L 429.12 822.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv35" d="M 807.41 199.87 L 807.46 207.6" stroke-width="1" zvalue="1321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@0" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.41 199.87 L 807.46 207.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 429.12 849.54 L 429.12 936.67" stroke-width="1" zvalue="1334"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="498" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 429.12 849.54 L 429.12 936.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 775.71 187.85 L 807.4 187.85 L 807.4 199.73" stroke-width="1" zvalue="1335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313" LinkObjectIDznd="6" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.71 187.85 L 807.4 187.85 L 807.4 199.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv35" d="M 751.97 474.18 L 775.51 474.18" stroke-width="1" zvalue="1338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="398@0" LinkObjectIDznd="393" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.97 474.18 L 775.51 474.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 802.81 979.67 L 873.25 979.67 L 873.25 993.55" stroke-width="1" zvalue="1341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321" LinkObjectIDznd="472" MaxPinNum="2"/>
   </metadata>
  <path d="M 802.81 979.67 L 873.25 979.67 L 873.25 993.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 1022.81 980.22 L 1005.22 980.22 L 1005.22 994.29" stroke-width="1" zvalue="1342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254" LinkObjectIDznd="314@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.81 980.22 L 1005.22 980.22 L 1005.22 994.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv10" d="M 1242.81 979.67 L 1225.22 979.67 L 1225.22 994.29" stroke-width="1" zvalue="1343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249" LinkObjectIDznd="328@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1242.81 979.67 L 1225.22 979.67 L 1225.22 994.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 1442.81 983 L 1425.22 983 L 1425.22 994.29" stroke-width="1" zvalue="1344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489" LinkObjectIDznd="493@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.81 983 L 1425.22 983 L 1425.22 994.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 1022.79 846.8 L 1022.79 822.91" stroke-width="1" zvalue="1345"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316@0" LinkObjectIDznd="408@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.79 846.8 L 1022.79 822.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 1022.69 798.85 L 1022.69 822.91" stroke-width="1" zvalue="1392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="401@1" LinkObjectIDznd="408@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.69 798.85 L 1022.69 822.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 429.12 849.54 L 429.12 822.91" stroke-width="1" zvalue="1393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16" LinkObjectIDznd="498" MaxPinNum="2"/>
   </metadata>
  <path d="M 429.12 849.54 L 429.12 822.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 1442.79 846.8 L 1442.79 822.91" stroke-width="1" zvalue="1394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@0" LinkObjectIDznd="408@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.79 846.8 L 1442.79 822.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 1275.74 171.57 L 1275.71 302.01" stroke-width="1" zvalue="1396"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="456@0" LinkObjectIDznd="457@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.74 171.57 L 1275.71 302.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv35" d="M 1251.85 275.51 L 1275.72 275.51" stroke-width="1" zvalue="1398"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="454@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 1251.85 275.51 L 1275.72 275.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1275.73 187.5 L 1307.32 187.5 L 1307.34 199.87" stroke-width="1" zvalue="1401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78" LinkObjectIDznd="13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.73 187.5 L 1307.32 187.5 L 1307.34 199.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv35" d="M 1275.73 187.5 L 1246.84 187.5 L 1246.84 205.83" stroke-width="1" zvalue="1402"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78" LinkObjectIDznd="444@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.73 187.5 L 1246.84 187.5 L 1246.84 205.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv10" d="M 802.69 846.8 L 802.69 822.91" stroke-width="1" zvalue="1403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327@0" LinkObjectIDznd="408@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 802.69 846.8 L 802.69 822.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 1307.34 207.6 L 1307.34 187.12 L 1275.73 187.12" stroke-width="1" zvalue="1407"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.34 207.6 L 1307.34 187.12 L 1275.73 187.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 1307.44 199.85 L 1307.34 199.85" stroke-width="1" zvalue="1408"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.44 199.85 L 1307.34 199.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv35" d="M 1275.74 478.49 L 1275.74 449.75" stroke-width="1" zvalue="1428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="334@0" LinkObjectIDznd="410@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.74 478.49 L 1275.74 449.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 1275.74 533.5 L 1275.74 478.49" stroke-width="1" zvalue="1429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="466@0" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.74 533.5 L 1275.74 478.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1022.51 473.91 L 1022.51 449.75" stroke-width="1" zvalue="1451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="407@0" LinkObjectIDznd="410@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.51 473.91 L 1022.51 449.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv35" d="M 775.96 449.75 L 775.96 425.51" stroke-width="1" zvalue="1452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@3" LinkObjectIDznd="380@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.96 449.75 L 775.96 425.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 1275.95 449.75 L 1275.95 425.51" stroke-width="1" zvalue="1453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@4" LinkObjectIDznd="455@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.95 449.75 L 1275.95 425.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="398">
   <use class="kv35" height="30" transform="rotate(90,739.565,474.191) scale(1.0125,0.866) translate(-9.05543,71.3637)" width="12" x="733.4902258300781" xlink:href="#GroundDisconnector:地刀12_0" y="461.201379102803" zvalue="991"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132653573" ObjectName="35kVⅠ母电压互感器39010"/>
   <cge:TPSR_Ref TObjectID="6192451132653573"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,739.565,474.191) scale(1.0125,0.866) translate(-9.05543,71.3637)" width="12" x="733.4902258300781" y="461.201379102803"/></g>
  <g id="397">
   <use class="kv35" height="30" transform="rotate(270,739.565,523.422) scale(-1.0125,-0.866) translate(-1469.93,-1129.85)" width="12" x="733.4902258300782" xlink:href="#GroundDisconnector:地刀12_0" y="510.4321483335722" zvalue="993"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132522501" ObjectName="35kVⅠ母电压互感器39017"/>
   <cge:TPSR_Ref TObjectID="6192451132522501"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,739.565,523.422) scale(-1.0125,-0.866) translate(-1469.93,-1129.85)" width="12" x="733.4902258300782" y="510.4321483335722"/></g>
  <g id="379">
   <use class="kv35" height="30" transform="rotate(90,739.565,275.524) scale(1.0125,0.866) translate(-9.05543,40.623)" width="12" x="733.4902258325221" xlink:href="#GroundDisconnector:地刀12_0" y="262.5336739122099" zvalue="1007"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132194821" ObjectName="35kV跃龙线37167"/>
   <cge:TPSR_Ref TObjectID="6192451132194821"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,739.565,275.524) scale(1.0125,0.866) translate(-9.05543,40.623)" width="12" x="733.4902258325221" y="262.5336739122099"/></g>
  <g id="454">
   <use class="kv35" height="30" transform="rotate(90,1239.44,275.524) scale(1.0125,0.866) translate(-15.2268,40.623)" width="12" x="1233.368998530865" xlink:href="#GroundDisconnector:地刀12_0" y="262.5336737957148" zvalue="1261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133177861" ObjectName="35kV龙鸡线37267"/>
   <cge:TPSR_Ref TObjectID="6192451133177861"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1239.44,275.524) scale(1.0125,0.866) translate(-15.2268,40.623)" width="12" x="1233.368998530865" y="262.5336737957148"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="381">
   <use class="kv35" height="30" transform="rotate(0,775.711,157.269) scale(2.06349,0.963025) translate(-396.067,5.48363)" width="7" x="768.488883000411" xlink:href="#ACLineSegment:线路_0" y="142.823547363282" zvalue="1003"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249314361350" ObjectName="35kV跃龙线"/>
   <cge:TPSR_Ref TObjectID="8444249314361350_5066549590949890"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,775.711,157.269) scale(2.06349,0.963025) translate(-396.067,5.48363)" width="7" x="768.488883000411" y="142.823547363282"/></g>
  <g id="456">
   <use class="kv35" height="30" transform="rotate(0,1275.74,157.269) scale(2.06349,0.963025) translate(-653.772,5.48363)" width="7" x="1268.513462880421" xlink:href="#ACLineSegment:线路_0" y="142.8235475382551" zvalue="1257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249314164742" ObjectName="35kV龙鸡线"/>
   <cge:TPSR_Ref TObjectID="8444249314164742_5066549590949890"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1275.74,157.269) scale(2.06349,0.963025) translate(-653.772,5.48363)" width="7" x="1268.513462880421" y="142.8235475382551"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="342">
   <use class="kv35" height="26" transform="rotate(0,746.803,218.199) scale(1,1) translate(0,0)" width="12" x="740.8025860771816" xlink:href="#Accessory:避雷器1_0" y="205.199086321934" zvalue="1052"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131932677" ObjectName="35kV跃龙线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,746.803,218.199) scale(1,1) translate(0,0)" width="12" x="740.8025860771816" y="205.199086321934"/></g>
  <g id="334">
   <use class="kv35" height="18" transform="rotate(0,1275.73,486.86) scale(0.911832,1.05715) translate(122.913,-25.8034)" width="10" x="1271.166322433822" xlink:href="#Accessory:熔断器_0" y="477.3459722222221" zvalue="1059"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131867141" ObjectName="35kV1号站用变3731"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1275.73,486.86) scale(0.911832,1.05715) translate(122.913,-25.8034)" width="10" x="1271.166322433822" y="477.3459722222221"/></g>
  <g id="325">
   <use class="kv10" height="26" transform="rotate(0,785.186,1006.65) scale(1,1) translate(0,0)" width="12" x="779.1856896214222" xlink:href="#Accessory:避雷器1_0" y="993.6520996093751" zvalue="1069"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131604997" ObjectName="10kV密古线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,785.186,1006.65) scale(1,1) translate(0,0)" width="12" x="779.1856896214222" y="993.6520996093751"/></g>
  <g id="312">
   <use class="kv35" height="42" transform="rotate(0,784.576,566.773) scale(0.909091,-0.909091) translate(77.0939,-1192.13)" width="30" x="770.9393939393942" xlink:href="#Accessory:4卷PT带容断器_0" y="547.6818181818182" zvalue="1078"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131539461" ObjectName="35kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,784.576,566.773) scale(0.909091,-0.909091) translate(77.0939,-1192.13)" width="30" x="770.9393939393942" y="547.6818181818182"/></g>
  <g id="292">
   <use class="kv10" height="18" transform="rotate(0,429.101,857.91) scale(0.911832,1.05715) translate(41.0502,-45.8609)" width="10" x="424.5416891638546" xlink:href="#Accessory:熔断器_0" y="848.3957358127166" zvalue="1099"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131473925" ObjectName="10kV故障信号源0721"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,429.101,857.91) scale(0.911832,1.05715) translate(41.0502,-45.8609)" width="10" x="424.5416891638546" y="848.3957358127166"/></g>
  <g id="291">
   <use class="kv10" height="20" transform="rotate(0,427.782,950) scale(1.66667,1.66667) translate(-162.779,-373.333)" width="25" x="406.9486111111111" xlink:href="#Accessory:10kV接地信号源_0" y="933.3333333333333" zvalue="1101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131408389" ObjectName="10kV故障信号源"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,427.782,950) scale(1.66667,1.66667) translate(-162.779,-373.333)" width="25" x="406.9486111111111" y="933.3333333333333"/></g>
  <g id="263">
   <use class="kv10" height="18" transform="rotate(0,598.4,906.855) scale(0.911832,1.05715) translate(57.4203,-48.5067)" width="10" x="593.8408317391106" xlink:href="#Accessory:熔断器_0" y="897.3404343937671" zvalue="1129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131277317" ObjectName="10kVⅠ母电压互感器0905"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,598.4,906.855) scale(0.911832,1.05715) translate(57.4203,-48.5067)" width="10" x="593.8408317391106" y="897.3404343937671"/></g>
  <g id="262">
   <use class="kv10" height="42" transform="rotate(0,590.667,941.25) scale(-1.25,-1.25) translate(-1059.45,-1689)" width="30" x="571.9166666666667" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="915" zvalue="1131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131211781" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,590.667,941.25) scale(-1.25,-1.25) translate(-1059.45,-1689)" width="30" x="571.9166666666667" y="915"/></g>
  <g id="288">
   <use class="kv10" height="26" transform="rotate(0,620.295,909.584) scale(1,1) translate(0,0)" width="12" x="614.2948582305912" xlink:href="#Accessory:避雷器1_0" y="896.5843103256893" zvalue="1133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131146245" ObjectName="10kVⅠ母电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,620.295,909.584) scale(1,1) translate(0,0)" width="12" x="614.2948582305912" y="896.5843103256893"/></g>
  <g id="314">
   <use class="kv10" height="26" transform="rotate(0,1005.19,1006.65) scale(1,1) translate(0,0)" width="12" x="999.1856896214221" xlink:href="#Accessory:避雷器1_0" y="993.6520996093751" zvalue="1146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451130884101" ObjectName="10kV富厂线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1005.19,1006.65) scale(1,1) translate(0,0)" width="12" x="999.1856896214221" y="993.6520996093751"/></g>
  <g id="328">
   <use class="kv10" height="26" transform="rotate(0,1225.19,1006.65) scale(1,1) translate(-1.08285e-12,0)" width="12" x="1219.185689621422" xlink:href="#Accessory:避雷器1_0" y="993.6520996093751" zvalue="1160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451130621957" ObjectName="10kV备用线间隔避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1225.19,1006.65) scale(1,1) translate(-1.08285e-12,0)" width="12" x="1219.185689621422" y="993.6520996093751"/></g>
  <g id="444">
   <use class="kv35" height="26" transform="rotate(0,1246.8,218.199) scale(1,1) translate(0,0)" width="12" x="1240.802586077182" xlink:href="#Accessory:避雷器1_0" y="205.199086321934" zvalue="1267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132915717" ObjectName="35kV龙鸡线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1246.8,218.199) scale(1,1) translate(0,0)" width="12" x="1240.802586077182" y="205.199086321934"/></g>
  <g id="476">
   <use class="kv10" height="18" transform="rotate(0,873.233,1001.85) scale(0.911832,1.05715) translate(83.9947,-53.642)" width="10" x="868.6741650724435" xlink:href="#Accessory:熔断器_0" y="992.3404343937671" zvalue="1279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133571077" ObjectName="10kV2号站用变0738"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,873.233,1001.85) scale(0.911832,1.05715) translate(83.9947,-53.642)" width="10" x="868.6741650724435" y="992.3404343937671"/></g>
  <g id="493">
   <use class="kv10" height="26" transform="rotate(0,1425.19,1006.65) scale(1,1) translate(0,0)" width="12" x="1419.185689621422" xlink:href="#Accessory:避雷器1_0" y="993.6520996093751" zvalue="1294"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133636613" ObjectName="10kV清河线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1425.19,1006.65) scale(1,1) translate(0,0)" width="12" x="1419.185689621422" y="993.6520996093751"/></g>
  <g id="153">
   <use class="kv35" height="29" transform="rotate(180,807.461,225.367) scale(1.24705,1.24705) translate(-156.26,-41.065)" width="30" x="788.7550901224769" xlink:href="#Accessory:PT12321_0" y="207.2842407226559" zvalue="1317"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132063749" ObjectName="35kV跃龙线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(180,807.461,225.367) scale(1.24705,1.24705) translate(-156.26,-41.065)" width="30" x="788.7550901224769" y="207.2842407226559"/></g>
  <g id="3">
   <use class="kv35" height="18" transform="rotate(0,807.394,205.756) scale(0.641662,0.74392) translate(449.1,68.5229)" width="10" x="804.186169683335" xlink:href="#Accessory:熔断器_0" y="199.0606689453125" zvalue="1319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131998213" ObjectName="35kV跃龙线3719"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,807.394,205.756) scale(0.641662,0.74392) translate(449.1,68.5229)" width="10" x="804.186169683335" y="199.0606689453125"/></g>
  <g id="7">
   <use class="kv35" height="29" transform="rotate(180,1307.34,225.367) scale(1.24705,1.24705) translate(-255.292,-41.065)" width="30" x="1288.63842345581" xlink:href="#Accessory:PT12321_0" y="207.2842415894273" zvalue="1323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133046789" ObjectName="35kV龙鸡线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(180,1307.34,225.367) scale(1.24705,1.24705) translate(-255.292,-41.065)" width="30" x="1288.63842345581" y="207.2842415894273"/></g>
  <g id="10">
   <use class="kv35" height="18" transform="rotate(0,1307.43,205.735) scale(0.641662,0.74392) translate(728.343,68.5156)" width="10" x="1304.217243129663" xlink:href="#Accessory:熔断器_0" y="199.039482758342" zvalue="1326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451132981253" ObjectName="35kV龙鸡线3729"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1307.43,205.735) scale(0.641662,0.74392) translate(728.343,68.5156)" width="10" x="1304.217243129663" y="199.039482758342"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="326">
   <use class="kv10" height="30" transform="rotate(180,802.809,1043.01) scale(1.25,1.23333) translate(-159.062,-193.825)" width="12" x="795.3088625400151" xlink:href="#EnergyConsumer:负荷_0" y="1024.505828857422" zvalue="1067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451131670533" ObjectName="10kV密古线"/>
   <cge:TPSR_Ref TObjectID="6192451131670533"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,802.809,1043.01) scale(1.25,1.23333) translate(-159.062,-193.825)" width="12" x="795.3088625400151" y="1024.505828857422"/></g>
  <g id="315">
   <use class="kv10" height="30" transform="rotate(180,1022.83,1044.01) scale(1.25,1.23333) translate(-203.065,-194.015)" width="12" x="1015.32599018546" xlink:href="#EnergyConsumer:负荷_0" y="1025.505827505828" zvalue="1144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451130949637" ObjectName="10kV富厂线"/>
   <cge:TPSR_Ref TObjectID="6192451130949637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1022.83,1044.01) scale(1.25,1.23333) translate(-203.065,-194.015)" width="12" x="1015.32599018546" y="1025.505827505828"/></g>
  <g id="329">
   <use class="kv10" height="30" transform="rotate(180,1242.83,1044.01) scale(1.25,1.23333) translate(-247.065,-194.015)" width="12" x="1235.32599018546" xlink:href="#EnergyConsumer:负荷_0" y="1025.505827505828" zvalue="1158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451130687493" ObjectName="10kV龙瓦线"/>
   <cge:TPSR_Ref TObjectID="6192451130687493"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1242.83,1044.01) scale(1.25,1.23333) translate(-247.065,-194.015)" width="12" x="1235.32599018546" y="1025.505827505828"/></g>
  <g id="466">
   <use class="kv35" height="30" transform="rotate(0,1275.59,557) scale(1.66793,1.63131) translate(-501.465,-206.087)" width="28" x="1252.236374772689" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="532.5303318863517" zvalue="1276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133440005" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1275.59,557) scale(1.66793,1.63131) translate(-501.465,-206.087)" width="28" x="1252.236374772689" y="532.5303318863517"/></g>
  <g id="475">
   <use class="kv10" height="30" transform="rotate(0,873.053,1041.56) scale(1.66793,1.63131) translate(-340.268,-393.611)" width="28" x="849.7023505645323" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="1017.092831886352" zvalue="1281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133505541" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,873.053,1041.56) scale(1.66793,1.63131) translate(-340.268,-393.611)" width="28" x="849.7023505645323" y="1017.092831886352"/></g>
  <g id="494">
   <use class="kv10" height="30" transform="rotate(180,1442.83,1044.01) scale(1.25,1.23333) translate(-287.065,-194.015)" width="12" x="1435.32599018546" xlink:href="#EnergyConsumer:负荷_0" y="1025.505827505828" zvalue="1292"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451133702149" ObjectName="10kV清河线"/>
   <cge:TPSR_Ref TObjectID="6192451133702149"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1442.83,1044.01) scale(1.25,1.23333) translate(-287.065,-194.015)" width="12" x="1435.32599018546" y="1025.505827505828"/></g>
 </g>
 <g id="StateClass">
  <g id="93">
   <use height="30" stroke="rgb(255,255,255)" transform="rotate(0,103.089,234.75) scale(0.958333,0.916667) translate(3.85714,20.0909)" width="30" x="88.70999999999999" xlink:href="#State:红绿圆_0" y="221" zvalue="1433"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549590949890" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,103.089,234.75) scale(0.958333,0.916667) translate(3.85714,20.0909)" width="30" x="88.70999999999999" y="221"/></g>
  <g id="92">
   <use height="30" transform="rotate(0,158.518,233.75) scale(0.958333,0.916667) translate(6.26708,20)" width="30" x="144.14" xlink:href="#State:红绿圆_0" y="220" zvalue="1434"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549590949890" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,158.518,233.75) scale(0.958333,0.916667) translate(6.26708,20)" width="30" x="144.14" y="220"/></g>
  <g id="378">
   <use height="30" transform="rotate(0,220.518,234.75) scale(0.958333,0.916667) translate(8.96273,20.0909)" width="30" x="206.14" xlink:href="#State:红绿圆_0" y="221" zvalue="1435"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549590949890" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,220.518,234.75) scale(0.958333,0.916667) translate(8.96273,20.0909)" width="30" x="206.14" y="221"/></g>
  <g id="365">
   <use height="30" transform="rotate(0,110.018,303.5) scale(0.958333,0.916667) translate(4.15839,26.3409)" width="30" x="95.64" xlink:href="#State:红绿圆_0" y="289.75" zvalue="1437"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549590949890" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,110.018,303.5) scale(0.958333,0.916667) translate(4.15839,26.3409)" width="30" x="95.64" y="289.75"/></g>
  <g id="360">
   <use height="30" transform="rotate(0,189.518,303.5) scale(0.958333,0.916667) translate(7.61491,26.3409)" width="30" x="175.14" xlink:href="#State:红绿圆_0" y="289.75" zvalue="1438"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549590949890" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,189.518,303.5) scale(0.958333,0.916667) translate(7.61491,26.3409)" width="30" x="175.14" y="289.75"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="63" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,777.862,47.3235) scale(1,1) translate(0,0)" writing-mode="lr" x="777.86" xml:space="preserve" y="53.8" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128775450627" ObjectName="P"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="77" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1276.24,47.3235) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.24" xml:space="preserve" y="53.8" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128781152259" ObjectName="P"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="84" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,777.862,67.3235) scale(1,1) translate(0,0)" writing-mode="lr" x="777.86" xml:space="preserve" y="73.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128775516163" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="96" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1276.24,67.3235) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.24" xml:space="preserve" y="73.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128781217795" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="97" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,777.862,87.3235) scale(1,1) translate(0,0)" writing-mode="lr" x="777.86" xml:space="preserve" y="93.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128775581699" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="98" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1276.24,87.3235) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.24" xml:space="preserve" y="93.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128781283331" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.862,108.198) scale(1,1) translate(0,0)" writing-mode="lr" x="777.86" xml:space="preserve" y="114.68" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128775843843" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1276.24,108.198) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.24" xml:space="preserve" y="114.68" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128781545475" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1444.27,351.614) scale(1,1) translate(0,5.71399e-13)" writing-mode="lr" x="1444.27" xml:space="preserve" y="357.88" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128780627971" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="105" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,388.944,725.401) scale(1,1) translate(0,-6.36736e-13)" writing-mode="lr" x="388.94" xml:space="preserve" y="731.67" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778072067" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1444.27,369.614) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.27" xml:space="preserve" y="375.88" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128780693507" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="107" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,388.944,743.401) scale(1,1) translate(0,0)" writing-mode="lr" x="388.94" xml:space="preserve" y="749.67" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778137603" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="108" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1444.27,387.614) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.27" xml:space="preserve" y="393.88" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128780759043" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="109" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,388.944,761.401) scale(1,1) translate(0,0)" writing-mode="lr" x="388.94" xml:space="preserve" y="767.67" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778203139" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="110" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1444.27,333.523) scale(1,1) translate(-9.36428e-13,0)" writing-mode="lr" x="1444.27" xml:space="preserve" y="339.79" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128780890115" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="111" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,388.944,707.623) scale(1,1) translate(0,0)" writing-mode="lr" x="388.94" xml:space="preserve" y="713.89" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778334211" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="112" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1444.27,406.068) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.27" xml:space="preserve" y="412.34" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128781086723" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="113" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,388.944,780.855) scale(1,1) translate(0,0)" writing-mode="lr" x="388.94" xml:space="preserve" y="787.13" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778530819" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="114" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,784.627,1094.82) scale(1,1) translate(0,0)" writing-mode="lr" x="784.63" xml:space="preserve" y="1101.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128774008835" ObjectName="P"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="115" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1022.83,1094.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.83" xml:space="preserve" y="1101.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128772567043" ObjectName="P"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="116" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1242.83,1094.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.83" xml:space="preserve" y="1101.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128771125251" ObjectName="P"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="117" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1438.83,1094.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1438.83" xml:space="preserve" y="1101.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128783904771" ObjectName="P"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="118" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,784.627,1114.82) scale(1,1) translate(0,0)" writing-mode="lr" x="784.63" xml:space="preserve" y="1121.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128774074371" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="119" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1022.83,1114.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.83" xml:space="preserve" y="1121.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128772632579" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="120" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1242.83,1114.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.83" xml:space="preserve" y="1121.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128771190787" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="121" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1438.83,1114.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1438.83" xml:space="preserve" y="1121.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128783970307" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="133" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,784.627,1134.82) scale(1,1) translate(0,0)" writing-mode="lr" x="784.63" xml:space="preserve" y="1141.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128774139907" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="134" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1022.83,1134.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.83" xml:space="preserve" y="1141.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128772698115" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="135" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1242.83,1134.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.83" xml:space="preserve" y="1141.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128771256323" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="136" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1438.83,1134.82) scale(1,1) translate(9.31139e-13,0)" writing-mode="lr" x="1438.83" xml:space="preserve" y="1141.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128784035843" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.627,1155.73) scale(1,1) translate(0,0)" writing-mode="lr" x="784.63" xml:space="preserve" y="1162.21" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128774533123" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1022.83,1155.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.83" xml:space="preserve" y="1162.21" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128773091331" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.83,1155.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.83" xml:space="preserve" y="1162.21" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128771649539" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1438.83,1155.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1438.83" xml:space="preserve" y="1162.21" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128784429059" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="143" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,924.727,521.724) scale(1,1) translate(0,0)" writing-mode="lr" x="924.73" xml:space="preserve" y="528.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778596355" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="144" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,924.727,541.724) scale(1,1) translate(0,0)" writing-mode="lr" x="924.73" xml:space="preserve" y="548.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778661891" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="145" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,924.727,725.109) scale(1,1) translate(0,5.5614e-13)" writing-mode="lr" x="924.73" xml:space="preserve" y="731.59" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778727427" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="146" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,924.727,745.109) scale(1,1) translate(0,0)" writing-mode="lr" x="924.73" xml:space="preserve" y="751.59" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778792963" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="148" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,924.727,561.724) scale(1,1) translate(0,0)" writing-mode="lr" x="924.73" xml:space="preserve" y="568.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128778858499" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="155" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.977,627.167) scale(1,1) translate(0,-1.3715e-13)" writing-mode="lr" x="937.98" xml:space="preserve" y="632.15" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128779055107" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="156" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.977,655.917) scale(1,1) translate(0,0)" writing-mode="lr" x="937.98" xml:space="preserve" y="660.9" zvalue="1">档位:ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128779120643" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="157">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="157" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,924.727,765.109) scale(1,1) translate(0,0)" writing-mode="lr" x="924.73" xml:space="preserve" y="771.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128779186179" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1273.74,614.03) scale(1,1) translate(9.59972e-13,0)" writing-mode="lr" x="1273.74" xml:space="preserve" y="620.3" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128783118339" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1273.74,633.03) scale(1,1) translate(9.59972e-13,0)" writing-mode="lr" x="1273.74" xml:space="preserve" y="639.3" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128783183875" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1273.74,651.03) scale(1,1) translate(9.59972e-13,0)" writing-mode="lr" x="1273.74" xml:space="preserve" y="657.3" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128783249411" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1271.59,667.97) scale(1,1) translate(0,1.46432e-13)" writing-mode="lr" x="1271.59" xml:space="preserve" y="674.24" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128782856195" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="21" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,887.741,1155.73) scale(1,1) translate(0,0)" writing-mode="lr" x="887.74" xml:space="preserve" y="1162" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128783446019" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,887.741,1095.82) scale(1,1) translate(0,0)" writing-mode="lr" x="887.74" xml:space="preserve" y="1102.09" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128783708163" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="28" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,887.741,1115.82) scale(1,1) translate(0,0)" writing-mode="lr" x="887.74" xml:space="preserve" y="1122.09" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128783773699" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="29" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,887.741,1135.82) scale(1,1) translate(0,0)" writing-mode="lr" x="887.74" xml:space="preserve" y="1142.09" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128783839235" ObjectName="Uc"/>
   </metadata>
  </g>
 </g>
</svg>