<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549596585986" height="1080" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1080" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:5卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
   <ellipse cx="8.710000000000001" cy="0.47" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.10887049225937" x2="11.10887049225937" y1="1.654102266954478" y2="1.654102266954478"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.60887049225937" x2="11.60887049225937" y1="1.154102266954478" y2="1.154102266954478"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.289699242498646" x2="7.787306517404822" y1="0.309450744955857" y2="-2.343680756240978"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.289699242498656" x2="10.74248159896798" y1="0.3094507449558748" y2="1.438770861071294"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.339309611123124" x2="8.289699242498626" y1="1.833262130037291" y2="0.3094507449558748"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:放电间隙4_0" viewBox="0,0,15,15">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.517857142857143" x2="7.517857142857143" y1="0.4377686487201995" y2="11.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.517857142857143" x2="7.517857142857143" y1="13.5" y2="11.35783935656103"/>
   <path d="M 7.53329 8.11336 L 4.01543 4.05453 L 10.9321 4.05453 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="7" y2="23"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="3.5" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.66666666666667" x2="4.666666666666667" y1="9.833333333333334" y2="20.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.416666666666667" x2="10.41666666666667" y1="9.5" y2="20.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:母线PT20201009_0" viewBox="0,0,28,27">
   <use terminal-index="0" type="0" x="8.562458524793255" xlink:href="#terminal" y="26.62373692455963"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.36,12.13) scale(1,-1) translate(0,-1025.19)" width="11" x="17.86" y="9.630000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.36245852479333" x2="27.36245852479333" y1="15.3796779607034" y2="17.3796779607034"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.36245852479332" x2="27.36245852479332" y1="8.379677960703395" y2="15.3796779607034"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.3624585247933" x2="19.3624585247933" y1="6.379677960703384" y2="8.379677960703384"/>
   <path d="M 16.25 20.4701 L 23.25 20.4701 L 23.25 17.4701" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="17.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="14" y1="20.5" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="10" y1="20.5" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.39772110866599" x2="23.39772110866599" y1="6.803414885262955" y2="3.303414885262956"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.46438777533266" x2="21.46438777533266" y1="3.061919543778654" y2="3.061919543778654"/>
   <ellipse cx="12.04" cy="20.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.5108382776216" x2="21.23402243293775" y1="3.300000000000015" y2="3.300000000000015"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762157" x2="22.1506890996044" y1="2.050000000000029" y2="2.050000000000029"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.26083827762159" x2="22.81735576627107" y1="0.5500000000000007" y2="0.5500000000000007"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.51457106407813" x2="3.320904828303071" y1="22.05654513693459" y2="19.43563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.58713830216066" x2="6.81883560169719" y1="19.54040359122622" y2="19.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.894107309452135" x2="7.087773545227193" y1="22.15299989806297" y2="19.53208583485582"/>
   <ellipse cx="5.04" cy="20.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.192203825592705" x2="1.192203825592705" y1="21.07076893362116" y2="21.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.591033638435105" x2="8.591033638435105" y1="22.91666666666666" y2="26.66276827235689"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="10.41666666666667" y2="13.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="9.75" y1="13.41666666666667" y2="15.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="13.75" y1="13.41666666666667" y2="15.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75" x2="6.75" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75" x2="2.75" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75" x2="4.75" y1="10.5" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.21438777533266" x2="21.21438777533266" y1="10.14525287711199" y2="10.14525287711199"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.71438777533266" x2="10.71438777533266" y1="13.65816200815096" y2="13.65816200815096"/>
   <ellipse cx="4.79" cy="13.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.79" cy="13.55" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.25" x2="10.25" y1="6.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.25" x2="8.25" y1="3.75" y2="6.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.25" x2="6.25" y1="6.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.71438777533266" x2="17.71438777533266" y1="16.81191954377866" y2="16.81191954377866"/>
   <ellipse cx="8.289999999999999" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:放电间隙2_0" viewBox="0,0,10,15">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1.333333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="5.25" y2="10.25"/>
   <path d="M 5.01786 10.3578 L 1.5 14.4167 L 8.58025 14.4167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 5.03329 5.36336 L 1.51543 1.30453 L 8.4321 1.30453 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:fdj_0" viewBox="0,0,70,70">
   <use terminal-index="0" type="0" x="35" xlink:href="#terminal" y="0"/>
   <ellipse cx="35" cy="35.1" fill-opacity="0" rx="35" ry="35" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 7.6 34.8 C 35 7.2 34.8 62.2 62.6 35" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:3卷互感器带消谐_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="13.75" xlink:href="#terminal" y="39.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.27912519146" x2="34.27912519146" y1="12.25707025789288" y2="10.25707025789288"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.72912519145994" x2="13.72912519145994" y1="34.80707025789293" y2="29.30707025789293"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.27912519145998" x2="26.27912519145998" y1="21.2570702578929" y2="19.2570702578929"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.52912519145998" x2="30.52912519145998" y1="23.8496628504855" y2="21.00707025789291"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.27912519145999" x2="34.27912519145998" y1="19.25707025789289" y2="12.25707025789289"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.28,15.51) scale(1,1) translate(0,0)" width="11" x="24.78" y="13.01"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75" x2="30.52912519145997" y1="23.85892210974476" y2="23.85892210974476"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.31438777533266" x2="30.31438777533266" y1="10.00341488526295" y2="3.322116677387807"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.38105444199933" x2="28.38105444199933" y1="2.932794352318616" y2="2.932794352318616"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.21715236070647" x2="18.21715236070647" y1="39.19751367349974" y2="39.19751367349974"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.42750494428827" x2="28.15068909960442" y1="3.170874808539971" y2="3.170874808539971"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.67750494428824" x2="29.06735576627106" y1="1.920874808539985" y2="1.920874808539985"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.17750494428825" x2="29.73402243293774" y1="0.4208748085399598" y2="0.4208748085399598"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.53106705533087" x2="18.53106705533087" y1="39.60879757166701" y2="39.60879757166701"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75770030510177" x2="13.75770030510177" y1="34.57488824795973" y2="39.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.958333333333337" x2="8.375000000000004" y1="19.70833333333333" y2="17.125"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.95833333333333" x2="7.95833333333333" y1="19.70833333333333" y2="22.70833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.958333333333329" x2="5.958333333333329" y1="19.70833333333333" y2="19.70833333333333"/>
   <ellipse cx="6.38" cy="19.74" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.79" cy="15.21" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.72" cy="24.21" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.95833333333334" x2="16.375" y1="24.29166666666666" y2="21.70833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.95833333333333" x2="15.95833333333333" y1="24.29166666666666" y2="27.29166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.95833333333333" x2="13.95833333333334" y1="24.29166666666666" y2="24.29166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.875" x2="16.29166666666667" y1="15.45833333333333" y2="12.875"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.875" x2="15.875" y1="15.45833333333333" y2="18.45833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.87499999999999" x2="13.87499999999999" y1="15.45833333333333" y2="15.45833333333333"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="9.003653261335021"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.14609851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.14687801656252" y2="8.861111111111104"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="16.14769090077732" y2="8.861111111111104"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="8.743941472336534" y2="1.450000000000003"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="8.736625514403292" y2="1.450000000000003"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_1" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="9.003653261335021"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.14609851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.14687801656252" y2="8.861111111111104"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="16.14769090077732" y2="8.861111111111104"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="9" y2="16.14037494284408"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_2" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="9.003653261335021"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.14609851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.992290301275212" x2="2.825623634608543" y1="7.813824112178023" y2="15.98049077884469"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.063455774018188" x2="9.563455774018188" y1="8.339836407051772" y2="16.00650307371844"/>
  </symbol>
  <symbol id="Accessory:五卷电压互感器带熔断器_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="0" x="32.43086845281398" xlink:href="#terminal" y="58.91935093140179"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.9000001487732" x2="32.11888237749056" y1="32.37785967630686" y2="32.37785967630686"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.99999965667723" x2="10.99999965667723" y1="26.33288376804624" y2="21.53288358494076"/>
   <rect fill-opacity="0" height="18.6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,32.3,16.93) scale(1,-1) translate(0,-644.43)" width="8.4" x="28.1" y="7.63"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.20000046539308" x2="32.20000046539308" y1="58.80000109863285" y2="17.23288342090877"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.99999965667723" x2="8.599999565124495" y1="21.53288358494076" y2="22.73288363071713"/>
   <rect fill-opacity="0" height="15.6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,32,46.2) scale(1,1) translate(0,0)" width="7.2" x="28.4" y="38.4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.99999965667723" x2="8.599999565124495" y1="26.33288376804624" y2="25.13288372226987"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.20000046539308" x2="33.40000051116945" y1="16.23288338276181" y2="22.23288361164365"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.20000046539308" x2="31.00000041961671" y1="16.23288338276181" y2="22.23288361164365"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.2153207920433" x2="32.2153207920433" y1="7.632883054697825" y2="3.855023324985204"/>
   <ellipse cx="18.77" cy="32.39" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.47" cy="16.29" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.65106148770614" x2="29.51888227830843" y1="3.773533079259202" y2="3.773533079259202"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.65106144955917" x2="30.5188823164554" y1="2.273533022038748" y2="2.273533022038748"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.95106142285629" x2="31.21888234315829" y1="0.7735329648182869" y2="0.7735329648182869"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.73888186708405" x2="15.85888175722077" y1="32.64795564905528" y2="34.13300368692789"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.73888186708406" x2="21.61888197694734" y1="32.64795564905528" y2="34.13300368692789"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.73888186708405" x2="18.73888186708405" y1="29.67785957331003" y2="32.64795564905525"/>
   <ellipse cx="18.77" cy="23.99" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.37" cy="32.39" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.33888154664947" x2="13.21888165651275" y1="32.64795564905528" y2="34.13300368692789"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.33888154664947" x2="10.33888154664947" y1="29.67785957331003" y2="32.64795564905525"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.73888186708406" x2="21.61888197694734" y1="24.24795532862069" y2="25.7330033664933"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.73888186708405" x2="18.73888186708405" y1="21.27785925287544" y2="24.24795532862067"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.73888186708405" x2="15.85888175722077" y1="24.24795532862069" y2="25.7330033664933"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.33888154664947" x2="7.458881436786188" y1="32.64795564905528" y2="34.13300368692789"/>
   <ellipse cx="10.37" cy="23.99" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.57888170839264" x2="17.45888181825592" y1="16.40795502954841" y2="17.89300306742102"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.57888170839264" x2="14.57888170839264" y1="13.43785895380316" y2="16.40795502954839"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.57888170839263" x2="11.69888159852936" y1="16.40795502954841" y2="17.89300306742102"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带负荷1_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(85,255,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV海门河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1080" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="35kV海门河电站_软压板.svg"><rect fill-opacity="0" height="40" width="97.14" x="185" y="368.33" zvalue="435"/></g>
  <g href="35kV海门河电站_直流监控.svg"><rect fill-opacity="0" height="40" width="97.14" x="185" y="430.19" zvalue="436"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="156" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,233.571,388.333) scale(1,1) translate(0,0)" width="97.14" x="185" y="368.33" zvalue="435"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.571,388.333) scale(1,1) translate(0,0)" writing-mode="lr" x="233.57" xml:space="preserve" y="392.33" zvalue="435">软压板</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="121" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,233.571,450.19) scale(1,1) translate(0,0)" width="97.14" x="185" y="430.19" zvalue="436"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.571,450.19) scale(1,1) translate(0,0)" writing-mode="lr" x="233.57" xml:space="preserve" y="454.19" zvalue="436">直流监控</text>
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="4" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,268.333,70) scale(1,1) translate(0,0)" writing-mode="lr" x="268.33" xml:space="preserve" y="87" zvalue="441">      海门河电站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569.528,647.913) scale(1,1) translate(0,0)" writing-mode="lr" x="569.53" xml:space="preserve" y="654.41" zvalue="42">6kV#Ⅰ母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.277,147.373) scale(1,1) translate(0,0)" writing-mode="lr" x="934.28" xml:space="preserve" y="153.37" zvalue="208">3519</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.886,202.881) scale(1,1) translate(0,0)" writing-mode="lr" x="962.89" xml:space="preserve" y="208.88" zvalue="210">35197</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,846.431,359.695) scale(1,1) translate(0,0)" writing-mode="lr" x="846.4299999999999" xml:space="preserve" y="365.69" zvalue="215">35kV1号电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1154.61,195.931) scale(1,1) translate(0,0)" writing-mode="lr" x="1154.61" xml:space="preserve" y="201.93" zvalue="217">35167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.85,249.585) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.85" xml:space="preserve" y="255.58" zvalue="219">3516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.8,271.949) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.8" xml:space="preserve" y="277.95" zvalue="221">35160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1053.45,332.673) scale(1,1) translate(0,0)" writing-mode="lr" x="1053.45" xml:space="preserve" y="338.67" zvalue="224">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1328.72,192.559) scale(1,1) translate(0,0)" writing-mode="lr" x="1328.72" xml:space="preserve" y="198.56" zvalue="227">3518</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1188.25,431.026) scale(1,1) translate(0,0)" writing-mode="lr" x="1188.25" xml:space="preserve" y="437.03" zvalue="246">3.15MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1160.59,522.633) scale(1,1) translate(0,0)" writing-mode="lr" x="1160.59" xml:space="preserve" y="528.63" zvalue="250">60117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1162.06,611.781) scale(1,1) translate(0,0)" writing-mode="lr" x="1162.06" xml:space="preserve" y="617.78" zvalue="252">6011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.504,740.178) scale(1,1) translate(0,0)" writing-mode="lr" x="695.5" xml:space="preserve" y="746.1799999999999" zvalue="273">651</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,720.079,768.532) scale(1,1) translate(0,0)" writing-mode="lr" x="720.08" xml:space="preserve" y="774.53" zvalue="286">65167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,488.142,885.154) scale(1,1) translate(0,0)" writing-mode="lr" x="488.14" xml:space="preserve" y="891.15" zvalue="288">6513</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.039,1005.73) scale(1,1) translate(0,0)" writing-mode="lr" x="643.04" xml:space="preserve" y="1011.73" zvalue="291">1.2MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,640.54,990.256) scale(1,1) translate(0,0)" writing-mode="lr" x="640.54" xml:space="preserve" y="996.76" zvalue="291">1号机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,785.839,882.774) scale(1,1) translate(0,0)" writing-mode="lr" x="785.84" xml:space="preserve" y="888.77" zvalue="295">6514</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,947.088,738.276) scale(1,1) translate(0,0)" writing-mode="lr" x="947.09" xml:space="preserve" y="744.28" zvalue="315">661</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1067.47,759.022) scale(1,1) translate(0,0)" writing-mode="lr" x="1067.47" xml:space="preserve" y="765.02" zvalue="321">66167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1164.68,712.975) scale(1,1) translate(0,0)" writing-mode="lr" x="1164.68" xml:space="preserve" y="718.98" zvalue="335">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1191.03,858.483) scale(1,1) translate(0,0)" writing-mode="lr" x="1191.03" xml:space="preserve" y="864.48" zvalue="336">6kV#Ⅰ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.39,738.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.39" xml:space="preserve" y="744.39" zvalue="342">652</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1532.12,768.532) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.12" xml:space="preserve" y="774.53" zvalue="347">65267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1291.84,885.154) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.84" xml:space="preserve" y="891.15" zvalue="350">6523</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1453.57,1005.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1453.57" xml:space="preserve" y="1011.73" zvalue="353">1.2MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1451.07,990.256) scale(1,1) translate(0,0)" writing-mode="lr" x="1451.07" xml:space="preserve" y="996.76" zvalue="354">2号机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1587.67,881.499) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.67" xml:space="preserve" y="887.5" zvalue="356">6524</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176.88,408.582) scale(1,1) translate(0,0)" writing-mode="lr" x="1176.88" xml:space="preserve" y="414.58" zvalue="393">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1378.7,412.947) scale(1,1) translate(0,0)" writing-mode="lr" x="1378.7" xml:space="preserve" y="418.95" zvalue="406">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.89,979.471) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.89" xml:space="preserve" y="985.97" zvalue="408">6kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.625,174.333) scale(1,1) translate(0,0)" writing-mode="lr" x="227.63" xml:space="preserve" y="180.33" zvalue="426">事故</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.375,281.833) scale(1,1) translate(0,0)" writing-mode="lr" x="238.38" xml:space="preserve" y="287.83" zvalue="427">负荷总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.625,241.083) scale(1,1) translate(0,0)" writing-mode="lr" x="238.63" xml:space="preserve" y="247.08" zvalue="428">是否失压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,320.125,239.083) scale(1,1) translate(0,0)" writing-mode="lr" x="320.13" xml:space="preserve" y="245.08" zvalue="430">失压排除</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.625,317.583) scale(1,1) translate(0,0)" writing-mode="lr" x="234.63" xml:space="preserve" y="323.58" zvalue="432">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.375,344.583) scale(1,1) translate(0,0)" writing-mode="lr" x="238.38" xml:space="preserve" y="350.58" zvalue="433">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,287.625,174.333) scale(1,1) translate(0,0)" writing-mode="lr" x="287.63" xml:space="preserve" y="180.33" zvalue="434">异常</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,346.577,174.19) scale(1,1) translate(0,0)" writing-mode="lr" x="346.58" xml:space="preserve" y="180.19" zvalue="442">告知</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1095.5,44.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1095.5" xml:space="preserve" y="51" zvalue="485">35kV海象线</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="25">
   <path class="kv6" d="M 525.34 675.97 L 1601.88 675.97" stroke-width="6" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674293121027" ObjectName="6kV#Ⅰ母线"/>
   <cge:TPSR_Ref TObjectID="9288674293121027"/></metadata>
  <path d="M 525.34 675.97 L 1601.88 675.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="61">
   <use class="kv35" height="30" transform="rotate(90,937.034,161.884) scale(1.48123,0.727291) translate(-300.821,56.6102)" width="15" x="925.9243313520652" xlink:href="#Disconnector:刀闸_0" y="150.9743225664218" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451909844995" ObjectName="35kV海象线3519"/>
   <cge:TPSR_Ref TObjectID="6192451909844995"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,937.034,161.884) scale(1.48123,0.727291) translate(-300.821,56.6102)" width="15" x="925.9243313520652" y="150.9743225664218"/></g>
  <g id="146">
   <use class="kv35" height="30" transform="rotate(0,1092.19,250.329) scale(1.48123,0.727291) translate(-351.23,89.7741)" width="15" x="1081.080554910187" xlink:href="#Disconnector:刀闸_0" y="239.419419920448" zvalue="218"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910238211" ObjectName="35kV海象线3516"/>
   <cge:TPSR_Ref TObjectID="6192451910238211"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1092.19,250.329) scale(1.48123,0.727291) translate(-351.23,89.7741)" width="15" x="1081.080554910187" y="239.419419920448"/></g>
  <g id="165">
   <use class="kv35" height="30" transform="rotate(0,1363.7,193.451) scale(-1.48123,0.727291) translate(-2280.74,68.447)" width="15" x="1352.587671210787" xlink:href="#Disconnector:令克_0" y="182.5419714348603" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910434819" ObjectName="10kV2号站用变3518"/>
   <cge:TPSR_Ref TObjectID="6192451910434819"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1363.7,193.451) scale(-1.48123,0.727291) translate(-2280.74,68.447)" width="15" x="1352.587671210787" y="182.5419714348603"/></g>
  <g id="7">
   <use class="kv6" height="30" transform="rotate(0,1092.55,611.145) scale(3.19139,2.13679) translate(-733.772,-308.082)" width="15" x="1068.614891953511" xlink:href="#Disconnector:联体手车刀闸3_0" y="579.0927864846514" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910828035" ObjectName="#1主变侧6011"/>
   <cge:TPSR_Ref TObjectID="6192451910828035"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1092.55,611.145) scale(3.19139,2.13679) translate(-733.772,-308.082)" width="15" x="1068.614891953511" y="579.0927864846514"/></g>
  <g id="85">
   <use class="kv6" height="26" transform="rotate(0,526.862,892.37) scale(1.8373,1.23016) translate(-235.079,-163.971)" width="12" x="515.838163126019" xlink:href="#Disconnector:20210316_0" y="876.3774197437917" zvalue="287"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911221251" ObjectName="1号机刀闸6513"/>
   <cge:TPSR_Ref TObjectID="6192451911221251"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,526.862,892.37) scale(1.8373,1.23016) translate(-235.079,-163.971)" width="12" x="515.838163126019" y="876.3774197437917"/></g>
  <g id="135">
   <use class="kv6" height="26" transform="rotate(0,738.493,892.37) scale(1.8373,1.23016) translate(-331.525,-163.971)" width="12" x="727.4695774783125" xlink:href="#Disconnector:20210316_0" y="876.3774197437917" zvalue="294"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911483395" ObjectName="1号机刀闸6514"/>
   <cge:TPSR_Ref TObjectID="6192451911483395"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,738.493,892.37) scale(1.8373,1.23016) translate(-331.525,-163.971)" width="12" x="727.4695774783125" y="876.3774197437917"/></g>
  <g id="134">
   <use class="kv6" height="18" transform="rotate(0,1218.61,710.654) scale(2.37472,-1.06) translate(-697.203,-1380.54)" width="12" x="1204.361644967566" xlink:href="#Disconnector:单手车刀闸_0" y="701.1143138761435" zvalue="334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912204291" ObjectName="6kV#Ⅰ母线刀闸6901"/>
   <cge:TPSR_Ref TObjectID="6192451912204291"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1218.61,710.654) scale(2.37472,-1.06) translate(-697.203,-1380.54)" width="12" x="1204.361644967566" y="701.1143138761435"/></g>
  <g id="107">
   <use class="kv6" height="26" transform="rotate(0,1337.39,892.37) scale(1.8373,1.23016) translate(-604.458,-163.971)" width="12" x="1326.369816204413" xlink:href="#Disconnector:20210316_0" y="876.3774197437917" zvalue="348"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912794115" ObjectName="2号机刀闸6523"/>
   <cge:TPSR_Ref TObjectID="6192451912794115"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1337.39,892.37) scale(1.8373,1.23016) translate(-604.458,-163.971)" width="12" x="1326.369816204413" y="876.3774197437917"/></g>
  <g id="102">
   <use class="kv6" height="26" transform="rotate(0,1549.03,892.37) scale(1.8373,1.23016) translate(-700.903,-163.971)" width="12" x="1538.001230556709" xlink:href="#Disconnector:20210316_0" y="876.3774197437917" zvalue="355"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912531971" ObjectName="2号机刀闸6524"/>
   <cge:TPSR_Ref TObjectID="6192451912531971"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1549.03,892.37) scale(1.8373,1.23016) translate(-700.903,-163.971)" width="12" x="1538.001230556709" y="876.3774197437917"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="63">
   <use class="kv35" height="30" transform="rotate(90,961.106,216.342) scale(1.01796,-0.681573) translate(-16.8461,-538.535)" width="12" x="954.9985559536599" xlink:href="#GroundDisconnector:地刀12_0" y="206.1188289114143" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451909976067" ObjectName="35kV海象线35197"/>
   <cge:TPSR_Ref TObjectID="6192451909976067"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,961.106,216.342) scale(1.01796,-0.681573) translate(-16.8461,-538.535)" width="12" x="954.9985559536599" y="206.1188289114143"/></g>
  <g id="144">
   <use class="kv35" height="30" transform="rotate(90,1151.74,211.128) scale(1.01796,-0.681573) translate(-20.2089,-525.67)" width="12" x="1145.63713179974" xlink:href="#GroundDisconnector:地刀12_0" y="200.9042959375635" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910172675" ObjectName="35kV海象线35167"/>
   <cge:TPSR_Ref TObjectID="6192451910172675"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1151.74,211.128) scale(1.01796,-0.681573) translate(-20.2089,-525.67)" width="12" x="1145.63713179974" y="200.9042959375635"/></g>
  <g id="148">
   <use class="kv35" height="30" transform="rotate(90,1164.19,289.576) scale(1.01796,-0.681573) translate(-20.4284,-719.217)" width="12" x="1158.079503667172" xlink:href="#GroundDisconnector:地刀12_0" y="279.3525291583134" zvalue="220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910369283" ObjectName="35kV海象线35160"/>
   <cge:TPSR_Ref TObjectID="6192451910369283"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1164.19,289.576) scale(1.01796,-0.681573) translate(-20.4284,-719.217)" width="12" x="1158.079503667172" y="279.3525291583134"/></g>
  <g id="32">
   <use class="kv6" height="30" transform="rotate(90,1153.61,539.144) scale(1.01796,-0.681573) translate(-20.2418,-1334.95)" width="12" x="1147.501555534534" xlink:href="#GroundDisconnector:地刀12_0" y="528.9203639427874" zvalue="249"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910762499" ObjectName="#1主变侧60117"/>
   <cge:TPSR_Ref TObjectID="6192451910762499"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1153.61,539.144) scale(1.01796,-0.681573) translate(-20.2418,-1334.95)" width="12" x="1147.501555534534" y="528.9203639427874"/></g>
  <g id="82">
   <use class="kv6" height="30" transform="rotate(270,714.124,782.812) scale(-1.01796,0.681573) translate(-1415.54,360.949)" width="12" x="708.0163854048599" xlink:href="#GroundDisconnector:地刀12_0" y="772.5880856235017" zvalue="285"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911090179" ObjectName="1号机65167"/>
   <cge:TPSR_Ref TObjectID="6192451911090179"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,714.124,782.812) scale(-1.01796,0.681573) translate(-1415.54,360.949)" width="12" x="708.0163854048599" y="772.5880856235017"/></g>
  <g id="48">
   <use class="kv6" height="30" transform="rotate(270,1076.69,782.812) scale(-1.01796,0.681573) translate(-2134.27,360.949)" width="12" x="1070.579691841918" xlink:href="#GroundDisconnector:地刀12_0" y="772.5880847079356" zvalue="320"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911942147" ObjectName="6kV1号站用变66167"/>
   <cge:TPSR_Ref TObjectID="6192451911942147"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1076.69,782.812) scale(-1.01796,0.681573) translate(-2134.27,360.949)" width="12" x="1070.579691841918" y="772.5880847079356"/></g>
  <g id="110">
   <use class="kv6" height="30" transform="rotate(270,1527.46,782.812) scale(-1.01796,0.681573) translate(-3027.86,360.949)" width="12" x="1521.348038483256" xlink:href="#GroundDisconnector:地刀12_0" y="772.5880858562193" zvalue="345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912990723" ObjectName="2号机65267"/>
   <cge:TPSR_Ref TObjectID="6192451912990723"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1527.46,782.812) scale(-1.01796,0.681573) translate(-3027.86,360.949)" width="12" x="1521.348038483256" y="772.5880858562193"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="142">
   <use class="kv35" height="42" transform="rotate(0,844.868,289.192) scale(-3.19972,-2.14237) translate(-1075.92,-400.188)" width="30" x="796.8726080827594" xlink:href="#Accessory:5卷PT带容断器_0" y="244.2018117018497" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910041603" ObjectName="35kV1号电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,844.868,289.192) scale(-3.19972,-2.14237) translate(-1075.92,-400.188)" width="30" x="796.8726080827594" y="244.2018117018497"/></g>
  <g id="23">
   <use class="kv35" height="15" transform="rotate(0,1363.73,254.998) scale(1.68861,-1.13061) translate(-550.958,-479.559)" width="15" x="1351.062399755997" xlink:href="#Accessory:放电间隙4_0" y="246.518546776127" zvalue="240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910565891" ObjectName="35kV2号站用变放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1363.73,254.998) scale(1.68861,-1.13061) translate(-550.958,-479.559)" width="15" x="1351.062399755997" y="246.518546776127"/></g>
  <g id="84">
   <use class="kv6" height="26" transform="rotate(0,598.591,803.829) scale(1.8373,1.23016) translate(-267.768,-147.405)" width="12" x="587.5676103774515" xlink:href="#Accessory:避雷器1_0" y="787.8367462158202" zvalue="286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911155715" ObjectName="1号机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,598.591,803.829) scale(1.8373,1.23016) translate(-267.768,-147.405)" width="12" x="587.5676103774515" y="787.8367462158202"/></g>
  <g id="88">
   <use class="kv6" height="27" transform="rotate(0,510.284,951.689) scale(-2.77732,-1.85955) translate(-669.134,-1451.87)" width="28" x="471.4011207423343" xlink:href="#Accessory:母线PT20201009_0" y="926.5852807652889" zvalue="288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911286787" ObjectName="1号机PT1"/>
   </metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,510.284,951.689) scale(-2.77732,-1.85955) translate(-669.134,-1451.87)" width="28" x="471.4011207423343" y="926.5852807652889"/></g>
  <g id="4">
   <use class="kv6" height="40" transform="rotate(180,730.588,947.839) scale(1.81513,1.28423) translate(-313.824,-204.094)" width="35" x="698.8235294117648" xlink:href="#Accessory:3卷互感器带消谐_0" y="922.1542377925979" zvalue="296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911548931" ObjectName="1号机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,730.588,947.839) scale(1.81513,1.28423) translate(-313.824,-204.094)" width="35" x="698.8235294117648" y="922.1542377925979"/></g>
  <g id="10">
   <use class="kv6" height="20" transform="rotate(0,845.185,952.994) scale(1.90444,1.27512) translate(-394.606,-202.867)" width="15" x="830.9016768178087" xlink:href="#Accessory:PT6_0" y="940.2429633018485" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911614467" ObjectName="1号机PT3"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,845.185,952.994) scale(1.90444,1.27512) translate(-394.606,-202.867)" width="15" x="830.9016768178087" y="940.2429633018485"/></g>
  <g id="13">
   <use class="kv6" height="15" transform="rotate(0,845.7,892.498) scale(1.11093,0.74382) translate(-83.8885,305.465)" width="10" x="840.1449229744633" xlink:href="#Accessory:放电间隙2_0" y="886.9192814386333" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911680003" ObjectName="1号机放电间隙2"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,845.7,892.498) scale(1.11093,0.74382) translate(-83.8885,305.465)" width="10" x="840.1449229744633" y="886.9192814386333"/></g>
  <g id="49">
   <use class="kv6" height="26" transform="rotate(0,963.855,803.829) scale(1.8373,1.23016) translate(-434.228,-147.405)" width="12" x="952.8315685360055" xlink:href="#Accessory:避雷器1_0" y="787.8367463954974" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912007683" ObjectName="6kV1号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,963.855,803.829) scale(1.8373,1.23016) translate(-434.228,-147.405)" width="12" x="952.8315685360055" y="787.8367463954974"/></g>
  <g id="51">
   <use class="kv6" height="15" transform="rotate(0,997.922,834.582) scale(2.44404,1.6364) translate(-582.393,-319.799)" width="10" x="985.7016949684498" xlink:href="#Accessory:放电间隙2_0" y="822.3087778747855" zvalue="323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912073219" ObjectName="6kV1号站用变放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,997.922,834.582) scale(2.44404,1.6364) translate(-582.393,-319.799)" width="10" x="985.7016949684498" y="822.3087778747855"/></g>
  <g id="138">
   <use class="kv6" height="60" transform="rotate(0,1191.54,803.315) scale(2.17912,-1.45903) translate(-621.163,-1340.12)" width="40" x="1147.962415821129" xlink:href="#Accessory:五卷电压互感器带熔断器_0" y="759.5437067121463" zvalue="335"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912269827" ObjectName="6kV#Ⅰ母电压互感器"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1191.54,803.315) scale(2.17912,-1.45903) translate(-621.163,-1340.12)" width="40" x="1147.962415821129" y="759.5437067121463"/></g>
  <g id="109">
   <use class="kv6" height="26" transform="rotate(0,1403.22,802.013) scale(1.8373,1.23016) translate(-634.458,-147.065)" width="12" x="1392.199263455848" xlink:href="#Accessory:避雷器1_0" y="786.0208970912978" zvalue="346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912859651" ObjectName="2号机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1403.22,802.013) scale(1.8373,1.23016) translate(-634.458,-147.065)" width="12" x="1392.199263455848" y="786.0208970912978"/></g>
  <g id="106">
   <use class="kv6" height="27" transform="rotate(0,1320.82,951.689) scale(-2.77732,-1.85955) translate(-1771.51,-1451.87)" width="28" x="1281.932773820729" xlink:href="#Accessory:母线PT20201009_0" y="926.5852807652889" zvalue="349"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912728579" ObjectName="2号机PT1"/>
   </metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1320.82,951.689) scale(-2.77732,-1.85955) translate(-1771.51,-1451.87)" width="28" x="1281.932773820729" y="926.5852807652889"/></g>
  <g id="105">
   <use class="kv6" height="15" transform="rotate(0,1445.13,887.981) scale(2.44404,1.6364) translate(-846.62,-340.566)" width="10" x="1432.907625319295" xlink:href="#Accessory:放电间隙2_0" y="875.7079815968386" zvalue="351"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912663043" ObjectName="2号机放电间隙1"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1445.13,887.981) scale(2.44404,1.6364) translate(-846.62,-340.566)" width="10" x="1432.907625319295" y="875.7079815968386"/></g>
  <g id="101">
   <use class="kv6" height="40" transform="rotate(180,1543.16,947.657) scale(1.90444,1.27512) translate(-717.038,-198.964)" width="35" x="1509.83131975991" xlink:href="#Accessory:3卷互感器带消谐_0" y="922.154237792598" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912466435" ObjectName="2号机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1543.16,947.657) scale(1.90444,1.27512) translate(-717.038,-198.964)" width="35" x="1509.83131975991" y="922.154237792598"/></g>
  <g id="100">
   <use class="kv6" height="20" transform="rotate(0,1655.72,952.994) scale(1.90444,1.27512) translate(-779.537,-202.867)" width="15" x="1641.433329896203" xlink:href="#Accessory:PT6_0" y="940.2429633018485" zvalue="358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912400899" ObjectName="2号机PT3"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1655.72,952.994) scale(1.90444,1.27512) translate(-779.537,-202.867)" width="15" x="1641.433329896203" y="940.2429633018485"/></g>
  <g id="99">
   <use class="kv6" height="15" transform="rotate(0,1657.1,891.628) scale(1.11093,0.74382) translate(-164.907,305.165)" width="10" x="1651.546141270251" xlink:href="#Accessory:放电间隙2_0" y="886.0497162212419" zvalue="359"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912335363" ObjectName="2号机放电间隙2"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1657.1,891.628) scale(1.11093,0.74382) translate(-164.907,305.165)" width="10" x="1651.546141270251" y="886.0497162212419"/></g>
  <g id="178">
   <use class="kv6" height="15" transform="rotate(0,635.437,885.778) scale(2.44404,1.6364) translate(-368.222,-339.71)" width="10" x="623.2167832167835" xlink:href="#Accessory:放电间隙2_0" y="873.5052447552448" zvalue="458"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911352323" ObjectName="1号机放电间隙1"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,635.437,885.778) scale(2.44404,1.6364) translate(-368.222,-339.71)" width="10" x="623.2167832167835" y="873.5052447552448"/></g>
  <g id="30">
   <use class="kv6" height="15" transform="rotate(0,1092.67,497.532) scale(2.44404,1.6364) translate(-638.374,-188.719)" width="10" x="1080.450390189521" xlink:href="#Accessory:放电间隙2_0" y="485.2586399108138" zvalue="480"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910631427" ObjectName="#1主变侧放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1092.67,497.532) scale(2.44404,1.6364) translate(-638.374,-188.719)" width="10" x="1080.450390189521" y="485.2586399108138"/></g>
 </g>
 <g id="BreakerClass">
  <g id="473">
   <use class="kv35" height="20" transform="rotate(0,1093.45,331.703) scale(2.43693,1.68118) translate(-637.563,-127.588)" width="10" x="1081.261015354998" xlink:href="#Breaker:开关_0" y="314.8908848477455" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924748050437" ObjectName="#1主变35kV侧351"/>
   <cge:TPSR_Ref TObjectID="6473924748050437"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1093.45,331.703) scale(2.43693,1.68118) translate(-637.563,-127.588)" width="10" x="1081.261015354998" y="314.8908848477455"/></g>
  <g id="372">
   <use class="kv6" height="20" transform="rotate(0,635.27,737.427) scale(2.43693,1.68118) translate(-367.401,-291.979)" width="10" x="623.0856959243443" xlink:href="#Breaker:开关_0" y="720.6154647241309" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924748115973" ObjectName="1号机651"/>
   <cge:TPSR_Ref TObjectID="6473924748115973"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,635.27,737.427) scale(2.43693,1.68118) translate(-367.401,-291.979)" width="10" x="623.0856959243443" y="720.6154647241309"/></g>
  <g id="46">
   <use class="kv6" height="20" transform="rotate(0,997.877,737.427) scale(2.43693,1.68118) translate(-581.21,-291.979)" width="10" x="985.6919617752054" xlink:href="#Breaker:开关_0" y="720.6154647241309" zvalue="314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924748181509" ObjectName="6kV1号站用变661"/>
   <cge:TPSR_Ref TObjectID="6473924748181509"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,997.877,737.427) scale(2.43693,1.68118) translate(-581.21,-291.979)" width="10" x="985.6919617752054" y="720.6154647241309"/></g>
  <g id="113">
   <use class="kv6" height="20" transform="rotate(0,1445.8,737.427) scale(2.43693,1.68118) translate(-845.328,-291.979)" width="10" x="1433.617349002741" xlink:href="#Breaker:开关_0" y="720.6154647241309" zvalue="341"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924748247045" ObjectName="2号机652"/>
   <cge:TPSR_Ref TObjectID="6473924748247045"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1445.8,737.427) scale(2.43693,1.68118) translate(-845.328,-291.979)" width="10" x="1433.617349002741" y="720.6154647241309"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="8">
   <path class="kv35" d="M 1090.73 128.68 L 1090.73 239.78" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="146@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1090.73 128.68 L 1090.73 239.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv35" d="M 1092.28 261.05 L 1092.28 315.62" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@1" LinkObjectIDznd="473@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.28 261.05 L 1092.28 315.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv35" d="M 1363.82 202.36 L 1363.82 278.46" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@1" LinkObjectIDznd="334@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.82 202.36 L 1363.82 278.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 1363.73 262.91 L 1363.82 262.91" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="22" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.73 262.91 L 1363.82 262.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv6" d="M 1092.55 634.65 L 1092.55 675.97" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@1" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.55 634.65 L 1092.55 675.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv6" d="M 635.3 698.39 L 635.3 675.97" stroke-width="1" zvalue="299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="371@0" LinkObjectIDznd="25@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.3 698.39 L 635.3 675.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv6" d="M 635.35 707.31 L 635.35 721.34" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="371@1" LinkObjectIDznd="372@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.35 707.31 L 635.35 721.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv6" d="M 527.02 908.32 L 527.02 927.28" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 527.02 908.32 L 527.02 927.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv6" d="M 738.65 908.32 L 738.65 922.9" stroke-width="1" zvalue="312"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="4@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.65 908.32 L 738.65 922.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv6" d="M 997.9 698.39 L 997.9 675.97" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="25@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.9 698.39 L 997.9 675.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv6" d="M 997.95 707.31 L 997.95 721.34" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.95 707.31 L 997.95 721.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv6" d="M 998.04 753.48 L 998.04 765.58" stroke-width="1" zvalue="328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 998.04 753.48 L 998.04 765.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv6" d="M 997.59 774.5 L 997.59 857.31" stroke-width="1" zvalue="329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.59 774.5 L 997.59 857.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv6" d="M 997.92 824.49 L 997.59 824.49" stroke-width="1" zvalue="330"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.92 824.49 L 997.59 824.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv6" d="M 1218.63 761.12 L 1218.61 710.65" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="134@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.63 761.12 L 1218.61 710.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv6" d="M 1218.62 703.08 L 1218.62 675.97" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@1" LinkObjectIDznd="25@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.62 703.08 L 1218.62 675.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv6" d="M 1445.83 698.39 L 1445.83 675.97" stroke-width="1" zvalue="360"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="25@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445.83 698.39 L 1445.83 675.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv6" d="M 1445.88 707.31 L 1445.88 721.34" stroke-width="1" zvalue="361"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@1" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445.88 707.31 L 1445.88 721.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv6" d="M 1445.96 753.48 L 1445.96 765.58" stroke-width="1" zvalue="362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@1" LinkObjectIDznd="111@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445.96 753.48 L 1445.96 765.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv6" d="M 1445.52 774.5 L 1445.52 920.41" stroke-width="1" zvalue="363"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445.52 774.5 L 1445.52 920.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv6" d="M 1445.13 877.89 L 1445.13 876.51" stroke-width="1" zvalue="364"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445.13 877.89 L 1445.13 876.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv6" d="M 1337.4 876.47 L 1337.4 827.28 L 1445.52 827.28" stroke-width="1" zvalue="367"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@1" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1337.4 876.47 L 1337.4 827.28 L 1445.52 827.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv6" d="M 1337.55 908.32 L 1337.55 927.28" stroke-width="1" zvalue="368"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1337.55 908.32 L 1337.55 927.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv6" d="M 1549.03 876.47 L 1549.03 826.6 L 1445.52 826.6" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1549.03 876.47 L 1549.03 826.6 L 1445.52 826.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv6" d="M 1549.18 908.32 L 1549.18 922.9" stroke-width="1" zvalue="372"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1549.18 908.32 L 1549.18 922.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv35" d="M 1093.61 347.76 L 1093.61 377.81" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="473@1" LinkObjectIDznd="28@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1093.61 347.76 L 1093.61 377.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv6" d="M 1092.55 447.7 L 1092.55 587.64" stroke-width="1" zvalue="401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@1" LinkObjectIDznd="7@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.55 447.7 L 1092.55 587.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv6" d="M 1143.85 539.13 L 1092.55 539.13" stroke-width="1" zvalue="403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.85 539.13 L 1092.55 539.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv35" d="M 1141.98 211.12 L 1090.82 211.12" stroke-width="1" zvalue="444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1141.98 211.12 L 1090.82 211.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv35" d="M 1154.42 289.56 L 1092.28 289.56" stroke-width="1" zvalue="445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1154.42 289.56 L 1092.28 289.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 875.33 245.54 L 875.33 161.97 L 926.31 161.97" stroke-width="1" zvalue="446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 875.33 245.54 L 875.33 161.97 L 926.31 161.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 951.34 216.33 L 875.33 216.33" stroke-width="1" zvalue="447"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.34 216.33 L 875.33 216.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv6" d="M 1655.83 940.78 L 1655.83 890 L 1655.83 811.25 L 1445.52 811.25" stroke-width="1" zvalue="453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.83 940.78 L 1655.83 890 L 1655.83 811.25 L 1445.52 811.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv6" d="M 526.87 876.47 L 526.87 844.38 L 635.67 844.38" stroke-width="1" zvalue="455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 526.87 876.47 L 526.87 844.38 L 635.67 844.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv6" d="M 635.67 845 L 738.5 845 L 738.5 876.47" stroke-width="1" zvalue="456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39" LinkObjectIDznd="135@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.67 845 L 738.5 845 L 738.5 876.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv6" d="M 845.3 940.78 L 845.3 826.52 L 635.67 826.52" stroke-width="1" zvalue="460"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.3 940.78 L 845.3 826.52 L 635.67 826.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv6" d="M 845.7 887.91 L 845.3 887.91" stroke-width="1" zvalue="461"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.7 887.91 L 845.3 887.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv6" d="M 1657.1 887.04 L 1655.83 887.04" stroke-width="1" zvalue="462"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="173" MaxPinNum="2"/>
   </metadata>
  <path d="M 1657.1 887.04 L 1655.83 887.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv6" d="M 704.36 782.8 L 635.67 782.8" stroke-width="1" zvalue="463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.36 782.8 L 635.67 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv6" d="M 636.05 782.8 L 598.65 782.8 L 598.65 788.62" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 636.05 782.8 L 598.65 782.8 L 598.65 788.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv6" d="M 963.92 788.62 L 963.92 782.31 L 997.59 782.31" stroke-width="1" zvalue="469"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 963.92 788.62 L 963.92 782.31 L 997.59 782.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv6" d="M 997.59 782.8 L 1066.92 782.8" stroke-width="1" zvalue="470"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.59 782.8 L 1066.92 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv6" d="M 1517.69 782.8 L 1445.52 782.8" stroke-width="1" zvalue="471"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.69 782.8 L 1445.52 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv6" d="M 1445.52 782 L 1403.28 782 L 1403.28 786.8" stroke-width="1" zvalue="472"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95" LinkObjectIDznd="109@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445.52 782 L 1403.28 782 L 1403.28 786.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv6" d="M 635.43 753.48 L 635.43 766.08" stroke-width="1" zvalue="473"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="372@1" LinkObjectIDznd="14@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.43 753.48 L 635.43 766.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv6" d="M 635.67 775 L 635.67 920.41" stroke-width="1" zvalue="474"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@0" LinkObjectIDznd="92@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.67 775 L 635.67 920.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv6" d="M 635.44 875.69 L 635.67 875.69" stroke-width="1" zvalue="475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.44 875.69 L 635.67 875.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 947.58 162.01 L 1090.82 162.01" stroke-width="1" zvalue="476"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 947.58 162.01 L 1090.82 162.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 1090.82 161 L 1363.57 161 L 1363.57 183.81" stroke-width="1" zvalue="477"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8" LinkObjectIDznd="165@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1090.82 161 L 1363.57 161 L 1363.57 183.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv6" d="M 1092.67 487.44 L 1092.55 487.44" stroke-width="1" zvalue="482"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.67 487.44 L 1092.55 487.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="371">
   <use class="kv6" height="22" transform="rotate(0,635.279,707.053) scale(1.2953,0.86727) translate(-141.583,106.75)" width="22" x="621.0308499471619" xlink:href="#DollyBreaker:手车_0" y="697.513220204131" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451910959107" ObjectName="1号机手车1"/>
   <cge:TPSR_Ref TObjectID="6192451910959107"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,635.279,707.053) scale(1.2953,0.86727) translate(-141.583,106.75)" width="22" x="621.0308499471619" y="697.513220204131"/></g>
  <g id="45">
   <use class="kv6" height="22" transform="rotate(0,997.885,707.053) scale(1.2953,0.86727) translate(-224.25,106.75)" width="22" x="983.6371157980218" xlink:href="#DollyBreaker:手车_0" y="697.513220204131" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911811075" ObjectName="6kV1号站用变手车1"/>
   <cge:TPSR_Ref TObjectID="6192451911811075"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,997.885,707.053) scale(1.2953,0.86727) translate(-224.25,106.75)" width="22" x="983.6371157980218" y="697.513220204131"/></g>
  <g id="44">
   <use class="kv6" height="22" transform="rotate(180,997.609,765.83) scale(1.2953,0.86727) translate(-224.187,115.745)" width="22" x="983.3604568476569" xlink:href="#DollyBreaker:手车_0" y="756.2897887406891" zvalue="316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911745539" ObjectName="6kV1号站用变手车2"/>
   <cge:TPSR_Ref TObjectID="6192451911745539"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,997.609,765.83) scale(1.2953,0.86727) translate(-224.187,115.745)" width="22" x="983.3604568476569" y="756.2897887406891"/></g>
  <g id="112">
   <use class="kv6" height="22" transform="rotate(0,1445.81,707.053) scale(1.2953,0.86727) translate(-326.368,106.75)" width="22" x="1431.562503025556" xlink:href="#DollyBreaker:手车_0" y="697.513220204131" zvalue="343"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451913121795" ObjectName="2号机手车1"/>
   <cge:TPSR_Ref TObjectID="6192451913121795"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1445.81,707.053) scale(1.2953,0.86727) translate(-326.368,106.75)" width="22" x="1431.562503025556" y="697.513220204131"/></g>
  <g id="111">
   <use class="kv6" height="22" transform="rotate(180,1445.53,765.83) scale(1.2953,0.86727) translate(-326.305,115.745)" width="22" x="1431.285844075192" xlink:href="#DollyBreaker:手车_0" y="756.2897950652019" zvalue="344"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451913056259" ObjectName="2号机手车2"/>
   <cge:TPSR_Ref TObjectID="6192451913056259"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1445.53,765.83) scale(1.2953,0.86727) translate(-326.305,115.745)" width="22" x="1431.285844075192" y="756.2897950652019"/></g>
  <g id="14">
   <use class="kv6" height="22" transform="rotate(180,635.69,766.335) scale(1.2953,0.86727) translate(-141.677,115.822)" width="22" x="621.4419191919193" xlink:href="#DollyBreaker:手车_0" y="756.7948377975309" zvalue="466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451917053955" ObjectName="1号机手车2"/>
   <cge:TPSR_Ref TObjectID="6192451917053955"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,635.69,766.335) scale(1.2953,0.86727) translate(-141.677,115.822)" width="22" x="621.4419191919193" y="756.7948377975309"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="92">
   <use class="kv6" height="70" transform="rotate(0,634.985,946.445) scale(1.11093,0.74382) translate(-59.5209,317)" width="70" x="596.1025002090905" xlink:href="#Generator:fdj_0" y="920.4115734100529" zvalue="290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451911417859" ObjectName="1号机"/>
   <cge:TPSR_Ref TObjectID="6192451911417859"/></metadata>
  <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,634.985,946.445) scale(1.11093,0.74382) translate(-59.5209,317)" width="70" x="596.1025002090905" y="920.4115734100529"/></g>
  <g id="104">
   <use class="kv6" height="70" transform="rotate(0,1445.52,946.445) scale(1.11093,0.74382) translate(-140.453,317)" width="70" x="1406.634153287485" xlink:href="#Generator:fdj_0" y="920.4115734100529" zvalue="352"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912597507" ObjectName="2号机"/>
   <cge:TPSR_Ref TObjectID="6192451912597507"/></metadata>
  <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1445.52,946.445) scale(1.11093,0.74382) translate(-140.453,317)" width="70" x="1406.634153287485" y="920.4115734100529"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="122">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="122" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,647.226,1017.9) scale(1,1) translate(6.70464e-14,-3.81875e-12)" writing-mode="lr" x="646.77" xml:space="preserve" y="1024.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131508170756" ObjectName="P"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="123" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,647.226,1029.41) scale(1,1) translate(6.70464e-14,-3.86217e-12)" writing-mode="lr" x="646.77" xml:space="preserve" y="1036.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131508236292" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="124" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,648.708,1040.41) scale(1,1) translate(6.72108e-14,-3.90373e-12)" writing-mode="lr" x="648.26" xml:space="preserve" y="1047.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131508301828" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="125" prefix="Cos:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,648.782,1051.82) scale(1,1) translate(6.72191e-14,-3.94678e-12)" writing-mode="lr" x="648.33" xml:space="preserve" y="1058.51" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131508498436" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="126" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1452.57,1017.34) scale(1,1) translate(-9.45852e-13,-3.59689e-12)" writing-mode="lr" x="1452.23" xml:space="preserve" y="1023.89" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131509350404" ObjectName="P"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="127" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1451.79,1028.48) scale(1,1) translate(-9.45329e-13,-3.63644e-12)" writing-mode="lr" x="1451.45" xml:space="preserve" y="1035.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131509415940" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="128" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1451.01,1041.71) scale(1,1) translate(-9.44807e-13,-3.68344e-12)" writing-mode="lr" x="1450.67" xml:space="preserve" y="1048.25" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131509481476" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="129" prefix="Cos:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1454.83,1053.42) scale(1,1) translate(3.15786e-13,2.32817e-12)" writing-mode="lr" x="1454.49" xml:space="preserve" y="1059.97" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131509678084" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="79" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1183.39,316.476) scale(1,1) translate(2.54478e-13,1.37372e-13)" writing-mode="lr" x="1183" xml:space="preserve" y="323.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131511971844" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="80" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1183.39,334.2) scale(1,1) translate(2.54478e-13,1.45243e-13)" writing-mode="lr" x="1183" xml:space="preserve" y="340.99" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131512037380" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="114" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1183.39,351.924) scale(1,1) translate(-2.54478e-13,1.53115e-13)" writing-mode="lr" x="1183" xml:space="preserve" y="358.72" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131512233989" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="115" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,973.598,403.44) scale(1,1) translate(0,1.75992e-13)" writing-mode="lr" x="973.08" xml:space="preserve" y="410.23" zvalue="1">油温:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131512430596" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="116" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978.169,426.009) scale(1,1) translate(0,1.86015e-13)" writing-mode="lr" x="977.75" xml:space="preserve" y="432.8" zvalue="1">档位:ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131512496132" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="130" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.87,367.863) scale(1,1) translate(-2.56803e-13,6.43943e-13)" writing-mode="lr" x="1193.48" xml:space="preserve" y="372.46" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131513151492" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="131" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,433.531,615.206) scale(1,1) translate(-8.36829e-14,2.69441e-13)" writing-mode="lr" x="432.94" xml:space="preserve" y="620.65" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131506597892" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="132" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,433.531,636.628) scale(1,1) translate(-8.36829e-14,2.78954e-13)" writing-mode="lr" x="432.94" xml:space="preserve" y="642.0700000000001" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131506663428" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,433.531,658.05) scale(1,1) translate(-8.36829e-14,2.88467e-13)" writing-mode="lr" x="432.94" xml:space="preserve" y="663.49" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131506728964" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="153" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,433.531,680.875) scale(1,1) translate(-8.36829e-14,2.98604e-13)" writing-mode="lr" x="432.94" xml:space="preserve" y="686.3200000000001" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131506860036" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="154" prefix="F:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,427.889,720.276) scale(1,1) translate(-8.24301e-14,3.16101e-13)" writing-mode="lr" x="427.3" xml:space="preserve" y="725.72" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131506991108" ObjectName="F"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="155" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,433.531,702.297) scale(1,1) translate(-8.36829e-14,3.08117e-13)" writing-mode="lr" x="432.94" xml:space="preserve" y="707.74" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131507056644" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="28">
   <g id="280">
    <use class="kv35" height="50" transform="rotate(0,1092.55,412.684) scale(2.1216,1.42052) translate(-560.762,-111.655)" width="30" x="1060.73" xlink:href="#PowerTransformer2:Y-D_0" y="377.17" zvalue="392"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874482515971" ObjectName="35"/>
    </metadata>
   </g>
   <g id="281">
    <use class="kv6" height="50" transform="rotate(0,1092.55,412.684) scale(2.1216,1.42052) translate(-560.762,-111.655)" width="30" x="1060.73" xlink:href="#PowerTransformer2:Y-D_1" y="377.17" zvalue="392"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874482647043" ObjectName="6"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399469367299" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399469367299"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1092.55,412.684) scale(2.1216,1.42052) translate(-560.762,-111.655)" width="30" x="1060.73" y="377.17"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="334">
   <use class="kv35" height="30" transform="rotate(0,1363.31,331.594) scale(5.50929,3.68874) translate(-1052.73,-201.37)" width="28" x="1286.184206305604" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="276.2633120083226" zvalue="405"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451916988419" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1363.31,331.594) scale(5.50929,3.68874) translate(-1052.73,-201.37)" width="28" x="1286.184206305604" y="276.2633120083226"/></g>
  <g id="71">
   <use class="kv6" height="30" transform="rotate(0,999.017,910.444) scale(5.50929,3.68874) translate(-754.554,-623.296)" width="28" x="921.8869280310236" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="855.1132710759414" zvalue="407"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451912138755" ObjectName="6kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,999.017,910.444) scale(5.50929,3.68874) translate(-754.554,-623.296)" width="28" x="921.8869280310236" y="855.1132710759414"/></g>
 </g>
 <g id="StateClass">
  <g id="120">
   <use height="30" stroke="rgb(255,255,255)" transform="rotate(0,228.375,145.083) scale(0.958333,0.916667) translate(9.30435,11.9394)" width="30" x="214" xlink:href="#State:红绿圆_0" y="131.33" zvalue="437"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549596585986" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,228.375,145.083) scale(0.958333,0.916667) translate(9.30435,11.9394)" width="30" x="214" y="131.33"/></g>
  <g id="119">
   <use height="30" transform="rotate(0,286.804,146.083) scale(0.958333,0.916667) translate(11.8447,12.0303)" width="30" x="272.43" xlink:href="#State:红绿圆_0" y="132.33" zvalue="438"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549596585986" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,286.804,146.083) scale(0.958333,0.916667) translate(11.8447,12.0303)" width="30" x="272.43" y="132.33"/></g>
  <g id="118">
   <use height="30" transform="rotate(0,346.804,146.083) scale(0.958333,0.916667) translate(14.4534,12.0303)" width="30" x="332.43" xlink:href="#State:红绿圆_0" y="132.33" zvalue="439"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549596585986" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,346.804,146.083) scale(0.958333,0.916667) translate(14.4534,12.0303)" width="30" x="332.43" y="132.33"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="20">
   <use class="kv35" height="30" transform="rotate(0,1090.73,97) scale(7.92381,2.13333) translate(-928.847,-34.5312)" width="7" x="1063" xlink:href="#ACLineSegment:线路_0" y="65" zvalue="484"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249342214147" ObjectName="35kV海象线"/>
   <cge:TPSR_Ref TObjectID="8444249342214147_5066549596585986"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1090.73,97) scale(7.92381,2.13333) translate(-928.847,-34.5312)" width="7" x="1063" y="65"/></g>
 </g>
</svg>