<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1节点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
  <symbol id="Accessory:10kV接地信号源_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="2"/>
   <rect fill-opacity="0" height="16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,10) scale(1,1) translate(0,0)" width="20" x="2.5" y="2"/>
  </symbol>
  <symbol id="Fuse:svg站内熔断器_0" viewBox="0,0,19,27">
   <use terminal-index="0" type="0" x="9.25" xlink:href="#terminal" y="1.815916669999341"/>
   <use terminal-index="1" type="0" x="9.083333333333334" xlink:href="#terminal" y="25.27907289158723"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.600825825825828" x2="9.600825825825828" y1="19.75939457202506" y2="26.0116562282533"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.600825825825828" x2="9.600825825825828" y1="6.212827650197193" y2="-0.03943400603105296"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.699512012012015" x2="2.495420420420419" y1="19.9678032938993" y2="4.858170958014387"/>
   <path d="M 1.2125 8.81794 L 6.83761 21.0098 L 12.1667 18.1963 L 6.54155 6.10862 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Fuse:svg站内熔断器_1" viewBox="0,0,19,27">
   <use terminal-index="0" type="0" x="9.25" xlink:href="#terminal" y="1.815916669999341"/>
   <use terminal-index="1" type="0" x="9.083333333333334" xlink:href="#terminal" y="25.27907289158723"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.128896502135934" x2="9.128896502135934" y1="2.333333333333327" y2="6.289333333333327"/>
   <path d="M 6.66162 6.47333 L 6.66162 20.8253 L 11.5962 20.8253 L 11.6667 6.56533 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.269883733498753" x2="9.269883733498753" y1="6.933333333333337" y2="20.73333333333332"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.128896502135934" x2="9.128896502135934" y1="19.81333333333331" y2="19.81333333333331"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.128896502135934" x2="9.128896502135934" y1="20.82533333333335" y2="25.33333333333335"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.128896502135934" x2="9.128896502135934" y1="19.81333333333331" y2="19.81333333333331"/>
  </symbol>
  <symbol id=":杆塔_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="7.499999999999997" xlink:href="#terminal" y="15.1"/>
   <use terminal-index="1" type="0" x="22.6" xlink:href="#terminal" y="15"/>
   <use terminal-index="2" type="0" x="15" xlink:href="#terminal" y="21.95"/>
   <rect fill-opacity="0" height="13.8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.05) scale(1,1) translate(0,0)" width="15" x="7.5" y="8.15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.800000000000001" x2="22.2" y1="8.4" y2="21.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.2" x2="22.2" y1="21.6" y2="8.4"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV钢淬球厂变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="32" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,240.054,126.946) scale(1,1) translate(0,0)" writing-mode="lr" x="240.05" xml:space="preserve" y="138.45" zvalue="5009">       钢淬球厂变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,165.161,294.321) scale(1,1) translate(0,0)" writing-mode="lr" x="165.16" xml:space="preserve" y="298.82" zvalue="5011">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,292.935,248.071) scale(1,1) translate(0,0)" writing-mode="lr" x="292.93" xml:space="preserve" y="252.57" zvalue="5012">钢淬球厂-01-2023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,268.935,293.333) scale(1,1) translate(0,0)" writing-mode="lr" x="268.93" xml:space="preserve" y="297.83" zvalue="5013">2023-03-06</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,168.018,248.821) scale(1,1) translate(0,0)" writing-mode="lr" x="168.02" xml:space="preserve" y="253.32" zvalue="5015">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774.123,1021.56) scale(1,1) translate(0,0)" writing-mode="lr" x="774.12" xml:space="preserve" y="1026.06" zvalue="5062">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,954.028,715.222) scale(1,1) translate(0,0)" writing-mode="lr" x="954.03" xml:space="preserve" y="719.72" zvalue="5066">35kV高压计量装置</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.186,635.444) scale(1,1) translate(0,0)" writing-mode="lr" x="898.1900000000001" xml:space="preserve" y="639.9400000000001" zvalue="5069">367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827.829,273.22) scale(1,1) translate(0,0)" writing-mode="lr" x="827.83" xml:space="preserve" y="277.72" zvalue="5073">3738</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.343,340.009) scale(1,1) translate(0,0)" writing-mode="lr" x="800.34" xml:space="preserve" y="344.51" zvalue="5074">37387</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1041.11,188.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1041.11" xml:space="preserve" y="194.11" zvalue="5077">35kV力角变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,856.889,152.889) scale(1,1) translate(0,0)" writing-mode="lr" x="856.89" xml:space="preserve" y="157.39" zvalue="5077">35kV牛力钢线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,625.556,190.333) scale(1,1) translate(0,0)" writing-mode="lr" x="625.5599999999999" xml:space="preserve" y="196.33" zvalue="5080">110kV牛井变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,829.495,865.22) scale(1,1) translate(0,0)" writing-mode="lr" x="829.5" xml:space="preserve" y="869.72" zvalue="5097">3674</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,829.488,579.22) scale(1,1) translate(0,0)" writing-mode="lr" x="829.49" xml:space="preserve" y="583.72" zvalue="5101">3676</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,815.333,205.333) scale(1,1) translate(0,0)" writing-mode="lr" x="815.33" xml:space="preserve" y="209.33" zvalue="5103">#33塔</text>
 </g>
 <g id="AccessoryClass">
  <g id="52">
   <use class="kv35" height="22" transform="rotate(0,854.801,939.472) scale(1,1) translate(0,0)" width="12" x="848.8007405598959" xlink:href="#Accessory:传输线_0" y="928.4722205268014" zvalue="4748"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,854.801,939.472) scale(1,1) translate(0,0)" width="12" x="848.8007405598959" y="928.4722205268014"/></g>
  <g id="105">
   <use class="kv35" height="20" transform="rotate(0,856.25,711) scale(2.72222,2.72222) translate(-520.181,-432.594)" width="25" x="822.2222222222224" xlink:href="#Accessory:10kV接地信号源_0" y="683.7777777777778" zvalue="5065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV高压计量装置"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,856.25,711) scale(2.72222,2.72222) translate(-520.181,-432.594)" width="25" x="822.2222222222224" y="683.7777777777778"/></g>
  <g id="195">
   <use class="kv35" height="26" transform="rotate(270,896.667,546.455) scale(-1.0017,1.45299) translate(-1791.8,-164.476)" width="12" x="890.6564539366726" xlink:href="#Accessory:避雷器_0" y="527.5657682855496" zvalue="5070"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,896.667,546.455) scale(-1.0017,1.45299) translate(-1791.8,-164.476)" width="12" x="890.6564539366726" y="527.5657682855496"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="194">
   <use class="kv35" height="30" transform="rotate(180,854.658,1096.6) scale(1.25,1.23333) translate(-169.432,-203.965)" width="12" x="847.1576392368149" xlink:href="#EnergyConsumer:负荷_0" y="1078.099747474748" zvalue="4889"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变负荷"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,854.658,1096.6) scale(1.25,1.23333) translate(-169.432,-203.965)" width="12" x="847.1576392368149" y="1078.099747474748"/></g>
  <g id="17">
   <use class="kv35" height="38" transform="rotate(0,862.789,1013.68) scale(2.09402,2.09402) translate(-436.541,-508.807)" width="26" x="835.567183396359" xlink:href="#EnergyConsumer:站用变1节点_0" y="973.8888888888891" zvalue="5061"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,862.789,1013.68) scale(2.09402,2.09402) translate(-436.541,-508.807)" width="26" x="835.567183396359" y="973.8888888888891"/></g>
  <g id="272">
   <use class="kv35" height="30" transform="rotate(90,954.167,189.222) scale(1.25,1.23333) translate(-189.333,-32.2988)" width="12" x="946.6666666666666" xlink:href="#EnergyConsumer:负荷_0" y="170.7222289774151" zvalue="5076"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV牛力钢线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,954.167,189.222) scale(1.25,1.23333) translate(-189.333,-32.2988)" width="12" x="946.6666666666666" y="170.7222289774151"/></g>
 </g>
 <g id="FuseClass">
  <g id="81">
   <use class="kv35" height="27" transform="rotate(0,855.376,794.289) scale(1.72515,1.72515) translate(-352.659,-324.081)" width="19" x="838.9875612641054" xlink:href="#Fuse:svg站内熔断器_0" y="771" zvalue="5063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变熔断器"/>
   </metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,855.376,794.289) scale(1.72515,1.72515) translate(-352.659,-324.081)" width="19" x="838.9875612641054" y="771"/></g>
 </g>
 <g id="BreakerClass">
  <g id="169">
   <use class="kv35" height="20" transform="rotate(0,854.823,640.835) scale(1.828,1.8835) translate(-383.056,-291.764)" width="10" x="845.6832955932618" xlink:href="#Breaker:开关_0" y="622" zvalue="5068"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变367"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,854.823,640.835) scale(1.828,1.8835) translate(-383.056,-291.764)" width="10" x="845.6832955932618" y="622"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="196">
   <use class="kv35" height="30" transform="rotate(0,854.83,274.22) scale(-1.11133,0.814667) translate(-1623.19,59.6039)" width="15" x="846.4953376666095" xlink:href="#Disconnector:刀闸_0" y="261.9999999999999" zvalue="5072"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变3738"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,854.83,274.22) scale(-1.11133,0.814667) translate(-1623.19,59.6039)" width="15" x="846.4953376666095" y="261.9999999999999"/></g>
  <g id="2">
   <use class="kv35" height="30" transform="rotate(0,854.83,866.22) scale(-1.11133,0.814667) translate(-1623.19,194.282)" width="15" x="846.4953376666095" xlink:href="#Disconnector:刀闸_0" y="853.9999999999999" zvalue="5096"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变3674"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,854.83,866.22) scale(-1.11133,0.814667) translate(-1623.19,194.282)" width="15" x="846.4953376666095" y="853.9999999999999"/></g>
  <g id="6">
   <use class="kv35" height="30" transform="rotate(0,854.86,580.22) scale(-1.11133,0.814667) translate(-1623.24,129.218)" width="15" x="846.5249131658099" xlink:href="#Disconnector:刀闸_0" y="568" zvalue="5100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变3676"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,854.86,580.22) scale(-1.11133,0.814667) translate(-1623.24,129.218)" width="15" x="846.5249131658099" y="568"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="202">
   <use class="kv35" height="30" transform="rotate(90,800.343,306.428) scale(-0.763595,0.763595) translate(-1849.89,91.3225)" width="12" x="795.7612409903674" xlink:href="#GroundDisconnector:地刀12_0" y="294.9737833558784" zvalue="5073"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变37387"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,800.343,306.428) scale(-0.763595,0.763595) translate(-1849.89,91.3225)" width="12" x="795.7612409903674" y="294.9737833558784"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="305">
   <path class="kv35" d="M 854.95 774.13 L 854.95 658.82" stroke-width="1" zvalue="5082"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="169@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.95 774.13 L 854.95 658.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv35" d="M 854.73 262.4 L 854.73 205.95" stroke-width="1" zvalue="5084"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="255@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.73 262.4 L 854.73 205.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="308">
   <path class="kv35" d="M 856.25 689.22 L 854.95 689.22" stroke-width="1" zvalue="5085"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="305" MaxPinNum="2"/>
   </metadata>
  <path d="M 856.25 689.22 L 854.95 689.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv35" d="M 854.76 929.47 L 854.8 929.47" stroke-width="1" zvalue="5086"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4" LinkObjectIDznd="52@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.76 929.47 L 854.8 929.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv35" d="M 937.52 189.22 L 873.03 189.22" stroke-width="1" zvalue="5088"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="255@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 937.52 189.22 L 873.03 189.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv35" d="M 836.68 189.46 L 683.33 189.46" stroke-width="1" zvalue="5089"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="255@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.68 189.46 L 683.33 189.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv35" d="M 811.28 306.44 L 854.76 306.44" stroke-width="1" zvalue="5090"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.28 306.44 L 854.76 306.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv35" d="M 854.66 974.41 L 854.66 974.41" stroke-width="1" zvalue="5092"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.66 974.41 L 854.66 974.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv35" d="M 854.66 974.41 L 854.66 1079.95" stroke-width="1" zvalue="5093"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="194@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.66 974.41 L 854.66 1079.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 854.66 974.41 L 854.66 974.41" stroke-width="1" zvalue="5094"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315" LinkObjectIDznd="316" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.66 974.41 L 854.66 974.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 854.66 814.61 L 854.73 854.4" stroke-width="1" zvalue="5097"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.66 814.61 L 854.73 854.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv35" d="M 854.76 878.23 L 854.76 975" stroke-width="1" zvalue="5098"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@1" LinkObjectIDznd="316" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.76 878.23 L 854.76 975" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 854.79 592.23 L 854.79 622.82" stroke-width="1" zvalue="5102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@1" LinkObjectIDznd="169@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.79 592.23 L 854.79 622.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv35" d="M 854.76 568.4 L 854.76 306.25 L 854.76 286.23" stroke-width="1" zvalue="5104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@0" LinkObjectIDznd="196@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.76 568.4 L 854.76 306.25 L 854.76 286.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 878.7 546.49 L 854.76 546.49" stroke-width="1" zvalue="5105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 878.7 546.49 L 854.76 546.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
</svg>