<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,42,60">
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.00564988052731" x2="21.00564988052731" y1="11.48148105720194" y2="17.55053820954545"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.00551944290267" x2="15.09409395878332" y1="17.54643096991963" y2="23.2932931396353"/>
   <line fill="none" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.02377641468389" x2="27.10610624600368" y1="17.54977445581135" y2="23.2932931396353"/>
   <ellipse cx="20.67" cy="19.6" fill-opacity="0" rx="17.75" ry="17.75" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,42,60">
   <path d="M 15.1942 39.4094 L 26.9059 39.4094 L 21.1001 49.0069 z" fill-opacity="0" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.79" cy="40.32" fill-opacity="0" rx="17.63" ry="17.63" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV大坡岭变电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,213.714,91.2321) scale(1,1) translate(0,0)" writing-mode="lr" x="213.71" xml:space="preserve" y="108.23" zvalue="573">     大坡岭变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="457" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1008.94,98.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1008.94" xml:space="preserve" y="103" zvalue="526">至35kV东坡线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="460" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.63,327.77) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.63" xml:space="preserve" y="332.27" zvalue="528">3151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="458" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,944.827,252.811) scale(1,1) translate(0,0)" writing-mode="lr" x="944.83" xml:space="preserve" y="257.31" zvalue="530">3150</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="459" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723,365.125) scale(1,1) translate(0,0)" writing-mode="lr" x="723" xml:space="preserve" y="369.63" zvalue="532">35kV电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="461" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1029.63,394.462) scale(1,1) translate(0,0)" writing-mode="lr" x="1029.63" xml:space="preserve" y="398.96" zvalue="541">315</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="462" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897.125,493.812) scale(1,1) translate(0,0)" writing-mode="lr" x="897.13" xml:space="preserve" y="500.31" zvalue="544">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="464" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031.8,599.712) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.8" xml:space="preserve" y="604.21" zvalue="547">016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="465" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.9,656.52) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.9" xml:space="preserve" y="661.02" zvalue="549">0161</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1463.25,703) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.25" xml:space="preserve" y="707.5" zvalue="553">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="466" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,764.927,774.02) scale(1,1) translate(0,0)" writing-mode="lr" x="764.9299999999999" xml:space="preserve" y="778.52" zvalue="556">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="471" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1247.54,774.02) scale(1,1) translate(0,0)" writing-mode="lr" x="1247.54" xml:space="preserve" y="778.52" zvalue="558">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.887,839.712) scale(1,1) translate(0,0)" writing-mode="lr" x="761.89" xml:space="preserve" y="844.21" zvalue="560">091</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="470" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1244,839.712) scale(1,1) translate(0,0)" writing-mode="lr" x="1244" xml:space="preserve" y="844.21" zvalue="562">092</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="468" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724.75,957) scale(1,1) translate(0,0)" writing-mode="lr" x="724.75" xml:space="preserve" y="961.5" zvalue="563">至小浪滩专线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="469" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.12,957) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.12" xml:space="preserve" y="961.5" zvalue="565">至大坡岭专线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="463" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,894.625,520.5) scale(1,1) translate(0,0)" writing-mode="lr" x="894.63" xml:space="preserve" y="527" zvalue="571">S9-4000KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,132.628,211.962) scale(1,1) translate(0,0)" writing-mode="lr" x="132.63" xml:space="preserve" y="217.96" zvalue="577">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,136.378,246.962) scale(1,1) translate(0,0)" writing-mode="lr" x="136.38" xml:space="preserve" y="252.96" zvalue="578">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.58,212.534) scale(1,1) translate(0,0)" writing-mode="lr" x="235.58" xml:space="preserve" y="218.53" zvalue="579">大坡岭变-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.58,247.534) scale(1,1) translate(0,0)" writing-mode="lr" x="231.58" xml:space="preserve" y="253.53" zvalue="580">2023-02-17</text>
 </g>
 <g id="ACLineSegmentClass">
  <g id="291">
   <use class="kv35" height="30" transform="rotate(0,1008.94,133.678) scale(5.08929,1.37019) translate(-796.378,-30.5636)" width="7" x="991.125" xlink:href="#ACLineSegment:线路_0" y="113.1249999999999" zvalue="525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至35kV东坡线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1008.94,133.678) scale(5.08929,1.37019) translate(-796.378,-30.5636)" width="7" x="991.125" y="113.1249999999999"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="409">
   <use class="kv35" height="30" transform="rotate(0,1008.84,328.77) scale(1.11111,0.814815) translate(-100.051,71.9426)" width="15" x="1000.506636610155" xlink:href="#Disconnector:刀闸_0" y="316.5473327636718" zvalue="527"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变35kV侧3151"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1008.84,328.77) scale(1.11111,0.814815) translate(-100.051,71.9426)" width="15" x="1000.506636610155" y="316.5473327636718"/></g>
  <g id="411">
   <use class="kv35" height="30" transform="rotate(90,946.404,235.02) scale(1.11111,0.814815) translate(-93.8071,50.6358)" width="15" x="938.0708696162886" xlink:href="#Disconnector:刀闸_0" y="222.7973327636718" zvalue="529"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV电压互感器3150"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,946.404,235.02) scale(1.11111,0.814815) translate(-93.8071,50.6358)" width="15" x="938.0708696162886" y="222.7973327636718"/></g>
  <g id="439">
   <use class="kv10" height="30" transform="rotate(0,1007.59,657.52) scale(1.11111,-0.814815) translate(-99.9257,-1467.25)" width="15" x="999.2566366101546" xlink:href="#Disconnector:刀闸_0" y="645.2973327636719" zvalue="548"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变10kV侧0161"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1007.59,657.52) scale(1.11111,-0.814815) translate(-99.9257,-1467.25)" width="15" x="999.2566366101546" y="645.2973327636719"/></g>
  <g id="444">
   <use class="kv10" height="30" transform="rotate(0,725.904,775.02) scale(-1.11111,0.814815) translate(-1378.38,173.363)" width="15" x="717.5708696162886" xlink:href="#Disconnector:刀闸_0" y="762.7973327636718" zvalue="555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至小浪滩专线0911"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,725.904,775.02) scale(-1.11111,0.814815) translate(-1378.38,173.363)" width="15" x="717.5708696162886" y="762.7973327636718"/></g>
  <g id="445">
   <use class="kv10" height="30" transform="rotate(0,1215.9,775.02) scale(-1.11111,0.814815) translate(-2309.38,173.363)" width="15" x="1207.570869616289" xlink:href="#Disconnector:刀闸_0" y="762.7973327636718" zvalue="557"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至大坡岭专线0921"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1215.9,775.02) scale(-1.11111,0.814815) translate(-2309.38,173.363)" width="15" x="1207.570869616289" y="762.7973327636718"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="426">
   <use class="kv35" height="30" transform="rotate(0,723,306.25) scale(-3.08333,3.08333) translate(-926.236,-175.676)" width="30" x="676.75" xlink:href="#Accessory:带熔断器35kVPT11_0" y="260" zvalue="531"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,723,306.25) scale(-3.08333,3.08333) translate(-926.236,-175.676)" width="30" x="676.75" y="260"/></g>
  <g id="427">
   <use class="kv35" height="26" transform="rotate(0,811.344,274.568) scale(1.25,1.25) translate(-160.769,-51.6636)" width="12" x="803.8441558441555" xlink:href="#Accessory:避雷器1_0" y="258.3181762695315" zvalue="533"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,811.344,274.568) scale(1.25,1.25) translate(-160.769,-51.6636)" width="12" x="803.8441558441555" y="258.3181762695315"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="429">
   <path class="kv35" d="M 1008.94 316.95 L 1008.94 154.03" stroke-width="1" zvalue="535"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="409@0" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.94 316.95 L 1008.94 154.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="430">
   <path class="kv35" d="M 958.22 235.12 L 1008.94 235.12" stroke-width="1" zvalue="536"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="411@0" LinkObjectIDznd="429" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.22 235.12 L 1008.94 235.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="431">
   <path class="kv35" d="M 723.15 263.39 L 723.15 235.09 L 934.39 235.09" stroke-width="1" zvalue="537"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="426@0" LinkObjectIDznd="411@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.15 263.39 L 723.15 235.09 L 934.39 235.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="432">
   <path class="kv35" d="M 811.39 259.11 L 811.39 235.09" stroke-width="1" zvalue="538"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="427@0" LinkObjectIDznd="431" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.39 259.11 L 811.39 235.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="434">
   <path class="kv35" d="M 1008 384.83 L 1008 340.78" stroke-width="1" zvalue="541"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="433@0" LinkObjectIDznd="409@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008 384.83 L 1008 340.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="437">
   <path class="kv35" d="M 1008.68 459.55 L 1008.68 406.07" stroke-width="1" zvalue="544"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@0" LinkObjectIDznd="433@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.68 459.55 L 1008.68 406.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv10" d="M 1007.75 590.08 L 1007.75 546.25" stroke-width="1" zvalue="549"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="438@0" LinkObjectIDznd="436@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.75 590.08 L 1007.75 546.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="441">
   <path class="kv10" d="M 1007.66 645.51 L 1007.66 611.32" stroke-width="1" zvalue="550"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="439@1" LinkObjectIDznd="438@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.66 645.51 L 1007.66 611.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="443">
   <path class="kv10" d="M 1007.69 669.34 L 1007.69 715.25" stroke-width="1" zvalue="553"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="439@0" LinkObjectIDznd="442@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.69 669.34 L 1007.69 715.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="451">
   <path class="kv10" d="M 725.81 763.2 L 725.81 715.25" stroke-width="1" zvalue="565"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="444@0" LinkObjectIDznd="442@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 725.81 763.2 L 725.81 715.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="452">
   <path class="kv10" d="M 726 830.08 L 726 787.03" stroke-width="1" zvalue="566"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="446@0" LinkObjectIDznd="444@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 726 830.08 L 726 787.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="453">
   <path class="kv10" d="M 724.75 896.44 L 724.75 851.32" stroke-width="1" zvalue="567"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="449@0" LinkObjectIDznd="446@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 724.75 896.44 L 724.75 851.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="454">
   <path class="kv10" d="M 1215.81 763.2 L 1215.81 715.25" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="445@0" LinkObjectIDznd="442@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1215.81 763.2 L 1215.81 715.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv10" d="M 1216 830.08 L 1216 787.03" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="447@0" LinkObjectIDznd="445@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1216 830.08 L 1216 787.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="456">
   <path class="kv10" d="M 1216.12 896.44 L 1216.12 851.32" stroke-width="1" zvalue="570"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="450@0" LinkObjectIDznd="447@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1216.12 896.44 L 1216.12 851.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="433">
   <use class="kv35" height="20" transform="rotate(0,1008.04,395.462) scale(1.22222,1.11111) translate(-182.169,-38.4351)" width="10" x="1001.927707236202" xlink:href="#Breaker:开关_0" y="384.35069385085" zvalue="540"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变35kV侧315"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1008.04,395.462) scale(1.22222,1.11111) translate(-182.169,-38.4351)" width="10" x="1001.927707236202" y="384.35069385085"/></g>
  <g id="438">
   <use class="kv10" height="20" transform="rotate(0,1007.79,600.712) scale(1.22222,1.11111) translate(-182.123,-58.9601)" width="10" x="1001.677707236202" xlink:href="#Breaker:开关_0" y="589.60069385085" zvalue="546"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变10kV侧016"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1007.79,600.712) scale(1.22222,1.11111) translate(-182.123,-58.9601)" width="10" x="1001.677707236202" y="589.60069385085"/></g>
  <g id="446">
   <use class="kv10" height="20" transform="rotate(0,726.039,840.712) scale(1.22222,1.11111) translate(-130.896,-82.9601)" width="10" x="719.9277072362017" xlink:href="#Breaker:开关_0" y="829.6006928971758" zvalue="559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至小浪滩专线091"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,726.039,840.712) scale(1.22222,1.11111) translate(-130.896,-82.9601)" width="10" x="719.9277072362017" y="829.6006928971758"/></g>
  <g id="447">
   <use class="kv10" height="20" transform="rotate(0,1216.04,840.712) scale(1.22222,1.11111) translate(-219.987,-82.9601)" width="10" x="1209.927707236202" xlink:href="#Breaker:开关_0" y="829.6006927490234" zvalue="561"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至大坡岭专线092"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1216.04,840.712) scale(1.22222,1.11111) translate(-219.987,-82.9601)" width="10" x="1209.927707236202" y="829.6006927490234"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="436">
   <g id="4360">
    <use class="kv0" height="60" transform="rotate(0,1008.62,502.812) scale(1.39881,1.46875) translate(-279.19,-146.41)" width="42" x="979.25" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="458.75" zvalue="543"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0"/>
    </metadata>
   </g>
   <g id="4361">
    <use class="kv0" height="60" transform="rotate(0,1008.62,502.812) scale(1.39881,1.46875) translate(-279.19,-146.41)" width="42" x="979.25" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="458.75" zvalue="543"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1008.62,502.812) scale(1.39881,1.46875) translate(-279.19,-146.41)" width="42" x="979.25" y="458.75"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="442">
   <path class="kv10" d="M 500 715.25 L 1492.5 715.25" stroke-width="6" zvalue="552"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV母线"/>
   </metadata>
  <path d="M 500 715.25 L 1492.5 715.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="449">
   <use class="kv10" height="30" transform="rotate(0,724.75,917.25) scale(1.5625,-1.54167) translate(-257.535,-1504.1)" width="12" x="715.375" xlink:href="#EnergyConsumer:负荷_0" y="894.125" zvalue="562"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至小浪滩专线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,724.75,917.25) scale(1.5625,-1.54167) translate(-257.535,-1504.1)" width="12" x="715.375" y="894.125"/></g>
  <g id="450">
   <use class="kv10" height="30" transform="rotate(0,1216.12,917.25) scale(1.5625,-1.54167) translate(-434.428,-1504.1)" width="12" x="1206.745299828794" xlink:href="#EnergyConsumer:负荷_0" y="894.125" zvalue="564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至大坡岭专线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1216.12,917.25) scale(1.5625,-1.54167) translate(-434.428,-1504.1)" width="12" x="1206.745299828794" y="894.125"/></g>
 </g>
</svg>