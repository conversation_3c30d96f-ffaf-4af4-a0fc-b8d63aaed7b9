<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Accessory:两卷变PT0926_0" viewBox="0,0,20,35">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="4.5" y2="15.5"/>
   <path d="M 7 17 L 10 22 L 13 17" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 7 25.75 L 10 30.75 L 13 25.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="9.82" cy="19.93" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.82" cy="27.62" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:放电间隙2_0" viewBox="0,0,10,15">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1.333333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="5.25" y2="10.25"/>
   <path d="M 5.01786 10.3578 L 1.5 14.4167 L 8.58025 14.4167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 5.03329 5.36336 L 1.51543 1.30453 L 8.4321 1.30453 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="7" y2="23"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="3.5" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.66666666666667" x2="4.666666666666667" y1="9.833333333333334" y2="20.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.416666666666667" x2="10.41666666666667" y1="9.5" y2="20.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV长罗变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,135.196,45.8036) scale(1,1) translate(0,0)" writing-mode="lr" x="135.2" xml:space="preserve" y="62.8" zvalue="529">    长罗变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.584,303.387) scale(1,1) translate(0,0)" writing-mode="lr" x="932.58" xml:space="preserve" y="309.39" zvalue="4">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.584,247.857) scale(1,1) translate(0,0)" writing-mode="lr" x="932.58" xml:space="preserve" y="253.86" zvalue="6">3716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.24,191.785) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.24" xml:space="preserve" y="197.79" zvalue="8">37167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.24,251.387) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.24" xml:space="preserve" y="257.39" zvalue="10">37160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.584,372.562) scale(1,1) translate(0,0)" writing-mode="lr" x="932.58" xml:space="preserve" y="378.56" zvalue="12">3711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.24,327.268) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.24" xml:space="preserve" y="333.27" zvalue="14">37117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.672,219.302) scale(1,1) translate(0,0)" writing-mode="lr" x="837.67" xml:space="preserve" y="225.3" zvalue="18">3719</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,726.957,217.445) scale(1,1) translate(0,0)" writing-mode="lr" x="726.96" xml:space="preserve" y="223.44" zvalue="28">3718</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,271.556,395.403) scale(1,1) translate(0,0)" writing-mode="lr" x="271.56" xml:space="preserve" y="401.4" zvalue="60">35kV Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.24,691.09) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.24" xml:space="preserve" y="697.09" zvalue="65">35kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1269.86,518.943) scale(1,1) translate(0,0)" writing-mode="lr" x="1269.86" xml:space="preserve" y="524.9400000000001" zvalue="71">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1183.68,487.463) scale(1,1) translate(0,0)" writing-mode="lr" x="1183.68" xml:space="preserve" y="493.46" zvalue="72">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1183.68,578.943) scale(1,1) translate(0,0)" writing-mode="lr" x="1183.68" xml:space="preserve" y="584.9400000000001" zvalue="73">39017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,576.187,606.103) scale(1,1) translate(0,0)" writing-mode="lr" x="576.1900000000001" xml:space="preserve" y="613.6" zvalue="83">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,541.178,518.943) scale(1,1) translate(0,0)" writing-mode="lr" x="541.1799999999999" xml:space="preserve" y="524.9400000000001" zvalue="85">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,542.501,440.805) scale(1,1) translate(0,0)" writing-mode="lr" x="542.5" xml:space="preserve" y="446.8" zvalue="86">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,478.267,487.463) scale(1,1) translate(0,0)" writing-mode="lr" x="478.27" xml:space="preserve" y="493.46" zvalue="87">30117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,537.724,761.652) scale(1,1) translate(0,0)" writing-mode="lr" x="537.72" xml:space="preserve" y="767.65" zvalue="98">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,258.306,805.028) scale(1,1) translate(0,0)" writing-mode="lr" x="258.31" xml:space="preserve" y="811.03" zvalue="107">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1518.82,604.584) scale(1,1) translate(0,0)" writing-mode="lr" x="1518.82" xml:space="preserve" y="612.08" zvalue="111">2号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.43,520.943) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.43" xml:space="preserve" y="526.9400000000001" zvalue="112">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1477.77,440.805) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.77" xml:space="preserve" y="446.8" zvalue="114">3021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.57,487.463) scale(1,1) translate(0,0)" writing-mode="lr" x="1410.57" xml:space="preserve" y="493.46" zvalue="116">30217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.18,761.652) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.18" xml:space="preserve" y="767.65" zvalue="125">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1652.31,805.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1652.31" xml:space="preserve" y="811.03" zvalue="134">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031.74,769.241) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.74" xml:space="preserve" y="775.24" zvalue="166">0122</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,967.568,659.986) scale(1,1) translate(0,0)" writing-mode="lr" x="967.5700000000001" xml:space="preserve" y="665.99" zvalue="172">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,397.907,963.536) scale(1,1) translate(0,0)" writing-mode="lr" x="397.91" xml:space="preserve" y="969.54" zvalue="175">07167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,391.241,884.137) scale(1,1) translate(0,0)" writing-mode="lr" x="391.24" xml:space="preserve" y="890.14" zvalue="177">071</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,395.314,1005.9) scale(1,1) translate(0,0)" writing-mode="lr" x="395.31" xml:space="preserve" y="1011.9" zvalue="186">0718</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,398.988,1053.66) scale(1,1) translate(0,0)" writing-mode="lr" x="398.99" xml:space="preserve" y="1059.66" zvalue="189">07187</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,362.048,1093.95) scale(1,1) translate(0,0)" writing-mode="lr" x="362.05" xml:space="preserve" y="1099.95" zvalue="197">1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,539.988,960.628) scale(1,1) translate(0,0)" writing-mode="lr" x="539.99" xml:space="preserve" y="966.63" zvalue="203">07267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,537.241,884.137) scale(1,1) translate(0,0)" writing-mode="lr" x="537.24" xml:space="preserve" y="890.14" zvalue="206">072</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.603,1093.95) scale(1,1) translate(0,0)" writing-mode="lr" x="503.6" xml:space="preserve" y="1099.95" zvalue="216">#1出线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,811.387,1093.95) scale(1,1) translate(0,0)" writing-mode="lr" x="811.39" xml:space="preserve" y="1099.95" zvalue="253">Ⅰ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176.99,963.265) scale(1,1) translate(0,0)" writing-mode="lr" x="1176.99" xml:space="preserve" y="969.27" zvalue="257">07467</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1169.24,884.137) scale(1,1) translate(0,0)" writing-mode="lr" x="1169.24" xml:space="preserve" y="890.14" zvalue="260">074</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173.31,1005.9) scale(1,1) translate(0,0)" writing-mode="lr" x="1173.31" xml:space="preserve" y="1011.9" zvalue="267">0748</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176.99,1053.66) scale(1,1) translate(0,0)" writing-mode="lr" x="1176.99" xml:space="preserve" y="1059.66" zvalue="269">07487</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.6,1093.95) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.6" xml:space="preserve" y="1099.95" zvalue="276">2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841.181,884.137) scale(1,1) translate(0,0)" writing-mode="lr" x="841.1799999999999" xml:space="preserve" y="890.14" zvalue="282">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1279.58,1093.95) scale(1,1) translate(0,0)" writing-mode="lr" x="1279.58" xml:space="preserve" y="1099.95" zvalue="286">Ⅱ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317.46,894.887) scale(1,1) translate(0,0)" writing-mode="lr" x="1317.46" xml:space="preserve" y="900.89" zvalue="288">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1480.99,960.628) scale(1,1) translate(0,0)" writing-mode="lr" x="1480.99" xml:space="preserve" y="966.63" zvalue="297">07567</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.24,884.137) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.24" xml:space="preserve" y="890.14" zvalue="300">075</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1442.94,1093.95) scale(1,1) translate(0,0)" writing-mode="lr" x="1442.94" xml:space="preserve" y="1099.95" zvalue="307">#3出线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1630.99,960.628) scale(1,1) translate(0,0)" writing-mode="lr" x="1630.99" xml:space="preserve" y="966.63" zvalue="316">07667</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.24,884.137) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.24" xml:space="preserve" y="890.14" zvalue="319">076</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1591.83,1093.95) scale(1,1) translate(0,0)" writing-mode="lr" x="1591.83" xml:space="preserve" y="1099.95" zvalue="326">#4出线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,965.682,89.5037) scale(1,1) translate(0,0)" writing-mode="lr" x="965.6799999999999" xml:space="preserve" y="95.5" zvalue="359">35kV龙长线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,689.574,884.137) scale(1,1) translate(0,0)" writing-mode="lr" x="689.5700000000001" xml:space="preserve" y="890.14" zvalue="376">073</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.125,1081.5) scale(1,1) translate(0,0)" writing-mode="lr" x="666.13" xml:space="preserve" y="1086" zvalue="508">10kV#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.745,357.809) scale(1,1) translate(0,0)" writing-mode="lr" x="761.74" xml:space="preserve" y="362.31" zvalue="514">35kV#1站用变</text>
 </g>
 <g id="BreakerClass">
  <g id="53">
   <use class="kv35" height="20" transform="rotate(0,966.058,304.387) scale(1.828,1.8835) translate(-433.44,-133.945)" width="10" x="956.9184699317176" xlink:href="#Breaker:开关_0" y="285.5523259442017" zvalue="3"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线371"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,966.058,304.387) scale(1.828,1.8835) translate(-433.44,-133.945)" width="10" x="956.9184699317176" y="285.5523259442017"/></g>
  <g id="33">
   <use class="kv35" height="20" transform="rotate(0,513.475,519.943) scale(1.828,1.8835) translate(-228.441,-235.056)" width="10" x="504.3351365983843" xlink:href="#Breaker:开关_0" y="501.1078787231445" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,513.475,519.943) scale(1.828,1.8835) translate(-228.441,-235.056)" width="10" x="504.3351365983843" y="501.1078787231445"/></g>
  <g id="210">
   <use class="kv10" height="20" transform="rotate(0,513.835,761.408) scale(1.828,1.8835) translate(-228.604,-348.321)" width="10" x="504.6949349047796" xlink:href="#Breaker:开关_0" y="742.5725241088867" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,513.835,761.408) scale(1.828,1.8835) translate(-228.604,-348.321)" width="10" x="504.6949349047796" y="742.5725241088867"/></g>
  <g id="106">
   <use class="kv35" height="20" transform="rotate(0,1448.73,519.943) scale(1.828,1.8835) translate(-652.066,-235.056)" width="10" x="1439.585136598384" xlink:href="#Breaker:开关_0" y="501.1078787231445" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧302"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1448.73,519.943) scale(1.828,1.8835) translate(-652.066,-235.056)" width="10" x="1439.585136598384" y="501.1078787231445"/></g>
  <g id="94">
   <use class="kv10" height="20" transform="rotate(0,1448.29,761.408) scale(1.828,1.8835) translate(-651.869,-348.321)" width="10" x="1439.149480359325" xlink:href="#Breaker:开关_0" y="742.5725236494906" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变10kV侧002"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1448.29,761.408) scale(1.828,1.8835) translate(-651.869,-348.321)" width="10" x="1439.149480359325" y="742.5725236494906"/></g>
  <g id="135">
   <use class="kv10" height="20" transform="rotate(0,365.494,882.726) scale(1.828,1.8835) translate(-161.412,-405.228)" width="10" x="356.3540258138707" xlink:href="#Breaker:开关_0" y="863.8907080078125" zvalue="176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器071"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,365.494,882.726) scale(1.828,1.8835) translate(-161.412,-405.228)" width="10" x="356.3540258138707" y="863.8907080078125"/></g>
  <g id="166">
   <use class="kv10" height="20" transform="rotate(0,511.494,882.726) scale(1.828,1.8835) translate(-227.543,-405.228)" width="10" x="502.3540258138706" xlink:href="#Breaker:开关_0" y="863.8907080078127" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线072"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,511.494,882.726) scale(1.828,1.8835) translate(-227.543,-405.228)" width="10" x="502.3540258138706" y="863.8907080078127"/></g>
  <g id="228">
   <use class="kv10" height="20" transform="rotate(0,1143.49,882.726) scale(1.828,1.8835) translate(-513.81,-405.228)" width="10" x="1134.354025813871" xlink:href="#Breaker:开关_0" y="863.8907080078125" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV2号电容器074"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1143.49,882.726) scale(1.828,1.8835) translate(-513.81,-405.228)" width="10" x="1134.354025813871" y="863.8907080078125"/></g>
  <g id="259">
   <use class="kv10" height="20" transform="rotate(0,1447.49,882.726) scale(1.828,1.8835) translate(-651.508,-405.228)" width="10" x="1438.354025813871" xlink:href="#Breaker:开关_0" y="863.8907080078127" zvalue="299"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#3出线075"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1447.49,882.726) scale(1.828,1.8835) translate(-651.508,-405.228)" width="10" x="1438.354025813871" y="863.8907080078127"/></g>
  <g id="278">
   <use class="kv10" height="20" transform="rotate(0,1597.49,882.726) scale(1.828,1.8835) translate(-719.451,-405.228)" width="10" x="1588.354025813871" xlink:href="#Breaker:开关_0" y="863.8907080078127" zvalue="318"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#4出线076"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1597.49,882.726) scale(1.828,1.8835) translate(-719.451,-405.228)" width="10" x="1588.354025813871" y="863.8907080078127"/></g>
  <g id="159">
   <use class="kv10" height="20" transform="rotate(0,663.827,882.726) scale(1.828,1.8835) translate(-296.543,-405.228)" width="10" x="654.687359147204" xlink:href="#Breaker:开关_0" y="863.8907080078127" zvalue="375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2站用变073"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,663.827,882.726) scale(1.828,1.8835) translate(-296.543,-405.228)" width="10" x="654.687359147204" y="863.8907080078127"/></g>
  <g id="284">
   <use class="kv10" height="20" transform="rotate(0,870.681,766.167) scale(1.828,1.8835) translate(-390.238,-350.554)" width="10" x="861.540735930736" xlink:href="#Breaker:母联开关_0" y="747.3322077922076" zvalue="460"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段012"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,870.681,766.167) scale(1.828,1.8835) translate(-390.238,-350.554)" width="10" x="861.540735930736" y="747.3322077922076"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="58">
   <use class="kv35" height="30" transform="rotate(0,965.965,246.249) scale(-1.11133,0.814667) translate(-1834.32,53.2407)" width="15" x="957.6295835345629" xlink:href="#Disconnector:刀闸_0" y="234.0291145883644" zvalue="5"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线3716"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,965.965,246.249) scale(-1.11133,0.814667) translate(-1834.32,53.2407)" width="15" x="957.6295835345629" y="234.0291145883644"/></g>
  <g id="13">
   <use class="kv35" height="30" transform="rotate(0,965.827,368.268) scale(1.11133,-0.814667) translate(-95.9215,-823.095)" width="15" x="957.4918440709615" xlink:href="#Disconnector:刀闸_0" y="356.0476694644713" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线3711"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,965.827,368.268) scale(1.11133,-0.814667) translate(-95.9215,-823.095)" width="15" x="957.4918440709615" y="356.0476694644713"/></g>
  <g id="235">
   <use class="kv35" height="30" transform="rotate(180,871.719,220.302) scale(-1.42857,-1.42857) translate(-1478.71,-368.085)" width="15" x="861.0050738371638" xlink:href="#Disconnector:令克_0" y="198.8732473114264" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线3719"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,871.719,220.302) scale(-1.42857,-1.42857) translate(-1478.71,-368.085)" width="15" x="861.0050738371638" y="198.8732473114264"/></g>
  <g id="11">
   <use class="kv35" height="30" transform="rotate(180,761.005,218.445) scale(-1.42857,-1.42857) translate(-1290.49,-364.927)" width="15" x="750.290788122878" xlink:href="#Disconnector:令克_0" y="197.0161044542834" zvalue="27"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线3718"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,761.005,218.445) scale(-1.42857,-1.42857) translate(-1290.49,-364.927)" width="15" x="750.290788122878" y="197.0161044542834"/></g>
  <g id="51">
   <use class="kv35" height="30" transform="rotate(0,1242.41,519.943) scale(-1.11133,0.814667) translate(-2359.52,115.505)" width="15" x="1234.074027979007" xlink:href="#Disconnector:刀闸_0" y="507.7228787716208" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母电压互感器3901"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1242.41,519.943) scale(-1.11133,0.814667) translate(-2359.52,115.505)" width="15" x="1234.074027979007" y="507.7228787716208"/></g>
  <g id="18">
   <use class="kv35" height="30" transform="rotate(0,514.631,441.805) scale(-1.11133,0.814667) translate(-976.872,97.7288)" width="15" x="506.2962502012296" xlink:href="#Disconnector:刀闸_0" y="429.5846722412109" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,514.631,441.805) scale(-1.11133,0.814667) translate(-976.872,97.7288)" width="15" x="506.2962502012296" y="429.5846722412109"/></g>
  <g id="105">
   <use class="kv35" height="30" transform="rotate(0,1448.63,441.805) scale(-1.11133,0.814667) translate(-2751.3,97.7288)" width="15" x="1440.29625020123" xlink:href="#Disconnector:刀闸_0" y="429.5846722412109" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧3021"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1448.63,441.805) scale(-1.11133,0.814667) translate(-2751.3,97.7288)" width="15" x="1440.29625020123" y="429.5846722412109"/></g>
  <g id="26">
   <use class="kv10" height="30" transform="rotate(0,1062.8,769.408) scale(1.42507,2.71455) translate(-313.825,-460.251)" width="15" x="1052.113361584805" xlink:href="#Disconnector:联体手车刀闸3_0" y="728.6892195647312" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段0122手车"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062.8,769.408) scale(1.42507,2.71455) translate(-313.825,-460.251)" width="15" x="1052.113361584805" y="728.6892195647312"/></g>
  <g id="138">
   <use class="kv10" height="30" transform="rotate(0,365.425,1006.9) scale(1.11133,0.814667) translate(-35.7733,226.286)" width="15" x="357.0900084191153" xlink:href="#Disconnector:刀闸_0" y="994.6831549924049" zvalue="185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器0718"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,365.425,1006.9) scale(1.11133,0.814667) translate(-35.7733,226.286)" width="15" x="357.0900084191153" y="994.6831549924049"/></g>
  <g id="222">
   <use class="kv10" height="30" transform="rotate(0,1143.43,1006.9) scale(1.11133,0.814667) translate(-113.713,226.286)" width="15" x="1135.090008419115" xlink:href="#Disconnector:刀闸_0" y="994.6831549924049" zvalue="266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV2号电容器0748"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1143.43,1006.9) scale(1.11133,0.814667) translate(-113.713,226.286)" width="15" x="1135.090008419115" y="994.6831549924049"/></g>
  <g id="231">
   <use class="kv10" height="30" transform="rotate(0,811.523,882.726) scale(1.42507,2.71455) translate(-238.874,-531.825)" width="15" x="800.8345008294748" xlink:href="#Disconnector:联体手车刀闸3_0" y="842.0074033576931" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母电压互感器0901"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,811.523,882.726) scale(1.42507,2.71455) translate(-238.874,-531.825)" width="15" x="800.8345008294748" y="842.0074033576931"/></g>
  <g id="240">
   <use class="kv10" height="30" transform="rotate(0,1285.52,882.726) scale(1.42507,2.71455) translate(-380.259,-531.825)" width="15" x="1274.834500829475" xlink:href="#Disconnector:联体手车刀闸3_0" y="842.0074033576931" zvalue="287"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅱ母电压互感器0902"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1285.52,882.726) scale(1.42507,2.71455) translate(-380.259,-531.825)" width="15" x="1274.834500829475" y="842.0074033576931"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="125">
   <use class="kv35" height="30" transform="rotate(90,1027.41,212.022) scale(-1.04822,-1.04822) translate(-2007.28,-413.568)" width="12" x="1021.123406270532" xlink:href="#GroundDisconnector:地刀12_0" y="196.2986107302402" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线37167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1027.41,212.022) scale(-1.04822,-1.04822) translate(-2007.28,-413.568)" width="12" x="1021.123406270532" y="196.2986107302402"/></g>
  <g id="14">
   <use class="kv35" height="30" transform="rotate(90,1027.41,268.062) scale(-1.04822,-1.04822) translate(-2007.28,-523.07)" width="12" x="1021.123406249211" xlink:href="#GroundDisconnector:地刀12_0" y="252.3385266966267" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线37160"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1027.41,268.062) scale(-1.04822,-1.04822) translate(-2007.28,-523.07)" width="12" x="1021.123406249211" y="252.3385266966267"/></g>
  <g id="268">
   <use class="kv35" height="30" transform="rotate(90,1027.41,344.268) scale(-1.04822,-1.04822) translate(-2007.28,-671.976)" width="12" x="1021.12340625973" xlink:href="#GroundDisconnector:地刀12_0" y="328.5444051111444" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线37117"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1027.41,344.268) scale(-1.04822,-1.04822) translate(-2007.28,-671.976)" width="12" x="1021.12340625973" y="328.5444051111444"/></g>
  <g id="50">
   <use class="kv35" height="30" transform="rotate(90,1183.68,468.795) scale(1.04822,1.04822) translate(-54.1594,-20.8411)" width="12" x="1177.387714792798" xlink:href="#GroundDisconnector:地刀12_0" y="453.0717030847771" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母电压互感器39010"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1183.68,468.795) scale(1.04822,1.04822) translate(-54.1594,-20.8411)" width="12" x="1177.387714792798" y="453.0717030847771"/></g>
  <g id="49">
   <use class="kv35" height="30" transform="rotate(90,1184.35,555.617) scale(1.04822,1.04822) translate(-54.1903,-24.8349)" width="12" x="1178.059983416987" xlink:href="#GroundDisconnector:地刀12_0" y="539.8940822521822" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母电压互感器39017"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1184.35,555.617) scale(1.04822,1.04822) translate(-54.1903,-24.8349)" width="12" x="1178.059983416987" y="539.8940822521822"/></g>
  <g id="25">
   <use class="kv35" height="30" transform="rotate(90,476.572,468.795) scale(1.04822,1.04822) translate(-21.6328,-20.8411)" width="12" x="470.2822056392093" xlink:href="#GroundDisconnector:地刀12_0" y="453.0717030847774" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧30117"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,476.572,468.795) scale(1.04822,1.04822) translate(-21.6328,-20.8411)" width="12" x="470.2822056392093" y="453.0717030847774"/></g>
  <g id="104">
   <use class="kv35" height="30" transform="rotate(90,1410.57,468.795) scale(1.04822,1.04822) translate(-64.5965,-20.8411)" width="12" x="1404.282205639209" xlink:href="#GroundDisconnector:地刀12_0" y="453.0717030847771" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧30217"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1410.57,468.795) scale(1.04822,1.04822) translate(-64.5965,-20.8411)" width="12" x="1404.282205639209" y="453.0717030847771"/></g>
  <g id="137">
   <use class="kv10" height="30" transform="rotate(90,398.988,945.462) scale(-1.04822,-1.04822) translate(-779.334,-1846.71)" width="12" x="392.6990974936765" xlink:href="#GroundDisconnector:地刀12_0" y="929.7390670484567" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器07167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,398.988,945.462) scale(-1.04822,-1.04822) translate(-779.334,-1846.71)" width="12" x="392.6990974936765" y="929.7390670484567"/></g>
  <g id="140">
   <use class="kv10" height="30" transform="rotate(90,398.988,1035.86) scale(-1.04822,-1.04822) translate(-779.334,-2023.35)" width="12" x="392.6990974961928" xlink:href="#GroundDisconnector:地刀12_0" y="1020.137702666855" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器07187"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,398.988,1035.86) scale(-1.04822,-1.04822) translate(-779.334,-2023.35)" width="12" x="392.6990974961928" y="1020.137702666855"/></g>
  <g id="168">
   <use class="kv10" height="30" transform="rotate(90,539.988,942.825) scale(-1.04822,-1.04822) translate(-1054.85,-1841.56)" width="12" x="533.6990974936765" xlink:href="#GroundDisconnector:地刀12_0" y="927.1020490166006" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线07267"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,539.988,942.825) scale(-1.04822,-1.04822) translate(-1054.85,-1841.56)" width="12" x="533.6990974936765" y="927.1020490166006"/></g>
  <g id="230">
   <use class="kv10" height="30" transform="rotate(270,1176.99,945.462) scale(-1.04822,1.04822) translate(-2299.55,-42.7677)" width="12" x="1170.699097493677" xlink:href="#GroundDisconnector:地刀12_0" y="929.7390670484567" zvalue="256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV2号电容器07467"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1176.99,945.462) scale(-1.04822,1.04822) translate(-2299.55,-42.7677)" width="12" x="1170.699097493677" y="929.7390670484567"/></g>
  <g id="221">
   <use class="kv10" height="30" transform="rotate(270,1176.99,1035.86) scale(-1.04822,1.04822) translate(-2299.55,-46.926)" width="12" x="1170.699097496193" xlink:href="#GroundDisconnector:地刀12_0" y="1020.137702666855" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV2号电容器07487"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1176.99,1035.86) scale(-1.04822,1.04822) translate(-2299.55,-46.926)" width="12" x="1170.699097496193" y="1020.137702666855"/></g>
  <g id="261">
   <use class="kv10" height="30" transform="rotate(270,1480.99,942.825) scale(-1.04822,1.04822) translate(-2893.56,-42.6464)" width="12" x="1474.699097493677" xlink:href="#GroundDisconnector:地刀12_0" y="927.1020490166005" zvalue="296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#3出线07567"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1480.99,942.825) scale(-1.04822,1.04822) translate(-2893.56,-42.6464)" width="12" x="1474.699097493677" y="927.1020490166005"/></g>
  <g id="280">
   <use class="kv10" height="30" transform="rotate(270,1630.99,942.825) scale(-1.04822,1.04822) translate(-3186.66,-42.6464)" width="12" x="1624.699097493677" xlink:href="#GroundDisconnector:地刀12_0" y="927.1020490166006" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#4出线07667"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1630.99,942.825) scale(-1.04822,1.04822) translate(-3186.66,-42.6464)" width="12" x="1624.699097493677" y="927.1020490166006"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="99">
   <use class="kv35" height="26" transform="rotate(270,1027.41,148.618) scale(1,1) translate(1.70099e-12,0)" width="12" x="1021.412712097168" xlink:href="#Accessory:避雷器_0" y="135.6177536231882" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1027.41,148.618) scale(1,1) translate(1.70099e-12,0)" width="12" x="1021.412712097168" y="135.6177536231882"/></g>
  <g id="8">
   <use class="kv35" height="35" transform="rotate(0,871.838,288.346) scale(1.75933,1.75933) translate(-368.693,-111.162)" width="20" x="854.2451162508902" xlink:href="#Accessory:两卷变PT0926_0" y="257.5580971659921" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,871.838,288.346) scale(1.75933,1.75933) translate(-368.693,-111.162)" width="20" x="854.2451162508902" y="257.5580971659921"/></g>
  <g id="665">
   <use class="kv35" height="40" transform="rotate(0,1259.73,639.804) scale(1.725,1.725) translate(-514.95,-254.403)" width="40" x="1225.226988450236" xlink:href="#Accessory:五卷PT_0" y="605.3040366030441" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1259.73,639.804) scale(1.725,1.725) translate(-514.95,-254.403)" width="40" x="1225.226988450236" y="605.3040366030441"/></g>
  <g id="70">
   <use class="kv35" height="26" transform="rotate(0,473.216,623.037) scale(-1,1) translate(-946.433,0)" width="12" x="467.2163742690058" xlink:href="#Accessory:避雷器_0" y="610.0365497076024" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,473.216,623.037) scale(-1,1) translate(-946.433,0)" width="12" x="467.2163742690058" y="610.0365497076024"/></g>
  <g id="75">
   <use class="kv10" height="26" transform="rotate(90,545.023,711.247) scale(-1,-1) translate(-1090.05,-1422.49)" width="12" x="539.0227272727275" xlink:href="#Accessory:避雷器_0" y="698.2467346191406" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,545.023,711.247) scale(-1,-1) translate(-1090.05,-1422.49)" width="12" x="539.0227272727275" y="698.2467346191406"/></g>
  <g id="108">
   <use class="kv35" height="26" transform="rotate(0,1407.22,623.037) scale(-1,1) translate(-2814.43,0)" width="12" x="1401.216374269006" xlink:href="#Accessory:避雷器_0" y="610.0365497076024" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1407.22,623.037) scale(-1,1) translate(-2814.43,0)" width="12" x="1401.216374269006" y="610.0365497076024"/></g>
  <g id="95">
   <use class="kv10" height="26" transform="rotate(90,1478.48,671.511) scale(-1,-1) translate(-2956.95,-1343.02)" width="12" x="1472.477272727273" xlink:href="#Accessory:避雷器_0" y="658.511243992314" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1478.48,671.511) scale(-1,-1) translate(-2956.95,-1343.02)" width="12" x="1472.477272727273" y="658.511243992314"/></g>
  <g id="124">
   <use class="kv10" height="15" transform="rotate(0,514.447,689.435) scale(0.94642,1.15674) translate(28.8566,-92.2417)" width="10" x="509.7153336651508" xlink:href="#Accessory:放电间隙2_0" y="680.7591257263189" zvalue="136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,514.447,689.435) scale(0.94642,1.15674) translate(28.8566,-92.2417)" width="10" x="509.7153336651508" y="680.7591257263189"/></g>
  <g id="111">
   <use class="kv10" height="15" transform="rotate(0,1448.49,690.611) scale(0.94642,1.15674) translate(81.7357,-92.4011)" width="10" x="1443.758114413814" xlink:href="#Accessory:放电间隙2_0" y="681.9355963145542" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变10kV侧放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1448.49,690.611) scale(0.94642,1.15674) translate(81.7357,-92.4011)" width="10" x="1443.758114413814" y="681.9355963145542"/></g>
  <g id="136">
   <use class="kv10" height="26" transform="rotate(180,326.615,967.465) scale(-1,-1) translate(-653.229,-1934.93)" width="12" x="320.614613880743" xlink:href="#Accessory:避雷器_0" y="954.4654541015626" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,326.615,967.465) scale(-1,-1) translate(-653.229,-1934.93)" width="12" x="320.614613880743" y="954.4654541015626"/></g>
  <g id="129">
   <use class="kv10" height="15" transform="rotate(0,366.111,972.013) scale(0.94642,1.15674) translate(20.4588,-130.531)" width="10" x="361.3793265350262" xlink:href="#Accessory:放电间隙2_0" y="963.3371080752272" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,366.111,972.013) scale(0.94642,1.15674) translate(20.4588,-130.531)" width="10" x="361.3793265350262" y="963.3371080752272"/></g>
  <g id="167">
   <use class="kv10" height="26" transform="rotate(180,472.615,967.465) scale(-1,-1) translate(-945.229,-1934.93)" width="12" x="466.614613880743" xlink:href="#Accessory:避雷器_0" y="954.4654541015625" zvalue="204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,472.615,967.465) scale(-1,-1) translate(-945.229,-1934.93)" width="12" x="466.614613880743" y="954.4654541015625"/></g>
  <g id="170">
   <use class="kv10" height="15" transform="rotate(0,511.486,971.388) scale(0.94642,1.15674) translate(28.689,-130.446)" width="10" x="506.7543265350261" xlink:href="#Accessory:放电间隙2_0" y="962.7121008311557" zvalue="211"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,511.486,971.388) scale(0.94642,1.15674) translate(28.689,-130.446)" width="10" x="506.7543265350261" y="962.7121008311557"/></g>
  <g id="204">
   <use class="kv10" height="40" transform="rotate(0,823.281,1040.37) scale(1.17308,1.17308) translate(-118.008,-150.039)" width="40" x="799.8195072223477" xlink:href="#Accessory:五卷PT_0" y="1016.910086039448" zvalue="252"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,823.281,1040.37) scale(1.17308,1.17308) translate(-118.008,-150.039)" width="40" x="799.8195072223477" y="1016.910086039448"/></g>
  <g id="229">
   <use class="kv10" height="26" transform="rotate(180,1104.61,967.465) scale(-1,-1) translate(-2209.23,-1934.93)" width="12" x="1098.614613880743" xlink:href="#Accessory:避雷器_0" y="954.4654541015626" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV2号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1104.61,967.465) scale(-1,-1) translate(-2209.23,-1934.93)" width="12" x="1098.614613880743" y="954.4654541015626"/></g>
  <g id="223">
   <use class="kv10" height="15" transform="rotate(0,1144.11,972.013) scale(0.94642,1.15674) translate(64.5039,-130.531)" width="10" x="1139.379326535026" xlink:href="#Accessory:放电间隙2_0" y="963.3371080752272" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV2号电容器放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1144.11,972.013) scale(0.94642,1.15674) translate(64.5039,-130.531)" width="10" x="1139.379326535026" y="963.3371080752272"/></g>
  <g id="241">
   <use class="kv10" height="40" transform="rotate(0,1298.07,1040.37) scale(1.17308,1.17308) translate(-188.061,-150.039)" width="40" x="1274.609911262752" xlink:href="#Accessory:五卷PT_0" y="1016.910086068562" zvalue="285"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅱ母电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1298.07,1040.37) scale(1.17308,1.17308) translate(-188.061,-150.039)" width="40" x="1274.609911262752" y="1016.910086068562"/></g>
  <g id="260">
   <use class="kv10" height="26" transform="rotate(180,1408.61,967.465) scale(-1,-1) translate(-2817.23,-1934.93)" width="12" x="1402.614613880743" xlink:href="#Accessory:避雷器_0" y="954.4654541015626" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#3出线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1408.61,967.465) scale(-1,-1) translate(-2817.23,-1934.93)" width="12" x="1402.614613880743" y="954.4654541015626"/></g>
  <g id="254">
   <use class="kv10" height="15" transform="rotate(0,1448.11,972.013) scale(0.94642,1.15674) translate(81.7143,-130.531)" width="10" x="1443.379326535026" xlink:href="#Accessory:放电间隙2_0" y="963.3371080752272" zvalue="305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#3出线放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1448.11,972.013) scale(0.94642,1.15674) translate(81.7143,-130.531)" width="10" x="1443.379326535026" y="963.3371080752272"/></g>
  <g id="279">
   <use class="kv10" height="26" transform="rotate(180,1558.61,967.465) scale(-1,-1) translate(-3117.23,-1934.93)" width="12" x="1552.614613880743" xlink:href="#Accessory:避雷器_0" y="954.4654541015625" zvalue="317"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#4出线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1558.61,967.465) scale(-1,-1) translate(-3117.23,-1934.93)" width="12" x="1552.614613880743" y="954.4654541015625"/></g>
  <g id="273">
   <use class="kv10" height="15" transform="rotate(0,1598.11,972.013) scale(0.94642,1.15674) translate(90.2062,-130.531)" width="10" x="1593.379326535026" xlink:href="#Accessory:放电间隙2_0" y="963.3371008311557" zvalue="324"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#4出线放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1598.11,972.013) scale(0.94642,1.15674) translate(90.2062,-130.531)" width="10" x="1593.379326535026" y="963.3371008311557"/></g>
  <g id="72">
   <use class="kv10" height="26" transform="rotate(180,623.521,967.465) scale(-1,-1) translate(-1247.04,-1934.93)" width="12" x="617.5209110149816" xlink:href="#Accessory:避雷器_0" y="954.4654541015625" zvalue="365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,623.521,967.465) scale(-1,-1) translate(-1247.04,-1934.93)" width="12" x="617.5209110149816" y="954.4654541015625"/></g>
  <g id="46">
   <use class="kv10" height="15" transform="rotate(0,663.325,971.558) scale(0.94642,1.15674) translate(37.285,-130.469)" width="10" x="658.5932733366706" xlink:href="#Accessory:放电间隙2_0" y="962.8825554186501" zvalue="366"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2站用变放电间隙"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,663.325,971.558) scale(0.94642,1.15674) translate(37.285,-130.469)" width="10" x="658.5932733366706" y="962.8825554186501"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="20">
   <path class="kv35" d="M 965.9 258.26 L 965.9 286.37" stroke-width="1" zvalue="35"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.9 258.26 L 965.9 286.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 966.18 322.37 L 966.18 356.26" stroke-width="1" zvalue="36"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="13@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.18 322.37 L 966.18 356.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 1012.4 268.07 L 965.9 268.07" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@0" LinkObjectIDznd="20" MaxPinNum="2"/>
   </metadata>
  <path d="M 1012.4 268.07 L 965.9 268.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv35" d="M 1012.4 344.28 L 966.18 344.28" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 1012.4 344.28 L 966.18 344.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 965.92 380.08 L 965.92 415.78" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.92 380.08 L 965.92 415.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv35" d="M 1242.48 607.03 L 1242.48 531.95" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="665@0" LinkObjectIDznd="51@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1242.48 607.03 L 1242.48 531.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv35" d="M 1242.31 508.13 L 1242.31 415.78" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1242.31 508.13 L 1242.31 415.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv35" d="M 1198.69 468.78 L 1242.31 468.78" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="54" MaxPinNum="2"/>
   </metadata>
  <path d="M 1198.69 468.78 L 1242.31 468.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 1199.36 555.61 L 1242.48 555.61" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 1199.36 555.61 L 1242.48 555.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 514.53 429.99 L 514.53 415.78" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="42@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.53 429.99 L 514.53 415.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv35" d="M 514.56 453.82 L 514.56 501.92" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@1" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.56 453.82 L 514.56 501.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv35" d="M 513.6 537.93 L 513.6 572.15" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@1" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.6 537.93 L 513.6 572.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv35" d="M 491.59 468.78 L 514.56 468.78" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 491.59 468.78 L 514.56 468.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv35" d="M 473.18 610.67 L 473.18 592.85 L 514.51 592.85" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="84@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 473.18 610.67 L 473.18 592.85 L 514.51 592.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 514.28 731.34 L 514.28 743.39" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@1" LinkObjectIDznd="210@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.28 731.34 L 514.28 743.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 1448.53 429.99 L 1448.53 415.78" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="42@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.53 429.99 L 1448.53 415.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv35" d="M 1448.56 453.82 L 1448.56 501.92" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@1" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.56 453.82 L 1448.56 501.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 1448.85 537.93 L 1448.9 570.64" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@1" LinkObjectIDznd="107@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.85 537.93 L 1448.9 570.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv35" d="M 1425.59 468.78 L 1448.56 468.78" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="102" MaxPinNum="2"/>
   </metadata>
  <path d="M 1425.59 468.78 L 1448.56 468.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 1448.28 731.34 L 1448.23 743.39" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@1" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.28 731.34 L 1448.23 743.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv35" d="M 760.89 287.14 L 760.89 235.94" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="337@0" LinkObjectIDznd="11@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.89 287.14 L 760.89 235.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 965.87 234.43 L 965.87 134.6" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.87 234.43 L 965.87 134.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 1015.05 148.58 L 965.87 148.58" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.05 148.58 L 965.87 148.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 1012.4 212.03 L 965.87 212.03" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1012.4 212.03 L 965.87 212.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv35" d="M 871.84 201.37 L 871.84 186.21 L 965.87 186.21" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.84 201.37 L 871.84 186.21 L 965.87 186.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 870.75 801.91 L 870.75 824.53" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@0" LinkObjectIDznd="78@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.75 801.91 L 870.75 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv10" d="M 871.08 721.35 L 871.08 695.71 L 1062.8 695.71 L 1062.8 739.55" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="346@0" LinkObjectIDznd="26@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.08 721.35 L 871.08 695.71 L 1062.8 695.71 L 1062.8 739.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 1062.8 799.27 L 1062.8 824.53" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@1" LinkObjectIDznd="109@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.8 799.27 L 1062.8 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv10" d="M 365.49 848.66 L 365.43 864.71" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@1" LinkObjectIDznd="135@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 365.49 848.66 L 365.43 864.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv10" d="M 365.45 838.67 L 365.45 824.53" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="78@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 365.45 838.67 L 365.45 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 383.97 945.47 L 365.53 945.47" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="35" MaxPinNum="2"/>
   </metadata>
  <path d="M 383.97 945.47 L 365.53 945.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 326.65 955.1 L 326.65 945.77 L 365.53 945.77" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="35" MaxPinNum="2"/>
   </metadata>
  <path d="M 326.65 955.1 L 326.65 945.77 L 365.53 945.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 365.49 1018.91 L 365.49 1054.34" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@1" LinkObjectIDznd="147@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 365.49 1018.91 L 365.49 1054.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 383.97 1035.87 L 365.49 1035.87" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 383.97 1035.87 L 365.49 1035.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 511.49 848.66 L 511.43 864.71" stroke-width="1" zvalue="210"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 511.49 848.66 L 511.43 864.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 511.45 838.67 L 511.45 824.53" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="78@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 511.45 838.67 L 511.45 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 511.53 927.23 L 511.49 1054.34" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="169@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 511.53 927.23 L 511.49 1054.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 524.97 942.84 L 511.52 942.84" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 524.97 942.84 L 511.52 942.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 472.65 955.1 L 472.65 942.39 L 511.52 942.39" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 472.65 955.1 L 472.65 942.39 L 511.52 942.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 511.49 964.25 L 511.52 942.84" stroke-width="1" zvalue="221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 511.49 964.25 L 511.52 942.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 1143.49 849.91 L 1143.43 864.71" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@1" LinkObjectIDznd="228@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.49 849.91 L 1143.43 864.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv10" d="M 1143.45 839.92 L 1143.45 824.53" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="109@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.45 839.92 L 1143.45 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 1143.49 1018.91 L 1143.49 1054.34" stroke-width="1" zvalue="277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@1" LinkObjectIDznd="215@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.49 1018.91 L 1143.49 1054.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 1161.97 1035.85 L 1143.49 1035.85" stroke-width="1" zvalue="278"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="214" MaxPinNum="2"/>
   </metadata>
  <path d="M 1161.97 1035.85 L 1143.49 1035.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 811.52 852.87 L 811.52 824.53" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="78@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.52 852.87 L 811.52 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 811.52 912.59 L 811.55 1018.08" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="204@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.52 912.59 L 811.55 1018.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 1285.52 852.87 L 1285.52 824.53" stroke-width="1" zvalue="289"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="109@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1285.52 852.87 L 1285.52 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv10" d="M 1285.52 912.59 L 1285.52 1018.08" stroke-width="1" zvalue="290"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@1" LinkObjectIDznd="241@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1285.52 912.59 L 1285.52 1018.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 1448.74 851.16 L 1448.74 864.71" stroke-width="1" zvalue="304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@1" LinkObjectIDznd="259@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.74 851.16 L 1448.74 864.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv10" d="M 1448.7 841.17 L 1448.7 824.53" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="109@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.7 841.17 L 1448.7 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 1447.53 927.23 L 1447.49 1054.34" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@0" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1447.53 927.23 L 1447.49 1054.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 1465.97 942.81 L 1447.52 942.81" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="251" MaxPinNum="2"/>
   </metadata>
  <path d="M 1465.97 942.81 L 1447.52 942.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv10" d="M 1408.65 955.1 L 1408.65 942.39 L 1447.52 942.39" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="251" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.65 955.1 L 1408.65 942.39 L 1447.52 942.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv10" d="M 1448.11 964.88 L 1448.11 942.81" stroke-width="1" zvalue="312"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="250" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.11 964.88 L 1448.11 942.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv10" d="M 1598.74 851.16 L 1598.74 864.71" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@1" LinkObjectIDznd="278@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1598.74 851.16 L 1598.74 864.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv10" d="M 1597.53 927.23 L 1597.49 1054.34" stroke-width="1" zvalue="328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.53 927.23 L 1597.49 1054.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 1615.97 942.81 L 1597.52 942.81" stroke-width="1" zvalue="329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1615.97 942.81 L 1597.52 942.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv10" d="M 1558.65 955.1 L 1558.65 942.39 L 1597.52 942.39" stroke-width="1" zvalue="330"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1558.65 955.1 L 1558.65 942.39 L 1597.52 942.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv10" d="M 1598.11 964.88 L 1598.11 942.81" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 1598.11 964.88 L 1598.11 942.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv10" d="M 514.32 801.91 L 514.32 824.53" stroke-width="1" zvalue="334"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="78@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.32 801.91 L 514.32 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 761.12 199.52 L 761.12 149.68 L 965.87 149.68" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 761.12 199.52 L 761.12 149.68 L 965.87 149.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 663.82 849.91 L 663.77 864.71" stroke-width="1" zvalue="380"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@1" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 663.82 849.91 L 663.77 864.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv10" d="M 663.78 839.92 L 663.78 824.53" stroke-width="1" zvalue="382"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="78@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 663.78 839.92 L 663.78 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 623.55 955.1 L 623.55 936.25 L 663.33 936.25" stroke-width="1" zvalue="384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 623.55 955.1 L 623.55 936.25 L 663.33 936.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 663.33 964.42 L 663.33 927.23" stroke-width="1" zvalue="385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 663.33 964.42 L 663.33 927.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv10" d="M 663.91 1019.14 L 663.91 964.42" stroke-width="1" zvalue="398"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 663.91 1019.14 L 663.91 964.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv35" d="M 871.84 265.48 L 871.84 237.8" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="235@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.84 265.48 L 871.84 237.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 514.36 791.92 L 514.36 779.39" stroke-width="1" zvalue="417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@1" LinkObjectIDznd="210@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.36 791.92 L 514.36 779.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1448.41 779.39 L 1448.41 801.42" stroke-width="1" zvalue="418"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="92@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.41 779.39 L 1448.41 801.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 365.62 900.71 L 365.56 917.24" stroke-width="1" zvalue="420"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@1" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 365.62 900.71 L 365.56 917.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 511.62 900.71 L 511.56 917.24" stroke-width="1" zvalue="421"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@1" LinkObjectIDznd="164@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 511.62 900.71 L 511.56 917.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 663.95 900.71 L 663.9 917.24" stroke-width="1" zvalue="422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@1" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 663.95 900.71 L 663.9 917.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1143.62 900.71 L 1143.56 917.24" stroke-width="1" zvalue="423"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@1" LinkObjectIDznd="226@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.62 900.71 L 1143.56 917.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1447.56 917.24 L 1447.62 900.71" stroke-width="1" zvalue="424"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@1" LinkObjectIDznd="259@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1447.56 917.24 L 1447.62 900.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 1597.56 917.24 L 1597.62 900.71" stroke-width="1" zvalue="425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="278@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.56 917.24 L 1597.62 900.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 1448.07 811.41 L 1448.07 824.53" stroke-width="1" zvalue="429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="109@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.07 811.41 L 1448.07 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 514.45 682.3 L 514.49 656.88" stroke-width="1" zvalue="430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="84@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.45 682.3 L 514.49 656.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv10" d="M 366.11 964.88 L 366.11 946.05 L 365.53 946.05 L 365.53 927.23" stroke-width="1" zvalue="432"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 366.11 964.88 L 366.11 946.05 L 365.53 946.05 L 365.53 927.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 366.11 964.32 L 366.11 979.71 L 365.52 979.71 L 365.52 995.09" stroke-width="1" zvalue="433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 366.11 964.32 L 366.11 979.71 L 365.52 979.71 L 365.52 995.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1143.53 927.23 L 1143.53 946.05 L 1144.11 946.05 L 1144.11 964.88" stroke-width="1" zvalue="434"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@0" LinkObjectIDznd="223@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.53 927.23 L 1143.53 946.05 L 1144.11 946.05 L 1144.11 964.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 1143.52 995.09 L 1143.52 979.33 L 1144.11 979.33 L 1144.11 963.57" stroke-width="1" zvalue="435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.52 995.09 L 1143.52 979.33 L 1144.11 979.33 L 1144.11 963.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 1161.97 945.45 L 1153.04 945.45 L 1153.04 946.05 L 1144.11 946.05" stroke-width="1" zvalue="437"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 1161.97 945.45 L 1153.04 945.45 L 1153.04 946.05 L 1144.11 946.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1598.7 841.17 L 1598.7 824.53" stroke-width="1" zvalue="438"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@0" LinkObjectIDznd="109@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1598.7 841.17 L 1598.7 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1104.65 955.1 L 1104.65 947.14 L 1144.11 947.14" stroke-width="1" zvalue="439"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 1104.65 955.1 L 1104.65 947.14 L 1144.11 947.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 871.12 731.34 L 871.12 747.67" stroke-width="1" zvalue="462"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="346@1" LinkObjectIDznd="284@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.12 731.34 L 871.12 747.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv10" d="M 870.86 784.81 L 870.79 791.92" stroke-width="1" zvalue="463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="349@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.86 784.81 L 870.79 791.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv10" d="M 514.25 721.35 L 514.25 701.82 L 514.45 701.82 L 514.45 682.3" stroke-width="1" zvalue="518"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="124@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.25 721.35 L 514.25 701.82 L 514.45 701.82 L 514.45 682.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 532.66 711.21 L 514.25 711.21" stroke-width="1" zvalue="519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="9" MaxPinNum="2"/>
   </metadata>
  <path d="M 532.66 711.21 L 514.25 711.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 1407.18 610.67 L 1407.18 591.33 L 1448.88 591.33" stroke-width="1" zvalue="521"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="107@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.18 610.67 L 1407.18 591.33 L 1448.88 591.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 1448.25 721.35 L 1448.25 683.48" stroke-width="1" zvalue="522"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.25 721.35 L 1448.25 683.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 1448.49 683.48 L 1448.49 655.37" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="107@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.49 683.48 L 1448.49 655.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 1466.11 671.48 L 1448.49 671.48" stroke-width="1" zvalue="527"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="45" MaxPinNum="2"/>
   </metadata>
  <path d="M 1466.11 671.48 L 1448.49 671.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="42">
   <path class="kv35" d="M 260 415.78 L 1688.33 415.78" stroke-width="6" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母"/>
   </metadata>
  <path d="M 260 415.78 L 1688.33 415.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 258.75 824.53 L 912.27 824.53" stroke-width="6" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母"/>
   </metadata>
  <path d="M 258.75 824.53 L 912.27 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1035.89 824.53 L 1689.42 824.53" stroke-width="6" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅱ母"/>
   </metadata>
  <path d="M 1035.89 824.53 L 1689.42 824.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="84">
   <g id="840">
    <use class="kv35" height="30" transform="rotate(0,514.49,614.333) scale(3.09833,3.02933) translate(-323.256,-381.098)" width="24" x="477.31" xlink:href="#PowerTransformer2:可调两卷变_0" y="568.89" zvalue="82"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="841">
    <use class="kv10" height="30" transform="rotate(0,514.49,614.333) scale(3.09833,3.02933) translate(-323.256,-381.098)" width="24" x="477.31" xlink:href="#PowerTransformer2:可调两卷变_1" y="568.89" zvalue="82"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,514.49,614.333) scale(3.09833,3.02933) translate(-323.256,-381.098)" width="24" x="477.31" y="568.89"/></g>
  <g id="107">
   <g id="1070">
    <use class="kv35" height="30" transform="rotate(0,1448.87,612.815) scale(3.09833,3.02933) translate(-956.058,-380.081)" width="24" x="1411.69" xlink:href="#PowerTransformer2:可调两卷变_0" y="567.38" zvalue="110"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1071">
    <use class="kv10" height="30" transform="rotate(0,1448.87,612.815) scale(3.09833,3.02933) translate(-956.058,-380.081)" width="24" x="1411.69" xlink:href="#PowerTransformer2:可调两卷变_1" y="567.38" zvalue="110"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1448.87,612.815) scale(3.09833,3.02933) translate(-956.058,-380.081)" width="24" x="1411.69" y="567.38"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="201">
   <use class="kv10" height="22" transform="rotate(0,514.232,731.057) scale(0.97164,0.97164) translate(14.6974,21.0261)" width="22" x="503.543867390293" xlink:href="#DollyBreaker:手车_0" y="720.3690454837314" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧001手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,514.232,731.057) scale(0.97164,0.97164) translate(14.6974,21.0261)" width="22" x="503.543867390293" y="720.3690454837314"/></g>
  <g id="157">
   <use class="kv10" height="22" transform="rotate(0,514.31,792.203) scale(0.97164,-0.97164) translate(14.6997,-1607.84)" width="22" x="503.6216451680708" xlink:href="#DollyBreaker:手车_0" y="781.5148165103427" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧001手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,514.31,792.203) scale(0.97164,-0.97164) translate(14.6997,-1607.84)" width="22" x="503.6216451680708" y="781.5148165103427"/></g>
  <g id="93">
   <use class="kv10" height="22" transform="rotate(0,1448.23,731.057) scale(0.97164,0.97164) translate(41.959,21.0261)" width="22" x="1437.543867390293" xlink:href="#DollyBreaker:手车_0" y="720.3690466994358" zvalue="126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变10kV侧002手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1448.23,731.057) scale(0.97164,0.97164) translate(41.959,21.0261)" width="22" x="1437.543867390293" y="720.3690466994358"/></g>
  <g id="92">
   <use class="kv10" height="22" transform="rotate(0,1448.06,801.703) scale(0.97164,-0.97164) translate(41.954,-1627.12)" width="22" x="1437.371645168071" xlink:href="#DollyBreaker:手车_0" y="791.0148168547618" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变10kV侧002手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1448.06,801.703) scale(0.97164,-0.97164) translate(41.954,-1627.12)" width="22" x="1437.371645168071" y="791.0148168547618"/></g>
  <g id="349">
   <use class="kv10" height="22" transform="rotate(0,870.737,792.203) scale(0.97164,-0.97164) translate(25.1031,-1607.84)" width="22" x="860.0489948574833" xlink:href="#DollyBreaker:手车_0" y="781.5148166152941" zvalue="158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,870.737,792.203) scale(0.97164,-0.97164) translate(25.1031,-1607.84)" width="22" x="860.0489948574833" y="781.5148166152941"/></g>
  <g id="346">
   <use class="kv10" height="22" transform="rotate(0,871.065,731.057) scale(0.97164,0.97164) translate(25.1127,21.0261)" width="22" x="860.3767726352611" xlink:href="#DollyBreaker:手车_0" y="720.3690445349937" zvalue="159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段012手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,871.065,731.057) scale(0.97164,0.97164) translate(25.1127,21.0261)" width="22" x="860.3767726352611" y="720.3690445349937"/></g>
  <g id="134">
   <use class="kv10" height="22" transform="rotate(0,365.436,848.375) scale(0.97164,0.97164) translate(10.3544,24.4504)" width="22" x="354.7484128448383" xlink:href="#DollyBreaker:手车_0" y="837.6872285176175" zvalue="178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器071手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,365.436,848.375) scale(0.97164,0.97164) translate(10.3544,24.4504)" width="22" x="354.7484128448383" y="837.6872285176175"/></g>
  <g id="133">
   <use class="kv10" height="22" transform="rotate(0,365.514,917.521) scale(0.97164,-0.97164) translate(10.3567,-1862.13)" width="22" x="354.8261906226161" xlink:href="#DollyBreaker:手车_0" y="906.8329889651772" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器071手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,365.514,917.521) scale(0.97164,-0.97164) translate(10.3567,-1862.13)" width="22" x="354.8261906226161" y="906.8329889651772"/></g>
  <g id="165">
   <use class="kv10" height="22" transform="rotate(0,511.436,848.375) scale(0.97164,0.97164) translate(14.6158,24.4504)" width="22" x="500.7484128448383" xlink:href="#DollyBreaker:手车_0" y="837.6872285176175" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线072手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,511.436,848.375) scale(0.97164,0.97164) translate(14.6158,24.4504)" width="22" x="500.7484128448383" y="837.6872285176175"/></g>
  <g id="164">
   <use class="kv10" height="22" transform="rotate(0,511.514,917.521) scale(0.97164,-0.97164) translate(14.6181,-1862.13)" width="22" x="500.8261906226161" xlink:href="#DollyBreaker:手车_0" y="906.8329889651772" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线072手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,511.514,917.521) scale(0.97164,-0.97164) translate(14.6181,-1862.13)" width="22" x="500.8261906226161" y="906.8329889651772"/></g>
  <g id="227">
   <use class="kv10" height="22" transform="rotate(0,1143.44,849.625) scale(0.97164,0.97164) translate(33.0627,24.4869)" width="22" x="1132.748412844838" xlink:href="#DollyBreaker:手车_0" y="838.9372285176175" zvalue="261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV2号电容器074手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1143.44,849.625) scale(0.97164,0.97164) translate(33.0627,24.4869)" width="22" x="1132.748412844838" y="838.9372285176175"/></g>
  <g id="226">
   <use class="kv10" height="22" transform="rotate(0,1143.51,917.521) scale(0.97164,-0.97164) translate(33.0649,-1862.13)" width="22" x="1132.826190622616" xlink:href="#DollyBreaker:手车_0" y="906.8329889651772" zvalue="262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV2号电容器074手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1143.51,917.521) scale(0.97164,-0.97164) translate(33.0649,-1862.13)" width="22" x="1132.826190622616" y="906.8329889651772"/></g>
  <g id="258">
   <use class="kv10" height="22" transform="rotate(0,1448.69,850.875) scale(0.97164,0.97164) translate(41.9723,24.5234)" width="22" x="1437.998412844838" xlink:href="#DollyBreaker:手车_0" y="840.1872285176175" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#3出线075手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1448.69,850.875) scale(0.97164,0.97164) translate(41.9723,24.5234)" width="22" x="1437.998412844838" y="840.1872285176175"/></g>
  <g id="257">
   <use class="kv10" height="22" transform="rotate(0,1447.51,917.521) scale(0.97164,-0.97164) translate(41.9381,-1862.13)" width="22" x="1436.826190622616" xlink:href="#DollyBreaker:手车_0" y="906.8329889651772" zvalue="302"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#3出线075手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1447.51,917.521) scale(0.97164,-0.97164) translate(41.9381,-1862.13)" width="22" x="1436.826190622616" y="906.8329889651772"/></g>
  <g id="277">
   <use class="kv10" height="22" transform="rotate(0,1598.69,850.875) scale(0.97164,0.97164) translate(46.3505,24.5234)" width="22" x="1587.998412844838" xlink:href="#DollyBreaker:手车_0" y="840.1872285176175" zvalue="320"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#4出线076手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1598.69,850.875) scale(0.97164,0.97164) translate(46.3505,24.5234)" width="22" x="1587.998412844838" y="840.1872285176175"/></g>
  <g id="276">
   <use class="kv10" height="22" transform="rotate(0,1597.51,917.521) scale(0.97164,-0.97164) translate(46.3163,-1862.13)" width="22" x="1586.826190622616" xlink:href="#DollyBreaker:手车_0" y="906.8329889651772" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#4出线076手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1597.51,917.521) scale(0.97164,-0.97164) translate(46.3163,-1862.13)" width="22" x="1586.826190622616" y="906.8329889651772"/></g>
  <g id="144">
   <use class="kv10" height="22" transform="rotate(0,663.77,849.625) scale(0.97164,0.97164) translate(19.0621,24.4869)" width="22" x="653.0817461781717" xlink:href="#DollyBreaker:手车_0" y="838.9372285176175" zvalue="377"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2站用变073手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,663.77,849.625) scale(0.97164,0.97164) translate(19.0621,24.4869)" width="22" x="653.0817461781717" y="838.9372285176175"/></g>
  <g id="130">
   <use class="kv10" height="22" transform="rotate(0,663.848,917.521) scale(0.97164,-0.97164) translate(19.0644,-1862.13)" width="22" x="653.1595239559495" xlink:href="#DollyBreaker:手车_0" y="906.8329889651772" zvalue="378"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2站用变073手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,663.848,917.521) scale(0.97164,-0.97164) translate(19.0644,-1862.13)" width="22" x="653.1595239559495" y="906.8329889651772"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="147">
   <use class="kv10" height="30" transform="rotate(180,365.493,1064.74) scale(0.78125,0.770833) translate(101.026,313.108)" width="12" x="360.8054688903439" xlink:href="#EnergyConsumer:负荷_0" y="1053.179779052734" zvalue="196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,365.493,1064.74) scale(0.78125,0.770833) translate(101.026,313.108)" width="12" x="360.8054688903439" y="1053.179779052734"/></g>
  <g id="169">
   <use class="kv10" height="30" transform="rotate(180,511.493,1064.74) scale(0.78125,0.770833) translate(141.906,313.108)" width="12" x="506.8054688903439" xlink:href="#EnergyConsumer:负荷_0" y="1053.179779052734" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,511.493,1064.74) scale(0.78125,0.770833) translate(141.906,313.108)" width="12" x="506.8054688903439" y="1053.179779052734"/></g>
  <g id="215">
   <use class="kv10" height="30" transform="rotate(180,1143.49,1064.74) scale(0.78125,0.770833) translate(318.866,313.108)" width="12" x="1138.805468890344" xlink:href="#EnergyConsumer:负荷_0" y="1053.179779052734" zvalue="275"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV2号电容器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1143.49,1064.74) scale(0.78125,0.770833) translate(318.866,313.108)" width="12" x="1138.805468890344" y="1053.179779052734"/></g>
  <g id="253">
   <use class="kv10" height="30" transform="rotate(180,1447.49,1064.74) scale(0.78125,0.770833) translate(403.986,313.108)" width="12" x="1442.805468890344" xlink:href="#EnergyConsumer:负荷_0" y="1053.179779052734" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#3出线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1447.49,1064.74) scale(0.78125,0.770833) translate(403.986,313.108)" width="12" x="1442.805468890344" y="1053.179779052734"/></g>
  <g id="272">
   <use class="kv10" height="30" transform="rotate(180,1597.49,1064.74) scale(0.78125,0.770833) translate(445.986,313.108)" width="12" x="1592.805468890344" xlink:href="#EnergyConsumer:负荷_0" y="1053.179779052734" zvalue="325"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#4出线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1597.49,1064.74) scale(0.78125,0.770833) translate(445.986,313.108)" width="12" x="1592.805468890344" y="1053.179779052734"/></g>
  <g id="331">
   <use class="kv10" height="30" transform="rotate(0,663.75,1043.75) scale(1.69643,1.70833) translate(-262.737,-422.149)" width="28" x="640" xlink:href="#EnergyConsumer:站用变DY接地_0" y="1018.125" zvalue="507"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,663.75,1043.75) scale(1.69643,1.70833) translate(-262.737,-422.149)" width="28" x="640" y="1018.125"/></g>
  <g id="337">
   <use class="kv35" height="30" transform="rotate(0,760.73,311.75) scale(1.69643,1.70833) translate(-302.55,-118.637)" width="28" x="736.9801709188301" xlink:href="#EnergyConsumer:站用变DY接地_0" y="286.125" zvalue="513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,760.73,311.75) scale(1.69643,1.70833) translate(-302.55,-118.637)" width="28" x="736.9801709188301" y="286.125"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="12">
   <use class="kv35" height="30" transform="rotate(0,965.682,117.51) scale(1.99975,1.15097) translate(-479.281,-13.1487)" width="7" x="958.6825538961693" xlink:href="#ACLineSegment:线路_0" y="100.2456823164824" zvalue="358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV龙长线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,965.682,117.51) scale(1.99975,1.15097) translate(-479.281,-13.1487)" width="7" x="958.6825538961693" y="100.2456823164824"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="113" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,963.682,32.9957) scale(1,1) translate(0,0)" writing-mode="lr" x="963.3" xml:space="preserve" y="37.77" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="123" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,963.682,46.6672) scale(1,1) translate(0,0)" writing-mode="lr" x="963.3" xml:space="preserve" y="51.44" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="139" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,963.682,60.3387) scale(1,1) translate(0,0)" writing-mode="lr" x="963.3" xml:space="preserve" y="65.12" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.682,74.0102) scale(1,1) translate(0,0)" writing-mode="lr" x="963.3" xml:space="preserve" y="78.79000000000001" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="176">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="176" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,271.417,328.778) scale(1,1) translate(0,0)" writing-mode="lr" x="271.04" xml:space="preserve" y="333.56" zvalue="1">Ua:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="177">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="177" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,271.417,341.215) scale(1,1) translate(0,0)" writing-mode="lr" x="271.04" xml:space="preserve" y="345.99" zvalue="1">Ub:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="180">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="180" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,271.417,353.653) scale(1,1) translate(0,-2.98564e-13)" writing-mode="lr" x="271.04" xml:space="preserve" y="358.43" zvalue="1">Uc:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,271.417,366.09) scale(1,1) translate(0,0)" writing-mode="lr" x="271.04" xml:space="preserve" y="370.87" zvalue="1">Uab:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="199" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,271.417,378.528) scale(1,1) translate(0,0)" writing-mode="lr" x="271.04" xml:space="preserve" y="383.31" zvalue="1">U0:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,255.012,742.028) scale(1,1) translate(0,0)" writing-mode="lr" x="254.63" xml:space="preserve" y="746.8099999999999" zvalue="1">Ua:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="202" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,255.012,754.403) scale(1,1) translate(0,0)" writing-mode="lr" x="254.63" xml:space="preserve" y="759.1799999999999" zvalue="1">Ub:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="205" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,255.012,766.778) scale(1,1) translate(0,0)" writing-mode="lr" x="254.63" xml:space="preserve" y="771.5599999999999" zvalue="1">Uc:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="212" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,255.012,779.153) scale(1,1) translate(0,0)" writing-mode="lr" x="254.63" xml:space="preserve" y="783.9299999999999" zvalue="1">Uab:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="242" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,255.012,791.528) scale(1,1) translate(0,0)" writing-mode="lr" x="254.63" xml:space="preserve" y="796.3099999999999" zvalue="1">U0:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="243" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1651.64,742.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1651.26" xml:space="preserve" y="746.8099999999999" zvalue="1">Ua:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="247" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1651.64,754.403) scale(1,1) translate(0,0)" writing-mode="lr" x="1651.26" xml:space="preserve" y="759.1799999999999" zvalue="1">Ub:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="265">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="265" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1651.64,766.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1651.26" xml:space="preserve" y="771.5599999999999" zvalue="1">Uc:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="271">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="271" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1651.64,779.153) scale(1,1) translate(0,0)" writing-mode="lr" x="1651.26" xml:space="preserve" y="783.9299999999999" zvalue="1">Uab:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="283">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="283" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1651.64,791.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1651.26" xml:space="preserve" y="796.3099999999999" zvalue="1">U0:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="288" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,810.931,746.332) scale(1,1) translate(0,0)" writing-mode="lr" x="810.55" xml:space="preserve" y="751.11" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="289">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="289" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,810.931,761.957) scale(1,1) translate(0,0)" writing-mode="lr" x="810.55" xml:space="preserve" y="766.73" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="290">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="290" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,810.931,777.582) scale(1,1) translate(0,0)" writing-mode="lr" x="810.55" xml:space="preserve" y="782.36" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="291">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="291" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,369.743,1115.8) scale(1,1) translate(0,-1.70711e-12)" writing-mode="lr" x="369.38" xml:space="preserve" y="1120.58" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="292">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="292" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,369.743,1129.35) scale(1,1) translate(0,0)" writing-mode="lr" x="369.38" xml:space="preserve" y="1134.12" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="293">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="293" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,369.743,1142.89) scale(1,1) translate(0,0)" writing-mode="lr" x="369.38" xml:space="preserve" y="1147.67" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="294">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,369.743,1156.43) scale(1,1) translate(0,0)" writing-mode="lr" x="369.36" xml:space="preserve" y="1161.21" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="295">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="295" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,511.993,1115.8) scale(1,1) translate(0,-1.70711e-12)" writing-mode="lr" x="511.63" xml:space="preserve" y="1120.58" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="296" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,511.993,1129.35) scale(1,1) translate(0,0)" writing-mode="lr" x="511.63" xml:space="preserve" y="1134.12" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="297">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="297" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,511.993,1142.89) scale(1,1) translate(0,0)" writing-mode="lr" x="511.63" xml:space="preserve" y="1147.67" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="298" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,511.993,1156.43) scale(1,1) translate(0,0)" writing-mode="lr" x="511.61" xml:space="preserve" y="1161.21" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="299">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="299" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1134.74,1115.8) scale(1,1) translate(0,-1.70711e-12)" writing-mode="lr" x="1134.38" xml:space="preserve" y="1120.58" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="300" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1134.74,1129.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1134.38" xml:space="preserve" y="1134.12" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="301">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="301" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1134.74,1142.89) scale(1,1) translate(0,0)" writing-mode="lr" x="1134.38" xml:space="preserve" y="1147.67" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="302">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1134.74,1156.43) scale(1,1) translate(0,0)" writing-mode="lr" x="1134.36" xml:space="preserve" y="1161.21" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="303">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="303" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1447.99,1115.8) scale(1,1) translate(-1.25499e-12,-1.70711e-12)" writing-mode="lr" x="1447.63" xml:space="preserve" y="1120.58" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="304">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="304" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1447.99,1129.35) scale(1,1) translate(-1.25499e-12,0)" writing-mode="lr" x="1447.63" xml:space="preserve" y="1134.12" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="305">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="305" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1447.99,1142.89) scale(1,1) translate(-1.25499e-12,0)" writing-mode="lr" x="1447.63" xml:space="preserve" y="1147.67" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="306">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="306" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1447.99,1156.43) scale(1,1) translate(1.09695e-12,0)" writing-mode="lr" x="1447.61" xml:space="preserve" y="1161.21" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="307">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="307" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1597.99,1115.8) scale(1,1) translate(0,-1.70711e-12)" writing-mode="lr" x="1597.63" xml:space="preserve" y="1120.58" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="308">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="308" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1597.99,1129.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.63" xml:space="preserve" y="1134.12" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="309">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="309" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1597.99,1142.89) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.63" xml:space="preserve" y="1147.67" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="310">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1597.99,1156.43) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.61" xml:space="preserve" y="1161.21" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="311">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="311" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,597.49,478.393) scale(1,1) translate(0,-2.04678e-13)" writing-mode="lr" x="597.05" xml:space="preserve" y="484.67" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="312">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="312" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,597.49,495.477) scale(1,1) translate(0,0)" writing-mode="lr" x="597.05" xml:space="preserve" y="501.75" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="313">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="313" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,597.49,745.523) scale(1,1) translate(0,0)" writing-mode="lr" x="597.05" xml:space="preserve" y="751.8" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="314">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="314" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,597.49,763.127) scale(1,1) translate(0,1.65562e-13)" writing-mode="lr" x="597.05" xml:space="preserve" y="769.4" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="315">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="315" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,597.49,512.56) scale(1,1) translate(0,0)" writing-mode="lr" x="597.05" xml:space="preserve" y="518.84" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="316">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="316" prefix=" 油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,426.17,638.333) scale(1,1) translate(0,0)" writing-mode="lr" x="425.73" xml:space="preserve" y="644.61" zvalue="1"> 油温:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="317">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="317" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,419.17,615.083) scale(1,1) translate(0,0)" writing-mode="lr" x="418.81" xml:space="preserve" y="621.36" zvalue="1">档位:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="318">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="318" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,597.49,780.73) scale(1,1) translate(0,0)" writing-mode="lr" x="597.05" xml:space="preserve" y="787.01" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="319">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="319" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,597.49,529.643) scale(1,1) translate(0,0)" writing-mode="lr" x="597.05" xml:space="preserve" y="535.92" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="320">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="320" prefix="Cos:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,597.49,798.333) scale(1,1) translate(0,0)" writing-mode="lr" x="597.08" xml:space="preserve" y="804.11" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LCos"/>
   </metadata>
  </g>
  <g id="321">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="321" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1535.12,491.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1534.67" xml:space="preserve" y="498.15" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="322">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="322" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1535.12,507.708) scale(1,1) translate(0,0)" writing-mode="lr" x="1534.67" xml:space="preserve" y="513.99" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="323">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="323" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1536.37,732.755) scale(1,1) translate(0,0)" writing-mode="lr" x="1535.92" xml:space="preserve" y="739.03" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="324">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="324" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1536.37,749.942) scale(1,1) translate(0,1.62635e-13)" writing-mode="lr" x="1535.92" xml:space="preserve" y="756.22" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="325">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="325" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1535.12,523.542) scale(1,1) translate(0,0)" writing-mode="lr" x="1534.67" xml:space="preserve" y="529.8200000000001" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="326">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="326" prefix=" 油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1354.3,613.065) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.85" xml:space="preserve" y="619.34" zvalue="1"> 油温:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="327">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="327" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350.94,589.815) scale(1,1) translate(0,0)" writing-mode="lr" x="1350.57" xml:space="preserve" y="596.09" zvalue="1">档位:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="328">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="328" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1536.37,767.128) scale(1,1) translate(0,0)" writing-mode="lr" x="1535.92" xml:space="preserve" y="773.41" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="329">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="329" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1533.87,536.875) scale(1,1) translate(0,-1.15324e-13)" writing-mode="lr" x="1533.43" xml:space="preserve" y="543.15" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="330">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="330" prefix="Cos:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1535.12,785.565) scale(1,1) translate(0,0)" writing-mode="lr" x="1534.7" xml:space="preserve" y="791.34" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LCos"/>
   </metadata>
  </g>
  <g id="333">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="333" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,660.25,1115.8) scale(1,1) translate(0,0)" writing-mode="lr" x="659.8099999999999" xml:space="preserve" y="1122.08" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="334">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="334" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,660.25,1129.35) scale(1,1) translate(0,0)" writing-mode="lr" x="659.8099999999999" xml:space="preserve" y="1135.62" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="335">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="335" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,660.25,1142.89) scale(1,1) translate(0,0)" writing-mode="lr" x="659.8099999999999" xml:space="preserve" y="1149.17" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="336">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="336" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,660.25,1156.43) scale(1,1) translate(0,0)" writing-mode="lr" x="659.8099999999999" xml:space="preserve" y="1162.71" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="339">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="339" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,687.348,285.022) scale(1,1) translate(0,0)" writing-mode="lr" x="687.09" xml:space="preserve" y="291.18" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="340">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="340" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,687.348,296.238) scale(1,1) translate(0,0)" writing-mode="lr" x="687.09" xml:space="preserve" y="302.4" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="341">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="341" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,687.348,307.453) scale(1,1) translate(0,0)" writing-mode="lr" x="687.09" xml:space="preserve" y="313.61" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="343">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="343" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.348,318.669) scale(1,1) translate(0,0)" writing-mode="lr" x="687.09" xml:space="preserve" y="324.83" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
 </g>
</svg>