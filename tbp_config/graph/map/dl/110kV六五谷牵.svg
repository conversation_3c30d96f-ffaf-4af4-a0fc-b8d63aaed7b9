<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549585117186" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:PT1_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9.15" xlink:href="#terminal" y="1.15"/>
   <ellipse cx="9.23" cy="6.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.12" cy="11.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.9" cy="11.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:V-V不带接地点_0" viewBox="0,0,35,60">
   <ellipse cx="17.7" cy="18.72" fill-opacity="0" rx="16" ry="16.21" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:V-V不带接地点_1" viewBox="0,0,35,60">
   <ellipse cx="17.7" cy="40.19" fill-opacity="0" rx="15.9" ry="16.11" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:不接地中性点_0" viewBox="0,0,40,60">
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.99646006153215" x2="19.99646006153215" y1="8.182580361835289" y2="15.25248792266316"/>
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="2.945891576747027"/>
   <use terminal-index="0" type="2" x="20" xlink:href="#terminal" y="15.57114217426508"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.90960970659896" x2="11.31783141058307" y1="15.33688141451502" y2="22.18353544410765"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.18564147036446" x2="29.87962002224004" y1="15.42995433372238" y2="22.71860379120086"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.63126264454616" x2="30.82164336930119" y1="3.54709398615265" y2="4.749498804963892"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.63126264454616" x2="34.42885782573492" y1="3.54709398615265" y2="7.154308442586377"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.166332536642596" x2="35.63126264454616" y1="28.79759518118876" y2="3.54709398615265"/>
   <ellipse cx="20.05" cy="18.53" fill-opacity="0" rx="15.38" ry="15.81" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV六五谷牵" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="35" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,156.5,85.5) scale(1,1) translate(0,0)" writing-mode="lr" x="156.5" xml:space="preserve" y="98" zvalue="3424">六五谷牵</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.177,126.842) scale(1,1) translate(0,0)" writing-mode="lr" x="483.18" xml:space="preserve" y="132.84" zvalue="2729">14167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595.276,156.852) scale(1,1) translate(0,0)" writing-mode="lr" x="595.28" xml:space="preserve" y="162.85" zvalue="2738">1416</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,556.82,40.7458) scale(1,1) translate(0,0)" writing-mode="lr" x="556.8200000000001" xml:space="preserve" y="46.75" zvalue="2745">110KV丁六山线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,376.554,284.43) scale(1,1) translate(0,0)" writing-mode="lr" x="376.55" xml:space="preserve" y="290.43" zvalue="3427">101BL</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,297.747,284.737) scale(1,1) translate(0,0)" writing-mode="lr" x="297.75" xml:space="preserve" y="290.74" zvalue="3429">101YH</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,494.683,243.591) scale(1,1) translate(0,0)" writing-mode="lr" x="494.68" xml:space="preserve" y="249.59" zvalue="3431">1012D</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,472.581,167.326) scale(1,1) translate(0,0)" writing-mode="lr" x="472.58" xml:space="preserve" y="173.33" zvalue="3433">1012GK</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,633.982,214.744) scale(1,1) translate(0,0)" writing-mode="lr" x="633.98" xml:space="preserve" y="220.74" zvalue="3435">1121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,500.381,275.812) scale(1,1) translate(0,0)" writing-mode="lr" x="500.38" xml:space="preserve" y="281.81" zvalue="3437">1013GK</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,636.865,280.962) scale(1,1) translate(0,0)" writing-mode="lr" x="636.87" xml:space="preserve" y="286.96" zvalue="3439">1013D</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,514.399,353.56) scale(1,1) translate(0,0)" writing-mode="lr" x="514.4" xml:space="preserve" y="359.56" zvalue="3441">101DL</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="97" stroke="rgb(255,255,255)" text-anchor="middle" x="660.171875" xml:space="preserve" y="453.7656249999999" zvalue="3456">1B  </text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="97" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="660.171875" xml:space="preserve" y="477.7656249999999" zvalue="3456">12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1095.89,132.192) scale(1,1) translate(0,0)" writing-mode="lr" x="1095.89" xml:space="preserve" y="138.19" zvalue="3459">14267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,985.645,161.019) scale(1,1) translate(0,0)" writing-mode="lr" x="985.65" xml:space="preserve" y="167.02" zvalue="3461">1426</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.855,29.5663) scale(1,1) translate(0,0)" writing-mode="lr" x="998.8548851078866" xml:space="preserve" y="35.56626416638551" zvalue="3463">110KV丁南六线 六五谷牵变支线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1203.16,283.567) scale(1,1) translate(0,0)" writing-mode="lr" x="1203.16" xml:space="preserve" y="289.57" zvalue="3467">102BL</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.34,283.862) scale(1,1) translate(0,0)" writing-mode="lr" x="1259.34" xml:space="preserve" y="289.86" zvalue="3469">102YH</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1074.41,244.338) scale(1,1) translate(0,0)" writing-mode="lr" x="1074.41" xml:space="preserve" y="250.34" zvalue="3471">1022D</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1091.46,167.776) scale(1,1) translate(0,0)" writing-mode="lr" x="1091.46" xml:space="preserve" y="173.78" zvalue="3473">1022GK</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.107,213.834) scale(1,1) translate(0,0)" writing-mode="lr" x="945.11" xml:space="preserve" y="219.83" zvalue="3475">1122</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1073.59,271.289) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.59" xml:space="preserve" y="277.29" zvalue="3477">1023GK</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.976,280.236) scale(1,1) translate(0,0)" writing-mode="lr" x="935.98" xml:space="preserve" y="286.24" zvalue="3479">1023D</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1058.8,349.972) scale(1,1) translate(0,0)" writing-mode="lr" x="1058.8" xml:space="preserve" y="355.97" zvalue="3482">102DL</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="115" stroke="rgb(255,255,255)" text-anchor="middle" x="915.375" xml:space="preserve" y="448.390625" zvalue="3492">2B  </text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="115" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="915.375" xml:space="preserve" y="472.390625" zvalue="3492">12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="369" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1371.96,235.059) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.96" xml:space="preserve" y="241.06" zvalue="3498">1041GK</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="375" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1429.43,280.492) scale(1,1) translate(0,0)" writing-mode="lr" x="1429.43" xml:space="preserve" y="286.49" zvalue="3500">1041D</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="377" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1372.01,326.792) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.01" xml:space="preserve" y="332.79" zvalue="3502">104DL</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="7" stroke="rgb(255,255,255)" text-anchor="middle" x="1326.6953125" xml:space="preserve" y="522.5" zvalue="3507">1DLB </text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="7" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1326.6953125" xml:space="preserve" y="546.5" zvalue="3507">3.15MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1509.25,428) scale(1,1) translate(0,0)" writing-mode="lr" x="1509.25" xml:space="preserve" y="434" zvalue="3510">1042GK</text>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="616">
   <use class="kv110" height="30" transform="rotate(90,527.701,128.324) scale(-0.937881,0.937881) translate(-1090.73,7.5676)" width="12" x="522.0735264892492" xlink:href="#GroundDisconnector:地刀12_0" y="114.256019810187" zvalue="2728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449989967877" ObjectName="110KV丁六山线14167"/>
   <cge:TPSR_Ref TObjectID="6192449989967877"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,527.701,128.324) scale(-0.937881,0.937881) translate(-1090.73,7.5676)" width="12" x="522.0735264892492" y="114.256019810187"/></g>
  <g id="63">
   <use class="kv110" height="30" transform="rotate(0,443.687,237.248) scale(-1.33761,1.33761) translate(-773.361,-54.8172)" width="12" x="435.6610487475326" xlink:href="#GroundDisconnector:地刀12_0" y="217.1840103073629" zvalue="3430"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁六山线1012D"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,443.687,237.248) scale(-1.33761,1.33761) translate(-773.361,-54.8172)" width="12" x="435.6610487475326" y="217.1840103073629"/></g>
  <g id="98">
   <use class="kv110" height="30" transform="rotate(270,628.23,311.096) scale(-1.18408,1.18408) translate(-1157.69,-45.6032)" width="12" x="621.1259089229713" xlink:href="#GroundDisconnector:地刀12_0" y="293.3351449489334" zvalue="3438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁六山线1013D"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,628.23,311.096) scale(-1.18408,1.18408) translate(-1157.69,-45.6032)" width="12" x="621.1259089229713" y="293.3351449489334"/></g>
  <g id="364">
   <use class="kv110" height="30" transform="rotate(270,1046.02,133.616) scale(0.900905,0.900905) translate(114.462,13.2105)" width="12" x="1040.61288570312" xlink:href="#GroundDisconnector:地刀12_0" y="120.1019689495147" zvalue="3458"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线14267"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1046.02,133.616) scale(0.900905,0.900905) translate(114.462,13.2105)" width="12" x="1040.61288570312" y="120.1019689495147"/></g>
  <g id="357">
   <use class="kv110" height="30" transform="rotate(0,1126.72,238.245) scale(1.28488,1.28488) translate(-248.103,-48.5497)" width="12" x="1119.010941595375" xlink:href="#GroundDisconnector:地刀12_0" y="218.9721088483365" zvalue="3470"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线1022D"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1126.72,238.245) scale(1.28488,1.28488) translate(-248.103,-48.5497)" width="12" x="1119.010941595375" y="218.9721088483365"/></g>
  <g id="348">
   <use class="kv110" height="30" transform="rotate(90,949.452,309.182) scale(1.1374,1.1374) translate(-113.872,-35.289)" width="12" x="942.6276120062503" xlink:href="#GroundDisconnector:地刀12_0" y="292.1210482097126" zvalue="3478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线1023D"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,949.452,309.182) scale(1.1374,1.1374) translate(-113.872,-35.289)" width="12" x="942.6276120062503" y="292.1210482097126"/></g>
  <g id="374">
   <use class="kv110" height="30" transform="rotate(270,1379.23,284.096) scale(-1.18408,1.18408) translate(-2542.94,-41.4056)" width="12" x="1372.125908922971" xlink:href="#GroundDisconnector:地刀12_0" y="266.3351449489334" zvalue="3499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁六山线1041D"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1379.23,284.096) scale(-1.18408,1.18408) translate(-2542.94,-41.4056)" width="12" x="1372.125908922971" y="266.3351449489334"/></g>
  <g id="9">
   <use class="kv110" height="40" transform="rotate(0,1426,432) scale(1.725,-1.725) translate(-584.833,-667.935)" width="40" x="1391.5" xlink:href="#GroundDisconnector:中性点地刀12_0" y="397.5" zvalue="3509"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1DLB1042GK"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1426,432) scale(1.725,-1.725) translate(-584.833,-667.935)" width="40" x="1391.5" y="397.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="600">
   <use class="kv110" height="30" transform="rotate(0,555.375,156.545) scale(-1.02105,0.748769) translate(-1099.14,48.7564)" width="15" x="547.7169358330572" xlink:href="#Disconnector:刀闸_0" y="145.3133892219913" zvalue="2737"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449989509125" ObjectName="110KV丁六山线1416"/>
   <cge:TPSR_Ref TObjectID="6192449989509125"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,555.375,156.545) scale(-1.02105,0.748769) translate(-1099.14,48.7564)" width="15" x="547.7169358330572" y="145.3133892219913"/></g>
  <g id="67">
   <use class="kv110" height="30" transform="rotate(270,483.751,191.277) scale(-1.02105,-0.748769) translate(-957.372,-450.501)" width="15" x="476.0928996735351" xlink:href="#Disconnector:刀闸_0" y="180.0454724766279" zvalue="3432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁六山线1012GK"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,483.751,191.277) scale(-1.02105,-0.748769) translate(-957.372,-450.501)" width="15" x="476.0928996735351" y="180.0454724766279"/></g>
  <g id="78">
   <use class="kv110" height="30" transform="rotate(270,631.526,191.304) scale(-1.02105,-0.748769) translate(-1249.88,-450.564)" width="15" x="623.868070474628" xlink:href="#Disconnector:刀闸_0" y="180.0726452460474" zvalue="3434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁六山线1121"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,631.526,191.304) scale(-1.02105,-0.748769) translate(-1249.88,-450.564)" width="15" x="623.868070474628" y="180.0726452460474"/></g>
  <g id="85">
   <use class="kv110" height="30" transform="rotate(0,555.846,277.655) scale(-1.36762,-1.00292) translate(-959.521,-554.456)" width="15" x="545.5888963349619" xlink:href="#Disconnector:刀闸_0" y="262.610701452154" zvalue="3436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁六山线1013GK"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,555.846,277.655) scale(-1.36762,-1.00292) translate(-959.521,-554.456)" width="15" x="545.5888963349619" y="262.610701452154"/></g>
  <g id="363">
   <use class="kv110" height="30" transform="rotate(0,1019.44,160.724) scale(0.980794,0.719249) translate(19.8186,58.5255)" width="15" x="1012.07940194663" xlink:href="#Disconnector:刀闸_0" y="149.9349273488995" zvalue="3460"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线1426"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1019.44,160.724) scale(0.980794,0.719249) translate(19.8186,58.5255)" width="15" x="1012.07940194663" y="149.9349273488995"/></g>
  <g id="356">
   <use class="kv110" height="30" transform="rotate(90,1087.28,190.783) scale(0.980794,-0.719249) translate(21.147,-460.247)" width="15" x="1079.919144096293" xlink:href="#Disconnector:刀闸_0" y="179.9942767097344" zvalue="3472"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线1022GK"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1087.28,190.783) scale(0.980794,-0.719249) translate(21.147,-460.247)" width="15" x="1079.919144096293" y="179.9942767097344"/></g>
  <g id="355">
   <use class="kv110" height="30" transform="rotate(90,946.286,190.809) scale(0.980794,-0.719249) translate(18.3862,-460.31)" width="15" x="938.9304625852542" xlink:href="#Disconnector:刀闸_0" y="180.0203782152711" zvalue="3474"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线1122"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,946.286,190.809) scale(0.980794,-0.719249) translate(18.3862,-460.31)" width="15" x="938.9304625852542" y="180.0203782152711"/></g>
  <g id="354">
   <use class="kv110" height="30" transform="rotate(0,1018.98,273.059) scale(1.31371,-0.963385) translate(-240.976,-557.045)" width="15" x="1009.129847940123" xlink:href="#Disconnector:刀闸_0" y="258.6078903974712" zvalue="3476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线1023GK"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1018.98,273.059) scale(1.31371,-0.963385) translate(-240.976,-557.045)" width="15" x="1009.129847940123" y="258.6078903974712"/></g>
  <g id="368">
   <use class="kv110" height="30" transform="rotate(0,1323.98,236.059) scale(1.31371,-0.963385) translate(-313.808,-481.638)" width="15" x="1314.129847940123" xlink:href="#Disconnector:刀闸_0" y="221.6078903974712" zvalue="3497"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线1041GK"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1323.98,236.059) scale(1.31371,-0.963385) translate(-313.808,-481.638)" width="15" x="1314.129847940123" y="221.6078903974712"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="182">
   <use class="kv110" height="30" transform="rotate(0,556.82,67.4942) scale(2.53447,0.682358) translate(-331.751,26.6544)" width="7" x="547.9498245268628" xlink:href="#ACLineSegment:线路_0" y="57.25882918477919" zvalue="2744"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449989443589" ObjectName="110KV丁六山线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,556.82,67.4942) scale(2.53447,0.682358) translate(-331.751,26.6544)" width="7" x="547.9498245268628" y="57.25882918477919"/></g>
  <g id="362">
   <use class="kv110" height="30" transform="rotate(0,1018.05,75.1837) scale(-2.43455,0.655456) translate(-1431.19,34.3525)" width="7" x="1009.525737605996" xlink:href="#ACLineSegment:线路_0" y="65.3518452586369" zvalue="3462"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1018.05,75.1837) scale(-2.43455,0.655456) translate(-1431.19,34.3525)" width="7" x="1009.525737605996" y="65.3518452586369"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="225">
   <path class="kv110" d="M 555.29 145.68 L 555.29 77.63" stroke-width="1" zvalue="2753"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="600@0" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 555.29 145.68 L 555.29 77.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv110" d="M 541.13 128.33 L 555.29 128.33" stroke-width="1" zvalue="2757"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="616@0" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 541.13 128.33 L 555.29 128.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv110" d="M 555.31 167.58 L 555.31 262.87" stroke-width="1" zvalue="3441"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="600@1" LinkObjectIDznd="85@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 555.31 167.58 L 555.31 262.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv110" d="M 620.49 191.37 L 555.31 191.37" stroke-width="1" zvalue="3442"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@1" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 620.49 191.37 L 555.31 191.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv110" d="M 494.61 191.37 L 555.31 191.37" stroke-width="1" zvalue="3443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 494.61 191.37 L 555.31 191.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv110" d="M 472.71 191.34 L 341.09 191.34 L 341.09 215.34 L 367.84 215.34 L 367.84 235.53" stroke-width="1" zvalue="3444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="18@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 472.71 191.34 L 341.09 191.34 L 341.09 215.34 L 367.84 215.34 L 367.84 235.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv110" d="M 342.54 215.34 L 313.52 215.34 L 313.52 227.22" stroke-width="1" zvalue="3445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 342.54 215.34 L 313.52 215.34 L 313.52 227.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv110" d="M 443.7 218.09 L 443.7 191.34" stroke-width="1" zvalue="3446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 443.7 218.09 L 443.7 191.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv110" d="M 555.73 292.2 L 555.73 343.25" stroke-width="1" zvalue="3447"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 555.73 292.2 L 555.73 343.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv110" d="M 611.27 311.08 L 555.73 311.08" stroke-width="1" zvalue="3448"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="174" MaxPinNum="2"/>
   </metadata>
  <path d="M 611.27 311.08 L 555.73 311.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="361">
   <path class="kv110" d="M 1019.52 150.29 L 1019.52 84.92" stroke-width="1" zvalue="3464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="363@0" LinkObjectIDznd="362@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.52 150.29 L 1019.52 84.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="360">
   <path class="kv110" d="M 1033.11 133.63 L 1019.52 133.63" stroke-width="1" zvalue="3465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="364@0" LinkObjectIDznd="361" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.11 133.63 L 1019.52 133.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="345">
   <path class="kv110" d="M 1019.5 171.33 L 1019.5 258.85" stroke-width="1" zvalue="3481"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="363@1" LinkObjectIDznd="354@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.5 171.33 L 1019.5 258.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="344">
   <path class="kv110" d="M 956.89 190.87 L 1019.59 190.87" stroke-width="1" zvalue="3483"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="355@1" LinkObjectIDznd="343" MaxPinNum="2"/>
   </metadata>
  <path d="M 956.89 190.87 L 1019.59 190.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="343">
   <path class="kv110" d="M 1076.84 190.87 L 1019.5 190.87" stroke-width="1" zvalue="3484"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="356@0" LinkObjectIDznd="345" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.84 190.87 L 1019.5 190.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="342">
   <path class="kv110" d="M 1097.88 190.84 L 1225.28 190.84 L 1225.28 217.2 L 1199.58 217.2 L 1199.58 236.6" stroke-width="1" zvalue="3485"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="356@1" LinkObjectIDznd="359@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.88 190.84 L 1225.28 190.84 L 1225.28 217.2 L 1199.58 217.2 L 1199.58 236.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="340">
   <path class="kv110" d="M 1224.1 217.2 L 1251.75 217.2 L 1251.75 228.61" stroke-width="1" zvalue="3486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342" LinkObjectIDznd="358@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1224.1 217.2 L 1251.75 217.2 L 1251.75 228.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv110" d="M 1126.71 219.84 L 1126.71 190.84" stroke-width="1" zvalue="3487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="357@0" LinkObjectIDznd="342" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.71 219.84 L 1126.71 190.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="308">
   <path class="kv110" d="M 1019.1 287.03 L 1019.1 340.06" stroke-width="1" zvalue="3488"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="354@0" LinkObjectIDznd="347@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.1 287.03 L 1019.1 340.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv110" d="M 965.74 309.17 L 1019.1 309.17" stroke-width="1" zvalue="3489"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="348@0" LinkObjectIDznd="308" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.74 309.17 L 1019.1 309.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv110" d="M 1018.25 382.21 L 1018.25 357.5" stroke-width="1" zvalue="3490"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="255@0" LinkObjectIDznd="347@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1018.25 382.21 L 1018.25 357.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="366">
   <path class="kv110" d="M 642.39 191.39 L 935.85 191.39" stroke-width="1" zvalue="3494"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="355@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 642.39 191.39 L 935.85 191.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="367">
   <path class="kv110" d="M 555 384.31 L 555 361.4" stroke-width="1" zvalue="3495"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="118@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 555 384.31 L 555 361.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv110" d="M 1324.06 221.85 L 1324.06 190.84 L 1225.28 190.84" stroke-width="1" zvalue="3503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@1" LinkObjectIDznd="342" MaxPinNum="2"/>
   </metadata>
  <path d="M 1324.06 221.85 L 1324.06 190.84 L 1225.28 190.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv110" d="M 1325.47 323.06 L 1325.47 250.03" stroke-width="1" zvalue="3504"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="376@0" LinkObjectIDznd="368@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1325.47 323.06 L 1325.47 250.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv110" d="M 1362.27 284.08 L 1325.47 284.08" stroke-width="1" zvalue="3505"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="374@0" LinkObjectIDznd="3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1362.27 284.08 L 1325.47 284.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv110" d="M 1326.13 386.81 L 1326.13 340.5" stroke-width="1" zvalue="3507"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="376@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.13 386.81 L 1326.13 340.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 1402.89 410.96 L 1326.13 410.96" stroke-width="1" zvalue="3510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.89 410.96 L 1326.13 410.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="18">
   <use class="kv110" height="26" transform="rotate(0,367.801,250.091) scale(1.17707,1.17707) translate(-54.2662,-35.3195)" width="12" x="360.7381582130827" xlink:href="#Accessory:避雷器1_0" y="234.7888425094462" zvalue="3426"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁六山线避雷器101BL"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,367.801,250.091) scale(1.17707,1.17707) translate(-54.2662,-35.3195)" width="12" x="360.7381582130827" y="234.7888425094462"/></g>
  <g id="6">
   <use class="kv110" height="18" transform="rotate(0,313.13,245.385) scale(2.62555,2.31463) translate(-179.237,-127.539)" width="18" x="289.5000000000001" xlink:href="#Accessory:PT1_0" y="224.5534749500943" zvalue="3428"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV丁六山线"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,313.13,245.385) scale(2.62555,2.31463) translate(-179.237,-127.539)" width="18" x="289.5000000000001" y="224.5534749500943"/></g>
  <g id="359">
   <use class="kv110" height="26" transform="rotate(0,1199.61,250.581) scale(-1.13066,1.13066) translate(-2259.81,-27.2593)" width="12" x="1192.830658518374" xlink:href="#Accessory:避雷器1_0" y="235.8828851523104" zvalue="3466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线102BL"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1199.61,250.581) scale(-1.13066,1.13066) translate(-2259.81,-27.2593)" width="12" x="1192.830658518374" y="235.8828851523104"/></g>
  <g id="358">
   <use class="kv110" height="18" transform="rotate(0,1252.13,246.061) scale(-2.52204,2.22338) translate(-1734.91,-124.381)" width="18" x="1229.431542115538" xlink:href="#Accessory:PT1_0" y="226.0510384639533" zvalue="3468"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线102yh"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1252.13,246.061) scale(-2.52204,2.22338) translate(-1734.91,-124.381)" width="18" x="1229.431542115538" y="226.0510384639533"/></g>
 </g>
 <g id="BreakerClass">
  <g id="118">
   <use class="kv110" height="20" transform="rotate(0,555.375,352.332) scale(0.955301,0.949678) translate(25.7628,18.1663)" width="10" x="550.5983124489991" xlink:href="#Breaker:开关_0" y="342.8348997006742" zvalue="3440"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110侧101DL"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,555.375,352.332) scale(0.955301,0.949678) translate(25.7628,18.1663)" width="10" x="550.5983124489991" y="342.8348997006742"/></g>
  <g id="347">
   <use class="kv110" height="20" transform="rotate(0,1019.44,348.792) scale(-0.917639,0.912238) translate(-2130.78,32.678)" width="10" x="1014.847141472713" xlink:href="#Breaker:开关_0" y="339.669316213607" zvalue="3480"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变110侧102DL"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1019.44,348.792) scale(-0.917639,0.912238) translate(-2130.78,32.678)" width="10" x="1014.847141472713" y="339.669316213607"/></g>
  <g id="376">
   <use class="kv110" height="20" transform="rotate(0,1325.44,331.792) scale(-0.917639,0.912238) translate(-2770.24,31.0425)" width="10" x="1320.847141472713" xlink:href="#Breaker:开关_0" y="322.669316213607" zvalue="3501"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110KV丁南六线 六五谷牵变支线104DL"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1325.44,331.792) scale(-0.917639,0.912238) translate(-2770.24,31.0425)" width="10" x="1320.847141472713" y="322.669316213607"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="210">
   <g id="2100">
    <use class="kv0" height="60" transform="rotate(0,555,456.857) scale(2.62857,2.62857) translate(-315.359,-234.196)" width="35" x="509" xlink:href="#PowerTransformer2:V-V不带接地点_0" y="378" zvalue="3455"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0"/>
    </metadata>
   </g>
   <g id="2101">
    <use class="kv0" height="60" transform="rotate(0,555,456.857) scale(2.62857,2.62857) translate(-315.359,-234.196)" width="35" x="509" xlink:href="#PowerTransformer2:V-V不带接地点_1" y="378" zvalue="3455"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,555,456.857) scale(2.62857,2.62857) translate(-315.359,-234.196)" width="35" x="509" y="378"/></g>
  <g id="255">
   <g id="2550">
    <use class="kv0" height="60" transform="rotate(0,1018.25,453.571) scale(-2.58571,2.58571) translate(-1384.3,-230.586)" width="35" x="973" xlink:href="#PowerTransformer2:V-V不带接地点_0" y="376" zvalue="3491"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0"/>
    </metadata>
   </g>
   <g id="2551">
    <use class="kv0" height="60" transform="rotate(0,1018.25,453.571) scale(-2.58571,2.58571) translate(-1384.3,-230.586)" width="35" x="973" xlink:href="#PowerTransformer2:V-V不带接地点_1" y="376" zvalue="3491"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1018.25,453.571) scale(-2.58571,2.58571) translate(-1384.3,-230.586)" width="35" x="973" y="376"/></g>
  <g id="5">
   <g id="50">
    <use class="kv110" height="60" transform="rotate(0,1326.12,436.688) scale(1.84375,1.84375) translate(-589.996,-174.528)" width="40" x="1289.25" xlink:href="#PowerTransformer2:不接地中性点_0" y="381.38" zvalue="3506"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="110"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1DLB"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1326.12,436.688) scale(1.84375,1.84375) translate(-589.996,-174.528)" width="40" x="1289.25" y="381.38"/></g>
 </g>
</svg>