<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584396290" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,14,18">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1.416666666666664"/>
   <ellipse cx="7.15" cy="6.35" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.15" cy="12.03" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:熔断器12_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1.916666666666668" y2="18.25"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,10.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="2.02"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:中间电缆_0" viewBox="0,0,8,21">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="10.5"/>
   <path d="M 1.08333 0.5 L 7 0.5 L 4 7.13889 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4" x2="4" y1="20.83333333333333" y2="0.5000000000000036"/>
   <path d="M 1.08333 20.6389 L 7 20.6389 L 4 14 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(85,255,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:高压计量装置_0" viewBox="0,0,22,12">
   <use terminal-index="0" type="0" x="11" xlink:href="#terminal" y="3.399999999999999"/>
   <text fill="rgb(0,0,0)" font-family="宋体" font-size="16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="11.08333333333333" xml:space="preserve" y="9.25">35kV  高压</text>
   <rect fill-opacity="0" height="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.08,5.97) scale(1,1) translate(0,0)" width="9.83" x="6.17" y="3.63"/>
   <text fill="rgb(0,0,0)" font-family="宋体" font-size="16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="11.08333333333333" xml:space="preserve" y="11.33333333333333">计量  装置</text>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器DY_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 11.5417 13.7917 L 8.54167 13.7917 L 10.0417 11.0417 L 11.5417 13.7917 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV大营变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="35kV大营变_软压板.svg"><rect fill-opacity="0" height="40" width="97.14" x="35.02" y="477.98" zvalue="154"/></g>
  <g href="35kV大营变_直流监控.svg"><rect fill-opacity="0" height="40" width="97.14" x="35.02" y="533.98" zvalue="155"/></g>
 </g>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,170.782,45.9583) scale(1,1) translate(0,0)" writing-mode="lr" x="170.78" xml:space="preserve" y="62.96" zvalue="126">     大营变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="126" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.5913,497.984) scale(1,1) translate(0,0)" width="97.14" x="35.02" y="477.98" zvalue="154"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.5913,497.984) scale(1,1) translate(0,0)" writing-mode="lr" x="83.59" xml:space="preserve" y="501.98" zvalue="154">软压板</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="125" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.5913,553.984) scale(1,1) translate(0,0)" width="97.14" x="35.02" y="533.98" zvalue="155"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.5913,553.984) scale(1,1) translate(0,0)" writing-mode="lr" x="83.59" xml:space="preserve" y="557.98" zvalue="155">直流监控</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="33" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,146.714,141.143) scale(1,1) translate(0,0)" width="97.14" x="98.14" y="121.14" zvalue="290"/>
  <text fill="rgb(255,170,0)" font-family="FangSong" font-size="19" id="5" stroke="rgb(255,170,0)" text-anchor="middle" transform="rotate(0,146.714,141.143) scale(1,1) translate(0,0)" writing-mode="lr" x="146.71" xml:space="preserve" y="147.64" zvalue="290">全站可控</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,698.495,346.857) scale(1,1) translate(0,0)" writing-mode="lr" x="698.5" xml:space="preserve" y="352.86" zvalue="3">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.29,611.198) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.29" xml:space="preserve" y="617.2" zvalue="3">4MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.065,501.31) scale(1,1) translate(0,0)" writing-mode="lr" x="925.0599999999999" xml:space="preserve" y="505.81" zvalue="6">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,926.511,409.201) scale(1,1) translate(0,0)" writing-mode="lr" x="926.51" xml:space="preserve" y="413.7" zvalue="8">3011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.548,88.6564) scale(1,1) translate(0,0)" writing-mode="lr" x="962.55" xml:space="preserve" y="94.66" zvalue="10">35kV宾瓦T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.89,461.027) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.89" xml:space="preserve" y="465.53" zvalue="13">30117</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,958.204,1103.76) scale(1,1) translate(0,0)" writing-mode="lr" x="958.2" xml:space="preserve" y="1109.76" zvalue="17">10kV大营线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,663.8,979.798) scale(1,1) translate(0,0)" writing-mode="lr" x="663.8" xml:space="preserve" y="985.8" zvalue="19">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,922.755,245.435) scale(1,1) translate(0,0)" writing-mode="lr" x="922.76" xml:space="preserve" y="249.94" zvalue="21">3806</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1015.96,167.655) scale(1,1) translate(0,0)" writing-mode="lr" x="1015.96" xml:space="preserve" y="172.15" zvalue="24">3809</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.448,688.103) scale(1,1) translate(0,0)" writing-mode="lr" x="929.45" xml:space="preserve" y="692.6" zvalue="39">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921.548,739.108) scale(1,1) translate(0,0)" writing-mode="lr" x="921.55" xml:space="preserve" y="743.61" zvalue="40">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.013,816.143) scale(1,1) translate(0,0)" writing-mode="lr" x="598.01" xml:space="preserve" y="822.14" zvalue="42">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1365.59,413.201) scale(1,1) translate(0,0)" writing-mode="lr" x="1365.59" xml:space="preserve" y="417.7" zvalue="49">3721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006.11,875.757) scale(1,1) translate(0,0)" writing-mode="lr" x="1006.11" xml:space="preserve" y="880.26" zvalue="58">0511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1001.16,923.365) scale(1,1) translate(0,0)" writing-mode="lr" x="1001.16" xml:space="preserve" y="927.87" zvalue="61">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006.11,975.84) scale(1,1) translate(0,0)" writing-mode="lr" x="1006.11" xml:space="preserve" y="980.34" zvalue="62">0516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.648,874.924) scale(1,1) translate(0,0)" writing-mode="lr" x="703.65" xml:space="preserve" y="879.42" zvalue="72">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336.99,979.798) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.99" xml:space="preserve" y="985.8" zvalue="81">10kV故障信号源装置</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.46,889.467) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.46" xml:space="preserve" y="893.97" zvalue="82">0521</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.2321,359.179) scale(1,1) translate(0,0)" writing-mode="lr" x="82.23" xml:space="preserve" y="365.18" zvalue="156">负荷总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.4821,395.198) scale(1,1) translate(0,0)" writing-mode="lr" x="78.48" xml:space="preserve" y="401.2" zvalue="157">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.2321,435.198) scale(1,1) translate(0,0)" writing-mode="lr" x="82.23" xml:space="preserve" y="441.2" zvalue="158">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.1488,241.429) scale(1,1) translate(0,0)" writing-mode="lr" x="88.15000000000001" xml:space="preserve" y="247.43" zvalue="163">事故</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.149,241.429) scale(1,1) translate(0,0)" writing-mode="lr" x="148.15" xml:space="preserve" y="247.43" zvalue="168">异常</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.149,241.429) scale(1,1) translate(0,0)" writing-mode="lr" x="208.15" xml:space="preserve" y="247.43" zvalue="169">告知</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044.89,588.886) scale(1,1) translate(0,0)" writing-mode="lr" x="1044.89" xml:space="preserve" y="594.89" zvalue="196">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,184.396,395.198) scale(1,1) translate(0,0)" writing-mode="lr" x="184.4" xml:space="preserve" y="401.2" zvalue="228">大营变-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,184.396,435.198) scale(1,1) translate(0,0)" writing-mode="lr" x="184.4" xml:space="preserve" y="441.2" zvalue="230">2022-07-15</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1339.98,594.152) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.98" xml:space="preserve" y="600.15" zvalue="233">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98.1488,311.179) scale(1,1) translate(0,0)" writing-mode="lr" x="98.15000000000001" xml:space="preserve" y="317.18" zvalue="285">是否失压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,179.649,311.179) scale(1,1) translate(0,0)" writing-mode="lr" x="179.65" xml:space="preserve" y="317.18" zvalue="287">失压排除</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="2">
   <path class="kv35" d="M 655.26 365.14 L 1402.79 365.14" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242002948" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242002948"/></metadata>
  <path d="M 655.26 365.14 L 1402.79 365.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 550.86 834.14 L 1460.02 834.14" stroke-width="6" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242068485" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242068485"/></metadata>
  <path d="M 550.86 834.14 L 1460.02 834.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="473">
   <use class="kv35" height="20" transform="rotate(0,960.056,501.81) scale(1.83111,1.88671) translate(-431.598,-226.971)" width="10" x="950.9002510297437" xlink:href="#Breaker:开关_0" y="482.9426475982896" zvalue="5"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512120837" ObjectName="#1主变35kV侧301"/>
   <cge:TPSR_Ref TObjectID="6473924512120837"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,960.056,501.81) scale(1.83111,1.88671) translate(-431.598,-226.971)" width="10" x="950.9002510297437" y="482.9426475982896"/></g>
  <g id="509">
   <use class="kv10" height="20" transform="rotate(0,960.056,686.103) scale(1.83111,1.88671) translate(-431.598,-313.585)" width="10" x="950.9002474330933" xlink:href="#Breaker:开关_0" y="667.235576891219" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512186373" ObjectName="#1主变10kV侧001"/>
   <cge:TPSR_Ref TObjectID="6473924512186373"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,960.056,686.103) scale(1.83111,1.88671) translate(-431.598,-313.585)" width="10" x="950.9002474330933" y="667.235576891219"/></g>
  <g id="46">
   <use class="kv10" height="20" transform="rotate(0,958.651,921.365) scale(1.83111,1.88671) translate(-430.961,-424.152)" width="10" x="949.495787845632" xlink:href="#Breaker:开关_0" y="902.4981993006156" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512251909" ObjectName="10kV大营线051"/>
   <cge:TPSR_Ref TObjectID="6473924512251909"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,958.651,921.365) scale(1.83111,1.88671) translate(-430.961,-424.152)" width="10" x="949.495787845632" y="902.4981993006156"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="472">
   <use class="kv35" height="30" transform="rotate(0,960.714,411.451) scale(-1.11133,0.814667) translate(-1824.35,90.8235)" width="15" x="952.378544781942" xlink:href="#Disconnector:刀闸_0" y="399.2313690185545" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449857847302" ObjectName="#1主变35kV侧3011"/>
   <cge:TPSR_Ref TObjectID="6192449857847302"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.714,411.451) scale(-1.11133,0.814667) translate(-1824.35,90.8235)" width="15" x="952.378544781942" y="399.2313690185545"/></g>
  <g id="12">
   <use class="kv35" height="30" transform="rotate(0,960.672,248.594) scale(-1.11133,0.814667) translate(-1824.27,53.7742)" width="15" x="952.3367173228488" xlink:href="#Disconnector:刀闸_0" y="236.3742303765209" zvalue="20"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858174982" ObjectName="35kV宾瓦T线3806"/>
   <cge:TPSR_Ref TObjectID="6192449858174982"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.672,248.594) scale(-1.11133,0.814667) translate(-1824.27,53.7742)" width="15" x="952.3367173228488" y="236.3742303765209"/></g>
  <g id="13">
   <use class="kv35" height="30" transform="rotate(270,1014.82,178.309) scale(-1.11133,0.814667) translate(-1927.14,37.7845)" width="15" x="1006.482689206004" xlink:href="#Disconnector:刀闸_0" y="166.0885160908066" zvalue="23"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858240518" ObjectName="35kV宾瓦T线3809"/>
   <cge:TPSR_Ref TObjectID="6192449858240518"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1014.82,178.309) scale(-1.11133,0.814667) translate(-1927.14,37.7845)" width="15" x="1006.482689206004" y="166.0885160908066"/></g>
  <g id="511">
   <use class="kv10" height="30" transform="rotate(0,960.056,738.108) scale(-1.11133,0.814667) translate(-1823.1,165.137)" width="15" x="951.7208049477307" xlink:href="#Disconnector:刀闸_0" y="725.8879388902294" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858502661" ObjectName="#1主变10kV侧0011"/>
   <cge:TPSR_Ref TObjectID="6192449858502661"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.056,738.108) scale(-1.11133,0.814667) translate(-1823.1,165.137)" width="15" x="951.7208049477307" y="725.8879388902294"/></g>
  <g id="35">
   <use class="kv35" height="30" transform="rotate(0,1323.79,411.451) scale(-1.11133,0.814667) translate(-2514.14,90.8235)" width="15" x="1315.459595504672" xlink:href="#Disconnector:刀闸_0" y="399.2313694189663" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858568197" ObjectName="35kV1号站用变3721"/>
   <cge:TPSR_Ref TObjectID="6192449858568197"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1323.79,411.451) scale(-1.11133,0.814667) translate(-2514.14,90.8235)" width="15" x="1315.459595504672" y="399.2313694189663"/></g>
  <g id="47">
   <use class="kv10" height="30" transform="rotate(0,958.69,874.007) scale(-1.11133,0.814667) translate(-1820.5,196.053)" width="15" x="950.3548410262074" xlink:href="#Disconnector:刀闸_0" y="861.7869266699429" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858830341" ObjectName="10kV大营线0511"/>
   <cge:TPSR_Ref TObjectID="6192449858830341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,958.69,874.007) scale(-1.11133,0.814667) translate(-1820.5,196.053)" width="15" x="950.3548410262074" y="861.7869266699429"/></g>
  <g id="45">
   <use class="kv10" height="30" transform="rotate(0,958.651,974.84) scale(-1.11133,0.814667) translate(-1820.43,218.992)" width="15" x="950.3163453602693" xlink:href="#Disconnector:刀闸_0" y="962.6202687886397" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858764805" ObjectName="10kV大营线0516"/>
   <cge:TPSR_Ref TObjectID="6192449858764805"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,958.651,974.84) scale(-1.11133,0.814667) translate(-1820.43,218.992)" width="15" x="950.3163453602693" y="962.6202687886397"/></g>
  <g id="55">
   <use class="kv10" height="30" transform="rotate(0,660.71,874.007) scale(-1.11133,0.814667) translate(-1254.39,196.053)" width="15" x="652.3748935259969" xlink:href="#Disconnector:刀闸_0" y="861.7869262695312" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858961413" ObjectName="10kV#Ⅰ母线电压互感器0901"/>
   <cge:TPSR_Ref TObjectID="6192449858961413"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,660.71,874.007) scale(-1.11133,0.814667) translate(-1254.39,196.053)" width="15" x="652.3748935259969" y="861.7869262695312"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="427">
   <use class="kv35" height="30" transform="rotate(0,960.534,121.796) scale(2.82817,1.15097) translate(-614.504,-13.7109)" width="7" x="950.6352034806682" xlink:href="#ACLineSegment:线路_0" y="104.5313966021969" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249322356741" ObjectName="35kV宾瓦T线大营支线"/>
   <cge:TPSR_Ref TObjectID="8444249322356741_5066549584396290"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.534,121.796) scale(2.82817,1.15097) translate(-614.504,-13.7109)" width="7" x="950.6352034806682" y="104.5313966021969"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="499">
   <use class="kv35" height="30" transform="rotate(90,1016.44,448.536) scale(1.01422,-0.867474) translate(-14.1691,-967.584)" width="12" x="1010.350039761265" xlink:href="#GroundDisconnector:地刀12_0" y="435.5240029617603" zvalue="12"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449857978374" ObjectName="#1主变35kV侧30117"/>
   <cge:TPSR_Ref TObjectID="6192449857978374"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1016.44,448.536) scale(1.01422,-0.867474) translate(-14.1691,-967.584)" width="12" x="1010.350039761265" y="435.5240029617603"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="86">
   <use class="kv10" height="30" transform="rotate(180,958.555,1072.91) scale(1.64976,1.15097) translate(-373.631,-138.464)" width="12" x="948.6566506133628" xlink:href="#EnergyConsumer:负荷_0" y="1055.644836128465" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858043910" ObjectName="10kV大营线"/>
   <cge:TPSR_Ref TObjectID="6192449858043910"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,958.555,1072.91) scale(1.64976,1.15097) translate(-373.631,-138.464)" width="12" x="948.6566506133628" y="1055.644836128465"/></g>
  <g id="792">
   <use class="kv35" height="30" transform="rotate(180,1323.73,518.929) scale(-2.77837,-2.77837) translate(-1782.38,-679.028)" width="20" x="1295.942903726674" xlink:href="#EnergyConsumer:站用变带熔断器DY_0" y="477.2532463414329" zvalue="232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858699269" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1323.73,518.929) scale(-2.77837,-2.77837) translate(-1782.38,-679.028)" width="20" x="1295.942903726674" y="477.2532463414329"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="152">
   <use class="kv10" height="18" transform="rotate(0,663.65,939.938) scale(3.23456,2.99452) translate(-441.716,-608.101)" width="15" x="639.3905998000304" xlink:href="#Accessory:PT8_0" y="912.9875508113527" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858109446" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,663.65,939.938) scale(3.23456,2.99452) translate(-441.716,-608.101)" width="15" x="639.3905998000304" y="912.9875508113527"/></g>
  <g id="14">
   <use class="kv35" height="26" transform="rotate(270,1091.93,178.415) scale(1.15916,1.14131) translate(-148.972,-20.2526)" width="12" x="1084.97604886939" xlink:href="#Accessory:避雷器1_0" y="163.5781613901264" zvalue="25"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858306054" ObjectName="35kV宾瓦T线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1091.93,178.415) scale(1.15916,1.14131) translate(-148.972,-20.2526)" width="12" x="1084.97604886939" y="163.5781613901264"/></g>
  <g id="507">
   <use class="kv10" height="26" transform="rotate(270,1011.79,770.872) scale(-1.15916,1.14131) translate(-1883.7,-93.6048)" width="12" x="1004.833685356819" xlink:href="#Accessory:避雷器1_0" y="756.0351484884117" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858437125" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1011.79,770.872) scale(-1.15916,1.14131) translate(-1883.7,-93.6048)" width="12" x="1004.833685356819" y="756.0351484884117"/></g>
  <g id="36">
   <use class="kv35" height="26" transform="rotate(0,1369.89,497.477) scale(-1.15916,1.14131) translate(-2550.73,-59.7558)" width="12" x="1362.931686354055" xlink:href="#Accessory:避雷器1_0" y="482.6402603070311" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858633733" ObjectName="35kV1号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1369.89,497.477) scale(-1.15916,1.14131) translate(-2550.73,-59.7558)" width="12" x="1362.931686354055" y="482.6402603070311"/></g>
  <g id="48">
   <use class="kv10" height="26" transform="rotate(0,922.98,1044.45) scale(-1.15916,1.14131) translate(-1718.28,-127.476)" width="12" x="916.025381109505" xlink:href="#Accessory:避雷器1_0" y="1029.611779610803" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449858895877" ObjectName="10kV大营线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,922.98,1044.45) scale(-1.15916,1.14131) translate(-1718.28,-127.476)" width="12" x="916.025381109505" y="1029.611779610803"/></g>
  <g id="60">
   <use class="kv10" height="26" transform="rotate(0,1299.17,890.144) scale(-1.15916,1.14131) translate(-2419,-108.372)" width="12" x="1292.211592070024" xlink:href="#Accessory:避雷器1_0" y="875.3069269736975" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449859092485" ObjectName="10kV故障信号源装置避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1299.17,890.144) scale(-1.15916,1.14131) translate(-2419,-108.372)" width="12" x="1292.211592070024" y="875.3069269736975"/></g>
  <g id="61">
   <use class="kv10" height="18" transform="rotate(0,1331.1,944.702) scale(3.11474,2.20238) translate(-888.943,-504.935)" width="14" x="1309.298422196767" xlink:href="#Accessory:PT7_0" y="924.8809523809525" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449859158022" ObjectName="10kV故障信号源装置"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1331.1,944.702) scale(3.11474,2.20238) translate(-888.943,-504.935)" width="14" x="1309.298422196767" y="924.8809523809525"/></g>
  <g id="62">
   <use class="kv10" height="20" transform="rotate(0,1331.1,891.036) scale(1.27812,1.32494) translate(-288.259,-215.276)" width="10" x="1324.710997400877" xlink:href="#Accessory:熔断器12_0" y="877.7869266844623" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449859223558" ObjectName="10kV故障信号源装置0521"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1331.1,891.036) scale(1.27812,1.32494) translate(-288.259,-215.276)" width="10" x="1324.710997400877" y="877.7869266844623"/></g>
  <g id="91">
   <use class="kv10" height="21" transform="rotate(0,959.96,792.823) scale(2.12139,1.04762) translate(-502.96,-35.5374)" width="8" x="951.4741283826461" xlink:href="#Accessory:中间电缆_0" y="781.8228905597327" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452019224579" ObjectName="#1主变10kV侧中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,959.96,792.823) scale(2.12139,1.04762) translate(-502.96,-35.5374)" width="8" x="951.4741283826461" y="781.8228905597327"/></g>
  <g id="1">
   <use class="kv35" height="12" transform="rotate(0,960.576,307.596) scale(10.6427,7.52525) translate(-764.249,-227.569)" width="22" x="843.5062462452755" xlink:href="#Accessory:高压计量装置_0" y="262.4444444444445" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452019290115" ObjectName="35kV高压计量装置"/>
   </metadata>
  <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(0,960.576,307.596) scale(10.6427,7.52525) translate(-764.249,-227.569)" width="22" x="843.5062462452755" y="262.4444444444445"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="15">
   <path class="kv35" d="M 960.57 236.78 L 960.53 138.89" stroke-width="1" zvalue="26"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@0" LinkObjectIDznd="427@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.57 236.78 L 960.53 138.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 1003 178.41 L 960.55 178.41" stroke-width="1" zvalue="29"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003 178.41 L 960.55 178.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 1026.83 178.38 L 1077.82 178.38" stroke-width="1" zvalue="30"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@1" LinkObjectIDznd="14@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.83 178.38 L 1077.82 178.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 960.62 399.64 L 960.62 365.14" stroke-width="1" zvalue="31"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@0" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.62 399.64 L 960.62 365.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv35" d="M 960.65 423.46 L 960.65 483.76" stroke-width="1" zvalue="32"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@1" LinkObjectIDznd="473@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.65 423.46 L 960.65 483.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 1004.01 448.52 L 960.65 448.52" stroke-width="1" zvalue="34"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="499@0" LinkObjectIDznd="22" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.01 448.52 L 960.65 448.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 1323.7 399.64 L 1323.7 365.14" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.7 399.64 L 1323.7 365.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 958.59 862.19 L 958.59 834.14" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.59 862.19 L 958.59 834.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 958.62 886.02 L 958.59 903.32" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.62 886.02 L 958.59 903.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 958.77 939.38 L 958.77 963.02" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.77 939.38 L 958.77 963.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 958.58 986.85 L 958.56 1057.37" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="86@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.58 986.85 L 958.56 1057.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 660.61 862.19 L 660.61 834.14" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="25@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.61 862.19 L 660.61 834.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 660.64 886.02 L 660.64 914.9" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.64 886.02 L 660.64 914.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 960.18 519.83 L 960.03 557.92" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="473@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.18 519.83 L 960.03 557.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv10" d="M 959.97 641.45 L 959.99 668.05" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="509@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 959.97 641.45 L 959.99 668.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 960.18 704.12 L 960.18 726.29" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="509@1" LinkObjectIDznd="511@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.18 704.12 L 960.18 726.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 959.99 750.12 L 959.96 792.82" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="511@1" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 959.99 750.12 L 959.96 792.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 997.67 770.91 L 959.97 770.91" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="507@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.67 770.91 L 959.97 770.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 958.58 1004.71 L 922.94 1004.71 L 922.94 1030.33" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.58 1004.71 L 922.94 1004.71 L 922.94 1030.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1331.1 891.04 L 1331.1 834.14" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="25@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.1 891.04 L 1331.1 834.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1331.1 891.04 L 1331.1 928" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.1 891.04 L 1331.1 928" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1331.1 857.69 L 1299.13 857.69 L 1299.13 876.03" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108" LinkObjectIDznd="60@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.1 857.69 L 1299.13 857.69 L 1299.13 876.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv35" d="M 1323.73 423.46 L 1323.73 479.61" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@1" LinkObjectIDznd="792@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.73 423.46 L 1323.73 479.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1323.73 462.75 L 1369.85 462.75 L 1369.85 483.36" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.73 462.75 L 1369.85 462.75 L 1369.85 483.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 959.96 792.8 L 959.96 834.14" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 959.96 792.8 L 959.96 834.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 960.6 260.61 L 960.58 288.03" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@1" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.6 260.61 L 960.58 288.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 960.58 288.03 L 960.58 365.14" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16" LinkObjectIDznd="2@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.58 288.03 L 960.58 365.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="StateClass">
  <g id="390">
   <use height="30" stroke="rgb(255,255,255)" transform="rotate(0,92.9702,207.429) scale(0.958333,0.916667) translate(3.41718,17.6071)" width="30" x="78.59999999999999" xlink:href="#State:红绿圆_0" y="193.68" zvalue="160"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584396290" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,92.9702,207.429) scale(0.958333,0.916667) translate(3.41718,17.6071)" width="30" x="78.59999999999999" y="193.68"/></g>
  <g id="389">
   <use height="30" transform="rotate(0,150.399,207.429) scale(0.958333,0.916667) translate(5.91408,17.6071)" width="30" x="136.02" xlink:href="#State:红绿圆_0" y="193.68" zvalue="161"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584396290" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,150.399,207.429) scale(0.958333,0.916667) translate(5.91408,17.6071)" width="30" x="136.02" y="193.68"/></g>
  <g id="388">
   <use height="30" transform="rotate(0,210.399,207.429) scale(0.958333,0.916667) translate(8.52277,17.6071)" width="30" x="196.02" xlink:href="#State:红绿圆_0" y="193.68" zvalue="162"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584396290" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,210.399,207.429) scale(0.958333,0.916667) translate(8.52277,17.6071)" width="30" x="196.02" y="193.68"/></g>
  <g id="559">
   <use height="30" transform="rotate(0,98.8988,279.179) scale(0.958333,0.916667) translate(3.67495,24.1299)" width="30" x="84.52" xlink:href="#State:红绿圆_0" y="265.43" zvalue="286"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584396290" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,98.8988,279.179) scale(0.958333,0.916667) translate(3.67495,24.1299)" width="30" x="84.52" y="265.43"/></g>
  <g id="557">
   <use height="30" transform="rotate(0,186.399,279.179) scale(0.958333,0.916667) translate(7.4793,24.1299)" width="30" x="172.02" xlink:href="#State:红绿圆_0" y="265.43" zvalue="288"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584396290" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,186.399,279.179) scale(0.958333,0.916667) translate(7.4793,24.1299)" width="30" x="172.02" y="265.43"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="65">
   <g id="650">
    <use class="kv35" height="50" transform="rotate(0,959.969,599.6) scale(1.77288,1.69788) translate(-406.902,-229.007)" width="30" x="933.38" xlink:href="#PowerTransformer2:Y-D_0" y="557.15" zvalue="195"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431135748" ObjectName="35"/>
    </metadata>
   </g>
   <g id="651">
    <use class="kv10" height="50" transform="rotate(0,959.969,599.6) scale(1.77288,1.69788) translate(-406.902,-229.007)" width="30" x="933.38" xlink:href="#PowerTransformer2:Y-D_1" y="557.15" zvalue="195"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431201284" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447216132" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447216132"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,959.969,599.6) scale(1.77288,1.69788) translate(-406.902,-229.007)" width="30" x="933.38" y="557.15"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="83" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,848.969,468.528) scale(1,1) translate(0,0)" writing-mode="lr" x="848.97" xml:space="preserve" y="474.69" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131886247940" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="84" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,848.969,485.528) scale(1,1) translate(0,0)" writing-mode="lr" x="848.97" xml:space="preserve" y="491.69" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131886313476" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="85" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,848.969,667.672) scale(1,1) translate(1.80738e-13,1.46477e-13)" writing-mode="lr" x="848.97" xml:space="preserve" y="673.84" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131886379012" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="90" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,848.969,684.672) scale(1,1) translate(1.80738e-13,0)" writing-mode="lr" x="848.97" xml:space="preserve" y="690.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131886444548" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="93" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,848.969,502.528) scale(1,1) translate(0,0)" writing-mode="lr" x="848.97" xml:space="preserve" y="508.69" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131886510084" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="94" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,855.469,599.225) scale(1,1) translate(0,0)" writing-mode="lr" x="855.47" xml:space="preserve" y="603.89" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131886706692" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="97" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,848.969,701.672) scale(1,1) translate(1.80738e-13,0)" writing-mode="lr" x="848.97" xml:space="preserve" y="707.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131886837764" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.969,518.778) scale(1,1) translate(0,0)" writing-mode="lr" x="848.97" xml:space="preserve" y="524.9400000000001" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131887427588" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.969,718.422) scale(1,1) translate(0,0)" writing-mode="lr" x="848.97" xml:space="preserve" y="724.59" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481131887624196" ObjectName="LCos"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="100" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,951.912,1119.57) scale(1,1) translate(0,0)" writing-mode="lr" x="951.91" xml:space="preserve" y="1125.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125190500356" ObjectName="P"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="101" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,950.484,1134.43) scale(1,1) translate(0,0)" writing-mode="lr" x="950.48" xml:space="preserve" y="1140.6" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125190565892" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="102" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,951.912,1149.29) scale(1,1) translate(0,0)" writing-mode="lr" x="951.91" xml:space="preserve" y="1155.45" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125190631428" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,950.3,1164.29) scale(1,1) translate(0,0)" writing-mode="lr" x="950.3" xml:space="preserve" y="1170.45" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125191024644" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,600.786,743.357) scale(1,1) translate(0,0)" writing-mode="lr" x="600.79" xml:space="preserve" y="749.63" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125191942148" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="105" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,692.041,271.786) scale(1,1) translate(0,0)" writing-mode="lr" x="692.04" xml:space="preserve" y="278.06" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125186371588" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="111" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,600.786,761.357) scale(1,1) translate(0,0)" writing-mode="lr" x="600.79" xml:space="preserve" y="767.63" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125192007684" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="114" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,692.041,289.786) scale(1,1) translate(0,0)" writing-mode="lr" x="692.04" xml:space="preserve" y="296.06" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125186437124" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="121" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,600.786,779.357) scale(1,1) translate(0,0)" writing-mode="lr" x="600.79" xml:space="preserve" y="785.63" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125192073220" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="147" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,692.041,307.786) scale(1,1) translate(0,0)" writing-mode="lr" x="692.04" xml:space="preserve" y="314.06" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125186502660" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="148" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,600.786,725.5) scale(1,1) translate(0,6.36824e-13)" writing-mode="lr" x="600.79" xml:space="preserve" y="731.77" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125192204292" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="151" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,695.239,254.929) scale(1,1) translate(0,0)" writing-mode="lr" x="695.24" xml:space="preserve" y="261.2" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125186633733" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="153" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,600.786,797.929) scale(1,1) translate(0,0)" writing-mode="lr" x="600.79" xml:space="preserve" y="804.2" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125192400900" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="154" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,692.041,326.357) scale(1,1) translate(0,0)" writing-mode="lr" x="692.04" xml:space="preserve" y="332.63" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125186830341" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
</svg>