<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584592898" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变_0" viewBox="0,0,18,30">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="9.08" cy="8.92" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.02" cy="21.08" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带负荷_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="11.6895889186774" y1="10.61388616667628" y2="13.9574037777906"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869541" x2="11.6895889186774" y1="13.9574037777906" y2="13.9574037777906"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627346" x2="8.329445933869529" y1="10.61388616667628" y2="13.9574037777906"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="23.31619780557639" y2="23.31619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="24.15207720835499" y2="24.15207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="24.98795661113357" y2="24.98795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="23.31619780557638"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="28.23101083579602" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.09764705882352" x2="9.277450980392153" y1="29.58333333333332" y2="28.23848223081002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.09764705882353" x2="10.9178431372549" y1="29.58333333333332" y2="28.23848223081002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.277450980392169" x2="10.91784313725491" y1="28.23848223081002" y2="28.23848223081002"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer: 接地变2_0" viewBox="0,0,25,40">
   <use terminal-index="0" type="0" x="10.2" xlink:href="#terminal" y="1.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="22.25" y1="24" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.25" x2="22.25" y1="23" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="22.25" y1="35.75" y2="37.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="23.25" y1="38.75" y2="38.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="22.75" y1="39.75" y2="39.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25" x2="24.25" y1="37.75" y2="37.75"/>
   <ellipse cx="10.29" cy="9.970000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.22" cy="22.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.28525619067018" x2="10.28525619067018" y1="18.85" y2="22.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.28525619067017" x2="6.3" y1="22.98394833233988" y2="26"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.31302903072498" x2="14.4" y1="22.93990500916851" y2="25.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.28525619067018" x2="10.28525619067018" y1="5.6" y2="9.708948332339869"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.28525619067017" x2="6.3" y1="9.733948332339878" y2="12.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.31302903072498" x2="14.4" y1="9.689905009168511" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.30128205128204" x2="22.30128205128204" y1="33.36269837455333" y2="35.66666666666664"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.91666666666666" x2="22.301282051282" y1="26.25044842933395" y2="33.39608921936657"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.44458689458689" x2="23.01666666666664" y1="26.10260218667743" y2="26.10260218667743"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.30561325170641" x2="22.30561325170641" y1="26.18366673970752" y2="24.02212849759811"/>
  </symbol>
  <symbol id="Accessory:高压计量装置_0" viewBox="0,0,22,12">
   <use terminal-index="0" type="0" x="11" xlink:href="#terminal" y="3.399999999999999"/>
   <text fill="rgb(0,0,0)" font-family="宋体" font-size="16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="11.08333333333333" xml:space="preserve" y="9.25">35kV  高压</text>
   <rect fill-opacity="0" height="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.08,5.97) scale(1,1) translate(0,0)" width="9.83" x="6.17" y="3.63"/>
   <text fill="rgb(0,0,0)" font-family="宋体" font-size="16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="11.08333333333333" xml:space="preserve" y="11.33333333333333">计量  装置</text>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(85,255,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV拉乌变" InitShowingPlane="0" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass">
  
 </g>
 <g id="ButtonClass">
  <g href="单厂站信息-dali.svg"><rect fill-opacity="0" height="24" width="72.88" x="310.78" y="274.13" zvalue="1167"/></g>
  <g href="信号一览表.svg"><rect fill-opacity="0" height="24" width="72.88" x="221.31" y="220" zvalue="1182"/></g>
  <g href="软压板控制表.svg"><rect fill-opacity="0" height="24" width="72.88" x="221.31" y="274.13" zvalue="1227"/></g>
  <g href="直流监控表.svg"><rect fill-opacity="0" height="24" width="72.88" x="310.78" y="220" zvalue="1228"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="101" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,347.222,286.127) scale(1,1) translate(0,0)" width="72.88" x="310.78" y="274.13" zvalue="1167"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,347.222,286.127) scale(1,1) translate(0,0)" writing-mode="lr" x="347.22" xml:space="preserve" y="290.13" zvalue="1167">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="87" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,257.75,232) scale(1,1) translate(0,0)" width="72.88" x="221.31" y="220" zvalue="1182"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,257.75,232) scale(1,1) translate(0,0)" writing-mode="lr" x="257.75" xml:space="preserve" y="236.5" zvalue="1182">信号一览</text>
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="32" id="4" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,215.571,47.0237) scale(1,1) translate(0,0)" writing-mode="lr" x="215.57" xml:space="preserve" y="58.52" zvalue="1185">35kV拉乌变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="120" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,257.75,286.127) scale(1,1) translate(0,0)" width="72.88" x="221.31" y="274.13" zvalue="1227"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,257.75,286.127) scale(1,1) translate(0,0)" writing-mode="lr" x="257.75" xml:space="preserve" y="290.13" zvalue="1227">软压板</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="119" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,347.222,232) scale(1,1) translate(0,0)" width="72.88" x="310.78" y="220" zvalue="1228"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,347.222,232) scale(1,1) translate(0,0)" writing-mode="lr" x="347.22" xml:space="preserve" y="236" zvalue="1228">直流监控</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1060.58,118.885) scale(1,1) translate(0,0)" writing-mode="lr" x="1060.58" xml:space="preserve" y="121.88" zvalue="282">35kV平拉新线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.57,1053.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1061.57" xml:space="preserve" y="1056.93" zvalue="695">10kV新田线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1215.71,1053.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1215.71" xml:space="preserve" y="1056.93" zvalue="712">10kV桃园箐电站线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1373.71,1053.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1373.71" xml:space="preserve" y="1056.93" zvalue="732">10kV大厂电站线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,546.133,752.987) scale(1,1) translate(0,0)" writing-mode="lr" x="546.13" xml:space="preserve" y="755.99" zvalue="776">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1653.05,390.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1653.05" xml:space="preserve" y="393.78" zvalue="780">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.2,445.701) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.2" xml:space="preserve" y="448.7" zvalue="782">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.21,485.108) scale(1,1) translate(0,0)" writing-mode="lr" x="1014.21" xml:space="preserve" y="488.11" zvalue="784">30117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089.67,506.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1089.67" xml:space="preserve" y="509.64" zvalue="786">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.19,580.593) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.19" xml:space="preserve" y="583.59" zvalue="788">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1091.86,732.201) scale(1,1) translate(0,0)" writing-mode="lr" x="1091.86" xml:space="preserve" y="735.2" zvalue="833">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1092.28,681.976) scale(1,1) translate(0,0)" writing-mode="lr" x="1092.28" xml:space="preserve" y="684.98" zvalue="835">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1086.93,810.076) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.93" xml:space="preserve" y="813.08" zvalue="840">0511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.73,915.326) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.73" xml:space="preserve" y="918.33" zvalue="842">0516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1085.46,863.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.46" xml:space="preserve" y="866.9299999999999" zvalue="844">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1235.92,810.076) scale(1,1) translate(0,0)" writing-mode="lr" x="1235.92" xml:space="preserve" y="813.08" zvalue="861">0521</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1233.73,911.326) scale(1,1) translate(0,0)" writing-mode="lr" x="1233.73" xml:space="preserve" y="914.33" zvalue="863">0526</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1234.46,859.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1234.46" xml:space="preserve" y="862.9299999999999" zvalue="865">052</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1583.65,555.654) scale(1,1) translate(0,0)" writing-mode="lr" x="1583.65" xml:space="preserve" y="558.65" zvalue="913">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.03,461.701) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.03" xml:space="preserve" y="464.7" zvalue="915">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1545.76,501.806) scale(1,1) translate(0,0)" writing-mode="lr" x="1545.76" xml:space="preserve" y="504.81" zvalue="919">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1545.26,444.621) scale(1,1) translate(0,0)" writing-mode="lr" x="1545.26" xml:space="preserve" y="447.62" zvalue="921">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1304.32,450.495) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.32" xml:space="preserve" y="453.49" zvalue="925">3721</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,751.125,935.775) scale(1,1) translate(0,0)" writing-mode="lr" x="751.13" xml:space="preserve" y="938.78" zvalue="961">10kV故障信号源</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.388,855.761) scale(1,1) translate(0,0)" writing-mode="lr" x="725.39" xml:space="preserve" y="860.26" zvalue="964">0611</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,597.851,935.775) scale(1,1) translate(0,0)" writing-mode="lr" x="597.85" xml:space="preserve" y="938.78" zvalue="969">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,624.994,803.576) scale(1,1) translate(0,0)" writing-mode="lr" x="624.99" xml:space="preserve" y="806.58" zvalue="971">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,578.591,854.007) scale(1,1) translate(0,0)" writing-mode="lr" x="578.59" xml:space="preserve" y="857.01" zvalue="973">0909</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.49,611.093) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.49" xml:space="preserve" y="614.09" zvalue="1001">2.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.268,1114.92) scale(1,1) translate(0,0)" writing-mode="lr" x="240.27" xml:space="preserve" y="1117.92" zvalue="1015">拉乌变-02-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,914.658,935.775) scale(1,1) translate(0,0)" writing-mode="lr" x="914.66" xml:space="preserve" y="938.78" zvalue="1022">10kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,944.026,809.451) scale(1,1) translate(0,0)" writing-mode="lr" x="944.03" xml:space="preserve" y="813.95" zvalue="1026">0801</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="382" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1389.92,810.076) scale(1,1) translate(0,0)" writing-mode="lr" x="1389.92" xml:space="preserve" y="813.08" zvalue="1047">0531</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="381" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1387.73,911.326) scale(1,1) translate(0,0)" writing-mode="lr" x="1387.73" xml:space="preserve" y="914.33" zvalue="1049">0536</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="380" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388.46,859.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1388.46" xml:space="preserve" y="862.9299999999999" zvalue="1051">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="405" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1329.08,551.652) scale(1,1) translate(0,0)" writing-mode="lr" x="1329.08" xml:space="preserve" y="556.15" zvalue="1063">35kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="408" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1094.76,293.435) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.76" xml:space="preserve" y="296.44" zvalue="1069">3716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="422" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.48,233.781) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.48" xml:space="preserve" y="237.78" zvalue="1081">37167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.9821,254.845) scale(1,1) translate(0,0)" writing-mode="lr" x="43.98" xml:space="preserve" y="260.85" zvalue="1140">事故</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.9821,321.595) scale(1,1) translate(0,0)" writing-mode="lr" x="54.98" xml:space="preserve" y="327.6" zvalue="1141">是否失压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,136.482,321.595) scale(1,1) translate(0,0)" writing-mode="lr" x="136.48" xml:space="preserve" y="327.6" zvalue="1143">失压排除</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,103.982,254.845) scale(1,1) translate(0,0)" writing-mode="lr" x="103.98" xml:space="preserve" y="260.85" zvalue="1145">异常</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,163.982,254.845) scale(1,1) translate(0,0)" writing-mode="lr" x="163.98" xml:space="preserve" y="260.85" zvalue="1146">告知</text>
  <line fill="none" id="100" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.00000000000023" x2="388.9999999999998" y1="131.8704926140824" y2="131.8704926140824" zvalue="1168"/>
  <line fill="none" id="96" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="390.6666666666667" x2="390.6666666666667" y1="8.333333333333371" y2="1198.333333333333" zvalue="1170"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="21" x2="202" y1="141" y2="141"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="21" x2="202" y1="167" y2="167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="21" x2="21" y1="141" y2="167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202" x2="202" y1="141" y2="167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202" x2="383" y1="141" y2="141"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202" x2="383" y1="167" y2="167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202" x2="202" y1="141" y2="167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="383" x2="383" y1="141" y2="167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="21" x2="202" y1="167" y2="167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="21" x2="202" y1="191.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="21" x2="21" y1="167" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202" x2="202" y1="167" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202" x2="383" y1="167" y2="167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202" x2="383" y1="191.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202" x2="202" y1="167" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="383" x2="383" y1="167" y2="191.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.1875,155) scale(1,1) translate(0,0)" writing-mode="lr" x="62.19" xml:space="preserve" y="158" zvalue="1172">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241,155) scale(1,1) translate(0,0)" writing-mode="lr" x="241" xml:space="preserve" y="158" zvalue="1173">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.6875,180.583) scale(1,1) translate(0,0)" writing-mode="lr" x="63.69" xml:space="preserve" y="183.58" zvalue="1176">#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.75,373.046) scale(1,1) translate(0,0)" writing-mode="lr" x="86.75" xml:space="preserve" y="376.05" zvalue="1178">调度数据网通道</text>
  <line fill="none" id="88" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000023" x2="388.2499999999998" y1="628.8704926140824" y2="628.8704926140824" zvalue="1180"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.9027,1111.26) scale(1,1) translate(0,0)" writing-mode="lr" x="50.9" xml:space="preserve" y="1114.26" zvalue="1186">图号</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,334.459,1153.5) scale(1,1) translate(0,0)" writing-mode="lr" x="334.46" xml:space="preserve" y="1156.5" zvalue="1188">2022-08-16</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.05952380952408" x2="106.0595238095241" y1="1085.815476190476" y2="1085.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.05952380952408" x2="106.0595238095241" y1="1137.978776190476" y2="1137.978776190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.05952380952408" x2="16.05952380952408" y1="1085.815476190476" y2="1137.978776190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="106.0595238095241" y1="1085.815476190476" y2="1137.978776190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="378.0595238095241" y1="1085.815476190476" y2="1085.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="378.0595238095241" y1="1137.978776190476" y2="1137.978776190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="106.0595238095241" y1="1085.815476190476" y2="1137.978776190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="378.0595238095241" x2="378.0595238095241" y1="1085.815476190476" y2="1137.978776190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.05952380952408" x2="106.0595238095241" y1="1137.978746190476" y2="1137.978746190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.05952380952408" x2="106.0595238095241" y1="1165.897146190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.05952380952408" x2="16.05952380952408" y1="1137.978746190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="106.0595238095241" y1="1137.978746190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="196.0595238095241" y1="1137.978746190476" y2="1137.978746190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="196.0595238095241" y1="1165.897146190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="106.0595238095241" y1="1137.978746190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0595238095241" x2="196.0595238095241" y1="1137.978746190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0595238095241" x2="286.0595238095241" y1="1137.978746190476" y2="1137.978746190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0595238095241" x2="286.0595238095241" y1="1165.897146190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0595238095241" x2="196.0595238095241" y1="1137.978746190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.0595238095241" x2="286.0595238095241" y1="1137.978746190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.0595238095241" x2="378.0595238095241" y1="1137.978746190476" y2="1137.978746190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.0595238095241" x2="378.0595238095241" y1="1165.897146190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.0595238095241" x2="286.0595238095241" y1="1137.978746190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="378.0595238095241" x2="378.0595238095241" y1="1137.978746190476" y2="1165.897146190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.05952380952408" x2="106.0595238095241" y1="1165.897076190476" y2="1165.897076190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.05952380952408" x2="106.0595238095241" y1="1193.815476190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.05952380952408" x2="16.05952380952408" y1="1165.897076190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="106.0595238095241" y1="1165.897076190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="196.0595238095241" y1="1165.897076190476" y2="1165.897076190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="196.0595238095241" y1="1193.815476190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.0595238095241" x2="106.0595238095241" y1="1165.897076190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0595238095241" x2="196.0595238095241" y1="1165.897076190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0595238095241" x2="286.0595238095241" y1="1165.897076190476" y2="1165.897076190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0595238095241" x2="286.0595238095241" y1="1193.815476190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0595238095241" x2="196.0595238095241" y1="1165.897076190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.0595238095241" x2="286.0595238095241" y1="1165.897076190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.0595238095241" x2="378.0595238095241" y1="1165.897076190476" y2="1165.897076190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.0595238095241" x2="378.0595238095241" y1="1193.815476190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.0595238095241" x2="286.0595238095241" y1="1165.897076190476" y2="1193.815476190476"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="378.0595238095241" x2="378.0595238095241" y1="1165.897076190476" y2="1193.815476190476"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="79" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,50.9027,1152.8) scale(1,1) translate(0,0)" writing-mode="lr" x="35.18" xml:space="preserve" y="1155.8" zvalue="1190">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="78" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232.682,1151.33) scale(1,1) translate(0,0)" writing-mode="lr" x="206.24" xml:space="preserve" y="1154.33" zvalue="1191">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.9027,1181.65) scale(1,1) translate(0,0)" writing-mode="lr" x="50.9" xml:space="preserve" y="1184.65" zvalue="1192">更新 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.682,1181.03) scale(1,1) translate(0,0)" writing-mode="lr" x="232.68" xml:space="preserve" y="1184.03" zvalue="1193">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="75" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,150.167,1154.03) scale(1,1) translate(0,0)" writing-mode="lr" x="130.08" xml:space="preserve" y="1157.03" zvalue="1194">杨碧磊</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.6607,645.792) scale(1,1) translate(0,0)" writing-mode="lr" x="96.66071428571399" xml:space="preserve" y="648.7916666666667" zvalue="1196">注意事项(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="444.25" y2="444.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="476.9167" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="48.99950555555586" y1="444.25" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0274055555559" x2="125.0274055555559" y1="444.25" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="444.25" y2="444.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="476.9167" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="125.0276055555559" y1="444.25" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0555055555559" x2="201.0555055555559" y1="444.25" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="444.25" y2="444.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="476.9167" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="201.0551055555559" y1="444.25" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.0830055555559" x2="277.0830055555559" y1="444.25" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="476.9167" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="503.5834" y2="503.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="48.99950555555586" y1="476.9167" y2="503.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0274055555559" x2="125.0274055555559" y1="476.9167" y2="503.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="476.9167" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="503.5834" y2="503.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="125.0276055555559" y1="476.9167" y2="503.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0555055555559" x2="201.0555055555559" y1="476.9167" y2="503.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="476.9167" y2="476.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="503.5834" y2="503.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="201.0551055555559" y1="476.9167" y2="503.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.0830055555559" x2="277.0830055555559" y1="476.9167" y2="503.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="503.5833" y2="503.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="530.25" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="48.99950555555586" y1="503.5833" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0274055555559" x2="125.0274055555559" y1="503.5833" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="503.5833" y2="503.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="530.25" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="125.0276055555559" y1="503.5833" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0555055555559" x2="201.0555055555559" y1="503.5833" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="503.5833" y2="503.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="530.25" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="201.0551055555559" y1="503.5833" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.0830055555559" x2="277.0830055555559" y1="503.5833" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="530.25" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="556.9167" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="48.99950555555586" y1="530.25" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0274055555559" x2="125.0274055555559" y1="530.25" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="530.25" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="556.9167" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="125.0276055555559" y1="530.25" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0555055555559" x2="201.0555055555559" y1="530.25" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="530.25" y2="530.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="556.9167" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="201.0551055555559" y1="530.25" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.0830055555559" x2="277.0830055555559" y1="530.25" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="556.9167" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="583.5834" y2="583.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="48.99950555555586" y1="556.9167" y2="583.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0274055555559" x2="125.0274055555559" y1="556.9167" y2="583.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="556.9167" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="583.5834" y2="583.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="125.0276055555559" y1="556.9167" y2="583.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0555055555559" x2="201.0555055555559" y1="556.9167" y2="583.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="556.9167" y2="556.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="583.5834" y2="583.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="201.0551055555559" y1="556.9167" y2="583.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.0830055555559" x2="277.0830055555559" y1="556.9167" y2="583.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="583.5833" y2="583.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="125.0274055555559" y1="610.25" y2="610.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="48.99950555555586" x2="48.99950555555586" y1="583.5833" y2="610.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0274055555559" x2="125.0274055555559" y1="583.5833" y2="610.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="583.5833" y2="583.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="201.0555055555559" y1="610.25" y2="610.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="125.0276055555559" x2="125.0276055555559" y1="583.5833" y2="610.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0555055555559" x2="201.0555055555559" y1="583.5833" y2="610.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="583.5833" y2="583.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="277.0830055555559" y1="610.25" y2="610.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.0551055555559" x2="201.0551055555559" y1="583.5833" y2="610.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.0830055555559" x2="277.0830055555559" y1="583.5833" y2="610.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.088,460.75) scale(1,1) translate(0,0)" writing-mode="lr" x="241.0881364073007" xml:space="preserve" y="463.75" zvalue="1198">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,493.75) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="498.25" zvalue="1200">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,519.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="523.75" zvalue="1201">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,544.75) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="549.25" zvalue="1202">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,570.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="574.75" zvalue="1203">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,595.75) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="600.25" zvalue="1204">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,160.745,460.75) scale(1,1) translate(0,0)" writing-mode="lr" x="160.7451969260329" xml:space="preserve" y="463.75" zvalue="1205">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.75,413.046) scale(1,1) translate(0,0)" writing-mode="lr" x="86.75" xml:space="preserve" y="416.05" zvalue="1212">104移动2M通道</text>
  <line fill="none" id="37" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="202.166666666667" x2="202.166666666667" y1="197.5000000000001" y2="441.4166666666669" zvalue="1224"/>
  <line fill="none" id="36" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.42857142857144" x2="200.6666666666666" y1="347.8704926140824" y2="347.8704926140824" zvalue="1225"/>
 </g>
 <g id="ACLineSegmentClass">
  <g id="39">
   <use class="kv35" height="30" transform="rotate(0,1060.58,149.551) scale(2.82817,1.15097) translate(-679.174,-17.3514)" width="7" x="1050.67991253934" xlink:href="#ACLineSegment:线路_0" y="132.2867820896591" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249343983618" ObjectName="35kV平拉新线拉乌支线"/>
   <cge:TPSR_Ref TObjectID="8444249343983618_5066549584592898"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1060.58,149.551) scale(2.82817,1.15097) translate(-679.174,-17.3514)" width="7" x="1050.67991253934" y="132.2867820896591"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="83">
   <use class="kv10" height="30" transform="rotate(180,1062.34,1023.89) scale(1.25,1.23333) translate(-210.969,-190.209)" width="12" x="1054.84393310292" xlink:href="#EnergyConsumer:负荷_0" y="1005.388885498047" zvalue="694"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875017734" ObjectName="10kV新田线"/>
   <cge:TPSR_Ref TObjectID="6192449875017734"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1062.34,1023.89) scale(1.25,1.23333) translate(-210.969,-190.209)" width="12" x="1054.84393310292" y="1005.388885498047"/></g>
  <g id="106">
   <use class="kv10" height="30" transform="rotate(180,1211.28,1023.89) scale(1.25,1.23333) translate(-240.755,-190.209)" width="12" x="1203.775959042146" xlink:href="#EnergyConsumer:负荷_0" y="1005.388885392083" zvalue="711"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875148806" ObjectName="10kV桃园箐电站线"/>
   <cge:TPSR_Ref TObjectID="6192449875148806"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1211.28,1023.89) scale(1.25,1.23333) translate(-240.755,-190.209)" width="12" x="1203.775959042146" y="1005.388885392083"/></g>
  <g id="163">
   <use class="kv10" height="30" transform="rotate(180,1365.28,1024.85) scale(1.25,1.23333) translate(-271.555,-190.39)" width="12" x="1357.775959042146" xlink:href="#EnergyConsumer:负荷_0" y="1006.348489616856" zvalue="731"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875410950" ObjectName="10kV大厂电站线"/>
   <cge:TPSR_Ref TObjectID="6192449875410950"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1365.28,1024.85) scale(1.25,1.23333) translate(-271.555,-190.39)" width="12" x="1357.775959042146" y="1006.348489616856"/></g>
  <g id="224">
   <use class="kv10" height="30" transform="rotate(0,750.188,904.396) scale(1.15972,1.15972) translate(-101.882,-122.162)" width="18" x="739.7501220703125" xlink:href="#EnergyConsumer:站用变_0" y="887" zvalue="960"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874755590" ObjectName="10kV故障信号源"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,750.188,904.396) scale(1.15972,1.15972) translate(-101.882,-122.162)" width="18" x="739.7501220703125" y="887"/></g>
  <g id="346">
   <use class="kv10" height="30" transform="rotate(0,916.293,888.143) scale(2.90545,2.34603) translate(-581.868,-489.379)" width="20" x="887.2381018629279" xlink:href="#EnergyConsumer:站用变带负荷_0" y="852.9525567245804" zvalue="1021"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875738630" ObjectName="10kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,916.293,888.143) scale(2.90545,2.34603) translate(-581.868,-489.379)" width="20" x="887.2381018629279" y="852.9525567245804"/></g>
  <g id="1537">
   <use class="kv35" height="40" transform="rotate(0,1334.01,505.777) scale(1.65909,1.65909) translate(-521.709,-187.743)" width="25" x="1313.267748917749" xlink:href="#EnergyConsumer: 接地变2_0" y="472.5946969696971" zvalue="1062"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874034694" ObjectName="35kV2号站用变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1334.01,505.777) scale(1.65909,1.65909) translate(-521.709,-187.743)" width="25" x="1313.267748917749" y="472.5946969696971"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="512">
   <path class="kv10" d="M 515.13 772.14 L 1454.29 772.14" stroke-width="6" zvalue="775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674298036227" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674298036227"/></metadata>
  <path d="M 515.13 772.14 L 1454.29 772.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="338">
   <path class="kv35" d="M 906.11 407.33 L 1689.92 407.33" stroke-width="6" zvalue="779"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674297970691" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674297970691"/></metadata>
  <path d="M 906.11 407.33 L 1689.92 407.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="335">
   <use class="kv35" height="30" transform="rotate(0,1060.67,443.951) scale(-1.11133,0.814667) translate(-2014.25,98.2171)" width="15" x="1052.338133855334" xlink:href="#Disconnector:刀闸_0" y="431.7313732336636" zvalue="781"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449873510406" ObjectName="#1主变35kV侧3011"/>
   <cge:TPSR_Ref TObjectID="6192449873510406"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1060.67,443.951) scale(-1.11133,0.814667) translate(-2014.25,98.2171)" width="15" x="1052.338133855334" y="431.7313732336636"/></g>
  <g id="298">
   <use class="kv10" height="30" transform="rotate(0,1062.33,730.451) scale(-1.11133,0.814667) translate(-2017.4,163.395)" width="15" x="1053.99598348419" xlink:href="#Disconnector:刀闸_0" y="718.2313732336636" zvalue="832"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874624518" ObjectName="#1主变10kV侧0011"/>
   <cge:TPSR_Ref TObjectID="6192449874624518"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062.33,730.451) scale(-1.11133,0.814667) translate(-2017.4,163.395)" width="15" x="1053.99598348419" y="718.2313732336636"/></g>
  <g id="293">
   <use class="kv10" height="30" transform="rotate(0,1062.36,810.451) scale(-1.11133,0.814667) translate(-2017.46,181.594)" width="15" x="1054.025558983391" xlink:href="#Disconnector:刀闸_0" y="798.2313690185547" zvalue="839"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874821126" ObjectName="10kV新田线0511"/>
   <cge:TPSR_Ref TObjectID="6192449874821126"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062.36,810.451) scale(-1.11133,0.814667) translate(-2017.46,181.594)" width="15" x="1054.025558983391" y="798.2313690185547"/></g>
  <g id="292">
   <use class="kv10" height="30" transform="rotate(0,1062.34,914.451) scale(-1.11133,0.814667) translate(-2017.43,205.254)" width="15" x="1054.008933105469" xlink:href="#Disconnector:刀闸_0" y="902.2313845695685" zvalue="841"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874886662" ObjectName="10kV新田线0516"/>
   <cge:TPSR_Ref TObjectID="6192449874886662"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062.34,914.451) scale(-1.11133,0.814667) translate(-2017.43,205.254)" width="15" x="1054.008933105469" y="902.2313845695685"/></g>
  <g id="314">
   <use class="kv10" height="30" transform="rotate(0,1211.36,810.451) scale(-1.11133,0.814667) translate(-2300.52,181.594)" width="15" x="1203.020659989476" xlink:href="#Disconnector:刀闸_0" y="798.2313690723611" zvalue="860"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875345414" ObjectName="10kV桃园箐电站线0521"/>
   <cge:TPSR_Ref TObjectID="6192449875345414"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1211.36,810.451) scale(-1.11133,0.814667) translate(-2300.52,181.594)" width="15" x="1203.020659989476" y="798.2313690723611"/></g>
  <g id="285">
   <use class="kv10" height="30" transform="rotate(0,1211.34,910.451) scale(-1.11133,0.814667) translate(-2300.5,204.344)" width="15" x="1203.008933105469" xlink:href="#Disconnector:刀闸_0" y="898.2313845695685" zvalue="862"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875279878" ObjectName="10kV桃园箐电站线0526"/>
   <cge:TPSR_Ref TObjectID="6192449875279878"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1211.34,910.451) scale(-1.11133,0.814667) translate(-2300.5,204.344)" width="15" x="1203.008933105469" y="898.2313845695685"/></g>
  <g id="256">
   <use class="kv35" height="30" transform="rotate(0,1592.67,460.785) scale(-1.11133,0.814667) translate(-3024.96,102.047)" width="15" x="1584.338858642578" xlink:href="#Disconnector:刀闸_0" y="448.564706566997" zvalue="914"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874427910" ObjectName="35kV母线电压互感器3901"/>
   <cge:TPSR_Ref TObjectID="6192449874427910"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1592.67,460.785) scale(-1.11133,0.814667) translate(-3024.96,102.047)" width="15" x="1584.338858642578" y="448.564706566997"/></g>
  <g id="233">
   <use class="kv10" height="30" transform="rotate(0,601.702,806.451) scale(-1.11133,0.814667) translate(-1142.29,180.684)" width="15" x="593.3668955775663" xlink:href="#Disconnector:刀闸_0" y="794.2313690448248" zvalue="970"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875869702" ObjectName="10kV母线电压互感器0901"/>
   <cge:TPSR_Ref TObjectID="6192449875869702"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,601.702,806.451) scale(-1.11133,0.814667) translate(-1142.29,180.684)" width="15" x="593.3668955775663" y="794.2313690448248"/></g>
  <g id="349">
   <use class="kv10" height="30" transform="rotate(0,916.361,810.451) scale(-1.11133,0.814667) translate(-1740.09,181.594)" width="15" x="908.0255589833907" xlink:href="#Disconnector:刀闸_0" y="798.2313690185547" zvalue="1025"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875673094" ObjectName="10kV1号站用变0801"/>
   <cge:TPSR_Ref TObjectID="6192449875673094"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,916.361,810.451) scale(-1.11133,0.814667) translate(-1740.09,181.594)" width="15" x="908.0255589833907" y="798.2313690185547"/></g>
  <g id="397">
   <use class="kv10" height="30" transform="rotate(0,1365.36,810.451) scale(-1.11133,0.814667) translate(-2593.1,181.594)" width="15" x="1357.020659989476" xlink:href="#Disconnector:刀闸_0" y="798.2313690723611" zvalue="1046"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875607558" ObjectName="10kV大厂电站线0531"/>
   <cge:TPSR_Ref TObjectID="6192449875607558"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1365.36,810.451) scale(-1.11133,0.814667) translate(-2593.1,181.594)" width="15" x="1357.020659989476" y="798.2313690723611"/></g>
  <g id="396">
   <use class="kv10" height="30" transform="rotate(0,1365.34,910.451) scale(-1.11133,0.814667) translate(-2593.07,204.344)" width="15" x="1357.008933105469" xlink:href="#Disconnector:刀闸_0" y="898.2313845695685" zvalue="1048"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875542022" ObjectName="10kV大厂电站线0536"/>
   <cge:TPSR_Ref TObjectID="6192449875542022"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1365.34,910.451) scale(-1.11133,0.814667) translate(-2593.07,204.344)" width="15" x="1357.008933105469" y="898.2313845695685"/></g>
  <g id="416">
   <use class="kv35" height="30" transform="rotate(0,1060.67,292.594) scale(-1.11133,0.814667) translate(-2014.25,63.784)" width="15" x="1052.336717322849" xlink:href="#Disconnector:刀闸_0" y="280.3742303765209" zvalue="1068"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449873903622" ObjectName="35kV平拉新线3716"/>
   <cge:TPSR_Ref TObjectID="6192449873903622"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1060.67,292.594) scale(-1.11133,0.814667) translate(-2014.25,63.784)" width="15" x="1052.336717322849" y="280.3742303765209"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="334">
   <use class="kv35" height="30" transform="rotate(90,1015.48,470.002) scale(1.01422,0.867474) translate(-14.1557,69.8154)" width="12" x="1009.399395824138" xlink:href="#GroundDisconnector:地刀12_0" y="456.9897393299883" zvalue="783"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449873444870" ObjectName="#1主变35kV侧30117"/>
   <cge:TPSR_Ref TObjectID="6192449873444870"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1015.48,470.002) scale(1.01422,0.867474) translate(-14.1557,69.8154)" width="12" x="1009.399395824138" y="456.9897393299883"/></g>
  <g id="252">
   <use class="kv35" height="30" transform="rotate(270,1546.76,486.887) scale(-1.01422,-0.867474) translate(-3071.74,-1050.15)" width="12" x="1540.672139128838" xlink:href="#GroundDisconnector:地刀12_0" y="473.8751770599241" zvalue="918"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874362374" ObjectName="35kV母线电压互感器39017"/>
   <cge:TPSR_Ref TObjectID="6192449874362374"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1546.76,486.887) scale(-1.01422,-0.867474) translate(-3071.74,-1050.15)" width="12" x="1540.672139128838" y="473.8751770599241"/></g>
  <g id="251">
   <use class="kv35" height="30" transform="rotate(270,1546.76,431.536) scale(-1.01422,-0.867474) translate(-3071.74,-930.987)" width="12" x="1540.672139128838" xlink:href="#GroundDisconnector:地刀12_0" y="418.5240029617604" zvalue="920"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452106977283" ObjectName="35kV母线电压互感器39010"/>
   <cge:TPSR_Ref TObjectID="6192452106977283"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1546.76,431.536) scale(-1.01422,-0.867474) translate(-3071.74,-930.987)" width="12" x="1540.672139128838" y="418.5240029617604"/></g>
  <g id="418">
   <use class="kv35" height="30" transform="rotate(90,1009.48,218.388) scale(1.01422,0.867474) translate(-14.0716,31.3758)" width="12" x="1003.399395824138" xlink:href="#GroundDisconnector:地刀12_0" y="205.3760097332567" zvalue="1080"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449873707014" ObjectName="35kV平拉新线37167"/>
   <cge:TPSR_Ref TObjectID="6192449873707014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1009.48,218.388) scale(1.01422,0.867474) translate(-14.0716,31.3758)" width="12" x="1003.399395824138" y="205.3760097332567"/></g>
 </g>
 <g id="BreakerClass">
  <g id="333">
   <use class="kv35" height="20" transform="rotate(0,1060.33,506.31) scale(1.83111,1.88671) translate(-477.112,-229.086)" width="10" x="1051.177775946961" xlink:href="#Breaker:开关_0" y="487.4426475982898" zvalue="785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513693701" ObjectName="#1主变35kV侧301"/>
   <cge:TPSR_Ref TObjectID="6473924513693701"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1060.33,506.31) scale(1.83111,1.88671) translate(-477.112,-229.086)" width="10" x="1051.177775946961" y="487.4426475982898"/></g>
  <g id="297">
   <use class="kv10" height="20" transform="rotate(0,1062.11,682.476) scale(1.83111,1.88671) translate(-477.919,-311.88)" width="10" x="1052.955802431082" xlink:href="#Breaker:开关_0" y="663.6093142649564" zvalue="834"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924766269445" ObjectName="#1主变10kV侧001"/>
   <cge:TPSR_Ref TObjectID="6473924766269445"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1062.11,682.476) scale(1.83111,1.88671) translate(-477.919,-311.88)" width="10" x="1052.955802431082" y="663.6093142649564"/></g>
  <g id="291">
   <use class="kv10" height="20" transform="rotate(0,1062.34,861.81) scale(1.83111,1.88671) translate(-478.025,-396.163)" width="10" x="1053.188375712558" xlink:href="#Breaker:开关_0" y="842.9426422882432" zvalue="843"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513824773" ObjectName="10kV新田线051"/>
   <cge:TPSR_Ref TObjectID="6473924513824773"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1062.34,861.81) scale(1.83111,1.88671) translate(-478.025,-396.163)" width="10" x="1053.188375712558" y="842.9426422882432"/></g>
  <g id="284">
   <use class="kv10" height="20" transform="rotate(0,1211.34,857.81) scale(1.83111,1.88671) translate(-545.654,-394.283)" width="10" x="1202.188375712558" xlink:href="#Breaker:开关_0" y="838.9426422882432" zvalue="864"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513890309" ObjectName="10kV桃园箐电站线052"/>
   <cge:TPSR_Ref TObjectID="6473924513890309"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1211.34,857.81) scale(1.83111,1.88671) translate(-545.654,-394.283)" width="10" x="1202.188375712558" y="838.9426422882432"/></g>
  <g id="395">
   <use class="kv10" height="20" transform="rotate(0,1365.34,857.81) scale(1.83111,1.88671) translate(-615.552,-394.283)" width="10" x="1356.188375712558" xlink:href="#Breaker:开关_0" y="838.9426422882432" zvalue="1050"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513955845" ObjectName="10kV大厂电站线053"/>
   <cge:TPSR_Ref TObjectID="6473924513955845"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1365.34,857.81) scale(1.83111,1.88671) translate(-615.552,-394.283)" width="10" x="1356.188375712558" y="838.9426422882432"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="340">
   <g id="3400">
    <use class="kv35" height="30" transform="rotate(0,1060.94,589.593) scale(3.09833,2.96282) translate(-693.336,-361.153)" width="24" x="1023.76" xlink:href="#PowerTransformer2:可调两卷变_0" y="545.15" zvalue="787"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431660036" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3401">
    <use class="kv10" height="30" transform="rotate(0,1060.94,589.593) scale(3.09833,2.96282) translate(-693.336,-361.153)" width="24" x="1023.76" xlink:href="#PowerTransformer2:可调两卷变_1" y="545.15" zvalue="787"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431725572" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447478276" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447478276"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1060.94,589.593) scale(3.09833,2.96282) translate(-693.336,-361.153)" width="24" x="1023.76" y="545.15"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="332">
   <path class="kv35" d="M 1060.58 432.14 L 1060.58 407.33" stroke-width="1" zvalue="789"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@0" LinkObjectIDznd="338@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.58 432.14 L 1060.58 407.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="331">
   <path class="kv35" d="M 1060.61 455.96 L 1060.61 488.26" stroke-width="1" zvalue="790"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@1" LinkObjectIDznd="333@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.61 455.96 L 1060.61 488.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="kv35" d="M 1060.46 524.33 L 1060.46 548.34" stroke-width="1" zvalue="791"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333@1" LinkObjectIDznd="340@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.46 524.33 L 1060.46 548.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="329">
   <path class="kv35" d="M 1027.91 469.99 L 1060.61 469.99" stroke-width="1" zvalue="792"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="334@0" LinkObjectIDznd="331" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.91 469.99 L 1060.61 469.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv10" d="M 1062.23 718.64 L 1062.23 700.49" stroke-width="1" zvalue="836"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="297@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.23 718.64 L 1062.23 700.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv10" d="M 1062.05 664.43 L 1062.05 631.21" stroke-width="1" zvalue="837"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="340@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.05 664.43 L 1062.05 631.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv10" d="M 1062.26 742.46 L 1062.26 772.14" stroke-width="1" zvalue="838"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@1" LinkObjectIDznd="512@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.26 742.46 L 1062.26 772.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv10" d="M 1062.29 822.46 L 1062.28 843.76" stroke-width="1" zvalue="845"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@1" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.29 822.46 L 1062.28 843.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv10" d="M 1062.47 879.83 L 1062.47 902.64" stroke-width="1" zvalue="846"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@1" LinkObjectIDznd="292@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.47 879.83 L 1062.47 902.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv10" d="M 1211.29 822.46 L 1211.28 839.76" stroke-width="1" zvalue="866"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@1" LinkObjectIDznd="284@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.29 822.46 L 1211.28 839.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv10" d="M 1211.47 875.83 L 1211.47 898.64" stroke-width="1" zvalue="867"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@1" LinkObjectIDznd="285@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.47 875.83 L 1211.47 898.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv10" d="M 1211.28 1007.24 L 1211.28 922.46" stroke-width="1" zvalue="872"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="285@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.28 1007.24 L 1211.28 922.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv35" d="M 1592.58 448.97 L 1592.58 407.33" stroke-width="1" zvalue="916"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="338@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1592.58 448.97 L 1592.58 407.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv35" d="M 1592.61 472.8 L 1592.61 503.66" stroke-width="1" zvalue="917"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@1" LinkObjectIDznd="257@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1592.61 472.8 L 1592.61 503.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv35" d="M 1559.18 431.52 L 1592.58 431.52" stroke-width="1" zvalue="922"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="255" MaxPinNum="2"/>
   </metadata>
  <path d="M 1559.18 431.52 L 1592.58 431.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv35" d="M 1559.18 486.88 L 1592.61 486.88" stroke-width="1" zvalue="923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="253" MaxPinNum="2"/>
   </metadata>
  <path d="M 1559.18 486.88 L 1592.61 486.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv35" d="M 1330.19 407.33 L 1330.19 443.2" stroke-width="1" zvalue="926"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="248@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1330.19 407.33 L 1330.19 443.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv35" d="M 1330.19 442.23 L 1330.19 475.08" stroke-width="1" zvalue="927"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247" LinkObjectIDznd="1537@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1330.19 442.23 L 1330.19 475.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv10" d="M 601.6 794.64 L 601.6 772.14" stroke-width="1" zvalue="974"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="512@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.6 794.64 L 601.6 772.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 601.63 818.46 L 601.69 846.24" stroke-width="1" zvalue="975"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.63 818.46 L 601.69 846.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv10" d="M 601.69 846.18 L 601.7 875.23" stroke-width="1" zvalue="976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.69 846.18 L 601.7 875.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 601.66 831.51 L 624.06 831.51 L 624.06 850.09" stroke-width="1" zvalue="978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211" LinkObjectIDznd="449@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.66 831.51 L 624.06 831.51 L 624.06 850.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 1211.28 946.67 L 1186.39 946.67 L 1186.39 971.74" stroke-width="1" zvalue="990"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272" LinkObjectIDznd="279@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.28 946.67 L 1186.39 946.67 L 1186.39 971.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="353">
   <path class="kv10" d="M 916.29 822.46 L 916.29 854.95" stroke-width="1" zvalue="1028"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@1" LinkObjectIDznd="346@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.29 822.46 L 916.29 854.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="357">
   <path class="kv10" d="M 916.26 798.64 L 916.26 772.14" stroke-width="1" zvalue="1031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@0" LinkObjectIDznd="512@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.26 798.64 L 916.26 772.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="359">
   <path class="kv10" d="M 916.29 832.67 L 944.39 832.67 L 944.39 845.84" stroke-width="1" zvalue="1034"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="353" LinkObjectIDznd="358@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.29 832.67 L 944.39 832.67 L 944.39 845.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="376">
   <path class="kv10" d="M 1062.28 926.46 L 1062.28 966" stroke-width="1" zvalue="1039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@1" LinkObjectIDznd="362@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.28 926.46 L 1062.28 966" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="377">
   <path class="kv10" d="M 1062.28 966 L 1062.34 1007.24" stroke-width="1" zvalue="1040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="376" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.28 966 L 1062.34 1007.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="378">
   <path class="kv10" d="M 1037.39 975.74 L 1037.39 945.31 L 1062.28 945.31" stroke-width="1" zvalue="1041"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@0" LinkObjectIDznd="376" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.39 975.74 L 1037.39 945.31 L 1062.28 945.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="379">
   <path class="kv10" d="M 1211.26 798.64 L 1211.26 772.14" stroke-width="1" zvalue="1042"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="512@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.26 798.64 L 1211.26 772.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="kv10" d="M 1365.29 822.46 L 1365.28 839.76" stroke-width="1" zvalue="1052"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="397@1" LinkObjectIDznd="395@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.29 822.46 L 1365.28 839.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="391">
   <path class="kv10" d="M 1365.47 875.83 L 1365.47 898.64" stroke-width="1" zvalue="1053"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="395@1" LinkObjectIDznd="396@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.47 875.83 L 1365.47 898.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="389">
   <path class="kv10" d="M 1365.28 1008.2 L 1365.28 922.46" stroke-width="1" zvalue="1055"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="396@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.28 1008.2 L 1365.28 922.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="388">
   <path class="kv10" d="M 1365.28 946.67 L 1340.39 946.67 L 1340.39 971.74" stroke-width="1" zvalue="1056"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389" LinkObjectIDznd="390@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.28 946.67 L 1340.39 946.67 L 1340.39 971.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="387">
   <path class="kv10" d="M 1365.26 798.64 L 1365.26 772.14" stroke-width="1" zvalue="1057"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="397@0" LinkObjectIDznd="512@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.26 798.64 L 1365.26 772.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="402">
   <path class="kv10" d="M 750.17 846.24 L 750.17 887.29" stroke-width="1" zvalue="1058"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="224@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 750.17 846.24 L 750.17 887.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="403">
   <path class="kv10" d="M 750.17 846.24 L 750.17 772.14" stroke-width="1" zvalue="1059"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="402" LinkObjectIDznd="512@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 750.17 846.24 L 750.17 772.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="404">
   <path class="kv10" d="M 750.17 816.96 L 774.39 816.96 L 774.39 837.84" stroke-width="1" zvalue="1060"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403" LinkObjectIDznd="223@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 750.17 816.96 L 774.39 816.96 L 774.39 837.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="406">
   <path class="kv35" d="M 1355.55 428.22 L 1330.19 428.22" stroke-width="1" zvalue="1065"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="671@0" LinkObjectIDznd="247" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.55 428.22 L 1330.19 428.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="413">
   <path class="kv35" d="M 1060.57 280.78 L 1060.58 166.64" stroke-width="1" zvalue="1073"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="416@0" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.57 280.78 L 1060.58 166.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="412">
   <path class="kv35" d="M 1103.11 218.38 L 1060.58 218.38" stroke-width="1" zvalue="1074"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="413" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.11 218.38 L 1060.58 218.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="419">
   <path class="kv35" d="M 1060.6 304.61 L 1060.6 332.65" stroke-width="1" zvalue="1081"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="416@1" LinkObjectIDznd="410@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.6 304.61 L 1060.6 332.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="420">
   <path class="kv35" d="M 1060.6 332.65 L 1060.6 407.33" stroke-width="1" zvalue="1082"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="419" LinkObjectIDznd="338@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.6 332.65 L 1060.6 407.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="421">
   <path class="kv35" d="M 1021.91 218.38 L 1060.58 218.38" stroke-width="1" zvalue="1083"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@0" LinkObjectIDznd="413" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.91 218.38 L 1060.58 218.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv10" d="M 1062.26 798.64 L 1062.26 772.14" stroke-width="1" zvalue="1147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@0" LinkObjectIDznd="512@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.26 798.64 L 1062.26 772.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="286">
   <use class="kv10" height="26" transform="rotate(0,1037.42,988.125) scale(-1.0017,1.0017) translate(-2073.08,-1.65692)" width="12" x="1031.41406899719" xlink:href="#Accessory:避雷器1_0" y="975.1031189214821" zvalue="850"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874952198" ObjectName="10kV新田线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1037.42,988.125) scale(-1.0017,1.0017) translate(-2073.08,-1.65692)" width="12" x="1031.41406899719" y="975.1031189214821"/></g>
  <g id="279">
   <use class="kv10" height="26" transform="rotate(0,1186.42,984.125) scale(-1.0017,1.0017) translate(-2370.82,-1.65013)" width="12" x="1180.41406899719" xlink:href="#Accessory:避雷器1_0" y="971.1031189214821" zvalue="871"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875214342" ObjectName="10kV桃园箐电站线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1186.42,984.125) scale(-1.0017,1.0017) translate(-2370.82,-1.65013)" width="12" x="1180.41406899719" y="971.1031189214821"/></g>
  <g id="257">
   <use class="kv35" height="18" transform="rotate(0,1594.79,523.308) scale(2.35043,2.35043) translate(-906.152,-288.51)" width="15" x="1577.163394980411" xlink:href="#Accessory:PT8_0" y="502.1538461538462" zvalue="912"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874558982" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1594.79,523.308) scale(2.35043,2.35043) translate(-906.152,-288.51)" width="15" x="1577.163394980411" y="502.1538461538462"/></g>
  <g id="248">
   <use class="kv35" height="18" transform="rotate(0,1330.21,451.495) scale(-0.903738,1.04776) translate(-2802.58,-20.1512)" width="10" x="1325.686846879769" xlink:href="#Accessory:熔断器_0" y="442.0650977421152" zvalue="924"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449873969158" ObjectName="35kV2号站用变3721"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1330.21,451.495) scale(-0.903738,1.04776) translate(-2802.58,-20.1512)" width="10" x="1325.686846879769" y="442.0650977421152"/></g>
  <g id="223">
   <use class="kv10" height="26" transform="rotate(0,774.424,850.227) scale(-1.0017,1.0017) translate(-1547.52,-1.4226)" width="12" x="768.4140296942484" xlink:href="#Accessory:避雷器1_0" y="837.2051451456184" zvalue="962"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874690054" ObjectName="10kV故障信号源避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,774.424,850.227) scale(-1.0017,1.0017) translate(-1547.52,-1.4226)" width="12" x="768.4140296942484" y="837.2051451456184"/></g>
  <g id="216">
   <use class="kv10" height="18" transform="rotate(0,750.188,854.531) scale(-0.903738,1.04776) translate(-1580.76,-38.5232)" width="10" x="745.6689304542192" xlink:href="#Accessory:熔断器_0" y="845.1011659253591" zvalue="963"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449876000774" ObjectName="10kV故障信号源0611"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,750.188,854.531) scale(-0.903738,1.04776) translate(-1580.76,-38.5232)" width="10" x="745.6689304542192" y="845.1011659253591"/></g>
  <g id="234">
   <use class="kv10" height="18" transform="rotate(0,601.702,895.758) scale(1.95869,2.35043) translate(-285.878,-502.5)" width="18" x="584.0736904448675" xlink:href="#Accessory:四卷PT_0" y="874.6040626040626" zvalue="968"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875935238" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,601.702,895.758) scale(1.95869,2.35043) translate(-285.878,-502.5)" width="18" x="584.0736904448675" y="874.6040626040626"/></g>
  <g id="232">
   <use class="kv10" height="18" transform="rotate(0,601.702,854.531) scale(-0.903738,1.04776) translate(-1267.98,-38.5232)" width="10" x="597.1832039472645" xlink:href="#Accessory:熔断器_0" y="845.1011657714845" zvalue="972"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449876066310" ObjectName="10kV母线电压互感器0909"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,601.702,854.531) scale(-0.903738,1.04776) translate(-1267.98,-38.5232)" width="10" x="597.1832039472645" y="845.1011657714845"/></g>
  <g id="449">
   <use class="kv10" height="26" transform="rotate(0,624.091,862.48) scale(-1.0017,1.0017) translate(-1247.11,-1.44342)" width="12" x="618.0806963609148" xlink:href="#Accessory:避雷器1_0" y="849.457962377614" zvalue="977"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449876131846" ObjectName="10kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,624.091,862.48) scale(-1.0017,1.0017) translate(-1247.11,-1.44342)" width="12" x="618.0806963609148" y="849.457962377614"/></g>
  <g id="358">
   <use class="kv10" height="26" transform="rotate(0,944.424,858.227) scale(-1.0017,1.0017) translate(-1887.23,-1.4362)" width="12" x="938.4140296942484" xlink:href="#Accessory:避雷器1_0" y="845.2051451456184" zvalue="1033"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875804166" ObjectName="10kV1号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,944.424,858.227) scale(-1.0017,1.0017) translate(-1887.23,-1.4362)" width="12" x="938.4140296942484" y="845.2051451456184"/></g>
  <g id="362">
   <use class="kv10" height="22" transform="rotate(180,1062.34,975.995) scale(1,-1) translate(0,-1951.99)" width="12" x="1056.343933852345" xlink:href="#Accessory:传输线_0" y="964.9951159951158" zvalue="1037"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452107567107" ObjectName="10kV新田线中间电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1062.34,975.995) scale(1,-1) translate(0,-1951.99)" width="12" x="1056.343933852345" y="964.9951159951158"/></g>
  <g id="390">
   <use class="kv10" height="26" transform="rotate(0,1340.42,984.125) scale(-1.0017,1.0017) translate(-2678.56,-1.65013)" width="12" x="1334.41406899719" xlink:href="#Accessory:避雷器1_0" y="971.1031189214821" zvalue="1054"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449875476486" ObjectName="10kV大厂电站线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1340.42,984.125) scale(-1.0017,1.0017) translate(-2678.56,-1.65013)" width="12" x="1334.41406899719" y="971.1031189214821"/></g>
  <g id="671">
   <use class="kv35" height="26" transform="rotate(270,1367.94,428.191) scale(-1.0017,1.0017) translate(-2733.54,-0.705467)" width="12" x="1361.928651943507" xlink:href="#Accessory:避雷器1_0" y="415.1689651848168" zvalue="1064"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449874100230" ObjectName="35kV2号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1367.94,428.191) scale(-1.0017,1.0017) translate(-2733.54,-0.705467)" width="12" x="1361.928651943507" y="415.1689651848168"/></g>
  <g id="414">
   <use class="kv35" height="26" transform="rotate(270,1115.5,218.41) scale(1.0017,1.0017) translate(-1.88528,-0.349001)" width="12" x="1109.489283648701" xlink:href="#Accessory:避雷器1_0" y="205.3877526431977" zvalue="1072"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449873772550" ObjectName="35kV平拉新线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1115.5,218.41) scale(1.0017,1.0017) translate(-1.88528,-0.349001)" width="12" x="1109.489283648701" y="205.3877526431977"/></g>
  <g id="410">
   <use class="kv35" height="12" transform="rotate(0,1060.6,352.211) scale(10.6427,7.52525) translate(-854.879,-266.256)" width="22" x="943.5344052119908" xlink:href="#Accessory:高压计量装置_0" y="307.0598290598292" zvalue="1076"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452107763715" ObjectName="35kV高压计量装置"/>
   </metadata>
  <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(0,1060.6,352.211) scale(10.6427,7.52525) translate(-854.879,-266.256)" width="22" x="943.5344052119908" y="307.0598290598292"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1648.61,315.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.61" xml:space="preserve" y="322.1" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064571396" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,543.633,678.643) scale(1,1) translate(0,0)" writing-mode="lr" x="543.63" xml:space="preserve" y="684.91" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065095684" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1648.61,333.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.61" xml:space="preserve" y="340.1" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064636932" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,543.633,696.643) scale(1,1) translate(0,0)" writing-mode="lr" x="543.63" xml:space="preserve" y="702.91" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065161220" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1648.61,351.833) scale(1,1) translate(0,-6.09883e-13)" writing-mode="lr" x="1648.61" xml:space="preserve" y="358.1" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064702468" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,543.633,714.643) scale(1,1) translate(0,0)" writing-mode="lr" x="543.63" xml:space="preserve" y="720.91" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065226756" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1648.52,299.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.52" xml:space="preserve" y="306.1" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064768004" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,543.633,661.643) scale(1,1) translate(0,2.90054e-13)" writing-mode="lr" x="543.63" xml:space="preserve" y="667.91" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065292292" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1648.52,263.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.52" xml:space="preserve" y="270.1" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064833540" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,543.633,625.643) scale(1,1) translate(0,0)" writing-mode="lr" x="543.63" xml:space="preserve" y="631.91" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065357829" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Ubc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1648.52,281.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.52" xml:space="preserve" y="288.1" zvalue="1">Ubc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064899076" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Ubc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,543.633,643.643) scale(1,1) translate(0,0)" writing-mode="lr" x="543.63" xml:space="preserve" y="649.91" zvalue="1">Ubc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065423364" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1648.61,367.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.61" xml:space="preserve" y="374.1" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065030148" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,543.633,730.643) scale(1,1) translate(0,0)" writing-mode="lr" x="543.63" xml:space="preserve" y="736.91" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065554436" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="21" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1152.94,491.15) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.94" xml:space="preserve" y="497.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125234999300" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="27" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1152.94,508.15) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.94" xml:space="preserve" y="514.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125235064836" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="28" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1152.94,663.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.94" xml:space="preserve" y="669.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125235130372" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="29" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1152.94,680.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.94" xml:space="preserve" y="686.3099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125235195908" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="30" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1152.94,525.15) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.94" xml:space="preserve" y="531.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125235261446" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="31" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,977.129,578.093) scale(1,1) translate(0,0)" writing-mode="lr" x="977.13" xml:space="preserve" y="582.76" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125235458052" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="32" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,977.129,609.88) scale(1,1) translate(0,0)" writing-mode="lr" x="977.13" xml:space="preserve" y="614.8" zvalue="1">档位:ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125235523588" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="33" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1152.94,697.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.94" xml:space="preserve" y="703.3099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125235589126" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="52" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1211.28,1073.01) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.28" xml:space="preserve" y="1079.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125241749510" ObjectName="P"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="54" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1370.28,1074.22) scale(1,1) translate(5.92316e-13,0)" writing-mode="lr" x="1370.28" xml:space="preserve" y="1080.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125243191302" ObjectName="P"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="55" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1055.59,1072.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.59" xml:space="preserve" y="1078.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125240832008" ObjectName="P"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="56" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1211.28,1089.39) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.28" xml:space="preserve" y="1095.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125241815046" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="57" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1370.28,1091.22) scale(1,1) translate(5.92316e-13,0)" writing-mode="lr" x="1370.28" xml:space="preserve" y="1097.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125243256838" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="60" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1055.59,1089.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.59" xml:space="preserve" y="1095.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125240897542" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="61" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1211.28,1106.39) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.28" xml:space="preserve" y="1112.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125241880582" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="62" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1370.28,1108.22) scale(1,1) translate(5.92316e-13,0)" writing-mode="lr" x="1370.28" xml:space="preserve" y="1114.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125243322374" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1055.59,1106.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.59" xml:space="preserve" y="1112.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125240963078" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211.28,1123.39) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.28" xml:space="preserve" y="1129.66" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125242273798" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1370.28,1123.39) scale(1,1) translate(0,0)" writing-mode="lr" x="1370.28" xml:space="preserve" y="1129.66" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125243715590" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.59,1123.39) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.59" xml:space="preserve" y="1129.66" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125241356292" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="1031">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="15" id="1031" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,153,155) scale(1,1) translate(0,0)" writing-mode="lr" x="152.56" xml:space="preserve" y="160.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125448450052" ObjectName=""/>
   </metadata>
  </g>
  <g id="1030">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="1030" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,339,155) scale(1,1) translate(0,0)" writing-mode="lr" x="338.56" xml:space="preserve" y="160.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125448515588" ObjectName=""/>
   </metadata>
  </g>
  <g id="1014">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="1014" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,153,179.5) scale(1,1) translate(0,0)" writing-mode="lr" x="152.64" xml:space="preserve" y="185.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125235458052" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="657">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="657" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,162.5,491) scale(1,1) translate(0,0)" writing-mode="lr" x="162.26" xml:space="preserve" y="495.72" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064833540" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="629">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="629" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,162.5,517.562) scale(1,1) translate(0,0)" writing-mode="lr" x="162.26" xml:space="preserve" y="522.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064571396" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="867">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="867" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,162.5,544.125) scale(1,1) translate(0,0)" writing-mode="lr" x="162.26" xml:space="preserve" y="548.85" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064636932" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="621">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="621" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,162.5,570.688) scale(1,1) translate(0,0)" writing-mode="lr" x="162.26" xml:space="preserve" y="575.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132064702468" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="321">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="321" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,162.5,597.25) scale(1,1) translate(0,0)" writing-mode="lr" x="162.26" xml:space="preserve" y="601.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065030148" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="980">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="980" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,240.5,491) scale(1,1) translate(0,0)" writing-mode="lr" x="240.26" xml:space="preserve" y="495.72" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065357829" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="978">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="978" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,240.5,517.562) scale(1,1) translate(0,0)" writing-mode="lr" x="240.26" xml:space="preserve" y="522.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065095684" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="977">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="977" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,240.5,544.125) scale(1,1) translate(0,0)" writing-mode="lr" x="240.26" xml:space="preserve" y="548.85" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065161220" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="976">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="976" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,240.5,570.688) scale(1,1) translate(0,0)" writing-mode="lr" x="240.26" xml:space="preserve" y="575.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065226756" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="975">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="975" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,240.5,597.25) scale(1,1) translate(0,0)" writing-mode="lr" x="240.26" xml:space="preserve" y="601.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132065554436" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="1166">
   <use height="30" stroke="rgb(255,255,255)" transform="rotate(0,40.8036,220.845) scale(0.958333,0.916667) translate(1.14907,18.8268)" width="30" x="26.43" xlink:href="#State:红绿圆_0" y="207.1" zvalue="1137"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584592898" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,40.8036,220.845) scale(0.958333,0.916667) translate(1.14907,18.8268)" width="30" x="26.43" y="207.1"/></g>
  <g id="1165">
   <use height="30" transform="rotate(0,102.232,220.845) scale(0.958333,0.916667) translate(3.81988,18.8268)" width="30" x="87.86" xlink:href="#State:红绿圆_0" y="207.1" zvalue="1138"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584592898" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,102.232,220.845) scale(0.958333,0.916667) translate(3.81988,18.8268)" width="30" x="87.86" y="207.1"/></g>
  <g id="1164">
   <use height="30" transform="rotate(0,166.232,220.845) scale(0.958333,0.916667) translate(6.60248,18.8268)" width="30" x="151.86" xlink:href="#State:红绿圆_0" y="207.1" zvalue="1139"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584592898" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,166.232,220.845) scale(0.958333,0.916667) translate(6.60248,18.8268)" width="30" x="151.86" y="207.1"/></g>
  <g id="1159">
   <use height="30" transform="rotate(0,55.7321,289.595) scale(0.958333,0.916667) translate(1.79814,25.0768)" width="30" x="41.36" xlink:href="#State:红绿圆_0" y="275.85" zvalue="1142"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584592898" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,55.7321,289.595) scale(0.958333,0.916667) translate(1.79814,25.0768)" width="30" x="41.36" y="275.85"/></g>
  <g id="1157">
   <use height="30" transform="rotate(0,143.232,289.595) scale(0.958333,0.916667) translate(5.60248,25.0768)" width="30" x="128.86" xlink:href="#State:红绿圆_0" y="275.85" zvalue="1144"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584592898" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,143.232,289.595) scale(0.958333,0.916667) translate(5.60248,25.0768)" width="30" x="128.86" y="275.85"/></g>
  <g id="993">
   <use height="30" transform="rotate(0,323.812,110.464) scale(1.27778,1.03333) translate(-57.894,-3.06335)" width="90" x="266.31" xlink:href="#State:全站检修_0" y="94.95999999999999" zvalue="1183"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584592898" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,323.812,110.464) scale(1.27778,1.03333) translate(-57.894,-3.06335)" width="90" x="266.31" y="94.95999999999999"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
</svg>