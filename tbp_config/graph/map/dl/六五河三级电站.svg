<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="0.333333333333333" y1="12.5859872611465" y2="5.083333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="8" y1="4" y2="4"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="1.666666666666666" y1="13.5" y2="2.749999999999997"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.666666666666666" x2="8" y1="14" y2="3.249999999999998"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013716"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261901" y2="42.91666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.5" y2="32.91666666666667"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
  </symbol>
  <symbol id="Ground:大地_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="13.08333333333334" y2="0.1666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.5833333333333313" x2="11.25" y1="12.99453511141348" y2="12.99453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333333" x2="8" y1="17.63116790988687" y2="17.63116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666667" x2="10.33333333333333" y1="15.40451817731685" y2="15.40451817731685"/>
  </symbol>
  <symbol id="Accessory:附属接地变_0" viewBox="0,0,15,15">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="1.5"/>
   <ellipse cx="7.57" cy="7.91" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.561718118722353" x2="7.561718118722353" y1="4.914465958746223" y2="7.513454058763156"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.1552656081938" x2="7.561718118722348" y1="10.11244215878007" y2="7.513454058763136"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.96817062925088" x2="7.561718118722333" y1="10.11244215878007" y2="7.513454058763136"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_0" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="19.75" y1="7" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="8.75" y1="2" y2="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="21.75" y1="2" y2="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.75" y1="4.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.583333333333336" x2="23.41666666666666" y1="3.583333333333333" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333332" x2="19.58333333333333" y1="6.833333333333333" y2="11.83333333333333"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_1" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="17" y1="8" y2="4"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17,10.5) scale(1,1) translate(0,0)" width="14" x="10" y="8"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.33333333333333" x2="6.000000000000004" y1="10.55" y2="10.55"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_2" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="21" y1="13" y2="7"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="21" y1="7" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="6" y2="3"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
  </symbol>
  <symbol id="EnergyConsumer:厂站用变_0" viewBox="0,0,15,25">
   <use terminal-index="0" type="0" x="5.916666666666666" xlink:href="#terminal" y="1.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.41666666666667" x2="5.916666666666666" y1="17.16666666666666" y2="20.33333333333333"/>
   <ellipse cx="5.8" cy="5.8" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.86" cy="11.79" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.926184092940137" x2="5.926184092940137" y1="10.47013412501683" y2="12.14189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.606255585344062" x2="5.926184092940133" y1="13.81365173613114" y2="12.14189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.246112600536196" x2="5.926184092940125" y1="13.81365173613114" y2="12.14189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.926184092940137" x2="13.41666666666667" y1="12.14189293057399" y2="12.14189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.41657730116176" x2="13.41657730116176" y1="12.16666666666666" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.009517426273471" x2="6.009517426273471" y1="3.303467458350164" y2="4.975226263907325"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.689588918677395" x2="6.009517426273467" y1="6.64698506946448" y2="4.975226263907319"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.246112600536196" x2="5.926184092940125" y1="6.563651736131146" y2="4.891892930573985"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.914160137763698" x2="5.914160137763698" y1="22.49434593889296" y2="15.91678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.906472232006561" x2="4.753286368439581" y1="24.43333333333333" y2="22.50505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.906472232006569" x2="7.05965809557355" y1="24.43333333333333" y2="22.50505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.753286368439605" x2="7.059658095573566" y1="22.50505857643125" y2="22.50505857643125"/>
  </symbol>
  <symbol id="Accessory:带熔断器的2卷变PT_0" viewBox="0,0,15,35">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="2.5"/>
   <path d="M 4.5 17.25 L 7.5 22.25 L 10.5 17.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333333" x2="7.583333333333333" y1="15.08333333333333" y2="2.416666666666666"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.42,9.58) scale(1,1) translate(0,0)" width="4" x="5.42" y="6.58"/>
   <path d="M 4.5 26 L 7.5 31 L 10.5 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="7.32" cy="20.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.32" cy="27.87" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器－阀式_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.083333333333333" x2="11.08333333333333" y1="18.83333333333334" y2="18.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="8.333333333333332" y1="8.333333333333336" y2="8.333333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.083333333333333" x2="11.08333333333333" y1="11.83333333333334" y2="11.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.13333333333333" x2="10.13333333333333" y1="2.166666666666666" y2="5.683333333333334"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,10.11,13.68) scale(1,1) translate(0,0)" width="10.05" x="5.08" y="5.68"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.702777777777772" x2="12.21666666666667" y1="28.1" y2="28.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.13333333333333" x2="10.13333333333333" y1="21.68333333333334" y2="25.28333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="8.333333333333332" y1="15.33333333333334" y2="15.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.744444444444442" x2="12.85555555555556" y1="26.71842105263158" y2="26.71842105263158"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.466666666666666" x2="14.13333333333333" y1="25.33684210526318" y2="25.33684210526318"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="Compensator:间隙击穿20210927_0" viewBox="0,0,11,45">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="1.25"/>
   <use terminal-index="1" type="0" x="5.5" xlink:href="#terminal" y="43.75"/>
   <path d="M 0.5 6 L 10.5 6 L 5.5 16 z" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="38.75" y2="43.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="6" y2="1.166666666666668"/>
   <path d="M 10.5 38.8333 L 0.5 38.8333 L 5.5 28.8333 z" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="六五河三级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="87" id="156" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,171.875,52.5) scale(1,1) translate(0,0)" width="333.75" x="5" y="9" zvalue="1886"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="48" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,171.875,52.5) scale(1,1) translate(0,0)" writing-mode="lr" x="171.88" xml:space="preserve" y="69.5" zvalue="1886">六五河三级电站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.882,71.2619) scale(1,1) translate(0,0)" writing-mode="lr" x="794.88" xml:space="preserve" y="75.76000000000001" zvalue="1888">35kV漾太三线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.876,337.648) scale(1,1) translate(0,0)" writing-mode="lr" x="880.88" xml:space="preserve" y="342.15" zvalue="1890">3636</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,803.926,339.007) scale(1,1) translate(0,0)" writing-mode="lr" x="803.9299999999999" xml:space="preserve" y="343.51" zvalue="1892">36367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.536,244) scale(1,1) translate(0,0)" writing-mode="lr" x="687.54" xml:space="preserve" y="248.5" zvalue="1894">3639</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.254,239.089) scale(1,1) translate(0,0)" writing-mode="lr" x="573.25" xml:space="preserve" y="243.59" zvalue="1896">36397</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,878.75,387) scale(1,1) translate(0,0)" writing-mode="lr" x="878.75" xml:space="preserve" y="391.5" zvalue="1904">363</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,902.927,604.25) scale(1,1) translate(0,0)" writing-mode="lr" x="902.9299999999999" xml:space="preserve" y="608.75" zvalue="1907">1B</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.5,585) scale(1,1) translate(0,0)" writing-mode="lr" x="900.5" xml:space="preserve" y="589.5" zvalue="1909">(50kVA)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557,684.004) scale(1,1) translate(0,0)" writing-mode="lr" x="557" xml:space="preserve" y="688.5" zvalue="1911">0.4kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,527,820) scale(1,1) translate(0,0)" writing-mode="lr" x="527" xml:space="preserve" y="824.5" zvalue="1919">413</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,545.75,1084.5) scale(1,1) translate(0,0)" writing-mode="lr" x="545.75" xml:space="preserve" y="1089" zvalue="1921">3F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,545,1105) scale(1,1) translate(0,0)" writing-mode="lr" x="545" xml:space="preserve" y="1109.5" zvalue="1922">(1800kW)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,459.857,1037.38) scale(1,1) translate(0,0)" writing-mode="lr" x="459.86" xml:space="preserve" y="1041.88" zvalue="1926">1YH</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,878.876,127.648) scale(1,1) translate(0,0)" writing-mode="lr" x="878.88" xml:space="preserve" y="131.15" zvalue="1948">3638</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,803.926,131.007) scale(1,1) translate(0,0)" writing-mode="lr" x="803.9299999999999" xml:space="preserve" y="134.51" zvalue="1950">36387</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.17,72.1249) scale(1,1) translate(0,0)" writing-mode="lr" x="937.17" xml:space="preserve" y="76.62" zvalue="1952">35kV漾太三线#50杆</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,946.5,215.5) scale(1,1) translate(0,0)" writing-mode="lr" x="946.5" xml:space="preserve" y="220" zvalue="1958">35kV漾太三线(长6.88KM)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1004.5,543.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.5" xml:space="preserve" y="548.25" zvalue="1962">厂变2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,881.709,484.556) scale(1,1) translate(0,0)" writing-mode="lr" x="881.71" xml:space="preserve" y="489.06" zvalue="1965">3631</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.172,481.089) scale(1,1) translate(0,0)" writing-mode="lr" x="800.17" xml:space="preserve" y="485.59" zvalue="1968">36317</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.29,1029.8) scale(1,1) translate(0,0)" writing-mode="lr" x="613.29" xml:space="preserve" y="1034.3" zvalue="1974">2YH</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,514.771,767.556) scale(1,1) translate(0,0)" writing-mode="lr" x="514.77" xml:space="preserve" y="772.0599999999999" zvalue="1976">4131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773,820) scale(1,1) translate(0,0)" writing-mode="lr" x="773" xml:space="preserve" y="824.5" zvalue="1990">412</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.75,1084.5) scale(1,1) translate(0,0)" writing-mode="lr" x="791.75" xml:space="preserve" y="1089" zvalue="1992">2F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791,1105) scale(1,1) translate(0,0)" writing-mode="lr" x="791" xml:space="preserve" y="1109.5" zvalue="1993">(1800kW)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,707.107,1038.62) scale(1,1) translate(0,0)" writing-mode="lr" x="707.11" xml:space="preserve" y="1043.13" zvalue="1995">1YH</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,859.29,1029.8) scale(1,1) translate(0,0)" writing-mode="lr" x="859.29" xml:space="preserve" y="1034.3" zvalue="1996">2YH</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,760.771,767.556) scale(1,1) translate(0,0)" writing-mode="lr" x="760.77" xml:space="preserve" y="772.0599999999999" zvalue="1997">4121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1019,820) scale(1,1) translate(0,0)" writing-mode="lr" x="1019" xml:space="preserve" y="824.5" zvalue="2007">411</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037.75,1084.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.75" xml:space="preserve" y="1089" zvalue="2009">1F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037,1105) scale(1,1) translate(0,0)" writing-mode="lr" x="1037" xml:space="preserve" y="1109.5" zvalue="2010">(1800kW)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.107,1038.62) scale(1,1) translate(0,0)" writing-mode="lr" x="953.11" xml:space="preserve" y="1043.13" zvalue="2012">1YH</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1105.29,1029.8) scale(1,1) translate(0,0)" writing-mode="lr" x="1105.29" xml:space="preserve" y="1034.3" zvalue="2013">2YH</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006.77,767.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1006.77" xml:space="preserve" y="772.0599999999999" zvalue="2014">4111</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1385,877.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1385" xml:space="preserve" y="882" zvalue="2029">厂变1</text>
 </g>
 <g id="ACLineSegmentClass">
  <g id="155">
   <use class="kv35" height="30" transform="rotate(270,797.835,88.1249) scale(5.04226,3.25568) translate(-625.458,-27.2217)" width="7" x="780.1872486884296" xlink:href="#ACLineSegment:线路_0" y="39.28971970733016" zvalue="1887"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,797.835,88.1249) scale(5.04226,3.25568) translate(-625.458,-27.2217)" width="7" x="780.1872486884296" y="39.28971970733016"/></g>
  <g id="69">
   <use class="kv35" height="30" transform="rotate(90,927.835,87.1249) scale(-5.04226,3.25568) translate(-1097.7,-26.5288)" width="7" x="910.1872486884296" xlink:href="#ACLineSegment:线路_0" y="38.28971970733028" zvalue="1951"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线#50杆"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,927.835,87.1249) scale(-5.04226,3.25568) translate(-1097.7,-26.5288)" width="7" x="910.1872486884296" y="38.28971970733028"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="153">
   <use class="kv35" height="30" transform="rotate(0,855.21,335.556) scale(-1.11111,0.814815) translate(-1624.07,73.4848)" width="15" x="846.8764839732156" xlink:href="#Disconnector:刀闸_0" y="323.3333333333331" zvalue="1889"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线3636"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,855.21,335.556) scale(-1.11111,0.814815) translate(-1624.07,73.4848)" width="15" x="846.8764839732156" y="323.3333333333331"/></g>
  <g id="151">
   <use class="kv35" height="30" transform="rotate(180,652.857,241) scale(1.42857,-1.04762) translate(-192.643,-470.331)" width="15" x="642.1428571428571" xlink:href="#Disconnector:刀闸_0" y="225.2857142857144" zvalue="1893"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线3639"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,652.857,241) scale(1.42857,-1.04762) translate(-192.643,-470.331)" width="15" x="642.1428571428571" y="225.2857142857144"/></g>
  <g id="71">
   <use class="kv35" height="30" transform="rotate(0,855.21,127.556) scale(-1.11111,0.814815) translate(-1624.07,26.2121)" width="15" x="846.8764839732156" xlink:href="#Disconnector:刀闸_0" y="115.3333333333331" zvalue="1947"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线3638"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,855.21,127.556) scale(-1.11111,0.814815) translate(-1624.07,26.2121)" width="15" x="846.8764839732156" y="115.3333333333331"/></g>
  <g id="62">
   <use class="kv35" height="14" transform="rotate(90,1049,463) scale(1,1) translate(0,0)" width="30" x="1034" xlink:href="#Disconnector:站外刀闸_0" y="456" zvalue="1959"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="厂变2刀闸"/>
   </metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(90,1049,463) scale(1,1) translate(0,0)" width="30" x="1034" y="456"/></g>
  <g id="58">
   <use class="kv35" height="30" transform="rotate(0,855.751,483.556) scale(-1.11111,0.814815) translate(-1625.09,107.121)" width="15" x="847.417870558304" xlink:href="#Disconnector:刀闸_0" y="471.3333333333331" zvalue="1964"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线3631"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,855.751,483.556) scale(-1.11111,0.814815) translate(-1625.09,107.121)" width="15" x="847.417870558304" y="471.3333333333331"/></g>
  <g id="1">
   <use class="v400" height="30" transform="rotate(0,545.751,767.306) scale(-1.11111,0.814815) translate(-1036.09,171.61)" width="15" x="537.417870558304" xlink:href="#Disconnector:刀闸_0" y="755.0833333333331" zvalue="1975"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F4131"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,545.751,767.306) scale(-1.11111,0.814815) translate(-1036.09,171.61)" width="15" x="537.417870558304" y="755.0833333333331"/></g>
  <g id="27">
   <use class="v400" height="30" transform="rotate(0,791.751,767.306) scale(-1.11111,0.814815) translate(-1503.49,171.61)" width="15" x="783.417870558304" xlink:href="#Disconnector:刀闸_0" y="755.0833333333331" zvalue="1996"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F4121"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,791.751,767.306) scale(-1.11111,0.814815) translate(-1503.49,171.61)" width="15" x="783.417870558304" y="755.0833333333331"/></g>
  <g id="83">
   <use class="v400" height="30" transform="rotate(0,1037.75,767.306) scale(-1.11111,0.814815) translate(-1970.89,171.61)" width="15" x="1029.417870558304" xlink:href="#Disconnector:刀闸_0" y="755.0833333333331" zvalue="2013"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F4111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1037.75,767.306) scale(-1.11111,0.814815) translate(-1970.89,171.61)" width="15" x="1029.417870558304" y="755.0833333333331"/></g>
  <g id="94">
   <use class="v400" height="30" transform="rotate(0,1329,766.056) scale(1.11111,0.814815) translate(-132.067,171.326)" width="15" x="1320.667870558304" xlink:href="#Disconnector:刀闸_0" y="753.8333333333331" zvalue="2030"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="厂变1刀闸"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1329,766.056) scale(1.11111,0.814815) translate(-132.067,171.326)" width="15" x="1320.667870558304" y="753.8333333333331"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="152">
   <use class="kv35" height="30" transform="rotate(180,827.926,334.089) scale(-0.763595,0.763595) translate(-1913.59,99.8862)" width="12" x="823.3447569719574" xlink:href="#GroundDisconnector:地刀12_0" y="322.6348948311083" zvalue="1891"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线36367"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,827.926,334.089) scale(-0.763595,0.763595) translate(-1913.59,99.8862)" width="12" x="823.3447569719574" y="322.6348948311083"/></g>
  <g id="150">
   <use class="kv35" height="30" transform="rotate(0,604.926,243.089) scale(0.763595,0.763595) translate(185.864,71.7131)" width="12" x="600.3447569274326" xlink:href="#GroundDisconnector:地刀12_0" y="231.6348948311083" zvalue="1895"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线36397"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,604.926,243.089) scale(0.763595,0.763595) translate(185.864,71.7131)" width="12" x="600.3447569274326" y="231.6348948311083"/></g>
  <g id="70">
   <use class="kv35" height="30" transform="rotate(180,827.926,126.089) scale(-0.763595,0.763595) translate(-1913.59,35.4904)" width="12" x="823.3447569719574" xlink:href="#GroundDisconnector:地刀12_0" y="114.6348948311083" zvalue="1949"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线36387"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,827.926,126.089) scale(-0.763595,0.763595) translate(-1913.59,35.4904)" width="12" x="823.3447569719574" y="114.6348948311083"/></g>
  <g id="51">
   <use class="kv35" height="30" transform="rotate(180,829.926,482.089) scale(-0.763595,0.763595) translate(-1918.21,145.706)" width="12" x="825.3447569719574" xlink:href="#GroundDisconnector:地刀12_0" y="470.6348948311083" zvalue="1966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线36317"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,829.926,482.089) scale(-0.763595,0.763595) translate(-1918.21,145.706)" width="12" x="825.3447569719574" y="470.6348948311083"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="149">
   <use class="kv35" height="30" transform="rotate(0,710.54,155.323) scale(-2.85714,-2.85714) translate(-931.372,-181.829)" width="30" x="667.6832519332521" xlink:href="#Accessory:三卷PT带容断器_0" y="112.4657065516137" zvalue="1897"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,710.54,155.323) scale(-2.85714,-2.85714) translate(-931.372,-181.829)" width="30" x="667.6832519332521" y="112.4657065516137"/></g>
  <g id="146">
   <use class="kv35" height="26" transform="rotate(0,599.745,166.478) scale(1.25,-1.25) translate(-118.449,-296.41)" width="12" x="592.244838494839" xlink:href="#Accessory:避雷器1_0" y="150.2275560493142" zvalue="1898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,599.745,166.478) scale(1.25,-1.25) translate(-118.449,-296.41)" width="12" x="592.244838494839" y="150.2275560493142"/></g>
  <g id="121">
   <use class="v400" height="15" transform="rotate(0,545.75,1039.75) scale(3.5,3.5) translate(-371.071,-723.929)" width="15" x="519.5" xlink:href="#Accessory:附属接地变_0" y="1013.5" zvalue="1920"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,545.75,1039.75) scale(3.5,3.5) translate(-371.071,-723.929)" width="15" x="519.5" y="1013.5"/></g>
  <g id="111">
   <use class="v400" height="35" transform="rotate(0,459.857,972) scale(3.01429,3.01429) translate(-292.191,-614.286)" width="15" x="437.25" xlink:href="#Accessory:带熔断器的2卷变PT_0" y="919.25" zvalue="1925"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,459.857,972) scale(3.01429,3.01429) translate(-292.191,-614.286)" width="15" x="437.25" y="919.25"/></g>
  <g id="74">
   <use class="v400" height="30" transform="rotate(0,613.29,974.323) scale(2.85714,2.85714) translate(-370.782,-605.453)" width="30" x="570.4332519332521" xlink:href="#Accessory:三卷PT带容断器_0" y="931.4657065516137" zvalue="1973"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F电压互感器1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,613.29,974.323) scale(2.85714,2.85714) translate(-370.782,-605.453)" width="30" x="570.4332519332521" y="931.4657065516137"/></g>
  <g id="5">
   <use class="v400" height="30" transform="rotate(0,481.705,872.841) scale(-0.75,1.08333) translate(-1126.48,-65.8916)" width="20" x="474.2045454545454" xlink:href="#Accessory:避雷器－阀式_0" y="856.5909118652344" zvalue="1983"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,481.705,872.841) scale(-0.75,1.08333) translate(-1126.48,-65.8916)" width="20" x="474.2045454545454" y="856.5909118652344"/></g>
  <g id="52">
   <use class="v400" height="15" transform="rotate(0,791.75,1039.75) scale(3.5,3.5) translate(-546.786,-723.929)" width="15" x="765.5" xlink:href="#Accessory:附属接地变_0" y="1013.5" zvalue="1991"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,791.75,1039.75) scale(3.5,3.5) translate(-546.786,-723.929)" width="15" x="765.5" y="1013.5"/></g>
  <g id="30">
   <use class="v400" height="35" transform="rotate(0,707.107,973.25) scale(3.01429,3.01429) translate(-457.415,-615.121)" width="15" x="684.5" xlink:href="#Accessory:带熔断器的2卷变PT_0" y="920.5" zvalue="1994"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,707.107,973.25) scale(3.01429,3.01429) translate(-457.415,-615.121)" width="15" x="684.5" y="920.5"/></g>
  <g id="28">
   <use class="v400" height="30" transform="rotate(0,859.29,974.323) scale(2.85714,2.85714) translate(-530.682,-605.453)" width="30" x="816.4332519332521" xlink:href="#Accessory:三卷PT带容断器_0" y="931.4657065516137" zvalue="1995"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F电压互感器1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,859.29,974.323) scale(2.85714,2.85714) translate(-530.682,-605.453)" width="30" x="816.4332519332521" y="931.4657065516137"/></g>
  <g id="18">
   <use class="v400" height="30" transform="rotate(0,737.705,872.841) scale(-0.75,1.08333) translate(-1723.81,-65.8916)" width="20" x="730.2045454545454" xlink:href="#Accessory:避雷器－阀式_0" y="856.5909129056063" zvalue="2002"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,737.705,872.841) scale(-0.75,1.08333) translate(-1723.81,-65.8916)" width="20" x="730.2045454545454" y="856.5909129056063"/></g>
  <g id="87">
   <use class="v400" height="15" transform="rotate(0,1037.75,1039.75) scale(3.5,3.5) translate(-722.5,-723.929)" width="15" x="1011.5" xlink:href="#Accessory:附属接地变_0" y="1013.5" zvalue="2008"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1037.75,1039.75) scale(3.5,3.5) translate(-722.5,-723.929)" width="15" x="1011.5" y="1013.5"/></g>
  <g id="85">
   <use class="v400" height="35" transform="rotate(0,953.107,973.25) scale(3.01429,3.01429) translate(-621.803,-615.121)" width="15" x="930.5" xlink:href="#Accessory:带熔断器的2卷变PT_0" y="920.5" zvalue="2011"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,953.107,973.25) scale(3.01429,3.01429) translate(-621.803,-615.121)" width="15" x="930.5" y="920.5"/></g>
  <g id="84">
   <use class="v400" height="30" transform="rotate(0,1105.29,974.323) scale(2.85714,2.85714) translate(-690.582,-605.453)" width="30" x="1062.433251933252" xlink:href="#Accessory:三卷PT带容断器_0" y="931.4657065516137" zvalue="2012"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F电压互感器1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1105.29,974.323) scale(2.85714,2.85714) translate(-690.582,-605.453)" width="30" x="1062.433251933252" y="931.4657065516137"/></g>
  <g id="76">
   <use class="v400" height="30" transform="rotate(0,983.705,872.841) scale(-0.75,1.08333) translate(-2297.81,-65.8916)" width="20" x="976.2045454545454" xlink:href="#Accessory:避雷器－阀式_0" y="856.5909129056063" zvalue="2019"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,983.705,872.841) scale(-0.75,1.08333) translate(-2297.81,-65.8916)" width="20" x="976.2045454545454" y="856.5909129056063"/></g>
  <g id="91">
   <use class="v400" height="30" transform="rotate(0,1193.29,814.323) scale(2.85714,2.85714) translate(-747.782,-501.453)" width="30" x="1150.433251933252" xlink:href="#Accessory:三卷PT带容断器_0" y="771.4657065516137" zvalue="2025"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0.66kV电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1193.29,814.323) scale(2.85714,2.85714) translate(-747.782,-501.453)" width="30" x="1150.433251933252" y="771.4657065516137"/></g>
  <g id="34">
   <use class="v400" height="18" transform="rotate(0,1329.25,813.75) scale(1.5,1.80556) translate(-440.583,-355.808)" width="10" x="1321.75" xlink:href="#Accessory:熔断器_0" y="797.5" zvalue="2032"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="厂变1熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1329.25,813.75) scale(1.5,1.80556) translate(-440.583,-355.808)" width="10" x="1321.75" y="797.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="145">
   <path class="kv35" d="M 599.79 181.94 L 599.79 197.18 L 710.68 197.18" stroke-width="1" zvalue="1899"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="149@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 599.79 181.94 L 599.79 197.18 L 710.68 197.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv35" d="M 652.73 225.81 L 652.73 197.18" stroke-width="1" zvalue="1900"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="145" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.73 225.81 L 652.73 197.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv35" d="M 604.92 232.15 L 604.92 219 L 652.73 219" stroke-width="1" zvalue="1901"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="142" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.92 232.15 L 604.92 219 L 652.73 219" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv35" d="M 855.65 375.38 L 855.65 347.57" stroke-width="1" zvalue="1903"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="153@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 855.65 375.38 L 855.65 347.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv35" d="M 827.92 345.03 L 827.92 360 L 855.65 360" stroke-width="1" zvalue="1905"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="139" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.92 345.03 L 827.92 360 L 855.65 360" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv35" d="M 855.65 471.74 L 855.65 398.62" stroke-width="1" zvalue="1908"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="140@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 855.65 471.74 L 855.65 398.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="v400" d="M 856.65 643.8 L 856.65 700" stroke-width="1" zvalue="1912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@1" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 856.65 643.8 L 856.65 700" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="v400" d="M 856.65 625.99 L 913 625.99" stroke-width="1" zvalue="1924"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@2" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 856.65 625.99 L 913 625.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv35" d="M 846.18 88.12 L 879.49 88.12" stroke-width="1" zvalue="1953"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="69@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 846.18 88.12 L 879.49 88.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv35" d="M 855.11 115.74 L 855.11 88.12" stroke-width="1" zvalue="1954"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 855.11 115.74 L 855.11 88.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 855.14 139.57 L 855.11 323.74" stroke-width="1" zvalue="1955"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@1" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 855.14 139.57 L 855.11 323.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv35" d="M 827.92 137.03 L 827.92 157 L 855.14 157" stroke-width="1" zvalue="1956"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.92 137.03 L 827.92 157 L 855.14 157" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv35" d="M 652.77 256.45 L 652.77 279 L 855.12 279" stroke-width="1" zvalue="1957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@1" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.77 256.45 L 652.77 279 L 855.12 279" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv35" d="M 1046.6 494.75 L 1046.6 477.92" stroke-width="1" zvalue="1961"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="62@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1046.6 494.75 L 1046.6 477.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 1045.33 448.42 L 1045.33 431 L 855.65 431" stroke-width="1" zvalue="1963"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 1045.33 448.42 L 1045.33 431 L 855.65 431" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv35" d="M 856.66 571.07 L 856.66 495.57" stroke-width="1" zvalue="1967"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="58@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 856.66 571.07 L 856.66 495.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv35" d="M 829.92 493.03 L 829.92 515 L 856.66 515" stroke-width="1" zvalue="1969"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.92 493.03 L 829.92 515 L 856.66 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v400" d="M 783 593.99 L 834 593.99" stroke-width="1" zvalue="1971"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 783 593.99 L 834 593.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="v400" d="M 545.65 755.49 L 545.65 700" stroke-width="1" zvalue="1976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 545.65 755.49 L 545.65 700" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="v400" d="M 546.65 808.38 L 546.65 779.32" stroke-width="1" zvalue="1977"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 546.65 808.38 L 546.65 779.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="v400" d="M 459.86 926.79 L 459.86 928.04" stroke-width="1" zvalue="1987"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 459.86 926.79 L 459.86 928.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="v400" d="M 792.65 808.38 L 792.65 779.32" stroke-width="1" zvalue="1998"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="27@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.65 808.38 L 792.65 779.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="v400" d="M 707.11 928.04 L 707.11 928.04" stroke-width="1" zvalue="2004"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.11 928.04 L 707.11 928.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="v400" d="M 1038.65 808.38 L 1038.65 779.32" stroke-width="1" zvalue="2015"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="83@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.65 808.38 L 1038.65 779.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v400" d="M 953.11 928.04 L 953.11 928.04" stroke-width="1" zvalue="2021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 953.11 928.04 L 953.11 928.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="v400" d="M 1037.65 755.49 L 1037.65 700" stroke-width="1" zvalue="2022"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="130@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.65 755.49 L 1037.65 700" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="v400" d="M 791.65 755.49 L 791.65 700" stroke-width="1" zvalue="2023"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="130@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 791.65 755.49 L 791.65 700" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v400" d="M 1193.15 772.47 L 1193.15 700" stroke-width="1" zvalue="2026"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="130@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1193.15 772.47 L 1193.15 700" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="v400" d="M 1329.1 754.24 L 1329.1 700" stroke-width="1" zvalue="2033"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="130@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.1 754.24 L 1329.1 700" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="v400" d="M 1329.28 799.46 L 1329.28 778.07" stroke-width="1" zvalue="2034"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="94@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.28 799.46 L 1329.28 778.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="v400" d="M 1329.28 799.46 L 1329.28 854.75" stroke-width="1" zvalue="2035"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.28 799.46 L 1329.28 854.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="v400" d="M 481.7 859.03 L 481.7 846.92" stroke-width="1" zvalue="2041"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="96@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 481.7 859.03 L 481.7 846.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="v400" d="M 481.19 805.87 L 481.19 796.25 L 546.65 796.25" stroke-width="1" zvalue="2042"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="3" MaxPinNum="2"/>
   </metadata>
  <path d="M 481.19 805.87 L 481.19 796.25 L 546.65 796.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="v400" d="M 737.7 859.03 L 737.7 849.42" stroke-width="1" zvalue="2043"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 737.7 859.03 L 737.7 849.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="v400" d="M 736.69 808.37 L 736.69 791.25 L 792.65 791.25" stroke-width="1" zvalue="2044"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 736.69 808.37 L 736.69 791.25 L 792.65 791.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="v400" d="M 983.7 859.03 L 983.7 849.17" stroke-width="1" zvalue="2045"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="98@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.7 859.03 L 983.7 849.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="v400" d="M 982.69 808.12 L 982.69 795 L 1038.65 795" stroke-width="1" zvalue="2046"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 982.69 808.12 L 982.69 795 L 1038.65 795" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="v400" d="M 545.75 1018.75 L 545.75 831.62" stroke-width="1" zvalue="2053"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="122@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 545.75 1018.75 L 545.75 831.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="v400" d="M 791.75 1018.75 L 791.75 925.19 L 792.65 925.19 L 792.65 831.62" stroke-width="1" zvalue="2054"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="53@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 791.75 1018.75 L 791.75 925.19 L 792.65 925.19 L 792.65 831.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="v400" d="M 1037.75 1018.75 L 1037.75 925.19 L 1038.65 925.19 L 1038.65 831.62" stroke-width="1" zvalue="2055"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.75 1018.75 L 1037.75 925.19 L 1038.65 925.19 L 1038.65 831.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="v400" d="M 459.86 926.79 L 459.86 907 L 545.75 907" stroke-width="1" zvalue="2056"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 459.86 926.79 L 459.86 907 L 545.75 907" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="v400" d="M 613.15 932.47 L 613.15 910 L 545.75 910" stroke-width="1" zvalue="2057"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 613.15 932.47 L 613.15 910 L 545.75 910" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="v400" d="M 707.11 928.04 L 707.11 912 L 792.65 912" stroke-width="1" zvalue="2058"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.11 928.04 L 707.11 912 L 792.65 912" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="v400" d="M 859.15 932.47 L 859.15 913 L 792.65 913" stroke-width="1" zvalue="2059"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 859.15 932.47 L 859.15 913 L 792.65 913" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="v400" d="M 953.11 928.04 L 953.11 908 L 1038.65 908" stroke-width="1" zvalue="2060"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 953.11 928.04 L 953.11 908 L 1038.65 908" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="v400" d="M 1105.15 932.47 L 1105.15 910 L 1038.65 910" stroke-width="1" zvalue="2061"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1105.15 932.47 L 1105.15 910 L 1038.65 910" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="140">
   <use class="kv35" height="20" transform="rotate(0,855.75,387) scale(-1.25,1.25) translate(-1539.1,-74.9)" width="10" x="849.5" xlink:href="#Breaker:刀闸绘制规范_0" y="374.5" zvalue="1902"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV漾太三线363"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,855.75,387) scale(-1.25,1.25) translate(-1539.1,-74.9)" width="10" x="849.5" y="374.5"/></g>
  <g id="122">
   <use class="v400" height="20" transform="rotate(0,546.75,820) scale(-1.25,1.25) translate(-982.9,-161.5)" width="10" x="540.5" xlink:href="#Breaker:刀闸绘制规范_0" y="807.5" zvalue="1918"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F413"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,546.75,820) scale(-1.25,1.25) translate(-982.9,-161.5)" width="10" x="540.5" y="807.5"/></g>
  <g id="53">
   <use class="v400" height="20" transform="rotate(0,792.75,820) scale(-1.25,1.25) translate(-1425.7,-161.5)" width="10" x="786.5" xlink:href="#Breaker:刀闸绘制规范_0" y="807.5" zvalue="1989"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F412"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,792.75,820) scale(-1.25,1.25) translate(-1425.7,-161.5)" width="10" x="786.5" y="807.5"/></g>
  <g id="88">
   <use class="v400" height="20" transform="rotate(0,1038.75,820) scale(-1.25,1.25) translate(-1868.5,-161.5)" width="10" x="1032.5" xlink:href="#Breaker:刀闸绘制规范_0" y="807.5" zvalue="2006"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F411"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1038.75,820) scale(-1.25,1.25) translate(-1868.5,-161.5)" width="10" x="1032.5" y="807.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="137">
   <g id="1370">
    <use class="kv35" height="50" transform="rotate(0,856.649,607.25) scale(1.47,1.47) translate(-266.845,-182.405)" width="30" x="834.6" xlink:href="#PowerTransformer2:D-Y_0" y="570.5" zvalue="1906"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1371">
    <use class="v400" height="50" transform="rotate(0,856.649,607.25) scale(1.47,1.47) translate(-266.845,-182.405)" width="30" x="834.6" xlink:href="#PowerTransformer2:D-Y_1" y="570.5" zvalue="1906"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,856.649,607.25) scale(1.47,1.47) translate(-266.845,-182.405)" width="30" x="834.6" y="570.5"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="130">
   <path class="v400" d="M 403.22 700 L 1395 700" stroke-width="6" zvalue="1910"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0.4kV母线"/>
   </metadata>
  <path d="M 403.22 700 L 1395 700" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundClass">
  <g id="119">
   <use class="v400" height="18" transform="rotate(0,913,639.119) scale(1.5,1.5) translate(-301.333,-208.54)" width="12" x="904" xlink:href="#Ground:大地_0" y="625.6187621551593" zvalue="1923"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变接地1"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,913,639.119) scale(1.5,1.5) translate(-301.333,-208.54)" width="12" x="904" y="625.6187621551593"/></g>
  <g id="48">
   <use class="v400" height="18" transform="rotate(0,783,607.119) scale(1.5,1.5) translate(-258,-197.873)" width="12" x="774" xlink:href="#Ground:大地_0" y="593.6187621551593" zvalue="1970"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变接地"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,783,607.119) scale(1.5,1.5) translate(-258,-197.873)" width="12" x="774" y="593.6187621551593"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="61">
   <use class="kv35" height="25" transform="rotate(0,1053.85,544.75) scale(4.58,4.58) translate(-796.902,-381.059)" width="15" x="1019.5" xlink:href="#EnergyConsumer:厂站用变_0" y="487.5" zvalue="1960"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="厂变2"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1053.85,544.75) scale(4.58,4.58) translate(-796.902,-381.059)" width="15" x="1019.5" y="487.5"/></g>
  <g id="93">
   <use class="v400" height="25" transform="rotate(0,1337.6,904.75) scale(4.58,4.58) translate(-1018.7,-662.456)" width="15" x="1303.25" xlink:href="#EnergyConsumer:厂站用变_0" y="847.5" zvalue="2028"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="厂变1"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1337.6,904.75) scale(4.58,4.58) translate(-1018.7,-662.456)" width="15" x="1303.25" y="847.5"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="96">
   <use class="v400" height="45" transform="rotate(0,481.188,826.392) scale(0.965909,0.965909) translate(16.7956,28.3997)" width="11" x="475.875" xlink:href="#Compensator:间隙击穿20210927_0" y="804.6590909090909" zvalue="2036"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F避雷器间隙击穿"/>
   </metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,481.188,826.392) scale(0.965909,0.965909) translate(16.7956,28.3997)" width="11" x="475.875" y="804.6590909090909"/></g>
  <g id="97">
   <use class="v400" height="45" transform="rotate(0,736.688,828.892) scale(0.965909,0.965909) translate(25.8132,28.488)" width="11" x="731.375" xlink:href="#Compensator:间隙击穿20210927_0" y="807.1590909090909" zvalue="2038"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F避雷器间隙击穿"/>
   </metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,736.688,828.892) scale(0.965909,0.965909) translate(25.8132,28.488)" width="11" x="731.375" y="807.1590909090909"/></g>
  <g id="98">
   <use class="v400" height="45" transform="rotate(0,982.688,828.642) scale(0.965909,0.965909) translate(34.4956,28.4791)" width="11" x="977.375" xlink:href="#Compensator:间隙击穿20210927_0" y="806.9090909090909" zvalue="2040"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F避雷器间隙击穿"/>
   </metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,982.688,828.642) scale(0.965909,0.965909) translate(34.4956,28.4791)" width="11" x="977.375" y="806.9090909090909"/></g>
 </g>
</svg>