<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549588131842" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带负荷1_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="9.062458524793254" xlink:href="#terminal" y="41.29040359122629"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,25.61,15.59) scale(1,1) translate(0,0)" width="11" x="20.11" y="13.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.61245852479332" x2="29.61245852479332" y1="19.34040359122622" y2="12.34040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666667" x2="25.8624585247933" y1="23.94225544307809" y2="23.94225544307809"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.86245852479331" x2="25.86245852479331" y1="23.93299618381883" y2="21.09040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.6124585247933" x2="21.6124585247933" y1="21.34040359122623" y2="19.34040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.61245852479333" x2="29.61245852479333" y1="12.34040359122622" y2="10.34040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.062458524793264" x2="9.062458524793264" y1="31.39040359122627" y2="25.89040359122627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.64772110866599" x2="25.64772110866599" y1="10.08674821859629" y2="3.405450010721147"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.71438777533266" x2="23.71438777533266" y1="3.016127685651949" y2="3.016127685651949"/>
   <ellipse cx="5.54" cy="23.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.55048569403981" x2="13.55048569403981" y1="35.78084700683308" y2="35.78084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.7608382776216" x2="23.48402243293775" y1="3.254208141873306" y2="3.254208141873306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.01083827762157" x2="24.4006890996044" y1="2.00420814187332" y2="2.00420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.51083827762159" x2="25.06735576627107" y1="0.5042081418732955" y2="0.5042081418732955"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="25.30654513693459" y2="22.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="22.79040359122621" y2="22.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="25.40299989806297" y2="22.78208583485582"/>
   <ellipse cx="5.54" cy="16.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="23.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.8644003886642" x2="13.8644003886642" y1="36.19213090500035" y2="36.19213090500035"/>
   <ellipse cx="12.54" cy="16.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="18.07076893362115" y2="18.07076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="24.32076893362116" y2="24.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="16.72611741162254" y2="17.85543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="18.24992879670395" y2="16.72611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="16.72611741162252" y2="14.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="17.57076893362115" y2="17.57076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="23.97611741162255" y2="21.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="25.49992879670398" y2="23.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="23.97611741162255" y2="25.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="16.72611741162253" y2="14.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="18.24992879670396" y2="16.72611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="16.72611741162254" y2="17.85543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.09103363843511" x2="9.09103363843511" y1="31.15822158129307" y2="41.41276827235689"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Accessory:10kV接地信号源_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="2"/>
   <rect fill-opacity="0" height="16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,10) scale(1,1) translate(0,0)" width="20" x="2.5" y="2"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="Compensator:电容20200722_0" viewBox="0,0,25,50">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="0.4166666666666643"/>
   <path d="M 15.3333 45.75 L 15.3333 48.75 L 1.33333 48.75 L 1.33333 0.0833333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="22.25" y1="37.91666666666667" y2="35.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="23.25" y1="32.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="23.75" y1="37.91666666666667" y2="37.91666666666667"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,22.25,31.42) scale(1,1) translate(0,0)" width="4" x="20.25" y="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="22.75" y1="39.91666666666667" y2="39.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="21.25" y1="32.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="23.25" y1="38.91666666666667" y2="38.91666666666667"/>
   <rect fill-opacity="0" height="4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.33,32.33) scale(1,1) translate(0,0)" width="2" x="14.33" y="30.33"/>
   <path d="M 15.25 23.75 L 22.25 23.75 L 22.25 32.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="40.10833333333333" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.321296296296296" x2="9.321296296296296" y1="41.38611111111112" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.10833333333333" x2="7.290905947441217" y1="46.02500000000001" y2="46.02500000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="9.333333333333332" y1="43.60833333333333" y2="43.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="43.69166666666667" y2="46.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.374239280774555" x2="7.374239280774555" y1="46.10833333333333" y2="44.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.19166666666666" x2="23.19166666666666" y1="44.66666666666667" y2="46.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.358333333333334" x2="9.358333333333334" y1="30.60833333333334" y2="28.40462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="21.5" y2="39.10833333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333332" x2="15.35833333333334" y1="28.35833333333333" y2="28.35833333333333"/>
   <path d="M 9.35833 34.2072 A 2.96392 1.81747 -180 0 1 9.35833 30.5723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 37.8421 A 2.96392 1.81747 -180 0 1 9.35833 34.2072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 41.3771 A 2.96392 1.81747 -180 0 1 9.35833 37.7421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.85833333333333" x2="13.85833333333334" y1="40.10833333333333" y2="40.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.85833333333333" x2="13.85833333333334" y1="39.10833333333333" y2="39.10833333333333"/>
   <path d="M 22.5 14.7417 A 6.84167 7.10597 -270 1 0 15.394 21.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 22.4547 14.749 L 15.3041 14.749 L 15.3041 0.416667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(85,255,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV民强变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="35kV民强变＿软压板.svg"><rect fill-opacity="0" height="40" width="97.14" x="44" y="458" zvalue="758"/></g>
  <g href="35kV民强变＿直流监控.svg"><rect fill-opacity="0" height="40" width="97.14" x="44" y="519.5" zvalue="760"/></g>
  <g href="单厂站信息-dali.svg"><rect fill-opacity="0" height="40" width="97.14" x="44" y="581" zvalue="955"/></g>
 </g>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,155.004,57.2639) scale(1,1) translate(0,0)" writing-mode="lr" x="155" xml:space="preserve" y="74.26000000000001" zvalue="744">    民强变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,92.5714,478) scale(1,1) translate(0,1.52545e-13)" width="97.14" x="44" y="458" zvalue="758"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.5714,478) scale(1,1) translate(0,0)" writing-mode="lr" x="92.56999999999999" xml:space="preserve" y="482" zvalue="758">软压板</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="10" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,92.5714,539.5) scale(1,1) translate(0,0)" width="97.14" x="44" y="519.5" zvalue="760"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.5714,539.5) scale(1,1) translate(0,0)" writing-mode="lr" x="92.56999999999999" xml:space="preserve" y="543.5" zvalue="760">直流监控</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,92.5714,601) scale(1,1) translate(0,0)" width="97.14" x="44" y="581" zvalue="955"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.5714,601) scale(1,1) translate(0,0)" writing-mode="lr" x="92.56999999999999" xml:space="preserve" y="610" zvalue="955">AVC</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="24" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,146.714,141.143) scale(1,1) translate(0,0)" width="97.14" x="98.14" y="121.14" zvalue="957"/>
  <text fill="rgb(255,170,0)" font-family="FangSong" font-size="19" id="6" stroke="rgb(255,170,0)" text-anchor="middle" transform="rotate(0,146.714,141.143) scale(1,1) translate(0,0)" writing-mode="lr" x="146.71" xml:space="preserve" y="147.64" zvalue="957">全站可控</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,469.763,317.417) scale(1,1) translate(0,0)" writing-mode="lr" x="469.76" xml:space="preserve" y="320.42" zvalue="4">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,260.222,768.17) scale(1,1) translate(0,0)" writing-mode="lr" x="260.22" xml:space="preserve" y="771.17" zvalue="8">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,720.456,390.729) scale(1,1) translate(0,0)" writing-mode="lr" x="720.46" xml:space="preserve" y="393.73" zvalue="10">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.956,440.446) scale(1,1) translate(0,0)" writing-mode="lr" x="723.96" xml:space="preserve" y="443.45" zvalue="12">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.889,739.843) scale(1,1) translate(0,0)" writing-mode="lr" x="721.89" xml:space="preserve" y="742.84" zvalue="17">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.956,683.901) scale(1,1) translate(0,0)" writing-mode="lr" x="723.96" xml:space="preserve" y="686.9" zvalue="19">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,479.396,387.84) scale(1,1) translate(0,0)" writing-mode="lr" x="479.4" xml:space="preserve" y="390.84" zvalue="23">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,472.979,444.45) scale(1,1) translate(0,0)" writing-mode="lr" x="472.98" xml:space="preserve" y="447.45" zvalue="27">39017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.971,91.0476) scale(1,1) translate(0,0)" writing-mode="lr" x="995.97" xml:space="preserve" y="94.05" zvalue="39">35kV巍民洗线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,967.334,286.495) scale(1,1) translate(0,0)" writing-mode="lr" x="967.33" xml:space="preserve" y="289.49" zvalue="41">3711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.319,885.368) scale(1,1) translate(0,0)" writing-mode="lr" x="955.3200000000001" xml:space="preserve" y="888.37" zvalue="126">071</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.819,937.604) scale(1,1) translate(0,0)" writing-mode="lr" x="951.8200000000001" xml:space="preserve" y="940.6" zvalue="128">0716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.819,828.08) scale(1,1) translate(0,0)" writing-mode="lr" x="951.8200000000001" xml:space="preserve" y="831.08" zvalue="130">0711</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,984.698,1091.86) scale(1,1) translate(0,0)" writing-mode="lr" x="984.7" xml:space="preserve" y="1094.86" zvalue="132">10kV鼠街线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,524.368,510.727) scale(1,1) translate(0,0)" writing-mode="lr" x="524.37" xml:space="preserve" y="513.73" zvalue="282">35kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,514.4,970.806) scale(1,1) translate(0,0)" writing-mode="lr" x="514.4" xml:space="preserve" y="973.8099999999999" zvalue="314">10kV故障信号源</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.372,884.654) scale(1,1) translate(0,0)" writing-mode="lr" x="725.37" xml:space="preserve" y="887.65" zvalue="321">075</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.872,827.366) scale(1,1) translate(0,0)" writing-mode="lr" x="721.87" xml:space="preserve" y="830.37" zvalue="323">0751</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="270" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709.448,997.103) scale(1,1) translate(0,0)" writing-mode="lr" x="709.45" xml:space="preserve" y="1000.1" zvalue="331">07567</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.872,937.14) scale(1,1) translate(0,0)" writing-mode="lr" x="721.87" xml:space="preserve" y="940.14" zvalue="334">0756</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.13,885.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.13" xml:space="preserve" y="888.37" zvalue="359">074</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1189.63,937.604) scale(1,1) translate(0,0)" writing-mode="lr" x="1189.63" xml:space="preserve" y="940.6" zvalue="361">0746</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1189.63,828.08) scale(1,1) translate(0,0)" writing-mode="lr" x="1189.63" xml:space="preserve" y="831.08" zvalue="363">0741</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1222.16,1089.01) scale(1,1) translate(0,0)" writing-mode="lr" x="1222.16" xml:space="preserve" y="1092.01" zvalue="365">10kV备用间隔4</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1436.76,885.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1436.76" xml:space="preserve" y="888.37" zvalue="374">073</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1432.72,937.604) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.72" xml:space="preserve" y="940.6" zvalue="376">0736</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1432.72,828.08) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.72" xml:space="preserve" y="831.08" zvalue="378">0731</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1466.16,1089.01) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.16" xml:space="preserve" y="1092.01" zvalue="380">10kV备用间隔3</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1042.29,255.954) scale(1,1) translate(0,0)" writing-mode="lr" x="1042.29" xml:space="preserve" y="258.95" zvalue="492">37167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285.56,538.758) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.56" xml:space="preserve" y="541.76" zvalue="545">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1188.46,390.729) scale(1,1) translate(0,0)" writing-mode="lr" x="1188.46" xml:space="preserve" y="393.73" zvalue="547">3021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1191.96,440.446) scale(1,1) translate(0,0)" writing-mode="lr" x="1191.96" xml:space="preserve" y="443.45" zvalue="549">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1190.48,739.843) scale(1,1) translate(0,0)" writing-mode="lr" x="1190.48" xml:space="preserve" y="742.84" zvalue="551">0021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1192.74,683.901) scale(1,1) translate(0,0)" writing-mode="lr" x="1192.74" xml:space="preserve" y="686.9" zvalue="553">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1262.73,376.248) scale(1,1) translate(0,0)" writing-mode="lr" x="1262.73" xml:space="preserve" y="379.25" zvalue="583">30217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.274,1056.67) scale(1,1) translate(0,0)" writing-mode="lr" x="722.27" xml:space="preserve" y="1059.67" zvalue="593">07597</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1078.99,1091.86) scale(1,1) translate(0,0)" writing-mode="lr" x="1078.99" xml:space="preserve" y="1094.86" zvalue="599">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1676.13,885.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1676.13" xml:space="preserve" y="888.37" zvalue="608">072</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1672.63,937.604) scale(1,1) translate(0,0)" writing-mode="lr" x="1672.63" xml:space="preserve" y="940.6" zvalue="610">0726</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1672.63,828.08) scale(1,1) translate(0,0)" writing-mode="lr" x="1672.63" xml:space="preserve" y="831.08" zvalue="612">0721</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1706.36,1089.01) scale(1,1) translate(0,0)" writing-mode="lr" x="1706.36" xml:space="preserve" y="1092.01" zvalue="614">10kV青华线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444.44,394.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.44" xml:space="preserve" y="397.93" zvalue="639">3721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,485.93,831.366) scale(1,1) translate(0,0)" writing-mode="lr" x="485.93" xml:space="preserve" y="834.37" zvalue="643">0761</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089.47,1005.38) scale(1,1) translate(0,0)" writing-mode="lr" x="1089.47" xml:space="preserve" y="1008.38" zvalue="646">0718</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,265.321,828.08) scale(1,1) translate(0,0)" writing-mode="lr" x="265.32" xml:space="preserve" y="831.08" zvalue="816">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,289.877,970.806) scale(1,1) translate(0,0)" writing-mode="lr" x="289.88" xml:space="preserve" y="973.8099999999999" zvalue="818">10kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1469.04,510.616) scale(1,1) translate(0,0)" writing-mode="lr" x="1469.04" xml:space="preserve" y="513.62" zvalue="838">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.786,572.139) scale(1,1) translate(0,0)" writing-mode="lr" x="820.79" xml:space="preserve" y="575.14" zvalue="840">5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285.56,572.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.56" xml:space="preserve" y="575.14" zvalue="842">2MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.83,1091.86) scale(1,1) translate(0,0)" writing-mode="lr" x="747.83" xml:space="preserve" y="1094.86" zvalue="860">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1742.69,989.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1742.69" xml:space="preserve" y="993.86" zvalue="890">07267</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="387" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.2321,346.179) scale(1,1) translate(0,0)" writing-mode="lr" x="85.23" xml:space="preserve" y="352.18" zvalue="934">负荷总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="386" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.4821,381.929) scale(1,1) translate(0,0)" writing-mode="lr" x="81.48" xml:space="preserve" y="387.93" zvalue="935">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="385" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.2321,416.929) scale(1,1) translate(0,0)" writing-mode="lr" x="85.23" xml:space="preserve" y="422.93" zvalue="936">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="384" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.1488,232.429) scale(1,1) translate(0,0)" writing-mode="lr" x="79.15000000000001" xml:space="preserve" y="238.43" zvalue="940">事故</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="383" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90.1488,299.179) scale(1,1) translate(0,0)" writing-mode="lr" x="90.15000000000001" xml:space="preserve" y="305.18" zvalue="941">是否失压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="382" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,171.649,299.179) scale(1,1) translate(0,0)" writing-mode="lr" x="171.65" xml:space="preserve" y="305.18" zvalue="943">失压排除</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="381" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139.149,232.429) scale(1,1) translate(0,0)" writing-mode="lr" x="139.15" xml:space="preserve" y="238.43" zvalue="945">异常</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="380" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.149,232.429) scale(1,1) translate(0,0)" writing-mode="lr" x="199.15" xml:space="preserve" y="238.43" zvalue="946">告知</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="379" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.435,382.5) scale(1,1) translate(0,0)" writing-mode="lr" x="200.43" xml:space="preserve" y="388.5" zvalue="947">民强变-01-2023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="378" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.435,417.5) scale(1,1) translate(0,0)" writing-mode="lr" x="196.43" xml:space="preserve" y="423.5" zvalue="948">2023-09-4</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.786,538.758) scale(1,1) translate(0,0)" writing-mode="lr" x="820.79" xml:space="preserve" y="544.26" zvalue="960">#1主变</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="204">
   <path class="kv35" d="M 428.97 335.75 L 1549 335.75" stroke-width="6" zvalue="3"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674255241220" ObjectName="35kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674255241220"/></metadata>
  <path d="M 428.97 335.75 L 1549 335.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 219.78 786.34 L 1805.5 786.34" stroke-width="6" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674255306756" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674255306756"/></metadata>
  <path d="M 219.78 786.34 L 1805.5 786.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="215">
   <use class="kv35" height="30" transform="rotate(0,753.19,391.729) scale(-1.11133,0.814667) translate(-1430.09,86.3368)" width="15" x="744.8553322244479" xlink:href="#Disconnector:刀闸_0" y="379.5093688964844" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399305734" ObjectName="#1主变35kV侧3011"/>
   <cge:TPSR_Ref TObjectID="6192450399305734"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,753.19,391.729) scale(-1.11133,0.814667) translate(-1430.09,86.3368)" width="15" x="744.8553322244479" y="379.5093688964844"/></g>
  <g id="229">
   <use class="kv10" height="30" transform="rotate(0,753.524,740.843) scale(-1.11133,0.814667) translate(-1430.73,165.759)" width="15" x="745.1892020231583" xlink:href="#Disconnector:刀闸_0" y="728.6230163574216" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398912518" ObjectName="#1主变10kV侧0011"/>
   <cge:TPSR_Ref TObjectID="6192450398912518"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,753.524,740.843) scale(-1.11133,0.814667) translate(-1430.73,165.759)" width="15" x="745.1892020231583" y="728.6230163574216"/></g>
  <g id="243">
   <use class="kv35" height="30" transform="rotate(0,514.353,391.729) scale(-1.11133,0.814667) translate(-976.344,86.3368)" width="15" x="506.0183817573409" xlink:href="#Disconnector:刀闸_0" y="379.5093687107587" zvalue="22"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398846981" ObjectName="35kVⅠ母电压互感器3901"/>
   <cge:TPSR_Ref TObjectID="6192450398846981"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,514.353,391.729) scale(-1.11133,0.814667) translate(-976.344,86.3368)" width="15" x="506.0183817573409" y="379.5093687107587"/></g>
  <g id="190">
   <use class="kv35" height="30" transform="rotate(0,996.069,291.495) scale(-1.11133,0.814667) translate(-1891.52,63.5338)" width="15" x="987.7340193176797" xlink:href="#Disconnector:刀闸_0" y="279.2745271809895" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398584837" ObjectName="35kV巍民洗线3711"/>
   <cge:TPSR_Ref TObjectID="6192450398584837"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,996.069,291.495) scale(-1.11133,0.814667) translate(-1891.52,63.5338)" width="15" x="987.7340193176797" y="279.2745271809895"/></g>
  <g id="244">
   <use class="kv10" height="30" transform="rotate(0,984.766,938.854) scale(-1.11133,0.814667) translate(-1870.04,210.805)" width="15" x="976.431153021559" xlink:href="#Disconnector:刀闸_0" y="926.6338195800781" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399174662" ObjectName="10kV鼠街线0716"/>
   <cge:TPSR_Ref TObjectID="6192450399174662"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,984.766,938.854) scale(-1.11133,0.814667) translate(-1870.04,210.805)" width="15" x="976.431153021559" y="926.6338195800781"/></g>
  <g id="240">
   <use class="kv10" height="30" transform="rotate(0,984.554,829.08) scale(-1.11133,0.814667) translate(-1869.64,185.832)" width="15" x="976.2187775223586" xlink:href="#Disconnector:刀闸_0" y="816.8600158691406" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399109126" ObjectName="10kV鼠街线0711"/>
   <cge:TPSR_Ref TObjectID="6192450399109126"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,984.554,829.08) scale(-1.11133,0.814667) translate(-1869.64,185.832)" width="15" x="976.2187775223586" y="816.8600158691406"/></g>
  <g id="266">
   <use class="kv10" height="30" transform="rotate(0,752.896,829.08) scale(-1.11133,0.814667) translate(-1429.53,185.832)" width="15" x="744.5611392458079" xlink:href="#Disconnector:刀闸_0" y="816.8600158691406" zvalue="322"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399502341" ObjectName="10kV1号电容器0751"/>
   <cge:TPSR_Ref TObjectID="6192450399502341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,752.896,829.08) scale(-1.11133,0.814667) translate(-1429.53,185.832)" width="15" x="744.5611392458079" y="816.8600158691406"/></g>
  <g id="273">
   <use class="kv10" height="30" transform="rotate(0,753.079,938.854) scale(-1.11133,0.814667) translate(-1429.88,210.805)" width="15" x="744.7442236328126" xlink:href="#Disconnector:刀闸_0" y="926.6338195800781" zvalue="333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401140742" ObjectName="10kV1号电容器0756"/>
   <cge:TPSR_Ref TObjectID="6192450401140742"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,753.079,938.854) scale(-1.11133,0.814667) translate(-1429.88,210.805)" width="15" x="744.7442236328126" y="926.6338195800781"/></g>
  <g id="317">
   <use class="kv10" height="30" transform="rotate(0,1222.21,938.854) scale(-1.11133,0.814667) translate(-2321.14,210.805)" width="15" x="1213.875169936671" xlink:href="#Disconnector:刀闸_0" y="926.6338195800781" zvalue="360"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400157701" ObjectName="10kV备用间隔0746"/>
   <cge:TPSR_Ref TObjectID="6192450400157701"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1222.21,938.854) scale(-1.11133,0.814667) translate(-2321.14,210.805)" width="15" x="1213.875169936671" y="926.6338195800781"/></g>
  <g id="316">
   <use class="kv10" height="30" transform="rotate(0,1222.36,829.08) scale(-1.11133,0.814667) translate(-2321.43,185.832)" width="15" x="1214.026378685152" xlink:href="#Disconnector:刀闸_0" y="816.860015774929" zvalue="362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400092165" ObjectName="10kV备用间隔0741"/>
   <cge:TPSR_Ref TObjectID="6192450400092165"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1222.36,829.08) scale(-1.11133,0.814667) translate(-2321.43,185.832)" width="15" x="1214.026378685152" y="816.860015774929"/></g>
  <g id="332">
   <use class="kv10" height="30" transform="rotate(0,1466.21,938.854) scale(-1.11133,0.814667) translate(-2784.7,210.805)" width="15" x="1457.875169936671" xlink:href="#Disconnector:刀闸_0" y="926.6338195800781" zvalue="375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400419845" ObjectName="10kV备用间隔0736"/>
   <cge:TPSR_Ref TObjectID="6192450400419845"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1466.21,938.854) scale(-1.11133,0.814667) translate(-2784.7,210.805)" width="15" x="1457.875169936671" y="926.6338195800781"/></g>
  <g id="330">
   <use class="kv10" height="30" transform="rotate(0,1465.45,829.08) scale(-1.11133,0.814667) translate(-2783.26,185.832)" width="15" x="1457.117287776061" xlink:href="#Disconnector:刀闸_0" y="816.860015774929" zvalue="377"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400354309" ObjectName="10kV备用间隔0731"/>
   <cge:TPSR_Ref TObjectID="6192450400354309"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1465.45,829.08) scale(-1.11133,0.814667) translate(-2783.26,185.832)" width="15" x="1457.117287776061" y="816.860015774929"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(0,1221.19,391.729) scale(-1.11133,0.814667) translate(-2319.21,86.3368)" width="15" x="1212.855332224448" xlink:href="#Disconnector:刀闸_0" y="379.5093688964844" zvalue="546"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400681989" ObjectName="#2主变35kV侧3021"/>
   <cge:TPSR_Ref TObjectID="6192450400681989"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1221.19,391.729) scale(-1.11133,0.814667) translate(-2319.21,86.3368)" width="15" x="1212.855332224448" y="379.5093688964844"/></g>
  <g id="117">
   <use class="kv10" height="30" transform="rotate(0,1222.33,740.843) scale(-1.11133,0.814667) translate(-2321.38,165.759)" width="15" x="1213.996803185951" xlink:href="#Disconnector:刀闸_0" y="728.6230163210087" zvalue="550"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400616453" ObjectName="#2主变10kV侧0021"/>
   <cge:TPSR_Ref TObjectID="6192450400616453"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1222.33,740.843) scale(-1.11133,0.814667) translate(-2321.38,165.759)" width="15" x="1213.996803185951" y="728.6230163210087"/></g>
  <g id="37">
   <use class="kv10" height="30" transform="rotate(0,1706.43,938.854) scale(-1.11133,0.814667) translate(-3241.07,210.805)" width="15" x="1698.094586165861" xlink:href="#Disconnector:刀闸_0" y="926.6338195800781" zvalue="609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401599493" ObjectName="10kV青华线0726"/>
   <cge:TPSR_Ref TObjectID="6192450401599493"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1706.43,938.854) scale(-1.11133,0.814667) translate(-3241.07,210.805)" width="15" x="1698.094586165861" y="926.6338195800781"/></g>
  <g id="36">
   <use class="kv10" height="30" transform="rotate(0,1705.36,829.08) scale(-1.11133,0.814667) translate(-3239.04,185.832)" width="15" x="1697.026378685152" xlink:href="#Disconnector:刀闸_0" y="816.8600158941383" zvalue="611"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401533957" ObjectName="10kV青华线0721"/>
   <cge:TPSR_Ref TObjectID="6192450401533957"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1705.36,829.08) scale(-1.11133,0.814667) translate(-3239.04,185.832)" width="15" x="1697.026378685152" y="816.8600158941383"/></g>
  <g id="200">
   <use class="kv10" height="30" transform="rotate(0,294.933,829.08) scale(-1.11133,0.814667) translate(-559.486,185.832)" width="15" x="286.5984988064231" xlink:href="#Disconnector:刀闸_0" y="816.8600159073837" zvalue="815"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452281761795" ObjectName="10kVⅠ母电压互感器0901"/>
   <cge:TPSR_Ref TObjectID="6192452281761795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,294.933,829.08) scale(-1.11133,0.814667) translate(-559.486,185.832)" width="15" x="286.5984988064231" y="816.8600159073837"/></g>
 </g>
 <g id="BreakerClass">
  <g id="217">
   <use class="kv35" height="20" transform="rotate(0,753.183,440.696) scale(1.828,1.8835) translate(-337.017,-197.884)" width="10" x="744.0432914944583" xlink:href="#Breaker:开关_0" y="421.8612899780273" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577263620" ObjectName="#1主变35kV侧301"/>
   <cge:TPSR_Ref TObjectID="6473924577263620"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,753.183,440.696) scale(1.828,1.8835) translate(-337.017,-197.884)" width="10" x="744.0432914944583" y="421.8612899780273"/></g>
  <g id="230">
   <use class="kv10" height="20" transform="rotate(0,753.305,684.151) scale(1.828,1.8835) translate(-337.072,-312.082)" width="10" x="744.1647857939683" xlink:href="#Breaker:开关_0" y="665.3158416748046" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924576935940" ObjectName="#1主变10kV侧001"/>
   <cge:TPSR_Ref TObjectID="6473924576935940"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,753.305,684.151) scale(1.828,1.8835) translate(-337.072,-312.082)" width="10" x="744.1647857939683" y="665.3158416748046"/></g>
  <g id="245">
   <use class="kv10" height="20" transform="rotate(0,984.547,885.618) scale(1.828,1.8835) translate(-441.814,-406.585)" width="10" x="975.406736792369" xlink:href="#Breaker:开关_0" y="866.7827911376953" zvalue="125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577001476" ObjectName="10kV鼠街线071"/>
   <cge:TPSR_Ref TObjectID="6473924577001476"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,984.547,885.618) scale(1.828,1.8835) translate(-441.814,-406.585)" width="10" x="975.406736792369" y="866.7827911376953"/></g>
  <g id="267">
   <use class="kv10" height="20" transform="rotate(0,752.889,885.618) scale(1.828,1.8835) translate(-336.884,-406.585)" width="10" x="743.7490985158183" xlink:href="#Breaker:开关_0" y="866.7827910384424" zvalue="320"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577067012" ObjectName="10kV1号电容器075"/>
   <cge:TPSR_Ref TObjectID="6473924577067012"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,752.889,885.618) scale(1.828,1.8835) translate(-336.884,-406.585)" width="10" x="743.7490985158183" y="866.7827910384424"/></g>
  <g id="318">
   <use class="kv10" height="20" transform="rotate(0,1222.35,885.618) scale(1.828,1.8835) translate(-549.53,-406.585)" width="10" x="1213.214337955162" xlink:href="#Breaker:开关_0" y="866.7827911973884" zvalue="358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577132548" ObjectName="10kV备用间隔074"/>
   <cge:TPSR_Ref TObjectID="6473924577132548"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1222.35,885.618) scale(1.828,1.8835) translate(-549.53,-406.585)" width="10" x="1213.214337955162" y="866.7827911973884"/></g>
  <g id="336">
   <use class="kv10" height="20" transform="rotate(0,1465.99,885.618) scale(1.828,1.8835) translate(-659.886,-406.585)" width="10" x="1456.850753707481" xlink:href="#Breaker:开关_0" y="866.7827911973884" zvalue="373"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577198084" ObjectName="10kV备用间隔073"/>
   <cge:TPSR_Ref TObjectID="6473924577198084"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1465.99,885.618) scale(1.828,1.8835) translate(-659.886,-406.585)" width="10" x="1456.850753707481" y="866.7827911973884"/></g>
  <g id="118">
   <use class="kv35" height="20" transform="rotate(0,1221.18,440.696) scale(1.828,1.8835) translate(-549,-197.884)" width="10" x="1212.043291494458" xlink:href="#Breaker:开关_0" y="421.8612899780273" zvalue="548"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577394692" ObjectName="#2主变35kV侧302"/>
   <cge:TPSR_Ref TObjectID="6473924577394692"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1221.18,440.696) scale(1.828,1.8835) translate(-549,-197.884)" width="10" x="1212.043291494458" y="421.8612899780273"/></g>
  <g id="116">
   <use class="kv10" height="20" transform="rotate(0,1222.11,684.151) scale(1.828,1.8835) translate(-549.421,-312.082)" width="10" x="1212.972386956762" xlink:href="#Breaker:开关_0" y="665.3158414888543" zvalue="552"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577329156" ObjectName="#2主变10kV侧002"/>
   <cge:TPSR_Ref TObjectID="6473924577329156"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1222.11,684.151) scale(1.828,1.8835) translate(-549.421,-312.082)" width="10" x="1212.972386956762" y="665.3158414888543"/></g>
  <g id="38">
   <use class="kv10" height="20" transform="rotate(0,1706.21,885.618) scale(1.828,1.8835) translate(-768.695,-406.585)" width="10" x="1697.070169936671" xlink:href="#Breaker:开关_0" y="866.7827911973884" zvalue="607"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577460228" ObjectName="10kV青华线072"/>
   <cge:TPSR_Ref TObjectID="6473924577460228"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1706.21,885.618) scale(1.828,1.8835) translate(-768.695,-406.585)" width="10" x="1697.070169936671" y="866.7827911973884"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="349">
   <use class="kv35" height="30" transform="rotate(270,473.08,428.74) scale(-1.0125,-0.866) translate(-940.245,-925.832)" width="12" x="467.0053639156317" xlink:href="#GroundDisconnector:地刀12_0" y="415.750330151754" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398781445" ObjectName="35kVⅠ母电压互感器39017"/>
   <cge:TPSR_Ref TObjectID="6192450398781445"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,473.08,428.74) scale(-1.0125,-0.866) translate(-940.245,-925.832)" width="12" x="467.0053639156317" y="415.750330151754"/></g>
  <g id="271">
   <use class="kv10" height="30" transform="rotate(90,711.254,981.643) scale(1.0125,0.866) translate(-8.70591,149.884)" width="12" x="705.1785083324872" xlink:href="#GroundDisconnector:地刀12_0" y="968.6531893246581" zvalue="330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399633414" ObjectName="10kV1号电容器07567"/>
   <cge:TPSR_Ref TObjectID="6192450399633414"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,711.254,981.643) scale(1.0125,0.866) translate(-8.70591,149.884)" width="12" x="705.1785083324872" y="968.6531893246581"/></g>
  <g id="17">
   <use class="kv35" height="30" transform="rotate(270,1042.39,240.244) scale(-1.0125,0.866) translate(-2071.84,35.164)" width="12" x="1036.315969976238" xlink:href="#GroundDisconnector:地刀12_0" y="227.2540300325571" zvalue="491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400550917" ObjectName="35kV巍民洗线37167"/>
   <cge:TPSR_Ref TObjectID="6192450400550917"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1042.39,240.244) scale(-1.0125,0.866) translate(-2071.84,35.164)" width="12" x="1036.315969976238" y="227.2540300325571"/></g>
  <g id="60">
   <use class="kv35" height="30" transform="rotate(270,1262.84,360.538) scale(-1.0125,0.866) translate(-2510.01,53.7777)" width="12" x="1256.760414420682" xlink:href="#GroundDisconnector:地刀12_0" y="347.5483099497337" zvalue="582"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401075205" ObjectName="#2主变35kV侧30217"/>
   <cge:TPSR_Ref TObjectID="6192450401075205"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1262.84,360.538) scale(-1.0125,0.866) translate(-2510.01,53.7777)" width="12" x="1256.760414420682" y="347.5483099497337"/></g>
  <g id="14">
   <use class="kv10" height="30" transform="rotate(0,753.079,1062.21) scale(1.0125,0.866) translate(-9.22227,162.35)" width="12" x="747.0042236328125" xlink:href="#GroundDisconnector:地刀12_0" y="1049.217180552815" zvalue="592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401271813" ObjectName="10kV1号电容器07597"/>
   <cge:TPSR_Ref TObjectID="6192450401271813"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,753.079,1062.21) scale(1.0125,0.866) translate(-9.22227,162.35)" width="12" x="747.0042236328125" y="1049.217180552815"/></g>
  <g id="308">
   <use class="kv10" height="30" transform="rotate(270,1741.36,975.103) scale(-1.0125,0.866) translate(-3461.14,148.872)" width="12" x="1735.281072435051" xlink:href="#GroundDisconnector:地刀12_0" y="962.1125194681986" zvalue="889"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452281892867" ObjectName="10kV青华线07267"/>
   <cge:TPSR_Ref TObjectID="6192452281892867"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1741.36,975.103) scale(-1.0125,0.866) translate(-3461.14,148.872)" width="12" x="1735.281072435051" y="962.1125194681986"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="197">
   <path class="kv35" d="M 514.29 443.18 L 514.29 403.74" stroke-width="1" zvalue="28"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="243@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.29 443.18 L 514.29 403.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 485.48 428.73 L 514.29 428.73" stroke-width="1" zvalue="30"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 485.48 428.73 L 514.29 428.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv10" d="M 753.24 666.13 L 753.27 600.37" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="29@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.24 666.13 L 753.27 600.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv10" d="M 984.46 817.26 L 984.46 786.34" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="211@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.46 817.26 L 984.46 786.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 984.49 841.09 L 984.49 867.6" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@1" LinkObjectIDznd="245@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.49 841.09 L 984.49 867.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv10" d="M 984.67 903.61 L 984.67 927.04" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@1" LinkObjectIDznd="244@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.67 903.61 L 984.67 927.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv10" d="M 984.7 1043.36 L 984.7 950.86" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="244@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.7 1043.36 L 984.7 950.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv10" d="M 753.43 729.03 L 753.43 702.14" stroke-width="1" zvalue="304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="230@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.43 729.03 L 753.43 702.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv35" d="M 753.12 403.74 L 753.12 422.68" stroke-width="1" zvalue="306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@1" LinkObjectIDznd="217@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.12 403.74 L 753.12 422.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv10" d="M 515 821.52 L 515 786.34" stroke-width="1" zvalue="316"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53" LinkObjectIDznd="211@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 515 821.52 L 515 786.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv10" d="M 752.83 841.09 L 752.83 867.6" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.83 841.09 L 752.83 867.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 1222.29 841.09 L 1222.29 867.6" stroke-width="1" zvalue="368"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316@1" LinkObjectIDznd="318@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222.29 841.09 L 1222.29 867.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 1222.48 903.61 L 1222.48 927.04" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318@1" LinkObjectIDznd="317@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222.48 903.61 L 1222.48 927.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="kv10" d="M 1222.16 1043.36 L 1222.14 950.86" stroke-width="1" zvalue="370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="317@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222.16 1043.36 L 1222.14 950.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="kv10" d="M 1465.35 817.26 L 1465.35 786.34" stroke-width="1" zvalue="382"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="211@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1465.35 817.26 L 1465.35 786.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv10" d="M 1465.38 841.09 L 1465.38 867.6" stroke-width="1" zvalue="383"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@1" LinkObjectIDznd="336@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1465.38 841.09 L 1465.38 867.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv10" d="M 1466.11 903.61 L 1466.11 927.04" stroke-width="1" zvalue="384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@1" LinkObjectIDznd="332@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1466.11 903.61 L 1466.11 927.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv10" d="M 1466.16 1043.36 L 1466.14 950.86" stroke-width="1" zvalue="385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@0" LinkObjectIDznd="332@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1466.16 1043.36 L 1466.14 950.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 1222.05 666.13 L 1222.05 596.37" stroke-width="1" zvalue="554"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="120@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222.05 666.13 L 1222.05 596.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1222.23 729.03 L 1222.23 702.14" stroke-width="1" zvalue="556"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222.23 729.03 L 1222.23 702.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv35" d="M 1221.12 403.74 L 1221.12 422.68" stroke-width="1" zvalue="558"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.12 403.74 L 1221.12 422.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 1221.31 458.68 L 1221.31 513.5" stroke-width="1" zvalue="580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="120@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.31 458.68 L 1221.31 513.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv35" d="M 1250.43 360.53 L 1221.09 360.53" stroke-width="1" zvalue="584"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="23" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.43 360.53 L 1221.09 360.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 752.98 927.04 L 753.01 903.61" stroke-width="1" zvalue="585"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="267@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.98 927.04 L 753.01 903.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 1705.29 841.09 L 1705.29 867.6" stroke-width="1" zvalue="617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@1" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1705.29 841.09 L 1705.29 867.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 1706.33 903.61 L 1706.33 927.04" stroke-width="1" zvalue="618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1706.33 903.61 L 1706.33 927.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 1706.36 1043.36 L 1706.36 950.86" stroke-width="1" zvalue="619"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="37@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1706.36 1043.36 L 1706.36 950.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 515 821.52 L 515 920.25" stroke-width="1" zvalue="643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="255@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 515 821.52 L 515 920.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 996 303.51 L 996 335.75" stroke-width="1" zvalue="680"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@1" LinkObjectIDznd="204@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 996 303.51 L 996 335.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 514.26 379.91 L 514.26 335.75" stroke-width="1" zvalue="681"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="204@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.26 379.91 L 514.26 335.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv35" d="M 753.09 379.91 L 753.09 335.75" stroke-width="1" zvalue="682"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="204@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.09 379.91 L 753.09 335.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 1221.09 379.91 L 1221.09 335.75" stroke-width="1" zvalue="683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="204@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.09 379.91 L 1221.09 335.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 531.63 865.47 L 531.63 854.22 L 515 854.22" stroke-width="1" zvalue="685"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="53" MaxPinNum="2"/>
   </metadata>
  <path d="M 531.63 865.47 L 531.63 854.22 L 515 854.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv35" d="M 1469.82 386.24 L 1469.82 335.75" stroke-width="1" zvalue="747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="204@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1469.82 386.24 L 1469.82 335.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 1495.12 365.02 L 1495.12 354.13 L 1469.82 354.13" stroke-width="1" zvalue="749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1495.12 365.02 L 1495.12 354.13 L 1469.82 354.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 984.7 978.2 L 1065.15 978.2 L 1065.15 997.31" stroke-width="1" zvalue="752"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.7 978.2 L 1065.15 978.2 L 1065.15 997.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1016.71 999 L 1016.71 978.2" stroke-width="1" zvalue="754"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016.71 999 L 1016.71 978.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 984.7 978.4 L 960 978.4 L 960 999" stroke-width="1" zvalue="756"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207" LinkObjectIDznd="238@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.7 978.4 L 960 978.4 L 960 999" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv10" d="M 294.84 817.26 L 294.84 786.34" stroke-width="1" zvalue="819"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="211@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 294.84 817.26 L 294.84 786.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 294.87 841.09 L 294.92 877.78" stroke-width="1" zvalue="821"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@1" LinkObjectIDznd="395@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 294.87 841.09 L 294.92 877.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 294.92 877.54 L 294.92 903.53" stroke-width="1" zvalue="822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90" LinkObjectIDznd="142@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 294.92 877.54 L 294.92 903.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv35" d="M 1469.82 431.42 L 1469.82 386.24" stroke-width="1" zvalue="838"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1469.82 431.42 L 1469.82 386.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 753.01 950.86 L 753.01 981.61" stroke-width="1" zvalue="861"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@1" LinkObjectIDznd="805@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.01 950.86 L 753.01 981.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv10" d="M 723.66 981.63 L 753.01 981.63" stroke-width="1" zvalue="862"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="254" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.66 981.63 L 753.01 981.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 753.07 981.63 L 753.07 1049.8" stroke-width="1" zvalue="863"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258" LinkObjectIDznd="14@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.07 981.63 L 753.07 1049.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv10" d="M 1065.15 997.31 L 1065.15 1025.7" stroke-width="1" zvalue="864"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112" LinkObjectIDznd="144@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.15 997.31 L 1065.15 1025.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 1705.26 817.26 L 1705.26 786.34" stroke-width="1" zvalue="865"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="211@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1705.26 817.26 L 1705.26 786.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv10" d="M 1196.55 999 L 1196.55 975.35 L 1222.15 975.35" stroke-width="1" zvalue="868"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="310" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.55 999 L 1196.55 975.35 L 1222.15 975.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv10" d="M 1440.55 999 L 1440.55 975.05 L 1466.15 975.05" stroke-width="1" zvalue="869"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="324" MaxPinNum="2"/>
   </metadata>
  <path d="M 1440.55 999 L 1440.55 975.05 L 1466.15 975.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv10" d="M 1679.55 999 L 1679.55 975.09 L 1706.36 975.09" stroke-width="1" zvalue="870"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 1679.55 999 L 1679.55 975.09 L 1706.36 975.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv35" d="M 995.97 146.23 L 995.97 279.68" stroke-width="1" zvalue="871"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 995.97 146.23 L 995.97 279.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv35" d="M 967.5 240.23 L 995.97 240.23" stroke-width="1" zvalue="872"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="280" MaxPinNum="2"/>
   </metadata>
  <path d="M 967.5 240.23 L 995.97 240.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv35" d="M 1029.99 240.23 L 995.97 240.23" stroke-width="1" zvalue="873"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="281" MaxPinNum="2"/>
   </metadata>
  <path d="M 1029.99 240.23 L 995.97 240.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="kv10" d="M 753.46 752.85 L 753.46 786.34" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@1" LinkObjectIDznd="211@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.46 752.85 L 753.46 786.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv10" d="M 752.67 786.34 L 752.67 817.26" stroke-width="1" zvalue="885"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@6" LinkObjectIDznd="266@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.67 786.34 L 752.67 817.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv10" d="M 1222.26 752.85 L 1222.26 786.34" stroke-width="1" zvalue="886"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@1" LinkObjectIDznd="211@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222.26 752.85 L 1222.26 786.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv10" d="M 1222.63 786.34 L 1222.63 817.26" stroke-width="1" zvalue="887"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@8" LinkObjectIDznd="316@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222.63 786.34 L 1222.63 817.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="333">
   <path class="kv10" d="M 1728.95 975.09 L 1706.36 975.09" stroke-width="1" zvalue="890"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 1728.95 975.09 L 1706.36 975.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv35" d="M 753.31 517.5 L 753.31 458.68" stroke-width="1" zvalue="960"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="217@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.31 517.5 L 753.31 458.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="191">
   <use class="kv35" height="30" transform="rotate(0,995.971,125.468) scale(2.06349,1.39841) translate(-509.586,-29.7702)" width="7" x="988.7492475329343" xlink:href="#ACLineSegment:线路_0" y="104.4920634920634" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249350733826" ObjectName="35kV巍民洗线"/>
   <cge:TPSR_Ref TObjectID="8444249350733826_5066549588131842"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,995.971,125.468) scale(2.06349,1.39841) translate(-509.586,-29.7702)" width="7" x="988.7492475329343" y="104.4920634920634"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="145">
   <use class="kv35" height="26" transform="rotate(90,955.136,240.199) scale(1,1) translate(0,-2.01793e-13)" width="12" x="949.1359194105149" xlink:href="#Accessory:避雷器1_0" y="227.199086321934" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400747525" ObjectName="35kV巍民洗线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,955.136,240.199) scale(1,1) translate(0,-2.01793e-13)" width="12" x="949.1359194105149" y="227.199086321934"/></g>
  <g id="238">
   <use class="kv10" height="26" transform="rotate(0,959.963,1011.37) scale(1,1) translate(3.91872e-12,-1.99514e-12)" width="12" x="953.9634673991999" xlink:href="#Accessory:避雷器1_0" y="998.3663940429688" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398978054" ObjectName="10kV鼠街线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,959.963,1011.37) scale(1,1) translate(3.91872e-12,-1.99514e-12)" width="12" x="953.9634673991999" y="998.3663940429688"/></g>
  <g id="214">
   <use class="kv35" height="42" transform="rotate(0,526.187,468.091) scale(1.25,-1.22255) translate(-101.487,-846.297)" width="30" x="507.4365012046931" xlink:href="#Accessory:4卷PT带容断器_0" y="442.4172715830314" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399240198" ObjectName="35kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,526.187,468.091) scale(1.25,-1.22255) translate(-101.487,-846.297)" width="30" x="507.4365012046931" y="442.4172715830314"/></g>
  <g id="255">
   <use class="kv10" height="20" transform="rotate(0,514.996,933.583) scale(1.66667,1.66667) translate(-197.665,-366.767)" width="25" x="494.1631043839695" xlink:href="#Accessory:10kV接地信号源_0" y="916.9166673024492" zvalue="313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399371269" ObjectName="10kV故障信号源"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,514.996,933.583) scale(1.66667,1.66667) translate(-197.665,-366.767)" width="25" x="494.1631043839695" y="916.9166673024492"/></g>
  <g id="256">
   <use class="kv10" height="26" transform="rotate(0,531.599,877.84) scale(1,1) translate(-3.03437e-12,-7.68133e-13)" width="12" x="525.5989517978426" xlink:href="#Accessory:避雷器1_0" y="864.8404274665521" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399436805" ObjectName="10kV故障信号源避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,531.599,877.84) scale(1,1) translate(-3.03437e-12,-7.68133e-13)" width="12" x="525.5989517978426" y="864.8404274665521"/></g>
  <g id="314">
   <use class="kv10" height="26" transform="rotate(0,1196.52,1011.37) scale(1,1) translate(-1.05739e-12,-1.99514e-12)" width="12" x="1190.519022954755" xlink:href="#Accessory:避雷器1_0" y="998.3663959178007" zvalue="366"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399961093" ObjectName="10kV备用间隔4避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1196.52,1011.37) scale(1,1) translate(-1.05739e-12,-1.99514e-12)" width="12" x="1190.519022954755" y="998.3663959178007"/></g>
  <g id="328">
   <use class="kv10" height="26" transform="rotate(0,1440.52,1011.37) scale(1,1) translate(0,-1.99514e-12)" width="12" x="1434.519022954755" xlink:href="#Accessory:避雷器1_0" y="998.3663959178007" zvalue="381"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400223237" ObjectName="10kV备用间隔3避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1440.52,1011.37) scale(1,1) translate(0,-1.99514e-12)" width="12" x="1434.519022954755" y="998.3663959178007"/></g>
  <g id="49">
   <use class="kv35" height="26" transform="rotate(0,1495.09,377.385) scale(1,1) translate(6.1169e-12,1.61819e-13)" width="12" x="1489.08541436001" xlink:href="#Accessory:避雷器1_0" y="364.3848484848485" zvalue="575"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400944133" ObjectName="35kV1号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1495.09,377.385) scale(1,1) translate(6.1169e-12,1.61819e-13)" width="12" x="1489.08541436001" y="364.3848484848485"/></g>
  <g id="22">
   <use class="kv10" height="26" transform="rotate(0,1016.68,1011.37) scale(1,1) translate(-8.97662e-13,-1.99514e-12)" width="12" x="1010.677753113486" xlink:href="#Accessory:避雷器1_0" y="998.3663940429688" zvalue="603"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401337349" ObjectName="10kV鼠街线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1016.68,1011.37) scale(1,1) translate(-8.97662e-13,-1.99514e-12)" width="12" x="1010.677753113486" y="998.3663940429688"/></g>
  <g id="34">
   <use class="kv10" height="26" transform="rotate(0,1679.52,1011.37) scale(1,1) translate(-1.48638e-12,-1.99514e-12)" width="12" x="1673.519022954755" xlink:href="#Accessory:避雷器1_0" y="998.3663959178007" zvalue="615"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401402885" ObjectName="10kV青华线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1679.52,1011.37) scale(1,1) translate(-1.48638e-12,-1.99514e-12)" width="12" x="1673.519022954755" y="998.3663959178007"/></g>
  <g id="44">
   <use class="kv35" height="18" transform="rotate(0,1469.8,394.538) scale(0.903738,1.04776) translate(156.075,-17.5548)" width="10" x="1465.284286899531" xlink:href="#Accessory:熔断器_0" y="385.1077832237416" zvalue="638"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401665029" ObjectName="35kV1号站用变3721"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1469.8,394.538) scale(0.903738,1.04776) translate(156.075,-17.5548)" width="10" x="1465.284286899531" y="385.1077832237416"/></g>
  <g id="51">
   <use class="kv10" height="18" transform="rotate(0,514.981,829.814) scale(0.903738,1.04776) translate(54.3719,-37.3965)" width="10" x="510.4626837958221" xlink:href="#Accessory:熔断器_0" y="820.3842493051831" zvalue="642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401730565" ObjectName="10kV故障信号源0761"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,514.981,829.814) scale(0.903738,1.04776) translate(54.3719,-37.3965)" width="10" x="510.4626837958221" y="820.3842493051831"/></g>
  <g id="54">
   <use class="kv10" height="18" transform="rotate(0,1065.16,1005.61) scale(-0.903738,1.04776) translate(-2244.26,-45.4099)" width="10" x="1060.643193208533" xlink:href="#Accessory:熔断器_0" y="996.1772039653642" zvalue="645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401796101" ObjectName="10kV2号站用变0718"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1065.16,1005.61) scale(-0.903738,1.04776) translate(-2244.26,-45.4099)" width="10" x="1060.643193208533" y="996.1772039653642"/></g>
  <g id="142">
   <use class="kv10" height="42" transform="rotate(0,287.497,928.889) scale(-1.25,-1.25) translate(-513.744,-1666.75)" width="30" x="268.7465097282445" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="902.6388888888889" zvalue="817"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452281696259" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,287.497,928.889) scale(-1.25,-1.25) translate(-513.744,-1666.75)" width="30" x="268.7465097282445" y="902.6388888888889"/></g>
  <g id="395">
   <use class="kv10" height="18" transform="rotate(0,294.933,886.071) scale(-0.903738,1.04776) translate(-621.763,-39.9609)" width="10" x="290.4148072615463" xlink:href="#Accessory:熔断器_0" y="876.6411006817439" zvalue="820"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452281630723" ObjectName="10kVⅠ母电压互感器熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,294.933,886.071) scale(-0.903738,1.04776) translate(-621.763,-39.9609)" width="10" x="290.4148072615463" y="876.6411006817439"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="239">
   <use class="kv10" height="30" transform="rotate(180,984.698,1060.01) scale(1.25,1.23333) translate(-195.44,-197.042)" width="12" x="977.198178958236" xlink:href="#EnergyConsumer:负荷_0" y="1041.505828857422" zvalue="131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399043590" ObjectName="10kV鼠街线"/>
   <cge:TPSR_Ref TObjectID="6192450399043590"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,984.698,1060.01) scale(1.25,1.23333) translate(-195.44,-197.042)" width="12" x="977.198178958236" y="1041.505828857422"/></g>
  <g id="315">
   <use class="kv10" height="30" transform="rotate(180,1222.16,1060.01) scale(1.25,1.23333) translate(-242.932,-197.042)" width="12" x="1214.659323518793" xlink:href="#EnergyConsumer:负荷_0" y="1041.505829413176" zvalue="364"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400026629" ObjectName="10kV备用间隔4"/>
   <cge:TPSR_Ref TObjectID="6192450400026629"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1222.16,1060.01) scale(1.25,1.23333) translate(-242.932,-197.042)" width="12" x="1214.659323518793" y="1041.505829413176"/></g>
  <g id="329">
   <use class="kv10" height="30" transform="rotate(180,1466.16,1060.01) scale(1.25,1.23333) translate(-291.732,-197.042)" width="12" x="1458.659323518793" xlink:href="#EnergyConsumer:负荷_0" y="1041.505827505828" zvalue="379"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450400288773" ObjectName="10kV备用间隔3"/>
   <cge:TPSR_Ref TObjectID="6192450400288773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1466.16,1060.01) scale(1.25,1.23333) translate(-291.732,-197.042)" width="12" x="1458.659323518793" y="1041.505827505828"/></g>
  <g id="144">
   <use class="kv10" height="30" transform="rotate(0,1064.99,1050.67) scale(1.73426,1.73426) translate(-440.62,-433.826)" width="28" x="1040.707861986769" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="1024.661111111111" zvalue="598"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401861637" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1064.99,1050.67) scale(1.73426,1.73426) translate(-440.62,-433.826)" width="28" x="1040.707861986769" y="1024.661111111111"/></g>
  <g id="35">
   <use class="kv10" height="30" transform="rotate(180,1706.36,1060.01) scale(1.25,1.23333) translate(-339.772,-197.042)" width="12" x="1698.861612102538" xlink:href="#EnergyConsumer:负荷_0" y="1041.505827505828" zvalue="613"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401468421" ObjectName="10kV青华线"/>
   <cge:TPSR_Ref TObjectID="6192450401468421"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1706.36,1060.01) scale(1.25,1.23333) translate(-339.772,-197.042)" width="12" x="1698.861612102538" y="1041.505827505828"/></g>
  <g id="224">
   <use class="kv35" height="30" transform="rotate(0,1469.63,460.723) scale(2.03436,2.03436) translate(-732.746,-218.737)" width="28" x="1441.150064176264" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="430.2079570251801" zvalue="837"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450402058245" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1469.63,460.723) scale(2.03436,2.03436) translate(-732.746,-218.737)" width="28" x="1441.150064176264" y="430.2079570251801"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="120">
   <g id="1200">
    <use class="kv35" height="30" transform="rotate(0,1221.27,554.758) scale(3.09833,2.96282) translate(-801.921,-338.076)" width="24" x="1184.09" xlink:href="#PowerTransformer2:可调两卷变_0" y="510.32" zvalue="544"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874445553667" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1201">
    <use class="kv10" height="30" transform="rotate(0,1221.27,554.758) scale(3.09833,2.96282) translate(-801.921,-338.076)" width="24" x="1184.09" xlink:href="#PowerTransformer2:可调两卷变_1" y="510.32" zvalue="544"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874445619203" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399453376515" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399453376515"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1221.27,554.758) scale(3.09833,2.96282) translate(-801.921,-338.076)" width="24" x="1184.09" y="510.32"/></g>
  <g id="29">
   <g id="290">
    <use class="kv35" height="30" transform="rotate(0,753.271,558.758) scale(3.09833,2.96282) translate(-484.97,-340.725)" width="24" x="716.09" xlink:href="#PowerTransformer2:可调两卷变_0" y="514.3200000000001" zvalue="959"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874445422595" ObjectName="35"/>
    </metadata>
   </g>
   <g id="291">
    <use class="kv10" height="30" transform="rotate(0,753.271,558.758) scale(3.09833,2.96282) translate(-484.97,-340.725)" width="24" x="716.09" xlink:href="#PowerTransformer2:可调两卷变_1" y="514.3200000000001" zvalue="959"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874445488131" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399453310979" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399453310979"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,753.271,558.758) scale(3.09833,2.96282) translate(-484.97,-340.725)" width="24" x="716.09" y="514.3200000000001"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="805">
   <use class="kv10" height="50" transform="rotate(0,749.83,1010.94) scale(1.19313,1.19313) translate(-118.961,-158.813)" width="25" x="734.9153910024356" xlink:href="#Compensator:电容20200722_0" y="981.1092719301421" zvalue="859"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450401927173" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192450401927173"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,749.83,1010.94) scale(1.19313,1.19313) translate(-118.961,-158.813)" width="25" x="734.9153910024356" y="981.1092719301421"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="286">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="286" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,467.027,245.917) scale(1,1) translate(0,0)" writing-mode="lr" x="467.03" xml:space="preserve" y="252.19" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126801571844" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="289">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="289" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,250.722,695.395) scale(1,1) translate(1.66484e-13,0)" writing-mode="lr" x="250.72" xml:space="preserve" y="701.67" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126803537927" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="290">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="290" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,467.027,263.917) scale(1,1) translate(0,0)" writing-mode="lr" x="467.03" xml:space="preserve" y="270.19" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126801637380" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="291">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="291" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,250.722,713.395) scale(1,1) translate(1.66484e-13,0)" writing-mode="lr" x="250.72" xml:space="preserve" y="719.67" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126803603460" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="292">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="292" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,467.027,281.917) scale(1,1) translate(0,0)" writing-mode="lr" x="467.03" xml:space="preserve" y="288.19" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126801702916" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="293">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="293" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,250.722,731.395) scale(1,1) translate(1.66484e-13,0)" writing-mode="lr" x="250.72" xml:space="preserve" y="737.67" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126803668996" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="295">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="295" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,467.027,229.25) scale(1,1) translate(-3.82385e-13,0)" writing-mode="lr" x="467.03" xml:space="preserve" y="235.52" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126801833988" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="296" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,250.722,677.395) scale(1,1) translate(1.66484e-13,0)" writing-mode="lr" x="250.72" xml:space="preserve" y="683.67" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126803800071" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="297">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="297" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,467.027,298.583) scale(1,1) translate(0,0)" writing-mode="lr" x="467.03" xml:space="preserve" y="304.85" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126802030596" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="303">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="303" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,250.722,748.062) scale(1,1) translate(1.66484e-13,0)" writing-mode="lr" x="250.72" xml:space="preserve" y="754.33" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126803996676" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="334">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="334" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,998.194,17.9921) scale(1,1) translate(0,0)" writing-mode="lr" x="998.1900000000001" xml:space="preserve" y="24.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126797967364" ObjectName="P"/>
   </metadata>
  </g>
  <g id="335">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="335" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,998.194,35.9921) scale(1,1) translate(0,0)" writing-mode="lr" x="998.1900000000001" xml:space="preserve" y="42.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126798032900" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="337">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="337" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,998.194,53.9921) scale(1,1) translate(0,0)" writing-mode="lr" x="998.1900000000001" xml:space="preserve" y="60.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126798098436" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="338">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="338" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.194,72.2183) scale(1,1) translate(0,0)" writing-mode="lr" x="998.1900000000001" xml:space="preserve" y="78.48999999999999" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126798360580" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="340">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="340" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1131.27,419.601) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.27" xml:space="preserve" y="425.87" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126812581892" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="342">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="342" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1131.27,436.201) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.27" xml:space="preserve" y="442.47" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126812647428" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="344">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="344" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1131.27,657.2) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.27" xml:space="preserve" y="663.47" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126812712964" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="346">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="346" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1131.27,674.2) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.27" xml:space="preserve" y="680.47" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126812778500" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="348">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="348" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1131.27,453.601) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.27" xml:space="preserve" y="459.87" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126812844036" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="351">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="351" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140.09,533.258) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.09" xml:space="preserve" y="538.03" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126813040644" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="353">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="353" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140.09,566.258) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.09" xml:space="preserve" y="571.03" zvalue="1">档位:ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126813106180" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="355">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="355" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1131.27,691.2) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.27" xml:space="preserve" y="697.47" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126813171716" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="360">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="360" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,982.198,1109.51) scale(1,1) translate(0,0)" writing-mode="lr" x="982.2" xml:space="preserve" y="1115.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126802096132" ObjectName="P"/>
   </metadata>
  </g>
  <g id="361">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="361" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1222.16,1109.51) scale(1,1) translate(5.26539e-13,-3.42261e-12)" writing-mode="lr" x="1222.16" xml:space="preserve" y="1115.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126804586500" ObjectName="P"/>
   </metadata>
  </g>
  <g id="362">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="362" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1466.16,1109.51) scale(1,1) translate(1.11107e-12,0)" writing-mode="lr" x="1466.16" xml:space="preserve" y="1115.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126806028292" ObjectName="P"/>
   </metadata>
  </g>
  <g id="363">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="363" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1706.36,1109.51) scale(1,1) translate(1.29774e-12,0)" writing-mode="lr" x="1706.36" xml:space="preserve" y="1115.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126809567236" ObjectName="P"/>
   </metadata>
  </g>
  <g id="364">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="364" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,982.198,1126.51) scale(1,1) translate(0,0)" writing-mode="lr" x="982.2" xml:space="preserve" y="1132.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126802161668" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="365">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="365" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1222.16,1126.51) scale(1,1) translate(5.26539e-13,0)" writing-mode="lr" x="1222.16" xml:space="preserve" y="1132.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126804652036" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="366">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="366" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1466.16,1126.51) scale(1,1) translate(1.11107e-12,0)" writing-mode="lr" x="1466.16" xml:space="preserve" y="1132.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126806093828" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="367">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="367" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1706.36,1126.51) scale(1,1) translate(1.29774e-12,0)" writing-mode="lr" x="1706.36" xml:space="preserve" y="1132.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126809632772" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="368">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="368" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,982.198,1143.51) scale(1,1) translate(0,0)" writing-mode="lr" x="982.2" xml:space="preserve" y="1149.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126802227204" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="369">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="369" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1222.16,1143.51) scale(1,1) translate(5.26539e-13,0)" writing-mode="lr" x="1222.16" xml:space="preserve" y="1149.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126804717572" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="370">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="370" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1466.16,1143.51) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.16" xml:space="preserve" y="1149.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126806159364" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="371">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="371" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1706.36,1143.51) scale(1,1) translate(1.29774e-12,0)" writing-mode="lr" x="1706.36" xml:space="preserve" y="1149.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126809698308" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="372">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="372" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,982.198,1158.51) scale(1,1) translate(0,0)" writing-mode="lr" x="982.2" xml:space="preserve" y="1164.78" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126802620420" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="373">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="373" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1222.16,1158.51) scale(1,1) translate(5.26539e-13,0)" writing-mode="lr" x="1222.16" xml:space="preserve" y="1164.78" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126805110788" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="374">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="374" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1466.16,1158.51) scale(1,1) translate(1.11107e-12,0)" writing-mode="lr" x="1466.16" xml:space="preserve" y="1164.78" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126806552580" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="375">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="375" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1706.36,1158.51) scale(1,1) translate(1.29774e-12,0)" writing-mode="lr" x="1706.36" xml:space="preserve" y="1164.78" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126810091524" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="376">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="376" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,749.83,1110.77) scale(1,1) translate(0,0)" writing-mode="lr" x="749.83" xml:space="preserve" y="1117.04" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126811598852" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="377">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="377" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,749.83,1127.77) scale(1,1) translate(0,0)" writing-mode="lr" x="749.83" xml:space="preserve" y="1134.04" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126811664388" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1555.15,415.513) scale(1,1) translate(-1.01162e-12,-7.23e-13)" writing-mode="lr" x="1555.15" xml:space="preserve" y="421.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126811992068" ObjectName="P"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="5" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1555.15,431.748) scale(1,1) translate(1.18023e-12,0)" writing-mode="lr" x="1555.15" xml:space="preserve" y="438.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126812057604" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1555.15,448.916) scale(1,1) translate(-1.01162e-12,0)" writing-mode="lr" x="1555.15" xml:space="preserve" y="455.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126812123140" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1555.15,483.667) scale(1,1) translate(1.18023e-12,0)" writing-mode="lr" x="1555.15" xml:space="preserve" y="489.94" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126812319748" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1555.15,467.004) scale(1,1) translate(1.18023e-12,0)" writing-mode="lr" x="1555.15" xml:space="preserve" y="473.27" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126812385284" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="42" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,813.591,415.815) scale(1,1) translate(0,0)" writing-mode="lr" x="813.59" xml:space="preserve" y="422.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126799540228" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="43" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,813.591,435.815) scale(1,1) translate(0,0)" writing-mode="lr" x="813.59" xml:space="preserve" y="442.09" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126799605764" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="46" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,808.951,657.7) scale(1,1) translate(0,0)" writing-mode="lr" x="808.95" xml:space="preserve" y="663.97" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126799671300" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="48" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,808.951,677.7) scale(1,1) translate(0,0)" writing-mode="lr" x="808.95" xml:space="preserve" y="683.97" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126799736836" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="50" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,813.591,455.815) scale(1,1) translate(0,0)" writing-mode="lr" x="813.59" xml:space="preserve" y="462.09" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126799802372" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="55" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.591,534.758) scale(1,1) translate(-2.81149e-13,0)" writing-mode="lr" x="669.59" xml:space="preserve" y="539.53" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126799998980" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="56" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.591,567.758) scale(1,1) translate(2.81149e-13,0)" writing-mode="lr" x="669.59" xml:space="preserve" y="572.53" zvalue="1">档位:ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126800064516" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="62" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,808.951,697.7) scale(1,1) translate(0,0)" writing-mode="lr" x="808.95" xml:space="preserve" y="703.97" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126800130052" ObjectName="LIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="1166">
   <use height="30" stroke="rgb(255,255,255)" transform="rotate(0,75.9702,198.429) scale(0.958333,0.916667) translate(2.67805,16.789)" width="30" x="61.6" xlink:href="#State:红绿圆_0" y="184.68" zvalue="937"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549588131842" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,75.9702,198.429) scale(0.958333,0.916667) translate(2.67805,16.789)" width="30" x="61.6" y="184.68"/></g>
  <g id="1165">
   <use height="30" transform="rotate(0,137.399,198.429) scale(0.958333,0.916667) translate(5.34886,16.789)" width="30" x="123.02" xlink:href="#State:红绿圆_0" y="184.68" zvalue="938"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549588131842" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,137.399,198.429) scale(0.958333,0.916667) translate(5.34886,16.789)" width="30" x="123.02" y="184.68"/></g>
  <g id="1164">
   <use height="30" transform="rotate(0,201.399,198.429) scale(0.958333,0.916667) translate(8.13147,16.789)" width="30" x="187.02" xlink:href="#State:红绿圆_0" y="184.68" zvalue="939"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549588131842" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,201.399,198.429) scale(0.958333,0.916667) translate(8.13147,16.789)" width="30" x="187.02" y="184.68"/></g>
  <g id="1159">
   <use height="30" transform="rotate(0,90.8988,267.179) scale(0.958333,0.916667) translate(3.32712,23.039)" width="30" x="76.52" xlink:href="#State:红绿圆_0" y="253.43" zvalue="942"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549588131842" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,90.8988,267.179) scale(0.958333,0.916667) translate(3.32712,23.039)" width="30" x="76.52" y="253.43"/></g>
  <g id="1157">
   <use height="30" transform="rotate(0,178.399,267.179) scale(0.958333,0.916667) translate(7.13147,23.039)" width="30" x="164.02" xlink:href="#State:红绿圆_0" y="253.43" zvalue="944"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549588131842" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,178.399,267.179) scale(0.958333,0.916667) translate(7.13147,23.039)" width="30" x="164.02" y="253.43"/></g>
 </g>
</svg>