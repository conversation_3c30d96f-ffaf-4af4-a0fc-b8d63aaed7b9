<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586624514" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(85,255,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:5卷PT带壁雷器_0" viewBox="0,0,30,35">
   <use terminal-index="0" type="0" x="25.27572331551165" xlink:href="#terminal" y="34.09945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.25" x2="25.0990682626599" y1="27.2315496546656" y2="27.2315496546656"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="25.16666666666666" y1="34.02740325661302" y2="14.61073658994635"/>
   <path d="M 10.5 13.9441 L 4.5 13.9441 L 4.5 6.94407" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25.25,14.36) scale(1,-1) translate(0,-1074.43)" width="7" x="21.75" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="22.19406992327969" y2="18.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="26.16666666666666" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="18.19406992327969" y2="19.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="24.16666666666666" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="22.19406992327969" y2="21.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.17943360505483" x2="25.17943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.98" cy="27.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.39" cy="13.83" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.20921744067709" x2="22.93240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.37588410734375" x2="23.76573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.79255077401042" x2="24.34906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="24.98154965466559" y2="27.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="27.45662962336982" y2="28.69416960772192"/>
   <ellipse cx="13.98" cy="20.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.98" cy="27.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94906826265991" x2="6.94906826265991" y1="24.98154965466559" y2="27.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659907" x2="4.549068262659912" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="20.45662962336982" y2="21.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="20.45662962336982" y2="21.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659912" x2="9.349068262659905" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="17.9815496546656" y2="20.4566296233698"/>
   <ellipse cx="6.98" cy="20.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="12.88240159599324" y1="13.92329629003649" y2="15.16083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="8.082401595993243" y1="13.92329629003649" y2="15.16083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="10.48240159599324" y1="11.44821632133226" y2="13.92329629003646"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.542550774010419" x2="2.265734929326571" y1="6.8946117330996" y2="6.8946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.709217440677083" x2="3.099068262659904" y1="5.644611733099605" y2="5.644611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.125884107343751" x2="3.682401595993237" y1="4.394611733099605" y2="4.394611733099605"/>
  </symbol>
  <symbol id="Accessory:站用变20210923_0" viewBox="0,0,23,35">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.05" x2="21.05" y1="24.5" y2="24.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="20.5" y1="17.5" y2="17.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="21.5" y1="23.5" y2="23.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="20.5" y1="17.5" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="16.5" y1="17.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="7.5" y1="25.5" y2="29.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.2" x2="7.2" y1="24.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.5" x2="22.5" y1="22.5" y2="22.5"/>
   <path d="M 6.25 30.25 L 8.25 30.25 L 7.25 32.25 L 6.25 30.25 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="7.07" cy="6.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.525" x2="9.425000000000001" y1="5.525" y2="5.525"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.988598941527138" x2="4.525000000000003" y1="8.779108825446739" y2="5.575000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.051503962584212" x2="9.475" y1="8.754108825446737" y2="5.550000000000002"/>
   <ellipse cx="7.07" cy="17.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.061718118722352" x2="7.061718118722352" y1="14.66446595874622" y2="17.26345405876316"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.655265608193801" x2="7.061718118722348" y1="19.86244215878007" y2="17.26345405876314"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.468170629250878" x2="7.061718118722331" y1="19.86244215878007" y2="17.26345405876314"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV锰矿变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass">
  
  
  
  
  
 </g>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,144.893,57.375) scale(1,1) translate(0,0)" writing-mode="lr" x="144.89" xml:space="preserve" y="74.37" zvalue="3194">     锰矿变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,567.738,374.65) scale(1,1) translate(0,0)" writing-mode="lr" x="567.74" xml:space="preserve" y="380.65" zvalue="1104">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="610" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987.47,565.996) scale(1,1) translate(0,0)" writing-mode="lr" x="987.47" xml:space="preserve" y="572" zvalue="1167">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,879.109,434.26) scale(1,1) translate(0,0)" writing-mode="lr" x="879.11" xml:space="preserve" y="438.76" zvalue="1172">3821</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,882.54,482.256) scale(1,1) translate(0,0)" writing-mode="lr" x="882.54" xml:space="preserve" y="486.76" zvalue="1175">382</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,727.237,439.273) scale(1,1) translate(0,0)" writing-mode="lr" x="727.24" xml:space="preserve" y="443.77" zvalue="1207">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.026,196.18) scale(1,1) translate(0,0)" writing-mode="lr" x="910.03" xml:space="preserve" y="202.18" zvalue="1450">35kV鹤金锰线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,879.156,357.017) scale(1,1) translate(0,0)" writing-mode="lr" x="879.16" xml:space="preserve" y="361.52" zvalue="1452">3811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.398,346.647) scale(1,1) translate(0,0)" writing-mode="lr" x="962.4" xml:space="preserve" y="351.15" zvalue="1922">38110</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,743.225,537.642) scale(1,1) translate(0,0)" writing-mode="lr" x="743.22" xml:space="preserve" y="543.64" zvalue="2407">35kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,491,792.073) scale(1,1) translate(0,0)" writing-mode="lr" x="491" xml:space="preserve" y="798.0700000000001" zvalue="2409">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,885.987,678.558) scale(1,1) translate(0,0)" writing-mode="lr" x="885.99" xml:space="preserve" y="683.0599999999999" zvalue="2415">0016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.791,728.816) scale(1,1) translate(0,0)" writing-mode="lr" x="887.79" xml:space="preserve" y="733.3200000000001" zvalue="2417">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="312" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,397.729,861.709) scale(1,1) translate(0,0)" writing-mode="lr" x="397.73" xml:space="preserve" y="866.21" zvalue="2739">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="615" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,380.008,1017.54) scale(1,1) translate(0,0)" writing-mode="lr" x="380.01" xml:space="preserve" y="1023.54" zvalue="2747">10kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="650" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,530.28,891.986) scale(1,1) translate(0,0)" writing-mode="lr" x="530.28" xml:space="preserve" y="896.49" zvalue="2781">080</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="647" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,499.795,1017.54) scale(1,1) translate(0,0)" writing-mode="lr" x="499.79" xml:space="preserve" y="1023.54" zvalue="2792">10kV避雷器柜</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="400" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987.47,592.208) scale(1,1) translate(0,0)" writing-mode="lr" x="987.47" xml:space="preserve" y="598.21" zvalue="3408">1600kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="484" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.2321,282.179) scale(1,1) translate(0,0)" writing-mode="lr" x="93.23" xml:space="preserve" y="288.18" zvalue="3483">负荷总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="483" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89.4821,317.929) scale(1,1) translate(0,0)" writing-mode="lr" x="89.48" xml:space="preserve" y="323.93" zvalue="3484">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="482" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.2321,352.929) scale(1,1) translate(0,0)" writing-mode="lr" x="93.23" xml:space="preserve" y="358.93" zvalue="3485">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="481" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.1488,168.429) scale(1,1) translate(0,0)" writing-mode="lr" x="87.15000000000001" xml:space="preserve" y="174.43" zvalue="3489">事故</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="480" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98.1488,235.179) scale(1,1) translate(0,0)" writing-mode="lr" x="98.15000000000001" xml:space="preserve" y="241.18" zvalue="3490">是否失压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="479" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,179.649,235.179) scale(1,1) translate(0,0)" writing-mode="lr" x="179.65" xml:space="preserve" y="241.18" zvalue="3492">失压排除</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="478" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.149,168.429) scale(1,1) translate(0,0)" writing-mode="lr" x="147.15" xml:space="preserve" y="174.43" zvalue="3494">异常</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="477" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,207.149,168.429) scale(1,1) translate(0,0)" writing-mode="lr" x="207.15" xml:space="preserve" y="174.43" zvalue="3495">告知</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="476" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.435,318.5) scale(1,1) translate(0,0)" writing-mode="lr" x="208.43" xml:space="preserve" y="324.5" zvalue="3496">锰矿变-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="475" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.435,353.5) scale(1,1) translate(0,0)" writing-mode="lr" x="204.43" xml:space="preserve" y="359.5" zvalue="3497">2023-02-10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,881.198,775.272) scale(1,1) translate(0,0)" writing-mode="lr" x="881.2" xml:space="preserve" y="779.77" zvalue="3515">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,518.67,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="518.67" xml:space="preserve" y="850.16" zvalue="3524">0801</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,622.67,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="622.67" xml:space="preserve" y="850.16" zvalue="3534">0903</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.073,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="603.0700000000001" xml:space="preserve" y="1022.54" zvalue="3540">10kV站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.228,891.989) scale(1,1) translate(0,0)" writing-mode="lr" x="733.23" xml:space="preserve" y="896.49" zvalue="3547">081</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.657,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="723.66" xml:space="preserve" y="850.16" zvalue="3548">0811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.657,938.22) scale(1,1) translate(0,0)" writing-mode="lr" x="723.66" xml:space="preserve" y="942.72" zvalue="3550">0816</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,701.5,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="701.5" xml:space="preserve" y="1022.54" zvalue="3555">10kV备用1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,846.28,891.986) scale(1,1) translate(0,0)" writing-mode="lr" x="846.28" xml:space="preserve" y="896.49" zvalue="3558">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,815.795,1017.54) scale(1,1) translate(0,0)" writing-mode="lr" x="815.79" xml:space="preserve" y="1023.54" zvalue="3560">10kV电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,834.67,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="834.67" xml:space="preserve" y="850.16" zvalue="3563">0021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.228,891.989) scale(1,1) translate(0,0)" writing-mode="lr" x="945.23" xml:space="preserve" y="896.49" zvalue="3568">082</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.657,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="935.66" xml:space="preserve" y="850.16" zvalue="3570">0821</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.657,938.22) scale(1,1) translate(0,0)" writing-mode="lr" x="935.66" xml:space="preserve" y="942.72" zvalue="3573">0826</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.5,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="913.5" xml:space="preserve" y="1022.54" zvalue="3577">10kV母联（备用）</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1041.23,891.989) scale(1,1) translate(0,0)" writing-mode="lr" x="1041.23" xml:space="preserve" y="896.49" zvalue="3581">083</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031.66,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.66" xml:space="preserve" y="850.16" zvalue="3583">0831</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031.66,938.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.66" xml:space="preserve" y="942.72" zvalue="3586">0836</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.5,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.5" xml:space="preserve" y="1022.54" zvalue="3590">10kV备用2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1133.23,891.989) scale(1,1) translate(0,0)" writing-mode="lr" x="1133.23" xml:space="preserve" y="896.49" zvalue="3594">084</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1123.66,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1123.66" xml:space="preserve" y="850.16" zvalue="3596">0841</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1123.66,938.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1123.66" xml:space="preserve" y="942.72" zvalue="3599">0846</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1101.5,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.5" xml:space="preserve" y="1022.54" zvalue="3603">10kV冶炼线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1229.23,891.989) scale(1,1) translate(0,0)" writing-mode="lr" x="1229.23" xml:space="preserve" y="896.49" zvalue="3607">085</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1219.66,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1219.66" xml:space="preserve" y="850.16" zvalue="3609">0851</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1219.66,938.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1219.66" xml:space="preserve" y="942.72" zvalue="3612">0856</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1197.5,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="1197.5" xml:space="preserve" y="1022.54" zvalue="3616">10kV矿山线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1333.23,891.989) scale(1,1) translate(0,0)" writing-mode="lr" x="1333.23" xml:space="preserve" y="896.49" zvalue="3620">086</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1323.66,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.66" xml:space="preserve" y="850.16" zvalue="3622">0861</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1323.66,938.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.66" xml:space="preserve" y="942.72" zvalue="3625">0866</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1301.5,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="1301.5" xml:space="preserve" y="1022.54" zvalue="3629">10kV水泥厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1421.23,891.989) scale(1,1) translate(0,0)" writing-mode="lr" x="1421.23" xml:space="preserve" y="896.49" zvalue="3633">087</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1411.66,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1411.66" xml:space="preserve" y="850.16" zvalue="3635">0871</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1411.66,938.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1411.66" xml:space="preserve" y="942.72" zvalue="3638">0876</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1389.5,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="1389.5" xml:space="preserve" y="1022.54" zvalue="3642">10kV备用３</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1505.23,891.989) scale(1,1) translate(0,0)" writing-mode="lr" x="1505.23" xml:space="preserve" y="896.49" zvalue="3646">088</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.66,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.66" xml:space="preserve" y="850.16" zvalue="3648">0881</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.66,938.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.66" xml:space="preserve" y="942.72" zvalue="3651">0886</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.5,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.5" xml:space="preserve" y="1022.54" zvalue="3655">10kV选厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1589.23,891.989) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.23" xml:space="preserve" y="896.49" zvalue="3659">089</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1579.66,845.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1579.66" xml:space="preserve" y="850.16" zvalue="3661">0891</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1579.66,938.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1579.66" xml:space="preserve" y="942.72" zvalue="3664">0896</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1557.5,1016.54) scale(1,1) translate(0,0)" writing-mode="lr" x="1557.5" xml:space="preserve" y="1022.54" zvalue="3668">10kV生活线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1153.54,479.904) scale(1,1) translate(0,0)" writing-mode="lr" x="1153.54" xml:space="preserve" y="484.4" zvalue="3672">383</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.5,657.076) scale(1,1) translate(0,0)" writing-mode="lr" x="1127.5" xml:space="preserve" y="663.08" zvalue="3674">35kV冶炼炉</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1146.38,433.582) scale(1,1) translate(0,0)" writing-mode="lr" x="1146.38" xml:space="preserve" y="438.08" zvalue="3677">3831</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.65,563.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.65" xml:space="preserve" y="568.09" zvalue="3682">384</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="5">
   <path class="kv35" d="M 581 392.26 L 1181 392.26" stroke-width="6" zvalue="1103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674249539588" ObjectName="35kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674249539588"/></metadata>
  <path d="M 581 392.26 L 1181 392.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="532">
   <path class="kv10" d="M 331.43 808.33 L 1620 808.33" stroke-width="6" zvalue="2408"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674249670660" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674249670660"/></metadata>
  <path d="M 331.43 808.33 L 1620 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="196">
   <g id="1960">
    <use class="kv35" height="30" transform="rotate(0,913.689,580.745) scale(3.09833,2.96282) translate(-593.612,-355.292)" width="24" x="876.51" xlink:href="#PowerTransformer2:可调两卷变_0" y="536.3" zvalue="1166"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439393284" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1961">
    <use class="kv10" height="30" transform="rotate(0,913.689,580.745) scale(3.09833,2.96282) translate(-593.612,-355.292)" width="24" x="876.51" xlink:href="#PowerTransformer2:可调两卷变_1" y="536.3" zvalue="1166"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439458820" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450624004" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450624004"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,913.689,580.745) scale(3.09833,2.96282) translate(-593.612,-355.292)" width="24" x="876.51" y="536.3"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="215">
   <use class="kv35" height="30" transform="rotate(0,913.608,435.238) scale(-1.11133,0.814667) translate(-1734.86,96.235)" width="15" x="905.2725954552799" xlink:href="#Disconnector:刀闸_0" y="423.0184682210286" zvalue="1171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183692293" ObjectName="#1主变35kV侧3821"/>
   <cge:TPSR_Ref TObjectID="6192450183692293"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,913.608,435.238) scale(-1.11133,0.814667) translate(-1734.86,96.235)" width="15" x="905.2725954552799" y="423.0184682210286"/></g>
  <g id="243">
   <use class="kv35" height="30" transform="rotate(0,757.972,443.118) scale(-1.11133,0.814667) translate(-1439.17,98.0276)" width="15" x="749.6366698343221" xlink:href="#Disconnector:刀闸_0" y="430.8983026208138" zvalue="1206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183757829" ObjectName="35kVⅠ母电压互感器0901"/>
   <cge:TPSR_Ref TObjectID="6192450183757829"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,757.972,443.118) scale(-1.11133,0.814667) translate(-1439.17,98.0276)" width="15" x="749.6366698343221" y="430.8983026208138"/></g>
  <g id="55">
   <use class="kv35" height="30" transform="rotate(0,912.365,357.343) scale(-1.11133,0.814667) translate(-1732.49,78.5142)" width="15" x="904.0303641813575" xlink:href="#Disconnector:刀闸_0" y="345.1233829395323" zvalue="1451"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184347653" ObjectName="35kV鹤金锰线3811"/>
   <cge:TPSR_Ref TObjectID="6192450184347653"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,912.365,357.343) scale(-1.11133,0.814667) translate(-1732.49,78.5142)" width="15" x="904.0303641813575" y="345.1233829395323"/></g>
  <g id="528">
   <use class="kv10" height="30" transform="rotate(0,913.676,679.558) scale(-1.11133,0.814667) translate(-1734.98,151.817)" width="15" x="905.3405019111636" xlink:href="#Disconnector:刀闸_0" y="667.3378612432044" zvalue="2414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185592837" ObjectName="#Ⅰ主变10kV侧0016"/>
   <cge:TPSR_Ref TObjectID="6192450185592837"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,913.676,679.558) scale(-1.11133,0.814667) translate(-1734.98,151.817)" width="15" x="905.3405019111636" y="667.3378612432044"/></g>
  <g id="612">
   <use class="kv10" height="30" transform="rotate(0,361.667,860.021) scale(-1.11133,-0.814667) translate(-686.267,-1918.47)" width="15" x="353.3316666666665" xlink:href="#Disconnector:刀闸_0" y="847.8007820843359" zvalue="2738"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186444805" ObjectName="10kVⅠ母电压互感器0901"/>
   <cge:TPSR_Ref TObjectID="6192450186444805"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.667,860.021) scale(-1.11133,-0.814667) translate(-686.267,-1918.47)" width="15" x="353.3316666666665" y="847.8007820843359"/></g>
  <g id="39">
   <use class="kv10" height="30" transform="rotate(0,914.007,775.558) scale(-1.11133,0.814667) translate(-1735.61,173.656)" width="15" x="905.6715064576624" xlink:href="#Disconnector:刀闸_0" y="763.3378612432044" zvalue="3514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#Ⅰ主变10kV侧0011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,914.007,775.558) scale(-1.11133,0.814667) translate(-1735.61,173.656)" width="15" x="905.6715064576624" y="763.3378612432044"/></g>
  <g id="51">
   <use class="kv10" height="30" transform="rotate(0,491.668,846.664) scale(-1.11133,-0.814667) translate(-933.246,-1888.72)" width="15" x="483.3333315700953" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3523"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV避雷器柜0801"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,491.668,846.664) scale(-1.11133,-0.814667) translate(-933.246,-1888.72)" width="15" x="483.3333315700953" y="834.4444427490234"/></g>
  <g id="75">
   <use class="kv10" height="30" transform="rotate(0,595.668,846.664) scale(-1.11133,-0.814667) translate(-1130.83,-1888.72)" width="15" x="587.3333315700952" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3532"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV站用变0903"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,595.668,846.664) scale(-1.11133,-0.814667) translate(-1130.83,-1888.72)" width="15" x="587.3333315700952" y="834.4444427490234"/></g>
  <g id="90">
   <use class="kv10" height="30" transform="rotate(0,700.822,846.664) scale(-1.11133,-0.814667) translate(-1330.6,-1888.72)" width="15" x="692.4872242024992" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3547"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用１　0811"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,700.822,846.664) scale(-1.11133,-0.814667) translate(-1330.6,-1888.72)" width="15" x="692.4872242024992" y="834.4444427490234"/></g>
  <g id="92">
   <use class="kv10" height="30" transform="rotate(0,701.012,939.22) scale(-1.11133,-0.814667) translate(-1330.96,-2094.89)" width="15" x="692.6772722205745" xlink:href="#Disconnector:刀闸_0" y="927" zvalue="3549"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用１　0816"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,701.012,939.22) scale(-1.11133,-0.814667) translate(-1330.96,-2094.89)" width="15" x="692.6772722205745" y="927"/></g>
  <g id="112">
   <use class="kv10" height="30" transform="rotate(0,807.668,846.664) scale(-1.11133,-0.814667) translate(-1533.59,-1888.72)" width="15" x="799.3333315700952" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3561"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV电容器0021"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,807.668,846.664) scale(-1.11133,-0.814667) translate(-1533.59,-1888.72)" width="15" x="799.3333315700952" y="834.4444427490234"/></g>
  <g id="136">
   <use class="kv10" height="30" transform="rotate(0,912.822,846.664) scale(-1.11133,-0.814667) translate(-1733.36,-1888.72)" width="15" x="904.4872242024993" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3569"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV母联（备用）　0821"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,912.822,846.664) scale(-1.11133,-0.814667) translate(-1733.36,-1888.72)" width="15" x="904.4872242024993" y="834.4444427490234"/></g>
  <g id="130">
   <use class="kv10" height="30" transform="rotate(0,913.012,939.22) scale(-1.11133,-0.814667) translate(-1733.72,-2094.89)" width="15" x="904.6772722205745" xlink:href="#Disconnector:刀闸_0" y="927" zvalue="3571"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV母联（备用）　0826"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,913.012,939.22) scale(-1.11133,-0.814667) translate(-1733.72,-2094.89)" width="15" x="904.6772722205745" y="927"/></g>
  <g id="170">
   <use class="kv10" height="30" transform="rotate(0,1008.82,846.664) scale(-1.11133,-0.814667) translate(-1915.75,-1888.72)" width="15" x="1000.487224202499" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3582"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用2　0831"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1008.82,846.664) scale(-1.11133,-0.814667) translate(-1915.75,-1888.72)" width="15" x="1000.487224202499" y="834.4444427490234"/></g>
  <g id="169">
   <use class="kv10" height="30" transform="rotate(0,1009.01,939.22) scale(-1.11133,-0.814667) translate(-1916.11,-2094.89)" width="15" x="1000.677272220575" xlink:href="#Disconnector:刀闸_0" y="927" zvalue="3584"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用2　0836"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1009.01,939.22) scale(-1.11133,-0.814667) translate(-1916.11,-2094.89)" width="15" x="1000.677272220575" y="927"/></g>
  <g id="190">
   <use class="kv10" height="30" transform="rotate(0,1100.82,846.664) scale(-1.11133,-0.814667) translate(-2090.53,-1888.72)" width="15" x="1092.487224202499" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3595"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV冶炼线　0841"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1100.82,846.664) scale(-1.11133,-0.814667) translate(-2090.53,-1888.72)" width="15" x="1092.487224202499" y="834.4444427490234"/></g>
  <g id="189">
   <use class="kv10" height="30" transform="rotate(0,1101.01,939.22) scale(-1.11133,-0.814667) translate(-2090.89,-2094.89)" width="15" x="1092.677272220575" xlink:href="#Disconnector:刀闸_0" y="927" zvalue="3597"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV冶炼线　0846"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1101.01,939.22) scale(-1.11133,-0.814667) translate(-2090.89,-2094.89)" width="15" x="1092.677272220575" y="927"/></g>
  <g id="220">
   <use class="kv10" height="30" transform="rotate(0,1196.82,846.664) scale(-1.11133,-0.814667) translate(-2272.91,-1888.72)" width="15" x="1188.487224202499" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3608"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV矿山线　0851"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1196.82,846.664) scale(-1.11133,-0.814667) translate(-2272.91,-1888.72)" width="15" x="1188.487224202499" y="834.4444427490234"/></g>
  <g id="213">
   <use class="kv10" height="30" transform="rotate(0,1197.01,939.22) scale(-1.11133,-0.814667) translate(-2273.27,-2094.89)" width="15" x="1188.677272220575" xlink:href="#Disconnector:刀闸_0" y="927" zvalue="3610"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV矿山线　0856"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1197.01,939.22) scale(-1.11133,-0.814667) translate(-2273.27,-2094.89)" width="15" x="1188.677272220575" y="927"/></g>
  <g id="237">
   <use class="kv10" height="30" transform="rotate(0,1300.82,846.664) scale(-1.11133,-0.814667) translate(-2470.49,-1888.72)" width="15" x="1292.487224202499" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3621"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV水泥厂线　0861"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1300.82,846.664) scale(-1.11133,-0.814667) translate(-2470.49,-1888.72)" width="15" x="1292.487224202499" y="834.4444427490234"/></g>
  <g id="236">
   <use class="kv10" height="30" transform="rotate(0,1301.01,939.22) scale(-1.11133,-0.814667) translate(-2470.85,-2094.89)" width="15" x="1292.677272220575" xlink:href="#Disconnector:刀闸_0" y="927" zvalue="3623"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV水泥厂线　0866"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1301.01,939.22) scale(-1.11133,-0.814667) translate(-2470.85,-2094.89)" width="15" x="1292.677272220575" y="927"/></g>
  <g id="257">
   <use class="kv10" height="30" transform="rotate(0,1388.82,846.664) scale(-1.11133,-0.814667) translate(-2637.68,-1888.72)" width="15" x="1380.487224202499" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用３　0871"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1388.82,846.664) scale(-1.11133,-0.814667) translate(-2637.68,-1888.72)" width="15" x="1380.487224202499" y="834.4444427490234"/></g>
  <g id="256">
   <use class="kv10" height="30" transform="rotate(0,1389.01,939.22) scale(-1.11133,-0.814667) translate(-2638.04,-2094.89)" width="15" x="1380.677272220575" xlink:href="#Disconnector:刀闸_0" y="927" zvalue="3636"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用３　0876"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1389.01,939.22) scale(-1.11133,-0.814667) translate(-2638.04,-2094.89)" width="15" x="1380.677272220575" y="927"/></g>
  <g id="280">
   <use class="kv10" height="30" transform="rotate(0,1472.82,846.664) scale(-1.11133,-0.814667) translate(-2797.26,-1888.72)" width="15" x="1464.487224202499" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3647"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV选厂线　0881"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1472.82,846.664) scale(-1.11133,-0.814667) translate(-2797.26,-1888.72)" width="15" x="1464.487224202499" y="834.4444427490234"/></g>
  <g id="279">
   <use class="kv10" height="30" transform="rotate(0,1473.01,939.22) scale(-1.11133,-0.814667) translate(-2797.62,-2094.89)" width="15" x="1464.677272220575" xlink:href="#Disconnector:刀闸_0" y="927" zvalue="3649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV选厂线　0886"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1473.01,939.22) scale(-1.11133,-0.814667) translate(-2797.62,-2094.89)" width="15" x="1464.677272220575" y="927"/></g>
  <g id="292">
   <use class="kv10" height="30" transform="rotate(0,1556.82,846.664) scale(-1.11133,-0.814667) translate(-2956.85,-1888.72)" width="15" x="1548.487224202499" xlink:href="#Disconnector:刀闸_0" y="834.4444427490234" zvalue="3660"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV生活线　0891"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1556.82,846.664) scale(-1.11133,-0.814667) translate(-2956.85,-1888.72)" width="15" x="1548.487224202499" y="834.4444427490234"/></g>
  <g id="291">
   <use class="kv10" height="30" transform="rotate(0,1557.01,939.22) scale(-1.11133,-0.814667) translate(-2957.21,-2094.89)" width="15" x="1548.677272220575" xlink:href="#Disconnector:刀闸_0" y="927" zvalue="3662"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV生活线　0896"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1557.01,939.22) scale(-1.11133,-0.814667) translate(-2957.21,-2094.89)" width="15" x="1548.677272220575" y="927"/></g>
  <g id="300">
   <use class="kv35" height="30" transform="rotate(0,1119.37,434.582) scale(-1.11133,-0.814667) translate(-2125.77,-970.81)" width="15" x="1111.038350782369" xlink:href="#Disconnector:刀闸_0" y="422.3623227678781" zvalue="3675"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV冶炼炉3831"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1119.37,434.582) scale(-1.11133,-0.814667) translate(-2125.77,-970.81)" width="15" x="1111.038350782369" y="422.3623227678781"/></g>
 </g>
 <g id="BreakerClass">
  <g id="217">
   <use class="kv35" height="20" transform="rotate(0,913.601,484.111) scale(1.83111,1.88671) translate(-410.513,-218.654)" width="10" x="904.4451010492124" xlink:href="#Breaker:开关_0" y="465.2442677815755" zvalue="1174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924550852612" ObjectName="#1主变35kV侧382"/>
   <cge:TPSR_Ref TObjectID="6473924550852612"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,913.601,484.111) scale(1.83111,1.88671) translate(-410.513,-218.654)" width="10" x="904.4451010492124" y="465.2442677815755"/></g>
  <g id="523">
   <use class="kv10" height="20" transform="rotate(0,913.787,729.479) scale(1.83111,1.88671) translate(-410.597,-333.971)" width="10" x="904.6313254045542" xlink:href="#Breaker:开关_0" y="710.6123174031576" zvalue="2416"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551114756" ObjectName="#Ⅰ主变10kV侧001"/>
   <cge:TPSR_Ref TObjectID="6473924551114756"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,913.787,729.479) scale(1.83111,1.88671) translate(-410.597,-333.971)" width="10" x="904.6313254045542" y="710.6123174031576"/></g>
  <g id="666">
   <use class="kv10" height="20" transform="rotate(0,491.668,891.439) scale(1.83111,1.88671) translate(-219.005,-410.088)" width="10" x="482.5127740342627" xlink:href="#Breaker:开关_0" y="872.5721435546875" zvalue="2780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551311364" ObjectName="10kV避雷器柜080"/>
   <cge:TPSR_Ref TObjectID="6473924551311364"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,491.668,891.439) scale(1.83111,1.88671) translate(-219.005,-410.088)" width="10" x="482.5127740342627" y="872.5721435546875"/></g>
  <g id="91">
   <use class="kv10" height="20" transform="rotate(0,700.822,891.439) scale(1.83111,1.88671) translate(-313.936,-410.088)" width="10" x="691.6666666666667" xlink:href="#Breaker:开关_0" y="872.5721434752145" zvalue="3546"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用１　081"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,700.822,891.439) scale(1.83111,1.88671) translate(-313.936,-410.088)" width="10" x="691.6666666666667" y="872.5721434752145"/></g>
  <g id="114">
   <use class="kv10" height="20" transform="rotate(0,807.668,891.439) scale(1.83111,1.88671) translate(-362.432,-410.088)" width="10" x="798.5127740342627" xlink:href="#Breaker:开关_0" y="872.5721435546875" zvalue="3557"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV电容器002"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,807.668,891.439) scale(1.83111,1.88671) translate(-362.432,-410.088)" width="10" x="798.5127740342627" y="872.5721435546875"/></g>
  <g id="137">
   <use class="kv10" height="20" transform="rotate(0,912.822,891.439) scale(1.83111,1.88671) translate(-410.159,-410.088)" width="10" x="903.6666666666666" xlink:href="#Breaker:开关_0" y="872.5721434752145" zvalue="3567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV母联（备用）　082"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,912.822,891.439) scale(1.83111,1.88671) translate(-410.159,-410.088)" width="10" x="903.6666666666666" y="872.5721434752145"/></g>
  <g id="171">
   <use class="kv10" height="20" transform="rotate(0,1008.82,891.439) scale(1.83111,1.88671) translate(-453.732,-410.088)" width="10" x="999.6666666666666" xlink:href="#Breaker:开关_0" y="872.5721434752145" zvalue="3580"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用2　083"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1008.82,891.439) scale(1.83111,1.88671) translate(-453.732,-410.088)" width="10" x="999.6666666666666" y="872.5721434752145"/></g>
  <g id="194">
   <use class="kv10" height="20" transform="rotate(0,1100.82,891.439) scale(1.83111,1.88671) translate(-495.49,-410.088)" width="10" x="1091.666666666667" xlink:href="#Breaker:开关_0" y="872.5721434752145" zvalue="3593"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV冶炼线　084"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1100.82,891.439) scale(1.83111,1.88671) translate(-495.49,-410.088)" width="10" x="1091.666666666667" y="872.5721434752145"/></g>
  <g id="221">
   <use class="kv10" height="20" transform="rotate(0,1196.82,891.439) scale(1.83111,1.88671) translate(-539.062,-410.088)" width="10" x="1187.666666666667" xlink:href="#Breaker:开关_0" y="872.5721434752145" zvalue="3606"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV矿山线　085"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1196.82,891.439) scale(1.83111,1.88671) translate(-539.062,-410.088)" width="10" x="1187.666666666667" y="872.5721434752145"/></g>
  <g id="240">
   <use class="kv10" height="20" transform="rotate(0,1300.82,891.439) scale(1.83111,1.88671) translate(-586.266,-410.088)" width="10" x="1291.666666666667" xlink:href="#Breaker:开关_0" y="872.5721434752145" zvalue="3619"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV水泥厂线　086"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1300.82,891.439) scale(1.83111,1.88671) translate(-586.266,-410.088)" width="10" x="1291.666666666667" y="872.5721434752145"/></g>
  <g id="258">
   <use class="kv10" height="20" transform="rotate(0,1388.82,891.439) scale(1.83111,1.88671) translate(-626.208,-410.088)" width="10" x="1379.666666666667" xlink:href="#Breaker:开关_0" y="872.5721434752145" zvalue="3632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用３　087"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1388.82,891.439) scale(1.83111,1.88671) translate(-626.208,-410.088)" width="10" x="1379.666666666667" y="872.5721434752145"/></g>
  <g id="281">
   <use class="kv10" height="20" transform="rotate(0,1472.82,891.439) scale(1.83111,1.88671) translate(-664.334,-410.088)" width="10" x="1463.666666666667" xlink:href="#Breaker:开关_0" y="872.5721434752145" zvalue="3645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV选厂线　088"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1472.82,891.439) scale(1.83111,1.88671) translate(-664.334,-410.088)" width="10" x="1463.666666666667" y="872.5721434752145"/></g>
  <g id="293">
   <use class="kv10" height="20" transform="rotate(0,1556.82,891.439) scale(1.83111,1.88671) translate(-702.461,-410.088)" width="10" x="1547.666666666667" xlink:href="#Breaker:开关_0" y="872.5721434752145" zvalue="3658"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV生活线　089"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1556.82,891.439) scale(1.83111,1.88671) translate(-702.461,-410.088)" width="10" x="1547.666666666667" y="872.5721434752145"/></g>
  <g id="302">
   <use class="kv35" height="20" transform="rotate(0,1119.37,479.357) scale(1.83111,1.88671) translate(-503.91,-216.419)" width="10" x="1110.217793246537" xlink:href="#Breaker:开关_0" y="460.4900235735422" zvalue="3671"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV冶炼炉383"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1119.37,479.357) scale(1.83111,1.88671) translate(-503.91,-216.419)" width="10" x="1110.217793246537" y="460.4900235735422"/></g>
  <g id="309">
   <use class="kv35" height="20" transform="rotate(0,1119.37,563.357) scale(1.83111,1.88671) translate(-503.91,-255.897)" width="10" x="1110.217793246537" xlink:href="#Breaker:开关_0" y="544.4900235735422" zvalue="3681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV冶炼炉384"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1119.37,563.357) scale(1.83111,1.88671) translate(-503.91,-255.897)" width="10" x="1110.217793246537" y="544.4900235735422"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="56">
   <use class="kv35" height="30" transform="rotate(0,912.74,228.932) scale(1.99975,1.15097) translate(-452.814,-27.7634)" width="7" x="905.7411336303085" xlink:href="#ACLineSegment:线路_0" y="211.6678493719236" zvalue="1449"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249324584965" ObjectName="35kV鹤金锰线"/>
   <cge:TPSR_Ref TObjectID="8444249324584965_5066549586624514"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,912.74,228.932) scale(1.99975,1.15097) translate(-452.814,-27.7634)" width="7" x="905.7411336303085" y="211.6678493719236"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="63">
   <use class="kv35" height="30" transform="rotate(270,960.087,331.526) scale(-1.01422,0.867474) translate(-1906.62,48.6601)" width="12" x="954.0015175563028" xlink:href="#GroundDisconnector:地刀12_0" y="318.5138884619807" zvalue="1921"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184871941" ObjectName="35kV鹤金锰线38110"/>
   <cge:TPSR_Ref TObjectID="6192450184871941"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,960.087,331.526) scale(-1.01422,0.867474) translate(-1906.62,48.6601)" width="12" x="954.0015175563028" y="318.5138884619807"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="4">
   <use class="kv35" height="42" transform="rotate(180,744.336,501.576) scale(1.25,1.22255) translate(-145.117,-86.6335)" width="30" x="725.585935593657" xlink:href="#Accessory:4卷PT带容断器_0" y="475.9028097742124" zvalue="2406"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185134085" ObjectName="35kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(180,744.336,501.576) scale(1.25,1.22255) translate(-145.117,-86.6335)" width="30" x="725.585935593657" y="475.9028097742124"/></g>
  <g id="614">
   <use class="kv10" height="35" transform="rotate(180,380.793,957.575) scale(1.87083,1.95877) translate(-164.188,-451.932)" width="30" x="352.730813885549" xlink:href="#Accessory:5卷PT带壁雷器_0" y="923.2961832682292" zvalue="2746"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186575877" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(180,380.793,957.575) scale(1.87083,1.95877) translate(-164.188,-451.932)" width="30" x="352.730813885549" y="923.2961832682292"/></g>
  <g id="79">
   <use class="kv10" height="35" transform="rotate(0,603.073,976.376) scale(1.61836,1.61836) translate(-223.317,-362.242)" width="23" x="584.4619066440389" xlink:href="#Accessory:站用变20210923_0" y="948.0544576196751" zvalue="3539"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV站用变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,603.073,976.376) scale(1.61836,1.61836) translate(-223.317,-362.242)" width="23" x="584.4619066440389" y="948.0544576196751"/></g>
  <g id="88">
   <use class="kv10" height="18" transform="rotate(0,595,900.167) scale(1.25,1.11111) translate(-117.75,-89.0167)" width="10" x="588.7500000000001" xlink:href="#Accessory:熔断器_0" y="890.1666666666667" zvalue="3543"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV站用变熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,595,900.167) scale(1.25,1.11111) translate(-117.75,-89.0167)" width="10" x="588.7500000000001" y="890.1666666666667"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="658">
   <use class="kv10" height="30" transform="rotate(180,491.668,989.792) scale(1.25,1.23333) translate(-96.8337,-183.758)" width="12" x="484.1683315676444" xlink:href="#EnergyConsumer:负荷_0" y="971.2924194335936" zvalue="2791"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450187296773" ObjectName="10kV避雷器柜"/>
   <cge:TPSR_Ref TObjectID="6192450187296773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,491.668,989.792) scale(1.25,1.23333) translate(-96.8337,-183.758)" width="12" x="484.1683315676444" y="971.2924194335936"/></g>
  <g id="101">
   <use class="kv10" height="30" transform="rotate(180,701.5,989.792) scale(1.25,1.23333) translate(-138.8,-183.758)" width="12" x="694" xlink:href="#EnergyConsumer:负荷_0" y="971.2924200693766" zvalue="3554"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,701.5,989.792) scale(1.25,1.23333) translate(-138.8,-183.758)" width="12" x="694" y="971.2924200693766"/></g>
  <g id="113">
   <use class="kv10" height="30" transform="rotate(180,807.668,989.792) scale(1.25,1.23333) translate(-160.034,-183.758)" width="12" x="800.1683315676444" xlink:href="#EnergyConsumer:负荷_0" y="971.2924194335936" zvalue="3559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV电容器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,807.668,989.792) scale(1.25,1.23333) translate(-160.034,-183.758)" width="12" x="800.1683315676444" y="971.2924194335936"/></g>
  <g id="125">
   <use class="kv10" height="30" transform="rotate(180,913.5,989.792) scale(1.25,1.23333) translate(-181.2,-183.758)" width="12" x="906" xlink:href="#EnergyConsumer:负荷_0" y="971.2924200693766" zvalue="3576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV母联（备用）"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,913.5,989.792) scale(1.25,1.23333) translate(-181.2,-183.758)" width="12" x="906" y="971.2924200693766"/></g>
  <g id="159">
   <use class="kv10" height="30" transform="rotate(180,1009.5,989.792) scale(1.25,1.23333) translate(-200.4,-183.758)" width="12" x="1002" xlink:href="#EnergyConsumer:负荷_0" y="971.2924200693766" zvalue="3589"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1009.5,989.792) scale(1.25,1.23333) translate(-200.4,-183.758)" width="12" x="1002" y="971.2924200693766"/></g>
  <g id="185">
   <use class="kv10" height="30" transform="rotate(180,1101.5,989.792) scale(1.25,1.23333) translate(-218.8,-183.758)" width="12" x="1094" xlink:href="#EnergyConsumer:负荷_0" y="971.2924200693766" zvalue="3602"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV冶炼线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1101.5,989.792) scale(1.25,1.23333) translate(-218.8,-183.758)" width="12" x="1094" y="971.2924200693766"/></g>
  <g id="205">
   <use class="kv10" height="30" transform="rotate(180,1197.5,989.792) scale(1.25,1.23333) translate(-238,-183.758)" width="12" x="1190" xlink:href="#EnergyConsumer:负荷_0" y="971.2924200693766" zvalue="3615"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV矿山线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1197.5,989.792) scale(1.25,1.23333) translate(-238,-183.758)" width="12" x="1190" y="971.2924200693766"/></g>
  <g id="229">
   <use class="kv10" height="30" transform="rotate(180,1301.5,989.792) scale(1.25,1.23333) translate(-258.8,-183.758)" width="12" x="1294" xlink:href="#EnergyConsumer:负荷_0" y="971.2924200693766" zvalue="3628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV水泥厂线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1301.5,989.792) scale(1.25,1.23333) translate(-258.8,-183.758)" width="12" x="1294" y="971.2924200693766"/></g>
  <g id="252">
   <use class="kv10" height="30" transform="rotate(180,1389.5,989.792) scale(1.25,1.23333) translate(-276.4,-183.758)" width="12" x="1382" xlink:href="#EnergyConsumer:负荷_0" y="971.2924200693766" zvalue="3641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用３"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1389.5,989.792) scale(1.25,1.23333) translate(-276.4,-183.758)" width="12" x="1382" y="971.2924200693766"/></g>
  <g id="267">
   <use class="kv10" height="30" transform="rotate(180,1473.5,989.792) scale(1.25,1.23333) translate(-293.2,-183.758)" width="12" x="1466" xlink:href="#EnergyConsumer:负荷_0" y="971.2924200693766" zvalue="3654"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV选厂线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1473.5,989.792) scale(1.25,1.23333) translate(-293.2,-183.758)" width="12" x="1466" y="971.2924200693766"/></g>
  <g id="287">
   <use class="kv10" height="30" transform="rotate(180,1557.5,989.792) scale(1.25,1.23333) translate(-310,-183.758)" width="12" x="1550" xlink:href="#EnergyConsumer:负荷_0" y="971.2924200693766" zvalue="3667"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV生活线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1557.5,989.792) scale(1.25,1.23333) translate(-310,-183.758)" width="12" x="1550" y="971.2924200693766"/></g>
  <g id="301">
   <use class="kv35" height="30" transform="rotate(180,1119.37,625.71) scale(1.25,1.23333) translate(-222.375,-114.878)" width="12" x="1111.873350779918" xlink:href="#EnergyConsumer:负荷_0" y="607.2102994524485" zvalue="3673"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV冶炼炉"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1119.37,625.71) scale(1.25,1.23333) translate(-222.375,-114.878)" width="12" x="1111.873350779918" y="607.2102994524485"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="21">
   <path class="kv35" d="M 912.27 317.51 L 912.27 345.53" stroke-width="1" zvalue="2973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.27 317.51 L 912.27 345.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv35" d="M 913.51 423.42 L 913.51 392.26" stroke-width="1" zvalue="2988"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="1" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.51 423.42 L 913.51 392.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv35" d="M 913.54 447.25 L 913.54 466.06" stroke-width="1" zvalue="2989"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@1" LinkObjectIDznd="217@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.54 447.25 L 913.54 466.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv35" d="M 913.72 502.13 L 913.72 539.49" stroke-width="1" zvalue="2990"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@1" LinkObjectIDznd="196@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.72 502.13 L 913.72 539.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv35" d="M 757.87 431.3 L 757.87 392.26" stroke-width="1" zvalue="3009"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="5@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 757.87 431.3 L 757.87 392.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv35" d="M 757.9 455.13 L 757.9 476.67" stroke-width="1" zvalue="3010"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@1" LinkObjectIDznd="4@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 757.9 455.13 L 757.9 476.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="391">
   <path class="kv10" d="M 913.69 622.36 L 913.69 659.29" stroke-width="1" zvalue="3397"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.69 622.36 L 913.69 659.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="393">
   <path class="kv10" d="M 913.69 659.29 L 913.69 667.74" stroke-width="1" zvalue="3399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391" LinkObjectIDznd="528@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.69 659.29 L 913.69 667.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="412">
   <path class="kv35" d="M 947.66 331.51 L 912.27 331.51" stroke-width="1" zvalue="3419"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 947.66 331.51 L 912.27 331.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 912.3 369.35 L 912.3 392.26" stroke-width="1" zvalue="3503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.3 369.35 L 912.3 392.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 912.74 246.02 L 912.74 317.51" stroke-width="1" zvalue="3512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.74 246.02 L 912.74 317.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 913.61 691.57 L 913.61 711.43" stroke-width="1" zvalue="3515"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="528@1" LinkObjectIDznd="523@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.61 691.57 L 913.61 711.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 913.91 747.5 L 913.91 763.74" stroke-width="1" zvalue="3516"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="523@1" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.91 747.5 L 913.91 763.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv10" d="M 913.94 787.57 L 913.94 808.33" stroke-width="1" zvalue="3517"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@1" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.94 787.57 L 913.94 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 361.57 925.06 L 361.57 871.84" stroke-width="1" zvalue="3518"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="614@0" LinkObjectIDznd="612@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.57 925.06 L 361.57 871.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 361.6 848.01 L 361.6 808.33" stroke-width="1" zvalue="3519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="612@1" LinkObjectIDznd="532@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.6 848.01 L 361.6 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 491.67 973.14 L 491.67 909.46" stroke-width="1" zvalue="3524"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="658@0" LinkObjectIDznd="666@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 491.67 973.14 L 491.67 909.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 491.61 873.39 L 491.57 858.48" stroke-width="1" zvalue="3525"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="666@0" LinkObjectIDznd="51@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 491.61 873.39 L 491.57 858.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 491.6 834.65 L 491.6 808.33" stroke-width="1" zvalue="3526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@1" LinkObjectIDznd="532@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 491.6 834.65 L 491.6 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 595.6 834.65 L 595.6 808.33" stroke-width="1" zvalue="3536"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@1" LinkObjectIDznd="532@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.6 834.65 L 595.6 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 595.79 948.46 L 595.79 858.48" stroke-width="1" zvalue="3542"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="75@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.79 948.46 L 595.79 858.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 595.02 891.37 L 595.79 891.37" stroke-width="1" zvalue="3544"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.02 891.37 L 595.79 891.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 700.75 834.65 L 700.75 808.33" stroke-width="1" zvalue="3550"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@1" LinkObjectIDznd="532@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.75 834.65 L 700.75 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv10" d="M 700.72 858.48 L 700.72 865.94 L 700.76 865.94 L 700.76 873.39" stroke-width="1" zvalue="3551"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.72 858.48 L 700.72 865.94 L 700.76 865.94 L 700.76 873.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv10" d="M 700.94 909.46 L 700.94 927.21" stroke-width="1" zvalue="3552"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@1" LinkObjectIDznd="92@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.94 909.46 L 700.94 927.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 701.5 973.14 L 701.5 951.04" stroke-width="1" zvalue="3555"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="92@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.5 973.14 L 701.5 951.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 807.67 973.14 L 807.67 909.46" stroke-width="1" zvalue="3562"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="114@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.67 973.14 L 807.67 909.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 807.61 873.39 L 807.57 858.48" stroke-width="1" zvalue="3564"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.61 873.39 L 807.57 858.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 807.6 834.65 L 807.6 808.33" stroke-width="1" zvalue="3565"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@1" LinkObjectIDznd="532@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.6 834.65 L 807.6 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 912.75 834.65 L 912.75 808.33" stroke-width="1" zvalue="3572"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@1" LinkObjectIDznd="532@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.75 834.65 L 912.75 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 912.72 858.48 L 912.76 873.39" stroke-width="1" zvalue="3574"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="137@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.72 858.48 L 912.76 873.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 912.94 909.46 L 912.94 927.21" stroke-width="1" zvalue="3575"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@1" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.94 909.46 L 912.94 927.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv10" d="M 913.5 973.14 L 913.5 951.04" stroke-width="1" zvalue="3578"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.5 973.14 L 913.5 951.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 1008.75 834.65 L 1008.75 808.33" stroke-width="1" zvalue="3585"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@1" LinkObjectIDznd="532@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.75 834.65 L 1008.75 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 1008.72 858.48 L 1008.76 873.39" stroke-width="1" zvalue="3587"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="171@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.72 858.48 L 1008.76 873.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1008.94 909.46 L 1008.94 927.21" stroke-width="1" zvalue="3588"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@1" LinkObjectIDznd="169@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.94 909.46 L 1008.94 927.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 1009.5 973.14 L 1009.5 951.04" stroke-width="1" zvalue="3591"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="169@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1009.5 973.14 L 1009.5 951.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1100.75 834.65 L 1100.75 808.33" stroke-width="1" zvalue="3598"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@1" LinkObjectIDznd="532@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.75 834.65 L 1100.75 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1100.72 858.48 L 1100.76 873.39" stroke-width="1" zvalue="3600"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="194@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.72 858.48 L 1100.76 873.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1100.94 909.46 L 1100.94 927.21" stroke-width="1" zvalue="3601"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@1" LinkObjectIDznd="189@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.94 909.46 L 1100.94 927.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv10" d="M 1101.5 973.14 L 1101.5 951.04" stroke-width="1" zvalue="3604"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="189@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1101.5 973.14 L 1101.5 951.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 1196.75 834.65 L 1196.75 808.33" stroke-width="1" zvalue="3611"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@1" LinkObjectIDznd="532@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.75 834.65 L 1196.75 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv10" d="M 1196.72 858.48 L 1196.76 873.39" stroke-width="1" zvalue="3613"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="221@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.72 858.48 L 1196.76 873.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv10" d="M 1196.94 909.46 L 1196.94 927.21" stroke-width="1" zvalue="3614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="213@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.94 909.46 L 1196.94 927.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 1197.5 973.14 L 1197.5 951.04" stroke-width="1" zvalue="3617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1197.5 973.14 L 1197.5 951.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv10" d="M 1300.75 834.65 L 1300.75 808.33" stroke-width="1" zvalue="3624"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="532@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.75 834.65 L 1300.75 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 1300.72 858.48 L 1300.76 873.39" stroke-width="1" zvalue="3626"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="240@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.72 858.48 L 1300.76 873.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 1300.94 909.46 L 1300.94 927.21" stroke-width="1" zvalue="3627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@1" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.94 909.46 L 1300.94 927.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv10" d="M 1301.5 973.14 L 1301.5 951.04" stroke-width="1" zvalue="3630"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="236@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1301.5 973.14 L 1301.5 951.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 1388.75 834.65 L 1388.75 808.33" stroke-width="1" zvalue="3637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@1" LinkObjectIDznd="532@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1388.75 834.65 L 1388.75 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 1388.72 858.48 L 1388.76 873.39" stroke-width="1" zvalue="3639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@0" LinkObjectIDznd="258@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1388.72 858.48 L 1388.76 873.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv10" d="M 1388.94 909.46 L 1388.94 927.21" stroke-width="1" zvalue="3640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@1" LinkObjectIDznd="256@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1388.94 909.46 L 1388.94 927.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 1389.5 973.14 L 1389.5 951.04" stroke-width="1" zvalue="3643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="256@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1389.5 973.14 L 1389.5 951.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv10" d="M 1472.75 834.65 L 1472.75 808.33" stroke-width="1" zvalue="3650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="532@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.75 834.65 L 1472.75 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 1472.72 858.48 L 1472.76 873.39" stroke-width="1" zvalue="3652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="281@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.72 858.48 L 1472.76 873.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv10" d="M 1472.94 909.46 L 1472.94 927.21" stroke-width="1" zvalue="3653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@1" LinkObjectIDznd="279@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.94 909.46 L 1472.94 927.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="kv10" d="M 1473.5 973.14 L 1473.5 951.04" stroke-width="1" zvalue="3656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="279@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1473.5 973.14 L 1473.5 951.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv10" d="M 1556.75 834.65 L 1556.75 808.33" stroke-width="1" zvalue="3663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@1" LinkObjectIDznd="532@12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1556.75 834.65 L 1556.75 808.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv10" d="M 1556.72 858.48 L 1556.76 873.39" stroke-width="1" zvalue="3665"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@0" LinkObjectIDznd="293@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1556.72 858.48 L 1556.76 873.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="288">
   <path class="kv10" d="M 1556.94 909.46 L 1556.94 927.21" stroke-width="1" zvalue="3666"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@1" LinkObjectIDznd="291@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1556.94 909.46 L 1556.94 927.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 1557.5 973.14 L 1557.5 951.04" stroke-width="1" zvalue="3669"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.5 973.14 L 1557.5 951.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv35" d="M 1119.31 461.31 L 1119.28 446.4" stroke-width="1" zvalue="3678"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="302@0" LinkObjectIDznd="300@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.31 461.31 L 1119.28 446.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv35" d="M 1119.31 422.57 L 1119.31 392.26" stroke-width="1" zvalue="3679"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@1" LinkObjectIDznd="5@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.31 422.57 L 1119.31 392.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv35" d="M 1119.5 497.38 L 1119.5 521.34 L 1119.31 521.34 L 1119.31 545.31" stroke-width="1" zvalue="3682"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="302@1" LinkObjectIDznd="309@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.5 497.38 L 1119.5 521.34 L 1119.31 521.34 L 1119.31 545.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv35" d="M 1119.5 581.38 L 1119.5 595.22 L 1119.37 595.22 L 1119.37 609.06" stroke-width="1" zvalue="3683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309@1" LinkObjectIDznd="301@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.5 581.38 L 1119.5 595.22 L 1119.37 595.22 L 1119.37 609.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="MeasurementClass">
  <g id="414">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="414" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,491.668,1039.29) scale(1,1) translate(0,0)" writing-mode="lr" x="491.67" xml:space="preserve" y="1045.46" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126148702212" ObjectName="P"/>
   </metadata>
  </g>
  <g id="418">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="418" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,491.668,1056.29) scale(1,1) translate(0,0)" writing-mode="lr" x="491.67" xml:space="preserve" y="1062.46" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126148767748" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="422">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="422" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,491.668,1073.29) scale(1,1) translate(0,0)" writing-mode="lr" x="491.67" xml:space="preserve" y="1079.46" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126148833287" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="426">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="426" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,491.668,1090.29) scale(1,1) translate(0,0)" writing-mode="lr" x="491.67" xml:space="preserve" y="1096.46" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126149226500" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="429">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="429" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,913.24,125.168) scale(1,1) translate(0,0)" writing-mode="lr" x="913.24" xml:space="preserve" y="131.44" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126138413062" ObjectName="P"/>
   </metadata>
  </g>
  <g id="430">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="430" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,913.24,143.168) scale(1,1) translate(0,0)" writing-mode="lr" x="913.24" xml:space="preserve" y="149.44" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126138478598" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="431">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="431" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,913.24,161.168) scale(1,1) translate(0,0)" writing-mode="lr" x="913.24" xml:space="preserve" y="167.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126138544134" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="432">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="432" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.239,178.432) scale(1,1) translate(0,0)" writing-mode="lr" x="913.24" xml:space="preserve" y="184.7" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126138806278" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="433">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="433" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1265.88,333.565) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.88" xml:space="preserve" y="339.84" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135332868" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="435">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="435" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,500.5,716.832) scale(1,1) translate(0,0)" writing-mode="lr" x="500.5" xml:space="preserve" y="723.1" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126144704516" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="436">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="436" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1265.88,351.565) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.88" xml:space="preserve" y="357.84" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135398404" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="438">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="438" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,500.5,734.832) scale(1,1) translate(0,0)" writing-mode="lr" x="500.5" xml:space="preserve" y="741.1" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126144770052" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="439">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="439" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1265.88,369.565) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.88" xml:space="preserve" y="375.84" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135463940" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="441">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="441" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,500.5,752.832) scale(1,1) translate(0,0)" writing-mode="lr" x="500.5" xml:space="preserve" y="759.1" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126144835588" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="442">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="442" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1265.88,314.898) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.88" xml:space="preserve" y="321.17" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135595012" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="444">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="444" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,500.5,700.165) scale(1,1) translate(0,0)" writing-mode="lr" x="500.5" xml:space="preserve" y="706.4400000000001" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126144966660" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="445">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="445" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1265.88,389.565) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.88" xml:space="preserve" y="395.84" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135791620" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="447">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="447" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,500.5,772.832) scale(1,1) translate(0,0)" writing-mode="lr" x="500.5" xml:space="preserve" y="779.1" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126145163270" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="450">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="450" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,825.689,464.303) scale(1,1) translate(0,0)" writing-mode="lr" x="825.6900000000001" xml:space="preserve" y="470.47" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135857156" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="452">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="452" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,825.689,481.303) scale(1,1) translate(0,0)" writing-mode="lr" x="825.6900000000001" xml:space="preserve" y="487.47" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135922692" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="454">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="454" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,825.689,710.188) scale(1,1) translate(0,0)" writing-mode="lr" x="825.6900000000001" xml:space="preserve" y="716.35" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135988228" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="456">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="456" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,825.689,727.188) scale(1,1) translate(0,-6.38767e-13)" writing-mode="lr" x="825.6900000000001" xml:space="preserve" y="733.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126136053764" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="458">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="458" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,825.689,498.303) scale(1,1) translate(0,0)" writing-mode="lr" x="825.6900000000001" xml:space="preserve" y="504.47" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126136119300" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="460">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="460" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,832.509,567.245) scale(1,1) translate(1.78526e-13,0)" writing-mode="lr" x="832.51" xml:space="preserve" y="571.91" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126136315908" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="462">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="462" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,832.509,596.245) scale(1,1) translate(-1.78526e-13,0)" writing-mode="lr" x="832.51" xml:space="preserve" y="600.91" zvalue="1">档位:ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126136381444" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="464">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="464" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,825.689,744.188) scale(1,1) translate(0,0)" writing-mode="lr" x="825.6900000000001" xml:space="preserve" y="750.35" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126136446980" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="466">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="466" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,825.689,514.303) scale(1,1) translate(0,0)" writing-mode="lr" x="825.6900000000001" xml:space="preserve" y="520.47" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126137036804" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="468">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="468" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,825.689,760.188) scale(1,1) translate(0,0)" writing-mode="lr" x="825.6900000000001" xml:space="preserve" y="766.35" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126137233412" ObjectName="LCos"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="1166">
   <use height="30" stroke="rgb(255,255,255)" transform="rotate(0,83.9702,134.429) scale(0.958333,0.916667) translate(3.02588,10.9708)" width="30" x="69.59999999999999" xlink:href="#State:红绿圆_0" y="120.68" zvalue="3486"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549586624514" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,83.9702,134.429) scale(0.958333,0.916667) translate(3.02588,10.9708)" width="30" x="69.59999999999999" y="120.68"/></g>
  <g id="1165">
   <use height="30" transform="rotate(0,145.399,134.429) scale(0.958333,0.916667) translate(5.69669,10.9708)" width="30" x="131.02" xlink:href="#State:红绿圆_0" y="120.68" zvalue="3487"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549586624514" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,145.399,134.429) scale(0.958333,0.916667) translate(5.69669,10.9708)" width="30" x="131.02" y="120.68"/></g>
  <g id="1164">
   <use height="30" transform="rotate(0,209.399,134.429) scale(0.958333,0.916667) translate(8.4793,10.9708)" width="30" x="195.02" xlink:href="#State:红绿圆_0" y="120.68" zvalue="3488"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549586624514" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,209.399,134.429) scale(0.958333,0.916667) translate(8.4793,10.9708)" width="30" x="195.02" y="120.68"/></g>
  <g id="1159">
   <use height="30" transform="rotate(0,98.8988,203.179) scale(0.958333,0.916667) translate(3.67495,17.2208)" width="30" x="84.52" xlink:href="#State:红绿圆_0" y="189.43" zvalue="3491"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549586624514" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,98.8988,203.179) scale(0.958333,0.916667) translate(3.67495,17.2208)" width="30" x="84.52" y="189.43"/></g>
  <g id="1157">
   <use height="30" transform="rotate(0,186.399,203.179) scale(0.958333,0.916667) translate(7.4793,17.2208)" width="30" x="172.02" xlink:href="#State:红绿圆_0" y="189.43" zvalue="3493"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549586624514" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,186.399,203.179) scale(0.958333,0.916667) translate(7.4793,17.2208)" width="30" x="172.02" y="189.43"/></g>
 </g>
</svg>