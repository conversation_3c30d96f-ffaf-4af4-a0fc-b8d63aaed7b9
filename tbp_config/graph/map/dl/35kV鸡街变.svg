<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587148802" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带负荷1_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:10kV接地信号源_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="2"/>
   <rect fill-opacity="0" height="16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,10) scale(1,1) translate(0,0)" width="20" x="2.5" y="2"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Compensator:10kV电容器_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="4.699999999999999"/>
   <path d="M 12 20.1 L 17.85 20.1 L 17.85 24.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="4.749074074074073" y1="35.35833333333333" y2="35.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.72129629629629" x2="4.72129629629629" y1="33.13611111111111" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="24.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.258333333333324" x2="0.258333333333324" y1="22.10833333333333" y2="33.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75833333333333" x2="4.75833333333333" y1="22.35833333333333" y2="20.15462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="31.85833333333333" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="20.04351851851851" y2="24.775"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.024239280774554" x2="4.024239280774554" y1="39.35833333333333" y2="37.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.786111111111114" x2="12.00833333333334" y1="20.10833333333333" y2="20.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="35.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666666" x2="4.024239280774546" y1="39.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666667" x2="19.84166666666667" y1="37.35833333333333" y2="39.35833333333333"/>
   <path d="M 4.75833 25.9572 A 2.96392 1.81747 180 0 1 4.75833 22.3223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 29.5921 A 2.96392 1.81747 180 0 1 4.75833 25.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 33.1271 A 2.96392 1.81747 180 0 1 4.75833 29.4921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="30.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="31.85833333333333" y2="31.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="4.5" y2="7.166666666666668"/>
   <path d="M 7.26667 12.1 A 4.91667 4.75 -450 1 0 12.0167 7.18333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 12.0833 L 12 12.1667 L 12 20.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="29.36667381975841" y2="30.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="31.0857461490052" y2="31.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="30.7156109584975" y2="30.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="30.34547576798984" y2="30.34547576798984"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,27.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="25.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="24.56666666666668" y2="25.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.08015580397418" y2="27.73243882624067"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(85,255,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV鸡街变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="35kV鸡街变_软压板.svg"><rect fill-opacity="0" height="40" width="97.14" x="106" y="479.89" zvalue="1145"/></g>
  <g href="35kV鸡街变_直流监控.svg"><rect fill-opacity="0" height="40" width="97.14" x="106" y="536.4400000000001" zvalue="1146"/></g>
  <g href="单厂站信息-dali.svg"><rect fill-opacity="0" height="40" width="97.14" x="106" y="593" zvalue="1263"/></g>
 </g>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,168.417,54.3036) scale(1,1) translate(0,0)" writing-mode="lr" x="168.42" xml:space="preserve" y="71.3" zvalue="1043">     鸡街变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="39" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,154.571,499.889) scale(1,1) translate(0,0)" width="97.14" x="106" y="479.89" zvalue="1145"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,154.571,499.889) scale(1,1) translate(0,0)" writing-mode="lr" x="154.57" xml:space="preserve" y="503.89" zvalue="1145">软压板</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="34" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,154.571,556.444) scale(1,1) translate(0,0)" width="97.14" x="106" y="536.4400000000001" zvalue="1146"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,154.571,556.444) scale(1,1) translate(0,0)" writing-mode="lr" x="154.57" xml:space="preserve" y="560.4400000000001" zvalue="1146">直流监控</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="4" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,193.571,145) scale(1,1) translate(0,0)" width="97.14" x="145" y="125" zvalue="1259"/>
  <text fill="rgb(255,170,0)" font-family="FangSong" font-size="19" id="5" stroke="rgb(255,170,0)" text-anchor="middle" transform="rotate(0,193.571,145) scale(1,1) translate(0,0)" writing-mode="lr" x="193.57" xml:space="preserve" y="151.5" zvalue="1259">全站可控</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40" id="90" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,154.571,613) scale(1,1) translate(0,0)" width="97.14" x="106" y="593" zvalue="1263"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,154.571,613) scale(1,1) translate(0,0)" writing-mode="lr" x="154.57" xml:space="preserve" y="622" zvalue="1263">AVC</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,284.495,788.17) scale(1,1) translate(0,0)" writing-mode="lr" x="284.49" xml:space="preserve" y="794.17" zvalue="8">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,993.6,464.965) scale(1,1) translate(0,0)" writing-mode="lr" x="993.6" xml:space="preserve" y="469.46" zvalue="10">3012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999.449,768.676) scale(1,1) translate(0,0)" writing-mode="lr" x="999.45" xml:space="preserve" y="773.1799999999999" zvalue="17">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1001.22,716.734) scale(1,1) translate(0,0)" writing-mode="lr" x="1001.22" xml:space="preserve" y="721.23" zvalue="19">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1524.52,842.366) scale(1,1) translate(0,0)" writing-mode="lr" x="1524.52" xml:space="preserve" y="846.87" zvalue="346">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,538.238,899.464) scale(1,1) translate(0,0)" writing-mode="lr" x="538.24" xml:space="preserve" y="903.96" zvalue="803">091</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="394" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.738,845.588) scale(1,1) translate(0,0)" writing-mode="lr" x="534.74" xml:space="preserve" y="850.09" zvalue="805">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="390" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.738,952.255) scale(1,1) translate(0,0)" writing-mode="lr" x="534.74" xml:space="preserve" y="956.75" zvalue="818">0916</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="424" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,566.722,1102.11) scale(1,1) translate(0,0)" writing-mode="lr" x="566.72" xml:space="preserve" y="1108.11" zvalue="830">10kV菜白线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="432" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.238,899.464) scale(1,1) translate(0,0)" writing-mode="lr" x="798.24" xml:space="preserve" y="903.96" zvalue="832">092</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="429" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.738,845.588) scale(1,1) translate(0,0)" writing-mode="lr" x="794.74" xml:space="preserve" y="850.09" zvalue="834">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="427" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.738,952.255) scale(1,1) translate(0,0)" writing-mode="lr" x="794.74" xml:space="preserve" y="956.75" zvalue="843">0926</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="425" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824.392,1102.11) scale(1,1) translate(0,0)" writing-mode="lr" x="824.39" xml:space="preserve" y="1108.11" zvalue="854">10kV新寨线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="463" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070.24,899.464) scale(1,1) translate(0,0)" writing-mode="lr" x="1070.24" xml:space="preserve" y="903.96" zvalue="862">093</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="462" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1066.74,846.088) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.74" xml:space="preserve" y="850.59" zvalue="864">0931</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="460" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1066.74,952.255) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.74" xml:space="preserve" y="956.75" zvalue="873">0936</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="458" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1081.72,1102.11) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.72" xml:space="preserve" y="1108.11" zvalue="884">10kV达村线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,996.1,322.959) scale(1,1) translate(0,0)" writing-mode="lr" x="996.1" xml:space="preserve" y="327.46" zvalue="894">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,993.6,253.911) scale(1,1) translate(0,0)" writing-mode="lr" x="993.6" xml:space="preserve" y="258.41" zvalue="896">3016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,965.203,95.4214) scale(1,1) translate(0,0)" writing-mode="lr" x="965.2" xml:space="preserve" y="101.42" zvalue="898">35kV龙鸡线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,993.6,391.828) scale(1,1) translate(0,0)" writing-mode="lr" x="993.6" xml:space="preserve" y="396.33" zvalue="900">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1015.49,158.864) scale(1,1) translate(0,0)" writing-mode="lr" x="1015.49" xml:space="preserve" y="163.36" zvalue="905">3018</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.254,237.095) scale(1,1) translate(0,0)" writing-mode="lr" x="925.25" xml:space="preserve" y="241.6" zvalue="909">30167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,301.321,845.588) scale(1,1) translate(0,0)" writing-mode="lr" x="301.32" xml:space="preserve" y="850.09" zvalue="951">0891</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,333.108,954.714) scale(1,1) translate(0,0)" writing-mode="lr" x="333.11" xml:space="preserve" y="960.71" zvalue="953">10kV接地信号源</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.45,1016.3) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.45" xml:space="preserve" y="1020.8" zvalue="966">0938</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1194.01,1102.11) scale(1,1) translate(0,0)" writing-mode="lr" x="1194.01" xml:space="preserve" y="1108.11" zvalue="968">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.254,510.205) scale(1,1) translate(0,0)" writing-mode="lr" x="925.25" xml:space="preserve" y="514.7" zvalue="977">30127</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.254,375.188) scale(1,1) translate(0,0)" writing-mode="lr" x="925.25" xml:space="preserve" y="379.69" zvalue="982">30117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.254,302.839) scale(1,1) translate(0,0)" writing-mode="lr" x="925.25" xml:space="preserve" y="307.34" zvalue="986">30160</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1152.9,173.724) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.9" xml:space="preserve" y="179.72" zvalue="990">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1320.46,897.242) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.46" xml:space="preserve" y="901.74" zvalue="1001">094</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1316.96,843.366) scale(1,1) translate(0,0)" writing-mode="lr" x="1316.96" xml:space="preserve" y="847.87" zvalue="1003">0941</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317.87,1001.51) scale(1,1) translate(0,0)" writing-mode="lr" x="1317.87" xml:space="preserve" y="1006.01" zvalue="1007">09467</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1316.96,952.255) scale(1,1) translate(0,0)" writing-mode="lr" x="1316.96" xml:space="preserve" y="956.75" zvalue="1012">0946</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1352.37,1102.11) scale(1,1) translate(0,0)" writing-mode="lr" x="1352.37" xml:space="preserve" y="1108.11" zvalue="1016">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.87,1068.85) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.87" xml:space="preserve" y="1073.35" zvalue="1018">09497</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1566.38,954.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.38" xml:space="preserve" y="960.71" zvalue="1097">10kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1129.3,580.104) scale(1,1) translate(0,0)" writing-mode="lr" x="1129.3" xml:space="preserve" y="586.1" zvalue="1134">35kV1号主变（3.15MVA）</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,113.232,362.179) scale(1,1) translate(0,0)" writing-mode="lr" x="113.23" xml:space="preserve" y="368.18" zvalue="1182">负荷总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,109.482,397.929) scale(1,1) translate(0,0)" writing-mode="lr" x="109.48" xml:space="preserve" y="403.93" zvalue="1183">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,113.232,432.929) scale(1,1) translate(0,0)" writing-mode="lr" x="113.23" xml:space="preserve" y="438.93" zvalue="1184">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,107.149,248.429) scale(1,1) translate(0,0)" writing-mode="lr" x="107.15" xml:space="preserve" y="254.43" zvalue="1188">事故</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,118.149,315.179) scale(1,1) translate(0,0)" writing-mode="lr" x="118.15" xml:space="preserve" y="321.18" zvalue="1189">是否失压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.649,315.179) scale(1,1) translate(0,0)" writing-mode="lr" x="199.65" xml:space="preserve" y="321.18" zvalue="1191">失压排除</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,167.149,248.429) scale(1,1) translate(0,0)" writing-mode="lr" x="167.15" xml:space="preserve" y="254.43" zvalue="1193">异常</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.149,248.429) scale(1,1) translate(0,0)" writing-mode="lr" x="227.15" xml:space="preserve" y="254.43" zvalue="1194">告知</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.435,398.5) scale(1,1) translate(0,0)" writing-mode="lr" x="228.43" xml:space="preserve" y="404.5" zvalue="1195">鸡街变-01-2023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.435,433.5) scale(1,1) translate(0,0)" writing-mode="lr" x="224.43" xml:space="preserve" y="439.5" zvalue="1196">2023-01-13</text>
 </g>
 <g id="PowerTransformer2Class">
  <g id="203">
   <g id="2030">
    <use class="kv35" height="30" transform="rotate(0,965.684,582.265) scale(3.09833,2.96282) translate(-628.825,-356.299)" width="24" x="928.5" xlink:href="#PowerTransformer2:可调两卷变_0" y="537.8200000000001" zvalue="5"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442145795" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2031">
    <use class="kv10" height="30" transform="rotate(0,965.684,582.265) scale(3.09833,2.96282) translate(-628.825,-356.299)" width="24" x="928.5" xlink:href="#PowerTransformer2:可调两卷变_1" y="537.8200000000001" zvalue="5"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442211331" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451672579" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399451672579"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,965.684,582.265) scale(3.09833,2.96282) translate(-628.825,-356.299)" width="24" x="928.5" y="537.8200000000001"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="211">
   <path class="kv10" d="M 252.22 808.84 L 1623.33 808.84" stroke-width="6" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674252357636" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674252357636"/></metadata>
  <path d="M 252.22 808.84 L 1623.33 808.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv35" d="M 935.95 426.84 L 994.73 426.84" stroke-width="6" zvalue="1250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674313175043" ObjectName="35kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674313175043"/></metadata>
  <path d="M 935.95 426.84 L 994.73 426.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="215">
   <use class="kv35" height="30" transform="rotate(0,965.786,467.729) scale(-1.11133,0.814667) translate(-1833.98,103.627)" width="15" x="957.4508039472503" xlink:href="#Disconnector:刀闸_0" y="455.509375665838" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450294972422" ObjectName="#1主变35kV侧3012"/>
   <cge:TPSR_Ref TObjectID="6192450294972422"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,965.786,467.729) scale(-1.11133,0.814667) translate(-1833.98,103.627)" width="15" x="957.4508039472503" y="455.509375665838"/></g>
  <g id="229">
   <use class="kv10" height="30" transform="rotate(0,965.684,769.676) scale(-1.11133,0.814667) translate(-1833.79,172.318)" width="15" x="957.348828829398" xlink:href="#Disconnector:刀闸_0" y="757.4563453628076" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450294906886" ObjectName="#1主变10kV侧0011"/>
   <cge:TPSR_Ref TObjectID="6192450294906886"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,965.684,769.676) scale(-1.11133,0.814667) translate(-1833.79,172.318)" width="15" x="957.348828829398" y="757.4563453628076"/></g>
  <g id="284">
   <use class="kv10" height="30" transform="rotate(0,1555.36,844.366) scale(-1.11133,0.814667) translate(-2954.07,189.31)" width="15" x="1547.025792371962" xlink:href="#Disconnector:刀闸_0" y="832.1457367193335" zvalue="345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295037958" ObjectName="10kVⅠ母电压互感器0901"/>
   <cge:TPSR_Ref TObjectID="6192450295037958"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1555.36,844.366) scale(-1.11133,0.814667) translate(-2954.07,189.31)" width="15" x="1547.025792371962" y="832.1457367193335"/></g>
  <g id="421">
   <use class="kv10" height="30" transform="rotate(0,565.583,846.588) scale(-1.11133,0.814667) translate(-1073.67,189.815)" width="15" x="557.2480079326767" xlink:href="#Disconnector:刀闸_0" y="834.3679615244237" zvalue="804"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295300102" ObjectName="10kV菜白线0911"/>
   <cge:TPSR_Ref TObjectID="6192450295300102"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,565.583,846.588) scale(-1.11133,0.814667) translate(-1073.67,189.815)" width="15" x="557.2480079326767" y="834.3679615244237"/></g>
  <g id="405">
   <use class="kv10" height="30" transform="rotate(0,565.571,953.255) scale(-1.11133,0.814667) translate(-1073.65,214.082)" width="15" x="557.236295898133" xlink:href="#Disconnector:刀闸_0" y="941.0346374511715" zvalue="817"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295234566" ObjectName="10kV菜白线0916"/>
   <cge:TPSR_Ref TObjectID="6192450295234566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,565.571,953.255) scale(-1.11133,0.814667) translate(-1073.65,214.082)" width="15" x="557.236295898133" y="941.0346374511715"/></g>
  <g id="452">
   <use class="kv10" height="30" transform="rotate(0,825.583,846.588) scale(-1.11133,0.814667) translate(-1567.62,189.815)" width="15" x="817.2480079326767" xlink:href="#Disconnector:刀闸_0" y="834.3679615244237" zvalue="833"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295562247" ObjectName="10kV新寨线0921"/>
   <cge:TPSR_Ref TObjectID="6192450295562247"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,825.583,846.588) scale(-1.11133,0.814667) translate(-1567.62,189.815)" width="15" x="817.2480079326767" y="834.3679615244237"/></g>
  <g id="443">
   <use class="kv10" height="30" transform="rotate(0,825.571,953.255) scale(-1.11133,0.814667) translate(-1567.6,214.082)" width="15" x="817.236295898133" xlink:href="#Disconnector:刀闸_0" y="941.0346374511715" zvalue="842"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295496711" ObjectName="10kV新寨线0926"/>
   <cge:TPSR_Ref TObjectID="6192450295496711"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,825.571,953.255) scale(-1.11133,0.814667) translate(-1567.6,214.082)" width="15" x="817.236295898133" y="941.0346374511715"/></g>
  <g id="480">
   <use class="kv10" height="30" transform="rotate(0,1097.58,847.088) scale(-1.11133,0.814667) translate(-2084.38,189.929)" width="15" x="1089.248007932677" xlink:href="#Disconnector:刀闸_0" y="834.8679615244237" zvalue="863"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295758855" ObjectName="10kV达村线0931"/>
   <cge:TPSR_Ref TObjectID="6192450295758855"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1097.58,847.088) scale(-1.11133,0.814667) translate(-2084.38,189.929)" width="15" x="1089.248007932677" y="834.8679615244237"/></g>
  <g id="473">
   <use class="kv10" height="30" transform="rotate(0,1097.57,953.255) scale(-1.11133,0.814667) translate(-2084.35,214.082)" width="15" x="1089.236295898133" xlink:href="#Disconnector:刀闸_0" y="941.0346374511715" zvalue="872"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295693319" ObjectName="10kV达村线0936"/>
   <cge:TPSR_Ref TObjectID="6192450295693319"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1097.57,953.255) scale(-1.11133,0.814667) translate(-2084.35,214.082)" width="15" x="1089.236295898133" y="941.0346374511715"/></g>
  <g id="37">
   <use class="kv35" height="30" transform="rotate(0,965.617,255.161) scale(-1.11133,0.814667) translate(-1833.66,55.2681)" width="15" x="957.2817465844635" xlink:href="#Disconnector:刀闸_0" y="242.9411911934817" zvalue="895"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296283141" ObjectName="35kV龙鸡线3016"/>
   <cge:TPSR_Ref TObjectID="6192450296283141"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,965.617,255.161) scale(-1.11133,0.814667) translate(-1833.66,55.2681)" width="15" x="957.2817465844635" y="242.9411911934817"/></g>
  <g id="35">
   <use class="kv35" height="30" transform="rotate(0,965.825,391.495) scale(-1.11133,0.814667) translate(-1834.06,86.2834)" width="15" x="957.489643360653" xlink:href="#Disconnector:刀闸_0" y="379.2745271809895" zvalue="899"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296152069" ObjectName="35kV龙鸡线3011"/>
   <cge:TPSR_Ref TObjectID="6192450296152069"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,965.825,391.495) scale(-1.11133,0.814667) translate(-1834.06,86.2834)" width="15" x="957.489643360653" y="379.2745271809895"/></g>
  <g id="266">
   <use class="kv10" height="30" transform="rotate(0,1351.81,844.366) scale(-1.11133,0.814667) translate(-2567.35,189.31)" width="15" x="1343.470230154899" xlink:href="#Disconnector:刀闸_0" y="832.1457366943359" zvalue="1002"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297856005" ObjectName="10kV1号电容器0941"/>
   <cge:TPSR_Ref TObjectID="6192450297856005"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1351.81,844.366) scale(-1.11133,0.814667) translate(-2567.35,189.31)" width="15" x="1343.470230154899" y="832.1457366943359"/></g>
  <g id="131">
   <use class="kv10" height="30" transform="rotate(0,1351.81,953.255) scale(-1.11133,0.814667) translate(-2567.35,214.082)" width="15" x="1343.470230154899" xlink:href="#Disconnector:刀闸_0" y="941.0346371980146" zvalue="1011"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297593863" ObjectName="10kV1号电容器0946"/>
   <cge:TPSR_Ref TObjectID="6192450297593863"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1351.81,953.255) scale(-1.11133,0.814667) translate(-2567.35,214.082)" width="15" x="1343.470230154899" y="941.0346371980146"/></g>
 </g>
 <g id="BreakerClass">
  <g id="230">
   <use class="kv10" height="20" transform="rotate(0,965.684,712.984) scale(1.83111,1.88671) translate(-434.152,-326.218)" width="10" x="956.5282714118275" xlink:href="#Breaker:开关_0" y="694.1171120227542" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563435524" ObjectName="#1主变10kV侧001"/>
   <cge:TPSR_Ref TObjectID="6473924563435524"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,965.684,712.984) scale(1.83111,1.88671) translate(-434.152,-326.218)" width="10" x="956.5282714118275" y="694.1171120227542"/></g>
  <g id="422">
   <use class="kv10" height="20" transform="rotate(0,565.571,899.714) scale(1.83111,1.88671) translate(-252.548,-413.977)" width="10" x="556.4157384521599" xlink:href="#Breaker:开关_0" y="880.846910002552" zvalue="802"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563501060" ObjectName="10kV菜白线091"/>
   <cge:TPSR_Ref TObjectID="6473924563501060"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,565.571,899.714) scale(1.83111,1.88671) translate(-252.548,-413.977)" width="10" x="556.4157384521599" y="880.846910002552"/></g>
  <g id="453">
   <use class="kv10" height="20" transform="rotate(0,825.571,899.714) scale(1.83111,1.88671) translate(-370.558,-413.977)" width="10" x="816.4157384521599" xlink:href="#Breaker:开关_0" y="880.846910002552" zvalue="831"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563566596" ObjectName="10kV新寨线092"/>
   <cge:TPSR_Ref TObjectID="6473924563566596"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,825.571,899.714) scale(1.83111,1.88671) translate(-370.558,-413.977)" width="10" x="816.4157384521599" y="880.846910002552"/></g>
  <g id="481">
   <use class="kv10" height="20" transform="rotate(0,1097.57,899.714) scale(1.83111,1.88671) translate(-494.014,-413.977)" width="10" x="1088.41573845216" xlink:href="#Breaker:开关_0" y="880.846910002552" zvalue="861"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563632132" ObjectName="10kV达村线093"/>
   <cge:TPSR_Ref TObjectID="6473924563632132"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1097.57,899.714) scale(1.83111,1.88671) translate(-494.014,-413.977)" width="10" x="1088.41573845216" y="880.846910002552"/></g>
  <g id="38">
   <use class="kv35" height="20" transform="rotate(0,965.605,324.621) scale(1.83111,1.88671) translate(-434.117,-143.697)" width="10" x="956.4494623075448" xlink:href="#Breaker:开关_0" y="305.7534756591178" zvalue="893"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563697668" ObjectName="35kV龙鸡线301"/>
   <cge:TPSR_Ref TObjectID="6473924563697668"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,965.605,324.621) scale(1.83111,1.88671) translate(-434.117,-143.697)" width="10" x="956.4494623075448" y="305.7534756591178"/></g>
  <g id="267">
   <use class="kv10" height="20" transform="rotate(0,1351.79,897.492) scale(1.83111,1.88671) translate(-609.401,-412.932)" width="10" x="1342.637960674382" xlink:href="#Breaker:开关_0" y="878.6246877803299" zvalue="1000"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563763204" ObjectName="10kV1号电容器094"/>
   <cge:TPSR_Ref TObjectID="6473924563763204"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1351.79,897.492) scale(1.83111,1.88671) translate(-609.401,-412.932)" width="10" x="1342.637960674382" y="878.6246877803299"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="246">
   <path class="kv10" d="M 965.59 757.86 L 965.59 731" stroke-width="1" zvalue="304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="230@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.59 757.86 L 965.59 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv10" d="M 1555.26 832.55 L 1555.26 808.84" stroke-width="1" zvalue="353"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="211@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1555.26 832.55 L 1555.26 808.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="420">
   <path class="kv10" d="M 565.49 834.77 L 565.49 808.84" stroke-width="1" zvalue="806"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="421@0" LinkObjectIDznd="211@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.49 834.77 L 565.49 808.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="412">
   <path class="kv10" d="M 565.52 858.6 L 565.51 881.66" stroke-width="1" zvalue="807"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="421@1" LinkObjectIDznd="422@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.52 858.6 L 565.51 881.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="406">
   <path class="kv10" d="M 565.5 965.27 L 565.5 1044.44" stroke-width="1" zvalue="816"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="405@1" LinkObjectIDznd="423@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.5 965.27 L 565.5 1044.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="402">
   <path class="kv10" d="M 565.47 941.44 L 565.47 917.73" stroke-width="1" zvalue="822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="405@0" LinkObjectIDznd="422@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.47 941.44 L 565.47 917.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="449">
   <path class="kv10" d="M 825.52 858.6 L 825.51 881.66" stroke-width="1" zvalue="836"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="452@1" LinkObjectIDznd="453@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.52 858.6 L 825.51 881.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="445">
   <path class="kv10" d="M 825.5 965.27 L 825.5 1044.44" stroke-width="1" zvalue="841"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="443@1" LinkObjectIDznd="433@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.5 965.27 L 825.5 1044.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv10" d="M 825.47 941.44 L 825.47 917.73" stroke-width="1" zvalue="847"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="443@0" LinkObjectIDznd="453@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.47 941.44 L 825.47 917.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="478">
   <path class="kv10" d="M 1097.52 859.1 L 1097.51 881.66" stroke-width="1" zvalue="866"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="480@1" LinkObjectIDznd="481@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.52 859.1 L 1097.51 881.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="470">
   <path class="kv10" d="M 1097.47 941.44 L 1097.47 917.73" stroke-width="1" zvalue="877"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="473@0" LinkObjectIDznd="481@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.47 941.44 L 1097.47 917.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv35" d="M 965.73 342.64 L 965.73 379.68" stroke-width="1" zvalue="903"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.73 342.64 L 965.73 379.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 965.52 243.35 L 965.54 143.95" stroke-width="1" zvalue="910"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.52 243.35 L 965.54 143.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 965.54 306.57 L 965.55 267.17" stroke-width="1" zvalue="912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="37@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.54 306.57 L 965.55 267.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 335.09 837.36 L 335.09 808.84" stroke-width="1" zvalue="953"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="211@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 335.09 837.36 L 335.09 808.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 335.09 837.36 L 335.17 903.1" stroke-width="1" zvalue="954"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 335.09 837.36 L 335.17 903.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv10" d="M 1169.91 1009.43 L 1169.91 1038.02" stroke-width="1" zvalue="969"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@0" LinkObjectIDznd="278@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1169.91 1009.43 L 1169.91 1038.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv35" d="M 965.72 541.01 L 965.72 479.74" stroke-width="1" zvalue="978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="215@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.72 541.01 L 965.72 479.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv10" d="M 1351.74 856.38 L 1351.73 879.44" stroke-width="1" zvalue="1005"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.74 856.38 L 1351.73 879.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv10" d="M 1351.71 941.44 L 1351.71 915.51" stroke-width="1" zvalue="1013"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="267@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.71 941.44 L 1351.71 915.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 1011.13 172.29 L 1046.19 172.26" stroke-width="1" zvalue="1050"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1011.13 172.29 L 1046.19 172.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 965.54 172.29 L 1011.13 172.29" stroke-width="1" zvalue="1069"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28" LinkObjectIDznd="1" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.54 172.29 L 1011.13 172.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 965.54 172.56 L 918.21 172.56" stroke-width="1" zvalue="1073"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.54 172.56 L 918.21 172.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 938.81 221.37 L 965.52 221.37" stroke-width="1" zvalue="1074"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.81 221.37 L 965.52 221.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 938.81 287.12 L 965.55 287.12" stroke-width="1" zvalue="1075"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.81 287.12 L 965.55 287.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 938.81 359.47 L 965.73 359.47" stroke-width="1" zvalue="1076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.81 359.47 L 965.73 359.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv35" d="M 938.81 494.48 L 965.72 494.48" stroke-width="1" zvalue="1077"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="40" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.81 494.48 L 965.72 494.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 1351.71 832.55 L 1351.71 808.84" stroke-width="1" zvalue="1081"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="211@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.71 832.55 L 1351.71 808.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 1097.5 984.36 L 1066.16 984.36 L 1066.16 1004.52" stroke-width="1" zvalue="1086"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66" LinkObjectIDznd="107@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.5 984.36 L 1066.16 984.36 L 1066.16 1004.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 798.16 1004.52 L 798.16 986.64 L 825.5 986.64" stroke-width="1" zvalue="1087"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="445" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.16 1004.52 L 798.16 986.64 L 825.5 986.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 538.41 1004.52 L 538.41 985.73 L 565.5 985.73" stroke-width="1" zvalue="1088"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="406" MaxPinNum="2"/>
   </metadata>
  <path d="M 538.41 1004.52 L 538.41 985.73 L 565.5 985.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 1097.5 965.27 L 1097.5 1044.94" stroke-width="1" zvalue="1089"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="473@1" LinkObjectIDznd="464@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.5 965.27 L 1097.5 1044.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 825.49 834.77 L 825.49 808.84" stroke-width="1" zvalue="1092"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="452@0" LinkObjectIDznd="211@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.49 834.77 L 825.49 808.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 965.62 781.69 L 965.62 808.84" stroke-width="1" zvalue="1093"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@1" LinkObjectIDznd="211@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.62 781.69 L 965.62 808.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 1097.49 808.84 L 1097.49 835.27" stroke-width="1" zvalue="1094"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@3" LinkObjectIDznd="480@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.49 808.84 L 1097.49 835.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 1555.29 893.59 L 1555.29 856.38" stroke-width="1" zvalue="1097"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="284@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1555.29 893.59 L 1555.29 856.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 1097.5 984.36 L 1169.91 984.36 L 1169.91 1009.43" stroke-width="1" zvalue="1098"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56" LinkObjectIDznd="116" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.5 984.36 L 1169.91 984.36 L 1169.91 1009.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv10" d="M 1351.74 965.27 L 1351.74 1006.91" stroke-width="1" zvalue="1138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="256@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.74 965.27 L 1351.74 1006.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 1351.74 1006.91 L 1351.74 1062.65" stroke-width="1" zvalue="1139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9" LinkObjectIDznd="258@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.74 1006.91 L 1351.74 1062.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 1329.33 986.04 L 1351.74 986.04" stroke-width="1" zvalue="1140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.33 986.04 L 1351.74 986.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv10" d="M 1371.48 986.25 L 1351.48 986.25" stroke-width="1" zvalue="1141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1371.48 986.25 L 1351.48 986.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 965.68 623.88 L 965.68 653.16" stroke-width="1" zvalue="1197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@1" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.68 623.88 L 965.68 653.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv10" d="M 965.68 653.16 L 965.62 694.93" stroke-width="1" zvalue="1198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.68 653.16 L 965.62 694.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv35" d="M 965.76 403.51 L 965.76 426.84" stroke-width="1" zvalue="1251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@1" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.76 403.51 L 965.76 426.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv35" d="M 965.69 426.84 L 965.69 455.91" stroke-width="1" zvalue="1252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="215@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.69 426.84 L 965.69 455.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="423">
   <use class="kv10" height="30" transform="rotate(180,566,1065.25) scale(1.5625,1.54167) translate(-200.385,-366.152)" width="12" x="556.625" xlink:href="#EnergyConsumer:负荷_0" y="1042.125" zvalue="829"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295365638" ObjectName="10kV菜白线"/>
   <cge:TPSR_Ref TObjectID="6192450295365638"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,566,1065.25) scale(1.5625,1.54167) translate(-200.385,-366.152)" width="12" x="556.625" y="1042.125"/></g>
  <g id="433">
   <use class="kv10" height="30" transform="rotate(180,825.503,1065.25) scale(1.5625,1.54167) translate(-293.806,-366.152)" width="12" x="816.1283218348102" xlink:href="#EnergyConsumer:负荷_0" y="1042.125" zvalue="853"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295431174" ObjectName="10kV新寨线"/>
   <cge:TPSR_Ref TObjectID="6192450295431174"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,825.503,1065.25) scale(1.5625,1.54167) translate(-293.806,-366.152)" width="12" x="816.1283218348102" y="1042.125"/></g>
  <g id="464">
   <use class="kv10" height="30" transform="rotate(180,1097.5,1065.75) scale(1.5625,1.54167) translate(-391.725,-366.328)" width="12" x="1088.125" xlink:href="#EnergyConsumer:负荷_0" y="1042.625" zvalue="883"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295627783" ObjectName="10kV达村线"/>
   <cge:TPSR_Ref TObjectID="6192450295627783"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1097.5,1065.75) scale(1.5625,1.54167) translate(-391.725,-366.328)" width="12" x="1088.125" y="1042.625"/></g>
  <g id="278">
   <use class="kv10" height="30" transform="rotate(0,1169.76,1061.52) scale(1.66793,1.63131) translate(-459.086,-401.334)" width="28" x="1146.408699770881" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="1037.049181092701" zvalue="967"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296676359" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1169.76,1061.52) scale(1.66793,1.63131) translate(-459.086,-401.334)" width="28" x="1146.408699770881" y="1037.049181092701"/></g>
  <g id="62">
   <use class="kv35" height="30" transform="rotate(270,1069.69,172.417) scale(1.66793,1.63131) translate(-419.012,-57.2549)" width="28" x="1046.336714278408" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="147.9469882035663" zvalue="989"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297266183" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1069.69,172.417) scale(1.66793,1.63131) translate(-419.012,-57.2549)" width="28" x="1046.336714278408" y="147.9469882035663"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="36">
   <use class="kv35" height="30" transform="rotate(0,965.544,126.857) scale(2.32143,1.15097) translate(-544.992,-14.3747)" width="7" x="957.41877690195" xlink:href="#ACLineSegment:线路_0" y="109.59264289552" zvalue="897"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249314164742" ObjectName="35kV龙鸡线"/>
   <cge:TPSR_Ref TObjectID="8444249314164742_5066549587148802"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,965.544,126.857) scale(2.32143,1.15097) translate(-544.992,-14.3747)" width="7" x="957.41877690195" y="109.59264289552"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="31">
   <use class="kv35" height="18" transform="rotate(270,1018.4,172.278) scale(-0.791452,0.917581) translate(-2306.19,14.7327)" width="10" x="1014.441671334834" xlink:href="#Accessory:熔断器_0" y="164.0198084732477" zvalue="904"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296020998" ObjectName="35kV1号站用变3018"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1018.4,172.278) scale(-0.791452,0.917581) translate(-2306.19,14.7327)" width="10" x="1014.441671334834" y="164.0198084732477"/></g>
  <g id="100">
   <use class="kv10" height="18" transform="rotate(0,335.071,847.321) scale(1.02857,1.25794) translate(-9.16468,-171.42)" width="10" x="329.9285714285716" xlink:href="#Accessory:熔断器_0" y="835.9999999999999" zvalue="950"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296348677" ObjectName="10kV接地信号源0891"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,335.071,847.321) scale(1.02857,1.25794) translate(-9.16468,-171.42)" width="10" x="329.9285714285716" y="835.9999999999999"/></g>
  <g id="101">
   <use class="kv10" height="20" transform="rotate(0,335.167,919.333) scale(2.02857,2.02857) translate(-157.087,-455.855)" width="25" x="309.8095238095239" xlink:href="#Accessory:10kV接地信号源_0" y="899.0476190476192" zvalue="952"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296414213" ObjectName="10kV接地信号源"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,335.167,919.333) scale(2.02857,2.02857) translate(-157.087,-455.855)" width="25" x="309.8095238095239" y="899.0476190476192"/></g>
  <g id="105">
   <use class="kv10" height="26" transform="rotate(0,538.378,1016.89) scale(1,1) translate(-2.71887e-12,-4.45817e-13)" width="12" x="532.3781915639241" xlink:href="#Accessory:避雷器1_0" y="1003.889862060547" zvalue="956"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296479751" ObjectName="10kV菜白线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,538.378,1016.89) scale(1,1) translate(-2.71887e-12,-4.45817e-13)" width="12" x="532.3781915639241" y="1003.889862060547"/></g>
  <g id="106">
   <use class="kv10" height="26" transform="rotate(0,798.128,1016.89) scale(1,1) translate(-4.04542e-12,-4.45817e-13)" width="12" x="792.1281915639241" xlink:href="#Accessory:避雷器1_0" y="1003.889862060547" zvalue="958"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296545287" ObjectName="10kV新寨线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,798.128,1016.89) scale(1,1) translate(-4.04542e-12,-4.45817e-13)" width="12" x="792.1281915639241" y="1003.889862060547"/></g>
  <g id="107">
   <use class="kv10" height="26" transform="rotate(0,1066.13,1016.89) scale(1,1) translate(-5.4141e-12,-4.45817e-13)" width="12" x="1060.128191563924" xlink:href="#Accessory:避雷器1_0" y="1003.889862060547" zvalue="960"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296610823" ObjectName="10kV达村线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1066.13,1016.89) scale(1,1) translate(-5.4141e-12,-4.45817e-13)" width="12" x="1060.128191563924" y="1003.889862060547"/></g>
  <g id="277">
   <use class="kv10" height="18" transform="rotate(0,1169.9,1015.36) scale(0.645337,0.748216) translate(641.18,339.414)" width="10" x="1166.675567318571" xlink:href="#Accessory:熔断器_0" y="1008.623015873015" zvalue="965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296741895" ObjectName="10kV2号站用变0938"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1169.9,1015.36) scale(0.645337,0.748216) translate(641.18,339.414)" width="10" x="1166.675567318571" y="1008.623015873015"/></g>
  <g id="119">
   <use class="kv10" height="22" transform="rotate(0,965.684,661.044) scale(0.788333,0.788636) translate(258.015,174.842)" width="12" x="960.9538288181707" xlink:href="#Accessory:传输线_0" y="652.3688596491228" zvalue="996"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297331719" ObjectName="#1主变10kV侧中间电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,965.684,661.044) scale(0.788333,0.788636) translate(258.015,174.842)" width="12" x="960.9538288181707" y="652.3688596491228"/></g>
  <g id="274">
   <use class="kv10" height="26" transform="rotate(270,1383.85,986.282) scale(1,1) translate(-7.03672e-12,-4.32224e-13)" width="12" x="1377.850413786146" xlink:href="#Accessory:避雷器1_0" y="973.282400207064" zvalue="1008"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297659399" ObjectName="10kV1号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1383.85,986.282) scale(1,1) translate(-7.03672e-12,-4.32224e-13)" width="12" x="1377.850413786146" y="973.282400207064"/></g>
  <g id="33">
   <use class="kv35" height="42" transform="rotate(90,894.81,161.629) scale(-1.14835,-1.14835) translate(-1671.8,-299.263)" width="30" x="877.584249084249" xlink:href="#Accessory:4卷PT带容断器_0" y="137.513798701141" zvalue="1045"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297200647" ObjectName="35kV龙鸡线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(90,894.81,161.629) scale(-1.14835,-1.14835) translate(-1671.8,-299.263)" width="30" x="877.584249084249" y="137.513798701141"/></g>
  <g id="76">
   <use class="kv10" height="42" transform="rotate(0,1566.23,916.987) scale(1.14835,-1.14835) translate(-200.11,-1712.4)" width="30" x="1549.000855863501" xlink:href="#Accessory:4卷PT带容断器_0" y="892.8717948717949" zvalue="1096"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295169030" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1566.23,916.987) scale(1.14835,-1.14835) translate(-200.11,-1712.4)" width="30" x="1549.000855863501" y="892.8717948717949"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="29">
   <use class="kv35" height="30" transform="rotate(270,926.403,221.385) scale(-1.0125,-0.866) translate(-1841.29,-479.036)" width="12" x="920.3282347106933" xlink:href="#GroundDisconnector:地刀12_0" y="208.3950709186546" zvalue="908"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450295955462" ObjectName="35kV龙鸡线30167"/>
   <cge:TPSR_Ref TObjectID="6192450295955462"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,926.403,221.385) scale(-1.0125,-0.866) translate(-1841.29,-479.036)" width="12" x="920.3282347106933" y="208.3950709186546"/></g>
  <g id="17">
   <use class="kv35" height="30" transform="rotate(90,926.403,494.495) scale(1.0125,0.866) translate(-11.3621,74.5053)" width="12" x="920.328234739188" xlink:href="#GroundDisconnector:地刀12_0" y="481.5046591560828" zvalue="976"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450296872967" ObjectName="#1主变35kV侧30127"/>
   <cge:TPSR_Ref TObjectID="6192450296872967"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,926.403,494.495) scale(1.0125,0.866) translate(-11.3621,74.5053)" width="12" x="920.328234739188" y="481.5046591560828"/></g>
  <g id="54">
   <use class="kv35" height="30" transform="rotate(90,926.403,359.477) scale(1.0125,0.866) translate(-11.3621,53.6135)" width="12" x="920.3282346710685" xlink:href="#GroundDisconnector:地刀12_0" y="346.4874120671675" zvalue="981"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297004039" ObjectName="35kV龙鸡线30117"/>
   <cge:TPSR_Ref TObjectID="6192450297004039"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,926.403,359.477) scale(1.0125,0.866) translate(-11.3621,53.6135)" width="12" x="920.3282346710685" y="346.4874120671675"/></g>
  <g id="59">
   <use class="kv35" height="30" transform="rotate(90,926.403,287.129) scale(1.0125,0.866) translate(-11.3621,42.4187)" width="12" x="920.3282347732478" xlink:href="#GroundDisconnector:地刀12_0" y="274.1385993523118" zvalue="985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297135111" ObjectName="35kV龙鸡线30160"/>
   <cge:TPSR_Ref TObjectID="6192450297135111"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,926.403,287.129) scale(1.0125,0.866) translate(-11.3621,42.4187)" width="12" x="920.3282347732478" y="274.1385993523118"/></g>
  <g id="271">
   <use class="kv10" height="30" transform="rotate(90,1316.93,986.053) scale(1.0125,0.866) translate(-16.1834,150.566)" width="12" x="1310.853833007812" xlink:href="#GroundDisconnector:地刀12_0" y="973.063045214469" zvalue="1006"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297790469" ObjectName="10kV1号电容器09467"/>
   <cge:TPSR_Ref TObjectID="6192450297790469"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1316.93,986.053) scale(1.0125,0.866) translate(-16.1834,150.566)" width="12" x="1310.853833007812" y="973.063045214469"/></g>
  <g id="258">
   <use class="kv10" height="30" transform="rotate(0,1352.26,1075.05) scale(1.0125,0.866) translate(-16.6196,164.338)" width="12" x="1346.187166341145" xlink:href="#GroundDisconnector:地刀12_0" y="1062.063045214469" zvalue="1017"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297462791" ObjectName="10kV1号电容器09497"/>
   <cge:TPSR_Ref TObjectID="6192450297462791"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1352.26,1075.05) scale(1.0125,0.866) translate(-16.6196,164.338)" width="12" x="1346.187166341145" y="1062.063045214469"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="256">
   <use class="kv10" height="40" transform="rotate(0,1351.92,1022.21) scale(-1.04167,1) translate(-2649.27,0)" width="24" x="1339.423974380296" xlink:href="#Compensator:10kV电容器_0" y="1002.210526315789" zvalue="1015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450297528327" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192450297528327"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1351.92,1022.21) scale(-1.04167,1) translate(-2649.27,0)" width="24" x="1339.423974380296" y="1002.210526315789"/></g>
 </g>
 <g id="StateClass">
  <g id="1166">
   <use height="30" stroke="rgb(255,255,255)" transform="rotate(0,103.97,214.429) scale(0.958333,0.916667) translate(3.89545,18.2435)" width="30" x="89.59999999999999" xlink:href="#State:红绿圆_0" y="200.68" zvalue="1185"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549587148802" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,103.97,214.429) scale(0.958333,0.916667) translate(3.89545,18.2435)" width="30" x="89.59999999999999" y="200.68"/></g>
  <g id="1165">
   <use height="30" transform="rotate(0,165.399,214.429) scale(0.958333,0.916667) translate(6.56625,18.2435)" width="30" x="151.02" xlink:href="#State:红绿圆_0" y="200.68" zvalue="1186"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549587148802" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,165.399,214.429) scale(0.958333,0.916667) translate(6.56625,18.2435)" width="30" x="151.02" y="200.68"/></g>
  <g id="1164">
   <use height="30" transform="rotate(0,229.399,214.429) scale(0.958333,0.916667) translate(9.34886,18.2435)" width="30" x="215.02" xlink:href="#State:红绿圆_0" y="200.68" zvalue="1187"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549587148802" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,229.399,214.429) scale(0.958333,0.916667) translate(9.34886,18.2435)" width="30" x="215.02" y="200.68"/></g>
  <g id="1159">
   <use height="30" transform="rotate(0,118.899,283.179) scale(0.958333,0.916667) translate(4.54451,24.4935)" width="30" x="104.52" xlink:href="#State:红绿圆_0" y="269.43" zvalue="1190"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549587148802" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,118.899,283.179) scale(0.958333,0.916667) translate(4.54451,24.4935)" width="30" x="104.52" y="269.43"/></g>
  <g id="1157">
   <use height="30" transform="rotate(0,206.399,283.179) scale(0.958333,0.916667) translate(8.34886,24.4935)" width="30" x="192.02" xlink:href="#State:红绿圆_0" y="269.43" zvalue="1192"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549587148802" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,206.399,283.179) scale(0.958333,0.916667) translate(8.34886,24.4935)" width="30" x="192.02" y="269.43"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="68" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1062.75,307.822) scale(1,1) translate(-2.26875e-13,0)" writing-mode="lr" x="1062.75" xml:space="preserve" y="314.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126454362118" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="75" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1062.75,327.822) scale(1,1) translate(-2.26875e-13,0)" writing-mode="lr" x="1062.75" xml:space="preserve" y="334.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126454427654" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="94" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1062.75,694.707) scale(1,1) translate(-2.26875e-13,0)" writing-mode="lr" x="1062.75" xml:space="preserve" y="701.1900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126454493190" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="95" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1062.75,714.707) scale(1,1) translate(-2.26875e-13,0)" writing-mode="lr" x="1062.75" xml:space="preserve" y="721.1900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126454558726" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="108" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1062.75,347.822) scale(1,1) translate(-2.26875e-13,0)" writing-mode="lr" x="1062.75" xml:space="preserve" y="354.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126454624262" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="109" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,858.504,565.765) scale(1,1) translate(0,0)" writing-mode="lr" x="858.5" xml:space="preserve" y="570.74" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126454820870" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="110" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,858.504,593.765) scale(1,1) translate(1.83077e-13,0)" writing-mode="lr" x="858.5" xml:space="preserve" y="598.74" zvalue="1">档位:ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126454886406" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="115" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1062.75,734.707) scale(1,1) translate(-2.26875e-13,0)" writing-mode="lr" x="1062.75" xml:space="preserve" y="741.1900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126454951940" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="117" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,284.972,688.59) scale(1,1) translate(0,0)" writing-mode="lr" x="284.97" xml:space="preserve" y="695.0700000000001" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126456393732" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="118" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,284.972,708.59) scale(1,1) translate(0,0)" writing-mode="lr" x="284.97" xml:space="preserve" y="715.0700000000001" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126456459268" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="120" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,284.972,728.59) scale(1,1) translate(0,0)" writing-mode="lr" x="284.97" xml:space="preserve" y="735.0700000000001" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126456524804" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="121" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,284.972,668.59) scale(1,1) translate(0,0)" writing-mode="lr" x="284.97" xml:space="preserve" y="675.0700000000001" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126456655876" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="129" prefix="F:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,284.972,767.09) scale(1,1) translate(0,0)" writing-mode="lr" x="284.97" xml:space="preserve" y="773.5700000000001" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126456786948" ObjectName="F"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="132" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,284.972,747.09) scale(1,1) translate(0,0)" writing-mode="lr" x="284.97" xml:space="preserve" y="753.5700000000001" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126456852484" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="150" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,552.625,1122.62) scale(1,1) translate(0,0)" writing-mode="lr" x="552.63" xml:space="preserve" y="1129.1" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126457442308" ObjectName="P"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="162" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,816.128,1122.62) scale(1,1) translate(0,0)" writing-mode="lr" x="816.13" xml:space="preserve" y="1129.1" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126458359812" ObjectName="P"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="163" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1080.12,1122.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.13" xml:space="preserve" y="1129.1" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126459801604" ObjectName="P"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="164" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,552.625,1142.62) scale(1,1) translate(0,0)" writing-mode="lr" x="552.63" xml:space="preserve" y="1149.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126457507844" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="165" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,816.128,1142.62) scale(1,1) translate(0,0)" writing-mode="lr" x="816.13" xml:space="preserve" y="1149.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126458425348" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="166" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1080.12,1142.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.13" xml:space="preserve" y="1149.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126459867140" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="167" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,552.625,1162.62) scale(1,1) translate(0,0)" writing-mode="lr" x="552.63" xml:space="preserve" y="1169.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126457573380" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="168" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,816.128,1162.62) scale(1,1) translate(0,0)" writing-mode="lr" x="816.13" xml:space="preserve" y="1169.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126458490884" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="169" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1080.12,1162.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.13" xml:space="preserve" y="1169.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126459932676" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="170">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,552.625,1181.62) scale(1,1) translate(0,0)" writing-mode="lr" x="552.63" xml:space="preserve" y="1188.1" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126457966596" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="171" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,816.128,1181.62) scale(1,1) translate(0,0)" writing-mode="lr" x="816.13" xml:space="preserve" y="1188.1" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126458884100" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.12,1182.12) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.13" xml:space="preserve" y="1188.6" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126460325895" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="173" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1351.92,1122.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.92" xml:space="preserve" y="1129.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126463995910" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="174">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="174" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1351.92,1142.63) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.92" xml:space="preserve" y="1149.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126464061444" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,804.795,405.724) scale(1,1) translate(1.69263e-13,0)" writing-mode="lr" x="804.79" xml:space="preserve" y="412.2" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135768797187" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,804.795,423.724) scale(1,1) translate(1.69263e-13,0)" writing-mode="lr" x="804.79" xml:space="preserve" y="430.2" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135768862723" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="87" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,804.795,441.724) scale(1,1) translate(1.69263e-13,0)" writing-mode="lr" x="804.79" xml:space="preserve" y="448.2" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135768928259" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="88" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,804.795,385.878) scale(1,1) translate(0,0)" writing-mode="lr" x="804.79" xml:space="preserve" y="392.36" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135769059331" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="89" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,804.795,460.34) scale(1,1) translate(1.69263e-13,0)" writing-mode="lr" x="804.79" xml:space="preserve" y="466.82" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135769255939" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
</svg>