<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549602942978" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带负荷1_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.49999999999999"/>
   <rect fill-opacity="0" height="9.5" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.04,13.75) scale(1,1) translate(0,0)" width="6.08" x="4" y="9"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="7.041666666666663" y1="1.166666666666664" y2="26.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.49999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="8.916666666666666" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
   <rect fill-opacity="0" height="9.5" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.04,13.75) scale(1,1) translate(0,0)" width="6.08" x="4" y="9"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.49999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="Accessory:中间电缆_0" viewBox="0,0,8,21">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="10.5"/>
   <path d="M 1.08333 0.5 L 7 0.5 L 4 7.13889 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4" x2="4" y1="20.83333333333333" y2="0.5000000000000036"/>
   <path d="M 1.08333 20.6389 L 7 20.6389 L 4 14 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:带熔断器带避雷器_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="12.25"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="37.5"/>
   <rect fill-opacity="0" height="9.5" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.04,25) scale(1,1) translate(0,0)" width="6.08" x="12" y="20.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="12.16666666666667" y2="16.97701149425287"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="20.5" y1="12.16666666666667" y2="16.97701149425287"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="37.81537356321841" y2="33.0050287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666666" x2="20.5" y1="37.81537356321841" y2="33.0050287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666666" x2="15.04166666666666" y1="12.41666666666666" y2="37.58333333333334"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.91666666666667" x2="26.91666666666667" y1="5.166666666666664" y2="6.166666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.91666666666667" x2="28.91666666666667" y1="5.166666666666664" y2="5.166666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.91666666666667" x2="27.91666666666667" y1="4.416666666666664" y2="4.416666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="26.89166666666667" y1="19.14166666666667" y2="19.14166666666667"/>
   <rect fill-opacity="0" height="8" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,26.92,10.17) scale(1,1) translate(0,0)" width="4" x="24.92" y="6.17"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.91666666666667" x2="26.91666666666667" y1="19.16666666666666" y2="11.16666666666666"/>
   <path d="M 25.9167 11.1667 L 27.9167 11.1667 L 26.9167 8.16667 L 25.9167 11.1667 L 25.9167 11.1667 z" fill="rgb(85,255,0)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.41666666666667" x2="27.41666666666667" y1="3.416666666666664" y2="3.416666666666664"/>
  </symbol>
  <symbol id="Disconnector:带熔断器带避雷器_1" viewBox="0,0,30,50">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="12.25"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="37.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.05" x2="15.05" y1="20.91666666666666" y2="30.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="12.91666666666667" y2="17.72701149425287"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="15.92313218390803" y2="20.73347701149424"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666666" x2="20.5" y1="15.92313218390803" y2="20.73347701149424"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="20.5" y1="12.91666666666667" y2="17.72701149425287"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="35.75933908045977" y2="30.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="38.56537356321841" y2="33.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666666" x2="20.5" y1="35.75933908045977" y2="30.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666666" x2="20.5" y1="38.56537356321841" y2="33.7550287356322"/>
   <rect fill-opacity="0" height="9.5" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.04,25.75) scale(1,1) translate(0,0)" width="6.08" x="12" y="21"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.41666666666667" x2="27.41666666666667" y1="3.416666666666664" y2="3.416666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.91666666666667" x2="27.91666666666667" y1="4.416666666666664" y2="4.416666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="26.89166666666667" y1="19.14166666666667" y2="19.14166666666667"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.91666666666667" x2="26.91666666666667" y1="5.166666666666664" y2="6.166666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.91666666666667" x2="28.91666666666667" y1="5.166666666666664" y2="5.166666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.91666666666667" x2="26.91666666666667" y1="19.16666666666666" y2="11.16666666666666"/>
   <rect fill-opacity="0" height="8" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,26.92,10.17) scale(1,1) translate(0,0)" width="4" x="24.92" y="6.17"/>
   <path d="M 25.9167 11.1667 L 27.9167 11.1667 L 26.9167 8.16667 L 25.9167 11.1667 L 25.9167 11.1667 z" fill="rgb(85,255,0)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:带熔断器带避雷器_2" viewBox="0,0,30,50">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="12.25"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="37.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.33333333333333" x2="11.33333333333333" y1="21.16666666666667" y2="30.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.66666666666667" x2="18.83333333333334" y1="21.12459389287362" y2="30.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="12.91666666666667" y2="17.72701149425287"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="15.92313218390803" y2="20.73347701149424"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666666" x2="20.5" y1="15.92313218390803" y2="20.73347701149424"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="20.5" y1="12.91666666666667" y2="17.72701149425287"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="35.75933908045977" y2="30.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666667" x2="9.583333333333334" y1="38.56537356321841" y2="33.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666666" x2="20.5" y1="35.75933908045977" y2="30.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.04166666666666" x2="20.5" y1="38.56537356321841" y2="33.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.41666666666667" x2="27.41666666666667" y1="3.416666666666664" y2="3.416666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.91666666666667" x2="27.91666666666667" y1="4.416666666666664" y2="4.416666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="26.89166666666667" y1="19.14166666666667" y2="19.14166666666667"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.91666666666667" x2="26.91666666666667" y1="5.166666666666664" y2="6.166666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.91666666666667" x2="28.91666666666667" y1="5.166666666666664" y2="5.166666666666664"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.91666666666667" x2="26.91666666666667" y1="19.16666666666666" y2="11.16666666666666"/>
   <rect fill-opacity="0" height="8" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,26.92,10.17) scale(1,1) translate(0,0)" width="4" x="24.92" y="6.17"/>
   <path d="M 25.9167 11.1667 L 27.9167 11.1667 L 26.9167 8.16667 L 25.9167 11.1667 L 25.9167 11.1667 z" fill="rgb(85,255,0)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.01340482573727" x2="10.01340482573727" y1="11.16666666666667" y2="12.83842547222383"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6934763181412" x2="10.01340482573727" y1="14.51018427778098" y2="12.83842547222382"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.333333333333332" x2="10.01340482573726" y1="14.51018427778098" y2="12.83842547222382"/>
  </symbol>
  <symbol id="Compensator:高家村电容_0" viewBox="0,0,40,55">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="1.539999009704552"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.79999983978271" x2="9.799999610900864" y1="17.71999962692259" y2="17.71999962692259"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.799999610900864" x2="9.799999610900864" y1="49.00000082015995" y2="53.30000098419193"/>
   <path d="M 10.4 4.5 L 10.4 1.58 L 29.6 1.58 L 29.6 5.18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.809999611282329" x2="9.809999611282329" y1="17.65222184655929" y2="23.32999984092712"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.809999611282329" x2="4.999999427795387" y1="36.03000032539369" y2="36.03000032539369"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.729086516243374" x2="3.729086516243374" y1="52.93000097007755" y2="50.53000087852482"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.89999942398069" x2="9.809999611282334" y1="17.72999962730407" y2="17.72999962730407"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.009999428176855" x2="5.009999428176855" y1="20.42999973030089" y2="17.78555518497889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.809999611282329" x2="9.809999611282329" y1="23.42999984474182" y2="26.59999996566772"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.965554982036986" x2="4.965554982036986" y1="33.36333355700176" y2="36.05222254846362"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5099992565154672" x2="0.5099992565154672" y1="20.1299997188568" y2="34.23000025672913"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.90999984397887" x2="15.90999984397887" y1="50.53000087852482" y2="52.93000097007755"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.8999998435974" x2="3.799999382019021" y1="53.13000097770694" y2="53.13000097770694"/>
   <path d="M 5.01 24.7486 A 3.5567 2.18096 180 0 1 5.01 20.3867" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5.01 29.1105 A 3.5567 2.18096 180 0 1 5.01 24.7486" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5.01 33.3525 A 3.5567 2.18096 180 0 1 5.01 28.9905" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.60999967994688" x2="8.009999542617777" y1="26.72999997062682" y2="26.72999997062682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.60999967994688" x2="8.009999542617777" y1="27.93000001640319" y2="27.93000001640319"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.799999610900864" x2="9.799999610900864" y1="12.79999943923948" y2="17.79999962997434"/>
   <path d="M 4.12 43.2 A 5.9 5.7 -450 1 1 9.82 49.1" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 4.2 43.22 L 9.8 43.12 L 9.8 28" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.00153415229021" x2="15.03706756194727" y1="17.70257562391618" y2="18.53839655132261"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.00153415229021" x2="15.03706756194727" y1="17.70257562391614" y2="16.86675469650972"/>
   <rect fill-opacity="0" height="5.14" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(-90,15.36,17.71) scale(1,1) translate(0,0)" width="2.8" x="13.96" y="15.14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.1050297156598" x2="19.1050297156598" y1="18.72413453519066" y2="16.58814772070757"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.99335420676512" x2="19.99335420676512" y1="18.10113838096646" y2="17.12214442432838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.93046733297608" x2="19.0878272413876" y1="17.70257562391614" y2="17.70257562391614"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.54919196121243" x2="19.54919196121243" y1="18.36813673277681" y2="16.94414552312142"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.66000059738161" x2="29.66000036849977" y1="18.31999964981078" y2="18.31999964981078"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.66000036849977" x2="29.66000036849977" y1="49.60000084304813" y2="53.90000100708012"/>
   <rect fill-opacity="0" height="8.4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,9.8,8.72) scale(1,1) translate(0,0)" width="4" x="7.8" y="4.52"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.37000001411437" x2="20.37000001411437" y1="20.72999974174499" y2="34.83000027961732"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.67000036888123" x2="29.67000036888123" y1="24.02999986763" y2="27.19999998855591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.67000036888123" x2="29.67000036888123" y1="18.25222186944747" y2="23.9299998638153"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.7600001815796" x2="29.67000036888124" y1="18.32999965019225" y2="18.32999965019225"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.77000060157778" x2="35.77000060157778" y1="51.130000901413" y2="53.53000099296574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.67000036888123" x2="24.86000018539429" y1="36.63000034828187" y2="36.63000034828187"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.87000018577576" x2="24.87000018577576" y1="21.02999975318907" y2="18.38555520786708"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.58908727384228" x2="23.58908727384228" y1="53.53000099296574" y2="51.130000901413"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.76000060119631" x2="23.66000013961792" y1="53.73000100059512" y2="53.73000100059512"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.82555573963589" x2="24.82555573963589" y1="33.96333357988994" y2="36.6522225713518"/>
   <path d="M 24.87 25.3486 A 3.5567 2.18096 180 0 1 24.87 20.9867" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 24.87 29.7105 A 3.5567 2.18096 180 0 1 24.87 25.3486" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 24.87 33.9525 A 3.5567 2.18096 180 0 1 24.87 29.5905" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.47000043754579" x2="27.87000030021668" y1="27.32999999351501" y2="27.32999999351501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.47000043754579" x2="27.87000030021668" y1="28.53000003929138" y2="28.53000003929138"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.66000036849977" x2="29.66000036849977" y1="13.39999946212766" y2="18.39999965286253"/>
   <path d="M 23.98 43.8 A 5.9 5.7 -450 1 1 29.68 49.7" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 24.06 43.82 L 29.66 43.72 L 29.66 28.6" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="39.40919271881133" x2="39.40919271881133" y1="18.968136755665" y2="17.5441455460096"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="38.96503047325871" x2="38.96503047325871" y1="19.32413455807884" y2="17.18814774359576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.79046809057499" x2="38.94782799898651" y1="18.30257564680433" y2="18.30257564680433"/>
   <rect fill-opacity="0" height="5.14" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(-90,35.22,18.31) scale(1,1) translate(0,0)" width="2.8" x="33.82" y="15.74"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.86153490988912" x2="34.89706831954619" y1="18.30257564680432" y2="17.4667547193979"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="39.85335496436403" x2="39.85335496436403" y1="18.70113840385465" y2="17.72214444721656"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.86153490988912" x2="34.89706831954619" y1="18.30257564680437" y2="19.13839657421079"/>
   <rect fill-opacity="0" height="8.4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,29.66,9.32) scale(1,1) translate(0,0)" width="4" x="27.66" y="5.12"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV石房河变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,163.119,53.125) scale(1,1) translate(0,0)" writing-mode="lr" x="163.12" xml:space="preserve" y="70.13" zvalue="4073">     石房河变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="306" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,889.367,1096.64) scale(1,1) translate(0,0)" writing-mode="lr" x="889.37" xml:space="preserve" y="1099.64" zvalue="4052">10kV#1出线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,691.373,1097.01) scale(1,1) translate(0,0)" writing-mode="lr" x="691.37" xml:space="preserve" y="1100.01" zvalue="4075">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,250.464,785.371) scale(1,1) translate(0,0)" writing-mode="lr" x="250.46" xml:space="preserve" y="788.37" zvalue="4085">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.082,496.381) scale(1,1) translate(0,0)" writing-mode="lr" x="866.08" xml:space="preserve" y="499.38" zvalue="4092">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,862.33,740.308) scale(1,1) translate(0,0)" writing-mode="lr" x="862.33" xml:space="preserve" y="743.3099999999999" zvalue="4101">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.664,869.642) scale(1,1) translate(0,0)" writing-mode="lr" x="722.66" xml:space="preserve" y="872.64" zvalue="4113">073</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,659.278,948.184) scale(1,1) translate(0,0)" writing-mode="lr" x="659.28" xml:space="preserve" y="951.1799999999999" zvalue="4117">07367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921.967,871.888) scale(1,1) translate(0,0)" writing-mode="lr" x="921.97" xml:space="preserve" y="874.89" zvalue="4121">074</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,859.765,951.286) scale(1,1) translate(0,0)" writing-mode="lr" x="859.76" xml:space="preserve" y="954.29" zvalue="4124">07467</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1394.04,249.939) scale(1,1) translate(0,0)" writing-mode="lr" x="1394.04" xml:space="preserve" y="252.94" zvalue="4134">10kV外接电源</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="505" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,986.956,606.384) scale(1,1) translate(0,0)" writing-mode="lr" x="986.96" xml:space="preserve" y="609.38" zvalue="4135">12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,263.616,1016.08) scale(1,1) translate(0,0)" writing-mode="lr" x="263.62" xml:space="preserve" y="1022.08" zvalue="4141">电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,986.956,577.071) scale(1,1) translate(0,0)" writing-mode="lr" x="986.96" xml:space="preserve" y="580.0700000000001" zvalue="4154">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.819,996.07) scale(1,1) translate(0,0)" writing-mode="lr" x="721.8200000000001" xml:space="preserve" y="1000.07" zvalue="4161">0738</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="267" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,657.749,1032.84) scale(1,1) translate(0,0)" writing-mode="lr" x="657.75" xml:space="preserve" y="1036.84" zvalue="4166">07387</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,862.582,438.639) scale(1,1) translate(0,0)" writing-mode="lr" x="862.58" xml:space="preserve" y="441.64" zvalue="4206">3716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,922.867,135.002) scale(1,1) translate(0,0)" writing-mode="lr" x="922.87" xml:space="preserve" y="138" zvalue="4208">3728</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,923.723,186.99) scale(1,1) translate(0,0)" writing-mode="lr" x="923.72" xml:space="preserve" y="189.99" zvalue="4210">372</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="342" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,263.939,995.213) scale(1,1) translate(0,0)" writing-mode="lr" x="263.94" xml:space="preserve" y="1001.21" zvalue="4428">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89.4821,157.929) scale(1,1) translate(0,0)" writing-mode="lr" x="89.48" xml:space="preserve" y="163.93" zvalue="4441">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="328" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.2321,192.929) scale(1,1) translate(0,0)" writing-mode="lr" x="93.23" xml:space="preserve" y="198.93" zvalue="4442">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="324" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.06,158.5) scale(1,1) translate(0,0)" writing-mode="lr" x="218.06" xml:space="preserve" y="164.5" zvalue="4446">石房河变-01-2023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.435,193.5) scale(1,1) translate(0,0)" writing-mode="lr" x="204.43" xml:space="preserve" y="199.5" zvalue="4447">2023-07-05</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="546" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,302.653,872.405) scale(1,1) translate(0,0)" writing-mode="lr" x="302.65" xml:space="preserve" y="876.4" zvalue="4469">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="551" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,496.335,872.881) scale(1,1) translate(0,0)" writing-mode="lr" x="496.34" xml:space="preserve" y="876.88" zvalue="4480">0721</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="560" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,467.371,1097.01) scale(1,1) translate(0,0)" writing-mode="lr" x="467.37" xml:space="preserve" y="1100.01" zvalue="4485">10kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="573" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,941.765,478.957) scale(1,1) translate(0,0)" writing-mode="lr" x="941.76" xml:space="preserve" y="482.96" zvalue="4501">37160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="578" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,942.765,418.184) scale(1,1) translate(0,0)" writing-mode="lr" x="942.76" xml:space="preserve" y="422.18" zvalue="4507">37167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,895.167,57.6656) scale(1,1) translate(0,0)" writing-mode="lr" x="895.17" xml:space="preserve" y="60.67" zvalue="4601">35kV永龙石线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,683.056,80.6656) scale(1,1) translate(0,0)" writing-mode="lr" x="683.0599999999999" xml:space="preserve" y="83.67" zvalue="4603">"110kV永平变"</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1099,80.6656) scale(1,1) translate(0,0)" writing-mode="lr" x="1099" xml:space="preserve" y="83.67" zvalue="4605">"35kV龙门变"</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,857.672,279.002) scale(1,1) translate(0,0)" writing-mode="lr" x="857.67" xml:space="preserve" y="282" zvalue="4621">3719</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.265,313.351) scale(1,1) translate(0,0)" writing-mode="lr" x="793.26" xml:space="preserve" y="317.35" zvalue="4626">37197</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,984.104,249.332) scale(1,1) translate(0,0)" writing-mode="lr" x="984.1" xml:space="preserve" y="252.33" zvalue="4629">站内</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,984.104,213.332) scale(1,1) translate(0,0)" writing-mode="lr" x="984.1" xml:space="preserve" y="216.33" zvalue="4631">站外</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.37,1096.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.37" xml:space="preserve" y="1099.64" zvalue="4639">10kV#2出线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125.97,871.888) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.97" xml:space="preserve" y="874.89" zvalue="4643">075</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063.76,951.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1063.76" xml:space="preserve" y="954.29" zvalue="4646">07567</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1394.22,464.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1394.22" xml:space="preserve" y="467.33" zvalue="4659">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1305.37,1096.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1305.37" xml:space="preserve" y="1099.64" zvalue="4671">10kV#3出线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1337.97,871.888) scale(1,1) translate(0,0)" writing-mode="lr" x="1337.97" xml:space="preserve" y="874.89" zvalue="4675">076</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1275.76,951.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1275.76" xml:space="preserve" y="954.29" zvalue="4678">07667</text>
 </g>
 <g id="EnergyConsumerClass">
  <g id="534">
   <use class="kv10" height="30" transform="rotate(180,896.749,1070.69) scale(1.25,1.23333) translate(-177.85,-199.063)" width="12" x="889.2493318080071" xlink:href="#EnergyConsumer:负荷_0" y="1052.191198175604" zvalue="4051"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948787203" ObjectName="10kV#1出线"/>
   <cge:TPSR_Ref TObjectID="6192452948787203"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,896.749,1070.69) scale(1.25,1.23333) translate(-177.85,-199.063)" width="12" x="889.2493318080071" y="1052.191198175604"/></g>
  <g id="558">
   <use class="kv10" height="30" transform="rotate(0,467.233,1033.01) scale(2.59743,2.59743) translate(-264.987,-611.342)" width="28" x="430.8691556886325" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="994.0452380952383" zvalue="4484"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948983811" ObjectName="10kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,467.233,1033.01) scale(2.59743,2.59743) translate(-264.987,-611.342)" width="28" x="430.8691556886325" y="994.0452380952383"/></g>
  <g id="51">
   <use class="kv10" height="30" transform="rotate(180,1100.75,1070.69) scale(1.25,1.23333) translate(-218.65,-199.063)" width="12" x="1093.249331808007" xlink:href="#EnergyConsumer:负荷_0" y="1052.191198175604" zvalue="4638"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950163459" ObjectName="10kV#2出线"/>
   <cge:TPSR_Ref TObjectID="6192452950163459"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1100.75,1070.69) scale(1.25,1.23333) translate(-218.65,-199.063)" width="12" x="1093.249331808007" y="1052.191198175604"/></g>
  <g id="55">
   <use class="kv10" height="30" transform="rotate(0,1393.98,380.556) scale(4.21272,4.18519) translate(-1030.96,-241.849)" width="20" x="1351.856725146199" xlink:href="#EnergyConsumer:站用变带熔断器_0" y="317.7777777777778" zvalue="4658"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950228995" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1393.98,380.556) scale(4.21272,4.18519) translate(-1030.96,-241.849)" width="20" x="1351.856725146199" y="317.7777777777778"/></g>
  <g id="71">
   <use class="kv10" height="30" transform="rotate(180,1312.75,1070.69) scale(1.25,1.23333) translate(-261.05,-199.063)" width="12" x="1305.249331808007" xlink:href="#EnergyConsumer:负荷_0" y="1052.191198175604" zvalue="4670"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950687747" ObjectName="10kV#3出线"/>
   <cge:TPSR_Ref TObjectID="6192452950687747"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1312.75,1070.69) scale(1.25,1.23333) translate(-261.05,-199.063)" width="12" x="1305.249331808007" y="1052.191198175604"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="645">
   <use class="kv10" height="55" transform="rotate(0,696.975,1057.05) scale(1.03854,1.02576) translate(-25.0943,-25.8337)" width="40" x="676.2043201355746" xlink:href="#Compensator:高家村电容_0" y="1028.838261231542" zvalue="4074"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948721667" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192452948721667"/></metadata>
  <rect fill="white" height="55" opacity="0" stroke="white" transform="rotate(0,696.975,1057.05) scale(1.03854,1.02576) translate(-25.0943,-25.8337)" width="40" x="676.2043201355746" y="1028.838261231542"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1181">
   <path class="kv10" d="M 218 805.71 L 1510.91 805.71" stroke-width="6" zvalue="4084"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674316058627" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674316058627"/></metadata>
  <path d="M 218 805.71 L 1510.91 805.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="1168">
   <use class="kv35" height="20" transform="rotate(0,897.048,496.881) scale(1.83111,1.88671) translate(-403,-224.655)" width="10" x="887.8923044119832" xlink:href="#Breaker:开关_0" y="478.0140784127371" zvalue="4091"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924844519427" ObjectName="#1主变35kV侧371"/>
   <cge:TPSR_Ref TObjectID="6473924844519427"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,897.048,496.881) scale(1.83111,1.88671) translate(-403,-224.655)" width="10" x="887.8923044119832" y="478.0140784127371"/></g>
  <g id="516">
   <use class="kv10" height="20" transform="rotate(180,896.881,740.783) scale(1.83111,-1.88671) translate(-402.924,-1124.55)" width="10" x="887.7258120435957" xlink:href="#Breaker:开关_0" y="721.9157267979214" zvalue="4100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924844453891" ObjectName="#1主变10kV侧001"/>
   <cge:TPSR_Ref TObjectID="6473924844453891"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,896.881,740.783) scale(1.83111,-1.88671) translate(-402.924,-1124.55)" width="10" x="887.7258120435957" y="721.9157267979214"/></g>
  <g id="1125">
   <use class="kv10" height="20" transform="rotate(180,697.216,870.116) scale(1.83111,-1.88671) translate(-312.299,-1322.43)" width="10" x="688.0599780448696" xlink:href="#Breaker:开关_0" y="851.2490574757117" zvalue="4112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924844388355" ObjectName="10kV1号电容器073"/>
   <cge:TPSR_Ref TObjectID="6473924844388355"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,697.216,870.116) scale(1.83111,-1.88671) translate(-312.299,-1322.43)" width="10" x="688.0599780448696" y="851.2490574757117"/></g>
  <g id="1100">
   <use class="kv10" height="20" transform="rotate(180,896.519,872.363) scale(1.83111,-1.88671) translate(-402.76,-1325.87)" width="10" x="887.3631280248122" xlink:href="#Breaker:开关_0" y="853.4958107224649" zvalue="4120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924844322819" ObjectName="10kV#1出线074"/>
   <cge:TPSR_Ref TObjectID="6473924844322819"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,896.519,872.363) scale(1.83111,-1.88671) translate(-402.76,-1325.87)" width="10" x="887.3631280248122" y="853.4958107224649"/></g>
  <g id="465">
   <use class="kv35" height="20" transform="rotate(0,896.835,187.49) scale(1.83111,1.88671) translate(-402.903,-79.2485)" width="10" x="887.6796177649423" xlink:href="#Breaker:开关_0" y="168.6225351579686" zvalue="4209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924844257283" ObjectName="35kV永龙石线372"/>
   <cge:TPSR_Ref TObjectID="6473924844257283"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,896.835,187.49) scale(1.83111,1.88671) translate(-402.903,-79.2485)" width="10" x="887.6796177649423" y="168.6225351579686"/></g>
  <g id="48">
   <use class="kv10" height="20" transform="rotate(180,1100.52,872.363) scale(1.83111,-1.88671) translate(-495.352,-1325.87)" width="10" x="1091.363128024812" xlink:href="#Breaker:开关_0" y="853.4958107224649" zvalue="4642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924844584963" ObjectName="10kV#2出线075"/>
   <cge:TPSR_Ref TObjectID="6473924844584963"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1100.52,872.363) scale(1.83111,-1.88671) translate(-495.352,-1325.87)" width="10" x="1091.363128024812" y="853.4958107224649"/></g>
  <g id="68">
   <use class="kv10" height="20" transform="rotate(180,1312.52,872.363) scale(1.83111,-1.88671) translate(-591.575,-1325.87)" width="10" x="1303.363128024812" xlink:href="#Breaker:开关_0" y="853.4958107224649" zvalue="4674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924844650499" ObjectName="10kV#3出线076"/>
   <cge:TPSR_Ref TObjectID="6473924844650499"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1312.52,872.363) scale(1.83111,-1.88671) translate(-591.575,-1325.87)" width="10" x="1303.363128024812" y="853.4958107224649"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="1136">
   <use class="kv10" height="22" transform="rotate(180,896.709,777.633) scale(-0.971818,0.971818) translate(-1819.73,22.2406)" width="22" x="886.0189422576549" xlink:href="#DollyBreaker:手车_0" y="766.9426422119141" zvalue="4099"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948656131" ObjectName="#1主变10kV侧001手车2"/>
   <cge:TPSR_Ref TObjectID="6192452948656131"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,896.709,777.633) scale(-0.971818,0.971818) translate(-1819.73,22.2406)" width="22" x="886.0189422576549" y="766.9426422119141"/></g>
  <g id="515">
   <use class="kv10" height="22" transform="rotate(0,896.892,702.299) scale(0.971818,0.971818) translate(25.699,20.056)" width="22" x="886.2020534054953" xlink:href="#DollyBreaker:手车_0" y="691.6093237740653" zvalue="4102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948590595" ObjectName="#1主变10kV侧001手车1"/>
   <cge:TPSR_Ref TObjectID="6192452948590595"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,896.892,702.299) scale(0.971818,0.971818) translate(25.699,20.056)" width="22" x="886.2020534054953" y="691.6093237740653"/></g>
  <g id="1126">
   <use class="kv10" height="22" transform="rotate(180,697.043,908.537) scale(-0.971818,0.971818) translate(-1414.61,26.0367)" width="22" x="686.353109893799" xlink:href="#DollyBreaker:手车_0" y="897.8474169037486" zvalue="4111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948459523" ObjectName="10kV1号电容器073手车2"/>
   <cge:TPSR_Ref TObjectID="6192452948459523"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,697.043,908.537) scale(-0.971818,0.971818) translate(-1414.61,26.0367)" width="22" x="686.353109893799" y="897.8474169037486"/></g>
  <g id="1124">
   <use class="kv10" height="22" transform="rotate(0,697.226,834.037) scale(0.971818,0.971818) translate(19.9089,23.8763)" width="22" x="686.5362194067693" xlink:href="#DollyBreaker:手车_0" y="823.3474215719657" zvalue="4114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948393987" ObjectName="10kV1号电容器073手车1"/>
   <cge:TPSR_Ref TObjectID="6192452948393987"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,697.226,834.037) scale(0.971818,0.971818) translate(19.9089,23.8763)" width="22" x="686.5362194067693" y="823.3474215719657"/></g>
  <g id="1101">
   <use class="kv10" height="22" transform="rotate(180,896.736,910.784) scale(-0.971818,0.971818) translate(-1819.79,26.1019)" width="22" x="886.0461105606499" xlink:href="#DollyBreaker:手车_0" y="900.0941696736644" zvalue="4119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948066307" ObjectName="10kV#1出线074手车2"/>
   <cge:TPSR_Ref TObjectID="6192452948066307"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,896.736,910.784) scale(-0.971818,0.971818) translate(-1819.79,26.1019)" width="22" x="886.0461105606499" y="900.0941696736644"/></g>
  <g id="1099">
   <use class="kv10" height="22" transform="rotate(0,896.709,835.451) scale(0.971818,0.971818) translate(25.6937,23.9173)" width="22" x="886.0189422576547" xlink:href="#DollyBreaker:手车_0" y="824.7608413661761" zvalue="4122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948000771" ObjectName="10kV#1出线074手车1"/>
   <cge:TPSR_Ref TObjectID="6192452948000771"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,896.709,835.451) scale(0.971818,0.971818) translate(25.6937,23.9173)" width="22" x="886.0189422576547" y="824.7608413661761"/></g>
  <g id="49">
   <use class="kv10" height="22" transform="rotate(180,1100.74,910.784) scale(-0.971818,0.971818) translate(-2233.7,26.1019)" width="22" x="1090.04611056065" xlink:href="#DollyBreaker:手车_0" y="900.0941696736644" zvalue="4641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950032387" ObjectName="10kV#2出线075手车2"/>
   <cge:TPSR_Ref TObjectID="6192452950032387"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1100.74,910.784) scale(-0.971818,0.971818) translate(-2233.7,26.1019)" width="22" x="1090.04611056065" y="900.0941696736644"/></g>
  <g id="47">
   <use class="kv10" height="22" transform="rotate(0,1100.71,835.451) scale(0.971818,0.971818) translate(31.6095,23.9173)" width="22" x="1090.018942257655" xlink:href="#DollyBreaker:手车_0" y="824.7608413661761" zvalue="4644"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949966851" ObjectName="10kV#2出线075手车1"/>
   <cge:TPSR_Ref TObjectID="6192452949966851"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1100.71,835.451) scale(0.971818,0.971818) translate(31.6095,23.9173)" width="22" x="1090.018942257655" y="824.7608413661761"/></g>
  <g id="69">
   <use class="kv10" height="22" transform="rotate(180,1312.74,910.784) scale(-0.971818,0.971818) translate(-2663.85,26.1019)" width="22" x="1302.04611056065" xlink:href="#DollyBreaker:手车_0" y="900.0941696736644" zvalue="4673"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950556675" ObjectName="10kV#3出线076手车2"/>
   <cge:TPSR_Ref TObjectID="6192452950556675"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1312.74,910.784) scale(-0.971818,0.971818) translate(-2663.85,26.1019)" width="22" x="1302.04611056065" y="900.0941696736644"/></g>
  <g id="67">
   <use class="kv10" height="22" transform="rotate(0,1312.71,835.451) scale(0.971818,0.971818) translate(37.7573,23.9173)" width="22" x="1302.018942257655" xlink:href="#DollyBreaker:手车_0" y="824.7608413661761" zvalue="4676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950491139" ObjectName="10kV#3出线076手车1"/>
   <cge:TPSR_Ref TObjectID="6192452950491139"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1312.71,835.451) scale(0.971818,0.971818) translate(37.7573,23.9173)" width="22" x="1302.018942257655" y="824.7608413661761"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="1128">
   <use class="kv10" height="21" transform="rotate(0,896.905,662.514) scale(1.1825,0.82619) translate(-137.693,137.551)" width="8" x="892.1752746528525" xlink:href="#Accessory:中间电缆_0" y="653.8389739990234" zvalue="4109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948525059" ObjectName="#1主变10kV侧中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,896.905,662.514) scale(1.1825,0.82619) translate(-137.693,137.551)" width="8" x="892.1752746528525" y="653.8389739990234"/></g>
  <g id="1123">
   <use class="kv10" height="26" transform="rotate(0,721.743,962.026) scale(-1.0017,1.0017) translate(-1442.25,-1.61258)" width="12" x="715.7329330131518" xlink:href="#Accessory:避雷器1_0" y="949.0039847237724" zvalue="4115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948328451" ObjectName="10kV1号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,721.743,962.026) scale(-1.0017,1.0017) translate(-1442.25,-1.61258)" width="12" x="715.7329330131518" y="949.0039847237724"/></g>
  <g id="1103">
   <use class="kv10" height="21" transform="rotate(0,896.736,969.636) scale(1.1825,0.82619) translate(-137.667,202.162)" width="8" x="892.0061105361776" xlink:href="#Accessory:中间电缆_0" y="960.9606683904472" zvalue="4118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948131843" ObjectName="10kV#1出线中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,896.736,969.636) scale(1.1825,0.82619) translate(-137.667,202.162)" width="8" x="892.0061105361776" y="960.9606683904472"/></g>
  <g id="1093">
   <use class="kv10" height="26" transform="rotate(180,922.889,969.273) scale(1.0017,-1.0017) translate(-1.55799,-1936.88)" width="12" x="916.87890267364" xlink:href="#Accessory:避雷器1_0" y="956.2507379705256" zvalue="4125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452947804163" ObjectName="10kV#1出线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,922.889,969.273) scale(1.0017,-1.0017) translate(-1.55799,-1936.88)" width="12" x="916.87890267364" y="956.2507379705256"/></g>
  <g id="939">
   <use class="kv10" height="42" transform="rotate(0,281.439,954.522) scale(1.46705,-1.44781) translate(-82.5935,-1604.4)" width="30" x="259.4337038885677" xlink:href="#Accessory:4卷PT带容断器_0" y="924.1176652840875" zvalue="4140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452947738627" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,281.439,954.522) scale(1.46705,-1.44781) translate(-82.5935,-1604.4)" width="30" x="259.4337038885677" y="924.1176652840875"/></g>
  <g id="1260">
   <use class="kv10" height="26" transform="rotate(270,931.636,680.365) scale(-1.0017,1.0017) translate(-1861.68,-1.13397)" width="12" x="925.625790156009" xlink:href="#Accessory:避雷器1_0" y="667.3426765179689" zvalue="4155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452947673091" ObjectName="#1主变10kV侧避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,931.636,680.365) scale(-1.0017,1.0017) translate(-1861.68,-1.13397)" width="12" x="925.625790156009" y="667.3426765179689"/></g>
  <g id="1285">
   <use class="kv10" height="22" transform="rotate(0,697.056,957.413) scale(0.788333,0.788636) translate(185.889,254.273)" width="12" x="692.3263311411561" xlink:href="#Accessory:传输线_0" y="948.7383117675781" zvalue="4158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452947607555" ObjectName="10kV1号电容器中间电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,697.056,957.413) scale(0.788333,0.788636) translate(185.889,254.273)" width="12" x="692.3263311411561" y="948.7383117675781"/></g>
  <g id="559">
   <use class="kv10" height="21" transform="rotate(0,467.472,969.413) scale(1.1825,0.82619) translate(-71.4168,202.115)" width="8" x="462.7418627584151" xlink:href="#Accessory:中间电缆_0" y="960.7383117675781" zvalue="4486"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949049347" ObjectName="10kV1号站用变中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,467.472,969.413) scale(1.1825,0.82619) translate(-71.4168,202.115)" width="8" x="462.7418627584151" y="960.7383117675781"/></g>
  <g id="564">
   <use class="kv10" height="26" transform="rotate(270,931.636,644.365) scale(-1.0017,1.0017) translate(-1861.68,-1.0728)" width="12" x="925.625790156009" xlink:href="#Accessory:避雷器1_0" y="631.3426765179689" zvalue="4491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949114883" ObjectName="#1主变10kV侧避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,931.636,644.365) scale(-1.0017,1.0017) translate(-1861.68,-1.0728)" width="12" x="925.625790156009" y="631.3426765179689"/></g>
  <g id="604">
   <use class="kv35" height="26" transform="rotate(270,927.869,100.956) scale(1.0017,1.0017) translate(-1.56645,-0.14942)" width="12" x="921.8584622972367" xlink:href="#Accessory:避雷器1_0" y="87.93396043670748" zvalue="4525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949442563" ObjectName="35kV永龙石线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,927.869,100.956) scale(1.0017,1.0017) translate(-1.56645,-0.14942)" width="12" x="921.8584622972367" y="87.93396043670748"/></g>
  <g id="16">
   <use class="kv35" height="42" transform="rotate(0,849.298,343.698) scale(1.80873,-1.80873) translate(-367.612,-516.736)" width="30" x="822.1666666666666" xlink:href="#Accessory:4卷PT带容断器_0" y="305.7142857142857" zvalue="4613"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949508099" ObjectName="35kV母线PT"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,849.298,343.698) scale(1.80873,-1.80873) translate(-367.612,-516.736)" width="30" x="822.1666666666666" y="305.7142857142857"/></g>
  <g id="50">
   <use class="kv10" height="21" transform="rotate(0,1100.74,969.636) scale(1.1825,0.82619) translate(-169.151,202.162)" width="8" x="1096.006110536178" xlink:href="#Accessory:中间电缆_0" y="960.9606683904472" zvalue="4640"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950097923" ObjectName="10kV#2出线中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1100.74,969.636) scale(1.1825,0.82619) translate(-169.151,202.162)" width="8" x="1096.006110536178" y="960.9606683904472"/></g>
  <g id="45">
   <use class="kv10" height="26" transform="rotate(180,1126.89,969.273) scale(1.0017,-1.0017) translate(-1.90463,-1936.88)" width="12" x="1120.87890267364" xlink:href="#Accessory:避雷器1_0" y="956.2507379705256" zvalue="4647"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949770243" ObjectName="10kV#2出线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1126.89,969.273) scale(1.0017,-1.0017) translate(-1.90463,-1936.88)" width="12" x="1120.87890267364" y="956.2507379705256"/></g>
  <g id="70">
   <use class="kv10" height="21" transform="rotate(0,1312.74,969.636) scale(1.1825,0.82619) translate(-201.87,202.162)" width="8" x="1308.006110536178" xlink:href="#Accessory:中间电缆_0" y="960.9606683904472" zvalue="4672"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950622211" ObjectName="10kV#3出线中间电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1312.74,969.636) scale(1.1825,0.82619) translate(-201.87,202.162)" width="8" x="1308.006110536178" y="960.9606683904472"/></g>
  <g id="65">
   <use class="kv10" height="26" transform="rotate(180,1338.89,969.273) scale(1.0017,-1.0017) translate(-2.26487,-1936.88)" width="12" x="1332.87890267364" xlink:href="#Accessory:避雷器1_0" y="956.2507379705256" zvalue="4679"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950294531" ObjectName="10kV#3出线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1338.89,969.273) scale(1.0017,-1.0017) translate(-2.26487,-1936.88)" width="12" x="1332.87890267364" y="956.2507379705256"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="1122">
   <use class="kv10" height="30" transform="rotate(90,659.278,934.103) scale(1.01422,0.867474) translate(-9.16033,140.717)" width="12" x="653.1931027295411" xlink:href="#GroundDisconnector:地刀12_0" y="921.0904286700361" zvalue="4116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948262915" ObjectName="10kV1号电容器07367"/>
   <cge:TPSR_Ref TObjectID="6192452948262915"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,659.278,934.103) scale(1.01422,0.867474) translate(-9.16033,140.717)" width="12" x="653.1931027295411" y="921.0904286700361"/></g>
  <g id="1097">
   <use class="kv10" height="30" transform="rotate(90,859.765,937.205) scale(1.01422,0.867474) translate(-11.9719,141.191)" width="12" x="853.6793567007296" xlink:href="#GroundDisconnector:地刀12_0" y="924.1927116956913" zvalue="4123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452947935235" ObjectName="10kV#1出线07467"/>
   <cge:TPSR_Ref TObjectID="6192452947935235"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,859.765,937.205) scale(1.01422,0.867474) translate(-11.9719,141.191)" width="12" x="853.6793567007296" y="924.1927116956913"/></g>
  <g id="489">
   <use class="kv10" height="30" transform="rotate(90,659.278,1018.16) scale(1.01422,0.867474) translate(-9.16033,153.559)" width="12" x="653.1931025521177" xlink:href="#GroundDisconnector:地刀12_0" y="1005.150356250553" zvalue="4165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452947476483" ObjectName="10kV1号电容器07387"/>
   <cge:TPSR_Ref TObjectID="6192452947476483"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,659.278,1018.16) scale(1.01422,0.867474) translate(-9.16033,153.559)" width="12" x="653.1931025521177" y="1005.150356250553"/></g>
  <g id="571">
   <use class="kv35" height="30" transform="rotate(270,940.765,463.871) scale(-1.01422,0.867474) translate(-1868.25,68.8788)" width="12" x="934.6793740016158" xlink:href="#GroundDisconnector:地刀12_0" y="450.8593783623584" zvalue="4500"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949245955" ObjectName="#1主变35kV侧37160"/>
   <cge:TPSR_Ref TObjectID="6192452949245955"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,940.765,463.871) scale(-1.01422,0.867474) translate(-1868.25,68.8788)" width="12" x="934.6793740016158" y="450.8593783623584"/></g>
  <g id="576">
   <use class="kv35" height="30" transform="rotate(270,941.765,403.871) scale(-1.01422,0.867474) translate(-1870.24,59.7125)" width="12" x="935.679374105286" xlink:href="#GroundDisconnector:地刀12_0" y="390.8593783623584" zvalue="4506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949377027" ObjectName="#1主变35kV侧37167"/>
   <cge:TPSR_Ref TObjectID="6192452949377027"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,941.765,403.871) scale(-1.01422,0.867474) translate(-1870.24,59.7125)" width="12" x="935.679374105286" y="390.8593783623584"/></g>
  <g id="27">
   <use class="kv35" height="30" transform="rotate(90,794.265,299.038) scale(1.01422,0.867474) translate(-11.0534,43.6968)" width="12" x="788.1793640917056" xlink:href="#GroundDisconnector:地刀12_0" y="286.0260450290251" zvalue="4625"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949704707" ObjectName="35kV母线PT37197"/>
   <cge:TPSR_Ref TObjectID="6192452949704707"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,794.265,299.038) scale(1.01422,0.867474) translate(-11.0534,43.6968)" width="12" x="788.1793640917056" y="286.0260450290251"/></g>
  <g id="46">
   <use class="kv10" height="30" transform="rotate(90,1063.76,937.205) scale(1.01422,0.867474) translate(-14.8328,141.191)" width="12" x="1057.67935670073" xlink:href="#GroundDisconnector:地刀12_0" y="924.192669314249" zvalue="4645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949901315" ObjectName="10kV#2出线07567"/>
   <cge:TPSR_Ref TObjectID="6192452949901315"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1063.76,937.205) scale(1.01422,0.867474) translate(-14.8328,141.191)" width="12" x="1057.67935670073" y="924.192669314249"/></g>
  <g id="66">
   <use class="kv10" height="30" transform="rotate(90,1275.76,937.205) scale(1.01422,0.867474) translate(-17.8059,141.191)" width="12" x="1269.67935670073" xlink:href="#GroundDisconnector:地刀12_0" y="924.1926481241726" zvalue="4677"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452950425603" ObjectName="10kV#3出线07667"/>
   <cge:TPSR_Ref TObjectID="6192452950425603"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1275.76,937.205) scale(1.01422,0.867474) translate(-17.8059,141.191)" width="12" x="1269.67935670073" y="924.1926481241726"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="510">
   <path class="kv10" d="M 896.94 702.58 L 896.94 722.73" stroke-width="1" zvalue="4128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@1" LinkObjectIDznd="516@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.94 702.58 L 896.94 722.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="509">
   <path class="kv10" d="M 896.76 758.8 L 896.76 777.35" stroke-width="1" zvalue="4129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="516@1" LinkObjectIDznd="1136@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.76 758.8 L 896.76 777.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="504">
   <path class="kv10" d="M 896.76 835.73 L 896.76 854.31" stroke-width="1" zvalue="4136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1099@1" LinkObjectIDznd="1100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.76 835.73 L 896.76 854.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="503">
   <path class="kv10" d="M 896.79 910.5 L 896.79 890.38" stroke-width="1" zvalue="4137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1101@1" LinkObjectIDznd="1100@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.79 910.5 L 896.79 890.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="502">
   <path class="kv10" d="M 697.28 834.32 L 697.28 852.07" stroke-width="1" zvalue="4138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1124@1" LinkObjectIDznd="1125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.28 834.32 L 697.28 852.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="501">
   <path class="kv10" d="M 697.09 888.13 L 697.09 908.25" stroke-width="1" zvalue="4139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1125@1" LinkObjectIDznd="1126@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.09 888.13 L 697.09 908.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="kv10" d="M 896.91 662.51 L 896.91 692.59" stroke-width="1" zvalue="4150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1128@0" LinkObjectIDznd="515@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.91 662.51 L 896.91 692.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="438">
   <path class="kv10" d="M 897.14 630.69 L 897.14 662.51" stroke-width="1" zvalue="4274"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1258@1" LinkObjectIDznd="498" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.14 630.69 L 897.14 662.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="437">
   <path class="kv10" d="M 919.25 680.4 L 896.91 680.4" stroke-width="1" zvalue="4275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1260@0" LinkObjectIDznd="498" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.25 680.4 L 896.91 680.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="433">
   <path class="kv10" d="M 872.19 937.19 L 896.74 937.19" stroke-width="1" zvalue="4285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1097@0" LinkObjectIDznd="340" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.19 937.19 L 896.74 937.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="432">
   <path class="kv10" d="M 896.74 937.19 L 922.86 937.19 L 922.86 956.89" stroke-width="1" zvalue="4286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="433" LinkObjectIDznd="1093@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.74 937.19 L 922.86 937.19 L 922.86 956.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="340">
   <path class="kv10" d="M 896.75 920.49 L 896.74 969.64" stroke-width="1" zvalue="4430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1101@0" LinkObjectIDznd="1103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.75 920.49 L 896.74 969.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv10" d="M 896.75 920.49 L 896.75 1054.04" stroke-width="1" zvalue="4431"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340" LinkObjectIDznd="534@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.75 920.49 L 896.75 1054.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="549">
   <path class="kv10" d="M 267.47 887.12 L 267.47 925.02" stroke-width="1" zvalue="4473"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="543@1" LinkObjectIDznd="939@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 267.47 887.12 L 267.47 925.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="561">
   <path class="kv10" d="M 467.47 847.44 L 467.47 805.71" stroke-width="1" zvalue="4487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="555@0" LinkObjectIDznd="1181@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 467.47 847.44 L 467.47 805.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="562">
   <path class="kv10" d="M 467.47 900.84 L 467.47 969.41" stroke-width="1" zvalue="4488"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="555@1" LinkObjectIDznd="559@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 467.47 900.84 L 467.47 969.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="563">
   <path class="kv10" d="M 467.47 969.41 L 467.47 995.59" stroke-width="1" zvalue="4489"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="562" LinkObjectIDznd="558@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 467.47 969.41 L 467.47 995.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="565">
   <path class="kv10" d="M 919.25 644.4 L 897.14 644.4" stroke-width="1" zvalue="4492"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="564@0" LinkObjectIDznd="438" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.25 644.4 L 897.14 644.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="689">
   <path class="kv10" d="M 697.09 1009.08 L 697.09 1030.42" stroke-width="1" zvalue="4577"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@1" LinkObjectIDznd="645@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.09 1009.08 L 697.09 1030.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv35" d="M 789 81 L 1001 81" stroke-width="1" zvalue="4597"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 789 81 L 1001 81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 789 233 L 1001 233" stroke-width="1" zvalue="4607"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 789 233 L 1001 233" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 915.48 100.92 L 897.24 100.92" stroke-width="1" zvalue="4608"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="604@0" LinkObjectIDznd="14" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.48 100.92 L 897.24 100.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 897.24 121.44 L 897.24 81" stroke-width="1" zvalue="4611"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="466@0" LinkObjectIDznd="4" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.24 121.44 L 897.24 81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 897.27 145.26 L 897.27 169.44" stroke-width="1" zvalue="4612"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="466@1" LinkObjectIDznd="465@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.27 145.26 L 897.27 169.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 896.99 448.9 L 896.99 478.83" stroke-width="1" zvalue="4614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="467@1" LinkObjectIDznd="1168@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.99 448.9 L 896.99 478.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 928.34 463.86 L 896.99 463.86" stroke-width="1" zvalue="4615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="571@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 928.34 463.86 L 896.99 463.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 929.34 403.86 L 896.96 403.86" stroke-width="1" zvalue="4616"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="576@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.34 403.86 L 896.96 403.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv35" d="M 897.17 514.9 L 897.17 547.82" stroke-width="1" zvalue="4617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1168@1" LinkObjectIDznd="1258@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.17 514.9 L 897.17 547.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 896.96 205.51 L 896.96 425.07" stroke-width="1" zvalue="4618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="465@1" LinkObjectIDznd="467@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.96 205.51 L 896.96 425.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 832.08 289.26 L 832.08 306.85" stroke-width="1" zvalue="4622"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@1" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.08 289.26 L 832.08 306.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 832.05 265.44 L 832.05 248.33 L 896.96 248.33" stroke-width="1" zvalue="4623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.05 265.44 L 832.05 248.33 L 896.96 248.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 806.69 299.03 L 832.08 299.03" stroke-width="1" zvalue="4627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.69 299.03 L 832.08 299.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 267.47 856.82 L 267.47 805.71" stroke-width="1" zvalue="4632"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="543@0" LinkObjectIDznd="1181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 267.47 856.82 L 267.47 805.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 697.24 824.33 L 697.24 805.71" stroke-width="1" zvalue="4634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1124@0" LinkObjectIDznd="1181@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.24 824.33 L 697.24 805.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv10" d="M 896.72 787.34 L 896.72 805.71" stroke-width="1" zvalue="4635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1136@0" LinkObjectIDznd="1181@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.72 787.34 L 896.72 805.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv10" d="M 896.72 825.74 L 896.72 805.71" stroke-width="1" zvalue="4636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1099@0" LinkObjectIDznd="1181@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.72 825.74 L 896.72 805.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 1100.76 835.73 L 1100.76 854.31" stroke-width="1" zvalue="4648"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.76 835.73 L 1100.76 854.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv10" d="M 1100.79 910.5 L 1100.79 890.38" stroke-width="1" zvalue="4649"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.79 910.5 L 1100.79 890.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 1076.19 937.19 L 1100.74 937.19" stroke-width="1" zvalue="4650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="40" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.19 937.19 L 1100.74 937.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1100.74 937.19 L 1126.86 937.19 L 1126.86 956.89" stroke-width="1" zvalue="4651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.74 937.19 L 1126.86 937.19 L 1126.86 956.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 1100.75 920.49 L 1100.74 969.64" stroke-width="1" zvalue="4652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.75 920.49 L 1100.74 969.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 1100.75 920.49 L 1100.75 1054.04" stroke-width="1" zvalue="4653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40" LinkObjectIDznd="51@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.75 920.49 L 1100.75 1054.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 1100.72 825.74 L 1100.72 805.71" stroke-width="1" zvalue="4654"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="1181@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.72 825.74 L 1100.72 805.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1393.98 321.34 L 1393.98 269.56" stroke-width="1" zvalue="4659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 1393.98 321.34 L 1393.98 269.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv115" d="M 1344.44 269.56 L 1450 269.56" stroke-width="1" zvalue="4660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1344.44 269.56 L 1450 269.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 697.06 934.09 L 721.71 934.09 L 721.71 949.64" stroke-width="1" zvalue="4663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12" LinkObjectIDznd="1123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.06 934.09 L 721.71 934.09 L 721.71 949.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv10" d="M 671.7 1018.15 L 697.09 1018.15" stroke-width="1" zvalue="4664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@0" LinkObjectIDznd="689" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.7 1018.15 L 697.09 1018.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv10" d="M 697.06 918.25 L 697.06 985.25" stroke-width="1" zvalue="4665"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1126@0" LinkObjectIDznd="494@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.06 918.25 L 697.06 985.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 697.06 949.53 L 697.06 949.53" stroke-width="1" zvalue="4666"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1285@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.06 949.53 L 697.06 949.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 671.7 934.09 L 697.06 934.09" stroke-width="1" zvalue="4668"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1122@0" LinkObjectIDznd="3" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.7 934.09 L 697.06 934.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 1312.76 835.73 L 1312.76 854.31" stroke-width="1" zvalue="4680"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1312.76 835.73 L 1312.76 854.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 1312.79 910.5 L 1312.79 890.38" stroke-width="1" zvalue="4681"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1312.79 910.5 L 1312.79 890.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 1288.19 937.19 L 1312.74 937.19" stroke-width="1" zvalue="4682"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 1288.19 937.19 L 1312.74 937.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 1312.74 937.19 L 1338.86 937.19 L 1338.86 956.89" stroke-width="1" zvalue="4683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1312.74 937.19 L 1338.86 937.19 L 1338.86 956.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 1312.75 920.49 L 1312.74 969.64" stroke-width="1" zvalue="4684"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1312.75 920.49 L 1312.74 969.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 1312.75 920.49 L 1312.75 1054.04" stroke-width="1" zvalue="4685"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1312.75 920.49 L 1312.75 1054.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 1312.72 825.74 L 1312.72 805.71" stroke-width="1" zvalue="4686"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="1181@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1312.72 825.74 L 1312.72 805.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="1258">
   <g id="12580">
    <use class="kv35" height="30" transform="rotate(0,897.136,589.071) scale(3.09833,2.96282) translate(-582.402,-360.808)" width="24" x="859.96" xlink:href="#PowerTransformer2:可调不带中性点_0" y="544.63" zvalue="4153"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874500538371" ObjectName="35"/>
    </metadata>
   </g>
   <g id="12581">
    <use class="kv10" height="30" transform="rotate(0,897.136,589.071) scale(3.09833,2.96282) translate(-582.402,-360.808)" width="24" x="859.96" xlink:href="#PowerTransformer2:可调不带中性点_1" y="544.63" zvalue="4153"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874500603907" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399477559299" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399477559299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,897.136,589.071) scale(3.09833,2.96282) translate(-582.402,-360.808)" width="24" x="859.96" y="544.63"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="494">
   <use class="kv10" height="30" transform="rotate(0,697.154,997.07) scale(-1.11133,0.814667) translate(-1323.63,224.049)" width="15" x="688.8188807036794" xlink:href="#Disconnector:刀闸_0" y="984.8498404366635" zvalue="4159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452947542019" ObjectName="10kV1号电容器0738"/>
   <cge:TPSR_Ref TObjectID="6192452947542019"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,697.154,997.07) scale(-1.11133,0.814667) translate(-1323.63,224.049)" width="15" x="688.8188807036794" y="984.8498404366635"/></g>
  <g id="467">
   <use class="kv35" height="30" transform="rotate(0,897.055,436.889) scale(-1.11133,0.814667) translate(-1703.41,96.6104)" width="15" x="888.7197988180505" xlink:href="#Disconnector:刀闸_0" y="424.6688290371205" zvalue="4205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452947345411" ObjectName="#1主变35kV侧3716"/>
   <cge:TPSR_Ref TObjectID="6192452947345411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,897.055,436.889) scale(-1.11133,0.814667) translate(-1703.41,96.6104)" width="15" x="888.7197988180505" y="424.6688290371205"/></g>
  <g id="466">
   <use class="kv35" height="30" transform="rotate(0,897.34,133.252) scale(-1.11133,0.814667) translate(-1703.95,27.5344)" width="15" x="889.0052703530284" xlink:href="#Disconnector:刀闸_0" y="121.0324470497511" zvalue="4207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452947279875" ObjectName="35kV永龙石线3728"/>
   <cge:TPSR_Ref TObjectID="6192452947279875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,897.34,133.252) scale(-1.11133,0.814667) translate(-1703.95,27.5344)" width="15" x="889.0052703530284" y="121.0324470497511"/></g>
  <g id="543">
   <use class="kv10" height="50" transform="rotate(0,267.472,872.118) scale(0.921195,1.2) translate(21.6993,-140.353)" width="30" x="253.653941585054" xlink:href="#Disconnector:带熔断器带避雷器_0" y="842.1176470588235" zvalue="4468"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948852739" ObjectName="10kVⅠ母电压互感器0901手车"/>
   <cge:TPSR_Ref TObjectID="6192452948852739"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,267.472,872.118) scale(0.921195,1.2) translate(21.6993,-140.353)" width="30" x="253.653941585054" y="842.1176470588235"/></g>
  <g id="555">
   <use class="kv10" height="27" transform="rotate(0,467.472,873.881) scale(1.64286,2.07407) translate(-178.424,-438.046)" width="14" x="455.9718627929686" xlink:href="#Disconnector:带融断手车刀闸_0" y="845.8811060257287" zvalue="4479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452948918275" ObjectName="10kV1号站用变0721手车"/>
   <cge:TPSR_Ref TObjectID="6192452948918275"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,467.472,873.881) scale(1.64286,2.07407) translate(-178.424,-438.046)" width="14" x="455.9718627929686" y="845.8811060257287"/></g>
  <g id="23">
   <use class="kv35" height="30" transform="rotate(0,832.145,277.252) scale(-1.11133,0.814667) translate(-1580.09,60.2938)" width="15" x="823.8098997072412" xlink:href="#Disconnector:刀闸_0" y="265.0324470497511" zvalue="4620"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452949573635" ObjectName="35kV母线PT3719"/>
   <cge:TPSR_Ref TObjectID="6192452949573635"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,832.145,277.252) scale(-1.11133,0.814667) translate(-1580.09,60.2938)" width="15" x="823.8098997072412" y="265.0324470497511"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="72" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,789.816,478.379) scale(1,1) translate(0,0)" writing-mode="lr" x="789.8200000000001" xml:space="preserve" y="484.96" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136627908611" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="73" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,789.816,499.379) scale(1,1) translate(0,0)" writing-mode="lr" x="789.8200000000001" xml:space="preserve" y="505.96" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136627974147" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="74" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,789.816,712.264) scale(1,1) translate(0,0)" writing-mode="lr" x="789.8200000000001" xml:space="preserve" y="718.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136628039683" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="75" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,789.816,733.264) scale(1,1) translate(0,0)" writing-mode="lr" x="789.8200000000001" xml:space="preserve" y="739.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136628105219" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="76" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,789.816,520.379) scale(1,1) translate(0,0)" writing-mode="lr" x="789.8200000000001" xml:space="preserve" y="526.96" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136628170755" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="77" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.456,579.321) scale(1,1) translate(0,0)" writing-mode="lr" x="791.46" xml:space="preserve" y="584.4" zvalue="1">油温:dd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136628367363" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="78" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.456,604.321) scale(1,1) translate(0,0)" writing-mode="lr" x="791.46" xml:space="preserve" y="609.4" zvalue="1">档位:ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136628432899" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="79" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,789.816,754.264) scale(1,1) translate(0,0)" writing-mode="lr" x="789.8200000000001" xml:space="preserve" y="760.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136628498435" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,258,697.964) scale(1,1) translate(0,0)" writing-mode="lr" x="258" xml:space="preserve" y="704.65" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136629940227" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="81" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,258,719.964) scale(1,1) translate(0,0)" writing-mode="lr" x="258" xml:space="preserve" y="726.65" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136630005763" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="82" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,258,741.964) scale(1,1) translate(0,0)" writing-mode="lr" x="258" xml:space="preserve" y="748.65" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136630071299" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="83" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,258,676.714) scale(1,1) translate(0,0)" writing-mode="lr" x="258" xml:space="preserve" y="683.4" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136630202371" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="84" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,258,761.464) scale(1,1) translate(0,0)" writing-mode="lr" x="258" xml:space="preserve" y="768.15" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136630398979" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="89" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693,214.214) scale(1,1) translate(0,0)" writing-mode="lr" x="693" xml:space="preserve" y="220.9" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="281482481434627" ObjectName="35kV母线 A相电压"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="88" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693,236.214) scale(1,1) translate(0,0)" writing-mode="lr" x="693" xml:space="preserve" y="242.9" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="281482481500163" ObjectName="35kV母线 B相电压"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="87" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693,258.214) scale(1,1) translate(0,0)" writing-mode="lr" x="693" xml:space="preserve" y="264.9" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="281482481565699" ObjectName="35kV母线 C相电压"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693,192.964) scale(1,1) translate(0,0)" writing-mode="lr" x="693" xml:space="preserve" y="199.65" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="281482481631235" ObjectName="35kV母线 AB线电压"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693,277.714) scale(1,1) translate(0,0)" writing-mode="lr" x="693" xml:space="preserve" y="284.4" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="281482481827843" ObjectName="35kV母线 零序电压"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="90" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,697.475,1117.25) scale(1,1) translate(0,2.4586e-13)" writing-mode="lr" x="697.48" xml:space="preserve" y="1123.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136630464515" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="91" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,697.475,1138.25) scale(1,1) translate(0,0)" writing-mode="lr" x="697.48" xml:space="preserve" y="1144.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136630530051" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1315.5,1118.19) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.5" xml:space="preserve" y="1124.77" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136634200067" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="93" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1102,1118.19) scale(1,1) translate(0,0)" writing-mode="lr" x="1102" xml:space="preserve" y="1124.77" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136632692739" ObjectName="P"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="94" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,892.749,1118.19) scale(1,1) translate(0,0)" writing-mode="lr" x="892.75" xml:space="preserve" y="1124.77" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136631185411" ObjectName="P"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="95" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1315.5,1139.19) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.5" xml:space="preserve" y="1145.77" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136634265603" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="96" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1102,1139.19) scale(1,1) translate(0,0)" writing-mode="lr" x="1102" xml:space="preserve" y="1145.77" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136632758275" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="97" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,892.749,1139.19) scale(1,1) translate(0,0)" writing-mode="lr" x="892.75" xml:space="preserve" y="1145.77" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136631250947" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="99" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1315.5,1160.19) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.5" xml:space="preserve" y="1166.77" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136634331139" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1102,1160.19) scale(1,1) translate(0,0)" writing-mode="lr" x="1102" xml:space="preserve" y="1166.77" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136632823811" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="101" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,892.749,1160.19) scale(1,1) translate(0,0)" writing-mode="lr" x="892.75" xml:space="preserve" y="1166.77" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136631316483" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1315.5,1181.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.5" xml:space="preserve" y="1188.02" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136634724355" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1102,1182.94) scale(1,1) translate(0,0)" writing-mode="lr" x="1102" xml:space="preserve" y="1189.52" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136633217027" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.749,1181.69) scale(1,1) translate(0,0)" writing-mode="lr" x="892.75" xml:space="preserve" y="1188.27" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136631709699" ObjectName="Cos"/>
   </metadata>
  </g>
 </g>
</svg>