<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1节点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="9.062458524793254" xlink:href="#terminal" y="41.29040359122629"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,25.61,15.59) scale(1,1) translate(0,0)" width="11" x="20.11" y="13.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.61245852479332" x2="29.61245852479332" y1="19.34040359122622" y2="12.34040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666667" x2="25.8624585247933" y1="23.94225544307809" y2="23.94225544307809"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.86245852479331" x2="25.86245852479331" y1="23.93299618381883" y2="21.09040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.6124585247933" x2="21.6124585247933" y1="21.34040359122623" y2="19.34040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.61245852479333" x2="29.61245852479333" y1="12.34040359122622" y2="10.34040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.062458524793264" x2="9.062458524793264" y1="31.39040359122627" y2="25.89040359122627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.64772110866599" x2="25.64772110866599" y1="10.08674821859629" y2="3.405450010721147"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.71438777533266" x2="23.71438777533266" y1="3.016127685651949" y2="3.016127685651949"/>
   <ellipse cx="5.54" cy="23.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.55048569403981" x2="13.55048569403981" y1="35.78084700683308" y2="35.78084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.7608382776216" x2="23.48402243293775" y1="3.254208141873306" y2="3.254208141873306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.01083827762157" x2="24.4006890996044" y1="2.00420814187332" y2="2.00420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.51083827762159" x2="25.06735576627107" y1="0.5042081418732955" y2="0.5042081418732955"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="25.30654513693459" y2="22.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="22.79040359122621" y2="22.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="25.40299989806297" y2="22.78208583485582"/>
   <ellipse cx="5.54" cy="16.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="23.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.8644003886642" x2="13.8644003886642" y1="36.19213090500035" y2="36.19213090500035"/>
   <ellipse cx="12.54" cy="16.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="18.07076893362115" y2="18.07076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="24.32076893362116" y2="24.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="16.72611741162254" y2="17.85543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="18.24992879670395" y2="16.72611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="16.72611741162252" y2="14.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="17.57076893362115" y2="17.57076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="23.97611741162255" y2="21.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="25.49992879670398" y2="23.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="23.97611741162255" y2="25.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="16.72611741162253" y2="14.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="18.24992879670396" y2="16.72611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="16.72611741162254" y2="17.85543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.09103363843511" x2="9.09103363843511" y1="31.15822158129307" y2="41.41276827235689"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV鸡坪关变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="48" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,157.5,111.25) scale(1,1) translate(0,0)" writing-mode="lr" x="157.5" xml:space="preserve" y="128.25" zvalue="4690">鸡坪关变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="521" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.96,249.061) scale(1,1) translate(0,0)" writing-mode="lr" x="956.96" xml:space="preserve" y="252.56" zvalue="4329">3311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="520" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987.55,272.992) scale(1,1) translate(0,0)" writing-mode="lr" x="987.55" xml:space="preserve" y="276.49" zvalue="4331">33167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="515" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.094,155.338) scale(1,1) translate(0,0)" writing-mode="lr" x="936.09" xml:space="preserve" y="158.84" zvalue="4344">35kV干牛线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="500" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1005.15,488.84) scale(1,1) translate(0,0)" writing-mode="lr" x="1005.15" xml:space="preserve" y="492.34" zvalue="4390">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="499" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.5,747.214) scale(1,1) translate(0,0)" writing-mode="lr" x="630.5" xml:space="preserve" y="750.71" zvalue="4392">10kV#Ⅰ母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="488" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1043.32,471.698) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.32" xml:space="preserve" y="475.2" zvalue="4429">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="486" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,950.72,651.535) scale(1,1) translate(0,0)" writing-mode="lr" x="950.72" xml:space="preserve" y="655.03" zvalue="4439">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,842.4,795.377) scale(1,1) translate(0,0)" writing-mode="lr" x="842.4" xml:space="preserve" y="798.88" zvalue="4883">071</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,825.148,1003.67) scale(1,1) translate(0,0)" writing-mode="lr" x="825.15" xml:space="preserve" y="1007.17" zvalue="4890">10kV鸡坪关Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.396,790.921) scale(1,1) translate(0,0)" writing-mode="lr" x="725.4" xml:space="preserve" y="794.42" zvalue="4912">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1048.38,353.181) scale(1,1) translate(0,0)" writing-mode="lr" x="1048.38" xml:space="preserve" y="356.68" zvalue="5020">3715</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.556,174) scale(1,1) translate(0,0)" writing-mode="lr" x="937.5599999999999" xml:space="preserve" y="177.5" zvalue="5025">鸡坪关变支线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.712,353.181) scale(1,1) translate(0,0)" writing-mode="lr" x="851.71" xml:space="preserve" y="356.68" zvalue="5030">3719</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824.222,459.056) scale(1,1) translate(0,0)" writing-mode="lr" x="824.22" xml:space="preserve" y="462.56" zvalue="5034">35kV#Ⅰ母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.379,356.181) scale(1,1) translate(0,0)" writing-mode="lr" x="953.38" xml:space="preserve" y="359.68" zvalue="5044">371</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,692.029,883.687) scale(1,1) translate(0,0)" writing-mode="lr" x="692.03" xml:space="preserve" y="887.1900000000001" zvalue="5051">10kV#Ⅰ母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,856.641,845.719) scale(1,1) translate(0,0)" writing-mode="lr" x="856.64" xml:space="preserve" y="849.22" zvalue="5060">07167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,996.4,795.377) scale(1,1) translate(0,0)" writing-mode="lr" x="996.4" xml:space="preserve" y="798.88" zvalue="5066">072</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,979.148,1003.67) scale(1,1) translate(0,0)" writing-mode="lr" x="979.15" xml:space="preserve" y="1007.17" zvalue="5070">10kV鸡坪关Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.64,845.719) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.64" xml:space="preserve" y="849.22" zvalue="5078">07267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1126.4,795.377) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.4" xml:space="preserve" y="798.88" zvalue="5084">073</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109.15,1003.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1109.15" xml:space="preserve" y="1007.17" zvalue="5088">10kV备用间隔3</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140.64,845.719) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.64" xml:space="preserve" y="849.22" zvalue="5096">07367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252.4,795.377) scale(1,1) translate(0,0)" writing-mode="lr" x="1252.4" xml:space="preserve" y="798.88" zvalue="5102">074</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1235.15,1003.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1235.15" xml:space="preserve" y="1007.17" zvalue="5106">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1266.64,845.719) scale(1,1) translate(0,0)" writing-mode="lr" x="1266.64" xml:space="preserve" y="849.22" zvalue="5114">07467</text>
 </g>
 <g id="DisconnectorClass">
  <g id="811">
   <use class="kv35" height="30" transform="rotate(0,933.945,247.561) scale(-0.831307,0.609625) translate(-2058.68,152.671)" width="15" x="927.7098595035945" xlink:href="#Disconnector:刀闸_0" y="238.4168856300397" zvalue="4328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV干牛线3311"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,933.945,247.561) scale(-0.831307,0.609625) translate(-2058.68,152.671)" width="15" x="927.7098595035945" y="238.4168856300397"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="810">
   <use class="kv35" height="30" transform="rotate(270,952.891,273.289) scale(0.763595,0.763595) translate(293.592,81.063)" width="12" x="948.3090273044022" xlink:href="#GroundDisconnector:地刀12_0" y="261.8354401063137" zvalue="4330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV干牛线33167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,952.891,273.289) scale(0.763595,0.763595) translate(293.592,81.063)" width="12" x="948.3090273044022" y="261.8354401063137"/></g>
  <g id="67">
   <use class="kv10" height="30" transform="rotate(270,854.709,828.744) scale(0.763595,0.763595) translate(263.195,253.029)" width="12" x="850.1272091225841" xlink:href="#GroundDisconnector:地刀12_0" y="817.2899855608591" zvalue="5059"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅰ回线07167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,854.709,828.744) scale(0.763595,0.763595) translate(263.195,253.029)" width="12" x="850.1272091225841" y="817.2899855608591"/></g>
  <g id="73">
   <use class="kv10" height="30" transform="rotate(270,1008.71,828.744) scale(0.763595,0.763595) translate(310.873,253.029)" width="12" x="1004.127209122584" xlink:href="#GroundDisconnector:地刀12_0" y="817.2899855608591" zvalue="5077"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅱ回线07267"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1008.71,828.744) scale(0.763595,0.763595) translate(310.873,253.029)" width="12" x="1004.127209122584" y="817.2899855608591"/></g>
  <g id="90">
   <use class="kv10" height="30" transform="rotate(270,1138.71,828.744) scale(0.763595,0.763595) translate(351.12,253.029)" width="12" x="1134.127209122584" xlink:href="#GroundDisconnector:地刀12_0" y="817.2899855608591" zvalue="5095"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用间隔307367"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1138.71,828.744) scale(0.763595,0.763595) translate(351.12,253.029)" width="12" x="1134.127209122584" y="817.2899855608591"/></g>
  <g id="185">
   <use class="kv10" height="30" transform="rotate(270,1264.71,828.744) scale(0.763595,0.763595) translate(390.129,253.029)" width="12" x="1260.127209122584" xlink:href="#GroundDisconnector:地刀12_0" y="817.2899855608591" zvalue="5113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器07467"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1264.71,828.744) scale(0.763595,0.763595) translate(390.129,253.029)" width="12" x="1260.127209122584" y="817.2899855608591"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="808">
   <use class="kv35" height="26" transform="rotate(270,959.452,211.784) scale(1,0.879121) translate(0,27.5488)" width="12" x="953.452380952381" xlink:href="#Accessory:避雷器_0" y="200.3549630178125" zvalue="4334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV干牛线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,959.452,211.784) scale(1,0.879121) translate(0,27.5488)" width="12" x="953.452380952381" y="200.3549630178125"/></g>
  <g id="730">
   <use class="kv10" height="22" transform="rotate(0,933.467,580.694) scale(1,1) translate(0,0)" width="12" x="927.4674072265625" xlink:href="#Accessory:传输线_0" y="569.6944444444446" zvalue="4443"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,933.467,580.694) scale(1,1) translate(0,0)" width="12" x="927.4674072265625" y="569.6944444444446"/></g>
  <g id="52">
   <use class="kv35" height="22" transform="rotate(0,933.558,420.604) scale(1,1) translate(0,0)" width="12" x="927.5583163174716" xlink:href="#Accessory:传输线_0" y="409.6035353535355" zvalue="4748"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,933.558,420.604) scale(1,1) translate(0,0)" width="12" x="927.5583163174716" y="409.6035353535355"/></g>
  <g id="55">
   <use class="kv35" height="26" transform="rotate(90,909.511,394.874) scale(-0.634984,0.720391) translate(-2344.04,149.63)" width="12" x="905.7007319371447" xlink:href="#Accessory:避雷器_0" y="385.508775946276" zvalue="4752"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,909.511,394.874) scale(-0.634984,0.720391) translate(-2344.04,149.63)" width="12" x="905.7007319371447" y="385.508775946276"/></g>
  <g id="59">
   <use class="kv10" height="26" transform="rotate(90,905.288,603.985) scale(-0.634984,0.720391) translate(-2333.16,230.793)" width="12" x="901.4785097149224" xlink:href="#Accessory:避雷器_0" y="594.6198870573871" zvalue="4758"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,905.288,603.985) scale(-0.634984,0.720391) translate(-2333.16,230.793)" width="12" x="901.4785097149224" y="594.6198870573871"/></g>
  <g id="193">
   <use class="kv10" height="22" transform="rotate(0,827.511,858.537) scale(1,1) translate(0,0)" width="12" x="821.5108225108231" xlink:href="#Accessory:传输线_0" y="847.5369769119769" zvalue="4891"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅰ回线放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,827.511,858.537) scale(1,1) translate(0,0)" width="12" x="821.5108225108231" y="847.5369769119769"/></g>
  <g id="192">
   <use class="kv10" height="26" transform="rotate(0,806.29,849.383) scale(-0.811869,1) translate(-1800.55,0)" width="12" x="801.4190627589372" xlink:href="#Accessory:避雷器_0" y="836.3830479791856" zvalue="4892"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅰ回线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,806.29,849.383) scale(-0.811869,1) translate(-1800.55,0)" width="12" x="801.4190627589372" y="836.3830479791856"/></g>
  <g id="28">
   <use class="kv35" height="22" transform="rotate(0,933.467,297.513) scale(1,-1) translate(0,-595.025)" width="12" x="927.4674072265625" xlink:href="#Accessory:传输线_0" y="286.5126262626263" zvalue="5010"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV干牛线放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,933.467,297.513) scale(1,-1) translate(0,-595.025)" width="12" x="927.4674072265625" y="286.5126262626263"/></g>
  <g id="21">
   <use class="kv35" height="42" transform="rotate(0,825.889,421.778) scale(-1.11111,-1.11111) translate(-1567.52,-799.044)" width="30" x="809.2222222222223" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="398.4444444444445" zvalue="5033"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,825.889,421.778) scale(-1.11111,-1.11111) translate(-1567.52,-799.044)" width="30" x="809.2222222222223" y="398.4444444444445"/></g>
  <g id="22">
   <use class="kv35" height="26" transform="rotate(0,812.4,354.652) scale(0.634984,0.720391) translate(464.811,134.018)" width="12" x="808.5896208260336" xlink:href="#Accessory:避雷器_0" y="345.2865537240538" zvalue="5035"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,812.4,354.652) scale(0.634984,0.720391) translate(464.811,134.018)" width="12" x="808.5896208260336" y="345.2865537240538"/></g>
  <g id="38">
   <use class="kv10" height="42" transform="rotate(0,693.696,846.409) scale(-1.11111,-1.11111) translate(-1316.36,-1605.84)" width="30" x="677.0292397660819" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="823.0760233918129" zvalue="5050"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,693.696,846.409) scale(-1.11111,-1.11111) translate(-1316.36,-1605.84)" width="30" x="677.0292397660819" y="823.0760233918129"/></g>
  <g id="63">
   <use class="kv10" height="26" transform="rotate(90,677.92,811.353) scale(-0.634984,0.720391) translate(-1747.73,311.28)" width="12" x="674.1100886622909" xlink:href="#Accessory:避雷器_0" y="801.9883081100186" zvalue="5055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,677.92,811.353) scale(-0.634984,0.720391) translate(-1747.73,311.28)" width="12" x="674.1100886622909" y="801.9883081100186"/></g>
  <g id="79">
   <use class="kv10" height="22" transform="rotate(0,981.511,858.537) scale(1,1) translate(0,0)" width="12" x="975.5108225108231" xlink:href="#Accessory:传输线_0" y="847.5369769119769" zvalue="5071"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅱ回线放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,981.511,858.537) scale(1,1) translate(0,0)" width="12" x="975.5108225108231" y="847.5369769119769"/></g>
  <g id="78">
   <use class="kv10" height="26" transform="rotate(0,960.29,849.383) scale(-0.811869,1) translate(-2144.23,0)" width="12" x="955.4190627589371" xlink:href="#Accessory:避雷器_0" y="836.3830479791856" zvalue="5072"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅱ回线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,960.29,849.383) scale(-0.811869,1) translate(-2144.23,0)" width="12" x="955.4190627589371" y="836.3830479791856"/></g>
  <g id="96">
   <use class="kv10" height="22" transform="rotate(0,1111.51,858.537) scale(1,1) translate(0,0)" width="12" x="1105.510822510823" xlink:href="#Accessory:传输线_0" y="847.5369769119769" zvalue="5089"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用间隔3放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1111.51,858.537) scale(1,1) translate(0,0)" width="12" x="1105.510822510823" y="847.5369769119769"/></g>
  <g id="95">
   <use class="kv10" height="26" transform="rotate(0,1090.29,849.383) scale(-0.811869,1) translate(-2434.36,0)" width="12" x="1085.419062758937" xlink:href="#Accessory:避雷器_0" y="836.3830479791856" zvalue="5090"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用间隔3避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1090.29,849.383) scale(-0.811869,1) translate(-2434.36,0)" width="12" x="1085.419062758937" y="836.3830479791856"/></g>
  <g id="191">
   <use class="kv10" height="22" transform="rotate(0,1237.51,858.537) scale(1,1) translate(0,0)" width="12" x="1231.510822510823" xlink:href="#Accessory:传输线_0" y="847.5369769119769" zvalue="5107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1237.51,858.537) scale(1,1) translate(0,0)" width="12" x="1231.510822510823" y="847.5369769119769"/></g>
  <g id="190">
   <use class="kv10" height="26" transform="rotate(0,1216.29,849.383) scale(-0.811869,1) translate(-2715.56,0)" width="12" x="1211.419062758937" xlink:href="#Accessory:避雷器_0" y="836.3830479791856" zvalue="5108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1216.29,849.383) scale(-0.811869,1) translate(-2715.56,0)" width="12" x="1211.419062758937" y="836.3830479791856"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="802">
   <use class="kv35" height="30" transform="rotate(0,933.872,191.218) scale(2.06349,0.555556) translate(-477.581,146.308)" width="7" x="926.6494705853457" xlink:href="#ACLineSegment:线路_0" y="182.8845598845598" zvalue="4343"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV干牛线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,933.872,191.218) scale(2.06349,0.555556) translate(-477.581,146.308)" width="7" x="926.6494705853457" y="182.8845598845598"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="769">
   <g id="7690">
    <use class="kv35" height="30" transform="rotate(0,934.023,494.84) scale(3.02083,3.04032) translate(-600.579,-301.476)" width="24" x="897.77" xlink:href="#PowerTransformer2:可调不带中性点_0" y="449.23" zvalue="4389"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="7691">
    <use class="kv10" height="30" transform="rotate(0,934.023,494.84) scale(3.02083,3.04032) translate(-600.579,-301.476)" width="24" x="897.77" xlink:href="#PowerTransformer2:可调不带中性点_1" y="449.23" zvalue="4389"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,934.023,494.84) scale(3.02083,3.04032) translate(-600.579,-301.476)" width="24" x="897.77" y="449.23"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="768">
   <path class="kv10" d="M 686.89 751.96 L 1291 751.96" stroke-width="3" zvalue="4391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母线"/>
   </metadata>
  <path d="M 686.89 751.96 L 1291 751.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="742">
   <use class="kv35" height="38" transform="rotate(0,1035.26,429.175) scale(1.32048,1.59569) translate(-247.092,-148.898)" width="26" x="1018.096052096052" xlink:href="#EnergyConsumer:站用变1节点_0" y="398.8571428571429" zvalue="4428"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1035.26,429.175) scale(1.32048,1.59569) translate(-247.092,-148.898)" width="26" x="1018.096052096052" y="398.8571428571429"/></g>
  <g id="194">
   <use class="kv10" height="30" transform="rotate(180,827.023,961.021) scale(1.25,1.23333) translate(-163.905,-178.315)" width="12" x="819.5227272727277" xlink:href="#EnergyConsumer:负荷_0" y="942.5211038961037" zvalue="4889"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅰ回线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,827.023,961.021) scale(1.25,1.23333) translate(-163.905,-178.315)" width="12" x="819.5227272727277" y="942.5211038961037"/></g>
  <g id="80">
   <use class="kv10" height="30" transform="rotate(180,981.023,961.021) scale(1.25,1.23333) translate(-194.705,-178.315)" width="12" x="973.5227272727277" xlink:href="#EnergyConsumer:负荷_0" y="942.5211038961037" zvalue="5069"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅱ回线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,981.023,961.021) scale(1.25,1.23333) translate(-194.705,-178.315)" width="12" x="973.5227272727277" y="942.5211038961037"/></g>
  <g id="97">
   <use class="kv10" height="30" transform="rotate(180,1111.02,961.021) scale(1.25,1.23333) translate(-220.705,-178.315)" width="12" x="1103.522727272728" xlink:href="#EnergyConsumer:负荷_0" y="942.5211038961037" zvalue="5087"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用间隔3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1111.02,961.021) scale(1.25,1.23333) translate(-220.705,-178.315)" width="12" x="1103.522727272728" y="942.5211038961037"/></g>
  <g id="195">
   <use class="kv10" height="30" transform="rotate(180,1237.02,961.021) scale(1.25,1.23333) translate(-245.905,-178.315)" width="12" x="1229.522727272728" xlink:href="#EnergyConsumer:负荷_0" y="942.5211038961037" zvalue="5105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1237.02,961.021) scale(1.25,1.23333) translate(-245.905,-178.315)" width="12" x="1229.522727272728" y="942.5211038961037"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="736">
   <use class="kv10" height="22" transform="rotate(0,933.459,634.462) scale(0.610836,0.610836) translate(590.427,399.936)" width="22" x="926.7399041235698" xlink:href="#DollyBreaker:手车_0" y="627.7431944797563" zvalue="4436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧手车2"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,933.459,634.462) scale(0.610836,0.610836) translate(590.427,399.936)" width="22" x="926.7399041235698" y="627.7431944797563"/></g>
  <g id="735">
   <use class="kv10" height="22" transform="rotate(0,933.505,667.541) scale(0.610836,-0.610836) translate(590.457,-1764.66)" width="22" x="926.7858473228595" xlink:href="#DollyBreaker:手车_0" y="660.8222979684156" zvalue="4437"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,933.505,667.541) scale(0.610836,-0.610836) translate(590.457,-1764.66)" width="22" x="926.7858473228595" y="660.8222979684156"/></g>
  <g id="201">
   <use class="kv10" height="22" transform="rotate(0,827.186,778.305) scale(0.610836,0.610836) translate(522.721,491.578)" width="22" x="820.4672887943773" xlink:href="#DollyBreaker:手车_0" y="771.5857269472888" zvalue="4880"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅰ回线手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,827.186,778.305) scale(0.610836,0.610836) translate(522.721,491.578)" width="22" x="820.4672887943773" y="771.5857269472888"/></g>
  <g id="200">
   <use class="kv10" height="22" transform="rotate(0,827.876,811.384) scale(0.610836,-0.610836) translate(523.16,-2143.98)" width="22" x="821.1567927820065" xlink:href="#DollyBreaker:手车_0" y="804.664830435948" zvalue="4881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅰ回线手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,827.876,811.384) scale(0.610836,-0.610836) translate(523.16,-2143.98)" width="22" x="821.1567927820065" y="804.664830435948"/></g>
  <g id="207">
   <use class="kv10" height="22" transform="rotate(0,700.781,789.871) scale(0.610836,0.610836) translate(442.188,498.947)" width="22" x="694.0616943887828" xlink:href="#DollyBreaker:手车_0" y="783.1521605137224" zvalue="4911"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母线电压互感器手车0901"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,700.781,789.871) scale(0.610836,0.610836) translate(442.188,498.947)" width="22" x="694.0616943887828" y="783.1521605137224"/></g>
  <g id="11">
   <use class="kv35" height="22" transform="rotate(0,1028.54,334.859) scale(0.610836,0.610836) translate(651.003,209.058)" width="22" x="1021.820824147913" xlink:href="#DollyBreaker:手车_0" y="328.1400198765816" zvalue="5017"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1028.54,334.859) scale(0.610836,0.610836) translate(651.003,209.058)" width="22" x="1021.820824147913" y="328.1400198765816"/></g>
  <g id="10">
   <use class="kv35" height="22" transform="rotate(0,1029.23,367.938) scale(0.610836,-0.610836) translate(651.443,-974.571)" width="22" x="1022.510328135542" xlink:href="#DollyBreaker:手车_0" y="361.219123365241" zvalue="5018"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变手车2"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1029.23,367.938) scale(0.610836,-0.610836) translate(651.443,-974.571)" width="22" x="1022.510328135542" y="361.219123365241"/></g>
  <g id="20">
   <use class="kv35" height="22" transform="rotate(0,831.873,334.859) scale(0.610836,0.610836) translate(525.707,209.058)" width="22" x="825.1541574812459" xlink:href="#DollyBreaker:手车_0" y="328.1400198765816" zvalue="5027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,831.873,334.859) scale(0.610836,0.610836) translate(525.707,209.058)" width="22" x="825.1541574812459" y="328.1400198765816"/></g>
  <g id="19">
   <use class="kv35" height="22" transform="rotate(0,832.563,367.938) scale(0.610836,-0.610836) translate(526.146,-974.571)" width="22" x="825.8436614688752" xlink:href="#DollyBreaker:手车_0" y="361.219123365241" zvalue="5028"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器手车2"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,832.563,367.938) scale(0.610836,-0.610836) translate(526.146,-974.571)" width="22" x="825.8436614688752" y="361.219123365241"/></g>
  <g id="34">
   <use class="kv35" height="22" transform="rotate(0,933.54,337.859) scale(0.610836,0.610836) translate(590.479,210.97)" width="22" x="926.8208241479125" xlink:href="#DollyBreaker:手车_0" y="331.1400198765816" zvalue="5041"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,933.54,337.859) scale(0.610836,0.610836) translate(590.479,210.97)" width="22" x="926.8208241479125" y="331.1400198765816"/></g>
  <g id="33">
   <use class="kv35" height="22" transform="rotate(0,934.23,370.938) scale(0.610836,-0.610836) translate(590.918,-982.483)" width="22" x="927.5103281355417" xlink:href="#DollyBreaker:手车_0" y="364.219123365241" zvalue="5042"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧手车2"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,934.23,370.938) scale(0.610836,-0.610836) translate(590.918,-982.483)" width="22" x="927.5103281355417" y="364.219123365241"/></g>
  <g id="85">
   <use class="kv10" height="22" transform="rotate(0,981.186,778.305) scale(0.610836,0.610836) translate(620.834,491.578)" width="22" x="974.4672887943773" xlink:href="#DollyBreaker:手车_0" y="771.5857269472888" zvalue="5063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅱ回线手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,981.186,778.305) scale(0.610836,0.610836) translate(620.834,491.578)" width="22" x="974.4672887943773" y="771.5857269472888"/></g>
  <g id="84">
   <use class="kv10" height="22" transform="rotate(0,981.876,811.384) scale(0.610836,-0.610836) translate(621.274,-2143.98)" width="22" x="975.1567927820065" xlink:href="#DollyBreaker:手车_0" y="804.664830435948" zvalue="5064"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅱ回线手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,981.876,811.384) scale(0.610836,-0.610836) translate(621.274,-2143.98)" width="22" x="975.1567927820065" y="804.664830435948"/></g>
  <g id="137">
   <use class="kv10" height="22" transform="rotate(0,1111.19,778.305) scale(0.610836,0.610836) translate(703.658,491.578)" width="22" x="1104.467288794377" xlink:href="#DollyBreaker:手车_0" y="771.5857269472888" zvalue="5081"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用间隔3手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1111.19,778.305) scale(0.610836,0.610836) translate(703.658,491.578)" width="22" x="1104.467288794377" y="771.5857269472888"/></g>
  <g id="121">
   <use class="kv10" height="22" transform="rotate(0,1111.88,811.384) scale(0.610836,-0.610836) translate(704.097,-2143.98)" width="22" x="1105.156792782007" xlink:href="#DollyBreaker:手车_0" y="804.664830435948" zvalue="5082"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用间隔3手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1111.88,811.384) scale(0.610836,-0.610836) translate(704.097,-2143.98)" width="22" x="1105.156792782007" y="804.664830435948"/></g>
  <g id="303">
   <use class="kv10" height="22" transform="rotate(0,1237.19,778.305) scale(0.610836,0.610836) translate(783.932,491.578)" width="22" x="1230.467288794377" xlink:href="#DollyBreaker:手车_0" y="771.5857269472888" zvalue="5099"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1237.19,778.305) scale(0.610836,0.610836) translate(783.932,491.578)" width="22" x="1230.467288794377" y="771.5857269472888"/></g>
  <g id="302">
   <use class="kv10" height="22" transform="rotate(0,1237.88,811.384) scale(0.610836,-0.610836) translate(784.372,-2143.98)" width="22" x="1231.156792782007" xlink:href="#DollyBreaker:手车_0" y="804.664830435948" zvalue="5100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1237.88,811.384) scale(0.610836,-0.610836) translate(784.372,-2143.98)" width="22" x="1231.156792782007" y="804.664830435948"/></g>
 </g>
 <g id="BreakerClass">
  <g id="734">
   <use class="kv10" height="20" transform="rotate(0,933.506,650.868) scale(0.777778,0.7732) translate(265.605,188.649)" width="10" x="929.6171719275319" xlink:href="#Breaker:开关_0" y="643.1359398765048" zvalue="4438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,933.506,650.868) scale(0.777778,0.7732) translate(265.605,188.649)" width="10" x="929.6171719275319" y="643.1359398765048"/></g>
  <g id="199">
   <use class="kv10" height="20" transform="rotate(0,826.745,794.362) scale(0.777778,0.7732) translate(235.102,230.74)" width="10" x="822.8560606060611" xlink:href="#Breaker:开关_0" y="786.6303754915872" zvalue="4882"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅰ回线071"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,826.745,794.362) scale(0.777778,0.7732) translate(235.102,230.74)" width="10" x="822.8560606060611" y="786.6303754915872"/></g>
  <g id="9">
   <use class="kv35" height="20" transform="rotate(0,1028.72,352.167) scale(0.777778,0.7732) translate(292.81,101.032)" width="10" x="1024.834595959596" xlink:href="#Breaker:开关_0" y="344.43466842088" zvalue="5019"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变3714"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1028.72,352.167) scale(0.777778,0.7732) translate(292.81,101.032)" width="10" x="1024.834595959596" y="344.43466842088"/></g>
  <g id="18">
   <use class="kv35" height="20" transform="rotate(0,832.057,352.167) scale(0.777778,0.7732) translate(236.619,101.032)" width="10" x="828.1679292929297" xlink:href="#Breaker:开关_0" y="344.43466842088" zvalue="5029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器3719"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,832.057,352.167) scale(0.777778,0.7732) translate(236.619,101.032)" width="10" x="828.1679292929297" y="344.43466842088"/></g>
  <g id="32">
   <use class="kv35" height="20" transform="rotate(0,933.723,355.167) scale(0.777778,0.7732) translate(265.667,101.912)" width="10" x="929.8345959595963" xlink:href="#Breaker:开关_0" y="347.43466842088" zvalue="5043"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧371"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,933.723,355.167) scale(0.777778,0.7732) translate(265.667,101.912)" width="10" x="929.8345959595963" y="347.43466842088"/></g>
  <g id="83">
   <use class="kv10" height="20" transform="rotate(0,980.745,794.362) scale(0.777778,0.7732) translate(279.102,230.74)" width="10" x="976.8560606060611" xlink:href="#Breaker:开关_0" y="786.6303754915872" zvalue="5065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV鸡坪关Ⅱ回线072"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,980.745,794.362) scale(0.777778,0.7732) translate(279.102,230.74)" width="10" x="976.8560606060611" y="786.6303754915872"/></g>
  <g id="111">
   <use class="kv10" height="20" transform="rotate(0,1110.74,794.362) scale(0.777778,0.7732) translate(316.245,230.74)" width="10" x="1106.856060606061" xlink:href="#Breaker:开关_0" y="786.6303754915872" zvalue="5083"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV备用间隔3073"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1110.74,794.362) scale(0.777778,0.7732) translate(316.245,230.74)" width="10" x="1106.856060606061" y="786.6303754915872"/></g>
  <g id="301">
   <use class="kv10" height="20" transform="rotate(0,1236.74,794.362) scale(0.777778,0.7732) translate(352.245,230.74)" width="10" x="1232.856060606061" xlink:href="#Breaker:开关_0" y="786.6303754915872" zvalue="5101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器074"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1236.74,794.362) scale(0.777778,0.7732) translate(352.245,230.74)" width="10" x="1232.856060606061" y="786.6303754915872"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="733">
   <path class="kv10" d="M 933.54 667.36 L 933.56 658.25" stroke-width="1" zvalue="4440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="735@1" LinkObjectIDznd="734@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.54 667.36 L 933.56 658.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="732">
   <path class="kv10" d="M 933.48 643.47 L 933.49 634.64" stroke-width="1" zvalue="4441"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="734@0" LinkObjectIDznd="736@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.48 643.47 L 933.49 634.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="729">
   <path class="kv10" d="M 933.47 570.69 L 933.47 537.54" stroke-width="1" zvalue="4444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="730@0" LinkObjectIDznd="769@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.47 570.69 L 933.47 537.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="728">
   <path class="kv10" d="M 933.47 570.69 L 933.47 628.36" stroke-width="1" zvalue="4445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="729" LinkObjectIDznd="736@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.47 570.69 L 933.47 628.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="727">
   <path class="kv10" d="M 933.51 673.65 L 933.51 751.96" stroke-width="1" zvalue="4446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="735@0" LinkObjectIDznd="768@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.51 673.65 L 933.51 751.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv35" d="M 918.42 394.85 L 933.56 394.85" stroke-width="1" zvalue="4755"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="35" MaxPinNum="2"/>
   </metadata>
  <path d="M 918.42 394.85 L 933.56 394.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 914.2 603.96 L 933.47 603.96" stroke-width="1" zvalue="4761"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="728" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.2 603.96 L 933.47 603.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv10" d="M 827.91 811.21 L 827.91 801.75" stroke-width="1" zvalue="4884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@1" LinkObjectIDznd="199@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.91 811.21 L 827.91 801.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 826.72 786.97 L 826.72 778.48" stroke-width="1" zvalue="4885"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="201@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 826.72 786.97 L 826.72 778.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv10" d="M 806.26 837.02 L 806.26 829.31 L 827.51 829.31" stroke-width="1" zvalue="4902"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="203" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.26 837.02 L 806.26 829.31 L 827.51 829.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 827.19 772.2 L 827.19 751.96" stroke-width="1" zvalue="4903"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="768@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.19 772.2 L 827.19 751.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 827.51 848.54 L 827.51 817.49" stroke-width="1" zvalue="4906"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="200@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.51 848.54 L 827.51 817.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 827.51 848.54 L 827.51 944.37" stroke-width="1" zvalue="4907"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203" LinkObjectIDznd="194@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.51 848.54 L 827.51 944.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 700.79 783.77 L 700.79 751.96" stroke-width="1" zvalue="4912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="768@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.79 783.77 L 700.79 751.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 933.47 307.51 L 933.47 256.55" stroke-width="1" zvalue="5011"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="811@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.47 307.51 L 933.47 256.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv35" d="M 933.87 238.72 L 933.87 199.47" stroke-width="1" zvalue="5012"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@0" LinkObjectIDznd="802@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.87 238.72 L 933.87 199.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 941.95 273.3 L 933.47 273.3" stroke-width="1" zvalue="5013"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="810@0" LinkObjectIDznd="1" MaxPinNum="2"/>
   </metadata>
  <path d="M 941.95 273.3 L 933.47 273.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv35" d="M 948.58 211.75 L 933.87 211.75" stroke-width="1" zvalue="5014"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="808@0" LinkObjectIDznd="2" MaxPinNum="2"/>
   </metadata>
  <path d="M 948.58 211.75 L 933.87 211.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 933.47 307.33 L 933.55 331.76" stroke-width="1" zvalue="5015"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.47 307.33 L 933.55 331.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv35" d="M 1029.26 367.76 L 1029.26 359.55" stroke-width="1" zvalue="5021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@1" LinkObjectIDznd="9@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1029.26 367.76 L 1029.26 359.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv35" d="M 1028.7 344.77 L 1028.7 335.04" stroke-width="1" zvalue="5022"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="11@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1028.7 344.77 L 1028.7 335.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv35" d="M 933.5 318 L 1028.55 318 L 1028.55 328.76" stroke-width="1" zvalue="5023"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5" LinkObjectIDznd="11@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.5 318 L 1028.55 318 L 1028.55 328.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 1030.13 399.26 L 1030.13 374.04" stroke-width="1" zvalue="5024"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="742@0" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.13 399.26 L 1030.13 374.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 832.59 367.76 L 832.59 359.55" stroke-width="1" zvalue="5031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@1" LinkObjectIDznd="18@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.59 367.76 L 832.59 359.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 832.03 344.77 L 832.03 335.04" stroke-width="1" zvalue="5032"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="20@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.03 344.77 L 832.03 335.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 832.03 340.11 L 812.42 340.11 L 812.42 345.74" stroke-width="1" zvalue="5036"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.03 340.11 L 812.42 340.11 L 812.42 345.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 832.49 399.23 L 832.57 374.04" stroke-width="1" zvalue="5037"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="19@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.49 399.23 L 832.57 374.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 934.78 318 L 831.88 318 L 831.88 328.76" stroke-width="1" zvalue="5039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12" LinkObjectIDznd="20@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 934.78 318 L 831.88 318 L 831.88 328.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv35" d="M 934.26 370.76 L 934.26 362.55" stroke-width="1" zvalue="5045"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@1" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 934.26 370.76 L 934.26 362.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 933.7 347.77 L 933.7 338.04" stroke-width="1" zvalue="5046"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="34@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.7 347.77 L 933.7 338.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 933.56 410.6 L 933.56 377.04" stroke-width="1" zvalue="5047"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.56 410.6 L 933.56 377.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 933.56 410.6 L 933.56 452.51" stroke-width="1" zvalue="5048"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35" LinkObjectIDznd="769@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.56 410.6 L 933.56 452.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 686.83 811.33 L 700.29 811.33" stroke-width="1" zvalue="5056"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.83 811.33 L 700.29 811.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 700.29 823.86 L 700.29 790.05" stroke-width="1" zvalue="5057"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="207@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.29 823.86 L 700.29 790.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 825.45 829.31 L 843.77 829.31" stroke-width="1" zvalue="5061"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184" LinkObjectIDznd="67@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.45 829.31 L 843.77 829.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 981.91 811.21 L 981.91 801.75" stroke-width="1" zvalue="5067"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@1" LinkObjectIDznd="83@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.91 811.21 L 981.91 801.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 980.72 786.97 L 980.72 778.48" stroke-width="1" zvalue="5068"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="85@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 980.72 786.97 L 980.72 778.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 960.26 837.02 L 960.26 829.31 L 981.51 829.31" stroke-width="1" zvalue="5073"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="75" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.26 837.02 L 960.26 829.31 L 981.51 829.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 981.19 772.2 L 981.19 751.96" stroke-width="1" zvalue="5074"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="768@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.19 772.2 L 981.19 751.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 981.51 848.54 L 981.51 817.49" stroke-width="1" zvalue="5075"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.51 848.54 L 981.51 817.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 981.51 848.54 L 981.51 944.37" stroke-width="1" zvalue="5076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.51 848.54 L 981.51 944.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 979.45 829.31 L 997.77 829.31" stroke-width="1" zvalue="5079"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.45 829.31 L 997.77 829.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 1111.91 811.21 L 1111.91 801.75" stroke-width="1" zvalue="5085"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@1" LinkObjectIDznd="111@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.91 811.21 L 1111.91 801.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1110.72 786.97 L 1110.72 778.48" stroke-width="1" zvalue="5086"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1110.72 786.97 L 1110.72 778.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 1090.26 837.02 L 1090.26 829.31 L 1111.51 829.31" stroke-width="1" zvalue="5091"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 1090.26 837.02 L 1090.26 829.31 L 1111.51 829.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 1111.19 772.2 L 1111.19 751.96" stroke-width="1" zvalue="5092"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="768@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.19 772.2 L 1111.19 751.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 1111.51 848.54 L 1111.51 817.49" stroke-width="1" zvalue="5093"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.51 848.54 L 1111.51 817.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1111.51 848.54 L 1111.51 944.37" stroke-width="1" zvalue="5094"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.51 848.54 L 1111.51 944.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 1109.45 829.31 L 1127.77 829.31" stroke-width="1" zvalue="5097"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94" LinkObjectIDznd="90@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.45 829.31 L 1127.77 829.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 1237.91 811.21 L 1237.91 801.75" stroke-width="1" zvalue="5103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="302@1" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.91 811.21 L 1237.91 801.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 1236.72 786.97 L 1236.72 778.48" stroke-width="1" zvalue="5104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@0" LinkObjectIDznd="303@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1236.72 786.97 L 1236.72 778.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1216.26 837.02 L 1216.26 829.31 L 1237.51 829.31" stroke-width="1" zvalue="5109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="187" MaxPinNum="2"/>
   </metadata>
  <path d="M 1216.26 837.02 L 1216.26 829.31 L 1237.51 829.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1237.19 772.2 L 1237.19 751.96" stroke-width="1" zvalue="5110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@0" LinkObjectIDznd="768@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.19 772.2 L 1237.19 751.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1237.51 848.54 L 1237.51 817.49" stroke-width="1" zvalue="5111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="302@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.51 848.54 L 1237.51 817.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1237.51 848.54 L 1237.51 944.37" stroke-width="1" zvalue="5112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.51 848.54 L 1237.51 944.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 1235.45 829.31 L 1253.77 829.31" stroke-width="1" zvalue="5115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1235.45 829.31 L 1253.77 829.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
</svg>