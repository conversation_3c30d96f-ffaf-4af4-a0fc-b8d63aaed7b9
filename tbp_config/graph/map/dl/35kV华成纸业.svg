<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带负荷1_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV华成纸业" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,135.482,48.9464) scale(1,1) translate(0,0)" writing-mode="lr" x="135.48" xml:space="preserve" y="65.95" zvalue="3808">     华成纸业</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,679.175,431.698) scale(1,1) translate(0,0)" writing-mode="lr" x="679.1799999999999" xml:space="preserve" y="437.7" zvalue="2725">35kV#Ⅰ母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,785.757,499.638) scale(1,1) translate(0,0)" writing-mode="lr" x="785.76" xml:space="preserve" y="503.14" zvalue="3725">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,783.735,555.916) scale(1,1) translate(0,0)" writing-mode="lr" x="783.73" xml:space="preserve" y="559.42" zvalue="3727">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,873.939,368.365) scale(1,1) translate(0,0)" writing-mode="lr" x="873.9400000000001" xml:space="preserve" y="371.86" zvalue="3740">3416</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,852.591,297.205) scale(1,1) translate(0,0)" writing-mode="lr" x="852.59" xml:space="preserve" y="303.2" zvalue="3742">35kV新民T线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,771.477,673.682) scale(1,1) translate(0,0)" writing-mode="lr" x="771.48" xml:space="preserve" y="679.6799999999999" zvalue="3745">35kV1号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,947.757,499.638) scale(1,1) translate(0,0)" writing-mode="lr" x="947.76" xml:space="preserve" y="503.14" zvalue="3750">3021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.735,555.916) scale(1,1) translate(0,0)" writing-mode="lr" x="945.73" xml:space="preserve" y="559.42" zvalue="3752">302</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931.659,674.591) scale(1,1) translate(0,0)" writing-mode="lr" x="931.66" xml:space="preserve" y="680.59" zvalue="3754">35kV2号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.76,499.638) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.76" xml:space="preserve" y="503.14" zvalue="3760">3031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1095.73,555.916) scale(1,1) translate(0,0)" writing-mode="lr" x="1095.73" xml:space="preserve" y="559.42" zvalue="3762">303</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.75,675.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.75" xml:space="preserve" y="681.5" zvalue="3764">35kV3号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211.76,499.638) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.76" xml:space="preserve" y="503.14" zvalue="3770">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211.73,395.638) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.73" xml:space="preserve" y="399.14" zvalue="3773">3426</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="97">
   <path class="kv35" d="M 720 443.96 L 1261.43 443.96" stroke-width="3" zvalue="2724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243706884" ObjectName="35kV#Ⅰ母线"/>
   </metadata>
  <path d="M 720 443.96 L 1261.43 443.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="609">
   <use class="kv35" height="30" transform="rotate(0,763.367,499.388) scale(-1.11133,0.814667) translate(-1449.42,110.829)" width="15" x="755.0316603781955" xlink:href="#Disconnector:刀闸_0" y="487.1675592041016" zvalue="3724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号主变3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,763.367,499.388) scale(-1.11133,0.814667) translate(-1449.42,110.829)" width="15" x="755.0316603781955" y="487.1675592041016"/></g>
  <g id="32">
   <use class="kv35" height="30" transform="rotate(0,851.548,368.115) scale(-1.11133,0.814667) translate(-1616.95,80.9646)" width="15" x="843.2134785600138" xlink:href="#Disconnector:刀闸_0" y="355.8948347721254" zvalue="3739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV新民T线3416"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,851.548,368.115) scale(-1.11133,0.814667) translate(-1616.95,80.9646)" width="15" x="843.2134785600138" y="355.8948347721254"/></g>
  <g id="74">
   <use class="kv35" height="30" transform="rotate(0,925.367,499.388) scale(-1.11133,0.814667) translate(-1757.2,110.829)" width="15" x="917.0316603781955" xlink:href="#Disconnector:刀闸_0" y="487.1675591838297" zvalue="3749"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV2号主变3021"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,925.367,499.388) scale(-1.11133,0.814667) translate(-1757.2,110.829)" width="15" x="917.0316603781955" y="487.1675591838297"/></g>
  <g id="86">
   <use class="kv35" height="30" transform="rotate(0,1075.37,499.388) scale(-1.11133,0.814667) translate(-2042.17,110.829)" width="15" x="1067.031660378196" xlink:href="#Disconnector:刀闸_0" y="487.1675591838297" zvalue="3759"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV3号主变3031"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1075.37,499.388) scale(-1.11133,0.814667) translate(-2042.17,110.829)" width="15" x="1067.031660378196" y="487.1675591838297"/></g>
  <g id="94">
   <use class="kv35" height="30" transform="rotate(0,1189.37,499.388) scale(-1.11133,0.814667) translate(-2258.75,110.829)" width="15" x="1181.031660378196" xlink:href="#Disconnector:刀闸_0" y="487.1675591838297" zvalue="3769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3901"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1189.37,499.388) scale(-1.11133,0.814667) translate(-2258.75,110.829)" width="15" x="1181.031660378196" y="487.1675591838297"/></g>
  <g id="102">
   <use class="kv35" height="30" transform="rotate(0,1189.34,395.388) scale(-1.11133,0.814667) translate(-2258.69,87.1691)" width="15" x="1181.002084878995" xlink:href="#Disconnector:刀闸_0" y="383.1675620448526" zvalue="3772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3426"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1189.34,395.388) scale(-1.11133,0.814667) translate(-2258.69,87.1691)" width="15" x="1181.002084878995" y="383.1675620448526"/></g>
 </g>
 <g id="BreakerClass">
  <g id="581">
   <use class="kv35" height="20" transform="rotate(0,763.367,556.916) scale(1.828,1.8835) translate(-341.63,-252.4)" width="10" x="754.226660366134" xlink:href="#Breaker:开关_0" y="538.0813504165067" zvalue="3726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号主变301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,763.367,556.916) scale(1.828,1.8835) translate(-341.63,-252.4)" width="10" x="754.226660366134" y="538.0813504165067"/></g>
  <g id="73">
   <use class="kv35" height="20" transform="rotate(0,925.367,556.916) scale(1.828,1.8835) translate(-415.009,-252.4)" width="10" x="916.226660366134" xlink:href="#Breaker:开关_0" y="538.0813504165067" zvalue="3751"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV2号主变302"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,925.367,556.916) scale(1.828,1.8835) translate(-415.009,-252.4)" width="10" x="916.226660366134" y="538.0813504165067"/></g>
  <g id="85">
   <use class="kv35" height="20" transform="rotate(0,1075.37,556.916) scale(1.828,1.8835) translate(-482.952,-252.4)" width="10" x="1066.226660366134" xlink:href="#Breaker:开关_0" y="538.0813504165067" zvalue="3761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV3号主变303"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1075.37,556.916) scale(1.828,1.8835) translate(-482.952,-252.4)" width="10" x="1066.226660366134" y="538.0813504165067"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="33">
   <use class="kv35" height="30" transform="rotate(0,850.773,318.182) scale(2.06349,0.555556) translate(-434.753,247.879)" width="7" x="843.5505050505051" xlink:href="#ACLineSegment:线路_0" y="309.8484848484848" zvalue="3741"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV新民T线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,850.773,318.182) scale(2.06349,0.555556) translate(-434.753,247.879)" width="7" x="843.5505050505051" y="309.8484848484848"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="34">
   <path class="kv35" d="M 851.48 380.13 L 851.48 443.96" stroke-width="1" zvalue="3742"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.48 380.13 L 851.48 443.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 851.45 356.3 L 851.45 326.43" stroke-width="1" zvalue="3743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.45 356.3 L 851.45 326.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 764.85 584.8 L 764.85 574.9" stroke-width="1" zvalue="3745"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="581@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 764.85 584.8 L 764.85 574.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 763.31 538.9 L 763.3 511.4" stroke-width="1" zvalue="3746"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="581@0" LinkObjectIDznd="609@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.31 538.9 L 763.3 511.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 763.27 487.57 L 763.27 443.96" stroke-width="1" zvalue="3747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="609@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.27 487.57 L 763.27 443.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv35" d="M 926.85 584.8 L 926.85 574.9" stroke-width="1" zvalue="3755"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="73@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 926.85 584.8 L 926.85 574.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv35" d="M 925.31 538.9 L 925.3 511.4" stroke-width="1" zvalue="3756"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="74@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.31 538.9 L 925.3 511.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv35" d="M 925.27 487.57 L 925.27 443.96" stroke-width="1" zvalue="3757"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="97@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.27 487.57 L 925.27 443.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv35" d="M 1076.85 584.8 L 1076.85 574.9" stroke-width="1" zvalue="3765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="85@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.85 584.8 L 1076.85 574.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv35" d="M 1075.31 538.9 L 1075.3 511.4" stroke-width="1" zvalue="3766"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="86@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.31 538.9 L 1075.3 511.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv35" d="M 1075.27 487.57 L 1075.27 443.96" stroke-width="1" zvalue="3767"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="97@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.27 487.57 L 1075.27 443.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 1189.27 407.4 L 1189.27 443.96" stroke-width="1" zvalue="3775"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="97@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1189.27 407.4 L 1189.27 443.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 1189.27 487.57 L 1189.27 443.96" stroke-width="1" zvalue="3776"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="97@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1189.27 487.57 L 1189.27 443.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv35" d="M 1189.3 511.4 L 1189.3 602.04" stroke-width="1" zvalue="3777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1189.3 511.4 L 1189.3 602.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="38">
   <use class="kv35" height="30" transform="rotate(0,764.667,623) scale(1.94444,2.65242) translate(-358.187,-363.334)" width="28" x="737.4444444444445" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="583.2136752136753" zvalue="3744"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,764.667,623) scale(1.94444,2.65242) translate(-358.187,-363.334)" width="28" x="737.4444444444445" y="583.2136752136753"/></g>
  <g id="72">
   <use class="kv35" height="30" transform="rotate(0,926.667,623) scale(1.94444,2.65242) translate(-436.873,-363.334)" width="28" x="899.4444444444445" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="583.2136752136753" zvalue="3753"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV2号主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,926.667,623) scale(1.94444,2.65242) translate(-436.873,-363.334)" width="28" x="899.4444444444445" y="583.2136752136753"/></g>
  <g id="82">
   <use class="kv35" height="30" transform="rotate(0,1076.67,623) scale(1.94444,2.65242) translate(-509.73,-363.334)" width="28" x="1049.444444444445" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="583.2136752136753" zvalue="3763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV3号主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1076.67,623) scale(1.94444,2.65242) translate(-509.73,-363.334)" width="28" x="1049.444444444445" y="583.2136752136753"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="103">
   <use class="kv35" height="20" transform="rotate(0,1190.04,616.325) scale(1.42857,1.42857) translate(-351.655,-180.612)" width="25" x="1172.181818181818" xlink:href="#Accessory:4绕组母线PT_0" y="602.038961038961" zvalue="3774"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3901PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1190.04,616.325) scale(1.42857,1.42857) translate(-351.655,-180.612)" width="25" x="1172.181818181818" y="602.038961038961"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="4" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,765.167,690.286) scale(1,1) translate(0,0)" writing-mode="lr" x="764.65" xml:space="preserve" y="695.0599999999999" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="5" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,927.167,690.286) scale(1,1) translate(0,0)" writing-mode="lr" x="926.65" xml:space="preserve" y="695.0599999999999" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="6" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1077.17,690.286) scale(1,1) translate(-2.28188e-13,0)" writing-mode="lr" x="1076.65" xml:space="preserve" y="695.0599999999999" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="7" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,765.167,711.286) scale(1,1) translate(0,1.54052e-13)" writing-mode="lr" x="764.65" xml:space="preserve" y="716.0599999999999" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="8" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,927.167,711.286) scale(1,1) translate(0,1.54052e-13)" writing-mode="lr" x="926.65" xml:space="preserve" y="716.0599999999999" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="9" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1077.17,711.286) scale(1,1) translate(-2.28188e-13,1.54052e-13)" writing-mode="lr" x="1076.65" xml:space="preserve" y="716.0599999999999" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,765.167,736.286) scale(1,1) translate(0,0)" writing-mode="lr" x="764.65" xml:space="preserve" y="741.0599999999999" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,927.167,736.286) scale(1,1) translate(0,0)" writing-mode="lr" x="926.65" xml:space="preserve" y="741.0599999999999" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1077.17,736.286) scale(1,1) translate(-2.28188e-13,0)" writing-mode="lr" x="1076.65" xml:space="preserve" y="741.0599999999999" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.167,760.286) scale(1,1) translate(0,0)" writing-mode="lr" x="764.65" xml:space="preserve" y="765.0599999999999" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.167,760.286) scale(1,1) translate(0,0)" writing-mode="lr" x="926.65" xml:space="preserve" y="765.0599999999999" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1077.17,760.286) scale(1,1) translate(-2.28188e-13,0)" writing-mode="lr" x="1076.65" xml:space="preserve" y="765.0599999999999" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,678.5,320.464) scale(1,1) translate(0,0)" writing-mode="lr" x="678.09" xml:space="preserve" y="325.24" zvalue="1">Ua:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,678.5,343.464) scale(1,1) translate(0,0)" writing-mode="lr" x="678.09" xml:space="preserve" y="348.24" zvalue="1">Ub:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,678.5,366.464) scale(1,1) translate(0,0)" writing-mode="lr" x="678.09" xml:space="preserve" y="371.24" zvalue="1">Uc:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,678.5,389.464) scale(1,1) translate(0,0)" writing-mode="lr" x="678.09" xml:space="preserve" y="394.24" zvalue="1">Uab:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,678.214,412.464) scale(1,1) translate(0,0)" writing-mode="lr" x="677.8" xml:space="preserve" y="417.24" zvalue="1">U0:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="21" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,851.273,232.348) scale(1,1) translate(-1.81138e-13,0)" writing-mode="lr" x="850.9" xml:space="preserve" y="237.09" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="22" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,851.273,249.348) scale(1,1) translate(-1.81138e-13,0)" writing-mode="lr" x="850.9" xml:space="preserve" y="254.09" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="23" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,851.273,266.348) scale(1,1) translate(-1.81138e-13,0)" writing-mode="lr" x="850.9" xml:space="preserve" y="271.09" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.495,284.182) scale(1,1) translate(1.81631e-13,0)" writing-mode="lr" x="853.13" xml:space="preserve" y="288.93" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
 </g>
</svg>