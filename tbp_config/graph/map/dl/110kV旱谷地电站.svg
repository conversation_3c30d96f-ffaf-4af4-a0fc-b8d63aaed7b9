<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:放电间隙2_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="5.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="9" y2="14"/>
   <path d="M 15.0179 14.1078 L 11.5 18.1667 L 18.5802 18.1667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 15.0333 9.11336 L 11.5154 5.05453 L 18.4321 5.05453 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:串联电阻_0" viewBox="0,0,15,22">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="5.25"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="16.25"/>
   <path d="M 7.71392 9.13493 A 2.96392 1.81747 0 0 0 7.71392 5.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 7.71392 12.7699 A 2.96392 1.81747 0 0 0 7.71392 9.13493" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 7.71392 16.3048 A 2.96392 1.81747 0 0 0 7.71392 12.6699" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:站用变0924_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="0" x="11" xlink:href="#terminal" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.41666666666666" x2="20.41666666666666" y1="23.83333333333333" y2="31.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.41666666666666" x2="25.41666666666666" y1="29.83333333333333" y2="29.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.41666666666666" x2="26.41666666666666" y1="28.83333333333333" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.41666666666667" x2="24.41666666666666" y1="23.83333333333333" y2="23.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.96666666666667" x2="24.96666666666667" y1="30.83333333333334" y2="30.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.41666666666666" x2="24.41666666666666" y1="23.83333333333333" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.41666666666666" x2="11.41666666666667" y1="31.83333333333334" y2="35.83333333333334"/>
   <path d="M 10.0833 36 L 12.0833 36 L 11.0833 38 L 10.0833 36 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.11666666666667" x2="11.11666666666667" y1="30.58333333333333" y2="43"/>
   <ellipse cx="10.98" cy="12.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.98" cy="24" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.97838478538902" x2="10.97838478538902" y1="20.99779929207955" y2="23.59678739209649"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.57193227486047" x2="10.97838478538901" y1="26.1957754921134" y2="23.59678739209647"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.72838478538902" x2="10.72838478538902" y1="9.747799292079552" y2="12.34678739209649"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.32193227486047" x2="10.72838478538901" y1="14.9457754921134" y2="12.34678739209647"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.384837295917544" x2="10.978384785389" y1="26.1957754921134" y2="23.59678739209647"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.134837295917544" x2="10.728384785389" y1="14.9457754921134" y2="12.34678739209647"/>
   <path d="M 10.0833 43 L 12.0833 43 L 11.0833 45 L 10.0833 43 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="0.333333333333333" y1="12.5859872611465" y2="5.083333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="8" y1="4" y2="4"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="1.666666666666666" y1="13.5" y2="2.749999999999997"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.666666666666666" x2="8" y1="14" y2="3.249999999999998"/>
  </symbol>
  <symbol id="Accessory:避雷器－阀式_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.083333333333333" x2="11.08333333333333" y1="18.83333333333334" y2="18.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="8.333333333333332" y1="8.333333333333336" y2="8.333333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.083333333333333" x2="11.08333333333333" y1="11.83333333333334" y2="11.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.13333333333333" x2="10.13333333333333" y1="2.166666666666666" y2="5.683333333333334"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,10.11,13.68) scale(1,1) translate(0,0)" width="10.05" x="5.08" y="5.68"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.702777777777772" x2="12.21666666666667" y1="28.1" y2="28.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.13333333333333" x2="10.13333333333333" y1="21.68333333333334" y2="25.28333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="8.333333333333332" y1="15.33333333333334" y2="15.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.744444444444442" x2="12.85555555555556" y1="26.71842105263158" y2="26.71842105263158"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.466666666666666" x2="14.13333333333333" y1="25.33684210526318" y2="25.33684210526318"/>
  </symbol>
  <symbol id="Generator:负荷发电机2_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10.16666666666667" xlink:href="#terminal" y="1.250000000000005"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="18.75" y2="16.41666666666667"/>
   <path d="M 2 14.75 L 2 18.75 L 18 18.75 L 18 14.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="10.04" cy="8.710000000000001" fill-opacity="0" rx="7.54" ry="7.54" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 10.0758 8.70852 A 2.61816 2.78528 0 0 0 15.3121 8.70852" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 10.0538 8.6308 A 2.70592 2.721 -180 0 0 4.64273 8.72626" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="Accessory:三卷PT20210927_0" viewBox="0,0,25,27">
   <use terminal-index="0" type="0" x="9.699999999999999" xlink:href="#terminal" y="0.4333333333333282"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="7.166666666666666" y1="11.58333333333333" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="9.75" y1="7.166666666666666" y2="0.3333333333333339"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="9.75" y1="8.583333333333332" y2="11.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="12.75" y1="11.58333333333333" y2="13.58333333333333"/>
   <ellipse cx="9.779999999999999" cy="12" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.75" cy="20.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333332" x2="9.833333333333332" y1="17.83333333333334" y2="20.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333336" x2="12.83333333333334" y1="20.83333333333334" y2="22.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333332" x2="7.249999999999998" y1="20.83333333333334" y2="23.25"/>
   <ellipse cx="17.25" cy="16.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.58333333333334" x2="16.58333333333334" y1="14.5" y2="18.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.16666666666667" x2="16.75" y1="17.33333333333334" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="19.25" y1="14.58333333333333" y2="15.75"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-1074.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="7" y2="23"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="3.5" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.66666666666667" x2="4.666666666666667" y1="9.833333333333334" y2="20.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.416666666666667" x2="10.41666666666667" y1="9.5" y2="20.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="Accessory:PT232_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.2666666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="15.16666666666667" y2="0.5"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,7.5) scale(1,1) translate(0,0)" width="6" x="9.5" y="2.5"/>
   <ellipse cx="12.75" cy="20" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="29.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="24.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="22.25" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="32" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="9.003653261335021"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.14609851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.14687801656252" y2="8.861111111111104"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="16.14769090077732" y2="8.861111111111104"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="8.743941472336534" y2="1.450000000000003"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="8.736625514403292" y2="1.450000000000003"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_1" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="9.003653261335021"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.14609851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.14687801656252" y2="8.861111111111104"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="16.14769090077732" y2="8.861111111111104"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="9" y2="16.14037494284408"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_2" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="9.003653261335021"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.14609851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.992290301275212" x2="2.825623634608543" y1="7.813824112178023" y2="15.98049077884469"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.063455774018188" x2="9.563455774018188" y1="8.339836407051772" y2="16.00650307371844"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Accessory:V-DPT20210927_0" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="2.916666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.416666666666668" x2="13.41666666666667" y1="16" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="2.999999999999996" y2="6.333333333333331"/>
   <path d="M 7 7.83333 L 10 12.8333 L 13 7.83333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.23526 16.0849 L 8.33333 21.9151 L 13.4314 19.0849" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="9.82" cy="10.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.82" cy="18.45" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV旱谷地电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="48" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,247.982,130.196) scale(1,1) translate(0,0)" writing-mode="lr" x="247.98" xml:space="preserve" y="147.2" zvalue="1">110kV旱谷地电站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.822,677.124) scale(1,1) translate(0,0)" writing-mode="lr" x="483.82" xml:space="preserve" y="683.12" zvalue="60">6.3kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.2,541.655) scale(1,1) translate(0,0)" writing-mode="lr" x="892.2" xml:space="preserve" y="547.66" zvalue="93">6.3MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.438,361.478) scale(1,1) translate(0,0)" writing-mode="lr" x="801.4400000000001" xml:space="preserve" y="367.48" zvalue="401">1016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,875.996,311.062) scale(1,1) translate(0,0)" writing-mode="lr" x="876" xml:space="preserve" y="317.06" zvalue="403">10160</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.2,521.066) scale(1,1) translate(0,0)" writing-mode="lr" x="892.2" xml:space="preserve" y="527.0700000000001" zvalue="1544">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" glyph-orientation-vertical="0" id="57" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1446.52,939.349) scale(1,1) translate(0,0)" writing-mode="tb" x="1446.52" xml:space="preserve" y="939.35" zvalue="1569">1号厂用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,835.162,196.927) scale(1,1) translate(0,0)" writing-mode="lr" x="835.16" xml:space="preserve" y="202.93" zvalue="1727">110kV红两茅旱线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.219,404.886) scale(1,1) translate(0,0)" writing-mode="lr" x="805.22" xml:space="preserve" y="410.89" zvalue="1731">101</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1004.45,1013.17) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.45" xml:space="preserve" y="1019.17" zvalue="1930">2号机 2.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,735.818,538.12) scale(1,1) translate(0,0)" writing-mode="lr" x="735.8200000000001" xml:space="preserve" y="544.12" zvalue="2116">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,960.587,313.244) scale(1,1) translate(0,0)" writing-mode="lr" x="960.59" xml:space="preserve" y="319.24" zvalue="2121">19010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1124.22,378.698) scale(1,1) translate(0,0)" writing-mode="lr" x="1124.22" xml:space="preserve" y="384.7" zvalue="2123">19017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.98,293.881) scale(1,1) translate(0,0)" writing-mode="lr" x="1061.98" xml:space="preserve" y="299.88" zvalue="2125">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.438,451.215) scale(1,1) translate(0,0)" writing-mode="lr" x="801.4400000000001" xml:space="preserve" y="457.21" zvalue="2133">1011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,875.996,364.698) scale(1,1) translate(0,0)" writing-mode="lr" x="876" xml:space="preserve" y="370.7" zvalue="2139">10167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,875.996,413.789) scale(1,1) translate(0,0)" writing-mode="lr" x="876" xml:space="preserve" y="419.79" zvalue="2142">10117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,868.659,658.69) scale(1,1) translate(0,0)" writing-mode="lr" x="868.66" xml:space="preserve" y="664.6900000000001" zvalue="2147">6011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1413.09,790.335) scale(1,1) translate(0,0)" writing-mode="lr" x="1413.09" xml:space="preserve" y="796.34" zvalue="2160">663</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.35,795.903) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.35" xml:space="preserve" y="801.9" zvalue="2167">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1112.25,923.205) scale(1,1) translate(0,0)" writing-mode="lr" x="1112.25" xml:space="preserve" y="929.2" zvalue="2174">6903</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.09,790.335) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.09" xml:space="preserve" y="796.34" zvalue="2188">662</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,674.448,1013.17) scale(1,1) translate(0,0)" writing-mode="lr" x="674.45" xml:space="preserve" y="1019.17" zvalue="2203">1号机 2.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,782.254,923.205) scale(1,1) translate(0,0)" writing-mode="lr" x="782.25" xml:space="preserve" y="929.2" zvalue="2208">6902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.086,790.335) scale(1,1) translate(0,0)" writing-mode="lr" x="693.09" xml:space="preserve" y="796.34" zvalue="2216">661</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1233.46,514.732) scale(1,1) translate(0,0)" writing-mode="lr" x="1233.46" xml:space="preserve" y="520.73" zvalue="2242">备用间隔（到热水河电站）</text>
  <rect fill="none" fill-opacity="0" height="165.62" id="135" stroke="rgb(255,255,255)" stroke-dasharray="2 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1218.28,613.75) scale(1,1) translate(3.91458e-13,0)" width="85.94" x="1175.31" y="530.9400000000001" zvalue="2243"/>
 </g>
 <g id="BusbarSectionClass">
  <g id="42">
   <path class="v6300" d="M 442.5 702.78 L 1521.67 702.78" stroke-width="6" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="6.3kV#Ⅰ母"/>
   </metadata>
  <path d="M 442.5 702.78 L 1521.67 702.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="41">
   <use class="kv110" height="30" transform="rotate(0,835.136,360.518) scale(1.11111,0.814815) translate(-82.6803,79.1581)" width="15" x="826.8029185287191" xlink:href="#Disconnector:刀闸_0" y="348.2954472422491" zvalue="400"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧1016"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.136,360.518) scale(1.11111,0.814815) translate(-82.6803,79.1581)" width="15" x="826.8029185287191" y="348.2954472422491"/></g>
  <g id="27">
   <use class="kv110" height="30" transform="rotate(0,1033.32,294.881) scale(1.11111,0.814815) translate(-102.498,64.2407)" width="15" x="1024.984736710537" xlink:href="#Disconnector:刀闸_0" y="282.6590836058855" zvalue="2124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧1901"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1033.32,294.881) scale(1.11111,0.814815) translate(-102.498,64.2407)" width="15" x="1024.984736710537" y="282.6590836058855"/></g>
  <g id="73">
   <use class="kv110" height="30" transform="rotate(0,834.227,454.881) scale(1.11111,0.814815) translate(-82.5894,100.604)" width="15" x="825.8938276196283" xlink:href="#Disconnector:刀闸_0" y="442.6590836058855" zvalue="2132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧1011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,834.227,454.881) scale(1.11111,0.814815) translate(-82.5894,100.604)" width="15" x="825.8938276196283" y="442.6590836058855"/></g>
  <g id="231">
   <use class="v6300" height="30" transform="rotate(0,835.163,659.69) scale(1.3125,1.3125) translate(-196.505,-152.382)" width="15" x="825.3192678624572" xlink:href="#Disconnector:联体手车刀闸3_0" y="640.0024936161491" zvalue="2146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变6.3kV侧6011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.163,659.69) scale(1.3125,1.3125) translate(-196.505,-152.382)" width="15" x="825.3192678624572" y="640.0024936161491"/></g>
  <g id="69">
   <use class="v6300" height="18" transform="rotate(0,1335.34,795.135) scale(0.862069,-0.862069) translate(212.827,-1718.73)" width="12" x="1330.166416791604" xlink:href="#Disconnector:单手车刀闸_0" y="787.3762365045218" zvalue="2166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号厂用变6901"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1335.34,795.135) scale(0.862069,-0.862069) translate(212.827,-1718.73)" width="12" x="1330.166416791604" y="787.3762365045218"/></g>
  <g id="6">
   <use class="v6300" height="18" transform="rotate(0,1084.7,920.067) scale(0.862069,-0.862069) translate(172.725,-1988.59)" width="12" x="1079.529739628611" xlink:href="#Disconnector:单手车刀闸_0" y="912.3080103836641" zvalue="2173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机电压互感器2_36903"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1084.7,920.067) scale(0.862069,-0.862069) translate(172.725,-1988.59)" width="12" x="1079.529739628611" y="912.3080103836641"/></g>
  <g id="118">
   <use class="v6300" height="18" transform="rotate(0,754.702,920.067) scale(0.862069,-0.862069) translate(119.925,-1988.59)" width="12" x="749.5297396286111" xlink:href="#Disconnector:单手车刀闸_0" y="912.3080103836641" zvalue="2207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机电压互感器2_36902"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,754.702,920.067) scale(0.862069,-0.862069) translate(119.925,-1988.59)" width="12" x="749.5297396286111" y="912.3080103836641"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="40">
   <use class="kv110" height="30" transform="rotate(270,877.581,328.062) scale(-0.763595,0.763595) translate(-2028.28,98.0203)" width="12" x="872.9997612780508" xlink:href="#GroundDisconnector:地刀12_0" y="316.6078671556626" zvalue="402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧10160"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,877.581,328.062) scale(-0.763595,0.763595) translate(-2028.28,98.0203)" width="12" x="872.9997612780508" y="316.6078671556626"/></g>
  <g id="1187">
   <use class="kv110" height="40" transform="rotate(0,774.091,539.12) scale(1,-1) translate(0,-1078.24)" width="40" x="754.0909090909092" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="519.1201282352596" zvalue="2115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧1010"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,774.091,539.12) scale(1,-1) translate(0,-1078.24)" width="40" x="754.0909090909092" y="519.1201282352596"/></g>
  <g id="19">
   <use class="kv110" height="30" transform="rotate(0,926.672,314.244) scale(-0.763595,0.763595) translate(-2141.66,93.7423)" width="12" x="922.0906703689598" xlink:href="#GroundDisconnector:地刀12_0" y="302.7896853374806" zvalue="2120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧19010"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,926.672,314.244) scale(-0.763595,0.763595) translate(-2141.66,93.7423)" width="12" x="922.0906703689598" y="302.7896853374806"/></g>
  <g id="20">
   <use class="kv110" height="30" transform="rotate(0,1090.31,379.698) scale(-0.763595,0.763595) translate(-2519.59,114.007)" width="12" x="1085.727034005323" xlink:href="#GroundDisconnector:地刀12_0" y="368.2442307920263" zvalue="2122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧19017"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1090.31,379.698) scale(-0.763595,0.763595) translate(-2519.59,114.007)" width="12" x="1085.727034005323" y="368.2442307920263"/></g>
  <g id="83">
   <use class="kv110" height="30" transform="rotate(270,875.763,381.698) scale(-0.763595,0.763595) translate(-2024.08,114.626)" width="12" x="871.181579459869" xlink:href="#GroundDisconnector:地刀12_0" y="370.2442307920262" zvalue="2138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧10167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,875.763,381.698) scale(-0.763595,0.763595) translate(-2024.08,114.626)" width="12" x="871.181579459869" y="370.2442307920262"/></g>
  <g id="86">
   <use class="kv110" height="30" transform="rotate(270,875.763,430.789) scale(-0.763595,0.763595) translate(-2024.08,129.824)" width="12" x="871.1815794598688" xlink:href="#GroundDisconnector:地刀12_0" y="419.3351398829352" zvalue="2141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧10117"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,875.763,430.789) scale(-0.763595,0.763595) translate(-2024.08,129.824)" width="12" x="871.1815794598688" y="419.3351398829352"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="2">
   <use class="kv110" height="22" transform="rotate(0,835.234,277.168) scale(1.25604,1.68831) translate(-168.339,-105.428)" width="15" x="825.813492063492" xlink:href="#Compensator:串联电阻_0" y="258.5965608465609" zvalue="1497"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧串联电感"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,835.234,277.168) scale(1.25604,1.68831) translate(-168.339,-105.428)" width="15" x="825.813492063492" y="258.5965608465609"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="53">
   <g id="530">
    <use class="kv110" height="50" transform="rotate(0,835.163,529.337) scale(1.25128,1.25128) translate(-163.948,-100.019)" width="30" x="816.39" xlink:href="#PowerTransformer2:Y-D_0" y="498.06" zvalue="1543"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="110"/>
    </metadata>
   </g>
   <g id="531">
    <use class="v6300" height="50" transform="rotate(0,835.163,529.337) scale(1.25128,1.25128) translate(-163.948,-100.019)" width="30" x="816.39" xlink:href="#PowerTransformer2:Y-D_1" y="498.06" zvalue="1543"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,835.163,529.337) scale(1.25128,1.25128) translate(-163.948,-100.019)" width="30" x="816.39" y="498.06"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="48">
   <use class="v6300" height="50" transform="rotate(0,1399.31,948.136) scale(2.20693,2.20693) translate(-747.15,-488.345)" width="30" x="1366.201304096549" xlink:href="#Accessory:站用变0924_0" y="892.9628149628147" zvalue="1568"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号厂用变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1399.31,948.136) scale(2.20693,2.20693) translate(-747.15,-488.345)" width="30" x="1366.201304096549" y="892.9628149628147"/></g>
  <g id="98">
   <use class="v6300" height="30" transform="rotate(0,835.637,607.228) scale(1.11111,1.11111) translate(-81.897,-59.0561)" width="30" x="818.9698612899258" xlink:href="#Accessory:放电间隙2_0" y="590.5614409890388" zvalue="1753"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变6.3kV侧放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.637,607.228) scale(1.11111,1.11111) translate(-81.897,-59.0561)" width="30" x="818.9698612899258" y="590.5614409890388"/></g>
  <g id="167">
   <use class="v6300" height="30" transform="rotate(0,1282.64,837.146) scale(1.0851,1.49158) translate(-99.7426,-268.525)" width="20" x="1271.79260613938" xlink:href="#Accessory:避雷器－阀式_0" y="814.7723049896963" zvalue="1774"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="6.3kV#Ⅱ母避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1282.64,837.146) scale(1.0851,1.49158) translate(-99.7426,-268.525)" width="20" x="1271.79260613938" y="814.7723049896963"/></g>
  <g id="62">
   <use class="v6300" height="30" transform="rotate(0,926.229,942.722) scale(1.11111,1.11111) translate(-90.9563,-92.6056)" width="30" x="909.5627917270916" xlink:href="#Accessory:放电间隙2_0" y="926.0556538899739" zvalue="1931"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机电压互感器1放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,926.229,942.722) scale(1.11111,1.11111) translate(-90.9563,-92.6056)" width="30" x="909.5627917270916" y="926.0556538899739"/></g>
  <g id="80">
   <use class="v6300" height="18" transform="rotate(0,1084.44,939.212) scale(0.809804,0.800141) translate(253.747,232.797)" width="10" x="1080.38640924206" xlink:href="#Accessory:熔断器_0" y="932.0111700102107" zvalue="1942"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机电压互感器2_3熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1084.44,939.212) scale(0.809804,0.800141) translate(253.747,232.797)" width="10" x="1080.38640924206" y="932.0111700102107"/></g>
  <g id="84">
   <use class="v6300" height="27" transform="rotate(0,1099.73,978.645) scale(1.22982,1.22982) translate(-202.637,-179.779)" width="25" x="1084.359474990205" xlink:href="#Accessory:三卷PT20210927_0" y="962.042521941356" zvalue="1945"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机电压互感器3"/>
   </metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1099.73,978.645) scale(1.22982,1.22982) translate(-202.637,-179.779)" width="25" x="1084.359474990205" y="962.042521941356"/></g>
  <g id="10">
   <use class="kv110" height="35" transform="rotate(0,1036.91,373.272) scale(-1.66958,-1.66958) translate(-1644.57,-585.126)" width="40" x="1003.515588797281" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="344.0542694749352" zvalue="2118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1036.91,373.272) scale(-1.66958,-1.66958) translate(-1644.57,-585.126)" width="40" x="1003.515588797281" y="344.0542694749352"/></g>
  <g id="36">
   <use class="v6300" height="35" transform="rotate(0,1335.34,845.192) scale(1.4254,1.4254) translate(-393.203,-244.796)" width="25" x="1317.522002931542" xlink:href="#Accessory:PT232_0" y="820.2478632478634" zvalue="2151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="6.3kV#Ⅰ母电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1335.34,845.192) scale(1.4254,1.4254) translate(-393.203,-244.796)" width="25" x="1317.522002931542" y="820.2478632478634"/></g>
  <g id="47">
   <use class="v6300" height="30" transform="rotate(0,1390.37,866.045) scale(1.11111,1.11111) translate(-137.37,-84.9378)" width="30" x="1373.70297054469" xlink:href="#Accessory:放电间隙2_0" y="849.3780516463235" zvalue="2154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号厂用变放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1390.37,866.045) scale(1.11111,1.11111) translate(-137.37,-84.9378)" width="30" x="1373.70297054469" y="849.3780516463235"/></g>
  <g id="223">
   <use class="v6300" height="26" transform="rotate(180,1044.7,940.623) scale(-1,-0.879121) translate(-2089.4,-2012.15)" width="12" x="1038.702450917032" xlink:href="#Accessory:避雷器_0" y="929.1944480941395" zvalue="2175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1044.7,940.623) scale(-1,-0.879121) translate(-2089.4,-2012.15)" width="12" x="1038.702450917032" y="929.1944480941395"/></g>
  <g id="13">
   <use class="v6300" height="29" transform="rotate(0,1073.28,976.113) scale(1.31964,-1.31964) translate(-255.176,-1711.16)" width="30" x="1053.487601413973" xlink:href="#Accessory:PT12321_0" y="956.9779647994769" zvalue="2180"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机电压互感器2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1073.28,976.113) scale(1.31964,-1.31964) translate(-255.176,-1711.16)" width="30" x="1053.487601413973" y="956.9779647994769"/></g>
  <g id="16">
   <use class="v6300" height="25" transform="rotate(0,926.353,980.416) scale(1.32321,1.32321) translate(-223.043,-235.441)" width="20" x="913.1209677419356" xlink:href="#Accessory:V-DPT20210927_0" y="963.8753840245776" zvalue="2181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机电压互感器1"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,926.353,980.416) scale(1.32321,1.32321) translate(-223.043,-235.441)" width="20" x="913.1209677419356" y="963.8753840245776"/></g>
  <g id="17">
   <use class="v6300" height="30" transform="rotate(0,1000.01,942.722) scale(1.11111,1.11111) translate(-98.3341,-92.6056)" width="30" x="983.3405695048693" xlink:href="#Accessory:放电间隙2_0" y="926.0556539154929" zvalue="2183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1000.01,942.722) scale(1.11111,1.11111) translate(-98.3341,-92.6056)" width="30" x="983.3405695048693" y="926.0556539154929"/></g>
  <g id="121">
   <use class="v6300" height="30" transform="rotate(0,596.229,942.722) scale(1.11111,1.11111) translate(-57.9563,-92.6056)" width="30" x="579.5627917270917" xlink:href="#Accessory:放电间隙2_0" y="926.0556538899739" zvalue="2204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机电压互感器1放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,596.229,942.722) scale(1.11111,1.11111) translate(-57.9563,-92.6056)" width="30" x="579.5627917270917" y="926.0556538899739"/></g>
  <g id="120">
   <use class="v6300" height="18" transform="rotate(0,754.435,939.212) scale(0.809804,0.800141) translate(176.241,232.797)" width="10" x="750.3864092420596" xlink:href="#Accessory:熔断器_0" y="932.0111700102107" zvalue="2205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机电压互感器2_3熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,754.435,939.212) scale(0.809804,0.800141) translate(176.241,232.797)" width="10" x="750.3864092420596" y="932.0111700102107"/></g>
  <g id="119">
   <use class="v6300" height="27" transform="rotate(0,769.732,978.645) scale(1.22982,1.22982) translate(-140.969,-179.779)" width="25" x="754.3594749902049" xlink:href="#Accessory:三卷PT20210927_0" y="962.042521941356" zvalue="2206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机电压互感器3"/>
   </metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,769.732,978.645) scale(1.22982,1.22982) translate(-140.969,-179.779)" width="25" x="754.3594749902049" y="962.042521941356"/></g>
  <g id="117">
   <use class="v6300" height="26" transform="rotate(180,714.702,940.623) scale(-1,-0.879121) translate(-1429.4,-2012.15)" width="12" x="708.7024509170324" xlink:href="#Accessory:避雷器_0" y="929.1944480941395" zvalue="2209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,714.702,940.623) scale(-1,-0.879121) translate(-1429.4,-2012.15)" width="12" x="708.7024509170324" y="929.1944480941395"/></g>
  <g id="116">
   <use class="v6300" height="29" transform="rotate(0,743.282,976.113) scale(1.31964,-1.31964) translate(-175.243,-1711.16)" width="30" x="723.4876014139734" xlink:href="#Accessory:PT12321_0" y="956.9779647994769" zvalue="2210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机电压互感器2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,743.282,976.113) scale(1.31964,-1.31964) translate(-175.243,-1711.16)" width="30" x="723.4876014139734" y="956.9779647994769"/></g>
  <g id="115">
   <use class="v6300" height="25" transform="rotate(0,596.353,980.416) scale(1.32321,1.32321) translate(-142.436,-235.441)" width="20" x="583.1209677419356" xlink:href="#Accessory:V-DPT20210927_0" y="963.8753840245776" zvalue="2211"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机电压互感器1"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,596.353,980.416) scale(1.32321,1.32321) translate(-142.436,-235.441)" width="20" x="583.1209677419356" y="963.8753840245776"/></g>
  <g id="114">
   <use class="v6300" height="30" transform="rotate(0,670.007,942.722) scale(1.11111,1.11111) translate(-65.3341,-92.6056)" width="30" x="653.3405695048694" xlink:href="#Accessory:放电间隙2_0" y="926.0556539154929" zvalue="2212"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,670.007,942.722) scale(1.11111,1.11111) translate(-65.3341,-92.6056)" width="30" x="653.3405695048694" y="926.0556539154929"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="3">
   <use class="kv110" height="30" transform="rotate(0,835.234,218.528) scale(2.06349,0.555556) translate(-426.744,168.156)" width="7" x="828.0115596963424" xlink:href="#ACLineSegment:线路_0" y="210.1944444444445" zvalue="1726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV红两茅旱线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.234,218.528) scale(2.06349,0.555556) translate(-426.744,168.156)" width="7" x="828.0115596963424" y="210.1944444444445"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="14">
   <path class="kv110" d="M 835.23 267.46 L 835.23 226.78" stroke-width="1" zvalue="1728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.23 267.46 L 835.23 226.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv110" d="M 835.2 372.53 L 835.2 389.59" stroke-width="1" zvalue="1741"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@1" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.2 372.53 L 835.2 389.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="v6300" d="M 835.64 596.21 L 835.16 596.21" stroke-width="1" zvalue="1754"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.64 596.21 L 835.16 596.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv110" d="M 776.69 526.92 L 776.69 513.09 L 835.18 513.09" stroke-width="1" zvalue="2116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1187@0" LinkObjectIDznd="53@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.69 526.92 L 776.69 513.09 L 835.18 513.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv110" d="M 1033.42 283.06 L 1033.42 250.91 L 835.23 250.91" stroke-width="1" zvalue="2125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="14" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.42 283.06 L 1033.42 250.91 L 835.23 250.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv110" d="M 1033.39 306.89 L 1033.39 344.86" stroke-width="1" zvalue="2126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@1" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.39 306.89 L 1033.39 344.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv110" d="M 1090.32 368.76 L 1090.32 321.82 L 1033.39 321.82" stroke-width="1" zvalue="2127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 1090.32 368.76 L 1090.32 321.82 L 1033.39 321.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 926.68 303.31 L 926.68 250.91" stroke-width="1" zvalue="2128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="35" MaxPinNum="2"/>
   </metadata>
  <path d="M 926.68 303.31 L 926.68 250.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv110" d="M 835.23 286.03 L 835.23 348.7" stroke-width="1" zvalue="2129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@1" LinkObjectIDznd="41@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.23 286.03 L 835.23 348.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 866.64 328.05 L 835.23 328.05" stroke-width="1" zvalue="2130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="61" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.64 328.05 L 835.23 328.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv110" d="M 835.2 418.99 L 835.2 443.06" stroke-width="1" zvalue="2133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@1" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.2 418.99 L 835.2 443.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv110" d="M 834.3 466.89 L 834.3 498.62" stroke-width="1" zvalue="2134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 834.3 466.89 L 834.3 498.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 864.83 381.69 L 835.2 381.69" stroke-width="1" zvalue="2143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 864.83 381.69 L 835.2 381.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 864.83 430.78 L 835.2 430.78" stroke-width="1" zvalue="2144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 864.83 430.78 L 835.2 430.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="v6300" d="M 835.16 560.18 L 835.16 645.25" stroke-width="1" zvalue="2149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="231@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.16 560.18 L 835.16 645.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="v6300" d="M 835.16 674.13 L 835.16 702.78" stroke-width="1" zvalue="2150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="42@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.16 674.13 L 835.16 702.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="v6300" d="M 1390.48 749.37 L 1390.48 702.78" stroke-width="1" zvalue="2161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="42@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.48 749.37 L 1390.48 702.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v6300" d="M 1390.5 755.65 L 1390.5 776.43" stroke-width="1" zvalue="2162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.5 755.65 L 1390.5 776.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="v6300" d="M 1390.5 805.84 L 1390.5 828.44" stroke-width="1" zvalue="2163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@1" LinkObjectIDznd="190@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.5 805.84 L 1390.5 828.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v6300" d="M 1390.48 834.72 L 1390.48 906.2" stroke-width="1" zvalue="2164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.48 834.72 L 1390.48 906.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v6300" d="M 1390.37 855.03 L 1390.37 834.72" stroke-width="1" zvalue="2165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.37 855.03 L 1390.37 834.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="v6300" d="M 1335.34 795.13 L 1335.34 820.63" stroke-width="1" zvalue="2167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1335.34 795.13 L 1335.34 820.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="v6300" d="M 1000.5 755.65 L 1000.5 776.43" stroke-width="1" zvalue="2189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@1" LinkObjectIDznd="24@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.5 755.65 L 1000.5 776.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="v6300" d="M 1000.5 805.84 L 1000.5 828.44" stroke-width="1" zvalue="2190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@1" LinkObjectIDznd="26@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.5 805.84 L 1000.5 828.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="v6300" d="M 1000.48 834.72 L 1000.48 961.09" stroke-width="1" zvalue="2191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="59@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.48 834.72 L 1000.48 961.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="v6300" d="M 1000.48 749.37 L 1000.48 702.78" stroke-width="1" zvalue="2192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.48 749.37 L 1000.48 702.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="v6300" d="M 1096.29 962.58 L 1096.29 957.31 L 1073.28 957.31" stroke-width="1" zvalue="2193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1096.29 962.58 L 1096.29 957.31 L 1073.28 957.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="v6300" d="M 1084.7 920.06 L 1084.7 957.31" stroke-width="1" zvalue="2194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.7 920.06 L 1084.7 957.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="v6300" d="M 1084.45 932.88 L 1084.7 932.88" stroke-width="1" zvalue="2195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.45 932.88 L 1084.7 932.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="v6300" d="M 1084.7 913.91 L 1084.7 901.48 L 1000.48 901.48" stroke-width="1" zvalue="2196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@1" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.7 913.91 L 1084.7 901.48 L 1000.48 901.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="v6300" d="M 1044.74 929.75 L 1044.74 901.48" stroke-width="1" zvalue="2197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="37" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.74 929.75 L 1044.74 901.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v6300" d="M 926.35 967.73 L 926.35 901.48 L 1000.48 901.48" stroke-width="1" zvalue="2198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 926.35 967.73 L 926.35 901.48 L 1000.48 901.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="v6300" d="M 926.23 931.7 L 926.35 931.7" stroke-width="1" zvalue="2199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 926.23 931.7 L 926.35 931.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v6300" d="M 1000.01 931.7 L 1000.48 931.7" stroke-width="1" zvalue="2200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.01 931.7 L 1000.48 931.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="v6300" d="M 670.5 755.65 L 670.5 776.43" stroke-width="1" zvalue="2217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@1" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.5 755.65 L 670.5 776.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="v6300" d="M 670.5 805.84 L 670.5 828.44" stroke-width="1" zvalue="2218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@1" LinkObjectIDznd="112@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.5 805.84 L 670.5 828.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="v6300" d="M 670.48 834.72 L 670.48 961.09" stroke-width="1" zvalue="2219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="122@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.48 834.72 L 670.48 961.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v6300" d="M 670.48 749.37 L 670.48 702.78" stroke-width="1" zvalue="2220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.48 749.37 L 670.48 702.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="v6300" d="M 766.29 962.58 L 766.29 957.31 L 743.28 957.31" stroke-width="1" zvalue="2221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="116@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.29 962.58 L 766.29 957.31 L 743.28 957.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="v6300" d="M 754.7 920.06 L 754.7 957.31" stroke-width="1" zvalue="2222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.7 920.06 L 754.7 957.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v6300" d="M 754.45 932.88 L 754.7 932.88" stroke-width="1" zvalue="2223"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.45 932.88 L 754.7 932.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="v6300" d="M 754.7 913.91 L 754.7 901.48 L 670.48 901.48" stroke-width="1" zvalue="2224"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.7 913.91 L 754.7 901.48 L 670.48 901.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="v6300" d="M 714.74 929.75 L 714.74 901.48" stroke-width="1" zvalue="2225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.74 929.75 L 714.74 901.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="v6300" d="M 596.35 967.73 L 596.35 901.48 L 670.48 901.48" stroke-width="1" zvalue="2226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.35 967.73 L 596.35 901.48 L 670.48 901.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v6300" d="M 596.23 931.7 L 596.35 931.7" stroke-width="1" zvalue="2227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.23 931.7 L 596.35 931.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v6300" d="M 670.01 931.7 L 670.48 931.7" stroke-width="1" zvalue="2228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.01 931.7 L 670.48 931.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="v6300" d="M 1282.64 818.13 L 1282.64 733.85 L 1390.48 733.85" stroke-width="1" zvalue="2229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="38" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.64 818.13 L 1282.64 733.85 L 1390.48 733.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="v6300" d="M 1335.34 788.97 L 1335.34 733.85" stroke-width="1" zvalue="2230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="123" MaxPinNum="2"/>
   </metadata>
  <path d="M 1335.34 788.97 L 1335.34 733.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="v6300" d="M 1217.96 599.02 L 1217.96 619.81" stroke-width="1" zvalue="2236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1217.96 599.02 L 1217.96 619.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="v6300" d="M 1217.96 649.21 L 1217.96 671.81" stroke-width="1" zvalue="2237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@1" LinkObjectIDznd="129@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1217.96 649.21 L 1217.96 671.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="v6300" d="M 1217.94 571.65 L 1217.94 592.74" stroke-width="1" zvalue="2239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1217.94 571.65 L 1217.94 592.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="v6300" d="M 1217.94 678.09 L 1217.94 702.78" stroke-width="1" zvalue="2240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="42@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1217.94 678.09 L 1217.94 702.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="25">
   <use class="kv110" height="20" transform="rotate(0,835.082,404.291) scale(1.5812,1.5812) translate(-304.043,-142.792)" width="10" x="827.1763809452352" xlink:href="#Breaker:刀闸绘制规范_0" y="388.4786324786355" zvalue="1730"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧101"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,835.082,404.291) scale(1.5812,1.5812) translate(-304.043,-142.792)" width="10" x="827.1763809452352" y="388.4786324786355"/></g>
  <g id="12">
   <use class="v6300" height="20" transform="rotate(0,1390.38,791.135) scale(1.5812,1.5812) translate(-508.152,-284.984)" width="10" x="1382.473010513791" xlink:href="#Breaker:刀闸绘制规范_0" y="775.3228913657692" zvalue="2159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号厂用变663"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1390.38,791.135) scale(1.5812,1.5812) translate(-508.152,-284.984)" width="10" x="1382.473010513791" y="775.3228913657692"/></g>
  <g id="24">
   <use class="v6300" height="20" transform="rotate(0,1000.38,791.135) scale(1.5812,1.5812) translate(-364.801,-284.984)" width="10" x="992.4730105137908" xlink:href="#Breaker:刀闸绘制规范_0" y="775.3228913657692" zvalue="2187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机662"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1000.38,791.135) scale(1.5812,1.5812) translate(-364.801,-284.984)" width="10" x="992.4730105137908" y="775.3228913657692"/></g>
  <g id="111">
   <use class="v6300" height="20" transform="rotate(0,670.379,791.135) scale(1.5812,1.5812) translate(-243.504,-284.984)" width="10" x="662.4730105137908" xlink:href="#Breaker:刀闸绘制规范_0" y="775.3228913657692" zvalue="2215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机661"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,670.379,791.135) scale(1.5812,1.5812) translate(-243.504,-284.984)" width="10" x="662.4730105137908" y="775.3228913657692"/></g>
  <g id="128">
   <use class="v6300" height="20" transform="rotate(0,1217.84,634.51) scale(1.5812,1.5812) translate(-444.732,-227.413)" width="10" x="1209.931343847124" xlink:href="#Breaker:刀闸绘制规范_0" y="618.6978913657692" zvalue="2234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1217.84,634.51) scale(1.5812,1.5812) translate(-444.732,-227.413)" width="10" x="1209.931343847124" y="618.6978913657692"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="59">
   <use class="v6300" height="20" transform="rotate(0,1000.17,977.403) scale(1.86458,1.86458) translate(-455.119,-444.564)" width="20" x="981.5209054905258" xlink:href="#Generator:负荷发电机2_0" y="958.7575757575758" zvalue="1929"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1000.17,977.403) scale(1.86458,1.86458) translate(-455.119,-444.564)" width="20" x="981.5209054905258" y="958.7575757575758"/></g>
  <g id="122">
   <use class="v6300" height="20" transform="rotate(0,670.167,977.403) scale(1.86458,1.86458) translate(-302.102,-444.564)" width="20" x="651.5209054905258" xlink:href="#Generator:负荷发电机2_0" y="958.7575757575758" zvalue="2202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,670.167,977.403) scale(1.86458,1.86458) translate(-302.102,-444.564)" width="20" x="651.5209054905258" y="958.7575757575758"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="191">
   <use class="v6300" height="22" transform="rotate(0,1390.47,755.469) scale(0.610836,0.610836) translate(881.589,477.03)" width="22" x="1383.749999608749" xlink:href="#DollyBreaker:手车_0" y="748.75" zvalue="2156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号厂用变663手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1390.47,755.469) scale(0.610836,0.610836) translate(881.589,477.03)" width="22" x="1383.749999608749" y="748.75"/></g>
  <g id="190">
   <use class="v6300" height="22" transform="rotate(0,1390.47,828.615) scale(0.610836,-0.610836) translate(881.589,-2189.42)" width="22" x="1383.749999609755" xlink:href="#DollyBreaker:手车_0" y="821.8957668174659" zvalue="2157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号厂用变663手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1390.47,828.615) scale(0.610836,-0.610836) translate(881.589,-2189.42)" width="22" x="1383.749999609755" y="821.8957668174659"/></g>
  <g id="28">
   <use class="v6300" height="22" transform="rotate(0,1000.47,755.469) scale(0.610836,0.610836) translate(633.12,477.03)" width="22" x="993.749999608749" xlink:href="#DollyBreaker:手车_0" y="748.75" zvalue="2185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机662手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1000.47,755.469) scale(0.610836,0.610836) translate(633.12,477.03)" width="22" x="993.749999608749" y="748.75"/></g>
  <g id="26">
   <use class="v6300" height="22" transform="rotate(0,1000.47,828.615) scale(0.610836,-0.610836) translate(633.12,-2189.42)" width="22" x="993.7499996097553" xlink:href="#DollyBreaker:手车_0" y="821.895766820663" zvalue="2186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号机662手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1000.47,828.615) scale(0.610836,-0.610836) translate(633.12,-2189.42)" width="22" x="993.7499996097553" y="821.895766820663"/></g>
  <g id="113">
   <use class="v6300" height="22" transform="rotate(0,670.469,755.469) scale(0.610836,0.610836) translate(422.876,477.03)" width="22" x="663.7499996087489" xlink:href="#DollyBreaker:手车_0" y="748.75" zvalue="2213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机661手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,670.469,755.469) scale(0.610836,0.610836) translate(422.876,477.03)" width="22" x="663.7499996087489" y="748.75"/></g>
  <g id="112">
   <use class="v6300" height="22" transform="rotate(0,670.469,828.615) scale(0.610836,-0.610836) translate(422.876,-2189.42)" width="22" x="663.7499996097554" xlink:href="#DollyBreaker:手车_0" y="821.895770155326" zvalue="2214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号机661手车"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,670.469,828.615) scale(0.610836,-0.610836) translate(422.876,-2189.42)" width="22" x="663.7499996097554" y="821.895770155326"/></g>
  <g id="130">
   <use class="v6300" height="22" transform="rotate(0,1217.93,598.844) scale(0.610836,0.610836) translate(771.663,377.244)" width="22" x="1211.208332942082" xlink:href="#DollyBreaker:手车_0" y="592.1249999999999" zvalue="2232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1217.93,598.844) scale(0.610836,0.610836) translate(771.663,377.244)" width="22" x="1211.208332942082" y="592.1249999999999"/></g>
  <g id="129">
   <use class="v6300" height="22" transform="rotate(0,1217.93,671.99) scale(0.610836,-0.610836) translate(771.663,-1776.39)" width="22" x="1211.208332943089" xlink:href="#DollyBreaker:手车_0" y="665.270766820663" zvalue="2233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1217.93,671.99) scale(0.610836,-0.610836) translate(771.663,-1776.39)" width="22" x="1211.208332943089" y="665.270766820663"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="131">
   <use class="v6300" height="30" transform="rotate(0,1217.94,555) scale(1.25,1.23333) translate(-242.087,-101.5)" width="12" x="1210.435836045075" xlink:href="#EnergyConsumer:负荷_0" y="536.5" zvalue="2238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1217.94,555) scale(1.25,1.23333) translate(-242.087,-101.5)" width="12" x="1210.435836045075" y="536.5"/></g>
 </g>
</svg>