<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584658434" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.01340482573727" x2="10.01340482573727" y1="11.16666666666667" y2="12.83842547222383"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6934763181412" x2="10.01340482573727" y1="14.51018427778098" y2="12.83842547222382"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.333333333333332" x2="10.01340482573726" y1="14.51018427778098" y2="12.83842547222382"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="Accessory:配电室_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.04236111111111" xlink:href="#terminal" y="28.15999999999999"/>
   <rect fill-opacity="0" height="26.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" transform="rotate(0,15.08,15.08) scale(1,1) translate(0,0)" width="27.5" x="1.33" y="2"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV杨保变电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="48" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,165,63.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="165" xml:space="preserve" y="80.33" zvalue="3060">杨保变电站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="513" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706.56,841.762) scale(1,1) translate(0,0)" writing-mode="lr" x="706.5599999999999" xml:space="preserve" y="845.26" zvalue="1918">10kV#Ⅰ母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.715,419) scale(1,1) translate(0,0)" writing-mode="lr" x="586.72" xml:space="preserve" y="422.5" zvalue="2265">35kV#Ⅲ母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.031,332.487) scale(1,1) translate(0,0)" writing-mode="lr" x="963.03" xml:space="preserve" y="335.99" zvalue="2309">3801</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.535,492.201) scale(1,1) translate(0,0)" writing-mode="lr" x="936.54" xml:space="preserve" y="495.7" zvalue="2341">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.752,588.678) scale(1,1) translate(0,0)" writing-mode="lr" x="935.75" xml:space="preserve" y="592.1799999999999" zvalue="2346">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,972.583,667.093) scale(1,1) translate(0,0)" writing-mode="lr" x="972.58" xml:space="preserve" y="670.59" zvalue="2348">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="405" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,914.668,254.821) scale(1,1) translate(0,0)" writing-mode="lr" x="914.67" xml:space="preserve" y="258.32" zvalue="3056">35kV蹇乔线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="419" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,884.982,557.606) scale(1,1) translate(0,0)" writing-mode="lr" x="884.98" xml:space="preserve" y="561.11" zvalue="3086">30117</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,914.286,345.571) scale(1,1) translate(0,0)" writing-mode="lr" x="914.29" xml:space="preserve" y="350.07" zvalue="3873">35kV高压计量装置</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1155.55,490.642) scale(1,1) translate(0,0)" writing-mode="lr" x="1155.55" xml:space="preserve" y="494.14" zvalue="3880">3517</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130.94,692.048) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.94" xml:space="preserve" y="696.55" zvalue="3882">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,939.084,799.701) scale(1,1) translate(0,0)" writing-mode="lr" x="939.08" xml:space="preserve" y="803.2" zvalue="3892">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.002,738.678) scale(1,1) translate(0,0)" writing-mode="lr" x="937" xml:space="preserve" y="742.1799999999999" zvalue="3894">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,769.474,955.817) scale(1,1) translate(0,0)" writing-mode="lr" x="769.47" xml:space="preserve" y="959.3200000000001" zvalue="3900">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.16,907.884) scale(1,1) translate(0,0)" writing-mode="lr" x="777.16" xml:space="preserve" y="911.38" zvalue="3902">0511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.938,1008.77) scale(1,1) translate(0,0)" writing-mode="lr" x="777.9400000000001" xml:space="preserve" y="1012.27" zvalue="3904">0516</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,751.114,1141.15) scale(1,1) translate(0,0)" writing-mode="lr" x="751.11" xml:space="preserve" y="1144.15" zvalue="3914">10kV罗家山线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.046,956.532) scale(1,1) translate(0,0)" writing-mode="lr" x="953.05" xml:space="preserve" y="960.03" zvalue="3917">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,960.731,908.599) scale(1,1) translate(0,0)" writing-mode="lr" x="960.73" xml:space="preserve" y="912.1" zvalue="3919">0521</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,961.509,1009.49) scale(1,1) translate(0,0)" writing-mode="lr" x="961.51" xml:space="preserve" y="1012.99" zvalue="3921">0526</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.685,1141.86) scale(1,1) translate(0,0)" writing-mode="lr" x="934.6900000000001" xml:space="preserve" y="1144.86" zvalue="3930">10kV雄鲁么线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1117.73,920.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1117.73" xml:space="preserve" y="924.11" zvalue="3951">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1099.8,1080.6) scale(1,1) translate(0,0)" writing-mode="lr" x="1099.8" xml:space="preserve" y="1085.1" zvalue="3953">10kV#Ⅰ母线电压互感器</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="512">
   <path class="kv10" d="M 712.89 859.14 L 1152.33 859.14" stroke-width="6" zvalue="1917"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242658309" ObjectName="10kV#Ⅰ母线"/>
   <cge:TPSR_Ref TObjectID="9288674242658309"/></metadata>
  <path d="M 712.89 859.14 L 1152.33 859.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 586.67 437.5 L 1275.48 437.5" stroke-width="6" zvalue="2264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅲ母线"/>
   </metadata>
  <path d="M 586.67 437.5 L 1275.48 437.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="333">
   <use class="kv35" height="30" transform="rotate(270,963.503,319.309) scale(-1.11111,0.814815) translate(-1829.82,69.7923)" width="15" x="955.1700768789973" xlink:href="#Disconnector:刀闸_0" y="307.0862938685843" zvalue="2308"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV蹇乔线3801"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,963.503,319.309) scale(-1.11111,0.814815) translate(-1829.82,69.7923)" width="15" x="955.1700768789973" y="307.0862938685843"/></g>
  <g id="336">
   <use class="kv35" height="30" transform="rotate(0,914.444,495.451) scale(-1.11111,0.814815) translate(-1736.61,109.825)" width="15" x="906.1104268819088" xlink:href="#Disconnector:刀闸_0" y="483.2291510114414" zvalue="2340"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,914.444,495.451) scale(-1.11111,0.814815) translate(-1736.61,109.825)" width="15" x="906.1104268819088" y="483.2291510114414"/></g>
  <g id="13">
   <use class="kv35" height="30" transform="rotate(0,1126.03,488.892) scale(-1.11111,0.814815) translate(-2138.62,108.334)" width="15" x="1117.693886402807" xlink:href="#Disconnector:刀闸_0" y="476.6696272019176" zvalue="3879"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变3517"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1126.03,488.892) scale(-1.11111,0.814815) translate(-2138.62,108.334)" width="15" x="1117.693886402807" y="476.6696272019176"/></g>
  <g id="27">
   <use class="kv10" height="30" transform="rotate(0,916.993,802.951) scale(-1.11111,0.814815) translate(-1741.45,179.711)" width="15" x="908.6592899352789" xlink:href="#Disconnector:刀闸_0" y="790.7291510114413" zvalue="3891"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧0011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,916.993,802.951) scale(-1.11111,0.814815) translate(-1741.45,179.711)" width="15" x="908.6592899352789" y="790.7291510114413"/></g>
  <g id="54">
   <use class="kv10" height="30" transform="rotate(0,747.633,906.134) scale(-1.11111,0.814815) translate(-1419.67,203.162)" width="15" x="739.2992157140189" xlink:href="#Disconnector:刀闸_0" y="893.9121575473893" zvalue="3901"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV罗家山线0511"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,747.633,906.134) scale(-1.11111,0.814815) translate(-1419.67,203.162)" width="15" x="739.2992157140189" y="893.9121575473893"/></g>
  <g id="53">
   <use class="kv10" height="30" transform="rotate(0,748.41,1007.02) scale(-1.11111,0.814815) translate(-1421.15,226.091)" width="15" x="740.0769934917967" xlink:href="#Disconnector:刀闸_0" y="994.8010464362783" zvalue="3903"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV罗家山线0516"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,748.41,1007.02) scale(-1.11111,0.814815) translate(-1421.15,226.091)" width="15" x="740.0769934917967" y="994.8010464362783"/></g>
  <g id="86">
   <use class="kv10" height="30" transform="rotate(0,931.204,906.849) scale(-1.11111,0.814815) translate(-1768.45,203.324)" width="15" x="922.8706442854475" xlink:href="#Disconnector:刀闸_0" y="894.6264432616749" zvalue="3918"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV雄鲁么线0521"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.204,906.849) scale(-1.11111,0.814815) translate(-1768.45,203.324)" width="15" x="922.8706442854475" y="894.6264432616749"/></g>
  <g id="85">
   <use class="kv10" height="30" transform="rotate(0,931.982,1007.74) scale(-1.11111,0.814815) translate(-1769.93,226.253)" width="15" x="923.6484220632252" xlink:href="#Disconnector:刀闸_0" y="995.5153321505638" zvalue="3920"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV雄鲁么线0526"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.982,1007.74) scale(-1.11111,0.814815) translate(-1769.93,226.253)" width="15" x="923.6484220632252" y="995.5153321505638"/></g>
  <g id="119">
   <use class="kv10" height="30" transform="rotate(0,1088.21,918.86) scale(-1.11111,0.814815) translate(-2066.76,206.054)" width="15" x="1079.873373582294" xlink:href="#Disconnector:刀闸_0" y="906.637575919866" zvalue="3950"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母线电压互感器0901"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1088.21,918.86) scale(-1.11111,0.814815) translate(-2066.76,206.054)" width="15" x="1079.873373582294" y="906.637575919866"/></g>
 </g>
 <g id="BreakerClass">
  <g id="339">
   <use class="kv35" height="20" transform="rotate(0,915.033,590.653) scale(1.22222,1.11111) translate(-165.258,-57.9542)" width="10" x="908.9215686331032" xlink:href="#Breaker:开关_0" y="579.5417332036538" zvalue="2345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,915.033,590.653) scale(1.22222,1.11111) translate(-165.258,-57.9542)" width="10" x="908.9215686331032" y="579.5417332036538"/></g>
  <g id="26">
   <use class="kv10" height="20" transform="rotate(0,916.283,740.653) scale(1.22222,1.11111) translate(-165.486,-72.9542)" width="10" x="910.1715686331032" xlink:href="#Breaker:开关_0" y="729.5417332036538" zvalue="3893"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,916.283,740.653) scale(1.22222,1.11111) translate(-165.486,-72.9542)" width="10" x="910.1715686331032" y="729.5417332036538"/></g>
  <g id="62">
   <use class="kv10" height="20" transform="rotate(0,748.755,957.792) scale(1.22222,1.11111) translate(-135.026,-94.6681)" width="10" x="742.6437908553257" xlink:href="#Breaker:开关_0" y="946.6806220925428" zvalue="3899"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV罗家山线051"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,748.755,957.792) scale(1.22222,1.11111) translate(-135.026,-94.6681)" width="10" x="742.6437908553257" y="946.6806220925428"/></g>
  <g id="87">
   <use class="kv10" height="20" transform="rotate(0,932.326,958.506) scale(1.22222,1.11111) translate(-168.403,-94.7395)" width="10" x="926.2152194267543" xlink:href="#Breaker:开关_0" y="947.3949078068284" zvalue="3916"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV雄鲁么线052"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,932.326,958.506) scale(1.22222,1.11111) translate(-168.403,-94.7395)" width="10" x="926.2152194267543" y="947.3949078068284"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="340">
   <g id="3400">
    <use class="kv35" height="30" transform="rotate(0,915.833,660.593) scale(-2.15278,2.10494) translate(-1327.42,-330.189)" width="24" x="890" xlink:href="#PowerTransformer2:可调两卷变_0" y="629.02" zvalue="2347"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431856644" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3401">
    <use class="kv10" height="30" transform="rotate(0,915.833,660.593) scale(-2.15278,2.10494) translate(-1327.42,-330.189)" width="24" x="890" xlink:href="#PowerTransformer2:可调两卷变_1" y="629.02" zvalue="2347"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874432053252" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447543812" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447543812"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,915.833,660.593) scale(-2.15278,2.10494) translate(-1327.42,-330.189)" width="24" x="890" y="629.02"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="99">
   <use class="kv35" height="26" transform="rotate(270,1019.78,319.381) scale(0.811869,1) translate(235.181,0)" width="12" x="1014.909204830257" xlink:href="#Accessory:避雷器_0" y="306.3805157638612" zvalue="2771"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV蹇乔线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1019.78,319.381) scale(0.811869,1) translate(235.181,0)" width="12" x="1014.909204830257" y="306.3805157638612"/></g>
  <g id="1">
   <use class="kv35" height="30" transform="rotate(0,914.286,375.714) scale(1.42857,1.42857) translate(-267.857,-106.286)" width="30" x="892.8571428571429" xlink:href="#Accessory:配电室_0" y="354.2857142857143" zvalue="3872"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV高压计量装置"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,914.286,375.714) scale(1.42857,1.42857) translate(-267.857,-106.286)" width="30" x="892.8571428571429" y="354.2857142857143"/></g>
  <g id="15">
   <use class="kv35" height="26" transform="rotate(0,1161.44,546.259) scale(-0.811869,1) translate(-2593.14,0)" width="12" x="1156.568625120113" xlink:href="#Accessory:避雷器_0" y="533.2585193220206" zvalue="3886"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1161.44,546.259) scale(-0.811869,1) translate(-2593.14,0)" width="12" x="1156.568625120113" y="533.2585193220206"/></g>
  <g id="39">
   <use class="kv10" height="22" transform="rotate(0,747.722,1055.11) scale(1.11111,1.11111) translate(-74.1056,-104.289)" width="12" x="741.0555555555555" xlink:href="#Accessory:传输线_0" y="1042.888888888889" zvalue="3909"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV罗家山线放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,747.722,1055.11) scale(1.11111,1.11111) translate(-74.1056,-104.289)" width="12" x="741.0555555555555" y="1042.888888888889"/></g>
  <g id="63">
   <use class="kv10" height="26" transform="rotate(0,768.94,1053.76) scale(-0.811869,1) translate(-1717.19,0)" width="12" x="764.0686251201126" xlink:href="#Accessory:避雷器_0" y="1040.758519322021" zvalue="3911"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV罗家山线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,768.94,1053.76) scale(-0.811869,1) translate(-1717.19,0)" width="12" x="764.0686251201126" y="1040.758519322021"/></g>
  <g id="79">
   <use class="kv10" height="22" transform="rotate(0,931.294,1055.83) scale(1.11111,1.11111) translate(-92.4627,-104.36)" width="12" x="924.6269841269841" xlink:href="#Accessory:传输线_0" y="1043.603174603174" zvalue="3926"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV雄鲁么线放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,931.294,1055.83) scale(1.11111,1.11111) translate(-92.4627,-104.36)" width="12" x="924.6269841269841" y="1043.603174603174"/></g>
  <g id="77">
   <use class="kv10" height="26" transform="rotate(0,952.511,1054.47) scale(-0.811869,1) translate(-2126.87,0)" width="12" x="947.6400536915412" xlink:href="#Accessory:避雷器_0" y="1041.472805036306" zvalue="3927"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV雄鲁么线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,952.511,1054.47) scale(-0.811869,1) translate(-2126.87,0)" width="12" x="947.6400536915412" y="1041.472805036306"/></g>
  <g id="120">
   <use class="kv10" height="30" transform="rotate(0,1088.5,1024.77) scale(2.44444,2.44444) translate(-621.535,-583.88)" width="30" x="1051.828609986505" xlink:href="#Accessory:带熔断器35kVPT11_0" y="988.103913630229" zvalue="3952"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1088.5,1024.77) scale(2.44444,2.44444) translate(-621.535,-583.88)" width="30" x="1051.828609986505" y="988.103913630229"/></g>
  <g id="118">
   <use class="kv10" height="26" transform="rotate(0,1114.74,995.046) scale(0.811869,1) translate(257.185,0)" width="12" x="1109.864381232802" xlink:href="#Accessory:避雷器_0" y="982.0458329028621" zvalue="3954"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#Ⅰ母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1114.74,995.046) scale(0.811869,1) translate(257.185,0)" width="12" x="1109.864381232802" y="982.0458329028621"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="406">
   <use class="kv35" height="30" transform="rotate(0,914.346,278.821) scale(2.32143,0.625) translate(-515.849,161.668)" width="7" x="906.2212301587301" xlink:href="#ACLineSegment:线路_0" y="269.4464285714285" zvalue="3055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV蹇乔线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,914.346,278.821) scale(2.32143,0.625) translate(-515.849,161.668)" width="7" x="906.2212301587301" y="269.4464285714285"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="420">
   <use class="kv35" height="30" transform="rotate(270,884.544,542.5) scale(0.763595,-0.763595) translate(272.432,-1256.5)" width="12" x="879.9620102716649" xlink:href="#GroundDisconnector:地刀12_0" y="531.0455954542551" zvalue="3085"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧30117"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,884.544,542.5) scale(0.763595,-0.763595) translate(272.432,-1256.5)" width="12" x="879.9620102716649" y="531.0455954542551"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="61">
   <path class="kv35" d="M 914.35 483.63 L 914.35 437.5" stroke-width="1" zvalue="3598"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.35 483.63 L 914.35 437.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 915.81 631.28 L 915.81 601.26" stroke-width="1" zvalue="3601"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@0" LinkObjectIDznd="339@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.81 631.28 L 915.81 601.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 895.48 542.51 L 914.99 542.51" stroke-width="1" zvalue="3607"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="420@0" LinkObjectIDznd="23" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.48 542.51 L 914.99 542.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 914.35 394.51 L 914.35 437.5" stroke-width="1" zvalue="3873"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.35 394.51 L 914.35 437.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv35" d="M 914.35 395.71 L 914.35 288.1" stroke-width="1" zvalue="3874"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3" LinkObjectIDznd="406@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.35 395.71 L 914.35 288.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 951.69 319.41 L 914.35 319.41" stroke-width="1" zvalue="3875"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333@0" LinkObjectIDznd="4" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.69 319.41 L 914.35 319.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv35" d="M 975.52 319.38 L 1007.41 319.35" stroke-width="1" zvalue="3877"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333@1" LinkObjectIDznd="99@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 975.52 319.38 L 1007.41 319.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 1126.23 550.15 L 1126.23 500.91" stroke-width="1" zvalue="3883"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="13@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.23 550.15 L 1126.23 500.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 1125.93 477.07 L 1125.93 437.5" stroke-width="1" zvalue="3884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="43@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125.93 477.07 L 1125.93 437.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 1126.23 518.75 L 1161.41 518.75 L 1161.41 533.89" stroke-width="1" zvalue="3887"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.23 518.75 L 1161.41 518.75 L 1161.41 533.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 914.99 580.02 L 914.99 507.46" stroke-width="1" zvalue="3889"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="339@0" LinkObjectIDznd="336@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.99 580.02 L 914.99 507.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 916.24 730.02 L 916.24 690.16" stroke-width="1" zvalue="3895"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="340@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.24 730.02 L 916.24 690.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 916.9 791.13 L 916.9 751.26" stroke-width="1" zvalue="3896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="26@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.9 791.13 L 916.9 751.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 916.92 814.96 L 916.92 859.14" stroke-width="1" zvalue="3897"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@1" LinkObjectIDznd="512@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.92 814.96 L 916.92 859.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 747.56 918.15 L 747.56 947.16" stroke-width="1" zvalue="3905"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@1" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.56 918.15 L 747.56 947.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 748.84 968.4 L 748.84 995.21" stroke-width="1" zvalue="3906"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.84 968.4 L 748.84 995.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 748.34 1019.04 L 748.34 1044" stroke-width="1" zvalue="3907"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.34 1019.04 L 748.34 1044" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 747.54 894.32 L 747.54 859.14" stroke-width="1" zvalue="3908"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="512@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.54 894.32 L 747.54 859.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 748.34 1026.25 L 768.91 1026.25 L 768.91 1041.39" stroke-width="1" zvalue="3912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.34 1026.25 L 768.91 1026.25 L 768.91 1041.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 748.34 1044 L 748.39 1081.35" stroke-width="1" zvalue="3914"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.34 1044 L 748.39 1081.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv10" d="M 931.14 918.86 L 931.14 947.88" stroke-width="1" zvalue="3922"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@1" LinkObjectIDznd="87@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.14 918.86 L 931.14 947.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 932.41 969.12 L 932.41 995.92" stroke-width="1" zvalue="3923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@1" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.41 969.12 L 932.41 995.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 931.91 1019.75 L 931.91 1044.71" stroke-width="1" zvalue="3924"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="79@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.91 1019.75 L 931.91 1044.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 931.11 895.03 L 931.11 859.14" stroke-width="1" zvalue="3925"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="512@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.11 895.03 L 931.11 859.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 931.91 1026.96 L 952.48 1026.96 L 952.48 1042.11" stroke-width="1" zvalue="3928"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81" LinkObjectIDznd="77@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.91 1026.96 L 952.48 1026.96 L 952.48 1042.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 931.91 1044.71 L 931.96 1082.06" stroke-width="1" zvalue="3931"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81" LinkObjectIDznd="75@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.91 1044.71 L 931.96 1082.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 1088.11 907.04 L 1088.11 859.14" stroke-width="1" zvalue="3955"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="512@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.11 907.04 L 1088.11 859.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv10" d="M 1088.14 930.87 L 1088.14 990.79" stroke-width="1" zvalue="3956"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="120@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.14 930.87 L 1088.14 990.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1088.14 950.65 L 1114.76 950.65 L 1114.76 982.68" stroke-width="1" zvalue="3957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.14 950.65 L 1114.76 950.65 L 1114.76 982.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="11">
   <use class="kv35" height="30" transform="rotate(0,1126.23,609.89) scale(4.25,4.22222) translate(-828.733,-417.109)" width="20" x="1083.728070175439" xlink:href="#EnergyConsumer:站用变带熔断器_0" y="546.5570175438597" zvalue="3881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1126.23,609.89) scale(4.25,4.22222) translate(-828.733,-417.109)" width="20" x="1083.728070175439" y="546.5570175438597"/></g>
  <g id="65">
   <use class="kv10" height="30" transform="rotate(0,748.386,1102.16) scale(1.5625,-1.54167) translate(-266.044,-1808.95)" width="12" x="739.0113636363637" xlink:href="#EnergyConsumer:负荷_0" y="1079.034090909091" zvalue="3913"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV罗家山线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,748.386,1102.16) scale(1.5625,-1.54167) translate(-266.044,-1808.95)" width="12" x="739.0113636363637" y="1079.034090909091"/></g>
  <g id="75">
   <use class="kv10" height="30" transform="rotate(0,931.958,1102.87) scale(1.5625,-1.54167) translate(-332.13,-1810.13)" width="12" x="922.5827922077922" xlink:href="#EnergyConsumer:负荷_0" y="1079.748376623377" zvalue="3929"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV雄鲁么线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.958,1102.87) scale(1.5625,-1.54167) translate(-332.13,-1810.13)" width="12" x="922.5827922077922" y="1079.748376623377"/></g>
 </g>
</svg>