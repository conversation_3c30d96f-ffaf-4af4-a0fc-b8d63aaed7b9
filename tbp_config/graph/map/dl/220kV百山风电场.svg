<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549600518146" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:线路PT三绕组_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="24" y2="24"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.98040845230975" x2="31.98040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.66666666666667" y2="32.66666666666667"/>
   <ellipse cx="12.01" cy="28.48" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666664" x2="15.16666666666666" y1="28.91666666666667" y2="28.91666666666667"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(85,255,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路发电机1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="30"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.3333333333333321" y2="29.99999999999999"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="220kV百山风电场" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="37" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.667,72.6607) scale(1,1) translate(0,0)" writing-mode="lr" x="180.67" xml:space="preserve" y="86.16" zvalue="1684">      百山风电场</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,823.36,369.673) scale(1,1) translate(0,0)" writing-mode="lr" x="823.36" xml:space="preserve" y="374.17" zvalue="4">231</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.87,318.142) scale(1,1) translate(0,0)" writing-mode="lr" x="820.87" xml:space="preserve" y="322.64" zvalue="6">2316</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.35,274.071) scale(1,1) translate(0,0)" writing-mode="lr" x="900.35" xml:space="preserve" y="278.57" zvalue="8">23167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.678,321.673) scale(1,1) translate(0,0)" writing-mode="lr" x="899.6799999999999" xml:space="preserve" y="326.17" zvalue="10">23160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.87,422.848) scale(1,1) translate(0,0)" writing-mode="lr" x="820.87" xml:space="preserve" y="427.35" zvalue="12">2311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.751,381.553) scale(1,1) translate(0,0)" writing-mode="lr" x="899.75" xml:space="preserve" y="386.05" zvalue="14">23117</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,539.738,451.994) scale(1,1) translate(0,0)" writing-mode="lr" x="539.74" xml:space="preserve" y="457.99" zvalue="60">220kV#Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.602,646.643) scale(1,1) translate(0,0)" writing-mode="lr" x="777.6018312552042" xml:space="preserve" y="652.642551134503" zvalue="83">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,826.071,544.173) scale(1,1) translate(0,0)" writing-mode="lr" x="826.0700000000001" xml:space="preserve" y="548.67" zvalue="393">201</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.87,601.892) scale(1,1) translate(0,0)" writing-mode="lr" x="821.87" xml:space="preserve" y="606.39" zvalue="395">2016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.699,613.615) scale(1,1) translate(0,0)" writing-mode="lr" x="892.7" xml:space="preserve" y="618.12" zvalue="397">20167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897.026,564.217) scale(1,1) translate(0,0)" writing-mode="lr" x="897.03" xml:space="preserve" y="568.72" zvalue="399">20160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.87,488.348) scale(1,1) translate(0,0)" writing-mode="lr" x="821.87" xml:space="preserve" y="492.85" zvalue="401">2011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897.1,494.598) scale(1,1) translate(0,0)" writing-mode="lr" x="897.1" xml:space="preserve" y="499.1" zvalue="403">20117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728.786,700.304) scale(1,1) translate(0,0)" writing-mode="lr" x="728.79" xml:space="preserve" y="704.8" zvalue="425">2010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="440" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.8,629.948) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.795855483113" xml:space="preserve" y="635.9481069212524" zvalue="1053">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="429" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1297.28,544.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1297.28" xml:space="preserve" y="548.67" zvalue="1056">202</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="415" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.2,599.165) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.2" xml:space="preserve" y="603.67" zvalue="1058">2026</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="414" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1371.03,614.615) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.03" xml:space="preserve" y="619.12" zvalue="1060">20267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1368.36,564.217) scale(1,1) translate(0,0)" writing-mode="lr" x="1368.36" xml:space="preserve" y="568.72" zvalue="1062">20260</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="403" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.2,488.348) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.2" xml:space="preserve" y="492.85" zvalue="1064">2021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="396" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1368.43,494.598) scale(1,1) translate(0,0)" writing-mode="lr" x="1368.43" xml:space="preserve" y="499.1" zvalue="1066">20217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="374" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1182.87,703.737) scale(1,1) translate(0,0)" writing-mode="lr" x="1182.87" xml:space="preserve" y="708.24" zvalue="1076">2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="508" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.214,170.425) scale(1,1) translate(0,0)" writing-mode="lr" x="848.21" xml:space="preserve" y="176.42" zvalue="1586">220kV蒲百线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1320.89,168.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.89" xml:space="preserve" y="174.21" zvalue="1713">预留</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.29,356.588) scale(1,1) translate(0,0)" writing-mode="lr" x="1076.29" xml:space="preserve" y="361.09" zvalue="1718">2901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1162.89,295.036) scale(1,1) translate(0,0)" writing-mode="lr" x="1162.89" xml:space="preserve" y="299.54" zvalue="1719">29017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1151.56,359.826) scale(1,1) translate(0,0)" writing-mode="lr" x="1151.56" xml:space="preserve" y="364.33" zvalue="1720">29010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,116.018,312.464) scale(1,1) translate(0,0)" writing-mode="lr" x="116.02" xml:space="preserve" y="318.46" zvalue="2008">负荷总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,116.018,348.214) scale(1,1) translate(0,0)" writing-mode="lr" x="116.02" xml:space="preserve" y="354.21" zvalue="2009">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,116.018,379.214) scale(1,1) translate(0,0)" writing-mode="lr" x="116.02" xml:space="preserve" y="385.21" zvalue="2010">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,128.268,196.893) scale(1,1) translate(0,0)" writing-mode="lr" x="128.27" xml:space="preserve" y="202.89" zvalue="2011">事故</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.056,266.353) scale(1,1) translate(0,0)" writing-mode="lr" x="143.06" xml:space="preserve" y="272.35" zvalue="2012">是否失压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.981,266.392) scale(1,1) translate(0,0)" writing-mode="lr" x="222.98" xml:space="preserve" y="272.39" zvalue="2013">失压排除</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.109,196.893) scale(1,1) translate(0,0)" writing-mode="lr" x="187.11" xml:space="preserve" y="202.89" zvalue="2014">异常</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245.95,196.893) scale(1,1) translate(0,0)" writing-mode="lr" x="245.95" xml:space="preserve" y="202.89" zvalue="2015">告知</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,246.446,348.214) scale(1,1) translate(0,0)" writing-mode="lr" x="246.45" xml:space="preserve" y="354.21" zvalue="2021">YK14-015，庆华村-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,212.036,380.214) scale(1,1) translate(0,0)" writing-mode="lr" x="212.04" xml:space="preserve" y="386.21" zvalue="2022">2022-12-06</text>
 </g>
 <g id="BreakerClass">
  <g id="53">
   <use class="kv220" height="20" transform="rotate(0,850.344,370.673) scale(1.828,1.8835) translate(-381.027,-165.038)" width="10" x="841.2041842174319" xlink:href="#Breaker:开关_0" y="351.8380402299159" zvalue="3"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924798382083" ObjectName="220kV蒲百线231"/>
   <cge:TPSR_Ref TObjectID="6473924798382083"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,850.344,370.673) scale(1.828,1.8835) translate(-381.027,-165.038)" width="10" x="841.2041842174319" y="351.8380402299159"/></g>
  <g id="119">
   <use class="kv220" height="20" transform="rotate(0,851.241,545.173) scale(1.828,1.8835) translate(-381.433,-246.891)" width="10" x="842.1014649440893" xlink:href="#Breaker:开关_0" y="526.3380402299159" zvalue="392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924798447619" ObjectName="#1主变220kV侧201"/>
   <cge:TPSR_Ref TObjectID="6473924798447619"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,851.241,545.173) scale(1.828,1.8835) translate(-381.433,-246.891)" width="10" x="842.1014649440893" y="526.3380402299159"/></g>
  <g id="493">
   <use class="kv220" height="20" transform="rotate(0,1322.45,545.173) scale(1.828,1.8835) translate(-594.867,-246.891)" width="10" x="1313.305890920936" xlink:href="#Breaker:开关_0" y="526.3380402299159" zvalue="1055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924798644227" ObjectName="#2主变220kV侧202"/>
   <cge:TPSR_Ref TObjectID="6473924798644227"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.45,545.173) scale(1.828,1.8835) translate(-594.867,-246.891)" width="10" x="1313.305890920936" y="526.3380402299159"/></g>
  <g id="118">
   <use class="kv220" height="20" transform="rotate(0,1323.02,368.463) scale(1.828,1.8835) translate(-595.129,-164.001)" width="10" x="1313.882755646003" xlink:href="#Breaker:开关_0" y="349.6277227695983" zvalue="1691"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924798775299" ObjectName="预留231"/>
   <cge:TPSR_Ref TObjectID="6473924798775299"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1323.02,368.463) scale(1.828,1.8835) translate(-595.129,-164.001)" width="10" x="1313.882755646003" y="349.6277227695983"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="58">
   <use class="kv220" height="30" transform="rotate(0,850.25,316.535) scale(1.11133,0.814667) translate(-84.343,69.2304)" width="15" x="841.9152978202771" xlink:href="#Disconnector:刀闸_0" y="304.3148288740787" zvalue="5"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478107651" ObjectName="220kV蒲百线2316"/>
   <cge:TPSR_Ref TObjectID="6192452478107651"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,850.25,316.535) scale(1.11133,0.814667) translate(-84.343,69.2304)" width="15" x="841.9152978202771" y="304.3148288740787"/></g>
  <g id="13">
   <use class="kv220" height="30" transform="rotate(0,850.113,422.553) scale(1.11133,-0.814667) translate(-84.3292,-944.016)" width="15" x="841.7775583566757" xlink:href="#Disconnector:刀闸_0" y="410.3333837501856" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452477779971" ObjectName="220kV蒲百线2311"/>
   <cge:TPSR_Ref TObjectID="6192452477779971"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,850.113,422.553) scale(1.11133,-0.814667) translate(-84.3292,-944.016)" width="15" x="841.7775583566757" y="410.3333837501856"/></g>
  <g id="110">
   <use class="kv220" height="30" transform="rotate(0,851.096,600.285) scale(1.11133,-0.814667) translate(-84.4278,-1339.91)" width="15" x="842.7614489682255" xlink:href="#Disconnector:刀闸_0" y="588.0648288740787" zvalue="394"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478697475" ObjectName="#1主变220kV侧2016"/>
   <cge:TPSR_Ref TObjectID="6192452478697475"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,851.096,600.285) scale(1.11133,-0.814667) translate(-84.4278,-1339.91)" width="15" x="842.7614489682255" y="588.0648288740787"/></g>
  <g id="41">
   <use class="kv220" height="30" transform="rotate(0,851.113,488.053) scale(1.11133,0.814667) translate(-84.4294,108.25)" width="15" x="842.7775575474329" xlink:href="#Disconnector:刀闸_0" y="475.8333837501856" zvalue="400"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478369795" ObjectName="#1主变220kV侧2011"/>
   <cge:TPSR_Ref TObjectID="6192452478369795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,851.113,488.053) scale(1.11133,0.814667) translate(-84.4294,108.25)" width="15" x="842.7775575474329" y="475.8333837501856"/></g>
  <g id="492">
   <use class="kv220" height="30" transform="rotate(0,1322.43,600.285) scale(1.11133,-0.814667) translate(-131.646,-1339.91)" width="15" x="1314.094782301559" xlink:href="#Disconnector:刀闸_0" y="588.0648288740787" zvalue="1057"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452479811587" ObjectName="#2主变220kV侧2026"/>
   <cge:TPSR_Ref TObjectID="6192452479811587"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.43,600.285) scale(1.11133,-0.814667) translate(-131.646,-1339.91)" width="15" x="1314.094782301559" y="588.0648288740787"/></g>
  <g id="489">
   <use class="kv220" height="30" transform="rotate(0,1322.45,488.053) scale(1.11133,0.814667) translate(-131.648,108.25)" width="15" x="1314.110890880766" xlink:href="#Disconnector:刀闸_0" y="475.8333837501856" zvalue="1063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452479483907" ObjectName="#2主变220kV侧2021"/>
   <cge:TPSR_Ref TObjectID="6192452479483907"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.45,488.053) scale(1.11133,0.814667) translate(-131.648,108.25)" width="15" x="1314.110890880766" y="475.8333837501856"/></g>
  <g id="116">
   <use class="kv220" height="30" transform="rotate(0,1322.93,314.325) scale(1.11133,0.814667) translate(-131.696,68.7275)" width="15" x="1314.593869248848" xlink:href="#Disconnector:刀闸_0" y="302.1045114137611" zvalue="1693"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452480991236" ObjectName="预留2316"/>
   <cge:TPSR_Ref TObjectID="6192452480991236"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.93,314.325) scale(1.11133,0.814667) translate(-131.696,68.7275)" width="15" x="1314.593869248848" y="302.1045114137611"/></g>
  <g id="109">
   <use class="kv220" height="30" transform="rotate(0,1322.79,420.343) scale(1.11133,-0.814667) translate(-131.682,-939.092)" width="15" x="1314.456129785247" xlink:href="#Disconnector:刀闸_0" y="408.123066289868" zvalue="1699"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452480663556" ObjectName="预留2311"/>
   <cge:TPSR_Ref TObjectID="6192452480663556"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.79,420.343) scale(1.11133,-0.814667) translate(-131.682,-939.092)" width="15" x="1314.456129785247" y="408.123066289868"/></g>
  <g id="128">
   <use class="kv220" height="30" transform="rotate(0,1101.62,357.588) scale(1.11133,0.814667) translate(-109.525,78.5698)" width="15" x="1093.285714285714" xlink:href="#Disconnector:刀闸_0" y="345.3679535772485" zvalue="1717"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452481318915" ObjectName="220kV母线PT2901"/>
   <cge:TPSR_Ref TObjectID="6192452481318915"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1101.62,357.588) scale(1.11133,0.814667) translate(-109.525,78.5698)" width="15" x="1093.285714285714" y="345.3679535772485"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="125">
   <use class="kv220" height="30" transform="rotate(270,899.518,290.308) scale(-1.0125,0.866) translate(-1787.86,42.9106)" width="12" x="893.4432832554407" xlink:href="#GroundDisconnector:地刀12_0" y="277.3175895825445" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478042115" ObjectName="220kV蒲百线23167"/>
   <cge:TPSR_Ref TObjectID="6192452478042115"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,899.518,290.308) scale(-1.0125,0.866) translate(-1787.86,42.9106)" width="12" x="893.4432832554407" y="277.3175895825445"/></g>
  <g id="14">
   <use class="kv220" height="30" transform="rotate(270,899.518,338.348) scale(-1.0125,0.866) translate(-1787.86,50.344)" width="12" x="893.4432832436569" xlink:href="#GroundDisconnector:地刀12_0" y="325.357505548931" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452477911044" ObjectName="220kV蒲百线23160"/>
   <cge:TPSR_Ref TObjectID="6192452477911044"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,899.518,338.348) scale(-1.0125,0.866) translate(-1787.86,50.344)" width="12" x="893.4432832436569" y="325.357505548931"/></g>
  <g id="268">
   <use class="kv220" height="30" transform="rotate(270,899.518,398.553) scale(-1.0125,0.866) translate(-1787.86,59.6599)" width="12" x="893.4432832541753" xlink:href="#GroundDisconnector:地刀12_0" y="385.5633839634487" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452477714435" ObjectName="220kV蒲百线23117"/>
   <cge:TPSR_Ref TObjectID="6192452477714435"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,899.518,398.553) scale(-1.0125,0.866) translate(-1787.86,59.6599)" width="12" x="893.4432832541753" y="385.5633839634487"/></g>
  <g id="101">
   <use class="kv220" height="30" transform="rotate(270,891.867,629.852) scale(-1.0125,0.866) translate(-1772.65,95.4497)" width="12" x="885.7920436314173" xlink:href="#GroundDisconnector:地刀12_0" y="616.8617072296033" zvalue="396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478631939" ObjectName="#1主变220kV侧20167"/>
   <cge:TPSR_Ref TObjectID="6192452478631939"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,891.867,629.852) scale(-1.0125,0.866) translate(-1772.65,95.4497)" width="12" x="885.7920436314173" y="616.8617072296033"/></g>
  <g id="45">
   <use class="kv220" height="30" transform="rotate(270,896.867,580.892) scale(-1.0125,0.866) translate(-1782.59,87.8739)" width="12" x="890.7920436314173" xlink:href="#GroundDisconnector:地刀12_0" y="567.9016231959897" zvalue="398"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478500867" ObjectName="#1主变220kV侧20160"/>
   <cge:TPSR_Ref TObjectID="6192452478500867"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,896.867,580.892) scale(-1.0125,0.866) translate(-1782.59,87.8739)" width="12" x="890.7920436314173" y="567.9016231959897"/></g>
  <g id="40">
   <use class="kv220" height="30" transform="rotate(270,896.867,511.598) scale(-1.0125,0.866) translate(-1782.59,77.1517)" width="12" x="890.7920436314173" xlink:href="#GroundDisconnector:地刀12_0" y="498.6075016105075" zvalue="402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478304259" ObjectName="#1主变220kV侧20117"/>
   <cge:TPSR_Ref TObjectID="6192452478304259"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,896.867,511.598) scale(-1.0125,0.866) translate(-1782.59,77.1517)" width="12" x="890.7920436314173" y="498.6075016105075"/></g>
  <g id="1187">
   <use class="kv220" height="40" transform="rotate(0,771.323,704.737) scale(1,-1) translate(0,-1409.47)" width="40" x="751.3232142857144" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="684.7371417454311" zvalue="424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478828547" ObjectName="#1主变220kV侧2010"/>
   <cge:TPSR_Ref TObjectID="6192452478828547"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,771.323,704.737) scale(1,-1) translate(0,-1409.47)" width="40" x="751.3232142857144" y="684.7371417454311"/></g>
  <g id="491">
   <use class="kv220" height="30" transform="rotate(270,1370.2,630.852) scale(-1.0125,0.866) translate(-2723.41,95.6045)" width="12" x="1364.125376964751" xlink:href="#GroundDisconnector:地刀12_0" y="617.8617072296033" zvalue="1059"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452479746051" ObjectName="#2主变220kV侧20267"/>
   <cge:TPSR_Ref TObjectID="6192452479746051"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1370.2,630.852) scale(-1.0125,0.866) translate(-2723.41,95.6045)" width="12" x="1364.125376964751" y="617.8617072296033"/></g>
  <g id="490">
   <use class="kv220" height="30" transform="rotate(270,1368.2,580.892) scale(-1.0125,0.866) translate(-2719.43,87.8739)" width="12" x="1362.125376964751" xlink:href="#GroundDisconnector:地刀12_0" y="567.9016231959897" zvalue="1061"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452479614979" ObjectName="#2主变220kV侧20260"/>
   <cge:TPSR_Ref TObjectID="6192452479614979"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1368.2,580.892) scale(-1.0125,0.866) translate(-2719.43,87.8739)" width="12" x="1362.125376964751" y="567.9016231959897"/></g>
  <g id="488">
   <use class="kv220" height="30" transform="rotate(270,1368.2,511.598) scale(-1.0125,0.866) translate(-2719.43,77.1517)" width="12" x="1362.125376964751" xlink:href="#GroundDisconnector:地刀12_0" y="498.6075016105075" zvalue="1065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452479418371" ObjectName="#2主变220kV侧20217"/>
   <cge:TPSR_Ref TObjectID="6192452479418371"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1368.2,511.598) scale(-1.0125,0.866) translate(-2719.43,77.1517)" width="12" x="1362.125376964751" y="498.6075016105075"/></g>
  <g id="480">
   <use class="kv220" height="40" transform="rotate(0,1225.62,704.737) scale(1,-1) translate(0,-1409.47)" width="40" x="1205.619047619048" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="684.7371416091919" zvalue="1074"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452479287300" ObjectName="#2主变220kV侧2020"/>
   <cge:TPSR_Ref TObjectID="6192452479287300"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1225.62,704.737) scale(1,-1) translate(0,-1409.47)" width="40" x="1205.619047619048" y="684.7371416091919"/></g>
  <g id="113">
   <use class="kv220" height="30" transform="rotate(270,1368.2,288.097) scale(-1.0125,0.866) translate(-2719.43,42.5686)" width="12" x="1362.121861889276" xlink:href="#GroundDisconnector:地刀12_0" y="275.1072721222269" zvalue="1695"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452480925700" ObjectName="预留23167"/>
   <cge:TPSR_Ref TObjectID="6192452480925700"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1368.2,288.097) scale(-1.0125,0.866) translate(-2719.43,42.5686)" width="12" x="1362.121861889276" y="275.1072721222269"/></g>
  <g id="111">
   <use class="kv220" height="30" transform="rotate(270,1368.87,336.137) scale(-1.0125,0.866) translate(-2720.76,50.002)" width="12" x="1362.794130513464" xlink:href="#GroundDisconnector:地刀12_0" y="323.1471880886134" zvalue="1697"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452480794628" ObjectName="预留23160"/>
   <cge:TPSR_Ref TObjectID="6192452480794628"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1368.87,336.137) scale(-1.0125,0.866) translate(-2720.76,50.002)" width="12" x="1362.794130513464" y="323.1471880886134"/></g>
  <g id="104">
   <use class="kv220" height="30" transform="rotate(270,1366.3,396.343) scale(-1.0125,0.866) translate(-2715.65,59.3179)" width="12" x="1360.220601101699" xlink:href="#GroundDisconnector:地刀12_0" y="383.353066503131" zvalue="1701"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452480598020" ObjectName="预留13117"/>
   <cge:TPSR_Ref TObjectID="6192452480598020"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1366.3,396.343) scale(-1.0125,0.866) translate(-2715.65,59.3179)" width="12" x="1360.220601101699" y="383.353066503131"/></g>
  <g id="124">
   <use class="kv220" height="30" transform="rotate(270,1142.89,317.611) scale(-1.0125,0.866) translate(-2271.59,47.1353)" width="12" x="1136.813706926141" xlink:href="#GroundDisconnector:地刀12_0" y="304.6207142857143" zvalue="1718"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452481253379" ObjectName="220kV母线PT29017"/>
   <cge:TPSR_Ref TObjectID="6192452481253379"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1142.89,317.611) scale(-1.0125,0.866) translate(-2271.59,47.1353)" width="12" x="1136.813706926141" y="304.6207142857143"/></g>
  <g id="123">
   <use class="kv220" height="30" transform="rotate(270,1143.56,379.401) scale(-1.0125,0.866) translate(-2272.93,56.6963)" width="12" x="1137.48597555033" xlink:href="#GroundDisconnector:地刀12_0" y="366.4106302521007" zvalue="1719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452481122307" ObjectName="220kV母线PT29010"/>
   <cge:TPSR_Ref TObjectID="6192452481122307"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1143.56,379.401) scale(-1.0125,0.866) translate(-2272.93,56.6963)" width="12" x="1137.48597555033" y="366.4106302521007"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="99">
   <use class="kv220" height="26" transform="rotate(270,902.162,255.623) scale(1.03261,1.20262) translate(-28.2937,-40.4338)" width="12" x="895.9666890314012" xlink:href="#Accessory:避雷器_0" y="239.988576604555" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478173187" ObjectName="220kV蒲百线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,902.162,255.623) scale(1.03261,1.20262) translate(-28.2937,-40.4338)" width="12" x="895.9666890314012" y="239.988576604555"/></g>
  <g id="1">
   <use class="kv220" height="30" transform="rotate(90,797.429,256.143) scale(1.75714,1.75714) translate(-332.25,-99.0134)" width="30" x="771.0714285714287" xlink:href="#Accessory:PT789_0" y="229.7857142857143" zvalue="967"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452478959619" ObjectName="220kV蒲百线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,797.429,256.143) scale(1.75714,1.75714) translate(-332.25,-99.0134)" width="30" x="771.0714285714287" y="229.7857142857143"/></g>
  <g id="103">
   <use class="kv220" height="26" transform="rotate(270,1363.38,253.412) scale(1.03261,1.20262) translate(-42.8584,-40.0614)" width="12" x="1357.18134755571" xlink:href="#Accessory:避雷器_0" y="237.7782591442373" zvalue="1703"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452480466947" ObjectName="预留避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1363.38,253.412) scale(1.03261,1.20262) translate(-42.8584,-40.0614)" width="12" x="1357.18134755571" y="237.7782591442373"/></g>
  <g id="93">
   <use class="kv220" height="30" transform="rotate(90,1283.09,253.45) scale(1.75714,1.75714) translate(-541.519,-97.8532)" width="30" x="1256.732142857143" xlink:href="#Accessory:PT789_0" y="227.0932539682539" zvalue="1709"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452480401412" ObjectName="预留PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1283.09,253.45) scale(1.75714,1.75714) translate(-541.519,-97.8532)" width="30" x="1256.732142857143" y="227.0932539682539"/></g>
  <g id="129">
   <use class="kv220" height="26" transform="rotate(0,1102.71,291.286) scale(-0.571442,-0.879121) translate(-3034.99,-624.195)" width="12" x="1099.285675048828" xlink:href="#Accessory:避雷器_0" y="279.8571428571428" zvalue="1723"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452481384451" ObjectName="220kV母线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1102.71,291.286) scale(-0.571442,-0.879121) translate(-3034.99,-624.195)" width="12" x="1099.285675048828" y="279.8571428571428"/></g>
  <g id="1569">
   <use class="kv220" height="40" transform="rotate(90,1044.94,316.786) scale(1.31786,1.31786) translate(-245.674,-70.0492)" width="40" x="1018.582417582418" xlink:href="#Accessory:线路PT三绕组_0" y="290.4285714285714" zvalue="1725"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452481449987" ObjectName="110kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1044.94,316.786) scale(1.31786,1.31786) translate(-245.674,-70.0492)" width="40" x="1018.582417582418" y="290.4285714285714"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="20">
   <path class="kv220" d="M 850.32 328.55 L 850.28 352.65" stroke-width="1" zvalue="35"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.32 328.55 L 850.28 352.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv220" d="M 850.47 388.66 L 850.47 410.54" stroke-width="1" zvalue="36"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="13@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.47 388.66 L 850.47 410.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv220" d="M 887.11 338.34 L 850.3 338.34" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@0" LinkObjectIDznd="20" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.11 338.34 L 850.3 338.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv220" d="M 887.11 398.54 L 850.47 398.54" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.11 398.54 L 850.47 398.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv220" d="M 850.21 434.37 L 850.21 452.62" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.21 434.37 L 850.21 452.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv220" d="M 851.21 476.24 L 851.21 452.62" stroke-width="1" zvalue="410"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.21 476.24 L 851.21 452.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv220" d="M 851.18 500.06 L 851.18 527.15" stroke-width="1" zvalue="411"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@1" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.18 500.06 L 851.18 527.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv220" d="M 851.16 588.27 L 851.16 563.16" stroke-width="1" zvalue="412"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@1" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.16 588.27 L 851.16 563.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv220" d="M 851.19 612.1 L 851.19 654.86" stroke-width="1" zvalue="413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.19 612.1 L 851.19 654.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv220" d="M 884.46 580.88 L 851.16 580.88" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.46 580.88 L 851.16 580.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv220" d="M 879.46 629.84 L 851.19 629.84" stroke-width="1" zvalue="416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.46 629.84 L 851.19 629.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv220" d="M 773.92 692.54 L 773.92 674.02 L 849.93 674.02" stroke-width="1" zvalue="425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1187@0" LinkObjectIDznd="84@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.92 692.54 L 773.92 674.02 L 849.93 674.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="486">
   <path class="kv220" d="M 1322.51 500.06 L 1322.38 527.15" stroke-width="1" zvalue="1068"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@1" LinkObjectIDznd="493@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.51 500.06 L 1322.38 527.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="485">
   <path class="kv220" d="M 1322.5 588.27 L 1322.57 563.16" stroke-width="1" zvalue="1069"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="492@1" LinkObjectIDznd="493@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.5 588.27 L 1322.57 563.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="484">
   <path class="kv220" d="M 1322.53 612.1 L 1322.55 645.57" stroke-width="1" zvalue="1070"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="492@0" LinkObjectIDznd="495@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.53 612.1 L 1322.55 645.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="346">
   <path class="kv220" d="M 1357.8 630.84 L 1322.54 630.84" stroke-width="1" zvalue="1451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="491@0" LinkObjectIDznd="484" MaxPinNum="2"/>
   </metadata>
  <path d="M 1357.8 630.84 L 1322.54 630.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="328">
   <path class="kv220" d="M 887.11 290.3 L 848.93 290.3" stroke-width="1" zvalue="1525"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="516" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.11 290.3 L 848.93 290.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="516">
   <path class="kv220" d="M 848.93 213.66 L 848.93 304.72" stroke-width="1" zvalue="1586"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="498@0" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 848.93 213.66 L 848.93 304.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="529">
   <path class="kv220" d="M 887.29 255.59 L 844.49 255.59" stroke-width="1" zvalue="1588"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="36" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.29 255.59 L 844.49 255.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="604">
   <path class="kv220" d="M 884.46 511.59 L 851.18 511.59" stroke-width="1" zvalue="1593"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.46 511.59 L 851.18 511.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="643">
   <path class="kv220" d="M 1355.8 511.59 L 1322.46 511.59" stroke-width="1" zvalue="1596"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="488@0" LinkObjectIDznd="486" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.8 511.59 L 1322.46 511.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="645">
   <path class="kv220" d="M 1355.8 580.88 L 1322.52 580.88" stroke-width="1" zvalue="1598"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="490@0" LinkObjectIDznd="485" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.8 580.88 L 1322.52 580.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv220" d="M 823.17 256.05 L 848.93 256.05" stroke-width="1" zvalue="1689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="516" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.17 256.05 L 848.93 256.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv220" d="M 1323 326.34 L 1322.96 350.44" stroke-width="1" zvalue="1704"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@1" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323 326.34 L 1322.96 350.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv220" d="M 1323.14 386.45 L 1323.14 408.33" stroke-width="1" zvalue="1705"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="109@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.14 386.45 L 1323.14 408.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv220" d="M 1356.46 336.13 L 1322.98 336.13" stroke-width="1" zvalue="1706"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="102" MaxPinNum="2"/>
   </metadata>
  <path d="M 1356.46 336.13 L 1322.98 336.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv220" d="M 1353.89 396.33 L 1323.14 396.33" stroke-width="1" zvalue="1707"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="100" MaxPinNum="2"/>
   </metadata>
  <path d="M 1353.89 396.33 L 1323.14 396.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv220" d="M 1322.89 432.16 L 1322.89 452.62" stroke-width="1" zvalue="1708"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="42@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.89 432.16 L 1322.89 452.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv220" d="M 1355.79 288.09 L 1321.61 288.09" stroke-width="1" zvalue="1710"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="90" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.79 288.09 L 1321.61 288.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv220" d="M 1321.61 211.45 L 1321.61 302.51" stroke-width="1" zvalue="1712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="116@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.61 211.45 L 1321.61 302.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv220" d="M 1348.5 253.38 L 1319.4 253.36" stroke-width="1" zvalue="1714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.5 253.38 L 1319.4 253.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv220" d="M 1308.83 253.36 L 1321.61 253.36" stroke-width="1" zvalue="1715"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="90" MaxPinNum="2"/>
   </metadata>
  <path d="M 1308.83 253.36 L 1321.61 253.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv220" d="M 1131.16 379.39 L 1101.69 379.39" stroke-width="1" zvalue="1720"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="138" MaxPinNum="2"/>
   </metadata>
  <path d="M 1131.16 379.39 L 1101.69 379.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv220" d="M 1130.48 317.6 L 1102.7 317.6" stroke-width="1" zvalue="1721"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="137" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.48 317.6 L 1102.7 317.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv220" d="M 1102.7 302.16 L 1102.7 345.77" stroke-width="1" zvalue="1726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.7 302.16 L 1102.7 345.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv220" d="M 1101.69 369.6 L 1101.69 452.62" stroke-width="1" zvalue="1727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@1" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1101.69 369.6 L 1101.69 452.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv220" d="M 1070.09 316.79 L 1102.82 316.79" stroke-width="1" zvalue="1728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1569@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 1070.09 316.79 L 1102.82 316.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv220" d="M 1228.22 692.54 L 1228.22 664.73 L 1322.53 664.73" stroke-width="1" zvalue="1731"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="480@0" LinkObjectIDznd="495@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1228.22 692.54 L 1228.22 664.73 L 1322.53 664.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv220" d="M 1322.54 476.24 L 1322.54 452.62" stroke-width="1" zvalue="1734"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.54 476.24 L 1322.54 452.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="42">
   <path class="kv220" d="M 601.4 452.62 L 1589.29 452.62" stroke-width="6" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674306228227" ObjectName="220kV#Ⅰ母"/>
   <cge:TPSR_Ref TObjectID="9288674306228227"/></metadata>
  <path d="M 601.4 452.62 L 1589.29 452.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="84">
   <g id="840">
    <use class="kv220" height="30" transform="rotate(0,849.912,693.909) scale(2.9283,2.80449) translate(-536.532,-419.413)" width="24" x="814.77" xlink:href="#PowerTransformer2:可调两卷变_0" y="651.84" zvalue="82"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874491625475" ObjectName="220"/>
    </metadata>
   </g>
   <g id="841">
    <use class="kv35" height="30" transform="rotate(0,849.912,693.909) scale(2.9283,2.80449) translate(-536.532,-419.413)" width="24" x="814.77" xlink:href="#PowerTransformer2:可调两卷变_1" y="651.84" zvalue="82"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874491691011" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399473496067" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399473496067"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,849.912,693.909) scale(2.9283,2.80449) translate(-536.532,-419.413)" width="24" x="814.77" y="651.84"/></g>
  <g id="495">
   <g id="4950">
    <use class="kv220" height="30" transform="rotate(0,1322.52,684.619) scale(2.9283,2.80449) translate(-847.746,-413.436)" width="24" x="1287.38" xlink:href="#PowerTransformer2:可调两卷变_0" y="642.55" zvalue="1052"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874491756547" ObjectName="220"/>
    </metadata>
   </g>
   <g id="4951">
    <use class="kv35" height="30" transform="rotate(0,1322.52,684.619) scale(2.9283,2.80449) translate(-847.746,-413.436)" width="24" x="1287.38" xlink:href="#PowerTransformer2:可调两卷变_1" y="642.55" zvalue="1052"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874491822083" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399473561603" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399473561603"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.52,684.619) scale(2.9283,2.80449) translate(-847.746,-413.436)" width="24" x="1287.38" y="642.55"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="453">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="453" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.022,702.034) scale(1,1) translate(0,0)" writing-mode="lr" x="934.02" xml:space="preserve" y="709.7" zvalue="1">油温:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="454">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="454" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,938.772,679.631) scale(1,1) translate(0,2.22301e-13)" writing-mode="lr" x="938.77" xml:space="preserve" y="685.17" zvalue="1">档位:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="465">
   <text Format="f3.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="465" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1395.77,686.536) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.77" xml:space="preserve" y="694.2" zvalue="1">油温:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="466">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" id="466" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1400.5,663.194) scale(1,1) translate(0,2.16827e-13)" writing-mode="lr" x="1400.5" xml:space="preserve" y="668.73" zvalue="1">档位:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="703">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="703" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,540.564,352.119) scale(1,1) translate(0,0)" writing-mode="lr" x="540.5599999999999" xml:space="preserve" y="358.39" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133720928260" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="704">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="704" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,540.564,369.619) scale(1,1) translate(0,0)" writing-mode="lr" x="540.5599999999999" xml:space="preserve" y="375.89" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133720993796" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="705">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="705" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,540.564,387.119) scale(1,1) translate(0,0)" writing-mode="lr" x="540.5599999999999" xml:space="preserve" y="393.39" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133721059332" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="706">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="706" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,540.564,422.119) scale(1,1) translate(0,0)" writing-mode="lr" x="540.5599999999999" xml:space="preserve" y="428.39" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133721190404" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="707">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="707" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,540.564,404.619) scale(1,1) translate(0,0)" writing-mode="lr" x="540.5599999999999" xml:space="preserve" y="410.89" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133721387012" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="718">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="718" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,847.206,101.744) scale(1,1) translate(1.80013e-13,0)" writing-mode="lr" x="847.21" xml:space="preserve" y="108.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133727481861" ObjectName="P"/>
   </metadata>
  </g>
  <g id="719">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="719" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,847.206,119.744) scale(1,1) translate(1.80013e-13,0)" writing-mode="lr" x="847.21" xml:space="preserve" y="126.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133727547397" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="720">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="720" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,847.206,137.744) scale(1,1) translate(1.80013e-13,0)" writing-mode="lr" x="847.21" xml:space="preserve" y="144.01" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133727612932" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="721">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="721" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850.109,152.452) scale(1,1) translate(1.80658e-13,0)" writing-mode="lr" x="850.11" xml:space="preserve" y="158.72" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133727875076" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="309">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="309" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,756.272,532.841) scale(1,1) translate(0,0)" writing-mode="lr" x="756.27" xml:space="preserve" y="539.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133721452548" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="310">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="310" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,756.272,549.841) scale(1,1) translate(0,0)" writing-mode="lr" x="756.27" xml:space="preserve" y="556.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133721518084" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="311">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="311" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,756.272,566.841) scale(1,1) translate(0,0)" writing-mode="lr" x="756.27" xml:space="preserve" y="573.01" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133721714692" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="312">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="312" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,766.051,756.226) scale(1,1) translate(0,0)" writing-mode="lr" x="766.05" xml:space="preserve" y="762.39" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133721583620" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="314">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="314" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,766.051,773.226) scale(1,1) translate(0,0)" writing-mode="lr" x="766.05" xml:space="preserve" y="779.39" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133721649156" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="315">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="315" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,766.051,790.226) scale(1,1) translate(0,0)" writing-mode="lr" x="766.05" xml:space="preserve" y="796.39" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133722042373" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="317">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="317" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1218.88,529.802) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.88" xml:space="preserve" y="535.97" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133724008453" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="319">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="319" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1218.88,546.802) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.88" xml:space="preserve" y="552.97" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133724073989" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="320">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="320" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1218.88,563.802) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.88" xml:space="preserve" y="569.97" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133724270596" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="323">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="323" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1243.66,753.186) scale(1,1) translate(0,0)" writing-mode="lr" x="1243.66" xml:space="preserve" y="759.35" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133724139525" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="325">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="325" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1243.66,770.186) scale(1,1) translate(0,0)" writing-mode="lr" x="1243.66" xml:space="preserve" y="776.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133724205061" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="326">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="326" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1243.66,787.186) scale(1,1) translate(0,0)" writing-mode="lr" x="1243.66" xml:space="preserve" y="793.35" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133724598277" ObjectName="LIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="241">
   <use height="30" stroke="rgb(255,255,255)" transform="rotate(0,126.768,165.179) scale(0.958333,0.916667) translate(4.88664,13.7662)" width="30" x="112.39" xlink:href="#State:红绿圆_0" y="151.43" zvalue="2016"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549600518146" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,126.768,165.179) scale(0.958333,0.916667) translate(4.88664,13.7662)" width="30" x="112.39" y="151.43"/></g>
  <g id="285">
   <use height="30" transform="rotate(0,188.196,165.179) scale(0.958333,0.916667) translate(7.55745,13.7662)" width="30" x="173.82" xlink:href="#State:红绿圆_0" y="151.43" zvalue="2017"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549600518146" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,188.196,165.179) scale(0.958333,0.916667) translate(7.55745,13.7662)" width="30" x="173.82" y="151.43"/></g>
  <g id="240">
   <use height="30" transform="rotate(0,248.196,165.179) scale(0.958333,0.916667) translate(10.1661,13.7662)" width="30" x="233.82" xlink:href="#State:红绿圆_0" y="151.43" zvalue="2018"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549600518146" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,248.196,165.179) scale(0.958333,0.916667) translate(10.1661,13.7662)" width="30" x="233.82" y="151.43"/></g>
  <g id="281">
   <use height="30" transform="rotate(0,145.696,237.929) scale(0.958333,0.916667) translate(5.70963,20.3799)" width="30" x="131.32" xlink:href="#State:红绿圆_0" y="224.18" zvalue="2019"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549600518146" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,145.696,237.929) scale(0.958333,0.916667) translate(5.70963,20.3799)" width="30" x="131.32" y="224.18"/></g>
  <g id="279">
   <use height="30" transform="rotate(0,225.196,239.929) scale(0.958333,0.916667) translate(9.16615,20.5617)" width="30" x="210.82" xlink:href="#State:红绿圆_0" y="226.18" zvalue="2020"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549600518146" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,225.196,239.929) scale(0.958333,0.916667) translate(9.16615,20.5617)" width="30" x="210.82" y="226.18"/></g>
 </g>
</svg>