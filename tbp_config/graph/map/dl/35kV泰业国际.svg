<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584592898" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:2绕组线路PT_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.4166666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="17" y1="28" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="17.5" y1="9.16666666666667" y2="9.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="9.166666666666671" y2="0.3333333333333321"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.5) scale(1,1) translate(0,0)" width="4" x="13" y="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="23.41666666666666" y2="9.083333333333332"/>
   <ellipse cx="15.15" cy="28.4" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="34" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.16666666666667" x2="17.16666666666667" y1="34.50000000000001" y2="34.50000000000001"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1节点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893005" x2="1.626337448559672" y1="3.853614126578682" y2="8.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893004" x2="12.54300411522634" y1="3.853614126578682" y2="8.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="1.066666666666674" y2="9.166666666666668"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893003" x2="7.084670781893003" y1="32.15000000000001" y2="35.16666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.00133744855967" x2="7.00133744855967" y1="26.75" y2="35.08333333333333"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.833333333333333" x2="7.001337448559672" y1="10.75" y2="27"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="0.7499999999999964" y2="35.08333333333334"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="2.083333333333333" y1="11.16666666666666" y2="25.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.133446357018218" x2="11.79254048508705" y1="11.12564285751284" y2="24.83570582669768"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="1.06666666666667" y2="4.083333333333332"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893003" x2="7.084670781893003" y1="32.15000000000001" y2="35.16666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV泰业国际" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,149.143,53.0417) scale(1,1) translate(0,0)" writing-mode="lr" x="149.14" xml:space="preserve" y="70.04000000000001" zvalue="4173">     泰业国际</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,461.357,448.607) scale(1,1) translate(0,0)" writing-mode="lr" x="461.36" xml:space="preserve" y="454.61" zvalue="2725">35kV#Ⅰ母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="382" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,726.592,737.395) scale(1,1) translate(0,0)" writing-mode="lr" x="726.59" xml:space="preserve" y="743.4" zvalue="2806">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="372" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,374.5,934.964) scale(1,1) translate(0,0)" writing-mode="lr" x="374.5" xml:space="preserve" y="940.96" zvalue="2808">10kV#Ⅰ母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="373" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1636.75,934.964) scale(1,1) translate(0,0)" writing-mode="lr" x="1636.75" xml:space="preserve" y="940.96" zvalue="2810">10kV#Ⅱ母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.578,683.266) scale(1,1) translate(0,0)" writing-mode="lr" x="851.58" xml:space="preserve" y="689.27" zvalue="3114">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.11,892.535) scale(1,1) translate(0,0)" writing-mode="lr" x="682.11" xml:space="preserve" y="897.03" zvalue="3135">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1566.86,446.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.86" xml:space="preserve" y="452.61" zvalue="3440">35kV#Ⅱ母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="407" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,499.895,630.19) scale(1,1) translate(0,0)" writing-mode="lr" x="499.9" xml:space="preserve" y="636.1900000000001" zvalue="3489">35kV#Ⅰ电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.5,877) scale(1,1) translate(0,0)" writing-mode="lr" x="880.5" xml:space="preserve" y="881.5" zvalue="3849">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1092.77,891.883) scale(1,1) translate(0,0)" writing-mode="lr" x="1092.77" xml:space="preserve" y="896.38" zvalue="3853">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,863.36,516.739) scale(1,1) translate(0,0)" writing-mode="lr" x="863.36" xml:space="preserve" y="521.24" zvalue="4004">3311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,509.36,516.739) scale(1,1) translate(0,0)" writing-mode="lr" x="509.36" xml:space="preserve" y="521.24" zvalue="4010">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,677.023,434.308) scale(1,1) translate(0,0)" writing-mode="lr" x="677.02" xml:space="preserve" y="438.81" zvalue="4015">3411</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.757,313.875) scale(1,1) translate(0,0)" writing-mode="lr" x="593.76" xml:space="preserve" y="318.38" zvalue="4017">34167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,679.157,389.712) scale(1,1) translate(0,0)" writing-mode="lr" x="679.16" xml:space="preserve" y="394.21" zvalue="4019">341</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.429,345.058) scale(1,1) translate(0,0)" writing-mode="lr" x="682.4299999999999" xml:space="preserve" y="349.56" zvalue="4028">3416</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.556,151.778) scale(1,1) translate(0,0)" writing-mode="lr" x="653.5599999999999" xml:space="preserve" y="157.78" zvalue="4035">35kV泰业国际线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="317" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1021.6,371.559) scale(1,1) translate(0,0)" writing-mode="lr" x="1021.6" xml:space="preserve" y="376.06" zvalue="4049">312</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.734,432.308) scale(1,1) translate(0,0)" writing-mode="lr" x="901.73" xml:space="preserve" y="436.81" zvalue="4051">3121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1122.98,435.058) scale(1,1) translate(0,0)" writing-mode="lr" x="1122.98" xml:space="preserve" y="439.56" zvalue="4053">3122</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1370.96,434.308) scale(1,1) translate(0,0)" writing-mode="lr" x="1370.96" xml:space="preserve" y="438.81" zvalue="4066">3422</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1287.76,313.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1287.76" xml:space="preserve" y="318.38" zvalue="4068">34267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1374.16,392.712) scale(1,1) translate(0,0)" writing-mode="lr" x="1374.16" xml:space="preserve" y="397.21" zvalue="4070">342</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.43,345.058) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.43" xml:space="preserve" y="349.56" zvalue="4074">3426</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="339" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1347.56,151.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1347.56" xml:space="preserve" y="157.78" zvalue="4078">35kV泰业国际Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="385" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1521.9,630.19) scale(1,1) translate(0,0)" writing-mode="lr" x="1521.9" xml:space="preserve" y="636.1900000000001" zvalue="4100">35kV#Ⅱ电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="384" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1531.36,516.739) scale(1,1) translate(0,0)" writing-mode="lr" x="1531.36" xml:space="preserve" y="521.24" zvalue="4102">3902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="394" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,674.955,622.308) scale(1,1) translate(0,0)" writing-mode="lr" x="674.96" xml:space="preserve" y="626.8099999999999" zvalue="4111">3016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="393" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,672.157,569.712) scale(1,1) translate(0,0)" writing-mode="lr" x="672.16" xml:space="preserve" y="574.21" zvalue="4113">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,680.441,517.058) scale(1,1) translate(0,0)" writing-mode="lr" x="680.4400000000001" xml:space="preserve" y="521.5599999999999" zvalue="4115">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="403" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,583.757,653.834) scale(1,1) translate(0,0)" writing-mode="lr" x="583.76" xml:space="preserve" y="658.33" zvalue="4120">30167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="483" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1224.59,726.395) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.59" xml:space="preserve" y="732.4" zvalue="4130">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="482" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.86,890.535) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.86" xml:space="preserve" y="895.03" zvalue="4134">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="481" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1328.96,622.308) scale(1,1) translate(0,0)" writing-mode="lr" x="1328.96" xml:space="preserve" y="626.8099999999999" zvalue="4143">3026</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="445" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.16,569.712) scale(1,1) translate(0,0)" writing-mode="lr" x="1326.16" xml:space="preserve" y="574.21" zvalue="4145">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="439" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1334.44,517.058) scale(1,1) translate(0,0)" writing-mode="lr" x="1334.44" xml:space="preserve" y="521.5599999999999" zvalue="4147">3022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="438" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1237.76,653.834) scale(1,1) translate(0,0)" writing-mode="lr" x="1237.76" xml:space="preserve" y="658.33" zvalue="4151">30267</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="97">
   <path class="kv35" d="M 428 467.96 L 969.43 467.96" stroke-width="3" zvalue="2724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243706884" ObjectName="35kV#Ⅰ母线"/>
   </metadata>
  <path d="M 428 467.96 L 969.43 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="370">
   <path class="kv10" d="M 342.89 955.96 L 947 955.96" stroke-width="3" zvalue="2807"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243772420" ObjectName="10kV#Ⅰ母线"/>
   </metadata>
  <path d="M 342.89 955.96 L 947 955.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="371">
   <path class="kv10" d="M 1045.39 955.96 L 1649.5 955.96" stroke-width="3" zvalue="2809"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243837956" ObjectName="10kV#Ⅱ母线"/>
   </metadata>
  <path d="M 1045.39 955.96 L 1649.5 955.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv35" d="M 1062.29 467.96 L 1605 467.96" stroke-width="3" zvalue="3439"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅱ母线"/>
   </metadata>
  <path d="M 1062.29 467.96 L 1605 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="369">
   <g id="3690">
    <use class="kv35" height="30" transform="rotate(0,653.467,775.395) scale(3.02083,3.04032) translate(-412.897,-489.753)" width="24" x="617.22" xlink:href="#PowerTransformer2:可调不带中性点_0" y="729.79" zvalue="2805"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431660036" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3691">
    <use class="kv10" height="30" transform="rotate(0,653.467,775.395) scale(3.02083,3.04032) translate(-412.897,-489.753)" width="24" x="617.22" xlink:href="#PowerTransformer2:可调不带中性点_1" y="729.79" zvalue="2805"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431725572" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447478276" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447478276"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,653.467,775.395) scale(3.02083,3.04032) translate(-412.897,-489.753)" width="24" x="617.22" y="729.79"/></g>
  <g id="512">
   <g id="5120">
    <use class="kv35" height="30" transform="rotate(0,1307.47,775.395) scale(3.02083,3.04032) translate(-850.401,-489.753)" width="24" x="1271.22" xlink:href="#PowerTransformer2:可调不带中性点_0" y="729.79" zvalue="4129"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5121">
    <use class="kv10" height="30" transform="rotate(0,1307.47,775.395) scale(3.02083,3.04032) translate(-850.401,-489.753)" width="24" x="1271.22" xlink:href="#PowerTransformer2:可调不带中性点_1" y="729.79" zvalue="4129"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1307.47,775.395) scale(3.02083,3.04032) translate(-850.401,-489.753)" width="24" x="1271.22" y="729.79"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="37">
   <use class="kv35" height="18" transform="rotate(0,839.956,563.427) scale(0.999495,1.35382) translate(0.421909,-144.066)" width="10" x="834.9580892255894" xlink:href="#Accessory:熔断器_0" y="551.2424242424241" zvalue="3111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449991933958" ObjectName="35kV1号站用变3731"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,839.956,563.427) scale(0.999495,1.35382) translate(0.421909,-144.066)" width="10" x="834.9580892255894" y="551.2424242424241"/></g>
  <g id="404">
   <use class="kv35" height="42" transform="rotate(0,499.895,592.524) scale(1.42857,-1.42857) translate(-143.54,-998.29)" width="30" x="478.4664391965339" xlink:href="#Accessory:4卷PT带容断器_0" y="562.5238095238095" zvalue="3488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,499.895,592.524) scale(1.42857,-1.42857) translate(-143.54,-998.29)" width="30" x="478.4664391965339" y="562.5238095238095"/></g>
  <g id="436">
   <use class="kv35" height="22" transform="rotate(0,651.558,697.422) scale(1,1) translate(0,0)" width="12" x="645.5583163174716" xlink:href="#Accessory:传输线_0" y="686.4217171717172" zvalue="3518"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,651.558,697.422) scale(1,1) translate(0,0)" width="12" x="645.5583163174716" y="686.4217171717172"/></g>
  <g id="192">
   <use class="kv10" height="26" transform="rotate(270,1103.15,846.136) scale(1,1) translate(2.80158e-12,0)" width="12" x="1097.147000945914" xlink:href="#Accessory:避雷器_0" y="833.1355570361694" zvalue="3859"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1103.15,846.136) scale(1,1) translate(2.80158e-12,0)" width="12" x="1097.147000945914" y="833.1355570361694"/></g>
  <g id="191">
   <use class="kv10" height="26" transform="rotate(90,880.21,851.1) scale(1,1) translate(2.23231e-12,0)" width="12" x="874.2104930094058" xlink:href="#Accessory:避雷器_0" y="838.1004065379066" zvalue="3860"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,880.21,851.1) scale(1,1) translate(2.23231e-12,0)" width="12" x="874.2104930094058" y="838.1004065379066"/></g>
  <g id="302">
   <use class="kv35" height="26" transform="rotate(0,680.357,289.224) scale(1,1) translate(0,0)" width="12" x="674.3571428571428" xlink:href="#Accessory:避雷器_0" y="276.2238095238095" zvalue="4020"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,680.357,289.224) scale(1,1) translate(0,0)" width="12" x="674.3571428571428" y="276.2238095238095"/></g>
  <g id="306">
   <use class="kv35" height="40" transform="rotate(90,616.855,268.857) scale(-0.948029,0.958333) translate(-1268.31,10.8561)" width="30" x="602.6345482474514" xlink:href="#Accessory:2绕组线路PT_0" y="249.6904761904762" zvalue="4040"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,616.855,268.857) scale(-0.948029,0.958333) translate(-1268.31,10.8561)" width="30" x="602.6345482474514" y="249.6904761904762"/></g>
  <g id="307">
   <use class="kv35" height="22" transform="rotate(0,653.415,227.136) scale(1,-1) translate(0,-454.272)" width="12" x="647.4154591746145" xlink:href="#Accessory:传输线_0" y="216.136002886003" zvalue="4042"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际线放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,653.415,227.136) scale(1,-1) translate(0,-454.272)" width="12" x="647.4154591746145" y="216.136002886003"/></g>
  <g id="364">
   <use class="kv35" height="26" transform="rotate(0,1374.36,293.714) scale(1,1) translate(0,0)" width="12" x="1368.357142857143" xlink:href="#Accessory:避雷器_0" y="280.7142857142858" zvalue="4071"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际Ⅱ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1374.36,293.714) scale(1,1) translate(0,0)" width="12" x="1368.357142857143" y="280.7142857142858"/></g>
  <g id="357">
   <use class="kv35" height="40" transform="rotate(90,1310.85,268.857) scale(-0.948029,0.958333) translate(-2694.35,10.8561)" width="30" x="1296.634548247452" xlink:href="#Accessory:2绕组线路PT_0" y="249.6904761904762" zvalue="4080"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际Ⅱ回线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1310.85,268.857) scale(-0.948029,0.958333) translate(-2694.35,10.8561)" width="30" x="1296.634548247452" y="249.6904761904762"/></g>
  <g id="355">
   <use class="kv35" height="22" transform="rotate(0,1347.42,227.136) scale(1,-1) translate(0,-454.272)" width="12" x="1341.415459174615" xlink:href="#Accessory:传输线_0" y="216.136002886003" zvalue="4081"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际Ⅱ回线放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1347.42,227.136) scale(1,-1) translate(0,-454.272)" width="12" x="1341.415459174615" y="216.136002886003"/></g>
  <g id="368">
   <use class="kv35" height="26" transform="rotate(0,962.357,437.714) scale(1,1) translate(0,0)" width="12" x="956.3571428571429" xlink:href="#Accessory:避雷器_0" y="424.7142857142858" zvalue="4087"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV分段避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,962.357,437.714) scale(1,1) translate(0,0)" width="12" x="956.3571428571429" y="424.7142857142858"/></g>
  <g id="389">
   <use class="kv35" height="42" transform="rotate(0,1521.9,592.524) scale(1.42857,-1.42857) translate(-450.14,-998.29)" width="30" x="1500.466439196534" xlink:href="#Accessory:4卷PT带容断器_0" y="562.5238095238095" zvalue="4099"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅱ电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1521.9,592.524) scale(1.42857,-1.42857) translate(-450.14,-998.29)" width="30" x="1500.466439196534" y="562.5238095238095"/></g>
  <g id="391">
   <use class="kv10" height="26" transform="rotate(90,623.753,833.561) scale(-1,1) translate(-1247.51,0)" width="12" x="617.7530615519743" xlink:href="#Accessory:避雷器_0" y="820.560723998224" zvalue="4107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,623.753,833.561) scale(-1,1) translate(-1247.51,0)" width="12" x="617.7530615519743" y="820.560723998224"/></g>
  <g id="423">
   <use class="kv35" height="26" transform="rotate(270,682.462,655.083) scale(1,1) translate(0,0)" width="12" x="676.4624060150375" xlink:href="#Accessory:避雷器_0" y="642.0827067669172" zvalue="4122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,682.462,655.083) scale(1,1) translate(0,0)" width="12" x="676.4624060150375" y="642.0827067669172"/></g>
  <g id="499">
   <use class="kv35" height="22" transform="rotate(0,1305.56,697.422) scale(1,1) translate(0,0)" width="12" x="1299.558316317472" xlink:href="#Accessory:传输线_0" y="686.4217171717172" zvalue="4138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧放电间隙"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1305.56,697.422) scale(1,1) translate(0,0)" width="12" x="1299.558316317472" y="686.4217171717172"/></g>
  <g id="497">
   <use class="kv10" height="26" transform="rotate(90,1277.75,833.561) scale(-1,1) translate(-2555.51,0)" width="12" x="1271.753061551974" xlink:href="#Accessory:避雷器_0" y="820.560723998224" zvalue="4140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1277.75,833.561) scale(-1,1) translate(-2555.51,0)" width="12" x="1271.753061551974" y="820.560723998224"/></g>
  <g id="489">
   <use class="kv35" height="26" transform="rotate(270,1336.46,655.083) scale(1,1) translate(0,0)" width="12" x="1330.462406015038" xlink:href="#Accessory:避雷器_0" y="642.0827067669172" zvalue="4152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1336.46,655.083) scale(1,1) translate(0,0)" width="12" x="1330.462406015038" y="642.0827067669172"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="40">
   <use class="kv35" height="38" transform="rotate(0,848.578,631.274) scale(2.09402,2.09402) translate(-429.117,-309.022)" width="26" x="821.356074682162" xlink:href="#EnergyConsumer:站用变1节点_0" y="591.4881422924902" zvalue="3113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449991868422" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,848.578,631.274) scale(2.09402,2.09402) translate(-429.117,-309.022)" width="26" x="821.356074682162" y="591.4881422924902"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="227">
   <use class="kv10" height="22" transform="rotate(0,653.459,858.462) scale(0.971818,0.971818) translate(18.6397,24.5846)" width="22" x="642.7690970197036" xlink:href="#DollyBreaker:手车_0" y="847.7723873758903" zvalue="3132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449992196102" ObjectName="#1主变10kV侧手车2"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,653.459,858.462) scale(0.971818,0.971818) translate(18.6397,24.5846)" width="22" x="642.7690970197036" y="847.7723873758903"/></g>
  <g id="226">
   <use class="kv10" height="22" transform="rotate(0,653.505,923.541) scale(0.971818,-0.971818) translate(18.641,-1874.17)" width="22" x="642.8150402189933" xlink:href="#DollyBreaker:手车_0" y="912.8514908645495" zvalue="3133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449992130565" ObjectName="#1主变10kV侧手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,653.505,923.541) scale(0.971818,-0.971818) translate(18.641,-1874.17)" width="22" x="642.8150402189933" y="912.8514908645495"/></g>
  <g id="200">
   <use class="kv10" height="22" transform="rotate(0,1063.28,858.462) scale(0.971818,0.971818) translate(30.5243,24.5846)" width="22" x="1052.594849461791" xlink:href="#DollyBreaker:手车_0" y="847.7723742679977" zvalue="3850"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段012手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1063.28,858.462) scale(0.971818,0.971818) translate(30.5243,24.5846)" width="22" x="1052.594849461791" y="847.7723742679977"/></g>
  <g id="199">
   <use class="kv10" height="22" transform="rotate(0,1063.52,923.541) scale(0.971818,-0.971818) translate(30.5311,-1874.17)" width="22" x="1052.829807994874" xlink:href="#DollyBreaker:手车_0" y="912.8514750702994" zvalue="3851"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段012手车2"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1063.52,923.541) scale(0.971818,-0.971818) translate(30.5311,-1874.17)" width="22" x="1052.829807994874" y="912.8514750702994"/></g>
  <g id="511">
   <use class="kv10" height="22" transform="rotate(0,1307.46,858.462) scale(0.971818,0.971818) translate(37.6051,24.5846)" width="22" x="1296.769097019704" xlink:href="#DollyBreaker:手车_0" y="847.7723742675777" zvalue="4131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变10kV侧手车2"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1307.46,858.462) scale(0.971818,0.971818) translate(37.6051,24.5846)" width="22" x="1296.769097019704" y="847.7723742675777"/></g>
  <g id="510">
   <use class="kv10" height="22" transform="rotate(0,1307.51,923.541) scale(0.971818,-0.971818) translate(37.6064,-1874.17)" width="22" x="1296.815040218994" xlink:href="#DollyBreaker:手车_0" y="912.8514758300782" zvalue="4132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变10kV侧手车1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1307.51,923.541) scale(0.971818,-0.971818) translate(37.6064,-1874.17)" width="22" x="1296.815040218994" y="912.8514758300782"/></g>
 </g>
 <g id="BreakerClass">
  <g id="28">
   <use class="kv10" height="20" transform="rotate(0,653.506,890.868) scale(1.828,1.8835) translate(-291.868,-409.048)" width="10" x="644.3660608164208" xlink:href="#Breaker:开关_0" y="872.0329381222914" zvalue="3134"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924523589637" ObjectName="#1主变10kV侧001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,653.506,890.868) scale(1.828,1.8835) translate(-291.868,-409.048)" width="10" x="644.3660608164208" y="872.0329381222914"/></g>
  <g id="198">
   <use class="kv10" height="20" transform="rotate(0,1063.39,890.868) scale(1.828,1.8835) translate(-477.526,-409.048)" width="10" x="1054.248771811684" xlink:href="#Breaker:母联开关_0" y="872.0329423502858" zvalue="3852"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段012"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1063.39,890.868) scale(1.828,1.8835) translate(-477.526,-409.048)" width="10" x="1054.248771811684" y="872.0329423502858"/></g>
  <g id="303">
   <use class="kv35" height="20" transform="rotate(0,653.789,390.712) scale(1.828,1.8835) translate(-291.996,-174.438)" width="10" x="644.6488183473127" xlink:href="#Breaker:开关_0" y="371.8768049619611" zvalue="4018"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际线341"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,653.789,390.712) scale(1.828,1.8835) translate(-291.996,-174.438)" width="10" x="644.6488183473127" y="371.8768049619611"/></g>
  <g id="333">
   <use class="kv35" height="20" transform="rotate(90,1019.17,390.559) scale(-1.828,-1.8835) translate(-1572.57,-589.083)" width="10" x="1010.032619047619" xlink:href="#Breaker:母联开关_0" y="371.7243928491147" zvalue="4048"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV分段312"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1019.17,390.559) scale(-1.828,-1.8835) translate(-1572.57,-589.083)" width="10" x="1010.032619047619" y="371.7243928491147"/></g>
  <g id="365">
   <use class="kv35" height="20" transform="rotate(0,1347.79,390.712) scale(1.828,1.8835) translate(-606.346,-174.438)" width="10" x="1338.648818347313" xlink:href="#Breaker:开关_0" y="371.8768049619611" zvalue="4069"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际Ⅱ回线342"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1347.79,390.712) scale(1.828,1.8835) translate(-606.346,-174.438)" width="10" x="1338.648818347313" y="371.8768049619611"/></g>
  <g id="398">
   <use class="kv35" height="20" transform="rotate(0,651.789,570.712) scale(1.828,1.8835) translate(-291.09,-258.871)" width="10" x="642.6488183473127" xlink:href="#Breaker:开关_0" y="551.8768049619611" zvalue="4112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,651.789,570.712) scale(1.828,1.8835) translate(-291.09,-258.871)" width="10" x="642.6488183473127" y="551.8768049619611"/></g>
  <g id="503">
   <use class="kv10" height="20" transform="rotate(0,1307.51,890.868) scale(1.828,1.8835) translate(-588.1,-409.048)" width="10" x="1298.366060816421" xlink:href="#Breaker:开关_0" y="872.0329418945314" zvalue="4133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变10kV侧002"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1307.51,890.868) scale(1.828,1.8835) translate(-588.1,-409.048)" width="10" x="1298.366060816421" y="872.0329418945314"/></g>
  <g id="494">
   <use class="kv35" height="20" transform="rotate(0,1305.79,570.712) scale(1.828,1.8835) translate(-587.322,-258.871)" width="10" x="1296.648818347313" xlink:href="#Breaker:开关_0" y="551.8768049619611" zvalue="4144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧302"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1305.79,570.712) scale(1.828,1.8835) translate(-587.322,-258.871)" width="10" x="1296.648818347313" y="551.8768049619611"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="27">
   <path class="kv10" d="M 653.56 923.26 L 653.63 908.86" stroke-width="1" zvalue="3136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@1" LinkObjectIDznd="28@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.56 923.26 L 653.63 908.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 653.45 872.85 L 653.51 858.75" stroke-width="1" zvalue="3137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="227@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.45 872.85 L 653.51 858.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 653.52 933.25 L 653.52 955.96" stroke-width="1" zvalue="3143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@0" LinkObjectIDznd="370@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.52 933.25 L 653.52 955.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 1063.57 923.26 L 1063.57 909.51" stroke-width="1" zvalue="3854"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@1" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.57 923.26 L 1063.57 909.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 1063.48 872.38 L 1063.48 858.75" stroke-width="1" zvalue="3855"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="200@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.48 872.38 L 1063.48 858.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 912.54 908.7 L 912.54 955.96" stroke-width="1" zvalue="3856"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@1" LinkObjectIDznd="370@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.54 908.7 L 912.54 955.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1063.53 933.25 L 1063.53 955.96" stroke-width="1" zvalue="3857"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="371@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.53 933.25 L 1063.53 955.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1090.78 846.1 L 1063.3 846.1" stroke-width="1" zvalue="3863"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="514" MaxPinNum="2"/>
   </metadata>
  <path d="M 1090.78 846.1 L 1063.3 846.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 892.58 851.13 L 912.48 851.13" stroke-width="1" zvalue="3864"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="514" MaxPinNum="2"/>
   </metadata>
  <path d="M 892.58 851.13 L 912.48 851.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv35" d="M 839.97 552.71 L 839.97 592.01" stroke-width="1" zvalue="3868"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="40@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.97 552.71 L 839.97 592.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 840.25 503.42 L 840.25 467.96" stroke-width="1" zvalue="4005"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 840.25 503.42 L 840.25 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 840.28 527.25 L 840.28 552.71" stroke-width="1" zvalue="4006"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@1" LinkObjectIDznd="205" MaxPinNum="2"/>
   </metadata>
  <path d="M 840.28 527.25 L 840.28 552.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 486.25 503.42 L 486.25 467.96" stroke-width="1" zvalue="4011"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 486.25 503.42 L 486.25 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 486.28 527.25 L 486.29 563.42" stroke-width="1" zvalue="4012"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@1" LinkObjectIDznd="404@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 486.28 527.25 L 486.29 563.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv35" d="M 653.94 444.82 L 653.94 467.96" stroke-width="1" zvalue="4027"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="305@1" LinkObjectIDznd="97@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.94 444.82 L 653.94 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv35" d="M 653.91 420.99 L 653.91 408.7" stroke-width="1" zvalue="4029"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="305@0" LinkObjectIDznd="303@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.91 420.99 L 653.91 408.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv35" d="M 653.73 372.69 L 653.72 356.82" stroke-width="1" zvalue="4030"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@0" LinkObjectIDznd="298@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.73 372.69 L 653.72 356.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 642.41 315.09 L 653.69 315.09" stroke-width="1" zvalue="4036"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@0" LinkObjectIDznd="309" MaxPinNum="2"/>
   </metadata>
  <path d="M 642.41 315.09 L 653.69 315.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv35" d="M 653.69 332.99 L 653.69 237.14" stroke-width="1" zvalue="4043"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="307@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.69 332.99 L 653.69 237.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv35" d="M 635.62 268.86 L 653.69 268.86" stroke-width="1" zvalue="4045"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="306@0" LinkObjectIDznd="309" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.62 268.86 L 653.69 268.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv35" d="M 926.75 421.49 L 926.75 390.5 L 1000.68 390.47" stroke-width="1" zvalue="4054"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@0" LinkObjectIDznd="333@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 926.75 421.49 L 926.75 390.5 L 1000.68 390.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="323">
   <path class="kv35" d="M 1037.82 390.38 L 1099.25 390.45 L 1099.25 425.49" stroke-width="1" zvalue="4055"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333@0" LinkObjectIDznd="327@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.82 390.38 L 1099.25 390.45 L 1099.25 425.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv35" d="M 926.78 445.32 L 926.78 467.96" stroke-width="1" zvalue="4056"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@1" LinkObjectIDznd="97@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 926.78 445.32 L 926.78 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="320">
   <path class="kv35" d="M 1099.28 449.32 L 1099.28 467.96" stroke-width="1" zvalue="4057"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327@1" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1099.28 449.32 L 1099.28 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="362">
   <path class="kv35" d="M 1347.87 444.82 L 1347.87 467.96" stroke-width="1" zvalue="4073"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="367@1" LinkObjectIDznd="121@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1347.87 444.82 L 1347.87 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="361">
   <path class="kv35" d="M 1347.84 420.99 L 1347.91 408.7" stroke-width="1" zvalue="4075"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="367@0" LinkObjectIDznd="365@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1347.84 420.99 L 1347.91 408.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="360">
   <path class="kv35" d="M 1347.73 372.69 L 1347.72 356.82" stroke-width="1" zvalue="4076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="365@0" LinkObjectIDznd="363@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1347.73 372.69 L 1347.72 356.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="358">
   <path class="kv35" d="M 1336.41 315.09 L 1347.69 315.09" stroke-width="1" zvalue="4079"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="366@0" LinkObjectIDznd="353" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336.41 315.09 L 1347.69 315.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="353">
   <path class="kv35" d="M 1347.69 332.99 L 1347.69 237.14" stroke-width="1" zvalue="4082"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="363@0" LinkObjectIDznd="352" MaxPinNum="2"/>
   </metadata>
  <path d="M 1347.69 332.99 L 1347.69 237.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="352">
   <path class="kv35" d="M 1347.42 237.14 L 1347.42 181.81" stroke-width="1" zvalue="4083"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="355@0" LinkObjectIDznd="359@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1347.42 237.14 L 1347.42 181.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="351">
   <path class="kv35" d="M 1329.62 268.86 L 1347.69 268.86" stroke-width="1" zvalue="4084"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="357@0" LinkObjectIDznd="353" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.62 268.86 L 1347.69 268.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="350">
   <path class="kv35" d="M 1374.39 281.35 L 1374.39 268.86 L 1347.69 268.86" stroke-width="1" zvalue="4085"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="364@0" LinkObjectIDznd="351" MaxPinNum="2"/>
   </metadata>
  <path d="M 1374.39 281.35 L 1374.39 268.86 L 1347.69 268.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv35" d="M 962.39 425.35 L 962.39 390.48" stroke-width="1" zvalue="4088"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@0" LinkObjectIDznd="324" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.39 425.35 L 962.39 390.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="387">
   <path class="kv35" d="M 1508.25 503.42 L 1508.25 467.96" stroke-width="1" zvalue="4103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@0" LinkObjectIDznd="121@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1508.25 503.42 L 1508.25 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv35" d="M 1508.28 527.25 L 1508.29 563.42" stroke-width="1" zvalue="4104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@1" LinkObjectIDznd="389@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1508.28 527.25 L 1508.29 563.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="390">
   <path class="kv10" d="M 653.47 818.1 L 653.47 848.75" stroke-width="1" zvalue="4105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@1" LinkObjectIDznd="227@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.47 818.1 L 653.47 848.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="kv10" d="M 636.12 833.53 L 653.47 833.53" stroke-width="1" zvalue="4108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391@0" LinkObjectIDznd="390" MaxPinNum="2"/>
   </metadata>
  <path d="M 636.12 833.53 L 653.47 833.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="397">
   <path class="kv35" d="M 651.84 608.99 L 651.91 588.7" stroke-width="1" zvalue="4116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="398@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 651.84 608.99 L 651.91 588.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="396">
   <path class="kv35" d="M 651.73 552.69 L 651.73 528.82" stroke-width="1" zvalue="4117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="398@0" LinkObjectIDznd="402@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 651.73 552.69 L 651.73 528.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="427">
   <path class="kv35" d="M 651.7 504.99 L 651.7 467.96" stroke-width="1" zvalue="4123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="402@0" LinkObjectIDznd="97@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 651.7 504.99 L 651.7 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="428">
   <path class="kv35" d="M 651.87 632.82 L 651.87 687.42" stroke-width="1" zvalue="4124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@1" LinkObjectIDznd="436@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 651.87 632.82 L 651.87 687.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="433">
   <path class="kv35" d="M 651.87 687.42 L 651.87 733.06" stroke-width="1" zvalue="4125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="428" LinkObjectIDznd="369@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 651.87 687.42 L 651.87 733.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="434">
   <path class="kv35" d="M 632.41 655.05 L 651.87 655.05" stroke-width="1" zvalue="4126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="422@0" LinkObjectIDznd="428" MaxPinNum="2"/>
   </metadata>
  <path d="M 632.41 655.05 L 651.87 655.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="435">
   <path class="kv35" d="M 670.1 655.05 L 651.87 655.05" stroke-width="1" zvalue="4127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="423@0" LinkObjectIDznd="428" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.1 655.05 L 651.87 655.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="502">
   <path class="kv10" d="M 1307.56 923.26 L 1307.63 908.86" stroke-width="1" zvalue="4135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="510@1" LinkObjectIDznd="503@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.56 923.26 L 1307.63 908.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="501">
   <path class="kv10" d="M 1307.45 872.85 L 1307.51 858.75" stroke-width="1" zvalue="4136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="503@0" LinkObjectIDznd="511@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.45 872.85 L 1307.51 858.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="500">
   <path class="kv10" d="M 1307.52 933.25 L 1307.52 955.96" stroke-width="1" zvalue="4137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="510@0" LinkObjectIDznd="371@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.52 933.25 L 1307.52 955.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="kv10" d="M 1307.47 818.1 L 1307.47 848.75" stroke-width="1" zvalue="4139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@1" LinkObjectIDznd="511@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.47 818.1 L 1307.47 848.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="496">
   <path class="kv10" d="M 1290.12 833.53 L 1307.47 833.53" stroke-width="1" zvalue="4141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="497@0" LinkObjectIDznd="498" MaxPinNum="2"/>
   </metadata>
  <path d="M 1290.12 833.53 L 1307.47 833.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="492">
   <path class="kv35" d="M 1305.84 608.99 L 1305.91 588.7" stroke-width="1" zvalue="4148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@0" LinkObjectIDznd="494@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1305.84 608.99 L 1305.91 588.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="491">
   <path class="kv35" d="M 1305.73 552.69 L 1305.73 528.82" stroke-width="1" zvalue="4149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@0" LinkObjectIDznd="493@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1305.73 552.69 L 1305.73 528.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="488">
   <path class="kv35" d="M 1305.7 504.99 L 1305.7 467.96" stroke-width="1" zvalue="4153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="493@0" LinkObjectIDznd="121@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1305.7 504.99 L 1305.7 467.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="487">
   <path class="kv35" d="M 1305.87 632.82 L 1305.87 687.42" stroke-width="1" zvalue="4154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@1" LinkObjectIDznd="499@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1305.87 632.82 L 1305.87 687.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="486">
   <path class="kv35" d="M 1305.87 687.42 L 1305.87 733.06" stroke-width="1" zvalue="4155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="487" LinkObjectIDznd="512@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1305.87 687.42 L 1305.87 733.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="485">
   <path class="kv35" d="M 1286.41 655.05 L 1305.87 655.05" stroke-width="1" zvalue="4156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="490@0" LinkObjectIDznd="487" MaxPinNum="2"/>
   </metadata>
  <path d="M 1286.41 655.05 L 1305.87 655.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="484">
   <path class="kv35" d="M 1324.1 655.05 L 1305.87 655.05" stroke-width="1" zvalue="4157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@0" LinkObjectIDznd="485" MaxPinNum="2"/>
   </metadata>
  <path d="M 1324.1 655.05 L 1305.87 655.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="514">
   <path class="kv10" d="M 912.48 861.32 L 912.48 836.73 L 1063.3 836.73 L 1063.3 848.75" stroke-width="1" zvalue="4158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="200@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.48 861.32 L 912.48 836.73 L 1063.3 836.73 L 1063.3 848.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 653.69 269 L 680.39 269 L 680.39 276.86" stroke-width="1" zvalue="4169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309" LinkObjectIDznd="302@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.69 269 L 680.39 269 L 680.39 276.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv35" d="M 653.56 181.81 L 653.56 209.47 L 653.69 209.47 L 653.69 237.14" stroke-width="1" zvalue="4225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="309" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.56 181.81 L 653.56 209.47 L 653.69 209.47 L 653.69 237.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="201">
   <use class="kv10" height="36" transform="rotate(0,912.5,885) scale(1.78571,1.38889) translate(-396,-240.8)" width="14" x="900" xlink:href="#Disconnector:联体手车刀闸1_0" y="860" zvalue="3848"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV分段0121"/>
   </metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,912.5,885) scale(1.78571,1.38889) translate(-396,-240.8)" width="14" x="900" y="860"/></g>
  <g id="13">
   <use class="kv35" height="30" transform="rotate(0,840.345,515.239) scale(-1.11133,0.814667) translate(-1595.67,114.435)" width="15" x="832.0096073164968" xlink:href="#Disconnector:刀闸_0" y="503.0192724259745" zvalue="4003"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变3311"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,840.345,515.239) scale(-1.11133,0.814667) translate(-1595.67,114.435)" width="15" x="832.0096073164968" y="503.0192724259745"/></g>
  <g id="24">
   <use class="kv35" height="30" transform="rotate(0,486.345,515.239) scale(-1.11133,0.814667) translate(-923.132,114.435)" width="15" x="478.009607316497" xlink:href="#Disconnector:刀闸_0" y="503.0192724259745" zvalue="4009"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ电压互感器3901"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,486.345,515.239) scale(-1.11133,0.814667) translate(-923.132,114.435)" width="15" x="478.009607316497" y="503.0192724259745"/></g>
  <g id="305">
   <use class="kv35" height="30" transform="rotate(0,654.008,432.808) scale(-1.11133,0.814667) translate(-1241.66,95.6821)" width="15" x="645.6732345765026" xlink:href="#Disconnector:刀闸_0" y="420.5880165903071" zvalue="4014"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际线3411"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,654.008,432.808) scale(-1.11133,0.814667) translate(-1241.66,95.6821)" width="15" x="645.6732345765026" y="420.5880165903071"/></g>
  <g id="298">
   <use class="kv35" height="30" transform="rotate(0,653.789,344.808) scale(-1.11133,0.814667) translate(-1241.25,75.6624)" width="15" x="645.4538183342374" xlink:href="#Disconnector:刀闸_0" y="332.588016590307" zvalue="4026"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际线3416"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,653.789,344.808) scale(-1.11133,0.814667) translate(-1241.25,75.6624)" width="15" x="645.4538183342374" y="332.588016590307"/></g>
  <g id="332">
   <use class="kv35" height="30" transform="rotate(0,926.844,433.308) scale(-1.11133,0.814667) translate(-1760,95.7958)" width="15" x="918.5089331054688" xlink:href="#Disconnector:刀闸_0" y="421.0880139160156" zvalue="4050"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV分段3121"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,926.844,433.308) scale(-1.11133,0.814667) translate(-1760,95.7958)" width="15" x="918.5089331054688" y="421.0880139160156"/></g>
  <g id="327">
   <use class="kv35" height="30" transform="rotate(0,1099.34,437.308) scale(-1.11133,0.814667) translate(-2087.72,96.7058)" width="15" x="1091.008933105469" xlink:href="#Disconnector:刀闸_0" y="425.0880139677026" zvalue="4052"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV分段3122"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1099.34,437.308) scale(-1.11133,0.814667) translate(-2087.72,96.7058)" width="15" x="1091.008933105469" y="425.0880139677026"/></g>
  <g id="367">
   <use class="kv35" height="30" transform="rotate(0,1347.94,432.808) scale(-1.11133,0.814667) translate(-2560.01,95.6821)" width="15" x="1339.605027095793" xlink:href="#Disconnector:刀闸_0" y="420.5880165903071" zvalue="4065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际Ⅱ回线3422"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1347.94,432.808) scale(-1.11133,0.814667) translate(-2560.01,95.6821)" width="15" x="1339.605027095793" y="420.5880165903071"/></g>
  <g id="363">
   <use class="kv35" height="30" transform="rotate(0,1347.79,344.808) scale(-1.11133,0.814667) translate(-2559.72,75.6624)" width="15" x="1339.453818334237" xlink:href="#Disconnector:刀闸_0" y="332.588016590307" zvalue="4072"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际Ⅱ回线3426"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1347.79,344.808) scale(-1.11133,0.814667) translate(-2559.72,75.6624)" width="15" x="1339.453818334237" y="332.588016590307"/></g>
  <g id="388">
   <use class="kv35" height="30" transform="rotate(0,1508.34,515.239) scale(-1.11133,0.814667) translate(-2864.75,114.435)" width="15" x="1500.009607316497" xlink:href="#Disconnector:刀闸_0" y="503.0192724259746" zvalue="4101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅱ电压互感器3902"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1508.34,515.239) scale(-1.11133,0.814667) translate(-2864.75,114.435)" width="15" x="1500.009607316497" y="503.0192724259746"/></g>
  <g id="399">
   <use class="kv35" height="30" transform="rotate(0,651.94,620.808) scale(-1.11133,0.814667) translate(-1237.73,138.451)" width="15" x="643.6050270957933" xlink:href="#Disconnector:刀闸_0" y="608.588016590307" zvalue="4110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧3016"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,651.94,620.808) scale(-1.11133,0.814667) translate(-1237.73,138.451)" width="15" x="643.6050270957933" y="608.588016590307"/></g>
  <g id="402">
   <use class="kv35" height="30" transform="rotate(0,651.801,516.808) scale(-1.11133,0.814667) translate(-1237.47,114.792)" width="15" x="643.4655452322068" xlink:href="#Disconnector:刀闸_0" y="504.588016590307" zvalue="4114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,651.801,516.808) scale(-1.11133,0.814667) translate(-1237.47,114.792)" width="15" x="643.4655452322068" y="504.588016590307"/></g>
  <g id="495">
   <use class="kv35" height="30" transform="rotate(0,1305.94,620.808) scale(-1.11133,0.814667) translate(-2480.22,138.451)" width="15" x="1297.605027095793" xlink:href="#Disconnector:刀闸_0" y="608.588016590307" zvalue="4142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧3026"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1305.94,620.808) scale(-1.11133,0.814667) translate(-2480.22,138.451)" width="15" x="1297.605027095793" y="608.588016590307"/></g>
  <g id="493">
   <use class="kv35" height="30" transform="rotate(0,1305.8,516.808) scale(-1.11133,0.814667) translate(-2479.95,114.792)" width="15" x="1297.465545232207" xlink:href="#Disconnector:刀闸_0" y="504.588016590307" zvalue="4146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧3022"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1305.8,516.808) scale(-1.11133,0.814667) translate(-2479.95,114.792)" width="15" x="1297.465545232207" y="504.588016590307"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="304">
   <use class="kv35" height="30" transform="rotate(90,630.007,315.082) scale(-1.0125,0.866) translate(-1252.16,46.744)" width="12" x="623.9324784889375" xlink:href="#GroundDisconnector:地刀12_0" y="302.0915680676523" zvalue="4016"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际线34167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,630.007,315.082) scale(-1.0125,0.866) translate(-1252.16,46.744)" width="12" x="623.9324784889375" y="302.0915680676523"/></g>
  <g id="366">
   <use class="kv35" height="30" transform="rotate(90,1324.01,315.082) scale(-1.0125,0.866) translate(-2631.59,46.744)" width="12" x="1317.932478488937" xlink:href="#GroundDisconnector:地刀12_0" y="302.0915680676523" zvalue="4067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际Ⅱ回线34267"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1324.01,315.082) scale(-1.0125,0.866) translate(-2631.59,46.744)" width="12" x="1317.932478488937" y="302.0915680676523"/></g>
  <g id="422">
   <use class="kv35" height="30" transform="rotate(90,620.007,655.041) scale(-1.0125,0.866) translate(-1232.29,99.3473)" width="12" x="613.9324784889375" xlink:href="#GroundDisconnector:地刀12_0" y="642.0506172634039" zvalue="4119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧30167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,620.007,655.041) scale(-1.0125,0.866) translate(-1232.29,99.3473)" width="12" x="613.9324784889375" y="642.0506172634039"/></g>
  <g id="490">
   <use class="kv35" height="30" transform="rotate(90,1274.01,655.041) scale(-1.0125,0.866) translate(-2532.21,99.3473)" width="12" x="1267.932478488937" xlink:href="#GroundDisconnector:地刀12_0" y="642.0506172634039" zvalue="4150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变35kV侧30267"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1274.01,655.041) scale(-1.0125,0.866) translate(-2532.21,99.3473)" width="12" x="1267.932478488937" y="642.0506172634039"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="181">
   <use class="kv35" height="30" transform="rotate(0,653.556,173.556) scale(2.06349,0.555556) translate(-333.11,132.178)" width="7" x="646.3333333333335" xlink:href="#ACLineSegment:线路_0" y="165.222222222222" zvalue="4034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,653.556,173.556) scale(2.06349,0.555556) translate(-333.11,132.178)" width="7" x="646.3333333333335" y="165.222222222222"/></g>
  <g id="359">
   <use class="kv35" height="30" transform="rotate(0,1347.56,173.556) scale(2.06349,0.555556) translate(-690.787,132.178)" width="7" x="1340.333333333333" xlink:href="#ACLineSegment:线路_0" y="165.222222222222" zvalue="4077"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV泰业国际Ⅱ回线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1347.56,173.556) scale(2.06349,0.555556) translate(-690.787,132.178)" width="7" x="1340.333333333333" y="165.222222222222"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="5" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,742.717,535.29) scale(1,1) translate(0,0)" writing-mode="lr" x="742.17" xml:space="preserve" y="541.5700000000001" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="6" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1216.72,512.29) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.17" xml:space="preserve" y="518.5700000000001" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="7" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,742.717,564.29) scale(1,1) translate(0,0)" writing-mode="lr" x="742.17" xml:space="preserve" y="570.5700000000001" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="9" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1216.72,549.29) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.17" xml:space="preserve" y="555.5700000000001" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="10" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,747.217,851.5) scale(1,1) translate(0,0)" writing-mode="lr" x="746.67" xml:space="preserve" y="857.78" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="11" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1409.22,852.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1408.67" xml:space="preserve" y="858.78" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="16" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,748.217,878.5) scale(1,1) translate(0,0)" writing-mode="lr" x="747.67" xml:space="preserve" y="884.78" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="17" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1409.22,881.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1408.67" xml:space="preserve" y="887.78" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="18" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,742.717,593.29) scale(1,1) translate(0,0)" writing-mode="lr" x="742.17" xml:space="preserve" y="599.5700000000001" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1216.72,586.29) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.17" xml:space="preserve" y="592.5700000000001" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="22" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,746.717,762.395) scale(1,1) translate(0,0)" writing-mode="lr" x="746.17" xml:space="preserve" y="768.67" zvalue="1">油温:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="23" prefix="油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1220.72,753.395) scale(1,1) translate(0,0)" writing-mode="lr" x="1220.17" xml:space="preserve" y="759.67" zvalue="1">油温:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="31" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,742.217,788.395) scale(1,1) translate(0,0)" writing-mode="lr" x="741.77" xml:space="preserve" y="794.67" zvalue="1">档位:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="32" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212.22,780.395) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.77" xml:space="preserve" y="786.67" zvalue="1">档位:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="33" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,749.217,901.5) scale(1,1) translate(0,0)" writing-mode="lr" x="748.67" xml:space="preserve" y="907.78" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="34" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1409.22,902.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1408.67" xml:space="preserve" y="908.78" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="35" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,732.967,619.29) scale(1,1) translate(0,0)" writing-mode="lr" x="732.5599999999999" xml:space="preserve" y="625.5700000000001" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="36" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1207.97,619.29) scale(1,1) translate(0,0)" writing-mode="lr" x="1207.56" xml:space="preserve" y="625.5700000000001" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="38" prefix="Cos:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,739.967,924.5) scale(1,1) translate(0,0)" writing-mode="lr" x="739.5599999999999" xml:space="preserve" y="930.78" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LCos"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="39" prefix="Cos:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1400.97,926.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1400.56" xml:space="preserve" y="932.78" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LCos"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="41" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1633.89,765.464) scale(1,1) translate(0,0)" writing-mode="lr" x="1633.45" xml:space="preserve" y="770.24" zvalue="1">Ua:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="42" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,375.393,759.464) scale(1,1) translate(0,0)" writing-mode="lr" x="374.95" xml:space="preserve" y="764.24" zvalue="1">Ua:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="43" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1633.89,799.339) scale(1,1) translate(0,0)" writing-mode="lr" x="1633.45" xml:space="preserve" y="804.12" zvalue="1">Ub:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="44" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,375.393,793.339) scale(1,1) translate(0,0)" writing-mode="lr" x="374.95" xml:space="preserve" y="798.12" zvalue="1">Ub:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="45" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1633.89,833.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1633.45" xml:space="preserve" y="837.99" zvalue="1">Uc:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="46" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,375.393,827.214) scale(1,1) translate(0,0)" writing-mode="lr" x="374.95" xml:space="preserve" y="831.99" zvalue="1">Uc:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="47" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1633.89,867.089) scale(1,1) translate(0,0)" writing-mode="lr" x="1633.45" xml:space="preserve" y="871.87" zvalue="1">Uab:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="48" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,375.393,861.089) scale(1,1) translate(0,0)" writing-mode="lr" x="374.95" xml:space="preserve" y="865.87" zvalue="1">Uab:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="49" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1633.89,900.964) scale(1,1) translate(0,0)" writing-mode="lr" x="1633.45" xml:space="preserve" y="905.74" zvalue="1">U0:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="51" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,375.393,894.964) scale(1,1) translate(0,0)" writing-mode="lr" x="374.95" xml:space="preserve" y="899.74" zvalue="1">U0:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1564.79,277.464) scale(1,1) translate(0,0)" writing-mode="lr" x="1564.34" xml:space="preserve" y="282.24" zvalue="1">Ua:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="53" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,462.5,279.464) scale(1,1) translate(0,0)" writing-mode="lr" x="462.06" xml:space="preserve" y="284.24" zvalue="1">Ua:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="54" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1564.79,311.339) scale(1,1) translate(0,0)" writing-mode="lr" x="1564.34" xml:space="preserve" y="316.12" zvalue="1">Ub:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,462.5,313.339) scale(1,1) translate(0,0)" writing-mode="lr" x="462.06" xml:space="preserve" y="318.12" zvalue="1">Ub:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1564.79,345.214) scale(1,1) translate(0,2.54685e-13)" writing-mode="lr" x="1564.34" xml:space="preserve" y="349.99" zvalue="1">Uc:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="57" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,462.5,347.214) scale(1,1) translate(0,2.56239e-13)" writing-mode="lr" x="462.06" xml:space="preserve" y="351.99" zvalue="1">Uc:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="58" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1564.79,379.089) scale(1,1) translate(0,0)" writing-mode="lr" x="1564.34" xml:space="preserve" y="383.87" zvalue="1">Uab:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="59" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,462.5,381.089) scale(1,1) translate(0,0)" writing-mode="lr" x="462.06" xml:space="preserve" y="385.87" zvalue="1">Uab:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="60" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1564.79,412.964) scale(1,1) translate(0,0)" writing-mode="lr" x="1564.34" xml:space="preserve" y="417.74" zvalue="1">U0:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="61" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,462.5,414.964) scale(1,1) translate(0,0)" writing-mode="lr" x="462.06" xml:space="preserve" y="419.74" zvalue="1">U0:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="62" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1019.67,312.919) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.23" xml:space="preserve" y="317.7" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="64" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1019.67,334.919) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.23" xml:space="preserve" y="339.7" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1019.67,355.919) scale(1,1) translate(0,-3.00577e-13)" writing-mode="lr" x="1019.23" xml:space="preserve" y="360.7" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="66" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,661.056,51.7222) scale(1,1) translate(0,0)" writing-mode="lr" x="660.61" xml:space="preserve" y="56.5" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="67" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1348.06,51.7222) scale(1,1) translate(0,0)" writing-mode="lr" x="1347.61" xml:space="preserve" y="56.5" zvalue="1">P:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="68" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,662.056,77.7222) scale(1,1) translate(0,0)" writing-mode="lr" x="661.61" xml:space="preserve" y="82.5" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="69" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1348.06,77.7222) scale(1,1) translate(0,0)" writing-mode="lr" x="1347.61" xml:space="preserve" y="82.5" zvalue="1">Q:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="70" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,663.056,102.722) scale(1,1) translate(0,1.41924e-13)" writing-mode="lr" x="662.61" xml:space="preserve" y="107.5" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="71" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1352.06,102.722) scale(1,1) translate(0,-1.32462e-13)" writing-mode="lr" x="1351.61" xml:space="preserve" y="107.5" zvalue="1">Ia:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,664.278,128.556) scale(1,1) translate(0,0)" writing-mode="lr" x="663.84" xml:space="preserve" y="133.33" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1354.28,131.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.84" xml:space="preserve" y="136.33" zvalue="1">Cos:dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
 </g>
</svg>