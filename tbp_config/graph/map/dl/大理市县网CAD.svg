<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1080" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1080" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
 </defs>
 <g id="HeadClass">
  <rect FacName="" InitShowingPlane="" fill="rgb(0,0,0)" height="1080" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <rect fill="rgb(37,77,162)" fill-opacity="0.72" height="163" id="660" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,960,-591.5) scale(1,1) translate(0,-8.21898e-13)" width="3846.67" x="-963.33" y="-673" zvalue="1559"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="27" id="1" stroke="rgb(85,255,255)" text-anchor="start" transform="rotate(0,2648.06,-552.924) scale(1,1) translate(0,0)" writing-mode="lr" x="2584.56" xml:space="preserve" y="-543.42" zvalue="1661">厂站图索引</text>
  
  <path d="M -497.329 -1380.01 L -497.329 -1458.62 L 1871.79 -1458.62 L 1871.79 -1376.68 z" fill="rgb(6,132,249)" fill-opacity="0.4" id="656" stroke="rgb(42,109,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1556"/>
  <path d="M 2504.11 -570.296 L 2504.11 -570.296" fill="none" id="652" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" zvalue="1560"/>
  <path d="M 2504.11 -570.296 L 2504.11 -570.296" fill="none" id="653" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" zvalue="1562"/>
  
  <rect fill="none" fill-opacity="0" height="42.47" id="650" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,2504.11,-546.666) scale(1,1) translate(1.6495e-12,2.52199e-13)" width="55.74" x="2476.24" y="-567.9" zvalue="1564"/>
  
  <rect fill="none" fill-opacity="0" height="42.47" id="651" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,2504.11,-546.666) scale(1,1) translate(1.6495e-12,2.52199e-13)" width="55.74" x="2476.24" y="-567.9" zvalue="1566"/>
  
  <rect fill="none" fill-opacity="0" height="52.83" id="648" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,2504.11,-616.698) scale(1,1) translate(6.30198e-12,-2.5704e-12)" width="72.29000000000001" x="2467.96" y="-643.12" zvalue="1568"/>
  
  <rect fill="none" fill-opacity="0" height="52.83" id="649" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,2504.11,-616.698) scale(1,1) translate(6.30198e-12,-2.5704e-12)" width="72.29000000000001" x="2467.96" y="-643.12" zvalue="1570"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="254.9972021522843" x2="631.5052021522843" y1="-665.961509975429" y2="-665.961509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="254.9972021522843" x2="631.5052021522843" y1="-589.8557099754289" y2="-589.8557099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="254.9972021522843" x2="254.9972021522843" y1="-665.961509975429" y2="-589.8557099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="631.5052021522843" x2="631.5052021522843" y1="-665.961509975429" y2="-589.8557099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="631.5092021522843" x2="978.6072021522843" y1="-665.961509975429" y2="-665.961509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="631.5092021522843" x2="978.6072021522843" y1="-589.8557099754289" y2="-589.8557099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="631.5092021522843" x2="631.5092021522843" y1="-665.961509975429" y2="-589.8557099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="978.6072021522843" y1="-665.961509975429" y2="-589.8557099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="1162.777202152284" y1="-665.961509975429" y2="-665.961509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="1162.777202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="978.6072021522843" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.777202152284" x2="1162.777202152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.776202152284" x2="1346.946202152284" y1="-665.961509975429" y2="-665.961509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.776202152284" x2="1346.946202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.776202152284" x2="1162.776202152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946202152284" x2="1346.946202152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946102152284" x2="1531.116102152284" y1="-665.961509975429" y2="-665.961509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946102152284" x2="1531.116102152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946102152284" x2="1346.946102152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.116102152284" x2="1531.116102152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.117202152284" x2="1715.287202152284" y1="-665.961509975429" y2="-665.961509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.117202152284" x2="1715.287202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.117202152284" x2="1531.117202152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.287202152284" x2="1715.287202152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.285202152284" x2="1899.455202152284" y1="-665.961509975429" y2="-665.961509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.285202152284" x2="1899.455202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.285202152284" x2="1715.285202152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.455202152284" x2="1899.455202152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.456202152284" x2="2083.626202152284" y1="-665.961509975429" y2="-665.961509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.456202152284" x2="2083.626202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.456202152284" x2="1899.456202152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2083.626202152284" x2="2083.626202152284" y1="-665.961509975429" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2083.624202152284" x2="2267.794202152284" y1="-665.961509975429" y2="-665.961509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2083.624202152284" x2="2267.794202152284" y1="-564.487509975429" y2="-564.487509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2083.624202152284" x2="2083.624202152284" y1="-665.961509975429" y2="-564.487509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2267.794202152284" x2="2267.794202152284" y1="-665.961509975429" y2="-564.487509975429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="1162.777202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="1162.777202152284" y1="-564.4873099754288" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="978.6072021522843" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.777202152284" x2="1162.777202152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.776202152284" x2="1346.946202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.776202152284" x2="1346.946202152284" y1="-564.4873099754288" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.776202152284" x2="1162.776202152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946202152284" x2="1346.946202152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946102152284" x2="1531.116102152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946102152284" x2="1531.116102152284" y1="-564.4873099754288" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946102152284" x2="1346.946102152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.116102152284" x2="1531.116102152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.117202152284" x2="1715.287202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.117202152284" x2="1715.287202152284" y1="-564.4873099754288" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.117202152284" x2="1531.117202152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.287202152284" x2="1715.287202152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.285202152284" x2="1899.455202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.285202152284" x2="1899.455202152284" y1="-564.4873099754288" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.285202152284" x2="1715.285202152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.455202152284" x2="1899.455202152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.456202152284" x2="2083.626202152284" y1="-615.2244099754289" y2="-615.2244099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.456202152284" x2="2083.626202152284" y1="-564.4873099754288" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.456202152284" x2="1899.456202152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2083.626202152284" x2="2083.626202152284" y1="-615.2244099754289" y2="-564.4873099754288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="254.9972021522843" x2="631.5052021522843" y1="-589.8557099754289" y2="-589.8557099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="254.9972021522843" x2="631.5052021522843" y1="-513.7499099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="254.9972021522843" x2="254.9972021522843" y1="-589.8557099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="631.5052021522843" x2="631.5052021522843" y1="-589.8557099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="631.5092021522843" x2="978.6072021522843" y1="-589.8557099754289" y2="-589.8557099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="631.5092021522843" x2="978.6072021522843" y1="-513.7499099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="631.5092021522843" x2="631.5092021522843" y1="-589.8557099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="978.6072021522843" y1="-589.8557099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="1162.777202152284" y1="-564.4870099754289" y2="-564.4870099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="1162.777202152284" y1="-513.7499099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="978.6072021522843" x2="978.6072021522843" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.777202152284" x2="1162.777202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.776202152284" x2="1346.946202152284" y1="-564.4870099754289" y2="-564.4870099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.776202152284" x2="1346.946202152284" y1="-513.7499099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1162.776202152284" x2="1162.776202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946202152284" x2="1346.946202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946102152284" x2="1531.116102152284" y1="-564.4870099754289" y2="-564.4870099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946102152284" x2="1531.116102152284" y1="-513.7499099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1346.946102152284" x2="1346.946102152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.116102152284" x2="1531.116102152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.117202152284" x2="1715.287202152284" y1="-564.4870099754289" y2="-564.4870099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.117202152284" x2="1715.287202152284" y1="-513.7499099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1531.117202152284" x2="1531.117202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.287202152284" x2="1715.287202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.285202152284" x2="1899.455202152284" y1="-564.4870099754289" y2="-564.4870099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.285202152284" x2="1899.455202152284" y1="-513.7499099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1715.285202152284" x2="1715.285202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.455202152284" x2="1899.455202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.456202152284" x2="2083.626202152284" y1="-564.4870099754289" y2="-564.4870099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.456202152284" x2="2083.626202152284" y1="-513.7499099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1899.456202152284" x2="1899.456202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2083.626202152284" x2="2083.626202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2083.624202152284" x2="2267.794202152284" y1="-564.4870099754289" y2="-564.4870099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2083.624202152284" x2="2267.794202152284" y1="-513.7499099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2083.624202152284" x2="2083.624202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2267.794202152284" x2="2267.794202152284" y1="-564.4870099754289" y2="-513.7499099754289"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" id="644" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1073.53,-639.812) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.53" xml:space="preserve" y="-632.3099999999999" zvalue="1578">电网数据</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" id="643" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1071.31,-590.456) scale(1,1) translate(0,0)" writing-mode="lr" x="1071.31" xml:space="preserve" y="-582.96" zvalue="1580">实时数据</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" id="642" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1073.53,-541.1) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.53" xml:space="preserve" y="-533.6" zvalue="1582">日最大数据</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" id="641" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1255.11,-639.812) scale(1,1) translate(0,0)" writing-mode="lr" x="1255.11" xml:space="preserve" y="-632.3099999999999" zvalue="1584">大电外送</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="16" id="640" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,350.956,-629.083) scale(1,1) translate(0,0)" writing-mode="lr" x="350.96" xml:space="preserve" y="-623.08" zvalue="1586">卫星钟日期：</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" id="638" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1440.02,-639.812) scale(1,1) translate(0,0)" writing-mode="lr" x="1440.02" xml:space="preserve" y="-632.3099999999999" zvalue="1589">全社会负荷</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" id="637" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1623.82,-639.812) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.82" xml:space="preserve" y="-632.3099999999999" zvalue="1591">新能源出力</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" id="636" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1808.72,-639.812) scale(1,1) translate(0,0)" writing-mode="lr" x="1808.72" xml:space="preserve" y="-632.3099999999999" zvalue="1593">大用户负荷</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" id="635" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1991.42,-639.812) scale(1,1) translate(0,0)" writing-mode="lr" x="1991.42" xml:space="preserve" y="-632.3099999999999" zvalue="1595">小水电出力</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="16" id="634" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,2174.11,-614.061) scale(1,1) translate(0,0)" writing-mode="lr" x="2174.108378185153" xml:space="preserve" y="-608.0611762643989" zvalue="1597">大理全网        发电出力</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="48" id="658" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,-654.028,-583.333) scale(1,1) translate(0,0)" writing-mode="lr" x="-654.03" xml:space="preserve" y="-566.33" zvalue="1621">大 理 电 网 潮 流 图</text>
  <rect fill="none" height="150.21" id="661" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,-317,-589.773) scale(1,1) translate(1.80478e-12,8.85798e-13)" width="43.33" x="-338.67" y="-664.88" zvalue="1623"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="661" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,-317,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="-317" xml:space="preserve" y="-589.77" zvalue="1623">大理潮流</text>
  <rect fill="none" height="150.21" id="662" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,-266.285,-589.773) scale(1,1) translate(-8.63163e-13,5.90532e-13)" width="43.33" x="-287.95" y="-664.88" zvalue="1625"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="662" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,-266.285,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="-266.28" xml:space="preserve" y="-589.77" zvalue="1625">祥云潮流</text>
  <rect fill="none" height="150.21" id="664" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,-164.854,-589.773) scale(1,1) translate(-5.59113e-13,5.90532e-13)" width="43.33" x="-186.52" y="-664.88" zvalue="1629"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="664" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,-164.854,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="-164.85" xml:space="preserve" y="-589.77" zvalue="1629">鹤庆潮流</text>
  <rect fill="none" height="150.21" id="665" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,-114.138,-589.773) scale(1,1) translate(-4.07089e-13,5.90532e-13)" width="43.33" x="-135.8" y="-664.88" zvalue="1631"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="665" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,-114.138,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="-114.14" xml:space="preserve" y="-589.77" zvalue="1631">宾川潮流</text>
  <rect fill="none" height="150.21" id="666" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,-63.4226,-589.773) scale(1,1) translate(-2.83404e-14,5.90532e-13)" width="43.33" x="-85.09" y="-664.88" zvalue="1633"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="666" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,-63.4226,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="-63.42" xml:space="preserve" y="-589.77" zvalue="1633">南涧潮流</text>
  <rect fill="none" height="150.21" id="668" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,-215.569,-589.773) scale(1,1) translate(-7.11138e-13,5.90532e-13)" width="43.33" x="-237.24" y="-664.88" zvalue="1637"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="668" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,-215.569,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="-215.57" xml:space="preserve" y="-589.77" zvalue="1637">巍山潮流</text>
  <rect fill="none" height="150.21" id="671" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,-15.4615,-589.773) scale(1,1) translate(8.24412e-14,5.90532e-13)" width="43.33" x="-37.13" y="-664.88" zvalue="1641"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="671" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,-15.4615,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="-15.46" xml:space="preserve" y="-589.77" zvalue="1641">永平潮流</text>
  <rect fill="none" height="150.21" id="672" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,34.5385,-589.773) scale(1,1) translate(-2.85811e-14,5.90532e-13)" width="43.33" x="12.87" y="-664.88" zvalue="1643"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="672" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,34.5385,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="34.54" xml:space="preserve" y="-589.77" zvalue="1643">漾濞潮流</text>
  <rect fill="none" height="150.21" id="673" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.8462,-589.773) scale(1,1) translate(-1.44728e-13,5.90532e-13)" width="43.33" x="65.18000000000001" y="-664.88" zvalue="1645"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="673" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,86.8462,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="86.84999999999999" xml:space="preserve" y="-589.77" zvalue="1645">洱源潮流</text>
  <rect fill="none" height="150.21" id="674" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,132.231,-589.773) scale(1,1) translate(-2.45502e-13,5.90532e-13)" width="43.33" x="110.56" y="-664.88" zvalue="1647"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="674" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,132.231,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="132.23" xml:space="preserve" y="-589.77" zvalue="1647">剑川潮流</text>
  <rect fill="none" height="150.21" id="675" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,179.923,-589.773) scale(1,1) translate(-3.514e-13,5.90532e-13)" width="43.33" x="158.26" y="-664.88" zvalue="1649"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="675" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,179.923,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="179.92" xml:space="preserve" y="-589.77" zvalue="1649">云龙潮流</text>
  <rect fill="none" height="150.21" id="676" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,229.154,-589.773) scale(1,1) translate(-4.60714e-13,5.90532e-13)" width="43.33" x="207.49" y="-664.88" zvalue="1651"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="676" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,229.154,-589.773) scale(1,1) translate(0,0)" writing-mode="tb" x="229.15" xml:space="preserve" y="-589.77" zvalue="1651">弥渡潮流</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="16" id="677" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,352.282,-554.354) scale(1,1) translate(0,0)" writing-mode="lr" x="352.28" xml:space="preserve" y="-548.35" zvalue="1653">卫星钟时间：</text>
  <rect fill="none" height="150.21" id="678" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,2298.67,-589.893) scale(1,1) translate(-5.05596e-12,5.90639e-13)" width="43.33" x="2277" y="-665" zvalue="1655"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="678" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,2298.67,-589.893) scale(1,1) translate(0,0)" writing-mode="tb" x="2298.67" xml:space="preserve" y="-589.89" zvalue="1655">发电负荷</text>
  <rect fill="none" height="150.21" id="679" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,2350.67,-590.893) scale(1,1) translate(-5.17142e-12,5.91527e-13)" width="43.33" x="2329" y="-666" zvalue="1657"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="679" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,2350.67,-590.893) scale(1,1) translate(0,0)" writing-mode="tb" x="2350.67" xml:space="preserve" y="-590.89" zvalue="1657">潮流断面</text>
  <rect fill="none" height="150.21" id="680" stroke="rgb(85,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,2403.67,-591.893) scale(1,1) translate(-5.2891e-12,5.92415e-13)" width="43.33" x="2382" y="-667" zvalue="1659"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="21" glyph-orientation-vertical="0" id="680" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,2403.67,-591.893) scale(1,1) translate(0,0)" writing-mode="tb" x="2403.67" xml:space="preserve" y="-591.89" zvalue="1659">临时断面</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="27" id="3" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,2607.88,-611.432) scale(1,1) translate(0,0)" writing-mode="lr" x="2607.88" xml:space="preserve" y="-601.9299999999999" zvalue="1662">首页</text>
 </g>
 <g id="ButtonClass">
  <g href="大理地调二级索引.svg"><rect fill-opacity="0" height="34" width="127" x="2584.56" y="-569.92" zvalue="1661"/></g>
 </g>
 <g id="ClockClass">
  
  
 </g>
</svg>